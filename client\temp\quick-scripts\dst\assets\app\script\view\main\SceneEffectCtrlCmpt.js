
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SceneEffectCtrlCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd71521kL05JUYliTsjUhsw+', 'SceneEffectCtrlCmpt');
// app/script/view/main/SceneEffectCtrlCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var SceneEffectCtrlHelper_1 = require("../../common/helper/SceneEffectCtrlHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 场景特效控制
 */
var SceneEffectCtrlCmpt = /** @class */ (function (_super) {
    __extends(SceneEffectCtrlCmpt, _super);
    function SceneEffectCtrlCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.psList = [];
        _this.running = true;
        return _this;
    }
    SceneEffectCtrlCmpt.prototype.onLoad = function () {
        var _this = this;
        this.running = SceneEffectCtrlHelper_1.sceneEffectCtrlHelper.running;
        this.psList = [];
        this.node.children.forEach(function (m) {
            var ps = m.getComponent(cc.ParticleSystem);
            if (ps) {
                _this.psList.push(ps);
                if (_this.running) {
                    ps.resetSystem();
                }
                else {
                    ps.stopSystem();
                }
            }
        });
    };
    SceneEffectCtrlCmpt.prototype.update = function (dt) {
        if (SceneEffectCtrlHelper_1.sceneEffectCtrlHelper.running === this.running) {
            return;
        }
        this.running = SceneEffectCtrlHelper_1.sceneEffectCtrlHelper.running;
        if (this.running) {
            this.psList.forEach(function (m) {
                m.resetSystem();
                m.node.opacity = 0;
                cc.tween(m.node).delay(2.5).to(0.5, { opacity: 255 }).start();
            });
        }
        else {
            this.psList.forEach(function (m) { return m.stopSystem(); });
        }
    };
    SceneEffectCtrlCmpt = __decorate([
        ccclass
    ], SceneEffectCtrlCmpt);
    return SceneEffectCtrlCmpt;
}(cc.Component));
exports.default = SceneEffectCtrlCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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