
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PawnDrillInfoObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b3986xIwSNNB7qJRNHebkOO', 'PawnDrillInfoObj');
// app/script/model/main/PawnDrillInfoObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 士兵招募信息
var PawnDrillInfoObj = /** @class */ (function () {
    function PawnDrillInfoObj() {
        this.type = 'drill';
        this.uid = '';
        this.index = 0;
        this.buid = ''; //建筑uid
        this.auid = ''; //军队uid
        this.id = 0; //士兵id
        this.lv = 0; //招募等级
        this.needTime = 0; //需要时间
        this.surplusTime = 0; //剩余时间
        this.getTime = 0; //获取时间
        this.json = null;
    }
    PawnDrillInfoObj.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.index = data.index;
        this.buid = data.buid;
        this.auid = data.auid;
        this.id = data.id;
        this.lv = data.lv;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        this.json = assetsMgr.getJsonData('pawnBase', this.id);
        return this;
    };
    // 获取实际的剩余时间
    PawnDrillInfoObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    PawnDrillInfoObj.prototype.isCanCancel = function () {
        return this.surplusTime === 0;
    };
    return PawnDrillInfoObj;
}());
exports.default = PawnDrillInfoObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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