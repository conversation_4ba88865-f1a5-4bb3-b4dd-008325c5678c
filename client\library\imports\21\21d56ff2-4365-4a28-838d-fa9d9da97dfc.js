"use strict";
cc._RF.push(module, '21d56/yQ2VKKION+p2dqX38', 'SubscriptionDescPnlCtrl');
// app/script/view/common/SubscriptionDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var ccclass = cc._decorator.ccclass;
var SubscriptionDescPnlCtrl = /** @class */ (function (_super) {
    __extends(SubscriptionDescPnlCtrl, _super);
    function SubscriptionDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/bg/content/title/title_l
        _this.infoNode_ = null; // path://root/bg/content/info_n
        _this.totalDescNode_ = null; // path://root/bg/content/total_desc_n
        _this.buttonsNode_ = null; // path://root/bg/content/buttons_n
        _this.subscriptionButtonNode_ = null; // path://root/bg/content/buttons_n/subscription_button_n
        //@end
        _this.user = null;
        _this.restoreSubscriptions = [];
        _this.cardType = Enums_1.MonthlyCardType.SALE;
        return _this;
    }
    SubscriptionDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SubscriptionDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var list;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.user = this.getModel('user');
                        return [4 /*yield*/, PayHelper_1.payHelper.checkPayInit()];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, PayHelper_1.payHelper.getSubscriptions()];
                    case 2:
                        list = _a.sent();
                        this.restoreSubscriptions = list.slice();
                        return [2 /*return*/];
                }
            });
        });
    };
    SubscriptionDescPnlCtrl.prototype.onEnter = function (type) {
        this.cardType = type;
        var index = Constant_1.MONTH_CARD.findIndex(function (m) { return m.TYPE === type; });
        this.titleLbl_.setLocaleKey('ui.monthly_card_' + (index + 1));
        var isFirsPay = !this.user.getRechargeCountRecord()[type];
        var firstPayLbl = this.infoNode_.Child('1/content/layout/val');
        firstPayLbl.active = isFirsPay;
        isFirsPay && firstPayLbl.setLocaleKey('ui.bracket', assetsMgr.lang('ui.first_pay_double'));
        var data = Constant_1.MONTH_CARD[index];
        this.infoNode_.Child('1/content/count', cc.Label).string = isFirsPay ? 'x' + data.FIRST * 2 : 'x' + data.FIRST;
        this.infoNode_.Child('1/tip_desc').setLocaleKey('ui.subscription_imm_tip', isFirsPay ? data.FIRST * 4 : data.FIRST * 3);
        this.infoNode_.Child('2/count', cc.Label).string = 'x' + data.DAY;
        this.infoNode_.Child('3/count', cc.Label).string = 'x' + data.EXTRA;
        this.infoNode_.Child('2/line').active = this.infoNode_.Child('3').active = type === Enums_1.MonthlyCardType.SUPER;
        var params = type === Enums_1.MonthlyCardType.SALE ? [data.DURATION, data.DAY * data.DURATION] : [data.DURATION, data.DAY * data.DURATION, data.EXTRA * data.DURATION];
        this.totalDescNode_.setLocaleKey('ui.subscription_total_value_' + (index + 1), params);
        var monthCard = Constant_1.MONTH_CARD.find(function (m) { return m.TYPE === type; });
        var has = this.restoreSubscriptions.some(function (m) { return monthCard.RESTORES.includes(m.productId); });
        this.buttonsNode_.Swih(has ? 'restore_buy_be' : 'subscription_button_n')[0];
        if (!has) {
            var node1 = this.subscriptionButtonNode_.Child('subscription_1_be/root');
            var node7 = this.subscriptionButtonNode_.Child('subscription_7_be/root');
            var node30 = this.subscriptionButtonNode_.Child('subscription_30_be/root');
            var key = '0';
            var it1 = node1.Swih(key)[0], it7 = node7.Swih(key)[0], it30 = node30.Swih(key)[0];
            it1.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'month', false);
            it7.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'month', true);
            it30.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'quarter', true);
        }
    };
    SubscriptionDescPnlCtrl.prototype.onRemove = function () {
    };
    SubscriptionDescPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_1_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription1 = function (event, data) {
        var _this = this;
        var id = Constant_1.MONTH_CARD.find(function (m) { return m.TYPE === _this.cardType; }).RECHARGES[0];
        id && this.buyProduct(id);
    };
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_7_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription7 = function (event, data) {
        this.buySubScription(this.cardType, 'month');
    };
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_30_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription30 = function (event, data) {
        this.buySubScription(this.cardType, 'quarter');
    };
    // path://root/bg/content/desc/desc1_be
    SubscriptionDescPnlCtrl.prototype.onClickDesc1 = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getPrivacyPolicyUrl());
    };
    // path://root/bg/content/desc/desc2_be
    SubscriptionDescPnlCtrl.prototype.onClickDesc2 = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getUserAgreementUrl());
    };
    // path://root/bg/content/buttons_n/restore_buy_be
    SubscriptionDescPnlCtrl.prototype.onClickRestoreBuy = function (event, data) {
        this.restore();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SubscriptionDescPnlCtrl.prototype.buyProduct = function (productId) {
        var _this = this;
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        PayHelper_1.payHelper.buyProduct(productId).then(function (ok) {
            logger.print('6.buyProduct end. suc=' + ok + ', isValid=' + _this.isValid);
            if (_this.isValid && ok) {
                _this.hide();
            }
        });
    };
    SubscriptionDescPnlCtrl.prototype.buySubScription = function (cardType, type) {
        var _this = this;
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        PayHelper_1.payHelper.buySubscription(cardType, type).then(function (ok) {
            if (_this.isValid && ok) {
                _this.hide();
            }
        });
    };
    // 恢复购买
    SubscriptionDescPnlCtrl.prototype.restore = function () {
        return __awaiter(this, void 0, void 0, function () {
            var order, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.restoreSubscriptions.length === 0) {
                            return [2 /*return*/, this.onEnter(this.cardType)];
                        }
                        order = this.restoreSubscriptions[0];
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifySubOrder', {
                                productId: order.productId,
                                orderId: order.orderId,
                                cpOrderId: order.cpOrderId,
                                token: order.token,
                                platform: order.platform,
                                price: order.price,
                                purchaseTime: order.purchaseTime,
                                currencyType: order.currencyType,
                                payAmount: order.payAmount,
                            }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!!this.isValid) return [3 /*break*/, 2];
                        return [2 /*return*/];
                    case 2:
                        if (!(err === ECode_1.ecode.SUBSCRIPTION_TIMEOUT || err === ECode_1.ecode.ORDER_VERIFY_API_ERROR)) return [3 /*break*/, 4];
                        this.restoreSubscriptions.shift();
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token, type: 'subs' })];
                    case 3:
                        _b.sent();
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        if (this.restoreSubscriptions.length > 0) {
                            this.restore();
                        }
                        else {
                            PayHelper_1.payHelper.cleanRestoredSubs();
                            ViewHelper_1.viewHelper.showAlert(err);
                            this.onEnter(this.cardType);
                        }
                        return [2 /*return*/];
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.restore_buy_sub_succeed');
                            this.hide();
                        }
                        _b.label = 5;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    SubscriptionDescPnlCtrl = __decorate([
        ccclass
    ], SubscriptionDescPnlCtrl);
    return SubscriptionDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SubscriptionDescPnlCtrl;

cc._RF.pop();