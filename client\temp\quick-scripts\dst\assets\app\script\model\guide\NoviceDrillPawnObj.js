
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceDrillPawnObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1e9cnDbj5JSpBhXECFazNB', 'NoviceDrillPawnObj');
// app/script/model/guide/NoviceDrillPawnObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个训练
var NoviceDrillPawnObj = /** @class */ (function () {
    function NoviceDrillPawnObj() {
        this.uid = '';
        this.index = 0; //区域位置
        this.bUid = ''; //建筑uid
        this.aUid = ''; //军队uid
        this.id = 0; //士兵id
        this.lv = 0; //训练等级
        this.startTime = 0; //开始时间
        this.needTime = 0; //需要时间
    }
    NoviceDrillPawnObj.prototype.init = function (index, buid, auid, id, lv, time) {
        this.uid = ut.UID();
        this.index = index;
        this.bUid = buid;
        this.aUid = auid;
        this.id = id;
        this.lv = lv;
        this.startTime = 0;
        this.needTime = time;
        return this;
    };
    NoviceDrillPawnObj.prototype.fromDB = function (data) {
        this.uid = data.uid;
        this.index = data.index;
        this.bUid = data.bUid;
        this.aUid = data.aUid;
        this.id = data.id;
        this.lv = data.lv;
        this.startTime = data.startTime;
        this.needTime = data.needTime;
        return this;
    };
    NoviceDrillPawnObj.prototype.strip = function () {
        return {
            uid: this.uid,
            index: this.index,
            buid: this.bUid,
            auid: this.aUid,
            id: this.id,
            lv: this.lv,
            needTime: this.needTime,
            surplusTime: Math.max(this.needTime - (Date.now() - this.startTime), 0),
        };
    };
    return NoviceDrillPawnObj;
}());
exports.default = NoviceDrillPawnObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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