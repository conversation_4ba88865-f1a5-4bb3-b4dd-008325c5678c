
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/help/NoticeGuidePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5689d8+InJPVqSMQXLQKLvU', 'NoticeGuidePnlCtrl');
// app/script/view/help/NoticeGuidePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var MultiFrame_1 = require("../../../core/component/MultiFrame");
var ccclass = cc._decorator.ccclass;
var NoticeGuidePnlCtrl = /** @class */ (function (_super) {
    __extends(NoticeGuidePnlCtrl, _super);
    function NoticeGuidePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        //@end
        _this.defaultPaddingBottom = 0;
        return _this;
    }
    NoticeGuidePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoticeGuidePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.defaultPaddingBottom = this.contentNode_.Child('item/box', cc.Layout).paddingBottom;
                return [2 /*return*/];
            });
        });
    };
    NoticeGuidePnlCtrl.prototype.onEnter = function (data) {
        this.initUI(data);
    };
    NoticeGuidePnlCtrl.prototype.onRemove = function () {
    };
    NoticeGuidePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    NoticeGuidePnlCtrl.prototype.initUI = function (params) {
        for (var i = 0; i < params.length; i++) {
            var itemData = params[i];
            var item = this.contentNode_.children[i];
            if (!item) {
                item = cc.instantiate(this.contentNode_.children[0]);
                item.parent = this.contentNode_;
                item.position = cc.v3(0, 0, 0);
            }
            item.Child('tips').setLocaleKey(itemData.tips);
            var descLbl = item.Child('box/desc', cc.Label);
            if (descLbl.node.active = !!itemData.desc) {
                descLbl.node.setLocaleKey(itemData.desc);
            }
            descLbl._forceUpdateRenderData();
            if (descLbl.node.height > descLbl.lineHeight * 2) {
                descLbl.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
            }
            else {
                descLbl.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
            }
            var changePaddingBottom = 0;
            var icon1 = item.Child('box/icon/icon1');
            var iconName1 = icon1.Child('name');
            icon1.Component(cc.Sprite).spriteFrame = icon1.Component(MultiFrame_1.default).getFrame(itemData.icon1);
            if (iconName1.active = !!itemData.iconName1) {
                iconName1.setLocaleKey(itemData.iconName1);
                iconName1.y = -(icon1.height / 2 + iconName1.height);
                changePaddingBottom = iconName1.height * 1.5;
            }
            var icon2 = item.Child('box/icon/icon2');
            var iconName2 = icon2.Child('name');
            icon2.Component(cc.Sprite).spriteFrame = icon2.Component(MultiFrame_1.default).getFrame(itemData.icon2);
            if (iconName2.active = !!itemData.iconName2) {
                iconName2.setLocaleKey(itemData.iconName2);
                iconName2.y = -(icon2.height / 2 + iconName2.height);
                changePaddingBottom = iconName2.height * 1.5;
            }
            var icon = item.Child('box/icon');
            icon.height = Math.max(icon1.height, icon2.height);
            item.Child('box', cc.Layout).paddingBottom = this.defaultPaddingBottom + changePaddingBottom;
        }
    };
    NoticeGuidePnlCtrl = __decorate([
        ccclass
    ], NoticeGuidePnlCtrl);
    return NoticeGuidePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoticeGuidePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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