
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/PopupPnlHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9ae26E/FglGbYnalCK1dhRg', 'PopupPnlHelper');
// app/script/common/helper/PopupPnlHelper.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.popupPnlHelper = void 0;
var ViewHelper_1 = require("./ViewHelper");
/**
 * 主动弹出UI
 */
var PopupPnlHelper = /** @class */ (function () {
    function PopupPnlHelper() {
        this.popups = [];
    }
    // 添加到队列
    PopupPnlHelper.prototype.add = function (data) {
        this.popups.push(data);
        if (this.popups.length === 1) {
            this.do();
        }
    };
    // 执行第一个
    PopupPnlHelper.prototype.do = function () {
        var data = this.popups[0];
        ViewHelper_1.viewHelper.showPnl.apply(ViewHelper_1.viewHelper, __spread([data.key], (data.params || [])));
    };
    // 下一个
    PopupPnlHelper.prototype.next = function (key) {
        var len = this.popups.length;
        if (len === 0) {
            return;
        }
        for (var i = this.popups.length - 1; i >= 0; i--) {
            if (this.popups[i].key === key) {
                this.popups.splice(i, 1);
            }
        }
        if (this.popups.length > 0) {
            this.do();
        }
    };
    PopupPnlHelper.prototype.isWorking = function () {
        return this.popups.length > 0;
    };
    PopupPnlHelper.prototype.clean = function () {
        this.popups = [];
    };
    return PopupPnlHelper;
}());
exports.popupPnlHelper = new PopupPnlHelper();
if (cc.sys.isBrowser) {
    window['popupPnlHelper'] = exports.popupPnlHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcaGVscGVyXFxQb3B1cFBubEhlbHBlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJDQUEwQztBQU8xQzs7R0FFRztBQUNIO0lBQUE7UUFFWSxXQUFNLEdBQW1CLEVBQUUsQ0FBQTtJQXVDdkMsQ0FBQztJQXJDRyxRQUFRO0lBQ0QsNEJBQUcsR0FBVixVQUFXLElBQWtCO1FBQ3pCLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3RCLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQzFCLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQTtTQUNaO0lBQ0wsQ0FBQztJQUVELFFBQVE7SUFDQSwyQkFBRSxHQUFWO1FBQ0ksSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUMzQix1QkFBVSxDQUFDLE9BQU8sT0FBbEIsdUJBQVUsWUFBUyxJQUFJLENBQUMsR0FBRyxHQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBQztJQUN4RCxDQUFDO0lBRUQsTUFBTTtJQUNDLDZCQUFJLEdBQVgsVUFBWSxHQUFXO1FBQ25CLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFBO1FBQzlCLElBQUksR0FBRyxLQUFLLENBQUMsRUFBRTtZQUNYLE9BQU07U0FDVDtRQUNELEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDOUMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsS0FBSyxHQUFHLEVBQUU7Z0JBQzVCLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTthQUMzQjtTQUNKO1FBQ0QsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDeEIsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFBO1NBQ1o7SUFDTCxDQUFDO0lBRU0sa0NBQVMsR0FBaEI7UUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTtJQUNqQyxDQUFDO0lBRU0sOEJBQUssR0FBWjtRQUNJLElBQUksQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFBO0lBQ3BCLENBQUM7SUFDTCxxQkFBQztBQUFELENBekNBLEFBeUNDLElBQUE7QUFFWSxRQUFBLGNBQWMsR0FBRyxJQUFJLGNBQWMsRUFBRSxDQUFBO0FBQ2xELElBQUksRUFBRSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUU7SUFDbEIsTUFBTSxDQUFDLGdCQUFnQixDQUFDLEdBQUcsc0JBQWMsQ0FBQTtDQUM1QyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi9WaWV3SGVscGVyXCI7XG5cbnR5cGUgUG9wdXBQbmxJbmZvID0ge1xuICAgIGtleTogc3RyaW5nO1xuICAgIHBhcmFtcz86IGFueVtdO1xufVxuXG4vKipcbiAqIOS4u+WKqOW8ueWHulVJXG4gKi9cbmNsYXNzIFBvcHVwUG5sSGVscGVyIHtcblxuICAgIHByaXZhdGUgcG9wdXBzOiBQb3B1cFBubEluZm9bXSA9IFtdXG5cbiAgICAvLyDmt7vliqDliLDpmJ/liJdcbiAgICBwdWJsaWMgYWRkKGRhdGE6IFBvcHVwUG5sSW5mbykge1xuICAgICAgICB0aGlzLnBvcHVwcy5wdXNoKGRhdGEpXG4gICAgICAgIGlmICh0aGlzLnBvcHVwcy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgIHRoaXMuZG8oKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5omn6KGM56ys5LiA5LiqXG4gICAgcHJpdmF0ZSBkbygpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMucG9wdXBzWzBdXG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubChkYXRhLmtleSwgLi4uKGRhdGEucGFyYW1zIHx8IFtdKSlcbiAgICB9XG5cbiAgICAvLyDkuIvkuIDkuKpcbiAgICBwdWJsaWMgbmV4dChrZXk6IHN0cmluZykge1xuICAgICAgICBjb25zdCBsZW4gPSB0aGlzLnBvcHVwcy5sZW5ndGhcbiAgICAgICAgaWYgKGxlbiA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQgaSA9IHRoaXMucG9wdXBzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5wb3B1cHNbaV0ua2V5ID09PSBrZXkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnBvcHVwcy5zcGxpY2UoaSwgMSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5wb3B1cHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgdGhpcy5kbygpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgaXNXb3JraW5nKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wb3B1cHMubGVuZ3RoID4gMFxuICAgIH1cblxuICAgIHB1YmxpYyBjbGVhbigpIHtcbiAgICAgICAgdGhpcy5wb3B1cHMgPSBbXVxuICAgIH1cbn1cblxuZXhwb3J0IGNvbnN0IHBvcHVwUG5sSGVscGVyID0gbmV3IFBvcHVwUG5sSGVscGVyKClcbmlmIChjYy5zeXMuaXNCcm93c2VyKSB7XG4gICAgd2luZG93Wydwb3B1cFBubEhlbHBlciddID0gcG9wdXBQbmxIZWxwZXJcbn0iXX0=