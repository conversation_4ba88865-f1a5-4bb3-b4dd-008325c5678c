
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/CompDebrisPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4b748b5b4NCu53/EJgvARsb', 'CompDebrisPnlCtrl');
// app/script/view/menu/CompDebrisPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var LongClickTouchCmpt_1 = require("../cmpt/LongClickTouchCmpt");
var ccclass = cc._decorator.ccclass;
var CompDebrisPnlCtrl = /** @class */ (function (_super) {
    __extends(CompDebrisPnlCtrl, _super);
    function CompDebrisPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.needItemNode_ = null; // path://root/need/need_item_be_n
        _this.costTitleLbl_ = null; // path://root/cost/title/cost_title_l
        _this.selectLockNode_ = null; // path://root/cost/title/select_lock_be_n
        _this.costItemsSv_ = null; // path://root/cost/cost_items_sv
        _this.tipLbl_ = null; // path://root/line/tip_l
        _this.bottomNode_ = null; // path://root/bottom_n
        _this.inputEb_ = null; // path://root/bottom_n/comp/need_count/input_eb_ebee
        //@end
        _this.portrayalDatas = [];
        _this.canCostDebris = []; //当前可以参与合成的残卷
        _this.costDebrisIndexs = []; //参与合成的残卷
        _this.canLockPortrayals = []; //可以锁定的英雄
        _this.lockPortrayalMap = {}; //当前锁定的map
        return _this;
    }
    CompDebrisPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CompDebrisPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.onRegisterLongTouchEvent();
                this.portrayalDatas = assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
                return [2 /*return*/];
            });
        });
    };
    CompDebrisPnlCtrl.prototype.onEnter = function (data) {
        this.lockPortrayalMap = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.LOCK_COMP_PORTRAYAL) || {};
        this.initCanCostDebris();
        this.closeSelectLock(true);
        this.selectNeedDebris(data);
    };
    CompDebrisPnlCtrl.prototype.onRemove = function () {
        this.canCostDebris.length = 0;
        this.costDebrisIndexs.length = 0;
        this.canLockPortrayals.length = 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.LOCK_COMP_PORTRAYAL, this.lockPortrayalMap);
        this.lockPortrayalMap = {};
    };
    CompDebrisPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/need/need_item_be_n
    CompDebrisPnlCtrl.prototype.onClickNeedItem = function (event, _) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.NEED_DEBRIS, this.portrayalDatas, function (arr) {
            var _a;
            if (_this.isValid && arr.length > 0) {
                var data = arr[0], id = (_a = _this.needItemNode_.Data) === null || _a === void 0 ? void 0 : _a.id;
                if (id !== data.id) {
                    _this.selectNeedDebris(data);
                }
            }
        });
    };
    // path://root/cost/title/select_lock_be_n
    CompDebrisPnlCtrl.prototype.onClickSelectLock = function (event, data) {
        this.openSelectLock();
    };
    // path://root/cost/cost_items_sv/view/content/item_be
    CompDebrisPnlCtrl.prototype.onClickItem = function (event, _) {
        var data = event.target.Data;
        if (!data) {
            return;
        }
        else if (this.selectLockNode_.Data) {
            if (data.select === -1) {
                data.select = 0;
                delete this.lockPortrayalMap[data.id];
            }
            else {
                data.select = -1;
                this.lockPortrayalMap[data.id] = true;
            }
        }
        else if (!this.needItemNode_.Data) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_need_debris');
        }
        else {
            if (data.select === 1) {
                data.select = 0;
                this.costDebrisIndexs.remove(data.index);
            }
            else {
                data.select = 1;
                this.costDebrisIndexs.push(data.index);
            }
            this.updateNeedCount();
        }
        //
        var it = this.costItemsSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.index) === data.index; });
        it.Child('icon/val').opacity = data.select ? 150 : 255;
        it.Child('lock').active = data.select === -1;
        it.Child('select').active = data.select === 1;
    };
    // path://root/bottom_n/comp/need_count/input_eb_ebee
    CompDebrisPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        if (!this.needItemNode_.Data) {
            return this.updateNeedCount();
        }
        var max = Math.floor(this.getCanCompMaxCount() / 3);
        if (max === 0) {
            return this.updateNeedCount();
        }
        var count = Number(event.string.trim()) || 0;
        this.autoSetCostDebris(cc.misc.clampf(count, 0, Math.min(99, max)), true);
    };
    // path://root/bottom_n/comp/need_count/max_be
    CompDebrisPnlCtrl.prototype.onClickMax = function (event, data) {
        if (!this.needItemNode_.Data) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_need_debris');
        }
        var max = Math.floor(this.getCanCompMaxCount() / 3);
        this.autoSetCostDebris(Math.min(99, max), true);
    };
    // path://root/bottom_n/comp/comp_be
    CompDebrisPnlCtrl.prototype.onClickComp = function (event, data) {
        var _this = this;
        var needDebris = this.needItemNode_.Data;
        if (!needDebris) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_need_debris');
        }
        else if (this.costDebrisIndexs.length < 3) {
            return ViewHelper_1.viewHelper.showAlert('toast.cost_comp_debris_deficiency');
        }
        var cnt = Math.floor(this.costDebrisIndexs.length / 3);
        var indexMap = {};
        this.costDebrisIndexs.slice(0, cnt * 3).forEach(function (m) { return indexMap[m] = true; });
        var idMap = {};
        this.canCostDebris.forEach(function (m) {
            if (indexMap[m.index] && m.id !== needDebris.id) {
                idMap[m.id] = (idMap[m.id] || 0) + 1;
            }
        });
        GameHelper_1.gameHpr.user.compPortrayalDebris(needDebris.id, idMap).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
                ViewHelper_1.viewHelper.showGainPortrayalDebris(needDebris.id, cnt);
            }
        });
    };
    // path://root/bottom_n/lock_ok_be
    CompDebrisPnlCtrl.prototype.onClickLockOk = function (event, data) {
        this.closeSelectLock();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CompDebrisPnlCtrl.prototype.onRegisterLongTouchEvent = function () {
        this.bottomNode_.Child('comp/need_count/sub').Component(LongClickTouchCmpt_1.default).on(this.clickSubEvent, this);
        this.bottomNode_.Child('comp/need_count/add').Component(LongClickTouchCmpt_1.default).on(this.clickAddEvent, this);
    };
    CompDebrisPnlCtrl.prototype.clickAddEvent = function () {
        if (!this.needItemNode_.Data) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_need_debris');
        }
        var count = Math.floor(this.costDebrisIndexs.length / 3), max = Math.floor(this.getCanCompMaxCount() / 3);
        this.autoSetCostDebris(Math.min(Math.min(99, max), count + 1), true);
    };
    CompDebrisPnlCtrl.prototype.clickSubEvent = function () {
        if (!this.needItemNode_.Data) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_need_debris');
        }
        var count = Math.floor(this.costDebrisIndexs.length / 3);
        this.autoSetCostDebris(Math.max(0, count - 1), true);
    };
    CompDebrisPnlCtrl.prototype.initCanCostDebris = function () {
        var _this = this;
        this.canCostDebris = [];
        this.canLockPortrayals = [];
        var index = 0;
        GameHelper_1.gameHpr.user.getPortrayals().forEach(function (m, i) {
            if (m.debris > 0) {
                for (var i_1 = 0; i_1 < m.debris && i_1 < 99; i_1++) {
                    _this.canCostDebris.push({ index: index++, id: m.id, select: 0 });
                }
                _this.canLockPortrayals.push({ index: i, id: m.id, select: _this.lockPortrayalMap[m.id] ? -1 : 0 });
            }
        });
        this.canLockPortrayals.sort(function (a, b) { return a.id - b.id; }).forEach(function (m, i) { return m.index = i; });
    };
    // 获取可参与合成的最大数量
    CompDebrisPnlCtrl.prototype.getCanCompMaxCount = function () {
        var _this = this;
        var currNeedItem = this.needItemNode_.Data;
        return this.canCostDebris.filter(function (m) { return !_this.lockPortrayalMap[m.id] && (currNeedItem === null || currNeedItem === void 0 ? void 0 : currNeedItem.id) !== m.id; }).length;
    };
    // 选择需要的残卷
    CompDebrisPnlCtrl.prototype.selectNeedDebris = function (data) {
        var _this = this;
        this.updateNeedDebris(data);
        if (this.selectLockNode_.Data) {
            this.updateLockDebris();
        }
        else {
            // 更新选择
            this.costDebrisIndexs = [];
            if (data) {
                this.canCostDebris.forEach(function (m) {
                    if (m.id !== (data === null || data === void 0 ? void 0 : data.id) && m.select === 1) {
                        _this.costDebrisIndexs.push(m.index);
                    }
                });
                if (this.costDebrisIndexs.length === 0) {
                    this.autoSetCostDebris(1);
                }
            }
            this.updateNeedCount();
            // 刷新列表
            this.updateCostDebris();
        }
    };
    CompDebrisPnlCtrl.prototype.autoSetCostDebris = function (val, updataSelect) {
        var count = val * 3, currNeedItem = this.needItemNode_.Data;
        if (this.costDebrisIndexs.length === count) {
            return;
        }
        else if (this.costDebrisIndexs.length < count) {
            for (var i = 0, l = this.canCostDebris.length; i < l && this.costDebrisIndexs.length < count; i++) {
                var data = this.canCostDebris[i];
                if (this.lockPortrayalMap[data.id] || (currNeedItem === null || currNeedItem === void 0 ? void 0 : currNeedItem.id) === data.id) {
                    continue;
                }
                else if (data.select === 0) {
                    data.select = 1;
                    this.costDebrisIndexs.push(data.index);
                }
            }
        }
        else {
            for (var i = this.canCostDebris.length - 1; i >= 0 && this.costDebrisIndexs.length > count; i--) {
                var data = this.canCostDebris[i];
                if (this.lockPortrayalMap[data.id] || (currNeedItem === null || currNeedItem === void 0 ? void 0 : currNeedItem.id) === data.id) {
                    continue;
                }
                else if (data.select === 1) {
                    data.select = 0;
                    this.costDebrisIndexs.remove(data.index);
                }
            }
        }
        // 刷新选择
        if (updataSelect) {
            this.updateNeedCount();
            this.costItemsSv_.content.children.forEach(function (it) {
                var data = it.Data;
                it.Child('icon/val').opacity = (data === null || data === void 0 ? void 0 : data.select) ? 150 : 255;
                it.Child('lock').active = (data === null || data === void 0 ? void 0 : data.select) === -1;
                it.Child('select').active = (data === null || data === void 0 ? void 0 : data.select) === 1;
            });
        }
    };
    // 刷新需要的残卷
    CompDebrisPnlCtrl.prototype.updateNeedDebris = function (data) {
        this.needItemNode_.Data = data;
        var icon = this.needItemNode_.Child('icon'), add = this.needItemNode_.Child('add');
        add.active = !data;
        if (icon.active = !!data) {
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, icon, this.key);
        }
    };
    // 刷新参与合成的残卷
    CompDebrisPnlCtrl.prototype.updateCostDebris = function () {
        var _this = this;
        var currNeedItem = this.needItemNode_.Data;
        var list = [];
        this.canCostDebris.sort(function (a, b) {
            var aw = (1 - a.select) * 100000 + a.index;
            var bw = (1 - b.select) * 100000 + b.index;
            return aw - bw;
        }).forEach(function (m) {
            if (_this.lockPortrayalMap[m.id] || (currNeedItem === null || currNeedItem === void 0 ? void 0 : currNeedItem.id) === m.id) {
                m.select = 0;
                _this.costDebrisIndexs.remove(m.index);
            }
            else {
                list.push(m);
            }
        });
        this.updateCanCostDebris(list);
    };
    // 刷新锁定的残卷
    CompDebrisPnlCtrl.prototype.updateLockDebris = function () {
        var currNeedItem = this.needItemNode_.Data;
        var list = this.canLockPortrayals.sort(function (a, b) {
            var aw = (1 + a.select) * 100000 + a.index;
            var bw = (1 + b.select) * 100000 + b.index;
            return aw - bw;
        }).filter(function (m) { return m.id !== (currNeedItem === null || currNeedItem === void 0 ? void 0 : currNeedItem.id); });
        this.updateCanCostDebris(list);
    };
    CompDebrisPnlCtrl.prototype.updateCanCostDebris = function (list) {
        var _this = this;
        this.tipLbl_.setLocaleKey(this.selectLockNode_.Data ? 'ui.lock_comp_debris_tip' : 'ui.comp_tip');
        var empty = this.costItemsSv_.Child('empty');
        if (empty.active = !list.length) {
            empty.setLocaleKey(this.selectLockNode_.Data ? 'ui.lock_debris_empty' : 'ui.comp_debris_empty');
        }
        this.costItemsSv_.stopAutoScroll();
        this.costItemsSv_.content.y = 0;
        this.costItemsSv_.List(list.length, function (it, i) {
            var data = it.Data = list[i];
            var icon = it.Child('icon/val');
            it.Child('name').setLocaleKey('portrayalText.name_' + data.id);
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(data.id, icon, _this.key);
            icon.opacity = data.select ? 150 : 255;
            it.Child('select').active = data.select === 1;
            it.Child('lock').active = data.select === -1;
        });
    };
    // 刷新需要的数量
    CompDebrisPnlCtrl.prototype.updateNeedCount = function () {
        this.inputEb_.string = Math.floor(this.costDebrisIndexs.length / 3) + '';
    };
    // 打开锁定
    CompDebrisPnlCtrl.prototype.openSelectLock = function () {
        this.selectLockNode_.active = false;
        this.selectLockNode_.Data = true;
        this.bottomNode_.Swih('lock_ok_be');
        this.costTitleLbl_.setLocaleKey('ui.select_lock_portrayal');
        this.updateLockDebris();
    };
    // 关闭锁定
    CompDebrisPnlCtrl.prototype.closeSelectLock = function (init) {
        this.selectLockNode_.active = true;
        this.selectLockNode_.Data = false;
        this.bottomNode_.Swih('comp');
        this.costTitleLbl_.setLocaleKey('ui.select_portrayal_1');
        if (!init) {
            this.updateCostDebris();
            this.updateNeedCount();
        }
    };
    CompDebrisPnlCtrl = __decorate([
        ccclass
    ], CompDebrisPnlCtrl);
    return CompDebrisPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CompDebrisPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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