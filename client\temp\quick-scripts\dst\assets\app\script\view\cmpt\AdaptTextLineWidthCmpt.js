
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AdaptTextLineWidthCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '597a53gLwhCsJjpUeFnB/nq', 'AdaptTextLineWidthCmpt');
// app/script/view/cmpt/AdaptTextLineWidthCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
/**
 * 适配文本和线（两端线中间文本）
 */
var AdaptTextLineWidthCmpt = /** @class */ (function (_super) {
    __extends(AdaptTextLineWidthCmpt, _super);
    function AdaptTextLineWidthCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.node_ = null;
        _this.lines = [];
        _this.interval = 0;
        _this.preLableWidth = -1;
        return _this;
    }
    AdaptTextLineWidthCmpt.prototype.update = function (dt) {
        if (this.node_ === null || this.node_.width === this.preLableWidth) {
            return;
        }
        this.preLableWidth = this.node_.width;
        var width = this.node.width - this.preLableWidth - this.interval * this.lines.length;
        if (width > 0) {
            width = Math.floor(width / this.lines.length);
            this.lines.forEach(function (it) { return it.width = width; });
        }
        else {
            this.lines.forEach(function (it) { return it.width = 0; });
        }
    };
    __decorate([
        property(cc.Node)
    ], AdaptTextLineWidthCmpt.prototype, "node_", void 0);
    __decorate([
        property([cc.Node])
    ], AdaptTextLineWidthCmpt.prototype, "lines", void 0);
    __decorate([
        property
    ], AdaptTextLineWidthCmpt.prototype, "interval", void 0);
    AdaptTextLineWidthCmpt = __decorate([
        ccclass
    ], AdaptTextLineWidthCmpt);
    return AdaptTextLineWidthCmpt;
}(cc.Component));
exports.default = AdaptTextLineWidthCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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