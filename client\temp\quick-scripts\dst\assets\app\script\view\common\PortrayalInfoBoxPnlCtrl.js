
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PortrayalInfoBoxPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '72ec6GswYVOwYtgXev4OTr1', 'PortrayalInfoBoxPnlCtrl');
// app/script/view/common/PortrayalInfoBoxPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PortrayalInfoBoxPnlCtrl = /** @class */ (function (_super) {
    __extends(PortrayalInfoBoxPnlCtrl, _super);
    function PortrayalInfoBoxPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.descNode_ = null; // path://root_n/desc_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    PortrayalInfoBoxPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PortrayalInfoBoxPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    PortrayalInfoBoxPnlCtrl.prototype.onEnter = function (data, fromTo, owner) {
        var isFromPawn = fromTo === 'pawn';
        if (this.rootNode_.Child('title').active = !isFromPawn) {
            this.rootNode_.Child('title/val').setLocaleKey(data.getChatName());
        }
        var root = this.rootNode_.Child('attrs');
        // 名字
        ResHelper_1.resHelper.loadHeroSkillIcon(data.json.skill, root.Child('icon/val'), this.key);
        root.Child('icon/name/val').setLocaleKey('portrayalSkillText.name_' + data.json.skill);
        root.Child('icon/name/pawn').setLocaleKey('ui.avatar_pawn_desc', data.avatarPawnName);
        // 小人
        if (root.Child('icon/hero').active = !isFromPawn) {
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, root.Child('icon/hero/val'), this.key);
        }
        // 属性
        root.Child('attr').Items(data.mainAttrs, function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = '+' + d.value;
        });
        // 技能
        var skillNode = root.Child('skill');
        if (skillNode.active = !!data.skill) {
            skillNode.setLocaleKey(data.skill.desc, data.skill.getDescParams());
        }
        // 韬略
        root.Child('strategy/name/count', cc.Label).string = "(" + data.strategys.length + ")";
        root.Child('strategys').Items(data.strategys, function (it, strategy) { return ViewHelper_1.viewHelper.showStrategyText(it, strategy, data.avatarPawnName); });
        // 说明
        if (this.descNode_.active = fromTo === 'hero_hall') {
            var info = GameHelper_1.gameHpr.player.getHeroSlots().find(function (m) { var _a; return ((_a = m.hero) === null || _a === void 0 ? void 0 : _a.id) === data.id; });
            if (info) {
                var _a = __read(info.getAvatarDesc(), 3), key = _a[0], params = _a[1], color = _a[2];
                this.descNode_.Child('val').Color(color).setLocaleKey(key, params);
            }
            else {
                this.descNode_.active = false;
            }
        }
    };
    PortrayalInfoBoxPnlCtrl.prototype.onRemove = function () {
    };
    PortrayalInfoBoxPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    PortrayalInfoBoxPnlCtrl = __decorate([
        ccclass
    ], PortrayalInfoBoxPnlCtrl);
    return PortrayalInfoBoxPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PortrayalInfoBoxPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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