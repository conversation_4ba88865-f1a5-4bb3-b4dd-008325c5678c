
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/proto/ProtoHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2aa0dDlBotJdYvAZxT2OF/y', 'ProtoHelper');
// app/proto/ProtoHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.protoHelper = void 0;
var MapHelper_1 = require("../script/common/helper/MapHelper");
var ProtoHelper = /** @class */ (function () {
    function ProtoHelper() {
        this.byteMask = [];
    }
    ProtoHelper.prototype.addFlags = function () {
        var flags = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            flags[_i] = arguments[_i];
        }
        var data = 0;
        for (var i = 0; i < flags.length; i++) {
            data |= (1 << flags[i]);
        }
        return data;
    };
    ProtoHelper.prototype.checkFlag = function (data, flag) {
        return (data & (1 << flag)) == (1 << flag);
    };
    // 解压缩字节数组到数字 size数组为压缩时拼接的数据所占位数
    ProtoHelper.prototype.uncompressBytesToNumber = function (bytes) {
        var sizes = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            sizes[_i - 1] = arguments[_i];
        }
        var ret = [];
        var data = 0;
        var lastIndex = -1; // 上个字节下标
        var curSizeIndex = 0; // 当前数据所占位数下标
        var curDataBitCount = sizes[curSizeIndex]; // 当前数据待读取位数
        for (var i = 0; i < bytes.length; i++) {
            var b = bytes[i];
            if (curDataBitCount > 8) {
                // 当前字节不够数据读取 继续读取下个字节
                data = data << 8 | b;
                curDataBitCount -= 8;
                continue;
            }
            else {
                // 当前字节足够剩余数据读取
                var leftBitCount = 8 - curDataBitCount;
                data = (data << curDataBitCount) | (b >> leftBitCount);
                ret.push(data);
                // 下一个数据
                data = 0;
                curSizeIndex = (curSizeIndex + 1) % sizes.length;
                curDataBitCount = sizes[curSizeIndex];
                if (leftBitCount > 0) {
                    // 当前字节仍剩余比特位 下一个数据继续读取该字节
                    data = b & this.getByteMask()[leftBitCount];
                    curDataBitCount -= leftBitCount;
                }
            }
        }
        return ret;
    };
    // 获取字节掩码数组
    ProtoHelper.prototype.getByteMask = function () {
        if (this.byteMask.length === 0) {
            this.initByteMasks();
        }
        return this.byteMask;
    };
    ProtoHelper.prototype.initByteMasks = function () {
        for (var i = 0; i < 8 + 1; i++) {
            if (i === 0) {
                this.byteMask.push(0);
            }
            else {
                this.byteMask.push(this.byteMask[i - 1] + Math.pow(2, (i - 1)));
            }
        }
    };
    ProtoHelper.prototype.uncompressPlayerCellsBytes = function (cellInfo) {
        var mapSize = MapHelper_1.mapHelper.MAP_SIZE;
        var matrixCells = this.uncompressBytesToNumber(cellInfo.indexs1, 19, 19);
        var playerCells = [];
        for (var i = 0; i < matrixCells.length; i += 2) {
            var start = matrixCells[i];
            var end = matrixCells[i + 1];
            var startX = start % mapSize.x;
            var endX = end % mapSize.x;
            var dirx = endX - startX;
            var dirY = (end - endX) / mapSize.y - (start - startX) / mapSize.y;
            for (var y = 0; y <= dirY; y++) {
                for (var x = 0; x <= dirx; x++) {
                    playerCells.push(start + y * mapSize.x + x);
                }
            }
        }
        var singleCells = this.uncompressBytesToNumber(cellInfo.indexs2, 19);
        playerCells = playerCells.concat(singleCells);
        var cities = this.uncompressBytesToNumber(cellInfo.cities, 19, 8);
        var citiesMap = {};
        for (var i = 0; i < cities.length; i += 2) {
            citiesMap[cities[i]] = cities[i + 1];
        }
        return { playerCells: playerCells, citiesMap: citiesMap };
    };
    return ProtoHelper;
}());
exports.protoHelper = new ProtoHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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