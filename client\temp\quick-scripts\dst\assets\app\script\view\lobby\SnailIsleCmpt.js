
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/SnailIsleCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ea0cefsa15C7pza3A9Vx3gG', 'SnailIsleCmpt');
// app/script/view/lobby/SnailIsleCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LobbyModeCmpt_1 = require("./LobbyModeCmpt");
var SBuildCmpt_1 = require("./SBuildCmpt");
var SceneRoleCmpt_1 = require("./SceneRoleCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 蜗牛岛
var SnailIsleCmpt = /** @class */ (function (_super) {
    __extends(SnailIsleCmpt, _super);
    function SnailIsleCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = 'lobby';
        _this.root = null;
        _this.likeCountLbl = null;
        _this.selfLikeCountLbl = null;
        _this.flowerpotNode = null;
        _this.flowerpotColliderPoints = [];
        _this.modeCmpt = null;
        _this.model = null;
        _this.builds = []; //当前场景的设施
        _this.roles = []; //当前场景角色
        _this.preLikeJwmCount = -1;
        _this.plantNode = null; //当前种植的节点
        _this.touchId = -1;
        _this.tempVec1 = cc.v2();
        _this.tempVec2 = cc.v2();
        _this.tempVec3 = cc.v2();
        return _this;
    }
    SnailIsleCmpt.prototype.create = function () {
        var _this = this;
        this.root = this.FindChild('root');
        this.likeCountLbl = this.FindChild('title/val', cc.LabelRollNumber);
        this.selfLikeCountLbl = this.FindChild('desc/val', cc.Label);
        this.flowerpotNode = this.FindChild('flowerpot');
        this.flowerpotColliderPoints = this.flowerpotNode.getComponent(cc.PolygonCollider).points;
        this.modeCmpt = this.node.parent.parent.getComponent(LobbyModeCmpt_1.default);
        this.model = GameHelper_1.gameHpr.lobby.getSnailIsle();
        this.model.getBuilds().forEach(function (m) { return _this.createBuild(m); });
        this.model.roles.forEach(function (m) { return _this.createRole(m); });
        this.flowerpotNode.on(cc.Node.EventType.TOUCH_START, this.onClickFlowerpotStart, this);
        this.flowerpotNode.on(cc.Node.EventType.TOUCH_END, this.onClickFlowerpot, this);
        // this.flowerpotNode.SetSwallowTouches(false) //默认开启穿透
    };
    SnailIsleCmpt.prototype.init = function () {
        this.updateLikeJwmCount(0);
        this.updatePlant();
        this.model.setActive(true);
    };
    SnailIsleCmpt.prototype.enter = function () {
        this.updateLikeJwmCount(0);
        this.updatePlant();
        // this.model.setActive(true)
    };
    SnailIsleCmpt.prototype.leave = function () {
        // this.model.setActive(false)
    };
    SnailIsleCmpt.prototype.clean = function () {
        var _a;
        this.model.setActive(false);
        this.flowerpotNode.off(cc.Node.EventType.TOUCH_START, this.onClickFlowerpotStart, this);
        this.flowerpotNode.off(cc.Node.EventType.TOUCH_END, this.onClickFlowerpot, this);
        this.touchId = -1;
        while (this.builds.length > 0) {
            this.builds.pop().destroy();
        }
        while (this.roles.length > 0) {
            this.roles.pop().clean();
        }
        (_a = this.plantNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.plantNode = null;
    };
    SnailIsleCmpt.prototype.updateLikeJwmCount = function (state) {
        this.selfLikeCountLbl.string = GameHelper_1.gameHpr.user.getAccLikeJwmCount() + '';
        var likeJwmCount = GameHelper_1.gameHpr.lobby.getSumLikeJwmCount();
        if (this.preLikeJwmCount === likeJwmCount) {
            return;
        }
        this.preLikeJwmCount = likeJwmCount;
        var node = this.likeCountLbl.node.parent;
        node.scale = 1;
        if (state === 1) {
            this.likeCountLbl.to(likeJwmCount);
            cc.tween(node)
                .to(0.15, { scale: 1.2 })
                .to(0.15, { scale: 1 })
                .start();
        }
        else if (state === 2) {
            this.likeCountLbl.to(likeJwmCount);
        }
        else {
            this.likeCountLbl.set(likeJwmCount);
        }
    };
    // 将数字转换为String
    SnailIsleCmpt.prototype.simplifyEn = function (val) {
        var value = Math.abs(val);
        if (value >= 1000000000000) {
            return parseFloat((val / 1000000000000).toFixed(2)) + 'T';
        }
        else if (value >= 1000000000) {
            return parseFloat((val / 1000000000).toFixed(2)) + 'B';
        }
        else if (value >= 1000000) {
            return parseFloat((val / 1000000).toFixed(2)) + 'M';
        }
        else {
            return val + '';
        }
    };
    SnailIsleCmpt.prototype.createBuild = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var url, key, pfb, it;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        url = data.getUrl(), key = this.key;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isValid) {
                            assetsMgr.releaseTempRes(url, key);
                            return [2 /*return*/, null];
                        }
                        it = cc.instantiate2(pfb, this.root);
                        it.Data = { url: url, id: data.id, data: data };
                        it.zIndex = data.zIndex;
                        it.setPosition(this.model.getActPixelByPoint(data.point));
                        data.rootPosition = ut.convertToNodeAR(it, this.root).clone();
                        (_a = it.Component(SBuildCmpt_1.default)) === null || _a === void 0 ? void 0 : _a.init(data);
                        this.builds.push(it);
                        return [2 /*return*/, it];
                }
            });
        });
    };
    // 创建角色
    SnailIsleCmpt.prototype.createRole = function (role) {
        return __awaiter(this, void 0, void 0, function () {
            var node, cmpt;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.roles.has('uid', role.uid) || this.model.uid !== role.getMapUid()) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('role/' + role.bodyUrl, this.key)];
                    case 1:
                        node = _a.sent();
                        if (!this.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(node)];
                        }
                        node.parent = this.root;
                        cmpt = node.Component(SceneRoleCmpt_1.default).init(role);
                        this.roles.push(cmpt);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新种植信息
    SnailIsleCmpt.prototype.updatePlant = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var root, data, pfb, mf, frameCount, remainTime, index;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        this.flowerpotNode.Child('watering/val', cc.Animation).play('watering_stop');
                        root = this.flowerpotNode.Child('root');
                        data = GameHelper_1.gameHpr.user.getPlantData();
                        if (!!(data === null || data === void 0 ? void 0 : data.id)) return [3 /*break*/, 1];
                        return [2 /*return*/, root.removeAllChildren()];
                    case 1:
                        if (!(((_b = (_a = this.plantNode) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.id) !== data.id)) return [3 /*break*/, 3];
                        (_c = this.plantNode) === null || _c === void 0 ? void 0 : _c.destroy();
                        this.plantNode = null;
                        root.removeAllChildren();
                        return [4 /*yield*/, assetsMgr.loadTempRes('plant/PLANT_' + data.id, cc.Prefab, this.key)];
                    case 2:
                        pfb = _e.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        this.plantNode = cc.instantiate2(pfb, root);
                        _e.label = 3;
                    case 3:
                        this.plantNode.Data = data;
                        this.plantNode.Child('done').active = !data.remainTime;
                        mf = this.plantNode.Child('val', cc.MultiFrame), frameCount = mf.frameCount() - 1;
                        if (data.remainTime) {
                            if (!data.needTime) {
                                data.needTime = (((_d = assetsMgr.getJsonData('botany', data.id)) === null || _d === void 0 ? void 0 : _d.time) || 1) * ut.Time.Day;
                            }
                            remainTime = Math.max(0, data.remainTime - (Date.now() - data.getTime));
                            index = Math.floor(Math.max(0, data.needTime - remainTime) / (data.needTime / frameCount));
                            mf.setFrame(cc.misc.clampf(index, 0, frameCount));
                        }
                        else {
                            mf.setFrame(frameCount);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SnailIsleCmpt.prototype.onClickFlowerpotStart = function (event) {
        if (this.touchId !== -1) {
            return;
        }
        this.touchId = event.getID();
    };
    // 点击花盆
    SnailIsleCmpt.prototype.onClickFlowerpot = function (event) {
        var _this = this;
        var _a;
        if (this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        if (event.getStartLocation().sub(event.getLocation(), this.tempVec3).mag() > 7) {
            return;
        }
        var pos = this.flowerpotNode.convertToNodeSpaceAR(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(event.getLocation(), this.tempVec1), this.tempVec2);
        if (!cc.Intersection.pointInPolygon(pos, this.flowerpotColliderPoints)) {
            return;
        }
        else if ((_a = this.modeCmpt) === null || _a === void 0 ? void 0 : _a.isRolling()) {
            return;
        }
        var data = GameHelper_1.gameHpr.user.getPlantData();
        if (!(data === null || data === void 0 ? void 0 : data.id)) {
            ViewHelper_1.viewHelper.showPnl('lobby/SelectPlantSeed'); //选择种子
        }
        else if (data.isWatering) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TODAY_YET_WATERING);
        }
        else if (data.remainTime === 0) { //可采集
            ViewHelper_1.viewHelper.showMessageBox('ui.gather_botany_tip', {
                params: ['ui.botany_name_' + data.id],
                ok: function () { return GameHelper_1.gameHpr.user.gatherBotany().then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        _this.playGather(data.id);
                    }
                }); },
                cancel: function () { },
            });
        }
        else { //浇水
            ViewHelper_1.viewHelper.showPnl('lobby/SelectWatering', function (type) {
                if (type && _this.isValid) {
                    _this.wateringPlant(type);
                }
            });
        }
    };
    // 浇水
    SnailIsleCmpt.prototype.wateringPlant = function (type) {
        var _this = this;
        var anim = this.flowerpotNode.Child('watering/val', cc.Animation);
        anim.playAsync('watering_' + type).then(function () {
            if (_this.isValid) {
                anim.play('watering_stop');
            }
        });
    };
    // 播放采集
    SnailIsleCmpt.prototype.playGather = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, it, y;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('flutter/FLUTTER_GATHER', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        it = cc.instantiate2(pfb, this.flowerpotNode);
                        ResHelper_1.resHelper.loadGiftIcon(id, it.Child('lay/icon', cc.Sprite), this.key);
                        y = 8;
                        it.setPosition(0, y);
                        it.opacity = 0;
                        it.scale = 0.5;
                        cc.tween(it)
                            .delay(0.3)
                            .call(function () { return it.opacity = 255; })
                            .to(0.3, { y: y + 30, scale: 1 }, { easing: cc.easing.sineOut })
                            .delay(0.5)
                            .to(1.5, { y: y + 70, opacity: 0 }, { easing: cc.easing.sineIn })
                            .call(function () { return assetsMgr.releaseTempRes('flutter/FLUTTER_GATHER', _this.key); }).start();
                        return [2 /*return*/];
                }
            });
        });
    };
    SnailIsleCmpt = __decorate([
        ccclass
    ], SnailIsleCmpt);
    return SnailIsleCmpt;
}(cc.Component));
exports.default = SnailIsleCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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