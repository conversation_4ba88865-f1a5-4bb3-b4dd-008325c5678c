
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/GuideTaskPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '19a553C9GxKDJ6iQNDeMcFy', 'GuideTaskPnlCtrl');
// app/script/view/common/GuideTaskPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GotoHelper_1 = require("../../common/helper/GotoHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var GuideConfig_1 = require("../../model/guide/GuideConfig");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var Enums_2 = require("../../common/constant/Enums");
var ccclass = cc._decorator.ccclass;
var GuideTaskPnlCtrl = /** @class */ (function (_super) {
    __extends(GuideTaskPnlCtrl, _super);
    function GuideTaskPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.showTipTge_ = null; // path://root/title/show_tip_t_te
        _this.listNode_ = null; // path://root/list_n
        //@end
        _this.player = null;
        return _this;
    }
    GuideTaskPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_GUIDE_TASK_STATE] = this.onUpdateGuideTaskState, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.CHANGE_SHOW_GUIDE_TASK_TIP] = this.onChangeShowGuideTaskTip, _b.enter = true, _b),
        ];
    };
    GuideTaskPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    GuideTaskPnlCtrl.prototype.onEnter = function (data) {
        ReddotHelper_1.reddotHelper.pause('guide_task', true);
        this.player.updateGuideTaskState();
        this.player.updateTodayTaskState();
        this.player.updateOtherTaskState();
        this.updateList();
        this.onChangeShowGuideTaskTip();
    };
    GuideTaskPnlCtrl.prototype.onRemove = function () {
        ReddotHelper_1.reddotHelper.pause('guide_task', false);
    };
    GuideTaskPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_n/item/claim_be
    GuideTaskPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        else if (data.needSelectReward) { //需要选择
            return ViewHelper_1.viewHelper.showPnl('common/SelectTaskReward', data);
        }
        else if (data.treasureRewards.length > 0) { //宝箱
            return ViewHelper_1.viewHelper.showPnl('common/TaskTreasureList', data);
        }
        else {
            var items = GameHelper_1.gameHpr.checkRewardFull(data.rewards);
            if (items.length > 0) {
                return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { return ok && _this.claimReward(data); });
            }
        }
        this.claimReward(data);
    };
    // path://root/list_n/item/go_to_be
    GuideTaskPnlCtrl.prototype.onClickGoTo = function (event, _) {
        var _this = this;
        var data = event.target.parent.Data;
        GotoHelper_1.gotoHelper.runByTaskCond(data.cond).then(function (ok) {
            if (ok) {
                if (_this.isValid) {
                    _this.hide();
                }
            }
            else {
                task2Forward(data).then(function (ok2) {
                    if (ok2 && _this.isValid) {
                        _this.hide();
                    }
                });
            }
        });
    };
    // path://root/title/show_tip_t_te
    GuideTaskPnlCtrl.prototype.onClickShowTip = function (event, data) {
        var _a;
        audioMgr.playSFX('click');
        if ((_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.isBattleing()) {
            event.isChecked = false;
            return ViewHelper_1.viewHelper.showAlert('toast.open_battleing_task_tip');
        }
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_GUIDE_TASK_TIP, event.isChecked);
        this.emit(EventType_1.default.CHANGE_SHOW_GUIDE_TASK_TIP, event.isChecked);
    };
    // path://root/list_n/item/info/help/help_be
    GuideTaskPnlCtrl.prototype.onClickHelp = function (event, _) {
        var data = event.target.parent.parent.parent.Data;
        if (data.cond.type === Enums_2.TCType.WEAR_EQUIP_DEF) {
            ViewHelper_1.viewHelper.showPnl('help/NoticeDefaultEquip');
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    GuideTaskPnlCtrl.prototype.onUpdateGuideTaskState = function () {
        this.updateList();
    };
    GuideTaskPnlCtrl.prototype.onChangeShowGuideTaskTip = function () {
        var _a;
        this.showTipTge_.isChecked = GameHelper_1.gameHpr.task.isShowTaskTip() && !((_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.isBattleing());
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    GuideTaskPnlCtrl.prototype.updateList = function () {
        var list = this.player.getPlayerAllTasks();
        if (list.length === 0) {
            return this.hide();
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            list.sort(function (a, b) { return a.id - b.id; });
        }
        this.listNode_.Items(list, function (it, data) {
            var _a;
            it.Data = data;
            if (data.id === GuideConfig_1.GuideTextArgsType.PawnToHeroId) {
                var pawnId = GameHelper_1.gameHpr.user.getPortrayals()[0].json.avatar_pawn;
                var pawnName = assetsMgr.lang('pawnText.name_' + pawnId);
                it.Child('desc/val').setLocaleKey(data.desc, [pawnName]);
            }
            else {
                it.Child('desc/val').setLocaleKey(data.desc, data.descParams);
            }
            var _b = __read(data.getProgress(), 2), a = _b[0], b = _b[1], isCanClaim = data.isCanClaim();
            it.Child('desc/progress', cc.Label).string = b > 0 ? "(" + a + "/" + b + ")" : '';
            it.Child('claim_be').active = isCanClaim;
            var doing = it.Child('doing').active = !isCanClaim && data.isDoing();
            it.Child('go_to_be').active = !isCanClaim && !doing && (GotoHelper_1.gotoHelper.isCanGotoByTaskCond((_a = data.cond) === null || _a === void 0 ? void 0 : _a.type) || taskCheckGoto(data));
            var info = it.Child('info');
            if (data.rewards.length > 0) {
                info.Child('award/desc').setLocaleKey('ui.award_name');
                ViewHelper_1.viewHelper.updateItemByCTypes(info.Child('award/list'), data.rewards);
                info.Child('award/select').setLocaleKey(data.needSelectReward ? 'ui.choice_of_which' : '', data.rewards.length);
            }
            else {
                info.Child('award/desc').setLocaleKey('ui.guide_no_award_tip');
                info.Child('award/list').Items(0);
                info.Child('award/select').setLocaleKey('');
            }
            if (info.Child('tip').active = !!data.tip) {
                info.Child('tip/val').setLocaleKey(data.tip);
            }
            if (info.Child('help').active = !!data.help) {
                info.Child('help/help_be').setLocaleKey(data.help);
            }
            it.height = (!!data.tip || !!data.help) /* && data.rewards.length > 0 */ ? 148 : 108;
        });
    };
    // 显示英雄自选
    GuideTaskPnlCtrl.prototype.showHeroOptSelect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var heroList, portrayalBase, key;
            return __generator(this, function (_a) {
                heroList = [320101];
                portrayalBase = assetsMgr.getJson('portrayalBase').dataIdMap;
                for (key in portrayalBase) {
                    if (portrayalBase[key].avatar_pawn === GameHelper_1.gameHpr.player.getPawnSlotByLv(Constant_1.PAWN_SLOT_CONF[1]).id) {
                        heroList.push(Number(key));
                        break;
                    }
                }
                return [2 /*return*/, new Promise(function (resolve) {
                        var list = heroList.map(function (m) { return new PortrayalInfo_1.default().init(m); });
                        ViewHelper_1.viewHelper.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.GIFT, list, function (arr) { var _a, _b; return resolve((_b = (_a = arr[0]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0); }, 1);
                    })];
            });
        });
    };
    GuideTaskPnlCtrl.prototype.claimReward = function (data) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var heroId, claimTaskRewardFunc;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!(data.id === GuideConfig_1.GuideTextArgsType.HeroPackageId)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.showHeroOptSelect()];
                    case 1:
                        heroId = _c.sent();
                        if (!heroId) {
                            return [2 /*return*/];
                        }
                        GameHelper_1.gameHpr.noviceServer.genHeroReward(heroId, 3);
                        _c.label = 2;
                    case 2:
                        claimTaskRewardFunc = this.player.claimTaskReward.bind(this.player);
                        if (((_a = data.json) === null || _a === void 0 ? void 0 : _a.type) === 20) { //每日任务
                            claimTaskRewardFunc = this.player.claimTodayTaskReward.bind(this.player);
                        }
                        else if (((_b = data.json) === null || _b === void 0 ? void 0 : _b.type) === 30) { //其他任务
                            claimTaskRewardFunc = this.player.claimOtherTaskReward.bind(this.player);
                        }
                        claimTaskRewardFunc(data.id).then(function (err) {
                            if (err) {
                                return ViewHelper_1.viewHelper.showAlert(err);
                            }
                            GameHelper_1.gameHpr.addGainMassage(data.rewards);
                            ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
                            if (_this.isValid) {
                                _this.updateList();
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    GuideTaskPnlCtrl = __decorate([
        ccclass
    ], GuideTaskPnlCtrl);
    return GuideTaskPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GuideTaskPnlCtrl;
var focuCell = function (index) {
    return __awaiter(this, void 0, void 0, function () {
        var world, scene, cell;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    world = GameHelper_1.gameHpr.world, scene = world.getSceneKey();
                    if (!(mc.currWindName !== scene)) return [3 /*break*/, 2];
                    return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind(scene)];
                case 1:
                    _a.sent();
                    _a.label = 2;
                case 2:
                    cell = world.getMapCellByIndex(index);
                    if (cell) {
                        eventCenter.emit(EventType_1.default.MAP_MOVE_TO, cell.point, true);
                    }
                    return [2 /*return*/, true];
            }
        });
    });
};
var gotoPnl = function (buildId, tab) {
    var _a;
    return __awaiter(this, void 0, void 0, function () {
        var area, build;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (!(mc.currWindName === 'novice' || ((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) return [3 /*break*/, 2];
                    GameHelper_1.gameHpr.world.setLookCell(GameHelper_1.gameHpr.novice.getMapCellByIndex(NoviceConfig_1.NOVICE_MAINCITY_INDEX));
                    return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                case 1:
                    _b.sent();
                    _b.label = 2;
                case 2:
                    area = GameHelper_1.gameHpr.areaCenter.getArea(NoviceConfig_1.NOVICE_MAINCITY_INDEX);
                    build = area.getBuildById(buildId);
                    return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build, tab)];
                case 3:
                    _b.sent();
                    return [2 /*return*/, false];
            }
        });
    });
};
var taskCheckGoto = function (data) {
    if (data.cond.type === Enums_2.TCType.CUSTOM) {
        switch (data.cond.id) {
            case Enums_1.TCusCType.OPEN_TREASURE:
            case Enums_1.TCusCType.CURE_ONE_PAWN:
            case Enums_1.TCusCType.CHANGE_PAWN_EQUIP:
            case Enums_1.TCusCType.OCCUPY_ENEMY_LAND:
            case Enums_1.TCusCType.DESTROY_ENEMY_TOWER:
            case Enums_1.TCusCType.CAPTURE_ENEMY_CITY:
                return true;
        }
    }
    return false;
};
//新手村任务前往逻辑
var task2Forward = function (data) {
    var _a;
    return __awaiter(this, void 0, void 0, function () {
        var hasTreasure, cell, treasureExsit;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    if (!(data.cond.type === Enums_2.TCType.CUSTOM)) return [3 /*break*/, 20];
                    if (!(data.cond.id === Enums_1.TCusCType.OPEN_TREASURE)) return [3 /*break*/, 10];
                    hasTreasure = GameHelper_1.gameHpr.noviceServer.checkPlayerHasTreasure(GameHelper_1.gameHpr.user.getUid());
                    if (!hasTreasure) return [3 /*break*/, 8];
                    if (!(mc.currWindName === 'novice')) return [3 /*break*/, 2];
                    return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('main/ArmyList')];
                case 1:
                    _b.sent();
                    return [2 /*return*/, true];
                case 2:
                    if (!(mc.currWindName === 'area')) return [3 /*break*/, 7];
                    cell = GameHelper_1.gameHpr.world.getLookCell();
                    treasureExsit = (_a = cell === null || cell === void 0 ? void 0 : cell.getArea().armys) === null || _a === void 0 ? void 0 : _a.some(function (m) { var _a; return (_a = m.pawns) === null || _a === void 0 ? void 0 : _a.some(function (p) { return p === null || p === void 0 ? void 0 : p.treasures.length; }); });
                    if (!treasureExsit) return [3 /*break*/, 4];
                    return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('area/AreaArmy')];
                case 3:
                    _b.sent();
                    return [2 /*return*/, true];
                case 4: return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('novice')];
                case 5:
                    _b.sent();
                    return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('main/ArmyList')];
                case 6:
                    _b.sent();
                    return [2 /*return*/, true];
                case 7: return [3 /*break*/, 9];
                case 8:
                    ViewHelper_1.viewHelper.showAlert('toast.occupy_for_treasure');
                    return [2 /*return*/, true];
                case 9: return [3 /*break*/, 20];
                case 10:
                    if (!(data.cond.id === Enums_1.TCusCType.CURE_ONE_PAWN)) return [3 /*break*/, 12];
                    return [4 /*yield*/, gotoAreaBTBuild(Constant_1.BUILD_HOSPITAL_NID, 1)];
                case 11: return [2 /*return*/, _b.sent()];
                case 12:
                    if (!(data.cond.id === Enums_1.TCusCType.CHANGE_PAWN_EQUIP)) return [3 /*break*/, 14];
                    return [4 /*yield*/, gotoWearEquip(2)];
                case 13: return [2 /*return*/, _b.sent()];
                case 14:
                    if (!(data.cond.id === Enums_1.TCusCType.OCCUPY_ENEMY_LAND)) return [3 /*break*/, 16];
                    return [4 /*yield*/, gotoEnemyLandCell(0)];
                case 15: return [2 /*return*/, _b.sent()];
                case 16:
                    if (!(data.cond.id === Enums_1.TCusCType.DESTROY_ENEMY_TOWER)) return [3 /*break*/, 18];
                    return [4 /*yield*/, gotoEnemyLandCell(Constant_1.CITY_FORT_NID)];
                case 17: return [2 /*return*/, _b.sent()];
                case 18:
                    if (!(data.cond.id === Enums_1.TCusCType.CAPTURE_ENEMY_CITY)) return [3 /*break*/, 20];
                    return [4 /*yield*/, gotoEnemyLandCell(Constant_1.CITY_MAIN_NID)];
                case 19: return [2 /*return*/, _b.sent()];
                case 20: return [2 /*return*/];
            }
        });
    });
};
// 跳转到区域修建 建筑
var gotoAreaBTBuild = function (id, tab) {
    return __awaiter(this, void 0, void 0, function () {
        var world, index, cell, area, build;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (mc.currWindName === 'lobby') {
                        ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                        return [2 /*return*/, false];
                    }
                    world = GameHelper_1.gameHpr.world;
                    index = GameHelper_1.gameHpr.player.getMainCityIndex();
                    cell = world.getLookCell();
                    if (!((cell === null || cell === void 0 ? void 0 : cell.actIndex) !== index)) return [3 /*break*/, 4];
                    cell = world.getMapCellByIndex(index);
                    if (!cell) {
                        return [2 /*return*/, false];
                    }
                    world.setLookCell(cell);
                    if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                    return [4 /*yield*/, eventCenter.req(EventType_1.default.REENTER_AREA_WIND)];
                case 1:
                    _a.sent();
                    return [3 /*break*/, 4];
                case 2: return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                case 3:
                    _a.sent();
                    _a.label = 4;
                case 4:
                    area = GameHelper_1.gameHpr.areaCenter.getArea(cell.index);
                    if (!area) {
                        return [2 /*return*/, false];
                    }
                    else if (GameHelper_1.gameHpr.guide.isForceWorking()) {
                        return [2 /*return*/, true]; //在引导的话 就不弹了
                    }
                    build = area.getBuildById(id);
                    if (!build) {
                        ViewHelper_1.viewHelper.showPnl('area/BuildList', area, id);
                    }
                    else if (build.lv > 0) {
                        ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build, tab);
                    }
                    return [2 /*return*/, true];
            }
        });
    });
};
var gotoEnemyLandCell = function (cityId) {
    return __awaiter(this, void 0, void 0, function () {
        var enemyCells, playerMainCell_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                    return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('novice')];
                case 1:
                    _a.sent();
                    _a.label = 2;
                case 2:
                    enemyCells = GameHelper_1.gameHpr.novice.getMapCells().filter(function (c) { return c.owner === GameHelper_1.gameHpr.noviceServer.getEnemyUID() && c.cityId === cityId; });
                    if (enemyCells.length > 0) {
                        playerMainCell_1 = GameHelper_1.gameHpr.novice.getMapCellByIndex(GameHelper_1.gameHpr.player.getMainCityIndex());
                        enemyCells.sort(function (a, b) { return GameHelper_1.gameHpr.getToMapCellDis(playerMainCell_1.index, a.index) - GameHelper_1.gameHpr.getToMapCellDis(playerMainCell_1.index, b.index); });
                        if (!GameHelper_1.gameHpr.guide.isForceWorking()) {
                            eventCenter.emit(EventType_1.default.MAP_MOVE_TO, enemyCells[0].point, true);
                        }
                    }
                    return [2 /*return*/, true];
            }
        });
    });
};
var gotoWearEquip = function (pawnType) {
    var _a, _b;
    return __awaiter(this, void 0, void 0, function () {
        var stateSwitchMap, armys, i, army, j, pawn, pawnInfo, i, army, j, pawn, pawnInfo, cell;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    stateSwitchMap = {};
                    stateSwitchMap[Enums_1.ArmyState.MARCH] = 9;
                    stateSwitchMap[Enums_1.ArmyState.NONE] = 0;
                    return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                case 1:
                    armys = _c.sent();
                    armys.sort(function (army1, army2) {
                        var state1 = stateSwitchMap[army1.state] || 1;
                        var state2 = stateSwitchMap[army2.state] || 1;
                        return state1 - state2;
                    });
                    if (mc.currWindName === 'area') {
                        for (i = 0; i < armys.length; i++) {
                            army = armys[i];
                            if (army.index === ((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index)) {
                                for (j = 0; j < army.pawns.length; j++) {
                                    pawn = army.pawns[j];
                                    pawnInfo = assetsMgr.getJsonData('pawnBase', pawn.id);
                                    if (pawnInfo.type === pawnType || 0 === pawnType) {
                                        return [2 /*return*/, true];
                                    }
                                }
                            }
                        }
                    }
                    i = 0;
                    _c.label = 2;
                case 2:
                    if (!(i < armys.length)) return [3 /*break*/, 9];
                    army = armys[i];
                    j = 0;
                    _c.label = 3;
                case 3:
                    if (!(j < army.pawns.length)) return [3 /*break*/, 8];
                    pawn = army.pawns[j];
                    pawnInfo = assetsMgr.getJsonData('pawnBase', pawn.id);
                    if (!(pawnInfo.type === pawnType || 0 === pawnType)) return [3 /*break*/, 7];
                    cell = GameHelper_1.gameHpr.world.getMapCellByIndex(army.index);
                    if (!(cell && cell.index !== ((_b = GameHelper_1.gameHpr.world.getLookCell()) === null || _b === void 0 ? void 0 : _b.index))) return [3 /*break*/, 7];
                    if (!(mc.currWindName === 'area')) return [3 /*break*/, 5];
                    return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('novice')];
                case 4:
                    _c.sent();
                    _c.label = 5;
                case 5:
                    GameHelper_1.gameHpr.world.setLookCell(cell);
                    return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                case 6:
                    _c.sent();
                    return [2 /*return*/, true];
                case 7:
                    j++;
                    return [3 /*break*/, 3];
                case 8:
                    i++;
                    return [3 /*break*/, 2];
                case 9: return [2 /*return*/, true];
            }
        });
    });
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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