
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/MainWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1e364aKCJxAnqBfRB6XE0Ca', 'MainWindCtrl');
// app/script/view/main/MainWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var MapUionFindHelper_1 = require("../../common/helper/MapUionFindHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildObj_1 = require("../../model/area/BuildObj");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var CellInfoCmpt_1 = require("./CellInfoCmpt");
var MapAnimNodePool_1 = require("./MapAnimNodePool");
var MarchCmpt_1 = require("./MarchCmpt");
var SceneEffectCmpt_1 = require("./SceneEffectCmpt");
var ccclass = cc._decorator.ccclass;
var MainWindCtrl = /** @class */ (function (_super) {
    __extends(MainWindCtrl, _super);
    function MainWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.cellEffectNode_ = null; // path://root/map_n/cell_effect_n
        _this.selectCellNode_ = null; // path://root/select_cell_n
        _this.textNode_ = null; // path://root/text_n
        _this.ancientTextNode_ = null; // path://root/ancient_text_n
        _this.marchLineNode_ = null; // path://root/march/march_line_n
        _this.marchRoleNode_ = null; // path://root/march/march_role_n
        _this.cellEmojiNode_ = null; // path://root/cell_emoji_n
        _this.sceneEffectNode_ = null; // path://root/scene_effect_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.INIT_KEY = '_init_main_';
        _this.diNode = null; //装饰-地块下面
        _this.decorationNode = null; //装饰-地块上面
        _this.mountainNode = null; //山脉-遮罩上面
        _this.protectLineNode = null; //保护线
        _this.lineNode = null;
        _this.landNode = null;
        _this.cityNode = null; //城市层
        _this.maskNode = null; //遮罩层
        _this.btinfoNode = null; //修建信息层
        _this.outputNode = null; //产出层
        _this.iconNode = null; //小图标层
        _this.tondenNode = null; //屯田中图标层
        _this.battleNode = null; //战斗中图标层
        _this.mapFlagNode = null; //联盟标记
        _this.mapMarkNode = null; //个人标记
        _this.cellInfoCmpt = null;
        _this.touchCmpt = null;
        _this.sceneEffect = null;
        _this.cellEmojiItemMap = {};
        _this.seasonType = 0;
        _this.model = null;
        _this.user = null;
        _this.player = null;
        _this.centre = cc.v2(); //当前的中心位置
        _this.preCameraZoomRatio = 0;
        _this.preCameraPosition = cc.v2();
        _this.marchs = []; //当前所有行军
        _this.tempShowCellMap = {}; //当前屏幕显示的地块信息
        _this.reqSelectArmysing = false; //当前是否请求军队列表中
        _this.cellEmojiMap = {}; //当前的领地表情map
        _this.cityAnimNodePool = null; //城市节点管理
        _this.seawaveAnimNodePool = null; //海浪节点管理
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        _this._temp_vec2_6 = cc.v2();
        _this._temp_vec2_7 = cc.v2();
        return _this;
    }
    MainWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_CELL_INFO] = this.onUpdateCellInfo, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.ADD_MARCH] = this.onAddMarch, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.HIDE_WORLD_TEXT] = this.onHideWorldText, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CLOSE_SELECT_CELL] = this.onCloseSelectCell, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_BATTLE_DIST_INFO] = this.onUpdateBattleDistInfo, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO] = this.onUpdateAvoidWarDistInfo, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_BT_CITY] = this.onUpdateBtCity, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_TONDEN] = this.onUpdateTonden, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmyDistInfo, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.MAP_MOVE_TO] = this.onMapMoveTo, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_PLAYER_NICKNAME] = this.onUpdatePlayerNickname, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_PLAYER_HEAD_ICON] = this.onUpdatePlayerHeadIcon, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.UPDATE_ALLI_MAP_FLAG] = this.onUpdateAllMapFlag, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.UPDATE_MAP_MARK] = this.onUpdateAllMapFlag, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.UPDATE_MARCH_OPACITY] = this.onUpdateMarchOpacity, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.PLAY_NEW_CELL_EFFECT] = this.onPlayNewCellEffect, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.PLAY_CELL_TONDEN_EFFECT] = this.onPlayCellTondenEffect, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.PLAY_CELL_EMOJI] = this.onPlayCellEmoji, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.UPDATE_CITY_OUTPUT] = this.onUpdateCityOutput, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.CHANGE_SEASON_BEGIN] = this.onChangeSeasonBegin, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.UPDATE_CITY_SKIN] = this.onUpdateCitySkin, _0.enter = true, _0),
            (_1 = {}, _1[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _1.enter = true, _1),
        ];
    };
    MainWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var i, item;
            return __generator(this, function (_a) {
                this.setParam({ isClean: false });
                this.decorationNode = this.mapNode_.FindChild('decoration');
                this.diNode = this.mapNode_.FindChild('di');
                this.mountainNode = this.mapNode_.FindChild('mountain');
                this.protectLineNode = this.mapNode_.FindChild('protect_line');
                this.lineNode = this.mapNode_.FindChild('line');
                this.landNode = this.mapNode_.FindChild('land');
                this.cityNode = this.mapNode_.FindChild('city');
                this.maskNode = this.mapNode_.FindChild('mask');
                this.btinfoNode = this.mapNode_.FindChild('btinfo');
                this.outputNode = this.mapNode_.FindChild('output');
                this.iconNode = this.mapNode_.FindChild('icon');
                this.tondenNode = this.mapNode_.FindChild('tonden');
                this.battleNode = this.mapNode_.FindChild('battle');
                this.mapFlagNode = this.mapNode_.FindChild('map_flag');
                this.mapMarkNode = this.mapNode_.FindChild('map_mark');
                for (i = 2; i <= 3; i++) {
                    item = this.cellEmojiItemMap[i] = this.cellEmojiNode_.FindChild('item_' + i);
                    item.parent = null;
                }
                this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt_1.default);
                this.cityAnimNodePool = new MapAnimNodePool_1.default().init(this.cityNode, ResHelper_1.resHelper.getCityPrefab.bind(ResHelper_1.resHelper));
                this.model = this.getModel('world');
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.selectCellNode_.active = false;
                this.cellInfoCmpt.close();
                this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt_1.default);
                this.updateSeasonSeceneEffect();
                return [2 /*return*/];
            });
        });
    };
    MainWindCtrl.prototype.onReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.checkSeason()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    MainWindCtrl.prototype.onEnter = function (data) {
        this.model.initCameraInfo();
        this.cellEffectNode_.Data = true;
        this.cellEmojiNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.showMap(); //显示地图
        this.initMarch(); //初始化行军
        this.playNewCellEffect();
        this.playCellTondenEffect();
        this.playCellEmoji();
        this.touchCmpt.init(this.onClickMap.bind(this));
        GameHelper_1.gameHpr.playMainBgm();
    };
    MainWindCtrl.prototype.onLeave = function () {
        this.model.saveCameraInfo();
        this.touchCmpt.clean();
        this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
        this.cellInfoCmpt.close();
        this.reqSelectArmysing = false;
        this.cleanMarch();
        this.cellEffectNode_.removeAllChildren();
        this.cellEffectNode_.Data = false;
        this.cellEmojiNode_.removeAllChildren();
        this.cellEmojiNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        // resHelper.cleanNodeChildren(this.diNode) 这里暂时不清理 因为进入其他场景太慢了
        this.cellEmojiMap = {};
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        assetsMgr.releaseTempResByTag(this.key);
        CameraCtrl_1.cameraCtrl.setBgColor('#D1F1F3');
    };
    MainWindCtrl.prototype.onClean = function () {
        var _a;
        for (var k in this.cellEmojiItemMap) {
            this.cellEmojiItemMap[k].destroy();
        }
        this.cellEmojiItemMap = {};
        this.cellEmojiMap = {};
        this.cellInfoCmpt.clean();
        (_a = this.sceneEffect) === null || _a === void 0 ? void 0 : _a.clean();
        assetsMgr.releaseTempResByTag(this.INIT_KEY);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/cell_info/buttons/enter_be
    MainWindCtrl.prototype.onClickEnter = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            this.model.setLookCell(cell);
            ViewHelper_1.viewHelper.gotoWind('area');
            this.hideSelectCell(false);
        }
    };
    // path://root/cell_info/buttons/occupy_be
    MainWindCtrl.prototype.onClickOccupy = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell) {
            return;
        }
        else if (!this.model.checkCanOccupyCell(cell)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_ATTACK_ADJOIN_CELL);
        }
        else if (cell.checkAttackByProtect()) { //是否有保护
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CELL_PROTECT);
        }
        else if (cell.isAvoidWar()) { //是否金盾
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.AVOID_WAR_NOT_ATTACK);
        }
        this.occupyCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/tonden_be
    MainWindCtrl.prototype.onClickTonden = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (GameHelper_1.gameHpr.isBattleingByIndex(cell.index)) {
            return ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden');
        }
        else if (cell.isBTCitying()) {
            return ViewHelper_1.viewHelper.showAlert('toast.bting_not_tonden');
        }
        this.cellTonden(cell.actIndex);
    };
    // path://root/cell_info/buttons/cancel_tonden_be
    MainWindCtrl.prototype.onClickCancelTonden = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn() || !cell.isTondening()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.cancel_tonden_tip', {
            ok: function () { return _this.isActive && _this.cancelTonden(cell.actIndex); },
            cancel: function () { },
        });
    };
    // path://root/cell_info/buttons/move_be
    MainWindCtrl.prototype.onClickMove = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOneAlliance()) {
            return;
        }
        this.moveToCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/build_be
    MainWindCtrl.prototype.onClickBuild = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        else if (cell.isTondening()) {
            return ViewHelper_1.viewHelper.showAlert('toast.tondening_not_bt');
        }
        ViewHelper_1.viewHelper.showPnl('main/CityList', cell);
    };
    // path://root/cell_info/buttons/dismantle_be
    MainWindCtrl.prototype.onClickDismantle = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/DismantleCityTip', cell);
    };
    // path://root/cell_info/buttons/player_info_be
    MainWindCtrl.prototype.onClickPlayerInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        var info = this.model.getPlayerInfo(cell === null || cell === void 0 ? void 0 : cell.owner);
        if (info) {
            ViewHelper_1.viewHelper.showPnl('common/PlayerInfo', info, 'cellinfo');
        }
    };
    // path://root/cell_info/title/share_pos_be
    MainWindCtrl.prototype.onClickSharePos = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var point_1 = cell.actPoint.Join(',');
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_point_to_chat_tip', params: [point_1] }, function (type, childType, select) {
                if (_this.isValid) {
                    var target = GameHelper_1.gameHpr.chat.getTargetChat(type, childType, select);
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, text: "[" + point_1 + "]", target: target }).then(function () { return _this.isValid && _this.hideSelectCell(false); });
                }
            });
        }
    };
    // path://root/cell_info/buttons/flag_be
    MainWindCtrl.prototype.onClickFlag = function (event, data) {
        if (!GameHelper_1.gameHpr.alliance.isMeMilitary()) {
            return;
        }
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/AlliFlag', cell.actPoint, false);
        }
    };
    // path://root/cell_info/info/score/score_desc_be
    MainWindCtrl.prototype.onClickScoreDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/LandScoreDesc', cell.landLv);
        }
    };
    // path://root/cell_info/info/stamina/stamina_desc_be
    MainWindCtrl.prototype.onClickStaminaDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType);
        }
    };
    // path://root/cell_info/title/cell_emoji_be
    MainWindCtrl.prototype.onClickCellEmoji = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellSelectEmoji', cell.isOwn(), function (id) {
                if (id) {
                    GameHelper_1.gameHpr.ground.sendCellEmoji(id, cell.actIndex);
                }
                if (_this.isValid) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    // path://root/map_n/output/item/city_output_be
    MainWindCtrl.prototype.onClickCityOutput = function (event, data) {
        var _this = this;
        var _a;
        var cell = event.target.parent.Data;
        if (!cell || !cell.isOwn()) {
            return;
        }
        var rewards = ((_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cityOutputMap[cell.index]) || [];
        this.model.claimCityOutput(cell.index).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            // gameHpr.addGainMassage(rewards)
            rewards.forEach(function (m, i) { return AnimHelper_1.animHelper.playFlutterCellOutput(i * 0.4, m, _this.topLayerNode_, cell.actPosition, _this.key); });
        });
    };
    // path://root/cell_info/buttons/ancient_info_be
    MainWindCtrl.prototype.onClickAncientInfo = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            var ancient = this.model.getAncientInfo(cell.index);
            if (ancient) {
                var build = new BuildObj_1.default().init(cell.index, ut.UID(), cc.v2(0, 0), ancient.id, ancient.lv);
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
    };
    // path://root/cell_info/buttons/city_skin_be
    MainWindCtrl.prototype.onClickCitySkin = function (event, data) {
        var _this = this;
        var cell = this.cellInfoCmpt.getCell();
        if (cell && cell.cityId > 0) {
            ViewHelper_1.viewHelper.showPnl('main/SelectCitySkin', cell, function (ok) {
                if (_this.isValid && ok) {
                    _this.hideSelectCell(false);
                }
            });
        }
    };
    // path://root/cell_info/title/map_mark_be
    MainWindCtrl.prototype.onClickMapMark = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/MapMark', cell.actPoint, false);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    MainWindCtrl.prototype.onNetReconnect = function () {
        if (!GameHelper_1.gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close();
            this.model.initCameraInfo();
        }
        this.updateMap(this.centre); //刷新地图显示
        this.initMarch(); //初始化行军
    };
    // 更新地块信息
    MainWindCtrl.prototype.onUpdateCellInfo = function () {
        this.updateMap(this.centre);
    };
    // 添加行军
    MainWindCtrl.prototype.onAddMarch = function (data) {
        var _this = this;
        if (!data.isCanShowMarch()) {
            return this.onRemoveMarch(data);
        }
        var march = this.marchs.find(function (m) { return m.uid === data.uid; });
        if (march) {
            march.init(data, this.marchRoleNode_, this.key);
            this.checkMarchLineOffset(data);
        }
        else {
            this.marchLineNode_.AddItem(function (it) {
                march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
                _this.checkMarchLineOffset(data);
            });
        }
    };
    // 删除行军
    MainWindCtrl.prototype.onRemoveMarch = function (data) {
        var march = this.marchs.remove('uid', data.uid);
        if (march) {
            march.clean();
            this.checkMarchLineOffset(data);
        }
    };
    // 刷新所有行军
    MainWindCtrl.prototype.onUpdateAllMarch = function () {
        this.initMarch();
    };
    // 隐藏文本
    MainWindCtrl.prototype.onHideWorldText = function (val) {
        val = !val;
        this.textNode_.active = val;
        this.ancientTextNode_.active = val;
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
        this.tondenNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = val); });
    };
    // 关闭选择地块
    MainWindCtrl.prototype.onCloseSelectCell = function (play) {
        this.hideSelectCell(!!play);
    };
    // 刷新战斗状态
    MainWindCtrl.prototype.onUpdateBattleDistInfo = function () {
        var cells = [], distMap = this.model.getBattleDistMap();
        for (var index in distMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push(cell);
        }
        this.battleNode.Items(cells, function (it, data) { return it.setPosition(data.actPosition); });
    };
    // 刷新免战状态
    MainWindCtrl.prototype.onUpdateAvoidWarDistInfo = function () {
        this.updateIconNode();
    };
    // 刷新修建信息
    MainWindCtrl.prototype.onUpdateBtCity = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新屯田信息
    MainWindCtrl.prototype.onUpdateTonden = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新地图上面的军队分布情况  这里主动绘制一次
    MainWindCtrl.prototype.onUpdateArmyDistInfo = function () {
        this.updateIconNode();
        this.cellInfoCmpt.updateArmyInfo();
    };
    // 移动地图
    MainWindCtrl.prototype.onMapMoveTo = function (point, showCellInfo) {
        var _this = this;
        if (this.centre.equals(point)) {
            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point.clone().floor()));
        }
        else if (!this.tempShowCellMap[MapHelper_1.mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点
            var start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point);
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(start), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, CameraCtrl_1.cameraCtrl.zoomRatio);
            this.updateMap(start.floor());
            this.checkInCameraMarchLine();
        }
        // 移动
        CameraCtrl_1.cameraCtrl.moveTo(0.25, MapHelper_1.mapHelper.getPixelByPoint(point).subSelf(CameraCtrl_1.cameraCtrl.getWinSizeHalf())).then(function () {
            if (_this.isActive && showCellInfo) {
                _this.showSelectCell(_this.model.getMapCellByPoint(point.clone().floor()));
            }
        });
    };
    // 刷新玩家昵称
    MainWindCtrl.prototype.onUpdatePlayerNickname = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            this.updatePlayerNickname(it, data);
        }
    };
    // 刷新玩家头像
    MainWindCtrl.prototype.onUpdatePlayerHeadIcon = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY);
        }
    };
    // 刷新个人标记
    MainWindCtrl.prototype.onUpdateMapMark = function () {
        var _this = this;
        var cells = [], mapFalg = this.player.getMapMarks();
        for (var index in mapFalg) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ cell: cell, flag: mapFalg[index].flag });
        }
        var otherFlag = GameHelper_1.gameHpr.alliance.getMapFlag();
        this.mapMarkNode.Items(cells, function (it, data) {
            var actPosition = data.cell.actPosition;
            var pos = otherFlag[data.cell.actIndex] ? _this._temp_vec2_6.set2(actPosition.x - 16, actPosition.y) : actPosition;
            it.setPosition(pos);
            it.Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(data.flag);
        });
    };
    // 刷新联盟地图标记
    MainWindCtrl.prototype.onUpdateAlliMapFlag = function () {
        var _this = this;
        var cells = [], mapFalg = GameHelper_1.gameHpr.alliance.getMapFlag();
        for (var index in mapFalg) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ cell: cell, flag: mapFalg[index].flag });
        }
        var otherFlag = this.player.getMapMarks();
        this.mapFlagNode.Items(cells, function (it, data) {
            var actPosition = data.cell.actPosition;
            var pos = otherFlag[data.cell.actIndex] ? _this._temp_vec2_7.set2(actPosition.x + 16, actPosition.y) : actPosition;
            it.setPosition(pos);
            it.Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(data.flag);
        });
    };
    MainWindCtrl.prototype.onUpdateAllMapFlag = function () {
        this.onUpdateMapMark();
        this.onUpdateAlliMapFlag();
        this.cellInfoCmpt.updateFlagDesc();
    };
    // 刷新行军线透明度
    MainWindCtrl.prototype.onUpdateMarchOpacity = function () {
        this.marchs.forEach(function (m) { return m.updateOpacity(); });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.onPlayNewCellEffect = function () {
        this.playNewCellEffect();
    };
    // 播放屯田结束效果
    MainWindCtrl.prototype.onPlayCellTondenEffect = function () {
        this.playCellTondenEffect();
    };
    // 播放地图表情
    MainWindCtrl.prototype.onPlayCellEmoji = function () {
        this.playCellEmoji();
    };
    // 刷新城市产出
    MainWindCtrl.prototype.onUpdateCityOutput = function () {
        var cells = [];
        for (var index in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[index];
            var output = cell.getOutputType();
            if (output) {
                cells.push({ cell: cell, output: output });
            }
        }
        this.outputNode.Items(cells, function (it, data) {
            it.setPosition(data.cell.actPosition);
            it.Child('city_output_be').active = data.cell.isOwn();
            it.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[data.output.type]);
            it.Child('root/val', cc.Label).string = data.output.count > 1 ? data.output.count + '' : '';
        });
    };
    // 改变季节开始
    MainWindCtrl.prototype.onChangeSeasonBegin = function () {
        this.playChangeSeason(this.model.getSeason().type);
    };
    // 刷新遗迹
    MainWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var index = data.index;
        var it = this.ancientTextNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.index) === index; });
        if (it) {
            this.updateAncientTextInfo(it, data);
        }
    };
    // 刷新城市皮肤
    MainWindCtrl.prototype.onUpdateCitySkin = function (index) {
        this.updateMap(this.centre);
    };
    // 若引导
    MainWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'main') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    Object.defineProperty(MainWindCtrl.prototype, "isActive", {
        // ----------------------------------------- custom function ----------------------------------------------------
        get: function () { return this.isValid && this.isEnter(); },
        enumerable: false,
        configurable: true
    });
    // 点击地图
    MainWindCtrl.prototype.onClickMap = function (worldLocation) {
        var cell = this.model.getMapCellByPoint(MapHelper_1.mapHelper.getPointByPixel(worldLocation));
        if (cell && !this.selectCellNode_.Data) {
            audioMgr.playSFX('click');
            this.showSelectCell(cell);
            CameraCtrl_1.cameraCtrl.redressPositionByRange(cell.actPosition, Constant_1.SELECT_CELL_INFO_BOX);
        }
        else {
            this.hideSelectCell();
        }
    };
    // 刷新场景特效
    MainWindCtrl.prototype.updateSeasonSeceneEffect = function () {
        this.sceneEffect.clean();
        var sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl();
        if (sceneEffectUrl) { //加载场景特效
            this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY);
        }
        var seasonType = this.model.getSeasonType();
        // 更新遮罩颜色
        this.maskNode.children.forEach(function (m) { return m.Color(Constant_1.MAP_MASK_ITEM_COLOR[seasonType]); });
    };
    // 绘制地图
    MainWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var _a, _b, _c, _d;
        this.preCameraZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.centre.set(centre);
        this.model.setCentre(centre);
        // 绘制地面
        var armyDistMap = this.player.getArmyDistMap(), battleDist = this.model.getBattleDistMap();
        var mapFlag = GameHelper_1.gameHpr.alliance.getMapFlag(), mapMark = this.player.getMapMarks();
        var btCityMap = this.model.getBTCityQueueMap();
        var tondenMap = this.model.getTondenQueueMap();
        var di = 0, linei = 0, li = 0, mi = 0, ii = 0, ti = 0, bi = 0, mfi = 0, mmi = 0, oi = 0, mti = 0, pli = 0, dti = 0;
        var texts = [], tondens = [], btCitys = [], ancientTexts = [];
        this.cityAnimNodePool.reset();
        this.tempShowCellMap = {};
        var drawProtectOwnerMap = {};
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange());
        var seasonType = this.seasonType;
        var tempDecorationLoadMap = {};
        for (var i = 0; i < points.length; i++) {
            var point = points[i], cell = this.model.getMapCellByPoint(point);
            var position = (cell === null || cell === void 0 ? void 0 : cell.position) || MapHelper_1.mapHelper.getPixelByPoint(point);
            if (cell) {
                var btInfo = btCityMap[cell.index], tondenInfo = tondenMap[cell.index], actPosition = cell.actPosition;
                var protectOwner = cell.getProtectOwner();
                this.tempShowCellMap[cell.index] = cell;
                if (cell.cityId > 0) {
                    var animName = cell.cityId === Constant_1.CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined;
                    var city = this.cityAnimNodePool.showNode(cell.getCityViewId(), actPosition, true, animName);
                    if (cell.cityId === Constant_1.CITY_MAIN_NID) {
                        // 这里先获取后面用来显示文本
                        texts.push(cell);
                    }
                    else if (cell.isAncient()) {
                        var info = this.model.getAncientInfo(cell.index);
                        if (info) {
                            ancientTexts.push(info); //遗迹
                            city.Child('val', cc.MultiFrame).setFrame(info.lv === 20);
                        }
                    }
                }
                else if (cell.cityId < 0) {
                }
                else if (tondenInfo) { //绘制屯田地
                    tondens.push({ tondenInfo: tondenInfo, cell: cell });
                    ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon('land_tonden', seasonType);
                }
                else if (cell.icon && (!btInfo || btInfo.id === 0)) {
                    if (cell.landType === Enums_1.LandType.SEA || cell.landType === Enums_1.LandType.BEACH || MapUionFindHelper_1.mapUionFindHelper.getIsUnLock(cell.index)) {
                        var itemNdoe = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, cell.position);
                        itemNdoe.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType());
                        itemNdoe.zIndex = 0;
                    }
                    else {
                        ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, cell.position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType());
                    }
                }
                // 绘制修建信息
                if (btInfo) {
                    btCitys.push({ btInfo: btInfo, cell: cell });
                    // 只绘制修建 不绘制拆除
                    if (cell.cityId === 0 && btInfo.id > 0) {
                        this.cityAnimNodePool.showNode(btInfo.id, actPosition, false);
                    }
                }
                // 绘制地图军队分布图标
                if (!!armyDistMap[cell.index]) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_a = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _a === void 0 ? void 0 : _a.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position); //显示到左下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('army_min_icon');
                }
                // 绘制免战图标
                if (cell.isCanShowAvoidWar()) {
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_b = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _b === void 0 ? void 0 : _b.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)); //显示到右下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('avoidwar_icon_1');
                }
                // 绘制战斗图标
                if (!!battleDist[cell.index]) {
                    ResHelper_1.resHelper.getNodeByIndex(this.battleNode, bi++, actPosition);
                }
                // 标记
                var flag = mapFlag[cell.index], mark = mapMark[cell.index];
                if (mark) {
                    var pos = flag ? this._temp_vec2_6.set2(actPosition.x - 16, actPosition.y) : actPosition;
                    ResHelper_1.resHelper.getNodeByIndex(this.mapMarkNode, mmi++, pos).Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(mark.flag);
                }
                if (flag) {
                    var pos = mark ? this._temp_vec2_7.set2(actPosition.x + 16, actPosition.y) : actPosition;
                    ResHelper_1.resHelper.getNodeByIndex(this.mapFlagNode, mfi++, pos).Child('root/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(flag.flag);
                }
                // 绘制产出
                var output = cell.getOutputType();
                if (output) {
                    var oNode = ResHelper_1.resHelper.getNodeByIndex(this.outputNode, oi++, actPosition);
                    oNode.Data = cell;
                    oNode.Child('city_output_be').active = cell.isOwn();
                    oNode.Child('root/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[output.type]);
                    oNode.Child('root/val', cc.Label).string = output.count > 1 ? output.count + '' : '';
                }
                // 绘制保护模式下的边框线
                if (protectOwner && !drawProtectOwnerMap[protectOwner]) {
                    var mainCell = this.model.getMapCellByIndex(GameHelper_1.gameHpr.getPlayerMainCityIndex(protectOwner));
                    if (mainCell) {
                        drawProtectOwnerMap[protectOwner] = true;
                        // 是否有保护模式 绘制保护线
                        var state = GameHelper_1.gameHpr.checkPlayerProtectModeState(protectOwner);
                        if (state > 0) {
                            ResHelper_1.resHelper.getNodeByIndex(this.protectLineNode, pli++, mainCell.actPosition).opacity = state === 1 ? 255 : 100;
                        }
                    }
                }
                // 记录边框线
                var borderLines = cell.owner ? cell.borderLines : [];
                if (borderLines.length > 0) {
                    var lineItemNode = ResHelper_1.resHelper.getNodeByIndex(this.lineNode, linei++, position);
                    ViewHelper_1.viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor());
                }
                // 绘制遮罩
                if (!cell.owner && cell.landType !== Enums_1.LandType.SEA) {
                    var maskItemNode = ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                    maskItemNode.opacity = protectOwner ? Constant_1.MAP_MASK_ITEM_OPACITY_GOLD[seasonType] : Constant_1.MAP_MASK_ITEM_OPACITY[seasonType];
                }
                //绘制地图装饰
                var decorationList = this.model.getDecorationByIndex(cell.index);
                if (decorationList) {
                    for (var i_1 = 0; i_1 < decorationList.length; i_1++) {
                        var _e = decorationList[i_1], decorationJson = _e.decorationJson, decorationActIndex = _e.decorationActIndex;
                        var tempKey = decorationActIndex + '_' + decorationJson.id;
                        if (!tempDecorationLoadMap[tempKey]) {
                            tempDecorationLoadMap[tempKey] = true;
                            var iconName = this.model.getDecorationIcon(decorationJson, decorationActIndex);
                            var frame = ResHelper_1.resHelper.getLandItemIcon(iconName, this.model.getSeasonType());
                            var actPosition_1 = this.model.getMapCellByIndex(decorationActIndex).actPosition;
                            var itemNode = null;
                            if (decorationJson.type == Enums_1.DecorationType.Shadow || decorationJson.type == Enums_1.DecorationType.MUD || decorationJson.type == Enums_1.DecorationType.MUD_OUTER) {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, actPosition_1);
                            }
                            else if (MapUionFindHelper_1.mapUionFindHelper.getIsUnLock(decorationActIndex)) {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, actPosition_1);
                            }
                            else {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.decorationNode, dti++, actPosition_1);
                            }
                            var zIndex = 1;
                            switch (decorationJson.type) {
                                case Enums_1.DecorationType.MOUNTAIN:
                                case Enums_1.DecorationType.MAIN_CITY_ROUND_MOUNTAIN:
                                case Enums_1.DecorationType.MAIN_CITY_ROUND_LAKE:
                                    zIndex = cc.macro.MAX_ZINDEX;
                                    break;
                                case Enums_1.DecorationType.Shadow:
                                    zIndex = 0;
                                    break;
                            }
                            itemNode.zIndex = zIndex;
                            itemNode.Component(cc.Sprite).spriteFrame = frame;
                            this.resetDecorationPoint(itemNode, iconName);
                        }
                    }
                }
            }
            else {
                var landId = this.model.getRoundId(point.x, point.y);
                if (landId) {
                    var itemInfo = assetsMgr.getJsonData('land', landId);
                    var itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, position);
                    itemNode.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(itemInfo.icon, this.model.getSeasonType());
                    itemNode.zIndex = 0;
                    if (itemInfo.type == Enums_1.LandType.BEACH) {
                        ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                    }
                }
            }
        }
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.decorationNode, dti);
        ResHelper_1.resHelper.hideNodeByIndex(this.diNode, di);
        ResHelper_1.resHelper.hideNodeByIndex(this.protectLineNode, pli);
        ResHelper_1.resHelper.hideNodeByIndex(this.lineNode, linei);
        ResHelper_1.resHelper.hideNodeByIndex(this.landNode, li);
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
        ResHelper_1.resHelper.hideNodeByIndex(this.iconNode, ii);
        ResHelper_1.resHelper.hideNodeByIndex(this.battleNode, bi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mapFlagNode, mfi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mapMarkNode, mmi);
        ResHelper_1.resHelper.hideNodeByIndex(this.outputNode, oi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mountainNode, mti);
        // this.seawaveAnimNodePool?.hideOtherNode()
        this.cityAnimNodePool.hideOtherNode();
        // 当前正在显示的
        var showIndex = (_d = (_c = this.cellInfoCmpt.getCell()) === null || _c === void 0 ? void 0 : _c.actIndex) !== null && _d !== void 0 ? _d : -1;
        var zIndexMaxY = MapHelper_1.mapHelper.MAP_SIZE.y;
        var isCanShowText = !this.touchCmpt.isDraging();
        // 绘制文本层
        this.textNode_.Items(texts, function (it, data) {
            var pos = data.actPosition, index = data.actIndex;
            var d = it.Data;
            var info = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            it.setPosition(pos.x, pos.y + 76);
            if (!d || d.owner !== data.owner || d.nickname !== (info === null || info === void 0 ? void 0 : info.nickname) || d.title !== (info === null || info === void 0 ? void 0 : info.title) || d.headIcon !== (info === null || info === void 0 ? void 0 : info.headIcon)) {
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info === null || info === void 0 ? void 0 : info.headIcon, _this.INIT_KEY);
                _this.updatePlayerNickname(it, info);
            }
            it.active = showIndex !== index;
            it.Data = { index: index, owner: data.owner, nickname: info === null || info === void 0 ? void 0 : info.nickname, title: info === null || info === void 0 ? void 0 : info.title, headIcon: info === null || info === void 0 ? void 0 : info.headIcon };
        });
        this.textNode_.active = isCanShowText;
        // 绘制遗迹文本层
        this.ancientTextNode_.Items(ancientTexts, function (it, data) {
            var pos = data.cell.actPosition, index = data.index;
            it.setPosition(pos.x, pos.y + 80);
            _this.updateAncientTextInfo(it, data);
            it.active = showIndex !== index;
            it.Data = { index: index };
        });
        this.ancientTextNode_.active = isCanShowText;
        // 绘制修建城市的信息
        this.btinfoNode.Items(btCitys, function (it, data) {
            var _a;
            var info = data.btInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                var surplusTime = info.getSurplusTime();
                timeNode.Color(info.id ? '#21DC2D' : '#FF9162');
                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001);
                // 动画
                var anim_1 = it.Child('anim', cc.Animation);
                var elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001;
                var tween = cc.tween(it);
                tween.stop();
                if (elapsedTime < 0.62) {
                    anim_1.play('cting_begin', elapsedTime);
                    tween.delay(0.62 - elapsedTime).call(function () { return _this.isValid && anim_1.play('cting_loop'); }).start();
                }
                else {
                    anim_1.play('cting_loop');
                }
            }
            timeNode.active = isCanShowText && showIndex !== index;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
        // 绘制屯田信息
        this.tondenNode.Items(tondens, function (it, data) {
            var _a;
            var info = data.tondenInfo, index = info.index;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== index) {
                timeNode.Component(cc.LabelTimer).run(info.getSurplusTime() * 0.001);
            }
            timeNode.active = isCanShowText;
            it.Data = { index: index };
            it.zIndex = zIndexMaxY - data.cell.actPoint.y;
        });
    };
    //设置装饰偏移
    MainWindCtrl.prototype.resetDecorationPoint = function (itemNode, icon) {
        var frame = ResHelper_1.resHelper.getLandItemIcon(icon, this.model.getSeasonType());
        if (!frame) {
            cc.error('resetDecorationPoint icon: ' + icon + ', season: ' + this.model.getSeasonType());
        }
        var size = frame.getOriginalSize();
        if (size.width / Constant_1.TILE_SIZE % 2 == 0) {
            itemNode.x += Constant_1.TILE_SIZE / 2;
        }
        if (size.height / Constant_1.TILE_SIZE % 2 == 0) {
            itemNode.y += Constant_1.TILE_SIZE / 2;
        }
    };
    // 0.上 1.右 2.下 3.左 4.左上 5.右上 6.右下 7.左下
    MainWindCtrl.prototype.getSeaLandIcon = function (point, minx, miny, maxx, maxy) {
        if (point.x < minx) {
            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4);
        }
        else if (point.x < maxx) {
            return point.y < miny ? 2 : 0;
        }
        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5);
    };
    MainWindCtrl.prototype.setSeaLand = function (it, type, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy);
        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(type + "_" + Math.min(dir, 4));
    };
    // 海浪
    MainWindCtrl.prototype.setSeawaveLand = function (position, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4;
        var it = this.seawaveAnimNodePool.showNode(no, position, true);
        it.angle = angle * -90;
    };
    MainWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandIcon(icon);
    };
    MainWindCtrl.prototype.updatePlayerNickname = function (it, data) {
        var _a;
        var nameLbl = it.Child('name/val', cc.Label);
        nameLbl.string = ut.nameFormator((_a = data === null || data === void 0 ? void 0 : data.nickname) !== null && _a !== void 0 ? _a : '???', 7);
        var titleLbl = it.Child('name/title', cc.Label);
        if (titleLbl.setActive(!!(data === null || data === void 0 ? void 0 : data.title))) {
            var json = assetsMgr.getJsonData('title', data.title);
            titleLbl.Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey('titleText.' + (json === null || json === void 0 ? void 0 : json.id));
            nameLbl.node.y = -10;
        }
        else {
            nameLbl.node.y = 0;
        }
    };
    MainWindCtrl.prototype.updateAncientTextInfo = function (it, data) {
        it.Child('name').setLocaleKey('ui.ancient_name_text', data.name, assetsMgr.lang('ui.short_lv', data.lv || 1));
        if (it.Child('time').active = data.state === 1 && !data.pauseState) {
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
        }
    };
    // 刷新显示文本节点
    MainWindCtrl.prototype.updateHideTextByIndex = function (index) {
        if (index === void 0) { index = -1; }
        this.textNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.ancientTextNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = m.Data.index !== index); });
    };
    // 刷新图标层
    MainWindCtrl.prototype.updateIconNode = function () {
        var _this = this;
        var offset1 = cc.v2(-22, -22);
        var offset2 = cc.v2(22, -22);
        var cells = [], armyDistMap = this.player.getArmyDistMap();
        for (var key in this.tempShowCellMap) {
            var cell = this.tempShowCellMap[key];
            if (cell.isCanShowAvoidWar()) {
                cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_icon_1' });
            }
            if (armyDistMap[key]) {
                cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' });
            }
        }
        this.iconNode.Items(cells, function (it, data) {
            it.setPosition(_this._temp_vec2_3.set(data.offset).addSelf(data.position));
            it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon(data.icon);
        });
    };
    // 显示选择地块
    MainWindCtrl.prototype.showSelectCell = function (cell) {
        if (!cell || cell.landType == Enums_1.LandType.SEA || cell.landType == Enums_1.LandType.BEACH) {
            return;
        }
        else if (cell.actIndex !== cell.index) {
            cell = this.model.getMapCellByIndex(cell.actIndex);
        }
        var pos = this.selectCellNode_.Data = cell.actPosition;
        this.selectCellNode_.Component(SelectCellCmpt_1.default).open(pos, cell.getSize());
        this.cellInfoCmpt.open(pos, cell);
        // 隐藏文本节点
        this.updateHideTextByIndex(cell.actIndex);
    };
    // 隐藏
    MainWindCtrl.prototype.hideSelectCell = function (play) {
        if (play === void 0) { play = true; }
        if (this.selectCellNode_.Data) {
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close(play);
            this.updateHideTextByIndex();
        }
    };
    // 初始化行军
    MainWindCtrl.prototype.initMarch = function () {
        var _this = this;
        this.cleanMarch();
        var list = this.model.getAllMarchs().filter(function (m) { return m.isCanShowMarch(); });
        this.marchLineNode_.Items(list, function (it, data) {
            var march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
            march.isCheckLineOffset = false;
        });
        this.marchs.forEach(function (m) { return !m.isCheckLineOffset && _this.checkMarchLineOffset(m.getData()); });
    };
    MainWindCtrl.prototype.cleanMarch = function () {
        while (this.marchs.length > 0) {
            this.marchs.pop().clean();
        }
        this.marchs = [];
        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况
        this.marchRoleNode_.removeAllChildren();
    };
    // 检测行军线偏移
    MainWindCtrl.prototype.checkMarchLineOffset = function (data) {
        var others = [];
        this.marchs.forEach(function (m) {
            var d = m.getData();
            if (data.checkOtherMarchLine(d)) {
                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180;
                m.isCheckLineOffset = true;
                others.push(m);
            }
        });
        var len = others.length;
        others.forEach(function (m, i) { return m.updateLineOffset(i, len); });
    };
    // 攻击地块
    MainWindCtrl.prototype.occupyCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 2, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_TIME) {
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_time_tip')];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, function (armys, isSameSpeed, autoBackType) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.occupyCell(armys, index, autoBackType, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动过去屯田
    MainWindCtrl.prototype.cellTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 3, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.BATTLEING) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.battleing_not_tonden')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectTondenArmy', index, list, canGotoCount, function (army) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.cellTonden(army, index).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 取消屯田
    MainWindCtrl.prototype.cancelTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.model.cancelTonden(index)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isActive) {
                            this.hideSelectCell(false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动到地块
    MainWindCtrl.prototype.moveToCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 1)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, function (armys, isSameSpeed) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.moveCellArmy(armys, index, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放新的地块效果
    MainWindCtrl.prototype.playNewCellEffect = function () {
        var _this = this;
        this.model.getNotPlayNewCells().forEach(function (index) {
            var cell = _this.tempShowCellMap[index];
            if (cell) {
                var json_1 = cell.getResJson() || {}, keys = Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; });
                var pos_1 = cell.actPosition, isMore_1 = keys.length > 1;
                keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, json_1[key], 0.3 + i * 0.2, isMore_1, _this.topLayerNode_, pos_1, _this.key); });
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos_1, _this.key);
                // 隐藏行军线
                _this.marchs.forEach(function (march) { return march.isHasIndex(index) && march.hide(1.2); });
            }
        });
    };
    // 播放屯田结束的地块效果
    MainWindCtrl.prototype.playCellTondenEffect = function () {
        var _this = this;
        this.model.getNotPlayCellTondens().forEach(function (data) {
            var _a;
            var cell = _this.tempShowCellMap[data.index];
            if (cell) {
                var pos = cell.actPosition;
                var obj_1 = {};
                (_a = data.treasureIds) === null || _a === void 0 ? void 0 : _a.forEach(function (id) {
                    var idObj = obj_1[id];
                    if (!idObj) {
                        var json = assetsMgr.getJsonData('treasure', id);
                        idObj = obj_1[id] = { count: 0, icon: 'treasure_' + ((json === null || json === void 0 ? void 0 : json.lv) || 1) + '_0' };
                    }
                    idObj.count += 1;
                });
                for (var key in obj_1) {
                    var data_1 = obj_1[key];
                    AnimHelper_1.animHelper.playFlutterTreasure(data_1.icon, data_1.count, _this.topLayerNode_, pos, _this.key);
                }
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos, _this.key);
            }
        });
    };
    // 播放地图表情
    MainWindCtrl.prototype.playCellEmoji = function () {
        var _this = this;
        var now = Date.now();
        GameHelper_1.gameHpr.ground.getCellEmojis().forEach(function (m) {
            var cell = _this.tempShowCellMap[m.index], item = _this.cellEmojiItemMap[Math.floor(m.emoji / 1000)];
            if (cell && item) {
                var node = _this.cellEmojiMap[m.index];
                if (node === null || node === void 0 ? void 0 : node.isValid) {
                    node.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                    node.destroy();
                }
                var startTime = Math.max(0, (now - m.getTime) * 0.001);
                node = _this.cellEmojiMap[m.index] = cc.instantiate2(item, _this.cellEmojiNode_);
                node.Data = m.index;
                node.setPosition(cell.actPosition);
                node.zIndex = cell.point.y;
                AnimHelper_1.animHelper.playCellEmoji(node, m.emoji, m.uid, startTime, _this.key).then(function (n) {
                    if (_this.isValid && (n === null || n === void 0 ? void 0 : n.isValid)) {
                        delete _this.cellEmojiMap[n.Data];
                        n.Child('root').children.forEach(function (it) { return nodePoolMgr.put(it); });
                        n.destroy();
                    }
                });
            }
        });
    };
    MainWindCtrl.prototype.testPlayNewCell = function (x, y, keys, delay) {
        var _this = this;
        // for (let i = 0; i < 5; i++) {
        //     const position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()
        //     animHelper.playFlutterCellRes('stone', 30, this.topLayerNode_, position, this.key)
        //     animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)
        // }
        var pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(x, y)).clone(), isMore = keys.length > 1;
        keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, 1, 0.3 + i * delay, isMore, _this.topLayerNode_, pos, _this.key); });
        AnimHelper_1.animHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key);
    };
    // 检测季节
    MainWindCtrl.prototype.checkSeason = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var oldType;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        oldType = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE)) !== null && _a !== void 0 ? _a : -1;
                        this.seasonType = cc.misc.clampf(oldType, 0, 3);
                        if (!(oldType !== this.model.getSeasonType())) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('main/SeasonSwitch')];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 显示地图
    MainWindCtrl.prototype.showMap = function () {
        var _this = this;
        var _a;
        // 设置相机颜色
        CameraCtrl_1.cameraCtrl.setBgColor(Constant_1.CAMERA_BG_COLOR[this.seasonType]);
        // 刷新地图显示
        if (ResHelper_1.resHelper.checkLandSkin(this.seasonType)) {
            this.updateMap(this.model.getCentre());
        }
        else {
            ResHelper_1.resHelper.initLandSkin(this.seasonType).then(function () { return _this.isValid && _this.updateMap(_this.model.getCentre()); });
        }
        // 显示季节切换全屏ui
        var oldType = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE)) !== null && _a !== void 0 ? _a : -1;
        if (oldType !== this.model.getSeasonType()) {
            ViewHelper_1.viewHelper.showPnl('main/SeasonSwitch', true);
        }
    };
    // 播放切换季节
    MainWindCtrl.prototype.playChangeSeason = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.seasonType = type;
                        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LAST_PLAY_SEASON_TYPE, type);
                        this.hideSelectCell(false);
                        return [4 /*yield*/, ResHelper_1.resHelper.initLandSkin(type)];
                    case 1:
                        _a.sent();
                        this.updateSeasonSeceneEffect();
                        // 设置相机颜色
                        CameraCtrl_1.cameraCtrl.setBgColor(Constant_1.CAMERA_BG_COLOR[this.seasonType]);
                        // 刷新地图显示
                        this.updateMap(this.centre);
                        ResHelper_1.resHelper.cleanLandSkin();
                        this.emit(EventType_1.default.CHANGE_SEASON_COMPLETE);
                        return [2 /*return*/];
                }
            });
        });
    };
    MainWindCtrl.prototype.update = function (dt) {
        var _a;
        //
        (_a = this.seawaveAnimNodePool) === null || _a === void 0 ? void 0 : _a.update(dt);
        // 检测是否需要填充地图
        this.checkUpdateMap();
        // 检测是否在相机范围
        this.checkInCameraRange();
    };
    MainWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_1);
        var size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y));
        if (size >= Constant_1.MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
            this.checkInCameraMarchLine();
        }
    };
    // 检测只会在在相机范围内的行军线
    MainWindCtrl.prototype.checkInCameraMarchLine = function () {
        var _a;
        var uidMap = {};
        this.marchs.forEach(function (m) {
            m.checkUpdateInCamera();
            uidMap[m.uid] = true;
        });
        // 兼容检测是否有多余的行军角色
        for (var i = this.marchRoleNode_.childrenCount - 1; i >= 0; i--) {
            var node = this.marchRoleNode_.children[i];
            if (!uidMap[(_a = node.Data) === null || _a === void 0 ? void 0 : _a.uid]) {
                node.destroy();
            }
        }
    };
    MainWindCtrl.prototype.checkInCameraRange = function () {
        var _a;
        var position = CameraCtrl_1.cameraCtrl.getPosition();
        if (this.preCameraPosition.equals(position)) {
            return;
        }
        this.preCameraPosition.set(position);
        // 选择地块框
        if ((_a = this.cellInfoCmpt) === null || _a === void 0 ? void 0 : _a.checkNotInScreenRange()) {
            this.hideSelectCell(false);
        }
    };
    MainWindCtrl = __decorate([
        ccclass
    ], MainWindCtrl);
    return MainWindCtrl;
}(mc.BaseWindCtrl));
exports.default = MainWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXE1haW5XaW5kQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw2REFBNEQ7QUFDNUQsMkRBQXlSO0FBRXpSLHFEQUFvRDtBQUNwRCxxREFBc0Y7QUFDdEYsMERBQXFEO0FBQ3JELHdEQUFtRDtBQUNuRCw2REFBNEQ7QUFDNUQsNkRBQXlEO0FBQ3pELCtEQUE4RDtBQUM5RCwyREFBMEQ7QUFDMUQsMkVBQTBFO0FBQzFFLDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFDNUQsc0RBQWlEO0FBVWpELHFEQUFnRDtBQUNoRCx5REFBb0Q7QUFDcEQsK0NBQTBDO0FBQzFDLHFEQUFnRDtBQUNoRCx5Q0FBb0M7QUFDcEMscURBQWdEO0FBRXhDLElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQTBDLGdDQUFlO0lBQXpEO1FBQUEscUVBK3ZDQztRQTd2Q0csMEJBQTBCO1FBQ2xCLGNBQVEsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7UUFDN0MscUJBQWUsR0FBWSxJQUFJLENBQUEsQ0FBQyxrQ0FBa0M7UUFDbEUscUJBQWUsR0FBWSxJQUFJLENBQUEsQ0FBQyw0QkFBNEI7UUFDNUQsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLHFCQUFxQjtRQUMvQyxzQkFBZ0IsR0FBWSxJQUFJLENBQUEsQ0FBQyw2QkFBNkI7UUFDOUQsb0JBQWMsR0FBWSxJQUFJLENBQUEsQ0FBQyxpQ0FBaUM7UUFDaEUsb0JBQWMsR0FBWSxJQUFJLENBQUEsQ0FBQyxpQ0FBaUM7UUFDaEUsb0JBQWMsR0FBWSxJQUFJLENBQUEsQ0FBQywyQkFBMkI7UUFDMUQsc0JBQWdCLEdBQVksSUFBSSxDQUFBLENBQUMsNkJBQTZCO1FBQzlELG1CQUFhLEdBQVksSUFBSSxDQUFBLENBQUMsMEJBQTBCO1FBQ3hELG9CQUFjLEdBQVksSUFBSSxDQUFBLENBQUMsMkJBQTJCO1FBQ2xFLE1BQU07UUFFVyxjQUFRLEdBQUcsYUFBYSxDQUFBO1FBRWpDLFlBQU0sR0FBWSxJQUFJLENBQUEsQ0FBQSxTQUFTO1FBQy9CLG9CQUFjLEdBQVksSUFBSSxDQUFBLENBQUEsU0FBUztRQUN2QyxrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFBLFNBQVM7UUFDckMscUJBQWUsR0FBWSxJQUFJLENBQUEsQ0FBQyxLQUFLO1FBQ3JDLGNBQVEsR0FBWSxJQUFJLENBQUE7UUFDeEIsY0FBUSxHQUFZLElBQUksQ0FBQTtRQUN4QixjQUFRLEdBQVksSUFBSSxDQUFBLENBQUMsS0FBSztRQUM5QixjQUFRLEdBQVksSUFBSSxDQUFBLENBQUMsS0FBSztRQUM5QixnQkFBVSxHQUFZLElBQUksQ0FBQSxDQUFDLE9BQU87UUFDbEMsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQyxLQUFLO1FBQ2hDLGNBQVEsR0FBWSxJQUFJLENBQUEsQ0FBQyxNQUFNO1FBQy9CLGdCQUFVLEdBQVksSUFBSSxDQUFBLENBQUMsUUFBUTtRQUNuQyxnQkFBVSxHQUFZLElBQUksQ0FBQSxDQUFDLFFBQVE7UUFDbkMsaUJBQVcsR0FBWSxJQUFJLENBQUEsQ0FBQyxNQUFNO1FBQ2xDLGlCQUFXLEdBQVksSUFBSSxDQUFBLENBQUMsTUFBTTtRQUNsQyxrQkFBWSxHQUFpQixJQUFJLENBQUE7UUFDakMsZUFBUyxHQUFpQixJQUFJLENBQUE7UUFDOUIsaUJBQVcsR0FBb0IsSUFBSSxDQUFBO1FBQ25DLHNCQUFnQixHQUFnQyxFQUFFLENBQUE7UUFDbEQsZ0JBQVUsR0FBVyxDQUFDLENBQUE7UUFFdEIsV0FBSyxHQUFlLElBQUksQ0FBQTtRQUN4QixVQUFJLEdBQWMsSUFBSSxDQUFBO1FBQ3RCLFlBQU0sR0FBZ0IsSUFBSSxDQUFBO1FBQzFCLFlBQU0sR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUEsQ0FBQyxTQUFTO1FBQ25DLHdCQUFrQixHQUFXLENBQUMsQ0FBQTtRQUM5Qix1QkFBaUIsR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDcEMsWUFBTSxHQUFnQixFQUFFLENBQUEsQ0FBQyxRQUFRO1FBQ2pDLHFCQUFlLEdBQWtDLEVBQUUsQ0FBQSxDQUFDLGFBQWE7UUFDakUsdUJBQWlCLEdBQVksS0FBSyxDQUFBLENBQUMsYUFBYTtRQUNoRCxrQkFBWSxHQUFRLEVBQUUsQ0FBQSxDQUFDLFlBQVk7UUFDbkMsc0JBQWdCLEdBQW9CLElBQUksQ0FBQSxDQUFDLFFBQVE7UUFDakQseUJBQW1CLEdBQW9CLElBQUksQ0FBQSxDQUFDLFFBQVE7UUFFcEQsa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDL0Isa0JBQVksR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7O0lBcXNDM0MsQ0FBQztJQW5zQ1Usc0NBQWUsR0FBdEI7O1FBQ0ksT0FBTztzQkFDRCxHQUFDLGtCQUFRLENBQUMsYUFBYSxJQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzFELEdBQUMsbUJBQVMsQ0FBQyxnQkFBZ0IsSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2hFLEdBQUMsbUJBQVMsQ0FBQyxTQUFTLElBQUcsSUFBSSxDQUFDLFVBQVUsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDbkQsR0FBQyxtQkFBUyxDQUFDLFlBQVksSUFBRyxJQUFJLENBQUMsYUFBYSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN6RCxHQUFDLG1CQUFTLENBQUMsZ0JBQWdCLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNoRSxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzlELEdBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsSUFBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2xFLEdBQUMsbUJBQVMsQ0FBQyx1QkFBdUIsSUFBRyxJQUFJLENBQUMsc0JBQXNCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzdFLEdBQUMsbUJBQVMsQ0FBQyx5QkFBeUIsSUFBRyxJQUFJLENBQUMsd0JBQXdCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2pGLEdBQUMsbUJBQVMsQ0FBQyxjQUFjLElBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDNUQsR0FBQyxtQkFBUyxDQUFDLGFBQWEsSUFBRyxJQUFJLENBQUMsY0FBYyxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUMzRCxHQUFDLG1CQUFTLENBQUMscUJBQXFCLElBQUcsSUFBSSxDQUFDLG9CQUFvQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN6RSxHQUFDLG1CQUFTLENBQUMsV0FBVyxJQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3RELEdBQUMsbUJBQVMsQ0FBQyxzQkFBc0IsSUFBRyxJQUFJLENBQUMsc0JBQXNCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzVFLEdBQUMsbUJBQVMsQ0FBQyx1QkFBdUIsSUFBRyxJQUFJLENBQUMsc0JBQXNCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzdFLEdBQUMsbUJBQVMsQ0FBQyxvQkFBb0IsSUFBRyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3RFLEdBQUMsbUJBQVMsQ0FBQyxlQUFlLElBQUcsSUFBSSxDQUFDLGtCQUFrQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNqRSxHQUFDLG1CQUFTLENBQUMsb0JBQW9CLElBQUcsSUFBSSxDQUFDLG9CQUFvQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN4RSxHQUFDLG1CQUFTLENBQUMsb0JBQW9CLElBQUcsSUFBSSxDQUFDLG1CQUFtQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN2RSxHQUFDLG1CQUFTLENBQUMsdUJBQXVCLElBQUcsSUFBSSxDQUFDLHNCQUFzQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM3RSxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzlELEdBQUMsbUJBQVMsQ0FBQyxrQkFBa0IsSUFBRyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3BFLEdBQUMsbUJBQVMsQ0FBQyxtQkFBbUIsSUFBRyxJQUFJLENBQUMsbUJBQW1CLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3RFLEdBQUMsbUJBQVMsQ0FBQyxtQkFBbUIsSUFBRyxJQUFJLENBQUMsbUJBQW1CLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3RFLEdBQUMsbUJBQVMsQ0FBQyxnQkFBZ0IsSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2hFLEdBQUMsbUJBQVMsQ0FBQywyQkFBMkIsSUFBRyxJQUFJLENBQUMseUJBQXlCLEVBQUUsUUFBSyxHQUFFLElBQUk7U0FDekYsQ0FBQTtJQUNMLENBQUM7SUFFWSwrQkFBUSxHQUFyQjs7OztnQkFDSSxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUE7Z0JBQ2pDLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLENBQUE7Z0JBQzNELElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUE7Z0JBQzNDLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUE7Z0JBQ3ZELElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsY0FBYyxDQUFDLENBQUE7Z0JBQzlELElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQy9DLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQy9DLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQy9DLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQy9DLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUE7Z0JBQ25ELElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUE7Z0JBQ25ELElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQy9DLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUE7Z0JBQ25ELElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUE7Z0JBQ25ELElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUE7Z0JBQ3RELElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsVUFBVSxDQUFDLENBQUE7Z0JBQ3RELEtBQVMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO29CQUNuQixJQUFJLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsU0FBUyxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsQ0FBQTtvQkFDbEYsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7aUJBQ3JCO2dCQUNELElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsc0JBQVksQ0FBQyxDQUFBO2dCQUNuRSxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsZ0JBQWdCLEVBQUUsc0JBQVksQ0FBQyxDQUFBO2dCQUNsRSxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSx5QkFBZSxFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUscUJBQVMsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLHFCQUFTLENBQUMsQ0FBQyxDQUFBO2dCQUMxRyxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLENBQUE7Z0JBQ25DLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQTtnQkFDakMsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFBO2dCQUNyQyxJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7Z0JBQ25DLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxFQUFFLENBQUE7Z0JBQ3pCLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFlBQVksQ0FBQyx5QkFBZSxDQUFDLENBQUE7Z0JBQ3RFLElBQUksQ0FBQyx3QkFBd0IsRUFBRSxDQUFBOzs7O0tBQ2xDO0lBRVksOEJBQU8sR0FBcEI7Ozs7NEJBQ0kscUJBQU0sSUFBSSxDQUFDLFdBQVcsRUFBRSxFQUFBOzt3QkFBeEIsU0FBd0IsQ0FBQTs7Ozs7S0FDM0I7SUFFTSw4QkFBTyxHQUFkLFVBQWUsSUFBUztRQUNwQixJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQzNCLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDL0IsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQzlCLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQSxDQUFDLE1BQU07UUFDckIsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFBLENBQUMsT0FBTztRQUN4QixJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTtRQUN4QixJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQTtRQUMzQixJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7UUFDcEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUMvQyxvQkFBTyxDQUFDLFdBQVcsRUFBRSxDQUFBO0lBQ3pCLENBQUM7SUFFTSw4QkFBTyxHQUFkO1FBQ0ksSUFBSSxDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUMzQixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ3RCLElBQUksQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLHdCQUFjLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUN0RCxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ3pCLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxLQUFLLENBQUE7UUFDOUIsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxlQUFlLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUE7UUFDakMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1FBQ3ZDLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxHQUFHLEtBQUssQ0FBQTtRQUNoQyxJQUFJLENBQUMsYUFBYSxDQUFDLGlCQUFpQixFQUFFLENBQUE7UUFDdEMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFBO1FBQy9CLCtEQUErRDtRQUMvRCxJQUFJLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQTtRQUN0QixXQUFXLENBQUMsMkJBQTJCLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2pELFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDdkMsdUJBQVUsQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLENBQUE7SUFDcEMsQ0FBQztJQUVNLDhCQUFPLEdBQWQ7O1FBQ0ksS0FBSyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUU7WUFDakMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFBO1NBQ3JDO1FBQ0QsSUFBSSxDQUFDLGdCQUFnQixHQUFHLEVBQUUsQ0FBQTtRQUMxQixJQUFJLENBQUMsWUFBWSxHQUFHLEVBQUUsQ0FBQTtRQUN0QixJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ3pCLE1BQUEsSUFBSSxDQUFDLFdBQVcsMENBQUUsS0FBSyxHQUFFO1FBQ3pCLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7SUFDaEQsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0IseUNBQXlDO0lBQ3pDLG1DQUFZLEdBQVosVUFBYSxLQUEwQixFQUFFLElBQVk7UUFDakQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLElBQUksRUFBRTtZQUNOLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQzVCLHVCQUFVLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1lBQzNCLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDN0I7SUFDTCxDQUFDO0lBRUQsMENBQTBDO0lBQzFDLG9DQUFhLEdBQWIsVUFBYyxLQUEwQixFQUFFLElBQVk7UUFDbEQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLENBQUMsSUFBSSxFQUFFO1lBQ1AsT0FBTTtTQUNUO2FBQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDN0MsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsdUJBQXVCLENBQUMsQ0FBQTtTQUM3RDthQUFNLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFLEVBQUUsRUFBRSxPQUFPO1lBQzdDLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLFlBQVksQ0FBQyxDQUFBO1NBQ2xEO2FBQU0sSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFLEVBQUUsRUFBRSxNQUFNO1lBQ2xDLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLG9CQUFvQixDQUFDLENBQUE7U0FDMUQ7UUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtJQUNsQyxDQUFDO0lBRUQsMENBQTBDO0lBQzFDLG9DQUFhLEdBQWIsVUFBYyxLQUEwQixFQUFFLElBQVk7UUFDbEQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ3hCLE9BQU07U0FDVDthQUFNLElBQUksb0JBQU8sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDL0MsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFBO1NBQzVEO2FBQU0sSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUU7WUFDM0IsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyx3QkFBd0IsQ0FBQyxDQUFBO1NBQ3hEO1FBQ0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7SUFDbEMsQ0FBQztJQUVELGlEQUFpRDtJQUNqRCwwQ0FBbUIsR0FBbkIsVUFBb0IsS0FBMEIsRUFBRSxJQUFZO1FBQTVELGlCQVNDO1FBUkcsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxFQUFFO1lBQy9DLE9BQU07U0FDVDtRQUNELHVCQUFVLENBQUMsY0FBYyxDQUFDLHNCQUFzQixFQUFFO1lBQzlDLEVBQUUsRUFBRSxjQUFNLE9BQUEsS0FBSSxDQUFDLFFBQVEsSUFBSSxLQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsRUFBakQsQ0FBaUQ7WUFDM0QsTUFBTSxFQUFFLGNBQVEsQ0FBQztTQUNwQixDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsd0NBQXdDO0lBQ3hDLGtDQUFXLEdBQVgsVUFBWSxLQUEwQixFQUFFLElBQVk7UUFDaEQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxFQUFFO1lBQ2hDLE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO0lBQ2xDLENBQUM7SUFFRCx5Q0FBeUM7SUFDekMsbUNBQVksR0FBWixVQUFhLEtBQTBCLEVBQUUsSUFBWTtRQUNqRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ3hDLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDeEIsT0FBTTtTQUNUO2FBQU0sSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUU7WUFDM0IsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyx3QkFBd0IsQ0FBQyxDQUFBO1NBQ3hEO1FBQ0QsdUJBQVUsQ0FBQyxPQUFPLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxDQUFBO0lBQzdDLENBQUM7SUFFRCw2Q0FBNkM7SUFDN0MsdUNBQWdCLEdBQWhCLFVBQWlCLEtBQTBCLEVBQUUsSUFBWTtRQUNyRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ3hDLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDeEIsT0FBTTtTQUNUO1FBQ0QsdUJBQVUsQ0FBQyxPQUFPLENBQUMsdUJBQXVCLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDckQsQ0FBQztJQUVELCtDQUErQztJQUMvQyx3Q0FBaUIsR0FBakIsVUFBa0IsS0FBMEIsRUFBRSxJQUFZO1FBQ3RELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLENBQUE7UUFDeEMsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLEtBQUssQ0FBQyxDQUFBO1FBQ2xELElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMsbUJBQW1CLEVBQUUsSUFBSSxFQUFFLFVBQVUsQ0FBQyxDQUFBO1NBQzVEO0lBQ0wsQ0FBQztJQUVELDJDQUEyQztJQUMzQyxzQ0FBZSxHQUFmLFVBQWdCLEtBQTBCLEVBQUUsSUFBWTtRQUF4RCxpQkFZQztRQVhHLFFBQVEsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDekIsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLElBQUksRUFBRTtZQUNOLElBQU0sT0FBSyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ3JDLHVCQUFVLENBQUMsT0FBTyxDQUFDLHVCQUF1QixFQUFFLEVBQUUsR0FBRyxFQUFFLDJCQUEyQixFQUFFLE1BQU0sRUFBRSxDQUFDLE9BQUssQ0FBQyxFQUFFLEVBQUUsVUFBQyxJQUFZLEVBQUUsU0FBaUIsRUFBRSxNQUF5QztnQkFDMUssSUFBSSxLQUFJLENBQUMsT0FBTyxFQUFFO29CQUNkLElBQU0sTUFBTSxHQUFHLG9CQUFPLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFBO29CQUNsRSx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxhQUFhLEVBQUUsRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxNQUFJLE9BQUssTUFBRyxFQUFFLE1BQU0sUUFBQSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksS0FBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsRUFBMUMsQ0FBMEMsQ0FBQyxDQUFBO2lCQUN0STtZQUNMLENBQUMsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBRUQsd0NBQXdDO0lBQ3hDLGtDQUFXLEdBQVgsVUFBWSxLQUEwQixFQUFFLElBQVk7UUFDaEQsSUFBSSxDQUFDLG9CQUFPLENBQUMsUUFBUSxDQUFDLFlBQVksRUFBRSxFQUFFO1lBQ2xDLE9BQU07U0FDVDtRQUNELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLENBQUE7UUFDeEMsSUFBSSxJQUFJLEVBQUU7WUFDTix1QkFBVSxDQUFDLE9BQU8sQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRSxLQUFLLENBQUMsQ0FBQTtTQUM1RDtJQUNMLENBQUM7SUFFRCxpREFBaUQ7SUFDakQsdUNBQWdCLEdBQWhCLFVBQWlCLEtBQTBCLEVBQUUsSUFBWTtRQUNyRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ3hDLElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1NBQ3hEO0lBQ0wsQ0FBQztJQUVELHFEQUFxRDtJQUNyRCx5Q0FBa0IsR0FBbEIsVUFBbUIsS0FBMEIsRUFBRSxJQUFZO1FBQ3ZELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLENBQUE7UUFDeEMsSUFBSSxJQUFJLEVBQUU7WUFDTix1QkFBVSxDQUFDLE9BQU8sQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1NBQzdFO0lBQ0wsQ0FBQztJQUVELDRDQUE0QztJQUM1Qyx1Q0FBZ0IsR0FBaEIsVUFBaUIsS0FBMEIsRUFBRSxJQUFZO1FBQXpELGlCQWFDO1FBWkcsUUFBUSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN6QixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ3hDLElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMsc0JBQXNCLEVBQUUsSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFLFVBQUMsRUFBVTtnQkFDaEUsSUFBSSxFQUFFLEVBQUU7b0JBQ0osb0JBQU8sQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLEVBQUUsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7aUJBQ2xEO2dCQUNELElBQUksS0FBSSxDQUFDLE9BQU8sRUFBRTtvQkFDZCxLQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFBO2lCQUM3QjtZQUNMLENBQUMsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBRUQsK0NBQStDO0lBQy9DLHdDQUFpQixHQUFqQixVQUFrQixLQUEwQixFQUFFLElBQVk7UUFBMUQsaUJBYUM7O1FBWkcsSUFBTSxJQUFJLEdBQWUsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQ2pELElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDeEIsT0FBTTtTQUNUO1FBQ0QsSUFBTSxPQUFPLEdBQUcsT0FBQSxvQkFBTyxDQUFDLGFBQWEsQ0FBQyxvQkFBTyxDQUFDLE1BQU0sRUFBRSxDQUFDLDBDQUFFLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxNQUFLLEVBQUUsQ0FBQTtRQUN4RixJQUFJLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQUEsR0FBRztZQUMzQyxJQUFJLEdBQUcsRUFBRTtnQkFDTCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFBO2FBQ25DO1lBQ0Qsa0NBQWtDO1lBQ2xDLE9BQU8sQ0FBQyxPQUFPLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQyxJQUFLLE9BQUEsdUJBQVUsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsRUFBRSxLQUFJLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxFQUE1RixDQUE0RixDQUFDLENBQUE7UUFDM0gsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsZ0RBQWdEO0lBQ2hELHlDQUFrQixHQUFsQixVQUFtQixLQUEwQixFQUFFLElBQVk7UUFDdkQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLElBQUksRUFBRTtZQUNOLElBQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUNyRCxJQUFJLE9BQU8sRUFBRTtnQkFDVCxJQUFNLEtBQUssR0FBRyxJQUFJLGtCQUFRLEVBQUUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsR0FBRyxFQUFFLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsT0FBTyxDQUFDLEVBQUUsRUFBRSxPQUFPLENBQUMsRUFBRSxDQUFDLENBQUE7Z0JBQzVGLHVCQUFVLENBQUMsT0FBTyxDQUFDLHdCQUF3QixFQUFFLEtBQUssQ0FBQyxDQUFBO2FBQ3REO1NBQ0o7SUFDTCxDQUFDO0lBRUQsNkNBQTZDO0lBQzdDLHNDQUFlLEdBQWYsVUFBZ0IsS0FBMEIsRUFBRSxJQUFZO1FBQXhELGlCQVNDO1FBUkcsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLElBQUksSUFBSSxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUN6Qix1QkFBVSxDQUFDLE9BQU8sQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLEVBQUUsVUFBQyxFQUFXO2dCQUN4RCxJQUFJLEtBQUksQ0FBQyxPQUFPLElBQUksRUFBRSxFQUFFO29CQUNwQixLQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFBO2lCQUM3QjtZQUNMLENBQUMsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBRUQsMENBQTBDO0lBQzFDLHFDQUFjLEdBQWQsVUFBZSxLQUEwQixFQUFFLElBQVk7UUFDbkQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUN4QyxJQUFJLElBQUksRUFBRTtZQUNOLHVCQUFVLENBQUMsT0FBTyxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFLEtBQUssQ0FBQyxDQUFBO1NBQzNEO0lBQ0wsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFekcscUNBQWMsR0FBdEI7UUFDSSxJQUFJLENBQUMsb0JBQU8sQ0FBQyxLQUFLLENBQUMsaUJBQWlCLEVBQUUsRUFBRSxFQUFFLGtCQUFrQjtZQUN4RCxJQUFJLENBQUMsZUFBZSxDQUFDLFNBQVMsQ0FBQyx3QkFBYyxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUE7WUFDdEQsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLEVBQUUsQ0FBQTtZQUN6QixJQUFJLENBQUMsS0FBSyxDQUFDLGNBQWMsRUFBRSxDQUFBO1NBQzlCO1FBQ0QsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUEsQ0FBQyxRQUFRO1FBQ3BDLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQSxDQUFDLE9BQU87SUFDNUIsQ0FBQztJQUVELFNBQVM7SUFDRCx1Q0FBZ0IsR0FBeEI7UUFDSSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTtJQUMvQixDQUFDO0lBRUQsT0FBTztJQUNDLGlDQUFVLEdBQWxCLFVBQW1CLElBQWtCO1FBQXJDLGlCQWNDO1FBYkcsSUFBSSxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUUsRUFBRTtZQUN4QixPQUFPLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDbEM7UUFDRCxJQUFJLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBbEIsQ0FBa0IsQ0FBQyxDQUFBO1FBQ3JELElBQUksS0FBSyxFQUFFO1lBQ1AsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7WUFDL0MsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxDQUFBO1NBQ2xDO2FBQU07WUFDSCxJQUFJLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxVQUFBLEVBQUU7Z0JBQzFCLEtBQUssR0FBRyxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsU0FBUyxDQUFDLG1CQUFTLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLEtBQUksQ0FBQyxjQUFjLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUE7Z0JBQzFGLEtBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUNuQyxDQUFDLENBQUMsQ0FBQTtTQUNMO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxvQ0FBYSxHQUFyQixVQUFzQixJQUFrQjtRQUNwQyxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2pELElBQUksS0FBSyxFQUFFO1lBQ1AsS0FBSyxDQUFDLEtBQUssRUFBRSxDQUFBO1lBQ2IsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxDQUFBO1NBQ2xDO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCx1Q0FBZ0IsR0FBeEI7UUFDSSxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUE7SUFDcEIsQ0FBQztJQUVELE9BQU87SUFDQyxzQ0FBZSxHQUF2QixVQUF3QixHQUFZO1FBQ2hDLEdBQUcsR0FBRyxDQUFDLEdBQUcsQ0FBQTtRQUNWLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLEdBQUcsQ0FBQTtRQUMzQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLEdBQUcsQ0FBQTtRQUNsQyxJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFDLEVBQXhDLENBQXdDLENBQUMsQ0FBQTtRQUMvRSxJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFDLEVBQXhDLENBQXdDLENBQUMsQ0FBQTtJQUNuRixDQUFDO0lBRUQsU0FBUztJQUNELHdDQUFpQixHQUF6QixVQUEwQixJQUFhO1FBQ25DLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQy9CLENBQUM7SUFFRCxTQUFTO0lBQ0QsNkNBQXNCLEdBQTlCO1FBQ0ksSUFBTSxLQUFLLEdBQWlCLEVBQUUsRUFBRSxPQUFPLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxDQUFBO1FBQ3ZFLEtBQUssSUFBSSxLQUFLLElBQUksT0FBTyxFQUFFO1lBQ3ZCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDeEMsSUFBSSxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDM0I7UUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSSxJQUFLLE9BQUEsRUFBRSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLEVBQWhDLENBQWdDLENBQUMsQ0FBQTtJQUNoRixDQUFDO0lBRUQsU0FBUztJQUNELCtDQUF3QixHQUFoQztRQUNJLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtJQUN6QixDQUFDO0lBRUQsU0FBUztJQUNELHFDQUFjLEdBQXRCLFVBQXVCLEtBQWE7O1FBQ2hDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQzNCLElBQUksT0FBQSxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSwwQ0FBRSxRQUFRLE1BQUssS0FBSyxFQUFFO1lBQ2pELElBQUksQ0FBQyxZQUFZLENBQUMsVUFBVSxFQUFFLENBQUE7U0FDakM7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHFDQUFjLEdBQXRCLFVBQXVCLEtBQWE7O1FBQ2hDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQzNCLElBQUksT0FBQSxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSwwQ0FBRSxRQUFRLE1BQUssS0FBSyxFQUFFO1lBQ2pELElBQUksQ0FBQyxZQUFZLENBQUMsVUFBVSxFQUFFLENBQUE7U0FDakM7SUFDTCxDQUFDO0lBRUQsMEJBQTBCO0lBQ2xCLDJDQUFvQixHQUE1QjtRQUNJLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUNyQixJQUFJLENBQUMsWUFBWSxDQUFDLGNBQWMsRUFBRSxDQUFBO0lBQ3RDLENBQUM7SUFFRCxPQUFPO0lBQ0Msa0NBQVcsR0FBbkIsVUFBb0IsS0FBYyxFQUFFLFlBQXFCO1FBQXpELGlCQWVDO1FBZEcsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUMzQixPQUFPLFlBQVksSUFBSSxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQTtTQUNsRzthQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLHFCQUFTLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxvQkFBb0I7WUFDbkYsSUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQ2pHLHVCQUFVLENBQUMsSUFBSSxDQUFDLHFCQUFTLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxFQUFFLHFCQUFTLENBQUMsUUFBUSxFQUFFLDBCQUFlLEVBQUUsdUJBQVUsQ0FBQyxTQUFTLENBQUMsQ0FBQTtZQUM1RyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFBO1lBQzdCLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO1NBQ2hDO1FBQ0QsS0FBSztRQUNMLHVCQUFVLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsdUJBQVUsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1lBQ2hHLElBQUksS0FBSSxDQUFDLFFBQVEsSUFBSSxZQUFZLEVBQUU7Z0JBQy9CLEtBQUksQ0FBQyxjQUFjLENBQUMsS0FBSSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFBO2FBQzNFO1FBQ0wsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsU0FBUztJQUNELDZDQUFzQixHQUE5QixVQUErQixJQUFnQjtRQUMzQyxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLFlBQUksT0FBQSxDQUFDLENBQUMsTUFBTSxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksMENBQUUsS0FBSyxNQUFLLElBQUksQ0FBQyxHQUFHLENBQUEsRUFBQSxDQUFDLENBQUE7UUFDcEYsSUFBSSxFQUFFLEVBQUU7WUFDSixJQUFJLENBQUMsb0JBQW9CLENBQUMsRUFBRSxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ3RDO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCw2Q0FBc0IsR0FBOUIsVUFBK0IsSUFBUztRQUNwQyxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLFlBQUksT0FBQSxDQUFDLENBQUMsTUFBTSxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksMENBQUUsS0FBSyxNQUFLLElBQUksQ0FBQyxHQUFHLENBQUEsRUFBQSxDQUFDLENBQUE7UUFDcEYsSUFBSSxFQUFFLEVBQUU7WUFDSixxQkFBUyxDQUFDLGNBQWMsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7U0FDdEY7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHNDQUFlLEdBQXZCO1FBQUEsaUJBYUM7UUFaRyxJQUFNLEtBQUssR0FBeUMsRUFBRSxFQUFFLE9BQU8sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFBO1FBQzNGLEtBQUssSUFBSSxLQUFLLElBQUksT0FBTyxFQUFFO1lBQ3ZCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDeEMsSUFBSSxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLE1BQUEsRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUE7U0FDMUQ7UUFDRCxJQUFNLFNBQVMsR0FBRyxvQkFBTyxDQUFDLFFBQVEsQ0FBQyxVQUFVLEVBQUUsQ0FBQTtRQUMvQyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTtZQUNuQyxJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQTtZQUN6QyxJQUFNLEdBQUcsR0FBRyxTQUFTLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsR0FBRyxFQUFFLEVBQUUsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUE7WUFDbkgsRUFBRSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUNuQixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3ZGLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVELFdBQVc7SUFDSCwwQ0FBbUIsR0FBM0I7UUFBQSxpQkFhQztRQVpHLElBQU0sS0FBSyxHQUF5QyxFQUFFLEVBQUUsT0FBTyxHQUFHLG9CQUFPLENBQUMsUUFBUSxDQUFDLFVBQVUsRUFBRSxDQUFBO1FBQy9GLEtBQUssSUFBSSxLQUFLLElBQUksT0FBTyxFQUFFO1lBQ3ZCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDeEMsSUFBSSxJQUFJLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLE1BQUEsRUFBRSxJQUFJLEVBQUUsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUE7U0FDMUQ7UUFDRCxJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFBO1FBQzNDLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQ25DLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFBO1lBQ3pDLElBQU0sR0FBRyxHQUFHLFNBQVMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQTtZQUNuSCxFQUFFLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ25CLEVBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDdkYsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRU8seUNBQWtCLEdBQTFCO1FBQ0ksSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFBO1FBQ3RCLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFBO1FBQzFCLElBQUksQ0FBQyxZQUFZLENBQUMsY0FBYyxFQUFFLENBQUE7SUFDdEMsQ0FBQztJQUVELFdBQVc7SUFDSCwyQ0FBb0IsR0FBNUI7UUFDSSxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxhQUFhLEVBQUUsRUFBakIsQ0FBaUIsQ0FBQyxDQUFBO0lBQy9DLENBQUM7SUFFRCxXQUFXO0lBQ0gsMENBQW1CLEdBQTNCO1FBQ0ksSUFBSSxDQUFDLGlCQUFpQixFQUFFLENBQUE7SUFDNUIsQ0FBQztJQUVELFdBQVc7SUFDSCw2Q0FBc0IsR0FBOUI7UUFDSSxJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQTtJQUMvQixDQUFDO0lBRUQsU0FBUztJQUNELHNDQUFlLEdBQXZCO1FBQ0ksSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO0lBQ3hCLENBQUM7SUFFRCxTQUFTO0lBQ0QseUNBQWtCLEdBQTFCO1FBQ0ksSUFBTSxLQUFLLEdBQTZDLEVBQUUsQ0FBQTtRQUMxRCxLQUFLLElBQUksS0FBSyxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUU7WUFDcEMsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUN4QyxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7WUFDbkMsSUFBSSxNQUFNLEVBQUU7Z0JBQ1IsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksTUFBQSxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsQ0FBQyxDQUFBO2FBQ3ZDO1NBQ0o7UUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTtZQUNsQyxFQUFFLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7WUFDckMsRUFBRSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFBO1lBQ3JELEVBQUUsQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcsU0FBUyxDQUFDLFFBQVEsQ0FBQyxxQkFBVSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtZQUMvRixFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUE7UUFDL0YsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsU0FBUztJQUNELDBDQUFtQixHQUEzQjtRQUNJLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ3RELENBQUM7SUFFRCxPQUFPO0lBQ0MsMENBQW1CLEdBQTNCLFVBQTRCLElBQWdCO1FBQ3hDLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7UUFDeEIsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLFlBQUksT0FBQSxDQUFDLENBQUMsTUFBTSxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksMENBQUUsS0FBSyxNQUFLLEtBQUssQ0FBQSxFQUFBLENBQUMsQ0FBQTtRQUN4RixJQUFJLEVBQUUsRUFBRTtZQUNKLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxFQUFFLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDdkM7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHVDQUFnQixHQUF4QixVQUF5QixLQUFhO1FBQ2xDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO0lBQy9CLENBQUM7SUFFRCxNQUFNO0lBQ0UsZ0RBQXlCLEdBQWpDLFVBQWtDLElBQVM7UUFDdkMsSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLE1BQU0sRUFBRTtZQUN2Qix5QkFBVyxDQUFDLG1CQUFtQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtTQUN2RTtJQUNMLENBQUM7SUFHRCxzQkFBWSxrQ0FBUTtRQUZwQixpSEFBaUg7YUFFakgsY0FBeUIsT0FBTyxJQUFJLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQSxDQUFDLENBQUM7OztPQUFBO0lBRWhFLE9BQU87SUFDQyxpQ0FBVSxHQUFsQixVQUFtQixhQUFzQjtRQUNyQyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLHFCQUFTLENBQUMsZUFBZSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUE7UUFDbkYsSUFBSSxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksRUFBRTtZQUNwQyxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1lBQ3pCLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLENBQUE7WUFDekIsdUJBQVUsQ0FBQyxzQkFBc0IsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLCtCQUFvQixDQUFDLENBQUE7U0FDNUU7YUFBTTtZQUNILElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtTQUN4QjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0QsK0NBQXdCLEdBQWhDO1FBQ0ksSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUN4QixJQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDLHFCQUFxQixFQUFFLENBQUE7UUFDckUsSUFBSSxjQUFjLEVBQUUsRUFBRSxRQUFRO1lBQzFCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7U0FDdkQ7UUFDRCxJQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFBO1FBQzdDLFNBQVM7UUFDVCxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsS0FBSyxDQUFDLDhCQUFtQixDQUFDLFVBQVUsQ0FBQyxDQUFDLEVBQXhDLENBQXdDLENBQUMsQ0FBQTtJQUNqRixDQUFDO0lBRUQsT0FBTztJQUNDLGdDQUFTLEdBQWpCLFVBQWtCLE1BQWU7UUFBakMsaUJBdVBDOztRQXRQRyxJQUFJLENBQUMsa0JBQWtCLEdBQUcsdUJBQVUsQ0FBQyxTQUFTLENBQUE7UUFDOUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDdkIsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDNUIsT0FBTztRQUNQLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsY0FBYyxFQUFFLEVBQUUsVUFBVSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtRQUM1RixJQUFNLE9BQU8sR0FBRyxvQkFBTyxDQUFDLFFBQVEsQ0FBQyxVQUFVLEVBQUUsRUFBRSxPQUFPLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLEVBQUUsQ0FBQTtRQUNsRixJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixFQUFFLENBQUE7UUFDaEQsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1FBQ2hELElBQUksRUFBRSxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDLEVBQUUsRUFBRSxHQUFHLENBQUMsRUFBRSxFQUFFLEdBQUcsQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDLEVBQUUsRUFBRSxHQUFHLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEVBQUUsRUFBRSxHQUFHLENBQUMsRUFBRSxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQTtRQUNsSCxJQUFNLEtBQUssR0FBaUIsRUFBRSxFQUFFLE9BQU8sR0FBVSxFQUFFLEVBQUUsT0FBTyxHQUFVLEVBQUUsRUFBRSxZQUFZLEdBQWlCLEVBQUUsQ0FBQTtRQUN6RyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsS0FBSyxFQUFFLENBQUE7UUFDN0IsSUFBSSxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUE7UUFDekIsSUFBTSxtQkFBbUIsR0FBK0IsRUFBRSxDQUFBO1FBQzFELElBQU0sTUFBTSxHQUFHLHFCQUFTLENBQUMscUJBQXFCLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsZUFBZSxFQUFFLENBQUMsQ0FBQTtRQUNwRixJQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFBO1FBQ2xDLElBQUkscUJBQXFCLEdBQUcsRUFBRSxDQUFBO1FBQzlCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ3BDLElBQU0sS0FBSyxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUNuRSxJQUFNLFFBQVEsR0FBRyxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxRQUFRLEtBQUkscUJBQVMsQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDbkUsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBTSxNQUFNLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsRUFBRSxVQUFVLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsRUFBRSxXQUFXLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQTtnQkFDeEcsSUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFBO2dCQUMzQyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUE7Z0JBQ3ZDLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7b0JBQ2pCLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxNQUFNLEtBQUssd0JBQWEsQ0FBQyxDQUFDLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFBO29CQUM3RixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsRUFBRSxXQUFXLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxDQUFBO29CQUM5RixJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssd0JBQWEsRUFBRTt3QkFDL0IsZ0JBQWdCO3dCQUNoQixLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO3FCQUNuQjt5QkFBTSxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUUsRUFBRTt3QkFDekIsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO3dCQUNsRCxJQUFJLElBQUksRUFBRTs0QkFDTixZQUFZLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBLENBQUMsSUFBSTs0QkFDNUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFBO3lCQUM1RDtxQkFDSjtpQkFDSjtxQkFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO2lCQUMzQjtxQkFBTSxJQUFJLFVBQVUsRUFBRSxFQUFFLE9BQU87b0JBQzVCLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxVQUFVLFlBQUEsRUFBRSxJQUFJLE1BQUEsRUFBRSxDQUFDLENBQUE7b0JBQ2xDLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsYUFBYSxFQUFFLFVBQVUsQ0FBQyxDQUFBO2lCQUNsSjtxQkFBTSxJQUFJLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxDQUFDLE1BQU0sSUFBSSxNQUFNLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxFQUFFO29CQUNsRCxJQUFJLElBQUksQ0FBQyxRQUFRLEtBQUssZ0JBQVEsQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxnQkFBUSxDQUFDLEtBQUssSUFBSSxxQ0FBaUIsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO3dCQUNqSCxJQUFJLFFBQVEsR0FBRyxxQkFBUyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLEdBQUcsRUFBRSxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTt3QkFDaEYsUUFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFBO3dCQUM1RyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTtxQkFDdEI7eUJBQU07d0JBQ0gscUJBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxFQUFFLEVBQUUsRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUE7cUJBQ25LO2lCQUNKO2dCQUNELFNBQVM7Z0JBQ1QsSUFBSSxNQUFNLEVBQUU7b0JBQ1IsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUFFLE1BQU0sUUFBQSxFQUFFLElBQUksTUFBQSxFQUFFLENBQUMsQ0FBQTtvQkFDOUIsY0FBYztvQkFDZCxJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssQ0FBQyxJQUFJLE1BQU0sQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFO3dCQUNwQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxFQUFFLEVBQUUsV0FBVyxFQUFFLEtBQUssQ0FBQyxDQUFBO3FCQUNoRTtpQkFDSjtnQkFDRCxhQUFhO2dCQUNiLElBQUksQ0FBQyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUU7b0JBQzNCLFNBQVM7b0JBQ1QsSUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsV0FBSSxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLDBDQUFFLFVBQVUsR0FBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQTtvQkFDL0ksSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQSxDQUFDLFFBQVE7b0JBQzFFLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxXQUFXLENBQUMsZUFBZSxDQUFDLENBQUE7aUJBQy9IO2dCQUNELFNBQVM7Z0JBQ1QsSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUUsRUFBRTtvQkFDMUIsU0FBUztvQkFDVCxJQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxXQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsMENBQUUsVUFBVSxHQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFBO29CQUMvSSxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQSxDQUFDLFFBQVE7b0JBQ3BHLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxXQUFXLENBQUMsaUJBQWlCLENBQUMsQ0FBQTtpQkFDakk7Z0JBQ0QsU0FBUztnQkFDVCxJQUFJLENBQUMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO29CQUMxQixxQkFBUyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLEVBQUUsRUFBRSxFQUFFLFdBQVcsQ0FBQyxDQUFBO2lCQUMvRDtnQkFDRCxLQUFLO2dCQUNMLElBQU0sSUFBSSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsSUFBSSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7Z0JBQzVELElBQUksSUFBSSxFQUFFO29CQUNOLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsR0FBRyxFQUFFLEVBQUUsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUE7b0JBQzFGLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsR0FBRyxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO2lCQUMxSTtnQkFDRCxJQUFJLElBQUksRUFBRTtvQkFDTixJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxFQUFFLFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFBO29CQUMxRixxQkFBUyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLEdBQUcsRUFBRSxFQUFFLEdBQUcsQ0FBQyxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxxQkFBUyxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtpQkFDMUk7Z0JBQ0QsT0FBTztnQkFDUCxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7Z0JBQ25DLElBQUksTUFBTSxFQUFFO29CQUNSLElBQU0sS0FBSyxHQUFHLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxFQUFFLEVBQUUsV0FBVyxDQUFDLENBQUE7b0JBQzFFLEtBQUssQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO29CQUNqQixLQUFLLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQTtvQkFDbkQsS0FBSyxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxTQUFTLENBQUMsUUFBUSxDQUFDLHFCQUFVLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7b0JBQzdGLEtBQUssQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFDLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUE7aUJBQ3ZGO2dCQUNELGNBQWM7Z0JBQ2QsSUFBSSxZQUFZLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxZQUFZLENBQUMsRUFBRTtvQkFDcEQsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxvQkFBTyxDQUFDLHNCQUFzQixDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUE7b0JBQzNGLElBQUksUUFBUSxFQUFFO3dCQUNWLG1CQUFtQixDQUFDLFlBQVksQ0FBQyxHQUFHLElBQUksQ0FBQTt3QkFDeEMsZ0JBQWdCO3dCQUNoQixJQUFNLEtBQUssR0FBRyxvQkFBTyxDQUFDLDJCQUEyQixDQUFDLFlBQVksQ0FBQyxDQUFBO3dCQUMvRCxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUU7NEJBQ1gscUJBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRSxHQUFHLEVBQUUsRUFBRSxRQUFRLENBQUMsV0FBVyxDQUFDLENBQUMsT0FBTyxHQUFHLEtBQUssS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFBO3lCQUNoSDtxQkFDSjtpQkFDSjtnQkFDRCxRQUFRO2dCQUNSLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQTtnQkFDdEQsSUFBSSxXQUFXLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtvQkFDeEIsSUFBTSxZQUFZLEdBQUcscUJBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxLQUFLLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQTtvQkFDL0UsdUJBQVUsQ0FBQyxxQkFBcUIsQ0FBQyxZQUFZLEVBQUUsV0FBVyxFQUFFLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFDLENBQUE7aUJBQ3pGO2dCQUNELE9BQU87Z0JBQ1AsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxnQkFBUSxDQUFDLEdBQUcsRUFBRTtvQkFDL0MsSUFBTSxZQUFZLEdBQUcscUJBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxFQUFFLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQTtvQkFDNUUsWUFBWSxDQUFDLE9BQU8sR0FBRyxZQUFZLENBQUMsQ0FBQyxDQUFDLHFDQUEwQixDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxnQ0FBcUIsQ0FBQyxVQUFVLENBQUMsQ0FBQTtpQkFDbkg7Z0JBQ0QsUUFBUTtnQkFDUixJQUFJLGNBQWMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtnQkFDaEUsSUFBSSxjQUFjLEVBQUU7b0JBQ2hCLEtBQUssSUFBSSxHQUFDLEdBQUcsQ0FBQyxFQUFFLEdBQUMsR0FBRyxjQUFjLENBQUMsTUFBTSxFQUFFLEdBQUMsRUFBRSxFQUFFO3dCQUN0QyxJQUFBLEtBQXlDLGNBQWMsQ0FBQyxHQUFDLENBQUMsRUFBeEQsY0FBYyxvQkFBQSxFQUFFLGtCQUFrQix3QkFBc0IsQ0FBQTt3QkFDaEUsSUFBSSxPQUFPLEdBQUcsa0JBQWtCLEdBQUcsR0FBRyxHQUFHLGNBQWMsQ0FBQyxFQUFFLENBQUE7d0JBQzFELElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxPQUFPLENBQUMsRUFBRTs0QkFDakMscUJBQXFCLENBQUMsT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFBOzRCQUNyQyxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLGNBQWMsRUFBRSxrQkFBa0IsQ0FBQyxDQUFBOzRCQUNqRixJQUFNLEtBQUssR0FBRyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFBOzRCQUM3RSxJQUFNLGFBQVcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixDQUFDLENBQUMsV0FBVyxDQUFBOzRCQUNoRixJQUFJLFFBQVEsR0FBWSxJQUFJLENBQUE7NEJBQzVCLElBQUksY0FBYyxDQUFDLElBQUksSUFBSSxzQkFBYyxDQUFDLE1BQU0sSUFBSSxjQUFjLENBQUMsSUFBSSxJQUFJLHNCQUFjLENBQUMsR0FBRyxJQUFJLGNBQWMsQ0FBQyxJQUFJLElBQUksc0JBQWMsQ0FBQyxTQUFTLEVBQUU7Z0NBQzlJLFFBQVEsR0FBRyxxQkFBUyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUUsRUFBRSxFQUFFLGFBQVcsQ0FBQyxDQUFBOzZCQUN0RTtpQ0FBTSxJQUFJLHFDQUFpQixDQUFDLFdBQVcsQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFO2dDQUMxRCxRQUFRLEdBQUcscUJBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxHQUFHLEVBQUUsRUFBRSxhQUFXLENBQUMsQ0FBQTs2QkFDN0U7aUNBQU07Z0NBQ0gsUUFBUSxHQUFHLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUUsR0FBRyxFQUFFLEVBQUUsYUFBVyxDQUFDLENBQUE7NkJBQy9FOzRCQUNELElBQUksTUFBTSxHQUFHLENBQUMsQ0FBQTs0QkFDZCxRQUFRLGNBQWMsQ0FBQyxJQUFJLEVBQUU7Z0NBQ3pCLEtBQUssc0JBQWMsQ0FBQyxRQUFRLENBQUM7Z0NBQzdCLEtBQUssc0JBQWMsQ0FBQyx3QkFBd0IsQ0FBQztnQ0FDN0MsS0FBSyxzQkFBYyxDQUFDLG9CQUFvQjtvQ0FDcEMsTUFBTSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFBO29DQUM1QixNQUFNO2dDQUNWLEtBQUssc0JBQWMsQ0FBQyxNQUFNO29DQUN0QixNQUFNLEdBQUcsQ0FBQyxDQUFBO29DQUNWLE1BQU07NkJBQ2I7NEJBQ0QsUUFBUSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7NEJBQ3hCLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxLQUFLLENBQUE7NEJBQ2pELElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxRQUFRLEVBQUUsUUFBUSxDQUFDLENBQUE7eUJBQ2hEO3FCQUNKO2lCQUNKO2FBRUo7aUJBQU07Z0JBQ0gsSUFBSSxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQ3BELElBQUksTUFBTSxFQUFFO29CQUNSLElBQUksUUFBUSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxDQUFBO29CQUNwRCxJQUFJLFFBQVEsR0FBRyxxQkFBUyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLEdBQUcsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUFBO29CQUMzRSxRQUFRLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFDLENBQUE7b0JBQ2hILFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO29CQUNuQixJQUFJLFFBQVEsQ0FBQyxJQUFJLElBQUksZ0JBQVEsQ0FBQyxLQUFLLEVBQUU7d0JBQ2pDLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUE7cUJBQzFEO2lCQUNKO2FBQ0o7U0FDSjtRQUNELFFBQVE7UUFDUixxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsY0FBYyxFQUFFLEdBQUcsQ0FBQyxDQUFBO1FBQ25ELHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUE7UUFDMUMscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRSxHQUFHLENBQUMsQ0FBQTtRQUNwRCxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEtBQUssQ0FBQyxDQUFBO1FBQy9DLHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLENBQUE7UUFDNUMscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsQ0FBQTtRQUM1QyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxDQUFBO1FBQzVDLHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLENBQUE7UUFDOUMscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxHQUFHLENBQUMsQ0FBQTtRQUNoRCxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLEdBQUcsQ0FBQyxDQUFBO1FBQ2hELHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLENBQUE7UUFDOUMscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxHQUFHLENBQUMsQ0FBQTtRQUNqRCw0Q0FBNEM7UUFDNUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLGFBQWEsRUFBRSxDQUFBO1FBQ3JDLFVBQVU7UUFDVixJQUFNLFNBQVMsZUFBRyxJQUFJLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSwwQ0FBRSxRQUFRLG1DQUFJLENBQUMsQ0FBQyxDQUFBO1FBQzdELElBQU0sVUFBVSxHQUFHLHFCQUFTLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQTtRQUN2QyxJQUFNLGFBQWEsR0FBRyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLENBQUE7UUFDakQsUUFBUTtRQUNSLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQ2pDLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsS0FBSyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUE7WUFDbkQsSUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQTtZQUNqQixJQUFNLElBQUksR0FBRyxvQkFBTyxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDOUMsRUFBRSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUE7WUFDakMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsS0FBSyxLQUFLLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFDLFFBQVEsTUFBSyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsUUFBUSxDQUFBLElBQUksQ0FBQyxDQUFDLEtBQUssTUFBSyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsS0FBSyxDQUFBLElBQUksQ0FBQyxDQUFDLFFBQVEsTUFBSyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsUUFBUSxDQUFBLEVBQUU7Z0JBQzNILHFCQUFTLENBQUMsY0FBYyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsRUFBRSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsUUFBUSxFQUFFLEtBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtnQkFDcEYsS0FBSSxDQUFDLG9CQUFvQixDQUFDLEVBQUUsRUFBRSxJQUFJLENBQUMsQ0FBQTthQUN0QztZQUNELEVBQUUsQ0FBQyxNQUFNLEdBQUcsU0FBUyxLQUFLLEtBQUssQ0FBQTtZQUMvQixFQUFFLENBQUMsSUFBSSxHQUFHLEVBQUUsS0FBSyxPQUFBLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxLQUFLLEVBQUUsUUFBUSxFQUFFLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLEVBQUUsUUFBUSxFQUFFLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxRQUFRLEVBQUUsQ0FBQTtRQUNsSCxDQUFDLENBQUMsQ0FBQTtRQUNGLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLGFBQWEsQ0FBQTtRQUNyQyxVQUFVO1FBQ1YsSUFBSSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxZQUFZLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTtZQUMvQyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtZQUNyRCxFQUFFLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQTtZQUNqQyxLQUFJLENBQUMscUJBQXFCLENBQUMsRUFBRSxFQUFFLElBQUksQ0FBQyxDQUFBO1lBQ3BDLEVBQUUsQ0FBQyxNQUFNLEdBQUcsU0FBUyxLQUFLLEtBQUssQ0FBQTtZQUMvQixFQUFFLENBQUMsSUFBSSxHQUFHLEVBQUUsS0FBSyxPQUFBLEVBQUUsQ0FBQTtRQUN2QixDQUFDLENBQUMsQ0FBQTtRQUNGLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsYUFBYSxDQUFBO1FBQzVDLFlBQVk7UUFDWixJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTs7WUFDcEMsSUFBTSxJQUFJLEdBQWMsSUFBSSxDQUFDLE1BQU0sRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtZQUN2RCxFQUFFLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7WUFDckMsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQTtZQUNqQyxJQUFJLE9BQUEsRUFBRSxDQUFDLElBQUksMENBQUUsS0FBSyxNQUFLLEtBQUssRUFBRTtnQkFDMUIsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO2dCQUN6QyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUE7Z0JBQy9DLFFBQVEsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxXQUFXLEdBQUcsS0FBSyxDQUFDLENBQUE7Z0JBQzFELEtBQUs7Z0JBQ0wsSUFBTSxNQUFJLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLFNBQVMsQ0FBQyxDQUFBO2dCQUMzQyxJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsUUFBUSxHQUFHLFdBQVcsQ0FBQyxHQUFHLEtBQUssQ0FBQTtnQkFDcEUsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsQ0FBQTtnQkFDMUIsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFBO2dCQUNaLElBQUksV0FBVyxHQUFHLElBQUksRUFBRTtvQkFDcEIsTUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsV0FBVyxDQUFDLENBQUE7b0JBQ3JDLEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBSSxHQUFHLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxjQUFNLE9BQUEsS0FBSSxDQUFDLE9BQU8sSUFBSSxNQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUF2QyxDQUF1QyxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUE7aUJBQzlGO3FCQUFNO29CQUNILE1BQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7aUJBQzFCO2FBQ0o7WUFDRCxRQUFRLENBQUMsTUFBTSxHQUFHLGFBQWEsSUFBSSxTQUFTLEtBQUssS0FBSyxDQUFBO1lBQ3RELEVBQUUsQ0FBQyxJQUFJLEdBQUcsRUFBRSxLQUFLLE9BQUEsRUFBRSxDQUFBO1lBQ25CLEVBQUUsQ0FBQyxNQUFNLEdBQUcsVUFBVSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQTtRQUNqRCxDQUFDLENBQUMsQ0FBQTtRQUNGLFNBQVM7UUFDVCxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTs7WUFDcEMsSUFBTSxJQUFJLEdBQWMsSUFBSSxDQUFDLFVBQVUsRUFBRSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtZQUMzRCxFQUFFLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7WUFDckMsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQTtZQUNqQyxJQUFJLE9BQUEsRUFBRSxDQUFDLElBQUksMENBQUUsS0FBSyxNQUFLLEtBQUssRUFBRTtnQkFDMUIsUUFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUUsR0FBRyxLQUFLLENBQUMsQ0FBQTthQUN2RTtZQUNELFFBQVEsQ0FBQyxNQUFNLEdBQUcsYUFBYSxDQUFBO1lBQy9CLEVBQUUsQ0FBQyxJQUFJLEdBQUcsRUFBRSxLQUFLLE9BQUEsRUFBRSxDQUFBO1lBQ25CLEVBQUUsQ0FBQyxNQUFNLEdBQUcsVUFBVSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQTtRQUNqRCxDQUFDLENBQUMsQ0FBQTtJQUNOLENBQUM7SUFFRCxRQUFRO0lBQ0QsMkNBQW9CLEdBQTNCLFVBQTRCLFFBQWlCLEVBQUUsSUFBWTtRQUN2RCxJQUFJLEtBQUssR0FBRyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFBO1FBQ3ZFLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDUixFQUFFLENBQUMsS0FBSyxDQUFDLDZCQUE2QixHQUFHLElBQUksR0FBRyxZQUFZLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFBO1NBQzdGO1FBQ0QsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLGVBQWUsRUFBRSxDQUFBO1FBQ2xDLElBQUksSUFBSSxDQUFDLEtBQUssR0FBRyxvQkFBUyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDakMsUUFBUSxDQUFDLENBQUMsSUFBSSxvQkFBUyxHQUFHLENBQUMsQ0FBQTtTQUM5QjtRQUNELElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxvQkFBUyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDbEMsUUFBUSxDQUFDLENBQUMsSUFBSSxvQkFBUyxHQUFHLENBQUMsQ0FBQTtTQUM5QjtJQUNMLENBQUM7SUFFRCxzQ0FBc0M7SUFDOUIscUNBQWMsR0FBdEIsVUFBdUIsS0FBYyxFQUFFLElBQVksRUFBRSxJQUFZLEVBQUUsSUFBWSxFQUFFLElBQVk7UUFDekYsSUFBSSxLQUFLLENBQUMsQ0FBQyxHQUFHLElBQUksRUFBRTtZQUNoQixPQUFPLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7U0FDdkQ7YUFBTSxJQUFJLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxFQUFFO1lBQ3ZCLE9BQU8sS0FBSyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1NBQ2hDO1FBQ0QsT0FBTyxLQUFLLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO0lBQ3hELENBQUM7SUFFTyxpQ0FBVSxHQUFsQixVQUFtQixFQUFXLEVBQUUsSUFBWSxFQUFFLEtBQWMsRUFBRSxJQUFZLEVBQUUsSUFBWSxFQUFFLElBQVksRUFBRSxJQUFZO1FBQ2hILElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQzlELEVBQUUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFJLElBQUksU0FBSSxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUcsQ0FBQyxDQUFBO0lBQ3pGLENBQUM7SUFFRCxLQUFLO0lBQ0cscUNBQWMsR0FBdEIsVUFBdUIsUUFBaUIsRUFBRSxLQUFjLEVBQUUsSUFBWSxFQUFFLElBQVksRUFBRSxJQUFZLEVBQUUsSUFBWTtRQUM1RyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFLEtBQUssR0FBRyxHQUFHLEdBQUcsQ0FBQyxDQUFBO1FBQzdHLElBQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxRQUFRLENBQUMsRUFBRSxFQUFFLFFBQVEsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUNoRSxFQUFFLENBQUMsS0FBSyxHQUFHLEtBQUssR0FBRyxDQUFDLEVBQUUsQ0FBQTtJQUMxQixDQUFDO0lBRU8sa0NBQVcsR0FBbkIsVUFBb0IsSUFBWTtRQUM1QixPQUFPLHFCQUFTLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ3RDLENBQUM7SUFFTywyQ0FBb0IsR0FBNUIsVUFBNkIsRUFBVyxFQUFFLElBQWdCOztRQUN0RCxJQUFNLE9BQU8sR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDOUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxPQUFDLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxRQUFRLG1DQUFJLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQTtRQUM1RCxJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDakQsSUFBSSxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsRUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsS0FBSyxDQUFBLENBQUMsRUFBRTtZQUNuQyxJQUFNLElBQUksR0FBRyxTQUFTLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDdkQsUUFBUSxDQUFDLEtBQUssQ0FBQyw0QkFBaUIsQ0FBQyxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxPQUFPLEtBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsWUFBWSxJQUFHLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxFQUFFLENBQUEsQ0FBQyxDQUFBO1lBQzNGLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFBO1NBQ3ZCO2FBQU07WUFDSCxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUE7U0FDckI7SUFDTCxDQUFDO0lBRU8sNENBQXFCLEdBQTdCLFVBQThCLEVBQVcsRUFBRSxJQUFnQjtRQUN2RCxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxzQkFBc0IsRUFBRSxJQUFJLENBQUMsSUFBSSxFQUFFLFNBQVMsQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUM3RyxJQUFJLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNoRSxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUUsR0FBRyxLQUFLLENBQUMsQ0FBQTtTQUNyRTtJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ0gsNENBQXFCLEdBQTdCLFVBQThCLEtBQWtCO1FBQWxCLHNCQUFBLEVBQUEsU0FBaUIsQ0FBQztRQUM1QyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxLQUFLLEtBQUssRUFBN0MsQ0FBNkMsQ0FBQyxDQUFBO1FBQ25GLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssS0FBSyxLQUFLLEVBQTdDLENBQTZDLENBQUMsQ0FBQTtRQUMxRixJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLEtBQUssS0FBSyxDQUFDLEVBQTNELENBQTJELENBQUMsQ0FBQTtJQUN0RyxDQUFDO0lBRUQsUUFBUTtJQUNBLHFDQUFjLEdBQXRCO1FBQUEsaUJBaUJDO1FBaEJHLElBQU0sT0FBTyxHQUFZLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUN4QyxJQUFNLE9BQU8sR0FBWSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1FBQ3ZDLElBQU0sS0FBSyxHQUFVLEVBQUUsRUFBRSxXQUFXLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUNuRSxLQUFLLElBQUksR0FBRyxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUU7WUFDbEMsSUFBTSxJQUFJLEdBQWUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUNsRCxJQUFJLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxFQUFFO2dCQUMxQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLE1BQU0sRUFBRSxPQUFPLEVBQUUsSUFBSSxFQUFFLGlCQUFpQixFQUFFLENBQUMsQ0FBQTthQUM5RjtZQUNELElBQUksV0FBVyxDQUFDLEdBQUcsQ0FBQyxFQUFFO2dCQUNsQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRLEVBQUUsTUFBTSxFQUFFLE9BQU8sRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLENBQUMsQ0FBQTthQUNsRjtTQUNKO1FBQ0QsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLFVBQUMsRUFBRSxFQUFFLElBQUk7WUFDaEMsRUFBRSxDQUFDLFdBQVcsQ0FBQyxLQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFBO1lBQ3pFLEVBQUUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxxQkFBUyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDMUUsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsU0FBUztJQUNELHFDQUFjLEdBQXRCLFVBQXVCLElBQWdCO1FBQ25DLElBQUksQ0FBQyxJQUFJLElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSSxnQkFBUSxDQUFDLEdBQUcsSUFBSSxJQUFJLENBQUMsUUFBUSxJQUFJLGdCQUFRLENBQUMsS0FBSyxFQUFFO1lBQzNFLE9BQU07U0FDVDthQUFNLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ3JDLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtTQUNyRDtRQUNELElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUE7UUFDeEQsSUFBSSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsd0JBQWMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUE7UUFDeEUsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQ2pDLFNBQVM7UUFDVCxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO0lBQzdDLENBQUM7SUFFRCxLQUFLO0lBQ0cscUNBQWMsR0FBdEIsVUFBdUIsSUFBb0I7UUFBcEIscUJBQUEsRUFBQSxXQUFvQjtRQUN2QyxJQUFJLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxFQUFFO1lBQzNCLElBQUksQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLHdCQUFjLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtZQUN0RCxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUM3QixJQUFJLENBQUMscUJBQXFCLEVBQUUsQ0FBQTtTQUMvQjtJQUNMLENBQUM7SUFFRCxRQUFRO0lBQ0EsZ0NBQVMsR0FBakI7UUFBQSxpQkFRQztRQVBHLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTtRQUNqQixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBRSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxjQUFjLEVBQUUsRUFBbEIsQ0FBa0IsQ0FBQyxDQUFBO1FBQ3RFLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQ3JDLElBQU0sS0FBSyxHQUFHLEtBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxTQUFTLENBQUMsbUJBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsS0FBSSxDQUFDLGNBQWMsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQTtZQUNoRyxLQUFLLENBQUMsaUJBQWlCLEdBQUcsS0FBSyxDQUFBO1FBQ25DLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsQ0FBQyxpQkFBaUIsSUFBSSxLQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDLEVBQTlELENBQThELENBQUMsQ0FBQTtJQUM1RixDQUFDO0lBRU8saUNBQVUsR0FBbEI7UUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUMzQixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsRUFBRSxDQUFDLEtBQUssRUFBRSxDQUFBO1NBQzVCO1FBQ0QsSUFBSSxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUE7UUFDaEIsK0VBQStFO1FBQy9FLElBQUksQ0FBQyxjQUFjLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTtJQUMzQyxDQUFDO0lBRUQsVUFBVTtJQUNGLDJDQUFvQixHQUE1QixVQUE2QixJQUFrQjtRQUMzQyxJQUFNLE1BQU0sR0FBZ0IsRUFBRSxDQUFBO1FBQzlCLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUNqQixJQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUE7WUFDckIsSUFBSSxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDLEVBQUU7Z0JBQzdCLENBQUMsQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFBO2dCQUMzRCxDQUFDLENBQUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFBO2dCQUMxQixNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQ2pCO1FBQ0wsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFNLEdBQUcsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFBO1FBQ3pCLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQyxJQUFLLE9BQUEsQ0FBQyxDQUFDLGdCQUFnQixDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBMUIsQ0FBMEIsQ0FBQyxDQUFBO0lBQ3hELENBQUM7SUFFRCxPQUFPO0lBQ08saUNBQVUsR0FBeEIsVUFBeUIsS0FBYTs7Ozs7Ozt3QkFDbEMsSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7NEJBQ3hCLHNCQUFNO3lCQUNUO3dCQUNELElBQUksQ0FBQyxpQkFBaUIsR0FBRyxJQUFJLENBQUE7d0JBQ08scUJBQU0sSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBQTs7d0JBQTNFLEtBQThCLFNBQTZDLEVBQXpFLEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQSxFQUFFLFlBQVksa0JBQUE7d0JBQy9CLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxLQUFLLENBQUE7d0JBQzlCLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFOzRCQUNoQixzQkFBTTt5QkFDVDs2QkFBTSxJQUFJLEdBQUcsS0FBSyxhQUFLLENBQUMsa0JBQWtCLEVBQUU7NEJBQ3pDLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7NEJBQzFCLHNCQUFPLHVCQUFVLENBQUMsY0FBYyxDQUFDLDJCQUEyQixDQUFDLEVBQUE7eUJBQ2hFOzZCQUFNLElBQUksR0FBRyxLQUFLLGFBQUssQ0FBQywwQkFBMEIsRUFBRSxFQUFFLGFBQWE7NEJBQ2hFLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7NEJBQzFCLHNCQUFPLHVCQUFVLENBQUMsY0FBYyxDQUFDLG1DQUFtQyxDQUFDLEVBQUE7eUJBQ3hFOzZCQUFNLElBQUksR0FBRyxFQUFFOzRCQUNaLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxFQUFBO3lCQUNuQzs2QkFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFOzRCQUMxQixzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxFQUFBO3lCQUNyRDt3QkFDRCx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxpQkFBaUIsRUFBRSxRQUFRLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsVUFBQyxLQUFzQixFQUFFLFdBQW9CLEVBQUUsWUFBb0I7NEJBQzFJLElBQUksS0FBSSxDQUFDLFFBQVEsRUFBRTtnQ0FDZixLQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFBO2dDQUMxQixLQUFJLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxLQUFLLEVBQUUsS0FBSyxFQUFFLFlBQVksRUFBRSxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQSxHQUFHLElBQUksT0FBQSxHQUFHLElBQUksdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQWhDLENBQWdDLENBQUMsQ0FBQTs2QkFDL0c7d0JBQ0wsQ0FBQyxDQUFDLENBQUE7Ozs7O0tBQ0w7SUFFRCxTQUFTO0lBQ0ssaUNBQVUsR0FBeEIsVUFBeUIsS0FBYTs7Ozs7Ozt3QkFDbEMsSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7NEJBQ3hCLHNCQUFNO3lCQUNUO3dCQUNELElBQUksQ0FBQyxpQkFBaUIsR0FBRyxJQUFJLENBQUE7d0JBQ08scUJBQU0sSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBQTs7d0JBQTNFLEtBQThCLFNBQTZDLEVBQXpFLEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQSxFQUFFLFlBQVksa0JBQUE7d0JBQy9CLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxLQUFLLENBQUE7d0JBQzlCLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFOzRCQUNoQixzQkFBTTt5QkFDVDs2QkFBTSxJQUFJLEdBQUcsS0FBSyxhQUFLLENBQUMsU0FBUyxFQUFFOzRCQUNoQyxzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyw0QkFBNEIsQ0FBQyxFQUFBO3lCQUM1RDs2QkFBTSxJQUFJLEdBQUcsRUFBRTs0QkFDWixzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBQTt5QkFDbkM7NkJBQU0sSUFBSSxJQUFJLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTs0QkFDMUIsc0JBQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMscUJBQXFCLENBQUMsRUFBQTt5QkFDckQ7d0JBQ0QsdUJBQVUsQ0FBQyxPQUFPLENBQUMsdUJBQXVCLEVBQUUsS0FBSyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsVUFBQyxJQUFtQjs0QkFDdkYsSUFBSSxLQUFJLENBQUMsUUFBUSxFQUFFO2dDQUNmLEtBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7Z0NBQzFCLEtBQUksQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLElBQUksRUFBRSxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQSxHQUFHLElBQUksT0FBQSxHQUFHLElBQUksdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQWhDLENBQWdDLENBQUMsQ0FBQTs2QkFDbkY7d0JBQ0wsQ0FBQyxDQUFDLENBQUE7Ozs7O0tBQ0w7SUFFRCxPQUFPO0lBQ08sbUNBQVksR0FBMUIsVUFBMkIsS0FBYTs7Ozs7NEJBQ3hCLHFCQUFNLElBQUksQ0FBQyxLQUFLLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxFQUFBOzt3QkFBMUMsR0FBRyxHQUFHLFNBQW9DO3dCQUNoRCxJQUFJLEdBQUcsRUFBRTs0QkFDTCxzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBQTt5QkFDbkM7NkJBQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFOzRCQUN0QixJQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFBO3lCQUM3Qjs7Ozs7S0FDSjtJQUVELFFBQVE7SUFDTSxpQ0FBVSxHQUF4QixVQUF5QixLQUFhOzs7Ozs7O3dCQUNsQyxJQUFJLElBQUksQ0FBQyxpQkFBaUIsRUFBRTs0QkFDeEIsc0JBQU07eUJBQ1Q7d0JBQ0QsSUFBSSxDQUFDLGlCQUFpQixHQUFHLElBQUksQ0FBQTt3QkFDTyxxQkFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEVBQUE7O3dCQUF4RSxLQUE4QixTQUEwQyxFQUF0RSxHQUFHLFNBQUEsRUFBRSxJQUFJLFVBQUEsRUFBRSxZQUFZLGtCQUFBO3dCQUMvQixJQUFJLENBQUMsaUJBQWlCLEdBQUcsS0FBSyxDQUFBO3dCQUM5QixJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRTs0QkFDaEIsc0JBQU07eUJBQ1Q7NkJBQU0sSUFBSSxHQUFHLEVBQUU7NEJBQ1osc0JBQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQUE7eUJBQ25DOzZCQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7NEJBQzFCLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLHFCQUFxQixDQUFDLEVBQUE7eUJBQ3JEO3dCQUNELHVCQUFVLENBQUMsT0FBTyxDQUFDLGlCQUFpQixFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxVQUFDLEtBQXNCLEVBQUUsV0FBb0I7NEJBQ2xILElBQUksS0FBSSxDQUFDLFFBQVEsRUFBRTtnQ0FDZixLQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFBO2dDQUMxQixLQUFJLENBQUMsS0FBSyxDQUFDLFlBQVksQ0FBQyxLQUFLLEVBQUUsS0FBSyxFQUFFLFdBQVcsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFBLEdBQUcsSUFBSSxPQUFBLEdBQUcsSUFBSSx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBaEMsQ0FBZ0MsQ0FBQyxDQUFBOzZCQUNuRzt3QkFDTCxDQUFDLENBQUMsQ0FBQTs7Ozs7S0FDTDtJQUVELFdBQVc7SUFDSCx3Q0FBaUIsR0FBekI7UUFBQSxpQkFZQztRQVhHLElBQUksQ0FBQyxLQUFLLENBQUMsa0JBQWtCLEVBQUUsQ0FBQyxPQUFPLENBQUMsVUFBQSxLQUFLO1lBQ3pDLElBQU0sSUFBSSxHQUFHLEtBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDeEMsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBTSxNQUFJLEdBQUcsSUFBSSxDQUFDLFVBQVUsRUFBRSxJQUFJLEVBQUUsRUFBRSxJQUFJLEdBQUcsMEJBQWUsQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsTUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFULENBQVMsQ0FBQyxDQUFBO2dCQUNuRixJQUFNLEtBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLFFBQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTtnQkFDdEQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxVQUFDLEdBQUcsRUFBRSxDQUFDLElBQUssT0FBQSx1QkFBVSxDQUFDLGtCQUFrQixDQUFDLEdBQUcsRUFBRSxNQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsR0FBRyxHQUFHLENBQUMsR0FBRyxHQUFHLEVBQUUsUUFBTSxFQUFFLEtBQUksQ0FBQyxhQUFhLEVBQUUsS0FBRyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsRUFBdkcsQ0FBdUcsQ0FBQyxDQUFBO2dCQUNqSSx1QkFBVSxDQUFDLGlCQUFpQixDQUFDLEtBQUksQ0FBQyxlQUFlLEVBQUUsS0FBRyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtnQkFDakUsUUFBUTtnQkFDUixLQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFBLEtBQUssSUFBSSxPQUFBLEtBQUssQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLElBQUksS0FBSyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBMUMsQ0FBMEMsQ0FBQyxDQUFBO2FBQzNFO1FBQ0wsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRUQsY0FBYztJQUNOLDJDQUFvQixHQUE1QjtRQUFBLGlCQXFCQztRQXBCRyxJQUFJLENBQUMsS0FBSyxDQUFDLHFCQUFxQixFQUFFLENBQUMsT0FBTyxDQUFDLFVBQUEsSUFBSTs7WUFDM0MsSUFBTSxJQUFJLEdBQUcsS0FBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDN0MsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQTtnQkFDNUIsSUFBTSxLQUFHLEdBQUcsRUFBRSxDQUFBO2dCQUNkLE1BQUEsSUFBSSxDQUFDLFdBQVcsMENBQUUsT0FBTyxDQUFDLFVBQUEsRUFBRTtvQkFDeEIsSUFBSSxLQUFLLEdBQUcsS0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFBO29CQUNuQixJQUFJLENBQUMsS0FBSyxFQUFFO3dCQUNSLElBQU0sSUFBSSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxDQUFBO3dCQUNsRCxLQUFLLEdBQUcsS0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsV0FBVyxHQUFHLENBQUMsQ0FBQSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsRUFBRSxLQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksRUFBRSxDQUFBO3FCQUM3RTtvQkFDRCxLQUFLLENBQUMsS0FBSyxJQUFJLENBQUMsQ0FBQTtnQkFDcEIsQ0FBQyxFQUFDO2dCQUNGLEtBQUssSUFBSSxHQUFHLElBQUksS0FBRyxFQUFFO29CQUNqQixJQUFNLE1BQUksR0FBRyxLQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7b0JBQ3JCLHVCQUFVLENBQUMsbUJBQW1CLENBQUMsTUFBSSxDQUFDLElBQUksRUFBRSxNQUFJLENBQUMsS0FBSyxFQUFFLEtBQUksQ0FBQyxhQUFhLEVBQUUsR0FBRyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtpQkFDM0Y7Z0JBQ0QsdUJBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxLQUFJLENBQUMsZUFBZSxFQUFFLEdBQUcsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDcEU7UUFDTCxDQUFDLENBQUMsQ0FBQTtJQUNOLENBQUM7SUFFRCxTQUFTO0lBQ0Qsb0NBQWEsR0FBckI7UUFBQSxpQkF3QkM7UUF2QkcsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFBO1FBQ3RCLG9CQUFPLENBQUMsTUFBTSxDQUFDLGFBQWEsRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7WUFDcEMsSUFBTSxJQUFJLEdBQUcsS0FBSSxDQUFDLGVBQWUsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEVBQUUsSUFBSSxHQUFHLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQTtZQUNwRyxJQUFJLElBQUksSUFBSSxJQUFJLEVBQUU7Z0JBQ2QsSUFBSSxJQUFJLEdBQVksS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUE7Z0JBQzlDLElBQUksSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLE9BQU8sRUFBRTtvQkFDZixJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxFQUFFLElBQUksT0FBQSxXQUFXLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFuQixDQUFtQixDQUFDLENBQUE7b0JBQzlELElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtpQkFDakI7Z0JBQ0QsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFBO2dCQUN4RCxJQUFJLEdBQUcsS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxJQUFJLEVBQUUsS0FBSSxDQUFDLGNBQWMsQ0FBQyxDQUFBO2dCQUM5RSxJQUFJLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxLQUFLLENBQUE7Z0JBQ25CLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFBO2dCQUNsQyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBO2dCQUMxQix1QkFBVSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsR0FBRyxFQUFFLFNBQVMsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQztvQkFDdEUsSUFBSSxLQUFJLENBQUMsT0FBTyxLQUFJLENBQUMsYUFBRCxDQUFDLHVCQUFELENBQUMsQ0FBRSxPQUFPLENBQUEsRUFBRTt3QkFDNUIsT0FBTyxLQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQTt3QkFDaEMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsRUFBRSxJQUFJLE9BQUEsV0FBVyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO3dCQUMzRCxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUE7cUJBQ2Q7Z0JBQ0wsQ0FBQyxDQUFDLENBQUE7YUFDTDtRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVPLHNDQUFlLEdBQXZCLFVBQXdCLENBQVMsRUFBRSxDQUFTLEVBQUUsSUFBYyxFQUFFLEtBQWE7UUFBM0UsaUJBU0M7UUFSRyxnQ0FBZ0M7UUFDaEMsMEVBQTBFO1FBQzFFLHlGQUF5RjtRQUN6Riw2RUFBNkU7UUFDN0UsSUFBSTtRQUNKLElBQU0sR0FBRyxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxFQUFFLEVBQUUsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO1FBQ3BGLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBQyxHQUFHLEVBQUUsQ0FBQyxJQUFLLE9BQUEsdUJBQVUsQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsS0FBSyxFQUFFLE1BQU0sRUFBRSxLQUFJLENBQUMsYUFBYSxFQUFFLEdBQUcsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLEVBQWpHLENBQWlHLENBQUMsQ0FBQTtRQUMzSCx1QkFBVSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxlQUFlLEVBQUUsR0FBRyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtJQUNyRSxDQUFDO0lBRUQsT0FBTztJQUNPLGtDQUFXLEdBQXpCOzs7Ozs7O3dCQUNVLE9BQU8sU0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLDJCQUEyQixDQUFDLHFCQUFhLENBQUMscUJBQXFCLENBQUMsbUNBQUksQ0FBQyxDQUFDLENBQUE7d0JBQ2hHLElBQUksQ0FBQyxVQUFVLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTs2QkFFM0MsQ0FBQSxPQUFPLEtBQUssSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLEVBQUUsQ0FBQSxFQUF0Qyx3QkFBc0M7d0JBQ3RDLHFCQUFNLHVCQUFVLENBQUMsVUFBVSxDQUFDLG1CQUFtQixDQUFDLEVBQUE7O3dCQUFoRCxTQUFnRCxDQUFBOzs7Ozs7S0FFdkQ7SUFFRCxPQUFPO0lBQ0MsOEJBQU8sR0FBZjtRQUFBLGlCQWNDOztRQWJHLFNBQVM7UUFDVCx1QkFBVSxDQUFDLFVBQVUsQ0FBQywwQkFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFBO1FBQ3ZELFNBQVM7UUFDVCxJQUFJLHFCQUFTLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRTtZQUMxQyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQTtTQUN6QzthQUFNO1lBQ0gscUJBQVMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLElBQUksQ0FBQyxjQUFNLE9BQUEsS0FBSSxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsU0FBUyxDQUFDLEtBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFFLENBQUMsRUFBdEQsQ0FBc0QsQ0FBQyxDQUFBO1NBQzdHO1FBQ0QsYUFBYTtRQUNiLElBQU0sT0FBTyxTQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsMkJBQTJCLENBQUMscUJBQWEsQ0FBQyxxQkFBcUIsQ0FBQyxtQ0FBSSxDQUFDLENBQUMsQ0FBQTtRQUNoRyxJQUFJLE9BQU8sS0FBSyxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsRUFBRSxFQUFFO1lBQ3hDLHVCQUFVLENBQUMsT0FBTyxDQUFDLG1CQUFtQixFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ2hEO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDSyx1Q0FBZ0IsR0FBOUIsVUFBK0IsSUFBWTs7Ozs7d0JBQ3ZDLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFBO3dCQUN0QixJQUFJLENBQUMsSUFBSSxDQUFDLDJCQUEyQixDQUFDLHFCQUFhLENBQUMscUJBQXFCLEVBQUUsSUFBSSxDQUFDLENBQUE7d0JBQ2hGLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7d0JBQzFCLHFCQUFNLHFCQUFTLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFBOzt3QkFBbEMsU0FBa0MsQ0FBQTt3QkFDbEMsSUFBSSxDQUFDLHdCQUF3QixFQUFFLENBQUE7d0JBQy9CLFNBQVM7d0JBQ1QsdUJBQVUsQ0FBQyxVQUFVLENBQUMsMEJBQWUsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQTt3QkFDdkQsU0FBUzt3QkFDVCxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTt3QkFDM0IscUJBQVMsQ0FBQyxhQUFhLEVBQUUsQ0FBQTt3QkFDekIsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLHNCQUFzQixDQUFDLENBQUE7Ozs7O0tBQzlDO0lBRUQsNkJBQU0sR0FBTixVQUFPLEVBQVU7O1FBQ2IsRUFBRTtRQUNGLE1BQUEsSUFBSSxDQUFDLG1CQUFtQiwwQ0FBRSxNQUFNLENBQUMsRUFBRSxFQUFDO1FBQ3BDLGFBQWE7UUFDYixJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDckIsWUFBWTtRQUNaLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFBO0lBQzdCLENBQUM7SUFFTyxxQ0FBYyxHQUF0QjtRQUNJLElBQU0sS0FBSyxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLHVCQUFVLENBQUMsaUJBQWlCLEVBQUUsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7UUFDMUYsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3pGLElBQUksSUFBSSxJQUFJLHlCQUFjLEdBQUcsQ0FBQyxJQUFJLElBQUksQ0FBQyxrQkFBa0IsS0FBSyx1QkFBVSxDQUFDLFNBQVMsRUFBRTtZQUNoRixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQ3JCLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO1NBQ2hDO0lBQ0wsQ0FBQztJQUVELGtCQUFrQjtJQUNWLDZDQUFzQixHQUE5Qjs7UUFDSSxJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUE7UUFDakIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO1lBQ2pCLENBQUMsQ0FBQyxtQkFBbUIsRUFBRSxDQUFBO1lBQ3ZCLE1BQU0sQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFBO1FBQ3hCLENBQUMsQ0FBQyxDQUFBO1FBQ0YsaUJBQWlCO1FBQ2pCLEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxhQUFhLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDN0QsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDNUMsSUFBSSxDQUFDLE1BQU0sT0FBQyxJQUFJLENBQUMsSUFBSSwwQ0FBRSxHQUFHLENBQUMsRUFBRTtnQkFDekIsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO2FBQ2pCO1NBQ0o7SUFDTCxDQUFDO0lBRU8seUNBQWtCLEdBQTFCOztRQUNJLElBQU0sUUFBUSxHQUFHLHVCQUFVLENBQUMsV0FBVyxFQUFFLENBQUE7UUFDekMsSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQ3pDLE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUE7UUFDcEMsUUFBUTtRQUNSLFVBQUksSUFBSSxDQUFDLFlBQVksMENBQUUscUJBQXFCLElBQUk7WUFDNUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUM3QjtJQUNMLENBQUM7SUE5dkNnQixZQUFZO1FBRGhDLE9BQU87T0FDYSxZQUFZLENBK3ZDaEM7SUFBRCxtQkFBQztDQS92Q0QsQUErdkNDLENBL3ZDeUMsRUFBRSxDQUFDLFlBQVksR0ErdkN4RDtrQkEvdkNvQixZQUFZIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FtZXJhQ3RybCB9IGZyb20gXCIuLi8uLi9jb21tb24vY2FtZXJhL0NhbWVyYUN0cmxcIjtcbmltcG9ydCB7IEFDSElFVkVNRU5UX0NPTE9SLCBDQU1FUkFfQkdfQ09MT1IsIENFTExfUkVTX0ZJRUxEUywgQ0lUWV9GT1JUX05JRCwgQ0lUWV9NQUlOX05JRCwgQ1RZUEVfSUNPTiwgTUFQX0VYVFJBX1NJWkUsIE1BUF9NQVNLX0lURU1fQ09MT1IsIE1BUF9NQVNLX0lURU1fT1BBQ0lUWSwgTUFQX01BU0tfSVRFTV9PUEFDSVRZX0dPTEQsIE1BUF9TSE9XX09GRlNFVCwgU0VMRUNUX0NFTExfSU5GT19CT1gsIFRJTEVfU0laRSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcbmltcG9ydCB7IEFybXlTaG9ydEluZm8sIFBsYXllckluZm8gfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCB7IERlY29yYXRpb25UeXBlLCBMYW5kVHlwZSwgUHJlZmVyZW5jZUtleSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIjtcbmltcG9ydCBFdmVudFR5cGUgZnJvbSBcIi4uLy4uL2NvbW1vbi9ldmVudC9FdmVudFR5cGVcIjtcbmltcG9ydCBOZXRFdmVudCBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L05ldEV2ZW50XCI7XG5pbXBvcnQgeyBhbmltSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvQW5pbUhlbHBlclwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IGd1aWRlSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR3VpZGVIZWxwZXJcIjtcbmltcG9ydCB7IG1hcEhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL01hcEhlbHBlclwiO1xuaW1wb3J0IHsgbWFwVWlvbkZpbmRIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9NYXBVaW9uRmluZEhlbHBlclwiO1xuaW1wb3J0IHsgcmVzSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvUmVzSGVscGVyXCI7XG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xuaW1wb3J0IEJ1aWxkT2JqIGZyb20gXCIuLi8uLi9tb2RlbC9hcmVhL0J1aWxkT2JqXCI7XG5pbXBvcnQgQ1R5cGVPYmogZnJvbSBcIi4uLy4uL21vZGVsL2NvbW1vbi9DVHlwZU9ialwiO1xuaW1wb3J0IFVzZXJNb2RlbCBmcm9tIFwiLi4vLi4vbW9kZWwvY29tbW9uL1VzZXJNb2RlbFwiO1xuaW1wb3J0IEFuY2llbnRPYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vQW5jaWVudE9ialwiO1xuaW1wb3J0IEJhc2VNYXJjaE9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvbWFpbi9CYXNlTWFyY2hPYmpcIjtcbmltcG9ydCBCVENpdHlPYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vQlRDaXR5T2JqXCI7XG5pbXBvcnQgTWFwQ2VsbE9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvbWFpbi9NYXBDZWxsT2JqXCI7XG5pbXBvcnQgUGxheWVyTW9kZWwgZnJvbSBcIi4uLy4uL21vZGVsL21haW4vUGxheWVyTW9kZWxcIjtcbmltcG9ydCBUb25kZW5PYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vVG9uZGVuT2JqXCI7XG5pbXBvcnQgV29ybGRNb2RlbCBmcm9tIFwiLi4vLi4vbW9kZWwvbWFpbi9Xb3JsZE1vZGVsXCI7XG5pbXBvcnQgTWFwVG91Y2hDbXB0IGZyb20gXCIuLi9jbXB0L01hcFRvdWNoQ21wdFwiO1xuaW1wb3J0IFNlbGVjdENlbGxDbXB0IGZyb20gXCIuLi9jbXB0L1NlbGVjdENlbGxDbXB0XCI7XG5pbXBvcnQgQ2VsbEluZm9DbXB0IGZyb20gXCIuL0NlbGxJbmZvQ21wdFwiO1xuaW1wb3J0IE1hcEFuaW1Ob2RlUG9vbCBmcm9tIFwiLi9NYXBBbmltTm9kZVBvb2xcIjtcbmltcG9ydCBNYXJjaENtcHQgZnJvbSBcIi4vTWFyY2hDbXB0XCI7XG5pbXBvcnQgU2NlbmVFZmZlY3RDbXB0IGZyb20gXCIuL1NjZW5lRWZmZWN0Q21wdFwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBNYWluV2luZEN0cmwgZXh0ZW5kcyBtYy5CYXNlV2luZEN0cmwge1xuXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cbiAgICBwcml2YXRlIG1hcE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9tYXBfblxuICAgIHByaXZhdGUgY2VsbEVmZmVjdE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9tYXBfbi9jZWxsX2VmZmVjdF9uXG4gICAgcHJpdmF0ZSBzZWxlY3RDZWxsTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L3NlbGVjdF9jZWxsX25cbiAgICBwcml2YXRlIHRleHROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvdGV4dF9uXG4gICAgcHJpdmF0ZSBhbmNpZW50VGV4dE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9hbmNpZW50X3RleHRfblxuICAgIHByaXZhdGUgbWFyY2hMaW5lTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L21hcmNoL21hcmNoX2xpbmVfblxuICAgIHByaXZhdGUgbWFyY2hSb2xlTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L21hcmNoL21hcmNoX3JvbGVfblxuICAgIHByaXZhdGUgY2VsbEVtb2ppTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2NlbGxfZW1vamlfblxuICAgIHByaXZhdGUgc2NlbmVFZmZlY3ROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Qvc2NlbmVfZWZmZWN0X25cbiAgICBwcml2YXRlIHRvcExheWVyTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L3RvcF9sYXllcl9uXG4gICAgcHJpdmF0ZSB3ZWFrR3VpZGVOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Qvd2Vha19ndWlkZV9uXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIHJlYWRvbmx5IElOSVRfS0VZID0gJ19pbml0X21haW5fJ1xuXG4gICAgcHJpdmF0ZSBkaU5vZGU6IGNjLk5vZGUgPSBudWxsLy/oo4XppbAt5Zyw5Z2X5LiL6Z2iXG4gICAgcHJpdmF0ZSBkZWNvcmF0aW9uTm9kZTogY2MuTm9kZSA9IG51bGwvL+ijhemlsC3lnLDlnZfkuIrpnaJcbiAgICBwcml2YXRlIG1vdW50YWluTm9kZTogY2MuTm9kZSA9IG51bGwvL+WxseiEiS3pga7nvankuIrpnaJcbiAgICBwcml2YXRlIHByb3RlY3RMaW5lTm9kZTogY2MuTm9kZSA9IG51bGwgLy/kv53miqTnur9cbiAgICBwcml2YXRlIGxpbmVOb2RlOiBjYy5Ob2RlID0gbnVsbFxuICAgIHByaXZhdGUgbGFuZE5vZGU6IGNjLk5vZGUgPSBudWxsXG4gICAgcHJpdmF0ZSBjaXR5Tm9kZTogY2MuTm9kZSA9IG51bGwgLy/ln47luILlsYJcbiAgICBwcml2YXRlIG1hc2tOb2RlOiBjYy5Ob2RlID0gbnVsbCAvL+mBrue9qeWxglxuICAgIHByaXZhdGUgYnRpbmZvTm9kZTogY2MuTm9kZSA9IG51bGwgLy/kv67lu7rkv6Hmga/lsYJcbiAgICBwcml2YXRlIG91dHB1dE5vZGU6IGNjLk5vZGUgPSBudWxsIC8v5Lqn5Ye65bGCXG4gICAgcHJpdmF0ZSBpY29uTm9kZTogY2MuTm9kZSA9IG51bGwgLy/lsI/lm77moIflsYJcbiAgICBwcml2YXRlIHRvbmRlbk5vZGU6IGNjLk5vZGUgPSBudWxsIC8v5bGv55Sw5Lit5Zu+5qCH5bGCXG4gICAgcHJpdmF0ZSBiYXR0bGVOb2RlOiBjYy5Ob2RlID0gbnVsbCAvL+aImOaWl+S4reWbvuagh+WxglxuICAgIHByaXZhdGUgbWFwRmxhZ05vZGU6IGNjLk5vZGUgPSBudWxsIC8v6IGU55uf5qCH6K6wXG4gICAgcHJpdmF0ZSBtYXBNYXJrTm9kZTogY2MuTm9kZSA9IG51bGwgLy/kuKrkurrmoIforrBcbiAgICBwcml2YXRlIGNlbGxJbmZvQ21wdDogQ2VsbEluZm9DbXB0ID0gbnVsbFxuICAgIHByaXZhdGUgdG91Y2hDbXB0OiBNYXBUb3VjaENtcHQgPSBudWxsXG4gICAgcHJpdmF0ZSBzY2VuZUVmZmVjdDogU2NlbmVFZmZlY3RDbXB0ID0gbnVsbFxuICAgIHByaXZhdGUgY2VsbEVtb2ppSXRlbU1hcDogeyBbdHlwZTogbnVtYmVyXTogY2MuTm9kZSB9ID0ge31cbiAgICBwcml2YXRlIHNlYXNvblR5cGU6IG51bWJlciA9IDBcblxuICAgIHByaXZhdGUgbW9kZWw6IFdvcmxkTW9kZWwgPSBudWxsXG4gICAgcHJpdmF0ZSB1c2VyOiBVc2VyTW9kZWwgPSBudWxsXG4gICAgcHJpdmF0ZSBwbGF5ZXI6IFBsYXllck1vZGVsID0gbnVsbFxuICAgIHByaXZhdGUgY2VudHJlOiBjYy5WZWMyID0gY2MudjIoKSAvL+W9k+WJjeeahOS4reW/g+S9jee9rlxuICAgIHByaXZhdGUgcHJlQ2FtZXJhWm9vbVJhdGlvOiBudW1iZXIgPSAwXG4gICAgcHJpdmF0ZSBwcmVDYW1lcmFQb3NpdGlvbjogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIG1hcmNoczogTWFyY2hDbXB0W10gPSBbXSAvL+W9k+WJjeaJgOacieihjOWGm1xuICAgIHByaXZhdGUgdGVtcFNob3dDZWxsTWFwOiB7IFtrZXk6IG51bWJlcl06IE1hcENlbGxPYmogfSA9IHt9IC8v5b2T5YmN5bGP5bmV5pi+56S655qE5Zyw5Z2X5L+h5oGvXG4gICAgcHJpdmF0ZSByZXFTZWxlY3RBcm15c2luZzogYm9vbGVhbiA9IGZhbHNlIC8v5b2T5YmN5piv5ZCm6K+35rGC5Yab6Zif5YiX6KGo5LitXG4gICAgcHJpdmF0ZSBjZWxsRW1vamlNYXA6IGFueSA9IHt9IC8v5b2T5YmN55qE6aKG5Zyw6KGo5oOFbWFwXG4gICAgcHJpdmF0ZSBjaXR5QW5pbU5vZGVQb29sOiBNYXBBbmltTm9kZVBvb2wgPSBudWxsIC8v5Z+O5biC6IqC54K5566h55CGXG4gICAgcHJpdmF0ZSBzZWF3YXZlQW5pbU5vZGVQb29sOiBNYXBBbmltTm9kZVBvb2wgPSBudWxsIC8v5rW35rWq6IqC54K5566h55CGXG5cbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMTogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMjogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMzogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfNDogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfNTogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfNjogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfNzogY2MuVmVjMiA9IGNjLnYyKClcblxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICB7IFtOZXRFdmVudC5ORVRfUkVDT05ORUNUXTogdGhpcy5vbk5ldFJlY29ubmVjdCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQ0VMTF9JTkZPXTogdGhpcy5vblVwZGF0ZUNlbGxJbmZvLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkFERF9NQVJDSF06IHRoaXMub25BZGRNYXJjaCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5SRU1PVkVfTUFSQ0hdOiB0aGlzLm9uUmVtb3ZlTWFyY2gsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FMTF9NQVJDSF06IHRoaXMub25VcGRhdGVBbGxNYXJjaCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5ISURFX1dPUkxEX1RFWFRdOiB0aGlzLm9uSGlkZVdvcmxkVGV4dCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5DTE9TRV9TRUxFQ1RfQ0VMTF06IHRoaXMub25DbG9zZVNlbGVjdENlbGwsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0JBVFRMRV9ESVNUX0lORk9dOiB0aGlzLm9uVXBkYXRlQmF0dGxlRGlzdEluZm8sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FWT0lEV0FSX0RJU1RfSU5GT106IHRoaXMub25VcGRhdGVBdm9pZFdhckRpc3RJbmZvLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9CVF9DSVRZXTogdGhpcy5vblVwZGF0ZUJ0Q2l0eSwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfVE9OREVOXTogdGhpcy5vblVwZGF0ZVRvbmRlbiwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQVJNWV9ESVNUX0lORk9dOiB0aGlzLm9uVXBkYXRlQXJteURpc3RJbmZvLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLk1BUF9NT1ZFX1RPXTogdGhpcy5vbk1hcE1vdmVUbywgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfUExBWUVSX05JQ0tOQU1FXTogdGhpcy5vblVwZGF0ZVBsYXllck5pY2tuYW1lLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9QTEFZRVJfSEVBRF9JQ09OXTogdGhpcy5vblVwZGF0ZVBsYXllckhlYWRJY29uLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9BTExJX01BUF9GTEFHXTogdGhpcy5vblVwZGF0ZUFsbE1hcEZsYWcsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX01BUF9NQVJLXTogdGhpcy5vblVwZGF0ZUFsbE1hcEZsYWcsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX01BUkNIX09QQUNJVFldOiB0aGlzLm9uVXBkYXRlTWFyY2hPcGFjaXR5LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlBMQVlfTkVXX0NFTExfRUZGRUNUXTogdGhpcy5vblBsYXlOZXdDZWxsRWZmZWN0LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlBMQVlfQ0VMTF9UT05ERU5fRUZGRUNUXTogdGhpcy5vblBsYXlDZWxsVG9uZGVuRWZmZWN0LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlBMQVlfQ0VMTF9FTU9KSV06IHRoaXMub25QbGF5Q2VsbEVtb2ppLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9DSVRZX09VVFBVVF06IHRoaXMub25VcGRhdGVDaXR5T3V0cHV0LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkNIQU5HRV9TRUFTT05fQkVHSU5dOiB0aGlzLm9uQ2hhbmdlU2Vhc29uQmVnaW4sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FOQ0lFTlRfSU5GT106IHRoaXMub25VcGRhdGVBbmNpZW50SW5mbywgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQ0lUWV9TS0lOXTogdGhpcy5vblVwZGF0ZUNpdHlTa2luLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLldFQUtfR1VJREVfU0hPV19OT0RFX0NIT09TRV06IHRoaXMub25XZWFrR3VpZGVTaG93Tm9kZUNob29zZSwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgXVxuICAgIH1cblxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcbiAgICAgICAgdGhpcy5zZXRQYXJhbSh7IGlzQ2xlYW46IGZhbHNlIH0pXG4gICAgICAgIHRoaXMuZGVjb3JhdGlvbk5vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnZGVjb3JhdGlvbicpXG4gICAgICAgIHRoaXMuZGlOb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ2RpJylcbiAgICAgICAgdGhpcy5tb3VudGFpbk5vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnbW91bnRhaW4nKVxuICAgICAgICB0aGlzLnByb3RlY3RMaW5lTm9kZSA9IHRoaXMubWFwTm9kZV8uRmluZENoaWxkKCdwcm90ZWN0X2xpbmUnKVxuICAgICAgICB0aGlzLmxpbmVOb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ2xpbmUnKVxuICAgICAgICB0aGlzLmxhbmROb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ2xhbmQnKVxuICAgICAgICB0aGlzLmNpdHlOb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ2NpdHknKVxuICAgICAgICB0aGlzLm1hc2tOb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ21hc2snKVxuICAgICAgICB0aGlzLmJ0aW5mb05vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnYnRpbmZvJylcbiAgICAgICAgdGhpcy5vdXRwdXROb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ291dHB1dCcpXG4gICAgICAgIHRoaXMuaWNvbk5vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnaWNvbicpXG4gICAgICAgIHRoaXMudG9uZGVuTm9kZSA9IHRoaXMubWFwTm9kZV8uRmluZENoaWxkKCd0b25kZW4nKVxuICAgICAgICB0aGlzLmJhdHRsZU5vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnYmF0dGxlJylcbiAgICAgICAgdGhpcy5tYXBGbGFnTm9kZSA9IHRoaXMubWFwTm9kZV8uRmluZENoaWxkKCdtYXBfZmxhZycpXG4gICAgICAgIHRoaXMubWFwTWFya05vZGUgPSB0aGlzLm1hcE5vZGVfLkZpbmRDaGlsZCgnbWFwX21hcmsnKVxuICAgICAgICBmb3IgKGxldCBpID0gMjsgaSA8PSAzOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSB0aGlzLmNlbGxFbW9qaUl0ZW1NYXBbaV0gPSB0aGlzLmNlbGxFbW9qaU5vZGVfLkZpbmRDaGlsZCgnaXRlbV8nICsgaSlcbiAgICAgICAgICAgIGl0ZW0ucGFyZW50ID0gbnVsbFxuICAgICAgICB9XG4gICAgICAgIHRoaXMudG91Y2hDbXB0ID0gdGhpcy5GaW5kQ2hpbGQoJ3RvdWNoJykuYWRkQ29tcG9uZW50KE1hcFRvdWNoQ21wdClcbiAgICAgICAgdGhpcy5jZWxsSW5mb0NtcHQgPSB0aGlzLkZpbmRDaGlsZCgncm9vdC9jZWxsX2luZm8nLCBDZWxsSW5mb0NtcHQpXG4gICAgICAgIHRoaXMuY2l0eUFuaW1Ob2RlUG9vbCA9IG5ldyBNYXBBbmltTm9kZVBvb2woKS5pbml0KHRoaXMuY2l0eU5vZGUsIHJlc0hlbHBlci5nZXRDaXR5UHJlZmFiLmJpbmQocmVzSGVscGVyKSlcbiAgICAgICAgdGhpcy5tb2RlbCA9IHRoaXMuZ2V0TW9kZWwoJ3dvcmxkJylcbiAgICAgICAgdGhpcy51c2VyID0gdGhpcy5nZXRNb2RlbCgndXNlcicpXG4gICAgICAgIHRoaXMucGxheWVyID0gdGhpcy5nZXRNb2RlbCgncGxheWVyJylcbiAgICAgICAgdGhpcy5zZWxlY3RDZWxsTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5jZWxsSW5mb0NtcHQuY2xvc2UoKVxuICAgICAgICB0aGlzLnNjZW5lRWZmZWN0ID0gdGhpcy5zY2VuZUVmZmVjdE5vZGVfLmdldENvbXBvbmVudChTY2VuZUVmZmVjdENtcHQpXG4gICAgICAgIHRoaXMudXBkYXRlU2Vhc29uU2VjZW5lRWZmZWN0KClcbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25SZWFkeSgpIHtcbiAgICAgICAgYXdhaXQgdGhpcy5jaGVja1NlYXNvbigpXG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIoZGF0YTogYW55KSB7XG4gICAgICAgIHRoaXMubW9kZWwuaW5pdENhbWVyYUluZm8oKVxuICAgICAgICB0aGlzLmNlbGxFZmZlY3ROb2RlXy5EYXRhID0gdHJ1ZVxuICAgICAgICB0aGlzLmNlbGxFbW9qaU5vZGVfLkRhdGEgPSB0cnVlXG4gICAgICAgIHRoaXMudG9wTGF5ZXJOb2RlXy5EYXRhID0gdHJ1ZVxuICAgICAgICB0aGlzLnNob3dNYXAoKSAvL+aYvuekuuWcsOWbvlxuICAgICAgICB0aGlzLmluaXRNYXJjaCgpIC8v5Yid5aeL5YyW6KGM5YabXG4gICAgICAgIHRoaXMucGxheU5ld0NlbGxFZmZlY3QoKVxuICAgICAgICB0aGlzLnBsYXlDZWxsVG9uZGVuRWZmZWN0KClcbiAgICAgICAgdGhpcy5wbGF5Q2VsbEVtb2ppKClcbiAgICAgICAgdGhpcy50b3VjaENtcHQuaW5pdCh0aGlzLm9uQ2xpY2tNYXAuYmluZCh0aGlzKSlcbiAgICAgICAgZ2FtZUhwci5wbGF5TWFpbkJnbSgpXG4gICAgfVxuXG4gICAgcHVibGljIG9uTGVhdmUoKSB7XG4gICAgICAgIHRoaXMubW9kZWwuc2F2ZUNhbWVyYUluZm8oKVxuICAgICAgICB0aGlzLnRvdWNoQ21wdC5jbGVhbigpXG4gICAgICAgIHRoaXMuc2VsZWN0Q2VsbE5vZGVfLkNvbXBvbmVudChTZWxlY3RDZWxsQ21wdCkuY2xvc2UoKVxuICAgICAgICB0aGlzLmNlbGxJbmZvQ21wdC5jbG9zZSgpXG4gICAgICAgIHRoaXMucmVxU2VsZWN0QXJteXNpbmcgPSBmYWxzZVxuICAgICAgICB0aGlzLmNsZWFuTWFyY2goKVxuICAgICAgICB0aGlzLmNlbGxFZmZlY3ROb2RlXy5yZW1vdmVBbGxDaGlsZHJlbigpXG4gICAgICAgIHRoaXMuY2VsbEVmZmVjdE5vZGVfLkRhdGEgPSBmYWxzZVxuICAgICAgICB0aGlzLmNlbGxFbW9qaU5vZGVfLnJlbW92ZUFsbENoaWxkcmVuKClcbiAgICAgICAgdGhpcy5jZWxsRW1vamlOb2RlXy5EYXRhID0gZmFsc2VcbiAgICAgICAgdGhpcy50b3BMYXllck5vZGVfLnJlbW92ZUFsbENoaWxkcmVuKClcbiAgICAgICAgdGhpcy50b3BMYXllck5vZGVfLkRhdGEgPSBmYWxzZVxuICAgICAgICAvLyByZXNIZWxwZXIuY2xlYW5Ob2RlQ2hpbGRyZW4odGhpcy5kaU5vZGUpIOi/memHjOaaguaXtuS4jea4heeQhiDlm6DkuLrov5vlhaXlhbbku5blnLrmma/lpKrmhaLkuoZcbiAgICAgICAgdGhpcy5jZWxsRW1vamlNYXAgPSB7fVxuICAgICAgICBub2RlUG9vbE1nci5jbGVhblVzZUFuZFJlbW92ZUl0ZW1zQnlUYWcodGhpcy5rZXkpXG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuICAgICAgICBjYW1lcmFDdHJsLnNldEJnQ29sb3IoJyNEMUYxRjMnKVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xuICAgICAgICBmb3IgKGxldCBrIGluIHRoaXMuY2VsbEVtb2ppSXRlbU1hcCkge1xuICAgICAgICAgICAgdGhpcy5jZWxsRW1vamlJdGVtTWFwW2tdLmRlc3Ryb3koKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2VsbEVtb2ppSXRlbU1hcCA9IHt9XG4gICAgICAgIHRoaXMuY2VsbEVtb2ppTWFwID0ge31cbiAgICAgICAgdGhpcy5jZWxsSW5mb0NtcHQuY2xlYW4oKVxuICAgICAgICB0aGlzLnNjZW5lRWZmZWN0Py5jbGVhbigpXG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMuSU5JVF9LRVkpXG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby9idXR0b25zL2VudGVyX2JlXG4gICAgb25DbGlja0VudGVyKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMuY2VsbEluZm9DbXB0LmdldENlbGwoKVxuICAgICAgICBpZiAoY2VsbCkge1xuICAgICAgICAgICAgdGhpcy5tb2RlbC5zZXRMb29rQ2VsbChjZWxsKVxuICAgICAgICAgICAgdmlld0hlbHBlci5nb3RvV2luZCgnYXJlYScpXG4gICAgICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3QvY2VsbF9pbmZvL2J1dHRvbnMvb2NjdXB5X2JlXG4gICAgb25DbGlja09jY3VweShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKCFjZWxsKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmICghdGhpcy5tb2RlbC5jaGVja0Nhbk9jY3VweUNlbGwoY2VsbCkpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5PTkxZX0FUVEFDS19BREpPSU5fQ0VMTClcbiAgICAgICAgfSBlbHNlIGlmIChjZWxsLmNoZWNrQXR0YWNrQnlQcm90ZWN0KCkpIHsgLy/mmK/lkKbmnInkv53miqRcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5DRUxMX1BST1RFQ1QpXG4gICAgICAgIH0gZWxzZSBpZiAoY2VsbC5pc0F2b2lkV2FyKCkpIHsgLy/mmK/lkKbph5Hnm75cbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5BVk9JRF9XQVJfTk9UX0FUVEFDSylcbiAgICAgICAgfVxuICAgICAgICB0aGlzLm9jY3VweUNlbGwoY2VsbC5hY3RJbmRleClcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vYnV0dG9ucy90b25kZW5fYmVcbiAgICBvbkNsaWNrVG9uZGVuKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMuY2VsbEluZm9DbXB0LmdldENlbGwoKVxuICAgICAgICBpZiAoIWNlbGwgfHwgIWNlbGwuaXNPd24oKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoZ2FtZUhwci5pc0JhdHRsZWluZ0J5SW5kZXgoY2VsbC5pbmRleCkpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QuYmF0dGxlaW5nX25vdF90b25kZW4nKVxuICAgICAgICB9IGVsc2UgaWYgKGNlbGwuaXNCVENpdHlpbmcoKSkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5idGluZ19ub3RfdG9uZGVuJylcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNlbGxUb25kZW4oY2VsbC5hY3RJbmRleClcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vYnV0dG9ucy9jYW5jZWxfdG9uZGVuX2JlXG4gICAgb25DbGlja0NhbmNlbFRvbmRlbihldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKCFjZWxsIHx8ICFjZWxsLmlzT3duKCkgfHwgIWNlbGwuaXNUb25kZW5pbmcoKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdmlld0hlbHBlci5zaG93TWVzc2FnZUJveCgndWkuY2FuY2VsX3RvbmRlbl90aXAnLCB7XG4gICAgICAgICAgICBvazogKCkgPT4gdGhpcy5pc0FjdGl2ZSAmJiB0aGlzLmNhbmNlbFRvbmRlbihjZWxsLmFjdEluZGV4KSxcbiAgICAgICAgICAgIGNhbmNlbDogKCkgPT4geyB9LFxuICAgICAgICB9KVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby9idXR0b25zL21vdmVfYmVcbiAgICBvbkNsaWNrTW92ZShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKCFjZWxsIHx8ICFjZWxsLmlzT25lQWxsaWFuY2UoKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5tb3ZlVG9DZWxsKGNlbGwuYWN0SW5kZXgpXG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3QvY2VsbF9pbmZvL2J1dHRvbnMvYnVpbGRfYmVcbiAgICBvbkNsaWNrQnVpbGQoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBjZWxsID0gdGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpXG4gICAgICAgIGlmICghY2VsbCB8fCAhY2VsbC5pc093bigpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmIChjZWxsLmlzVG9uZGVuaW5nKCkpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QudG9uZGVuaW5nX25vdF9idCcpXG4gICAgICAgIH1cbiAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdtYWluL0NpdHlMaXN0JywgY2VsbClcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vYnV0dG9ucy9kaXNtYW50bGVfYmVcbiAgICBvbkNsaWNrRGlzbWFudGxlKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMuY2VsbEluZm9DbXB0LmdldENlbGwoKVxuICAgICAgICBpZiAoIWNlbGwgfHwgIWNlbGwuaXNPd24oKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdtYWluL0Rpc21hbnRsZUNpdHlUaXAnLCBjZWxsKVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby9idXR0b25zL3BsYXllcl9pbmZvX2JlXG4gICAgb25DbGlja1BsYXllckluZm8oZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBjZWxsID0gdGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpXG4gICAgICAgIGNvbnN0IGluZm8gPSB0aGlzLm1vZGVsLmdldFBsYXllckluZm8oY2VsbD8ub3duZXIpXG4gICAgICAgIGlmIChpbmZvKSB7XG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2NvbW1vbi9QbGF5ZXJJbmZvJywgaW5mbywgJ2NlbGxpbmZvJylcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby90aXRsZS9zaGFyZV9wb3NfYmVcbiAgICBvbkNsaWNrU2hhcmVQb3MoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIGNvbnN0IHBvaW50ID0gY2VsbC5hY3RQb2ludC5Kb2luKCcsJylcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnY29tbW9uL1NlbmRJbmZvVG9DaGF0JywgeyBrZXk6ICd1aS5zZW5kX3BvaW50X3RvX2NoYXRfdGlwJywgcGFyYW1zOiBbcG9pbnRdIH0sICh0eXBlOiBudW1iZXIsIGNoaWxkVHlwZTogbnVtYmVyLCBzZWxlY3Q6IHsgdHlwZTogbnVtYmVyLCBjaGFubmVsOiBzdHJpbmcgfSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZ2FtZUhwci5jaGF0LmdldFRhcmdldENoYXQodHlwZSwgY2hpbGRUeXBlLCBzZWxlY3QpXG4gICAgICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnY29tbW9uL0NoYXQnLCB7IHRhYjogdHlwZSwgdGV4dDogYFske3BvaW50fV1gLCB0YXJnZXQgfSkudGhlbigoKSA9PiB0aGlzLmlzVmFsaWQgJiYgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSkpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby9idXR0b25zL2ZsYWdfYmVcbiAgICBvbkNsaWNrRmxhZyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGlmICghZ2FtZUhwci5hbGxpYW5jZS5pc01lTWlsaXRhcnkoKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMuY2VsbEluZm9DbXB0LmdldENlbGwoKVxuICAgICAgICBpZiAoY2VsbCkge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdtYWluL0FsbGlGbGFnJywgY2VsbC5hY3RQb2ludCwgZmFsc2UpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vaW5mby9zY29yZS9zY29yZV9kZXNjX2JlXG4gICAgb25DbGlja1Njb3JlRGVzYyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9MYW5kU2NvcmVEZXNjJywgY2VsbC5sYW5kTHYpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vaW5mby9zdGFtaW5hL3N0YW1pbmFfZGVzY19iZVxuICAgIG9uQ2xpY2tTdGFtaW5hRGVzYyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9DZWxsRHJvcEluZm8nLCBjZWxsLmdldExhbmRBdHRyKCksIGNlbGwubGFuZFR5cGUpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vdGl0bGUvY2VsbF9lbW9qaV9iZVxuICAgIG9uQ2xpY2tDZWxsRW1vamkoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9DZWxsU2VsZWN0RW1vamknLCBjZWxsLmlzT3duKCksIChpZDogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIGdhbWVIcHIuZ3JvdW5kLnNlbmRDZWxsRW1vamkoaWQsIGNlbGwuYWN0SW5kZXgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICh0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3QvbWFwX24vb3V0cHV0L2l0ZW0vY2l0eV9vdXRwdXRfYmVcbiAgICBvbkNsaWNrQ2l0eU91dHB1dChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGw6IE1hcENlbGxPYmogPSBldmVudC50YXJnZXQucGFyZW50LkRhdGFcbiAgICAgICAgaWYgKCFjZWxsIHx8ICFjZWxsLmlzT3duKCkpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJld2FyZHMgPSBnYW1lSHByLmdldFBsYXllckluZm8oZ2FtZUhwci5nZXRVaWQoKSk/LmNpdHlPdXRwdXRNYXBbY2VsbC5pbmRleF0gfHwgW11cbiAgICAgICAgdGhpcy5tb2RlbC5jbGFpbUNpdHlPdXRwdXQoY2VsbC5pbmRleCkudGhlbihlcnIgPT4ge1xuICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBnYW1lSHByLmFkZEdhaW5NYXNzYWdlKHJld2FyZHMpXG4gICAgICAgICAgICByZXdhcmRzLmZvckVhY2goKG0sIGkpID0+IGFuaW1IZWxwZXIucGxheUZsdXR0ZXJDZWxsT3V0cHV0KGkgKiAwLjQsIG0sIHRoaXMudG9wTGF5ZXJOb2RlXywgY2VsbC5hY3RQb3NpdGlvbiwgdGhpcy5rZXkpKVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2NlbGxfaW5mby9idXR0b25zL2FuY2llbnRfaW5mb19iZVxuICAgIG9uQ2xpY2tBbmNpZW50SW5mbyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIGNvbnN0IGFuY2llbnQgPSB0aGlzLm1vZGVsLmdldEFuY2llbnRJbmZvKGNlbGwuaW5kZXgpXG4gICAgICAgICAgICBpZiAoYW5jaWVudCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJ1aWxkID0gbmV3IEJ1aWxkT2JqKCkuaW5pdChjZWxsLmluZGV4LCB1dC5VSUQoKSwgY2MudjIoMCwgMCksIGFuY2llbnQuaWQsIGFuY2llbnQubHYpXG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdidWlsZC9CdWlsZEFuY2llbnRCYXNlJywgYnVpbGQpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vYnV0dG9ucy9jaXR5X3NraW5fYmVcbiAgICBvbkNsaWNrQ2l0eVNraW4oZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBjZWxsID0gdGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpXG4gICAgICAgIGlmIChjZWxsICYmIGNlbGwuY2l0eUlkID4gMCkge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdtYWluL1NlbGVjdENpdHlTa2luJywgY2VsbCwgKG9rOiBib29sZWFuKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCAmJiBvaykge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKGZhbHNlKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9jZWxsX2luZm8vdGl0bGUvbWFwX21hcmtfYmVcbiAgICBvbkNsaWNrTWFwTWFyayhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLmNlbGxJbmZvQ21wdC5nZXRDZWxsKClcbiAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9NYXBNYXJrJywgY2VsbC5hY3RQb2ludCwgZmFsc2UpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLy9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgb25OZXRSZWNvbm5lY3QoKSB7XG4gICAgICAgIGlmICghZ2FtZUhwci5ndWlkZS5pc09uZUd1aWRlV29ya2luZygpKSB7IC8v6L+Z6YeM5aaC5p6c5Zyo5paw5omL5byV5a+8IOWwseS4jeimgeWFs+mXreS6hlxuICAgICAgICAgICAgdGhpcy5zZWxlY3RDZWxsTm9kZV8uQ29tcG9uZW50KFNlbGVjdENlbGxDbXB0KS5jbG9zZSgpXG4gICAgICAgICAgICB0aGlzLmNlbGxJbmZvQ21wdC5jbG9zZSgpXG4gICAgICAgICAgICB0aGlzLm1vZGVsLmluaXRDYW1lcmFJbmZvKClcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnVwZGF0ZU1hcCh0aGlzLmNlbnRyZSkgLy/liLfmlrDlnLDlm77mmL7npLpcbiAgICAgICAgdGhpcy5pbml0TWFyY2goKSAvL+WIneWni+WMluihjOWGm1xuICAgIH1cblxuICAgIC8vIOabtOaWsOWcsOWdl+S/oeaBr1xuICAgIHByaXZhdGUgb25VcGRhdGVDZWxsSW5mbygpIHtcbiAgICAgICAgdGhpcy51cGRhdGVNYXAodGhpcy5jZW50cmUpXG4gICAgfVxuXG4gICAgLy8g5re75Yqg6KGM5YabXG4gICAgcHJpdmF0ZSBvbkFkZE1hcmNoKGRhdGE6IEJhc2VNYXJjaE9iaikge1xuICAgICAgICBpZiAoIWRhdGEuaXNDYW5TaG93TWFyY2goKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMub25SZW1vdmVNYXJjaChkYXRhKVxuICAgICAgICB9XG4gICAgICAgIGxldCBtYXJjaCA9IHRoaXMubWFyY2hzLmZpbmQobSA9PiBtLnVpZCA9PT0gZGF0YS51aWQpXG4gICAgICAgIGlmIChtYXJjaCkge1xuICAgICAgICAgICAgbWFyY2guaW5pdChkYXRhLCB0aGlzLm1hcmNoUm9sZU5vZGVfLCB0aGlzLmtleSlcbiAgICAgICAgICAgIHRoaXMuY2hlY2tNYXJjaExpbmVPZmZzZXQoZGF0YSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMubWFyY2hMaW5lTm9kZV8uQWRkSXRlbShpdCA9PiB7XG4gICAgICAgICAgICAgICAgbWFyY2ggPSB0aGlzLm1hcmNocy5hZGQoaXQuQ29tcG9uZW50KE1hcmNoQ21wdCkuaW5pdChkYXRhLCB0aGlzLm1hcmNoUm9sZU5vZGVfLCB0aGlzLmtleSkpXG4gICAgICAgICAgICAgICAgdGhpcy5jaGVja01hcmNoTGluZU9mZnNldChkYXRhKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIoOmZpOihjOWGm1xuICAgIHByaXZhdGUgb25SZW1vdmVNYXJjaChkYXRhOiBCYXNlTWFyY2hPYmopIHtcbiAgICAgICAgY29uc3QgbWFyY2ggPSB0aGlzLm1hcmNocy5yZW1vdmUoJ3VpZCcsIGRhdGEudWlkKVxuICAgICAgICBpZiAobWFyY2gpIHtcbiAgICAgICAgICAgIG1hcmNoLmNsZWFuKClcbiAgICAgICAgICAgIHRoaXMuY2hlY2tNYXJjaExpbmVPZmZzZXQoZGF0YSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOaJgOacieihjOWGm1xuICAgIHByaXZhdGUgb25VcGRhdGVBbGxNYXJjaCgpIHtcbiAgICAgICAgdGhpcy5pbml0TWFyY2goKVxuICAgIH1cblxuICAgIC8vIOmakOiXj+aWh+acrFxuICAgIHByaXZhdGUgb25IaWRlV29ybGRUZXh0KHZhbDogYm9vbGVhbikge1xuICAgICAgICB2YWwgPSAhdmFsXG4gICAgICAgIHRoaXMudGV4dE5vZGVfLmFjdGl2ZSA9IHZhbFxuICAgICAgICB0aGlzLmFuY2llbnRUZXh0Tm9kZV8uYWN0aXZlID0gdmFsXG4gICAgICAgIHRoaXMuYnRpbmZvTm9kZS5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gbS5EYXRhICYmIChtLkNoaWxkKCd0aW1lJykuYWN0aXZlID0gdmFsKSlcbiAgICAgICAgdGhpcy50b25kZW5Ob2RlLmNoaWxkcmVuLmZvckVhY2gobSA9PiBtLkRhdGEgJiYgKG0uQ2hpbGQoJ3RpbWUnKS5hY3RpdmUgPSB2YWwpKVxuICAgIH1cblxuICAgIC8vIOWFs+mXremAieaLqeWcsOWdl1xuICAgIHByaXZhdGUgb25DbG9zZVNlbGVjdENlbGwocGxheTogYm9vbGVhbikge1xuICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKCEhcGxheSlcbiAgICB9XG5cbiAgICAvLyDliLfmlrDmiJjmlpfnirbmgIFcbiAgICBwcml2YXRlIG9uVXBkYXRlQmF0dGxlRGlzdEluZm8oKSB7XG4gICAgICAgIGNvbnN0IGNlbGxzOiBNYXBDZWxsT2JqW10gPSBbXSwgZGlzdE1hcCA9IHRoaXMubW9kZWwuZ2V0QmF0dGxlRGlzdE1hcCgpXG4gICAgICAgIGZvciAobGV0IGluZGV4IGluIGRpc3RNYXApIHtcbiAgICAgICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLnRlbXBTaG93Q2VsbE1hcFtpbmRleF1cbiAgICAgICAgICAgIGNlbGwgJiYgY2VsbHMucHVzaChjZWxsKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuYmF0dGxlTm9kZS5JdGVtcyhjZWxscywgKGl0LCBkYXRhKSA9PiBpdC5zZXRQb3NpdGlvbihkYXRhLmFjdFBvc2l0aW9uKSlcbiAgICB9XG5cbiAgICAvLyDliLfmlrDlhY3miJjnirbmgIFcbiAgICBwcml2YXRlIG9uVXBkYXRlQXZvaWRXYXJEaXN0SW5mbygpIHtcbiAgICAgICAgdGhpcy51cGRhdGVJY29uTm9kZSgpXG4gICAgfVxuXG4gICAgLy8g5Yi35paw5L+u5bu65L+h5oGvXG4gICAgcHJpdmF0ZSBvblVwZGF0ZUJ0Q2l0eShpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIHRoaXMudXBkYXRlTWFwKHRoaXMuY2VudHJlKVxuICAgICAgICBpZiAodGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpPy5hY3RJbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuY2VsbEluZm9DbXB0LnVwZGF0ZUluZm8oKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yi35paw5bGv55Sw5L+h5oGvXG4gICAgcHJpdmF0ZSBvblVwZGF0ZVRvbmRlbihpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIHRoaXMudXBkYXRlTWFwKHRoaXMuY2VudHJlKVxuICAgICAgICBpZiAodGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpPy5hY3RJbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuY2VsbEluZm9DbXB0LnVwZGF0ZUluZm8oKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yi35paw5Zyw5Zu+5LiK6Z2i55qE5Yab6Zif5YiG5biD5oOF5Ya1ICDov5nph4zkuLvliqjnu5jliLbkuIDmrKFcbiAgICBwcml2YXRlIG9uVXBkYXRlQXJteURpc3RJbmZvKCkge1xuICAgICAgICB0aGlzLnVwZGF0ZUljb25Ob2RlKClcbiAgICAgICAgdGhpcy5jZWxsSW5mb0NtcHQudXBkYXRlQXJteUluZm8oKVxuICAgIH1cblxuICAgIC8vIOenu+WKqOWcsOWbvlxuICAgIHByaXZhdGUgb25NYXBNb3ZlVG8ocG9pbnQ6IGNjLlZlYzIsIHNob3dDZWxsSW5mbzogYm9vbGVhbikge1xuICAgICAgICBpZiAodGhpcy5jZW50cmUuZXF1YWxzKHBvaW50KSkge1xuICAgICAgICAgICAgcmV0dXJuIHNob3dDZWxsSW5mbyAmJiB0aGlzLnNob3dTZWxlY3RDZWxsKHRoaXMubW9kZWwuZ2V0TWFwQ2VsbEJ5UG9pbnQocG9pbnQuY2xvbmUoKS5mbG9vcigpKSlcbiAgICAgICAgfSBlbHNlIGlmICghdGhpcy50ZW1wU2hvd0NlbGxNYXBbbWFwSGVscGVyLnBvaW50VG9JbmRleChwb2ludCldKSB7IC8v5aaC5p6c5rKh5pyJ5Zyo5b2T5YmN57uY5Yi25Yy65Z+f5bCx56e75Yqo5Yiw55uu5qCH54K5XG4gICAgICAgICAgICBjb25zdCBzdGFydCA9IHRoaXMuY2VudHJlLnN1Yihwb2ludCwgdGhpcy5fdGVtcF92ZWMyXzQpLm5vcm1hbGl6ZVNlbGYoKS5tdWxTZWxmKDIpLmFkZFNlbGYocG9pbnQpXG4gICAgICAgICAgICBjYW1lcmFDdHJsLmluaXQobWFwSGVscGVyLmdldFBpeGVsQnlQb2ludChzdGFydCksIG1hcEhlbHBlci5NQVBfU0laRSwgTUFQX1NIT1dfT0ZGU0VULCBjYW1lcmFDdHJsLnpvb21SYXRpbylcbiAgICAgICAgICAgIHRoaXMudXBkYXRlTWFwKHN0YXJ0LmZsb29yKCkpXG4gICAgICAgICAgICB0aGlzLmNoZWNrSW5DYW1lcmFNYXJjaExpbmUoKVxuICAgICAgICB9XG4gICAgICAgIC8vIOenu+WKqFxuICAgICAgICBjYW1lcmFDdHJsLm1vdmVUbygwLjI1LCBtYXBIZWxwZXIuZ2V0UGl4ZWxCeVBvaW50KHBvaW50KS5zdWJTZWxmKGNhbWVyYUN0cmwuZ2V0V2luU2l6ZUhhbGYoKSkpLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgaWYgKHRoaXMuaXNBY3RpdmUgJiYgc2hvd0NlbGxJbmZvKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zaG93U2VsZWN0Q2VsbCh0aGlzLm1vZGVsLmdldE1hcENlbGxCeVBvaW50KHBvaW50LmNsb25lKCkuZmxvb3IoKSkpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g5Yi35paw546p5a625pi156ewXG4gICAgcHJpdmF0ZSBvblVwZGF0ZVBsYXllck5pY2tuYW1lKGRhdGE6IFBsYXllckluZm8pIHtcbiAgICAgICAgY29uc3QgaXQgPSB0aGlzLnRleHROb2RlXy5jaGlsZHJlbi5maW5kKG0gPT4gbS5hY3RpdmUgJiYgbS5EYXRhPy5vd25lciA9PT0gZGF0YS51aWQpXG4gICAgICAgIGlmIChpdCkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGVQbGF5ZXJOaWNrbmFtZShpdCwgZGF0YSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOeOqeWutuWktOWDj1xuICAgIHByaXZhdGUgb25VcGRhdGVQbGF5ZXJIZWFkSWNvbihkYXRhOiBhbnkpIHtcbiAgICAgICAgY29uc3QgaXQgPSB0aGlzLnRleHROb2RlXy5jaGlsZHJlbi5maW5kKG0gPT4gbS5hY3RpdmUgJiYgbS5EYXRhPy5vd25lciA9PT0gZGF0YS51aWQpXG4gICAgICAgIGlmIChpdCkge1xuICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRQbGF5ZXJIZWFkKGl0LkNoaWxkKCdoZWFkJywgY2MuU3ByaXRlKSwgZGF0YS5oZWFkSWNvbiwgdGhpcy5JTklUX0tFWSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOS4quS6uuagh+iusFxuICAgIHByaXZhdGUgb25VcGRhdGVNYXBNYXJrKCkge1xuICAgICAgICBjb25zdCBjZWxsczogeyBjZWxsOiBNYXBDZWxsT2JqLCBmbGFnOiBudW1iZXIgfVtdID0gW10sIG1hcEZhbGcgPSB0aGlzLnBsYXllci5nZXRNYXBNYXJrcygpXG4gICAgICAgIGZvciAobGV0IGluZGV4IGluIG1hcEZhbGcpIHtcbiAgICAgICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLnRlbXBTaG93Q2VsbE1hcFtpbmRleF1cbiAgICAgICAgICAgIGNlbGwgJiYgY2VsbHMucHVzaCh7IGNlbGwsIGZsYWc6IG1hcEZhbGdbaW5kZXhdLmZsYWcgfSlcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBvdGhlckZsYWcgPSBnYW1lSHByLmFsbGlhbmNlLmdldE1hcEZsYWcoKVxuICAgICAgICB0aGlzLm1hcE1hcmtOb2RlLkl0ZW1zKGNlbGxzLCAoaXQsIGRhdGEpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGFjdFBvc2l0aW9uID0gZGF0YS5jZWxsLmFjdFBvc2l0aW9uXG4gICAgICAgICAgICBjb25zdCBwb3MgPSBvdGhlckZsYWdbZGF0YS5jZWxsLmFjdEluZGV4XSA/IHRoaXMuX3RlbXBfdmVjMl82LnNldDIoYWN0UG9zaXRpb24ueCAtIDE2LCBhY3RQb3NpdGlvbi55KSA6IGFjdFBvc2l0aW9uXG4gICAgICAgICAgICBpdC5zZXRQb3NpdGlvbihwb3MpXG4gICAgICAgICAgICBpdC5DaGlsZCgncm9vdC92YWwnLCBjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldE1hcEZsYWdOdW1iZXIoZGF0YS5mbGFnKVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOiBlOebn+WcsOWbvuagh+iusFxuICAgIHByaXZhdGUgb25VcGRhdGVBbGxpTWFwRmxhZygpIHtcbiAgICAgICAgY29uc3QgY2VsbHM6IHsgY2VsbDogTWFwQ2VsbE9iaiwgZmxhZzogbnVtYmVyIH1bXSA9IFtdLCBtYXBGYWxnID0gZ2FtZUhwci5hbGxpYW5jZS5nZXRNYXBGbGFnKClcbiAgICAgICAgZm9yIChsZXQgaW5kZXggaW4gbWFwRmFsZykge1xuICAgICAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMudGVtcFNob3dDZWxsTWFwW2luZGV4XVxuICAgICAgICAgICAgY2VsbCAmJiBjZWxscy5wdXNoKHsgY2VsbCwgZmxhZzogbWFwRmFsZ1tpbmRleF0uZmxhZyB9KVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG90aGVyRmxhZyA9IHRoaXMucGxheWVyLmdldE1hcE1hcmtzKClcbiAgICAgICAgdGhpcy5tYXBGbGFnTm9kZS5JdGVtcyhjZWxscywgKGl0LCBkYXRhKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBhY3RQb3NpdGlvbiA9IGRhdGEuY2VsbC5hY3RQb3NpdGlvblxuICAgICAgICAgICAgY29uc3QgcG9zID0gb3RoZXJGbGFnW2RhdGEuY2VsbC5hY3RJbmRleF0gPyB0aGlzLl90ZW1wX3ZlYzJfNy5zZXQyKGFjdFBvc2l0aW9uLnggKyAxNiwgYWN0UG9zaXRpb24ueSkgOiBhY3RQb3NpdGlvblxuICAgICAgICAgICAgaXQuc2V0UG9zaXRpb24ocG9zKVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ3Jvb3QvdmFsJywgY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRNYXBGbGFnTnVtYmVyKGRhdGEuZmxhZylcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICBwcml2YXRlIG9uVXBkYXRlQWxsTWFwRmxhZygpIHtcbiAgICAgICAgdGhpcy5vblVwZGF0ZU1hcE1hcmsoKVxuICAgICAgICB0aGlzLm9uVXBkYXRlQWxsaU1hcEZsYWcoKVxuICAgICAgICB0aGlzLmNlbGxJbmZvQ21wdC51cGRhdGVGbGFnRGVzYygpXG4gICAgfVxuXG4gICAgLy8g5Yi35paw6KGM5Yab57q/6YCP5piO5bqmXG4gICAgcHJpdmF0ZSBvblVwZGF0ZU1hcmNoT3BhY2l0eSgpIHtcbiAgICAgICAgdGhpcy5tYXJjaHMuZm9yRWFjaChtID0+IG0udXBkYXRlT3BhY2l0eSgpKVxuICAgIH1cblxuICAgIC8vIOaSreaUvuaWsOeahOWcsOWdl+aViOaenFxuICAgIHByaXZhdGUgb25QbGF5TmV3Q2VsbEVmZmVjdCgpIHtcbiAgICAgICAgdGhpcy5wbGF5TmV3Q2VsbEVmZmVjdCgpXG4gICAgfVxuXG4gICAgLy8g5pKt5pS+5bGv55Sw57uT5p2f5pWI5p6cXG4gICAgcHJpdmF0ZSBvblBsYXlDZWxsVG9uZGVuRWZmZWN0KCkge1xuICAgICAgICB0aGlzLnBsYXlDZWxsVG9uZGVuRWZmZWN0KClcbiAgICB9XG5cbiAgICAvLyDmkq3mlL7lnLDlm77ooajmg4VcbiAgICBwcml2YXRlIG9uUGxheUNlbGxFbW9qaSgpIHtcbiAgICAgICAgdGhpcy5wbGF5Q2VsbEVtb2ppKClcbiAgICB9XG5cbiAgICAvLyDliLfmlrDln47luILkuqflh7pcbiAgICBwcml2YXRlIG9uVXBkYXRlQ2l0eU91dHB1dCgpIHtcbiAgICAgICAgY29uc3QgY2VsbHM6IHsgY2VsbDogTWFwQ2VsbE9iaiwgb3V0cHV0OiBDVHlwZU9iaiB9W10gPSBbXVxuICAgICAgICBmb3IgKGxldCBpbmRleCBpbiB0aGlzLnRlbXBTaG93Q2VsbE1hcCkge1xuICAgICAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMudGVtcFNob3dDZWxsTWFwW2luZGV4XVxuICAgICAgICAgICAgY29uc3Qgb3V0cHV0ID0gY2VsbC5nZXRPdXRwdXRUeXBlKClcbiAgICAgICAgICAgIGlmIChvdXRwdXQpIHtcbiAgICAgICAgICAgICAgICBjZWxscy5wdXNoKHsgY2VsbCwgb3V0cHV0OiBvdXRwdXQgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLm91dHB1dE5vZGUuSXRlbXMoY2VsbHMsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgaXQuc2V0UG9zaXRpb24oZGF0YS5jZWxsLmFjdFBvc2l0aW9uKVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ2NpdHlfb3V0cHV0X2JlJykuYWN0aXZlID0gZGF0YS5jZWxsLmlzT3duKClcbiAgICAgICAgICAgIGl0LkNoaWxkKCdyb290L2ljb24nLCBjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gYXNzZXRzTWdyLmdldEltYWdlKENUWVBFX0lDT05bZGF0YS5vdXRwdXQudHlwZV0pXG4gICAgICAgICAgICBpdC5DaGlsZCgncm9vdC92YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0gZGF0YS5vdXRwdXQuY291bnQgPiAxID8gZGF0YS5vdXRwdXQuY291bnQgKyAnJyA6ICcnXG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g5pS55Y+Y5a2j6IqC5byA5aeLXG4gICAgcHJpdmF0ZSBvbkNoYW5nZVNlYXNvbkJlZ2luKCkge1xuICAgICAgICB0aGlzLnBsYXlDaGFuZ2VTZWFzb24odGhpcy5tb2RlbC5nZXRTZWFzb24oKS50eXBlKVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOmBl+i/uVxuICAgIHByaXZhdGUgb25VcGRhdGVBbmNpZW50SW5mbyhkYXRhOiBBbmNpZW50T2JqKSB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gZGF0YS5pbmRleFxuICAgICAgICBjb25zdCBpdCA9IHRoaXMuYW5jaWVudFRleHROb2RlXy5jaGlsZHJlbi5maW5kKG0gPT4gbS5hY3RpdmUgJiYgbS5EYXRhPy5pbmRleCA9PT0gaW5kZXgpXG4gICAgICAgIGlmIChpdCkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGVBbmNpZW50VGV4dEluZm8oaXQsIGRhdGEpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDln47luILnmq7ogqRcbiAgICBwcml2YXRlIG9uVXBkYXRlQ2l0eVNraW4oaW5kZXg6IG51bWJlcikge1xuICAgICAgICB0aGlzLnVwZGF0ZU1hcCh0aGlzLmNlbnRyZSlcbiAgICB9XG5cbiAgICAvLyDoi6XlvJXlr7xcbiAgICBwcml2YXRlIG9uV2Vha0d1aWRlU2hvd05vZGVDaG9vc2UoZGF0YTogYW55KSB7XG4gICAgICAgIGlmIChkYXRhLnNjZW5lID09PSAnbWFpbicpIHtcbiAgICAgICAgICAgIGd1aWRlSGVscGVyLnBsYXlXZWFrR3VpZGVGaW5nZXIoZGF0YSwgdGhpcy53ZWFrR3VpZGVOb2RlXywgdGhpcy5rZXkpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgZ2V0IGlzQWN0aXZlKCkgeyByZXR1cm4gdGhpcy5pc1ZhbGlkICYmIHRoaXMuaXNFbnRlcigpIH1cblxuICAgIC8vIOeCueWHu+WcsOWbvlxuICAgIHByaXZhdGUgb25DbGlja01hcCh3b3JsZExvY2F0aW9uOiBjYy5WZWMyKSB7XG4gICAgICAgIGNvbnN0IGNlbGwgPSB0aGlzLm1vZGVsLmdldE1hcENlbGxCeVBvaW50KG1hcEhlbHBlci5nZXRQb2ludEJ5UGl4ZWwod29ybGRMb2NhdGlvbikpXG4gICAgICAgIGlmIChjZWxsICYmICF0aGlzLnNlbGVjdENlbGxOb2RlXy5EYXRhKSB7XG4gICAgICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgICAgICB0aGlzLnNob3dTZWxlY3RDZWxsKGNlbGwpXG4gICAgICAgICAgICBjYW1lcmFDdHJsLnJlZHJlc3NQb3NpdGlvbkJ5UmFuZ2UoY2VsbC5hY3RQb3NpdGlvbiwgU0VMRUNUX0NFTExfSU5GT19CT1gpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOWcuuaZr+eJueaViFxuICAgIHByaXZhdGUgdXBkYXRlU2Vhc29uU2VjZW5lRWZmZWN0KCkge1xuICAgICAgICB0aGlzLnNjZW5lRWZmZWN0LmNsZWFuKClcbiAgICAgICAgY29uc3Qgc2NlbmVFZmZlY3RVcmwgPSB0aGlzLm1vZGVsLmdldFNlYXNvbigpLmdldEN1cnJTY2VuZUVmZmVjdFVybCgpXG4gICAgICAgIGlmIChzY2VuZUVmZmVjdFVybCkgeyAvL+WKoOi9veWcuuaZr+eJueaViFxuICAgICAgICAgICAgdGhpcy5zY2VuZUVmZmVjdC5pbml0KHNjZW5lRWZmZWN0VXJsLCB0aGlzLklOSVRfS0VZKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNlYXNvblR5cGUgPSB0aGlzLm1vZGVsLmdldFNlYXNvblR5cGUoKVxuICAgICAgICAvLyDmm7TmlrDpga7nvanpopzoibJcbiAgICAgICAgdGhpcy5tYXNrTm9kZS5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gbS5Db2xvcihNQVBfTUFTS19JVEVNX0NPTE9SW3NlYXNvblR5cGVdKSlcbiAgICB9XG5cbiAgICAvLyDnu5jliLblnLDlm75cbiAgICBwcml2YXRlIHVwZGF0ZU1hcChjZW50cmU6IGNjLlZlYzIpIHtcbiAgICAgICAgdGhpcy5wcmVDYW1lcmFab29tUmF0aW8gPSBjYW1lcmFDdHJsLnpvb21SYXRpb1xuICAgICAgICB0aGlzLmNlbnRyZS5zZXQoY2VudHJlKVxuICAgICAgICB0aGlzLm1vZGVsLnNldENlbnRyZShjZW50cmUpXG4gICAgICAgIC8vIOe7mOWItuWcsOmdolxuICAgICAgICBjb25zdCBhcm15RGlzdE1hcCA9IHRoaXMucGxheWVyLmdldEFybXlEaXN0TWFwKCksIGJhdHRsZURpc3QgPSB0aGlzLm1vZGVsLmdldEJhdHRsZURpc3RNYXAoKVxuICAgICAgICBjb25zdCBtYXBGbGFnID0gZ2FtZUhwci5hbGxpYW5jZS5nZXRNYXBGbGFnKCksIG1hcE1hcmsgPSB0aGlzLnBsYXllci5nZXRNYXBNYXJrcygpXG4gICAgICAgIGNvbnN0IGJ0Q2l0eU1hcCA9IHRoaXMubW9kZWwuZ2V0QlRDaXR5UXVldWVNYXAoKVxuICAgICAgICBjb25zdCB0b25kZW5NYXAgPSB0aGlzLm1vZGVsLmdldFRvbmRlblF1ZXVlTWFwKClcbiAgICAgICAgbGV0IGRpID0gMCwgbGluZWkgPSAwLCBsaSA9IDAsIG1pID0gMCwgaWkgPSAwLCB0aSA9IDAsIGJpID0gMCwgbWZpID0gMCwgbW1pID0gMCwgb2kgPSAwLCBtdGkgPSAwLCBwbGkgPSAwLCBkdGkgPSAwXG4gICAgICAgIGNvbnN0IHRleHRzOiBNYXBDZWxsT2JqW10gPSBbXSwgdG9uZGVuczogYW55W10gPSBbXSwgYnRDaXR5czogYW55W10gPSBbXSwgYW5jaWVudFRleHRzOiBBbmNpZW50T2JqW10gPSBbXVxuICAgICAgICB0aGlzLmNpdHlBbmltTm9kZVBvb2wucmVzZXQoKVxuICAgICAgICB0aGlzLnRlbXBTaG93Q2VsbE1hcCA9IHt9XG4gICAgICAgIGNvbnN0IGRyYXdQcm90ZWN0T3duZXJNYXA6IHsgW2tleTogc3RyaW5nXTogYm9vbGVhbiB9ID0ge31cbiAgICAgICAgY29uc3QgcG9pbnRzID0gbWFwSGVscGVyLmdldFJhbmdlUG9pbnRzQnlQb2ludChjZW50cmUsIHRoaXMubW9kZWwuZ2V0TWF4VGlsZVJhbmdlKCkpXG4gICAgICAgIGNvbnN0IHNlYXNvblR5cGUgPSB0aGlzLnNlYXNvblR5cGVcbiAgICAgICAgbGV0IHRlbXBEZWNvcmF0aW9uTG9hZE1hcCA9IHt9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcG9pbnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBjb25zdCBwb2ludCA9IHBvaW50c1tpXSwgY2VsbCA9IHRoaXMubW9kZWwuZ2V0TWFwQ2VsbEJ5UG9pbnQocG9pbnQpXG4gICAgICAgICAgICBjb25zdCBwb3NpdGlvbiA9IGNlbGw/LnBvc2l0aW9uIHx8IG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQocG9pbnQpXG4gICAgICAgICAgICBpZiAoY2VsbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJ0SW5mbyA9IGJ0Q2l0eU1hcFtjZWxsLmluZGV4XSwgdG9uZGVuSW5mbyA9IHRvbmRlbk1hcFtjZWxsLmluZGV4XSwgYWN0UG9zaXRpb24gPSBjZWxsLmFjdFBvc2l0aW9uXG4gICAgICAgICAgICAgICAgY29uc3QgcHJvdGVjdE93bmVyID0gY2VsbC5nZXRQcm90ZWN0T3duZXIoKVxuICAgICAgICAgICAgICAgIHRoaXMudGVtcFNob3dDZWxsTWFwW2NlbGwuaW5kZXhdID0gY2VsbFxuICAgICAgICAgICAgICAgIGlmIChjZWxsLmNpdHlJZCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYW5pbU5hbWUgPSBjZWxsLmNpdHlJZCA9PT0gQ0lUWV9GT1JUX05JRCA/ICdjaXR5XzIxMDJfJyArIGNlbGwuZ2V0T3duVHlwZSgpIDogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNpdHkgPSB0aGlzLmNpdHlBbmltTm9kZVBvb2wuc2hvd05vZGUoY2VsbC5nZXRDaXR5Vmlld0lkKCksIGFjdFBvc2l0aW9uLCB0cnVlLCBhbmltTmFtZSlcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNlbGwuY2l0eUlkID09PSBDSVRZX01BSU5fTklEKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyDov5nph4zlhYjojrflj5blkI7pnaLnlKjmnaXmmL7npLrmlofmnKxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRleHRzLnB1c2goY2VsbClcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjZWxsLmlzQW5jaWVudCgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpbmZvID0gdGhpcy5tb2RlbC5nZXRBbmNpZW50SW5mbyhjZWxsLmluZGV4KVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGluZm8pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmNpZW50VGV4dHMucHVzaChpbmZvKSAvL+mBl+i/uVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNpdHkuQ2hpbGQoJ3ZhbCcsIGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKGluZm8ubHYgPT09IDIwKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChjZWxsLmNpdHlJZCA8IDApIHtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRvbmRlbkluZm8pIHsgLy/nu5jliLblsa/nlLDlnLBcbiAgICAgICAgICAgICAgICAgICAgdG9uZGVucy5wdXNoKHsgdG9uZGVuSW5mbywgY2VsbCB9KVxuICAgICAgICAgICAgICAgICAgICByZXNIZWxwZXIuZ2V0Tm9kZUJ5SW5kZXgodGhpcy5sYW5kTm9kZSwgbGkrKywgcG9zaXRpb24pLkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJdGVtSWNvbignbGFuZF90b25kZW4nLCBzZWFzb25UeXBlKVxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoY2VsbC5pY29uICYmICghYnRJbmZvIHx8IGJ0SW5mby5pZCA9PT0gMCkpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNlbGwubGFuZFR5cGUgPT09IExhbmRUeXBlLlNFQSB8fCBjZWxsLmxhbmRUeXBlID09PSBMYW5kVHlwZS5CRUFDSCB8fCBtYXBVaW9uRmluZEhlbHBlci5nZXRJc1VuTG9jayhjZWxsLmluZGV4KSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW1OZG9lID0gcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMubW91bnRhaW5Ob2RlLCBtdGkrKywgY2VsbC5wb3NpdGlvbilcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW1OZG9lLkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJdGVtSWNvbihjZWxsLmljb24sIHRoaXMubW9kZWwuZ2V0U2Vhc29uVHlwZSgpKVxuICAgICAgICAgICAgICAgICAgICAgICAgaXRlbU5kb2UuekluZGV4ID0gMFxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMubGFuZE5vZGUsIGxpKyssIGNlbGwucG9zaXRpb24pLkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJdGVtSWNvbihjZWxsLmljb24sIHRoaXMubW9kZWwuZ2V0U2Vhc29uVHlwZSgpKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIOe7mOWItuS/ruW7uuS/oeaBr1xuICAgICAgICAgICAgICAgIGlmIChidEluZm8pIHtcbiAgICAgICAgICAgICAgICAgICAgYnRDaXR5cy5wdXNoKHsgYnRJbmZvLCBjZWxsIH0pXG4gICAgICAgICAgICAgICAgICAgIC8vIOWPque7mOWItuS/ruW7uiDkuI3nu5jliLbmi4bpmaRcbiAgICAgICAgICAgICAgICAgICAgaWYgKGNlbGwuY2l0eUlkID09PSAwICYmIGJ0SW5mby5pZCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2l0eUFuaW1Ob2RlUG9vbC5zaG93Tm9kZShidEluZm8uaWQsIGFjdFBvc2l0aW9uLCBmYWxzZSlcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDnu5jliLblnLDlm77lhpvpmJ/liIbluIPlm77moIdcbiAgICAgICAgICAgICAgICBpZiAoISFhcm15RGlzdE1hcFtjZWxsLmluZGV4XSkge1xuICAgICAgICAgICAgICAgICAgICAvLyDkuIvpnaLmmK/lkKbkuLvln45cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeSA9ICghY2VsbC5pc01haW5DaXR5KCkgJiYgdGhpcy5tb2RlbC5nZXRNYXBDZWxsQnlQb2ludCh0aGlzLl90ZW1wX3ZlYzJfMi5zZXQyKGNlbGwucG9pbnQueCwgY2VsbC5wb2ludC55IC0gMSkpPy5pc01haW5DaXR5KCkpID8gLTYgOiAtMjJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcG9zID0gdGhpcy5fdGVtcF92ZWMyXzMuc2V0MigtMjIsIHkpLmFkZFNlbGYoY2VsbC5wb3NpdGlvbikgLy/mmL7npLrliLDlt6bkuIvop5JcbiAgICAgICAgICAgICAgICAgICAgcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMuaWNvbk5vZGUsIGlpKyssIHBvcykuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSByZXNIZWxwZXIuZ2V0TGFuZEljb24oJ2FybXlfbWluX2ljb24nKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDnu5jliLblhY3miJjlm77moIdcbiAgICAgICAgICAgICAgICBpZiAoY2VsbC5pc0NhblNob3dBdm9pZFdhcigpKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOS4i+mdouaYr+WQpuS4u+WfjlxuICAgICAgICAgICAgICAgICAgICBjb25zdCB5ID0gKCFjZWxsLmlzTWFpbkNpdHkoKSAmJiB0aGlzLm1vZGVsLmdldE1hcENlbGxCeVBvaW50KHRoaXMuX3RlbXBfdmVjMl8yLnNldDIoY2VsbC5wb2ludC54LCBjZWxsLnBvaW50LnkgLSAxKSk/LmlzTWFpbkNpdHkoKSkgPyAtNiA6IC0yMlxuICAgICAgICAgICAgICAgICAgICBjb25zdCBwb3MgPSB0aGlzLl90ZW1wX3ZlYzJfMy5zZXQyKDIyLCB5KS5hZGRTZWxmKGNlbGwuZ2V0UmlnaHRQb3NpdGlvbih0aGlzLl90ZW1wX3ZlYzJfNSkpIC8v5pi+56S65Yiw5Y+z5LiL6KeSXG4gICAgICAgICAgICAgICAgICAgIHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLmljb25Ob2RlLCBpaSsrLCBwb3MpLkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJY29uKCdhdm9pZHdhcl9pY29uXzEnKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDnu5jliLbmiJjmlpflm77moIdcbiAgICAgICAgICAgICAgICBpZiAoISFiYXR0bGVEaXN0W2NlbGwuaW5kZXhdKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLmJhdHRsZU5vZGUsIGJpKyssIGFjdFBvc2l0aW9uKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDmoIforrBcbiAgICAgICAgICAgICAgICBjb25zdCBmbGFnID0gbWFwRmxhZ1tjZWxsLmluZGV4XSwgbWFyayA9IG1hcE1hcmtbY2VsbC5pbmRleF1cbiAgICAgICAgICAgICAgICBpZiAobWFyaykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwb3MgPSBmbGFnID8gdGhpcy5fdGVtcF92ZWMyXzYuc2V0MihhY3RQb3NpdGlvbi54IC0gMTYsIGFjdFBvc2l0aW9uLnkpIDogYWN0UG9zaXRpb25cbiAgICAgICAgICAgICAgICAgICAgcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMubWFwTWFya05vZGUsIG1taSsrLCBwb3MpLkNoaWxkKCdyb290L3ZhbCcsIGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSByZXNIZWxwZXIuZ2V0TWFwRmxhZ051bWJlcihtYXJrLmZsYWcpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChmbGFnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBvcyA9IG1hcmsgPyB0aGlzLl90ZW1wX3ZlYzJfNy5zZXQyKGFjdFBvc2l0aW9uLnggKyAxNiwgYWN0UG9zaXRpb24ueSkgOiBhY3RQb3NpdGlvblxuICAgICAgICAgICAgICAgICAgICByZXNIZWxwZXIuZ2V0Tm9kZUJ5SW5kZXgodGhpcy5tYXBGbGFnTm9kZSwgbWZpKyssIHBvcykuQ2hpbGQoJ3Jvb3QvdmFsJywgY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRNYXBGbGFnTnVtYmVyKGZsYWcuZmxhZylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8g57uY5Yi25Lqn5Ye6XG4gICAgICAgICAgICAgICAgY29uc3Qgb3V0cHV0ID0gY2VsbC5nZXRPdXRwdXRUeXBlKClcbiAgICAgICAgICAgICAgICBpZiAob3V0cHV0KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG9Ob2RlID0gcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMub3V0cHV0Tm9kZSwgb2krKywgYWN0UG9zaXRpb24pXG4gICAgICAgICAgICAgICAgICAgIG9Ob2RlLkRhdGEgPSBjZWxsXG4gICAgICAgICAgICAgICAgICAgIG9Ob2RlLkNoaWxkKCdjaXR5X291dHB1dF9iZScpLmFjdGl2ZSA9IGNlbGwuaXNPd24oKVxuICAgICAgICAgICAgICAgICAgICBvTm9kZS5DaGlsZCgncm9vdC9pY29uJywgY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IGFzc2V0c01nci5nZXRJbWFnZShDVFlQRV9JQ09OW291dHB1dC50eXBlXSlcbiAgICAgICAgICAgICAgICAgICAgb05vZGUuQ2hpbGQoJ3Jvb3QvdmFsJywgY2MuTGFiZWwpLnN0cmluZyA9IG91dHB1dC5jb3VudCA+IDEgPyBvdXRwdXQuY291bnQgKyAnJyA6ICcnXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIOe7mOWItuS/neaKpOaooeW8j+S4i+eahOi+ueahhue6v1xuICAgICAgICAgICAgICAgIGlmIChwcm90ZWN0T3duZXIgJiYgIWRyYXdQcm90ZWN0T3duZXJNYXBbcHJvdGVjdE93bmVyXSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtYWluQ2VsbCA9IHRoaXMubW9kZWwuZ2V0TWFwQ2VsbEJ5SW5kZXgoZ2FtZUhwci5nZXRQbGF5ZXJNYWluQ2l0eUluZGV4KHByb3RlY3RPd25lcikpXG4gICAgICAgICAgICAgICAgICAgIGlmIChtYWluQ2VsbCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZHJhd1Byb3RlY3RPd25lck1hcFtwcm90ZWN0T3duZXJdID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgLy8g5piv5ZCm5pyJ5L+d5oqk5qih5byPIOe7mOWItuS/neaKpOe6v1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhdGUgPSBnYW1lSHByLmNoZWNrUGxheWVyUHJvdGVjdE1vZGVTdGF0ZShwcm90ZWN0T3duZXIpXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc3RhdGUgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMucHJvdGVjdExpbmVOb2RlLCBwbGkrKywgbWFpbkNlbGwuYWN0UG9zaXRpb24pLm9wYWNpdHkgPSBzdGF0ZSA9PT0gMSA/IDI1NSA6IDEwMFxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIOiusOW9lei+ueahhue6v1xuICAgICAgICAgICAgICAgIGNvbnN0IGJvcmRlckxpbmVzID0gY2VsbC5vd25lciA/IGNlbGwuYm9yZGVyTGluZXMgOiBbXVxuICAgICAgICAgICAgICAgIGlmIChib3JkZXJMaW5lcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpbmVJdGVtTm9kZSA9IHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLmxpbmVOb2RlLCBsaW5laSsrLCBwb3NpdGlvbilcbiAgICAgICAgICAgICAgICAgICAgdmlld0hlbHBlci51cGRhdGVDZWxsQm9yZGVyTGluZXMobGluZUl0ZW1Ob2RlLCBib3JkZXJMaW5lcywgY2VsbC5nZXRCb3JkZXJMaW5lQ29sb3IoKSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8g57uY5Yi26YGu572pXG4gICAgICAgICAgICAgICAgaWYgKCFjZWxsLm93bmVyICYmIGNlbGwubGFuZFR5cGUgIT09IExhbmRUeXBlLlNFQSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXNrSXRlbU5vZGUgPSByZXNIZWxwZXIuZ2V0Tm9kZUJ5SW5kZXgodGhpcy5tYXNrTm9kZSwgbWkrKywgcG9zaXRpb24pXG4gICAgICAgICAgICAgICAgICAgIG1hc2tJdGVtTm9kZS5vcGFjaXR5ID0gcHJvdGVjdE93bmVyID8gTUFQX01BU0tfSVRFTV9PUEFDSVRZX0dPTERbc2Vhc29uVHlwZV0gOiBNQVBfTUFTS19JVEVNX09QQUNJVFlbc2Vhc29uVHlwZV1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy/nu5jliLblnLDlm77oo4XppbBcbiAgICAgICAgICAgICAgICBsZXQgZGVjb3JhdGlvbkxpc3QgPSB0aGlzLm1vZGVsLmdldERlY29yYXRpb25CeUluZGV4KGNlbGwuaW5kZXgpXG4gICAgICAgICAgICAgICAgaWYgKGRlY29yYXRpb25MaXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGVjb3JhdGlvbkxpc3QubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZGVjb3JhdGlvbkpzb24sIGRlY29yYXRpb25BY3RJbmRleCB9ID0gZGVjb3JhdGlvbkxpc3RbaV1cbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCB0ZW1wS2V5ID0gZGVjb3JhdGlvbkFjdEluZGV4ICsgJ18nICsgZGVjb3JhdGlvbkpzb24uaWRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghdGVtcERlY29yYXRpb25Mb2FkTWFwW3RlbXBLZXldKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGVtcERlY29yYXRpb25Mb2FkTWFwW3RlbXBLZXldID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGljb25OYW1lID0gdGhpcy5tb2RlbC5nZXREZWNvcmF0aW9uSWNvbihkZWNvcmF0aW9uSnNvbiwgZGVjb3JhdGlvbkFjdEluZGV4KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJdGVtSWNvbihpY29uTmFtZSwgdGhpcy5tb2RlbC5nZXRTZWFzb25UeXBlKCkpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYWN0UG9zaXRpb24gPSB0aGlzLm1vZGVsLmdldE1hcENlbGxCeUluZGV4KGRlY29yYXRpb25BY3RJbmRleCkuYWN0UG9zaXRpb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbU5vZGU6IGNjLk5vZGUgPSBudWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRlY29yYXRpb25Kc29uLnR5cGUgPT0gRGVjb3JhdGlvblR5cGUuU2hhZG93IHx8IGRlY29yYXRpb25Kc29uLnR5cGUgPT0gRGVjb3JhdGlvblR5cGUuTVVEIHx8IGRlY29yYXRpb25Kc29uLnR5cGUgPT0gRGVjb3JhdGlvblR5cGUuTVVEX09VVEVSKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW1Ob2RlID0gcmVzSGVscGVyLmdldE5vZGVCeUluZGV4KHRoaXMuZGlOb2RlLCBkaSsrLCBhY3RQb3NpdGlvbilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG1hcFVpb25GaW5kSGVscGVyLmdldElzVW5Mb2NrKGRlY29yYXRpb25BY3RJbmRleCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbU5vZGUgPSByZXNIZWxwZXIuZ2V0Tm9kZUJ5SW5kZXgodGhpcy5tb3VudGFpbk5vZGUsIG10aSsrLCBhY3RQb3NpdGlvbilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtTm9kZSA9IHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLmRlY29yYXRpb25Ob2RlLCBkdGkrKywgYWN0UG9zaXRpb24pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCB6SW5kZXggPSAxXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3dpdGNoIChkZWNvcmF0aW9uSnNvbi50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgRGVjb3JhdGlvblR5cGUuTU9VTlRBSU46XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgRGVjb3JhdGlvblR5cGUuTUFJTl9DSVRZX1JPVU5EX01PVU5UQUlOOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIERlY29yYXRpb25UeXBlLk1BSU5fQ0lUWV9ST1VORF9MQUtFOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgekluZGV4ID0gY2MubWFjcm8uTUFYX1pJTkRFWFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgRGVjb3JhdGlvblR5cGUuU2hhZG93OlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgekluZGV4ID0gMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW1Ob2RlLnpJbmRleCA9IHpJbmRleFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW1Ob2RlLkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gZnJhbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlc2V0RGVjb3JhdGlvblBvaW50KGl0ZW1Ob2RlLCBpY29uTmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBsZXQgbGFuZElkID0gdGhpcy5tb2RlbC5nZXRSb3VuZElkKHBvaW50LngsIHBvaW50LnkpXG4gICAgICAgICAgICAgICAgaWYgKGxhbmRJZCkge1xuICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbUluZm8gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ2xhbmQnLCBsYW5kSWQpXG4gICAgICAgICAgICAgICAgICAgIGxldCBpdGVtTm9kZSA9IHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLm1vdW50YWluTm9kZSwgbXRpKyssIHBvc2l0aW9uKVxuICAgICAgICAgICAgICAgICAgICBpdGVtTm9kZS5Db21wb25lbnQoY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRMYW5kSXRlbUljb24oaXRlbUluZm8uaWNvbiwgdGhpcy5tb2RlbC5nZXRTZWFzb25UeXBlKCkpXG4gICAgICAgICAgICAgICAgICAgIGl0ZW1Ob2RlLnpJbmRleCA9IDBcbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW1JbmZvLnR5cGUgPT0gTGFuZFR5cGUuQkVBQ0gpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLm1hc2tOb2RlLCBtaSsrLCBwb3NpdGlvbilcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyDpmpDol4/lpJrkvZnnmoRcbiAgICAgICAgcmVzSGVscGVyLmhpZGVOb2RlQnlJbmRleCh0aGlzLmRlY29yYXRpb25Ob2RlLCBkdGkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5kaU5vZGUsIGRpKVxuICAgICAgICByZXNIZWxwZXIuaGlkZU5vZGVCeUluZGV4KHRoaXMucHJvdGVjdExpbmVOb2RlLCBwbGkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5saW5lTm9kZSwgbGluZWkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5sYW5kTm9kZSwgbGkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5tYXNrTm9kZSwgbWkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5pY29uTm9kZSwgaWkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5iYXR0bGVOb2RlLCBiaSlcbiAgICAgICAgcmVzSGVscGVyLmhpZGVOb2RlQnlJbmRleCh0aGlzLm1hcEZsYWdOb2RlLCBtZmkpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5tYXBNYXJrTm9kZSwgbW1pKVxuICAgICAgICByZXNIZWxwZXIuaGlkZU5vZGVCeUluZGV4KHRoaXMub3V0cHV0Tm9kZSwgb2kpXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5tb3VudGFpbk5vZGUsIG10aSlcbiAgICAgICAgLy8gdGhpcy5zZWF3YXZlQW5pbU5vZGVQb29sPy5oaWRlT3RoZXJOb2RlKClcbiAgICAgICAgdGhpcy5jaXR5QW5pbU5vZGVQb29sLmhpZGVPdGhlck5vZGUoKVxuICAgICAgICAvLyDlvZPliY3mraPlnKjmmL7npLrnmoRcbiAgICAgICAgY29uc3Qgc2hvd0luZGV4ID0gdGhpcy5jZWxsSW5mb0NtcHQuZ2V0Q2VsbCgpPy5hY3RJbmRleCA/PyAtMVxuICAgICAgICBjb25zdCB6SW5kZXhNYXhZID0gbWFwSGVscGVyLk1BUF9TSVpFLnlcbiAgICAgICAgY29uc3QgaXNDYW5TaG93VGV4dCA9ICF0aGlzLnRvdWNoQ21wdC5pc0RyYWdpbmcoKVxuICAgICAgICAvLyDnu5jliLbmlofmnKzlsYJcbiAgICAgICAgdGhpcy50ZXh0Tm9kZV8uSXRlbXModGV4dHMsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgcG9zID0gZGF0YS5hY3RQb3NpdGlvbiwgaW5kZXggPSBkYXRhLmFjdEluZGV4XG4gICAgICAgICAgICBjb25zdCBkID0gaXQuRGF0YVxuICAgICAgICAgICAgY29uc3QgaW5mbyA9IGdhbWVIcHIuZ2V0UGxheWVySW5mbyhkYXRhLm93bmVyKVxuICAgICAgICAgICAgaXQuc2V0UG9zaXRpb24ocG9zLngsIHBvcy55ICsgNzYpXG4gICAgICAgICAgICBpZiAoIWQgfHwgZC5vd25lciAhPT0gZGF0YS5vd25lciB8fCBkLm5pY2tuYW1lICE9PSBpbmZvPy5uaWNrbmFtZSB8fCBkLnRpdGxlICE9PSBpbmZvPy50aXRsZSB8fCBkLmhlYWRJY29uICE9PSBpbmZvPy5oZWFkSWNvbikge1xuICAgICAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkUGxheWVySGVhZChpdC5DaGlsZCgnaGVhZCcsIGNjLlNwcml0ZSksIGluZm8/LmhlYWRJY29uLCB0aGlzLklOSVRfS0VZKVxuICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlUGxheWVyTmlja25hbWUoaXQsIGluZm8pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpdC5hY3RpdmUgPSBzaG93SW5kZXggIT09IGluZGV4XG4gICAgICAgICAgICBpdC5EYXRhID0geyBpbmRleCwgb3duZXI6IGRhdGEub3duZXIsIG5pY2tuYW1lOiBpbmZvPy5uaWNrbmFtZSwgdGl0bGU6IGluZm8/LnRpdGxlLCBoZWFkSWNvbjogaW5mbz8uaGVhZEljb24gfVxuICAgICAgICB9KVxuICAgICAgICB0aGlzLnRleHROb2RlXy5hY3RpdmUgPSBpc0NhblNob3dUZXh0XG4gICAgICAgIC8vIOe7mOWItumBl+i/ueaWh+acrOWxglxuICAgICAgICB0aGlzLmFuY2llbnRUZXh0Tm9kZV8uSXRlbXMoYW5jaWVudFRleHRzLCAoaXQsIGRhdGEpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHBvcyA9IGRhdGEuY2VsbC5hY3RQb3NpdGlvbiwgaW5kZXggPSBkYXRhLmluZGV4XG4gICAgICAgICAgICBpdC5zZXRQb3NpdGlvbihwb3MueCwgcG9zLnkgKyA4MClcbiAgICAgICAgICAgIHRoaXMudXBkYXRlQW5jaWVudFRleHRJbmZvKGl0LCBkYXRhKVxuICAgICAgICAgICAgaXQuYWN0aXZlID0gc2hvd0luZGV4ICE9PSBpbmRleFxuICAgICAgICAgICAgaXQuRGF0YSA9IHsgaW5kZXggfVxuICAgICAgICB9KVxuICAgICAgICB0aGlzLmFuY2llbnRUZXh0Tm9kZV8uYWN0aXZlID0gaXNDYW5TaG93VGV4dFxuICAgICAgICAvLyDnu5jliLbkv67lu7rln47luILnmoTkv6Hmga9cbiAgICAgICAgdGhpcy5idGluZm9Ob2RlLkl0ZW1zKGJ0Q2l0eXMsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgaW5mbzogQlRDaXR5T2JqID0gZGF0YS5idEluZm8sIGluZGV4ID0gaW5mby5pbmRleFxuICAgICAgICAgICAgaXQuc2V0UG9zaXRpb24oZGF0YS5jZWxsLmFjdFBvc2l0aW9uKVxuICAgICAgICAgICAgY29uc3QgdGltZU5vZGUgPSBpdC5DaGlsZCgndGltZScpXG4gICAgICAgICAgICBpZiAoaXQuRGF0YT8uaW5kZXggIT09IGluZGV4KSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3VycGx1c1RpbWUgPSBpbmZvLmdldFN1cnBsdXNUaW1lKClcbiAgICAgICAgICAgICAgICB0aW1lTm9kZS5Db2xvcihpbmZvLmlkID8gJyMyMURDMkQnIDogJyNGRjkxNjInKVxuICAgICAgICAgICAgICAgIHRpbWVOb2RlLkNvbXBvbmVudChjYy5MYWJlbFRpbWVyKS5ydW4oc3VycGx1c1RpbWUgKiAwLjAwMSlcbiAgICAgICAgICAgICAgICAvLyDliqjnlLtcbiAgICAgICAgICAgICAgICBjb25zdCBhbmltID0gaXQuQ2hpbGQoJ2FuaW0nLCBjYy5BbmltYXRpb24pXG4gICAgICAgICAgICAgICAgY29uc3QgZWxhcHNlZFRpbWUgPSBNYXRoLm1heCgwLCBpbmZvLm5lZWRUaW1lIC0gc3VycGx1c1RpbWUpICogMC4wMDFcbiAgICAgICAgICAgICAgICBjb25zdCB0d2VlbiA9IGNjLnR3ZWVuKGl0KVxuICAgICAgICAgICAgICAgIHR3ZWVuLnN0b3AoKVxuICAgICAgICAgICAgICAgIGlmIChlbGFwc2VkVGltZSA8IDAuNjIpIHtcbiAgICAgICAgICAgICAgICAgICAgYW5pbS5wbGF5KCdjdGluZ19iZWdpbicsIGVsYXBzZWRUaW1lKVxuICAgICAgICAgICAgICAgICAgICB0d2Vlbi5kZWxheSgwLjYyIC0gZWxhcHNlZFRpbWUpLmNhbGwoKCkgPT4gdGhpcy5pc1ZhbGlkICYmIGFuaW0ucGxheSgnY3RpbmdfbG9vcCcpKS5zdGFydCgpXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgYW5pbS5wbGF5KCdjdGluZ19sb29wJylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aW1lTm9kZS5hY3RpdmUgPSBpc0NhblNob3dUZXh0ICYmIHNob3dJbmRleCAhPT0gaW5kZXhcbiAgICAgICAgICAgIGl0LkRhdGEgPSB7IGluZGV4IH1cbiAgICAgICAgICAgIGl0LnpJbmRleCA9IHpJbmRleE1heFkgLSBkYXRhLmNlbGwuYWN0UG9pbnQueVxuICAgICAgICB9KVxuICAgICAgICAvLyDnu5jliLblsa/nlLDkv6Hmga9cbiAgICAgICAgdGhpcy50b25kZW5Ob2RlLkl0ZW1zKHRvbmRlbnMsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgaW5mbzogVG9uZGVuT2JqID0gZGF0YS50b25kZW5JbmZvLCBpbmRleCA9IGluZm8uaW5kZXhcbiAgICAgICAgICAgIGl0LnNldFBvc2l0aW9uKGRhdGEuY2VsbC5hY3RQb3NpdGlvbilcbiAgICAgICAgICAgIGNvbnN0IHRpbWVOb2RlID0gaXQuQ2hpbGQoJ3RpbWUnKVxuICAgICAgICAgICAgaWYgKGl0LkRhdGE/LmluZGV4ICE9PSBpbmRleCkge1xuICAgICAgICAgICAgICAgIHRpbWVOb2RlLkNvbXBvbmVudChjYy5MYWJlbFRpbWVyKS5ydW4oaW5mby5nZXRTdXJwbHVzVGltZSgpICogMC4wMDEpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aW1lTm9kZS5hY3RpdmUgPSBpc0NhblNob3dUZXh0XG4gICAgICAgICAgICBpdC5EYXRhID0geyBpbmRleCB9XG4gICAgICAgICAgICBpdC56SW5kZXggPSB6SW5kZXhNYXhZIC0gZGF0YS5jZWxsLmFjdFBvaW50LnlcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICAvL+iuvue9ruijhemlsOWBj+enu1xuICAgIHB1YmxpYyByZXNldERlY29yYXRpb25Qb2ludChpdGVtTm9kZTogY2MuTm9kZSwgaWNvbjogc3RyaW5nKSB7XG4gICAgICAgIGxldCBmcmFtZSA9IHJlc0hlbHBlci5nZXRMYW5kSXRlbUljb24oaWNvbiwgdGhpcy5tb2RlbC5nZXRTZWFzb25UeXBlKCkpXG4gICAgICAgIGlmICghZnJhbWUpIHtcbiAgICAgICAgICAgIGNjLmVycm9yKCdyZXNldERlY29yYXRpb25Qb2ludCBpY29uOiAnICsgaWNvbiArICcsIHNlYXNvbjogJyArIHRoaXMubW9kZWwuZ2V0U2Vhc29uVHlwZSgpKVxuICAgICAgICB9XG4gICAgICAgIGxldCBzaXplID0gZnJhbWUuZ2V0T3JpZ2luYWxTaXplKClcbiAgICAgICAgaWYgKHNpemUud2lkdGggLyBUSUxFX1NJWkUgJSAyID09IDApIHtcbiAgICAgICAgICAgIGl0ZW1Ob2RlLnggKz0gVElMRV9TSVpFIC8gMlxuICAgICAgICB9XG4gICAgICAgIGlmIChzaXplLmhlaWdodCAvIFRJTEVfU0laRSAlIDIgPT0gMCkge1xuICAgICAgICAgICAgaXRlbU5vZGUueSArPSBUSUxFX1NJWkUgLyAyXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyAwLuS4iiAxLuWPsyAyLuS4iyAzLuW3piA0LuW3puS4iiA1LuWPs+S4iiA2LuWPs+S4iyA3LuW3puS4i1xuICAgIHByaXZhdGUgZ2V0U2VhTGFuZEljb24ocG9pbnQ6IGNjLlZlYzIsIG1pbng6IG51bWJlciwgbWlueTogbnVtYmVyLCBtYXh4OiBudW1iZXIsIG1heHk6IG51bWJlcikge1xuICAgICAgICBpZiAocG9pbnQueCA8IG1pbngpIHtcbiAgICAgICAgICAgIHJldHVybiBwb2ludC55IDwgbWlueSA/IDcgOiAocG9pbnQueSA8IG1heHkgPyAzIDogNClcbiAgICAgICAgfSBlbHNlIGlmIChwb2ludC54IDwgbWF4eCkge1xuICAgICAgICAgICAgcmV0dXJuIHBvaW50LnkgPCBtaW55ID8gMiA6IDBcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcG9pbnQueSA8IG1pbnkgPyA2IDogKHBvaW50LnkgPCBtYXh5ID8gMSA6IDUpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBzZXRTZWFMYW5kKGl0OiBjYy5Ob2RlLCB0eXBlOiBzdHJpbmcsIHBvaW50OiBjYy5WZWMyLCBtaW54OiBudW1iZXIsIG1pbnk6IG51bWJlciwgbWF4eDogbnVtYmVyLCBtYXh5OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgZGlyID0gdGhpcy5nZXRTZWFMYW5kSWNvbihwb2ludCwgbWlueCwgbWlueSwgbWF4eCwgbWF4eSlcbiAgICAgICAgaXQuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSB0aGlzLmdldExhbmRJY29uKGAke3R5cGV9XyR7TWF0aC5taW4oZGlyLCA0KX1gKVxuICAgIH1cblxuICAgIC8vIOa1t+a1qlxuICAgIHByaXZhdGUgc2V0U2Vhd2F2ZUxhbmQocG9zaXRpb246IGNjLlZlYzIsIHBvaW50OiBjYy5WZWMyLCBtaW54OiBudW1iZXIsIG1pbnk6IG51bWJlciwgbWF4eDogbnVtYmVyLCBtYXh5OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgZGlyID0gdGhpcy5nZXRTZWFMYW5kSWNvbihwb2ludCwgbWlueCwgbWlueSwgbWF4eCwgbWF4eSksIG5vID0gTWF0aC5mbG9vcihkaXIgLyA0KSArIDEsIGFuZ2xlID0gZGlyICUgNFxuICAgICAgICBjb25zdCBpdCA9IHRoaXMuc2Vhd2F2ZUFuaW1Ob2RlUG9vbC5zaG93Tm9kZShubywgcG9zaXRpb24sIHRydWUpXG4gICAgICAgIGl0LmFuZ2xlID0gYW5nbGUgKiAtOTBcbiAgICB9XG5cbiAgICBwcml2YXRlIGdldExhbmRJY29uKGljb246IHN0cmluZykge1xuICAgICAgICByZXR1cm4gcmVzSGVscGVyLmdldExhbmRJY29uKGljb24pXG4gICAgfVxuXG4gICAgcHJpdmF0ZSB1cGRhdGVQbGF5ZXJOaWNrbmFtZShpdDogY2MuTm9kZSwgZGF0YTogUGxheWVySW5mbykge1xuICAgICAgICBjb25zdCBuYW1lTGJsID0gaXQuQ2hpbGQoJ25hbWUvdmFsJywgY2MuTGFiZWwpXG4gICAgICAgIG5hbWVMYmwuc3RyaW5nID0gdXQubmFtZUZvcm1hdG9yKGRhdGE/Lm5pY2tuYW1lID8/ICc/Pz8nLCA3KVxuICAgICAgICBjb25zdCB0aXRsZUxibCA9IGl0LkNoaWxkKCduYW1lL3RpdGxlJywgY2MuTGFiZWwpXG4gICAgICAgIGlmICh0aXRsZUxibC5zZXRBY3RpdmUoISFkYXRhPy50aXRsZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IGpzb24gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3RpdGxlJywgZGF0YS50aXRsZSlcbiAgICAgICAgICAgIHRpdGxlTGJsLkNvbG9yKEFDSElFVkVNRU5UX0NPTE9SW2pzb24/LnF1YWxpdHkgfHwgMV0pLnNldExvY2FsZUtleSgndGl0bGVUZXh0LicgKyBqc29uPy5pZClcbiAgICAgICAgICAgIG5hbWVMYmwubm9kZS55ID0gLTEwXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBuYW1lTGJsLm5vZGUueSA9IDBcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgdXBkYXRlQW5jaWVudFRleHRJbmZvKGl0OiBjYy5Ob2RlLCBkYXRhOiBBbmNpZW50T2JqKSB7XG4gICAgICAgIGl0LkNoaWxkKCduYW1lJykuc2V0TG9jYWxlS2V5KCd1aS5hbmNpZW50X25hbWVfdGV4dCcsIGRhdGEubmFtZSwgYXNzZXRzTWdyLmxhbmcoJ3VpLnNob3J0X2x2JywgZGF0YS5sdiB8fCAxKSlcbiAgICAgICAgaWYgKGl0LkNoaWxkKCd0aW1lJykuYWN0aXZlID0gZGF0YS5zdGF0ZSA9PT0gMSAmJiAhZGF0YS5wYXVzZVN0YXRlKSB7XG4gICAgICAgICAgICBpdC5DaGlsZCgndGltZScsIGNjLkxhYmVsVGltZXIpLnJ1bihkYXRhLmdldFN1cnBsdXNUaW1lKCkgKiAwLjAwMSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOaYvuekuuaWh+acrOiKgueCuVxuICAgIHByaXZhdGUgdXBkYXRlSGlkZVRleHRCeUluZGV4KGluZGV4OiBudW1iZXIgPSAtMSkge1xuICAgICAgICB0aGlzLnRleHROb2RlXy5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gbS5hY3RpdmUgPSAhIW0uRGF0YSAmJiBtLkRhdGEuaW5kZXggIT09IGluZGV4KVxuICAgICAgICB0aGlzLmFuY2llbnRUZXh0Tm9kZV8uY2hpbGRyZW4uZm9yRWFjaChtID0+IG0uYWN0aXZlID0gISFtLkRhdGEgJiYgbS5EYXRhLmluZGV4ICE9PSBpbmRleClcbiAgICAgICAgdGhpcy5idGluZm9Ob2RlLmNoaWxkcmVuLmZvckVhY2gobSA9PiBtLkRhdGEgJiYgKG0uQ2hpbGQoJ3RpbWUnKS5hY3RpdmUgPSBtLkRhdGEuaW5kZXggIT09IGluZGV4KSlcbiAgICB9XG5cbiAgICAvLyDliLfmlrDlm77moIflsYJcbiAgICBwcml2YXRlIHVwZGF0ZUljb25Ob2RlKCkge1xuICAgICAgICBjb25zdCBvZmZzZXQxOiBjYy5WZWMyID0gY2MudjIoLTIyLCAtMjIpXG4gICAgICAgIGNvbnN0IG9mZnNldDI6IGNjLlZlYzIgPSBjYy52MigyMiwgLTIyKVxuICAgICAgICBjb25zdCBjZWxsczogYW55W10gPSBbXSwgYXJteURpc3RNYXAgPSB0aGlzLnBsYXllci5nZXRBcm15RGlzdE1hcCgpXG4gICAgICAgIGZvciAobGV0IGtleSBpbiB0aGlzLnRlbXBTaG93Q2VsbE1hcCkge1xuICAgICAgICAgICAgY29uc3QgY2VsbDogTWFwQ2VsbE9iaiA9IHRoaXMudGVtcFNob3dDZWxsTWFwW2tleV1cbiAgICAgICAgICAgIGlmIChjZWxsLmlzQ2FuU2hvd0F2b2lkV2FyKCkpIHtcbiAgICAgICAgICAgICAgICBjZWxscy5wdXNoKHsgcG9zaXRpb246IGNlbGwuZ2V0UmlnaHRQb3NpdGlvbigpLCBvZmZzZXQ6IG9mZnNldDIsIGljb246ICdhdm9pZHdhcl9pY29uXzEnIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoYXJteURpc3RNYXBba2V5XSkge1xuICAgICAgICAgICAgICAgIGNlbGxzLnB1c2goeyBwb3NpdGlvbjogY2VsbC5wb3NpdGlvbiwgb2Zmc2V0OiBvZmZzZXQxLCBpY29uOiAnYXJteV9taW5faWNvbicgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmljb25Ob2RlLkl0ZW1zKGNlbGxzLCAoaXQsIGRhdGEpID0+IHtcbiAgICAgICAgICAgIGl0LnNldFBvc2l0aW9uKHRoaXMuX3RlbXBfdmVjMl8zLnNldChkYXRhLm9mZnNldCkuYWRkU2VsZihkYXRhLnBvc2l0aW9uKSlcbiAgICAgICAgICAgIGl0LkNvbXBvbmVudChjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldExhbmRJY29uKGRhdGEuaWNvbilcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDmmL7npLrpgInmi6nlnLDlnZdcbiAgICBwcml2YXRlIHNob3dTZWxlY3RDZWxsKGNlbGw6IE1hcENlbGxPYmopIHtcbiAgICAgICAgaWYgKCFjZWxsIHx8IGNlbGwubGFuZFR5cGUgPT0gTGFuZFR5cGUuU0VBIHx8IGNlbGwubGFuZFR5cGUgPT0gTGFuZFR5cGUuQkVBQ0gpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKGNlbGwuYWN0SW5kZXggIT09IGNlbGwuaW5kZXgpIHtcbiAgICAgICAgICAgIGNlbGwgPSB0aGlzLm1vZGVsLmdldE1hcENlbGxCeUluZGV4KGNlbGwuYWN0SW5kZXgpXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcG9zID0gdGhpcy5zZWxlY3RDZWxsTm9kZV8uRGF0YSA9IGNlbGwuYWN0UG9zaXRpb25cbiAgICAgICAgdGhpcy5zZWxlY3RDZWxsTm9kZV8uQ29tcG9uZW50KFNlbGVjdENlbGxDbXB0KS5vcGVuKHBvcywgY2VsbC5nZXRTaXplKCkpXG4gICAgICAgIHRoaXMuY2VsbEluZm9DbXB0Lm9wZW4ocG9zLCBjZWxsKVxuICAgICAgICAvLyDpmpDol4/mlofmnKzoioLngrlcbiAgICAgICAgdGhpcy51cGRhdGVIaWRlVGV4dEJ5SW5kZXgoY2VsbC5hY3RJbmRleClcbiAgICB9XG5cbiAgICAvLyDpmpDol49cbiAgICBwcml2YXRlIGhpZGVTZWxlY3RDZWxsKHBsYXk6IGJvb2xlYW4gPSB0cnVlKSB7XG4gICAgICAgIGlmICh0aGlzLnNlbGVjdENlbGxOb2RlXy5EYXRhKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdENlbGxOb2RlXy5Db21wb25lbnQoU2VsZWN0Q2VsbENtcHQpLmNsb3NlKClcbiAgICAgICAgICAgIHRoaXMuY2VsbEluZm9DbXB0LmNsb3NlKHBsYXkpXG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUhpZGVUZXh0QnlJbmRleCgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliJ3lp4vljJbooYzlhptcbiAgICBwcml2YXRlIGluaXRNYXJjaCgpIHtcbiAgICAgICAgdGhpcy5jbGVhbk1hcmNoKClcbiAgICAgICAgY29uc3QgbGlzdCA9IHRoaXMubW9kZWwuZ2V0QWxsTWFyY2hzKCkuZmlsdGVyKG0gPT4gbS5pc0NhblNob3dNYXJjaCgpKVxuICAgICAgICB0aGlzLm1hcmNoTGluZU5vZGVfLkl0ZW1zKGxpc3QsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgbWFyY2ggPSB0aGlzLm1hcmNocy5hZGQoaXQuQ29tcG9uZW50KE1hcmNoQ21wdCkuaW5pdChkYXRhLCB0aGlzLm1hcmNoUm9sZU5vZGVfLCB0aGlzLmtleSkpXG4gICAgICAgICAgICBtYXJjaC5pc0NoZWNrTGluZU9mZnNldCA9IGZhbHNlXG4gICAgICAgIH0pXG4gICAgICAgIHRoaXMubWFyY2hzLmZvckVhY2gobSA9PiAhbS5pc0NoZWNrTGluZU9mZnNldCAmJiB0aGlzLmNoZWNrTWFyY2hMaW5lT2Zmc2V0KG0uZ2V0RGF0YSgpKSlcbiAgICB9XG5cbiAgICBwcml2YXRlIGNsZWFuTWFyY2goKSB7XG4gICAgICAgIHdoaWxlICh0aGlzLm1hcmNocy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLm1hcmNocy5wb3AoKS5jbGVhbigpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5tYXJjaHMgPSBbXVxuICAgICAgICAvLyByZXNIZWxwZXIuY2xlYW5Ob2RlQ2hpbGRyZW4odGhpcy5tYXJjaExpbmVOb2RlXykgLy/ov5nkuKrms6jph4rkuoYg5LiN55+l6YGT5LuA5LmI5Y6f5Zug5Lya5Ye6546w6KGM5Yab57q/6KKr5raI6ICX55qE5oOF5Ya1XG4gICAgICAgIHRoaXMubWFyY2hSb2xlTm9kZV8ucmVtb3ZlQWxsQ2hpbGRyZW4oKVxuICAgIH1cblxuICAgIC8vIOajgOa1i+ihjOWGm+e6v+WBj+enu1xuICAgIHByaXZhdGUgY2hlY2tNYXJjaExpbmVPZmZzZXQoZGF0YTogQmFzZU1hcmNoT2JqKSB7XG4gICAgICAgIGNvbnN0IG90aGVyczogTWFyY2hDbXB0W10gPSBbXVxuICAgICAgICB0aGlzLm1hcmNocy5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgY29uc3QgZCA9IG0uZ2V0RGF0YSgpXG4gICAgICAgICAgICBpZiAoZGF0YS5jaGVja090aGVyTWFyY2hMaW5lKGQpKSB7XG4gICAgICAgICAgICAgICAgbS5hbmdsZU9mZnNldCA9IGRhdGEuc3RhcnRJbmRleCA9PT0gZC5zdGFydEluZGV4ID8gMCA6IC0xODBcbiAgICAgICAgICAgICAgICBtLmlzQ2hlY2tMaW5lT2Zmc2V0ID0gdHJ1ZVxuICAgICAgICAgICAgICAgIG90aGVycy5wdXNoKG0pXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICAgIGNvbnN0IGxlbiA9IG90aGVycy5sZW5ndGhcbiAgICAgICAgb3RoZXJzLmZvckVhY2goKG0sIGkpID0+IG0udXBkYXRlTGluZU9mZnNldChpLCBsZW4pKVxuICAgIH1cblxuICAgIC8vIOaUu+WHu+WcsOWdl1xuICAgIHByaXZhdGUgYXN5bmMgb2NjdXB5Q2VsbChpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLnJlcVNlbGVjdEFybXlzaW5nKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnJlcVNlbGVjdEFybXlzaW5nID0gdHJ1ZVxuICAgICAgICBjb25zdCB7IGVyciwgbGlzdCwgY2FuR290b0NvdW50IH0gPSBhd2FpdCB0aGlzLnBsYXllci5nZXRTZWxlY3RBcm15cyhpbmRleCwgMiwgMClcbiAgICAgICAgdGhpcy5yZXFTZWxlY3RBcm15c2luZyA9IGZhbHNlXG4gICAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyID09PSBlY29kZS5OT1RfSU5fT0NDVVBZX1RJTUUpIHtcbiAgICAgICAgICAgIHRoaXMuaGlkZVNlbGVjdENlbGwoZmFsc2UpXG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93TWVzc2FnZUJveCgndWkubm90X2luX29jY3VweV90aW1lX3RpcCcpXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyID09PSBlY29kZS5OT1RfSU5fT0NDVVBZX0FOQ0lFTlRfVElNRSkgeyAvL+aPkOekuuWPquiDveWcqOWbuuWumuaXtumXtOaUu+WHu1xuICAgICAgICAgICAgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dNZXNzYWdlQm94KCd1aS5ub3RfaW5fb2NjdXB5X2FuY2llbnRfdGltZV90aXAnKVxuICAgICAgICB9IGVsc2UgaWYgKGVycikge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVycilcbiAgICAgICAgfSBlbHNlIGlmIChsaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5ub3RfaWRsZV9hcm15JylcbiAgICAgICAgfVxuICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ21haW4vU2VsZWN0QXJteScsICdvY2N1cHknLCBpbmRleCwgbGlzdCwgY2FuR290b0NvdW50LCAoYXJteXM6IEFybXlTaG9ydEluZm9bXSwgaXNTYW1lU3BlZWQ6IGJvb2xlYW4sIGF1dG9CYWNrVHlwZTogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgICBpZiAodGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuaGlkZVNlbGVjdENlbGwoZmFsc2UpXG4gICAgICAgICAgICAgICAgdGhpcy5tb2RlbC5vY2N1cHlDZWxsKGFybXlzLCBpbmRleCwgYXV0b0JhY2tUeXBlLCBpc1NhbWVTcGVlZCkudGhlbihlcnIgPT4gZXJyICYmIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVycikpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g56e75Yqo6L+H5Y675bGv55SwXG4gICAgcHJpdmF0ZSBhc3luYyBjZWxsVG9uZGVuKGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMucmVxU2VsZWN0QXJteXNpbmcpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHRoaXMucmVxU2VsZWN0QXJteXNpbmcgPSB0cnVlXG4gICAgICAgIGNvbnN0IHsgZXJyLCBsaXN0LCBjYW5Hb3RvQ291bnQgfSA9IGF3YWl0IHRoaXMucGxheWVyLmdldFNlbGVjdEFybXlzKGluZGV4LCAzLCAwKVxuICAgICAgICB0aGlzLnJlcVNlbGVjdEFybXlzaW5nID0gZmFsc2VcbiAgICAgICAgaWYgKCF0aGlzLmlzQWN0aXZlKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmIChlcnIgPT09IGVjb2RlLkJBVFRMRUlORykge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5iYXR0bGVpbmdfbm90X3RvbmRlbicpXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZXJyKVxuICAgICAgICB9IGVsc2UgaWYgKGxpc3QubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0Lm5vdF9pZGxlX2FybXknKVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9TZWxlY3RUb25kZW5Bcm15JywgaW5kZXgsIGxpc3QsIGNhbkdvdG9Db3VudCwgKGFybXk6IEFybXlTaG9ydEluZm8pID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzQWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSlcbiAgICAgICAgICAgICAgICB0aGlzLm1vZGVsLmNlbGxUb25kZW4oYXJteSwgaW5kZXgpLnRoZW4oZXJyID0+IGVyciAmJiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpKVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIC8vIOWPlua2iOWxr+eUsFxuICAgIHByaXZhdGUgYXN5bmMgY2FuY2VsVG9uZGVuKGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgZXJyID0gYXdhaXQgdGhpcy5tb2RlbC5jYW5jZWxUb25kZW4oaW5kZXgpXG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgICAgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOenu+WKqOWIsOWcsOWdl1xuICAgIHByaXZhdGUgYXN5bmMgbW92ZVRvQ2VsbChpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLnJlcVNlbGVjdEFybXlzaW5nKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnJlcVNlbGVjdEFybXlzaW5nID0gdHJ1ZVxuICAgICAgICBjb25zdCB7IGVyciwgbGlzdCwgY2FuR290b0NvdW50IH0gPSBhd2FpdCB0aGlzLnBsYXllci5nZXRTZWxlY3RBcm15cyhpbmRleCwgMSlcbiAgICAgICAgdGhpcy5yZXFTZWxlY3RBcm15c2luZyA9IGZhbHNlXG4gICAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoZXJyKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZXJyKVxuICAgICAgICB9IGVsc2UgaWYgKGxpc3QubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0Lm5vdF9pZGxlX2FybXknKVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9TZWxlY3RBcm15JywgJ21vdmUnLCBpbmRleCwgbGlzdCwgY2FuR290b0NvdW50LCAoYXJteXM6IEFybXlTaG9ydEluZm9bXSwgaXNTYW1lU3BlZWQ6IGJvb2xlYW4pID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzQWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5oaWRlU2VsZWN0Q2VsbChmYWxzZSlcbiAgICAgICAgICAgICAgICB0aGlzLm1vZGVsLm1vdmVDZWxsQXJteShhcm15cywgaW5kZXgsIGlzU2FtZVNwZWVkKS50aGVuKGVyciA9PiBlcnIgJiYgdmlld0hlbHBlci5zaG93QWxlcnQoZXJyKSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDmkq3mlL7mlrDnmoTlnLDlnZfmlYjmnpxcbiAgICBwcml2YXRlIHBsYXlOZXdDZWxsRWZmZWN0KCkge1xuICAgICAgICB0aGlzLm1vZGVsLmdldE5vdFBsYXlOZXdDZWxscygpLmZvckVhY2goaW5kZXggPT4ge1xuICAgICAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMudGVtcFNob3dDZWxsTWFwW2luZGV4XVxuICAgICAgICAgICAgaWYgKGNlbGwpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBqc29uID0gY2VsbC5nZXRSZXNKc29uKCkgfHwge30sIGtleXMgPSBDRUxMX1JFU19GSUVMRFMuZmlsdGVyKG0gPT4gISFqc29uW21dKVxuICAgICAgICAgICAgICAgIGNvbnN0IHBvcyA9IGNlbGwuYWN0UG9zaXRpb24sIGlzTW9yZSA9IGtleXMubGVuZ3RoID4gMVxuICAgICAgICAgICAgICAgIGtleXMuZm9yRWFjaCgoa2V5LCBpKSA9PiBhbmltSGVscGVyLnBsYXlGbHV0dGVyQ2VsbFJlcyhrZXksIGpzb25ba2V5XSwgMC4zICsgaSAqIDAuMiwgaXNNb3JlLCB0aGlzLnRvcExheWVyTm9kZV8sIHBvcywgdGhpcy5rZXkpKVxuICAgICAgICAgICAgICAgIGFuaW1IZWxwZXIucGxheU5ld0NlbGxFZmZlY3QodGhpcy5jZWxsRWZmZWN0Tm9kZV8sIHBvcywgdGhpcy5rZXkpXG4gICAgICAgICAgICAgICAgLy8g6ZqQ6JeP6KGM5Yab57q/XG4gICAgICAgICAgICAgICAgdGhpcy5tYXJjaHMuZm9yRWFjaChtYXJjaCA9PiBtYXJjaC5pc0hhc0luZGV4KGluZGV4KSAmJiBtYXJjaC5oaWRlKDEuMikpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g5pKt5pS+5bGv55Sw57uT5p2f55qE5Zyw5Z2X5pWI5p6cXG4gICAgcHJpdmF0ZSBwbGF5Q2VsbFRvbmRlbkVmZmVjdCgpIHtcbiAgICAgICAgdGhpcy5tb2RlbC5nZXROb3RQbGF5Q2VsbFRvbmRlbnMoKS5mb3JFYWNoKGRhdGEgPT4ge1xuICAgICAgICAgICAgY29uc3QgY2VsbCA9IHRoaXMudGVtcFNob3dDZWxsTWFwW2RhdGEuaW5kZXhdXG4gICAgICAgICAgICBpZiAoY2VsbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHBvcyA9IGNlbGwuYWN0UG9zaXRpb25cbiAgICAgICAgICAgICAgICBjb25zdCBvYmogPSB7fVxuICAgICAgICAgICAgICAgIGRhdGEudHJlYXN1cmVJZHM/LmZvckVhY2goaWQgPT4ge1xuICAgICAgICAgICAgICAgICAgICBsZXQgaWRPYmogPSBvYmpbaWRdXG4gICAgICAgICAgICAgICAgICAgIGlmICghaWRPYmopIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGpzb24gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3RyZWFzdXJlJywgaWQpXG4gICAgICAgICAgICAgICAgICAgICAgICBpZE9iaiA9IG9ialtpZF0gPSB7IGNvdW50OiAwLCBpY29uOiAndHJlYXN1cmVfJyArIChqc29uPy5sdiB8fCAxKSArICdfMCcgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlkT2JqLmNvdW50ICs9IDFcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIGZvciAobGV0IGtleSBpbiBvYmopIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IG9ialtrZXldXG4gICAgICAgICAgICAgICAgICAgIGFuaW1IZWxwZXIucGxheUZsdXR0ZXJUcmVhc3VyZShkYXRhLmljb24sIGRhdGEuY291bnQsIHRoaXMudG9wTGF5ZXJOb2RlXywgcG9zLCB0aGlzLmtleSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYW5pbUhlbHBlci5wbGF5TmV3Q2VsbEVmZmVjdCh0aGlzLmNlbGxFZmZlY3ROb2RlXywgcG9zLCB0aGlzLmtleSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDmkq3mlL7lnLDlm77ooajmg4VcbiAgICBwcml2YXRlIHBsYXlDZWxsRW1vamkoKSB7XG4gICAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KClcbiAgICAgICAgZ2FtZUhwci5ncm91bmQuZ2V0Q2VsbEVtb2ppcygpLmZvckVhY2gobSA9PiB7XG4gICAgICAgICAgICBjb25zdCBjZWxsID0gdGhpcy50ZW1wU2hvd0NlbGxNYXBbbS5pbmRleF0sIGl0ZW0gPSB0aGlzLmNlbGxFbW9qaUl0ZW1NYXBbTWF0aC5mbG9vcihtLmVtb2ppIC8gMTAwMCldXG4gICAgICAgICAgICBpZiAoY2VsbCAmJiBpdGVtKSB7XG4gICAgICAgICAgICAgICAgbGV0IG5vZGU6IGNjLk5vZGUgPSB0aGlzLmNlbGxFbW9qaU1hcFttLmluZGV4XVxuICAgICAgICAgICAgICAgIGlmIChub2RlPy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIG5vZGUuQ2hpbGQoJ3Jvb3QnKS5jaGlsZHJlbi5mb3JFYWNoKGl0ID0+IG5vZGVQb29sTWdyLnB1dChpdCkpXG4gICAgICAgICAgICAgICAgICAgIG5vZGUuZGVzdHJveSgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IE1hdGgubWF4KDAsIChub3cgLSBtLmdldFRpbWUpICogMC4wMDEpXG4gICAgICAgICAgICAgICAgbm9kZSA9IHRoaXMuY2VsbEVtb2ppTWFwW20uaW5kZXhdID0gY2MuaW5zdGFudGlhdGUyKGl0ZW0sIHRoaXMuY2VsbEVtb2ppTm9kZV8pXG4gICAgICAgICAgICAgICAgbm9kZS5EYXRhID0gbS5pbmRleFxuICAgICAgICAgICAgICAgIG5vZGUuc2V0UG9zaXRpb24oY2VsbC5hY3RQb3NpdGlvbilcbiAgICAgICAgICAgICAgICBub2RlLnpJbmRleCA9IGNlbGwucG9pbnQueVxuICAgICAgICAgICAgICAgIGFuaW1IZWxwZXIucGxheUNlbGxFbW9qaShub2RlLCBtLmVtb2ppLCBtLnVpZCwgc3RhcnRUaW1lLCB0aGlzLmtleSkudGhlbihuID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCAmJiBuPy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWxldGUgdGhpcy5jZWxsRW1vamlNYXBbbi5EYXRhXVxuICAgICAgICAgICAgICAgICAgICAgICAgbi5DaGlsZCgncm9vdCcpLmNoaWxkcmVuLmZvckVhY2goaXQgPT4gbm9kZVBvb2xNZ3IucHV0KGl0KSlcbiAgICAgICAgICAgICAgICAgICAgICAgIG4uZGVzdHJveSgpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIHByaXZhdGUgdGVzdFBsYXlOZXdDZWxsKHg6IG51bWJlciwgeTogbnVtYmVyLCBrZXlzOiBzdHJpbmdbXSwgZGVsYXk6IG51bWJlcikge1xuICAgICAgICAvLyBmb3IgKGxldCBpID0gMDsgaSA8IDU7IGkrKykge1xuICAgICAgICAvLyAgICAgY29uc3QgcG9zaXRpb24gPSBtYXBIZWxwZXIuZ2V0UGl4ZWxCeVBvaW50KGNjLnYyKHggKyBpLCB5KSkuY2xvbmUoKVxuICAgICAgICAvLyAgICAgYW5pbUhlbHBlci5wbGF5Rmx1dHRlckNlbGxSZXMoJ3N0b25lJywgMzAsIHRoaXMudG9wTGF5ZXJOb2RlXywgcG9zaXRpb24sIHRoaXMua2V5KVxuICAgICAgICAvLyAgICAgYW5pbUhlbHBlci5wbGF5TmV3Q2VsbEVmZmVjdCh0aGlzLmNlbGxFZmZlY3ROb2RlXywgcG9zaXRpb24sIHRoaXMua2V5KVxuICAgICAgICAvLyB9XG4gICAgICAgIGNvbnN0IHBvcyA9IG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQoY2MudjIoeCwgeSkpLmNsb25lKCksIGlzTW9yZSA9IGtleXMubGVuZ3RoID4gMVxuICAgICAgICBrZXlzLmZvckVhY2goKGtleSwgaSkgPT4gYW5pbUhlbHBlci5wbGF5Rmx1dHRlckNlbGxSZXMoa2V5LCAxLCAwLjMgKyBpICogZGVsYXksIGlzTW9yZSwgdGhpcy50b3BMYXllck5vZGVfLCBwb3MsIHRoaXMua2V5KSlcbiAgICAgICAgYW5pbUhlbHBlci5wbGF5TmV3Q2VsbEVmZmVjdCh0aGlzLmNlbGxFZmZlY3ROb2RlXywgcG9zLCB0aGlzLmtleSlcbiAgICB9XG5cbiAgICAvLyDmo4DmtYvlraPoioJcbiAgICBwcml2YXRlIGFzeW5jIGNoZWNrU2Vhc29uKCkge1xuICAgICAgICBjb25zdCBvbGRUeXBlID0gdGhpcy51c2VyLmdldExvY2FsUHJlZmVyZW5jZURhdGFCeVNpZChQcmVmZXJlbmNlS2V5LkxBU1RfUExBWV9TRUFTT05fVFlQRSkgPz8gLTFcbiAgICAgICAgdGhpcy5zZWFzb25UeXBlID0gY2MubWlzYy5jbGFtcGYob2xkVHlwZSwgMCwgMylcbiAgICAgICAgLy8g5YWI6aKE5Yqg6L2955WM6Z2iXG4gICAgICAgIGlmIChvbGRUeXBlICE9PSB0aGlzLm1vZGVsLmdldFNlYXNvblR5cGUoKSkge1xuICAgICAgICAgICAgYXdhaXQgdmlld0hlbHBlci5wcmVsb2FkUG5sKCdtYWluL1NlYXNvblN3aXRjaCcpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmmL7npLrlnLDlm75cbiAgICBwcml2YXRlIHNob3dNYXAoKSB7XG4gICAgICAgIC8vIOiuvue9ruebuOacuuminOiJslxuICAgICAgICBjYW1lcmFDdHJsLnNldEJnQ29sb3IoQ0FNRVJBX0JHX0NPTE9SW3RoaXMuc2Vhc29uVHlwZV0pXG4gICAgICAgIC8vIOWIt+aWsOWcsOWbvuaYvuekulxuICAgICAgICBpZiAocmVzSGVscGVyLmNoZWNrTGFuZFNraW4odGhpcy5zZWFzb25UeXBlKSkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGVNYXAodGhpcy5tb2RlbC5nZXRDZW50cmUoKSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJlc0hlbHBlci5pbml0TGFuZFNraW4odGhpcy5zZWFzb25UeXBlKS50aGVuKCgpID0+IHRoaXMuaXNWYWxpZCAmJiB0aGlzLnVwZGF0ZU1hcCh0aGlzLm1vZGVsLmdldENlbnRyZSgpKSlcbiAgICAgICAgfVxuICAgICAgICAvLyDmmL7npLrlraPoioLliIfmjaLlhajlsY91aVxuICAgICAgICBjb25zdCBvbGRUeXBlID0gdGhpcy51c2VyLmdldExvY2FsUHJlZmVyZW5jZURhdGFCeVNpZChQcmVmZXJlbmNlS2V5LkxBU1RfUExBWV9TRUFTT05fVFlQRSkgPz8gLTFcbiAgICAgICAgaWYgKG9sZFR5cGUgIT09IHRoaXMubW9kZWwuZ2V0U2Vhc29uVHlwZSgpKSB7XG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ21haW4vU2Vhc29uU3dpdGNoJywgdHJ1ZSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaSreaUvuWIh+aNouWto+iKglxuICAgIHByaXZhdGUgYXN5bmMgcGxheUNoYW5nZVNlYXNvbih0eXBlOiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5zZWFzb25UeXBlID0gdHlwZVxuICAgICAgICB0aGlzLnVzZXIuc2V0TG9jYWxQcmVmZXJlbmNlRGF0YUJ5U2lkKFByZWZlcmVuY2VLZXkuTEFTVF9QTEFZX1NFQVNPTl9UWVBFLCB0eXBlKVxuICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKGZhbHNlKVxuICAgICAgICBhd2FpdCByZXNIZWxwZXIuaW5pdExhbmRTa2luKHR5cGUpXG4gICAgICAgIHRoaXMudXBkYXRlU2Vhc29uU2VjZW5lRWZmZWN0KClcbiAgICAgICAgLy8g6K6+572u55u45py66aKc6ImyXG4gICAgICAgIGNhbWVyYUN0cmwuc2V0QmdDb2xvcihDQU1FUkFfQkdfQ09MT1JbdGhpcy5zZWFzb25UeXBlXSlcbiAgICAgICAgLy8g5Yi35paw5Zyw5Zu+5pi+56S6XG4gICAgICAgIHRoaXMudXBkYXRlTWFwKHRoaXMuY2VudHJlKVxuICAgICAgICByZXNIZWxwZXIuY2xlYW5MYW5kU2tpbigpXG4gICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuQ0hBTkdFX1NFQVNPTl9DT01QTEVURSlcbiAgICB9XG5cbiAgICB1cGRhdGUoZHQ6IG51bWJlcikge1xuICAgICAgICAvL1xuICAgICAgICB0aGlzLnNlYXdhdmVBbmltTm9kZVBvb2w/LnVwZGF0ZShkdClcbiAgICAgICAgLy8g5qOA5rWL5piv5ZCm6ZyA6KaB5aGr5YWF5Zyw5Zu+XG4gICAgICAgIHRoaXMuY2hlY2tVcGRhdGVNYXAoKVxuICAgICAgICAvLyDmo4DmtYvmmK/lkKblnKjnm7jmnLrojIPlm7RcbiAgICAgICAgdGhpcy5jaGVja0luQ2FtZXJhUmFuZ2UoKVxuICAgIH1cblxuICAgIHByaXZhdGUgY2hlY2tVcGRhdGVNYXAoKSB7XG4gICAgICAgIGNvbnN0IHBvaW50ID0gbWFwSGVscGVyLmdldFBvaW50QnlQaXhlbChjYW1lcmFDdHJsLmdldENlbnRyZVBvc2l0aW9uKCksIHRoaXMuX3RlbXBfdmVjMl8xKVxuICAgICAgICBsZXQgc2l6ZSA9IE1hdGgubWF4KE1hdGguYWJzKHBvaW50LnggLSB0aGlzLmNlbnRyZS54KSwgTWF0aC5hYnMocG9pbnQueSAtIHRoaXMuY2VudHJlLnkpKVxuICAgICAgICBpZiAoc2l6ZSA+PSBNQVBfRVhUUkFfU0laRSAvIDIgfHwgdGhpcy5wcmVDYW1lcmFab29tUmF0aW8gIT09IGNhbWVyYUN0cmwuem9vbVJhdGlvKSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZU1hcChwb2ludClcbiAgICAgICAgICAgIHRoaXMuY2hlY2tJbkNhbWVyYU1hcmNoTGluZSgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmo4DmtYvlj6rkvJrlnKjlnKjnm7jmnLrojIPlm7TlhoXnmoTooYzlhpvnur9cbiAgICBwcml2YXRlIGNoZWNrSW5DYW1lcmFNYXJjaExpbmUoKSB7XG4gICAgICAgIGNvbnN0IHVpZE1hcCA9IHt9XG4gICAgICAgIHRoaXMubWFyY2hzLmZvckVhY2gobSA9PiB7XG4gICAgICAgICAgICBtLmNoZWNrVXBkYXRlSW5DYW1lcmEoKVxuICAgICAgICAgICAgdWlkTWFwW20udWlkXSA9IHRydWVcbiAgICAgICAgfSlcbiAgICAgICAgLy8g5YW85a655qOA5rWL5piv5ZCm5pyJ5aSa5L2Z55qE6KGM5Yab6KeS6ImyXG4gICAgICAgIGZvciAobGV0IGkgPSB0aGlzLm1hcmNoUm9sZU5vZGVfLmNoaWxkcmVuQ291bnQgLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICAgICAgY29uc3Qgbm9kZSA9IHRoaXMubWFyY2hSb2xlTm9kZV8uY2hpbGRyZW5baV1cbiAgICAgICAgICAgIGlmICghdWlkTWFwW25vZGUuRGF0YT8udWlkXSkge1xuICAgICAgICAgICAgICAgIG5vZGUuZGVzdHJveSgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcml2YXRlIGNoZWNrSW5DYW1lcmFSYW5nZSgpIHtcbiAgICAgICAgY29uc3QgcG9zaXRpb24gPSBjYW1lcmFDdHJsLmdldFBvc2l0aW9uKClcbiAgICAgICAgaWYgKHRoaXMucHJlQ2FtZXJhUG9zaXRpb24uZXF1YWxzKHBvc2l0aW9uKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5wcmVDYW1lcmFQb3NpdGlvbi5zZXQocG9zaXRpb24pXG4gICAgICAgIC8vIOmAieaLqeWcsOWdl+ahhlxuICAgICAgICBpZiAodGhpcy5jZWxsSW5mb0NtcHQ/LmNoZWNrTm90SW5TY3JlZW5SYW5nZSgpKSB7XG4gICAgICAgICAgICB0aGlzLmhpZGVTZWxlY3RDZWxsKGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxufVxuIl19