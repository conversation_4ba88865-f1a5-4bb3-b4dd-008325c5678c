
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CellInfoCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e9d66uKVf5P4KnEbIVvO5ch', 'CellInfoCmpt');
// app/script/view/main/CellInfoCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var AdaptWidthCmpt_1 = require("../cmpt/AdaptWidthCmpt");
var ccclass = cc._decorator.ccclass;
var CellInfoCmpt = /** @class */ (function (_super) {
    __extends(CellInfoCmpt, _super);
    function CellInfoCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.SIZE_OFFSET = cc.v2(-24, 160); //位置大小的偏移 anchorX=0.5 anchorY=1
        _this.BASE_SIZE = cc.size(576, 96); //基础大小
        _this.SPACE = 10;
        _this.OPEN_TIME = 0.15;
        _this.CLOSE_TIME = 0.1;
        _this.titleNode = null;
        _this.infoNode = null;
        _this.buttonsNode = null;
        _this.titleHeightHall = 0;
        _this.contentSizePointMin = cc.v2(); //内容大小的左下角的点
        _this.contentSizePointMax = cc.v2(); //内容大小的右上角的点
        _this.cell = null;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        return _this;
    }
    CellInfoCmpt.prototype.onLoad = function () {
        this.titleNode = this.FindChild('title');
        this.infoNode = this.FindChild('info');
        this.buttonsNode = this.FindChild('buttons');
        this.titleHeightHall = this.titleNode.height * 0.5;
    };
    CellInfoCmpt.prototype.clean = function () {
        this.close();
    };
    CellInfoCmpt.prototype.getCell = function () {
        return this.cell;
    };
    // 是否没有在屏幕范围
    CellInfoCmpt.prototype.checkNotInScreenRange = function () {
        if (!this.cell || this.contentSizePointMax.equals(cc.Vec2.ZERO)) {
            return false;
        }
        var min = this.node.convertToWorldSpaceAR(this.contentSizePointMin, this._temp_vec2_2);
        var max = this.node.convertToWorldSpaceAR(this.contentSizePointMax, this._temp_vec2_3);
        var outMin = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(min, min);
        var outMax = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(max, max);
        return outMax.x <= 0 || outMax.y <= 0 || outMin.x >= cc.winSize.width || outMin.y >= cc.winSize.height;
    };
    CellInfoCmpt.prototype.open = function (pos, cell) {
        this.cell = cell;
        this.node.active = true;
        this.node.setPosition(pos);
        this.updateInfo();
        // 播放动画
        this.playOpen(cell.getSize().y - 1);
    };
    CellInfoCmpt.prototype.close = function (anim) {
        if (anim && this.cell) {
            this.cell = null;
            this.playClose();
        }
        else {
            this.cell = null;
            this.unscheduleAllCallbacks();
            mc.unlockTouch('cell_info_open');
            mc.unlockTouch('cell_info_close');
            this.node.active = false;
        }
    };
    CellInfoCmpt.prototype.getPlayerInfo = function (uid) {
        return GameHelper_1.gameHpr.getPlayerInfo(uid);
    };
    CellInfoCmpt.prototype.getBattleDistMap = function () {
        return GameHelper_1.gameHpr.world.getBattleDistMap();
    };
    CellInfoCmpt.prototype.checkCanOccupyCell = function (cell) {
        return GameHelper_1.gameHpr.world.checkCanOccupyCell(cell);
    };
    CellInfoCmpt.prototype.updateInfo = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        if (!this.cell) {
            return;
        }
        var cell = this.cell;
        var isCanOccupy = cell.isCanOccupy();
        this.buttonsNode.active = this.infoNode.active = isCanOccupy;
        (_a = this.titleNode.Child('share_pos_be')) === null || _a === void 0 ? void 0 : _a.setActive(isCanOccupy);
        (_b = this.titleNode.Child('cell_emoji_be')) === null || _b === void 0 ? void 0 : _b.setActive(isCanOccupy);
        (_c = this.titleNode.Child('map_mark_be')) === null || _c === void 0 ? void 0 : _c.setActive(isCanOccupy); //标记
        // 标记说明
        this.updateFlagDesc();
        // 是否障碍
        if (!isCanOccupy) {
            this.titleNode.Child('name').Swih('res')[0].setLocaleKey(this.cell.getName());
            this.titleNode.Child('point', cc.Label).setLocaleKey(this.cell.landType === Enums_1.LandType.PASS ? 'ui.land_desc_bridge' : 'ui.land_desc_1');
            return;
        }
        var player = GameHelper_1.gameHpr.player;
        var isCapture = player.isCapture(); //是否沦陷
        var isSpectate = GameHelper_1.gameHpr.isSpectate(); //是否观战
        var isOwn = cell.isOwn(); //是否自己的
        var isOneAlliance = cell.isOneAlliance(); //是否一个联盟
        var isMainCity = ((_d = cell.city) === null || _d === void 0 ? void 0 : _d.id) === Constant_1.CITY_MAIN_NID; //是否主城
        var isAncient = cell.isAncient(); //是否遗迹
        var battleUids = this.getBattleDistMap()[cell.actIndex], isBattleing = !!battleUids; //是否战斗中
        var btInfo = cell.getBTCityInfo(), isBTCitying = !!btInfo; //是否修建中
        var info = this.getPlayerInfo(cell.owner), nickname = (info === null || info === void 0 ? void 0 : info.nickname) || '';
        if (isMainCity) {
            this.titleNode.Child('name').Swih('player')[0].Component(cc.Label).string = ut.nameFormator(nickname, 8);
        }
        else if (isBTCitying) {
            var node = this.titleNode.Child('name').Swih('bt')[0], isBt = btInfo.id > 0, btingNode = node.Child('bting');
            node.Child('val').setLocaleKey(isBt ? ('cityText.name_' + btInfo.id) : cell.getName());
            if (btingNode.active = mc.lang !== 'en') {
                btingNode.Color(isBt ? '#49983C' : '#C34A32').setLocaleKey(isBt ? 'ui.btcitying' : 'ui.dismantleing');
            }
        }
        else {
            this.titleNode.Child('name').Swih('res')[0].setLocaleKey(cell.getName());
        }
        this.titleNode.Child('point', cc.Label).setLocaleKey('(' + cell.actPoint.Join(',') + ')');
        // 基础信息内容
        var base = this.infoNode.Child('base');
        var hpLbl = base.Child('hp/val', cc.Label);
        hpLbl.string = cell.getHpText();
        hpLbl._forceUpdateRenderData();
        if (cell.owner || isAncient) {
            base.Child('hp/lv', cc.Label).setLocaleKey('ui.short_lv', ((_e = cell.getPawnInfo()) === null || _e === void 0 ? void 0 : _e.lv) || 1);
        }
        else {
            var _p = __read(cell.getLandDifficultyLv(), 2), type = _p[0], lv = _p[1];
            base.Child('hp/lv', cc.Label).setLocaleKey('ui.land_difficulty_' + type, lv);
        }
        base.Child('hp/lv', AdaptWidthCmpt_1.default).setMaxWidth(172 - hpLbl.node.width - 12);
        var owner = base.Child('owner'); //名字
        var has = owner.active = !isMainCity && !!cell.owner;
        if (has) {
            owner.Child('val', cc.Label).string = ut.nameFormator(nickname, 6);
        }
        var association = base.Child('association'); //联盟
        if (association.active = (isMainCity || !!(info === null || info === void 0 ? void 0 : info.allianceName)) && !(info === null || info === void 0 ? void 0 : info.isGiveupGame)) {
            association.Child('val', cc.Label).string = (info === null || info === void 0 ? void 0 : info.allianceName) || '-';
        }
        var isOutGame = (_f = base.Child('out_game')) === null || _f === void 0 ? void 0 : _f.setActive(!!(info === null || info === void 0 ? void 0 : info.isSettled) && !info.allianceUid && isMainCity); //出局
        (_g = base.Child('giveup_game')) === null || _g === void 0 ? void 0 : _g.setActive(!isOutGame && !!(info === null || info === void 0 ? void 0 : info.isGiveupGame) && isMainCity); //放弃对局
        var avoidwar = base.Child('avoidwar'); //免战
        var _q = __read(cell.getAvoidWarTypeAndTime(), 2), avoidWarType = _q[0], avoidWarTime = _q[1];
        if (avoidwar.active = avoidWarType > 0) {
            avoidwar.Child('icon', cc.MultiFrame).setFrame(avoidWarType - 1);
            avoidwar.Child('lay/val', cc.LabelTimer).run(avoidWarTime * 0.001);
        }
        // 资源信息内容
        var res = this.infoNode.Child('res');
        has = this.infoNode.Child('res_line').active = res.active = cell.isHasRes();
        if (has) {
            var json_1 = cell.getResJson() || {}, keys = Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; });
            var cityOutputs = isBTCitying ? [] : ((_h = this.getCityOutputOdds()) === null || _h === void 0 ? void 0 : _h[cell.cityId]) || [];
            res.Items(keys.concat(cityOutputs), function (it, key) {
                var _a;
                var isType = typeof (key) === 'string';
                var type = isType ? key : key.type;
                var valLbl = it.Child('val', cc.Label), oddsNode = it.Child('odds');
                it.Child('icon', cc.Sprite).spriteFrame = assetsMgr.getImage(type);
                valLbl.setLocaleKey('ui.res_desc', (_a = json_1[type]) !== null && _a !== void 0 ? _a : 1);
                if (oddsNode === null || oddsNode === void 0 ? void 0 : oddsNode.setActive(!isType)) {
                    oddsNode.setLocaleKey('ui.odds', key.odds);
                    valLbl._forceUpdateRenderData();
                    oddsNode.Component(AdaptWidthCmpt_1.default).setMaxWidth(it.width - valLbl.node.x - valLbl.node.width - 2);
                }
            });
        }
        // 体力
        var staminaNode = this.infoNode.Child('stamina');
        if (!(staminaNode === null || staminaNode === void 0 ? void 0 : staminaNode.setActive(!cell.owner && !isAncient && !isSpectate))) {
        }
        else if (GameHelper_1.gameHpr.isFreeServer()) {
            staminaNode.Child('val', cc.Label).Color('#756963').string = '-0';
            staminaNode.Child('stamina_desc_be/dot').active = false;
        }
        else {
            var needStamina = cell.getLandAttr().need_stamina || 0;
            var curStamina = player.getStamina();
            staminaNode.Child('val', cc.Label).Color(curStamina < needStamina ? '#C34A32' : '#756963').string = '-' + needStamina;
            staminaNode.Child('stamina_desc_be/dot').active = curStamina < needStamina;
        }
        // 积分
        var scoreNode = this.infoNode.Child('score');
        if (!scoreNode) {
        }
        else if (scoreNode.active = !cell.owner && !isAncient && !isSpectate) {
            scoreNode.Child('val').setLocaleKey('ui.add_score_desc', this.cell.getLandScore());
        }
        // 军队信息
        var armys = GameHelper_1.gameHpr.player.getDistArmysByIndex(this.cell.actIndex) || [];
        this.updateArmyInfo(armys);
        // 按钮
        var isCanAttack = !isCapture && !isOwn && !isOneAlliance;
        var isTondening = !!GameHelper_1.gameHpr.world.getTondenQueueMap()[this.cell.actIndex];
        // const isCanOccupy = this.checkCanOccupyCell(cell)
        this.buttonsNode.Child('enter_be').active = isOwn || isOneAlliance || isBattleing || !cell.owner; //进入
        this.buttonsNode.Child('enter_be/icon', cc.MultiFrame).setFrame(isOwn && isMainCity); //进入
        this.buttonsNode.Child('player_info_be').active = !GameHelper_1.gameHpr.isNoviceMode && !!cell.owner && !isOwn && isMainCity; //玩家信息
        (_j = this.buttonsNode.Child('ancient_info_be')) === null || _j === void 0 ? void 0 : _j.setActive(!!cell.owner && !isOneAlliance && isAncient); //遗迹信息
        (_k = this.buttonsNode.Child('city_skin_be')) === null || _k === void 0 ? void 0 : _k.setActive(isOwn && !isSpectate && !!GameHelper_1.gameHpr.user.getCitySkinList(cell.cityId).length); //城市皮肤
        this.buttonsNode.Child('move_be').active = !isCapture && (isOwn || isOneAlliance); //移动
        this.buttonsNode.Child('build_be').active = isOwn && !cell.city && !isBTCitying; //修建建筑
        this.buttonsNode.Child('dismantle_be').active = isOwn && cell.city && !isMainCity && !isAncient && !isBTCitying; //拆除建筑
        if (this.buttonsNode.Child('btcitying').active = isOwn && !isMainCity && !isAncient && isBTCitying) { //修建或拆除中
            this.buttonsNode.Child('btcitying/val', cc.LabelTimer).run(btInfo.getSurplusTime() * 0.001);
        }
        // this.buttonsNode.Child('cancel_build_be').active = isOwn && isBTCitying //取消修建
        this.buttonsNode.Child('occupy_be').active = isCanAttack; //攻占
        (_l = this.buttonsNode.Child('tonden_be')) === null || _l === void 0 ? void 0 : _l.setActive(!isCapture && isOwn && !cell.city && !isTondening); //屯田
        (_m = this.buttonsNode.Child('cancel_tonden_be')) === null || _m === void 0 ? void 0 : _m.setActive(!isCapture && isOwn && isTondening); //屯田
        (_o = this.buttonsNode.Child('flag_be')) === null || _o === void 0 ? void 0 : _o.setActive(GameHelper_1.gameHpr.alliance.isMeMilitary()); //标记
        // 有免战 但没有玩家 这里请求一下
        if (!cell.owner && (avoidWarType > 0 || armys.length > 0)) {
            GameHelper_1.gameHpr.world.syncServerCellInfo(cell.index);
        }
    };
    CellInfoCmpt.prototype.getCityOutputOdds = function () {
        var _a, _b, _c;
        if (((_a = this.cell) === null || _a === void 0 ? void 0 : _a.cityId) !== Constant_1.BUILD_FARM_ID && ((_b = this.cell) === null || _b === void 0 ? void 0 : _b.cityId) !== Constant_1.BUILD_TIMBER_ID && ((_c = this.cell) === null || _c === void 0 ? void 0 : _c.cityId) !== Constant_1.BUILD_QUARRY_ID) {
            return null;
        }
        var owner = this.cell.owner;
        var obj = {};
        var odds = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FARM_OUTPUT, owner);
        if (odds > 0) {
            obj[Constant_1.BUILD_FARM_ID] = [{ type: Constant_1.CTYPE_ICON[Enums_1.CType.EXP_BOOK], odds: odds }, { type: Constant_1.CTYPE_ICON[Enums_1.CType.IRON], odds: odds }];
        }
        odds = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MILL_OUTPUT, owner);
        if (odds > 0) {
            obj[Constant_1.BUILD_TIMBER_ID] = [{ type: Constant_1.CTYPE_ICON[Enums_1.CType.EXP_BOOK], odds: odds }];
        }
        odds = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.QUARRY_OUTPUT, owner);
        if (odds > 0) {
            obj[Constant_1.BUILD_QUARRY_ID] = [{ type: Constant_1.CTYPE_ICON[Enums_1.CType.IRON], odds: odds }];
        }
        return obj;
    };
    CellInfoCmpt.prototype.updateArmyInfo = function (armys) {
        if (!this.cell) {
            return;
        }
        armys = armys !== null && armys !== void 0 ? armys : (GameHelper_1.gameHpr.player.getDistArmysByIndex(this.cell.actIndex) || []);
        var army = this.infoNode.Child('army');
        var has = this.infoNode.Child('army_line').active = army.active = armys.length > 0;
        if (has) {
            army.Items(armys, function (it, data) { return it.Child('val', cc.Label).string = ut.nameFormator(data.name, 6); });
        }
    };
    // 刷新标记说明
    CellInfoCmpt.prototype.updateFlagDesc = function () {
        if (!this.cell) {
            return;
        }
        // 标记说明
        var flagDesc = this.titleNode.Child('flag_desc');
        if (flagDesc) {
            var mark = GameHelper_1.gameHpr.getOneFlagDescByIndex(this.cell.actIndex);
            if (flagDesc.active = !!(mark === null || mark === void 0 ? void 0 : mark.desc)) {
                // flagDesc.Child('lay/icon', cc.MultiFrame).setFrame(mark.type)
                // flagDesc.Child('lay/icon/val', cc.Sprite).spriteFrame = resHelper.getMapFlagNumber(mark.flag)
                flagDesc.Child('val', cc.Label).string = mark.desc;
            }
        }
    };
    // 刷新内容大小
    CellInfoCmpt.prototype.updateContentSizeRect = function (sy) {
        this.buttonsNode.Component(cc.Layout).updateLayout();
        this.infoNode.Component(cc.Layout).updateLayout();
        // 宽
        var w = sy === 0 ? 0 : 8;
        this.contentSizePointMin.x = -(this.BASE_SIZE.width * 0.5 + w) + this.SIZE_OFFSET.x;
        this.contentSizePointMax.x = this.contentSizePointMin.x + (this.BASE_SIZE.width + w * 2);
        // 高
        var height = this.titleNode.height + this.SPACE + Math.max(this.buttonsNode.height, this.infoNode.height);
        this.contentSizePointMax.y = this.titleNode.height + this.getTitleOffsetY(sy);
        this.contentSizePointMin.y = this.contentSizePointMax.y - height;
    };
    CellInfoCmpt.prototype.getTitleOffsetY = function (sy) {
        return sy * 40 + 52;
    };
    // 播放打开
    CellInfoCmpt.prototype.playOpen = function (sy) {
        return __awaiter(this, void 0, void 0, function () {
            var offsetY, y, w, oy;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mc.lockTouch('cell_info_open');
                        offsetY = this.getTitleOffsetY(sy);
                        y = this.titleHeightHall + offsetY;
                        w = sy === 0 ? 84 : 92;
                        oy = this.titleHeightHall + this.SPACE;
                        this.playOpenAnim(this.titleNode, 0, y);
                        this.playOpenAnim(this.infoNode, -(this.infoNode.width * 0.5 + w), y - oy);
                        this.playOpenAnim(this.buttonsNode, this.buttonsNode.width * 0.5 + w, y - oy);
                        return [4 /*yield*/, ut.wait(this.OPEN_TIME)];
                    case 1:
                        _a.sent();
                        this.updateContentSizeRect(sy);
                        mc.unlockTouch('cell_info_open');
                        eventCenter.emit(EventType_1.default.GUIDE_OPEN_MAP_CELL, this.cell);
                        return [2 /*return*/];
                }
            });
        });
    };
    CellInfoCmpt.prototype.playOpenAnim = function (node, x, y) {
        node.stopAllActions();
        var mag = this._temp_vec2_1.set2(x, y).mag();
        node.setPosition(this._temp_vec2_1.normalizeSelf().mulSelf(mag * 0.5));
        node.opacity = 0;
        node.scale = 0.5;
        cc.tween(node)
            .to(this.OPEN_TIME, { x: x, y: y, opacity: 255, scale: 1 } /* , { easing: cc.easing.sineOut } */)
            .start();
    };
    // 播放关闭
    CellInfoCmpt.prototype.playClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mc.lockTouch('cell_info_close');
                        this.playCloseAnim(this.titleNode);
                        this.playCloseAnim(this.infoNode);
                        this.playCloseAnim(this.buttonsNode);
                        return [4 /*yield*/, ut.wait(this.CLOSE_TIME)];
                    case 1:
                        _a.sent();
                        mc.unlockTouch('cell_info_close');
                        if (this.isValid) {
                            this.node.active = false;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CellInfoCmpt.prototype.playCloseAnim = function (node) {
        node.stopAllActions();
        var mag = node.getPosition(this._temp_vec2_1).mag();
        this._temp_vec2_1.normalizeSelf().mulSelf(mag * 0.5);
        cc.tween(node)
            .to(this.CLOSE_TIME, { x: this._temp_vec2_1.x, y: this._temp_vec2_1.y, opacity: 0, scale: 0.5 }, { easing: cc.easing.sineIn })
            .start();
    };
    CellInfoCmpt = __decorate([
        ccclass
    ], CellInfoCmpt);
    return CellInfoCmpt;
}(cc.Component));
exports.default = CellInfoCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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