import { BUY_OPT_HERO_COST, MONTH_CARD } from "../../common/constant/Constant";
import { ecode } from "../../common/constant/ECode";
import { CType, MonthlyCardType, NoLongerTipKey, SelectPortrayalType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHpr } from "../../common/helper/GameHelper";
import { payHelper } from "../../common/helper/PayHelper";
import { reddotHelper } from "../../common/helper/ReddotHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { localConfig } from "../../common/LocalConfig";
import PortrayalInfo from "../../model/common/PortrayalInfo";
import UserModel from "../../model/common/UserModel";

const { ccclass } = cc._decorator;

@ccclass
export default class ShopPnlCtrl extends mc.BasePnlCtrl {

	//@autocode property begin
	private topNode_: cc.Node = null // path://top_n
	private rootNode_: cc.Node = null // path://root_n
	private shopSv_: cc.ScrollView = null // path://root_n/shop_sv
	private mysteryBoxNode_: cc.Node = null // path://root_n/shop_sv/view/content/mystery_box_n
	private freeGoldNode_: cc.Node = null // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
	private optionalHeroNode_: cc.Node = null // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
	private ingotNode_: cc.Node = null // path://root_n/shop_sv/view/content/ingot_n
	private recommendSv_: cc.ScrollView = null // path://root_n/shop_sv/view/content/recommend/recommend_sv
	private cardNode_: cc.Node = null // path://root_n/shop_sv/view/content/card_n
	private restoreBuyNode_: cc.Node = null // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
	private limitedSkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/limited_skin_n
	private citySkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/city_skin_n
	private pawnSkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/pawn_skin_n
	private newHeadNode_: cc.Node = null // path://root_n/shop_sv/view/content/new_head_n
	private classicalHeadNode_: cc.Node = null // path://root_n/shop_sv/view/content/classical_head_n
	private newEmojiNode_: cc.Node = null // path://root_n/shop_sv/view/content/new_emoji_n
	private classicalEmojiNode_: cc.Node = null // path://root_n/shop_sv/view/content/classical_emoji_n
	private skinTabsNode_: cc.Node = null // path://root_n/skin_tabs_n
	private socialTabsNode_: cc.Node = null // path://root_n/social_tabs_n
	private tabsTc_: cc.ToggleContainer = null // path://root_n/tabs_tc_tce
	//@end

	// 推荐列表 type:对应配置表，id:对应配置表相应id
	private readonly RECOMMENDED_LIST = [
		{ type: 'head', id: 206 },
		{ type: 'city', id: 1001111 },
		{ type: 'pawn', id: 3103105 },
		{ type: 'emoji', id: 2040 },
		{ type: 'head', id: 190 },
		{ type: 'city', id: 1001107 },
		{ type: 'pawn', id: 3202101 },
		{ type: 'emoji', id: 2050 },
		{ type: 'head', id: 187 },
	]

	private readonly PKEY_TAB: string = 'SHOP_TAB'

	private goldValLbl: cc.LabelRollNumber = null
	private ingotValLbl: cc.LabelRollNumber = null

	private user: UserModel = null
	private curTab: number = 0
	private showType: string = ''
	private mysteryBoxPrefab: cc.Prefab = null
	private preViewHeight: number = 904

	public listenEventMaps() {
		return [
			{ [EventType.UPDATE_GOLD]: this.onUpdateGold, enter: true },
			{ [EventType.UPDATE_INGOT]: this.onUpdateIngot, enter: true },
			{ [EventType.INIT_PAY_FINISH]: this.onInitPayFinish, enter: true },
			{ [EventType.UPDATE_SUBSCRIPTION]: this.onUpdateSubscripion, enter: true },
			{ [EventType.UPDATE_RECHARGE_COUNT]: this.onUpdateRechargeCount, enter: true },
			{ [EventType.UPDATE_MYSTERYBOX]: this.onUpdateMysteryBox, enter: true },
		]
	}

	public async onCreate() {
		this.setParam({ isAct: false })
		this.user = this.getModel('user')
		this.loadMysteryBoxPrefab()
		// this.restoreBuyNode_.active = !gameHpr.isNoviceMode && (cc.sys.isBrowser || !gameHpr.isRelease)
		this.optionalHeroNode_.Child('button/money/val', cc.Label).string = BUY_OPT_HERO_COST + ''
		const throttleUpdate = this.throttle(event => this.onScrolling(event), 200)
		this.shopSv_.node.on('scrolling', throttleUpdate, this)
	}

	public onEnter(type?: number, showType?: string) {
		if (typeof (type) === 'string') {
			type = Number(type)
		}
		this.curTab = type ?? this.user.getTempPreferenceMap(this.PKEY_TAB) ?? 0
		this.showType = showType
		this.restoreBuyNode_.active = ut.isIos() && localConfig.RELEASE && !gameHpr.isRelease // 苹果审核才显示
		this.drawCount = this.frameCount = 0
		this.initTop()
		this.checkShowNotFinishOrder()
		this.updateShopIngot()
		// this.updateRecommend()
		// this.updateMonthlyCard()
		this.updateSkin()
		// this.updateHeadIcon()
		// this.updateChatEmoji()
		this.tabsTc_.Tabs(this.curTab)
		this.emit(EventType.HIDE_TOP_NODE, false)
		this.playRootAnimation()
	}

	public onRemove() {
		// this.topNode_.active = false
		this.emit(EventType.HIDE_TOP_NODE, true)
	}

	public onClean() {
		this.drawCount = this.frameCount = 0
		assetsMgr.releaseTempResByTag(this.key)
	}

	// ----------------------------------------- button listener function -------------------------------------------
	//@autocode button listener

	// path://root_n/tabs_tc_tce
	onClickTabs(event: cc.Toggle, data: string) {
		!data && audioMgr.playSFX('click')
		const type = this.curTab = Number(event.node.name)
		this.user.setTempPreferenceData(this.PKEY_TAB, type)
		let y = 0
		if (type === 0) { // 元宝位置 0
			if (this.showType === 'ingot') {
				y = this.ingotNode_.y
			}
			this.skinTabsNode_.active = this.socialTabsNode_.active = false
		} else if (type === 1) { // 月卡位置 1
			y = this.cardNode_.y
			this.skinTabsNode_.active = this.socialTabsNode_.active = false
		} else if (type === 2) { // 皮肤位置 2
			this.skinTabsNode_.active = true
			this.socialTabsNode_.active = false
			y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y
		} else if (type === 3) { // 新款头像位置 3
			this.skinTabsNode_.active = false
			this.socialTabsNode_.active = true
			y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y
		}
		this.shopSv_.stopAutoScroll()
		this.shopSv_.content.y = Math.abs(y)
	}

	// path://root_n/skin_tabs_n/skin_tabs_nbe
	onClickSkinTabs(event: cc.Event.EventTouch, data: string) {
		const name = event.target.name
		let y = 0
		if (name === '0') { // 跳转限定
			y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y
		} else if (name === '1') { // 跳转主城
			y = this.citySkinNode_.y
		} else if (name === '2') { // 跳转士兵
			y = this.pawnSkinNode_.y
		}
		this.shopSv_.stopAutoScroll()
		this.shopSv_.content.y = Math.abs(y)
	}

	// path://root_n/social_tabs_n/social_tabs_nbe
	onClickSocialTabs(event: cc.Event.EventTouch, data: string) {
		const name = event.target.name
		let y = 0
		if (name === '0') { // 跳转头像
			y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y
		} else if (name === '1') { // 跳转表情
			y = this.newEmojiNode_.active ? this.newEmojiNode_.y : this.classicalEmojiNode_.y
		}
		this.shopSv_.stopAutoScroll()
		this.shopSv_.content.y = Math.abs(y)
	}

	// path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
	onClickFreeGold(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		if (!this.user.isBuyLimitFreeGold()) {
			return viewHelper.showAlert('ui.yet_buy_day')
		}
		this.user.buyFreeGold().then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				this.updateFreeGold()
				viewHelper.showAlert('toast.buy_succeed')
			}
		})
	}

	// path://root_n/shop_sv/view/content/exchange/list/buy_gold_be
	onClickBuyGold(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		viewHelper.showPnl('common/ShopBuyGoldTip')
	}

	// path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
	onClickOptionalHero(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		if (!this.user.isBuyLimitOptionalHero()) {
			return viewHelper.showAlert('ui.yet_buy_week')
		}
		const list = assetsMgr.getJson('portrayalBase').datas.map(m => new PortrayalInfo().init(m.id, m))
		viewHelper.showPnl('common/SelectPortrayal', SelectPortrayalType.BUY, list, (arr: PortrayalInfo[]) => {
			if (this.isValid && arr.length > 0) {
				this.buyHero(arr[0].id)
			}
		}, 3)
	}

	// path://root_n/shop_sv/view/content/ingot_n/title/pay_not_arrived_be
	onClickPayNotArrived(event: cc.Event.EventTouch, data: string) {
		viewHelper.showLoadingWait(true)
		this.checkShowNotFinishOrder(true).then(() => viewHelper.showLoadingWait(false))
	}

	// path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
	onClickRestoreBuy(event: cc.Event.EventTouch, data: string) {
		payHelper.restoreBuySub()
	}

	// path://root_n/shop_sv/view/content/ingot_n/list/ingot_be
	onClickIngot(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		if (this.user.isGuest()) {
			return viewHelper.showMessageBox('ui.guest_buy_ingot_tip', {
				okText: 'ui.button_bind_account',
				ok: () => viewHelper.showPnl('common/BindAccount'),
				cancel: () => { }
			})
		} else if (!payHelper.isInitFinish()) {
			return viewHelper.showAlert('toast.please_wait_init_pay')
		}
		const json = event.target.Data
		viewHelper.showPnl('common/ShopBuyIngotTip', json, (ok: boolean) => {
			if (ok) {
				payHelper.buyProduct(json.product_id).then((suc: boolean) => {
					logger.print('6.buyProduct end. suc=' + suc + ', isValid=' + this.isValid)
					if (suc && this.isValid) {
						this.updateShopIngot()
					}
				})
			}
		})
	}

	// path://root_n/shop_sv/view/content/recommend/recommend_sv/view/content/buy_be@recommend
	onClickBuy(event: cc.Event.EventTouch, param: string) {
		let data = event.target.Data
		if (!data) {
			return
		}
		let buyType = ''
		if (param === 'recommend') { // 推荐
			buyType = data.type
			data = data.json
		}
		if (param === 'city' || buyType === 'city' || param === 'pawn' || buyType === 'pawn') { // 士兵、城市皮肤
			const type = param === 'city' || buyType === 'city' ? 'city_skin' : 'pawn_skin'
			viewHelper.showPnl('menu/CollectionSkinInfo', { type, list: [data] }, (ret) => {
				if (!this.isValid || !ret) {
					return
				} else if (gameHpr.costDeductTip(ret)) {
					return
				}
				if (type === 'pawn_skin') {
					this.buyPawnSkin(ret.id)
				} else if (type === 'city_skin') {
					this.buyCitySkin(ret.id)
				}
			})
		} else if (param === 'head' || buyType === 'head' || param === 'emoji' || buyType === 'emoji') { // 头像
			const type = param === 'head' || buyType === 'head' ? 'headicon' : 'chat_emoji'
			viewHelper.showPnl('menu/CollectionEmojiInfo', { type: type, list: [data] }, (ret) => {
				if (!this.isValid || !ret) {
					return
				} else if (gameHpr.costDeductTip(ret)) {
					return
				}
				if (type === 'headicon') {
					this.buyHeadIcon(ret.id)
				} else if (type === 'chat_emoji') {
					this.buyChatEmoji(ret.id)
				}
			})
		}
	}

	// path://root_n/shop_sv/view/content/card_n/list/card_0/sale_card_nbe
	onClickSaleCard(event: cc.Event.EventTouch, _data: string) {
		const name = event.target.name, data = event.target.Data
		if (name === '0') { // 订阅
			if (data && data.leftDays === 0 && data.surplusTime > 0) {
				viewHelper.showAlert('toast.subscription_not_end')
			} else {
				viewHelper.showPnl('common/SubscriptionDesc', MonthlyCardType.SALE)
			}
		} else if (name === '1') { // 领取
			this.getMonthlyCardAward(MonthlyCardType.SALE)
		} else if (name === '2') { // 已领取

		}
	}

	// path://root_n/shop_sv/view/content/card_n/list/card_1/super_card_nbe
	onClickSuperCard(event: cc.Event.EventTouch, _data: string) {
		const name = event.target.name, data = event.target.Data
		if (name === '0') { // 订阅
			if (data && data.leftDays === 0 && data.surplusTime > 0) {
				viewHelper.showAlert('toast.subscription_not_end')
			} else {
				viewHelper.showPnl('common/SubscriptionDesc', MonthlyCardType.SUPER)
			}
		} else if (name === '1') { // 领取
			this.getMonthlyCardAward(MonthlyCardType.SUPER)
		} else if (name === '2') { // 已领取

		}
	}
	//@end
	// ----------------------------------------- event listener function --------------------------------------------

	// 刷新元宝
	private onUpdateIngot() {
		const ingot = this.user.getIngot()
		this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49')
		this.ingotValLbl.to(ingot)
	}

	// 刷新元宝
	private onUpdateGold() {
		const gold = this.user.getGold()
		this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49')
		this.goldValLbl.to(gold)
	}

	// 初始化支付完成 刷新下金币
	private onInitPayFinish() {
		this.updateShopIngot()
		this.checkShowNotFinishOrder()
	}

	// 刷新订阅信息
	private onUpdateSubscripion() {
		console.log('onUpdateSubscripion has=' + this.user.isHasSubscription())
		this.updateMonthlyCard()
	}

	// 刷新充值次数
	private onUpdateRechargeCount() {
		this.updateShopIngot()
	}

	// 刷新盲盒
	private onUpdateMysteryBox() {
		this.updateMysteryBox(this.mysteryBoxNode_.children[0])
	}

	// ----------------------------------------- custom function ----------------------------------------------------

	private initTop() {
		const node = this.topNode_.Swih(mc.currWindName === 'lobby' ? 'lobby' : 'main')[0]
		this.goldValLbl = node.Child('gold/val', cc.LabelRollNumber)
		this.ingotValLbl = node.Child('ingot/val', cc.LabelRollNumber)
		const gold = this.user.getGold(), ingot = this.user.getIngot()
		this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49')
		this.goldValLbl.set(gold)
		this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49')
		this.ingotValLbl.set(ingot)
	}

	// 
	private playRootAnimation() {
		this.rootNode_.stopAllActions()
		this.rootNode_.scale = 0.4
		cc.tween(this.rootNode_).to(0.25, { scale: 1 }, { easing: cc.easing.backOut }).start()
	}

	// 检测未完成的订单
	private async checkShowNotFinishOrder(showToast?: boolean) {
		if (!payHelper.isInitFinish()) {
			await payHelper.checkPayInit()
		}
		// 是否有未完成的订单
		const ok = await this.user.checkHasNotFinishOrder(payHelper.getPlatform())
		if (ok) {
			viewHelper.showPnl('main/NotFinishOrderTip')
		} else if (!showToast) {
		} else if (!payHelper.isInitFinish()) {
			viewHelper.showAlert('toast.please_wait_init_pay')
		} else {
			viewHelper.showAlert('toast.no_check_not_finish_order')
		}
	}

	// 刷新元宝
	private updateShopIngot() {
		const isFinish = payHelper.isInitFinish()
		const list = assetsMgr.getJson('recharge').datas.filter(m => !!m.ignore)
		const rechargeCountRecord = this.user.getRechargeCountRecord()
		this.ingotNode_.Child('list').Items(list, (it, json) => {
			it.Data = json
			resHelper.loadIcon(json.icon, it.Child('icon/val'), this.key)
			it.Child('name').setLocaleKey('ui.shop_ingot_name', json.ingot)
			const button = it.Child('button')
			if (button.Child('money').active = isFinish) {
				button.Child('money/val', cc.Label).string = payHelper.getProductPriceText(json.product_id)
			}
			const extra = it.Child('mask/extra')
			if (!rechargeCountRecord[json.product_id]) { //首次
				extra.active = true
				extra.Child('val').setLocaleKey('ui.first_give_ingot', json.ingot)
			} else if (extra.active = !!json.extra) {
				extra.Child('val').setLocaleKey('ui.give_ingot', json.extra)
			}
			button.Child('loading').active = !isFinish
		})
		// 免费
		this.updateFreeGold()
		// 自选
		this.updateOptionalHero()
		//
		this.onUpdateIngot()
		this.onUpdateGold()
	}

	// 刷新推荐
	private updateRecommend() {
		const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
		const list = []
		// 先做一次筛选，拥有的就不显示了
		for (let i = 0; i < this.RECOMMENDED_LIST.length; i++) {
			let arr = []
			const data = this.RECOMMENDED_LIST[i]
			if (data.type === 'city') {
				arr = this.user.getCanBuyCitySkins(serverArea)
			} else if (data.type === 'pawn') {
				arr = this.user.getCanBuyPawnSkins(serverArea)
			} else if (data.type === 'head') {
				arr = this.user.getCanBuyHeadIcons(serverArea)
			} else if (data.type === 'emoji') {
				arr = this.user.getCanBuyChatEmojis(serverArea)
			}
			if (arr.some(m => m.id === data.id)) {
				list.push(data)
			}
		}
		this.recommendSv_.stopAutoScroll()
		this.recommendSv_.content.y = 0
		this.recommendSv_.Items(list, (it, data: any) => {
			const isPawnOrCity = data.type === 'city' || data.type === 'pawn'
			it.Child('icon', cc.Sprite).enabled = isPawnOrCity
			const icon = it.Child('icon').Swih(isPawnOrCity ? '1' : '0')[0]
			icon.scale = 1
			let json = null
			if (data.type === 'pawn') {
				json = assetsMgr.getJsonData('pawnSkin', data.id)
				resHelper.loadPawnHeadIcon(json.id, icon, this.key)
			} else if (data.type === 'city') {
				icon.scale = 0.65
				json = assetsMgr.getJsonData('citySkin', data.id)
				resHelper.loadCityIcon(json.id, icon, this.key)
			} else if (data.type === 'head') {
				json = assetsMgr.getJsonData('headIcon', data.id)
				resHelper.loadPlayerHead(icon, json.icon, this.key, true)
			} else if (data.type === 'emoji') {
				json = assetsMgr.getJsonData('chatEmoji', data.id)
				resHelper.loadEmojiIcon(json.id, icon, this.key)
			}
			it.Data = { type: data.type, json }
			this.updateCostText(it, json)
			const isNew = gameHpr.checkShopNewProduct(json)
			it.Child('new').active = isNew
			it.Color(isNew ? '#FAEDCD' : '#F1E8D3')
			it.Child('mask/extra', cc.Sprite).enabled = isNew
		})
	}

	// 刷新月卡
	private updateMonthlyCard() {
		const node = this.cardNode_.Child('list')
		const subDatas = this.user.getSubDatas()
		for (let i = 0; i < node.childrenCount; i++) {
			const data = MONTH_CARD[i], item = node.Child('card_' + i)
			const subData = subDatas.find(m => data.PRODUCT_IDS_ANDROID.includes(m.productId)) || subDatas.find(m => data.PRODUCT_IDS_IOS.includes(m.productId)) || subDatas.find(m => data.RECHARGES.includes(m.productId))
			const isBuy = !!subData // 是否已购买
			const canClaim = isBuy ? (subData.leftDays > 0 && subData.lastAwardTime <= 0) : false //是否可领取
			const imm = item.Child('imm'), isFirstPay = !this.user.getRechargeCountRecord()[data.TYPE]
			imm.Child('mask').active = isFirstPay
			imm.Child('count', cc.Label).string = 'x' + data.FIRST
			imm.Child('mask/extra/val').setLocaleKey('ui.first_give_ingot', data.FIRST)
			const isDone = isBuy && !canClaim
			imm.Child('done').active = isBuy
			imm.Child('icon').opacity = isBuy ? 150 : 255
			const day = item.Child('day')
			day.Child('done').active = isDone
			const tipNode = item.Child('tip').Swih(isBuy ? 'surplus' : 'total')[0]
			if (isBuy) {
				tipNode.Child('val', cc.Label).string = subData.leftDays + ''
			} else {
				let total = data.FIRST * 2 + data.DAY * data.DURATION
				tipNode.Child('g_count', cc.Label).string = total + ''
				const wartoken = tipNode.Child('wartoken'), wtCount = tipNode.Child('wt_count', cc.Label)
				if (wartoken.active = wtCount.node.active = !!data.EXTRA) {
					wtCount.string = data.EXTRA * data.DURATION + ''
				}
			}
			const notEnd = subData?.leftDays === 0 && subData?.surplusTime > 0, showBuy = !subData || notEnd
			if (i === 0) {
				day.Child('icon').opacity = isDone ? 150 : 255
				day.Child('count', cc.Label).string = 'x' + data.DAY
				const button = item.Child('sale_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0] // 0:购买；1：领取；2：已领取
				button.Data = subData
				button.opacity = notEnd ? 120 : 255 // 天数已尽，但没有到期
			} else {
				day.Child('icon').children.forEach(m => m.opacity = isDone ? 150 : 255)
				day.Child('count').children.forEach((m, i) => {
					if (i === 0) {
						m.Component(cc.Label).string = 'x' + data.DAY
					} else {
						m.Component(cc.Label).string = 'x' + data.EXTRA
					}
				})
				const button = item.Child('super_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0] // 0:购买；1：领取；2：已领取
				button.Data = subData
				button.opacity = notEnd ? 120 : 255 // 天数已尽，但没有到期
			}
		}
	}

	// 刷新皮肤
	private async updateSkin() {
		const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
		// 主城皮肤
		const citySkinList = this.updateCitySkin(serverArea)
		// 士兵皮肤
		const list = this.user.getCanBuyPawnSkins(serverArea)
		const arr1 = [], arr2 = [], arr3 = []
		let mysteryBoxId = 0
		list.forEach(m => {
			if (m.cond > 100) {
				arr3.push(m)
				mysteryBoxId = m.cond
			} else if (m['limit_time_' + serverArea] && m.cond === 4) {
				arr2.push(m)
			} else {
				arr1.push(m)
			}
		})
		arr1.sort((a, b) => {
			let aw = 0, bw = 0
			aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
			bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
			return bw - aw
		})
		arr2.sort((a, b) => {
			let aw = 0, bw = 0
			aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
			bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
			return bw - aw
		})
		// 限定皮肤(目前只计划士兵皮肤)
		if (this.limitedSkinNode_.active = this.skinTabsNode_.Child('skin_tabs_nbe/0').active = arr2.length > 0) {
			this.updatePawnSkinList(this.limitedSkinNode_, arr2, true)
			const timeNode = this.limitedSkinNode_.Child('title/lay/time'), json = arr2[0]
			const [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')
			timeNode.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))
		}
		// 士兵皮肤
		this.updatePawnSkinList(this.pawnSkinNode_, arr1, false)
		// 盲盒限定
		if (this.mysteryBoxNode_.active = arr3.length > 0) {
			await this.loadMysteryBoxPrefab(mysteryBoxId)
			this.initMysterBox(serverArea, arr3)
		}
	}

	// 加载盲盒预制
	private async loadMysteryBoxPrefab(id?: number) {
		// 当前开放的盲盒id
		if (!id) {
			const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
			assetsMgr.getJson('pawnSkin').datas.forEach(m => {
				if (m.cond < 100) {
					return
				}
				const [startTime, endTime] = (m['limit_time_' + serverArea] || m.limit_time_hk).split('|')
				if (startTime && endTime && !gameHpr.checkActivityAutoDate(startTime, endTime)) {
					return //是否有时间限制
				}
				id = m.cond
			})
		}
		if (!this.mysteryBoxPrefab) {
			this.mysteryBoxPrefab = await assetsMgr.loadTempRes('mysterybox/MYSTERYBOX_' + id, cc.Prefab, this.key)
		}
		return this.mysteryBoxPrefab
	}

	private initMysterBox(serverArea: string, arr: any[]) {
		let mysteryboxNode = null
		if (this.mysteryBoxNode_.childrenCount > 1) {
			mysteryboxNode = this.mysteryBoxNode_.children[0]
		} else {
			mysteryboxNode = cc.instantiate2(this.mysteryBoxPrefab, this.mysteryBoxNode_)
		}
		mysteryboxNode.zIndex = -1
		this.mysteryBoxNode_.Swih(mysteryboxNode.name)[0]
		const json = mysteryboxNode.Data = arr[0]
		const [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')
		const timeNode = mysteryboxNode.Child('time'), timeVal = timeNode.Child('val')
		timeVal.setLocaleKey('ui.mysterybox_limit_time', endTime.split('-').slice(0, 3).join('/'))
		this.updateMysteryBox(mysteryboxNode, serverArea)

		this.addClickEvent(mysteryboxNode.Child('mysterybox_be', cc.Button), 'onClickMysterybox')
		this.addClickEvent(mysteryboxNode.Child('mysterybox_rule_be', cc.Button), 'onClickMysteryboxRule')
		this.addClickEvent(mysteryboxNode.Child('skin_exchange_be', cc.Button), 'onClickSkinExchange')

		if (!storageMgr.loadBool('click_MysteryBox_tab' + json.cond)) {
			reddotHelper.set('mystery_box', false)
			storageMgr.saveBool('click_MysteryBox_tab' + json.cond, true)
		}
	}

	private updateMysteryBox(node: cc.Node, serverArea?: string) {
		serverArea = serverArea || (gameHpr.isRelease ? gameHpr.getServerArea() : 'test')
		const exchangeSv = node.Child('exchange_sv', cc.ScrollView), colorSkins = this.getColorSkin(serverArea)
		exchangeSv.Items(colorSkins, (it, data) => {
			it.Data = data.id
			const icon = it.Child('icon/val'), mask = it.Child('icon/mask')
			resHelper.loadPawnHeadIcon(data.id, icon, this.key)
			resHelper.loadPawnHeadIcon(data.id, mask, this.key)
			const [needId, needCount] = ut.stringToNumbers(data.value, ',')
			const hasCount = this.user.getSkinItemList().filter(m => m.id === needId)?.length || 0
			mask.Component(cc.Sprite).fillRange = Math.min(1, 1 - hasCount / needCount)
			this.addClickEvent(it.Component(cc.Button), 'onClickSkinExchange')
		})
	}

	private getColorSkin(serverArea: string) {
		const unlockMap = {}
		this.user.getUnlockPawnSkinIds().forEach(m => unlockMap[m] = true)
		return assetsMgr.getJson('pawnSkin').datas.filter(m => {
			if (!unlockMap[m.id] && m.cond === 5) {
				const [startTime, endTime] = (m['limit_time_' + serverArea] || m.limit_time_hk).split('|')
				if (startTime && endTime && gameHpr.checkActivityAutoDate(startTime, endTime)) {
					return true
				}
			}
			return false
		})
	}

	private updateCitySkin(serverArea: string) {
		const list = this.user.getCanBuyCitySkins(serverArea)
		const arr1 = [], arr2 = []
		list.forEach(m => {
			// if (m['limit_time_' + serverArea]) {
			// 	arr2.push(m)
			// } else {
			arr1.push(m)
			// }
		})
		arr1.sort((a, b) => {
			let aw = 0, bw = 0
			aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
			bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
			return bw - aw
		})
		// arr2.sort((a, b) => {
		// 	let aw = 0, bw = 0
		// 	aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
		// 	bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
		// 	return bw - aw
		// })
		this.updateCitySkinList(this.citySkinNode_, arr1)
		// // 季节限定
		// const limitedNode = this.citySkinNode_.Child('limited')
		// if (limitedNode.active = arr2.length > 0) {
		// 	this.updateCitySkinList(limitedNode.Child('list'), arr2)
		// 	const timeNode = limitedNode.Child('time'), timeLbl = timeNode.Child('val', cc.Label), json = arr2[0]
		// 	const [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')
		// 	timeLbl.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))
		// 	timeLbl._forceUpdateRenderData()
		// 	const w = (timeNode.width - timeLbl.node.width - 16) / 2
		// 	timeNode.Child('0').width = timeNode.Child('1').width = w
		// }
		return list
	}

	private updateCitySkinList(node: cc.Node, list: any[]) {
		let node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]
		if (node_.name === 'list') {
			node_.Items(list, (it, json) => {
				it.Data = json
				resHelper.loadCityIcon(json.id, it.Child('icon/val'), this.key)
				this.updateCostText(it, json)
				const isNew = gameHpr.checkShopNewProduct(json)
				it.Child('new').active = isNew
				it.Color(isNew ? '#FAEDCD' : '#F1E8D3')
				it.Child('mask/extra', cc.Sprite).enabled = isNew
			})
		}
	}

	private updatePawnSkinList(node: cc.Node, list: any[], isLimited: boolean) {
		let node_ = null
		if (isLimited) {
			node_ = node.Child('list')
		} else {
			node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]
		}
		if (node_.name === 'list') {
			node_.Items(list, (it, json) => {
				it.Data = json
				it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.pawn_id), 5))
				this.updateCostText(it, json)
				const isNew = gameHpr.checkShopNewProduct(json)
				it.Child('new')?.setActive(isNew)
				it.Color(isNew ? '#FAEDCD' : '#F1E8D3')
				it.Child('mask/extra', cc.Sprite).enabled = isNew
				resHelper.loadPawnHeadIcon(json.id, it.Child('icon/val'), this.key)
			})
		}
	}

	private updateCostText(it: cc.Node, json: any) {
		const node = it.Child('mask/extra')
		if (json.gold > 0) {
			node.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.GOLD)
			node.Child('val', cc.Label).string = json.gold + ''
		} else if (json.ingot > 0) {
			node.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.INGOT)
			node.Child('val', cc.Label).string = json.ingot + ''
		} else {
			node.Child('icon').active = false
		}
	}

	private getEndDate(endTime: string, startTime: string) {
		let year = new Date().getFullYear()
		if ((startTime?.split('-')[0] || '').length !== 4) {
			startTime = year + '-' + startTime
		}
		if ((endTime?.split('-')[0] || '').length !== 4) {
			let [_1, sm] = startTime.split('-')
			let em = endTime.split('-')[0]
			if (Number(em) < Number(sm)) {
				year += 1
			}
			endTime = year + '-' + endTime
		}
		let [endYear, endMonth, endDay] = endTime?.split('-')
		return assetsMgr.lang('ui.date', endYear, endMonth, endDay)
	}

	private getAddNewTime(endTime: string) {
		let [endYear, endMonth, endDay] = endTime?.split('-')
		return endMonth + '/' + endDay
	}

	private buyCitySkin(id: number) {
		this.user.buyCitySkin(id).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				viewHelper.showAlert('toast.buy_succeed')
				this.updateSkin()
				if (this.RECOMMENDED_LIST.some(m => m.type === 'city')) {
					this.updateRecommend()
				}
				viewHelper.showAlert('toast.buy_succeed')
			}
		})
	}

	private buyPawnSkin(id: number) {
		this.user.buyPawnSkin(id).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				viewHelper.showAlert('toast.buy_succeed')
				this.updateSkin()
				if (this.RECOMMENDED_LIST.some(m => m.type === 'pawn')) {
					this.updateRecommend()
				}
				viewHelper.showAlert('toast.buy_succeed')
			}
		})
	}

	// 购买盲盒
	private buyMysterybox(json: any) {
		viewHelper.showMessageBox('ui.buy_mysterybox_tip', {
			params: [json.ingot, json.desc],
			ok: () => viewHelper.showPnl(`activity/MysteryboxShow${json.cond}`, json.cond, json.ingot),
			cancel: () => { },
		})
	}

	// 刷新头像
	private updateHeadIcon() {
		const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
		const list = this.user.getCanBuyHeadIcons(serverArea).sort((a, b) => {
			let aw = 0, bw = 0
			aw = a.id + (gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0)
			bw = b.id + (gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0)
			return bw - aw
		})
		// 新款头像
		const newHeadIcons = list.filter(m => !!m['limit_time_' + serverArea])
		let date = ''
		if (newHeadIcons.length > 0) {
			const [startTime, endTime] = (newHeadIcons[0]['limit_time_' + serverArea] || newHeadIcons[0].limit_time_hk).split('|')
			date = this.getAddNewTime(startTime)
		}
		this.updateHeadIconList(this.newHeadNode_, newHeadIcons, true, date)

		// 经典头像
		const classicHeadIcons = list.filter(m => !m['limit_time_' + serverArea])
		this.updateHeadIconList(this.classicalHeadNode_, classicHeadIcons, false, '')
	}

	private updateHeadIconList(node: cc.Node, list: any[], isNew: boolean, date: string) {
		let node_ = null
		if (isNew) {
			node.active = list.length > 0
			node.Child('title/lay/lay/time', cc.Label).string = date
			node_ = node.Child('list')
		} else {
			node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]
		}
		if (node_.active && node_.name === 'list') {
			node_.Items(list, (it, json) => {
				it.Data = json
				resHelper.loadPlayerHead(it.Child('icon/val'), json.icon, this.key, true)
				this.updateCostText(it, json)
			})
		}
	}

	// 购买头像
	private buyHeadIcon(id: number) {
		this.user.buyHeadIcon(id).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				viewHelper.showAlert('toast.buy_succeed')
				this.updateHeadIcon()
				if (this.RECOMMENDED_LIST.some(m => m.type === 'head')) {
					this.updateRecommend()
				}
			}
		})
	}

	// 刷新表情
	private updateChatEmoji() {
		const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
		const list = this.user.getCanBuyChatEmojis(serverArea).sort((a, b) => {
			let aw = 0, bw = 0
			aw = a.id + (gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0)
			bw = b.id + (gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0)
			return bw - aw
		})
		// 新款表情
		const newEmojis = list.filter(m => !!m['limit_time_' + serverArea])
		let date = ''
		if (newEmojis.length > 0) {
			const [startTime, endTime] = (newEmojis[0]['limit_time_' + serverArea] || newEmojis[0].limit_time_hk).split('|')
			date = this.getAddNewTime(startTime)
		}
		this.updateChatEmojiList(this.newEmojiNode_, newEmojis, true, date)

		//经典表情
		const classicEmojis = list.filter(m => !m['limit_time_' + serverArea])
		this.updateChatEmojiList(this.classicalEmojiNode_, classicEmojis, false, '')
	}

	private updateChatEmojiList(node: cc.Node, list: any[], isNew: boolean, date: string) {
		let node_ = null
		if (isNew) {
			node.active = list.length > 0
			node.Child('title/lay/lay/time', cc.Label).string = date
			node_ = node.Child('list')
		} else {
			node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]
		}
		if (node_.active && node_.name === 'list') {
			node_.Items(list, (it, json) => {
				it.Data = json
				resHelper.loadEmojiIcon(json.id, it.Child('icon/val'), this.key)
				this.updateCostText(it, json)
			})
		}
	}

	private buyChatEmoji(id: number) {
		this.user.buyChatEmoji(id).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				viewHelper.showAlert('toast.buy_succeed')
				this.updateChatEmoji()
				if (this.RECOMMENDED_LIST.some(m => m.type === 'emoji')) {
					this.updateRecommend()
				}
			}
		})
	}

	// 购买英雄
	private buyHero(id: number) {
		this.user.buyHero(id).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				this.updateOptionalHero()
				viewHelper.showGainPortrayalDebris(id, 3)
			}
		})
	}

	// 刷新免费金币
	private updateFreeGold() {
		const isCanBuy = this.user.isBuyLimitFreeGold()
		this.freeGoldNode_.Child('button').opacity = isCanBuy ? 255 : 120
		this.freeGoldNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_day' : 'ui.yet_buy_day')
	}

	// 刷新自选英雄
	private updateOptionalHero() {
		const isCanBuy = this.user.isBuyLimitOptionalHero()
		this.optionalHeroNode_.Child('button').opacity = isCanBuy ? 255 : 120
		this.optionalHeroNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_week' : 'ui.yet_buy_week')
	}

	// 领取订阅的月卡奖励
	private getMonthlyCardAward(type: MonthlyCardType) {
		this.user.reqGetMonthlyCardAward(type).then(err => {
			if (err) {
				return viewHelper.showAlert(err)
			} else if (this.isValid) {
				this.updateMonthlyCard()
				viewHelper.showAlert('toast.claim_succeed')
			}
		})
	}

	onClickMysterybox(event: cc.Event.EventTouch, data: string) {
		const node = event.target.parent
		const json = node.Data
		if (json && json.cond > 100) {
			if (this.user.getIngot() < json.ingot) {
				return viewHelper.showAlert(ecode.INGOT_NOT_ENOUGH)
			} else if (!gameHpr.isNoLongerTip(NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
				viewHelper.showPnl('common/MysteryboxRule', json.cond, () => this.isValid && this.buyMysterybox(json))
			} else {
				this.buyMysterybox(json)
			}
		}
	}

	onClickMysteryboxRule(event: cc.Event.EventTouch, data: string) {
		audioMgr.playSFX('click')
		const id = this.mysteryBoxNode_.children[0].Data.cond
		id && viewHelper.showPnl('common/MysteryboxRule', id)
	}


	onClickSkinExchange(event: cc.Event.EventTouch, _data: string) {
		const data = event.target.Data
		audioMgr.playSFX('click')
		viewHelper.showPnl('common/SkinExchange', data)
	}

	private onScrolling(event: cc.ScrollView) {
		const height = event.node.height / 2, content = event.content,
			skinY = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y,
			socialY = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y,
			showCard = content.y >= Math.abs(this.cardNode_.y + height) && content.y < Math.abs(skinY + height),
			showSkin = content.y >= Math.abs(skinY + height) && content.y < Math.abs(socialY + height),
			showSocial = content.y >= Math.abs(socialY + height)
		if (this.skinTabsNode_.active !== showSkin) {
			this.skinTabsNode_.active = showSkin
		}
		if (this.socialTabsNode_.active !== showSocial) {
			this.socialTabsNode_.active = showSocial
		}
		event.Child('view').height = (showSkin || showSocial) ? this.preViewHeight - this.skinTabsNode_.height : this.preViewHeight
		const tab = showCard ? 1 : showSkin ? 2 : showSocial ? 3 : 0
		if (this.curTab !== tab) {
			this.curTab = tab
			this.tabsTc_.Swih(tab)
			this.user.setTempPreferenceData(this.PKEY_TAB, tab)
		}
	}

	private throttle(func: Function, delay: number) {
		let timer = null
		return function (...args) {
			if (!timer) {
				func.apply(this, args)
				timer = setTimeout(() => {
					timer = null
				}, delay)
			}
		}
	}

	private drawCount: number = 0
	private drawRate: number = 10
	private frameCount: number = 0

	private funcList: Function[] = [
		this.updateRecommend,
		this.updateMonthlyCard,
		this.updateHeadIcon,
		this.updateChatEmoji,
	]

	update(dt: number) {
		if (this.drawCount < this.funcList.length) {
			this.frameCount++
			if (this.frameCount > this.drawRate) {
				this.frameCount = 0
				this.funcList[this.drawCount].bind(this)()
				this.drawCount++
			}
		}
	}
}
