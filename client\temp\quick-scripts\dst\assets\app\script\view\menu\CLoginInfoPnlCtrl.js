
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/CLoginInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '47b45q1h8tJdJ7CCbAUUOBL', 'CLoginInfoPnlCtrl');
// app/script/view/menu/CLoginInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var ccclass = cc._decorator.ccclass;
var CLoginInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CLoginInfoPnlCtrl, _super);
    function CLoginInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descRt_ = null; // path://root/title/desc_rt
        _this.contentNode_ = null; // path://root/content_n
        //@end
        _this.ITEM_ADAPT_SIZE = cc.size(64, 64);
        // 可点击的类型
        _this.INTERACTABLE_ARR = [
            Enums_1.CType.PAWN_SKIN,
            Enums_1.CType.HEAD_ICON,
            Enums_1.CType.CHAT_EMOJI,
            Enums_1.CType.HERO_DEBRIS,
            Enums_1.CType.HERO_OPT
        ];
        _this.task = null;
        _this.curTask = null;
        return _this;
    }
    CLoginInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CLoginInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.task = this.getModel('task');
                return [2 /*return*/];
            });
        });
    };
    CLoginInfoPnlCtrl.prototype.onEnter = function () {
        this.init();
    };
    CLoginInfoPnlCtrl.prototype.onRemove = function () {
    };
    CLoginInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/item_be
    CLoginInfoPnlCtrl.prototype.onClickItem = function (event, _data) {
        var _this = this;
        var _a, _b, _c;
        var _d = event.target.Data, id = _d.id, reward = _d.reward;
        if (((_a = this.curTask) === null || _a === void 0 ? void 0 : _a.id) === id && ((_b = this.curTask) === null || _b === void 0 ? void 0 : _b.isCanClaim())) {
            if (!this.curTask) {
                return;
            }
            else if (((_c = this.curTask.rewards[0]) === null || _c === void 0 ? void 0 : _c.type) === Enums_1.CType.HERO_OPT) { //自选礼包
                ViewHelper_1.viewHelper.showHeroOptSelect(this.curTask.rewards[0].id).then(function (id) {
                    if (_this.isValid && !!id) {
                        _this.claimGeneralReward(_this.curTask, id);
                    }
                });
                return;
            }
            var items = GameHelper_1.gameHpr.checkRewardFull(this.curTask.rewards);
            if (items.length > 0) {
                return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimGeneralReward(_this.curTask); });
            }
            this.claimGeneralReward(this.curTask);
        }
        else if (reward) {
            if (reward.type === Enums_1.CType.PAWN_SKIN) {
                var json = assetsMgr.getJsonData('pawnSkin', reward.id);
                ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [json] });
            }
            else if (reward.type === Enums_1.CType.HEAD_ICON) {
                var json = assetsMgr.getJsonData('headIcon', reward.id);
                ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'headicon', list: [json] });
            }
            else if (reward.type === Enums_1.CType.CHAT_EMOJI) {
                var json = assetsMgr.getJsonData('chatEmoji', reward.id);
                ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'chat_emoji', list: [json] });
            }
            else if (reward.type === Enums_1.CType.HERO_DEBRIS) {
                var json = assetsMgr.getJsonData('portrayalBase', reward.id);
                ViewHelper_1.viewHelper.showPnl('common/PortrayalBaseInfo', json, 'shop');
            }
            else if (reward.type === Enums_1.CType.HERO_OPT) {
                var id_1 = reward.id, arr = Constant_1.HERO_OPT_GIFT[id_1];
                var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
                ViewHelper_1.viewHelper.showPnl('common/SelectPortrayalPreview', id_1, list);
            }
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CLoginInfoPnlCtrl.prototype.init = function () {
        var _this = this;
        var dayNo = 0;
        this.task.updateGeneralTaskState();
        this.curTask = this.task.getGeneralTasks().find(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.SIGN_DAY_COUNT; });
        var datas = assetsMgr.getJson('generalTask').datas.filter(function (m) { return m.cond.startsWith('1002,0,'); });
        this.contentNode_.Items(datas, function (it, json) {
            var _a, _b;
            var reward = new CTypeObj_1.default().fromString(json.reward);
            it.Data = { id: json.id, reward: reward };
            var root = it.Child('root'), noNode = it.Child('no');
            ViewHelper_1.viewHelper.updateItemByCTypeOne(root, reward, _this.key, _this.ITEM_ADAPT_SIZE, true);
            root.Child('text').active = reward.type === Enums_1.CType.HERO_DEBRIS || reward.type === Enums_1.CType.HERO_OPT;
            var _c = __read(json.cond.split(','), 3), a = _c[0], b = _c[1], no = _c[2], taskId = (_a = _this.curTask) === null || _a === void 0 ? void 0 : _a.id, ing = taskId === json.id, done = it.Child('done'), reddot = it.Child('dot');
            var isDone = !taskId || taskId > json.id;
            done.active == isDone;
            noNode.opacity = root.opacity = isDone ? 150 : 255;
            noNode.Color(ing ? '#936E5A' : '#756963').setLocaleKey('ui.day_no', no);
            var canClaim = ing && ((_b = _this.curTask) === null || _b === void 0 ? void 0 : _b.isCanClaim());
            reddot.active = canClaim;
            it.Component(cc.MultiFrame).setFrame(canClaim);
            it.Component(cc.Button).interactable = _this.INTERACTABLE_ARR.has(reward.type) || canClaim;
            if (ing) {
                dayNo = no - 1;
            }
        });
        var str = assetsMgr.lang('ui.desc_daily_sign_in', dayNo).replace('<color=#21DE29>', '<color=#21DE29><size=30>');
        this.descRt_.setLocaleKey(str);
    };
    CLoginInfoPnlCtrl.prototype.claimGeneralReward = function (data, heroId) {
        var _this = this;
        this.task.claimGeneralTaskReward(data.id, heroId).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            var hero = heroId ? new CTypeObj_1.default().init(Enums_1.CType.HERO_DEBRIS, heroId, 3) : data.rewards.find(function (m) { return m.type === Enums_1.CType.HERO_DEBRIS; });
            if (hero) {
                ViewHelper_1.viewHelper.showGainPortrayalDebris(hero.id, hero.count);
            }
            else {
                GameHelper_1.gameHpr.addGainMassage(data.rewards);
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
            }
            if (_this.isValid) {
                _this.init();
                _this.emit(EventType_1.default.UPDATE_ACITIVITIES);
            }
        });
    };
    CLoginInfoPnlCtrl = __decorate([
        ccclass
    ], CLoginInfoPnlCtrl);
    return CLoginInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CLoginInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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