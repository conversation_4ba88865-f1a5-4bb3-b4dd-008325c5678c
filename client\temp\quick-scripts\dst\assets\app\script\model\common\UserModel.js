
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/UserModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0fbd077YNNOKILFDeXmqSnX', 'UserModel');
// app/script/model/common/UserModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var PortrayalInfo_1 = require("./PortrayalInfo");
/**
 * 用户模块
 */
var UserModel = /** @class */ (function (_super) {
    __extends(UserModel, _super);
    function UserModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.localPreferenceMap = {}; //本地偏好设置
        _this.tempPreferenceMap = {}; //临时偏好设置
        _this.localPreferenceSidMap = {}; //本地偏好设置
        _this.initTime = 0;
        _this.uid = '';
        _this.sessionId = '';
        _this.sid = 0; //当前所在服务器id
        _this.playSid = -1; //当前所在服务器id
        _this.createTime = 0;
        _this.nickname = '';
        _this.headIcon = '';
        _this.personalDesc = ''; //个人简介
        _this.title = 0; //当前佩戴的称号
        _this.gold = 0; //金币
        _this.ingot = 0; //元宝
        _this.warToken = 0; //兵符
        _this.portrayals = []; //拥有的画像列表
        _this.totalGameCount = []; //总局数
        _this.totalRankCount = []; //排位次数
        _this.totalNewbieCount = []; //新手次数
        _this.giveupCount = 0; //放弃次数
        _this.loginDayCount = 0; //一共登陆的天数 user信息
        _this.cloginDayCount = 0; //连续登录天数 user信息
        _this.signDays = 0; //签到天数
        _this.sumOnlineTime = 0; //累计在线时间
        _this.modifyNameCount = 0; //修改昵称次数
        _this.unlockHeadIcons = []; //解锁的头像列表
        _this.unlockPawnSkinIds = []; //解锁的士兵皮肤列表
        _this.skinItemList = []; // 盲盒皮肤列表  state 0:正常可用; -1:锁住; >0:剩余时间
        _this.unlockChatEmojiIds = []; //解锁的聊天表情列表
        _this.unlockCitySkinIds = []; //解锁的城市皮肤列表
        _this.unlockBotanys = []; //解释的植物列表
        _this.titles = []; //称号列表
        _this.inviteFriends = []; //邀请的好友
        _this.carryNoviceData = false; //是否携带新手村数据
        _this.todaySendTrumpetCount = 0; //每日发送喇叭次数
        _this.notFinishOrders = []; //已经验证过 但是还未领取奖励
        _this.checkPraiseCount = 0; //记录触发好评弹窗x值
        _this.maxLandCount = 0; //历史最大领地
        _this.maxWheelMul = 0; //历史最大转动倍数
        _this.rankSeason = 0; //当前赛季
        _this.rankScore = 0; //段位分
        _this.rankCoin = 0; //段位积分
        _this.logoutSurplusTime = 0; //注销剩余时间
        _this.fcmConfs = []; //消息推送配置信息
        _this.subDatas = []; //订阅信息
        _this.battleForecastCount = 0; //战斗预测次数
        _this.buyOptionalHeroSurplusTime = 0; //购买自选英雄包剩余时间
        _this.buyOptionalHeroSurplusGetTime = 0;
        _this.buyFreeGoldSurplusTime = 0; //购买免费金币剩余时间
        _this.buyFreeGoldSurplusGetTime = 0;
        _this.rechargeCountRecord = {}; //充值次数记录
        _this.expectPosition = 0; //期望位置
        _this.farmType = 0; // 开局模式（0：未选择模式，1：正常模式，2：保护模式）
        _this.passNewbieIndex = 0; //第X局通关了新手区
        _this.accLikeJwmCount = 0; //累计点赞九万亩次数
        _this.rankRewardRecords = []; //已经领取过的赛季奖励
        _this.activityRecord = {}; //活动记录
        _this.plantData = {}; //当前种植信息
        _this.wheelRecords = []; //转动记录
        _this.wheelCurrCount = -1; //转动当前次数
        _this.wheelFreeCount = -1; //免费转动次数
        _this.wheelResidueCount = -1; //剩余转动次数
        _this.wheelWaitTime = 0; //转动等待时间
        _this.wheelRandomAwards = []; //随机奖励
        _this.wheelRandomAwardRecords = []; //随机奖励记录
        _this.wheelInRoomRunDay = 0; //所在服务器的运行天数
        _this.wheelGetTime = 0;
        _this.tempMailList = []; //邮件列表
        _this.lastReqMailsTime = 0; //最后一次请求邮件时间
        _this.tempTriggerPraiseCount = 1; //在线情况只能触发一次好评弹窗
        _this.tempBattlePassInfo = null; // 临时战令信息
        _this.lastReqBattlePassTime = 0; // 最后一次请求战令时间
        return _this;
    }
    UserModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.localPreferenceMap = storageMgr.loadJson('preference_map') || {};
    };
    UserModel.prototype.init = function (data) {
        var now = this.initTime = Date.now();
        this.lastReqMailsTime = 0;
        this.lastReqBattlePassTime = 0;
        this.wheelResidueCount = -1;
        this.wheelRandomAwards = [];
        this.wheelRandomAwardRecords = [];
        cc.log('init user', data);
        this.uid = data.uid;
        this.sessionId = data.sessionId;
        this.setSid(data.sid);
        this.setPlaySid(data.playSid || 0);
        this.createTime = data.createTime || 0;
        this.loginType = data.loginType;
        this.gold = data.gold || 0;
        this.ingot = data.ingot || 0;
        this.warToken = data.warToken || 0;
        this.totalGameCount = data.totalGameCount || [0, 0];
        this.totalRankCount = data.totalRankCount || [0, 0];
        this.totalNewbieCount = data.totalNewbieCount || [0, 0];
        this.giveupCount = data.giveupCount || 0;
        this.loginDayCount = data.loginDayCount || 0;
        this.cloginDayCount = data.cloginDayCount || 0;
        this.signDays = data.signDays || 0;
        this.sumOnlineTime = data.sumOnlineTime || 0;
        this.nickname = data.nickname || '???';
        this.headIcon = data.headIcon || '';
        this.personalDesc = data.personalDesc || '';
        this.title = data.title || 0;
        this.modifyNameCount = data.modifyNameCount || 0;
        this.setUnlockHeadIcons(data.unlockHeadIcons);
        this.setUnlockPawnSkinIds(data.unlockPawnSkinIds);
        this.setUnlockCitySkinIds(data.unlockCitySkinIds);
        this.setSkinItemList(data.skinItemList);
        this.setUnlockChatEmojiIds(data.unlockChatEmojiIds);
        this.setUnlockBotanys(data.unlockBotanys);
        this.setTitles(data.titles || []);
        this.inviteFriends = data.inviteFriends || [];
        this.setWheelCurrCount(data.wheelCurrCount || 0);
        this.wheelFreeCount = data.wheelFreeCount || 0;
        this.wheelWaitTime = data.wheelTime || 0;
        this.wheelGetTime = now;
        this.carryNoviceData = !!data.carryNoviceData;
        this.todaySendTrumpetCount = data.todaySendTrumpetCount || 0;
        this.initServerNotFinishOrders(data.notFinishOrders || []);
        this.checkPraiseCount = data.checkPraiseCount || 0;
        this.maxLandCount = data.maxLandCount || 0;
        this.maxWheelMul = data.maxWheelMul || 0;
        this.rankSeason = data.rankSeason || 0;
        this.rankScore = data.rankScore || 0;
        this.rankCoin = data.rankCoin || 0;
        this.logoutSurplusTime = data.logoutSurplusTime || 0;
        this.fcmConfs = data.offlineNotifyOpt || [0, 1, 3, 4, 5, 7, 8, 9, 10, 11];
        this.updateSubDatas(data.subData || []);
        this.battleForecastCount = data.battleForecastCount || 0;
        this.buyFreeGoldSurplusTime = data.buyFreeGoldSurplusTime || 0;
        this.buyFreeGoldSurplusGetTime = now;
        this.buyOptionalHeroSurplusTime = data.buyOptionalHeroSurplusTime || 0;
        this.buyOptionalHeroSurplusGetTime = now;
        this.rechargeCountRecord = data.rechargeCountRecord || {};
        this.expectPosition = data.expectPosition || 0;
        this.farmType = data.farmType || 0;
        this.passNewbieIndex = data.passNewbieIndex || 0;
        this.accLikeJwmCount = data.accLikeJwmCount || 0;
        this.setPortrayals((data === null || data === void 0 ? void 0 : data.portrayals) || [], false);
        this.setRankRewardRecords(data.rankRewardRecord);
        this.activityRecord = data.activityRecord || {};
        this.setPlantData(data.plantData || {});
        GameHelper_1.gameHpr.guide.init(data.guides || []);
        GameHelper_1.gameHpr.friend.init(data.friendsList || [], data.friendsApplys || [], data.blacklists || []);
        // reddotHelper.set('wheel_award', !!data.hasWheelAward)
        // reddotHelper.set('wheel_free_count', !!data.wheelFreeCount)
        // reddotHelper.unregister('can_wheel')
        // reddotHelper.register('can_wheel', this.checkCanWheel, this, 30, 28)
        ReddotHelper_1.reddotHelper.unregister('free_gold');
        ReddotHelper_1.reddotHelper.register('free_gold', this.checkCanBuyFreeGold, this, 30, 28);
        ReddotHelper_1.reddotHelper.unregister('monthly_card');
        ReddotHelper_1.reddotHelper.register('monthly_card', this.checkMonthlyCardReward, this, 30, 28);
        // 监听消息
        this.net.on('lobby/OnUpdateUserInfo', this.OnUpdateUserInfo, this);
        // 检测上报
        if (this.sumOnlineTime >= ut.Time.Hour * 20) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('online_20h');
        }
        else if (this.sumOnlineTime >= ut.Time.Hour * 5) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('online_5h');
        }
        if (this.cloginDayCount >= 7) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('login_7d');
        }
        EventReportHelper_1.eventReportHelper.checkReportGetPortrayal();
        if (GameHelper_1.gameHpr.isNoviceMode) {
            this.setGold(GameHelper_1.gameHpr.noviceServer.getGold());
            this.setWarToken(GameHelper_1.gameHpr.noviceServer.getWarToken());
        }
    };
    // 首次进入游戏调用
    UserModel.prototype.enter = function () {
        // 获取邮件
        this.getMails(true);
        // 获取战令
        this.reqBattlePassInfo(true);
        // 检测段位奖励红点
        this.checkRankRewardReddot();
        // 通过deeplink打开相应游戏界面
        ViewHelper_1.viewHelper.openUIByDeepLink();
        // 检测新增的盲盒活动红点
        this.checkClickMysteryBoxTabReddot();
        // 上报
        EventReportHelper_1.eventReportHelper.checkReportRankScore();
    };
    UserModel.prototype.cleanByLogin = function () {
        this.tempPreferenceMap = {};
        this.clean();
    };
    UserModel.prototype.clean = function () {
        this.wheelResidueCount = -1;
        this.wheelRandomAwards = [];
        this.wheelRandomAwardRecords = [];
        this.lastReqMailsTime = 0;
        this.tempMailList = [];
    };
    // 语言改变
    UserModel.prototype.languageChange = function () {
        // 刷新邮件文本
        this.updateTempMailLang();
    };
    UserModel.prototype.getUid = function () { return this.uid; };
    UserModel.prototype.getSessionId = function () { return this.sessionId; };
    UserModel.prototype.getSid = function () { return this.sid; };
    UserModel.prototype.getPlaySid = function () { return this.playSid; };
    UserModel.prototype.getCreateTime = function () { return this.createTime; };
    UserModel.prototype.getLoginType = function () { return this.loginType; };
    UserModel.prototype.setLoginType = function (val) { this.loginType = val; };
    UserModel.prototype.getTotalGameCount = function () { return this.totalGameCount; };
    UserModel.prototype.getAccTotalGameCount = function () { return this.totalGameCount[1] || 0; };
    UserModel.prototype.setTotalGameCount = function (val) { this.totalGameCount = val; };
    UserModel.prototype.getTotalRankCount = function () { return this.totalRankCount; };
    UserModel.prototype.getAccTotalRankCount = function () { return this.totalRankCount[1] || 0; };
    UserModel.prototype.getAccTotalNewbieCount = function () { return this.totalNewbieCount[1] || 0; };
    UserModel.prototype.setTotalNewbieCount = function (val) { this.totalNewbieCount = val; };
    UserModel.prototype.getLoginDayCount = function () { return this.loginDayCount; };
    UserModel.prototype.getSignDays = function () { return this.signDays; };
    UserModel.prototype.getNickname = function () { return this.nickname; };
    UserModel.prototype.setNickname = function (val) { this.nickname = val; };
    UserModel.prototype.getHeadIcon = function () { return this.headIcon; };
    UserModel.prototype.getPersonalDesc = function () { return this.personalDesc; };
    UserModel.prototype.getTitle = function () { return this.title; };
    UserModel.prototype.getModifyNameCount = function () { return this.modifyNameCount; };
    UserModel.prototype.setModifyNameCount = function (val) { this.modifyNameCount = val; };
    UserModel.prototype.getUnlockHeadIcons = function () { return this.unlockHeadIcons; };
    UserModel.prototype.getInviteFriendNotUseCount = function () { var _a; return ((_a = this.inviteFriends) === null || _a === void 0 ? void 0 : _a.filter(function (m) { return m.useType === 0; }).length) || 0; };
    UserModel.prototype.isCarryNoviceData = function () { return this.carryNoviceData; };
    UserModel.prototype.setCarryNoviceData = function (val) { this.carryNoviceData = val; };
    UserModel.prototype.getTodaySendTrumpetCount = function () { return this.todaySendTrumpetCount; };
    UserModel.prototype.setTodaySendTrumpetCount = function (val) { this.todaySendTrumpetCount = val; };
    UserModel.prototype.getNotFinishOrders = function () { return this.notFinishOrders; };
    UserModel.prototype.getMaxLandCount = function () { return this.maxLandCount; };
    UserModel.prototype.getCLoginDayCount = function () { return this.cloginDayCount; };
    UserModel.prototype.getMaxWheelMul = function () { return this.maxWheelMul; };
    UserModel.prototype.getRankSeason = function () { return this.rankSeason; };
    UserModel.prototype.getRankScore = function () { return this.rankScore; };
    UserModel.prototype.getLogoutSurplusTime = function () { return this.logoutSurplusTime; };
    UserModel.prototype.getFcmConfs = function () { return this.fcmConfs; };
    UserModel.prototype.setFcmConfs = function (val) { this.fcmConfs = val; };
    UserModel.prototype.getSubDatas = function () { return this.subDatas; };
    UserModel.prototype.getRechargeCountRecord = function () { return this.rechargeCountRecord; };
    UserModel.prototype.getPassNewbieIndex = function () { return this.passNewbieIndex; };
    UserModel.prototype.setPassNewbieIndex = function (val) { this.passNewbieIndex = val; };
    UserModel.prototype.setGiveupCount = function (val) { this.giveupCount = val; };
    UserModel.prototype.addGiveupCount = function (val) { this.giveupCount += val; };
    UserModel.prototype.getAccLikeJwmCount = function () { return this.accLikeJwmCount; };
    UserModel.prototype.addAccLikeJwmCount = function (val) { this.accLikeJwmCount += val; };
    UserModel.prototype.getSkinItemList = function () { return this.skinItemList; };
    UserModel.prototype.getSkinItemCountById = function (id) { return this.skinItemList.filter(function (m) { return m.id === id && m.state === 0; }).length; };
    UserModel.prototype.getUnlockPawnSkinIds = function () { return this.unlockPawnSkinIds; };
    UserModel.prototype.getUnlockCitySkinIds = function () { return this.unlockCitySkinIds; };
    UserModel.prototype.getRankRewardRecords = function () { return this.rankRewardRecords; };
    UserModel.prototype.getActivityRecord = function () { return this.activityRecord; };
    UserModel.prototype.addRechargeCountRecord = function (key, count, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        var cnt = this.rechargeCountRecord[key] || 0;
        this.rechargeCountRecord[key] = count + cnt;
        isEmit && this.emit(EventType_1.default.UPDATE_RECHARGE_COUNT);
    };
    UserModel.prototype.setRankScore = function (val) {
        this.rankScore = val;
        // 上报
        EventReportHelper_1.eventReportHelper.checkReportRankScore();
    };
    // 获取累计在线时间
    UserModel.prototype.getSumOnlineTime = function () {
        return this.sumOnlineTime + (Date.now() - this.initTime);
    };
    // 是否可以购买免费金币
    UserModel.prototype.isBuyLimitFreeGold = function () {
        return (Date.now() - this.buyFreeGoldSurplusGetTime) >= this.buyFreeGoldSurplusTime;
    };
    // 是否可以购买自选英雄
    UserModel.prototype.isBuyLimitOptionalHero = function () {
        return (Date.now() - this.buyOptionalHeroSurplusGetTime) >= this.buyOptionalHeroSurplusTime;
    };
    UserModel.prototype.setSid = function (val) {
        this.sid = val;
    };
    UserModel.prototype.setPlaySid = function (val) {
        var sid = val !== null && val !== void 0 ? val : this.playSid;
        if (this.playSid === sid) {
            return;
        }
        this.playSid = sid;
        // 删除旧的sid信息
        if (this.playSid > 0) {
            this.expectPosition = 0;
            this.farmType = 0;
            var lastSid = storageMgr.loadNumber('last_play_sid_' + this.uid);
            if (!!lastSid && this.playSid !== lastSid) {
                storageMgr.remove('preference_' + lastSid + '_' + this.uid);
            }
            if (this.playSid !== lastSid) {
                storageMgr.saveNumber('last_play_sid_' + this.uid, this.playSid);
            }
        }
        this.localPreferenceSidMap = storageMgr.loadJson('preference_' + this.playSid + '_' + this.uid) || {};
    };
    // 期望位置
    UserModel.prototype.getExpectPosition = function () { return this.expectPosition; };
    UserModel.prototype.setExpectPosition = function (val) {
        this.expectPosition = val;
    };
    // 开局模式
    UserModel.prototype.getFarmType = function () { return this.farmType; };
    UserModel.prototype.setFarmType = function (val) {
        this.farmType = val;
    };
    // 是否游客
    UserModel.prototype.isGuest = function () {
        return this.loginType === Enums_1.LoginType.GUEST;
    };
    // 是否有订阅
    UserModel.prototype.isHasSubscription = function () {
        // const now = Date.now()
        return this.subDatas.some(function (m) { return m.leftDays > 0; });
        // return this.subDatas.some(m => Math.max(0, m.surplusTime - (now - m.getTime)) > 0)
    };
    // 刷新订阅信息
    UserModel.prototype.updateSubDatas = function (datas) {
        var now = Date.now();
        this.subDatas = (datas === null || datas === void 0 ? void 0 : datas.map(function (m) {
            var _a;
            m.getTime = now;
            m.payAmount = parseFloat((_a = m.payAmount) === null || _a === void 0 ? void 0 : _a.toFixed(2));
            return m;
        })) || [];
    };
    // 主动请求更新订阅信息
    UserModel.prototype.reqUpdateSubData = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetUserSubInfo', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateSubDatas(data.list || []);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    UserModel.prototype.reqGetMonthlyCardAward = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetMonthCardAward', { type: type })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                            GameHelper_1.gameHpr.addGainMassage(data.rewards);
                            this.updateSubDatas(data.list || []);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测月卡可领取红点
    UserModel.prototype.checkMonthlyCardReward = function () {
        var ok = false;
        var _loop_1 = function (i) {
            var data = Constant_1.MONTH_CARD[i];
            var subData = this_1.subDatas.find(function (m) { return data.PRODUCT_IDS_ANDROID.includes(m.productId); }) || this_1.subDatas.find(function (m) { return data.PRODUCT_IDS_IOS.includes(m.productId); }) || this_1.subDatas.find(function (m) { return data.RECHARGES.includes(m.productId); });
            ok = subData ? (subData.leftDays > 0 && subData.lastAwardTime <= 0) : false; //是否可领取   
            if (ok) {
                return "break";
            }
        };
        var this_1 = this;
        for (var i = 0; i < Constant_1.MONTH_CARD.length; i++) {
            var state_1 = _loop_1(i);
            if (state_1 === "break")
                break;
        }
        return ok;
    };
    // 初始化来之服务器的未完成订单
    UserModel.prototype.initServerNotFinishOrders = function (orders) {
        this.notFinishOrders = orders.map(function (m) {
            m.state = 1;
            return m;
        });
    };
    // 初始化来之native未完成的订单
    UserModel.prototype.initNativeLostOrderList = function (orders) {
        var _this = this;
        orders.forEach(function (m) {
            if (m.cpOrderId === 'null' || !m.cpOrderId) {
                m.cpOrderId = '';
            }
            var order = _this.notFinishOrders.find(function (o) { return o.orderId === m.orderId; });
            if (order) {
                order.token = m.token;
            }
            else {
                _this.notFinishOrders.push(m);
            }
        });
    };
    // 检测是否有未完成订单
    UserModel.prototype.checkHasNotFinishOrder = function (platform) {
        return __awaiter(this, void 0, void 0, function () {
            var i, m;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        i = this.notFinishOrders.length - 1;
                        _a.label = 1;
                    case 1:
                        if (!(i >= 0)) return [3 /*break*/, 5];
                        m = this.notFinishOrders[i];
                        if (!(!!m.userId && m.userId !== this.uid)) return [3 /*break*/, 3];
                        // 这里如果不一样 直接删掉 并消费掉
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: m.token })];
                    case 2:
                        // 这里如果不一样 直接删掉 并消费掉
                        _a.sent();
                        this.notFinishOrders.splice(i, 1);
                        return [3 /*break*/, 4];
                    case 3:
                        if (m.state === 1 && !!m.cpOrderId) {
                            return [3 /*break*/, 4]; //如果是已经验证只需要领取 不用删除直接领取
                        }
                        else if (!!m.platform && m.platform !== platform) {
                            this.notFinishOrders.splice(i, 1); //如果还未验证 需要到对应平台验证
                        }
                        else if (m.productId === Constant_1.RECHARGE_BATTLE_PASS) {
                            return [3 /*break*/, 4]; //战令的
                        }
                        else if (!assetsMgr.getJson('recharge').get('product_id', m.productId)[0]) {
                            this.notFinishOrders.splice(i, 1);
                        }
                        _a.label = 4;
                    case 4:
                        i--;
                        return [3 /*break*/, 1];
                    case 5: return [2 /*return*/, this.notFinishOrders.length > 0];
                }
            });
        });
    };
    // 删除未完成的订单
    UserModel.prototype.removeNotFinishOrders = function (key, val) {
        this.notFinishOrders.remove(key, val);
        return this.notFinishOrders;
    };
    // 是否可以玩新手区
    UserModel.prototype.isCanPlayNewbie = function () {
        var totalGameCount = this.getAccTotalGameCount();
        var newbieCount = this.getAccTotalNewbieCount();
        if (totalGameCount + this.giveupCount >= 5) {
            return false; //已经玩过5次了 不能再玩新手区
        }
        else if (totalGameCount - newbieCount > 0) {
            return false; //已玩过其他区 不能进入新手区
        }
        else if (this.passNewbieIndex > 0 && newbieCount > this.passNewbieIndex) {
            return false; //新手区通关后只能再玩一次新手区
        }
        return true;
    };
    // 是否通关新手区
    UserModel.prototype.isPassNewbie = function () {
        if (this.passNewbieIndex > 0) {
            return true;
        }
        // 已玩过其他区 默认通关了
        var totalGameCount = this.getAccTotalGameCount();
        var newbieCount = this.getAccTotalNewbieCount();
        return totalGameCount - newbieCount > 0;
    };
    // 是否新手 只能玩新手区
    UserModel.prototype.isNewbie = function () {
        return this.getAccTotalNewbieCount() < 1 && this.giveupCount === 0;
    };
    // 元宝
    UserModel.prototype.getIngot = function () { return this.ingot; };
    UserModel.prototype.setIngot = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.ingot);
        this.ingot = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_INGOT, add);
        }
    };
    // 金币
    UserModel.prototype.getGold = function () { return this.gold; };
    UserModel.prototype.setGold = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.gold);
        this.gold = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_GOLD, add);
        }
    };
    // 兵符
    UserModel.prototype.getWarToken = function () { return this.warToken; };
    UserModel.prototype.setWarToken = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.warToken);
        this.warToken = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_WAR_TOKEN, add);
        }
    };
    // 段位积分
    UserModel.prototype.getRankCoin = function () { return this.rankCoin; };
    UserModel.prototype.setRankCoin = function (val) {
        if (val === undefined || isNaN(val)) {
            return;
        }
        this.rankCoin = Math.floor(val);
    };
    // 刷新画像
    UserModel.prototype.setPortrayals = function (list, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.portrayals = [];
        var hasComp = false, unlockCount = 0;
        list.forEach(function (m, i) {
            var info = new PortrayalInfo_1.default().fromSvr(m);
            if (!hasComp && info.isCanComp()) {
                hasComp = true;
            }
            info.index = i;
            _this.portrayals.push(info);
            if (info.isUnlock()) {
                unlockCount += 1;
                EventReportHelper_1.eventReportHelper.checkReportGetPortrayal();
            }
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_PORTRAYALS);
        }
        ReddotHelper_1.reddotHelper.set('can_comp', hasComp);
        // 上报
        if (unlockCount >= 25) {
            EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('hero_25');
        }
        else if (unlockCount >= 20) {
            EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('hero_20');
        }
        else if (unlockCount >= 15) {
            EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('hero_15');
        }
        else if (unlockCount >= 10) {
            EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('hero_10');
        }
        else if (unlockCount >= 6) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('hero_6');
        }
        else if (unlockCount >= 3) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('hero_3');
        }
    };
    UserModel.prototype.getPortrayals = function () { return this.portrayals; };
    // 检测是否可以合成
    UserModel.prototype.checkHasCanCompPortrayal = function () {
        var hasComp = this.portrayals.some(function (m) { return m.isCanComp(); });
        ReddotHelper_1.reddotHelper.set('can_comp', hasComp);
    };
    // 偏好设置
    UserModel.prototype.getLocalPreferenceData = function (key) {
        return this.localPreferenceMap[key];
    };
    UserModel.prototype.setLocalPreferenceData = function (key, val) {
        this.localPreferenceMap[key] = val;
        storageMgr.saveJson('preference_map', this.localPreferenceMap);
        return val;
    };
    // 偏好设置 根据所在区和uid 保存
    UserModel.prototype.getLocalPreferenceDataBySid = function (key) {
        return this.localPreferenceSidMap[key];
    };
    UserModel.prototype.setLocalPreferenceDataBySid = function (key, val) {
        this.localPreferenceSidMap[key] = val;
        storageMgr.saveJson('preference_' + this.sid + '_' + this.uid, this.localPreferenceSidMap);
        return val;
    };
    // 临时的偏好设置
    UserModel.prototype.getTempPreferenceMap = function (key) {
        return this.tempPreferenceMap[key + '_' + this.sid];
    };
    UserModel.prototype.setTempPreferenceData = function (key, val) {
        this.tempPreferenceMap[key + '_' + this.sid] = val;
        return val;
    };
    UserModel.prototype.delTempPreferenceData = function (key) {
        delete this.tempPreferenceMap[key + '_' + this.sid];
    };
    // 兑换金币
    UserModel.prototype.exchangeGold = function (count) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_ExchangeGold', { count: count }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setIngot(data.ingot);
                            this.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 修改名字
    UserModel.prototype.modifyNickname = function (nickname) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_ModifyUserNickname', { nickname: nickname }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.nickname = nickname;
                            this.modifyNameCount = data.modifyNameCount;
                            this.setGold(data.gold);
                            this.emit(EventType_1.default.MODIFY_NICKNAME_SUC);
                            GameHelper_1.gameHpr.rank.updateAllRankUserInfo();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 改变头像
    UserModel.prototype.changeHeadicon = function (headIcon) {
        if (this.setHeadIcon(headIcon)) {
            this.net.send('lobby/HD_ChangeUserHeadIcon', { headIcon: headIcon });
        }
    };
    // 设置头像
    UserModel.prototype.setHeadIcon = function (val) {
        if (this.headIcon !== val) {
            this.headIcon = val;
            var info = GameHelper_1.gameHpr.getPlayerInfo(this.uid);
            if (info) {
                info.headIcon = val;
            }
            this.emit(EventType_1.default.CLOSE_SELECT_CELL);
            this.emit(EventType_1.default.UPDATE_PLAYER_HEAD_ICON, { uid: this.uid, headIcon: val });
            GameHelper_1.gameHpr.rank.updateAllRankUserInfo();
            return true;
        }
        return false;
    };
    // 设置个人简介
    UserModel.prototype.setPersonalDesc = function (val) {
        if (this.personalDesc !== val) {
            this.personalDesc = val;
            var info = GameHelper_1.gameHpr.getPlayerInfo(this.uid);
            if (info) {
                info.personalDesc = val;
            }
        }
    };
    // 设置称号
    UserModel.prototype.setTitle = function (val) {
        if (this.title !== val) {
            this.title = val;
            var info = GameHelper_1.gameHpr.getPlayerInfo(this.uid);
            if (info) {
                info.title = val;
            }
            return true;
        }
        return false;
    };
    // 刷新称号
    UserModel.prototype.getUnlockTitles = function () { return this.titles; };
    UserModel.prototype.setTitles = function (titles) {
        var now = Date.now();
        this.titles = (titles === null || titles === void 0 ? void 0 : titles.map(function (m) {
            m.getTime = now;
            return m;
        })) || [];
    };
    // 刷新邀请好友信息
    UserModel.prototype.updateInviteFriends = function (inviteFriends) {
        this.inviteFriends = inviteFriends !== null && inviteFriends !== void 0 ? inviteFriends : this.inviteFriends;
    };
    UserModel.prototype.setUnlockHeadIcons = function (ids) {
        this.unlockHeadIcons = ids || [];
    };
    UserModel.prototype.setUnlockChatEmojiIds = function (ids) {
        this.unlockChatEmojiIds = ids || [];
    };
    UserModel.prototype.setUnlockPawnSkinIds = function (ids) {
        this.unlockPawnSkinIds = ids || [];
    };
    UserModel.prototype.isHasPawnSkinById = function (id) {
        return this.unlockPawnSkinIds.indexOf(id) !== -1 || this.skinItemList.some(function (m) { return m.state >= 0 && m.id === id; });
    };
    UserModel.prototype.reqSkinItemList = function (loading) {
        if (loading === void 0) { loading = false; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetSkinItems', {}, loading)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setSkinItemList(data.list);
                            this.setUnlockPawnSkinIds(data.unlockPawnSkinIds);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    UserModel.prototype.setSkinItemList = function (items) {
        this.skinItemList = items || [];
    };
    UserModel.prototype.removeSkinItem = function (id, uid) {
        var data = this.skinItemList.find(function (m) { return m.id === id && m.uid === uid; });
        if (data) {
            this.skinItemList.remove('uid', uid);
        }
    };
    // 获取士兵皮肤列表
    UserModel.prototype.getPawnSkins = function (id) {
        var arr = [{ id: 0, unlock: true }]; //默认把自己装进去
        var unlockMap = {}, serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        this.unlockPawnSkinIds.forEach(function (m) { return unlockMap[m] = true; });
        this.skinItemList.forEach(function (m) { return m.state >= 0 && (unlockMap[m.id] = true); });
        assetsMgr.getJson('pawnSkin').datas.forEach(function (m) {
            if (m.pawn_id !== id) {
                return;
            }
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            if (startTime && ut.timediff(Date.now(), startTime) > 0) { // 避免剧透
                return;
            }
            var unlock = !!unlockMap[m.id], gold = 0;
            if (unlock) {
            }
            else if (startTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) { //是否有时间限制
            }
            else if (m.cond === 1) { //商城购买
                gold = m.gold || 0;
            }
            arr.push({ id: m.id, unlock: unlock, gold: gold });
        });
        arr.sort(function (a, b) {
            var aw = a.unlock ? 1 : 0, bw = b.unlock ? 1 : 0;
            return bw - aw;
        });
        return arr;
    };
    // 获取可以购买的士兵皮肤
    UserModel.prototype.getCanBuyPawnSkins = function (serverArea) {
        var arr = [], unlockMap = {};
        this.unlockPawnSkinIds.forEach(function (m) { return unlockMap[m] = true; });
        assetsMgr.getJson('pawnSkin').datas.forEach(function (m) {
            if (unlockMap[m.id] || (m.cond !== 1 && m.cond !== 4 && m.cond < 100)) { // 1是常驻，2是宝典，3是段位商城，4是限定，5是炫彩，100以上是盲盒
                return; //已经有了 或者不是商城购买的
            }
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            if (m.cond !== 1 && startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) { // 宝典、限定、盲盒和炫彩都有限时，常驻加时间仅仅是为了显示上新，所以不做判断
                return; //是否有时间限制
            }
            arr.push(m);
        });
        return arr;
    };
    // 购买皮肤
    UserModel.prototype.buyPawnSkin = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyPawnSkin', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockPawnSkinIds(data.unlockPawnSkinIds);
                            this.setGold(data.gold);
                            this.setIngot(data.ingot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取可以购买的主城皮肤
    UserModel.prototype.getCanBuyCitySkins = function (serverArea) {
        var arr = [], unlockMap = {};
        this.unlockCitySkinIds.forEach(function (m) { return unlockMap[m] = true; });
        assetsMgr.getJson('citySkin').datas.forEach(function (m) {
            if (unlockMap[m.id] || (m.cond !== 1 && m.cond !== 4 && m.cond < 100)) {
                return; //已经有了 或者不是商城购买的
            }
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            if (m.cond === 4 && startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) { // 4是限时购买，1仅仅是为了显示上新，所以不加时间判断
                return; //是否有时间限制
            }
            arr.push(m);
        });
        return arr;
    };
    // 购买主城皮肤
    UserModel.prototype.buyCitySkin = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyCitySkin', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockCitySkinIds(data.unlockCitySkinIds);
                            this.setGold(data.gold);
                            this.setIngot(data.ingot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取已解锁的表情
    UserModel.prototype.getUnlockChatEmojiIds = function () {
        return this.unlockChatEmojiIds;
    };
    // 获取可购买的聊天表情
    UserModel.prototype.getCanBuyChatEmojis = function (serverArea) {
        var arr = [];
        var unlockMap = {};
        this.unlockChatEmojiIds.forEach(function (m) { return unlockMap[m] = true; });
        assetsMgr.getJson('chatEmoji').datas.forEach(function (m) {
            if (unlockMap[m.id] || (m.cond !== 1 && m.cond !== 4)) {
                return; //已经有了 或者不是商城购买的
            }
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            if (m.cond === 4 && startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) { // 4是限时购买，1仅仅是为了显示上新，所以不加时间判断
                return; //是否有时间限制
            }
            arr.push(m);
        });
        return arr;
    };
    // 购买聊天表情
    UserModel.prototype.buyChatEmoji = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyChatEmoji', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.unlockChatEmojiIds = data.unlockChatEmojiIds || [];
                            this.setIngot(data.ingot);
                            this.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 设置城市皮肤
    UserModel.prototype.setUnlockCitySkinIds = function (ids) {
        this.unlockCitySkinIds = ids || [];
    };
    // 获取城市皮肤列表
    UserModel.prototype.getCitySkinList = function (id) {
        return id ? this.unlockCitySkinIds.filter(function (m) { return Math.floor(m / 1000) === id; }) : [];
    };
    // 获取邮件
    UserModel.prototype.getMails = function (init) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, translateMap, hasNotRead;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!init && this.lastReqMailsTime > 0 && Date.now() - this.lastReqMailsTime <= ut.Time.Minute * 10) {
                            return [2 /*return*/, this.tempMailList];
                        }
                        return [4 /*yield*/, this.net.request('mail/HD_GetMails', { sid: this.sid })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        this.lastReqMailsTime = Date.now();
                        translateMap = {};
                        this.tempMailList.forEach(function (m) { return translateMap[m.uid] = m.translate; });
                        this.tempMailList = [];
                        hasNotRead = false;
                        (_a = data === null || data === void 0 ? void 0 : data.list) === null || _a === void 0 ? void 0 : _a.forEach(function (m) {
                            m.getTime = _this.lastReqMailsTime;
                            if (m.contentId) {
                                m.titleParames = m.title.split('|');
                                m.title = assetsMgr.lang('mailText.title_' + m.contentId, _this.toMailParames(m.titleParames));
                                m.contentParames = m.content.split('|');
                                m.content = assetsMgr.lang('mailText.content_' + m.contentId, _this.toMailParames(m.contentParames));
                            }
                            if (!!m.sender && m.sender !== '-1' && !m.senderName) {
                                m.senderName = GameHelper_1.gameHpr.getPlayerName(m.sender);
                            }
                            m.translate = translateMap[m.uid];
                            _this.tempMailList.push(m);
                            if (m.state !== Enums_1.MailStateType.READ) {
                                hasNotRead = true;
                            }
                        });
                        // 设置新邮件红点
                        if (init) {
                            ReddotHelper_1.reddotHelper.set('new_mail', hasNotRead);
                        }
                        return [2 /*return*/, this.tempMailList];
                }
            });
        });
    };
    UserModel.prototype.getTempMails = function () {
        return this.tempMailList;
    };
    UserModel.prototype.toMailParames = function (parames) {
        return parames.map(function (s) {
            if (s.startsWith('@serverName')) { //服务器名字
                var _a = __read(s.split('_'), 3), _ = _a[0], type = _a[1], no = _a[2];
                return assetsMgr.lang('ui.server_name_' + type, no);
            }
            else if (s.startsWith('@skinIds')) { //皮肤列表
                var _b = __read(s.split('_'), 2), _ = _b[0], str = _b[1];
                return str ? str.split(';').join2(function (m) {
                    var _a = __read(m.split('x'), 2), id = _a[0], count = _a[1];
                    var json = assetsMgr.getJsonData('pawnSkin', id);
                    if (!json) {
                        return m;
                    }
                    var skinName = assetsMgr.lang(json.desc);
                    var pawnName = assetsMgr.lang('pawnText.name_' + json.pawn_id);
                    return "[" + skinName + pawnName + "x" + count + "]";
                }, '、') : s;
            }
            return s;
        });
    };
    UserModel.prototype.updateTempMailLang = function () {
        var _this = this;
        this.tempMailList.forEach(function (m) {
            if (m.contentId) {
                m.title = assetsMgr.lang('mailText.title_' + m.contentId, _this.toMailParames(m.titleParames));
                m.content = assetsMgr.lang('mailText.content_' + m.contentId, _this.toMailParames(m.contentParames));
            }
            if (!!m.sender && m.sender !== '-1' && !m.senderName) {
                m.senderName = GameHelper_1.gameHpr.getPlayerName(m.sender);
            }
        });
        return this.tempMailList;
    };
    // 删除邮件
    UserModel.prototype.removeMail = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('mail/HD_RemoveMail', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.tempMailList.remove('uid', uid);
                            this.emit(EventType_1.default.REMOVE_MAIL, uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 删除所有已读邮件
    UserModel.prototype.delAllReadMail = function () {
        return __awaiter(this, void 0, void 0, function () {
            var uids, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        uids = [];
                        this.tempMailList.forEach(function (m) {
                            if (m.state === Enums_1.MailStateType.READ) {
                                uids.push(m.uid);
                            }
                        });
                        return [4 /*yield*/, this.net.request('mail/HD_RemoveAllReadMail', { uids: uids }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.tempMailList.delete(function (m) { return m.state === Enums_1.MailStateType.READ; });
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取可购买的头像
    UserModel.prototype.getCanBuyHeadIcons = function (serverArea) {
        var arr = [];
        var unlockMap = {}, appType = GameHelper_1.gameHpr.getAppType();
        this.unlockHeadIcons.forEach(function (m) { return unlockMap[m] = true; });
        assetsMgr.getJson('headIcon').datas.forEach(function (m) {
            if (unlockMap[m.icon] || (m.cond !== 1 && m.cond !== 4)) {
                return; //已经有了 或者不是商城购买的
            }
            else if (m.platform && m.platform !== appType) {
                return; //平台不符
            }
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            if (m.cond === 4 && startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) { // 4是限时购买，1仅仅是为了显示上新，所以不加时间判断
                return; //是否有时间限制
            }
            arr.push(m);
        });
        return arr;
    };
    // 购买头像
    UserModel.prototype.buyHeadIcon = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyHeadIcon', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockHeadIcons(data.unlockHeadIcons);
                            this.setIngot(data.ingot);
                            this.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    UserModel.prototype.checkCanBuyFreeGold = function () {
        return this.isBuyLimitFreeGold();
    };
    // 购买免费金币
    UserModel.prototype.buyFreeGold = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyFreeGold', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.buyFreeGoldSurplusTime = data.buyFreeGoldSurplusTime || 0;
                            this.buyFreeGoldSurplusGetTime = Date.now();
                            this.setGold(data.gold);
                            ReddotHelper_1.reddotHelper.set('free_gold', false);
                            GameHelper_1.gameHpr.addGainMassage({ type: Enums_1.CType.GOLD, count: 3 });
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 购买英雄
    UserModel.prototype.buyHero = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BuyHero', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.buyOptionalHeroSurplusTime = data.buyOptionalHeroSurplusTime || 0;
                            this.buyOptionalHeroSurplusGetTime = Date.now();
                            this.setIngot(data.ingot);
                            this.setPortrayals(data.portrayals);
                            this.emit(EventType_1.default.UPDATE_PORTRAYAL_INFO);
                            // 上报
                            EventReportHelper_1.eventReportHelper.reportAppflyerEvent('all_hero_seletion');
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 初始化转动信息
    UserModel.prototype.initWheelInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetWheelInfo', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateWheelData(data);
                            this.updateWheelAwardDot();
                            this.emit(EventType_1.default.INIT_WHEEL_COMPLETE);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 刷新大转盘数据
    UserModel.prototype.updateWheelData = function (data) {
        var _a, _b;
        data = data || {};
        if (data.wheelRecords && data.wheelRecords.length > 0) {
            this.wheelRecords = data.wheelRecords.reverse();
        }
        if (!data.info) {
            data.info = {};
        }
        this.wheelWaitTime = data.info.wheelWaitTime || 0;
        this.setWheelCurrCount(data.info.wheelCurrCount || 0);
        this.wheelResidueCount = data.info.wheelResidueCount || 0;
        this.wheelFreeCount = data.info.wheelFreeCount || 0;
        this.wheelRandomAwards = (_a = data.info.wheelRandomAwards) !== null && _a !== void 0 ? _a : [];
        this.wheelRandomAwardRecords = ((_b = data.info.wheelRandomAwardRecords) !== null && _b !== void 0 ? _b : []).reverse();
        this.wheelInRoomRunDay = data.info.wheelInRoomRunDay || 0;
        this.wheelGetTime = Date.now();
        ReddotHelper_1.reddotHelper.set('wheel_free_count', !!this.wheelFreeCount);
    };
    UserModel.prototype.getWheelWaitTime = function () { return Math.max(this.wheelWaitTime - (Date.now() - this.wheelGetTime), 0); };
    UserModel.prototype.getWheelRecords = function () { return this.wheelRecords; };
    UserModel.prototype.getWheelResidueCount = function () { return this.wheelResidueCount; };
    UserModel.prototype.getWheelFreeCount = function () { return this.wheelFreeCount; };
    UserModel.prototype.getWheelRandomAwards = function () { return this.wheelRandomAwards; };
    UserModel.prototype.getWheelRandomAwardRecords = function () { return this.wheelRandomAwardRecords; };
    UserModel.prototype.getWheelCurrCount = function () { return this.wheelCurrCount; };
    UserModel.prototype.getWheelInRoomRunDay = function () { return this.wheelInRoomRunDay; };
    UserModel.prototype.setWheelCurrCount = function (val) {
        this.wheelCurrCount = val;
        // 上报
        if (val >= 5) {
            EventReportHelper_1.eventReportHelper.reportGlobalEventOne('lucky_wheel_5');
        }
    };
    // 刷新历史最大倍数
    UserModel.prototype.updateMaxWheelMul = function (lastMul) {
        if (lastMul > this.maxWheelMul) {
            this.maxWheelMul = lastMul;
        }
    };
    // 领取转盘奖励
    UserModel.prototype.claimWheelAward = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, it;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_ClaimWheelAward', { sid: this.sid, uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                            it = this.wheelRecords.find(function (m) { return m.uid === uid; });
                            if (it) {
                                it.isClaim = true;
                                this.updateWheelAwardDot();
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    UserModel.prototype.updateWheelAwardDot = function () {
        var sid = this.sid;
        ReddotHelper_1.reddotHelper.set('wheel_award', this.wheelRecords.some(function (m) { return m.items.length > 0 && !m.isClaim && (m.needSid === 0 || m.needSid === sid); }));
    };
    UserModel.prototype.checkCanWheel = function (val) {
        return (this.getWheelResidueCount() > 0 || this.wheelCurrCount < 10) && this.getWheelWaitTime() <= 0;
    };
    // 检测是否可以弹好评窗
    UserModel.prototype.checkTriggerPraiseTip = function () {
        if (ut.isMiniGame() || this.checkPraiseCount >= 3 && this.tempTriggerPraiseCount <= 0) {
            return false;
        }
        else if (ut.chance(75) || !GameHelper_1.gameHpr.getGameDownloadUrl()) {
            return false;
        }
        var count = GameHelper_1.gameHpr.getPlayerOweCellCount();
        if (this.checkPraiseCount < 1 && count >= 30) {
        }
        else if (this.checkPraiseCount < 2 && count >= 100) {
        }
        else if (this.checkPraiseCount < 3 && count >= 500) {
        }
        else {
            return false;
        }
        this.setCheckPraiseCount(this.checkPraiseCount + 1);
        this.tempTriggerPraiseCount -= 1;
        return true;
    };
    UserModel.prototype.setCheckPraiseCount = function (val, mailId) {
        if (mailId === void 0) { mailId = 0; }
        this.checkPraiseCount = val;
        this.net.send('lobby/HD_SyncCheckPraiseCount', { checkPraiseCount: val, mailId: mailId });
    };
    // 战斗预测免费次数
    UserModel.prototype.getBattleForecastFreeCount = function () {
        return Math.max(0, Constant_1.BATTLE_FORECAST_FREE_COUNT - this.battleForecastCount);
    };
    // 扣除战斗预测费用
    UserModel.prototype.deductBattleForecastCost = function (landlv, landDis) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_BattleForecast', {
                            sid: this.sid,
                            landCount: GameHelper_1.gameHpr.getPlayerOweCellCount(),
                            maincityLevel: GameHelper_1.gameHpr.player.getMainBuildLv(),
                            landDis: landDis,
                            landlv: landlv,
                        })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setGold(data.gold);
                            this.battleForecastCount = data.battleForecastCount;
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 发送喇叭
    UserModel.prototype.sendTrumpet = function (content) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.getIngot() < Constant_1.SEND_TRUMPET_COST) {
                            return [2 /*return*/, ECode_1.ecode.INGOT_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_SendTrumpet', { content: content }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setIngot(data.ingot);
                            this.setTodaySendTrumpetCount(data.todaySendTrumpetCount);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 合成指定的英雄残卷
    UserModel.prototype.compPortrayalDebris = function (id, idMap) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_CompPortrayalDebris', { id: id, idMap: idMap }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setPortrayals(data.portrayals);
                            this.emit(EventType_1.default.UPDATE_PORTRAYAL_INFO);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 领取段位赛季奖励
    UserModel.prototype.claimRankSeasonReward = function (id) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetRankReward', { id: id }, true)];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                            this.setRankRewardRecords(data.rankRewardRecord);
                            this.checkRankRewardReddot();
                            GameHelper_1.gameHpr.stringToCTypes((_a = assetsMgr.getJsonData('seasonReward', id)) === null || _a === void 0 ? void 0 : _a['s' + this.rankSeason]).forEach(function (m) {
                                if (m.type === Enums_1.CType.RANK_COIN) {
                                    GameHelper_1.gameHpr.addGainMassage(m);
                                }
                            });
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    UserModel.prototype.setRankRewardRecords = function (arr) {
        this.rankRewardRecords = arr;
    };
    // 检测段位奖励可领取红点
    UserModel.prototype.checkRankRewardReddot = function () {
        var claimMap = {};
        this.rankRewardRecords.forEach(function (m) { return claimMap[m] = true; });
        var _a = GameHelper_1.gameHpr.resolutionRankScore(this.rankScore, this.getAccTotalRankCount()), id = _a.id, winPoint = _a.winPoint;
        var ok = assetsMgr.getJson('seasonReward').datas.some(function (m) { return id >= m.id && !claimMap[m.id]; });
        ReddotHelper_1.reddotHelper.set('rank_reward', ok);
    };
    // 兑换城市皮肤
    UserModel.prototype.rsExchangeCitySkin = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_RSExchangeCitySkin', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockCitySkinIds(data.unlockCitySkinIds);
                            this.setRankCoin(data.rankCoin);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 兑换士兵皮肤
    UserModel.prototype.rsExchangePawnSkin = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_RSExchangePawnSkin', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockPawnSkinIds(data.unlockPawnSkinIds);
                            this.setRankCoin(data.rankCoin);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 兑换聊天表情
    UserModel.prototype.rsExchangeChatEmoji = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_RSExchangeChatEmoji', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockChatEmojiIds(data.unlockChatEmojiIds);
                            this.setRankCoin(data.rankCoin);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 兑换头像
    UserModel.prototype.rsExchangeHeadicon = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_RSExchangeHeadIcon', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockHeadIcons(data.unlockHeadIcons);
                            this.setRankCoin(data.rankCoin);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取当前种植信息
    UserModel.prototype.getPlantData = function () { return this.plantData; };
    UserModel.prototype.setPlantData = function (data) {
        if (data === null || data === void 0 ? void 0 : data.remainTime) {
            data.getTime = Date.now();
        }
        this.plantData = data;
    };
    // 解锁的植物
    UserModel.prototype.getUnlockBotanys = function () { return this.unlockBotanys; };
    UserModel.prototype.setUnlockBotanys = function (arr) {
        this.unlockBotanys = arr || [];
    };
    // 种植
    UserModel.prototype.planting = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_Planting', { id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setGold(data.gold);
                            this.setPlantData(data.plantData);
                            this.emit(EventType_1.default.UPDATE_PLANT);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 浇水
    UserModel.prototype.watering = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_Watering', { type: type }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setGold(data.gold);
                            this.setPlantData(data.plantData);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 采集
    UserModel.prototype.gatherBotany = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GatherBotany', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setUnlockBotanys(data.unlockBotanys);
                            this.setPlantData({});
                            this.emit(EventType_1.default.UPDATE_PLANT);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    UserModel.prototype.getBattlePassInfo = function () {
        return this.tempBattlePassInfo;
    };
    UserModel.prototype.setBattlePassInfo = function (data) {
        this.tempBattlePassInfo = data.info;
    };
    UserModel.prototype.setBattlePassBuyPass = function () {
        if (this.tempBattlePassInfo) {
            this.tempBattlePassInfo.buyPass = true;
            this.tempBattlePassInfo.score += 300;
            this.emit(EventType_1.default.UPDATE_BATTLE_PASS_INFO, this.tempBattlePassInfo);
        }
    };
    UserModel.prototype.updateBattlePassRewarded = function (ids, isPay) {
        if (this.tempBattlePassInfo) {
            for (var i = 0; i < ids.length; i++) {
                var id = ids[i];
                if (isPay && !this.tempBattlePassInfo.rewardedPay.has(id)) {
                    this.tempBattlePassInfo.rewardedPay.push(id);
                }
                else if (!this.tempBattlePassInfo.rewarded.has(id)) {
                    this.tempBattlePassInfo.rewarded.push(id);
                }
            }
        }
    };
    // 红点检测
    UserModel.prototype.checkBattlePassReddot = function () {
        var _this = this;
        var rewards = assetsMgr.getJson('battlePass').datas.filter(function (m) {
            return m.score <= _this.tempBattlePassInfo.score
                && Math.floor(m.id / 1000) === _this.getOpeningBattlePassCfg().id
                && (!_this.tempBattlePassInfo.rewarded.has(m.id) || (_this.tempBattlePassInfo.buyPass && !_this.tempBattlePassInfo.rewardedPay.has(m.id)));
        });
        ReddotHelper_1.reddotHelper.set('battle_pass', rewards.length > 0);
    };
    // 获取当前开放的战令配置信息
    UserModel.prototype.getOpeningBattlePassCfg = function () {
        var id = 0, startTime = '', endTime = '';
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        assetsMgr.getJson('battlePassBase').datas.forEach(function (m) {
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), _startTime = _a[0], _endTime = _a[1];
            if (_startTime && GameHelper_1.gameHpr.checkActivityAutoDate(_startTime, _endTime)) {
                id = m.id;
                startTime = _startTime;
                endTime = _endTime;
            }
        });
        return { id: id, startTime: startTime, endTime: endTime };
    };
    // 请求战令信息
    UserModel.prototype.reqBattlePassInfo = function (init) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!init && this.lastReqBattlePassTime > 0 && Date.now() - this.lastReqBattlePassTime <= ut.Time.Second * 10) {
                            return [2 /*return*/, { err: '', info: this.tempBattlePassInfo }];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetBattlePassInfo', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqBattlePassTime = Date.now();
                        if (!err) {
                            this.setBattlePassInfo(data);
                            init && this.checkBattlePassReddot();
                        }
                        return [2 /*return*/, { err: err, info: this.tempBattlePassInfo }];
                }
            });
        });
    };
    // 领取战令奖励
    UserModel.prototype.receiveBattlePassReward = function (id, isPay, heros) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetBattlePassReward', { id: id, isPay: isPay, heros: heros }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && data.rewards) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 购买战令积分
    UserModel.prototype.buyBattlePassExp = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuyBattlePassScore', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setBattlePassInfo(data);
                            this.setIngot(data.ingot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 盲盒红点检测
    UserModel.prototype.checkClickMysteryBoxTabReddot = function () {
        var cond = 0;
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        assetsMgr.getJson('pawnSkin').datas.forEach(function (m) {
            if (m.cond > 100) {
                var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                if (startTime && endTime && GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) {
                    cond = m.cond;
                }
            }
        });
        if (!cond) {
            ReddotHelper_1.reddotHelper.set('mystery_box', false);
        }
        else {
            var click = storageMgr.loadBool('click_MysteryBox_tab' + cond);
            ReddotHelper_1.reddotHelper.set('mystery_box', !!!click);
        }
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新用户信息
    UserModel.prototype.OnUpdateUserInfo = function (data) {
        var _this = this;
        cc.log('OnUpdateUserInfo', data.list);
        data.list.forEach(function (m) {
            var data = m['data_' + m.type];
            if (m.type === Enums_1.NotifyType.NEW_MAIL) { //有新邮件
                _this.lastReqMailsTime = 0;
                ReddotHelper_1.reddotHelper.set('new_mail', true);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_ITEMS) { //更新通用物品
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_INVITES) { //刷新邀请列表
                if (Array.isArray(data)) {
                    _this.inviteFriends = data;
                }
                else {
                    _this.inviteFriends = [];
                    WxHelper_1.wxHelper.errorAndFilter('OnUpdatePlayerInfo', 'invites is not array', data);
                }
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_GENERAL_TASKS) { //刷新常规任务
                GameHelper_1.gameHpr.task.updateGeneralTasks(data || []);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_WHEEL_COUNT) { //转盘次数
                _this.setWheelCurrCount(data || 0);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TASKS) { //刷新任务进度
                GameHelper_1.gameHpr.task.updateGeneralTasksProgress(data.generalTasks || []);
                GameHelper_1.gameHpr.task.updateAchieveTasksProgress(data.achieveTasks || []);
            }
            else if (m.type === Enums_1.NotifyType.USER_SUBSCRIPTION) { //用户订阅更新
                _this.updateSubDatas(data);
                _this.emit(EventType_1.default.UPDATE_SUBSCRIPTION, _this.subDatas);
            }
            else if (m.type === Enums_1.NotifyType.TODAY_TRUMPET_COUNT) { //当天喇叭次数
                _this.setTodaySendTrumpetCount(data || 0);
            }
            else if (m.type === Enums_1.NotifyType.TODAY_FREE_GOLD_TIME) { //每日免费金币剩余时间
                _this.buyFreeGoldSurplusTime = data || 0;
                _this.buyFreeGoldSurplusGetTime = Date.now();
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TITLES) { //刷新称号列表
                _this.setTitles(data.titles);
                _this.setTitle(data.title);
            }
            else if (m.type === Enums_1.NotifyType.ACTIVITY_RECORD) { //活动记录
                _this.activityRecord = data;
                _this.emit(EventType_1.default.UPDATE_ACTIVITY_RECORD);
            }
            else if (m.type === Enums_1.NotifyType.BATTLE_PASS_HAS_AWARD) { //战令有奖励可领取
                _this.lastReqBattlePassTime = 0;
                ReddotHelper_1.reddotHelper.set('battle_pass', true);
            }
        });
    };
    __decorate([
        ut.syncLock
    ], UserModel.prototype, "initWheelInfo", null);
    UserModel = __decorate([
        mc.addmodel('user')
    ], UserModel);
    return UserModel;
}(mc.BaseModel));
exports.default = UserModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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