
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceEnemyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c4cadtATQpHrIh9YBCSs8iq', 'NoviceEnemyObj');
// app/script/model/guide/NoviceEnemyObj.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var EquipInfo_1 = require("../main/EquipInfo");
var NoviceConfig_1 = require("./NoviceConfig");
var PELTAST_ID = 3205; //默认盾兵
var ARCHER_ID = 3305; //默认弓兵
var PELTAST_EQUIP_ID = 6028; //盾兵装备
var ARCHER_EQUIP_ID = 6005; //弓兵装备
// 新手村敌人
var NoviceEnemyObj = /** @class */ (function () {
    function NoviceEnemyObj() {
        this.enemyUID = '911';
        this.armyUIDMap = {}; //军队
        this.finishTriggerMap = {}; //完成触发
        this.equipMap = {}; //装备
        this.supportMap = {}; //支援地块
        this.supportDelayTime = 3 * 1000; //支援延时
        this.firstAttackKey = '999';
        this.CITY_FORT_NID_INDEX = 973;
        this.tempAreaPointMap = {}; //临时缓存
        this.tempAreaPointIndexMap = {};
        //condList第1位type类型:1-玩家攻占地块数量,2-敌人攻占地块数量,3-建筑等级,4-玩家攻占地块距离,5-敌人攻占地块下标
        this.allTriggerList = [
            { condList: [[1, 3]], triggerList: [{ func: this.initEnemy.bind(this), args: null }] },
            {
                condList: [[1, 3]], triggerList: [{ func: this.createEnemyArmy.bind(this), args: { index: 1, pawnList: [{ id: PELTAST_ID, count: 4 }] } },
                    { func: this.occupyCell.bind(this), args: { index: 872, armyList: [1] } }]
            },
            {
                condList: [[5, 872], [4, 1]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 873, armyList: [1] } }]
            },
            {
                condList: [[5, 873], [4, 2]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 923, armyList: [1] } }]
            },
            {
                condList: [[5, 923], [4, 3]], triggerList: [{ func: this.resetArmyPanws.bind(this), args: { index: 1, areaIndex: 923, pawnList: [{ id: PELTAST_ID, count: 4 }, { id: ARCHER_ID, count: 2 }] } },
                    { func: this.occupyCell.bind(this), args: { index: 973, armyList: [1] } }]
            },
            {
                condList: [[5, 973], [4, 4]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 1023, armyList: [1] } }]
            },
            //打周围地块
            {
                condList: [[1, 5]], triggerList: [{ func: this.createEnemyArmy.bind(this), args: { index: 4, pawnList: [{ id: PELTAST_ID, count: 3 }] } },
                    { func: this.occupyCell.bind(this), args: { index: 919, armyList: [4] } }]
            },
            {
                condList: [[1, 5]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 869, armyList: [4] } }]
            },
            {
                condList: [[1, 7]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 820, armyList: [4] } }]
            },
            {
                condList: [[1, 10]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 821, armyList: [4] } }]
            },
            {
                condList: [[1, 13]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 918, armyList: [4] } }]
            },
            {
                condList: [[1, 17]], triggerList: [{ func: this.occupyCell.bind(this), args: { index: 770, armyList: [4] } }]
            },
        ];
    }
    NoviceEnemyObj.prototype.stripObject = function (obj) {
        var stripObj = {};
        for (var key in obj) {
            stripObj[key] = obj[key].strip();
        }
        return stripObj;
    };
    NoviceEnemyObj.prototype.strip = function () {
        return {
            armyUIDMap: this.armyUIDMap,
            finishTriggerMap: this.finishTriggerMap,
            equipMap: this.stripObject(this.equipMap),
            supportMap: this.supportMap,
        };
    };
    NoviceEnemyObj.prototype.load = function (data) {
        for (var key in data) {
            this[key] = data[key];
        }
        for (var key in this.equipMap) {
            this.equipMap[key] = new EquipInfo_1.default().fromSvr(this.equipMap[key]);
        }
        this.tempAreaPointMap = {};
        this.tempAreaPointIndexMap = {};
    };
    NoviceEnemyObj.prototype.getEnemyUID = function () {
        return this.enemyUID;
    };
    NoviceEnemyObj.prototype.trigger = function () {
        var playerLandCount = GameHelper_1.gameHpr.getPlayerOweCellCount(GameHelper_1.gameHpr.getUid(), true);
        var enemyLandCount = GameHelper_1.gameHpr.getPlayerOweCellCount(this.enemyUID, true);
        var playerCellMaxDis = this.getPlayerMaxDistance();
        for (var i = 0; i < this.allTriggerList.length; i++) {
            var condList = this.allTriggerList[i].condList;
            var condFull = true;
            for (var j = 0; j < condList.length; j++) {
                var condType = condList[j][0];
                var condValue = condList[j][1];
                if ((condType === 1 && playerLandCount < condValue) || (condType === 2 && enemyLandCount < condValue)
                    || (condType === 3 && !this.checkBuildLv(condValue, condList[j][2])) || (condType === 4 && playerCellMaxDis < condValue)
                    || (condType === 5 && GameHelper_1.gameHpr.noviceServer.getArea(condValue).owner !== this.enemyUID)) {
                    condFull = false;
                    break;
                }
            }
            if (condFull) {
                this.checkFunc(this.allTriggerList[i].triggerList, i.toString());
            }
        }
        if (GameHelper_1.gameHpr.novice.isCanAttackPlayer) {
            if (!this.finishTriggerMap[this.firstAttackKey]) {
                var info = this.getDistanceInfoWithPlayer();
                if (info.distance <= 2) {
                    this.firstAttackPlayer(info.enemyPos, info.playerPos);
                }
            }
        }
        this.tempAreaPointIndexMap = {};
        this.updateSupportArmy();
    };
    // 添加要塞
    NoviceEnemyObj.prototype.checkAddCity = function () {
        var area = GameHelper_1.gameHpr.noviceServer.getArea(this.CITY_FORT_NID_INDEX);
        if (area && area.owner === this.getEnemyUID() && area.cityId === 0) {
            area.updateCity(Constant_1.CITY_FORT_NID, true);
            GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ADD_CELL, area.toBaseData());
        }
    };
    // 获取是否攻击玩家
    NoviceEnemyObj.prototype.getIsAttackingPlayerArea = function () {
        var index = this.finishTriggerMap[this.firstAttackKey] || 0;
        if (index > 0) {
            var area = GameHelper_1.gameHpr.noviceServer.getArea(index);
            for (var i = 0; i < area.armys.length; i++) {
                if (area.armys[i].uid === this.getEnemyUID()) {
                    return true;
                }
            }
            var marchs = GameHelper_1.gameHpr.noviceServer.getMarchs();
            for (var i = 0; i < marchs.length; i++) {
                var march = marchs[i];
                if (march.owner === this.getEnemyUID() && march.targetIndex === index) {
                    return true;
                }
            }
        }
        return false;
    };
    // 获取是否完成首次攻击
    NoviceEnemyObj.prototype.getIsFinishFirstAttack = function () {
        if (this.finishTriggerMap[this.firstAttackKey] && !this.getIsAttackingPlayerArea()) {
            return true;
        }
        return false;
    };
    // 获取玩家最近距离
    NoviceEnemyObj.prototype.getDistanceInfoWithPlayer = function () {
        var distance = 999;
        var beginIndex = NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX, endIndex = NoviceConfig_1.NOVICE_MAINCITY_INDEX;
        var beginPoint = cc.v2(beginIndex % NoviceConfig_1.NOVICE_MAP_SIZE.x, Math.floor(beginIndex / NoviceConfig_1.NOVICE_MAP_SIZE.x));
        var endPoint = cc.v2(endIndex % NoviceConfig_1.NOVICE_MAP_SIZE.x, Math.floor(endIndex / NoviceConfig_1.NOVICE_MAP_SIZE.x));
        var enemyMaxDis = 0, enemyPos = null, playerMin = 999, playerPos = null;
        for (var y = beginPoint.y; y <= endPoint.y; y++) {
            for (var x = beginPoint.x; x <= endPoint.x; x++) {
                var pos = cc.v2(x, y);
                var dis = pos.x - beginPoint.x + pos.y - beginPoint.y;
                var cell = GameHelper_1.gameHpr.world.getMapCellByPoint(pos);
                if (cell.owner === this.enemyUID) {
                    if (dis > enemyMaxDis) {
                        enemyMaxDis = dis;
                        enemyPos = pos;
                    }
                }
                if (cell.owner === GameHelper_1.gameHpr.getUid()) {
                    if (dis < playerMin) {
                        playerMin = dis;
                        playerPos = pos;
                    }
                }
            }
        }
        if (enemyPos && playerPos) {
            distance = Math.abs(playerPos.x - enemyPos.x) + Math.abs(playerPos.y - enemyPos.y);
        }
        return { distance: distance, enemyPos: enemyPos, playerPos: playerPos };
    };
    // 获取玩家最远距离
    NoviceEnemyObj.prototype.getPlayerMaxDistance = function () {
        var distance = 0;
        var mapCells = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid()).cells;
        mapCells.forEach(function (cell) {
            var dis = GameHelper_1.gameHpr.noviceServer.getDistanceToMainCity(cell);
            distance = Math.max(distance, dis);
        });
        return distance;
    };
    // 检查建筑等级
    NoviceEnemyObj.prototype.checkBuildLv = function (buildId, buildLv) {
        if (GameHelper_1.gameHpr.player.getBuildLv(buildId) < buildLv) {
            return false;
        }
        //兵营满足等级，并且有研究兵
        if (buildId === 2004) {
            var slot = GameHelper_1.gameHpr.player.getPawnSlotByLv(2);
            if (slot && slot.id) {
                return true;
            }
            return false;
        }
        return true;
    };
    NoviceEnemyObj.prototype.checkFunc = function (funList, key) {
        for (var i = 0; i < funList.length; i++) {
            if (this.finishTriggerMap[key + i]) {
                continue;
            }
            var fun = funList[i];
            var ret = fun.func(fun.args);
            if (ret) {
                this.finishTriggerMap[key + i] = true;
            }
        }
    };
    // 初始化敌人
    NoviceEnemyObj.prototype.initEnemy = function () {
        var enemyUID = this.enemyUID;
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        var mainArea = areas[NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX];
        if (!mainArea.owner) {
            GameHelper_1.gameHpr.novice.initEnemy(enemyUID, NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX);
            mainArea.owner = enemyUID;
            mainArea.cityId = Constant_1.CITY_MAIN_NID;
            mainArea.builds = [];
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_MAIN_NID, point: cc.v2(2, 5), lv: 5 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_WALL_NID, point: cc.v2(-1, -1), lv: 1 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_BARRACKS_NID, point: cc.v2(3, 1), lv: 5 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_SMITHY_NID, point: cc.v2(2, 1), lv: 5 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_HOSPITAL_NID, point: cc.v2(4, 1), lv: 5 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_GRANARY_NID, point: cc.v2(2, 3), lv: 3 }, false);
            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: Constant_1.BUILD_WAREHOUSE_NID, point: cc.v2(4, 3), lv: 3 }, false);
            mainArea.armys = [];
            mainArea.updateSize();
            NoviceConfig_1.NOVICE_ENEMY_MAINCITY_OTHER_INDEXS.forEach(function (i) {
                var area = areas[i];
                area.owner = enemyUID;
                area.cityId = -Constant_1.CITY_MAIN_NID;
                GameHelper_1.gameHpr.noviceServer.setEnemyArea(area);
                GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ADD_CELL, area.strip());
            });
            GameHelper_1.gameHpr.noviceServer.setEnemyArea(mainArea);
            GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ADD_CELL, mainArea.strip());
            return true;
        }
        return false;
    };
    // 创建军队
    NoviceEnemyObj.prototype.createEnemyArmy = function (data, attackDir) {
        if (attackDir === void 0) { attackDir = 2; }
        var armyIndex = data.index;
        if (this.armyUIDMap[armyIndex]) {
            return false;
        }
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        var mainArea = areas[data.areaIndex || NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX];
        var armyUID = ut.UID();
        this.armyUIDMap[armyIndex] = armyUID;
        mainArea.addArmy({
            index: mainArea.index,
            uid: armyUID,
            name: assetsMgr.lang('ui.default_army_name') + armyIndex,
            owner: this.enemyUID,
        });
        var army = mainArea.armys[mainArea.armys.length - 1];
        var pawnList = data.pawnList;
        for (var k = 0; k < pawnList.length; k++) {
            var pawn = pawnList[k];
            for (var m = 0; m < pawn.count; m++) {
                var pawnId = pawn.id;
                army.addPawn({
                    index: data.areaIndex || mainArea.index,
                    uid: ut.UID(),
                    id: pawnId,
                    lv: 1,
                    point: this.getPoint(mainArea, attackDir)
                });
            }
        }
        GameHelper_1.gameHpr.noviceServer.updatePlayerArmyDist(mainArea);
        GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ARMY_DIST, mainArea.armys[mainArea.armys.length - 1].strip());
        return true;
    };
    // 增加军队士兵
    NoviceEnemyObj.prototype.addArmyPawns = function (data, attackDir) {
        if (attackDir === void 0) { attackDir = 2; }
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        var armyUID = this.armyUIDMap[data.index];
        for (var i = 0; i < areas.length; i++) {
            var area = areas[i];
            for (var j = 0; j < area.armys.length; j++) {
                var army = area.armys[j];
                if (army.uid === armyUID) {
                    var pawnList = data.pawnList;
                    for (var k = 0; k < pawnList.length; k++) {
                        var pawn = pawnList[k];
                        var pawnId = pawn.id;
                        for (var m = 0; m < pawn.count; m++) {
                            army.addPawn({
                                index: area.index,
                                uid: ut.UID(),
                                id: pawnId,
                                lv: 1,
                                point: this.getPoint(area, attackDir)
                            });
                        }
                    }
                    GameHelper_1.gameHpr.noviceServer.updatePlayerArmyDist(area);
                    GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ARMY_DIST, army.strip());
                    return true;
                }
            }
        }
        return false;
    };
    // 获取方向 0.上 1.右 2.下 3.左
    NoviceEnemyObj.prototype.getPoint = function (area, dir) {
        if (dir === void 0) { dir = 2; }
        var areaPointMap = this.tempAreaPointMap[area.index];
        if (!areaPointMap) {
            areaPointMap = { 0: [], 1: [], 2: [], 3: [] };
            this.tempAreaPointMap[area.index] = areaPointMap;
            var minx = Number.MAX_VALUE, miny = Number.MAX_VALUE, maxx = 0, maxy = 0;
            for (var i = 0; i < area.mainPoints.length; i++) {
                var point = area.mainPoints[i];
                maxx = Math.max(maxx, point.x);
                maxy = Math.max(maxy, point.y);
                minx = Math.min(minx, point.x);
                miny = Math.min(miny, point.y);
            }
            var centerRect = cc.rect(minx, miny, maxx - minx, maxy - miny);
            var centerPoint_1 = cc.v2((minx + maxx) / 2, (miny + maxy) / 2);
            for (var y = 0; y < area.areaSize.y; y++) {
                for (var x = 0; x < area.areaSize.x; x++) {
                    var pos = cc.v2(x, y);
                    if (!centerRect.contains(pos)) {
                        if (pos.x < minx) {
                            areaPointMap[3].push(pos);
                        }
                        if (pos.x > maxx) {
                            areaPointMap[1].push(pos);
                        }
                        if (pos.y < miny) {
                            areaPointMap[2].push(pos);
                        }
                        if (pos.y > maxy) {
                            areaPointMap[0].push(pos);
                        }
                    }
                }
            }
            for (var key in areaPointMap) {
                areaPointMap[key].sort(function (a, b) {
                    return a.sub(centerPoint_1).mag() - b.sub(centerPoint_1).mag();
                });
            }
        }
        var list = areaPointMap[dir];
        var pawnIndex = this.tempAreaPointIndexMap[area.index] || 0;
        this.tempAreaPointIndexMap[area.index] = pawnIndex + 1;
        var pawnPos = list[pawnIndex];
        return pawnPos;
    };
    // 重置军队
    NoviceEnemyObj.prototype.resetArmyPanws = function (data, attackDir) {
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        var armyUID = this.armyUIDMap[data.index];
        for (var i = 0; i < areas.length; i++) {
            var area = areas[i];
            for (var j = 0; j < area.armys.length; j++) {
                var army = area.armys[j];
                if (army.uid === armyUID) {
                    area.armys.splice(j, 1); //删除原来的军队
                    j--;
                    // 通知删除战场中的军队
                    GameHelper_1.gameHpr.noviceServer.notifyArea(area.index, Enums_1.NotifyType.REMOVE_ARMY, army.uid);
                    //重新设置军队
                    var targetArea = GameHelper_1.gameHpr.noviceServer.getArea(data.areaIndex);
                    targetArea.armys.push(army);
                    army.index = targetArea.index;
                    army.pawns.length = 0; //清空原来的士兵
                    army.state = 0;
                    var pawnList = data.pawnList;
                    for (var k = 0; k < pawnList.length; k++) {
                        var pawn = pawnList[k];
                        var pawnId = pawn.id;
                        for (var m = 0; m < pawn.count; m++) {
                            army.addPawn({
                                index: targetArea.index,
                                uid: ut.UID(),
                                id: pawnId,
                                lv: 1,
                                point: this.getPoint(targetArea, attackDir)
                            });
                        }
                    }
                    GameHelper_1.gameHpr.noviceServer.notifyArea(targetArea.index, Enums_1.NotifyType.ADD_ARMY, army.strip());
                    return true;
                }
            }
        }
        delete this.armyUIDMap[data.index]; //删除原来的军队下标
        this.createEnemyArmy(data, attackDir);
    };
    // 军队士兵穿戴装备
    NoviceEnemyObj.prototype.equipArmyPawns = function (data) {
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        for (var i = 0; i < areas.length; i++) {
            var area = areas[i];
            for (var j = 0; j < area.armys.length; j++) {
                var army = area.armys[j];
                if (army.owner === this.enemyUID) {
                    for (var k = 0; k < army.pawns.length; k++) {
                        var pawn = army.pawns[k];
                        if (pawn.type === data.type) {
                            pawn.changeEquip(this.getEquipWithID(data.id), false);
                        }
                    }
                    GameHelper_1.gameHpr.noviceServer.updatePlayerArmyDist(area);
                    GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ARMY_DIST, army.strip());
                }
            }
        }
        return true;
    };
    // 生成装备
    NoviceEnemyObj.prototype.getEquipWithID = function (id) {
        var equipInfo = this.equipMap[id];
        if (!equipInfo) {
            var jsonData = assetsMgr.getJson('equipBase').dataIdMap;
            var info = jsonData[id];
            var list = info.need_build_lv.split(',');
            var lv = list[0];
            var uid = id + '_' + lv;
            equipInfo = new EquipInfo_1.default().init(uid, id, []);
            GameHelper_1.gameHpr.noviceServer.randomEquipAttr(equipInfo);
            this.equipMap[id] = equipInfo;
        }
        return equipInfo;
    };
    // 生成进攻军队
    NoviceEnemyObj.prototype.occupyCell = function (data) {
        var e_1, _a;
        var armyMap = {};
        var armyList = data.armyList;
        for (var i = 0; i < armyList.length; i++) {
            var armyUID = this.armyUIDMap[armyList[i]];
            if (armyUID) {
                armyMap[armyUID] = true;
            }
        }
        if (Object.keys(armyMap).length > 0) {
            var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
            if (areas[data.index].owner === this.enemyUID) {
                return false;
            }
            var occupyList = [];
            for (var i = 0; i < areas.length; i++) {
                var area = areas[i];
                if (area.owner == this.enemyUID && area.armys.length > 0) {
                    var recoverList = [];
                    try {
                        for (var _b = (e_1 = void 0, __values(area.armys)), _c = _b.next(); !_c.done; _c = _b.next()) {
                            var army = _c.value;
                            if (armyMap[army.uid] && army.state === Enums_1.ArmyState.NONE) {
                                army.recoverAllPawn(); //恢复军队血量
                                recoverList.push(army.strip());
                                occupyList.push(army.strip());
                            }
                        }
                    }
                    catch (e_1_1) { e_1 = { error: e_1_1 }; }
                    finally {
                        try {
                            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                        }
                        finally { if (e_1) throw e_1.error; }
                    }
                    if (recoverList.length > 0) {
                        GameHelper_1.gameHpr.noviceServer.notifyArea(area.index, Enums_1.NotifyType.UPDATE_ALL_PAWN_HP, recoverList);
                    }
                }
            }
            if (occupyList.length > 0) {
                GameHelper_1.gameHpr.novice.occupyCell(occupyList, data.index, 0, false, false);
                return true;
            }
        }
        return false;
    };
    // 移动军队
    NoviceEnemyObj.prototype.moveCellArmy = function (data) {
        var e_2, _a;
        var armyMap = {};
        var armyList = data.armyList;
        for (var i = 0; i < armyList.length; i++) {
            var armyUID = this.armyUIDMap[armyList[i]];
            if (armyUID) {
                armyMap[armyUID] = true;
            }
        }
        if (Object.keys(armyMap).length > 0) {
            var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
            var moveList = [];
            for (var i = 0; i < areas.length; i++) {
                var area = areas[i];
                if (area.owner == this.enemyUID && area.armys.length > 0) {
                    try {
                        for (var _b = (e_2 = void 0, __values(area.armys)), _c = _b.next(); !_c.done; _c = _b.next()) {
                            var army = _c.value;
                            if (armyMap[army.uid] && army.state === Enums_1.ArmyState.NONE) {
                                moveList.push(army.strip());
                            }
                        }
                    }
                    catch (e_2_1) { e_2 = { error: e_2_1 }; }
                    finally {
                        try {
                            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                        }
                        finally { if (e_2) throw e_2.error; }
                    }
                }
            }
            if (moveList.length > 0) {
                GameHelper_1.gameHpr.novice.moveCellArmy(moveList, data.index, false, false);
                return true;
            }
        }
        return false;
    };
    //设置军队士兵血量，按比例设置
    NoviceEnemyObj.prototype.setArmyPawnsHp = function (data) {
        var e_3, _a;
        var armyMap = {};
        var armyList = data.armyList;
        for (var i = 0; i < armyList.length; i++) {
            var armyUID = this.armyUIDMap[armyList[i]];
            if (armyUID) {
                armyMap[armyUID] = true;
            }
        }
        if (Object.keys(armyMap).length > 0) {
            var isSetHp = false;
            var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
            for (var i = 0; i < areas.length; i++) {
                var area = areas[i];
                if (area.owner == this.enemyUID && area.armys.length > 0 && !area.isRecoverPawnHP()) {
                    var armyList_1 = [];
                    try {
                        for (var _b = (e_3 = void 0, __values(area.armys)), _c = _b.next(); !_c.done; _c = _b.next()) {
                            var army = _c.value;
                            if (armyMap[army.uid] && army.state === Enums_1.ArmyState.NONE) {
                                army.pawns.forEach(function (m) {
                                    var percent = Math.random() * (data.max - data.min) + data.min;
                                    m.curHp = Math.floor(m.maxHp * percent);
                                });
                                isSetHp = true;
                                armyList_1.push(army.strip());
                            }
                        }
                    }
                    catch (e_3_1) { e_3 = { error: e_3_1 }; }
                    finally {
                        try {
                            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                        }
                        finally { if (e_3) throw e_3.error; }
                    }
                    if (armyList_1.length > 0) {
                        GameHelper_1.gameHpr.noviceServer.notifyArea(area.index, Enums_1.NotifyType.UPDATE_ALL_PAWN_HP, armyList_1);
                    }
                }
            }
            return isSetHp;
        }
        return false;
    };
    //首次攻击玩家
    NoviceEnemyObj.prototype.firstAttackPlayer = function (enemyPos, playerPos) {
        var marchTargetMap = {};
        var marchs = GameHelper_1.gameHpr.world.getMarchs();
        for (var i = 0; i < marchs.length; i++) {
            var march = marchs[i];
            if (march.owner === GameHelper_1.gameHpr.getUid()) {
                marchTargetMap[march.targetIndex] = true;
            }
        }
        var enemyCell = GameHelper_1.gameHpr.world.getMapCellByPoint(enemyPos);
        var areas = GameHelper_1.gameHpr.noviceServer.getAreas();
        for (var y = enemyPos.y; y <= playerPos.y; y++) {
            for (var x = enemyPos.x; x <= playerPos.x; x++) {
                if (y === playerPos.y || x === enemyPos.x) {
                    var index = y * NoviceConfig_1.NOVICE_MAP_SIZE.x + x;
                    var area = areas[index];
                    if (!area.owner && !marchTargetMap[index] && !(area.armys.length > 0 && area.armys[0].state === Enums_1.ArmyState.FIGHT)) {
                        area.armys.length = 0;
                        area.owner = this.enemyUID;
                        area.updateCity(0, true);
                        GameHelper_1.gameHpr.noviceServer.setEnemyArea(area);
                        GameHelper_1.gameHpr.noviceServer.notifyWorld(Enums_1.NotifyType.ADD_CELL, { index: index, owner: this.getEnemyUID() }); //通知
                        enemyCell = GameHelper_1.gameHpr.world.getMapCellByIndex(area.index);
                    }
                }
            }
        }
        var targetCell = GameHelper_1.gameHpr.world.getMapCellByPoint(playerPos);
        if (targetCell.owner === GameHelper_1.gameHpr.getUid()) {
            var dir = MapHelper_1.mapHelper.getDirByIndex(targetCell.index, enemyCell.index, NoviceConfig_1.NOVICE_MAP_SIZE);
            this.resetArmyPanws({ index: 2, areaIndex: enemyCell.index, pawnList: [{ id: ARCHER_ID, count: 1 }] }, dir);
            this.occupyCell({ index: targetCell.index, armyList: [2] });
            this.finishTriggerMap[this.firstAttackKey] = targetCell.index;
        }
    };
    //生成军队到area，当前被攻击的area有军队先移动回主城
    NoviceEnemyObj.prototype.createEnemyArmyToArea = function (area, attackCount, attackDir) {
        if (area.index === NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX) {
            this.resetArmyPanws({ index: 1, areaIndex: area.index, pawnList: [{ id: ARCHER_ID, count: 5 }] }, attackDir);
            this.resetArmyPanws({ index: 2, areaIndex: area.index, pawnList: [{ id: PELTAST_ID, count: 2 }] }, attackDir);
            this.resetArmyPanws({ index: 3, areaIndex: area.index, pawnList: [{ id: PELTAST_ID, count: 2 }] }, attackDir);
        }
        else if (area.cityId === Constant_1.CITY_FORT_NID) {
            this.resetArmyPanws({ index: 3, areaIndex: area.index, pawnList: [{ id: PELTAST_ID, count: 3 }] }, attackDir);
            this.supportMap[area.index] = Date.now() + this.supportDelayTime;
        }
        else {
            if (1 === attackCount) {
                this.resetArmyPanws({ index: 1, areaIndex: area.index, pawnList: [{ id: PELTAST_ID, count: 1 }] }, attackDir);
                this.setArmyPawnsHp({ armyList: [1], min: 0.8, max: 0.9 });
            }
            else if (3 === attackCount) {
                this.resetArmyPanws({ index: 2, areaIndex: area.index, pawnList: [{ id: ARCHER_ID, count: 1 }, { id: PELTAST_ID, count: 1 }] }, attackDir);
                this.setArmyPawnsHp({ armyList: [2], min: 0.9, max: 1 });
            }
        }
        this.equipArmyPawns({ type: 2, id: PELTAST_EQUIP_ID });
        this.equipArmyPawns({ type: 3, id: ARCHER_EQUIP_ID });
    };
    // 更新支援
    NoviceEnemyObj.prototype.updateSupportArmy = function () {
        if (GameHelper_1.gameHpr.noviceServer.getArea(NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX).owner === this.getEnemyUID()) {
            var nowTime = Date.now();
            for (var key in this.supportMap) {
                var supportTime = this.supportMap[key];
                if (nowTime >= supportTime) {
                    delete this.supportMap[key];
                    var areaIndex = Number(key);
                    this.resetArmyPanws({ index: 5, areaIndex: NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX, pawnList: [{ id: ARCHER_ID, count: 2 }] }, 2);
                    this.moveCellArmy({ index: areaIndex, armyList: [5] });
                }
            }
        }
    };
    return NoviceEnemyObj;
}());
exports.default = NoviceEnemyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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