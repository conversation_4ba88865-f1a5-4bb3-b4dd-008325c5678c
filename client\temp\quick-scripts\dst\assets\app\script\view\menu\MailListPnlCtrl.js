
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/MailListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '68e8b+3xppL9pvROLmEBpw4', 'MailListPnlCtrl');
// app/script/view/menu/MailListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MailListPnlCtrl = /** @class */ (function (_super) {
    __extends(MailListPnlCtrl, _super);
    function MailListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.ttileLbl_ = null; // path://root/title/ttile_l
        _this.listSv_ = null; // path://root/list_sv
        _this.loadingNode_ = null; // path://root/loading_n
        _this.delReadBtn_ = null; // path://root/buttons/del_read_be_b
        _this.writeNode_ = null; // path://root/buttons/write_be_n
        //@end
        _this.model = null;
        return _this;
    }
    MailListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.REMOVE_MAIL] = this.onRemoveMail, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_MAIL_STATE] = this.onUpdateMailState, _b.enter = true, _b),
        ];
    };
    MailListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('user');
                this.ttileLbl_.setLocaleKey('ui.title_mail_list', 0);
                return [2 /*return*/];
            });
        });
    };
    MailListPnlCtrl.prototype.onEnter = function () {
        var _this = this;
        this.listSv_.content.Swih('');
        this.loadingNode_.active = true;
        this.delReadBtn_.interactable = false;
        this.listSv_.Child('empty').active = false;
        this.model.getMails().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            ReddotHelper_1.reddotHelper.set('new_mail', false);
            _this.loadingNode_.active = false;
            _this.updateList(list);
        });
        this.writeNode_.active = GameHelper_1.gameHpr.alliance.isCanSendMail();
    };
    MailListPnlCtrl.prototype.onRemove = function () {
    };
    MailListPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    MailListPnlCtrl.prototype.onClickItem = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('menu/MailInfo', data);
        }
    };
    // path://root/buttons/write_be_n
    MailListPnlCtrl.prototype.onClickWrite = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/WriteMail');
    };
    // path://root/buttons/del_read_be_b
    MailListPnlCtrl.prototype.onClickDelRead = function (event, data) {
        var _this = this;
        this.model.delAllReadMail().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateList(_this.model.getTempMails());
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 删除邮件
    MailListPnlCtrl.prototype.onRemoveMail = function (uid) {
        this.updateList(this.model.getTempMails());
    };
    // 刷新邮件状态
    MailListPnlCtrl.prototype.onUpdateMailState = function (data) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateState(it, data);
        }
        if (data.state === Enums_1.MailStateType.READ) {
            this.delReadBtn_.interactable = true;
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    MailListPnlCtrl.prototype.updateList = function (mails) {
        var list = mails.sort(function (a, b) { return a.state === b.state ? b.createTime - a.createTime : a.state - b.state; }), len = list.length;
        this.delReadBtn_.interactable = mails.some(function (m) { return m.state === Enums_1.MailStateType.READ; });
        this.ttileLbl_.setLocaleKey('ui.title_mail_list', len);
        this.listSv_.Child('empty').active = len === 0;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.List(len, function (it, i) {
            var data = it.Data = list[i];
            var isReaded = data.state === Enums_1.MailStateType.READ; // 已读状态全部换色 
            it.Child('state').Color(Constant_1.MAIL_STATE_COLOR[data.state]).setLocaleKey('ui.mail_state_' + data.state);
            it.Child('title', cc.Label).Color(isReaded ? '#A18876' : '#3F332F').string = data.title;
            it.Child('sender/name').Color(isReaded ? '#A18876' : '#625450');
            var isSys = data.sender === '-1';
            it.Child('sender/val', cc.Label).Color(isReaded ? '#A18876' : isSys ? '#BE772B' : '#936E5A').string = isSys ? assetsMgr.lang('ui.system') : ut.nameFormator(data.senderName, 8);
            it.Child('time', cc.Label).Color(isReaded ? '#A18876' : '#625450').string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.createTime);
        });
    };
    MailListPnlCtrl.prototype.updateState = function (it, mailInfo) {
        var state = mailInfo.state, isSys = mailInfo.sender === '-1';
        it.Child('state').Color(Constant_1.MAIL_STATE_COLOR[state]).setLocaleKey('ui.mail_state_' + state);
        var isReaded = state === Enums_1.MailStateType.READ;
        it.Child('title', cc.Label).Color(isReaded ? '#A18876' : '#3F332F');
        it.Child('sender/name').Color(isReaded ? '#A18876' : '#625450');
        it.Child('sender/val', cc.Label).Color(isReaded ? '#A18876' : isSys ? '#BE772B' : '#936E5A');
        it.Child('time', cc.Label).Color(isReaded ? '#A18876' : '#625450');
    };
    MailListPnlCtrl = __decorate([
        ccclass
    ], MailListPnlCtrl);
    return MailListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MailListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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