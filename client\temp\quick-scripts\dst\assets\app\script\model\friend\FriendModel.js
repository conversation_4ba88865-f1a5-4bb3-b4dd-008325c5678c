
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/friend/FriendModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6ee1enACU9Ewo6Y00tZvKqV', 'FriendModel');
// app/script/model/friend/FriendModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var FriendInfo_1 = require("./FriendInfo");
/**
 * 好友模块
 */
var FriendModel = /** @class */ (function (_super) {
    __extends(FriendModel, _super);
    function FriendModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.friends = [];
        _this.applys = []; //申请列表
        _this.blacklists = []; //黑名单信息
        _this.friendChatMap = new Map();
        _this.currLookChatFriendUID = ''; //当前正在查看的好友聊天
        _this.lastSendChatTime = 0; //上次发送时间
        _this.tolerateCount = 0;
        _this.restStartTime = 0; //休息时间
        return _this;
    }
    FriendModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    FriendModel.prototype.init = function (friendsList, friendsApplys, blacklists) {
        this.friends = friendsList.map(function (m) { return new FriendInfo_1.default().init(m); });
        this.applys = friendsApplys;
        this.blacklists = blacklists;
        this.friendChatMap.clear();
        this.lastSendChatTime = this.lastSendChatTime || Date.now();
        // 监听事件
        this.net.on('lobby/OnFriendUpdate', this.OnFriendUpdate, this); //好友信息更新
        this.net.on('lobby/OnFriendApply', this.OnFriendApply, this); //好友申请通知
        this.net.on('lobby/OnFriendAdd', this.OnFriendAdd, this); //好友添加通知
        this.net.on('lobby/OnFriendChat', this.OnFriendChat, this); //好友聊天通知
        this.net.on('lobby/OnFriendDel', this.OnFriendDel, this); //删除好友通知
        this.net.on('lobby/OnFriendSendGift', this.OnFriendSendGift, this); //好友赠送通知
        // 红点
        this.updateReddot();
        ReddotHelper_1.reddotHelper.unregister('new_friend_apply');
        ReddotHelper_1.reddotHelper.register('new_friend_apply', this.checkHasFriendApply, this, 30, 29);
    };
    FriendModel.prototype.getFriends = function () { return this.friends; };
    FriendModel.prototype.getApplys = function () { return this.applys; };
    FriendModel.prototype.getBlacklists = function () { return this.blacklists; };
    FriendModel.prototype.updateReddot = function () {
        var chat = false, gift = false;
        this.friends.forEach(function (m) {
            if (m.notReadCount > 0) {
                chat = true;
            }
            if (m.giftList.length > 0) {
                gift = true;
            }
        });
        ReddotHelper_1.reddotHelper.set('friend_chat', chat);
        ReddotHelper_1.reddotHelper.set('friend_gift', gift);
    };
    // 是否好友
    FriendModel.prototype.isFriend = function (uid) {
        return this.friends.some(function (m) { return m.uid === uid; });
    };
    // 是否在黑名单
    FriendModel.prototype.isInBlacklist = function (uid) {
        return this.blacklists.some(function (m) { return m.uid === uid; });
    };
    // 检测是否 有好友申请
    FriendModel.prototype.checkHasFriendApply = function (val) {
        return this.applys.length > 0;
    };
    FriendModel.prototype.getFriendByUID = function (uid) {
        return this.friends.find(function (m) { return m.uid === uid; });
    };
    // 刷新好友列表
    FriendModel.prototype.updateFriends = function (friendsList) {
        this.friends = friendsList.map(function (m) { return new FriendInfo_1.default().init(m); });
        this.emit(EventType_1.default.UPDATE_FRIEND_LIST);
        this.updateReddot();
    };
    // 刷新申请列表
    FriendModel.prototype.updateApplys = function (friendsApplys) {
        this.applys = friendsApplys;
        this.emit(EventType_1.default.UPDATE_FRIEND_APPLYS);
        ReddotHelper_1.reddotHelper.set('new_friend_apply', this.applys.length > 0);
    };
    // 刷新黑名单
    FriendModel.prototype.updateBlacklist = function (blacklists) {
        this.blacklists = blacklists;
        this.emit(EventType_1.default.UPDATE_BLACKLISTS);
    };
    // 发起申请
    FriendModel.prototype.applyFriend = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.getUid() === uid) {
                            return [2 /*return*/, ECode_1.ecode.PLAYER_NOT_EXIST];
                        }
                        else if (GameHelper_1.gameHpr.isGuest()) {
                            return [2 /*return*/, ECode_1.ecode.FRIEND_NOT_GUEST];
                        } /* else if (gameHpr.getMaxLandCountByPChat() < FRIENDS_MIN_LAND_COUNT) {
                            return ecode.FRIEND_APPLY_LAND_LIMIT
                        } */
                        return [4 /*yield*/, this.net.request('lobby/HD_ApplyFriend', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 黑名单操作 false.添加 true.删除
    FriendModel.prototype.doBlacklist = function (uid, state) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_Blacklist', { uid: uid, state: state }, true)];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (!err) {
                            this.updateBlacklist(data.blacklists || []);
                            // 删除私聊 如果有
                            GameHelper_1.gameHpr.chat.removePChatChannel(data.channel, uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 删除好友
    FriendModel.prototype.removeFriend = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_DelFriend', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.OnFriendDel({ uid: uid });
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 修改好友备注
    FriendModel.prototype.modifyFriendNote = function (uid, noteName) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, friend;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_SetFriendNoteName', { uid: uid, noteName: noteName }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            friend = this.friends.find(function (m) { return m.uid === uid; });
                            if (friend) {
                                friend.noteName = noteName;
                                this.emit(EventType_1.default.UPDATE_FRIEND_INFO, friend);
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测初始化好友的聊天信息
    FriendModel.prototype.checkInitFriendChats = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!!this.friendChatMap.has(uid)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.net.request('lobby/HD_GetFriendChats', { uid: uid, start: 0, count: 50 })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.friendChatMap.set(uid, (data === null || data === void 0 ? void 0 : data.chats) || []);
                        _b.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 获取好友的聊天信息
    FriendModel.prototype.getChatsByFriend = function (uid) {
        return this.friendChatMap.get(uid) || [];
    };
    // 发送聊天
    FriendModel.prototype.sendChat = function (friendUid, content, param) {
        if (param === void 0) { param = {}; }
        if (this.checkOftenTime()) {
            ViewHelper_1.viewHelper.showAlert('toast.send_chat_tolerate');
            return 1;
        }
        var now = this.lastSendChatTime = Date.now();
        var list = this.getChatsByFriend(friendUid);
        var uid = ut.UID();
        // 先发送到本地
        this.addChat(list, {
            uid: uid,
            sender: GameHelper_1.gameHpr.getUid(),
            content: content,
            emoji: param.emoji || 0,
            time: now,
            wait: true,
        });
        GameHelper_1.gameHpr.updateChatAllTime(list);
        // 发送到服务器
        this.net.request('lobby/HD_FriendChat', {
            uid: uid, friendUid: friendUid, content: content,
            emoji: param.emoji
        });
        return 0;
    };
    FriendModel.prototype.addChat = function (list, data) {
        list.unshift(data);
        if (list.length > Constant_1.CHAT_MAX_COUNT) {
            list.pop();
        }
        return data;
    };
    FriendModel.prototype.getChatInfoByUID = function (uid) {
        var e_1, _a;
        try {
            for (var _b = __values(this.friendChatMap), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), k = _d[0], v = _d[1];
                var chat = v.find(function (m) { return m.uid === uid; });
                if (chat) {
                    return chat;
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return null;
    };
    // 检测是否发送得太频繁了
    FriendModel.prototype.checkOftenTime = function () {
        var now = Date.now();
        if (now - this.restStartTime < Constant_1.CHAT_REST_MAX_TIME) {
            return true;
        }
        else if (now - this.lastSendChatTime < Constant_1.CHAT_SEND_INTERVAL) {
            this.tolerateCount += 1;
        }
        else {
            this.tolerateCount = 0;
        }
        // cc.log(now - this.lastSendChatTime, this.tolerateCount)
        if (this.tolerateCount > Constant_1.CHAT_TOLERATE_MAX_COUNT) {
            this.restStartTime = now;
            return true;
        }
        this.restStartTime = 0;
        return false;
    };
    // 检测休息剩余时间
    FriendModel.prototype.checkRestSurplusTime = function () {
        if (this.restStartTime <= 0) {
            return 0;
        }
        var time = Date.now() - this.restStartTime;
        if (time >= Constant_1.CHAT_REST_MAX_TIME) {
            this.restStartTime = 0;
            return 0;
        }
        return Constant_1.CHAT_REST_MAX_TIME - time;
    };
    // 标记已读
    FriendModel.prototype.tagChatRead = function (data) {
        if (data) {
            this.net.send('lobby/HD_FriendChatRead', { uid: data.uid });
            data.notReadCount = 0;
            ReddotHelper_1.reddotHelper.set('friend_chat', this.friends.some(function (m) { return m.notReadCount > 0; }));
            this.emit(EventType_1.default.UPDATE_FRIEND_INFO, data);
        }
    };
    FriendModel.prototype.setCurrLookChatFriendUID = function (uid) {
        this.currLookChatFriendUID = uid;
    };
    FriendModel.prototype.getCurrLookChatFriendUID = function () {
        return this.currLookChatFriendUID;
    };
    // 领取礼物
    FriendModel.prototype.claimFriendGift = function (friendUid, uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, friend;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_ReceiveFriendGift', { friendUid: friendUid, uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                            friend = this.friends.find(function (m) { return m.uid === friendUid; });
                            if (friend) {
                                friend.giftList.remove('uid', uid);
                                ReddotHelper_1.reddotHelper.set('friend_gift', this.friends.some(function (m) { return m.giftList.length > 0; }));
                                this.emit(EventType_1.default.UPDATE_FRIEND_INFO, friend);
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 好友信息更新
    FriendModel.prototype.OnFriendUpdate = function (data) {
        var _a, _b;
        var friend = this.friends.find(function (m) { return m.uid === data.uid; });
        if (friend) {
            var offlineTime = friend.offlineTime;
            friend.nickname = (_a = data.nickname) !== null && _a !== void 0 ? _a : friend.nickname;
            friend.headIcon = (_b = data.headIcon) !== null && _b !== void 0 ? _b : friend.headIcon;
            friend.offlineTime = data.offlineTime || 0;
            friend.playSid = data.playSid || 0;
            friend.getTime = Date.now();
            friend.lastChatInfo = data.lastChatInfo;
            // if (offlineTime !== friend.offlineTime) {
            //     this.emit(EventType.UPDATE_FRIEND_LIST) //如果是状态改变 就刷新列表 因为要排序
            // } else {
            this.emit(EventType_1.default.UPDATE_FRIEND_INFO, friend);
            // }
        }
    };
    // 好友申请通知
    FriendModel.prototype.OnFriendApply = function (data) {
        var index = this.applys.findIndex(function (m) { return m.uid === data.uid; });
        if (!data.time) {
            if (index === -1) {
                return;
            }
            this.applys.splice(index, 1);
        }
        else if (index === -1) {
            this.applys.push(data);
            GameHelper_1.gameHpr.addMessage({ key: 'ui.message_108', tag: 'new_friend_apply' });
        }
        this.emit(EventType_1.default.UPDATE_FRIEND_APPLYS);
        var has = this.applys.length > 0;
        ReddotHelper_1.reddotHelper.set('new_friend_apply', has);
    };
    // 好友添加通知
    FriendModel.prototype.OnFriendAdd = function (data) {
        if (!this.friends.some(function (m) { return m.uid === data.uid; })) {
            this.friends.push(new FriendInfo_1.default().init(data));
            this.emit(EventType_1.default.UPDATE_FRIEND_LIST);
        }
    };
    // 好友聊天通知
    FriendModel.prototype.OnFriendChat = function (notify) {
        var uid = notify.uid, list = this.friendChatMap.get(uid), data = notify.chatInfo;
        do {
            if (data.sender === GameHelper_1.gameHpr.getUid()) {
                var chat = list === null || list === void 0 ? void 0 : list.find(function (m) { return m.uid === data.uid; });
                if (chat) {
                    chat.content = data.content;
                    chat.wait = false;
                    data = chat;
                    break;
                }
            }
            else {
                var friend = this.friends.find(function (m) { return m.uid === uid; });
                if (!friend) {
                }
                else if (this.currLookChatFriendUID === uid) {
                    this.tagChatRead(friend); //标记已读
                }
                else {
                    friend.notReadCount += 1;
                    friend.lastChatInfo = data;
                    ReddotHelper_1.reddotHelper.set('friend_chat', true);
                    this.emit(EventType_1.default.UPDATE_FRIEND_INFO, friend);
                }
            }
            if (list) {
                this.addChat(list, data);
            }
        } while (false);
        if (list) {
            GameHelper_1.gameHpr.updateChatAllTime(list);
            this.emit(EventType_1.default.ADD_FRIEND_CHAT, { uid: uid, chat: data });
        }
    };
    // 删除好友通知
    FriendModel.prototype.OnFriendDel = function (data) {
        if (this.friends.remove('uid', data.uid)) {
            this.emit(EventType_1.default.UPDATE_FRIEND_LIST);
            this.updateReddot();
            if (this.currLookChatFriendUID === data.uid) {
                ViewHelper_1.viewHelper.hidePnl('common/Chat');
            }
        }
    };
    // 好友赠送通知
    FriendModel.prototype.OnFriendSendGift = function (data) {
        var friend = this.friends.find(function (m) { return m.uid === data.friendUid; });
        if (friend) {
            friend.giftList.push(data);
            ReddotHelper_1.reddotHelper.set('friend_gift', true);
            this.emit(EventType_1.default.UPDATE_FRIEND_INFO, friend);
        }
    };
    FriendModel = __decorate([
        mc.addmodel('friend')
    ], FriendModel);
    return FriendModel;
}(mc.BaseModel));
exports.default = FriendModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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