
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildWallPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7927enbb8dJHKSWX08f4Nui', 'BuildWallPnlCtrl');
// app/script/view/build/BuildWallPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BuildWallPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildWallPnlCtrl, _super);
    function BuildWallPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.data = null;
        return _this;
    }
    BuildWallPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a)
        ];
    };
    BuildWallPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildWallPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        ViewHelper_1.viewHelper._updateBuildBaseInfo(this.rootNode_.Child('info/top'), data, this.key);
        this.updateBuildAttrInfo();
    };
    BuildWallPnlCtrl.prototype.onRemove = function () {
    };
    BuildWallPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/bottom/buttons/up_be
    BuildWallPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildWallPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            this.rootNode_.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            this.updateBuildAttrInfo();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildWallPnlCtrl.prototype.updateBuildAttrInfo = function () {
        var data = this.data, isMaxLv = data.isMaxLv();
        var attr = this.rootNode_.Child('info/attrs');
        var top = attr.Child('top');
        // 显示下级信息和升级费用
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        if (data.nextLvInfo) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', data.nextLvInfo.lv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex);
        var _a = cell.getPawnInfo() || { id: 7003, lv: 1 }, id = _a.id, lv = _a.lv;
        var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv), nextJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + (data.lv + 1)), json = cell === null || cell === void 0 ? void 0 : cell.getPawnAttrJson();
        var items = attr.Child('items');
        items.children[0].active = false;
        for (var i = items.childrenCount - 1; i > 0; i--) {
            items.removeChild(items.children[i]);
        }
        // 耐久
        var hp = (cell === null || cell === void 0 ? void 0 : cell.getHpInfo()) || [0, 0], jsonHp = (attrJson === null || attrJson === void 0 ? void 0 : attrJson.hp) || 0, maxHp = hp[1], hpAdd = Math.max(0, maxHp - jsonHp);
        items.AddItem(function (it, i) {
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey('ui.build_eff_desc_35_1', ['buildText.name_2000', hp.join('/')]);
            it.Child('curr/add').setLocaleKey(hpAdd > 0 ? assetsMgr.lang('ui.bracket', jsonHp + "+<color=#49983C>" + hpAdd + "</c>") : '');
            if (it.Child('next').active = !isMaxLv) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextJson.hp + hpAdd;
            }
            it.Child('line').active = true;
        });
        // 攻击
        var attackAdd = Math.max(0, json.attack - attrJson.attack);
        items.AddItem(function (it, i) {
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey('ui.build_eff_desc_35_2', ['buildText.name_2000', json.attack]);
            it.Child('curr/add').setLocaleKey(attackAdd > 0 ? assetsMgr.lang('ui.bracket', attrJson.attack + "+<color=#49983C>" + attackAdd + "</c>") : '');
            if (it.Child('next').active = !isMaxLv) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextJson.attack + attackAdd;
            }
            it.Child('line').active = true;
        });
        // 范围
        items.AddItem(function (it, i) {
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey('ui.build_range', json.attack_range);
            it.Child('curr/add').active = false;
            if (it.Child('next').active = !isMaxLv) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = '';
            }
            it.Child('line').active = false;
        });
        // 刷新费用和按钮
        var bottom = this.rootNode_.Child('bottom');
        if (bottom.active = !isMaxLv) {
            var params = [];
            var condText = GameHelper_1.gameHpr.checkUnlcokBuildCond(data.attrJson.prep_cond);
            if (!condText) { // 只要不是主城 就不能比主城等级高
                var limit = data.lv >= GameHelper_1.gameHpr.player.getMainBuildLv();
                params = limit ? [data.lv + 1] : [];
                condText = limit ? 'ui.need_main_lv' : '';
            }
            if (!condText) {
                bottom.Child('title/val').setLocaleKey('ui.up_cost');
                bottom.Child('cond').active = false;
                var need = bottom.Child('need');
                need.active = true;
                ViewHelper_1.viewHelper.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
            }
            else {
                bottom.Child('title/val').setLocaleKey('ui.up_cond');
                bottom.Child('need').active = false;
                var cond = bottom.Child('cond');
                cond.active = true;
                cond.Child('val').setLocaleKey(condText, params);
            }
            ViewHelper_1.viewHelper.updateBuildButtons(bottom.Child('buttons'), data, condText);
        }
    };
    BuildWallPnlCtrl = __decorate([
        ccclass
    ], BuildWallPnlCtrl);
    return BuildWallPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildWallPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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