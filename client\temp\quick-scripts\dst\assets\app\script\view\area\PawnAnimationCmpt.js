
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/PawnAnimationCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7113ainvRVOKrZhngY7CrXJ', 'PawnAnimationCmpt');
// app/script/view/area/PawnAnimationCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var FrameAnimConf_1 = require("../../common/constant/FrameAnimConf");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnAnimConf_1 = require("./PawnAnimConf");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 士兵的动画组件
var PawnAnimationCmpt = /** @class */ (function (_super) {
    __extends(PawnAnimationCmpt, _super);
    function PawnAnimationCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sfs = [];
        _this.sprite = null; //动画精灵
        _this.frames = {};
        _this.currFrameIndex = 0; //当前帧
        _this.frameNamePrefix = '';
        _this.loading = false;
        _this.conf = null;
        _this.id = 0;
        _this.model = null;
        _this.anim = null; //当前播放的动画配置
        _this.playInterval = 0;
        _this.playElapsed = 0;
        _this.playFrameIndex = 0;
        _this.playCallback = null; //播放回调
        _this.delayPlayAnim = 0; //延迟播放
        _this.moveCurrTime = 0;
        _this.moveNeedTime = 0; //移动需要的时间
        _this.moveStartPos = cc.v2(); //移动开始位置
        _this.moveTargetPos = cc.v2(); //移动目标位置
        _this.moveCallback = null; //移动回调
        _this.delayMoveTime = 0; //延迟移动
        _this.moveParabolaHeight = 0; //抛物线高度
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        return _this;
    }
    PawnAnimationCmpt.prototype.init = function (spr, id, key) {
        var _this = this;
        this.id = id;
        this.sprite = spr;
        var isBuySkin = key === 'menu/CollectionSkinInfo';
        if (isBuySkin) {
            this.conf = FrameAnimConf_1.SHOP_PAWN_SKIN_ANIM_CONF[id];
        }
        else {
            this.conf = PawnAnimConf_1.PAWN_ANIM_CONF[PawnAnimConf_1.PAWN_ANIM_CONF_TRANSITION[id] || id];
        }
        if (!this.conf) {
            cc.error('PawnAnimationCmpt init error. id: ' + id);
        }
        this.frameNamePrefix = this.conf.type + '_' + id + '_';
        var frames = this.getAnimConfAllFrames();
        if (frames.length > 0 && this.sfs.length === 0) {
            this.loadFrames(frames, key);
        }
        else {
            this.frames = {};
            this.sfs.forEach(function (m) { return _this.frames[m.name] = m; });
        }
        this.currFrameIndex = 0;
        if (!isBuySkin) {
            this.initModel();
        }
        return this;
    };
    PawnAnimationCmpt.prototype.initModel = function () {
        var _a;
        this.model = GameHelper_1.gameHpr.areaCenter.getLookArea();
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.addPawnAnimation(this);
    };
    PawnAnimationCmpt.prototype.clean = function () {
        var _a;
        this.frames = {};
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.removePawnAnimation(this.uuid);
        this.model = null;
        this.anim = null;
        this.playCallback = null;
        this.moveCallback = null;
        this.currFrameIndex = 0;
        this.delayPlayAnim = 0;
        this.delayMoveTime = 0;
    };
    PawnAnimationCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    // 获取所有动画需要的帧
    PawnAnimationCmpt.prototype.getAnimConfAllFrames = function () {
        var frames = [], obj = {};
        this.conf.anims.forEach(function (m) { return m.frameIndexs.forEach(function (frame) {
            if (frame !== '00' && !obj[frame]) {
                obj[frame] = true;
                frames.push(frame);
            }
        }); });
        return frames;
    };
    // 加载所有帧
    PawnAnimationCmpt.prototype.loadFrames = function (frames, key) {
        return __awaiter(this, void 0, void 0, function () {
            var url, sfs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.loading) {
                            return [2 /*return*/];
                        }
                        this.loading = true;
                        url = this.conf.type + '/' + this.id + '/' + this.frameNamePrefix;
                        return [4 /*yield*/, Promise.all(frames.map(function (m) { return assetsMgr.loadTempRes(url + m, cc.SpriteFrame, key); }))];
                    case 1:
                        sfs = _a.sent();
                        this.frames = {};
                        sfs.forEach(function (m, i) {
                            if (m) {
                                _this.frames[m.name] = m;
                            }
                            else {
                                cc.error(frames[i]);
                            }
                        });
                        this.loading = false;
                        this.setFrame(this.currFrameIndex);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 每帧刷新
    PawnAnimationCmpt.prototype.updateFrame = function (dt) {
        // 刷新精灵动画
        if (!this.loading && this.anim) {
            if (this.delayPlayAnim > 0) {
                this.delayPlayAnim -= dt;
                if (this.delayPlayAnim <= 0) {
                    this.node.opacity = 255;
                }
            }
            else {
                this.playElapsed += dt;
                if (this.playElapsed >= this.playInterval) {
                    this.playElapsed -= this.playInterval;
                    this.setFrame(this.playFrameIndex);
                    if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {
                        this.playFrameIndex += 1;
                    }
                    else if (this.anim.loop) {
                        this.playFrameIndex = 0;
                    }
                    else {
                        this.anim = null;
                        this.playCallback && this.playCallback();
                    }
                }
            }
        }
        // 刷新移动
        this.updateMove(dt);
    };
    // 播放动画
    PawnAnimationCmpt.prototype.play = function (name, cb, startTime, intervalMul) {
        var anim = this.anim = this.conf.anims.find(function (m) { return m.name === name; });
        if (!anim) {
            return cb && cb();
        }
        this.playCallback = cb;
        this.playInterval = (anim.interval || 1) * (intervalMul || 1);
        startTime = startTime || 0;
        var index = Math.floor(startTime / this.playInterval);
        this.playFrameIndex = index + 1;
        this.playElapsed = startTime % this.playInterval;
        this.setFrame(index);
    };
    PawnAnimationCmpt.prototype.delayPlay = function (delay, name, cb, startTime) {
        if (delay > 0) {
            this.delayPlayAnim = delay;
        }
        else {
            this.node.opacity = 255;
        }
        this.play(name, cb, startTime);
    };
    PawnAnimationCmpt.prototype.stop = function (isCallback) {
        if (isCallback === void 0) { isCallback = true; }
        this.setFrame(0);
        this.anim = null;
        if (isCallback) {
            this.playCallback && this.playCallback();
        }
        this.playCallback = null;
    };
    // 设置一帧
    PawnAnimationCmpt.prototype.setFrame = function (index) {
        this.currFrameIndex = index;
        if (this.anim && !this.loading) {
            var name = this.anim.frameIndexs[index];
            if (name) {
                this.sprite.spriteFrame = this.frames[this.frameNamePrefix + name];
            }
        }
    };
    Object.defineProperty(PawnAnimationCmpt.prototype, "playAnimName", {
        get: function () {
            var _a;
            return ((_a = this.anim) === null || _a === void 0 ? void 0 : _a.name) || '';
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnAnimationCmpt.prototype, "needPlayTime", {
        get: function () {
            if (this.anim) {
                return this.anim.interval * this.anim.frameIndexs.length;
            }
            return 0;
        },
        enumerable: false,
        configurable: true
    });
    PawnAnimationCmpt.prototype.resetMove = function () {
        this.moveCurrTime = 0;
        this.moveNeedTime = 0;
        this.moveCallback = null;
        this.moveParabolaHeight = 0;
        return this;
    };
    PawnAnimationCmpt.prototype.moveNodeOne = function (currTime, needTime, startPos, targetPos, cb) {
        this.moveCurrTime += currTime;
        this.moveNeedTime = needTime;
        this.moveStartPos.set(startPos);
        this.moveTargetPos.set(targetPos);
        this.moveCallback = cb;
    };
    // 设置延迟移动
    PawnAnimationCmpt.prototype.setMoveDelay = function (delay) {
        this.delayMoveTime = delay;
        return this;
    };
    // 设置抛物线高度
    PawnAnimationCmpt.prototype.setMoveParabolaHeight = function (val) {
        this.moveParabolaHeight = val;
        return this;
    };
    // 刷新移动
    PawnAnimationCmpt.prototype.updateMove = function (dt) {
        if (this.delayMoveTime > 0) {
            this.delayMoveTime -= dt;
        }
        else if (this.moveNeedTime > 0) {
            this.moveCurrTime += dt;
            if (this.moveCurrTime >= this.moveNeedTime) {
                if (dt >= this.moveNeedTime) {
                    this.moveCurrTime = 0;
                }
                else {
                    this.moveCurrTime -= this.moveNeedTime;
                }
                this.moveNeedTime = 0;
                this.moveParabolaHeight = 0;
                this.node.setPosition(this.moveTargetPos);
                this.moveCallback && this.moveCallback();
            }
            else {
                this.node.setPosition(this.getMoveNextPosition(Math.min(1, this.moveCurrTime / this.moveNeedTime)));
            }
        }
    };
    PawnAnimationCmpt.prototype.getMoveNextPosition = function (percent) {
        var pos = this.moveStartPos.lerp(this.moveTargetPos, percent, this._temp_vec2_2);
        if (this.moveParabolaHeight > 0) {
            var h = this.moveParabolaHeight * Math.cos(percent * Math.PI - Math.PI / 2);
            var isY = this.moveStartPos.y !== this.moveTargetPos.y;
            if (isY) {
                pos.x += h;
            }
            if (this.moveStartPos.x !== this.moveTargetPos.x || !isY) {
                pos.y += h;
            }
        }
        return pos;
    };
    __decorate([
        property([cc.SpriteFrame])
    ], PawnAnimationCmpt.prototype, "sfs", void 0);
    PawnAnimationCmpt = __decorate([
        ccclass
    ], PawnAnimationCmpt);
    return PawnAnimationCmpt;
}(cc.Component));
exports.default = PawnAnimationCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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