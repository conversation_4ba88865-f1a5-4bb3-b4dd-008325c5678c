
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/JsonType.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '501e5izCgtMcJWywkYBpeA9', 'JsonType');
// app/script/common/constant/JsonType.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcY29uc3RhbnRcXEpzb25UeXBlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJcbi8vIOWcsOWdl1xudHlwZSBMYW5kSkl0ZW0gPSB7XG4gICAgaWQ6IG51bWJlcjtcbiAgICB0eXBlOiBudW1iZXI7XG4gICAgbHY6IG51bWJlcjtcbiAgICBocDogbnVtYmVyOyAvL+m7mOiupOihgOmHj1xuICAgIG9jY3VweTogbnVtYmVyOyAvL+aYr+WQpuWPr+WNoOmihlxuICAgIGNlcmVhbDogbnVtYmVyO1xuICAgIHRpbWJlcjogbnVtYmVyO1xuICAgIHN0b25lOiBudW1iZXI7XG4gICAgaWNvbjogc3RyaW5nOyAvL+WcsOS4iuWbvuagh1xuICAgIG5hbWU6IHN0cmluZztcbn1cblxuLy8g6KOF6aWwXG50eXBlIERlY29yYXRpb25KSXRlbSA9IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIHR5cGU6IG51bWJlcjtcbiAgICBpY29uOiBzdHJpbmc7IC8v5Zu+5qCHXG59XG5cbi8vIOWfjuW4glxudHlwZSBDaXR5Skl0ZW0gPSB7XG4gICAgaWQ6IG51bWJlcjtcbiAgICBjZWxsX3NpemU6IHN0cmluZzsgLy/lnLDlnZflpKflsI9cbiAgICBhcmVhX3NpemU6IHN0cmluZzsgLy/ljLrln5/pnaLnp69cbiAgICBidWlsZF9zaXplOiBzdHJpbmc7IC8v5bu6562R6Z2i56evXG4gICAgbWF4X2FybXk6IG51bWJlcjsgLy/mnIDlpKflhpvpmJ/mlbDph49cbiAgICBocDogbnVtYmVyO1xuICAgIG5lZWRfbGFuZDogbnVtYmVyOyAvL+mcgOimgeW7uuWcqOWTquS4quWcsOWdl1xuICAgIGJ0X2NvdW50OiBudW1iZXI7IC8v5Y+v5L+u5bu65pWw6YePXG4gICAgYnRfY29uZDogc3RyaW5nOyAvL+S/ruW7uuadoeS7tlxuICAgIGJ0X2Nvc3Q6IHN0cmluZzsgLy/kv67lu7rotLnnlKhcbiAgICBidF90aW1lOiBudW1iZXI7IC8v5L+u5bu65pe26Ze0KOenkilcbiAgICBkdF90aW1lOiBudW1iZXI7IC8v5ouG6Zmk5pe26Ze0KOenkilcbiAgICBpY29uOiBzdHJpbmc7IC8v5Zyw5LiK5Zu+5qCHXG4gICAgbmFtZTogc3RyaW5nO1xuICAgIGRlc2M6IHN0cmluZztcbn1cblxuLy8g5bu6562RYmFzZVxudHlwZSBCdWlsZEJhc2VKSXRlbSA9IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIHR5cGU6IG51bWJlcjtcbiAgICBidF9jb3VudDogbnVtYmVyOyAvL+WPr+S/ruW7uuaVsOmHj1xuICAgIHByZXBfY29uZDogc3RyaW5nOyAvL+WJjee9ruadoeS7tlxuICAgIHNpemU6IHN0cmluZzsgLy/lu7rnrZHlpKflsI9cbiAgICBwYXduX2lkOiBudW1iZXI7IC8v5YWz6IGU5aOr5YW1aWRcbiAgICBzb3J0OiBudW1iZXI7IC8v5o6S5bqPXG4gICAgdWk6IHN0cmluZzsgLy/mmL7npLpVSVxuICAgIHBvczogc3RyaW5nOyAvL+m7mOiupOS9jee9rlxuICAgIGRyYWc6IG51bWJlcjsgLy/mmK/lkKblj6/ku6Xmi5bmi71cbn1cblxuLy8g5bu6562RYXR0clxudHlwZSBCdWlsZEF0dHJKSXRlbSA9IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIHByZXBfY29uZDogc3RyaW5nOyAvL+WJjee9ruadoeS7tlxuICAgIHVwX2Nvc3Q6IHN0cmluZzsgLy/ljYfnuqfmnaHku7ZcbiAgICBidF90aW1lOiBudW1iZXI7IC8v5L+u5bu65pe26Ze077yI56eS77yJXG4gICAgZWZmZWN0czogc3RyaW5nOyAvL+aViOaenOWIl+ihqFxufVxuXG4vLyDlo6vlhbVhdHRyXG50eXBlIFBhd25BdHRySkl0ZW0gPSB7XG4gICAgaWQ6IG51bWJlcjtcbiAgICBocDogbnVtYmVyO1xuICAgIGF0dGFjazogbnVtYmVyO1xuICAgIGF0dGFja19yYW5nZTogbnVtYmVyO1xuICAgIG1vdmVfcmFuZ2U6IG51bWJlcjtcbiAgICBhbmdlcjogbnVtYmVyOyAvL+aAkuawlOWAvFxuICAgIGluaXRfYW5nZXI6IG51bWJlcjsgLy/liJ3lp4vljJbmgJLmsJRcbiAgICBza2lsbDogc3RyaW5nO1xuICAgIGJlaGF2aW9yX2lkOiBudW1iZXI7XG4gICAgYXR0YWNrX2FuaW1fdGltZTogc3RyaW5nOyAvL+aUu+WHu+WKqOeUu+aXtumXtFxuICAgIGx2X2NvbmQ6IHN0cmluZztcbiAgICBsdl9jb3N0OiBzdHJpbmc7XG4gICAgbHZfdGltZTogbnVtYmVyOyAvL+WNh+e6p+aXtumXtFxufVxuXG5leHBvcnQge1xuICAgIExhbmRKSXRlbSxcbiAgICBDaXR5Skl0ZW0sXG4gICAgQnVpbGRCYXNlSkl0ZW0sXG4gICAgQnVpbGRBdHRySkl0ZW0sXG4gICAgUGF3bkF0dHJKSXRlbSxcbiAgICBEZWNvcmF0aW9uSkl0ZW0sXG59Il19