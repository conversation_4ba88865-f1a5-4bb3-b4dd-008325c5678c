
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LocaleFont.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '23099pYahRDnKU3m2XIkGEN', 'LocaleFont');
// app/core/component/LocaleFont.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LocaleFont = /** @class */ (function (_super) {
    __extends(LocaleFont, _super);
    function LocaleFont() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.fontName = '';
        _this._label = null;
        _this._lang = '';
        _this._font = '';
        _this._change = false;
        return _this;
    }
    Object.defineProperty(LocaleFont.prototype, "label", {
        get: function () {
            if (!this._label) {
                this._label = this.Component(cc.Label);
            }
            return this._label;
        },
        enumerable: false,
        configurable: true
    });
    LocaleFont.prototype.onEnable = function () {
        if (!mc.lang) {
            return;
        }
        else if (this._lang !== mc.lang) {
            this._lang = mc.lang;
            this._font = '';
        }
        else if (this.label.font && !this.label.font.isValid) {
            this._font = '';
        }
        if (!this._font) {
            this.updateFont();
        }
        this._change = mc.canChangeLang;
        if (this._change) {
            eventCenter.on(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    LocaleFont.prototype.onDisable = function () {
        if (this._change) {
            this._change = false;
            eventCenter.off(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    // 语言切换
    LocaleFont.prototype.onLanguageChanged = function (lang) {
        this._lang = lang;
        this._font = '';
        this.updateFont();
    };
    Object.defineProperty(LocaleFont.prototype, "string", {
        get: function () { return this.label.string; },
        set: function (val) {
            this.label.string = val;
        },
        enumerable: false,
        configurable: true
    });
    LocaleFont.prototype.updateLang = function () {
        this._lang = mc.lang;
    };
    // 刷新字体
    LocaleFont.prototype.updateFont = function () {
        if (this.fontName !== '' && this.fontName !== this._font) {
            this.setFont(this.fontName);
        }
    };
    LocaleFont.prototype.setFont = function (fontUrl) {
        if (!this.label) {
            return;
        }
        this._font = fontUrl;
        // const text = this.label.string
        // this.label.string = ''
        var font = assetsMgr.getFont(fontUrl);
        if (font) {
            this.label.font = font;
        }
        else {
            this.label.font = null;
            this._font = '';
        }
        // this.label.string = text
    };
    __decorate([
        property()
    ], LocaleFont.prototype, "fontName", void 0);
    LocaleFont = __decorate([
        ccclass,
        menu('多语言组件/LocaleFont')
    ], LocaleFont);
    return LocaleFont;
}(BaseLocale_1.default));
exports.default = LocaleFont;
cc.LocaleFont = LocaleFont;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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