
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseViewCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4c8972RNlJJ8oA223CppFZA', 'BaseViewCtrl');
// app/core/base/BaseViewCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseMvcCtrl_1 = require("./BaseMvcCtrl");
// 基础视图控制器
var BaseViewCtrl = /** @class */ (function (_super) {
    __extends(BaseViewCtrl, _super);
    function BaseViewCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._state = 'none';
        _this.__listens = []; //监听列表
        return _this;
    }
    BaseViewCtrl.prototype.__register = function (val) {
        this.__listens.forEach(function (_a) {
            var type = _a.type, cb = _a.cb, target = _a.target, tag = _a.tag;
            return (!val || val === tag) && eventCenter.on(type, cb, target);
        });
    };
    BaseViewCtrl.prototype.__unregister = function (val) {
        this.__listens.forEach(function (_a) {
            var type = _a.type, cb = _a.cb, target = _a.target, tag = _a.tag;
            return (!val || val === tag) && eventCenter.off(type, cb, target);
        });
    };
    BaseViewCtrl.prototype.__listenMaps = function () {
        this.__wrapListenMaps(this.listenEventMaps(), this.__listens, this);
    };
    BaseViewCtrl.prototype.addListener = function (type, cb, target) {
        this.__listens.push({ type: type, cb: cb, target: target });
        if (this._state === 'enter') {
            eventCenter.on(type, cb, target);
        }
    };
    BaseViewCtrl.prototype.removeListener = function (type) {
        var data = this.__listens.remove('type', type);
        if (data) {
            eventCenter.off(data.type, data.cb, data.target);
        }
    };
    BaseViewCtrl.prototype.emit = function (type) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        eventCenter.emit.apply(eventCenter, __spreadArrays([type], params));
    };
    // 添加点击事件
    BaseViewCtrl.prototype.addClickEvent = function (cmpt, handler, data) {
        if (!cmpt) {
            return;
        }
        if (!this[handler] || typeof (this[handler]) !== 'function') {
            return logger.error(handler + ' 没有找到对应的方法名 at=' + this['__classname__']);
        }
        var events = this.__getEvents(cmpt);
        if (events) {
            events[0] = this.__newEventHandler(handler, data);
        }
        else {
            return logger.error(handler + ' 没有对应的events at=' + cmpt.name);
        }
    };
    BaseViewCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BaseViewCtrl.prototype.getModel = function (key) {
        return mc.getModel(key);
    };
    return BaseViewCtrl;
}(BaseMvcCtrl_1.default));
exports.default = BaseViewCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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