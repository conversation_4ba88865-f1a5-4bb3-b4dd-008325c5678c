{"version": 3, "sources": ["assets\\app\\script\\view\\common\\ShopPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA+E;AAC/E,qDAAoD;AACpD,qDAA0G;AAC1G,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,iEAAgE;AAChE,2DAA0D;AAC1D,6DAA4D;AAC5D,wDAAuD;AACvD,kEAA6D;AAGrD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAyC,+BAAc;IAAvD;QAAA,qEAw/BC;QAt/BA,0BAA0B;QAClB,cAAQ,GAAY,IAAI,CAAA,CAAC,eAAe;QACxC,eAAS,GAAY,IAAI,CAAA,CAAC,gBAAgB;QAC1C,aAAO,GAAkB,IAAI,CAAA,CAAC,wBAAwB;QACtD,qBAAe,GAAY,IAAI,CAAA,CAAC,mDAAmD;QACnF,mBAAa,GAAY,IAAI,CAAA,CAAC,kEAAkE;QAChG,uBAAiB,GAAY,IAAI,CAAA,CAAC,sEAAsE;QACxG,gBAAU,GAAY,IAAI,CAAA,CAAC,6CAA6C;QACxE,kBAAY,GAAkB,IAAI,CAAA,CAAC,4DAA4D;QAC/F,eAAS,GAAY,IAAI,CAAA,CAAC,4CAA4C;QACtE,qBAAe,GAAY,IAAI,CAAA,CAAC,mEAAmE;QACnG,sBAAgB,GAAY,IAAI,CAAA,CAAC,oDAAoD;QACrF,mBAAa,GAAY,IAAI,CAAA,CAAC,iDAAiD;QAC/E,mBAAa,GAAY,IAAI,CAAA,CAAC,iDAAiD;QAC/E,kBAAY,GAAY,IAAI,CAAA,CAAC,gDAAgD;QAC7E,wBAAkB,GAAY,IAAI,CAAA,CAAC,sDAAsD;QACzF,mBAAa,GAAY,IAAI,CAAA,CAAC,iDAAiD;QAC/E,yBAAmB,GAAY,IAAI,CAAA,CAAC,uDAAuD;QAC3F,mBAAa,GAAY,IAAI,CAAA,CAAC,4BAA4B;QAC1D,qBAAe,GAAY,IAAI,CAAA,CAAC,8BAA8B;QAC9D,aAAO,GAAuB,IAAI,CAAA,CAAC,4BAA4B;QACvE,MAAM;QAEN,+BAA+B;QACd,sBAAgB,GAAG;YACnC,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE;YACzB,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE;YAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE;YAC7B,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;YAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE;YACzB,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE;YAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE;YAC7B,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE;YAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE;SACzB,CAAA;QAEgB,cAAQ,GAAW,UAAU,CAAA;QAEtC,gBAAU,GAAuB,IAAI,CAAA;QACrC,iBAAW,GAAuB,IAAI,CAAA;QAEtC,UAAI,GAAc,IAAI,CAAA;QACtB,YAAM,GAAW,CAAC,CAAA;QAClB,cAAQ,GAAW,EAAE,CAAA;QACrB,sBAAgB,GAAc,IAAI,CAAA;QAClC,mBAAa,GAAW,GAAG,CAAA;QAo7B3B,eAAS,GAAW,CAAC,CAAA;QACrB,cAAQ,GAAW,EAAE,CAAA;QACrB,gBAAU,GAAW,CAAC,CAAA;QAEtB,cAAQ,GAAe;YAC9B,KAAI,CAAC,eAAe;YACpB,KAAI,CAAC,iBAAiB;YACtB,KAAI,CAAC,cAAc;YACnB,KAAI,CAAC,eAAe;SACpB,CAAA;;IAYF,CAAC;IAv8BO,qCAAe,GAAtB;;QACC,OAAO;sBACJ,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,YAAY,EAAE,QAAK,GAAE,IAAI;sBACvD,GAAC,mBAAS,CAAC,YAAY,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBACzD,GAAC,mBAAS,CAAC,eAAe,IAAG,IAAI,CAAC,eAAe,EAAE,QAAK,GAAE,IAAI;sBAC9D,GAAC,mBAAS,CAAC,mBAAmB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACtE,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,qBAAqB,EAAE,QAAK,GAAE,IAAI;sBAC1E,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,kBAAkB,EAAE,QAAK,GAAE,IAAI;SACrE,CAAA;IACF,CAAC;IAEY,8BAAQ,GAArB;;;;;gBACC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAA;gBAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACjC,IAAI,CAAC,oBAAoB,EAAE,CAAA;gBAC3B,kGAAkG;gBAClG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,4BAAiB,GAAG,EAAE,CAAA;gBACpF,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAvB,CAAuB,EAAE,GAAG,CAAC,CAAA;gBAC3E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,CAAA;;;;KACvD;IAEM,6BAAO,GAAd,UAAe,IAAa,EAAE,QAAiB;;QAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;YAC/B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;SACnB;QACD,IAAI,CAAC,MAAM,SAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,mCAAI,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,yBAAW,CAAC,OAAO,IAAI,CAAC,oBAAO,CAAC,SAAS,CAAA,CAAC,UAAU;QAChG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,OAAO,EAAE,CAAA;QACd,IAAI,CAAC,uBAAuB,EAAE,CAAA;QAC9B,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,yBAAyB;QACzB,2BAA2B;QAC3B,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,wBAAwB;QACxB,yBAAyB;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;QACzC,IAAI,CAAC,iBAAiB,EAAE,CAAA;IACzB,CAAC;IAEM,8BAAQ,GAAf;QACC,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAEM,6BAAO,GAAd;QACC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;QACpC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACxC,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,4BAA4B;IAC5B,iCAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACzC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS;YAC1B,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC9B,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;aACrB;YACD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;SAC/D;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS;YACjC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;SAC/D;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,SAAS;YACjC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAA;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;YACnC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;SACjF;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,WAAW;YACnC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAA;YACjC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAA;YAClC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;SAC9E;QACD,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACrC,CAAC;IAED,0CAA0C;IAC1C,qCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QACvD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO;YAC1B,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;SACjF;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO;YACjC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;SACxB;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO;YACjC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;SACxB;QACD,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACrC,CAAC;IAED,8CAA8C;IAC9C,uCAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO;YAC1B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA;SAC9E;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO;YACjC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAA;SACjF;QACD,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACrC,CAAC;IAED,kEAAkE;IAClE,qCAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;QAAxD,iBAaC;QAZA,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACpC,OAAO,uBAAU,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;SAC7C;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAA,GAAG;YAC/B,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,KAAI,CAAC,cAAc,EAAE,CAAA;gBACrB,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;aACzC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,+DAA+D;IAC/D,oCAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QACtD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,uBAAU,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAA;IAC5C,CAAC;IAED,sEAAsE;IACtE,yCAAmB,GAAnB,UAAoB,KAA0B,EAAE,IAAY;QAA5D,iBAWC;QAVA,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE;YACxC,OAAO,uBAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;SAC9C;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAA;QACjG,uBAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE,2BAAmB,CAAC,GAAG,EAAE,IAAI,EAAE,UAAC,GAAoB;YAChG,IAAI,KAAI,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnC,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;aACvB;QACF,CAAC,EAAE,CAAC,CAAC,CAAA;IACN,CAAC;IAED,sEAAsE;IACtE,0CAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QAC5D,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,EAAjC,CAAiC,CAAC,CAAA;IACjF,CAAC;IAED,mEAAmE;IACnE,uCAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,qBAAS,CAAC,aAAa,EAAE,CAAA;IAC1B,CAAC;IAED,2DAA2D;IAC3D,kCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QAArD,iBAsBC;QArBA,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACxB,OAAO,uBAAU,CAAC,cAAc,CAAC,wBAAwB,EAAE;gBAC1D,MAAM,EAAE,wBAAwB;gBAChC,EAAE,EAAE,cAAM,OAAA,uBAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAxC,CAAwC;gBAClD,MAAM,EAAE,cAAQ,CAAC;aACjB,CAAC,CAAA;SACF;aAAM,IAAI,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAE;YACrC,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SACzD;QACD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,uBAAU,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE,UAAC,EAAW;YAC9D,IAAI,EAAE,EAAE;gBACP,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAC,GAAY;oBACvD,MAAM,CAAC,KAAK,CAAC,wBAAwB,GAAG,GAAG,GAAG,YAAY,GAAG,KAAI,CAAC,OAAO,CAAC,CAAA;oBAC1E,IAAI,GAAG,IAAI,KAAI,CAAC,OAAO,EAAE;wBACxB,KAAI,CAAC,eAAe,EAAE,CAAA;qBACtB;gBACF,CAAC,CAAC,CAAA;aACF;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,0FAA0F;IAC1F,gCAAU,GAAV,UAAW,KAA0B,EAAE,KAAa;QAApD,iBAuCC;QAtCA,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,IAAI,EAAE;YACV,OAAM;SACN;QACD,IAAI,OAAO,GAAG,EAAE,CAAA;QAChB,IAAI,KAAK,KAAK,WAAW,EAAE,EAAE,KAAK;YACjC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;YACnB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;SAChB;QACD,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE,EAAE,UAAU;YACjG,IAAM,MAAI,GAAG,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAA;YAC/E,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,IAAI,QAAA,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,UAAC,GAAG;gBACzE,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;oBAC1B,OAAM;iBACN;qBAAM,IAAI,oBAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;oBACtC,OAAM;iBACN;gBACD,IAAI,MAAI,KAAK,WAAW,EAAE;oBACzB,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;iBACxB;qBAAM,IAAI,MAAI,KAAK,WAAW,EAAE;oBAChC,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;iBACxB;YACF,CAAC,CAAC,CAAA;SACF;aAAM,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,EAAE,EAAE,KAAK;YACrG,IAAM,MAAI,GAAG,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAA;YAC/E,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,MAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,UAAC,GAAG;gBAChF,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;oBAC1B,OAAM;iBACN;qBAAM,IAAI,oBAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;oBACtC,OAAM;iBACN;gBACD,IAAI,MAAI,KAAK,UAAU,EAAE;oBACxB,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;iBACxB;qBAAM,IAAI,MAAI,KAAK,YAAY,EAAE;oBACjC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;iBACzB;YACF,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAED,sEAAsE;IACtE,qCAAe,GAAf,UAAgB,KAA0B,EAAE,KAAa;QACxD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACxD,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK;YACxB,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACxD,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;aAClD;iBAAM;gBACN,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,uBAAe,CAAC,IAAI,CAAC,CAAA;aACnE;SACD;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK;YAC/B,IAAI,CAAC,mBAAmB,CAAC,uBAAe,CAAC,IAAI,CAAC,CAAA;SAC9C;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,MAAM;SAEhC;IACF,CAAC;IAED,uEAAuE;IACvE,sCAAgB,GAAhB,UAAiB,KAA0B,EAAE,KAAa;QACzD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACxD,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK;YACxB,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBACxD,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;aAClD;iBAAM;gBACN,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,uBAAe,CAAC,KAAK,CAAC,CAAA;aACpE;SACD;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,KAAK;YAC/B,IAAI,CAAC,mBAAmB,CAAC,uBAAe,CAAC,KAAK,CAAC,CAAA;SAC/C;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,MAAM;SAEhC;IACF,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,OAAO;IACC,mCAAa,GAArB;QACC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;QAClC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QACzD,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED,OAAO;IACC,kCAAY,GAApB;QACC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QAChC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QACvD,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,gBAAgB;IACR,qCAAe,GAAvB;QACC,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,CAAC,uBAAuB,EAAE,CAAA;IAC/B,CAAC;IAED,SAAS;IACD,yCAAmB,GAA3B;QACC,OAAO,CAAC,GAAG,CAAC,0BAA0B,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;QACvE,IAAI,CAAC,iBAAiB,EAAE,CAAA;IACzB,CAAC;IAED,SAAS;IACD,2CAAqB,GAA7B;QACC,IAAI,CAAC,eAAe,EAAE,CAAA;IACvB,CAAC;IAED,OAAO;IACC,wCAAkB,GAA1B;QACC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACxD,CAAC;IAED,iHAAiH;IAEzG,6BAAO,GAAf;QACC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QAClF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,eAAe,CAAC,CAAA;QAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,eAAe,CAAC,CAAA;QAC9D,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC9D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED,GAAG;IACK,uCAAiB,GAAzB;QACC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;QAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAA;QAC1B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;IACvF,CAAC;IAED,WAAW;IACG,6CAAuB,GAArC,UAAsC,SAAmB;;;;;;6BACpD,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAzB,wBAAyB;wBAC5B,qBAAM,qBAAS,CAAC,YAAY,EAAE,EAAA;;wBAA9B,SAA8B,CAAA;;4BAGpB,qBAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAS,CAAC,WAAW,EAAE,CAAC,EAAA;;wBAApE,EAAE,GAAG,SAA+D;wBAC1E,IAAI,EAAE,EAAE;4BACP,uBAAU,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAA;yBAC5C;6BAAM,IAAI,CAAC,SAAS,EAAE;yBACtB;6BAAM,IAAI,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAE;4BACrC,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;yBAClD;6BAAM;4BACN,uBAAU,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAA;yBACvD;;;;;KACD;IAED,OAAO;IACC,qCAAe,GAAvB;QAAA,iBA4BC;QA3BA,IAAM,QAAQ,GAAG,qBAAS,CAAC,YAAY,EAAE,CAAA;QACzC,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,CAAC,MAAM,EAAV,CAAU,CAAC,CAAA;QACxE,IAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC9D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;YAClD,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,qBAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YAC7D,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC/D,IAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,QAAQ,EAAE;gBAC5C,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,qBAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;aAC3F;YACD,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;YACpC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI;gBAChD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAA;gBACnB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;aAClE;iBAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE;gBACvC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;aAC5D;YACD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAA;QAC3C,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,KAAK;QACL,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,EAAE;QACF,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,YAAY,EAAE,CAAA;IACpB,CAAC;IAED,OAAO;IACC,qCAAe,GAAvB;QAAA,iBAiDC;QAhDA,IAAM,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;QACvE,IAAM,IAAI,GAAG,EAAE,CAAA;gCAEN,CAAC;YACT,IAAI,GAAG,GAAG,EAAE,CAAA;YACZ,IAAM,IAAI,GAAG,OAAK,gBAAgB,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBACzB,GAAG,GAAG,OAAK,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;aAC9C;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBAChC,GAAG,GAAG,OAAK,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;aAC9C;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBAChC,GAAG,GAAG,OAAK,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;aAC9C;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;gBACjC,GAAG,GAAG,OAAK,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA;aAC/C;YACD,IAAI,GAAG,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACf;;;QAfF,kBAAkB;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA5C,CAAC;SAeT;QACD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;QAClC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAS;YAC3C,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAA;YACjE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,YAAY,CAAA;YAClD,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/D,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;YACd,IAAI,IAAI,GAAG,IAAI,CAAA;YACf,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBACzB,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;gBACjD,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aACnD;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;gBACjB,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;gBACjD,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aAC/C;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBAChC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;gBACjD,qBAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;aACzD;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;gBACjC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;gBAClD,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aAChD;YACD,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,MAAA,EAAE,CAAA;YACnC,KAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YAC7B,IAAM,KAAK,GAAG,oBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YAC/C,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YAC9B,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YACvC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAA;QAClD,CAAC,CAAC,CAAA;IACH,CAAC;IAED,OAAO;IACC,uCAAiB,GAAzB;QACC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACzC,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAA;gCAC/B,CAAC;YACT,IAAM,IAAI,GAAG,qBAAU,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAA;YAC1D,IAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAA9C,CAA8C,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAA1C,CAA0C,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAApC,CAAoC,CAAC,CAAA;YAChN,IAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAA,CAAC,QAAQ;YAChC,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA,CAAC,OAAO;YAC7F,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,CAAC,OAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1F,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,UAAU,CAAA;YACrC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;YACtD,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YAC3E,IAAM,MAAM,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAA;YACjC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YAChC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC7C,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC7B,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;YACjC,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;YACtE,IAAI,KAAK,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAA;aAC7D;iBAAM;gBACN,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;gBACrD,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,GAAG,EAAE,CAAA;gBACtD,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;gBACzF,IAAI,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE;oBACzD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;iBAChD;aACD;YACD,IAAM,MAAM,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,MAAK,CAAC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,IAAG,CAAC,EAAE,OAAO,GAAG,CAAC,OAAO,IAAI,MAAM,CAAA;YAChG,IAAI,CAAC,KAAK,CAAC,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;gBAC9C,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;gBACpD,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,kBAAkB;gBAC3G,MAAM,CAAC,IAAI,GAAG,OAAO,CAAA;gBACrB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,aAAa;aACjD;iBAAM;gBACN,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAA9B,CAA8B,CAAC,CAAA;gBACvE,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;oBACxC,IAAI,CAAC,KAAK,CAAC,EAAE;wBACZ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;qBAC7C;yBAAM;wBACN,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;qBAC/C;gBACF,CAAC,CAAC,CAAA;gBACF,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,kBAAkB;gBAC5G,MAAM,CAAC,IAAI,GAAG,OAAO,CAAA;gBACrB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,aAAa;aACjD;;;QA5CF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE;oBAAlC,CAAC;SA6CT;IACF,CAAC;IAED,OAAO;IACO,gCAAU,GAAxB;;;;;;wBACO,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;wBAEjE,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;wBAE9C,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;wBAC/C,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,CAAA;wBACjC,YAAY,GAAG,CAAC,CAAA;wBACpB,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC;4BACb,IAAI,CAAC,CAAC,IAAI,GAAG,GAAG,EAAE;gCACjB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gCACZ,YAAY,GAAG,CAAC,CAAC,IAAI,CAAA;6BACrB;iCAAM,IAAI,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;gCACzD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;6BACZ;iCAAM;gCACN,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;6BACZ;wBACF,CAAC,CAAC,CAAA;wBACF,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;4BACd,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;4BAClB,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;4BAChE,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;4BAChE,OAAO,EAAE,GAAG,EAAE,CAAA;wBACf,CAAC,CAAC,CAAA;wBACF,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;4BACd,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;4BAClB,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;4BAChE,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;4BAChE,OAAO,EAAE,GAAG,EAAE,CAAA;wBACf,CAAC,CAAC,CAAA;wBACF,kBAAkB;wBAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;4BACxG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;4BACpD,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;4BACxE,KAAA,OAAuB,CAAC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAzF,SAAS,QAAA,EAAE,OAAO,QAAA,CAAuE;4BAChG,QAAQ,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAA;yBAClF;wBACD,OAAO;wBACP,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;6BAEpD,CAAA,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA,EAA7C,wBAA6C;wBAChD,qBAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAA;;wBAA7C,SAA6C,CAAA;wBAC7C,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;;;;;;KAErC;IAED,SAAS;IACK,0CAAoB,GAAlC,UAAmC,EAAW;;;;;;wBAC7C,YAAY;wBACZ,IAAI,CAAC,EAAE,EAAE;4BACF,eAAa,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;4BACvE,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gCAC5C,IAAI,CAAC,CAAC,IAAI,GAAG,GAAG,EAAE;oCACjB,OAAM;iCACN;gCACK,IAAA,KAAA,OAAuB,CAAC,CAAC,CAAC,aAAa,GAAG,YAAU,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAnF,SAAS,QAAA,EAAE,OAAO,QAAiE,CAAA;gCAC1F,IAAI,SAAS,IAAI,OAAO,IAAI,CAAC,oBAAO,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;oCAC/E,OAAM,CAAC,SAAS;iCAChB;gCACD,EAAE,GAAG,CAAC,CAAC,IAAI,CAAA;4BACZ,CAAC,CAAC,CAAA;yBACF;6BACG,CAAC,IAAI,CAAC,gBAAgB,EAAtB,wBAAsB;wBACzB,KAAA,IAAI,CAAA;wBAAoB,qBAAM,SAAS,CAAC,WAAW,CAAC,wBAAwB,GAAG,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAAvG,GAAK,gBAAgB,GAAG,SAA+E,CAAA;;4BAExG,sBAAO,IAAI,CAAC,gBAAgB,EAAA;;;;KAC5B;IAEO,mCAAa,GAArB,UAAsB,UAAkB,EAAE,GAAU;QACnD,IAAI,cAAc,GAAG,IAAI,CAAA;QACzB,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,CAAC,EAAE;YAC3C,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SACjD;aAAM;YACN,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;SAC7E;QACD,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC1B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACjD,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QACnC,IAAA,KAAA,OAAuB,CAAC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAzF,SAAS,QAAA,EAAE,OAAO,QAAuE,CAAA;QAChG,IAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9E,OAAO,CAAC,YAAY,CAAC,0BAA0B,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;QAC1F,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;QAEjD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAA;QACzF,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,uBAAuB,CAAC,CAAA;QAClG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,qBAAqB,CAAC,CAAA;QAE9F,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7D,2BAAY,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YACtC,UAAU,CAAC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC7D;IACF,CAAC;IAEO,sCAAgB,GAAxB,UAAyB,IAAa,EAAE,UAAmB;QAA3D,iBAaC;QAZA,UAAU,GAAG,UAAU,IAAI,CAAC,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QACjF,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;QACvG,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,UAAC,EAAE,EAAE,IAAI;;YACrC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;YACjB,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;YAC/D,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YACnD,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YAC7C,IAAA,KAAA,OAAsB,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAA,EAAxD,MAAM,QAAA,EAAE,SAAS,QAAuC,CAAA;YAC/D,IAAM,QAAQ,GAAG,OAAA,KAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,MAAM,EAAf,CAAe,CAAC,0CAAE,MAAM,KAAI,CAAC,CAAA;YACtF,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAA;YAC3E,KAAI,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,qBAAqB,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,kCAAY,GAApB,UAAqB,UAAkB;QACtC,IAAM,SAAS,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,EAAnB,CAAmB,CAAC,CAAA;QAClE,OAAO,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC/B,IAAA,KAAA,OAAuB,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAnF,SAAS,QAAA,EAAE,OAAO,QAAiE,CAAA;gBAC1F,IAAI,SAAS,IAAI,OAAO,IAAI,oBAAO,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;oBAC9E,OAAO,IAAI,CAAA;iBACX;aACD;YACD,OAAO,KAAK,CAAA;QACb,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,oCAAc,GAAtB,UAAuB,UAAkB;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;QACrD,IAAM,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC;YACb,uCAAuC;YACvC,gBAAgB;YAChB,WAAW;YACX,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACZ,IAAI;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YACd,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;YAClB,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAChE,EAAE,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAChE,OAAO,EAAE,GAAG,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,wBAAwB;QACxB,sBAAsB;QACtB,oEAAoE;QACpE,oEAAoE;QACpE,kBAAkB;QAClB,KAAK;QACL,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;QACjD,UAAU;QACV,0DAA0D;QAC1D,8CAA8C;QAC9C,4DAA4D;QAC5D,yGAAyG;QACzG,oGAAoG;QACpG,qFAAqF;QACrF,oCAAoC;QACpC,4DAA4D;QAC5D,6DAA6D;QAC7D,IAAI;QACJ,OAAO,IAAI,CAAA;IACZ,CAAC;IAEO,wCAAkB,GAA1B,UAA2B,IAAa,EAAE,IAAW;QAArD,iBAaC;QAZA,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7E,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;gBAC1B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBAC/D,KAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC7B,IAAM,KAAK,GAAG,oBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAC/C,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;gBAC9B,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;gBACvC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAA;YAClD,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAEO,wCAAkB,GAA1B,UAA2B,IAAa,EAAE,IAAW,EAAE,SAAkB;QAAzE,iBAmBC;QAlBA,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,SAAS,EAAE;YACd,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;SAC1B;aAAM;YACN,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;SACzE;QACD,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;;gBAC1B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;gBAClG,KAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC7B,IAAM,KAAK,GAAG,oBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAC/C,MAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,SAAS,CAAC,KAAK,EAAC;gBACjC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;gBACvC,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAA;gBACjD,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YACpE,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAEO,oCAAc,GAAtB,UAAuB,EAAW,EAAE,IAAS;QAC5C,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACnC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,aAAK,CAAC,IAAI,CAAC,CAAA;YAC5E,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;SACnD;aAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,aAAK,CAAC,KAAK,CAAC,CAAA;YAC7E,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;SACpD;aAAM;YACN,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;SACjC;IACF,CAAC;IAEO,gCAAU,GAAlB,UAAmB,OAAe,EAAE,SAAiB;QACpD,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;QACnC,IAAI,CAAC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,MAAK,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAClD,SAAS,GAAG,IAAI,GAAG,GAAG,GAAG,SAAS,CAAA;SAClC;QACD,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,MAAK,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAA,KAAA,OAAW,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAA9B,EAAE,QAAA,EAAE,EAAE,QAAwB,CAAA;YACnC,IAAI,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE;gBAC5B,IAAI,IAAI,CAAC,CAAA;aACT;YACD,OAAO,GAAG,IAAI,GAAG,GAAG,GAAG,OAAO,CAAA;SAC9B;QACG,IAAA,KAAA,OAA8B,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,GAAG,KAAC,EAAhD,OAAO,QAAA,EAAE,QAAQ,QAAA,EAAE,MAAM,QAAuB,CAAA;QACrD,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;IAC5D,CAAC;IAEO,mCAAa,GAArB,UAAsB,OAAe;QAChC,IAAA,KAAA,OAA8B,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAC,GAAG,KAAC,EAAhD,OAAO,QAAA,EAAE,QAAQ,QAAA,EAAE,MAAM,QAAuB,CAAA;QACrD,OAAO,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAA;IAC/B,CAAC;IAEO,iCAAW,GAAnB,UAAoB,EAAU;QAA9B,iBAaC;QAZA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACjC,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;gBACzC,KAAI,CAAC,UAAU,EAAE,CAAA;gBACjB,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,EAAjB,CAAiB,CAAC,EAAE;oBACvD,KAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;gBACD,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;aACzC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,iCAAW,GAAnB,UAAoB,EAAU;QAA9B,iBAaC;QAZA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACjC,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;gBACzC,KAAI,CAAC,UAAU,EAAE,CAAA;gBACjB,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,EAAjB,CAAiB,CAAC,EAAE;oBACvD,KAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;gBACD,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;aACzC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,OAAO;IACC,mCAAa,GAArB,UAAsB,IAAS;QAC9B,uBAAU,CAAC,cAAc,CAAC,uBAAuB,EAAE;YAClD,MAAM,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;YAC/B,EAAE,EAAE,cAAM,OAAA,uBAAU,CAAC,OAAO,CAAC,4BAA0B,IAAI,CAAC,IAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAhF,CAAgF;YAC1F,MAAM,EAAE,cAAQ,CAAC;SACjB,CAAC,CAAA;IACH,CAAC;IAED,OAAO;IACC,oCAAc,GAAtB;QACC,IAAM,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;QACvE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YAC/D,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;YAClB,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5D,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5D,OAAO,EAAE,GAAG,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,OAAO;QACP,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,EAA/B,CAA+B,CAAC,CAAA;QACtE,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,IAAA,KAAA,OAAuB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAA/G,SAAS,QAAA,EAAE,OAAO,QAA6F,CAAA;YACtH,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAEpE,OAAO;QACP,IAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,EAA9B,CAA8B,CAAC,CAAA;QACzE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAC9E,CAAC;IAEO,wCAAkB,GAA1B,UAA2B,IAAa,EAAE,IAAW,EAAE,KAAc,EAAE,IAAY;QAAnF,iBAgBC;QAfA,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,KAAK,EAAE;YACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;YACxD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;SAC1B;aAAM;YACN,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;SACzE;QACD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1C,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;gBAC1B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,qBAAS,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;gBACzE,KAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YAC9B,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAED,OAAO;IACC,iCAAW,GAAnB,UAAoB,EAAU;QAA9B,iBAYC;QAXA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACjC,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;gBACzC,KAAI,CAAC,cAAc,EAAE,CAAA;gBACrB,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,EAAjB,CAAiB,CAAC,EAAE;oBACvD,KAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;aACD;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,OAAO;IACC,qCAAe,GAAvB;QACC,IAAM,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;QACvE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YAChE,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;YAClB,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5D,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,oBAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5D,OAAO,EAAE,GAAG,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,OAAO;QACP,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,EAA/B,CAA+B,CAAC,CAAA;QACnE,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,IAAA,KAAA,OAAuB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAzG,SAAS,QAAA,EAAE,OAAO,QAAuF,CAAA;YAChH,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAEnE,MAAM;QACN,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,EAA9B,CAA8B,CAAC,CAAA;QACtE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAC7E,CAAC;IAEO,yCAAmB,GAA3B,UAA4B,IAAa,EAAE,IAAW,EAAE,KAAc,EAAE,IAAY;QAApF,iBAgBC;QAfA,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,KAAK,EAAE;YACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;YACxD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;SAC1B;aAAM;YACN,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;SACzE;QACD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1C,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;gBAC1B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBAChE,KAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YAC9B,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAEO,kCAAY,GAApB,UAAqB,EAAU;QAA/B,iBAYC;QAXA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAClC,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;gBACzC,KAAI,CAAC,eAAe,EAAE,CAAA;gBACtB,IAAI,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,OAAO,EAAlB,CAAkB,CAAC,EAAE;oBACxD,KAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;aACD;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,OAAO;IACC,6BAAO,GAAf,UAAgB,EAAU;QAA1B,iBASC;QARA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC7B,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,KAAI,CAAC,kBAAkB,EAAE,CAAA;gBACzB,uBAAU,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;aACzC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,SAAS;IACD,oCAAc,GAAtB;QACC,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC/C,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACjE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;IAChH,CAAC;IAED,SAAS;IACD,wCAAkB,GAA1B;QACC,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACrE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAA;IACtH,CAAC;IAED,YAAY;IACJ,yCAAmB,GAA3B,UAA4B,IAAqB;QAAjD,iBASC;QARA,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YAC9C,IAAI,GAAG,EAAE;gBACR,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aAChC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACxB,KAAI,CAAC,iBAAiB,EAAE,CAAA;gBACxB,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAA;aAC3C;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,uCAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QAA1D,iBAYC;QAXA,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAA;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE;YAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE;gBACtC,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,gBAAgB,CAAC,CAAA;aACnD;iBAAM,IAAI,CAAC,oBAAO,CAAC,aAAa,CAAC,sBAAc,CAAC,wBAAwB,CAAC,EAAE;gBAC3E,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,EAAE,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,KAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAxC,CAAwC,CAAC,CAAA;aACtG;iBAAM;gBACN,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aACxB;SACD;IACF,CAAC;IAED,2CAAqB,GAArB,UAAsB,KAA0B,EAAE,IAAY;QAC7D,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;QACrD,EAAE,IAAI,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAA;IACtD,CAAC;IAGD,yCAAmB,GAAnB,UAAoB,KAA0B,EAAE,KAAa;QAC5D,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC9B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAEO,iCAAW,GAAnB,UAAoB,KAAoB;QACvC,IAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,OAAO,EAC5D,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EACrF,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EACpF,QAAQ,GAAG,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,EACnG,QAAQ,GAAG,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,EAC1F,UAAU,GAAG,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA;QACrD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,EAAE;YAC3C,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAA;SACpC;QACD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,UAAU,EAAE;YAC/C,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,UAAU,CAAA;SACxC;QACD,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,QAAQ,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAA;QAC3H,IAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC5D,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACtB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;SACnD;IACF,CAAC;IAEO,8BAAQ,GAAhB,UAAiB,IAAc,EAAE,KAAa;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,OAAO;YAAU,cAAO;iBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;gBAAP,yBAAO;;YACvB,IAAI,CAAC,KAAK,EAAE;gBACX,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACtB,KAAK,GAAG,UAAU,CAAC;oBAClB,KAAK,GAAG,IAAI,CAAA;gBACb,CAAC,EAAE,KAAK,CAAC,CAAA;aACT;QACF,CAAC,CAAA;IACF,CAAC;IAaD,4BAAM,GAAN,UAAO,EAAU;QAChB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC1C,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE;gBACpC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;gBACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;gBAC1C,IAAI,CAAC,SAAS,EAAE,CAAA;aAChB;SACD;IACF,CAAC;IAv/BmB,WAAW;QAD/B,OAAO;OACa,WAAW,CAw/B/B;IAAD,kBAAC;CAx/BD,AAw/BC,CAx/BwC,EAAE,CAAC,WAAW,GAw/BtD;kBAx/BoB,WAAW", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BUY_OPT_HERO_COST, MONTH_CARD } from \"../../common/constant/Constant\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport { CType, MonthlyCardType, NoLongerTipKey, SelectPortrayalType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { payHelper } from \"../../common/helper/PayHelper\";\nimport { reddotHelper } from \"../../common/helper/ReddotHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport { localConfig } from \"../../common/LocalConfig\";\nimport PortrayalInfo from \"../../model/common/PortrayalInfo\";\nimport UserModel from \"../../model/common/UserModel\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class ShopPnlCtrl extends mc.BasePnlCtrl {\n\n\t//@autocode property begin\n\tprivate topNode_: cc.Node = null // path://top_n\n\tprivate rootNode_: cc.Node = null // path://root_n\n\tprivate shopSv_: cc.ScrollView = null // path://root_n/shop_sv\n\tprivate mysteryBoxNode_: cc.Node = null // path://root_n/shop_sv/view/content/mystery_box_n\n\tprivate freeGoldNode_: cc.Node = null // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n\n\tprivate optionalHeroNode_: cc.Node = null // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n\n\tprivate ingotNode_: cc.Node = null // path://root_n/shop_sv/view/content/ingot_n\n\tprivate recommendSv_: cc.ScrollView = null // path://root_n/shop_sv/view/content/recommend/recommend_sv\n\tprivate cardNode_: cc.Node = null // path://root_n/shop_sv/view/content/card_n\n\tprivate restoreBuyNode_: cc.Node = null // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n\n\tprivate limitedSkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/limited_skin_n\n\tprivate citySkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/city_skin_n\n\tprivate pawnSkinNode_: cc.Node = null // path://root_n/shop_sv/view/content/pawn_skin_n\n\tprivate newHeadNode_: cc.Node = null // path://root_n/shop_sv/view/content/new_head_n\n\tprivate classicalHeadNode_: cc.Node = null // path://root_n/shop_sv/view/content/classical_head_n\n\tprivate newEmojiNode_: cc.Node = null // path://root_n/shop_sv/view/content/new_emoji_n\n\tprivate classicalEmojiNode_: cc.Node = null // path://root_n/shop_sv/view/content/classical_emoji_n\n\tprivate skinTabsNode_: cc.Node = null // path://root_n/skin_tabs_n\n\tprivate socialTabsNode_: cc.Node = null // path://root_n/social_tabs_n\n\tprivate tabsTc_: cc.ToggleContainer = null // path://root_n/tabs_tc_tce\n\t//@end\n\n\t// 推荐列表 type:对应配置表，id:对应配置表相应id\n\tprivate readonly RECOMMENDED_LIST = [\n\t\t{ type: 'head', id: 206 },\n\t\t{ type: 'city', id: 1001111 },\n\t\t{ type: 'pawn', id: 3103105 },\n\t\t{ type: 'emoji', id: 2040 },\n\t\t{ type: 'head', id: 190 },\n\t\t{ type: 'city', id: 1001107 },\n\t\t{ type: 'pawn', id: 3202101 },\n\t\t{ type: 'emoji', id: 2050 },\n\t\t{ type: 'head', id: 187 },\n\t]\n\n\tprivate readonly PKEY_TAB: string = 'SHOP_TAB'\n\n\tprivate goldValLbl: cc.LabelRollNumber = null\n\tprivate ingotValLbl: cc.LabelRollNumber = null\n\n\tprivate user: UserModel = null\n\tprivate curTab: number = 0\n\tprivate showType: string = ''\n\tprivate mysteryBoxPrefab: cc.Prefab = null\n\tprivate preViewHeight: number = 904\n\n\tpublic listenEventMaps() {\n\t\treturn [\n\t\t\t{ [EventType.UPDATE_GOLD]: this.onUpdateGold, enter: true },\n\t\t\t{ [EventType.UPDATE_INGOT]: this.onUpdateIngot, enter: true },\n\t\t\t{ [EventType.INIT_PAY_FINISH]: this.onInitPayFinish, enter: true },\n\t\t\t{ [EventType.UPDATE_SUBSCRIPTION]: this.onUpdateSubscripion, enter: true },\n\t\t\t{ [EventType.UPDATE_RECHARGE_COUNT]: this.onUpdateRechargeCount, enter: true },\n\t\t\t{ [EventType.UPDATE_MYSTERYBOX]: this.onUpdateMysteryBox, enter: true },\n\t\t]\n\t}\n\n\tpublic async onCreate() {\n\t\tthis.setParam({ isAct: false })\n\t\tthis.user = this.getModel('user')\n\t\tthis.loadMysteryBoxPrefab()\n\t\t// this.restoreBuyNode_.active = !gameHpr.isNoviceMode && (cc.sys.isBrowser || !gameHpr.isRelease)\n\t\tthis.optionalHeroNode_.Child('button/money/val', cc.Label).string = BUY_OPT_HERO_COST + ''\n\t\tconst throttleUpdate = this.throttle(event => this.onScrolling(event), 200)\n\t\tthis.shopSv_.node.on('scrolling', throttleUpdate, this)\n\t}\n\n\tpublic onEnter(type?: number, showType?: string) {\n\t\tif (typeof (type) === 'string') {\n\t\t\ttype = Number(type)\n\t\t}\n\t\tthis.curTab = type ?? this.user.getTempPreferenceMap(this.PKEY_TAB) ?? 0\n\t\tthis.showType = showType\n\t\tthis.restoreBuyNode_.active = ut.isIos() && localConfig.RELEASE && !gameHpr.isRelease // 苹果审核才显示\n\t\tthis.drawCount = this.frameCount = 0\n\t\tthis.initTop()\n\t\tthis.checkShowNotFinishOrder()\n\t\tthis.updateShopIngot()\n\t\t// this.updateRecommend()\n\t\t// this.updateMonthlyCard()\n\t\tthis.updateSkin()\n\t\t// this.updateHeadIcon()\n\t\t// this.updateChatEmoji()\n\t\tthis.tabsTc_.Tabs(this.curTab)\n\t\tthis.emit(EventType.HIDE_TOP_NODE, false)\n\t\tthis.playRootAnimation()\n\t}\n\n\tpublic onRemove() {\n\t\t// this.topNode_.active = false\n\t\tthis.emit(EventType.HIDE_TOP_NODE, true)\n\t}\n\n\tpublic onClean() {\n\t\tthis.drawCount = this.frameCount = 0\n\t\tassetsMgr.releaseTempResByTag(this.key)\n\t}\n\n\t// ----------------------------------------- button listener function -------------------------------------------\n\t//@autocode button listener\n\n\t// path://root_n/tabs_tc_tce\n\tonClickTabs(event: cc.Toggle, data: string) {\n\t\t!data && audioMgr.playSFX('click')\n\t\tconst type = this.curTab = Number(event.node.name)\n\t\tthis.user.setTempPreferenceData(this.PKEY_TAB, type)\n\t\tlet y = 0\n\t\tif (type === 0) { // 元宝位置 0\n\t\t\tif (this.showType === 'ingot') {\n\t\t\t\ty = this.ingotNode_.y\n\t\t\t}\n\t\t\tthis.skinTabsNode_.active = this.socialTabsNode_.active = false\n\t\t} else if (type === 1) { // 月卡位置 1\n\t\t\ty = this.cardNode_.y\n\t\t\tthis.skinTabsNode_.active = this.socialTabsNode_.active = false\n\t\t} else if (type === 2) { // 皮肤位置 2\n\t\t\tthis.skinTabsNode_.active = true\n\t\t\tthis.socialTabsNode_.active = false\n\t\t\ty = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y\n\t\t} else if (type === 3) { // 新款头像位置 3\n\t\t\tthis.skinTabsNode_.active = false\n\t\t\tthis.socialTabsNode_.active = true\n\t\t\ty = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y\n\t\t}\n\t\tthis.shopSv_.stopAutoScroll()\n\t\tthis.shopSv_.content.y = Math.abs(y)\n\t}\n\n\t// path://root_n/skin_tabs_n/skin_tabs_nbe\n\tonClickSkinTabs(event: cc.Event.EventTouch, data: string) {\n\t\tconst name = event.target.name\n\t\tlet y = 0\n\t\tif (name === '0') { // 跳转限定\n\t\t\ty = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y\n\t\t} else if (name === '1') { // 跳转主城\n\t\t\ty = this.citySkinNode_.y\n\t\t} else if (name === '2') { // 跳转士兵\n\t\t\ty = this.pawnSkinNode_.y\n\t\t}\n\t\tthis.shopSv_.stopAutoScroll()\n\t\tthis.shopSv_.content.y = Math.abs(y)\n\t}\n\n\t// path://root_n/social_tabs_n/social_tabs_nbe\n\tonClickSocialTabs(event: cc.Event.EventTouch, data: string) {\n\t\tconst name = event.target.name\n\t\tlet y = 0\n\t\tif (name === '0') { // 跳转头像\n\t\t\ty = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y\n\t\t} else if (name === '1') { // 跳转表情\n\t\t\ty = this.newEmojiNode_.active ? this.newEmojiNode_.y : this.classicalEmojiNode_.y\n\t\t}\n\t\tthis.shopSv_.stopAutoScroll()\n\t\tthis.shopSv_.content.y = Math.abs(y)\n\t}\n\n\t// path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n\n\tonClickFreeGold(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tif (!this.user.isBuyLimitFreeGold()) {\n\t\t\treturn viewHelper.showAlert('ui.yet_buy_day')\n\t\t}\n\t\tthis.user.buyFreeGold().then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tthis.updateFreeGold()\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t})\n\t}\n\n\t// path://root_n/shop_sv/view/content/exchange/list/buy_gold_be\n\tonClickBuyGold(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tviewHelper.showPnl('common/ShopBuyGoldTip')\n\t}\n\n\t// path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n\n\tonClickOptionalHero(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tif (!this.user.isBuyLimitOptionalHero()) {\n\t\t\treturn viewHelper.showAlert('ui.yet_buy_week')\n\t\t}\n\t\tconst list = assetsMgr.getJson('portrayalBase').datas.map(m => new PortrayalInfo().init(m.id, m))\n\t\tviewHelper.showPnl('common/SelectPortrayal', SelectPortrayalType.BUY, list, (arr: PortrayalInfo[]) => {\n\t\t\tif (this.isValid && arr.length > 0) {\n\t\t\t\tthis.buyHero(arr[0].id)\n\t\t\t}\n\t\t}, 3)\n\t}\n\n\t// path://root_n/shop_sv/view/content/ingot_n/title/pay_not_arrived_be\n\tonClickPayNotArrived(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showLoadingWait(true)\n\t\tthis.checkShowNotFinishOrder(true).then(() => viewHelper.showLoadingWait(false))\n\t}\n\n\t// path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n\n\tonClickRestoreBuy(event: cc.Event.EventTouch, data: string) {\n\t\tpayHelper.restoreBuySub()\n\t}\n\n\t// path://root_n/shop_sv/view/content/ingot_n/list/ingot_be\n\tonClickIngot(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tif (this.user.isGuest()) {\n\t\t\treturn viewHelper.showMessageBox('ui.guest_buy_ingot_tip', {\n\t\t\t\tokText: 'ui.button_bind_account',\n\t\t\t\tok: () => viewHelper.showPnl('common/BindAccount'),\n\t\t\t\tcancel: () => { }\n\t\t\t})\n\t\t} else if (!payHelper.isInitFinish()) {\n\t\t\treturn viewHelper.showAlert('toast.please_wait_init_pay')\n\t\t}\n\t\tconst json = event.target.Data\n\t\tviewHelper.showPnl('common/ShopBuyIngotTip', json, (ok: boolean) => {\n\t\t\tif (ok) {\n\t\t\t\tpayHelper.buyProduct(json.product_id).then((suc: boolean) => {\n\t\t\t\t\tlogger.print('6.buyProduct end. suc=' + suc + ', isValid=' + this.isValid)\n\t\t\t\t\tif (suc && this.isValid) {\n\t\t\t\t\t\tthis.updateShopIngot()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t}\n\n\t// path://root_n/shop_sv/view/content/recommend/recommend_sv/view/content/buy_be@recommend\n\tonClickBuy(event: cc.Event.EventTouch, param: string) {\n\t\tlet data = event.target.Data\n\t\tif (!data) {\n\t\t\treturn\n\t\t}\n\t\tlet buyType = ''\n\t\tif (param === 'recommend') { // 推荐\n\t\t\tbuyType = data.type\n\t\t\tdata = data.json\n\t\t}\n\t\tif (param === 'city' || buyType === 'city' || param === 'pawn' || buyType === 'pawn') { // 士兵、城市皮肤\n\t\t\tconst type = param === 'city' || buyType === 'city' ? 'city_skin' : 'pawn_skin'\n\t\t\tviewHelper.showPnl('menu/CollectionSkinInfo', { type, list: [data] }, (ret) => {\n\t\t\t\tif (!this.isValid || !ret) {\n\t\t\t\t\treturn\n\t\t\t\t} else if (gameHpr.costDeductTip(ret)) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (type === 'pawn_skin') {\n\t\t\t\t\tthis.buyPawnSkin(ret.id)\n\t\t\t\t} else if (type === 'city_skin') {\n\t\t\t\t\tthis.buyCitySkin(ret.id)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (param === 'head' || buyType === 'head' || param === 'emoji' || buyType === 'emoji') { // 头像\n\t\t\tconst type = param === 'head' || buyType === 'head' ? 'headicon' : 'chat_emoji'\n\t\t\tviewHelper.showPnl('menu/CollectionEmojiInfo', { type: type, list: [data] }, (ret) => {\n\t\t\t\tif (!this.isValid || !ret) {\n\t\t\t\t\treturn\n\t\t\t\t} else if (gameHpr.costDeductTip(ret)) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (type === 'headicon') {\n\t\t\t\t\tthis.buyHeadIcon(ret.id)\n\t\t\t\t} else if (type === 'chat_emoji') {\n\t\t\t\t\tthis.buyChatEmoji(ret.id)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\t// path://root_n/shop_sv/view/content/card_n/list/card_0/sale_card_nbe\n\tonClickSaleCard(event: cc.Event.EventTouch, _data: string) {\n\t\tconst name = event.target.name, data = event.target.Data\n\t\tif (name === '0') { // 订阅\n\t\t\tif (data && data.leftDays === 0 && data.surplusTime > 0) {\n\t\t\t\tviewHelper.showAlert('toast.subscription_not_end')\n\t\t\t} else {\n\t\t\t\tviewHelper.showPnl('common/SubscriptionDesc', MonthlyCardType.SALE)\n\t\t\t}\n\t\t} else if (name === '1') { // 领取\n\t\t\tthis.getMonthlyCardAward(MonthlyCardType.SALE)\n\t\t} else if (name === '2') { // 已领取\n\n\t\t}\n\t}\n\n\t// path://root_n/shop_sv/view/content/card_n/list/card_1/super_card_nbe\n\tonClickSuperCard(event: cc.Event.EventTouch, _data: string) {\n\t\tconst name = event.target.name, data = event.target.Data\n\t\tif (name === '0') { // 订阅\n\t\t\tif (data && data.leftDays === 0 && data.surplusTime > 0) {\n\t\t\t\tviewHelper.showAlert('toast.subscription_not_end')\n\t\t\t} else {\n\t\t\t\tviewHelper.showPnl('common/SubscriptionDesc', MonthlyCardType.SUPER)\n\t\t\t}\n\t\t} else if (name === '1') { // 领取\n\t\t\tthis.getMonthlyCardAward(MonthlyCardType.SUPER)\n\t\t} else if (name === '2') { // 已领取\n\n\t\t}\n\t}\n\t//@end\n\t// ----------------------------------------- event listener function --------------------------------------------\n\n\t// 刷新元宝\n\tprivate onUpdateIngot() {\n\t\tconst ingot = this.user.getIngot()\n\t\tthis.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49')\n\t\tthis.ingotValLbl.to(ingot)\n\t}\n\n\t// 刷新元宝\n\tprivate onUpdateGold() {\n\t\tconst gold = this.user.getGold()\n\t\tthis.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49')\n\t\tthis.goldValLbl.to(gold)\n\t}\n\n\t// 初始化支付完成 刷新下金币\n\tprivate onInitPayFinish() {\n\t\tthis.updateShopIngot()\n\t\tthis.checkShowNotFinishOrder()\n\t}\n\n\t// 刷新订阅信息\n\tprivate onUpdateSubscripion() {\n\t\tconsole.log('onUpdateSubscripion has=' + this.user.isHasSubscription())\n\t\tthis.updateMonthlyCard()\n\t}\n\n\t// 刷新充值次数\n\tprivate onUpdateRechargeCount() {\n\t\tthis.updateShopIngot()\n\t}\n\n\t// 刷新盲盒\n\tprivate onUpdateMysteryBox() {\n\t\tthis.updateMysteryBox(this.mysteryBoxNode_.children[0])\n\t}\n\n\t// ----------------------------------------- custom function ----------------------------------------------------\n\n\tprivate initTop() {\n\t\tconst node = this.topNode_.Swih(mc.currWindName === 'lobby' ? 'lobby' : 'main')[0]\n\t\tthis.goldValLbl = node.Child('gold/val', cc.LabelRollNumber)\n\t\tthis.ingotValLbl = node.Child('ingot/val', cc.LabelRollNumber)\n\t\tconst gold = this.user.getGold(), ingot = this.user.getIngot()\n\t\tthis.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49')\n\t\tthis.goldValLbl.set(gold)\n\t\tthis.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49')\n\t\tthis.ingotValLbl.set(ingot)\n\t}\n\n\t// \n\tprivate playRootAnimation() {\n\t\tthis.rootNode_.stopAllActions()\n\t\tthis.rootNode_.scale = 0.4\n\t\tcc.tween(this.rootNode_).to(0.25, { scale: 1 }, { easing: cc.easing.backOut }).start()\n\t}\n\n\t// 检测未完成的订单\n\tprivate async checkShowNotFinishOrder(showToast?: boolean) {\n\t\tif (!payHelper.isInitFinish()) {\n\t\t\tawait payHelper.checkPayInit()\n\t\t}\n\t\t// 是否有未完成的订单\n\t\tconst ok = await this.user.checkHasNotFinishOrder(payHelper.getPlatform())\n\t\tif (ok) {\n\t\t\tviewHelper.showPnl('main/NotFinishOrderTip')\n\t\t} else if (!showToast) {\n\t\t} else if (!payHelper.isInitFinish()) {\n\t\t\tviewHelper.showAlert('toast.please_wait_init_pay')\n\t\t} else {\n\t\t\tviewHelper.showAlert('toast.no_check_not_finish_order')\n\t\t}\n\t}\n\n\t// 刷新元宝\n\tprivate updateShopIngot() {\n\t\tconst isFinish = payHelper.isInitFinish()\n\t\tconst list = assetsMgr.getJson('recharge').datas.filter(m => !!m.ignore)\n\t\tconst rechargeCountRecord = this.user.getRechargeCountRecord()\n\t\tthis.ingotNode_.Child('list').Items(list, (it, json) => {\n\t\t\tit.Data = json\n\t\t\tresHelper.loadIcon(json.icon, it.Child('icon/val'), this.key)\n\t\t\tit.Child('name').setLocaleKey('ui.shop_ingot_name', json.ingot)\n\t\t\tconst button = it.Child('button')\n\t\t\tif (button.Child('money').active = isFinish) {\n\t\t\t\tbutton.Child('money/val', cc.Label).string = payHelper.getProductPriceText(json.product_id)\n\t\t\t}\n\t\t\tconst extra = it.Child('mask/extra')\n\t\t\tif (!rechargeCountRecord[json.product_id]) { //首次\n\t\t\t\textra.active = true\n\t\t\t\textra.Child('val').setLocaleKey('ui.first_give_ingot', json.ingot)\n\t\t\t} else if (extra.active = !!json.extra) {\n\t\t\t\textra.Child('val').setLocaleKey('ui.give_ingot', json.extra)\n\t\t\t}\n\t\t\tbutton.Child('loading').active = !isFinish\n\t\t})\n\t\t// 免费\n\t\tthis.updateFreeGold()\n\t\t// 自选\n\t\tthis.updateOptionalHero()\n\t\t//\n\t\tthis.onUpdateIngot()\n\t\tthis.onUpdateGold()\n\t}\n\n\t// 刷新推荐\n\tprivate updateRecommend() {\n\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n\t\tconst list = []\n\t\t// 先做一次筛选，拥有的就不显示了\n\t\tfor (let i = 0; i < this.RECOMMENDED_LIST.length; i++) {\n\t\t\tlet arr = []\n\t\t\tconst data = this.RECOMMENDED_LIST[i]\n\t\t\tif (data.type === 'city') {\n\t\t\t\tarr = this.user.getCanBuyCitySkins(serverArea)\n\t\t\t} else if (data.type === 'pawn') {\n\t\t\t\tarr = this.user.getCanBuyPawnSkins(serverArea)\n\t\t\t} else if (data.type === 'head') {\n\t\t\t\tarr = this.user.getCanBuyHeadIcons(serverArea)\n\t\t\t} else if (data.type === 'emoji') {\n\t\t\t\tarr = this.user.getCanBuyChatEmojis(serverArea)\n\t\t\t}\n\t\t\tif (arr.some(m => m.id === data.id)) {\n\t\t\t\tlist.push(data)\n\t\t\t}\n\t\t}\n\t\tthis.recommendSv_.stopAutoScroll()\n\t\tthis.recommendSv_.content.y = 0\n\t\tthis.recommendSv_.Items(list, (it, data: any) => {\n\t\t\tconst isPawnOrCity = data.type === 'city' || data.type === 'pawn'\n\t\t\tit.Child('icon', cc.Sprite).enabled = isPawnOrCity\n\t\t\tconst icon = it.Child('icon').Swih(isPawnOrCity ? '1' : '0')[0]\n\t\t\ticon.scale = 1\n\t\t\tlet json = null\n\t\t\tif (data.type === 'pawn') {\n\t\t\t\tjson = assetsMgr.getJsonData('pawnSkin', data.id)\n\t\t\t\tresHelper.loadPawnHeadIcon(json.id, icon, this.key)\n\t\t\t} else if (data.type === 'city') {\n\t\t\t\ticon.scale = 0.65\n\t\t\t\tjson = assetsMgr.getJsonData('citySkin', data.id)\n\t\t\t\tresHelper.loadCityIcon(json.id, icon, this.key)\n\t\t\t} else if (data.type === 'head') {\n\t\t\t\tjson = assetsMgr.getJsonData('headIcon', data.id)\n\t\t\t\tresHelper.loadPlayerHead(icon, json.icon, this.key, true)\n\t\t\t} else if (data.type === 'emoji') {\n\t\t\t\tjson = assetsMgr.getJsonData('chatEmoji', data.id)\n\t\t\t\tresHelper.loadEmojiIcon(json.id, icon, this.key)\n\t\t\t}\n\t\t\tit.Data = { type: data.type, json }\n\t\t\tthis.updateCostText(it, json)\n\t\t\tconst isNew = gameHpr.checkShopNewProduct(json)\n\t\t\tit.Child('new').active = isNew\n\t\t\tit.Color(isNew ? '#FAEDCD' : '#F1E8D3')\n\t\t\tit.Child('mask/extra', cc.Sprite).enabled = isNew\n\t\t})\n\t}\n\n\t// 刷新月卡\n\tprivate updateMonthlyCard() {\n\t\tconst node = this.cardNode_.Child('list')\n\t\tconst subDatas = this.user.getSubDatas()\n\t\tfor (let i = 0; i < node.childrenCount; i++) {\n\t\t\tconst data = MONTH_CARD[i], item = node.Child('card_' + i)\n\t\t\tconst subData = subDatas.find(m => data.PRODUCT_IDS_ANDROID.includes(m.productId)) || subDatas.find(m => data.PRODUCT_IDS_IOS.includes(m.productId)) || subDatas.find(m => data.RECHARGES.includes(m.productId))\n\t\t\tconst isBuy = !!subData // 是否已购买\n\t\t\tconst canClaim = isBuy ? (subData.leftDays > 0 && subData.lastAwardTime <= 0) : false //是否可领取\n\t\t\tconst imm = item.Child('imm'), isFirstPay = !this.user.getRechargeCountRecord()[data.TYPE]\n\t\t\timm.Child('mask').active = isFirstPay\n\t\t\timm.Child('count', cc.Label).string = 'x' + data.FIRST\n\t\t\timm.Child('mask/extra/val').setLocaleKey('ui.first_give_ingot', data.FIRST)\n\t\t\tconst isDone = isBuy && !canClaim\n\t\t\timm.Child('done').active = isBuy\n\t\t\timm.Child('icon').opacity = isBuy ? 150 : 255\n\t\t\tconst day = item.Child('day')\n\t\t\tday.Child('done').active = isDone\n\t\t\tconst tipNode = item.Child('tip').Swih(isBuy ? 'surplus' : 'total')[0]\n\t\t\tif (isBuy) {\n\t\t\t\ttipNode.Child('val', cc.Label).string = subData.leftDays + ''\n\t\t\t} else {\n\t\t\t\tlet total = data.FIRST * 2 + data.DAY * data.DURATION\n\t\t\t\ttipNode.Child('g_count', cc.Label).string = total + ''\n\t\t\t\tconst wartoken = tipNode.Child('wartoken'), wtCount = tipNode.Child('wt_count', cc.Label)\n\t\t\t\tif (wartoken.active = wtCount.node.active = !!data.EXTRA) {\n\t\t\t\t\twtCount.string = data.EXTRA * data.DURATION + ''\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst notEnd = subData?.leftDays === 0 && subData?.surplusTime > 0, showBuy = !subData || notEnd\n\t\t\tif (i === 0) {\n\t\t\t\tday.Child('icon').opacity = isDone ? 150 : 255\n\t\t\t\tday.Child('count', cc.Label).string = 'x' + data.DAY\n\t\t\t\tconst button = item.Child('sale_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0] // 0:购买；1：领取；2：已领取\n\t\t\t\tbutton.Data = subData\n\t\t\t\tbutton.opacity = notEnd ? 120 : 255 // 天数已尽，但没有到期\n\t\t\t} else {\n\t\t\t\tday.Child('icon').children.forEach(m => m.opacity = isDone ? 150 : 255)\n\t\t\t\tday.Child('count').children.forEach((m, i) => {\n\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\tm.Component(cc.Label).string = 'x' + data.DAY\n\t\t\t\t\t} else {\n\t\t\t\t\t\tm.Component(cc.Label).string = 'x' + data.EXTRA\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tconst button = item.Child('super_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0] // 0:购买；1：领取；2：已领取\n\t\t\t\tbutton.Data = subData\n\t\t\t\tbutton.opacity = notEnd ? 120 : 255 // 天数已尽，但没有到期\n\t\t\t}\n\t\t}\n\t}\n\n\t// 刷新皮肤\n\tprivate async updateSkin() {\n\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n\t\t// 主城皮肤\n\t\tconst citySkinList = this.updateCitySkin(serverArea)\n\t\t// 士兵皮肤\n\t\tconst list = this.user.getCanBuyPawnSkins(serverArea)\n\t\tconst arr1 = [], arr2 = [], arr3 = []\n\t\tlet mysteryBoxId = 0\n\t\tlist.forEach(m => {\n\t\t\tif (m.cond > 100) {\n\t\t\t\tarr3.push(m)\n\t\t\t\tmysteryBoxId = m.cond\n\t\t\t} else if (m['limit_time_' + serverArea] && m.cond === 4) {\n\t\t\t\tarr2.push(m)\n\t\t\t} else {\n\t\t\t\tarr1.push(m)\n\t\t\t}\n\t\t})\n\t\tarr1.sort((a, b) => {\n\t\t\tlet aw = 0, bw = 0\n\t\t\taw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)\n\t\t\tbw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\tarr2.sort((a, b) => {\n\t\t\tlet aw = 0, bw = 0\n\t\t\taw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)\n\t\t\tbw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\t// 限定皮肤(目前只计划士兵皮肤)\n\t\tif (this.limitedSkinNode_.active = this.skinTabsNode_.Child('skin_tabs_nbe/0').active = arr2.length > 0) {\n\t\t\tthis.updatePawnSkinList(this.limitedSkinNode_, arr2, true)\n\t\t\tconst timeNode = this.limitedSkinNode_.Child('title/lay/time'), json = arr2[0]\n\t\t\tconst [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')\n\t\t\ttimeNode.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))\n\t\t}\n\t\t// 士兵皮肤\n\t\tthis.updatePawnSkinList(this.pawnSkinNode_, arr1, false)\n\t\t// 盲盒限定\n\t\tif (this.mysteryBoxNode_.active = arr3.length > 0) {\n\t\t\tawait this.loadMysteryBoxPrefab(mysteryBoxId)\n\t\t\tthis.initMysterBox(serverArea, arr3)\n\t\t}\n\t}\n\n\t// 加载盲盒预制\n\tprivate async loadMysteryBoxPrefab(id?: number) {\n\t\t// 当前开放的盲盒id\n\t\tif (!id) {\n\t\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n\t\t\tassetsMgr.getJson('pawnSkin').datas.forEach(m => {\n\t\t\t\tif (m.cond < 100) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tconst [startTime, endTime] = (m['limit_time_' + serverArea] || m.limit_time_hk).split('|')\n\t\t\t\tif (startTime && endTime && !gameHpr.checkActivityAutoDate(startTime, endTime)) {\n\t\t\t\t\treturn //是否有时间限制\n\t\t\t\t}\n\t\t\t\tid = m.cond\n\t\t\t})\n\t\t}\n\t\tif (!this.mysteryBoxPrefab) {\n\t\t\tthis.mysteryBoxPrefab = await assetsMgr.loadTempRes('mysterybox/MYSTERYBOX_' + id, cc.Prefab, this.key)\n\t\t}\n\t\treturn this.mysteryBoxPrefab\n\t}\n\n\tprivate initMysterBox(serverArea: string, arr: any[]) {\n\t\tlet mysteryboxNode = null\n\t\tif (this.mysteryBoxNode_.childrenCount > 1) {\n\t\t\tmysteryboxNode = this.mysteryBoxNode_.children[0]\n\t\t} else {\n\t\t\tmysteryboxNode = cc.instantiate2(this.mysteryBoxPrefab, this.mysteryBoxNode_)\n\t\t}\n\t\tmysteryboxNode.zIndex = -1\n\t\tthis.mysteryBoxNode_.Swih(mysteryboxNode.name)[0]\n\t\tconst json = mysteryboxNode.Data = arr[0]\n\t\tconst [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')\n\t\tconst timeNode = mysteryboxNode.Child('time'), timeVal = timeNode.Child('val')\n\t\ttimeVal.setLocaleKey('ui.mysterybox_limit_time', endTime.split('-').slice(0, 3).join('/'))\n\t\tthis.updateMysteryBox(mysteryboxNode, serverArea)\n\n\t\tthis.addClickEvent(mysteryboxNode.Child('mysterybox_be', cc.Button), 'onClickMysterybox')\n\t\tthis.addClickEvent(mysteryboxNode.Child('mysterybox_rule_be', cc.Button), 'onClickMysteryboxRule')\n\t\tthis.addClickEvent(mysteryboxNode.Child('skin_exchange_be', cc.Button), 'onClickSkinExchange')\n\n\t\tif (!storageMgr.loadBool('click_MysteryBox_tab' + json.cond)) {\n\t\t\treddotHelper.set('mystery_box', false)\n\t\t\tstorageMgr.saveBool('click_MysteryBox_tab' + json.cond, true)\n\t\t}\n\t}\n\n\tprivate updateMysteryBox(node: cc.Node, serverArea?: string) {\n\t\tserverArea = serverArea || (gameHpr.isRelease ? gameHpr.getServerArea() : 'test')\n\t\tconst exchangeSv = node.Child('exchange_sv', cc.ScrollView), colorSkins = this.getColorSkin(serverArea)\n\t\texchangeSv.Items(colorSkins, (it, data) => {\n\t\t\tit.Data = data.id\n\t\t\tconst icon = it.Child('icon/val'), mask = it.Child('icon/mask')\n\t\t\tresHelper.loadPawnHeadIcon(data.id, icon, this.key)\n\t\t\tresHelper.loadPawnHeadIcon(data.id, mask, this.key)\n\t\t\tconst [needId, needCount] = ut.stringToNumbers(data.value, ',')\n\t\t\tconst hasCount = this.user.getSkinItemList().filter(m => m.id === needId)?.length || 0\n\t\t\tmask.Component(cc.Sprite).fillRange = Math.min(1, 1 - hasCount / needCount)\n\t\t\tthis.addClickEvent(it.Component(cc.Button), 'onClickSkinExchange')\n\t\t})\n\t}\n\n\tprivate getColorSkin(serverArea: string) {\n\t\tconst unlockMap = {}\n\t\tthis.user.getUnlockPawnSkinIds().forEach(m => unlockMap[m] = true)\n\t\treturn assetsMgr.getJson('pawnSkin').datas.filter(m => {\n\t\t\tif (!unlockMap[m.id] && m.cond === 5) {\n\t\t\t\tconst [startTime, endTime] = (m['limit_time_' + serverArea] || m.limit_time_hk).split('|')\n\t\t\t\tif (startTime && endTime && gameHpr.checkActivityAutoDate(startTime, endTime)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false\n\t\t})\n\t}\n\n\tprivate updateCitySkin(serverArea: string) {\n\t\tconst list = this.user.getCanBuyCitySkins(serverArea)\n\t\tconst arr1 = [], arr2 = []\n\t\tlist.forEach(m => {\n\t\t\t// if (m['limit_time_' + serverArea]) {\n\t\t\t// \tarr2.push(m)\n\t\t\t// } else {\n\t\t\tarr1.push(m)\n\t\t\t// }\n\t\t})\n\t\tarr1.sort((a, b) => {\n\t\t\tlet aw = 0, bw = 0\n\t\t\taw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)\n\t\t\tbw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\t// arr2.sort((a, b) => {\n\t\t// \tlet aw = 0, bw = 0\n\t\t// \taw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)\n\t\t// \tbw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)\n\t\t// \treturn bw - aw\n\t\t// })\n\t\tthis.updateCitySkinList(this.citySkinNode_, arr1)\n\t\t// // 季节限定\n\t\t// const limitedNode = this.citySkinNode_.Child('limited')\n\t\t// if (limitedNode.active = arr2.length > 0) {\n\t\t// \tthis.updateCitySkinList(limitedNode.Child('list'), arr2)\n\t\t// \tconst timeNode = limitedNode.Child('time'), timeLbl = timeNode.Child('val', cc.Label), json = arr2[0]\n\t\t// \tconst [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')\n\t\t// \ttimeLbl.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))\n\t\t// \ttimeLbl._forceUpdateRenderData()\n\t\t// \tconst w = (timeNode.width - timeLbl.node.width - 16) / 2\n\t\t// \ttimeNode.Child('0').width = timeNode.Child('1').width = w\n\t\t// }\n\t\treturn list\n\t}\n\n\tprivate updateCitySkinList(node: cc.Node, list: any[]) {\n\t\tlet node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]\n\t\tif (node_.name === 'list') {\n\t\t\tnode_.Items(list, (it, json) => {\n\t\t\t\tit.Data = json\n\t\t\t\tresHelper.loadCityIcon(json.id, it.Child('icon/val'), this.key)\n\t\t\t\tthis.updateCostText(it, json)\n\t\t\t\tconst isNew = gameHpr.checkShopNewProduct(json)\n\t\t\t\tit.Child('new').active = isNew\n\t\t\t\tit.Color(isNew ? '#FAEDCD' : '#F1E8D3')\n\t\t\t\tit.Child('mask/extra', cc.Sprite).enabled = isNew\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate updatePawnSkinList(node: cc.Node, list: any[], isLimited: boolean) {\n\t\tlet node_ = null\n\t\tif (isLimited) {\n\t\t\tnode_ = node.Child('list')\n\t\t} else {\n\t\t\tnode_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]\n\t\t}\n\t\tif (node_.name === 'list') {\n\t\t\tnode_.Items(list, (it, json) => {\n\t\t\t\tit.Data = json\n\t\t\t\tit.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.pawn_id), 5))\n\t\t\t\tthis.updateCostText(it, json)\n\t\t\t\tconst isNew = gameHpr.checkShopNewProduct(json)\n\t\t\t\tit.Child('new')?.setActive(isNew)\n\t\t\t\tit.Color(isNew ? '#FAEDCD' : '#F1E8D3')\n\t\t\t\tit.Child('mask/extra', cc.Sprite).enabled = isNew\n\t\t\t\tresHelper.loadPawnHeadIcon(json.id, it.Child('icon/val'), this.key)\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate updateCostText(it: cc.Node, json: any) {\n\t\tconst node = it.Child('mask/extra')\n\t\tif (json.gold > 0) {\n\t\t\tnode.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.GOLD)\n\t\t\tnode.Child('val', cc.Label).string = json.gold + ''\n\t\t} else if (json.ingot > 0) {\n\t\t\tnode.Child('icon', cc.Sprite).spriteFrame = resHelper.getResIcon(CType.INGOT)\n\t\t\tnode.Child('val', cc.Label).string = json.ingot + ''\n\t\t} else {\n\t\t\tnode.Child('icon').active = false\n\t\t}\n\t}\n\n\tprivate getEndDate(endTime: string, startTime: string) {\n\t\tlet year = new Date().getFullYear()\n\t\tif ((startTime?.split('-')[0] || '').length !== 4) {\n\t\t\tstartTime = year + '-' + startTime\n\t\t}\n\t\tif ((endTime?.split('-')[0] || '').length !== 4) {\n\t\t\tlet [_1, sm] = startTime.split('-')\n\t\t\tlet em = endTime.split('-')[0]\n\t\t\tif (Number(em) < Number(sm)) {\n\t\t\t\tyear += 1\n\t\t\t}\n\t\t\tendTime = year + '-' + endTime\n\t\t}\n\t\tlet [endYear, endMonth, endDay] = endTime?.split('-')\n\t\treturn assetsMgr.lang('ui.date', endYear, endMonth, endDay)\n\t}\n\n\tprivate getAddNewTime(endTime: string) {\n\t\tlet [endYear, endMonth, endDay] = endTime?.split('-')\n\t\treturn endMonth + '/' + endDay\n\t}\n\n\tprivate buyCitySkin(id: number) {\n\t\tthis.user.buyCitySkin(id).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t\tthis.updateSkin()\n\t\t\t\tif (this.RECOMMENDED_LIST.some(m => m.type === 'city')) {\n\t\t\t\t\tthis.updateRecommend()\n\t\t\t\t}\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t})\n\t}\n\n\tprivate buyPawnSkin(id: number) {\n\t\tthis.user.buyPawnSkin(id).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t\tthis.updateSkin()\n\t\t\t\tif (this.RECOMMENDED_LIST.some(m => m.type === 'pawn')) {\n\t\t\t\t\tthis.updateRecommend()\n\t\t\t\t}\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t})\n\t}\n\n\t// 购买盲盒\n\tprivate buyMysterybox(json: any) {\n\t\tviewHelper.showMessageBox('ui.buy_mysterybox_tip', {\n\t\t\tparams: [json.ingot, json.desc],\n\t\t\tok: () => viewHelper.showPnl(`activity/MysteryboxShow${json.cond}`, json.cond, json.ingot),\n\t\t\tcancel: () => { },\n\t\t})\n\t}\n\n\t// 刷新头像\n\tprivate updateHeadIcon() {\n\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n\t\tconst list = this.user.getCanBuyHeadIcons(serverArea).sort((a, b) => {\n\t\t\tlet aw = 0, bw = 0\n\t\t\taw = a.id + (gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0)\n\t\t\tbw = b.id + (gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\t// 新款头像\n\t\tconst newHeadIcons = list.filter(m => !!m['limit_time_' + serverArea])\n\t\tlet date = ''\n\t\tif (newHeadIcons.length > 0) {\n\t\t\tconst [startTime, endTime] = (newHeadIcons[0]['limit_time_' + serverArea] || newHeadIcons[0].limit_time_hk).split('|')\n\t\t\tdate = this.getAddNewTime(startTime)\n\t\t}\n\t\tthis.updateHeadIconList(this.newHeadNode_, newHeadIcons, true, date)\n\n\t\t// 经典头像\n\t\tconst classicHeadIcons = list.filter(m => !m['limit_time_' + serverArea])\n\t\tthis.updateHeadIconList(this.classicalHeadNode_, classicHeadIcons, false, '')\n\t}\n\n\tprivate updateHeadIconList(node: cc.Node, list: any[], isNew: boolean, date: string) {\n\t\tlet node_ = null\n\t\tif (isNew) {\n\t\t\tnode.active = list.length > 0\n\t\t\tnode.Child('title/lay/lay/time', cc.Label).string = date\n\t\t\tnode_ = node.Child('list')\n\t\t} else {\n\t\t\tnode_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]\n\t\t}\n\t\tif (node_.active && node_.name === 'list') {\n\t\t\tnode_.Items(list, (it, json) => {\n\t\t\t\tit.Data = json\n\t\t\t\tresHelper.loadPlayerHead(it.Child('icon/val'), json.icon, this.key, true)\n\t\t\t\tthis.updateCostText(it, json)\n\t\t\t})\n\t\t}\n\t}\n\n\t// 购买头像\n\tprivate buyHeadIcon(id: number) {\n\t\tthis.user.buyHeadIcon(id).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t\tthis.updateHeadIcon()\n\t\t\t\tif (this.RECOMMENDED_LIST.some(m => m.type === 'head')) {\n\t\t\t\t\tthis.updateRecommend()\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n\n\t// 刷新表情\n\tprivate updateChatEmoji() {\n\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n\t\tconst list = this.user.getCanBuyChatEmojis(serverArea).sort((a, b) => {\n\t\t\tlet aw = 0, bw = 0\n\t\t\taw = a.id + (gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0)\n\t\t\tbw = b.id + (gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\t// 新款表情\n\t\tconst newEmojis = list.filter(m => !!m['limit_time_' + serverArea])\n\t\tlet date = ''\n\t\tif (newEmojis.length > 0) {\n\t\t\tconst [startTime, endTime] = (newEmojis[0]['limit_time_' + serverArea] || newEmojis[0].limit_time_hk).split('|')\n\t\t\tdate = this.getAddNewTime(startTime)\n\t\t}\n\t\tthis.updateChatEmojiList(this.newEmojiNode_, newEmojis, true, date)\n\n\t\t//经典表情\n\t\tconst classicEmojis = list.filter(m => !m['limit_time_' + serverArea])\n\t\tthis.updateChatEmojiList(this.classicalEmojiNode_, classicEmojis, false, '')\n\t}\n\n\tprivate updateChatEmojiList(node: cc.Node, list: any[], isNew: boolean, date: string) {\n\t\tlet node_ = null\n\t\tif (isNew) {\n\t\t\tnode.active = list.length > 0\n\t\t\tnode.Child('title/lay/lay/time', cc.Label).string = date\n\t\t\tnode_ = node.Child('list')\n\t\t} else {\n\t\t\tnode_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0]\n\t\t}\n\t\tif (node_.active && node_.name === 'list') {\n\t\t\tnode_.Items(list, (it, json) => {\n\t\t\t\tit.Data = json\n\t\t\t\tresHelper.loadEmojiIcon(json.id, it.Child('icon/val'), this.key)\n\t\t\t\tthis.updateCostText(it, json)\n\t\t\t})\n\t\t}\n\t}\n\n\tprivate buyChatEmoji(id: number) {\n\t\tthis.user.buyChatEmoji(id).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t\tthis.updateChatEmoji()\n\t\t\t\tif (this.RECOMMENDED_LIST.some(m => m.type === 'emoji')) {\n\t\t\t\t\tthis.updateRecommend()\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n\n\t// 购买英雄\n\tprivate buyHero(id: number) {\n\t\tthis.user.buyHero(id).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tthis.updateOptionalHero()\n\t\t\t\tviewHelper.showGainPortrayalDebris(id, 3)\n\t\t\t}\n\t\t})\n\t}\n\n\t// 刷新免费金币\n\tprivate updateFreeGold() {\n\t\tconst isCanBuy = this.user.isBuyLimitFreeGold()\n\t\tthis.freeGoldNode_.Child('button').opacity = isCanBuy ? 255 : 120\n\t\tthis.freeGoldNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_day' : 'ui.yet_buy_day')\n\t}\n\n\t// 刷新自选英雄\n\tprivate updateOptionalHero() {\n\t\tconst isCanBuy = this.user.isBuyLimitOptionalHero()\n\t\tthis.optionalHeroNode_.Child('button').opacity = isCanBuy ? 255 : 120\n\t\tthis.optionalHeroNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_week' : 'ui.yet_buy_week')\n\t}\n\n\t// 领取订阅的月卡奖励\n\tprivate getMonthlyCardAward(type: MonthlyCardType) {\n\t\tthis.user.reqGetMonthlyCardAward(type).then(err => {\n\t\t\tif (err) {\n\t\t\t\treturn viewHelper.showAlert(err)\n\t\t\t} else if (this.isValid) {\n\t\t\t\tthis.updateMonthlyCard()\n\t\t\t\tviewHelper.showAlert('toast.claim_succeed')\n\t\t\t}\n\t\t})\n\t}\n\n\tonClickMysterybox(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = event.target.parent\n\t\tconst json = node.Data\n\t\tif (json && json.cond > 100) {\n\t\t\tif (this.user.getIngot() < json.ingot) {\n\t\t\t\treturn viewHelper.showAlert(ecode.INGOT_NOT_ENOUGH)\n\t\t\t} else if (!gameHpr.isNoLongerTip(NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {\n\t\t\t\tviewHelper.showPnl('common/MysteryboxRule', json.cond, () => this.isValid && this.buyMysterybox(json))\n\t\t\t} else {\n\t\t\t\tthis.buyMysterybox(json)\n\t\t\t}\n\t\t}\n\t}\n\n\tonClickMysteryboxRule(event: cc.Event.EventTouch, data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst id = this.mysteryBoxNode_.children[0].Data.cond\n\t\tid && viewHelper.showPnl('common/MysteryboxRule', id)\n\t}\n\n\n\tonClickSkinExchange(event: cc.Event.EventTouch, _data: string) {\n\t\tconst data = event.target.Data\n\t\taudioMgr.playSFX('click')\n\t\tviewHelper.showPnl('common/SkinExchange', data)\n\t}\n\n\tprivate onScrolling(event: cc.ScrollView) {\n\t\tconst height = event.node.height / 2, content = event.content,\n\t\t\tskinY = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y,\n\t\t\tsocialY = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y,\n\t\t\tshowCard = content.y >= Math.abs(this.cardNode_.y + height) && content.y < Math.abs(skinY + height),\n\t\t\tshowSkin = content.y >= Math.abs(skinY + height) && content.y < Math.abs(socialY + height),\n\t\t\tshowSocial = content.y >= Math.abs(socialY + height)\n\t\tif (this.skinTabsNode_.active !== showSkin) {\n\t\t\tthis.skinTabsNode_.active = showSkin\n\t\t}\n\t\tif (this.socialTabsNode_.active !== showSocial) {\n\t\t\tthis.socialTabsNode_.active = showSocial\n\t\t}\n\t\tevent.Child('view').height = (showSkin || showSocial) ? this.preViewHeight - this.skinTabsNode_.height : this.preViewHeight\n\t\tconst tab = showCard ? 1 : showSkin ? 2 : showSocial ? 3 : 0\n\t\tif (this.curTab !== tab) {\n\t\t\tthis.curTab = tab\n\t\t\tthis.tabsTc_.Swih(tab)\n\t\t\tthis.user.setTempPreferenceData(this.PKEY_TAB, tab)\n\t\t}\n\t}\n\n\tprivate throttle(func: Function, delay: number) {\n\t\tlet timer = null\n\t\treturn function (...args) {\n\t\t\tif (!timer) {\n\t\t\t\tfunc.apply(this, args)\n\t\t\t\ttimer = setTimeout(() => {\n\t\t\t\t\ttimer = null\n\t\t\t\t}, delay)\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate drawCount: number = 0\n\tprivate drawRate: number = 10\n\tprivate frameCount: number = 0\n\n\tprivate funcList: Function[] = [\n\t\tthis.updateRecommend,\n\t\tthis.updateMonthlyCard,\n\t\tthis.updateHeadIcon,\n\t\tthis.updateChatEmoji,\n\t]\n\n\tupdate(dt: number) {\n\t\tif (this.drawCount < this.funcList.length) {\n\t\t\tthis.frameCount++\n\t\t\tif (this.frameCount > this.drawRate) {\n\t\t\t\tthis.frameCount = 0\n\t\t\t\tthis.funcList[this.drawCount].bind(this)()\n\t\t\t\tthis.drawCount++\n\t\t\t}\n\t\t}\n\t}\n}\n"]}