
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/SceneBuildObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '41d82KKgoRI2rIIP8aP1NRP', 'SceneBuildObj');
// app/script/model/snailisle/SceneBuildObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var MapHelper_1 = require("../../common/helper/MapHelper");
var ISceneMapObj_1 = require("./ISceneMapObj");
// 建筑设施
var SceneBuildObj = /** @class */ (function (_super) {
    __extends(SceneBuildObj, _super);
    function SceneBuildObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.id = 0;
        _this.json = null; //当前的json数据
        _this.rootPosition = null; //临时记录位置
        return _this;
    }
    SceneBuildObj.prototype.init = function (json) {
        this.uid = ut.UID();
        this.id = json.id;
        this.json = json;
        this.initGridJson();
        return this;
    };
    SceneBuildObj.prototype.initGridJson = function () {
        var json = this.json;
        this.point.set(ut.stringToVec2(json.point));
        this.points = MapHelper_1.mapHelper.genPointsBySize(ut.stringToVec2(json.size, 'x'), this.point);
    };
    Object.defineProperty(SceneBuildObj.prototype, "use_begin_anim", {
        get: function () { return this.json ? this.json.use_begin_anim : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SceneBuildObj.prototype, "use_run_anim", {
        get: function () { return this.json ? this.json.use_run_anim : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SceneBuildObj.prototype, "use_end_anim", {
        get: function () { return this.json ? this.json.use_end_anim : ''; },
        enumerable: false,
        configurable: true
    });
    // 获取含有站位的子设施
    SceneBuildObj.prototype.getHasPointsChilds = function () {
        return this.points.length > 0;
    };
    // 检测是否在站位中
    SceneBuildObj.prototype.checkInPoints = function (x, y) {
        return this.points.some(function (m) { return m.x === x && m.y === y; });
    };
    SceneBuildObj.prototype.getSceneType = function () { return ''; }; //设施是属于哪个场景的
    SceneBuildObj.prototype.getJsonName = function () { return ''; }; //配置表名字
    SceneBuildObj.prototype.getUrl = function () { return ''; }; //获取设施加载路径
    return SceneBuildObj;
}(ISceneMapObj_1.default));
exports.default = SceneBuildObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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