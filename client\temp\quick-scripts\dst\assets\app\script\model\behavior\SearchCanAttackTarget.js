
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/SearchCanAttackTarget.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '90e14uVt/RNJ4PxQ1IyUQqf', 'SearchCanAttackTarget');
// app/script/model/behavior/SearchCanAttackTarget.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 寻找周围可攻击的目标
var SearchCanAttackTarget = /** @class */ (function (_super) {
    __extends(SearchCanAttackTarget, _super);
    function SearchCanAttackTarget() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    SearchCanAttackTarget.prototype.onInit = function (conf) {
    };
    SearchCanAttackTarget.prototype.onTick = function (dt) {
        var _this = this;
        var _a;
        var allTargets = this.target.getCanAttackTargets().filter(function (m) { return _this.target.checkInMyAttackRange(m.target); });
        if (allTargets.length > 0) {
            if (this.target.getEquipEffectByType(Enums_1.EquipEffectType.SILVER_SNAKE_WHIP)) {
                return this.randomTarget(allTargets);
            }
            else if (this.target.isHasBuff(Enums_1.BuffType.RAGE)) {
                var it = this.randomMinDisTargetInAR(allTargets);
                if (it) {
                    this.target.changeAttackTarget(it.target);
                    return BTConstant_1.BTState.SUCCESS;
                }
            }
            var heroSkillId = (_a = this.target.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id;
            var isCx_1 = heroSkillId === Enums_1.HeroType.CAO_XIU, isHz_1 = heroSkillId === Enums_1.HeroType.HUANG_ZHONG;
            allTargets.sort(function (a, b) {
                var aw = 0, bw = 0;
                if (isCx_1) { //曹休 最远目标
                    aw = a.dis;
                    bw = b.dis;
                }
                else if (isHz_1) { //黄忠 生命比最低的目标
                    aw = Math.floor((1 - a.target.getHpRatio()) * 100);
                    bw = Math.floor((1 - b.target.getHpRatio()) * 100);
                }
                aw = aw * 10 + (a.isRestrain ? 1 : 0);
                bw = bw * 10 + (b.isRestrain ? 1 : 0);
                aw = aw * 1000 + (999 - a.attackIndex);
                bw = bw * 1000 + (999 - b.attackIndex);
                return bw - aw;
            });
            this.target.changeAttackTarget(allTargets[0].target);
            return BTConstant_1.BTState.SUCCESS;
        }
        return BTConstant_1.BTState.FAILURE;
    };
    SearchCanAttackTarget.prototype.randomTarget = function (allTargets) {
        var it = allTargets[this.ctrl.getRandom().get(0, allTargets.length - 1)];
        this.target.changeAttackTarget(it.target);
        return BTConstant_1.BTState.SUCCESS;
    };
    // 随机最近的目标
    SearchCanAttackTarget.prototype.randomMinDisTargetInAR = function (allTargets) {
        var _this = this;
        var arr = [], minDis = 100000;
        allTargets.forEach(function (m) {
            var dis = _this.target.getMinDis(m.target);
            if (dis < minDis) {
                minDis = dis;
                arr = [m];
            }
            else if (dis === minDis) {
                arr.push(m);
            }
        });
        if (arr.length > 0) {
            return arr[this.ctrl.getRandom().get(0, arr.length - 1)];
        }
        return null;
    };
    return SearchCanAttackTarget;
}(BaseAction_1.default));
exports.default = SearchCanAttackTarget;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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