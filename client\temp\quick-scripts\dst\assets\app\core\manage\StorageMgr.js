
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/StorageMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '68910T62iZKnKD+M44GPK4Y', 'StorageMgr');
// app/core/manage/StorageMgr.ts

// 存储封装
var StorageMgr = /** @class */ (function () {
    function StorageMgr() {
    }
    StorageMgr.prototype.__key = function (key) {
        return mc.GameNameSpace + '_' + key;
    };
    StorageMgr.prototype.__get = function (key) {
        return localStorage.getItem(this.__key(key));
    };
    StorageMgr.prototype.__set = function (key, val) {
        localStorage.setItem(this.__key(key), val);
    };
    StorageMgr.prototype.loadString = function (key) {
        return this.__get(key);
    };
    StorageMgr.prototype.saveString = function (key, val) {
        this.__set(key, val);
    };
    StorageMgr.prototype.loadNumber = function (key) {
        var val = this.__get(key);
        return val ? Number(val) : null;
    };
    StorageMgr.prototype.saveNumber = function (key, val) {
        this.__set(key, String(val));
    };
    StorageMgr.prototype.loadBool = function (key) {
        var val = this.__get(key);
        return val ? val === '1' : null;
    };
    StorageMgr.prototype.saveBool = function (key, val) {
        this.__set(key, val ? '1' : '0');
    };
    StorageMgr.prototype.loadJson = function (key) {
        var val = this.__get(key);
        return val ? JSON.parse(val) : null;
    };
    StorageMgr.prototype.saveJson = function (key, val) {
        this.__set(key, JSON.stringify(val));
    };
    StorageMgr.prototype.remove = function (key) {
        localStorage.removeItem(this.__key(key));
    };
    StorageMgr.prototype.clear = function () {
        localStorage.clear();
    };
    return StorageMgr;
}());
window['storageMgr'] = new StorageMgr();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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