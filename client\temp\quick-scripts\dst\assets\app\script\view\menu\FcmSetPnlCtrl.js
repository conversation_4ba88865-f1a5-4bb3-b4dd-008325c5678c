
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/FcmSetPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '724fbSft0hOtZWTQIfxUmts', 'FcmSetPnlCtrl');
// app/script/view/menu/FcmSetPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var FcmSetPnlCtrl = /** @class */ (function (_super) {
    __extends(FcmSetPnlCtrl, _super);
    function FcmSetPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.openTge_ = null; // path://root/open_te_t
        _this.listNode_ = null; // path://root/list_n
        //@end
        _this.user = null;
        _this.isOpenAuth = false;
        return _this;
    }
    FcmSetPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    FcmSetPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    FcmSetPnlCtrl.prototype.onEnter = function (data) {
        var arr = this.user.getFcmConfs().slice();
        this.listNode_.children.forEach(function (m) { return m.Component(cc.Toggle).isChecked = arr.has(Number(m.name)); });
        this.init();
    };
    FcmSetPnlCtrl.prototype.onRemove = function () {
        // 检测是否有改变
        var confs = this.getConfs();
        if (this.checkHasChange(confs)) {
            this.user.setFcmConfs(confs);
            GameHelper_1.gameHpr.net.send('lobby/HD_SetOfflineNotify', { opt: confs });
        }
        GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.PUSH);
    };
    FcmSetPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/open_te_t
    FcmSetPnlCtrl.prototype.onClickOpen = function (event, data) {
        var _this = this;
        if (!this.isOpenAuth && event.isChecked) {
            event.isChecked = false;
            return ViewHelper_1.viewHelper.showMessageBox('ui.not_open_fcm_auth_tip', {
                okText: 'ui.button_goto_open',
                ok: function () {
                    JsbHelper_1.jsbHelper.openSelfSetting();
                    ut.waitGameShow().then(function () { return _this.isValid && _this.init(); });
                },
                cancel: function () { }
            });
        }
        this.hideListNode(!event.isChecked, false);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    FcmSetPnlCtrl.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loading, background, ok, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        loading = this.openTge_.Child('loading'), background = this.openTge_.Child('Background');
                        loading.active = true;
                        background.active = false;
                        this.openTge_.interactable = false;
                        this.hideListNode(true, true);
                        _a = this;
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.checkNoticePer()];
                    case 1:
                        ok = _a.isOpenAuth = _b.sent();
                        if (!this.isValid || !this.getActive()) {
                            return [2 /*return*/];
                        }
                        this.openTge_.interactable = true;
                        loading.active = false;
                        background.active = true;
                        if (ok) {
                            this.openTge_.isChecked = this.user.getFcmConfs().has(0);
                        }
                        else {
                            this.openTge_.isChecked = false;
                        }
                        this.hideListNode(!this.openTge_.isChecked, false);
                        return [2 /*return*/];
                }
            });
        });
    };
    FcmSetPnlCtrl.prototype.hideListNode = function (val, init) {
        this.listNode_.children.forEach(function (m) {
            m.opacity = val ? 100 : 255;
            m.Component(cc.Toggle).interactable = !val;
            var ground = m.Child('val/Background');
            ground.opacity = init ? 0 : 255;
            init && ground.Component(cc.Widget).updateAlignment();
        });
    };
    FcmSetPnlCtrl.prototype.getConfs = function () {
        var arr = [];
        if (this.openTge_.isChecked) {
            arr.push(0);
        }
        this.listNode_.children.forEach(function (m) {
            if (m.Component(cc.Toggle).isChecked) {
                arr.push(Number(m.name));
            }
        });
        return arr;
    };
    // 检测2个数组是否一样
    FcmSetPnlCtrl.prototype.checkHasChange = function (confs) {
        var arr = this.user.getFcmConfs();
        if (confs.length !== arr.length) {
            return true;
        }
        var obj = {};
        arr.forEach(function (m) { return obj[m] = true; });
        return confs.some(function (m) { return !obj[m]; });
    };
    FcmSetPnlCtrl = __decorate([
        ccclass
    ], FcmSetPnlCtrl);
    return FcmSetPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = FcmSetPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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