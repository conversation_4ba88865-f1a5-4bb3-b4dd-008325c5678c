
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildBarracksPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1c4b0sEORJEk5NY3HlJENZA', 'BuildBarracksPnlCtrl');
// app/script/view/build/BuildBarracksPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var NetHelper_1 = require("../../common/helper/NetHelper");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ccclass = cc._decorator.ccclass;
var BuildBarracksPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildBarracksPnlCtrl, _super);
    function BuildBarracksPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.queueSv_ = null; // path://root/pages_n/1/drill/content/queue_sv
        //@end
        _this.PKEY_TAB = '';
        _this.PKEY_SELECT_ARMY = '';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.tab = 0;
        _this.selectPawnSlot = null;
        _this.selectArmy = null;
        _this.drillProgressTween = {};
        _this.tempArmySortWeightMap = {};
        _this.tempCreateArmy = null;
        _this.armyList = [];
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildBarracksPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdatePawnDrillQueue, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.REMOVE_ARMY] = this.onUpdateArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmy, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI] = this.onUpdateArmy, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_PAWN_SLOTS] = this.onUpdatePawnSlots, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.BARRACKS_SELECT_PAWN] = this.onBarracksSelectPawn, _p.enter = true, _p),
        ];
    };
    BuildBarracksPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildBarracksPnlCtrl.prototype.onEnter = function (data, tab) {
        this.PKEY_TAB = 'BARRACKS_TAB_' + data.id;
        this.PKEY_SELECT_ARMY = 'BARRACKS_SELECT_ARMY_TAB_' + data.id;
        this.data = data;
        this.tempCreateArmy = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.TEMP_CREATE_ARMY);
        var cond = this.pagesNode_.Child('1/info/cond');
        cond.Child('need/title/val').setLocaleKey('ui.drill_cost', 'ui.button_drill');
        this.pagesNode_.Child('1/drill/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_drill');
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildBarracksPnlCtrl.prototype.onRemove = function () {
        this.selectPawnSlot = null;
        this.selectArmy = null;
        this.tempArmySortWeightMap = {};
        this.showCreateArmyFingerTip(false);
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy);
    };
    BuildBarracksPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildBarracksPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.PAWN_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            // 显示当前的军队列表
            this.selectArmy = null;
            this.tempArmySortWeightMap = {};
            this.updateArmyList(true, node);
            // 显示可训练的士兵
            this.selectPawnSlot = null;
            this.updatePawnList(true, node);
            // 训练列表
            this.updateDrillQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildBarracksPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/info/pawn/list/view/content/pawn_be
    BuildBarracksPnlCtrl.prototype.onClickPawn = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, slot = it.Data;
        if (!slot) {
            return;
        }
        else if (slot.uid === ((_a = this.selectPawnSlot) === null || _a === void 0 ? void 0 : _a.uid)) {
            if (slot.pawn) {
                GameHelper_1.gameHpr.setNoLongerTip('look_pawn_info', true);
                ViewHelper_1.viewHelper.showPnl('area/PawnInfo', slot.pawn);
            }
            return;
        }
        else if (!slot.id && slot.lv > 0 && this.data.lv >= slot.lv) {
            if (slot.selectIds.length === 0) {
                return ViewHelper_1.viewHelper.showAlert('toast.please_unlock_prev_slot', { params: ['ui.ceri_type_name_2'] });
            }
            return ViewHelper_1.viewHelper.showPnl('build/StudySelect', slot);
        }
        this.updatePawnSelect(slot, false);
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildBarracksPnlCtrl.prototype.onClickArmy = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data;
        if (!data) {
            if (this.tempCreateArmy) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_has_empty_army');
            }
            return this.showCreateArmyUI();
        } /* else if (!data.army) {
            return viewHelper.showAlert('toast.army_not_in_maincity')
        } */
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/drill_be
    BuildBarracksPnlCtrl.prototype.onClickDrill = function (event, data) {
        var _this = this;
        var slot = this.selectPawnSlot, state = event.target.Data;
        if (!slot) {
            if (!this.selectArmy) { // 没有军队，提示创建军队
                if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                    return ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                        cb: function () {
                            if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                                _this.showCreateArmyFingerTip(true);
                            }
                        }
                    });
                }
                else {
                    var army_1 = this.player.getBaseArmys()[0];
                    return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_1, 'ui.button_drill');
                }
            }
            else { // 有军队，但是外出，先提示军队
                if (!this.selectArmy.army) { // 外出
                    if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                        return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
                    }
                    else {
                        var army_2 = this.player.getBaseArmys()[0];
                        return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_2, 'ui.button_drill');
                    }
                }
                else if (this.player.getCanRecruitPawns(this.data.id).filter(function (m) { return !!m.pawn; }).length === 0) { // 没有士兵，提示点击加号解锁
                    return ViewHelper_1.viewHelper.showAlert('ui.please_select_pawn_study');
                }
                else { // 已有士兵，提示选择一个士兵进行招募
                    return ViewHelper_1.viewHelper.showAlert('toast.please_select_ceir_item', { params: ['ui.ceri_type_name_2'] });
                }
            }
        }
        else if (state === 1) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_PAWN_FULL);
        }
        else if (state === 2) { // 军队不在主城内
            if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
            }
            else {
                return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', this.player.getBaseArmys()[0], 'ui.button_drill');
            }
        }
        var area = this.areaCenter.getArea(this.data.aIndex);
        if (!area) {
            return;
        }
        else if (!this.selectArmy) {
            if (area.armys.length === 0 || area.armys.every(function (m) { return m.pawns.length >= Constant_1.ARMY_PAWN_MAX_COUNT; })) { //一个军队也没有
                ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                    cb: function () {
                        if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                            _this.showCreateArmyFingerTip(true);
                        }
                    }
                });
            }
            else {
                ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
            }
            return;
        }
        var selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = '';
        var army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid);
        if (!army) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_NOT_EXIST);
        }
        else if (army.uid.startsWith('temp_')) {
            armyName = army.name;
        }
        else {
            tempArmyUid = army.uid;
        }
        this.areaCenter.drillPawnToServer(this.data.aIndex, this.data.uid, slot.id, tempArmyUid, armyName).then(function (res) {
            var _a;
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            }
            else if (res.err === ECode_1.ecode.ANTI_CHEAT) {
                ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
            }
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                audioMgr.playSFX('build/sound_ui_00' + (_this.data.id === 2010 ? '7' : '6'));
                var army_3 = res.army;
                if (((_a = _this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === selectArmyUid) {
                    _this.tempCreateArmy = null;
                    if (_this.selectArmy)
                        _this.selectArmy.uid = army_3.uid;
                    if (_this.tempArmySortWeightMap[selectArmyUid]) {
                        _this.tempArmySortWeightMap[army_3.uid] = _this.tempArmySortWeightMap[selectArmyUid];
                        delete _this.tempArmySortWeightMap[selectArmyUid];
                    }
                    _this.player.getBaseArmys().push({
                        index: _this.data.aIndex,
                        uid: army_3.uid,
                        name: army_3.name,
                        state: Enums_1.ArmyState.DRILL,
                        pawns: [],
                    });
                }
                // this.updateDrillQueue()
                // const node = this.pagesNode_.Child(1)
                // this.updateArmyList(false, node)
                // this.updateRecruitCost(node)
            }
        });
    };
    // path://root/pages_n/1/drill/content/0/drill_pawn_be
    BuildBarracksPnlCtrl.prototype.onClickDrillPawn = function (event, _) {
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByDrillInfo(data), data);
        }
    };
    // path://root/pages_n/1/info/cond/none/buttons/restudy/study_be
    BuildBarracksPnlCtrl.prototype.onClickStudy = function (event, data) {
        var slot = this.selectPawnSlot;
        if (!slot) {
            return;
        }
        else if (slot.selectIds.length === 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_unlock_prev_slot', { params: ['ui.ceri_type_name_2'] });
        }
        ViewHelper_1.viewHelper.showPnl('build/StudySelect', slot);
    };
    // path://root/pages_n/1/info/cond/none/buttons/cond/button/goto_worship_be
    BuildBarracksPnlCtrl.prototype.onClickGotoWorship = function (event, data) {
        var _this = this;
        if (!this.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL)) {
            ViewHelper_1.viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + Enums_1.BUILD_NID.HERO_HALL] });
        }
        ViewHelper_1.viewHelper.showBuildInfoByMain(Enums_1.BUILD_NID.HERO_HALL, 1).then(function (ok) {
            if (ok && _this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/pages_n/1/drill/content/0/cancel_drill_be
    BuildBarracksPnlCtrl.prototype.onClickCancelDrill = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data, pawnObj = this.areaCenter.createPawnByDrillInfo(data);
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox(pawnObj.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelDrill(pawnObj, data); },
                cancel: function () { },
            });
        }
        this.cancelDrill(pawnObj, data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildBarracksPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.PAWN_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新训练列表
    BuildBarracksPnlCtrl.prototype.onUpdatePawnDrillQueue = function () {
        this.updateDrillQueue();
        // this.updateArmyList(false)
        this.updateRecruitCost();
    };
    // 战斗开始
    BuildBarracksPnlCtrl.prototype.onAreaBattleBegin = function (index) {
        if (this.data.aIndex === index) {
            this.updateDrillQueue();
        }
    };
    // 战斗结束
    BuildBarracksPnlCtrl.prototype.onAreaBattleEnd = function (index) {
        if (this.data.aIndex === index) {
            this.updateDrillQueue();
        }
    };
    // 切换士兵皮肤
    BuildBarracksPnlCtrl.prototype.onChangePawnSkin = function (data) {
        var node = this.pagesNode_.Child(1);
        this.updatePawnList(false, node);
        this.updateDrillQueue(node);
    };
    // 切换士兵装备
    BuildBarracksPnlCtrl.prototype.onChangePawnEquip = function () {
        this.updatePawnList(false);
    };
    // 重新刷新军队列表
    BuildBarracksPnlCtrl.prototype.onUpdateArmy = function () {
        this.updateArmyList(false);
    };
    // 刷新槽位
    BuildBarracksPnlCtrl.prototype.onUpdatePawnSlots = function () {
        if (this.tab === 1) {
            this.updatePawnList(false);
        }
    };
    // 选择士兵
    BuildBarracksPnlCtrl.prototype.onBarracksSelectPawn = function (slot) {
        if (slot) {
            this.updatePawnSelect(slot, true);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildBarracksPnlCtrl.prototype.getTempCreateArmy = function (uid) {
        var _a;
        if (((_a = this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.tempCreateArmy;
        }
        return null;
    };
    BuildBarracksPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        var _a, _b;
        var item = {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
        if (!this.tempArmySortWeightMap[data.uid]) {
            var weight = item.army ? 2 : 1;
            weight = weight * 10 + (9 - (((_a = item.army) === null || _a === void 0 ? void 0 : _a.getActPawnCount()) || 0));
            weight = weight * 10 + (9 - (((_b = item.army) === null || _b === void 0 ? void 0 : _b.pawns.length) || 0));
            this.tempArmySortWeightMap[data.uid] = weight;
        }
        return item;
    };
    // 刷新军队列表
    BuildBarracksPnlCtrl.prototype.updateArmyList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        node = node || this.pagesNode_.Child(1);
        // 当前区域的军队
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        this.armyList = [null];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return _this.armyList.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        if (this.tempCreateArmy) {
            this.armyList.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy));
        }
        // 排个序
        this.armyList.sort(function (a, b) { return _this.tempArmySortWeightMap[b === null || b === void 0 ? void 0 : b.uid] - _this.tempArmySortWeightMap[a === null || a === void 0 ? void 0 : a.uid]; });
        var countNode = node.Child('army/title/count_bg');
        countNode.Child('cur', cc.Label).string = (this.armyList.length - 1) + '';
        countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount();
        var uid = (_c = (_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid) !== null && _c !== void 0 ? _c : this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? this.armyList.find(function (m) { return !!(m === null || m === void 0 ? void 0 : m.army) && (m === null || m === void 0 ? void 0 : m.uid) === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.stopAutoScroll();
        // armys.push(null)
        var showDot = !this.armyList.some(function (m) { return !!m; });
        sv.Items(this.armyList, function (it, data, i) {
            var _a;
            it.Data = data;
            var army = data === null || data === void 0 ? void 0 : data.army;
            var root = it.Child('root'), info = root.Child('info');
            info.Child('dot').active = showDot;
            info.Child('add').active = !data;
            info.Child('count').active = !!data;
            info.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            var state = info.Child('state');
            if (army) {
                info.Child('count/val', cc.Label).string = army.pawns.length + '';
                var addLbl = info.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length;
                if (addLbl.node.active = dpc > 0) {
                    addLbl.string = '+' + dpc;
                }
                var isFull = state.active = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT;
                root.Child('bg').Component(cc.MultiFrame).setFrame(!isFull);
                info.opacity = isFull ? 150 : 255;
                if (isFull) {
                    state.setLocaleKey('ui.yet_full');
                }
                // 显示选择
                /* if (!curArmy && isFull) {
                } else */ if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
                }
            }
            else if (data) {
                info.opacity = 150;
                root.Child('bg').Component(cc.MultiFrame).setFrame(false);
                info.Child('count/val', cc.Label).string = (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0) + '';
                info.Child('count/add').active = false;
                state.active = true;
                state.setLocaleKey('ui.go_out');
                if (index === -1 && (!curArmy || curArmy.uid === data.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, data.uid);
                }
            }
            else {
                info.opacity = 255;
                state.active = false;
                root.Child('bg').Component(cc.MultiFrame).setFrame(true);
            }
        });
        // 将选中的移动到中间
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        // 刷新选中
        this.updateArmySelect(curArmy, node);
    };
    BuildBarracksPnlCtrl.prototype.updateArmySelect = function (item, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var uid = (item === null || item === void 0 ? void 0 : item.uid) || '';
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        node.Child('info/army').Swih('army_pawns');
        this.selectArmy = item;
        var army = item === null || item === void 0 ? void 0 : item.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
            pawns.pushArr(army.curingPawns);
            pawns.pushArr(army.drillPawns);
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            pawns.pushArr(item.pawns);
        }
        // 刷新士兵列表
        node.Child('info/army/army_pawns').children.forEach(function (it, i) {
            var _a;
            var data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!(army === null || army === void 0 ? void 0 : army.curingPawns.some(function (m) { return m.uid === data.uid; }));
            it.Swih('none', !!data);
            if (data) {
                var icon = it.Child('icon');
                icon.opacity = (isId || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? data : (((_a = data.portrayal) === null || _a === void 0 ? void 0 : _a.id) || data.id), icon, _this.key, false);
                it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + '';
            }
        });
        // 刷新按钮
        var cond = node.Child('info/cond'), button = cond.Child('need/buttons/drill_be');
        if (army) {
            button.Data = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT ? 1 : 0;
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            button.Data = 2; //不在主城内
        }
        else {
            button.Data = 0; //没有选择军队
        }
        button.opacity = (!this.selectPawnSlot || !item || button.Data) ? 120 : 255;
    };
    // 刷新士兵列表
    BuildBarracksPnlCtrl.prototype.updatePawnList = function (isLocation, node) {
        var _this = this;
        var _a;
        node = node || this.pagesNode_.Child(1);
        var root = node.Child('info');
        var selectPawnSlot = this.selectPawnSlot;
        var selectUid = (_a = this.selectPawnSlot) === null || _a === void 0 ? void 0 : _a.uid;
        var hasEquip = this.player.getEquips().length > 0, buildLv = this.data.lv;
        var slots = this.player.getCanRecruitPawns(this.data.id);
        var sv = root.Child('pawn/list', cc.ScrollView), pawnCount = slots.filter(function (m) { return !!m.pawn; }).length;
        sv.stopAutoScroll();
        sv.Items(slots, function (it, slot, i) {
            var _a;
            it.Data = slot;
            var lv = slot.lv, pawn = slot.pawn, isHeroHallUnlock = lv > 1000;
            if (slot.id < 0 && isHeroHallUnlock) { // 英雄解锁重复只保留一个
                it.active = false;
            }
            else {
                var isSelect = slot.isYetStudy(), isUnlock = buildLv >= lv, restudyHero = slot.getRestudyHero();
                var icon = it.Child('icon');
                if (icon.active = isSelect || !!restudyHero) {
                    ResHelper_1.resHelper.loadPawnHeadIcon(slot.viewId, icon, _this.key);
                    icon.opacity = restudyHero ? 100 : 255;
                }
                var showAdd = isUnlock && !icon.active && !isHeroHallUnlock;
                it.Child('add').active = it.Child('new').active = showAdd;
                it.Child('lock').active = !isHeroHallUnlock && !isUnlock && !isSelect;
                it.Child('none').active = isHeroHallUnlock && !isSelect && !restudyHero;
                // 重新研究红点 || 没有配置装备的红点
                it.Child('dot').active = !!restudyHero || (isSelect && !((_a = pawn === null || pawn === void 0 ? void 0 : pawn.equip) === null || _a === void 0 ? void 0 : _a.id) && (pawn === null || pawn === void 0 ? void 0 : pawn.type) < Enums_1.PawnType.MACHINE && hasEquip);
                // 画像解锁的单独颜色底
                it.Child('bg').Component(cc.MultiFrame).setFrame(isHeroHallUnlock);
                // 找到选择
                // if (!selectUid && (pawnCount === 0 || showAdd)) {
                // } else if (!selectPawnSlot && (!selectUid || slot.uid === selectUid)) {
                //     selectPawnSlot = slot
                // }
            }
        });
        this.updatePawnSelect(selectPawnSlot, isLocation, node);
    };
    BuildBarracksPnlCtrl.prototype.updatePawnSelect = function (slot, isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        this.selectPawnSlot = slot;
        var uid = (slot === null || slot === void 0 ? void 0 : slot.uid) || '', selectIndex = -1;
        var root = node.Child('info'), sv = root.Child('pawn/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a, _b;
            var select = m.Child('bg/select').active = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select || !!((_b = m.Data) === null || _b === void 0 ? void 0 : _b.pawn);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
        // 显示士兵费用
        this.updateRecruitCost(node);
    };
    // 刷新招募费用
    BuildBarracksPnlCtrl.prototype.updateRecruitCost = function (node) {
        node = node || this.pagesNode_.Child(1);
        var root = node.Child('info'), cond = root.Child('cond');
        var slot = this.selectPawnSlot;
        if (!slot) { // 招募按钮一直显示，通过点击按钮做出相应的提示
            var it_1 = cond.Swih('need')[0];
            var cost = [Enums_1.CType.CEREAL, Enums_1.CType.CEREAL_C];
            it_1.Child('time/up').active = false;
            it_1.Child('time/val', cc.Label) /* .Color('#756963') */.string = '-';
            it_1.Child('cost').Items(cost || [], function (it, cost) {
                if (it && cost) {
                    it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost);
                    it.Child('val', cc.Label) /* .Color('#756963') */.string = '-';
                }
            });
            it_1.Child('buttons/drill_be/val').setLocaleKey('ui.button_drill');
            return;
        }
        var isYetStudy = slot.isYetStudy();
        var it = cond.Swih(isYetStudy ? 'need' : 'none')[0];
        if (isYetStudy) { //已经选择
            // 检测是否有训练士兵费用增加
            var cost = GameHelper_1.gameHpr.world.getSeason().changeBaseResCost(Enums_1.CEffect.RECRUIT_COST, slot.drillCost);
            // 减少时间
            var time = slot.drillTime;
            var cd = this.getDrillTimeCD();
            var upCount = this.player.getUpRecruitPawnCount(); //加速 不免费
            var policyFreeCount = this.player.getFreeRecruitPawnSurplusCount(); //免费
            if (GameHelper_1.gameHpr.isNoviceMode) {
                cd = GameHelper_1.gameHpr.noviceServer.getDrillSpeedTime(time).cd;
            }
            it.Child('time/up').active = true;
            ViewHelper_1.viewHelper.updateFreeCostView(it, cost, time, cd, false, policyFreeCount, upCount);
            var button = it.Child('buttons/drill_be');
            button.opacity = 255;
            button.Child('val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_drill' : 'ui.button_drill');
        }
        else if (slot.lv > 1000) { // 英雄殿相关提示
            var restudyHero = slot.getRestudyHero();
            if (restudyHero) { // 重复解锁提示
                it.Child('buttons').Swih('restudy')[0].Child('desc').setLocaleKey('ui.restudy_desc', restudyHero.avatarPawnName);
            }
            else {
                var n = it.Child('buttons').Swih('cond')[0], needLv = Math.abs(slot.lv % 100), heroHallLv = this.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL);
                n.Child('desc').setLocaleKey('ui.unlock_hero_pawn_desc', heroHallLv >= needLv ? assetsMgr.lang('ui.short_lv', needLv) : "<color=#C34B3F>" + assetsMgr.lang('ui.short_lv', needLv) + "</c>");
            }
        }
        else if (this.data.lv < slot.lv) { // 等级不足提示
            it.Child('buttons').Swih('lock')[0].Child('val').setLocaleKey('ui.lv_unlock_new', [assetsMgr.lang('ui.short_lv', slot.lv), 'ui.ceri_type_name_2']);
        }
    };
    // 获取训练时间
    BuildBarracksPnlCtrl.prototype.getDrillTimeCD = function () {
        var cd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.XL_CD) + this.data.getEffectValue(Enums_1.CEffect.XL_CD);
        return cd * 0.01;
    };
    // 刷新训练列表
    BuildBarracksPnlCtrl.prototype.updateDrillQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getPawnDrillQueues(this.data.uid);
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var pawnConf = this.player.getConfigPawnMap();
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.DRILL_QUEUE);
        node.Child('drill/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('drill/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1 || childrenCount < queueCount - 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var skinId = data ? (((_a = pawnConf[data.id]) === null || _a === void 0 ? void 0 : _a.skinId) || data.id) : 0;
            var has = it.Child('icon').active = it.Child('drill_pawn_be').active = !!data;
            (_b = it.Child('cancel_drill_be')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            (_c = it.Child('icon/progress')) === null || _c === void 0 ? void 0 : _c.setActive(has);
            if (data) {
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            }
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = data.getSurplusTime();
                time += stime;
                (_d = this.drillProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.drillProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                var st = stime * 0.001;
                it.Child('time', cc.LabelTimer).run(st);
                this.drillProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('drill/desc').active = time > 0;
        if (time > 0) {
            node.Child('drill/desc/title').setLocaleKey('ui.drill_all_desc', this.data.id === 2010 ? 'ui.button_produce' : 'ui.button_drill');
            node.Child('drill/desc/time/val', cc.LabelTimer).run(time * 0.001);
        }
    };
    BuildBarracksPnlCtrl.prototype.showCreateArmyUI = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        this.showCreateArmyFingerTip(false);
        return ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.tempCreateArmy = new ArmyObj_1.default().init(_this.data.aIndex, GameHelper_1.gameHpr.getUid(), name);
                _this.tempArmySortWeightMap = {};
                if (!_this.selectArmy) {
                    _this.selectArmy = {};
                }
                _this.selectArmy.uid = _this.tempCreateArmy.uid;
                var node = _this.pagesNode_.Child(1);
                _this.updateArmyList(true, node);
                _this.updatePawnList(false, node);
            }
        });
    };
    // 显示创建军队提示手指
    BuildBarracksPnlCtrl.prototype.showCreateArmyFingerTip = function (val) {
        return; //不显示手指
        var node = this.pagesNode_.Child(1);
        var sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger');
        if (finger.active = val) {
            var count = sv.content.childrenCount;
            sv.stopAutoScroll();
            if (count >= 4) {
                sv.scrollToLeft();
            }
            var it = sv.content.children[0];
            var pos = ut.convertToNodeAR(it, sv.node);
            finger.setPosition(pos.x, pos.y - 12);
        }
    };
    // 取消招募
    BuildBarracksPnlCtrl.prototype.cancelDrill = function (data, info) {
        if (!data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        var isMachine = data.isMachine();
        NetHelper_1.netHelper.reqCancelDrillPawn({ index: index, buildUid: info.buid, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data_1 = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data_1.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data_1.army);
                GameHelper_1.gameHpr.player.updatePawnDrillQueue(data_1.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_b = data_1.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',
                        id: json.id,
                        cost: data_1.needCost,
                    });
                }
            }
        });
    };
    BuildBarracksPnlCtrl = __decorate([
        ccclass
    ], BuildBarracksPnlCtrl);
    return BuildBarracksPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildBarracksPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxCdWlsZEJhcnJhY2tzUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyREFBcUY7QUFDckYscURBQW9EO0FBQ3BELHFEQUE0SDtBQUM1SCwwREFBcUQ7QUFDckQsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFFNUQsb0RBQStDO0FBUy9DLDJEQUEwRDtBQUMxRCxpRUFBNEQ7QUFFcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBa0Qsd0NBQWM7SUFBaEU7UUFBQSxxRUE0dEJDO1FBMXRCRywwQkFBMEI7UUFDbEIsYUFBTyxHQUF1QixJQUFJLENBQUEsQ0FBQywwQkFBMEI7UUFDN0QsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQyxzQkFBc0I7UUFDakQsb0JBQWMsR0FBWSxJQUFJLENBQUEsQ0FBQyxrREFBa0Q7UUFDakYsY0FBUSxHQUFrQixJQUFJLENBQUEsQ0FBQywrQ0FBK0M7UUFDdEYsTUFBTTtRQUVFLGNBQVEsR0FBVyxFQUFFLENBQUE7UUFDckIsc0JBQWdCLEdBQVcsRUFBRSxDQUFBO1FBRTdCLFVBQUksR0FBYyxJQUFJLENBQUE7UUFDdEIsWUFBTSxHQUFnQixJQUFJLENBQUE7UUFDMUIsZ0JBQVUsR0FBb0IsSUFBSSxDQUFBO1FBQ2xDLFVBQUksR0FBYSxJQUFJLENBQUE7UUFFckIsU0FBRyxHQUFXLENBQUMsQ0FBQTtRQUNmLG9CQUFjLEdBQWdCLElBQUksQ0FBQTtRQUNsQyxnQkFBVSxHQUFhLElBQUksQ0FBQTtRQUMzQix3QkFBa0IsR0FBZ0MsRUFBRSxDQUFBO1FBQ3BELDJCQUFxQixHQUE4QixFQUFFLENBQUE7UUFDckQsb0JBQWMsR0FBWSxJQUFJLENBQUE7UUFDOUIsY0FBUSxHQUFlLEVBQUUsQ0FBQTtRQUN6QixtQkFBYSxHQUF1QixJQUFJLENBQUE7O0lBb3NCcEQsQ0FBQztJQWxzQlUsOENBQWUsR0FBdEI7O1FBQ0ksT0FBTztzQkFDRCxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzlELEdBQUMsbUJBQVMsQ0FBQyx1QkFBdUIsSUFBRyxJQUFJLENBQUMsc0JBQXNCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzdFLEdBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsSUFBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2xFLEdBQUMsbUJBQVMsQ0FBQyxlQUFlLElBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDOUQsR0FBQyxtQkFBUyxDQUFDLGdCQUFnQixJQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDaEUsR0FBQyxtQkFBUyxDQUFDLGlCQUFpQixJQUFHLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDbEUsR0FBQyxtQkFBUyxDQUFDLFFBQVEsSUFBRyxJQUFJLENBQUMsWUFBWSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNwRCxHQUFDLG1CQUFTLENBQUMsV0FBVyxJQUFHLElBQUksQ0FBQyxZQUFZLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3ZELEdBQUMsbUJBQVMsQ0FBQyxXQUFXLElBQUcsSUFBSSxDQUFDLFlBQVksRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDdkQsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsWUFBWSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUMzRCxHQUFDLG1CQUFTLENBQUMscUJBQXFCLElBQUcsSUFBSSxDQUFDLFlBQVksRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDakUsR0FBQyxtQkFBUyxDQUFDLHdCQUF3QixJQUFHLElBQUksQ0FBQyxZQUFZLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3BFLEdBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsSUFBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2xFLEdBQUMsbUJBQVMsQ0FBQyxvQkFBb0IsSUFBRyxJQUFJLENBQUMsb0JBQW9CLEVBQUUsUUFBSyxHQUFFLElBQUk7U0FDN0UsQ0FBQTtJQUNMLENBQUM7SUFFWSx1Q0FBUSxHQUFyQjs7O2dCQUNJLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQTtnQkFDakMsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFFBQVEsQ0FBQyxDQUFBO2dCQUNyQyxJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsWUFBWSxDQUFDLENBQUE7Z0JBQzdDLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxTQUFTLENBQUMsNEJBQWtCLENBQUMsQ0FBQTs7OztLQUN6RTtJQUVNLHNDQUFPLEdBQWQsVUFBZSxJQUFjLEVBQUUsR0FBWTtRQUN2QyxJQUFJLENBQUMsUUFBUSxHQUFHLGVBQWUsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBO1FBQ3pDLElBQUksQ0FBQyxnQkFBZ0IsR0FBRywyQkFBMkIsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBO1FBQzdELElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2hCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxxQkFBYSxDQUFDLGdCQUFnQixDQUFDLENBQUE7UUFDcEYsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDakQsSUFBSSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxlQUFlLEVBQUUsaUJBQWlCLENBQUMsQ0FBQTtRQUM3RSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxnQkFBZ0IsRUFBRSxpQkFBaUIsQ0FBQyxDQUFBO1FBQy9GLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsYUFBSCxHQUFHLGNBQUgsR0FBRyxHQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQTtJQUNsRixDQUFDO0lBRU0sdUNBQVEsR0FBZjtRQUNJLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFBO1FBQzFCLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFBO1FBQ3RCLElBQUksQ0FBQyxxQkFBcUIsR0FBRyxFQUFFLENBQUE7UUFDL0IsSUFBSSxDQUFDLHVCQUF1QixDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQ25DLElBQUksQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMscUJBQWEsQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUE7SUFDeEYsQ0FBQztJQUVNLHNDQUFPLEdBQWQ7UUFDSSxTQUFTLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQzNDLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDBCQUEwQjtJQUMxQiwwQ0FBVyxHQUFYLFVBQVksS0FBZ0IsRUFBRSxJQUFZO1FBQ3RDLENBQUMsSUFBSSxJQUFJLFFBQVEsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDbEMsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUMvQyxJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDcEQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDMUMsSUFBSSxJQUFJLEtBQUssQ0FBQyxFQUFFO1lBQ1osSUFBSSxDQUFDLGFBQWEsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSx5QkFBYyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUNsRSx1QkFBVSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7WUFDNUUsdUJBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsWUFBWSxDQUFDLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixFQUFFLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1NBQ3RJO2FBQU0sSUFBSSxJQUFJLEtBQUssQ0FBQyxFQUFFO1lBQ25CLFlBQVk7WUFDWixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQTtZQUN0QixJQUFJLENBQUMscUJBQXFCLEdBQUcsRUFBRSxDQUFBO1lBQy9CLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFBO1lBQy9CLFdBQVc7WUFDWCxJQUFJLENBQUMsY0FBYyxHQUFHLElBQUksQ0FBQTtZQUMxQixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQTtZQUMvQixPQUFPO1lBQ1AsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxDQUFBO1NBQzlCO0lBQ0wsQ0FBQztJQUVELDZDQUE2QztJQUM3Qyx3Q0FBUyxHQUFULFVBQVUsS0FBMEIsRUFBRSxJQUFZO1FBQzlDLG9CQUFPLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDekMsQ0FBQztJQUVELDREQUE0RDtJQUM1RCwwQ0FBVyxHQUFYLFVBQVksS0FBMEIsRUFBRSxDQUFTOztRQUM3QyxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLEVBQUUsSUFBSSxHQUFnQixFQUFFLENBQUMsSUFBSSxDQUFBO1FBQ3BELElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDUCxPQUFNO1NBQ1Q7YUFBTSxJQUFJLElBQUksQ0FBQyxHQUFHLFlBQUssSUFBSSxDQUFDLGNBQWMsMENBQUUsR0FBRyxDQUFBLEVBQUU7WUFDOUMsSUFBSSxJQUFJLENBQUMsSUFBSSxFQUFFO2dCQUNYLG9CQUFPLENBQUMsY0FBYyxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFBO2dCQUM5Qyx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO2FBQ2pEO1lBQ0QsT0FBTTtTQUNUO2FBQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsR0FBRyxDQUFDLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUMzRCxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDN0IsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywrQkFBK0IsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLHFCQUFxQixDQUFDLEVBQUUsQ0FBQyxDQUFBO2FBQ3BHO1lBQ0QsT0FBTyx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsQ0FBQTtTQUN2RDtRQUNELElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLENBQUE7SUFDdEMsQ0FBQztJQUVELHVEQUF1RDtJQUN2RCwwQ0FBVyxHQUFYLFVBQVksS0FBMEIsRUFBRSxDQUFTOztRQUM3QyxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLEVBQUUsSUFBSSxHQUFhLEVBQUUsQ0FBQyxJQUFJLENBQUE7UUFDakQsSUFBSSxDQUFDLElBQUksRUFBRTtZQUNQLElBQUksSUFBSSxDQUFDLGNBQWMsRUFBRTtnQkFDckIsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywwQkFBMEIsQ0FBQyxDQUFBO2FBQzFEO1lBQ0QsT0FBTyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtTQUNqQyxDQUFDOztZQUVFO2FBQU0sSUFBSSxJQUFJLENBQUMsR0FBRyxZQUFLLElBQUksQ0FBQyxVQUFVLDBDQUFFLEdBQUcsQ0FBQSxFQUFFO1lBQzdDLElBQUksQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUNoRSxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDOUI7SUFDTCxDQUFDO0lBRUQsd0RBQXdEO0lBQ3hELDJDQUFZLEdBQVosVUFBYSxLQUEwQixFQUFFLElBQVk7UUFBckQsaUJBbUdDO1FBbEdHLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsS0FBSyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQzNELElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDUCxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxFQUFFLGNBQWM7Z0JBQ2xDLElBQUksb0JBQU8sQ0FBQyxhQUFhLENBQUMsc0JBQWMsQ0FBQyx5QkFBeUIsQ0FBQyxFQUFFO29CQUNqRSxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDBCQUEwQixFQUFFO3dCQUNwRCxFQUFFLEVBQUU7NEJBQ0EsSUFBSSxLQUFJLENBQUMsT0FBTyxJQUFJLENBQUMsS0FBSSxDQUFDLE1BQU0sQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLG9CQUFPLENBQUMsYUFBYSxDQUFDLFNBQVMsQ0FBQyxFQUFFO2dDQUNyRixLQUFJLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLENBQUE7NkJBQ3JDO3dCQUNMLENBQUM7cUJBQ0osQ0FBQyxDQUFBO2lCQUNMO3FCQUFNO29CQUNILElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7b0JBQzFDLE9BQU8sdUJBQVUsQ0FBQyxPQUFPLENBQUMsd0JBQXdCLEVBQUUsTUFBSSxFQUFFLGlCQUFpQixDQUFDLENBQUE7aUJBQy9FO2FBQ0o7aUJBQU0sRUFBRSxpQkFBaUI7Z0JBQ3RCLElBQUksQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksRUFBRSxFQUFFLEtBQUs7b0JBQzlCLElBQUksb0JBQU8sQ0FBQyxhQUFhLENBQUMsc0JBQWMsQ0FBQyx5QkFBeUIsQ0FBQyxFQUFFO3dCQUNqRSxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDRCQUE0QixDQUFDLENBQUE7cUJBQzVEO3lCQUFNO3dCQUNILElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7d0JBQzFDLE9BQU8sdUJBQVUsQ0FBQyxPQUFPLENBQUMsd0JBQXdCLEVBQUUsTUFBSSxFQUFFLGlCQUFpQixDQUFDLENBQUE7cUJBQy9FO2lCQUNKO3FCQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxFQUFSLENBQVEsQ0FBQyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsRUFBRSxnQkFBZ0I7b0JBQzFHLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsNkJBQTZCLENBQUMsQ0FBQTtpQkFDN0Q7cUJBQU0sRUFBRSxvQkFBb0I7b0JBQ3pCLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsK0JBQStCLEVBQUUsRUFBRSxNQUFNLEVBQUUsQ0FBQyxxQkFBcUIsQ0FBQyxFQUFFLENBQUMsQ0FBQTtpQkFDcEc7YUFDSjtTQUNKO2FBQU0sSUFBSSxLQUFLLEtBQUssQ0FBQyxFQUFFO1lBQ3BCLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLGNBQWMsQ0FBQyxDQUFBO1NBQ3BEO2FBQU0sSUFBSSxLQUFLLEtBQUssQ0FBQyxFQUFFLEVBQUUsVUFBVTtZQUNoQyxJQUFJLG9CQUFPLENBQUMsYUFBYSxDQUFDLHNCQUFjLENBQUMseUJBQXlCLENBQUMsRUFBRTtnQkFDakUsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyw0QkFBNEIsQ0FBQyxDQUFBO2FBQzVEO2lCQUFNO2dCQUNILE9BQU8sdUJBQVUsQ0FBQyxPQUFPLENBQUMsd0JBQXdCLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxpQkFBaUIsQ0FBQyxDQUFBO2FBQ3hHO1NBQ0o7UUFDRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQ3RELElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDUCxPQUFNO1NBQ1Q7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUN6QixJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxLQUFLLENBQUMsTUFBTSxJQUFJLDhCQUFtQixFQUFyQyxDQUFxQyxDQUFDLEVBQUUsRUFBRSxTQUFTO2dCQUNwRyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywwQkFBMEIsRUFBRTtvQkFDN0MsRUFBRSxFQUFFO3dCQUNBLElBQUksS0FBSSxDQUFDLE9BQU8sSUFBSSxDQUFDLEtBQUksQ0FBQyxNQUFNLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxvQkFBTyxDQUFDLGFBQWEsQ0FBQyxTQUFTLENBQUMsRUFBRTs0QkFDckYsS0FBSSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFBO3lCQUNyQztvQkFDTCxDQUFDO2lCQUNKLENBQUMsQ0FBQTthQUNMO2lCQUFNO2dCQUNILHVCQUFVLENBQUMsU0FBUyxDQUFDLDBCQUEwQixDQUFDLENBQUE7YUFDbkQ7WUFDRCxPQUFNO1NBQ1Q7UUFDRCxJQUFJLGFBQWEsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsRUFBRSxXQUFXLEdBQUcsRUFBRSxFQUFFLFFBQVEsR0FBRyxFQUFFLENBQUE7UUFDeEUsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxhQUFhLENBQUMsSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDdEYsSUFBSSxDQUFDLElBQUksRUFBRTtZQUNQLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLGNBQWMsQ0FBQyxDQUFBO1NBQ3BEO2FBQU0sSUFBSSxJQUFJLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsRUFBRTtZQUNyQyxRQUFRLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQTtTQUN2QjthQUFNO1lBQ0gsV0FBVyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUE7U0FDekI7UUFDRCxJQUFJLENBQUMsVUFBVSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsV0FBVyxFQUFFLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFBLEdBQUc7O1lBQ3ZHLElBQUksQ0FBQyxLQUFJLENBQUMsT0FBTyxFQUFFO2FBQ2xCO2lCQUFNLElBQUksR0FBRyxDQUFDLEdBQUcsS0FBSyxhQUFLLENBQUMsY0FBYyxFQUFFO2dCQUN6QyxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDJCQUEyQixDQUFDLENBQUE7YUFDM0Q7aUJBQU0sSUFBSSxHQUFHLENBQUMsR0FBRyxLQUFLLGFBQUssQ0FBQyxrQkFBa0IsRUFBRTtnQkFDN0MsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywrQkFBK0IsQ0FBQyxDQUFBO2FBQy9EO2lCQUFNLElBQUksR0FBRyxDQUFDLEdBQUcsS0FBSyxhQUFLLENBQUMsVUFBVSxFQUFFO2dCQUNyQyx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO2FBQ3ZDO2lCQUFNLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRTtnQkFDaEIsdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBO2FBQ2hDO2lCQUFNO2dCQUNILFFBQVEsQ0FBQyxPQUFPLENBQUMsbUJBQW1CLEdBQUcsQ0FBQyxLQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSyxJQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQTtnQkFDM0UsSUFBTSxNQUFJLEdBQUcsR0FBRyxDQUFDLElBQUksQ0FBQTtnQkFDckIsSUFBSSxPQUFBLEtBQUksQ0FBQyxjQUFjLDBDQUFFLEdBQUcsTUFBSyxhQUFhLEVBQUU7b0JBQzVDLEtBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFBO29CQUMxQixJQUFJLEtBQUksQ0FBQyxVQUFVO3dCQUFFLEtBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxHQUFHLE1BQUksQ0FBQyxHQUFHLENBQUE7b0JBQ25ELElBQUksS0FBSSxDQUFDLHFCQUFxQixDQUFDLGFBQWEsQ0FBQyxFQUFFO3dCQUMzQyxLQUFJLENBQUMscUJBQXFCLENBQUMsTUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxhQUFhLENBQUMsQ0FBQTt3QkFDaEYsT0FBTyxLQUFJLENBQUMscUJBQXFCLENBQUMsYUFBYSxDQUFDLENBQUE7cUJBQ25EO29CQUNELEtBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFLENBQUMsSUFBSSxDQUFDO3dCQUM1QixLQUFLLEVBQUUsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNO3dCQUN2QixHQUFHLEVBQUUsTUFBSSxDQUFDLEdBQUc7d0JBQ2IsSUFBSSxFQUFFLE1BQUksQ0FBQyxJQUFJO3dCQUNmLEtBQUssRUFBRSxpQkFBUyxDQUFDLEtBQUs7d0JBQ3RCLEtBQUssRUFBRSxFQUFFO3FCQUNaLENBQUMsQ0FBQTtpQkFDTDtnQkFDRCwwQkFBMEI7Z0JBQzFCLHdDQUF3QztnQkFDeEMsbUNBQW1DO2dCQUNuQywrQkFBK0I7YUFDbEM7UUFDTCxDQUFDLENBQUMsQ0FBQTtJQUNOLENBQUM7SUFFRCxzREFBc0Q7SUFDdEQsK0NBQWdCLEdBQWhCLFVBQWlCLEtBQTBCLEVBQUUsQ0FBUztRQUNsRCxJQUFNLElBQUksR0FBcUIsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQ3ZELElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDekY7SUFDTCxDQUFDO0lBRUQsZ0VBQWdFO0lBQ2hFLDJDQUFZLEdBQVosVUFBYSxLQUEwQixFQUFFLElBQVk7UUFDakQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQTtRQUNoQyxJQUFJLENBQUMsSUFBSSxFQUFFO1lBQ1AsT0FBTTtTQUNUO2FBQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFDcEMsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywrQkFBK0IsRUFBRSxFQUFFLE1BQU0sRUFBRSxDQUFDLHFCQUFxQixDQUFDLEVBQUUsQ0FBQyxDQUFBO1NBQ3BHO1FBQ0QsdUJBQVUsQ0FBQyxPQUFPLENBQUMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDakQsQ0FBQztJQUVELDJFQUEyRTtJQUMzRSxpREFBa0IsR0FBbEIsVUFBbUIsS0FBMEIsRUFBRSxJQUFZO1FBQTNELGlCQVNDO1FBUkcsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGlCQUFTLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDOUMsdUJBQVUsQ0FBQyxTQUFTLENBQUMsMEJBQTBCLEVBQUUsRUFBRSxNQUFNLEVBQUUsQ0FBQyxpQkFBaUIsR0FBRyxpQkFBUyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsQ0FBQTtTQUMxRztRQUNELHVCQUFVLENBQUMsbUJBQW1CLENBQUMsaUJBQVMsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQUEsRUFBRTtZQUMxRCxJQUFJLEVBQUUsSUFBSSxLQUFJLENBQUMsT0FBTyxFQUFFO2dCQUNwQixLQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7YUFDZDtRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVELHdEQUF3RDtJQUN4RCxpREFBa0IsR0FBbEIsVUFBbUIsS0FBMEIsRUFBRSxLQUFhO1FBQTVELGlCQVdDO1FBVkcsSUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE9BQU8sR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxDQUFBO1FBQzVGLElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDUCxPQUFNO1NBQ1Q7YUFBTSxJQUFJLElBQUksQ0FBQyxXQUFXLEdBQUcsQ0FBQyxFQUFFO1lBQzdCLE9BQU8sdUJBQVUsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsQ0FBQywrQkFBK0IsQ0FBQyxDQUFDLENBQUMsa0NBQWtDLEVBQUU7Z0JBQ3pILEVBQUUsRUFBRSxjQUFNLE9BQUEsS0FBSSxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBL0MsQ0FBK0M7Z0JBQ3pELE1BQU0sRUFBRSxjQUFRLENBQUM7YUFDcEIsQ0FBQyxDQUFBO1NBQ0w7UUFDRCxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQTtJQUNuQyxDQUFDO0lBQ0QsTUFBTTtJQUNOLGlIQUFpSDtJQUV6Ryw4Q0FBZSxHQUF2QixVQUF3QixJQUFjO1FBQ2xDLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBRTtZQUM1QixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNyQyxJQUFJLENBQUMsS0FBSyxDQUFDLHNCQUFzQixDQUFDLENBQUMsWUFBWSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUE7WUFDakUsdUJBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsWUFBWSxDQUFDLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixFQUFFLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ25JLElBQUksQ0FBQyxhQUFhLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUseUJBQWMsRUFBRSxJQUFJLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtTQUM5RTtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0QscURBQXNCLEdBQTlCO1FBQ0ksSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUE7UUFDdkIsNkJBQTZCO1FBQzdCLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO0lBQzVCLENBQUM7SUFFRCxPQUFPO0lBQ0MsZ0RBQWlCLEdBQXpCLFVBQTBCLEtBQWE7UUFDbkMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sS0FBSyxLQUFLLEVBQUU7WUFDNUIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUE7U0FDMUI7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLDhDQUFlLEdBQXZCLFVBQXdCLEtBQWE7UUFDakMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sS0FBSyxLQUFLLEVBQUU7WUFDNUIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUE7U0FDMUI7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELCtDQUFnQixHQUF4QixVQUF5QixJQUFhO1FBQ2xDLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3JDLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQ2hDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQTtJQUMvQixDQUFDO0lBRUQsU0FBUztJQUNELGdEQUFpQixHQUF6QjtRQUNJLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDOUIsQ0FBQztJQUVELFdBQVc7SUFDSCwyQ0FBWSxHQUFwQjtRQUNJLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDOUIsQ0FBQztJQUVELE9BQU87SUFDQyxnREFBaUIsR0FBekI7UUFDSSxJQUFJLElBQUksQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDN0I7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLG1EQUFvQixHQUE1QixVQUE2QixJQUFpQjtRQUMxQyxJQUFJLElBQUksRUFBRTtZQUNOLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDcEM7SUFDTCxDQUFDO0lBQ0QsaUhBQWlIO0lBRXpHLGdEQUFpQixHQUF6QixVQUEwQixHQUFXOztRQUNqQyxJQUFJLE9BQUEsSUFBSSxDQUFDLGNBQWMsMENBQUUsR0FBRyxNQUFLLEdBQUcsRUFBRTtZQUNsQyxPQUFPLElBQUksQ0FBQyxjQUFjLENBQUE7U0FDN0I7UUFDRCxPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTyw0Q0FBYSxHQUFyQixVQUFzQixJQUFTLEVBQUUsSUFBYSxFQUFFLEtBQWE7O1FBQ3pELElBQU0sSUFBSSxHQUFhO1lBQ25CLElBQUksRUFBRSxJQUFJLENBQUMsSUFBSTtZQUNmLEdBQUcsRUFBRSxJQUFJLENBQUMsR0FBRztZQUNiLEtBQUssT0FBQTtZQUNMLElBQUksTUFBQTtTQUNQLENBQUE7UUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN2QyxJQUFJLE1BQU0sR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUM5QixNQUFNLEdBQUcsTUFBTSxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLE9BQUEsSUFBSSxDQUFDLElBQUksMENBQUUsZUFBZSxPQUFNLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDaEUsTUFBTSxHQUFHLE1BQU0sR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFBLElBQUksQ0FBQyxJQUFJLDBDQUFFLEtBQUssQ0FBQyxNQUFNLEtBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMzRCxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLE1BQU0sQ0FBQTtTQUNoRDtRQUNELE9BQU8sSUFBSSxDQUFBO0lBQ2YsQ0FBQztJQUVELFNBQVM7SUFDRCw2Q0FBYyxHQUF0QixVQUF1QixVQUFtQixFQUFFLElBQWM7UUFBMUQsaUJBOEVDOztRQTdFRyxJQUFJLEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3ZDLFVBQVU7UUFDVixJQUFNLFdBQVcsR0FBK0IsRUFBRSxDQUFBO1FBQ2xELE1BQUEsSUFBSSxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsMENBQUUsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7WUFDdEQsSUFBSSxDQUFDLENBQUMsY0FBYyxFQUFFLEVBQUU7Z0JBQ3BCLFdBQVcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBO2FBQ3pCO1FBQ0wsQ0FBQyxFQUFDO1FBQ0YsSUFBSSxDQUFDLFFBQVEsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3RCLHNCQUFzQjtRQUN0QixJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksRUFBRSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLEtBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEtBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxFQUFFLFdBQVcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQXRFLENBQXNFLENBQUMsQ0FBQTtRQUMvRyxJQUFJLElBQUksQ0FBQyxjQUFjLEVBQUU7WUFDckIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxDQUFBO1NBQ25GO1FBQ0QsTUFBTTtRQUNOLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSyxPQUFBLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLGFBQUQsQ0FBQyx1QkFBRCxDQUFDLENBQUUsR0FBRyxDQUFDLEdBQUcsS0FBSSxDQUFDLHFCQUFxQixDQUFDLENBQUMsYUFBRCxDQUFDLHVCQUFELENBQUMsQ0FBRSxHQUFHLENBQUMsRUFBdkUsQ0FBdUUsQ0FBQyxDQUFBO1FBQ3JHLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMscUJBQXFCLENBQUMsQ0FBQTtRQUNuRCxTQUFTLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFBO1FBQ3pFLFNBQVMsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsZUFBZSxFQUFFLENBQUE7UUFDN0UsSUFBTSxHQUFHLGVBQUcsSUFBSSxDQUFDLFVBQVUsMENBQUUsR0FBRyxtQ0FBSSxJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO1FBQ3pGLElBQUksT0FBTyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLEVBQUMsQ0FBQyxhQUFELENBQUMsdUJBQUQsQ0FBQyxDQUFFLElBQUksQ0FBQSxJQUFJLENBQUEsQ0FBQyxhQUFELENBQUMsdUJBQUQsQ0FBQyxDQUFFLEdBQUcsTUFBSyxHQUFHLEVBQTNCLENBQTJCLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxFQUFFLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQTtRQUMzRixJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUE7UUFDakQsRUFBRSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ25CLG1CQUFtQjtRQUNuQixJQUFNLE9BQU8sR0FBRyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLENBQUMsRUFBSCxDQUFHLENBQUMsQ0FBQTtRQUM3QyxFQUFFLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUM7O1lBQ2hDLEVBQUUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1lBQ2QsSUFBTSxJQUFJLEdBQUcsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQTtZQUN2QixJQUFNLElBQUksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO1lBQ3hELElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQTtZQUNsQyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksQ0FBQTtZQUNoQyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFBO1lBQ25DLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQTtZQUMvRSxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1lBQ2pDLElBQUksSUFBSSxFQUFFO2dCQUNOLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFBO2dCQUNqRSxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLEVBQUUsR0FBRyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxDQUFBO2dCQUN4RyxJQUFJLE1BQU0sQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEdBQUcsR0FBRyxDQUFDLEVBQUU7b0JBQzlCLE1BQU0sQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLEdBQUcsQ0FBQTtpQkFDNUI7Z0JBQ0QsSUFBTSxNQUFNLEdBQUcsS0FBSyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsZUFBZSxFQUFFLElBQUksOEJBQW1CLENBQUE7Z0JBQzNFLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQTtnQkFDM0QsSUFBSSxDQUFDLE9BQU8sR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFBO2dCQUNqQyxJQUFJLE1BQU0sRUFBRTtvQkFDUixLQUFLLENBQUMsWUFBWSxDQUFDLGFBQWEsQ0FBQyxDQUFBO2lCQUNwQztnQkFDRCxPQUFPO2dCQUNQO3lCQUNTLENBQUMsSUFBSSxLQUFLLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLE9BQU8sSUFBSSxPQUFPLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRTtvQkFDbEUsT0FBTyxHQUFHLElBQUksQ0FBQTtvQkFDZCxLQUFLLEdBQUcsQ0FBQyxDQUFBO29CQUNULEtBQUksQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsS0FBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtpQkFDbkU7YUFDSjtpQkFBTSxJQUFJLElBQUksRUFBRTtnQkFDYixJQUFJLENBQUMsT0FBTyxHQUFHLEdBQUcsQ0FBQTtnQkFDbEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtnQkFDekQsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLE9BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsTUFBTSxLQUFJLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtnQkFDekUsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO2dCQUN0QyxLQUFLLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtnQkFDbkIsS0FBSyxDQUFDLFlBQVksQ0FBQyxXQUFXLENBQUMsQ0FBQTtnQkFDL0IsSUFBSSxLQUFLLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLE9BQU8sSUFBSSxPQUFPLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRTtvQkFDeEQsT0FBTyxHQUFHLElBQUksQ0FBQTtvQkFDZCxLQUFLLEdBQUcsQ0FBQyxDQUFBO29CQUNULEtBQUksQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsS0FBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtpQkFDbkU7YUFDSjtpQkFBTTtnQkFDSCxJQUFJLENBQUMsT0FBTyxHQUFHLEdBQUcsQ0FBQTtnQkFDbEIsS0FBSyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7Z0JBQ3BCLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLENBQUE7YUFDM0Q7UUFDTCxDQUFDLENBQUMsQ0FBQTtRQUNGLFlBQVk7UUFDWixJQUFJLFVBQVUsRUFBRTtZQUNaLEVBQUUsQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUMvQjtRQUNELE9BQU87UUFDUCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFBO0lBQ3hDLENBQUM7SUFFTywrQ0FBZ0IsR0FBeEIsVUFBeUIsSUFBYyxFQUFFLElBQWM7UUFBdkQsaUJBc0NDO1FBckNHLElBQUksR0FBRyxJQUFJLElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDdkMsSUFBTSxHQUFHLEdBQUcsQ0FBQSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsR0FBRyxLQUFJLEVBQUUsQ0FBQTtRQUMzQixJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDOztZQUM3RCxJQUFNLE1BQU0sR0FBRyxDQUFDLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxPQUFBLENBQUMsQ0FBQyxJQUFJLDBDQUFFLEdBQUcsTUFBSyxHQUFHLENBQUE7WUFDN0QsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxHQUFHLENBQUMsTUFBTSxDQUFBO1FBQ2pELENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7UUFDMUMsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUE7UUFDdEIsSUFBTSxJQUFJLEdBQUcsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksRUFBRSxLQUFLLEdBQUcsRUFBRSxDQUFBO1FBQ25DLElBQUksSUFBSSxFQUFFO1lBQ04sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDekIsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7WUFDL0IsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUE7U0FDakM7YUFBTSxJQUFJLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLEVBQUU7WUFDcEIsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDNUI7UUFDRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQyxFQUFFLEVBQUUsQ0FBQzs7WUFDdEQsSUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFLElBQUksR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLEtBQUssUUFBUSxFQUFFLFFBQVEsR0FBRyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsRUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBbEIsQ0FBa0IsRUFBQyxDQUFBO1lBQ2hJLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUN2QixJQUFJLElBQUksRUFBRTtnQkFDTixJQUFNLElBQUksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO2dCQUM3QixJQUFJLENBQUMsT0FBTyxHQUFHLENBQUMsSUFBSSxJQUFJLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQTtnQkFDN0MscUJBQVMsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFBLElBQUksQ0FBQyxTQUFTLDBDQUFFLEVBQUUsS0FBSSxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLEtBQUksQ0FBQyxHQUFHLEVBQUUsS0FBSyxDQUFDLENBQUE7Z0JBQ3BHLEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFBO2FBQzdFO1FBQ0wsQ0FBQyxDQUFDLENBQUE7UUFDRixPQUFPO1FBQ1AsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsRUFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQyxDQUFBO1FBQ2xGLElBQUksSUFBSSxFQUFFO1lBQ04sTUFBTSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsZUFBZSxFQUFFLElBQUksOEJBQW1CLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1NBQ3RFO2FBQU0sSUFBSSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsS0FBSyxFQUFFO1lBQ3BCLE1BQU0sQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFBLENBQUMsT0FBTztTQUMxQjthQUFNO1lBQ0gsTUFBTSxDQUFDLElBQUksR0FBRyxDQUFDLENBQUEsQ0FBQyxRQUFRO1NBQzNCO1FBQ0QsTUFBTSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLGNBQWMsSUFBSSxDQUFDLElBQUksSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFBO0lBQy9FLENBQUM7SUFFRCxTQUFTO0lBQ0QsNkNBQWMsR0FBdEIsVUFBdUIsVUFBbUIsRUFBRSxJQUFjO1FBQTFELGlCQXFDQzs7UUFwQ0csSUFBSSxHQUFHLElBQUksSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUN2QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQy9CLElBQUksY0FBYyxHQUFnQixJQUFJLENBQUMsY0FBYyxDQUFBO1FBQ3JELElBQUksU0FBUyxTQUFHLElBQUksQ0FBQyxjQUFjLDBDQUFFLEdBQUcsQ0FBQTtRQUN4QyxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsRUFBRSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsT0FBTyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFBO1FBQzNFLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUMxRCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLEVBQUUsU0FBUyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBUixDQUFRLENBQUMsQ0FBQyxNQUFNLENBQUE7UUFDakcsRUFBRSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ25CLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLFVBQUMsRUFBRSxFQUFFLElBQUksRUFBRSxDQUFDOztZQUN4QixFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtZQUNkLElBQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsSUFBSSxHQUFZLElBQUksQ0FBQyxJQUFJLEVBQUUsZ0JBQWdCLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQTtZQUMzRSxJQUFJLElBQUksQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFJLGdCQUFnQixFQUFFLEVBQUUsY0FBYztnQkFDakQsRUFBRSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7YUFDcEI7aUJBQU07Z0JBQ0gsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFVBQVUsRUFBRSxFQUFFLFFBQVEsR0FBRyxPQUFPLElBQUksRUFBRSxFQUFFLFdBQVcsR0FBRyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7Z0JBQ2pHLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQzdCLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxRQUFRLElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRTtvQkFDekMscUJBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLElBQUksRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7b0JBQ3ZELElBQUksQ0FBQyxPQUFPLEdBQUcsV0FBVyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQTtpQkFDekM7Z0JBQ0QsSUFBTSxPQUFPLEdBQUcsUUFBUSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sSUFBSSxDQUFDLGdCQUFnQixDQUFBO2dCQUM3RCxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxPQUFPLENBQUE7Z0JBQ3pELEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsZ0JBQWdCLElBQUksQ0FBQyxRQUFRLElBQUksQ0FBQyxRQUFRLENBQUE7Z0JBQ3JFLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxHQUFHLGdCQUFnQixJQUFJLENBQUMsUUFBUSxJQUFJLENBQUMsV0FBVyxDQUFBO2dCQUN2RSxzQkFBc0I7Z0JBQ3RCLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxXQUFXLElBQUksQ0FBQyxRQUFRLElBQUksUUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsS0FBSywwQ0FBRSxFQUFFLENBQUEsSUFBSSxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxJQUFJLElBQUcsZ0JBQVEsQ0FBQyxPQUFPLElBQUksUUFBUSxDQUFDLENBQUE7Z0JBQ3JILGFBQWE7Z0JBQ2IsRUFBRSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO2dCQUNsRSxPQUFPO2dCQUNQLG9EQUFvRDtnQkFDcEQsMEVBQTBFO2dCQUMxRSw0QkFBNEI7Z0JBQzVCLElBQUk7YUFDUDtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLGdCQUFnQixDQUFDLGNBQWMsRUFBRSxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDM0QsQ0FBQztJQUVPLCtDQUFnQixHQUF4QixVQUF5QixJQUFpQixFQUFFLFVBQW1CLEVBQUUsSUFBYztRQUMzRSxJQUFJLEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3ZDLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFBO1FBQzFCLElBQUksR0FBRyxHQUFHLENBQUEsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLEdBQUcsS0FBSSxFQUFFLEVBQUUsV0FBVyxHQUFHLENBQUMsQ0FBQyxDQUFBO1FBQzNDLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQTtRQUM1RSxFQUFFLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQzs7WUFDN0IsSUFBTSxNQUFNLEdBQUcsQ0FBQyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEdBQUcsT0FBQSxDQUFDLENBQUMsSUFBSSwwQ0FBRSxHQUFHLE1BQUssR0FBRyxDQUFBO1lBQzNGLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksR0FBRyxDQUFDLE1BQU0sSUFBSSxDQUFDLFFBQUMsQ0FBQyxDQUFDLElBQUksMENBQUUsSUFBSSxDQUFBLENBQUE7WUFDL0QsSUFBSSxNQUFNLEVBQUU7Z0JBQ1IsV0FBVyxHQUFHLENBQUMsQ0FBQTthQUNsQjtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxVQUFVLEVBQUU7WUFDWixFQUFFLENBQUMsa0JBQWtCLENBQUMsV0FBVyxDQUFDLENBQUE7U0FDckM7UUFDRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ2hDLENBQUM7SUFFRCxTQUFTO0lBQ0QsZ0RBQWlCLEdBQXpCLFVBQTBCLElBQWM7UUFDcEMsSUFBSSxHQUFHLElBQUksSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUN2QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQzFELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUE7UUFDaEMsSUFBSSxDQUFDLElBQUksRUFBRSxFQUFFLHlCQUF5QjtZQUNsQyxJQUFNLElBQUUsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQy9CLElBQU0sSUFBSSxHQUFHLENBQUMsYUFBSyxDQUFDLE1BQU0sRUFBRSxhQUFLLENBQUMsUUFBUSxDQUFDLENBQUE7WUFDM0MsSUFBRSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO1lBQ2xDLElBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQSx1QkFBdUIsQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFBO1lBQ2xFLElBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsS0FBSyxDQUFDLElBQUksSUFBSSxFQUFFLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTtnQkFDeEMsSUFBSSxFQUFFLElBQUksSUFBSSxFQUFFO29CQUNaLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUE7b0JBQ3BFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQSx1QkFBdUIsQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFBO2lCQUNoRTtZQUNMLENBQUMsQ0FBQyxDQUFBO1lBQ0YsSUFBRSxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1lBQ2hFLE9BQU07U0FDVDtRQUNELElBQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTtRQUNwQyxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUNyRCxJQUFJLFVBQVUsRUFBRSxFQUFFLE1BQU07WUFDcEIsZ0JBQWdCO1lBQ2hCLElBQU0sSUFBSSxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDLGlCQUFpQixDQUFDLGVBQU8sQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFBO1lBQzlGLE9BQU87WUFDUCxJQUFJLElBQUksR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFBO1lBQ3pCLElBQUksRUFBRSxHQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtZQUM5QixJQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLHFCQUFxQixFQUFFLENBQUEsQ0FBQyxRQUFRO1lBQzVELElBQU0sZUFBZSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsOEJBQThCLEVBQUUsQ0FBQSxDQUFDLElBQUk7WUFDekUsSUFBSSxvQkFBTyxDQUFDLFlBQVksRUFBRTtnQkFDdEIsRUFBRSxHQUFHLG9CQUFPLENBQUMsWUFBWSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUUsQ0FBQTthQUN2RDtZQUNELEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtZQUNqQyx1QkFBVSxDQUFDLGtCQUFrQixDQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxLQUFLLEVBQUUsZUFBZSxFQUFFLE9BQU8sQ0FBQyxDQUFBO1lBQ2xGLElBQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtZQUMzQyxNQUFNLENBQUMsT0FBTyxHQUFHLEdBQUcsQ0FBQTtZQUNwQixNQUFNLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxlQUFlLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLENBQUMsQ0FBQTtTQUNyRzthQUFNLElBQUksSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLEVBQUUsRUFBRSxVQUFVO1lBQ25DLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtZQUN6QyxJQUFJLFdBQVcsRUFBRSxFQUFFLFNBQVM7Z0JBQ3hCLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsaUJBQWlCLEVBQUUsV0FBVyxDQUFDLGNBQWMsQ0FBQyxDQUFBO2FBQ25IO2lCQUFNO2dCQUNILElBQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLE1BQU0sR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEVBQUUsVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGlCQUFTLENBQUMsU0FBUyxDQUFDLENBQUE7Z0JBQ3pJLENBQUMsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLDBCQUEwQixFQUFFLFVBQVUsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxvQkFBa0IsU0FBUyxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsTUFBTSxDQUFDLFNBQU0sQ0FBQyxDQUFBO2FBQ3pMO1NBQ0o7YUFBTSxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxTQUFTO1lBQzFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsa0JBQWtCLEVBQUUsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUscUJBQXFCLENBQUMsQ0FBQyxDQUFBO1NBQ3JKO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCw2Q0FBYyxHQUF0QjtRQUNJLElBQU0sRUFBRSxHQUFHLG9CQUFPLENBQUMscUJBQXFCLENBQUMsZUFBTyxDQUFDLEtBQUssQ0FBQyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLGVBQU8sQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUNqRyxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDcEIsQ0FBQztJQUVELFNBQVM7SUFDRCwrQ0FBZ0IsR0FBeEIsVUFBeUIsSUFBYzs7UUFDbkMsSUFBSSxHQUFHLElBQUksSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUN2QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDMUQsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDLElBQUssT0FBQSxDQUFDLENBQUMsV0FBVyxHQUFHLENBQUMsQ0FBQyxXQUFXLEVBQTdCLENBQTZCLENBQUMsQ0FBQTtRQUNsRCxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixFQUFFLENBQUE7UUFDL0MsSUFBSSxJQUFJLEdBQUcsQ0FBQyxDQUFBO1FBQ1osV0FBVztRQUNYLElBQU0sVUFBVSxHQUFHLENBQUMsR0FBRyxvQkFBTyxDQUFDLHFCQUFxQixDQUFDLGVBQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQTtRQUN6RSxJQUFJLENBQUMsS0FBSyxDQUFDLHNCQUFzQixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLFVBQVUsR0FBRyxHQUFHLENBQUE7UUFDaEcsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFVBQVUsRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUNqQyxJQUFJLEVBQUUsR0FBRyxJQUFJLEVBQUUsSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUM3QixJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUU7Z0JBQ1QsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxDQUFDLENBQUE7YUFDeEM7aUJBQU07Z0JBQ0gsSUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFBO2dCQUN6RCxJQUFJLGFBQWEsSUFBSSxDQUFDLElBQUksYUFBYSxHQUFHLFVBQVUsR0FBRyxDQUFDLEVBQUU7b0JBQ3RELElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLFVBQVUsR0FBRyxDQUFDLEVBQUUsY0FBUSxDQUFDLENBQUMsQ0FBQTtpQkFDakQ7Z0JBQ0QsRUFBRSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUE7YUFDN0M7WUFDRCxFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtZQUNkLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFBLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLDBDQUFFLE1BQU0sS0FBSSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNoRSxJQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFBO1lBQy9FLE1BQUEsRUFBRSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQywwQ0FBRSxTQUFTLENBQUMsR0FBRyxFQUFDO1lBQzNDLE1BQUEsRUFBRSxDQUFDLEtBQUssQ0FBQyxlQUFlLENBQUMsMENBQUUsU0FBUyxDQUFDLEdBQUcsRUFBQztZQUN6QyxJQUFJLElBQUksRUFBRTtnQkFDTixxQkFBUyxDQUFDLGdCQUFnQixDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTthQUNqRTtZQUNELElBQUksQ0FBQyxLQUFLLENBQUMsRUFBRTtnQkFDVCxJQUFJLElBQUksQ0FBQSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsUUFBUSxLQUFJLENBQUMsQ0FBQTthQUM5QjtpQkFBTSxJQUFJLElBQUksRUFBRTtnQkFDYixJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ3JELHFCQUFTLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxFQUFFLFFBQVEsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7Z0JBQ3RELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtnQkFDbkMsSUFBSSxJQUFJLEtBQUssQ0FBQTtnQkFDYixNQUFBLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsMENBQUUsSUFBSSxHQUFFO2dCQUNsQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFBO2dCQUNqQyxRQUFRLENBQUMsU0FBUyxHQUFHLEtBQUssR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFBO2dCQUMxQyxJQUFNLEVBQUUsR0FBRyxLQUFLLEdBQUcsS0FBSyxDQUFBO2dCQUN4QixFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFBO2dCQUN2QyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLEVBQUUsU0FBUyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUE7YUFDbkY7aUJBQU07Z0JBQ0gsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUE7YUFDOUM7U0FDSjtRQUNELElBQUksQ0FBQyxLQUFLLENBQUMsWUFBWSxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksR0FBRyxDQUFDLENBQUE7UUFDMUMsSUFBSSxJQUFJLEdBQUcsQ0FBQyxFQUFFO1lBQ1YsSUFBSSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLFlBQVksQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSyxJQUFJLENBQUMsQ0FBQyxDQUFDLG1CQUFtQixDQUFDLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1lBQ2pJLElBQUksQ0FBQyxLQUFLLENBQUMscUJBQXFCLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDLENBQUE7U0FDckU7SUFDTCxDQUFDO0lBRU8sK0NBQWdCLEdBQXhCO1FBQUEsaUJBa0JDO1FBakJHLElBQUksb0JBQU8sQ0FBQyxNQUFNLENBQUMsZUFBZSxFQUFFLEVBQUU7WUFDbEMsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtTQUN0RDtRQUNELElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUNuQyxPQUFPLHVCQUFVLENBQUMsT0FBTyxDQUFDLG1CQUFtQixFQUFFLFVBQUMsSUFBWTtZQUN4RCxJQUFJLEtBQUksQ0FBQyxPQUFPLEVBQUU7Z0JBQ2QsS0FBSSxDQUFDLGNBQWMsR0FBRyxJQUFJLGlCQUFPLEVBQUUsQ0FBQyxJQUFJLENBQUMsS0FBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUUsb0JBQU8sQ0FBQyxNQUFNLEVBQUUsRUFBRSxJQUFJLENBQUMsQ0FBQTtnQkFDbEYsS0FBSSxDQUFDLHFCQUFxQixHQUFHLEVBQUUsQ0FBQTtnQkFDL0IsSUFBSSxDQUFDLEtBQUksQ0FBQyxVQUFVLEVBQUU7b0JBQ2xCLEtBQUksQ0FBQyxVQUFVLEdBQUcsRUFBUyxDQUFBO2lCQUM5QjtnQkFDRCxLQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsR0FBRyxLQUFJLENBQUMsY0FBYyxDQUFDLEdBQUcsQ0FBQTtnQkFDN0MsSUFBTSxJQUFJLEdBQUcsS0FBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQ3JDLEtBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFBO2dCQUMvQixLQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQTthQUNuQztRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVELGFBQWE7SUFDTCxzREFBdUIsR0FBL0IsVUFBZ0MsR0FBWTtRQUN4QyxPQUFNLENBQUEsT0FBTztRQUNiLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3JDLElBQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsRUFBRSxNQUFNLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUM5RSxJQUFJLE1BQU0sQ0FBQyxNQUFNLEdBQUcsR0FBRyxFQUFFO1lBQ3JCLElBQU0sS0FBSyxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFBO1lBQ3RDLEVBQUUsQ0FBQyxjQUFjLEVBQUUsQ0FBQTtZQUNuQixJQUFJLEtBQUssSUFBSSxDQUFDLEVBQUU7Z0JBQ1osRUFBRSxDQUFDLFlBQVksRUFBRSxDQUFBO2FBQ3BCO1lBQ0QsSUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDakMsSUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLGVBQWUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQzNDLE1BQU0sQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFBO1NBQ3hDO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQywwQ0FBVyxHQUFuQixVQUFvQixJQUFhLEVBQUUsSUFBb0I7UUFDbkQsSUFBSSxDQUFDLElBQUksRUFBRTtZQUNQLE9BQU07U0FDVDtRQUNELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7UUFDeEIsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQTtRQUNwQixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFBO1FBQ3RCLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQTtRQUNsQyxxQkFBUyxDQUFDLGtCQUFrQixDQUFDLEVBQUUsS0FBSyxPQUFBLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxJQUFJLEVBQUUsR0FBRyxLQUFBLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFBLEdBQUc7O1lBQ3RFLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRTtnQkFDVCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQTthQUN2QztpQkFBTTtnQkFDSCxJQUFNLE1BQUksR0FBRyxHQUFHLENBQUMsSUFBSSxDQUFBO2dCQUNyQixvQkFBTyxDQUFDLE1BQU0sQ0FBQyx3QkFBd0IsQ0FBQyxNQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ3BELE1BQUEsb0JBQU8sQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQywwQ0FBRSxvQkFBb0IsQ0FBQyxNQUFJLENBQUMsSUFBSSxFQUFDO2dCQUNsRSxvQkFBTyxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxNQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ2hELG9CQUFPLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxDQUFBO2dCQUM1QixVQUFJLE1BQUksQ0FBQyxRQUFRLDBDQUFFLE1BQU0sRUFBRTtvQkFDdkIsdUJBQVUsQ0FBQyxPQUFPLENBQUMsb0JBQW9CLEVBQUU7d0JBQ3JDLElBQUksRUFBRSxTQUFTLENBQUMsQ0FBQyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxxQkFBcUI7d0JBQzVELEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRTt3QkFDWCxJQUFJLEVBQUUsTUFBSSxDQUFDLFFBQVE7cUJBQ3RCLENBQUMsQ0FBQTtpQkFDTDthQUNKO1FBQ0wsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBM3RCZ0Isb0JBQW9CO1FBRHhDLE9BQU87T0FDYSxvQkFBb0IsQ0E0dEJ4QztJQUFELDJCQUFDO0NBNXRCRCxBQTR0QkMsQ0E1dEJpRCxFQUFFLENBQUMsV0FBVyxHQTR0Qi9EO2tCQTV0Qm9CLG9CQUFvQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFSTVlfUEFXTl9NQVhfQ09VTlQsIFBBV05fU0xPVF9DT05GIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xuaW1wb3J0IHsgZWNvZGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VDb2RlXCI7XG5pbXBvcnQgeyBBcm15U3RhdGUsIEJVSUxEX05JRCwgQ0VmZmVjdCwgQ1R5cGUsIE5vTG9uZ2VyVGlwS2V5LCBQYXduVHlwZSwgUHJlZmVyZW5jZUtleSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIjtcbmltcG9ydCBFdmVudFR5cGUgZnJvbSBcIi4uLy4uL2NvbW1vbi9ldmVudC9FdmVudFR5cGVcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgQXJlYUNlbnRlck1vZGVsIGZyb20gXCIuLi8uLi9tb2RlbC9hcmVhL0FyZWFDZW50ZXJNb2RlbFwiO1xuaW1wb3J0IEFybXlPYmogZnJvbSBcIi4uLy4uL21vZGVsL2FyZWEvQXJteU9ialwiO1xuaW1wb3J0IEJ1aWxkT2JqIGZyb20gXCIuLi8uLi9tb2RlbC9hcmVhL0J1aWxkT2JqXCI7XG5pbXBvcnQgUGF3bk9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9QYXduT2JqXCI7XG5pbXBvcnQgUGF3bkRyaWxsSW5mb09iaiBmcm9tIFwiLi4vLi4vbW9kZWwvbWFpbi9QYXduRHJpbGxJbmZvT2JqXCI7XG5pbXBvcnQgVXNlck1vZGVsIGZyb20gXCIuLi8uLi9tb2RlbC9jb21tb24vVXNlck1vZGVsXCI7XG5pbXBvcnQgUGxheWVyTW9kZWwgZnJvbSBcIi4uLy4uL21vZGVsL21haW4vUGxheWVyTW9kZWxcIjtcbmltcG9ydCBQYXduU2xvdE9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvbWFpbi9QYXduU2xvdE9ialwiO1xuaW1wb3J0IHsgQXJteUl0ZW0gfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCI7XG5pbXBvcnQgeyBJUGF3bkRyaWxsSW5mbyB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvSW50ZXJmYWNlXCI7XG5pbXBvcnQgeyBuZXRIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9OZXRIZWxwZXJcIjtcbmltcG9ydCBCdWlsZFVubG9ja1RpcENtcHQgZnJvbSBcIi4uL2NtcHQvQnVpbGRVbmxvY2tUaXBDbXB0XCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJ1aWxkQmFycmFja3NQbmxDdHJsIGV4dGVuZHMgbWMuQmFzZVBubEN0cmwge1xuXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cbiAgICBwcml2YXRlIHRhYnNUY186IGNjLlRvZ2dsZUNvbnRhaW5lciA9IG51bGwgLy8gcGF0aDovL3Jvb3QvdGFic190Y190Y2VcbiAgICBwcml2YXRlIHBhZ2VzTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L3BhZ2VzX25cbiAgICBwcml2YXRlIHVubG9ja1RpcE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9wYWdlc19uLzAvYm90dG9tL3RpdGxlL3VubG9ja190aXBfblxuICAgIHByaXZhdGUgcXVldWVTdl86IGNjLlNjcm9sbFZpZXcgPSBudWxsIC8vIHBhdGg6Ly9yb290L3BhZ2VzX24vMS9kcmlsbC9jb250ZW50L3F1ZXVlX3N2XG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIFBLRVlfVEFCOiBzdHJpbmcgPSAnJ1xuICAgIHByaXZhdGUgUEtFWV9TRUxFQ1RfQVJNWTogc3RyaW5nID0gJydcblxuICAgIHByaXZhdGUgdXNlcjogVXNlck1vZGVsID0gbnVsbFxuICAgIHByaXZhdGUgcGxheWVyOiBQbGF5ZXJNb2RlbCA9IG51bGxcbiAgICBwcml2YXRlIGFyZWFDZW50ZXI6IEFyZWFDZW50ZXJNb2RlbCA9IG51bGxcbiAgICBwcml2YXRlIGRhdGE6IEJ1aWxkT2JqID0gbnVsbFxuXG4gICAgcHJpdmF0ZSB0YWI6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIHNlbGVjdFBhd25TbG90OiBQYXduU2xvdE9iaiA9IG51bGxcbiAgICBwcml2YXRlIHNlbGVjdEFybXk6IEFybXlJdGVtID0gbnVsbFxuICAgIHByaXZhdGUgZHJpbGxQcm9ncmVzc1R3ZWVuOiB7IFtrZXk6IG51bWJlcl06IGNjLlR3ZWVuIH0gPSB7fVxuICAgIHByaXZhdGUgdGVtcEFybXlTb3J0V2VpZ2h0TWFwOiB7IFtrZXk6IHN0cmluZ106IG51bWJlciB9ID0ge31cbiAgICBwcml2YXRlIHRlbXBDcmVhdGVBcm15OiBBcm15T2JqID0gbnVsbFxuICAgIHByaXZhdGUgYXJteUxpc3Q6IEFybXlJdGVtW10gPSBbXVxuICAgIHByaXZhdGUgdW5sb2NrVGlwQ21wdDogQnVpbGRVbmxvY2tUaXBDbXB0ID0gbnVsbFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQlVJTERfTFZdOiB0aGlzLm9uVXBkYXRlQnVpbGRMdiwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfUEFXTl9EUklMTF9RVUVVRV06IHRoaXMub25VcGRhdGVQYXduRHJpbGxRdWV1ZSwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5BUkVBX0JBVFRMRV9CRUdJTl06IHRoaXMub25BcmVhQmF0dGxlQmVnaW4sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQVJFQV9CQVRUTEVfRU5EXTogdGhpcy5vbkFyZWFCYXR0bGVFbmQsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQ0hBTkdFX1BBV05fU0tJTl06IHRoaXMub25DaGFuZ2VQYXduU2tpbiwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5DSEFOR0VfUEFXTl9FUVVJUF06IHRoaXMub25DaGFuZ2VQYXduRXF1aXAsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQUREX0FSTVldOiB0aGlzLm9uVXBkYXRlQXJteSwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5SRU1PVkVfQVJNWV06IHRoaXMub25VcGRhdGVBcm15LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9BUk1ZXTogdGhpcy5vblVwZGF0ZUFybXksIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FMTF9BUk1ZXTogdGhpcy5vblVwZGF0ZUFybXksIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FSTVlfRElTVF9JTkZPXTogdGhpcy5vblVwZGF0ZUFybXksIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0JBVFRMRV9BUk1ZX0JZX1VJXTogdGhpcy5vblVwZGF0ZUFybXksIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX1BBV05fU0xPVFNdOiB0aGlzLm9uVXBkYXRlUGF3blNsb3RzLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkJBUlJBQ0tTX1NFTEVDVF9QQVdOXTogdGhpcy5vbkJhcnJhY2tzU2VsZWN0UGF3biwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgXVxuICAgIH1cblxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcbiAgICAgICAgdGhpcy51c2VyID0gdGhpcy5nZXRNb2RlbCgndXNlcicpXG4gICAgICAgIHRoaXMucGxheWVyID0gdGhpcy5nZXRNb2RlbCgncGxheWVyJylcbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyID0gdGhpcy5nZXRNb2RlbCgnYXJlYUNlbnRlcicpXG4gICAgICAgIHRoaXMudW5sb2NrVGlwQ21wdCA9IHRoaXMudW5sb2NrVGlwTm9kZV8uQ29tcG9uZW50KEJ1aWxkVW5sb2NrVGlwQ21wdClcbiAgICB9XG5cbiAgICBwdWJsaWMgb25FbnRlcihkYXRhOiBCdWlsZE9iaiwgdGFiPzogbnVtYmVyKSB7XG4gICAgICAgIHRoaXMuUEtFWV9UQUIgPSAnQkFSUkFDS1NfVEFCXycgKyBkYXRhLmlkXG4gICAgICAgIHRoaXMuUEtFWV9TRUxFQ1RfQVJNWSA9ICdCQVJSQUNLU19TRUxFQ1RfQVJNWV9UQUJfJyArIGRhdGEuaWRcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YVxuICAgICAgICB0aGlzLnRlbXBDcmVhdGVBcm15ID0gdGhpcy51c2VyLmdldFRlbXBQcmVmZXJlbmNlTWFwKFByZWZlcmVuY2VLZXkuVEVNUF9DUkVBVEVfQVJNWSlcbiAgICAgICAgY29uc3QgY29uZCA9IHRoaXMucGFnZXNOb2RlXy5DaGlsZCgnMS9pbmZvL2NvbmQnKVxuICAgICAgICBjb25kLkNoaWxkKCduZWVkL3RpdGxlL3ZhbCcpLnNldExvY2FsZUtleSgndWkuZHJpbGxfY29zdCcsICd1aS5idXR0b25fZHJpbGwnKVxuICAgICAgICB0aGlzLnBhZ2VzTm9kZV8uQ2hpbGQoJzEvZHJpbGwvdGl0bGUvYmcvdmFsJykuc2V0TG9jYWxlS2V5KCd1aS5kcmlsbF9xdWV1ZScsICd1aS5idXR0b25fZHJpbGwnKVxuICAgICAgICB0aGlzLnRhYnNUY18uVGFicyh0YWIgPz8gKHRoaXMudXNlci5nZXRUZW1wUHJlZmVyZW5jZU1hcCh0aGlzLlBLRVlfVEFCKSB8fCAwKSlcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgICAgIHRoaXMuc2VsZWN0UGF3blNsb3QgPSBudWxsXG4gICAgICAgIHRoaXMuc2VsZWN0QXJteSA9IG51bGxcbiAgICAgICAgdGhpcy50ZW1wQXJteVNvcnRXZWlnaHRNYXAgPSB7fVxuICAgICAgICB0aGlzLnNob3dDcmVhdGVBcm15RmluZ2VyVGlwKGZhbHNlKVxuICAgICAgICB0aGlzLnVzZXIuc2V0VGVtcFByZWZlcmVuY2VEYXRhKFByZWZlcmVuY2VLZXkuVEVNUF9DUkVBVEVfQVJNWSwgdGhpcy50ZW1wQ3JlYXRlQXJteSlcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICAgICAgYXNzZXRzTWdyLnJlbGVhc2VUZW1wUmVzQnlUYWcodGhpcy5rZXkpXG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L3RhYnNfdGNfdGNlXG4gICAgb25DbGlja1RhYnMoZXZlbnQ6IGNjLlRvZ2dsZSwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgICFkYXRhICYmIGF1ZGlvTWdyLnBsYXlTRlgoJ2NsaWNrJylcbiAgICAgICAgY29uc3QgdHlwZSA9IHRoaXMudGFiID0gTnVtYmVyKGV2ZW50Lm5vZGUubmFtZSlcbiAgICAgICAgdGhpcy51c2VyLnNldFRlbXBQcmVmZXJlbmNlRGF0YSh0aGlzLlBLRVlfVEFCLCB0eXBlKVxuICAgICAgICBjb25zdCBub2RlID0gdGhpcy5wYWdlc05vZGVfLlN3aWgodHlwZSlbMF1cbiAgICAgICAgaWYgKHR5cGUgPT09IDApIHtcbiAgICAgICAgICAgIHRoaXMudW5sb2NrVGlwQ21wdC51cGRhdGVJbmZvKHRoaXMuZGF0YSwgUEFXTl9TTE9UX0NPTkYsIHRoaXMua2V5KVxuICAgICAgICAgICAgdmlld0hlbHBlci5fdXBkYXRlQnVpbGRCYXNlSW5mbyhub2RlLkNoaWxkKCdpbmZvL3RvcCcpLCB0aGlzLmRhdGEsIHRoaXMua2V5KVxuICAgICAgICAgICAgdmlld0hlbHBlci5fdXBkYXRlQnVpbGRBdHRySW5mbyh0aGlzLmRhdGEsIG5vZGUuQ2hpbGQoJ2luZm8vYXR0cnMnKSwgbm9kZS5DaGlsZCgnYm90dG9tJyksIHRoaXMuZGF0YS5nZXRFZmZlY3RzRm9yVmlldygpLCB0aGlzLmtleSlcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAxKSB7XG4gICAgICAgICAgICAvLyDmmL7npLrlvZPliY3nmoTlhpvpmJ/liJfooahcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0QXJteSA9IG51bGxcbiAgICAgICAgICAgIHRoaXMudGVtcEFybXlTb3J0V2VpZ2h0TWFwID0ge31cbiAgICAgICAgICAgIHRoaXMudXBkYXRlQXJteUxpc3QodHJ1ZSwgbm9kZSlcbiAgICAgICAgICAgIC8vIOaYvuekuuWPr+iuree7g+eahOWjq+WFtVxuICAgICAgICAgICAgdGhpcy5zZWxlY3RQYXduU2xvdCA9IG51bGxcbiAgICAgICAgICAgIHRoaXMudXBkYXRlUGF3bkxpc3QodHJ1ZSwgbm9kZSlcbiAgICAgICAgICAgIC8vIOiuree7g+WIl+ihqFxuICAgICAgICAgICAgdGhpcy51cGRhdGVEcmlsbFF1ZXVlKG5vZGUpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9wYWdlc19uLzAvYm90dG9tL2J1dHRvbnMvdXBfYmVcbiAgICBvbkNsaWNrVXAoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBnYW1lSHByLmNsaWNrQnVpbGRVcCh0aGlzLmRhdGEsIHRoaXMpXG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3QvcGFnZXNfbi8xL2luZm8vcGF3bi9saXN0L3ZpZXcvY29udGVudC9wYXduX2JlXG4gICAgb25DbGlja1Bhd24oZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIF86IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGNvbnN0IGl0ID0gZXZlbnQudGFyZ2V0LCBzbG90OiBQYXduU2xvdE9iaiA9IGl0LkRhdGFcbiAgICAgICAgaWYgKCFzbG90KSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmIChzbG90LnVpZCA9PT0gdGhpcy5zZWxlY3RQYXduU2xvdD8udWlkKSB7XG4gICAgICAgICAgICBpZiAoc2xvdC5wYXduKSB7XG4gICAgICAgICAgICAgICAgZ2FtZUhwci5zZXROb0xvbmdlclRpcCgnbG9va19wYXduX2luZm8nLCB0cnVlKVxuICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnYXJlYS9QYXduSW5mbycsIHNsb3QucGF3bilcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKCFzbG90LmlkICYmIHNsb3QubHYgPiAwICYmIHRoaXMuZGF0YS5sdiA+PSBzbG90Lmx2KSB7XG4gICAgICAgICAgICBpZiAoc2xvdC5zZWxlY3RJZHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2VfdW5sb2NrX3ByZXZfc2xvdCcsIHsgcGFyYW1zOiBbJ3VpLmNlcmlfdHlwZV9uYW1lXzInXSB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd1BubCgnYnVpbGQvU3R1ZHlTZWxlY3QnLCBzbG90KVxuICAgICAgICB9XG4gICAgICAgIHRoaXMudXBkYXRlUGF3blNlbGVjdChzbG90LCBmYWxzZSlcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9wYWdlc19uLzEvYXJteS9saXN0L3ZpZXcvY29udGVudC9hcm15X2JlXG4gICAgb25DbGlja0FybXkoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIF86IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGNvbnN0IGl0ID0gZXZlbnQudGFyZ2V0LCBkYXRhOiBBcm15SXRlbSA9IGl0LkRhdGFcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICBpZiAodGhpcy50ZW1wQ3JlYXRlQXJteSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QueWV0X2hhc19lbXB0eV9hcm15JylcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0aGlzLnNob3dDcmVhdGVBcm15VUkoKVxuICAgICAgICB9IC8qIGVsc2UgaWYgKCFkYXRhLmFybXkpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QuYXJteV9ub3RfaW5fbWFpbmNpdHknKVxuICAgICAgICB9ICovIGVsc2UgaWYgKGRhdGEudWlkICE9PSB0aGlzLnNlbGVjdEFybXk/LnVpZCkge1xuICAgICAgICAgICAgdGhpcy51c2VyLnNldFRlbXBQcmVmZXJlbmNlRGF0YSh0aGlzLlBLRVlfU0VMRUNUX0FSTVksIGRhdGEudWlkKVxuICAgICAgICAgICAgdGhpcy51cGRhdGVBcm15U2VsZWN0KGRhdGEpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9wYWdlc19uLzEvaW5mby9jb25kL25lZWQvYnV0dG9ucy9kcmlsbF9iZVxuICAgIG9uQ2xpY2tEcmlsbChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IHNsb3QgPSB0aGlzLnNlbGVjdFBhd25TbG90LCBzdGF0ZSA9IGV2ZW50LnRhcmdldC5EYXRhXG4gICAgICAgIGlmICghc2xvdCkge1xuICAgICAgICAgICAgaWYgKCF0aGlzLnNlbGVjdEFybXkpIHsgLy8g5rKh5pyJ5Yab6Zif77yM5o+Q56S65Yib5bu65Yab6ZifXG4gICAgICAgICAgICAgICAgaWYgKGdhbWVIcHIuaXNOb0xvbmdlclRpcChOb0xvbmdlclRpcEtleS5TSE9XX0JVSUxEX0JBUlJBQ0tTX0RSSUxMKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnBsZWFzZV9jcmVhdGVfYXJteScsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNiOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCAmJiAhdGhpcy5wbGF5ZXIuaXNBcm15Q291bnRGdWxsKCkgJiYgIWdhbWVIcHIuaXNOb0xvbmdlclRpcCgnbm9fYXJteScpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvd0NyZWF0ZUFybXlGaW5nZXJUaXAodHJ1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXJteSA9IHRoaXMucGxheWVyLmdldEJhc2VBcm15cygpWzBdXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dQbmwoJ2J1aWxkL0J1aWxkQmFycmFja3NUaXAnLCBhcm15LCAndWkuYnV0dG9uX2RyaWxsJylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgeyAvLyDmnInlhpvpmJ/vvIzkvYbmmK/lpJblh7rvvIzlhYjmj5DnpLrlhpvpmJ9cbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuc2VsZWN0QXJteS5hcm15KSB7IC8vIOWkluWHulxuICAgICAgICAgICAgICAgICAgICBpZiAoZ2FtZUhwci5pc05vTG9uZ2VyVGlwKE5vTG9uZ2VyVGlwS2V5LlNIT1dfQlVJTERfQkFSUkFDS1NfRFJJTEwpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LmFybXlfbm90X2luX21haW5jaXR5JylcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGFybXkgPSB0aGlzLnBsYXllci5nZXRCYXNlQXJteXMoKVswXVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd1BubCgnYnVpbGQvQnVpbGRCYXJyYWNrc1RpcCcsIGFybXksICd1aS5idXR0b25fZHJpbGwnKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLnBsYXllci5nZXRDYW5SZWNydWl0UGF3bnModGhpcy5kYXRhLmlkKS5maWx0ZXIobSA9PiAhIW0ucGF3bikubGVuZ3RoID09PSAwKSB7IC8vIOayoeacieWjq+WFte+8jOaPkOekuueCueWHu+WKoOWPt+ino+mUgVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3VpLnBsZWFzZV9zZWxlY3RfcGF3bl9zdHVkeScpXG4gICAgICAgICAgICAgICAgfSBlbHNlIHsgLy8g5bey5pyJ5aOr5YW177yM5o+Q56S66YCJ5oup5LiA5Liq5aOr5YW16L+b6KGM5oub5YufXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QucGxlYXNlX3NlbGVjdF9jZWlyX2l0ZW0nLCB7IHBhcmFtczogWyd1aS5jZXJpX3R5cGVfbmFtZV8yJ10gfSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoc3RhdGUgPT09IDEpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5BUk1ZX1BBV05fRlVMTClcbiAgICAgICAgfSBlbHNlIGlmIChzdGF0ZSA9PT0gMikgeyAvLyDlhpvpmJ/kuI3lnKjkuLvln47lhoVcbiAgICAgICAgICAgIGlmIChnYW1lSHByLmlzTm9Mb25nZXJUaXAoTm9Mb25nZXJUaXBLZXkuU0hPV19CVUlMRF9CQVJSQUNLU19EUklMTCkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LmFybXlfbm90X2luX21haW5jaXR5JylcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd1BubCgnYnVpbGQvQnVpbGRCYXJyYWNrc1RpcCcsIHRoaXMucGxheWVyLmdldEJhc2VBcm15cygpWzBdLCAndWkuYnV0dG9uX2RyaWxsJylcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhcmVhID0gdGhpcy5hcmVhQ2VudGVyLmdldEFyZWEodGhpcy5kYXRhLmFJbmRleClcbiAgICAgICAgaWYgKCFhcmVhKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmICghdGhpcy5zZWxlY3RBcm15KSB7XG4gICAgICAgICAgICBpZiAoYXJlYS5hcm15cy5sZW5ndGggPT09IDAgfHwgYXJlYS5hcm15cy5ldmVyeShtID0+IG0ucGF3bnMubGVuZ3RoID49IEFSTVlfUEFXTl9NQVhfQ09VTlQpKSB7IC8v5LiA5Liq5Yab6Zif5Lmf5rKh5pyJXG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnBsZWFzZV9jcmVhdGVfYXJteScsIHtcbiAgICAgICAgICAgICAgICAgICAgY2I6ICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmlzVmFsaWQgJiYgIXRoaXMucGxheWVyLmlzQXJteUNvdW50RnVsbCgpICYmICFnYW1lSHByLmlzTm9Mb25nZXJUaXAoJ25vX2FybXknKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvd0NyZWF0ZUFybXlGaW5nZXJUaXAodHJ1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2Vfc2VsZWN0X2FybXknKVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgbGV0IHNlbGVjdEFybXlVaWQgPSB0aGlzLnNlbGVjdEFybXkudWlkLCB0ZW1wQXJteVVpZCA9ICcnLCBhcm15TmFtZSA9ICcnXG4gICAgICAgIGNvbnN0IGFybXkgPSBhcmVhLmdldEFybXlCeVVpZChzZWxlY3RBcm15VWlkKSB8fCB0aGlzLmdldFRlbXBDcmVhdGVBcm15KHNlbGVjdEFybXlVaWQpXG4gICAgICAgIGlmICghYXJteSkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVjb2RlLkFSTVlfTk9UX0VYSVNUKVxuICAgICAgICB9IGVsc2UgaWYgKGFybXkudWlkLnN0YXJ0c1dpdGgoJ3RlbXBfJykpIHtcbiAgICAgICAgICAgIGFybXlOYW1lID0gYXJteS5uYW1lXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0ZW1wQXJteVVpZCA9IGFybXkudWlkXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyLmRyaWxsUGF3blRvU2VydmVyKHRoaXMuZGF0YS5hSW5kZXgsIHRoaXMuZGF0YS51aWQsIHNsb3QuaWQsIHRlbXBBcm15VWlkLCBhcm15TmFtZSkudGhlbihyZXMgPT4ge1xuICAgICAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzLmVyciA9PT0gZWNvZGUuVEVYVF9MRU5fTElNSVQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnRleHRfbGVuX2xpbWl0X25hbWUnKVxuICAgICAgICAgICAgfSBlbHNlIGlmIChyZXMuZXJyID09PSBlY29kZS5URVhUX0hBU19TRU5TSVRJVkUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0Lmhhc19zZW5zaXRpdmVfd29yZF9uYW1lJylcbiAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzLmVyciA9PT0gZWNvZGUuQU5USV9DSEVBVCkge1xuICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9BbnRpQ2hlYXQnKVxuICAgICAgICAgICAgfSBlbHNlIGlmIChyZXMuZXJyKSB7XG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5zaG93QWxlcnQocmVzLmVycilcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgYXVkaW9NZ3IucGxheVNGWCgnYnVpbGQvc291bmRfdWlfMDAnICsgKHRoaXMuZGF0YS5pZCA9PT0gMjAxMCA/ICc3JyA6ICc2JykpXG4gICAgICAgICAgICAgICAgY29uc3QgYXJteSA9IHJlcy5hcm15XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMudGVtcENyZWF0ZUFybXk/LnVpZCA9PT0gc2VsZWN0QXJteVVpZCkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnRlbXBDcmVhdGVBcm15ID0gbnVsbFxuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5zZWxlY3RBcm15KSB0aGlzLnNlbGVjdEFybXkudWlkID0gYXJteS51aWRcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMudGVtcEFybXlTb3J0V2VpZ2h0TWFwW3NlbGVjdEFybXlVaWRdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRlbXBBcm15U29ydFdlaWdodE1hcFthcm15LnVpZF0gPSB0aGlzLnRlbXBBcm15U29ydFdlaWdodE1hcFtzZWxlY3RBcm15VWlkXVxuICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRlIHRoaXMudGVtcEFybXlTb3J0V2VpZ2h0TWFwW3NlbGVjdEFybXlVaWRdXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5wbGF5ZXIuZ2V0QmFzZUFybXlzKCkucHVzaCh7XG4gICAgICAgICAgICAgICAgICAgICAgICBpbmRleDogdGhpcy5kYXRhLmFJbmRleCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHVpZDogYXJteS51aWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBhcm15Lm5hbWUsIC8v5Yab6Zif5ZCN5a2XXG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZTogQXJteVN0YXRlLkRSSUxMLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGF3bnM6IFtdLCAvL+Wjq+WFteWIl+ihqFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyB0aGlzLnVwZGF0ZURyaWxsUXVldWUoKVxuICAgICAgICAgICAgICAgIC8vIGNvbnN0IG5vZGUgPSB0aGlzLnBhZ2VzTm9kZV8uQ2hpbGQoMSlcbiAgICAgICAgICAgICAgICAvLyB0aGlzLnVwZGF0ZUFybXlMaXN0KGZhbHNlLCBub2RlKVxuICAgICAgICAgICAgICAgIC8vIHRoaXMudXBkYXRlUmVjcnVpdENvc3Qobm9kZSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9wYWdlc19uLzEvZHJpbGwvY29udGVudC8wL2RyaWxsX3Bhd25fYmVcbiAgICBvbkNsaWNrRHJpbGxQYXduKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgZGF0YTogUGF3bkRyaWxsSW5mb09iaiA9IGV2ZW50LnRhcmdldC5wYXJlbnQuRGF0YVxuICAgICAgICBpZiAoZGF0YSkge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdhcmVhL1Bhd25JbmZvJywgdGhpcy5hcmVhQ2VudGVyLmNyZWF0ZVBhd25CeURyaWxsSW5mbyhkYXRhKSwgZGF0YSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L3BhZ2VzX24vMS9pbmZvL2NvbmQvbm9uZS9idXR0b25zL3Jlc3R1ZHkvc3R1ZHlfYmVcbiAgICBvbkNsaWNrU3R1ZHkoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBzbG90ID0gdGhpcy5zZWxlY3RQYXduU2xvdFxuICAgICAgICBpZiAoIXNsb3QpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKHNsb3Quc2VsZWN0SWRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2VfdW5sb2NrX3ByZXZfc2xvdCcsIHsgcGFyYW1zOiBbJ3VpLmNlcmlfdHlwZV9uYW1lXzInXSB9KVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnYnVpbGQvU3R1ZHlTZWxlY3QnLCBzbG90KVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L3BhZ2VzX24vMS9pbmZvL2NvbmQvbm9uZS9idXR0b25zL2NvbmQvYnV0dG9uL2dvdG9fd29yc2hpcF9iZVxuICAgIG9uQ2xpY2tHb3RvV29yc2hpcChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGlmICghdGhpcy5wbGF5ZXIuZ2V0QnVpbGRMdihCVUlMRF9OSUQuSEVST19IQUxMKSkge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnBsZWFzZV9idWlsZF9maXJzdCcsIHsgcGFyYW1zOiBbJ2J1aWxkVGV4dC5uYW1lXycgKyBCVUlMRF9OSUQuSEVST19IQUxMXSB9KVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd0J1aWxkSW5mb0J5TWFpbihCVUlMRF9OSUQuSEVST19IQUxMLCAxKS50aGVuKG9rID0+IHtcbiAgICAgICAgICAgIGlmIChvayAmJiB0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L3BhZ2VzX24vMS9kcmlsbC9jb250ZW50LzAvY2FuY2VsX2RyaWxsX2JlXG4gICAgb25DbGlja0NhbmNlbERyaWxsKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBldmVudC50YXJnZXQucGFyZW50LkRhdGEsIHBhd25PYmogPSB0aGlzLmFyZWFDZW50ZXIuY3JlYXRlUGF3bkJ5RHJpbGxJbmZvKGRhdGEpXG4gICAgICAgIGlmICghZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdXJwbHVzVGltZSA+IDApIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dNZXNzYWdlQm94KHBhd25PYmouaXNNYWNoaW5lKCkgPyAndWkuY2FuY2VsX3NjX25vX2JhY2tfY29zdF90aXAnIDogJ3VpLmNhbmNlbF9kcmlsbF9ub19iYWNrX2Nvc3RfdGlwJywge1xuICAgICAgICAgICAgICAgIG9rOiAoKSA9PiB0aGlzLmlzVmFsaWQgJiYgdGhpcy5jYW5jZWxEcmlsbChwYXduT2JqLCBkYXRhKSxcbiAgICAgICAgICAgICAgICBjYW5jZWw6ICgpID0+IHsgfSxcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jYW5jZWxEcmlsbChwYXduT2JqLCBkYXRhKVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgcHJpdmF0ZSBvblVwZGF0ZUJ1aWxkTHYoZGF0YTogQnVpbGRPYmopIHtcbiAgICAgICAgaWYgKHRoaXMuZGF0YS51aWQgPT09IGRhdGEudWlkKSB7XG4gICAgICAgICAgICBjb25zdCBub2RlID0gdGhpcy5wYWdlc05vZGVfLkNoaWxkKDApXG4gICAgICAgICAgICBub2RlLkNoaWxkKCdpbmZvL3RvcC9pY29uL2x2L3ZhbCcpLnNldExvY2FsZUtleSgndWkubHYnLCBkYXRhLmx2KVxuICAgICAgICAgICAgdmlld0hlbHBlci5fdXBkYXRlQnVpbGRBdHRySW5mbyh0aGlzLmRhdGEsIG5vZGUuQ2hpbGQoJ2luZm8vYXR0cnMnKSwgbm9kZS5DaGlsZCgnYm90dG9tJyksIHRoaXMuZGF0YS5nZXRFZmZlY3RzRm9yVmlldygpLCB0aGlzLmtleSlcbiAgICAgICAgICAgIHRoaXMudW5sb2NrVGlwQ21wdC51cGRhdGVJbmZvKHRoaXMuZGF0YSwgUEFXTl9TTE9UX0NPTkYsIHRoaXMua2V5LCBkYXRhLmx2KVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yi35paw6K6t57uD5YiX6KGoXG4gICAgcHJpdmF0ZSBvblVwZGF0ZVBhd25EcmlsbFF1ZXVlKCkge1xuICAgICAgICB0aGlzLnVwZGF0ZURyaWxsUXVldWUoKVxuICAgICAgICAvLyB0aGlzLnVwZGF0ZUFybXlMaXN0KGZhbHNlKVxuICAgICAgICB0aGlzLnVwZGF0ZVJlY3J1aXRDb3N0KClcbiAgICB9XG5cbiAgICAvLyDmiJjmlpflvIDlp4tcbiAgICBwcml2YXRlIG9uQXJlYUJhdHRsZUJlZ2luKGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMuZGF0YS5hSW5kZXggPT09IGluZGV4KSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZURyaWxsUXVldWUoKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5oiY5paX57uT5p2fXG4gICAgcHJpdmF0ZSBvbkFyZWFCYXR0bGVFbmQoaW5kZXg6IG51bWJlcikge1xuICAgICAgICBpZiAodGhpcy5kYXRhLmFJbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMudXBkYXRlRHJpbGxRdWV1ZSgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliIfmjaLlo6vlhbXnmq7ogqRcbiAgICBwcml2YXRlIG9uQ2hhbmdlUGF3blNraW4oZGF0YTogUGF3bk9iaikge1xuICAgICAgICBjb25zdCBub2RlID0gdGhpcy5wYWdlc05vZGVfLkNoaWxkKDEpXG4gICAgICAgIHRoaXMudXBkYXRlUGF3bkxpc3QoZmFsc2UsIG5vZGUpXG4gICAgICAgIHRoaXMudXBkYXRlRHJpbGxRdWV1ZShub2RlKVxuICAgIH1cblxuICAgIC8vIOWIh+aNouWjq+WFteijheWkh1xuICAgIHByaXZhdGUgb25DaGFuZ2VQYXduRXF1aXAoKSB7XG4gICAgICAgIHRoaXMudXBkYXRlUGF3bkxpc3QoZmFsc2UpXG4gICAgfVxuXG4gICAgLy8g6YeN5paw5Yi35paw5Yab6Zif5YiX6KGoXG4gICAgcHJpdmF0ZSBvblVwZGF0ZUFybXkoKSB7XG4gICAgICAgIHRoaXMudXBkYXRlQXJteUxpc3QoZmFsc2UpXG4gICAgfVxuXG4gICAgLy8g5Yi35paw5qe95L2NXG4gICAgcHJpdmF0ZSBvblVwZGF0ZVBhd25TbG90cygpIHtcbiAgICAgICAgaWYgKHRoaXMudGFiID09PSAxKSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZVBhd25MaXN0KGZhbHNlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g6YCJ5oup5aOr5YW1XG4gICAgcHJpdmF0ZSBvbkJhcnJhY2tzU2VsZWN0UGF3bihzbG90OiBQYXduU2xvdE9iaikge1xuICAgICAgICBpZiAoc2xvdCkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGVQYXduU2VsZWN0KHNsb3QsIHRydWUpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgZ2V0VGVtcENyZWF0ZUFybXkodWlkOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKHRoaXMudGVtcENyZWF0ZUFybXk/LnVpZCA9PT0gdWlkKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy50ZW1wQ3JlYXRlQXJteVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBhZGRBcm15VG9MaXN0KGRhdGE6IGFueSwgYXJteTogQXJteU9iaiwgcGF3bnM/OiBhbnlbXSkge1xuICAgICAgICBjb25zdCBpdGVtOiBBcm15SXRlbSA9IHtcbiAgICAgICAgICAgIG5hbWU6IGRhdGEubmFtZSxcbiAgICAgICAgICAgIHVpZDogZGF0YS51aWQsXG4gICAgICAgICAgICBwYXducyxcbiAgICAgICAgICAgIGFybXlcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMudGVtcEFybXlTb3J0V2VpZ2h0TWFwW2RhdGEudWlkXSkge1xuICAgICAgICAgICAgbGV0IHdlaWdodCA9IGl0ZW0uYXJteSA/IDIgOiAxXG4gICAgICAgICAgICB3ZWlnaHQgPSB3ZWlnaHQgKiAxMCArICg5IC0gKGl0ZW0uYXJteT8uZ2V0QWN0UGF3bkNvdW50KCkgfHwgMCkpXG4gICAgICAgICAgICB3ZWlnaHQgPSB3ZWlnaHQgKiAxMCArICg5IC0gKGl0ZW0uYXJteT8ucGF3bnMubGVuZ3RoIHx8IDApKVxuICAgICAgICAgICAgdGhpcy50ZW1wQXJteVNvcnRXZWlnaHRNYXBbZGF0YS51aWRdID0gd2VpZ2h0XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGl0ZW1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDlhpvpmJ/liJfooahcbiAgICBwcml2YXRlIHVwZGF0ZUFybXlMaXN0KGlzTG9jYXRpb246IGJvb2xlYW4sIG5vZGU/OiBjYy5Ob2RlKSB7XG4gICAgICAgIG5vZGUgPSBub2RlIHx8IHRoaXMucGFnZXNOb2RlXy5DaGlsZCgxKVxuICAgICAgICAvLyDlvZPliY3ljLrln5/nmoTlhpvpmJ9cbiAgICAgICAgY29uc3QgYXJlYUFybXlNYXA6IHsgW2tleTogc3RyaW5nXTogQXJteU9iaiB9ID0ge31cbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyLmdldEFyZWEodGhpcy5kYXRhLmFJbmRleCk/LmFybXlzLmZvckVhY2gobSA9PiB7XG4gICAgICAgICAgICBpZiAobS5pc0NhbkRyaWxsUGF3bigpKSB7XG4gICAgICAgICAgICAgICAgYXJlYUFybXlNYXBbbS51aWRdID0gbVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICB0aGlzLmFybXlMaXN0ID0gW251bGxdXG4gICAgICAgIC8vIOWFiOijheiHquW3seaJgOacieeahOWGm+mYnyDlho3oo4XkuLTml7bliJvlu7rnmoTlhpvpmJ9cbiAgICAgICAgdGhpcy5wbGF5ZXIuZ2V0QmFzZUFybXlzKCkuZm9yRWFjaChtID0+IHRoaXMuYXJteUxpc3QucHVzaCh0aGlzLmFkZEFybXlUb0xpc3QobSwgYXJlYUFybXlNYXBbbS51aWRdLCBtLnBhd25zKSkpXG4gICAgICAgIGlmICh0aGlzLnRlbXBDcmVhdGVBcm15KSB7XG4gICAgICAgICAgICB0aGlzLmFybXlMaXN0LnB1c2godGhpcy5hZGRBcm15VG9MaXN0KHRoaXMudGVtcENyZWF0ZUFybXksIHRoaXMudGVtcENyZWF0ZUFybXkpKVxuICAgICAgICB9XG4gICAgICAgIC8vIOaOkuS4quW6j1xuICAgICAgICB0aGlzLmFybXlMaXN0LnNvcnQoKGEsIGIpID0+IHRoaXMudGVtcEFybXlTb3J0V2VpZ2h0TWFwW2I/LnVpZF0gLSB0aGlzLnRlbXBBcm15U29ydFdlaWdodE1hcFthPy51aWRdKVxuICAgICAgICBjb25zdCBjb3VudE5vZGUgPSBub2RlLkNoaWxkKCdhcm15L3RpdGxlL2NvdW50X2JnJylcbiAgICAgICAgY291bnROb2RlLkNoaWxkKCdjdXInLCBjYy5MYWJlbCkuc3RyaW5nID0gKHRoaXMuYXJteUxpc3QubGVuZ3RoIC0gMSkgKyAnJ1xuICAgICAgICBjb3VudE5vZGUuQ2hpbGQoJ21heCcsIGNjLkxhYmVsKS5zdHJpbmcgPSAnLycgKyB0aGlzLnBsYXllci5nZXRBcm15TWF4Q291bnQoKVxuICAgICAgICBjb25zdCB1aWQgPSB0aGlzLnNlbGVjdEFybXk/LnVpZCA/PyB0aGlzLnVzZXIuZ2V0VGVtcFByZWZlcmVuY2VNYXAodGhpcy5QS0VZX1NFTEVDVF9BUk1ZKVxuICAgICAgICBsZXQgY3VyQXJteSA9IHVpZCA/IHRoaXMuYXJteUxpc3QuZmluZChtID0+ICEhbT8uYXJteSAmJiBtPy51aWQgPT09IHVpZCkgOiBudWxsLCBpbmRleCA9IC0xXG4gICAgICAgIGNvbnN0IHN2ID0gbm9kZS5DaGlsZCgnYXJteS9saXN0JywgY2MuU2Nyb2xsVmlldylcbiAgICAgICAgc3Yuc3RvcEF1dG9TY3JvbGwoKVxuICAgICAgICAvLyBhcm15cy5wdXNoKG51bGwpXG4gICAgICAgIGNvbnN0IHNob3dEb3QgPSAhdGhpcy5hcm15TGlzdC5zb21lKG0gPT4gISFtKVxuICAgICAgICBzdi5JdGVtcyh0aGlzLmFybXlMaXN0LCAoaXQsIGRhdGEsIGkpID0+IHtcbiAgICAgICAgICAgIGl0LkRhdGEgPSBkYXRhXG4gICAgICAgICAgICBjb25zdCBhcm15ID0gZGF0YT8uYXJteVxuICAgICAgICAgICAgY29uc3Qgcm9vdCA9IGl0LkNoaWxkKCdyb290JyksIGluZm8gPSByb290LkNoaWxkKCdpbmZvJylcbiAgICAgICAgICAgIGluZm8uQ2hpbGQoJ2RvdCcpLmFjdGl2ZSA9IHNob3dEb3RcbiAgICAgICAgICAgIGluZm8uQ2hpbGQoJ2FkZCcpLmFjdGl2ZSA9ICFkYXRhXG4gICAgICAgICAgICBpbmZvLkNoaWxkKCdjb3VudCcpLmFjdGl2ZSA9ICEhZGF0YVxuICAgICAgICAgICAgaW5mby5DaGlsZCgnbmFtZScsIGNjLkxhYmVsKS5zdHJpbmcgPSBkYXRhID8gdXQubmFtZUZvcm1hdG9yKGRhdGEubmFtZSwgNykgOiAnJ1xuICAgICAgICAgICAgY29uc3Qgc3RhdGUgPSBpbmZvLkNoaWxkKCdzdGF0ZScpXG4gICAgICAgICAgICBpZiAoYXJteSkge1xuICAgICAgICAgICAgICAgIGluZm8uQ2hpbGQoJ2NvdW50L3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSBhcm15LnBhd25zLmxlbmd0aCArICcnXG4gICAgICAgICAgICAgICAgY29uc3QgYWRkTGJsID0gaW5mby5DaGlsZCgnY291bnQvYWRkJywgY2MuTGFiZWwpLCBkcGMgPSBhcm15LmRyaWxsUGF3bnMubGVuZ3RoICsgYXJteS5jdXJpbmdQYXducy5sZW5ndGhcbiAgICAgICAgICAgICAgICBpZiAoYWRkTGJsLm5vZGUuYWN0aXZlID0gZHBjID4gMCkge1xuICAgICAgICAgICAgICAgICAgICBhZGRMYmwuc3RyaW5nID0gJysnICsgZHBjXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGlzRnVsbCA9IHN0YXRlLmFjdGl2ZSA9IGFybXkuZ2V0QWN0UGF3bkNvdW50KCkgPj0gQVJNWV9QQVdOX01BWF9DT1VOVFxuICAgICAgICAgICAgICAgIHJvb3QuQ2hpbGQoJ2JnJykuQ29tcG9uZW50KGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKCFpc0Z1bGwpXG4gICAgICAgICAgICAgICAgaW5mby5vcGFjaXR5ID0gaXNGdWxsID8gMTUwIDogMjU1XG4gICAgICAgICAgICAgICAgaWYgKGlzRnVsbCkge1xuICAgICAgICAgICAgICAgICAgICBzdGF0ZS5zZXRMb2NhbGVLZXkoJ3VpLnlldF9mdWxsJylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8g5pi+56S66YCJ5oupXG4gICAgICAgICAgICAgICAgLyogaWYgKCFjdXJBcm15ICYmIGlzRnVsbCkge1xuICAgICAgICAgICAgICAgIH0gZWxzZSAqLyBpZiAoaW5kZXggPT09IC0xICYmICghY3VyQXJteSB8fCBjdXJBcm15LnVpZCA9PT0gYXJteS51aWQpKSB7XG4gICAgICAgICAgICAgICAgICAgIGN1ckFybXkgPSBkYXRhXG4gICAgICAgICAgICAgICAgICAgIGluZGV4ID0gaVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnVzZXIuc2V0VGVtcFByZWZlcmVuY2VEYXRhKHRoaXMuUEtFWV9TRUxFQ1RfQVJNWSwgYXJteS51aWQpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhKSB7XG4gICAgICAgICAgICAgICAgaW5mby5vcGFjaXR5ID0gMTUwXG4gICAgICAgICAgICAgICAgcm9vdC5DaGlsZCgnYmcnKS5Db21wb25lbnQoY2MuTXVsdGlGcmFtZSkuc2V0RnJhbWUoZmFsc2UpXG4gICAgICAgICAgICAgICAgaW5mby5DaGlsZCgnY291bnQvdmFsJywgY2MuTGFiZWwpLnN0cmluZyA9IChkYXRhLnBhd25zPy5sZW5ndGggfHwgMCkgKyAnJ1xuICAgICAgICAgICAgICAgIGluZm8uQ2hpbGQoJ2NvdW50L2FkZCcpLmFjdGl2ZSA9IGZhbHNlXG4gICAgICAgICAgICAgICAgc3RhdGUuYWN0aXZlID0gdHJ1ZVxuICAgICAgICAgICAgICAgIHN0YXRlLnNldExvY2FsZUtleSgndWkuZ29fb3V0JylcbiAgICAgICAgICAgICAgICBpZiAoaW5kZXggPT09IC0xICYmICghY3VyQXJteSB8fCBjdXJBcm15LnVpZCA9PT0gZGF0YS51aWQpKSB7XG4gICAgICAgICAgICAgICAgICAgIGN1ckFybXkgPSBkYXRhXG4gICAgICAgICAgICAgICAgICAgIGluZGV4ID0gaVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnVzZXIuc2V0VGVtcFByZWZlcmVuY2VEYXRhKHRoaXMuUEtFWV9TRUxFQ1RfQVJNWSwgZGF0YS51aWQpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBpbmZvLm9wYWNpdHkgPSAyNTVcbiAgICAgICAgICAgICAgICBzdGF0ZS5hY3RpdmUgPSBmYWxzZVxuICAgICAgICAgICAgICAgIHJvb3QuQ2hpbGQoJ2JnJykuQ29tcG9uZW50KGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKHRydWUpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICAgIC8vIOWwhumAieS4reeahOenu+WKqOWIsOS4remXtFxuICAgICAgICBpZiAoaXNMb2NhdGlvbikge1xuICAgICAgICAgICAgc3YuU2VsZWN0SXRlbVRvQ2VudHJlKGluZGV4KVxuICAgICAgICB9XG4gICAgICAgIC8vIOWIt+aWsOmAieS4rVxuICAgICAgICB0aGlzLnVwZGF0ZUFybXlTZWxlY3QoY3VyQXJteSwgbm9kZSlcbiAgICB9XG5cbiAgICBwcml2YXRlIHVwZGF0ZUFybXlTZWxlY3QoaXRlbTogQXJteUl0ZW0sIG5vZGU/OiBjYy5Ob2RlKSB7XG4gICAgICAgIG5vZGUgPSBub2RlIHx8IHRoaXMucGFnZXNOb2RlXy5DaGlsZCgxKVxuICAgICAgICBjb25zdCB1aWQgPSBpdGVtPy51aWQgfHwgJydcbiAgICAgICAgbm9kZS5DaGlsZCgnYXJteS9saXN0JywgY2MuU2Nyb2xsVmlldykuY29udGVudC5jaGlsZHJlbi5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgY29uc3Qgc2VsZWN0ID0gbS5DaGlsZCgnc2VsZWN0JykuYWN0aXZlID0gbS5EYXRhPy51aWQgPT09IHVpZFxuICAgICAgICAgICAgbS5Db21wb25lbnQoY2MuQnV0dG9uKS5pbnRlcmFjdGFibGUgPSAhc2VsZWN0XG4gICAgICAgIH0pXG4gICAgICAgIG5vZGUuQ2hpbGQoJ2luZm8vYXJteScpLlN3aWgoJ2FybXlfcGF3bnMnKVxuICAgICAgICB0aGlzLnNlbGVjdEFybXkgPSBpdGVtXG4gICAgICAgIGNvbnN0IGFybXkgPSBpdGVtPy5hcm15LCBwYXducyA9IFtdXG4gICAgICAgIGlmIChhcm15KSB7XG4gICAgICAgICAgICBwYXducy5wdXNoQXJyKGFybXkucGF3bnMpXG4gICAgICAgICAgICBwYXducy5wdXNoQXJyKGFybXkuY3VyaW5nUGF3bnMpXG4gICAgICAgICAgICBwYXducy5wdXNoQXJyKGFybXkuZHJpbGxQYXducylcbiAgICAgICAgfSBlbHNlIGlmIChpdGVtPy5wYXducykge1xuICAgICAgICAgICAgcGF3bnMucHVzaEFycihpdGVtLnBhd25zKVxuICAgICAgICB9XG4gICAgICAgIC8vIOWIt+aWsOWjq+WFteWIl+ihqFxuICAgICAgICBub2RlLkNoaWxkKCdpbmZvL2FybXkvYXJteV9wYXducycpLmNoaWxkcmVuLmZvckVhY2goKGl0LCBpKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcGF3bnNbaV0sIGlzSWQgPSB0eXBlb2YgKGRhdGEpID09PSAnbnVtYmVyJywgaXNDdXJpbmcgPSAhIWRhdGEgJiYgISFhcm15Py5jdXJpbmdQYXducy5zb21lKG0gPT4gbS51aWQgPT09IGRhdGEudWlkKVxuICAgICAgICAgICAgaXQuU3dpaCgnbm9uZScsICEhZGF0YSlcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaWNvbiA9IGl0LkNoaWxkKCdpY29uJylcbiAgICAgICAgICAgICAgICBpY29uLm9wYWNpdHkgPSAoaXNJZCB8fCBpc0N1cmluZykgPyAxMjAgOiAyNTVcbiAgICAgICAgICAgICAgICByZXNIZWxwZXIubG9hZFBhd25IZWFkTWluaUljb24oaXNJZCA/IGRhdGEgOiAoZGF0YS5wb3J0cmF5YWw/LmlkIHx8IGRhdGEuaWQpLCBpY29uLCB0aGlzLmtleSwgZmFsc2UpXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2x2JywgY2MuTGFiZWwpLnN0cmluZyA9IGlzSWQgfHwgZGF0YS5sdiA8PSAxID8gJycgOiBkYXRhLmx2ICsgJydcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgLy8g5Yi35paw5oyJ6ZKuXG4gICAgICAgIGNvbnN0IGNvbmQgPSBub2RlLkNoaWxkKCdpbmZvL2NvbmQnKSwgYnV0dG9uID0gY29uZC5DaGlsZCgnbmVlZC9idXR0b25zL2RyaWxsX2JlJylcbiAgICAgICAgaWYgKGFybXkpIHtcbiAgICAgICAgICAgIGJ1dHRvbi5EYXRhID0gYXJteS5nZXRBY3RQYXduQ291bnQoKSA+PSBBUk1ZX1BBV05fTUFYX0NPVU5UID8gMSA6IDBcbiAgICAgICAgfSBlbHNlIGlmIChpdGVtPy5wYXducykge1xuICAgICAgICAgICAgYnV0dG9uLkRhdGEgPSAyIC8v5LiN5Zyo5Li75Z+O5YaFXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBidXR0b24uRGF0YSA9IDAgLy/msqHmnInpgInmi6nlhpvpmJ9cbiAgICAgICAgfVxuICAgICAgICBidXR0b24ub3BhY2l0eSA9ICghdGhpcy5zZWxlY3RQYXduU2xvdCB8fCAhaXRlbSB8fCBidXR0b24uRGF0YSkgPyAxMjAgOiAyNTVcbiAgICB9XG5cbiAgICAvLyDliLfmlrDlo6vlhbXliJfooahcbiAgICBwcml2YXRlIHVwZGF0ZVBhd25MaXN0KGlzTG9jYXRpb246IGJvb2xlYW4sIG5vZGU/OiBjYy5Ob2RlKSB7XG4gICAgICAgIG5vZGUgPSBub2RlIHx8IHRoaXMucGFnZXNOb2RlXy5DaGlsZCgxKVxuICAgICAgICBjb25zdCByb290ID0gbm9kZS5DaGlsZCgnaW5mbycpXG4gICAgICAgIGxldCBzZWxlY3RQYXduU2xvdDogUGF3blNsb3RPYmogPSB0aGlzLnNlbGVjdFBhd25TbG90XG4gICAgICAgIGxldCBzZWxlY3RVaWQgPSB0aGlzLnNlbGVjdFBhd25TbG90Py51aWRcbiAgICAgICAgY29uc3QgaGFzRXF1aXAgPSB0aGlzLnBsYXllci5nZXRFcXVpcHMoKS5sZW5ndGggPiAwLCBidWlsZEx2ID0gdGhpcy5kYXRhLmx2XG4gICAgICAgIGNvbnN0IHNsb3RzID0gdGhpcy5wbGF5ZXIuZ2V0Q2FuUmVjcnVpdFBhd25zKHRoaXMuZGF0YS5pZClcbiAgICAgICAgY29uc3Qgc3YgPSByb290LkNoaWxkKCdwYXduL2xpc3QnLCBjYy5TY3JvbGxWaWV3KSwgcGF3bkNvdW50ID0gc2xvdHMuZmlsdGVyKG0gPT4gISFtLnBhd24pLmxlbmd0aFxuICAgICAgICBzdi5zdG9wQXV0b1Njcm9sbCgpXG4gICAgICAgIHN2Lkl0ZW1zKHNsb3RzLCAoaXQsIHNsb3QsIGkpID0+IHtcbiAgICAgICAgICAgIGl0LkRhdGEgPSBzbG90XG4gICAgICAgICAgICBjb25zdCBsdiA9IHNsb3QubHYsIHBhd246IFBhd25PYmogPSBzbG90LnBhd24sIGlzSGVyb0hhbGxVbmxvY2sgPSBsdiA+IDEwMDBcbiAgICAgICAgICAgIGlmIChzbG90LmlkIDwgMCAmJiBpc0hlcm9IYWxsVW5sb2NrKSB7IC8vIOiLsembhOino+mUgemHjeWkjeWPquS/neeVmeS4gOS4qlxuICAgICAgICAgICAgICAgIGl0LmFjdGl2ZSA9IGZhbHNlXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ID0gc2xvdC5pc1lldFN0dWR5KCksIGlzVW5sb2NrID0gYnVpbGRMdiA+PSBsdiwgcmVzdHVkeUhlcm8gPSBzbG90LmdldFJlc3R1ZHlIZXJvKClcbiAgICAgICAgICAgICAgICBjb25zdCBpY29uID0gaXQuQ2hpbGQoJ2ljb24nKVxuICAgICAgICAgICAgICAgIGlmIChpY29uLmFjdGl2ZSA9IGlzU2VsZWN0IHx8ICEhcmVzdHVkeUhlcm8pIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRQYXduSGVhZEljb24oc2xvdC52aWV3SWQsIGljb24sIHRoaXMua2V5KVxuICAgICAgICAgICAgICAgICAgICBpY29uLm9wYWNpdHkgPSByZXN0dWR5SGVybyA/IDEwMCA6IDI1NVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBzaG93QWRkID0gaXNVbmxvY2sgJiYgIWljb24uYWN0aXZlICYmICFpc0hlcm9IYWxsVW5sb2NrXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2FkZCcpLmFjdGl2ZSA9IGl0LkNoaWxkKCduZXcnKS5hY3RpdmUgPSBzaG93QWRkXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2xvY2snKS5hY3RpdmUgPSAhaXNIZXJvSGFsbFVubG9jayAmJiAhaXNVbmxvY2sgJiYgIWlzU2VsZWN0XG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ25vbmUnKS5hY3RpdmUgPSBpc0hlcm9IYWxsVW5sb2NrICYmICFpc1NlbGVjdCAmJiAhcmVzdHVkeUhlcm9cbiAgICAgICAgICAgICAgICAvLyDph43mlrDnoJTnqbbnuqLngrkgfHwg5rKh5pyJ6YWN572u6KOF5aSH55qE57qi54K5XG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2RvdCcpLmFjdGl2ZSA9ICEhcmVzdHVkeUhlcm8gfHwgKGlzU2VsZWN0ICYmICFwYXduPy5lcXVpcD8uaWQgJiYgcGF3bj8udHlwZSA8IFBhd25UeXBlLk1BQ0hJTkUgJiYgaGFzRXF1aXApXG4gICAgICAgICAgICAgICAgLy8g55S75YOP6Kej6ZSB55qE5Y2V54us6aKc6Imy5bqVXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2JnJykuQ29tcG9uZW50KGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKGlzSGVyb0hhbGxVbmxvY2spXG4gICAgICAgICAgICAgICAgLy8g5om+5Yiw6YCJ5oupXG4gICAgICAgICAgICAgICAgLy8gaWYgKCFzZWxlY3RVaWQgJiYgKHBhd25Db3VudCA9PT0gMCB8fCBzaG93QWRkKSkge1xuICAgICAgICAgICAgICAgIC8vIH0gZWxzZSBpZiAoIXNlbGVjdFBhd25TbG90ICYmICghc2VsZWN0VWlkIHx8IHNsb3QudWlkID09PSBzZWxlY3RVaWQpKSB7XG4gICAgICAgICAgICAgICAgLy8gICAgIHNlbGVjdFBhd25TbG90ID0gc2xvdFxuICAgICAgICAgICAgICAgIC8vIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgdGhpcy51cGRhdGVQYXduU2VsZWN0KHNlbGVjdFBhd25TbG90LCBpc0xvY2F0aW9uLCBub2RlKVxuICAgIH1cblxuICAgIHByaXZhdGUgdXBkYXRlUGF3blNlbGVjdChzbG90OiBQYXduU2xvdE9iaiwgaXNMb2NhdGlvbjogYm9vbGVhbiwgbm9kZT86IGNjLk5vZGUpIHtcbiAgICAgICAgbm9kZSA9IG5vZGUgfHwgdGhpcy5wYWdlc05vZGVfLkNoaWxkKDEpXG4gICAgICAgIHRoaXMuc2VsZWN0UGF3blNsb3QgPSBzbG90XG4gICAgICAgIGxldCB1aWQgPSBzbG90Py51aWQgfHwgJycsIHNlbGVjdEluZGV4ID0gLTFcbiAgICAgICAgY29uc3Qgcm9vdCA9IG5vZGUuQ2hpbGQoJ2luZm8nKSwgc3YgPSByb290LkNoaWxkKCdwYXduL2xpc3QnLCBjYy5TY3JvbGxWaWV3KVxuICAgICAgICBzdi5jb250ZW50LmNoaWxkcmVuLmZvckVhY2goKG0sIGkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHNlbGVjdCA9IG0uQ2hpbGQoJ2JnL3NlbGVjdCcpLmFjdGl2ZSA9IG0uQ2hpbGQoJ3NlbGVjdCcpLmFjdGl2ZSA9IG0uRGF0YT8udWlkID09PSB1aWRcbiAgICAgICAgICAgIG0uQ29tcG9uZW50KGNjLkJ1dHRvbikuaW50ZXJhY3RhYmxlID0gIXNlbGVjdCB8fCAhIW0uRGF0YT8ucGF3blxuICAgICAgICAgICAgaWYgKHNlbGVjdCkge1xuICAgICAgICAgICAgICAgIHNlbGVjdEluZGV4ID0gaVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgICBpZiAoaXNMb2NhdGlvbikge1xuICAgICAgICAgICAgc3YuU2VsZWN0SXRlbVRvQ2VudHJlKHNlbGVjdEluZGV4KVxuICAgICAgICB9XG4gICAgICAgIC8vIOaYvuekuuWjq+WFtei0ueeUqFxuICAgICAgICB0aGlzLnVwZGF0ZVJlY3J1aXRDb3N0KG5vZGUpXG4gICAgfVxuXG4gICAgLy8g5Yi35paw5oub5Yuf6LS555SoXG4gICAgcHJpdmF0ZSB1cGRhdGVSZWNydWl0Q29zdChub2RlPzogY2MuTm9kZSkge1xuICAgICAgICBub2RlID0gbm9kZSB8fCB0aGlzLnBhZ2VzTm9kZV8uQ2hpbGQoMSlcbiAgICAgICAgY29uc3Qgcm9vdCA9IG5vZGUuQ2hpbGQoJ2luZm8nKSwgY29uZCA9IHJvb3QuQ2hpbGQoJ2NvbmQnKVxuICAgICAgICBjb25zdCBzbG90ID0gdGhpcy5zZWxlY3RQYXduU2xvdFxuICAgICAgICBpZiAoIXNsb3QpIHsgLy8g5oub5Yuf5oyJ6ZKu5LiA55u05pi+56S677yM6YCa6L+H54K55Ye75oyJ6ZKu5YGa5Ye655u45bqU55qE5o+Q56S6XG4gICAgICAgICAgICBjb25zdCBpdCA9IGNvbmQuU3dpaCgnbmVlZCcpWzBdXG4gICAgICAgICAgICBjb25zdCBjb3N0ID0gW0NUeXBlLkNFUkVBTCwgQ1R5cGUuQ0VSRUFMX0NdXG4gICAgICAgICAgICBpdC5DaGlsZCgndGltZS91cCcpLmFjdGl2ZSA9IGZhbHNlXG4gICAgICAgICAgICBpdC5DaGlsZCgndGltZS92YWwnLCBjYy5MYWJlbCkvKiAuQ29sb3IoJyM3NTY5NjMnKSAqLy5zdHJpbmcgPSAnLSdcbiAgICAgICAgICAgIGl0LkNoaWxkKCdjb3N0JykuSXRlbXMoY29zdCB8fCBbXSwgKGl0LCBjb3N0KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGl0ICYmIGNvc3QpIHtcbiAgICAgICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2ljb24nLCBjYy5TcHJpdGUpLnNwcml0ZUZyYW1lID0gcmVzSGVscGVyLmdldFJlc0ljb24oY29zdClcbiAgICAgICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ3ZhbCcsIGNjLkxhYmVsKS8qIC5Db2xvcignIzc1Njk2MycpICovLnN0cmluZyA9ICctJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBpdC5DaGlsZCgnYnV0dG9ucy9kcmlsbF9iZS92YWwnKS5zZXRMb2NhbGVLZXkoJ3VpLmJ1dHRvbl9kcmlsbCcpXG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpc1lldFN0dWR5ID0gc2xvdC5pc1lldFN0dWR5KClcbiAgICAgICAgY29uc3QgaXQgPSBjb25kLlN3aWgoaXNZZXRTdHVkeSA/ICduZWVkJyA6ICdub25lJylbMF1cbiAgICAgICAgaWYgKGlzWWV0U3R1ZHkpIHsgLy/lt7Lnu4/pgInmi6lcbiAgICAgICAgICAgIC8vIOajgOa1i+aYr+WQpuacieiuree7g+Wjq+WFtei0ueeUqOWinuWKoFxuICAgICAgICAgICAgY29uc3QgY29zdCA9IGdhbWVIcHIud29ybGQuZ2V0U2Vhc29uKCkuY2hhbmdlQmFzZVJlc0Nvc3QoQ0VmZmVjdC5SRUNSVUlUX0NPU1QsIHNsb3QuZHJpbGxDb3N0KVxuICAgICAgICAgICAgLy8g5YeP5bCR5pe26Ze0XG4gICAgICAgICAgICBsZXQgdGltZSA9IHNsb3QuZHJpbGxUaW1lXG4gICAgICAgICAgICBsZXQgY2QgPSB0aGlzLmdldERyaWxsVGltZUNEKClcbiAgICAgICAgICAgIGNvbnN0IHVwQ291bnQgPSB0aGlzLnBsYXllci5nZXRVcFJlY3J1aXRQYXduQ291bnQoKSAvL+WKoOmAnyDkuI3lhY3otLlcbiAgICAgICAgICAgIGNvbnN0IHBvbGljeUZyZWVDb3VudCA9IHRoaXMucGxheWVyLmdldEZyZWVSZWNydWl0UGF3blN1cnBsdXNDb3VudCgpIC8v5YWN6LS5XG4gICAgICAgICAgICBpZiAoZ2FtZUhwci5pc05vdmljZU1vZGUpIHtcbiAgICAgICAgICAgICAgICBjZCA9IGdhbWVIcHIubm92aWNlU2VydmVyLmdldERyaWxsU3BlZWRUaW1lKHRpbWUpLmNkXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpdC5DaGlsZCgndGltZS91cCcpLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgICAgIHZpZXdIZWxwZXIudXBkYXRlRnJlZUNvc3RWaWV3KGl0LCBjb3N0LCB0aW1lLCBjZCwgZmFsc2UsIHBvbGljeUZyZWVDb3VudCwgdXBDb3VudClcbiAgICAgICAgICAgIGNvbnN0IGJ1dHRvbiA9IGl0LkNoaWxkKCdidXR0b25zL2RyaWxsX2JlJylcbiAgICAgICAgICAgIGJ1dHRvbi5vcGFjaXR5ID0gMjU1XG4gICAgICAgICAgICBidXR0b24uQ2hpbGQoJ3ZhbCcpLnNldExvY2FsZUtleShwb2xpY3lGcmVlQ291bnQgPiAwID8gJ3VpLmJ1dHRvbl9mcmVlX2RyaWxsJyA6ICd1aS5idXR0b25fZHJpbGwnKVxuICAgICAgICB9IGVsc2UgaWYgKHNsb3QubHYgPiAxMDAwKSB7IC8vIOiLsembhOauv+ebuOWFs+aPkOekulxuICAgICAgICAgICAgY29uc3QgcmVzdHVkeUhlcm8gPSBzbG90LmdldFJlc3R1ZHlIZXJvKClcbiAgICAgICAgICAgIGlmIChyZXN0dWR5SGVybykgeyAvLyDph43lpI3op6PplIHmj5DnpLpcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgnYnV0dG9ucycpLlN3aWgoJ3Jlc3R1ZHknKVswXS5DaGlsZCgnZGVzYycpLnNldExvY2FsZUtleSgndWkucmVzdHVkeV9kZXNjJywgcmVzdHVkeUhlcm8uYXZhdGFyUGF3bk5hbWUpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IG4gPSBpdC5DaGlsZCgnYnV0dG9ucycpLlN3aWgoJ2NvbmQnKVswXSwgbmVlZEx2ID0gTWF0aC5hYnMoc2xvdC5sdiAlIDEwMCksIGhlcm9IYWxsTHYgPSB0aGlzLnBsYXllci5nZXRCdWlsZEx2KEJVSUxEX05JRC5IRVJPX0hBTEwpXG4gICAgICAgICAgICAgICAgbi5DaGlsZCgnZGVzYycpLnNldExvY2FsZUtleSgndWkudW5sb2NrX2hlcm9fcGF3bl9kZXNjJywgaGVyb0hhbGxMdiA+PSBuZWVkTHYgPyBhc3NldHNNZ3IubGFuZygndWkuc2hvcnRfbHYnLCBuZWVkTHYpIDogYDxjb2xvcj0jQzM0QjNGPiR7YXNzZXRzTWdyLmxhbmcoJ3VpLnNob3J0X2x2JywgbmVlZEx2KX08L2M+YClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmRhdGEubHYgPCBzbG90Lmx2KSB7IC8vIOetiee6p+S4jei2s+aPkOekulxuICAgICAgICAgICAgaXQuQ2hpbGQoJ2J1dHRvbnMnKS5Td2loKCdsb2NrJylbMF0uQ2hpbGQoJ3ZhbCcpLnNldExvY2FsZUtleSgndWkubHZfdW5sb2NrX25ldycsIFthc3NldHNNZ3IubGFuZygndWkuc2hvcnRfbHYnLCBzbG90Lmx2KSwgJ3VpLmNlcmlfdHlwZV9uYW1lXzInXSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOiOt+WPluiuree7g+aXtumXtFxuICAgIHByaXZhdGUgZ2V0RHJpbGxUaW1lQ0QoKSB7XG4gICAgICAgIGNvbnN0IGNkID0gZ2FtZUhwci5nZXRQbGF5ZXJQb2xpY3lFZmZlY3QoQ0VmZmVjdC5YTF9DRCkgKyB0aGlzLmRhdGEuZ2V0RWZmZWN0VmFsdWUoQ0VmZmVjdC5YTF9DRClcbiAgICAgICAgcmV0dXJuIGNkICogMC4wMVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOiuree7g+WIl+ihqFxuICAgIHByaXZhdGUgdXBkYXRlRHJpbGxRdWV1ZShub2RlPzogY2MuTm9kZSkge1xuICAgICAgICBub2RlID0gbm9kZSB8fCB0aGlzLnBhZ2VzTm9kZV8uQ2hpbGQoMSlcbiAgICAgICAgY29uc3QgbGlzdCA9IHRoaXMucGxheWVyLmdldFBhd25EcmlsbFF1ZXVlcyh0aGlzLmRhdGEudWlkKVxuICAgICAgICBsaXN0LnNvcnQoKGEsIGIpID0+IGIuc3VycGx1c1RpbWUgLSBhLnN1cnBsdXNUaW1lKVxuICAgICAgICBjb25zdCBwYXduQ29uZiA9IHRoaXMucGxheWVyLmdldENvbmZpZ1Bhd25NYXAoKVxuICAgICAgICBsZXQgdGltZSA9IDBcbiAgICAgICAgLy8g5piv5ZCm5pyJ5pS/562W55qE5Yqg5oiQXG4gICAgICAgIGNvbnN0IHF1ZXVlQ291bnQgPSA2ICsgZ2FtZUhwci5nZXRQbGF5ZXJQb2xpY3lFZmZlY3QoQ0VmZmVjdC5EUklMTF9RVUVVRSlcbiAgICAgICAgbm9kZS5DaGlsZCgnZHJpbGwvdGl0bGUvYmcvbGltaXQnLCBjYy5MYWJlbCkuc3RyaW5nID0gJygnICsgbGlzdC5sZW5ndGggKyAnLycgKyBxdWV1ZUNvdW50ICsgJyknXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcXVldWVDb3VudDsgaSsrKSB7XG4gICAgICAgICAgICBsZXQgaXQgPSBudWxsLCBkYXRhID0gbGlzdFtpXVxuICAgICAgICAgICAgaWYgKGkgPT09IDApIHtcbiAgICAgICAgICAgICAgICBpdCA9IG5vZGUuQ2hpbGQoJ2RyaWxsL2NvbnRlbnQvJyArIGkpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNoaWxkcmVuQ291bnQgPSB0aGlzLnF1ZXVlU3ZfLmNvbnRlbnQuY2hpbGRyZW5Db3VudFxuICAgICAgICAgICAgICAgIGlmIChjaGlsZHJlbkNvdW50IDw9IDEgfHwgY2hpbGRyZW5Db3VudCA8IHF1ZXVlQ291bnQgLSAxKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMucXVldWVTdl8uSXRlbXMocXVldWVDb3VudCAtIDEsICgpID0+IHsgfSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaXQgPSB0aGlzLnF1ZXVlU3ZfLmNvbnRlbnQuY2hpbGRyZW5baSAtIDFdXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpdC5EYXRhID0gZGF0YVxuICAgICAgICAgICAgY29uc3Qgc2tpbklkID0gZGF0YSA/IChwYXduQ29uZltkYXRhLmlkXT8uc2tpbklkIHx8IGRhdGEuaWQpIDogMFxuICAgICAgICAgICAgY29uc3QgaGFzID0gaXQuQ2hpbGQoJ2ljb24nKS5hY3RpdmUgPSBpdC5DaGlsZCgnZHJpbGxfcGF3bl9iZScpLmFjdGl2ZSA9ICEhZGF0YVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ2NhbmNlbF9kcmlsbF9iZScpPy5zZXRBY3RpdmUoaGFzKVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ2ljb24vcHJvZ3Jlc3MnKT8uc2V0QWN0aXZlKGhhcylcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRQYXduSGVhZEljb24oc2tpbklkLCBpdC5DaGlsZCgnaWNvbicpLCB0aGlzLmtleSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgdGltZSArPSBkYXRhPy5uZWVkVGltZSB8fCAwXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBwcm9ncmVzcyA9IGl0LkNoaWxkKCdpY29uL3Byb2dyZXNzJywgY2MuU3ByaXRlKVxuICAgICAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkUGF3bkhlYWRJY29uKHNraW5JZCwgcHJvZ3Jlc3MsIHRoaXMua2V5KVxuICAgICAgICAgICAgICAgIGNvbnN0IHN0aW1lID0gZGF0YS5nZXRTdXJwbHVzVGltZSgpXG4gICAgICAgICAgICAgICAgdGltZSArPSBzdGltZVxuICAgICAgICAgICAgICAgIHRoaXMuZHJpbGxQcm9ncmVzc1R3ZWVuW2ldPy5zdG9wKClcbiAgICAgICAgICAgICAgICB0aGlzLmRyaWxsUHJvZ3Jlc3NUd2VlbltpXSA9IG51bGxcbiAgICAgICAgICAgICAgICBwcm9ncmVzcy5maWxsUmFuZ2UgPSBzdGltZSAvIGRhdGEubmVlZFRpbWVcbiAgICAgICAgICAgICAgICBjb25zdCBzdCA9IHN0aW1lICogMC4wMDFcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgndGltZScsIGNjLkxhYmVsVGltZXIpLnJ1bihzdClcbiAgICAgICAgICAgICAgICB0aGlzLmRyaWxsUHJvZ3Jlc3NUd2VlbltpXSA9IGNjLnR3ZWVuKHByb2dyZXNzKS50byhzdCwgeyBmaWxsUmFuZ2U6IDAgfSkuc3RhcnQoKVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgndGltZScsIGNjLkxhYmVsVGltZXIpLnN0cmluZyA9ICcnXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbm9kZS5DaGlsZCgnZHJpbGwvZGVzYycpLmFjdGl2ZSA9IHRpbWUgPiAwXG4gICAgICAgIGlmICh0aW1lID4gMCkge1xuICAgICAgICAgICAgbm9kZS5DaGlsZCgnZHJpbGwvZGVzYy90aXRsZScpLnNldExvY2FsZUtleSgndWkuZHJpbGxfYWxsX2Rlc2MnLCB0aGlzLmRhdGEuaWQgPT09IDIwMTAgPyAndWkuYnV0dG9uX3Byb2R1Y2UnIDogJ3VpLmJ1dHRvbl9kcmlsbCcpXG4gICAgICAgICAgICBub2RlLkNoaWxkKCdkcmlsbC9kZXNjL3RpbWUvdmFsJywgY2MuTGFiZWxUaW1lcikucnVuKHRpbWUgKiAwLjAwMSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgc2hvd0NyZWF0ZUFybXlVSSgpIHtcbiAgICAgICAgaWYgKGdhbWVIcHIucGxheWVyLmlzQXJteUNvdW50RnVsbCgpKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZWNvZGUuUExBWUVSX0ZVTExfQVJNWSlcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnNob3dDcmVhdGVBcm15RmluZ2VyVGlwKGZhbHNlKVxuICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93UG5sKCdjb21tb24vQ3JlYXRlQXJteScsIChuYW1lOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRlbXBDcmVhdGVBcm15ID0gbmV3IEFybXlPYmooKS5pbml0KHRoaXMuZGF0YS5hSW5kZXgsIGdhbWVIcHIuZ2V0VWlkKCksIG5hbWUpXG4gICAgICAgICAgICAgICAgdGhpcy50ZW1wQXJteVNvcnRXZWlnaHRNYXAgPSB7fVxuICAgICAgICAgICAgICAgIGlmICghdGhpcy5zZWxlY3RBcm15KSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0QXJteSA9IHt9IGFzIGFueVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdEFybXkudWlkID0gdGhpcy50ZW1wQ3JlYXRlQXJteS51aWRcbiAgICAgICAgICAgICAgICBjb25zdCBub2RlID0gdGhpcy5wYWdlc05vZGVfLkNoaWxkKDEpXG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVBcm15TGlzdCh0cnVlLCBub2RlKVxuICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlUGF3bkxpc3QoZmFsc2UsIG5vZGUpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g5pi+56S65Yib5bu65Yab6Zif5o+Q56S65omL5oyHXG4gICAgcHJpdmF0ZSBzaG93Q3JlYXRlQXJteUZpbmdlclRpcCh2YWw6IGJvb2xlYW4pIHtcbiAgICAgICAgcmV0dXJuLy/kuI3mmL7npLrmiYvmjIdcbiAgICAgICAgY29uc3Qgbm9kZSA9IHRoaXMucGFnZXNOb2RlXy5DaGlsZCgxKVxuICAgICAgICBjb25zdCBzdiA9IG5vZGUuQ2hpbGQoJ2FybXkvbGlzdCcsIGNjLlNjcm9sbFZpZXcpLCBmaW5nZXIgPSBzdi5DaGlsZCgnZmluZ2VyJylcbiAgICAgICAgaWYgKGZpbmdlci5hY3RpdmUgPSB2YWwpIHtcbiAgICAgICAgICAgIGNvbnN0IGNvdW50ID0gc3YuY29udGVudC5jaGlsZHJlbkNvdW50XG4gICAgICAgICAgICBzdi5zdG9wQXV0b1Njcm9sbCgpXG4gICAgICAgICAgICBpZiAoY291bnQgPj0gNCkge1xuICAgICAgICAgICAgICAgIHN2LnNjcm9sbFRvTGVmdCgpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBpdCA9IHN2LmNvbnRlbnQuY2hpbGRyZW5bMF1cbiAgICAgICAgICAgIGNvbnN0IHBvcyA9IHV0LmNvbnZlcnRUb05vZGVBUihpdCwgc3Yubm9kZSlcbiAgICAgICAgICAgIGZpbmdlci5zZXRQb3NpdGlvbihwb3MueCwgcG9zLnkgLSAxMilcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWPlua2iOaLm+WLn1xuICAgIHByaXZhdGUgY2FuY2VsRHJpbGwoZGF0YTogUGF3bk9iaiwgaW5mbzogSVBhd25EcmlsbEluZm8pIHtcbiAgICAgICAgaWYgKCFkYXRhKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpbmRleCA9IGluZm8uaW5kZXhcbiAgICAgICAgY29uc3QgdWlkID0gaW5mby51aWRcbiAgICAgICAgY29uc3QganNvbiA9IGluZm8uanNvblxuICAgICAgICBjb25zdCBpc01hY2hpbmUgPSBkYXRhLmlzTWFjaGluZSgpXG4gICAgICAgIG5ldEhlbHBlci5yZXFDYW5jZWxEcmlsbFBhd24oeyBpbmRleCwgYnVpbGRVaWQ6IGluZm8uYnVpZCwgdWlkIH0pLnRoZW4ocmVzID0+IHtcbiAgICAgICAgICAgIGlmIChyZXMuZXJyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KHJlcy5lcnIpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXMuZGF0YVxuICAgICAgICAgICAgICAgIGdhbWVIcHIucGxheWVyLnVwZGF0ZVJld2FyZEl0ZW1zQnlGbGFncyhkYXRhLm91dHB1dClcbiAgICAgICAgICAgICAgICBnYW1lSHByLmFyZWFDZW50ZXIuZ2V0QXJlYShpbmRleCk/LnVwZGF0ZUFybXlEcmlsbFBhd25zKGRhdGEuYXJteSlcbiAgICAgICAgICAgICAgICBnYW1lSHByLnBsYXllci51cGRhdGVQYXduRHJpbGxRdWV1ZShkYXRhLnF1ZXVlcylcbiAgICAgICAgICAgICAgICBnYW1lSHByLmRlbE1lc3NhZ2VCeVRhZyh1aWQpXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEubmVlZENvc3Q/Lmxlbmd0aCkge1xuICAgICAgICAgICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2NvbW1vbi9DYW5jZWxEcmlsbCcsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IGlzTWFjaGluZSA/ICd1aS5jYW5jZWxfc2NfdGlwJyA6ICd1aS5jYW5jZWxfZHJpbGxfdGlwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBqc29uLmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29zdDogZGF0YS5uZWVkQ29zdCxcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxufVxuIl19