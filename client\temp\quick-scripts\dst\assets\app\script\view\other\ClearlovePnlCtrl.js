
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/other/ClearlovePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '39f96tDXpNI3ZFzlvmwKWiR', 'ClearlovePnlCtrl');
// app/script/view/other/ClearlovePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var ClearlovePnlCtrl = /** @class */ (function (_super) {
    __extends(ClearlovePnlCtrl, _super);
    function ClearlovePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        _this.inputEb_ = null; // path://root/input/input_eb
        _this.commonSv_ = null; // path://root/common_sv
        //@end
        _this.list = [];
        _this.lastLine = null;
        return _this;
    }
    ClearlovePnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[mc.Event.MVC_LOGGER_PRINT] = this.onLoggerPrint, _a)
        ];
    };
    ClearlovePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isClean: false, isAct: false, isMask: false });
                this.listSv_.Items([]);
                this.commonSv_.Items([
                    'cc.assetManager.assets.count',
                    'mc.__locks',
                ], function (it, text) {
                    it.Data = text;
                    it.Child('content', cc.Label).string = text;
                });
                this.commonSv_.setActive(false);
                return [2 /*return*/];
            });
        });
    };
    ClearlovePnlCtrl.prototype.onEnter = function () {
        // let len = this.list.length - 1
        // this.listSv_.Items(this.list, (it, data, i) => {
        //     const label = it.Child('content', cc.Label)
        //     label.string = data.text
        //     label._forceUpdateRenderData()
        //     it.height = label.node.height + 36
        //     if (i === len) {
        //         this.lastLine = it.Child('line')
        //         this.lastLine.active = false
        //     } else {
        //         it.Child('line').active = true
        //     }
        // })
        // this.scrollToBottom()
    };
    ClearlovePnlCtrl.prototype.onRemove = function () {
    };
    ClearlovePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/input/ok_be
    ClearlovePnlCtrl.prototype.onClickOk = function (event, data) {
        var text = this.inputEb_.string.trim();
        if (!text) {
            return;
        }
        this.addContent(1, text);
        this.execCmd(text);
        this.inputEb_.string = '';
    };
    // path://root/input/common_be
    ClearlovePnlCtrl.prototype.onClickCommon = function (event, data) {
        this.commonSv_.setActive(!this.commonSv_.getActive());
    };
    // path://root/list_sv/view/content/item_be
    ClearlovePnlCtrl.prototype.onClickItem = function (event, _) {
        var data = event.target.Data;
        if (data.type === 1) {
            this.inputEb_.string = data.text;
        }
    };
    // path://root/common_sv/view/content/com_cmd_be
    ClearlovePnlCtrl.prototype.onClickComCmd = function (event, _) {
        var data = event.target.Data;
        this.inputEb_.string = data;
        this.commonSv_.setActive(false);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ClearlovePnlCtrl.prototype.onLoggerPrint = function (type, text) {
        if (type === 'print') {
            this.addContent(0, text);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ClearlovePnlCtrl.prototype.addContent = function (type, text) {
        // if (this.lastLine) {
        //     this.lastLine.active = true
        // }
        this.listSv_.AddItem(function (it) {
            it.Data = { type: type, text: text };
            it.Component(cc.Button).interactable = type === 1;
            if (type === 0) {
                it.Child('type', cc.Label).string = '';
            }
            else {
                it.Child('type', cc.Label).string = type === 1 ? '>' : '<';
            }
            var label = it.Child('content', cc.Label);
            label.string = text;
            label._forceUpdateRenderData();
            it.height = label.node.height + 20;
            // this.lastLine = it.Child('line')
            // this.lastLine.active = false
        });
        this.scrollToBottom();
    };
    ClearlovePnlCtrl.prototype.scrollToBottom = function () {
        var _this = this;
        ut.wait(0.1, this).then(function () {
            if (_this.listSv_.content.height > _this.listSv_.node.height) {
                _this.listSv_.scrollToBottom();
            }
        });
    };
    // 执行命令
    ClearlovePnlCtrl.prototype.execCmd = function (text) {
        var _this = this;
        var arr = text.split('.');
        var key = arr.shift(), data = window[key], msg = key;
        while (arr.length > 0) {
            key = arr.shift();
            msg += '.' + key;
            if (key.includes('=')) {
                var arr_1 = key.split('=').map(function (m) { return m.trim(); });
                if (arr_1.length === 2) {
                    data[arr_1[0]] = this.parseValue(arr_1[1]);
                }
                else {
                    this.addContent(2, msg + ' = is error');
                }
                return;
            }
            else if (key[key.length - 1] === ')') {
                var index = key.indexOf('(');
                var arr_2 = key.substring(index + 1, key.length - 1).split(',').map(function (m) { return _this.parseValue(m); });
                data = data[key.substring(0, index)].apply(data, arr_2);
                if (!data) {
                    continue;
                }
            }
            else {
                data = data[key];
            }
            if (data === undefined) {
                return this.addContent(2, msg + ' is undefined');
            }
            else if (data === null) {
                return this.addContent(2, msg + ' is null');
            }
        }
        this.addContent(2, this.formatter(data));
    };
    ClearlovePnlCtrl.prototype.formatter = function (val) {
        var _this = this;
        if (val === null) {
            return 'null';
        }
        else if (val === undefined) {
            return 'undefined';
        }
        else if (Array.isArray(val)) {
            return '[' + val.join2(function (m) { return _this.formatter(m); }, ',') + ']';
        }
        else if (typeof (val) === 'object') {
            return this.warpObject(val);
        }
        else {
            return String(val);
        }
    };
    ClearlovePnlCtrl.prototype.warpObject = function (obj) {
        try {
            if (obj.constructor || obj.prototype) {
                return 'object { ' + cc.js.getClassName(obj) + ' }';
            }
            else if (obj.__classname__) {
                return obj.__classname__ + " { name: " + obj.name + " }";
            }
            return JSON.stringify(obj);
        }
        catch (error) {
            return '无法解析';
        }
    };
    ClearlovePnlCtrl.prototype.parseValue = function (str) {
        str = str.trim();
        str = str.replace(/"/g, '');
        str = str.replace(/\'/g, '');
        if (str === 'true') {
            return true;
        }
        else if (str === 'false') {
            return false;
        }
        var num = Number(str);
        return isNaN(num) ? str : num;
    };
    ClearlovePnlCtrl = __decorate([
        ccclass
    ], ClearlovePnlCtrl);
    return ClearlovePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ClearlovePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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