
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/AStar.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ad50fUGiNBKJYDWg4R7NedD', 'AStar');
// app/script/model/snailisle/AStar.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var AStarConfig_1 = require("../../common/astar/AStarConfig");
// 一个节点
var Node = /** @class */ (function () {
    function Node() {
        this.uid = '0_0';
        this.point = cc.v2(0, 0);
        this.parent = null;
        this.F = 0;
        this.G = 0;
        this.H = 0;
        this.ignoreCheck = false; //是否忽略检测
        this.isMovePoint = true; //是否算移动点
    }
    Object.defineProperty(Node.prototype, "x", {
        get: function () { return this.point.x; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Node.prototype, "y", {
        get: function () { return this.point.y; },
        enumerable: false,
        configurable: true
    });
    Node.prototype.init = function (x, y) {
        this.uid = x + '_' + y;
        this.point.set2(x, y);
        this.parent = null;
        this.F = 0;
        this.G = 0;
        this.H = 0;
        this.ignoreCheck = false;
        this.isMovePoint = true;
        return this;
    };
    Node.prototype.has = function (x, y) {
        return this.point.x === x && this.point.y === y;
    };
    Node.prototype.updateParent = function (node, tag) {
        this.parent = node;
        this.G = node.G + tag;
        this.F = this.H + this.G;
    };
    return Node;
}());
var AstarNodePool = /** @class */ (function () {
    function AstarNodePool() {
        this.nodePool = []; // 节点对象池
    }
    // 新建一个节点
    AstarNodePool.prototype.newNode = function () {
        if (this.nodePool.length > 0) {
            return this.nodePool.pop();
        }
        return new Node();
    };
    AstarNodePool.prototype.removeNode = function (node) {
        this.nodePool.push(node);
    };
    return AstarNodePool;
}());
var commonNodePool = new AstarNodePool();
/**
 * A星
 */
var AStar = /** @class */ (function () {
    function AStar() {
        this.tempPathPoints = [];
        this.tempDisV2 = cc.v2();
        this.nodePool = []; // 节点对象池
        this.nodePoolIdx = 0;
        this.opened = [];
        this.closed = {};
        this.dirPoints = [];
        this.dirCount = 0;
        this.checkCanPass = null; // 检测当前点是否可以通过
        this.area = null;
        this.mapHeight = 0;
        this.mapWidth = 0;
    }
    AStar.prototype.setDir = function (cnt) {
        this.dirPoints = cnt === 4 ? AStarConfig_1.DIR_POINTS_4 : AStarConfig_1.DIR_POINTS_8;
        this.dirCount = this.dirPoints.length;
    };
    AStar.prototype.clean = function () {
        this.checkCanPass = null;
        this.area = null;
        this.opened = null;
        this.closed = null;
        this.dirPoints = null;
        this.tempPathPoints = null;
        this.nodePool.forEach(function (node) {
            commonNodePool.removeNode(node);
        });
        this.nodePool.length = 0;
        this.nodePool = null;
    };
    AStar.prototype.reset = function () {
        this.nodePoolIdx = 0;
        this.opened.length = 0;
        this.closed = {};
        this.tempPathPoints.length = 0;
        var count = Math.floor(this.nodePool.length * 0.5);
        for (var i = 0; i < count; i++) {
            var node = this.nodePool.pop();
            commonNodePool.removeNode(node);
        }
    };
    // 新建一个节点
    AStar.prototype.newNode = function (x, y) {
        var node = this.nodePool[this.nodePoolIdx++];
        if (!node) {
            node = commonNodePool.newNode();
            this.nodePool.push(node);
        }
        node.init(x, y);
        return node;
    };
    // 是否在范围内
    AStar.prototype.inMapRange = function (point) {
        return point.x >= 0 && point.x < this.mapWidth && point.y >= 0 && point.y < this.mapHeight;
    };
    // 检查拐角是否有障碍 只用于8个方向行走
    AStar.prototype.checkCornerHasTile = function (x1, y1, x2, y2) {
        return this.checkCanPass(x1, y1, this.area) && this.checkCanPass(x2, y2, this.area);
    };
    // 寻路
    AStar.prototype.searchPath = function (start, end, useNotOptimize, removePop) {
        return __awaiter(this, void 0, Promise, function () {
            var cnt, node, has, _loop_1, this_1, i;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.opened) {
                            return [2 /*return*/, []];
                        }
                        if (start.equals(end) || !this.inMapRange(end) /*  || !this.checkCanPass(end.x, end.y, this.area) */) {
                            return [2 /*return*/, []];
                        }
                        this.reset();
                        // 把第一个点装进开起列表
                        this.opened.push(this.newNode(start.x, start.y));
                        cnt = 0;
                        _a.label = 1;
                    case 1:
                        if (!(this.opened.length > 0)) return [3 /*break*/, 4];
                        node = this.opened.shift();
                        if (node.point.equals(end)) {
                            return [2 /*return*/, Promise.resolve(this.genPoints(node, !!useNotOptimize, !!removePop))];
                        }
                        this.closed[node.uid] = true;
                        has = false;
                        _loop_1 = function (i) {
                            var d = this_1.dirPoints[i];
                            var x = node.x + d.point.x;
                            var y = node.y + d.point.y;
                            if (node.ignoreCheck) {
                            }
                            else if (!end.equals2(x, y) && (!this_1.checkCanPass(x, y, this_1.area) || this_1.closed[x + '_' + y])) {
                                return "continue";
                            }
                            else if (this_1.dirCount === 8 && d.tag !== 1 && !this_1.checkCornerHasTile(x, node.y, node.x, y)) { // 优化拐歪
                                return "continue";
                            }
                            has = true;
                            // 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
                            var it = this_1.opened.find(function (m) { return m.point.equals2(x, y); });
                            if (it) {
                                if (node.G + d.tag < it.G) {
                                    it.updateParent(node, d.tag);
                                }
                            }
                            else {
                                var temp = this_1.newNode(x, y);
                                temp.H = end.sub(temp.point, this_1.tempDisV2).mag();
                                temp.updateParent(node, d.tag);
                                this_1.opened.push(temp);
                            }
                        };
                        this_1 = this;
                        // 找周围的是否可以移动
                        for (i = 0; i < this.dirCount; i++) {
                            _loop_1(i);
                        }
                        // 如果当前生在障碍中 那么下次就忽略障碍
                        if (!has && !this.checkCanPass(node.x, node.y, this.area)) {
                            this.opened.unshift(node);
                            node.ignoreCheck = true;
                            // cc.error('searchPath ignoreCheck', node.point.toString(), this.area)
                        }
                        else {
                            node.ignoreCheck = false;
                        }
                        // 排序
                        this.opened.sort(function (a, b) { return a.F - b.F; });
                        if (!(++cnt > 50)) return [3 /*break*/, 3];
                        cnt = 0;
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!this.opened) {
                            return [2 /*return*/, []];
                        }
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, []];
                }
            });
        });
    };
    AStar.prototype.genPoints = function (node, useNotOptimize, removePop) {
        var _this = this;
        this.tempPathPoints.length = 0;
        while (node.parent !== null) {
            this.tempPathPoints.push(node.point);
            node = node.parent;
        }
        this.tempPathPoints.reverse();
        // 是否删除最后一个
        if (removePop && this.tempPathPoints.length > 1) {
            this.tempPathPoints.pop();
        }
        // 是否优化 删除没有障碍的点
        if (!useNotOptimize) {
            var _loop_2 = function (i) {
                var p = this_2.tempPathPoints[i];
                if (!this_2.dirPoints.some(function (m) { return !_this.checkCanPass(p.x + m.point.x, p.y + m.point.y, _this.area); })) {
                    this_2.tempPathPoints.splice(i, 1);
                }
            };
            var this_2 = this;
            for (var i = this.tempPathPoints.length - 2; i >= 1; i--) {
                _loop_2(i);
            }
        }
        return this.tempPathPoints;
    };
    return AStar;
}());
exports.default = AStar;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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