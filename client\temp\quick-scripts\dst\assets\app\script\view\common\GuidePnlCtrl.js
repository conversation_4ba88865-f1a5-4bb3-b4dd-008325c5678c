
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/GuidePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'df58b39M8hI5qlz4KMqViKx', 'GuidePnlCtrl');
// app/script/view/common/GuidePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var EventType_1 = require("../../common/event/EventType");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var GuideConfig_1 = require("../../model/guide/GuideConfig");
var TypeWriterCmpt_1 = require("../cmpt/TypeWriterCmpt");
var ccclass = cc._decorator.ccclass;
var GuidePnlCtrl = /** @class */ (function (_super) {
    __extends(GuidePnlCtrl, _super);
    function GuidePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.maskNode_ = null; // path://mask_n
        _this.boxNode_ = null; // path://box_n
        _this.descNode_ = null; // path://desc_n
        _this.fingerNode_ = null; // path://finger_n
        _this.touchNode_ = null; // path://touch_n
        _this.dialogNode_ = null; // path://dialog_n
        _this.dialogTouchNode_ = null; // path://dialog_touch_be_n
        _this.loadingNode_ = null; // path://loading_n
        //@end
        _this.model = null;
        _this.touchId = -1;
        _this.chooseRect = null;
        _this.chooseDesc = '';
        _this.chooseDescOffset = null;
        _this.chooseFinger = null;
        _this.dialogs = []; //当前的对话列表
        _this.dialogAutoClose = false; //对话框是否自动关闭
        _this.lastDialogKey = ''; //最后一次对话key 用于上报判断
        _this.dialogTypeWriter = null; //对话框打字机
        _this.dialogRoleAnim = null; //说话的人
        _this.fullScreen = false; //是否全屏
        _this.hideChoose = false; //是否隐藏选择框
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._descNodeDefaultWidth = 0; //默认宽度
        _this._descLblDefaultWidth = 0; //默认宽度
        return _this;
    }
    GuidePnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f;
        return [
            (_a = {}, _a[EventType_1.default.GUIDE_SHOW_DIALOG] = this.onGuideShowDialog, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.GUIDE_SHOW_NODE_CHOOSE] = this.onGuideShowNodeChoose, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.GUIDE_RESET] = this.onGuideReset, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.GUIDE_CANCEL_FORCE] = this.onGuideCancelForce, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.GUIDE_CHOOSE_RECT_CHANGE] = this.onGuideChooseRectChange, _e.enter = true, _e),
            (_f = {}, _f[mc.Event.LANGUAGE_CHANGED] = this.onLanguageChanged, _f),
        ];
    };
    GuidePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false, isClean: false });
                this.model = this.getModel('guide');
                this.dialogTypeWriter = this.dialogNode_.FindChild('text', TypeWriterCmpt_1.default);
                this.dialogRoleAnim = this.dialogNode_.FindChild('role', cc.Animation);
                this.touchNode_.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
                this.touchNode_.SetSwallowTouches(false);
                this._descNodeDefaultWidth = this.descNode_.width;
                this._descLblDefaultWidth = this.descNode_.FindChild('val').width;
                return [2 /*return*/];
            });
        });
    };
    GuidePnlCtrl.prototype.onEnter = function (data) {
        this.maskNode_.Child('di').setContentSize(cc.winSize);
        this.reset();
    };
    GuidePnlCtrl.prototype.onRemove = function () {
    };
    GuidePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://dialog_touch_be_n
    GuidePnlCtrl.prototype.onClickDialogTouch = function (event, data) {
        if (!this.dialogNode_.Data) {
            return this.dialogTypeWriter.stop();
        }
        if (this.dialogs.length > 0) {
            audioMgr.playSFX('click');
            this.showNextDialog();
        }
        else {
            this.openDialogBox(false);
            if (!this.dialogAutoClose) {
                this.model.nextStep();
            }
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 打开对话框
    GuidePnlCtrl.prototype.onGuideShowDialog = function (content, autoClose, isMask) {
        this.dialogs = content.slice();
        this.dialogAutoClose = !!autoClose;
        this.openDialogBox(true);
        this.showNextDialog();
        if (this.dialogAutoClose) {
            this.model.nextStep();
        }
        this.maskNode_.active = isMask;
        this.maskNode_.opacity = isMask ? 255 : 0;
        this.maskNode_.getComponent(cc.Mask).enabled = false;
    };
    // 打开框选节点
    GuidePnlCtrl.prototype.onGuideShowNodeChoose = function (data) {
        var _a;
        this.chooseDesc = data.desc;
        this.chooseDescOffset = (_a = data.descOffset) !== null && _a !== void 0 ? _a : cc.Vec2.ZERO;
        this.chooseFinger = data.finger;
        this.fullScreen = data.fullScreen;
        this.hideChoose = data.hideChoose;
        this.beginChooseRect(data);
    };
    GuidePnlCtrl.prototype.onGuideReset = function () {
        this.reset();
    };
    // 取消和开启强制引导
    GuidePnlCtrl.prototype.onGuideCancelForce = function (val, isReset) {
        if (isReset === void 0) { isReset = true; }
        if (isReset) {
            this.reset();
        }
        this.touchNode_.active = val;
    };
    GuidePnlCtrl.prototype.onGuideChooseRectChange = function (data) {
        var size = data.size;
        if (data.visible) {
            this.chooseRect.height += size.height;
            this.chooseRect.y -= size.height;
        }
        else {
            this.chooseRect.height -= size.height;
            this.chooseRect.y += size.height;
        }
        this.chooseRect = cc.rect(this.chooseRect.x, this.chooseRect.y, this.chooseRect.width, this.chooseRect.height);
        this.playChooseRect(cc.v2(this.chooseRect.width / this.maskNode_.width, this.chooseRect.height / this.maskNode_.height), true);
    };
    // 切换语言
    GuidePnlCtrl.prototype.onLanguageChanged = function () {
        this.dialogTypeWriter.updateFont();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    GuidePnlCtrl.prototype.update = function (dt) {
        // if (this.dialogAutoCloseTime > 0) {
        //     this.dialogAutoCloseTime -= dt
        //     if (this.dialogAutoCloseTime <= 0) {
        //         this.dialogAutoCloseTime = 0
        //         this.openDialogBox(false)
        //         this.model.nextStep()
        //     }
        // }
    };
    GuidePnlCtrl.prototype.reset = function () {
        if (!this.dialogAutoClose) {
            this.openDialogBox(false);
            this.dialogs = [];
        }
        this.maskNode_.active = false;
        this.boxNode_.active = false;
        this.descNode_.active = false;
        this.fingerNode_.active = false;
        this.loadingNode_.active = false;
        this.chooseRect = null;
        this.chooseDesc = '';
        this.chooseFinger = null;
        AnimHelper_1.animHelper.hideAllFinger(false);
    };
    // 一些特殊步骤的特殊提示
    GuidePnlCtrl.prototype.specialAlert = function () {
        if (this.model.isOneGuideWorking()) {
            if (this.lastDialogKey === 'guideText.dialog_100020') {
                ViewHelper_1.viewHelper.showAlert('toast.guide_first_move_alert');
            }
            else if (this.lastDialogKey === 'guideText.dialog_100040') {
                ViewHelper_1.viewHelper.showAlert('toast.guide_first_battle_alert');
            }
            else if (this.model.isSpecialStepByIndex(38)) { // 38：修建兵营的下标
                ViewHelper_1.viewHelper.showAlert('toast.guide_first_build_alert');
            }
        }
    };
    GuidePnlCtrl.prototype.onTouchStart = function (event) {
        if (this.touchId !== -1) {
            event.stopPropagation();
        }
        else if (!this.checkSection(event.getLocation())) {
            this.specialAlert();
            event.stopPropagation();
            if (this.model.checkFuncWeakStep()) {
                this.touchId = event.getID();
            }
            else {
                this.touchId = -1;
            }
        }
        else {
            this.touchId = event.getID();
        }
    };
    GuidePnlCtrl.prototype.onTouchEnd = function (event) {
        if (this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        if (this.checkSection(event.getLocation()) || this.model.checkFuncWeakStep()) {
            if (this.hideChoose) {
                this.reset();
            }
            this.emit(EventType_1.default.GUIDE_CLICK_CHOOSE_RECT);
        }
    };
    GuidePnlCtrl.prototype.onTouchMove = function (event) {
        event.stopPropagation();
    };
    GuidePnlCtrl.prototype.onTouchCancel = function (event) {
        event.stopPropagation();
        if (this.touchId === event.getID()) {
            this.touchId = -1;
        }
    };
    GuidePnlCtrl.prototype.checkSection = function (pos) {
        if (!this.maskNode_.active || !this.maskNode_.Data || !this.chooseRect) {
            return false;
        }
        pos.x -= cc.winSize.width * 0.5;
        pos.y -= cc.winSize.height * 0.5;
        return this.chooseRect.contains(pos);
    };
    GuidePnlCtrl.prototype.openDialogBox = function (val) {
        this.dialogNode_.active = val;
        this.dialogTouchNode_.active = val;
        this.dialogTypeWriter.clean();
    };
    // 显示对话框内容
    GuidePnlCtrl.prototype.showNextDialog = function () {
        var _this = this;
        var dialog = this.dialogs.shift();
        var params = [];
        switch (dialog.args) {
            case GuideConfig_1.GuideTextArgsType.PawnToHeroId:
                params.push(assetsMgr.lang('pawnText.name_' + GameHelper_1.gameHpr.user.getPortrayals()[0].json.avatar_pawn));
                break;
            case GuideConfig_1.GuideTextArgsType.FirstHeroName:
                params.push(assetsMgr.lang('portrayalText.name_' + GameHelper_1.gameHpr.user.getPortrayals()[0].id));
                break;
            case GuideConfig_1.GuideTextArgsType.TreasureIronCount:
                params.push(GameHelper_1.gameHpr.noviceServer.tempTreasureIronCount);
                break;
        }
        var text = this.lastDialogKey = dialog.text;
        this.dialogNode_.Data = false;
        var next = this.dialogNode_.Child('next');
        var desc = this.dialogNode_.Child('desc');
        desc.setLocaleKey(dialog.desc || '', params);
        desc.opacity = next.opacity = 0;
        this.dialogRoleAnim.play('xsjc_talk');
        this.dialogTypeWriter.run(text || '', null, params).then(function () {
            _this.dialogRoleAnim.play('xsjc_stand');
            _this.dialogNode_.Data = true;
            next.opacity = desc.opacity = 255;
        });
    };
    // 找节点
    GuidePnlCtrl.prototype.findNode = function (path) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.loadingNode_.active = true;
                        return [4 /*yield*/, ut.findNode(path)];
                    case 1:
                        it = _a.sent();
                        this.loadingNode_.active = false;
                        if (it) {
                            return [2 /*return*/, this.getTargetInfo(it)];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    // 获取目标节点信息
    GuidePnlCtrl.prototype.getTargetInfo = function (node) {
        var _a = node.getBoundingBox(), xMin = _a.xMin, yMin = _a.yMin, xMax = _a.xMax, yMax = _a.yMax;
        var lbPos = GuideHelper_1.guideHelper.convertToNodePosAR(node.parent, this.maskNode_.parent, cc.v2(xMin, yMin));
        var rtPos = GuideHelper_1.guideHelper.convertToNodePosAR(node.parent, this.maskNode_.parent, cc.v2(xMax, yMax));
        var width = rtPos.x - lbPos.x;
        var height = rtPos.y - lbPos.y;
        this.chooseRect = cc.rect(lbPos.x, lbPos.y, width, height);
        return cc.v2(width / this.maskNode_.width, height / this.maskNode_.height);
    };
    // 转换世界rect
    GuidePnlCtrl.prototype.convertWorldRect = function (rect) {
        var lbPos = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(cc.v2(rect.x, rect.y), this._temp_vec2_1);
        var rtPos = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(cc.v2(rect.x + rect.width, rect.y + rect.height), this._temp_vec2_2);
        var width = rtPos.x - lbPos.x;
        var height = rtPos.y - lbPos.y;
        this.chooseRect = cc.rect(lbPos.x - (cc.winSize.width * 0.5), lbPos.y - (cc.winSize.height * 0.5), width, height);
        return cc.v2(width / this.maskNode_.width, height / this.maskNode_.height);
    };
    // 开始选择
    GuidePnlCtrl.prototype.beginChooseRect = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var center;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!data.rect) return [3 /*break*/, 3];
                        if (!data.moveCamera) return [3 /*break*/, 2];
                        center = data.rect.center;
                        CameraCtrl_1.cameraCtrl.getWorldWinSize().mul(0.5, this._temp_vec2_1);
                        return [4 /*yield*/, CameraCtrl_1.cameraCtrl.moveTo(0.1, center.sub(this._temp_vec2_1, this._temp_vec2_2))];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        this.playChooseRect(this.convertWorldRect(data.rect), data.hide);
                        return [3 /*break*/, 4];
                    case 3:
                        if (data.node) {
                            this.playChooseRect(this.getTargetInfo(data.node), data.hide);
                        }
                        else if (data.path) {
                            this.findNode(data.path).then(function (scale) { return (_this.isValid && scale) && _this.playChooseRect(scale, data.hide); });
                        }
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 播放框选
    GuidePnlCtrl.prototype.playChooseRect = function (scale, hide) {
        var _this = this;
        this.maskNode_.active = true;
        this.maskNode_.opacity = hide ? 0 : 255;
        this.maskNode_.getComponent(cc.Mask).enabled = true;
        var center = this.chooseRect.center;
        if (hide) {
            return this.playChooseRectDone(center, scale);
        }
        cc.tween(this.maskNode_).to(0.4, { x: center.x, y: center.y, scaleX: scale.x, scaleY: scale.y }).call(function () {
            if (_this.isValid && _this.chooseRect) {
                _this.playChooseRectDone(center, scale);
            }
        }).start();
    };
    GuidePnlCtrl.prototype.playChooseRectDone = function (center, scale) {
        this.maskNode_.Data = true;
        this.maskNode_.setPosition(center.x, center.y);
        this.maskNode_.setScale(scale);
        this.setBox(this.chooseRect);
        this.setDesc(this.chooseRect);
        this.setFinger(this.chooseRect);
        if (this.fullScreen) {
            this.chooseRect = cc.rect(-cc.winSize.width / 2, -cc.winSize.height / 2, cc.winSize.width, cc.winSize.height);
        }
    };
    // 框框
    GuidePnlCtrl.prototype.setBox = function (rect) {
        if (this.boxNode_.active = !!this.maskNode_.opacity) {
            this.boxNode_.setPosition(rect.center.x, rect.center.y);
            this.boxNode_.setContentSize(rect.width + 8, rect.height + 8);
        }
    };
    // 引导描述
    GuidePnlCtrl.prototype.setDesc = function (rect) {
        if (!this.chooseDesc) {
            return;
        }
        var descLbl = this.descNode_.Child('val', cc.Label);
        descLbl.setLocaleKey(this.chooseDesc);
        descLbl.overflow = cc.Label.Overflow.NONE;
        descLbl._forceUpdateRenderData();
        if (descLbl.node.width > this._descLblDefaultWidth) {
            descLbl.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
            descLbl.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
            descLbl.node.width = this._descLblDefaultWidth;
            this.descNode_.width = this._descNodeDefaultWidth;
        }
        else {
            descLbl.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
            this.descNode_.width = descLbl.node.width + this._descNodeDefaultWidth - this._descLblDefaultWidth;
        }
        var halfWidth = this.descNode_.width / 2;
        var halfHeight = this.descNode_.height / 2;
        var halfScreen = cc.winSize.width / 2;
        var center = rect.center;
        var posX = center.x;
        var posY = center.y;
        var offsetY = rect.height / 2 + halfHeight + 50;
        if (halfScreen - center.x < halfWidth) {
            posX = halfScreen - halfWidth;
        }
        else if (center.x < -halfScreen + halfWidth) {
            posX = -halfScreen + halfWidth;
        }
        else if (cc.winSize.height / 2 - center.y < halfWidth) {
            offsetY = -offsetY;
        }
        if (this.chooseDescOffset) {
            posX += this.chooseDescOffset.x;
            posY += this.chooseDescOffset.y;
        }
        this.descNode_.setPosition(posX, posY + offsetY);
        this.descNode_.active = true;
        this.descNode_.opacity = 0;
        cc.tween(this.descNode_)
            .to(0.3, { opacity: 255 })
            .start();
    };
    // 设置手指
    GuidePnlCtrl.prototype.setFinger = function (rect) {
        if (!this.chooseFinger) {
            return;
        }
        this.fingerNode_.active = true;
        var center = rect.center, offset = this.chooseFinger.offset || cc.v2();
        var winWHratio = cc.winSize.width / cc.winSize.height;
        var designWHratio = 9 / 16;
        var ratio = winWHratio < designWHratio ? winWHratio / designWHratio : 1;
        offset.x *= ratio;
        this.fingerNode_.setPosition(center.x + offset.x, center.y + offset.y);
        this.fingerNode_.angle = this.chooseFinger.angle || 0;
        this.fingerNode_.scaleX = this.chooseFinger.flip ? -1 : 1;
        AnimHelper_1.animHelper.hideAllFinger(true);
    };
    GuidePnlCtrl = __decorate([
        ccclass
    ], GuidePnlCtrl);
    return GuidePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GuidePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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