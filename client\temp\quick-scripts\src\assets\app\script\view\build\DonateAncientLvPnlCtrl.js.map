{"version": 3, "sources": ["assets\\app\\script\\view\\build\\DonateAncientLvPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AAEpD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAc;IAAlE;QAAA,qEAiFC;QA/EG,0BAA0B;QAClB,cAAQ,GAAc,IAAI,CAAA,CAAC,gCAAgC;QAC3D,cAAQ,GAAe,IAAI,CAAA,CAAC,uCAAuC;QAC3E,MAAM;QAEE,UAAI,GAAU,IAAI,CAAA;QAClB,cAAQ,GAAW,CAAC,CAAA;QACpB,QAAE,GAAa,IAAI,CAAA;;QAmE3B,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IAErH,CAAC;IAtEU,gDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,yCAAQ,GAArB;;;;;;KACC;IAEM,wCAAO,GAAd,UAAe,IAAW,EAAE,QAAgB,EAAE,EAAY;QACtD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAA;QACzB,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,qBAAqB,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QACpE,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IAC1D,CAAC;IAEM,yCAAQ,GAAf;QACI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAA;IAClB,CAAC;IAEM,wCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,oBAAoB;IACpB,0CAAS,GAAT,UAAU,KAA0B,EAAE,IAAY;QAC9C,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;QACjD,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,sBAAsB,CAAC,CAAA;SAC5D;aAAM,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,4BAA4B,CAAC,CAAA;SAClE;aAAM,IAAI,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE;YACnD,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,cAAc,CAAC,CAAA;SACpD;QACD,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QACzB,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAED,uCAAuC;IACvC,kDAAiB,GAAjB,UAAkB,KAAiB,EAAE,IAAY;QAC7C,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;QACzC,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;YACjB,OAAM;SACT;QACD,IAAI,GAAG,GAAG,KAAK,CAAA;QACf,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;YACvB,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;SACtB;QACD,IAAM,EAAE,GAAG,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC7C,IAAI,EAAE,GAAG,GAAG,EAAE;YACV,GAAG,GAAG,EAAE,CAAA;SACX;QACD,IAAI,GAAG,KAAK,KAAK,EAAE;YACf,KAAK,CAAC,MAAM,GAAG,EAAE,GAAG,GAAG,CAAA;SAC1B;IACL,CAAC;IAED,6BAA6B;IAC7B,2CAAU,GAAV,UAAW,KAA0B,EAAE,IAAY;QAC/C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;IAC3F,CAAC;IA3EgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CAiF1C;IAAD,6BAAC;CAjFD,AAiFC,CAjFmD,EAAE,CAAC,WAAW,GAiFjE;kBAjFoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ecode } from \"../../common/constant/ECode\";\r\nimport { CType } from \"../../common/constant/Enums\";\r\nimport { gameHpr } from \"../../common/helper/GameHelper\";\r\nimport { resHelper } from \"../../common/helper/ResHelper\";\r\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\r\n\r\nconst { ccclass } = cc._decorator;\r\n\r\n@ccclass\r\nexport default class DonateAncientLvPnlCtrl extends mc.BasePnlCtrl {\r\n\r\n    //@autocode property begin\r\n    private iconSpr_: cc.Sprite = null // path://root/content/bg/icon_s\r\n    private inputEb_: cc.EditBox = null // path://root/content/bg/input_eb_ebee\r\n    //@end\r\n\r\n    private type: CType = null\r\n    private maxCount: number = 0\r\n    private cb: Function = null\r\n\r\n    public listenEventMaps() {\r\n        return []\r\n    }\r\n\r\n    public async onCreate() {\r\n    }\r\n\r\n    public onEnter(type: CType, maxCount: number, cb: Function) {\r\n        this.type = type\r\n        this.maxCount = maxCount\r\n        this.cb = cb\r\n        this.inputEb_.string = ''\r\n        this.inputEb_.setPlaceholder('ui.max_donate_count', 'f_m', maxCount)\r\n        this.iconSpr_.spriteFrame = resHelper.getResIcon(type)\r\n    }\r\n\r\n    public onRemove() {\r\n        this.cb && this.cb(0)\r\n        this.cb = null\r\n    }\r\n\r\n    public onClean() {\r\n    }\r\n\r\n    // ----------------------------------------- button listener function -------------------------------------------\r\n    //@autocode button listener\r\n\r\n    // path://root/ok_be\r\n    onClickOk(event: cc.Event.EventTouch, data: string) {\r\n        const count = Number(this.inputEb_.string.trim())\r\n        if (!count) {\r\n            return viewHelper.showAlert(ecode.PLEASE_INPUT_RES_COUNT)\r\n        } else if (count > this.maxCount) {\r\n            return viewHelper.showAlert(ecode.ANCIETN_CONTRIBUTE_RES_LIMIT)\r\n        } else if (gameHpr.getCountByCType(this.type) < count) {\r\n            return viewHelper.showAlert(ecode.RES_NOT_ENOUGH)\r\n        }\r\n        this.cb && this.cb(count)\r\n        this.hide()\r\n    }\r\n\r\n    // path://root/content/bg/input_eb_ebee\r\n    onClickInputEnded(event: cc.EditBox, data: string) {\r\n        const count = Number(event.string.trim())\r\n        if (!count) {\r\n            event.string = ''\r\n            return\r\n        }\r\n        let cnt = count\r\n        if (count > this.maxCount) {\r\n            cnt = this.maxCount\r\n        }\r\n        const ac = gameHpr.getCountByCType(this.type)\r\n        if (ac < cnt) {\r\n            cnt = ac\r\n        }\r\n        if (cnt !== count) {\r\n            event.string = '' + cnt\r\n        }\r\n    }\r\n\r\n    // path://root/content/max_be\r\n    onClickMax(event: cc.Event.EventTouch, data: string) {\r\n        this.inputEb_.string = Math.min(this.maxCount, gameHpr.getCountByCType(this.type)) + ''\r\n    }\r\n    //@end\r\n    // ----------------------------------------- event listener function --------------------------------------------\r\n\r\n    // ----------------------------------------- custom function ----------------------------------------------------\r\n\r\n}\r\n"]}