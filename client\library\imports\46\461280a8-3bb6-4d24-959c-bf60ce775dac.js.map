{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\CollectionSkinInfoPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,qEAA+E;AAC/E,6DAAyD;AACzD,6DAA4D;AAC5D,2DAA0D;AAC1D,6DAA4D;AAC5D,yEAAoE;AAE5D,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAuD,6CAAc;IAArE;QAAA,qEA6OC;QA3OG,0BAA0B;QAClB,eAAS,GAAa,IAAI,CAAA,CAAC,4BAA4B;QACvD,aAAO,GAAY,IAAI,CAAA,CAAC,mBAAmB;QAC3C,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,uBAAiB,GAAY,IAAI,CAAA,CAAC,iCAAiC;QACnE,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAC7D,MAAM;QAEE,UAAI,GAAQ,IAAI,CAAA;QAChB,cAAQ,GAA2B,IAAI,CAAA;QAEvC,UAAI,GAAW,EAAE,CAAA;QACjB,UAAI,GAAU,EAAE,CAAA;QAChB,cAAQ,GAAQ,IAAI,CAAA;QACpB,aAAO,GAAW,CAAC,CAAA;QACnB,aAAO,GAAW,CAAC,CAAA;QACnB,QAAE,GAAa,IAAI,CAAA;;IAwN/B,CAAC;IAtNU,mDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,4CAAQ,GAArB;;;;;;KACC;IAEM,2CAAO,GAAd,UAAe,IAAkE,EAAE,EAAY;QAC3F,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAEM,4CAAQ,GAAf;IACA,CAAC;IAEM,2CAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,oCAAoC;IACpC,+CAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED,mCAAmC;IACnC,8CAAU,GAAV,UAAW,KAA0B,EAAE,KAAa;QAChD,IAAI,oBAAO,CAAC,SAAS,EAAE,EAAE;YACrB,OAAO,uBAAU,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;SACvD;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;gBACzB,OAAO,uBAAU,CAAC,SAAS,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,CAAC,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAA;aAC1H;YACD,IAAI,CAAC,gBAAgB,EAAE,CAAA;SAC1B;aAAM;YACH,IAAI,CAAC,cAAc,EAAE,CAAA;SACxB;IACL,CAAC;IAED,+BAA+B;IAC/B,8CAAU,GAAV,UAAW,KAA0B,EAAE,KAAa;;QAChD,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0CAAE,IAAI,KAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;QAC5E,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAED,iCAAiC;IACjC,uDAAmB,GAAnB,UAAoB,KAA0B,EAAE,KAAa;QAA7D,iBAGC;;QAFG,IAAM,EAAE,SAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,KAAI,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAA/C,CAA+C,CAAC,0CAAE,EAAE,CAAA;QAC7G,uBAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAA;IACjD,CAAC;IAED,oCAAoC;IACpC,mDAAe,GAAf,UAAgB,KAA0B,EAAE,IAAY;;QACpD,IAAM,GAAG,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QACzD,IAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,CAAA;QAClD,IAAI,KAAK,KAAK,OAAO,EAAE;YACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;YACpB,IAAM,KAAK,SAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,SAAS,CAAA;YACzC,IAAI,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAA;SAC/F;IACL,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEzG,mDAAe,GAAvB;QACI,OAAO,oBAAO,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAC1E,CAAC;IAEO,kDAAc,GAAtB,UAAuB,IAAkE;;QACrF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAI,CAAC,QAAQ,GAAG,OAAA,IAAI,CAAC,KAAK,0CAAE,MAAM,IAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAC7D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAA;QAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC/B,IAAI,CAAC,IAAI,GAAG,OAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0CAAE,IAAI,KAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,CAAA;QAC5C,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAA;QAC5C,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,CAAA;QAClC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAA;QACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QAC/C,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAA,CAAC,0BAA0B;QACrH,IAAI,UAAU,EAAE;YACZ,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAA;YAC/C,IAAM,UAAU,GAAG,oBAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC3F,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,iCAAiC;gBAC5F,IAAI,IAAI,EAAE,EAAE,OAAO;oBACf,IAAI,KAAK,GAAG,CAAC,EAAE,EAAE,MAAM;wBACnB,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;wBAC9C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAA;qBACxD;yBAAM,EAAK,YAAY;wBACpB,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;wBAC5C,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;wBAClB,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAM,OAAO,GAAG,KAAK,KAAK,CAAC,CAAA;wBAC3B,OAAO,CAAC,YAAY,GAAG,OAAO,CAAA;wBAC9B,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;wBACtD,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;wBACzF,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;wBAClC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;wBAC3E,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,EAAE;4BACnC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,2BAA2B,EAAE,oBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;yBAC5H;qBACJ;iBACJ;qBAAM;oBACH,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC9C,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;oBAChF,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,CAAA;iBACxD;aACJ;iBAAM;gBACH,IAAM,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;gBACvE,IAAM,OAAO,GAAG,oBAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;gBAC3D,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC9C,IAAI,IAAI,EAAE;oBACN,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;oBACjE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;iBAC9E;qBAAM,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;oBACvC,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC/C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;oBACpB,uBAAU,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;iBAC5C;qBAAM;oBACH,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;oBACtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,CAAA;iBACxD;aACJ;SACJ;aAAM;YACH,IAAM,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,CAAA;YACvE,IAAM,OAAO,GAAG,oBAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAA;YAC3D,IAAM,UAAU,GAAG,oBAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAA;YACtD,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC9C,IAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACzC,IAAI,IAAI,EAAE;gBACN,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;gBACjE,MAAM,CAAC,OAAO,GAAG,CAAC,oBAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;aACpD;iBAAM,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;gBACvC,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC/C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;gBACpB,uBAAU,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;aAC5C;iBAAM;gBACH,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC9C,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;gBACtD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,CAAA;aACxD;SACJ;QACD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;IAC7B,CAAC;IAEa,4CAAQ,GAAtB,UAAuB,UAAmB;;;;;;;wBAChC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;wBAChB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;6BAC1E,UAAU,EAAV,wBAAU;wBACV,IAAI,CAAC,iBAAiB,EAAE,CAAA;wBACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;wBACpB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;wBAC7E,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;wBACpB,qBAAM,SAAS,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAA/E,GAAG,GAAG,SAAyE;wBACrF,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBAC3B,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;wBACjC,IAAI,GAAG,wCAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;wBAC9C,IAAI,CAAC,CAAC,IAAI,EAAE;4BACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,gCAAsB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;4BACzH,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;yBACvB;6BAAM;4BACH,MAAA,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,0CAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO,EAAC;yBAC3E;;;wBAED,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;;;;;;KAEtD;IAEa,oDAAgB,GAA9B;;;;;;wBACU,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;wBAMjC,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;;wBAApF,KAAgB,SAAoE,EAAlF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,KAAK,aAAK,CAAC,cAAc,EAAE;4BAC9B,sBAAO,uBAAU,CAAC,SAAS,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,CAAC,qBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAA;yBAC/G;6BAAM,IAAI,GAAG,EAAE;4BACZ,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACrB,uBAAU,CAAC,SAAS,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,CAAC,qBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAA;yBACxG;6BAAM;4BACH,uBAAU,CAAC,SAAS,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,CAAC,qBAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAA;yBACxG;wBACD,oBAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;wBACrD,IAAI,CAAC,IAAI,EAAE,CAAA;;;;;KACd;IAEa,kDAAc,GAA5B;;;;;;wBACU,UAAU,GAAG,oBAAO,CAAC,aAAa,CAAC,oBAAO,CAAC,MAAM,EAAE,CAAC,EACtD,KAAK,GAAG,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,aAAa,KAAI,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAC1D,MAAM,GAAG,EAAE,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;wBACzC,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,EAAE,IAAI,CAAC,EAAA;;wBAA5F,KAAgB,SAA4E,EAA1F,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;wBACD,oBAAO,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;wBAC3C,uBAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;wBACrC,IAAI,CAAC,IAAI,EAAE,CAAA;wBACX,uBAAU,CAAC,YAAY,EAAE,CAAA;;;;;KAC5B;IAED,0CAAM,GAAN,UAAO,EAAU;;QACb,MAAA,IAAI,CAAC,QAAQ,0CAAE,WAAW,CAAC,EAAE,GAAG,IAAI,EAAC;IACzC,CAAC;IA5OgB,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CA6O7C;IAAD,gCAAC;CA7OD,AA6OC,CA7OsD,EAAE,CAAC,WAAW,GA6OpE;kBA7OoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ecode } from \"../../common/constant/ECode\";\nimport { SHOP_PAWN_SKIN_ANIM_CONF } from \"../../common/constant/FrameAnimConf\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { gotoHelper } from \"../../common/helper/GotoHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport PawnFrameAnimationCmpt from \"../cmpt/PawnFrameAnimationCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class CollectionSkinInfoPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private titleLbl_: cc.Label = null // path://root/title/title_l\n    private bgNode_: cc.Node = null // path://root/bg_n\n    private nameNode_: cc.Node = null // path://root/name_n\n    private iconNode_: cc.Node = null // path://root/icon_n\n    private timeNode_: cc.Node = null // path://root/time_n\n    private skinExchangeNode_: cc.Node = null // path://root/skin_exchange_be_n\n    private nextNode_: cc.Node = null // path://root/next_n\n    private buttonsNode_: cc.Node = null // path://root/buttons_n\n    //@end\n\n    private json: any = null\n    private animCmpt: PawnFrameAnimationCmpt = null\n\n    private type: string = ''\n    private list: any[] = []\n    private itemSkin: any = null\n    private curPage: number = 0\n    private maxPage: number = 0\n    private cb: Function = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: { list: any[], type: string, index?: number, skins?: any[] }, cb: Function) {\n        this.cb = cb\n        this.updateViewInfo(data)\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/buttons_n/lay/give_be\n    onClickGive(event: cc.Event.EventTouch, data: string) {\n        viewHelper.showPnl('menu/Personal', 0)\n    }\n\n    // path://root/buttons_n/lay/use_be\n    onClickUse(event: cc.Event.EventTouch, _data: string) {\n        if (gameHpr.isInLobby()) {\n            return viewHelper.showAlert('toast.please_game_use')\n        } else if (this.type === 'pawn_skin') {\n            if (!this.checkPawnUnlock()) {\n                return viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [resHelper.getPawnName(this.json.pawn_id)] })\n            }\n            this.syncInfoToServer()\n        } else {\n            this.changeCitySkin()\n        }\n    }\n\n    // path://root/buttons_n/buy_be\n    onClickBuy(event: cc.Event.EventTouch, _data: string) {\n        this.cb && this.cb(this.list[this.curPage]?.json || this.list[this.curPage])\n        this.hide()\n    }\n\n    // path://root/skin_exchange_be_n\n    onClickSkinExchange(event: cc.Event.EventTouch, _data: string) {\n        const id = assetsMgr.getJson('pawnSkin').datas.find(m => m.value.toString().includes(this.json.id + ','))?.id\n        viewHelper.showPnl('common/SkinExchange', id)\n    }\n\n    // path://root/next_n/next_page_be@0\n    onClickNextPage(event: cc.Event.EventTouch, data: string) {\n        const add = data === '0' ? -1 : 1\n        const curPage = this.curPage || 0, maxPage = this.maxPage\n        const index = ut.loopValue(curPage + add, maxPage)\n        if (index !== curPage) {\n            this.curPage = index\n            const skins = this.list[index]?.itemSkins\n            this.updateViewInfo({ list: this.list, type: this.type, index: this.curPage, skins: skins })\n        }\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private checkPawnUnlock() {\n        return gameHpr.player.getAllCanRecruitPawnIds().has(this.json.pawn_id)\n    }\n\n    private updateViewInfo(data: { type: string, list: any[], index?: number, skins?: any[] }) {\n        this.type = data.type\n        this.list = data.list\n        this.itemSkin = data.skins?.length > 0 ? data.skins[0] : null\n        this.curPage = data.index || 0\n        this.maxPage = this.list.length\n        this.json = this.list[this.curPage]?.json || this.list[this.curPage]\n        this.titleLbl_.setLocaleKey(this.json.desc)\n        const isPawnSkin = this.type === 'pawn_skin'\n        this.nextNode_.active = !!(this.maxPage - 1)\n        this.nameNode_.active = isPawnSkin\n        this.timeNode_.active = isPawnSkin && !!this.itemSkin\n        this.bgNode_.Swih(isPawnSkin ? 'pawn' : 'city')\n        this.skinExchangeNode_.active = isPawnSkin && this.json.cond > 102 && this.json.type !== 5 // 101牛仔 102机甲没有炫彩且隐藏皮肤不显示\n        if (isPawnSkin) {\n            const skin = this.itemSkin, state = skin?.state\n            const unlockList = gameHpr.user.getUnlockPawnSkinIds(), have = unlockList.has(this.json.id)\n            if (this.json.cond > 100 || (this.json.cond === 5 && !have)) { // 盲盒皮肤 || 5 炫彩皮肤(一旦拥有会和盲盒皮肤分开存放)\n                if (skin) { // 已经有了\n                    if (state < 0) { // 封禁中\n                        const cond = this.buttonsNode_.Swih('cond')[0]\n                        cond.Child('val').setLocaleKey('toast.ban_item_skin')\n                    } else {    // 正常 || 锁定中\n                        const lay = this.buttonsNode_.Swih('lay')[0]\n                        lay.Swih('', true)\n                        const giveBtn = lay.Child('give_be', cc.Button)\n                        const canGive = state === 0\n                        giveBtn.interactable = canGive\n                        giveBtn.Child('root', cc.MultiFrame).setFrame(canGive)\n                        giveBtn.Child('count/val', cc.Label).string = this.itemSkin ? data.skins.length + '' : ''\n                        const useBtn = lay.Child('use_be')\n                        useBtn.opacity = this.checkPawnUnlock() && !gameHpr.isInLobby() ? 255 : 150\n                        if (this.timeNode_.active = state > 0) {\n                            this.timeNode_.Child('val').setLocaleKey('ui.item_skin_surplus_time', gameHpr.millisecondToCountDown(Math.max(0, state)))\n                        }\n                    }\n                } else {\n                    const cond = this.buttonsNode_.Swih('cond')[0]\n                    const type = (this.json.cond > 100 || this.json.cond === 5) ? 2 : this.json.cond\n                    cond.Child('val').setLocaleKey('ui.get_cond_' + type)\n                }\n            } else {\n                const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n                const buyList = gameHpr.user.getCanBuyPawnSkins(serverArea)\n                const canBuy = buyList.has('id', this.json.id)\n                if (have) {\n                    const useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0]\n                    useBtn.opacity = this.checkPawnUnlock() && !gameHpr.isInLobby() ? 255 : 150\n                } else if (canBuy || this.json.cond === 3) {\n                    const buy = this.buttonsNode_.Swih('buy_be')[0]\n                    buy.Data = this.json\n                    viewHelper.updateCostText(buy, this.json)\n                } else {\n                    const cond = this.buttonsNode_.Swih('cond')[0]\n                    const type = this.json.cond === 1 ? 2 : this.json.cond\n                    cond.Child('val').setLocaleKey('ui.get_cond_' + type)\n                }\n            }\n        } else {\n            const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'\n            const buyList = gameHpr.user.getCanBuyCitySkins(serverArea)\n            const unlockList = gameHpr.user.getUnlockCitySkinIds()\n            const canBuy = buyList.has('id', this.json.id)\n            const have = unlockList.has(this.json.id)\n            if (have) {\n                const useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0]\n                useBtn.opacity = !gameHpr.isInLobby() ? 255 : 150\n            } else if (canBuy || this.json.cond === 3) {\n                const buy = this.buttonsNode_.Swih('buy_be')[0]\n                buy.Data = this.json\n                viewHelper.updateCostText(buy, this.json)\n            } else {\n                const cond = this.buttonsNode_.Swih('cond')[0]\n                const type = this.json.cond === 1 ? 2 : this.json.cond\n                cond.Child('val').setLocaleKey('ui.get_cond_' + type)\n            }\n        }\n        this.loadSkin(isPawnSkin)\n    }\n\n    private async loadSkin(isPawnSkin: boolean) {\n        const json = this.json\n        const root = this.iconNode_.Child('mask').Swih(isPawnSkin ? 'root' : 'val')[0]\n        if (isPawnSkin) {\n            root.removeAllChildren()\n            this.animCmpt = null\n            this.nameNode_.Child('val').setLocaleKey(resHelper.getPawnName(json.pawn_id))\n            viewHelper.showLoadingWait(true)\n            const pfb = await assetsMgr.loadTempRes('march/ROLE_' + json.id, cc.Prefab, this.key)\n            viewHelper.showLoadingWait(false)\n            const node = cc.instantiate2(pfb, root)\n            const conf = SHOP_PAWN_SKIN_ANIM_CONF[json.id]\n            if (!!conf) {\n                this.animCmpt = node.addComponent(PawnFrameAnimationCmpt).init(node.FindChild('body/anim', cc.Sprite), json.id, this.key)\n                this.animCmpt.play()\n            } else {\n                node.Child('body/anim', cc.Animation)?.play('role_' + json.id + '_walk')\n            }\n        } else {\n            resHelper.loadCityIcon(json.id, root, this.key)\n        }\n    }\n\n    private async syncInfoToServer() {\n        const pawnId = this.json.pawn_id, skinId = this.json.id\n        // const conf = gameHpr.player.getConfigPawnInfo(pawnId)\n        // if (conf.skinId === skinId) {\n        //     viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })\n        //     return this.hide()\n        // }\n        const { err, data } = await gameHpr.net.request('game/HD_UsePawnSkin', { pawnId, skinId })\n        if (err === ecode.PAWN_NOT_EXIST) {\n            return viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [resHelper.getPawnName(pawnId)] })\n        } else if (err) {\n            return viewHelper.showAlert(err)\n        } else if (data.hasPawn) {\n            viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })\n        } else {\n            viewHelper.showAlert('toast.replace_def_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })\n        }\n        gameHpr.player.changeConfigPawnSkinId(pawnId, skinId)\n        this.hide()\n    }\n\n    private async changeCitySkin() {\n        const playerInfo = gameHpr.getPlayerInfo(gameHpr.getUid()),\n            index = playerInfo?.mainCityIndex || -1, id = this.json.id,\n            skinId = id === playerInfo.cells.get(index).cityId ? 0 : id\n        const { err, data } = await gameHpr.net.request('game/HD_ChangeCitySkin', { index, skinId }, true)\n        if (err) {\n            return viewHelper.showAlert(err)\n        }\n        gameHpr.world.updateCitySkin(index, skinId)\n        viewHelper.hidePnl('menu/Collection')\n        this.hide()\n        gotoHelper.gotoMainCity()\n    }\n\n    update(dt: number) {\n        this.animCmpt?.updateFrame(dt * 1000)\n    }\n}\n"]}