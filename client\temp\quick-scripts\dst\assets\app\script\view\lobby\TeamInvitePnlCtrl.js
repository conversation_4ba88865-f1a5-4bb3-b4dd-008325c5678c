
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/TeamInvitePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4777eu53v1I/IOWFXJ1OowJ', 'TeamInvitePnlCtrl');
// app/script/view/lobby/TeamInvitePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TeamInvitePnlCtrl = /** @class */ (function (_super) {
    __extends(TeamInvitePnlCtrl, _super);
    function TeamInvitePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        return _this;
    }
    //@end
    TeamInvitePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    TeamInvitePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    TeamInvitePnlCtrl.prototype.onEnter = function () {
        this.updateList();
    };
    TeamInvitePnlCtrl.prototype.onRemove = function () {
    };
    TeamInvitePnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/apply_agree_be@0
    TeamInvitePnlCtrl.prototype.onClickApplyAgree = function (event, data) {
        var _this = this;
        var info = event.target.parent.Data;
        if (info) {
            var isAgree_1 = data === '1';
            GameHelper_1.gameHpr.team.inviteTeammateResponse(info.teamUid, isAgree_1).then(function (err) {
                if (err) {
                    _this.updateList();
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (!_this.isValid) {
                }
                else if (isAgree_1) {
                    _this.hide();
                }
                else {
                    _this.updateList();
                }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    TeamInvitePnlCtrl.prototype.updateList = function () {
        var _this = this;
        var list = GameHelper_1.gameHpr.team.getReceiveInvites();
        if (list.length === 0) {
            return this.hide();
        }
        this.listSv_.List(list.length, function (it, i) {
            var data = it.Data = list[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            it.Child('time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
        });
    };
    TeamInvitePnlCtrl = __decorate([
        ccclass
    ], TeamInvitePnlCtrl);
    return TeamInvitePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TeamInvitePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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