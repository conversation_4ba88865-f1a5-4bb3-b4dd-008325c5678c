
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/SceneBgAnimCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6fa30SUawVAA4Baq69cjnSw', 'SceneBgAnimCmpt');
// app/script/view/login/SceneBgAnimCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var SceneBgAnimCmpt = /** @class */ (function (_super) {
    __extends(SceneBgAnimCmpt, _super);
    function SceneBgAnimCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.di = null;
        _this.diSpeed = cc.v2();
        _this.mountain = null;
        _this.mountainSpeed = 30;
        _this.tree = null;
        _this.treeSpeed = 60;
        _this.land = null;
        _this.landSpeed = 100;
        _this.cloud = null;
        _this.cloudSpeed = cc.v2(50, 110);
        _this.cloudDelay = cc.v2(1, 5);
        return _this;
    }
    SceneBgAnimCmpt.prototype.onLoad = function () {
        var _this = this;
        var ww = cc.winSize.width, wh = cc.winSize.height;
        // di
        this.di = this.node.FindChild('di');
        var cntx = Math.ceil(ww / 64);
        cntx = cntx % 2 === 0 ? cntx : cntx + 1;
        var cnty = Math.ceil(wh / 64);
        cnty = cnty % 2 === 0 ? cnty : cnty + 1;
        this.di.children.forEach(function (m) {
            m.width = cntx * 64;
            m.height = cnty * 64;
        });
        var _a = this.di.children, di0 = _a[0], di1 = _a[1], di2 = _a[2], di3 = _a[3];
        di0.x = 0;
        di0.y = 0;
        di1.x = -di0.width;
        di1.y = 0;
        di2.x = 0;
        di2.y = di2.height;
        di3.x = -di3.width;
        di3.y = di3.height;
        this.diSpeed.x = ww / 50;
        this.diSpeed.y = wh / 50;
        // log.info(cntx, cnty)
        // mountain
        this.mountain = this.node.FindChild('mountain');
        this.initItem(this.mountain);
        // tree
        this.tree = this.node.FindChild('tree');
        this.initItem(this.tree);
        // land
        this.land = this.node.FindChild('land');
        this.initItem(this.land);
        // cloud
        this.cloud = this.node.FindChild('cloud');
        this.cloud.children.forEach(function (m) {
            m.Data = { delay: 0, speed: ut.random(_this.cloudSpeed.x, _this.cloudSpeed.y) };
            m.x = -cc.winSize.width - ut.random(1, 100);
        });
    };
    SceneBgAnimCmpt.prototype.initItem = function (item) {
        var mul = Math.ceil(cc.winSize.width / item.children[0].width);
        item.children.forEach(function (m) { return m.width *= mul; });
        var _a = item.children, it0 = _a[0], it1 = _a[1];
        it0.x = 0;
        it1.x = -it0.width;
    };
    SceneBgAnimCmpt.prototype.moveItem = function (item, speed) {
        var _a = item.children, it0 = _a[0], it1 = _a[1];
        it0.x += speed;
        it1.x += speed;
        if (it0.x >= it0.width) {
            it0.x = it1.x - it1.width;
        }
        else if (it1.x >= it1.width) {
            it1.x = it0.x - it0.width;
        }
    };
    SceneBgAnimCmpt.prototype.update = function (dt) {
        var _this = this;
        // di
        var diSpeedX = dt * this.diSpeed.x;
        var diSpeedY = dt * this.diSpeed.y;
        this.di.children.forEach(function (m) {
            m.x += diSpeedX;
            m.y -= diSpeedY;
        });
        var _a = this.di.children, di0 = _a[0], di1 = _a[1], di2 = _a[2], di3 = _a[3];
        if (di0.x >= di0.width) {
            di0.x = di1.x - di1.width;
            di2.x = di1.x - di1.width;
        }
        else if (di1.x >= di1.width) {
            di1.x = di0.x - di0.width;
            di3.x = di0.x - di0.width;
        }
        if (di0.y <= -di0.height) {
            di0.y = di2.y + di2.height;
            di1.y = di2.y + di2.height;
        }
        else if (di2.y <= -di2.height) {
            di2.y = di0.y + di0.height;
            di3.y = di0.y + di0.height;
        }
        // mountain
        this.moveItem(this.mountain, this.mountainSpeed * dt);
        // tree
        this.moveItem(this.tree, this.treeSpeed * dt);
        // land
        this.moveItem(this.land, this.landSpeed * dt);
        // cloud
        this.cloud.children.forEach(function (m) {
            if (m.Data.delay > 0) {
                m.Data.delay -= dt;
            }
            else {
                m.x += dt * m.Data.speed;
                if (m.x > m.width) {
                    m.x = -ut.random(cc.winSize.width, cc.winSize.width * 2);
                    m.Data.delay = ut.random(_this.cloudDelay.x, _this.cloudDelay.y);
                    m.Data.speed = ut.random(_this.cloudSpeed.x, _this.cloudSpeed.y);
                }
            }
        });
    };
    SceneBgAnimCmpt = __decorate([
        ccclass
    ], SceneBgAnimCmpt);
    return SceneBgAnimCmpt;
}(cc.Component));
exports.default = SceneBgAnimCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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