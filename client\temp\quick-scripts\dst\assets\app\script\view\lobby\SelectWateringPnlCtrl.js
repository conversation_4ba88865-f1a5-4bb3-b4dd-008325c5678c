
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/SelectWateringPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c44395t9RpLFKCRb93wJsxv', 'SelectWateringPnlCtrl');
// app/script/view/lobby/SelectWateringPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectWateringPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectWateringPnlCtrl, _super);
    function SelectWateringPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentTc_ = null; // path://root/content_tc_tce
        _this.tipLbl_ = null; // path://root/tip_l
        _this.buttonTextLbl_ = null; // path://root/ok_be/lay/button_text_l
        _this.goldNode_ = null; // path://root/ok_be/lay/gold_n
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    SelectWateringPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectWateringPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectWateringPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        this.contentTc_.Tabs(1);
    };
    SelectWateringPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    SelectWateringPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_tc_tce
    SelectWateringPnlCtrl.prototype.onClickContent = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.tipLbl_.setLocaleKey('ui.watering_tip_' + type);
        this.buttonTextLbl_.setLocaleKey('ui.button_watering_' + type);
        this.goldNode_.active = type === 2;
    };
    // path://root/ok_be
    SelectWateringPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var _a;
        var type = Number(((_a = this.contentTc_.toggleItems.find(function (m) { return m.isChecked; })) === null || _a === void 0 ? void 0 : _a.node.name) || 0);
        if (!type) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_watering');
        }
        GameHelper_1.gameHpr.user.watering(type).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.cb && _this.cb(type);
                _this.cb = null;
                _this.hide();
            }
        });
    };
    SelectWateringPnlCtrl = __decorate([
        ccclass
    ], SelectWateringPnlCtrl);
    return SelectWateringPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectWateringPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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