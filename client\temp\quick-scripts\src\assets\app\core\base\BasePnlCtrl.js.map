{"version": 3, "sources": ["assets\\app\\core\\base\\BasePnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAyC;AACzC,wDAAkD;AAElD,YAAY;AACZ;IAAyC,+BAAY;IAArD;QAAA,qEAoGC;QAlGU,SAAG,GAAW,EAAE,CAAA,CAAC,MAAM;QACvB,SAAG,GAAW,EAAE,CAAA,CAAC,MAAM;QACvB,SAAG,GAAW,EAAE,CAAA,CAAC,OAAO;QACxB,aAAO,GAAY,IAAI,CAAA;QACvB,WAAK,GAAY,IAAI,CAAA,CAAC,QAAQ;QAC9B,YAAM,GAAY,IAAI,CAAA,CAAC,QAAQ;QAC/B,iBAAW,GAAW,CAAC,CAAA,CAAC,SAAS;QACjC,iBAAW,GAAW,GAAG,CAAA,CAAC,QAAQ;QAElC,UAAI,GAAY,IAAI,CAAA,CAAC,QAAQ;QAC7B,kBAAY,GAAW,CAAC,CAAA,CAAC,OAAO;;IAwF3C,CAAC;IAtFgB,8BAAQ,GAArB;;;;;wBACI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;wBACtB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;wBACtB,IAAI,CAAC,YAAY,EAAE,CAAA;wBACnB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;wBACzB,qBAAM,IAAI,CAAC,QAAQ,EAAE,EAAA;;wBAArB,SAAqB,CAAA;;;;;KACxB;IAEM,6BAAO,GAAd;QAAe,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,2BAAc;;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAA;YACrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;SAC3B;QACD,IAAI,CAAC,OAAO,OAAZ,IAAI,WAAY,MAAM,GAAC;IAC3B,CAAC;IAEM,8BAAQ,GAAf;QACI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAA;QACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QAC1B,IAAI,CAAC,QAAQ,EAAE,CAAA;IACnB,CAAC;IAEM,6BAAO,GAAd;QACI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAA;QACrB,IAAI,CAAC,YAAY,EAAE,CAAA;QACnB,IAAI,CAAC,OAAO,EAAE,CAAA;IAClB,CAAC;IAEY,8BAAQ,GAArB;;;;;;KACC;IAEM,6BAAO,GAAd;QAAe,gBAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,2BAAc;;IAC7B,CAAC;IAEM,8BAAQ,GAAf;IACA,CAAC;IAEM,6BAAO,GAAd;IACA,CAAC;IAEM,0CAAoB,GAA3B;IACA,CAAC;IAEM,6BAAO,GAAd;QACI,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,CAAA;IAClC,CAAC;IAEM,0BAAI,GAAX;QACI,IAAI,CAAC,IAAI,CAAC,uBAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAEM,2BAAK,GAAZ;QACI,IAAI,CAAC,IAAI,CAAC,uBAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAEM,gCAAU,GAAjB,UAAkB,GAAW;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;QACvB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;SAC/D;IACL,CAAC;IAEM,8BAAQ,GAAf,UAAgB,GAAY;QACxB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;SACzB;IACL,CAAC;IAEM,8BAAQ,GAAf,UAAgB,KAAa;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACxB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,CAAA;SAC/B;IACL,CAAC;IAEM,wCAAkB,GAAzB;QACI,OAAO,IAAI,CAAC,WAAW,IAAI,GAAG,CAAA;IAClC,CAAC;IAEM,8BAAQ,GAAf,UAAgB,IAAc;;QAC1B,IAAI,CAAC,OAAO,SAAG,IAAI,CAAC,OAAO,mCAAI,IAAI,CAAC,OAAO,CAAA;QAC3C,IAAI,CAAC,KAAK,SAAG,IAAI,CAAC,KAAK,mCAAI,IAAI,CAAC,KAAK,CAAA;QACrC,IAAI,CAAC,MAAM,SAAG,IAAI,CAAC,MAAM,mCAAI,IAAI,CAAC,MAAM,CAAA;QACxC,IAAI,CAAC,WAAW,SAAG,IAAI,CAAC,WAAW,mCAAI,IAAI,CAAC,WAAW,CAAA;QACvD,IAAI,CAAC,WAAW,SAAG,IAAI,CAAC,WAAW,mCAAI,IAAI,CAAC,WAAW,CAAA;IAC3D,CAAC;IACL,kBAAC;AAAD,CApGA,AAoGC,CApGwC,sBAAY,GAoGpD", "file": "", "sourceRoot": "/", "sourcesContent": ["import BaseViewCtrl from \"./BaseViewCtrl\"\nimport CoreEventType from \"../event/CoreEventType\"\n\n// 基础UI视图控制器\nexport default class BasePnlCtrl extends BaseViewCtrl {\n\n    public key: string = '' // 传入名\n    public mod: string = '' // 模块名\n    public url: string = '' // UI地址\n    public isClean: boolean = true\n    public isAct: boolean = true //是否播放动作\n    public isMask: boolean = true //是否显示遮照\n    public maskOpacity: number = 0 //mask透明度\n    public adaptHeight: number = 400 //适应高度距离\n\n    public mask: cc.Node = null // 当前的遮照\n    public __open_index: number = 0 // 打开顺序\n\n    public async __create() {\n        this._state = 'create'\n        this.node.group = 'ui'\n        this.__listenMaps()\n        this.__register('create')\n        await this.onCreate()\n    }\n\n    public __enter(...params: any) {\n        if (this._state !== 'enter') {\n            this._state = 'enter'\n            this.__register('enter')\n        }\n        this.onEnter(...params)\n    }\n\n    public __remove() {\n        this._state = 'remove'\n        this.__unregister('enter')\n        this.onRemove()\n    }\n\n    public __clean() {\n        this._state = 'clean'\n        this.__unregister()\n        this.onClean()\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(...params: any) {\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    public onPlayActionComplete() {\n    }\n\n    public isEnter() {\n        return this._state === 'enter'\n    }\n\n    public hide() {\n        this.emit(CoreEventType.HIDE_PNL, this)\n    }\n\n    public close() {\n        this.emit(CoreEventType.CLOSE_PNL, this)\n    }\n\n    public setOpacity(val: number) {\n        this.node.opacity = val\n        if (this.mask) {\n            this.mask.opacity = Math.min(val, this.getMaskInitOpacity())\n        }\n    }\n\n    public showMask(val: boolean) {\n        if (this.mask) {\n            this.mask.active = val\n        }\n    }\n\n    public setIndex(index: number) {\n        this.node.zIndex = index\n        if (this.mask) {\n            this.mask.zIndex = index - 1\n        }\n    }\n\n    public getMaskInitOpacity() {\n        return this.maskOpacity || 153\n    }\n\n    public setParam(opts: PnlParam) {\n        this.isClean = opts.isClean ?? this.isClean\n        this.isAct = opts.isAct ?? this.isAct\n        this.isMask = opts.isMask ?? this.isMask\n        this.maskOpacity = opts.maskOpacity ?? this.maskOpacity\n        this.adaptHeight = opts.adaptHeight ?? this.adaptHeight\n    }\n}"]}