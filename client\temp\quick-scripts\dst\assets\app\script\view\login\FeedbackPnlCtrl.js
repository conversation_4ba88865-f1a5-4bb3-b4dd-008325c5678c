
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/FeedbackPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3083315dOZHVq5jn9qaC7JL', 'FeedbackPnlCtrl');
// app/script/view/login/FeedbackPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var LocalConfig_1 = require("../../common/LocalConfig");
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var FeedbackPnlCtrl = /** @class */ (function (_super) {
    __extends(FeedbackPnlCtrl, _super);
    function FeedbackPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentEb_ = null; // path://root/content_eb
        //@end
        _this.net = null;
        _this.httpUrl = '';
        _this.cb = null;
        _this.state = 0;
        return _this;
    }
    FeedbackPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    FeedbackPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.net = this.getModel('net');
                return [2 /*return*/];
            });
        });
    };
    FeedbackPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        this.state = 0;
    };
    FeedbackPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(this.state === 1);
        this.cb = null;
    };
    FeedbackPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/send_be
    FeedbackPnlCtrl.prototype.onClickSend = function (event, data) {
        var content = this.contentEb_.string.trim();
        if (content) {
            this.do(content);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    FeedbackPnlCtrl.prototype.getHttpUrl = function (route) {
        if (!this.httpUrl) {
            var url = LocalConfig_1.localConfig.errorReportUrl.test;
            if (GameHelper_1.gameHpr.isRelease) {
                url = LocalConfig_1.localConfig.errorReportUrl[GameHelper_1.gameHpr.getServerArea()] || url;
            }
            this.httpUrl = url.replace('errorReport', '');
        }
        return this.httpUrl + route;
    };
    FeedbackPnlCtrl.prototype.do = function (content) {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.net.post({
                            url: this.getHttpUrl('feedback'),
                            data: {
                                uid: GameHelper_1.gameHpr.getUid(),
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                serverId: GameHelper_1.gameHpr.getSid() || 0,
                                deviceOS: TaHelper_1.taHelper.getOsAndVersion(),
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                                version: GameHelper_1.gameHpr.getVersion(),
                                content: content,
                            },
                            wait: true
                        })];
                    case 1:
                        res = _a.sent();
                        this.state = (res === null || res === void 0 ? void 0 : res.status) || 0;
                        if (this.state === 1) {
                            this.contentEb_.string = '';
                            ViewHelper_1.viewHelper.showAlert('toast.send_mail_succeed');
                        }
                        this.hide();
                        return [2 /*return*/];
                }
            });
        });
    };
    FeedbackPnlCtrl = __decorate([
        ccclass
    ], FeedbackPnlCtrl);
    return FeedbackPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = FeedbackPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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