
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CellDropInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4476aewoDxAaKiyqjwiekwf', 'CellDropInfoPnlCtrl');
// app/script/view/main/CellDropInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var CellDropInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CellDropInfoPnlCtrl, _super);
    function CellDropInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.treasureNode_ = null; // path://root/treasure_n
        _this.otherNode_ = null; // path://root/other_n
        _this.notStaminaNode_ = null; // path://root/not_stamina_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    CellDropInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CellDropInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CellDropInfoPnlCtrl.prototype.onEnter = function (json, landType) {
        var _this = this;
        var _a;
        var player = GameHelper_1.gameHpr.player;
        var needStamina = json.need_stamina || 0;
        var curStamina = player.getStamina();
        if (GameHelper_1.gameHpr.isFreeServer()) {
            this.treasureNode_.Child('count').setLocaleKey('ui.suc_allo_treasure_count_free', (json === null || json === void 0 ? void 0 : json.treasures_count.replace(',', '~')) || '0~0');
        }
        else {
            this.treasureNode_.Child('count').setLocaleKey('ui.suc_allo_treasure_count', "<img src='stamina' /><color=" + (curStamina < needStamina ? '#D7634D' : '#3F332F') + ">" + needStamina + "</c>", (json === null || json === void 0 ? void 0 : json.treasures_count.replace(',', '~')) || '0~0');
        }
        var treasures = [];
        ut.stringToNumbers(json === null || json === void 0 ? void 0 : json.treasures_lv, ',').forEach(function (m, i) { return m > 0 && treasures.push({ lv: i + 1, val: m }); });
        var sum = treasures.reduce(function (val, cur) { return val + cur.val; }, 0) || 1;
        this.treasureNode_.Child('items').Items(treasures, function (it, data) {
            ResHelper_1.resHelper.loadIcon('icon/treasure_' + data.lv + '_0', it.Child('icon'), _this.key);
            it.Child('desc').setLocaleKey('ui.treasure_name_' + data.lv);
            it.Child('val', cc.Label).string = Math.round(data.val / sum * 100) + '%';
        });
        // 其他资源
        if (this.otherNode_.active = !!json.other_rewards_odds || !!json.material_odds) {
            var idp = landType * 100;
            var extraOdds = GameHelper_1.gameHpr.getAncientEffectByPlayer(GameHelper_1.gameHpr.getUid(), Enums_1.CEffect.OTHER_RES_ODDS);
            // 书铁
            var res = this.otherNode_.Child('res');
            if (res.active = !!json.material_odds) {
                var odds = Math.min(100, json.material_odds + extraOdds);
                var rewards = (((_a = assetsMgr.getJsonData('treasure', idp + 11)) === null || _a === void 0 ? void 0 : _a.rewards) || '').split('|');
                var reward = ut.stringToNumbers(rewards[0] || '0,0,0,0', ',');
                res.Child('desc').setLocaleKey('ui.other_res_desc_' + landType, "<color=" + (extraOdds ? '#4AB32E' : '#3F332F') + ">" + odds + "%</c>", (reward === null || reward === void 0 ? void 0 : reward.length) === 4 ? reward[2] + '~' + reward[3] : '0~0');
            }
            // 特殊资源
            var special = this.otherNode_.Child('special');
            if (special.active = !!json.other_rewards_odds) {
                var odds = Math.min(100, json.other_rewards_odds + extraOdds);
                special.Child('desc').setLocaleKey('ui.sres_odds_desc', "<color=" + (extraOdds ? '#4AB32E' : '#3F332F') + ">" + odds + "%</c>", 1);
                var treasure = assetsMgr.getJsonData('treasure', idp + 13);
                var rewards = (treasure.rewards || '').split('|');
                var weights_1 = ut.stringToNumbers(treasure.weight, ',');
                var sumWeight_1 = weights_1.reduce(function (val, cur) { return val + cur; }, 0) || 1;
                special.Child('items').Items(rewards, function (it, str, i) {
                    var _a = __read(ut.stringToNumbers(str, ','), 1), type = _a[0], weight = weights_1[i];
                    it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(type);
                    it.Child('desc').setLocaleKey(Constant_1.CTYPE_NAME[type]);
                    it.Child('val', cc.Label).string = Math.round(weight / sumWeight_1 * 100) + '%';
                });
            }
        }
        this.notStaminaNode_.active = curStamina < needStamina;
    };
    CellDropInfoPnlCtrl.prototype.onRemove = function () {
    };
    CellDropInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    CellDropInfoPnlCtrl = __decorate([
        ccclass
    ], CellDropInfoPnlCtrl);
    return CellDropInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CellDropInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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