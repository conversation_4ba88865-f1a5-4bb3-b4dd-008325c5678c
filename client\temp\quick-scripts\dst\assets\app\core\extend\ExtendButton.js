
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendButton.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ebf5bsVTV9Olos+Zs12UxDX', 'ExtendButton');
// app/core/extend/ExtendButton.ts

/**
 * Button扩展方法
 */
//注意这里是改写了引擎的方法，避免同一个按钮在同一帧会被调用两次
//@ts-ignore
cc.Button.prototype._onTouchEnded = function (event) {
    var _this = this;
    if (this.__pressedFlag || !this.interactable || !this.enabledInHierarchy) {
        return;
    }
    else if (this._pressed) {
        this.__pressedFlag = true;
        cc.Component.EventHandler.emitEvents(this.clickEvents, event);
        this.node.emit('click', this);
        ut.waitNextFrame().then(function () { return _this.isValid && (_this.__pressedFlag = false); });
    }
    this._pressed = false;
    this._updateState();
    event.stopPropagation();
};
cc.Button.prototype.setInteractableAndMF = function (val) {
    this.interactable = val;
    this.Component(cc.MultiFrame).setFrame(val);
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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