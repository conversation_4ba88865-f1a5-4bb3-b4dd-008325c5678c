{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\AgreeFriendApplyPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,qDAAmE;AACnE,6DAAyD;AACzD,6DAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAqD,2CAAc;IAAnE;QAAA,qEAoEC;QAlEG,0BAA0B;QAClB,gBAAU,GAAgB,IAAI,CAAA,CAAC,yBAAyB;QACxD,iBAAW,GAAc,IAAI,CAAA,CAAC,yBAAyB;QAC/D,MAAM;QAEE,UAAI,GAAQ,IAAI,CAAA;QAChB,aAAO,GAAY,KAAK,CAAA;;IA4DpC,CAAC;IA1DU,iDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,0CAAQ,GAArB;;;;;;KACC;IAEM,yCAAO,GAAd,UAAe,IAAS,EAAE,OAAgB;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,yBAAyB,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA;QACzI,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAA;SACrC;IACL,CAAC;IAEM,0CAAQ,GAAf;QACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAEM,yCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,0BAA0B;IAC1B,gDAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QACnD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACZ,OAAM;SACT;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;YACnC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAA;SACnB;QACD,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEnG,oCAAE,GAAhB;;;;;;wBACU,MAAM,GAAG,oBAAO,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;wBAC5B,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,8BAA8B,EAAE,EAAE,GAAG,KAAA,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,EAAA;;wBAAxI,KAAgB,SAAwH,EAAtI,GAAG,SAAA,EAAE,IAAI,UAAA;wBACjB,IAAI,GAAG,EAAE;4BACL,IAAI,GAAG,KAAK,aAAK,CAAC,gBAAgB,IAAI,GAAG,KAAK,aAAK,CAAC,eAAe,IAAI,GAAG,KAAK,aAAK,CAAC,gBAAgB,EAAE;gCAC7F,IAAI,GAAG,MAAM,CAAC,SAAS,EAAE,CAAA;gCAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;gCACvB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;6BAC5B;4BACD,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;yBAC5B;6BAAM;4BACH,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAA;4BAC5C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC,CAAA;4BAC7C,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;yBAChD;wBACD,IAAI,CAAC,IAAI,EAAE,CAAA;wBACX,IAAI,CAAC,OAAO,IAAI,oBAAO,CAAC,qBAAqB,CAAC,4BAAoB,CAAC,IAAI,CAAC,CAAA;;;;;KAC3E;IAnEgB,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAoE3C;IAAD,8BAAC;CApED,AAoEC,CApEoD,EAAE,CAAC,WAAW,GAoElE;kBApEoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ecode } from \"../../common/constant/ECode\";\r\nimport { NoticePermissionType } from \"../../common/constant/Enums\";\r\nimport { gameHpr } from \"../../common/helper/GameHelper\";\r\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\r\n\r\nconst { ccclass } = cc._decorator;\r\n\r\n@ccclass\r\nexport default class AgreeFriendApplyPnlCtrl extends mc.BasePnlCtrl {\r\n\r\n    //@autocode property begin\r\n    private contentRt_: cc.RichText = null // path://root/content_rt\r\n    private noApplyTge_: cc.Toggle = null // path://root/no_apply_t\r\n    //@end\r\n\r\n    private data: any = null\r\n    private isAgree: boolean = false\r\n\r\n    public listenEventMaps() {\r\n        return []\r\n    }\r\n\r\n    public async onCreate() {\r\n    }\r\n\r\n    public onEnter(data: any, isAgree: boolean) {\r\n        this.data = data\r\n        this.isAgree = isAgree\r\n        this.contentRt_.setLocaleKey(isAgree ? 'ui.agree_friend_apply_1' : 'ui.agree_friend_apply_0', ut.nameFormator(data.nickname || '???', 7))\r\n        if (this.noApplyTge_.setActive(!isAgree)) {\r\n            this.noApplyTge_.isChecked = false\r\n        }\r\n    }\r\n\r\n    public onRemove() {\r\n        this.data = null\r\n    }\r\n\r\n    public onClean() {\r\n    }\r\n\r\n    // ----------------------------------------- button listener function -------------------------------------------\r\n    //@autocode button listener\r\n\r\n    // path://root/buttons_nbe\r\n    onClickButtons(event: cc.Event.EventTouch, data: string) {\r\n        if (!this.data) {\r\n            return\r\n        } else if (event.target.name === 'ok') {\r\n            return this.do()\r\n        }\r\n        this.hide()\r\n    }\r\n    //@end\r\n    // ----------------------------------------- event listener function --------------------------------------------\r\n\r\n    // ----------------------------------------- custom function ----------------------------------------------------\r\n\r\n    private async do() {\r\n        const friend = gameHpr.friend, uid = this.data.uid\r\n        const { err, data } = await gameHpr.net.request('lobby/HD_ApplyFriendResponse', { uid, agree: this.isAgree, ban: this.noApplyTge_.isChecked })\r\n        if (err) {\r\n            if (err === ecode.NOT_APPLY_FRIEND || err === ecode.ALREADY_FRIENDS || err === ecode.PLAYER_NOT_EXIST) {\r\n                const list = friend.getApplys()\r\n                list.remove('uid', uid)\r\n                friend.updateApplys(list)\r\n            }\r\n            viewHelper.showAlert(err)\r\n        } else {\r\n            friend.updateFriends(data.friendsList || [])\r\n            friend.updateApplys(data.friendsApplys || [])\r\n            friend.updateBlacklist(data.blacklists || [])\r\n        }\r\n        this.hide()\r\n        this.isAgree && gameHpr.checkNoticePermission(NoticePermissionType.SUBS)\r\n    }\r\n}\r\n"]}