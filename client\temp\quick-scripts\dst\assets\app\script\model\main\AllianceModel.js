
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/AllianceModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '58e3fJqDeVIUpZe2k7dYCgt', 'AllianceModel');
// app/script/model/main/AllianceModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 联盟模块
 */
var AllianceModel = /** @class */ (function (_super) {
    __extends(AllianceModel, _super);
    function AllianceModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.uid = '';
        _this.icon = 0;
        _this.name = '';
        _this.creater = '';
        _this.createTime = 0; //创建时间
        _this.notice = ''; //联盟公告
        _this.members = [];
        _this.applys = [];
        _this.applyDesc = '';
        _this.persLimit = 0; //人数上限
        _this.sumEmbassyLv = 0; //总的大使馆等级
        _this.selectPolicys = {}; //当前可选政策
        _this.mapFlag = {};
        _this.chatChannels = []; //聊天频道
        _this.editNoticeCD = 0;
        _this.getCDTime = 0;
        _this.leadervotes = []; //投票信息
        _this.confirmSurplusTime = 0;
        _this.confirmSurplusGetTime = 0;
        _this.voteTimes = 0; //投票次数
        _this.lastReqLogsTime = 0; //最后一次请求时间
        _this.tempAlliLogs = []; //日志信息列表
        _this.tempBattleRecordMap = {};
        return _this;
    }
    AllianceModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    AllianceModel.prototype.init = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.reset();
                        if (!uid) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliance', { uid: uid })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (!err && data.alliance) {
                            this.fromSvr(data.alliance);
                        }
                        _b.label = 2;
                    case 2:
                        this.net.on('game/OnAgreeJoinAlliance', this.OnAgreeJoinAlliance, this);
                        return [2 /*return*/];
                }
            });
        });
    };
    AllianceModel.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.icon = data.icon || 100101;
        this.name = data.name || '';
        this.creater = data.creater;
        this.createTime = data.createTime || Date.now();
        this.notice = data.notice || '';
        this.applyDesc = data.applyDesc || '';
        this.editNoticeCD = data.editNoticeCD || 0;
        this.confirmSurplusTime = data.confirmSurplusTime || 0;
        this.confirmSurplusGetTime = this.getCDTime = Date.now();
        this.voteTimes = data.voteTimes || 0;
        this.updateMembers(data.members);
        this.updateApplys(data.applys);
        this.updateLeadervotes(data.leadervotes);
        this.persLimit = data.persLimit || 1;
        this.sumEmbassyLv = data.sumEmbassyLv || 1;
        this.selectPolicys = data.selectPolicys || {};
        this.mapFlag = data.mapFlag || {};
        this.chatChannels = data.chatChannels || [];
        this.tempBattleRecordMap = {};
        this.net.on('game/OnPlayerJoinAlliance', this.OnPlayerJoinAlliance, this);
        this.net.on('game/OnUpdateAlliMembers', this.OnUpdateAlliMembers, this);
        this.net.on('game/OnUpdateAlliApplys', this.OnUpdateAlliApplys, this);
        this.net.on('game/OnKickoutAlliance', this.OnKickoutAlliance, this);
        this.net.on('game/OnAllianceChangeNotice', this.OnAllianceChangeNotice, this);
        this.net.on('game/OnAllianceChangeMemberJob', this.OnAllianceChangeMemberJob, this);
        this.net.on('game/OnUpdateAlliMemberEmbassyLv', this.OnUpdateAlliMemberEmbassyLv, this);
        this.net.on('game/OnUpdateAlliMapFlag', this.OnUpdateAlliMapFlag, this);
        this.net.on('game/OnCellEmoji', this.OnCellEmoji, this);
        this.net.on('game/OnAllianceChangeApplyDesc', this.OnAllianceChangeApplyDesc, this);
        this.net.on('game/OnAllianceCreateChatChannel', this.OnAllianceCreateChatChannel, this);
        this.net.on('game/OnAllianceDelChatChannel', this.OnAllianceDelChatChannel, this);
        this.net.on('game/OnAllianceVoteLeader', this.OnAllianceVoteLeader, this);
        this.net.on('game/OnAllianceConfirm', this.OnAllianceConfirm, this);
        // 保存上一次icon
        var icon = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.LAST_ALLI_ICON) || 0;
        if (this.icon !== icon) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.LAST_ALLI_ICON, this.icon);
        }
        // 上报
        EventReportHelper_1.eventReportHelper.reportGlobalEventOne('union_1st');
    };
    AllianceModel.prototype.reset = function () {
        this.uid = '';
        this.icon = 0;
        this.name = '';
        this.creater = '';
        this.createTime = 0;
        this.notice = '';
        this.applyDesc = '';
        this.members.length = 0;
        this.applys.length = 0;
        this.persLimit = 0;
        this.sumEmbassyLv = 0;
        this.selectPolicys = {};
        this.mapFlag = {};
        this.editNoticeCD = 0;
        this.leadervotes = [];
        this.confirmSurplusTime = 0;
        this.tempBattleRecordMap = {};
        this.chatChannels.length = 0;
    };
    AllianceModel.prototype.clean = function () {
        this.reset();
        this.net.off('game/OnPlayerJoinAlliance', this.OnPlayerJoinAlliance, this);
        this.net.off('game/OnUpdateAlliMembers', this.OnUpdateAlliMembers, this);
        this.net.off('game/OnUpdateAlliApplys', this.OnUpdateAlliApplys, this);
        this.net.off('game/OnKickoutAlliance', this.OnKickoutAlliance, this);
        this.net.off('game/OnAllianceChangeNotice', this.OnAllianceChangeNotice, this);
        this.net.off('game/OnAllianceChangeMemberJob', this.OnAllianceChangeMemberJob, this);
        this.net.off('game/OnUpdateAlliMemberEmbassyLv', this.OnUpdateAlliMemberEmbassyLv, this);
        this.net.off('game/OnUpdateAlliMapFlag', this.OnUpdateAlliMapFlag, this);
        this.net.off('game/OnCellEmoji', this.OnCellEmoji, this);
        this.net.off('game/OnAllianceChangeApplyDesc', this.OnAllianceChangeApplyDesc, this);
        this.net.off('game/OnAllianceCreateChatChannel', this.OnAllianceCreateChatChannel, this);
        this.net.off('game/OnAllianceDelChatChannel', this.OnAllianceDelChatChannel, this);
        this.net.off('game/OnAllianceVoteLeader', this.OnAllianceVoteLeader, this);
        this.net.off('game/OnAllianceConfirm', this.OnAllianceConfirm, this);
    };
    AllianceModel.prototype.getUid = function () { return this.uid; };
    AllianceModel.prototype.getIcon = function () { return this.icon; };
    AllianceModel.prototype.getName = function () { return this.name; };
    AllianceModel.prototype.getCreater = function () { return this.creater; };
    AllianceModel.prototype.getNotice = function () { return this.notice; };
    AllianceModel.prototype.getApplys = function () { return this.applys; };
    AllianceModel.prototype.getApplyDesc = function () { return this.applyDesc; };
    AllianceModel.prototype.setApplyDesc = function (val) { this.applyDesc = val; };
    AllianceModel.prototype.getMembers = function () { return this.members; };
    AllianceModel.prototype.getLeadervotes = function () { return this.leadervotes; };
    AllianceModel.prototype.getPersLimit = function () { return this.persLimit; };
    AllianceModel.prototype.getSumEmbassyLv = function () { return this.sumEmbassyLv; };
    AllianceModel.prototype.getSelectPolicysByIndex = function (index) { var _a; return ((_a = this.selectPolicys[index]) === null || _a === void 0 ? void 0 : _a.arr) || []; };
    AllianceModel.prototype.getEditNoticeCd = function () { return Math.max(0, this.editNoticeCD - (Date.now() - this.getCDTime)); };
    AllianceModel.prototype.getChatChannels = function () { return this.chatChannels; };
    AllianceModel.prototype.getVoteTimes = function () { return this.voteTimes; };
    AllianceModel.prototype.getCreateTimeText = function () {
        return ut.dateFormat('yyyy-MM-dd', this.createTime);
    };
    // 是否投票状态
    AllianceModel.prototype.isVoteState = function () {
        return !!this.uid && (this.creater === '' || this.confirmSurplusTime > 0);
    };
    // 获取投票剩余时间
    AllianceModel.prototype.getVoteSurplusTime = function () {
        if (!this.uid || this.creater) {
            return 0;
        }
        return Math.max(0, GameHelper_1.gameHpr.world.getSeason().getSurplusTime() - ut.Time.Day);
    };
    // 获取确认盟主剩余时间
    AllianceModel.prototype.getConfirmCreaterSurplusTime = function () {
        return Math.max(0, this.confirmSurplusTime - (Date.now() - this.confirmSurplusGetTime));
    };
    // 是否自己是创建者
    AllianceModel.prototype.isMeCreater = function () {
        return this.creater === GameHelper_1.gameHpr.getUid();
    };
    // 是否副盟主
    AllianceModel.prototype.isMeCreaterVice = function () {
        return this.getMemberJob() === Enums_1.AllianceJobType.CREATER_VICE;
    };
    // 是否自己是军师
    AllianceModel.prototype.isMeMilitary = function () {
        return this.getMemberJob() === Enums_1.AllianceJobType.MILITARY;
    };
    // 是否可以编辑频道
    AllianceModel.prototype.canEditChannel = function () {
        var job = this.getMemberJob();
        return job === Enums_1.AllianceJobType.CREATER || job === Enums_1.AllianceJobType.MILITARY;
    };
    // 是否可以发送邮件
    AllianceModel.prototype.isCanSendMail = function (uid) {
        if (!uid) {
            return this.isMeCreater() || this.isMeCreaterVice();
        }
        var job = this.getMemberJob(uid);
        return job === Enums_1.AllianceJobType.CREATER || job === Enums_1.AllianceJobType.CREATER_VICE;
    };
    // 是否可以创建联盟子频道
    AllianceModel.prototype.isCanCreateChatChannel = function (uid) {
        if (!uid) {
            return this.isMeCreater() || this.isMeMilitary();
        }
        var job = this.getMemberJob(uid);
        return job === Enums_1.AllianceJobType.CREATER || job === Enums_1.AllianceJobType.MILITARY;
    };
    // 获取自己的职位
    AllianceModel.prototype.getMemberJob = function (uid) {
        var _a, _b;
        uid = uid || GameHelper_1.gameHpr.getUid();
        return (_b = (_a = this.members.find(function (m) { return m.uid === uid; })) === null || _a === void 0 ? void 0 : _a.job) !== null && _b !== void 0 ? _b : Enums_1.AllianceJobType.MEMBER;
    };
    // 是否成员
    AllianceModel.prototype.isMember = function (uid) {
        return this.members.has('uid', uid);
    };
    AllianceModel.prototype.getMember = function (uid) {
        return this.members.find(function (m) { return m.uid === uid; });
    };
    // 根据uid或昵称获取成员
    AllianceModel.prototype.getMemberByUidOrName = function (val) {
        return this.members.find(function (m) { return m.uid === val || m.nickname === val; });
    };
    AllianceModel.prototype.updateMembers = function (members) {
        var now = Date.now();
        this.members = members || [];
        this.members.forEach(function (m) {
            m.getTime = now;
        });
    };
    AllianceModel.prototype.updateApplys = function (applys) {
        var _this = this;
        var now = Date.now();
        this.applys = [];
        applys === null || applys === void 0 ? void 0 : applys.forEach(function (m) {
            m.getTime = now;
            _this.applys.push(m);
        });
        this.applys.sort(function (a, b) { return b.time - a.time; });
    };
    // 获取政策列表
    AllianceModel.prototype.getPolicys = function () {
        return GameHelper_1.gameHpr.world.getAlliPolicysByUid(this.uid);
    };
    // 创建联盟
    AllianceModel.prototype.createAlliance = function (name, icon) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.uid) {
                            return [2 /*return*/, 'ecode.500021'];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_CreateAlliance', { name: name, icon: icon }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && data.alliance) {
                            this.fromSvr(data.alliance);
                            this.emit(EventType_1.default.UPDATE_ALLIANCE);
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 同意加入
    AllianceModel.prototype.agreeJoin = function (uid, isAgree) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.applys.has('uid', uid)) {
                            this.emit(EventType_1.default.UPDATE_ALLIANCE_APPLYS, true);
                            return [2 /*return*/, ECode_1.ecode.YET_CANCEL_APPLY];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_AgreeJoinAlliance', { uid: uid, isAgree: isAgree }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.YET_CANCEL_APPLY) {
                            this.applys.remove('uid', uid);
                            this.updateApplys(this.applys);
                            this.emit(EventType_1.default.UPDATE_ALLIANCE_APPLYS, true);
                        }
                        else if (!err || err === ECode_1.ecode.YET_JOIN_ALLIANCE) {
                            this.OnUpdateAlliApplys(data);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 踢出成员
    AllianceModel.prototype.kickMember = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_KickoutAlliance', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 编辑公告
    AllianceModel.prototype.changeNotice = function (text) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isMeCreater()) {
                            return [2 /*return*/, ECode_1.ecode.NOT_OPERATING_AUTH]; //只有盟主可以编辑
                        }
                        return [4 /*yield*/, this.net.request('game/HD_ChangeAllianceNotice', { notice: text }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.notice = (data === null || data === void 0 ? void 0 : data.notice) || '';
                            this.editNoticeCD = (data === null || data === void 0 ? void 0 : data.editNoticeCD) || 0;
                            this.getCDTime = Date.now();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 改变职位
    AllianceModel.prototype.changeJob = function (uid, job) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isMeCreater()) {
                            return [2 /*return*/, ECode_1.ecode.NOT_OPERATING_AUTH]; //只有盟主可以编辑
                        }
                        return [4 /*yield*/, this.net.request('game/HD_ChangeAlliMemberJob', { uid: uid, job: job }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && data) {
                            this.updateMemberJob(uid, data.job);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取战斗记录
    AllianceModel.prototype.getBattleRecordList = function (date) {
        return __awaiter(this, void 0, void 0, function () {
            var list, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        list = this.tempBattleRecordMap[date];
                        if (list) {
                            return [2 /*return*/, list];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliBattleRecord', { date: date }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        list = this.tempBattleRecordMap[date] = (data === null || data === void 0 ? void 0 : data.records) || [];
                        return [2 /*return*/, list];
                }
            });
        });
    };
    // 获取成员的总战绩
    AllianceModel.prototype.getMemberBattleRecord = function (member) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!(!member.battleRecordData || this.checkToDay(member.reqBattleRecordTime))) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliMemberBattleRecord', { uid: member.uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        member.reqBattleRecordTime = Date.now();
                        member.battleRecordData = data ? data.record : null;
                        _b.label = 2;
                    case 2: return [2 /*return*/, member.battleRecordData];
                }
            });
        });
    };
    AllianceModel.prototype.checkToDay = function (time) {
        var date = new Date(time), now = new Date();
        if (date.getHours() < 4 && now.getHours() > 4) {
            return true;
        }
        return now.getTime() - ut.dateZeroTime(time) >= ut.Time.Day + ut.Time.Hour * 4;
    };
    // 获取日志
    AllianceModel.prototype.getAlliLogs = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqLogsTime > 0 && Date.now() - this.lastReqLogsTime <= 10000) {
                            return [2 /*return*/, this.tempAlliLogs];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliLogs', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqLogsTime = Date.now();
                        this.tempAlliLogs = ((data === null || data === void 0 ? void 0 : data.list) || []).map(function (m) {
                            if (m.type === 7) {
                                m.params[1] = 'policyText.name_' + m.params[1];
                            }
                            else if (m.type === 8) {
                                m.params[1] = 'ui.alliance_job_' + m.params[1];
                                m.params[2] = 'ui.alliance_job_' + m.params[2];
                            }
                            return m;
                        });
                        return [2 /*return*/, this.tempAlliLogs];
                }
            });
        });
    };
    // 刷新投票信息
    AllianceModel.prototype.updateLeadervotes = function (leadervotes) {
        this.leadervotes = leadervotes || [];
    };
    // 投票
    AllianceModel.prototype.leaderVote = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_VoteAlliLeader', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && data) {
                            this.voteTimes = data.voteTimes;
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 创建联盟信息
    AllianceModel.prototype.createAllianceInfo = function (name, icon) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_AlliLeaderConfirm', { name: name, icon: icon }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 加入联盟
    AllianceModel.prototype.OnAgreeJoinAlliance = function (notify) {
        this.fromSvr(notify === null || notify === void 0 ? void 0 : notify.data);
        this.emit(EventType_1.default.UPDATE_ALLIANCE);
        this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
    };
    // 有成员加入
    AllianceModel.prototype.OnPlayerJoinAlliance = function (data) {
        this.OnUpdateAlliMembers(data);
    };
    // 更新成员信息
    AllianceModel.prototype.OnUpdateAlliMembers = function (data) {
        this.updateMembers(data.members);
        this.emit(EventType_1.default.UPDATE_ALLIANCE_MEMBERS);
    };
    // 更新申请列表
    AllianceModel.prototype.OnUpdateAlliApplys = function (data) {
        var applys = (data === null || data === void 0 ? void 0 : data.applys) || [];
        if (this.applys.length < applys.length) {
            GameHelper_1.gameHpr.addMessage({ key: 'ui.message_106', tag: 'alli_apply' });
        }
        this.updateApplys(applys);
        this.emit(EventType_1.default.UPDATE_ALLIANCE_APPLYS);
    };
    // 被踢出联盟
    AllianceModel.prototype.OnKickoutAlliance = function () {
        this.clean();
        this.emit(EventType_1.default.UPDATE_ALLIANCE);
        this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
        ViewHelper_1.viewHelper.showMessageBox('ui.kickout_alli_tip', { okText: 'ui.button_gotit' });
    };
    // 公告变更
    AllianceModel.prototype.OnAllianceChangeNotice = function (data) {
        if (this.isMeCreater()) {
            return;
        }
        this.notice = (data === null || data === void 0 ? void 0 : data.notice) || '';
        this.emit(EventType_1.default.UPDATE_ALLIANCE_NOTICE);
        GameHelper_1.gameHpr.addMessage({ key: 'ui.message_107', tag: 'alli_notice_change' });
    };
    // 成员职位变更
    AllianceModel.prototype.OnAllianceChangeMemberJob = function (data) {
        if (!this.isMeCreater()) {
            this.updateMemberJob(data.uid, data.job);
        }
    };
    // 更新成员大使馆等级
    AllianceModel.prototype.OnUpdateAlliMemberEmbassyLv = function (data) {
        var _a;
        this.persLimit = data.persLimit || 1;
        this.sumEmbassyLv = data.sumEmbassyLv || 1;
        this.selectPolicys = (_a = data.selectPolicys) !== null && _a !== void 0 ? _a : {};
        var member = this.members.find(function (m) { return m.uid === data.uid; });
        if (member) {
            member.embassyLv = data.embassyLv;
        }
        this.emit(EventType_1.default.UPDATE_ALLIANCE_MEMBER_EMBASSY_LV, member);
    };
    // 更新地图标记
    AllianceModel.prototype.OnUpdateAlliMapFlag = function (data) {
        this.mapFlag = (data === null || data === void 0 ? void 0 : data.mapFlag) || {};
        this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
    };
    // 添加地图表情
    AllianceModel.prototype.OnCellEmoji = function (data) {
        GameHelper_1.gameHpr.ground.addCellEmoji(data === null || data === void 0 ? void 0 : data.info);
    };
    // 更新申请说明
    AllianceModel.prototype.OnAllianceChangeApplyDesc = function (data) {
        var _a;
        this.applyDesc = (_a = data === null || data === void 0 ? void 0 : data.applyDesc) !== null && _a !== void 0 ? _a : this.applyDesc;
    };
    // 更新创建频道
    AllianceModel.prototype.OnAllianceCreateChatChannel = function (data) {
        this.addChatChannel(data);
    };
    // 更新删除频道
    AllianceModel.prototype.OnAllianceDelChatChannel = function (data) {
        if (this.chatChannels.remove('uid', data.uid)) {
            this.emit(EventType_1.default.UPDATE_ALLI_CHAT_CHANNEL);
        }
    };
    // 更新投票信息
    AllianceModel.prototype.OnAllianceVoteLeader = function (data) {
        var _a, _b;
        this.updateLeadervotes(data.info);
        this.creater = (_a = data.creater) !== null && _a !== void 0 ? _a : '';
        this.confirmSurplusTime = (_b = data.confirmSurplusTime) !== null && _b !== void 0 ? _b : 0;
        this.confirmSurplusGetTime = Date.now();
        this.emit(EventType_1.default.UPDATE_ALLI_LEADER_VOTE_INFO);
    };
    // 盟主确认信息
    AllianceModel.prototype.OnAllianceConfirm = function (data) {
        this.name = data.name;
        this.icon = data.icon;
        this.confirmSurplusTime = 0;
        this.leadervotes = [];
        this.emit(EventType_1.default.UPDATE_ALLIANCE);
        this.emit(mc.Event.HIDE_PNL, 'build/CreateAlliance');
    };
    // ----------------------------------------- other function --------------------------------------------
    // 更新成员职位
    AllianceModel.prototype.updateMemberJob = function (uid, job) {
        var member = this.members.find(function (m) { return m.uid === uid; });
        if (member) {
            member.job = job;
            this.emit(EventType_1.default.UPDATE_ALLIANCE_MEMBER_JOB, member);
            // 如果是自己的职位变化 刷新一下频道
            if (uid === GameHelper_1.gameHpr.getUid()) {
                this.emit(EventType_1.default.UPDATE_ALLI_CHAT_CHANNEL);
            }
        }
    };
    // 退出
    AllianceModel.prototype.exit = function () {
        this.clean();
        this.emit(EventType_1.default.UPDATE_ALLIANCE);
        this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
    };
    AllianceModel.prototype.getMapFlag = function () { return this.mapFlag; };
    AllianceModel.prototype.getMapFlagInfo = function (index) { return this.mapFlag[index]; };
    // 添加标记
    AllianceModel.prototype.addMapFlag = function (index, flag, desc) {
        var mark = this.mapFlag[index];
        if (!mark) {
            this.mapFlag[index] = { flag: flag, desc: desc };
        }
        else {
            mark.flag = flag;
            mark.desc = desc;
        }
        this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
    };
    // 删除标记
    AllianceModel.prototype.removeMapFlag = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.mapFlag[index]) {
                            return [2 /*return*/, ''];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_DelAlliMapFlag', { index: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            delete this.mapFlag[index];
                            this.emit(EventType_1.default.UPDATE_ALLI_MAP_FLAG);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取日期列表
    AllianceModel.prototype.getCanSelectBattleDateList = function () {
        if (!this.uid || !this.creater) {
            return [];
        }
        var list = [], nowTime = GameHelper_1.gameHpr.getServerNowTime();
        var curTime = ut.dateZeroTime(nowTime) - ut.Time.Day;
        if (new Date(nowTime).getHours() < 4) {
            curTime -= ut.Time.Day; //4点前 还不能获取前一天的 所以还要减1天
        }
        list.push(ut.dateFormat('yyyy-MM-dd', curTime));
        if (curTime < this.createTime) {
            return list; //如果才创建的 那么就直接显示前一天
        }
        for (var i = 0; i < 10; i++) {
            curTime -= ut.Time.Day;
            list.push(ut.dateFormat('yyyy-MM-dd', curTime));
            if (curTime < this.createTime) {
                break;
            }
        }
        return list;
    };
    // 添加频道
    AllianceModel.prototype.addChatChannel = function (data) {
        var isKick = false;
        var info = data.info, myselfUid = GameHelper_1.gameHpr.user.getUid();
        var channel = this.chatChannels.find(function (m) { return m.uid === info.uid; });
        if (channel) {
            channel.name = info.name;
            channel.color = info.color;
            channel.memberUids = info.memberUids;
            channel.memberFilter = info.memberFilter;
            if (!info.memberUids.has(myselfUid)) { // 如果玩家不在这个频道内就要删除
                if (this.chatChannels.remove('uid', info.uid)) {
                    isKick = true;
                    this.emit(EventType_1.default.UPDATE_ALLI_CHAT_CHANNEL);
                }
            }
        }
        else if (info.memberUids.has(myselfUid)) { // 成员里面有自己才会添加
            this.chatChannels.push(info);
            this.emit(EventType_1.default.UPDATE_ALLI_CHAT_CHANNEL);
        }
        this.emit(EventType_1.default.UPDATE_ALLI_CHANEEL_MEMEBERS, info, isKick);
    };
    // 删除频道
    AllianceModel.prototype.removeChatChannel = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_DelAlliChatChannel', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.OnAllianceDelChatChannel({ uid: uid });
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取频道成员信息
    AllianceModel.prototype.getChannelMemberInfos = function (uid) {
        var channel = this.chatChannels.find(function (m) { return m.uid === uid; });
        if (channel) {
            if (!channel.memberFilter) {
                return this.members.slice();
            }
            return this.members.filter(function (m) { return channel.memberUids.has(m.uid); });
        }
        return this.members.slice();
    };
    AllianceModel = __decorate([
        mc.addmodel('alliance')
    ], AllianceModel);
    return AllianceModel;
}(mc.BaseModel));
exports.default = AllianceModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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