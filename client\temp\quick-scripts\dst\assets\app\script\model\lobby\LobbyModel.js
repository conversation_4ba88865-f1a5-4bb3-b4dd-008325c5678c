
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/lobby/LobbyModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2cf28yuVApGqZ5elqPjr/EN', 'LobbyModel');
// app/script/model/lobby/LobbyModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var version_1 = require("../../../../scene/version");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var LoadProgressHelper_1 = require("../../common/helper/LoadProgressHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BaseUserInfo_1 = require("../common/BaseUserInfo");
var SnailIsleModel_1 = require("../snailisle/SnailIsleModel");
/**
 * 大厅模块
 */
var LobbyModel = /** @class */ (function (_super) {
    __extends(LobbyModel, _super);
    function LobbyModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.user = null;
        _this.team = null;
        _this.isRuning = false;
        _this.userCacheMap = new Map(); //临时的用户缓存信息
        _this.pawnCostMap = {}; //士兵招募费用
        _this.curRoomType = 2; //当前选择的对局类型 1.新手 2.排位 0.自由
        _this.canSelectRooms = [2, 0];
        _this.roomStateInfos = []; //服务器状态信息
        _this.getRoomStateInfoTime = -1;
        _this.roomStateCheckInterval = 0;
        _this.allotPlaySid = 0; //当前分配的新手区sid
        _this.cancelApplyTime = 0; //取消报名时间 用于计算下次报名时间的等待时间
        _this.chatMap = {}; //所有聊天map
        _this.curLookChannel = ''; //当前正在看的频道
        _this.reqChatInfo = false; //请求中
        _this.lastSendChatTime = 0; //上次发送时间
        _this.tolerateCount = 0;
        _this.restStartTime = 0; //休息时间
        _this.bannedStartTime = 0; //禁言按钮禁止时间
        _this.bannedStartMaxTime = 0; //禁言按钮禁止最大时间
        _this.loadGamePercent = 0;
        _this.snailIsle = null; //蜗牛岛
        _this.getLikeJwmCountInterval = 0; //获取九万亩点赞数间隔
        _this.sumLikeJwmCount = -1;
        return _this;
    }
    LobbyModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
        this.team = this.getModel('team');
    };
    LobbyModel.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.chatMap = {};
                        this.curRoomType = -1;
                        this.loadGamePercent = 0;
                        this.getRoomStateInfoTime = -1;
                        this.sumLikeJwmCount = -1;
                        this.initLikeJwmCount();
                        return [4 /*yield*/, this.updateRoomStateInfo(false)];
                    case 1:
                        _a.sent();
                        this.checkInitRoomType();
                        return [4 /*yield*/, Promise.all([this.team.init(), this.initPawnCost()])];
                    case 2:
                        _a.sent();
                        this.checkCloseRoomReUpdate();
                        this.net.off('chat/OnLobbyChat', this.OnChat, this); //大厅聊天
                        this.net.on('chat/OnLobbyChat', this.OnChat, this); //大厅聊天
                        this.net.on('lobby/OnUserGameOver', this.OnUserGameOver, this); //游戏结算
                        this.isRuning = true;
                        if (!this.snailIsle) {
                            this.snailIsle = new SnailIsleModel_1.default().create();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyModel.prototype.initBaseInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.team.init()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyModel.prototype.active = function () {
        this.team.active();
    };
    LobbyModel.prototype.clean = function () {
        this.isRuning = false;
        // this.net.off('chat/OnLobbyChat', this.OnChat, this) //大厅聊天
        this.net.off('lobby/OnUserGameOver', this.OnUserGameOver, this); //游戏结算
        this.team.clean();
    };
    LobbyModel.prototype.updateRoomStateInfo = function (isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetRoomStateInfos')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        cc.log('init lobby', data);
                        if (data === null || data === void 0 ? void 0 : data.canPlayRoomTypes.length) {
                            this.canSelectRooms = data.canPlayRoomTypes;
                        }
                        else {
                            this.canSelectRooms = [2, 0];
                        }
                        this.user.setPlaySid(data === null || data === void 0 ? void 0 : data.playSid);
                        this.allotPlaySid = (data === null || data === void 0 ? void 0 : data.allotPlaySid) || 0; //分配的新手区sid
                        this.roomStateInfos = (data === null || data === void 0 ? void 0 : data.states) || [];
                        this.getRoomStateInfoTime = Date.now();
                        isEmit && this.emit(EventType_1.default.UPDATE_TEAM_MODE, true);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化士兵费用系数
    LobbyModel.prototype.initPawnCost = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetPawnCost')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.pawnCostMap = (data === null || data === void 0 ? void 0 : data.costMap) || {};
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取士兵基础费用
    LobbyModel.prototype.getPawnBaseCost = function (id) {
        return this.pawnCostMap[id] || 0;
    };
    LobbyModel.prototype.getCanSelectRooms = function () { return this.canSelectRooms; };
    LobbyModel.prototype.getCurRoomType = function () { return this.curRoomType; };
    LobbyModel.prototype.setCurRoomType = function (val) {
        this.curRoomType = val !== null && val !== void 0 ? val : this.canSelectRooms[0];
    };
    LobbyModel.prototype.getUser = function (uid) {
        var user = this.userCacheMap.get(uid);
        if (user) {
            return user;
        }
        else if (this.userCacheMap.size > 100) {
            this.userCacheMap.delete(this.userCacheMap.keys().next().value);
        }
        user = new BaseUserInfo_1.default().init({ uid: uid });
        this.userCacheMap.set(uid, user);
        return user;
    };
    LobbyModel.prototype.checkInitRoomType = function () {
        var playSid = this.user.getPlaySid();
        if (playSid) { //当前正在玩
            var type = GameHelper_1.gameHpr.getServerType(playSid);
            if (type !== this.curRoomType) {
                this.curRoomType = type;
            }
        }
        if (!this.canSelectRooms.has(this.curRoomType)) {
            if (this.user.getPassNewbieIndex() > 0) {
                this.curRoomType = this.canSelectRooms.find(function (m) { return m === 2; }) || this.canSelectRooms[0];
            }
            else {
                this.curRoomType = this.canSelectRooms[0];
            }
        }
        return this.curRoomType;
    };
    // 获取服务器状态信息
    LobbyModel.prototype.getRoomStateInfoByType = function (type) {
        var data = { state: Enums_1.RoomStateType.CLOSE, time: 0 };
        var info = this.roomStateInfos.find(function (m) { return m.roomType === type; });
        if (!info) {
            return data;
        }
        else if (info.state === Enums_1.RoomStateType.NONE) {
            data.time = info.time;
        }
        else if (info.state === Enums_1.RoomStateType.IN_MATCH) { //即将开启
            data.time = Math.max(0, info.time - (Date.now() - this.getRoomStateInfoTime));
        }
        else if (info.state === Enums_1.RoomStateType.IN_GAME) { //开启中
            data.time = info.time + (Date.now() - this.getRoomStateInfoTime);
        }
        data.state = info.state;
        return data;
    };
    // 获取下次报名的等待时间
    LobbyModel.prototype.setCancelApplyTime = function () {
        this.cancelApplyTime = Date.now();
    };
    LobbyModel.prototype.getNextApplyCDTime = function () {
        return this.cancelApplyTime > 0 ? Math.max(0, Constant_1.NEXT_APPLY_CD - (Date.now() - this.cancelApplyTime)) : 0;
    };
    // 检测关闭的服务器 定时获取状态
    LobbyModel.prototype.checkCloseRoomReUpdate = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var sid, roomType;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.curRoomType === -1) {
                            return [2 /*return*/];
                        }
                        sid = this.user.getPlaySid();
                        roomType = GameHelper_1.gameHpr.getServerType(sid);
                        if (!sid || ((_a = this.roomStateInfos.find(function (m) { return m.roomType === roomType; })) === null || _a === void 0 ? void 0 : _a.state) !== Enums_1.RoomStateType.CLOSE) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, ut.wait(5)];
                    case 1:
                        _b.sent();
                        return [4 /*yield*/, this.updateRoomStateInfo()];
                    case 2:
                        _b.sent();
                        this.checkCloseRoomReUpdate();
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyModel.prototype.update = function (dt) {
        var _a;
        if (!this.isRuning) {
            return;
        }
        this.updateRoomState(dt);
        this.updateCheckLikeJwmCount(dt);
        (_a = this.snailIsle) === null || _a === void 0 ? void 0 : _a.update(dt);
    };
    LobbyModel.prototype.updateRoomState = function (dt) {
        if (this.getRoomStateInfoTime === -1) {
            return;
        }
        else if (this.roomStateCheckInterval > 0) {
            this.roomStateCheckInterval -= dt;
            return;
        }
        var now = Date.now();
        var serverNow = GameHelper_1.gameHpr.getServerNowTime(), getTime = this.getRoomStateInfoTime;
        var ok = this.roomStateInfos.some(function (m) {
            if (m.state === Enums_1.RoomStateType.NONE) {
                return serverNow >= m.time;
            }
            else if (m.state === Enums_1.RoomStateType.IN_MATCH) { //即将开启
                return now - getTime >= m.time;
            }
            else if (m.state === Enums_1.RoomStateType.CLOSE) { //维护中
                return true;
            }
            return false;
        });
        if (ok) {
            this.roomStateCheckInterval = 5;
            this.getRoomStateInfoTime = -1;
            this.updateRoomStateInfo();
        }
    };
    // 报名
    LobbyModel.prototype.apply = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.team.isInGame()) {
                            return [2 /*return*/, ECode_1.ecode.IN_GAME];
                        }
                        else if (this.team.isInApply()) {
                            return [2 /*return*/, ECode_1.ecode.IN_APPLY];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_ApplyNewServer', { roomType: this.curRoomType }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.cancelApplyTime = 0;
                            this.team.cleanReceiveInvite();
                            this.team.setRoomType(this.curRoomType); //兼容下
                            if (this.team.getCancelApplySurplusTime() === -1) {
                                this.team.setCancelApplySurplusTime(70000);
                                this.emit(EventType_1.default.UPDATE_TEAM_LIST);
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 取消报名
    LobbyModel.prototype.cancelApply = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isExit, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.team.isInGame()) {
                            return [2 /*return*/, ECode_1.ecode.IN_GAME];
                        }
                        else if (!this.team.isInApply()) {
                            return [2 /*return*/, ECode_1.ecode.SERVER_NOT_APPLY];
                        }
                        isExit = this.team.hasTeam() && !this.team.isCaptain();
                        return [4 /*yield*/, this.net.request('lobby/HD_CancelApplyNewServer', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && isExit) {
                            this.team.cleanTeam();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    //#region -----------------------------------------------------------------进入游戏--------------------------------------------------------------------
    LobbyModel.prototype.getLoadGamePercent = function () { return this.loadGamePercent; };
    LobbyModel.prototype.playEnterSound = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ut.wait(0.2)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, audioMgr.playSFX('common/sound_ui_013', { volume: 0.8, tag: 'jigu' })];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, ut.wait(1.4)];
                    case 3:
                        _a.sent();
                        audioMgr.fadeOutSFX(0.2, 'common/sound_ui_013', 'jigu');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 进入游戏
    LobbyModel.prototype.enterGame = function () {
        return __awaiter(this, void 0, void 0, function () {
            var sid, res, pos, _a, err, data, rst;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        sid = this.team.getPlaySid() || this.allotPlaySid;
                        if (!sid) {
                            return [2 /*return*/, ECode_1.ecode.ROOM_NOT_EXIST];
                        }
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, this.net.request('lobby/HD_SelectGameServer', { sid: sid })];
                    case 1:
                        res = _b.sent();
                        if (res.err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            if (res.err === ECode_1.ecode.ROOM_CLOSE || res.err === ECode_1.ecode.YET_GIVE_GAME) {
                                this.user.setSid(0);
                                this.user.setPlaySid(0);
                                this.team.setPlaySid(0);
                                this.emit(EventType_1.default.UPDATE_TEAM_MODE, true);
                            }
                            return [2 /*return*/, res.err];
                        }
                        pos = Number(ut.getBrowserParamByKey('pos')) || 0;
                        return [4 /*yield*/, this.net.request('game/HD_Entry', {
                                sid: sid,
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                version: version_1.default.VERSION,
                                isReconnect: false,
                                lang: mc.lang,
                                os: TaHelper_1.taHelper.getOs(),
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                                pos: pos > 0 ? -pos : 0,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        if (!err) {
                            rst = data.rst;
                            this.user.setSid(sid);
                            this.user.setPlaySid(sid);
                            this.team.setPlaySid(sid);
                            GameHelper_1.gameHpr.initServerConfig(rst);
                            // 加载游戏
                            this.loadGame(sid, rst);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 进入观战
    LobbyModel.prototype.enterSpectators = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var res, sid, _a, err, data, rst;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, this.net.request('lobby/HD_SpectateFriend', { uid: uid })];
                    case 1:
                        res = _b.sent();
                        if (res.err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            if (res.err === ECode_1.ecode.ROOM_OVER) {
                                return [2 /*return*/, 'toast.game_over_no_spectator'];
                            }
                            else if (res.err === ECode_1.ecode.YET_GIVE_GAME) {
                                return [2 /*return*/, 'toast.give_game_no_spectator'];
                            }
                            return [2 /*return*/, res.err];
                        }
                        sid = res.data.sid;
                        return [4 /*yield*/, this.net.request('game/HD_Spectate', {
                                sid: sid,
                                version: version_1.default.VERSION,
                                isReconnect: false,
                                friendUid: uid,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        if (err === ECode_1.ecode.ROOM_CLOSE) {
                            return [2 /*return*/, 'login.server_maintaining_desc'];
                        }
                        else if (err === ECode_1.ecode.ROOM_OVER) {
                            return [2 /*return*/, 'toast.game_over_no_spectator'];
                        }
                        else if (err === ECode_1.ecode.YET_GIVE_GAME) {
                            return [2 /*return*/, 'toast.give_game_no_spectator'];
                        }
                        else if (!err) {
                            rst = data.rst;
                            GameHelper_1.gameHpr.initServerConfig(rst);
                            // 加载游戏
                            this.loadGame(sid, rst);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    LobbyModel.prototype.loadGame = function (sid, data) {
        var _this = this;
        // gameHpr.stopBgBgm()
        mc.lockTouch('load_game');
        this.emit(mc.Event.HIDE_ALL_PNL);
        this.emit(EventType_1.default.SHOW_GAME_LOADING, sid);
        // this.playEnterSound()
        this.loadGamePercent = 0;
        var loadProgress = LoadProgressHelper_1.loadProgressHelper.create();
        loadProgress.add(1, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, GameHelper_1.gameHpr.player.init(data.player)];
        }); }); });
        loadProgress.add(5, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, GameHelper_1.gameHpr.world.init(sid, data.world, cb)];
        }); }); }); //57
        loadProgress.spawn(6, [
            function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UI', function (done, total) { return cb(done / total); })];
            }); }); },
            function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UIMenuChild', function (done, total) { return cb(done / total); })];
            }); }); },
            function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/Top', function (done, total) { return cb(done / total); })];
            }); }); },
            function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind('main', cb)];
            }); }); },
        ]);
        loadProgress.run(function (p) { return _this.loadGamePercent = p; });
    };
    // 游戏结算
    LobbyModel.prototype.OnUserGameOver = function () {
        this.user.setSid(0);
        this.user.setPlaySid(0);
        this.team.setPlaySid(0);
        this.emit(EventType_1.default.UPDATE_TEAM_MODE, true);
    };
    //#region -------------------------------------------------------------------聊天--------------------------------------------------------------------
    LobbyModel.prototype.setCurLookChannel = function (val) { this.curLookChannel = val; };
    LobbyModel.prototype.getCurLookChannel = function () { return this.curLookChannel; };
    // 获取聊天信息
    LobbyModel.prototype.getChatsByChannel = function (channel) {
        return __awaiter(this, void 0, void 0, function () {
            var info, now, _a, err, data, list, blacklistMap;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqChatInfo) {
                            return [2 /*return*/];
                        }
                        info = this.chatMap[channel], now = Date.now();
                        if (info && info.lastReqTime > 0 && now - info.lastReqTime < ut.Time.Hour) {
                            return [2 /*return*/, info.list];
                        }
                        else if (!info) {
                            info = this.chatMap[channel] = { list: [], lastReqTime: now };
                        }
                        this.reqChatInfo = true;
                        return [4 /*yield*/, this.net.request('chat/HD_GetChats', { channel: channel })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.reqChatInfo = false;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [];
                        blacklistMap = {};
                        GameHelper_1.gameHpr.friend.getBlacklists().forEach(function (m) { return blacklistMap[m.uid] = m.time; });
                        list.forEach(function (m) {
                            var blacklistTime = blacklistMap[m.sender] || 0;
                            if (blacklistTime && m.time >= blacklistTime) {
                                return; //黑名单的消息不看
                            }
                            m.portrayal = GameHelper_1.gameHpr.checkChatPortrayal(m.portrayalInfo);
                            info.list.unshift(m);
                        });
                        info.lastReqTime = now;
                        return [2 /*return*/, info.list];
                }
            });
        });
    };
    LobbyModel.prototype.getChannelByType = function (type) {
        var channel = '0';
        if (type === 1) { //队伍
            var teamUid = this.team.getUid();
            if (teamUid) {
                channel = '1_' + teamUid;
            }
            else {
                channel = '';
            }
        }
        else if (type === 0 || type === 2) { // 大厅频道（0：主频道，2：当前语言频道）
            var uid = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL) || '';
            if (uid && uid !== '0') {
                channel = '2_' + uid;
            }
        }
        return channel;
    };
    // 收到服务器的聊天信息
    LobbyModel.prototype.OnChat = function (notify) {
        var _a;
        var data = notify === null || notify === void 0 ? void 0 : notify.data;
        var list = (_a = this.chatMap[data.channel]) === null || _a === void 0 ? void 0 : _a.list;
        if (!list) {
            return;
        }
        data.type = Number(data.channel.split('_')[0]) || 0;
        var uid = this.user.getUid();
        do {
            if (data.sender === uid) {
                var chat = list.find(function (m) { return m.uid === data.uid; });
                if (chat) {
                    chat.content = data.content;
                    chat.wait = false;
                    chat.bannedSurplusTime = data.bannedSurplusTime;
                    chat.portrayal = GameHelper_1.gameHpr.checkChatPortrayal(data.portrayalInfo);
                    data = chat;
                    break;
                }
            }
            else if (GameHelper_1.gameHpr.friend.isInBlacklist(data.sender)) {
                return; //被加入黑名单直接返回
            }
            else {
                data.portrayal = GameHelper_1.gameHpr.checkChatPortrayal(data.portrayalInfo);
            }
            this.addChat(list, data);
        } while (false);
        GameHelper_1.gameHpr.updateChatAllTime(list);
        this.emit(EventType_1.default.ADD_LOBBY_CHAT, data);
    };
    // 发送聊天信息
    LobbyModel.prototype.sendChat = function (type, content, param) {
        var _this = this;
        var _a, _b, _c, _d;
        if (param === void 0) { param = {}; }
        if (!ut.isEmptyObject(param.select)) { // 更改选择的频道
            var channel_1 = param.select[type];
            if (type === 0) { // 大厅/世界频道
                GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, channel_1);
            }
        }
        var channel = this.getChannelByType(type);
        var isMilitaryChannelChat = type === 1; //队伍发言没有限制
        if (this.checkRestSurplusTime(isMilitaryChannelChat) > 0) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BAN_OPT);
            return 1;
        }
        else if (this.checkOftenTime(isMilitaryChannelChat)) {
            ViewHelper_1.viewHelper.showAlert('toast.send_chat_tolerate');
            return 1;
        }
        var now = this.lastSendChatTime = Date.now();
        var list = ((_a = this.chatMap[channel]) === null || _a === void 0 ? void 0 : _a.list) || [];
        var uid = ut.UID();
        // 先发送到本地
        this.addChat(list, {
            uid: uid,
            type: type,
            channel: channel,
            sender: this.user.getUid(),
            senderNickname: this.user.getNickname(),
            senderHeadicon: this.user.getHeadIcon(),
            content: content,
            emoji: param.emoji || 0,
            portrayal: GameHelper_1.gameHpr.checkChatPortrayal((_b = param.portrayal) === null || _b === void 0 ? void 0 : _b.strip()),
            replyInfo: GameHelper_1.gameHpr.checkChatHasReplyInfo(param.replyChatInfo),
            time: now,
            wait: true,
            bannedSurplusTime: 0,
        });
        GameHelper_1.gameHpr.updateChatAllTime(list);
        // 发送到服务器
        this.net.request('lobby/HD_LobbyChat', {
            uid: uid, channel: channel, content: content,
            emoji: param.emoji,
            portrayalId: ((_c = param.portrayal) === null || _c === void 0 ? void 0 : _c.id) || 0,
            replyUid: (_d = param.replyChatInfo) === null || _d === void 0 ? void 0 : _d.uid
        }).then(function (res) {
            var _a;
            if (res === null || res === void 0 ? void 0 : res.err) {
            }
            else if (!((_a = res === null || res === void 0 ? void 0 : res.data) === null || _a === void 0 ? void 0 : _a.bannedSurplusTime)) {
                _this.bannedStartTime = _this.bannedStartMaxTime = 0;
                storageMgr.remove('chat_banned_time');
            }
            else if (_this.bannedStartTime === 0) {
                _this.bannedStartTime = Date.now();
                _this.bannedStartMaxTime = Math.min(Constant_1.CHAT_BANNED_REST_MAX_TIME, res.data.bannedSurplusTime);
                storageMgr.saveJson('chat_banned_time', { startTime: _this.bannedStartTime, maxTime: _this.bannedStartMaxTime });
                _this.emit(EventType_1.default.UPDATE_SEND_CHAT_BUTTON);
            }
        });
        return 0;
    };
    LobbyModel.prototype.addChat = function (list, data) {
        data.user = new BaseUserInfo_1.default().init({ uid: data.sender, senderNickname: data.senderNickname, senderHeadicon: data.senderHeadicon });
        list.unshift(data);
        if (list.length > Constant_1.CHAT_MAX_COUNT) {
            list.pop();
        }
        return data;
    };
    // 获取聊天信息
    LobbyModel.prototype.getChatInfoByUID = function (uid) {
        for (var k in this.chatMap) {
            var list = this.chatMap[k].list;
            var chat = list.find(function (m) { return m.uid === uid; });
            if (chat) {
                return chat;
            }
        }
        return null;
    };
    // 检测是否发送得太频繁了
    LobbyModel.prototype.checkOftenTime = function (isMilitaryChannelChat) {
        if (isMilitaryChannelChat) {
            return false;
        }
        var now = Date.now();
        if (now - this.restStartTime < Constant_1.CHAT_REST_MAX_TIME) {
            return true;
        }
        else if (now - this.lastSendChatTime < Constant_1.CHAT_SEND_INTERVAL) {
            this.tolerateCount += 1;
        }
        else {
            this.tolerateCount = 0;
        }
        // cc.log(now - this.lastSendChatTime, this.tolerateCount)
        if (this.tolerateCount > Constant_1.CHAT_TOLERATE_MAX_COUNT) {
            this.restStartTime = now;
            return true;
        }
        this.restStartTime = 0;
        return false;
    };
    // 检测休息剩余时间
    LobbyModel.prototype.checkRestSurplusTime = function (isMilitaryChannelChat) {
        if (this.bannedStartTime > 0) {
            var t = Date.now() - this.bannedStartTime;
            if (t < this.bannedStartMaxTime) {
                return this.bannedStartMaxTime - t;
            }
            this.bannedStartTime = this.bannedStartMaxTime = 0;
            storageMgr.remove('chat_banned_time');
        }
        if (isMilitaryChannelChat || this.restStartTime <= 0) {
            return 0;
        }
        var time = Date.now() - this.restStartTime;
        if (time >= Constant_1.CHAT_REST_MAX_TIME) {
            this.restStartTime = 0;
            return 0;
        }
        return Constant_1.CHAT_REST_MAX_TIME - time;
    };
    //#region -------------------------------------------------------------------蜗牛岛--------------------------------------------------------------------
    LobbyModel.prototype.getSnailIsle = function () {
        return this.snailIsle;
    };
    // 点赞九万亩
    LobbyModel.prototype.likeJwm = function () {
        if (!this.isInitLikeJwmCount()) {
            return;
        }
        this.net.send('lobby/HD_LikeJwm');
        this.snailIsle.roleBow();
        this.sumLikeJwmCount += 1;
        this.user.addAccLikeJwmCount(1);
        this.getLikeJwmCountInterval = Math.max(5, this.getLikeJwmCountInterval);
        this.emit(EventType_1.default.UPDATE_LIKE_JWM_COUNT, 1);
    };
    LobbyModel.prototype.isInitLikeJwmCount = function () {
        return this.sumLikeJwmCount !== -1;
    };
    // 刷新检测获取九万亩点赞数
    LobbyModel.prototype.updateCheckLikeJwmCount = function (dt) {
        if (this.getLikeJwmCountInterval === -1) {
            return;
        }
        this.getLikeJwmCountInterval -= dt;
        if (this.getLikeJwmCountInterval <= 0) {
            this.initLikeJwmCount();
        }
    };
    LobbyModel.prototype.initLikeJwmCount = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        this.getLikeJwmCountInterval = -1;
                        return [4 /*yield*/, this.net.request('chat/HD_GetLikeJwmCount')];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (data) {
                            this.sumLikeJwmCount = (_a = data.sumLikeJwmCount) !== null && _a !== void 0 ? _a : 0;
                            this.emit(EventType_1.default.UPDATE_LIKE_JWM_COUNT, 2);
                        }
                        this.getLikeJwmCountInterval = 10;
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyModel.prototype.getSumLikeJwmCount = function () { return this.sumLikeJwmCount; };
    LobbyModel = __decorate([
        mc.addmodel('lobby')
    ], LobbyModel);
    return LobbyModel;
}(mc.BaseModel));
exports.default = LobbyModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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