
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/WeakGuidePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5f926Yp6BxIV4FLigqjKGj2', 'WeakGuidePnlCtrl');
// app/script/view/common/WeakGuidePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var ccclass = cc._decorator.ccclass;
var WeakGuidePnlCtrl = /** @class */ (function (_super) {
    __extends(WeakGuidePnlCtrl, _super);
    function WeakGuidePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descNode_ = null; // path://desc_n
        _this.fingerNode_ = null; // path://finger_n
        _this.touchNode_ = null; // path://touch_n
        _this.loadingNode_ = null; // path://loading_n
        //@end
        _this.model = null;
        _this.touchId = -1;
        _this.chooseRect = null;
        _this.chooseDesc = '';
        _this.chooseFinger = null;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    WeakGuidePnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.WEAK_GUIDE_RESET] = this.onWeakGuideReset, _a.enter = true, _a),
        ];
    };
    WeakGuidePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false, isClean: false });
                this.model = this.getModel('weak_guide');
                this.touchNode_.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
                this.touchNode_.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
                this.touchNode_.SetSwallowTouches(false);
                return [2 /*return*/];
            });
        });
    };
    WeakGuidePnlCtrl.prototype.onEnter = function (data) {
        this.reset();
    };
    WeakGuidePnlCtrl.prototype.onRemove = function () {
    };
    WeakGuidePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 打开框选节点
    WeakGuidePnlCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        this.chooseDesc = data.desc;
        this.chooseFinger = data.finger;
        this.beginChooseRect(data);
    };
    WeakGuidePnlCtrl.prototype.onWeakGuideReset = function () {
        this.reset();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    WeakGuidePnlCtrl.prototype.update = function (dt) {
    };
    WeakGuidePnlCtrl.prototype.reset = function () {
        this.descNode_.active = false;
        this.fingerNode_.active = false;
        this.loadingNode_.active = false;
        this.chooseRect = null;
        this.chooseDesc = '';
        this.chooseFinger = null;
    };
    WeakGuidePnlCtrl.prototype.onTouchStart = function (event) {
        if (this.touchId !== -1) {
            // event.stopPropagation()
        }
        else if (!this.checkSection(event.getLocation())) {
            // event.stopPropagation()
            this.touchId = -1;
        }
        else {
            this.touchId = event.getID();
        }
    };
    WeakGuidePnlCtrl.prototype.onTouchEnd = function (event) {
        if (this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        if (this.checkSection(event.getLocation())) {
            this.emit(EventType_1.default.WEAK_GUIDE_CLICK_CHOOSE_RECT);
        }
    };
    WeakGuidePnlCtrl.prototype.onTouchMove = function (event) {
        // event.stopPropagation()
    };
    WeakGuidePnlCtrl.prototype.onTouchCancel = function (event) {
        // event.stopPropagation()
        if (this.touchId === event.getID()) {
            this.touchId = -1;
        }
    };
    WeakGuidePnlCtrl.prototype.checkSection = function (pos) {
        if (!this.chooseRect) {
            return false;
        }
        pos.x -= cc.winSize.width * 0.5;
        pos.y -= cc.winSize.height * 0.5;
        return this.chooseRect.contains(pos);
    };
    // 开始选择
    WeakGuidePnlCtrl.prototype.beginChooseRect = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    // 播放框选
    WeakGuidePnlCtrl.prototype.playChooseRect = function (scale, hide) {
        var center = this.chooseRect.center;
        this.playChooseRectDone(center, scale);
    };
    WeakGuidePnlCtrl.prototype.playChooseRectDone = function (center, scale) {
        this.setDesc(this.chooseRect);
        this.setFinger(this.chooseRect);
    };
    // 引导描述
    WeakGuidePnlCtrl.prototype.setDesc = function (rect) {
        if (!this.chooseDesc) {
            return;
        }
        this.descNode_.Child('val').setLocaleKey(this.chooseDesc);
        var halfWidth = this.descNode_.width / 2;
        var halfHeight = this.descNode_.height / 2;
        var halfScreen = cc.winSize.width / 2;
        var center = rect.center;
        var posX = center.x;
        var posY = center.y;
        var offsetY = rect.height / 2 + halfHeight + 50;
        if (halfScreen - center.x < halfWidth) {
            posX = halfScreen - halfWidth;
        }
        else if (center.x < -halfScreen + halfWidth) {
            posX = -halfScreen + halfWidth;
        }
        else if (cc.winSize.height / 2 - center.y < halfWidth) {
            offsetY = -offsetY;
        }
        this.descNode_.setPosition(posX, posY + offsetY);
        this.descNode_.active = true;
        this.descNode_.opacity = 0;
        cc.tween(this.descNode_)
            .to(0.3, { opacity: 255 })
            .start();
    };
    // 设置手指
    WeakGuidePnlCtrl.prototype.setFinger = function (rect) {
        if (!this.chooseFinger) {
            return;
        }
        this.fingerNode_.active = true;
        var center = rect.center, offset = this.chooseFinger.offset || cc.v2();
        this.fingerNode_.setPosition(center.x + offset.x, center.y + offset.y);
        this.fingerNode_.angle = this.chooseFinger.angle || 0;
        // this.fingerNode_.Child('val', cc.Animation).play()
    };
    WeakGuidePnlCtrl = __decorate([
        ccclass
    ], WeakGuidePnlCtrl);
    return WeakGuidePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = WeakGuidePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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