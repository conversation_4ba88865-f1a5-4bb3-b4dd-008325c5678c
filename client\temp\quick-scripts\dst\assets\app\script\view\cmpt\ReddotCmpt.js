
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ReddotCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '81c18Qljq1J3Y4BEVk305Xw', 'ReddotCmpt');
// app/script/view/cmpt/ReddotCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 红点
 */
var ReddotCmpt = /** @class */ (function (_super) {
    __extends(ReddotCmpt, _super);
    function ReddotCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.keys = [];
        _this.dot = null;
        _this.datas = null;
        _this.len = 0;
        _this.preVal = false;
        return _this;
    }
    ReddotCmpt.prototype.onLoad = function () {
        this.dot = this.FindChild('val');
    };
    ReddotCmpt.prototype.onEnable = function () {
        this.init();
    };
    ReddotCmpt.prototype.update = function (dt) {
        if (!this.dot || this.len === 0 || !this.datas) {
            return;
        }
        var val = this.datas.some(function (m) { return m.val; });
        if (this.preVal !== val) {
            this.dot.active = this.preVal = val;
        }
    };
    ReddotCmpt.prototype.init = function () {
        if (this.dot) {
            this.dot.active = this.preVal = false;
        }
        this.datas = this.keys.map(function (key) { return ReddotHelper_1.reddotHelper.get(key); });
        this.len = this.datas.length;
    };
    ReddotCmpt.prototype.setKeys = function (keys) {
        this.keys = keys;
        this.init();
    };
    ReddotCmpt.prototype.addKey = function (key) {
        if (this.keys.has(key)) {
            return;
        }
        this.keys.push(key);
        this.init();
    };
    ReddotCmpt.prototype.removeKey = function (key) {
        if (this.keys.remove(key)) {
            this.init();
        }
    };
    __decorate([
        property([cc.String])
    ], ReddotCmpt.prototype, "keys", void 0);
    ReddotCmpt = __decorate([
        ccclass
    ], ReddotCmpt);
    return ReddotCmpt;
}(cc.Component));
exports.default = ReddotCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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