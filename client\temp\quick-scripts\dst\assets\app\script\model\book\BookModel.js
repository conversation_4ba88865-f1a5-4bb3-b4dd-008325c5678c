
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/book/BookModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '07b3dV00VhCVpmeZXbPrb6o', 'BookModel');
// app/script/model/book/BookModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
/**
 * 图鉴模块
 */
var BookModel = /** @class */ (function (_super) {
    __extends(BookModel, _super);
    function BookModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.lastReqBookStarTime = 0; //最后一次请求时间
        _this.tempBookStarMap = null; //图鉴基础信息列表
        _this.reqBookStarState = false;
        _this.lastReqCommentTimeMap = {}; //最后一次请求时间
        _this.reqCommentStateMap = {};
        _this.tempCommentMap = {};
        return _this;
    }
    BookModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    BookModel.prototype.clean = function () {
        this.lastReqBookStarTime = 0;
        this.tempBookStarMap = null;
        this.lastReqCommentTimeMap = {};
    };
    // 获取图鉴信息
    BookModel.prototype.initBookStarMap = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, isEmit, list;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqBookStarState) {
                            return [2 /*return*/];
                        }
                        else if (this.lastReqBookStarTime > 0 && Date.now() - this.lastReqBookStarTime <= 60000) {
                            return [2 /*return*/];
                        }
                        this.reqBookStarState = true;
                        return [4 /*yield*/, this.net.request('chat/HD_GetGalleryInfo', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.reqBookStarState = false;
                        this.lastReqBookStarTime = Date.now();
                        isEmit = !this.tempBookStarMap;
                        if (isEmit) {
                            this.tempBookStarMap = {};
                        }
                        list = (data === null || data === void 0 ? void 0 : data.list) || [];
                        list.forEach(function (m) {
                            _this.tempBookStarMap[m.type + '_' + m.id] = { star: m.star, commentCount: m.commentCount };
                        });
                        if (isEmit) {
                            this.emit(EventType_1.default.INIT_BOOK_BASEINFO_COMPLETE, this.tempBookStarMap);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化评价
    BookModel.prototype.initBookComment = function (type, id) {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var key, _e, err, data, isEmit, list, obj, preStar, curStar;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        key = type + '_' + id;
                        if (this.reqCommentStateMap[key]) {
                            return [2 /*return*/];
                        }
                        else if (!!this.lastReqCommentTimeMap[key] && Date.now() - this.lastReqCommentTimeMap[key] <= 60000) {
                            return [2 /*return*/];
                        }
                        this.reqCommentStateMap[key] = true;
                        return [4 /*yield*/, this.net.request('chat/HD_GetGalleryComments', { type: type, id: id })];
                    case 1:
                        _e = _f.sent(), err = _e.err, data = _e.data;
                        delete this.reqCommentStateMap[key];
                        this.lastReqCommentTimeMap[key] = Date.now();
                        isEmit = true /* !this.tempCommentMap[key] */;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [], obj = {};
                        list.forEach(function (m) {
                            var arr = obj[m.version];
                            if (!arr) {
                                arr = obj[m.version] = [];
                            }
                            arr.push(m);
                        });
                        this.tempCommentMap[key] = obj;
                        preStar = ((_b = (_a = this.tempBookStarMap) === null || _a === void 0 ? void 0 : _a[key]) === null || _b === void 0 ? void 0 : _b.star) || 0, curStar = ((_c = data === null || data === void 0 ? void 0 : data.galleryInfo) === null || _c === void 0 ? void 0 : _c.star) || 0;
                        if (this.tempBookStarMap && preStar !== curStar) {
                            this.tempBookStarMap[key] = { star: curStar, commentCount: ((_d = data === null || data === void 0 ? void 0 : data.galleryInfo) === null || _d === void 0 ? void 0 : _d.commentCount) || 0 };
                            this.emit(EventType_1.default.UPDATE_BOOK_COMMENT, key);
                        }
                        else if (isEmit) {
                            this.emit(EventType_1.default.INIT_BOOK_COMMENT_COMPLETE, key);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 检测更新图鉴信息
    BookModel.prototype.checkInitBookStarMap = function () {
        this.initBookStarMap();
        return this.tempBookStarMap;
    };
    BookModel.prototype.getBookStarByKey = function (key) {
        var _a, _b;
        return ((_b = (_a = this.tempBookStarMap) === null || _a === void 0 ? void 0 : _a[key]) === null || _b === void 0 ? void 0 : _b.star) || 0;
    };
    BookModel.prototype.getBookStarInfoByStudy = function (type, id) {
        var _a;
        var key = Constant_1.STUDY_TO_BOOKTYPE[type] + '_' + id;
        return ((_a = this.tempBookStarMap) === null || _a === void 0 ? void 0 : _a[key]) || { star: 0, commentCount: 0 };
    };
    // 检测初始化评价
    BookModel.prototype.checkInitComment = function (type, id) {
        this.initBookComment(type, id);
        return this.tempCommentMap[type + '_' + id];
    };
    BookModel.prototype.getCommentVersionList = function (key) {
        var arr = [];
        var obj = this.tempCommentMap[key] || {};
        for (var k in obj) {
            if (obj[k].length > 0) {
                arr.push(k);
            }
        }
        return arr;
    };
    // 获取列表
    BookModel.prototype.getCommentListByVersion = function (key, version) {
        var obj = this.tempCommentMap[key];
        if (!obj) {
            return [];
        }
        else if (version) {
            return obj[version] || [];
        }
        var arr = [];
        for (var k in obj) {
            arr.pushArr(obj[k]);
        }
        return arr;
    };
    // 获取评价列表
    BookModel.prototype.getCommentListAndNew = function (key, version) {
        var obj = this.tempCommentMap[key];
        if (!obj) {
            obj = this.tempCommentMap[key] = {};
        }
        var list = obj[version];
        if (!list) {
            list = obj[version] = [];
        }
        return list;
    };
    BookModel.prototype.removeCommentByUser = function (key, uid) {
        var obj = this.tempCommentMap[key] || {};
        for (var k in obj) {
            var list = obj[k];
            var i = list.findIndex(function (m) { return m.userId === uid; });
            if (i !== -1) {
                list.splice(i, 1);
                return;
            }
        }
    };
    // 发表评价
    BookModel.prototype.snedComment = function (type, id, star, content) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var version, _c, err, data, type_1, id_1, key;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        version = GameHelper_1.gameHpr.getVersion();
                        return [4 /*yield*/, this.net.request('chat/HD_CommentGallery', { id: id, type: type, star: star, content: content, version: version }, true)];
                    case 1:
                        _c = _d.sent(), err = _c.err, data = _c.data;
                        if (!err && data.comment) {
                            type_1 = data.comment.type, id_1 = data.comment.id;
                            key = type_1 + '_' + id_1;
                            this.removeCommentByUser(key, GameHelper_1.gameHpr.getUid());
                            this.getCommentListAndNew(key, data.comment.version).push(data.comment);
                            this.tempBookStarMap[key] = { star: ((_a = data.galleryInfo) === null || _a === void 0 ? void 0 : _a.star) || 0, commentCount: ((_b = data.galleryInfo) === null || _b === void 0 ? void 0 : _b.commentCount) || 0 };
                            this.emit(EventType_1.default.UPDATE_BOOK_COMMENT, key);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    BookModel = __decorate([
        mc.addmodel('book')
    ], BookModel);
    return BookModel;
}(mc.BaseModel));
exports.default = BookModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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