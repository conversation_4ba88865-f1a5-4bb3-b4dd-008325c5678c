
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/activity/MysteryboxShow102PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a7deeryKhZM6rGaiFAKftch', 'MysteryboxShow102PnlCtrl');
// app/script/view/activity/MysteryboxShow102PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MysteryboxShow102PnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxShow102PnlCtrl, _super);
    function MysteryboxShow102PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.posNode_ = null; // path://root/pos_n
        _this.rootNode_ = null; // path://root/root_nbe_n
        _this.descLbl_ = null; // path://root/desc_l
        //@end
        _this.boxId = 0;
        _this.ANIMATIONS = ['mysterybox_anim_102_normal', 'mysterybox_anim_102_special'];
        return _this;
    }
    MysteryboxShow102PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxShow102PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MysteryboxShow102PnlCtrl.prototype.onEnter = function (id) {
        var _this = this;
        this.boxId = id;
        this.rootNode_.children.forEach(function (m) {
            var _a;
            m.active = true;
            m.opacity = 255;
            var effect = m.Child('item/effect', cc.Animation);
            effect.reset();
            effect.node.active = false;
            m.Child('count').active = false;
            m.Component(cc.Button).interactable = false;
            var anim = m.Child('item', cc.Animation), name = (_a = m.Data) !== null && _a !== void 0 ? _a : _this.ANIMATIONS[0];
            anim.reset(name);
            m.Child('item/mask/card/val', cc.Sprite).spriteFrame = null;
            m.setPosition(0, 0);
            var target = _this.posNode_.Child(m.name).getPosition();
            m.stopAllActions();
            cc.tween(m)
                .to(0.3, { x: target.x, y: target.y }, { easing: cc.easing.sineOut })
                .call(function () { return m.Component(cc.Button).interactable = true; })
                .start();
        });
        this.descLbl_.setLocaleKey('');
        ut.wait(0.4, this).then(function () {
            if (_this.isValid) {
                _this.descLbl_.setLocaleKey('ui.please_select_mb_box_102');
            }
        });
    };
    MysteryboxShow102PnlCtrl.prototype.onRemove = function () {
    };
    MysteryboxShow102PnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/root_nbe_n
    MysteryboxShow102PnlCtrl.prototype.onClickRoot = function (event, data) {
        audioMgr.playSFX('click');
        this.do(event.target.name);
    };
    // path://close_be_n
    MysteryboxShow102PnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 购买盲盒
    MysteryboxShow102PnlCtrl.prototype.do = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, json, _loop_1, this_1, i, l, isHideCard, it, anim;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        mc.lockTouch('item_skin');
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuySkinBlindBox', { id: this.boxId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.descLbl_.setLocaleKey('');
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.setSkinItemList(data.skinItemList);
                        json = assetsMgr.getJsonData('pawnSkin', data.skinId);
                        if (!json) {
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.UNKNOWN)];
                        }
                        _loop_1 = function (i, l) {
                            var it_1 = this_1.rootNode_.children[i];
                            it_1.Component(cc.Button).interactable = false;
                            if (it_1.name !== index) {
                                cc.tween(it_1)
                                    .to(0.3, { opacity: 0 })
                                    .call(function () { return it_1.active = false; })
                                    .start();
                            }
                        };
                        this_1 = this;
                        for (i = 0, l = this.rootNode_.children.length; i < l; i++) {
                            _loop_1(i, l);
                        }
                        isHideCard = json.value === 2;
                        it = this.rootNode_.Child(index);
                        anim = it.Data = isHideCard ? this.ANIMATIONS[1] : this.ANIMATIONS[0];
                        cc.tween(it)
                            .to(0.3, { x: 0, y: 0 })
                            .call(function () {
                            var spr = it.Child('item/mask/card/val', cc.Sprite);
                            ResHelper_1.resHelper.loadPawnHeadIcon(data.skinId, spr, _this.key);
                            it.Child('item', cc.Animation).playAsync(anim).then(function () {
                                var _a;
                                var count = ((_a = GameHelper_1.gameHpr.user.getSkinItemList().filter(function (m) { return m.id === data.skinId; })) === null || _a === void 0 ? void 0 : _a.length) || 1;
                                it.Child('count').active = true;
                                it.Child('count/val', cc.Label).string = Math.max(0, count - 1) + '';
                                _this.closeNode_.active = true;
                                _this.descLbl_.setLocaleKey('ui.click_close_desc');
                                mc.unlockTouch('item_skin');
                            });
                            if (isHideCard) {
                                ut.wait(1.58).then(function () {
                                    if (_this.isValid) {
                                        var effect = it.Child('item/effect', cc.Animation);
                                        effect.node.active = true;
                                        effect.play();
                                    }
                                });
                            }
                        })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxShow102PnlCtrl = __decorate([
        ccclass
    ], MysteryboxShow102PnlCtrl);
    return MysteryboxShow102PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxShow102PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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