
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/DonateAncientLvPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ff88aonsGdNPo3LtJ/+WF7j', 'DonateAncientLvPnlCtrl');
// app/script/view/build/DonateAncientLvPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var DonateAncientLvPnlCtrl = /** @class */ (function (_super) {
    __extends(DonateAncientLvPnlCtrl, _super);
    function DonateAncientLvPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconSpr_ = null; // path://root/content/bg/icon_s
        _this.inputEb_ = null; // path://root/content/bg/input_eb_ebee
        //@end
        _this.type = null;
        _this.maxCount = 0;
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    DonateAncientLvPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DonateAncientLvPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DonateAncientLvPnlCtrl.prototype.onEnter = function (type, maxCount, cb) {
        this.type = type;
        this.maxCount = maxCount;
        this.cb = cb;
        this.inputEb_.string = '';
        this.inputEb_.setPlaceholder('ui.max_donate_count', 'f_m', maxCount);
        this.iconSpr_.spriteFrame = ResHelper_1.resHelper.getResIcon(type);
    };
    DonateAncientLvPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    DonateAncientLvPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    DonateAncientLvPnlCtrl.prototype.onClickOk = function (event, data) {
        var count = Number(this.inputEb_.string.trim());
        if (!count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RES_COUNT);
        }
        else if (count > this.maxCount) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIETN_CONTRIBUTE_RES_LIMIT);
        }
        else if (GameHelper_1.gameHpr.getCountByCType(this.type) < count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        this.cb && this.cb(count);
        this.hide();
    };
    // path://root/content/bg/input_eb_ebee
    DonateAncientLvPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var count = Number(event.string.trim());
        if (!count) {
            event.string = '';
            return;
        }
        var cnt = count;
        if (count > this.maxCount) {
            cnt = this.maxCount;
        }
        var ac = GameHelper_1.gameHpr.getCountByCType(this.type);
        if (ac < cnt) {
            cnt = ac;
        }
        if (cnt !== count) {
            event.string = '' + cnt;
        }
    };
    // path://root/content/max_be
    DonateAncientLvPnlCtrl.prototype.onClickMax = function (event, data) {
        this.inputEb_.string = Math.min(this.maxCount, GameHelper_1.gameHpr.getCountByCType(this.type)) + '';
    };
    DonateAncientLvPnlCtrl = __decorate([
        ccclass
    ], DonateAncientLvPnlCtrl);
    return DonateAncientLvPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DonateAncientLvPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxEb25hdGVBbmNpZW50THZQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHFEQUFvRDtBQUVwRCw2REFBeUQ7QUFDekQsMkRBQTBEO0FBQzFELDZEQUE0RDtBQUVwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFvRCwwQ0FBYztJQUFsRTtRQUFBLHFFQWlGQztRQS9FRywwQkFBMEI7UUFDbEIsY0FBUSxHQUFjLElBQUksQ0FBQSxDQUFDLGdDQUFnQztRQUMzRCxjQUFRLEdBQWUsSUFBSSxDQUFBLENBQUMsdUNBQXVDO1FBQzNFLE1BQU07UUFFRSxVQUFJLEdBQVUsSUFBSSxDQUFBO1FBQ2xCLGNBQVEsR0FBVyxDQUFDLENBQUE7UUFDcEIsUUFBRSxHQUFhLElBQUksQ0FBQTs7UUFtRTNCLE1BQU07UUFDTixpSEFBaUg7UUFFakgsaUhBQWlIO0lBRXJILENBQUM7SUF0RVUsZ0RBQWUsR0FBdEI7UUFDSSxPQUFPLEVBQUUsQ0FBQTtJQUNiLENBQUM7SUFFWSx5Q0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSx3Q0FBTyxHQUFkLFVBQWUsSUFBVyxFQUFFLFFBQWdCLEVBQUUsRUFBWTtRQUN0RCxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsUUFBUSxHQUFHLFFBQVEsQ0FBQTtRQUN4QixJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQTtRQUNaLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQTtRQUN6QixJQUFJLENBQUMsUUFBUSxDQUFDLGNBQWMsQ0FBQyxxQkFBcUIsRUFBRSxLQUFLLEVBQUUsUUFBUSxDQUFDLENBQUE7UUFDcEUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUE7SUFDMUQsQ0FBQztJQUVNLHlDQUFRLEdBQWY7UUFDSSxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDckIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLHdDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQixvQkFBb0I7SUFDcEIsMENBQVMsR0FBVCxVQUFVLEtBQTBCLEVBQUUsSUFBWTtRQUM5QyxJQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQTtRQUNqRCxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ1IsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsc0JBQXNCLENBQUMsQ0FBQTtTQUM1RDthQUFNLElBQUksS0FBSyxHQUFHLElBQUksQ0FBQyxRQUFRLEVBQUU7WUFDOUIsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsNEJBQTRCLENBQUMsQ0FBQTtTQUNsRTthQUFNLElBQUksb0JBQU8sQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEtBQUssRUFBRTtZQUNuRCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLGFBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQTtTQUNwRDtRQUNELElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUN6QixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7SUFDZixDQUFDO0lBRUQsdUNBQXVDO0lBQ3ZDLGtEQUFpQixHQUFqQixVQUFrQixLQUFpQixFQUFFLElBQVk7UUFDN0MsSUFBTSxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQTtRQUN6QyxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ1IsS0FBSyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUE7WUFDakIsT0FBTTtTQUNUO1FBQ0QsSUFBSSxHQUFHLEdBQUcsS0FBSyxDQUFBO1FBQ2YsSUFBSSxLQUFLLEdBQUcsSUFBSSxDQUFDLFFBQVEsRUFBRTtZQUN2QixHQUFHLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQTtTQUN0QjtRQUNELElBQU0sRUFBRSxHQUFHLG9CQUFPLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUM3QyxJQUFJLEVBQUUsR0FBRyxHQUFHLEVBQUU7WUFDVixHQUFHLEdBQUcsRUFBRSxDQUFBO1NBQ1g7UUFDRCxJQUFJLEdBQUcsS0FBSyxLQUFLLEVBQUU7WUFDZixLQUFLLENBQUMsTUFBTSxHQUFHLEVBQUUsR0FBRyxHQUFHLENBQUE7U0FDMUI7SUFDTCxDQUFDO0lBRUQsNkJBQTZCO0lBQzdCLDJDQUFVLEdBQVYsVUFBVyxLQUEwQixFQUFFLElBQVk7UUFDL0MsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLG9CQUFPLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtJQUMzRixDQUFDO0lBM0VnQixzQkFBc0I7UUFEMUMsT0FBTztPQUNhLHNCQUFzQixDQWlGMUM7SUFBRCw2QkFBQztDQWpGRCxBQWlGQyxDQWpGbUQsRUFBRSxDQUFDLFdBQVcsR0FpRmpFO2tCQWpGb0Isc0JBQXNCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZWNvZGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VDb2RlXCI7XHJcbmltcG9ydCB7IENUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xyXG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xyXG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcclxuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcclxuXHJcbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIERvbmF0ZUFuY2llbnRMdlBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XHJcblxyXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cclxuICAgIHByaXZhdGUgaWNvblNwcl86IGNjLlNwcml0ZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvY29udGVudC9iZy9pY29uX3NcclxuICAgIHByaXZhdGUgaW5wdXRFYl86IGNjLkVkaXRCb3ggPSBudWxsIC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvYmcvaW5wdXRfZWJfZWJlZVxyXG4gICAgLy9AZW5kXHJcblxyXG4gICAgcHJpdmF0ZSB0eXBlOiBDVHlwZSA9IG51bGxcclxuICAgIHByaXZhdGUgbWF4Q291bnQ6IG51bWJlciA9IDBcclxuICAgIHByaXZhdGUgY2I6IEZ1bmN0aW9uID0gbnVsbFxyXG5cclxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XHJcbiAgICAgICAgcmV0dXJuIFtdXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkVudGVyKHR5cGU6IENUeXBlLCBtYXhDb3VudDogbnVtYmVyLCBjYjogRnVuY3Rpb24pIHtcclxuICAgICAgICB0aGlzLnR5cGUgPSB0eXBlXHJcbiAgICAgICAgdGhpcy5tYXhDb3VudCA9IG1heENvdW50XHJcbiAgICAgICAgdGhpcy5jYiA9IGNiXHJcbiAgICAgICAgdGhpcy5pbnB1dEViXy5zdHJpbmcgPSAnJ1xyXG4gICAgICAgIHRoaXMuaW5wdXRFYl8uc2V0UGxhY2Vob2xkZXIoJ3VpLm1heF9kb25hdGVfY291bnQnLCAnZl9tJywgbWF4Q291bnQpXHJcbiAgICAgICAgdGhpcy5pY29uU3ByXy5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRSZXNJY29uKHR5cGUpXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xyXG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYigwKVxyXG4gICAgICAgIHRoaXMuY2IgPSBudWxsXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxyXG5cclxuICAgIC8vIHBhdGg6Ly9yb290L29rX2JlXHJcbiAgICBvbkNsaWNrT2soZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG4gICAgICAgIGNvbnN0IGNvdW50ID0gTnVtYmVyKHRoaXMuaW5wdXRFYl8uc3RyaW5nLnRyaW0oKSlcclxuICAgICAgICBpZiAoIWNvdW50KSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5QTEVBU0VfSU5QVVRfUkVTX0NPVU5UKVxyXG4gICAgICAgIH0gZWxzZSBpZiAoY291bnQgPiB0aGlzLm1heENvdW50KSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5BTkNJRVROX0NPTlRSSUJVVEVfUkVTX0xJTUlUKVxyXG4gICAgICAgIH0gZWxzZSBpZiAoZ2FtZUhwci5nZXRDb3VudEJ5Q1R5cGUodGhpcy50eXBlKSA8IGNvdW50KSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5SRVNfTk9UX0VOT1VHSClcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5jYiAmJiB0aGlzLmNiKGNvdW50KVxyXG4gICAgICAgIHRoaXMuaGlkZSgpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gcGF0aDovL3Jvb3QvY29udGVudC9iZy9pbnB1dF9lYl9lYmVlXHJcbiAgICBvbkNsaWNrSW5wdXRFbmRlZChldmVudDogY2MuRWRpdEJveCwgZGF0YTogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QgY291bnQgPSBOdW1iZXIoZXZlbnQuc3RyaW5nLnRyaW0oKSlcclxuICAgICAgICBpZiAoIWNvdW50KSB7XHJcbiAgICAgICAgICAgIGV2ZW50LnN0cmluZyA9ICcnXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuICAgICAgICBsZXQgY250ID0gY291bnRcclxuICAgICAgICBpZiAoY291bnQgPiB0aGlzLm1heENvdW50KSB7XHJcbiAgICAgICAgICAgIGNudCA9IHRoaXMubWF4Q291bnRcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgYWMgPSBnYW1lSHByLmdldENvdW50QnlDVHlwZSh0aGlzLnR5cGUpXHJcbiAgICAgICAgaWYgKGFjIDwgY250KSB7XHJcbiAgICAgICAgICAgIGNudCA9IGFjXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChjbnQgIT09IGNvdW50KSB7XHJcbiAgICAgICAgICAgIGV2ZW50LnN0cmluZyA9ICcnICsgY250XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvbWF4X2JlXHJcbiAgICBvbkNsaWNrTWF4KGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcclxuICAgICAgICB0aGlzLmlucHV0RWJfLnN0cmluZyA9IE1hdGgubWluKHRoaXMubWF4Q291bnQsIGdhbWVIcHIuZ2V0Q291bnRCeUNUeXBlKHRoaXMudHlwZSkpICsgJydcclxuICAgIH1cclxuICAgIC8vQGVuZFxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxufVxyXG4iXX0=