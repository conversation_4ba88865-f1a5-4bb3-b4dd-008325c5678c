
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NovicePawnLevelingObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '22050Fg4SVME7YYZv8BYyGV', 'NovicePawnLevelingObj');
// app/script/model/guide/NovicePawnLevelingObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个练级
var NovicePawnLevelingObj = /** @class */ (function () {
    function NovicePawnLevelingObj() {
        this.uid = '';
        this.index = 0; //区域位置
        this.auid = ''; //军队uid
        this.puid = ''; //士兵uid
        this.id = 0; //士兵id
        this.lv = 0; //训练等级
        this.startTime = 0; //开始时间
        this.needTime = 0; //需要时间
    }
    NovicePawnLevelingObj.prototype.init = function (index, auid, puid, id, lv, time) {
        this.uid = ut.UID();
        this.index = index;
        this.auid = auid;
        this.puid = puid;
        this.id = id;
        this.lv = lv;
        this.startTime = 0;
        this.needTime = time;
        return this;
    };
    NovicePawnLevelingObj.prototype.fromDB = function (data) {
        this.uid = data.uid;
        this.index = data.index;
        this.auid = data.auid;
        this.puid = data.puid;
        this.id = data.id;
        this.lv = data.lv;
        this.startTime = data.startTime;
        this.needTime = data.needTime;
        return this;
    };
    NovicePawnLevelingObj.prototype.strip = function () {
        return {
            uid: this.uid,
            index: this.index,
            auid: this.auid,
            puid: this.puid,
            id: this.id,
            lv: this.lv,
            needTime: this.needTime,
            surplusTime: Math.max(this.needTime - (Date.now() - this.startTime), 0),
        };
    };
    return NovicePawnLevelingObj;
}());
exports.default = NovicePawnLevelingObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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