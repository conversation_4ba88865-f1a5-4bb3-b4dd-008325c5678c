
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/TeamListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ed9a1sbEW9KI6Q6ktb95LlJ', 'TeamListPnlCtrl');
// app/script/view/lobby/TeamListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TeamListPnlCtrl = /** @class */ (function (_super) {
    __extends(TeamListPnlCtrl, _super);
    function TeamListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/team/list_sv
        _this.friendsSv_ = null; // path://root/friend/friends_sv
        _this.inviteNode_ = null; // path://root/invite_n
        _this.inputPlayerEb_ = null; // path://root/invite_n/input_player_eb
        _this.buttonNode_ = null; // path://root/button_n
        //@end
        _this.team = null;
        _this.isCanExitTeam = false;
        return _this;
    }
    TeamListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_TEAM_LIST] = this.onUpdateTeamList, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_TEAM_APPLYS] = this.onUpdateTeamInvite, _b.enter = true, _b),
        ];
    };
    TeamListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.team = this.getModel('team');
                return [2 /*return*/];
            });
        });
    };
    TeamListPnlCtrl.prototype.onEnter = function (showFriend) {
        var teamNode = this.listSv_.node.parent;
        teamNode.height = showFriend ? 410 : 790;
        this.updateTeams();
        if (this.friendsSv_.node.parent.active = showFriend) {
            this.updateFriends();
        }
        if (this.inviteNode_.active = showFriend) {
            this.inputPlayerEb_.setPlaceholder('ui.input_give_name_desc', 'f_m');
        }
    };
    TeamListPnlCtrl.prototype.onRemove = function () {
    };
    TeamListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/invite_n/invite_player_be
    TeamListPnlCtrl.prototype.onClickInvitePlayer = function (event, _) {
        if (!this.team.isMyInviteAuth()) {
            return ViewHelper_1.viewHelper.showAlert('toast.not_invite_auth');
        }
        else if (this.team.isInGame()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.IN_GAME);
        }
        else if (this.team.isInApply()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.IN_APPLY);
        }
        var val = this.inputPlayerEb_.string.trim();
        if (!val) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_name_or_uid');
        }
        this.invitePlayer(val, true);
    };
    // path://root/team/list_sv/view/content/item_be
    TeamListPnlCtrl.prototype.onClickItem = function (event, _) {
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('lobby/UserInfo', data, 'team'); // TeammateInfo'
        }
    };
    // path://root/button_n/dissolve_be
    TeamListPnlCtrl.prototype.onClickDissolve = function (event, data) {
        var _this = this;
        if (!this.team.isCaptain() || this.team.isInGame()) {
            return;
        }
        else if (this.team.isInApply()) {
            return ViewHelper_1.viewHelper.showAlert('toast.applying_not_dissolve_team'); //报名中无法解散队伍，请先取消报名
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.dissolve_team_tip', {
            ok: function () { return _this.team.dissolve().then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); }); },
            cancel: function () { }
        });
    };
    // path://root/friend/friends_sv/view/content/friend/invite_be
    TeamListPnlCtrl.prototype.onClickInvite = function (event, _data) {
        var data = event.target.parent.Data;
        data && this.invitePlayer(data.uid);
    };
    // path://root/button_n/exit_team_be
    TeamListPnlCtrl.prototype.onClickExitTeam = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showMessageBox('ui.exit_team_tip', {
            ok: function () { return _this.isValid && _this.exitTeam(); },
            cancel: function () { }
        });
    };
    // path://root/button_n/team_invite_be
    TeamListPnlCtrl.prototype.onClickTeamInvite = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('lobby/TeamInvite');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    TeamListPnlCtrl.prototype.onUpdateTeamList = function () {
        this.updateTeams();
    };
    TeamListPnlCtrl.prototype.onUpdateTeamInvite = function () {
        this.updateTeams();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 队伍列表 -----------------------------------------------------------------------------------------------------------------------------
    TeamListPnlCtrl.prototype.updateTeams = function () {
        var _this = this;
        var hasTeam = this.team.hasTeam(), teamUid = this.team.getUid(), list = this.team.getActTeammates().slice();
        var count = this.listSv_.node.parent.Child('title/count_bg');
        count.Child('cur', cc.Label).string = list.length + '/';
        count.Child('max', cc.Label).string = '40';
        var showButton = this.updateOptionButton();
        var len = showButton ? list.unshift(null) : list.length;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Items(list, function (it, data, i) {
            var _a;
            it.Data = data;
            if (!data) {
                it.Swih('');
            }
            else {
                it.Swih('', true, 'button_n');
                var headNode = it.Child('head');
                var captain = (_a = it.Child('captain')) === null || _a === void 0 ? void 0 : _a.setActive(!!data && data.uid === teamUid && hasTeam);
                it.Child('auth').active = !captain && (data === null || data === void 0 ? void 0 : data.job) === 2;
                ResHelper_1.resHelper.loadPlayerHead(headNode, data.headIcon, _this.key);
                it.Child('name', cc.Label).string = data ? ut.nameFormator(data.nickname || '???', 4) : '';
                var wait = it.Child('wait').setActive(!!data && data.job === undefined);
                it.Child('name').opacity = headNode.opacity = wait ? 120 : 255;
            }
            if (i === len - 1) {
                _this.buttonNode_.active = showButton;
                if (showButton) {
                    _this.buttonNode_.parent = _this.listSv_.content.children[0];
                    _this.buttonNode_.setPosition(0, 0);
                }
            }
        });
    };
    TeamListPnlCtrl.prototype.updateOptionButton = function () {
        var hasTeam = this.team.hasTeam(), isPlay = this.team.isInGame(), isApply = this.team.isInApply(), isCaptain = this.team.isCaptain();
        this.isCanExitTeam = hasTeam && !isPlay && !isApply && !isCaptain;
        var showDissolve = hasTeam && !isPlay && isCaptain;
        var showExit = this.isCanExitTeam;
        var showTeamInvite = this.team.getReceiveInvites().length > 0 && !this.isCanExitTeam;
        var showButton = showDissolve || showExit || showTeamInvite;
        this.buttonNode_.Child('dissolve_be').active = showDissolve;
        this.buttonNode_.Child('exit_team_be').active = showExit;
        this.buttonNode_.Child('team_invite_be').active = showTeamInvite;
        return showButton;
    };
    // 退出队伍
    TeamListPnlCtrl.prototype.exitTeam = function () {
        this.team.exitTeam().then(function (err) {
            if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    // 邀请同玩 -----------------------------------------------------------------------------------------------------------------------------
    TeamListPnlCtrl.prototype.updateFriends = function () {
        var _this = this;
        var list = GameHelper_1.gameHpr.friend.getFriends(), len = list.length, inTeamMap = {}, yetinviteMap = {};
        var count = this.friendsSv_.node.parent.Child('title/count_bg');
        count.Child('cur', cc.Label).string = len + '/';
        count.Child('max', cc.Label).string = '100';
        this.team.getTeammates().forEach(function (m) { return inTeamMap[m.uid] = true; });
        this.team.getSendApplys().forEach(function (m) { return yetinviteMap[m.uid] = true; });
        list.sort(function (a, b) {
            var aw = !a.playSid && !inTeamMap[a.uid] && !yetinviteMap[a.uid] ? 1 : 0;
            var bw = !b.playSid && !inTeamMap[b.uid] && !yetinviteMap[b.uid] ? 1 : 0;
            if (aw === bw) {
                return a.offlineTime - b.offlineTime;
            }
            return bw - aw;
        });
        this.friendsSv_.Child('empty').active = !len;
        this.friendsSv_.List(len, function (it, i) {
            var data = it.Data = list[i], inTeam = inTeamMap[data.uid], yetInvite = yetinviteMap[data.uid];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name/val', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            it.Child('name/note', cc.Label).string = data.noteName ? '(' + data.noteName + ')' : '';
            /*  if (data.offlineTime) {
                 it.Child('online').Color('#A18876').setLocaleKey('ui.offline_time', gameHpr.millisecondToStringForDay(data.getOfflineTime()))
             } else */ if (data.playSid) {
                it.Child('online').Color('#A18876').setLocaleKey('ui.in_game');
            }
            else if (inTeam) {
                it.Child('online').Color('#4AB32E').setLocaleKey('ui.in_team');
            }
            else {
                it.Child('online').Color('#A18876').setLocaleKey('ui.in_ready_war');
            }
            it.Child('invite_be').active = !data.playSid && !inTeam && !yetInvite;
            it.Child('playing').active = false; //!!data.playSid
            it.Child('teaming').active = false; //!!inTeam
            it.Child('yetinvite').active = !!yetInvite;
        });
    };
    TeamListPnlCtrl.prototype.invitePlayer = function (uid, isAlert) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.team.inviteTeammate(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.updateFriends();
                            this.inputPlayerEb_.string = '';
                            isAlert && ViewHelper_1.viewHelper.showAlert('toast.invite_sended');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    TeamListPnlCtrl = __decorate([
        ccclass
    ], TeamListPnlCtrl);
    return TeamListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TeamListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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