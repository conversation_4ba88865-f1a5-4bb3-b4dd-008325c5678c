
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelWaitDot.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '36939br9zhHZ4F0UWXjtbg9', 'LabelWaitDot');
// app/core/component/LabelWaitDot.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent, menu = _a.menu;
var LabelWaitDot = /** @class */ (function (_super) {
    __extends(LabelWaitDot, _super);
    function LabelWaitDot() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.interval = 0.5;
        _this._label = null;
        _this.originalString = '';
        _this.dot = '';
        _this.elapsed = 0;
        _this.running = false;
        _this.pause = false;
        return _this;
    }
    Object.defineProperty(LabelWaitDot.prototype, "label", {
        get: function () { return this._label || (this._label = this.getComponent(cc.Label)); },
        enumerable: false,
        configurable: true
    });
    LabelWaitDot.prototype.onEnable = function () {
        this.pause = false;
    };
    LabelWaitDot.prototype.onDisable = function () {
        this.pause = true;
    };
    LabelWaitDot.prototype.play = function (val) {
        this.originalString = this.label.string = val !== undefined ? val : this.label.string;
        this.dot = '';
        this.elapsed = 0;
        this.running = true;
    };
    LabelWaitDot.prototype.stop = function (val) {
        this.running = false;
        if (val !== undefined) {
            this.label.string = val;
        }
        else {
            this.label.string = this.originalString === undefined ? this.label.string : this.originalString;
        }
        this.originalString = '';
        this.dot = '';
    };
    LabelWaitDot.prototype.tick = function () {
        if (this.dot.length < 3) {
            this.dot += '.';
        }
        else {
            this.dot = '';
        }
        this.label.string = this.originalString + this.dot;
    };
    LabelWaitDot.prototype.update = function (dt) {
        if (!this.running || this.pause) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed >= this.interval) {
            this.elapsed -= this.interval;
            this.tick();
        }
    };
    __decorate([
        property()
    ], LabelWaitDot.prototype, "interval", void 0);
    LabelWaitDot = __decorate([
        ccclass,
        menu('自定义组件/LabelWaitDot'),
        requireComponent(cc.Label)
    ], LabelWaitDot);
    return LabelWaitDot;
}(cc.Component));
exports.default = LabelWaitDot;
cc.LabelWaitDot = LabelWaitDot;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxjb21wb25lbnRcXExhYmVsV2FpdERvdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBTSxJQUFBLEtBQWdELEVBQUUsQ0FBQyxVQUFVLEVBQTNELE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBQSxFQUFFLGdCQUFnQixzQkFBQSxFQUFFLElBQUksVUFBa0IsQ0FBQztBQUtwRTtJQUEwQyxnQ0FBWTtJQUF0RDtRQUFBLHFFQTREQztRQXpEVyxjQUFRLEdBQVcsR0FBRyxDQUFBO1FBRXRCLFlBQU0sR0FBYSxJQUFJLENBQUE7UUFDdkIsb0JBQWMsR0FBVyxFQUFFLENBQUE7UUFDM0IsU0FBRyxHQUFXLEVBQUUsQ0FBQTtRQUVoQixhQUFPLEdBQVcsQ0FBQyxDQUFBO1FBQ25CLGFBQU8sR0FBWSxLQUFLLENBQUE7UUFDeEIsV0FBSyxHQUFZLEtBQUssQ0FBQTs7SUFpRGxDLENBQUM7SUEvQ0csc0JBQVksK0JBQUs7YUFBakIsY0FBc0IsT0FBTyxJQUFJLENBQUMsTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBLENBQUMsQ0FBQzs7O09BQUE7SUFFekYsK0JBQVEsR0FBUjtRQUNJLElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFBO0lBQ3RCLENBQUM7SUFFRCxnQ0FBUyxHQUFUO1FBQ0ksSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7SUFDckIsQ0FBQztJQUVNLDJCQUFJLEdBQVgsVUFBWSxHQUFZO1FBQ3BCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsR0FBRyxLQUFLLFNBQVMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQTtRQUNyRixJQUFJLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQTtRQUNiLElBQUksQ0FBQyxPQUFPLEdBQUcsQ0FBQyxDQUFBO1FBQ2hCLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFBO0lBQ3ZCLENBQUM7SUFFTSwyQkFBSSxHQUFYLFVBQVksR0FBWTtRQUNwQixJQUFJLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQTtRQUNwQixJQUFJLEdBQUcsS0FBSyxTQUFTLEVBQUU7WUFDbkIsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFBO1NBQzFCO2FBQU07WUFDSCxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsY0FBYyxLQUFLLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUE7U0FDbEc7UUFDRCxJQUFJLENBQUMsY0FBYyxHQUFHLEVBQUUsQ0FBQTtRQUN4QixJQUFJLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQTtJQUNqQixDQUFDO0lBRU8sMkJBQUksR0FBWjtRQUNJLElBQUksSUFBSSxDQUFDLEdBQUcsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCLElBQUksQ0FBQyxHQUFHLElBQUksR0FBRyxDQUFBO1NBQ2xCO2FBQU07WUFDSCxJQUFJLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQTtTQUNoQjtRQUNELElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQTtJQUN0RCxDQUFDO0lBRUQsNkJBQU0sR0FBTixVQUFPLEVBQVU7UUFDYixJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQzdCLE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxPQUFPLElBQUksRUFBRSxDQUFBO1FBQ2xCLElBQUksSUFBSSxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQy9CLElBQUksQ0FBQyxPQUFPLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQTtZQUM3QixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7U0FDZDtJQUNMLENBQUM7SUF4REQ7UUFEQyxRQUFRLEVBQUU7a0RBQ21CO0lBSGIsWUFBWTtRQUhoQyxPQUFPO1FBQ1AsSUFBSSxDQUFDLG9CQUFvQixDQUFDO1FBQzFCLGdCQUFnQixDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUM7T0FDTixZQUFZLENBNERoQztJQUFELG1CQUFDO0NBNURELEFBNERDLENBNUR5QyxFQUFFLENBQUMsU0FBUyxHQTREckQ7a0JBNURvQixZQUFZO0FBOERqQyxFQUFFLENBQUMsWUFBWSxHQUFHLFlBQVksQ0FBQSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHksIHJlcXVpcmVDb21wb25lbnQsIG1lbnUgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG5AY2NjbGFzc1xyXG5AbWVudSgn6Ieq5a6a5LmJ57uE5Lu2L0xhYmVsV2FpdERvdCcpXHJcbkByZXF1aXJlQ29tcG9uZW50KGNjLkxhYmVsKVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBMYWJlbFdhaXREb3QgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIEBwcm9wZXJ0eSgpXHJcbiAgICBwcml2YXRlIGludGVydmFsOiBudW1iZXIgPSAwLjVcclxuXHJcbiAgICBwcml2YXRlIF9sYWJlbDogY2MuTGFiZWwgPSBudWxsXHJcbiAgICBwcml2YXRlIG9yaWdpbmFsU3RyaW5nOiBzdHJpbmcgPSAnJ1xyXG4gICAgcHJpdmF0ZSBkb3Q6IHN0cmluZyA9ICcnXHJcblxyXG4gICAgcHJpdmF0ZSBlbGFwc2VkOiBudW1iZXIgPSAwXHJcbiAgICBwcml2YXRlIHJ1bm5pbmc6IGJvb2xlYW4gPSBmYWxzZVxyXG4gICAgcHJpdmF0ZSBwYXVzZTogYm9vbGVhbiA9IGZhbHNlXHJcblxyXG4gICAgcHJpdmF0ZSBnZXQgbGFiZWwoKSB7IHJldHVybiB0aGlzLl9sYWJlbCB8fCAodGhpcy5fbGFiZWwgPSB0aGlzLmdldENvbXBvbmVudChjYy5MYWJlbCkpIH1cclxuXHJcbiAgICBvbkVuYWJsZSgpIHtcclxuICAgICAgICB0aGlzLnBhdXNlID0gZmFsc2VcclxuICAgIH1cclxuXHJcbiAgICBvbkRpc2FibGUoKSB7XHJcbiAgICAgICAgdGhpcy5wYXVzZSA9IHRydWVcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgcGxheSh2YWw/OiBzdHJpbmcpIHtcclxuICAgICAgICB0aGlzLm9yaWdpbmFsU3RyaW5nID0gdGhpcy5sYWJlbC5zdHJpbmcgPSB2YWwgIT09IHVuZGVmaW5lZCA/IHZhbCA6IHRoaXMubGFiZWwuc3RyaW5nXHJcbiAgICAgICAgdGhpcy5kb3QgPSAnJ1xyXG4gICAgICAgIHRoaXMuZWxhcHNlZCA9IDBcclxuICAgICAgICB0aGlzLnJ1bm5pbmcgPSB0cnVlXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHN0b3AodmFsPzogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5ydW5uaW5nID0gZmFsc2VcclxuICAgICAgICBpZiAodmFsICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5sYWJlbC5zdHJpbmcgPSB2YWxcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLmxhYmVsLnN0cmluZyA9IHRoaXMub3JpZ2luYWxTdHJpbmcgPT09IHVuZGVmaW5lZCA/IHRoaXMubGFiZWwuc3RyaW5nIDogdGhpcy5vcmlnaW5hbFN0cmluZ1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLm9yaWdpbmFsU3RyaW5nID0gJydcclxuICAgICAgICB0aGlzLmRvdCA9ICcnXHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSB0aWNrKCkge1xyXG4gICAgICAgIGlmICh0aGlzLmRvdC5sZW5ndGggPCAzKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZG90ICs9ICcuJ1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuZG90ID0gJydcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5sYWJlbC5zdHJpbmcgPSB0aGlzLm9yaWdpbmFsU3RyaW5nICsgdGhpcy5kb3RcclxuICAgIH1cclxuXHJcbiAgICB1cGRhdGUoZHQ6IG51bWJlcikge1xyXG4gICAgICAgIGlmICghdGhpcy5ydW5uaW5nIHx8IHRoaXMucGF1c2UpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuZWxhcHNlZCArPSBkdFxyXG4gICAgICAgIGlmICh0aGlzLmVsYXBzZWQgPj0gdGhpcy5pbnRlcnZhbCkge1xyXG4gICAgICAgICAgICB0aGlzLmVsYXBzZWQgLT0gdGhpcy5pbnRlcnZhbFxyXG4gICAgICAgICAgICB0aGlzLnRpY2soKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuY2MuTGFiZWxXYWl0RG90ID0gTGFiZWxXYWl0RG90XHJcbiJdfQ==