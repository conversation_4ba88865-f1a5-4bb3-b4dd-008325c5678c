
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/message/MessageModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9fa01+fk1FKnpH0XNCgx2N/', 'MessageModel');
// app/script/model/message/MessageModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var MESSAGE_DURATION_TIME = 10; //持续时间（秒）
// const MESSAGE_DURATION_TIME = 1000000 //持续时间（秒）
var MessageModel = /** @class */ (function (_super) {
    __extends(MessageModel, _super);
    function MessageModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.messages = []; //消息列表
        return _this;
    }
    MessageModel.prototype.onCreate = function () {
    };
    MessageModel.prototype.getMessages = function () { return this.messages; };
    MessageModel.prototype.clean = function () {
        this.messages = [];
        this.emit(EventType_1.default.CLEAN_MESSAGE);
    };
    // 添加一条消息
    MessageModel.prototype.add = function (opts) {
        var _this = this;
        var message = this.messages.find(function (m) { return _this.equals(m, opts); });
        if (message) {
            message.time = MESSAGE_DURATION_TIME; //重复添加就直接将时间重置
        }
        else {
            message = this.messages.add({
                uid: ut.UID(),
                createTime: Date.now(),
                key: opts.key,
                params: opts.params || [],
                time: MESSAGE_DURATION_TIME,
                delay: (opts.delay || 0) * 1000,
                tag: opts.tag,
                check: opts.check,
            });
            if (!opts.delay) {
                this.emit(EventType_1.default.ADD_MESSAGE, message);
            }
        }
    };
    MessageModel.prototype.equals = function (a, b) {
        var params = b.params || [];
        if (a.key !== b.key || a.tag !== b.tag || a.params.length !== params.length) {
            return false;
        }
        for (var i = 0, l = a.params.length; i < l; i++) {
            if (a.params[i] !== params[i]) {
                return false;
            }
        }
        return true;
    };
    // 删除一个
    MessageModel.prototype.remove = function (uid) {
        this.messages.remove('uid', uid);
    };
    MessageModel.prototype.removeByTag = function (tag) {
        this.messages.remove('tag', tag);
    };
    // 强行停止延迟 立马播放
    MessageModel.prototype.delayEndByTag = function (tag) {
        var msg = this.messages.find(function (m) { return m.tag === tag; });
        if (msg && msg.delay > 0) {
            msg.delay = 0;
            this.emit(EventType_1.default.ADD_MESSAGE, msg);
        }
    };
    MessageModel.prototype.update = function (dt) {
        if (this.messages.length === 0) {
            return;
        }
        var now = Date.now();
        for (var i = this.messages.length - 1; i >= 0; i--) {
            var message = this.messages[i];
            if (message.delay > 0) {
                var time = now - message.createTime;
                if (time < message.delay) {
                }
                else if (!message.check || message.check()) {
                    message.time = Math.max(1, message.time - (time - message.delay) * 0.001);
                    message.delay = 0;
                    this.emit(EventType_1.default.ADD_MESSAGE, message);
                }
                else {
                    this.messages.splice(i, 1);
                }
                continue;
            }
            message.time -= dt;
            if (message.time <= 0) {
                this.messages.splice(i, 1);
                this.emit(EventType_1.default.DEL_MESSAGE, message);
            }
        }
    };
    MessageModel = __decorate([
        mc.addmodel('message')
    ], MessageModel);
    return MessageModel;
}(mc.BaseModel));
exports.default = MessageModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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