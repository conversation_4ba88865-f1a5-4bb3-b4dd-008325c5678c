
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendToggleContainer.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '540c3/gv5JOeK6P2kCRMUo6', 'ExtendToggleContainer');
// app/core/extend/ExtendToggleContainer.ts

/**
 * cc.ToggleContainer 扩展方法
 */
// 切换
cc.ToggleContainer.prototype.Swih = function (val) {
    var _this = this;
    var name = '', cb = null;
    if (typeof (val) === 'function') {
        cb = val;
    }
    else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val);
    }
    else {
        return [];
    }
    var arr = [];
    this.toggleItems.forEach(function (m) {
        var checked = cb ? !!cb(m) : (m.node.name === name);
        if (checked) {
            var event = _this.checkEvents.shift();
            m.isChecked = true;
            event && _this.checkEvents.push(event);
            arr.push(m);
        }
        else if (m.isChecked) {
            m.isChecked = false;
        }
    });
    return arr;
};
// 切换tabs
cc.ToggleContainer.prototype.Tabs = function (val) {
    var _this = this;
    var name = '', ret = null, cb = null;
    if (typeof (val) === 'function') {
        cb = val;
    }
    else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val);
    }
    else {
        return ret;
    }
    this.toggleItems.forEach(function (m) {
        var checked = cb ? !!cb(m) : (m.node.name === name);
        if (checked) {
            ret = m;
            var event = _this.checkEvents.shift();
            m.isChecked = true;
            if (event) {
                event.emit([m, 'true']);
                _this.checkEvents.push(event);
            }
        }
        else if (m.isChecked) {
            m.isChecked = false;
        }
    });
    return ret;
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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