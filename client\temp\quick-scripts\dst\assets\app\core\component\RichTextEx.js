
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/RichTextEx.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e28cbJZkY9KA6GXcTug495W', 'RichTextEx');
// app/core/component/RichTextEx.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, disallowMultiple = _a.disallowMultiple, executeInEditMode = _a.executeInEditMode;
var FONT_MAX_LENGTH = 10;
var COLOR_MAX_LENGTH = 6;
var IMAGE_MAX_LENGTH = 20;
var PREFAB_MAX_LENGTH = 16;
var RichTextEx = /** @class */ (function (_super) {
    __extends(RichTextEx, _super);
    function RichTextEx() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._string = '';
        _this._color = new cc.Color(255, 255, 255);
        _this._strokeWidth = 0;
        _this._strokeColor = new cc.Color(0, 0, 0);
        _this._fontName = 'Arial';
        _this._fontSize = 50;
        _this._rowDis = 10;
        _this.cvs = null;
        _this.ctx = null;
        _this.cellX = 0;
        _this.cellY = 0;
        _this.rowW = 0;
        _this.rowH = 0;
        _this.font = '';
        _this.bold = '';
        _this.style = '0';
        _this.viewX = 0;
        _this.viewY = 0;
        _this.viewWidth = 0;
        _this.viewHeight = 0;
        _this.pageNode = null;
        return _this;
    }
    Object.defineProperty(RichTextEx.prototype, "string", {
        get: function () { return this._string; },
        set: function (value) {
            this._string = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "color", {
        get: function () { return this._color; },
        set: function (value) {
            this._color = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "strokeWidth", {
        get: function () { return this._strokeWidth; },
        set: function (value) {
            this._strokeWidth = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "strokeColor", {
        get: function () { return this._strokeColor; },
        set: function (value) {
            this._strokeColor = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "fontName", {
        get: function () { return this._fontName; },
        set: function (value) {
            this._fontName = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "fontSize", {
        get: function () { return this._fontSize; },
        set: function (value) {
            this._fontSize = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(RichTextEx.prototype, "rowDis", {
        get: function () { return this._rowDis; },
        set: function (value) {
            this._rowDis = value;
            this.updateContent();
        },
        enumerable: false,
        configurable: true
    });
    RichTextEx.prototype.start = function () {
        this.updateContent();
        this.node.on(cc.Node.EventType.SIZE_CHANGED, this.updateContent, this);
        this.node.on(cc.Node.EventType.ANCHOR_CHANGED, this.updateContent, this);
    };
    RichTextEx.prototype.updateContent = function () {
        if (cc.isValid(this.node)) {
            this.node.removeAllChildren();
            this.node.destroyAllChildren();
        }
        this.cvs = null;
        this.ctx = null;
        var str = this.string;
        if (!str)
            return;
        this.viewX = -this.node.width * this.node.anchorX;
        this.viewY = this.node.height * (1 - this.node.anchorY);
        this.viewWidth = this.node.width;
        this.rowH = this.fontSize + this.rowDis;
        this.viewHeight = this.node.height - this.node.height % this.rowH;
        this.cellX = 0;
        this.cellY = this.rowH;
        this.rowW = 0;
        this.bold = '';
        this.style = '0';
        this.font = this.fontName;
        var charW = 0;
        var startID = 0;
        this.createPage();
        for (var i = 0, len = str.length; i <= len; ++i) {
            if (i === len) {
                this.drawToPage(str.substring(startID, len));
                if (startID < len || this.pageNode.childrenCount > 0) {
                    this.pageToNode();
                }
                else {
                    this.pageNode.removeFromParent();
                    this.pageNode.destroy();
                }
                break;
            }
            switch (str[i]) {
                case '\\':
                    if (i === len - 1) {
                        continue;
                    }
                    this.drawToPage(str.substring(startID, i));
                    switch (str[i + 1]) {
                        case 'n':
                            startID = ++i + 1;
                            this.addRow();
                            this.rowW = 0;
                            this.cellX = 0;
                            continue;
                        case 't':
                            startID = ++i + 1;
                            charW = this.fontSize;
                            if (this.rowW + charW > this.viewWidth) {
                                this.rowW = charW;
                                this.cellX = this.rowW;
                                this.addRow();
                            }
                            else {
                                this.rowW += charW;
                                this.cellX = this.rowW;
                            }
                            continue;
                        case 'b':
                            startID = ++i + 1;
                            this.cellX = this.rowW;
                            this.bold = 'bold';
                            this.ctx.font = this.bold + " " + this.fontSize + "px " + this.font;
                            continue;
                        case 'd':
                            startID = ++i + 1;
                            this.cellX = this.rowW;
                            this.style = 'd';
                            continue;
                        case 'u':
                            startID = ++i + 1;
                            this.cellX = this.rowW;
                            this.style = 'u';
                            continue;
                        case 'f':
                            {
                                var id = str.substring(i + 2, i + 4 + FONT_MAX_LENGTH).indexOf('\\f', 0);
                                if (id === -1)
                                    break;
                                id += i + 2;
                                this.font = str.substring(i + 2, id);
                                this.ctx.font = this.bold + " " + this.fontSize + "px " + this.font;
                                i = id + 1;
                                startID = id + 2;
                                this.cellX = this.rowW;
                            }
                            continue;
                        case 'c':
                            {
                                var id = str.substring(i + 2, i + 4 + COLOR_MAX_LENGTH).indexOf('\\c', 0);
                                if (id === -1)
                                    break;
                                if (id !== 3 && id !== 6)
                                    break;
                                id += i + 2;
                                this.ctx.fillStyle = '#' + str.substring(i + 2, id);
                                i = id + 1;
                                startID = id + 2;
                                this.cellX = this.rowW;
                            }
                            continue;
                        case '0':
                            startID = ++i + 1;
                            this.cellX = this.rowW;
                            this.style = '0';
                            this.font = this.fontName;
                            this.bold = '';
                            this.ctx.font = this.bold + " " + this.fontSize + "px " + this.font;
                            this.ctx.fillStyle = '#' + this.color.toHEX();
                            continue;
                        case 'i':
                            {
                                var id = str.substring(i + 2, i + 4 + IMAGE_MAX_LENGTH).indexOf('\\i', 0);
                                if (id === -1)
                                    break;
                                id += i + 2;
                                var node = new cc.Node('image');
                                node.anchorX = 0;
                                node.anchorY = 0;
                                if (CC_EDITOR) {
                                    node.width = this.rowH;
                                    node.height = this.rowH;
                                }
                                else {
                                    var spt = node.addComponent(cc.Sprite);
                                    // spt.spriteFrame = gi.load(str.substring(i + 2, id));
                                    node.scale = this.rowH / node.height;
                                    if (node.width * node.scale > this.viewWidth) {
                                        node.scale = this.viewWidth / node.width;
                                    }
                                }
                                var width = node.width * node.scale;
                                if (this.rowW + width > this.viewWidth) {
                                    node.x = 0;
                                    this.rowW = width;
                                    this.addRow();
                                }
                                else {
                                    node.x = this.rowW;
                                    this.rowW += width;
                                }
                                node.setParent(this.pageNode);
                                node.y = -this.cellY;
                                this.cellX = this.rowW;
                                i = id + 1;
                                startID = id + 2;
                            }
                            continue;
                        case 'p':
                            {
                                var id = str.substring(i + 2, i + 4 + PREFAB_MAX_LENGTH).indexOf('\\p', 0);
                                if (id === -1)
                                    break;
                                id += i + 2;
                                var node = null;
                                if (CC_EDITOR) {
                                    node = new cc.Node('prefab');
                                    node.width = this.rowH;
                                    node.height = this.rowH;
                                }
                                else {
                                    // node = cc.instantiate(gi.resource[str.substring(i + 2, id)]);
                                    node.scale = this.rowH / node.height;
                                    if (node.width * node.scale > this.viewWidth) {
                                        node.scale = this.viewWidth / node.width;
                                    }
                                }
                                var width = node.width * node.scale;
                                var height = node.height * node.scale;
                                if (this.rowW + width > this.viewWidth) {
                                    node.x = 0;
                                    this.rowW = width;
                                    this.addRow();
                                }
                                else {
                                    node.x = this.rowW;
                                    this.rowW += width;
                                }
                                node.setParent(this.pageNode);
                                node.x += width * node.anchorX;
                                node.y = height * node.anchorY - this.cellY;
                                this.cellX = this.rowW;
                                i = id + 1;
                                startID = id + 2;
                            }
                            continue;
                    }
                    break;
                case '\n':
                    this.drawToPage(str.substring(startID, i));
                    startID = i + 1;
                    this.addRow();
                    this.rowW = 0;
                    this.cellX = 0;
                    continue;
                case '\t':
                    this.drawToPage(str.substring(startID, i));
                    startID = i + 1;
                    charW = this.fontSize;
                    if (this.rowW + charW > this.viewWidth) {
                        this.rowW = charW;
                        this.cellX = this.rowW;
                        this.addRow();
                    }
                    else {
                        this.rowW += charW;
                        this.cellX = this.rowW;
                    }
                    continue;
            }
            charW = this.ctx.measureText(str[i]).width;
            if (this.rowW + charW > this.viewWidth) {
                this.drawToPage(str.substring(startID, i));
                startID = i;
                this.cellX = 0;
                this.rowW = charW;
                this.addRow();
            }
            else {
                this.rowW += charW;
            }
        }
        var scrollView = this.node.getComponent('ScrollView');
        scrollView && scrollView.updateLayout();
    };
    RichTextEx.prototype.createPage = function () {
        this.pageNode = new cc.Node("page" + this.node.childrenCount);
        this.pageNode['_objFlags'] |= cc.Object['Flags'].HideInHierarchy;
        this.pageNode['_objFlags'] |= cc.Object['Flags'].LockedInEditor;
        var font = this.ctx ? this.ctx.font : this.bold + " " + this.fontSize + "px " + this.font;
        var fillStyle = this.ctx ? this.ctx.fillStyle : '#' + this.color.toHEX();
        this.cvs = document.createElement('canvas');
        this.ctx = this.cvs.getContext('2d');
        this.cvs.width = this.viewWidth;
        this.cvs.height = this.viewHeight;
        this.ctx.font = font;
        this.ctx.fillStyle = fillStyle;
        this.ctx.lineWidth = this.strokeWidth;
        this.ctx.strokeStyle = '#' + this.strokeColor.toHEX();
    };
    RichTextEx.prototype.drawToPage = function (str) {
        if (str === '')
            return;
        var x = this.cellX;
        var y = this.cellY - this.fontSize * 0.15 - this.rowDis * 0.5;
        var strokeWidth = this.ctx.lineWidth;
        if (this.strokeWidth) {
            this.ctx.strokeText(str, x, y);
        }
        this.ctx.fillText(str, x, y);
        //显示外框，调试用
        // this.ctx.strokeRect(x, this.cellY - this.rowH, this.ctx.measureText(str).width, this.rowH);
        switch (this.style) {
            case 'd':
                this.ctx.lineWidth = Math.max(this.fontSize >> 3, this.strokeWidth);
                this.ctx.beginPath();
                this.ctx.moveTo(x, y - this.fontSize / 2 + this.ctx.lineWidth);
                this.ctx.lineTo(x + this.ctx.measureText(str).width, y - this.fontSize / 2 + this.ctx.lineWidth);
                this.ctx.stroke();
                this.ctx.lineWidth = strokeWidth;
                break;
            case 'u':
                this.ctx.lineWidth = Math.max(this.fontSize >> 3, this.strokeWidth);
                this.ctx.beginPath();
                this.ctx.moveTo(x, y + this.ctx.lineWidth);
                this.ctx.lineTo(x + this.ctx.measureText(str).width, y + this.ctx.lineWidth);
                this.ctx.stroke();
                this.ctx.lineWidth = strokeWidth;
                break;
        }
    };
    RichTextEx.prototype.pageToNode = function () {
        var texture = new cc.Texture2D();
        texture.initWithElement(this.cvs);
        this.pageNode.addComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture, cc.rect(0, 0, this.cvs.width, this.cvs.height));
        this.pageNode.width = this.viewWidth;
        this.pageNode.height = this.viewHeight;
        this.pageNode.anchorX = 0;
        this.pageNode.anchorY = 1;
        this.pageNode.x = this.viewX;
        this.pageNode.y = this.viewY - this.viewHeight * this.node.childrenCount;
        this.pageNode.setParent(this.node);
    };
    RichTextEx.prototype.addRow = function () {
        if (this.cellY + this.rowH > this.viewHeight) {
            this.pageToNode();
            this.createPage();
            this.cellY = this.rowH;
        }
        else {
            this.cellY += this.rowH;
        }
    };
    RichTextEx.prototype.onDestroy = function () {
        this.node.removeAllChildren();
        this.node.destroyAllChildren();
        this.node.targetOff(this);
    };
    __decorate([
        property
    ], RichTextEx.prototype, "_string", void 0);
    __decorate([
        property({ displayName: CC_DEV && '文本内容', multiline: true, tooltip: CC_DEV && '转译字符表：\n\\n 或 回车--->换行\n\\t 或 Tab --->插入2个空格\n\\b--->字体加粗，例如：\n“江\\b苏\\0省”——“苏”字加粗\n\\c--->换色，例如：\n“四\\cf00\\c川\\0省”——“川”变#f00\n\\d--->添加删除线，例如：\n“\\d陕西\\0省”——“陕西”会有删除线\n\\u--->添加下划线，例如：\n“安\\u徽省\\0”——“徽省”会有下划线\n注意：删除线和下划线只能2选1\n\\f--->设置字体，例如：\n“浙\\fKaiTi\\f江\\0省”——“江”变楷体\n常见字体:Arial、KaiTi、SimSun\n\\0--->恢复初始值\n搭配\\b、\\c、\\d、\\u、\\f使用\n\\i--->插入Atlas图集帧，例如：\n“海南\\iGame/Hero\\i省”——“海南”后面插入图集帧\n“Resource/Atlas/Game/Hero”\n\\p--->添加预制体，例如：\n“新疆\\pBoss\\p省”——“新疆”后面插入预制体“Resource/Boss”，设置PlayOnLoad可播动画' })
    ], RichTextEx.prototype, "string", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_color", void 0);
    __decorate([
        property({ displayName: CC_DEV && '文本颜色' })
    ], RichTextEx.prototype, "color", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_strokeWidth", void 0);
    __decorate([
        property({ min: 0, displayName: CC_DEV && '描边宽度' })
    ], RichTextEx.prototype, "strokeWidth", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_strokeColor", void 0);
    __decorate([
        property({ displayName: CC_DEV && '描边颜色', visible: function () { return this.strokeWidth > 0; } })
    ], RichTextEx.prototype, "strokeColor", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_fontName", void 0);
    __decorate([
        property({ displayName: CC_DEV && '字体名称' })
    ], RichTextEx.prototype, "fontName", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_fontSize", void 0);
    __decorate([
        property({ min: 4, displayName: CC_DEV && '字体大小' })
    ], RichTextEx.prototype, "fontSize", null);
    __decorate([
        property
    ], RichTextEx.prototype, "_rowDis", void 0);
    __decorate([
        property({ displayName: CC_DEV && '行间距' })
    ], RichTextEx.prototype, "rowDis", null);
    RichTextEx = __decorate([
        ccclass,
        executeInEditMode,
        disallowMultiple(),
        menu('自定义组件/RichTextEx')
    ], RichTextEx);
    return RichTextEx;
}(cc.Component));
exports.default = RichTextEx;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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