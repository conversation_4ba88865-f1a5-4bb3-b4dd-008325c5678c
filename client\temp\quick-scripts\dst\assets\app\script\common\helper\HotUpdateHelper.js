
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/HotUpdateHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5a524kUsydBRI5GGpjXqSSp', 'HotUpdateHelper');
// app/script/common/helper/HotUpdateHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hotUpdateHelper = void 0;
var CryptoHelper_1 = require("../crypto/CryptoHelper");
var EventType_1 = require("../event/EventType");
var GameHelper_1 = require("./GameHelper");
var TaHelper_1 = require("./TaHelper");
var STORE_KEY = '__@hotUpdateInfo';
/**
 * 热更新管理
 */
var HotUpdateHelper = /** @class */ (function () {
    function HotUpdateHelper() {
        this.debug = true;
        this._am = null;
        this._updating = false; //是否更新中
        this._hotStorage = ''; //热更新存档文件名字
        this.tempCostTime = 0;
        this.downloadManifestError = false;
        this.code7Count = 0;
    }
    HotUpdateHelper.prototype.printError = function (code, msg) {
        this.printLog('【Update Error】' + msg);
        var info = storageMgr.loadJson(STORE_KEY), time = 0;
        if (!!(info === null || info === void 0 ? void 0 : info.time)) {
            time = info.report ? info.time : Date.now() - info.time;
        }
        if (code === 7) {
            this.code7Count++;
        }
        else {
            TaHelper_1.taHelper.track('ta_hotUpdate', { cost_time: time, error_code: code, error_msg: msg, code7Count: this.code7Count });
        }
    };
    HotUpdateHelper.prototype.printLog = function (msg) {
        if (this.debug) {
            console.log(msg);
            eventCenter.emit(EventType_1.default.HOT_UPDATE_LOG, msg);
        }
    };
    HotUpdateHelper.prototype.setDebug = function (val) {
        this.debug = val;
    };
    Object.defineProperty(HotUpdateHelper.prototype, "searchPaths", {
        get: function () {
            return CC_JSB ? jsb.fileUtils.getSearchPaths() : [];
        },
        enumerable: false,
        configurable: true
    });
    HotUpdateHelper.prototype.setSearchPaths = function (paths) {
        if (CC_JSB) {
            localStorage.setItem('HotUpdateSearchPaths', JSON.stringify(paths));
            jsb.fileUtils.setSearchPaths(paths);
        }
    };
    HotUpdateHelper.prototype.addSearchPath = function (path) {
        var searchPaths = this.searchPaths;
        if (searchPaths.indexOf(path) === -1) {
            if (CC_JSB) {
                jsb.fileUtils.addSearchPath(path, true);
                localStorage.setItem('HotUpdateSearchPaths', JSON.stringify(this.searchPaths));
            }
        }
    };
    HotUpdateHelper.prototype.getWritablePath = function (path) {
        var root = CC_JSB ? jsb.fileUtils.getWritablePath() : './';
        return path ? root + path : root;
    };
    HotUpdateHelper.prototype.isFileExist = function (path) {
        return CC_JSB ? jsb.fileUtils.isFileExist(path) : false;
    };
    HotUpdateHelper.prototype.getStringFromFile = function (path) {
        return CC_JSB ? jsb.fileUtils.getStringFromFile(path) : '';
    };
    HotUpdateHelper.prototype.writeStringToFile = function (data, path) {
        if (CC_JSB) {
            var pathArry = path.split('/');
            var dirPath = '';
            for (var i = 0; i < pathArry.length - 1; i++) {
                dirPath += pathArry[i] + '/';
                if (!jsb.fileUtils.isDirectoryExist(dirPath)) {
                    var success = jsb.fileUtils.createDirectory(dirPath);
                    if (!success) {
                        return false;
                    }
                }
            }
            return jsb.fileUtils.writeStringToFile(data, path);
        }
    };
    HotUpdateHelper.prototype.getHotStorage = function () {
        if (!this._hotStorage) {
            this._hotStorage = mc.GameNameSpace + '-hot-update';
        }
        return this._hotStorage;
    };
    HotUpdateHelper.prototype.getTempHotStorage = function () {
        return mc.GameNameSpace + '-hot-update_temp';
    };
    // 根据path，uuid等找资源url, 更详细的参数参考源码
    HotUpdateHelper.prototype.getOriginManifestPath = function () {
        // @ts-ignore
        return cc.assetManager._transform({ bundle: 'manifest', path: 'project' }, { ext: '.manifest', __isNative__: true });
    };
    HotUpdateHelper.prototype.getCacheManifestPath = function () {
        return 'project.manifest';
    };
    HotUpdateHelper.prototype.getTempManifestPath = function () {
        return this.getWritablePath(this.getTempHotStorage() + '/project.manifest.temp');
    };
    HotUpdateHelper.prototype.versionCompareHandle = function (versionA, versionB) {
        this.printLog('JS Custom Version Compare: version A is ' + versionA + ', version B is ' + versionB);
        var vA = versionA.split('.');
        var vB = versionB.split('.');
        for (var i = 0; i < vA.length; ++i) {
            var a = parseInt(vA[i] || '0');
            var b = parseInt(vB[i] || '0');
            if (a === b) {
                continue;
            }
            else {
                return a - b;
            }
        }
        return vB.length > vA.length ? -1 : 0;
    };
    HotUpdateHelper.prototype.setVerifyCallback = function (path, asset) {
        // if (CC_JSB) {
        //     if (this._verfiyManifest === null) {
        //         this._verfiyManifest = this.verifyRemoteManifest()
        //         if (!this._verfiyManifest) {
        //             return false
        //         }
        //     } else if (this._verfiyManifest === false) {
        //         return false
        //     }
        // }
        var compressed = asset.compressed;
        var expectedMD5 = asset.md5;
        var relativePath = asset.path;
        if (compressed) {
            return true;
        }
        // @ts-ignore
        var file = jsb.fileUtils.getDataFromFile(path);
        var md5 = CryptoHelper_1.cryptoHelper.md5(file);
        if (expectedMD5 === md5) {
            this.printLog('Verification passed : ' + relativePath + ' (' + expectedMD5 + ')');
            return true;
        }
        else {
            this.printLog('Verification fail : ' + relativePath + ' (' + expectedMD5 + ')' + ' (' + md5 + ')');
            return false;
        }
    };
    HotUpdateHelper.prototype.verifyRemoteManifest = function () {
        if (CC_JSB) {
            var path = this.getTempManifestPath();
            var content = this.getStringFromFile(path);
            if (!content) {
                this.printLog('verifyRemoteManifest content is null');
                return false;
            }
            var json = JSON.parse(content);
            json = this.filterManifest(json);
            var sign = json.sign;
            if (!sign) {
                this.printLog('verifyRemoteManifest sign is null: ' + sign);
                return false;
            }
            delete json['sign'];
            var pass = CryptoHelper_1.cryptoHelper.pkVerify(JSON.stringify(json), sign);
            if (!pass) {
                this.printLog('verifyRemoteManifest fail : ' + sign);
            }
            return pass;
        }
        return true;
    };
    HotUpdateHelper.prototype.filterManifest = function (manifest) {
        if (CC_JSB) {
            var keys = ['version', 'packageUrl', 'remoteManifestUrl', 'assets', 'searchPaths', 'sign'];
            var filter = {};
            for (var key in manifest) {
                if (keys.indexOf(key) > -1) {
                    if (key === 'assets') {
                        var newAssets = {};
                        var assets = manifest[key];
                        for (var key_1 in assets) {
                            var _a = assets[key_1], md5 = _a.md5, size = _a.size;
                            newAssets[key_1] = { size: size, md5: md5 };
                        }
                        filter[key] = newAssets;
                    }
                    else {
                        filter[key] = manifest[key];
                    }
                }
            }
            return filter;
        }
        return {};
    };
    // 检测大版本更新
    HotUpdateHelper.prototype.versionCompareHandle2 = function (versionA, versionB) {
        var va = versionA.split('.'), vb = versionB.split('.');
        va.pop();
        vb.pop();
        return this.versionCompareHandle(va.join('.'), vb.join('.'));
    };
    HotUpdateHelper.prototype.release = function () {
        if (this._am) {
            this._am.setEventCallback(null);
            this._am = null;
        }
    };
    HotUpdateHelper.prototype.createAssetsManager = function (packageUrl) {
        var manifestUrl = this.getOriginManifestPath();
        var storagePath = this.getWritablePath(this.getHotStorage() + '/');
        this.printLog('hotupdate originManifest path:' + manifestUrl);
        this.printLog('hotupdate storage path:' + storagePath);
        // @ts-ignore
        var am = new jsb.AssetsManager(manifestUrl, storagePath);
        am.setVersionCompareHandle(this.versionCompareHandle.bind(this));
        am.setVerifyCallback(this.setVerifyCallback.bind(this));
        am.setEventCallback(this.updateCallback.bind(this));
        // @ts-ignore
        if (am.getState() === jsb.AssetsManager.State.UNINITED) {
            var str = JSON.stringify({
                'packageUrl': packageUrl,
                'remoteManifestUrl': packageUrl + 'project.manifest',
                'remoteVersionUrl': packageUrl + 'version.manifest',
                'version': '0.0.0.0',
                'assets': {},
                'searchPaths': [],
            });
            // @ts-ignore
            var manifest = new jsb.Manifest(str, storagePath);
            am.loadLocalManifest(manifest, storagePath);
            this.printLog('not manifest!!!');
        }
        else {
            this.printLog('find manifest!!!');
        }
        //尝试开放并发限制2->16
        if (cc.sys.os === cc.sys.OS_ANDROID) {
            // Some Android device may slow down the download process when concurrent tasks is too much.
            // The value may not be accurate, please do more test and find what's most suitable for your game.
            // @ts-ignore
            am.setMaxConcurrentTask(10);
        }
        return am;
    };
    HotUpdateHelper.prototype.updateCallback = function (event) {
        var failed = false, finish = false;
        var code = event.getEventCode();
        switch (code) {
            case jsb.EventAssetsManager.ERROR_UPDATING:
                this.printError(code, 'Asset update error: ' + event.getAssetId() + ', ' + event.getMessage());
                // failed = true
                break;
            case jsb.EventAssetsManager.ERROR_DECOMPRESS:
                this.printError(code, event.getMessage());
                // failed = true
                break;
            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:
                failed = true;
                this.printError(code, 'No local manifest file found, hot update skipped.');
                break;
            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:
            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:
                failed = true;
                this.printError(code, 'Fail to download manifest file, hot update skipped.');
                this.downloadManifestError = true;
                break;
            case jsb.EventAssetsManager.NEW_VERSION_FOUND:
                this.printLog('New version found.');
                break;
            case jsb.EventAssetsManager.UPDATE_FAILED:
                failed = true;
                this.printError(code, 'Update Fail.');
                break;
            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:
                this.printLog('Already up to date with the latest remote version.');
                this.setSearchPaths(this.searchPaths);
                finish = true;
                break;
            case jsb.EventAssetsManager.UPDATE_PROGRESSION:
                // this.printLog('Update Progression', event.getDownloadedBytes(), event.getTotalBytes(), event.getPercent())
                break;
            case jsb.EventAssetsManager.UPDATE_FINISHED:
                this.printLog('Update finished. ');
                this.onFinish(event);
                finish = true;
                break;
        }
        if (failed || finish) {
            this._am && this._am.setEventCallback(null);
            this._updating = false;
        }
        if (finish) {
            this.setSearchPaths(this.searchPaths);
        }
        // this.printLog('updateCallback failed=', failed, 'finish=', finish, 'event=', event.getEventCode())
        eventCenter.emit(EventType_1.default.HOT_UPDATE_EVENT, event, failed);
    };
    // 开始更新
    HotUpdateHelper.prototype.start = function (packageUrl, manifestUrl, version) {
        if (!cc.sys.isNative) {
            return;
        }
        else if (this._updating) {
            return this.printLog('HotUpdateHelper is updating, did NOT valid to run....');
        }
        else if (this._am) {
            this.release();
        }
        this.printLog('start packageUrl=' + packageUrl + ', manifestUrl=' + manifestUrl + ', version=' + version);
        this.onStart(version);
        this.updateManifestUrl(packageUrl, manifestUrl);
        this.setSearchPaths(this.searchPaths);
        this._am = this.createAssetsManager(packageUrl);
        if (!this._am) {
            return this.printError(-1, 'AssetsManager create failed!');
        }
        this._am.update();
        this._updating = true;
    };
    // 中断下载
    HotUpdateHelper.prototype.abort = function () {
        if (this._updating) {
            this._updating = false;
            this.release();
            this.printLog('中止下载...');
        }
    };
    // 重新下载
    HotUpdateHelper.prototype.redownload = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this._updating = true;
                        this._am.setEventCallback(this.updateCallback.bind(this));
                        if (!this.downloadManifestError) return [3 /*break*/, 2];
                        this.downloadManifestError = false;
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 1:
                        _a.sent();
                        this._am.update();
                        return [3 /*break*/, 3];
                    case 2:
                        this._am.downloadFailedAssets();
                        _a.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    // 获取当前版本
    HotUpdateHelper.prototype.getVersion = function () {
        if (!CC_JSB) {
            return '0.0.0.0';
        }
        try {
            var path = this.getWritablePath(this.getHotStorage() + "/project.manifest");
            var content = this.getStringFromFile(path);
            if (!content) {
                content = this.getStringFromFile(this.getOriginManifestPath());
            }
            if (content) {
                var json = JSON.parse(content);
                return json ? json.version : '0.0.0.0';
            }
            return '0.0.0.0';
        }
        catch (error) {
        }
        return '0.0.0.0';
    };
    // 清楚缓存
    HotUpdateHelper.prototype.cleanCache = function () {
        if (!cc.sys.isNative) {
            return this.printError(-1, 'the platform not support.');
        }
        var storagePath = this.getWritablePath(this.getHotStorage() + '/');
        if (!storagePath) {
            this.printError(-2, 'storagePath not exist');
        }
        else if (jsb.fileUtils.isDirectoryExist(storagePath)) {
            jsb.fileUtils.removeDirectory(storagePath);
        }
        else {
            this.printError(-3, "path:-->" + storagePath + " not exist");
        }
    };
    // 转换字节数
    HotUpdateHelper.prototype.convertBytesToString = function (bytes) {
        if (bytes >= 1073741824) {
            return (bytes / 1073741824).toFixed(2) + 'GB';
        }
        else if (bytes >= 1048576) {
            return (bytes / 1048576).toFixed(2) + 'MB';
        }
        else if (bytes >= 1024) {
            return (bytes / 1024).toFixed(2) + 'KB';
        }
        else {
            return bytes + 'B';
        }
    };
    // 更新manifest的远端地址，根据服务器动态替换
    HotUpdateHelper.prototype.updateManifestUrl = function (packageUrl, manifestUrl) {
        if (packageUrl) {
            this.updateManifestPath(packageUrl, manifestUrl, this.getCacheManifestPath());
            this.updateManifestPath(packageUrl, manifestUrl, this.getOriginManifestPath());
        }
    };
    HotUpdateHelper.prototype.updateManifestPath = function (packageUrl, manifestUrl, path) {
        var content = this.getStringFromFile(path);
        if (!content) {
            return;
        }
        var json = JSON.parse(content);
        this.printLog('updateManifest1 remoteManifestUrl=' + json.remoteManifestUrl);
        json.remoteManifestUrl = manifestUrl + 'project.manifest';
        this.printLog('updateManifest2 remoteManifestUrl=' + json.remoteManifestUrl);
        json.packageUrl = packageUrl;
        var success = this.writeStringToFile(JSON.stringify(json), path);
        if (!success) { //不可写，新建一个可写路径覆盖
            var storagePath = this.getWritablePath(this.getHotStorage() + '/');
            var filePath = storagePath + path;
            success = this.writeStringToFile(JSON.stringify(json), filePath);
            if (success) {
                this.addSearchPath(storagePath);
            }
        }
    };
    HotUpdateHelper.prototype.isUpdating = function () { return this._updating; };
    HotUpdateHelper.prototype.initStoreInfo = function (version) {
        var info = this.getStoreInfo();
        if (info.version !== version) {
            info = { curVer: GameHelper_1.gameHpr.getVersion(), version: version, time: 0 };
            this.setStoreInfo(info);
        }
        return info;
    };
    HotUpdateHelper.prototype.getStoreInfo = function () {
        var info = storageMgr.loadJson(STORE_KEY);
        if (!info) {
            info = { curVer: GameHelper_1.gameHpr.getVersion(), version: '0.0.0', time: 0 };
            this.setStoreInfo(info);
        }
        return info;
    };
    HotUpdateHelper.prototype.setStoreInfo = function (info) {
        storageMgr.saveJson(STORE_KEY, info);
    };
    HotUpdateHelper.prototype.update = function (dt) {
        if (!this.isUpdating()) {
            return;
        }
        this.tempCostTime += dt;
        if (this.tempCostTime > 0.5) {
            this.syncCostTime();
        }
    };
    HotUpdateHelper.prototype.syncCostTime = function () {
        var info = this.getStoreInfo();
        info.time += this.tempCostTime * 1000;
        this.tempCostTime = 0;
        this.setStoreInfo(info);
    };
    HotUpdateHelper.prototype.onStart = function (version) {
        var info = this.initStoreInfo(version);
        var uid = GameHelper_1.gameHpr.user.getUid(), distinctId = TaHelper_1.taHelper.getDistinctId();
        GameHelper_1.gameHpr.net.post({ url: GameHelper_1.gameHpr.getConfigByArea().hotUpdateStartUrl, data: { uid: uid, distinctId: distinctId, curVer: info.curVer, version: info.version } });
        this.printLog('hotUpdateStart curVer=' + info.curVer + ', version=' + info.version);
        // 开始热更新
        TaHelper_1.taHelper.track('ta_hotUpdate', { cost_time: 0, error_code: 10086 });
    };
    HotUpdateHelper.prototype.onFinish = function (event) {
        var info = this.getStoreInfo();
        info.finish = true;
        info.size = event.getTotalBytes();
        info.files = event.getTotalFiles();
        info.time += this.tempCostTime;
        this.setStoreInfo(info);
        // dhlog.report(DH_LOG_EVENT.UPDATE_SUCCESS, { total_time: info.time, total_size: event.getTotalBytes(), total_files: event.getTotalFiles(), new_version: info.version })
        this.checkReport();
        this.printLog('hotUpdateEnd curVer=' + info.curVer + ', version=' + info.version + ', time=' + info.time + ', size=' + info.size);
    };
    HotUpdateHelper.prototype.checkReport = function () {
        return __awaiter(this, void 0, void 0, function () {
            var info, uid, distinctId, res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        info = this.getStoreInfo();
                        if (!info.finish || info.report) {
                            return [2 /*return*/];
                        }
                        // 上报
                        TaHelper_1.taHelper.track('ta_hotUpdate', { cost_time: info.time, error_code: 200 });
                        uid = GameHelper_1.gameHpr.user.getUid(), distinctId = TaHelper_1.taHelper.getDistinctId();
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({
                                url: GameHelper_1.gameHpr.getConfigByArea().hotUpdateEndUrl,
                                data: {
                                    uid: uid, distinctId: distinctId,
                                    curVer: info.curVer,
                                    version: info.version,
                                    costTime: info.time,
                                    size: info.size,
                                    files: info.files,
                                    os: TaHelper_1.taHelper.getOsAndVersion()
                                }
                            })];
                    case 1:
                        res = _a.sent();
                        if ((res === null || res === void 0 ? void 0 : res.status) === 1) {
                            info.report = true;
                            this.setStoreInfo(info);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return HotUpdateHelper;
}());
exports.hotUpdateHelper = new HotUpdateHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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