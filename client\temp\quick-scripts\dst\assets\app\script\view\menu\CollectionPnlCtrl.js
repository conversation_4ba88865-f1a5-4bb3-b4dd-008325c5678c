
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/CollectionPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e559258g/5GZKNnTPy7gs78', 'CollectionPnlCtrl');
// app/script/view/menu/CollectionPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CollectionPnlCtrl = /** @class */ (function (_super) {
    __extends(CollectionPnlCtrl, _super);
    function CollectionPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.skinTabsTc_ = null; // path://root/pages_n/1/skin_tabs_tc_tce
        _this.socialTabsTc_ = null; // path://root/pages_n/2/social_tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'COLLECTION_TAB';
        _this.PKEY_SKIN_CHILD_TAB = 'COLLECTION_SKIN_CHILD_TAB';
        _this.PKEY_SOCOAL_CHILD_TAB = 'COLLECTION_SOCOAL_CHILD_TAB';
        _this.PKEY_HERO_SORT = 'COLLECTION_HERO_SORT';
        _this.PKEY_PAWN_SKIN_SORT = 'COLLECTION_PAWN_SKIN_SORT';
        _this.PKEY_CITY_SKIN_SORT = 'COLLECTION_CITY_SKIN_SORT';
        _this.PKEY_HEAD_ICON_SORT = 'COLLECTION_HEAD_ICON_SORT';
        _this.PKEY_CHAT_EMOJI_SORT = 'COLLECTION_CHAT_EMOJI_SORT';
        _this.PKEY_PLANT_BOTANY_SORT = 'COLLECTION_PLANT_BOTANY_SORT';
        _this.PKEY_HERO_FILTER = 'COLLECTION_HERO_FILTER';
        _this.PKEY_PAWN_SKIN_FILTER = 'COLLECTION_PAWN_SKIN_FILTER';
        _this.user = null;
        _this.curTab = 0;
        _this.curChildTab = 0;
        _this.skinList = [];
        _this.socialList = [];
        _this.portrayalList = [];
        _this.sortSelectNode = null;
        _this.filterSelectNode = null;
        _this.itemSkinsCount = 0;
        return _this;
    }
    CollectionPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_EXCHANGE_SKINS] = this.onUpdateSkins, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PORTRAYAL_INFO] = this.onUpdatePortrayalInfo, _b.enter = true, _b),
        ];
    };
    CollectionPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    CollectionPnlCtrl.prototype.onEnter = function (data) {
        this.tabsTc_.Tabs(this.user.getTempPreferenceMap(this.PKEY_TAB) || 0);
    };
    CollectionPnlCtrl.prototype.onRemove = function () {
    };
    CollectionPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    CollectionPnlCtrl.prototype.onClickTabs = function (event, data) {
        var _a, _b;
        !data && audioMgr.playSFX('click');
        var type = this.curTab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        this.sortSelectNode = node.Child('top/sort_select_be');
        if (type === 0) {
            this.filterSelectNode = node.Child('top/filter_be');
            this.selectFilterItem(this.filterSelectNode, (_a = this.user.getTempPreferenceMap(this.PKEY_HERO_FILTER)) !== null && _a !== void 0 ? _a : 0, true);
            this.selectSortItem(this.sortSelectNode, (_b = this.user.getTempPreferenceMap(this.PKEY_HERO_SORT)) !== null && _b !== void 0 ? _b : 1, true);
            this.updateHeroList(node);
        }
        else if (type === 1) {
            this.filterSelectNode = node.Child('top/other/filter_be');
            var tab = this.user.getTempPreferenceMap(this.PKEY_SKIN_CHILD_TAB) || 0;
            this.skinTabsTc_.Tabs(tab);
        }
        else if (type === 2) {
            var tab = this.user.getTempPreferenceMap(this.PKEY_SOCOAL_CHILD_TAB) || 0;
            this.socialTabsTc_.Tabs(tab);
        }
    };
    // path://root/pages_n/1/skin_tabs_tc_tce
    CollectionPnlCtrl.prototype.onClickSkinTabs = function (event, data) {
        var _a, _b, _c;
        !data && audioMgr.playSFX('click');
        var type = this.curChildTab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_SKIN_CHILD_TAB, type);
        var node = this.pagesNode_.Child(1);
        if (type === 0) {
            this.selectFilterItem(this.filterSelectNode, (_a = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER)) !== null && _a !== void 0 ? _a : 0, true);
            this.selectSortItem(this.sortSelectNode, (_b = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT)) !== null && _b !== void 0 ? _b : 1, true);
            this.updatePawnSkins(node);
        }
        else if (type === 1) {
            this.selectSortItem(this.sortSelectNode, (_c = this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT)) !== null && _c !== void 0 ? _c : 1, true);
            this.updateCitySkins(node);
        }
    };
    // path://root/pages_n/2/social_tabs_tc_tce
    CollectionPnlCtrl.prototype.onClickSocialTabs = function (event, data) {
        var _a, _b, _c;
        !data && audioMgr.playSFX('click');
        var type = this.curChildTab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_SOCOAL_CHILD_TAB, type);
        var node = this.pagesNode_.Child(2);
        if (type === 0) {
            this.selectSortItem(this.sortSelectNode, (_a = this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT)) !== null && _a !== void 0 ? _a : 1, true);
            this.updateHeadIcons(node);
        }
        else if (type === 1) {
            this.selectSortItem(this.sortSelectNode, (_b = this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT)) !== null && _b !== void 0 ? _b : 1, true);
            this.updateChatEmojis(node);
        }
        else if (type === 2) {
            this.selectSortItem(this.sortSelectNode, (_c = this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT)) !== null && _c !== void 0 ? _c : 1, true);
            this.updatePlantBotanys(node);
        }
    };
    // path://root/pages_n/0/top/sort_select_be
    CollectionPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        var node = this.sortSelectNode;
        ViewHelper_1.viewHelper.changePopupBoxList(node, true, true);
    };
    // path://root/pages_n/0/top/sort_select_be/select_mask_be
    CollectionPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false, true);
    };
    // path://root/pages_n/0/top/sort_select_be/mask/root/sort_items_nbe
    CollectionPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = this.sortSelectNode;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false, true);
        var type = Number(event.target.name);
        if (type !== this.getSelectSort()) {
            this.selectSortItem(node, type);
        }
    };
    // path://root/pages_n/0/top/filter_be
    CollectionPnlCtrl.prototype.onClickFilter = function (event, data) {
        var node = this.filterSelectNode;
        ViewHelper_1.viewHelper.changePopupBoxList(node, true, true);
    };
    // path://root/pages_n/0/top/filter_be/mask/root/filter_items_nbe
    CollectionPnlCtrl.prototype.onClickFilterItems = function (event, data) {
        var node = this.filterSelectNode;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false, true);
        var type = Number(event.target.name);
        if (type !== this.getSelectFilter()) {
            this.selectFilterItem(node, type);
        }
    };
    // path://root/pages_n/0/list/view/content/portrayal_be
    CollectionPnlCtrl.prototype.onClickPortrayal = function (event, _data) {
        audioMgr.playSFX('click');
        var idMap = {};
        this.user.getPortrayals().forEach(function (m) { return idMap[m.id] = m; });
        var list = [];
        this.portrayalList.forEach(function (m) {
            var portrayal = idMap[m.id];
            if (portrayal) {
                list.push(portrayal);
            }
        });
        var data = event.target.Data, info = list.find(function (m) { return m.id === data.id; });
        if (info) {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfo', info, list);
        }
        else {
            ViewHelper_1.viewHelper.showAlert('toast.dont_have');
        }
    };
    // path://root/pages_n/0/buttons/compo_be
    CollectionPnlCtrl.prototype.onClickCompo = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/CompDebris');
    };
    // path://root/pages_n/0/buttons/point_be
    CollectionPnlCtrl.prototype.onClickPoint = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/Pointsets');
    };
    // path://root/pages_n/1/list/view/content/skin_be
    CollectionPnlCtrl.prototype.onClickSkin = function (event, _data) {
        var _this = this;
        audioMgr.playSFX('click');
        var data = event.target.Data, type = data.type;
        ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: type, list: this.skinList, index: data.index, skins: data.skins }, function (ret) {
            if (!_this.isValid || !ret) {
                return;
            }
            else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                return;
            }
            if (type === 'pawn_skin') {
                _this.buyPawnSkin(ret.id, ret.cond);
            }
            else if (type === 'city_skin') {
                _this.buyCitySkin(ret.id, ret.cond);
            }
        });
    };
    // path://root/pages_n/2/list/view/content/social_be
    CollectionPnlCtrl.prototype.onClickSocial = function (event, _data) {
        var _this = this;
        audioMgr.playSFX('click');
        var data = event.target.Data, type = data.type;
        ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: type, list: this.socialList, index: data.index }, function (ret) {
            if (!_this.isValid || !ret) {
                return;
            }
            else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                return;
            }
            if (type === 'headicon') {
                _this.buyHeadIcon(ret.id, ret.cond);
            }
            else if (type === 'chat_emoji') {
                _this.buyChatEmoji(ret.id, ret.cond);
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    CollectionPnlCtrl.prototype.onUpdateSkins = function () {
        this.updatePawnSkins();
    };
    CollectionPnlCtrl.prototype.onUpdatePortrayalInfo = function () {
        this.updateHeroList();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 选择排序
    CollectionPnlCtrl.prototype.getSelectSort = function () {
        var selectSort = 1;
        if (this.curTab === 0) {
            selectSort = this.user.getTempPreferenceMap(this.PKEY_HERO_SORT);
        }
        else if (this.curTab === 1) {
            if (this.curChildTab === 0) {
                selectSort = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT);
            }
            else if (this.curChildTab === 1) {
                selectSort = this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT);
            }
        }
        else if (this.curTab === 2) {
            if (this.curChildTab === 0) {
                selectSort = this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT);
            }
            else if (this.curChildTab === 1) {
                selectSort = this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT);
            }
            else if (this.curChildTab === 2) {
                selectSort = this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT);
            }
        }
        return selectSort;
    };
    // 设置排序
    CollectionPnlCtrl.prototype.setSelectSort = function (val) {
        if (this.curTab === 0) {
            this.user.setTempPreferenceData(this.PKEY_HERO_SORT, val);
        }
        else if (this.curTab === 1) {
            if (this.curChildTab === 0) {
                this.user.setTempPreferenceData(this.PKEY_PAWN_SKIN_SORT, val);
            }
            else if (this.curChildTab === 1) {
                this.user.setTempPreferenceData(this.PKEY_CITY_SKIN_SORT, val);
            }
        }
        else if (this.curTab === 2) {
            if (this.curChildTab === 0) {
                this.user.setTempPreferenceData(this.PKEY_HEAD_ICON_SORT, val);
            }
            else if (this.curChildTab === 1) {
                this.user.setTempPreferenceData(this.PKEY_CHAT_EMOJI_SORT, val);
            }
            else if (this.curChildTab === 2) {
                this.user.setTempPreferenceData(this.PKEY_PLANT_BOTANY_SORT, val);
            }
        }
    };
    // 选择筛选
    CollectionPnlCtrl.prototype.getSelectFilter = function () {
        var selectFilter = 0;
        if (this.curTab === 0) {
            selectFilter = this.user.getTempPreferenceMap(this.PKEY_HERO_FILTER);
        }
        else if (this.curTab === 1) {
            selectFilter = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER);
        }
        return selectFilter;
    };
    // 设置筛选
    CollectionPnlCtrl.prototype.setSelectFilter = function (val) {
        if (this.curTab === 0) {
            this.user.setTempPreferenceData(this.PKEY_HERO_FILTER, val);
        }
        else if (this.curTab === 1) {
            this.user.setTempPreferenceData(this.PKEY_PAWN_SKIN_FILTER, val);
        }
    };
    // 英雄画像---------------------------------------------------------------------------------------------
    // 选择排序
    CollectionPnlCtrl.prototype.selectSortItem = function (node, type, init) {
        node.Data = type;
        this.setSelectSort(type);
        if (this.curTab === 0) {
            node.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type);
        }
        else if (this.curTab === 1) {
            node.Child('val', cc.Label).setLocaleKey(type !== 2 ? 'ui.portrayal_list_sort_' + type : 'ui.skin_series_sort');
        }
        else {
            node.Child('val', cc.Label).setLocaleKey(type ? 'ui.portrayal_list_sort_' + type : 'ui.default_sort');
        }
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#A18876' : '#C2B3A1');
            m.Child('select').active = select;
        });
        if (!init) {
            if (this.curTab === 0) {
                this.updateHeroList();
            }
            else if (this.curTab === 1) {
                if (this.curChildTab === 0) {
                    this.updatePawnSkins();
                }
                else {
                    this.updateCitySkins();
                }
            }
            else if (this.curTab === 2) {
                if (this.curChildTab === 0) {
                    this.updateHeadIcons();
                }
                else if (this.curChildTab === 1) {
                    this.updateChatEmojis();
                }
                else if (this.curChildTab === 2) {
                    this.updatePlantBotanys();
                }
            }
        }
    };
    CollectionPnlCtrl.prototype.selectFilterItem = function (node, type, init) {
        node.Data = type;
        this.setSelectFilter(type);
        if (this.curTab <= 1) { // 画像和皮肤才用
            node.Child('lay/val', cc.Label).setLocaleKey(type ? 'ui.pawn_type_' + type : 'ui.bazaar_filter_all');
            this.udpateCountVal(type);
        }
        else {
            node.Child('val', cc.Label).setLocaleKey(type ? 'ui.portrayal_list_sort_' + type : 'ui.bazaar_filter_all');
        }
        node.Child('mask/root/filter_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#A18876' : '#C2B3A1');
            m.Child('select').active = select;
        });
        if (!init) {
            if (this.curTab === 0) {
                this.updateHeroList();
            }
            else if (this.curTab === 1) {
                this.updatePawnSkins();
            }
        }
    };
    CollectionPnlCtrl.prototype.udpateCountVal = function (type) {
        var _a;
        type = (_a = type !== null && type !== void 0 ? type : this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER)) !== null && _a !== void 0 ? _a : 0;
        var node = this.filterSelectNode;
        var countNode = node.Child('lay/count');
        if (countNode.active = !type) {
            if (this.curTab === 0) {
                var datas = assetsMgr.getJson('portrayalBase').datas, cur = this.user.getPortrayals().filter(function (m) { return m.isUnlock(); }).length, max = datas.length;
                countNode.Component(cc.Label).string = "(" + cur + "/" + max + ")";
            }
            else if (this.curTab === 1) {
                var datas = assetsMgr.getJson('pawnSkin').datas, skins = this.user.getUnlockPawnSkinIds(), len = datas.length;
                countNode.Component(cc.Label).string = "(" + (skins.length + this.itemSkinsCount) + "/" + len + ")";
            }
        }
    };
    CollectionPnlCtrl.prototype.updateHeroList = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(0);
        var sv = node.Child('list', cc.ScrollView);
        var filter = this.getSelectFilter();
        this.portrayalList = assetsMgr.getJson('portrayalBase').datas.filter(function (m) { return !filter || filter === Math.floor(m.avatar_pawn / 100 % 10); });
        var map = {};
        this.user.getPortrayals().forEach(function (m) { return map[m.id] = m; });
        var sort = this.user.getTempPreferenceMap(this.PKEY_HERO_SORT);
        this.portrayalList.sort(function (a, b) {
            var objA = map[a.id], objB = map[b.id];
            var aw = objA ? 1 : 0, bw = objB ? 1 : 0;
            if (sort === 2) { //按残卷数量
                aw = ((objA === null || objA === void 0 ? void 0 : objA.debris) || 0);
                bw = ((objB === null || objB === void 0 ? void 0 : objB.debris) || 0);
            }
            aw = aw * 10 + ((objA === null || objA === void 0 ? void 0 : objA.isUnlock()) ? 1 : 0);
            bw = bw * 10 + ((objB === null || objB === void 0 ? void 0 : objB.isUnlock()) ? 1 : 0);
            if (sort === 0) { //按兵种类型
                aw = aw * 1000000 + (1000000 - a.id);
                bw = bw * 1000000 + (1000000 - b.id);
            }
            else if (sort === 1) { //按获取时间
                aw = aw * 10000 + ((objA === null || objA === void 0 ? void 0 : objA.index) || 0);
                bw = bw * 10000 + ((objB === null || objB === void 0 ? void 0 : objB.index) || 0);
            }
            aw = aw * 10 + ((objA === null || objA === void 0 ? void 0 : objA.isChosenOne()) ? 1 : 0);
            bw = bw * 10 + ((objB === null || objB === void 0 ? void 0 : objB.isChosenOne()) ? 1 : 0);
            return bw - aw;
        });
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(this.portrayalList.length, function (it, i) {
            var json = it.Data = _this.portrayalList[i], data = map[json.id];
            var shadowNode = it.Child('mask/icon'), iconNode = it.Child('mask/val');
            var pos = ut.stringToVec2(json.ui_offset);
            iconNode.Component(cc.Sprite).spriteFrame = null;
            shadowNode.Component(cc.Sprite).spriteFrame = null;
            iconNode.opacity = 255;
            var nameNode = it.Child('name');
            nameNode.Color('#C58461');
            if (!data) { // 没碎片 只显示剪影
                shadowNode.setPosition(pos);
                shadowNode.Color('#BCA092');
                ResHelper_1.resHelper.loadPortrayalImage(json.id, shadowNode, _this.key);
            }
            else if (data.isUnlock()) { // 已解锁
                nameNode.Color('#DB543B');
                iconNode.Color('#FFFFFF');
                iconNode.setPosition(pos);
                ResHelper_1.resHelper.loadPortrayalImage(json.id, iconNode, _this.key);
            }
            else { // 未解锁
                iconNode.opacity = 120;
                iconNode.Color('#EEE2CB');
                iconNode.setPosition(pos);
                ResHelper_1.resHelper.loadPortrayalImage(json.id, iconNode, _this.key);
            }
            nameNode.Child('val').setLocaleKey('portrayalText.name_' + json.id);
            if (it.Child('debris_count').active = (data === null || data === void 0 ? void 0 : data.debris) > 0) {
                ViewHelper_1.viewHelper.updatePortrayalDebrisCount(it, data.debris);
            }
            it.Child('di', cc.MultiFrame).setFrame(!!(data === null || data === void 0 ? void 0 : data.getUIDiType()));
            it.Child('chosen').active = !!(data === null || data === void 0 ? void 0 : data.isChosenOne());
        });
    };
    // 皮肤---------------------------------------------------------------------------------------------
    // 显示士兵皮肤
    CollectionPnlCtrl.prototype.updatePawnSkins = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var sv, err, serverArea, canBuySkinMap, pawnSkinMap, itemSkinMap, list, filter, sort;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        node = node || this.pagesNode_.Child(1);
                        node.Child('top/other').Swih('filter_be');
                        sv = node.Child('list', cc.ScrollView);
                        return [4 /*yield*/, this.user.reqSkinItemList()];
                    case 1:
                        err = _a.sent();
                        if (!this.isValid) {
                        }
                        else if (err) {
                            sv.List(0);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test', canBuySkinMap = {};
                        this.user.getCanBuyPawnSkins(serverArea).forEach(function (m) { return canBuySkinMap[m.id] = m; });
                        pawnSkinMap = {};
                        this.user.getUnlockPawnSkinIds().forEach(function (m, i) { return pawnSkinMap[m] = { data: m, index: i }; });
                        itemSkinMap = {};
                        this.itemSkinsCount = 0;
                        this.user.getSkinItemList().forEach(function (m) {
                            var item = itemSkinMap[m.id];
                            if (!item) {
                                itemSkinMap[m.id] = item = {};
                                _this.itemSkinsCount++;
                            }
                            var sta = 0; //正常
                            if (m.state > 0) {
                                sta = 1; //锁定
                            }
                            else if (m.state < 0) {
                                sta = 2; //封禁
                            }
                            var list = item[sta];
                            if (!list) {
                                item[sta] = list = [];
                            }
                            list.push(m);
                            if (sta === 1) {
                                list.sort(function (a, b) { return a.state - b.state; });
                            }
                        });
                        this.udpateCountVal(); // 刷新下数量
                        list = [];
                        filter = this.getSelectFilter();
                        sort = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT);
                        assetsMgr.getJson('pawnSkin').datas.forEach(function (m, i) {
                            if (!filter || filter === Math.floor(m.pawn_id / 100) % 10) {
                                var item = itemSkinMap[m.id];
                                if (!ut.isEmptyObject(item)) {
                                    for (var k in item) {
                                        var skin = item[k][0], index = Number(k), weight = 0;
                                        if (sort === 0) { //按兵种类型
                                            weight = 10000000 - m.id + 1000;
                                        }
                                        else if (sort === 1) { // 按获取时间
                                            weight = index === 0 ? 10 : index > 0 ? 9 : 8;
                                            weight += skin.id;
                                        }
                                        list.push({ json: m, itemSkins: item[k], state: index, weight: weight });
                                    }
                                }
                                else {
                                    var pawnSkin = pawnSkinMap[m.id], weight = 0;
                                    if (sort === 0) { //按兵种类型
                                        weight = 10000000 - m.id + 1 + (canBuySkinMap[m.id] ? 1 : 0);
                                    }
                                    else if (sort === 1) { // 按获取时间
                                        var index = pawnSkin ? (pawnSkin.index + 1) : 0;
                                        weight = index * 10 + (canBuySkinMap[m.id] ? 1 : 0);
                                    }
                                    list.push({ json: m, pawnSkin: pawnSkin === null || pawnSkin === void 0 ? void 0 : pawnSkin.data, weight: weight });
                                }
                            }
                        });
                        list.sort(function (a, b) { return b.weight - a.weight; });
                        this.skinList = list;
                        sv.stopAutoScroll();
                        sv.content.y = 0;
                        sv.List(list.length, function (it, i) {
                            var data = list[i], json = data.json, id = json.id;
                            var node = it.Swih('pawn')[0];
                            node.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang(ResHelper_1.resHelper.getPawnName(json.pawn_id)), 5));
                            ResHelper_1.resHelper.loadPawnHeadIcon(id, node.Child('icon/val'), _this.key, false);
                            var itemSkins = data.itemSkins, itemSkin = itemSkins ? itemSkins[0] : null, state = itemSkin === null || itemSkin === void 0 ? void 0 : itemSkin.state;
                            it.Data = { type: 'pawn_skin', json: json, index: i, skins: data.itemSkins };
                            var isItemSkin = !!itemSkin;
                            node.Child('count', cc.Label).string = isItemSkin ? 'x' + itemSkins.length : '';
                            node.Child('clock').active = isItemSkin ? state > 0 : false;
                            var isBan = isItemSkin && state < 0, icon = node.Child('icon/val');
                            icon.Color(isBan ? '#A29D95' : '#FFFFFF');
                            icon.opacity = (data.pawnSkin || isItemSkin) ? (isItemSkin && state < 0 ? 140 : 255) : 140;
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 显示主城皮肤
    CollectionPnlCtrl.prototype.updateCitySkins = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var datas, skins, len, countNode, sort, sv;
            var _this = this;
            return __generator(this, function (_a) {
                node = node || this.pagesNode_.Child(1);
                datas = assetsMgr.getJson('citySkin').datas.filter(function (m) { return m.cond !== 0; }), skins = this.user.getUnlockCitySkinIds(), len = datas.length;
                countNode = node.Child('top/other').Swih('count')[0];
                countNode.Child('bg/cur', cc.Label).string = skins.length + '';
                countNode.Child('bg/max', cc.Label).string = '/' + len;
                sort = this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT);
                if (sort === 1) { // 按获取时间
                    datas.sort(function (a, b) {
                        return skins.findIndex(function (m) { return m === b.id; }) - skins.findIndex(function (m) { return m === a.id; });
                    });
                }
                this.skinList = datas;
                sv = node.Child('list', cc.ScrollView);
                sv.stopAutoScroll();
                sv.content.y = 0;
                sv.List(len, function (it, i) {
                    var data = datas[i];
                    var node = it.Swih('city')[0];
                    it.Data = { type: 'city_skin', json: data, index: i };
                    node.Child('icon').opacity = skins.has(data.id) ? 255 : 140;
                    ResHelper_1.resHelper.loadCityIcon(data.id, node.Child('icon/val'), _this.key);
                });
                return [2 /*return*/];
            });
        });
    };
    CollectionPnlCtrl.prototype.buyPawnSkin = function (id, cond) {
        return __awaiter(this, void 0, void 0, function () {
            var err, isExchange;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        err = '', isExchange = cond === 3;
                        if (!isExchange) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.user.rsExchangePawnSkin(id)];
                    case 1:
                        err = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, this.user.buyPawnSkin(id)];
                    case 3:
                        err = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.skinTabsTc_.Tabs(this.curChildTab);
                            if (isExchange) {
                                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                            }
                            else {
                                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionPnlCtrl.prototype.buyCitySkin = function (id, cond) {
        return __awaiter(this, void 0, void 0, function () {
            var err, isExchange;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        err = '', isExchange = cond === 3;
                        if (!isExchange) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.user.rsExchangeCitySkin(id)];
                    case 1:
                        err = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, this.user.buyCitySkin(id)];
                    case 3:
                        err = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.skinTabsTc_.Tabs(this.curChildTab);
                            if (isExchange) {
                                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                            }
                            else {
                                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 社交---------------------------------------------------------------------------------------------
    // 显示头像
    CollectionPnlCtrl.prototype.updateHeadIcons = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(2);
        // 排除已停止售卖的 platform=none
        var datas = assetsMgr.getJson('headIcon').datas.filter(function (m) { return m.platform !== 'none'; }), heads = this.user.getUnlockHeadIcons(), len = datas.length;
        var countNode = node.Child('top/count');
        countNode.Child('cur', cc.Label).string = heads.length + '';
        countNode.Child('max', cc.Label).string = '/' + len;
        var sort = this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT);
        if (sort === 1) { // 按获取时间
            datas.sort(function (a, b) {
                return heads.findIndex(function (m) { return m === b.icon; }) - heads.findIndex(function (m) { return m === a.icon; });
            });
        }
        this.socialList = datas;
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(len, function (it, i) {
            var data = datas[i];
            it.Data = { type: 'headicon', json: data, index: i };
            it.Child('bg').active = it.Child('count').active = false;
            it.Child('icon').opacity = heads.has(data.icon) ? 255 : 140;
            var icon = it.Child('icon/val');
            icon.width !== 96 && icon.setContentSize(96, 96);
            ResHelper_1.resHelper.loadPlayerHead(icon, data.icon, _this.key, true);
        });
    };
    // 显示emoji表情
    CollectionPnlCtrl.prototype.updateChatEmojis = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(2);
        // 排除 地图表情 type=3
        var datas = assetsMgr.getJson('chatEmoji').datas.filter(function (m) { return m.type !== 3 && m.cond !== 0; }), emojis = this.user.getUnlockChatEmojiIds(), len = datas.length;
        var countNode = node.Child('top/count');
        countNode.Child('cur', cc.Label).string = emojis.length + '';
        countNode.Child('max', cc.Label).string = '/' + len;
        var sort = this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT);
        if (sort === 1) { // 按获取时间
            datas.sort(function (a, b) {
                return emojis.findIndex(function (m) { return m === b.id; }) - emojis.findIndex(function (m) { return m === a.id; });
            });
        }
        this.socialList = datas;
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(len, function (it, i) {
            var data = datas[i];
            it.Data = { type: 'chat_emoji', json: data, index: i };
            it.Child('bg').active = it.Child('count').active = false;
            it.Child('icon').opacity = emojis.has(data.id) ? 255 : 140;
            var icon = it.Child('icon/val');
            icon.removeAllChildren();
            ResHelper_1.resHelper.loadEmojiIcon(data.id, icon, _this.key);
        });
    };
    // 显示植物表情
    CollectionPnlCtrl.prototype.updatePlantBotanys = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(2);
        var datas = assetsMgr.getJson('botany').datas.slice(), botanys = this.user.getUnlockBotanys(), len = datas.length;
        var countNode = node.Child('top/count');
        countNode.Child('cur', cc.Label).string = botanys.length + '';
        countNode.Child('max', cc.Label).string = '/' + len;
        var sort = this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT);
        if (sort === 1) { // 按获取时间
            datas.sort(function (a, b) {
                return botanys.findIndex(function (m) { return m.id === b.id; }) - botanys.findIndex(function (m) { return m.id === a.id; });
            });
        }
        this.socialList = datas;
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(len, function (it, i) {
            var data = datas[i];
            it.Data = { type: 'plant_emoji', json: data, index: i };
            it.Child('bg').active = it.Child('count').active = true;
            var has = botanys.has(data.id);
            it.Child('icon').opacity = has ? 255 : 140;
            it.Child('count', cc.Label).string = has ? 'x' + data.count : '';
            var icon = it.Child('icon/val');
            icon.removeAllChildren();
            ResHelper_1.resHelper.loadGiftIcon(data.id, icon, _this.key);
        });
    };
    CollectionPnlCtrl.prototype.buyHeadIcon = function (id, cond) {
        return __awaiter(this, void 0, void 0, function () {
            var err, isExchange;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        err = '', isExchange = cond === 3;
                        if (!isExchange) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.user.rsExchangeHeadicon(id)];
                    case 1:
                        err = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, this.user.buyHeadIcon(id)];
                    case 3:
                        err = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.socialTabsTc_.Tabs(this.curChildTab);
                            if (isExchange) {
                                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                            }
                            else {
                                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionPnlCtrl.prototype.buyChatEmoji = function (id, cond) {
        return __awaiter(this, void 0, void 0, function () {
            var err, isExchange;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        err = '', isExchange = cond === 3;
                        if (!isExchange) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.user.rsExchangeChatEmoji(id)];
                    case 1:
                        err = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, this.user.buyChatEmoji(id)];
                    case 3:
                        err = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.socialTabsTc_.Tabs(this.curChildTab);
                            if (isExchange) {
                                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                            }
                            else {
                                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionPnlCtrl = __decorate([
        ccclass
    ], CollectionPnlCtrl);
    return CollectionPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CollectionPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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