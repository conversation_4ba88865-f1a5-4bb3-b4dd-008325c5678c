
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/SelectAlliJobPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8a6a6PuyuBIMKRTsZMF7IeX', 'SelectAlliJobPnlCtrl');
// app/script/view/build/SelectAlliJobPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectAlliJobPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectAlliJobPnlCtrl, _super);
    function SelectAlliJobPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentTc_ = null; // path://root/content_tc_tce
        _this.descNode_ = null; // path://root/lay/desc_n
        _this.jobCountNode_ = null; // path://root/lay/job_count_n
        //@end
        _this.data = null;
        _this.jobCount = 0;
        return _this;
    }
    SelectAlliJobPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectAlliJobPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectAlliJobPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        this.contentTc_.Tabs(data.job || Enums_1.AllianceJobType.MEMBER);
    };
    SelectAlliJobPnlCtrl.prototype.onRemove = function () {
    };
    SelectAlliJobPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_tc_tce
    SelectAlliJobPnlCtrl.prototype.onClickContent = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.showJobDesc(type);
    };
    // path://root/buttons_nbe
    SelectAlliJobPnlCtrl.prototype.onClickButtons = function (event, data) {
        var name = event.target.name;
        if (name === 'ok') {
            this.do();
        }
        else if (name === 'cancel') {
            this.hide();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectAlliJobPnlCtrl.prototype.showJobDesc = function (job) {
        this.descNode_.Items(Constant_1.ALLI_JOB_DESC[job] || [0], function (it, index, i) {
            if (index > 0) {
                it.setLocaleKey('ui.text', (i + 1) + '. ' + assetsMgr.lang('ui.alli_authority_desc_' + index));
            }
            else {
                it.setLocaleKey('ui.alli_authority_desc_0');
            }
        });
        var count = this.jobCount = Math.max(0, (Constant_1.ALLI_JOB_COUNT[job] || 1) - GameHelper_1.gameHpr.alliance.getMembers().filter(function (m) { return m.job === job; }).length);
        if (job !== Enums_1.AllianceJobType.MEMBER) {
            this.jobCountNode_.setLocaleKey('ui.alli_job_count_desc', count > 0 ? "<color=#3F332F>" + count + "</c>" : "<color=#D7634D>0</c>");
        }
        else {
            this.jobCountNode_.setLocaleKey('');
        }
    };
    SelectAlliJobPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var job, err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        job = Number(this.contentTc_.toggleItems.find(function (m) { return m.isChecked; }).node.name);
                        if (this.data.job === job) {
                            return [2 /*return*/, this.hide()];
                        }
                        else if (this.jobCount <= 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ALLI_JOB_FULL)];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.alliance.changeJob(this.data.uid, job)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SelectAlliJobPnlCtrl = __decorate([
        ccclass
    ], SelectAlliJobPnlCtrl);
    return SelectAlliJobPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectAlliJobPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxTZWxlY3RBbGxpSm9iUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyREFBK0U7QUFFL0UscURBQW9EO0FBQ3BELHFEQUE4RDtBQUM5RCw2REFBeUQ7QUFDekQsNkRBQTREO0FBRXBELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQWtELHdDQUFjO0lBQWhFO1FBQUEscUVBbUZDO1FBakZHLDBCQUEwQjtRQUNsQixnQkFBVSxHQUF1QixJQUFJLENBQUEsQ0FBQyw2QkFBNkI7UUFDbkUsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLHlCQUF5QjtRQUNuRCxtQkFBYSxHQUFZLElBQUksQ0FBQSxDQUFDLDhCQUE4QjtRQUNwRSxNQUFNO1FBRUUsVUFBSSxHQUFtQixJQUFJLENBQUE7UUFDM0IsY0FBUSxHQUFXLENBQUMsQ0FBQTs7SUEwRWhDLENBQUM7SUF4RVUsOENBQWUsR0FBdEI7UUFDSSxPQUFPLEVBQUUsQ0FBQTtJQUNiLENBQUM7SUFFWSx1Q0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSxzQ0FBTyxHQUFkLFVBQWUsSUFBb0I7UUFDL0IsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsSUFBSSx1QkFBZSxDQUFDLE1BQU0sQ0FBQyxDQUFBO0lBQzVELENBQUM7SUFFTSx1Q0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLHNDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiw2QkFBNkI7SUFDN0IsNkNBQWMsR0FBZCxVQUFlLEtBQWdCLEVBQUUsSUFBWTtRQUN6QyxDQUFDLElBQUksSUFBSSxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ2xDLElBQU0sSUFBSSxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3BDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUE7SUFDMUIsQ0FBQztJQUVELDBCQUEwQjtJQUMxQiw2Q0FBYyxHQUFkLFVBQWUsS0FBMEIsRUFBRSxJQUFZO1FBQ25ELElBQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQzlCLElBQUksSUFBSSxLQUFLLElBQUksRUFBRTtZQUNmLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQTtTQUNaO2FBQU0sSUFBSSxJQUFJLEtBQUssUUFBUSxFQUFFO1lBQzFCLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtTQUNkO0lBQ0wsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFakgsaUhBQWlIO0lBRXpHLDBDQUFXLEdBQW5CLFVBQW9CLEdBQVc7UUFDM0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsd0JBQWEsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLFVBQUMsRUFBRSxFQUFFLEtBQWEsRUFBRSxDQUFTO1lBQ3pFLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtnQkFDWCxFQUFFLENBQUMsWUFBWSxDQUFDLFNBQVMsRUFBRSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyx5QkFBeUIsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFBO2FBQ2pHO2lCQUFNO2dCQUNILEVBQUUsQ0FBQyxZQUFZLENBQUMsMEJBQTBCLENBQUMsQ0FBQTthQUM5QztRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLHlCQUFjLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsb0JBQU8sQ0FBQyxRQUFRLENBQUMsVUFBVSxFQUFFLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxHQUFHLEVBQWIsQ0FBYSxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDdkksSUFBSSxHQUFHLEtBQUssdUJBQWUsQ0FBQyxNQUFNLEVBQUU7WUFDaEMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxZQUFZLENBQUMsd0JBQXdCLEVBQUUsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsb0JBQWtCLEtBQUssU0FBTSxDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFBO1NBQ2hJO2FBQU07WUFDSCxJQUFJLENBQUMsYUFBYSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsQ0FBQTtTQUN0QztJQUNMLENBQUM7SUFFYSxpQ0FBRSxHQUFoQjs7Ozs7O3dCQUNVLEdBQUcsR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFNBQVMsRUFBWCxDQUFXLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7d0JBQ2hGLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEtBQUssR0FBRyxFQUFFOzRCQUN2QixzQkFBTyxJQUFJLENBQUMsSUFBSSxFQUFFLEVBQUE7eUJBQ3JCOzZCQUFNLElBQUksSUFBSSxDQUFDLFFBQVEsSUFBSSxDQUFDLEVBQUU7NEJBQzNCLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLGFBQUssQ0FBQyxhQUFhLENBQUMsRUFBQTt5QkFDbkQ7d0JBQ1cscUJBQU0sb0JBQU8sQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxFQUFBOzt3QkFBMUQsR0FBRyxHQUFHLFNBQW9EO3dCQUNoRSxJQUFJLEdBQUcsRUFBRTs0QkFDTCxzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBQTt5QkFDbkM7NkJBQU0sSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFOzRCQUNyQixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7eUJBQ2Q7Ozs7O0tBQ0o7SUFsRmdCLG9CQUFvQjtRQUR4QyxPQUFPO09BQ2Esb0JBQW9CLENBbUZ4QztJQUFELDJCQUFDO0NBbkZELEFBbUZDLENBbkZpRCxFQUFFLENBQUMsV0FBVyxHQW1GL0Q7a0JBbkZvQixvQkFBb0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBTExJX0pPQl9DT1VOVCwgQUxMSV9KT0JfREVTQyB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcbmltcG9ydCB7IEFsbGlNZW1iZXJJbmZvIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9EYXRhVHlwZVwiO1xuaW1wb3J0IHsgZWNvZGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VDb2RlXCI7XG5pbXBvcnQgeyBBbGxpYW5jZUpvYlR5cGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VudW1zXCI7XG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcblxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5AY2NjbGFzc1xuZXhwb3J0IGRlZmF1bHQgY2xhc3MgU2VsZWN0QWxsaUpvYlBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgY29udGVudFRjXzogY2MuVG9nZ2xlQ29udGFpbmVyID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9jb250ZW50X3RjX3RjZVxuICAgIHByaXZhdGUgZGVzY05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9sYXkvZGVzY19uXG4gICAgcHJpdmF0ZSBqb2JDb3VudE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9sYXkvam9iX2NvdW50X25cbiAgICAvL0BlbmRcblxuICAgIHByaXZhdGUgZGF0YTogQWxsaU1lbWJlckluZm8gPSBudWxsXG4gICAgcHJpdmF0ZSBqb2JDb3VudDogbnVtYmVyID0gMFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IEFsbGlNZW1iZXJJbmZvKSB7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGFcbiAgICAgICAgdGhpcy5jb250ZW50VGNfLlRhYnMoZGF0YS5qb2IgfHwgQWxsaWFuY2VKb2JUeXBlLk1FTUJFUilcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L2NvbnRlbnRfdGNfdGNlXG4gICAgb25DbGlja0NvbnRlbnQoZXZlbnQ6IGNjLlRvZ2dsZSwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgICFkYXRhICYmIGF1ZGlvTWdyLnBsYXlTRlgoJ2NsaWNrJylcbiAgICAgICAgY29uc3QgdHlwZSA9IE51bWJlcihldmVudC5ub2RlLm5hbWUpXG4gICAgICAgIHRoaXMuc2hvd0pvYkRlc2ModHlwZSlcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9idXR0b25zX25iZVxuICAgIG9uQ2xpY2tCdXR0b25zKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgbmFtZSA9IGV2ZW50LnRhcmdldC5uYW1lXG4gICAgICAgIGlmIChuYW1lID09PSAnb2snKSB7XG4gICAgICAgICAgICB0aGlzLmRvKClcbiAgICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAnY2FuY2VsJykge1xuICAgICAgICAgICAgdGhpcy5oaWRlKClcbiAgICAgICAgfVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgc2hvd0pvYkRlc2Moam9iOiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5kZXNjTm9kZV8uSXRlbXMoQUxMSV9KT0JfREVTQ1tqb2JdIHx8IFswXSwgKGl0LCBpbmRleDogbnVtYmVyLCBpOiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgIGlmIChpbmRleCA+IDApIHtcbiAgICAgICAgICAgICAgICBpdC5zZXRMb2NhbGVLZXkoJ3VpLnRleHQnLCAoaSArIDEpICsgJy4gJyArIGFzc2V0c01nci5sYW5nKCd1aS5hbGxpX2F1dGhvcml0eV9kZXNjXycgKyBpbmRleCkpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGl0LnNldExvY2FsZUtleSgndWkuYWxsaV9hdXRob3JpdHlfZGVzY18wJylcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgY29uc3QgY291bnQgPSB0aGlzLmpvYkNvdW50ID0gTWF0aC5tYXgoMCwgKEFMTElfSk9CX0NPVU5UW2pvYl0gfHwgMSkgLSBnYW1lSHByLmFsbGlhbmNlLmdldE1lbWJlcnMoKS5maWx0ZXIobSA9PiBtLmpvYiA9PT0gam9iKS5sZW5ndGgpXG4gICAgICAgIGlmIChqb2IgIT09IEFsbGlhbmNlSm9iVHlwZS5NRU1CRVIpIHtcbiAgICAgICAgICAgIHRoaXMuam9iQ291bnROb2RlXy5zZXRMb2NhbGVLZXkoJ3VpLmFsbGlfam9iX2NvdW50X2Rlc2MnLCBjb3VudCA+IDAgPyBgPGNvbG9yPSMzRjMzMkY+JHtjb3VudH08L2M+YCA6IGA8Y29sb3I9I0Q3NjM0RD4wPC9jPmApXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmpvYkNvdW50Tm9kZV8uc2V0TG9jYWxlS2V5KCcnKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHJpdmF0ZSBhc3luYyBkbygpIHtcbiAgICAgICAgY29uc3Qgam9iID0gTnVtYmVyKHRoaXMuY29udGVudFRjXy50b2dnbGVJdGVtcy5maW5kKG0gPT4gbS5pc0NoZWNrZWQpLm5vZGUubmFtZSlcbiAgICAgICAgaWYgKHRoaXMuZGF0YS5qb2IgPT09IGpvYikge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuaGlkZSgpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5qb2JDb3VudCA8PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZWNvZGUuQUxMSV9KT0JfRlVMTClcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBlcnIgPSBhd2FpdCBnYW1lSHByLmFsbGlhbmNlLmNoYW5nZUpvYih0aGlzLmRhdGEudWlkLCBqb2IpXG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICB9XG4gICAgfVxufVxuIl19