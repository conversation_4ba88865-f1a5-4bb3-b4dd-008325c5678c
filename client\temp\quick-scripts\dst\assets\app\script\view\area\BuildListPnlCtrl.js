
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BuildListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c9f83z2IFhHBY4DlfGsswWT', 'BuildListPnlCtrl');
// app/script/view/area/BuildListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var BuildListPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildListPnlCtrl, _super);
    function BuildListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.data = null;
        _this.areaCenter = null;
        _this.datas = [];
        _this.nodeMore = null;
        return _this;
    }
    BuildListPnlCtrl.prototype.listenEventMaps = function () {
        return [
        // { [EventType.UPDATE_BUILD_LV]: this.onUpdateBuildLv }
        ];
    };
    BuildListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var serverType;
            var _this = this;
            return __generator(this, function (_a) {
                this.areaCenter = this.getModel('areaCenter');
                this.datas.length = 0;
                serverType = GameHelper_1.gameHpr.getServerType();
                assetsMgr.getJson('buildBase').datas.forEach(function (m) {
                    if (m.id === Constant_1.BUILD_WALL_NID) {
                        return;
                    }
                    var types = ut.stringToNumbers(m.server, ',');
                    if (types.length > 0 && !types.has(serverType)) {
                        return;
                    }
                    var attr = assetsMgr.getJsonData('buildAttr', m.id * 1000);
                    _this.datas.push({
                        id: m.id,
                        name: 'buildText.name_' + m.id,
                        desc: 'buildText.desc_' + m.id,
                        json: m,
                        cost: GameHelper_1.gameHpr.stringToCTypes(attr === null || attr === void 0 ? void 0 : attr.up_cost),
                        btTime: (attr === null || attr === void 0 ? void 0 : attr.bt_time) || 0,
                        isHas: false,
                        condText: '',
                        sortVal: 0,
                    });
                });
                this.nodeMore = this.listSv_.content.Child('itemMore');
                this.nodeMore.removeFromParent();
                return [2 /*return*/];
            });
        });
    };
    BuildListPnlCtrl.prototype.onEnter = function (data, id) {
        this.data = data;
        this.updateList(id);
    };
    BuildListPnlCtrl.prototype.onRemove = function () {
    };
    BuildListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/unlock_be
    BuildListPnlCtrl.prototype.onClickUnlock = function (event, _) {
        var _this = this;
        var _a;
        var data = event.target.parent.parent.Data;
        if (!data) {
            return;
        }
        else if (!((_a = data.json) === null || _a === void 0 ? void 0 : _a.bt_count)) {
            return;
        }
        else if (!GameHelper_1.gameHpr.checkCTypes(data.cost)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
            // } else if (data.id === BUILD_ALLI_BAZAAR_NID && !gameHpr.player.isHasAlliance()) { //联盟市场需要加入联盟才能修建
            //     return viewHelper.showAlert('toast.please_join_alli')
        }
        else if (data.json.bt_count === -1) { //只能创建一个
            if (this.data.getBuildsById(data.id).length > 0) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.YET_IN_BTQUEUE);
            }
        }
        else if (data.json.bt_count === -3) { //同建筑全部满级才可创建
            if (!this.data.isBuildsMaxLv(data.id)) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.HAS_ONE_BUILD_NOT_MAXLV);
            }
        }
        this.areaCenter.unlockBuildToServer(this.data.index, data.id).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                _this.hide();
                // viewHelper.showAlert('toast.yet_add_build_queue')
                GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.PUSH);
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildListPnlCtrl.prototype.updateList = function (assignId) {
        var _this = this;
        var _a;
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode;
        var ignores = isNoviceMode ? NoviceConfig_1.NOVICE_IGNORE_BUILD : {};
        var datas = this.datas.filter(function (m) {
            var _a;
            if (!((_a = m.json) === null || _a === void 0 ? void 0 : _a.bt_count) || ignores[m.id]) {
                return false;
            }
            var bt_count = isNoviceMode ? -1 : m.json.bt_count;
            m.isHas = _this.data.getBuildsById(m.id).length > 0;
            if (bt_count === -1 && m.isHas) {
                return false;
            }
            m.condText = _this.checkUnlcokBuildCond(m.json);
            m.sortVal = (m.isHas ? 0 : 1) * 1000 + (m.condText ? 0 : 1) * 100 + (100 - m.json.sort);
            return true;
        }).sort(function (a, b) { return b.sortVal - a.sortVal; });
        (_a = this.nodeMore) === null || _a === void 0 ? void 0 : _a.removeFromParent();
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = this.getContentY(datas, GameHelper_1.gameHpr.guide.isOneGuideWorking() ? Constant_1.BUILD_BARRACKS_NID : assignId);
        this.listSv_.Items(datas, function (it, data, i) {
            it.name = 'item_' + data.id;
            it.Data = data;
            var base = it.Child('base');
            ResHelper_1.resHelper.loadBuildIcon('build_' + data.id, base.Child('icon/val', cc.Sprite), _this.key);
            base.Child('name').setLocaleKey(data.name);
            base.Child('desc').setLocaleKey(data.desc);
            var time = data.btTime;
            var cd = 0;
            if (GameHelper_1.gameHpr.isNoviceMode) {
                cd = GameHelper_1.gameHpr.noviceServer.getBuildCreateSpeedTime(time).cd;
            }
            ViewHelper_1.viewHelper.updateCostView(it.Child('need'), data.cost, time, cd);
            var cond = it.Child('cond'), condText = _this.checkUnlcokBuildCond(data.json);
            var isHasCond = cond.active = !!condText;
            it.Child('button/unlock_be').active = !isHasCond;
            if (isHasCond) {
                cond.Child('val').setLocaleKey(condText);
            }
            if (i === datas.length - 1 && isNoviceMode && !_this.nodeMore.parent) {
                _this.listSv_.content.addChild(_this.nodeMore);
                _this.listSv_.content.height += _this.nodeMore.height;
                _this.nodeMore.y = -(_this.listSv_.content.height - _this.nodeMore.height / 2);
            }
            it.Component(cc.Layout).updateLayout();
        });
        if (!datas.length && isNoviceMode) {
            this.listSv_.content.addChild(this.nodeMore);
            this.listSv_.content.getComponent(cc.Layout).enabled = true;
        }
    };
    BuildListPnlCtrl.prototype.checkUnlcokBuildCond = function (json) {
        var condText = GameHelper_1.gameHpr.checkUnlcokBuildCond(json.prep_cond);
        if (!condText && json.bt_count === -3 && !this.data.isBuildsMaxLv(json.id)) { //同建筑全部满级才可创建
            condText = assetsMgr.lang('ui.build_cond_lv', 'buildText.name_' + json.id, 20, '#D7634D');
        }
        return condText;
    };
    // 滑到指定建筑位置
    BuildListPnlCtrl.prototype.getContentY = function (datas, id) {
        if (!id) {
            return 0;
        }
        var index = datas.findIndex(function (m) { return m.id === id; });
        if (index <= 0) {
            return 0;
        }
        var height = datas.length * 380 - 8;
        if (height < this.listSv_.node.height) {
            return 0;
        }
        return 380 * index - 8;
    };
    BuildListPnlCtrl = __decorate([
        ccclass
    ], BuildListPnlCtrl);
    return BuildListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGFyZWFcXEJ1aWxkTGlzdFBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQW9GO0FBQ3BGLHFEQUFvRDtBQUNwRCxxREFBbUU7QUFFbkUsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFJNUQsK0RBQXFFO0FBRTdELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBZWxDO0lBQThDLG9DQUFjO0lBQTVEO1FBQUEscUVBNEtDO1FBMUtHLDBCQUEwQjtRQUNsQixhQUFPLEdBQWtCLElBQUksQ0FBQSxDQUFDLHNCQUFzQjtRQUM1RCxNQUFNO1FBRUUsVUFBSSxHQUFZLElBQUksQ0FBQTtRQUNwQixnQkFBVSxHQUFvQixJQUFJLENBQUE7UUFFbEMsV0FBSyxHQUFnQixFQUFFLENBQUE7UUFDdkIsY0FBUSxHQUFZLElBQUksQ0FBQTs7SUFrS3BDLENBQUM7SUFoS1UsMENBQWUsR0FBdEI7UUFDSSxPQUFPO1FBQ0gsd0RBQXdEO1NBQzNELENBQUE7SUFDTCxDQUFDO0lBRVksbUNBQVEsR0FBckI7Ozs7O2dCQUNJLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQTtnQkFDN0MsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO2dCQUNmLFVBQVUsR0FBRyxvQkFBTyxDQUFDLGFBQWEsRUFBRSxDQUFBO2dCQUMxQyxTQUFTLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO29CQUMxQyxJQUFJLENBQUMsQ0FBQyxFQUFFLEtBQUsseUJBQWMsRUFBRTt3QkFDekIsT0FBTTtxQkFDVDtvQkFDRCxJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUE7b0JBQy9DLElBQUksS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLFVBQVUsQ0FBQyxFQUFFO3dCQUM1QyxPQUFNO3FCQUNUO29CQUNELElBQU0sSUFBSSxHQUFtQixTQUFTLENBQUMsV0FBVyxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFBO29CQUM1RSxLQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQzt3QkFDWixFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUU7d0JBQ1IsSUFBSSxFQUFFLGlCQUFpQixHQUFHLENBQUMsQ0FBQyxFQUFFO3dCQUM5QixJQUFJLEVBQUUsaUJBQWlCLEdBQUcsQ0FBQyxDQUFDLEVBQUU7d0JBQzlCLElBQUksRUFBRSxDQUFDO3dCQUNQLElBQUksRUFBRSxvQkFBTyxDQUFDLGNBQWMsQ0FBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsT0FBTyxDQUFDO3dCQUMzQyxNQUFNLEVBQUUsQ0FBQSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsT0FBTyxLQUFJLENBQUM7d0JBQzFCLEtBQUssRUFBRSxLQUFLO3dCQUNaLFFBQVEsRUFBRSxFQUFFO3dCQUNaLE9BQU8sRUFBRSxDQUFDO3FCQUNiLENBQUMsQ0FBQTtnQkFDTixDQUFDLENBQUMsQ0FBQTtnQkFDRixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQTtnQkFDdEQsSUFBSSxDQUFDLFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFBOzs7O0tBQ25DO0lBRU0sa0NBQU8sR0FBZCxVQUFlLElBQWEsRUFBRSxFQUFVO1FBQ3BDLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2hCLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDLENBQUE7SUFDdkIsQ0FBQztJQUVNLG1DQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sa0NBQU8sR0FBZDtRQUNJLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDM0MsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0Isa0RBQWtEO0lBQ2xELHdDQUFhLEdBQWIsVUFBYyxLQUEwQixFQUFFLENBQVM7UUFBbkQsaUJBNkJDOztRQTVCRyxJQUFNLElBQUksR0FBYyxLQUFLLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQ3ZELElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDUCxPQUFNO1NBQ1Q7YUFBTSxJQUFJLFFBQUMsSUFBSSxDQUFDLElBQUksMENBQUUsUUFBUSxDQUFBLEVBQUU7WUFDN0IsT0FBTTtTQUNUO2FBQU0sSUFBSSxDQUFDLG9CQUFPLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUN4QyxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLGFBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQTtZQUNqRCxzR0FBc0c7WUFDdEcsNERBQTREO1NBQy9EO2FBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLFFBQVE7WUFDNUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtnQkFDN0MsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsY0FBYyxDQUFDLENBQUE7YUFDcEQ7U0FDSjthQUFNLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxhQUFhO1lBQ2pELElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUU7Z0JBQ25DLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLHVCQUF1QixDQUFDLENBQUE7YUFDN0Q7U0FDSjtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFBLEdBQUc7WUFDbEUsSUFBSSxDQUFDLEtBQUksQ0FBQyxPQUFPLEVBQUU7YUFDbEI7aUJBQU0sSUFBSSxHQUFHLEVBQUU7Z0JBQ1osdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDNUI7aUJBQU07Z0JBQ0gsS0FBSSxDQUFDLElBQUksRUFBRSxDQUFBO2dCQUNYLG9EQUFvRDtnQkFDcEQsb0JBQU8sQ0FBQyxxQkFBcUIsQ0FBQyw0QkFBb0IsQ0FBQyxJQUFJLENBQUMsQ0FBQTthQUMzRDtRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFakgsaUhBQWlIO0lBRXpHLHFDQUFVLEdBQWxCLFVBQW1CLFFBQWlCO1FBQXBDLGlCQWlEQzs7UUFoREcsSUFBTSxZQUFZLEdBQUcsb0JBQU8sQ0FBQyxZQUFZLENBQUE7UUFDekMsSUFBTSxPQUFPLEdBQUcsWUFBWSxDQUFDLENBQUMsQ0FBQyxrQ0FBbUIsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFBO1FBQ3ZELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQzs7WUFDN0IsSUFBSSxRQUFDLENBQUMsQ0FBQyxJQUFJLDBDQUFFLFFBQVEsQ0FBQSxJQUFJLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUU7Z0JBQ3BDLE9BQU8sS0FBSyxDQUFBO2FBQ2Y7WUFDRCxJQUFNLFFBQVEsR0FBRyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQTtZQUNwRCxDQUFDLENBQUMsS0FBSyxHQUFHLEtBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO1lBQ2xELElBQUksUUFBUSxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxLQUFLLEVBQUU7Z0JBQzVCLE9BQU8sS0FBSyxDQUFBO2FBQ2Y7WUFDRCxDQUFDLENBQUMsUUFBUSxHQUFHLEtBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUE7WUFDOUMsQ0FBQyxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxHQUFHLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUN2RixPQUFPLElBQUksQ0FBQTtRQUNmLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDLElBQUssT0FBQSxDQUFDLENBQUMsT0FBTyxHQUFHLENBQUMsQ0FBQyxPQUFPLEVBQXJCLENBQXFCLENBQUMsQ0FBQTtRQUN4QyxNQUFBLElBQUksQ0FBQyxRQUFRLDBDQUFFLGdCQUFnQixHQUFFO1FBQ2pDLElBQUksQ0FBQyxPQUFPLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDN0IsSUFBSSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxFQUFFLG9CQUFPLENBQUMsS0FBSyxDQUFDLGlCQUFpQixFQUFFLENBQUMsQ0FBQyxDQUFDLDZCQUFrQixDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUNuSCxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUM7WUFDbEMsRUFBRSxDQUFDLElBQUksR0FBRyxPQUFPLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQTtZQUMzQixFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtZQUNkLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7WUFDN0IscUJBQVMsQ0FBQyxhQUFhLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUN4RixJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7WUFDMUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQzFDLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7WUFDdEIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFBO1lBQ1YsSUFBSSxvQkFBTyxDQUFDLFlBQVksRUFBRTtnQkFDdEIsRUFBRSxHQUFHLG9CQUFPLENBQUMsWUFBWSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUUsQ0FBQTthQUM3RDtZQUNELHVCQUFVLENBQUMsY0FBYyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUE7WUFDaEUsSUFBTSxJQUFJLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsRUFBRSxRQUFRLEdBQUcsS0FBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUM5RSxJQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxRQUFRLENBQUE7WUFDMUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLFNBQVMsQ0FBQTtZQUNoRCxJQUFJLFNBQVMsRUFBRTtnQkFDWCxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxRQUFRLENBQUMsQ0FBQTthQUMzQztZQUNELElBQUksQ0FBQyxLQUFLLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLFlBQVksSUFBSSxDQUFDLEtBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxFQUFFO2dCQUNqRSxLQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsS0FBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO2dCQUM1QyxLQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxNQUFNLElBQUksS0FBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUE7Z0JBQ25ELEtBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxLQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsS0FBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUE7YUFDOUU7WUFDRCxFQUFFLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLEVBQUUsQ0FBQTtRQUMxQyxDQUFDLENBQUMsQ0FBQTtRQUNGLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxJQUFJLFlBQVksRUFBRTtZQUMvQixJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1lBQzVDLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQTtTQUM5RDtJQUNMLENBQUM7SUFFTywrQ0FBb0IsR0FBNUIsVUFBNkIsSUFBUztRQUNsQyxJQUFJLFFBQVEsR0FBRyxvQkFBTyxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQTtRQUMzRCxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxRQUFRLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxhQUFhO1lBQ3ZGLFFBQVEsR0FBRyxTQUFTLENBQUMsSUFBSSxDQUFDLGtCQUFrQixFQUFFLGlCQUFpQixHQUFHLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFBO1NBQzVGO1FBQ0QsT0FBTyxRQUFRLENBQUE7SUFDbkIsQ0FBQztJQUVELFdBQVc7SUFDSCxzQ0FBVyxHQUFuQixVQUFvQixLQUFrQixFQUFFLEVBQVU7UUFDOUMsSUFBSSxDQUFDLEVBQUUsRUFBRTtZQUNMLE9BQU8sQ0FBQyxDQUFBO1NBQ1g7UUFDRCxJQUFNLEtBQUssR0FBRyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEVBQUUsS0FBSyxFQUFFLEVBQVgsQ0FBVyxDQUFDLENBQUE7UUFDL0MsSUFBSSxLQUFLLElBQUksQ0FBQyxFQUFFO1lBQ1osT0FBTyxDQUFDLENBQUE7U0FDWDtRQUNELElBQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQTtRQUNyQyxJQUFJLE1BQU0sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDbkMsT0FBTyxDQUFDLENBQUE7U0FDWDtRQUNELE9BQU8sR0FBRyxHQUFHLEtBQUssR0FBRyxDQUFDLENBQUE7SUFDMUIsQ0FBQztJQTNLZ0IsZ0JBQWdCO1FBRHBDLE9BQU87T0FDYSxnQkFBZ0IsQ0E0S3BDO0lBQUQsdUJBQUM7Q0E1S0QsQUE0S0MsQ0E1SzZDLEVBQUUsQ0FBQyxXQUFXLEdBNEszRDtrQkE1S29CLGdCQUFnQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJVSUxEX0JBUlJBQ0tTX05JRCwgQlVJTERfV0FMTF9OSUQgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCB7IE5vdGljZVBlcm1pc3Npb25UeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgQnVpbGRBdHRySkl0ZW0sIEJ1aWxkQmFzZUpJdGVtIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Kc29uVHlwZVwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBBcmVhQ2VudGVyTW9kZWwgZnJvbSBcIi4uLy4uL21vZGVsL2FyZWEvQXJlYUNlbnRlck1vZGVsXCI7XG5pbXBvcnQgQXJlYU9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9BcmVhT2JqXCI7XG5pbXBvcnQgQ1R5cGVPYmogZnJvbSBcIi4uLy4uL21vZGVsL2NvbW1vbi9DVHlwZU9ialwiO1xuaW1wb3J0IHsgTk9WSUNFX0lHTk9SRV9CVUlMRCB9IGZyb20gXCIuLi8uLi9tb2RlbC9ndWlkZS9Ob3ZpY2VDb25maWdcIjtcblxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG50eXBlIEJ1aWxkSW5mbyA9IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBkZXNjOiBzdHJpbmc7XG4gICAganNvbjogQnVpbGRCYXNlSkl0ZW07XG4gICAgY29zdDogQ1R5cGVPYmpbXTtcbiAgICBidFRpbWU6IG51bWJlcjtcbiAgICBpc0hhczogYm9vbGVhbjtcbiAgICBjb25kVGV4dDogc3RyaW5nO1xuICAgIHNvcnRWYWw6IG51bWJlcjtcbn1cblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJ1aWxkTGlzdFBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgbGlzdFN2XzogY2MuU2Nyb2xsVmlldyA9IG51bGwgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdlxuICAgIC8vQGVuZFxuXG4gICAgcHJpdmF0ZSBkYXRhOiBBcmVhT2JqID0gbnVsbFxuICAgIHByaXZhdGUgYXJlYUNlbnRlcjogQXJlYUNlbnRlck1vZGVsID0gbnVsbFxuXG4gICAgcHJpdmF0ZSBkYXRhczogQnVpbGRJbmZvW10gPSBbXVxuICAgIHByaXZhdGUgbm9kZU1vcmU6IGNjLk5vZGUgPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgLy8geyBbRXZlbnRUeXBlLlVQREFURV9CVUlMRF9MVl06IHRoaXMub25VcGRhdGVCdWlsZEx2IH1cbiAgICAgICAgXVxuICAgIH1cblxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyID0gdGhpcy5nZXRNb2RlbCgnYXJlYUNlbnRlcicpXG4gICAgICAgIHRoaXMuZGF0YXMubGVuZ3RoID0gMFxuICAgICAgICBjb25zdCBzZXJ2ZXJUeXBlID0gZ2FtZUhwci5nZXRTZXJ2ZXJUeXBlKClcbiAgICAgICAgYXNzZXRzTWdyLmdldEpzb24oJ2J1aWxkQmFzZScpLmRhdGFzLmZvckVhY2gobSA9PiB7XG4gICAgICAgICAgICBpZiAobS5pZCA9PT0gQlVJTERfV0FMTF9OSUQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHR5cGVzID0gdXQuc3RyaW5nVG9OdW1iZXJzKG0uc2VydmVyLCAnLCcpXG4gICAgICAgICAgICBpZiAodHlwZXMubGVuZ3RoID4gMCAmJiAhdHlwZXMuaGFzKHNlcnZlclR5cGUpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBhdHRyOiBCdWlsZEF0dHJKSXRlbSA9IGFzc2V0c01nci5nZXRKc29uRGF0YSgnYnVpbGRBdHRyJywgbS5pZCAqIDEwMDApXG4gICAgICAgICAgICB0aGlzLmRhdGFzLnB1c2goe1xuICAgICAgICAgICAgICAgIGlkOiBtLmlkLFxuICAgICAgICAgICAgICAgIG5hbWU6ICdidWlsZFRleHQubmFtZV8nICsgbS5pZCxcbiAgICAgICAgICAgICAgICBkZXNjOiAnYnVpbGRUZXh0LmRlc2NfJyArIG0uaWQsXG4gICAgICAgICAgICAgICAganNvbjogbSxcbiAgICAgICAgICAgICAgICBjb3N0OiBnYW1lSHByLnN0cmluZ1RvQ1R5cGVzKGF0dHI/LnVwX2Nvc3QpLFxuICAgICAgICAgICAgICAgIGJ0VGltZTogYXR0cj8uYnRfdGltZSB8fCAwLFxuICAgICAgICAgICAgICAgIGlzSGFzOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjb25kVGV4dDogJycsXG4gICAgICAgICAgICAgICAgc29ydFZhbDogMCxcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgICAgIHRoaXMubm9kZU1vcmUgPSB0aGlzLmxpc3RTdl8uY29udGVudC5DaGlsZCgnaXRlbU1vcmUnKVxuICAgICAgICB0aGlzLm5vZGVNb3JlLnJlbW92ZUZyb21QYXJlbnQoKVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IEFyZWFPYmosIGlkOiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YVxuICAgICAgICB0aGlzLnVwZGF0ZUxpc3QoaWQpXG4gICAgfVxuXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xuICAgICAgICBhc3NldHNNZ3IucmVsZWFzZVRlbXBSZXNCeVRhZyh0aGlzLmtleSlcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdi92aWV3L2NvbnRlbnQvaXRlbS91bmxvY2tfYmVcbiAgICBvbkNsaWNrVW5sb2NrKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgZGF0YTogQnVpbGRJbmZvID0gZXZlbnQudGFyZ2V0LnBhcmVudC5wYXJlbnQuRGF0YVxuICAgICAgICBpZiAoIWRhdGEpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKCFkYXRhLmpzb24/LmJ0X2NvdW50KSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfSBlbHNlIGlmICghZ2FtZUhwci5jaGVja0NUeXBlcyhkYXRhLmNvc3QpKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZWNvZGUuUkVTX05PVF9FTk9VR0gpXG4gICAgICAgICAgICAvLyB9IGVsc2UgaWYgKGRhdGEuaWQgPT09IEJVSUxEX0FMTElfQkFaQUFSX05JRCAmJiAhZ2FtZUhwci5wbGF5ZXIuaXNIYXNBbGxpYW5jZSgpKSB7IC8v6IGU55uf5biC5Zy66ZyA6KaB5Yqg5YWl6IGU55uf5omN6IO95L+u5bu6XG4gICAgICAgICAgICAvLyAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2Vfam9pbl9hbGxpJylcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmpzb24uYnRfY291bnQgPT09IC0xKSB7IC8v5Y+q6IO95Yib5bu65LiA5LiqXG4gICAgICAgICAgICBpZiAodGhpcy5kYXRhLmdldEJ1aWxkc0J5SWQoZGF0YS5pZCkubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5ZRVRfSU5fQlRRVUVVRSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmpzb24uYnRfY291bnQgPT09IC0zKSB7IC8v5ZCM5bu6562R5YWo6YOo5ruh57qn5omN5Y+v5Yib5bu6XG4gICAgICAgICAgICBpZiAoIXRoaXMuZGF0YS5pc0J1aWxkc01heEx2KGRhdGEuaWQpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVjb2RlLkhBU19PTkVfQlVJTERfTk9UX01BWExWKVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuYXJlYUNlbnRlci51bmxvY2tCdWlsZFRvU2VydmVyKHRoaXMuZGF0YS5pbmRleCwgZGF0YS5pZCkudGhlbihlcnIgPT4ge1xuICAgICAgICAgICAgaWYgKCF0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5zaG93QWxlcnQoZXJyKVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICAgICAgICAgIC8vIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC55ZXRfYWRkX2J1aWxkX3F1ZXVlJylcbiAgICAgICAgICAgICAgICBnYW1lSHByLmNoZWNrTm90aWNlUGVybWlzc2lvbihOb3RpY2VQZXJtaXNzaW9uVHlwZS5QVVNIKVxuICAgICAgICAgICAgfVxuICAgICAgICB9KVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgdXBkYXRlTGlzdChhc3NpZ25JZD86IG51bWJlcikge1xuICAgICAgICBjb25zdCBpc05vdmljZU1vZGUgPSBnYW1lSHByLmlzTm92aWNlTW9kZVxuICAgICAgICBjb25zdCBpZ25vcmVzID0gaXNOb3ZpY2VNb2RlID8gTk9WSUNFX0lHTk9SRV9CVUlMRCA6IHt9XG4gICAgICAgIGNvbnN0IGRhdGFzID0gdGhpcy5kYXRhcy5maWx0ZXIobSA9PiB7XG4gICAgICAgICAgICBpZiAoIW0uanNvbj8uYnRfY291bnQgfHwgaWdub3Jlc1ttLmlkXSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgYnRfY291bnQgPSBpc05vdmljZU1vZGUgPyAtMSA6IG0uanNvbi5idF9jb3VudFxuICAgICAgICAgICAgbS5pc0hhcyA9IHRoaXMuZGF0YS5nZXRCdWlsZHNCeUlkKG0uaWQpLmxlbmd0aCA+IDBcbiAgICAgICAgICAgIGlmIChidF9jb3VudCA9PT0gLTEgJiYgbS5pc0hhcykge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgbS5jb25kVGV4dCA9IHRoaXMuY2hlY2tVbmxjb2tCdWlsZENvbmQobS5qc29uKVxuICAgICAgICAgICAgbS5zb3J0VmFsID0gKG0uaXNIYXMgPyAwIDogMSkgKiAxMDAwICsgKG0uY29uZFRleHQgPyAwIDogMSkgKiAxMDAgKyAoMTAwIC0gbS5qc29uLnNvcnQpXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgICB9KS5zb3J0KChhLCBiKSA9PiBiLnNvcnRWYWwgLSBhLnNvcnRWYWwpXG4gICAgICAgIHRoaXMubm9kZU1vcmU/LnJlbW92ZUZyb21QYXJlbnQoKVxuICAgICAgICB0aGlzLmxpc3RTdl8uc3RvcEF1dG9TY3JvbGwoKVxuICAgICAgICB0aGlzLmxpc3RTdl8uY29udGVudC55ID0gdGhpcy5nZXRDb250ZW50WShkYXRhcywgZ2FtZUhwci5ndWlkZS5pc09uZUd1aWRlV29ya2luZygpID8gQlVJTERfQkFSUkFDS1NfTklEIDogYXNzaWduSWQpXG4gICAgICAgIHRoaXMubGlzdFN2Xy5JdGVtcyhkYXRhcywgKGl0LCBkYXRhLCBpKSA9PiB7XG4gICAgICAgICAgICBpdC5uYW1lID0gJ2l0ZW1fJyArIGRhdGEuaWRcbiAgICAgICAgICAgIGl0LkRhdGEgPSBkYXRhXG4gICAgICAgICAgICBjb25zdCBiYXNlID0gaXQuQ2hpbGQoJ2Jhc2UnKVxuICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRCdWlsZEljb24oJ2J1aWxkXycgKyBkYXRhLmlkLCBiYXNlLkNoaWxkKCdpY29uL3ZhbCcsIGNjLlNwcml0ZSksIHRoaXMua2V5KVxuICAgICAgICAgICAgYmFzZS5DaGlsZCgnbmFtZScpLnNldExvY2FsZUtleShkYXRhLm5hbWUpXG4gICAgICAgICAgICBiYXNlLkNoaWxkKCdkZXNjJykuc2V0TG9jYWxlS2V5KGRhdGEuZGVzYylcbiAgICAgICAgICAgIGxldCB0aW1lID0gZGF0YS5idFRpbWVcbiAgICAgICAgICAgIGxldCBjZCA9IDBcbiAgICAgICAgICAgIGlmIChnYW1lSHByLmlzTm92aWNlTW9kZSkge1xuICAgICAgICAgICAgICAgIGNkID0gZ2FtZUhwci5ub3ZpY2VTZXJ2ZXIuZ2V0QnVpbGRDcmVhdGVTcGVlZFRpbWUodGltZSkuY2RcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZpZXdIZWxwZXIudXBkYXRlQ29zdFZpZXcoaXQuQ2hpbGQoJ25lZWQnKSwgZGF0YS5jb3N0LCB0aW1lLCBjZClcbiAgICAgICAgICAgIGNvbnN0IGNvbmQgPSBpdC5DaGlsZCgnY29uZCcpLCBjb25kVGV4dCA9IHRoaXMuY2hlY2tVbmxjb2tCdWlsZENvbmQoZGF0YS5qc29uKVxuICAgICAgICAgICAgY29uc3QgaXNIYXNDb25kID0gY29uZC5hY3RpdmUgPSAhIWNvbmRUZXh0XG4gICAgICAgICAgICBpdC5DaGlsZCgnYnV0dG9uL3VubG9ja19iZScpLmFjdGl2ZSA9ICFpc0hhc0NvbmRcbiAgICAgICAgICAgIGlmIChpc0hhc0NvbmQpIHtcbiAgICAgICAgICAgICAgICBjb25kLkNoaWxkKCd2YWwnKS5zZXRMb2NhbGVLZXkoY29uZFRleHQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaSA9PT0gZGF0YXMubGVuZ3RoIC0gMSAmJiBpc05vdmljZU1vZGUgJiYgIXRoaXMubm9kZU1vcmUucGFyZW50KSB7XG4gICAgICAgICAgICAgICAgdGhpcy5saXN0U3ZfLmNvbnRlbnQuYWRkQ2hpbGQodGhpcy5ub2RlTW9yZSlcbiAgICAgICAgICAgICAgICB0aGlzLmxpc3RTdl8uY29udGVudC5oZWlnaHQgKz0gdGhpcy5ub2RlTW9yZS5oZWlnaHRcbiAgICAgICAgICAgICAgICB0aGlzLm5vZGVNb3JlLnkgPSAtKHRoaXMubGlzdFN2Xy5jb250ZW50LmhlaWdodCAtIHRoaXMubm9kZU1vcmUuaGVpZ2h0IC8gMilcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGl0LkNvbXBvbmVudChjYy5MYXlvdXQpLnVwZGF0ZUxheW91dCgpXG4gICAgICAgIH0pXG4gICAgICAgIGlmICghZGF0YXMubGVuZ3RoICYmIGlzTm92aWNlTW9kZSkge1xuICAgICAgICAgICAgdGhpcy5saXN0U3ZfLmNvbnRlbnQuYWRkQ2hpbGQodGhpcy5ub2RlTW9yZSlcbiAgICAgICAgICAgIHRoaXMubGlzdFN2Xy5jb250ZW50LmdldENvbXBvbmVudChjYy5MYXlvdXQpLmVuYWJsZWQgPSB0cnVlXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcml2YXRlIGNoZWNrVW5sY29rQnVpbGRDb25kKGpzb246IGFueSkge1xuICAgICAgICBsZXQgY29uZFRleHQgPSBnYW1lSHByLmNoZWNrVW5sY29rQnVpbGRDb25kKGpzb24ucHJlcF9jb25kKVxuICAgICAgICBpZiAoIWNvbmRUZXh0ICYmIGpzb24uYnRfY291bnQgPT09IC0zICYmICF0aGlzLmRhdGEuaXNCdWlsZHNNYXhMdihqc29uLmlkKSkgeyAvL+WQjOW7uuetkeWFqOmDqOa7oee6p+aJjeWPr+WIm+W7ulxuICAgICAgICAgICAgY29uZFRleHQgPSBhc3NldHNNZ3IubGFuZygndWkuYnVpbGRfY29uZF9sdicsICdidWlsZFRleHQubmFtZV8nICsganNvbi5pZCwgMjAsICcjRDc2MzREJylcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY29uZFRleHRcbiAgICB9XG5cbiAgICAvLyDmu5HliLDmjIflrprlu7rnrZHkvY3nva5cbiAgICBwcml2YXRlIGdldENvbnRlbnRZKGRhdGFzOiBCdWlsZEluZm9bXSwgaWQ6IG51bWJlcikge1xuICAgICAgICBpZiAoIWlkKSB7XG4gICAgICAgICAgICByZXR1cm4gMFxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGluZGV4ID0gZGF0YXMuZmluZEluZGV4KG0gPT4gbS5pZCA9PT0gaWQpXG4gICAgICAgIGlmIChpbmRleCA8PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gMFxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGhlaWdodCA9IGRhdGFzLmxlbmd0aCAqIDM4MCAtIDhcbiAgICAgICAgaWYgKGhlaWdodCA8IHRoaXMubGlzdFN2Xy5ub2RlLmhlaWdodCkge1xuICAgICAgICAgICAgcmV0dXJuIDBcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gMzgwICogaW5kZXggLSA4XG4gICAgfVxufVxuIl19