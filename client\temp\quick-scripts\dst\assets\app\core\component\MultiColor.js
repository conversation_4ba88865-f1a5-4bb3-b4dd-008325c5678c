
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/MultiColor.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '496b2t/7mxJwZrIreYXXHls', 'MultiColor');
// app/core/component/MultiColor.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
var MultiColor = /** @class */ (function (_super) {
    __extends(MultiColor, _super);
    function MultiColor() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.colors = [];
        return _this;
    }
    MultiColor.prototype.Color = function (idx) {
        var i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0);
        if (i >= this.colors.length) {
            return;
        }
        this.node.color = this.colors[i];
    };
    __decorate([
        property([cc.Color])
    ], MultiColor.prototype, "colors", void 0);
    MultiColor = __decorate([
        ccclass,
        menu('自定义组件/MultiColor')
    ], MultiColor);
    return MultiColor;
}(cc.Component));
exports.default = MultiColor;
cc.MultiColor = MultiColor;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxjb21wb25lbnRcXE11bHRpQ29sb3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUE4QixFQUFFLENBQUMsVUFBVSxFQUF6QyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQUEsRUFBRSxJQUFJLFVBQWtCLENBQUM7QUFJbEQ7SUFBd0MsOEJBQVk7SUFBcEQ7UUFBQSxxRUFZQztRQVRXLFlBQU0sR0FBZSxFQUFFLENBQUE7O0lBU25DLENBQUM7SUFQVSwwQkFBSyxHQUFaLFVBQWEsR0FBcUI7UUFDOUIsSUFBSSxDQUFDLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxLQUFLLFFBQVEsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUN2RCxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRTtZQUN6QixPQUFNO1NBQ1Q7UUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFBO0lBQ3BDLENBQUM7SUFSRDtRQURDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQzs4Q0FDVTtJQUhkLFVBQVU7UUFGOUIsT0FBTztRQUNQLElBQUksQ0FBQyxrQkFBa0IsQ0FBQztPQUNKLFVBQVUsQ0FZOUI7SUFBRCxpQkFBQztDQVpELEFBWUMsQ0FadUMsRUFBRSxDQUFDLFNBQVMsR0FZbkQ7a0JBWm9CLFVBQVU7QUFjL0IsRUFBRSxDQUFDLFVBQVUsR0FBRyxVQUFVLENBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5LCBtZW51IH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuQG1lbnUoJ+iHquWumuS5iee7hOS7ti9NdWx0aUNvbG9yJylcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgTXVsdGlDb2xvciBleHRlbmRzIGNjLkNvbXBvbmVudCB7XHJcblxyXG4gICAgQHByb3BlcnR5KFtjYy5Db2xvcl0pXHJcbiAgICBwcml2YXRlIGNvbG9yczogY2MuQ29sb3JbXSA9IFtdXHJcblxyXG4gICAgcHVibGljIENvbG9yKGlkeDogbnVtYmVyIHwgYm9vbGVhbikge1xyXG4gICAgICAgIGxldCBpID0gdHlwZW9mIChpZHgpID09PSAnbnVtYmVyJyA/IGlkeCA6IChpZHggPyAxIDogMClcclxuICAgICAgICBpZiAoaSA+PSB0aGlzLmNvbG9ycy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMubm9kZS5jb2xvciA9IHRoaXMuY29sb3JzW2ldXHJcbiAgICB9XHJcbn1cclxuXHJcbmNjLk11bHRpQ29sb3IgPSBNdWx0aUNvbG9yIl19