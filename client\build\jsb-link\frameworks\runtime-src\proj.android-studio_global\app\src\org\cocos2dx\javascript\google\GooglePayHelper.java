package org.cocos2dx.javascript.google;

import static com.android.billingclient.api.BillingClient.*;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.android.billingclient.api.AcknowledgePurchaseParams;
import com.android.billingclient.api.AcknowledgePurchaseResponseListener;
import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeParams;
import com.android.billingclient.api.ConsumeResponseListener;
import com.android.billingclient.api.PendingPurchasesParams;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.ProductDetailsResponseListener;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesResponseListener;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryProductDetailsResult;
import com.android.billingclient.api.QueryPurchasesParams;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.cocos2dx.javascript.JsbHelper;
import org.cocos2dx.javascript.AppActivity;

public class GooglePayHelper{
    private static final String TAG = GooglePayHelper.class.getSimpleName();

    public static BillingClient mBillingClient;
    public static PurchasesUpdatedListener mPurchasesUpdatedListener;
    public static HashMap<String, ProductDetails> iapMap = new HashMap<String, ProductDetails>();
    public static PurchasesResponseListener mPurchasesResponseListener;

    public static boolean inAppInitEnd = false, subsInitEnd = true;

    public static void Init(Map json) {
        Log.v(TAG, "init");

        mPurchasesUpdatedListener = null;
        mPurchasesUpdatedListener = new PurchasesUpdatedListener() {
            @Override
            public void onPurchasesUpdated(@NonNull BillingResult billingResult, @Nullable List<Purchase> purchasesList) {
                Log.d(TAG, "onPurchasesUpdated " + purchasesList);
                Map resMap = new HashMap();
                if (billingResult.getResponseCode() == BillingResponseCode.OK
                        && purchasesList != null) {

                    if (purchasesList.size() > 0) {
                        Purchase purchase = purchasesList.get(0);
                        Log.d(TAG, "succ cpOrderId is " + purchase.getAccountIdentifiers());
                        resMap.put("result", JSON.parse(purchase.getOriginalJson()));
                    }

                } else if (billingResult.getResponseCode() == BillingResponseCode.USER_CANCELED) {
                    // Handle an error caused by a user cancelling the purchase flow.
                    Log.d(TAG, "取消支付");
                } else {
                    Log.d(TAG, "支付失败 " + billingResult.getDebugMessage() + " " + billingResult.getResponseCode());
                    resMap.put("error", billingResult.getResponseCode() + billingResult.getDebugMessage());
                }
                JsbHelper.callbackToTs("GOOGLE_PAY", resMap);
            }
        };

        mBillingClient = null;
        mBillingClient = newBuilder(AppActivity.app)
                .setListener(mPurchasesUpdatedListener)
                .enablePendingPurchases(PendingPurchasesParams.newBuilder()
                        .enableOneTimeProducts() //
                        .enablePrepaidPlans()
                        .build())
                .enableAutoServiceReconnection()
                .build();

//        if (!mBillingClient.isReady()) {
            mBillingClient.startConnection(new BillingClientStateListener() {
                @Override
                public void onBillingSetupFinished(BillingResult billingResult) {
                    if (billingResult.getResponseCode() ==  BillingResponseCode.OK) {
                        List<String> skuListStr = new ArrayList<>();
                        skuListStr = JSON.parseArray(String.valueOf(json.get("key")), String.class);
                        List<QueryProductDetailsParams.Product> skuListPro = new ArrayList<>();
                        for (String id: skuListStr) {
                            skuListPro.add(QueryProductDetailsParams.Product.newBuilder().setProductId(id).setProductType(ProductType.INAPP).build());
                        }
                        QueryProductDetailsParams queryProductDetailsParams =
                                QueryProductDetailsParams.newBuilder()
                                        .setProductList(skuListPro)
                                        .build();

                        HashMap<String, ArrayList> initResMap = new HashMap<String, ArrayList>();
                        inAppInitEnd = false; subsInitEnd = true;
                        if (json.containsKey("subKey")) {
                            subsInitEnd = false;
                        }

                        mBillingClient.queryProductDetailsAsync(
                                queryProductDetailsParams,
                                new ProductDetailsResponseListener() {
                                    public void onProductDetailsResponse(@NonNull BillingResult billingResult,@NonNull QueryProductDetailsResult queryProductDetailsResult) {
                                        List<ProductDetails> proList = queryProductDetailsResult.getProductDetailsList();
                                        Log.d(TAG, "inapp init: " + proList.size());
                                        ArrayList<HashMap<String, String> > list = new  ArrayList<HashMap<String, String> >();
                                        inAppInitEnd = true;
                                        if (!proList.isEmpty()) {
                                            try {
                                                for (ProductDetails productDetails : proList) {
                                                    Log.d(TAG, productDetails + "");
                                                    iapMap.put(productDetails.getProductId(), productDetails);
                                                    HashMap<String, String> info = new HashMap<String, String>();
                                                    info.put("productId", productDetails.getProductId());
                                                    info.put("price", productDetails.getOneTimePurchaseOfferDetails().getFormattedPrice());
                                                    info.put("currency_pay", productDetails.getOneTimePurchaseOfferDetails().getPriceCurrencyCode());
                                                    info.put("currency_price", ""+productDetails.getOneTimePurchaseOfferDetails().getPriceAmountMicros());
                                                    list.add(info);
                                                }
                                                initResMap.put("result", list);
                                                if (subsInitEnd) JsbHelper.callbackToTs("IAP_INIT", initResMap);
                                            } catch (NullPointerException e) {
                                                if (subsInitEnd) {
                                                    HashMap<String, String> resMap1 = new HashMap<String, String>();
                                                    resMap1.put("error", "iap_init_item_crash");
                                                    JsbHelper.callbackToTs("IAP_INIT", initResMap);
                                                }
                                            }
                                        } else {
                                            if (subsInitEnd) {
                                                HashMap<String, String> resMap1 = new HashMap<String, String>();
                                                resMap1.put("error", "iap_init_null");
                                                JsbHelper.callbackToTs("IAP_INIT", resMap1);
                                            }
                                        }
                                    }
                                }
                        );

                        if (!subsInitEnd) {
                            List<String> subSkuListStr = new ArrayList<>();
                            subSkuListStr = JSON.parseObject(String.valueOf(json.get("subKey")), new TypeReference<List<String>>(){});
                            List<QueryProductDetailsParams.Product> subSkuListPro = new ArrayList<>();
                            for (String id: subSkuListStr) {
                                subSkuListPro.add(QueryProductDetailsParams.Product.newBuilder().setProductId(id).setProductType(ProductType.SUBS).build());
                            }

                            QueryProductDetailsParams querySubProductDetailsParams =
                                    QueryProductDetailsParams.newBuilder()
                                            .setProductList(subSkuListPro)
                                            .build();

                            mBillingClient.queryProductDetailsAsync(
                                    querySubProductDetailsParams,
                                    new ProductDetailsResponseListener() {
                                        public void onProductDetailsResponse(@NonNull BillingResult billingResult,@NonNull QueryProductDetailsResult queryProductDetailsResult) {
                                            List<ProductDetails> proList = queryProductDetailsResult.getProductDetailsList();
                                            Log.d(TAG, "subs init: " + proList.size());
                                            ArrayList<HashMap<String, String> > list = new  ArrayList<HashMap<String, String> >();
                                            subsInitEnd = true;
                                            if (!proList.isEmpty()) {
                                                try {
                                                    for (ProductDetails productDetails : proList) {
                                                        Log.d(TAG, productDetails.getSubscriptionOfferDetails().size() + "");
                                                        iapMap.put(productDetails.getProductId(), productDetails);
                                                        for (ProductDetails.SubscriptionOfferDetails subofferDetails : productDetails.getSubscriptionOfferDetails()) {
                                                            HashMap<String, String> info = new HashMap<String, String>();
                                                            info.put("productId", productDetails.getProductId());
                                                            info.put("price", subofferDetails.getPricingPhases().getPricingPhaseList().get(0).getFormattedPrice());
                                                            info.put("currency_pay", subofferDetails.getPricingPhases().getPricingPhaseList().get(0).getPriceCurrencyCode());
                                                            info.put("currency_price", ""+subofferDetails.getPricingPhases().getPricingPhaseList().get(0).getPriceAmountMicros());
                                                            info.put("token", subofferDetails.getOfferToken());
                                                            info.put("planId", subofferDetails.getBasePlanId());
                                                            info.put("offerId", subofferDetails.getOfferId());
                                                            list.add(info);
                                                        }
                                                    }
                                                    initResMap.put("subs", list);
                                                    if (inAppInitEnd) JsbHelper.callbackToTs("IAP_INIT", initResMap);
                                                } catch (NullPointerException e) {
                                                    if (inAppInitEnd) {
                                                        HashMap<String, String> resMap2 = new HashMap<String, String>();
                                                        resMap2.put("error", "iap_init_subsItem_crash");
                                                        JsbHelper.callbackToTs("IAP_INIT", resMap2);
                                                    }
                                                }
                                            } else {
                                                if (inAppInitEnd) {
                                                    HashMap<String, String> resMap1 = new HashMap<String, String>();
                                                    resMap1.put("error", "iap_init_null");
                                                    JsbHelper.callbackToTs("IAP_INIT", resMap1);
                                                }
                                            }
                                        }
                                    }
                            );
                        }
                    }
                    else {
                        Log.d(TAG, "连接失败1" + billingResult.getResponseCode() + billingResult.getDebugMessage());
                        HashMap<String, String> resMap = new HashMap<String, String>();
                        resMap.put("error", billingResult.getResponseCode() + billingResult.getDebugMessage());
                        JsbHelper.callbackToTs("IAP_INIT", resMap);
                    }
                }
                @Override
                public void onBillingServiceDisconnected() {
                    // Try to restart the connection on the next request to
                    // Google Play by calling the startConnection() method.
                    Log.d(TAG, "init fail");
                    HashMap<String, String> resMap = new HashMap<String, String>();
                    resMap.put("error", "onBillingServiceDisconnected");
                    JsbHelper.callbackToTs("IAP_INIT", resMap);
                }
            });
//        } else {
//            Log.d(TAG, "mBillingClient没有ready");
//            HashMap<String, String> resMap = new HashMap<String, String>();
//            resMap.put("error", "mBillingClient没有ready");
//            JsbHelper.callbackToTs("IAP_INIT", resMap);
//        }
    }

    public static void pay(Map json) {
        String payId = String.valueOf(json.get("pay_id"));
        String obfuscatedProfileId = String.valueOf(json.get("cpOrderId"));
        if (iapMap.containsKey(payId)) {
            ProductDetails proDetails = iapMap.get(payId);
            List<BillingFlowParams.ProductDetailsParams> listPro = new ArrayList<>();

            if (ProductType.SUBS.equals(proDetails.getProductType()) && proDetails.getSubscriptionOfferDetails() != null) {
                String token = String.valueOf(json.get("token"));
                listPro.add(BillingFlowParams.ProductDetailsParams.newBuilder().setProductDetails(proDetails).setOfferToken(token).build());
            } else {
                listPro.add(BillingFlowParams.ProductDetailsParams.newBuilder().setProductDetails(proDetails).build());
            }

            BillingFlowParams billingFlowParams = BillingFlowParams.newBuilder()
                    .setProductDetailsParamsList(listPro)
                    .setObfuscatedAccountId(obfuscatedProfileId)
                    .build();
            int responseCode = mBillingClient.launchBillingFlow(AppActivity.app, billingFlowParams).getResponseCode();
            Log.d(TAG, "launchBillingFlow: "+ responseCode);
        } else {
            Log.d(TAG, "无效订单号 " + payId);
            HashMap<String, String> resMap = new HashMap<String, String>();
            resMap.put("error", "pay_id not found " + payId);
            JsbHelper.callbackToTs("GOOGLE_PAY", resMap);
        }
    }

    public static void queryPurchasesAsync(String type, String callbackEvent) {
        mBillingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType(type).build(),
                new PurchasesResponseListener() {
                    @Override
                    public void onQueryPurchasesResponse(@NonNull BillingResult billingResult, @NonNull List<Purchase> purchasesList) {
                        Log.d(TAG, "queryPurchasesAsync " + purchasesList);
                        Map resMap = new HashMap();
                        if (billingResult.getResponseCode() == BillingResponseCode.OK
                                && purchasesList != null) {
                            ArrayList<Object> list = new  ArrayList<Object>();
                            for (Purchase purchase : purchasesList) {
                                list.add(JSON.parse(purchase.getOriginalJson()));
                            }
                            resMap.put("result", list);
                        } else {
                            Log.d(TAG, "queryPurchasesAsync fail" + billingResult.getDebugMessage() + " " + billingResult.getResponseCode());
                            resMap.put("error", billingResult.getResponseCode() + billingResult.getDebugMessage());
                        }
                        JsbHelper.callbackToTs(callbackEvent, resMap);
                    }
                });
    }

//    消耗型商品finish
    public static void consume(Map json) {
        String token = String.valueOf(json.get("token"));
        Log.d(TAG, "consume: " + token);

        if (mBillingClient != null && mBillingClient.isReady()) {
            ConsumeParams consumeParams = ConsumeParams
                    .newBuilder()
                    .setPurchaseToken(token)
                    .build();
            mBillingClient.consumeAsync(consumeParams, new ConsumeResponseListener() {
                @Override
                public void onConsumeResponse(@NonNull BillingResult billingResult, @NonNull String s) {
                    Map resMap = new HashMap();
                    int code = billingResult.getResponseCode();
                    if (code == BillingResponseCode.OK) {
                    } else {
                        Log.d(TAG, "consume fail " + code + billingResult.getDebugMessage());
                        resMap.put("error", code + billingResult.getDebugMessage());
                        resMap.put("code", code+"");
                    }
                    JsbHelper.callbackToTs("CONSUME_ORDER", resMap);
                }
            });
        } else {
            Map resMap = new HashMap();
            resMap.put("error", "not init");
            JsbHelper.callbackToTs("CONSUME_ORDER", resMap);
        }
    }

//    订阅型商品finish
    public static void acknowledgedPurchase(Map json) {
        String token = String.valueOf(json.get("token"));
        Log.d(TAG, "acknowledgedPurchase: " + token);

        if (mBillingClient != null && mBillingClient.isReady()) {
            AcknowledgePurchaseParams consumeParams = AcknowledgePurchaseParams.newBuilder().setPurchaseToken(token).build();
            mBillingClient.acknowledgePurchase(consumeParams, new AcknowledgePurchaseResponseListener() {
                @Override
                public void onAcknowledgePurchaseResponse(@NonNull BillingResult billingResult) {
                    Map resMap = new HashMap();
                    int code = billingResult.getResponseCode();
                    if (code == BillingResponseCode.OK) {
                    } else {
                        Log.d(TAG, "consume fail " + code + billingResult.getDebugMessage());
                        resMap.put("error", code + billingResult.getDebugMessage());
                    }
                    JsbHelper.callbackToTs("CONSUME_ORDER", resMap);
                }
            });
        } else {
            Map resMap = new HashMap();
            resMap.put("error", "not init");
            JsbHelper.callbackToTs("CONSUME_ORDER", resMap);
        }
    }

    public static void queryPurchasesAsyncAll() {
        Map resMap = new HashMap();
        mBillingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType("inapp").build(),
                new PurchasesResponseListener() {
                    @Override
                    public void onQueryPurchasesResponse(@NonNull BillingResult billingResult1, @NonNull List<Purchase> purchasesList1) {
                        Log.d(TAG, "queryPurchasesAsyncAll1 " + purchasesList1);
                        if (billingResult1.getResponseCode() == BillingResponseCode.OK
                                && purchasesList1 != null) {
                            ArrayList<Object> list1 = new  ArrayList<Object>();
                            for (Purchase purchase : purchasesList1) {
                                list1.add(JSON.parse(purchase.getOriginalJson()));
                            }

                            mBillingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType("subs").build(),
                                    new PurchasesResponseListener() {
                                        @Override
                                        public void onQueryPurchasesResponse(@NonNull BillingResult billingResult2, @NonNull List<Purchase> purchasesList2) {
                                            Log.d(TAG, "queryPurchasesAsyncAll2 " + purchasesList2);

                                            if (billingResult2.getResponseCode() == BillingResponseCode.OK
                                                    && purchasesList2 != null) {
                                                ArrayList<Object> list2 = new  ArrayList<Object>();
                                                for (Purchase purchase : purchasesList2) {
                                                    list2.add(JSON.parse(purchase.getOriginalJson()));
                                                }
                                                ArrayList<Object> list = new ArrayList<Object>();
                                                list.addAll(list1);
                                                list.addAll(list2);
                                                resMap.put("result", list);
                                            } else {
                                                Log.d(TAG, "queryPurchasesAsync2 fail" + billingResult2.getDebugMessage() + " " + billingResult2.getResponseCode());
                                                resMap.put("error", billingResult2.getResponseCode() + billingResult2.getDebugMessage());
                                            }
                                            JsbHelper.callbackToTs("GET_LOST_ORDER_LIST", resMap);
                                        }
                                    });
                        } else {
                            Log.d(TAG, "queryPurchasesAsync1 fail" + billingResult1.getDebugMessage() + " " + billingResult1.getResponseCode());
                            resMap.put("error", billingResult1.getResponseCode() + billingResult1.getDebugMessage());
                            JsbHelper.callbackToTs("GET_LOST_ORDER_LIST", resMap);
                        }
                    }
                });
    }
}
