
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/CanMove.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '77b2eUyP/VOj4cPN2ve8IMY', 'CanMove');
// app/script/model/behavior/CanMove.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseCondition_1 = require("./BaseCondition");
var BTConstant_1 = require("./BTConstant");
// 是否可以移动
var CanMove = /** @class */ (function (_super) {
    __extends(CanMove, _super);
    function CanMove() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    CanMove.prototype.onTick = function (dt) {
        if (this.getTreeBlackboardData('isMove')) {
            return BTConstant_1.BTState.FAILURE; //如果已经移动过了就不能再移动了
        }
        else if (this.target.isHasBuffs(Enums_1.BuffType.STAND_SHIELD, Enums_1.BuffType.TIMIDITY, Enums_1.BuffType.IRREMOVABILITY, Enums_1.BuffType.OVERLORD)) {
            return BTConstant_1.BTState.FAILURE; //立盾状态下 畏惧 限制移动
        }
        return BTConstant_1.BTState.SUCCESS;
    };
    return CanMove;
}(BaseCondition_1.default));
exports.default = CanMove;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQ2FuTW92ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxREFBdUQ7QUFDdkQsaURBQTRDO0FBQzVDLDJDQUF1QztBQUV2QyxTQUFTO0FBQ1Q7SUFBcUMsMkJBQWE7SUFBbEQ7O0lBVUEsQ0FBQztJQVJVLHdCQUFNLEdBQWIsVUFBYyxFQUFVO1FBQ3BCLElBQUksSUFBSSxDQUFDLHFCQUFxQixDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQ3RDLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUEsQ0FBQyxpQkFBaUI7U0FDM0M7YUFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFRLENBQUMsWUFBWSxFQUFFLGdCQUFRLENBQUMsUUFBUSxFQUFFLGdCQUFRLENBQUMsY0FBYyxFQUFFLGdCQUFRLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDckgsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQSxDQUFDLGVBQWU7U0FDekM7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFDTCxjQUFDO0FBQUQsQ0FWQSxBQVVDLENBVm9DLHVCQUFhLEdBVWpEIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnVmZlR5cGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VudW1zXCI7XHJcbmltcG9ydCBCYXNlQ29uZGl0aW9uIGZyb20gXCIuL0Jhc2VDb25kaXRpb25cIjtcclxuaW1wb3J0IHsgQlRTdGF0ZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIjtcclxuXHJcbi8vIOaYr+WQpuWPr+S7peenu+WKqFxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBDYW5Nb3ZlIGV4dGVuZHMgQmFzZUNvbmRpdGlvbiB7XHJcblxyXG4gICAgcHVibGljIG9uVGljayhkdDogbnVtYmVyKSB7XHJcbiAgICAgICAgaWYgKHRoaXMuZ2V0VHJlZUJsYWNrYm9hcmREYXRhKCdpc01vdmUnKSkge1xyXG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5GQUlMVVJFIC8v5aaC5p6c5bey57uP56e75Yqo6L+H5LqG5bCx5LiN6IO95YaN56e75Yqo5LqGXHJcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhcmdldC5pc0hhc0J1ZmZzKEJ1ZmZUeXBlLlNUQU5EX1NISUVMRCwgQnVmZlR5cGUuVElNSURJVFksIEJ1ZmZUeXBlLklSUkVNT1ZBQklMSVRZLCBCdWZmVHlwZS5PVkVSTE9SRCkpIHtcclxuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuRkFJTFVSRSAvL+eri+ebvueKtuaAgeS4iyDnlY/mg6cg6ZmQ5Yi256e75YqoXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBCVFN0YXRlLlNVQ0NFU1NcclxuICAgIH1cclxufSJdfQ==