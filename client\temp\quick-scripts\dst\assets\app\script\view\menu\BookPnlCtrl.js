
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/BookPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '967d5tVPL9GOYfSynsItbWc', 'BookPnlCtrl');
// app/script/view/menu/BookPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PawnObj_1 = require("../../model/area/PawnObj");
var ccclass = cc._decorator.ccclass;
var BookPnlCtrl = /** @class */ (function (_super) {
    __extends(BookPnlCtrl, _super);
    function BookPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pagesNode_ = null; // path://root/pages_n
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.model = null;
        _this.curTab = 0;
        return _this;
    }
    BookPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.INIT_BOOK_BASEINFO_COMPLETE] = this.onInitBookBaseInfoComplete, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_BOOK_COMMENT] = this.onUpdateBookComment, _b.enter = true, _b),
        ];
    };
    BookPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('book');
                return [2 /*return*/];
            });
        });
    };
    BookPnlCtrl.prototype.onEnter = function (tab) {
        this.model.checkInitBookStarMap();
        this.tabsTc_.Tabs(tab || this.curTab || 1);
    };
    BookPnlCtrl.prototype.onRemove = function () {
    };
    BookPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BookPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.curTab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 1) {
            this.showPolicys(node);
        }
        else if (type === 2) {
            this.showPawns(node);
        }
        else if (type === 3) {
            this.showEquips(node);
        }
        else if (type === 4) {
            this.showPortrayal(node);
        }
    };
    // path://root/pages_n/1/view/content/1/item_be
    BookPnlCtrl.prototype.onClickItem = function (event, _) {
        audioMgr.playSFX('click');
        var type = Number(event.target.parent.name), json = event.target.parent.Data;
        if (type === 1) { //政策
            ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', json.id, 'book');
        }
        else if (type === 2) { //士兵
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', new PawnObj_1.default().init(json.id, null, 1, 0, -1).initAnger(), null, 'book');
        }
        else if (type === 3) { //装备
            ViewHelper_1.viewHelper.showPnl('common/EquipBaseInfoBox', json, 'book');
        }
        else if (type === 4) { //画像
            ViewHelper_1.viewHelper.showPnl('common/PortrayalBaseInfo', json, 'book');
        }
    };
    // path://root/pages_n/1/view/content/1/rating_be
    BookPnlCtrl.prototype.onClickRating = function (event, data) {
        audioMgr.playSFX('click');
        var it = event.target.parent, json = it.Data;
        if (json) {
            ViewHelper_1.viewHelper.showPnl('menu/BookRating', Number(it.name), json.id);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 图鉴初始化完成
    BookPnlCtrl.prototype.onInitBookBaseInfoComplete = function (starMap) {
        var _this = this;
        var node = this.pagesNode_.Child(this.curTab);
        node === null || node === void 0 ? void 0 : node.Component(cc.ScrollView).content.children.forEach(function (it) {
            if (it.Data) {
                ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(_this.curTab + '_' + it.Data.id));
            }
        });
    };
    // 更新评论
    BookPnlCtrl.prototype.onUpdateBookComment = function (key) {
        var _a = __read(ut.stringToNumbers(key, '_'), 2), type = _a[0], id = _a[1];
        if (type === this.curTab) {
            var node = this.pagesNode_.Child(this.curTab);
            var it = node === null || node === void 0 ? void 0 : node.Component(cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.id) === id; });
            if (it) {
                ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), this.model.getBookStarByKey(key));
            }
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 政策
    BookPnlCtrl.prototype.showPolicys = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var datas = assetsMgr.getJson('policy').datas;
        var sv = node.Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(datas.length, function (it, i) {
            var json = it.Data = datas[i];
            ResHelper_1.resHelper.loadPolicyIcon(json.id, it.Child('item_be/val'), _this.key);
            it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('policyText.name_' + json.id), 5));
            ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(Enums_1.BookCommentType.POLICY + '_' + json.id));
        });
    };
    // 士兵
    BookPnlCtrl.prototype.showPawns = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(2);
        var datas = assetsMgr.getJson('pawnBase').datas.filter(function (m) { return !!m.spawn_build_id; });
        var sv = node.Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(datas.length, function (it, i) {
            var json = it.Data = datas[i];
            ResHelper_1.resHelper.loadPawnHeadIcon(json.id, it.Child('item_be/val'), _this.key);
            it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.id), 5));
            ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(Enums_1.BookCommentType.PAWN + '_' + json.id));
        });
    };
    // 装备
    BookPnlCtrl.prototype.showEquips = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(3);
        var datas = assetsMgr.getJson('equipBase').datas, equips = datas.filter(function (m) { return !m.exclusive_pawn && !!m.sort; }).sort(function (a, b) { return a.sort - b.sort; }), exlusives = datas.filter(function (m) { return !!m.exclusive_pawn && !!m.sort; }).sort(function (a, b) { return a.exclusive_pawn - b.exclusive_pawn; });
        var equipNode = node.Child('view/content/equip/list'), exclusiveNode = node.Child('view/content/exclusive/list');
        equipNode.Items(equips, function (it, data) {
            var json = it.Data = data;
            ResHelper_1.resHelper.loadEquipIcon(json.id, it.Child('item_be/val'), _this.key);
            it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + json.id), 5));
            ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(Enums_1.BookCommentType.EQUIP + '_' + json.id));
        });
        exclusiveNode.Items(exlusives, function (it, data) {
            var json = it.Data = data;
            ResHelper_1.resHelper.loadEquipIcon(json.id, it.Child('item_be/val'), _this.key);
            it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + json.id), 5));
            ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(Enums_1.BookCommentType.EQUIP + '_' + json.id));
        });
    };
    // 画像
    BookPnlCtrl.prototype.showPortrayal = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(4);
        var datas = assetsMgr.getJson('portrayalBase').datas;
        var sv = node.Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(datas.length, function (it, i) {
            var json = it.Data = datas[i], iconNode = it.Child('mask/icon');
            ResHelper_1.resHelper.loadPortrayalImage(json.id, iconNode, _this.key);
            iconNode.setPosition(ut.stringToVec2(json.ui_offset));
            it.Child('name/val').setLocaleKey('portrayalText.name_' + json.id);
            ViewHelper_1.viewHelper.updateBookStar(it.Child('rating_be'), _this.model.getBookStarByKey(Enums_1.BookCommentType.PORTRAYAL + '_' + json.id));
        });
    };
    BookPnlCtrl = __decorate([
        ccclass
    ], BookPnlCtrl);
    return BookPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BookPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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