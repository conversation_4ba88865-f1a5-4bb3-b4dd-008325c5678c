
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/AllianceMembersPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'faf51rgOthMiImQsKLycuX+', 'AllianceMembersPnlCtrl');
// app/script/view/build/AllianceMembersPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AllianceMembersPnlCtrl = /** @class */ (function (_super) {
    __extends(AllianceMembersPnlCtrl, _super);
    function AllianceMembersPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    AllianceMembersPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AllianceMembersPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AllianceMembersPnlCtrl.prototype.onEnter = function (cb) {
        var _this = this;
        this.cb = cb;
        var list = GameHelper_1.gameHpr.alliance.getMembers().filter(function (m) { return m.uid !== GameHelper_1.gameHpr.getUid(); });
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Items(list, function (it, data) {
            it.Data = data;
            var info = GameHelper_1.gameHpr.getPlayerInfo(data.uid);
            var head = it.Child('head');
            head.Child('name', cc.Label).string = ut.nameFormator(info.nickname || '???', 7);
            ViewHelper_1.viewHelper.updatePositionView(head.Child('pos_be'), info.mainCityIndex, false);
            ResHelper_1.resHelper.loadPlayerHead(head.Child('icon'), info.headIcon, _this.key);
        });
    };
    AllianceMembersPnlCtrl.prototype.onRemove = function () {
    };
    AllianceMembersPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/head/pos_be
    AllianceMembersPnlCtrl.prototype.onClickPos = function (event, data) {
        var index = event.target.Data;
        if (index) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos(index);
        }
    };
    // path://root/list_sv/view/content/item/give_be
    AllianceMembersPnlCtrl.prototype.onClickGive = function (event, data) {
        var member = event.target.parent.Data;
        this.cb && this.cb(member);
        this.hide();
    };
    AllianceMembersPnlCtrl = __decorate([
        ccclass
    ], AllianceMembersPnlCtrl);
    return AllianceMembersPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AllianceMembersPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxBbGxpYW5jZU1lbWJlcnNQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDZEQUF5RDtBQUN6RCwyREFBMEQ7QUFDMUQsNkRBQTREO0FBRXBELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQW9ELDBDQUFjO0lBQWxFO1FBQUEscUVBNERDO1FBMURHLDBCQUEwQjtRQUNsQixhQUFPLEdBQWtCLElBQUksQ0FBQSxDQUFDLHNCQUFzQjtRQUM1RCxNQUFNO1FBRUUsUUFBRSxHQUFhLElBQUksQ0FBQTs7UUFpRDNCLE1BQU07UUFDTixpSEFBaUg7UUFFakgsaUhBQWlIO0lBRXJILENBQUM7SUFwRFUsZ0RBQWUsR0FBdEI7UUFDSSxPQUFPLEVBQUUsQ0FBQTtJQUNiLENBQUM7SUFFWSx5Q0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSx3Q0FBTyxHQUFkLFVBQWUsRUFBWTtRQUEzQixpQkFhQztRQVpHLElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFBO1FBQ1osSUFBTSxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxRQUFRLENBQUMsVUFBVSxFQUFFLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxvQkFBTyxDQUFDLE1BQU0sRUFBRSxFQUExQixDQUEwQixDQUFDLENBQUE7UUFDbEYsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUM3QixJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQzFCLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQzlCLEVBQUUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1lBQ2QsSUFBTSxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzVDLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7WUFDN0IsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxRQUFRLElBQUksS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ2hGLHVCQUFVLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQzlFLHFCQUFTLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLFFBQVEsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDekUsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRU0seUNBQVEsR0FBZjtJQUNBLENBQUM7SUFFTSx3Q0FBTyxHQUFkO1FBQ0ksU0FBUyxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtJQUMzQyxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQixvREFBb0Q7SUFDcEQsMkNBQVUsR0FBVixVQUFXLEtBQTBCLEVBQUUsSUFBWTtRQUMvQyxJQUFNLEtBQUssR0FBVyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQTtRQUN2QyxJQUFJLEtBQUssRUFBRTtZQUNQLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtZQUNYLG9CQUFPLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQy9CO0lBQ0wsQ0FBQztJQUVELGdEQUFnRDtJQUNoRCw0Q0FBVyxHQUFYLFVBQVksS0FBMEIsRUFBRSxJQUFZO1FBQ2hELElBQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQTtRQUN2QyxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDMUIsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO0lBQ2YsQ0FBQztJQXREZ0Isc0JBQXNCO1FBRDFDLE9BQU87T0FDYSxzQkFBc0IsQ0E0RDFDO0lBQUQsNkJBQUM7Q0E1REQsQUE0REMsQ0E1RG1ELEVBQUUsQ0FBQyxXQUFXLEdBNERqRTtrQkE1RG9CLHNCQUFzQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEFsbGlhbmNlTWVtYmVyc1BubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgbGlzdFN2XzogY2MuU2Nyb2xsVmlldyA9IG51bGwgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdlxuICAgIC8vQGVuZFxuXG4gICAgcHJpdmF0ZSBjYjogRnVuY3Rpb24gPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIoY2I6IEZ1bmN0aW9uKSB7XG4gICAgICAgIHRoaXMuY2IgPSBjYlxuICAgICAgICBjb25zdCBsaXN0ID0gZ2FtZUhwci5hbGxpYW5jZS5nZXRNZW1iZXJzKCkuZmlsdGVyKG0gPT4gbS51aWQgIT09IGdhbWVIcHIuZ2V0VWlkKCkpXG4gICAgICAgIHRoaXMubGlzdFN2Xy5zdG9wQXV0b1Njcm9sbCgpXG4gICAgICAgIHRoaXMubGlzdFN2Xy5jb250ZW50LnkgPSAwXG4gICAgICAgIHRoaXMubGlzdFN2Xy5JdGVtcyhsaXN0LCAoaXQsIGRhdGEpID0+IHtcbiAgICAgICAgICAgIGl0LkRhdGEgPSBkYXRhXG4gICAgICAgICAgICBjb25zdCBpbmZvID0gZ2FtZUhwci5nZXRQbGF5ZXJJbmZvKGRhdGEudWlkKVxuICAgICAgICAgICAgY29uc3QgaGVhZCA9IGl0LkNoaWxkKCdoZWFkJylcbiAgICAgICAgICAgIGhlYWQuQ2hpbGQoJ25hbWUnLCBjYy5MYWJlbCkuc3RyaW5nID0gdXQubmFtZUZvcm1hdG9yKGluZm8ubmlja25hbWUgfHwgJz8/PycsIDcpXG4gICAgICAgICAgICB2aWV3SGVscGVyLnVwZGF0ZVBvc2l0aW9uVmlldyhoZWFkLkNoaWxkKCdwb3NfYmUnKSwgaW5mby5tYWluQ2l0eUluZGV4LCBmYWxzZSlcbiAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkUGxheWVySGVhZChoZWFkLkNoaWxkKCdpY29uJyksIGluZm8uaGVhZEljb24sIHRoaXMua2V5KVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICAgICAgYXNzZXRzTWdyLnJlbGVhc2VUZW1wUmVzQnlUYWcodGhpcy5rZXkpXG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L2xpc3Rfc3Yvdmlldy9jb250ZW50L2l0ZW0vaGVhZC9wb3NfYmVcbiAgICBvbkNsaWNrUG9zKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgaW5kZXg6IG51bWJlciA9IGV2ZW50LnRhcmdldC5EYXRhXG4gICAgICAgIGlmIChpbmRleCkge1xuICAgICAgICAgICAgdGhpcy5oaWRlKClcbiAgICAgICAgICAgIGdhbWVIcHIuZ290b1RhcmdldFBvcyhpbmRleClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L2xpc3Rfc3Yvdmlldy9jb250ZW50L2l0ZW0vZ2l2ZV9iZVxuICAgIG9uQ2xpY2tHaXZlKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgbWVtYmVyID0gZXZlbnQudGFyZ2V0LnBhcmVudC5EYXRhXG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYihtZW1iZXIpXG4gICAgICAgIHRoaXMuaGlkZSgpXG4gICAgfVxuICAgIC8vQGVuZFxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG59XG4iXX0=