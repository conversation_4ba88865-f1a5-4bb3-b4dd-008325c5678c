
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ReddotHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '45b7fK4gvNFipEzPyqKmP9n', 'ReddotHelper');
// app/script/common/helper/ReddotHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reddotHelper = void 0;
// 红点管理
var ReddotHelper = /** @class */ (function () {
    function ReddotHelper() {
        this.dataMap = {};
        this.checkDataMap = new Map(); //数据map
    }
    ReddotHelper.prototype.clean = function () {
        this.dataMap = {};
        this.checkDataMap.clear();
    };
    // 注册红点检测
    ReddotHelper.prototype.register = function (key, cb, target, interval, elapsed) {
        interval = interval || 30;
        elapsed = elapsed === undefined ? interval : elapsed;
        var data = {
            cb: cb,
            target: target,
            interval: interval,
            elapsed: elapsed,
            pause: false
        };
        var arr = this.checkDataMap.get(key);
        if (arr) {
            arr.push(data);
        }
        else {
            this.checkDataMap.set(key, [data]);
        }
    };
    ReddotHelper.prototype.unregister = function (key) {
        this.checkDataMap.delete(key);
        var data = this.dataMap[key];
        if (data) {
            data.val = false; //这里需要先设置一下 因为外部可能还在引用
            // delete this.dataMap[key]
        }
    };
    ReddotHelper.prototype.getRegisterCount = function (key) {
        var _a;
        return ((_a = this.checkDataMap.get(key)) === null || _a === void 0 ? void 0 : _a.length) || 0;
    };
    ReddotHelper.prototype.set = function (key, val, pause) {
        this.get(key).val = val;
        this.resetElapsedTime(key, pause);
    };
    ReddotHelper.prototype.check = function (key) {
        var _a;
        var data = this.get(key);
        data && ((_a = this.checkDataMap.get(key)) === null || _a === void 0 ? void 0 : _a.forEach(function (m) {
            m.elapsed = 0;
            data.val = !!m.cb.call(m.target, data.val, key);
        }));
    };
    // 重置时间
    ReddotHelper.prototype.resetElapsedTime = function (key, pause) {
        var _a;
        (_a = this.checkDataMap.get(key)) === null || _a === void 0 ? void 0 : _a.forEach(function (m) {
            m.elapsed = 0;
            m.pause = !!pause;
        });
    };
    ReddotHelper.prototype.get = function (key) {
        var data = this.dataMap[key];
        if (!data) {
            data = this.dataMap[key] = { key: key, val: false };
        }
        return data;
    };
    ReddotHelper.prototype.pause = function (key, val) {
        var _a;
        (_a = this.checkDataMap.get(key)) === null || _a === void 0 ? void 0 : _a.forEach(function (m) {
            m.pause = val;
        });
    };
    ReddotHelper.prototype.update = function (dt) {
        var _this = this;
        this.checkDataMap.forEach(function (arr, key) {
            var data = _this.get(key);
            arr.forEach(function (m) {
                if (m.pause) {
                    return;
                }
                m.elapsed += dt;
                if (m.elapsed >= m.interval) {
                    m.elapsed = 0;
                    data.val = !!m.cb.call(m.target, data.val, key);
                }
            });
        });
    };
    return ReddotHelper;
}());
exports.reddotHelper = new ReddotHelper();
if (cc.sys.isBrowser) {
    window['reddotHelper'] = exports.reddotHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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