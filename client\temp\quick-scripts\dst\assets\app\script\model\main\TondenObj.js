
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/TondenObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8f9f8GkANZATJbmnalqObvK', 'TondenObj');
// app/script/model/main/TondenObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 屯田信息
var TondenObj = /** @class */ (function () {
    function TondenObj() {
        this.index = 0;
        this.armyUid = '';
        this.surplusTime = 0; //剩余建造的时间
        this.getTime = 0; //获取时间
    }
    TondenObj.prototype.init = function (index, data) {
        this.index = index;
        this.armyUid = data.auid;
        this.surplusTime = data.time;
        this.getTime = Date.now();
        return this;
    };
    // 获取实际的剩余时间
    TondenObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 是否开始
    TondenObj.prototype.isRuning = function () {
        return this.surplusTime > 0;
    };
    return TondenObj;
}());
exports.default = TondenObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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