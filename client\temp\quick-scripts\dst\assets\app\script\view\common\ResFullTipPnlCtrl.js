
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ResFullTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'db57cGv9PpE3Yd9vI8USyQD', 'ResFullTipPnlCtrl');
// app/script/view/common/ResFullTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ResFullTipPnlCtrl = /** @class */ (function (_super) {
    __extends(ResFullTipPnlCtrl, _super);
    function ResFullTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descLbl_ = null; // path://root/content/desc_l
        _this.itemsSv_ = null; // path://root/content/items_sv
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    ResFullTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ResFullTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ResFullTipPnlCtrl.prototype.onEnter = function (items, cb) {
        var _this = this;
        this.cb = cb;
        this.descLbl_.setLocaleKey(GameHelper_1.gameHpr.isInLobby() ? 'ui.lobby_claim_res_tip' : 'ui.res_full_tip_desc');
        this.itemsSv_.Items(items, function (it, data) { return ViewHelper_1.viewHelper.updateItemByCTypeOne(it, data, _this.key); });
    };
    ResFullTipPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    ResFullTipPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    ResFullTipPnlCtrl.prototype.onClickButtons = function (event, data) {
        this.cb && this.cb(event.target.name === 'ok');
        this.hide();
    };
    ResFullTipPnlCtrl = __decorate([
        ccclass
    ], ResFullTipPnlCtrl);
    return ResFullTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResFullTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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