
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/test_battle_new.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5514bgUw8VNuZBKDXLV2JnI', 'test_battle_new');
// app/script/model/fsp/test_battle_new.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var testConfig_1 = require("./testConfig");
var BattleTest = /** @class */ (function () {
    function BattleTest() {
        // 我方军队
        this.myArmys = [
            //以下为野地阵容测试  勿删//
            //  简单1 简单2 1级地<=1~2
            {
                pawns: [{ id: 3201, lv: 1 },
                    { id: 3201, lv: 1 },
                ]
            },
        ];
        //敌方军队
        this.enemyArmys = [
            {
                pawns: [
                    {
                        id: 3401, lv: 6,
                        portrayal: { id: 340102, attrs: [[0, 1, 200], [0, 2, 14], [1, 36, 100], [2, 50017, 0]] },
                        equip: { id: 6108, attrs: [[0, 1, 100], [0, 2, 10], [2, 3, 200, 40]] }
                    },
                ]
            },
        ];
        this.attackIndex = 0;
        this.frameSeq = [];
        this.tagSeq = {};
        this.areaArmy = {};
    }
    BattleTest.prototype._time2Frameindex = function (time) {
        var tx = 1 / 20;
        var index = Math.max(1, Math.floor(time / tx));
        return index;
    };
    BattleTest.prototype._setAttackIndex = function (fighters, startAttackIndex) {
        var e_1, _a;
        try {
            for (var fighters_1 = __values(fighters), fighters_1_1 = fighters_1.next(); !fighters_1_1.done; fighters_1_1 = fighters_1.next()) {
                var fighter = fighters_1_1.value;
                fighter.attackIndex = ++startAttackIndex;
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (fighters_1_1 && !fighters_1_1.done && (_a = fighters_1.return)) _a.call(fighters_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    // 打地战
    BattleTest.prototype._battleLand = function (landId, dis, dir, mul, testArmy) {
        var _this = this;
        if (dir === void 0) { dir = 2; }
        if (mul === void 0) { mul = 1; }
        if (testArmy === void 0) { testArmy = []; }
        var index = 1, owner = GameHelper_1.gameHpr.getUid();
        this.attackIndex = 0;
        var armys = [], fighters = [], towers = [], builds = [];
        var point = { x: 5, y: 0 };
        if (dir === 0) {
            point = { x: 5, y: 10 };
        }
        else if (dir === 1) {
            point = { x: 10, y: 5 };
        }
        else if (dir === 3) {
            point = { x: 0, y: 5 };
        }
        var frameSet = new Set();
        // 保存等待时间进入的军队
        var frameSeq = [];
        // 保存等待前置军队的
        var tagSeq = {};
        var listened = false;
        // 分配帧
        var sumFrame = function (armyList, camp) {
            var e_2, _a;
            var _loop_1 = function (army) {
                var curFighters = army.pawns.map(function (pawn) {
                    return {
                        uid: pawn.uid,
                        camp: camp,
                        attackIndex: _this.attackIndex++,
                    };
                });
                // 需等待前置军队的
                if (army.preArmyTag) {
                    if (!tagSeq[army.preArmyTag]) {
                        var hasArmy = armyList.find(function (x) { return x.tag === army.preArmyTag; });
                        if (!hasArmy) {
                            cc.warn('找不到前置军队,忽略此军队', army, armyList);
                            return "continue";
                        }
                        tagSeq[army.preArmyTag] = [];
                    }
                    var seq = tagSeq[army.preArmyTag];
                    seq.push({
                        type: 1,
                        fighters: curFighters,
                        army: army,
                        currentFrameIndex: 0,
                    });
                    listened = true;
                    return "continue";
                }
                // 需等待时间进入的军队
                if (army.waitTime) {
                    var frameIndex = _this._time2Frameindex(army.waitTime);
                    while (frameSet.has(frameIndex)) {
                        frameIndex++;
                    }
                    frameSet.add(frameIndex);
                    frameSeq[frameIndex] = {
                        type: 1,
                        fighters: curFighters,
                        army: army,
                        currentFrameIndex: frameIndex,
                    };
                    listened = true;
                    return "continue";
                }
                // 首帧进入的军队
                fighters.push.apply(fighters, __spread(curFighters));
                armys.push(army);
            };
            try {
                for (var armyList_1 = __values(armyList), armyList_1_1 = armyList_1.next(); !armyList_1_1.done; armyList_1_1 = armyList_1.next()) {
                    var army = armyList_1_1.value;
                    _loop_1(army);
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (armyList_1_1 && !armyList_1_1.done && (_a = armyList_1.return)) _a.call(armyList_1);
                }
                finally { if (e_2) throw e_2.error; }
            }
        };
        var areaArmy = {};
        var pawnCountMap = {};
        // 加入我方军队
        if (testArmy.length == 0) {
            testArmy = this.myArmys;
        }
        var myArmys = testArmy.map(function (m, i) {
            var army = {
                uid: ut.UID(),
                name: '编队' + i,
                index: index,
                owner: owner,
                state: 3,
                enterDir: dir,
                pawns: m.pawns.map(function (x) {
                    if (pawnCountMap[x.id + '_' + x.lv]) {
                        pawnCountMap[x.id + '_' + x.lv] += 1;
                    }
                    else {
                        pawnCountMap[x.id + '_' + x.lv] = 1;
                    }
                    return {
                        index: index,
                        uid: ut.UID(),
                        point: { x: point.x, y: point.y },
                        id: x.id,
                        lv: x.lv,
                        equip: x.equip,
                    };
                })
            };
            areaArmy[army.uid] = army;
            return army;
        });
        sumFrame(myArmys, 2);
        // 加入敌方军队
        var conf = GameHelper_1.gameHpr.getAreaPawnConfInfo(index, landId, dis);
        conf.armys.forEach(function (army) {
            army.pawns.sort(function (a, b) {
                var ap = assetsMgr.getJsonData('pawnBase', a.id).attack_speed;
                var bp = assetsMgr.getJsonData('pawnBase', b.id).attack_speed;
                var aw = ap * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(a.point, point));
                var bw = bp * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(b.point, point));
                return bw - aw;
            });
            // console.log(this.attackIndex, 'attackIdx')
            army.pawns.forEach(function (pawn) {
                fighters.push({
                    uid: pawn.uid,
                    camp: 1,
                    waitRound: 0,
                    attackIndex: ++_this.attackIndex,
                });
            });
        });
        // 加入箭塔
        if (conf.hp[1] > 0) {
            towers.push({
                id: 7002,
                lv: 1,
                uid: ut.UID(),
                camp: 1,
                attackIndex: ++this.attackIndex,
            });
            builds.push({ id: 2102, point: { x: 5, y: 5 } });
        }
        armys.pushArr(conf.armys);
        var onStep = listened ? this._onFrame.bind(this) : null;
        this.areaArmy = areaArmy;
        this.frameSeq = frameSeq;
        this.tagSeq = tagSeq;
        // 设置回放数据
        GameHelper_1.gameHpr.playback.setRecordData({
            index: index,
            frames: [{
                    type: 0,
                    camp: 1,
                    owner: '',
                    cityId: 0,
                    randSeed: 114200,
                    fps: 20,
                    hp: conf.hp,
                    builds: builds,
                    armys: armys,
                    fighters: fighters,
                    towers: towers,
                    mul: mul,
                    onStep: onStep
                }],
        }).then(function () {
            eventCenter.off(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
            eventCenter.on(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
            function onAreaBattleEnd(index) {
                eventCenter.off(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
                var area = GameHelper_1.gameHpr.areaCenter.getLookArea();
                var has = false, cnt = 0;
                cc.log('----------------------存活士兵----------------------');
                area === null || area === void 0 ? void 0 : area.armys.forEach(function (m) {
                    if (m.owner === owner) {
                        m.pawns.forEach(function (pawn) {
                            var key = pawn.id + '_' + pawn.lv;
                            if (pawnCountMap[key]) {
                                pawnCountMap[key] -= 1;
                            }
                            cnt += 1;
                            cc.log(cnt + ' id=' + pawn.id + ', lv=' + pawn.lv + ', hp=' + pawn.getHpText() + ' (' + (pawn.getHpRatio() * 100).toFixed(1) + '%)');
                            has = true;
                        });
                    }
                });
                if (!has) {
                    cc.log('无一生还!');
                }
                cc.log('----------------------损失士兵----------------------');
                has = false;
                for (var key in pawnCountMap) {
                    var _a = __read(key.split('_'), 2), id = _a[0], lv = _a[1];
                    var count = pawnCountMap[key];
                    if (count) {
                        has = true;
                        cc.log('id=' + id + ', lv=' + lv + ', count=' + count);
                    }
                }
                if (!has) {
                    cc.log('没有损失!');
                }
                cc.log('---------------------------------------------------');
            }
            ViewHelper_1.viewHelper.gotoWind('playback');
        });
    };
    BattleTest.prototype.getPawnInfoByHeroId = function (id, lv) {
        var pawnId = Math.floor(id / 100);
        var json = assetsMgr.getJsonData('portrayalBase', id);
        var skillJson = assetsMgr.getJsonData('portrayalSkill', json.skill);
        var attrs = [
            [0, 1, ut.stringToNumbers(json.hp, ',').random()],
            [0, 2, ut.stringToNumbers(json.attack, ',').random()],
            [1, json.skill, ut.stringToNumbers(skillJson.value, ',').random()],
            [2, ut.stringToNumbers(json.strategy, '|').random(), 0],
        ];
        return { id: pawnId, lv: lv, portrayal: { id: id, attrs: attrs } };
    };
    // 军队对抗
    BattleTest.prototype._battle = function (isHero, myHeroId, enemyHeroId, count, skinId) {
        if (skinId === void 0) { skinId = []; }
        return __awaiter(this, void 0, void 0, function () {
            var ct, ct, ct, ct, index, owner, armys, fighters, towers, frameSet, frameSeq, tagSeq, listened, sumFrame, areaArmy, myArmys, enemyArmys, onStep;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (isHero) {
                            if (myHeroId) {
                                this.myArmys = [];
                                this.myArmys.push({ pawns: [this.getPawnInfoByHeroId(myHeroId, 6)] });
                                if (count && count > 1) {
                                    ct = count - 1;
                                    while (ct > 0) {
                                        ct--;
                                        this.myArmys.push({ pawns: [this.getPawnInfoByHeroId(myHeroId, 6)] });
                                    }
                                }
                            }
                            if (enemyHeroId) {
                                this.enemyArmys = [];
                                this.enemyArmys.push({ pawns: [this.getPawnInfoByHeroId(enemyHeroId, 6)] });
                                if (count && count > 1) {
                                    ct = count - 1;
                                    while (ct > 0) {
                                        ct--;
                                        this.enemyArmys.push({ pawns: [this.getPawnInfoByHeroId(enemyHeroId, 6)] });
                                    }
                                }
                            }
                        }
                        else {
                            if (myHeroId) {
                                this.myArmys = [];
                                this.myArmys.push({ pawns: [{ id: myHeroId, lv: 6, skinId: skinId[0] }] });
                                if (count && count > 1) {
                                    ct = count - 1;
                                    while (ct > 0) {
                                        ct--;
                                        this.myArmys.push({ pawns: [{ id: myHeroId, lv: 6, skinId: skinId[0] }] });
                                    }
                                }
                            }
                            if (enemyHeroId) {
                                this.enemyArmys = [];
                                this.enemyArmys.push({ pawns: [{ id: enemyHeroId, lv: 6, skinId: skinId[1] }] });
                                if (count && count > 1) {
                                    ct = count - 1;
                                    while (ct > 0) {
                                        ct--;
                                        this.enemyArmys.push({ pawns: [{ id: enemyHeroId, lv: 6, skinId: skinId[1] }] });
                                    }
                                }
                            }
                        }
                        index = 1, owner = GameHelper_1.gameHpr.getUid();
                        this.attackIndex = 0;
                        armys = [], fighters = [], towers = [];
                        frameSet = new Set();
                        frameSeq = [];
                        tagSeq = {};
                        listened = false;
                        sumFrame = function (armyList, camp) {
                            var e_3, _a;
                            var _loop_2 = function (army) {
                                var curFighters = army.pawns.map(function (pawn) {
                                    return {
                                        uid: pawn.uid,
                                        camp: camp,
                                        attackIndex: ++_this.attackIndex,
                                    };
                                });
                                // 需等待前置军队的
                                if (army.preArmyTag) {
                                    if (!tagSeq[army.preArmyTag]) {
                                        var hasArmy = armyList.find(function (x) { return x.tag === army.preArmyTag; });
                                        if (!hasArmy) {
                                            cc.warn('找不到前置军队,忽略此军队', army, armyList);
                                            return "continue";
                                        }
                                        tagSeq[army.preArmyTag] = [];
                                    }
                                    var seq = tagSeq[army.preArmyTag];
                                    var waitTime = 0;
                                    if (army.waitTime) {
                                        waitTime = army.waitTime;
                                    }
                                    var frame = _this._time2Frameindex(waitTime);
                                    seq.push({
                                        type: 1,
                                        fighters: curFighters,
                                        army: army,
                                        frame: frame
                                    });
                                    listened = true;
                                    return "continue";
                                }
                                // 需等待时间进入的军队
                                if (army.waitTime) {
                                    var frameIndex = _this._time2Frameindex(army.waitTime);
                                    while (frameSet.has(frameIndex)) {
                                        frameIndex++;
                                    }
                                    frameSet.add(frameIndex);
                                    frameSeq[frameIndex] = {
                                        type: 1,
                                        fighters: curFighters,
                                        army: army,
                                        currentFrameIndex: frameIndex,
                                    };
                                    listened = true;
                                    return "continue";
                                }
                                // 首帧进入的军队
                                fighters.push.apply(fighters, __spread(curFighters));
                                armys.push(army);
                            };
                            try {
                                for (var armyList_2 = __values(armyList), armyList_2_1 = armyList_2.next(); !armyList_2_1.done; armyList_2_1 = armyList_2.next()) {
                                    var army = armyList_2_1.value;
                                    _loop_2(army);
                                }
                            }
                            catch (e_3_1) { e_3 = { error: e_3_1 }; }
                            finally {
                                try {
                                    if (armyList_2_1 && !armyList_2_1.done && (_a = armyList_2.return)) _a.call(armyList_2);
                                }
                                finally { if (e_3) throw e_3.error; }
                            }
                        };
                        areaArmy = {};
                        myArmys = this.myArmys.map(function (m, i) {
                            m.uid = ut.UID();
                            m.name = '我方' + i;
                            m.index = index;
                            m.owner = owner;
                            m.state = 3;
                            m.enterDir = 2;
                            m.pawns.forEach(function (x) {
                                x.index = index;
                                x.uid = ut.UID();
                                x.point = { x: 5, y: 0 };
                            });
                            areaArmy[m.uid] = m;
                            return m;
                        });
                        enemyArmys = this.enemyArmys.map(function (m, i) {
                            m.uid = ut.UID();
                            m.name = '敌方' + i;
                            m.index = index;
                            m.owner = '1111111';
                            m.state = 3;
                            m.enterDir = 0;
                            m.pawns.forEach(function (x) {
                                x.index = index;
                                x.uid = ut.UID();
                                x.point = { x: 5, y: 10 };
                            });
                            areaArmy[m.uid] = m;
                            return m;
                        });
                        sumFrame(myArmys, 2);
                        sumFrame(enemyArmys, 1);
                        // // 加入箭塔
                        // fighters.push({
                        //     towerId: 7001,
                        //     towerLv: 1,
                        //     uid: ut.UID(),
                        //     camp: 1,
                        //     attackIndex: ++this.attackIndex,
                        //     point: { x: 5, y: 5 },
                        //     waitRound: 0,
                        // })
                        console.log(fighters, 'fighters');
                        onStep = listened ? this._onFrame.bind(this) : null;
                        this.areaArmy = areaArmy;
                        this.frameSeq = frameSeq;
                        this.tagSeq = tagSeq;
                        // 设置回放数据
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordData({
                                index: index,
                                frames: [{
                                        type: 0,
                                        camp: 1,
                                        owner: '1111111',
                                        cityId: 0,
                                        randSeed: 114200,
                                        fps: 20,
                                        hp: [50, 50],
                                        armys: armys,
                                        fighters: fighters,
                                        towers: towers,
                                    }, {
                                        currentFrameIndex: 30000,
                                        type: 200,
                                    }],
                                beginTime: 0,
                                onStep: onStep
                            })
                            // debugger
                        ];
                    case 1:
                        // 设置回放数据
                        _a.sent();
                        // debugger
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    BattleTest.prototype._onFrame = function (frameIndex, area) {
        var e_4, _a, e_5, _b, e_6, _c;
        var _d, _e;
        var fsp = area.getFspModel();
        if (!fsp)
            return;
        var frameData = this.frameSeq[frameIndex];
        if (frameData) {
            var fighters = (_d = area.getFspModel()) === null || _d === void 0 ? void 0 : _d.getBattleController().getFighters();
            var maxAttackIndex = 0;
            if (fighters) {
                try {
                    for (var fighters_2 = __values(fighters), fighters_2_1 = fighters_2.next(); !fighters_2_1.done; fighters_2_1 = fighters_2.next()) {
                        var fighter = fighters_2_1.value;
                        maxAttackIndex = Math.max(maxAttackIndex, fighter.attackIndex);
                    }
                }
                catch (e_4_1) { e_4 = { error: e_4_1 }; }
                finally {
                    try {
                        if (fighters_2_1 && !fighters_2_1.done && (_a = fighters_2.return)) _a.call(fighters_2);
                    }
                    finally { if (e_4) throw e_4.error; }
                }
            }
            // this._setAttackIndex(frameData.fighters, maxAttackIndex)
            // this._handlePlayBackData(frameData)
            fsp.checkHasFrameDataItem(frameData);
        }
        if (this.tagSeq && frameIndex > 1) {
            var armys = area.armys;
            var tagLive = new Set;
            try {
                for (var armys_1 = __values(armys), armys_1_1 = armys_1.next(); !armys_1_1.done; armys_1_1 = armys_1.next()) {
                    var army = armys_1_1.value;
                    var tag = (_e = this.areaArmy[army.uid]) === null || _e === void 0 ? void 0 : _e.tag;
                    tag && tagLive.add(tag);
                }
            }
            catch (e_5_1) { e_5 = { error: e_5_1 }; }
            finally {
                try {
                    if (armys_1_1 && !armys_1_1.done && (_b = armys_1.return)) _b.call(armys_1);
                }
                finally { if (e_5) throw e_5.error; }
            }
            for (var tag in this.tagSeq) {
                if (tagLive.has(tag)) {
                    continue;
                }
                cc.log('tag', tag, '已死亡, 添加后继士兵');
                var seq = this.tagSeq[tag];
                delete this.tagSeq[tag];
                try {
                    for (var seq_1 = (e_6 = void 0, __values(seq)), seq_1_1 = seq_1.next(); !seq_1_1.done; seq_1_1 = seq_1.next()) {
                        var data = seq_1_1.value;
                        var frame = data.frame + frameIndex;
                        while (this.frameSeq[frame]) {
                            frame++;
                        }
                        console.log('frame', frame, 'data', data);
                        this.frameSeq[frame] = data;
                    }
                }
                catch (e_6_1) { e_6 = { error: e_6_1 }; }
                finally {
                    try {
                        if (seq_1_1 && !seq_1_1.done && (_c = seq_1.return)) _c.call(seq_1);
                    }
                    finally { if (e_6) throw e_6.error; }
                }
            }
        }
    };
    /** 开始测试 空参数表示对战 */
    BattleTest.prototype.begin = function (landId, dis, dir, mul) {
        if (dir === void 0) { dir = 2; }
        if (mul === void 0) { mul = 1; }
        if (!landId) {
            this._battle();
        }
        else {
            this._battleLand(landId, dis, dir, mul);
        }
    };
    BattleTest.prototype.beginByArmy = function (landId, dis, testArmy, dir, mul) {
        if (dir === void 0) { dir = 2; }
        if (mul === void 0) { mul = 1; }
        if (!landId) {
            this._battle();
        }
        else {
            this._battleLand(landId, dis, dir, mul, testArmy);
        }
    };
    BattleTest.prototype.begin1v1 = function (heroId1, heroId2, count, skinId) {
        if (heroId1 >= 10000) {
            this._battle(true, heroId1, heroId2, count, skinId);
        }
        else {
            this._battle(false, heroId1, heroId2, count, skinId);
        }
    };
    BattleTest.prototype.beginConfig = function (id, type, dir, mul) {
        if (dir === void 0) { dir = 2; }
        if (mul === void 0) { mul = 1; }
        var armyArr = testConfig_1.ARMY_CONF[id];
        if (!armyArr) {
            return console.log('没找到相关配置 id: ' + id);
        }
        var dis = id % 1000;
        var landId = type * 100 + (id - dis) / 1000;
        this._battleLand(landId, dis, dir, mul, armyArr);
    };
    return BattleTest;
}());
if (cc.sys.isBrowser) {
    window['testBattle'] = new BattleTest();
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxmc3BcXHRlc3RfYmF0dGxlX25ldy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwwREFBb0Q7QUFDcEQsNkRBQXdEO0FBQ3hELDJEQUF5RDtBQUN6RCw2REFBMkQ7QUFHM0QsMkNBQXdDO0FBWXhDO0lBQUE7UUFFSSxPQUFPO1FBQ1AsWUFBTyxHQUFnQjtZQUNuQixpQkFBaUI7WUFFakIsb0JBQW9CO1lBQ3BCO2dCQUNJLEtBQUssRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFO29CQUMzQixFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsRUFBRSxFQUFFLENBQUMsRUFBRTtpQkFDbEI7YUFDSjtTQStwQkosQ0FBQTtRQUVELE1BQU07UUFDTixlQUFVLEdBQWdCO1lBQ3RCO2dCQUNJLEtBQUssRUFBRTtvQkFDSDt3QkFDSSxFQUFFLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxDQUFDO3dCQUNmLFNBQVMsRUFBRSxFQUFFLEVBQUUsRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUU7d0JBQ3hGLEtBQUssRUFBRSxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUU7cUJBQ3pFO2lCQUNKO2FBQ0o7U0FtRUosQ0FBQTtRQUVPLGdCQUFXLEdBQUcsQ0FBQyxDQUFBO1FBQ2YsYUFBUSxHQUFHLEVBQUUsQ0FBQTtRQUNiLFdBQU0sR0FBRyxFQUFFLENBQUE7UUFDWCxhQUFRLEdBQUcsRUFBRSxDQUFBO0lBNGdCekIsQ0FBQztJQXpnQlcscUNBQWdCLEdBQXhCLFVBQXlCLElBQVk7UUFDakMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtRQUNmLElBQUksS0FBSyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxHQUFHLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDOUMsT0FBTyxLQUFLLENBQUE7SUFDaEIsQ0FBQztJQUVPLG9DQUFlLEdBQXZCLFVBQXdCLFFBQWUsRUFBRSxnQkFBd0I7OztZQUM3RCxLQUFvQixJQUFBLGFBQUEsU0FBQSxRQUFRLENBQUEsa0NBQUEsd0RBQUU7Z0JBQXpCLElBQUksT0FBTyxxQkFBQTtnQkFDWixPQUFPLENBQUMsV0FBVyxHQUFHLEVBQUUsZ0JBQWdCLENBQUE7YUFDM0M7Ozs7Ozs7OztJQUNMLENBQUM7SUFFRCxNQUFNO0lBQ0UsZ0NBQVcsR0FBbkIsVUFBb0IsTUFBYyxFQUFFLEdBQVcsRUFBRSxHQUFlLEVBQUUsR0FBZSxFQUFFLFFBQTBCO1FBQTdHLGlCQWlOQztRQWpOZ0Qsb0JBQUEsRUFBQSxPQUFlO1FBQUUsb0JBQUEsRUFBQSxPQUFlO1FBQUUseUJBQUEsRUFBQSxhQUEwQjtRQUN6RyxJQUFNLEtBQUssR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHLG9CQUFPLENBQUMsTUFBTSxFQUFFLENBQUE7UUFDekMsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUE7UUFDcEIsSUFBTSxLQUFLLEdBQUcsRUFBRSxFQUFFLFFBQVEsR0FBRyxFQUFFLEVBQUUsTUFBTSxHQUFHLEVBQUUsRUFBRSxNQUFNLEdBQUcsRUFBRSxDQUFBO1FBQ3pELElBQUksS0FBSyxHQUFRLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7UUFDL0IsSUFBSSxHQUFHLEtBQUssQ0FBQyxFQUFFO1lBQ1gsS0FBSyxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUE7U0FDMUI7YUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLEVBQUU7WUFDbEIsS0FBSyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7U0FDMUI7YUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLEVBQUU7WUFDbEIsS0FBSyxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7U0FDekI7UUFFRCxJQUFNLFFBQVEsR0FBZ0IsSUFBSSxHQUFHLEVBQUUsQ0FBQTtRQUV2QyxjQUFjO1FBQ2QsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFBO1FBQ25CLFlBQVk7UUFDWixJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUE7UUFDakIsSUFBSSxRQUFRLEdBQUcsS0FBSyxDQUFBO1FBQ3BCLE1BQU07UUFDTixJQUFNLFFBQVEsR0FBRyxVQUFDLFFBQXFCLEVBQUUsSUFBWTs7b0NBQ3hDLElBQUk7Z0JBRVQsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsVUFBQSxJQUFJO29CQUNuQyxPQUFPO3dCQUNILEdBQUcsRUFBRSxJQUFJLENBQUMsR0FBRzt3QkFDYixJQUFJLE1BQUE7d0JBQ0osV0FBVyxFQUFFLEtBQUksQ0FBQyxXQUFXLEVBQUU7cUJBQ2xDLENBQUE7Z0JBQ0wsQ0FBQyxDQUFDLENBQUE7Z0JBRUYsV0FBVztnQkFDWCxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUU7b0JBQ2pCLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxFQUFFO3dCQUMxQixJQUFJLE9BQU8sR0FBRyxRQUFRLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxJQUFJLENBQUMsVUFBVSxFQUF6QixDQUF5QixDQUFDLENBQUE7d0JBQzNELElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ1YsRUFBRSxDQUFDLElBQUksQ0FBQyxlQUFlLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxDQUFBOzt5QkFFM0M7d0JBQ0QsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxFQUFFLENBQUE7cUJBQy9CO29CQUNELElBQUksR0FBRyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUE7b0JBQ2pDLEdBQUcsQ0FBQyxJQUFJLENBQUM7d0JBQ0wsSUFBSSxFQUFFLENBQUM7d0JBQ1AsUUFBUSxFQUFFLFdBQVc7d0JBQ3JCLElBQUksTUFBQTt3QkFDSixpQkFBaUIsRUFBRSxDQUFDO3FCQUN2QixDQUFDLENBQUE7b0JBQ0YsUUFBUSxHQUFHLElBQUksQ0FBQTs7aUJBRWxCO2dCQUVELGFBQWE7Z0JBQ2IsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFO29CQUNmLElBQUksVUFBVSxHQUFHLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7b0JBQ3JELE9BQU8sUUFBUSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsRUFBRTt3QkFDN0IsVUFBVSxFQUFFLENBQUE7cUJBQ2Y7b0JBQ0QsUUFBUSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQTtvQkFFeEIsUUFBUSxDQUFDLFVBQVUsQ0FBQyxHQUFHO3dCQUNuQixJQUFJLEVBQUUsQ0FBQzt3QkFDUCxRQUFRLEVBQUUsV0FBVzt3QkFDckIsSUFBSSxNQUFBO3dCQUNKLGlCQUFpQixFQUFFLFVBQVU7cUJBQ2hDLENBQUE7b0JBQ0QsUUFBUSxHQUFHLElBQUksQ0FBQTs7aUJBRWxCO2dCQUVELFVBQVU7Z0JBQ1YsUUFBUSxDQUFDLElBQUksT0FBYixRQUFRLFdBQVMsV0FBVyxHQUFDO2dCQUM3QixLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBOzs7Z0JBbkRwQixLQUFpQixJQUFBLGFBQUEsU0FBQSxRQUFRLENBQUEsa0NBQUE7b0JBQXBCLElBQUksSUFBSSxxQkFBQTs0QkFBSixJQUFJO2lCQXFEWjs7Ozs7Ozs7O1FBQ0wsQ0FBQyxDQUFBO1FBRUQsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFBO1FBQ25CLElBQU0sWUFBWSxHQUFHLEVBQUUsQ0FBQTtRQUN2QixTQUFTO1FBQ1QsSUFBSSxRQUFRLENBQUMsTUFBTSxJQUFJLENBQUMsRUFBRTtZQUN0QixRQUFRLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQTtTQUMxQjtRQUNELElBQU0sT0FBTyxHQUFHLFFBQVEsQ0FBQyxHQUFHLENBQUMsVUFBQyxDQUFNLEVBQUUsQ0FBQztZQUNuQyxJQUFNLElBQUksR0FBRztnQkFDVCxHQUFHLEVBQUUsRUFBRSxDQUFDLEdBQUcsRUFBRTtnQkFDYixJQUFJLEVBQUUsSUFBSSxHQUFHLENBQUM7Z0JBQ2QsS0FBSyxFQUFFLEtBQUs7Z0JBQ1osS0FBSyxFQUFFLEtBQUs7Z0JBQ1osS0FBSyxFQUFFLENBQUM7Z0JBQ1IsUUFBUSxFQUFFLEdBQUc7Z0JBQ2IsS0FBSyxFQUFFLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLFVBQUEsQ0FBQztvQkFDaEIsSUFBSSxZQUFZLENBQUMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFO3dCQUNqQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQTtxQkFDdkM7eUJBQU07d0JBQ0gsWUFBWSxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUE7cUJBQ3RDO29CQUNELE9BQU87d0JBQ0gsS0FBSyxFQUFFLEtBQUs7d0JBQ1osR0FBRyxFQUFFLEVBQUUsQ0FBQyxHQUFHLEVBQUU7d0JBQ2IsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDLEVBQUU7d0JBQ2pDLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRTt3QkFDUixFQUFFLEVBQUUsQ0FBQyxDQUFDLEVBQUU7d0JBQ1IsS0FBSyxFQUFFLENBQUMsQ0FBQyxLQUFLO3FCQUNqQixDQUFBO2dCQUNMLENBQUMsQ0FBQzthQUNMLENBQUE7WUFDRCxRQUFRLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQTtZQUN6QixPQUFPLElBQWlCLENBQUE7UUFDNUIsQ0FBQyxDQUFDLENBQUE7UUFDRixRQUFRLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFBO1FBRXBCLFNBQVM7UUFDVCxJQUFNLElBQUksR0FBRyxvQkFBTyxDQUFDLG1CQUFtQixDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsR0FBRyxDQUFDLENBQUE7UUFDNUQsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxJQUFJO1lBQ25CLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUM7Z0JBQ2pCLElBQU0sRUFBRSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxZQUFZLENBQUE7Z0JBQy9ELElBQU0sRUFBRSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxZQUFZLENBQUE7Z0JBQy9ELElBQU0sRUFBRSxHQUFHLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUE7Z0JBQ3pFLElBQU0sRUFBRSxHQUFHLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUE7Z0JBQ3pFLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQTtZQUNsQixDQUFDLENBQUMsQ0FBQTtZQUNGLDZDQUE2QztZQUM3QyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLElBQUk7Z0JBQ25CLFFBQVEsQ0FBQyxJQUFJLENBQUM7b0JBQ1YsR0FBRyxFQUFFLElBQUksQ0FBQyxHQUFHO29CQUNiLElBQUksRUFBRSxDQUFDO29CQUNQLFNBQVMsRUFBRSxDQUFDO29CQUNaLFdBQVcsRUFBRSxFQUFFLEtBQUksQ0FBQyxXQUFXO2lCQUNsQyxDQUFDLENBQUE7WUFDTixDQUFDLENBQUMsQ0FBQTtRQUNOLENBQUMsQ0FBQyxDQUFBO1FBQ0YsT0FBTztRQUNQLElBQUksSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDaEIsTUFBTSxDQUFDLElBQUksQ0FBQztnQkFDUixFQUFFLEVBQUUsSUFBSTtnQkFDUixFQUFFLEVBQUUsQ0FBQztnQkFDTCxHQUFHLEVBQUUsRUFBRSxDQUFDLEdBQUcsRUFBRTtnQkFDYixJQUFJLEVBQUUsQ0FBQztnQkFDUCxXQUFXLEVBQUUsRUFBRSxJQUFJLENBQUMsV0FBVzthQUNsQyxDQUFDLENBQUE7WUFDRixNQUFNLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUE7U0FDbkQ7UUFDRCxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUV6QixJQUFNLE1BQU0sR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUE7UUFDekQsSUFBSSxDQUFDLFFBQVEsR0FBRyxRQUFRLENBQUE7UUFDeEIsSUFBSSxDQUFDLFFBQVEsR0FBRyxRQUFRLENBQUE7UUFDeEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7UUFDcEIsU0FBUztRQUNULG9CQUFPLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQztZQUMzQixLQUFLLEVBQUUsS0FBSztZQUNaLE1BQU0sRUFBRSxDQUFDO29CQUNMLElBQUksRUFBRSxDQUFDO29CQUNQLElBQUksRUFBRSxDQUFDO29CQUNQLEtBQUssRUFBRSxFQUFFO29CQUNULE1BQU0sRUFBRSxDQUFDO29CQUNULFFBQVEsRUFBRSxNQUFNO29CQUNoQixHQUFHLEVBQUUsRUFBRTtvQkFDUCxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUU7b0JBQ1gsTUFBTSxRQUFBO29CQUNOLEtBQUssT0FBQTtvQkFDTCxRQUFRLFVBQUE7b0JBQ1IsTUFBTSxRQUFBO29CQUNOLEdBQUcsS0FBQTtvQkFDSCxNQUFNLFFBQUE7aUJBQ0YsQ0FBQztTQUNaLENBQUMsQ0FBQyxJQUFJLENBQUM7WUFDSixXQUFXLENBQUMsR0FBRyxDQUFDLG1CQUFTLENBQUMsZUFBZSxFQUFFLGVBQWUsQ0FBQyxDQUFBO1lBQzNELFdBQVcsQ0FBQyxFQUFFLENBQUMsbUJBQVMsQ0FBQyxlQUFlLEVBQUUsZUFBZSxDQUFDLENBQUE7WUFDMUQsU0FBUyxlQUFlLENBQUMsS0FBYTtnQkFDbEMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxtQkFBUyxDQUFDLGVBQWUsRUFBRSxlQUFlLENBQUMsQ0FBQTtnQkFDM0QsSUFBTSxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFLENBQUE7Z0JBQzdDLElBQUksR0FBRyxHQUFHLEtBQUssRUFBRSxHQUFHLEdBQUcsQ0FBQyxDQUFBO2dCQUN4QixFQUFFLENBQUMsR0FBRyxDQUFDLGtEQUFrRCxDQUFDLENBQUE7Z0JBQzFELElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztvQkFDakIsSUFBSSxDQUFDLENBQUMsS0FBSyxLQUFLLEtBQUssRUFBRTt3QkFDbkIsQ0FBQyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxJQUFJOzRCQUNoQixJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBOzRCQUNuQyxJQUFJLFlBQVksQ0FBQyxHQUFHLENBQUMsRUFBRTtnQ0FDbkIsWUFBWSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQTs2QkFDekI7NEJBQ0QsR0FBRyxJQUFJLENBQUMsQ0FBQTs0QkFDUixFQUFFLENBQUMsR0FBRyxDQUFDLEdBQUcsR0FBRyxNQUFNLEdBQUcsSUFBSSxDQUFDLEVBQUUsR0FBRyxPQUFPLEdBQUcsSUFBSSxDQUFDLEVBQUUsR0FBRyxPQUFPLEdBQUcsSUFBSSxDQUFDLFNBQVMsRUFBRSxHQUFHLElBQUksR0FBRyxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUE7NEJBQ3BJLEdBQUcsR0FBRyxJQUFJLENBQUE7d0JBQ2QsQ0FBQyxDQUFDLENBQUE7cUJBQ0w7Z0JBQ0wsQ0FBQyxFQUFDO2dCQUNGLElBQUksQ0FBQyxHQUFHLEVBQUU7b0JBQ04sRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQTtpQkFDbEI7Z0JBQ0QsRUFBRSxDQUFDLEdBQUcsQ0FBQyxrREFBa0QsQ0FBQyxDQUFBO2dCQUMxRCxHQUFHLEdBQUcsS0FBSyxDQUFBO2dCQUNYLEtBQUssSUFBSSxHQUFHLElBQUksWUFBWSxFQUFFO29CQUNwQixJQUFBLEtBQUEsT0FBVyxHQUFHLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxJQUFBLEVBQXhCLEVBQUUsUUFBQSxFQUFFLEVBQUUsUUFBa0IsQ0FBQTtvQkFDL0IsSUFBTSxLQUFLLEdBQUcsWUFBWSxDQUFDLEdBQUcsQ0FBQyxDQUFBO29CQUMvQixJQUFJLEtBQUssRUFBRTt3QkFDUCxHQUFHLEdBQUcsSUFBSSxDQUFBO3dCQUNWLEVBQUUsQ0FBQyxHQUFHLENBQUMsS0FBSyxHQUFHLEVBQUUsR0FBRyxPQUFPLEdBQUcsRUFBRSxHQUFHLFVBQVUsR0FBRyxLQUFLLENBQUMsQ0FBQTtxQkFDekQ7aUJBQ0o7Z0JBQ0QsSUFBSSxDQUFDLEdBQUcsRUFBRTtvQkFDTixFQUFFLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFBO2lCQUNsQjtnQkFDRCxFQUFFLENBQUMsR0FBRyxDQUFDLHFEQUFxRCxDQUFDLENBQUE7WUFDakUsQ0FBQztZQUNELHVCQUFVLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxDQUFBO1FBQ25DLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVPLHdDQUFtQixHQUEzQixVQUE0QixFQUFVLEVBQUUsRUFBVTtRQUM5QyxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsR0FBRyxHQUFHLENBQUMsQ0FBQTtRQUNuQyxJQUFNLElBQUksR0FBRyxTQUFTLENBQUMsV0FBVyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsQ0FBQTtRQUN2RCxJQUFNLFNBQVMsR0FBRyxTQUFTLENBQUMsV0FBVyxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUNyRSxJQUFNLEtBQUssR0FBRztZQUNWLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUM7WUFDakQsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNyRCxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxHQUFHLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FBQztZQUNsRSxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1NBQzFELENBQUE7UUFDRCxPQUFPLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLElBQUEsRUFBRSxTQUFTLEVBQUUsRUFBRSxFQUFFLElBQUEsRUFBRSxLQUFLLE9BQUEsRUFBRSxFQUFFLENBQUE7SUFDdkQsQ0FBQztJQUVELE9BQU87SUFDTyw0QkFBTyxHQUFyQixVQUFzQixNQUFnQixFQUFFLFFBQWlCLEVBQUUsV0FBb0IsRUFBRSxLQUFjLEVBQUUsTUFBcUI7UUFBckIsdUJBQUEsRUFBQSxXQUFxQjs7Ozs7Ozt3QkFDbEgsSUFBSSxNQUFNLEVBQUU7NEJBQ1IsSUFBSSxRQUFRLEVBQUU7Z0NBQ1YsSUFBSSxDQUFDLE9BQU8sR0FBRyxFQUFFLENBQUE7Z0NBQ2pCLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSyxFQUFFLENBQUMsSUFBSSxDQUFDLG1CQUFtQixDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQTtnQ0FDckUsSUFBSSxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtvQ0FDaEIsRUFBRSxHQUFHLEtBQUssR0FBRyxDQUFDLENBQUE7b0NBQ2xCLE9BQU8sRUFBRSxHQUFHLENBQUMsRUFBRTt3Q0FDWCxFQUFFLEVBQUUsQ0FBQTt3Q0FDSixJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUE7cUNBQ3hFO2lDQUNKOzZCQUNKOzRCQUNELElBQUksV0FBVyxFQUFFO2dDQUNiLElBQUksQ0FBQyxVQUFVLEdBQUcsRUFBRSxDQUFBO2dDQUNwQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUE7Z0NBRTNFLElBQUksS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUU7b0NBQ2hCLEVBQUUsR0FBRyxLQUFLLEdBQUcsQ0FBQyxDQUFBO29DQUNsQixPQUFPLEVBQUUsR0FBRyxDQUFDLEVBQUU7d0NBQ1gsRUFBRSxFQUFFLENBQUE7d0NBQ0osSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFBO3FDQUM5RTtpQ0FDSjs2QkFDSjt5QkFDSjs2QkFDSTs0QkFDRCxJQUFJLFFBQVEsRUFBRTtnQ0FDVixJQUFJLENBQUMsT0FBTyxHQUFHLEVBQUUsQ0FBQTtnQ0FDakIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxRQUFRLEVBQUUsRUFBRSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUE7Z0NBRTFFLElBQUksS0FBSyxJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUU7b0NBQ2hCLEVBQUUsR0FBRyxLQUFLLEdBQUcsQ0FBQyxDQUFBO29DQUNsQixPQUFPLEVBQUUsR0FBRyxDQUFDLEVBQUU7d0NBQ1gsRUFBRSxFQUFFLENBQUE7d0NBQ0osSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxRQUFRLEVBQUUsRUFBRSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUE7cUNBQzdFO2lDQUNKOzZCQUNKOzRCQUNELElBQUksV0FBVyxFQUFFO2dDQUNiLElBQUksQ0FBQyxVQUFVLEdBQUcsRUFBRSxDQUFBO2dDQUNwQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLFdBQVcsRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtnQ0FFaEYsSUFBSSxLQUFLLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtvQ0FDaEIsRUFBRSxHQUFHLEtBQUssR0FBRyxDQUFDLENBQUE7b0NBQ2xCLE9BQU8sRUFBRSxHQUFHLENBQUMsRUFBRTt3Q0FDWCxFQUFFLEVBQUUsQ0FBQTt3Q0FDSixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLFdBQVcsRUFBRSxFQUFFLEVBQUUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtxQ0FDbkY7aUNBQ0o7NkJBQ0o7eUJBQ0o7d0JBRUssS0FBSyxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsb0JBQU8sQ0FBQyxNQUFNLEVBQUUsQ0FBQTt3QkFDekMsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLENBQUE7d0JBQ2QsS0FBSyxHQUFHLEVBQUUsRUFBRSxRQUFRLEdBQUcsRUFBRSxFQUFFLE1BQU0sR0FBRyxFQUFFLENBQUE7d0JBRXRDLFFBQVEsR0FBZ0IsSUFBSSxHQUFHLEVBQUUsQ0FBQTt3QkFFakMsUUFBUSxHQUFHLEVBQUUsQ0FBQTt3QkFFYixNQUFNLEdBQUcsRUFBRSxDQUFBO3dCQUNiLFFBQVEsR0FBRyxLQUFLLENBQUE7d0JBRWQsUUFBUSxHQUFHLFVBQUMsUUFBcUIsRUFBRSxJQUFZOztvREFDeEMsSUFBSTtnQ0FFVCxJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxVQUFBLElBQUk7b0NBQ25DLE9BQU87d0NBQ0gsR0FBRyxFQUFFLElBQUksQ0FBQyxHQUFHO3dDQUNiLElBQUksTUFBQTt3Q0FDSixXQUFXLEVBQUUsRUFBRSxLQUFJLENBQUMsV0FBVztxQ0FDbEMsQ0FBQTtnQ0FDTCxDQUFDLENBQUMsQ0FBQTtnQ0FFRixXQUFXO2dDQUNYLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtvQ0FDakIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLEVBQUU7d0NBQzFCLElBQUksT0FBTyxHQUFHLFFBQVEsQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxVQUFVLEVBQXpCLENBQXlCLENBQUMsQ0FBQTt3Q0FDM0QsSUFBSSxDQUFDLE9BQU8sRUFBRTs0Q0FDVixFQUFFLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRSxJQUFJLEVBQUUsUUFBUSxDQUFDLENBQUE7O3lDQUUzQzt3Q0FDRCxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtxQ0FDL0I7b0NBQ0QsSUFBSSxHQUFHLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtvQ0FDakMsSUFBSSxRQUFRLEdBQUcsQ0FBQyxDQUFBO29DQUNoQixJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUU7d0NBQ2YsUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUE7cUNBQzNCO29DQUNELElBQUksS0FBSyxHQUFHLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxRQUFRLENBQUMsQ0FBQTtvQ0FDM0MsR0FBRyxDQUFDLElBQUksQ0FBQzt3Q0FDTCxJQUFJLEVBQUUsQ0FBQzt3Q0FDUCxRQUFRLEVBQUUsV0FBVzt3Q0FDckIsSUFBSSxNQUFBO3dDQUNKLEtBQUssT0FBQTtxQ0FDUixDQUFDLENBQUE7b0NBQ0YsUUFBUSxHQUFHLElBQUksQ0FBQTs7aUNBRWxCO2dDQUVELGFBQWE7Z0NBQ2IsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFO29DQUNmLElBQUksVUFBVSxHQUFHLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7b0NBQ3JELE9BQU8sUUFBUSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsRUFBRTt3Q0FDN0IsVUFBVSxFQUFFLENBQUE7cUNBQ2Y7b0NBQ0QsUUFBUSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQTtvQ0FFeEIsUUFBUSxDQUFDLFVBQVUsQ0FBQyxHQUFHO3dDQUNuQixJQUFJLEVBQUUsQ0FBQzt3Q0FDUCxRQUFRLEVBQUUsV0FBVzt3Q0FDckIsSUFBSSxNQUFBO3dDQUNKLGlCQUFpQixFQUFFLFVBQVU7cUNBQ2hDLENBQUE7b0NBQ0QsUUFBUSxHQUFHLElBQUksQ0FBQTs7aUNBRWxCO2dDQUVELFVBQVU7Z0NBQ1YsUUFBUSxDQUFDLElBQUksT0FBYixRQUFRLFdBQVMsV0FBVyxHQUFDO2dDQUM3QixLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBOzs7Z0NBeERwQixLQUFpQixJQUFBLGFBQUEsU0FBQSxRQUFRLENBQUEsa0NBQUE7b0NBQXBCLElBQUksSUFBSSxxQkFBQTs0Q0FBSixJQUFJO2lDQTBEWjs7Ozs7Ozs7O3dCQUNMLENBQUMsQ0FBQTt3QkFFSyxRQUFRLEdBQUcsRUFBRSxDQUFBO3dCQUViLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFDLENBQU0sRUFBRSxDQUFDOzRCQUN2QyxDQUFDLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQTs0QkFDaEIsQ0FBQyxDQUFDLElBQUksR0FBRyxJQUFJLEdBQUcsQ0FBQyxDQUFBOzRCQUNqQixDQUFDLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTs0QkFDZixDQUFDLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTs0QkFDZixDQUFDLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTs0QkFDWCxDQUFDLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQTs0QkFDZCxDQUFDLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7Z0NBQ2IsQ0FBQyxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUE7Z0NBQ2YsQ0FBQyxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUE7Z0NBQ2hCLENBQUMsQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQTs0QkFDNUIsQ0FBQyxDQUFDLENBQUE7NEJBQ0YsUUFBUSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7NEJBQ25CLE9BQU8sQ0FBYyxDQUFBO3dCQUN6QixDQUFDLENBQUMsQ0FBQTt3QkFFSSxVQUFVLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsVUFBQyxDQUFNLEVBQUUsQ0FBQzs0QkFDN0MsQ0FBQyxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUE7NEJBQ2hCLENBQUMsQ0FBQyxJQUFJLEdBQUcsSUFBSSxHQUFHLENBQUMsQ0FBQTs0QkFDakIsQ0FBQyxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUE7NEJBQ2YsQ0FBQyxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUE7NEJBQ25CLENBQUMsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFBOzRCQUNYLENBQUMsQ0FBQyxRQUFRLEdBQUcsQ0FBQyxDQUFBOzRCQUNkLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztnQ0FDYixDQUFDLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTtnQ0FDZixDQUFDLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtnQ0FDaEIsQ0FBQyxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFBOzRCQUM3QixDQUFDLENBQUMsQ0FBQTs0QkFDRixRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQTs0QkFDbkIsT0FBTyxDQUFjLENBQUE7d0JBQ3pCLENBQUMsQ0FBQyxDQUFBO3dCQUNGLFFBQVEsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUE7d0JBQ3BCLFFBQVEsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLENBQUE7d0JBQ3ZCLFVBQVU7d0JBQ1Ysa0JBQWtCO3dCQUNsQixxQkFBcUI7d0JBQ3JCLGtCQUFrQjt3QkFDbEIscUJBQXFCO3dCQUNyQixlQUFlO3dCQUNmLHVDQUF1Qzt3QkFDdkMsNkJBQTZCO3dCQUM3QixvQkFBb0I7d0JBQ3BCLEtBQUs7d0JBRUwsT0FBTyxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsVUFBVSxDQUFDLENBQUE7d0JBRTNCLE1BQU0sR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUE7d0JBQ3pELElBQUksQ0FBQyxRQUFRLEdBQUcsUUFBUSxDQUFBO3dCQUN4QixJQUFJLENBQUMsUUFBUSxHQUFHLFFBQVEsQ0FBQTt3QkFDeEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7d0JBQ3BCLFNBQVM7d0JBQ1QscUJBQU0sb0JBQU8sQ0FBQyxRQUFRLENBQUMsYUFBYSxDQUFDO2dDQUNqQyxLQUFLLEVBQUUsS0FBSztnQ0FDWixNQUFNLEVBQUUsQ0FBQzt3Q0FDTCxJQUFJLEVBQUUsQ0FBQzt3Q0FDUCxJQUFJLEVBQUUsQ0FBQzt3Q0FDUCxLQUFLLEVBQUUsU0FBUzt3Q0FDaEIsTUFBTSxFQUFFLENBQUM7d0NBQ1QsUUFBUSxFQUFFLE1BQU07d0NBQ2hCLEdBQUcsRUFBRSxFQUFFO3dDQUNQLEVBQUUsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUM7d0NBQ1osS0FBSyxPQUFBO3dDQUNMLFFBQVEsVUFBQTt3Q0FDUixNQUFNLFFBQUE7cUNBQ0YsRUFBRTt3Q0FDTixpQkFBaUIsRUFBRSxLQUFLO3dDQUN4QixJQUFJLEVBQUUsR0FBRztxQ0FDWixDQUFDO2dDQUNGLFNBQVMsRUFBRSxDQUFDO2dDQUNaLE1BQU0sUUFBQTs2QkFDVCxDQUFDOzRCQUNGLFdBQVc7MEJBRFQ7O3dCQXBCRixTQUFTO3dCQUNULFNBbUJFLENBQUE7d0JBQ0YsV0FBVzt3QkFDWCx1QkFBVSxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQTs7Ozs7S0FDbEM7SUFFTyw2QkFBUSxHQUFoQixVQUFpQixVQUFrQixFQUFFLElBQWE7OztRQUM5QyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUE7UUFDOUIsSUFBSSxDQUFDLEdBQUc7WUFBRSxPQUFNO1FBQ2hCLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLENBQUE7UUFDM0MsSUFBSSxTQUFTLEVBQUU7WUFDWCxJQUFNLFFBQVEsU0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLDBDQUFFLG1CQUFtQixHQUFHLFdBQVcsRUFBRSxDQUFBO1lBQ3hFLElBQUksY0FBYyxHQUFHLENBQUMsQ0FBQTtZQUN0QixJQUFJLFFBQVEsRUFBRTs7b0JBQ1YsS0FBb0IsSUFBQSxhQUFBLFNBQUEsUUFBUSxDQUFBLGtDQUFBLHdEQUFFO3dCQUF6QixJQUFJLE9BQU8scUJBQUE7d0JBQ1osY0FBYyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsY0FBYyxFQUFFLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQTtxQkFDakU7Ozs7Ozs7OzthQUNKO1lBRUQsMkRBQTJEO1lBQzNELHNDQUFzQztZQUN0QyxHQUFHLENBQUMscUJBQXFCLENBQUMsU0FBUyxDQUFDLENBQUE7U0FDdkM7UUFDRCxJQUFJLElBQUksQ0FBQyxNQUFNLElBQUksVUFBVSxHQUFHLENBQUMsRUFBRTtZQUMvQixJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO1lBQ3hCLElBQU0sT0FBTyxHQUFHLElBQUksR0FBRyxDQUFBOztnQkFDdkIsS0FBaUIsSUFBQSxVQUFBLFNBQUEsS0FBSyxDQUFBLDRCQUFBLCtDQUFFO29CQUFuQixJQUFJLElBQUksa0JBQUE7b0JBQ1QsSUFBSSxHQUFHLFNBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLDBDQUFFLEdBQUcsQ0FBQTtvQkFDdEMsR0FBRyxJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7aUJBQzFCOzs7Ozs7Ozs7WUFDRCxLQUFLLElBQUksR0FBRyxJQUFJLElBQUksQ0FBQyxNQUFNLEVBQUU7Z0JBQ3pCLElBQUksT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRTtvQkFDbEIsU0FBUTtpQkFDWDtnQkFDRCxFQUFFLENBQUMsR0FBRyxDQUFDLEtBQUssRUFBRSxHQUFHLEVBQUUsYUFBYSxDQUFDLENBQUE7Z0JBQ2pDLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUE7Z0JBQzFCLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQTs7b0JBQ3ZCLEtBQWlCLElBQUEsdUJBQUEsU0FBQSxHQUFHLENBQUEsQ0FBQSx3QkFBQSx5Q0FBRTt3QkFBakIsSUFBSSxJQUFJLGdCQUFBO3dCQUNULElBQUksS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLEdBQUcsVUFBVSxDQUFBO3dCQUNuQyxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLEVBQUU7NEJBQ3pCLEtBQUssRUFBRSxDQUFBO3lCQUNWO3dCQUNELE9BQU8sQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLENBQUE7d0JBQ3pDLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFBO3FCQUM5Qjs7Ozs7Ozs7O2FBQ0o7U0FDSjtJQUNMLENBQUM7SUFFRCxtQkFBbUI7SUFDWiwwQkFBSyxHQUFaLFVBQWEsTUFBZSxFQUFFLEdBQVksRUFBRSxHQUFlLEVBQUUsR0FBZTtRQUFoQyxvQkFBQSxFQUFBLE9BQWU7UUFBRSxvQkFBQSxFQUFBLE9BQWU7UUFDeEUsSUFBSSxDQUFDLE1BQU0sRUFBRTtZQUNULElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtTQUNqQjthQUFNO1lBQ0gsSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQTtTQUMxQztJQUNMLENBQUM7SUFFTSxnQ0FBVyxHQUFsQixVQUFtQixNQUFjLEVBQUUsR0FBVyxFQUFFLFFBQXFCLEVBQUUsR0FBZSxFQUFFLEdBQWU7UUFBaEMsb0JBQUEsRUFBQSxPQUFlO1FBQUUsb0JBQUEsRUFBQSxPQUFlO1FBQ25HLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDVCxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7U0FDakI7YUFBTTtZQUNILElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLFFBQVEsQ0FBQyxDQUFBO1NBQ3BEO0lBQ0wsQ0FBQztJQUdNLDZCQUFRLEdBQWYsVUFBZ0IsT0FBZ0IsRUFBRSxPQUFnQixFQUFFLEtBQWMsRUFBRSxNQUFpQjtRQUNqRixJQUFJLE9BQU8sSUFBSSxLQUFLLEVBQUU7WUFDbEIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsT0FBTyxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsTUFBTSxDQUFDLENBQUE7U0FDdEQ7YUFDSTtZQUNELElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLE9BQU8sRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFBO1NBQ3ZEO0lBQ0wsQ0FBQztJQUVNLGdDQUFXLEdBQWxCLFVBQW1CLEVBQVUsRUFBRSxJQUFjLEVBQUUsR0FBZSxFQUFFLEdBQWU7UUFBaEMsb0JBQUEsRUFBQSxPQUFlO1FBQUUsb0JBQUEsRUFBQSxPQUFlO1FBQzNFLElBQU0sT0FBTyxHQUFVLHNCQUFTLENBQUMsRUFBRSxDQUFDLENBQUE7UUFDcEMsSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNWLE9BQU8sT0FBTyxDQUFDLEdBQUcsQ0FBQyxjQUFjLEdBQUcsRUFBRSxDQUFDLENBQUE7U0FDMUM7UUFDRCxJQUFNLEdBQUcsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFBO1FBQ3JCLElBQU0sTUFBTSxHQUFHLElBQUksR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFBO1FBQzdDLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLE9BQU8sQ0FBQyxDQUFBO0lBQ3BELENBQUM7SUFFTCxpQkFBQztBQUFELENBMXdDQSxBQTB3Q0MsSUFBQTtBQUVELElBQUksRUFBRSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUU7SUFDbEIsTUFBTSxDQUFDLFlBQVksQ0FBQyxHQUFHLElBQUksVUFBVSxFQUFFLENBQUE7Q0FDMUMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRXZlbnRUeXBlIGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvRXZlbnRUeXBlXCJcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCJcbmltcG9ydCB7IG1hcEhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL01hcEhlbHBlclwiXG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiXG5pbXBvcnQgQXJlYU9iaiBmcm9tIFwiLi4vYXJlYS9BcmVhT2JqXCJcbmltcG9ydCB7IExhbmRUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiXG5pbXBvcnQgeyBBUk1ZX0NPTkYgfSBmcm9tIFwiLi90ZXN0Q29uZmlnXCJcblxuaW50ZXJmYWNlIElBcm15VGVzdCB7XG4gICAgcGF3bnM6IGFueVtdXG4gICAgLyoqIOetieW+heWkmuWwkeenkuWQjui/m+WFpeaImOWcuiAqL1xuICAgIHdhaXRUaW1lPzogbnVtYmVyXG4gICAgLyoqIOWPr+S7peWkmuS4quWFseeUqOS4gOS4qnRhZyDljIXmi6zmlYzmlrkqL1xuICAgIHRhZz86IGFueSxcbiAgICAvKiog6K+lVGFn5omA5pyJ5Yab6Zif5YWo54Gt5ZCO6L+b5YWlICovXG4gICAgcHJlQXJteVRhZz86IGFueVxufVxuXG5jbGFzcyBCYXR0bGVUZXN0IHtcblxuICAgIC8vIOaIkeaWueWGm+mYn1xuICAgIG15QXJteXM6IElBcm15VGVzdFtdID0gW1xuICAgICAgICAvL+S7peS4i+S4uumHjuWcsOmYteWuuea1i+ivlSAg5Yu/5YigLy9cblxuICAgICAgICAvLyAg566A5Y2VMSDnroDljZUyIDHnuqflnLA8PTF+MlxuICAgICAgICB7XG4gICAgICAgICAgICBwYXduczogW3sgaWQ6IDMyMDEsIGx2OiAxIH0sXG4gICAgICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSB9LFxuICAgICAgICAgICAgXVxuICAgICAgICB9LFxuICAgICAgICAvLyAg566A5Y2VIDMgMee6p+WcsCAz6Led56a7XG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMTAxLCBsdjogMSAgfSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDF9LCBcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyAg566A5Y2VNCAx57qn5ZywIDTot53nprtcbiAgICAgICAgLy8geyBwYXduczogW1xuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEgfSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDF9LCBcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyAg566A5Y2VNSAgMee6p+WcsCA16Led56a7XG4gICAgICAgIC8vICB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgNl1dIH0gfSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgNl1dIH19LCBcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA2XV0gfX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDZdXSB9fSwgXG4gICAgICAgIC8vIF0gfSxcbiAgICAgICAgLy8gIOeugOWNlTYgMee6p+WcsCA26Led56a7XG4gICAgICAgIC8vIHsgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgNzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA2XV0gfX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDZdXSB9fSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgNl1dIH19LCBcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA2XV0gfX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDZdXSB9fSwgXG4gICAgICAgIC8vIF0gfSxcblxuICAgICAgICAvLyAg566A5Y2VNyAy57qn5ZywIDTot53nprtcbiAgICAgICAgLy8geyBwYXduczogW1xuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCA3MF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9fSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH19LCBcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA3XV0gfX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9fSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH19LCBcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA3XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcblxuICAgICAgICAvLyAg566A5Y2VOCAy57qn5ZywIDXot53nprtcbiAgICAgICAgLy8geyBwYXduczogW1xuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCA3MF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCA3MF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9fSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH19LCBcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA3XV0gfX0sIFxuICAgICAgICAvLyAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9fSwgXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA3XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcblxuICAgICAgICAvLyDnroDljZU5IDLnuqflnLAgNei3neemu1xuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDcwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDcwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDcwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDcwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDcwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA3XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDddXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgN11dIH0gfSxcbiAgICAgICAgLy8gXSB9LFxuXG4gICAgICAgIC8vICDmma7pgJoxIDHnuqflnLAgN+i3neemu1xuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDgwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDgwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDgwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA4XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDhdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA4XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDhdXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcblxuICAgICAgICAvLyAg5pmu6YCaNCAy57qn5ZywIDfot53nprsgMee6p+i3neemuzEwXG4gICAgICAgIC8vIHsgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTAwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEwMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMDBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy8gIOaZrumAmjUgMue6p+WcsCA46Led56a7IDHnuqfot53nprsxMVxuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEwMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMDBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTAwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuXG4gICAgICAgIC8vIOaZrumAmjYgIDHnuqflnLAgMTPot53nprsgXG4gICAgICAgIC8vIHsgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTAwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEwMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMDBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCA5XV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDldXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgOV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIC8vIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy8gIOaZrumAmjcgMue6p+WcsCAxMOi3neemu1xuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAwNl8xJywgYXR0cnM6IFtbMCwgMSwgMzVdLCBbMiwgNCwgMCwgMl1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDA2XzEnLCBhdHRyczogW1swLCAxLCAzNV0sIFsyLCA0LCAwLCAyXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMDZfMScsIGF0dHJzOiBbWzAsIDEsIDM1XSwgWzIsIDQsIDAsIDJdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAxNV8xJywgYXR0cnM6IFtbMCwyLDRdLCBbMiwgMTUsIDAsIDUwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMTVfMScsIGF0dHJzOiBbWzAsMiw0XSwgWzIsIDE1LCAwLCA1MF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDE1XzEnLCBhdHRyczogW1swLDIsNF0sIFsyLCAxNSwgMCwgNTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAxNV8xJywgYXR0cnM6IFtbMCwyLDRdLCBbMiwgMTUsIDAsIDUwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMTVfMScsIGF0dHJzOiBbWzAsMiw0XSwgWzIsIDE1LCAwLCA1MF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDE1XzEnLCBhdHRyczogW1swLDIsNF0sIFsyLCAxNSwgMCwgNTBdXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMTVfMScsIGF0dHJzOiBbWzAsMiw0XSwgWzIsIDE1LCAwLCA1MF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAxNV8xJywgYXR0cnM6IFtbMCwyLDRdLCBbMiwgMTUsIDAsIDUwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDE1XzEnLCBhdHRyczogW1swLDIsNF0sIFsyLCAxNSwgMCwgNTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMTVfMScsIGF0dHJzOiBbWzAsMiw0XSwgWzIsIDE1LCAwLCA1MF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAxNV8xJywgYXR0cnM6IFtbMCwyLDRdLCBbMiwgMTUsIDAsIDUwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDE1XzEnLCBhdHRyczogW1swLDIsNF0sIFsyLCAxNSwgMCwgNTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IHVpZDogJzYwMTVfMScsIGF0dHJzOiBbWzAsMiw0XSwgWzIsIDE1LCAwLCA1MF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgdWlkOiAnNjAxNV8xJywgYXR0cnM6IFtbMCwyLDRdLCBbMiwgMTUsIDAsIDUwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyB1aWQ6ICc2MDE1XzEnLCBhdHRyczogW1swLDIsNF0sIFsyLCAxNSwgMCwgNTBdXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuXG4gICAgICAgIC8vICDmma7pgJo5IDLnuqflnLAgMTLot53nprtcbiAgICAgICAgLy8geyBwYXduczogW1xuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTEwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyBdIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcblxuXG4gICAgICAgIC8vICDlm7Dpmr4xfjMgM+e6p+WcsCA46Led56a7IFxuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTEwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vIF0gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEwXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcblxuICAgICAgICAvLyAg5Zuw6Zq+NSA057qn5ZywIDfot53nprsgXG4gICAgICAgIC8vIHsgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy8gIOWbsOmavjcgNOe6p+WcsCA36Led56a7XG4gICAgICAgIC8vIHsgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gXSB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDExXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMV1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy8gIOWbsOmavjkgNOe6p+WcsCAxNei3neemu1xuICAgICAgICAvLyB7IHBhd25zOiBbXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMTVdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTE1XSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDExNV0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyBdIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8geyAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICBdICwgXG4gICAgICAgIC8vICAgICB3YWl0VGltZTogMVxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7ICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0gLCBcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lOiAxXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHsgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxMl1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTJdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEyXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSAsIFxuICAgICAgICAvLyAgICAgd2FpdFRpbWU6IDFcbiAgICAgICAgLy8gfSxcblxuICAgICAgICAvL+WcsOeLsTFcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIF0sXG4gICAgICAgIC8vICAgICB0YWcgOiBcImR1bl8xXCIsXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMzBdLCBbMSwgMiwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEzMF0sIFsxLCAyLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTMwXSwgWzEsIDIsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICBdLFxuICAgICAgICAvLyAgICAgd2FpdFRpbWUgOiA0MDAsXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICBdLFxuICAgICAgICAvLyAgICAgd2FpdFRpbWUgOiAxLFxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7XG4gICAgICAgIC8vICAgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgXSxcbiAgICAgICAgLy8gICAgIHdhaXRUaW1lIDogMSxcbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgIF0sXG4gICAgICAgIC8vICAgICB3YWl0VGltZSA6IDEsXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy/lnLDni7EyXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHdhaXRSb3VuZDogMSxcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHdhaXRSb3VuZDogMSxcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHdhaXRSb3VuZDogMixcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHdhaXRSb3VuZDogMixcbiAgICAgICAgLy8gICAgIHBhd25zOiBbXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNCwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDQsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA0LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIHdhaXRSb3VuZDogMTAsXG4gICAgICAgIC8vICAgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgXVxuICAgICAgICAvLyB9LFxuICAgICAgICAvLyB7XG4gICAgICAgIC8vICAgICB3YWl0Um91bmQ6IDE1LFxuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzIwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAyLCBhdHRyczogW1swLCAxLCAxMjBdLCBbMSwgMiwgMV1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMjAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDIsIGF0dHJzOiBbWzAsIDEsIDEyMF0sIFsxLCAyLCAxXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMyMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMiwgYXR0cnM6IFtbMCwgMSwgMTIwXSwgWzEsIDIsIDFdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMxMDEsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTAxLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwMSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgXVxuICAgICAgICAvLyB9LFxuXG4gICAgICAgIC8v5Lul5LiK5Li66YeO5Zyw6Zi15a655rWL6K+VICDli7/liKAvL1xuXG4gICAgICAgIC8vIHtcbiAgICAgICAgLy8gICAgIC8vIOWImOWkhyDlhbPnvr0g5byg6aOeXG4gICAgICAgIC8vICAgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIC8vIHsgaWQ6IDMzMDIsIGx2OiA2LCBwb3J0cmF5YWw6IHsgaWQ6IDMzMDIwMSwgYXR0cnM6IFtbMCwgMSwgMTAwXSwgWzAsIDIsIDEwXSwgWzEsIDI1LCAxMF0sIFsyLCAxMDAwMSwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICAvLyB7IGlkOiAzNDAzLCBsdjogNiwgcG9ydHJheWFsOiB7IGlkOiAzNDAzMDEsIGF0dHJzOiBbWzAsIDEsIDIwMF0sIFswLCAyLCAyMF0sIFsxLCA0MCwgNTBdLCBbMiwgMTAwMDEsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgLy8geyBpZDogMzIwMywgbHY6IDYsIHBvcnRyYXlhbDogeyBpZDogMzIwMzAxLCBhdHRyczogW1swLCAxLCAzNTBdLCBbMCwgMiwgMTRdLCBbMSwgMTgsIDcwXSwgWzIsIDEwMDAxLCAwXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMxMDMsIGx2OiA2LCBwb3J0cmF5YWw6IHsgaWQ6IDMxMDMwMSwgYXR0cnM6IFtbMCwgMSwgMjAwXSwgWzAsIDIsIDE0XSwgWzEsIDUsIDEwMF0sIFsyLCAxMDAwMSwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgIF1cbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTAxLCBsdjogNiwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwMSwgbHY6IDYsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSwgcG9ydHJheWFsOiB7IGlkOiAzMTAxMDEsIGF0dHJzOiBbWzAsIDEsIDMwMF0sIFswLCAyLCAxNl0sIFsxLCAxLCA4MF0sIFsyLCAzMDEwMSwgMF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTAxLCBsdjogNiwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9LCBwb3J0cmF5YWw6IHsgaWQ6IDMxMDEwMiwgYXR0cnM6IFtbMCwgMSwgMzAwXSwgWzAsIDIsIDE2XSwgWzEsIDIsIDIwXSwgWzIsIDMwMTAxLCAwXV0gfSB9XG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH1cbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzNDAxLCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzQwMSwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDM0MDEsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgICAgICAvLyB7IGlkOiAzMjAyLCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgLy8geyBpZDogMzIwMiwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIC8vIHsgaWQ6IDMyMDIsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgICAgICAvLyB7IGlkOiAzMjAyLCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgLy8geyBpZDogMzIwMiwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIC8vIHsgaWQ6IDMyMDIsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgICAgICAvLyB7IGlkOiAzNDAxLCBsdjogNiwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgLy8geyBpZDogMzEwNiwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIC8vIHsgaWQ6IDMxMDYsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICAvLyB7IGlkOiAzMTA2LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgLy8geyBpZDogMzIwMywgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIC8vIHsgaWQ6IDM0MDQsIGx2OiA0LCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gfSxcbiAgICAgICAgLy8gICAgIF1cbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA1LCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNSwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDUsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA1LCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNSwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDUsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMzA1LCBsdjogMS8qICwgZXF1aXA6IHsgaWQ6IDYwMDEsIGF0dHJzOiBbWzAsIDEsIDBdLCBbMSwgMiwgMTNdXSB9ICovIH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzMwNSwgbHY6IDEvKiAsIGVxdWlwOiB7IGlkOiA2MDAxLCBhdHRyczogW1swLCAxLCAwXSwgWzEsIDIsIDEzXV0gfSAqLyB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMzMDUsIGx2OiAxLyogLCBlcXVpcDogeyBpZDogNjAwMSwgYXR0cnM6IFtbMCwgMSwgMF0sIFsxLCAyLCAxM11dIH0gKi8gfSxcbiAgICAgICAgLy8gICAgIF1cbiAgICAgICAgLy8gfSxcbiAgICBdXG5cbiAgICAvL+aVjOaWueWGm+mYn1xuICAgIGVuZW15QXJteXM6IElBcm15VGVzdFtdID0gW1xuICAgICAgICB7XG4gICAgICAgICAgICBwYXduczogW1xuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgaWQ6IDM0MDEsIGx2OiA2LFxuICAgICAgICAgICAgICAgICAgICBwb3J0cmF5YWw6IHsgaWQ6IDM0MDEwMiwgYXR0cnM6IFtbMCwgMSwgMjAwXSwgWzAsIDIsIDE0XSwgWzEsIDM2LCAxMDBdLCBbMiwgNTAwMTcsIDBdXSB9LFxuICAgICAgICAgICAgICAgICAgICBlcXVpcDogeyBpZDogNjEwOCwgYXR0cnM6IFtbMCwgMSwgMTAwXSwgWzAsIDIsIDEwXSwgWzIsIDMsIDIwMCwgNDBdXSB9XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF1cbiAgICAgICAgfSxcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgLy8g5ZCV5biDXG4gICAgICAgIC8vICAgICBwYXduczogW1xuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDM0MDIsIGx2OiA2LCBwb3J0cmF5YWw6IHsgaWQ6IDM0MDIwMSwgYXR0cnM6IFtbMCwgMSwgMzAwXSwgWzAsIDIsIDE2XSwgWzEsIDM3LCAyMDBdLCBbMiwgMTAwMDEsIDBdXSB9IH0sXG4gICAgICAgIC8vICAgICBdXG4gICAgICAgIC8vIH0sXG5cbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMxMDEsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMTAyLCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzEwMywgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMxMDQsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMTA1LCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzIwMSwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMyMDIsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMjAzLCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzIwNCwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMyMDUsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMzAxLCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzMwMiwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMzMDMsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMzA0LCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzMwNSwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDEsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzNDAyLCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMywgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDQsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzNDA1LCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMSwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDIsIGx2OiA2IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzNDAyLCBsdjogNiB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMiwgbHY6IDYgfV0gfSxcbiAgICAgICAgLy8ge1xuICAgICAgICAvLyAgICAgcGF3bnM6IFtcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTA1LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMjMsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMTBdLCBbMiwgMjcsIDRdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwNSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDIzLCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDEwXSwgWzIsIDI3LCA0XV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMxMDUsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAyMywgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAxMF0sIFsyLCAyNywgNF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTA1LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMjMsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMTBdLCBbMiwgMjcsIDRdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwNSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDIzLCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDEwXSwgWzIsIDI3LCA0XV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMxMDUsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAyMywgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAxMF0sIFsyLCAyNywgNF1dIH0gfSxcbiAgICAgICAgLy8gICAgICAgICB7IGlkOiAzMTA1LCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMjMsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMTBdLCBbMiwgMjcsIDRdXSB9IH0sXG4gICAgICAgIC8vICAgICAgICAgeyBpZDogMzEwNSwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDIzLCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDEwXSwgWzIsIDI3LCA0XV0gfSB9LFxuICAgICAgICAvLyAgICAgICAgIHsgaWQ6IDMxMDUsIGx2OiAxLCBlcXVpcDogeyBpZDogNjAyMywgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAxMF0sIFsyLCAyNywgNF1dIH0gfSxcbiAgICAgICAgLy8gICAgIF1cbiAgICAgICAgLy8gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDMsIGx2OiAzIH0sIHsgaWQ6IDM0MDQsIGx2OiAzIH0sIHsgaWQ6IDM0MDEsIGx2OiAxIH0sIHsgaWQ6IDM0MDEsIGx2OiAxIH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiA0MTA1LCBsdjogNiB9LCB7IGlkOiA0MTA2LCBsdjogNiB9LCB7IGlkOiA0MTA3LCBsdjogNiB9LCB7IGlkOiA0MTA4LCBsdjogNiB9LCB7IGlkOiA0MTA5LCBsdjogNiB9LCB7IGlkOiA0MTEwLCBsdjogNiB9XSB9LFxuXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfSwgeyBpZDogMzMwMSwgbHY6IDMgfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH0sIHsgaWQ6IDMzMDEsIGx2OiAzIH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9LCB7IGlkOiAzMzAxLCBsdjogMyB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogNDEwMSwgbHY6IDMgfSwgeyBpZDogNDEwMiwgbHY6IDMgfSwgeyBpZDogNDEwMywgbHY6IDMgfSwgeyBpZDogNDEwNCwgbHY6IDMgfSwgeyBpZDogNDIwMSwgbHY6IDMgfSwgeyBpZDogNDIwMiwgbHY6IDMgfSwgeyBpZDogNDIwMywgbHY6IDMgfSwgeyBpZDogNDIwNCwgbHY6IDMgfSwgeyBpZDogNDIwNSwgbHY6IDMgfV0gfSxcblxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzEwMSwgbHY6IDYsIGVxdWlwOiB7IGlkOiA2MTAxLCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgMSwgNSwgNDBdXSB9IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMjAxLCBsdjogNiwgZXF1aXA6IHsgaWQ6IDYxMDQsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMl0sIFsyLCAyLCA2LCA0MF1dIH0gfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDMyMDIsIGx2OiA2LCBlcXVpcDogeyBpZDogNjEwNSwgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAyXSwgWzIsIDIsIDYsIDQwXV0gfSB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzMwMSwgbHY6IDIsIGVxdWlwOiB7IGlkOiA2MDA2LCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgNCwgNCwgNDBdXSB9IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMzAyLCBsdjogNiwgZXF1aXA6IHsgaWQ6IDYxMDcsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMl0sIFsyLCAzLCAxNTAsIDQwXV0gfSB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzEwMywgbHY6IDIsIGVxdWlwOiB7IGlkOiA2MTAzLCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgMywgMTUwLCA0MF1dIH0gfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDEsIGx2OiA2LCBlcXVpcDogeyBpZDogNjEwOCwgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAyXSwgWzIsIDUsIDIwLCA0MF1dIH0gfV0gfSxcbiAgICAgICAgLy8geyBwYXduczogW3sgaWQ6IDM0MDIsIGx2OiAyLCBlcXVpcDogeyBpZDogNjAwNiwgYXR0cnM6IFtbMCwgMSwgMV0sIFsxLCAyLCAyXSwgWzIsIDQsIDQsIDQwXV0gfSB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMiwgbHY6IDEsIGVxdWlwOiB7IGlkOiA2MDA2LCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgNCwgNCwgNDBdXSB9IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzMTAzLCBsdjogMiwgZXF1aXA6IHsgaWQ6IDYxMDMsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMl0sIFsyLCAzLCAxNTAsIDQwXV0gfSB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMSwgbHY6IDYsIGVxdWlwOiB7IGlkOiA2MTA4LCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgNSwgMjAsIDQwXV0gfSB9XSB9LFxuICAgICAgICAvLyB7IHBhd25zOiBbeyBpZDogMzQwMiwgbHY6IDIsIGVxdWlwOiB7IGlkOiA2MDA2LCBhdHRyczogW1swLCAxLCAxXSwgWzEsIDIsIDJdLCBbMiwgNCwgNCwgNDBdXSB9IH1dIH0sXG4gICAgICAgIC8vIHsgcGF3bnM6IFt7IGlkOiAzNDAyLCBsdjogMSwgZXF1aXA6IHsgaWQ6IDYwMDYsIGF0dHJzOiBbWzAsIDEsIDFdLCBbMSwgMiwgMl0sIFsyLCA0LCA0LCA0MF1dIH0gfV0gfSxcbiAgICBdXG5cbiAgICBwcml2YXRlIGF0dGFja0luZGV4ID0gMFxuICAgIHByaXZhdGUgZnJhbWVTZXEgPSBbXVxuICAgIHByaXZhdGUgdGFnU2VxID0ge31cbiAgICBwcml2YXRlIGFyZWFBcm15ID0ge31cblxuXG4gICAgcHJpdmF0ZSBfdGltZTJGcmFtZWluZGV4KHRpbWU6IG51bWJlcikge1xuICAgICAgICBsZXQgdHggPSAxIC8gMjBcbiAgICAgICAgbGV0IGluZGV4ID0gTWF0aC5tYXgoMSwgTWF0aC5mbG9vcih0aW1lIC8gdHgpKVxuICAgICAgICByZXR1cm4gaW5kZXhcbiAgICB9XG5cbiAgICBwcml2YXRlIF9zZXRBdHRhY2tJbmRleChmaWdodGVyczogYW55W10sIHN0YXJ0QXR0YWNrSW5kZXg6IG51bWJlcikge1xuICAgICAgICBmb3IgKGxldCBmaWdodGVyIG9mIGZpZ2h0ZXJzKSB7XG4gICAgICAgICAgICBmaWdodGVyLmF0dGFja0luZGV4ID0gKytzdGFydEF0dGFja0luZGV4XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmiZPlnLDmiJhcbiAgICBwcml2YXRlIF9iYXR0bGVMYW5kKGxhbmRJZDogbnVtYmVyLCBkaXM6IG51bWJlciwgZGlyOiBudW1iZXIgPSAyLCBtdWw6IG51bWJlciA9IDEsIHRlc3RBcm15OiBJQXJteVRlc3RbXSA9IFtdKSB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gMSwgb3duZXIgPSBnYW1lSHByLmdldFVpZCgpXG4gICAgICAgIHRoaXMuYXR0YWNrSW5kZXggPSAwXG4gICAgICAgIGNvbnN0IGFybXlzID0gW10sIGZpZ2h0ZXJzID0gW10sIHRvd2VycyA9IFtdLCBidWlsZHMgPSBbXVxuICAgICAgICBsZXQgcG9pbnQ6IGFueSA9IHsgeDogNSwgeTogMCB9XG4gICAgICAgIGlmIChkaXIgPT09IDApIHtcbiAgICAgICAgICAgIHBvaW50ID0geyB4OiA1LCB5OiAxMCB9XG4gICAgICAgIH0gZWxzZSBpZiAoZGlyID09PSAxKSB7XG4gICAgICAgICAgICBwb2ludCA9IHsgeDogMTAsIHk6IDUgfVxuICAgICAgICB9IGVsc2UgaWYgKGRpciA9PT0gMykge1xuICAgICAgICAgICAgcG9pbnQgPSB7IHg6IDAsIHk6IDUgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZnJhbWVTZXQ6IFNldDxudW1iZXI+ID0gbmV3IFNldCgpXG5cbiAgICAgICAgLy8g5L+d5a2Y562J5b6F5pe26Ze06L+b5YWl55qE5Yab6ZifXG4gICAgICAgIGNvbnN0IGZyYW1lU2VxID0gW11cbiAgICAgICAgLy8g5L+d5a2Y562J5b6F5YmN572u5Yab6Zif55qEXG4gICAgICAgIGNvbnN0IHRhZ1NlcSA9IHt9XG4gICAgICAgIGxldCBsaXN0ZW5lZCA9IGZhbHNlXG4gICAgICAgIC8vIOWIhumFjeW4p1xuICAgICAgICBjb25zdCBzdW1GcmFtZSA9IChhcm15TGlzdDogSUFybXlUZXN0W10sIGNhbXA6IG51bWJlcikgPT4ge1xuICAgICAgICAgICAgZm9yIChsZXQgYXJteSBvZiBhcm15TGlzdCkge1xuXG4gICAgICAgICAgICAgICAgY29uc3QgY3VyRmlnaHRlcnMgPSBhcm15LnBhd25zLm1hcChwYXduID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVpZDogcGF3bi51aWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBjYW1wLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXR0YWNrSW5kZXg6IHRoaXMuYXR0YWNrSW5kZXgrKywgLy8g6L+b5YWl5pe25YaN6K6h566X5pS75Ye76aG65bqPXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgICAgLy8g6ZyA562J5b6F5YmN572u5Yab6Zif55qEXG4gICAgICAgICAgICAgICAgaWYgKGFybXkucHJlQXJteVRhZykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXRhZ1NlcVthcm15LnByZUFybXlUYWddKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgaGFzQXJteSA9IGFybXlMaXN0LmZpbmQoeCA9PiB4LnRhZyA9PT0gYXJteS5wcmVBcm15VGFnKVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFoYXNBcm15KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2Mud2Fybign5om+5LiN5Yiw5YmN572u5Yab6ZifLOW/veeVpeatpOWGm+mYnycsIGFybXksIGFybXlMaXN0KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0YWdTZXFbYXJteS5wcmVBcm15VGFnXSA9IFtdXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgbGV0IHNlcSA9IHRhZ1NlcVthcm15LnByZUFybXlUYWddXG4gICAgICAgICAgICAgICAgICAgIHNlcS5wdXNoKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6IDEsXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWdodGVyczogY3VyRmlnaHRlcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICBhcm15LFxuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudEZyYW1lSW5kZXg6IDAsXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIGxpc3RlbmVkID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIOmcgOetieW+heaXtumXtOi/m+WFpeeahOWGm+mYn1xuICAgICAgICAgICAgICAgIGlmIChhcm15LndhaXRUaW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBmcmFtZUluZGV4ID0gdGhpcy5fdGltZTJGcmFtZWluZGV4KGFybXkud2FpdFRpbWUpXG4gICAgICAgICAgICAgICAgICAgIHdoaWxlIChmcmFtZVNldC5oYXMoZnJhbWVJbmRleCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZyYW1lSW5kZXgrK1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGZyYW1lU2V0LmFkZChmcmFtZUluZGV4KVxuXG4gICAgICAgICAgICAgICAgICAgIGZyYW1lU2VxW2ZyYW1lSW5kZXhdID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZ2h0ZXJzOiBjdXJGaWdodGVycyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFybXksXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50RnJhbWVJbmRleDogZnJhbWVJbmRleCxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBsaXN0ZW5lZCA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyDpppbluKfov5vlhaXnmoTlhpvpmJ9cbiAgICAgICAgICAgICAgICBmaWdodGVycy5wdXNoKC4uLmN1ckZpZ2h0ZXJzKVxuICAgICAgICAgICAgICAgIGFybXlzLnB1c2goYXJteSlcblxuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgYXJlYUFybXkgPSB7fVxuICAgICAgICBjb25zdCBwYXduQ291bnRNYXAgPSB7fVxuICAgICAgICAvLyDliqDlhaXmiJHmlrnlhpvpmJ9cbiAgICAgICAgaWYgKHRlc3RBcm15Lmxlbmd0aCA9PSAwKSB7XG4gICAgICAgICAgICB0ZXN0QXJteSA9IHRoaXMubXlBcm15c1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG15QXJteXMgPSB0ZXN0QXJteS5tYXAoKG06IGFueSwgaSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgYXJteSA9IHtcbiAgICAgICAgICAgICAgICB1aWQ6IHV0LlVJRCgpLFxuICAgICAgICAgICAgICAgIG5hbWU6ICfnvJbpmJ8nICsgaSxcbiAgICAgICAgICAgICAgICBpbmRleDogaW5kZXgsXG4gICAgICAgICAgICAgICAgb3duZXI6IG93bmVyLFxuICAgICAgICAgICAgICAgIHN0YXRlOiAzLFxuICAgICAgICAgICAgICAgIGVudGVyRGlyOiBkaXIsXG4gICAgICAgICAgICAgICAgcGF3bnM6IG0ucGF3bnMubWFwKHggPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAocGF3bkNvdW50TWFwW3guaWQgKyAnXycgKyB4Lmx2XSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcGF3bkNvdW50TWFwW3guaWQgKyAnXycgKyB4Lmx2XSArPSAxXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBwYXduQ291bnRNYXBbeC5pZCArICdfJyArIHgubHZdID0gMVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpbmRleDogaW5kZXgsXG4gICAgICAgICAgICAgICAgICAgICAgICB1aWQ6IHV0LlVJRCgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgcG9pbnQ6IHsgeDogcG9pbnQueCwgeTogcG9pbnQueSB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHguaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBsdjogeC5sdixcbiAgICAgICAgICAgICAgICAgICAgICAgIGVxdWlwOiB4LmVxdWlwLFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFyZWFBcm15W2FybXkudWlkXSA9IGFybXlcbiAgICAgICAgICAgIHJldHVybiBhcm15IGFzIElBcm15VGVzdFxuICAgICAgICB9KVxuICAgICAgICBzdW1GcmFtZShteUFybXlzLCAyKVxuXG4gICAgICAgIC8vIOWKoOWFpeaVjOaWueWGm+mYn1xuICAgICAgICBjb25zdCBjb25mID0gZ2FtZUhwci5nZXRBcmVhUGF3bkNvbmZJbmZvKGluZGV4LCBsYW5kSWQsIGRpcylcbiAgICAgICAgY29uZi5hcm15cy5mb3JFYWNoKGFybXkgPT4ge1xuICAgICAgICAgICAgYXJteS5wYXducy5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgYXAgPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3Bhd25CYXNlJywgYS5pZCkuYXR0YWNrX3NwZWVkXG4gICAgICAgICAgICAgICAgY29uc3QgYnAgPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3Bhd25CYXNlJywgYi5pZCkuYXR0YWNrX3NwZWVkXG4gICAgICAgICAgICAgICAgY29uc3QgYXcgPSBhcCAqIDEwMCArICg5OSAtIG1hcEhlbHBlci5nZXRQb2ludFRvUG9pbnREaXMoYS5wb2ludCwgcG9pbnQpKVxuICAgICAgICAgICAgICAgIGNvbnN0IGJ3ID0gYnAgKiAxMDAgKyAoOTkgLSBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKGIucG9pbnQsIHBvaW50KSlcbiAgICAgICAgICAgICAgICByZXR1cm4gYncgLSBhd1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuYXR0YWNrSW5kZXgsICdhdHRhY2tJZHgnKVxuICAgICAgICAgICAgYXJteS5wYXducy5mb3JFYWNoKHBhd24gPT4ge1xuICAgICAgICAgICAgICAgIGZpZ2h0ZXJzLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICB1aWQ6IHBhd24udWlkLFxuICAgICAgICAgICAgICAgICAgICBjYW1wOiAxLFxuICAgICAgICAgICAgICAgICAgICB3YWl0Um91bmQ6IDAsXG4gICAgICAgICAgICAgICAgICAgIGF0dGFja0luZGV4OiArK3RoaXMuYXR0YWNrSW5kZXgsXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgICAgIC8vIOWKoOWFpeeureWhlFxuICAgICAgICBpZiAoY29uZi5ocFsxXSA+IDApIHtcbiAgICAgICAgICAgIHRvd2Vycy5wdXNoKHtcbiAgICAgICAgICAgICAgICBpZDogNzAwMixcbiAgICAgICAgICAgICAgICBsdjogMSxcbiAgICAgICAgICAgICAgICB1aWQ6IHV0LlVJRCgpLFxuICAgICAgICAgICAgICAgIGNhbXA6IDEsXG4gICAgICAgICAgICAgICAgYXR0YWNrSW5kZXg6ICsrdGhpcy5hdHRhY2tJbmRleCxcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBidWlsZHMucHVzaCh7IGlkOiAyMTAyLCBwb2ludDogeyB4OiA1LCB5OiA1IH0gfSlcbiAgICAgICAgfVxuICAgICAgICBhcm15cy5wdXNoQXJyKGNvbmYuYXJteXMpXG5cbiAgICAgICAgY29uc3Qgb25TdGVwID0gbGlzdGVuZWQgPyB0aGlzLl9vbkZyYW1lLmJpbmQodGhpcykgOiBudWxsXG4gICAgICAgIHRoaXMuYXJlYUFybXkgPSBhcmVhQXJteVxuICAgICAgICB0aGlzLmZyYW1lU2VxID0gZnJhbWVTZXFcbiAgICAgICAgdGhpcy50YWdTZXEgPSB0YWdTZXFcbiAgICAgICAgLy8g6K6+572u5Zue5pS+5pWw5o2uXG4gICAgICAgIGdhbWVIcHIucGxheWJhY2suc2V0UmVjb3JkRGF0YSh7XG4gICAgICAgICAgICBpbmRleDogaW5kZXgsXG4gICAgICAgICAgICBmcmFtZXM6IFt7XG4gICAgICAgICAgICAgICAgdHlwZTogMCxcbiAgICAgICAgICAgICAgICBjYW1wOiAxLFxuICAgICAgICAgICAgICAgIG93bmVyOiAnJyxcbiAgICAgICAgICAgICAgICBjaXR5SWQ6IDAsXG4gICAgICAgICAgICAgICAgcmFuZFNlZWQ6IDExNDIwMCxcbiAgICAgICAgICAgICAgICBmcHM6IDIwLFxuICAgICAgICAgICAgICAgIGhwOiBjb25mLmhwLFxuICAgICAgICAgICAgICAgIGJ1aWxkcyxcbiAgICAgICAgICAgICAgICBhcm15cyxcbiAgICAgICAgICAgICAgICBmaWdodGVycyxcbiAgICAgICAgICAgICAgICB0b3dlcnMsXG4gICAgICAgICAgICAgICAgbXVsLFxuICAgICAgICAgICAgICAgIG9uU3RlcFxuICAgICAgICAgICAgfSBhcyBhbnldLFxuICAgICAgICB9KS50aGVuKCgpID0+IHtcbiAgICAgICAgICAgIGV2ZW50Q2VudGVyLm9mZihFdmVudFR5cGUuQVJFQV9CQVRUTEVfRU5ELCBvbkFyZWFCYXR0bGVFbmQpXG4gICAgICAgICAgICBldmVudENlbnRlci5vbihFdmVudFR5cGUuQVJFQV9CQVRUTEVfRU5ELCBvbkFyZWFCYXR0bGVFbmQpXG4gICAgICAgICAgICBmdW5jdGlvbiBvbkFyZWFCYXR0bGVFbmQoaW5kZXg6IG51bWJlcikge1xuICAgICAgICAgICAgICAgIGV2ZW50Q2VudGVyLm9mZihFdmVudFR5cGUuQVJFQV9CQVRUTEVfRU5ELCBvbkFyZWFCYXR0bGVFbmQpXG4gICAgICAgICAgICAgICAgY29uc3QgYXJlYSA9IGdhbWVIcHIuYXJlYUNlbnRlci5nZXRMb29rQXJlYSgpXG4gICAgICAgICAgICAgICAgbGV0IGhhcyA9IGZhbHNlLCBjbnQgPSAwXG4gICAgICAgICAgICAgICAgY2MubG9nKCctLS0tLS0tLS0tLS0tLS0tLS0tLS0t5a2Y5rS75aOr5YW1LS0tLS0tLS0tLS0tLS0tLS0tLS0tLScpXG4gICAgICAgICAgICAgICAgYXJlYT8uYXJteXMuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG0ub3duZXIgPT09IG93bmVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtLnBhd25zLmZvckVhY2gocGF3biA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gcGF3bi5pZCArICdfJyArIHBhd24ubHZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocGF3bkNvdW50TWFwW2tleV0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGF3bkNvdW50TWFwW2tleV0gLT0gMVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbnQgKz0gMVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNjLmxvZyhjbnQgKyAnIGlkPScgKyBwYXduLmlkICsgJywgbHY9JyArIHBhd24ubHYgKyAnLCBocD0nICsgcGF3bi5nZXRIcFRleHQoKSArICcgKCcgKyAocGF3bi5nZXRIcFJhdGlvKCkgKiAxMDApLnRvRml4ZWQoMSkgKyAnJSknKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhcyA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIGlmICghaGFzKSB7XG4gICAgICAgICAgICAgICAgICAgIGNjLmxvZygn5peg5LiA55Sf6L+YIScpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNjLmxvZygnLS0tLS0tLS0tLS0tLS0tLS0tLS0tLeaNn+WkseWjq+WFtS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0nKVxuICAgICAgICAgICAgICAgIGhhcyA9IGZhbHNlXG4gICAgICAgICAgICAgICAgZm9yIChsZXQga2V5IGluIHBhd25Db3VudE1hcCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBbaWQsIGx2XSA9IGtleS5zcGxpdCgnXycpXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvdW50ID0gcGF3bkNvdW50TWFwW2tleV1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGNvdW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBoYXMgPSB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgICBjYy5sb2coJ2lkPScgKyBpZCArICcsIGx2PScgKyBsdiArICcsIGNvdW50PScgKyBjb3VudClcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWhhcykge1xuICAgICAgICAgICAgICAgICAgICBjYy5sb2coJ+ayoeacieaNn+WksSEnKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYy5sb2coJy0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLScpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB2aWV3SGVscGVyLmdvdG9XaW5kKCdwbGF5YmFjaycpXG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBnZXRQYXduSW5mb0J5SGVyb0lkKGlkOiBudW1iZXIsIGx2OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgcGF3bklkID0gTWF0aC5mbG9vcihpZCAvIDEwMClcbiAgICAgICAgY29uc3QganNvbiA9IGFzc2V0c01nci5nZXRKc29uRGF0YSgncG9ydHJheWFsQmFzZScsIGlkKVxuICAgICAgICBjb25zdCBza2lsbEpzb24gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3BvcnRyYXlhbFNraWxsJywganNvbi5za2lsbClcbiAgICAgICAgY29uc3QgYXR0cnMgPSBbXG4gICAgICAgICAgICBbMCwgMSwgdXQuc3RyaW5nVG9OdW1iZXJzKGpzb24uaHAsICcsJykucmFuZG9tKCldLFxuICAgICAgICAgICAgWzAsIDIsIHV0LnN0cmluZ1RvTnVtYmVycyhqc29uLmF0dGFjaywgJywnKS5yYW5kb20oKV0sXG4gICAgICAgICAgICBbMSwganNvbi5za2lsbCwgdXQuc3RyaW5nVG9OdW1iZXJzKHNraWxsSnNvbi52YWx1ZSwgJywnKS5yYW5kb20oKV0sXG4gICAgICAgICAgICBbMiwgdXQuc3RyaW5nVG9OdW1iZXJzKGpzb24uc3RyYXRlZ3ksICd8JykucmFuZG9tKCksIDBdLFxuICAgICAgICBdXG4gICAgICAgIHJldHVybiB7IGlkOiBwYXduSWQsIGx2LCBwb3J0cmF5YWw6IHsgaWQsIGF0dHJzIH0gfVxuICAgIH1cblxuICAgIC8vIOWGm+mYn+WvueaKl1xuICAgIHByaXZhdGUgYXN5bmMgX2JhdHRsZShpc0hlcm8/OiBib29sZWFuLCBteUhlcm9JZD86IG51bWJlciwgZW5lbXlIZXJvSWQ/OiBudW1iZXIsIGNvdW50PzogbnVtYmVyLCBza2luSWQ6IG51bWJlcltdID0gW10pIHtcbiAgICAgICAgaWYgKGlzSGVybykge1xuICAgICAgICAgICAgaWYgKG15SGVyb0lkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5teUFybXlzID0gW11cbiAgICAgICAgICAgICAgICB0aGlzLm15QXJteXMucHVzaCh7IHBhd25zOiBbdGhpcy5nZXRQYXduSW5mb0J5SGVyb0lkKG15SGVyb0lkLCA2KV0gfSlcbiAgICAgICAgICAgICAgICBpZiAoY291bnQgJiYgY291bnQgPiAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBjdCA9IGNvdW50IC0gMVxuICAgICAgICAgICAgICAgICAgICB3aGlsZSAoY3QgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdC0tXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm15QXJteXMucHVzaCh7IHBhd25zOiBbdGhpcy5nZXRQYXduSW5mb0J5SGVyb0lkKG15SGVyb0lkLCA2KV0gfSlcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChlbmVteUhlcm9JZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZW5lbXlBcm15cyA9IFtdXG4gICAgICAgICAgICAgICAgdGhpcy5lbmVteUFybXlzLnB1c2goeyBwYXduczogW3RoaXMuZ2V0UGF3bkluZm9CeUhlcm9JZChlbmVteUhlcm9JZCwgNildIH0pXG5cbiAgICAgICAgICAgICAgICBpZiAoY291bnQgJiYgY291bnQgPiAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBjdCA9IGNvdW50IC0gMVxuICAgICAgICAgICAgICAgICAgICB3aGlsZSAoY3QgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdC0tXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVuZW15QXJteXMucHVzaCh7IHBhd25zOiBbdGhpcy5nZXRQYXduSW5mb0J5SGVyb0lkKGVuZW15SGVyb0lkLCA2KV0gfSlcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmIChteUhlcm9JZCkge1xuICAgICAgICAgICAgICAgIHRoaXMubXlBcm15cyA9IFtdXG4gICAgICAgICAgICAgICAgdGhpcy5teUFybXlzLnB1c2goeyBwYXduczogW3sgaWQ6IG15SGVyb0lkLCBsdjogNiwgc2tpbklkOiBza2luSWRbMF0gfV0gfSlcblxuICAgICAgICAgICAgICAgIGlmIChjb3VudCAmJiBjb3VudCA+IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGN0ID0gY291bnQgLSAxXG4gICAgICAgICAgICAgICAgICAgIHdoaWxlIChjdCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGN0LS1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubXlBcm15cy5wdXNoKHsgcGF3bnM6IFt7IGlkOiBteUhlcm9JZCwgbHY6IDYsIHNraW5JZDogc2tpbklkWzBdIH1dIH0pXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZW5lbXlIZXJvSWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmVuZW15QXJteXMgPSBbXVxuICAgICAgICAgICAgICAgIHRoaXMuZW5lbXlBcm15cy5wdXNoKHsgcGF3bnM6IFt7IGlkOiBlbmVteUhlcm9JZCwgbHY6IDYsIHNraW5JZDogc2tpbklkWzFdIH1dIH0pXG5cbiAgICAgICAgICAgICAgICBpZiAoY291bnQgJiYgY291bnQgPiAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBjdCA9IGNvdW50IC0gMVxuICAgICAgICAgICAgICAgICAgICB3aGlsZSAoY3QgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdC0tXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVuZW15QXJteXMucHVzaCh7IHBhd25zOiBbeyBpZDogZW5lbXlIZXJvSWQsIGx2OiA2LCBza2luSWQ6IHNraW5JZFsxXSB9XSB9KVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgaW5kZXggPSAxLCBvd25lciA9IGdhbWVIcHIuZ2V0VWlkKClcbiAgICAgICAgdGhpcy5hdHRhY2tJbmRleCA9IDBcbiAgICAgICAgY29uc3QgYXJteXMgPSBbXSwgZmlnaHRlcnMgPSBbXSwgdG93ZXJzID0gW11cblxuICAgICAgICBjb25zdCBmcmFtZVNldDogU2V0PG51bWJlcj4gPSBuZXcgU2V0KClcbiAgICAgICAgLy8g5L+d5a2Y562J5b6F5pe26Ze06L+b5YWl55qE5Yab6ZifXG4gICAgICAgIGNvbnN0IGZyYW1lU2VxID0gW11cbiAgICAgICAgLy8g5L+d5a2Y562J5b6F5YmN572u5Yab6Zif55qEXG4gICAgICAgIGNvbnN0IHRhZ1NlcSA9IHt9XG4gICAgICAgIGxldCBsaXN0ZW5lZCA9IGZhbHNlXG4gICAgICAgIC8vIOWIhumFjeW4p1xuICAgICAgICBjb25zdCBzdW1GcmFtZSA9IChhcm15TGlzdDogSUFybXlUZXN0W10sIGNhbXA6IG51bWJlcikgPT4ge1xuICAgICAgICAgICAgZm9yIChsZXQgYXJteSBvZiBhcm15TGlzdCkge1xuXG4gICAgICAgICAgICAgICAgY29uc3QgY3VyRmlnaHRlcnMgPSBhcm15LnBhd25zLm1hcChwYXduID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHVpZDogcGF3bi51aWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBjYW1wLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXR0YWNrSW5kZXg6ICsrdGhpcy5hdHRhY2tJbmRleCxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pXG5cbiAgICAgICAgICAgICAgICAvLyDpnIDnrYnlvoXliY3nva7lhpvpmJ/nmoRcbiAgICAgICAgICAgICAgICBpZiAoYXJteS5wcmVBcm15VGFnKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghdGFnU2VxW2FybXkucHJlQXJteVRhZ10pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBoYXNBcm15ID0gYXJteUxpc3QuZmluZCh4ID0+IHgudGFnID09PSBhcm15LnByZUFybXlUYWcpXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWhhc0FybXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYy53YXJuKCfmib7kuI3liLDliY3nva7lhpvpmJ8s5b+955Wl5q2k5Yab6ZifJywgYXJteSwgYXJteUxpc3QpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRhZ1NlcVthcm15LnByZUFybXlUYWddID0gW11cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBsZXQgc2VxID0gdGFnU2VxW2FybXkucHJlQXJteVRhZ11cbiAgICAgICAgICAgICAgICAgICAgbGV0IHdhaXRUaW1lID0gMFxuICAgICAgICAgICAgICAgICAgICBpZiAoYXJteS53YWl0VGltZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgd2FpdFRpbWUgPSBhcm15LndhaXRUaW1lXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgbGV0IGZyYW1lID0gdGhpcy5fdGltZTJGcmFtZWluZGV4KHdhaXRUaW1lKVxuICAgICAgICAgICAgICAgICAgICBzZXEucHVzaCh7XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAxLFxuICAgICAgICAgICAgICAgICAgICAgICAgZmlnaHRlcnM6IGN1ckZpZ2h0ZXJzLFxuICAgICAgICAgICAgICAgICAgICAgICAgYXJteSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZyYW1lXG4gICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIGxpc3RlbmVkID0gdHJ1ZVxuICAgICAgICAgICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIOmcgOetieW+heaXtumXtOi/m+WFpeeahOWGm+mYn1xuICAgICAgICAgICAgICAgIGlmIChhcm15LndhaXRUaW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBmcmFtZUluZGV4ID0gdGhpcy5fdGltZTJGcmFtZWluZGV4KGFybXkud2FpdFRpbWUpXG4gICAgICAgICAgICAgICAgICAgIHdoaWxlIChmcmFtZVNldC5oYXMoZnJhbWVJbmRleCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZyYW1lSW5kZXgrK1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGZyYW1lU2V0LmFkZChmcmFtZUluZGV4KVxuXG4gICAgICAgICAgICAgICAgICAgIGZyYW1lU2VxW2ZyYW1lSW5kZXhdID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZ2h0ZXJzOiBjdXJGaWdodGVycyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFybXksXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50RnJhbWVJbmRleDogZnJhbWVJbmRleCxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBsaXN0ZW5lZCA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyDpppbluKfov5vlhaXnmoTlhpvpmJ9cbiAgICAgICAgICAgICAgICBmaWdodGVycy5wdXNoKC4uLmN1ckZpZ2h0ZXJzKVxuICAgICAgICAgICAgICAgIGFybXlzLnB1c2goYXJteSlcblxuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgYXJlYUFybXkgPSB7fVxuICAgICAgICAvLyDliqDlhaXmiJHmlrnlhpvpmJ9cbiAgICAgICAgY29uc3QgbXlBcm15cyA9IHRoaXMubXlBcm15cy5tYXAoKG06IGFueSwgaSkgPT4ge1xuICAgICAgICAgICAgbS51aWQgPSB1dC5VSUQoKVxuICAgICAgICAgICAgbS5uYW1lID0gJ+aIkeaWuScgKyBpXG4gICAgICAgICAgICBtLmluZGV4ID0gaW5kZXhcbiAgICAgICAgICAgIG0ub3duZXIgPSBvd25lclxuICAgICAgICAgICAgbS5zdGF0ZSA9IDNcbiAgICAgICAgICAgIG0uZW50ZXJEaXIgPSAyXG4gICAgICAgICAgICBtLnBhd25zLmZvckVhY2goeCA9PiB7XG4gICAgICAgICAgICAgICAgeC5pbmRleCA9IGluZGV4XG4gICAgICAgICAgICAgICAgeC51aWQgPSB1dC5VSUQoKVxuICAgICAgICAgICAgICAgIHgucG9pbnQgPSB7IHg6IDUsIHk6IDAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIGFyZWFBcm15W20udWlkXSA9IG1cbiAgICAgICAgICAgIHJldHVybiBtIGFzIElBcm15VGVzdFxuICAgICAgICB9KVxuICAgICAgICAvLyDliqDlhaXmlYzmlrnlhpvpmJ9cbiAgICAgICAgY29uc3QgZW5lbXlBcm15cyA9IHRoaXMuZW5lbXlBcm15cy5tYXAoKG06IGFueSwgaSkgPT4ge1xuICAgICAgICAgICAgbS51aWQgPSB1dC5VSUQoKVxuICAgICAgICAgICAgbS5uYW1lID0gJ+aVjOaWuScgKyBpXG4gICAgICAgICAgICBtLmluZGV4ID0gaW5kZXhcbiAgICAgICAgICAgIG0ub3duZXIgPSAnMTExMTExMSdcbiAgICAgICAgICAgIG0uc3RhdGUgPSAzXG4gICAgICAgICAgICBtLmVudGVyRGlyID0gMFxuICAgICAgICAgICAgbS5wYXducy5mb3JFYWNoKHggPT4ge1xuICAgICAgICAgICAgICAgIHguaW5kZXggPSBpbmRleFxuICAgICAgICAgICAgICAgIHgudWlkID0gdXQuVUlEKClcbiAgICAgICAgICAgICAgICB4LnBvaW50ID0geyB4OiA1LCB5OiAxMCB9XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgYXJlYUFybXlbbS51aWRdID0gbVxuICAgICAgICAgICAgcmV0dXJuIG0gYXMgSUFybXlUZXN0XG4gICAgICAgIH0pXG4gICAgICAgIHN1bUZyYW1lKG15QXJteXMsIDIpXG4gICAgICAgIHN1bUZyYW1lKGVuZW15QXJteXMsIDEpXG4gICAgICAgIC8vIC8vIOWKoOWFpeeureWhlFxuICAgICAgICAvLyBmaWdodGVycy5wdXNoKHtcbiAgICAgICAgLy8gICAgIHRvd2VySWQ6IDcwMDEsXG4gICAgICAgIC8vICAgICB0b3dlckx2OiAxLFxuICAgICAgICAvLyAgICAgdWlkOiB1dC5VSUQoKSxcbiAgICAgICAgLy8gICAgIGNhbXA6IDEsXG4gICAgICAgIC8vICAgICBhdHRhY2tJbmRleDogKyt0aGlzLmF0dGFja0luZGV4LFxuICAgICAgICAvLyAgICAgcG9pbnQ6IHsgeDogNSwgeTogNSB9LFxuICAgICAgICAvLyAgICAgd2FpdFJvdW5kOiAwLFxuICAgICAgICAvLyB9KVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGZpZ2h0ZXJzLCAnZmlnaHRlcnMnKVxuXG4gICAgICAgIGNvbnN0IG9uU3RlcCA9IGxpc3RlbmVkID8gdGhpcy5fb25GcmFtZS5iaW5kKHRoaXMpIDogbnVsbFxuICAgICAgICB0aGlzLmFyZWFBcm15ID0gYXJlYUFybXlcbiAgICAgICAgdGhpcy5mcmFtZVNlcSA9IGZyYW1lU2VxXG4gICAgICAgIHRoaXMudGFnU2VxID0gdGFnU2VxXG4gICAgICAgIC8vIOiuvue9ruWbnuaUvuaVsOaNrlxuICAgICAgICBhd2FpdCBnYW1lSHByLnBsYXliYWNrLnNldFJlY29yZERhdGEoe1xuICAgICAgICAgICAgaW5kZXg6IGluZGV4LFxuICAgICAgICAgICAgZnJhbWVzOiBbe1xuICAgICAgICAgICAgICAgIHR5cGU6IDAsXG4gICAgICAgICAgICAgICAgY2FtcDogMSxcbiAgICAgICAgICAgICAgICBvd25lcjogJzExMTExMTEnLFxuICAgICAgICAgICAgICAgIGNpdHlJZDogMCxcbiAgICAgICAgICAgICAgICByYW5kU2VlZDogMTE0MjAwLFxuICAgICAgICAgICAgICAgIGZwczogMjAsXG4gICAgICAgICAgICAgICAgaHA6IFs1MCwgNTBdLFxuICAgICAgICAgICAgICAgIGFybXlzLFxuICAgICAgICAgICAgICAgIGZpZ2h0ZXJzLFxuICAgICAgICAgICAgICAgIHRvd2VycyxcbiAgICAgICAgICAgIH0gYXMgYW55LCB7XG4gICAgICAgICAgICAgICAgY3VycmVudEZyYW1lSW5kZXg6IDMwMDAwLFxuICAgICAgICAgICAgICAgIHR5cGU6IDIwMCxcbiAgICAgICAgICAgIH1dLFxuICAgICAgICAgICAgYmVnaW5UaW1lOiAwLFxuICAgICAgICAgICAgb25TdGVwXG4gICAgICAgIH0pXG4gICAgICAgIC8vIGRlYnVnZ2VyXG4gICAgICAgIHZpZXdIZWxwZXIuZ290b1dpbmQoJ3BsYXliYWNrJylcbiAgICB9XG5cbiAgICBwcml2YXRlIF9vbkZyYW1lKGZyYW1lSW5kZXg6IG51bWJlciwgYXJlYTogQXJlYU9iaikge1xuICAgICAgICBjb25zdCBmc3AgPSBhcmVhLmdldEZzcE1vZGVsKClcbiAgICAgICAgaWYgKCFmc3ApIHJldHVyblxuICAgICAgICBjb25zdCBmcmFtZURhdGEgPSB0aGlzLmZyYW1lU2VxW2ZyYW1lSW5kZXhdXG4gICAgICAgIGlmIChmcmFtZURhdGEpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZ2h0ZXJzID0gYXJlYS5nZXRGc3BNb2RlbCgpPy5nZXRCYXR0bGVDb250cm9sbGVyKCkuZ2V0RmlnaHRlcnMoKVxuICAgICAgICAgICAgbGV0IG1heEF0dGFja0luZGV4ID0gMFxuICAgICAgICAgICAgaWYgKGZpZ2h0ZXJzKSB7XG4gICAgICAgICAgICAgICAgZm9yIChsZXQgZmlnaHRlciBvZiBmaWdodGVycykge1xuICAgICAgICAgICAgICAgICAgICBtYXhBdHRhY2tJbmRleCA9IE1hdGgubWF4KG1heEF0dGFja0luZGV4LCBmaWdodGVyLmF0dGFja0luZGV4KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gdGhpcy5fc2V0QXR0YWNrSW5kZXgoZnJhbWVEYXRhLmZpZ2h0ZXJzLCBtYXhBdHRhY2tJbmRleClcbiAgICAgICAgICAgIC8vIHRoaXMuX2hhbmRsZVBsYXlCYWNrRGF0YShmcmFtZURhdGEpXG4gICAgICAgICAgICBmc3AuY2hlY2tIYXNGcmFtZURhdGFJdGVtKGZyYW1lRGF0YSlcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy50YWdTZXEgJiYgZnJhbWVJbmRleCA+IDEpIHtcbiAgICAgICAgICAgIGNvbnN0IGFybXlzID0gYXJlYS5hcm15c1xuICAgICAgICAgICAgY29uc3QgdGFnTGl2ZSA9IG5ldyBTZXRcbiAgICAgICAgICAgIGZvciAobGV0IGFybXkgb2YgYXJteXMpIHtcbiAgICAgICAgICAgICAgICBsZXQgdGFnID0gdGhpcy5hcmVhQXJteVthcm15LnVpZF0/LnRhZ1xuICAgICAgICAgICAgICAgIHRhZyAmJiB0YWdMaXZlLmFkZCh0YWcpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmb3IgKGxldCB0YWcgaW4gdGhpcy50YWdTZXEpIHtcbiAgICAgICAgICAgICAgICBpZiAodGFnTGl2ZS5oYXModGFnKSkge1xuICAgICAgICAgICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYy5sb2coJ3RhZycsIHRhZywgJ+W3suatu+S6oSwg5re75Yqg5ZCO57un5aOr5YW1JylcbiAgICAgICAgICAgICAgICBsZXQgc2VxID0gdGhpcy50YWdTZXFbdGFnXVxuICAgICAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnRhZ1NlcVt0YWddXG4gICAgICAgICAgICAgICAgZm9yIChsZXQgZGF0YSBvZiBzZXEpIHtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGZyYW1lID0gZGF0YS5mcmFtZSArIGZyYW1lSW5kZXhcbiAgICAgICAgICAgICAgICAgICAgd2hpbGUgKHRoaXMuZnJhbWVTZXFbZnJhbWVdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmcmFtZSsrXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2ZyYW1lJywgZnJhbWUsICdkYXRhJywgZGF0YSlcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5mcmFtZVNlcVtmcmFtZV0gPSBkYXRhXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqIOW8gOWni+a1i+ivlSDnqbrlj4LmlbDooajnpLrlr7nmiJggKi9cbiAgICBwdWJsaWMgYmVnaW4obGFuZElkPzogbnVtYmVyLCBkaXM/OiBudW1iZXIsIGRpcjogbnVtYmVyID0gMiwgbXVsOiBudW1iZXIgPSAxKSB7XG4gICAgICAgIGlmICghbGFuZElkKSB7XG4gICAgICAgICAgICB0aGlzLl9iYXR0bGUoKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5fYmF0dGxlTGFuZChsYW5kSWQsIGRpcywgZGlyLCBtdWwpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgYmVnaW5CeUFybXkobGFuZElkOiBudW1iZXIsIGRpczogbnVtYmVyLCB0ZXN0QXJteTogSUFybXlUZXN0W10sIGRpcjogbnVtYmVyID0gMiwgbXVsOiBudW1iZXIgPSAxKSB7XG4gICAgICAgIGlmICghbGFuZElkKSB7XG4gICAgICAgICAgICB0aGlzLl9iYXR0bGUoKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5fYmF0dGxlTGFuZChsYW5kSWQsIGRpcywgZGlyLCBtdWwsIHRlc3RBcm15KVxuICAgICAgICB9XG4gICAgfVxuXG5cbiAgICBwdWJsaWMgYmVnaW4xdjEoaGVyb0lkMT86IG51bWJlciwgaGVyb0lkMj86IG51bWJlciwgY291bnQ/OiBudW1iZXIsIHNraW5JZD86IG51bWJlcltdKSB7XG4gICAgICAgIGlmIChoZXJvSWQxID49IDEwMDAwKSB7XG4gICAgICAgICAgICB0aGlzLl9iYXR0bGUodHJ1ZSwgaGVyb0lkMSwgaGVyb0lkMiwgY291bnQsIHNraW5JZClcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuX2JhdHRsZShmYWxzZSwgaGVyb0lkMSwgaGVyb0lkMiwgY291bnQsIHNraW5JZClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHB1YmxpYyBiZWdpbkNvbmZpZyhpZDogbnVtYmVyLCB0eXBlOiBMYW5kVHlwZSwgZGlyOiBudW1iZXIgPSAyLCBtdWw6IG51bWJlciA9IDEpIHtcbiAgICAgICAgY29uc3QgYXJteUFycjogYW55W10gPSBBUk1ZX0NPTkZbaWRdXG4gICAgICAgIGlmICghYXJteUFycikge1xuICAgICAgICAgICAgcmV0dXJuIGNvbnNvbGUubG9nKCfmsqHmib7liLDnm7jlhbPphY3nva4gaWQ6ICcgKyBpZClcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBkaXMgPSBpZCAlIDEwMDBcbiAgICAgICAgY29uc3QgbGFuZElkID0gdHlwZSAqIDEwMCArIChpZCAtIGRpcykgLyAxMDAwXG4gICAgICAgIHRoaXMuX2JhdHRsZUxhbmQobGFuZElkLCBkaXMsIGRpciwgbXVsLCBhcm15QXJyKVxuICAgIH1cblxufVxuXG5pZiAoY2Muc3lzLmlzQnJvd3Nlcikge1xuICAgIHdpbmRvd1sndGVzdEJhdHRsZSddID0gbmV3IEJhdHRsZVRlc3QoKVxufVxuXG5cbiJdfQ==