
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/StudySelectPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8a60Kz+L1DA67RfSEk5i69', 'StudySelectPnlCtrl');
// app/script/view/build/StudySelectPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PawnObj_1 = require("../../model/area/PawnObj");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var StudySelectPnlCtrl = /** @class */ (function (_super) {
    __extends(StudySelectPnlCtrl, _super);
    function StudySelectPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.itemsSv_ = null; // path://root/select/items_sv
        _this.resetNode_ = null; // path://root/select/reset_be_n
        _this.infoNode_ = null; // path://root/info_n
        _this.btnStudyLbl_ = null; // path://root/buttons/study_be/btn_study_l
        _this.lookPoolNode_ = null; // path://root/buttons/look_pool_be_n
        _this.poolNode_ = null; // path://root/buttons/pool_n
        //@end
        _this.data = null;
        _this.selectId = 0;
        _this.selectIds = [];
        _this.tempLoopMap = {};
        return _this;
    }
    StudySelectPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BOOK_COMMENT] = this.onUpdateBookComment, _a.enter = true, _a),
        ];
    };
    StudySelectPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                GameHelper_1.gameHpr.book.checkInitBookStarMap();
                return [2 /*return*/];
            });
        });
    };
    StudySelectPnlCtrl.prototype.onEnter = function (data) {
        var _this = this;
        this.data = data;
        var uiStudyType = data.getUIStudyType(), isStudyPolicy = data.studyType === Enums_1.StudyType.POLICY;
        var key = isStudyPolicy ? 'ui.select_ceri_policy' : 'ui.select_ceri_item', params = isStudyPolicy ? [] : ['ui.ceri_type_name_' + uiStudyType];
        this.titleLbl_.setLocaleKey(key, params);
        this.btnStudyLbl_.setLocaleKey(isStudyPolicy ? 'ui.button_study_policy' : 'ui.button_study');
        var isExclusive = uiStudyType === Enums_1.StudyType.EXCLUSIVE;
        var canReset = this.lookPoolNode_.active = !isExclusive; // data.studyType === StudyType.POLICY || (data.studyType === StudyType.EQUIP && !isExclusive)
        if (canReset) {
            var key_1 = isStudyPolicy ? 'ui.ceri_policy_pool_title' : 'ui.ceri_pool_title', params_1 = isStudyPolicy ? [] : ['ui.ceri_type_name_' + uiStudyType];
            this.poolNode_.Child('root/title').setLocaleKey(key_1, params_1);
            this.lookPoolNode_.Child('val', cc.MultiFrame).setFrame(false);
        }
        this.poolNode_.active = false;
        this.poolNode_.scaleY = 0;
        // this.itemsSv_.node.width = canReset ? 316 : 560
        this.itemsSv_.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        this.selectIds = data.selectIds.slice();
        if (isExclusive) { // 特殊处理
            this.selectIds = [];
            var arr = GameHelper_1.gameHpr.player.getCanRecruitPawns(Constant_1.BUILD_BARRACKS_NID).filter(function (m) { return m.id > 0; }), datas_1 = assetsMgr.getJson('equipBase').datas;
            arr.forEach(function (a) {
                var data = datas_1.find(function (m) { return m.exclusive_pawn === a.id; });
                data && _this.selectIds.push(data.id);
            });
        }
        this.updateSelectList(this.selectIds);
        this.updateResetButton();
    };
    StudySelectPnlCtrl.prototype.onRemove = function () {
        this.tempLoopMap = {};
    };
    StudySelectPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/select/items_sv/view/content/item_be
    StudySelectPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        if (id) {
            this.updateSelect(id);
        }
    };
    // path://root/buttons/study_be
    StudySelectPnlCtrl.prototype.onClickStudy = function (event, data) {
        var _this = this;
        var selectIds = this.data.getUIStudyType() === Enums_1.StudyType.EXCLUSIVE ? this.selectIds : this.data.selectIds;
        if (!this.selectId || !selectIds.has(this.selectId)) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_ceir_item', { params: ['ui.ceri_type_name_' + this.data.getUIStudyType()] });
        }
        if (GameHelper_1.gameHpr.isNoviceMode && this.selectId === 3201) { //新手村刀盾兵不需要弹确认窗
            this.study();
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/StartStudyTip', { type: this.data.studyType, id: this.selectId }, function (ok) {
                if (ok && _this.isValid) {
                    _this.study();
                }
            });
        }
    };
    // path://root/select/reset_be_n
    StudySelectPnlCtrl.prototype.onClickReset = function (event, _) {
        var _this = this;
        var data = this.data;
        if (data.resetCount <= 0) {
            return this.resetSelect();
        }
        else if (GameHelper_1.gameHpr.user.getGold() < Constant_1.RESET_STUDY_SLOT_GOLD) {
            return ViewHelper_1.viewHelper.showGoldNotEnough();
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.ceri_reset_tip', {
            params: [Constant_1.RESET_STUDY_SLOT_GOLD],
            ok: function () { return _this.isValid && _this.resetSelect(); },
            cancel: function () { },
        });
    };
    // path://root/buttons/look_pool_be_n
    StudySelectPnlCtrl.prototype.onClickLookPool = function (event, data) {
        var _this = this;
        var open = !this.poolNode_.active;
        this.lookPoolNode_.Child('val', cc.MultiFrame).setFrame(open);
        if (open) {
            this.poolNode_.active = true;
            this.showRandomPool();
            this.poolNode_.scaleY = 0;
            cc.tween(this.poolNode_).to(0.2, { scaleY: 1 }, { easing: cc.easing.sineOut }).call(function () {
                _this.emitPoolVisible();
            }).start();
        }
        else {
            this.poolNode_.scaleY = 1;
            cc.tween(this.poolNode_).to(0.1, { scaleY: 0 }).call(function () {
                _this.poolNode_.active = false;
                _this.emitPoolVisible();
            }).start();
        }
    };
    // path://root/buttons/pool_n/root/list/view/content/pool_item_be
    StudySelectPnlCtrl.prototype.onClickPoolItem = function (event, _) {
        audioMgr.playSFX('click');
        var json = event.target.Data;
        if (json) {
            this.showCeriItemInfo(json);
        }
    };
    // path://root/info_n/lock_evaluate_be
    StudySelectPnlCtrl.prototype.onClickLockEvaluate = function (event, data) {
        if (this.selectId) {
            ViewHelper_1.viewHelper.showPnl('menu/BookRating', Constant_1.STUDY_TO_BOOKTYPE[this.data.studyType], this.selectId);
        }
    };
    // path://root/info_n/2/lv/edit/0/edit_pawn_lv_be
    StudySelectPnlCtrl.prototype.onClickEditPawnLv = function (event, data) {
        var node = this.infoNode_.Child(2), pawn = node.Data;
        if (!pawn) {
            return;
        }
        var type = event.target.parent.name;
        var val = type === '0' ? -1 : 1;
        var lv = pawn.lv + val;
        if (lv > 6) {
            lv = 1;
        }
        else if (lv < 1) {
            lv = 6;
        }
        pawn.lv = lv;
        pawn.updateAttrJson();
        pawn.curHp = pawn.getMaxHp();
        pawn.recordCurrHp(true);
        this.updatePawnInfo(node);
    };
    // path://root/info_n/2/skill/skills/pawn_skill_be
    StudySelectPnlCtrl.prototype.onClickPawnSkill = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkillInfoBox', event.target.Data);
    };
    // path://root/info_n/2/cost/lay/cost_desc_be
    StudySelectPnlCtrl.prototype.onClickCostDesc = function (event, _) {
        var data = event.target.parent.parent.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('common/PawnCostFactorDesc', data);
        }
    };
    // path://root/info_n/3/view/content/attrs/view_attr_be
    StudySelectPnlCtrl.prototype.onClickViewAttr = function (event, data) {
        var json = assetsMgr.getJsonData('equipBase', this.selectId);
        if (json) {
            ViewHelper_1.viewHelper.showPnl('common/EquipBaseInfoBox', json, 'book');
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新评价
    StudySelectPnlCtrl.prototype.onUpdateBookComment = function (key) {
        var type = this.data.studyType, id = this.selectId;
        if (key === type + '_' + id) {
            this.updateEvaluate(type, id);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新选择列表
    StudySelectPnlCtrl.prototype.updateSelectList = function (selectIds) {
        var _this = this;
        this.itemsSv_.stopAutoScroll();
        this.itemsSv_.Items(selectIds, function (it, id) {
            it.Data = id;
            _this.loadIcon(it.Child('val', cc.Sprite), id);
            it.Child('select').active = false;
        });
        this.itemsSv_.scrollToLeft();
        this.updateSelect(0);
    };
    StudySelectPnlCtrl.prototype.loadIcon = function (spr, id) {
        var type = this.data.studyType;
        if (type === Enums_1.StudyType.POLICY) { //政策
            ResHelper_1.resHelper.loadPolicyIcon(id, spr, this.key);
        }
        else if (type === Enums_1.StudyType.PAWN) { //士兵
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, spr, this.key);
        }
        else if (type === Enums_1.StudyType.EQUIP) { //装备
            ResHelper_1.resHelper.loadEquipIcon(id, spr, this.key);
        }
        else {
            spr.spriteFrame = null;
        }
    };
    // 刷新重置按钮
    StudySelectPnlCtrl.prototype.updateResetButton = function () {
        var data = this.data;
        this.resetNode_.active = (this.lookPoolNode_.active && (!GameHelper_1.gameHpr.isNoviceMode || data.resetCount === 0)) || (this.data.getUIStudyType() !== Enums_1.StudyType.EXCLUSIVE && this.getPoolList().length > 3);
        if (this.resetNode_.active) {
            this.resetNode_.Child('lay/val').setLocaleKey(data.resetCount > 0 ? 'ui.button_reset' : 'ui.button_free_reset');
            if (this.resetNode_.Child('lay/gold').active = data.resetCount > 0) {
                this.resetNode_.Child('lay/gold/val', cc.Label).string = Constant_1.RESET_STUDY_SLOT_GOLD + '';
            }
        }
    };
    // 刷新选择
    StudySelectPnlCtrl.prototype.updateSelect = function (id) {
        var _a;
        this.selectId = id;
        this.itemsSv_.content.children.forEach(function (m) {
            var select = m.Child('select').active = m.Data === id;
            m.Component(cc.Button).interactable = !select;
        });
        var type = this.data.studyType, uiType = this.data.getUIStudyType();
        var node = this.infoNode_.Swih(id ? type : 0, false, 'lock_evaluate_be')[0];
        // 设置最高度
        var minHeight = 160;
        if (type === Enums_1.StudyType.PAWN) {
            minHeight = 392;
        }
        else if (type === Enums_1.StudyType.EQUIP) {
            minHeight = uiType === Enums_1.StudyType.EXCLUSIVE ? 350 : 260;
        }
        // 显示内容
        if (!id) {
            node.setLocaleKey('toast.please_select_ceir_item', 'ui.ceri_type_name_' + uiType);
        }
        else if (type === Enums_1.StudyType.POLICY) { //政策
            var json = assetsMgr.getJsonData('policy', id);
            node.Child('name').setLocaleKey('policyText.name_' + json.id);
            node.Child('desc').setLocaleKey('policyText.desc_' + json.id, json.value.split(',')[0])._forceUpdateRenderData();
        }
        else if (type === Enums_1.StudyType.PAWN) { //士兵
            var pawn = node.Data = new PawnObj_1.default().init(id, null, 1, 0, -1).initAnger();
            node.Child('name/val').setLocaleKey(pawn.name);
            node.Child('name/type').setLocaleKey(pawn.type ? pawn.typeName : '');
            this.updatePawnInfo(node);
        }
        else if (type === Enums_1.StudyType.EQUIP) { //装备
            var json = assetsMgr.getJsonData('equipBase', id);
            var sv = node.Component(cc.ScrollView), content = sv.content;
            sv.stopAutoScroll();
            sv.node.height = minHeight - 60;
            sv.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            content.y = 0;
            ViewHelper_1.viewHelper.updateEquipBaseView(content, json, 'study');
        }
        // 显示评价
        this.updateEvaluate(type, id);
        // 自适应高度
        (_a = node.Component(cc.Layout)) === null || _a === void 0 ? void 0 : _a.updateLayout();
        var height = Math.max(node.height + 60, minHeight);
        if (height !== this.infoNode_.height) {
            this.infoNode_.height = height;
            this.infoNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        }
    };
    // 刷新评价
    StudySelectPnlCtrl.prototype.updateEvaluate = function (type, id) {
        var evaluate = this.infoNode_.Child('lock_evaluate_be');
        if (evaluate.active = !!id) {
            var starInfo = GameHelper_1.gameHpr.book.getBookStarInfoByStudy(type, id);
            ViewHelper_1.viewHelper.updateBookStar(evaluate.Child('lay/rating'), starInfo.star);
            evaluate.Child('lay/val', cc.Label).string = '(' + starInfo.commentCount + ')';
        }
    };
    StudySelectPnlCtrl.prototype.updatePawnInfo = function (node) {
        var pawn = node.Data;
        node.Child('lv/edit/num/val', cc.Label).string = '' + pawn.lv;
        ViewHelper_1.viewHelper.updatePawnAttrs(node.Child('attrs'), pawn);
        ViewHelper_1.viewHelper.updatePawnSkills(node.Child('skill'), pawn, this.key);
        // 行军速度
        if (node.Child('march_speed').active = pawn.marchSpeed > 0) {
            node.Child('march_speed/val', cc.Label).setLocaleKey('ui.march_speed_desc', pawn.marchSpeed);
        }
        // 费用
        ViewHelper_1.viewHelper.updatePawnCostFactor(node.Child('cost/lay/cost_desc_be'), pawn.id, pawn.baseJson);
    };
    // 显示池子信息
    StudySelectPnlCtrl.prototype.showRandomPool = function () {
        var _this = this;
        var datas = this.getPoolList();
        var sv = this.poolNode_.Child('root/list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(datas.length, function (it, i) {
            var json = it.Data = datas[i];
            _this.loadIcon(it.Child('val', cc.Sprite), json.id);
        });
    };
    StudySelectPnlCtrl.prototype.getPoolList = function () {
        var type = this.data.studyType, lv = this.data.lv + '', player = GameHelper_1.gameHpr.player;
        var pool = this.tempLoopMap[type];
        if (pool) {
            return pool;
        }
        else if (type === Enums_1.StudyType.POLICY) { //政策
            // 研究的
            var idMap_1 = GameHelper_1.gameHpr.getStudySlotIdMap(player.getPolicySlots());
            if (GameHelper_1.gameHpr.isNoviceMode) {
                pool = assetsMgr.getJson('policy').datas.filter(function (m) { return !idMap_1[m.id] && (!m.need_build_lv || String(m.need_build_lv).split(',').has(lv)) && NoviceConfig_1.NOVICE_POLICY_SLOTS.indexOf(m.id) > -1; });
            }
            else {
                pool = assetsMgr.getJson('policy').datas.filter(function (m) { return !idMap_1[m.id] && (!m.need_build_lv || String(m.need_build_lv).split(',').has(lv)); });
            }
        }
        else if (type === Enums_1.StudyType.EQUIP) { //装备
            var datas = assetsMgr.getJson('equipBase').datas;
            // 研究的
            var idMap_2 = GameHelper_1.gameHpr.getStudySlotIdMap(player.getEquipSlots());
            // 已经解锁的
            player.getUnlockEquipIds().forEach(function (id) { return idMap_2[id] = true; });
            player.getEquips().forEach(function (m) { return idMap_2[m.id] = true; });
            if (this.data.getUIStudyType() === Enums_1.StudyType.EXCLUSIVE) {
                // 研究的
                var pawnMap_1 = GameHelper_1.gameHpr.getStudySlotIdMap(player.getPawnSlots());
                // 已经解锁的
                player.getUnlockPawnIds().forEach(function (id) { return pawnMap_1[id] = true; });
                pool = datas.filter(function (m) { return !idMap_2[m.id] && !!pawnMap_1[m.exclusive_pawn]; });
            }
            else {
                pool = datas.filter(function (m) { return !idMap_2[m.id] && !m.exclusive_pawn && String(m.need_build_lv).split(',').has(lv); });
            }
        }
        else if (type === Enums_1.StudyType.PAWN) { // 士兵
            // 研究的
            var idMap_3 = GameHelper_1.gameHpr.getStudySlotIdMap(player.getPawnSlots());
            // 已经解锁的
            player.getUnlockPawnIds().forEach(function (id) { return idMap_3[id] = true; });
            var datas = assetsMgr.getJson('pawnBase').datas;
            if (this.data.id < 0) { // 重复解锁时判断条件不一样
                var pawnType_1 = Math.floor(Math.abs(this.data.id) / 100 % 10);
                pool = datas.filter(function (m) { return !idMap_3[m.id] && !!m.need_unlock && m.type === pawnType_1; });
            }
            else {
                pool = datas.filter(function (m) { return !idMap_3[m.id] && !!m.need_unlock && String(m.need_build_lv).split(',').has(lv); });
            }
        }
        else {
            pool = [];
        }
        this.tempLoopMap[type] = pool;
        return pool;
    };
    // 显示对应信息
    StudySelectPnlCtrl.prototype.showCeriItemInfo = function (json) {
        var type = this.data.studyType;
        if (type === Enums_1.StudyType.POLICY) { //政策
            ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', json.id);
        }
        else if (type === Enums_1.StudyType.EQUIP) { //装备
            ViewHelper_1.viewHelper.showPnl('common/EquipBaseInfoBox', json, 'ceri');
        }
        else if (type === Enums_1.StudyType.PAWN) { // 士兵
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', new PawnObj_1.default().init(json.id).initAnger(), null, 'ceri');
        }
    };
    // 重置选择
    StudySelectPnlCtrl.prototype.resetSelect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var info, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        info = this.data;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqCeriResetSelect({ lv: info.lv, tp: info.studyType })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.selectIds = info.selectIds = (data === null || data === void 0 ? void 0 : data.selectIds) || [];
                        info.resetCount = (data === null || data === void 0 ? void 0 : data.resetCount) || 0;
                        if (data === null || data === void 0 ? void 0 : data.useGold) {
                            GameHelper_1.gameHpr.user.setGold(data === null || data === void 0 ? void 0 : data.gold);
                        }
                        if (this.isValid) {
                            this.updateSelectList(this.selectIds);
                            this.updateResetButton();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 研究
    StudySelectPnlCtrl.prototype.study = function () {
        return __awaiter(this, void 0, void 0, function () {
            var player, type, lv, id, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        player = GameHelper_1.gameHpr.player;
                        type = this.data.studyType, lv = this.data.lv, id = this.selectId;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqStudySelect({ lv: lv, id: id, tp: type })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (type === Enums_1.StudyType.POLICY) { //政策
                            player.updatePolicySlots(data.slots);
                        }
                        else if (type === Enums_1.StudyType.PAWN) { //士兵
                            player.updatePawnSlots(data.slots);
                            this.emit(EventType_1.default.BARRACKS_SELECT_PAWN, player.getPawnSlotByLv(lv));
                        }
                        else if (type === Enums_1.StudyType.EQUIP) { //装备
                            player.updateEquipSlots(data.slots);
                            this.emit(EventType_1.default.SMITHY_SELECT_EQUIP, player.getEquipSlotByLv(lv));
                        }
                        if (this.isValid) {
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 可研究列表查看，用于新手引导框范围变化
    StudySelectPnlCtrl.prototype.emitPoolVisible = function () {
        if (GameHelper_1.gameHpr.isNoviceMode) {
            var size = this.poolNode_.getContentSize();
            size.width = 0;
            this.emit(EventType_1.default.GUIDE_CHOOSE_RECT_CHANGE, { size: size, visible: this.poolNode_.active });
        }
    };
    StudySelectPnlCtrl = __decorate([
        ccclass
    ], StudySelectPnlCtrl);
    return StudySelectPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = StudySelectPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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