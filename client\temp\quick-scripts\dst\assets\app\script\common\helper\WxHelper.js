
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/WxHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a6a6dEFrsBA0ZB3qscXnurb', 'WxHelper');
// app/script/common/helper/WxHelper.ts

"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wxHelper = void 0;
var GameHelper_1 = require("./GameHelper");
/**
 * 微信方法
 */
var WxHelper = /** @class */ (function () {
    function WxHelper() {
        this.buttons = {}; //当前创建的微信按钮
        this._log = null;
        this._systemInfo = null;
    }
    WxHelper.prototype.is = function () { return typeof wx !== 'undefined'; };
    WxHelper.prototype.promisify = function (api) {
        if (!this.is()) {
            return function () {
                return __awaiter(this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/];
                }); });
            };
        }
        var cb = wx[api];
        if (!cb) {
            return function () {
                return __awaiter(this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/];
                }); });
            };
        }
        return function (args) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    return [2 /*return*/, new Promise(function (resolve, reject) {
                            cb(__assign({ fail: reject, success: resolve }, args));
                        })];
                });
            });
        };
    };
    Object.defineProperty(WxHelper.prototype, "log", {
        get: function () {
            if (this._log) {
                return this._log;
            }
            else if (this.is()) {
                this._log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;
            }
            this._log = this._log || { error: function () { }, info: function () { }, setFilterMsg: function () { } };
            return this._log;
        },
        enumerable: false,
        configurable: true
    });
    WxHelper.prototype.info = function () {
        var _a;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        (_a = this.log).info.apply(_a, __spread([this.log], params));
    };
    WxHelper.prototype.error = function () {
        var _a;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        (_a = this.log).error.apply(_a, __spread([this.log], params));
    };
    WxHelper.prototype.setFilterMsg = function (val) {
        this.log.setFilterMsg(val);
    };
    WxHelper.prototype.errorAndFilter = function (val) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        this.setFilterMsg(val);
        this.error.apply(this, __spread(params));
    };
    // 获取小程序启动时的参数
    WxHelper.prototype.getLaunchInfo = function () {
        try {
            var launchInfo = wx.getLaunchOptionsSync();
            var query = launchInfo.query;
            if (query && Object.keys(query).length > 0) {
                return query;
            }
        }
        catch (e) {
            this.error('getLaunchOptionsSync', e);
        }
        return {};
    };
    // 获取进入时的参数
    WxHelper.prototype.getEnterInfo = function () {
        try {
            var enterInfo = wx.getEnterOptionsSync();
            var query = enterInfo === null || enterInfo === void 0 ? void 0 : enterInfo.query;
            if (query && Object.keys(query).length > 0) {
                return query;
            }
            else {
                return this.getLaunchInfo();
            }
        }
        catch (e) {
            this.error('getEnterInfo', e);
        }
        return {};
    };
    // 获取是否正式版
    WxHelper.prototype.isRelease = function () {
        var _a, _b;
        if (!this.is()) {
            return false;
        }
        else if (!wx['getAccountInfoSync']) {
            return true;
        }
        var version = (_b = (_a = wx.getAccountInfoSync()) === null || _a === void 0 ? void 0 : _a.miniProgram) === null || _b === void 0 ? void 0 : _b.envVersion;
        // console.log('envVersion=' + version)
        return version === 'release';
    };
    // 判断是否大于或小于某个版本, 大于返回1，小于返回-1, 相等返回0
    WxHelper.prototype.compareVersion = function (v2) {
        if (!this.is()) {
            return -1;
        }
        var systemInfo = this.getSystemInfo();
        var v1 = systemInfo.SDKVersion;
        if (!v1 || !v2) {
            return -1;
        }
        var vv1 = v1.split('.');
        var vv2 = v2.split('.');
        var len = Math.max(vv1.length, vv2.length);
        while (vv1.length < len) {
            vv1.push('0');
        }
        while (vv2.length < len) {
            vv2.push('0');
        }
        for (var i = 0; i < len; i++) {
            var num1 = parseInt(vv1[i]);
            var num2 = parseInt(vv2[i]);
            if (num1 > num2) {
                return 1;
            }
            else if (num1 < num2) {
                return -1;
            }
        }
        return 0;
    };
    WxHelper.prototype.getSystemInfo = function () {
        if (!this.is()) {
            return {};
        }
        else if (!this._systemInfo) {
            this._systemInfo = wx.getSystemInfoSync();
        }
        return this._systemInfo;
    };
    // 获取授权信息
    WxHelper.prototype.getAuthSetting = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            var self;
            return __generator(this, function (_a) {
                if (!this.is()) {
                    return [2 /*return*/, false];
                }
                self = this;
                return [2 /*return*/, new Promise(function (resolve) {
                        wx.getSetting({
                            success: function (res) {
                                resolve(!!(res === null || res === void 0 ? void 0 : res.authSetting[key]));
                            },
                            fail: function () {
                                self.error('getAuthSetting error. key=' + key);
                                resolve(false);
                            }
                        });
                    })];
            });
        });
    };
    // 是否授权了用户信息
    WxHelper.prototype.isAuthUserInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.getAuthSetting('scope.userInfo')];
            });
        });
    };
    WxHelper.prototype.getRect = function (node) {
        var nodeRec = node.getBoundingBoxToWorld();
        // console.log('nodeRec', nodeRec.x, nodeRec.y, nodeRec.width, nodeRec.height)
        nodeRec.x -= 2;
        nodeRec.y -= 2;
        nodeRec.width += 4;
        nodeRec.height += 4;
        var frameSize = cc.view.getFrameSize();
        var winSize = cc.winSize;
        var height = nodeRec.height / winSize.height * frameSize.height;
        var width = nodeRec.width / winSize.width * frameSize.width;
        var left = nodeRec.x / winSize.width * frameSize.width;
        var top = frameSize.height - (nodeRec.y + nodeRec.height) / winSize.height * frameSize.height;
        return { height: height, width: width, left: left, top: top };
    };
    // wx toast，默认提示版本问题
    WxHelper.prototype.createModalBtn = function (node, title, content) {
        if (title === void 0) { title = '提示'; }
        if (content === void 0) { content = '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'; }
        var btn = node.getComponent(cc.Button);
        if (!btn) {
            btn = node.addComponent(cc.Button);
        }
        btn.interactable = true;
        node.on('click', function () { return wx.showModal({ title: title, content: content }); });
        return null;
    };
    WxHelper.prototype.createButton = function (api, node, args) {
        var _this = this;
        var func = wx[api];
        if (!func) {
            return this.createModalBtn(node);
        }
        var _a = this.getRect(node), height = _a.height, width = _a.width, left = _a.left, top = _a.top;
        args = args || {
            type: 'text',
            text: '',
            style: {
                left: left,
                top: top,
                width: width,
                height: height,
                lineHeight: 0,
                backgroundColor: '',
                color: '#FFFFFF',
                textAlign: 'center',
                fontSize: 16,
                borderRadius: 4
            }
        };
        if (typeof qq != 'undefined') {
            args.withCredentials = true;
        }
        var button = func(args);
        ut.waitNextFrame().then(function () {
            if (node.isValid) {
                var rect = _this.getRect(node);
                button.style.left = rect.left;
                button.style.top = rect.top;
            }
        });
        this.buttons[node.uuid] = button;
        return button;
    };
    // 创建用户授权按钮
    WxHelper.prototype.createUserInfoButton = function (node) {
        return this.createButton('createUserInfoButton', node);
    };
    // 创建反馈按钮
    WxHelper.prototype.createFeedbackButton = function (node, args) {
        return this.createButton('createFeedbackButton', node, args);
    };
    // 创建游戏圈按钮
    WxHelper.prototype.createGameClubButton = function (node, args) {
        return this.createButton('createGameClubButton', node, args);
    };
    // 销毁按钮
    WxHelper.prototype.destroyButton = function (node) {
        var _a;
        if (node) {
            var uuid = node.uuid;
            (_a = this.buttons[uuid]) === null || _a === void 0 ? void 0 : _a.destroy();
            delete this.buttons[uuid];
        }
    };
    WxHelper.prototype.destroyAllButton = function () {
        for (var key in this.buttons) {
            this.buttons[key].destroy();
        }
        this.buttons = {};
    };
    // 获取登录code
    WxHelper.prototype.getLoginCode = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.is()) {
                    return [2 /*return*/, ''];
                }
                return [2 /*return*/, new Promise(function (resolve) {
                        wx.login({
                            success: function (res) {
                                resolve((res === null || res === void 0 ? void 0 : res.code) || '');
                            }
                        });
                    })];
            });
        });
    };
    // 获取用户信息
    WxHelper.prototype.getUserInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var self;
            return __generator(this, function (_a) {
                if (!this.is()) {
                    return [2 /*return*/, null];
                }
                self = this;
                return [2 /*return*/, new Promise(function (resolve) {
                        wx.getUserInfo({
                            // withCredentials: true,
                            lang: 'zh_CN',
                            success: function (res) {
                                resolve(res === null || res === void 0 ? void 0 : res.userInfo);
                            },
                            fail: function () {
                                resolve(null);
                            }
                        });
                    })];
            });
        });
    };
    // 设置到剪切板
    WxHelper.prototype.setClipboardData = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (this.is()) {
                    return [2 /*return*/, this.promisify('setClipboardData')(data)];
                }
                return [2 /*return*/];
            });
        });
    };
    // 分享
    WxHelper.prototype.share = function (title, imgUrl, queryInfo, loose) {
        if (queryInfo === void 0) { queryInfo = {}; }
        if (loose === void 0) { loose = true; }
        return __awaiter(this, void 0, void 0, function () {
            var queryStr, wxShare;
            return __generator(this, function (_a) {
                if (!this.is()) {
                    return [2 /*return*/];
                }
                queryStr = GameHelper_1.gameHpr.encodeURIObj(queryInfo);
                wxShare = wx.uma || wx;
                wxShare.shareAppMessage({
                    title: title,
                    imageUrl: imgUrl,
                    query: queryStr,
                });
                return [2 /*return*/, true];
            });
        });
    };
    return WxHelper;
}());
exports.wxHelper = new WxHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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