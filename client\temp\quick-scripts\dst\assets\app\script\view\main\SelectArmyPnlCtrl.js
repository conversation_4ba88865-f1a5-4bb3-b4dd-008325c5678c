
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SelectArmyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a8e60EkzuROU6aqtemfwbiJ', 'SelectArmyPnlCtrl');
// app/script/view/main/SelectArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var GuideConfig_1 = require("../../model/guide/GuideConfig");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var SelectArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectArmyPnlCtrl, _super);
    function SelectArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://root/top_n
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.listSv_ = null; // path://root/list_sv
        _this.sortSelectNode_ = null; // path://root/sort_select_be_n
        _this.autobackSelectNode_ = null; // path://root/autoback_select_be_n
        _this.sameSpeedTge_ = null; // path://root/same_speed_t
        _this.selectedCountNode_ = null; // path://root/selected_count_n
        _this.riskNode_ = null; // path://root/ok_be/risk_n
        _this.forecastNode_ = null; // path://root/forecast_be_n
        //@end
        _this.cb = null;
        _this.type = '';
        _this.targetCell = null;
        _this.targetAreaMaxArmyCount = 0; //目标区域最大军队数量
        _this.armys = []; //当前的军队列表
        _this.selectArmys = []; //当前选择的
        _this.currSelectSort = 0; //当前选择的排序方式
        _this.currSelectAutoback = 0; //当前选择的自动返回
        _this.monsterAttack = 0; //野怪战力
        return _this;
    }
    SelectArmyPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectArmyPnlCtrl.prototype.onEnter = function (type, index, list, canGotoCount, cb) {
        var _this = this;
        this.titleLbl_.setLocaleKey('ui.title_select_army_' + type);
        this.type = type;
        this.targetAreaMaxArmyCount = canGotoCount || 0;
        this.cb = cb;
        var isOccupy = type === 'occupy';
        var cell = this.targetCell = GameHelper_1.gameHpr.world.getMapCellByIndex(index), isAncient = !!(cell === null || cell === void 0 ? void 0 : cell.isAncient());
        this.monsterAttack = isOccupy ? (cell === null || cell === void 0 ? void 0 : cell.getMonsterAttack()) || 0 : 0; //获取野地的战斗力
        this.forecastNode_.active = !GameHelper_1.gameHpr.isNoviceMode && isOccupy && !cell.owner && !isAncient;
        // top的内容
        this.updateTopUI();
        // 排序选择
        // this.selectSortItem(this.sortSelectNode_, gameHpr.user.getLocalPreferenceData(PreferenceKey.SELECT_ARMY_SORT) ?? 0, true)
        // 刷新列表
        var upSpeedMul = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MAIN_MARCH_MUL) || Constant_1.UP_MARCH_SPEED_MUL; //加速倍数
        // 是否第一次新手引导
        var isGuideMove = GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.FIRST_BATTLE_MOVE), isNoviceMode = GameHelper_1.gameHpr.isNoviceMode;
        var cd = this.getBuildMarchCD() + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MARCH_CD);
        // 加速季节的
        cd += Math.floor(GameHelper_1.gameHpr.world.getSeason().getEffect(Enums_1.CEffect.MARCH_CD) * 100);
        // 加遗迹的
        cd += GameHelper_1.gameHpr.getAncientEffectByPlayer(GameHelper_1.gameHpr.getUid(), Enums_1.CEffect.MARCH_CD);
        // 刷新列表
        this.armys = list.map(function (m) {
            m.defaultMarchSpeed = GameHelper_1.gameHpr.getArmyMarchSpeed(m);
            m.marchSpeed = m.marchSpeed || m.defaultMarchSpeed;
            m.dis = GameHelper_1.gameHpr.getToMapCellDis(m.index, index);
            m.attack = GameHelper_1.gameHpr.getPawnsAttack(m.pawns); //战力
            // 是否加速
            var upSpeedState = 0, mul = 0, mainOutUpDis = 0;
            if (isGuideMove) {
                mul = NoviceConfig_1.NOVICE_FIRST_MOVE_MUL;
                upSpeedState = 3;
            }
            else if (isNoviceMode) {
                mul = NoviceConfig_1.NOVICE_ARMY_MOVE_MUL;
                upSpeedState = 4;
            }
            else {
                // 城市加速
                mul += ((_this.type === 'move' || _this.type === 'tonden') && _this.isCanUpSpeed(m.index, index)) ? upSpeedMul : 0;
                // 是否城边加速
                mainOutUpDis = GameHelper_1.gameHpr.getMainOutMarchSeepUpDis(index, m.index);
                mul += (Constant_1.MAIN_CITY_MARCH_SPEED[mainOutUpDis] || 0);
                upSpeedState = mainOutUpDis >= 0 ? 2 : (mul > 0 ? 1 : 0);
            }
            return {
                uid: m.uid,
                data: m,
                dis: m.dis,
                cd: cd,
                mainOutUpDis: Math.max(0, mainOutUpDis),
                time: _this.getMarchTime(m, cd, mul),
                upSpeedState: upSpeedState,
                upSpeedMul: mul,
                states: [],
                attack: m.attack,
            };
        });
        this.updateArmys();
        // 设置自动返回
        // if (this.autobackSelectNode_.active = isOccupy) {
        //     this.selectAutobackItem(this.autobackSelectNode_, gameHpr.user.getLocalPreferenceData(PreferenceKey.BATTLE_AUTO_BACK_TYPE) ?? 0)
        // }
        // 设置同时抵达
        this.sameSpeedTge_.isChecked = !!GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.ARRIVE_SIMULTANEOUSLY);
    };
    SelectArmyPnlCtrl.prototype.onRemove = function () {
        if (this.currSelectSort !== GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SELECT_ARMY_SORT)) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SELECT_ARMY_SORT, this.currSelectSort);
        }
        // if (this.autobackSelectNode_.active && this.currSelectAutoback !== gameHpr.user.getLocalPreferenceData(PreferenceKey.BATTLE_AUTO_BACK_TYPE)) {
        //     gameHpr.user.setLocalPreferenceData(PreferenceKey.BATTLE_AUTO_BACK_TYPE, this.currSelectAutoback)
        // }
        if (this.sameSpeedTge_.isChecked !== GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.ARRIVE_SIMULTANEOUSLY)) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.ARRIVE_SIMULTANEOUSLY, this.sameSpeedTge_.isChecked);
        }
        this.cb = null;
    };
    SelectArmyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    SelectArmyPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var _a, _b, _c;
        if (!((_a = this.selectArmys) === null || _a === void 0 ? void 0 : _a.length)) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
        }
        else if (this.riskNode_.active && !GameHelper_1.gameHpr.isNoLongerTip('show_risk_tip')) {
            return ViewHelper_1.viewHelper.showPnl('main/RiskTip', this.selectArmys, this.targetCell, function () { return _this.isValid && _this.do(); });
        }
        else if (this.type === 'occupy' && !this.targetCell.owner && !this.targetCell.isAncient()) {
            var treasuresCountStr = ((_b = this.targetCell.getLandAttr()) === null || _b === void 0 ? void 0 : _b.treasures_count) || '0,0';
            var _d = __read(ut.stringToNumbers(treasuresCountStr, ','), 2), minCount = _d[0], maxCount = _d[1];
            var cap = this.selectArmys.reduce(function (a, b) {
                var cur = 0, max = 0;
                b.pawns.forEach(function (p) {
                    var _a;
                    cur += p.treasures.length;
                    max += (((_a = assetsMgr.getJsonData('pawnBase', p.id)) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
                });
                return a + (max - cur);
            }, 0);
            if (maxCount > cap && ViewHelper_1.viewHelper.showNoLongerTip('treasure_count_not_enough', {
                content: 'ui.treasure_count_not_enough',
                select: false,
                okText: 'ui.button_goon',
                ok: function () { return _this.do(); },
            })) {
                return;
            }
        }
        else if (this.type === 'tonden' && !!this.targetCell.owner) {
            var treasuresCountStr = ((_c = this.targetCell.getLandAttr()) === null || _c === void 0 ? void 0 : _c.treasures_count) || '0,0';
            var _e = __read(ut.stringToNumbers(treasuresCountStr, ','), 2), minCount = _e[0], maxCount = _e[1];
            var cap = this.selectArmys.reduce(function (a, b) {
                var cur = 0, max = 0;
                b.pawns.forEach(function (p) {
                    var _a;
                    cur += p.treasures.length;
                    max += (((_a = assetsMgr.getJsonData('pawnBase', p.id)) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
                });
                return a + (max - cur);
            }, 0);
            if (maxCount > cap) {
                ViewHelper_1.viewHelper.showMessageBox('ui.treasure_count_not_enough', {
                    okText: 'ui.button_goon',
                    ok: function () { return _this.do(); },
                    cancel: function () { }
                });
                return;
            }
        }
        this.do();
    };
    // path://root/list_sv/view/content/item_be
    SelectArmyPnlCtrl.prototype.onClickItem = function (event, _) {
        var _this = this;
        var _a, _b;
        audioMgr.playSFX('click');
        var data = event.currentTarget.Data;
        var state = (_b = (_a = event.currentTarget['_data']) === null || _a === void 0 ? void 0 : _a.states) === null || _b === void 0 ? void 0 : _b[0];
        if (state) {
            var key = 'ui.army_state_' + state;
            if (state === Enums_1.ArmyState.FIGHT) {
                key = 'toast.battling_cant_operation';
            }
            return ViewHelper_1.viewHelper.showAlert(key);
        }
        else if (this.selectArmys.remove('uid', data.uid)) {
        }
        else if (this.isNotAttackPlayerByDis(data.dis)) {
            if (!GameHelper_1.gameHpr.isNoLongerTip('occupy_dis_cond')) {
                return ViewHelper_1.viewHelper.showPnl('common/NoLongerTip', {
                    noKey: 'occupy_dis_cond',
                    content: 'ui.occupy_player_dis_cond_tip',
                    okText: 'ui.button_gotit',
                });
            }
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.OCCUPY_CELL_DIS_COND);
        }
        else if (this.selectArmys.length < this.targetAreaMaxArmyCount) {
            this.selectArmys.push(data);
            this.selectArmys.sort(function (a, b) { return a.marchTime - b.marchTime; });
        }
        else {
            return ViewHelper_1.viewHelper.showAlert('toast.yet_exceed_max_army_count');
        }
        var obj = this.getSelectArmyMap();
        this.listSv_.content.children.forEach(function (m) { var _a, _b; return _this.updateSelectItem(m, (_b = obj[(_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid]) !== null && _b !== void 0 ? _b : -1); });
        this.updateSelectArmyCount();
    };
    // path://root/list_sv/view/content/item_be/pos_be
    SelectArmyPnlCtrl.prototype.onClickPos = function (event, _) {
        var data = event.target.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos(data.index);
        }
    };
    // path://root/sort_select_be_n
    SelectArmyPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true, true);
    };
    // path://root/sort_select_be_n/mask/root/sort_items_nbe
    SelectArmyPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = this.sortSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false, true);
        var type = Number(event.target.name);
        if (type !== this.currSelectSort) {
            this.selectSortItem(node, type);
        }
    };
    // path://root/autoback_select_be_n
    SelectArmyPnlCtrl.prototype.onClickAutobackSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true, true);
    };
    // path://root/autoback_select_be_n/mask/root/autoback_items_nbe
    SelectArmyPnlCtrl.prototype.onClickAutobackItems = function (event, data) {
        var node = this.autobackSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false, true);
        var type = Number(event.target.name);
        if (type !== this.currSelectAutoback) {
            this.selectAutobackItem(node, type);
        }
    };
    // path://root/sort_select_be_n/select_mask_be
    SelectArmyPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false, true);
    };
    // path://root/list_sv/view/content/item_be/march_speed_be
    SelectArmyPnlCtrl.prototype.onClickMarchSpeed = function (event, _) {
        var _this = this;
        var it = event.target.parent;
        var army = it === null || it === void 0 ? void 0 : it.Data;
        if (army) {
            ViewHelper_1.viewHelper.showPnl('main/ModifyMarchSpeed', army, function (speed) {
                if (!speed || army.marchSpeed === speed) {
                    return;
                }
                army.marchSpeed = speed;
                if (_this.isValid) {
                    var data = _this.armys.find(function (m) { return m.uid === army.uid; });
                    if (!data) {
                        return;
                    }
                    data.time = _this.getMarchTime(army, data.cd, data.upSpeedMul);
                    var select = _this.selectArmys.some(function (m) { return m.uid === army.uid; });
                    if (select) {
                        _this.selectArmys.sort(function (a, b) { return a.marchTime - b.marchTime; });
                    }
                    if (_this.currSelectSort === 0) { //时间排序 直接刷新整个列表
                        _this.updateArmys(false, false);
                    }
                    else { //否则刷新单个
                        _this.updateArmyMarchSpeed(it, data);
                        // 还要刷新序号
                        if (select) {
                            var obj_1 = _this.getSelectArmyMap();
                            _this.listSv_.content.children.forEach(function (m) { var _a, _b; return _this.updateSelectItem(m, (_b = obj_1[(_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid]) !== null && _b !== void 0 ? _b : -1); });
                        }
                    }
                }
            });
        }
    };
    // path://root/forecast_be_n
    SelectArmyPnlCtrl.prototype.onClickForecast = function (event, data) {
        var _a;
        if (!((_a = this.selectArmys) === null || _a === void 0 ? void 0 : _a.length) || !this.targetCell) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
        }
        else if (GameHelper_1.gameHpr.isBattleingByIndex(this.targetCell.actIndex)) {
            return ViewHelper_1.viewHelper.showAlert('toast.battle_not_forecast_tip');
        }
        ViewHelper_1.viewHelper.showPnl('main/BattleForecast', this.selectArmys, this.targetCell);
    };
    // path://root/list_sv/view/content/item_be/enter_dir_be
    SelectArmyPnlCtrl.prototype.onClickEnterDir = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('main/EnterDirDesc', event.target.Data || 0);
    };
    // path://root/top_n/info/monster/drop_treasure_be
    SelectArmyPnlCtrl.prototype.onClickDropTreasure = function (event, data) {
        audioMgr.playSFX('click');
        var cell = this.targetCell;
        ViewHelper_1.viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectArmyPnlCtrl.prototype.updateTopUI = function () {
        var _this = this;
        var cell = this.targetCell, isOccupy = this.type === 'occupy', isAncient = !!(cell === null || cell === void 0 ? void 0 : cell.isAncient());
        this.topNode_.Child('type', cc.MultiFrame).setFrame(isOccupy);
        this.topNode_.Child('name').setLocaleKey(cell.getName());
        if (!!cell.owner || isAncient) {
            var node = this.topNode_.Child('info').Swih('player')[0];
            var info = GameHelper_1.gameHpr.getPlayerInfo(cell.owner), name = node.Child('head/name');
            ResHelper_1.resHelper.loadPlayerHead(node.Child('head/val'), info ? info.headIcon : 'head_icon_000', this.key);
            name.Color(this.getTargetOwnerColor(cell));
            name.setLocaleKey('ui.whose_territory', !info ? 'ui.system' : (info.uid === GameHelper_1.gameHpr.getUid() ? 'ui.me' : "<fontFamily=Arial>" + ut.nameFormator(info.nickname, 15) + "</>"));
            // 显示建筑
            var build_1 = node.Child('build'), lvLbl_1 = node.Child('build/lv', cc.Label);
            lvLbl_1.setLocaleKey('');
            var _a = cell.getAreaBuildIcon(), id_1 = _a.id, icon = _a.icon;
            ResHelper_1.resHelper.loadBuildIcon(icon, build_1, this.key).then(function () {
                if (_this.isValid) {
                    lvLbl_1.setLocaleKey('ui.build_lv_short', cell.getPawnInfo().lv || 1, 'buildText.name_' + id_1);
                    lvLbl_1._forceUpdateRenderData();
                    build_1.x = 268 - Math.floor(Math.max(0, lvLbl_1.node.width - build_1.width - 16) / 2);
                }
            });
        }
        else {
            var node = this.topNode_.Child('info').Swih('monster')[0], landAttr = cell.getLandAttr();
            var _b = __read(cell.getLandDifficultyLv(landAttr), 2), type = _b[0], lv = _b[1], isBoss = landAttr.hp === -1;
            node.Child('difficulty/bg').Color(Constant_1.DIFFICULTY_BG_COLOR[type]);
            node.Child('difficulty/bg/val', cc.Label).setLocaleKey('ui.land_difficulty_' + type, lv);
            node.Child('difficulty/tip').active = type > GameHelper_1.gameHpr.player.getMaxOccupyLandDifficulty();
            // 显示怪物
            var pawn = node.Child('pawn');
            pawn.active = !isBoss;
            node.Child('boss').active = isBoss;
            var armysStr = landAttr['armys_' + cell.landType] || landAttr.armys_3;
            var id = Number(armysStr.split('|')[0].split(',')[0]);
            ResHelper_1.resHelper.loadPawnHeadIcon(id, isBoss ? node.Child('boss/val') : pawn, this.key);
            // 宝箱
            node.Child('drop_treasure_be/val', cc.Label).string = landAttr.treasures_count.replace(',', '~');
        }
    };
    // 获取边界线颜色
    SelectArmyPnlCtrl.prototype.getTargetOwnerColor = function (cell) {
        if (cell.isOwn()) {
            return '#6AFF96';
        }
        else if (cell.isOneAlliance()) {
            return '#71D1FF';
        }
        return '#FF747D';
    };
    SelectArmyPnlCtrl.prototype.getMarchTime = function (data, cd, mul) {
        var time = Math.floor(data.dis * (ut.Time.Hour / data.marchSpeed));
        // 是否有减少时间的政策
        time = Math.floor(time * (1 - cd * 0.01));
        // 计算需要的时间
        data.marchTime = Math.floor(mul > 0 ? Math.max(time / mul, 1000) : time);
        return data.marchTime;
    };
    // 获取校场行军加速cd
    SelectArmyPnlCtrl.prototype.getBuildMarchCD = function () {
        var build = GameHelper_1.gameHpr.player.getMainBuilds().find(function (m) { return m.id === 2011; });
        if (!build) {
            return 0;
        }
        var json = assetsMgr.getJsonData('buildAttr', build.id * 1000 + build.lv);
        return json ? ut.stringToNumbers(json.effects, ',')[1] || 0 : 0;
    };
    // 是否加速
    SelectArmyPnlCtrl.prototype.isCanUpSpeed = function (aindex, bindex) {
        var acell = GameHelper_1.gameHpr.world.getMapCellByIndex(aindex);
        var bcell = GameHelper_1.gameHpr.world.getMapCellByIndex(bindex);
        return (acell === null || acell === void 0 ? void 0 : acell.isCanUpSpeedMarchCity()) && (bcell === null || bcell === void 0 ? void 0 : bcell.isCanUpSpeedMarchCity());
    };
    // 刷新列表
    SelectArmyPnlCtrl.prototype.updateArmys = function (updateState, updateSelect) {
        var _this = this;
        if (updateState === void 0) { updateState = true; }
        if (updateSelect === void 0) { updateSelect = true; }
        // 训练列表
        var lvingPawnLvMap = {};
        GameHelper_1.gameHpr.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
        // 更新状态
        if (updateState) {
            this.armys.forEach(function (m) {
                var states = [], data = m.data;
                if (data.state !== Enums_1.ArmyState.NONE) {
                    states.push(data.state);
                }
                else {
                    if (data.drillPawns.length > 0) {
                        states.push(Enums_1.ArmyState.DRILL);
                    }
                    if (data.curingPawns.length > 0) {
                        states.push(Enums_1.ArmyState.CURING);
                    }
                    if (data.pawns.some(function (p) { return lvingPawnLvMap[p.uid]; })) {
                        states.push(Enums_1.ArmyState.LVING);
                    }
                    if (GameHelper_1.gameHpr.world.getArmyTondenInfo(data.index, data.uid)) {
                        states.push(Enums_1.ArmyState.TONDEN);
                    }
                }
                m.states = states;
            });
        }
        // 排序
        this.armys.sort(function (a, b) {
            if (a.states.length > 0 || b.states.length > 0) {
                return (a.states[0] || 0) - (b.states[0] || 0);
            }
            else if (_this.currSelectSort === 0) {
                return a.time === b.time ? b.attack - a.attack : a.time - b.time;
            }
            return a.attack === b.attack ? a.time - b.time : b.attack - a.attack;
        });
        // 刷新选择  所有模式下都不再自动选
        if (updateSelect) {
            // 攻击玩家的地 判断位置 
            this.selectArmys = [];
        }
        var selectArmyMap = this.getSelectArmyMap();
        //
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Items(this.armys, function (it, m) {
            var _a, _b, _c;
            it['_data'] = m;
            var data = it.Data = m.data;
            it.Child('name', cc.Label).string = data.name;
            ViewHelper_1.viewHelper.updatePositionView(it.Child('pos_be'), data.index);
            it.Child('target', cc.Label).setLocaleKey('ui.target_distance_count', m.dis);
            var pawns = data.pawns;
            it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), function (node2, pawn) {
                var _a;
                var icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                var lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id), icon, _this.key);
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                if (node2.Child('hp').active = (!isId && !isCuring)) {
                    node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1];
                }
            });
            // 显示距离
            if (it.Child('dis').active = _this.type === 'occupy' && !!((_a = _this.targetCell) === null || _a === void 0 ? void 0 : _a.owner) && (!m.upSpeedState || GameHelper_1.gameHpr.isNoviceMode)) {
                var color = m.dis > Constant_1.OCCUPY_PLAYER_CELL_MIN_DIS ? '#C34A32' : '#B6A591';
                it.Child('dis', cc.Label).Color(color).setLocaleKey('ui.dis_grid_count', m.dis);
            }
            // 进入方向
            var enterDir = it.Child('enter_dir_be');
            if (enterDir.active = !!_this.targetCell) {
                var dir = enterDir.Data = MapHelper_1.mapHelper.getAddArmyDir(data.index, (_b = _this.targetCell) === null || _b === void 0 ? void 0 : _b.actIndex);
                enterDir.Child('val').angle = 180 - (dir * 90);
            }
            // 刷新状态
            _this.updateArmyState(it, m.states);
            // 行军速度
            _this.updateArmyMarchSpeed(it, m);
            // 选择
            _this.updateSelectItem(it, (_c = selectArmyMap[data.uid]) !== null && _c !== void 0 ? _c : -1);
            //
            var canSelect = !m.states.length;
            it.opacity = canSelect ? 255 : 200;
            it.Color(canSelect ? '#E9DDC7' : '#DCDBD3');
        });
        this.updateSelectArmyCount();
    };
    // 刷新选择
    SelectArmyPnlCtrl.prototype.updateSelectItem = function (it, selectIndex) {
        var _a, _b;
        var select = it.Child('select').active = selectIndex !== -1;
        it.Child('no', cc.MultiFrame).setFrame(select);
        it.Child('no/val', cc.Label).string = select ? '' + (selectIndex + 1) : '';
        // 只有选择的时候才可点击位置跳转
        var canSelect = select || !!((_b = (_a = it['_data']) === null || _a === void 0 ? void 0 : _a.states) === null || _b === void 0 ? void 0 : _b.length);
        it.Child('pos_be').Color(canSelect ? '#936E5A' : '#B6A591');
        it.Child('pos_be', cc.Button).interactable = canSelect;
        it.Child('pos_be/line').active = canSelect;
    };
    // 行军速度
    SelectArmyPnlCtrl.prototype.updateArmyMarchSpeed = function (it, m) {
        var _a, _b;
        var data = m.data;
        if (it.Child('time').active = !((_a = m.states) === null || _a === void 0 ? void 0 : _a.length)) {
            it.Child('time/val', cc.Label).Color(!!m.upSpeedState ? '#6DB14C' : '#936E5A').string = ut.millisecondFormat(m.time, 'h:mm:ss');
            var isMul = it.Child('time/mul').active = m.upSpeedState > 0;
            var isUp = it.Child('time/up').active = !isMul && !!m.cd;
            if (isMul) {
                it.Child('time/mul', cc.Label).setLocaleKey('ui.up_march_speed_' + m.upSpeedState, m.upSpeedMul, m.mainOutUpDis);
            }
            else if (isUp) {
                it.Child('time/up', cc.Label).string = '(-' + m.cd + '%)';
            }
        }
        if (it.Child('march_speed_be').active = false /* !gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0 */) {
            var marchSpeedLbl = it.Child('march_speed_be', cc.Label), line = it.Child('march_speed_be/line');
            marchSpeedLbl.Color(data.marchSpeed !== data.defaultMarchSpeed ? '#B6A591' : '#936E5A').setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            if (line.active = it.Child('march_speed_be', cc.Button).interactable = !((_b = m.states) === null || _b === void 0 ? void 0 : _b.length)) {
                marchSpeedLbl._forceUpdateRenderData();
                line.width = marchSpeedLbl.node.width;
            }
        }
    };
    // 刷新状态
    SelectArmyPnlCtrl.prototype.updateArmyState = function (node, states) {
        node.Child('state').Items(states, function (it, state) {
            it.Color(Constant_1.ARMY_STATE_COLOR[state]).setLocaleKey('ui.army_state_' + state);
        });
    };
    SelectArmyPnlCtrl.prototype.updateSelectArmyCount = function () {
        var _a, _b;
        this.selectedCountNode_.setLocaleKey('ui.selected_army_count', this.selectArmys.length + '/' + this.targetAreaMaxArmyCount);
        if (this.type === 'occupy' && !((_a = this.targetCell) === null || _a === void 0 ? void 0 : _a.owner) && this.selectArmys.length > 0) {
            var attack = GameHelper_1.gameHpr.getArmysAttack((_b = this.selectArmys) !== null && _b !== void 0 ? _b : []);
            this.riskNode_.active = attack < this.monsterAttack && !GameHelper_1.gameHpr.isNoLongerTip('show_risk_tip');
        }
        else {
            this.riskNode_.active = false;
        }
    };
    // 选择排序
    SelectArmyPnlCtrl.prototype.selectSortItem = function (node, type, init) {
        node.Data = this.currSelectSort = type;
        node.Child('val', cc.Label).setLocaleKey('ui.select_army_sort_' + type);
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
        if (!init) {
            this.updateArmys(false, false);
        }
    };
    // 选择自动返回
    SelectArmyPnlCtrl.prototype.selectAutobackItem = function (node, type) {
        node.Data = this.currSelectAutoback = type;
        node.Child('val', cc.Label).setLocaleKey('ui.battle_auto_back_' + type);
        node.Child('mask/root/autoback_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
    };
    SelectArmyPnlCtrl.prototype.do = function () {
        var _a;
        if (!((_a = this.selectArmys) === null || _a === void 0 ? void 0 : _a.length)) {
            return;
        }
        this.cb && this.cb(this.selectArmys, this.sameSpeedTge_.isChecked, /* this.currSelectAutoback */ -1);
        this.hide();
    };
    // 是否可以攻占这个玩家
    SelectArmyPnlCtrl.prototype.isNotAttackPlayerByDis = function (dis) {
        var _a;
        return this.type === 'occupy' && !!((_a = this.targetCell) === null || _a === void 0 ? void 0 : _a.owner) && dis > Constant_1.OCCUPY_PLAYER_CELL_MIN_DIS;
    };
    SelectArmyPnlCtrl.prototype.getSelectArmyMap = function () {
        var obj = {};
        this.selectArmys.forEach(function (m, i) { return obj[m.uid] = i; });
        return obj;
    };
    SelectArmyPnlCtrl = __decorate([
        ccclass
    ], SelectArmyPnlCtrl);
    return SelectArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectArmyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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