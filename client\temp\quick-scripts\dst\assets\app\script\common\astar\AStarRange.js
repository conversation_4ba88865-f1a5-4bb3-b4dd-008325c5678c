
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStarRange.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a41cbDqmvBN2abnTG/bc1j3', 'AStarRange');
// app/script/common/astar/AStarRange.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var MapHelper_1 = require("../helper/MapHelper");
var AStarNode_1 = require("./AStarNode");
var AStarConfig_1 = require("./AStarConfig");
var AStep_1 = require("./AStep");
/**
 * A星寻路
 */
var AStarRange = /** @class */ (function () {
    function AStarRange() {
        this.checkHasPass = null; //检测方法
    }
    AStarRange.prototype.init = function (checkHasPass) {
        this.checkHasPass = checkHasPass;
        return this;
    };
    AStarRange.prototype.newNode = function (x, y) {
        return new AStarNode_1.default().init(x, y);
    };
    AStarRange.prototype.newStep = function (node, parent) {
        return new AStep_1.default().init(node, parent);
    };
    // 寻路
    // 一次移动一次搜索
    AStarRange.prototype.search = function (start, end, dir, moveRange, camp, maxSearchCount) {
        var _a;
        var _this = this;
        if (start.equals(end)) {
            return [];
        }
        var dirPoints = AStarConfig_1.DIR_POINTS_4_TO_SEARCH[dir] || AStarConfig_1.DIR_POINTS_4_TO_SEARCH[2];
        var dirCount = dirPoints.length;
        var begin = this.newStep(this.newNode(start.x, start.y), null);
        var steps = [begin];
        var stepMap = (_a = {}, _a[begin.ID()] = begin, _a);
        var closed = {};
        var maxG = Math.ceil(MapHelper_1.mapHelper.getPointToPointDis(start, end) / moveRange);
        var minG = 100, minStep = null;
        var count = 0;
        var _loop_1 = function () {
            var _a;
            begin = steps.shift();
            delete stepMap[begin.ID()];
            if (begin.G > maxSearchCount) {
                return "continue";
            }
            else if (begin.G > minG) {
                return "continue";
            }
            else if (begin.last.equals(end)) {
                if (!minStep || (begin.G < minStep.G || (begin.G === minStep.G && begin.equals(minStep) > 0))) {
                    minStep = begin;
                    minG = begin.G;
                    // 这里可以看是否是最短 并且都是走满了的 就直接结束搜索
                    if (minG <= maxG && begin.isPerfectPath(moveRange)) {
                        return "break";
                    }
                }
                return "continue";
            }
            var node = this_1.newNode(begin.last.x, begin.last.y);
            var opened = [node];
            var openedMap = (_a = {}, _a[node.ID()] = node, _a);
            var stepGMap = {};
            steps.forEach(function (m) { return _this.addStepGMap(m, stepGMap); });
            // 找出周围可移动到的路径点
            while (opened.length > 0) {
                node = opened.shift();
                delete openedMap[node.ID()];
                if (node.point.equals(end)) {
                    break;
                }
                else if (node.G >= moveRange) {
                    continue;
                }
                closed[node.uid] = { G: begin.G, F: begin.F, M: begin.M, NG: node.G };
                // 找周围的是否可以移动
                for (var i = 0; i < dirCount; i++) {
                    count += 1;
                    var d = dirPoints[i];
                    var x = node.x + d.point.x;
                    var y = node.y + d.point.y;
                    var state = this_1.checkHasPass(x, y, camp);
                    if (!state) {
                        continue; //不可通行
                    }
                    // 是否找过了
                    var id = x + '_' + y;
                    var info = closed[id];
                    if (!info) {
                    }
                    else if ((node.G + 1 < moveRange || state === 1) && this_1.equalsStep(begin, info, node.G)) {
                        // 如果当前不是找最后一个点 或者找个位置没有人
                        // 如果当前次数小于那么就换成找个
                        info.G = begin.G;
                        info.F = begin.F;
                    }
                    else {
                        continue;
                    }
                    var step_1 = stepGMap[id];
                    if (step_1 && begin.G + 1 > step_1.G) {
                        continue; //如果有更短的就放弃这个
                    }
                    // 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
                    var it = openedMap[id];
                    if (!it) {
                        it = this_1.newNode(x, y);
                        it.H = MapHelper_1.mapHelper.getPointToPointDis(end, it.point);
                        it.updateParent(node, d.tag, state === 2 ? 1 : 0);
                        opened.push(it);
                        openedMap[it.uid] = it;
                    }
                    else if (node.G + node.T + d.tag < it.G + it.T) {
                        it.updateParent(node, d.tag, state === 2 ? 1 : 0);
                    }
                    if (it.G >= moveRange || it.point.equals(end)) {
                        var n = it;
                        if (state === 2) { //如果这个位置有人 就继续上看直到没有人为止
                            n = it.parent;
                            while (!!n && n.P > 0) {
                                delete closed[n.ID()];
                                n = n.parent;
                            }
                            if (!!n && n.point.equals(begin.last)) {
                                n = null;
                            }
                        }
                        if (!!n) {
                            var s = stepMap[n.ID()];
                            if (!s || begin.G + 1 < s.G) {
                                if (s) {
                                    s.init(n, begin);
                                }
                                else {
                                    s = this_1.newStep(n, begin);
                                    steps.push(s);
                                }
                                s.F += moveRange - s.points.length + 1;
                                stepMap[s.ID()] = s;
                                this_1.addStepGMap(s, stepGMap);
                            }
                        }
                    }
                }
                // 排序
                opened.sort(function (a, b) { return a.F - b.F; });
            }
            steps.sort(function (a, b) { return _this.getStepWeight(b, end) - _this.getStepWeight(a, end); });
        };
        var this_1 = this;
        while (steps.length > 0) {
            var state_1 = _loop_1();
            if (state_1 === "break")
                break;
        }
        //
        var step = minStep || begin;
        var points = [];
        while (step.parent) {
            points.push(step.points);
            step = step.parent;
        }
        points.reverse();
        // cc.log('---------------------------需要' + points.length + '次走完------------------------------')
        // points.forEach(arr => cc.log(arr.map(m => m.ID())))
        // cc.log('复杂度=' + count)
        return points;
    };
    AStarRange.prototype.equalsStep = function (step, info, ng) {
        if (step.G !== info.G) {
            return step.G < info.G;
        }
        var ag = step.F + ng, bg = info.F + info.NG;
        if (ag !== bg) {
            return ag < bg;
        }
        return step.M > info.M;
    };
    AStarRange.prototype.addStepGMap = function (step, out) {
        for (var i = 1, l = step.points.length; i < l; i++) {
            var key = step.points[i].ID();
            var st = out[key];
            if (!st || step.G < st.G) {
                out[key] = step;
            }
        }
    };
    AStarRange.prototype.getStepWeight = function (step, end) {
        var aw = step.last.equals(end) ? 2 : 1;
        aw = aw * 99 + (99 - step.G);
        aw = aw * 100 + (99 - step.F);
        // aw = aw * 10 + (9 - step.points.length)
        aw = aw * 10 + step.points.length;
        return aw;
    };
    return AStarRange;
}());
exports.default = AStarRange;
if (cc.sys.isBrowser) {
    window['astarTest'] = function () {
        var maps = [
            // [0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0], // 10
            // [0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1], // 9
            // [0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0], // 8
            // [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], // 7
            // [0, 0, 0, 1, 1, 2, 1, 1, 1, 0, 0], // 6
            // [1, 0, 2, 1, 0, 0, 0, 2, 1, 1, 1], // 5
            // [0, 1, 0, 2, 0, 2, 1, 0, 1, 1, 0], // 4
            // [0, 1, 0, 2, 0, 0, 0, 0, 0, 1, 1], // 3
            // [0, 0, 1, 0, 2, 2, 0, 0, 0, 0, 0], // 2
            // [1, 0, 0, 1, 0, 0, 2, 0, 0, 0, 0], // 1
            // [0, 1, 0, 1, 0, 1, 0, 2, 0, 0, 0], // 0
            // [0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0], // 10
            // [0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1], // 9
            // [0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0], // 8
            // [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], // 7
            // [0, 0, 0, 1, 1, 2, 1, 1, 1, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 2, 1, 1, 1], // 5
            // [0, 0, 0, 0, 0, 2, 1, 0, 1, 1, 0], // 4
            // [0, 0, 0, 0, 2, 0, 0, 0, 0, 1, 1], // 3
            // [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], // 2
            // [0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], // 0
            // [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], // 10
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 9
            // [0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], // 8
            // [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], // 7
            // [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], // 5
            // [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], // 4
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 3
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 2
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 0
            // [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1], // 10
            // [0, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0], // 9
            // [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], // 8
            // [0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 0], // 7
            // [0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], // 5
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 4
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 3
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 2
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 0
            // [0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0], // 10
            // [0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1], // 9
            // [0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 0], // 8
            // [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0], // 7
            // [0, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 2, 1, 1, 1], // 5
            // [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0], // 4
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], // 3
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], // 2
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 0
            // [0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0], // 10
            // [0, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1], // 9
            // [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0], // 8
            // [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0], // 7
            // [0, 0, 0, 1, 1, 2, 1, 1, 1, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 2, 1, 1, 1], // 5
            // [0, 0, 0, 0, 0, 2, 1, 0, 1, 1, 0], // 4
            // [0, 0, 0, 0, 2, 0, 0, 0, 0, 1, 1], // 3
            // [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], // 2
            // [0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0], // 0
            // [0, 0, 0, 0, 1, 0, 0, 1, 1, 1, 0], // 10
            // [1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1], // 9
            // [0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0], // 8
            // [1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0], // 7
            // [0, 1, 0, 1, 1, 0, 1, 1, 1, 0, 0], // 6
            // [1, 2, 1, 0, 0, 2, 0, 2, 1, 1, 1], // 5
            // [0, 0, 1, 0, 0, 0, 0, 0, 1, 1, 0], // 4
            // [1, 1, 0, 0, 0, 0, 2, 0, 0, 1, 1], // 3
            // [1, 1, 2, 2, 2, 2, 0, 1, 1, 0, 1], // 2
            // [1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0], // 1
            // [0, 2, 0, 2, 0, 2, 0, 0, 0, 0, 0], // 0
            // [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], // 10
            // [0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0], // 9
            // [0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0], // 8
            // [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0], // 7
            // [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], // 6
            // [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0], // 5
            // [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0], // 4
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 3
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 2
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 1
            // [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 0
            [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 2, 2, 2, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ];
        //   0  1  2  3  4  5  6  7  8  9  10
        function checkHasPass(x, y, camp) {
            y = 10 - y;
            if (x < 0 || x >= maps[0].length || y < 0 || y >= maps.length) {
                return 0;
            }
            else if (!camp) {
                return !maps[y][x] ? 1 : 0;
            }
            else if (maps[y][x] === 0) {
                return 1;
            }
            return maps[y][x] === camp ? 2 : 0;
        }
        var astar = new AStarRange().init(checkHasPass);
        console.time("AStarRange");
        for (var index = 0; index < 1; index++) {
            // astar.search(cc.v2(1, 8), cc.v2(4, 3), 0, 2, 1)
            // astar.search(cc.v2(8, 10), cc.v2(9, 6), 0, 2, 1)
            // astar.search(cc.v2(5, 10), cc.v2(7, 6), 0, 3, 1)
            // astar.search(cc.v2(1, 8), cc.v2(6, 5), 0, 2, 1)
            // astar.search(cc.v2(9, 9), cc.v2(0, 0), 0, 2, 1)
            // astar.search(cc.v2(5, 10), cc.v2(7, 6), 0, 3, 1)
            astar.search(cc.v2(5, 10), cc.v2(6, 7), 0, 3, 1, 1000);
        }
        console.timeEnd("AStarRange");
    };
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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