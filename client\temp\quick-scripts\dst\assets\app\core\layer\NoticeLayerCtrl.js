
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/layer/NoticeLayerCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '652bcl+oexCdosCmelbJxbB', 'NoticeLayerCtrl');
// app/core/layer/NoticeLayerCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLayerCtrl_1 = require("../base/BaseLayerCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeLayerCtrl = /** @class */ (function (_super) {
    __extends(NoticeLayerCtrl, _super);
    function NoticeLayerCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ctrlMgr = null;
        return _this;
    }
    NoticeLayerCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[CoreEventType_1.default.LOAD_ALL_NOTICE] = this.onLoadAllNotice, _a),
            (_b = {}, _b[CoreEventType_1.default.LOAD_NOTICE] = this.onLoadNotice, _b),
        ];
    };
    NoticeLayerCtrl.prototype.onCreate = function () {
        this.node.group = 'ui';
    };
    NoticeLayerCtrl.prototype.onClean = function () {
    };
    NoticeLayerCtrl.prototype.setCtrlMgr = function (mgr) {
        this.ctrlMgr = mgr;
        this.ctrlMgr.node = this.node;
    };
    NoticeLayerCtrl.prototype.onLoadAllNotice = function (complete, progress) {
        this.ctrlMgr.loadAll('view/notice', complete, progress);
    };
    NoticeLayerCtrl.prototype.onLoadNotice = function (val, complete) {
        this.ctrlMgr.loadNot('view/notice', val, complete);
    };
    NoticeLayerCtrl = __decorate([
        ccclass
    ], NoticeLayerCtrl);
    return NoticeLayerCtrl;
}(BaseLayerCtrl_1.default));
exports.default = NoticeLayerCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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