
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BattleEndPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6128fX+7cZEM4CSpFbuAl7D', 'BattleEndPnlCtrl');
// app/script/view/area/BattleEndPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BattleEndPnlCtrl = /** @class */ (function (_super) {
    __extends(BattleEndPnlCtrl, _super);
    function BattleEndPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.resNode_ = null; // path://content/res_n
        _this.treasureNode_ = null; // path://content/treasure_n
        _this.notStaminaNode_ = null; // path://content/not_stamina_n
        _this.flyNode_ = null; // path://fly_n
        //@end
        _this.isClickClose = false;
        return _this;
    }
    BattleEndPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BattleEndPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BattleEndPnlCtrl.prototype.onEnter = function (res, treasures, noTreasureByNotStamina, fullLostCount) {
        var _this = this;
        this.isClickClose = false;
        audioMgr.playSFX('common/sound_ui_001');
        this.Child('title').active = true;
        this.resNode_.active = true;
        this.resNode_.Items(res, function (it, data) {
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(data.type);
            it.Child('val', cc.Label).setLocaleKey('ui.res_desc', data.val);
        });
        if (this.treasureNode_.active = !!(treasures === null || treasures === void 0 ? void 0 : treasures.length)) {
            this.treasureNode_.Items(treasures, function (it, data) {
                it.Data = data;
                var json = assetsMgr.getJsonData('treasure', data.id);
                ResHelper_1.resHelper.loadIcon('icon/treasure_' + ((json === null || json === void 0 ? void 0 : json.lv) || 1) + '_0', it.Child('icon'), _this.key);
            });
        }
        var noStamina = !!noTreasureByNotStamina, fullLost = !!fullLostCount;
        if (this.notStaminaNode_.active = noStamina || fullLost) {
            var key = noStamina ? 'ui.not_stamina_tip_2' : fullLost ? 'ui.treasure_bag_not_enough' : '', params = fullLost ? [fullLostCount] : [];
            this.notStaminaNode_.Child('val').setLocaleKey(key, params);
        }
        this.flyNode_.Swih('');
        this.emit(mc.Event.HIDE_ALL_PNL, 'area/AreaArmy|common/TreasureList|area/EditArmyName');
    };
    BattleEndPnlCtrl.prototype.onRemove = function () {
        this.isClickClose = false;
    };
    BattleEndPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be
    BattleEndPnlCtrl.prototype.onClickClose = function (event, data) {
        var _this = this;
        var _a;
        if (this.isClickClose) {
            return;
        }
        this.isClickClose = true;
        if (this.notStaminaNode_.active) {
            return this.hide();
        }
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'common/UI'; });
        if (!ui) {
            return this.hide();
        }
        var treasureMap = {};
        (_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) { return m.getAllPawnTreasures().forEach(function (t) {
            treasureMap[t.uid] = true;
        }); });
        var a = [], b = [];
        this.treasureNode_.children.forEach(function (m) {
            var _a;
            if (treasureMap[(_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid]) {
                a.push(_this.addItem(m));
            }
            else {
                b.push(_this.addItem(m));
            }
        });
        // 飞里面
        if (a.length > 0 || b.length > 0) {
            this.hideAll();
            var nodeA = ui.FindChild('scene_n/area/bottom/area_army_be_n'), posA = ut.convertToNodeAR(nodeA, this.flyNode_).clone();
            var nodeB = ui.FindChild('scene_n/area/back_main_be'), posB = ut.convertToNodeAR(nodeB, this.flyNode_).clone();
            this.playFly(a, posA);
            this.playFly(b, posB);
            ut.wait(0.7, this).then(function () { return _this.isValid && _this.hide(); });
        }
        else {
            this.hide();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    BattleEndPnlCtrl.prototype.addItem = function (node) {
        var it = this.flyNode_.children.find(function (m) { return !m.active; }) || cc.instantiate2(this.flyNode_.children[0], this.flyNode_);
        it.active = true;
        it.Component(cc.Sprite).spriteFrame = node.Child('icon', cc.Sprite).spriteFrame;
        it.setPosition(ut.convertToNodeAR(node.Child('icon'), this.flyNode_));
        return it;
    };
    BattleEndPnlCtrl.prototype.hideAll = function () {
        this.showMask(false);
        this.Child('title').active = false;
        this.resNode_.active = false;
        this.treasureNode_.active = false;
        this.notStaminaNode_.active = false;
    };
    BattleEndPnlCtrl.prototype.playFly = function (arr, pos) {
        if (arr.length === 0) {
            return;
        }
        arr.forEach(function (m) {
            m.scale = 1;
            cc.tween(m)
                .to(0.5, { x: pos.x, y: pos.y }, { easing: cc.easing.sineIn })
                .to(0.2, { scale: 1.3, opacity: 0 })
                .hide()
                .start();
        });
    };
    BattleEndPnlCtrl = __decorate([
        ccclass
    ], BattleEndPnlCtrl);
    return BattleEndPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BattleEndPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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