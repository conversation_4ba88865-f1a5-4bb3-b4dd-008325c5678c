
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/bazaar/BazaarConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0851jURCFJaKNH4eYzRWCi', 'BazaarConfig');
// app/script/model/bazaar/BazaarConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BAZAAR_RECORD_INFO_CONF = void 0;
// 市场记录信息配置
var BAZAAR_RECORD_INFO_CONF = {
    1: {
        text: ['name', 'res0'],
    },
    2: {
        text: ['name', 'res0'],
        end: { key: 'ui.bazaar_record_info_sjhd', res: true, checkCount: true },
    },
    3: {
        text: ['res0'],
    },
    4: {
        text: ['res0'],
        end: { key: 'ui.bazaar_record_info_yth', res: true },
    },
    5: {
        text: ['res0'],
        end: { key: 'ui.bazaar_record_info_yth', res: true },
    },
    60: {
        text: ['name', 'res0', 'res1'],
    },
    61: {
        text: ['name', 'res0', 'res1'],
    },
    7: {
        text: ['name', 'res0'],
        end: { key: 'ui.bazaar_record_info_sjhd', res: true, checkCount: true },
    },
    8: {
        text: ['name', 'res0'],
        end: { key: 'ui.bazaar_record_info_sjhd', res: true, checkCount: true },
    },
    9: {
        text: ['res0', 'res1'],
    },
};
exports.BAZAAR_RECORD_INFO_CONF = BAZAAR_RECORD_INFO_CONF;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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