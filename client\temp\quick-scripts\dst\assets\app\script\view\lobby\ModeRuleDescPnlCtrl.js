
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/ModeRuleDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4c948+umOlDxJGaoIW0UwFB', 'ModeRuleDescPnlCtrl');
// app/script/view/lobby/ModeRuleDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MODE_RULE_DESC = [
    [
        'ui.server_rule_desc_1_1',
        'ui.server_rule_desc_2_1',
        'ui.server_rule_desc_3_1',
        'ui.server_rule_desc_4_1',
        'wartoken',
        'ui.server_rule_desc_5_0',
    ],
    [
        'ui.server_rule_desc_1_0',
        'ui.server_rule_desc_2_1',
        'ui.server_rule_desc_3_0',
        'ui.server_rule_desc_4_1',
        'wartoken',
        'ui.server_rule_desc_5_0',
    ],
    [
        'ui.server_rule_desc_1_0',
        'ui.server_rule_desc_2_0',
        'ui.server_rule_desc_3_0',
        'ui.server_rule_desc_4_1',
        'wartoken',
        'ui.server_rule_desc_5_1',
    ],
];
var ModeRuleDescPnlCtrl = /** @class */ (function (_super) {
    __extends(ModeRuleDescPnlCtrl, _super);
    function ModeRuleDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.contentNode_ = null; // path://root/content_n
        _this.listNode_ = null; // path://root/content_n/list_n
        _this.historyTitleLbl_ = null; // path://root/content_n/history/title/history_title_l
        //@end
        _this.curMode = 0;
        return _this;
    }
    ModeRuleDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ModeRuleDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ModeRuleDescPnlCtrl.prototype.onEnter = function (mode) {
        this.curMode = mode;
        this.titleLbl_.setLocaleKey('ui.server_rule_title_' + mode);
        this.listNode_.Items(MODE_RULE_DESC[mode] || MODE_RULE_DESC[0], function (it, text, i) {
            var isWartoken = text === 'wartoken';
            var desc = it.Swih(isWartoken ? 'wartoken' : 'desc')[0];
            if (!isWartoken) {
                var no = Math.min(i + 1, 5);
                desc.setLocaleKey('ui.no_desc', no, text);
            }
        });
        this.showGameHistoryList();
    };
    ModeRuleDescPnlCtrl.prototype.onRemove = function () {
    };
    ModeRuleDescPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/history/list/view/content/item/detail_be
    ModeRuleDescPnlCtrl.prototype.onClickDetail = function (event, _data) {
        var data = event.target.parent.Data;
        ViewHelper_1.viewHelper.showPnl('lobby/GameDetail', data);
    };
    // path://root/content_n/more_history_be
    ModeRuleDescPnlCtrl.prototype.onClickMoreHistory = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('lobby/GameHistory', this.curMode);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 历史对局
    ModeRuleDescPnlCtrl.prototype.showGameHistoryList = function () {
        var _this = this;
        var serverType = this.curMode;
        this.historyTitleLbl_.setLocaleKey('ui.title_game_history', [assetsMgr.lang('ui.title_server_name_' + serverType)]);
        var node = this.contentNode_.Child('history/list'), empty = node.Child('empty');
        empty.active = false;
        var content = node.Child('content');
        content.active = false;
        GameHelper_1.gameHpr.net.request('chat/HD_GetGameHistoryList', { page: 1, serverType: serverType }, true).then(function (_a) {
            var err = _a.err, data = _a.data;
            var list = (data === null || data === void 0 ? void 0 : data.datas) || [], len = list.length;
            empty.active = !len;
            content.active = !!len;
            content.Items(list.splice(0, 3), function (it, info, i) {
                ViewHelper_1.viewHelper.updateGameHistoryItem(it, info, _this.key);
                it.Child('line').active = i < 2;
            });
        });
    };
    ModeRuleDescPnlCtrl = __decorate([
        ccclass
    ], ModeRuleDescPnlCtrl);
    return ModeRuleDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ModeRuleDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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