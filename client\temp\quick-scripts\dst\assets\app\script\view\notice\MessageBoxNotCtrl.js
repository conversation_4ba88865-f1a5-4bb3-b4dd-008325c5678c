
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/MessageBoxNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f4343vSIL9O4LtHOx09eOb0', 'MessageBoxNotCtrl');
// app/script/view/notice/MessageBoxNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NotEvent_1 = require("../../common/event/NotEvent");
var CoreEventType_1 = require("../../../core/event/CoreEventType");
var ccclass = cc._decorator.ccclass;
var MessageBoxNotCtrl = /** @class */ (function (_super) {
    __extends(MessageBoxNotCtrl, _super);
    function MessageBoxNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        _this.contentRt_ = null; // path://root_n/content_rt
        _this.buttonsNode_ = null; // path://root_n/buttons_nbe_n
        //@end
        _this.okCb = null;
        _this.cancelCb = null;
        _this.clickButtonClose = true; //点击按钮是否关闭界面
        return _this;
    }
    MessageBoxNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[NotEvent_1.default.OPEN_MESSAGE_BOX] = this.onEventOpen, _a),
            (_b = {}, _b[NotEvent_1.default.HIDE_MESSAGE_BOX] = this.onEventHide, _b),
        ];
    };
    MessageBoxNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/buttons_nbe_n
    MessageBoxNotCtrl.prototype.onClickButtons = function (event, data) {
        if (this.clickButtonClose) {
            this.hide();
        }
        var name = event.target.name;
        if (name === 'ok') {
            this.okCb && this.okCb();
        }
        else if (name === 'cancel') {
            this.cancelCb && this.cancelCb();
        }
        this.onHide();
    };
    // path://close_be_n
    MessageBoxNotCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
        this.onHide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    MessageBoxNotCtrl.prototype.onEventOpen = function (msg, opts) {
        this.open();
        this.show(msg, opts);
    };
    MessageBoxNotCtrl.prototype.onEventHide = function () {
        this.hide();
        this.onHide();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    MessageBoxNotCtrl.prototype.show = function (msg, opts) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var isMask;
            var _b;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        mc.lockTouch('__open_message_box__');
                        opts = opts || {};
                        this.titleLbl_.setLocaleKey(opts.title || 'login.title_tip');
                        (_b = this.contentRt_).setLocaleKey.apply(_b, __spread([msg], (opts.params || [])));
                        this.okCb = opts.ok;
                        this.cancelCb = opts.cancel;
                        // 是否显示取消按钮
                        this.buttonsNode_.Child('cancel').active = !!this.cancelCb;
                        // 设置按钮名字
                        this.buttonsNode_.Child('ok/val', cc.Label).setLocaleKey(opts.okText || 'login.button_ok');
                        this.buttonsNode_.Child('cancel/val', cc.Label).setLocaleKey(opts.cancelText || 'login.button_cancel');
                        // 做动画
                        this.rootNode_.stopAllActions();
                        this.rootNode_.height = Math.max(360, this.contentRt_.node.height + 200);
                        this.rootNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
                        isMask = this.closeNode_.active = (opts === null || opts === void 0 ? void 0 : opts.mask) === false ? false : true;
                        this.playShowAction(isMask, !(opts === null || opts === void 0 ? void 0 : opts.lockClose));
                        // 是否开启点击按钮就关闭 默认开启
                        this.clickButtonClose = (_a = opts === null || opts === void 0 ? void 0 : opts.clickButtonClose) !== null && _a !== void 0 ? _a : true;
                        return [4 /*yield*/, ut.wait(0.25)];
                    case 1:
                        _c.sent();
                        mc.unlockTouch('__open_message_box__');
                        eventCenter.emit(CoreEventType_1.default.NOTICE_ENTER_PLAY_DONE);
                        return [2 /*return*/];
                }
            });
        });
    };
    MessageBoxNotCtrl.prototype.playShowAction = function (isMask, lockClose) {
        var widget = this.Component(cc.Widget);
        if (widget && widget.enabled) {
            widget.updateAlignment();
            widget.enabled = false;
        }
        this.rootNode_.stopAllActions();
        this.rootNode_.scale = 0.4;
        cc.tween(this.rootNode_).to(0.25, { scale: this.initScale }, { easing: cc.easing.backOut }).start();
        if (isMask) {
            // 禁止点击空白关闭
            this.closeNode_.Component(cc.Button).interactable = lockClose;
            // 做动画
            this.closeNode_.stopAllActions();
            this.closeNode_.opacity = 0;
            cc.tween(this.closeNode_).to(0.3, { opacity: 120 }, { easing: cc.easing.sineOut }).start();
        }
    };
    MessageBoxNotCtrl.prototype.onHide = function () {
        this.okCb = null;
        this.cancelCb = null;
    };
    __decorate([
        ut.syncLock
    ], MessageBoxNotCtrl.prototype, "show", null);
    MessageBoxNotCtrl = __decorate([
        ccclass
    ], MessageBoxNotCtrl);
    return MessageBoxNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = MessageBoxNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG5vdGljZVxcTWVzc2FnZUJveE5vdEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx3REFBbUQ7QUFFbkQsbUVBQThEO0FBRXRELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQStDLHFDQUFpQjtJQUFoRTtRQUFBLHFFQStHQztRQTdHQSwwQkFBMEI7UUFDbEIsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7UUFDL0MsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLGdCQUFnQjtRQUMxQyxlQUFTLEdBQWEsSUFBSSxDQUFBLENBQUMsOEJBQThCO1FBQ3pELGdCQUFVLEdBQWdCLElBQUksQ0FBQSxDQUFDLDJCQUEyQjtRQUMxRCxrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLDhCQUE4QjtRQUNuRSxNQUFNO1FBRUUsVUFBSSxHQUFhLElBQUksQ0FBQTtRQUNyQixjQUFRLEdBQWEsSUFBSSxDQUFBO1FBRXpCLHNCQUFnQixHQUFZLElBQUksQ0FBQSxDQUFDLFlBQVk7O0lBa0d0RCxDQUFDO0lBaEdPLDJDQUFlLEdBQXRCOztRQUNDLE9BQU87c0JBQ0osR0FBQyxrQkFBUSxDQUFDLGdCQUFnQixJQUFHLElBQUksQ0FBQyxXQUFXO3NCQUM3QyxHQUFDLGtCQUFRLENBQUMsZ0JBQWdCLElBQUcsSUFBSSxDQUFDLFdBQVc7U0FDL0MsQ0FBQTtJQUNGLENBQUM7SUFFWSxvQ0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDhCQUE4QjtJQUM5QiwwQ0FBYyxHQUFkLFVBQWUsS0FBMEIsRUFBRSxJQUFZO1FBQ3RELElBQUksSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQzFCLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtTQUNYO1FBQ0QsSUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUE7UUFDOUIsSUFBSSxJQUFJLEtBQUssSUFBSSxFQUFFO1lBQ2xCLElBQUksQ0FBQyxJQUFJLElBQUksSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1NBQ3hCO2FBQU0sSUFBSSxJQUFJLEtBQUssUUFBUSxFQUFFO1lBQzdCLElBQUksQ0FBQyxRQUFRLElBQUksSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFBO1NBQ2hDO1FBQ0QsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFBO0lBQ2QsQ0FBQztJQUVELG9CQUFvQjtJQUNwQix3Q0FBWSxHQUFaLFVBQWEsS0FBMEIsRUFBRSxJQUFZO1FBQ3BELElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtRQUNYLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQTtJQUNkLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRXpHLHVDQUFXLEdBQW5CLFVBQW9CLEdBQVcsRUFBRSxJQUFxQjtRQUNyRCxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsQ0FBQTtJQUNyQixDQUFDO0lBRU8sdUNBQVcsR0FBbkI7UUFDQyxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDWCxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUE7SUFDZCxDQUFDO0lBQ0QsaUhBQWlIO0lBR25HLGdDQUFJLEdBQWxCLFVBQW1CLEdBQVcsRUFBRSxJQUFxQjs7Ozs7Ozs7d0JBQ3BELEVBQUUsQ0FBQyxTQUFTLENBQUMsc0JBQXNCLENBQUMsQ0FBQTt3QkFDcEMsSUFBSSxHQUFHLElBQUksSUFBSSxFQUFFLENBQUE7d0JBQ2pCLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksaUJBQWlCLENBQUMsQ0FBQTt3QkFDNUQsQ0FBQSxLQUFBLElBQUksQ0FBQyxVQUFVLENBQUEsQ0FBQyxZQUFZLHFCQUFDLEdBQUcsR0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksRUFBRSxDQUFDLEdBQUM7d0JBQ3pELElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQTt3QkFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFBO3dCQUMzQixXQUFXO3dCQUNYLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQTt3QkFDMUQsU0FBUzt3QkFDVCxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLGlCQUFpQixDQUFDLENBQUE7d0JBQzFGLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxVQUFVLElBQUkscUJBQXFCLENBQUMsQ0FBQTt3QkFDdEcsTUFBTTt3QkFDTixJQUFJLENBQUMsU0FBUyxDQUFDLGNBQWMsRUFBRSxDQUFBO3dCQUMvQixJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFDLENBQUE7d0JBQ3hFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMseUJBQUksQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLDBDQUFFLGVBQWUsS0FBRSxDQUFDLENBQUE7d0JBRXpFLE1BQU0sR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxJQUFJLE1BQUssS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQTt3QkFDM0UsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEVBQUUsRUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsU0FBUyxDQUFBLENBQUMsQ0FBQTt3QkFDN0MsbUJBQW1CO3dCQUNuQixJQUFJLENBQUMsZ0JBQWdCLFNBQUcsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLGdCQUFnQixtQ0FBSSxJQUFJLENBQUE7d0JBQ3RELHFCQUFNLEVBQUUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUE7O3dCQUFuQixTQUFtQixDQUFBO3dCQUNuQixFQUFFLENBQUMsV0FBVyxDQUFDLHNCQUFzQixDQUFDLENBQUE7d0JBQ3RDLFdBQVcsQ0FBQyxJQUFJLENBQUMsdUJBQWEsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFBOzs7OztLQUN0RDtJQUVPLDBDQUFjLEdBQXRCLFVBQXVCLE1BQWUsRUFBRSxTQUFrQjtRQUN6RCxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUN4QyxJQUFJLE1BQU0sSUFBSSxNQUFNLENBQUMsT0FBTyxFQUFFO1lBQzdCLE1BQU0sQ0FBQyxlQUFlLEVBQUUsQ0FBQTtZQUN4QixNQUFNLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQTtTQUN0QjtRQUNELElBQUksQ0FBQyxTQUFTLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDL0IsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEdBQUcsR0FBRyxDQUFBO1FBQzFCLEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLFNBQVMsRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUNuRyxJQUFJLE1BQU0sRUFBRTtZQUNYLFdBQVc7WUFDWCxJQUFJLENBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxHQUFHLFNBQVMsQ0FBQTtZQUM3RCxNQUFNO1lBQ04sSUFBSSxDQUFDLFVBQVUsQ0FBQyxjQUFjLEVBQUUsQ0FBQTtZQUNoQyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUE7WUFDM0IsRUFBRSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLE9BQU8sRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLE1BQU0sRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUE7U0FDMUY7SUFDRixDQUFDO0lBRU8sa0NBQU0sR0FBZDtRQUNDLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2hCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFBO0lBQ3JCLENBQUM7SUFoREQ7UUFEQyxFQUFFLENBQUMsUUFBUTtpREF5Qlg7SUF0Rm1CLGlCQUFpQjtRQURyQyxPQUFPO09BQ2EsaUJBQWlCLENBK0dyQztJQUFELHdCQUFDO0NBL0dELEFBK0dDLENBL0c4QyxFQUFFLENBQUMsY0FBYyxHQStHL0Q7a0JBL0dvQixpQkFBaUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTm90RXZlbnQgZnJvbSBcIi4uLy4uL2NvbW1vbi9ldmVudC9Ob3RFdmVudFwiO1xuaW1wb3J0IHsgTWVzc2FnZUJveE9wdHMgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCI7XG5pbXBvcnQgQ29yZUV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vLi4vY29yZS9ldmVudC9Db3JlRXZlbnRUeXBlXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE1lc3NhZ2VCb3hOb3RDdHJsIGV4dGVuZHMgbWMuQmFzZU5vdGljZUN0cmwge1xuXG5cdC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG5cdHByaXZhdGUgY2xvc2VOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL2Nsb3NlX2JlX25cblx0cHJpdmF0ZSByb290Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X25cblx0cHJpdmF0ZSB0aXRsZUxibF86IGNjLkxhYmVsID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL3RpdGxlL3RpdGxlX2xcblx0cHJpdmF0ZSBjb250ZW50UnRfOiBjYy5SaWNoVGV4dCA9IG51bGwgLy8gcGF0aDovL3Jvb3Rfbi9jb250ZW50X3J0XG5cdHByaXZhdGUgYnV0dG9uc05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL2J1dHRvbnNfbmJlX25cblx0Ly9AZW5kXG5cblx0cHJpdmF0ZSBva0NiOiBGdW5jdGlvbiA9IG51bGxcblx0cHJpdmF0ZSBjYW5jZWxDYjogRnVuY3Rpb24gPSBudWxsXG5cblx0cHJpdmF0ZSBjbGlja0J1dHRvbkNsb3NlOiBib29sZWFuID0gdHJ1ZSAvL+eCueWHu+aMiemSruaYr+WQpuWFs+mXreeVjOmdolxuXG5cdHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG5cdFx0cmV0dXJuIFtcblx0XHRcdHsgW05vdEV2ZW50Lk9QRU5fTUVTU0FHRV9CT1hdOiB0aGlzLm9uRXZlbnRPcGVuIH0sXG5cdFx0XHR7IFtOb3RFdmVudC5ISURFX01FU1NBR0VfQk9YXTogdGhpcy5vbkV2ZW50SGlkZSB9LFxuXHRcdF1cblx0fVxuXG5cdHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcblx0fVxuXG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cdC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG5cdC8vIHBhdGg6Ly9yb290X24vYnV0dG9uc19uYmVfblxuXHRvbkNsaWNrQnV0dG9ucyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG5cdFx0aWYgKHRoaXMuY2xpY2tCdXR0b25DbG9zZSkge1xuXHRcdFx0dGhpcy5oaWRlKClcblx0XHR9XG5cdFx0Y29uc3QgbmFtZSA9IGV2ZW50LnRhcmdldC5uYW1lXG5cdFx0aWYgKG5hbWUgPT09ICdvaycpIHtcblx0XHRcdHRoaXMub2tDYiAmJiB0aGlzLm9rQ2IoKVxuXHRcdH0gZWxzZSBpZiAobmFtZSA9PT0gJ2NhbmNlbCcpIHtcblx0XHRcdHRoaXMuY2FuY2VsQ2IgJiYgdGhpcy5jYW5jZWxDYigpXG5cdFx0fVxuXHRcdHRoaXMub25IaWRlKClcblx0fVxuXG5cdC8vIHBhdGg6Ly9jbG9zZV9iZV9uXG5cdG9uQ2xpY2tDbG9zZShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG5cdFx0dGhpcy5oaWRlKClcblx0XHR0aGlzLm9uSGlkZSgpXG5cdH1cblx0Ly9AZW5kXG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cblx0cHJpdmF0ZSBvbkV2ZW50T3Blbihtc2c6IHN0cmluZywgb3B0cz86IE1lc3NhZ2VCb3hPcHRzKSB7XG5cdFx0dGhpcy5vcGVuKClcblx0XHR0aGlzLnNob3cobXNnLCBvcHRzKVxuXHR9XG5cblx0cHJpdmF0ZSBvbkV2ZW50SGlkZSgpIHtcblx0XHR0aGlzLmhpZGUoKVxuXHRcdHRoaXMub25IaWRlKClcblx0fVxuXHQvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG5cdEB1dC5zeW5jTG9ja1xuXHRwcml2YXRlIGFzeW5jIHNob3cobXNnOiBzdHJpbmcsIG9wdHM/OiBNZXNzYWdlQm94T3B0cykge1xuXHRcdG1jLmxvY2tUb3VjaCgnX19vcGVuX21lc3NhZ2VfYm94X18nKVxuXHRcdG9wdHMgPSBvcHRzIHx8IHt9XG5cdFx0dGhpcy50aXRsZUxibF8uc2V0TG9jYWxlS2V5KG9wdHMudGl0bGUgfHwgJ2xvZ2luLnRpdGxlX3RpcCcpXG5cdFx0dGhpcy5jb250ZW50UnRfLnNldExvY2FsZUtleShtc2csIC4uLihvcHRzLnBhcmFtcyB8fCBbXSkpXG5cdFx0dGhpcy5va0NiID0gb3B0cy5va1xuXHRcdHRoaXMuY2FuY2VsQ2IgPSBvcHRzLmNhbmNlbFxuXHRcdC8vIOaYr+WQpuaYvuekuuWPlua2iOaMiemSrlxuXHRcdHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCdjYW5jZWwnKS5hY3RpdmUgPSAhIXRoaXMuY2FuY2VsQ2Jcblx0XHQvLyDorr7nva7mjInpkq7lkI3lrZdcblx0XHR0aGlzLmJ1dHRvbnNOb2RlXy5DaGlsZCgnb2svdmFsJywgY2MuTGFiZWwpLnNldExvY2FsZUtleShvcHRzLm9rVGV4dCB8fCAnbG9naW4uYnV0dG9uX29rJylcblx0XHR0aGlzLmJ1dHRvbnNOb2RlXy5DaGlsZCgnY2FuY2VsL3ZhbCcsIGNjLkxhYmVsKS5zZXRMb2NhbGVLZXkob3B0cy5jYW5jZWxUZXh0IHx8ICdsb2dpbi5idXR0b25fY2FuY2VsJylcblx0XHQvLyDlgZrliqjnlLtcblx0XHR0aGlzLnJvb3ROb2RlXy5zdG9wQWxsQWN0aW9ucygpXG5cdFx0dGhpcy5yb290Tm9kZV8uaGVpZ2h0ID0gTWF0aC5tYXgoMzYwLCB0aGlzLmNvbnRlbnRSdF8ubm9kZS5oZWlnaHQgKyAyMDApXG5cdFx0dGhpcy5yb290Tm9kZV8uY2hpbGRyZW4uZm9yRWFjaChtID0+IG0uQ29tcG9uZW50KGNjLldpZGdldCk/LnVwZGF0ZUFsaWdubWVudCgpKVxuXHRcdC8vIOaYr+WQpuaYvuekum1hc2sg6buY6K6k5pi+56S6XG5cdFx0Y29uc3QgaXNNYXNrID0gdGhpcy5jbG9zZU5vZGVfLmFjdGl2ZSA9IG9wdHM/Lm1hc2sgPT09IGZhbHNlID8gZmFsc2UgOiB0cnVlXG5cdFx0dGhpcy5wbGF5U2hvd0FjdGlvbihpc01hc2ssICFvcHRzPy5sb2NrQ2xvc2UpXG5cdFx0Ly8g5piv5ZCm5byA5ZCv54K55Ye75oyJ6ZKu5bCx5YWz6ZetIOm7mOiupOW8gOWQr1xuXHRcdHRoaXMuY2xpY2tCdXR0b25DbG9zZSA9IG9wdHM/LmNsaWNrQnV0dG9uQ2xvc2UgPz8gdHJ1ZVxuXHRcdGF3YWl0IHV0LndhaXQoMC4yNSlcblx0XHRtYy51bmxvY2tUb3VjaCgnX19vcGVuX21lc3NhZ2VfYm94X18nKVxuXHRcdGV2ZW50Q2VudGVyLmVtaXQoQ29yZUV2ZW50VHlwZS5OT1RJQ0VfRU5URVJfUExBWV9ET05FKVxuXHR9XG5cblx0cHJpdmF0ZSBwbGF5U2hvd0FjdGlvbihpc01hc2s6IGJvb2xlYW4sIGxvY2tDbG9zZTogYm9vbGVhbikge1xuXHRcdGNvbnN0IHdpZGdldCA9IHRoaXMuQ29tcG9uZW50KGNjLldpZGdldClcblx0XHRpZiAod2lkZ2V0ICYmIHdpZGdldC5lbmFibGVkKSB7XG5cdFx0XHR3aWRnZXQudXBkYXRlQWxpZ25tZW50KClcblx0XHRcdHdpZGdldC5lbmFibGVkID0gZmFsc2Vcblx0XHR9XG5cdFx0dGhpcy5yb290Tm9kZV8uc3RvcEFsbEFjdGlvbnMoKVxuXHRcdHRoaXMucm9vdE5vZGVfLnNjYWxlID0gMC40XG5cdFx0Y2MudHdlZW4odGhpcy5yb290Tm9kZV8pLnRvKDAuMjUsIHsgc2NhbGU6IHRoaXMuaW5pdFNjYWxlIH0sIHsgZWFzaW5nOiBjYy5lYXNpbmcuYmFja091dCB9KS5zdGFydCgpXG5cdFx0aWYgKGlzTWFzaykge1xuXHRcdFx0Ly8g56aB5q2i54K55Ye756m655m95YWz6ZetXG5cdFx0XHR0aGlzLmNsb3NlTm9kZV8uQ29tcG9uZW50KGNjLkJ1dHRvbikuaW50ZXJhY3RhYmxlID0gbG9ja0Nsb3NlXG5cdFx0XHQvLyDlgZrliqjnlLtcblx0XHRcdHRoaXMuY2xvc2VOb2RlXy5zdG9wQWxsQWN0aW9ucygpXG5cdFx0XHR0aGlzLmNsb3NlTm9kZV8ub3BhY2l0eSA9IDBcblx0XHRcdGNjLnR3ZWVuKHRoaXMuY2xvc2VOb2RlXykudG8oMC4zLCB7IG9wYWNpdHk6IDEyMCB9LCB7IGVhc2luZzogY2MuZWFzaW5nLnNpbmVPdXQgfSkuc3RhcnQoKVxuXHRcdH1cblx0fVxuXG5cdHByaXZhdGUgb25IaWRlKCkge1xuXHRcdHRoaXMub2tDYiA9IG51bGxcblx0XHR0aGlzLmNhbmNlbENiID0gbnVsbFxuXHR9XG59Il19