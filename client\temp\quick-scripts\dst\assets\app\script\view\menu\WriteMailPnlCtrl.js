
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/WriteMailPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '061305tfr5Jire17tiXX0EC', 'WriteMailPnlCtrl');
// app/script/view/menu/WriteMailPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var WriteMailPnlCtrl = /** @class */ (function (_super) {
    __extends(WriteMailPnlCtrl, _super);
    function WriteMailPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentEb_ = null; // path://root/content_eb
        _this.titleEb_ = null; // path://root/top/title/title_eb
        _this.receiverNode_ = null; // path://root/top/receiver/receiver_n
        _this.receiverEb_ = null; // path://root/top/receiver/receiver_n/receiver_eb
        _this.targetTc_ = null; // path://root/top/target_tc_tce
        //@end
        _this.receiverType = 0;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    WriteMailPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    WriteMailPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    WriteMailPnlCtrl.prototype.onEnter = function (data) {
        var alli = GameHelper_1.gameHpr.alliance;
        this.targetTc_.Child(2).active = (alli.isMeCreater() || alli.isMeCreaterVice()) && alli.getMembers().length > 1;
        if (data) {
            this.titleEb_.string = '';
            this.contentEb_.string = '';
            // this.targetTc_.Tabs(data.sender === '-1' ? 0 : 1)
            this.targetTc_.Tabs(1);
            if (data.sender !== '-1' && data.senderName) {
                this.receiverEb_.string = data.senderName;
            }
            else {
                this.receiverEb_.string = '';
            }
        }
        else {
            this.targetTc_.Tabs(1);
        }
    };
    WriteMailPnlCtrl.prototype.onRemove = function () {
    };
    WriteMailPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/send_be
    WriteMailPnlCtrl.prototype.onClickSend = function (event, data) {
        var _this = this;
        var title = this.titleEb_.string.trim();
        var content = this.contentEb_.string.trim();
        var receiver = this.receiverEb_.string.trim();
        if (!title) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_TITLE);
        }
        else if (!content) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_CONTENT);
        }
        else if (receiver === GameHelper_1.gameHpr.getUid() || receiver === GameHelper_1.gameHpr.user.getNickname()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RECEIVER);
        }
        else if (this.receiverType === 0 || receiver === '-1') { //不能回复系统邮件
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RECEIVER);
        }
        else if (this.receiverType === 1 && !receiver) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RECEIVER);
        }
        else if (this.receiverType === 1) {
            var member = GameHelper_1.gameHpr.alliance.getMemberByUidOrName(receiver);
            if (GameHelper_1.gameHpr.alliance.isCanSendMail()) {
                if (!member) { //盟主只能给盟友发送邮件
                    return ViewHelper_1.viewHelper.showAlert('toast.only_alli_memer_sned_mail');
                }
            }
            else if (!member || (member.job !== Enums_1.AllianceJobType.CREATER && member.job !== Enums_1.AllianceJobType.CREATER_VICE)) { //非盟主只能给盟主发送邮件
                return ViewHelper_1.viewHelper.showAlert('toast.only_alli_creater_end_mail');
            }
        }
        if (ut.getStringLen(title) > 20) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_title');
        }
        else if (ut.getStringLen(content) > 200) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
        }
        else if (this.receiverType !== 1) {
            receiver = '';
        }
        GameHelper_1.gameHpr.net.request('game/HD_SendMail', { title: title, content: content, receiverType: this.receiverType, receiver: receiver }, true).then(function (res) {
            if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_title');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_content');
            }
            else if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else if (_this.isValid) {
                _this.contentEb_.string = '';
                _this.titleEb_.string = '';
                if (_this.receiverType !== 1) {
                    _this.receiverEb_.string = '';
                }
                _this.hide();
                ViewHelper_1.viewHelper.showAlert('toast.send_mail_succeed');
            }
        });
    };
    // path://root/top/target_tc_tce
    WriteMailPnlCtrl.prototype.onClickTarget = function (event, data) {
        var tab = this.receiverType = Number(event.node.name);
        if (tab === 0) {
            this.receiverNode_.Swih('system');
        }
        else if (tab === 1) {
            this.receiverNode_.Swih('receiver_eb');
        }
        else if (tab === 2) {
            this.receiverNode_.Swih('alli_member');
        }
    };
    WriteMailPnlCtrl = __decorate([
        ccclass
    ], WriteMailPnlCtrl);
    return WriteMailPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = WriteMailPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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