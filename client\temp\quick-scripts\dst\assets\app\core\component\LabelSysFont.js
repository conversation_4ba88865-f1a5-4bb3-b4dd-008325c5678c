
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelSysFont.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c87183S+LxND6NKN/25MDlb', 'LabelSysFont');
// app/core/component/LabelSysFont.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
/**
 * 系统字体 属性适应父节点宽度
 */
var LabelSysFont = /** @class */ (function (_super) {
    __extends(LabelSysFont, _super);
    function LabelSysFont() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.label = null;
        return _this;
    }
    LabelSysFont.prototype.onLoad = function () {
        this.label = this.label || this.FindChild('val', cc.Label);
    };
    Object.defineProperty(LabelSysFont.prototype, "string", {
        set: function (val) {
            if (!this.label) {
                this.onLoad();
            }
            this.label.string = val;
            this.label._forceUpdateRenderData();
            this.node.width = this.label.node.width * this.label.node.scaleX;
        },
        enumerable: false,
        configurable: true
    });
    __decorate([
        property(cc.Label)
    ], LabelSysFont.prototype, "label", void 0);
    LabelSysFont = __decorate([
        ccclass,
        menu('自定义组件/LabelSysFont')
    ], LabelSysFont);
    return LabelSysFont;
}(cc.Component));
exports.default = LabelSysFont;
cc.LabelSysFont = LabelSysFont;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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