
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseNoticeCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '57580+3Z/RAhIRV83j6pSr3', 'BaseNoticeCtrl');
// app/core/base/BaseNoticeCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseViewCtrl_1 = require("./BaseViewCtrl");
var BaseNoticeCtrl = /** @class */ (function (_super) {
    __extends(BaseNoticeCtrl, _super);
    function BaseNoticeCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.adaptHeight = 400; //适应高度距离
        _this.initScale = 1;
        return _this;
    }
    BaseNoticeCtrl.prototype.__create = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this._state = 'create';
                        this.node.group = 'ui';
                        this.__listenMaps();
                        this.__register('create');
                        return [4 /*yield*/, this.onCreate()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    BaseNoticeCtrl.prototype.open = function () {
        if (this._state === 'enter') {
            return;
        }
        this._state = 'enter';
        this.node.active = true;
        this.__register('enter');
    };
    BaseNoticeCtrl.prototype.hide = function () {
        if (this._state === 'remove') {
            return;
        }
        this._state = 'remove';
        this.node.active = false;
        this.__unregister('enter');
    };
    BaseNoticeCtrl.prototype.onDestroy = function () {
        this._state = 'clean';
        this.__unregister();
        this.onClean();
    };
    BaseNoticeCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BaseNoticeCtrl.prototype.onClean = function () {
    };
    return BaseNoticeCtrl;
}(BaseViewCtrl_1.default));
exports.default = BaseNoticeCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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