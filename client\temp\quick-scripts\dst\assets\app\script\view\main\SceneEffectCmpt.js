
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SceneEffectCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cac7dNy76pMk74PDIHSkVJd', 'SceneEffectCmpt');
// app/script/view/main/SceneEffectCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var _a = cc._decorator, property = _a.property, ccclass = _a.ccclass;
// 场景特效
var SceneEffectCmpt = /** @class */ (function (_super) {
    __extends(SceneEffectCmpt, _super);
    function SceneEffectCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.MAX_SIZE = 800;
        _this.MAX_SIZE_HALF = cc.v2(400, 400);
        _this.GRID_SIZE = 50;
        _this.preCameraZoomRatio = -1;
        _this.preWinSize = cc.v2();
        _this.maxTileRange = cc.v2();
        _this.centre = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this.tempRangePoints = [];
        _this.initing = false;
        return _this;
    }
    SceneEffectCmpt.prototype.init = function (url, key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.clean();
                        return [4 /*yield*/, assetsMgr.loadTempRes('effect/' + url, cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            cc.instantiate2(pfb, this.node);
                            this.initing = true;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SceneEffectCmpt.prototype.clean = function () {
        this.centre.set2(-1, -1);
        this.initing = false;
        this.node.removeAllChildren();
    };
    SceneEffectCmpt.prototype.updateShow = function (centre) {
        var _this = this;
        this.preCameraZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.centre.set(centre);
        var points = this.getRangePointsByPoint(centre, this.getMaxTileRange());
        this.node.Items(points, function (it, point) {
            it.setPosition(_this.getPixelByPoint(point));
        });
    };
    SceneEffectCmpt.prototype.update = function (dt) {
        if (!this.initing) {
            return;
        }
        var point = this.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition());
        if (!this.centre.equals(point) || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateShow(point);
        }
    };
    SceneEffectCmpt.prototype.getPointByPixel = function (pos) {
        return this._temp_vec2_1.set2(Math.floor(pos.x / this.MAX_SIZE), Math.floor(pos.y / this.MAX_SIZE));
    };
    SceneEffectCmpt.prototype.getPixelByPoint = function (point) {
        return point.mul(this.MAX_SIZE, this._temp_vec2_1).addSelf(this.MAX_SIZE_HALF);
    };
    SceneEffectCmpt.prototype.getMaxTileRange = function () {
        var size = CameraCtrl_1.cameraCtrl.getWorldWinSize();
        if (!this.preWinSize.equals(size)) {
            this.preWinSize.set(size);
            this.maxTileRange.set2(Math.ceil(Math.ceil(size.x / this.MAX_SIZE) / 2), Math.ceil(Math.ceil(size.y / this.MAX_SIZE) / 2));
        }
        return this.maxTileRange;
    };
    // 根据中心点算出范围点
    SceneEffectCmpt.prototype.getRangePointsByPoint = function (point, maxRange) {
        var i = 0, l = this.tempRangePoints.length;
        for (var x = -maxRange.x; x <= maxRange.x; x++) {
            for (var y = -maxRange.y; y <= maxRange.y; y++) {
                var px = point.x + x, py = point.y + y;
                if (i < l) {
                    this.tempRangePoints[i].set2(px, py);
                }
                else {
                    this.tempRangePoints.push(cc.v2(px, py));
                }
                i += 1;
            }
        }
        if (i < l) {
            this.tempRangePoints.splice(i + 1);
        }
        return this.tempRangePoints;
    };
    SceneEffectCmpt = __decorate([
        ccclass
    ], SceneEffectCmpt);
    return SceneEffectCmpt;
}(cc.Component));
exports.default = SceneEffectCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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