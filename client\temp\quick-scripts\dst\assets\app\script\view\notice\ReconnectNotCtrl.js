
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/ReconnectNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9c926XHxCtHNY5lzD95W/bP', 'ReconnectNotCtrl');
// app/script/view/notice/ReconnectNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ReconnectNotCtrl = /** @class */ (function (_super) {
    __extends(ReconnectNotCtrl, _super);
    function ReconnectNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.login = null;
        _this.isReconnecting = false;
        _this.isGameShow = true;
        return _this;
    }
    ReconnectNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_DISCONNECT] = this.onNetDisconnect, _a),
            (_b = {}, _b[EventType_1.default.EVENT_GAME_SHOW] = this.onEventGameShow, _b),
            (_c = {}, _c[EventType_1.default.EVENT_GAME_HIDE] = this.onEventGameHide, _c),
        ];
    };
    ReconnectNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.login = this.getModel('login');
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 网络断开
    ReconnectNotCtrl.prototype.onNetDisconnect = function () {
        var key = mc.currWindName;
        if (!key || key === 'login' || this.isReconnecting || GameHelper_1.gameHpr.net.isKick()) {
            return;
        }
        logger.info('mqant 网络断开', this.isGameShow);
        this.isReconnecting = true;
        if (this.isGameShow) {
            this.show();
        }
    };
    // 进入前台
    ReconnectNotCtrl.prototype.onEventGameShow = function () {
        logger.info('mqant 游戏进入前台', this.isReconnecting);
        this.isGameShow = true;
        if (this.isReconnecting) {
            this.show();
        }
        else {
            GameHelper_1.gameHpr.net.ping(); //这里主动ping一下
        }
    };
    // 进入后台
    ReconnectNotCtrl.prototype.onEventGameHide = function () {
        logger.info('mqant 游戏进入后台', this.isReconnecting);
        this.isGameShow = false;
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ReconnectNotCtrl.prototype.show = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var ok, sceneKey, ret, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        logger.info('mqant reconnect...');
                        this.open();
                        this.rootNode_.Swih(0);
                        // 隐藏所有ui
                        this.emit(mc.Event.LOAD_END_PNL);
                        this.emit(mc.Event.LOAD_END_WIND);
                        this.emit(mc.Event.CLEAN_LOAD_PNL_QUEUE, true);
                        this.emit(NetEvent_1.default.NET_REQ_END);
                        if (!GameHelper_1.gameHpr.guide.isForceWorking()) {
                            this.emit(mc.Event.HIDE_ALL_PNL, '', 'playback/PlaybackUI|area/AreaUI|common/Shop|login/LoginUI|menu/LogoutTip');
                        }
                        return [4 /*yield*/, this.login.reconnect()];
                    case 1:
                        ok = _b.sent();
                        if (!ok) {
                            if (GameHelper_1.gameHpr.net.isKick()) {
                                return [2 /*return*/, this.hide()];
                            }
                            this.isReconnecting = false;
                            this.hide();
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox(GameHelper_1.gameHpr.getTextByNetworkStatus('ui.reconnect_error'), {
                                    ok: function () { return eventCenter.emit(NetEvent_1.default.NET_DISCONNECT); },
                                    okText: 'login.button_reconnect',
                                    lockClose: true,
                                })
                                // return this.rootNode_.Swih(1) //多次失败 提示检测网络
                            ];
                            // return this.rootNode_.Swih(1) //多次失败 提示检测网络
                        }
                        else if (!this.isGameShow) {
                            return [2 /*return*/];
                        }
                        sceneKey = mc.currWindName;
                        return [4 /*yield*/, this.login.tryLogin()];
                    case 2:
                        ret = _b.sent();
                        if (!!this.isGameShow) return [3 /*break*/, 3];
                        return [2 /*return*/];
                    case 3:
                        if (!(ret.state === Enums_1.LoginState.UNDONE_NOVICE)) return [3 /*break*/, 4];
                        if (GameHelper_1.gameHpr.isNoviceMode) {
                            this.isReconnecting = false;
                            return [2 /*return*/, this.hide()];
                        }
                        sceneKey = 'login';
                        return [3 /*break*/, 15];
                    case 4:
                        if (!(ret.state === Enums_1.LoginState.NOT_SELECT_SERVER)) return [3 /*break*/, 5];
                        sceneKey = 'lobby';
                        return [3 /*break*/, 15];
                    case 5:
                        if (!(ret.state !== Enums_1.LoginState.SUCCEED || ret.state === Enums_1.LoginState.VERSION_TOOLOW)) return [3 /*break*/, 6];
                        sceneKey = 'login';
                        return [3 /*break*/, 15];
                    case 6:
                        if (!(GameHelper_1.gameHpr.getSid() && !GameHelper_1.gameHpr.isNoviceMode && sceneKey !== 'login' && sceneKey !== 'lobby')) return [3 /*break*/, 14];
                        return [4 /*yield*/, this.login.enterGame(ret.data, true)];
                    case 7:
                        ret = _b.sent();
                        if (!!this.isGameShow) return [3 /*break*/, 8];
                        return [2 /*return*/];
                    case 8:
                        if (!(ret.state === Enums_1.LoginState.VERSION_TOOLOW || ret.state === Enums_1.LoginState.VERSION_TOOTALL)) return [3 /*break*/, 9];
                        sceneKey = 'login'; //版本不对
                        return [3 /*break*/, 13];
                    case 9:
                        if (!(ret.state !== Enums_1.LoginState.SUCCEED)) return [3 /*break*/, 10];
                        sceneKey = 'lobby';
                        return [3 /*break*/, 13];
                    case 10:
                        data = (_a = ret === null || ret === void 0 ? void 0 : ret.data) === null || _a === void 0 ? void 0 : _a.rst;
                        GameHelper_1.gameHpr.initServerConfig(data);
                        // 初始化信息
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.init(data.player)
                            // 初始化世界数据
                        ];
                    case 11:
                        // 初始化信息
                        _b.sent();
                        // 初始化世界数据
                        return [4 /*yield*/, GameHelper_1.gameHpr.world.init(GameHelper_1.gameHpr.getSid(), data.world)
                            // 清空请求过的区域
                        ];
                    case 12:
                        // 初始化世界数据
                        _b.sent();
                        // 清空请求过的区域
                        GameHelper_1.gameHpr.areaCenter.cleanAreas();
                        // 重新初始化
                        GameHelper_1.gameHpr.reinit();
                        _b.label = 13;
                    case 13: return [3 /*break*/, 15];
                    case 14:
                        if (mc.currWindName !== 'lobby' && !GameHelper_1.gameHpr.isNoviceMode) { //如果在大厅中
                            sceneKey = 'lobby';
                        }
                        _b.label = 15;
                    case 15:
                        // 进入场景
                        this.isReconnecting = false;
                        this.hide();
                        if (!(sceneKey !== mc.currWindName)) return [3 /*break*/, 16];
                        ViewHelper_1.viewHelper.gotoWind(sceneKey);
                        return [3 /*break*/, 18];
                    case 16:
                        if (!(sceneKey === 'lobby')) return [3 /*break*/, 18];
                        return [4 /*yield*/, GameHelper_1.gameHpr.lobby.init()];
                    case 17:
                        _b.sent();
                        GameHelper_1.gameHpr.lobby.active();
                        _b.label = 18;
                    case 18:
                        GameHelper_1.gameHpr.user.enter();
                        this.emit(NetEvent_1.default.NET_RECONNECT);
                        return [2 /*return*/];
                }
            });
        });
    };
    __decorate([
        ut.syncLock
    ], ReconnectNotCtrl.prototype, "show", null);
    ReconnectNotCtrl = __decorate([
        ccclass
    ], ReconnectNotCtrl);
    return ReconnectNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = ReconnectNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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