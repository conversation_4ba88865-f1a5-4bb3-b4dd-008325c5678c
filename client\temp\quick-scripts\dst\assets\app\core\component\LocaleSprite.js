
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LocaleSprite.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '120c1Cc8FVER42epaZtmWqS', 'LocaleSprite');
// app/core/component/LocaleSprite.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LocaleSprite = /** @class */ (function (_super) {
    __extends(LocaleSprite, _super);
    function LocaleSprite() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.frames = [];
        _this._sprite = null;
        _this._json = null;
        _this._lang = '';
        _this._change = false;
        return _this;
    }
    Object.defineProperty(LocaleSprite.prototype, "sprite", {
        get: function () {
            if (!this._sprite) {
                this._sprite = this.getComponent(cc.Sprite);
            }
            return this._sprite;
        },
        enumerable: false,
        configurable: true
    });
    LocaleSprite.prototype.onEnable = function () {
        if (!mc.lang) {
            return;
        }
        if (this._lang !== mc.lang) {
            this._lang = mc.lang;
            this._json = null;
        }
        if (!this._json) {
            this.updateJson();
        }
        this.updateSprite();
        this._change = mc.canChangeLang;
        if (this._change) {
            eventCenter.on(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    LocaleSprite.prototype.onDisable = function () {
        if (this._change) {
            this._change = false;
            eventCenter.off(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    // 语言切换
    LocaleSprite.prototype.onLanguageChanged = function (lang) {
        this._lang = lang;
        this.updateSprite();
    };
    // 刷新string
    LocaleSprite.prototype.updateSprite = function () {
        return __awaiter(this, void 0, void 0, function () {
            var val, spr;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        val = this._json[this._lang];
                        if (val === undefined) {
                            return [2 /*return*/];
                        }
                        spr = this.frames.find(function (m) { return m.name === val; });
                        if (!!spr) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes(val, cc.SpriteFrame, '__lang_sprite')];
                    case 1:
                        spr = _a.sent();
                        _a.label = 2;
                    case 2:
                        if (!spr) {
                            return [2 /*return*/, logger.info('没有找到多语言图片, key=' + this.key + ', url=' + val)];
                        }
                        else if (this.isValid) {
                            this.sprite.spriteFrame = spr;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新json
    LocaleSprite.prototype.updateJson = function () {
        var _a = this.key.split('.'), name = _a[0], id = _a[1];
        this._json = assetsMgr.getJsonData(name, id) || {};
    };
    // 设置key
    LocaleSprite.prototype.setKey = function (key) {
        if (this.key !== key || !this._json) {
            this.key = key;
            this.updateJson();
        }
        this.updateSprite();
    };
    LocaleSprite.prototype.addSpriteFrame = function (val) {
        this.frames.push(val);
    };
    __decorate([
        property()
    ], LocaleSprite.prototype, "key", void 0);
    __decorate([
        property([cc.SpriteFrame])
    ], LocaleSprite.prototype, "frames", void 0);
    LocaleSprite = __decorate([
        ccclass,
        menu('多语言组件/LocaleSprite'),
        requireComponent(cc.Sprite)
    ], LocaleSprite);
    return LocaleSprite;
}(BaseLocale_1.default));
exports.default = LocaleSprite;
cc.LocaleSprite = LocaleSprite;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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