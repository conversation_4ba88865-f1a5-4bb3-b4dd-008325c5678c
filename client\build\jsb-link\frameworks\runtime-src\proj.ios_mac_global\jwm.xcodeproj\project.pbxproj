// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1A37E9DA200DEF120078AF72 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A37E9D9200DEF110078AF72 /* SystemConfiguration.framework */; };
		1A37E9DC200DEF190078AF72 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A37E9DB200DEF190078AF72 /* SystemConfiguration.framework */; };
		1A499EBB1F0CDDC3004E6AEC /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A499EBA1F0CDDC3004E6AEC /* CFNetwork.framework */; };
		1A499EBD1F0CDDD9004E6AEC /* libicucore.A.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A499EBC1F0CDDD9004E6AEC /* libicucore.A.tbd */; };
		1A82F5FB169AC92500C4B13A /* libsqlite3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A82F5FA169AC92500C4B13A /* libsqlite3.dylib */; };
		1AC22EDE18CA0E11007112B9 /* project.json in Resources */ = {isa = PBXBuildFile; fileRef = 1A1A081B18C9DF05005C6854 /* project.json */; };
		1ACA99231F550A20002DE225 /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1ACA99221F550A20002DE225 /* JavaScriptCore.framework */; };
		1ACA99251F550A28002DE225 /* JavaScriptCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1ACA99241F550A28002DE225 /* JavaScriptCore.framework */; };
		1AD7E0A818C9DB93004817A6 /* main.js in Resources */ = {isa = PBXBuildFile; fileRef = 1A6BF21418C9DB5900FB0E1C /* main.js */; };
		1AD7E0A918C9DBE3004817A6 /* main.js in Resources */ = {isa = PBXBuildFile; fileRef = 1A6BF21418C9DB5900FB0E1C /* main.js */; };
		1AE159EC18C9DF3600FCA372 /* project.json in Resources */ = {isa = PBXBuildFile; fileRef = 1A1A081B18C9DF05005C6854 /* project.json */; };
		1AFFCD7E1F7A5D5400628F2C /* LaunchScreenBackground.png in Resources */ = {isa = PBXBuildFile; fileRef = 1AFFCD7B1F7A5D4F00628F2C /* LaunchScreenBackground.png */; };
		1AFFCD7F1F7A5D5400628F2C /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1ABC56911F7A005D00826282 /* Images.xcassets */; };
		22CC757121BA5B5C00FBF2F7 /* SDKWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 22CC756D21BA5B5C00FBF2F7 /* SDKWrapper.m */; };
		286B0E9524075FE000095E1A /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 28A34C0F225B462E00926306 /* assets */; };
		28A34C10225B462E00926306 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 28A34C0F225B462E00926306 /* assets */; };
		294D7D801D0D668F002CE7B7 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 294D7D7F1D0D668F002CE7B7 /* CoreText.framework */; };
		4011F9CF212BD6750091CC5B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4011F9D1212BD6750091CC5B /* LaunchScreen.storyboard */; };
		4011F9D5212BD7050091CC5B /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 4011F9D7212BD7050091CC5B /* Localizable.strings */; };
		4033CDBC20E4935100DAA37B /* jsb-adapter in Resources */ = {isa = PBXBuildFile; fileRef = 4033CDBB20E4935000DAA37B /* jsb-adapter */; };
		4033CDBD20E4935100DAA37B /* jsb-adapter in Resources */ = {isa = PBXBuildFile; fileRef = 4033CDBB20E4935000DAA37B /* jsb-adapter */; };
		467AF6082321FBED00770112 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 467AF6072321FBED00770112 /* WebKit.framework */; };
		467AF613232881E400770112 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 467AF612232881E400770112 /* AVKit.framework */; };
		467AF615232881EB00770112 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 467AF614232881EB00770112 /* CoreMedia.framework */; };
		4DD5E6AB1CDAF6430046171E /* libcocos2d Mac.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DD0DB561CDAF5F40099AD86 /* libcocos2d Mac.a */; };
		4DD5E6B11CDAF65C0046171E /* libcocos2d iOS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DD0DB581CDAF5F40099AD86 /* libcocos2d iOS.a */; };
		502380DC17EBB88200990C9B /* libcurl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 502380DB17EBB88200990C9B /* libcurl.dylib */; };
		509D4A8117EBB24E00697056 /* AppDelegate.cpp in Sources */ = {isa = PBXBuildFile; fileRef = D4545215156E28EF00887EB5 /* AppDelegate.cpp */; };
		509D4A8817EBB24E00697056 /* libsqlite3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A82F5FA169AC92500C4B13A /* libsqlite3.dylib */; };
		509D4A8917EBB24E00697056 /* libz.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = D454520B156E22BD00887EB5 /* libz.dylib */; };
		509D4A8A17EBB24E00697056 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275411517C094001B78AA /* QuartzCore.framework */; };
		509D4A8C17EBB24E00697056 /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275451517C094001B78AA /* OpenAL.framework */; };
		509D4A8D17EBB24E00697056 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275471517C094001B78AA /* AudioToolbox.framework */; };
		509D4A8E17EBB24E00697056 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275491517C094001B78AA /* AVFoundation.framework */; };
		509D4A9017EBB24E00697056 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A922754D1517C094001B78AA /* Foundation.framework */; };
		509D4A9117EBB24E00697056 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A922754F1517C094001B78AA /* CoreGraphics.framework */; };
		509D4ABC17EBB2AB00697056 /* AppController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 509D4AAC17EBB2AB00697056 /* AppController.mm */; };
		509D4AC817EBB2AB00697056 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 509D4AB817EBB2AB00697056 /* main.m */; };
		509D4AC917EBB2AB00697056 /* RootViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 509D4ABB17EBB2AB00697056 /* RootViewController.mm */; };
		509D4ACF17EBB2BE00697056 /* Icon.icns in Resources */ = {isa = PBXBuildFile; fileRef = 509D4ACB17EBB2BE00697056 /* Icon.icns */; };
		509D4AE717EBB81800697056 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 509D4AE617EBB81800697056 /* OpenGL.framework */; };
		509D4AE917EBB82000697056 /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 509D4AE817EBB82000697056 /* AppKit.framework */; };
		509D4AEB17EBB82600697056 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 509D4AEA17EBB82600697056 /* IOKit.framework */; };
		50E3168B4BEE3544DA405DE6 /* Pods_jwm_mobile.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 80FF04FF3442AABA4728DEA3 /* Pods_jwm_mobile.framework */; };
		A92275421517C094001B78AA /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275411517C094001B78AA /* QuartzCore.framework */; };
		A92275441517C094001B78AA /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275431517C094001B78AA /* OpenGLES.framework */; };
		A92275461517C094001B78AA /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275451517C094001B78AA /* OpenAL.framework */; };
		A92275481517C094001B78AA /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275471517C094001B78AA /* AudioToolbox.framework */; };
		A922754A1517C094001B78AA /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A92275491517C094001B78AA /* AVFoundation.framework */; };
		A922754C1517C094001B78AA /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A922754B1517C094001B78AA /* UIKit.framework */; };
		A922754E1517C094001B78AA /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A922754D1517C094001B78AA /* Foundation.framework */; };
		A92275501517C094001B78AA /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A922754F1517C094001B78AA /* CoreGraphics.framework */; };
		ABE456E31D34F87300F1F400 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABE456E21D34F87300F1F400 /* CFNetwork.framework */; };
		ABE456E51D34F87A00F1F400 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABE456E41D34F87A00F1F400 /* CoreFoundation.framework */; };
		ABE456E71D34F87F00F1F400 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABE456E61D34F87F00F1F400 /* MobileCoreServices.framework */; };
		ABE456E91D34F88A00F1F400 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABE456E81D34F88A00F1F400 /* SystemConfiguration.framework */; };
		ABE456EB1D34F89000F1F400 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ABE456EA1D34F89000F1F400 /* GameController.framework */; };
		BA3A85EC1A724AE900924D24 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BA3A85EB1A724AE900924D24 /* Security.framework */; };
		BAEE4D841AC40C11003BEB0F /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BAEE4D831AC40C11003BEB0F /* Security.framework */; };
		C03780EB18BEE0E400FE4F13 /* src in Resources */ = {isa = PBXBuildFile; fileRef = C03780EA18BEE0E400FE4F13 /* src */; };
		C03780EC18BEE0E400FE4F13 /* src in Resources */ = {isa = PBXBuildFile; fileRef = C03780EA18BEE0E400FE4F13 /* src */; };
		C06E23CC18CEFE680093C81A /* main.cpp in Sources */ = {isa = PBXBuildFile; fileRef = C06E23CB18CEFE680093C81A /* main.cpp */; };
		D103A65129F110F000F7B3A0 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = D103A65029F110F000F7B3A0 /* GoogleService-Info.plist */; };
		D13073242B174A9F009820E9 /* FirebaseHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D13073232B174A9F009820E9 /* FirebaseHelper.m */; };
		D14D6EAD29BEFCC90026E67A /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D14D6EAC29BEFCC90026E67A /* StoreKit.framework */; };
		D14EF6472CA4370100DEC044 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = D14EF6452CA4370100DEC044 /* InfoPlist.strings */; };
		D153F6D82C7C827100C0F45C /* DeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D153F6D72C7C827100C0F45C /* DeviceHelper.m */; };
		D15FDEF129BF37C500B1820E /* jsbHelp.mm in Sources */ = {isa = PBXBuildFile; fileRef = D15FDEF029BF37C500B1820E /* jsbHelp.mm */; };
		D15FDEF629BF389F00B1820E /* AppleLoginManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = D15FDEF429BF389F00B1820E /* AppleLoginManager.mm */; };
		D15FDEFC29BF38AF00B1820E /* EMAppStorePay.m in Sources */ = {isa = PBXBuildFile; fileRef = D15FDEF829BF38AF00B1820E /* EMAppStorePay.m */; };
		D15FDEFD29BF38AF00B1820E /* EMAppStoreBoot.m in Sources */ = {isa = PBXBuildFile; fileRef = D15FDEFB29BF38AF00B1820E /* EMAppStoreBoot.m */; };
		D15FDF0029BF3B3B00B1820E /* AppTrackingTransparency.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D15FDEFF29BF3B3A00B1820E /* AppTrackingTransparency.framework */; };
		D15FDF0829BF426700B1820E /* AuthenticationServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D15FDF0729BF426700B1820E /* AuthenticationServices.framework */; };
		D15FDF0B29BF466500B1820E /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D15FDF0A29BF466500B1820E /* Photos.framework */; };
		D16B07AA2A66344A00FD6E8C /* CHToolKeychain.m in Sources */ = {isa = PBXBuildFile; fileRef = D16B07A92A66344A00FD6E8C /* CHToolKeychain.m */; };
		D18027D62A653509002CC495 /* MessageHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D18027D42A653508002CC495 /* MessageHelper.m */; };
		D1919B822B5668E6000AC56B /* GoogleHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D1919B812B5668E6000AC56B /* GoogleHelper.m */; };
		D1AD12F929D5A58A00808FA4 /* AppsflyerHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D1AD12F829D5A58A00808FA4 /* AppsflyerHelper.m */; };
		D1AE3B6529E6A162002D944E /* FacebookAEM in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6429E6A162002D944E /* FacebookAEM */; };
		D1AE3B6729E6A162002D944E /* FacebookBasics in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6629E6A162002D944E /* FacebookBasics */; };
		D1AE3B6929E6A162002D944E /* FacebookCore in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6829E6A162002D944E /* FacebookCore */; };
		D1AE3B6B29E6A162002D944E /* FacebookGamingServices in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6A29E6A162002D944E /* FacebookGamingServices */; };
		D1AE3B6D29E6A162002D944E /* FacebookLogin in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6C29E6A162002D944E /* FacebookLogin */; };
		D1AE3B6F29E6A162002D944E /* FacebookShare in Frameworks */ = {isa = PBXBuildFile; productRef = D1AE3B6E29E6A162002D944E /* FacebookShare */; };
		D1B65CBF29CD8B5900210BDE /* ThinkingSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D1B65CBE29CD8B5900210BDE /* ThinkingSDK.framework */; };
		D1B65CC329CD8B7A00210BDE /* CocosCreatorProxyApi.mm in Sources */ = {isa = PBXBuildFile; fileRef = D1B65CC129CD8B7A00210BDE /* CocosCreatorProxyApi.mm */; };
		D1F2EB1B29C2C293001EAA42 /* FacebookHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = D1F2EB1829C2C293001EAA42 /* FacebookHelper.m */; };
		D1F2EB1C29C2C294001EAA42 /* FaceBookManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1F2EB1A29C2C293001EAA42 /* FaceBookManager.swift */; };
		D454520C156E22BD00887EB5 /* libz.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = D454520B156E22BD00887EB5 /* libz.dylib */; };
		D4545227156E28EF00887EB5 /* AppDelegate.cpp in Sources */ = {isa = PBXBuildFile; fileRef = D4545215156E28EF00887EB5 /* AppDelegate.cpp */; };
		D6B061241803AB9F0077942B /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D6B061231803AB9F0077942B /* CoreMotion.framework */; };
		ED6D5265263544C8003A20DD /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ED6D5264263544C8003A20DD /* CoreVideo.framework */; };
		FA676C761C71AFA200E091E3 /* libiconv.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = FA676C751C71AFA200E091E3 /* libiconv.dylib */; };
		FA676C781C71AFAB00E091E3 /* libiconv.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = FA676C771C71AFAB00E091E3 /* libiconv.dylib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		4DD0DB551CDAF5F40099AD86 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 1551A33F158F2AB200E66CFE;
			remoteInfo = "libcocos2d Mac";
		};
		4DD0DB571CDAF5F40099AD86 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A07A4D641783777C0073F6A7;
			remoteInfo = "libcocos2d iOS";
		};
		4DD5E6A91CDAF6330046171E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 1551A33E158F2AB200E66CFE;
			remoteInfo = "libcocos2d Mac";
		};
		4DD5E6AF1CDAF6520046171E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = A07A4C241783777C0073F6A7;
			remoteInfo = "libcocos2d iOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		051C018521E32AF000D4A347 /* UserConfigMac.release.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = UserConfigMac.release.xcconfig; sourceTree = "<group>"; };
		051C018621E32AF100D4A347 /* UserConfigMac.debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; path = UserConfigMac.debug.xcconfig; sourceTree = "<group>"; };
		05A8AD8A2449A9F900A65D12 /* NativeConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NativeConfig.h; sourceTree = "<group>"; };
		1A1A081B18C9DF05005C6854 /* project.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = project.json; path = ../../../project.json; sourceTree = "<group>"; };
		1A37E9D9200DEF110078AF72 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS11.2.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		1A37E9DB200DEF190078AF72 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		1A499EBA1F0CDDC3004E6AEC /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		1A499EBC1F0CDDD9004E6AEC /* libicucore.A.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.A.tbd; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS10.3.sdk/usr/lib/libicucore.A.tbd; sourceTree = DEVELOPER_DIR; };
		1A6BF21418C9DB5900FB0E1C /* main.js */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.javascript; name = main.js; path = ../../../main.js; sourceTree = "<group>"; };
		1A82F5FA169AC92500C4B13A /* libsqlite3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.dylib; path = usr/lib/libsqlite3.dylib; sourceTree = SDKROOT; };
		1A96A4F2174A3432008653A9 /* libcurl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcurl.a; path = "../../cocos2d-x/cocos2dx/platform/third_party/ios/libraries/libcurl.a"; sourceTree = "<group>"; };
		1ABC56911F7A005D00826282 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ios/Images.xcassets; sourceTree = "<group>"; };
		1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = cocos2d_libs.xcodeproj; path = "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/build/cocos2d_libs.xcodeproj"; sourceTree = "<absolute>"; };
		1ACA99221F550A20002DE225 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		1ACA99241F550A28002DE225 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS10.3.sdk/System/Library/Frameworks/JavaScriptCore.framework; sourceTree = DEVELOPER_DIR; };
		1AFFCD7B1F7A5D4F00628F2C /* LaunchScreenBackground.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = LaunchScreenBackground.png; path = ios/LaunchScreenBackground.png; sourceTree = "<group>"; };
		22CC756D21BA5B5C00FBF2F7 /* SDKWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SDKWrapper.m; path = ios/SDKWrapper.m; sourceTree = "<group>"; };
		22CC756F21BA5B5C00FBF2F7 /* SDKDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDKDelegate.h; sourceTree = "<group>"; };
		22CC757021BA5B5C00FBF2F7 /* SDKWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SDKWrapper.h; path = ios/SDKWrapper.h; sourceTree = "<group>"; };
		22F236C12E65A48500463DCC /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		22F236C22E65A48600463DCC /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = ios/vi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		22F236C32E65A48600463DCC /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		28A34C0F225B462E00926306 /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; name = assets; path = ../../../assets; sourceTree = "<group>"; };
		294D7D7F1D0D668F002CE7B7 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/CoreText.framework; sourceTree = DEVELOPER_DIR; };
		3F01F19A904F6C7742206F4D /* Pods-jwm-mobile.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-jwm-mobile.release.xcconfig"; path = "Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile.release.xcconfig"; sourceTree = "<group>"; };
		4011F9D2212BD6810091CC5B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		4011F9D8212BD7150091CC5B /* Base */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = Base; path = Base.lproj/Localizable.strings; sourceTree = "<group>"; };
		4011F9D9212BD73A0091CC5B /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		4033CDBB20E4935000DAA37B /* jsb-adapter */ = {isa = PBXFileReference; lastKnownFileType = folder; name = "jsb-adapter"; path = "../../../jsb-adapter"; sourceTree = "<group>"; };
		467AF6072321FBED00770112 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/WebKit.framework; sourceTree = DEVELOPER_DIR; };
		467AF612232881E400770112 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/AVKit.framework; sourceTree = DEVELOPER_DIR; };
		467AF614232881EB00770112 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/CoreMedia.framework; sourceTree = DEVELOPER_DIR; };
		4D697BD91CBD301C00A5AF29 /* libjs_static.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libjs_static.a; path = "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/libs/libjs_static.a"; sourceTree = "<absolute>"; };
		4D697BDC1CBD303100A5AF29 /* libjs_static.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libjs_static.a; path = "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/libs/libjs_static.a"; sourceTree = "<absolute>"; };
		502380DB17EBB88200990C9B /* libcurl.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libcurl.dylib; path = usr/lib/libcurl.dylib; sourceTree = SDKROOT; };
		509D4AAA17EBB24E00697056 /* jwm-desktop.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "jwm-desktop.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		509D4AAB17EBB2AB00697056 /* AppController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppController.h; path = ios/AppController.h; sourceTree = "<group>"; };
		509D4AAC17EBB2AB00697056 /* AppController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppController.mm; path = ios/AppController.mm; sourceTree = "<group>"; };
		509D4AB717EBB2AB00697056 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ios/Info.plist; sourceTree = "<group>"; };
		509D4AB817EBB2AB00697056 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = ios/main.m; sourceTree = "<group>"; };
		509D4AB917EBB2AB00697056 /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Prefix.pch; path = ios/Prefix.pch; sourceTree = "<group>"; };
		509D4ABA17EBB2AB00697056 /* RootViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RootViewController.h; path = ios/RootViewController.h; sourceTree = "<group>"; };
		509D4ABB17EBB2AB00697056 /* RootViewController.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = RootViewController.mm; path = ios/RootViewController.mm; sourceTree = "<group>"; };
		509D4ACB17EBB2BE00697056 /* Icon.icns */ = {isa = PBXFileReference; lastKnownFileType = image.icns; path = Icon.icns; sourceTree = "<group>"; };
		509D4ACC17EBB2BE00697056 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		509D4ACE17EBB2BE00697056 /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		509D4AE617EBB81800697056 /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		509D4AE817EBB82000697056 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		509D4AEA17EBB82600697056 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		6E1C75C500C1D5EA6528A72B /* Pods-jwm-desktop.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-jwm-desktop.debug.xcconfig"; path = "Target Support Files/Pods-jwm-desktop/Pods-jwm-desktop.debug.xcconfig"; sourceTree = "<group>"; };
		80FF04FF3442AABA4728DEA3 /* Pods_jwm_mobile.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_jwm_mobile.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A922753D1517C094001B78AA /* jwm-mobile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "jwm-mobile.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A92275411517C094001B78AA /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		A92275431517C094001B78AA /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		A92275451517C094001B78AA /* OpenAL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenAL.framework; path = System/Library/Frameworks/OpenAL.framework; sourceTree = SDKROOT; };
		A92275471517C094001B78AA /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		A92275491517C094001B78AA /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		A922754B1517C094001B78AA /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		A922754D1517C094001B78AA /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		A922754F1517C094001B78AA /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		ABE456E21D34F87300F1F400 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/CFNetwork.framework; sourceTree = DEVELOPER_DIR; };
		ABE456E41D34F87A00F1F400 /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/CoreFoundation.framework; sourceTree = DEVELOPER_DIR; };
		ABE456E61D34F87F00F1F400 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/MobileCoreServices.framework; sourceTree = DEVELOPER_DIR; };
		ABE456E81D34F88A00F1F400 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		ABE456EA1D34F89000F1F400 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/GameController.framework; sourceTree = DEVELOPER_DIR; };
		BA3A85EB1A724AE900924D24 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.1.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		BA4E718019EB6E3E00932425 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.0.sdk/System/Library/Frameworks/MediaPlayer.framework; sourceTree = DEVELOPER_DIR; };
		BA4E718219EB6E4600932425 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.0.sdk/System/Library/Frameworks/GameController.framework; sourceTree = DEVELOPER_DIR; };
		BAEE4D831AC40C11003BEB0F /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		C03780EA18BEE0E400FE4F13 /* src */ = {isa = PBXFileReference; lastKnownFileType = folder; name = src; path = ../../../src; sourceTree = "<group>"; };
		C06E23CB18CEFE680093C81A /* main.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = main.cpp; sourceTree = "<group>"; };
		C0799CB518BAE62000E9C828 /* res */ = {isa = PBXFileReference; lastKnownFileType = folder; name = res; path = ../../../res; sourceTree = "<group>"; };
		C113BDCEA2DC7648E7D112E7 /* Pods-jwm-desktop.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-jwm-desktop.release.xcconfig"; path = "Target Support Files/Pods-jwm-desktop/Pods-jwm-desktop.release.xcconfig"; sourceTree = "<group>"; };
		D103A65029F110F000F7B3A0 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		D10447552CA56C5700E77AD4 /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = ios/id.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D10447562CA56C5900E77AD4 /* id */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = id; path = id.lproj/Localizable.strings; sourceTree = "<group>"; };
		D10447572CA56C7500E77AD4 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = ios/th.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D10447582CA56C7700E77AD4 /* th */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = th; path = th.lproj/Localizable.strings; sourceTree = "<group>"; };
		D13073232B174A9F009820E9 /* FirebaseHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = FirebaseHelper.m; path = ios/FirebaseHelper.m; sourceTree = "<group>"; };
		D13073252B174ABF009820E9 /* FirebaseHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = FirebaseHelper.h; path = ios/FirebaseHelper.h; sourceTree = "<group>"; };
		D14D6EAC29BEFCC90026E67A /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS16.1.sdk/System/Library/Frameworks/StoreKit.framework; sourceTree = DEVELOPER_DIR; };
		D14D6EAE29BEFCD60026E67A /* jwm-mobile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "jwm-mobile.entitlements"; sourceTree = "<group>"; };
		D14EF63E2CA434CD00DEC044 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		D14EF6422CA435E100DEC044 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		D14EF6432CA435F200DEC044 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		D14EF6442CA435FC00DEC044 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/Localizable.strings"; sourceTree = "<group>"; };
		D14EF6462CA4370100DEC044 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = ios/en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D14EF6482CA4373400DEC044 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "ios/zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		D14EF6492CA4374100DEC044 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "ios/zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		D14EF64A2CA4374600DEC044 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "ios/zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		D14EF64B2CA4374E00DEC044 /* English */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = English; path = ios/English.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D14EF64C2CA4375100DEC044 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ios/ja.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D14EF64D2CA4375700DEC044 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ios/ko.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		D153F6D72C7C827100C0F45C /* DeviceHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = DeviceHelper.m; path = ios/DeviceHelper.m; sourceTree = "<group>"; };
		D153F6DA2C7C82A300C0F45C /* DeviceHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = DeviceHelper.h; path = ios/DeviceHelper.h; sourceTree = "<group>"; };
		D15FDEEF29BF37C500B1820E /* jsbHelp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = jsbHelp.h; path = ios/jsbHelp.h; sourceTree = "<group>"; };
		D15FDEF029BF37C500B1820E /* jsbHelp.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = jsbHelp.mm; path = ios/jsbHelp.mm; sourceTree = "<group>"; };
		D15FDEF429BF389F00B1820E /* AppleLoginManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AppleLoginManager.mm; sourceTree = "<group>"; };
		D15FDEF529BF389F00B1820E /* AppleLoginManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppleLoginManager.h; sourceTree = "<group>"; };
		D15FDEF829BF38AF00B1820E /* EMAppStorePay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = EMAppStorePay.m; sourceTree = "<group>"; };
		D15FDEF929BF38AF00B1820E /* EMAppStoreBoot.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EMAppStoreBoot.h; sourceTree = "<group>"; };
		D15FDEFA29BF38AF00B1820E /* EMAppStorePay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EMAppStorePay.h; sourceTree = "<group>"; };
		D15FDEFB29BF38AF00B1820E /* EMAppStoreBoot.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = EMAppStoreBoot.m; sourceTree = "<group>"; };
		D15FDEFF29BF3B3A00B1820E /* AppTrackingTransparency.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppTrackingTransparency.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS16.1.sdk/System/Library/Frameworks/AppTrackingTransparency.framework; sourceTree = DEVELOPER_DIR; };
		D15FDF0729BF426700B1820E /* AuthenticationServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AuthenticationServices.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS16.1.sdk/System/Library/Frameworks/AuthenticationServices.framework; sourceTree = DEVELOPER_DIR; };
		D15FDF0A29BF466500B1820E /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS16.1.sdk/System/Library/Frameworks/Photos.framework; sourceTree = DEVELOPER_DIR; };
		D16B07A82A66344A00FD6E8C /* CHToolKeychain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CHToolKeychain.h; sourceTree = "<group>"; };
		D16B07A92A66344A00FD6E8C /* CHToolKeychain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CHToolKeychain.m; sourceTree = "<group>"; };
		D18027D42A653508002CC495 /* MessageHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = MessageHelper.m; path = ios/MessageHelper.m; sourceTree = "<group>"; };
		D18027D52A653509002CC495 /* MessageHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = MessageHelper.h; path = ios/MessageHelper.h; sourceTree = "<group>"; };
		D1919B812B5668E6000AC56B /* GoogleHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GoogleHelper.m; sourceTree = "<group>"; };
		D1919B832B566901000AC56B /* GoogleHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GoogleHelper.h; sourceTree = "<group>"; };
		D1AD12F729D5A58A00808FA4 /* AppsflyerHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppsflyerHelper.h; sourceTree = "<group>"; };
		D1AD12F829D5A58A00808FA4 /* AppsflyerHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppsflyerHelper.m; sourceTree = "<group>"; };
		D1B65CBE29CD8B5900210BDE /* ThinkingSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ThinkingSDK.framework; sourceTree = "<group>"; };
		D1B65CC129CD8B7A00210BDE /* CocosCreatorProxyApi.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CocosCreatorProxyApi.mm; sourceTree = "<group>"; };
		D1B65CC229CD8B7A00210BDE /* CocosCreatorProxyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CocosCreatorProxyApi.h; sourceTree = "<group>"; };
		D1F2EB1829C2C293001EAA42 /* FacebookHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FacebookHelper.m; sourceTree = "<group>"; };
		D1F2EB1929C2C293001EAA42 /* FacebookHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FacebookHelper.h; sourceTree = "<group>"; };
		D1F2EB1A29C2C293001EAA42 /* FaceBookManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FaceBookManager.swift; sourceTree = "<group>"; };
		D1F2EB3029C2CC91001EAA42 /* jwm-mobile-Bridging-Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "jwm-mobile-Bridging-Header.h"; sourceTree = "<group>"; };
		D454520B156E22BD00887EB5 /* libz.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.dylib; path = usr/lib/libz.dylib; sourceTree = SDKROOT; };
		D4545215156E28EF00887EB5 /* AppDelegate.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = AppDelegate.cpp; sourceTree = "<group>"; };
		D4545216156E28EF00887EB5 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		D6B061231803AB9F0077942B /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS7.0.sdk/System/Library/Frameworks/CoreMotion.framework; sourceTree = DEVELOPER_DIR; };
		ED6D5264263544C8003A20DD /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.1.sdk/System/Library/Frameworks/CoreVideo.framework; sourceTree = DEVELOPER_DIR; };
		FA676C751C71AFA200E091E3 /* libiconv.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libiconv.dylib; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS8.4.sdk/usr/lib/libiconv.dylib; sourceTree = DEVELOPER_DIR; };
		FA676C771C71AFAB00E091E3 /* libiconv.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libiconv.dylib; path = usr/lib/libiconv.dylib; sourceTree = SDKROOT; };
		FE629E463CDF3382B5ACEF86 /* Pods-jwm-mobile.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-jwm-mobile.debug.xcconfig"; path = "Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		509D4A8217EBB24E00697056 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A37E9DC200DEF190078AF72 /* SystemConfiguration.framework in Frameworks */,
				1ACA99231F550A20002DE225 /* JavaScriptCore.framework in Frameworks */,
				1A499EBB1F0CDDC3004E6AEC /* CFNetwork.framework in Frameworks */,
				4DD5E6AB1CDAF6430046171E /* libcocos2d Mac.a in Frameworks */,
				FA676C781C71AFAB00E091E3 /* libiconv.dylib in Frameworks */,
				BAEE4D841AC40C11003BEB0F /* Security.framework in Frameworks */,
				502380DC17EBB88200990C9B /* libcurl.dylib in Frameworks */,
				509D4A8817EBB24E00697056 /* libsqlite3.dylib in Frameworks */,
				509D4A8917EBB24E00697056 /* libz.dylib in Frameworks */,
				509D4AEB17EBB82600697056 /* IOKit.framework in Frameworks */,
				509D4AE917EBB82000697056 /* AppKit.framework in Frameworks */,
				509D4AE717EBB81800697056 /* OpenGL.framework in Frameworks */,
				509D4A8A17EBB24E00697056 /* QuartzCore.framework in Frameworks */,
				509D4A8C17EBB24E00697056 /* OpenAL.framework in Frameworks */,
				509D4A8D17EBB24E00697056 /* AudioToolbox.framework in Frameworks */,
				509D4A8E17EBB24E00697056 /* AVFoundation.framework in Frameworks */,
				509D4A9017EBB24E00697056 /* Foundation.framework in Frameworks */,
				509D4A9117EBB24E00697056 /* CoreGraphics.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A922753A1517C094001B78AA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				467AF615232881EB00770112 /* CoreMedia.framework in Frameworks */,
				467AF613232881E400770112 /* AVKit.framework in Frameworks */,
				467AF6082321FBED00770112 /* WebKit.framework in Frameworks */,
				D15FDF0829BF426700B1820E /* AuthenticationServices.framework in Frameworks */,
				D1AE3B6B29E6A162002D944E /* FacebookGamingServices in Frameworks */,
				1A37E9DA200DEF120078AF72 /* SystemConfiguration.framework in Frameworks */,
				1ACA99251F550A28002DE225 /* JavaScriptCore.framework in Frameworks */,
				1A499EBD1F0CDDD9004E6AEC /* libicucore.A.tbd in Frameworks */,
				D1AE3B6929E6A162002D944E /* FacebookCore in Frameworks */,
				D1AE3B6729E6A162002D944E /* FacebookBasics in Frameworks */,
				ABE456EB1D34F89000F1F400 /* GameController.framework in Frameworks */,
				ABE456E91D34F88A00F1F400 /* SystemConfiguration.framework in Frameworks */,
				D1AE3B6D29E6A162002D944E /* FacebookLogin in Frameworks */,
				D15FDF0B29BF466500B1820E /* Photos.framework in Frameworks */,
				D1AE3B6529E6A162002D944E /* FacebookAEM in Frameworks */,
				ABE456E71D34F87F00F1F400 /* MobileCoreServices.framework in Frameworks */,
				ABE456E51D34F87A00F1F400 /* CoreFoundation.framework in Frameworks */,
				ABE456E31D34F87300F1F400 /* CFNetwork.framework in Frameworks */,
				294D7D801D0D668F002CE7B7 /* CoreText.framework in Frameworks */,
				4DD5E6B11CDAF65C0046171E /* libcocos2d iOS.a in Frameworks */,
				FA676C761C71AFA200E091E3 /* libiconv.dylib in Frameworks */,
				D14D6EAD29BEFCC90026E67A /* StoreKit.framework in Frameworks */,
				D1B65CBF29CD8B5900210BDE /* ThinkingSDK.framework in Frameworks */,
				D15FDF0029BF3B3B00B1820E /* AppTrackingTransparency.framework in Frameworks */,
				BA3A85EC1A724AE900924D24 /* Security.framework in Frameworks */,
				D6B061241803AB9F0077942B /* CoreMotion.framework in Frameworks */,
				1A82F5FB169AC92500C4B13A /* libsqlite3.dylib in Frameworks */,
				D454520C156E22BD00887EB5 /* libz.dylib in Frameworks */,
				A92275421517C094001B78AA /* QuartzCore.framework in Frameworks */,
				D1AE3B6F29E6A162002D944E /* FacebookShare in Frameworks */,
				A92275441517C094001B78AA /* OpenGLES.framework in Frameworks */,
				A92275461517C094001B78AA /* OpenAL.framework in Frameworks */,
				A92275481517C094001B78AA /* AudioToolbox.framework in Frameworks */,
				A922754A1517C094001B78AA /* AVFoundation.framework in Frameworks */,
				A922754C1517C094001B78AA /* UIKit.framework in Frameworks */,
				A922754E1517C094001B78AA /* Foundation.framework in Frameworks */,
				A92275501517C094001B78AA /* CoreGraphics.framework in Frameworks */,
				ED6D5265263544C8003A20DD /* CoreVideo.framework in Frameworks */,
				50E3168B4BEE3544DA405DE6 /* Pods_jwm_mobile.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		15628F5B15F0F5C2000CF24B /* Resources */ = {
			isa = PBXGroup;
			children = (
				4033CDBB20E4935000DAA37B /* jsb-adapter */,
				1A1A081B18C9DF05005C6854 /* project.json */,
				1A6BF21418C9DB5900FB0E1C /* main.js */,
				C0799CB518BAE62000E9C828 /* res */,
				C03780EA18BEE0E400FE4F13 /* src */,
				4011F9D7212BD7050091CC5B /* Localizable.strings */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		22CC756E21BA5B5C00FBF2F7 /* service */ = {
			isa = PBXGroup;
			children = (
				22CC756F21BA5B5C00FBF2F7 /* SDKDelegate.h */,
			);
			name = service;
			path = ios/service;
			sourceTree = "<group>";
		};
		4DD0DB511CDAF5F40099AD86 /* Products */ = {
			isa = PBXGroup;
			children = (
				4DD0DB561CDAF5F40099AD86 /* libcocos2d Mac.a */,
				4DD0DB581CDAF5F40099AD86 /* libcocos2d iOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		509D4ACA17EBB2BE00697056 /* mac */ = {
			isa = PBXGroup;
			children = (
				051C018621E32AF100D4A347 /* UserConfigMac.debug.xcconfig */,
				051C018521E32AF000D4A347 /* UserConfigMac.release.xcconfig */,
				C06E23CB18CEFE680093C81A /* main.cpp */,
				509D4ACB17EBB2BE00697056 /* Icon.icns */,
				509D4ACC17EBB2BE00697056 /* Info.plist */,
				509D4ACE17EBB2BE00697056 /* Prefix.pch */,
			);
			path = mac;
			sourceTree = "<group>";
		};
		A92275321517C094001B78AA = {
			isa = PBXGroup;
			children = (
				D1B65CBD29CD8B5900210BDE /* lib */,
				D14D6EAE29BEFCD60026E67A /* jwm-mobile.entitlements */,
				28A34C0F225B462E00926306 /* assets */,
				1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */,
				D4545214156E28EF00887EB5 /* Classes */,
				A92275401517C094001B78AA /* Frameworks */,
				D45446CC156DE73F00887EB5 /* ios */,
				509D4ACA17EBB2BE00697056 /* mac */,
				A922753E1517C094001B78AA /* Products */,
				15628F5B15F0F5C2000CF24B /* Resources */,
				E81B8F8ED6EB3D3F16135B2E /* Pods */,
			);
			sourceTree = "<group>";
		};
		A922753E1517C094001B78AA /* Products */ = {
			isa = PBXGroup;
			children = (
				A922753D1517C094001B78AA /* jwm-mobile.app */,
				509D4AAA17EBB24E00697056 /* jwm-desktop.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A92275401517C094001B78AA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D15FDF0A29BF466500B1820E /* Photos.framework */,
				D15FDF0729BF426700B1820E /* AuthenticationServices.framework */,
				D15FDEFF29BF3B3A00B1820E /* AppTrackingTransparency.framework */,
				D14D6EAC29BEFCC90026E67A /* StoreKit.framework */,
				467AF614232881EB00770112 /* CoreMedia.framework */,
				467AF612232881E400770112 /* AVKit.framework */,
				467AF6072321FBED00770112 /* WebKit.framework */,
				1A37E9D9200DEF110078AF72 /* SystemConfiguration.framework */,
				1A37E9DB200DEF190078AF72 /* SystemConfiguration.framework */,
				1ACA99241F550A28002DE225 /* JavaScriptCore.framework */,
				1ACA99221F550A20002DE225 /* JavaScriptCore.framework */,
				1A499EBC1F0CDDD9004E6AEC /* libicucore.A.tbd */,
				1A499EBA1F0CDDC3004E6AEC /* CFNetwork.framework */,
				ABE456EA1D34F89000F1F400 /* GameController.framework */,
				ABE456E81D34F88A00F1F400 /* SystemConfiguration.framework */,
				ABE456E61D34F87F00F1F400 /* MobileCoreServices.framework */,
				ABE456E41D34F87A00F1F400 /* CoreFoundation.framework */,
				ABE456E21D34F87300F1F400 /* CFNetwork.framework */,
				294D7D7F1D0D668F002CE7B7 /* CoreText.framework */,
				4D697BDC1CBD303100A5AF29 /* libjs_static.a */,
				4D697BD91CBD301C00A5AF29 /* libjs_static.a */,
				FA676C771C71AFAB00E091E3 /* libiconv.dylib */,
				FA676C751C71AFA200E091E3 /* libiconv.dylib */,
				BAEE4D831AC40C11003BEB0F /* Security.framework */,
				BA3A85EB1A724AE900924D24 /* Security.framework */,
				BA4E718219EB6E4600932425 /* GameController.framework */,
				BA4E718019EB6E3E00932425 /* MediaPlayer.framework */,
				D6B061231803AB9F0077942B /* CoreMotion.framework */,
				502380DB17EBB88200990C9B /* libcurl.dylib */,
				509D4AEA17EBB82600697056 /* IOKit.framework */,
				509D4AE817EBB82000697056 /* AppKit.framework */,
				509D4AE617EBB81800697056 /* OpenGL.framework */,
				1A96A4F2174A3432008653A9 /* libcurl.a */,
				1A82F5FA169AC92500C4B13A /* libsqlite3.dylib */,
				D454520B156E22BD00887EB5 /* libz.dylib */,
				A92275411517C094001B78AA /* QuartzCore.framework */,
				A92275431517C094001B78AA /* OpenGLES.framework */,
				A92275451517C094001B78AA /* OpenAL.framework */,
				A92275471517C094001B78AA /* AudioToolbox.framework */,
				A92275491517C094001B78AA /* AVFoundation.framework */,
				A922754B1517C094001B78AA /* UIKit.framework */,
				A922754D1517C094001B78AA /* Foundation.framework */,
				A922754F1517C094001B78AA /* CoreGraphics.framework */,
				ED6D5264263544C8003A20DD /* CoreVideo.framework */,
				80FF04FF3442AABA4728DEA3 /* Pods_jwm_mobile.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		D15FDEF329BF389E00B1820E /* apple */ = {
			isa = PBXGroup;
			children = (
				D16B07A82A66344A00FD6E8C /* CHToolKeychain.h */,
				D16B07A92A66344A00FD6E8C /* CHToolKeychain.m */,
				D15FDEF429BF389F00B1820E /* AppleLoginManager.mm */,
				D15FDEF529BF389F00B1820E /* AppleLoginManager.h */,
			);
			name = apple;
			path = ios/apple;
			sourceTree = "<group>";
		};
		D15FDEF729BF38AF00B1820E /* pay */ = {
			isa = PBXGroup;
			children = (
				D15FDEF829BF38AF00B1820E /* EMAppStorePay.m */,
				D15FDEF929BF38AF00B1820E /* EMAppStoreBoot.h */,
				D15FDEFA29BF38AF00B1820E /* EMAppStorePay.h */,
				D15FDEFB29BF38AF00B1820E /* EMAppStoreBoot.m */,
			);
			name = pay;
			path = ios/pay;
			sourceTree = "<group>";
		};
		D1919B802B5668C2000AC56B /* google */ = {
			isa = PBXGroup;
			children = (
				D1919B812B5668E6000AC56B /* GoogleHelper.m */,
				D1919B832B566901000AC56B /* GoogleHelper.h */,
			);
			name = google;
			path = ios/google;
			sourceTree = "<group>";
		};
		D1AD12F629D5A58A00808FA4 /* appsflyer */ = {
			isa = PBXGroup;
			children = (
				D1AD12F729D5A58A00808FA4 /* AppsflyerHelper.h */,
				D1AD12F829D5A58A00808FA4 /* AppsflyerHelper.m */,
			);
			name = appsflyer;
			path = ios/appsflyer;
			sourceTree = "<group>";
		};
		D1B65CBD29CD8B5900210BDE /* lib */ = {
			isa = PBXGroup;
			children = (
				D1B65CBE29CD8B5900210BDE /* ThinkingSDK.framework */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		D1B65CC029CD8B7A00210BDE /* think */ = {
			isa = PBXGroup;
			children = (
				D1B65CC129CD8B7A00210BDE /* CocosCreatorProxyApi.mm */,
				D1B65CC229CD8B7A00210BDE /* CocosCreatorProxyApi.h */,
			);
			name = think;
			path = ios/think;
			sourceTree = "<group>";
		};
		D1F2EB1729C2C293001EAA42 /* facebook */ = {
			isa = PBXGroup;
			children = (
				D1F2EB1829C2C293001EAA42 /* FacebookHelper.m */,
				D1F2EB1929C2C293001EAA42 /* FacebookHelper.h */,
				D1F2EB1A29C2C293001EAA42 /* FaceBookManager.swift */,
			);
			name = facebook;
			path = ios/facebook;
			sourceTree = "<group>";
		};
		D45446CC156DE73F00887EB5 /* ios */ = {
			isa = PBXGroup;
			children = (
				D1919B802B5668C2000AC56B /* google */,
				D18027D52A653509002CC495 /* MessageHelper.h */,
				D18027D42A653508002CC495 /* MessageHelper.m */,
				D103A65029F110F000F7B3A0 /* GoogleService-Info.plist */,
				D1AD12F629D5A58A00808FA4 /* appsflyer */,
				D1B65CC029CD8B7A00210BDE /* think */,
				D1F2EB3029C2CC91001EAA42 /* jwm-mobile-Bridging-Header.h */,
				D1F2EB1729C2C293001EAA42 /* facebook */,
				D15FDEF729BF38AF00B1820E /* pay */,
				D15FDEF329BF389E00B1820E /* apple */,
				D15FDEEF29BF37C500B1820E /* jsbHelp.h */,
				D15FDEF029BF37C500B1820E /* jsbHelp.mm */,
				22CC756E21BA5B5C00FBF2F7 /* service */,
				4011F9D1212BD6750091CC5B /* LaunchScreen.storyboard */,
				1AFFCD7B1F7A5D4F00628F2C /* LaunchScreenBackground.png */,
				1ABC56911F7A005D00826282 /* Images.xcassets */,
				509D4AAB17EBB2AB00697056 /* AppController.h */,
				509D4AAC17EBB2AB00697056 /* AppController.mm */,
				509D4AB717EBB2AB00697056 /* Info.plist */,
				509D4AB817EBB2AB00697056 /* main.m */,
				509D4AB917EBB2AB00697056 /* Prefix.pch */,
				509D4ABA17EBB2AB00697056 /* RootViewController.h */,
				509D4ABB17EBB2AB00697056 /* RootViewController.mm */,
				22CC757021BA5B5C00FBF2F7 /* SDKWrapper.h */,
				22CC756D21BA5B5C00FBF2F7 /* SDKWrapper.m */,
				D13073232B174A9F009820E9 /* FirebaseHelper.m */,
				D13073252B174ABF009820E9 /* FirebaseHelper.h */,
				D153F6D72C7C827100C0F45C /* DeviceHelper.m */,
				D153F6DA2C7C82A300C0F45C /* DeviceHelper.h */,
				D14EF6452CA4370100DEC044 /* InfoPlist.strings */,
			);
			name = ios;
			sourceTree = "<group>";
		};
		D4545214156E28EF00887EB5 /* Classes */ = {
			isa = PBXGroup;
			children = (
				05A8AD8A2449A9F900A65D12 /* NativeConfig.h */,
				D4545215156E28EF00887EB5 /* AppDelegate.cpp */,
				D4545216156E28EF00887EB5 /* AppDelegate.h */,
			);
			name = Classes;
			path = ../Classes;
			sourceTree = "<group>";
		};
		E81B8F8ED6EB3D3F16135B2E /* Pods */ = {
			isa = PBXGroup;
			children = (
				6E1C75C500C1D5EA6528A72B /* Pods-jwm-desktop.debug.xcconfig */,
				C113BDCEA2DC7648E7D112E7 /* Pods-jwm-desktop.release.xcconfig */,
				FE629E463CDF3382B5ACEF86 /* Pods-jwm-mobile.debug.xcconfig */,
				3F01F19A904F6C7742206F4D /* Pods-jwm-mobile.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		509D4A7517EBB24E00697056 /* jwm-desktop */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 509D4AA717EBB24E00697056 /* Build configuration list for PBXNativeTarget "jwm-desktop" */;
			buildPhases = (
				509D4A8017EBB24E00697056 /* Sources */,
				509D4A9317EBB24E00697056 /* Resources */,
				509D4A8217EBB24E00697056 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				4DD5E6AA1CDAF6330046171E /* PBXTargetDependency */,
			);
			name = "jwm-desktop";
			productName = jwm;
			productReference = 509D4AAA17EBB24E00697056 /* jwm-desktop.app */;
			productType = "com.apple.product-type.application";
		};
		A922753C1517C094001B78AA /* jwm-mobile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A92277001517C097001B78AA /* Build configuration list for PBXNativeTarget "jwm-mobile" */;
			buildPhases = (
				AF043511764C0AF639992AA6 /* [CP] Check Pods Manifest.lock */,
				A92275391517C094001B78AA /* Sources */,
				A922753A1517C094001B78AA /* Frameworks */,
				A922753B1517C094001B78AA /* Resources */,
				371C9F6000D61E33409ADEA9 /* [CP] Embed Pods Frameworks */,
				410C537948F75FB860D84B89 /* [CP] Copy Pods Resources */,
				D15379312C19B64B000C1342 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				4DD5E6B01CDAF6520046171E /* PBXTargetDependency */,
			);
			name = "jwm-mobile";
			packageProductDependencies = (
				D1AE3B6429E6A162002D944E /* FacebookAEM */,
				D1AE3B6629E6A162002D944E /* FacebookBasics */,
				D1AE3B6829E6A162002D944E /* FacebookCore */,
				D1AE3B6A29E6A162002D944E /* FacebookGamingServices */,
				D1AE3B6C29E6A162002D944E /* FacebookLogin */,
				D1AE3B6E29E6A162002D944E /* FacebookShare */,
			);
			productName = jwm;
			productReference = A922753D1517C094001B78AA /* jwm-mobile.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A92275341517C094001B78AA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0500;
			};
			buildConfigurationList = A92275371517C094001B78AA /* Build configuration list for PBXProject "jwm" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
				"zh-Hans",
				ja,
				ko,
				"zh-Hant",
				"zh-HK",
				id,
				th,
				vi,
			);
			mainGroup = A92275321517C094001B78AA;
			packageReferences = (
				D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */,
			);
			productRefGroup = A922753E1517C094001B78AA /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 4DD0DB511CDAF5F40099AD86 /* Products */;
					ProjectRef = 1AC6FB34180E9ACB004C840B /* cocos2d_libs.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				A922753C1517C094001B78AA /* jwm-mobile */,
				509D4A7517EBB24E00697056 /* jwm-desktop */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		4DD0DB561CDAF5F40099AD86 /* libcocos2d Mac.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libcocos2d Mac.a";
			remoteRef = 4DD0DB551CDAF5F40099AD86 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4DD0DB581CDAF5F40099AD86 /* libcocos2d iOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libcocos2d iOS.a";
			remoteRef = 4DD0DB571CDAF5F40099AD86 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		509D4A9317EBB24E00697056 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				286B0E9524075FE000095E1A /* assets in Resources */,
				1AC22EDE18CA0E11007112B9 /* project.json in Resources */,
				1AD7E0A918C9DBE3004817A6 /* main.js in Resources */,
				4033CDBD20E4935100DAA37B /* jsb-adapter in Resources */,
				509D4ACF17EBB2BE00697056 /* Icon.icns in Resources */,
				C03780EC18BEE0E400FE4F13 /* src in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A922753B1517C094001B78AA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28A34C10225B462E00926306 /* assets in Resources */,
				4033CDBC20E4935100DAA37B /* jsb-adapter in Resources */,
				1AFFCD7F1F7A5D5400628F2C /* Images.xcassets in Resources */,
				1AFFCD7E1F7A5D5400628F2C /* LaunchScreenBackground.png in Resources */,
				1AE159EC18C9DF3600FCA372 /* project.json in Resources */,
				1AD7E0A818C9DB93004817A6 /* main.js in Resources */,
				C03780EB18BEE0E400FE4F13 /* src in Resources */,
				D103A65129F110F000F7B3A0 /* GoogleService-Info.plist in Resources */,
				4011F9CF212BD6750091CC5B /* LaunchScreen.storyboard in Resources */,
				D14EF6472CA4370100DEC044 /* InfoPlist.strings in Resources */,
				4011F9D5212BD7050091CC5B /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		371C9F6000D61E33409ADEA9 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AppAuth/AppAuth.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseABTesting/FirebaseABTesting.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseAppCheckInterop/FirebaseAppCheckInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseAuth/FirebaseAuth.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework",
				"${BUILT_PRODUCTS_DIR}/FirebasePerformance/FirebasePerformance.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSessions/FirebaseSessions.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework",
				"${BUILT_PRODUCTS_DIR}/GTMAppAuth/GTMAppAuth.framework",
				"${BUILT_PRODUCTS_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleSignIn/GoogleSignIn.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesSwift/Promises.framework",
				"${BUILT_PRODUCTS_DIR}/RecaptchaInterop/RecaptchaInterop.framework",
				"${BUILT_PRODUCTS_DIR}/SAMKeychain/SAMKeychain.framework",
				"${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/AppLovinSDK/AppLovinSDK.framework/AppLovinSDK",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AppAuth.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseABTesting.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseAppCheckInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseAuth.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreInternal.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCrashlytics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseInstallations.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseMessaging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebasePerformance.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfig.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfigInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSessions.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSharedSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GTMAppAuth.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GTMSessionFetcher.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleDataTransport.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleSignIn.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleUtilities.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBLPromises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Promises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RecaptchaInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SAMKeychain.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/nanopb.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AppLovinSDK.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		410C537948F75FB860D84B89 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile-resources.sh",
				"${PODS_ROOT}/Ads-Global/SDK/PAGAdSDK.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Ads-Global/AdsGlobalSDK.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/AppsFlyerFramework/AppsFlyerLib_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/FBAudienceNetwork/FBAudienceNetwork.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/GoogleMobileAdsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUserMessagingPlatform/UserMessagingPlatformResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/IronSourceAdMobAdapter/ISAdMobResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/IronSourceSDK/IronSourcePrivacyInfo.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/PAGAdSDK.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AdsGlobalSDK.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AppsFlyerLib_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBAudienceNetwork.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMobileAdsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/UserMessagingPlatformResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ISAdMobResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/IronSourcePrivacyInfo.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-jwm-mobile/Pods-jwm-mobile-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AF043511764C0AF639992AA6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-jwm-mobile-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D15379312C19B64B000C1342 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		509D4A8017EBB24E00697056 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				509D4A8117EBB24E00697056 /* AppDelegate.cpp in Sources */,
				C06E23CC18CEFE680093C81A /* main.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A92275391517C094001B78AA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D15FDEF629BF389F00B1820E /* AppleLoginManager.mm in Sources */,
				509D4AC917EBB2AB00697056 /* RootViewController.mm in Sources */,
				D4545227156E28EF00887EB5 /* AppDelegate.cpp in Sources */,
				D15FDEF129BF37C500B1820E /* jsbHelp.mm in Sources */,
				D1B65CC329CD8B7A00210BDE /* CocosCreatorProxyApi.mm in Sources */,
				D15FDEFC29BF38AF00B1820E /* EMAppStorePay.m in Sources */,
				D18027D62A653509002CC495 /* MessageHelper.m in Sources */,
				D13073242B174A9F009820E9 /* FirebaseHelper.m in Sources */,
				D1919B822B5668E6000AC56B /* GoogleHelper.m in Sources */,
				D1F2EB1B29C2C293001EAA42 /* FacebookHelper.m in Sources */,
				509D4AC817EBB2AB00697056 /* main.m in Sources */,
				D1AD12F929D5A58A00808FA4 /* AppsflyerHelper.m in Sources */,
				22CC757121BA5B5C00FBF2F7 /* SDKWrapper.m in Sources */,
				509D4ABC17EBB2AB00697056 /* AppController.mm in Sources */,
				D1F2EB1C29C2C294001EAA42 /* FaceBookManager.swift in Sources */,
				D15FDEFD29BF38AF00B1820E /* EMAppStoreBoot.m in Sources */,
				D16B07AA2A66344A00FD6E8C /* CHToolKeychain.m in Sources */,
				D153F6D82C7C827100C0F45C /* DeviceHelper.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4DD5E6AA1CDAF6330046171E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "libcocos2d Mac";
			targetProxy = 4DD5E6A91CDAF6330046171E /* PBXContainerItemProxy */;
		};
		4DD5E6B01CDAF6520046171E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "libcocos2d iOS";
			targetProxy = 4DD5E6AF1CDAF6520046171E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		4011F9D1212BD6750091CC5B /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4011F9D2212BD6810091CC5B /* Base */,
				22F236C12E65A48500463DCC /* vi */,
			);
			name = LaunchScreen.storyboard;
			path = ios;
			sourceTree = "<group>";
		};
		4011F9D7212BD7050091CC5B /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				4011F9D8212BD7150091CC5B /* Base */,
				4011F9D9212BD73A0091CC5B /* zh-Hans */,
				D14EF63E2CA434CD00DEC044 /* ja */,
				D14EF6422CA435E100DEC044 /* ko */,
				D14EF6432CA435F200DEC044 /* zh-Hant */,
				D14EF6442CA435FC00DEC044 /* zh-HK */,
				D10447562CA56C5900E77AD4 /* id */,
				D10447582CA56C7700E77AD4 /* th */,
				22F236C32E65A48600463DCC /* vi */,
			);
			name = Localizable.strings;
			path = ios;
			sourceTree = "<group>";
		};
		D14EF6452CA4370100DEC044 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				D14EF6462CA4370100DEC044 /* en */,
				D14EF6482CA4373400DEC044 /* zh-Hant */,
				D14EF6492CA4374100DEC044 /* zh-Hans */,
				D14EF64A2CA4374600DEC044 /* zh-HK */,
				D14EF64B2CA4374E00DEC044 /* English */,
				D14EF64C2CA4375100DEC044 /* ja */,
				D14EF64D2CA4375700DEC044 /* ko */,
				D10447552CA56C5700E77AD4 /* id */,
				D10447572CA56C7500E77AD4 /* th */,
				22F236C22E65A48600463DCC /* vi */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		509D4AA817EBB24E00697056 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 051C018621E32AF100D4A347 /* UserConfigMac.debug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = mac/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					CC_TARGET_OS_MAC,
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = "";
				INFOPLIST_FILE = mac/Info.plist;
				LIBRARY_SEARCH_PATHS = "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/libs";
				OTHER_LDFLAGS = "";
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/platform/mac",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/glfw3/include/mac",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/spidermonkey",
				);
			};
			name = Debug;
		};
		509D4AA917EBB24E00697056 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = mac/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					CC_TARGET_OS_MAC,
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = "";
				INFOPLIST_FILE = mac/Info.plist;
				LIBRARY_SEARCH_PATHS = "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/libs";
				OTHER_LDFLAGS = "";
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/platform/mac",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/glfw3/include/mac",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/spidermonkey",
				);
			};
			name = Release;
		};
		A92276FE1517C097001B78AA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"COCOS2D_DEBUG=1",
					USE_FILE32API,
					COCOS2D_JAVASCRIPT,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				USER_HEADER_SEARCH_PATHS = (
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/base",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/physics",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/math/kazmath",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/2d",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/gui",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/network",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/audio/include",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/editor-support",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/extensions",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/sources",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/chipmunk/include/chipmunk",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/v8",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/scripting/js-bindings/auto",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/scripting/js-bindings/manual",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/renderer",
				);
			};
			name = Debug;
		};
		A92276FF1517C097001B78AA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_PREPROCESSOR_DEFINITIONS = (
					NDEBUG,
					USE_FILE32API,
					COCOS2D_JAVASCRIPT,
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				PRODUCT_NAME = "$(TARGET_NAME)";
				USER_HEADER_SEARCH_PATHS = (
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/base",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/physics",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/math/kazmath",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/2d",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/gui",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/network",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/audio/include",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/editor-support",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/extensions",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/sources",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/chipmunk/include/chipmunk",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/mac/include/v8",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/scripting/js-bindings/auto",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/scripting/js-bindings/manual",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/renderer",
				);
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A92277011517C097001B78AA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FE629E463CDF3382B5ACEF86 /* Pods-jwm-mobile.debug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = "jwm-mobile.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COMPRESS_PNG_FILES = NO;
				CURRENT_PROJECT_VERSION = 2.0;
				DEVELOPMENT_TEAM = YT86BQG28K;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/lib",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = ios/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					CC_TARGET_OS_IPHONE,
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ios/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/libs",
				);
				MARKETING_VERSION = 4.0.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = twgame.global.acers;
				SDKROOT = iphoneos;
				STRIP_PNG_TEXT = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "jwm-mobile-Bridging-Header.h";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/platform/ios",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/plugin/jsbindings/auto",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/plugin/jsbindings/manual",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/include",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/include/spidermonkey",
				);
			};
			name = Debug;
		};
		A92277021517C097001B78AA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3F01F19A904F6C7742206F4D /* Pods-jwm-mobile.release.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = "jwm-mobile.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COMPRESS_PNG_FILES = NO;
				CURRENT_PROJECT_VERSION = 2.0;
				DEVELOPMENT_TEAM = YT86BQG28K;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/lib",
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = ios/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					CC_TARGET_OS_IPHONE,
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = ios/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/libs",
				);
				MARKETING_VERSION = 4.0.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = twgame.global.acers;
				SDKROOT = iphoneos;
				STRIP_PNG_TEXT = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "jwm-mobile-Bridging-Header.h";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/platform/ios",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/plugin/jsbindings/auto",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/plugin/jsbindings/manual",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/include",
					"/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external/ios/include/spidermonkey",
				);
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		509D4AA717EBB24E00697056 /* Build configuration list for PBXNativeTarget "jwm-desktop" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				509D4AA817EBB24E00697056 /* Debug */,
				509D4AA917EBB24E00697056 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A92275371517C094001B78AA /* Build configuration list for PBXProject "jwm" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A92276FE1517C097001B78AA /* Debug */,
				A92276FF1517C097001B78AA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A92277001517C097001B78AA /* Build configuration list for PBXNativeTarget "jwm-mobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A92277011517C097001B78AA /* Debug */,
				A92277021517C097001B78AA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/facebook/facebook-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 17.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		D1AE3B6429E6A162002D944E /* FacebookAEM */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookAEM;
		};
		D1AE3B6629E6A162002D944E /* FacebookBasics */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookBasics;
		};
		D1AE3B6829E6A162002D944E /* FacebookCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookCore;
		};
		D1AE3B6A29E6A162002D944E /* FacebookGamingServices */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookGamingServices;
		};
		D1AE3B6C29E6A162002D944E /* FacebookLogin */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookLogin;
		};
		D1AE3B6E29E6A162002D944E /* FacebookShare */ = {
			isa = XCSwiftPackageProductDependency;
			package = D1AE3B6329E6A162002D944E /* XCRemoteSwiftPackageReference "facebook-ios-sdk" */;
			productName = FacebookShare;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A92275341517C094001B78AA /* Project object */;
}
