
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/ad/InlandNativeRewardAd.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e195bQjZo5EFb4H4dyHJB3y', 'InlandNativeRewardAd');
// app/script/common/ad/InlandNativeRewardAd.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../constant/Enums");
var JsbEvent_1 = require("../event/JsbEvent");
var JsbHelper_1 = require("../helper/JsbHelper");
var ViewHelper_1 = require("../helper/ViewHelper");
var BaseRewardAd_1 = require("./BaseRewardAd");
var WAIT_CACHE_TIME = 5;
// APP视频广告 国内，暂时还没用到
var InlandNativeRewardAd = /** @class */ (function (_super) {
    __extends(InlandNativeRewardAd, _super);
    function InlandNativeRewardAd() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ready = false;
        _this.waitTime = 0;
        _this.waitCache = true;
        _this.loadStartTime = 0;
        _this.loadCacheStartTime = 0;
        _this.loadReqId = 0;
        _this.expireCheckTime = 0;
        _this.ecpmLevel = '';
        _this.isEcpmHigh = true;
        _this.ecpmCount = 0;
        return _this;
    }
    InlandNativeRewardAd.prototype.create = function () {
        this.inited = true;
        JsbHelper_1.jsbHelper.on(JsbEvent_1.default.REWARD_AD_ON_ERROR, this.onError, this);
    };
    // 加载失败
    InlandNativeRewardAd.prototype.onError = function (_a) {
        var status = _a.status, errcode = _a.errcode;
        console.error('ad error', status);
        this.ready = false;
        this.state = Enums_1.AdState.WAIT;
        this.reloadTime = 1;
        this.noSuitAdCount += 0.1;
        if (errcode !== undefined) {
            var key = 'id' + errcode;
            // reportHelper.reportEvent("reward_ad_load_error", { [key]: 1 })
        }
    };
    InlandNativeRewardAd.prototype.isReady = function () {
        if (cc.sys.isBrowser) {
            return true;
        }
        else if (!ut.isMobile()) {
            return false;
        }
        else if (this.waitCache) { //模式1，只需等待加载完成即可ready, show的时候再去等缓存完成 
            return this.ready;
        }
        else {
            return this.isCacheReady() && this.ready; //模式2, 需要等待加载和缓存都完成
        }
    };
    InlandNativeRewardAd.prototype.update = function (dt) {
        var _this = this;
        if (!this.inited) {
            this.create();
        }
        if (this.state !== Enums_1.AdState.LOADING && this.state !== Enums_1.AdState.LOAD_SUCCESS && this.state !== Enums_1.AdState.WAIT) {
            this.state = Enums_1.AdState.LOADING;
            this.loadStartTime = Date.now();
            var id_1 = ++this.loadReqId;
            // reportHelper.reportGDTEvent("reward_ad_preload", null, false)
            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.LOAD_REWARD_VIDEO_AD, function (res) {
                if (_this.loadReqId !== id_1) {
                    return;
                }
                var code = res.status;
                if (code === '0') {
                    _this.state = Enums_1.AdState.LOAD_SUCCESS;
                    _this.ready = true;
                    _this.noSuitAdCount = 0;
                    var now = Date.now();
                    // reportHelper.reportEvent('reward_ad_load_time', { passTime: now - this.loadStartTime })
                    // reportHelper.reportGDTEvent('reward_ad_load_success', null, false)
                    _this.loadCacheStartTime = now;
                    _this.ecpmLevel = res.ecpmLevel;
                    if (_this.ecpmLevel) {
                        var levelMap = {};
                        levelMap[_this.ecpmLevel] = 1;
                        // reportHelper.reportEvent('ad_ecpm_lv', levelMap)
                    }
                    // console.log('reward ecpm is ', this.ecpmLevel)
                    if (_this.getEcpmLevel() > 6) {
                        _this.isEcpmHigh = false;
                    }
                    else {
                        _this.isEcpmHigh = true;
                    }
                    _this.ecpmCount = 0;
                }
                else if (code === '-1') {
                    _this.reloadTime = 1;
                    _this.noSuitAdCount += 0.1;
                }
                else if (code === '-3') { //网络问题
                    _this.reloadTime = 4;
                }
                else if (code === '-4') { //广告加载频繁
                    _this.reloadTime = 20;
                    _this.noSuitAdCount += 2;
                }
                else if (code === '-5') {
                    _this.reloadTime = 120;
                    if (res.errcode == '5005') { //广告请求量或者消耗等超过日限额，请明天再请求广告
                        _this.noSuitAdCount += 1200;
                    }
                    else {
                        _this.noSuitAdCount += 12;
                    }
                }
                else if (code === '-6') { //匹配不到合适广告，多次重试影响流量
                    _this.reloadTime = 6000;
                    _this.noSuitAdCount += 60;
                }
                else if (code === '-7') { //次日才重新拉取
                    _this.reloadTime = 120 + _this.getFixTimeSurplyTime(Date.now(), -8);
                }
                else {
                    logger.info('load ads unknow code is ' + code + ' ' + typeof (code));
                    _this.noSuitAdCount += 1;
                    _this.reloadTime = 10;
                }
                if (code !== '0') {
                    _this.state = Enums_1.AdState.WAIT;
                    _this.ready = false;
                    if (res.errcode !== undefined) {
                        var key = 'id' + res.errcode;
                        // reportHelper.reportEvent('reward_ad_load_error', { [key]: 1 })
                    }
                }
            });
        }
        else if (this.state === Enums_1.AdState.WAIT) {
            this.waitTime += dt;
            if (this.waitTime >= this.reloadTime) {
                this.waitTime = 0;
                this.state = Enums_1.AdState.LOAD_FAIL;
            }
        }
        if (this.ready && this.loadCacheStartTime > 0) {
            if (this.isCacheReady()) {
                this.state = Enums_1.AdState.LOAD_SUCCESS; //成功缓存，重置一下状态
                // reportHelper.reportEvent("reward_ad_cache_time", { passTime: gameHelper.now() - this.loadCacheStartTime })
                this.loadCacheStartTime = -1;
            }
        }
        if (this.state === Enums_1.AdState.LOAD_SUCCESS) {
            this.expireCheckTime += dt;
            if (this.expireCheckTime > 10) {
                this.expireCheckTime = 0;
                var isVaild = this.isAdVaild();
                if (!isVaild) {
                    this.state = Enums_1.AdState.LOAD_FAIL;
                }
            }
        }
    };
    InlandNativeRewardAd.prototype.show = function () {
        return __awaiter(this, void 0, void 0, function () {
            var now, res, error_1, code, passTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.pause();
                        now = Date.now();
                        res = null;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.SHOW_REWARD_VIDEO_AD)];
                    case 2:
                        res = _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        logger.error(error_1);
                        return [3 /*break*/, 4];
                    case 4:
                        this.resume();
                        code = res.status;
                        if (code === '0') {
                            if (res.duration && typeof res.duration === 'string') {
                                res.duration = parseInt(res.duration);
                            }
                            if (res.duration && !isNaN(res.duration)) {
                                res.duration = Math.min(res.duration, 20);
                                passTime = Date.now() - now;
                                if (passTime < res.duration * 0.7 * ut.Time.Second) { //防作弊 播放不足70%
                                    this.state = Enums_1.AdState.PLAY_FAIL;
                                    this.ready = false;
                                    return [2 /*return*/, false];
                                }
                            }
                            this.state = Enums_1.AdState.PLAY_SUCCESS;
                            this.ready = false;
                            this.waitCache = true; //播完之后，重新进入模式1
                            // reportHelper.reportEvent('reward_ad_show', { code })
                            return [2 /*return*/, true];
                        }
                        else if (code === '-1' || code === '-101') {
                            this.state = Enums_1.AdState.PLAY_FAIL;
                            this.ready = false;
                            // reportHelper.reportEvent('reward_ad_show', { code })
                            return [2 /*return*/, false];
                        }
                        else {
                            this.state = Enums_1.AdState.PLAY_FAIL;
                            this.ready = false;
                            logger.info('show rewardAD unknow code is ' + code + ' ' + typeof (code));
                            // reportHelper.reportEvent('reward_ad_show', { code })
                            return [2 /*return*/, false];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    InlandNativeRewardAd.prototype.waitCacheReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isReady, now;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.waitCache) {
                            return [2 /*return*/, true];
                        }
                        isReady = this.isCacheReady();
                        now = Date.now();
                        if (!!isReady) return [3 /*break*/, 4];
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        _a.label = 1;
                    case 1:
                        if (!!isReady) return [3 /*break*/, 3];
                        return [4 /*yield*/, ut.wait(0.05)];
                    case 2:
                        _a.sent();
                        isReady = this.isCacheReady();
                        if (Date.now() - now >= WAIT_CACHE_TIME * ut.Time.Second) { //超时
                            return [3 /*break*/, 3];
                        }
                        return [3 /*break*/, 1];
                    case 3:
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        _a.label = 4;
                    case 4:
                        // 如果等待缓存失败，切换为模式2
                        if (!isReady) {
                            this.waitCache = false;
                            this.state = Enums_1.AdState.WAIT; //等30s，如果还缓存不成功，重新加载
                            this.reloadTime = 30;
                        }
                        return [2 /*return*/, isReady];
                }
            });
        });
    };
    InlandNativeRewardAd.prototype.isCacheReady = function () {
        var isVaild = true;
        if (ut.isIos()) {
            isVaild = jsb.reflection.callStaticMethod('jsbHelp', 'isRewardVideoADReady');
        }
        else if (ut.isAndroid()) {
            isVaild = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'isRewardVideoADReady', '()Z');
        }
        return isVaild;
    };
    InlandNativeRewardAd.prototype.isAdVaild = function () {
        var isVaild = true;
        if (ut.isIos()) {
            isVaild = jsb.reflection.callStaticMethod('jsbHelp', 'isRewardVideoADVaild');
        }
        else if (ut.isAndroid()) {
            isVaild = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'isRewardVideoADVaild', '()Z');
        }
        return isVaild;
    };
    InlandNativeRewardAd.prototype.getEcpmLevel = function () {
        if (!this.ecpmLevel) {
            return 100;
        }
        var val = Number(this.ecpmLevel.substring(5));
        return !isNaN(val) ? val : 100;
    };
    InlandNativeRewardAd.prototype.reLoad = function () {
        this.state = Enums_1.AdState.WAIT;
        this.ready = false;
        this.reloadTime = 5;
        this.noSuitAdCount += 0.5;
    };
    return InlandNativeRewardAd;
}(BaseRewardAd_1.default));
exports.default = InlandNativeRewardAd;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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