
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SeasonInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '67f90+FLupFcaVAYIPyw91R', 'SeasonInfoPnlCtrl');
// app/script/view/common/SeasonInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AdaptWidthCmpt_1 = require("../cmpt/AdaptWidthCmpt");
var ccclass = cc._decorator.ccclass;
var SeasonInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(SeasonInfoPnlCtrl, _super);
    function SeasonInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.seasonNode_ = null; // path://root/title/season_n
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.timeNode_ = null; // path://root/title/time_n
        _this.policyNode_ = null; // path://root/policy_nbe_n
        _this.effectsNode_ = null; // path://root/effects_n
        _this.windCondNode_ = null; // path://root/wind_cond_n
        _this.gameTimeNode_ = null; // path://root/game_time_n
        //@end
        _this.WIN_COND_TYPE_DESC_COUNT = { 1: 3, 2: 5, 3: 2 };
        return _this;
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    SeasonInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.CHANGE_SEASON] = this.onChangeSeason, _a.enter = true, _a),
        ];
    };
    SeasonInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SeasonInfoPnlCtrl.prototype.onEnter = function () {
        var _this = this;
        var season = GameHelper_1.gameHpr.world.getSeason();
        this.seasonNode_.Component(cc.MultiFrame).setFrame(season.type);
        this.titleLbl_.setLocaleKey('ui.season_' + season.type);
        // 季节政策
        var policys = GameHelper_1.gameHpr.getSeasonPolicies();
        this.policyNode_.children.forEach(function (it, i) {
            var policy = it.Data = policys[i];
            var icon = it.Swih(policy ? 'icon' : 'lock')[0];
            if (policy) {
                ResHelper_1.resHelper.loadPolicyIcon(policy.id, icon, _this.key);
            }
        });
        // 效果
        var effects = season.getEffectDescTextByType(season.type);
        if (this.effectsNode_.active = !!effects.length) {
            this.effectsNode_.Items(effects, function (it, data) {
                var _a;
                (_a = it.Child('val')).setLocaleKey.apply(_a, __spread([data.key], data.params));
            });
        }
        // 时间
        if (this.timeNode_.active = season.type < 3) {
            this.titleLbl_._forceUpdateRenderData();
            var maxWidth = this.titleLbl_.node.parent.width - this.titleLbl_.node.width - 20;
            this.timeNode_.Component(AdaptWidthCmpt_1.default).setMaxWidth(maxWidth);
            var seasonTextKey_1 = 'ui.season_' + (season.type + 1);
            this.timeNode_.Child('desc', cc.Label).font = assetsMgr.getFont('f_m');
            this.timeNode_.Child('desc', cc.LabelTimer).setFormat(function (time) {
                return assetsMgr.lang('ui.next_season_desc', ut.millisecondFormat(time * 1000, 'h:mm:ss'), seasonTextKey_1);
            }).run(season.getSurplusTime() * 0.001);
        }
        // 获胜规则
        var isCun = season.type <= 0, winType = GameHelper_1.gameHpr.world.getWinCondType();
        var cond = this.windCondNode_.Swih(isCun ? '0' : '1')[0];
        if (isCun) {
            cond.Child('val').setLocaleKey(winType === Enums_1.WinCondType.KARMIC_MAHJONG ? 'ui.season_desc_x_3' : 'ui.season_desc_x');
        }
        else {
            cond.Child('val').setLocaleKey('ui.win_cond_desc', 'ui.win_cond_' + winType);
        }
        // 战场时间
        this.gameTimeNode_.Child('run_time/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToString(GameHelper_1.gameHpr.world.getServerRunTime()));
        this.gameTimeNode_.Child('server_time/val', cc.LabelTimer).setEndTime(ut.MAX_VALUE).setFormat(function (time) { return ut.dateFormat('MM/dd hh:mm:ss', time * 1000); }).run(GameHelper_1.gameHpr.getServerNowTimeByZoneOffset() * 0.001);
    };
    SeasonInfoPnlCtrl.prototype.onRemove = function () {
    };
    SeasonInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/policy_nbe_n
    SeasonInfoPnlCtrl.prototype.onClickPolicy = function (event, _data) {
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book');
        }
        else {
            ViewHelper_1.viewHelper.showAlert('ui.lv_unlock_new', { params: ['ui.season_' + event.target.name, 'ui.ceri_type_name_1'] });
        }
    };
    // path://root/wind_cond_n/1/help_be
    SeasonInfoPnlCtrl.prototype.onClickHelp = function (event, data) {
        var winType = GameHelper_1.gameHpr.world.getWinCondType(), list = [];
        var count = this.WIN_COND_TYPE_DESC_COUNT[winType] || 3;
        for (var i = 0; i < count; i++) {
            if (winType === Enums_1.WinCondType.TERRITORY_DISPUTE && i === 0) {
                list.push({ key: "ui.win_rule_desc_" + winType + "_" + i, params: [GameHelper_1.gameHpr.world.getWinCondValue()] });
            }
            else {
                list.push({ key: "ui.win_rule_desc_" + winType + "_" + i });
            }
        }
        ViewHelper_1.viewHelper.showDescInfo(list, 'ui.title_win_cond_' + winType);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    SeasonInfoPnlCtrl.prototype.onChangeSeason = function () {
        this.onEnter();
    };
    SeasonInfoPnlCtrl = __decorate([
        ccclass
    ], SeasonInfoPnlCtrl);
    return SeasonInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SeasonInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2Vhc29uSW5mb1BubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxREFBMEQ7QUFDMUQsMERBQXFEO0FBQ3JELDZEQUF5RDtBQUN6RCwyREFBMEQ7QUFDMUQsNkRBQTREO0FBQzVELHlEQUFvRDtBQUU1QyxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUErQyxxQ0FBYztJQUE3RDtRQUFBLHFFQTBHQztRQXhHRywwQkFBMEI7UUFDbEIsaUJBQVcsR0FBWSxJQUFJLENBQUEsQ0FBQyw2QkFBNkI7UUFDekQsZUFBUyxHQUFhLElBQUksQ0FBQSxDQUFDLDRCQUE0QjtRQUN2RCxlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMsMkJBQTJCO1FBQ3JELGlCQUFXLEdBQVksSUFBSSxDQUFBLENBQUMsMkJBQTJCO1FBQ3ZELGtCQUFZLEdBQVksSUFBSSxDQUFBLENBQUMsd0JBQXdCO1FBQ3JELG1CQUFhLEdBQVksSUFBSSxDQUFBLENBQUMsMEJBQTBCO1FBQ3hELG1CQUFhLEdBQVksSUFBSSxDQUFBLENBQUMsMEJBQTBCO1FBQ2hFLE1BQU07UUFFVyw4QkFBd0IsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7O1FBNkZoRSxpSEFBaUg7SUFDckgsQ0FBQztJQTVGVSwyQ0FBZSxHQUF0Qjs7UUFDSSxPQUFPO3NCQUNELEdBQUMsbUJBQVMsQ0FBQyxhQUFhLElBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxRQUFLLEdBQUUsSUFBSTtTQUNoRSxDQUFBO0lBQ0wsQ0FBQztJQUVZLG9DQUFRLEdBQXJCOzs7Ozs7S0FDQztJQUVNLG1DQUFPLEdBQWQ7UUFBQSxpQkEwQ0M7UUF6Q0csSUFBTSxNQUFNLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFFLENBQUE7UUFDeEMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDL0QsSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsWUFBWSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUN2RCxPQUFPO1FBQ1AsSUFBTSxPQUFPLEdBQUcsb0JBQU8sQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1FBQzNDLElBQUksQ0FBQyxXQUFXLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ3BDLElBQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxJQUFJLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ25DLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ2pELElBQUksTUFBTSxFQUFFO2dCQUNSLHFCQUFTLENBQUMsY0FBYyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEVBQUUsSUFBSSxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTthQUN0RDtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsS0FBSztRQUNMLElBQU0sT0FBTyxHQUFHLE1BQU0sQ0FBQyx1QkFBdUIsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDM0QsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sRUFBRTtZQUM3QyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBSTs7Z0JBQ3RDLENBQUEsS0FBQSxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBLENBQUMsWUFBWSxxQkFBQyxJQUFJLENBQUMsR0FBRyxHQUFLLElBQUksQ0FBQyxNQUFNLEdBQUM7WUFDMUQsQ0FBQyxDQUFDLENBQUE7U0FDTDtRQUNELEtBQUs7UUFDTCxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLE1BQU0sQ0FBQyxJQUFJLEdBQUcsQ0FBQyxFQUFFO1lBQ3pDLElBQUksQ0FBQyxTQUFTLENBQUMsc0JBQXNCLEVBQUUsQ0FBQTtZQUN2QyxJQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUE7WUFDbEYsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsd0JBQWMsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsQ0FBQTtZQUM5RCxJQUFNLGVBQWEsR0FBRyxZQUFZLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQ3RELElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDdEUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxTQUFTLENBQUMsVUFBQyxJQUFZO2dCQUMvRCxPQUFPLFNBQVMsQ0FBQyxJQUFJLENBQUMscUJBQXFCLEVBQUUsRUFBRSxDQUFDLGlCQUFpQixDQUFDLElBQUksR0FBRyxJQUFJLEVBQUUsU0FBUyxDQUFDLEVBQUUsZUFBYSxDQUFDLENBQUE7WUFDN0csQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxjQUFjLEVBQUUsR0FBRyxLQUFLLENBQUMsQ0FBQTtTQUMxQztRQUNELE9BQU87UUFDUCxJQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsSUFBSSxJQUFJLENBQUMsRUFBRSxPQUFPLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDeEUsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQzFELElBQUksS0FBSyxFQUFFO1lBQ1AsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsT0FBTyxLQUFLLG1CQUFXLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtTQUNySDthQUFNO1lBQ0gsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsa0JBQWtCLEVBQUUsY0FBYyxHQUFHLE9BQU8sQ0FBQyxDQUFBO1NBQy9FO1FBQ0QsT0FBTztRQUNQLElBQUksQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLGNBQWMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxTQUFTLEVBQUUsb0JBQU8sQ0FBQyxtQkFBbUIsQ0FBQyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDLENBQUMsQ0FBQTtRQUMvSCxJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQyxTQUFTLENBQUMsQ0FBQyxTQUFTLENBQUMsVUFBQSxJQUFJLElBQUksT0FBQSxFQUFFLENBQUMsVUFBVSxDQUFDLGdCQUFnQixFQUFFLElBQUksR0FBRyxJQUFJLENBQUMsRUFBNUMsQ0FBNEMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxvQkFBTyxDQUFDLDRCQUE0QixFQUFFLEdBQUcsS0FBSyxDQUFDLENBQUE7SUFDM00sQ0FBQztJQUVNLG9DQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sbUNBQU8sR0FBZDtJQUNBLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDJCQUEyQjtJQUMzQix5Q0FBYSxHQUFiLFVBQWMsS0FBMEIsRUFBRSxLQUFhO1FBQ25ELElBQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQzlCLElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMsc0JBQXNCLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQTtTQUM5RDthQUFNO1lBQ0gsdUJBQVUsQ0FBQyxTQUFTLENBQUMsa0JBQWtCLEVBQUUsRUFBRSxNQUFNLEVBQUUsQ0FBQyxZQUFZLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUscUJBQXFCLENBQUMsRUFBRSxDQUFDLENBQUE7U0FDbEg7SUFDTCxDQUFDO0lBRUQsb0NBQW9DO0lBQ3BDLHVDQUFXLEdBQVgsVUFBWSxLQUEwQixFQUFFLElBQVk7UUFDaEQsSUFBTSxPQUFPLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsY0FBYyxFQUFFLEVBQUUsSUFBSSxHQUFHLEVBQUUsQ0FBQTtRQUN6RCxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsd0JBQXdCLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3pELEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxLQUFLLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDNUIsSUFBSSxPQUFPLEtBQUssbUJBQVcsQ0FBQyxpQkFBaUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO2dCQUN0RCxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLHNCQUFvQixPQUFPLFNBQUksQ0FBRyxFQUFFLE1BQU0sRUFBRSxDQUFDLG9CQUFPLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFBO2FBQ3BHO2lCQUFNO2dCQUNILElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsc0JBQW9CLE9BQU8sU0FBSSxDQUFHLEVBQUUsQ0FBQyxDQUFBO2FBQ3pEO1NBQ0o7UUFDRCx1QkFBVSxDQUFDLFlBQVksQ0FBQyxJQUFJLEVBQUUsb0JBQW9CLEdBQUcsT0FBTyxDQUFDLENBQUE7SUFDakUsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFekcsMENBQWMsR0FBdEI7UUFDSSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7SUFDbEIsQ0FBQztJQXhHZ0IsaUJBQWlCO1FBRHJDLE9BQU87T0FDYSxpQkFBaUIsQ0EwR3JDO0lBQUQsd0JBQUM7Q0ExR0QsQUEwR0MsQ0ExRzhDLEVBQUUsQ0FBQyxXQUFXLEdBMEc1RDtrQkExR29CLGlCQUFpQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFdpbkNvbmRUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBBZGFwdFdpZHRoQ21wdCBmcm9tIFwiLi4vY21wdC9BZGFwdFdpZHRoQ21wdFwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTZWFzb25JbmZvUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSBzZWFzb25Ob2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvdGl0bGUvc2Vhc29uX25cbiAgICBwcml2YXRlIHRpdGxlTGJsXzogY2MuTGFiZWwgPSBudWxsIC8vIHBhdGg6Ly9yb290L3RpdGxlL3RpdGxlX2xcbiAgICBwcml2YXRlIHRpbWVOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvdGl0bGUvdGltZV9uXG4gICAgcHJpdmF0ZSBwb2xpY3lOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvcG9saWN5X25iZV9uXG4gICAgcHJpdmF0ZSBlZmZlY3RzTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2VmZmVjdHNfblxuICAgIHByaXZhdGUgd2luZENvbmROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Qvd2luZF9jb25kX25cbiAgICBwcml2YXRlIGdhbWVUaW1lTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2dhbWVfdGltZV9uXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIHJlYWRvbmx5IFdJTl9DT05EX1RZUEVfREVTQ19DT1VOVCA9IHsgMTogMywgMjogNSwgMzogMiB9XG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkNIQU5HRV9TRUFTT05dOiB0aGlzLm9uQ2hhbmdlU2Vhc29uLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICBdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKCkge1xuICAgICAgICBjb25zdCBzZWFzb24gPSBnYW1lSHByLndvcmxkLmdldFNlYXNvbigpXG4gICAgICAgIHRoaXMuc2Vhc29uTm9kZV8uQ29tcG9uZW50KGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKHNlYXNvbi50eXBlKVxuICAgICAgICB0aGlzLnRpdGxlTGJsXy5zZXRMb2NhbGVLZXkoJ3VpLnNlYXNvbl8nICsgc2Vhc29uLnR5cGUpXG4gICAgICAgIC8vIOWto+iKguaUv+etllxuICAgICAgICBjb25zdCBwb2xpY3lzID0gZ2FtZUhwci5nZXRTZWFzb25Qb2xpY2llcygpXG4gICAgICAgIHRoaXMucG9saWN5Tm9kZV8uY2hpbGRyZW4uZm9yRWFjaCgoaXQsIGkpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHBvbGljeSA9IGl0LkRhdGEgPSBwb2xpY3lzW2ldXG4gICAgICAgICAgICBjb25zdCBpY29uID0gaXQuU3dpaChwb2xpY3kgPyAnaWNvbicgOiAnbG9jaycpWzBdXG4gICAgICAgICAgICBpZiAocG9saWN5KSB7XG4gICAgICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRQb2xpY3lJY29uKHBvbGljeS5pZCwgaWNvbiwgdGhpcy5rZXkpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICAgIC8vIOaViOaenFxuICAgICAgICBjb25zdCBlZmZlY3RzID0gc2Vhc29uLmdldEVmZmVjdERlc2NUZXh0QnlUeXBlKHNlYXNvbi50eXBlKVxuICAgICAgICBpZiAodGhpcy5lZmZlY3RzTm9kZV8uYWN0aXZlID0gISFlZmZlY3RzLmxlbmd0aCkge1xuICAgICAgICAgICAgdGhpcy5lZmZlY3RzTm9kZV8uSXRlbXMoZWZmZWN0cywgKGl0LCBkYXRhKSA9PiB7XG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ3ZhbCcpLnNldExvY2FsZUtleShkYXRhLmtleSwgLi4uZGF0YS5wYXJhbXMpXG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICAgIC8vIOaXtumXtFxuICAgICAgICBpZiAodGhpcy50aW1lTm9kZV8uYWN0aXZlID0gc2Vhc29uLnR5cGUgPCAzKSB7XG4gICAgICAgICAgICB0aGlzLnRpdGxlTGJsXy5fZm9yY2VVcGRhdGVSZW5kZXJEYXRhKClcbiAgICAgICAgICAgIGNvbnN0IG1heFdpZHRoID0gdGhpcy50aXRsZUxibF8ubm9kZS5wYXJlbnQud2lkdGggLSB0aGlzLnRpdGxlTGJsXy5ub2RlLndpZHRoIC0gMjBcbiAgICAgICAgICAgIHRoaXMudGltZU5vZGVfLkNvbXBvbmVudChBZGFwdFdpZHRoQ21wdCkuc2V0TWF4V2lkdGgobWF4V2lkdGgpXG4gICAgICAgICAgICBjb25zdCBzZWFzb25UZXh0S2V5ID0gJ3VpLnNlYXNvbl8nICsgKHNlYXNvbi50eXBlICsgMSlcbiAgICAgICAgICAgIHRoaXMudGltZU5vZGVfLkNoaWxkKCdkZXNjJywgY2MuTGFiZWwpLmZvbnQgPSBhc3NldHNNZ3IuZ2V0Rm9udCgnZl9tJylcbiAgICAgICAgICAgIHRoaXMudGltZU5vZGVfLkNoaWxkKCdkZXNjJywgY2MuTGFiZWxUaW1lcikuc2V0Rm9ybWF0KCh0aW1lOiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXNzZXRzTWdyLmxhbmcoJ3VpLm5leHRfc2Vhc29uX2Rlc2MnLCB1dC5taWxsaXNlY29uZEZvcm1hdCh0aW1lICogMTAwMCwgJ2g6bW06c3MnKSwgc2Vhc29uVGV4dEtleSlcbiAgICAgICAgICAgIH0pLnJ1bihzZWFzb24uZ2V0U3VycGx1c1RpbWUoKSAqIDAuMDAxKVxuICAgICAgICB9XG4gICAgICAgIC8vIOiOt+iDnOinhOWImVxuICAgICAgICBjb25zdCBpc0N1biA9IHNlYXNvbi50eXBlIDw9IDAsIHdpblR5cGUgPSBnYW1lSHByLndvcmxkLmdldFdpbkNvbmRUeXBlKClcbiAgICAgICAgY29uc3QgY29uZCA9IHRoaXMud2luZENvbmROb2RlXy5Td2loKGlzQ3VuID8gJzAnIDogJzEnKVswXVxuICAgICAgICBpZiAoaXNDdW4pIHtcbiAgICAgICAgICAgIGNvbmQuQ2hpbGQoJ3ZhbCcpLnNldExvY2FsZUtleSh3aW5UeXBlID09PSBXaW5Db25kVHlwZS5LQVJNSUNfTUFISk9ORyA/ICd1aS5zZWFzb25fZGVzY194XzMnIDogJ3VpLnNlYXNvbl9kZXNjX3gnKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uZC5DaGlsZCgndmFsJykuc2V0TG9jYWxlS2V5KCd1aS53aW5fY29uZF9kZXNjJywgJ3VpLndpbl9jb25kXycgKyB3aW5UeXBlKVxuICAgICAgICB9XG4gICAgICAgIC8vIOaImOWcuuaXtumXtFxuICAgICAgICB0aGlzLmdhbWVUaW1lTm9kZV8uQ2hpbGQoJ3J1bl90aW1lL3ZhbCcpLnNldExvY2FsZUtleSgndWkudGV4dCcsIGdhbWVIcHIubWlsbGlzZWNvbmRUb1N0cmluZyhnYW1lSHByLndvcmxkLmdldFNlcnZlclJ1blRpbWUoKSkpXG4gICAgICAgIHRoaXMuZ2FtZVRpbWVOb2RlXy5DaGlsZCgnc2VydmVyX3RpbWUvdmFsJywgY2MuTGFiZWxUaW1lcikuc2V0RW5kVGltZSh1dC5NQVhfVkFMVUUpLnNldEZvcm1hdCh0aW1lID0+IHV0LmRhdGVGb3JtYXQoJ01NL2RkIGhoOm1tOnNzJywgdGltZSAqIDEwMDApKS5ydW4oZ2FtZUhwci5nZXRTZXJ2ZXJOb3dUaW1lQnlab25lT2Zmc2V0KCkgKiAwLjAwMSlcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L3BvbGljeV9uYmVfblxuICAgIG9uQ2xpY2tQb2xpY3koZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIF9kYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGV2ZW50LnRhcmdldC5EYXRhXG4gICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2NvbW1vbi9Qb2xpY3lJbmZvQm94JywgZGF0YS5pZCwgJ2Jvb2snKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93QWxlcnQoJ3VpLmx2X3VubG9ja19uZXcnLCB7IHBhcmFtczogWyd1aS5zZWFzb25fJyArIGV2ZW50LnRhcmdldC5uYW1lLCAndWkuY2VyaV90eXBlX25hbWVfMSddIH0pXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC93aW5kX2NvbmRfbi8xL2hlbHBfYmVcbiAgICBvbkNsaWNrSGVscChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IHdpblR5cGUgPSBnYW1lSHByLndvcmxkLmdldFdpbkNvbmRUeXBlKCksIGxpc3QgPSBbXVxuICAgICAgICBjb25zdCBjb3VudCA9IHRoaXMuV0lOX0NPTkRfVFlQRV9ERVNDX0NPVU5UW3dpblR5cGVdIHx8IDNcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAod2luVHlwZSA9PT0gV2luQ29uZFR5cGUuVEVSUklUT1JZX0RJU1BVVEUgJiYgaSA9PT0gMCkge1xuICAgICAgICAgICAgICAgIGxpc3QucHVzaCh7IGtleTogYHVpLndpbl9ydWxlX2Rlc2NfJHt3aW5UeXBlfV8ke2l9YCwgcGFyYW1zOiBbZ2FtZUhwci53b3JsZC5nZXRXaW5Db25kVmFsdWUoKV0gfSlcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgbGlzdC5wdXNoKHsga2V5OiBgdWkud2luX3J1bGVfZGVzY18ke3dpblR5cGV9XyR7aX1gIH0pXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdmlld0hlbHBlci5zaG93RGVzY0luZm8obGlzdCwgJ3VpLnRpdGxlX3dpbl9jb25kXycgKyB3aW5UeXBlKVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgcHJpdmF0ZSBvbkNoYW5nZVNlYXNvbigpIHtcbiAgICAgICAgdGhpcy5vbkVudGVyKClcbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbn1cbiJdfQ==