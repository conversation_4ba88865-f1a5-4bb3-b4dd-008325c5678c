
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/BattlePassBuyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '08defDVIW5Axan1CPCVud+8', 'BattlePassBuyPnlCtrl');
// app/script/view/common/BattlePassBuyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var ccclass = cc._decorator.ccclass;
var BattlePassBuyPnlCtrl = /** @class */ (function (_super) {
    __extends(BattlePassBuyPnlCtrl, _super);
    function BattlePassBuyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rewardsNode_ = null; // path://root/rewards_n
        _this.tipNode_ = null; // path://root/tip_n
        _this.bpPriceLbl_ = null; // path://root/buy_bp_be/root/bp_price_l
        //@end
        _this.ITEM_ADAPT_SIZE = cc.size(64, 64);
        return _this;
    }
    BattlePassBuyPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BattlePassBuyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BattlePassBuyPnlCtrl.prototype.onEnter = function () {
        this.bpPriceLbl_.string = PayHelper_1.payHelper.getBattlePassPriceText();
        this.showPayRewards();
    };
    BattlePassBuyPnlCtrl.prototype.onRemove = function () {
    };
    BattlePassBuyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buy_bp_be
    BattlePassBuyPnlCtrl.prototype.onClickBuyBp = function (event, data) {
        var _this = this;
        PayHelper_1.payHelper.buyProduct(Constant_1.RECHARGE_BATTLE_PASS).then(function (suc) {
            logger.print('BATTLE_PASS.buyProduct end. suc=' + suc + ', isValid=' + _this.isValid);
            if (suc && _this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/rewards_n/item_be
    BattlePassBuyPnlCtrl.prototype.onClickItem = function (event, _data) {
        var data = event.target.Data;
        data && ViewHelper_1.viewHelper.previewRewardDetail(data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 展示付费奖励
    BattlePassBuyPnlCtrl.prototype.showPayRewards = function () {
        var _this = this;
        var _a;
        // 到时间结束最多能获取多少经验
        var curExp = ((_a = GameHelper_1.gameHpr.user.getBattlePassInfo()) === null || _a === void 0 ? void 0 : _a.score) || 0;
        // 找到当前开放的战令
        var cfg = GameHelper_1.gameHpr.user.getOpeningBattlePassCfg();
        var maxExp = 300 + curExp + Math.ceil(ut.timediff(Date.now(), cfg.endTime) / ut.Time.Day) * 200;
        var datas = assetsMgr.getJson('battlePass').datas.filter(function (m) { return Math.floor(m.id / 1000) === cfg.id; }).map(function (m) {
            var reward = GameHelper_1.gameHpr.stringToCTypes(m.reward_2)[0];
            if (m.score > maxExp) { // 获取不到的
                return { id: m.id, reward: reward, red: true };
            }
            else {
                return { id: m.id, reward: reward, red: false };
            }
        });
        var gold = new CTypeObj_1.default().init(Enums_1.CType.GOLD, 0, 0), list = [{ id: 0, reward: gold, red: false }];
        for (var i = 0; i < datas.length; i++) {
            var data = datas[i], reward = data.reward;
            if (reward.type === gold.type && !data.red) {
                gold.count += reward.count;
            }
            else if (reward.type !== gold.type) {
                list.push({ id: data.id, reward: reward, red: data.red });
            }
        }
        this.tipNode_.active = list.some(function (m) { return m.red; });
        var arr = [Enums_1.CType.PAWN_SKIN, Enums_1.CType.HERO_DEBRIS, Enums_1.CType.HERO_OPT];
        this.rewardsNode_.Items(list, function (it, data) {
            var reward = it.Data = data.reward;
            it.Component(cc.Button).interactable = arr.has(reward.type);
            it.Child('no').active = data.red;
            it.Child('root/icon').opacity = data.red ? 100 : 255;
            ViewHelper_1.viewHelper.updateItemByCTypeOne(it.Child('root'), reward, _this.key, _this.ITEM_ADAPT_SIZE);
            ViewHelper_1.viewHelper.updateItemNameByCTypeOne(it.Child('msg'), reward, true);
        });
    };
    BattlePassBuyPnlCtrl = __decorate([
        ccclass
    ], BattlePassBuyPnlCtrl);
    return BattlePassBuyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BattlePassBuyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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