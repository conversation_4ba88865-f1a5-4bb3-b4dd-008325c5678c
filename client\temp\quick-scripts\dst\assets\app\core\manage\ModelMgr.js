
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/ModelMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cffddbrkdpD1pjEQfDC+Irq', 'ModelMgr');
// app/core/manage/ModelMgr.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 数据模型管理
var ModelMgr = /** @class */ (function () {
    function ModelMgr() {
        this.models = new Map();
    }
    // 静态添加模型
    ModelMgr.prototype.init = function (map) {
        for (var key in map) {
            this.__add(new map[key](key));
        }
    };
    ModelMgr.prototype.create = function () {
        this.models.forEach(function (m) { return m.__create(); });
    };
    ModelMgr.prototype.__add = function (model) {
        if (!model._type) {
            return logger.error('model type=null!');
        }
        else if (this.models.has(model._type)) {
            return logger.error('出现相同的model type=' + model._type);
        }
        this.models.set(model._type, model);
    };
    // 添加模型
    ModelMgr.prototype.add = function () {
        var _this = this;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        params.forEach(function (m) { return _this.__add(m); });
        params.forEach(function (m) { return m.__create(); });
    };
    // 获取模型
    ModelMgr.prototype.get = function (key) {
        var model = this.models.get(key);
        if (!model) {
            logger.error('get model error! not found ' + key);
        }
        return model;
    };
    // 替换模型
    ModelMgr.prototype.reset = function (model) {
        var _a;
        if (!model._type) {
            return logger.error('model type=null!');
        }
        (_a = this.models.get(model._type)) === null || _a === void 0 ? void 0 : _a.__clean();
        this.models.set(model._type, model);
        model.__create();
    };
    return ModelMgr;
}());
exports.default = ModelMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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