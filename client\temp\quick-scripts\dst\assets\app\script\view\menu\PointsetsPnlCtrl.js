
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/PointsetsPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '914b824wjtAcKQYcm83zUj6', 'PointsetsPnlCtrl');
// app/script/view/menu/PointsetsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PointsetsPnlCtrl = /** @class */ (function (_super) {
    __extends(PointsetsPnlCtrl, _super);
    function PointsetsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.animNode_ = null; // path://anim_n
        _this.skipTge_ = null; // path://skip_t_te
        _this.pointsetsNode_ = null; // path://pointsets_nbe_n
        _this.warTokenNode_ = null; // path://top/war_token_n
        _this.goldNode_ = null; // path://top/gold_n
        _this.showNode_ = null; // path://show_n
        _this.showSsrNode_ = null; // path://show_ssr_n
        //@end
        _this.warTokenValLbl = null;
        _this.goldValLbl = null;
        _this.user = null;
        _this.isSkipAnim = false;
        return _this;
    }
    PointsetsPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_WAR_TOKEN] = this.onUpdateWarToken, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _b.enter = true, _b),
        ];
    };
    PointsetsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                this.pointsetsNode_.children.forEach(function (m) {
                    var isGold = m.name === '50';
                    var v = isGold ? 5 : Number(m.name);
                    m.Child('lay/val').setLocaleKey('ui.button_pointsets', v);
                    if (isGold) {
                        m.Child('lay/gold/val', cc.Label).string = Constant_1.POINTSETS_ONE_GOLD_COST + '';
                    }
                    else {
                        m.Child('lay/war_token/val', cc.Label).string = (Constant_1.POINTSETS_ONE_COST * v) + '';
                    }
                });
                this.warTokenValLbl = this.warTokenNode_.FindChild('val', cc.LabelRollNumber);
                this.goldValLbl = this.goldNode_.FindChild('val', cc.LabelRollNumber);
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    PointsetsPnlCtrl.prototype.onEnter = function (data) {
        this.pointsetsNode_.children.forEach(function (m) { return m.Component(cc.Button).interactable = true; });
        this.playGuAnim('idle');
        this.warTokenValLbl.set(this.user.getWarToken());
        this.goldValLbl.set(this.user.getGold());
        this.showNode_.active = false;
        this.skipTge_.setActive(true);
        this.showSsrNode_.active = false;
        this.isSkipAnim = !!storageMgr.loadBool('draw_hero');
        this.skipTge_.isChecked = this.isSkipAnim;
    };
    PointsetsPnlCtrl.prototype.onRemove = function () {
        audioMgr.stopSFX('common/sound_ui_013', 'jigu');
        audioMgr.stopSFX('common/sound_ui_014', 'pointsets_play_debris');
        audioMgr.stopSFX('common/sound_ui_015', 'pointsets_play_ssr_open');
        audioMgr.stopSFX('common/sound_ui_016', 'pointsets_play_ssr_close');
    };
    PointsetsPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://pointsets_nbe_n
    PointsetsPnlCtrl.prototype.onClickPointsets = function (event, data) {
        this.do(Number(event.target.name));
    };
    // path://show_n/close_show_be
    PointsetsPnlCtrl.prototype.onClickCloseShow = function (event, data) {
        this.showNode_.active = false;
        this.skipTge_.setActive(true);
        this.hidePointsetsButton(false);
        if (GameHelper_1.gameHpr.guide.isWorking()) {
            this.emit('POINTSETS_CLOSE_SHOW'); //新手引导使用
        }
    };
    // path://show_ssr_n/close_show_ssr_be
    PointsetsPnlCtrl.prototype.onClickCloseShowSsr = function (event, data) {
        this.emit(EventType_1.default.CLOSE_SHOW_SSR_UI);
    };
    // path://skip_t_te
    PointsetsPnlCtrl.prototype.onClickSkip = function (event, data) {
        this.isSkipAnim = this.skipTge_.isChecked;
        storageMgr.saveBool('draw_hero', this.isSkipAnim);
    };
    // path://view_chance/view_chance_be
    PointsetsPnlCtrl.prototype.onClickViewChance = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/PointsetsChance');
    };
    // path://top/gold_n/add_gold_be
    PointsetsPnlCtrl.prototype.onClickAddGold = function (event, data) {
        ViewHelper_1.viewHelper.showBuyGoldTipPnl();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PointsetsPnlCtrl.prototype.onUpdateWarToken = function () {
        this.warTokenValLbl.to(this.user.getWarToken());
    };
    // 刷新金币
    PointsetsPnlCtrl.prototype.onUpdateGold = function () {
        this.goldValLbl.to(this.user.getGold());
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PointsetsPnlCtrl.prototype.playGuAnim = function (name) {
        this.animNode_.Child('gu', cc.Animation).play('jigu_' + name);
    };
    PointsetsPnlCtrl.prototype.banClick = function (val) {
        this.pointsetsNode_.children.forEach(function (m) { return m.Component(cc.Button).interactable = !val; });
        if (val) {
            mc.lockTouch('pointsets');
        }
        else {
            mc.unlockTouch('pointsets');
        }
    };
    // 隐藏点将按钮
    PointsetsPnlCtrl.prototype.hidePointsetsButton = function (val) {
        this.pointsetsNode_.opacity = val ? 100 : 255;
    };
    // 点将
    PointsetsPnlCtrl.prototype.do = function (count) {
        return __awaiter(this, void 0, void 0, function () {
            var user, isGold, _a, err, data;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        user = this.user, isGold = count === 50 //代表使用金币
                        ;
                        if (isGold) {
                            if (Constant_1.POINTSETS_ONE_GOLD_COST > user.getGold()) {
                                return [2 /*return*/, ViewHelper_1.viewHelper.showGoldNotEnough()];
                            }
                        }
                        else if (count * Constant_1.POINTSETS_ONE_COST > user.getWarToken()) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.WAR_TOKEN_NOT_ENOUGH)];
                        }
                        this.banClick(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_Pointsets', { count: count, skip: this.skipTge_.isChecked }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            this.banClick(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        user.setPortrayals(data.portrayals);
                        user.setWarToken(data.warToken);
                        user.setGold(data.gold);
                        this.emit(EventType_1.default.UPDATE_PORTRAYAL_INFO);
                        if (!this.isValid) {
                            return [2 /*return*/, this.banClick(false)];
                        }
                        if (!!this.isSkipAnim) return [3 /*break*/, 3];
                        audioMgr.fadeOutBGM(0.2, false);
                        audioMgr.playSFX('common/sound_ui_013', { tag: 'jigu' });
                        this.animNode_.Child('gu', cc.Animation).playAsync('jigu_run').then(function () { return _this.isValid && _this.playGuAnim('idle'); });
                        return [4 /*yield*/, ut.wait(2.15)];
                    case 2:
                        _b.sent();
                        _b.label = 3;
                    case 3:
                        if (!this.isValid) {
                            audioMgr.fadeInBGM(0.2);
                            return [2 /*return*/, mc.unlockTouch('pointsets')];
                        }
                        this.banClick(false);
                        this.showGain(data.ids, data.counts);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 展示获得
    PointsetsPnlCtrl.prototype.showGain = function (ids, counts) {
        return __awaiter(this, void 0, void 0, function () {
            var posList, root, closeNode, i, l, pos, id, count, ssr, json, it, node, icon, debris, ssrNode, name, anim, box, isMax;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.guide.isWorking()) {
                            this.emit('POINTSETS_OPEN_SHOW'); //新手引导使用
                        }
                        !this.isSkipAnim && audioMgr.fadeInBGM(1, 0.5);
                        this.hidePointsetsButton(true);
                        this.showNode_.active = true;
                        this.skipTge_.setActive(false);
                        posList = ids.length === 1 ? [cc.Vec2.ZERO] : this.showNode_.Child('pos').children.map(function (m) { return m.getPosition(); });
                        root = this.showNode_.Child('root');
                        closeNode = this.showNode_.Child('close_show_be');
                        closeNode.active = false;
                        root.Swih('');
                        i = 0, l = ids.length;
                        _a.label = 1;
                    case 1:
                        if (!(i < l)) return [3 /*break*/, 7];
                        pos = posList[i], id = ids[i], count = counts[i], ssr = count >= 3;
                        json = assetsMgr.getJsonData('portrayalBase', id);
                        it = root.AddItem().it;
                        it.setPosition(0, 0);
                        node = it.Child('1/root'), icon = node.Child('mask/icon'), debris = node.Child('debris'), ssrNode = it.Child('ssr');
                        icon.opacity = ssr ? 255 : 150;
                        ResHelper_1.resHelper.loadPortrayalImage(id, icon, this.key);
                        if (json.ui_offset) {
                            icon.setPosition(ut.stringToVec2(json.ui_offset));
                        }
                        else {
                            icon.setPosition(0, 0);
                        }
                        node.Child('di', cc.MultiFrame).setFrame(ssr);
                        if (debris.active = !ssr) {
                            ResHelper_1.resHelper.loadPortrayalDebrisMask(json.ui_debris_mask || 1, debris, this.key);
                        }
                        if (node.Child('debris_count').active = ssr) {
                            ViewHelper_1.viewHelper.updatePortrayalDebrisCount(node, count);
                        }
                        name = node.Child('name');
                        name.Color(ssr ? '#DB543B' : '#C58461');
                        name.Child('val').setLocaleKey('portrayalText.name_' + id);
                        anim = it.Component(cc.Animation);
                        if (!this.isSkipAnim) return [3 /*break*/, 2];
                        box = ssrNode.Child('box'), isMax = count >= 81;
                        box.Component(cc.MultiFrame).setFrame(isMax);
                        if (isMax) {
                            box.Color('#FFFFFF');
                        }
                        else {
                            box.Color(count >= 27 ? '#FFEA5D' : '#FD5EFF');
                        }
                        ssrNode.opacity = count >= 3 ? 255 : 0;
                        it.setPosition(pos.x, pos.y);
                        return [3 /*break*/, 6];
                    case 2:
                        ssrNode.opacity = 0;
                        anim.play('juanzhou_idle');
                        if (!ssr) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.playSSR(it, pos, anim, ssrNode, json, count)];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 6];
                    case 4:
                        this.playDebris(it, pos, anim);
                        return [4 /*yield*/, ut.wait(0.7, this)];
                    case 5:
                        _a.sent();
                        _a.label = 6;
                    case 6:
                        i++;
                        return [3 /*break*/, 1];
                    case 7:
                        !this.isSkipAnim && audioMgr.fadeInBGM(1);
                        return [4 /*yield*/, ut.wait(0.5, this)];
                    case 8:
                        _a.sent();
                        closeNode.active = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    PointsetsPnlCtrl.prototype.playDebris = function (it, pos, anim) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.playFly(it, pos)];
                    case 1:
                        _a.sent();
                        ut.wait(0.1, this).then(function () { return (_this.isValid && _this.getActive()) && audioMgr.playSFX('common/sound_ui_014', { tag: 'pointsets_play_debris' }); });
                        anim.play('juanzhou_open');
                        return [2 /*return*/];
                }
            });
        });
    };
    PointsetsPnlCtrl.prototype.playSSR = function (it, pos, anim, ssrNode, json, count) {
        return __awaiter(this, void 0, void 0, function () {
            var closeNode, icon, nameNode, countLab, viceNode, box, isMax;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.playFly(it, pos)];
                    case 1:
                        _a.sent();
                        audioMgr.playSFX('common/sound_ui_015', { tag: 'pointsets_play_ssr_open' });
                        return [4 /*yield*/, anim.playAsync('juanzhou_open')];
                    case 2:
                        _a.sent();
                        this.showSsrNode_.active = true;
                        this.showSsrNode_.Swih('close_show_ssr_be', true);
                        this.showSsrNode_.Child('root', cc.Animation).play();
                        closeNode = this.showSsrNode_.Child('close_show_ssr_be'), icon = this.showSsrNode_.Child('root/val'), nameNode = this.showSsrNode_.Child('name/layout/lay'), countLab = this.showSsrNode_.Child('name/layout/count/val', cc.Label);
                        icon.scale = 4;
                        icon.opacity = 255;
                        if (json.ui_ssr_offset) {
                            icon.setPosition(ut.stringToVec2(json.ui_ssr_offset));
                        }
                        else {
                            icon.setPosition(0, 0);
                        }
                        ResHelper_1.resHelper.loadPortrayalImage(json.id, icon, this.key);
                        ResHelper_1.resHelper.loadPawnHeadIcon(json.id, this.showSsrNode_.Child('role'), this.key);
                        countLab.string = count + '';
                        nameNode.Child('name').setLocaleKey('portrayalText.name_' + json.id);
                        viceNode = nameNode.Child('vice');
                        if (viceNode.active = !!assetsMgr.getJsonData('portrayalText', 'vice_' + json.id)) {
                            viceNode.setLocaleKey('ui.bracket', 'portrayalText.vice_' + json.id);
                        }
                        return [4 /*yield*/, ut.wait(0.5, this)];
                    case 3:
                        _a.sent();
                        closeNode.active = true;
                        return [4 /*yield*/, eventCenter.wait(EventType_1.default.CLOSE_SHOW_SSR_UI)];
                    case 4:
                        _a.sent();
                        audioMgr.playSFX('common/sound_ui_016', { tag: 'pointsets_play_ssr_close' });
                        return [4 /*yield*/, this.playFlySSR(this.showSsrNode_.Swih('root')[0].Child('val'), ut.stringToVec2(json.ui_offset).addSelf(pos))];
                    case 5:
                        _a.sent();
                        this.showSsrNode_.active = false;
                        box = ssrNode.Child('box'), isMax = count >= 81;
                        box.Component(cc.MultiFrame).setFrame(isMax);
                        if (isMax) {
                            box.Color('#FFFFFF');
                        }
                        else {
                            box.Color(count >= 27 ? '#FFEA5D' : '#FD5EFF');
                        }
                        cc.tween(ssrNode).to(0.2, { opacity: 255 }).start();
                        return [2 /*return*/];
                }
            });
        });
    };
    PointsetsPnlCtrl.prototype.playFly = function (node, target) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                node.scale = 0.2;
                return [2 /*return*/, new Promise(function (resolve) { return cc.tween(node).to(0.2, { x: target.x, y: target.y, scale: 1 }, { easing: cc.easing.quartOut }).call(resolve).start(); })];
            });
        });
    };
    PointsetsPnlCtrl.prototype.playFlySSR = function (node, target) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        cc.tween(node)
                            .to(0.2, { x: target.x, y: target.y, scale: 2, opacity: 200 }, { easing: cc.easing.sineOut })
                            .to(0.3, { opacity: 0, scale: 2.2 })
                            .call(resolve).start();
                    })];
            });
        });
    };
    PointsetsPnlCtrl = __decorate([
        ccclass
    ], PointsetsPnlCtrl);
    return PointsetsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PointsetsPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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