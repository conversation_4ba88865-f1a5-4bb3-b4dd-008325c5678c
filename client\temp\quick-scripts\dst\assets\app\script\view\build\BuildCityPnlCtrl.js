
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildCityPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '05c1fSyOmdHmaKU0jj366rn', 'BuildCityPnlCtrl');
// app/script/view/build/BuildCityPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BuildCityPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildCityPnlCtrl, _super);
    function BuildCityPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconNode_ = null; // path://root/icon_n
        _this.infoNode_ = null; // path://root/info_n
        _this.descLbl_ = null; // path://root/info_n/desc_l
        //@end
        _this.supportData = null;
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    BuildCityPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuildCityPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildCityPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex);
        var icon = data.icon;
        if (cell.cityId === Constant_1.CITY_FORT_NID) {
            icon = 'build_2102_' + cell.getOwnType();
        }
        ResHelper_1.resHelper.loadBuildIcon(icon, this.iconNode_.Child('val', cc.Sprite), this.key);
        this.iconNode_.Child('name').setLocaleKey(data.name);
        this.iconNode_.Child('lv').setLocaleKey('ui.lv', ((_a = cell === null || cell === void 0 ? void 0 : cell.getPawnInfo()) === null || _a === void 0 ? void 0 : _a.lv) || data.lv);
        this.descLbl_.setLocaleKey(data.desc);
        var attr = this.infoNode_.Child('attr');
        // hp
        var _b = cell.getPawnInfo() || { id: 7001, lv: 1 }, id = _b.id, lv = _b.lv;
        var newLv = id === 7001 ? Math.max(1, lv - GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TOWER_LV, cell.owner)) : lv;
        var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + newLv);
        var hp = (cell === null || cell === void 0 ? void 0 : cell.getHpInfo()) || [0, 0], jsonHp = (attrJson === null || attrJson === void 0 ? void 0 : attrJson.hp) || 0, maxHp = hp[1];
        attr.Child('hp/val').setLocaleKey('ui.build_hp', hp.join('/'));
        if (attr.Child('hp/add').active = jsonHp < maxHp) {
            attr.Child('hp/add/val', cc.Label).string = '+' + (maxHp - jsonHp);
        }
        // attack
        var attack = attr.Child('attack'), range = attr.Child('range');
        var json = cell === null || cell === void 0 ? void 0 : cell.getPawnAttrJson();
        attack.active = range.active = !!json;
        if (json) {
            attack.Child('val').setLocaleKey('ui.build_attack', json.attack);
            if (attack.Child('add').active = attrJson.attack < json.attack) {
                attack.Child('add/val', cc.Label).string = '+' + (json.attack - attrJson.attack);
            }
            range.Child('val').setLocaleKey('ui.build_range', json.attack_range);
        }
    };
    BuildCityPnlCtrl.prototype.onRemove = function () {
    };
    BuildCityPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    BuildCityPnlCtrl = __decorate([
        ccclass
    ], BuildCityPnlCtrl);
    return BuildCityPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildCityPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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