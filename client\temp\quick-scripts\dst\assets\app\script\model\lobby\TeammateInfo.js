
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/lobby/TeammateInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0d4dajNNZdENaf9ko4LcOKv', 'TeammateInfo');
// app/script/model/lobby/TeammateInfo.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseUserInfo_1 = require("../common/BaseUserInfo");
// 队友信息
var TeammateInfo = /** @class */ (function (_super) {
    __extends(TeammateInfo, _super);
    function TeammateInfo() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.job = 0; //职位 0普通 1队长 2邀请权限
        _this.farmType = 0; // 开局模式
        _this.expectPosition = 0; // 期望位置       
        return _this;
    }
    TeammateInfo.prototype.init = function (data) {
        _super.prototype.init.call(this, data);
        this.job = data.job;
        this.farmType = data.farmType;
        this.expectPosition = data.expectPosition;
        return this;
    };
    TeammateInfo.prototype.isInviteAuth = function () {
        return this.job === 2;
    };
    return TeammateInfo;
}(BaseUserInfo_1.default));
exports.default = TeammateInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxsb2JieVxcVGVhbW1hdGVJbmZvLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHVEQUFpRDtBQUVqRCxPQUFPO0FBQ1A7SUFBMEMsZ0NBQVk7SUFBdEQ7UUFBQSxxRUFpQkM7UUFmVSxTQUFHLEdBQVcsQ0FBQyxDQUFBLENBQUMsa0JBQWtCO1FBQ2xDLGNBQVEsR0FBRyxDQUFDLENBQUEsQ0FBQyxPQUFPO1FBQ3BCLG9CQUFjLEdBQUcsQ0FBQyxDQUFBLENBQUMsY0FBYzs7SUFhNUMsQ0FBQztJQVhVLDJCQUFJLEdBQVgsVUFBWSxJQUFTO1FBQ2pCLGlCQUFNLElBQUksWUFBQyxJQUFJLENBQUMsQ0FBQTtRQUNoQixJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUE7UUFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFBO1FBQzdCLElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQTtRQUN6QyxPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSxtQ0FBWSxHQUFuQjtRQUNJLE9BQU8sSUFBSSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUE7SUFDekIsQ0FBQztJQUNMLG1CQUFDO0FBQUQsQ0FqQkEsQUFpQkMsQ0FqQnlDLHNCQUFZLEdBaUJyRCIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCYXNlVXNlckluZm8gZnJvbSBcIi4uL2NvbW1vbi9CYXNlVXNlckluZm9cIlxuXG4vLyDpmJ/lj4vkv6Hmga9cbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFRlYW1tYXRlSW5mbyBleHRlbmRzIEJhc2VVc2VySW5mbyB7XG5cbiAgICBwdWJsaWMgam9iOiBudW1iZXIgPSAwIC8v6IGM5L2NIDDmma7pgJogMemYn+mVvyAy6YKA6K+35p2D6ZmQXG4gICAgcHVibGljIGZhcm1UeXBlID0gMCAvLyDlvIDlsYDmqKHlvI9cbiAgICBwdWJsaWMgZXhwZWN0UG9zaXRpb24gPSAwIC8vIOacn+acm+S9jee9riAgICAgICBcblxuICAgIHB1YmxpYyBpbml0KGRhdGE6IGFueSkge1xuICAgICAgICBzdXBlci5pbml0KGRhdGEpXG4gICAgICAgIHRoaXMuam9iID0gZGF0YS5qb2JcbiAgICAgICAgdGhpcy5mYXJtVHlwZSA9IGRhdGEuZmFybVR5cGVcbiAgICAgICAgdGhpcy5leHBlY3RQb3NpdGlvbiA9IGRhdGEuZXhwZWN0UG9zaXRpb25cbiAgICAgICAgcmV0dXJuIHRoaXNcbiAgICB9XG5cbiAgICBwdWJsaWMgaXNJbnZpdGVBdXRoKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5qb2IgPT09IDJcbiAgICB9XG59Il19