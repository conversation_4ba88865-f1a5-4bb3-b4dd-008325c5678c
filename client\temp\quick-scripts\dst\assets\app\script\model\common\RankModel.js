
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/RankModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1aefayfPJFEk7iF4ha86Xk6', 'RankModel');
// app/script/model/common/RankModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
/**
 * 排行榜模块
 */
var RankModel = /** @class */ (function (_super) {
    __extends(RankModel, _super);
    function RankModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.lastReqPlayerRankTime = 0; //最后一次请求排行榜时间
        _this.tempPlayerRankList = []; //玩家排行榜列表
        _this.meRankInfo = null; //自己的排行榜信息
        _this.lastReqAlliRankTime = 0; //最后一次请求排行榜时间
        _this.tempAlliRankList = []; //联盟排行榜列表
        _this.lastReqPlayerScoreTimeMap = {};
        _this.tempPlayerScoreListMap = {};
        _this.meScoreInfo = null; //自己的积分排行信息
        _this.lastReqUserScoreRankTime = 0;
        _this.tempUserScoreRankList = [];
        _this.meScoreRankInfo = null; //自己的评分排行信息
        return _this;
    }
    RankModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    RankModel.prototype.clean = function () {
        this.lastReqPlayerRankTime = 0;
        this.lastReqAlliRankTime = 0;
    };
    // 获取玩家排行榜信息
    RankModel.prototype.getPlayerRankList = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, uid, me;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (this.lastReqPlayerRankTime > 0 && Date.now() - this.lastReqPlayerRankTime <= 10000) {
                            return [2 /*return*/, this.tempPlayerRankList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetPlayerRankList', {})];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        this.lastReqPlayerRankTime = Date.now();
                        this.tempPlayerRankList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.meRankInfo = this.newMeInfo((_a = data === null || data === void 0 ? void 0 : data.no) !== null && _a !== void 0 ? _a : -1);
                        uid = this.meRankInfo.uid;
                        me = this.tempPlayerRankList.find(function (m) { return m.uid === uid; });
                        if (me) {
                            me.nickname = this.meRankInfo.nickname;
                            me.headIcon = this.meRankInfo.headIcon;
                        }
                        return [2 /*return*/, this.tempPlayerRankList];
                }
            });
        });
    };
    RankModel.prototype.newMeInfo = function (no) {
        return { uid: GameHelper_1.gameHpr.getUid(), no: no, nickname: GameHelper_1.gameHpr.user.getNickname(), headIcon: GameHelper_1.gameHpr.user.getHeadIcon(), alliName: GameHelper_1.gameHpr.alliance.getName(), landCount: GameHelper_1.gameHpr.getPlayerOweCellCount() };
    };
    RankModel.prototype.getMeRankInfo = function () { return this.meRankInfo || this.newMeInfo(-1); };
    // 获取联盟排行榜
    RankModel.prototype.getAlliRankList = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqAlliRankTime > 0 && Date.now() - this.lastReqAlliRankTime <= 10000) {
                            return [2 /*return*/, this.tempAlliRankList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliRankList', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqAlliRankTime = Date.now();
                        this.tempAlliRankList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        return [2 /*return*/, this.tempAlliRankList];
                }
            });
        });
    };
    // 获取玩家积分列表
    RankModel.prototype.getPlayerScoreList = function (type) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var time, _b, err, data, list, uid, me;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        time = this.lastReqPlayerScoreTimeMap[type] || 0;
                        if (time > 0 && Date.now() - time <= 10000) {
                            return [2 /*return*/, this.tempPlayerScoreListMap[type] || []];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetPlayerScoreList', { type: type })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        this.lastReqPlayerScoreTimeMap[type] = Date.now();
                        list = this.tempPlayerScoreListMap[type] = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.meScoreInfo = this.newMeScoreInfo((data === null || data === void 0 ? void 0 : data.me) || {}, (_a = data === null || data === void 0 ? void 0 : data.no) !== null && _a !== void 0 ? _a : -1);
                        uid = this.meScoreInfo.uid;
                        me = list.find(function (m) { return m.uid === uid; });
                        if (me) {
                            me.nickname = this.meScoreInfo.nickname;
                            me.headIcon = this.meScoreInfo.headIcon;
                        }
                        return [2 /*return*/, list];
                }
            });
        });
    };
    RankModel.prototype.newMeScoreInfo = function (data, no) {
        return {
            uid: GameHelper_1.gameHpr.getUid(),
            no: no,
            nickname: GameHelper_1.gameHpr.user.getNickname(),
            headIcon: GameHelper_1.gameHpr.user.getHeadIcon(),
            alliIcon: GameHelper_1.gameHpr.alliance.getIcon(),
            alliName: GameHelper_1.gameHpr.alliance.getName(),
            curLandScore: data.landScore || 0,
            curAlliScore: data.alliScore || 0,
            landScore: data.landScoreTop || 0,
            alliScore: data.alliScoreTop || 0,
            extraScore: data.extraScore || 0,
        };
    };
    // 获取自己的积分信息
    RankModel.prototype.getMeScoreInfo = function () { return this.meScoreInfo || this.newMeScoreInfo({}, -1); };
    // 获取用户评分列表
    RankModel.prototype.getUserScoreRankList = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, user, uid, me;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (this.lastReqUserScoreRankTime > 0 && Date.now() - this.lastReqUserScoreRankTime <= ut.Time.Minute) {
                            return [2 /*return*/, this.tempUserScoreRankList];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetScoreRank', {})];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        user = GameHelper_1.gameHpr.user, uid = user.getUid();
                        this.lastReqUserScoreRankTime = Date.now();
                        this.tempUserScoreRankList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.meScoreRankInfo = (data === null || data === void 0 ? void 0 : data.me) || this.newMeScoreRankInfo(data === null || data === void 0 ? void 0 : data.no);
                        this.meScoreRankInfo.no = (_a = data === null || data === void 0 ? void 0 : data.no) !== null && _a !== void 0 ? _a : -1;
                        this.meScoreRankInfo.nickname = user.getNickname();
                        this.meScoreRankInfo.headIcon = user.getHeadIcon();
                        me = this.tempUserScoreRankList.find(function (m) { return m.uid === uid; });
                        if (me) {
                            me.nickname = this.meScoreRankInfo.nickname;
                            me.headIcon = this.meScoreRankInfo.headIcon;
                        }
                        return [2 /*return*/, this.tempUserScoreRankList];
                }
            });
        });
    };
    RankModel.prototype.newMeScoreRankInfo = function (no) {
        var user = GameHelper_1.gameHpr.user;
        return { uid: user.getUid(), no: no, nickname: user.getNickname(), headIcon: user.getHeadIcon(), score: user.getRankScore(), rankCount: user.getAccTotalRankCount() };
    };
    // 获取自己的评分信息
    RankModel.prototype.getMeScoreRankInfo = function () { return this.meScoreRankInfo || this.newMeScoreRankInfo(-1); };
    // 刷新所有排名的自己用户信息
    RankModel.prototype.updateAllRankUserInfo = function () {
        var _a, _b, _c;
        var user = GameHelper_1.gameHpr.user, uid = user.getUid(), nickname = user.getNickname(), headIcon = user.getHeadIcon();
        this.updateRankUserInfoOne((_a = this.tempUserScoreRankList) === null || _a === void 0 ? void 0 : _a.find(function (m) { return m.uid === uid; }), nickname, headIcon);
        this.updateRankUserInfoOne(this.meScoreRankInfo, nickname, headIcon);
        for (var key in this.tempPlayerScoreListMap) {
            this.updateRankUserInfoOne((_b = this.tempPlayerScoreListMap[key]) === null || _b === void 0 ? void 0 : _b.find(function (m) { return m.uid === uid; }), nickname, headIcon);
        }
        this.updateRankUserInfoOne(this.meScoreInfo, nickname, headIcon);
        this.updateRankUserInfoOne((_c = this.tempPlayerRankList) === null || _c === void 0 ? void 0 : _c.find(function (m) { return m.uid === uid; }), nickname, headIcon);
        this.updateRankUserInfoOne(this.meRankInfo, nickname, headIcon);
    };
    RankModel.prototype.updateRankUserInfoOne = function (data, nickname, headIcon) {
        if (data) {
            data.nickname = nickname;
            data.headIcon = headIcon;
        }
    };
    __decorate([
        ut.syncLock
    ], RankModel.prototype, "getPlayerRankList", null);
    RankModel = __decorate([
        mc.addmodel('rank')
    ], RankModel);
    return RankModel;
}(mc.BaseModel));
exports.default = RankModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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