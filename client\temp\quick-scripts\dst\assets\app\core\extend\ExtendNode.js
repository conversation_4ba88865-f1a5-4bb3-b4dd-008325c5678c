
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendNode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7e32fdwd6lBTYmLyFEjsuGu', 'ExtendNode');
// app/core/extend/ExtendNode.ts

"use strict";
/**
 * Node扩展方法
 */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
cc.Node.prototype.Data = null;
cc.Node.prototype.FindChild = function (name, className) {
    name = String(name);
    var val = this;
    var arr = name.split('/');
    for (var i = 0, l = arr.length; i < l; i++) {
        if (!val.isValid) {
            return null;
        }
        val = val.getChildByName(arr[i]);
        if (!val) {
            return null;
        }
    }
    if (className) {
        val = val.getComponent(className);
    }
    return val;
};
cc.Node.prototype.Child = function (name, className) {
    if (!this.isValid) {
        return null;
    }
    name = String(name);
    var cls = typeof className === 'function' ? '_' + cc.js.getClassName(className).replace('.', '') : (className ? '_' + className : '');
    var field = '$_' + name.replace(/\//g, '_') + cls;
    var val = this[field];
    if (val === undefined) {
        val = this;
        if (!val.isValid) {
            return null;
        }
        var arr = name.split('/');
        for (var i = 0, l = arr.length; i < l; i++) {
            val = val.getChildByName(arr[i]);
            if (!val) {
                break;
            }
        }
        if (val && className) {
            val = val.getComponent(className);
        }
        this[field] = !!val ? val : null;
    }
    return val;
};
cc.Node.prototype.Component = function (className) {
    if (!className) {
        return null;
    }
    var cls = typeof className === 'function' ? cc.js.getClassName(className).replace('.', '') : className;
    var field = '$_' + cls;
    var val = this[field];
    if (val === undefined) {
        val = this.getComponent(className);
        this[field] = val;
    }
    return val;
};
cc.Node.prototype.Items = function (list, prefab, cb, target) {
    var i = 0, childs = this.children, item = childs[0];
    var count = 0;
    if (typeof (list) === 'number') {
        count = list;
        list = null;
    }
    else {
        count = list.length;
    }
    if (typeof (prefab) === 'function') {
        target = cb;
        cb = prefab;
    }
    else if (prefab instanceof cc.Node || prefab instanceof cc.Prefab) {
        item = prefab;
    }
    if (!item) {
        return logger.error('Items error, not item');
    }
    for (var l = this.childrenCount; i < l; i++) {
        var it = childs[i];
        if (i < count) {
            it.active = true;
            setItemData(it, list ? list[i] : undefined, i, cb, target);
        }
        else {
            it.Data = null;
            it.active = false;
        }
    }
    for (; i < count; i++) {
        var it = cc.instantiate2(item, this);
        it.active = true;
        setItemData(it, list ? list[i] : undefined, i, cb, target);
    }
};
// 添加一个
cc.Node.prototype.AddItem = function (prefab, cb, target) {
    var item = null;
    if (typeof (prefab) === 'function') {
        target = cb;
        cb = prefab;
    }
    else if (prefab instanceof cc.Node || prefab instanceof cc.Prefab) {
        item = prefab;
    }
    var i = this.children.findIndex(function (m) { return !m.active; }), it = null;
    if (item) {
        it = cc.instantiate2(item, this);
    }
    else if (i !== -1) {
        it = this.children[i];
    }
    else {
        it = cc.instantiate2(this.children[0], this);
    }
    it.active = true;
    var index = i === -1 ? this.childrenCount : i;
    if (!cb) {
        return { it: it, i: i };
    }
    return setItemData(it, index, undefined, cb, target);
};
function setItemData(it, data, i, cb, target) {
    if (!cb) {
        return;
    }
    else if (target) {
        cb.call(target, it, data, i);
    }
    else {
        cb(it, data, i);
    }
}
// 切换节点
cc.Node.prototype.Swih = function (val, reverse, ignores) {
    var name, cb;
    if (typeof (val) === 'function') {
        cb = val;
    }
    else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val);
    }
    else {
        return [];
    }
    var arr = [];
    for (var i = 0, l = this.childrenCount; i < l; i++) {
        var m = this.children[i];
        if (ignores === null || ignores === void 0 ? void 0 : ignores.includes(m.name)) {
            continue;
        }
        else if (reverse) {
            m.active = cb ? !cb(m) : (m.name !== name);
        }
        else {
            m.active = cb ? !!cb(m) : (m.name === name);
        }
        if (m.active) {
            arr.push(m);
        }
    }
    return arr;
};
// 适应大小
cc.Node.prototype.adaptScale = function (targetSize, selfSize, maxScale) {
    if (maxScale === void 0) { maxScale = 1; }
    selfSize = selfSize || this.getContentSize();
    // 先算出宽度比例
    var scale = targetSize.width / selfSize.width;
    // 如果高度大了 就用高的比例
    if (selfSize.height * scale > targetSize.height) {
        scale = targetSize.height / selfSize.height;
    }
    this.scale = Math.min(scale, maxScale);
};
// 适应宽高
cc.Node.prototype.adaptSize = function (targetSize, selfSize, maxScale) {
    if (maxScale === void 0) { maxScale = 1; }
    selfSize = selfSize || this.getContentSize();
    var r = Math.min(targetSize.width / selfSize.width, maxScale);
    var h = Math.floor(r * selfSize.height);
    if (h <= targetSize.height) {
        this.width = Math.floor(r * selfSize.width);
        this.height = h;
    }
    else {
        r = Math.min(targetSize.height / selfSize.height, maxScale);
        this.width = Math.floor(r * selfSize.width);
        this.height = Math.floor(r * selfSize.height);
    }
};
//
cc.Node.prototype.getActive = function () {
    return this.active;
};
cc.Node.prototype.setActive = function (val) {
    this.active = val;
    return val;
};
// 设置颜色
cc.Node.prototype.Color = function (val) {
    if (!val) {
    }
    else if (val instanceof cc.Color) {
        this.color = val;
    }
    else {
        this.color = cc.Color.WHITE.fromHEX(val);
    }
    return this;
};
// 设置触摸事件穿透
cc.Node.prototype.SetSwallowTouches = function (val) {
    if (this._touchListener) {
        this._touchListener.setSwallowTouches(val);
    }
};
cc.Node.prototype.IsSwallowTouches = function () {
    return this._touchListener && this._touchListener.isSwallowTouches();
};
// 设置多语言key
cc.Node.prototype.setLocaleKey = function (key) {
    var params = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        params[_i - 1] = arguments[_i];
    }
    var locale = this.Component(BaseLocale_1.default);
    if (locale) {
        locale.setKey.apply(locale, __spread([key], params));
    }
    else {
        cc.error('setLocaleKey error, not LocaleComponent!');
    }
    return this.Component(cc.Label);
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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