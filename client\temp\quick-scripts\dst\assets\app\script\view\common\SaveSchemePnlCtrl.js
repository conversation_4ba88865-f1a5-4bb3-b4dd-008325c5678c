
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SaveSchemePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd2539ihZhlEK5q8aEDtnZWh', 'SaveSchemePnlCtrl');
// app/script/view/common/SaveSchemePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SaveSchemePnlCtrl = /** @class */ (function (_super) {
    __extends(SaveSchemePnlCtrl, _super);
    function SaveSchemePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.saveNode_ = null; // path://root/save_n
        _this.redrawRecordNode_ = null; // path://root/redraw_record_n
        //@end
        _this.data = null;
        return _this;
    }
    SaveSchemePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SaveSchemePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('common/TopCurrency')];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    SaveSchemePnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        var avatarPawnName = data.avatarPawnName;
        // 槽位信息
        this.updateSlotInfo();
        // 历史记录
        var sv = this.redrawRecordNode_.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.Items(data.historyAttrs, function (it, info, i) {
            it.Data = { index: i, attrs: info.attrs, avatarPawnName: avatarPawnName };
            it.Child('x/count').setLocaleKey('ui.portrayal_redraw_count', data.recompCount - i);
            ViewHelper_1.viewHelper.updatePortrayalShortAttr(it, info.attrs, avatarPawnName);
        });
    };
    SaveSchemePnlCtrl.prototype.onPlayActionComplete = function () {
        ViewHelper_1.viewHelper.showPnl('common/TopCurrency');
    };
    SaveSchemePnlCtrl.prototype.onRemove = function () {
        this.data = null;
        ViewHelper_1.viewHelper.hidePnl('common/TopCurrency');
    };
    SaveSchemePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/save_n/title/desc_be
    SaveSchemePnlCtrl.prototype.onClickDesc = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.save_scheme_desc_1' },
            { key: 'ui.save_scheme_desc_2' },
        ]);
    };
    // path://root/save_n/attrs/x/use_be
    SaveSchemePnlCtrl.prototype.onClickUse = function (event, data) {
        var _this = this;
        var _a;
        var index = 0;
        var slotAttrs = (_a = this.data.storeSlots[index]) === null || _a === void 0 ? void 0 : _a.attrs;
        if (slotAttrs === null || slotAttrs === void 0 ? void 0 : slotAttrs.length) {
            ViewHelper_1.viewHelper.showPnl('common/RestorePortrayal', slotAttrs, this.data.avatarPawnName, function (ok) {
                if (ok && _this.isValid) {
                    _this.doUse(index);
                }
            });
        }
    };
    // path://root/redraw_record_n/list/view/content/item/x/save_be
    SaveSchemePnlCtrl.prototype.onClickSave = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('common/SavePortrayalAttr', data, function (type) {
                if (type >= 0 && _this.isValid) {
                    _this.doSave(type, data.index);
                }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SaveSchemePnlCtrl.prototype.updateSlotInfo = function () {
        var _a;
        var slotAttrs = (_a = this.data.storeSlots[0]) === null || _a === void 0 ? void 0 : _a.attrs;
        var has = !!(slotAttrs === null || slotAttrs === void 0 ? void 0 : slotAttrs.length);
        var slotNode = this.saveNode_.Swih(has ? 'attrs' : 'empty', false, 'title')[0];
        if (has) {
            ViewHelper_1.viewHelper.updatePortrayalShortAttr(slotNode, slotAttrs, this.data.avatarPawnName);
        }
    };
    // 保存
    SaveSchemePnlCtrl.prototype.doSave = function (costType, historyIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var user, portrayal, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        user = GameHelper_1.gameHpr.user, portrayal = this.data;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_SavePortrayal', { id: portrayal.id, slotIndex: 0, historyIndex: historyIndex, costType: costType }, true)];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        portrayal.updateInfo(data.info);
                        user.setWarToken(data.warToken);
                        user.setGold(data.gold);
                        this.updateSlotInfo();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 使用
    SaveSchemePnlCtrl.prototype.doUse = function (slotIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var portrayal, user, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        portrayal = this.data, user = GameHelper_1.gameHpr.user;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_RestorePortrayal', { id: portrayal.id, slotIndex: slotIndex }, true)];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        portrayal.updateInfo(data.info);
                        GameHelper_1.gameHpr.player.updatePawnHeroAttr(portrayal.id, portrayal.attrs);
                        eventCenter.emit(EventType_1.default.UPDATE_PORTRAYAL_ATTR, portrayal);
                        if (this.isValid) {
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SaveSchemePnlCtrl = __decorate([
        ccclass
    ], SaveSchemePnlCtrl);
    return SaveSchemePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SaveSchemePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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