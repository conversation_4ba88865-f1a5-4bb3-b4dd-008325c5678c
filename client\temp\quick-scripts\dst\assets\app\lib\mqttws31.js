
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/lib/mqttws31.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8211aee2NJLA4HTIjDuar9S', 'mqttws31');
// app/lib/mqttws31.js

"use strict";

/*******************************************************************************
 * Copyright (c) 2013 IBM Corp.
 *
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v1.0 which accompany this distribution. 
 *
 * The Eclipse Public License is available at 
 *    http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at 
 *   http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    Andrew Banks - initial API and implementation and initial documentation
 *******************************************************************************/
// Only expose a single object name in the global namespace.
// Everything must go through this module. Global Paho.MQTT module
// only has a single public function, client, which returns
// a Paho.MQTT client object given connection details.

/**
 * Send and receive messages using web browsers.
 * <p> 
 * This programming interface lets a JavaScript client application use the MQTT V3.1 or
 * V3.1.1 protocol to connect to an MQTT-supporting messaging server.
 *  
 * The function supported includes:
 * <ol>
 * <li>Connecting to and disconnecting from a server. The server is identified by its host name and port number. 
 * <li>Specifying options that relate to the communications link with the server, 
 * for example the frequency of keep-alive heartbeats, and whether SSL/TLS is required.
 * <li>Subscribing to and receiving messages from MQTT Topics.
 * <li>Publishing messages to MQTT Topics.
 * </ol>
 * <p>
 * The API consists of two main objects:
 * <dl>
 * <dt><b>{@link Paho.MQTT.Client}</b></dt>
 * <dd>This contains methods that provide the functionality of the API,
 * including provision of callbacks that notify the application when a message
 * arrives from or is delivered to the messaging server,
 * or when the status of its connection to the messaging server changes.</dd>
 * <dt><b>{@link Paho.MQTT.Message}</b></dt>
 * <dd>This encapsulates the payload of the message along with various attributes
 * associated with its delivery, in particular the destination to which it has
 * been (or is about to be) sent.</dd>
 * </dl> 
 * <p>
 * The programming interface validates parameters passed to it, and will throw
 * an Error containing an error message intended for developer use, if it detects
 * an error with any parameter.
 * <p>
 * Example:
 * 
 * <code><pre>
client = new Paho.MQTT.Client(location.hostname, Number(location.port), "clientId");
client.onConnectionLost = onConnectionLost;
client.onMessageArrived = onMessageArrived;
client.connect({onSuccess:onConnect});

function onConnect() {
  // Once a connection has been made, make a subscription and send a message.
  console.log("onConnect");
  client.subscribe("/World");
  message = new Paho.MQTT.Message("Hello");
  message.destinationName = "/World";
  client.send(message); 
};
function onConnectionLost(responseObject) {
  if (responseObject.errorCode !== 0)
    console.log("onConnectionLost:"+responseObject.errorMessage);
};
function onMessageArrived(message) {
  console.log("onMessageArrived:"+message.payloadString);
  client.disconnect(); 
};	
 * </pre></code>
 * @namespace Paho.MQTT 
 */
// if (typeof Paho === "undefined") {
//     Paho = {};
// }
var Paho = window['Paho'] = {};

Paho.MQTT = function (global) {
  // Private variables below, these are only visible inside the function closure
  // which is used to define the module. 
  var version = "@VERSION@";
  var buildLevel = "@BUILDLEVEL@";
  /** 
   * Unique message type identifiers, with associated
   * associated integer values.
   * @private 
   */

  var MESSAGE_TYPE = {
    CONNECT: 1,
    CONNACK: 2,
    PUBLISH: 3,
    PUBACK: 4,
    PUBREC: 5,
    PUBREL: 6,
    PUBCOMP: 7,
    SUBSCRIBE: 8,
    SUBACK: 9,
    UNSUBSCRIBE: 10,
    UNSUBACK: 11,
    PINGREQ: 12,
    PINGRESP: 13,
    DISCONNECT: 14
  }; // Collection of utility methods used to simplify module code 
  // and promote the DRY pattern.  

  /**
   * Validate an object's parameter names to ensure they 
   * match a list of expected variables name for this option
   * type. Used to ensure option object passed into the API don't
   * contain erroneous parameters.
   * @param {Object} obj - User options object
   * @param {Object} keys - valid keys and types that may exist in obj. 
   * @throws {Error} Invalid option parameter found. 
   * @private 
   */

  var validate = function validate(obj, keys) {
    for (var key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (keys.hasOwnProperty(key)) {
          if (typeof obj[key] !== keys[key]) throw new Error(format(ERROR.INVALID_TYPE, [typeof obj[key], key]));
        } else {
          var errorStr = "Unknown property, " + key + ". Valid properties are:";

          for (var key in keys) {
            if (keys.hasOwnProperty(key)) errorStr = errorStr + " " + key;
          }

          throw new Error(errorStr);
        }
      }
    }
  };
  /**
   * Return a new function which runs the user function bound
   * to a fixed scope. 
   * @param {function} User function
   * @param {object} Function scope  
   * @return {function} User function bound to another scope
   * @private 
   */


  var scope = function scope(f, _scope) {
    return function () {
      return f.apply(_scope, arguments);
    };
  };
  /** 
   * Unique message type identifiers, with associated
   * associated integer values.
   * @private 
   */


  var ERROR = {
    OK: {
      code: 0,
      text: "AMQJSC0000I OK."
    },
    CONNECT_TIMEOUT: {
      code: 1,
      text: "AMQJSC0001E Connect timed out."
    },
    SUBSCRIBE_TIMEOUT: {
      code: 2,
      text: "AMQJS0002E Subscribe timed out."
    },
    UNSUBSCRIBE_TIMEOUT: {
      code: 3,
      text: "AMQJS0003E Unsubscribe timed out."
    },
    PING_TIMEOUT: {
      code: 4,
      text: "AMQJS0004E Ping timed out."
    },
    INTERNAL_ERROR: {
      code: 5,
      text: "AMQJS0005E Internal error. Error Message: {0}, Stack trace: {1}"
    },
    CONNACK_RETURNCODE: {
      code: 6,
      text: "AMQJS0006E Bad Connack return code:{0} {1}."
    },
    SOCKET_ERROR: {
      code: 7,
      text: "AMQJS0007E Socket error:{0}."
    },
    SOCKET_CLOSE: {
      code: 8,
      text: "AMQJS0008I Socket closed."
    },
    MALFORMED_UTF: {
      code: 9,
      text: "AMQJS0009E Malformed UTF data:{0} {1} {2}."
    },
    UNSUPPORTED: {
      code: 10,
      text: "AMQJS0010E {0} is not supported by this browser."
    },
    INVALID_STATE: {
      code: 11,
      text: "AMQJS0011E Invalid state {0}."
    },
    INVALID_TYPE: {
      code: 12,
      text: "AMQJS0012E Invalid type {0} for {1}."
    },
    INVALID_ARGUMENT: {
      code: 13,
      text: "AMQJS0013E Invalid argument {0} for {1}."
    },
    UNSUPPORTED_OPERATION: {
      code: 14,
      text: "AMQJS0014E Unsupported operation."
    },
    INVALID_STORED_DATA: {
      code: 15,
      text: "AMQJS0015E Invalid data in local storage key={0} value={1}."
    },
    INVALID_MQTT_MESSAGE_TYPE: {
      code: 16,
      text: "AMQJS0016E Invalid MQTT message type {0}."
    },
    MALFORMED_UNICODE: {
      code: 17,
      text: "AMQJS0017E Malformed Unicode string:{0} {1}."
    }
  };
  /** CONNACK RC Meaning. */

  var CONNACK_RC = {
    0: "Connection Accepted",
    1: "Connection Refused: unacceptable protocol version",
    2: "Connection Refused: identifier rejected",
    3: "Connection Refused: server unavailable",
    4: "Connection Refused: bad user name or password",
    5: "Connection Refused: not authorized"
  };
  /**
   * Format an error message text.
   * @private
   * @param {error} ERROR.KEY value above.
   * @param {substitutions} [array] substituted into the text.
   * @return the text with the substitutions made.
   */

  var format = function format(error, substitutions) {
    var text = error.text;

    if (substitutions) {
      var field, start;

      for (var i = 0; i < substitutions.length; i++) {
        field = "{" + i + "}";
        start = text.indexOf(field);

        if (start > 0) {
          var part1 = text.substring(0, start);
          var part2 = text.substring(start + field.length);
          text = part1 + substitutions[i] + part2;
        }
      }
    }

    return text;
  }; //MQTT protocol and version          6    M    Q    I    s    d    p    3


  var MqttProtoIdentifierv3 = [0x00, 0x06, 0x4d, 0x51, 0x49, 0x73, 0x64, 0x70, 0x03]; //MQTT proto/version for 311         4    M    Q    T    T    4

  var MqttProtoIdentifierv4 = [0x00, 0x04, 0x4d, 0x51, 0x54, 0x54, 0x04];
  /**
   * Construct an MQTT wire protocol message.
   * @param type MQTT packet type.
   * @param options optional wire message attributes.
   * 
   * Optional properties
   * 
   * messageIdentifier: message ID in the range [0..65535]
   * payloadMessage:	Application Message - PUBLISH only
   * connectStrings:	array of 0 or more Strings to be put into the CONNECT payload
   * topics:			array of strings (SUBSCRIBE, UNSUBSCRIBE)
   * requestQoS:		array of QoS values [0..2]
   *  
   * "Flag" properties 
   * cleanSession:	true if present / false if absent (CONNECT)
   * willMessage:  	true if present / false if absent (CONNECT)
   * isRetained:		true if present / false if absent (CONNECT)
   * userName:		true if present / false if absent (CONNECT)
   * password:		true if present / false if absent (CONNECT)
   * keepAliveInterval:	integer [0..65535]  (CONNECT)
   *
   * @private
   * @ignore
   */

  var WireMessage = function WireMessage(type, options) {
    this.type = type;

    for (var name in options) {
      if (options.hasOwnProperty(name)) {
        this[name] = options[name];
      }
    }
  };

  WireMessage.prototype.encode = function () {
    // Compute the first byte of the fixed header
    var first = (this.type & 0x0f) << 4;
    /*
     * Now calculate the length of the variable header + payload by adding up the lengths
     * of all the component parts
     */

    var remLength = 0;
    var topicStrLength = new Array();
    var destinationNameLength = 0; // if the message contains a messageIdentifier then we need two bytes for that

    if (this.messageIdentifier != undefined) remLength += 2;

    switch (this.type) {
      // If this a Connect then we need to include 12 bytes for its header
      case MESSAGE_TYPE.CONNECT:
        switch (this.mqttVersion) {
          case 3:
            remLength += MqttProtoIdentifierv3.length + 3;
            break;

          case 4:
            remLength += MqttProtoIdentifierv4.length + 3;
            break;
        }

        remLength += UTF8Length(this.clientId) + 2;

        if (this.willMessage != undefined) {
          remLength += UTF8Length(this.willMessage.destinationName) + 2; // Will message is always a string, sent as UTF-8 characters with a preceding length.

          var willMessagePayloadBytes = this.willMessage.payloadBytes;
          if (!(willMessagePayloadBytes instanceof Uint8Array)) willMessagePayloadBytes = new Uint8Array(payloadBytes);
          remLength += willMessagePayloadBytes.byteLength + 2;
        }

        if (this.userName != undefined) remLength += UTF8Length(this.userName) + 2;
        if (this.password != undefined) remLength += UTF8Length(this.password) + 2;
        break;
      // Subscribe, Unsubscribe can both contain topic strings

      case MESSAGE_TYPE.SUBSCRIBE:
        first |= 0x02; // Qos = 1;

        for (var i = 0; i < this.topics.length; i++) {
          topicStrLength[i] = UTF8Length(this.topics[i]);
          remLength += topicStrLength[i] + 2;
        }

        remLength += this.requestedQos.length; // 1 byte for each topic's Qos
        // QoS on Subscribe only

        break;

      case MESSAGE_TYPE.UNSUBSCRIBE:
        first |= 0x02; // Qos = 1;

        for (var i = 0; i < this.topics.length; i++) {
          topicStrLength[i] = UTF8Length(this.topics[i]);
          remLength += topicStrLength[i] + 2;
        }

        break;

      case MESSAGE_TYPE.PUBREL:
        first |= 0x02; // Qos = 1;

        break;

      case MESSAGE_TYPE.PUBLISH:
        if (this.payloadMessage.duplicate) first |= 0x08;
        first = first |= this.payloadMessage.qos << 1;
        if (this.payloadMessage.retained) first |= 0x01;
        destinationNameLength = UTF8Length(this.payloadMessage.destinationName);
        remLength += destinationNameLength + 2;
        var payloadBytes = this.payloadMessage.payloadBytes;
        remLength += payloadBytes.byteLength;
        if (payloadBytes instanceof ArrayBuffer) payloadBytes = new Uint8Array(payloadBytes);else if (!(payloadBytes instanceof Uint8Array)) payloadBytes = new Uint8Array(payloadBytes.buffer);
        break;

      case MESSAGE_TYPE.DISCONNECT:
        break;

      default:
        ;
    } // Now we can allocate a buffer for the message


    var mbi = encodeMBI(remLength); // Convert the length to MQTT MBI format

    var pos = mbi.length + 1; // Offset of start of variable header

    var buffer = new ArrayBuffer(remLength + pos);
    var byteStream = new Uint8Array(buffer); // view it as a sequence of bytes
    //Write the fixed header into the buffer

    byteStream[0] = first;
    byteStream.set(mbi, 1); // If this is a PUBLISH then the variable header starts with a topic

    if (this.type == MESSAGE_TYPE.PUBLISH) pos = writeString(this.payloadMessage.destinationName, destinationNameLength, byteStream, pos); // If this is a CONNECT then the variable header contains the protocol name/version, flags and keepalive time
    else if (this.type == MESSAGE_TYPE.CONNECT) {
      switch (this.mqttVersion) {
        case 3:
          byteStream.set(MqttProtoIdentifierv3, pos);
          pos += MqttProtoIdentifierv3.length;
          break;

        case 4:
          byteStream.set(MqttProtoIdentifierv4, pos);
          pos += MqttProtoIdentifierv4.length;
          break;
      }

      var connectFlags = 0;
      if (this.cleanSession) connectFlags = 0x02;

      if (this.willMessage != undefined) {
        connectFlags |= 0x04;
        connectFlags |= this.willMessage.qos << 3;

        if (this.willMessage.retained) {
          connectFlags |= 0x20;
        }
      }

      if (this.userName != undefined) connectFlags |= 0x80;
      if (this.password != undefined) connectFlags |= 0x40;
      byteStream[pos++] = connectFlags;
      pos = writeUint16(this.keepAliveInterval, byteStream, pos);
    } // Output the messageIdentifier - if there is one

    if (this.messageIdentifier != undefined) pos = writeUint16(this.messageIdentifier, byteStream, pos);

    switch (this.type) {
      case MESSAGE_TYPE.CONNECT:
        pos = writeString(this.clientId, UTF8Length(this.clientId), byteStream, pos);

        if (this.willMessage != undefined) {
          pos = writeString(this.willMessage.destinationName, UTF8Length(this.willMessage.destinationName), byteStream, pos);
          pos = writeUint16(willMessagePayloadBytes.byteLength, byteStream, pos);
          byteStream.set(willMessagePayloadBytes, pos);
          pos += willMessagePayloadBytes.byteLength;
        }

        if (this.userName != undefined) pos = writeString(this.userName, UTF8Length(this.userName), byteStream, pos);
        if (this.password != undefined) pos = writeString(this.password, UTF8Length(this.password), byteStream, pos);
        break;

      case MESSAGE_TYPE.PUBLISH:
        // PUBLISH has a text or binary payload, if text do not add a 2 byte length field, just the UTF characters.	
        byteStream.set(payloadBytes, pos);
        break;
      //    	    case MESSAGE_TYPE.PUBREC:	
      //    	    case MESSAGE_TYPE.PUBREL:	
      //    	    case MESSAGE_TYPE.PUBCOMP:	
      //    	    	break;

      case MESSAGE_TYPE.SUBSCRIBE:
        // SUBSCRIBE has a list of topic strings and request QoS
        for (var i = 0; i < this.topics.length; i++) {
          pos = writeString(this.topics[i], topicStrLength[i], byteStream, pos);
          byteStream[pos++] = this.requestedQos[i];
        }

        break;

      case MESSAGE_TYPE.UNSUBSCRIBE:
        // UNSUBSCRIBE has a list of topic strings
        for (var i = 0; i < this.topics.length; i++) {
          pos = writeString(this.topics[i], topicStrLength[i], byteStream, pos);
        }

        break;

      default: // Do nothing.

    }

    return buffer;
  };

  function decodeMessage(input, pos) {
    var startingPos = pos;
    var first = input[pos];
    var type = first >> 4;
    var messageInfo = first &= 0x0f;
    pos += 1; // Decode the remaining length (MBI format)

    var digit;
    var remLength = 0;
    var multiplier = 1;

    do {
      if (pos == input.length) {
        return [null, startingPos];
      }

      digit = input[pos++];
      remLength += (digit & 0x7F) * multiplier;
      multiplier *= 128;
    } while ((digit & 0x80) != 0);

    var endPos = pos + remLength;

    if (endPos > input.length) {
      return [null, startingPos];
    }

    var wireMessage = new WireMessage(type);

    switch (type) {
      case MESSAGE_TYPE.CONNACK:
        var connectAcknowledgeFlags = input[pos++];
        if (connectAcknowledgeFlags & 0x01) wireMessage.sessionPresent = true;
        wireMessage.returnCode = input[pos++];
        break;

      case MESSAGE_TYPE.PUBLISH:
        var qos = messageInfo >> 1 & 0x03;
        var len = readUint16(input, pos);
        pos += 2;
        var topicName = parseUTF8(input, pos, len);
        pos += len; // If QoS 1 or 2 there will be a messageIdentifier

        if (qos > 0) {
          wireMessage.messageIdentifier = readUint16(input, pos);
          pos += 2;
        }

        var message = new Paho.MQTT.Message(input.subarray(pos, endPos));
        if ((messageInfo & 0x01) == 0x01) message.retained = true;
        if ((messageInfo & 0x08) == 0x08) message.duplicate = true;
        message.qos = qos;
        message.destinationName = topicName;
        wireMessage.payloadMessage = message;
        break;

      case MESSAGE_TYPE.PUBACK:
      case MESSAGE_TYPE.PUBREC:
      case MESSAGE_TYPE.PUBREL:
      case MESSAGE_TYPE.PUBCOMP:
      case MESSAGE_TYPE.UNSUBACK:
        wireMessage.messageIdentifier = readUint16(input, pos);
        break;

      case MESSAGE_TYPE.SUBACK:
        wireMessage.messageIdentifier = readUint16(input, pos);
        pos += 2;
        wireMessage.returnCode = input.subarray(pos, endPos);
        break;

      default:
        ;
    }

    return [wireMessage, endPos];
  }

  function writeUint16(input, buffer, offset) {
    buffer[offset++] = input >> 8; //MSB

    buffer[offset++] = input % 256; //LSB 

    return offset;
  }

  function writeString(input, utf8Length, buffer, offset) {
    offset = writeUint16(utf8Length, buffer, offset);
    stringToUTF8(input, buffer, offset);
    return offset + utf8Length;
  }

  function readUint16(buffer, offset) {
    return 256 * buffer[offset] + buffer[offset + 1];
  }
  /**
   * Encodes an MQTT Multi-Byte Integer
   * @private 
   */


  function encodeMBI(number) {
    var output = new Array(1);
    var numBytes = 0;

    do {
      var digit = number % 128;
      number = number >> 7;

      if (number > 0) {
        digit |= 0x80;
      }

      output[numBytes++] = digit;
    } while (number > 0 && numBytes < 4);

    return output;
  }
  /**
   * Takes a String and calculates its length in bytes when encoded in UTF8.
   * @private
   */


  function UTF8Length(input) {
    var output = 0;

    for (var i = 0; i < input.length; i++) {
      var charCode = input.charCodeAt(i);

      if (charCode > 0x7FF) {
        // Surrogate pair means its a 4 byte character
        if (0xD800 <= charCode && charCode <= 0xDBFF) {
          i++;
          output++;
        }

        output += 3;
      } else if (charCode > 0x7F) output += 2;else output++;
    }

    return output;
  }
  /**
   * Takes a String and writes it into an array as UTF8 encoded bytes.
   * @private
   */


  function stringToUTF8(input, output, start) {
    var pos = start;

    for (var i = 0; i < input.length; i++) {
      var charCode = input.charCodeAt(i); // Check for a surrogate pair.

      if (0xD800 <= charCode && charCode <= 0xDBFF) {
        var lowCharCode = input.charCodeAt(++i);

        if (isNaN(lowCharCode)) {
          throw new Error(format(ERROR.MALFORMED_UNICODE, [charCode, lowCharCode]));
        }

        charCode = (charCode - 0xD800 << 10) + (lowCharCode - 0xDC00) + 0x10000;
      }

      if (charCode <= 0x7F) {
        output[pos++] = charCode;
      } else if (charCode <= 0x7FF) {
        output[pos++] = charCode >> 6 & 0x1F | 0xC0;
        output[pos++] = charCode & 0x3F | 0x80;
      } else if (charCode <= 0xFFFF) {
        output[pos++] = charCode >> 12 & 0x0F | 0xE0;
        output[pos++] = charCode >> 6 & 0x3F | 0x80;
        output[pos++] = charCode & 0x3F | 0x80;
      } else {
        output[pos++] = charCode >> 18 & 0x07 | 0xF0;
        output[pos++] = charCode >> 12 & 0x3F | 0x80;
        output[pos++] = charCode >> 6 & 0x3F | 0x80;
        output[pos++] = charCode & 0x3F | 0x80;
      }

      ;
    }

    return output;
  }

  function parseUTF8(input, offset, length) {
    var output = "";
    var utf16;
    var pos = offset;

    while (pos < offset + length) {
      var byte1 = input[pos++];
      if (byte1 < 128) utf16 = byte1;else {
        var byte2 = input[pos++] - 128;
        if (byte2 < 0) throw new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), ""]));
        if (byte1 < 0xE0) // 2 byte character
          utf16 = 64 * (byte1 - 0xC0) + byte2;else {
          var byte3 = input[pos++] - 128;
          if (byte3 < 0) throw new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16)]));
          if (byte1 < 0xF0) // 3 byte character
            utf16 = 4096 * (byte1 - 0xE0) + 64 * byte2 + byte3;else {
            var byte4 = input[pos++] - 128;
            if (byte4 < 0) throw new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16), byte4.toString(16)]));
            if (byte1 < 0xF8) // 4 byte character 
              utf16 = 262144 * (byte1 - 0xF0) + 4096 * byte2 + 64 * byte3 + byte4;else // longer encodings are not supported  
              throw new Error(format(ERROR.MALFORMED_UTF, [byte1.toString(16), byte2.toString(16), byte3.toString(16), byte4.toString(16)]));
          }
        }
      }

      if (utf16 > 0xFFFF) // 4 byte character - express as a surrogate pair
        {
          utf16 -= 0x10000;
          output += String.fromCharCode(0xD800 + (utf16 >> 10)); // lead character

          utf16 = 0xDC00 + (utf16 & 0x3FF); // trail character
        }

      output += String.fromCharCode(utf16);
    }

    return output;
  }
  /** 
   * Repeat keepalive requests, monitor responses.
   * @ignore
   */


  var Pinger = function Pinger(client, window, keepAliveInterval) {
    this._client = client;
    this._window = window;
    this._keepAliveInterval = keepAliveInterval * 1000;
    this._state = {
      lastPingResponse: Date.now()
    };
    var pingReq = new WireMessage(MESSAGE_TYPE.PINGREQ).encode();

    var doTimeout = function doTimeout(pinger) {
      return function () {
        return doPing.apply(pinger);
      };
    };
    /** @ignore */


    var doPing = function doPing() {
      var now = Date.now(); // 1.5倍是为了给网络延迟留一定余地

      if (now - this._state.lastPingResponse > this._keepAliveInterval * 1.5) {
        this._client._trace("Pinger.doPing", "Timed out");

        this._client._disconnected(ERROR.PING_TIMEOUT.code, format(ERROR.PING_TIMEOUT));

        return;
      }

      this._client._trace("Pinger.doPing", "send PINGREQ 1");

      this._client.socket.send(pingReq);

      if (this.timeout) {
        ut.clearTimeout(this.timeout);
        this.timeout = null;
      }

      this.timeout = ut.setTimeout(doTimeout(this), this._keepAliveInterval);
    };

    this.ping = function () {
      this._client._trace("Pinger.doPing", "send PINGREQ 2");

      this._client.socket.send(pingReq);

      this.reset();
    };

    this.reset = function () {
      if (this.timeout) {
        ut.clearTimeout(this.timeout);
        this.timeout = null;
      }

      this._state.lastPingResponse = Date.now();
      this.timeout = ut.setTimeout(doTimeout(this), this._keepAliveInterval);
    };

    this.cancel = function () {
      ut.clearTimeout(this.timeout);
    };

    this.setKeepAliveInterval = function (keepAliveInterval) {
      this._keepAliveInterval = keepAliveInterval * 1000;
    };
  };
  /**
   * Monitor request completion.
   * @ignore
   */


  var Timeout = function Timeout(client, window, timeoutSeconds, action, args) {
    this._window = window;
    if (!timeoutSeconds) timeoutSeconds = 30;

    var doTimeout = function doTimeout(action, client, args) {
      return function () {
        return action.apply(client, args);
      };
    };

    this.timeout = ut.setTimeout(doTimeout(action, client, args), timeoutSeconds * 1000);

    this.cancel = function () {
      ut.clearTimeout(this.timeout);
    };
  };
  /*
   * Internal implementation of the Websockets MQTT V3.1 client.
   * 
   * @name Paho.MQTT.ClientImpl @constructor 
   * @param {String} host the DNS nameof the webSocket host. 
   * @param {Number} port the port number for that host.
   * @param {String} clientId the MQ client identifier.
   */


  var ClientImpl = function ClientImpl(uri, host, port, path, clientId) {
    // Check dependencies are satisfied in this browser.
    if (!("WebSocket" in global && global["WebSocket"] !== null)) {
      throw new Error(format(ERROR.UNSUPPORTED, ["WebSocket"]));
    }

    if (!("localStorage" in global && global["localStorage"] !== null)) {
      throw new Error(format(ERROR.UNSUPPORTED, ["localStorage"]));
    }

    if (!("ArrayBuffer" in global && global["ArrayBuffer"] !== null)) {
      throw new Error(format(ERROR.UNSUPPORTED, ["ArrayBuffer"]));
    }

    this._trace("Paho.MQTT.Client", uri, host, port, path, clientId);

    this.host = host;
    this.port = port;
    this.path = path;
    this.uri = uri;
    this.clientId = clientId; // Local storagekeys are qualified with the following string.
    // The conditional inclusion of path in the key is for backward
    // compatibility to when the path was not configurable and assumed to
    // be /mqtt

    this._localKey = host + ":" + port + (path != "/mqtt" ? ":" + path : "") + ":" + clientId + ":"; // Create private instance-only message queue
    // Internal queue of messages to be sent, in sending order. 

    this._msg_queue = []; // Messages we have sent and are expecting a response for, indexed by their respective message ids. 

    this._sentMessages = {}; // Messages we have received and acknowleged and are expecting a confirm message for
    // indexed by their respective message ids. 

    this._receivedMessages = {}; // Internal list of callbacks to be executed when messages
    // have been successfully sent over web socket, e.g. disconnect
    // when it doesn't have to wait for ACK, just message is dispatched.

    this._notify_msg_sent = {}; // Unique identifier for SEND messages, incrementing
    // counter as messages are sent.

    this._message_identifier = 1; // Used to determine the transmission sequence of stored sent messages.

    this._sequence = 0; // Load the local state, if any, from the saved version, only restore state relevant to this client.   	

    for (var key in localStorage) {
      if (key.indexOf("Sent:" + this._localKey) == 0 || key.indexOf("Received:" + this._localKey) == 0) this.restore(key);
    }
  }; // Messaging Client public instance members. 


  ClientImpl.prototype.host;
  ClientImpl.prototype.port;
  ClientImpl.prototype.path;
  ClientImpl.prototype.uri;
  ClientImpl.prototype.clientId; // Messaging Client private instance members.

  ClientImpl.prototype.socket;
  /* true once we have received an acknowledgement to a CONNECT packet. */

  ClientImpl.prototype.connected = false;
  /* The largest message identifier allowed, may not be larger than 2**16 but 
   * if set smaller reduces the maximum number of outbound messages allowed.
   */

  ClientImpl.prototype.maxMessageIdentifier = 65536;
  ClientImpl.prototype.connectOptions;
  ClientImpl.prototype.hostIndex;
  ClientImpl.prototype.onConnectionLost;
  ClientImpl.prototype.onMessageDelivered;
  ClientImpl.prototype.onMessageArrived;
  ClientImpl.prototype.traceFunction;
  ClientImpl.prototype._msg_queue = null;
  ClientImpl.prototype._connectTimeout;
  /* The sendPinger monitors how long we allow before we send data to prove to the server that we are alive. */

  ClientImpl.prototype.sendPinger = null;
  /* The receivePinger monitors how long we allow before we require evidence that the server is alive. */

  ClientImpl.prototype.receivePinger = null;
  ClientImpl.prototype.receiveBuffer = null;
  ClientImpl.prototype._traceBuffer = null;
  ClientImpl.prototype._MAX_TRACE_ENTRIES = 100;

  ClientImpl.prototype.connect = function (connectOptions) {
    var connectOptionsMasked = this._traceMask(connectOptions, "password");

    this._trace("Client.connect", connectOptionsMasked, this.socket, this.connected);

    if (this.connected) throw new Error(format(ERROR.INVALID_STATE, ["already connected"]));
    if (this.socket) throw new Error(format(ERROR.INVALID_STATE, ["already connected"]));
    this.connectOptions = connectOptions;

    if (connectOptions.uris) {
      this.hostIndex = 0;

      this._doConnect(connectOptions.uris[0]);
    } else {
      this._doConnect(this.uri);
    }
  };

  ClientImpl.prototype.subscribe = function (filter, subscribeOptions) {
    this._trace("Client.subscribe", filter, subscribeOptions);

    if (!this.connected) throw new Error(format(ERROR.INVALID_STATE, ["not connected"]));
    var wireMessage = new WireMessage(MESSAGE_TYPE.SUBSCRIBE);
    wireMessage.topics = [filter];
    if (subscribeOptions.qos != undefined) wireMessage.requestedQos = [subscribeOptions.qos];else wireMessage.requestedQos = [0];

    if (subscribeOptions.onSuccess) {
      wireMessage.onSuccess = function (grantedQos) {
        subscribeOptions.onSuccess({
          invocationContext: subscribeOptions.invocationContext,
          grantedQos: grantedQos
        });
      };
    }

    if (subscribeOptions.onFailure) {
      wireMessage.onFailure = function (errorCode) {
        subscribeOptions.onFailure({
          invocationContext: subscribeOptions.invocationContext,
          errorCode: errorCode
        });
      };
    }

    if (subscribeOptions.timeout) {
      wireMessage.timeOut = new Timeout(this, window, subscribeOptions.timeout, subscribeOptions.onFailure, [{
        invocationContext: subscribeOptions.invocationContext,
        errorCode: ERROR.SUBSCRIBE_TIMEOUT.code,
        errorMessage: format(ERROR.SUBSCRIBE_TIMEOUT)
      }]);
    } // All subscriptions return a SUBACK. 


    this._requires_ack(wireMessage);

    this._schedule_message(wireMessage);
  };
  /** @ignore */


  ClientImpl.prototype.unsubscribe = function (filter, unsubscribeOptions) {
    this._trace("Client.unsubscribe", filter, unsubscribeOptions);

    if (!this.connected) throw new Error(format(ERROR.INVALID_STATE, ["not connected"]));
    var wireMessage = new WireMessage(MESSAGE_TYPE.UNSUBSCRIBE);
    wireMessage.topics = [filter];

    if (unsubscribeOptions.onSuccess) {
      wireMessage.callback = function () {
        unsubscribeOptions.onSuccess({
          invocationContext: unsubscribeOptions.invocationContext
        });
      };
    }

    if (unsubscribeOptions.timeout) {
      wireMessage.timeOut = new Timeout(this, window, unsubscribeOptions.timeout, unsubscribeOptions.onFailure, [{
        invocationContext: unsubscribeOptions.invocationContext,
        errorCode: ERROR.UNSUBSCRIBE_TIMEOUT.code,
        errorMessage: format(ERROR.UNSUBSCRIBE_TIMEOUT)
      }]);
    } // All unsubscribes return a SUBACK.         


    this._requires_ack(wireMessage);

    this._schedule_message(wireMessage);
  };

  ClientImpl.prototype.send = function (message) {
    this._trace("Client.send", message);

    if (!this.connected) throw new Error(format(ERROR.INVALID_STATE, ["not connected"]));
    var wireMessage = new WireMessage(MESSAGE_TYPE.PUBLISH);
    wireMessage.payloadMessage = message;
    if (message.qos > 0) this._requires_ack(wireMessage);else if (this.onMessageDelivered) this._notify_msg_sent[wireMessage] = this.onMessageDelivered(wireMessage.payloadMessage);

    this._schedule_message(wireMessage);
  };

  ClientImpl.prototype.disconnect = function () {
    this._trace("Client.disconnect");

    if (!this.socket) throw new Error(format(ERROR.INVALID_STATE, ["not connecting or connected"]));
    var wireMessage = new WireMessage(MESSAGE_TYPE.DISCONNECT); // Run the disconnected call back as soon as the message has been sent,
    // in case of a failure later on in the disconnect processing.
    // as a consequence, the _disconected call back may be run several times.

    this._notify_msg_sent[wireMessage] = scope(this._disconnected, this);

    this._schedule_message(wireMessage);
  };

  ClientImpl.prototype.getTraceLog = function () {
    if (this._traceBuffer !== null) {
      this._trace("Client.getTraceLog", new Date());

      this._trace("Client.getTraceLog in flight messages", this._sentMessages.length);

      for (var key in this._sentMessages) {
        this._trace("_sentMessages ", key, this._sentMessages[key]);
      }

      for (var key in this._receivedMessages) {
        this._trace("_receivedMessages ", key, this._receivedMessages[key]);
      }

      return this._traceBuffer;
    }
  };

  ClientImpl.prototype.startTrace = function () {
    if (this._traceBuffer === null) {
      this._traceBuffer = [];
    }

    this._trace("Client.startTrace", new Date(), version);
  };

  ClientImpl.prototype.stopTrace = function () {
    delete this._traceBuffer;
  };

  ClientImpl.prototype._doConnect = function (wsurl) {
    // When the socket is open, this client will send the CONNECT WireMessage using the saved parameters. 
    if (this.connectOptions.useSSL) {
      var uriParts = wsurl.split(":");
      uriParts[0] = "wss";
      wsurl = uriParts.join(":");
    }

    this.connected = false;

    if (cc.sys.isNative && cc.sys.os !== cc.sys.OS_IOS) {
      var url = "resources/cert/cacert.pem";

      var cacertUrl = cc.assetManager._transform({
        'path': cc.path.changeExtname(url.substring(10)),
        bundle: cc.AssetManager.BuiltinBundleName.RESOURCES,
        __isNative__: true,
        ext: cc.path.extname(url)
      });

      this.socket = new WebSocket(wsurl, null, cacertUrl);
    } else if (this.connectOptions.mqttVersion < 4) {
      this.socket = new WebSocket(wsurl, ["mqttv3.1"]);
    } else {
      this.socket = new WebSocket(wsurl, ["mqtt"]);
    }

    this.socket.binaryType = 'arraybuffer';
    this.socket.onopen = scope(this._on_socket_open, this);
    this.socket.onmessage = scope(this._on_socket_message, this);
    this.socket.onerror = scope(this._on_socket_error, this);
    this.socket.onclose = scope(this._on_socket_close, this);
    this.sendPinger = new Pinger(this, window, this.connectOptions.keepAliveInterval);
    this.receivePinger = new Pinger(this, window, this.connectOptions.keepAliveInterval);
    this._connectTimeout = new Timeout(this, window, this.connectOptions.timeout, this._disconnected, [ERROR.CONNECT_TIMEOUT.code, format(ERROR.CONNECT_TIMEOUT)]);
  }; // Schedule a new message to be sent over the WebSockets
  // connection. CONNECT messages cause WebSocket connection
  // to be started. All other messages are queued internally
  // until this has happened. When WS connection starts, process
  // all outstanding messages. 


  ClientImpl.prototype._schedule_message = function (message) {
    this._msg_queue.push(message); // Process outstanding messages in the queue if we have an  open socket, and have received CONNACK. 


    if (this.connected) {
      this._process_queue();
    }
  };

  ClientImpl.prototype.store = function (prefix, wireMessage) {
    var storedMessage = {
      type: wireMessage.type,
      messageIdentifier: wireMessage.messageIdentifier,
      version: 1
    };

    switch (wireMessage.type) {
      case MESSAGE_TYPE.PUBLISH:
        if (wireMessage.pubRecReceived) storedMessage.pubRecReceived = true; // Convert the payload to a hex string.

        storedMessage.payloadMessage = {};
        var hex = "";
        var messageBytes = wireMessage.payloadMessage.payloadBytes;

        for (var i = 0; i < messageBytes.length; i++) {
          if (messageBytes[i] <= 0xF) hex = hex + "0" + messageBytes[i].toString(16);else hex = hex + messageBytes[i].toString(16);
        }

        storedMessage.payloadMessage.payloadHex = hex;
        storedMessage.payloadMessage.qos = wireMessage.payloadMessage.qos;
        storedMessage.payloadMessage.destinationName = wireMessage.payloadMessage.destinationName;
        if (wireMessage.payloadMessage.duplicate) storedMessage.payloadMessage.duplicate = true;
        if (wireMessage.payloadMessage.retained) storedMessage.payloadMessage.retained = true; // Add a sequence number to sent messages.

        if (prefix.indexOf("Sent:") == 0) {
          if (wireMessage.sequence === undefined) wireMessage.sequence = ++this._sequence;
          storedMessage.sequence = wireMessage.sequence;
        }

        break;

      default:
        throw Error(format(ERROR.INVALID_STORED_DATA, [key, storedMessage]));
    }

    localStorage.setItem(prefix + this._localKey + wireMessage.messageIdentifier, JSON.stringify(storedMessage));
  };

  ClientImpl.prototype.restore = function (key) {
    var value = localStorage.getItem(key);
    var storedMessage = JSON.parse(value);
    var wireMessage = new WireMessage(storedMessage.type, storedMessage);

    switch (storedMessage.type) {
      case MESSAGE_TYPE.PUBLISH:
        // Replace the payload message with a Message object.
        var hex = storedMessage.payloadMessage.payloadHex;
        var buffer = new ArrayBuffer(hex.length / 2);
        var byteStream = new Uint8Array(buffer);
        var i = 0;

        while (hex.length >= 2) {
          var x = parseInt(hex.substring(0, 2), 16);
          hex = hex.substring(2, hex.length);
          byteStream[i++] = x;
        }

        var payloadMessage = new Paho.MQTT.Message(byteStream);
        payloadMessage.qos = storedMessage.payloadMessage.qos;
        payloadMessage.destinationName = storedMessage.payloadMessage.destinationName;
        if (storedMessage.payloadMessage.duplicate) payloadMessage.duplicate = true;
        if (storedMessage.payloadMessage.retained) payloadMessage.retained = true;
        wireMessage.payloadMessage = payloadMessage;
        break;

      default:
        throw Error(format(ERROR.INVALID_STORED_DATA, [key, value]));
    }

    if (key.indexOf("Sent:" + this._localKey) == 0) {
      wireMessage.payloadMessage.duplicate = true;
      this._sentMessages[wireMessage.messageIdentifier] = wireMessage;
    } else if (key.indexOf("Received:" + this._localKey) == 0) {
      this._receivedMessages[wireMessage.messageIdentifier] = wireMessage;
    }
  };

  ClientImpl.prototype._process_queue = function () {
    var message = null; // Process messages in order they were added

    var fifo = this._msg_queue.reverse(); // Send all queued messages down socket connection


    while (message = fifo.pop()) {
      this._socket_send(message); // Notify listeners that message was successfully sent


      if (this._notify_msg_sent[message]) {
        this._notify_msg_sent[message]();

        delete this._notify_msg_sent[message];
      }
    }
  };
  /**
   * Expect an ACK response for this message. Add message to the set of in progress
   * messages and set an unused identifier in this message.
   * @ignore
   */


  ClientImpl.prototype._requires_ack = function (wireMessage) {
    var messageCount = Object.keys(this._sentMessages).length;
    if (messageCount > this.maxMessageIdentifier) throw Error("Too many messages:" + messageCount);

    while (this._sentMessages[this._message_identifier] !== undefined) {
      this._message_identifier++;
    }

    wireMessage.messageIdentifier = this._message_identifier;
    this._sentMessages[wireMessage.messageIdentifier] = wireMessage;

    if (wireMessage.type === MESSAGE_TYPE.PUBLISH) {
      this.store("Sent:", wireMessage);
    }

    if (this._message_identifier === this.maxMessageIdentifier) {
      this._message_identifier = 1;
    }
  };
  /** 
   * Called when the underlying websocket has been opened.
   * @ignore
   */


  ClientImpl.prototype._on_socket_open = function () {
    // Create the CONNECT message object.
    var wireMessage = new WireMessage(MESSAGE_TYPE.CONNECT, this.connectOptions);
    wireMessage.clientId = this.clientId;

    this._socket_send(wireMessage);
  };
  /** 
   * Called when the underlying websocket has received a complete packet.
   * @ignore
   */


  ClientImpl.prototype._on_socket_message = function (event) {
    this._trace("Client._on_socket_message", event.data); // Reset the receive ping timer, we now have evidence the server is alive.
    // this.receivePinger.reset();


    var messages = this._deframeMessages(event.data);

    for (var i = 0; i < messages.length; i += 1) {
      this._handleMessage(messages[i]);
    }
  };

  ClientImpl.prototype._deframeMessages = function (data) {
    var byteArray = new Uint8Array(data);

    if (this.receiveBuffer) {
      var newData = new Uint8Array(this.receiveBuffer.length + byteArray.length);
      newData.set(this.receiveBuffer);
      newData.set(byteArray, this.receiveBuffer.length);
      byteArray = newData;
      delete this.receiveBuffer;
    }

    try {
      var offset = 0;
      var messages = [];

      while (offset < byteArray.length) {
        var result = decodeMessage(byteArray, offset);
        var wireMessage = result[0];
        offset = result[1];

        if (wireMessage !== null) {
          messages.push(wireMessage);
        } else {
          break;
        }
      }

      if (offset < byteArray.length) {
        this.receiveBuffer = byteArray.subarray(offset);
      }
    } catch (error) {
      this._disconnected(ERROR.INTERNAL_ERROR.code, format(ERROR.INTERNAL_ERROR, [error.message, error.stack.toString()]));

      return;
    }

    return messages;
  };

  ClientImpl.prototype._handleMessage = function (wireMessage) {
    this._trace("Client._handleMessage", wireMessage); // try {


    switch (wireMessage.type) {
      case MESSAGE_TYPE.CONNACK:
        this._connectTimeout.cancel(); // If we have started using clean session then clear up the local state.


        if (this.connectOptions.cleanSession) {
          for (var key in this._sentMessages) {
            var sentMessage = this._sentMessages[key];
            localStorage.removeItem("Sent:" + this._localKey + sentMessage.messageIdentifier);
          }

          this._sentMessages = {};

          for (var key in this._receivedMessages) {
            var receivedMessage = this._receivedMessages[key];
            localStorage.removeItem("Received:" + this._localKey + receivedMessage.messageIdentifier);
          }

          this._receivedMessages = {};
        } // Client connected and ready for business.


        if (wireMessage.returnCode === 0) {
          this.connected = true; // Jump to the end of the list of uris and stop looking for a good host.

          if (this.connectOptions.uris) this.hostIndex = this.connectOptions.uris.length;
        } else {
          this._disconnected(ERROR.CONNACK_RETURNCODE.code, format(ERROR.CONNACK_RETURNCODE, [wireMessage.returnCode, CONNACK_RC[wireMessage.returnCode]]));

          break;
        } // Resend messages.


        var sequencedMessages = new Array();

        for (var msgId in this._sentMessages) {
          if (this._sentMessages.hasOwnProperty(msgId)) sequencedMessages.push(this._sentMessages[msgId]);
        } // Sort sentMessages into the original sent order.


        var sequencedMessages = sequencedMessages.sort(function (a, b) {
          return a.sequence - b.sequence;
        });

        for (var i = 0, len = sequencedMessages.length; i < len; i++) {
          var sentMessage = sequencedMessages[i];

          if (sentMessage.type == MESSAGE_TYPE.PUBLISH && sentMessage.pubRecReceived) {
            var pubRelMessage = new WireMessage(MESSAGE_TYPE.PUBREL, {
              messageIdentifier: sentMessage.messageIdentifier
            });

            this._schedule_message(pubRelMessage);
          } else {
            this._schedule_message(sentMessage);
          }

          ;
        } // Execute the connectOptions.onSuccess callback if there is one.


        if (this.connectOptions.onSuccess) {
          this.connectOptions.onSuccess({
            invocationContext: this.connectOptions.invocationContext
          });
        } // Process all queued messages now that the connection is established. 


        this._process_queue();

        break;

      case MESSAGE_TYPE.PUBLISH:
        this._receivePublish(wireMessage);

        break;

      case MESSAGE_TYPE.PUBACK:
        var sentMessage = this._sentMessages[wireMessage.messageIdentifier]; // If this is a re flow of a PUBACK after we have restarted receivedMessage will not exist.

        if (sentMessage) {
          delete this._sentMessages[wireMessage.messageIdentifier];
          localStorage.removeItem("Sent:" + this._localKey + wireMessage.messageIdentifier);
          if (this.onMessageDelivered) this.onMessageDelivered(sentMessage.payloadMessage);
        }

        break;

      case MESSAGE_TYPE.PUBREC:
        var sentMessage = this._sentMessages[wireMessage.messageIdentifier]; // If this is a re flow of a PUBREC after we have restarted receivedMessage will not exist.

        if (sentMessage) {
          sentMessage.pubRecReceived = true;
          var pubRelMessage = new WireMessage(MESSAGE_TYPE.PUBREL, {
            messageIdentifier: wireMessage.messageIdentifier
          });
          this.store("Sent:", sentMessage);

          this._schedule_message(pubRelMessage);
        }

        break;

      case MESSAGE_TYPE.PUBREL:
        var receivedMessage = this._receivedMessages[wireMessage.messageIdentifier];
        localStorage.removeItem("Received:" + this._localKey + wireMessage.messageIdentifier); // If this is a re flow of a PUBREL after we have restarted receivedMessage will not exist.

        if (receivedMessage) {
          this._receiveMessage(receivedMessage);

          delete this._receivedMessages[wireMessage.messageIdentifier];
        } // Always flow PubComp, we may have previously flowed PubComp but the server lost it and restarted.


        var pubCompMessage = new WireMessage(MESSAGE_TYPE.PUBCOMP, {
          messageIdentifier: wireMessage.messageIdentifier
        });

        this._schedule_message(pubCompMessage);

        break;

      case MESSAGE_TYPE.PUBCOMP:
        var sentMessage = this._sentMessages[wireMessage.messageIdentifier];
        delete this._sentMessages[wireMessage.messageIdentifier];
        localStorage.removeItem("Sent:" + this._localKey + wireMessage.messageIdentifier);
        if (this.onMessageDelivered) this.onMessageDelivered(sentMessage.payloadMessage);
        break;

      case MESSAGE_TYPE.SUBACK:
        var sentMessage = this._sentMessages[wireMessage.messageIdentifier];

        if (sentMessage) {
          if (sentMessage.timeOut) sentMessage.timeOut.cancel();
          wireMessage.returnCode.indexOf = Array.prototype.indexOf;

          if (wireMessage.returnCode.indexOf(0x80) !== -1) {
            if (sentMessage.onFailure) {
              sentMessage.onFailure(wireMessage.returnCode);
            }
          } else if (sentMessage.onSuccess) {
            sentMessage.onSuccess(wireMessage.returnCode);
          }

          delete this._sentMessages[wireMessage.messageIdentifier];
        }

        break;

      case MESSAGE_TYPE.UNSUBACK:
        var sentMessage = this._sentMessages[wireMessage.messageIdentifier];

        if (sentMessage) {
          if (sentMessage.timeOut) sentMessage.timeOut.cancel();

          if (sentMessage.callback) {
            sentMessage.callback();
          }

          delete this._sentMessages[wireMessage.messageIdentifier];
        }

        break;

      case MESSAGE_TYPE.PINGRESP:
        /* The sendPinger or receivePinger may have sent a ping, the receivePinger has already been reset. */
        this.sendPinger.reset();
        break;

      case MESSAGE_TYPE.DISCONNECT:
        // Clients do not expect to receive disconnect packets.
        this._disconnected(ERROR.INVALID_MQTT_MESSAGE_TYPE.code, format(ERROR.INVALID_MQTT_MESSAGE_TYPE, [wireMessage.type]));

        break;

      default:
        this._disconnected(ERROR.INVALID_MQTT_MESSAGE_TYPE.code, format(ERROR.INVALID_MQTT_MESSAGE_TYPE, [wireMessage.type]));

    }

    ; // } catch (error) {
    // 	this._disconnected(ERROR.INTERNAL_ERROR.code , format(ERROR.INTERNAL_ERROR, [error.message,error.stack.toString()]));
    // 	return;
    // }
  };
  /** @ignore */


  ClientImpl.prototype._on_socket_error = function (error) {
    this._disconnected(ERROR.SOCKET_ERROR.code, format(ERROR.SOCKET_ERROR, [error.data]));
  };
  /** @ignore */


  ClientImpl.prototype._on_socket_close = function () {
    this._disconnected(ERROR.SOCKET_CLOSE.code, format(ERROR.SOCKET_CLOSE));
  };
  /** @ignore */


  ClientImpl.prototype._socket_send = function (wireMessage) {
    if (wireMessage.type == 1) {
      var wireMessageMasked = this._traceMask(wireMessage, "password");

      this._trace("Client._socket_send", wireMessageMasked);
    } else this._trace("Client._socket_send", wireMessage);

    this.socket.send(wireMessage.encode());
    /* We have proved to the server we are alive. */

    if (wireMessage.type != MESSAGE_TYPE.PINGREQ) {
      this.sendPinger.reset();
    }
  };
  /** @ignore */


  ClientImpl.prototype._receivePublish = function (wireMessage) {
    switch (wireMessage.payloadMessage.qos) {
      case "undefined":
      case 0:
        this._receiveMessage(wireMessage);

        break;

      case 1:
        var pubAckMessage = new WireMessage(MESSAGE_TYPE.PUBACK, {
          messageIdentifier: wireMessage.messageIdentifier
        });

        this._schedule_message(pubAckMessage);

        this._receiveMessage(wireMessage);

        break;

      case 2:
        this._receivedMessages[wireMessage.messageIdentifier] = wireMessage;
        this.store("Received:", wireMessage);
        var pubRecMessage = new WireMessage(MESSAGE_TYPE.PUBREC, {
          messageIdentifier: wireMessage.messageIdentifier
        });

        this._schedule_message(pubRecMessage);

        break;

      default:
        throw Error("Invaild qos=" + wireMmessage.payloadMessage.qos);
    }

    ;
  };
  /** @ignore */


  ClientImpl.prototype._receiveMessage = function (wireMessage) {
    if (this.onMessageArrived) {
      this.onMessageArrived(wireMessage.payloadMessage);
    }
  };
  /**
   * Client has disconnected either at its own request or because the server
   * or network disconnected it. Remove all non-durable state.
   * @param {errorCode} [number] the error number.
   * @param {errorText} [string] the error text.
   * @ignore
   */


  ClientImpl.prototype._disconnected = function (errorCode, errorText) {
    this._trace("Client._disconnected", errorCode, errorText);

    this.sendPinger.cancel();
    this.receivePinger.cancel();
    if (this._connectTimeout) this._connectTimeout.cancel(); // Clear message buffers.

    this._msg_queue = [];
    this._notify_msg_sent = {};

    if (this.socket) {
      // Cancel all socket callbacks so that they cannot be driven again by this socket.
      this.socket.onopen = null;
      this.socket.onmessage = null;
      this.socket.onerror = null;
      this.socket.onclose = null;
      if (this.socket.readyState === 1) this.socket.close();
      delete this.socket;
    }

    if (this.connectOptions.uris && this.hostIndex < this.connectOptions.uris.length - 1) {
      // Try the next host.
      this.hostIndex++;

      this._doConnect(this.connectOptions.uris[this.hostIndex]);
    } else {
      if (errorCode === undefined) {
        errorCode = ERROR.OK.code;
        errorText = format(ERROR.OK);
      } // Run any application callbacks last as they may attempt to reconnect and hence create a new socket.


      if (this.connected) {
        this.connected = false; // Execute the connectionLostCallback if there is one, and we were connected.       

        if (this.onConnectionLost) this.onConnectionLost({
          errorCode: errorCode,
          errorMessage: errorText
        });
      } else {
        // Otherwise we never had a connection, so indicate that the connect has failed.
        if (this.connectOptions.mqttVersion === 4 && this.connectOptions.mqttVersionExplicit === false) {
          this._trace("Failed to connect V4, dropping back to V3");

          this.connectOptions.mqttVersion = 3;

          if (this.connectOptions.uris) {
            this.hostIndex = 0;

            this._doConnect(this.connectOptions.uris[0]);
          } else {
            this._doConnect(this.uri);
          }
        } else if (this.connectOptions.onFailure) {
          this.connectOptions.onFailure({
            invocationContext: this.connectOptions.invocationContext,
            errorCode: errorCode,
            errorMessage: errorText
          });
        }
      }
    }
  };
  /** @ignore */


  ClientImpl.prototype._trace = function () {
    // Pass trace message back to client's callback function
    if (this.traceFunction) {
      for (var i in arguments) {
        if (typeof arguments[i] !== "undefined") arguments[i] = JSON.stringify(arguments[i]);
      }

      var record = Array.prototype.slice.call(arguments).join("");
      this.traceFunction({
        severity: "Debug",
        message: record
      });
    } //buffer style trace


    if (this._traceBuffer !== null) {
      for (var i = 0, max = arguments.length; i < max; i++) {
        if (this._traceBuffer.length == this._MAX_TRACE_ENTRIES) {
          this._traceBuffer.shift();
        }

        if (i === 0) this._traceBuffer.push(arguments[i]);else if (typeof arguments[i] === "undefined") this._traceBuffer.push(arguments[i]);else this._traceBuffer.push("  " + JSON.stringify(arguments[i]));
      }

      ;
    }

    ;
  };
  /** @ignore */


  ClientImpl.prototype._traceMask = function (traceObject, masked) {
    var traceObjectMasked = {};

    for (var attr in traceObject) {
      if (traceObject.hasOwnProperty(attr)) {
        if (attr == masked) traceObjectMasked[attr] = "******";else traceObjectMasked[attr] = traceObject[attr];
      }
    }

    return traceObjectMasked;
  }; // ------------------------------------------------------------------------
  // Public Programming interface.
  // ------------------------------------------------------------------------

  /** 
   * The JavaScript application communicates to the server using a {@link Paho.MQTT.Client} object. 
   * <p>
   * Most applications will create just one Client object and then call its connect() method,
   * however applications can create more than one Client object if they wish. 
   * In this case the combination of host, port and clientId attributes must be different for each Client object.
   * <p>
   * The send, subscribe and unsubscribe methods are implemented as asynchronous JavaScript methods 
   * (even though the underlying protocol exchange might be synchronous in nature). 
   * This means they signal their completion by calling back to the application, 
   * via Success or Failure callback functions provided by the application on the method in question. 
   * Such callbacks are called at most once per method invocation and do not persist beyond the lifetime 
   * of the script that made the invocation.
   * <p>
   * In contrast there are some callback functions, most notably <i>onMessageArrived</i>, 
   * that are defined on the {@link Paho.MQTT.Client} object.  
   * These may get called multiple times, and aren't directly related to specific method invocations made by the client. 
   *
   * @name Paho.MQTT.Client    
   * 
   * @constructor
   *  
   * @param {string} host - the address of the messaging server, as a fully qualified WebSocket URI, as a DNS name or dotted decimal IP address.
   * @param {number} port - the port number to connect to - only required if host is not a URI
   * @param {string} path - the path on the host to connect to - only used if host is not a URI. Default: '/mqtt'.
   * @param {string} clientId - the Messaging client identifier, between 1 and 23 characters in length.
   * 
   * @property {string} host - <i>read only</i> the server's DNS hostname or dotted decimal IP address.
   * @property {number} port - <i>read only</i> the server's port.
   * @property {string} path - <i>read only</i> the server's path.
   * @property {string} clientId - <i>read only</i> used when connecting to the server.
   * @property {function} onConnectionLost - called when a connection has been lost. 
   *                            after a connect() method has succeeded.
   *                            Establish the call back used when a connection has been lost. The connection may be
   *                            lost because the client initiates a disconnect or because the server or network 
   *                            cause the client to be disconnected. The disconnect call back may be called without 
   *                            the connectionComplete call back being invoked if, for example the client fails to 
   *                            connect.
   *                            A single response object parameter is passed to the onConnectionLost callback containing the following fields:
   *                            <ol>   
   *                            <li>errorCode
   *                            <li>errorMessage       
   *                            </ol>
   * @property {function} onMessageDelivered called when a message has been delivered. 
   *                            All processing that this Client will ever do has been completed. So, for example,
   *                            in the case of a Qos=2 message sent by this client, the PubComp flow has been received from the server
   *                            and the message has been removed from persistent storage before this callback is invoked. 
   *                            Parameters passed to the onMessageDelivered callback are:
   *                            <ol>   
   *                            <li>{@link Paho.MQTT.Message} that was delivered.
   *                            </ol>    
   * @property {function} onMessageArrived called when a message has arrived in this Paho.MQTT.client. 
   *                            Parameters passed to the onMessageArrived callback are:
   *                            <ol>   
   *                            <li>{@link Paho.MQTT.Message} that has arrived.
   *                            </ol>    
   */


  var Client = function Client(host, port, path, clientId) {
    var uri;
    if (typeof host !== "string") throw new Error(format(ERROR.INVALID_TYPE, [typeof host, "host"]));

    if (arguments.length == 2) {
      // host: must be full ws:// uri
      // port: clientId
      clientId = port;
      uri = host;
      var match = uri.match(/^(wss?):\/\/((\[(.+)\])|([^\/]+?))(:(\d+))?(\/.*)$/);

      if (match) {
        host = match[4] || match[2];
        port = parseInt(match[7]);
        path = match[8];
      } else {
        throw new Error(format(ERROR.INVALID_ARGUMENT, [host, "host"]));
      }
    } else {
      if (arguments.length == 3) {
        clientId = path;
        path = "/mqtt";
      }

      if (typeof port !== "number" || port < 0) throw new Error(format(ERROR.INVALID_TYPE, [typeof port, "port"]));
      if (typeof path !== "string") throw new Error(format(ERROR.INVALID_TYPE, [typeof path, "path"]));
      var ipv6AddSBracket = host.indexOf(":") != -1 && host.slice(0, 1) != "[" && host.slice(-1) != "]";
      uri = "ws://" + (ipv6AddSBracket ? "[" + host + "]" : host) + ":" + port + path;
    }

    var clientIdLength = 0;

    for (var i = 0; i < clientId.length; i++) {
      var charCode = clientId.charCodeAt(i);

      if (0xD800 <= charCode && charCode <= 0xDBFF) {
        i++; // Surrogate pair.
      }

      clientIdLength++;
    }

    if (typeof clientId !== "string" || clientIdLength > 65535) throw new Error(format(ERROR.INVALID_ARGUMENT, [clientId, "clientId"]));
    var client = new ClientImpl(uri, host, port, path, clientId);

    this._getHost = function () {
      return host;
    };

    this._setHost = function () {
      throw new Error(format(ERROR.UNSUPPORTED_OPERATION));
    };

    this._getPort = function () {
      return port;
    };

    this._setPort = function () {
      throw new Error(format(ERROR.UNSUPPORTED_OPERATION));
    };

    this._getPath = function () {
      return path;
    };

    this._setPath = function () {
      throw new Error(format(ERROR.UNSUPPORTED_OPERATION));
    };

    this._getURI = function () {
      return uri;
    };

    this._setURI = function () {
      throw new Error(format(ERROR.UNSUPPORTED_OPERATION));
    };

    this._getClientId = function () {
      return client.clientId;
    };

    this._setClientId = function () {
      throw new Error(format(ERROR.UNSUPPORTED_OPERATION));
    };

    this._getOnConnectionLost = function () {
      return client.onConnectionLost;
    };

    this._setOnConnectionLost = function (newOnConnectionLost) {
      if (typeof newOnConnectionLost === "function") client.onConnectionLost = newOnConnectionLost;else throw new Error(format(ERROR.INVALID_TYPE, [typeof newOnConnectionLost, "onConnectionLost"]));
    };

    this._getOnMessageDelivered = function () {
      return client.onMessageDelivered;
    };

    this._setOnMessageDelivered = function (newOnMessageDelivered) {
      if (typeof newOnMessageDelivered === "function") client.onMessageDelivered = newOnMessageDelivered;else throw new Error(format(ERROR.INVALID_TYPE, [typeof newOnMessageDelivered, "onMessageDelivered"]));
    };

    this._getOnMessageArrived = function () {
      return client.onMessageArrived;
    };

    this._setOnMessageArrived = function (newOnMessageArrived) {
      if (typeof newOnMessageArrived === "function") client.onMessageArrived = newOnMessageArrived;else throw new Error(format(ERROR.INVALID_TYPE, [typeof newOnMessageArrived, "onMessageArrived"]));
    };

    this._getTrace = function () {
      return client.traceFunction;
    };

    this._setTrace = function (trace) {
      if (typeof trace === "function") {
        client.traceFunction = trace;
      } else {
        throw new Error(format(ERROR.INVALID_TYPE, [typeof trace, "onTrace"]));
      }
    };
    /** 
     * Connect this Messaging client to its server. 
     * 
     * @name Paho.MQTT.Client#connect
     * @function
     * @param {Object} connectOptions - attributes used with the connection. 
     * @param {number} connectOptions.timeout - If the connect has not succeeded within this 
     *                    number of seconds, it is deemed to have failed.
     *                    The default is 30 seconds.
     * @param {string} connectOptions.userName - Authentication username for this connection.
     * @param {string} connectOptions.password - Authentication password for this connection.
     * @param {Paho.MQTT.Message} connectOptions.willMessage - sent by the server when the client
     *                    disconnects abnormally.
     * @param {Number} connectOptions.keepAliveInterval - the server disconnects this client if
     *                    there is no activity for this number of seconds.
     *                    The default value of 60 seconds is assumed if not set.
     * @param {boolean} connectOptions.cleanSession - if true(default) the client and server 
     *                    persistent state is deleted on successful connect.
     * @param {boolean} connectOptions.useSSL - if present and true, use an SSL Websocket connection.
     * @param {object} connectOptions.invocationContext - passed to the onSuccess callback or onFailure callback.
     * @param {function} connectOptions.onSuccess - called when the connect acknowledgement 
     *                    has been received from the server.
     * A single response object parameter is passed to the onSuccess callback containing the following fields:
     * <ol>
     * <li>invocationContext as passed in to the onSuccess method in the connectOptions.       
     * </ol>
     * @config {function} [onFailure] called when the connect request has failed or timed out.
     * A single response object parameter is passed to the onFailure callback containing the following fields:
     * <ol>
     * <li>invocationContext as passed in to the onFailure method in the connectOptions.       
     * <li>errorCode a number indicating the nature of the error.
     * <li>errorMessage text describing the error.      
     * </ol>
     * @config {Array} [hosts] If present this contains either a set of hostnames or fully qualified
     * WebSocket URIs (ws://example.com:1883/mqtt), that are tried in order in place 
     * of the host and port paramater on the construtor. The hosts are tried one at at time in order until
     * one of then succeeds.
     * @config {Array} [ports] If present the set of ports matching the hosts. If hosts contains URIs, this property
     * is not used.
     * @throws {InvalidState} if the client is not in disconnected state. The client must have received connectionLost
     * or disconnected before calling connect for a second or subsequent time.
     */


    this.connect = function (connectOptions) {
      connectOptions = connectOptions || {};
      validate(connectOptions, {
        timeout: "number",
        userName: "string",
        password: "string",
        willMessage: "object",
        keepAliveInterval: "number",
        cleanSession: "boolean",
        useSSL: "boolean",
        invocationContext: "object",
        onSuccess: "function",
        onFailure: "function",
        hosts: "object",
        ports: "object",
        mqttVersion: "number"
      }); // If no keep alive interval is set, assume 60 seconds.

      if (connectOptions.keepAliveInterval === undefined) connectOptions.keepAliveInterval = 60;

      if (connectOptions.mqttVersion > 4 || connectOptions.mqttVersion < 3) {
        throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.mqttVersion, "connectOptions.mqttVersion"]));
      }

      if (connectOptions.mqttVersion === undefined) {
        connectOptions.mqttVersionExplicit = false;
        connectOptions.mqttVersion = 4;
      } else {
        connectOptions.mqttVersionExplicit = true;
      } //Check that if password is set, so is username


      if (connectOptions.password === undefined && connectOptions.userName !== undefined) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.password, "connectOptions.password"]));

      if (connectOptions.willMessage) {
        if (!(connectOptions.willMessage instanceof Message)) throw new Error(format(ERROR.INVALID_TYPE, [connectOptions.willMessage, "connectOptions.willMessage"])); // The will message must have a payload that can be represented as a string.
        // Cause the willMessage to throw an exception if this is not the case.

        connectOptions.willMessage.stringPayload;
        if (typeof connectOptions.willMessage.destinationName === "undefined") throw new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.willMessage.destinationName, "connectOptions.willMessage.destinationName"]));
      }

      if (typeof connectOptions.cleanSession === "undefined") connectOptions.cleanSession = true;

      if (connectOptions.hosts) {
        if (!(connectOptions.hosts instanceof Array)) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts, "connectOptions.hosts"]));
        if (connectOptions.hosts.length < 1) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts, "connectOptions.hosts"]));
        var usingURIs = false;

        for (var i = 0; i < connectOptions.hosts.length; i++) {
          if (typeof connectOptions.hosts[i] !== "string") throw new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.hosts[i], "connectOptions.hosts[" + i + "]"]));

          if (/^(wss?):\/\/((\[(.+)\])|([^\/]+?))(:(\d+))?(\/.*)$/.test(connectOptions.hosts[i])) {
            if (i == 0) {
              usingURIs = true;
            } else if (!usingURIs) {
              throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts[i], "connectOptions.hosts[" + i + "]"]));
            }
          } else if (usingURIs) {
            throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.hosts[i], "connectOptions.hosts[" + i + "]"]));
          }
        }

        if (!usingURIs) {
          if (!connectOptions.ports) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, "connectOptions.ports"]));
          if (!(connectOptions.ports instanceof Array)) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, "connectOptions.ports"]));
          if (connectOptions.hosts.length != connectOptions.ports.length) throw new Error(format(ERROR.INVALID_ARGUMENT, [connectOptions.ports, "connectOptions.ports"]));
          connectOptions.uris = [];

          for (var i = 0; i < connectOptions.hosts.length; i++) {
            if (typeof connectOptions.ports[i] !== "number" || connectOptions.ports[i] < 0) throw new Error(format(ERROR.INVALID_TYPE, [typeof connectOptions.ports[i], "connectOptions.ports[" + i + "]"]));
            var host = connectOptions.hosts[i];
            var port = connectOptions.ports[i];
            var ipv6 = host.indexOf(":") != -1;
            uri = "ws://" + (ipv6 ? "[" + host + "]" : host) + ":" + port + path;
            connectOptions.uris.push(uri);
          }
        } else {
          connectOptions.uris = connectOptions.hosts;
        }
      }

      client.connect(connectOptions);
    };
    /** 
     * Subscribe for messages, request receipt of a copy of messages sent to the destinations described by the filter.
     * 
     * @name Paho.MQTT.Client#subscribe
     * @function
     * @param {string} filter describing the destinations to receive messages from.
     * <br>
     * @param {object} subscribeOptions - used to control the subscription
     *
     * @param {number} subscribeOptions.qos - the maiximum qos of any publications sent 
     *                                  as a result of making this subscription.
     * @param {object} subscribeOptions.invocationContext - passed to the onSuccess callback 
     *                                  or onFailure callback.
     * @param {function} subscribeOptions.onSuccess - called when the subscribe acknowledgement
     *                                  has been received from the server.
     *                                  A single response object parameter is passed to the onSuccess callback containing the following fields:
     *                                  <ol>
     *                                  <li>invocationContext if set in the subscribeOptions.       
     *                                  </ol>
     * @param {function} subscribeOptions.onFailure - called when the subscribe request has failed or timed out.
     *                                  A single response object parameter is passed to the onFailure callback containing the following fields:
     *                                  <ol>
     *                                  <li>invocationContext - if set in the subscribeOptions.       
     *                                  <li>errorCode - a number indicating the nature of the error.
     *                                  <li>errorMessage - text describing the error.      
     *                                  </ol>
     * @param {number} subscribeOptions.timeout - which, if present, determines the number of
     *                                  seconds after which the onFailure calback is called.
     *                                  The presence of a timeout does not prevent the onSuccess
     *                                  callback from being called when the subscribe completes.         
     * @throws {InvalidState} if the client is not in connected state.
     */


    this.subscribe = function (filter, subscribeOptions) {
      if (typeof filter !== "string") throw new Error("Invalid argument:" + filter);
      subscribeOptions = subscribeOptions || {};
      validate(subscribeOptions, {
        qos: "number",
        invocationContext: "object",
        onSuccess: "function",
        onFailure: "function",
        timeout: "number"
      });
      if (subscribeOptions.timeout && !subscribeOptions.onFailure) throw new Error("subscribeOptions.timeout specified with no onFailure callback.");
      if (typeof subscribeOptions.qos !== "undefined" && !(subscribeOptions.qos === 0 || subscribeOptions.qos === 1 || subscribeOptions.qos === 2)) throw new Error(format(ERROR.INVALID_ARGUMENT, [subscribeOptions.qos, "subscribeOptions.qos"]));
      client.subscribe(filter, subscribeOptions);
    };
    /**
     * Unsubscribe for messages, stop receiving messages sent to destinations described by the filter.
     * 
     * @name Paho.MQTT.Client#unsubscribe
     * @function
     * @param {string} filter - describing the destinations to receive messages from.
     * @param {object} unsubscribeOptions - used to control the subscription
     * @param {object} unsubscribeOptions.invocationContext - passed to the onSuccess callback 
                                          or onFailure callback.
     * @param {function} unsubscribeOptions.onSuccess - called when the unsubscribe acknowledgement has been received from the server.
     *                                    A single response object parameter is passed to the 
     *                                    onSuccess callback containing the following fields:
     *                                    <ol>
     *                                    <li>invocationContext - if set in the unsubscribeOptions.     
     *                                    </ol>
     * @param {function} unsubscribeOptions.onFailure called when the unsubscribe request has failed or timed out.
     *                                    A single response object parameter is passed to the onFailure callback containing the following fields:
     *                                    <ol>
     *                                    <li>invocationContext - if set in the unsubscribeOptions.       
     *                                    <li>errorCode - a number indicating the nature of the error.
     *                                    <li>errorMessage - text describing the error.      
     *                                    </ol>
     * @param {number} unsubscribeOptions.timeout - which, if present, determines the number of seconds
     *                                    after which the onFailure callback is called. The presence of
     *                                    a timeout does not prevent the onSuccess callback from being
     *                                    called when the unsubscribe completes
     * @throws {InvalidState} if the client is not in connected state.
     */


    this.unsubscribe = function (filter, unsubscribeOptions) {
      if (typeof filter !== "string") throw new Error("Invalid argument:" + filter);
      unsubscribeOptions = unsubscribeOptions || {};
      validate(unsubscribeOptions, {
        invocationContext: "object",
        onSuccess: "function",
        onFailure: "function",
        timeout: "number"
      });
      if (unsubscribeOptions.timeout && !unsubscribeOptions.onFailure) throw new Error("unsubscribeOptions.timeout specified with no onFailure callback.");
      client.unsubscribe(filter, unsubscribeOptions);
    };
    /**
     * Send a message to the consumers of the destination in the Message.
     * 
     * @name Paho.MQTT.Client#send
     * @function 
     * @param {string|Paho.MQTT.Message} topic - <b>mandatory</b> The name of the destination to which the message is to be sent. 
     * 					   - If it is the only parameter, used as Paho.MQTT.Message object.
     * @param {String|ArrayBuffer} payload - The message data to be sent. 
     * @param {number} qos The Quality of Service used to deliver the message.
     * 		<dl>
     * 			<dt>0 Best effort (default).
     *     			<dt>1 At least once.
     *     			<dt>2 Exactly once.     
     * 		</dl>
     * @param {Boolean} retained If true, the message is to be retained by the server and delivered 
     *                     to both current and future subscriptions.
     *                     If false the server only delivers the message to current subscribers, this is the default for new Messages. 
     *                     A received message has the retained boolean set to true if the message was published 
     *                     with the retained boolean set to true
     *                     and the subscrption was made after the message has been published. 
     * @throws {InvalidState} if the client is not connected.
     */


    this.send = function (topic, payload, qos, retained) {
      var message;

      if (arguments.length == 0) {
        throw new Error("Invalid argument." + "length");
      } else if (arguments.length == 1) {
        if (!(topic instanceof Message) && typeof topic !== "string") throw new Error("Invalid argument:" + typeof topic);
        message = topic;
        if (typeof message.destinationName === "undefined") throw new Error(format(ERROR.INVALID_ARGUMENT, [message.destinationName, "Message.destinationName"]));
        client.send(message);
      } else {
        //parameter checking in Message object 
        message = new Message(payload);
        message.destinationName = topic;
        if (arguments.length >= 3) message.qos = qos;
        if (arguments.length >= 4) message.retained = retained;
        client.send(message);
      }
    };
    /** 
     * Normal disconnect of this Messaging client from its server.
     * 
     * @name Paho.MQTT.Client#disconnect
     * @function
     * @throws {InvalidState} if the client is already disconnected.     
     */


    this.disconnect = function () {
      client.disconnect();
    };
    /** 
     * Get the contents of the trace log.
     * 
     * @name Paho.MQTT.Client#getTraceLog
     * @function
     * @return {Object[]} tracebuffer containing the time ordered trace records.
     */


    this.getTraceLog = function () {
      return client.getTraceLog();
    };
    /** 
     * Start tracing.
     * 
     * @name Paho.MQTT.Client#startTrace
     * @function
     */


    this.startTrace = function () {
      client.startTrace();
    };
    /** 
     * Stop tracing.
     * 
     * @name Paho.MQTT.Client#stopTrace
     * @function
     */


    this.stopTrace = function () {
      client.stopTrace();
    };

    this.isConnected = function () {
      return client.connected;
    };

    this.ping = function () {
      var _client$sendPinger;

      client == null ? void 0 : (_client$sendPinger = client.sendPinger) == null ? void 0 : _client$sendPinger.ping(); // client.receivePinger.reset()
    };

    this.setKeepAliveInterval = function (keepAliveInterval) {
      if (client) {
        var _client$sendPinger2, _client$receivePinger;

        (_client$sendPinger2 = client.sendPinger) == null ? void 0 : _client$sendPinger2.ping(keepAliveInterval);
        (_client$receivePinger = client.receivePinger) == null ? void 0 : _client$receivePinger.ping(keepAliveInterval);
      }
    };
  };

  Client.prototype = {
    get host() {
      return this._getHost();
    },

    set host(newHost) {
      this._setHost(newHost);
    },

    get port() {
      return this._getPort();
    },

    set port(newPort) {
      this._setPort(newPort);
    },

    get path() {
      return this._getPath();
    },

    set path(newPath) {
      this._setPath(newPath);
    },

    get clientId() {
      return this._getClientId();
    },

    set clientId(newClientId) {
      this._setClientId(newClientId);
    },

    get onConnectionLost() {
      return this._getOnConnectionLost();
    },

    set onConnectionLost(newOnConnectionLost) {
      this._setOnConnectionLost(newOnConnectionLost);
    },

    get onMessageDelivered() {
      return this._getOnMessageDelivered();
    },

    set onMessageDelivered(newOnMessageDelivered) {
      this._setOnMessageDelivered(newOnMessageDelivered);
    },

    get onMessageArrived() {
      return this._getOnMessageArrived();
    },

    set onMessageArrived(newOnMessageArrived) {
      this._setOnMessageArrived(newOnMessageArrived);
    },

    get trace() {
      return this._getTrace();
    },

    set trace(newTraceFunction) {
      this._setTrace(newTraceFunction);
    }

  };
  /** 
   * An application message, sent or received.
   * <p>
   * All attributes may be null, which implies the default values.
   * 
   * @name Paho.MQTT.Message
   * @constructor
   * @param {String|ArrayBuffer} payload The message data to be sent.
   * <p>
   * @property {string} payloadString <i>read only</i> The payload as a string if the payload consists of valid UTF-8 characters.
   * @property {ArrayBuffer} payloadBytes <i>read only</i> The payload as an ArrayBuffer.
   * <p>
   * @property {string} destinationName <b>mandatory</b> The name of the destination to which the message is to be sent
   *                    (for messages about to be sent) or the name of the destination from which the message has been received.
   *                    (for messages received by the onMessage function).
   * <p>
   * @property {number} qos The Quality of Service used to deliver the message.
   * <dl>
   *     <dt>0 Best effort (default).
   *     <dt>1 At least once.
   *     <dt>2 Exactly once.     
   * </dl>
   * <p>
   * @property {Boolean} retained If true, the message is to be retained by the server and delivered 
   *                     to both current and future subscriptions.
   *                     If false the server only delivers the message to current subscribers, this is the default for new Messages. 
   *                     A received message has the retained boolean set to true if the message was published 
   *                     with the retained boolean set to true
   *                     and the subscrption was made after the message has been published. 
   * <p>
   * @property {Boolean} duplicate <i>read only</i> If true, this message might be a duplicate of one which has already been received. 
   *                     This is only set on messages received from the server.
   *                     
   */

  var Message = function Message(newPayload) {
    var payload;

    if (typeof newPayload === "string" || newPayload instanceof ArrayBuffer || newPayload instanceof Int8Array || newPayload instanceof Uint8Array || newPayload instanceof Int16Array || newPayload instanceof Uint16Array || newPayload instanceof Int32Array || newPayload instanceof Uint32Array || newPayload instanceof Float32Array || newPayload instanceof Float64Array) {
      payload = newPayload;
    } else {
      throw format(ERROR.INVALID_ARGUMENT, [newPayload, "newPayload"]);
    }

    this._getPayloadString = function () {
      if (typeof payload === "string") return payload;else return parseUTF8(payload, 0, payload.length);
    };

    this._getPayloadBytes = function () {
      if (typeof payload === "string") {
        var buffer = new ArrayBuffer(UTF8Length(payload));
        var byteStream = new Uint8Array(buffer);
        stringToUTF8(payload, byteStream, 0);
        return byteStream;
      } else {
        return payload;
      }

      ;
    };

    var destinationName = undefined;

    this._getDestinationName = function () {
      return destinationName;
    };

    this._setDestinationName = function (newDestinationName) {
      if (typeof newDestinationName === "string") destinationName = newDestinationName;else throw new Error(format(ERROR.INVALID_ARGUMENT, [newDestinationName, "newDestinationName"]));
    };

    var qos = 0;

    this._getQos = function () {
      return qos;
    };

    this._setQos = function (newQos) {
      if (newQos === 0 || newQos === 1 || newQos === 2) qos = newQos;else throw new Error("Invalid argument:" + newQos);
    };

    var retained = false;

    this._getRetained = function () {
      return retained;
    };

    this._setRetained = function (newRetained) {
      if (typeof newRetained === "boolean") retained = newRetained;else throw new Error(format(ERROR.INVALID_ARGUMENT, [newRetained, "newRetained"]));
    };

    var duplicate = false;

    this._getDuplicate = function () {
      return duplicate;
    };

    this._setDuplicate = function (newDuplicate) {
      duplicate = newDuplicate;
    };
  };

  Message.prototype = {
    get payloadString() {
      return this._getPayloadString();
    },

    get payloadBytes() {
      return this._getPayloadBytes();
    },

    get destinationName() {
      return this._getDestinationName();
    },

    set destinationName(newDestinationName) {
      this._setDestinationName(newDestinationName);
    },

    get qos() {
      return this._getQos();
    },

    set qos(newQos) {
      this._setQos(newQos);
    },

    get retained() {
      return this._getRetained();
    },

    set retained(newRetained) {
      this._setRetained(newRetained);
    },

    get duplicate() {
      return this._getDuplicate();
    },

    set duplicate(newDuplicate) {
      this._setDuplicate(newDuplicate);
    }

  }; // Module contents.

  return {
    Client: Client,
    Message: Message
  };
}(window);

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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