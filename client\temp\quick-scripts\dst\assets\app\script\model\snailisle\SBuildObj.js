
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/SBuildObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a7e90OuShpBJpYbF6OlkmN7', 'SBuildObj');
// app/script/model/snailisle/SBuildObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var SceneBuildObj_1 = require("./SceneBuildObj");
// 大通铺 建筑设施
var SBuildObj = /** @class */ (function (_super) {
    __extends(SBuildObj, _super);
    function SBuildObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.anim = ''; //动画
        return _this;
    }
    SBuildObj.prototype.getSceneType = function () { return 'snailisle'; };
    // 获取设施加载路径
    SBuildObj.prototype.getUrl = function () {
        return this.getSceneType() + '/SNAILISLE_' + this.id;
    };
    return SBuildObj;
}(SceneBuildObj_1.default));
exports.default = SBuildObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxzbmFpbGlzbGVcXFNCdWlsZE9iai50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpREFBMkM7QUFFM0MsV0FBVztBQUNYO0lBQXVDLDZCQUFhO0lBQXBEO1FBQUEscUVBVUM7UUFSVSxVQUFJLEdBQVcsRUFBRSxDQUFBLENBQUMsSUFBSTs7SUFRakMsQ0FBQztJQU5VLGdDQUFZLEdBQW5CLGNBQXdCLE9BQU8sV0FBVyxDQUFBLENBQUMsQ0FBQztJQUU1QyxXQUFXO0lBQ0osMEJBQU0sR0FBYjtRQUNJLE9BQU8sSUFBSSxDQUFDLFlBQVksRUFBRSxHQUFHLGFBQWEsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBO0lBQ3hELENBQUM7SUFDTCxnQkFBQztBQUFELENBVkEsQUFVQyxDQVZzQyx1QkFBYSxHQVVuRCIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTY2VuZUJ1aWxkT2JqIGZyb20gXCIuL1NjZW5lQnVpbGRPYmpcIlxyXG5cclxuLy8g5aSn6YCa6ZO6IOW7uuetkeiuvuaWvVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTQnVpbGRPYmogZXh0ZW5kcyBTY2VuZUJ1aWxkT2JqIHtcclxuXHJcbiAgICBwdWJsaWMgYW5pbTogc3RyaW5nID0gJycgLy/liqjnlLtcclxuXHJcbiAgICBwdWJsaWMgZ2V0U2NlbmVUeXBlKCkgeyByZXR1cm4gJ3NuYWlsaXNsZScgfVxyXG5cclxuICAgIC8vIOiOt+WPluiuvuaWveWKoOi9vei3r+W+hFxyXG4gICAgcHVibGljIGdldFVybCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5nZXRTY2VuZVR5cGUoKSArICcvU05BSUxJU0xFXycgKyB0aGlzLmlkXHJcbiAgICB9XHJcbn0iXX0=