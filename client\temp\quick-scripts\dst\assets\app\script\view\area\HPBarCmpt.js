
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/HPBarCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8f8d3vA+lxLEIeFMw5qbjc8', 'HPBarCmpt');
// app/script/view/area/HPBarCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 血条
var HPBarCmpt = /** @class */ (function (_super) {
    __extends(HPBarCmpt, _super);
    function HPBarCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.root = null;
        _this.diSpr = null;
        _this.hpSpr = null;
        _this.shieldSpr = null;
        _this.angerSpr = null;
        _this.data = null;
        _this.barTween = null;
        _this.valTween = null;
        return _this;
    }
    HPBarCmpt.prototype.init = function (data) {
        var _a;
        this.data = data;
        this.root = this.Child('root');
        this.diSpr = this.root.Child('val', cc.Sprite);
        this.hpSpr = this.root.Child('bar', cc.Sprite);
        this.shieldSpr = this.root.Child('bar/shield', cc.Sprite);
        this.angerSpr = this.root.Child('anger', cc.Sprite);
        (_a = this.root.Child('anger_bg')) === null || _a === void 0 ? void 0 : _a.Color(data.isHasAnger() ? '#453018' : '#7F7F7F');
        this.initInfo();
        return this;
    };
    HPBarCmpt.prototype.clean = function () {
        this.node.destroy();
        this.data = null;
        this.barTween = null;
        this.valTween = null;
    };
    // 初始化血条信息
    HPBarCmpt.prototype.initInfo = function () {
        var _a, _b;
        if (!this.data) {
            return;
        }
        this.node.active = !this.data.isDie();
        (_a = this.barTween) === null || _a === void 0 ? void 0 : _a.stop();
        (_b = this.valTween) === null || _b === void 0 ? void 0 : _b.stop();
        // cc.log(this.data.getUid(), 'initInfo', this.data.isDie())
        var ratio = this.data.getHpRatio();
        var bar = this.hpSpr, bg = this.root.Child('hp_bg');
        var color = GameHelper_1.gameHpr.getBattleHpBarColor(this.data);
        bar.Color(color.bar);
        bg === null || bg === void 0 ? void 0 : bg.Color(color.bg);
        this.diSpr.Color('#FFFFFF');
        bar.fillRange = this.diSpr.fillRange = ratio;
    };
    // 播放血条
    HPBarCmpt.prototype.play = function () {
        var _this = this;
        var _a, _b;
        if (!this.data) {
            return;
        }
        var ratio = this.data.getHpRatio();
        (_a = this.barTween) === null || _a === void 0 ? void 0 : _a.stop();
        this.barTween = cc.tween(this.hpSpr)
            .to(0.1, { fillRange: ratio }).start();
        (_b = this.valTween) === null || _b === void 0 ? void 0 : _b.stop();
        this.valTween = cc.tween(this.diSpr)
            .delay(0.1)
            .to(0.2, { fillRange: ratio })
            .call(function () {
            if (_this.isValid) {
                _this.initInfo();
            }
        }).start();
    };
    // 刷新怒气
    HPBarCmpt.prototype.updateAnger = function (ratio) {
        if (this.angerSpr) {
            this.angerSpr.fillRange = ratio;
        }
    };
    // 刷新白盾
    HPBarCmpt.prototype.updateShieldValue = function (val, curHp, maxHp) {
        var _a;
        if (!this.shieldSpr) {
        }
        else if (this.shieldSpr.setActive(val > 0)) {
            var width = this.hpSpr.node.width;
            // 当前生命值+护盾 / Max(当前生命值+护盾, 最大生命值)
            var tempHp = curHp + val;
            var ratio = tempHp / Math.max(tempHp, maxHp);
            // 计算当前生命的真正占比
            var hs = this.diSpr.fillRange = this.hpSpr.fillRange = curHp / tempHp * ratio;
            // 当前生命值+护盾 的宽度
            var tempWidth = ratio * width;
            this.shieldSpr.node.x = hs * width;
            this.shieldSpr.node.width = val / tempHp * tempWidth;
        }
        else {
            this.diSpr.fillRange = this.hpSpr.fillRange = ((_a = this.data) === null || _a === void 0 ? void 0 : _a.getHpRatio()) || 0;
        }
    };
    HPBarCmpt = __decorate([
        ccclass
    ], HPBarCmpt);
    return HPBarCmpt;
}(cc.Component));
exports.default = HPBarCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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