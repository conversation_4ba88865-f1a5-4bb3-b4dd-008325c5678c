
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/FSPModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '37b9etLOyFHVa01DkAQhfWO', 'FSPModel');
// app/script/model/fsp/FSPModel.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var LocalConfig_1 = require("../../common/LocalConfig");
var FSPBattleController_1 = require("./FSPBattleController");
// 帧同步 一场战斗对应一个
var FSPModel = /** @class */ (function () {
    function FSPModel() {
        this.net = null;
        this.area = null;
        this.battleController = null;
        this.LOCKED_MAX_FRAME = 20; //客户端可以多余服务器还没下发的时候 多少帧
        this.isRunning = false;
        this.isLocal = false; //是否本地战斗
        this.fps = 0; //帧率
        this.mul = 0; //加速倍数
        this.dt = 0;
        this.onCheckHasFrameData = null;
        this.upSpeedFrame = 0; //多少帧开始加速
        this.checkFrameCount = 0; //服务器多少帧向客户端同步一次
        this.currentFrameIndex = 0; //当前帧
        this.serverFrameIndex = 0; //服务器当前的帧数
        this.lockedFrameIndex = 0; //锁定帧 客户端相较于服务器需要慢多少帧
        this.serverSnapshootMD5Map = {}; //快照md5 用于和客户端比较是否同步
        this.otherFrameDataMap = {}; //其他帧同步数据
        this.attackerArmyAcc = 0; //攻击方补兵次数
        this.defenderArmyAcc = 0; //防守方补兵次数
        this.initInterval = 0;
        this.fpsInterval = 0;
        this.fpsElapsed = 0;
        this.currUpSpeedFrame = 0;
        this.speedMulIndex = 2; //当前加速倍数下标
        this.pause = false; //是否暂停
        this.startTime = 0;
        this.net = mc.getModel('net');
    }
    FSPModel.prototype.init = function (area, data, frameDataMap, mulIndex, serverPlayBack) {
        var _a, _b;
        cc.log('fsp init', data);
        this.area = area;
        this.isLocal = !!frameDataMap && !serverPlayBack;
        this.fps = (_a = data.fps) !== null && _a !== void 0 ? _a : 20;
        this.mul = data.mul || 1;
        this.upSpeedFrame = data.upSpeedFrame || (ut.Time.Minute * 30 / (1000 / this.fps));
        this.currUpSpeedFrame = this.upSpeedFrame * Math.floor((this.mul + 1) * this.mul / 2);
        this.checkFrameCount = data.checkFrameCount || ut.MAX_VALUE;
        this.currentFrameIndex = this.serverFrameIndex = (_b = data.currentFrameIndex) !== null && _b !== void 0 ? _b : 0;
        this.lockedFrameIndex = this.serverFrameIndex + this.checkFrameCount + this.LOCKED_MAX_FRAME;
        this.fpsInterval = this.initInterval = this.fpsElapsed = 1 / this.fps;
        this.dt = Math.floor(1000 / this.fps);
        this.onCheckHasFrameData = this.checkHasFrameData.bind(this);
        this.serverSnapshootMD5Map = {};
        this.setSpeedMulIndex(mulIndex !== null && mulIndex !== void 0 ? mulIndex : Constant_1.PLAYBACK_MULS.findIndex(function (m) { return m.val === 1; }));
        this.defenderArmyAcc = data.defenderArmyAcc || 0;
        this.attackerArmyAcc = data.attackerArmyAcc || 1;
        // 初始化战斗逻辑控制
        this.battleController = new FSPBattleController_1.default().init(area, data);
        // 监听服务器定时同步帧信息
        if (this.isLocal || serverPlayBack) {
            this.otherFrameDataMap = frameDataMap;
        }
        else {
            this.otherFrameDataMap = {};
        }
        this.isRunning = true;
        this.pause = false;
        this.startTime = Date.now();
        return this;
    };
    FSPModel.prototype.strip = function () {
        var _a;
        var ctrlStip = (_a = this.battleController) === null || _a === void 0 ? void 0 : _a.strip();
        return Object.assign({
            mul: this.mul,
            currentFrameIndex: this.currentFrameIndex,
            battleTime: this.getBattleTime(),
            defenderArmyAcc: this.defenderArmyAcc,
            attackerArmyAcc: this.attackerArmyAcc
        }, ctrlStip);
    };
    FSPModel.prototype.stop = function () {
        var _a;
        this.isRunning = false;
        this.pause = false;
        (_a = this.battleController) === null || _a === void 0 ? void 0 : _a.stop();
        this.battleController = null;
        if (this.isLocal) {
            eventCenter.emit(EventType_1.default.PLAYBACK_END);
        }
        cc.log('fsp stop ' + ((Date.now() - this.startTime) * 0.001) + 's, frameCount: ' + this.currentFrameIndex);
    };
    FSPModel.prototype.isWin = function () {
        var _a;
        return !!((_a = this.battleController) === null || _a === void 0 ? void 0 : _a.isWin());
    };
    FSPModel.prototype.resyncLocal = function (areaInfo) {
        var index = this.area.index;
        this.area.initBattleData(areaInfo);
        // this.area.battleReady()
        this.init(this.area, areaInfo.battle, this.otherFrameDataMap, this.speedMulIndex);
        eventCenter.emit(EventType_1.default.AREA_BATTLE_BEGIN, index);
    };
    // 重新同步信息
    FSPModel.prototype.resync = function () {
        return __awaiter(this, void 0, void 0, function () {
            var index, _a, err, data, areaInfo;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isLocal || GameHelper_1.gameHpr.isNoviceMode) {
                            return [2 /*return*/];
                        }
                        this.stop();
                        index = this.area.index;
                        return [4 /*yield*/, this.net.request('game/HD_GetAreaInfo', { index: index, noRecord: true })
                            // cc.log(data)
                        ];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        areaInfo = data === null || data === void 0 ? void 0 : data.data;
                        if (!areaInfo) {
                            return [2 /*return*/, GameHelper_1.gameHpr.areaCenter.removeArea(index)];
                        }
                        else if (areaInfo.battle) {
                            this.area.initBattleData(areaInfo);
                            this.area.battleReady();
                            this.init(this.area, areaInfo.battle);
                            eventCenter.emit(EventType_1.default.AREA_BATTLE_BEGIN, index);
                        }
                        else {
                            this.area.battleEndData = areaInfo;
                            this.area.battleEndByLocal();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    FSPModel.prototype.getCurrentFrameIndex = function () {
        return this.currentFrameIndex;
    };
    // 获取战斗时间
    FSPModel.prototype.getBattleTime = function () {
        return this.currentFrameIndex * this.dt;
    };
    FSPModel.prototype.getBattleController = function () {
        return this.battleController;
    };
    // 是否当前士兵回合结束
    FSPModel.prototype.recordEnable = function () {
        var _a;
        return (_a = this.battleController) === null || _a === void 0 ? void 0 : _a.isCurrentRoundEnd();
    };
    FSPModel.prototype.update = function (dt) {
        if (!this.isRunning || this.pause) {
            return;
        }
        this.fpsElapsed += dt;
        if (this.fpsElapsed >= this.fpsInterval) {
            if (dt >= this.fpsInterval) {
                this.fpsElapsed = 0;
            }
            else {
                this.fpsElapsed -= this.fpsInterval;
            }
            for (var i = 0; i < this.mul; i++) {
                this.tick();
                this.serverFrameIndex += 1; //模拟服务器的帧
            }
            // 加速 回放不加速
            if (!this.isLocal && this.currentFrameIndex >= this.currUpSpeedFrame && this.mul < 3) {
                this.mul += 1;
                this.currUpSpeedFrame += this.upSpeedFrame * this.mul;
                eventCenter.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.area.index);
            }
        }
    };
    FSPModel.prototype.setSpeedMulIndex = function (index) {
        var _a;
        this.speedMulIndex = index;
        this.fpsInterval = this.initInterval * (((_a = Constant_1.PLAYBACK_MULS[index]) === null || _a === void 0 ? void 0 : _a.val) || 1);
        this.fpsElapsed = 0;
    };
    FSPModel.prototype.getSpeedMulIndex = function () {
        return this.speedMulIndex;
    };
    FSPModel.prototype.getMul = function () {
        return this.mul;
    };
    FSPModel.prototype.getAttackerArmyAcc = function () {
        return this.attackerArmyAcc;
    };
    FSPModel.prototype.getDefenderArmyAcc = function () {
        return this.defenderArmyAcc;
    };
    // 暂停
    FSPModel.prototype.setPause = function (val) {
        this.pause = val;
    };
    FSPModel.prototype.isPause = function () {
        return this.pause;
    };
    // 服务器下发的当前帧信息
    FSPModel.prototype.onFSPCheckFrame = function (data) {
        if (this.isLocal) {
            return;
        }
        // cc.log('OnFSPCheckFrame', this.currentFrameIndex, data)
        this.serverFrameIndex = data.currentFrameIndex;
        this.lockedFrameIndex = this.serverFrameIndex + this.checkFrameCount + this.LOCKED_MAX_FRAME; //加上可以多跑出服务器的帧
        // 有其他同步信息
        if (!data.type) {
        }
        else if (this.currentFrameIndex > this.serverFrameIndex) { //如果跑到前面去了 就直接重新同步
            cc.log('不同步了 OnFSPCheckFrame currentFrameIndex > serverFrameIndex, currentFrameIndex=' + this.currentFrameIndex + ', serverFrameIndex=' + this.serverFrameIndex);
            // errorReportHelper.reportError('fsp no sync', { index: this.area.index })
            this.resync();
            return;
        }
        else {
            var arr = this.otherFrameDataMap[this.serverFrameIndex];
            if (!arr) {
                arr = this.otherFrameDataMap[this.serverFrameIndex] = [];
            }
            arr.push(data);
            this.checkHasFrameData(this.currentFrameIndex);
        }
        // 比较快照
        if (data.snapshootMD5) {
            var md5 = this.serverSnapshootMD5Map[this.serverFrameIndex];
            if (!md5) {
                this.serverSnapshootMD5Map[this.serverFrameIndex] = data.snapshootMD5;
            }
            else {
                delete this.serverSnapshootMD5Map[this.serverFrameIndex];
                if (md5 !== data.snapshootMD5) {
                    cc.log('不同步了 OnFSPCheckFrame dm5');
                    cc.log('local', md5);
                    cc.log('server', data.snapshootMD5);
                    this.resync();
                    this.noSyncReport(md5, data.snapshootMD5, this.serverFrameIndex);
                }
            }
        }
    };
    // 每帧刷新
    FSPModel.prototype.tick = function () {
        // 当前和服务器帧的差距
        var frameDiff = this.checkFrameDiff();
        if (frameDiff < 0) {
            return;
        }
        // 获取速度
        var speed = this.getFrameSpeed(frameDiff);
        // 执行
        while (speed > 0) {
            speed -= 1;
            this.executeOneFrame();
        }
    };
    FSPModel.prototype.checkFrameDiff = function () {
        if (this.isLocal) {
            return 0;
        }
        else if (this.currentFrameIndex > this.lockedFrameIndex) {
            return -1; //如果跑前面去了 就停止等待
        }
        // 当前和服务器帧的差距
        return this.serverFrameIndex - this.currentFrameIndex;
    };
    // 后台计算一帧
    FSPModel.prototype.executeOneFrameForSimulate = function () {
        var _a;
        this.currentFrameIndex += 1;
        if (this.onCheckHasFrameData) {
            this.onCheckHasFrameData(this.currentFrameIndex);
        }
        (_a = this.battleController) === null || _a === void 0 ? void 0 : _a.updateFrame(this.dt);
    };
    // 执行一帧
    FSPModel.prototype.executeOneFrame = function () {
        var _a;
        this.currentFrameIndex += 1;
        if (this.onCheckHasFrameData) {
            this.onCheckHasFrameData(this.currentFrameIndex);
        }
        (_a = this.battleController) === null || _a === void 0 ? void 0 : _a.updateFrame(this.dt);
        this.area.updatePawnAnimationFrame(this.dt);
        this.checkStateSync();
    };
    FSPModel.prototype.setCheckHasFrameData = function (cb) {
        this.onCheckHasFrameData = cb;
    };
    // 检测是否有帧数据
    FSPModel.prototype.checkHasFrameData = function (currentFrameIndex) {
        var _this = this;
        var arr = this.otherFrameDataMap[currentFrameIndex];
        if (!arr || !this.battleController) {
            return;
        }
        else if (!this.isLocal) { //本地数据的话 就不要删除因为下次还要用
            delete this.otherFrameDataMap[currentFrameIndex];
        }
        arr.forEach(function (data) { return _this.checkHasFrameDataItem(data); });
    };
    FSPModel.prototype.checkHasFrameDataItem = function (data) {
        if (!this.battleController) {
            return;
        }
        else if (data.type === 1) { //添加军队
            this.area.addArmy(data.army);
            this.battleController.addFighters(data.fighters);
            eventCenter.emit(EventType_1.default.UPDATE_BATTLE_ARMY_COUNT_SHOW);
            cc.log(this.currentFrameIndex + ' AddArmy ' + data.army.name);
        }
        else if (data.type === 2) { //刷新区域信息
            this.area.curHp = data.hp[0];
            this.area.maxHp = data.hp[1];
            this.battleController.UpdateBuildInfo(data.buildInfo[0], data.buildInfo[1]);
            if (this.area.index < 0) { //回放的话 要刷新建筑
                this.area.updateCityByPawnInfo(data.buildInfo[0], data.buildInfo[1]);
            }
        }
        else if (data.type === 3) { //删除军队
            this.area.removeArmy(data.uid);
            this.battleController.removeFightersByArmyUid(data.uid);
            eventCenter.emit(EventType_1.default.UPDATE_BATTLE_ARMY_COUNT_SHOW);
        }
        else if (data.type === 4) { //添加士兵
            this.area.addPawn(data.armyUid, data.pawn);
            this.battleController.addFighters(data.fighters);
        }
        else if (data.type === 5) { //同步增援数量
            this.defenderArmyAcc = data.hp[0] || 0;
            this.attackerArmyAcc = data.hp[1] || 1;
            eventCenter.emit(EventType_1.default.UPDATE_BATTLE_ARMY_COUNT_SHOW);
        }
        else if (data.type === 200) { //战斗结束 目前只用于回放
            this.area.battleEndByLocal();
        }
        if (data.randSeed) {
            this.battleController.getRandom().setSeed(data.randSeed);
        }
    };
    // 检测是否同步
    FSPModel.prototype.checkStateSync = function () {
        if (this.isLocal) {
            return; //本地战斗就不用检测同步了
        }
        else if (this.currentFrameIndex % this.checkFrameCount !== 0 || !this.battleController) {
            return;
        }
        var serverMd5 = this.serverSnapshootMD5Map[this.currentFrameIndex];
        var loaclMD5 = this.battleController.getSnapshootMD5();
        delete this.serverSnapshootMD5Map[this.currentFrameIndex];
        if (!serverMd5) {
            this.serverSnapshootMD5Map[this.currentFrameIndex] = loaclMD5;
        }
        else if (serverMd5 !== loaclMD5) {
            cc.log('不同步了 checkStateSync');
            cc.log('local', loaclMD5);
            cc.log('server', serverMd5);
            this.resync();
            this.noSyncReport(loaclMD5, serverMd5, this.currentFrameIndex);
        }
    };
    // 获取帧速度
    FSPModel.prototype.getFrameSpeed = function (frameNum) {
        var speed = 1;
        if (frameNum > 100) {
            speed = 8;
        }
        else if (frameNum > 50) {
            speed = 4;
        }
        else if (frameNum > 4) {
            speed = 2;
        }
        return speed;
    };
    FSPModel.prototype.noSyncReport = function (loaclMD5, serverMd5, currentFrameIndex) {
        var _a, _b;
        if (!LocalConfig_1.localConfig.RELEASE) {
        }
        else if (ErrorReportHelper_1.errorReportHelper.checkCountLimit('no_sync', 1)) {
            ErrorReportHelper_1.errorReportHelper.reportError('fsp no sync 2', {
                index: (_a = this.area) === null || _a === void 0 ? void 0 : _a.index,
                camps: (_b = this.battleController) === null || _b === void 0 ? void 0 : _b.getCamps(),
                currentFrameIndex: currentFrameIndex,
                local: loaclMD5,
                server: serverMd5,
            });
        }
    };
    return FSPModel;
}());
exports.default = FSPModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxmc3BcXEZTUE1vZGVsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQStEO0FBQy9ELDBEQUFxRDtBQUNyRCwyRUFBMEU7QUFDMUUsNkRBQXlEO0FBQ3pELHdEQUF1RDtBQUd2RCw2REFBd0Q7QUFFeEQsZUFBZTtBQUNmO0lBZ0NJO1FBOUJRLFFBQUcsR0FBaUIsSUFBSSxDQUFBO1FBQ3hCLFNBQUksR0FBWSxJQUFJLENBQUE7UUFDcEIscUJBQWdCLEdBQXdCLElBQUksQ0FBQTtRQUVuQyxxQkFBZ0IsR0FBRyxFQUFFLENBQUEsQ0FBQyx1QkFBdUI7UUFFdkQsY0FBUyxHQUFZLEtBQUssQ0FBQTtRQUN6QixZQUFPLEdBQVksS0FBSyxDQUFBLENBQUMsUUFBUTtRQUNqQyxRQUFHLEdBQVcsQ0FBQyxDQUFBLENBQUMsSUFBSTtRQUNwQixRQUFHLEdBQVcsQ0FBQyxDQUFBLENBQUMsTUFBTTtRQUN0QixPQUFFLEdBQVcsQ0FBQyxDQUFBO1FBQ2Qsd0JBQW1CLEdBQWEsSUFBSSxDQUFBO1FBQ3BDLGlCQUFZLEdBQVcsQ0FBQyxDQUFBLENBQUMsU0FBUztRQUNsQyxvQkFBZSxHQUFXLENBQUMsQ0FBQSxDQUFDLGdCQUFnQjtRQUM1QyxzQkFBaUIsR0FBVyxDQUFDLENBQUEsQ0FBQyxLQUFLO1FBQ25DLHFCQUFnQixHQUFXLENBQUMsQ0FBQSxDQUFDLFVBQVU7UUFDdkMscUJBQWdCLEdBQVcsQ0FBQyxDQUFBLENBQUMscUJBQXFCO1FBQ2xELDBCQUFxQixHQUE4QixFQUFFLENBQUEsQ0FBQyxvQkFBb0I7UUFDMUUsc0JBQWlCLEdBQTZCLEVBQUUsQ0FBQSxDQUFDLFNBQVM7UUFDMUQsb0JBQWUsR0FBVyxDQUFDLENBQUEsQ0FBQyxTQUFTO1FBQ3JDLG9CQUFlLEdBQVcsQ0FBQyxDQUFBLENBQUMsU0FBUztRQUVyQyxpQkFBWSxHQUFXLENBQUMsQ0FBQTtRQUN4QixnQkFBVyxHQUFXLENBQUMsQ0FBQTtRQUN2QixlQUFVLEdBQVcsQ0FBQyxDQUFBO1FBQ3RCLHFCQUFnQixHQUFXLENBQUMsQ0FBQTtRQUM1QixrQkFBYSxHQUFXLENBQUMsQ0FBQSxDQUFDLFVBQVU7UUFDcEMsVUFBSyxHQUFZLEtBQUssQ0FBQSxDQUFDLE1BQU07UUFDN0IsY0FBUyxHQUFXLENBQUMsQ0FBQTtRQUd6QixJQUFJLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDakMsQ0FBQztJQUVNLHVCQUFJLEdBQVgsVUFBWSxJQUFhLEVBQUUsSUFBUyxFQUFFLFlBQWtCLEVBQUUsUUFBaUIsRUFBRSxjQUF3Qjs7UUFDakcsRUFBRSxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDeEIsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsWUFBWSxJQUFJLENBQUMsY0FBYyxDQUFBO1FBQ2hELElBQUksQ0FBQyxHQUFHLFNBQUcsSUFBSSxDQUFDLEdBQUcsbUNBQUksRUFBRSxDQUFBO1FBQ3pCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUE7UUFDeEIsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsWUFBWSxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsRUFBRSxHQUFHLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFBO1FBQ2xGLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUE7UUFDckYsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUMsZUFBZSxJQUFJLEVBQUUsQ0FBQyxTQUFTLENBQUE7UUFDM0QsSUFBSSxDQUFDLGlCQUFpQixHQUFHLElBQUksQ0FBQyxnQkFBZ0IsU0FBRyxJQUFJLENBQUMsaUJBQWlCLG1DQUFJLENBQUMsQ0FBQTtRQUM1RSxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFBO1FBQzVGLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsVUFBVSxHQUFHLENBQUMsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFBO1FBQ3JFLElBQUksQ0FBQyxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ3JDLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQzVELElBQUksQ0FBQyxxQkFBcUIsR0FBRyxFQUFFLENBQUE7UUFDL0IsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsYUFBUixRQUFRLGNBQVIsUUFBUSxHQUFJLHdCQUFhLENBQUMsU0FBUyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQVgsQ0FBVyxDQUFDLENBQUMsQ0FBQTtRQUM1RSxJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQyxlQUFlLElBQUksQ0FBQyxDQUFBO1FBQ2hELElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUE7UUFDaEQsWUFBWTtRQUNaLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLDZCQUFtQixFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUNsRSxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsT0FBTyxJQUFJLGNBQWMsRUFBRTtZQUNoQyxJQUFJLENBQUMsaUJBQWlCLEdBQUcsWUFBWSxDQUFBO1NBQ3hDO2FBQU07WUFDSCxJQUFJLENBQUMsaUJBQWlCLEdBQUcsRUFBRSxDQUFBO1NBQzlCO1FBQ0QsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUE7UUFDckIsSUFBSSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUE7UUFDbEIsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUE7UUFDM0IsT0FBTyxJQUFJLENBQUE7SUFDZixDQUFDO0lBRU0sd0JBQUssR0FBWjs7UUFDSSxJQUFNLFFBQVEsU0FBRyxJQUFJLENBQUMsZ0JBQWdCLDBDQUFFLEtBQUssRUFBRSxDQUFBO1FBQy9DLE9BQU8sTUFBTSxDQUFDLE1BQU0sQ0FBQztZQUNqQixHQUFHLEVBQUUsSUFBSSxDQUFDLEdBQUc7WUFDYixpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO1lBQ3pDLFVBQVUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ2hDLGVBQWUsRUFBRSxJQUFJLENBQUMsZUFBZTtZQUNyQyxlQUFlLEVBQUUsSUFBSSxDQUFDLGVBQWU7U0FDeEMsRUFBRSxRQUFRLENBQUMsQ0FBQTtJQUNoQixDQUFDO0lBRU0sdUJBQUksR0FBWDs7UUFDSSxJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssQ0FBQTtRQUN0QixJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTtRQUNsQixNQUFBLElBQUksQ0FBQyxnQkFBZ0IsMENBQUUsSUFBSSxHQUFFO1FBQzdCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUE7UUFDNUIsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2QsV0FBVyxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLFlBQVksQ0FBQyxDQUFBO1NBQzNDO1FBQ0QsRUFBRSxDQUFDLEdBQUcsQ0FBQyxXQUFXLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsS0FBSyxDQUFDLEdBQUcsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUE7SUFDOUcsQ0FBQztJQUVNLHdCQUFLLEdBQVo7O1FBQ0ksT0FBTyxDQUFDLFFBQUMsSUFBSSxDQUFDLGdCQUFnQiwwQ0FBRSxLQUFLLEdBQUUsQ0FBQTtJQUMzQyxDQUFDO0lBRU0sOEJBQVcsR0FBbEIsVUFBbUIsUUFBYTtRQUM1QixJQUFJLEtBQUssR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQTtRQUMzQixJQUFJLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUNsQywwQkFBMEI7UUFDMUIsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLFFBQVEsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLGlCQUFpQixFQUFFLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQTtRQUNqRixXQUFXLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsaUJBQWlCLEVBQUUsS0FBSyxDQUFDLENBQUE7SUFDeEQsQ0FBQztJQUVELFNBQVM7SUFDSyx5QkFBTSxHQUFwQjs7Ozs7O3dCQUNJLElBQUksSUFBSSxDQUFDLE9BQU8sSUFBSSxvQkFBTyxDQUFDLFlBQVksRUFBRTs0QkFDdEMsc0JBQU07eUJBQ1Q7d0JBQ0QsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO3dCQUNMLEtBQUssR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQTt3QkFDUCxxQkFBTSxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxxQkFBcUIsRUFBRSxFQUFFLEtBQUssT0FBQSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs0QkFDOUYsZUFBZTswQkFEK0U7O3dCQUF4RixLQUFnQixTQUF3RSxFQUF0RixHQUFHLFNBQUEsRUFBRSxJQUFJLFVBQUE7d0JBRVgsUUFBUSxHQUFHLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxJQUFJLENBQUE7d0JBQzNCLElBQUksQ0FBQyxRQUFRLEVBQUU7NEJBQ1gsc0JBQU8sb0JBQU8sQ0FBQyxVQUFVLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxFQUFBO3lCQUM5Qzs2QkFBTSxJQUFJLFFBQVEsQ0FBQyxNQUFNLEVBQUU7NEJBQ3hCLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFBOzRCQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFBOzRCQUN2QixJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFBOzRCQUNyQyxXQUFXLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsaUJBQWlCLEVBQUUsS0FBSyxDQUFDLENBQUE7eUJBQ3ZEOzZCQUFNOzRCQUNILElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYSxHQUFHLFFBQVEsQ0FBQTs0QkFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFBO3lCQUMvQjs7Ozs7S0FDSjtJQUVNLHVDQUFvQixHQUEzQjtRQUNJLE9BQU8sSUFBSSxDQUFDLGlCQUFpQixDQUFBO0lBQ2pDLENBQUM7SUFFRCxTQUFTO0lBQ0YsZ0NBQWEsR0FBcEI7UUFDSSxPQUFPLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBO0lBQzNDLENBQUM7SUFFTSxzQ0FBbUIsR0FBMUI7UUFDSSxPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQTtJQUNoQyxDQUFDO0lBRUQsYUFBYTtJQUNOLCtCQUFZLEdBQW5COztRQUNJLGFBQU8sSUFBSSxDQUFDLGdCQUFnQiwwQ0FBRSxpQkFBaUIsR0FBRTtJQUNyRCxDQUFDO0lBRU0seUJBQU0sR0FBYixVQUFjLEVBQVU7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLElBQUksSUFBSSxDQUFDLEtBQUssRUFBRTtZQUMvQixPQUFNO1NBQ1Q7UUFDRCxJQUFJLENBQUMsVUFBVSxJQUFJLEVBQUUsQ0FBQTtRQUNyQixJQUFJLElBQUksQ0FBQyxVQUFVLElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRTtZQUNyQyxJQUFJLEVBQUUsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO2dCQUN4QixJQUFJLENBQUMsVUFBVSxHQUFHLENBQUMsQ0FBQTthQUN0QjtpQkFBTTtnQkFDSCxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQyxXQUFXLENBQUE7YUFDdEM7WUFDRCxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDL0IsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO2dCQUNYLElBQUksQ0FBQyxnQkFBZ0IsSUFBSSxDQUFDLENBQUEsQ0FBQyxTQUFTO2FBQ3ZDO1lBQ0QsV0FBVztZQUNYLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxpQkFBaUIsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLElBQUksSUFBSSxDQUFDLEdBQUcsR0FBRyxDQUFDLEVBQUU7Z0JBQ2xGLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFBO2dCQUNiLElBQUksQ0FBQyxnQkFBZ0IsSUFBSSxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUE7Z0JBQ3JELFdBQVcsQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQywwQkFBMEIsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO2FBQzFFO1NBQ0o7SUFDTCxDQUFDO0lBRU0sbUNBQWdCLEdBQXZCLFVBQXdCLEtBQWE7O1FBQ2pDLElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFBO1FBQzFCLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFlBQVksR0FBRyxDQUFDLE9BQUEsd0JBQWEsQ0FBQyxLQUFLLENBQUMsMENBQUUsR0FBRyxLQUFJLENBQUMsQ0FBQyxDQUFBO1FBQ3ZFLElBQUksQ0FBQyxVQUFVLEdBQUcsQ0FBQyxDQUFBO0lBQ3ZCLENBQUM7SUFFTSxtQ0FBZ0IsR0FBdkI7UUFDSSxPQUFPLElBQUksQ0FBQyxhQUFhLENBQUE7SUFDN0IsQ0FBQztJQUVNLHlCQUFNLEdBQWI7UUFDSSxPQUFPLElBQUksQ0FBQyxHQUFHLENBQUE7SUFDbkIsQ0FBQztJQUVNLHFDQUFrQixHQUF6QjtRQUNJLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQTtJQUMvQixDQUFDO0lBRU0scUNBQWtCLEdBQXpCO1FBQ0ksT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFBO0lBQy9CLENBQUM7SUFFRCxLQUFLO0lBQ0UsMkJBQVEsR0FBZixVQUFnQixHQUFZO1FBQ3hCLElBQUksQ0FBQyxLQUFLLEdBQUcsR0FBRyxDQUFBO0lBQ3BCLENBQUM7SUFDTSwwQkFBTyxHQUFkO1FBQ0ksT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFBO0lBQ3JCLENBQUM7SUFFRCxjQUFjO0lBQ1Asa0NBQWUsR0FBdEIsVUFBdUIsSUFBUztRQUM1QixJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7WUFDZCxPQUFNO1NBQ1Q7UUFDRCwwREFBMEQ7UUFDMUQsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxpQkFBaUIsQ0FBQTtRQUM5QyxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLGdCQUFnQixDQUFBLENBQUMsY0FBYztRQUMzRyxVQUFVO1FBQ1YsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUU7U0FDZjthQUFNLElBQUksSUFBSSxDQUFDLGlCQUFpQixHQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLGtCQUFrQjtZQUMzRSxFQUFFLENBQUMsR0FBRyxDQUFDLCtFQUErRSxHQUFHLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxxQkFBcUIsR0FBRyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtZQUNoSywyRUFBMkU7WUFDM0UsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFBO1lBQ2IsT0FBTTtTQUNUO2FBQU07WUFDSCxJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUE7WUFDdkQsSUFBSSxDQUFDLEdBQUcsRUFBRTtnQkFDTixHQUFHLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLEVBQUUsQ0FBQTthQUMzRDtZQUNELEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7WUFDZCxJQUFJLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUE7U0FDakQ7UUFDRCxPQUFPO1FBQ1AsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtZQUM3RCxJQUFJLENBQUMsR0FBRyxFQUFFO2dCQUNOLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFBO2FBQ3hFO2lCQUFNO2dCQUNILE9BQU8sSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO2dCQUN4RCxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUMsWUFBWSxFQUFFO29CQUMzQixFQUFFLENBQUMsR0FBRyxDQUFDLDBCQUEwQixDQUFDLENBQUE7b0JBQ2xDLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLEdBQUcsQ0FBQyxDQUFBO29CQUNwQixFQUFFLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUE7b0JBQ25DLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQTtvQkFDYixJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO2lCQUNuRTthQUNKO1NBQ0o7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLHVCQUFJLEdBQVo7UUFDSSxhQUFhO1FBQ2IsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ3ZDLElBQUksU0FBUyxHQUFHLENBQUMsRUFBRTtZQUNmLE9BQU07U0FDVDtRQUNELE9BQU87UUFDUCxJQUFJLEtBQUssR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLFNBQVMsQ0FBQyxDQUFBO1FBQ3pDLEtBQUs7UUFDTCxPQUFPLEtBQUssR0FBRyxDQUFDLEVBQUU7WUFDZCxLQUFLLElBQUksQ0FBQyxDQUFBO1lBQ1YsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFBO1NBQ3pCO0lBQ0wsQ0FBQztJQUVPLGlDQUFjLEdBQXRCO1FBQ0ksSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2QsT0FBTyxDQUFDLENBQUE7U0FDWDthQUFNLElBQUksSUFBSSxDQUFDLGlCQUFpQixHQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUN2RCxPQUFPLENBQUMsQ0FBQyxDQUFBLENBQUMsZUFBZTtTQUM1QjtRQUNELGFBQWE7UUFDYixPQUFPLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsaUJBQWlCLENBQUE7SUFDekQsQ0FBQztJQUVELFNBQVM7SUFDRiw2Q0FBMEIsR0FBakM7O1FBQ0ksSUFBSSxDQUFDLGlCQUFpQixJQUFJLENBQUMsQ0FBQTtRQUMzQixJQUFJLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtZQUMxQixJQUFJLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUE7U0FDbkQ7UUFDRCxNQUFBLElBQUksQ0FBQyxnQkFBZ0IsMENBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7SUFDL0MsQ0FBQztJQUVELE9BQU87SUFDQyxrQ0FBZSxHQUF2Qjs7UUFDSSxJQUFJLENBQUMsaUJBQWlCLElBQUksQ0FBQyxDQUFBO1FBQzNCLElBQUksSUFBSSxDQUFDLG1CQUFtQixFQUFFO1lBQzFCLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQTtTQUNuRDtRQUNELE1BQUEsSUFBSSxDQUFDLGdCQUFnQiwwQ0FBRSxXQUFXLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBQztRQUMzQyxJQUFJLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUMzQyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7SUFDekIsQ0FBQztJQUVNLHVDQUFvQixHQUEzQixVQUE0QixFQUFZO1FBQ3BDLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxFQUFFLENBQUE7SUFDakMsQ0FBQztJQUVELFdBQVc7SUFDSCxvQ0FBaUIsR0FBekIsVUFBMEIsaUJBQXlCO1FBQW5ELGlCQVFDO1FBUEcsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGlCQUFpQixDQUFDLENBQUE7UUFDckQsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUNoQyxPQUFNO1NBQ1Q7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxFQUFFLHFCQUFxQjtZQUM3QyxPQUFPLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1NBQ25EO1FBQ0QsR0FBRyxDQUFDLE9BQU8sQ0FBQyxVQUFBLElBQUksSUFBSSxPQUFBLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsRUFBaEMsQ0FBZ0MsQ0FBQyxDQUFBO0lBQ3pELENBQUM7SUFFTSx3Q0FBcUIsR0FBNUIsVUFBNkIsSUFBUztRQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFO1lBQ3hCLE9BQU07U0FDVDthQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxDQUFDLEVBQUUsRUFBRSxNQUFNO1lBQ2hDLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUM1QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtZQUNoRCxXQUFXLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsNkJBQTZCLENBQUMsQ0FBQTtZQUN6RCxFQUFFLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxXQUFXLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUNoRTthQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxDQUFDLEVBQUUsRUFBRSxRQUFRO1lBQ2xDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDNUIsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUM1QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQzNFLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEdBQUcsQ0FBQyxFQUFFLEVBQUUsWUFBWTtnQkFDbkMsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTthQUN2RTtTQUNKO2FBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLENBQUMsRUFBRSxFQUFFLE1BQU07WUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzlCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyx1QkFBdUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7WUFDdkQsV0FBVyxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLDZCQUE2QixDQUFDLENBQUE7U0FDNUQ7YUFBTSxJQUFJLElBQUksQ0FBQyxJQUFJLEtBQUssQ0FBQyxFQUFFLEVBQUUsTUFBTTtZQUNoQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUMxQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtTQUNuRDthQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxDQUFDLEVBQUUsRUFBRSxRQUFRO1lBQ2xDLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUE7WUFDdEMsSUFBSSxDQUFDLGVBQWUsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUN0QyxXQUFXLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsNkJBQTZCLENBQUMsQ0FBQTtTQUM1RDthQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxHQUFHLEVBQUUsRUFBRSxjQUFjO1lBQzFDLElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtTQUMvQjtRQUNELElBQUksSUFBSSxDQUFDLFFBQVEsRUFBRTtZQUNmLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1NBQzNEO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxpQ0FBYyxHQUF0QjtRQUNJLElBQUksSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNkLE9BQU0sQ0FBQyxjQUFjO1NBQ3hCO2FBQU0sSUFBSSxJQUFJLENBQUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLGVBQWUsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLEVBQUU7WUFDdEYsT0FBTTtTQUNUO1FBQ0QsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1FBQ3BFLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxlQUFlLEVBQUUsQ0FBQTtRQUN4RCxPQUFPLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQTtRQUN6RCxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ1osSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLFFBQVEsQ0FBQTtTQUNoRTthQUFNLElBQUksU0FBUyxLQUFLLFFBQVEsRUFBRTtZQUMvQixFQUFFLENBQUMsR0FBRyxDQUFDLHFCQUFxQixDQUFDLENBQUE7WUFDN0IsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsUUFBUSxDQUFDLENBQUE7WUFDekIsRUFBRSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEVBQUUsU0FBUyxDQUFDLENBQUE7WUFDM0IsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFBO1lBQ2IsSUFBSSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsU0FBUyxFQUFFLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1NBQ2pFO0lBQ0wsQ0FBQztJQUVELFFBQVE7SUFDQSxnQ0FBYSxHQUFyQixVQUFzQixRQUFnQjtRQUNsQyxJQUFJLEtBQUssR0FBRyxDQUFDLENBQUE7UUFDYixJQUFJLFFBQVEsR0FBRyxHQUFHLEVBQUU7WUFDaEIsS0FBSyxHQUFHLENBQUMsQ0FBQTtTQUNaO2FBQU0sSUFBSSxRQUFRLEdBQUcsRUFBRSxFQUFFO1lBQ3RCLEtBQUssR0FBRyxDQUFDLENBQUE7U0FDWjthQUFNLElBQUksUUFBUSxHQUFHLENBQUMsRUFBRTtZQUNyQixLQUFLLEdBQUcsQ0FBQyxDQUFBO1NBQ1o7UUFDRCxPQUFPLEtBQUssQ0FBQTtJQUNoQixDQUFDO0lBRU8sK0JBQVksR0FBcEIsVUFBcUIsUUFBZ0IsRUFBRSxTQUFpQixFQUFFLGlCQUF5Qjs7UUFDL0UsSUFBSSxDQUFDLHlCQUFXLENBQUMsT0FBTyxFQUFFO1NBQ3pCO2FBQU0sSUFBSSxxQ0FBaUIsQ0FBQyxlQUFlLENBQUMsU0FBUyxFQUFFLENBQUMsQ0FBQyxFQUFFO1lBQ3hELHFDQUFpQixDQUFDLFdBQVcsQ0FBQyxlQUFlLEVBQUU7Z0JBQzNDLEtBQUssUUFBRSxJQUFJLENBQUMsSUFBSSwwQ0FBRSxLQUFLO2dCQUN2QixLQUFLLFFBQUUsSUFBSSxDQUFDLGdCQUFnQiwwQ0FBRSxRQUFRLEVBQUU7Z0JBQ3hDLGlCQUFpQixFQUFFLGlCQUFpQjtnQkFDcEMsS0FBSyxFQUFFLFFBQVE7Z0JBQ2YsTUFBTSxFQUFFLFNBQVM7YUFDcEIsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBQ0wsZUFBQztBQUFELENBM1hBLEFBMlhDLElBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQTEFZQkFDS19NVUxTIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiO1xuaW1wb3J0IHsgZXJyb3JSZXBvcnRIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9FcnJvclJlcG9ydEhlbHBlclwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IGxvY2FsQ29uZmlnIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9Mb2NhbENvbmZpZ1wiO1xuaW1wb3J0IEFyZWFPYmogZnJvbSBcIi4uL2FyZWEvQXJlYU9ialwiO1xuaW1wb3J0IE5ldHdvcmtNb2RlbCBmcm9tIFwiLi4vY29tbW9uL05ldHdvcmtNb2RlbFwiO1xuaW1wb3J0IEZTUEJhdHRsZUNvbnRyb2xsZXIgZnJvbSBcIi4vRlNQQmF0dGxlQ29udHJvbGxlclwiO1xuXG4vLyDluKflkIzmraUg5LiA5Zy65oiY5paX5a+55bqU5LiA5LiqXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBGU1BNb2RlbCB7XG5cbiAgICBwcml2YXRlIG5ldDogTmV0d29ya01vZGVsID0gbnVsbFxuICAgIHByaXZhdGUgYXJlYTogQXJlYU9iaiA9IG51bGxcbiAgICBwcml2YXRlIGJhdHRsZUNvbnRyb2xsZXI6IEZTUEJhdHRsZUNvbnRyb2xsZXIgPSBudWxsXG5cbiAgICBwcml2YXRlIHJlYWRvbmx5IExPQ0tFRF9NQVhfRlJBTUUgPSAyMCAvL+WuouaIt+err+WPr+S7peWkmuS9meacjeWKoeWZqOi/mOayoeS4i+WPkeeahOaXtuWAmSDlpJrlsJHluKdcblxuICAgIHB1YmxpYyBpc1J1bm5pbmc6IGJvb2xlYW4gPSBmYWxzZVxuICAgIHByaXZhdGUgaXNMb2NhbDogYm9vbGVhbiA9IGZhbHNlIC8v5piv5ZCm5pys5Zyw5oiY5paXXG4gICAgcHJpdmF0ZSBmcHM6IG51bWJlciA9IDAgLy/luKfnjodcbiAgICBwcml2YXRlIG11bDogbnVtYmVyID0gMCAvL+WKoOmAn+WAjeaVsFxuICAgIHByaXZhdGUgZHQ6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIG9uQ2hlY2tIYXNGcmFtZURhdGE6IEZ1bmN0aW9uID0gbnVsbFxuICAgIHByaXZhdGUgdXBTcGVlZEZyYW1lOiBudW1iZXIgPSAwIC8v5aSa5bCR5bin5byA5aeL5Yqg6YCfXG4gICAgcHJpdmF0ZSBjaGVja0ZyYW1lQ291bnQ6IG51bWJlciA9IDAgLy/mnI3liqHlmajlpJrlsJHluKflkJHlrqLmiLfnq6/lkIzmraXkuIDmrKFcbiAgICBwcml2YXRlIGN1cnJlbnRGcmFtZUluZGV4OiBudW1iZXIgPSAwIC8v5b2T5YmN5binXG4gICAgcHJpdmF0ZSBzZXJ2ZXJGcmFtZUluZGV4OiBudW1iZXIgPSAwIC8v5pyN5Yqh5Zmo5b2T5YmN55qE5bin5pWwXG4gICAgcHJpdmF0ZSBsb2NrZWRGcmFtZUluZGV4OiBudW1iZXIgPSAwIC8v6ZSB5a6a5binIOWuouaIt+err+ebuOi+g+S6juacjeWKoeWZqOmcgOimgeaFouWkmuWwkeW4p1xuICAgIHByaXZhdGUgc2VydmVyU25hcHNob290TUQ1TWFwOiB7IFtrZXk6IG51bWJlcl06IHN0cmluZyB9ID0ge30gLy/lv6vnhadtZDUg55So5LqO5ZKM5a6i5oi356uv5q+U6L6D5piv5ZCm5ZCM5q2lXG4gICAgcHJpdmF0ZSBvdGhlckZyYW1lRGF0YU1hcDogeyBba2V5OiBudW1iZXJdOiBhbnlbXSB9ID0ge30gLy/lhbbku5bluKflkIzmraXmlbDmja5cbiAgICBwcml2YXRlIGF0dGFja2VyQXJteUFjYzogbnVtYmVyID0gMCAvL+aUu+WHu+aWueihpeWFteasoeaVsFxuICAgIHByaXZhdGUgZGVmZW5kZXJBcm15QWNjOiBudW1iZXIgPSAwIC8v6Ziy5a6I5pa56KGl5YW15qyh5pWwXG5cbiAgICBwcml2YXRlIGluaXRJbnRlcnZhbDogbnVtYmVyID0gMFxuICAgIHByaXZhdGUgZnBzSW50ZXJ2YWw6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIGZwc0VsYXBzZWQ6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIGN1cnJVcFNwZWVkRnJhbWU6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIHNwZWVkTXVsSW5kZXg6IG51bWJlciA9IDIgLy/lvZPliY3liqDpgJ/lgI3mlbDkuIvmoIdcbiAgICBwcml2YXRlIHBhdXNlOiBib29sZWFuID0gZmFsc2UgLy/mmK/lkKbmmoLlgZxcbiAgICBwcml2YXRlIHN0YXJ0VGltZTogbnVtYmVyID0gMFxuXG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMubmV0ID0gbWMuZ2V0TW9kZWwoJ25ldCcpXG4gICAgfVxuXG4gICAgcHVibGljIGluaXQoYXJlYTogQXJlYU9iaiwgZGF0YTogYW55LCBmcmFtZURhdGFNYXA/OiBhbnksIG11bEluZGV4PzogbnVtYmVyLCBzZXJ2ZXJQbGF5QmFjaz86IGJvb2xlYW4pIHtcbiAgICAgICAgY2MubG9nKCdmc3AgaW5pdCcsIGRhdGEpXG4gICAgICAgIHRoaXMuYXJlYSA9IGFyZWFcbiAgICAgICAgdGhpcy5pc0xvY2FsID0gISFmcmFtZURhdGFNYXAgJiYgIXNlcnZlclBsYXlCYWNrXG4gICAgICAgIHRoaXMuZnBzID0gZGF0YS5mcHMgPz8gMjBcbiAgICAgICAgdGhpcy5tdWwgPSBkYXRhLm11bCB8fCAxXG4gICAgICAgIHRoaXMudXBTcGVlZEZyYW1lID0gZGF0YS51cFNwZWVkRnJhbWUgfHwgKHV0LlRpbWUuTWludXRlICogMzAgLyAoMTAwMCAvIHRoaXMuZnBzKSlcbiAgICAgICAgdGhpcy5jdXJyVXBTcGVlZEZyYW1lID0gdGhpcy51cFNwZWVkRnJhbWUgKiBNYXRoLmZsb29yKCh0aGlzLm11bCArIDEpICogdGhpcy5tdWwgLyAyKVxuICAgICAgICB0aGlzLmNoZWNrRnJhbWVDb3VudCA9IGRhdGEuY2hlY2tGcmFtZUNvdW50IHx8IHV0Lk1BWF9WQUxVRVxuICAgICAgICB0aGlzLmN1cnJlbnRGcmFtZUluZGV4ID0gdGhpcy5zZXJ2ZXJGcmFtZUluZGV4ID0gZGF0YS5jdXJyZW50RnJhbWVJbmRleCA/PyAwXG4gICAgICAgIHRoaXMubG9ja2VkRnJhbWVJbmRleCA9IHRoaXMuc2VydmVyRnJhbWVJbmRleCArIHRoaXMuY2hlY2tGcmFtZUNvdW50ICsgdGhpcy5MT0NLRURfTUFYX0ZSQU1FXG4gICAgICAgIHRoaXMuZnBzSW50ZXJ2YWwgPSB0aGlzLmluaXRJbnRlcnZhbCA9IHRoaXMuZnBzRWxhcHNlZCA9IDEgLyB0aGlzLmZwc1xuICAgICAgICB0aGlzLmR0ID0gTWF0aC5mbG9vcigxMDAwIC8gdGhpcy5mcHMpXG4gICAgICAgIHRoaXMub25DaGVja0hhc0ZyYW1lRGF0YSA9IHRoaXMuY2hlY2tIYXNGcmFtZURhdGEuYmluZCh0aGlzKVxuICAgICAgICB0aGlzLnNlcnZlclNuYXBzaG9vdE1ENU1hcCA9IHt9XG4gICAgICAgIHRoaXMuc2V0U3BlZWRNdWxJbmRleChtdWxJbmRleCA/PyBQTEFZQkFDS19NVUxTLmZpbmRJbmRleChtID0+IG0udmFsID09PSAxKSlcbiAgICAgICAgdGhpcy5kZWZlbmRlckFybXlBY2MgPSBkYXRhLmRlZmVuZGVyQXJteUFjYyB8fCAwXG4gICAgICAgIHRoaXMuYXR0YWNrZXJBcm15QWNjID0gZGF0YS5hdHRhY2tlckFybXlBY2MgfHwgMVxuICAgICAgICAvLyDliJ3lp4vljJbmiJjmlpfpgLvovpHmjqfliLZcbiAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyID0gbmV3IEZTUEJhdHRsZUNvbnRyb2xsZXIoKS5pbml0KGFyZWEsIGRhdGEpXG4gICAgICAgIC8vIOebkeWQrOacjeWKoeWZqOWumuaXtuWQjOatpeW4p+S/oeaBr1xuICAgICAgICBpZiAodGhpcy5pc0xvY2FsIHx8IHNlcnZlclBsYXlCYWNrKSB7XG4gICAgICAgICAgICB0aGlzLm90aGVyRnJhbWVEYXRhTWFwID0gZnJhbWVEYXRhTWFwXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLm90aGVyRnJhbWVEYXRhTWFwID0ge31cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmlzUnVubmluZyA9IHRydWVcbiAgICAgICAgdGhpcy5wYXVzZSA9IGZhbHNlXG4gICAgICAgIHRoaXMuc3RhcnRUaW1lID0gRGF0ZS5ub3coKVxuICAgICAgICByZXR1cm4gdGhpc1xuICAgIH1cblxuICAgIHB1YmxpYyBzdHJpcCgpIHtcbiAgICAgICAgY29uc3QgY3RybFN0aXAgPSB0aGlzLmJhdHRsZUNvbnRyb2xsZXI/LnN0cmlwKClcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe1xuICAgICAgICAgICAgbXVsOiB0aGlzLm11bCxcbiAgICAgICAgICAgIGN1cnJlbnRGcmFtZUluZGV4OiB0aGlzLmN1cnJlbnRGcmFtZUluZGV4LFxuICAgICAgICAgICAgYmF0dGxlVGltZTogdGhpcy5nZXRCYXR0bGVUaW1lKCksXG4gICAgICAgICAgICBkZWZlbmRlckFybXlBY2M6IHRoaXMuZGVmZW5kZXJBcm15QWNjLFxuICAgICAgICAgICAgYXR0YWNrZXJBcm15QWNjOiB0aGlzLmF0dGFja2VyQXJteUFjY1xuICAgICAgICB9LCBjdHJsU3RpcClcbiAgICB9XG5cbiAgICBwdWJsaWMgc3RvcCgpIHtcbiAgICAgICAgdGhpcy5pc1J1bm5pbmcgPSBmYWxzZVxuICAgICAgICB0aGlzLnBhdXNlID0gZmFsc2VcbiAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyPy5zdG9wKClcbiAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyID0gbnVsbFxuICAgICAgICBpZiAodGhpcy5pc0xvY2FsKSB7XG4gICAgICAgICAgICBldmVudENlbnRlci5lbWl0KEV2ZW50VHlwZS5QTEFZQkFDS19FTkQpXG4gICAgICAgIH1cbiAgICAgICAgY2MubG9nKCdmc3Agc3RvcCAnICsgKChEYXRlLm5vdygpIC0gdGhpcy5zdGFydFRpbWUpICogMC4wMDEpICsgJ3MsIGZyYW1lQ291bnQ6ICcgKyB0aGlzLmN1cnJlbnRGcmFtZUluZGV4KVxuICAgIH1cblxuICAgIHB1YmxpYyBpc1dpbigpIHtcbiAgICAgICAgcmV0dXJuICEhdGhpcy5iYXR0bGVDb250cm9sbGVyPy5pc1dpbigpXG4gICAgfVxuXG4gICAgcHVibGljIHJlc3luY0xvY2FsKGFyZWFJbmZvOiBhbnkpIHtcbiAgICAgICAgbGV0IGluZGV4ID0gdGhpcy5hcmVhLmluZGV4XG4gICAgICAgIHRoaXMuYXJlYS5pbml0QmF0dGxlRGF0YShhcmVhSW5mbylcbiAgICAgICAgLy8gdGhpcy5hcmVhLmJhdHRsZVJlYWR5KClcbiAgICAgICAgdGhpcy5pbml0KHRoaXMuYXJlYSwgYXJlYUluZm8uYmF0dGxlLCB0aGlzLm90aGVyRnJhbWVEYXRhTWFwLCB0aGlzLnNwZWVkTXVsSW5kZXgpXG4gICAgICAgIGV2ZW50Q2VudGVyLmVtaXQoRXZlbnRUeXBlLkFSRUFfQkFUVExFX0JFR0lOLCBpbmRleClcbiAgICB9XG5cbiAgICAvLyDph43mlrDlkIzmraXkv6Hmga9cbiAgICBwcml2YXRlIGFzeW5jIHJlc3luYygpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNMb2NhbCB8fCBnYW1lSHByLmlzTm92aWNlTW9kZSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdG9wKClcbiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmFyZWEuaW5kZXhcbiAgICAgICAgY29uc3QgeyBlcnIsIGRhdGEgfSA9IGF3YWl0IHRoaXMubmV0LnJlcXVlc3QoJ2dhbWUvSERfR2V0QXJlYUluZm8nLCB7IGluZGV4LCBub1JlY29yZDogdHJ1ZSB9KVxuICAgICAgICAvLyBjYy5sb2coZGF0YSlcbiAgICAgICAgY29uc3QgYXJlYUluZm8gPSBkYXRhPy5kYXRhXG4gICAgICAgIGlmICghYXJlYUluZm8pIHtcbiAgICAgICAgICAgIHJldHVybiBnYW1lSHByLmFyZWFDZW50ZXIucmVtb3ZlQXJlYShpbmRleClcbiAgICAgICAgfSBlbHNlIGlmIChhcmVhSW5mby5iYXR0bGUpIHtcbiAgICAgICAgICAgIHRoaXMuYXJlYS5pbml0QmF0dGxlRGF0YShhcmVhSW5mbylcbiAgICAgICAgICAgIHRoaXMuYXJlYS5iYXR0bGVSZWFkeSgpXG4gICAgICAgICAgICB0aGlzLmluaXQodGhpcy5hcmVhLCBhcmVhSW5mby5iYXR0bGUpXG4gICAgICAgICAgICBldmVudENlbnRlci5lbWl0KEV2ZW50VHlwZS5BUkVBX0JBVFRMRV9CRUdJTiwgaW5kZXgpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmFyZWEuYmF0dGxlRW5kRGF0YSA9IGFyZWFJbmZvXG4gICAgICAgICAgICB0aGlzLmFyZWEuYmF0dGxlRW5kQnlMb2NhbCgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgZ2V0Q3VycmVudEZyYW1lSW5kZXgoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRGcmFtZUluZGV4XG4gICAgfVxuXG4gICAgLy8g6I635Y+W5oiY5paX5pe26Ze0XG4gICAgcHVibGljIGdldEJhdHRsZVRpbWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRGcmFtZUluZGV4ICogdGhpcy5kdFxuICAgIH1cblxuICAgIHB1YmxpYyBnZXRCYXR0bGVDb250cm9sbGVyKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5iYXR0bGVDb250cm9sbGVyXG4gICAgfVxuXG4gICAgLy8g5piv5ZCm5b2T5YmN5aOr5YW15Zue5ZCI57uT5p2fXG4gICAgcHVibGljIHJlY29yZEVuYWJsZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYmF0dGxlQ29udHJvbGxlcj8uaXNDdXJyZW50Um91bmRFbmQoKVxuICAgIH1cblxuICAgIHB1YmxpYyB1cGRhdGUoZHQ6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuaXNSdW5uaW5nIHx8IHRoaXMucGF1c2UpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHRoaXMuZnBzRWxhcHNlZCArPSBkdFxuICAgICAgICBpZiAodGhpcy5mcHNFbGFwc2VkID49IHRoaXMuZnBzSW50ZXJ2YWwpIHtcbiAgICAgICAgICAgIGlmIChkdCA+PSB0aGlzLmZwc0ludGVydmFsKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mcHNFbGFwc2VkID0gMFxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLmZwc0VsYXBzZWQgLT0gdGhpcy5mcHNJbnRlcnZhbFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLm11bDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgdGhpcy50aWNrKClcbiAgICAgICAgICAgICAgICB0aGlzLnNlcnZlckZyYW1lSW5kZXggKz0gMSAvL+aooeaLn+acjeWKoeWZqOeahOW4p1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8g5Yqg6YCfIOWbnuaUvuS4jeWKoOmAn1xuICAgICAgICAgICAgaWYgKCF0aGlzLmlzTG9jYWwgJiYgdGhpcy5jdXJyZW50RnJhbWVJbmRleCA+PSB0aGlzLmN1cnJVcFNwZWVkRnJhbWUgJiYgdGhpcy5tdWwgPCAzKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5tdWwgKz0gMVxuICAgICAgICAgICAgICAgIHRoaXMuY3VyclVwU3BlZWRGcmFtZSArPSB0aGlzLnVwU3BlZWRGcmFtZSAqIHRoaXMubXVsXG4gICAgICAgICAgICAgICAgZXZlbnRDZW50ZXIuZW1pdChFdmVudFR5cGUuVVBEQVRFX0FSRUFfQkFUVExFX1RJTUVfVUksIHRoaXMuYXJlYS5pbmRleClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIHB1YmxpYyBzZXRTcGVlZE11bEluZGV4KGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5zcGVlZE11bEluZGV4ID0gaW5kZXhcbiAgICAgICAgdGhpcy5mcHNJbnRlcnZhbCA9IHRoaXMuaW5pdEludGVydmFsICogKFBMQVlCQUNLX01VTFNbaW5kZXhdPy52YWwgfHwgMSlcbiAgICAgICAgdGhpcy5mcHNFbGFwc2VkID0gMFxuICAgIH1cblxuICAgIHB1YmxpYyBnZXRTcGVlZE11bEluZGV4KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zcGVlZE11bEluZGV4XG4gICAgfVxuXG4gICAgcHVibGljIGdldE11bCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubXVsXG4gICAgfVxuXG4gICAgcHVibGljIGdldEF0dGFja2VyQXJteUFjYygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYXR0YWNrZXJBcm15QWNjXG4gICAgfVxuXG4gICAgcHVibGljIGdldERlZmVuZGVyQXJteUFjYygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZGVmZW5kZXJBcm15QWNjXG4gICAgfVxuXG4gICAgLy8g5pqC5YGcXG4gICAgcHVibGljIHNldFBhdXNlKHZhbDogYm9vbGVhbikge1xuICAgICAgICB0aGlzLnBhdXNlID0gdmFsXG4gICAgfVxuICAgIHB1YmxpYyBpc1BhdXNlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wYXVzZVxuICAgIH1cblxuICAgIC8vIOacjeWKoeWZqOS4i+WPkeeahOW9k+WJjeW4p+S/oeaBr1xuICAgIHB1YmxpYyBvbkZTUENoZWNrRnJhbWUoZGF0YTogYW55KSB7XG4gICAgICAgIGlmICh0aGlzLmlzTG9jYWwpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIC8vIGNjLmxvZygnT25GU1BDaGVja0ZyYW1lJywgdGhpcy5jdXJyZW50RnJhbWVJbmRleCwgZGF0YSlcbiAgICAgICAgdGhpcy5zZXJ2ZXJGcmFtZUluZGV4ID0gZGF0YS5jdXJyZW50RnJhbWVJbmRleFxuICAgICAgICB0aGlzLmxvY2tlZEZyYW1lSW5kZXggPSB0aGlzLnNlcnZlckZyYW1lSW5kZXggKyB0aGlzLmNoZWNrRnJhbWVDb3VudCArIHRoaXMuTE9DS0VEX01BWF9GUkFNRSAvL+WKoOS4iuWPr+S7peWkmui3keWHuuacjeWKoeWZqOeahOW4p1xuICAgICAgICAvLyDmnInlhbbku5blkIzmraXkv6Hmga9cbiAgICAgICAgaWYgKCFkYXRhLnR5cGUpIHtcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRGcmFtZUluZGV4ID4gdGhpcy5zZXJ2ZXJGcmFtZUluZGV4KSB7IC8v5aaC5p6c6LeR5Yiw5YmN6Z2i5Y675LqGIOWwseebtOaOpemHjeaWsOWQjOatpVxuICAgICAgICAgICAgY2MubG9nKCfkuI3lkIzmraXkuoYgT25GU1BDaGVja0ZyYW1lIGN1cnJlbnRGcmFtZUluZGV4ID4gc2VydmVyRnJhbWVJbmRleCwgY3VycmVudEZyYW1lSW5kZXg9JyArIHRoaXMuY3VycmVudEZyYW1lSW5kZXggKyAnLCBzZXJ2ZXJGcmFtZUluZGV4PScgKyB0aGlzLnNlcnZlckZyYW1lSW5kZXgpXG4gICAgICAgICAgICAvLyBlcnJvclJlcG9ydEhlbHBlci5yZXBvcnRFcnJvcignZnNwIG5vIHN5bmMnLCB7IGluZGV4OiB0aGlzLmFyZWEuaW5kZXggfSlcbiAgICAgICAgICAgIHRoaXMucmVzeW5jKClcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbGV0IGFyciA9IHRoaXMub3RoZXJGcmFtZURhdGFNYXBbdGhpcy5zZXJ2ZXJGcmFtZUluZGV4XVxuICAgICAgICAgICAgaWYgKCFhcnIpIHtcbiAgICAgICAgICAgICAgICBhcnIgPSB0aGlzLm90aGVyRnJhbWVEYXRhTWFwW3RoaXMuc2VydmVyRnJhbWVJbmRleF0gPSBbXVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYXJyLnB1c2goZGF0YSlcbiAgICAgICAgICAgIHRoaXMuY2hlY2tIYXNGcmFtZURhdGEodGhpcy5jdXJyZW50RnJhbWVJbmRleClcbiAgICAgICAgfVxuICAgICAgICAvLyDmr5TovoPlv6vnhadcbiAgICAgICAgaWYgKGRhdGEuc25hcHNob290TUQ1KSB7XG4gICAgICAgICAgICBjb25zdCBtZDUgPSB0aGlzLnNlcnZlclNuYXBzaG9vdE1ENU1hcFt0aGlzLnNlcnZlckZyYW1lSW5kZXhdXG4gICAgICAgICAgICBpZiAoIW1kNSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2VydmVyU25hcHNob290TUQ1TWFwW3RoaXMuc2VydmVyRnJhbWVJbmRleF0gPSBkYXRhLnNuYXBzaG9vdE1ENVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBkZWxldGUgdGhpcy5zZXJ2ZXJTbmFwc2hvb3RNRDVNYXBbdGhpcy5zZXJ2ZXJGcmFtZUluZGV4XVxuICAgICAgICAgICAgICAgIGlmIChtZDUgIT09IGRhdGEuc25hcHNob290TUQ1KSB7XG4gICAgICAgICAgICAgICAgICAgIGNjLmxvZygn5LiN5ZCM5q2l5LqGIE9uRlNQQ2hlY2tGcmFtZSBkbTUnKVxuICAgICAgICAgICAgICAgICAgICBjYy5sb2coJ2xvY2FsJywgbWQ1KVxuICAgICAgICAgICAgICAgICAgICBjYy5sb2coJ3NlcnZlcicsIGRhdGEuc25hcHNob290TUQ1KVxuICAgICAgICAgICAgICAgICAgICB0aGlzLnJlc3luYygpXG4gICAgICAgICAgICAgICAgICAgIHRoaXMubm9TeW5jUmVwb3J0KG1kNSwgZGF0YS5zbmFwc2hvb3RNRDUsIHRoaXMuc2VydmVyRnJhbWVJbmRleClcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmr4/luKfliLfmlrBcbiAgICBwcml2YXRlIHRpY2soKSB7XG4gICAgICAgIC8vIOW9k+WJjeWSjOacjeWKoeWZqOW4p+eahOW3rui3nVxuICAgICAgICBjb25zdCBmcmFtZURpZmYgPSB0aGlzLmNoZWNrRnJhbWVEaWZmKClcbiAgICAgICAgaWYgKGZyYW1lRGlmZiA8IDApIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIC8vIOiOt+WPlumAn+W6plxuICAgICAgICBsZXQgc3BlZWQgPSB0aGlzLmdldEZyYW1lU3BlZWQoZnJhbWVEaWZmKVxuICAgICAgICAvLyDmiafooYxcbiAgICAgICAgd2hpbGUgKHNwZWVkID4gMCkge1xuICAgICAgICAgICAgc3BlZWQgLT0gMVxuICAgICAgICAgICAgdGhpcy5leGVjdXRlT25lRnJhbWUoKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHJpdmF0ZSBjaGVja0ZyYW1lRGlmZigpIHtcbiAgICAgICAgaWYgKHRoaXMuaXNMb2NhbCkge1xuICAgICAgICAgICAgcmV0dXJuIDBcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRGcmFtZUluZGV4ID4gdGhpcy5sb2NrZWRGcmFtZUluZGV4KSB7XG4gICAgICAgICAgICByZXR1cm4gLTEgLy/lpoLmnpzot5HliY3pnaLljrvkuoYg5bCx5YGc5q2i562J5b6FXG4gICAgICAgIH1cbiAgICAgICAgLy8g5b2T5YmN5ZKM5pyN5Yqh5Zmo5bin55qE5beu6LedXG4gICAgICAgIHJldHVybiB0aGlzLnNlcnZlckZyYW1lSW5kZXggLSB0aGlzLmN1cnJlbnRGcmFtZUluZGV4XG4gICAgfVxuXG4gICAgLy8g5ZCO5Y+w6K6h566X5LiA5binXG4gICAgcHVibGljIGV4ZWN1dGVPbmVGcmFtZUZvclNpbXVsYXRlKCkge1xuICAgICAgICB0aGlzLmN1cnJlbnRGcmFtZUluZGV4ICs9IDFcbiAgICAgICAgaWYgKHRoaXMub25DaGVja0hhc0ZyYW1lRGF0YSkge1xuICAgICAgICAgICAgdGhpcy5vbkNoZWNrSGFzRnJhbWVEYXRhKHRoaXMuY3VycmVudEZyYW1lSW5kZXgpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyPy51cGRhdGVGcmFtZSh0aGlzLmR0KVxuICAgIH1cblxuICAgIC8vIOaJp+ihjOS4gOW4p1xuICAgIHByaXZhdGUgZXhlY3V0ZU9uZUZyYW1lKCkge1xuICAgICAgICB0aGlzLmN1cnJlbnRGcmFtZUluZGV4ICs9IDFcbiAgICAgICAgaWYgKHRoaXMub25DaGVja0hhc0ZyYW1lRGF0YSkge1xuICAgICAgICAgICAgdGhpcy5vbkNoZWNrSGFzRnJhbWVEYXRhKHRoaXMuY3VycmVudEZyYW1lSW5kZXgpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyPy51cGRhdGVGcmFtZSh0aGlzLmR0KVxuICAgICAgICB0aGlzLmFyZWEudXBkYXRlUGF3bkFuaW1hdGlvbkZyYW1lKHRoaXMuZHQpXG4gICAgICAgIHRoaXMuY2hlY2tTdGF0ZVN5bmMoKVxuICAgIH1cblxuICAgIHB1YmxpYyBzZXRDaGVja0hhc0ZyYW1lRGF0YShjYjogRnVuY3Rpb24pIHtcbiAgICAgICAgdGhpcy5vbkNoZWNrSGFzRnJhbWVEYXRhID0gY2JcbiAgICB9XG5cbiAgICAvLyDmo4DmtYvmmK/lkKbmnInluKfmlbDmja5cbiAgICBwcml2YXRlIGNoZWNrSGFzRnJhbWVEYXRhKGN1cnJlbnRGcmFtZUluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgYXJyID0gdGhpcy5vdGhlckZyYW1lRGF0YU1hcFtjdXJyZW50RnJhbWVJbmRleF1cbiAgICAgICAgaWYgKCFhcnIgfHwgIXRoaXMuYmF0dGxlQ29udHJvbGxlcikge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAoIXRoaXMuaXNMb2NhbCkgeyAvL+acrOWcsOaVsOaNrueahOivnSDlsLHkuI3opoHliKDpmaTlm6DkuLrkuIvmrKHov5jopoHnlKhcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLm90aGVyRnJhbWVEYXRhTWFwW2N1cnJlbnRGcmFtZUluZGV4XVxuICAgICAgICB9XG4gICAgICAgIGFyci5mb3JFYWNoKGRhdGEgPT4gdGhpcy5jaGVja0hhc0ZyYW1lRGF0YUl0ZW0oZGF0YSkpXG4gICAgfVxuXG4gICAgcHVibGljIGNoZWNrSGFzRnJhbWVEYXRhSXRlbShkYXRhOiBhbnkpIHtcbiAgICAgICAgaWYgKCF0aGlzLmJhdHRsZUNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gMSkgeyAvL+a3u+WKoOWGm+mYn1xuICAgICAgICAgICAgdGhpcy5hcmVhLmFkZEFybXkoZGF0YS5hcm15KVxuICAgICAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyLmFkZEZpZ2h0ZXJzKGRhdGEuZmlnaHRlcnMpXG4gICAgICAgICAgICBldmVudENlbnRlci5lbWl0KEV2ZW50VHlwZS5VUERBVEVfQkFUVExFX0FSTVlfQ09VTlRfU0hPVylcbiAgICAgICAgICAgIGNjLmxvZyh0aGlzLmN1cnJlbnRGcmFtZUluZGV4ICsgJyBBZGRBcm15ICcgKyBkYXRhLmFybXkubmFtZSlcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09IDIpIHsgLy/liLfmlrDljLrln5/kv6Hmga9cbiAgICAgICAgICAgIHRoaXMuYXJlYS5jdXJIcCA9IGRhdGEuaHBbMF1cbiAgICAgICAgICAgIHRoaXMuYXJlYS5tYXhIcCA9IGRhdGEuaHBbMV1cbiAgICAgICAgICAgIHRoaXMuYmF0dGxlQ29udHJvbGxlci5VcGRhdGVCdWlsZEluZm8oZGF0YS5idWlsZEluZm9bMF0sIGRhdGEuYnVpbGRJbmZvWzFdKVxuICAgICAgICAgICAgaWYgKHRoaXMuYXJlYS5pbmRleCA8IDApIHsgLy/lm57mlL7nmoTor50g6KaB5Yi35paw5bu6562RXG4gICAgICAgICAgICAgICAgdGhpcy5hcmVhLnVwZGF0ZUNpdHlCeVBhd25JbmZvKGRhdGEuYnVpbGRJbmZvWzBdLCBkYXRhLmJ1aWxkSW5mb1sxXSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09IDMpIHsgLy/liKDpmaTlhpvpmJ9cbiAgICAgICAgICAgIHRoaXMuYXJlYS5yZW1vdmVBcm15KGRhdGEudWlkKVxuICAgICAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyLnJlbW92ZUZpZ2h0ZXJzQnlBcm15VWlkKGRhdGEudWlkKVxuICAgICAgICAgICAgZXZlbnRDZW50ZXIuZW1pdChFdmVudFR5cGUuVVBEQVRFX0JBVFRMRV9BUk1ZX0NPVU5UX1NIT1cpXG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS50eXBlID09PSA0KSB7IC8v5re75Yqg5aOr5YW1XG4gICAgICAgICAgICB0aGlzLmFyZWEuYWRkUGF3bihkYXRhLmFybXlVaWQsIGRhdGEucGF3bilcbiAgICAgICAgICAgIHRoaXMuYmF0dGxlQ29udHJvbGxlci5hZGRGaWdodGVycyhkYXRhLmZpZ2h0ZXJzKVxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gNSkgeyAvL+WQjOatpeWinuaPtOaVsOmHj1xuICAgICAgICAgICAgdGhpcy5kZWZlbmRlckFybXlBY2MgPSBkYXRhLmhwWzBdIHx8IDBcbiAgICAgICAgICAgIHRoaXMuYXR0YWNrZXJBcm15QWNjID0gZGF0YS5ocFsxXSB8fCAxXG4gICAgICAgICAgICBldmVudENlbnRlci5lbWl0KEV2ZW50VHlwZS5VUERBVEVfQkFUVExFX0FSTVlfQ09VTlRfU0hPVylcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnR5cGUgPT09IDIwMCkgeyAvL+aImOaWl+e7k+adnyDnm67liY3lj6rnlKjkuo7lm57mlL5cbiAgICAgICAgICAgIHRoaXMuYXJlYS5iYXR0bGVFbmRCeUxvY2FsKClcbiAgICAgICAgfVxuICAgICAgICBpZiAoZGF0YS5yYW5kU2VlZCkge1xuICAgICAgICAgICAgdGhpcy5iYXR0bGVDb250cm9sbGVyLmdldFJhbmRvbSgpLnNldFNlZWQoZGF0YS5yYW5kU2VlZClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOajgOa1i+aYr+WQpuWQjOatpVxuICAgIHByaXZhdGUgY2hlY2tTdGF0ZVN5bmMoKSB7XG4gICAgICAgIGlmICh0aGlzLmlzTG9jYWwpIHtcbiAgICAgICAgICAgIHJldHVybiAvL+acrOWcsOaImOaWl+WwseS4jeeUqOajgOa1i+WQjOatpeS6hlxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudEZyYW1lSW5kZXggJSB0aGlzLmNoZWNrRnJhbWVDb3VudCAhPT0gMCB8fCAhdGhpcy5iYXR0bGVDb250cm9sbGVyKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzZXJ2ZXJNZDUgPSB0aGlzLnNlcnZlclNuYXBzaG9vdE1ENU1hcFt0aGlzLmN1cnJlbnRGcmFtZUluZGV4XVxuICAgICAgICBjb25zdCBsb2FjbE1ENSA9IHRoaXMuYmF0dGxlQ29udHJvbGxlci5nZXRTbmFwc2hvb3RNRDUoKVxuICAgICAgICBkZWxldGUgdGhpcy5zZXJ2ZXJTbmFwc2hvb3RNRDVNYXBbdGhpcy5jdXJyZW50RnJhbWVJbmRleF1cbiAgICAgICAgaWYgKCFzZXJ2ZXJNZDUpIHtcbiAgICAgICAgICAgIHRoaXMuc2VydmVyU25hcHNob290TUQ1TWFwW3RoaXMuY3VycmVudEZyYW1lSW5kZXhdID0gbG9hY2xNRDVcbiAgICAgICAgfSBlbHNlIGlmIChzZXJ2ZXJNZDUgIT09IGxvYWNsTUQ1KSB7XG4gICAgICAgICAgICBjYy5sb2coJ+S4jeWQjOatpeS6hiBjaGVja1N0YXRlU3luYycpXG4gICAgICAgICAgICBjYy5sb2coJ2xvY2FsJywgbG9hY2xNRDUpXG4gICAgICAgICAgICBjYy5sb2coJ3NlcnZlcicsIHNlcnZlck1kNSlcbiAgICAgICAgICAgIHRoaXMucmVzeW5jKClcbiAgICAgICAgICAgIHRoaXMubm9TeW5jUmVwb3J0KGxvYWNsTUQ1LCBzZXJ2ZXJNZDUsIHRoaXMuY3VycmVudEZyYW1lSW5kZXgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDojrflj5bluKfpgJ/luqZcbiAgICBwcml2YXRlIGdldEZyYW1lU3BlZWQoZnJhbWVOdW06IG51bWJlcik6IG51bWJlciB7XG4gICAgICAgIGxldCBzcGVlZCA9IDFcbiAgICAgICAgaWYgKGZyYW1lTnVtID4gMTAwKSB7XG4gICAgICAgICAgICBzcGVlZCA9IDhcbiAgICAgICAgfSBlbHNlIGlmIChmcmFtZU51bSA+IDUwKSB7XG4gICAgICAgICAgICBzcGVlZCA9IDRcbiAgICAgICAgfSBlbHNlIGlmIChmcmFtZU51bSA+IDQpIHtcbiAgICAgICAgICAgIHNwZWVkID0gMlxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBzcGVlZFxuICAgIH1cblxuICAgIHByaXZhdGUgbm9TeW5jUmVwb3J0KGxvYWNsTUQ1OiBzdHJpbmcsIHNlcnZlck1kNTogc3RyaW5nLCBjdXJyZW50RnJhbWVJbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmICghbG9jYWxDb25maWcuUkVMRUFTRSkge1xuICAgICAgICB9IGVsc2UgaWYgKGVycm9yUmVwb3J0SGVscGVyLmNoZWNrQ291bnRMaW1pdCgnbm9fc3luYycsIDEpKSB7XG4gICAgICAgICAgICBlcnJvclJlcG9ydEhlbHBlci5yZXBvcnRFcnJvcignZnNwIG5vIHN5bmMgMicsIHtcbiAgICAgICAgICAgICAgICBpbmRleDogdGhpcy5hcmVhPy5pbmRleCxcbiAgICAgICAgICAgICAgICBjYW1wczogdGhpcy5iYXR0bGVDb250cm9sbGVyPy5nZXRDYW1wcygpLFxuICAgICAgICAgICAgICAgIGN1cnJlbnRGcmFtZUluZGV4OiBjdXJyZW50RnJhbWVJbmRleCxcbiAgICAgICAgICAgICAgICBsb2NhbDogbG9hY2xNRDUsXG4gICAgICAgICAgICAgICAgc2VydmVyOiBzZXJ2ZXJNZDUsXG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgfVxufSJdfQ==