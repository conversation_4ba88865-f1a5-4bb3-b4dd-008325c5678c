
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/PersonalGameDetailPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cef8dXkunlNLY7dXCMbks/n', 'PersonalGameDetailPnlCtrl');
// app/script/view/menu/PersonalGameDetailPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var PersonalGameDetailPnlCtrl = /** @class */ (function (_super) {
    __extends(PersonalGameDetailPnlCtrl, _super);
    function PersonalGameDetailPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/layout/title_l
        _this.contentNode_ = null; // path://root/content_n
        _this.heroNode_ = null; // path://root/content_n/info/view/content/hero_n
        _this.pawnNode_ = null; // path://root/content_n/info/view/content/pawn_n
        //@end
        _this.user = null;
        return _this;
    }
    PersonalGameDetailPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PersonalGameDetailPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    PersonalGameDetailPnlCtrl.prototype.onEnter = function (data) {
        var _a = GameHelper_1.gameHpr.getServerNameById(data.sid), key = _a.key, id = _a.id;
        this.titleLbl_.setLocaleKey(key, id);
        this.updateInfo(data);
    };
    PersonalGameDetailPnlCtrl.prototype.onRemove = function () {
    };
    PersonalGameDetailPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/info/share_be
    PersonalGameDetailPnlCtrl.prototype.onClickShare = function (event, data) {
        cc.log('onClickShare', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    PersonalGameDetailPnlCtrl.prototype.updateInfo = function (data) {
        var _a;
        var _this = this;
        var info = this.contentNode_.Child('info');
        info.Child('name/val', cc.Label).string = this.user.getNickname();
        ResHelper_1.resHelper.loadPlayerHead(info.Child('head'), this.user.getHeadIcon(), this.key);
        var layout = info.Child('layout');
        layout.Child('score/val', cc.Label).string = data.score;
        layout.Child('ranking/val', cc.Label).string = data.rank >= 0 ? (data.rank + 1) : '-';
        layout.Child('mvp').active = data.rank === 0;
        // 总计
        var statics = data.statistics, total = this.contentNode_.Child('total/content');
        total.Child('0', cc.Label).string = statics[Enums_1.BattleStatistics.MAX_LAND_COUNT] || 0;
        total.Child('1', cc.Label).string = statics[Enums_1.BattleStatistics.SUM_KILL] || 0;
        total.Child('2', cc.Label).string = statics[Enums_1.BattleStatistics.PAWN_DEAD] || 0;
        total.Child('3', cc.Label).string = statics[Enums_1.BattleStatistics.KILL_MAIN] || 0;
        // 英雄 统计
        var pawnData = data.pawnStatistics;
        var herosNode_ = this.heroNode_.Child('content');
        herosNode_.Items(data.useHero, function (it, id, i) {
            var hero = pawnData[id] || { data: {} };
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, it.Child('head/val'), _this.key);
            var averge = Math.floor((hero.data[Enums_1.BattleStatistics.SUM_DAMAGE] || 0) / (hero.data[Enums_1.BattleStatistics.FIGHT_COUNT] || 1));
            it.Child('1', cc.Label).string = averge + '';
            it.Child('2', cc.Label).string = (hero.data[Enums_1.BattleStatistics.SUM_DAMAGE] || 0) + '';
            it.Child('3', cc.Label).string = (hero.data[Enums_1.BattleStatistics.HIT_DAMAGE_TAKEN] || 0) + '';
            it.Child('line').active = i < data.useHero.length - 1;
        });
        // 士兵统计
        var pawnMap = {};
        for (var k in statics) {
            var key = Number(k), type = key, pawnId = 0;
            if (key >= Enums_1.BattleStatistics.ACC_RECRUIT_PAWN_COUNT) {
                type = Math.floor(key / 10000);
                pawnId = key % 10000;
            }
            if (type === Enums_1.BattleStatistics.ACC_RECRUIT_PAWN_COUNT || type === Enums_1.BattleStatistics.MAXLV_PAWN_COUNT) {
                var pawn = pawnMap[pawnId];
                if (pawn) {
                    pawn[type] = statics[k];
                }
                else {
                    pawnMap[pawnId] = (_a = {}, _a[type] = statics[k], _a);
                }
            }
        }
        var pawnSv_ = this.pawnNode_.Child('list', cc.ScrollView);
        var keys = Object.keys(pawnMap), len = keys.length;
        pawnSv_.List(len, function (it, i) {
            var id = Number(keys[i]), countMap = pawnMap[id];
            var pawn = pawnData[id] || { data: {} };
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, it.Child('pawn/head/val'), _this.key);
            it.Child('pawn/max/count', cc.Label).string = (countMap[Enums_1.BattleStatistics.MAXLV_PAWN_COUNT] || 0) + '';
            it.Child('1', cc.Label).string = (countMap[Enums_1.BattleStatistics.ACC_RECRUIT_PAWN_COUNT] || 0) + '';
            it.Child('2', cc.Label).string = pawn.data[Enums_1.BattleStatistics.SUM_DAMAGE] || 0;
            it.Child('3', cc.Label).string = pawn.data[Enums_1.BattleStatistics.HIT_DAMAGE_TAKEN] || 0;
            it.Child('line').active = i < len - 1;
        });
    };
    PersonalGameDetailPnlCtrl = __decorate([
        ccclass
    ], PersonalGameDetailPnlCtrl);
    return PersonalGameDetailPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PersonalGameDetailPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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