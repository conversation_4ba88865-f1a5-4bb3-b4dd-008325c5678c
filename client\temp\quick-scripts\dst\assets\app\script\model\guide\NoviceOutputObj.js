
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceOutputObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e47bdSnR5xPwIR3F2SAYM2e', 'NoviceOutputObj');
// app/script/model/guide/NoviceOutputObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var NoviceOutputObj = /** @class */ (function () {
    function NoviceOutputObj() {
        this.value = 0; //当前值
        this.opHour = 0; //小时产
        this.opSec = 0; //秒产
        this.opCount = 0; //产出数量
    }
    NoviceOutputObj.prototype.initOutput = function (val) {
        this.opHour = val;
        this.opSec = this.opHour / ut.Time.Hour * 1000; //每秒产
        if (this.opSec <= 0) {
            return 0;
        }
        return Math.max(Math.floor(1.0 / this.opSec), 10) * 1000; //最小10秒
    };
    NoviceOutputObj.prototype.do = function (dt, cap) {
        if (this.value >= cap) {
            return false; //已经满了
        }
        else if (this.opSec < 0 && this.value <= 0) {
            return false; //如果是扣 并且没有了
        }
        this.opCount += (dt / 1000.0) * this.opSec;
        if (Math.abs(this.opCount) < 1) {
            return false;
        }
        var cnt = Math.floor(this.opCount);
        var val = this.value + cnt;
        if (val >= cap) {
            cnt = cap - this.value;
            this.opCount = 0;
        }
        else if (val < 0) {
            cnt = -this.value;
            this.opCount = 0;
        }
        else {
            this.opCount -= cnt;
        }
        this.change(cnt, cap);
        return true;
    };
    NoviceOutputObj.prototype.change = function (val, cap) {
        var count = this.value + val;
        if (count < 0) {
            val = -this.value;
        }
        else if (count > cap) {
            val = cap - this.value;
        }
        this.value += val;
        return val;
    };
    NoviceOutputObj.prototype.strip = function () {
        return { value: this.value, opHour: this.opHour };
    };
    return NoviceOutputObj;
}());
exports.default = NoviceOutputObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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