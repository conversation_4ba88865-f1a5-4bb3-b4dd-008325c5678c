
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SelectCitySkinPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '23b727uRoJOqYkaEcbUHDl1', 'SelectCitySkinPnlCtrl');
// app/script/view/main/SelectCitySkinPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectCitySkinPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectCitySkinPnlCtrl, _super);
    function SelectCitySkinPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.ADAPT_SIZE = cc.size(88, 88);
        _this.cell = null;
        _this.cb = null;
        _this.perSkinId = 0;
        _this.selectSkinId = 0;
        _this.selectNode = null;
        return _this;
    }
    SelectCitySkinPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectCitySkinPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectCitySkinPnlCtrl.prototype.onEnter = function (cell, cb) {
        var _this = this;
        this.cell = cell;
        this.cb = cb;
        this.titleLbl_.setLocaleKey('ui.title_select_city_skin', 'cityText.name_' + cell.cityId);
        this.selectSkinId = this.perSkinId = GameHelper_1.gameHpr.world.getCitySkinByIndex(cell.index) || cell.cityId;
        var list = GameHelper_1.gameHpr.user.getCitySkinList(cell.cityId);
        list.unshift(cell.cityId);
        this.listSv_.List(list.length, function (it, i) {
            var skinId = it.Data = list[i], icon = it.Child('val');
            ResHelper_1.resHelper.loadCityIcon(skinId, icon, _this.key).then(function () { return _this.isValid && icon.adaptScale(_this.ADAPT_SIZE); });
            if (it.Child('select').active = skinId === _this.selectSkinId) {
                _this.selectNode = it;
            }
        });
    };
    SelectCitySkinPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(false);
        this.cb = null;
        this.cell = null;
    };
    SelectCitySkinPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    SelectCitySkinPnlCtrl.prototype.onClickItem = function (event, data) {
        var _a, _b;
        audioMgr.playSFX('click');
        var it = event.target;
        if (it.uuid !== ((_a = this.selectNode) === null || _a === void 0 ? void 0 : _a.uuid)) {
            it.Child('select').active = true;
            (_b = this.selectNode) === null || _b === void 0 ? void 0 : _b.Child('select').setActive(false);
            this.selectNode = it;
            this.selectSkinId = it.Data;
        }
    };
    // path://root/ok_be
    SelectCitySkinPnlCtrl.prototype.onClickOk = function (event, data) {
        this.do();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectCitySkinPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var index, skinId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.selectSkinId === 0 || this.selectSkinId === this.perSkinId) {
                            return [2 /*return*/, this.hide()];
                        }
                        index = this.cell.index, skinId = this.selectSkinId === this.cell.cityId ? 0 : this.selectSkinId;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_ChangeCitySkin', { index: index, skinId: skinId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        GameHelper_1.gameHpr.world.updateCitySkin(index, skinId);
                        if (this.isValid) {
                            this.cb && this.cb(true);
                            this.cb = null;
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SelectCitySkinPnlCtrl = __decorate([
        ccclass
    ], SelectCitySkinPnlCtrl);
    return SelectCitySkinPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectCitySkinPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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