
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/NodePoolMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '50ee1qIagRD67YLRsbjyWiD', 'NodePoolMgr');
// app/core/manage/NodePoolMgr.ts

var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
// 对象池管理
var NodePoolMgr = /** @class */ (function () {
    function NodePoolMgr() {
        this.LOAD_KEY = '__node_pool_load_key';
        this.debug = false;
        this.nodePoolMap = new Map();
        this.useItems = []; //当前正在使用的node
    }
    NodePoolMgr.prototype.get = function (url, key) {
        return __awaiter(this, void 0, void 0, function () {
            var pool, prefab, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        key = key !== null && key !== void 0 ? key : '__none';
                        pool = this.nodePoolMap.get(url);
                        if (!(!pool || !pool.prefab || !pool.prefab.isValid)) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.LOAD_KEY)];
                    case 1:
                        prefab = _a.sent();
                        if (!prefab) {
                            logger.error('nodePool get prefab is null, url=' + url);
                            return [2 /*return*/, null];
                        }
                        pool = this.nodePoolMap.get(url); //防止异步回来的时候 已经有了
                        if (!pool) {
                            pool = { prefab: prefab, items: [], itemsUuidMap: {}, keys: [] };
                            this.nodePoolMap.set(url, pool);
                        }
                        _a.label = 2;
                    case 2:
                        if (!pool.keys.has(key)) {
                            pool.keys.push(key);
                        }
                        node = pool.items.pop();
                        delete pool.itemsUuidMap[node === null || node === void 0 ? void 0 : node.uuid];
                        if (!node || !node.isValid) {
                            node = cc.instantiate(pool.prefab);
                        }
                        if (node) {
                            node['__@node_pool_url'] = url;
                            node['__@node_pool_key'] = key;
                            this.useItems.push({ uuid: node.uuid, key: key, node: node });
                        }
                        if (this.debug) {
                            logger.info('nodePool get ' + url + ', <' + key + '> ' + !!node);
                        }
                        return [2 /*return*/, node];
                }
            });
        });
    };
    NodePoolMgr.prototype.put = function (node) {
        if (!node || !node.isValid) {
            return;
        }
        var url = node['__@node_pool_url'];
        if (!url) {
            node.destroy();
            return logger.error('nodePool put error! url == null, node=' + node.name);
        }
        var pool = this.nodePoolMap.get(url);
        if (!pool || pool.items.length >= 100) {
            node.destroy();
            return logger.error('nodePool put error! not pool, node=' + node.name);
        }
        else if (!pool.itemsUuidMap[node.uuid]) {
            node.stopAllActions();
            node.parent = null;
            node.Data = null;
            pool.items.push(node);
            pool.itemsUuidMap[node.uuid] = true;
            this.useItems.remove('uuid', node.uuid);
            if (this.debug) {
                logger.info('nodePool put ' + url + ', <' + node['__@node_pool_key'] + '> ');
            }
        }
        else {
            logger.info('nodePool put error! repeat push item, node=' + node.name);
        }
    };
    NodePoolMgr.prototype.releaseOne = function (url, pool) {
        var _a;
        pool.items.forEach(function (m) { return m.destroy(); });
        pool.items.length = 0;
        (_a = pool.prefab) === null || _a === void 0 ? void 0 : _a.destroy();
        assetsMgr.releaseTempRes(url, this.LOAD_KEY);
        this.nodePoolMap.delete(url);
        if (this.debug) {
            logger.info('nodePool release ' + url);
        }
    };
    // 释放所有
    NodePoolMgr.prototype.releaseAll = function () {
        var _this = this;
        this.nodePoolMap.forEach(function (m, url) { return _this.releaseOne(url, m); });
    };
    // 释放
    NodePoolMgr.prototype.releaseByTag = function (key) {
        var _this = this;
        this.nodePoolMap.forEach(function (m, url) {
            if (m.keys.remove(key) !== undefined) {
                // this.removeItemsOne(m, key)
                if (m.keys.length === 0) {
                    _this.releaseOne(url, m);
                }
            }
        });
    };
    // 释放
    NodePoolMgr.prototype.releaseByUrl = function (url) {
        var pool = this.nodePoolMap.get(url);
        if (pool) {
            this.releaseOne(url, pool);
        }
    };
    // 清理items
    NodePoolMgr.prototype.removeItemsByTag = function (key) {
        var _this = this;
        this.nodePoolMap.forEach(function (m) { return _this.removeItemsOne(m, key); });
    };
    NodePoolMgr.prototype.removeItemsOne = function (pool, key) {
        if (!pool.keys.remove(key)) {
            return;
        }
        for (var i = pool.items.length - 1; i >= 0; i--) {
            var node = pool.items[i];
            if (node['__@node_pool_key'] === key) {
                pool.items.splice(i, 1);
                delete pool.itemsUuidMap[node === null || node === void 0 ? void 0 : node.uuid];
                node.destroy();
            }
        }
    };
    // 清理使用中的
    NodePoolMgr.prototype.cleanUseItemsByTag = function (key) {
        var _a;
        for (var i = this.useItems.length - 1; i >= 0; i--) {
            var data = this.useItems[i], isValid = (_a = data.node) === null || _a === void 0 ? void 0 : _a.isValid;
            if (data.key !== key && isValid) {
                continue;
            }
            else if (isValid) {
                data.node.destroy();
            }
            this.useItems.splice(i, 1);
        }
    };
    NodePoolMgr.prototype.cleanUseAndRemoveItemsByTag = function (key) {
        this.cleanUseItemsByTag(key);
        this.removeItemsByTag(key);
    };
    return NodePoolMgr;
}());
window['nodePoolMgr'] = new NodePoolMgr();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxtYW5hZ2VcXE5vZGVQb29sTWdyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFBLFFBQVE7QUFDUjtJQUFBO1FBRXFCLGFBQVEsR0FBRyxzQkFBc0IsQ0FBQTtRQUUxQyxVQUFLLEdBQVksS0FBSyxDQUFBO1FBQ3RCLGdCQUFXLEdBQThCLElBQUksR0FBRyxFQUFFLENBQUE7UUFDbEQsYUFBUSxHQUFtRCxFQUFFLENBQUEsQ0FBQyxhQUFhO0lBd0l2RixDQUFDO0lBdElnQix5QkFBRyxHQUFoQixVQUFpQixHQUFXLEVBQUUsR0FBWTs7Ozs7O3dCQUN0QyxHQUFHLEdBQUcsR0FBRyxhQUFILEdBQUcsY0FBSCxHQUFHLEdBQUksUUFBUSxDQUFBO3dCQUNqQixJQUFJLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7NkJBQ2hDLENBQUEsQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUEsRUFBN0Msd0JBQTZDO3dCQUM5QixxQkFBTSxTQUFTLENBQUMsV0FBVyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsRUFBQTs7d0JBQW5FLE1BQU0sR0FBRyxTQUEwRDt3QkFDekUsSUFBSSxDQUFDLE1BQU0sRUFBRTs0QkFDVCxNQUFNLENBQUMsS0FBSyxDQUFDLG1DQUFtQyxHQUFHLEdBQUcsQ0FBQyxDQUFBOzRCQUN2RCxzQkFBTyxJQUFJLEVBQUE7eUJBQ2Q7d0JBQ0QsSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBLENBQUMsZ0JBQWdCO3dCQUNqRCxJQUFJLENBQUMsSUFBSSxFQUFFOzRCQUNQLElBQUksR0FBRyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLEVBQUUsRUFBRSxZQUFZLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsQ0FBQTs0QkFDaEUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxDQUFBO3lCQUNsQzs7O3dCQUVMLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBRTs0QkFDckIsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7eUJBQ3RCO3dCQUNHLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxDQUFBO3dCQUMzQixPQUFPLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQyxDQUFBO3dCQUNwQyxJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRTs0QkFDeEIsSUFBSSxHQUFHLEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO3lCQUNyQzt3QkFDRCxJQUFJLElBQUksRUFBRTs0QkFDTixJQUFJLENBQUMsa0JBQWtCLENBQUMsR0FBRyxHQUFHLENBQUE7NEJBQzlCLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLEdBQUcsQ0FBQTs0QkFDOUIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFBO3lCQUNoRTt3QkFDRCxJQUFJLElBQUksQ0FBQyxLQUFLLEVBQUU7NEJBQ1osTUFBTSxDQUFDLElBQUksQ0FBQyxlQUFlLEdBQUcsR0FBRyxHQUFHLEtBQUssR0FBRyxHQUFHLEdBQUcsSUFBSSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQTt5QkFDbkU7d0JBQ0Qsc0JBQU8sSUFBSSxFQUFBOzs7O0tBQ2Q7SUFFTSx5QkFBRyxHQUFWLFVBQVcsSUFBYTtRQUNwQixJQUFJLENBQUMsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUN4QixPQUFNO1NBQ1Q7UUFDRCxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtRQUNwQyxJQUFJLENBQUMsR0FBRyxFQUFFO1lBQ04sSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO1lBQ2QsT0FBTyxNQUFNLENBQUMsS0FBSyxDQUFDLHdDQUF3QyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUM1RTtRQUNELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ3RDLElBQUksQ0FBQyxJQUFJLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLElBQUksR0FBRyxFQUFFO1lBQ25DLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtZQUNkLE9BQU8sTUFBTSxDQUFDLEtBQUssQ0FBQyxxQ0FBcUMsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDekU7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDdEMsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1lBQ3JCLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBO1lBQ2xCLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1lBQ2hCLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQ3JCLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQTtZQUNuQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQ3ZDLElBQUksSUFBSSxDQUFDLEtBQUssRUFBRTtnQkFDWixNQUFNLENBQUMsSUFBSSxDQUFDLGVBQWUsR0FBRyxHQUFHLEdBQUcsS0FBSyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFBO2FBQy9FO1NBQ0o7YUFBTTtZQUNILE1BQU0sQ0FBQyxJQUFJLENBQUMsNkNBQTZDLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1NBQ3pFO0lBQ0wsQ0FBQztJQUVPLGdDQUFVLEdBQWxCLFVBQW1CLEdBQVcsRUFBRSxJQUFrQjs7UUFDOUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsT0FBTyxFQUFFLEVBQVgsQ0FBVyxDQUFDLENBQUE7UUFDcEMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO1FBQ3JCLE1BQUEsSUFBSSxDQUFDLE1BQU0sMENBQUUsT0FBTyxHQUFFO1FBQ3RCLFNBQVMsQ0FBQyxjQUFjLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUM1QyxJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQTtRQUM1QixJQUFJLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDWixNQUFNLENBQUMsSUFBSSxDQUFDLG1CQUFtQixHQUFHLEdBQUcsQ0FBQyxDQUFBO1NBQ3pDO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQSxnQ0FBVSxHQUFqQjtRQUFBLGlCQUVDO1FBREcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsVUFBQyxDQUFDLEVBQUUsR0FBRyxJQUFLLE9BQUEsS0FBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDLEVBQXZCLENBQXVCLENBQUMsQ0FBQTtJQUNqRSxDQUFDO0lBRUQsS0FBSztJQUNFLGtDQUFZLEdBQW5CLFVBQW9CLEdBQVc7UUFBL0IsaUJBU0M7UUFSRyxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sQ0FBQyxVQUFDLENBQUMsRUFBRSxHQUFHO1lBQzVCLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEtBQUssU0FBUyxFQUFFO2dCQUNsQyw4QkFBOEI7Z0JBQzlCLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO29CQUNyQixLQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQTtpQkFDMUI7YUFDSjtRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVELEtBQUs7SUFDRSxrQ0FBWSxHQUFuQixVQUFvQixHQUFXO1FBQzNCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ3RDLElBQUksSUFBSSxFQUFFO1lBQ04sSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDN0I7SUFDTCxDQUFDO0lBRUQsVUFBVTtJQUNILHNDQUFnQixHQUF2QixVQUF3QixHQUFXO1FBQW5DLGlCQUVDO1FBREcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxLQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBM0IsQ0FBMkIsQ0FBQyxDQUFBO0lBQzlELENBQUM7SUFFTyxvQ0FBYyxHQUF0QixVQUF1QixJQUFrQixFQUFFLEdBQVc7UUFDbEQsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ3hCLE9BQU07U0FDVDtRQUNELEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDN0MsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMxQixJQUFJLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLEdBQUcsRUFBRTtnQkFDbEMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO2dCQUN2QixPQUFPLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQyxDQUFBO2dCQUNwQyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7YUFDakI7U0FDSjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0Ysd0NBQWtCLEdBQXpCLFVBQTBCLEdBQVc7O1FBQ2pDLEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDaEQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsRUFBRSxPQUFPLFNBQUcsSUFBSSxDQUFDLElBQUksMENBQUUsT0FBTyxDQUFBO1lBQzNELElBQUksSUFBSSxDQUFDLEdBQUcsS0FBSyxHQUFHLElBQUksT0FBTyxFQUFFO2dCQUM3QixTQUFRO2FBQ1g7aUJBQU0sSUFBSSxPQUFPLEVBQUU7Z0JBQ2hCLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7YUFDdEI7WUFDRCxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7U0FDN0I7SUFDTCxDQUFDO0lBRU0saURBQTJCLEdBQWxDLFVBQW1DLEdBQVc7UUFDMUMsSUFBSSxDQUFDLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQzVCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLENBQUMsQ0FBQTtJQUM5QixDQUFDO0lBQ0wsa0JBQUM7QUFBRCxDQTlJQSxBQThJQyxJQUFBO0FBRUQsTUFBTSxDQUFDLGFBQWEsQ0FBQyxHQUFHLElBQUksV0FBVyxFQUFFLENBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJcclxudHlwZSBOb2RlUG9vbEl0ZW0gPSB7XHJcbiAgICBwcmVmYWI6IGNjLlByZWZhYjtcclxuICAgIGl0ZW1zOiBjYy5Ob2RlW107XHJcbiAgICBpdGVtc1V1aWRNYXA6IGFueTtcclxuICAgIGtleXM6IHN0cmluZ1tdO1xyXG59XHJcblxyXG4vLyDlr7nosaHmsaDnrqHnkIZcclxuY2xhc3MgTm9kZVBvb2xNZ3Ige1xyXG5cclxuICAgIHByaXZhdGUgcmVhZG9ubHkgTE9BRF9LRVkgPSAnX19ub2RlX3Bvb2xfbG9hZF9rZXknXHJcblxyXG4gICAgcHJpdmF0ZSBkZWJ1ZzogYm9vbGVhbiA9IGZhbHNlXHJcbiAgICBwcml2YXRlIG5vZGVQb29sTWFwOiBNYXA8c3RyaW5nLCBOb2RlUG9vbEl0ZW0+ID0gbmV3IE1hcCgpXHJcbiAgICBwcml2YXRlIHVzZUl0ZW1zOiB7IHV1aWQ6IHN0cmluZywga2V5OiBzdHJpbmcsIG5vZGU6IGNjLk5vZGUgfVtdID0gW10gLy/lvZPliY3mraPlnKjkvb/nlKjnmoRub2RlXHJcblxyXG4gICAgcHVibGljIGFzeW5jIGdldCh1cmw6IHN0cmluZywga2V5Pzogc3RyaW5nKSB7XHJcbiAgICAgICAga2V5ID0ga2V5ID8/ICdfX25vbmUnXHJcbiAgICAgICAgbGV0IHBvb2wgPSB0aGlzLm5vZGVQb29sTWFwLmdldCh1cmwpXHJcbiAgICAgICAgaWYgKCFwb29sIHx8ICFwb29sLnByZWZhYiB8fCAhcG9vbC5wcmVmYWIuaXNWYWxpZCkge1xyXG4gICAgICAgICAgICBjb25zdCBwcmVmYWIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXModXJsLCBjYy5QcmVmYWIsIHRoaXMuTE9BRF9LRVkpXHJcbiAgICAgICAgICAgIGlmICghcHJlZmFiKSB7XHJcbiAgICAgICAgICAgICAgICBsb2dnZXIuZXJyb3IoJ25vZGVQb29sIGdldCBwcmVmYWIgaXMgbnVsbCwgdXJsPScgKyB1cmwpXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHBvb2wgPSB0aGlzLm5vZGVQb29sTWFwLmdldCh1cmwpIC8v6Ziy5q2i5byC5q2l5Zue5p2l55qE5pe25YCZIOW3sue7j+acieS6hlxyXG4gICAgICAgICAgICBpZiAoIXBvb2wpIHtcclxuICAgICAgICAgICAgICAgIHBvb2wgPSB7IHByZWZhYjogcHJlZmFiLCBpdGVtczogW10sIGl0ZW1zVXVpZE1hcDoge30sIGtleXM6IFtdIH1cclxuICAgICAgICAgICAgICAgIHRoaXMubm9kZVBvb2xNYXAuc2V0KHVybCwgcG9vbClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoIXBvb2wua2V5cy5oYXMoa2V5KSkge1xyXG4gICAgICAgICAgICBwb29sLmtleXMucHVzaChrZXkpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBub2RlID0gcG9vbC5pdGVtcy5wb3AoKVxyXG4gICAgICAgIGRlbGV0ZSBwb29sLml0ZW1zVXVpZE1hcFtub2RlPy51dWlkXVxyXG4gICAgICAgIGlmICghbm9kZSB8fCAhbm9kZS5pc1ZhbGlkKSB7XHJcbiAgICAgICAgICAgIG5vZGUgPSBjYy5pbnN0YW50aWF0ZShwb29sLnByZWZhYilcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKG5vZGUpIHtcclxuICAgICAgICAgICAgbm9kZVsnX19Abm9kZV9wb29sX3VybCddID0gdXJsXHJcbiAgICAgICAgICAgIG5vZGVbJ19fQG5vZGVfcG9vbF9rZXknXSA9IGtleVxyXG4gICAgICAgICAgICB0aGlzLnVzZUl0ZW1zLnB1c2goeyB1dWlkOiBub2RlLnV1aWQsIGtleToga2V5LCBub2RlOiBub2RlIH0pXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh0aGlzLmRlYnVnKSB7XHJcbiAgICAgICAgICAgIGxvZ2dlci5pbmZvKCdub2RlUG9vbCBnZXQgJyArIHVybCArICcsIDwnICsga2V5ICsgJz4gJyArICEhbm9kZSlcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG5vZGVcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgcHV0KG5vZGU6IGNjLk5vZGUpIHtcclxuICAgICAgICBpZiAoIW5vZGUgfHwgIW5vZGUuaXNWYWxpZCkge1xyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgdXJsID0gbm9kZVsnX19Abm9kZV9wb29sX3VybCddXHJcbiAgICAgICAgaWYgKCF1cmwpIHtcclxuICAgICAgICAgICAgbm9kZS5kZXN0cm95KClcclxuICAgICAgICAgICAgcmV0dXJuIGxvZ2dlci5lcnJvcignbm9kZVBvb2wgcHV0IGVycm9yISB1cmwgPT0gbnVsbCwgbm9kZT0nICsgbm9kZS5uYW1lKVxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBwb29sID0gdGhpcy5ub2RlUG9vbE1hcC5nZXQodXJsKVxyXG4gICAgICAgIGlmICghcG9vbCB8fCBwb29sLml0ZW1zLmxlbmd0aCA+PSAxMDApIHtcclxuICAgICAgICAgICAgbm9kZS5kZXN0cm95KClcclxuICAgICAgICAgICAgcmV0dXJuIGxvZ2dlci5lcnJvcignbm9kZVBvb2wgcHV0IGVycm9yISBub3QgcG9vbCwgbm9kZT0nICsgbm9kZS5uYW1lKVxyXG4gICAgICAgIH0gZWxzZSBpZiAoIXBvb2wuaXRlbXNVdWlkTWFwW25vZGUudXVpZF0pIHtcclxuICAgICAgICAgICAgbm9kZS5zdG9wQWxsQWN0aW9ucygpXHJcbiAgICAgICAgICAgIG5vZGUucGFyZW50ID0gbnVsbFxyXG4gICAgICAgICAgICBub2RlLkRhdGEgPSBudWxsXHJcbiAgICAgICAgICAgIHBvb2wuaXRlbXMucHVzaChub2RlKVxyXG4gICAgICAgICAgICBwb29sLml0ZW1zVXVpZE1hcFtub2RlLnV1aWRdID0gdHJ1ZVxyXG4gICAgICAgICAgICB0aGlzLnVzZUl0ZW1zLnJlbW92ZSgndXVpZCcsIG5vZGUudXVpZClcclxuICAgICAgICAgICAgaWYgKHRoaXMuZGVidWcpIHtcclxuICAgICAgICAgICAgICAgIGxvZ2dlci5pbmZvKCdub2RlUG9vbCBwdXQgJyArIHVybCArICcsIDwnICsgbm9kZVsnX19Abm9kZV9wb29sX2tleSddICsgJz4gJylcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGxvZ2dlci5pbmZvKCdub2RlUG9vbCBwdXQgZXJyb3IhIHJlcGVhdCBwdXNoIGl0ZW0sIG5vZGU9JyArIG5vZGUubmFtZSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSByZWxlYXNlT25lKHVybDogc3RyaW5nLCBwb29sOiBOb2RlUG9vbEl0ZW0pIHtcclxuICAgICAgICBwb29sLml0ZW1zLmZvckVhY2gobSA9PiBtLmRlc3Ryb3koKSlcclxuICAgICAgICBwb29sLml0ZW1zLmxlbmd0aCA9IDBcclxuICAgICAgICBwb29sLnByZWZhYj8uZGVzdHJveSgpXHJcbiAgICAgICAgYXNzZXRzTWdyLnJlbGVhc2VUZW1wUmVzKHVybCwgdGhpcy5MT0FEX0tFWSlcclxuICAgICAgICB0aGlzLm5vZGVQb29sTWFwLmRlbGV0ZSh1cmwpXHJcbiAgICAgICAgaWYgKHRoaXMuZGVidWcpIHtcclxuICAgICAgICAgICAgbG9nZ2VyLmluZm8oJ25vZGVQb29sIHJlbGVhc2UgJyArIHVybClcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8g6YeK5pS+5omA5pyJXHJcbiAgICBwdWJsaWMgcmVsZWFzZUFsbCgpIHtcclxuICAgICAgICB0aGlzLm5vZGVQb29sTWFwLmZvckVhY2goKG0sIHVybCkgPT4gdGhpcy5yZWxlYXNlT25lKHVybCwgbSkpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g6YeK5pS+XHJcbiAgICBwdWJsaWMgcmVsZWFzZUJ5VGFnKGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5ub2RlUG9vbE1hcC5mb3JFYWNoKChtLCB1cmwpID0+IHtcclxuICAgICAgICAgICAgaWYgKG0ua2V5cy5yZW1vdmUoa2V5KSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICAgICAgICAvLyB0aGlzLnJlbW92ZUl0ZW1zT25lKG0sIGtleSlcclxuICAgICAgICAgICAgICAgIGlmIChtLmtleXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5yZWxlYXNlT25lKHVybCwgbSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8g6YeK5pS+XHJcbiAgICBwdWJsaWMgcmVsZWFzZUJ5VXJsKHVybDogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QgcG9vbCA9IHRoaXMubm9kZVBvb2xNYXAuZ2V0KHVybClcclxuICAgICAgICBpZiAocG9vbCkge1xyXG4gICAgICAgICAgICB0aGlzLnJlbGVhc2VPbmUodXJsLCBwb29sKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyDmuIXnkIZpdGVtc1xyXG4gICAgcHVibGljIHJlbW92ZUl0ZW1zQnlUYWcoa2V5OiBzdHJpbmcpIHtcclxuICAgICAgICB0aGlzLm5vZGVQb29sTWFwLmZvckVhY2gobSA9PiB0aGlzLnJlbW92ZUl0ZW1zT25lKG0sIGtleSkpXHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSByZW1vdmVJdGVtc09uZShwb29sOiBOb2RlUG9vbEl0ZW0sIGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgaWYgKCFwb29sLmtleXMucmVtb3ZlKGtleSkpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGZvciAobGV0IGkgPSBwb29sLml0ZW1zLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG5vZGUgPSBwb29sLml0ZW1zW2ldXHJcbiAgICAgICAgICAgIGlmIChub2RlWydfX0Bub2RlX3Bvb2xfa2V5J10gPT09IGtleSkge1xyXG4gICAgICAgICAgICAgICAgcG9vbC5pdGVtcy5zcGxpY2UoaSwgMSlcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBwb29sLml0ZW1zVXVpZE1hcFtub2RlPy51dWlkXVxyXG4gICAgICAgICAgICAgICAgbm9kZS5kZXN0cm95KClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyDmuIXnkIbkvb/nlKjkuK3nmoRcclxuICAgIHB1YmxpYyBjbGVhblVzZUl0ZW1zQnlUYWcoa2V5OiBzdHJpbmcpIHtcclxuICAgICAgICBmb3IgKGxldCBpID0gdGhpcy51c2VJdGVtcy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy51c2VJdGVtc1tpXSwgaXNWYWxpZCA9IGRhdGEubm9kZT8uaXNWYWxpZFxyXG4gICAgICAgICAgICBpZiAoZGF0YS5rZXkgIT09IGtleSAmJiBpc1ZhbGlkKSB7XHJcbiAgICAgICAgICAgICAgICBjb250aW51ZVxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGlzVmFsaWQpIHtcclxuICAgICAgICAgICAgICAgIGRhdGEubm9kZS5kZXN0cm95KClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aGlzLnVzZUl0ZW1zLnNwbGljZShpLCAxKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgY2xlYW5Vc2VBbmRSZW1vdmVJdGVtc0J5VGFnKGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5jbGVhblVzZUl0ZW1zQnlUYWcoa2V5KVxyXG4gICAgICAgIHRoaXMucmVtb3ZlSXRlbXNCeVRhZyhrZXkpXHJcbiAgICB9XHJcbn1cclxuXHJcbndpbmRvd1snbm9kZVBvb2xNZ3InXSA9IG5ldyBOb2RlUG9vbE1ncigpIl19