
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/MapSceneHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '79e48/nWZpOp6dJRxGhq0zt', 'MapSceneHelper');
// app/script/model/snailisle/MapSceneHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapSceneHelper = void 0;
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
/**
 * 地图场景
 */
var MapSceneHelper = /** @class */ (function () {
    function MapSceneHelper() {
        this.tempPoint1 = cc.v2();
        this.tempPoint2 = cc.v2();
        this.tempPoint3 = cc.v2();
        this.tempVec2 = cc.v2();
    }
    /**
     * 根据网格坐标获取像素坐标
     * @param point [传入的point不会被改变]
     * @param type
     */
    MapSceneHelper.prototype.getPixelByPoint = function (point, type, out) {
        out = out || this.tempPoint1;
        if (type === Enums_1.MapType.SKEW) {
            out.x = (point.x - point.y) * Constant_1.SKEW_SIZE_HALF.width;
            out.y = (point.x + point.y) * Constant_1.SKEW_SIZE_HALF.height + Constant_1.SKEW_SIZE_HALF.height;
        }
        else {
            point.mul(Constant_1.TILE_SIZE, out).addSelf(Constant_1.TILE_SIZE_HALF);
        }
        return out;
    };
    /**
     * 根据像素位置获取网格坐标
     * @param pos
     * @param type
     */
    MapSceneHelper.prototype.getPointByPixel = function (pos, type, out) {
        out = out || this.tempPoint2;
        if (type === Enums_1.MapType.SKEW) {
            var x = pos.x / Constant_1.SKEW_SIZE.width; //-0.0625
            var y = pos.y / Constant_1.SKEW_SIZE.height; // 0.95
            out.x = Math.ceil(x + y) - 1;
            out.y = Math.ceil(y - x) - 1;
        }
        else {
            out.x = Math.floor(pos.x / Constant_1.TILE_SIZE);
            out.y = Math.floor(pos.y / Constant_1.TILE_SIZE);
        }
        return out;
    };
    // 获取实际的网格点
    MapSceneHelper.prototype.getActPointByPixel = function (pos, type, out) {
        out = out || this.tempPoint3;
        if (type === Enums_1.MapType.SKEW) {
            var x = pos.x / Constant_1.SKEW_SIZE.width;
            var y = pos.y / Constant_1.SKEW_SIZE.height;
            out.x = x + y;
            out.y = y - x;
        }
        else {
            out.x = pos.x / Constant_1.TILE_SIZE;
            out.y = pos.y / Constant_1.TILE_SIZE;
        }
        return out;
    };
    // 获取地图网格像素的起点
    MapSceneHelper.prototype.getMapGridPixel = function (point, type) {
        var pos = this.getPixelByPoint(point, type);
        if (type === Enums_1.MapType.SKEW) {
            return cc.v2(0, -Constant_1.SKEW_SIZE_HALF.height).addSelf(pos);
        }
        return cc.v2(-Constant_1.TILE_SIZE_HALF.x, -Constant_1.TILE_SIZE_HALF.y).addSelf(pos);
    };
    /**
     * 转换地图实际大小
     */
    MapSceneHelper.prototype.convertMapSize = function (type, grid) {
        if (type === Enums_1.MapType.SKEW) {
            return cc.size((grid.width + grid.height) * Constant_1.SKEW_SIZE_HALF.width, (grid.width + grid.height) * Constant_1.SKEW_SIZE_HALF.height);
        }
        return cc.size(grid.width * Constant_1.TILE_SIZE, grid.height * Constant_1.TILE_SIZE);
    };
    // 获取地图的矩形顶点
    MapSceneHelper.prototype.getMapPoints = function (type, grid, size) {
        if (type === Enums_1.MapType.SKEW) {
            return [cc.v2(), cc.v2(grid.width * Constant_1.SKEW_SIZE_HALF.width, grid.width * Constant_1.SKEW_SIZE_HALF.height), cc.v2((grid.width - grid.height) * Constant_1.SKEW_SIZE_HALF.width, size.height), cc.v2(-grid.height * Constant_1.SKEW_SIZE_HALF.width, grid.height * Constant_1.SKEW_SIZE_HALF.height)];
        }
        return [cc.v2(), cc.v2(size.width, 0), cc.v2(size.width, size.height), cc.v2(0, size.height)];
    };
    // 修正速度
    MapSceneHelper.prototype.amendMoveSpeed = function (speed, position, targetPosition, dt) {
        speed.mul(dt, this.tempVec2);
        var diffx = targetPosition.x - position.x;
        if (Math.abs(diffx) <= Math.abs(this.tempVec2.x)) {
            this.tempVec2.x = diffx;
        }
        var diffy = targetPosition.y - position.y;
        if (Math.abs(diffy) <= Math.abs(this.tempVec2.y)) {
            this.tempVec2.y = diffy;
        }
        return this.tempVec2;
    };
    // 给地图上面的静态建筑排序层级
    // AABBC
    // AA家具排序(最多60个道具)，BB地图格子排序(最多99)，C一格排序(0-9)
    // 目前没用C，所以最多600个道具
    MapSceneHelper.prototype.sortMapObjZindex = function (items, z, removes, p) {
        if (z === void 0) { z = -30; }
        if (removes === void 0) { removes = []; }
        if (p === void 0) { p = 1000; }
        if (items.length === 0) {
            return removes;
        }
        var count = items.length;
        var _loop_1 = function (i) {
            var it = items[i], point = it.point;
            if (!items.some(function (m) { return m.uid !== it.uid && m.getActPoints().some(function (p) { return p.x >= point.x && p.y >= point.y; }); })) {
                it.zIndex = (++z) * p;
                items.splice(i, 1);
            }
        };
        for (var i = count - 1; i >= 0; i--) {
            _loop_1(i);
        }
        if (count === items.length) {
            removes.push(items.shift().uid);
            cc.error('sortMapObjZindex count === items.length?');
        }
        return this.sortMapObjZindex(items, z, removes, p);
    };
    return MapSceneHelper;
}());
exports.mapSceneHelper = new MapSceneHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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