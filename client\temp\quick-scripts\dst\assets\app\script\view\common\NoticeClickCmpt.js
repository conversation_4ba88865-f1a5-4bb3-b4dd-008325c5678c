
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/NoticeClickCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a9c82R7yMFIe6z4ZYXydUgZ', 'NoticeClickCmpt');
// app/script/view/common/NoticeClickCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var NoticeClickCmpt = /** @class */ (function (_super) {
    __extends(NoticeClickCmpt, _super);
    function NoticeClickCmpt() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NoticeClickCmpt.prototype.onHandler = function (event, data) {
        var _a = __read(data.split('|'), 2), type = _a[0], value = _a[1];
        if (type === 'url') {
            cc.sys.openURL(value);
        }
    };
    NoticeClickCmpt = __decorate([
        ccclass
    ], NoticeClickCmpt);
    return NoticeClickCmpt;
}(cc.Component));
exports.default = NoticeClickCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcTm90aWNlQ2xpY2tDbXB0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBUSxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUE2QyxtQ0FBWTtJQUF6RDs7SUFRQSxDQUFDO0lBTkcsbUNBQVMsR0FBVCxVQUFVLEtBQTBCLEVBQUUsSUFBWTtRQUN4QyxJQUFBLEtBQUEsT0FBZ0IsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBQSxFQUE5QixJQUFJLFFBQUEsRUFBRSxLQUFLLFFBQW1CLENBQUE7UUFDckMsSUFBSSxJQUFJLEtBQUssS0FBSyxFQUFFO1lBQ2hCLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3hCO0lBQ0wsQ0FBQztJQVBnQixlQUFlO1FBRG5DLE9BQU87T0FDYSxlQUFlLENBUW5DO0lBQUQsc0JBQUM7Q0FSRCxBQVFDLENBUjRDLEVBQUUsQ0FBQyxTQUFTLEdBUXhEO2tCQVJvQixlQUFlIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgTm90aWNlQ2xpY2tDbXB0IGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcclxuXHJcbiAgICBvbkhhbmRsZXIoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG4gICAgICAgIGNvbnN0IFt0eXBlLCB2YWx1ZV0gPSBkYXRhLnNwbGl0KCd8JylcclxuICAgICAgICBpZiAodHlwZSA9PT0gJ3VybCcpIHtcclxuICAgICAgICAgICAgY2Muc3lzLm9wZW5VUkwodmFsdWUpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il19