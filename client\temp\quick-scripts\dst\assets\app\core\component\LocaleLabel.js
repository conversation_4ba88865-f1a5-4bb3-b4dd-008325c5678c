
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LocaleLabel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9705eZTZHVNNalGAojhJWC6', 'LocaleLabel');
// app/core/component/LocaleLabel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LocaleLabel = /** @class */ (function (_super) {
    __extends(LocaleLabel, _super);
    function LocaleLabel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.fontName = '';
        _this._label = null;
        _this._string = '';
        _this._lang = '';
        _this._font = '';
        _this._json = null;
        _this._params = [];
        _this._temp_params = []; //转换好过后的参数
        _this._change = false;
        _this._is_empty_string = false; //是否主动设置空字符串
        return _this;
    }
    Object.defineProperty(LocaleLabel.prototype, "label", {
        get: function () {
            if (!this._label) {
                this._label = this.getComponent(cc.Label);
            }
            return this._label;
        },
        enumerable: false,
        configurable: true
    });
    LocaleLabel.prototype.onEnable = function () {
        if (!mc.lang) {
            return;
        }
        else if (this._lang !== mc.lang) {
            this._lang = mc.lang;
            this._font = '';
            this._json = null;
            this.updateTempParams();
        }
        else if (this.label.font && !this.label.font.isValid) {
            this._font = '';
        }
        if (!this._json) {
            this.updateJson();
        }
        else if (!this._font) {
            this.updateFont();
        }
        this.updateString();
        this._change = mc.canChangeLang;
        if (this._change) {
            eventCenter.on(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    LocaleLabel.prototype.onDisable = function () {
        if (this._change) {
            this._change = false;
            eventCenter.off(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    // 语言切换
    LocaleLabel.prototype.onLanguageChanged = function (lang) {
        this._lang = lang;
        this._font = '';
        this.updateTempParams();
        this.updateJson();
        this.updateString();
    };
    Object.defineProperty(LocaleLabel.prototype, "string", {
        get: function () { return this.label.string; },
        set: function (val) {
            this.label.string = val;
            this._is_empty_string = val === '';
        },
        enumerable: false,
        configurable: true
    });
    LocaleLabel.prototype.updateLang = function () {
        this._lang = mc.lang;
    };
    // 刷新string
    LocaleLabel.prototype.updateString = function () {
        var val = this._json ? this._json[this._lang] : undefined;
        if (val !== undefined) {
            this._string = ut.stringFormat(val, this._temp_params);
        }
        else if (this.key) {
            this._string = ut.stringFormat(this.key, this._temp_params);
        }
        else if (this._is_empty_string) {
            this._string = '';
        }
        else {
            this._string = '404';
        }
        if (this._string !== this.label.string) {
            this.label.string = this._string;
        }
    };
    // 设置参数
    LocaleLabel.prototype.setParams = function (params) {
        var _this = this;
        this._params.length = 0;
        params === null || params === void 0 ? void 0 : params.forEach(function (m) { return Array.isArray(m) ? _this._params.pushArr(m) : _this._params.push(m); });
        this.updateTempParams();
    };
    // 刷新参数
    LocaleLabel.prototype.updateTempParams = function () {
        var _this = this;
        this._temp_params = this._params.map(function (m) {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                var _a = __read(m.split('.'), 2), name = _a[0], id = _a[1];
                var json = assetsMgr.getJsonData(name, id) || {};
                var val = json[_this._lang];
                return val !== undefined ? val : m;
            }
            return m;
        });
    };
    // 刷新json
    LocaleLabel.prototype.updateJson = function () {
        if (this.key) {
            var _a = __read(this.key.split('.'), 2), name = _a[0], id = _a[1];
            this._json = assetsMgr.getJsonData(name, id);
            this.updateFont();
        }
        else {
            this._json = null;
        }
    };
    // 刷新字体
    LocaleLabel.prototype.updateFont = function () {
        if (this.fontName !== '') {
            if (this.fontName !== this._font) {
                this.setFont(this.fontName);
            }
        }
        else if (this._json && this._json.font && this._json.font !== this._font) {
            this.setFont(this._json.font);
        }
    };
    LocaleLabel.prototype.setFont = function (fontUrl) {
        this._font = fontUrl;
        var font = assetsMgr.getFont(fontUrl);
        if (font) {
            this.label.string = '';
            this.label.font = font;
        }
        else {
            this.label.font = null;
            this._font = '';
        }
    };
    // 设置key
    LocaleLabel.prototype.setKey = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        if (!this._lang) {
            this._lang = mc.lang;
        }
        if (this.key !== key || !this._json) {
            this.key = key;
            this.string = '';
            this.updateJson();
        }
        this._is_empty_string = key === '';
        this.setParams(params);
        this.updateString();
    };
    __decorate([
        property()
    ], LocaleLabel.prototype, "key", void 0);
    __decorate([
        property()
    ], LocaleLabel.prototype, "fontName", void 0);
    LocaleLabel = __decorate([
        ccclass,
        menu('多语言组件/LocaleLabel'),
        requireComponent(cc.Label)
    ], LocaleLabel);
    return LocaleLabel;
}(BaseLocale_1.default));
exports.default = LocaleLabel;
cc.LocaleLabel = LocaleLabel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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