
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/EquipEffectObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '80222SEJ1NA5qZK5lqZwJXN', 'EquipEffectObj');
// app/script/model/main/EquipEffectObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
// 装备效果
var EquipEffectObj = /** @class */ (function () {
    function EquipEffectObj() {
        this.type = 0;
        this.value = 0;
        this.odds = 0; //触发概率
    }
    EquipEffectObj.prototype.init = function (type, value, odds) {
        this.type = type;
        this.value = value || 0;
        this.odds = odds || 0;
        return this;
    };
    Object.defineProperty(EquipEffectObj.prototype, "name", {
        get: function () { return 'equipText.effect_' + this.type; },
        enumerable: false,
        configurable: true
    });
    // 获取说明参数
    EquipEffectObj.prototype.getDescParams = function (showRnage) {
        var arr = [], json = assetsMgr.getJsonData('equipEffect', this.type);
        if (!json) {
            return arr;
        }
        var _a = __read(json.value.split(','), 2), a = _a[0], b = _a[1];
        if (this.type === Enums_1.EquipEffectType.MINGGUANG_ARMOR) {
            arr.push((this.value * 10) + json.suffix);
        }
        else if (this.type === Enums_1.EquipEffectType.BAIBI_SWORD) {
            arr.push(this.value + json.suffix);
        }
        else if (!json.value) {
            arr.push(''); //这里没有也要加一个 因为要占位
        }
        else if (a === b) {
            arr.push(this.value + json.suffix);
        }
        else {
            var val = showRnage ? this.value + "<color=#C2B3A1>[" + json.value.replace(',', '-') + "]</c>" : this.value + '';
            arr.push(val + json.suffix);
        }
        if (json.odds) {
            var val = showRnage ? this.odds + "<color=#C2B3A1>[" + json.odds.replace(',', '-') + "]</c>" : this.odds + '';
            arr.push(val + '%');
        }
        return arr;
    };
    return EquipEffectObj;
}());
exports.default = EquipEffectObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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