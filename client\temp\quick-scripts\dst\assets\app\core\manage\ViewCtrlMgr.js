
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/ViewCtrlMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7b762tQD0JATLLljp2IsxGT', 'ViewCtrlMgr');
// app/core/manage/ViewCtrlMgr.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BasePnlCtrl_1 = require("../base/BasePnlCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var ResLoader_1 = require("../utils/ResLoader");
var ViewCtrlMgr = /** @class */ (function () {
    function ViewCtrlMgr() {
        this.node = null;
        this.caches = new Map();
        this.opened = [];
        this.masks = []; // 遮罩列表
        this.pnlOpenIndex = 0; // 打开顺序id
        this.pnlIndexConf = {}; // ui的层级关系配置
        this.loadQueues = []; // 当前加载队列
        this.loadId = 0;
    }
    ViewCtrlMgr.prototype.getOpened = function () {
        return this.opened;
    };
    ViewCtrlMgr.prototype.getLoadQueues = function () {
        return this.loadQueues;
    };
    ViewCtrlMgr.prototype.setPnlIndexConf = function (conf) {
        this.pnlIndexConf = conf;
    };
    // 添加到加载队列
    ViewCtrlMgr.prototype.addLoadQueue = function (name) {
        return this.loadQueues.add({ id: ++this.loadId, name: name, url: '' });
    };
    ViewCtrlMgr.prototype.hasLoadQueue = function (name) {
        return this.loadQueues.has('name', name);
    };
    ViewCtrlMgr.prototype.__load = function (name, info) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, url, mod, _a, wind, pnl, head, head;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pfb = null, url = '', mod = '';
                        _a = __read(name.split('/'), 2), wind = _a[0], pnl = _a[1];
                        if (!!pnl) return [3 /*break*/, 4];
                        head = ut.initialUpperCase(wind).replace('Pnl', '');
                        mod = mc.currWindName;
                        info.url = url = "view/" + mod + "/" + head + "Pnl";
                        ResLoader_1.loader.error = false;
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.Prefab)];
                    case 1:
                        pfb = _b.sent();
                        ResLoader_1.loader.error = true;
                        if (!!pfb) return [3 /*break*/, 3];
                        mod = 'common';
                        info.url = url = "view/" + mod + "/" + head + "Pnl";
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.Prefab)];
                    case 2:
                        pfb = _b.sent();
                        _b.label = 3;
                    case 3: return [3 /*break*/, 6];
                    case 4:
                        head = ut.initialUpperCase(pnl).replace('Pnl', '');
                        mod = wind;
                        info.url = url = "view/" + mod + "/" + head + "Pnl";
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.Prefab)];
                    case 5:
                        pfb = _b.sent();
                        _b.label = 6;
                    case 6:
                        // console.timeEnd('load ' + name)
                        if (!this.loadQueues.remove('id', info.id)) {
                            pfb = null;
                        }
                        info.id = 0; //这里表示已经加载完成了 用于后续弹出是否重试检测是的时候
                        return [2 /*return*/, Promise.resolve({ mod: mod, url: url, pfb: pfb })];
                }
            });
        });
    };
    // 加载一个Pnl
    ViewCtrlMgr.prototype.__loadPnl = function (name, info) {
        return __awaiter(this, void 0, Promise, function () {
            var _a, mod, url, pfb, it, className, pnl;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.__load(name, info)];
                    case 1:
                        _a = _b.sent(), mod = _a.mod, url = _a.url, pfb = _a.pfb;
                        if (!pfb) {
                            return [2 /*return*/, Promise.resolve(null)];
                        }
                        it = cc.instantiate2(pfb, this.node);
                        className = it.name + 'Ctrl';
                        if (!cc.js.getClassByName(className)) {
                            logger.error('loadPnl error! not found class ' + className);
                            return [2 /*return*/, Promise.resolve(null)];
                        }
                        pnl = it.getComponent(className);
                        if (!pnl) {
                            pnl = it.addComponent(className);
                        }
                        if (!pnl || !(pnl instanceof BasePnlCtrl_1.default)) {
                            logger.error('loadPnl error! not found class ' + className);
                            return [2 /*return*/, Promise.resolve(null)];
                        }
                        pnl.key = name;
                        pnl.mod = mod;
                        pnl.url = url;
                        // 这里检查一下 是否还没有加载属性
                        if (!pnl._isLoadProperty) {
                            logger.error('load pnl error! not load property. at=' + className);
                            pnl.loadProperty();
                        }
                        it.active = false;
                        return [4 /*yield*/, pnl.__create()];
                    case 2:
                        _b.sent();
                        this.caches.set(url, pnl);
                        return [2 /*return*/, Promise.resolve(pnl)];
                }
            });
        });
    };
    // 获取缓存的Pnl
    ViewCtrlMgr.prototype.__getForCache = function (name) {
        var _a = __read(name.split('/'), 2), wind = _a[0], key = _a[1], ui = null;
        if (!key) {
            var head = ut.initialUpperCase(wind).replace('Pnl', '');
            ui = this.caches.get("view/" + mc.currWindName + "/" + head + "Pnl");
            if (!ui) {
                ui = this.caches.get("view/common/" + head + "Pnl");
            }
        }
        else {
            var head = ut.initialUpperCase(key).replace('Pnl', '');
            ui = this.caches.get("view/" + wind + "/" + head + "Pnl");
        }
        return ui;
    };
    // 预加载
    ViewCtrlMgr.prototype.preloadPnl = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            var pnl;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        pnl = this.__getForCache(name);
                        if (!(!pnl && !this.hasLoadQueue(name))) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.__loadPnl(name, this.addLoadQueue(name))];
                    case 1:
                        pnl = _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/, pnl];
                }
            });
        });
    };
    // 获取一个遮罩
    ViewCtrlMgr.prototype.getMask = function (ui) {
        var it = this.masks.pop();
        if (!it) {
            var pfb = assetsMgr.getPrefab('PNL_MASK');
            if (pfb) {
                it = cc.instantiate(pfb);
            }
        }
        if (it) {
            it.parent = this.node;
            it.active = true;
            it.opacity = ui.getMaskInitOpacity();
            it.zIndex = ui.node.zIndex - 1;
        }
        ui.mask = it;
    };
    ViewCtrlMgr.prototype.putMask = function (ui) {
        if (ui && ui.mask) {
            ui.mask.parent = null;
            this.masks.push(ui.mask);
            ui.mask = null;
        }
    };
    // 播放显示的动作
    ViewCtrlMgr.prototype.playShowAction = function (ui) {
        return __awaiter(this, void 0, void 0, function () {
            var widget;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        widget = ui.Component(cc.Widget);
                        if (widget && widget.enabled) {
                            widget.updateAlignment();
                            widget.enabled = false;
                        }
                        ui.node.stopAllActions();
                        ui.node.scale = 0.4;
                        cc.tween(ui.node).to(0.25, { scale: 1 }, { easing: cc.easing.backOut }).start();
                        if (ui.mask) {
                            ui.mask.stopAllActions();
                            ui.mask.opacity = 0;
                            cc.tween(ui.mask).to(0.3, { opacity: ui.getMaskInitOpacity() }, { easing: cc.easing.sineOut }).start();
                        }
                        return [4 /*yield*/, ut.wait(0.25)];
                    case 1:
                        _a.sent();
                        if (ui.isValid) {
                            ui.onPlayActionComplete();
                            eventCenter.emit(CoreEventType_1.default.PNL_ENTER_PLAY_DONE, ui);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 适应大小
    ViewCtrlMgr.prototype.adaptRootSize = function (ui) {
        if (!ui) {
            return;
        }
        var root = ui.Child('root') || ui.Child('root_n');
        if (!root) {
            return;
        }
        var wsize = cc.winSize;
        var dsize = cc.view.getDesignResolutionSize();
        var rsize = root.getContentSize();
        // 算出宽度比例
        var scale = (rsize.width / dsize.width * wsize.width) / rsize.width;
        // 如果高度超过了
        var height = wsize.height - ui.adaptHeight;
        if (rsize.height * scale > height) {
            scale = height / rsize.height;
        }
        root.scale = Math.min(1.2, scale);
    };
    ViewCtrlMgr.prototype.getNextOpenIndex = function () {
        return ++this.pnlOpenIndex;
    };
    ViewCtrlMgr.prototype.pushPnl = function (ui) {
        ui.__open_index = this.getNextOpenIndex();
        this.opened.remove('url', ui.url);
        this.opened.push(ui);
        this.updatePnlIndex();
    };
    ViewCtrlMgr.prototype.popPnl = function (ui) {
        this.opened.remove('url', ui.url);
        this.updatePnlIndex();
    };
    // 刷新ui层级关系
    ViewCtrlMgr.prototype.updatePnlIndex = function () {
        var _this = this;
        // 排个序根据打开顺序
        var list = this.opened.sort(function (a, b) { return a.__open_index - b.__open_index; }).map(function (m, i) {
            var index = i + 1;
            return {
                ui: m,
                key: m.key,
                initIndex: index,
                sortIndex: index * 1000
            };
        });
        list.forEach(function (m) {
            var _a;
            var conf = _this.pnlIndexConf[m.key];
            if (!conf) {
                return;
            }
            var lt = (_a = conf.lt) === null || _a === void 0 ? void 0 : _a.slice();
            // 找出大于的 调整自己的位置 在大于的上面
            if (conf.gt) {
                var arr = list.filter(function (pnl) { return conf.gt.has(pnl.key); }).sort(function (a, b) { return b.sortIndex - a.sortIndex; }), temp = arr[0];
                if (temp && m.sortIndex < temp.sortIndex) {
                    m.sortIndex = temp.sortIndex + m.initIndex;
                }
                // 这里如果小于的也在 当前大于的大于里面 就删除掉
                lt && arr.forEach(function (m) {
                    var _a;
                    var gt = (_a = _this.pnlIndexConf[m.key]) === null || _a === void 0 ? void 0 : _a.gt;
                    gt && lt.delete(function (s) { return gt.has(s); });
                });
            }
            // 找出小于的 调整小于的位置 在自己的上面
            lt && list.filter(function (pnl) { return lt.has(pnl.key); }).forEach(function (temp) {
                if (temp.sortIndex < m.sortIndex) {
                    temp.sortIndex = m.sortIndex + temp.initIndex;
                }
            });
        });
        list.sort(function (a, b) { return a.sortIndex - b.sortIndex; }).forEach(function (m, i) { return m.ui.setIndex(i * 10); });
        this.opened.sort(function (a, b) { return a.node.zIndex - b.node.zIndex; });
    };
    // 显示一个UI
    ViewCtrlMgr.prototype.show = function (pnl) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, Promise, function () {
            var ui, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mc.lockTouch('__show_pnl__');
                        ui = null;
                        if (!(typeof (pnl) === 'string')) return [3 /*break*/, 3];
                        ui = this.__getForCache(pnl);
                        if (!(!ui && !this.hasLoadQueue(pnl))) return [3 /*break*/, 2];
                        data = this.addLoadQueue(pnl);
                        data.params = params;
                        eventCenter.emit(CoreEventType_1.default.LOAD_BEGIN_PNL, data);
                        return [4 /*yield*/, this.__loadPnl(pnl, data)];
                    case 1:
                        ui = _a.sent();
                        eventCenter.emit(CoreEventType_1.default.LOAD_END_PNL, data);
                        _a.label = 2;
                    case 2: return [3 /*break*/, 4];
                    case 3:
                        if (pnl.isValid && pnl._state !== 'clean') {
                            ui = pnl;
                        }
                        else {
                            return [2 /*return*/, this.show.apply(this, __spread([pnl.key], params))];
                        }
                        _a.label = 4;
                    case 4:
                        if (!ui || !ui.isValid) {
                            mc.unlockTouch('__show_pnl__');
                            return [2 /*return*/, null];
                        }
                        else if (!ui.getActive()) {
                            this.pushPnl(ui);
                            ui.setActive(true);
                            ui.isMask && this.getMask(ui);
                            ui.__enter.apply(ui, __spread(params));
                            this.adaptRootSize(ui);
                            ui.setOpacity(255);
                            // 发送进入事件
                            eventCenter.emit(CoreEventType_1.default.PNL_ENTER, ui);
                            if (ui.isAct) {
                                this.playShowAction(ui).then(function () { return mc.unlockTouch('__show_pnl__'); });
                            }
                            else {
                                ui.node.scale = 1;
                                ui.onPlayActionComplete();
                                eventCenter.emit(CoreEventType_1.default.PNL_ENTER_PLAY_DONE, ui);
                                mc.unlockTouch('__show_pnl__');
                            }
                        }
                        else {
                            mc.unlockTouch('__show_pnl__');
                        }
                        return [2 /*return*/, ui];
                }
            });
        });
    };
    // 隐藏一个Pnl
    ViewCtrlMgr.prototype.hide = function (val) {
        var ui = val instanceof BasePnlCtrl_1.default ? val : this.__getForCache(val);
        if (!ui || !ui.isValid) {
            return;
        }
        this.popPnl(ui);
        this.putMask(ui);
        if (ui.getActive()) {
            ui.__remove();
            ui.setActive(false);
            eventCenter.emit(CoreEventType_1.default.PNL_LEAVE, ui);
        }
    };
    // 隐藏所有Pnl
    ViewCtrlMgr.prototype.hideAll = function (val, ignores) {
        var _this = this;
        if (!val) {
            var arr = ignores ? ignores.split('|') : [];
            for (var i = this.opened.length - 1; i >= 0; i--) {
                var m = this.opened[i];
                // 这里关闭所有的时候 忽略掉不清理的UI
                if (m.isClean && arr.indexOf(m.key) === -1) {
                    this.opened.splice(i, 1);
                    this.putMask(m);
                    if (m.getActive()) {
                        m.__remove();
                        m.setActive(false);
                        eventCenter.emit(CoreEventType_1.default.PNL_LEAVE, m);
                    }
                }
            }
            this.updatePnlIndex();
        }
        else {
            val.split('|').forEach(function (m) { return _this.hide(m); });
        }
    };
    // 清理一个Pnl
    ViewCtrlMgr.prototype.clean = function (val, force) {
        var ui = val instanceof BasePnlCtrl_1.default ? val : this.__getForCache(val);
        if (!ui || (!ui.isClean && !force)) {
            return;
        }
        this.hide(ui);
        ui.__clean();
        ui.node.destroy();
        this.caches.delete(ui.url);
        ResLoader_1.loader.releaseRes(ui.url, cc.Prefab);
    };
    // 清理所有Pnl
    ViewCtrlMgr.prototype.cleanAll = function (val, force) {
        var _this = this;
        if (!val) {
            this.caches.forEach(function (m) { return _this.clean(m, force); });
            this.cleanLoadQueue();
        }
        else {
            val.split('|').forEach(function (m) {
                _this.clean(m, force);
                _this.giveupLoadByName(m);
            });
        }
    };
    // 清理pnl根据模块
    ViewCtrlMgr.prototype.cleanByMod = function (mod) {
        var _this = this;
        this.caches.forEach(function (m) { return m.mod === mod && _this.clean(m); });
    };
    // 清理所有未打开的Pnl
    ViewCtrlMgr.prototype.cleanAllUnused = function () {
        var _this = this;
        this.caches.forEach(function (m) { return !m.getActive() && _this.clean(m); });
        this.cleanLoadQueue();
    };
    // 清理加载队列
    ViewCtrlMgr.prototype.cleanLoadQueue = function (isUnlock) {
        while (this.loadQueues.length > 0) {
            ResLoader_1.loader.giveupLoad(this.loadQueues.pop().url);
        }
        if (isUnlock) {
            mc.unlockTouch('__show_pnl__');
        }
    };
    // 放弃加载
    ViewCtrlMgr.prototype.giveupLoadByName = function (name) {
        var data = this.loadQueues.remove('name', name);
        data && ResLoader_1.loader.giveupLoad(data.url);
    };
    // 放弃当前加载
    ViewCtrlMgr.prototype.giveupLoadById = function (id) {
        var data = this.loadQueues.remove('id', id);
        data && ResLoader_1.loader.giveupLoad(data.url);
    };
    return ViewCtrlMgr;
}());
exports.default = ViewCtrlMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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