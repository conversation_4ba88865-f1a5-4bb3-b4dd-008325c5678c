
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SeasonLandDiAnim.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e90cfeBa11JrZNNoXz4mcP3', 'SeasonLandDiAnim');
// app/script/view/main/SeasonLandDiAnim.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, property = _a.property, ccclass = _a.ccclass;
// 换季 地块底部动画
var SeasonLandDiAnim = /** @class */ (function (_super) {
    __extends(SeasonLandDiAnim, _super);
    function SeasonLandDiAnim() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    SeasonLandDiAnim = __decorate([
        ccclass
    ], SeasonLandDiAnim);
    return SeasonLandDiAnim;
}(cc.Component));
exports.default = SeasonLandDiAnim;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXFNlYXNvbkxhbmREaUFuaW0udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUF3QixFQUFFLENBQUMsVUFBVSxFQUFuQyxRQUFRLGNBQUEsRUFBRSxPQUFPLGFBQWtCLENBQUM7QUFFNUMsWUFBWTtBQUVaO0lBQThDLG9DQUFZO0lBQTFEOztJQUVBLENBQUM7SUFGb0IsZ0JBQWdCO1FBRHBDLE9BQU87T0FDYSxnQkFBZ0IsQ0FFcEM7SUFBRCx1QkFBQztDQUZELEFBRUMsQ0FGNkMsRUFBRSxDQUFDLFNBQVMsR0FFekQ7a0JBRm9CLGdCQUFnQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgcHJvcGVydHksIGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG4vLyDmjaLlraMg5Zyw5Z2X5bqV6YOo5Yqo55S7XHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNlYXNvbkxhbmREaUFuaW0gZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxufSJdfQ==