
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/GuideObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9e890fT0DdIDbo2L4O+d6Hh', 'GuideObj');
// app/script/model/guide/GuideObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var GuideConfig_1 = require("./GuideConfig");
// 一个引导
var GuideObj = /** @class */ (function () {
    function GuideObj() {
        this.id = 0; //模块id
        this.progressTag = ''; //模块进度标记
        this.currTag = ''; //当前标记
        this.checkFunc = null; //模块触发检测
        this.steps = []; //当前步骤列表
        this.index = 0; //当前步骤
        this.isSub = false; //是否是子引导
        this.finishTag = '';
    }
    GuideObj.prototype.fromSvr = function (id, progress) {
        var _a;
        this.id = id;
        this.progressTag = this.currTag = progress || '0';
        var conf = GuideConfig_1.GUIDE_CONFIG.datas.find(function (m) { return m.id === id; });
        this.steps = conf.steps;
        this.checkFunc = conf.checkFunc;
        this.isSub = (_a = conf.isSub) !== null && _a !== void 0 ? _a : false;
        this.finishTag = this.steps.last().tag;
        this.resetIndex();
        return this;
    };
    GuideObj.prototype.isFinish = function () {
        return this.progressTag === this.finishTag;
    };
    GuideObj.prototype.complete = function () {
        this.progressTag = this.currTag = this.finishTag;
        return this.progressTag;
    };
    GuideObj.prototype.getCurrStep = function () {
        return this.steps[this.index];
    };
    GuideObj.prototype.resetIndex = function () {
        var _this = this;
        this.index = this.steps.findIndex(function (m) { return m.tag === _this.progressTag; });
    };
    return GuideObj;
}());
exports.default = GuideObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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