
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelTimer.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '37b09imgMpEbpFEmXhZUc6V', 'LabelTimer');
// app/core/component/LabelTimer.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LabelTimer = /** @class */ (function (_super) {
    __extends(LabelTimer, _super);
    function LabelTimer() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.format = 'hh:mm:ss';
        _this.endTime = 0; //结束时间 -1.表示无限增加
        _this.prefix = '';
        _this.suffix = '';
        _this._label = null;
        _this.pause = false;
        _this.formatFunc = null; //格式化方法
        _this.time = 0;
        _this.tempTime = 0;
        _this.callback = null;
        _this.startTime = 0;
        _this.speed = 1;
        return _this;
    }
    Object.defineProperty(LabelTimer.prototype, "label", {
        get: function () {
            if (!this._label) {
                this._label = this.getComponent(cc.Label);
            }
            return this._label;
        },
        enumerable: false,
        configurable: true
    });
    LabelTimer.prototype.Color = function (val) {
        this.node.Color(val);
        return this;
    };
    LabelTimer.prototype.setPause = function (val) {
        this.pause = val;
    };
    LabelTimer.prototype.setPrefix = function (val) {
        this.prefix = val;
        return this;
    };
    LabelTimer.prototype.setFormat = function (val) {
        if (typeof val === 'string') {
            this.format = val;
            this.formatFunc = null;
        }
        else if (typeof val === 'function') {
            this.format = '';
            this.formatFunc = val;
        }
        return this;
    };
    LabelTimer.prototype.setEndTime = function (val) {
        this.endTime = val;
        return this;
    };
    LabelTimer.prototype.run = function (time, callback) {
        this.time = time;
        this.speed = this.endTime === -1 ? 1 : ut.normalizeNumber(this.endTime - time);
        this.callback = callback;
        this.tempTime = Math.floor(this.time);
        this.startTime = ut.now();
        this.updateLabelString();
    };
    Object.defineProperty(LabelTimer.prototype, "string", {
        get: function () { return this.label.string; },
        set: function (val) {
            this.time = -1;
            this.tempTime = -1;
            this.startTime = 0;
            this.callback = null;
            this.label.string = val;
        },
        enumerable: false,
        configurable: true
    });
    LabelTimer.prototype.getTime = function () { return this.time; };
    LabelTimer.prototype.update = function () {
        if (this.startTime === 0 || this.pause) {
            return;
        }
        var now = ut.now();
        var dt = (now - this.startTime) * 0.001;
        this.startTime = now;
        this.time += dt * this.speed;
        var t = Math.floor(this.time);
        if (this.tempTime !== t) {
            this.tempTime = t;
            this.updateLabelString();
        }
        if (this.endTime === -1) { //无限累加不会结束
        }
        else if ((this.speed < 0 && this.time <= this.endTime) || (this.speed > 0 && this.time >= this.endTime)) {
            this.startTime = 0;
            this.tempTime = this.endTime;
            this.updateLabelString();
            this.callback && this.callback();
            if (this.startTime === 0) {
                this.callback = null;
            }
        }
    };
    LabelTimer.prototype.updateLabelString = function () {
        var _a;
        if (this.format) {
            this.label.string = this.prefix + ut.millisecondFormat(this.tempTime * 1000, this.format) + this.suffix;
        }
        else if (this.formatFunc) {
            this.label.string = this.prefix + ((_a = this.formatFunc(this.tempTime)) !== null && _a !== void 0 ? _a : this.tempTime) + this.suffix;
        }
        else {
            this.label.string = this.prefix + this.tempTime + this.suffix;
        }
    };
    __decorate([
        property()
    ], LabelTimer.prototype, "format", void 0);
    __decorate([
        property()
    ], LabelTimer.prototype, "endTime", void 0);
    __decorate([
        property()
    ], LabelTimer.prototype, "prefix", void 0);
    __decorate([
        property()
    ], LabelTimer.prototype, "suffix", void 0);
    LabelTimer = __decorate([
        ccclass,
        menu('自定义组件/LabelTimer'),
        requireComponent(cc.Label)
    ], LabelTimer);
    return LabelTimer;
}(cc.Component));
exports.default = LabelTimer;
cc.LabelTimer = LabelTimer;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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