
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/HeroSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b31e0nLwfxJeLnGAy/Z6TqE', 'HeroSlotObj');
// app/script/model/main/HeroSlotObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var PortrayalInfo_1 = require("../common/PortrayalInfo");
// 英雄槽位信息
var HeroSlotObj = /** @class */ (function () {
    function HeroSlotObj() {
        this.lv = 0; //需要等级
        this.hero = null; //当前英雄
        this.reviveSurplusTime = 0; //复活剩余时间
        this.avatarArmyUID = ''; //化身的军队uid
        this.getTime = 0; //获取时间
    }
    HeroSlotObj.prototype.fromSvr = function (data) {
        this.lv = data.lv;
        this.hero = data.hero ? new PortrayalInfo_1.default().fromSvr(data.hero) : null;
        this.reviveSurplusTime = data.reviveSurplusTime || 0;
        this.avatarArmyUID = data.avatarArmyUID || '';
        this.getTime = Date.now();
        return this;
    };
    HeroSlotObj.prototype.getReviveSurplusTime = function () {
        return this.reviveSurplusTime > 0 ? Math.max(0, this.reviveSurplusTime - (Date.now() - this.getTime)) : 0;
    };
    HeroSlotObj.prototype.isDie = function () {
        return this.getReviveSurplusTime() > 0;
    };
    // 0.选择画像 1.可化身 2.化身中 3.阵亡中
    HeroSlotObj.prototype.getState = function () {
        if (!this.hero) {
            return 0;
        }
        else if (this.isDie()) {
            return 3;
        }
        else if (this.avatarArmyUID) {
            return 2;
        }
        return 1;
    };
    // 获取化身说明
    HeroSlotObj.prototype.getAvatarDesc = function () {
        var _this = this;
        var _a, _b, _c;
        var time = this.getReviveSurplusTime();
        if (time) { //该英雄的化身已阵亡，{0}后可再次化身
            return ['ui.avatar_hero_desc_2', GameHelper_1.gameHpr.millisecondToString(time), '#C34A32'];
        }
        else if (this.avatarArmyUID) { //该英雄已化身在'{0}'中
            return ['ui.avatar_hero_desc_1', ((_a = GameHelper_1.gameHpr.player.getBaseArmys().find(function (m) { return m.uid === _this.avatarArmyUID; })) === null || _a === void 0 ? void 0 : _a.name) || '???', '#756963'];
        }
        return ['ui.avatar_hero_desc_0', (_c = (_b = this.hero) === null || _b === void 0 ? void 0 : _b.avatarPawnName) !== null && _c !== void 0 ? _c : '???', '#49983C']; //可选择军队中的{0}，化身成该英雄
    };
    return HeroSlotObj;
}());
exports.default = HeroSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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