
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/UIMenuChildPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f0310LHvbBAWpGqvHMBDc18', 'UIMenuChildPnlCtrl');
// app/script/view/common/UIMenuChildPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var UIMenuChildPnlCtrl = /** @class */ (function (_super) {
    __extends(UIMenuChildPnlCtrl, _super);
    function UIMenuChildPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_n
        _this.menuNode_ = null; // path://menu_n
        _this.bookNode_ = null; // path://menu_n/menu/book_n
        _this.marchNode_ = null; // path://menu_n/menu/march_be_n
        _this.backLobbyNode_ = null; // path://menu_n/menu/back_lobby_be_n
        _this.leaveNoviceNode_ = null; // path://menu_n/menu/leave_novice_be_n
        return _this;
    }
    //@end
    UIMenuChildPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    UIMenuChildPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                this.setParam({ isClean: false, isAct: false, isMask: false });
                this.closeNode_.on(cc.Node.EventType.TOUCH_END, function () { return _this.openAnimation(false); }, this);
                this.closeNode_.SetSwallowTouches(false);
                return [2 /*return*/];
            });
        });
    };
    UIMenuChildPnlCtrl.prototype.onEnter = function (data) {
        this.menuNode_.Child('menu/task').active = !GameHelper_1.gameHpr.isNoviceMode;
        this.menuNode_.Child('menu/mail').active = !GameHelper_1.gameHpr.isNoviceMode;
        this.menuNode_.Child('menu/rank').active = !GameHelper_1.gameHpr.isNoviceMode;
        this.bookNode_.active = this.marchNode_.active = this.backLobbyNode_.active = !GameHelper_1.gameHpr.isNoviceMode;
        this.leaveNoviceNode_.active = GameHelper_1.gameHpr.isNoviceMode;
        this.marchNode_.opacity = this.backLobbyNode_.opacity = GameHelper_1.gameHpr.isInLobby() ? 150 : 255;
        this.openAnimation(true);
    };
    UIMenuChildPnlCtrl.prototype.onRemove = function () {
    };
    UIMenuChildPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://menu_n/menu/back_lobby_be_n
    UIMenuChildPnlCtrl.prototype.onClickBackLobby = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.isInLobby()) {
            ViewHelper_1.viewHelper.showAlert('toast.already_in_lobby');
        }
        else {
            if (!GameHelper_1.gameHpr.world.isGameOver() || !ViewHelper_1.viewHelper.showNoLongerTip('back_lobby_tip', {
                content: 'ui.back_lobby_tip',
                okText: 'login.button_ok',
                ok: function () { return _this.backToLobby(); },
                cancel: function () { },
            })) {
                this.backToLobby();
            }
        }
    };
    // path://menu_n/menu/leave_novice_be_n
    UIMenuChildPnlCtrl.prototype.onClickLeaveNovice = function (event, data) {
        ViewHelper_1.viewHelper.showMessageBox('ui.leave_novice_tip', {
            ok: function () { return GameHelper_1.gameHpr.guide.forceEndNovice(); },
            cancel: function () { }
        });
    };
    // path://menu_n/menu/march_be_n
    UIMenuChildPnlCtrl.prototype.onClickMarch = function (event, data) {
        if (GameHelper_1.gameHpr.isInLobby()) {
            ViewHelper_1.viewHelper.showAlert('toast.please_game_use');
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/MarchSetting');
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 动画
    UIMenuChildPnlCtrl.prototype.openAnimation = function (open) {
        var _this = this;
        if (open) {
            this.menuNode_.scaleY = 0;
            cc.tween(this.menuNode_).to(0.2, { scaleY: 1 }, { easing: cc.easing.sineOut }).start();
        }
        else {
            this.menuNode_.scaleY = 1;
            cc.tween(this.menuNode_).to(0.1, { scaleY: 0 }).call(function () { return _this.hide(); }).start();
        }
    };
    UIMenuChildPnlCtrl.prototype.backToLobby = function () {
        this.hide();
        ViewHelper_1.viewHelper.backLobby();
    };
    UIMenuChildPnlCtrl = __decorate([
        ccclass
    ], UIMenuChildPnlCtrl);
    return UIMenuChildPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = UIMenuChildPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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