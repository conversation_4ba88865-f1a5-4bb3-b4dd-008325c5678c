
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/TondenEndPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '23e1cwlSaVF8YBgwCtcx2bW', 'TondenEndPnlCtrl');
// app/script/view/area/TondenEndPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var TondenEndPnlCtrl = /** @class */ (function (_super) {
    __extends(TondenEndPnlCtrl, _super);
    function TondenEndPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.treasureNode_ = null; // path://content/treasure_n
        _this.notStaminaNode_ = null; // path://content/not_stamina_n
        _this.flyNode_ = null; // path://fly_n
        //@end
        _this.isClickClose = false;
        return _this;
    }
    TondenEndPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    TondenEndPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    TondenEndPnlCtrl.prototype.onEnter = function (treasures) {
        var _this = this;
        this.isClickClose = false;
        audioMgr.playSFX('common/sound_ui_001');
        this.Child('title').active = true;
        if (this.treasureNode_.active = treasures.length > 0) {
            this.treasureNode_.Items(treasures, function (it, data) {
                it.Data = data;
                var json = assetsMgr.getJsonData('treasure', data.id);
                ResHelper_1.resHelper.loadIcon('icon/treasure_' + ((json === null || json === void 0 ? void 0 : json.lv) || 1) + '_0', it.Child('icon'), _this.key);
            });
        }
        this.notStaminaNode_.active = !treasures.length;
        this.flyNode_.Swih('');
    };
    TondenEndPnlCtrl.prototype.onRemove = function () {
        this.isClickClose = false;
    };
    TondenEndPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be
    TondenEndPnlCtrl.prototype.onClickClose = function (event, data) {
        var _this = this;
        var _a;
        if (this.isClickClose) {
            return;
        }
        this.isClickClose = true;
        if (this.notStaminaNode_.active) {
            return this.hide();
        }
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'common/UI'; });
        if (!ui) {
            return this.hide();
        }
        var treasureMap = {};
        (_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) { return m.getAllPawnTreasures().forEach(function (t) {
            treasureMap[t.uid] = true;
        }); });
        var a = [], b = [];
        this.treasureNode_.children.forEach(function (m) {
            var _a;
            if (treasureMap[(_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid]) {
                a.push(_this.addItem(m));
            }
            else {
                b.push(_this.addItem(m));
            }
        });
        // 飞里面
        if (a.length > 0 || b.length > 0) {
            this.hideAll();
            var nodeA = ui.FindChild('scene_n/area/bottom/area_army_be_n'), posA = ut.convertToNodeAR(nodeA, this.flyNode_).clone();
            var nodeB = ui.FindChild('scene_n/area/back_main_be'), posB = ut.convertToNodeAR(nodeB, this.flyNode_).clone();
            this.playFly(a, posA);
            this.playFly(b, posB);
            ut.wait(0.7, this).then(function () { return _this.isValid && _this.hide(); });
        }
        else {
            this.hide();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    TondenEndPnlCtrl.prototype.addItem = function (node) {
        var it = this.flyNode_.children.find(function (m) { return !m.active; }) || cc.instantiate2(this.flyNode_.children[0], this.flyNode_);
        it.active = true;
        it.Component(cc.Sprite).spriteFrame = node.Child('icon', cc.Sprite).spriteFrame;
        it.setPosition(ut.convertToNodeAR(node.Child('icon'), this.flyNode_));
        return it;
    };
    TondenEndPnlCtrl.prototype.hideAll = function () {
        this.showMask(false);
        this.Child('title').active = false;
        this.treasureNode_.active = false;
        this.notStaminaNode_.active = false;
    };
    TondenEndPnlCtrl.prototype.playFly = function (arr, pos) {
        if (arr.length === 0) {
            return;
        }
        arr.forEach(function (m) {
            m.scale = 1;
            cc.tween(m)
                .to(0.5, { x: pos.x, y: pos.y }, { easing: cc.easing.sineIn })
                .to(0.2, { scale: 1.3, opacity: 0 })
                .hide()
                .start();
        });
    };
    TondenEndPnlCtrl = __decorate([
        ccclass
    ], TondenEndPnlCtrl);
    return TondenEndPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TondenEndPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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