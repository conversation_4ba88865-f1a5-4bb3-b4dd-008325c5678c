
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ViewHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd7769eBkKxF1YXHywafMCu6', 'ViewHelper');
// app/script/common/helper/ViewHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewHelper = void 0;
var CTypeObj_1 = require("../../model/common/CTypeObj");
var Constant_1 = require("../constant/Constant");
var ECode_1 = require("../constant/ECode");
var Enums_1 = require("../constant/Enums");
var NetEvent_1 = require("../event/NetEvent");
var NotEvent_1 = require("../event/NotEvent");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
var ResHelper_1 = require("./ResHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var StrategyObj_1 = require("../../model/common/StrategyObj");
var FrameAnimationCmpt_1 = require("../../view/cmpt/FrameAnimationCmpt");
var PortrayalSkillObj_1 = require("../../model/common/PortrayalSkillObj");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var AdaptWidthCmpt_1 = require("../../view/cmpt/AdaptWidthCmpt");
/**
 * 视图帮助方法
 */
var ViewHelper = /** @class */ (function () {
    function ViewHelper() {
    }
    // 跳转场景
    ViewHelper.prototype.gotoWind = function (val) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.GOTO_WIND, val, resolve], params)); })];
            });
        });
    };
    // 预加载场景
    ViewHelper.prototype.preloadWind = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress); })];
            });
        });
    };
    // 预加载UI
    ViewHelper.prototype.preloadPnl = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_PNL, key, resolve, progress); })];
            });
        });
    };
    // 显示UI
    ViewHelper.prototype.showPnl = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit.apply(eventCenter, __spread([mc.Event.OPEN_PNL, key, resolve], params)); })];
            });
        });
    };
    // 隐藏UI
    ViewHelper.prototype.hidePnl = function (key) {
        eventCenter.emit(mc.Event.HIDE_PNL, key);
    };
    // 按顺序依次打开多个界面，且要判断是否已打开
    ViewHelper.prototype.showBuyGoldTipPnl = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pnls, opened, pnls_1, pnls_1_1, key, e_1_1;
            var e_1, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pnls = ['common/Shop', 'common/ShopBuyGoldTip'], opened = mc.getOpenPnls().map(function (m) { return m.key; });
                        _b.label = 1;
                    case 1:
                        _b.trys.push([1, 6, 7, 8]);
                        pnls_1 = __values(pnls), pnls_1_1 = pnls_1.next();
                        _b.label = 2;
                    case 2:
                        if (!!pnls_1_1.done) return [3 /*break*/, 5];
                        key = pnls_1_1.value;
                        if (opened.includes(key)) {
                            return [3 /*break*/, 4];
                        }
                        return [4 /*yield*/, this.showPnl(key)];
                    case 3:
                        _b.sent();
                        _b.label = 4;
                    case 4:
                        pnls_1_1 = pnls_1.next();
                        return [3 /*break*/, 2];
                    case 5: return [3 /*break*/, 8];
                    case 6:
                        e_1_1 = _b.sent();
                        e_1 = { error: e_1_1 };
                        return [3 /*break*/, 8];
                    case 7:
                        try {
                            if (pnls_1_1 && !pnls_1_1.done && (_a = pnls_1.return)) _a.call(pnls_1);
                        }
                        finally { if (e_1) throw e_1.error; }
                        return [7 /*endfinally*/];
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    // 显示提示框
    ViewHelper.prototype.showAlert = function (msg, opts) {
        if (msg === ECode_1.ecode.NOT_BIND_UID) {
            return eventCenter.emit(NetEvent_1.default.NET_DISCONNECT);
        }
        eventCenter.emit(NotEvent_1.default.OPEN_ALERT, msg, opts);
    };
    // 显示对话框
    ViewHelper.prototype.showMessageBox = function (msg, opts) {
        eventCenter.emit(NotEvent_1.default.OPEN_MESSAGE_BOX, msg, opts);
    };
    // 主动关闭对话框
    ViewHelper.prototype.hideMessageBox = function () {
        eventCenter.emit(NotEvent_1.default.HIDE_MESSAGE_BOX);
    };
    // 显示说明
    ViewHelper.prototype.showDesc = function (text, params) {
        this.showPnl('common/Desc', { text: text, params: params });
    };
    // 显示说明信息
    ViewHelper.prototype.showDescInfo = function (list, title) {
        if (title === void 0) { title = 'ui.explain'; }
        this.showPnl('common/DescInfo', { title: title, list: list });
    };
    // 显示网络等待
    ViewHelper.prototype.showNetWait = function (val, delay) {
        if (val) {
            eventCenter.emit(NetEvent_1.default.NET_REQ_BEGIN, delay);
        }
        else {
            eventCenter.emit(NetEvent_1.default.NET_REQ_END);
        }
    };
    // 显示通用加载动画
    ViewHelper.prototype.showLoadingWait = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOADING_WAIT_BEGIN);
        }
        else {
            eventCenter.emit(mc.Event.LOADING_WAIT_END);
        }
    };
    // 显示加载wind的动画
    ViewHelper.prototype.showWindLoading = function (val) {
        if (val) {
            eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
        }
        else {
            eventCenter.emit(mc.Event.LOAD_END_WIND);
        }
    };
    // 显示连接失败
    ViewHelper.prototype.showConnectFail = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.connect_server_fail');
                        _this.showMessageBox(text, {
                            lockClose: true,
                            okText: 'login.button_retry',
                            ok: function () { return resolve(true); },
                            cancel: function () { return resolve(false); },
                        });
                    })];
            });
        });
    };
    // 刷新地块的边框线
    ViewHelper.prototype.updateCellBorderLines = function (node, lines, color) {
        node.Items(lines, function (it, line) {
            var conf = Constant_1.BORDER_LINE_CONF[line];
            it.setContentSize(conf.size);
            it.setPosition(conf.pos);
            it.Color(color);
        });
    };
    // 绘制网格
    ViewHelper.prototype.drawGrid = function (g, size, start) {
        g.clear();
        var pos = cc.v2();
        var w = size.x * Constant_1.TILE_SIZE, h = size.y * Constant_1.TILE_SIZE;
        for (var i = 0; i <= size.x; i++) {
            pos.set(start).x += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x, pos.y + h);
        }
        for (var i = 0; i <= size.y; i++) {
            pos.set(start).y += i;
            MapHelper_1.mapHelper.getPixelByPoint(pos, pos).subSelf(Constant_1.TILE_SIZE_HALF);
            g.moveTo(pos.x, pos.y);
            g.lineTo(pos.x + w, pos.y);
        }
        g.stroke();
    };
    // 显示建筑的基础信息
    ViewHelper.prototype.updateBuildBaseUI = function (node, data, key) {
        this.updateBuildBaseInfo(node.Child('top'), data, key);
        this.updateBuildAttrInfo(node, data);
    };
    // 基础上信息
    ViewHelper.prototype.updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv').setLocaleKey('ui.lv', data.lv);
        var desc = node.Child('desc') || node.Child('info/desc');
        desc.setLocaleKey(data.desc);
    };
    ViewHelper.prototype._updateBuildBaseInfo = function (node, data, key) {
        ResHelper_1.resHelper.loadBuildIcon(data.icon, node.Child('icon/val', cc.Sprite), key);
        node.Child('icon/name').setLocaleKey(data.name);
        node.Child('icon/lv/val').setLocaleKey('ui.lv', data.lv);
        var descLbl = node.Child('desc', cc.Label);
        descLbl.setLocaleKey(data.desc);
        descLbl._forceUpdateRenderData();
        node.height = Math.max(220, descLbl.node.height + 168);
    };
    ViewHelper.prototype._updateBuildAttrInfo = function (data, attr, bottom, attrs, key) {
        var _a;
        var top = attr.Child('top');
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv();
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        var nextLv = data.tempNextLv || ((_a = data.nextLvInfo) === null || _a === void 0 ? void 0 : _a.lv);
        if (nextLv) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', nextLv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        attr.Child('items').Items(attrs, function (it, data, i) {
            var curr = data.curr, nextVal = data.nextVal;
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey(curr.key, curr.params);
            if (it.Child('next').active = !!nextVal) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextVal;
            }
            it.Child('line').active = i < attrs.length - 1;
        });
        // 刷新费用和按钮
        if (bottom.active = !isMaxLv) {
            this.updateBuildBottomInfo(data, bottom);
        }
    };
    ViewHelper.prototype.updateBuildBottomInfo = function (data, bottom) {
        var needMainLv = data.id !== Constant_1.BUILD_MAIN_NID && data.lv >= GameHelper_1.gameHpr.player.getMainBuildLv(); // 只要不是主城 就不能比主城等级高
        var params = needMainLv ? [data.lv + 1] : [];
        var condText = data.id === Constant_1.BUILD_MAIN_NID ? GameHelper_1.gameHpr.checkCellCountCond(data.attrJson.prep_cond) : GameHelper_1.gameHpr.checkUnlcokBuildCond(data.attrJson.prep_cond) || (needMainLv ? 'ui.need_main_lv' : '');
        if (!condText) {
            bottom.Child('title/val').setLocaleKey('ui.up_cost');
            bottom.Child('cond').active = false;
            var need = bottom.Child('need');
            need.active = true;
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        else {
            bottom.Child('title/val').setLocaleKey('ui.up_cond');
            bottom.Child('need').active = false;
            var cond = bottom.Child('cond');
            cond.active = true;
            cond.Child('val').setLocaleKey(condText, params);
        }
        this.updateBuildButtons(bottom.Child('buttons'), data, condText);
    };
    // 显示建筑的属性信息
    ViewHelper.prototype.updateBuildAttrInfo = function (node, data) {
        var _a;
        var attr = node.Child('attrs/attr');
        var dtype = data.id === Constant_1.BUILD_PLANT_NID ? 501 : (_a = data.effect) === null || _a === void 0 ? void 0 : _a.getDescType();
        var showAttr = attr.active = !!dtype && (data.id !== Constant_1.BUILD_EMBASSY_NID || GameHelper_1.gameHpr.alliance.isMeCreater());
        // 显示下级信息和升级费用
        var isMaxLv = data.isMaxLv(), nextInfo = data.nextLvInfo;
        var top = attr.Child('top'), need = node.Child('need');
        top.active = need.active = !isMaxLv;
        attr.Child('items').Items(1, function (it, _data) {
            var _a, _b;
            if (showAttr) {
                it.Child('cur/val').setLocaleKey('ui.build_eff_desc_' + dtype, ((_a = data.effect) === null || _a === void 0 ? void 0 : _a.getValueText()) || '');
            }
            var nextLbl = it.Child('next', cc.Label);
            if (it.Child('arrow').active = nextLbl.setActive(!isMaxLv)) {
                nextLbl.setLocaleKey(((_b = nextInfo.effect) === null || _b === void 0 ? void 0 : _b.getValueText()) || '');
            }
        });
        if (!isMaxLv) {
            top.Child('cur').setLocaleKey('ui.lv', data.lv);
            top.Child('next').setLocaleKey('ui.lv', data.nextLvInfo.lv);
            this.updateCostViewForBuild(need, data.upCost, data.attrJson.bt_time);
        }
        this.updateBuildButtons(need.Child('buttons'), data);
    };
    // 刷新按钮
    ViewHelper.prototype.updateBuildButtons = function (buttonsNode, data, condText) {
        if (data.isMaxLv()) {
            return buttonsNode.Swih('');
        }
        buttonsNode.opacity = !!condText ? 120 : 255;
        var player = GameHelper_1.gameHpr.player;
        var bt = player.getBuildBtInfo(data.uid);
        if (bt) { //是否在队列中
            buttonsNode.Swih('uping')[0].Child('val').setLocaleKey(bt.isRuning() ? 'ui.uping' : 'ui.queueing');
        }
        else {
            buttonsNode.Swih('up_be');
        }
    };
    ViewHelper.prototype.updateCostViewForBuild = function (node, ctypes, time, cd) {
        // const up = node.Child('time/guide_up')
        if (GameHelper_1.gameHpr.isNoviceMode) {
            cd = GameHelper_1.gameHpr.noviceServer.getBuildUpSpeedTime(time).cd;
        }
        this.updateCostView(node, ctypes, time, cd);
        // if (up?.getActive()) {
        //     node.Child('time/val', cc.Label).Color('#49983C')
        // }
    };
    // 刷新费用
    ViewHelper.prototype.updateCostView = function (node, ctypes, time, cd) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateCostViewOne(it, cost, true); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            cd = cd || 0;
            var up = node.Child('time/up', cc.Label);
            if (up === null || up === void 0 ? void 0 : up.setActive(!!cd)) {
                up.string = "(-" + Math.floor(cd * 100) + "%)";
            }
            if (cd) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    time = Math.floor(time * ut.numberFixed(1 - cd));
                }
                else {
                    time = Math.max(3, Math.floor(time * ut.numberFixed(1 - cd)));
                }
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
        }
    };
    // 刷新费用
    ViewHelper.prototype.updateFreeCostView = function (node, ctypes, time, cd, isFree, policyFreeCount, upCount) {
        var _this = this;
        var _a;
        node.Child('cost').Items(ctypes || [], function (it, cost) { return _this.updateFreeCostViewOne(it, cost, !(isFree || policyFreeCount > 0)); });
        if ((_a = node.Child('time')) === null || _a === void 0 ? void 0 : _a.setActive(!!time)) {
            var up = node.Child('time/up');
            if (isFree || policyFreeCount > 0 || upCount > 0) { // 政策的免费 || 兵营、工厂开局送的加速9个
                cd = 1;
                var infoNode = up.Swih('info')[0], freeNode = infoNode.Child('free');
                if (freeNode === null || freeNode === void 0 ? void 0 : freeNode.setActive(isFree || policyFreeCount > 0)) {
                    var bothFree = isFree && policyFreeCount > 0, valLbl = freeNode.Child('val', cc.Label), addLbl = freeNode.Child('add', cc.Label);
                    if (valLbl.node.active = (bothFree || policyFreeCount > 0)) {
                        valLbl.string = bothFree ? "x(" + policyFreeCount : policyFreeCount > 0 ? "x" + policyFreeCount : '';
                    }
                    if (addLbl.node.active = (bothFree || isFree)) {
                        addLbl.string = bothFree ? '+1' : isFree ? 'x1' : '';
                    }
                    freeNode.Child('xx').active = bothFree;
                }
                var upNode = infoNode.Child('up');
                if (upNode === null || upNode === void 0 ? void 0 : upNode.setActive(!!upCount)) {
                    upNode.Child('val', cc.Label).string = 'x' + upCount;
                }
            }
            else if (!!cd) { // 正常升级的cd减免
                up.Swih('val')[0].Component(cc.Label).string = "(-" + Math.floor(cd * 100) + "%)";
            }
            else {
                up.Swih('');
            }
            if (cd) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    time = Math.floor(time * ut.numberFixed(1 - cd));
                }
                else {
                    time = Math.max(3, Math.floor(time * ut.numberFixed(1 - cd)));
                }
            }
            node.Child('time/val', cc.Label).Color(cd ? '#49983C' : '#756963').string = ut.secondFormat(time, 'h:mm:ss');
            node.Child('cost').children.forEach(function (m) { var _a; return m.opacity = (isFree || policyFreeCount > 0) && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.type) !== Enums_1.CType.FIXATOR ? 100 : 255; });
            node.Child('time/up/val').opacity = node.Child('time/val').opacity = node.Child('time/icon').opacity = (isFree || policyFreeCount > 0) ? 100 : 255;
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            if (!isCheck) {
                it.Child('val', cc.Label).string = cost.count + '';
            }
            else {
                it.Child('val', cc.Label).Color(GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
            }
        }
    };
    // 刷新单个费用
    ViewHelper.prototype.updateFreeCostViewOne = function (it, cost, isCheck) {
        if (it && cost) {
            it.Data = cost;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost.type);
            it.Child('val', cc.Label).Color(!isCheck || GameHelper_1.gameHpr.checkCType(cost) ? '#756963' : '#D7634D').string = cost.count + '';
        }
    };
    // 更新费用
    ViewHelper.prototype.updateCostText = function (it, json) {
        var _a, _b, _c;
        if (json.gold > 0) {
            (_a = it.Child('gold/desc')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.button_buy');
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.GOLD);
            it.Child('gold/val', cc.Label).string = json.gold + '';
        }
        else if (json.ingot > 0) {
            (_b = it.Child('gold/desc')) === null || _b === void 0 ? void 0 : _b.setLocaleKey('ui.button_buy');
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.INGOT);
            it.Child('gold/val', cc.Label).string = json.ingot + '';
        }
        else if (json.rank_coin > 0) {
            (_c = it.Child('gold/desc')) === null || _c === void 0 ? void 0 : _c.setLocaleKey('ui.button_exchange_skin');
            it.Child('gold/icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.RANK_COIN);
            it.Child('gold/val', cc.Label).string = json.rank_coin + '';
        }
        else {
            it.Child('gold').active = false;
        }
    };
    // 显示位置
    ViewHelper.prototype.updatePositionView = function (it, index, hasName) {
        if (hasName === void 0) { hasName = true; }
        it.Data = index;
        var posLbl = it.Component(cc.Label);
        if (hasName) {
            posLbl.setLocaleKey('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(index), MapHelper_1.mapHelper.indexToPoint(index).Join());
        }
        else {
            posLbl.string = '(' + MapHelper_1.mapHelper.indexToPoint(index).Join() + ')';
        }
        posLbl._forceUpdateRenderData();
        it.Child('line').width = posLbl.node.width;
    };
    // 刷新道具 根据类型
    ViewHelper.prototype.updateItemByCTypes = function (node, items, key) {
        var _this = this;
        node === null || node === void 0 ? void 0 : node.Items(items, function (it, data) { return _this.updateItemByCTypeOne(it, data, key); });
    };
    ViewHelper.prototype.updateItemByCTypeOne = function (it, item, key, adaptSize, isAddX) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
        if (!item || !it) {
            return;
        }
        var iconSpr = it.Child('icon', cc.Sprite), countLbl = it.Child('count', cc.Label);
        if (iconSpr) {
            iconSpr.node.removeAllChildren();
        }
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadBuildIcon('build_' + item.id, iconSpr, key || mc.currWindName);
            (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPlayerHead(iconSpr, ((_b = assetsMgr.getJsonData('headIcon', item.id)) === null || _b === void 0 ? void 0 : _b.icon) || '', key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey('ui.headicon_title');
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEmojiIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.chat_emoji_title');
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_e = assetsMgr.getJsonData('treasure', item.id)) === null || _e === void 0 ? void 0 : _e.lv) || 1;
            (_f = it.Swih('text')[0]) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_g = it.Child('text')) === null || _g === void 0 ? void 0 : _g.setLocaleKey('titleText.' + item.id);
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
            (_h = it.Child('text')) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.rank_score_num_2', item.count);
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_j = it.Child('text')) === null || _j === void 0 ? void 0 : _j.setLocaleKey('pawnText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadEquipIcon(item.id, iconSpr, key || mc.currWindName);
            (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey('equipText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadPolicyIcon(item.id, iconSpr, key || mc.currWindName);
            (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('policyText.name_' + item.id);
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [assetsMgr.getJsonData('pawnSkin', item.id)] }); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
                ResHelper_1.resHelper.loadPawnHeadIcon(item.id, skinNode, key || mc.currWindName);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                iconSpr && ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                (_o = it.Swih('text')[0]) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(item.id, iconSpr, key || mc.currWindName);
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey('portrayalText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                (_q = it.Swih('text')[0]) === null || _q === void 0 ? void 0 : _q.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type] + '_' + item.id, iconSpr, key || mc.currWindName);
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            (_s = it.Swih('text')[0]) === null || _s === void 0 ? void 0 : _s.setLocaleKey('guideText.guide_task_complete');
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            iconSpr && ResHelper_1.resHelper.loadCityIcon(item.id, iconSpr, key || mc.currWindName).then(function () { return (iconSpr.isValid && adaptSize) && iconSpr.node.adaptScale(adaptSize); });
            (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey('ui.title_main_city_skin');
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (iconSpr) {
                var sf = ResHelper_1.resHelper.getResIcon(item.type);
                if (sf) {
                    iconSpr.spriteFrame = sf;
                }
                else {
                    ResHelper_1.resHelper.loadIcon(Constant_1.CTYPE_ICON_URL[item.type], iconSpr, key || mc.currWindName);
                }
            }
            if (countLbl) {
                countLbl.string = isAddX ? 'x' + item.count : '' + item.count;
            }
        }
    };
    ViewHelper.prototype.updateItemNameByCTypeOne = function (it, item, isFormat) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8;
        if (isFormat === void 0) { isFormat = false; }
        if (!item || !it) {
            return;
        }
        var countLbl = it.Child('count', cc.Label);
        if (item.type === Enums_1.CType.BUILD_LV) { //建筑等级
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_a = it.Child('text')) === null || _a === void 0 ? void 0 : _a.setLocaleKey('ui.build_lv', 'buildText.name_' + item.id, item.count);
            }
            else {
                (_b = it.Child('text')) === null || _b === void 0 ? void 0 : _b.setLocaleKey('ui.build_lv', ut.nameFormator(assetsMgr.lang('buildText.name_' + item.id), 5), item.count);
            }
        }
        else if (item.type === Enums_1.CType.HEAD_ICON) { //头像
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_c = it.Child('text')) === null || _c === void 0 ? void 0 : _c.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.headicon_title'), 5));
            }
            else {
                (_d = it.Child('text')) === null || _d === void 0 ? void 0 : _d.setLocaleKey('ui.headicon_title');
            }
        }
        else if (item.type === Enums_1.CType.CHAT_EMOJI) { //聊天表情
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_e = it.Child('text')) === null || _e === void 0 ? void 0 : _e.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.chat_emoji_title'), 5));
            }
            else {
                (_f = it.Child('text')) === null || _f === void 0 ? void 0 : _f.setLocaleKey('ui.chat_emoji_title');
            }
        }
        else if (item.type === Enums_1.CType.TREASURE) { //宝箱
            var lv = ((_g = assetsMgr.getJsonData('treasure', item.id)) === null || _g === void 0 ? void 0 : _g.lv) || 1;
            if (isFormat) {
                (_h = it.Swih('text')[0]) === null || _h === void 0 ? void 0 : _h.setLocaleKey('ui.treasure_reward_desc', ut.nameFormator(assetsMgr.lang('ui.treasure_name_' + lv), 5), item.count);
            }
            else {
                (_j = it.Swih('text')[0]) === null || _j === void 0 ? void 0 : _j.setLocaleKey('ui.treasure_reward_desc', 'ui.treasure_name_' + lv, item.count);
            }
        }
        else if (item.type === Enums_1.CType.TITLE) { //称号
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_k = it.Child('text')) === null || _k === void 0 ? void 0 : _k.setLocaleKey(ut.nameFormator(assetsMgr.lang('titleText.' + item.id), 5));
            }
            else {
                (_l = it.Child('text')) === null || _l === void 0 ? void 0 : _l.setLocaleKey('titleText.' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.WIN_POINT) { //胜点
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_m = it.Child('text')) === null || _m === void 0 ? void 0 : _m.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.rank_score_num_2'), 5), item.count);
            }
            else {
                (_o = it.Child('text')) === null || _o === void 0 ? void 0 : _o.setLocaleKey('ui.rank_score_num_2', item.count);
            }
        }
        else if (item.type === Enums_1.CType.PAWN) { //士兵
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_p = it.Child('text')) === null || _p === void 0 ? void 0 : _p.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + item.id), 5));
            }
            else {
                (_q = it.Child('text')) === null || _q === void 0 ? void 0 : _q.setLocaleKey('pawnText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.EQUIP) { //装备
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_r = it.Child('text')) === null || _r === void 0 ? void 0 : _r.setLocaleKey(ut.nameFormator(assetsMgr.lang('equipText.name_' + item.id), 5));
            }
            else {
                (_s = it.Child('text')) === null || _s === void 0 ? void 0 : _s.setLocaleKey('equipText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.POLICY) { //政策
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_t = it.Child('text')) === null || _t === void 0 ? void 0 : _t.setLocaleKey(ut.nameFormator(assetsMgr.lang('policyText.name_' + item.id), 5));
            }
            else {
                (_u = it.Child('text')) === null || _u === void 0 ? void 0 : _u.setLocaleKey('policyText.name_' + item.id);
            }
        }
        else if (item.type === Enums_1.CType.PAWN_SKIN) { //皮肤
            var textNode = it.Child('text_click'), skinNode = it.Child('pawn_skin');
            if (textNode) { //纯文本 '限定皮肤'
                if (isFormat) {
                    it.Swih('text_click')[0].setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.limited_skin_reward_desc'), 5));
                }
                else {
                    it.Swih('text_click')[0].setLocaleKey('ui.limited_skin_reward_desc');
                }
                textNode.off('click');
                textNode.on('click', function () { return _this.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [assetsMgr.getJsonData('pawnSkin', item.id)] }); });
            }
            else if (skinNode) {
                it.Swih('pawn_skin');
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_v = it.Child('text')) === null || _v === void 0 ? void 0 : _v.setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + Math.floor(item.id / 1000)), 5));
                }
                else {
                    (_w = it.Child('text')) === null || _w === void 0 ? void 0 : _w.setLocaleKey('pawnText.name_' + Math.floor(item.id / 1000));
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_DEBRIS) { //英雄残卷
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_x = it.Swih('text')[0]) === null || _x === void 0 ? void 0 : _x.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id), 5));
                }
                else {
                    (_y = it.Swih('text')[0]) === null || _y === void 0 ? void 0 : _y.setLocaleKey('ui.brackets', assetsMgr.lang('ui.hero_gift', 'portrayalText.name_' + item.id));
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_z = it.Child('text')) === null || _z === void 0 ? void 0 : _z.setLocaleKey(ut.nameFormator(assetsMgr.lang('portrayalText.name_' + item.id), 5));
                }
                else {
                    (_0 = it.Child('text')) === null || _0 === void 0 ? void 0 : _0.setLocaleKey('portrayalText.name_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.HERO_OPT) { //自选英雄包
            if (it.Child('text_click')) {
                if (isFormat) {
                    (_1 = it.Swih('text')[0]) === null || _1 === void 0 ? void 0 : _1.setLocaleKey('ui.brackets', ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_' + item.id), 5));
                }
                else {
                    (_2 = it.Swih('text')[0]) === null || _2 === void 0 ? void 0 : _2.setLocaleKey('ui.brackets', 'ui.hero_opt_gift_' + item.id);
                }
            }
            else {
                it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
                if (isFormat) {
                    (_3 = it.Child('text')) === null || _3 === void 0 ? void 0 : _3.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.hero_opt_gift_short_' + item.id), 5));
                }
                else {
                    (_4 = it.Child('text')) === null || _4 === void 0 ? void 0 : _4.setLocaleKey('ui.hero_opt_gift_short_' + item.id);
                }
            }
        }
        else if (item.type === Enums_1.CType.COMPLETE_GUIDE) { //完成新手引导
            if (isFormat) {
                (_5 = it.Swih('text')[0]) === null || _5 === void 0 ? void 0 : _5.setLocaleKey(ut.nameFormator(assetsMgr.lang('guideText.guide_task_complete'), 5));
            }
            else {
                (_6 = it.Swih('text')[0]) === null || _6 === void 0 ? void 0 : _6.setLocaleKey('guideText.guide_task_complete');
            }
        }
        else if (item.type === Enums_1.CType.CITY_SKIN) { //城市皮肤
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'text'; });
            if (isFormat) {
                (_7 = it.Child('text')) === null || _7 === void 0 ? void 0 : _7.setLocaleKey(ut.nameFormator(assetsMgr.lang('ui.title_main_city_skin'), 5));
            }
            else {
                (_8 = it.Child('text')) === null || _8 === void 0 ? void 0 : _8.setLocaleKey('ui.title_main_city_skin');
            }
        }
        else {
            it.Swih(function (m) { return m.name === 'icon' || m.name === 'count'; });
            if (countLbl) {
                countLbl.string = '' + item.count;
            }
        }
    };
    // 刷新装备显示
    ViewHelper.prototype.updateEquipView = function (node, equip, key, lockEffect, smeltEffects, showRange) {
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey(equip.name);
        var meltNode = node.Child('melt') || node.Child('icon/melt');
        if (meltNode === null || meltNode === void 0 ? void 0 : meltNode.setActive(equip.isSmelt())) {
            for (var i = 0; i < 2; i++) {
                var it = meltNode.Child(i), data = equip.smeltEffects[i];
                if (it.active = !!data) {
                    ResHelper_1.resHelper.loadEquipIcon(data.id, it, key);
                }
            }
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(false);
        var exclusive = node.Child('exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!equip.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + equip.exclusive_pawn);
        }
        this.updateEquipAttrView(node.Child('attrs'), equip, lockEffect, smeltEffects, showRange);
    };
    ViewHelper.prototype.updateEquipAttrView = function (node, equip, lockEffect, smeltEffects, showRange) {
        var _a, _b, _c, _d;
        var attrNode = node.Child('attr'), effectNode = node.Child('effects'), skillIntensifyNode = node.Child('skill_intensify');
        (_a = node.Child('more_desc')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        (_b = node.Child('more_effects')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        (_c = node.Child('more_tip')) === null || _c === void 0 ? void 0 : _c.setActive(false);
        (_d = node.Child('view_attr_be')) === null || _d === void 0 ? void 0 : _d.setActive(false);
        // 属性
        var serverRunDay = GameHelper_1.gameHpr.getServerRunDay();
        attrNode.Items(equip.mainAttrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            var value = data.value, canShowRange = showRange && data.base.length > 0;
            if (!!data.todayAdd) {
                value += serverRunDay * data.todayAdd;
            }
            it.Child('val', cc.Label).string = '+' + value;
            // 额外添加的
            var add = value - data.initValue;
            if ((_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(!!add)) {
                it.Child('add/0', cc.Label).string = '(' + data.initValue;
                var addLbl = it.Child('add/1', cc.Label);
                if (addLbl.setActive(!canShowRange)) {
                    addLbl.string = '+' + add;
                }
            }
            // 随机范围
            if ((_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(canShowRange)) {
                data.base.forEach(function (v, i) { return it.Child('base/' + i, cc.Label).string = v + ''; });
            }
        });
        // 效果
        var effectCount = equip.effects.length;
        if (effectNode.active = effectCount > 0) {
            var mult_1 = effectCount > 1;
            var effects = equip.effects, smeltEffectMap_1 = null, effectIdMap_1 = null;
            // 如果有锁定和融炼 排个序
            if (lockEffect || smeltEffects) {
                smeltEffectMap_1 = {};
                smeltEffects.forEach(function (m) { return smeltEffectMap_1[m.type] = m.id; });
                effects = effects.slice().sort(function (a, b) {
                    var aw = smeltEffectMap_1[a.type] ? 1 : 0, bw = smeltEffectMap_1[b.type] ? 1 : 0;
                    aw = aw * 10 + Number(a.type === lockEffect);
                    bw = bw * 10 + Number(b.type === lockEffect);
                    return aw - bw;
                });
            }
            if (smeltEffects && equip.isExclusive()) {
                effectIdMap_1 = {};
                GameHelper_1.gameHpr.world.getExclusiveEquipEffects(equip.id).forEach(function (m) { return effectIdMap_1[m] = true; });
            }
            effectNode.Items(effects, function (it, data) {
                var descParams = data.getDescParams(showRange), smeltId = smeltEffectMap_1 === null || smeltEffectMap_1 === void 0 ? void 0 : smeltEffectMap_1[data.type];
                if (smeltId) { // 融炼词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_smelt_equip_' + Number(!!effectIdMap_1[data.type]), assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'), 'equipText.name_' + smeltId);
                }
                else if (data.type === lockEffect) { // 锁定词条
                    it.Color('#C2B3A1');
                    it.setLocaleKey('ui.yet_lock_equip', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)).replace(/#000001/g, '#C2B3A1'));
                }
                else if (mult_1) { // 正常词条
                    it.Color('#756963');
                    it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang.apply(assetsMgr, __spread([data.name], descParams)));
                }
                else {
                    it.Color('#756963');
                    it.setLocaleKey.apply(it, __spread([data.name], descParams));
                }
            });
        }
        // 技能强化效果
        if (skillIntensifyNode === null || skillIntensifyNode === void 0 ? void 0 : skillIntensifyNode.setActive(!!equip.skillIntensify && equip.skillIntensify.length > 0)) {
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + equip.skillIntensify[0] + "_" + equip.skillIntensify[1]);
        }
    };
    // 刷新装备基础信息显示
    ViewHelper.prototype.updateEquipBaseView = function (node, json, from) {
        var _this = this;
        var _a;
        var nameNode = node.Child('name') || node.Child('icon/name');
        nameNode.setLocaleKey('equipText.name_' + json.id);
        var exclusive = node.Child('exclusive') || node.Child('icon/exclusive');
        if (exclusive === null || exclusive === void 0 ? void 0 : exclusive.setActive(!!json.exclusive_pawn)) {
            exclusive.setLocaleKey('ui.exclusive_pawn_desc', 'pawnText.name_' + json.exclusive_pawn);
        }
        var noForge = node.Child('no_forge') || node.Child('icon/no_forge');
        noForge === null || noForge === void 0 ? void 0 : noForge.setActive(true);
        // 属性
        var attrNode = node.Child('attrs/attr'), attrs = this.getEquipAttrs(json);
        attrNode.Items(attrs, function (it, data) {
            var _a, _b;
            it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
            it.Child('val', cc.Label).string = _this.wrapEquipVal(data.val);
            (_a = it.Child('add')) === null || _a === void 0 ? void 0 : _a.setActive(false);
            (_b = it.Child('base')) === null || _b === void 0 ? void 0 : _b.setActive(false);
        });
        // 效果
        var effectIds = ut.stringToNumbers(json.effect, '|');
        if (!!json.exclusive_pawn) {
            effectIds = GameHelper_1.gameHpr.world.getExclusiveEquipEffects(json.id);
        }
        var effects = effectIds.map(function (m) { return assetsMgr.getJsonData('equipEffect', m); }).sort(function (a, b) { return a.sort - b.sort; });
        var effectsNode = node.Child('attrs/effects'), moreDescNode = node.Child('attrs/more_desc'), moreEffectsNode = node.Child('attrs/more_effects'), skillIntensifyNode = node.Child('attrs/skill_intensify'), moreTipNode = node.Child('attrs/more_tip'), viewAttrBtn = node.Child('attrs/view_attr_be');
        // 多个的处理
        if (from === 'book') {
            moreDescNode.active = moreEffectsNode.active = effects.length > 1;
            if (moreEffectsNode.active) {
                moreDescNode.setLocaleKey('ui.equip_more_effect_desc', json.effect_count);
                moreEffectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, true); });
            }
        }
        else {
            moreDescNode.active = moreEffectsNode.active = false;
            var isMore = effects.length > 1;
            moreTipNode.active = viewAttrBtn.active = isMore;
            moreTipNode.Items(2, function (it, data, i) {
                it.setLocaleKey('ui.get_exclusive_attr', [i + 1]);
            });
        }
        // 单个的处理
        if (effectsNode.active = !!effects.length && !moreEffectsNode.active && !(viewAttrBtn === null || viewAttrBtn === void 0 ? void 0 : viewAttrBtn.active)) {
            effectsNode.Items(effects, function (it, m) { return _this.updateEquipBaseEffect(it, m, false); });
        }
        // 技能强化效果
        if (skillIntensifyNode.active = !!json.skill_intensify) {
            var _b = __read(ut.stringToNumbers(json.skill_intensify, ','), 2), a = _b[0], b = _b[1];
            skillIntensifyNode.setLocaleKey("pawnSkillText.intensify_desc_" + a + "_" + b);
        }
        // 空属性
        (_a = node.Child('attrs/empty_effect')) === null || _a === void 0 ? void 0 : _a.setActive(!effects.length);
    };
    ViewHelper.prototype.wrapEquipVal = function (arr) {
        return arr[0] === arr[1] ? (arr[0] + '') : "[" + arr.join('-') + "]";
    };
    ViewHelper.prototype.getEquipAttrs = function (data) {
        var arr = [];
        if (data.hp) {
            arr.push({ type: 1, val: ut.stringToNumbers(data.hp, ',') });
        }
        if (data.attack) {
            arr.push({ type: 2, val: ut.stringToNumbers(data.attack, ',') });
        }
        return arr;
    };
    ViewHelper.prototype.updateEquipBaseEffect = function (it, json, isMore) {
        var params = [], _a = __read(json.value.split(','), 2), a = _a[0], b = _a[1];
        if (json.id === Enums_1.EquipEffectType.MINGGUANG_ARMOR || json.id === Enums_1.EquipEffectType.BAIBI_SWORD) {
            params.push('0');
        }
        else if (!json.value) {
            params.push('');
        }
        else if (a === b) {
            params.push("" + a + json.suffix);
        }
        else {
            params.push("[" + a + "-" + b + "]" + json.suffix);
        }
        if (json.odds) {
            params.push("[" + ut.stringToNumbers(json.odds, ',').join('-') + "]%");
        }
        it.Color('#756963');
        if (isMore) {
            it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang('equipText.effect_' + json.id, params));
        }
        else {
            it.setLocaleKey('equipText.effect_' + json.id, params);
        }
    };
    // 刷新宠物信息
    ViewHelper.prototype.updatePetView = function (it, id, lv) {
        it.Child('name').setLocaleKey('pawnText.name_' + id);
        var lvNode = it.Child('name/lv');
        if (lvNode.active = !!lv) {
            lvNode.setLocaleKey('ui.lv', lv);
        }
        it.Child('none').active = !lv;
        this.updatePetAttrView(it.Child('attrs'), id, lv);
    };
    ViewHelper.prototype.updatePetAttrView = function (node, id, lv) {
        if (node.active = !!lv) {
            var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv);
            var mainAttrs = [{ type: 1, value: attrJson.hp }, { type: 2, value: attrJson.attack }];
            // 属性
            node.Child('attr').Items(mainAttrs, function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val', cc.Label).string = '' + data.value;
            });
            // 技能
            var skillNode = node.Child('skill');
            if (skillNode.active = !!attrJson.skill) {
                skillNode.Items(ut.stringToNumbers(attrJson.skill), function (node, data) {
                    var json = assetsMgr.getJsonData('pawnSkill', data);
                    var text = assetsMgr.lang(json.desc, json.desc_params.split('|'));
                    node.setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang(json.name) + "</c>", text);
                });
            }
        }
    };
    // 刷新玩家简介
    ViewHelper.prototype.updatePlayerPersonalDesc = function (node, uid, plr) {
        var personalDescLbl = node.Component(cc.Label);
        personalDescLbl.Color('#B6A591').string = '...';
        GameHelper_1.gameHpr.getUserPersonalDesc(uid, plr).then(function (val) {
            if (personalDescLbl.isValid) {
                personalDescLbl.Color(val ? '#756963' : '#B6A591').string = val || assetsMgr.lang('ui.empty_personal_desc');
            }
        });
    };
    // 刷新称号显示
    ViewHelper.prototype.updatePlayerTitleText = function (node, uid, key, plr) {
        if (!node) {
            return;
        }
        GameHelper_1.gameHpr.getUserTitle(uid, plr).then(function (title) {
            if (node.isValid) {
                var json = assetsMgr.getJsonData('title', title);
                var iconNode = node.Child('icon');
                if (iconNode === null || iconNode === void 0 ? void 0 : iconNode.setActive(!!json)) {
                    ResHelper_1.resHelper.loadTitleMiniIcon(json.icon, iconNode, key);
                }
                node.Child('val').Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey(json ? 'titleText.' + json.id : 'ui.nought');
            }
        });
    };
    // 刷新人气显示
    ViewHelper.prototype.updatePlayerPopularity = function (root, buttonNode, uid, key, plr) {
        var _a, _b;
        var button = (_b = (_a = buttonNode.Swih(uid === GameHelper_1.gameHpr.getUid() ? 'popularity_record_be' : 'add_popularity_be')) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.Component(cc.Button);
        if (button) {
            button.node.opacity = 120;
            button.interactable = false;
        }
        root.Items(1, function (it, _) {
            ResHelper_1.resHelper.loadGiftIcon(101, it.Child('icon'), key);
            it.Child('count').active = false;
            it.Child('loading').active = true;
        });
        GameHelper_1.gameHpr.getUserPopularity(uid, plr).then(function (info) {
            var _a;
            if (!root.isValid) {
                return;
            }
            else if (!((_a = info === null || info === void 0 ? void 0 : info.list) === null || _a === void 0 ? void 0 : _a.length)) {
                info = { list: [[101, 0]] };
            }
            if (button === null || button === void 0 ? void 0 : button.isValid) {
                button.node.opacity = 255;
                button.interactable = true;
            }
            root.Items(info.list, function (it, _a) {
                var _b = __read(_a, 2), id = _b[0], count = _b[1];
                it.Child('count').active = true;
                it.Child('loading').active = false;
                ResHelper_1.resHelper.loadGiftIcon(id, it.Child('icon'), key);
                it.Child('count', cc.Label).string = count + '';
            });
        });
    };
    // 刷新段位
    ViewHelper.prototype.updatePlayerRankInfo = function (node, uid, key, plr) {
        var icon = node.Child('icon'), loading = node.Child('loading');
        icon.active = false;
        loading.active = true;
        node.Child('rank').setLocaleKey('');
        node.Child('rank_val').setLocaleKey('');
        node.Child('ranked_val', cc.Label).string = '-';
        GameHelper_1.gameHpr.getUserRankScore(uid, plr).then(function (data) {
            if (node.isValid) {
                var _a = GameHelper_1.gameHpr.resolutionRankScore(data.score, data.count), id = _a.id, winPoint = _a.winPoint;
                loading.active = false;
                icon.active = true;
                ResHelper_1.resHelper.loadRankScoreIcon(id, icon, key).then(function (m) { return icon.isValid && icon.adaptScale(cc.size(100, 100)); });
                node.Child('rank').setLocaleKey(id >= 0 ? 'ui.rank_name_' + id : 'ui.rank_name_none');
                node.Child('rank_val').setLocaleKey('ui.rank_score_num', winPoint);
                node.Child('ranked_val', cc.Label).string = data.count + '';
            }
        });
    };
    // 刷新总局数
    ViewHelper.prototype.updateTotalGameCount = function (node, uid, plr) {
        var valLbl = node.Child('val', cc.Label), winRate = node.Child('win_rate');
        valLbl.string = '-';
        winRate.active = false;
        GameHelper_1.gameHpr.getUserTotalGameCount(uid, plr).then(function (info) {
            if (node.isValid) {
                // winRate.active = true
                var _a = __read(info, 2), win = _a[0], total = _a[1];
                valLbl.string = '' + (total || 0);
                // winRate.setLocaleKey('ui.win_rate', total ? Math.floor(win / total * 100) : 0)
            }
        });
    };
    // 在主场景显示建筑信息
    ViewHelper.prototype.showBuildInfoByMain = function (id, params) {
        return __awaiter(this, void 0, void 0, function () {
            var ui, area, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ui = Constant_1.FIXATION_MENU_CONFIG[id];
                        if (!ui) {
                            return [2 /*return*/, false];
                        }
                        this.showNetWait(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.areaCenter.reqAreaByIndex(GameHelper_1.gameHpr.player.getMainCityIndex())];
                    case 1:
                        area = _a.sent();
                        this.showNetWait(false);
                        data = area === null || area === void 0 ? void 0 : area.getBuildsById(id)[0];
                        if (!data) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.showPnl(ui, data, params)];
                    case 2:
                        _a.sent();
                        return [2 /*return*/, true];
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    // 显示金币不足
    ViewHelper.prototype.showGoldNotEnough = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.isNoviceMode) {
            this.showAlert('ecode.500053');
            return;
        }
        this.showMessageBox('ui.gold_not_enough_tip', {
            okText: 'ui.button_exchange',
            cancelText: 'ui.button_no',
            ok: function () { return _this.showBuyGoldTipPnl(); },
            cancel: function () { }
        });
    };
    // 初始化转盘界面
    ViewHelper.prototype.initWheelItem = function (it, data, descColor) {
        var items = [];
        if (data.factor > 0) {
            it.Data = data;
            var runDay = GameHelper_1.gameHpr.user.getWheelInRoomRunDay();
            if (runDay > 0) {
                var count = Math.floor(30 * Math.min(runDay, 10) * data.factor);
                items = count > 0 ? [new CTypeObj_1.default().init(Enums_1.CType.CEREAL, 0, count), new CTypeObj_1.default().init(Enums_1.CType.TIMBER, 0, count), new CTypeObj_1.default().init(Enums_1.CType.STONE, 0, count)] : [];
            }
        }
        else if (data.factor === -1) {
            it.Data = data;
            items = GameHelper_1.gameHpr.user.getWheelRandomAwards();
        }
        else {
            it.Data = null;
            items = GameHelper_1.gameHpr.stringToCTypes(data.award);
        }
        this.updateItemByCTypes(it.Child('award'), items);
        it.Child('loading').active = !!it.Data && !items.length;
        it.Child('desc').Color(descColor).setLocaleKey(items.length === 0 ? 'ui.empty' : '');
    };
    // 打开关闭弹出框
    ViewHelper.prototype.changePopupBoxList = function (node, val, isDown) {
        node.Child('select_mask_be').active = val;
        var mask = node.Child('mask'), root = mask.Child('root');
        if (val) {
            mask.active = true;
            root.y = isDown ? mask.height : -mask.height;
            var y = isDown ? -4 : 4;
            cc.tween(root).to(0.15, { y: y }, { easing: cc.easing.sineOut }).start();
        }
        else {
            root.y = isDown ? -4 : 4;
            var y = isDown ? mask.height : -mask.height;
            cc.tween(root).to(0.1, { y: y }).call(function () { return mask.active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start();
    };
    ViewHelper.prototype.closePopupBoxList = function (node) {
        node.Child('select_mask_be').active = false;
        node.Child('mask').active = false;
        node.Child('icon').angle = 0;
    };
    // 显示不再提示
    ViewHelper.prototype.showNoLongerTip = function (key, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTip(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/NoLongerTip', data);
            return true;
        }
        return false;
    };
    // 显示不再提示
    ViewHelper.prototype.showResFullNoLongerTip = function (key, items, data) {
        if (!GameHelper_1.gameHpr.isNoLongerTipBySid(key)) {
            data.noKey = key;
            data.okText = data.okText || 'ui.button_gotit';
            this.showPnl('common/ResFullNoLongerTip', items, data);
            return true;
        }
        return false;
    };
    // 添加人气
    ViewHelper.prototype.addPlayerPopularity = function (data, cb) {
        var info = data.popularityInfo;
        if (info === null || info === void 0 ? void 0 : info.reqing) {
            return;
        }
        else if (info && info.records.length > 0) {
            var lastTime_1 = 0, d_1 = null, uid_1 = GameHelper_1.gameHpr.getUid();
            info.records.forEach(function (m) {
                if (m.time > lastTime_1) {
                    lastTime_1 = m.time;
                }
                if (m.uid === uid_1) {
                    d_1 = m;
                }
            });
            var now = Date.now();
            if (d_1 && now - d_1.time < Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL) {
                var day = Math.max(1, Math.floor((Constant_1.ONE_USER_POPULARITY_CHANGE_INTERVAL - (now - d_1.time)) / ut.Time.Day));
                return this.showMessageBox('ui.month_add_popularity_tip', { params: [day], okText: 'ui.button_gotit' });
            }
            else if (lastTime_1 > ut.dateZeroTime(GameHelper_1.gameHpr.getServerNowTime())) {
                return this.showMessageBox('ui.today_add_popularity_tip', { okText: 'ui.button_gotit' });
            }
        }
        this.showPnl('common/AddPopularityTip', data, cb);
    };
    // 刷新图鉴心
    ViewHelper.prototype.updateBookStar = function (node, star) {
        var val = star * 0.5;
        node.children.forEach(function (m, i) {
            if (val >= 1) {
                val -= 1;
                m.Component(cc.MultiFrame).setFrame(2);
            }
            else if (val >= 0.5) {
                val -= 0.5;
                m.Component(cc.MultiFrame).setFrame(1);
            }
            else {
                m.Component(cc.MultiFrame).setFrame(0);
            }
        });
    };
    // 显示画像名字
    ViewHelper.prototype.showPortrayalName = function (node, name, vice) {
        node.Child('val').setLocaleKey(name);
        var viceNode = node.Child('vice');
        if (viceNode.active = !!vice) {
            viceNode.setLocaleKey('ui.bracket', vice);
        }
    };
    // 显示立绘
    ViewHelper.prototype.updatePicture = function (id, isUnlock, iconNode, offset, hasAnim, key) {
        var valNode = iconNode.Child('val');
        var anim = valNode.Component(FrameAnimationCmpt_1.default);
        anim === null || anim === void 0 ? void 0 : anim.clean();
        ResHelper_1.resHelper.loadPortrayalImage(id, valNode, key);
        iconNode.setPosition(offset);
        valNode.opacity = isUnlock ? 255 : 100;
        if (isUnlock) {
            iconNode.Component(cc.Sprite).spriteFrame = null;
        }
        else {
            ResHelper_1.resHelper.loadPortrayalImage(id, iconNode, key);
        }
        if (anim && isUnlock && hasAnim) {
            anim.init('portrayal_' + id, key).then(function () {
                if (valNode.isValid) {
                    anim.play('standby');
                }
            });
        }
    };
    // 刷新画像碎片数量
    ViewHelper.prototype.updatePortrayalDebrisCount = function (it, debris) {
        var isCanComp = debris >= Constant_1.PORTRAYAL_COMP_NEED_COUNT;
        it.Child('debris_count/val', cc.Label).Color(isCanComp ? cc.Color.GREEN : cc.Color.WHITE).string = debris + '';
        it.Child('debris_count').Color(isCanComp ? '#FFA647' : cc.Color.GRAY);
    };
    // 显示获得画像
    ViewHelper.prototype.showGainPortrayalDebris = function (id, count) {
        this.showPnl('common/GetPortrayal', id, count);
    };
    // 刷新英雄属性
    ViewHelper.prototype.updatePortrayalAttr = function (node, data, isHero) {
        var _this = this;
        var root = node.Child('attrs');
        //
        root.Child('avatar/val').setLocaleKey(data.avatarPawnName);
        // 属性
        root.Child('attr').Items(this.getPortrayalMainAttrs(data, isHero), function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = d.value;
        });
        // 技能
        var id = data.json.skill;
        root.Child('skill').setLocaleKey('ui.res_transit_cap_desc', "<color=#333333>" + assetsMgr.lang('portrayalSkillText.name_' + id) + "</c>", assetsMgr.lang('portrayalSkillText.desc_' + id, this.getPortrayalSkillDescParams(data, isHero)));
        // 韬略
        var strategysNode = root.Child('strategys');
        var showStrategy = root.Child('strategy').active = strategysNode.active = isHero && data.strategys.length > 0;
        if (showStrategy) {
            root.Child('strategy/name/count', cc.Label).string = "(" + data.strategys.length + ")";
            strategysNode.Items(data.strategys, function (it, strategy) { return _this.showStrategyText(it, strategy, data.avatarPawnName); });
        }
    };
    // 显示韬略文本
    ViewHelper.prototype.showStrategyText = function (it, strategy, avatarPawnName) {
        it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang(strategy.desc, strategy.getDescParamsRange(avatarPawnName, 1, '#' + it.color.toHEX('#rrggbb'))));
    };
    ViewHelper.prototype.getPortrayalMainAttrs = function (data, isHero) {
        if (isHero && data.mainAttrs.length > 0) {
            return data.mainAttrs.map(function (m) { return { type: m.type, value: '+' + m.value }; });
        }
        return ['hp', 'attack'].map(function (k, i) { return { type: i + 1, value: '[' + data.json[k].replace(',', '-') + ']' }; });
    };
    // 获取说明参数
    ViewHelper.prototype.getPortrayalSkillDescParams = function (data, isHero) {
        if (isHero && data.skill) {
            return data.skill.getDescParams();
        }
        var json = assetsMgr.getJsonData('portrayalSkill', data.json.skill);
        var arr = [];
        if (json.value) {
            arr.push('[' + json.value.replace(',', '-') + ']' + json.suffix);
        }
        if (json.target) {
            arr.push(json.target);
        }
        return arr;
    };
    // 刷新英雄简短属性
    ViewHelper.prototype.updatePortrayalShortAttr = function (node, attrs, avatarPawnName) {
        var mainAttrs = [], skill = null, strategys = [];
        attrs.forEach(function (m) {
            var _a = __read(m.attr, 3), fieldType = _a[0], type = _a[1], value = _a[2];
            if (fieldType === 0) { //属性
                mainAttrs.push({ type: type, value: value });
            }
            else if (fieldType === 1) { //技能
                skill = new PortrayalSkillObj_1.default().init(type, value);
            }
            else if (fieldType === 2) { //韬略
                strategys.push(new StrategyObj_1.default().init(type));
            }
        });
        // 属性
        node.Child('attr').Items(mainAttrs, function (it, d) {
            it.Child('icon', cc.MultiFrame).setFrame(d.type - 1);
            it.Child('val', cc.Label).string = '+' + d.value;
        });
        // 技能
        var skillNode = node.Child('skill');
        if (skillNode.active = !!skill) {
            skillNode.setLocaleKey(skill.desc, skill.getDescParams());
        }
        // 韬略
        node.Child('strategy/name/count', cc.Label).string = "(" + strategys.length + ")";
        node.Child('strategys').Items(strategys, function (it, strategy) { return exports.viewHelper.showStrategyText(it, strategy, avatarPawnName); });
    };
    // 返回大厅
    ViewHelper.prototype.backLobby = function () {
        var _this = this;
        GameHelper_1.gameHpr.resetSelectServer(true).then(function (res) { return _this.gotoWind('lobby'); });
    };
    // 显示英雄自选
    ViewHelper.prototype.showHeroOptSelect = function (lv) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var arr = Constant_1.HERO_OPT_GIFT[lv];
                        var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
                        _this.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.GIFT, list, function (arr) { var _a, _b; return resolve((_b = (_a = arr[0]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0); }, lv);
                    })];
            });
        });
    };
    // 获取状态背景颜色
    ViewHelper.prototype.getHeroStateBgColor = function (state) {
        if (state === 0) {
            return '#FFFFFF';
        }
        else if (state === 1) {
            return '#21DE29';
        }
        else if (state === 2) {
            return '#FFFFFF';
        }
        return '#F45757';
    };
    // 显示申请好友
    ViewHelper.prototype.showApplyFriend = function (data) {
        var _this = this;
        this.showMessageBox('ui.apply_friend_tip', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.applyFriend(data.uid).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                _this.showAlert('toast.apply_friend_succeed');
            }); },
            cancel: function () { }
        });
    };
    // 显示拉黑
    ViewHelper.prototype.showBlacklist = function (data, event, buttonsNode) {
        var _this = this;
        var state = GameHelper_1.gameHpr.friend.isInBlacklist(data.uid);
        this.showMessageBox(state ? 'ui.cancel_blacklist_desc' : 'ui.add_blacklist_desc', {
            params: [ut.nameFormator(data.nickname, 8)],
            ok: function () { return GameHelper_1.gameHpr.friend.doBlacklist(data.uid, state).then(function (err) {
                if (err) {
                    return _this.showAlert(err);
                }
                else if (buttonsNode.isValid) {
                    event.target.Child('val', cc.MultiFrame).setFrame(!state);
                    buttonsNode.Child('add_friend_be').active = state && !GameHelper_1.gameHpr.friend.isFriend(data.uid);
                }
                _this.showAlert(state ? 'toast.cancel_blacklist_succeed' : 'toast.add_blacklist_succeed');
            }); },
            cancel: function () { }
        });
    };
    ViewHelper.prototype.showFeedback = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isGLobal()) {
                    return [2 /*return*/, this.showPnl('login/HDFeedback')];
                }
                else {
                    return [2 /*return*/, this.showPnl('login/Feedback')];
                }
                return [2 /*return*/];
            });
        });
    };
    // 查看奖励详情
    ViewHelper.prototype.previewRewardDetail = function (reward) {
        if (reward.type === Enums_1.CType.PAWN_SKIN) {
            exports.viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [assetsMgr.getJsonData('pawnSkin', reward.id)] });
        }
        else if (reward.type === Enums_1.CType.HERO_DEBRIS) {
            var json = assetsMgr.getJsonData('portrayalBase', reward.id);
            this.showPnl('common/PortrayalBaseInfo', json, 'shop');
        }
        else if (reward.type === Enums_1.CType.HERO_OPT) {
            var id = reward.id, arr = Constant_1.HERO_OPT_GIFT[id];
            var list = arr ? arr.map(function (m) { return new PortrayalInfo_1.default().init(m); }) : assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
            this.showPnl('common/SelectPortrayalPreview', id, list);
        }
    };
    // 通过deeplink打开相应游戏界面
    ViewHelper.prototype.openUIByDeepLink = function () {
        var data = GameHelper_1.gameHpr.getEnterQuery();
        if (data === null || data === void 0 ? void 0 : data.openUI) {
            var url = data.openUI.replace('_', '/'), params = data.params.split('_');
            this.showPnl.apply(this, __spread([url], params));
        }
    };
    // 刷新士兵属性
    ViewHelper.prototype.updatePawnAttrs = function (node, pawn) {
        node.Child('hp/val', cc.Label).string = pawn.getHpText();
        node.Child('anger/val', cc.Label).string = pawn.getAngerText();
        node.Child('attack/val', cc.Label).string = pawn.getAttackText();
        node.Child('attack_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getAttackRange());
        node.Child('move_range/val', cc.Label).setLocaleKey('ui.range_desc', pawn.getMoveRange());
        if (node.Child('cereal_c').active = !!pawn.baseJson.cereal_cost) {
            node.Child('cereal_c/val', cc.Label).string = pawn.baseJson.cereal_cost;
        }
    };
    // 刷新士兵技能
    ViewHelper.prototype.updatePawnSkills = function (node, pawn, key) {
        var _a, _b;
        // 技能
        var skillNode = node.Child('skills'), portrayalSkill = node.Child('portrayal_skill_be');
        if (skillNode.active = pawn.skills.length > 0) {
            var skills_1 = [];
            pawn.skills.forEach(function (m) {
                if (m.type < Enums_1.PawnSkillType.RESTRAIN) {
                    var d = skills_1.find(function (s) { return s.type === Enums_1.PawnSkillType.RESTRAIN; });
                    if (!d) {
                        d = skills_1.add({ id: 101, useType: m.use_type, type: Enums_1.PawnSkillType.RESTRAIN, name: m.json.name, descs: [], desc_params: [] });
                    }
                    d.descs.push(m.json.desc);
                    d.desc_params.push(String(m.json.desc_params).split('|'));
                }
                else if (m.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getAttackText(), m.json.desc_params]] });
                }
                else if (m.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [pawn.getAttackTextByIndex(2)] });
                }
                else if (m.type === Enums_1.PawnSkillType.SKILL_217) {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[m.json.desc_params, pawn.getMoveRange()]] });
                }
                else if (m.type === Enums_1.PawnSkillType.CADET) {
                    skills_1.add({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [[pawn.getCadetLvText(), m.json.desc_params, 'pawnText.name_4205']] });
                }
                else {
                    skills_1.push({ id: m.baseId, useType: m.use_type, type: m.type, name: m.json.name, descs: [m.json.desc], desc_params: [String(m.json.desc_params).split('|')] });
                }
            });
            skillNode.Items(skills_1, function (it, skill) {
                it.Data = skill;
                ResHelper_1.resHelper.loadSkillIcon(skill.id, it.Child('val'), key);
            });
        }
        // 英雄技能
        if (portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.setActive(!!((_b = (_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id))) {
            ResHelper_1.resHelper.loadHeroSkillIcon(pawn.portrayal.skill.id, portrayalSkill.Child('val'), key);
        }
        return skillNode.active || !!(portrayalSkill === null || portrayalSkill === void 0 ? void 0 : portrayalSkill.active);
    };
    // 刷新军队状态
    ViewHelper.prototype.updateArmyState = function (node, data, march, isHasLving, isMe) {
        var _this = this;
        var states = [], tonden = data instanceof ArmyObj_1.default ? GameHelper_1.gameHpr.world.getArmyTondenInfo(data.index, data.uid) : data.tonden;
        if (data.drillPawns.length > 0) {
            states.push(Enums_1.ArmyState.DRILL);
        }
        if (data.curingPawns.length > 0) {
            states.push(Enums_1.ArmyState.CURING);
        }
        if (isHasLving) {
            states.push(Enums_1.ArmyState.LVING);
        }
        if (tonden) {
            states.push(Enums_1.ArmyState.TONDEN);
        }
        if (data.state !== Enums_1.ArmyState.NONE || states.length === 0) {
            states.unshift(data.state);
        }
        var scroll = node.Child('stateScroll'), player = GameHelper_1.gameHpr.player;
        scroll.Child('state').Items(states, function (it, state) {
            var color = Constant_1.ARMY_STATE_COLOR[state];
            it.Child('val').Color(color).setLocaleKey('ui.army_state_' + state);
            var timeLbl = it.Child('time', cc.LabelTimer);
            timeLbl.Color(color);
            if (!isMe) {
                timeLbl.setActive(false);
            }
            else if (state === Enums_1.ArmyState.MARCH && march) {
                var targetIndex_1 = march.targetIndex;
                timeLbl.setActive(true);
                timeLbl.run(march.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        data.index = targetIndex_1;
                        data.state = Enums_1.ArmyState.NONE;
                        data.treasures.forEach(function (m) { return m.index = data.index; });
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else if (state === Enums_1.ArmyState.DRILL || state === Enums_1.ArmyState.LVING || state === Enums_1.ArmyState.CURING) { //训练
                timeLbl.setActive(true);
                var _a = state === Enums_1.ArmyState.DRILL ? player.getSumDrillTimeByArmy(data.uid) : state === Enums_1.ArmyState.CURING ? player.getSumCuringTimeByArmy(data.uid) : player.getSumLvingTimeByArmy(data.uid), time = _a.time, pause = _a.pause;
                if (pause) {
                    timeLbl.string = ut.millisecondFormat(time, 'h:mm:ss');
                }
                else {
                    timeLbl.run(time * 0.001);
                }
                timeLbl.node.opacity = pause ? 128 : 255;
            }
            else if (state === Enums_1.ArmyState.TONDEN && tonden) { //屯田
                timeLbl.setActive(true);
                timeLbl.run(tonden.getSurplusTime() * 0.001, function () {
                    if (node.isValid) {
                        if (data instanceof ArmyObj_1.default) {
                        }
                        else {
                            data.tonden = null;
                        }
                        data.state = Enums_1.ArmyState.NONE;
                        _this.updateArmyState(node, data, march, isHasLving, isMe);
                    }
                });
                timeLbl.node.opacity = 255;
            }
            else {
                timeLbl.setActive(false);
            }
        });
    };
    // 刷新士兵费用系数
    ViewHelper.prototype.updatePawnCostFactor = function (node, id, json) {
        var _a, _b;
        var val = ((_a = GameHelper_1.gameHpr.getPawnCost(id, 0, json).find(function (m) { return m.type === Enums_1.CType.CEREAL; })) === null || _a === void 0 ? void 0 : _a.count) || 0;
        var factor = Math.round((val / Constant_1.PAWN_COST_FACTOR_BASE_VALUE) * 100), color = factor <= 125 ? '#4AB32E' : '#D7634D';
        node.Component(cc.Label).Color(color).string = factor + '%';
        (_b = node.Child('line')) === null || _b === void 0 ? void 0 : _b.Color(color);
    };
    // 刷新游戏对局历史记录
    ViewHelper.prototype.updateGameHistoryItem = function (it, data, _key) {
        it.Data = data;
        var _a = GameHelper_1.gameHpr.getServerNameById(data.id), key = _a.key, id = _a.id;
        it.Child('name/val').setLocaleKey(key, id);
        it.Child('time', cc.Label).string = ut.dateFormat('yyyy/MM/dd', data.createTime) + ' - ' + ut.dateFormat('yyyy/MM/dd', data.endTime);
        it.Child('win_cond').setLocaleKey('ui.win_cond_desc', 'ui.win_cond_' + data.winCondType);
        it.Child('winer/name', cc.Label).string = data.winName;
        var alli = it.Child('winer/alli'), mvp = it.Child('mvp'), leader = it.Child('leader'), lmvp = it.Child('lmvp');
        // mvp
        var mvpInfo = data.winners.sort(function (a, b) { return b.score - a.score; })[0], leaderInfo = null;
        // 联盟获胜
        if (alli.active = data.winType === 2) {
            var alliInfo = data.winAlliInfo;
            leaderInfo = data.winners.find(function (m) { return m.job === Enums_1.AllianceJobType.CREATER; });
            ResHelper_1.resHelper.loadAlliIcon((alliInfo === null || alliInfo === void 0 ? void 0 : alliInfo.headicon) || 0, alli.Child('val'), _key);
        }
        // 如果是同一个人 那么就是lmvp
        if (lmvp.active = !!mvpInfo && !!leaderInfo && (mvpInfo === null || mvpInfo === void 0 ? void 0 : mvpInfo.uid) === (leaderInfo === null || leaderInfo === void 0 ? void 0 : leaderInfo.uid)) {
            lmvp.Child('name', cc.Label).string = ut.nameFormator(leaderInfo.name, 7);
            ResHelper_1.resHelper.loadPlayerHead(lmvp.Child('head/val'), leaderInfo.headicon, _key);
        }
        // 盟主
        if (leader.active = data.winType === 2 && !lmvp.active && !!leaderInfo) {
            leader.Child('name', cc.Label).string = ut.nameFormator(leaderInfo.name, 5);
            ResHelper_1.resHelper.loadPlayerHead(leader.Child('head/val'), leaderInfo.headicon, _key);
        }
        // mvp
        if (mvp.active = !!mvpInfo && !lmvp.active) {
            mvp.x = leader.active ? 180 : 120;
            mvp.Child('name', cc.Label).string = ut.nameFormator(mvpInfo.name, leader.active ? 5 : 7);
            mvp.Child('name', AdaptWidthCmpt_1.default).setMaxWidth(leader.active ? 100 : 140);
            ResHelper_1.resHelper.loadPlayerHead(mvp.Child('head/val'), mvpInfo.headicon, _key);
        }
    };
    return ViewHelper;
}());
exports.viewHelper = new ViewHelper();
if (cc.sys.isBrowser) {
    window['viewHelper'] = exports.viewHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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