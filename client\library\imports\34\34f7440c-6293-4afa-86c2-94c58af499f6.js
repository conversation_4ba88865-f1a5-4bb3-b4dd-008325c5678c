"use strict";
cc._RF.push(module, '34f74QMYpNK+obClMWK9Jn2', 'ShopPnlCtrl');
// app/script/view/common/ShopPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LocalConfig_1 = require("../../common/LocalConfig");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var ccclass = cc._decorator.ccclass;
var ShopPnlCtrl = /** @class */ (function (_super) {
    __extends(ShopPnlCtrl, _super);
    function ShopPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://top_n
        _this.rootNode_ = null; // path://root_n
        _this.shopSv_ = null; // path://root_n/shop_sv
        _this.mysteryBoxNode_ = null; // path://root_n/shop_sv/view/content/mystery_box_n
        _this.freeGoldNode_ = null; // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
        _this.optionalHeroNode_ = null; // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
        _this.ingotNode_ = null; // path://root_n/shop_sv/view/content/ingot_n
        _this.recommendSv_ = null; // path://root_n/shop_sv/view/content/recommend/recommend_sv
        _this.cardNode_ = null; // path://root_n/shop_sv/view/content/card_n
        _this.restoreBuyNode_ = null; // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
        _this.limitedSkinNode_ = null; // path://root_n/shop_sv/view/content/limited_skin_n
        _this.citySkinNode_ = null; // path://root_n/shop_sv/view/content/city_skin_n
        _this.pawnSkinNode_ = null; // path://root_n/shop_sv/view/content/pawn_skin_n
        _this.newHeadNode_ = null; // path://root_n/shop_sv/view/content/new_head_n
        _this.classicalHeadNode_ = null; // path://root_n/shop_sv/view/content/classical_head_n
        _this.newEmojiNode_ = null; // path://root_n/shop_sv/view/content/new_emoji_n
        _this.classicalEmojiNode_ = null; // path://root_n/shop_sv/view/content/classical_emoji_n
        _this.skinTabsNode_ = null; // path://root_n/skin_tabs_n
        _this.socialTabsNode_ = null; // path://root_n/social_tabs_n
        _this.tabsTc_ = null; // path://root_n/tabs_tc_tce
        //@end
        // 推荐列表 type:对应配置表，id:对应配置表相应id
        _this.RECOMMENDED_LIST = [
            { type: 'head', id: 206 },
            { type: 'city', id: 1001111 },
            { type: 'pawn', id: 3103105 },
            { type: 'emoji', id: 2040 },
            { type: 'head', id: 190 },
            { type: 'city', id: 1001107 },
            { type: 'pawn', id: 3202101 },
            { type: 'emoji', id: 2050 },
            { type: 'head', id: 187 },
        ];
        _this.PKEY_TAB = 'SHOP_TAB';
        _this.goldValLbl = null;
        _this.ingotValLbl = null;
        _this.user = null;
        _this.curTab = 0;
        _this.showType = '';
        _this.mysteryBoxPrefab = null;
        _this.preViewHeight = 904;
        _this.drawCount = 0;
        _this.drawRate = 10;
        _this.frameCount = 0;
        _this.funcList = [
            _this.updateRecommend,
            _this.updateMonthlyCard,
            _this.updateHeadIcon,
            _this.updateChatEmoji,
        ];
        return _this;
    }
    ShopPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_INGOT] = this.onUpdateIngot, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.INIT_PAY_FINISH] = this.onInitPayFinish, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_SUBSCRIPTION] = this.onUpdateSubscripion, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_RECHARGE_COUNT] = this.onUpdateRechargeCount, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_MYSTERYBOX] = this.onUpdateMysteryBox, _f.enter = true, _f),
        ];
    };
    ShopPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var throttleUpdate;
            var _this = this;
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                this.user = this.getModel('user');
                this.loadMysteryBoxPrefab();
                // this.restoreBuyNode_.active = !gameHpr.isNoviceMode && (cc.sys.isBrowser || !gameHpr.isRelease)
                this.optionalHeroNode_.Child('button/money/val', cc.Label).string = Constant_1.BUY_OPT_HERO_COST + '';
                throttleUpdate = this.throttle(function (event) { return _this.onScrolling(event); }, 200);
                this.shopSv_.node.on('scrolling', throttleUpdate, this);
                return [2 /*return*/];
            });
        });
    };
    ShopPnlCtrl.prototype.onEnter = function (type, showType) {
        var _a;
        if (typeof (type) === 'string') {
            type = Number(type);
        }
        this.curTab = (_a = type !== null && type !== void 0 ? type : this.user.getTempPreferenceMap(this.PKEY_TAB)) !== null && _a !== void 0 ? _a : 0;
        this.showType = showType;
        this.restoreBuyNode_.active = ut.isIos() && LocalConfig_1.localConfig.RELEASE && !GameHelper_1.gameHpr.isRelease; // 苹果审核才显示
        this.drawCount = this.frameCount = 0;
        this.initTop();
        this.checkShowNotFinishOrder();
        this.updateShopIngot();
        // this.updateRecommend()
        // this.updateMonthlyCard()
        this.updateSkin();
        // this.updateHeadIcon()
        // this.updateChatEmoji()
        this.tabsTc_.Tabs(this.curTab);
        this.emit(EventType_1.default.HIDE_TOP_NODE, false);
        this.playRootAnimation();
    };
    ShopPnlCtrl.prototype.onRemove = function () {
        // this.topNode_.active = false
        this.emit(EventType_1.default.HIDE_TOP_NODE, true);
    };
    ShopPnlCtrl.prototype.onClean = function () {
        this.drawCount = this.frameCount = 0;
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/tabs_tc_tce
    ShopPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.curTab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var y = 0;
        if (type === 0) { // 元宝位置 0
            if (this.showType === 'ingot') {
                y = this.ingotNode_.y;
            }
            this.skinTabsNode_.active = this.socialTabsNode_.active = false;
        }
        else if (type === 1) { // 月卡位置 1
            y = this.cardNode_.y;
            this.skinTabsNode_.active = this.socialTabsNode_.active = false;
        }
        else if (type === 2) { // 皮肤位置 2
            this.skinTabsNode_.active = true;
            this.socialTabsNode_.active = false;
            y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y;
        }
        else if (type === 3) { // 新款头像位置 3
            this.skinTabsNode_.active = false;
            this.socialTabsNode_.active = true;
            y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/skin_tabs_n/skin_tabs_nbe
    ShopPnlCtrl.prototype.onClickSkinTabs = function (event, data) {
        var name = event.target.name;
        var y = 0;
        if (name === '0') { // 跳转限定
            y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y;
        }
        else if (name === '1') { // 跳转主城
            y = this.citySkinNode_.y;
        }
        else if (name === '2') { // 跳转士兵
            y = this.pawnSkinNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/social_tabs_n/social_tabs_nbe
    ShopPnlCtrl.prototype.onClickSocialTabs = function (event, data) {
        var name = event.target.name;
        var y = 0;
        if (name === '0') { // 跳转头像
            y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y;
        }
        else if (name === '1') { // 跳转表情
            y = this.newEmojiNode_.active ? this.newEmojiNode_.y : this.classicalEmojiNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
    ShopPnlCtrl.prototype.onClickFreeGold = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (!this.user.isBuyLimitFreeGold()) {
            return ViewHelper_1.viewHelper.showAlert('ui.yet_buy_day');
        }
        this.user.buyFreeGold().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateFreeGold();
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    // path://root_n/shop_sv/view/content/exchange/list/buy_gold_be
    ShopPnlCtrl.prototype.onClickBuyGold = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/ShopBuyGoldTip');
    };
    // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
    ShopPnlCtrl.prototype.onClickOptionalHero = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (!this.user.isBuyLimitOptionalHero()) {
            return ViewHelper_1.viewHelper.showAlert('ui.yet_buy_week');
        }
        var list = assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
        ViewHelper_1.viewHelper.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.BUY, list, function (arr) {
            if (_this.isValid && arr.length > 0) {
                _this.buyHero(arr[0].id);
            }
        }, 3);
    };
    // path://root_n/shop_sv/view/content/ingot_n/title/pay_not_arrived_be
    ShopPnlCtrl.prototype.onClickPayNotArrived = function (event, data) {
        ViewHelper_1.viewHelper.showLoadingWait(true);
        this.checkShowNotFinishOrder(true).then(function () { return ViewHelper_1.viewHelper.showLoadingWait(false); });
    };
    // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
    ShopPnlCtrl.prototype.onClickRestoreBuy = function (event, data) {
        PayHelper_1.payHelper.restoreBuySub();
    };
    // path://root_n/shop_sv/view/content/ingot_n/list/ingot_be
    ShopPnlCtrl.prototype.onClickIngot = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_ingot_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        var json = event.target.Data;
        ViewHelper_1.viewHelper.showPnl('common/ShopBuyIngotTip', json, function (ok) {
            if (ok) {
                PayHelper_1.payHelper.buyProduct(json.product_id).then(function (suc) {
                    logger.print('6.buyProduct end. suc=' + suc + ', isValid=' + _this.isValid);
                    if (suc && _this.isValid) {
                        _this.updateShopIngot();
                    }
                });
            }
        });
    };
    // path://root_n/shop_sv/view/content/recommend/recommend_sv/view/content/buy_be@recommend
    ShopPnlCtrl.prototype.onClickBuy = function (event, param) {
        var _this = this;
        var data = event.target.Data;
        if (!data) {
            return;
        }
        var buyType = '';
        if (param === 'recommend') { // 推荐
            buyType = data.type;
            data = data.json;
        }
        if (param === 'city' || buyType === 'city' || param === 'pawn' || buyType === 'pawn') { // 士兵、城市皮肤
            var type_1 = param === 'city' || buyType === 'city' ? 'city_skin' : 'pawn_skin';
            ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: type_1, list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                    return;
                }
                if (type_1 === 'pawn_skin') {
                    _this.buyPawnSkin(ret.id);
                }
                else if (type_1 === 'city_skin') {
                    _this.buyCitySkin(ret.id);
                }
            });
        }
        else if (param === 'head' || buyType === 'head' || param === 'emoji' || buyType === 'emoji') { // 头像
            var type_2 = param === 'head' || buyType === 'head' ? 'headicon' : 'chat_emoji';
            ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: type_2, list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                    return;
                }
                if (type_2 === 'headicon') {
                    _this.buyHeadIcon(ret.id);
                }
                else if (type_2 === 'chat_emoji') {
                    _this.buyChatEmoji(ret.id);
                }
            });
        }
    };
    // path://root_n/shop_sv/view/content/card_n/list/card_0/sale_card_nbe
    ShopPnlCtrl.prototype.onClickSaleCard = function (event, _data) {
        var name = event.target.name, data = event.target.Data;
        if (name === '0') { // 订阅
            if (data && data.leftDays === 0 && data.surplusTime > 0) {
                ViewHelper_1.viewHelper.showAlert('toast.subscription_not_end');
            }
            else {
                ViewHelper_1.viewHelper.showPnl('common/SubscriptionDesc', Enums_1.MonthlyCardType.SALE);
            }
        }
        else if (name === '1') { // 领取
            this.getMonthlyCardAward(Enums_1.MonthlyCardType.SALE);
        }
        else if (name === '2') { // 已领取
        }
    };
    // path://root_n/shop_sv/view/content/card_n/list/card_1/super_card_nbe
    ShopPnlCtrl.prototype.onClickSuperCard = function (event, _data) {
        var name = event.target.name, data = event.target.Data;
        if (name === '0') { // 订阅
            if (data && data.leftDays === 0 && data.surplusTime > 0) {
                ViewHelper_1.viewHelper.showAlert('toast.subscription_not_end');
            }
            else {
                ViewHelper_1.viewHelper.showPnl('common/SubscriptionDesc', Enums_1.MonthlyCardType.SUPER);
            }
        }
        else if (name === '1') { // 领取
            this.getMonthlyCardAward(Enums_1.MonthlyCardType.SUPER);
        }
        else if (name === '2') { // 已领取
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新元宝
    ShopPnlCtrl.prototype.onUpdateIngot = function () {
        var ingot = this.user.getIngot();
        this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.to(ingot);
    };
    // 刷新元宝
    ShopPnlCtrl.prototype.onUpdateGold = function () {
        var gold = this.user.getGold();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.to(gold);
    };
    // 初始化支付完成 刷新下金币
    ShopPnlCtrl.prototype.onInitPayFinish = function () {
        this.updateShopIngot();
        this.checkShowNotFinishOrder();
    };
    // 刷新订阅信息
    ShopPnlCtrl.prototype.onUpdateSubscripion = function () {
        console.log('onUpdateSubscripion has=' + this.user.isHasSubscription());
        this.updateMonthlyCard();
    };
    // 刷新充值次数
    ShopPnlCtrl.prototype.onUpdateRechargeCount = function () {
        this.updateShopIngot();
    };
    // 刷新盲盒
    ShopPnlCtrl.prototype.onUpdateMysteryBox = function () {
        this.updateMysteryBox(this.mysteryBoxNode_.children[0]);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ShopPnlCtrl.prototype.initTop = function () {
        var node = this.topNode_.Swih(mc.currWindName === 'lobby' ? 'lobby' : 'main')[0];
        this.goldValLbl = node.Child('gold/val', cc.LabelRollNumber);
        this.ingotValLbl = node.Child('ingot/val', cc.LabelRollNumber);
        var gold = this.user.getGold(), ingot = this.user.getIngot();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.set(gold);
        this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.set(ingot);
    };
    // 
    ShopPnlCtrl.prototype.playRootAnimation = function () {
        this.rootNode_.stopAllActions();
        this.rootNode_.scale = 0.4;
        cc.tween(this.rootNode_).to(0.25, { scale: 1 }, { easing: cc.easing.backOut }).start();
    };
    // 检测未完成的订单
    ShopPnlCtrl.prototype.checkShowNotFinishOrder = function (showToast) {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!PayHelper_1.payHelper.isInitFinish()) return [3 /*break*/, 2];
                        return [4 /*yield*/, PayHelper_1.payHelper.checkPayInit()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [4 /*yield*/, this.user.checkHasNotFinishOrder(PayHelper_1.payHelper.getPlatform())];
                    case 3:
                        ok = _a.sent();
                        if (ok) {
                            ViewHelper_1.viewHelper.showPnl('main/NotFinishOrderTip');
                        }
                        else if (!showToast) {
                        }
                        else if (!PayHelper_1.payHelper.isInitFinish()) {
                            ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.no_check_not_finish_order');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新元宝
    ShopPnlCtrl.prototype.updateShopIngot = function () {
        var _this = this;
        var isFinish = PayHelper_1.payHelper.isInitFinish();
        var list = assetsMgr.getJson('recharge').datas.filter(function (m) { return !!m.ignore; });
        var rechargeCountRecord = this.user.getRechargeCountRecord();
        this.ingotNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadIcon(json.icon, it.Child('icon/val'), _this.key);
            it.Child('name').setLocaleKey('ui.shop_ingot_name', json.ingot);
            var button = it.Child('button');
            if (button.Child('money').active = isFinish) {
                button.Child('money/val', cc.Label).string = PayHelper_1.payHelper.getProductPriceText(json.product_id);
            }
            var extra = it.Child('mask/extra');
            if (!rechargeCountRecord[json.product_id]) { //首次
                extra.active = true;
                extra.Child('val').setLocaleKey('ui.first_give_ingot', json.ingot);
            }
            else if (extra.active = !!json.extra) {
                extra.Child('val').setLocaleKey('ui.give_ingot', json.extra);
            }
            button.Child('loading').active = !isFinish;
        });
        // 免费
        this.updateFreeGold();
        // 自选
        this.updateOptionalHero();
        //
        this.onUpdateIngot();
        this.onUpdateGold();
    };
    // 刷新推荐
    ShopPnlCtrl.prototype.updateRecommend = function () {
        var _this = this;
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = [];
        var _loop_1 = function (i) {
            var arr = [];
            var data = this_1.RECOMMENDED_LIST[i];
            if (data.type === 'city') {
                arr = this_1.user.getCanBuyCitySkins(serverArea);
            }
            else if (data.type === 'pawn') {
                arr = this_1.user.getCanBuyPawnSkins(serverArea);
            }
            else if (data.type === 'head') {
                arr = this_1.user.getCanBuyHeadIcons(serverArea);
            }
            else if (data.type === 'emoji') {
                arr = this_1.user.getCanBuyChatEmojis(serverArea);
            }
            if (arr.some(function (m) { return m.id === data.id; })) {
                list.push(data);
            }
        };
        var this_1 = this;
        // 先做一次筛选，拥有的就不显示了
        for (var i = 0; i < this.RECOMMENDED_LIST.length; i++) {
            _loop_1(i);
        }
        this.recommendSv_.stopAutoScroll();
        this.recommendSv_.content.y = 0;
        this.recommendSv_.Items(list, function (it, data) {
            var isPawnOrCity = data.type === 'city' || data.type === 'pawn';
            it.Child('icon', cc.Sprite).enabled = isPawnOrCity;
            var icon = it.Child('icon').Swih(isPawnOrCity ? '1' : '0')[0];
            icon.scale = 1;
            var json = null;
            if (data.type === 'pawn') {
                json = assetsMgr.getJsonData('pawnSkin', data.id);
                ResHelper_1.resHelper.loadPawnHeadIcon(json.id, icon, _this.key);
            }
            else if (data.type === 'city') {
                icon.scale = 0.65;
                json = assetsMgr.getJsonData('citySkin', data.id);
                ResHelper_1.resHelper.loadCityIcon(json.id, icon, _this.key);
            }
            else if (data.type === 'head') {
                json = assetsMgr.getJsonData('headIcon', data.id);
                ResHelper_1.resHelper.loadPlayerHead(icon, json.icon, _this.key, true);
            }
            else if (data.type === 'emoji') {
                json = assetsMgr.getJsonData('chatEmoji', data.id);
                ResHelper_1.resHelper.loadEmojiIcon(json.id, icon, _this.key);
            }
            it.Data = { type: data.type, json: json };
            _this.updateCostText(it, json);
            var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
            it.Child('new').active = isNew;
            it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
            it.Child('mask/extra', cc.Sprite).enabled = isNew;
        });
    };
    // 刷新月卡
    ShopPnlCtrl.prototype.updateMonthlyCard = function () {
        var node = this.cardNode_.Child('list');
        var subDatas = this.user.getSubDatas();
        var _loop_2 = function (i) {
            var data = Constant_1.MONTH_CARD[i], item = node.Child('card_' + i);
            var subData = subDatas.find(function (m) { return data.PRODUCT_IDS_ANDROID.includes(m.productId); }) || subDatas.find(function (m) { return data.PRODUCT_IDS_IOS.includes(m.productId); }) || subDatas.find(function (m) { return data.RECHARGES.includes(m.productId); });
            var isBuy = !!subData; // 是否已购买
            var canClaim = isBuy ? (subData.leftDays > 0 && subData.lastAwardTime <= 0) : false; //是否可领取
            var imm = item.Child('imm'), isFirstPay = !this_2.user.getRechargeCountRecord()[data.TYPE];
            imm.Child('mask').active = isFirstPay;
            imm.Child('count', cc.Label).string = 'x' + data.FIRST;
            imm.Child('mask/extra/val').setLocaleKey('ui.first_give_ingot', data.FIRST);
            var isDone = isBuy && !canClaim;
            imm.Child('done').active = isBuy;
            imm.Child('icon').opacity = isBuy ? 150 : 255;
            var day = item.Child('day');
            day.Child('done').active = isDone;
            var tipNode = item.Child('tip').Swih(isBuy ? 'surplus' : 'total')[0];
            if (isBuy) {
                tipNode.Child('val', cc.Label).string = subData.leftDays + '';
            }
            else {
                var total = data.FIRST * 2 + data.DAY * data.DURATION;
                tipNode.Child('g_count', cc.Label).string = total + '';
                var wartoken = tipNode.Child('wartoken'), wtCount = tipNode.Child('wt_count', cc.Label);
                if (wartoken.active = wtCount.node.active = !!data.EXTRA) {
                    wtCount.string = data.EXTRA * data.DURATION + '';
                }
            }
            var notEnd = (subData === null || subData === void 0 ? void 0 : subData.leftDays) === 0 && (subData === null || subData === void 0 ? void 0 : subData.surplusTime) > 0, showBuy = !subData || notEnd;
            if (i === 0) {
                day.Child('icon').opacity = isDone ? 150 : 255;
                day.Child('count', cc.Label).string = 'x' + data.DAY;
                var button = item.Child('sale_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0]; // 0:购买；1：领取；2：已领取
                button.Data = subData;
                button.opacity = notEnd ? 120 : 255; // 天数已尽，但没有到期
            }
            else {
                day.Child('icon').children.forEach(function (m) { return m.opacity = isDone ? 150 : 255; });
                day.Child('count').children.forEach(function (m, i) {
                    if (i === 0) {
                        m.Component(cc.Label).string = 'x' + data.DAY;
                    }
                    else {
                        m.Component(cc.Label).string = 'x' + data.EXTRA;
                    }
                });
                var button = item.Child('super_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0]; // 0:购买；1：领取；2：已领取
                button.Data = subData;
                button.opacity = notEnd ? 120 : 255; // 天数已尽，但没有到期
            }
        };
        var this_2 = this;
        for (var i = 0; i < node.childrenCount; i++) {
            _loop_2(i);
        }
    };
    // 刷新皮肤
    ShopPnlCtrl.prototype.updateSkin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var serverArea, citySkinList, list, arr1, arr2, arr3, mysteryBoxId, timeNode, json, _a, startTime, endTime;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                        citySkinList = this.updateCitySkin(serverArea);
                        list = this.user.getCanBuyPawnSkins(serverArea);
                        arr1 = [], arr2 = [], arr3 = [];
                        mysteryBoxId = 0;
                        list.forEach(function (m) {
                            if (m.cond > 100) {
                                arr3.push(m);
                                mysteryBoxId = m.cond;
                            }
                            else if (m['limit_time_' + serverArea] && m.cond === 4) {
                                arr2.push(m);
                            }
                            else {
                                arr1.push(m);
                            }
                        });
                        arr1.sort(function (a, b) {
                            var aw = 0, bw = 0;
                            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
                            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
                            return bw - aw;
                        });
                        arr2.sort(function (a, b) {
                            var aw = 0, bw = 0;
                            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
                            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
                            return bw - aw;
                        });
                        // 限定皮肤(目前只计划士兵皮肤)
                        if (this.limitedSkinNode_.active = this.skinTabsNode_.Child('skin_tabs_nbe/0').active = arr2.length > 0) {
                            this.updatePawnSkinList(this.limitedSkinNode_, arr2, true);
                            timeNode = this.limitedSkinNode_.Child('title/lay/time'), json = arr2[0];
                            _a = __read((json['limit_time_' + serverArea] || json.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                            timeNode.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime));
                        }
                        // 士兵皮肤
                        this.updatePawnSkinList(this.pawnSkinNode_, arr1, false);
                        if (!(this.mysteryBoxNode_.active = arr3.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.loadMysteryBoxPrefab(mysteryBoxId)];
                    case 1:
                        _b.sent();
                        this.initMysterBox(serverArea, arr3);
                        _b.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 加载盲盒预制
    ShopPnlCtrl.prototype.loadMysteryBoxPrefab = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var serverArea_1, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // 当前开放的盲盒id
                        if (!id) {
                            serverArea_1 = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                            assetsMgr.getJson('pawnSkin').datas.forEach(function (m) {
                                if (m.cond < 100) {
                                    return;
                                }
                                var _a = __read((m['limit_time_' + serverArea_1] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                                if (startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) {
                                    return; //是否有时间限制
                                }
                                id = m.cond;
                            });
                        }
                        if (!!this.mysteryBoxPrefab) return [3 /*break*/, 2];
                        _a = this;
                        return [4 /*yield*/, assetsMgr.loadTempRes('mysterybox/MYSTERYBOX_' + id, cc.Prefab, this.key)];
                    case 1:
                        _a.mysteryBoxPrefab = _b.sent();
                        _b.label = 2;
                    case 2: return [2 /*return*/, this.mysteryBoxPrefab];
                }
            });
        });
    };
    ShopPnlCtrl.prototype.initMysterBox = function (serverArea, arr) {
        var mysteryboxNode = null;
        if (this.mysteryBoxNode_.childrenCount > 1) {
            mysteryboxNode = this.mysteryBoxNode_.children[0];
        }
        else {
            mysteryboxNode = cc.instantiate2(this.mysteryBoxPrefab, this.mysteryBoxNode_);
        }
        mysteryboxNode.zIndex = -1;
        this.mysteryBoxNode_.Swih(mysteryboxNode.name)[0];
        var json = mysteryboxNode.Data = arr[0];
        var _a = __read((json['limit_time_' + serverArea] || json.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
        var timeNode = mysteryboxNode.Child('time'), timeVal = timeNode.Child('val');
        timeVal.setLocaleKey('ui.mysterybox_limit_time', endTime.split('-').slice(0, 3).join('/'));
        this.updateMysteryBox(mysteryboxNode, serverArea);
        this.addClickEvent(mysteryboxNode.Child('mysterybox_be', cc.Button), 'onClickMysterybox');
        this.addClickEvent(mysteryboxNode.Child('mysterybox_rule_be', cc.Button), 'onClickMysteryboxRule');
        this.addClickEvent(mysteryboxNode.Child('skin_exchange_be', cc.Button), 'onClickSkinExchange');
        if (!storageMgr.loadBool('click_MysteryBox_tab' + json.cond)) {
            ReddotHelper_1.reddotHelper.set('mystery_box', false);
            storageMgr.saveBool('click_MysteryBox_tab' + json.cond, true);
        }
    };
    ShopPnlCtrl.prototype.updateMysteryBox = function (node, serverArea) {
        var _this = this;
        serverArea = serverArea || (GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test');
        var exchangeSv = node.Child('exchange_sv', cc.ScrollView), colorSkins = this.getColorSkin(serverArea);
        exchangeSv.Items(colorSkins, function (it, data) {
            var _a;
            it.Data = data.id;
            var icon = it.Child('icon/val'), mask = it.Child('icon/mask');
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, icon, _this.key);
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, mask, _this.key);
            var _b = __read(ut.stringToNumbers(data.value, ','), 2), needId = _b[0], needCount = _b[1];
            var hasCount = ((_a = _this.user.getSkinItemList().filter(function (m) { return m.id === needId; })) === null || _a === void 0 ? void 0 : _a.length) || 0;
            mask.Component(cc.Sprite).fillRange = Math.min(1, 1 - hasCount / needCount);
            _this.addClickEvent(it.Component(cc.Button), 'onClickSkinExchange');
        });
    };
    ShopPnlCtrl.prototype.getColorSkin = function (serverArea) {
        var unlockMap = {};
        this.user.getUnlockPawnSkinIds().forEach(function (m) { return unlockMap[m] = true; });
        return assetsMgr.getJson('pawnSkin').datas.filter(function (m) {
            if (!unlockMap[m.id] && m.cond === 5) {
                var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                if (startTime && endTime && GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) {
                    return true;
                }
            }
            return false;
        });
    };
    ShopPnlCtrl.prototype.updateCitySkin = function (serverArea) {
        var list = this.user.getCanBuyCitySkins(serverArea);
        var arr1 = [], arr2 = [];
        list.forEach(function (m) {
            // if (m['limit_time_' + serverArea]) {
            // 	arr2.push(m)
            // } else {
            arr1.push(m);
            // }
        });
        arr1.sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
            return bw - aw;
        });
        // arr2.sort((a, b) => {
        // 	let aw = 0, bw = 0
        // 	aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
        // 	bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
        // 	return bw - aw
        // })
        this.updateCitySkinList(this.citySkinNode_, arr1);
        // // 季节限定
        // const limitedNode = this.citySkinNode_.Child('limited')
        // if (limitedNode.active = arr2.length > 0) {
        // 	this.updateCitySkinList(limitedNode.Child('list'), arr2)
        // 	const timeNode = limitedNode.Child('time'), timeLbl = timeNode.Child('val', cc.Label), json = arr2[0]
        // 	const [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')
        // 	timeLbl.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))
        // 	timeLbl._forceUpdateRenderData()
        // 	const w = (timeNode.width - timeLbl.node.width - 16) / 2
        // 	timeNode.Child('0').width = timeNode.Child('1').width = w
        // }
        return list;
    };
    ShopPnlCtrl.prototype.updateCitySkinList = function (node, list) {
        var _this = this;
        var node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        if (node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadCityIcon(json.id, it.Child('icon/val'), _this.key);
                _this.updateCostText(it, json);
                var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
                it.Child('new').active = isNew;
                it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
                it.Child('mask/extra', cc.Sprite).enabled = isNew;
            });
        }
    };
    ShopPnlCtrl.prototype.updatePawnSkinList = function (node, list, isLimited) {
        var _this = this;
        var node_ = null;
        if (isLimited) {
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.name === 'list') {
            node_.Items(list, function (it, json) {
                var _a;
                it.Data = json;
                it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.pawn_id), 5));
                _this.updateCostText(it, json);
                var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
                (_a = it.Child('new')) === null || _a === void 0 ? void 0 : _a.setActive(isNew);
                it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
                it.Child('mask/extra', cc.Sprite).enabled = isNew;
                ResHelper_1.resHelper.loadPawnHeadIcon(json.id, it.Child('icon/val'), _this.key);
            });
        }
    };
    ShopPnlCtrl.prototype.updateCostText = function (it, json) {
        var node = it.Child('mask/extra');
        if (json.gold > 0) {
            node.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.GOLD);
            node.Child('val', cc.Label).string = json.gold + '';
        }
        else if (json.ingot > 0) {
            node.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.INGOT);
            node.Child('val', cc.Label).string = json.ingot + '';
        }
        else {
            node.Child('icon').active = false;
        }
    };
    ShopPnlCtrl.prototype.getEndDate = function (endTime, startTime) {
        var year = new Date().getFullYear();
        if (((startTime === null || startTime === void 0 ? void 0 : startTime.split('-')[0]) || '').length !== 4) {
            startTime = year + '-' + startTime;
        }
        if (((endTime === null || endTime === void 0 ? void 0 : endTime.split('-')[0]) || '').length !== 4) {
            var _a = __read(startTime.split('-'), 2), _1 = _a[0], sm = _a[1];
            var em = endTime.split('-')[0];
            if (Number(em) < Number(sm)) {
                year += 1;
            }
            endTime = year + '-' + endTime;
        }
        var _b = __read(endTime === null || endTime === void 0 ? void 0 : endTime.split('-'), 3), endYear = _b[0], endMonth = _b[1], endDay = _b[2];
        return assetsMgr.lang('ui.date', endYear, endMonth, endDay);
    };
    ShopPnlCtrl.prototype.getAddNewTime = function (endTime) {
        var _a = __read(endTime === null || endTime === void 0 ? void 0 : endTime.split('-'), 3), endYear = _a[0], endMonth = _a[1], endDay = _a[2];
        return endMonth + '/' + endDay;
    };
    ShopPnlCtrl.prototype.buyCitySkin = function (id) {
        var _this = this;
        this.user.buyCitySkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateSkin();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'city'; })) {
                    _this.updateRecommend();
                }
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    ShopPnlCtrl.prototype.buyPawnSkin = function (id) {
        var _this = this;
        this.user.buyPawnSkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateSkin();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'pawn'; })) {
                    _this.updateRecommend();
                }
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    // 购买盲盒
    ShopPnlCtrl.prototype.buyMysterybox = function (json) {
        ViewHelper_1.viewHelper.showMessageBox('ui.buy_mysterybox_tip', {
            params: [json.ingot, json.desc],
            ok: function () { return ViewHelper_1.viewHelper.showPnl("activity/MysteryboxShow" + json.cond, json.cond, json.ingot); },
            cancel: function () { },
        });
    };
    // 刷新头像
    ShopPnlCtrl.prototype.updateHeadIcon = function () {
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = this.user.getCanBuyHeadIcons(serverArea).sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.id + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0);
            bw = b.id + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0);
            return bw - aw;
        });
        // 新款头像
        var newHeadIcons = list.filter(function (m) { return !!m['limit_time_' + serverArea]; });
        var date = '';
        if (newHeadIcons.length > 0) {
            var _a = __read((newHeadIcons[0]['limit_time_' + serverArea] || newHeadIcons[0].limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            date = this.getAddNewTime(startTime);
        }
        this.updateHeadIconList(this.newHeadNode_, newHeadIcons, true, date);
        // 经典头像
        var classicHeadIcons = list.filter(function (m) { return !m['limit_time_' + serverArea]; });
        this.updateHeadIconList(this.classicalHeadNode_, classicHeadIcons, false, '');
    };
    ShopPnlCtrl.prototype.updateHeadIconList = function (node, list, isNew, date) {
        var _this = this;
        var node_ = null;
        if (isNew) {
            node.active = list.length > 0;
            node.Child('title/lay/lay/time', cc.Label).string = date;
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.active && node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadPlayerHead(it.Child('icon/val'), json.icon, _this.key, true);
                _this.updateCostText(it, json);
            });
        }
    };
    // 购买头像
    ShopPnlCtrl.prototype.buyHeadIcon = function (id) {
        var _this = this;
        this.user.buyHeadIcon(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateHeadIcon();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'head'; })) {
                    _this.updateRecommend();
                }
            }
        });
    };
    // 刷新表情
    ShopPnlCtrl.prototype.updateChatEmoji = function () {
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = this.user.getCanBuyChatEmojis(serverArea).sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.id + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0);
            bw = b.id + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0);
            return bw - aw;
        });
        // 新款表情
        var newEmojis = list.filter(function (m) { return !!m['limit_time_' + serverArea]; });
        var date = '';
        if (newEmojis.length > 0) {
            var _a = __read((newEmojis[0]['limit_time_' + serverArea] || newEmojis[0].limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            date = this.getAddNewTime(startTime);
        }
        this.updateChatEmojiList(this.newEmojiNode_, newEmojis, true, date);
        //经典表情
        var classicEmojis = list.filter(function (m) { return !m['limit_time_' + serverArea]; });
        this.updateChatEmojiList(this.classicalEmojiNode_, classicEmojis, false, '');
    };
    ShopPnlCtrl.prototype.updateChatEmojiList = function (node, list, isNew, date) {
        var _this = this;
        var node_ = null;
        if (isNew) {
            node.active = list.length > 0;
            node.Child('title/lay/lay/time', cc.Label).string = date;
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.active && node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadEmojiIcon(json.id, it.Child('icon/val'), _this.key);
                _this.updateCostText(it, json);
            });
        }
    };
    ShopPnlCtrl.prototype.buyChatEmoji = function (id) {
        var _this = this;
        this.user.buyChatEmoji(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateChatEmoji();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'emoji'; })) {
                    _this.updateRecommend();
                }
            }
        });
    };
    // 购买英雄
    ShopPnlCtrl.prototype.buyHero = function (id) {
        var _this = this;
        this.user.buyHero(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateOptionalHero();
                ViewHelper_1.viewHelper.showGainPortrayalDebris(id, 3);
            }
        });
    };
    // 刷新免费金币
    ShopPnlCtrl.prototype.updateFreeGold = function () {
        var isCanBuy = this.user.isBuyLimitFreeGold();
        this.freeGoldNode_.Child('button').opacity = isCanBuy ? 255 : 120;
        this.freeGoldNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_day' : 'ui.yet_buy_day');
    };
    // 刷新自选英雄
    ShopPnlCtrl.prototype.updateOptionalHero = function () {
        var isCanBuy = this.user.isBuyLimitOptionalHero();
        this.optionalHeroNode_.Child('button').opacity = isCanBuy ? 255 : 120;
        this.optionalHeroNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_week' : 'ui.yet_buy_week');
    };
    // 领取订阅的月卡奖励
    ShopPnlCtrl.prototype.getMonthlyCardAward = function (type) {
        var _this = this;
        this.user.reqGetMonthlyCardAward(type).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateMonthlyCard();
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
            }
        });
    };
    ShopPnlCtrl.prototype.onClickMysterybox = function (event, data) {
        var _this = this;
        var node = event.target.parent;
        var json = node.Data;
        if (json && json.cond > 100) {
            if (this.user.getIngot() < json.ingot) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
            }
            else if (!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
                ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', json.cond, function () { return _this.isValid && _this.buyMysterybox(json); });
            }
            else {
                this.buyMysterybox(json);
            }
        }
    };
    ShopPnlCtrl.prototype.onClickMysteryboxRule = function (event, data) {
        audioMgr.playSFX('click');
        var id = this.mysteryBoxNode_.children[0].Data.cond;
        id && ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', id);
    };
    ShopPnlCtrl.prototype.onClickSkinExchange = function (event, _data) {
        var data = event.target.Data;
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkinExchange', data);
    };
    ShopPnlCtrl.prototype.onScrolling = function (event) {
        var height = event.node.height / 2, content = event.content, skinY = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y, socialY = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y, showCard = content.y >= Math.abs(this.cardNode_.y + height) && content.y < Math.abs(skinY + height), showSkin = content.y >= Math.abs(skinY + height) && content.y < Math.abs(socialY + height), showSocial = content.y >= Math.abs(socialY + height);
        if (this.skinTabsNode_.active !== showSkin) {
            this.skinTabsNode_.active = showSkin;
        }
        if (this.socialTabsNode_.active !== showSocial) {
            this.socialTabsNode_.active = showSocial;
        }
        event.Child('view').height = (showSkin || showSocial) ? this.preViewHeight - this.skinTabsNode_.height : this.preViewHeight;
        var tab = showCard ? 1 : showSkin ? 2 : showSocial ? 3 : 0;
        if (this.curTab !== tab) {
            this.curTab = tab;
            this.tabsTc_.Swih(tab);
            this.user.setTempPreferenceData(this.PKEY_TAB, tab);
        }
    };
    ShopPnlCtrl.prototype.throttle = function (func, delay) {
        var timer = null;
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (!timer) {
                func.apply(this, args);
                timer = setTimeout(function () {
                    timer = null;
                }, delay);
            }
        };
    };
    ShopPnlCtrl.prototype.update = function (dt) {
        if (this.drawCount < this.funcList.length) {
            this.frameCount++;
            if (this.frameCount > this.drawRate) {
                this.frameCount = 0;
                this.funcList[this.drawCount].bind(this)();
                this.drawCount++;
            }
        }
    };
    ShopPnlCtrl = __decorate([
        ccclass
    ], ShopPnlCtrl);
    return ShopPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ShopPnlCtrl;

cc._RF.pop();