
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AutoScrollCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3991dKytutMAb15IMRiGeX2', 'AutoScrollCmpt');
// app/script/view/cmpt/AutoScrollCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AutoScrollCmpt = /** @class */ (function (_super) {
    __extends(AutoScrollCmpt, _super);
    function AutoScrollCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.target = null; // 可滑动的节点
        _this.speed = 30; // 滑动速度
        _this.delay = 2; // 延迟多久开始
        _this.isVertical = false; // 是否纵向滑动
        _this.isLoopScroll = false; // 是否循环滚动
        // 滚动距离
        _this.scrollDis = 0;
        // 初始宽高
        _this.initWH = 0;
        // 延迟计时
        _this.delayTime = 0;
        return _this;
    }
    AutoScrollCmpt.prototype.onLoad = function () {
        if (!this.target) {
            this.target = this.node.children[0];
        }
        this.delayTime = this.delay;
    };
    AutoScrollCmpt.prototype.update = function (dt) {
        if (this.isVertical) {
            if (this.initWH !== this.target.height) {
                this.initWH = this.target.height;
                this.scrollDis = this.initWH - this.node.height + 4;
            }
        }
        else {
            if (this.initWH !== this.target.width) {
                this.initWH = this.target.width;
                this.scrollDis = this.initWH - this.node.width + 4;
            }
        }
        if (this.scrollDis <= 0) {
            if (this.isVertical) {
                if (this.target.y !== 0) {
                    this.target.y = 0;
                }
            }
            else if (this.target.x !== 0) {
                this.target.x = 0;
            }
            return;
        }
        if (this.delayTime > 0) {
            this.delayTime -= dt;
        }
        else {
            if (this.isVertical) {
                this.target.y -= dt * this.speed;
                if (this.target.y <= -this.scrollDis || this.target.y > 4) {
                    this.speed *= -1;
                    this.delayTime = this.delay;
                }
            }
            else {
                this.target.x -= dt * this.speed;
                if (this.target.x <= -this.scrollDis || this.target.x > 4) {
                    this.speed *= -1;
                    this.delayTime = this.delay;
                }
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], AutoScrollCmpt.prototype, "target", void 0);
    __decorate([
        property(cc.Integer)
    ], AutoScrollCmpt.prototype, "speed", void 0);
    __decorate([
        property(cc.Integer)
    ], AutoScrollCmpt.prototype, "delay", void 0);
    __decorate([
        property(cc.Boolean)
    ], AutoScrollCmpt.prototype, "isVertical", void 0);
    __decorate([
        property(cc.Boolean)
    ], AutoScrollCmpt.prototype, "isLoopScroll", void 0);
    AutoScrollCmpt = __decorate([
        ccclass
    ], AutoScrollCmpt);
    return AutoScrollCmpt;
}(cc.Component));
exports.default = AutoScrollCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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