
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/shader/FlashLightShaderCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '62e3buwV8FILadsk9x6b9R9', 'FlashLightShaderCtrl');
// app/script/common/shader/FlashLightShaderCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent;
var FlashLightShaderCtrl = /** @class */ (function (_super) {
    __extends(FlashLightShaderCtrl, _super);
    function FlashLightShaderCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.maxTime = 1.3; //扫一次的时间
        _this.maxCount = 2; //扫几次
        _this.interval = 1;
        _this.delay = 0;
        _this.elapsed = 0;
        _this.count = 0;
        _this.material = null;
        _this.preMaterial = null;
        _this.sprite = null;
        return _this;
    }
    FlashLightShaderCtrl.prototype.onLoad = function () {
        this.sprite = this.getComponent(cc.Sprite);
        this.preMaterial = this.sprite.getMaterial(0);
        var material = assetsMgr.getMaterial('FlashLight');
        //@ts-ignore
        this.material = cc.Material.create(material._effectAsset);
    };
    FlashLightShaderCtrl.prototype.setCount = function (count) {
        this.maxCount = count;
    };
    // 设置材质
    FlashLightShaderCtrl.prototype.play = function () {
        if (!this.sprite || !this.sprite.spriteFrame) {
            return;
        }
        this.sprite.setMaterial(0, this.material);
        this.material.setProperty('time', 0);
        this.elapsed = 0;
        this.delay = 0;
        this.count = this.maxCount;
        if (this.sprite.spriteFrame.isRotated() == true) {
            this.material.setProperty('rotated', 1);
        }
        else {
            this.material.setProperty('rotated', 0);
        }
        this.setMaterialProperty();
    };
    FlashLightShaderCtrl.prototype.setMaterialProperty = function () {
        var _a;
        if (!((_a = this.sprite) === null || _a === void 0 ? void 0 : _a.spriteFrame)) {
            return;
        }
        //@ts-ignore
        var uv = this.sprite.spriteFrame.uv;
        if (this.sprite.spriteFrame.isRotated() == true) {
            this.material.setProperty('u_lrbt', [uv[1], uv[7], uv[0], uv[6]]);
        }
        else {
            this.material.setProperty('u_lrbt', [uv[0], uv[6], uv[7], uv[1]]);
        }
    };
    // 还原
    FlashLightShaderCtrl.prototype.stop = function () {
        this.count = 0;
        this.sprite.setMaterial(0, this.preMaterial);
    };
    FlashLightShaderCtrl.prototype.update = function (dt) {
        if (this.count === 0) {
            return;
        }
        if (this.delay > 0) {
            this.delay -= dt;
            return;
        }
        this.elapsed += dt;
        if (this.elapsed < this.maxTime) {
            this.setMaterialProperty();
            this.material.setProperty('time', this.elapsed / this.maxTime);
        }
        else {
            this.delay = this.interval;
            this.count -= 1;
            this.elapsed = 0;
            if (this.count <= 0) {
                this.stop();
            }
        }
    };
    FlashLightShaderCtrl = __decorate([
        ccclass,
        requireComponent(cc.Sprite)
    ], FlashLightShaderCtrl);
    return FlashLightShaderCtrl;
}(cc.Component));
exports.default = FlashLightShaderCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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