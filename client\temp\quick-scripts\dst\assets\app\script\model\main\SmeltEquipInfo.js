
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/SmeltEquipInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c0041/n/9FKcYlQtt5lfyWU', 'SmeltEquipInfo');
// app/script/model/main/SmeltEquipInfo.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
// 融炼信息
var SmeltEquipInfo = /** @class */ (function () {
    function SmeltEquipInfo() {
        this.uid = '';
        this.id = 0;
        this.viceAttrs = []; //属性
        this.needTime = 0;
        this.surplusTime = 0;
        this.getTime = 0;
        this.viceIds = []; //副装备的id列表
    }
    SmeltEquipInfo.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.id = Number(this.uid.split('_')[0]);
        this.viceAttrs = data.viceAttrs || [];
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        this.updateViceAttrs();
        return this;
    };
    SmeltEquipInfo.prototype.updateViceAttrs = function () {
        var _this = this;
        this.viceIds = [];
        this.viceAttrs.forEach(function (m) {
            var _a = __read(m.attr, 5), fieldType = _a[0], type = _a[1], value = _a[2], odds = _a[3], viceId = _a[4];
            if (viceId && !_this.viceIds.has(viceId)) {
                _this.viceIds.push(viceId);
            }
        });
    };
    Object.defineProperty(SmeltEquipInfo.prototype, "name", {
        get: function () { return 'equipText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    // 获取实际的剩余时间
    SmeltEquipInfo.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    return SmeltEquipInfo;
}());
exports.default = SmeltEquipInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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