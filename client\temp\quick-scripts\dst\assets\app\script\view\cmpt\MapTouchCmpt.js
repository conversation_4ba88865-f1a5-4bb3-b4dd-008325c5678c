
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/MapTouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c99b7ALWxNE5J8Acasbm8Pl', 'MapTouchCmpt');
// app/script/view/cmpt/MapTouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于移动地图的触摸组建
 */
var MapTouchCmpt = /** @class */ (function (_super) {
    __extends(MapTouchCmpt, _super);
    function MapTouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.touchs = [];
        _this.touchCount = 2;
        _this.startZoomRatio = 0;
        _this.startTouchDis = 0;
        _this.startWorldPoint = cc.v2();
        _this.startPoint = cc.v2();
        _this.isDownClick = false; //是否按下点击
        _this.draging = false; //是否拖动中
        _this.clickCallback = null; //点击回调
        _this._temp_vec2 = cc.v2();
        return _this;
    }
    MapTouchCmpt.prototype.onLoad = function () {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    };
    MapTouchCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    MapTouchCmpt.prototype.init = function (clickCallback) {
        this.reset();
        this.clickCallback = clickCallback;
        eventCenter.off(EventType_1.default.CHANGE_MAP_TOUCH, this.onChangeMapTouch, this);
        eventCenter.on(EventType_1.default.CHANGE_MAP_TOUCH, this.onChangeMapTouch, this);
        eventCenter.off(EventType_1.default.EVENT_GAME_SHOW, this.onResetMapTouch, this);
        eventCenter.on(EventType_1.default.EVENT_GAME_SHOW, this.onResetMapTouch, this);
    };
    MapTouchCmpt.prototype.clean = function () {
        this.clickCallback = null;
        eventCenter.off(EventType_1.default.CHANGE_MAP_TOUCH, this.onChangeMapTouch, this);
        eventCenter.off(EventType_1.default.EVENT_GAME_SHOW, this.onResetMapTouch, this);
    };
    MapTouchCmpt.prototype.reset = function () {
        this.touchs.length = 0;
        this.touchCount = 2;
        this.startZoomRatio = 0;
        this.startTouchDis = 0;
        this.startWorldPoint.set2(0, 0);
        this.startPoint.set2(0, 0);
        this.draging = false;
    };
    MapTouchCmpt.prototype.isDraging = function () { return this.draging; };
    MapTouchCmpt.prototype.onTouchStart = function (event) {
        var id = event.getID(), touches = event.getTouches();
        if (touches.length < this.touchs.length) {
            this.touchs.length = 0;
        }
        if (this.touchs.some(function (m) { return m.id === id; }) || this.touchs.length >= this.touchCount) {
            return;
        }
        this.isDownClick = true;
        this.draging = false;
        this.touchs.push({ id: id, startLocation: event.getStartLocation(), location: event.getLocation() });
        if (this.touchs.length === 2) {
            // 记录当前摄像机缩放比
            this.startZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
            // 记录两点的距离
            var _a = __read(this.touchs, 2), a = _a[0], b = _a[1];
            var c = a.location.sub(b.location, this._temp_vec2);
            this.startTouchDis = c.mag();
            // 记录中点 以及算出在世界坐标的位置
            this.startPoint.set(c.divSelf(2).addSelf(b.location));
            this.startWorldPoint.set(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint));
        }
        else {
            CameraCtrl_1.cameraCtrl.dragStart();
            this.startTouchDis = -1;
        }
    };
    MapTouchCmpt.prototype.onTouchMove = function (event) {
        var id = event.getID();
        var touch = this.touchs.find(function (m) { return m.id === id; });
        if (!touch) {
            return;
        }
        var drag = false;
        touch.location.set(event.getLocation());
        if (this.touchs.length === 2) {
            // 获取两点的距离 然后根据上一次记录的距离 计算出比例
            var _a = __read(this.touchs, 2), a = _a[0], b = _a[1];
            var dis = a.location.sub(b.location, this._temp_vec2).mag();
            var isMin = dis < this.startTouchDis;
            var delta = isMin ? -(this.startTouchDis / dis - 1) : (dis / this.startTouchDis - 1);
            var r = isMin ? 0.5 : 0.4;
            // 根据比例设置 摄像机缩放比
            CameraCtrl_1.cameraCtrl.zoomRatio = this.startZoomRatio + delta * r;
            // 如果没有达到最高或者最低的缩放比
            if (!CameraCtrl_1.cameraCtrl.scaleIsMinOrMax()) {
                // 这里根据之前记录的开始位置 计算缩放过后 偏移的位置 然后纠正摄像机位置
                var speed = CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint).subSelf(this.startWorldPoint);
                // 重新纠正摄像机位置
                CameraCtrl_1.cameraCtrl.setPosition(CameraCtrl_1.cameraCtrl.getPosition().subSelf(speed));
            }
        }
        else {
            var a = CameraCtrl_1.cameraCtrl.convertWorldSub(touch.startLocation, touch.location), mag = a.mag();
            // 只要滑的距离够长 这次就不算为点击了
            if (this.isDownClick && mag > Constant_1.CLICK_SPACE) {
                this.isDownClick = false;
            }
            // 拖动
            if (drag = mag > 4) {
                CameraCtrl_1.cameraCtrl.drag(a);
            }
        }
        if (drag && !this.draging) {
            this.draging = true;
            eventCenter.emit(EventType_1.default.HIDE_WORLD_TEXT, true);
        }
    };
    MapTouchCmpt.prototype.onTouchEnd = function (event) {
        var id = event.getID();
        var i = this.touchs.findIndex(function (m) { return m.id === id; });
        if (i === -1) {
            return;
        }
        var touch = this.touchs.splice(i, 1)[0];
        touch.location.set(event.getLocation());
        if (this.touchs.length === 1) {
            touch = this.touchs[0];
            touch.startLocation.set(touch.location);
            CameraCtrl_1.cameraCtrl.recordPosition();
        }
        else if (this.startTouchDis !== -1) { //这里表示是触摸了两个点之后松开 要做摄像机自动还原缩放 
            CameraCtrl_1.cameraCtrl.zoomRatioRestore();
        }
        else if (this.clickCallback && this.isDownClick && CameraCtrl_1.cameraCtrl.convertWorldSub(touch.startLocation, touch.location).mag() <= Constant_1.CLICK_SPACE) { //如果只有一个 那么就是点击
            this.clickCallback(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(touch.location), touch.location);
        }
        if (this.draging) {
            this.draging = false;
            eventCenter.emit(EventType_1.default.HIDE_WORLD_TEXT, false);
        }
    };
    // 切换是否可以 移动和缩放
    MapTouchCmpt.prototype.onChangeMapTouch = function (val) {
        this.touchs.length = 0;
        this.touchCount = val ? 2 : 0;
    };
    // 重置触摸
    MapTouchCmpt.prototype.onResetMapTouch = function () {
        this.touchs.length = 0;
        // this.touchCount = 2
        this.startZoomRatio = 0;
        this.startTouchDis = 0;
        this.startWorldPoint.set2(0, 0);
        this.startPoint.set2(0, 0);
    };
    MapTouchCmpt = __decorate([
        ccclass
    ], MapTouchCmpt);
    return MapTouchCmpt;
}(cc.Component));
exports.default = MapTouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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