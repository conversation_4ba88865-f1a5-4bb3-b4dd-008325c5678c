
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BTConstant.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8566ezNSaZChan1CedG9POl', 'BTConstant');
// app/script/model/behavior/BTConstant.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBuffIDBySkin = exports.BTState = exports.BTType = void 0;
var BTType;
(function (BTType) {
    BTType["COMPOSITE"] = "composite";
    BTType["DECORATOR"] = "decorator";
    BTType["ACTION"] = "action";
    BTType["CONDITION"] = "condition";
})(BTType || (BTType = {}));
exports.BTType = BTType;
var BTState;
(function (BTState) {
    BTState[BTState["SUCCESS"] = 1] = "SUCCESS";
    BTState[BTState["FAILURE"] = 2] = "FAILURE";
    BTState[BTState["RUNNING"] = 3] = "RUNNING";
    BTState[BTState["ERROR"] = 4] = "ERROR";
})(BTState || (BTState = {}));
exports.BTState = BTState;
var SKIN_BUFF_CONF = {
    320501: {
        26: 26001,
    },
    3205102: {
        26: 26102,
    },
    320301: {
        11: 11001,
    },
    3203102: {
        11: 11102,
    },
    3304109: {
        12: 12001,
    },
};
function getBuffIDBySkin(id, skinId) {
    var skinBuff = SKIN_BUFF_CONF[skinId];
    if (!skinBuff) {
        return id;
    }
    return skinBuff[id] || id;
}
exports.getBuffIDBySkin = getBuffIDBySkin;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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