
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/GoogleHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '605c27eXyxLu6YA2B1gdvWF', 'GoogleHelper');
// app/script/common/helper/GoogleHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleHelper = void 0;
var JsbEvent_1 = require("../event/JsbEvent");
var ErrorReportHelper_1 = require("./ErrorReportHelper");
var JsbHelper_1 = require("./JsbHelper");
/**
 * 处理海外google 相关
 */
var GoogleHelper = /** @class */ (function () {
    function GoogleHelper() {
    }
    // 登陆
    GoogleHelper.prototype.nativeLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.GOOGLE_LOGIN)];
                    case 1:
                        res = _a.sent();
                        if (res.error) {
                            ErrorReportHelper_1.errorReportHelper.reportError('NativeLogin Error', { type: 'google', status: res.code, extra: JSON.stringify(res) });
                            return [2 /*return*/, { errcode: res.code || -10086, msg: res.message }];
                        }
                        return [2 /*return*/, { idToken: res.token, nickname: res.nickName, headicon: res.photoUrl }];
                }
            });
        });
    };
    return GoogleHelper;
}());
exports.googleHelper = new GoogleHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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