
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/ModifyNicknamePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '49862KWv4VA86HnUeJz+FRg', 'ModifyNicknamePnlCtrl');
// app/script/view/menu/ModifyNicknamePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ModifyNicknamePnlCtrl = /** @class */ (function (_super) {
    __extends(ModifyNicknamePnlCtrl, _super);
    function ModifyNicknamePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/input_eb
        _this.goldNode_ = null; // path://root/ok_be/lay/gold_n
        //@end
        _this.user = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    ModifyNicknamePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ModifyNicknamePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    ModifyNicknamePnlCtrl.prototype.onEnter = function (data) {
        if (this.goldNode_.active = this.user.getModifyNameCount() > 0) {
            this.goldNode_.Child('val', cc.Label).string = '' + Constant_1.MODIFY_NICKNAME_GOLD;
        }
        this.inputEb_.string = '';
    };
    ModifyNicknamePnlCtrl.prototype.onRemove = function () {
    };
    ModifyNicknamePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    ModifyNicknamePnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var name = this.inputEb_.string.trim();
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_nickname');
        }
        else if (ut.getStringLen(name) > 14 || GameHelper_1.gameHpr.getTextNewlineCount(name) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        else if (this.user.getModifyNameCount() > 0 && this.user.getGold() < Constant_1.MODIFY_NICKNAME_GOLD) {
            return ViewHelper_1.viewHelper.showGoldNotEnough();
        }
        this.user.modifyNickname(name).then(function (err) {
            if (err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            }
            else if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    ModifyNicknamePnlCtrl = __decorate([
        ccclass
    ], ModifyNicknamePnlCtrl);
    return ModifyNicknamePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ModifyNicknamePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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