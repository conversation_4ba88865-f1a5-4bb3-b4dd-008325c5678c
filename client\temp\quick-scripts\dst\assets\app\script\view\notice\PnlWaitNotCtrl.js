
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/PnlWaitNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6cbdeYFqSxIebLzjY/85uDW', 'PnlWaitNotCtrl');
// app/script/view/notice/PnlWaitNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PnlWaitNotCtrl = /** @class */ (function (_super) {
    __extends(PnlWaitNotCtrl, _super);
    function PnlWaitNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.maskNode_ = null; // path://mask_n
        _this.roootNode_ = null; // path://rooot_n
        //@end
        _this.opening = false;
        _this.delay = 0.5; // 延迟多少秒显示画面
        _this.hideTime = 15; // 延迟多少秒后强行关闭界面
        _this.elapsed = 0;
        _this.loadInfo = null; //当前加载信息
        return _this;
    }
    PnlWaitNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[mc.Event.LOAD_BEGIN_PNL] = this.onEventOpen, _a),
            (_b = {}, _b[mc.Event.LOAD_END_PNL] = this.onEventHide, _b),
        ];
    };
    PnlWaitNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.maskNode_.active = false;
                this.roootNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PnlWaitNotCtrl.prototype.onEventOpen = function (info) {
        this.open();
        this.loadInfo = info;
        this.opening = true;
        this.maskNode_.active = true;
        this.roootNode_.active = false;
        this.elapsed = 0;
    };
    PnlWaitNotCtrl.prototype.onEventHide = function () {
        this.hide();
        this.opening = false;
        this.maskNode_.active = false;
        this.roootNode_.active = false;
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PnlWaitNotCtrl.prototype.update = function (dt) {
        var _a;
        if (!this.opening) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed >= this.delay && this.elapsed < this.hideTime) {
            this.elapsed += this.hideTime;
            this.roootNode_.active = true;
        }
        if (this.elapsed - this.hideTime >= this.hideTime) {
            mc.unlockTouch('__show_pnl__');
            if ((_a = this.loadInfo) === null || _a === void 0 ? void 0 : _a.id) {
                var _b = this.loadInfo, id = _b.id, name_1 = _b.name, params_1 = _b.params;
                this.emit(mc.Event.GIVEUP_LOAD_PNL, id); //放弃加载
                ViewHelper_1.viewHelper.showMessageBox('toast.load_timeout', {
                    ok: function () { return ViewHelper_1.viewHelper.showPnl.apply(ViewHelper_1.viewHelper, __spread([name_1], params_1)); },
                    cancel: function () { },
                    okText: 'login.button_retry',
                });
            }
            else {
                ViewHelper_1.viewHelper.showMessageBox('toast.load_timeout');
            }
            this.onEventHide();
        }
    };
    PnlWaitNotCtrl = __decorate([
        ccclass
    ], PnlWaitNotCtrl);
    return PnlWaitNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = PnlWaitNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG5vdGljZVxcUG5sV2FpdE5vdEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw2REFBNEQ7QUFFcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBNEMsa0NBQWlCO0lBQTdEO1FBQUEscUVBeUVDO1FBdkVHLDBCQUEwQjtRQUNyQixlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMsZ0JBQWdCO1FBQzFDLGdCQUFVLEdBQVksSUFBSSxDQUFBLENBQUMsaUJBQWlCO1FBQ3BELE1BQU07UUFFSyxhQUFPLEdBQVksS0FBSyxDQUFBO1FBQ3hCLFdBQUssR0FBVyxHQUFHLENBQUEsQ0FBQSxZQUFZO1FBQy9CLGNBQVEsR0FBVyxFQUFFLENBQUEsQ0FBQSxlQUFlO1FBQ3BDLGFBQU8sR0FBVyxDQUFDLENBQUE7UUFFbkIsY0FBUSxHQUFnQixJQUFJLENBQUEsQ0FBQyxRQUFROztJQTZEakQsQ0FBQztJQTNEVSx3Q0FBZSxHQUF0Qjs7UUFDSSxPQUFPO3NCQUNELEdBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxjQUFjLElBQUcsSUFBSSxDQUFDLFdBQVc7c0JBQzNDLEdBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxZQUFZLElBQUcsSUFBSSxDQUFDLFdBQVc7U0FDOUMsQ0FBQTtJQUNMLENBQUM7SUFFWSxpQ0FBUSxHQUFyQjs7O2dCQUNJLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtnQkFDN0IsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBOzs7O0tBQ2pDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUM5QixNQUFNO0lBQ0gsaUhBQWlIO0lBRXpHLG9DQUFXLEdBQW5CLFVBQW9CLElBQWlCO1FBQ2pDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtRQUNYLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFBO1FBQ3BCLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFBO1FBQ25CLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtRQUM1QixJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7UUFDOUIsSUFBSSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUE7SUFDcEIsQ0FBQztJQUVPLG9DQUFXLEdBQW5CO1FBQ0ksSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1FBQ1gsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUE7UUFDcEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO1FBQzdCLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtJQUNsQyxDQUFDO0lBQ0QsaUhBQWlIO0lBRWpILCtCQUFNLEdBQU4sVUFBTyxFQUFVOztRQUNiLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFO1lBQ2YsT0FBTTtTQUNUO1FBQ0QsSUFBSSxDQUFDLE9BQU8sSUFBSSxFQUFFLENBQUE7UUFDbEIsSUFBSSxJQUFJLENBQUMsT0FBTyxJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQzVELElBQUksQ0FBQyxPQUFPLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQTtZQUM3QixJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7U0FDaEM7UUFDRCxJQUFJLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQy9DLEVBQUUsQ0FBQyxXQUFXLENBQUMsY0FBYyxDQUFDLENBQUE7WUFDOUIsVUFBSSxJQUFJLENBQUMsUUFBUSwwQ0FBRSxFQUFFLEVBQUU7Z0JBQ2IsSUFBQSxLQUF1QixJQUFJLENBQUMsUUFBUSxFQUFsQyxFQUFFLFFBQUEsRUFBRSxNQUFJLFVBQUEsRUFBRSxRQUFNLFlBQWtCLENBQUE7Z0JBQzFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxlQUFlLEVBQUUsRUFBRSxDQUFDLENBQUEsQ0FBQyxNQUFNO2dCQUM5Qyx1QkFBVSxDQUFDLGNBQWMsQ0FBQyxvQkFBb0IsRUFBRTtvQkFDNUMsRUFBRSxFQUFFLGNBQU0sT0FBQSx1QkFBVSxDQUFDLE9BQU8sT0FBbEIsdUJBQVUsWUFBUyxNQUFJLEdBQUssUUFBTSxJQUFsQyxDQUFtQztvQkFDN0MsTUFBTSxFQUFFLGNBQVEsQ0FBQztvQkFDakIsTUFBTSxFQUFFLG9CQUFvQjtpQkFDL0IsQ0FBQyxDQUFBO2FBQ0w7aUJBQU07Z0JBQ0gsdUJBQVUsQ0FBQyxjQUFjLENBQUMsb0JBQW9CLENBQUMsQ0FBQTthQUNsRDtZQUNELElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQTtTQUNyQjtJQUNMLENBQUM7SUF4RWdCLGNBQWM7UUFEbEMsT0FBTztPQUNhLGNBQWMsQ0F5RWxDO0lBQUQscUJBQUM7Q0F6RUQsQUF5RUMsQ0F6RTJDLEVBQUUsQ0FBQyxjQUFjLEdBeUU1RDtrQkF6RW9CLGNBQWMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBQbmxXYWl0Tm90Q3RybCBleHRlbmRzIG1jLkJhc2VOb3RpY2VDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG5cdHByaXZhdGUgbWFza05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vbWFza19uXG5cdHByaXZhdGUgcm9vb3ROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb290X25cblx0Ly9AZW5kXG5cbiAgICBwcml2YXRlIG9wZW5pbmc6IGJvb2xlYW4gPSBmYWxzZVxuICAgIHByaXZhdGUgZGVsYXk6IG51bWJlciA9IDAuNS8vIOW7tui/n+WkmuWwkeenkuaYvuekuueUu+mdolxuICAgIHByaXZhdGUgaGlkZVRpbWU6IG51bWJlciA9IDE1Ly8g5bu26L+f5aSa5bCR56eS5ZCO5by66KGM5YWz6Zet55WM6Z2iXG4gICAgcHJpdmF0ZSBlbGFwc2VkOiBudW1iZXIgPSAwXG5cbiAgICBwcml2YXRlIGxvYWRJbmZvOiBMb2FkUG5sSW5mbyA9IG51bGwgLy/lvZPliY3liqDovb3kv6Hmga9cblxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICB7IFttYy5FdmVudC5MT0FEX0JFR0lOX1BOTF06IHRoaXMub25FdmVudE9wZW4gfSxcbiAgICAgICAgICAgIHsgW21jLkV2ZW50LkxPQURfRU5EX1BOTF06IHRoaXMub25FdmVudEhpZGUgfSxcbiAgICAgICAgXVxuICAgIH1cblxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcbiAgICAgICAgdGhpcy5tYXNrTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5yb29vdE5vZGVfLmFjdGl2ZSA9IGZhbHNlXG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblx0Ly9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgb25FdmVudE9wZW4oaW5mbzogTG9hZFBubEluZm8pIHtcbiAgICAgICAgdGhpcy5vcGVuKClcbiAgICAgICAgdGhpcy5sb2FkSW5mbyA9IGluZm9cbiAgICAgICAgdGhpcy5vcGVuaW5nID0gdHJ1ZVxuICAgICAgICB0aGlzLm1hc2tOb2RlXy5hY3RpdmUgPSB0cnVlXG4gICAgICAgIHRoaXMucm9vb3ROb2RlXy5hY3RpdmUgPSBmYWxzZVxuICAgICAgICB0aGlzLmVsYXBzZWQgPSAwXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBvbkV2ZW50SGlkZSgpIHtcbiAgICAgICAgdGhpcy5oaWRlKClcbiAgICAgICAgdGhpcy5vcGVuaW5nID0gZmFsc2VcbiAgICAgICAgdGhpcy5tYXNrTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5yb29vdE5vZGVfLmFjdGl2ZSA9IGZhbHNlXG4gICAgfVxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICB1cGRhdGUoZHQ6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMub3BlbmluZykge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lbGFwc2VkICs9IGR0XG4gICAgICAgIGlmICh0aGlzLmVsYXBzZWQgPj0gdGhpcy5kZWxheSAmJiB0aGlzLmVsYXBzZWQgPCB0aGlzLmhpZGVUaW1lKSB7XG4gICAgICAgICAgICB0aGlzLmVsYXBzZWQgKz0gdGhpcy5oaWRlVGltZVxuICAgICAgICAgICAgdGhpcy5yb29vdE5vZGVfLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5lbGFwc2VkIC0gdGhpcy5oaWRlVGltZSA+PSB0aGlzLmhpZGVUaW1lKSB7XG4gICAgICAgICAgICBtYy51bmxvY2tUb3VjaCgnX19zaG93X3BubF9fJylcbiAgICAgICAgICAgIGlmICh0aGlzLmxvYWRJbmZvPy5pZCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgaWQsIG5hbWUsIHBhcmFtcyB9ID0gdGhpcy5sb2FkSW5mb1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdChtYy5FdmVudC5HSVZFVVBfTE9BRF9QTkwsIGlkKSAvL+aUvuW8g+WKoOi9vVxuICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd01lc3NhZ2VCb3goJ3RvYXN0LmxvYWRfdGltZW91dCcsIHtcbiAgICAgICAgICAgICAgICAgICAgb2s6ICgpID0+IHZpZXdIZWxwZXIuc2hvd1BubChuYW1lLCAuLi5wYXJhbXMpLFxuICAgICAgICAgICAgICAgICAgICBjYW5jZWw6ICgpID0+IHsgfSxcbiAgICAgICAgICAgICAgICAgICAgb2tUZXh0OiAnbG9naW4uYnV0dG9uX3JldHJ5JyxcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB2aWV3SGVscGVyLnNob3dNZXNzYWdlQm94KCd0b2FzdC5sb2FkX3RpbWVvdXQnKVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5vbkV2ZW50SGlkZSgpXG4gICAgICAgIH1cbiAgICB9XG59XG4iXX0=