
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/ResTransitCapDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7bcc8aCkeRMzK/DSwlmyUY5', 'ResTransitCapDescPnlCtrl');
// app/script/view/build/ResTransitCapDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ccclass = cc._decorator.ccclass;
var ResTransitCapDescPnlCtrl = /** @class */ (function (_super) {
    __extends(ResTransitCapDescPnlCtrl, _super);
    function ResTransitCapDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.itemsNode_ = null; // path://root/list/items_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    ResTransitCapDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ResTransitCapDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ResTransitCapDescPnlCtrl.prototype.onEnter = function (data) {
        var keys = Object.keys(Constant_1.RES_TRANSIT_CAP);
        this.itemsNode_.Items(keys, function (it, key) {
            it.setLocaleKey('ui.res_transit_cap_desc', Constant_1.CTYPE_NAME[key], Constant_1.RES_TRANSIT_CAP[key]);
        });
    };
    ResTransitCapDescPnlCtrl.prototype.onRemove = function () {
    };
    ResTransitCapDescPnlCtrl.prototype.onClean = function () {
    };
    ResTransitCapDescPnlCtrl = __decorate([
        ccclass
    ], ResTransitCapDescPnlCtrl);
    return ResTransitCapDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResTransitCapDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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