
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/TreasureListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8e012u3+nhPO4I8jonMwGv4', 'TreasureListPnlCtrl');
// app/script/view/common/TreasureListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var ccclass = cc._decorator.ccclass;
var TreasureListPnlCtrl = /** @class */ (function (_super) {
    __extends(TreasureListPnlCtrl, _super);
    function TreasureListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.listSv_ = null; // path://root_n/list_sv
        _this.buttonNode_ = null; // path://root_n/button_n
        //@end
        _this.list = [];
        return _this;
    }
    TreasureListPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.CHANGE_SEASON] = this.onChangeSeason, _a.enter = true, _a),
        ];
    };
    TreasureListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    TreasureListPnlCtrl.prototype.onEnter = function (list) {
        var _this = this;
        if (!list || list.length === 0) {
            return this.hide();
        }
        this.list = list;
        var mul = this.getTreasureAwardMul();
        this.listSv_.Items(list, function (it, data) {
            var _a;
            it.Data = data;
            var lv = ((_a = data.json) === null || _a === void 0 ? void 0 : _a.lv) || 1, isHasAdd = lv < 10 && !data.rewards.length && !!mul;
            it.Child('name/val').setLocaleKey('ui.treasure_name_' + lv);
            it.Child('name/add', cc.Label).string = isHasAdd ? "(+" + mul + "%)" : '';
            _this.updateItem(it, data);
        });
        this.updateButton();
    };
    TreasureListPnlCtrl.prototype.onRemove = function () {
    };
    TreasureListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/list_sv/view/content/item/claim_be
    TreasureListPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        var _a;
        var it = event.target.parent, uid = (_a = it.Data) === null || _a === void 0 ? void 0 : _a.uid;
        var data = this.list.find(function (m) { return m.uid === uid; });
        if (!data) {
            return;
        }
        else if (data.rewards.length === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TREASURE_NOT_OPEN);
        }
        var items = GameHelper_1.gameHpr.checkRewardFull(data.rewards);
        if (items.length <= 0) {
            this.claimAward(data);
        }
        else if (items.length > 0 && !ViewHelper_1.viewHelper.showResFullNoLongerTip('res_full_tip_desc', items, {
            content: 'ui.res_full_tip_desc',
            okText: 'ui.button_goon',
            select: false,
            ok: function () { return _this.claimAward(data); },
            cancel: function () { }
        })) {
            // return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimAward(data) })
            this.claimAward(data);
        }
    };
    // path://root_n/list_sv/view/content/item/open_be
    TreasureListPnlCtrl.prototype.onClickOpen = function (event, _) {
        var _this = this;
        var _a;
        var it = event.target.parent, uid = (_a = it.Data) === null || _a === void 0 ? void 0 : _a.uid;
        var data = this.list.find(function (m) { return m.uid === uid; });
        if (!data) {
            return;
        }
        else if (data.rewards.length > 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TREASURE_YET_OPEN);
        }
        NetHelper_1.netHelper.reqOpenTreasure({ auid: data.auid, puid: data.puid, uid: data.uid }).then(function (ret) {
            if (ret.err) {
                return ViewHelper_1.viewHelper.showAlert(ret.err);
            }
            audioMgr.playSFX('common/sound_ui_002');
            data.rewards = ret.data.rewards.map(function (m) { return new CTypeObj_1.default().init(m.type, m.id, m.count); });
            GameHelper_1.gameHpr.player.updateTempArmyTreasureInfo(ret.data.treasures, ret.data.armyUid, ret.data.uid);
            eventCenter.emit(EventType_1.default.GUIDE_OPEN_TREASURE); //打开宝箱
            if (_this.isValid) {
                _this.updateItem(it, data);
                _this.updateButton();
            }
        });
    };
    // path://root_n/button_n/open_all_be
    TreasureListPnlCtrl.prototype.onClickOpenAll = function (event, _) {
        var _this = this;
        var treasures = this.list;
        var data = treasures[0];
        if (!data) {
            return;
        }
        GameHelper_1.gameHpr.net.request('game/HD_OpenArmyTreasure', { index: data.index, auid: data.auid }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            var info = res.data;
            if (!info) {
                return;
            }
            audioMgr.playSFX('common/sound_ui_002');
            var obj = {};
            treasures.forEach(function (m) { return obj[m.uid] = m; });
            var armyUid = info.armyUid, pawnTreasures = info.pawnTreasures;
            pawnTreasures.forEach(function (m) {
                m.treasures.forEach(function (t) {
                    var treasure = obj[t.uid];
                    if (treasure) {
                        treasure.rewards = t.rewards.map(function (r) { return new CTypeObj_1.default().init(r.type, r.id, r.count); });
                    }
                });
                GameHelper_1.gameHpr.player.updateTempArmyTreasureInfo(m.treasures, armyUid, m.uid);
            });
            if (_this.isValid) {
                _this.onEnter(treasures);
                _this.updateButton();
            }
        });
    };
    // path://root_n/button_n/claim_all_be
    TreasureListPnlCtrl.prototype.onClickClaimAll = function (event, _) {
        var _this = this;
        var treasures = this.list;
        if (treasures.length === 0) {
            return;
        }
        else if (treasures.some(function (m) { return m.rewards.length === 0; })) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TREASURE_NOT_OPEN);
        }
        var allRewards = [];
        treasures.forEach(function (m) { return GameHelper_1.gameHpr.mergeTypeObjsCount.apply(GameHelper_1.gameHpr, __spread([allRewards], m.rewards)); });
        var items = GameHelper_1.gameHpr.checkRewardFull(allRewards);
        if (items.length <= 0) {
            this.claimArmyAward(treasures, allRewards);
        }
        else if (items.length > 0 && !ViewHelper_1.viewHelper.showResFullNoLongerTip('res_full_tip_desc', items, {
            content: 'ui.res_full_tip_desc',
            okText: 'ui.button_goon',
            select: false,
            ok: function () { return _this.claimArmyAward(treasures, allRewards); },
            cancel: function () { }
        })) {
            // return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimArmyAward(treasures, allRewards) })
            this.claimArmyAward(treasures, allRewards);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    TreasureListPnlCtrl.prototype.onChangeSeason = function () {
        var mul = this.getTreasureAwardMul();
        this.listSv_.content.children.forEach(function (m) {
            var data = m.Data;
            if (data && m.active) {
                m.Child('name/add', cc.Label).string = !data.rewards.length && mul ? "(+" + mul + "%)" : '';
            }
        });
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    TreasureListPnlCtrl.prototype.getTreasureAwardMul = function () {
        // 获取宝箱加成
        var val = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TREASURE_AWARD);
        // 获取季节加成
        val += Math.floor(GameHelper_1.gameHpr.world.getSeason().getEffect(Enums_1.CEffect.TREASURE_AWARD) * 100);
        return val;
    };
    TreasureListPnlCtrl.prototype.updateButton = function () {
        var height = this.listSv_.node.height = Math.min(this.list.length * 108 - 2, 800 - 24);
        if (this.buttonNode_.active = GameHelper_1.gameHpr.getMaxLandCountByPChat() >= Constant_1.OPEN_ALL_TREASURE_MIN_LAND_COUNT && !GameHelper_1.gameHpr.isNoviceMode && this.list.length > 1) {
            height += 128;
            this.buttonNode_.Swih(this.list.some(function (m) { return m.rewards.length === 0; }) ? 'open_all_be' : 'claim_all_be');
        }
        else {
            height += 24;
        }
        if (height !== this.rootNode_.height) {
            this.rootNode_.height = height;
            this.rootNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        }
    };
    TreasureListPnlCtrl.prototype.updateItem = function (it, data) {
        var _a;
        var award = it.Child('award'), desc = it.Child('desc');
        var hasAward = award.active = it.Child('claim_be').active = data.rewards.length > 0;
        var state = hasAward ? 1 : 0;
        ResHelper_1.resHelper.loadIcon('icon/treasure_' + (((_a = data.json) === null || _a === void 0 ? void 0 : _a.lv) || 1) + '_' + state, it.Child('icon'), this.key);
        desc.active = it.Child('open_be').active = !hasAward;
        if (hasAward) {
            data.rewards.sort(function (a, b) { return a.type - b.type; });
            ViewHelper_1.viewHelper.updateItemByCTypes(award, data.rewards);
        }
    };
    TreasureListPnlCtrl.prototype.claimAward = function (data) {
        var _this = this;
        NetHelper_1.netHelper.reqClaimTreasure({ auid: data.auid, puid: data.puid, uid: data.uid }).then(function (ret) {
            if (ret.err) {
                return ViewHelper_1.viewHelper.showAlert(ret.err);
            }
            GameHelper_1.gameHpr.addGainMassage(data.rewards);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(ret.data.rewards);
            GameHelper_1.gameHpr.player.updateTempArmyTreasureInfo(ret.data.treasures, ret.data.armyUid, ret.data.uid);
            eventCenter.emit(EventType_1.default.GUIDE_CLAIM_TREASURE); //领取宝箱宝箱
            if (_this.isValid) {
                _this.list.remove('uid', data.uid);
                if (_this.list.length > 0) {
                    _this.onEnter(_this.list);
                }
                else {
                    _this.hide();
                }
            }
        });
    };
    TreasureListPnlCtrl.prototype.claimArmyAward = function (treasures, allRewards) {
        var _this = this;
        var d = treasures[0];
        GameHelper_1.gameHpr.net.request('game/HD_ClaimArmyTreasure', { index: d.index, auid: d.auid }, true).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            var data = res.data;
            if (!data) {
                return;
            }
            allRewards.sort(function (a, b) { return a.type - b.type; });
            GameHelper_1.gameHpr.addGainMassage(allRewards);
            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
            var armyUid = data.armyUid, pawnTreasures = data.pawnTreasures;
            pawnTreasures.forEach(function (m) {
                GameHelper_1.gameHpr.player.updateTempArmyTreasureInfo(m.treasures, armyUid, m.uid);
            });
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    TreasureListPnlCtrl = __decorate([
        ccclass
    ], TreasureListPnlCtrl);
    return TreasureListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TreasureListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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