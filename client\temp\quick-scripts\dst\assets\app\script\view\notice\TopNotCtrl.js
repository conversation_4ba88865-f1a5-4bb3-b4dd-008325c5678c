
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/TopNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '513dcIK8tlDCrKOaSUe3BuQ', 'TopNotCtrl');
// app/script/view/notice/TopNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GainMessageCmpt_1 = require("../cmpt/GainMessageCmpt");
var ccclass = cc._decorator.ccclass;
var TopNotCtrl = /** @class */ (function (_super) {
    __extends(TopNotCtrl, _super);
    function TopNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.msgNode_ = null; // path://msg_n
        _this.noticeNode_ = null; // path://notice_n
        _this.gainTipNode_ = null; // path://gain_tip_n
        //@end
        _this.NOTICE_MOVE_SPEED = 50;
        _this.noticeItemNode = null;
        _this.noticeEndX = 0;
        _this.noticeDelay = 0;
        _this.msgTextNode = null;
        _this.msgEndX = 0;
        _this.msgDelay = 0;
        _this.notices = []; //公告队列
        _this.sysMsgs = []; //消息队列
        return _this;
    }
    TopNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e;
        return [
            (_a = {}, _a[EventType_1.default.GAME_NOTICE] = this.onGameNotice, _a),
            (_b = {}, _b[EventType_1.default.SYS_MSG_NOTICE] = this.onSysMsgNotice, _b),
            (_c = {}, _c[EventType_1.default.CLEAN_SYS_MSG_NOTICE] = this.onCleanSysMsgNotice, _c),
            (_d = {}, _d[EventType_1.default.ADD_GAIN_MESSAGE] = this.onAddGainMessage, _d),
            (_e = {}, _e[mc.Event.LANGUAGE_CHANGED] = this.onLanguageChanged, _e),
        ];
    };
    TopNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var width;
            return __generator(this, function (_a) {
                this.open();
                this.noticeItemNode = this.noticeNode_.FindChild('mask/item');
                this.msgTextNode = this.msgNode_.FindChild('mask/val');
                width = Math.max(100, Math.floor(cc.winSize.width - 240));
                this.noticeNode_.width = width;
                this.noticeNode_.Child('mask', cc.Widget).updateAlignment();
                this.msgNode_.width = Math.min(1000, width);
                this.msgNode_.Child('mask', cc.Widget).updateAlignment();
                this.noticeNode_.active = false;
                this.msgNode_.active = false;
                this.node.zIndex = 2;
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    TopNotCtrl.prototype.onGameNotice = function (data) {
        this.notices.push(data);
        if (this.notices.length === 1) {
            this.openNotice();
        }
    };
    TopNotCtrl.prototype.onSysMsgNotice = function (data) {
        if (mc.currWindName === 'login') {
            return;
        }
        this.sysMsgs.push(data);
        if (this.sysMsgs.length === 1) {
            this.openSysMsg();
        }
    };
    TopNotCtrl.prototype.onCleanSysMsgNotice = function () {
        this.sysMsgs.length = 0;
        this.msgNode_.active = false;
        this.msgTextNode.Data = null;
    };
    TopNotCtrl.prototype.onAddGainMessage = function (data) {
        this.gainTipNode_.Component(GainMessageCmpt_1.default).add(data);
    };
    TopNotCtrl.prototype.onLanguageChanged = function () {
        if (this.noticeEndX !== 0 && this.noticeItemNode.Data) {
            this.setNoticeText(this.noticeItemNode.Data);
        }
        if (this.msgEndX !== 0 && this.msgTextNode.Data) {
            this.setSysMsgText(this.msgTextNode.Data);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    TopNotCtrl.prototype.openNotice = function () {
        this.noticeNode_.active = true;
        this.noticeNode_.opacity = 250;
        this.noticeDelay = 0;
        this.initNextNotice();
    };
    TopNotCtrl.prototype.closeNotice = function () {
        var _this = this;
        this.noticeEndX = 0;
        cc.tween(this.noticeNode_)
            .to(0.2, { opacity: 0 })
            .call(function () {
            _this.noticeNode_.active = false;
            _this.noticeItemNode.Data = null;
            if (_this.sysMsgs.length > 0) {
                _this.openSysMsg();
            }
        })
            .start();
    };
    TopNotCtrl.prototype.initNextNotice = function () {
        var data = this.noticeItemNode.Data = this.notices[0];
        this.setNoticeText(data);
        this.noticeItemNode.x = this.noticeItemNode.parent.width;
    };
    TopNotCtrl.prototype.setNoticeText = function (data) {
        var _a;
        var msgLbl = this.noticeItemNode.Child('val', cc.Label), trumpetNode = this.noticeItemNode.Child('trumpet');
        var width = 0;
        if (trumpetNode.active = !!data.trumpet) {
            width += trumpetNode.width + 4;
            msgLbl.string = data.nickname + ': ' + data.content;
        }
        else {
            msgLbl.string = assetsMgr.lang('sysMsgText.' + data.id, (_a = data.parames) !== null && _a !== void 0 ? _a : []);
        }
        msgLbl._forceUpdateRenderData();
        width += msgLbl.node.width + 8;
        this.noticeEndX = -width;
    };
    TopNotCtrl.prototype.openSysMsg = function () {
        if (this.noticeNode_.active) {
            return;
        }
        this.msgNode_.active = true;
        this.msgNode_.opacity = 250;
        this.msgDelay = 0;
        this.initNextSysMsg();
    };
    TopNotCtrl.prototype.closeSysMsg = function () {
        var _this = this;
        this.msgEndX = 0;
        cc.tween(this.msgNode_)
            .to(0.2, { opacity: 0 })
            .call(function () {
            _this.msgNode_.active = false;
            _this.msgTextNode.Data = null;
            if (_this.notices.length > 0) {
                _this.openNotice();
            }
        })
            .start();
    };
    TopNotCtrl.prototype.initNextSysMsg = function () {
        var data = this.msgTextNode.Data = this.sysMsgs[0];
        this.setSysMsgText(data);
        this.msgTextNode.x = this.msgTextNode.parent.width;
    };
    TopNotCtrl.prototype.setSysMsgText = function (data) {
        var _a;
        var msgLbl = this.msgTextNode.Component(cc.Label);
        msgLbl.string = assetsMgr.lang('sysMsgText.' + data.id, (_a = data.parames) !== null && _a !== void 0 ? _a : []);
        msgLbl._forceUpdateRenderData();
        var width = msgLbl.node.width * msgLbl.node.scaleX + 8;
        this.msgEndX = -width;
    };
    TopNotCtrl.prototype.update = function (dt) {
        if (this.noticeDelay > 0) {
            this.noticeDelay -= dt;
        }
        else if (this.noticeEndX !== 0) {
            this.noticeItemNode.x -= dt * this.NOTICE_MOVE_SPEED;
            if (this.noticeItemNode.x <= this.noticeEndX) {
                this.notices.shift();
                if (this.notices.length === 0) {
                    this.closeNotice();
                }
                else {
                    this.noticeDelay = 0.5;
                    this.initNextNotice();
                }
            }
        }
        if (this.msgDelay > 0) {
            this.msgDelay -= dt;
        }
        else if (this.msgEndX !== 0) {
            this.msgTextNode.x -= dt * this.NOTICE_MOVE_SPEED;
            if (this.msgTextNode.x <= this.msgEndX) {
                this.sysMsgs.shift();
                if (this.sysMsgs.length === 0) {
                    this.closeSysMsg();
                }
                else {
                    this.msgDelay = 0.5;
                    this.initNextSysMsg();
                }
            }
        }
    };
    TopNotCtrl = __decorate([
        ccclass
    ], TopNotCtrl);
    return TopNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = TopNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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