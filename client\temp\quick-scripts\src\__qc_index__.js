
require('./assets/app/App');
require('./assets/app/core/CCMvc');
require('./assets/app/core/base/BaseLayerCtrl');
require('./assets/app/core/base/BaseLocale');
require('./assets/app/core/base/BaseLogCtrl');
require('./assets/app/core/base/BaseModel');
require('./assets/app/core/base/BaseMvcCtrl');
require('./assets/app/core/base/BaseNoticeCtrl');
require('./assets/app/core/base/BasePnlCtrl');
require('./assets/app/core/base/BaseViewCtrl');
require('./assets/app/core/base/BaseWdtCtrl');
require('./assets/app/core/base/BaseWindCtrl');
require('./assets/app/core/component/AdaptNodeSize');
require('./assets/app/core/component/ButtonEx');
require('./assets/app/core/component/LabelAutoAdaptSize');
require('./assets/app/core/component/LabelDPI');
require('./assets/app/core/component/LabelRollNumber');
require('./assets/app/core/component/LabelSysFont');
require('./assets/app/core/component/LabelTimer');
require('./assets/app/core/component/LabelWaitDot');
require('./assets/app/core/component/LocaleFont');
require('./assets/app/core/component/LocaleLabel');
require('./assets/app/core/component/LocaleRichText');
require('./assets/app/core/component/LocaleSprite');
require('./assets/app/core/component/MultiColor');
require('./assets/app/core/component/MultiFrame');
require('./assets/app/core/component/RichTextEx');
require('./assets/app/core/component/ScrollViewEx');
require('./assets/app/core/component/ScrollViewPlus');
require('./assets/app/core/event/CoreEventType');
require('./assets/app/core/extend/ExtendAnimation');
require('./assets/app/core/extend/ExtendArray');
require('./assets/app/core/extend/ExtendButton');
require('./assets/app/core/extend/ExtendCC');
require('./assets/app/core/extend/ExtendComponent');
require('./assets/app/core/extend/ExtendEditBox');
require('./assets/app/core/extend/ExtendLabel');
require('./assets/app/core/extend/ExtendNode');
require('./assets/app/core/extend/ExtendScrollView');
require('./assets/app/core/extend/ExtendSprite');
require('./assets/app/core/extend/ExtendToggleContainer');
require('./assets/app/core/extend/ExtendVec');
require('./assets/app/core/layer/NoticeLayerCtrl');
require('./assets/app/core/layer/ViewLayerCtrl');
require('./assets/app/core/layer/WindLayerCtrl');
require('./assets/app/core/manage/AssetsMgr');
require('./assets/app/core/manage/AudioMgr');
require('./assets/app/core/manage/ModelMgr');
require('./assets/app/core/manage/NodePoolMgr');
require('./assets/app/core/manage/NoticeCtrlMgr');
require('./assets/app/core/manage/StorageMgr');
require('./assets/app/core/manage/ViewCtrlMgr');
require('./assets/app/core/manage/WindCtrlMgr');
require('./assets/app/core/utils/EventCenter');
require('./assets/app/core/utils/Logger');
require('./assets/app/core/utils/ResLoader');
require('./assets/app/core/utils/Utils');
require('./assets/app/lib/base64');
require('./assets/app/lib/mqttws31');
require('./assets/app/proto/ProtoHelper');
require('./assets/app/script/common/LocalConfig');
require('./assets/app/script/common/ad/BaseRewardAd');
require('./assets/app/script/common/ad/InlandNativeRewardAd');
require('./assets/app/script/common/ad/NativeRewardAd');
require('./assets/app/script/common/ad/ShareAd');
require('./assets/app/script/common/ad/WxRewardAd');
require('./assets/app/script/common/astar/ANode');
require('./assets/app/script/common/astar/AStar4');
require('./assets/app/script/common/astar/AStar8');
require('./assets/app/script/common/astar/AStarConfig');
require('./assets/app/script/common/astar/AStarNode');
require('./assets/app/script/common/astar/AStarRange');
require('./assets/app/script/common/astar/AStep');
require('./assets/app/script/common/astar/SearchCircle');
require('./assets/app/script/common/astar/SearchPoint');
require('./assets/app/script/common/astar/SearchRange');
require('./assets/app/script/common/camera/CameraCtrl');
require('./assets/app/script/common/camera/CameraInertiaCtrl');
require('./assets/app/script/common/constant/CommunityConfig');
require('./assets/app/script/common/constant/Constant');
require('./assets/app/script/common/constant/DataType');
require('./assets/app/script/common/constant/ECode');
require('./assets/app/script/common/constant/Enums');
require('./assets/app/script/common/constant/FrameAnimConf');
require('./assets/app/script/common/constant/Interface');
require('./assets/app/script/common/constant/JsonType');
require('./assets/app/script/common/constant/RechargeConfig');
require('./assets/app/script/common/constant/SceneConf');
require('./assets/app/script/common/constant/VersionDesc');
require('./assets/app/script/common/crypto/ByteArrayMD5');
require('./assets/app/script/common/crypto/CryptoHelper');
require('./assets/app/script/common/crypto/CryptoJS');
require('./assets/app/script/common/crypto/JSEncrypt');
require('./assets/app/script/common/event/EventType');
require('./assets/app/script/common/event/JsbEvent');
require('./assets/app/script/common/event/NetEvent');
require('./assets/app/script/common/event/NotEvent');
require('./assets/app/script/common/helper/AdHelper');
require('./assets/app/script/common/helper/AnimHelper');
require('./assets/app/script/common/helper/AppleHelper');
require('./assets/app/script/common/helper/DBHelper');
require('./assets/app/script/common/helper/DhHelper');
require('./assets/app/script/common/helper/ErrorReportHelper');
require('./assets/app/script/common/helper/EventReportHelper');
require('./assets/app/script/common/helper/FacebookHelper');
require('./assets/app/script/common/helper/GameHelper');
require('./assets/app/script/common/helper/GoogleHelper');
require('./assets/app/script/common/helper/GotoHelper');
require('./assets/app/script/common/helper/GuideHelper');
require('./assets/app/script/common/helper/HotUpdateHelper');
require('./assets/app/script/common/helper/JsbHelper');
require('./assets/app/script/common/helper/LoadProgressHelper');
require('./assets/app/script/common/helper/MapHelper');
require('./assets/app/script/common/helper/MapUionFindHelper');
require('./assets/app/script/common/helper/NetHelper');
require('./assets/app/script/common/helper/PayHelper');
require('./assets/app/script/common/helper/PopupPnlHelper');
require('./assets/app/script/common/helper/ReddotHelper');
require('./assets/app/script/common/helper/ResHelper');
require('./assets/app/script/common/helper/SceneEffectCtrlHelper');
require('./assets/app/script/common/helper/ShareHelper');
require('./assets/app/script/common/helper/TaHelper');
require('./assets/app/script/common/helper/ViewHelper');
require('./assets/app/script/common/helper/WxHelper');
require('./assets/app/script/common/shader/FlashLightShaderCtrl');
require('./assets/app/script/common/shader/OutlineShaderCtrl');
require('./assets/app/script/model/area/AreaCenterModel');
require('./assets/app/script/model/area/AreaObj');
require('./assets/app/script/model/area/ArmyObj');
require('./assets/app/script/model/area/BuffObj');
require('./assets/app/script/model/area/BuildObj');
require('./assets/app/script/model/area/PawnObj');
require('./assets/app/script/model/area/PawnSkillObj');
require('./assets/app/script/model/area/PawnStateObj');
require('./assets/app/script/model/bazaar/BazaarConfig');
require('./assets/app/script/model/bazaar/BazaarModel');
require('./assets/app/script/model/bazaar/MerchantObj');
require('./assets/app/script/model/bazaar/TradingResObj');
require('./assets/app/script/model/behavior/Attack');
require('./assets/app/script/model/behavior/BTConstant');
require('./assets/app/script/model/behavior/BaseAction');
require('./assets/app/script/model/behavior/BaseComposite');
require('./assets/app/script/model/behavior/BaseCondition');
require('./assets/app/script/model/behavior/BaseDecorator');
require('./assets/app/script/model/behavior/BaseNode');
require('./assets/app/script/model/behavior/BehaviorTree');
require('./assets/app/script/model/behavior/BevTreeFactory');
require('./assets/app/script/model/behavior/CanMove');
require('./assets/app/script/model/behavior/CheckBeginBlood');
require('./assets/app/script/model/behavior/CheckBeginDeductHp');
require('./assets/app/script/model/behavior/CheckRoundBegin');
require('./assets/app/script/model/behavior/CheckUseSkillAttack');
require('./assets/app/script/model/behavior/EndRound');
require('./assets/app/script/model/behavior/HasAttackTarget');
require('./assets/app/script/model/behavior/InAttackRange');
require('./assets/app/script/model/behavior/Move');
require('./assets/app/script/model/behavior/Parallel');
require('./assets/app/script/model/behavior/Priority');
require('./assets/app/script/model/behavior/Probability');
require('./assets/app/script/model/behavior/SearchCanAttackTarget');
require('./assets/app/script/model/behavior/SearchTarget');
require('./assets/app/script/model/behavior/Sequence');
require('./assets/app/script/model/book/BookModel');
require('./assets/app/script/model/common/BaseUserInfo');
require('./assets/app/script/model/common/CEffectObj');
require('./assets/app/script/model/common/CTypeObj');
require('./assets/app/script/model/common/NetworkModel');
require('./assets/app/script/model/common/PlaybackModel');
require('./assets/app/script/model/common/PortrayalInfo');
require('./assets/app/script/model/common/PortrayalSkillObj');
require('./assets/app/script/model/common/RandomObj');
require('./assets/app/script/model/common/RankModel');
require('./assets/app/script/model/common/StrategyObj');
require('./assets/app/script/model/common/TaskCondObj');
require('./assets/app/script/model/common/TaskModel');
require('./assets/app/script/model/common/TaskObj');
require('./assets/app/script/model/common/UserModel');
require('./assets/app/script/model/friend/FriendInfo');
require('./assets/app/script/model/friend/FriendModel');
require('./assets/app/script/model/fsp/FSPBattleController');
require('./assets/app/script/model/fsp/FSPModel');
require('./assets/app/script/model/fsp/Fighter');
require('./assets/app/script/model/fsp/MainDoor');
require('./assets/app/script/model/fsp/Tower');
require('./assets/app/script/model/fsp/testConfig');
require('./assets/app/script/model/fsp/test_battle');
require('./assets/app/script/model/fsp/test_battle_land');
require('./assets/app/script/model/fsp/test_battle_new');
require('./assets/app/script/model/fsp/test_playback');
require('./assets/app/script/model/guide/GuideConfig');
require('./assets/app/script/model/guide/GuideModel');
require('./assets/app/script/model/guide/GuideObj');
require('./assets/app/script/model/guide/NoviceAreaObj');
require('./assets/app/script/model/guide/NoviceBTCityObj');
require('./assets/app/script/model/guide/NoviceBTObj');
require('./assets/app/script/model/guide/NoviceConfig');
require('./assets/app/script/model/guide/NoviceDrillPawnObj');
require('./assets/app/script/model/guide/NoviceEnemyObj');
require('./assets/app/script/model/guide/NoviceEquipSlotObj');
require('./assets/app/script/model/guide/NoviceHeroSlotObj');
require('./assets/app/script/model/guide/NoviceMarchObj');
require('./assets/app/script/model/guide/NoviceModel');
require('./assets/app/script/model/guide/NoviceOutputObj');
require('./assets/app/script/model/guide/NovicePawnCureInfoObj');
require('./assets/app/script/model/guide/NovicePawnLevelingObj');
require('./assets/app/script/model/guide/NovicePawnSlotObj');
require('./assets/app/script/model/guide/NovicePolicyObj');
require('./assets/app/script/model/guide/NoviceRecordObj');
require('./assets/app/script/model/guide/NoviceServerModel');
require('./assets/app/script/model/guide/WeakGuideConfig');
require('./assets/app/script/model/guide/WeakGuideModel');
require('./assets/app/script/model/guide/WeakGuideObj');
require('./assets/app/script/model/lobby/LobbyModel');
require('./assets/app/script/model/lobby/SendInviteInfo');
require('./assets/app/script/model/lobby/TeamModel');
require('./assets/app/script/model/lobby/TeammateInfo');
require('./assets/app/script/model/login/LoginModel');
require('./assets/app/script/model/main/AllianceModel');
require('./assets/app/script/model/main/AncientObj');
require('./assets/app/script/model/main/AvoidWarObj');
require('./assets/app/script/model/main/BTCityObj');
require('./assets/app/script/model/main/BTInfoObj');
require('./assets/app/script/model/main/BaseMarchObj');
require('./assets/app/script/model/main/BaseStudyObj');
require('./assets/app/script/model/main/CeriSlotObj');
require('./assets/app/script/model/main/CityObj');
require('./assets/app/script/model/main/EquipEffectObj');
require('./assets/app/script/model/main/EquipInfo');
require('./assets/app/script/model/main/EquipSlotObj');
require('./assets/app/script/model/main/ForgeEquipInfo');
require('./assets/app/script/model/main/GroundModel');
require('./assets/app/script/model/main/HeroSlotObj');
require('./assets/app/script/model/main/MapCellObj');
require('./assets/app/script/model/main/MarchObj');
require('./assets/app/script/model/main/OutputObj');
require('./assets/app/script/model/main/PawnCureInfoObj');
require('./assets/app/script/model/main/PawnDrillInfoObj');
require('./assets/app/script/model/main/PawnLevelingInfoObj');
require('./assets/app/script/model/main/PawnSlotObj');
require('./assets/app/script/model/main/PlayerModel');
require('./assets/app/script/model/main/PolicyObj');
require('./assets/app/script/model/main/SeasonInfo');
require('./assets/app/script/model/main/SmeltEquipInfo');
require('./assets/app/script/model/main/TondenObj');
require('./assets/app/script/model/main/TransitObj');
require('./assets/app/script/model/main/WorldModel');
require('./assets/app/script/model/message/ChatModel');
require('./assets/app/script/model/message/MessageModel');
require('./assets/app/script/model/snailisle/AStar');
require('./assets/app/script/model/snailisle/BaseMapModel');
require('./assets/app/script/model/snailisle/BaseRoleObj');
require('./assets/app/script/model/snailisle/BuildEnums');
require('./assets/app/script/model/snailisle/ISceneMapObj');
require('./assets/app/script/model/snailisle/MapSceneHelper');
require('./assets/app/script/model/snailisle/MoveRoleObj');
require('./assets/app/script/model/snailisle/RoleObj');
require('./assets/app/script/model/snailisle/SBuildObj');
require('./assets/app/script/model/snailisle/SIConstant');
require('./assets/app/script/model/snailisle/SceneBuildObj');
require('./assets/app/script/model/snailisle/SnailIsleModel');
require('./assets/app/script/model/snailisle/StateDataType');
require('./assets/app/script/view/activity/MysteryboxShow101PnlCtrl');
require('./assets/app/script/view/activity/MysteryboxShow102PnlCtrl');
require('./assets/app/script/view/activity/MysteryboxShow103PnlCtrl');
require('./assets/app/script/view/activity/MysteryboxShow104PnlCtrl');
require('./assets/app/script/view/activity/MysteryboxShow105PnlCtrl');
require('./assets/app/script/view/area/AncientBTAnimRoleConf');
require('./assets/app/script/view/area/AncientBuildCmpt');
require('./assets/app/script/view/area/AnimFollowCmpt');
require('./assets/app/script/view/area/AreaArmyPnlCtrl');
require('./assets/app/script/view/area/AreaSelectEmojiPnlCtrl');
require('./assets/app/script/view/area/AreaUIChildPnlCtrl');
require('./assets/app/script/view/area/AreaUIPnlCtrl');
require('./assets/app/script/view/area/AreaWatchChatCmpt');
require('./assets/app/script/view/area/AreaWatchListPnlCtrl');
require('./assets/app/script/view/area/AreaWindCtrl');
require('./assets/app/script/view/area/BaseBuildCmpt');
require('./assets/app/script/view/area/BattleEndPnlCtrl');
require('./assets/app/script/view/area/BattleInfoPnlCtrl');
require('./assets/app/script/view/area/BattleRulePnlCtrl');
require('./assets/app/script/view/area/BuffIconCmpt');
require('./assets/app/script/view/area/BuildCmpt');
require('./assets/app/script/view/area/BuildListPnlCtrl');
require('./assets/app/script/view/area/CityBuildCmpt');
require('./assets/app/script/view/area/DragTouchCmpt');
require('./assets/app/script/view/area/EditArmyNamePnlCtrl');
require('./assets/app/script/view/area/EditBuildPnlCtrl');
require('./assets/app/script/view/area/EditPawnPnlCtrl');
require('./assets/app/script/view/area/HPBarCmpt');
require('./assets/app/script/view/area/PawnAnimConf');
require('./assets/app/script/view/area/PawnAnimationCmpt');
require('./assets/app/script/view/area/PawnCmpt');
require('./assets/app/script/view/area/PawnInfoPnlCtrl');
require('./assets/app/script/view/area/PawnStrategyInfoPnlCtrl');
require('./assets/app/script/view/area/PolicyBuffInfoPnlCtrl');
require('./assets/app/script/view/area/SelectAvatarHeroPnlCtrl');
require('./assets/app/script/view/area/TondenEndPnlCtrl');
require('./assets/app/script/view/area/UpPawnLvPnlCtrl');
require('./assets/app/script/view/build/AlliApplyListPnlCtrl');
require('./assets/app/script/view/build/AlliJobDescPnlCtrl');
require('./assets/app/script/view/build/AlliMemberBattlePnlCtrl');
require('./assets/app/script/view/build/AlliMemberInfoPnlCtrl');
require('./assets/app/script/view/build/AlliPolicySelectPnlCtrl');
require('./assets/app/script/view/build/AllianceMembersPnlCtrl');
require('./assets/app/script/view/build/AvatarDescPnlCtrl');
require('./assets/app/script/view/build/BuildAncientBasePnlCtrl');
require('./assets/app/script/view/build/BuildAncientPnlCtrl');
require('./assets/app/script/view/build/BuildBarracksPnlCtrl');
require('./assets/app/script/view/build/BuildBarracksTipPnlCtrl');
require('./assets/app/script/view/build/BuildBazaarChildPnlCtrl');
require('./assets/app/script/view/build/BuildBazaarPnlCtrl');
require('./assets/app/script/view/build/BuildBazaarRecordPnlCtrl');
require('./assets/app/script/view/build/BuildCityPnlCtrl');
require('./assets/app/script/view/build/BuildDrillgroundPnlCtrl');
require('./assets/app/script/view/build/BuildEmbassyPnlCtrl');
require('./assets/app/script/view/build/BuildFactoryPnlCtrl');
require('./assets/app/script/view/build/BuildGranaryPnlCtrl');
require('./assets/app/script/view/build/BuildHerohallPnlCtrl');
require('./assets/app/script/view/build/BuildHospitalPnlCtrl');
require('./assets/app/script/view/build/BuildMainInfoPnlCtrl');
require('./assets/app/script/view/build/BuildSmithyPnlCtrl');
require('./assets/app/script/view/build/BuildTowerPnlCtrl');
require('./assets/app/script/view/build/BuildWallPnlCtrl');
require('./assets/app/script/view/build/CreateAlliancePnlCtrl');
require('./assets/app/script/view/build/DonateAncientLvPnlCtrl');
require('./assets/app/script/view/build/DonateAncientSUpPnlCtrl');
require('./assets/app/script/view/build/EditAlliNoticePnlCtrl');
require('./assets/app/script/view/build/FixationMenuButtonCmpt');
require('./assets/app/script/view/build/HospitalChanceDescPnlCtrl');
require('./assets/app/script/view/build/LockEquipEffectPnlCtrl');
require('./assets/app/script/view/build/PlayForgeSoundCmpt');
require('./assets/app/script/view/build/ResTransitCapDescPnlCtrl');
require('./assets/app/script/view/build/RestoreForgePnlCtrl');
require('./assets/app/script/view/build/SelectAlliJobPnlCtrl');
require('./assets/app/script/view/build/SelectSmeltEquipPnlCtrl');
require('./assets/app/script/view/build/SendAlliApplyPnlCtrl');
require('./assets/app/script/view/build/SpeedUpCurePnlCtrl');
require('./assets/app/script/view/build/StartStudyTipPnlCtrl');
require('./assets/app/script/view/build/StudySelectPnlCtrl');
require('./assets/app/script/view/cmpt/AdaptMidLineWidthCmpt');
require('./assets/app/script/view/cmpt/AdaptNodeSizeCmpt');
require('./assets/app/script/view/cmpt/AdaptTextLineWidthCmpt');
require('./assets/app/script/view/cmpt/AdaptWidthCmpt');
require('./assets/app/script/view/cmpt/AutoLoadLandCmpt');
require('./assets/app/script/view/cmpt/AutoScrollCmpt');
require('./assets/app/script/view/cmpt/BuildUnlockTipCmpt');
require('./assets/app/script/view/cmpt/ChatContentEventCmpt');
require('./assets/app/script/view/cmpt/ClickTouchCmpt');
require('./assets/app/script/view/cmpt/FollowCameraScaleCmpt');
require('./assets/app/script/view/cmpt/FrameAnimationCmpt');
require('./assets/app/script/view/cmpt/GainMessageCmpt');
require('./assets/app/script/view/cmpt/ITouchCmpt');
require('./assets/app/script/view/cmpt/IgnoreFlashLightCmpt');
require('./assets/app/script/view/cmpt/LabelAutoAnyCmpt');
require('./assets/app/script/view/cmpt/LongClickTouchCmpt');
require('./assets/app/script/view/cmpt/MapTouchCmpt');
require('./assets/app/script/view/cmpt/PawnFrameAnimationCmpt');
require('./assets/app/script/view/cmpt/ReddotCmpt');
require('./assets/app/script/view/cmpt/RichTextAutoAnyCmpt');
require('./assets/app/script/view/cmpt/RollListCmpt');
require('./assets/app/script/view/cmpt/ScrollViewInnerCmpt');
require('./assets/app/script/view/cmpt/ScrollViewOuterCmpt');
require('./assets/app/script/view/cmpt/SelectArrowsCmpt');
require('./assets/app/script/view/cmpt/SelectCellCmpt');
require('./assets/app/script/view/cmpt/TextButtonCmpt');
require('./assets/app/script/view/cmpt/TypeWriterCmpt');
require('./assets/app/script/view/cmpt/ViewGroupNestingCmpt');
require('./assets/app/script/view/common/ActivitiesPnlCtrl');
require('./assets/app/script/view/common/AddAlliMemberPnlCtrl');
require('./assets/app/script/view/common/AddPChatPnlCtrl');
require('./assets/app/script/view/common/AddPopularityTipPnlCtrl');
require('./assets/app/script/view/common/AlliChannelMemberPnlCtrl');
require('./assets/app/script/view/common/BTQueuePnlCtrl');
require('./assets/app/script/view/common/BattlePassBuyExpPnlCtrl');
require('./assets/app/script/view/common/BattlePassBuyPnlCtrl');
require('./assets/app/script/view/common/BattlePassExpNotPnlCtrl');
require('./assets/app/script/view/common/BattlePassHelpPnlCtrl');
require('./assets/app/script/view/common/BindAccountPnlCtrl');
require('./assets/app/script/view/common/BottomPnlCtrl');
require('./assets/app/script/view/common/BuffInfoBoxPnlCtrl');
require('./assets/app/script/view/common/BuyTResTipPnlCtrl');
require('./assets/app/script/view/common/CancelBTPnlCtrl');
require('./assets/app/script/view/common/CancelDrillPnlCtrl');
require('./assets/app/script/view/common/CancelMarchTipPnlCtrl');
require('./assets/app/script/view/common/ChatBarrageCmpt');
require('./assets/app/script/view/common/ChatPnlCtrl');
require('./assets/app/script/view/common/ChatSelectEmojiPnlCtrl');
require('./assets/app/script/view/common/CreateAlliChannelPnlCtrl');
require('./assets/app/script/view/common/CreateArmyPnlCtrl');
require('./assets/app/script/view/common/DescInfoPnlCtrl');
require('./assets/app/script/view/common/DescListPnlCtrl');
require('./assets/app/script/view/common/DescPnlCtrl');
require('./assets/app/script/view/common/EquipBaseInfoBoxPnlCtrl');
require('./assets/app/script/view/common/EquipInfoBoxPnlCtrl');
require('./assets/app/script/view/common/FirstPaySalePnlCtrl');
require('./assets/app/script/view/common/GameStatisticsPnlCtrl');
require('./assets/app/script/view/common/GetGiftPnlCtrl');
require('./assets/app/script/view/common/GetPortrayalPnlCtrl');
require('./assets/app/script/view/common/GuidePnlCtrl');
require('./assets/app/script/view/common/GuideTaskPnlCtrl');
require('./assets/app/script/view/common/MarchQueuePnlCtrl');
require('./assets/app/script/view/common/MarchSettingPnlCtrl');
require('./assets/app/script/view/common/MessageCmpt');
require('./assets/app/script/view/common/MysteryboxRulePnlCtrl');
require('./assets/app/script/view/common/NoLongerTipPnlCtrl');
require('./assets/app/script/view/common/NoticeClickCmpt');
require('./assets/app/script/view/common/NoticePermission1PnlCtrl');
require('./assets/app/script/view/common/NoticePermission2PnlCtrl');
require('./assets/app/script/view/common/NoticePermission3PnlCtrl');
require('./assets/app/script/view/common/NoticePnlCtrl');
require('./assets/app/script/view/common/OtherResDescPnlCtrl');
require('./assets/app/script/view/common/PawnAttrBoxPnlCtrl');
require('./assets/app/script/view/common/PawnCostFactorDescPnlCtrl');
require('./assets/app/script/view/common/PetInfoBoxPnlCtrl');
require('./assets/app/script/view/common/PlayerInfoPnlCtrl');
require('./assets/app/script/view/common/PolicyInfoBoxPnlCtrl');
require('./assets/app/script/view/common/PopularityRecordPnlCtrl');
require('./assets/app/script/view/common/PortrayalBaseInfoPnlCtrl');
require('./assets/app/script/view/common/PortrayalInfoBoxPnlCtrl');
require('./assets/app/script/view/common/PortrayalInfoPnlCtrl');
require('./assets/app/script/view/common/ResDetailsPnlCtrl');
require('./assets/app/script/view/common/ResFullNoLongerTipPnlCtrl');
require('./assets/app/script/view/common/ResFullTipPnlCtrl');
require('./assets/app/script/view/common/RestorePortrayalPnlCtrl');
require('./assets/app/script/view/common/SavePortrayalAttrPnlCtrl');
require('./assets/app/script/view/common/SaveSchemePnlCtrl');
require('./assets/app/script/view/common/SeasonInfoPnlCtrl');
require('./assets/app/script/view/common/SelectPortrayalPnlCtrl');
require('./assets/app/script/view/common/SelectPortrayalPreviewPnlCtrl');
require('./assets/app/script/view/common/SelectTaskRewardPnlCtrl');
require('./assets/app/script/view/common/SendInfoToChatPnlCtrl');
require('./assets/app/script/view/common/SendTrumpetPnlCtrl');
require('./assets/app/script/view/common/ShopBuyGoldTipPnlCtrl');
require('./assets/app/script/view/common/ShopBuyIngotTipPnlCtrl');
require('./assets/app/script/view/common/ShopBuyTipPnlCtrl');
require('./assets/app/script/view/common/ShopPnlCtrl');
require('./assets/app/script/view/common/SkillInfoBoxPnlCtrl');
require('./assets/app/script/view/common/SkinExchangePnlCtrl');
require('./assets/app/script/view/common/SkinExchangeTipPnlCtrl');
require('./assets/app/script/view/common/StaminaDescPnlCtrl');
require('./assets/app/script/view/common/StrategyListBoxPnlCtrl');
require('./assets/app/script/view/common/SubscriptionDescPnlCtrl');
require('./assets/app/script/view/common/TaskTreasureListPnlCtrl');
require('./assets/app/script/view/common/TopCurrencyPnlCtrl');
require('./assets/app/script/view/common/TopPnlCtrl');
require('./assets/app/script/view/common/TransitQueuePnlCtrl');
require('./assets/app/script/view/common/TreasureListPnlCtrl');
require('./assets/app/script/view/common/UIMenuChildPnlCtrl');
require('./assets/app/script/view/common/UIPnlCtrl');
require('./assets/app/script/view/common/UseGoldTipPnlCtrl');
require('./assets/app/script/view/common/VersionDescPnlCtrl');
require('./assets/app/script/view/common/WeakGuidePnlCtrl');
require('./assets/app/script/view/help/NoticeDefaultEquipPnlCtrl');
require('./assets/app/script/view/help/NoticeGuidePnlCtrl');
require('./assets/app/script/view/lobby/FirstNewbieEndTipPnlCtrl');
require('./assets/app/script/view/lobby/FollowDCPnlCtrl');
require('./assets/app/script/view/lobby/GameDetailPnlCtrl');
require('./assets/app/script/view/lobby/GameHistoryPnlCtrl');
require('./assets/app/script/view/lobby/JingyuAnimCmpt');
require('./assets/app/script/view/lobby/LobbyChatPnlCtrl');
require('./assets/app/script/view/lobby/LobbyModeCmpt');
require('./assets/app/script/view/lobby/LobbyWindCtrl');
require('./assets/app/script/view/lobby/LongPressLikeCmpt');
require('./assets/app/script/view/lobby/ModeRuleDescPnlCtrl');
require('./assets/app/script/view/lobby/ReadyInfoPnlCtrl');
require('./assets/app/script/view/lobby/ReadyPosListPnlCtrl');
require('./assets/app/script/view/lobby/RulePositionCmpt');
require('./assets/app/script/view/lobby/SBuildCmpt');
require('./assets/app/script/view/lobby/SceneRoleCmpt');
require('./assets/app/script/view/lobby/SelectFarmTypePnlCtrl');
require('./assets/app/script/view/lobby/SelectMapPosPnlCtrl');
require('./assets/app/script/view/lobby/SelectPlantSeedPnlCtrl');
require('./assets/app/script/view/lobby/SelectWateringPnlCtrl');
require('./assets/app/script/view/lobby/SnailIsleCmpt');
require('./assets/app/script/view/lobby/TWLogoCmpt');
require('./assets/app/script/view/lobby/TeamInvitePnlCtrl');
require('./assets/app/script/view/lobby/TeamListPnlCtrl');
require('./assets/app/script/view/lobby/TextUpdateCmpt');
require('./assets/app/script/view/lobby/UserInfoPnlCtrl');
require('./assets/app/script/view/login/AppUpdateTipPnlCtrl');
require('./assets/app/script/view/login/BanAccountTimeTipPnlCtrl');
require('./assets/app/script/view/login/BgMoveCmpt');
require('./assets/app/script/view/login/CloudCmpt');
require('./assets/app/script/view/login/FeedbackPnlCtrl');
require('./assets/app/script/view/login/HDFeedbackPnlCtrl');
require('./assets/app/script/view/login/LineupTipPnlCtrl');
require('./assets/app/script/view/login/LoginButtonPnlCtrl');
require('./assets/app/script/view/login/LoginRoleAnimCmpt');
require('./assets/app/script/view/login/LoginUIPnlCtrl');
require('./assets/app/script/view/login/LoginWindCtrl');
require('./assets/app/script/view/login/LogoTitleCmpt');
require('./assets/app/script/view/login/LogoutTimeTipPnlCtrl');
require('./assets/app/script/view/login/MaintainTipPnlCtrl');
require('./assets/app/script/view/login/SceneBgAnimCmpt');
require('./assets/app/script/view/login/VersionLowTipPnlCtrl');
require('./assets/app/script/view/login/WxUpdateTipPnlCtrl');
require('./assets/app/script/view/main/AlliFlagPnlCtrl');
require('./assets/app/script/view/main/AntiCheatPnlCtrl');
require('./assets/app/script/view/main/ArmyListPnlCtrl');
require('./assets/app/script/view/main/BattleForecastPnlCtrl');
require('./assets/app/script/view/main/BattleStatisticsPnlCtrl');
require('./assets/app/script/view/main/CaptureTipPnlCtrl');
require('./assets/app/script/view/main/CellDropInfoPnlCtrl');
require('./assets/app/script/view/main/CellInfoCmpt');
require('./assets/app/script/view/main/CellSelectEmojiPnlCtrl');
require('./assets/app/script/view/main/CellTondenInfoPnlCtrl');
require('./assets/app/script/view/main/CityListPnlCtrl');
require('./assets/app/script/view/main/DismantleCityTipPnlCtrl');
require('./assets/app/script/view/main/EnterDirDescPnlCtrl');
require('./assets/app/script/view/main/FirstEnterPnlCtrl');
require('./assets/app/script/view/main/GameOverPnlCtrl');
require('./assets/app/script/view/main/LandScoreDescPnlCtrl');
require('./assets/app/script/view/main/MainWindCtrl');
require('./assets/app/script/view/main/MapAnimNodePool');
require('./assets/app/script/view/main/MapMarkPnlCtrl');
require('./assets/app/script/view/main/MarchCmpt');
require('./assets/app/script/view/main/ModifyMarchSpeedPnlCtrl');
require('./assets/app/script/view/main/NotFinishOrderTipPnlCtrl');
require('./assets/app/script/view/main/PraisePnlCtrl');
require('./assets/app/script/view/main/RiskTipPnlCtrl');
require('./assets/app/script/view/main/SceneEffectCmpt');
require('./assets/app/script/view/main/SceneEffectCtrlCmpt');
require('./assets/app/script/view/main/SeasonLandDiAnim');
require('./assets/app/script/view/main/SeasonLandItemAnim');
require('./assets/app/script/view/main/SeasonSwitchPnlCtrl');
require('./assets/app/script/view/main/SelectArmyPnlCtrl');
require('./assets/app/script/view/main/SelectCitySkinPnlCtrl');
require('./assets/app/script/view/main/SelectTondenArmyPnlCtrl');
require('./assets/app/script/view/main/WorldMapDescPnlCtrl');
require('./assets/app/script/view/main/WorldMapPnlCtrl');
require('./assets/app/script/view/main/WorldMapTouchCmpt');
require('./assets/app/script/view/menu/AchievementListPnlCtrl');
require('./assets/app/script/view/menu/AgreeFriendApplyPnlCtrl');
require('./assets/app/script/view/menu/BlacklistPnlCtrl');
require('./assets/app/script/view/menu/BookCommentPnlCtrl');
require('./assets/app/script/view/menu/BookPnlCtrl');
require('./assets/app/script/view/menu/BookRatingPnlCtrl');
require('./assets/app/script/view/menu/CLoginInfoPnlCtrl');
require('./assets/app/script/view/menu/CollectionEmojiInfoPnlCtrl');
require('./assets/app/script/view/menu/CollectionPnlCtrl');
require('./assets/app/script/view/menu/CollectionSkinInfoPnlCtrl');
require('./assets/app/script/view/menu/CompDebrisPnlCtrl');
require('./assets/app/script/view/menu/ExchangePnlCtrl');
require('./assets/app/script/view/menu/FcmSetPnlCtrl');
require('./assets/app/script/view/menu/FriendInfoPnlCtrl');
require('./assets/app/script/view/menu/GiftBoxAnimPnlCtrl');
require('./assets/app/script/view/menu/GiveGiftPnlCtrl');
require('./assets/app/script/view/menu/LogoutTipPnlCtrl');
require('./assets/app/script/view/menu/MailInfoPnlCtrl');
require('./assets/app/script/view/menu/MailListPnlCtrl');
require('./assets/app/script/view/menu/ModifyFriendNotePnlCtrl');
require('./assets/app/script/view/menu/ModifyNicknamePnlCtrl');
require('./assets/app/script/view/menu/PersonalGameDetailPnlCtrl');
require('./assets/app/script/view/menu/PersonalGameHistoryPnlCtrl');
require('./assets/app/script/view/menu/PersonalPnlCtrl');
require('./assets/app/script/view/menu/PointsetsChancePnlCtrl');
require('./assets/app/script/view/menu/PointsetsPnlCtrl');
require('./assets/app/script/view/menu/RankPnlCtrl');
require('./assets/app/script/view/menu/RankShopPnlCtrl');
require('./assets/app/script/view/menu/ScoreRankDescPnlCtrl');
require('./assets/app/script/view/menu/ScoreRankPnlCtrl');
require('./assets/app/script/view/menu/SelectHeadIconPnlCtrl');
require('./assets/app/script/view/menu/SelectTitlePnlCtrl');
require('./assets/app/script/view/menu/SettingPnlCtrl');
require('./assets/app/script/view/menu/WriteMailPnlCtrl');
require('./assets/app/script/view/notice/AlertNotCtrl');
require('./assets/app/script/view/notice/EventNotCtrl');
require('./assets/app/script/view/notice/LoadingNotCtrl');
require('./assets/app/script/view/notice/MessageBoxNotCtrl');
require('./assets/app/script/view/notice/NetWaitNotCtrl');
require('./assets/app/script/view/notice/PnlWaitNotCtrl');
require('./assets/app/script/view/notice/ReconnectNotCtrl');
require('./assets/app/script/view/notice/TopNotCtrl');
require('./assets/app/script/view/notice/WindWaitNotCtrl');
require('./assets/app/script/view/novice/NoviceGameOverPnlCtrl');
require('./assets/app/script/view/novice/NoviceRiskTipPnlCtrl');
require('./assets/app/script/view/novice/NoviceWindCtrl');
require('./assets/app/script/view/other/ClearlovePnlCtrl');
require('./assets/app/script/view/playback/PlaybackUIPnlCtrl');
require('./assets/app/script/view/playback/PlaybackWindCtrl');
require('./assets/scene/Start');
require('./assets/scene/version');
