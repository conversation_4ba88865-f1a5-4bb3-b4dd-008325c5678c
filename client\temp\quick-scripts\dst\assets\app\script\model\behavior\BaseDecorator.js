
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BaseDecorator.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5215f1fFYBJW6frTjvS4zLK', 'BaseDecorator');
// app/script/model/behavior/BaseDecorator.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseNode_1 = require("./BaseNode");
var BTConstant_1 = require("./BTConstant");
// 装饰节点
var BaseDecorator = /** @class */ (function (_super) {
    __extends(BaseDecorator, _super);
    function BaseDecorator() {
        var _this = _super.call(this) || this;
        _this.child = null;
        _this.type = BTConstant_1.BTType.DECORATOR;
        return _this;
    }
    return BaseDecorator;
}(BaseNode_1.default));
exports.default = BaseDecorator;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQmFzZURlY29yYXRvci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1Q0FBa0M7QUFDbEMsMkNBQXNDO0FBRXRDLE9BQU87QUFDUDtJQUEyQyxpQ0FBUTtJQUkvQztRQUFBLFlBQ0ksaUJBQU8sU0FFVjtRQUxNLFdBQUssR0FBYSxJQUFJLENBQUE7UUFJekIsS0FBSSxDQUFDLElBQUksR0FBRyxtQkFBTSxDQUFDLFNBQVMsQ0FBQTs7SUFDaEMsQ0FBQztJQUNMLG9CQUFDO0FBQUQsQ0FSQSxBQVFDLENBUjBDLGtCQUFRLEdBUWxEIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJhc2VOb2RlIGZyb20gXCIuL0Jhc2VOb2RlXCI7XHJcbmltcG9ydCB7IEJUVHlwZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIjtcclxuXHJcbi8vIOijhemlsOiKgueCuVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBCYXNlRGVjb3JhdG9yIGV4dGVuZHMgQmFzZU5vZGUge1xyXG5cclxuICAgIHB1YmxpYyBjaGlsZDogQmFzZU5vZGUgPSBudWxsXHJcblxyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoKVxyXG4gICAgICAgIHRoaXMudHlwZSA9IEJUVHlwZS5ERUNPUkFUT1JcclxuICAgIH1cclxufSJdfQ==