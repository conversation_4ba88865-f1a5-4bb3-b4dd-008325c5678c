{"version": 3, "sources": ["assets\\app\\script\\view\\novice\\NoviceGameOverPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAyD;AACzD,2DAA0D;AAElD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAmD,yCAAc;IAAjE;QAAA,qEAyEC;QAvEG,0BAA0B;QAClB,eAAS,GAAY,IAAI,CAAA,CAAC,gBAAgB;QAC1C,eAAS,GAAa,IAAI,CAAA,CAAC,8BAA8B;QACzD,kBAAY,GAAY,IAAI,CAAA,CAAC,0BAA0B;QAC/D,MAAM;QAEE,eAAS,GAAa,IAAI,CAAA;;QA4DlC,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IAErH,CAAC;IA/DU,+CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,wCAAQ,GAArB;;;;;;KACC;IAEM,uCAAO,GAAd,UAAe,QAAkB;;QAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QACzB,IAAM,QAAQ,GAAG,EAAE,OAAO,EAAE,oBAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAA;QAC/I,IAAM,KAAK,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/D,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,mBAAmB,GAAG,KAAK,CAAC,CAAA;QACxD,+DAA+D;QAC/D,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAA;QAC7B,OAAO;QACP,IAAI,QAAQ,EAAE;YACV,4FAA4F;YAC5F,wGAAwG;YACxG,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;SAC9C;aAAM;YACH,IAAM,OAAO,GAAG,oBAAO,CAAC,KAAK,CAAC,eAAe,CAAC,oBAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;YACxE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,oBAAoB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtF;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,mBAAmB,EAAE,OAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QAC5F,IAAM,YAAY,GAAG,KAAK,CAAA;QAC1B,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAChD,IAAI,QAAQ,CAAC,MAAM,GAAG,YAAY,IAAI,CAAC,CAAC,MAAM,EAAE;YACtC,IAAA,KAA6B,oBAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC,CAAC,EAAnF,EAAE,QAAA,EAAE,QAAQ,cAAA,EAAE,QAAQ,cAA6D,CAAA;YAC3F,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,CAAC,CAAA;YAC7C,qBAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACjE,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;YACjF,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAA;YACrG,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,CAAA;YAC5I,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAA;YAC/F,QAAQ,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,GAAG,GAAG,QAAQ,CAAC,CAAA;SACrH;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAC9C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,CAAA,EAAE;YACtC,KAAK,CAAC,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC,WAAW,CAAC,CAAA;SACrE;IACL,CAAC;IAEM,wCAAQ,GAAf;QACI,EAAE,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;IAC5C,CAAC;IAEM,uCAAO,GAAd;QACI,EAAE,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,uBAAuB;IACvB,gDAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACrD,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAA;IACtC,CAAC;IAnEgB,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CAyEzC;IAAD,4BAAC;CAzED,AAyEC,CAzEkD,EAAE,CAAC,WAAW,GAyEhE;kBAzEoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class NoviceGameOverPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private rootNode_: cc.Node = null // path://root_n\n    private titleLbl_: cc.Label = null // path://root_n/title/title_l\n    private contentNode_: cc.Node = null // path://root_n/content_n\n    //@end\n\n    private _callback: Function = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(callback: Function) {\n        this._callback = callback\n        const overInfo = { winName: gameHpr.user.getNickname(), closeTime: 0 }, record = { rank: 0, addWarToken: 10, curRankScore: 0, addRankScore: 0 }\n        const isWin = 1, closeTime = this.rootNode_.Child('close_time')\n        this.titleLbl_.setLocaleKey('ui.game_over_win_' + isWin)\n        // this.rootNode_.Child('title', cc.MultiFrame).setFrame(isWin)\n        closeTime.active = !!overInfo\n        // 对局结算\n        if (overInfo) {\n            // this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + isWin, overInfo.winName)\n            // closeTime.setLocaleKey('ui.server_close_desc', gameHpr.millisecondToStringForDay(overInfo.closeTime))\n            closeTime.active = false\n            this.rootNode_.Child('desc').active = false\n        } else {\n            const hasAlli = gameHpr.world.getAlliBaseInfo(gameHpr.alliance.getUid())\n            this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + (hasAlli ? 3 : 2))\n        }\n        this.rootNode_.Child('rank/val').setLocaleKey('ui.game_over_rank', (record?.rank ?? -1) + 1)\n        const isRankServer = false\n        const rankNode = this.contentNode_.Child('rank')\n        if (rankNode.active = isRankServer && !!record) {\n            const { id, winPoint, maxPoint } = gameHpr.resolutionRankScore(record.curRankScore || 0, 1)\n            const addRankScore = record.addRankScore || 0\n            resHelper.loadRankScoreIcon(id, rankNode.Child('icon'), this.key)\n            rankNode.Child('lay/val').setLocaleKey('ui.rank_name_' + (id >= 0 ? id : 'none'))\n            rankNode.Child('lay/point').setLocaleKey('ui.rank_score_num_3', Math.max(0, winPoint - addRankScore))\n            rankNode.Child('lay/add', cc.Label).Color(addRankScore >= 0 ? '#ECFF47' : '#F55756').string = (addRankScore >= 0 ? '+' : '-') + addRankScore\n            rankNode.Child('progress/bar', cc.Sprite).fillRange = maxPoint === -1 ? 1 : winPoint / maxPoint\n            rankNode.Child('progress/text', cc.Label).string = maxPoint === -1 ? (winPoint + '') : (winPoint + '/' + maxPoint)\n        }\n        const award = this.contentNode_.Child('award')\n        if (award.active = !!record?.addWarToken) {\n            award.setLocaleKey('ui.game_over_award_desc2', record.addWarToken)\n        }\n    }\n\n    public onRemove() {\n        cc.log('NoviceGameOverPnlCtrl onRemove')\n    }\n\n    public onClean() {\n        cc.log('NoviceGameOverPnlCtrl onClean')\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://back_lobby_be\n    onClickBackLobby(event: cc.Event.EventTouch, data: string) {\n        this._callback && this._callback()\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}