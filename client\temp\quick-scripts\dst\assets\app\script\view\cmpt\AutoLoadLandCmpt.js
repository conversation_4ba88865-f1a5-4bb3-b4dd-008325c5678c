
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AutoLoadLandCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5bf52FQsVVEPL2G0AofuQ6v', 'AutoLoadLandCmpt');
// app/script/view/cmpt/AutoLoadLandCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 自动加载地块信息
 */
var AutoLoadLandCmpt = /** @class */ (function (_super) {
    __extends(AutoLoadLandCmpt, _super);
    function AutoLoadLandCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.landIcon = '';
        return _this;
    }
    AutoLoadLandCmpt.prototype.onLoad = function () {
        var spr = this.getComponent(cc.Sprite);
        if (spr) {
            spr.spriteFrame = this.getLandIcon(this.landIcon);
        }
    };
    AutoLoadLandCmpt.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandIcon(icon);
    };
    __decorate([
        property
    ], AutoLoadLandCmpt.prototype, "landIcon", void 0);
    AutoLoadLandCmpt = __decorate([
        ccclass
    ], AutoLoadLandCmpt);
    return AutoLoadLandCmpt;
}(cc.Component));
exports.default = AutoLoadLandCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNtcHRcXEF1dG9Mb2FkTGFuZENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQTBEO0FBRXBELElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRTVDOztHQUVHO0FBRUg7SUFBOEMsb0NBQVk7SUFBMUQ7UUFBQSxxRUFlQztRQVpXLGNBQVEsR0FBVyxFQUFFLENBQUE7O0lBWWpDLENBQUM7SUFWRyxpQ0FBTSxHQUFOO1FBQ0ksSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDeEMsSUFBSSxHQUFHLEVBQUU7WUFDTCxHQUFHLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1NBQ3BEO0lBQ0wsQ0FBQztJQUVPLHNDQUFXLEdBQW5CLFVBQW9CLElBQVk7UUFDNUIsT0FBTyxxQkFBUyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQTtJQUN0QyxDQUFDO0lBWEQ7UUFEQyxRQUFRO3NEQUNvQjtJQUhaLGdCQUFnQjtRQURwQyxPQUFPO09BQ2EsZ0JBQWdCLENBZXBDO0lBQUQsdUJBQUM7Q0FmRCxBQWVDLENBZjZDLEVBQUUsQ0FBQyxTQUFTLEdBZXpEO2tCQWZvQixnQkFBZ0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcclxuXHJcbmNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHkgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG4vKipcclxuICog6Ieq5Yqo5Yqg6L295Zyw5Z2X5L+h5oGvXHJcbiAqL1xyXG5AY2NjbGFzc1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBBdXRvTG9hZExhbmRDbXB0IGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcclxuXHJcbiAgICBAcHJvcGVydHlcclxuICAgIHByaXZhdGUgbGFuZEljb246IHN0cmluZyA9ICcnXHJcblxyXG4gICAgb25Mb2FkKCkge1xyXG4gICAgICAgIGNvbnN0IHNwciA9IHRoaXMuZ2V0Q29tcG9uZW50KGNjLlNwcml0ZSlcclxuICAgICAgICBpZiAoc3ByKSB7XHJcbiAgICAgICAgICAgIHNwci5zcHJpdGVGcmFtZSA9IHRoaXMuZ2V0TGFuZEljb24odGhpcy5sYW5kSWNvbilcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBnZXRMYW5kSWNvbihpY29uOiBzdHJpbmcpIHtcclxuICAgICAgICByZXR1cm4gcmVzSGVscGVyLmdldExhbmRJY29uKGljb24pXHJcbiAgICB9XHJcbn0gIl19