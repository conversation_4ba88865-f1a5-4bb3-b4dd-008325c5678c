
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/RechargeConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2b6b0GALDFDiodtSvRrT2mU', 'RechargeConfig');
// app/script/common/constant/RechargeConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RECHARGE_PRICE_USD_CONFIG = void 0;
// 充值价格配置
var RECHARGE_PRICE_USD_CONFIG = {
    'jwm_ingot_1': 0.99,
    'jwm_ingot_2': 4.99,
    'jwm_ingot_3': 9.99,
    'jwm_ingot_4': 19.99,
    'jwm_ingot_5': 49.99,
    'jwm_ingot_6': 99.99,
    'jwm_up_book': 8.99,
    'jwm-card-month': 1.99,
    'jwm-card-quarter': 4.99,
    'jwm-ad-free-month': 6.99,
    'jwm-ad-free-quarter': 19.99,
};
exports.RECHARGE_PRICE_USD_CONFIG = RECHARGE_PRICE_USD_CONFIG;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcY29uc3RhbnRcXFJlY2hhcmdlQ29uZmlnLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNBLFNBQVM7QUFDVCxJQUFNLHlCQUF5QixHQUFHO0lBQzlCLGFBQWEsRUFBRSxJQUFJO0lBQ25CLGFBQWEsRUFBRSxJQUFJO0lBQ25CLGFBQWEsRUFBRSxJQUFJO0lBQ25CLGFBQWEsRUFBRSxLQUFLO0lBQ3BCLGFBQWEsRUFBRSxLQUFLO0lBQ3BCLGFBQWEsRUFBRSxLQUFLO0lBQ3BCLGFBQWEsRUFBRSxJQUFJO0lBQ25CLGdCQUFnQixFQUFFLElBQUk7SUFDdEIsa0JBQWtCLEVBQUUsSUFBSTtJQUN4QixtQkFBbUIsRUFBRSxJQUFJO0lBQ3pCLHFCQUFxQixFQUFFLEtBQUs7Q0FDL0IsQ0FBQTtBQUdHLDhEQUF5QiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxuLy8g5YWF5YC85Lu35qC86YWN572uXG5jb25zdCBSRUNIQVJHRV9QUklDRV9VU0RfQ09ORklHID0ge1xuICAgICdqd21faW5nb3RfMSc6IDAuOTksXG4gICAgJ2p3bV9pbmdvdF8yJzogNC45OSxcbiAgICAnandtX2luZ290XzMnOiA5Ljk5LFxuICAgICdqd21faW5nb3RfNCc6IDE5Ljk5LFxuICAgICdqd21faW5nb3RfNSc6IDQ5Ljk5LFxuICAgICdqd21faW5nb3RfNic6IDk5Ljk5LFxuICAgICdqd21fdXBfYm9vayc6IDguOTksIC8vIOWuneWFuFxuICAgICdqd20tY2FyZC1tb250aCc6IDEuOTksIC8vIOeJueaDoOaciOWNoSDmnIjluqborqLpmIVcbiAgICAnandtLWNhcmQtcXVhcnRlcic6IDQuOTksIC8vIOeJueaDoOaciOWNoSDlraPluqborqLpmIVcbiAgICAnandtLWFkLWZyZWUtbW9udGgnOiA2Ljk5LCAvLyDotoXnuqfmnIjljaHvvIjlhbzlrrnnur/kuIrnmoTvvInmnIjluqborqLpmIVcbiAgICAnandtLWFkLWZyZWUtcXVhcnRlcic6IDE5Ljk5LCAvLyDotoXnuqfmnIjljaHvvIjlhbzlrrnnur/kuIrnmoTvvInlraPluqborqLpmIVcbn1cblxuZXhwb3J0IHtcbiAgICBSRUNIQVJHRV9QUklDRV9VU0RfQ09ORklHLFxufSJdfQ==