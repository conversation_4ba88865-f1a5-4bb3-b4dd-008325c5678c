
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SendInfoToChatPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '85306CDwRpOAI3fcU45kKL1', 'SendInfoToChatPnlCtrl');
// app/script/view/common/SendInfoToChatPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SendInfoToChatPnlCtrl = /** @class */ (function (_super) {
    __extends(SendInfoToChatPnlCtrl, _super);
    function SendInfoToChatPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentLbl_ = null; // path://root/content_l
        _this.chatTypeSelectNode_ = null; // path://root/chat_type_select_be_n
        _this.chatChannelSelectNode_ = null; // path://root/chat_channel_select_be_n
        //@end
        _this.CHAT_TYPE_CHANNEL = {
            0: { type: 2, childType: 1, name: 'temp', key: 'ui.button_temp_chat' },
            1: { type: 2, childType: 0, name: 'friend', key: 'ui.button_friend' },
            2: { type: 1, childType: 1, name: 'team', key: 'ui.title_lobby_chat_1' },
            3: { type: 1, childType: 0, name: 'alliance', key: 'ui.button_alliance' },
            4: { type: 0, childType: 1, name: 'lobby', key: 'ui.lobby' },
            5: { type: 0, childType: 0, name: 'world', key: 'ui.button_battlefield' },
        };
        _this.cb = null;
        _this.user = null;
        _this.chat = null;
        _this.curChatType = '';
        _this.curSelect = {}; // 记录当前选择的频道 {type}:channel
        return _this;
    }
    SendInfoToChatPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SendInfoToChatPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.chat = this.getModel('chat');
                return [2 /*return*/];
            });
        });
    };
    SendInfoToChatPnlCtrl.prototype.onEnter = function (data, cb) {
        var _this = this;
        this.cb = cb;
        this.contentLbl_.setLocaleKey(data.key, data.params || []);
        var mask = this.chatTypeSelectNode_.Child('mask');
        mask.Child('root/type_items_nbe').children.forEach(function (m, i) {
            if (m.name === '0') {
                m.active = _this.chat.getPChatChannels().length > 0;
            }
            else if (m.name === '1') { //好友暂不能分享
                m.active = false;
            }
            else if (m.name === '2') { // 大厅不能分享坐标，装备，战报等，只能分享画像
                m.active = data.key === 'ui.send_portrayal_to_chat_tip' && GameHelper_1.gameHpr.team.hasTeam();
            }
            else if (m.name === '3') {
                m.active = _this.chat.isCanSendAlli();
            }
            else if (m.name === '4') { // 大厅不能分享坐标，装备，战报等，只能分享画像
                m.active = data.key === 'ui.send_portrayal_to_chat_tip';
            }
            var idx = _this.user.getLocalPreferenceData(Enums_1.PreferenceKey.SEND_INFO_TO_CHAT_TYPE) || 5;
            if (data.islobby) { // 大厅有些频道不存在
                if (idx === 3) {
                    idx = 2;
                }
                else if (idx !== 2) {
                    idx = 4;
                }
            }
            var info = _this.CHAT_TYPE_CHANNEL[idx];
            _this.initChannelByType(info.type, info.childType);
            ViewHelper_1.viewHelper.changePopupBoxList(_this.chatTypeSelectNode_, false);
            _this.openChannelBox(false);
        });
    };
    SendInfoToChatPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
        this.openChannelBox(false);
        ViewHelper_1.viewHelper.changePopupBoxList(this.chatTypeSelectNode_, false);
    };
    SendInfoToChatPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/chat_type_select_be_n
    SendInfoToChatPnlCtrl.prototype.onClickChatTypeSelect = function (event, data) {
        audioMgr.playSFX('click');
        this.openChannelBox(false);
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/chat_type_select_be_n/mask/root/type_items_nbe
    SendInfoToChatPnlCtrl.prototype.onClickTypeItems = function (event, _data) {
        var idx = Number(event.target.name);
        ViewHelper_1.viewHelper.changePopupBoxList(this.chatTypeSelectNode_, false);
        var info = this.CHAT_TYPE_CHANNEL[idx];
        this.initChannelByType(info.type, info.childType);
    };
    // path://root/chat_channel_select_be_n
    SendInfoToChatPnlCtrl.prototype.onClickChatChannelSelect = function (event, data) {
        audioMgr.playSFX('click');
        this.openChannelBox(true);
    };
    // path://root/chat_channel_select_be_n/mask/list/view/content/channel_be
    SendInfoToChatPnlCtrl.prototype.onClickChannel = function (event, _data) {
        var data = event.target.Data;
        this.openChannelBox(false);
        this.selectChannel(data, this.curChatType);
    };
    // path://root/chat_type_select_be_n/select_mask_be
    SendInfoToChatPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/chat_channel_select_be_n/channel_mask_be
    SendInfoToChatPnlCtrl.prototype.onClickChannelMask = function (event, data) {
        this.openChannelBox(false);
    };
    // path://root/button/send_be
    SendInfoToChatPnlCtrl.prototype.onClickSend = function (event, data) {
        var target = null, idx = '';
        for (var key in this.CHAT_TYPE_CHANNEL) {
            var item = this.CHAT_TYPE_CHANNEL[key];
            if (item.name === this.curChatType) {
                idx = key;
                target = item;
                break;
            }
        }
        var type = target.type, childType = target.childType;
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.SEND_INFO_TO_CHAT_TYPE, Number(idx));
        this.cb && this.cb(type, childType, this.curSelect);
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ---------------------------------------- custom function ----------------------------------------------------
    SendInfoToChatPnlCtrl.prototype.initChannelByType = function (type, childType) {
        var typeNode = this.chatTypeSelectNode_, channelNode = this.chatChannelSelectNode_, sv = channelNode.Child('mask/list', cc.ScrollView);
        var isSelectedTeam = (type === 1 && childType === 1);
        channelNode.opacity = isSelectedTeam ? 120 : 255;
        channelNode.Component(cc.Button).interactable = !isSelectedTeam;
        if (type === 2) {
            if (childType === 1) { //私聊
                this.curChatType = 'temp';
                this.updateSecretChannels(sv, typeNode, channelNode);
            }
            else if (childType === 0) { //好友
                this.curChatType = 'friend';
            }
        }
        else if (type === 1) {
            if (childType === 1) { //队伍
                this.curChatType = 'team';
                channelNode.Child('val', cc.Label).string = '';
            }
            else if (childType === 0) { //联盟
                this.curChatType = 'alliance';
                this.updateAlliChannels(sv, typeNode, channelNode);
            }
        }
        else if (type === 0) {
            if (childType === 1) { //大厅
                this.curChatType = 'lobby';
                this.updateLobbyChannels(sv, typeNode, channelNode);
            }
            else if (childType === 0) { //战场
                this.curChatType = 'world';
                this.updateWorldChannels(sv, typeNode, channelNode);
            }
        }
    };
    // 刷新对应频道
    SendInfoToChatPnlCtrl.prototype.openChannelBox = function (show) {
        var node = this.chatChannelSelectNode_, sv = node.Child('mask/list', cc.ScrollView);
        node.Child('channel_mask_be').active = show;
        var mask = node.Child('mask');
        if (show) {
            mask.active = true;
            sv.node.y = -mask.height;
            sv.content.Component(cc.Layout).updateLayout();
            cc.tween(sv.node).to(0.15, { y: 4 }, { easing: cc.easing.sineOut }).start();
        }
        else {
            sv.node.y = 4;
            cc.tween(sv.node).to(0.1, { y: -mask.height }).call(function () { return mask.active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: show ? -180 : 0 }).start();
    };
    // 选择对应频道
    SendInfoToChatPnlCtrl.prototype.selectChannel = function (data, type) {
        var typeNode = this.chatTypeSelectNode_, channelNode = this.chatChannelSelectNode_;
        if (type === 'temp') { //私聊
            this.changeSecretChannel(data, typeNode, channelNode);
        }
        else if (type === 'friend') { //好友
        }
        else if (type === 'team') { //队伍
        }
        else if (type === 'alliance') { //联盟
            this.changeAlliChannel(data, typeNode, channelNode);
        }
        else if (type === 'lobby') { //大厅
            this.changeLobbyChannel(data, typeNode, channelNode);
        }
        else if (type === 'world') { //战场
            this.changeWorldChannel(data, typeNode, channelNode);
        }
    };
    // 刷新私聊频道
    SendInfoToChatPnlCtrl.prototype.updateSecretChannels = function (sv, typeNode, channelNode) {
        var list = this.chat.getPChatChannels();
        var channel = this.curSelect[2] || this.chat.getCurPChatChannel() || list[0].channel;
        list.sort(function (a, b) { return (b.count || 0) - (a.count || 0); });
        sv.stopAutoScroll();
        // sv.node.height = Math.min(list.length * 44 + 8, 216)
        sv.Items(list, function (it, data, i) {
            var select = data.channel === channel;
            it.Data = data;
            it.Child('select').active = select;
            it.Child('val', cc.Label).Color(select ? '#564C49' : '#A18876').string = ut.nameFormator(data.name, 7);
            it.Child('line').active = i !== 0;
        });
        // 刷新频道信息
        var data = list.find(function (m) { return (m === null || m === void 0 ? void 0 : m.channel) === channel; });
        if (data) {
            this.changeSecretChannel(data, typeNode, channelNode);
        }
    };
    // 切换私聊频道
    SendInfoToChatPnlCtrl.prototype.changeSecretChannel = function (data, typeNode, channelNode) {
        data && (this.curSelect[2] = data.channel);
        var idx = 0;
        typeNode.Child('val').setLocaleKey(this.CHAT_TYPE_CHANNEL[idx].key);
        typeNode.Child('mask/root/type_items_nbe').children.forEach(function (m) {
            var select = m.name === idx + '';
            m.Child('select').active = select;
            m.Child('val').Color(select ? '#564C49' : '#A18876');
        });
        var name = data ? data.name : assetsMgr.lang('ui.button_temp_chat');
        channelNode.Child('val', cc.Label).string = ut.nameFormator(name, 6);
        channelNode.Child('mask/list/view/content').children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.channel) {
                var select = m.Data.channel === data.channel;
                m.Child('select').active = select;
                m.Child('val').Color(select ? '#564C49' : '#A18876');
            }
        });
    };
    // 刷新联盟频道
    SendInfoToChatPnlCtrl.prototype.updateAlliChannels = function (sv, typeNode, channelNode) {
        var _a;
        var channels = [];
        // 加入主频道
        var mainChannel = { uid: '0', name: assetsMgr.lang('ui.main_channel') };
        channels.push(mainChannel);
        // 加入联盟频道
        channels.pushArr(GameHelper_1.gameHpr.alliance.getChatChannels());
        var curChannelUid = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        sv.Items(channels, function (it, data, i) {
            it.Data = data;
            var has = it.Child('val').active = !!data;
            if (has) {
                it.Child('val', cc.Label).string = data.name;
                it.Child('val').Color(curChannelUid === data.uid ? '#564C49' : '#A18876');
            }
            it.Child('line').active = i !== 0;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            this.changeAlliChannel(mainChannel, typeNode, channelNode);
        }
        else {
            this.changeAlliChannel(data, typeNode, channelNode);
        }
    };
    // 切换联盟频道
    SendInfoToChatPnlCtrl.prototype.changeAlliChannel = function (data, typeNode, channelNode) {
        this.curSelect[1] = data.uid;
        var idx = 3;
        typeNode.Child('val').setLocaleKey(this.CHAT_TYPE_CHANNEL[idx].key);
        typeNode.Child('mask/root/type_items_nbe').children.forEach(function (m) {
            var select = m.name === idx + '';
            m.Child('select').active = select;
            m.Child('val').Color(select ? '#564C49' : '#A18876');
        });
        channelNode.Child('val', cc.Label).string = data.name;
        channelNode.Child('mask/list/view/content').children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('select').active = select;
                m.Child('val').Color(select ? '#564C49' : '#A18876');
            }
        });
    };
    // 刷新世界频道
    SendInfoToChatPnlCtrl.prototype.updateWorldChannels = function (sv, typeNode, channelNode) {
        var _a;
        var sames = ['cn', 'hk', 'tw'], channels = [{ uid: '0', name: assetsMgr.lang('ui.world_chat_channel') }];
        if (sames.has(mc.lang)) {
            channels.push({ uid: 'zh', name: '简繁中文' });
        }
        else {
            var data_1 = Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === mc.lang; });
            channels.push({ uid: mc.lang, name: data_1.text });
        }
        var curChannelUid = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        sv.Items(channels, function (it, data, i) {
            it.Data = data;
            it.Child('val', cc.Label).Color(curChannelUid === data.uid ? '#564C49' : '#A18876').string = data.name;
            it.Child('line').active = i !== 0;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            this.changeWorldChannel(channels[0], typeNode, channelNode);
        }
        else {
            this.changeWorldChannel(data, typeNode, channelNode);
        }
    };
    // 切换世界频道
    SendInfoToChatPnlCtrl.prototype.changeWorldChannel = function (data, typeNode, channelNode) {
        this.curSelect[0] = data.uid;
        var idx = 5;
        typeNode.Child('val').setLocaleKey(this.CHAT_TYPE_CHANNEL[idx].key);
        typeNode.Child('mask/root/type_items_nbe').children.forEach(function (m) {
            var select = m.name === idx + '';
            m.Child('select').active = select;
            m.Child('val').Color(select ? '#564C49' : '#A18876');
        });
        channelNode.Child('val', cc.Label).string = data.name;
        channelNode.Child('mask/list/view/content').children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('select').active = select;
                m.Child('val').Color(select ? '#564C49' : '#A18876');
            }
        });
    };
    // 刷新大厅频道
    SendInfoToChatPnlCtrl.prototype.updateLobbyChannels = function (sv, typeNode, channelNode) {
        var _a;
        var sames = ['cn', 'hk', 'tw'], channels = [{ uid: '0', name: assetsMgr.lang('ui.lobby_chat_channel') }];
        if (sames.has(mc.lang)) {
            channels.push({ uid: 'zh', name: '简繁中文' });
        }
        else {
            var data_2 = Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === mc.lang; });
            channels.push({ uid: mc.lang, name: data_2.text });
        }
        var len = channels.length - 1;
        var curChannelUid = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        sv.Items(channels, function (it, data, i) {
            it.Data = data;
            it.Child('val', cc.Label).Color(curChannelUid === data.uid ? '#564C49' : '#A18876').string = data.name;
            it.Child('line').active = i !== 0;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            this.changeLobbyChannel(channels[0], typeNode, channelNode);
        }
        else {
            this.changeLobbyChannel(data, typeNode, channelNode);
        }
    };
    // 切换大厅频道
    SendInfoToChatPnlCtrl.prototype.changeLobbyChannel = function (data, typeNode, channelNode) {
        this.curSelect[0] = data.uid;
        var idx = 4;
        typeNode.Child('val').setLocaleKey(this.CHAT_TYPE_CHANNEL[idx].key);
        typeNode.Child('mask/root/type_items_nbe').children.forEach(function (m) {
            var select = m.name === idx + '';
            m.Child('select').active = select;
            m.Child('val').Color(select ? '#564C49' : '#A18876');
        });
        channelNode.Child('val', cc.Label).string = data.name;
        channelNode.Child('mask/list/view/content').children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('select').active = select;
                m.Child('val').Color(select ? '#564C49' : '#A18876');
            }
        });
    };
    SendInfoToChatPnlCtrl = __decorate([
        ccclass
    ], SendInfoToChatPnlCtrl);
    return SendInfoToChatPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SendInfoToChatPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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