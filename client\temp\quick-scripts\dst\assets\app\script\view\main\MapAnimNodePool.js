
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/MapAnimNodePool.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0b2edaJ2ZxLl4VDsuEF/3XT', 'MapAnimNodePool');
// app/script/view/main/MapAnimNodePool.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
// 地图的动画节点
var MapAnimNodePool = /** @class */ (function () {
    function MapAnimNodePool() {
        this.root = null;
        this.getPrefabFunc = null;
        this.animNeedTime = -1;
        this.animNamePrdfix = '';
        this.nodeIndexMap = {}; //节点下标map
        this.tempUseNodeIndex = {}; //临时记录使用的下标
        this.curTime = 0;
    }
    MapAnimNodePool.prototype.init = function (root, getPrefabCb) {
        this.root = root;
        this.getPrefabFunc = getPrefabCb;
        return this;
    };
    MapAnimNodePool.prototype.setAnimInfo = function (name, time) {
        this.animNamePrdfix = name;
        this.animNeedTime = time;
    };
    MapAnimNodePool.prototype.reset = function () {
        this.tempUseNodeIndex = {};
    };
    // 加载节点
    MapAnimNodePool.prototype.showNode = function (id, position, play, name) {
        var _this = this;
        var _a;
        var node = this.root, key = id + '_' + position.ID();
        var indexs = this.nodeIndexMap[key] || this.nodeIndexMap[id];
        var i = indexs === null || indexs === void 0 ? void 0 : indexs.find(function (m) { return !_this.tempUseNodeIndex[m]; }), it = null;
        if (i !== undefined) {
            it = node.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.i) === i; });
        }
        else {
            i = node.childrenCount;
            it = cc.instantiate2(this.getPrefabFunc(id), node);
            it.Data = { i: i };
            this.addNodeIndex(key, i);
            this.addNodeIndex(id, i);
        }
        this.tempUseNodeIndex[i] = true;
        it.active = true;
        it.setPosition(position);
        it.zIndex = 1000 - Math.floor(position.y / 80);
        var anim = it.Child('val', cc.Animation);
        if (!anim) {
            return it;
        }
        else if (this.animNeedTime !== -1 && this.animNamePrdfix) {
            anim.play(this.animNamePrdfix + '_' + id, this.curTime % this.animNeedTime);
        }
        else if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.play) !== play || anim.node.Data !== name) {
            it.Data.play = play;
            anim.node.Data = name;
            if (play) {
                anim.play(name);
            }
            else {
                anim.stop();
                if (id === Constant_1.CITY_FORT_NID) { //这里如果是要塞 特殊处理
                    anim.Component(cc.Sprite).spriteFrame = null;
                }
            }
        }
        return it;
    };
    MapAnimNodePool.prototype.addNodeIndex = function (key, index) {
        var arr = this.nodeIndexMap[key];
        if (!arr) {
            arr = this.nodeIndexMap[key] = [];
        }
        arr.push(index);
    };
    // 隐藏其他的节点
    MapAnimNodePool.prototype.hideOtherNode = function () {
        var _this = this;
        this.root.children.forEach(function (it) {
            if (!_this.tempUseNodeIndex[it.Data.i]) {
                it.active = false;
            }
        });
    };
    MapAnimNodePool.prototype.update = function (dt) {
        this.curTime += dt;
    };
    return MapAnimNodePool;
}());
exports.default = MapAnimNodePool;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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