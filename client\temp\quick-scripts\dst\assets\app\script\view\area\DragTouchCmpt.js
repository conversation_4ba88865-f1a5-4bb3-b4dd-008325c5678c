
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/DragTouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9f2ba9BqexB6bvtaovV68g7', 'DragTouchCmpt');
// app/script/view/area/DragTouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ITouchCmpt_1 = require("../cmpt/ITouchCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于拖动的组建
 */
var DragTouchCmpt = /** @class */ (function (_super) {
    __extends(DragTouchCmpt, _super);
    function DragTouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.click = false; //点击状态
        _this.target = null;
        _this.touchEvent = null;
        _this.touchTime = 0; //触摸时间 用于长按
        _this.jiantouTime = 0.1;
        _this.jiantIndex = -1;
        return _this;
    }
    DragTouchCmpt.prototype.init = function (target) {
        this.target = target;
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.SetSwallowTouches(false); //默认开启穿透
        this.jiantouTime = Constant_1.LONG_PRESS_TIME / 4;
        this.touchTime = 0;
        return this;
    };
    DragTouchCmpt.prototype.clean = function () {
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.target = null;
    };
    // 触摸开始
    DragTouchCmpt.prototype.onTouchStart = function (event) {
        if (!this.interactable || GameHelper_1.gameHpr.buildTouchId !== -1 || GameHelper_1.gameHpr.clickTouchId !== -1) {
            return;
        }
        this.touchEvent = event;
        GameHelper_1.gameHpr.buildTouchId = event.getID();
        //
        if (this.touchTime === -1) {
            eventCenter.emit(EventType_1.default.CHANGE_MAP_TOUCH, false);
            this.target.onTouchEvent(Enums_1.DragTouchType.DRAG_PRESS, event);
        }
        else {
            this.touchTime = Constant_1.LONG_PRESS_TIME + 0.2; //如果没有被选中 那么重制点击时间
            cc.tween(this.target.getAnimTarget()).to(0.1, { scale: 1.05 }).start();
        }
    };
    DragTouchCmpt.prototype.onTouchMove = function (event) {
        if (GameHelper_1.gameHpr.buildTouchId !== event.getID() || !this.interactable || !this.touchEvent) {
            return;
        }
        // 表示选中了 才可以拖动
        if (this.touchTime === -1) {
            this.target.onTouchEvent(Enums_1.DragTouchType.DRAG_MOVE, event);
        }
        else if (this.touchTime !== 0 && CameraCtrl_1.cameraCtrl.convertWorldSub(event.getStartLocation(), event.getLocation()).mag() > Constant_1.CLICK_SPACE) {
            GameHelper_1.gameHpr.buildTouchId = -1;
            this.cancel(); //如果还没有选中 那么看是不是移动了
        }
    };
    DragTouchCmpt.prototype.onTouchEnd = function (event) {
        if (GameHelper_1.gameHpr.buildTouchId !== event.getID() || !this.interactable || !this.touchEvent) {
            return;
        }
        GameHelper_1.gameHpr.buildTouchId = -1;
        eventCenter.emit(EventType_1.default.CHANGE_MAP_TOUCH, true);
        if (this.touchTime === -1) {
            return; //如果选中了 直接返回
        }
        else if (this.touchTime > 0) {
            this.cancel();
            this.target.onTouchEvent(Enums_1.DragTouchType.CLICK, event);
        }
        else {
            this.cancel();
        }
    };
    // 触摸取消
    DragTouchCmpt.prototype.onTouchCancel = function (event) {
        if (GameHelper_1.gameHpr.buildTouchId !== event.getID() || !this.interactable || !this.touchEvent) {
            return;
        }
        GameHelper_1.gameHpr.buildTouchId = -1;
        eventCenter.emit(EventType_1.default.CHANGE_MAP_TOUCH, true);
        if (this.touchTime !== -1) {
            this.cancel();
        }
    };
    // 设置是否可以点击选择
    DragTouchCmpt.prototype.setCanClickSelect = function (val) {
        this.click = val;
    };
    DragTouchCmpt.prototype.cancel = function () {
        this.touchTime = 0;
        this.jiantIndex = -1;
        if (this.node.IsSwallowTouches()) {
            this.node.SetSwallowTouches(false);
        }
        this.touchEvent = null;
        cc.tween(this.target.getAnimTarget()).to(0.1, { scale: 1 }).start();
        eventCenter.emit(EventType_1.default.SHOW_BUILD_JIANTOU, null, -1);
    };
    DragTouchCmpt.prototype.select = function () {
        this.touchTime = -1;
        // cc.tween(this.target.body).to(0.1, { scale: 1 }).start()
        this.node.SetSwallowTouches(true);
    };
    DragTouchCmpt.prototype.update = function (dt) {
        if (!this.interactable || this.touchTime <= 0 || this.click) {
            return;
        }
        this.touchTime -= dt;
        if (this.touchTime <= 0 && !!this.touchEvent) {
            this.select();
            eventCenter.emit(EventType_1.default.CHANGE_MAP_TOUCH, false);
            eventCenter.emit(EventType_1.default.SHOW_BUILD_JIANTOU, null, -1);
            this.target.onTouchEvent(Enums_1.DragTouchType.LONG_PRESS, this.touchEvent);
        }
        else {
            var index = Math.floor((Constant_1.LONG_PRESS_TIME - this.touchTime) / this.jiantouTime);
            if (index < 0 || this.jiantIndex === index) {
                return;
            }
            this.jiantIndex = index;
            if (index > 0) {
                eventCenter.emit(EventType_1.default.SHOW_BUILD_JIANTOU, this.target, index - 1);
            }
        }
    };
    DragTouchCmpt = __decorate([
        ccclass
    ], DragTouchCmpt);
    return DragTouchCmpt;
}(ITouchCmpt_1.default));
exports.default = DragTouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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