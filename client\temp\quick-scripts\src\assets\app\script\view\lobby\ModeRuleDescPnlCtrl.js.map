{"version": 3, "sources": ["assets\\app\\script\\view\\lobby\\ModeRuleDescPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAyD;AACzD,6DAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAElC,IAAM,cAAc,GAAG;IACnB;QACI,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,UAAU;QACV,yBAAyB;KAC5B;IACD;QACI,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,UAAU;QACV,yBAAyB;KAC5B;IACD;QACI,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,yBAAyB;QACzB,UAAU;QACV,yBAAyB;KAC5B;CACJ,CAAA;AAGD;IAAiD,uCAAc;IAA/D;QAAA,qEA4EC;QA1EG,0BAA0B;QAClB,eAAS,GAAa,IAAI,CAAA,CAAC,4BAA4B;QACvD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QACrD,eAAS,GAAY,IAAI,CAAA,CAAC,+BAA+B;QACzD,sBAAgB,GAAa,IAAI,CAAA,CAAC,sDAAsD;QAChG,MAAM;QAEE,aAAO,GAAW,CAAC,CAAA;;IAmE/B,CAAC;IAjEU,6CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,sCAAQ,GAArB;;;;;;KACC;IAEM,qCAAO,GAAd,UAAe,IAAY;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;YACxE,IAAM,UAAU,GAAG,IAAI,KAAK,UAAU,CAAA;YACtC,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YACzD,IAAI,CAAC,UAAU,EAAE;gBACb,IAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC7B,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;aAC5C;QACL,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAC9B,CAAC;IAEM,sCAAQ,GAAf;IACA,CAAC;IAEM,qCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,iEAAiE;IACjE,2CAAa,GAAb,UAAc,KAA0B,EAAE,KAAa;QACnD,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACrC,uBAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,wCAAwC;IACxC,gDAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QACvD,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IACzD,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAGjH,OAAO;IACC,iDAAmB,GAA3B;QAAA,iBAgBC;QAfG,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAA;QAC/B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QACnH,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACjF,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;QACpB,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACrC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAA;QACtB,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,4BAA4B,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,YAAA,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,EAAa;gBAAX,GAAG,SAAA,EAAE,IAAI,UAAA;YAC9F,IAAM,IAAI,GAAU,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,KAAI,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;YACxD,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAA;YACnB,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAA;YACtB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;gBACzC,uBAAU,CAAC,qBAAqB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBACpD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IA3EgB,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CA4EvC;IAAD,0BAAC;CA5ED,AA4EC,CA5EgD,EAAE,CAAC,WAAW,GA4E9D;kBA5EoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { gameHpr } from \"../../common/helper/GameHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\n\nconst { ccclass } = cc._decorator;\n\nconst MODE_RULE_DESC = [\n    [ //自由\n        'ui.server_rule_desc_1_1',\n        'ui.server_rule_desc_2_1',\n        'ui.server_rule_desc_3_1',\n        'ui.server_rule_desc_4_1',\n        'wartoken',\n        'ui.server_rule_desc_5_0',\n    ],\n    [ //新手\n        'ui.server_rule_desc_1_0',\n        'ui.server_rule_desc_2_1',\n        'ui.server_rule_desc_3_0',\n        'ui.server_rule_desc_4_1',\n        'wartoken',\n        'ui.server_rule_desc_5_0',\n    ],\n    [ //排位\n        'ui.server_rule_desc_1_0',\n        'ui.server_rule_desc_2_0',\n        'ui.server_rule_desc_3_0',\n        'ui.server_rule_desc_4_1',\n        'wartoken',\n        'ui.server_rule_desc_5_1',\n    ],\n]\n\n@ccclass\nexport default class ModeRuleDescPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private titleLbl_: cc.Label = null // path://root/title/title_l\n    private contentNode_: cc.Node = null // path://root/content_n\n    private listNode_: cc.Node = null // path://root/content_n/list_n\n    private historyTitleLbl_: cc.Label = null // path://root/content_n/history/title/history_title_l\n    //@end\n\n    private curMode: number = 0\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(mode: number) {\n        this.curMode = mode\n        this.titleLbl_.setLocaleKey('ui.server_rule_title_' + mode)\n        this.listNode_.Items(MODE_RULE_DESC[mode] || MODE_RULE_DESC[0], (it, text, i) => {\n            const isWartoken = text === 'wartoken'\n            const desc = it.Swih(isWartoken ? 'wartoken' : 'desc')[0]\n            if (!isWartoken) {\n                const no = Math.min(i + 1, 5)\n                desc.setLocaleKey('ui.no_desc', no, text)\n            }\n        })\n        this.showGameHistoryList()\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/content_n/history/list/view/content/item/detail_be\n    onClickDetail(event: cc.Event.EventTouch, _data: string) {\n        const data = event.target.parent.Data\n        viewHelper.showPnl('lobby/GameDetail', data)\n    }\n\n    // path://root/content_n/more_history_be\n    onClickMoreHistory(event: cc.Event.EventTouch, data: string) {\n        viewHelper.showPnl('lobby/GameHistory', this.curMode)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n\n    // 历史对局\n    private showGameHistoryList() {\n        const serverType = this.curMode\n        this.historyTitleLbl_.setLocaleKey('ui.title_game_history', [assetsMgr.lang('ui.title_server_name_' + serverType)])\n        const node = this.contentNode_.Child('history/list'), empty = node.Child('empty')\n        empty.active = false\n        const content = node.Child('content')\n        content.active = false\n        gameHpr.net.request('chat/HD_GetGameHistoryList', { page: 1, serverType }, true).then(({ err, data }) => {\n            const list: any[] = data?.datas || [], len = list.length\n            empty.active = !len\n            content.active = !!len\n            content.Items(list.splice(0, 3), (it, info, i) => { // 取前三个\n                viewHelper.updateGameHistoryItem(it, info, this.key)\n                it.Child('line').active = i < 2\n            })\n        })\n    }\n}\n"]}