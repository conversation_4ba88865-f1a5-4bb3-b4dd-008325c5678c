
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/InAttackRange.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '78b2bomR5BKG4UtRMB5RfkA', 'InAttackRange');
// app/script/model/behavior/InAttackRange.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseCondition_1 = require("./BaseCondition");
var BTConstant_1 = require("./BTConstant");
// 是否在攻击范围
var InAttackRange = /** @class */ (function (_super) {
    __extends(InAttackRange, _super);
    function InAttackRange() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    InAttackRange.prototype.onTick = function (dt) {
        if (!this.target.attackTarget) {
            return BTConstant_1.BTState.FAILURE;
        }
        return this.target.checkInMyAttackRange(this.target.attackTarget) ? BTConstant_1.BTState.SUCCESS : BTConstant_1.BTState.FAILURE;
    };
    return InAttackRange;
}(BaseCondition_1.default));
exports.default = InAttackRange;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcSW5BdHRhY2tSYW5nZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxpREFBNEM7QUFDNUMsMkNBQXVDO0FBRXZDLFVBQVU7QUFDVjtJQUEyQyxpQ0FBYTtJQUF4RDs7SUFRQSxDQUFDO0lBTlUsOEJBQU0sR0FBYixVQUFjLEVBQVU7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFO1lBQzNCLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUMsb0JBQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQ3pHLENBQUM7SUFDTCxvQkFBQztBQUFELENBUkEsQUFRQyxDQVIwQyx1QkFBYSxHQVF2RCIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCYXNlQ29uZGl0aW9uIGZyb20gXCIuL0Jhc2VDb25kaXRpb25cIjtcbmltcG9ydCB7IEJUU3RhdGUgfSBmcm9tIFwiLi9CVENvbnN0YW50XCI7XG5cbi8vIOaYr+WQpuWcqOaUu+WHu+iMg+WbtFxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgSW5BdHRhY2tSYW5nZSBleHRlbmRzIEJhc2VDb25kaXRpb24ge1xuXG4gICAgcHVibGljIG9uVGljayhkdDogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy50YXJnZXQuYXR0YWNrVGFyZ2V0KSB7XG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5GQUlMVVJFXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMudGFyZ2V0LmNoZWNrSW5NeUF0dGFja1JhbmdlKHRoaXMudGFyZ2V0LmF0dGFja1RhcmdldCkgPyBCVFN0YXRlLlNVQ0NFU1MgOiBCVFN0YXRlLkZBSUxVUkVcbiAgICB9XG59Il19