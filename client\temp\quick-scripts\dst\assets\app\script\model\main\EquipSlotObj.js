
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/EquipSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '33919bWitdFPLwXVhK93NG7', 'EquipSlotObj');
// app/script/model/main/EquipSlotObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var BaseStudyObj_1 = require("./BaseStudyObj");
// 一个装备槽位
var EquipSlotObj = /** @class */ (function (_super) {
    __extends(EquipSlotObj, _super);
    function EquipSlotObj() {
        var _this = _super.call(this) || this;
        _this.equip = null;
        _this.json = null;
        _this.forgeCost = [];
        _this.studyType = Enums_1.StudyType.EQUIP;
        return _this;
    }
    EquipSlotObj.prototype.init = function () {
        this.json = assetsMgr.getJsonData('equipBase', this.id);
        if (this.json) {
            this.forgeCost = GameHelper_1.gameHpr.stringToCTypes(this.json.forge_cost);
        }
        return this;
    };
    EquipSlotObj.prototype.getUIStudyType = function () {
        if (Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[this.lv]) {
            return Enums_1.StudyType.EXCLUSIVE;
        }
        return this.studyType; //子类实现
    };
    Object.defineProperty(EquipSlotObj.prototype, "exclusive_pawn", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.exclusive_pawn) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EquipSlotObj.prototype, "forgeTime", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.forge_time) || 0; },
        enumerable: false,
        configurable: true
    });
    EquipSlotObj.prototype.setEquip = function (equip) {
        this.equip = equip;
        return this;
    };
    EquipSlotObj.prototype.getEquipUid = function () { var _a; return ((_a = this.equip) === null || _a === void 0 ? void 0 : _a.uid) || this.uid; };
    return EquipSlotObj;
}(BaseStudyObj_1.default));
exports.default = EquipSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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