
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/GainMessageCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6c28b8HW3FHzbipzvh+cZQQ', 'GainMessageCmpt');
// app/script/view/cmpt/GainMessageCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var GainMessageCmpt = /** @class */ (function (_super) {
    __extends(GainMessageCmpt, _super);
    function GainMessageCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.OUT_OFFSET_X = 12; //在外面初始x的偏移
        _this.item = null;
        _this.itemPool = []; //消息节点池子
        _this.datas = [];
        return _this;
    }
    GainMessageCmpt.prototype.onLoad = function () {
        this.item = this.FindChild('item');
        this.item.parent = null;
        this.itemPool.push(this.item);
    };
    GainMessageCmpt.prototype.onDestroy = function () {
        while (this.itemPool.length > 0) {
            this.itemPool.pop().destroy();
        }
        ut.destroyNode(this.item);
        this.item = null;
    };
    // 添加一个消息
    GainMessageCmpt.prototype.add = function (data) {
        var _this = this;
        if (!data) {
            return;
        }
        else if (data.type !== Enums_1.CType.CEREAL
            && data.type !== Enums_1.CType.TIMBER
            && data.type !== Enums_1.CType.STONE
            && data.type !== Enums_1.CType.GOLD
            && data.type !== Enums_1.CType.INGOT
            && data.type !== Enums_1.CType.WAR_TOKEN
            && data.type !== Enums_1.CType.EXP_BOOK
            && data.type !== Enums_1.CType.IRON
            && data.type !== Enums_1.CType.UP_SCROLL
            && data.type !== Enums_1.CType.FIXATOR
            && data.type !== Enums_1.CType.RANK_COIN) {
            return;
        }
        else if (GameHelper_1.gameHpr.isNoviceMode) {
        }
        else if (GameHelper_1.gameHpr.getSid() === 0 && (data.type !== Enums_1.CType.GOLD
            && data.type !== Enums_1.CType.INGOT
            && data.type !== Enums_1.CType.WAR_TOKEN
            && data.type !== Enums_1.CType.RANK_COIN)) {
            return;
        }
        this.datas.push(data);
        if (this.datas.length === 1) {
            ut.wait(0.25, this).then(function () { return _this.isValid && _this.show(); });
        }
    };
    GainMessageCmpt.prototype.getItem = function () {
        var node = this.itemPool.pop() || cc.instantiate(this.item);
        node.parent = this.node;
        node.active = true;
        node.stopAllActions();
        node.opacity = 230;
        return node;
    };
    GainMessageCmpt.prototype.putItem = function (node) {
        node.parent = null;
        node.Data = null;
        this.itemPool.push(node);
    };
    // 显示一条信息
    GainMessageCmpt.prototype.show = function () {
        var _this = this;
        var data = this.datas[0];
        var it = this.getItem();
        it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(data.type);
        var lable = it.Child('val', cc.Label);
        lable.string = data.count + '';
        lable._forceUpdateRenderData();
        it.Component(cc.Layout).updateLayout();
        var x = it.width + this.OUT_OFFSET_X, y = it.height * 0.5;
        it.setPosition(x, y);
        // 出来
        cc.tween(it)
            .to(0.2, { x: -12 }, { easing: cc.easing.sineOut }) //先出来
            .to(1.8, { y: y + 220, opacity: 50 } /* , { easing: cc.easing.sineIn } */) //上去
            .call(function () { return _this.isValid && _this.putItem(it); })
            .start();
        // 下一个出来
        ut.wait(0.33, this).then(function () {
            if (_this.isValid) {
                _this.datas.shift();
                if (_this.datas.length > 0 && _this.isValid) {
                    _this.show();
                }
            }
        });
    };
    GainMessageCmpt = __decorate([
        ccclass
    ], GainMessageCmpt);
    return GainMessageCmpt;
}(cc.Component));
exports.default = GainMessageCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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