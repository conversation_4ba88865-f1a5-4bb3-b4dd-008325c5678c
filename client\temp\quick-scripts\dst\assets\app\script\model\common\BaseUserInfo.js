
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/BaseUserInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fb6f4KAJIpND7ItOh3IL+fn', 'BaseUserInfo');
// app/script/model/common/BaseUserInfo.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 基础user信息
var BaseUserInfo = /** @class */ (function () {
    function BaseUserInfo() {
        this.uid = '';
        this.nickname = '';
        this.headIcon = '';
        this.titleInfo = null; //称号
        this.personalDesc = null; //个人简介
        this.popularityInfo = null; //人气信息
        this.rankScoreInfo = null; //评分信息
        this.totalGameCount = null; //总局数
    }
    BaseUserInfo.prototype.init = function (data) {
        this.uid = data.uid;
        this.nickname = data.nickname || data.senderNickname;
        this.headIcon = data.headIcon || data.senderHeadicon;
        return this;
    };
    BaseUserInfo.prototype.set = function (data) {
        this.nickname = data.nickname;
        this.headIcon = data.headIcon;
        return this;
    };
    return BaseUserInfo;
}());
exports.default = BaseUserInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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