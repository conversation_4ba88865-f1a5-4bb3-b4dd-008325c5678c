
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/CollectionSkinInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '46128CoO7ZNJJWcv2DOd12s', 'CollectionSkinInfoPnlCtrl');
// app/script/view/menu/CollectionSkinInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var FrameAnimConf_1 = require("../../common/constant/FrameAnimConf");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GotoHelper_1 = require("../../common/helper/GotoHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PawnFrameAnimationCmpt_1 = require("../cmpt/PawnFrameAnimationCmpt");
var ccclass = cc._decorator.ccclass;
var CollectionSkinInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CollectionSkinInfoPnlCtrl, _super);
    function CollectionSkinInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.bgNode_ = null; // path://root/bg_n
        _this.nameNode_ = null; // path://root/name_n
        _this.iconNode_ = null; // path://root/icon_n
        _this.timeNode_ = null; // path://root/time_n
        _this.skinExchangeNode_ = null; // path://root/skin_exchange_be_n
        _this.nextNode_ = null; // path://root/next_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.json = null;
        _this.animCmpt = null;
        _this.type = '';
        _this.list = [];
        _this.itemSkin = null;
        _this.curPage = 0;
        _this.maxPage = 0;
        _this.cb = null;
        return _this;
    }
    CollectionSkinInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CollectionSkinInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        this.updateViewInfo(data);
    };
    CollectionSkinInfoPnlCtrl.prototype.onRemove = function () {
    };
    CollectionSkinInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/lay/give_be
    CollectionSkinInfoPnlCtrl.prototype.onClickGive = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/Personal', 0);
    };
    // path://root/buttons_n/lay/use_be
    CollectionSkinInfoPnlCtrl.prototype.onClickUse = function (event, _data) {
        if (GameHelper_1.gameHpr.isInLobby()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_game_use');
        }
        else if (this.type === 'pawn_skin') {
            if (!this.checkPawnUnlock()) {
                return ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [ResHelper_1.resHelper.getPawnName(this.json.pawn_id)] });
            }
            this.syncInfoToServer();
        }
        else {
            this.changeCitySkin();
        }
    };
    // path://root/buttons_n/buy_be
    CollectionSkinInfoPnlCtrl.prototype.onClickBuy = function (event, _data) {
        var _a;
        this.cb && this.cb(((_a = this.list[this.curPage]) === null || _a === void 0 ? void 0 : _a.json) || this.list[this.curPage]);
        this.hide();
    };
    // path://root/skin_exchange_be_n
    CollectionSkinInfoPnlCtrl.prototype.onClickSkinExchange = function (event, _data) {
        var _this = this;
        var _a;
        var id = (_a = assetsMgr.getJson('pawnSkin').datas.find(function (m) { return m.value.toString().includes(_this.json.id + ','); })) === null || _a === void 0 ? void 0 : _a.id;
        ViewHelper_1.viewHelper.showPnl('common/SkinExchange', id);
    };
    // path://root/next_n/next_page_be@0
    CollectionSkinInfoPnlCtrl.prototype.onClickNextPage = function (event, data) {
        var _a;
        var add = data === '0' ? -1 : 1;
        var curPage = this.curPage || 0, maxPage = this.maxPage;
        var index = ut.loopValue(curPage + add, maxPage);
        if (index !== curPage) {
            this.curPage = index;
            var skins = (_a = this.list[index]) === null || _a === void 0 ? void 0 : _a.itemSkins;
            this.updateViewInfo({ list: this.list, type: this.type, index: this.curPage, skins: skins });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CollectionSkinInfoPnlCtrl.prototype.checkPawnUnlock = function () {
        return GameHelper_1.gameHpr.player.getAllCanRecruitPawnIds().has(this.json.pawn_id);
    };
    CollectionSkinInfoPnlCtrl.prototype.updateViewInfo = function (data) {
        var _a, _b;
        this.type = data.type;
        this.list = data.list;
        this.itemSkin = ((_a = data.skins) === null || _a === void 0 ? void 0 : _a.length) > 0 ? data.skins[0] : null;
        this.curPage = data.index || 0;
        this.maxPage = this.list.length;
        this.json = ((_b = this.list[this.curPage]) === null || _b === void 0 ? void 0 : _b.json) || this.list[this.curPage];
        this.titleLbl_.setLocaleKey(this.json.desc);
        var isPawnSkin = this.type === 'pawn_skin';
        this.nextNode_.active = !!(this.maxPage - 1);
        this.nameNode_.active = isPawnSkin;
        this.timeNode_.active = isPawnSkin && !!this.itemSkin;
        this.bgNode_.Swih(isPawnSkin ? 'pawn' : 'city');
        this.skinExchangeNode_.active = isPawnSkin && this.json.cond > 102 && this.json.type !== 5; // 101牛仔 102机甲没有炫彩且隐藏皮肤不显示
        if (isPawnSkin) {
            var skin = this.itemSkin, state = skin === null || skin === void 0 ? void 0 : skin.state;
            var unlockList = GameHelper_1.gameHpr.user.getUnlockPawnSkinIds(), have = unlockList.has(this.json.id);
            if (this.json.cond > 100 || (this.json.cond === 5 && !have)) { // 盲盒皮肤 || 5 炫彩皮肤(一旦拥有会和盲盒皮肤分开存放)
                if (skin) { // 已经有了
                    if (state < 0) { // 封禁中
                        var cond = this.buttonsNode_.Swih('cond')[0];
                        cond.Child('val').setLocaleKey('toast.ban_item_skin');
                    }
                    else { // 正常 || 锁定中
                        var lay = this.buttonsNode_.Swih('lay')[0];
                        lay.Swih('', true);
                        var giveBtn = lay.Child('give_be', cc.Button);
                        var canGive = state === 0;
                        giveBtn.interactable = canGive;
                        giveBtn.Child('root', cc.MultiFrame).setFrame(canGive);
                        giveBtn.Child('count/val', cc.Label).string = this.itemSkin ? data.skins.length + '' : '';
                        var useBtn = lay.Child('use_be');
                        useBtn.opacity = this.checkPawnUnlock() && !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
                        if (this.timeNode_.active = state > 0) {
                            this.timeNode_.Child('val').setLocaleKey('ui.item_skin_surplus_time', GameHelper_1.gameHpr.millisecondToCountDown(Math.max(0, state)));
                        }
                    }
                }
                else {
                    var cond = this.buttonsNode_.Swih('cond')[0];
                    var type = (this.json.cond > 100 || this.json.cond === 5) ? 2 : this.json.cond;
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type);
                }
            }
            else {
                var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                var buyList = GameHelper_1.gameHpr.user.getCanBuyPawnSkins(serverArea);
                var canBuy = buyList.has('id', this.json.id);
                if (have) {
                    var useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0];
                    useBtn.opacity = this.checkPawnUnlock() && !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
                }
                else if (canBuy || this.json.cond === 3) {
                    var buy = this.buttonsNode_.Swih('buy_be')[0];
                    buy.Data = this.json;
                    ViewHelper_1.viewHelper.updateCostText(buy, this.json);
                }
                else {
                    var cond = this.buttonsNode_.Swih('cond')[0];
                    var type = this.json.cond === 1 ? 2 : this.json.cond;
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type);
                }
            }
        }
        else {
            var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
            var buyList = GameHelper_1.gameHpr.user.getCanBuyCitySkins(serverArea);
            var unlockList = GameHelper_1.gameHpr.user.getUnlockCitySkinIds();
            var canBuy = buyList.has('id', this.json.id);
            var have = unlockList.has(this.json.id);
            if (have) {
                var useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0];
                useBtn.opacity = !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
            }
            else if (canBuy || this.json.cond === 3) {
                var buy = this.buttonsNode_.Swih('buy_be')[0];
                buy.Data = this.json;
                ViewHelper_1.viewHelper.updateCostText(buy, this.json);
            }
            else {
                var cond = this.buttonsNode_.Swih('cond')[0];
                var type = this.json.cond === 1 ? 2 : this.json.cond;
                cond.Child('val').setLocaleKey('ui.get_cond_' + type);
            }
        }
        this.loadSkin(isPawnSkin);
    };
    CollectionSkinInfoPnlCtrl.prototype.loadSkin = function (isPawnSkin) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var json, root, pfb, node, conf;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        json = this.json;
                        root = this.iconNode_.Child('mask').Swih(isPawnSkin ? 'root' : 'val')[0];
                        if (!isPawnSkin) return [3 /*break*/, 2];
                        root.removeAllChildren();
                        this.animCmpt = null;
                        this.nameNode_.Child('val').setLocaleKey(ResHelper_1.resHelper.getPawnName(json.pawn_id));
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/ROLE_' + json.id, cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        node = cc.instantiate2(pfb, root);
                        conf = FrameAnimConf_1.SHOP_PAWN_SKIN_ANIM_CONF[json.id];
                        if (!!conf) {
                            this.animCmpt = node.addComponent(PawnFrameAnimationCmpt_1.default).init(node.FindChild('body/anim', cc.Sprite), json.id, this.key);
                            this.animCmpt.play();
                        }
                        else {
                            (_a = node.Child('body/anim', cc.Animation)) === null || _a === void 0 ? void 0 : _a.play('role_' + json.id + '_walk');
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        ResHelper_1.resHelper.loadCityIcon(json.id, root, this.key);
                        _b.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.syncInfoToServer = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawnId, skinId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pawnId = this.json.pawn_id, skinId = this.json.id;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_UsePawnSkin', { pawnId: pawnId, skinId: skinId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.PAWN_NOT_EXIST) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] })];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (data.hasPawn) {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] });
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_def_pawn_skin_succ', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] });
                        }
                        GameHelper_1.gameHpr.player.changeConfigPawnSkinId(pawnId, skinId);
                        this.hide();
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.changeCitySkin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var playerInfo, index, id, skinId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        playerInfo = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid()), index = (playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.mainCityIndex) || -1, id = this.json.id, skinId = id === playerInfo.cells.get(index).cityId ? 0 : id;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_ChangeCitySkin', { index: index, skinId: skinId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        GameHelper_1.gameHpr.world.updateCitySkin(index, skinId);
                        ViewHelper_1.viewHelper.hidePnl('menu/Collection');
                        this.hide();
                        GotoHelper_1.gotoHelper.gotoMainCity();
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.update = function (dt) {
        var _a;
        (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.updateFrame(dt * 1000);
    };
    CollectionSkinInfoPnlCtrl = __decorate([
        ccclass
    ], CollectionSkinInfoPnlCtrl);
    return CollectionSkinInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CollectionSkinInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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