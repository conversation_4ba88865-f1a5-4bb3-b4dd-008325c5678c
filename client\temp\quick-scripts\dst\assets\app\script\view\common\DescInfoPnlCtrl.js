
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/DescInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc1d2bi9JdH8rN0LEk03lpf', 'DescInfoPnlCtrl');
// app/script/view/common/DescInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var DescInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(DescInfoPnlCtrl, _super);
    function DescInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.contentNode_ = null; // path://root/content_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    DescInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DescInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DescInfoPnlCtrl.prototype.onEnter = function (data) {
        this.titleLbl_.setLocaleKey(data.title);
        var isTh = mc.lang === 'th';
        this.contentNode_.Items(data.list, function (it, desc, i) {
            if (desc.isNo) {
                it.setLocaleKey('ui.text', (i + 1) + (isTh ? '.' : '. ') + assetsMgr.lang(desc.key, desc.params || []));
            }
            else {
                it.setLocaleKey(desc.key, desc.params);
            }
        });
    };
    DescInfoPnlCtrl.prototype.onRemove = function () {
    };
    DescInfoPnlCtrl.prototype.onClean = function () {
    };
    DescInfoPnlCtrl = __decorate([
        ccclass
    ], DescInfoPnlCtrl);
    return DescInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DescInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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