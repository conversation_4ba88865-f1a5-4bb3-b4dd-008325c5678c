
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SelectPortrayalPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1d59fGkL2lB+Kob4+jOjm+r', 'SelectPortrayalPnlCtrl');
// app/script/view/common/SelectPortrayalPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PORTRAYAL_TYPE_TEXT = (_a = {},
    _a[Enums_1.SelectPortrayalType.NEED_DEBRIS] = 'debris',
    _a[Enums_1.SelectPortrayalType.BUY] = 'portrayal',
    _a[Enums_1.SelectPortrayalType.GIFT] = 'portrayal',
    _a[Enums_1.SelectPortrayalType.HERO] = 'portrayal',
    _a);
var SelectPortrayalPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectPortrayalPnlCtrl, _super);
    function SelectPortrayalPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        _this.filterSelectNode_ = null; // path://root_n/button/filter_select_be_n
        _this.list = [];
        _this.cb = null;
        _this.currSelectFilter = 0;
        _this.isHero = false;
        _this.currSelectPortrayal = null;
        return _this;
    }
    SelectPortrayalPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectPortrayalPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectPortrayalPnlCtrl.prototype.onEnter = function (type, list, cb, lv) {
        this.type = type;
        this.list = list;
        this.cb = cb;
        this.isHero = type === Enums_1.SelectPortrayalType.HERO;
        if (type === Enums_1.SelectPortrayalType.NEED_DEBRIS) {
            this.titleLbl_.setLocaleKey('ui.select_portrayal_0');
        }
        else if (type === Enums_1.SelectPortrayalType.BUY || type === Enums_1.SelectPortrayalType.GIFT) {
            this.titleLbl_.setLocaleKey(!!lv ? 'ui.hero_opt_gift_' + lv : 'ui.select_portrayal_1');
        }
        else if (type === Enums_1.SelectPortrayalType.HERO) {
            this.titleLbl_.setLocaleKey('ui.select_portrayal_2');
        }
        this.selectFilterItem(this.filterSelectNode_, 0);
        this.filterSelectNode_.active = this.list.length > 1;
    };
    SelectPortrayalPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb([]);
        this.cb = null;
    };
    SelectPortrayalPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/list/view/content/item_be
    SelectPortrayalPnlCtrl.prototype.onClickItem = function (event, data) {
        this.updateSelectHeroInfo(event.target.Data);
    };
    // path://root_n/button/ok_be
    SelectPortrayalPnlCtrl.prototype.onClickOk = function (event, _) {
        var data = this.currSelectPortrayal;
        if (data) {
            this.do([data]);
        }
    };
    // path://root_n/button/filter_select_be_n
    SelectPortrayalPnlCtrl.prototype.onClickFilterSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root_n/button/filter_select_be_n/select_mask_be
    SelectPortrayalPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root_n/button/filter_select_be_n/mask/root/filter_items_nbe
    SelectPortrayalPnlCtrl.prototype.onClickFilterItems = function (event, data) {
        var node = this.filterSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = Number(event.target.name);
        if (type !== this.currSelectFilter) {
            this.selectFilterItem(node, type);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectPortrayalPnlCtrl.prototype.updateList = function () {
        var _this = this;
        var list = this.list.filter(function (m) { return !_this.currSelectFilter || _this.currSelectFilter === m.pawnType; });
        var sv = this.rootNode_.Child('list', cc.ScrollView);
        sv.scrollToTopLeft();
        sv.content.x = 0;
        if (sv.Child('empty').active = !list.length) {
            sv.Child('empty', cc.Label).setLocaleKey('ui.empty_hero_' + PORTRAYAL_TYPE_TEXT[this.type]);
        }
        sv.Items(list, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, it.Child('val'), _this.key);
            it.Child('name').setLocaleKey(data.getChatName());
        });
        this.updateSelectHeroInfo(null);
    };
    // 刷新选择
    SelectPortrayalPnlCtrl.prototype.updateSelectHeroInfo = function (data) {
        var _this = this;
        if (this.rootNode_.Child('unlock_pawn').active = this.isHero && !!data) {
            this.rootNode_.Child('unlock_pawn/val').setLocaleKey('ui.worship_unlock_pawn_desc', data.name, data.avatarPawnName);
        }
        this.rootNode_.Child('button/ok_be').active = !!data;
        this.currSelectPortrayal = data;
        var root = this.rootNode_;
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var _a;
            var select = (data === null || data === void 0 ? void 0 : data.id) === ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id);
            it.Child('select').active = select;
            it.Component(cc.Button).interactable = !select;
        });
        // 显示信息
        root.Child('empty').active = !data;
        var sv = root.Child('info', cc.ScrollView);
        if (sv.setActive(!!data)) {
            var info_1 = sv.content;
            sv.stopAutoScroll();
            info_1.y = 0;
            ViewHelper_1.viewHelper.updatePortrayalAttr(info_1, data, this.isHero);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = cc.misc.clampf(info_1.height + 4, 160, 320);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    // 选择赛选
    SelectPortrayalPnlCtrl.prototype.selectFilterItem = function (node, type) {
        node.Data = this.currSelectFilter = type;
        node.Child('val', cc.Label).setLocaleKey(type ? 'ui.pawn_type_' + type : 'ui.bazaar_filter_all');
        node.Child('mask/root/filter_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
        this.updateList();
    };
    SelectPortrayalPnlCtrl.prototype.do = function (list) {
        var _this = this;
        if (this.type === Enums_1.SelectPortrayalType.BUY) {
            ViewHelper_1.viewHelper.showPnl('common/ShopBuyTip', { type: 'hero', data: list[0], textKey: 'ui.shop_buy_tip_ingot', cost: Constant_1.BUY_OPT_HERO_COST }, function (ok) {
                if (!_this.isValid || !ok) {
                    return;
                }
                else if (GameHelper_1.gameHpr.user.getIngot() < Constant_1.BUY_OPT_HERO_COST) {
                    return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
                }
                _this.end(list);
            });
        }
        else if (this.type === Enums_1.SelectPortrayalType.HERO) {
            ViewHelper_1.viewHelper.showMessageBox('ui.worship_hero_tip', {
                params: [list[0].getChatName()],
                ok: function () { return _this.isValid && _this.end(list); },
                cancel: function () { },
            });
        }
        else {
            this.end(list);
        }
    };
    SelectPortrayalPnlCtrl.prototype.end = function (list) {
        this.cb && this.cb(list);
        this.cb = null;
        this.hide();
    };
    SelectPortrayalPnlCtrl = __decorate([
        ccclass
    ], SelectPortrayalPnlCtrl);
    return SelectPortrayalPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectPortrayalPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2VsZWN0UG9ydHJheWFsUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQW1FO0FBQ25FLHFEQUFvRDtBQUNwRCxxREFBa0U7QUFDbEUsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFHcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFFbEMsSUFBTSxtQkFBbUI7SUFDckIsR0FBQywyQkFBbUIsQ0FBQyxXQUFXLElBQUcsUUFBUTtJQUMzQyxHQUFDLDJCQUFtQixDQUFDLEdBQUcsSUFBRyxXQUFXO0lBQ3RDLEdBQUMsMkJBQW1CLENBQUMsSUFBSSxJQUFHLFdBQVc7SUFDdkMsR0FBQywyQkFBbUIsQ0FBQyxJQUFJLElBQUcsV0FBVztPQUMxQyxDQUFBO0FBR0Q7SUFBb0QsMENBQWM7SUFBbEU7UUFBQSxxRUErS0M7UUE3S0csMEJBQTBCO1FBQ2xCLGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQyxnQkFBZ0I7UUFDMUMsZUFBUyxHQUFhLElBQUksQ0FBQSxDQUFDLDhCQUE4QjtRQUN6RCx1QkFBaUIsR0FBWSxJQUFJLENBQUEsQ0FBQywwQ0FBMEM7UUFJNUUsVUFBSSxHQUFvQixFQUFFLENBQUE7UUFDMUIsUUFBRSxHQUFhLElBQUksQ0FBQTtRQUVuQixzQkFBZ0IsR0FBVyxDQUFDLENBQUE7UUFDNUIsWUFBTSxHQUFZLEtBQUssQ0FBQTtRQUV2Qix5QkFBbUIsR0FBa0IsSUFBSSxDQUFBOztJQWdLckQsQ0FBQztJQTlKVSxnREFBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLHlDQUFRLEdBQXJCOzs7Ozs7S0FDQztJQUVNLHdDQUFPLEdBQWQsVUFBZSxJQUF5QixFQUFFLElBQXFCLEVBQUUsRUFBWSxFQUFFLEVBQVc7UUFDdEYsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUE7UUFDWixJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksS0FBSywyQkFBbUIsQ0FBQyxJQUFJLENBQUE7UUFDL0MsSUFBSSxJQUFJLEtBQUssMkJBQW1CLENBQUMsV0FBVyxFQUFFO1lBQzFDLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLHVCQUF1QixDQUFDLENBQUE7U0FDdkQ7YUFBTSxJQUFJLElBQUksS0FBSywyQkFBbUIsQ0FBQyxHQUFHLElBQUksSUFBSSxLQUFLLDJCQUFtQixDQUFDLElBQUksRUFBRTtZQUM5RSxJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxtQkFBbUIsR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLHVCQUF1QixDQUFDLENBQUE7U0FDekY7YUFBTSxJQUFJLElBQUksS0FBSywyQkFBbUIsQ0FBQyxJQUFJLEVBQUU7WUFDMUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxZQUFZLENBQUMsdUJBQXVCLENBQUMsQ0FBQTtTQUN2RDtRQUNELElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDaEQsSUFBSSxDQUFDLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7SUFDeEQsQ0FBQztJQUVNLHlDQUFRLEdBQWY7UUFDSSxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUE7UUFDdEIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLHdDQUFPLEdBQWQ7UUFDSSxTQUFTLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQzNDLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDBDQUEwQztJQUMxQyw0Q0FBVyxHQUFYLFVBQVksS0FBMEIsRUFBRSxJQUFZO1FBQ2hELElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ2hELENBQUM7SUFFRCw2QkFBNkI7SUFDN0IsMENBQVMsR0FBVCxVQUFVLEtBQTBCLEVBQUUsQ0FBUztRQUMzQyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsbUJBQW1CLENBQUE7UUFDckMsSUFBSSxJQUFJLEVBQUU7WUFDTixJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtTQUNsQjtJQUNMLENBQUM7SUFFRCwwQ0FBMEM7SUFDMUMsb0RBQW1CLEdBQW5CLFVBQW9CLEtBQTBCLEVBQUUsSUFBWTtRQUN4RCxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLHVCQUFVLENBQUMsa0JBQWtCLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQTtJQUNyRCxDQUFDO0lBRUQseURBQXlEO0lBQ3pELGtEQUFpQixHQUFqQixVQUFrQixLQUEwQixFQUFFLElBQVk7UUFDdEQsdUJBQVUsQ0FBQyxrQkFBa0IsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxLQUFLLENBQUMsQ0FBQTtJQUM3RCxDQUFDO0lBRUQscUVBQXFFO0lBQ3JFLG1EQUFrQixHQUFsQixVQUFtQixLQUEwQixFQUFFLElBQVk7UUFDdkQsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLGlCQUFpQixDQUFBO1FBQ25DLHVCQUFVLENBQUMsa0JBQWtCLENBQUMsSUFBSSxFQUFFLEtBQUssQ0FBQyxDQUFBO1FBQzFDLElBQU0sSUFBSSxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3RDLElBQUksSUFBSSxLQUFLLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUNoQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ3BDO0lBQ0wsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFakgsaUhBQWlIO0lBRXpHLDJDQUFVLEdBQWxCO1FBQUEsaUJBY0M7UUFiRyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsS0FBSSxDQUFDLGdCQUFnQixJQUFJLEtBQUksQ0FBQyxnQkFBZ0IsS0FBSyxDQUFDLENBQUMsUUFBUSxFQUE5RCxDQUE4RCxDQUFDLENBQUE7UUFDbEcsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQTtRQUN0RCxFQUFFLENBQUMsZUFBZSxFQUFFLENBQUE7UUFDcEIsRUFBRSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2hCLElBQUksRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQ3pDLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsZ0JBQWdCLEdBQUcsbUJBQW1CLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7U0FDOUY7UUFDRCxFQUFFLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQ3BCLEVBQUUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1lBQ2QscUJBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzlELEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFBO1FBQ3JELENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ25DLENBQUM7SUFFRCxPQUFPO0lBQ0MscURBQW9CLEdBQTVCLFVBQTZCLElBQW1CO1FBQWhELGlCQTRCQztRQTNCRyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxJQUFJLENBQUMsQ0FBQyxJQUFJLEVBQUU7WUFDcEUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxZQUFZLENBQUMsNkJBQTZCLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUE7U0FDdEg7UUFDRCxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQTtRQUNwRCxJQUFJLENBQUMsbUJBQW1CLEdBQUcsSUFBSSxDQUFBO1FBQy9CLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUE7UUFDM0IsT0FBTztRQUNQLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFBLEVBQUU7O1lBQ3pELElBQU0sTUFBTSxHQUFHLENBQUEsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLEVBQUUsYUFBSyxFQUFFLENBQUMsSUFBSSwwQ0FBRSxFQUFFLENBQUEsQ0FBQTtZQUN2QyxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7WUFDbEMsRUFBRSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxHQUFHLENBQUMsTUFBTSxDQUFBO1FBQ2xELENBQUMsQ0FBQyxDQUFBO1FBQ0YsT0FBTztRQUNQLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxDQUFBO1FBQ2xDLElBQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQTtRQUM1QyxJQUFJLEVBQUUsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ3RCLElBQU0sTUFBSSxHQUFHLEVBQUUsQ0FBQyxPQUFPLENBQUE7WUFDdkIsRUFBRSxDQUFDLGNBQWMsRUFBRSxDQUFBO1lBQ25CLE1BQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ1YsdUJBQVUsQ0FBQyxtQkFBbUIsQ0FBQyxNQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTtZQUN2RCxFQUFFLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQztnQkFDckIsSUFBSSxLQUFJLENBQUMsT0FBTyxFQUFFO29CQUNkLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUMsQ0FBQTtvQkFDMUQsRUFBRSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxlQUFlLEVBQUUsQ0FBQTtpQkFDckQ7WUFDTCxDQUFDLENBQUMsQ0FBQTtTQUNMO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxpREFBZ0IsR0FBeEIsVUFBeUIsSUFBYSxFQUFFLElBQVk7UUFDaEQsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFBO1FBQ3hDLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFBO1FBQ2hHLElBQUksQ0FBQyxLQUFLLENBQUMsNEJBQTRCLENBQUMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUN2RCxJQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQTtZQUN0QyxDQUFDLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUE7WUFDcEQsQ0FBQyxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFBO1FBQ3JDLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO0lBQ3JCLENBQUM7SUFFTyxtQ0FBRSxHQUFWLFVBQVcsSUFBcUI7UUFBaEMsaUJBbUJDO1FBbEJHLElBQUksSUFBSSxDQUFDLElBQUksS0FBSywyQkFBbUIsQ0FBQyxHQUFHLEVBQUU7WUFDdkMsdUJBQVUsQ0FBQyxPQUFPLENBQUMsbUJBQW1CLEVBQUUsRUFBRSxJQUFJLEVBQUUsTUFBTSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsT0FBTyxFQUFFLHVCQUF1QixFQUFFLElBQUksRUFBRSw0QkFBaUIsRUFBRSxFQUFFLFVBQUMsRUFBVztnQkFDNUksSUFBSSxDQUFDLEtBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxFQUFFLEVBQUU7b0JBQ3RCLE9BQU07aUJBQ1Q7cUJBQU0sSUFBSSxvQkFBTyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsR0FBRyw0QkFBaUIsRUFBRTtvQkFDcEQsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxhQUFLLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtpQkFDdEQ7Z0JBQ0QsS0FBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUNsQixDQUFDLENBQUMsQ0FBQTtTQUNMO2FBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLDJCQUFtQixDQUFDLElBQUksRUFBRTtZQUMvQyx1QkFBVSxDQUFDLGNBQWMsQ0FBQyxxQkFBcUIsRUFBRTtnQkFDN0MsTUFBTSxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDO2dCQUMvQixFQUFFLEVBQUUsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksS0FBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBOUIsQ0FBOEI7Z0JBQ3hDLE1BQU0sRUFBRSxjQUFRLENBQUM7YUFDcEIsQ0FBQyxDQUFBO1NBQ0w7YUFBTTtZQUNILElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDakI7SUFDTCxDQUFDO0lBRU8sb0NBQUcsR0FBWCxVQUFZLElBQVc7UUFDbkIsSUFBSSxDQUFDLEVBQUUsSUFBSSxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3hCLElBQUksQ0FBQyxFQUFFLEdBQUcsSUFBSSxDQUFBO1FBQ2QsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO0lBQ2YsQ0FBQztJQTlLZ0Isc0JBQXNCO1FBRDFDLE9BQU87T0FDYSxzQkFBc0IsQ0ErSzFDO0lBQUQsNkJBQUM7Q0EvS0QsQUErS0MsQ0EvS21ELEVBQUUsQ0FBQyxXQUFXLEdBK0tqRTtrQkEvS29CLHNCQUFzQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJVWV9PUFRfSEVST19DT1NUIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xuaW1wb3J0IHsgZWNvZGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VDb2RlXCI7XG5pbXBvcnQgeyBTZWxlY3RQb3J0cmF5YWxUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBQb3J0cmF5YWxJbmZvIGZyb20gXCIuLi8uLi9tb2RlbC9jb21tb24vUG9ydHJheWFsSW5mb1wiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbmNvbnN0IFBPUlRSQVlBTF9UWVBFX1RFWFQgPSB7XG4gICAgW1NlbGVjdFBvcnRyYXlhbFR5cGUuTkVFRF9ERUJSSVNdOiAnZGVicmlzJyxcbiAgICBbU2VsZWN0UG9ydHJheWFsVHlwZS5CVVldOiAncG9ydHJheWFsJyxcbiAgICBbU2VsZWN0UG9ydHJheWFsVHlwZS5HSUZUXTogJ3BvcnRyYXlhbCcsXG4gICAgW1NlbGVjdFBvcnRyYXlhbFR5cGUuSEVST106ICdwb3J0cmF5YWwnLFxufVxuXG5AY2NjbGFzc1xuZXhwb3J0IGRlZmF1bHQgY2xhc3MgU2VsZWN0UG9ydHJheWFsUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSByb290Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X25cbiAgICBwcml2YXRlIHRpdGxlTGJsXzogY2MuTGFiZWwgPSBudWxsIC8vIHBhdGg6Ly9yb290X24vdGl0bGUvdGl0bGVfbFxuICAgIHByaXZhdGUgZmlsdGVyU2VsZWN0Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X24vYnV0dG9uL2ZpbHRlcl9zZWxlY3RfYmVfblxuICAgIC8vQGVuZFxuXG4gICAgcHJpdmF0ZSB0eXBlOiBTZWxlY3RQb3J0cmF5YWxUeXBlXG4gICAgcHJpdmF0ZSBsaXN0OiBQb3J0cmF5YWxJbmZvW10gPSBbXVxuICAgIHByaXZhdGUgY2I6IEZ1bmN0aW9uID0gbnVsbFxuXG4gICAgcHJpdmF0ZSBjdXJyU2VsZWN0RmlsdGVyOiBudW1iZXIgPSAwXG4gICAgcHJpdmF0ZSBpc0hlcm86IGJvb2xlYW4gPSBmYWxzZVxuXG4gICAgcHJpdmF0ZSBjdXJyU2VsZWN0UG9ydHJheWFsOiBQb3J0cmF5YWxJbmZvID0gbnVsbFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKHR5cGU6IFNlbGVjdFBvcnRyYXlhbFR5cGUsIGxpc3Q6IFBvcnRyYXlhbEluZm9bXSwgY2I6IEZ1bmN0aW9uLCBsdj86IG51bWJlcikge1xuICAgICAgICB0aGlzLnR5cGUgPSB0eXBlXG4gICAgICAgIHRoaXMubGlzdCA9IGxpc3RcbiAgICAgICAgdGhpcy5jYiA9IGNiXG4gICAgICAgIHRoaXMuaXNIZXJvID0gdHlwZSA9PT0gU2VsZWN0UG9ydHJheWFsVHlwZS5IRVJPXG4gICAgICAgIGlmICh0eXBlID09PSBTZWxlY3RQb3J0cmF5YWxUeXBlLk5FRURfREVCUklTKSB7XG4gICAgICAgICAgICB0aGlzLnRpdGxlTGJsXy5zZXRMb2NhbGVLZXkoJ3VpLnNlbGVjdF9wb3J0cmF5YWxfMCcpXG4gICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gU2VsZWN0UG9ydHJheWFsVHlwZS5CVVkgfHwgdHlwZSA9PT0gU2VsZWN0UG9ydHJheWFsVHlwZS5HSUZUKSB7XG4gICAgICAgICAgICB0aGlzLnRpdGxlTGJsXy5zZXRMb2NhbGVLZXkoISFsdiA/ICd1aS5oZXJvX29wdF9naWZ0XycgKyBsdiA6ICd1aS5zZWxlY3RfcG9ydHJheWFsXzEnKVxuICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IFNlbGVjdFBvcnRyYXlhbFR5cGUuSEVSTykge1xuICAgICAgICAgICAgdGhpcy50aXRsZUxibF8uc2V0TG9jYWxlS2V5KCd1aS5zZWxlY3RfcG9ydHJheWFsXzInKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2VsZWN0RmlsdGVySXRlbSh0aGlzLmZpbHRlclNlbGVjdE5vZGVfLCAwKVxuICAgICAgICB0aGlzLmZpbHRlclNlbGVjdE5vZGVfLmFjdGl2ZSA9IHRoaXMubGlzdC5sZW5ndGggPiAxXG4gICAgfVxuXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xuICAgICAgICB0aGlzLmNiICYmIHRoaXMuY2IoW10pXG4gICAgICAgIHRoaXMuY2IgPSBudWxsXG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuICAgIH1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cbiAgICAvLyBwYXRoOi8vcm9vdF9uL2xpc3Qvdmlldy9jb250ZW50L2l0ZW1fYmVcbiAgICBvbkNsaWNrSXRlbShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIHRoaXMudXBkYXRlU2VsZWN0SGVyb0luZm8oZXZlbnQudGFyZ2V0LkRhdGEpXG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3Rfbi9idXR0b24vb2tfYmVcbiAgICBvbkNsaWNrT2soZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIF86IHN0cmluZykge1xuICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5jdXJyU2VsZWN0UG9ydHJheWFsXG4gICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICB0aGlzLmRvKFtkYXRhXSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290X24vYnV0dG9uL2ZpbHRlcl9zZWxlY3RfYmVfblxuICAgIG9uQ2xpY2tGaWx0ZXJTZWxlY3QoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIHZpZXdIZWxwZXIuY2hhbmdlUG9wdXBCb3hMaXN0KGV2ZW50LnRhcmdldCwgdHJ1ZSlcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdF9uL2J1dHRvbi9maWx0ZXJfc2VsZWN0X2JlX24vc2VsZWN0X21hc2tfYmVcbiAgICBvbkNsaWNrU2VsZWN0TWFzayhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIHZpZXdIZWxwZXIuY2hhbmdlUG9wdXBCb3hMaXN0KGV2ZW50LnRhcmdldC5wYXJlbnQsIGZhbHNlKVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290X24vYnV0dG9uL2ZpbHRlcl9zZWxlY3RfYmVfbi9tYXNrL3Jvb3QvZmlsdGVyX2l0ZW1zX25iZVxuICAgIG9uQ2xpY2tGaWx0ZXJJdGVtcyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZpbHRlclNlbGVjdE5vZGVfXG4gICAgICAgIHZpZXdIZWxwZXIuY2hhbmdlUG9wdXBCb3hMaXN0KG5vZGUsIGZhbHNlKVxuICAgICAgICBjb25zdCB0eXBlID0gTnVtYmVyKGV2ZW50LnRhcmdldC5uYW1lKVxuICAgICAgICBpZiAodHlwZSAhPT0gdGhpcy5jdXJyU2VsZWN0RmlsdGVyKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdEZpbHRlckl0ZW0obm9kZSwgdHlwZSlcbiAgICAgICAgfVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgdXBkYXRlTGlzdCgpIHtcbiAgICAgICAgY29uc3QgbGlzdCA9IHRoaXMubGlzdC5maWx0ZXIobSA9PiAhdGhpcy5jdXJyU2VsZWN0RmlsdGVyIHx8IHRoaXMuY3VyclNlbGVjdEZpbHRlciA9PT0gbS5wYXduVHlwZSlcbiAgICAgICAgY29uc3Qgc3YgPSB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnbGlzdCcsIGNjLlNjcm9sbFZpZXcpXG4gICAgICAgIHN2LnNjcm9sbFRvVG9wTGVmdCgpXG4gICAgICAgIHN2LmNvbnRlbnQueCA9IDBcbiAgICAgICAgaWYgKHN2LkNoaWxkKCdlbXB0eScpLmFjdGl2ZSA9ICFsaXN0Lmxlbmd0aCkge1xuICAgICAgICAgICAgc3YuQ2hpbGQoJ2VtcHR5JywgY2MuTGFiZWwpLnNldExvY2FsZUtleSgndWkuZW1wdHlfaGVyb18nICsgUE9SVFJBWUFMX1RZUEVfVEVYVFt0aGlzLnR5cGVdKVxuICAgICAgICB9XG4gICAgICAgIHN2Lkl0ZW1zKGxpc3QsIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgaXQuRGF0YSA9IGRhdGFcbiAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkUGF3bkhlYWRJY29uKGRhdGEuaWQsIGl0LkNoaWxkKCd2YWwnKSwgdGhpcy5rZXkpXG4gICAgICAgICAgICBpdC5DaGlsZCgnbmFtZScpLnNldExvY2FsZUtleShkYXRhLmdldENoYXROYW1lKCkpXG4gICAgICAgIH0pXG4gICAgICAgIHRoaXMudXBkYXRlU2VsZWN0SGVyb0luZm8obnVsbClcbiAgICB9XG5cbiAgICAvLyDliLfmlrDpgInmi6lcbiAgICBwcml2YXRlIHVwZGF0ZVNlbGVjdEhlcm9JbmZvKGRhdGE6IFBvcnRyYXlhbEluZm8pIHtcbiAgICAgICAgaWYgKHRoaXMucm9vdE5vZGVfLkNoaWxkKCd1bmxvY2tfcGF3bicpLmFjdGl2ZSA9IHRoaXMuaXNIZXJvICYmICEhZGF0YSkge1xuICAgICAgICAgICAgdGhpcy5yb290Tm9kZV8uQ2hpbGQoJ3VubG9ja19wYXduL3ZhbCcpLnNldExvY2FsZUtleSgndWkud29yc2hpcF91bmxvY2tfcGF3bl9kZXNjJywgZGF0YS5uYW1lLCBkYXRhLmF2YXRhclBhd25OYW1lKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMucm9vdE5vZGVfLkNoaWxkKCdidXR0b24vb2tfYmUnKS5hY3RpdmUgPSAhIWRhdGFcbiAgICAgICAgdGhpcy5jdXJyU2VsZWN0UG9ydHJheWFsID0gZGF0YVxuICAgICAgICBjb25zdCByb290ID0gdGhpcy5yb290Tm9kZV9cbiAgICAgICAgLy8g5Yi35paw6YCJ5oupXG4gICAgICAgIHJvb3QuQ2hpbGQoJ2xpc3QnLCBjYy5TY3JvbGxWaWV3KS5jb250ZW50LmNoaWxkcmVuLmZvckVhY2goaXQgPT4ge1xuICAgICAgICAgICAgY29uc3Qgc2VsZWN0ID0gZGF0YT8uaWQgPT09IGl0LkRhdGE/LmlkXG4gICAgICAgICAgICBpdC5DaGlsZCgnc2VsZWN0JykuYWN0aXZlID0gc2VsZWN0XG4gICAgICAgICAgICBpdC5Db21wb25lbnQoY2MuQnV0dG9uKS5pbnRlcmFjdGFibGUgPSAhc2VsZWN0XG4gICAgICAgIH0pXG4gICAgICAgIC8vIOaYvuekuuS/oeaBr1xuICAgICAgICByb290LkNoaWxkKCdlbXB0eScpLmFjdGl2ZSA9ICFkYXRhXG4gICAgICAgIGNvbnN0IHN2ID0gcm9vdC5DaGlsZCgnaW5mbycsIGNjLlNjcm9sbFZpZXcpXG4gICAgICAgIGlmIChzdi5zZXRBY3RpdmUoISFkYXRhKSkge1xuICAgICAgICAgICAgY29uc3QgaW5mbyA9IHN2LmNvbnRlbnRcbiAgICAgICAgICAgIHN2LnN0b3BBdXRvU2Nyb2xsKClcbiAgICAgICAgICAgIGluZm8ueSA9IDBcbiAgICAgICAgICAgIHZpZXdIZWxwZXIudXBkYXRlUG9ydHJheWFsQXR0cihpbmZvLCBkYXRhLCB0aGlzLmlzSGVybylcbiAgICAgICAgICAgIHV0LndhaXROZXh0RnJhbWUoMikudGhlbigoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCkge1xuICAgICAgICAgICAgICAgICAgICBzdi5ub2RlLmhlaWdodCA9IGNjLm1pc2MuY2xhbXBmKGluZm8uaGVpZ2h0ICsgNCwgMTYwLCAzMjApXG4gICAgICAgICAgICAgICAgICAgIHN2Lm5vZGUuQ2hpbGQoJ3ZpZXcnLCBjYy5XaWRnZXQpLnVwZGF0ZUFsaWdubWVudCgpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOmAieaLqei1m+mAiVxuICAgIHByaXZhdGUgc2VsZWN0RmlsdGVySXRlbShub2RlOiBjYy5Ob2RlLCB0eXBlOiBudW1iZXIpIHtcbiAgICAgICAgbm9kZS5EYXRhID0gdGhpcy5jdXJyU2VsZWN0RmlsdGVyID0gdHlwZVxuICAgICAgICBub2RlLkNoaWxkKCd2YWwnLCBjYy5MYWJlbCkuc2V0TG9jYWxlS2V5KHR5cGUgPyAndWkucGF3bl90eXBlXycgKyB0eXBlIDogJ3VpLmJhemFhcl9maWx0ZXJfYWxsJylcbiAgICAgICAgbm9kZS5DaGlsZCgnbWFzay9yb290L2ZpbHRlcl9pdGVtc19uYmUnKS5jaGlsZHJlbi5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgY29uc3Qgc2VsZWN0ID0gTnVtYmVyKG0ubmFtZSkgPT09IHR5cGVcbiAgICAgICAgICAgIG0uQ2hpbGQoJ3ZhbCcpLkNvbG9yKHNlbGVjdCA/ICcjQjZBNTkxJyA6ICcjNzU2OTYzJylcbiAgICAgICAgICAgIG0uQ2hpbGQoJ3NlbGVjdCcpLmFjdGl2ZSA9IHNlbGVjdFxuICAgICAgICB9KVxuICAgICAgICB0aGlzLnVwZGF0ZUxpc3QoKVxuICAgIH1cblxuICAgIHByaXZhdGUgZG8obGlzdDogUG9ydHJheWFsSW5mb1tdKSB7XG4gICAgICAgIGlmICh0aGlzLnR5cGUgPT09IFNlbGVjdFBvcnRyYXlhbFR5cGUuQlVZKSB7XG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2NvbW1vbi9TaG9wQnV5VGlwJywgeyB0eXBlOiAnaGVybycsIGRhdGE6IGxpc3RbMF0sIHRleHRLZXk6ICd1aS5zaG9wX2J1eV90aXBfaW5nb3QnLCBjb3N0OiBCVVlfT1BUX0hFUk9fQ09TVCB9LCAob2s6IGJvb2xlYW4pID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuaXNWYWxpZCB8fCAhb2spIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChnYW1lSHByLnVzZXIuZ2V0SW5nb3QoKSA8IEJVWV9PUFRfSEVST19DT1NUKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5JTkdPVF9OT1RfRU5PVUdIKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLmVuZChsaXN0KVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnR5cGUgPT09IFNlbGVjdFBvcnRyYXlhbFR5cGUuSEVSTykge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93TWVzc2FnZUJveCgndWkud29yc2hpcF9oZXJvX3RpcCcsIHtcbiAgICAgICAgICAgICAgICBwYXJhbXM6IFtsaXN0WzBdLmdldENoYXROYW1lKCldLFxuICAgICAgICAgICAgICAgIG9rOiAoKSA9PiB0aGlzLmlzVmFsaWQgJiYgdGhpcy5lbmQobGlzdCksXG4gICAgICAgICAgICAgICAgY2FuY2VsOiAoKSA9PiB7IH0sXG4gICAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5lbmQobGlzdClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgZW5kKGxpc3Q6IGFueVtdKSB7XG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYihsaXN0KVxuICAgICAgICB0aGlzLmNiID0gbnVsbFxuICAgICAgICB0aGlzLmhpZGUoKVxuICAgIH1cbn1cbiJdfQ==