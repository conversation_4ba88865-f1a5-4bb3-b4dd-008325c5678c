
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/MapUionFindHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8c1ebodOjhDmK7Gnun/XmYy', 'MapUionFindHelper');
// app/script/common/helper/MapUionFindHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapUionFindHelper = void 0;
var Enums_1 = require("../constant/Enums");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
var SameInfoItem = /** @class */ (function () {
    function SameInfoItem() {
        this.isUnLock = false;
    }
    return SameInfoItem;
}());
/**
 * 地图装饰是否解锁
 */
var MapUionFindHelper = /** @class */ (function () {
    function MapUionFindHelper() {
        this.sameMaps = {};
        this.tempCheckSameMap = {};
        this.tempCheckUnLockMap = {};
        //检查是否相同
        this.sameTypeList = [Enums_1.LandType.OBSTACLE, Enums_1.LandType.MOUNTAIN, Enums_1.LandType.PASS];
    }
    MapUionFindHelper.prototype.clean = function () {
        this.sameMaps = {};
        this.tempCheckSameMap = {};
        this.tempCheckUnLockMap = {};
    };
    MapUionFindHelper.prototype.init = function () {
        this.clean();
        var mapCells = GameHelper_1.gameHpr.world.getMapCells();
        for (var i = 0; i < mapCells.length; i++) {
            var cell = mapCells[i];
            if (this.sameMaps[cell.index]) {
                continue;
            }
            if (Enums_1.LandType.MOUNTAIN === cell.landType || Enums_1.LandType.OBSTACLE === cell.landType || Enums_1.LandType.LAKE === cell.landType) {
                var item = new SameInfoItem();
                this.sameMaps[cell.index] = item;
                this.checkNearLand(cell);
            }
        }
        this.checkAllUnLock();
    };
    //检查周围是否相同
    MapUionFindHelper.prototype.checkNearLand = function (cell) {
        var mapCells = GameHelper_1.gameHpr.world.getMapCells();
        if (this.tempCheckSameMap[cell.index]) {
            return;
        }
        this.tempCheckSameMap[cell.index] = true;
        var directionList = [[-1, 0], [0, 1], [1, 0], [0, -1]];
        for (var i = 0; i < directionList.length; i++) {
            var dir = directionList[i];
            var x = cell.point.x + dir[0];
            var y = cell.point.y + dir[1];
            var index = y * MapHelper_1.mapHelper.MAP_SIZE.x + x;
            var nearCell = mapCells[index];
            if (this.checkLandTypeSame(cell.landType, nearCell.landType) && !this.tempCheckSameMap[nearCell.index]) {
                this.sameMaps[nearCell.index] = this.sameMaps[cell.index];
                this.checkNearLand(nearCell);
            }
        }
    };
    MapUionFindHelper.prototype.checkLandTypeSame = function (type1, type2) {
        if (type1 === type2) {
            return true;
        }
        var index1 = this.sameTypeList.indexOf(type1);
        var index2 = this.sameTypeList.indexOf(type2);
        if (index1 !== -1 && index2 !== -1) {
            return true;
        }
        return false;
    };
    //检查全部地图是否解锁
    MapUionFindHelper.prototype.checkAllUnLock = function () {
        var mapCells = GameHelper_1.gameHpr.world.getMapCells();
        for (var i = 0; i < mapCells.length; i++) {
            var cell = mapCells[i];
            if (cell.owner) {
                this.checkNearUnLock(cell);
            }
        }
    };
    //检查单个Cell是否解锁
    MapUionFindHelper.prototype.checkNearUnLock = function (cell) {
        if (this.tempCheckUnLockMap[cell.index]) {
            return;
        }
        this.tempCheckUnLockMap[cell.index] = true;
        var directionList = [[-1, 0], [0, 1], [1, 0], [0, -1]];
        for (var i = 0; i < directionList.length; i++) {
            var dir = directionList[i];
            var x = cell.point.x + dir[0];
            var y = cell.point.y + dir[1];
            var index = y * MapHelper_1.mapHelper.MAP_SIZE.x + x;
            var item = this.sameMaps[index];
            if (item) {
                item.isUnLock = true;
            }
        }
    };
    //获取是否解锁
    MapUionFindHelper.prototype.getIsUnLock = function (index) {
        var item = this.sameMaps[index];
        if (item) {
            return item.isUnLock;
        }
        return false;
    };
    return MapUionFindHelper;
}());
exports.mapUionFindHelper = new MapUionFindHelper();
if (cc.sys.isBrowser) {
    window['mapUionFindHelper'] = exports.mapUionFindHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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