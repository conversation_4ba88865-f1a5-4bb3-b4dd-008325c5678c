
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/AlliMemberInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2bf1f9zSyVDLZBtYE4CNnW9', 'AlliMemberInfoPnlCtrl');
// app/script/view/build/AlliMemberInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AlliMemberInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(AlliMemberInfoPnlCtrl, _super);
    function AlliMemberInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_n
        _this.infoNode_ = null; // path://root/info_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.data = null;
        return _this;
    }
    AlliMemberInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ALLIANCE_MEMBER_JOB] = this.onUpdateAllianceMemberJob, _a.enter = true, _a)
        ];
    };
    AlliMemberInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AlliMemberInfoPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.data = data;
        var isOwner = data.uid === GameHelper_1.gameHpr.getUid();
        var isMeCreater = GameHelper_1.gameHpr.alliance.isMeCreater();
        ResHelper_1.resHelper.loadPlayerHead(this.headNode_.Child('val'), data.headIcon, this.key);
        this.headNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 11);
        ViewHelper_1.viewHelper.updatePlayerTitleText(this.headNode_.Child('title'), data.uid, this.key);
        this.updateJob();
        this.infoNode_.Child('1/val', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.joinTime);
        var mainPosNode = this.infoNode_.Child('2');
        if (mainPosNode.active = !isOwner) {
            var posNode = mainPosNode.Child('main_pos_be');
            var info = GameHelper_1.gameHpr.getPlayerInfo(data.uid);
            var isCapture = mainPosNode.Child('capture').active = !((_a = info === null || info === void 0 ? void 0 : info.cells) === null || _a === void 0 ? void 0 : _a.size);
            if (posNode.active = !isCapture) {
                ViewHelper_1.viewHelper.updatePositionView(posNode, info.mainCityIndex, false);
            }
        }
        this.infoNode_.Child('3/val', cc.Label).string = data.landCount + '';
        var extraScore = GameHelper_1.gameHpr.getExtraScore();
        var curScore = (data.landScores[0] || 0) + (data.alliScores[0] || 0) + extraScore;
        var topScore = (data.landScores[1] || 0) + (data.alliScores[1] || 0) + extraScore;
        this.infoNode_.Child('4/lay/val', cc.Label).string = curScore + '';
        var topScoreNode = this.infoNode_.Child('4/lay/top');
        if (topScoreNode.active = topScore > curScore) {
            topScoreNode.setLocaleKey('ui.score_historic_high_desc', topScore);
        }
        this.infoNode_.Child('5/val', cc.Label).string = data.embassyLv + '';
        var isCreater = data.uid === GameHelper_1.gameHpr.alliance.getCreater(), isHasJurisdiction = GameHelper_1.gameHpr.alliance.getMemberJob() <= Enums_1.AllianceJobType.CREATER_VICE;
        var isCanLeaveAlli = this.checkCanLeaveAlli();
        this.buttonsNode_.Child('kick_be').active = isCanLeaveAlli && !isOwner && !isCreater && (isMeCreater || (isHasJurisdiction && data.job === Enums_1.AllianceJobType.MEMBER));
        if (this.buttonsNode_.Child('exit_be').active = isCanLeaveAlli && isOwner && !isMeCreater) {
            var maxTime = (GameHelper_1.gameHpr.player.getExitAllianceCount() + 1) * Constant_1.CAN_EXIT_ALLI_TIME;
            this.updateExitButton(Math.max(0, maxTime - (Date.now() - data.joinTime)));
        }
        this.buttonsNode_.Child('send_pchat_be').active = !isOwner;
        this.buttonsNode_.active = this.buttonsNode_.children.some(function (m) { return m.active; });
    };
    AlliMemberInfoPnlCtrl.prototype.onRemove = function () {
        this.data = null;
    };
    AlliMemberInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/info_n/2/main_pos_be
    AlliMemberInfoPnlCtrl.prototype.onClickMainPos = function (event, data) {
        var index = event.target.Data;
        if (index) {
            this.hide();
            ViewHelper_1.viewHelper.hidePnl('build/BuildEmbassy');
            GameHelper_1.gameHpr.gotoTargetPos(index);
        }
    };
    // path://root/buttons_n/kick_be
    AlliMemberInfoPnlCtrl.prototype.onClickKick = function (event, data) {
        var _this = this;
        if (!this.data) {
            return;
        }
        else if (GameHelper_1.gameHpr.isAncientOwner(this.data.uid)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CANT_LEAVE_ALLI_WITH_ANCIENT);
        } /*  else if (gameHpr.isRankServer()) {
            return viewHelper.showAlert('toast.rank_server_not_kick_alli')
        } */
        GameHelper_1.gameHpr.alliance.kickMember(this.data.uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/buttons_n/send_mail_be
    AlliMemberInfoPnlCtrl.prototype.onClickSendMail = function (event, data) {
        var _this = this;
        if (this.data) {
            ViewHelper_1.viewHelper.showPnl('menu/WriteMail', { senderName: this.data.nickname }).then(function () { return _this.isValid && _this.hide(); });
        }
    };
    // path://root/buttons_n/exit_be
    AlliMemberInfoPnlCtrl.prototype.onClickExit = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.isRankServer()) {
            return ViewHelper_1.viewHelper.showAlert('toast.rank_server_not_exit_alli');
        }
        else if (GameHelper_1.gameHpr.isAncientOwner()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CANT_LEAVE_ALLI_WITH_ANCIENT);
        }
        GameHelper_1.gameHpr.net.request('game/HD_ExitAlliance', {}, true).then(function (res) {
            var _a, _b;
            if (res.err) {
                if (res.err === ECode_1.ecode.JOIN_ALLI_TIME_TOO_SHORT && _this.isValid) {
                    _this.updateExitButton(((_a = res.data) === null || _a === void 0 ? void 0 : _a.time) || 0);
                }
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            GameHelper_1.gameHpr.alliance.exit();
            GameHelper_1.gameHpr.player.setExitAllianceCount(((_b = res.data) === null || _b === void 0 ? void 0 : _b.exitAllianceCount) || 1);
            _this.emit(mc.Event.HIDE_PNL, 'build/BuildEmbassy');
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/buttons_n/send_pchat_be
    AlliMemberInfoPnlCtrl.prototype.onClickSendPchat = function (event, data) {
        var _this = this;
        GameHelper_1.gameHpr.addPChat(this.data.uid).then(function (ok) { return (ok && _this.isValid) && _this.hide(); });
    };
    // path://root/info_n/0/change_job_be
    AlliMemberInfoPnlCtrl.prototype.onClickChangeJob = function (event, data) {
        if (GameHelper_1.gameHpr.alliance.isMeCreater() && this.data) {
            ViewHelper_1.viewHelper.showPnl('build/SelectAlliJob', this.data);
        }
    };
    // path://root/info_n/0/look_job_be
    AlliMemberInfoPnlCtrl.prototype.onClickLookJob = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/AlliJobDesc');
    };
    // path://root/info_n/4/battle_record_be
    AlliMemberInfoPnlCtrl.prototype.onClickBattleRecord = function (event, _) {
        var _this = this;
        var alliScores = this.data.alliScores;
        GameHelper_1.gameHpr.alliance.getMemberBattleRecord(this.data).then(function (data) { return _this.isValid && ViewHelper_1.viewHelper.showPnl('build/AlliMemberBattle', data, alliScores); });
    };
    // path://root/head_n/copy_uid_be
    AlliMemberInfoPnlCtrl.prototype.onClickCopyUid = function (event, data) {
        GameHelper_1.gameHpr.copyToClipboard(this.data.uid, 'toast.yet_copy_clipboard');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AlliMemberInfoPnlCtrl.prototype.onUpdateAllianceMemberJob = function (member) {
        if (member.uid === this.data.uid) {
            this.updateJob();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AlliMemberInfoPnlCtrl.prototype.updateJob = function () {
        var data = this.data;
        var isOwner = data.uid === GameHelper_1.gameHpr.getUid();
        var isMeCreater = GameHelper_1.gameHpr.alliance.isMeCreater();
        this.infoNode_.Child('0/lay/val', cc.Label).setLocaleKey('ui.alliance_job_' + data.job);
        var b = this.infoNode_.Child('0/change_job_be').active = !isOwner && isMeCreater;
        this.infoNode_.Child('0/look_job_be').active = !b;
    };
    // 刷新退出按钮
    AlliMemberInfoPnlCtrl.prototype.updateExitButton = function (time) {
        var exitNode = this.buttonsNode_.Child('exit_be');
        if (!exitNode.active) {
            return;
        }
        var isCanExit = time <= 0;
        if (isCanExit) {
            exitNode.Swih('val');
        }
        else {
            exitNode.Swih('time')[0].setLocaleKey('ui.exit_alli_time', GameHelper_1.gameHpr.millisecondToStringForDay(time));
        }
        exitNode.Component(cc.Button).interactable = isCanExit;
        exitNode.Component(cc.MultiFrame).setFrame(isCanExit);
    };
    // 检测是否可以离开联盟
    AlliMemberInfoPnlCtrl.prototype.checkCanLeaveAlli = function () {
        if (!GameHelper_1.gameHpr.isRankServer()) {
            return true; //只要不是排位区 都可以
        }
        else if (this.data.offlineTime === 0) {
            return false; //在线不能踢
        }
        else if (GameHelper_1.gameHpr.world.getWinCondType() === Enums_1.WinCondType.KARMIC_MAHJONG) {
            return false; //血战到底 不能踢
        }
        var offlineTime = this.data.offlineTime + (Date.now() - this.data.getTime);
        return offlineTime > ut.Time.Day * 3; //排位区 超过3天 可踢出联盟
    };
    AlliMemberInfoPnlCtrl = __decorate([
        ccclass
    ], AlliMemberInfoPnlCtrl);
    return AlliMemberInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliMemberInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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