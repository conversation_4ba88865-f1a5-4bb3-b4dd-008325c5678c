
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SelectTondenArmyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef502vw9+lCXqx2ldy346T3', 'SelectTondenArmyPnlCtrl');
// app/script/view/main/SelectTondenArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectTondenArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectTondenArmyPnlCtrl, _super);
    function SelectTondenArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://root/top_n
        _this.listSv_ = null; // path://root/list_sv
        _this.timeNode_ = null; // path://root/time_n
        _this.surplusCountNode_ = null; // path://root/surplus_count_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.cb = null;
        _this.targetCell = null;
        _this.targetAreaMaxArmyCount = 0; //目标区域最大军队数量
        _this.armys = []; //当前的军队列表
        _this.selectArmy = null; //当前选择的
        return _this;
    }
    SelectTondenArmyPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_TONDEN_COUNT] = this.onUpdateTondenCount, _a.enter = true, _a)
        ];
    };
    SelectTondenArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectTondenArmyPnlCtrl.prototype.onEnter = function (index, list, canGotoCount, cb) {
        var _this = this;
        var _a;
        this.targetAreaMaxArmyCount = canGotoCount || 0;
        this.cb = cb;
        this.targetCell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
        var json = this.targetCell.getLandAttr();
        var player = GameHelper_1.gameHpr.player;
        // 显示宝箱信息
        this.topNode_.Child('name').setLocaleKey(this.targetCell.getName());
        this.topNode_.Child('info/val').setLocaleKey('ui.tonden_yet_cell_desc', ((_a = player.getOccupyLandCountMap()[this.targetCell.landLv]) === null || _a === void 0 ? void 0 : _a[1]) || 1);
        var add = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CELL_TONDEN_TREASURE);
        var counts = ut.stringToNumbers(json.treasures_count, ',').map(function (m) { return m + add; });
        this.topNode_.Child('info/drop_treasure_be/val', cc.Label).Color(add ? '#6BFF44' : '#FFFFFF').string = counts.join('~');
        // 屯田时间
        var time = json.tonden_time;
        var tondenCd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CELL_TONDEN_CD);
        time = Math.floor(time * ut.numberFixed(1 - tondenCd * 0.01));
        this.timeNode_.Child('val', cc.Label).Color(tondenCd ? '#4AB32E' : '#936E5A').string = ut.secondFormat(time, 'h:mm:ss');
        if (this.timeNode_.Child('up').active = !!tondenCd) {
            this.timeNode_.Child('up/val', cc.Label).string = '-' + tondenCd + '%';
        }
        // 剩余次数
        var isCanTonden = this.updateTondenCount();
        // 刷新按钮
        var okButtonNode = this.buttonsNode_.Swih(isCanTonden ? 'ok_be' : 'wait')[0];
        if (!isCanTonden) {
        }
        else if (okButtonNode.Child('lay/stamina').active = !GameHelper_1.gameHpr.isFreeServer()) {
            var needStamina = (json.need_stamina || 0) * Constant_1.TONDEN_STAMINA_MUL;
            var curStamina = player.getStamina();
            okButtonNode.Child('lay/stamina/val', cc.Label).Color(curStamina >= needStamina ? '#3F332F' : '#D7634D').string = '' + needStamina;
        }
        // 刷新列表
        var upSpeedMul = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MAIN_MARCH_MUL) || Constant_1.UP_MARCH_SPEED_MUL; //加速倍数
        // 是否第一次新手引导
        var cd = this.getBuildMarchCD() + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MARCH_CD);
        // 加速季节的
        cd += Math.floor(GameHelper_1.gameHpr.world.getSeason().getEffect(Enums_1.CEffect.MARCH_CD) * 100);
        // 加遗迹的
        cd += GameHelper_1.gameHpr.getAncientEffectByPlayer(GameHelper_1.gameHpr.getUid(), Enums_1.CEffect.MARCH_CD);
        // 刷新列表
        this.armys = list.map(function (m) {
            m.defaultMarchSpeed = GameHelper_1.gameHpr.getArmyMarchSpeed(m);
            m.marchSpeed = m.marchSpeed || m.defaultMarchSpeed;
            m.dis = GameHelper_1.gameHpr.getToMapCellDis(m.index, index);
            // 是否加速
            var upSpeedState = 0, mul = 0, mainOutUpDis = 0;
            // 城市加速
            mul += _this.isCanUpSpeed(m.index, index) ? upSpeedMul : 0;
            // 是否城边加速
            mainOutUpDis = GameHelper_1.gameHpr.getMainOutMarchSeepUpDis(index, m.index);
            mul += (Constant_1.MAIN_CITY_MARCH_SPEED[mainOutUpDis] || 0);
            upSpeedState = mainOutUpDis >= 0 ? 2 : (mul > 0 ? 1 : 0);
            return {
                uid: m.uid,
                data: m,
                dis: m.dis,
                mainOutUpDis: Math.max(0, mainOutUpDis),
                cd: cd,
                time: _this.getMarchTime(m, cd, mul),
                upSpeedState: upSpeedState,
                upSpeedMul: mul,
                states: [],
            };
        });
        this.updateArmys();
    };
    SelectTondenArmyPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    SelectTondenArmyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    SelectTondenArmyPnlCtrl.prototype.onClickItem = function (event, _) {
        var _this = this;
        var _a, _b, _c;
        audioMgr.playSFX('click');
        var data = event.currentTarget.Data;
        var state = (_b = (_a = event.currentTarget['_data']) === null || _a === void 0 ? void 0 : _a.states) === null || _b === void 0 ? void 0 : _b[0];
        if (state) {
            var key = 'ui.army_state_' + state;
            if (state === Enums_1.ArmyState.FIGHT) {
                key = 'toast.battling_cant_operation';
            }
            return ViewHelper_1.viewHelper.showAlert(key);
        }
        else if (((_c = this.selectArmy) === null || _c === void 0 ? void 0 : _c.uid) === data.uid) {
            return;
        }
        else if (this.targetAreaMaxArmyCount > 0 || data.index === this.targetCell.index) {
            this.selectArmy = data;
        }
        else {
            return ViewHelper_1.viewHelper.showAlert('toast.yet_exceed_max_army_count');
        }
        this.listSv_.content.children.forEach(function (m) { return _this.updateSelectItem(m); });
    };
    // path://root/list_sv/view/content/item_be/pos_be
    SelectTondenArmyPnlCtrl.prototype.onClickPos = function (event, _) {
        var data = event.target.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos(data.index);
        }
    };
    // path://root/list_sv/view/content/item_be/march_speed_be
    SelectTondenArmyPnlCtrl.prototype.onClickMarchSpeed = function (event, data) {
        var _this = this;
        var it = event.target.parent;
        var army = it === null || it === void 0 ? void 0 : it.Data;
        if (!army) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/ModifyMarchSpeed', army, function (speed) {
            if (!speed || army.marchSpeed === speed) {
                return;
            }
            army.marchSpeed = speed;
            if (_this.isValid) {
                var data_1 = _this.armys.find(function (m) { return m.uid === army.uid; });
                if (!data_1) {
                    return;
                }
                data_1.time = _this.getMarchTime(army, data_1.cd, data_1.upSpeedMul);
                _this.updateArmys(false, false);
            }
        });
    };
    // path://root/buttons_n/ok_be
    SelectTondenArmyPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var _a;
        if (!this.selectArmy) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
        }
        else if (GameHelper_1.gameHpr.player.getStamina() < this.targetCell.getLandAttr().need_stamina * Constant_1.TONDEN_STAMINA_MUL) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.STAMINA_NOT_ENOUGH);
        }
        var treasuresCountStr = ((_a = this.targetCell.getLandAttr()) === null || _a === void 0 ? void 0 : _a.treasures_count) || '0,0';
        var _b = __read(ut.stringToNumbers(treasuresCountStr, ','), 2), minCount = _b[0], maxCount = _b[1];
        var cur = 0, max = 0;
        this.selectArmy.pawns.forEach(function (p) {
            var _a;
            cur += p.treasures.length;
            max += (((_a = assetsMgr.getJsonData('pawnBase', p.id)) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
        });
        var cap = max - cur;
        if (maxCount > cap) {
            ViewHelper_1.viewHelper.showMessageBox('ui.treasure_count_not_enough', {
                okText: 'ui.button_goon',
                ok: function () { return _this.do(); },
                cancel: function () { }
            });
            return;
        }
        this.do();
    };
    // path://root/top_n/info/drop_treasure_be
    SelectTondenArmyPnlCtrl.prototype.onClickDropTreasure = function (event, data) {
        if (this.targetCell) {
            ViewHelper_1.viewHelper.showPnl('main/CellTondenInfo', this.targetCell.getLandAttr(), this.targetCell.landType);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    SelectTondenArmyPnlCtrl.prototype.onUpdateTondenCount = function () {
        this.updateTondenCount();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectTondenArmyPnlCtrl.prototype.updateTondenCount = function () {
        var tondenCount = Math.max(0, Constant_1.TODAY_TONDEN_MAX_COUNT - GameHelper_1.gameHpr.player.getCellTondenCount()), isCanTonden = tondenCount > 0, todeningCount = GameHelper_1.gameHpr.world.getArmyTodeningCount(), todeningStr = (isCanTonden && todeningCount > 0) ? "<color=#c34A32>(-" + todeningCount + ")</c>" : '';
        this.surplusCountNode_.setLocaleKey('ui.tonden_surplus_count', "<color=" + (isCanTonden ? '#3F332F' : '#D7634D') + ">" + tondenCount + "</c>" + todeningStr);
        return isCanTonden;
    };
    SelectTondenArmyPnlCtrl.prototype.getMarchTime = function (data, cd, mul) {
        var _a;
        var time = Math.floor(data.dis * (ut.Time.Hour / data.marchSpeed));
        // 是否有减少时间的政策
        time = Math.floor(time * (1 - cd * 0.01));
        // 计算需要的时间
        var minTime = data.index === ((_a = this.targetCell) === null || _a === void 0 ? void 0 : _a.index) ? 0 : 1000;
        data.marchTime = Math.floor(mul > 0 ? Math.max(time / mul, minTime) : time);
        return data.marchTime;
    };
    // 获取校场行军加速cd
    SelectTondenArmyPnlCtrl.prototype.getBuildMarchCD = function () {
        var build = GameHelper_1.gameHpr.player.getMainBuilds().find(function (m) { return m.id === 2011; });
        if (!build) {
            return 0;
        }
        var json = assetsMgr.getJsonData('buildAttr', build.id * 1000 + build.lv);
        return json ? ut.stringToNumbers(json.effects, ',')[1] || 0 : 0;
    };
    // 是否加速
    SelectTondenArmyPnlCtrl.prototype.isCanUpSpeed = function (aindex, bindex) {
        var acell = GameHelper_1.gameHpr.world.getMapCellByIndex(aindex);
        var bcell = GameHelper_1.gameHpr.world.getMapCellByIndex(bindex);
        return (acell === null || acell === void 0 ? void 0 : acell.isCanUpSpeedMarchCity()) && (bcell === null || bcell === void 0 ? void 0 : bcell.isCanUpSpeedMarchCity());
    };
    // 刷新列表
    SelectTondenArmyPnlCtrl.prototype.updateArmys = function (updateState, updateSelect) {
        var _this = this;
        var _a;
        if (updateState === void 0) { updateState = true; }
        if (updateSelect === void 0) { updateSelect = true; }
        // 训练列表
        var lvingPawnLvMap = {};
        GameHelper_1.gameHpr.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
        // 更新状态
        if (updateState) {
            this.armys.forEach(function (m) {
                var states = [], data = m.data;
                if (data.state !== Enums_1.ArmyState.NONE) {
                    states.push(data.state);
                }
                else {
                    if (data.drillPawns.length > 0) {
                        states.push(Enums_1.ArmyState.DRILL);
                    }
                    if (data.curingPawns.length > 0) {
                        states.push(Enums_1.ArmyState.CURING);
                    }
                    if (data.pawns.some(function (p) { return lvingPawnLvMap[p.uid]; })) {
                        states.push(Enums_1.ArmyState.LVING);
                    }
                    if (GameHelper_1.gameHpr.world.getArmyTondenInfo(data.index, data.uid)) {
                        states.push(Enums_1.ArmyState.TONDEN);
                    }
                }
                m.states = states;
            });
        }
        // 排序
        this.armys.sort(function (a, b) {
            if (a.states.length > 0 || b.states.length > 0) {
                return (a.states[0] || 0) - (b.states[0] || 0);
            }
            return a.time - b.time;
        });
        // 刷新选择  所有模式下都不再自动选
        if (updateSelect) {
            var army = this.armys[0];
            if (army && !((_a = army.states) === null || _a === void 0 ? void 0 : _a.length)) {
                this.selectArmy = army.data;
            }
        }
        //
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Items(this.armys, function (it, m) {
            it['_data'] = m;
            var data = it.Data = m.data;
            it.Child('name', cc.Label).string = data.name;
            ViewHelper_1.viewHelper.updatePositionView(it.Child('pos_be'), data.index);
            it.Child('target', cc.Label).setLocaleKey('ui.target_distance_count', m.dis);
            var pawns = data.pawns;
            it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), function (node2, pawn) {
                var _a;
                var icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                var lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id), icon, _this.key);
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                if (node2.Child('hp').active = (!isId && !isCuring)) {
                    node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1];
                }
            });
            // 刷新状态
            _this.updateArmyState(it, m.states);
            // 行军速度
            _this.updateArmyMarchSpeed(it, m);
            // 选择
            _this.updateSelectItem(it);
            //
            var canSelect = !m.states.length;
            it.opacity = canSelect ? 255 : 200;
            it.Color(canSelect ? '#E9DDC7' : '#DCDBD3');
        });
    };
    // 刷新选择
    SelectTondenArmyPnlCtrl.prototype.updateSelectItem = function (it) {
        var _a, _b, _c, _d;
        var select = it.Child('select').active = ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.uid) === ((_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid);
        // 只有选择的时候才可点击位置跳转
        var canSelect = select || !!((_d = (_c = it['_data']) === null || _c === void 0 ? void 0 : _c.states) === null || _d === void 0 ? void 0 : _d.length);
        it.Child('pos_be').Color(canSelect ? '#936E5A' : '#B6A591');
        it.Child('pos_be', cc.Button).interactable = canSelect;
        it.Child('pos_be/line').active = canSelect;
    };
    // 行军速度
    SelectTondenArmyPnlCtrl.prototype.updateArmyMarchSpeed = function (it, m) {
        var _a, _b;
        var data = m.data;
        if (it.Child('time').active = !((_a = m.states) === null || _a === void 0 ? void 0 : _a.length)) {
            it.Child('time/val', cc.Label).Color(!!m.upSpeedState ? '#6DB14C' : '#936E5A').string = ut.millisecondFormat(m.time, 'h:mm:ss');
            var isMul = it.Child('time/mul').active = m.upSpeedState > 0 && m.time > 0;
            var isUp = it.Child('time/up').active = !isMul && !!m.cd && m.time > 0;
            if (isMul) {
                it.Child('time/mul', cc.Label).setLocaleKey('ui.up_march_speed_' + m.upSpeedState, m.upSpeedMul, m.mainOutUpDis);
            }
            else if (isUp) {
                it.Child('time/up', cc.Label).string = '(-' + m.cd + '%)';
            }
        }
        if (it.Child('march_speed_be').active = false /* !gameHpr.isNoviceMode && data.pawns.length > 0 && data.defaultMarchSpeed > 0 */) {
            var marchSpeedLbl = it.Child('march_speed_be', cc.Label), line = it.Child('march_speed_be/line');
            marchSpeedLbl.Color(data.marchSpeed !== data.defaultMarchSpeed ? '#B6A591' : '#936E5A').setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            if (line.active = it.Child('march_speed_be', cc.Button).interactable = !((_b = m.states) === null || _b === void 0 ? void 0 : _b.length)) {
                marchSpeedLbl._forceUpdateRenderData();
                line.width = marchSpeedLbl.node.width;
            }
        }
    };
    // 刷新状态
    SelectTondenArmyPnlCtrl.prototype.updateArmyState = function (node, states) {
        node.Child('state').Items(states, function (it, state) {
            it.Color(Constant_1.ARMY_STATE_COLOR[state]).setLocaleKey('ui.army_state_' + state);
        });
    };
    SelectTondenArmyPnlCtrl.prototype.do = function () {
        if (!this.selectArmy) {
            return;
        }
        this.cb && this.cb(this.selectArmy);
        this.hide();
    };
    SelectTondenArmyPnlCtrl = __decorate([
        ccclass
    ], SelectTondenArmyPnlCtrl);
    return SelectTondenArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectTondenArmyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXFNlbGVjdFRvbmRlbkFybXlQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyREFBeUo7QUFFekoscURBQW9EO0FBQ3BELHFEQUFpRTtBQUNqRSwwREFBcUQ7QUFDckQsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFHcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBcUQsMkNBQWM7SUFBbkU7UUFBQSxxRUFpV0M7UUEvVkEsMEJBQTBCO1FBQ2xCLGNBQVEsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7UUFDN0MsYUFBTyxHQUFrQixJQUFJLENBQUEsQ0FBQyxzQkFBc0I7UUFDcEQsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLHFCQUFxQjtRQUMvQyx1QkFBaUIsR0FBWSxJQUFJLENBQUEsQ0FBQyw4QkFBOEI7UUFDaEUsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQyx3QkFBd0I7UUFDN0QsTUFBTTtRQUVFLFFBQUUsR0FBYSxJQUFJLENBQUE7UUFDbkIsZ0JBQVUsR0FBZSxJQUFJLENBQUE7UUFDN0IsNEJBQXNCLEdBQVcsQ0FBQyxDQUFBLENBQUMsWUFBWTtRQUMvQyxXQUFLLEdBQVUsRUFBRSxDQUFBLENBQUMsU0FBUztRQUMzQixnQkFBVSxHQUFrQixJQUFJLENBQUEsQ0FBQyxPQUFPOztJQW1WakQsQ0FBQztJQWpWTyxpREFBZSxHQUF0Qjs7UUFDQyxPQUFPO3NCQUNKLEdBQUMsbUJBQVMsQ0FBQyxtQkFBbUIsSUFBRyxJQUFJLENBQUMsbUJBQW1CLEVBQUUsUUFBSyxHQUFFLElBQUk7U0FDeEUsQ0FBQTtJQUNGLENBQUM7SUFFWSwwQ0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSx5Q0FBTyxHQUFkLFVBQWUsS0FBYSxFQUFFLElBQXFCLEVBQUUsWUFBb0IsRUFBRSxFQUFZO1FBQXZGLGlCQWdFQzs7UUEvREEsSUFBSSxDQUFDLHNCQUFzQixHQUFHLFlBQVksSUFBSSxDQUFDLENBQUE7UUFDL0MsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUE7UUFDWixJQUFJLENBQUMsVUFBVSxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQ3hELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFLENBQUE7UUFDMUMsSUFBTSxNQUFNLEdBQUcsb0JBQU8sQ0FBQyxNQUFNLENBQUE7UUFDN0IsU0FBUztRQUNULElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sRUFBRSxDQUFDLENBQUE7UUFDbkUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsVUFBVSxDQUFDLENBQUMsWUFBWSxDQUFDLHlCQUF5QixFQUFFLE9BQUEsTUFBTSxDQUFDLHFCQUFxQixFQUFFLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsMENBQUcsQ0FBQyxNQUFLLENBQUMsQ0FBQyxDQUFBO1FBQ3pJLElBQU0sR0FBRyxHQUFHLG9CQUFPLENBQUMscUJBQXFCLENBQUMsZUFBTyxDQUFDLG9CQUFvQixDQUFDLENBQUE7UUFDdkUsSUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsZUFBZSxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsR0FBRyxHQUFHLEVBQVAsQ0FBTyxDQUFDLENBQUE7UUFDOUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsMkJBQTJCLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsTUFBTSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDdkgsT0FBTztRQUNQLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUE7UUFDM0IsSUFBTSxRQUFRLEdBQUcsb0JBQU8sQ0FBQyxxQkFBcUIsQ0FBQyxlQUFPLENBQUMsY0FBYyxDQUFDLENBQUE7UUFDdEUsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxHQUFHLEVBQUUsQ0FBQyxXQUFXLENBQUMsQ0FBQyxHQUFHLFFBQVEsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQzdELElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxJQUFJLEVBQUUsU0FBUyxDQUFDLENBQUE7UUFDdkgsSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLFFBQVEsRUFBRTtZQUNuRCxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxHQUFHLEdBQUcsUUFBUSxHQUFHLEdBQUcsQ0FBQTtTQUN0RTtRQUNELE9BQU87UUFDUCxJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTtRQUM1QyxPQUFPO1FBQ1AsSUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQzlFLElBQUksQ0FBQyxXQUFXLEVBQUU7U0FDakI7YUFBTSxJQUFJLFlBQVksQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsb0JBQU8sQ0FBQyxZQUFZLEVBQUUsRUFBRTtZQUM5RSxJQUFNLFdBQVcsR0FBRyxDQUFDLElBQUksQ0FBQyxZQUFZLElBQUksQ0FBQyxDQUFDLEdBQUcsNkJBQWtCLENBQUE7WUFDakUsSUFBTSxVQUFVLEdBQUcsTUFBTSxDQUFDLFVBQVUsRUFBRSxDQUFBO1lBQ3RDLFlBQVksQ0FBQyxLQUFLLENBQUMsaUJBQWlCLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxVQUFVLElBQUksV0FBVyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLEdBQUcsV0FBVyxDQUFBO1NBQ2xJO1FBQ0QsT0FBTztRQUNQLElBQU0sVUFBVSxHQUFHLG9CQUFPLENBQUMscUJBQXFCLENBQUMsZUFBTyxDQUFDLGNBQWMsQ0FBQyxJQUFJLDZCQUFrQixDQUFBLENBQUMsTUFBTTtRQUNyRyxZQUFZO1FBQ1osSUFBSSxFQUFFLEdBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxHQUFHLG9CQUFPLENBQUMscUJBQXFCLENBQUMsZUFBTyxDQUFDLFFBQVEsQ0FBQyxDQUFBO1FBQ2pGLFFBQVE7UUFDUixFQUFFLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsQ0FBQyxTQUFTLENBQUMsZUFBTyxDQUFDLFFBQVEsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFBO1FBQzdFLE9BQU87UUFDUCxFQUFFLElBQUksb0JBQU8sQ0FBQyx3QkFBd0IsQ0FBQyxvQkFBTyxDQUFDLE1BQU0sRUFBRSxFQUFFLGVBQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUMxRSxPQUFPO1FBQ1AsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLFVBQUEsQ0FBQztZQUN0QixDQUFDLENBQUMsaUJBQWlCLEdBQUcsb0JBQU8sQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNsRCxDQUFDLENBQUMsVUFBVSxHQUFHLENBQUMsQ0FBQyxVQUFVLElBQUksQ0FBQyxDQUFDLGlCQUFpQixDQUFBO1lBQ2xELENBQUMsQ0FBQyxHQUFHLEdBQUcsb0JBQU8sQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLEtBQUssRUFBRSxLQUFLLENBQUMsQ0FBQTtZQUMvQyxPQUFPO1lBQ1AsSUFBSSxZQUFZLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLEVBQUUsWUFBWSxHQUFHLENBQUMsQ0FBQTtZQUMvQyxPQUFPO1lBQ1AsR0FBRyxJQUFJLEtBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEtBQUssRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDekQsU0FBUztZQUNULFlBQVksR0FBRyxvQkFBTyxDQUFDLHdCQUF3QixDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDL0QsR0FBRyxJQUFJLENBQUMsZ0NBQXFCLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7WUFDakQsWUFBWSxHQUFHLFlBQVksSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ3hELE9BQU87Z0JBQ04sR0FBRyxFQUFFLENBQUMsQ0FBQyxHQUFHO2dCQUNWLElBQUksRUFBRSxDQUFDO2dCQUNQLEdBQUcsRUFBRSxDQUFDLENBQUMsR0FBRztnQkFDVixZQUFZLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsWUFBWSxDQUFDO2dCQUN2QyxFQUFFLEVBQUUsRUFBRTtnQkFDTixJQUFJLEVBQUUsS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLEVBQUUsRUFBRSxFQUFFLEdBQUcsQ0FBQztnQkFDbkMsWUFBWSxFQUFFLFlBQVk7Z0JBQzFCLFVBQVUsRUFBRSxHQUFHO2dCQUNmLE1BQU0sRUFBRSxFQUFFO2FBQ1YsQ0FBQTtRQUNGLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFBO0lBQ25CLENBQUM7SUFFTSwwQ0FBUSxHQUFmO1FBQ0MsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDZixDQUFDO0lBRU0seUNBQU8sR0FBZDtRQUNDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDeEMsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0IsMkNBQTJDO0lBQzNDLDZDQUFXLEdBQVgsVUFBWSxLQUEwQixFQUFFLENBQVM7UUFBakQsaUJBa0JDOztRQWpCQSxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQU0sSUFBSSxHQUFrQixLQUFLLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQTtRQUNwRCxJQUFNLEtBQUssZUFBYyxLQUFLLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQywwQ0FBRSxNQUFNLDBDQUFHLENBQUMsQ0FBQyxDQUFBO1FBQ2xFLElBQUksS0FBSyxFQUFFO1lBQ1YsSUFBSSxHQUFHLEdBQUcsZ0JBQWdCLEdBQUcsS0FBSyxDQUFBO1lBQ2xDLElBQUksS0FBSyxLQUFLLGlCQUFTLENBQUMsS0FBSyxFQUFFO2dCQUM5QixHQUFHLEdBQUcsK0JBQStCLENBQUE7YUFDckM7WUFDRCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1NBQ2hDO2FBQU0sSUFBSSxPQUFBLElBQUksQ0FBQyxVQUFVLDBDQUFFLEdBQUcsTUFBSyxJQUFJLENBQUMsR0FBRyxFQUFFO1lBQzdDLE9BQU07U0FDTjthQUFNLElBQUksSUFBSSxDQUFDLHNCQUFzQixHQUFHLENBQUMsSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxFQUFFO1lBQ25GLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFBO1NBQ3RCO2FBQU07WUFDTixPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLGlDQUFpQyxDQUFDLENBQUE7U0FDOUQ7UUFDRCxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxFQUF4QixDQUF3QixDQUFDLENBQUE7SUFDckUsQ0FBQztJQUVELGtEQUFrRDtJQUNsRCw0Q0FBVSxHQUFWLFVBQVcsS0FBMEIsRUFBRSxDQUFTO1FBQy9DLElBQU0sSUFBSSxHQUFrQixLQUFLLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUE7UUFDcEQsSUFBSSxJQUFJLEVBQUU7WUFDVCxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7WUFDWCxvQkFBTyxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDakM7SUFDRixDQUFDO0lBRUQsMERBQTBEO0lBQzFELG1EQUFpQixHQUFqQixVQUFrQixLQUEwQixFQUFFLElBQVk7UUFBMUQsaUJBb0JDO1FBbkJBLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFBO1FBQzlCLElBQU0sSUFBSSxHQUFrQixFQUFFLGFBQUYsRUFBRSx1QkFBRixFQUFFLENBQUUsSUFBSSxDQUFBO1FBQ3BDLElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDVixPQUFNO1NBQ047UUFDRCx1QkFBVSxDQUFDLE9BQU8sQ0FBQyx1QkFBdUIsRUFBRSxJQUFJLEVBQUUsVUFBQyxLQUFhO1lBQy9ELElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLFVBQVUsS0FBSyxLQUFLLEVBQUU7Z0JBQ3hDLE9BQU07YUFDTjtZQUNELElBQUksQ0FBQyxVQUFVLEdBQUcsS0FBSyxDQUFBO1lBQ3ZCLElBQUksS0FBSSxDQUFDLE9BQU8sRUFBRTtnQkFDakIsSUFBTSxNQUFJLEdBQUcsS0FBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLEVBQWxCLENBQWtCLENBQUMsQ0FBQTtnQkFDckQsSUFBSSxDQUFDLE1BQUksRUFBRTtvQkFDVixPQUFNO2lCQUNOO2dCQUNELE1BQUksQ0FBQyxJQUFJLEdBQUcsS0FBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLEVBQUUsTUFBSSxDQUFDLEVBQUUsRUFBRSxNQUFJLENBQUMsVUFBVSxDQUFDLENBQUE7Z0JBQzdELEtBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFBO2FBQzlCO1FBQ0YsQ0FBQyxDQUFDLENBQUE7SUFDSCxDQUFDO0lBRUQsOEJBQThCO0lBQzlCLDJDQUFTLEdBQVQsVUFBVSxLQUEwQixFQUFFLElBQVk7UUFBbEQsaUJBdUJDOztRQXRCQSxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNyQixPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDBCQUEwQixDQUFDLENBQUE7U0FDdkQ7YUFBTSxJQUFJLG9CQUFPLENBQUMsTUFBTSxDQUFDLFVBQVUsRUFBRSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFLENBQUMsWUFBWSxHQUFHLDZCQUFrQixFQUFFO1lBQ3pHLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLGtCQUFrQixDQUFDLENBQUE7U0FDckQ7UUFDRCxJQUFNLGlCQUFpQixHQUFHLE9BQUEsSUFBSSxDQUFDLFVBQVUsQ0FBQyxXQUFXLEVBQUUsMENBQUUsZUFBZSxLQUFJLEtBQUssQ0FBQTtRQUMzRSxJQUFBLEtBQUEsT0FBdUIsRUFBRSxDQUFDLGVBQWUsQ0FBQyxpQkFBaUIsRUFBRSxHQUFHLENBQUMsSUFBQSxFQUFoRSxRQUFRLFFBQUEsRUFBRSxRQUFRLFFBQThDLENBQUE7UUFDdkUsSUFBSSxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUE7UUFDcEIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQzs7WUFDOUIsR0FBRyxJQUFJLENBQUMsQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFBO1lBQ3pCLEdBQUcsSUFBSSxDQUFDLE9BQUEsU0FBUyxDQUFDLFdBQVcsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQywwQ0FBRSxPQUFPLEtBQUksQ0FBQyxDQUFDLENBQUE7UUFDL0QsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFNLEdBQUcsR0FBRyxHQUFHLEdBQUcsR0FBRyxDQUFBO1FBQ3JCLElBQUksUUFBUSxHQUFHLEdBQUcsRUFBRTtZQUNuQix1QkFBVSxDQUFDLGNBQWMsQ0FBQyw4QkFBOEIsRUFBRTtnQkFDekQsTUFBTSxFQUFFLGdCQUFnQjtnQkFDeEIsRUFBRSxFQUFFLGNBQU0sT0FBQSxLQUFJLENBQUMsRUFBRSxFQUFFLEVBQVQsQ0FBUztnQkFDbkIsTUFBTSxFQUFFLGNBQVEsQ0FBQzthQUNqQixDQUFDLENBQUE7WUFDRixPQUFNO1NBQ047UUFDRCxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUE7SUFDVixDQUFDO0lBRUQsMENBQTBDO0lBQzFDLHFEQUFtQixHQUFuQixVQUFvQixLQUEwQixFQUFFLElBQVk7UUFDM0QsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ3BCLHVCQUFVLENBQUMsT0FBTyxDQUFDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxFQUFFLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsQ0FBQTtTQUNsRztJQUNGLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRXpHLHFEQUFtQixHQUEzQjtRQUNDLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO0lBQ3pCLENBQUM7SUFDRCxpSEFBaUg7SUFFekcsbURBQWlCLEdBQXpCO1FBQ0MsSUFBTSxXQUFXLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsaUNBQXNCLEdBQUcsb0JBQU8sQ0FBQyxNQUFNLENBQUMsa0JBQWtCLEVBQUUsQ0FBQyxFQUM1RixXQUFXLEdBQUcsV0FBVyxHQUFHLENBQUMsRUFBRSxhQUFhLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsb0JBQW9CLEVBQUUsRUFDbkYsV0FBVyxHQUFHLENBQUMsV0FBVyxJQUFJLGFBQWEsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsc0JBQW9CLGFBQWEsVUFBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUE7UUFDakcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFlBQVksQ0FBQyx5QkFBeUIsRUFBRSxhQUFVLFdBQVcsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTLFVBQUksV0FBVyxZQUFPLFdBQWEsQ0FBQyxDQUFBO1FBQ2hKLE9BQU8sV0FBVyxDQUFBO0lBQ25CLENBQUM7SUFFTyw4Q0FBWSxHQUFwQixVQUFxQixJQUFtQixFQUFFLEVBQVUsRUFBRSxHQUFXOztRQUNoRSxJQUFJLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxHQUFHLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQTtRQUNsRSxhQUFhO1FBQ2IsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQ3pDLFVBQVU7UUFDVixJQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsS0FBSyxZQUFLLElBQUksQ0FBQyxVQUFVLDBDQUFFLEtBQUssQ0FBQSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQTtRQUNoRSxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEdBQUcsR0FBRyxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUMzRSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUE7SUFDdEIsQ0FBQztJQUVELGFBQWE7SUFDTCxpREFBZSxHQUF2QjtRQUNDLElBQU0sS0FBSyxHQUFHLG9CQUFPLENBQUMsTUFBTSxDQUFDLGFBQWEsRUFBRSxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBSSxFQUFiLENBQWEsQ0FBQyxDQUFBO1FBQ3JFLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDWCxPQUFPLENBQUMsQ0FBQTtTQUNSO1FBQ0QsSUFBTSxJQUFJLEdBQUcsU0FBUyxDQUFDLFdBQVcsQ0FBQyxXQUFXLEVBQUUsS0FBSyxDQUFDLEVBQUUsR0FBRyxJQUFJLEdBQUcsS0FBSyxDQUFDLEVBQUUsQ0FBQyxDQUFBO1FBQzNFLE9BQU8sSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7SUFDaEUsQ0FBQztJQUVELE9BQU87SUFDQyw4Q0FBWSxHQUFwQixVQUFxQixNQUFjLEVBQUUsTUFBYztRQUNsRCxJQUFNLEtBQUssR0FBRyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUNyRCxJQUFNLEtBQUssR0FBRyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUNyRCxPQUFPLENBQUEsS0FBSyxhQUFMLEtBQUssdUJBQUwsS0FBSyxDQUFFLHFCQUFxQixRQUFNLEtBQUssYUFBTCxLQUFLLHVCQUFMLEtBQUssQ0FBRSxxQkFBcUIsR0FBRSxDQUFBO0lBQ3hFLENBQUM7SUFFRCxPQUFPO0lBQ0MsNkNBQVcsR0FBbkIsVUFBb0IsV0FBMkIsRUFBRSxZQUE0QjtRQUE3RSxpQkF5RUM7O1FBekVtQiw0QkFBQSxFQUFBLGtCQUEyQjtRQUFFLDZCQUFBLEVBQUEsbUJBQTRCO1FBQzVFLE9BQU87UUFDUCxJQUFNLGNBQWMsR0FBRyxFQUFFLENBQUE7UUFDekIsb0JBQU8sQ0FBQyxNQUFNLENBQUMscUJBQXFCLEVBQUUsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxjQUFjLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEVBQTdCLENBQTZCLENBQUMsQ0FBQTtRQUNsRixPQUFPO1FBQ1AsSUFBSSxXQUFXLEVBQUU7WUFDaEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUNuQixJQUFNLE1BQU0sR0FBZ0IsRUFBRSxFQUFFLElBQUksR0FBa0IsQ0FBQyxDQUFDLElBQUksQ0FBQTtnQkFDNUQsSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLGlCQUFTLENBQUMsSUFBSSxFQUFFO29CQUNsQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtpQkFDdkI7cUJBQU07b0JBQ04sSUFBSSxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7d0JBQy9CLE1BQU0sQ0FBQyxJQUFJLENBQUMsaUJBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtxQkFDNUI7b0JBQ0QsSUFBSSxJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7d0JBQ2hDLE1BQU0sQ0FBQyxJQUFJLENBQUMsaUJBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQTtxQkFDN0I7b0JBQ0QsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLGNBQWMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQXJCLENBQXFCLENBQUMsRUFBRTt3QkFDaEQsTUFBTSxDQUFDLElBQUksQ0FBQyxpQkFBUyxDQUFDLEtBQUssQ0FBQyxDQUFBO3FCQUM1QjtvQkFDRCxJQUFJLG9CQUFPLENBQUMsS0FBSyxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFO3dCQUMxRCxNQUFNLENBQUMsSUFBSSxDQUFDLGlCQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7cUJBQzdCO2lCQUNEO2dCQUNELENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFBO1lBQ2xCLENBQUMsQ0FBQyxDQUFBO1NBQ0Y7UUFDRCxLQUFLO1FBQ0wsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7Z0JBQy9DLE9BQU8sQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTthQUM5QztZQUNELE9BQU8sQ0FBQyxDQUFDLElBQUksR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFBO1FBQ3ZCLENBQUMsQ0FBQyxDQUFBO1FBQ0Ysb0JBQW9CO1FBQ3BCLElBQUksWUFBWSxFQUFFO1lBQ2pCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDMUIsSUFBSSxJQUFJLElBQUksUUFBQyxJQUFJLENBQUMsTUFBTSwwQ0FBRSxNQUFNLENBQUEsRUFBRTtnQkFDakMsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFBO2FBQzNCO1NBQ0Q7UUFDRCxFQUFFO1FBQ0YsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUM3QixJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQzFCLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsVUFBQyxFQUFFLEVBQUUsQ0FBQztZQUNwQyxFQUFFLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ2YsSUFBTSxJQUFJLEdBQWtCLEVBQUUsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQTtZQUM1QyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUE7WUFDN0MsdUJBQVUsQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUM3RCxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLDBCQUEwQixFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUM1RSxJQUFNLEtBQUssR0FBVSxJQUFJLENBQUMsS0FBSyxDQUFBO1lBQy9CLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLEVBQUUsVUFBQyxLQUFLLEVBQUUsSUFBSTs7Z0JBQzNGLElBQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxHQUFHLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxRQUFRLEVBQUUsUUFBUSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFBO2dCQUMvRixJQUFNLE9BQU8sR0FBRyxDQUFDLElBQUksSUFBSSxDQUFDLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQTtnQkFDaEUsSUFBTSxFQUFFLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFBO2dCQUN2RCxxQkFBUyxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQUEsSUFBSSxDQUFDLFNBQVMsMENBQUUsRUFBRSxLQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO2dCQUM3RixJQUFJLENBQUMsT0FBTyxHQUFHLENBQUMsSUFBSSxJQUFJLE9BQU8sSUFBSSxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUE7Z0JBQ3hELEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLElBQUksSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQTtnQkFDNUcsSUFBSSxLQUFLLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUU7b0JBQ3BELEtBQUssQ0FBQyxLQUFLLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFBO2lCQUNwRTtZQUNGLENBQUMsQ0FBQyxDQUFBO1lBQ0YsT0FBTztZQUNQLEtBQUksQ0FBQyxlQUFlLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQTtZQUNsQyxPQUFPO1lBQ1AsS0FBSSxDQUFDLG9CQUFvQixDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQTtZQUNoQyxLQUFLO1lBQ0wsS0FBSSxDQUFDLGdCQUFnQixDQUFDLEVBQUUsQ0FBQyxDQUFBO1lBQ3pCLEVBQUU7WUFDRixJQUFNLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFBO1lBQ2xDLEVBQUUsQ0FBQyxPQUFPLEdBQUcsU0FBUyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQTtZQUNsQyxFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQTtRQUM1QyxDQUFDLENBQUMsQ0FBQTtJQUNILENBQUM7SUFFRCxPQUFPO0lBQ0Msa0RBQWdCLEdBQXhCLFVBQXlCLEVBQVc7O1FBQ25DLElBQU0sTUFBTSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUMsTUFBTSxHQUFHLE9BQUEsRUFBRSxDQUFDLElBQUksMENBQUUsR0FBRyxhQUFLLElBQUksQ0FBQyxVQUFVLDBDQUFFLEdBQUcsQ0FBQSxDQUFBO1FBQ2hGLGtCQUFrQjtRQUNsQixJQUFNLFNBQVMsR0FBRyxNQUFNLElBQUksQ0FBQyxjQUFDLEVBQUUsQ0FBQyxPQUFPLENBQUMsMENBQUUsTUFBTSwwQ0FBRSxNQUFNLENBQUEsQ0FBQTtRQUN6RCxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUE7UUFDM0QsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksR0FBRyxTQUFTLENBQUE7UUFDdEQsRUFBRSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsQ0FBQyxNQUFNLEdBQUcsU0FBUyxDQUFBO0lBQzNDLENBQUM7SUFFRCxPQUFPO0lBQ0Msc0RBQW9CLEdBQTVCLFVBQTZCLEVBQVcsRUFBRSxDQUFNOztRQUMvQyxJQUFNLElBQUksR0FBa0IsQ0FBQyxDQUFDLElBQUksQ0FBQTtRQUNsQyxJQUFJLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxHQUFHLFFBQUMsQ0FBQyxDQUFDLE1BQU0sMENBQUUsTUFBTSxDQUFBLEVBQUU7WUFDaEQsRUFBRSxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFDLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsU0FBUyxDQUFDLENBQUE7WUFDL0gsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLFlBQVksR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksR0FBRyxDQUFDLENBQUE7WUFDNUUsSUFBTSxJQUFJLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksR0FBRyxDQUFDLENBQUE7WUFDeEUsSUFBSSxLQUFLLEVBQUU7Z0JBQ1YsRUFBRSxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxvQkFBb0IsR0FBRyxDQUFDLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFBO2FBQ2hIO2lCQUFNLElBQUksSUFBSSxFQUFFO2dCQUNoQixFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksR0FBRyxDQUFDLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQTthQUN6RDtTQUNEO1FBQ0QsSUFBSSxFQUFFLENBQUMsS0FBSyxDQUFDLGdCQUFnQixDQUFDLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQSxrRkFBa0YsRUFBRTtZQUNoSSxJQUFNLGFBQWEsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsRUFBRSxJQUFJLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxxQkFBcUIsQ0FBQyxDQUFBO1lBQ2xHLGFBQWEsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFVBQVUsS0FBSyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsWUFBWSxDQUFDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtZQUM1SSxJQUFJLElBQUksQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxHQUFHLFFBQUMsQ0FBQyxDQUFDLE1BQU0sMENBQUUsTUFBTSxDQUFBLEVBQUU7Z0JBQ3pGLGFBQWEsQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO2dCQUN0QyxJQUFJLENBQUMsS0FBSyxHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFBO2FBQ3JDO1NBQ0Q7SUFDRixDQUFDO0lBRUQsT0FBTztJQUNDLGlEQUFlLEdBQXZCLFVBQXdCLElBQWEsRUFBRSxNQUFtQjtRQUN6RCxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsVUFBQyxFQUFFLEVBQUUsS0FBSztZQUMzQyxFQUFFLENBQUMsS0FBSyxDQUFDLDJCQUFnQixDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsWUFBWSxDQUFDLGdCQUFnQixHQUFHLEtBQUssQ0FBQyxDQUFBO1FBQ3pFLENBQUMsQ0FBQyxDQUFBO0lBQ0gsQ0FBQztJQUVPLG9DQUFFLEdBQVY7UUFDQyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNyQixPQUFNO1NBQ047UUFDRCxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFBO1FBQ25DLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtJQUNaLENBQUM7SUFoV21CLHVCQUF1QjtRQUQzQyxPQUFPO09BQ2EsdUJBQXVCLENBaVczQztJQUFELDhCQUFDO0NBaldELEFBaVdDLENBaldvRCxFQUFFLENBQUMsV0FBVyxHQWlXbEU7a0JBaldvQix1QkFBdUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBUk1ZX1NUQVRFX0NPTE9SLCBNQUlOX0NJVFlfTUFSQ0hfU1BFRUQsIFRPREFZX1RPTkRFTl9NQVhfQ09VTlQsIFRPTkRFTl9TVEFNSU5BX01VTCwgVVBfTUFSQ0hfU1BFRURfTVVMIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xuaW1wb3J0IHsgQXJteVNob3J0SW5mbyB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRGF0YVR5cGVcIjtcbmltcG9ydCB7IGVjb2RlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FQ29kZVwiO1xuaW1wb3J0IHsgQXJteVN0YXRlLCBDRWZmZWN0IH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBNYXBDZWxsT2JqIGZyb20gXCIuLi8uLi9tb2RlbC9tYWluL01hcENlbGxPYmpcIjtcblxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5AY2NjbGFzc1xuZXhwb3J0IGRlZmF1bHQgY2xhc3MgU2VsZWN0VG9uZGVuQXJteVBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cblx0Ly9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cblx0cHJpdmF0ZSB0b3BOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvdG9wX25cblx0cHJpdmF0ZSBsaXN0U3ZfOiBjYy5TY3JvbGxWaWV3ID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9saXN0X3N2XG5cdHByaXZhdGUgdGltZU5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC90aW1lX25cblx0cHJpdmF0ZSBzdXJwbHVzQ291bnROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Qvc3VycGx1c19jb3VudF9uXG5cdHByaXZhdGUgYnV0dG9uc05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9idXR0b25zX25cblx0Ly9AZW5kXG5cblx0cHJpdmF0ZSBjYjogRnVuY3Rpb24gPSBudWxsXG5cdHByaXZhdGUgdGFyZ2V0Q2VsbDogTWFwQ2VsbE9iaiA9IG51bGxcblx0cHJpdmF0ZSB0YXJnZXRBcmVhTWF4QXJteUNvdW50OiBudW1iZXIgPSAwIC8v55uu5qCH5Yy65Z+f5pyA5aSn5Yab6Zif5pWw6YePXG5cdHByaXZhdGUgYXJteXM6IGFueVtdID0gW10gLy/lvZPliY3nmoTlhpvpmJ/liJfooahcblx0cHJpdmF0ZSBzZWxlY3RBcm15OiBBcm15U2hvcnRJbmZvID0gbnVsbCAvL+W9k+WJjemAieaLqeeahFxuXG5cdHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG5cdFx0cmV0dXJuIFtcblx0XHRcdHsgW0V2ZW50VHlwZS5VUERBVEVfVE9OREVOX0NPVU5UXTogdGhpcy5vblVwZGF0ZVRvbmRlbkNvdW50LCBlbnRlcjogdHJ1ZSB9XG5cdFx0XVxuXHR9XG5cblx0cHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuXHR9XG5cblx0cHVibGljIG9uRW50ZXIoaW5kZXg6IG51bWJlciwgbGlzdDogQXJteVNob3J0SW5mb1tdLCBjYW5Hb3RvQ291bnQ6IG51bWJlciwgY2I6IEZ1bmN0aW9uKSB7XG5cdFx0dGhpcy50YXJnZXRBcmVhTWF4QXJteUNvdW50ID0gY2FuR290b0NvdW50IHx8IDBcblx0XHR0aGlzLmNiID0gY2Jcblx0XHR0aGlzLnRhcmdldENlbGwgPSBnYW1lSHByLndvcmxkLmdldE1hcENlbGxCeUluZGV4KGluZGV4KVxuXHRcdGNvbnN0IGpzb24gPSB0aGlzLnRhcmdldENlbGwuZ2V0TGFuZEF0dHIoKVxuXHRcdGNvbnN0IHBsYXllciA9IGdhbWVIcHIucGxheWVyXG5cdFx0Ly8g5pi+56S65a6d566x5L+h5oGvXG5cdFx0dGhpcy50b3BOb2RlXy5DaGlsZCgnbmFtZScpLnNldExvY2FsZUtleSh0aGlzLnRhcmdldENlbGwuZ2V0TmFtZSgpKVxuXHRcdHRoaXMudG9wTm9kZV8uQ2hpbGQoJ2luZm8vdmFsJykuc2V0TG9jYWxlS2V5KCd1aS50b25kZW5feWV0X2NlbGxfZGVzYycsIHBsYXllci5nZXRPY2N1cHlMYW5kQ291bnRNYXAoKVt0aGlzLnRhcmdldENlbGwubGFuZEx2XT8uWzFdIHx8IDEpXG5cdFx0Y29uc3QgYWRkID0gZ2FtZUhwci5nZXRQbGF5ZXJQb2xpY3lFZmZlY3QoQ0VmZmVjdC5DRUxMX1RPTkRFTl9UUkVBU1VSRSlcblx0XHRjb25zdCBjb3VudHMgPSB1dC5zdHJpbmdUb051bWJlcnMoanNvbi50cmVhc3VyZXNfY291bnQsICcsJykubWFwKG0gPT4gbSArIGFkZClcblx0XHR0aGlzLnRvcE5vZGVfLkNoaWxkKCdpbmZvL2Ryb3BfdHJlYXN1cmVfYmUvdmFsJywgY2MuTGFiZWwpLkNvbG9yKGFkZCA/ICcjNkJGRjQ0JyA6ICcjRkZGRkZGJykuc3RyaW5nID0gY291bnRzLmpvaW4oJ34nKVxuXHRcdC8vIOWxr+eUsOaXtumXtFxuXHRcdGxldCB0aW1lID0ganNvbi50b25kZW5fdGltZVxuXHRcdGNvbnN0IHRvbmRlbkNkID0gZ2FtZUhwci5nZXRQbGF5ZXJQb2xpY3lFZmZlY3QoQ0VmZmVjdC5DRUxMX1RPTkRFTl9DRClcblx0XHR0aW1lID0gTWF0aC5mbG9vcih0aW1lICogdXQubnVtYmVyRml4ZWQoMSAtIHRvbmRlbkNkICogMC4wMSkpXG5cdFx0dGhpcy50aW1lTm9kZV8uQ2hpbGQoJ3ZhbCcsIGNjLkxhYmVsKS5Db2xvcih0b25kZW5DZCA/ICcjNEFCMzJFJyA6ICcjOTM2RTVBJykuc3RyaW5nID0gdXQuc2Vjb25kRm9ybWF0KHRpbWUsICdoOm1tOnNzJylcblx0XHRpZiAodGhpcy50aW1lTm9kZV8uQ2hpbGQoJ3VwJykuYWN0aXZlID0gISF0b25kZW5DZCkge1xuXHRcdFx0dGhpcy50aW1lTm9kZV8uQ2hpbGQoJ3VwL3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSAnLScgKyB0b25kZW5DZCArICclJ1xuXHRcdH1cblx0XHQvLyDliankvZnmrKHmlbBcblx0XHRjb25zdCBpc0NhblRvbmRlbiA9IHRoaXMudXBkYXRlVG9uZGVuQ291bnQoKVxuXHRcdC8vIOWIt+aWsOaMiemSrlxuXHRcdGNvbnN0IG9rQnV0dG9uTm9kZSA9IHRoaXMuYnV0dG9uc05vZGVfLlN3aWgoaXNDYW5Ub25kZW4gPyAnb2tfYmUnIDogJ3dhaXQnKVswXVxuXHRcdGlmICghaXNDYW5Ub25kZW4pIHtcblx0XHR9IGVsc2UgaWYgKG9rQnV0dG9uTm9kZS5DaGlsZCgnbGF5L3N0YW1pbmEnKS5hY3RpdmUgPSAhZ2FtZUhwci5pc0ZyZWVTZXJ2ZXIoKSkge1xuXHRcdFx0Y29uc3QgbmVlZFN0YW1pbmEgPSAoanNvbi5uZWVkX3N0YW1pbmEgfHwgMCkgKiBUT05ERU5fU1RBTUlOQV9NVUxcblx0XHRcdGNvbnN0IGN1clN0YW1pbmEgPSBwbGF5ZXIuZ2V0U3RhbWluYSgpXG5cdFx0XHRva0J1dHRvbk5vZGUuQ2hpbGQoJ2xheS9zdGFtaW5hL3ZhbCcsIGNjLkxhYmVsKS5Db2xvcihjdXJTdGFtaW5hID49IG5lZWRTdGFtaW5hID8gJyMzRjMzMkYnIDogJyNENzYzNEQnKS5zdHJpbmcgPSAnJyArIG5lZWRTdGFtaW5hXG5cdFx0fVxuXHRcdC8vIOWIt+aWsOWIl+ihqFxuXHRcdGNvbnN0IHVwU3BlZWRNdWwgPSBnYW1lSHByLmdldFBsYXllclBvbGljeUVmZmVjdChDRWZmZWN0Lk1BSU5fTUFSQ0hfTVVMKSB8fCBVUF9NQVJDSF9TUEVFRF9NVUwgLy/liqDpgJ/lgI3mlbBcblx0XHQvLyDmmK/lkKbnrKzkuIDmrKHmlrDmiYvlvJXlr7xcblx0XHRsZXQgY2QgPSB0aGlzLmdldEJ1aWxkTWFyY2hDRCgpICsgZ2FtZUhwci5nZXRQbGF5ZXJQb2xpY3lFZmZlY3QoQ0VmZmVjdC5NQVJDSF9DRClcblx0XHQvLyDliqDpgJ/lraPoioLnmoRcblx0XHRjZCArPSBNYXRoLmZsb29yKGdhbWVIcHIud29ybGQuZ2V0U2Vhc29uKCkuZ2V0RWZmZWN0KENFZmZlY3QuTUFSQ0hfQ0QpICogMTAwKVxuXHRcdC8vIOWKoOmBl+i/ueeahFxuXHRcdGNkICs9IGdhbWVIcHIuZ2V0QW5jaWVudEVmZmVjdEJ5UGxheWVyKGdhbWVIcHIuZ2V0VWlkKCksIENFZmZlY3QuTUFSQ0hfQ0QpXG5cdFx0Ly8g5Yi35paw5YiX6KGoXG5cdFx0dGhpcy5hcm15cyA9IGxpc3QubWFwKG0gPT4ge1xuXHRcdFx0bS5kZWZhdWx0TWFyY2hTcGVlZCA9IGdhbWVIcHIuZ2V0QXJteU1hcmNoU3BlZWQobSlcblx0XHRcdG0ubWFyY2hTcGVlZCA9IG0ubWFyY2hTcGVlZCB8fCBtLmRlZmF1bHRNYXJjaFNwZWVkXG5cdFx0XHRtLmRpcyA9IGdhbWVIcHIuZ2V0VG9NYXBDZWxsRGlzKG0uaW5kZXgsIGluZGV4KVxuXHRcdFx0Ly8g5piv5ZCm5Yqg6YCfXG5cdFx0XHRsZXQgdXBTcGVlZFN0YXRlID0gMCwgbXVsID0gMCwgbWFpbk91dFVwRGlzID0gMFxuXHRcdFx0Ly8g5Z+O5biC5Yqg6YCfXG5cdFx0XHRtdWwgKz0gdGhpcy5pc0NhblVwU3BlZWQobS5pbmRleCwgaW5kZXgpID8gdXBTcGVlZE11bCA6IDBcblx0XHRcdC8vIOaYr+WQpuWfjui+ueWKoOmAn1xuXHRcdFx0bWFpbk91dFVwRGlzID0gZ2FtZUhwci5nZXRNYWluT3V0TWFyY2hTZWVwVXBEaXMoaW5kZXgsIG0uaW5kZXgpXG5cdFx0XHRtdWwgKz0gKE1BSU5fQ0lUWV9NQVJDSF9TUEVFRFttYWluT3V0VXBEaXNdIHx8IDApXG5cdFx0XHR1cFNwZWVkU3RhdGUgPSBtYWluT3V0VXBEaXMgPj0gMCA/IDIgOiAobXVsID4gMCA/IDEgOiAwKVxuXHRcdFx0cmV0dXJuIHtcblx0XHRcdFx0dWlkOiBtLnVpZCxcblx0XHRcdFx0ZGF0YTogbSxcblx0XHRcdFx0ZGlzOiBtLmRpcyxcblx0XHRcdFx0bWFpbk91dFVwRGlzOiBNYXRoLm1heCgwLCBtYWluT3V0VXBEaXMpLFxuXHRcdFx0XHRjZDogY2QsXG5cdFx0XHRcdHRpbWU6IHRoaXMuZ2V0TWFyY2hUaW1lKG0sIGNkLCBtdWwpLFxuXHRcdFx0XHR1cFNwZWVkU3RhdGU6IHVwU3BlZWRTdGF0ZSxcblx0XHRcdFx0dXBTcGVlZE11bDogbXVsLFxuXHRcdFx0XHRzdGF0ZXM6IFtdLFxuXHRcdFx0fVxuXHRcdH0pXG5cdFx0dGhpcy51cGRhdGVBcm15cygpXG5cdH1cblxuXHRwdWJsaWMgb25SZW1vdmUoKSB7XG5cdFx0dGhpcy5jYiA9IG51bGxcblx0fVxuXG5cdHB1YmxpYyBvbkNsZWFuKCkge1xuXHRcdGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuXHR9XG5cblx0Ly8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblx0Ly9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cblx0Ly8gcGF0aDovL3Jvb3QvbGlzdF9zdi92aWV3L2NvbnRlbnQvaXRlbV9iZVxuXHRvbkNsaWNrSXRlbShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgXzogc3RyaW5nKSB7XG5cdFx0YXVkaW9NZ3IucGxheVNGWCgnY2xpY2snKVxuXHRcdGNvbnN0IGRhdGE6IEFybXlTaG9ydEluZm8gPSBldmVudC5jdXJyZW50VGFyZ2V0LkRhdGFcblx0XHRjb25zdCBzdGF0ZTogQXJteVN0YXRlID0gZXZlbnQuY3VycmVudFRhcmdldFsnX2RhdGEnXT8uc3RhdGVzPy5bMF1cblx0XHRpZiAoc3RhdGUpIHtcblx0XHRcdGxldCBrZXkgPSAndWkuYXJteV9zdGF0ZV8nICsgc3RhdGVcblx0XHRcdGlmIChzdGF0ZSA9PT0gQXJteVN0YXRlLkZJR0hUKSB7XG5cdFx0XHRcdGtleSA9ICd0b2FzdC5iYXR0bGluZ19jYW50X29wZXJhdGlvbidcblx0XHRcdH1cblx0XHRcdHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChrZXkpXG5cdFx0fSBlbHNlIGlmICh0aGlzLnNlbGVjdEFybXk/LnVpZCA9PT0gZGF0YS51aWQpIHtcblx0XHRcdHJldHVyblxuXHRcdH0gZWxzZSBpZiAodGhpcy50YXJnZXRBcmVhTWF4QXJteUNvdW50ID4gMCB8fCBkYXRhLmluZGV4ID09PSB0aGlzLnRhcmdldENlbGwuaW5kZXgpIHtcblx0XHRcdHRoaXMuc2VsZWN0QXJteSA9IGRhdGFcblx0XHR9IGVsc2Uge1xuXHRcdFx0cmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC55ZXRfZXhjZWVkX21heF9hcm15X2NvdW50Jylcblx0XHR9XG5cdFx0dGhpcy5saXN0U3ZfLmNvbnRlbnQuY2hpbGRyZW4uZm9yRWFjaChtID0+IHRoaXMudXBkYXRlU2VsZWN0SXRlbShtKSlcblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2xpc3Rfc3Yvdmlldy9jb250ZW50L2l0ZW1fYmUvcG9zX2JlXG5cdG9uQ2xpY2tQb3MoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIF86IHN0cmluZykge1xuXHRcdGNvbnN0IGRhdGE6IEFybXlTaG9ydEluZm8gPSBldmVudC50YXJnZXQucGFyZW50LkRhdGFcblx0XHRpZiAoZGF0YSkge1xuXHRcdFx0dGhpcy5oaWRlKClcblx0XHRcdGdhbWVIcHIuZ290b1RhcmdldFBvcyhkYXRhLmluZGV4KVxuXHRcdH1cblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2xpc3Rfc3Yvdmlldy9jb250ZW50L2l0ZW1fYmUvbWFyY2hfc3BlZWRfYmVcblx0b25DbGlja01hcmNoU3BlZWQoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuXHRcdGNvbnN0IGl0ID0gZXZlbnQudGFyZ2V0LnBhcmVudFxuXHRcdGNvbnN0IGFybXk6IEFybXlTaG9ydEluZm8gPSBpdD8uRGF0YVxuXHRcdGlmICghYXJteSkge1xuXHRcdFx0cmV0dXJuXG5cdFx0fVxuXHRcdHZpZXdIZWxwZXIuc2hvd1BubCgnbWFpbi9Nb2RpZnlNYXJjaFNwZWVkJywgYXJteSwgKHNwZWVkOiBudW1iZXIpID0+IHtcblx0XHRcdGlmICghc3BlZWQgfHwgYXJteS5tYXJjaFNwZWVkID09PSBzcGVlZCkge1xuXHRcdFx0XHRyZXR1cm5cblx0XHRcdH1cblx0XHRcdGFybXkubWFyY2hTcGVlZCA9IHNwZWVkXG5cdFx0XHRpZiAodGhpcy5pc1ZhbGlkKSB7XG5cdFx0XHRcdGNvbnN0IGRhdGEgPSB0aGlzLmFybXlzLmZpbmQobSA9PiBtLnVpZCA9PT0gYXJteS51aWQpXG5cdFx0XHRcdGlmICghZGF0YSkge1xuXHRcdFx0XHRcdHJldHVyblxuXHRcdFx0XHR9XG5cdFx0XHRcdGRhdGEudGltZSA9IHRoaXMuZ2V0TWFyY2hUaW1lKGFybXksIGRhdGEuY2QsIGRhdGEudXBTcGVlZE11bClcblx0XHRcdFx0dGhpcy51cGRhdGVBcm15cyhmYWxzZSwgZmFsc2UpXG5cdFx0XHR9XG5cdFx0fSlcblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2J1dHRvbnNfbi9va19iZVxuXHRvbkNsaWNrT2soZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuXHRcdGlmICghdGhpcy5zZWxlY3RBcm15KSB7XG5cdFx0XHRyZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnBsZWFzZV9zZWxlY3RfYXJteScpXG5cdFx0fSBlbHNlIGlmIChnYW1lSHByLnBsYXllci5nZXRTdGFtaW5hKCkgPCB0aGlzLnRhcmdldENlbGwuZ2V0TGFuZEF0dHIoKS5uZWVkX3N0YW1pbmEgKiBUT05ERU5fU1RBTUlOQV9NVUwpIHtcblx0XHRcdHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5TVEFNSU5BX05PVF9FTk9VR0gpXG5cdFx0fVxuXHRcdGNvbnN0IHRyZWFzdXJlc0NvdW50U3RyID0gdGhpcy50YXJnZXRDZWxsLmdldExhbmRBdHRyKCk/LnRyZWFzdXJlc19jb3VudCB8fCAnMCwwJ1xuXHRcdGNvbnN0IFttaW5Db3VudCwgbWF4Q291bnRdID0gdXQuc3RyaW5nVG9OdW1iZXJzKHRyZWFzdXJlc0NvdW50U3RyLCAnLCcpXG5cdFx0bGV0IGN1ciA9IDAsIG1heCA9IDBcblx0XHR0aGlzLnNlbGVjdEFybXkucGF3bnMuZm9yRWFjaChwID0+IHtcblx0XHRcdGN1ciArPSBwLnRyZWFzdXJlcy5sZW5ndGhcblx0XHRcdG1heCArPSAoYXNzZXRzTWdyLmdldEpzb25EYXRhKCdwYXduQmFzZScsIHAuaWQpPy5iYWdfY2FwIHx8IDApXG5cdFx0fSlcblx0XHRjb25zdCBjYXAgPSBtYXggLSBjdXJcblx0XHRpZiAobWF4Q291bnQgPiBjYXApIHtcblx0XHRcdHZpZXdIZWxwZXIuc2hvd01lc3NhZ2VCb3goJ3VpLnRyZWFzdXJlX2NvdW50X25vdF9lbm91Z2gnLCB7XG5cdFx0XHRcdG9rVGV4dDogJ3VpLmJ1dHRvbl9nb29uJyxcblx0XHRcdFx0b2s6ICgpID0+IHRoaXMuZG8oKSxcblx0XHRcdFx0Y2FuY2VsOiAoKSA9PiB7IH1cblx0XHRcdH0pXG5cdFx0XHRyZXR1cm5cblx0XHR9XG5cdFx0dGhpcy5kbygpXG5cdH1cblxuXHQvLyBwYXRoOi8vcm9vdC90b3Bfbi9pbmZvL2Ryb3BfdHJlYXN1cmVfYmVcblx0b25DbGlja0Ryb3BUcmVhc3VyZShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG5cdFx0aWYgKHRoaXMudGFyZ2V0Q2VsbCkge1xuXHRcdFx0dmlld0hlbHBlci5zaG93UG5sKCdtYWluL0NlbGxUb25kZW5JbmZvJywgdGhpcy50YXJnZXRDZWxsLmdldExhbmRBdHRyKCksIHRoaXMudGFyZ2V0Q2VsbC5sYW5kVHlwZSlcblx0XHR9XG5cdH1cblx0Ly9AZW5kXG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cblx0cHJpdmF0ZSBvblVwZGF0ZVRvbmRlbkNvdW50KCkge1xuXHRcdHRoaXMudXBkYXRlVG9uZGVuQ291bnQoKVxuXHR9XG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cblx0cHJpdmF0ZSB1cGRhdGVUb25kZW5Db3VudCgpIHtcblx0XHRjb25zdCB0b25kZW5Db3VudCA9IE1hdGgubWF4KDAsIFRPREFZX1RPTkRFTl9NQVhfQ09VTlQgLSBnYW1lSHByLnBsYXllci5nZXRDZWxsVG9uZGVuQ291bnQoKSksXG5cdFx0XHRpc0NhblRvbmRlbiA9IHRvbmRlbkNvdW50ID4gMCwgdG9kZW5pbmdDb3VudCA9IGdhbWVIcHIud29ybGQuZ2V0QXJteVRvZGVuaW5nQ291bnQoKSxcblx0XHRcdHRvZGVuaW5nU3RyID0gKGlzQ2FuVG9uZGVuICYmIHRvZGVuaW5nQ291bnQgPiAwKSA/IGA8Y29sb3I9I2MzNEEzMj4oLSR7dG9kZW5pbmdDb3VudH0pPC9jPmAgOiAnJ1xuXHRcdHRoaXMuc3VycGx1c0NvdW50Tm9kZV8uc2V0TG9jYWxlS2V5KCd1aS50b25kZW5fc3VycGx1c19jb3VudCcsIGA8Y29sb3I9JHtpc0NhblRvbmRlbiA/ICcjM0YzMzJGJyA6ICcjRDc2MzREJ30+JHt0b25kZW5Db3VudH08L2M+JHt0b2RlbmluZ1N0cn1gKVxuXHRcdHJldHVybiBpc0NhblRvbmRlblxuXHR9XG5cblx0cHJpdmF0ZSBnZXRNYXJjaFRpbWUoZGF0YTogQXJteVNob3J0SW5mbywgY2Q6IG51bWJlciwgbXVsOiBudW1iZXIpIHtcblx0XHRsZXQgdGltZSA9IE1hdGguZmxvb3IoZGF0YS5kaXMgKiAodXQuVGltZS5Ib3VyIC8gZGF0YS5tYXJjaFNwZWVkKSlcblx0XHQvLyDmmK/lkKbmnInlh4/lsJHml7bpl7TnmoTmlL/nrZZcblx0XHR0aW1lID0gTWF0aC5mbG9vcih0aW1lICogKDEgLSBjZCAqIDAuMDEpKVxuXHRcdC8vIOiuoeeul+mcgOimgeeahOaXtumXtFxuXHRcdGNvbnN0IG1pblRpbWUgPSBkYXRhLmluZGV4ID09PSB0aGlzLnRhcmdldENlbGw/LmluZGV4ID8gMCA6IDEwMDBcblx0XHRkYXRhLm1hcmNoVGltZSA9IE1hdGguZmxvb3IobXVsID4gMCA/IE1hdGgubWF4KHRpbWUgLyBtdWwsIG1pblRpbWUpIDogdGltZSlcblx0XHRyZXR1cm4gZGF0YS5tYXJjaFRpbWVcblx0fVxuXG5cdC8vIOiOt+WPluagoeWcuuihjOWGm+WKoOmAn2NkXG5cdHByaXZhdGUgZ2V0QnVpbGRNYXJjaENEKCkge1xuXHRcdGNvbnN0IGJ1aWxkID0gZ2FtZUhwci5wbGF5ZXIuZ2V0TWFpbkJ1aWxkcygpLmZpbmQobSA9PiBtLmlkID09PSAyMDExKVxuXHRcdGlmICghYnVpbGQpIHtcblx0XHRcdHJldHVybiAwXG5cdFx0fVxuXHRcdGNvbnN0IGpzb24gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ2J1aWxkQXR0cicsIGJ1aWxkLmlkICogMTAwMCArIGJ1aWxkLmx2KVxuXHRcdHJldHVybiBqc29uID8gdXQuc3RyaW5nVG9OdW1iZXJzKGpzb24uZWZmZWN0cywgJywnKVsxXSB8fCAwIDogMFxuXHR9XG5cblx0Ly8g5piv5ZCm5Yqg6YCfXG5cdHByaXZhdGUgaXNDYW5VcFNwZWVkKGFpbmRleDogbnVtYmVyLCBiaW5kZXg6IG51bWJlcikge1xuXHRcdGNvbnN0IGFjZWxsID0gZ2FtZUhwci53b3JsZC5nZXRNYXBDZWxsQnlJbmRleChhaW5kZXgpXG5cdFx0Y29uc3QgYmNlbGwgPSBnYW1lSHByLndvcmxkLmdldE1hcENlbGxCeUluZGV4KGJpbmRleClcblx0XHRyZXR1cm4gYWNlbGw/LmlzQ2FuVXBTcGVlZE1hcmNoQ2l0eSgpICYmIGJjZWxsPy5pc0NhblVwU3BlZWRNYXJjaENpdHkoKVxuXHR9XG5cblx0Ly8g5Yi35paw5YiX6KGoXG5cdHByaXZhdGUgdXBkYXRlQXJteXModXBkYXRlU3RhdGU6IGJvb2xlYW4gPSB0cnVlLCB1cGRhdGVTZWxlY3Q6IGJvb2xlYW4gPSB0cnVlKSB7XG5cdFx0Ly8g6K6t57uD5YiX6KGoXG5cdFx0Y29uc3QgbHZpbmdQYXduTHZNYXAgPSB7fVxuXHRcdGdhbWVIcHIucGxheWVyLmdldFBhd25MZXZlbGluZ1F1ZXVlcygpLmZvckVhY2gobSA9PiBsdmluZ1Bhd25Mdk1hcFttLnB1aWRdID0gbS5sdilcblx0XHQvLyDmm7TmlrDnirbmgIFcblx0XHRpZiAodXBkYXRlU3RhdGUpIHtcblx0XHRcdHRoaXMuYXJteXMuZm9yRWFjaChtID0+IHtcblx0XHRcdFx0Y29uc3Qgc3RhdGVzOiBBcm15U3RhdGVbXSA9IFtdLCBkYXRhOiBBcm15U2hvcnRJbmZvID0gbS5kYXRhXG5cdFx0XHRcdGlmIChkYXRhLnN0YXRlICE9PSBBcm15U3RhdGUuTk9ORSkge1xuXHRcdFx0XHRcdHN0YXRlcy5wdXNoKGRhdGEuc3RhdGUpXG5cdFx0XHRcdH0gZWxzZSB7XG5cdFx0XHRcdFx0aWYgKGRhdGEuZHJpbGxQYXducy5sZW5ndGggPiAwKSB7XG5cdFx0XHRcdFx0XHRzdGF0ZXMucHVzaChBcm15U3RhdGUuRFJJTEwpXG5cdFx0XHRcdFx0fVxuXHRcdFx0XHRcdGlmIChkYXRhLmN1cmluZ1Bhd25zLmxlbmd0aCA+IDApIHtcblx0XHRcdFx0XHRcdHN0YXRlcy5wdXNoKEFybXlTdGF0ZS5DVVJJTkcpXG5cdFx0XHRcdFx0fVxuXHRcdFx0XHRcdGlmIChkYXRhLnBhd25zLnNvbWUocCA9PiBsdmluZ1Bhd25Mdk1hcFtwLnVpZF0pKSB7XG5cdFx0XHRcdFx0XHRzdGF0ZXMucHVzaChBcm15U3RhdGUuTFZJTkcpXG5cdFx0XHRcdFx0fVxuXHRcdFx0XHRcdGlmIChnYW1lSHByLndvcmxkLmdldEFybXlUb25kZW5JbmZvKGRhdGEuaW5kZXgsIGRhdGEudWlkKSkge1xuXHRcdFx0XHRcdFx0c3RhdGVzLnB1c2goQXJteVN0YXRlLlRPTkRFTilcblx0XHRcdFx0XHR9XG5cdFx0XHRcdH1cblx0XHRcdFx0bS5zdGF0ZXMgPSBzdGF0ZXNcblx0XHRcdH0pXG5cdFx0fVxuXHRcdC8vIOaOkuW6j1xuXHRcdHRoaXMuYXJteXMuc29ydCgoYSwgYikgPT4ge1xuXHRcdFx0aWYgKGEuc3RhdGVzLmxlbmd0aCA+IDAgfHwgYi5zdGF0ZXMubGVuZ3RoID4gMCkge1xuXHRcdFx0XHRyZXR1cm4gKGEuc3RhdGVzWzBdIHx8IDApIC0gKGIuc3RhdGVzWzBdIHx8IDApXG5cdFx0XHR9XG5cdFx0XHRyZXR1cm4gYS50aW1lIC0gYi50aW1lXG5cdFx0fSlcblx0XHQvLyDliLfmlrDpgInmi6kgIOaJgOacieaooeW8j+S4i+mDveS4jeWGjeiHquWKqOmAiVxuXHRcdGlmICh1cGRhdGVTZWxlY3QpIHtcblx0XHRcdGNvbnN0IGFybXkgPSB0aGlzLmFybXlzWzBdXG5cdFx0XHRpZiAoYXJteSAmJiAhYXJteS5zdGF0ZXM/Lmxlbmd0aCkge1xuXHRcdFx0XHR0aGlzLnNlbGVjdEFybXkgPSBhcm15LmRhdGFcblx0XHRcdH1cblx0XHR9XG5cdFx0Ly9cblx0XHR0aGlzLmxpc3RTdl8uc3RvcEF1dG9TY3JvbGwoKVxuXHRcdHRoaXMubGlzdFN2Xy5jb250ZW50LnkgPSAwXG5cdFx0dGhpcy5saXN0U3ZfLkl0ZW1zKHRoaXMuYXJteXMsIChpdCwgbSkgPT4ge1xuXHRcdFx0aXRbJ19kYXRhJ10gPSBtXG5cdFx0XHRjb25zdCBkYXRhOiBBcm15U2hvcnRJbmZvID0gaXQuRGF0YSA9IG0uZGF0YVxuXHRcdFx0aXQuQ2hpbGQoJ25hbWUnLCBjYy5MYWJlbCkuc3RyaW5nID0gZGF0YS5uYW1lXG5cdFx0XHR2aWV3SGVscGVyLnVwZGF0ZVBvc2l0aW9uVmlldyhpdC5DaGlsZCgncG9zX2JlJyksIGRhdGEuaW5kZXgpXG5cdFx0XHRpdC5DaGlsZCgndGFyZ2V0JywgY2MuTGFiZWwpLnNldExvY2FsZUtleSgndWkudGFyZ2V0X2Rpc3RhbmNlX2NvdW50JywgbS5kaXMpXG5cdFx0XHRjb25zdCBwYXduczogYW55W10gPSBkYXRhLnBhd25zXG5cdFx0XHRpdC5DaGlsZCgncGF3bnMnKS5JdGVtcyhwYXducy5jb25jYXQoZGF0YS5kcmlsbFBhd25zKS5jb25jYXQoZGF0YS5jdXJpbmdQYXducyksIChub2RlMiwgcGF3bikgPT4ge1xuXHRcdFx0XHRjb25zdCBpY29uID0gbm9kZTIuQ2hpbGQoJ2ljb24nKSwgaXNJZCA9IHR5cGVvZiAocGF3bikgPT09ICdudW1iZXInLCBpc0N1cmluZyA9ICEhcGF3bi5kZWFkVGltZVxuXHRcdFx0XHRjb25zdCBpc0x2aW5nID0gIWlzSWQgJiYgISFsdmluZ1Bhd25Mdk1hcFtwYXduLnVpZF0gJiYgIWlzQ3VyaW5nXG5cdFx0XHRcdGNvbnN0IGx2ID0gaXNMdmluZyA/IGx2aW5nUGF3bkx2TWFwW3Bhd24udWlkXSA6IHBhd24ubHZcblx0XHRcdFx0cmVzSGVscGVyLmxvYWRQYXduSGVhZE1pbmlJY29uKGlzSWQgPyBwYXduIDogKHBhd24ucG9ydHJheWFsPy5pZCB8fCBwYXduLmlkKSwgaWNvbiwgdGhpcy5rZXkpXG5cdFx0XHRcdGljb24ub3BhY2l0eSA9IChpc0lkIHx8IGlzTHZpbmcgfHwgaXNDdXJpbmcpID8gMTIwIDogMjU1XG5cdFx0XHRcdG5vZGUyLkNoaWxkKCdsdicsIGNjLkxhYmVsKS5Db2xvcihpc0x2aW5nID8gJyMyMURDMkQnIDogJyNGRkZGRkYnKS5zdHJpbmcgPSAoaXNJZCB8fCBsdiA8PSAxKSA/ICcnIDogJycgKyBsdlxuXHRcdFx0XHRpZiAobm9kZTIuQ2hpbGQoJ2hwJykuYWN0aXZlID0gKCFpc0lkICYmICFpc0N1cmluZykpIHtcblx0XHRcdFx0XHRub2RlMi5DaGlsZCgnaHAvYmFyJywgY2MuU3ByaXRlKS5maWxsUmFuZ2UgPSBwYXduLmhwWzBdIC8gcGF3bi5ocFsxXVxuXHRcdFx0XHR9XG5cdFx0XHR9KVxuXHRcdFx0Ly8g5Yi35paw54q25oCBXG5cdFx0XHR0aGlzLnVwZGF0ZUFybXlTdGF0ZShpdCwgbS5zdGF0ZXMpXG5cdFx0XHQvLyDooYzlhpvpgJ/luqZcblx0XHRcdHRoaXMudXBkYXRlQXJteU1hcmNoU3BlZWQoaXQsIG0pXG5cdFx0XHQvLyDpgInmi6lcblx0XHRcdHRoaXMudXBkYXRlU2VsZWN0SXRlbShpdClcblx0XHRcdC8vXG5cdFx0XHRjb25zdCBjYW5TZWxlY3QgPSAhbS5zdGF0ZXMubGVuZ3RoXG5cdFx0XHRpdC5vcGFjaXR5ID0gY2FuU2VsZWN0ID8gMjU1IDogMjAwXG5cdFx0XHRpdC5Db2xvcihjYW5TZWxlY3QgPyAnI0U5RERDNycgOiAnI0RDREJEMycpXG5cdFx0fSlcblx0fVxuXG5cdC8vIOWIt+aWsOmAieaLqVxuXHRwcml2YXRlIHVwZGF0ZVNlbGVjdEl0ZW0oaXQ6IGNjLk5vZGUpIHtcblx0XHRjb25zdCBzZWxlY3QgPSBpdC5DaGlsZCgnc2VsZWN0JykuYWN0aXZlID0gaXQuRGF0YT8udWlkID09PSB0aGlzLnNlbGVjdEFybXk/LnVpZFxuXHRcdC8vIOWPquaciemAieaLqeeahOaXtuWAmeaJjeWPr+eCueWHu+S9jee9rui3s+i9rFxuXHRcdGNvbnN0IGNhblNlbGVjdCA9IHNlbGVjdCB8fCAhIWl0WydfZGF0YSddPy5zdGF0ZXM/Lmxlbmd0aFxuXHRcdGl0LkNoaWxkKCdwb3NfYmUnKS5Db2xvcihjYW5TZWxlY3QgPyAnIzkzNkU1QScgOiAnI0I2QTU5MScpXG5cdFx0aXQuQ2hpbGQoJ3Bvc19iZScsIGNjLkJ1dHRvbikuaW50ZXJhY3RhYmxlID0gY2FuU2VsZWN0XG5cdFx0aXQuQ2hpbGQoJ3Bvc19iZS9saW5lJykuYWN0aXZlID0gY2FuU2VsZWN0XG5cdH1cblxuXHQvLyDooYzlhpvpgJ/luqZcblx0cHJpdmF0ZSB1cGRhdGVBcm15TWFyY2hTcGVlZChpdDogY2MuTm9kZSwgbTogYW55KSB7XG5cdFx0Y29uc3QgZGF0YTogQXJteVNob3J0SW5mbyA9IG0uZGF0YVxuXHRcdGlmIChpdC5DaGlsZCgndGltZScpLmFjdGl2ZSA9ICFtLnN0YXRlcz8ubGVuZ3RoKSB7XG5cdFx0XHRpdC5DaGlsZCgndGltZS92YWwnLCBjYy5MYWJlbCkuQ29sb3IoISFtLnVwU3BlZWRTdGF0ZSA/ICcjNkRCMTRDJyA6ICcjOTM2RTVBJykuc3RyaW5nID0gdXQubWlsbGlzZWNvbmRGb3JtYXQobS50aW1lLCAnaDptbTpzcycpXG5cdFx0XHRjb25zdCBpc011bCA9IGl0LkNoaWxkKCd0aW1lL211bCcpLmFjdGl2ZSA9IG0udXBTcGVlZFN0YXRlID4gMCAmJiBtLnRpbWUgPiAwXG5cdFx0XHRjb25zdCBpc1VwID0gaXQuQ2hpbGQoJ3RpbWUvdXAnKS5hY3RpdmUgPSAhaXNNdWwgJiYgISFtLmNkICYmIG0udGltZSA+IDBcblx0XHRcdGlmIChpc011bCkge1xuXHRcdFx0XHRpdC5DaGlsZCgndGltZS9tdWwnLCBjYy5MYWJlbCkuc2V0TG9jYWxlS2V5KCd1aS51cF9tYXJjaF9zcGVlZF8nICsgbS51cFNwZWVkU3RhdGUsIG0udXBTcGVlZE11bCwgbS5tYWluT3V0VXBEaXMpXG5cdFx0XHR9IGVsc2UgaWYgKGlzVXApIHtcblx0XHRcdFx0aXQuQ2hpbGQoJ3RpbWUvdXAnLCBjYy5MYWJlbCkuc3RyaW5nID0gJygtJyArIG0uY2QgKyAnJSknXG5cdFx0XHR9XG5cdFx0fVxuXHRcdGlmIChpdC5DaGlsZCgnbWFyY2hfc3BlZWRfYmUnKS5hY3RpdmUgPSBmYWxzZS8qICFnYW1lSHByLmlzTm92aWNlTW9kZSAmJiBkYXRhLnBhd25zLmxlbmd0aCA+IDAgJiYgZGF0YS5kZWZhdWx0TWFyY2hTcGVlZCA+IDAgKi8pIHtcblx0XHRcdGNvbnN0IG1hcmNoU3BlZWRMYmwgPSBpdC5DaGlsZCgnbWFyY2hfc3BlZWRfYmUnLCBjYy5MYWJlbCksIGxpbmUgPSBpdC5DaGlsZCgnbWFyY2hfc3BlZWRfYmUvbGluZScpXG5cdFx0XHRtYXJjaFNwZWVkTGJsLkNvbG9yKGRhdGEubWFyY2hTcGVlZCAhPT0gZGF0YS5kZWZhdWx0TWFyY2hTcGVlZCA/ICcjQjZBNTkxJyA6ICcjOTM2RTVBJykuc2V0TG9jYWxlS2V5KCd1aS5tYXJjaF9zcGVlZF9kZXNjJywgZGF0YS5tYXJjaFNwZWVkKVxuXHRcdFx0aWYgKGxpbmUuYWN0aXZlID0gaXQuQ2hpbGQoJ21hcmNoX3NwZWVkX2JlJywgY2MuQnV0dG9uKS5pbnRlcmFjdGFibGUgPSAhbS5zdGF0ZXM/Lmxlbmd0aCkge1xuXHRcdFx0XHRtYXJjaFNwZWVkTGJsLl9mb3JjZVVwZGF0ZVJlbmRlckRhdGEoKVxuXHRcdFx0XHRsaW5lLndpZHRoID0gbWFyY2hTcGVlZExibC5ub2RlLndpZHRoXG5cdFx0XHR9XG5cdFx0fVxuXHR9XG5cblx0Ly8g5Yi35paw54q25oCBXG5cdHByaXZhdGUgdXBkYXRlQXJteVN0YXRlKG5vZGU6IGNjLk5vZGUsIHN0YXRlczogQXJteVN0YXRlW10pIHtcblx0XHRub2RlLkNoaWxkKCdzdGF0ZScpLkl0ZW1zKHN0YXRlcywgKGl0LCBzdGF0ZSkgPT4ge1xuXHRcdFx0aXQuQ29sb3IoQVJNWV9TVEFURV9DT0xPUltzdGF0ZV0pLnNldExvY2FsZUtleSgndWkuYXJteV9zdGF0ZV8nICsgc3RhdGUpXG5cdFx0fSlcblx0fVxuXG5cdHByaXZhdGUgZG8oKSB7XG5cdFx0aWYgKCF0aGlzLnNlbGVjdEFybXkpIHtcblx0XHRcdHJldHVyblxuXHRcdH1cblx0XHR0aGlzLmNiICYmIHRoaXMuY2IodGhpcy5zZWxlY3RBcm15KVxuXHRcdHRoaXMuaGlkZSgpXG5cdH1cbn1cbiJdfQ==