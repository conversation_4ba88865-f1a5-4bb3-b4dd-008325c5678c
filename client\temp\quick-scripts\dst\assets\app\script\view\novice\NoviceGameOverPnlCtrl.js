
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/novice/NoviceGameOverPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c0a92Z8x+JGL6bzDHE+bwNm', 'NoviceGameOverPnlCtrl');
// app/script/view/novice/NoviceGameOverPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var NoviceGameOverPnlCtrl = /** @class */ (function (_super) {
    __extends(NoviceGameOverPnlCtrl, _super);
    function NoviceGameOverPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        _this.contentNode_ = null; // path://root_n/content_n
        //@end
        _this._callback = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    NoviceGameOverPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoviceGameOverPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    NoviceGameOverPnlCtrl.prototype.onEnter = function (callback) {
        var _a;
        this._callback = callback;
        var overInfo = { winName: GameHelper_1.gameHpr.user.getNickname(), closeTime: 0 }, record = { rank: 0, addWarToken: 10, curRankScore: 0, addRankScore: 0 };
        var isWin = 1, closeTime = this.rootNode_.Child('close_time');
        this.titleLbl_.setLocaleKey('ui.game_over_win_' + isWin);
        // this.rootNode_.Child('title', cc.MultiFrame).setFrame(isWin)
        closeTime.active = !!overInfo;
        // 对局结算
        if (overInfo) {
            // this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + isWin, overInfo.winName)
            // closeTime.setLocaleKey('ui.server_close_desc', gameHpr.millisecondToStringForDay(overInfo.closeTime))
            closeTime.active = false;
            this.rootNode_.Child('desc').active = false;
        }
        else {
            var hasAlli = GameHelper_1.gameHpr.world.getAlliBaseInfo(GameHelper_1.gameHpr.alliance.getUid());
            this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + (hasAlli ? 3 : 2));
        }
        this.rootNode_.Child('rank/val').setLocaleKey('ui.game_over_rank', ((_a = record === null || record === void 0 ? void 0 : record.rank) !== null && _a !== void 0 ? _a : -1) + 1);
        var isRankServer = false;
        var rankNode = this.contentNode_.Child('rank');
        if (rankNode.active = isRankServer && !!record) {
            var _b = GameHelper_1.gameHpr.resolutionRankScore(record.curRankScore || 0, 1), id = _b.id, winPoint = _b.winPoint, maxPoint = _b.maxPoint;
            var addRankScore = record.addRankScore || 0;
            ResHelper_1.resHelper.loadRankScoreIcon(id, rankNode.Child('icon'), this.key);
            rankNode.Child('lay/val').setLocaleKey('ui.rank_name_' + (id >= 0 ? id : 'none'));
            rankNode.Child('lay/point').setLocaleKey('ui.rank_score_num_3', Math.max(0, winPoint - addRankScore));
            rankNode.Child('lay/add', cc.Label).Color(addRankScore >= 0 ? '#ECFF47' : '#F55756').string = (addRankScore >= 0 ? '+' : '-') + addRankScore;
            rankNode.Child('progress/bar', cc.Sprite).fillRange = maxPoint === -1 ? 1 : winPoint / maxPoint;
            rankNode.Child('progress/text', cc.Label).string = maxPoint === -1 ? (winPoint + '') : (winPoint + '/' + maxPoint);
        }
        var award = this.contentNode_.Child('award');
        if (award.active = !!(record === null || record === void 0 ? void 0 : record.addWarToken)) {
            award.setLocaleKey('ui.game_over_award_desc2', record.addWarToken);
        }
    };
    NoviceGameOverPnlCtrl.prototype.onRemove = function () {
        cc.log('NoviceGameOverPnlCtrl onRemove');
    };
    NoviceGameOverPnlCtrl.prototype.onClean = function () {
        cc.log('NoviceGameOverPnlCtrl onClean');
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://back_lobby_be
    NoviceGameOverPnlCtrl.prototype.onClickBackLobby = function (event, data) {
        this._callback && this._callback();
    };
    NoviceGameOverPnlCtrl = __decorate([
        ccclass
    ], NoviceGameOverPnlCtrl);
    return NoviceGameOverPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoviceGameOverPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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