<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>270</integer>
    <key>angleVariance</key>
    <integer>15</integer>
    <key>blendFuncDestination</key>
    <integer>771</integer>
    <key>blendFuncSource</key>
    <integer>770</integer>
    <key>duration</key>
    <integer>-1</integer>
    <key>emitterType</key>
    <integer>0</integer>
    <key>finishColorAlpha</key>
    <real>0.12</real>
    <key>finishColorBlue</key>
    <integer>1</integer>
    <key>finishColorGreen</key>
    <integer>1</integer>
    <key>finishColorRed</key>
    <integer>1</integer>
    <key>finishColorVarianceAlpha</key>
    <integer>0</integer>
    <key>finishColorVarianceBlue</key>
    <integer>0</integer>
    <key>finishColorVarianceGreen</key>
    <integer>0</integer>
    <key>finishColorVarianceRed</key>
    <integer>0</integer>
    <key>finishParticleSize</key>
    <integer>20</integer>
    <key>finishParticleSizeVariance</key>
    <integer>0</integer>
    <key>gravityx</key>
    <integer>10</integer>
    <key>gravityy</key>
    <integer>0</integer>
    <key>maxParticles</key>
    <integer>25</integer>
    <key>maxRadius</key>
    <integer>0</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <integer>10</integer>
    <key>particleLifespanVariance</key>
    <integer>2</integer>
    <key>radialAccelVariance</key>
    <integer>0</integer>
    <key>radialAcceleration</key>
    <integer>0</integer>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <integer>0</integer>
    <key>rotationEndVariance</key>
    <integer>20</integer>
    <key>rotationStart</key>
    <integer>0</integer>
    <key>rotationStartVariance</key>
    <integer>50</integer>
    <key>sourcePositionVariancex</key>
    <integer>400</integer>
    <key>sourcePositionVariancey</key>
    <integer>400</integer>
    <key>sourcePositionx</key>
    <real>373.7277526855469</real>
    <key>sourcePositiony</key>
    <real>478.40472412109375</real>
    <key>speed</key>
    <integer>50</integer>
    <key>speedVariance</key>
    <integer>10</integer>
    <key>startColorAlpha</key>
    <integer>1</integer>
    <key>startColorBlue</key>
    <integer>1</integer>
    <key>startColorGreen</key>
    <integer>1</integer>
    <key>startColorRed</key>
    <integer>1</integer>
    <key>startColorVarianceAlpha</key>
    <integer>0</integer>
    <key>startColorVarianceBlue</key>
    <integer>0</integer>
    <key>startColorVarianceGreen</key>
    <integer>0</integer>
    <key>startColorVarianceRed</key>
    <integer>0</integer>
    <key>startParticleSize</key>
    <integer>0</integer>
    <key>startParticleSizeVariance</key>
    <integer>0</integer>
    <key>tangentialAccelVariance</key>
    <integer>0</integer>
    <key>tangentialAcceleration</key>
    <integer>0</integer>
    <key>positionType</key>
    <integer>0</integer>
    <key>rotationIsDir</key>
    <false/>
    <key>minRadiusVariance</key>
    <integer>0</integer>
    <key>emissionRate</key>
    <integer>1</integer>
    <key>spriteFrameUuid</key>
    <string>6a95935f-82c9-4ed0-b354-eb5da344ac72</string>
  </dict>
</plist>