
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PlayerModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '770fe0z1u5PoKxie/Onbh/c', 'PlayerModel');
// app/script/model/main/PlayerModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var DBHelper_1 = require("../../common/helper/DBHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var MerchantObj_1 = require("../bazaar/MerchantObj");
var BTInfoObj_1 = require("./BTInfoObj");
var CTypeObj_1 = require("../common/CTypeObj");
var EquipInfo_1 = require("./EquipInfo");
var ForgeEquipInfo_1 = require("./ForgeEquipInfo");
var OutputObj_1 = require("./OutputObj");
var PawnDrillInfoObj_1 = require("./PawnDrillInfoObj");
var PawnLevelingInfoObj_1 = require("./PawnLevelingInfoObj");
var PolicyObj_1 = require("./PolicyObj");
var SmeltEquipInfo_1 = require("./SmeltEquipInfo");
var TaskObj_1 = require("../common/TaskObj");
var GuideConfig_1 = require("../guide/GuideConfig");
var HeroSlotObj_1 = require("./HeroSlotObj");
var PawnCureInfoObj_1 = require("./PawnCureInfoObj");
var EquipSlotObj_1 = require("./EquipSlotObj");
var PawnSlotObj_1 = require("./PawnSlotObj");
/**
 * 玩家
 */
var PlayerModel = /** @class */ (function (_super) {
    __extends(PlayerModel, _super);
    function PlayerModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.user = null;
        _this.alliance = null;
        _this.initTime = 0; //初始化时间
        _this.cereal = null; //粮
        _this.timber = null; //木
        _this.stone = null; //石
        _this.expBook = 0; //经验书
        _this.iron = 0; //铁
        _this.upScroll = 0; //卷轴
        _this.fixator = 0; //固定器
        _this.cerealConsume = 0; //当前粮耗
        _this.granaryCap = 0; //粮仓容量
        _this.warehouseCap = 0; //仓库容量
        _this.stamina = 0; //当前奖励点
        _this.captureInfo = null; //被攻陷的信息
        _this.sumOnlineTime = 0; //累计在线时间
        _this.mainCityIndex = 0; //主城所在位置
        _this.mainBuilds = []; //主城当前的建筑列表
        _this.unlockPawnIds = []; //当前额外解锁的兵种列表
        _this.unlockEquipIds = []; //当前额外解锁的装备列表
        _this.btQueues = []; //当前的建造队列
        _this.pawnDrillQueueMap = new Map(); //士兵训练队列
        _this.pawnLevelingQueues = []; //士兵练级队列
        _this.baseArmys = []; //临时的军队列表
        _this.armyDistMap = {};
        _this.merchants = []; //商人列表
        _this.guideTasks = []; //新手任务列表
        _this.todayTasks = []; //每日任务列表
        _this.otherTasks = []; //其他任务列表
        _this.equips = []; //已有的装备列表
        _this.currForgeEquip = null; //当前打造装备信息
        _this.currSmeltEquip = null; //当前正在融炼得装备信息
        _this.configPawnMap = {}; //配置士兵的信息
        _this.citySkinConfigMap = {}; //城市皮肤配置的信息
        _this.fortAutoSupports = []; //要塞自动支援配置
        _this.addOutputSurplusTime = {}; //添加产量剩余时间
        _this.getAddOutputTime = 0;
        _this.policySlots = {}; //当前政策槽位列表
        _this.equipSlots = {}; //当前装备槽位列表
        _this.pawnSlots = {}; //当前士兵槽位列表
        _this.heroSlots = []; //当前英雄槽位信息
        _this.hidePChatChannels = {}; //隐藏的私聊频道
        _this.exitAllianceCount = 0; //退出联盟次数
        _this.todayOccupyCellCount = 0; //每日打地数量
        _this.accTotalGiveResCount = 0; //累计赠送资源数量
        _this.todayReplacementCount = 0; //每日置换次数
        _this.landScore = 0; //领地积分
        _this.occupyLandCountMap = {}; //历史攻占野地数量 key=地块等级 val=数量
        _this.maxOccupyLandDifficulty = 0; //历史最大攻占野地难度
        _this.killRecordMap = {}; //击杀数量 key=id val=数量
        _this.mapMarks = {}; //地图标记
        _this.reCreateMainCityCount = 0; //重新创建主城次数
        _this.cellTondenCount = 0; //每日屯田次数
        _this.upRecruitPawnCount = 0; //加速招募士兵数量
        _this.freeRecruitPawnCount = 0; //免费招募士兵数量
        _this.freeLevingPawnCount = 0; //免费训练士兵数量
        _this.freeCurePawnCount = 0; //免费治疗士兵数量
        _this.freeForgeCount = 0; //免费打造/重铸数量
        _this.armyMaxCount = 0; //军队最大数量
        _this.mainCityRect = { min: cc.v2(), max: cc.v2() };
        _this.injuryPawns = []; // 可治疗的伤兵
        _this.curingPawns = []; // 治疗中的伤兵
        _this.lastReqSelectArmysTime = 0; //最后一次请求军队时间
        _this.tempSelectArmyErr = '';
        _this.tempSelectArmyList = []; //临时的选择军队信息
        _this.tempSelectArmyCanGotoCount = 0; //临时的选择军队 可前往数量
        _this.lastReqArmysTime = 0; //最后一次请求军队时间
        _this.tempArmyList = []; //临时的军队信息
        _this.lastReqArmyMarchRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyMarchRecordList = []; //军队记录列表
        _this.lastReqArmyBattleRecordTime = 0; //最后一次请求军队记录时间
        _this.tempArmyBattleRecordList = []; //军队记录列表
        _this.lastReqBazaarRecordTime = 0; //最后一次请求市场记录时间
        _this.tempBazaarRecordList = []; //市场记录列表
        _this.tempBattleForecastRets = []; //临时记录玩家战斗预测结果
        _this.tempStopPlayMessageSound = {}; //临时需要停止播放的音效
        _this.tempCanForgeEquips = null;
        _this.tempCanRecruitPawns = {};
        _this.deadPawnLvMap = {}; //各等级士兵战败次数 击杀数量 key=等级 val=次数
        _this.lastReqDeadPawnLvMapTime = 0; //最后一次请求各等级士兵战败次数的时间
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    PlayerModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
        this.alliance = this.getModel('alliance');
    };
    // 初始化信息
    PlayerModel.prototype.init = function (data, isLocal) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cc.log('init player', data);
                        this.resetTempInfo();
                        this.lastReqSelectArmysTime = 0;
                        this.lastReqArmysTime = 0;
                        this.lastReqArmyMarchRecordTime = 0;
                        this.lastReqArmyBattleRecordTime = 0;
                        this.lastReqBazaarRecordTime = 0;
                        this.lastReqDeadPawnLvMapTime = 0;
                        this.initTime = Date.now();
                        this.sumOnlineTime = data.sumOnlineTime || 0;
                        this.setCaptureInfo(data.captureInfo);
                        this.expBook = data.expBook || 0;
                        this.iron = data.iron || 0;
                        this.upScroll = data.upScroll || 0;
                        this.fixator = data.fixator || 0;
                        this.granaryCap = data.granaryCap || 1;
                        this.warehouseCap = data.warehouseCap || 1;
                        this.cereal = new OutputObj_1.default(EventType_1.default.UPDATE_CEREAL).fromSvr(data.cereal);
                        this.timber = new OutputObj_1.default(EventType_1.default.UPDATE_TIMBER).fromSvr(data.timber);
                        this.stone = new OutputObj_1.default(EventType_1.default.UPDATE_STONE).fromSvr(data.stone);
                        this.cerealConsume = data.cerealConsume || 0;
                        this.stamina = data.stamina || 0;
                        this.mainCityIndex = data.mainCityIndex || 0;
                        this.mainBuilds = data.builds || [];
                        this.unlockPawnIds = data.unlockPawnIds || [];
                        this.unlockEquipIds = data.unlockEquipIds || [];
                        this.updateBtQueue(data.btQueues || [], false);
                        this.updatePawnDrillQueue(data.pawnDrillQueues || {}, false);
                        this.updatePawnLevelingQueue(data.pawnLevelingQueues || [], false);
                        this.updatePawnCuringQueue(data.curingQueues || [], false);
                        this.updateArmyDists(data.armyDists || [], false);
                        this.merchants = (data.merchants || []).map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
                        this.equips = (data.equips || []).map(function (m) { return new EquipInfo_1.default().fromSvr(m); });
                        this.configPawnMap = data.configPawnMap || {};
                        this.citySkinConfigMap = data.citySkinConfigMap || {};
                        this.fortAutoSupports = data.fortAutoSupports || [];
                        this.updateAddOutputTime(data.addOutputSurplusTime);
                        this.updatePolicySlots(data.policySlots || {}, false);
                        this.heroSlots = (data.heroSlots || []).map(function (m) { return new HeroSlotObj_1.default().fromSvr(m); }); // 英雄槽位移到士兵槽位前，需要判断是否重复
                        this.updatePawnSlots(data.pawnSlots || {}, false);
                        this.updateEquipSlots(data.equipSlots || {}, false);
                        this.updateCurrForgeEquip(data.currForgeEquip);
                        this.updateCurrSmeltEquip(data.currSmeltEquip);
                        this.hidePChatChannels = data.hidePChatChannels || {};
                        this.exitAllianceCount = data.exitAllianceCount || 0;
                        this.updateArmyMaxCount(); //刷新军队最大数量
                        this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                        this.accTotalGiveResCount = data.accTotalGiveResCount || 0;
                        this.todayReplacementCount = data.todayReplacementCount || 0;
                        this.landScore = data.landScore || 0;
                        this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
                        this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                        this.killRecordMap = data.killRecordMap || {};
                        this.mapMarks = data.mapMarks || {};
                        this.reCreateMainCityCount = data.reCreateMainCityCount || 0;
                        this.cellTondenCount = data.cellTondenCount || 0;
                        this.injuryPawns = data.injuryPawns || [];
                        this.upRecruitPawnCount = data.upRecruitPawnCount || 0;
                        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0;
                        this.freeLevingPawnCount = data.freeLevingPawnCount || 0;
                        this.freeCurePawnCount = data.freeCurePawnCount || 0;
                        this.freeForgeCount = data.freeForgeCount || 0;
                        this.updateGuideTasks(data.guideTasks || [], false);
                        this.updateTodayTasks(data.todayTasks || [], false); //需要放到这个位置 因为需要前面的todayOccupyCellCount
                        this.updateOtherTasks(data.otherTasks || [], false);
                        // 主城的矩形区域
                        this.updateMainCityRect(this.mainCityIndex);
                        if (!!isLocal) return [3 /*break*/, 2];
                        // 获取联盟信息
                        return [4 /*yield*/, this.alliance.init(data.allianceUid || '')];
                    case 1:
                        // 获取联盟信息
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        // 宝箱
                        ReddotHelper_1.reddotHelper.set('treasure_main', !!data.hasNewTreasure);
                        if (this.guideTasks.length > 0) {
                            ReddotHelper_1.reddotHelper.unregister('guide_task');
                            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
                        }
                        // 监听消息
                        this.net.on('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
                        return [2 /*return*/];
                }
            });
        });
    };
    PlayerModel.prototype.clean = function () {
        this.net.off('game/OnUpdatePlayerInfo', this.OnUpdatePlayerInfo, this);
        this.tempBattleForecastRets = [];
        this.tempArmyList = [];
        this.mainBuilds = [];
        this.guideTasks = [];
        this.todayTasks = [];
        this.otherTasks = [];
        this.heroSlots = [];
        this.baseArmys = [];
        this.pawnDrillQueueMap.clear();
        this.pawnLevelingQueues = [];
        this.tempSelectArmyList = [];
        this.tempArmyMarchRecordList = [];
        this.tempArmyBattleRecordList = [];
        this.tempBazaarRecordList = [];
        this.resetTempInfo();
    };
    PlayerModel.prototype.resetTempInfo = function () {
        this.tempCanForgeEquips = null;
        this.tempCanRecruitPawns = {};
    };
    PlayerModel.prototype.getInitTIme = function () { return this.initTime; };
    PlayerModel.prototype.getToInitElapsedTime = function () { return Date.now() - this.initTime; }; //到初始化经过的时间
    PlayerModel.prototype.getCaptureInfo = function () { return this.captureInfo; };
    PlayerModel.prototype.isCapture = function () { var _a; return !!this.captureInfo || !((_a = this.mainBuilds) === null || _a === void 0 ? void 0 : _a.length); }; //是否沦陷
    PlayerModel.prototype.getMainCityIndex = function () { return this.mainCityIndex; };
    PlayerModel.prototype.getMainCityPoint = function () { return MapHelper_1.mapHelper.indexToPoint(this.mainCityIndex); };
    PlayerModel.prototype.getAlliance = function () { return this.alliance; };
    PlayerModel.prototype.getAllianceUid = function () { return this.alliance.getUid(); };
    PlayerModel.prototype.isHasAlliance = function () { return !!this.alliance.getUid(); };
    PlayerModel.prototype.getCereal = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getCerealOp = function () { var _a; return ((_a = this.cereal) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.cereal.set(val, isEmit);
    };
    PlayerModel.prototype.changeCereal = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.cereal.change(val, isEmit);
    };
    PlayerModel.prototype.getTimber = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getTimberOp = function () { var _a; return ((_a = this.timber) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.timber.set(val, isEmit);
    };
    PlayerModel.prototype.changeTimber = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.timber.change(val, isEmit);
    };
    PlayerModel.prototype.getStone = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.value) || 0; };
    PlayerModel.prototype.getStoneOp = function () { var _a; return ((_a = this.stone) === null || _a === void 0 ? void 0 : _a.opHour) || 0; };
    PlayerModel.prototype.setStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.stone.set(val, isEmit);
    };
    PlayerModel.prototype.changeStone = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        return this.stone.change(val, isEmit);
    };
    PlayerModel.prototype.getCerealConsume = function () { return this.cerealConsume; };
    PlayerModel.prototype.getCerealCapRatio = function () { return this.getCereal() / this.granaryCap; };
    PlayerModel.prototype.getTimberCapRatio = function () { return this.getTimber() / this.warehouseCap; };
    PlayerModel.prototype.getStoneCapRatio = function () { return this.getStone() / this.warehouseCap; };
    PlayerModel.prototype.getGranaryCap = function () { return this.granaryCap; };
    PlayerModel.prototype.getWarehouseCap = function () { return this.warehouseCap; };
    PlayerModel.prototype.getAddOutputSurplusTime = function () { return this.addOutputSurplusTime; };
    PlayerModel.prototype.getAddOutputElapsedTime = function () { return Date.now() - this.getAddOutputTime; };
    PlayerModel.prototype.getExitAllianceCount = function () { return this.exitAllianceCount; };
    PlayerModel.prototype.setExitAllianceCount = function (val) { this.exitAllianceCount = val; };
    PlayerModel.prototype.getHeroSlots = function () { return this.heroSlots; };
    PlayerModel.prototype.getHidePChatChannels = function () { return this.hidePChatChannels; };
    PlayerModel.prototype.getUnlockPawnIds = function () { return this.unlockPawnIds; };
    PlayerModel.prototype.getUnlockEquipIds = function () { return this.unlockEquipIds; };
    PlayerModel.prototype.getTodayOccupyCellCount = function () { return this.todayOccupyCellCount; };
    PlayerModel.prototype.addTodayOccupyCellCount = function (val) { return this.todayOccupyCellCount += val; };
    PlayerModel.prototype.getAccTotalGiveResCount = function () { return this.accTotalGiveResCount; };
    PlayerModel.prototype.addAccTotalGiveResCount = function (val) { return this.accTotalGiveResCount += val; };
    PlayerModel.prototype.getTodayReplacementCount = function () { return this.todayReplacementCount; };
    PlayerModel.prototype.setTodayReplacementCount = function (val) { this.todayReplacementCount = val; };
    PlayerModel.prototype.getUpRecruitPawnCount = function () { return this.upRecruitPawnCount; };
    PlayerModel.prototype.setUpRecruitPawnCount = function (val) { this.upRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeRecruitPawnCount = function () { return this.freeRecruitPawnCount; };
    PlayerModel.prototype.setFreeRecruitPawnCount = function (val) { this.freeRecruitPawnCount = val; };
    PlayerModel.prototype.getFreeLevingPawnCount = function () { return this.freeLevingPawnCount; };
    PlayerModel.prototype.setFreeLevingPawnCount = function (val) { this.freeLevingPawnCount = val; };
    PlayerModel.prototype.getFreeCurePawnCount = function () { return this.freeCurePawnCount; };
    PlayerModel.prototype.setFreeCurePawnCount = function (val) { this.freeCurePawnCount = val; };
    PlayerModel.prototype.getFreeForgeCount = function () { return this.freeForgeCount; };
    PlayerModel.prototype.setFreeForgeCount = function (val) { this.freeForgeCount = val; };
    PlayerModel.prototype.getMaxOccupyLandDifficulty = function () { return this.maxOccupyLandDifficulty; };
    PlayerModel.prototype.getLandScore = function () { return this.landScore; };
    PlayerModel.prototype.getReCreateMainCityCount = function () { return this.reCreateMainCityCount; };
    PlayerModel.prototype.getCellTondenCount = function () { return this.cellTondenCount; };
    PlayerModel.prototype.getSumOnlineTime = function () {
        return this.sumOnlineTime + (Date.now() - this.initTime);
    };
    // 获取免费招募剩余次数
    PlayerModel.prototype.getFreeRecruitPawnSurplusCount = function () {
        var freeCount = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_DRILL_COUNT);
        return Math.max(0, freeCount - this.freeRecruitPawnCount);
    };
    // 获取免费训练士兵剩余次数
    PlayerModel.prototype.getFreeLevingPawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_LEVING_COUNT) - this.freeLevingPawnCount);
    };
    // 获取免费治疗士兵剩余次数
    PlayerModel.prototype.getFreeCurePawnSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_FREE_COUNT) - this.freeCurePawnCount);
    };
    // 获取免费免费打造/重铸剩余次数
    PlayerModel.prototype.getfreeForgeSurplusCount = function () {
        return Math.max(0, GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.FREE_RECAST_COUNT) - this.freeForgeCount);
    };
    // 兼容刷新一下固定菜单
    PlayerModel.prototype.updateFixationMenuData = function () {
        var _this = this;
        var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA) || [];
        if (!this.captureInfo) {
            var len = ids.length;
            ids.delete(function (id) { return !_this.mainBuilds.has('id', id); });
            if (ids.length !== len) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, ids);
            }
        }
        else if (ids.length > 0) {
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
        }
    };
    PlayerModel.prototype.updateMainCityRect = function (index) {
        var pos = MapHelper_1.mapHelper.indexToPoint(index).mul(Constant_1.TILE_SIZE);
        this.mainCityRect.min.x = pos.x;
        this.mainCityRect.min.y = pos.y;
        this.mainCityRect.max.x = pos.x + Constant_1.TILE_SIZE * 2;
        this.mainCityRect.max.y = pos.y + Constant_1.TILE_SIZE * 2;
    };
    // 主城是否不在屏幕范围内
    PlayerModel.prototype.checkMainNotInScreenRange = function () {
        if (this.isCapture() && !GameHelper_1.gameHpr.isSpectate()) {
            return false;
        }
        var outMin = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.min, this._temp_vec2_1);
        var outMax = CameraCtrl_1.cameraCtrl.getWorldToScreenPoint(this.mainCityRect.max, this._temp_vec2_2);
        return outMax.x <= 0 || outMax.y <= 0 || outMin.x >= cc.winSize.width || outMin.y >= cc.winSize.height;
    };
    // 经验书
    PlayerModel.prototype.getExpBook = function () { return this.expBook; };
    PlayerModel.prototype.setExpBook = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.expBook);
        this.expBook = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_EXP_BOOK, add);
        }
    };
    // 铁
    PlayerModel.prototype.getIron = function () { return this.iron; };
    PlayerModel.prototype.setIron = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.iron);
        this.iron = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_IRON, add);
        }
    };
    // 卷轴
    PlayerModel.prototype.getUpScroll = function () { return this.upScroll; };
    PlayerModel.prototype.setUpScroll = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.upScroll);
        this.upScroll = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_UPSCROLL, add);
        }
    };
    // 固定器
    PlayerModel.prototype.getFixator = function () { return this.fixator; };
    PlayerModel.prototype.setFixator = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.fixator);
        this.fixator = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_FIXATOR, add);
        }
    };
    // 奖励点
    PlayerModel.prototype.getStamina = function () { return this.stamina; };
    PlayerModel.prototype.setStamina = function (val, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (val === undefined || isNaN(val)) {
            return;
        }
        var add = Math.floor(val - this.stamina);
        this.stamina = Math.floor(val);
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_STAMINA, add);
        }
    };
    PlayerModel.prototype.setUnlockEquipIds = function (ids) {
        this.unlockEquipIds = ids || [];
        this.tempCanForgeEquips = null; //重新获取
    };
    PlayerModel.prototype.setUnlockPawnIds = function (ids) {
        this.unlockPawnIds = ids || [];
        this.tempCanRecruitPawns = {}; //重新获取
    };
    // 获取修建队列
    PlayerModel.prototype.getBtQueueCount = function () {
        return Constant_1.DEFAULT_BT_QUEUE_COUNT + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.BT_QUEUE);
    };
    // 设置被沦陷了
    PlayerModel.prototype.setCaptureInfo = function (data) {
        if (!(data === null || data === void 0 ? void 0 : data.uid)) {
            this.captureInfo = null;
        }
        else {
            this.captureInfo = data;
            var ids = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA);
            if (ids && ids.length > 0) {
                this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, []);
            }
            // 固定器效果重置
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LOCK_EQUIP_EFFECT_CONF, {});
        }
    };
    // 主城建筑列表信息
    PlayerModel.prototype.getMainBuilds = function () { return this.mainBuilds; };
    PlayerModel.prototype.updateMainBuildInfo = function (data) {
        GameHelper_1.gameHpr.cleanUnlockBuildCondText(); //清理一下 因为建筑有改变
        var build = this.mainBuilds.find(function (m) { return m.uid === data.uid; });
        if (build) {
            build.lv = data.lv;
        }
        else {
            this.mainBuilds.push(data);
        }
        if (data.id === Enums_1.BUILD_NID.MAIN) {
            this.updateArmyMaxCount();
        }
        this.emit(EventType_1.default.MAIN_BUILD_CHANGE_LV, data);
    };
    // 获取建筑等级
    PlayerModel.prototype.getBuildLv = function (id) {
        var _a;
        return ((_a = this.mainBuilds.find(function (m) { return m.id === id; })) === null || _a === void 0 ? void 0 : _a.lv) || 0;
    };
    PlayerModel.prototype.getMainBuildLv = function () {
        return this.getBuildLv(Enums_1.BUILD_NID.MAIN);
    };
    PlayerModel.prototype.updateArmyMaxCount = function () {
        var _a;
        var lv = this.getMainBuildLv();
        var effect = GameHelper_1.gameHpr.stringToCEffects((_a = assetsMgr.getJsonData('buildAttr', Enums_1.BUILD_NID.MAIN * 1000 + lv)) === null || _a === void 0 ? void 0 : _a.effects)[0];
        this.armyMaxCount = this.isCapture() ? 0 : (effect === null || effect === void 0 ? void 0 : effect.value) || 4;
    };
    // 当前的军队最大数量
    PlayerModel.prototype.getArmyMaxCount = function () {
        return this.armyMaxCount + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.ARMY_COUNT);
    };
    // 军队数量是否满了
    PlayerModel.prototype.isArmyCountFull = function () {
        return this.baseArmys.length >= this.getArmyMaxCount();
    };
    // 军队分布信息
    PlayerModel.prototype.getBaseArmys = function () { return this.baseArmys; };
    PlayerModel.prototype.getArmyDistMap = function () { return this.armyDistMap; };
    PlayerModel.prototype.getDistArmysByIndex = function (index) { return this.armyDistMap[index] || []; };
    PlayerModel.prototype.updateArmyDists = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.armyDistMap = {};
        this.baseArmys.length = 0;
        datas.forEach(function (m) {
            var armys = [];
            m.armys.forEach(function (army) {
                army.index = m.index;
                if (army.state !== Enums_1.ArmyState.MARCH) {
                    armys.push(army);
                }
                _this.baseArmys.push(army);
                _this.updateTempArmyIndex(army);
            });
            if (armys.length > 0) {
                _this.armyDistMap[m.index] = armys;
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_ARMY_DIST_INFO);
    };
    // 是否可以刷新产出
    PlayerModel.prototype.isCanUpdateOutput = function () {
        return this.user.getSid() > 0 || GameHelper_1.gameHpr.isNoviceMode;
    };
    // 刷新产出信息
    PlayerModel.prototype.updateOutput = function (data) {
        var _a, _b, _c;
        data = data || {};
        this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        this.cereal.updateInfo(data.cereal);
        this.timber.updateInfo(data.timber);
        this.stone.updateInfo(data.stone);
        this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((data.granaryCap || data.warehouseCap) && (!data.cereal && !data.timber && !data.stone)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新产出信息（位标记更新）
    PlayerModel.prototype.updateOutputByFlags = function (data) {
        var _a, _b, _c;
        if (!this.isCanUpdateOutput()) {
            return;
        }
        else if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateOutput(data);
        }
        else if (data.flag === 0) {
            return;
        }
        var granaryCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.GranaryCap);
        var warehouseCapFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarehouseCap);
        var cerealFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Cereal);
        var timberFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Timber);
        var stoneFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stone);
        var cerealConsumeFlag = ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CerealConsume);
        //
        if (granaryCapFlag)
            this.granaryCap = (_a = data.granaryCap) !== null && _a !== void 0 ? _a : this.granaryCap;
        if (warehouseCapFlag)
            this.warehouseCap = (_b = data.warehouseCap) !== null && _b !== void 0 ? _b : this.warehouseCap;
        if (cerealFlag)
            this.cereal.updateInfo(data.cereal);
        if (timberFlag)
            this.timber.updateInfo(data.timber);
        if (stoneFlag)
            this.stone.updateInfo(data.stone);
        if (cerealConsumeFlag)
            this.cerealConsume = (_c = data.cerealConsume) !== null && _c !== void 0 ? _c : this.cerealConsume;
        // 如果只更新了容量
        if ((granaryCapFlag || warehouseCapFlag) && (!cerealFlag && !timberFlag && !stoneFlag)) {
            this.emit(EventType_1.default.UPDATE_RES_CAP);
        }
    };
    // 刷新奖励信息
    PlayerModel.prototype.updateRewardItems = function (data) {
        if (data) {
            this.user.setGold(data.gold);
            this.user.setIngot(data.ingot);
            this.user.setWarToken(data.warToken);
            this.user.setTitles(data.titles);
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                this.setExpBook(data.expBook);
                this.setIron(data.iron);
                this.setUpScroll(data.upScroll);
                this.setFixator(data.fixator);
                this.setStamina(data.stamina);
                this.setUpRecruitPawnCount(data.upRecruitPawnCount);
            }
        }
    };
    // 刷新奖励信息（位标记更新）
    PlayerModel.prototype.updateRewardItemsByFlags = function (data) {
        if ((data === null || data === void 0 ? void 0 : data.flag) === undefined) {
            return this.updateRewardItems(data);
        }
        else if (data.flag) {
            this.updateOutputByFlags(data);
            if (this.isCanUpdateOutput()) {
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ExpBook))
                    this.setExpBook(data.expBook);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Iron))
                    this.setIron(data.iron);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpScroll))
                    this.setUpScroll(data.upScroll);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Fixator))
                    this.setFixator(data.fixator);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Equip))
                    this.setUnlockEquipIds(data.unlockEquipIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Pawn))
                    this.setUnlockPawnIds(data.unlockPawnIds);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Stamina))
                    this.setStamina(data.stamina);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.UpRecruit))
                    this.setUpRecruitPawnCount(data.upRecruitPawnCount);
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeRecruit))
                    this.setFreeRecruitPawnCount(data.freeRecruitPawnCount); //免费招募
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeLeving))
                    this.setFreeLevingPawnCount(data.freeLevingPawnCount); //免费训练
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeCure))
                    this.setFreeCurePawnCount(data.freeCurePawnCount); //免费治疗
                if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.FreeForge))
                    this.setFreeForgeCount(data.freeForgeCount); //免费打造
            }
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Gold))
                this.user.setGold(data.gold);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Ingot))
                this.user.setIngot(data.ingot);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.WarToken))
                this.user.setWarToken(data.warToken);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Title))
                this.user.setTitles(data.titles);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.PawnSkin))
                this.user.setUnlockPawnSkinIds(data.pawnSkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Portrayal))
                this.user.setPortrayals(data.portrayals);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.SkinItemEnum))
                this.user.setSkinItemList(data.skinItems);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.CitySkin))
                this.user.setUnlockCitySkinIds(data.citySkins);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.HeadIcon))
                this.user.setUnlockHeadIcons(data.unlockHeadIcons);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.ChatEmoji))
                this.user.setUnlockChatEmojiIds(data.unlockChatEmojiIds);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.RankCoin))
                this.user.setRankCoin(data.rankCoin);
            if (ProtoHelper_1.protoHelper.checkFlag(data.flag, proto.OutPutFlagEnum.Botany))
                this.user.setUnlockBotanys(data.unlockBotanys);
        }
    };
    // 兵营训练队列
    PlayerModel.prototype.getPawnDrillQueues = function (uid) { return this.pawnDrillQueueMap.get(uid) || []; };
    PlayerModel.prototype.updatePawnDrillQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.pawnDrillQueueMap.clear();
        for (var uid in datas) {
            this.pawnDrillQueueMap.set(uid, datas[uid].list.map(function (m) {
                var data = new PawnDrillInfoObj_1.default().fromSvr(m);
                // 添加训练完成消息
                if (data.surplusTime > 0) {
                    var type = Math.floor(data.id / 100);
                    GameHelper_1.gameHpr.addMessage({
                        key: 'ui.message_101',
                        params: ['pawnText.name_' + data.id, type === 35 ? 'ui.button_produce' : 'ui.button_drill'],
                        tag: data.uid,
                        delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/),
                    });
                }
                return data;
            }));
        }
        // 检测
        this.pawnDrillQueueMap.forEach(function (arr, key) {
            if (arr.length === 1 && !arr[0].surplusTime) {
                _this.pawnDrillQueueMap.delete(key);
            }
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_DRILL_QUEUE, this.mainCityIndex);
    };
    PlayerModel.prototype.getAllPawnDrillList = function () {
        var arr = [];
        this.pawnDrillQueueMap.forEach(function (m) { return arr.pushArr(m); });
        return arr;
    };
    // 获取某个军队训练士兵剩余时间
    PlayerModel.prototype.getSumDrillTimeByArmy = function (uid) {
        var maxTime = 0;
        this.pawnDrillQueueMap.forEach(function (arr) {
            var time = 0;
            arr.forEach(function (m) {
                if (m.surplusTime > 0) {
                    time += m.getSurplusTime();
                }
                else {
                    time += m.needTime;
                }
                if (m.auid === uid && time > maxTime) {
                    maxTime = time;
                }
            });
        });
        return { time: maxTime };
    };
    // 获取某个军队治疗士兵剩余时间
    PlayerModel.prototype.getSumCuringTimeByArmy = function (uid) {
        var maxTime = 0, time = 0;
        this.curingPawns.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += m.getSurplusTime();
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime };
    };
    // 获取受伤的士兵
    PlayerModel.prototype.getInjuryPawns = function () {
        return this.injuryPawns;
    };
    PlayerModel.prototype.updateInjuryPawns = function (datas) {
        this.injuryPawns = datas;
    };
    PlayerModel.prototype.addInjuryPawn = function (data) {
        this.injuryPawns.push(data);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    PlayerModel.prototype.removeInjuryPawn = function (uid) {
        this.injuryPawns.remove('uid', uid);
        this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
    };
    // 获取治疗中的士兵
    PlayerModel.prototype.getCuringPawnsQueue = function () {
        return this.curingPawns;
    };
    PlayerModel.prototype.updatePawnCuringQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.curingPawns.length = 0;
        for (var i = 0; i < datas.length; i++) {
            var data = new PawnCureInfoObj_1.default().fromSvr(datas[i]);
            // 添加治疗完成消息
            if (data.surplusTime > 0) {
                // const type = Math.floor(data.id / 100)
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv]), 'ui.button_cure'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/),
                });
            }
            this.curingPawns.push(data);
        }
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_CURING_QUEUE, this.mainCityIndex);
    };
    // 获取士兵练级队列
    PlayerModel.prototype.getPawnLevelingQueues = function () { return this.pawnLevelingQueues; };
    PlayerModel.prototype.updatePawnLevelingQueue = function (datas, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnLevelingQueues = datas.map(function (m) {
            var data = new PawnLevelingInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_104',
                    params: ['pawnText.name_' + data.id, data.lv],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/),
                });
            }
            return data;
        });
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_LVING_QUEUE, this.mainCityIndex);
    };
    // 是否在队列中
    PlayerModel.prototype.isInPawnLvingQueue = function (uid) { return this.pawnLevelingQueues.has('puid', uid); };
    // 获取某个军队士兵练级剩余时间
    PlayerModel.prototype.getSumLvingTimeByArmy = function (uid) {
        var maxTime = 0, isBattleing = GameHelper_1.gameHpr.isBattleingByIndex(this.mainCityIndex), pause = true;
        var time = 0;
        this.pawnLevelingQueues.forEach(function (m) {
            if (m.surplusTime > 0) {
                time += isBattleing ? m.surplusTime : m.getSurplusTime();
                pause = isBattleing;
            }
            else {
                time += m.needTime;
            }
            if (m.auid === uid && time > maxTime) {
                maxTime = time;
            }
        });
        return { time: maxTime, pause: pause };
    };
    PlayerModel.prototype.getConfigPawnMap = function () { return this.configPawnMap; };
    // 获取配置士兵的装备信息
    PlayerModel.prototype.getConfigPawnInfo = function (pawnId) {
        if (!pawnId) {
            return null;
        }
        var info = this.configPawnMap[pawnId], uid = info === null || info === void 0 ? void 0 : info.equipUid;
        var conf = {
            equip: { uid: '', attrs: [] },
            skinId: (info === null || info === void 0 ? void 0 : info.skinId) || 0,
            attackSpeed: (info === null || info === void 0 ? void 0 : info.attackSpeed) || 0,
        };
        // 兼容装备
        if (uid) {
            var equip = this.getEquipByUid(uid);
            if (equip && (!equip.isExclusive() || equip.checkExclusivePawn(pawnId))) {
                conf.equip = { uid: equip.uid, attrs: equip.attrs };
            }
            else {
                conf.equip = { uid: '', attrs: [] };
            }
            if (info) {
                info.equipUid = conf.equip.uid;
            }
        }
        return conf;
    };
    PlayerModel.prototype.changeConfigPawnInfo = function (id, equipUid, skinId, attackSpeed) {
        this.configPawnMap[id] = { equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed };
    };
    PlayerModel.prototype.changeConfigPawnSkinId = function (id, skinId) {
        var _a;
        var conf = this.configPawnMap[id];
        if (conf) {
            conf.skinId = skinId;
        }
        else {
            this.configPawnMap[id] = { equipUid: '', skinId: skinId, attackSpeed: 0 };
        }
        // 刷一下槽位的
        var pawn = (_a = this.getPawnSlotById(id)) === null || _a === void 0 ? void 0 : _a.pawn;
        if (pawn) {
            pawn.skinId = skinId;
        }
        // 重新获取士兵
        this.tempCanRecruitPawns = {};
    };
    // 改变皮肤配置
    PlayerModel.prototype.changeConfigPawnInfoByData = function (data) {
        var _a, _b, _c;
        var conf = this.configPawnMap[data.id];
        if (conf) {
            conf.skinId = data.skinId;
            conf.equipUid = ((_a = data.equip) === null || _a === void 0 ? void 0 : _a.uid) || '';
        }
        else {
            this.configPawnMap[data.id] = { equipUid: ((_b = data.equip) === null || _b === void 0 ? void 0 : _b.uid) || '', skinId: data.skinId, attackSpeed: data.attackSpeed };
        }
        var buildId = (_c = data.baseJson) === null || _c === void 0 ? void 0 : _c.spawn_build_id;
        if (buildId) {
            this.tempCanRecruitPawns[buildId] = null;
        }
    };
    // 获取城市皮肤配置
    PlayerModel.prototype.getCitySkinConfigMap = function () { return this.citySkinConfigMap; };
    PlayerModel.prototype.getStudySlots = function (slots) {
        var list = [];
        for (var k in slots) {
            var slot = slots[k];
            if (slot.isYetStudy()) {
                list.push(slot);
            }
        }
        return list;
    };
    // ---------------------------------------------------------政策----------------------------------------------------------------
    // 当前政策槽位
    PlayerModel.prototype.getPolicySlots = function () { return this.policySlots; };
    // 获取已经研究的政策ids
    PlayerModel.prototype.getStudyPolicySlots = function () { return this.getStudySlots(this.policySlots); };
    // 刷新
    PlayerModel.prototype.updatePolicySlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.policySlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PolicyObj_1.default);
        ReddotHelper_1.reddotHelper.set('can_study_policy', GameHelper_1.gameHpr.checkStudySlotsReddot(this.policySlots));
        isEmit && this.emit(EventType_1.default.UPDATE_POLICY_SLOTS);
    };
    // ---------------------------------------------------------士兵----------------------------------------------------------------
    // 当前士兵槽位
    PlayerModel.prototype.getPawnSlots = function () { return this.pawnSlots; };
    PlayerModel.prototype.getStudyPawnSlots = function () { return this.getStudySlots(this.pawnSlots); };
    PlayerModel.prototype.getPawnSlotByLv = function (lv) { return this.pawnSlots[lv]; };
    PlayerModel.prototype.updatePawnSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.pawnSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, PawnSlotObj_1.default);
        this.checkPawnSlotInfo();
        ReddotHelper_1.reddotHelper.set('can_study_pawn', GameHelper_1.gameHpr.checkStudySlotsReddot(this.pawnSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_PAWN_SLOTS);
    };
    PlayerModel.prototype.getPawnSlotById = function (id) {
        for (var k in this.pawnSlots) {
            var slot = this.pawnSlots[k];
            if (slot.id === id) {
                return slot;
            }
        }
        return null;
    };
    // 检测槽位
    PlayerModel.prototype.checkPawnSlotInfo = function () {
        var _this = this;
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var _a;
            var slot = _this.pawnSlots[lv];
            if (slot) {
                slot.initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(slot.id));
            }
            else if (lv > 1000) {
                var heroSlotLv_1 = lv % 1000, hero = (_a = _this.heroSlots.find(function (m) { return m.lv === heroSlotLv_1; })) === null || _a === void 0 ? void 0 : _a.hero;
                _this.pawnSlots[lv] = new PawnSlotObj_1.default().fromSvr({ lv: lv, id: hero ? -1 : 0 }).init();
            }
        });
        this.tempCanRecruitPawns = {};
    };
    // 获取可以招募的士兵
    PlayerModel.prototype.getCanRecruitPawns = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        var hasMap = {};
        // 加入固定的槽位信息
        Constant_1.PAWN_SLOT_CONF.forEach(function (lv) {
            var slot = _this.pawnSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
            }
            else {
                slot = new PawnSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            slots.push(slot);
        });
        // // 加入英雄殿固定的几个槽位信息
        // this.heroSlots.forEach(m => {
        //     const id = m.hero?.avatarPawn
        //     if (!id) {
        //         slots.push(new PawnSlotObj().fromSvr({ lv: 1000 + m.lv }).init())
        //     } else if (!hasMap[id]) {
        //         hasMap[id] = true
        //         slots.push(new PawnSlotObj().fromSvr({ lv: 1000 + m.lv, id }).init().initPawn(this.mainCityIndex, this.getConfigPawnInfo(id)))
        //     }
        // })
        // 加入直接解锁的士兵
        this.unlockPawnIds.forEach(function (id) {
            var _a;
            if (!hasMap[id] && ((_a = assetsMgr.getJsonData('pawnBase', id)) === null || _a === void 0 ? void 0 : _a.spawn_build_id) === buildId) {
                hasMap[id] = true;
                slots.push(new PawnSlotObj_1.default().fromSvr({ id: id, lv: 1 }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(id)));
            }
        });
        return slots;
    };
    // 获取可生产的器械
    PlayerModel.prototype.getCanProduceMachines = function (buildId) {
        var _this = this;
        var slots = this.tempCanRecruitPawns[buildId];
        if (slots) {
            return slots;
        }
        slots = this.tempCanRecruitPawns[buildId] = [];
        assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.spawn_build_id === buildId && !m.need_unlock; }).forEach(function (m) {
            slots.push(new PawnSlotObj_1.default().fromSvr({ id: m.id, lv: m.need_build_lv }).init().initPawn(_this.mainCityIndex, _this.getConfigPawnInfo(m.id)));
        });
        return slots;
    };
    // 获取所有士兵的ids
    PlayerModel.prototype.getAllCanRecruitPawnIds = function () {
        var _a;
        var ids = [], hasMap = {};
        // 槽位
        for (var k in this.pawnSlots) {
            var id = (_a = this.pawnSlots[k].pawn) === null || _a === void 0 ? void 0 : _a.id;
            if (id && !hasMap[id]) {
                hasMap[id] = true;
                ids.push(id);
            }
        }
        // 英雄
        this.heroSlots.forEach(function (m) {
            var _a;
            var id = (_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn;
            if (id && !hasMap[id]) {
                hasMap[id] = true;
                ids.push(id);
            }
        });
        // 直接解锁的士兵
        this.unlockPawnIds.forEach(function (id) {
            if (!hasMap[id]) {
                hasMap[id] = true;
                ids.push(id);
            }
        });
        return ids;
    };
    // ---------------------------------------------------------装备----------------------------------------------------------------
    // 当前装备槽位
    PlayerModel.prototype.getEquipSlots = function () { return this.equipSlots; };
    PlayerModel.prototype.getStudyEquipSlots = function () { return this.getStudySlots(this.equipSlots); };
    PlayerModel.prototype.getEquipSlotByLv = function (lv) { return this.equipSlots[lv]; };
    PlayerModel.prototype.updateEquipSlots = function (slots, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.equipSlots = GameHelper_1.gameHpr.fromSvrByStudyData(slots, EquipSlotObj_1.default);
        this.checkEquipSlotInfo();
        var equipSlots = {};
        for (var key in this.equipSlots) { // 这里只检测普通装备，专属检测分开了
            if (this.equipSlots[key].getUIStudyType() === Enums_1.StudyType.EQUIP) {
                equipSlots[key] = this.equipSlots[key];
            }
        }
        ReddotHelper_1.reddotHelper.set('can_study_equip', GameHelper_1.gameHpr.checkStudySlotsReddot(equipSlots));
        isEmit && this.emit(EventType_1.default.UPDATE_EQUIP_SLOTS);
    };
    // 将打造的装备更新到槽位里面
    PlayerModel.prototype.checkEquipSlotInfo = function () {
        var _this = this;
        var equipMap = {};
        this.equips.forEach(function (m) { return equipMap[m.uid] = m; });
        // 刷新已经打造的信息
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (!slot) {
                _this.equipSlots[lv] = new EquipSlotObj_1.default().fromSvr({ lv: lv }).init();
            }
            else if (slot === null || slot === void 0 ? void 0 : slot.isYetStudy()) {
                slot.equip = equipMap[slot.uid];
            }
        });
        this.tempCanForgeEquips = null; //重新获取
    };
    // 获取可以打造的装备
    PlayerModel.prototype.getCanForgeEquips = function () {
        var _this = this;
        if (this.tempCanForgeEquips && this.tempCanForgeEquips.length > 0) {
            return this.tempCanForgeEquips;
        }
        var hasMap = {};
        this.tempCanForgeEquips = [];
        Constant_1.EQUIP_SLOT_CONF.forEach(function (lv) {
            var slot = _this.equipSlots[lv];
            if (slot) {
                hasMap[slot.id] = true;
                _this.tempCanForgeEquips.push(slot);
            }
        });
        this.equips.forEach(function (m) {
            if (!hasMap[m.id]) {
                hasMap[m.id] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m.id }).init().setEquip(m));
            }
        });
        // 加入直接解锁的
        this.unlockEquipIds.forEach(function (m) {
            if (!hasMap[m]) {
                hasMap[m] = true;
                _this.tempCanForgeEquips.push(new EquipSlotObj_1.default().fromSvr({ id: m, lv: 1 }).init());
            }
        });
        return this.tempCanForgeEquips;
    };
    // 打造装备
    PlayerModel.prototype.forgeEquip = function (uid, lockEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, equip;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqForgeEquip({ uid: uid, lockEffect: lockEffect })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            equip = this.getEquipByUid(uid);
                            if (equip) {
                                equip.nextForgeFree = !!data.nextForgeFree;
                            }
                            this.updateCurrForgeEquip(data.currForgeEquip);
                            this.updateRewardItemsByFlags(data.cost);
                            this.setFreeForgeCount(data.freeForgeCount || 0);
                            this.emit(EventType_1.default.FORGE_EQUIP_BEGIN);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 立即完成打造
    PlayerModel.prototype.inDoneForge = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_FORGE_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.HD_InDoneForge({})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取当前打造装备信息
    PlayerModel.prototype.getCurrForgeEquip = function () { return this.currForgeEquip; };
    PlayerModel.prototype.updateCurrForgeEquip = function (data) {
        this.currForgeEquip = data ? new ForgeEquipInfo_1.default().fromSvr(data) : null;
        if (this.currForgeEquip) {
            this.currForgeEquip.isYetForge = !!this.equips.find(function (m) { return m.id === data.id; });
        }
    };
    // 刷新装备
    PlayerModel.prototype.updateEquip = function (data) {
        var equip = this.equips.find(function (m) { return m.uid === data.uid; });
        if (equip) {
            equip.updateInfo(data);
        }
        else {
            this.equips.push(new EquipInfo_1.default().fromSvr(data));
            this.checkEquipSlotInfo();
        }
        return !equip; //是否新装备
    };
    PlayerModel.prototype.updateEquipInfo = function (data) {
        var isNew = this.updateEquip(data);
        this.updatePawnEquipAttr(data.uid, data.attrs);
        this.tempCanRecruitPawns = {}; //重新获取可训练的士兵 因为士兵有可能有装备
        return isNew;
    };
    PlayerModel.prototype.getEquips = function () { return this.equips; };
    PlayerModel.prototype.getEquipById = function (id) { return id ? this.equips.find(function (m) { return m.id === id; }) : null; };
    PlayerModel.prototype.getEquipByUid = function (uid) { return uid ? this.equips.find(function (m) { return m.uid === uid; }) : null; };
    // 获取士兵可以携带的装备列表
    PlayerModel.prototype.getPawnEquips = function (pawnId) {
        return this.equips.filter(function (m) { return !m.exclusive_pawn || m.exclusive_pawn === pawnId; });
    };
    // 获取已经参与融炼的装备idMap
    PlayerModel.prototype.getYetSmeltEquipIdMap = function (uid) {
        var smeltEquipIdMap = {};
        this.equips.forEach(function (m) {
            if (m.uid !== uid) {
                m.smeltEffects.forEach(function (s) { return smeltEquipIdMap[s.id] = m.id; });
            }
        });
        return smeltEquipIdMap;
    };
    // 添加打造消息通知
    PlayerModel.prototype.addForgeMessage = function (id, isNew) {
        GameHelper_1.gameHpr.addMessage({
            key: isNew ? 'ui.message_102' : 'ui.message_103',
            params: ['equipText.name_' + id],
            tag: id + '',
        });
    };
    // 获取正在融炼的装备
    PlayerModel.prototype.getCurrSmeltEquip = function () { return this.currSmeltEquip; };
    // 刷新融炼装备
    PlayerModel.prototype.updateCurrSmeltEquip = function (data) {
        this.currSmeltEquip = data ? new SmeltEquipInfo_1.default().fromSvr(data) : null;
    };
    // 融炼装备
    PlayerModel.prototype.smeltEquip = function (mainUid, viceIds) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_SmeltingEquip', { mainUid: mainUid, viceIds: viceIds }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateCurrSmeltEquip(data.currSmeltEquip);
                            this.setFixator(data.fixator);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 还原融炼
    PlayerModel.prototype.restoreSmeltEquip = function (mainUid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_RestoreSmeltEquip', { mainUid: mainUid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnEquipAttr = function (uid, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateEquipAttr(uid, attrs); });
            }
        });
    };
    // 还原装备属性
    PlayerModel.prototype.restoreEquipAttr = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqRestoreForge({ uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.setIron(data.iron);
                            this.updateEquipInfo(data.equip);
                            this.emit(EventType_1.default.UPDATE_EQUIP_ATTR, data.equip.uid);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加打造消息通知
    PlayerModel.prototype.addSmeltMessage = function (id) {
        GameHelper_1.gameHpr.addMessage({
            key: 'ui.message_109',
            tag: id + '',
        });
    };
    // 获取要塞自动资源配置
    PlayerModel.prototype.getFortAutoSupports = function () { return this.fortAutoSupports; };
    // 是否自动支援
    PlayerModel.prototype.isForAutoSupport = function (index) {
        var _a;
        return !!((_a = this.fortAutoSupports.find(function (m) { return m.index === index; })) === null || _a === void 0 ? void 0 : _a.isAuto);
    };
    // 刷新自动支援配置
    PlayerModel.prototype.updateForAutoSupport = function (index, isAuto) {
        var data = this.fortAutoSupports.find(function (m) { return m.index === index; });
        if (data) {
            data.isAuto = isAuto;
        }
        else {
            this.fortAutoSupports.push({ index: index, isAuto: isAuto });
        }
    };
    // 建筑升级效果通知
    PlayerModel.prototype.addBuildEffectMessage = function (data) {
        if (!Enums_1.BUILD_NID[data.id]) {
            return;
        }
        // 查找定义的效果文本
        var key = 'ui.msg_build_effect_' + Enums_1.BUILD_NID[data.id].toLowerCase();
        var localeText = assetsMgr.lang(key);
        if (localeText === key) {
            return;
        }
        var diff = DBHelper_1.default.buildEffectDelta(data.id, data.lv - 1, data.lv);
        var sign = '-';
        var unit = '%';
        if ([Enums_1.BUILD_NID.MAIN, Enums_1.BUILD_NID.WAREHOUSE, Enums_1.BUILD_NID.GRANARY, Enums_1.BUILD_NID.FREE_BAZAAR, Enums_1.BUILD_NID.ALLI_BAZAAR].includes(data.id)) {
            sign = '+';
            unit = '';
        }
        var delay = 0.5;
        if (diff > 0) {
            GameHelper_1.gameHpr.addMessage({
                key: key,
                params: [" <color=" + Constant_1.COLOR_NORMAL.DONE + ">" + sign + diff + unit + "</color>"],
                tag: data.uid + '_desc',
                delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/) + delay,
            });
            delay += 0.5;
        }
        // 兵营特有的二级解锁刀盾兵提示
        if (data.id === Enums_1.BUILD_NID.BARRACKS && data.lv === 2) {
            // 解锁刀盾兵提示
            GameHelper_1.gameHpr.addMessage({
                key: 'ui.msg_build_effect_barracks_2',
                params: ["<color=" + Constant_1.COLOR_NORMAL.DONE + "> " + assetsMgr.lang('pawnText.name_3201') + "</color>"],
                tag: data.uid + '_desc_2',
                delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/) + delay,
            });
        }
    };
    // 修建队列
    PlayerModel.prototype.getBtQueues = function () { return this.btQueues; };
    PlayerModel.prototype.updateBtQueue = function (datas, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.btQueues = datas.map(function (m) {
            var data = new BTInfoObj_1.default().fromSvr(m);
            // 添加训练完成消息
            if (data.surplusTime > 0) {
                GameHelper_1.gameHpr.addMessage({
                    key: 'ui.message_101',
                    params: ['buildText.name_' + data.id, data.lv > 1 ? 'ui.button_up' : 'ui.button_build'],
                    tag: data.uid,
                    delay: Math.max(0, data.getSurplusTime() * 0.001 /*-1*/),
                });
                _this.addBuildEffectMessage(data);
            }
            return data;
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_BT_QUEUE);
            // 如果当前是在做引导 并且没有修建兵营的队列了
            if (GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.CHOOSE_BTING_BUTTON) && !this.btQueues.has('id', Enums_1.BUILD_NID.BARRACKS)) {
                GameHelper_1.gameHpr.guide.gotoNextStep(GuideConfig_1.GuideTagType.CHECK_CAN_XL_PAWN, true);
            }
        }
    };
    PlayerModel.prototype.removeLocalBTQueues = function (uid) {
        this.btQueues.remove('uid', uid);
    };
    // 取消修建
    PlayerModel.prototype.cancelBtToServer = function (index, uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqCancelBT({ index: index, uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.updateBtQueue(data.queues);
                        this.updateOutputByFlags(data.output);
                        GameHelper_1.gameHpr.delMessageByTag(uid);
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc');
                        GameHelper_1.gameHpr.delMessageByTag(uid + '_desc_2');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 立即完成
    PlayerModel.prototype.inDoneBt = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.IN_DONE_BT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqInDoneBt()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            audioMgr.playSFX('common/sound_ui_023');
                            this.btQueues.forEach(function (m) {
                                GameHelper_1.gameHpr.delMessageByTag(m.uid);
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc');
                                GameHelper_1.gameHpr.message.delayEndByTag(m.uid + '_desc_2');
                            });
                            this.updateBtQueue(data.queues);
                            this.user.setGold(data.gold);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 是否有建筑在队列中
    PlayerModel.prototype.hasBuildInBtQueue = function (uid) {
        var it = this.btQueues.find(function (m) { return m.uid === uid; });
        return !!(it === null || it === void 0 ? void 0 : it.getSurplusTime());
    };
    PlayerModel.prototype.getBuildBtInfo = function (uid) {
        return this.btQueues.find(function (m) { return m.uid === uid; });
    };
    // 获取军队列表
    PlayerModel.prototype.getAllArmys = function (interval, wait) {
        if (interval === void 0) { interval = 0.5; }
        if (wait === void 0) { wait = true; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, []]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqArmysTime > 0 && Date.now() - this.lastReqArmysTime <= interval * 1000) {
                            return [2 /*return*/, this.tempArmyList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetPlayerArmys(wait)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmysTime = Date.now();
                        this.tempArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        // 统计宝箱
                        this.tempArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, this.tempArmyList];
                }
            });
        });
    };
    PlayerModel.prototype.getTempArmyList = function () { return this.tempArmyList; };
    // 获取选择军队列表
    PlayerModel.prototype.getSelectArmys = function (index, type, interval) {
        if (interval === void 0) { interval = 1; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isCapture()) {
                            return [2 /*return*/, { list: [], canGotoCount: 0 }]; //如果被沦陷了 直接返回
                        }
                        else if (interval > 0 && this.lastReqSelectArmysTime > 0 && Date.now() - this.lastReqSelectArmysTime <= interval * 1000) {
                            return [2 /*return*/, { err: this.tempSelectArmyErr, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetSelectArmys({ index: index, type: type })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqSelectArmysTime = Date.now();
                        this.tempSelectArmyErr = err;
                        this.tempSelectArmyList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempSelectArmyCanGotoCount = (data === null || data === void 0 ? void 0 : data.canGotoCount) || 0;
                        // 统计宝箱
                        this.tempSelectArmyList.forEach(function (army) {
                            army.treasures = [];
                            army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
                        });
                        return [2 /*return*/, { err: err, list: this.tempSelectArmyList, canGotoCount: this.tempSelectArmyCanGotoCount }];
                }
            });
        });
    };
    PlayerModel.prototype.getTempSelectArmyList = function () { return this.tempSelectArmyList; };
    // 刷新临时军队宝箱信息
    PlayerModel.prototype.updateTempArmyTreasureInfo = function (treasures, auid, puid) {
        this.updateTempArmyTreasureInfoOne(this.tempArmyList, treasures, auid, puid);
        this.updateTempArmyTreasureInfoOne(this.tempSelectArmyList, treasures, auid, puid);
        this.emit(EventType_1.default.UPDATE_ARMY_TREASURE, auid);
    };
    PlayerModel.prototype.updateTempArmyTreasureInfoOne = function (armys, treasures, auid, puid) {
        var army = armys.find(function (m) { return m.uid === auid; });
        if (army) {
            var pawn = army.pawns.find(function (m) { return m.uid === puid; });
            if (pawn) {
                pawn.treasures.length = 0;
                pawn.treasures.pushArr(treasures);
                army.treasures.length = 0;
                army.pawns.forEach(function (pawn) { return pawn.treasures.forEach(function (m) { return army.treasures.push(GameHelper_1.gameHpr.fromSvrTreasureInfo(m, army.index, army.uid, pawn.uid)); }); });
            }
        }
    };
    // 刷新临时军队所在位置
    PlayerModel.prototype.updateTempArmyIndex = function (data) {
        this.updateTempArmyIndexOne(this.tempArmyList, data);
        this.updateTempArmyIndexOne(this.tempSelectArmyList, data);
        this.emit(EventType_1.default.UPDATE_ARMY_AREA_INDEX, data.uid, data.index);
    };
    PlayerModel.prototype.updateTempArmyIndexOne = function (armys, data) {
        var _a;
        var army = armys.find(function (m) { return m.uid === (data === null || data === void 0 ? void 0 : data.uid); });
        if (army) {
            army.index = (_a = data === null || data === void 0 ? void 0 : data.index) !== null && _a !== void 0 ? _a : army.index;
        }
    };
    // 获取商人列表
    PlayerModel.prototype.getMerchants = function () { return this.merchants; };
    // 刷新商人列表
    PlayerModel.prototype.updateMerchants = function (datas) {
        this.merchants = datas.map(function (m) { return new MerchantObj_1.default().fromSvr(m); });
        this.emit(EventType_1.default.UPDATE_MERCHANTS);
    };
    // 获取一个商人的运输量
    PlayerModel.prototype.getMerchantTransitCap = function () { return 1000 + (GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TRANSIT_CD) > 0 ? 1000 : 0); };
    // 获取军队记录
    PlayerModel.prototype.getArmyMarchRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.isNoviceMode) {
                            return [2 /*return*/, []];
                        }
                        else if (this.lastReqArmyMarchRecordTime > 0 && Date.now() - this.lastReqArmyMarchRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyMarchRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetArmyRecords', { isBattle: false })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyMarchRecordTime = Date.now();
                        if (err) {
                            this.tempArmyMarchRecordList = [];
                        }
                        else {
                            this.tempArmyMarchRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyMarchRecordList];
                }
            });
        });
    };
    PlayerModel.prototype.getArmyBattleRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqArmyBattleRecordTime > 0 && Date.now() - this.lastReqArmyBattleRecordTime <= 5000) {
                            return [2 /*return*/, this.tempArmyBattleRecordList];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetBattleRecordsList()];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqArmyBattleRecordTime = Date.now();
                        if (err) {
                            this.tempArmyBattleRecordList = [];
                        }
                        else {
                            this.tempArmyBattleRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        }
                        return [2 /*return*/, this.tempArmyBattleRecordList];
                }
            });
        });
    };
    // 获取市场记录
    PlayerModel.prototype.getBazaarRecords = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqBazaarRecordTime > 0 && Date.now() - this.lastReqBazaarRecordTime <= 5000) {
                            return [2 /*return*/, this.tempBazaarRecordList];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetBazaarRecords', {})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        uid = GameHelper_1.gameHpr.getUid();
                        this.lastReqBazaarRecordTime = Date.now();
                        this.tempBazaarRecordList = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.tempBazaarRecordList.forEach(function (m) {
                            var _a, _b, _c;
                            m.nickname = (_a = m.names) === null || _a === void 0 ? void 0 : _a[0];
                            if (m.type === 6) {
                                m.type = m.type * 10 + (m.owner === uid ? 1 : 0);
                                m.nickname = m.owner === uid ? (_b = m.names) === null || _b === void 0 ? void 0 : _b[1] : (_c = m.names) === null || _c === void 0 ? void 0 : _c[0];
                            }
                            var res = m.res || {};
                            m.res0 = res.resType !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.resCount) : null;
                            m.res1 = res.costType !== undefined ? new CTypeObj_1.default().init(res.costType, 0, res.costCount) : null;
                            m.actRes = res.actCount !== undefined ? new CTypeObj_1.default().init(res.resType, 0, res.actCount) : null;
                        });
                        return [2 /*return*/, this.tempBazaarRecordList];
                }
            });
        });
    };
    // 获取新手任务列表
    PlayerModel.prototype.getGuideTasks = function () {
        return this.guideTasks;
    };
    PlayerModel.prototype.updateGuideTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.guideTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'guideTask');
            task && _this.guideTasks.push(task);
        });
        if (isEmit) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
        if (this.guideTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('guide_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('guide_task');
            ReddotHelper_1.reddotHelper.register('guide_task', this.checkGuideTaskState, this, 1);
        }
    };
    // 更新新手任务进度
    PlayerModel.prototype.updateGuideTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.guideTasks.find(function (m) { return m.id === info.id; });
                if (!data) {
                    var task = new TaskObj_1.default().init(info, 'guideTask');
                    task && _this.guideTasks.push(task);
                }
                else {
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
            });
            this.updateGuideTaskState(false);
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetGuideTask = function () {
        var task = null;
        this.guideTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateGuideTaskState = function (isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.guideTasks.length === 0) {
            return;
        }
        this.guideTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetGuideTask();
        ReddotHelper_1.reddotHelper.set('guide_task', !!task);
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkGuideTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetGuideTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTaskReward = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqClaimTaskReward({ id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateGuideTasks(data.tasks);
                            this.updateGuideTaskState();
                            this.updateTodayTasks(data.todayTasks || []);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取每日任务列表
    PlayerModel.prototype.getTodayTasks = function () {
        return this.todayTasks;
    };
    PlayerModel.prototype.updateTodayTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.todayTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'todayTask');
            task && _this.todayTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.todayTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('today_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('today_task');
            ReddotHelper_1.reddotHelper.register('today_task', this.checkTodayTaskState, this, 1);
        }
    };
    // 更新每日任务进度
    PlayerModel.prototype.updateTodayTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (taskInfo) { var _a, _b; return (_b = (_a = _this.todayTasks.find(function (m) { return m.id === taskInfo.id; })) === null || _a === void 0 ? void 0 : _a.cond) === null || _b === void 0 ? void 0 : _b.updateProgress(taskInfo.progress); });
            this.updateTodayTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetTodayTask = function () {
        var task = null;
        this.todayTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateTodayTaskState = function () {
        if (this.todayTasks.length === 0) {
            if (this.guideTasks.length === 0) {
                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
            }
            return;
        }
        this.todayTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetTodayTask();
        ReddotHelper_1.reddotHelper.set('today_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkTodayTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetTodayTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimTodayTaskReward = function (id, treasureIndex, selectIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimTodayTaskReward', { id: id, treasureIndex: treasureIndex, selectIndex: selectIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateTodayTasks(data.todayTasks);
                            this.updateTodayTaskState();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取其他任务列表
    PlayerModel.prototype.getOtherTasks = function () {
        return this.otherTasks;
    };
    PlayerModel.prototype.updateOtherTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.otherTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'otherTask');
            task && _this.otherTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        if (this.otherTasks.length === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
        }
        else if (ReddotHelper_1.reddotHelper.getRegisterCount('other_task') === 0) {
            ReddotHelper_1.reddotHelper.unregister('other_task');
            ReddotHelper_1.reddotHelper.register('other_task', this.checkOtherTaskState, this, 1);
        }
    };
    // 更新其他任务进度
    PlayerModel.prototype.updateOtherTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (info) {
                var _a;
                var data = _this.otherTasks.find(function (m) { return m.id === info.id; });
                if (data) {
                    data.state = Enums_1.TaskState.UNDONE;
                    (_a = data.cond) === null || _a === void 0 ? void 0 : _a.updateProgress(info.progress);
                }
                else {
                    var task = new TaskObj_1.default().init(info, 'otherTask');
                    task && _this.otherTasks.push(task);
                }
            });
            this.updateOtherTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GUIDE_TASK_LIST);
        }
    };
    PlayerModel.prototype.getCangetOtherTask = function () {
        var task = null;
        this.otherTasks.forEach(function (m) {
            var state = m.checkUpdateComplete();
            if (!task && state === Enums_1.TaskState.CANGET) {
                task = m;
            }
        });
        return task;
    };
    // 刷新任务状态
    PlayerModel.prototype.updateOtherTaskState = function () {
        if (this.otherTasks.length === 0) {
            return;
        }
        this.otherTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        var task = this.getCangetOtherTask();
        ReddotHelper_1.reddotHelper.set('other_task', !!task);
        this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
    };
    // 红点检测
    PlayerModel.prototype.checkOtherTaskState = function (val) {
        if (val) {
            return val;
        }
        var task = this.getCangetOtherTask(), ok = !!task;
        if (ok !== val) {
            this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE, task);
        }
        return ok;
    };
    // 领取任务奖励
    PlayerModel.prototype.claimOtherTaskReward = function (id, treasureIndex, selectIndex) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimOtherTaskReward', { id: id, treasureIndex: treasureIndex, selectIndex: selectIndex }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateRewardItemsByFlags(data.rewards);
                            this.updateOtherTasks(data.otherTasks);
                            if (this.otherTasks.length === 0) {
                                this.emit(EventType_1.default.UPDATE_GUIDE_TASK_STATE);
                            }
                            else {
                                this.updateOtherTaskState();
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取所有任务
    PlayerModel.prototype.getPlayerAllTasks = function () {
        var tasks = [];
        tasks.pushArr(this.getGuideTasks());
        if (tasks.length === 0) {
            tasks.pushArr(this.getTodayTasks());
        }
        tasks.pushArr(this.getOtherTasks());
        return tasks;
    };
    PlayerModel.prototype.getPlayerTaskCount = function () {
        return (this.guideTasks.length || this.todayTasks.length) + this.otherTasks.length;
    };
    PlayerModel.prototype.getCangetPlayerTask = function () {
        return this.getCangetGuideTask() || this.getCangetTodayTask() || this.getCangetOtherTask();
    };
    // 刷新添加产量时间
    PlayerModel.prototype.updateAddOutputTime = function (timeMap) {
        this.addOutputSurplusTime = timeMap || {};
        this.getAddOutputTime = Date.now();
    };
    // 购买添加产量时间
    PlayerModel.prototype.buyAddOutputTime = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.user.getGold() < Constant_1.ADD_OUTPUT_GOLD) {
                            return [2 /*return*/, ECode_1.ecode.GOLD_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BuyAddOutput', { type: type }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateAddOutputTime(data.addOutputSurplusTime);
                            this.user.setGold(data.gold);
                            this.updateOutputByFlags(data.output);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 添加隐藏的私聊频道
    PlayerModel.prototype.addHidePChatChannels = function (channel, uid) {
        this.hidePChatChannels[channel] = uid;
    };
    PlayerModel.prototype.getOccupyLandCountMap = function () { return this.occupyLandCountMap; };
    // 刷新攻占领地数量
    PlayerModel.prototype.updateOccupyLandCountMap = function (data) {
        this.occupyLandCountMap = {};
        for (var key in data) {
            this.occupyLandCountMap[key] = data[key].arr;
        }
    };
    // 击杀数量
    PlayerModel.prototype.getKillRecordMap = function () { return this.killRecordMap; };
    PlayerModel.prototype.recordKillCount = function (id, count) {
        var val = this.killRecordMap[id] || 0;
        this.killRecordMap[id] = val + count;
    };
    // 记录结果
    PlayerModel.prototype.getBattleForecastRetData = function (index, key) {
        var _a;
        return (_a = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; })) === null || _a === void 0 ? void 0 : _a.data;
    };
    PlayerModel.prototype.setBattleForecastRetMap = function (index, key, data) {
        var it = this.tempBattleForecastRets.find(function (m) { return m.index === index && m.key === key; });
        if (it) {
            it.data = data;
            return;
        }
        else if (this.tempBattleForecastRets.length > 30) {
            this.tempBattleForecastRets.shift();
        }
        this.tempBattleForecastRets.push({ index: index, key: key, data: data });
    };
    // 刷新士兵的装备等级
    PlayerModel.prototype.updatePawnHeroAttr = function (id, attrs) {
        var areaCenter = GameHelper_1.gameHpr.areaCenter;
        this.baseArmys.forEach(function (m) {
            var _a;
            var area = areaCenter.getArea(m.index);
            if (area && !area.isBattleing()) {
                (_a = area.getArmyByUid(m.uid)) === null || _a === void 0 ? void 0 : _a.pawns.forEach(function (pawn) { return pawn.updateHeroAttr(id, attrs); });
            }
        });
    };
    // 刷新单个槽位信息
    PlayerModel.prototype.updateHeroSlotOne = function (data) {
        var index = Constant_1.HERO_SLOT_LV_COND.indexOf(data.lv);
        if (index === -1) {
            return;
        }
        var info = this.heroSlots[index];
        if (info) {
            info.fromSvr(data);
        }
        else {
            this.heroSlots[index] = new HeroSlotObj_1.default().fromSvr(data);
        }
        this.emit(EventType_1.default.UPDATE_HERO_SLOT_INFO);
    };
    // 供奉英雄
    PlayerModel.prototype.worshipHero = function (index, id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqWorshipHero({ index: index, id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                            this.setUnlockPawnIds(data.unlockPawnIds);
                            this.updatePawnSlots(data.pawnSlots);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 改变士兵画像
    PlayerModel.prototype.changePawnPortrayal = function (index, armyUid, uid, portrayalId) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnPortrayal({ index: index, armyUid: armyUid, uid: uid, portrayalId: portrayalId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.updateHeroSlotOne(data.slot);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测某个士兵是否可化身
    PlayerModel.prototype.checkCanAvatarPawn = function (id) {
        return this.heroSlots.some(function (m) { return !!m.hero && !m.avatarArmyUID && !m.isDie() && m.hero.avatarPawn === id; });
    };
    PlayerModel.prototype.getHeroSlotByPawnId = function (id) {
        return this.heroSlots.find(function (m) { var _a; return ((_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn) === id; });
    };
    PlayerModel.prototype.getMapMarks = function () { return this.mapMarks; };
    PlayerModel.prototype.getMapMark = function (index) { return this.mapMarks[index]; };
    // 添加标记
    PlayerModel.prototype.addMapMark = function (index, flag, desc) {
        var mark = this.mapMarks[index];
        if (!mark) {
            this.mapMarks[index] = { flag: flag, desc: desc };
        }
        else {
            mark.flag = flag;
            mark.desc = desc;
        }
        this.emit(EventType_1.default.UPDATE_MAP_MARK);
    };
    // 删除标记
    PlayerModel.prototype.removeMapMark = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.mapMarks[index]) {
                            return [2 /*return*/, ''];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_RemoveMapMark', { index: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            delete this.mapMarks[index];
                            this.emit(EventType_1.default.UPDATE_MAP_MARK);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 立即结算游戏 只限血战到底
    PlayerModel.prototype.nowSettleGame = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_SettleGame', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, false];
                        }
                        info = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid());
                        if (info) {
                            info.isSettled = true;
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('main/GameOver')];
                    case 2:
                        _b.sent();
                        return [2 /*return*/, true];
                }
            });
        });
    };
    PlayerModel.prototype.getDeadPawnLvMap = function () { return this.deadPawnLvMap; };
    // 请求战败士兵回馆概率
    PlayerModel.prototype.reqDeadPawnLvMap = function (interval) {
        if (interval === void 0) { interval = 60; }
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (interval > 0 && this.lastReqDeadPawnLvMapTime > 0 && Date.now() - this.lastReqDeadPawnLvMapTime <= interval * 1000) {
                            return [2 /*return*/, this.deadPawnLvMap];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetPawnDeadLvMap({})];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, this.deadPawnLvMap];
                        }
                        this.lastReqDeadPawnLvMapTime = Date.now();
                        this.deadPawnLvMap = (data === null || data === void 0 ? void 0 : data.pawnDeadLvMap) || {};
                        return [2 /*return*/, this.deadPawnLvMap];
                }
            });
        });
    };
    //#region ----------------------------------------- net listener function --------------------------------------------
    // 更新玩家信息
    PlayerModel.prototype.OnUpdatePlayerInfo = function (data) {
        var _this = this;
        cc.log('OnUpdatePlayerInfo', data.list);
        data.list.forEach(function (m) {
            var data = m['data_' + m.type];
            if (m.type === Enums_1.NotifyType.OUTPUT) { //产出
                _this.updateRewardItemsByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_ITEMS) { //更新通用物品
                _this.updateRewardItemsByFlags(data);
            }
            else if (m.type === Enums_1.NotifyType.BT_QUEUE) { //建造队列
                _this.updateBtQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.BUILD_UP) { //建筑升级
                _this.updateMainBuildInfo(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_DRILL_QUEUE) { //训练队列
                _this.updatePawnDrillQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_LEVELING_QUEUE) { //练级队列
                _this.updatePawnLevelingQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_CURING_QUEUE) { // 刷新士兵治疗队列
                _this.updatePawnCuringQueue(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_ADD) { // 添加受伤的士兵
                _this.addInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.PAWN_INJURY_REMOVE) { // 受伤士兵移除
                _this.removeInjuryPawn(data);
            }
            else if (m.type === Enums_1.NotifyType.ARMY_DIST) { //军队分布
                _this.updateArmyDists(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_MERCHANT) { //更新商人
                _this.updateMerchants(data);
            }
            else if (m.type === Enums_1.NotifyType.FORGE_EQUIP_RET) { //打造装备结果
                _this.updateCurrForgeEquip(null);
                var isNew = _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.FORGE_EQUIP_COMPLETE, data.uid);
                _this.addForgeMessage(data.uid.split('_')[0], isNew);
            }
            else if (m.type === Enums_1.NotifyType.SMELT_EQUIP_RET) { //融炼装备结果
                _this.updateCurrSmeltEquip(null);
                _this.updateEquipInfo(data);
                _this.emit(EventType_1.default.SMELT_EQUIP_COMPLETE, data.uid);
                _this.addSmeltMessage(data.uid.split('_')[0]);
            }
            else if (m.type === Enums_1.NotifyType.ADD_OUTPUT_TIME) { //刷新添加的产量时间
                _this.updateAddOutputTime(data);
                _this.emit(EventType_1.default.UPDATE_ADD_OUTPUT_TIME);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_GENERAL_TASKS) { //刷新常规任务
                GameHelper_1.gameHpr.task.updateGeneralTasks(data || []);
            }
            else if (m.type === Enums_1.NotifyType.NEW_TREASURE) { //是否有新的宝箱
                ReddotHelper_1.reddotHelper.set('treasure_main', !!data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TODAY_INFO) { //每日信息
                _this.todayOccupyCellCount = data.todayOccupyCellCount || 0;
                _this.todayReplacementCount = data.todayReplacementCount || 0;
                _this.cellTondenCount = data.cellTondenCount || 0;
                _this.updateTodayTasks(data.todayTasks || []);
                _this.updateTodayTaskState();
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.CELL_TONDEN_COUNT) { //刷新屯田次数
                _this.cellTondenCount = data || 0;
                _this.emit(EventType_1.default.UPDATE_TONDEN_COUNT);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_TASKS) { //刷新任务进度
                _this.updateGuideTasksProgress(data.guideTasks || []);
                _this.updateTodayTasksProgress(data.todayTasks || []);
                _this.updateOtherTasksProgress(data.otherTasks || []);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_LAND_SCORE) { //刷新玩家领地积分
                _this.landScore = data.landScore || 0;
                _this.maxOccupyLandDifficulty = data.maxOccupyLandDifficulty || 1;
                _this.updateOccupyLandCountMap(data.occupyLandCountMap || {});
            }
            else if (m.type === Enums_1.NotifyType.CHANGE_HERO_SLOT_INFO) { //英雄殿信息改变
                _this.updateHeroSlotOne(data);
            }
            else if (m.type === Enums_1.NotifyType.COMPENSATE) { //战损补偿通知
                ViewHelper_1.viewHelper.showMessageBox('ui.battle_compensate_tip', { okText: 'ui.button_gotit' });
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_POLICY_SLOT) { //政策槽位更新
                _this.updatePolicySlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_EQUIP_SLOT) { //装备槽位更新
                _this.updateEquipSlots(data);
            }
            else if (m.type === Enums_1.NotifyType.UPDATE_PAWN_SLOT) { //士兵槽位更新
                _this.updatePawnSlots(data);
            }
        });
    };
    __decorate([
        ut.syncLock
    ], PlayerModel.prototype, "getBazaarRecords", null);
    PlayerModel = __decorate([
        mc.addmodel('player')
    ], PlayerModel);
    return PlayerModel;
}(mc.BaseModel));
exports.default = PlayerModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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