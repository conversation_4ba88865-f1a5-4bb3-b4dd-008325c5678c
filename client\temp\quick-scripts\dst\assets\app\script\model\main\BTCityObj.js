
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/BTCityObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'de5a53IBvNBHYZ6GaUewF5Y', 'BTCityObj');
// app/script/model/main/BTCityObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 修建城市信息
var BTCityObj = /** @class */ (function () {
    function BTCityObj() {
        this.index = 0;
        this.id = 0;
        this.needTime = 0; //需要建造的时间
        this.surplusTime = 0; //剩余建造的时间
        this.getTime = 0;
        this.json = null;
    }
    BTCityObj.prototype.init = function (data) {
        this.index = data.index;
        this.id = data.id;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        this.json = assetsMgr.getJsonData('city', this.id);
        return this;
    };
    // 获取实际的剩余时间
    BTCityObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 获取经过的时间
    BTCityObj.prototype.getElapsedTime = function () {
        return Math.max(0, this.needTime - this.getSurplusTime());
    };
    return BTCityObj;
}());
exports.default = BTCityObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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