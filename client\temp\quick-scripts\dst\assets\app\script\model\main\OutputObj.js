
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/OutputObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9d4a0+Fr6pDk4YJ5fKftlPg', 'OutputObj');
// app/script/model/main/OutputObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 产出
var OutputObj = /** @class */ (function () {
    function OutputObj(type) {
        this.type = '';
        this.value = 0;
        this.opHour = 0; //每小时产出
        this.type = type;
    }
    OutputObj.prototype.fromSvr = function (data) {
        this.value = (data === null || data === void 0 ? void 0 : data.value) || 0;
        this.opHour = (data === null || data === void 0 ? void 0 : data.opHour) || 0;
        return this;
    };
    OutputObj.prototype.updateInfo = function (data) {
        if (data === undefined || data === null) {
            return;
        }
        else if (data.opHour !== this.opHour || data.value !== this.value) {
            this.opHour = data.opHour;
            this.set(data.value, true);
        }
    };
    // 设置
    OutputObj.prototype.set = function (val, isEmit) {
        if (!isNaN(val) && val !== undefined) {
            var add = Math.floor(val - this.value);
            this.value = Math.floor(val);
            if (isEmit) {
                eventCenter.emit(this.type, add);
            }
        }
    };
    // 改变
    OutputObj.prototype.change = function (val, isEmit) {
        if (this.value + val < 0) {
            return -1;
        }
        this.set(this.value + val, isEmit);
        return this.value;
    };
    return OutputObj;
}());
exports.default = OutputObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxtYWluXFxPdXRwdXRPYmoudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxLQUFLO0FBQ0w7SUFPSSxtQkFBWSxJQUFZO1FBTGhCLFNBQUksR0FBVyxFQUFFLENBQUE7UUFFbEIsVUFBSyxHQUFXLENBQUMsQ0FBQTtRQUNqQixXQUFNLEdBQVcsQ0FBQyxDQUFBLENBQUMsT0FBTztRQUc3QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtJQUNwQixDQUFDO0lBRU0sMkJBQU8sR0FBZCxVQUFlLElBQVM7UUFDcEIsSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLEtBQUksQ0FBQyxDQUFBO1FBQzdCLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQSxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsTUFBTSxLQUFJLENBQUMsQ0FBQTtRQUMvQixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSw4QkFBVSxHQUFqQixVQUFrQixJQUFTO1FBQ3ZCLElBQUksSUFBSSxLQUFLLFNBQVMsSUFBSSxJQUFJLEtBQUssSUFBSSxFQUFFO1lBQ3JDLE9BQU07U0FDVDthQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxJQUFJLENBQUMsTUFBTSxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNqRSxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7WUFDekIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQzdCO0lBQ0wsQ0FBQztJQUVELEtBQUs7SUFDRSx1QkFBRyxHQUFWLFVBQVcsR0FBVyxFQUFFLE1BQWdCO1FBQ3BDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksR0FBRyxLQUFLLFNBQVMsRUFBRTtZQUNsQyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDeEMsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzVCLElBQUksTUFBTSxFQUFFO2dCQUNSLFdBQVcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FBQTthQUNuQztTQUNKO0lBQ0wsQ0FBQztJQUVELEtBQUs7SUFDRSwwQkFBTSxHQUFiLFVBQWMsR0FBVyxFQUFFLE1BQWdCO1FBQ3ZDLElBQUksSUFBSSxDQUFDLEtBQUssR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFO1lBQ3RCLE9BQU8sQ0FBQyxDQUFDLENBQUE7U0FDWjtRQUNELElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxHQUFHLEVBQUUsTUFBTSxDQUFDLENBQUE7UUFDbEMsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFBO0lBQ3JCLENBQUM7SUFDTCxnQkFBQztBQUFELENBN0NBLEFBNkNDLElBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyDkuqflh7pcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgT3V0cHV0T2JqIHtcclxuXHJcbiAgICBwcml2YXRlIHR5cGU6IHN0cmluZyA9ICcnXHJcblxyXG4gICAgcHVibGljIHZhbHVlOiBudW1iZXIgPSAwXHJcbiAgICBwdWJsaWMgb3BIb3VyOiBudW1iZXIgPSAwIC8v5q+P5bCP5pe25Lqn5Ye6XHJcblxyXG4gICAgY29uc3RydWN0b3IodHlwZTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy50eXBlID0gdHlwZVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBmcm9tU3ZyKGRhdGE6IGFueSkge1xyXG4gICAgICAgIHRoaXMudmFsdWUgPSBkYXRhPy52YWx1ZSB8fCAwXHJcbiAgICAgICAgdGhpcy5vcEhvdXIgPSBkYXRhPy5vcEhvdXIgfHwgMFxyXG4gICAgICAgIHJldHVybiB0aGlzXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHVwZGF0ZUluZm8oZGF0YTogYW55KSB7XHJcbiAgICAgICAgaWYgKGRhdGEgPT09IHVuZGVmaW5lZCB8fCBkYXRhID09PSBudWxsKSB7XHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5vcEhvdXIgIT09IHRoaXMub3BIb3VyIHx8IGRhdGEudmFsdWUgIT09IHRoaXMudmFsdWUpIHtcclxuICAgICAgICAgICAgdGhpcy5vcEhvdXIgPSBkYXRhLm9wSG91clxyXG4gICAgICAgICAgICB0aGlzLnNldChkYXRhLnZhbHVlLCB0cnVlKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyDorr7nva5cclxuICAgIHB1YmxpYyBzZXQodmFsOiBudW1iZXIsIGlzRW1pdD86IGJvb2xlYW4pIHtcclxuICAgICAgICBpZiAoIWlzTmFOKHZhbCkgJiYgdmFsICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgY29uc3QgYWRkID0gTWF0aC5mbG9vcih2YWwgLSB0aGlzLnZhbHVlKVxyXG4gICAgICAgICAgICB0aGlzLnZhbHVlID0gTWF0aC5mbG9vcih2YWwpXHJcbiAgICAgICAgICAgIGlmIChpc0VtaXQpIHtcclxuICAgICAgICAgICAgICAgIGV2ZW50Q2VudGVyLmVtaXQodGhpcy50eXBlLCBhZGQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5pS55Y+YXHJcbiAgICBwdWJsaWMgY2hhbmdlKHZhbDogbnVtYmVyLCBpc0VtaXQ/OiBib29sZWFuKSB7XHJcbiAgICAgICAgaWYgKHRoaXMudmFsdWUgKyB2YWwgPCAwKSB7XHJcbiAgICAgICAgICAgIHJldHVybiAtMVxyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLnNldCh0aGlzLnZhbHVlICsgdmFsLCBpc0VtaXQpXHJcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVcclxuICAgIH1cclxufSJdfQ==