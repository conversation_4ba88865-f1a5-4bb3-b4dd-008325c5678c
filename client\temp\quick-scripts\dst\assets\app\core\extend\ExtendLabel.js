
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendLabel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a3e7bcIQTZGoYq9aaTNrOx0', 'ExtendLabel');
// app/core/extend/ExtendLabel.ts

/**
 * Label扩展方法
 */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
cc.Label.prototype.setLocaleKey = function (key) {
    var params = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        params[_i - 1] = arguments[_i];
    }
    var localeLabel = this.Component(cc.LocaleLabel);
    if (localeLabel) {
        localeLabel.setKey.apply(localeLabel, __spread([key], params));
    }
    else {
        this.string = key;
    }
    return this;
};
cc.RichText.prototype.setLocaleKey = function (key) {
    var params = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        params[_i - 1] = arguments[_i];
    }
    var localeLabel = this.Component(cc.LocaleRichText);
    if (localeLabel) {
        localeLabel.setKey.apply(localeLabel, __spread([key], params));
    }
    else {
        this.string = key;
    }
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxleHRlbmRcXEV4dGVuZExhYmVsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUNBOztHQUVHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFSCxFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxZQUFZLEdBQUcsVUFBVSxHQUFXO0lBQUUsZ0JBQWdCO1NBQWhCLFVBQWdCLEVBQWhCLHFCQUFnQixFQUFoQixJQUFnQjtRQUFoQiwrQkFBZ0I7O0lBQ3JFLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFdBQVcsQ0FBQyxDQUFBO0lBQ2xELElBQUksV0FBVyxFQUFFO1FBQ2IsV0FBVyxDQUFDLE1BQU0sT0FBbEIsV0FBVyxZQUFRLEdBQUcsR0FBSyxNQUFNLEdBQUM7S0FDckM7U0FBTTtRQUNILElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFBO0tBQ3BCO0lBQ0QsT0FBTyxJQUFJLENBQUE7QUFDZixDQUFDLENBQUE7QUFFRCxFQUFFLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxZQUFZLEdBQUcsVUFBVSxHQUFXO0lBQUUsZ0JBQWdCO1NBQWhCLFVBQWdCLEVBQWhCLHFCQUFnQixFQUFoQixJQUFnQjtRQUFoQiwrQkFBZ0I7O0lBQ3hFLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLGNBQWMsQ0FBQyxDQUFBO0lBQ3JELElBQUksV0FBVyxFQUFFO1FBQ2IsV0FBVyxDQUFDLE1BQU0sT0FBbEIsV0FBVyxZQUFRLEdBQUcsR0FBSyxNQUFNLEdBQUM7S0FDckM7U0FBTTtRQUNILElBQUksQ0FBQyxNQUFNLEdBQUcsR0FBRyxDQUFBO0tBQ3BCO0FBQ0wsQ0FBQyxDQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXG4vKipcbiAqIExhYmVs5omp5bGV5pa55rOVXG4gKi9cblxuY2MuTGFiZWwucHJvdG90eXBlLnNldExvY2FsZUtleSA9IGZ1bmN0aW9uIChrZXk6IHN0cmluZywgLi4ucGFyYW1zOiBhbnlbXSkge1xuICAgIGNvbnN0IGxvY2FsZUxhYmVsID0gdGhpcy5Db21wb25lbnQoY2MuTG9jYWxlTGFiZWwpXG4gICAgaWYgKGxvY2FsZUxhYmVsKSB7XG4gICAgICAgIGxvY2FsZUxhYmVsLnNldEtleShrZXksIC4uLnBhcmFtcylcbiAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLnN0cmluZyA9IGtleVxuICAgIH1cbiAgICByZXR1cm4gdGhpc1xufVxuXG5jYy5SaWNoVGV4dC5wcm90b3R5cGUuc2V0TG9jYWxlS2V5ID0gZnVuY3Rpb24gKGtleTogc3RyaW5nLCAuLi5wYXJhbXM6IGFueVtdKSB7XG4gICAgY29uc3QgbG9jYWxlTGFiZWwgPSB0aGlzLkNvbXBvbmVudChjYy5Mb2NhbGVSaWNoVGV4dClcbiAgICBpZiAobG9jYWxlTGFiZWwpIHtcbiAgICAgICAgbG9jYWxlTGFiZWwuc2V0S2V5KGtleSwgLi4ucGFyYW1zKVxuICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMuc3RyaW5nID0ga2V5XG4gICAgfVxufSJdfQ==