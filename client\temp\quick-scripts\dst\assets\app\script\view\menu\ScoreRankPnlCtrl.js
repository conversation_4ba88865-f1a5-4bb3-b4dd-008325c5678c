
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/ScoreRankPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9d864tfbERGBqCmcfgJV8bg', 'ScoreRankPnlCtrl');
// app/script/view/menu/ScoreRankPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var ScoreRankPnlCtrl = /** @class */ (function (_super) {
    __extends(ScoreRankPnlCtrl, _super);
    function ScoreRankPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://root/top_n
        _this.descNode_ = null; // path://root/desc_n
        _this.lineNode_ = null; // path://root/line_n
        _this.emptyNode_ = null; // path://root/empty_n
        _this.listSv_ = null; // path://root/list_sv
        _this.meNode_ = null; // path://root/me_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.model = null;
        _this.list = [];
        return _this;
    }
    ScoreRankPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ScoreRankPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('rank');
                return [2 /*return*/];
            });
        });
    };
    ScoreRankPnlCtrl.prototype.onEnter = function (data) {
        this.showUserScoreRankList();
    };
    ScoreRankPnlCtrl.prototype.onRemove = function () {
    };
    ScoreRankPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    ScoreRankPnlCtrl.prototype.showUserScoreRankList = function () {
        return __awaiter(this, void 0, void 0, function () {
            var sv, me, _a, meInfo;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        sv = this.listSv_, me = this.meNode_;
                        sv.content.Swih('');
                        this.loadingNode_.active = true;
                        me.active = this.lineNode_.active = this.emptyNode_.active = false;
                        _a = this;
                        return [4 /*yield*/, this.model.getUserScoreRankList()];
                    case 1:
                        _a.list = _b.sent();
                        if (!this.isValid || !this.isEnter()) {
                            return [2 /*return*/];
                        }
                        this.loadingNode_.active = false;
                        // 前三名
                        this.topNode_.Items(this.list.splice(0, 3), function (it, data, i) { return _this.updateUserScoreRankItem(it, data, i, true); });
                        sv.stopAutoScroll();
                        sv.content.y = 0;
                        this.lineNode_.active = this.list.length > 0;
                        this.emptyNode_.active = !this.list.length;
                        sv.List(this.list.length || 0, function (it, i) { return _this.updateUserScoreRankItem(it, _this.list[i], i + 3, false); });
                        // 显示自己
                        me.active = true;
                        meInfo = this.model.getMeScoreRankInfo();
                        this.updateUserScoreRankItem(me, meInfo, meInfo.no, false);
                        me.Child('icon').Color(meInfo.no < 0 ? '#B6A591' : '#FFFFFF');
                        return [2 /*return*/];
                }
            });
        });
    };
    ScoreRankPnlCtrl.prototype.updateUserScoreRankItem = function (it, data, no, isTop) {
        !isTop && (it.Child('no', cc.Label).string = no < 0 ? '-' : (no + 1) > 999 ? '999+' : (no + 1) + '');
        it.Child('name', cc.Label).string = ut.nameFormator(data.nickname, 7);
        ResHelper_1.resHelper.loadPlayerHead(it.Child('head'), data.headIcon, this.key);
        var _a = GameHelper_1.gameHpr.resolutionRankScore(data.score, data.rankCount), id = _a.id, winPoint = _a.winPoint;
        var icon = it.Child('icon') || it.Child('rank/icon'), score = it.Child('score') || it.Child('rank/score'), win_point = it.Child('win_point') || it.Child('rank/win_point');
        ResHelper_1.resHelper.loadRankScoreIcon(id, icon, this.key);
        score.setLocaleKey('ui.rank_name_' + (id >= 0 ? id : 'none'));
        win_point.setLocaleKey('ui.bracket', winPoint);
        var line = it.Child('line');
        if (line) {
            line.active = no < this.list.length - 1;
        }
    };
    ScoreRankPnlCtrl = __decorate([
        ccclass
    ], ScoreRankPnlCtrl);
    return ScoreRankPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ScoreRankPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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