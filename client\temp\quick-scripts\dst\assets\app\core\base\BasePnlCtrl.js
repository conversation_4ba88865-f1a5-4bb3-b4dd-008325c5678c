
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BasePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '51806AeOpdHh6PrkzelalK1', 'BasePnlCtrl');
// app/core/base/BasePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseViewCtrl_1 = require("./BaseViewCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
// 基础UI视图控制器
var BasePnlCtrl = /** @class */ (function (_super) {
    __extends(BasePnlCtrl, _super);
    function BasePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = ''; // 传入名
        _this.mod = ''; // 模块名
        _this.url = ''; // UI地址
        _this.isClean = true;
        _this.isAct = true; //是否播放动作
        _this.isMask = true; //是否显示遮照
        _this.maskOpacity = 0; //mask透明度
        _this.adaptHeight = 400; //适应高度距离
        _this.mask = null; // 当前的遮照
        _this.__open_index = 0; // 打开顺序
        return _this;
    }
    BasePnlCtrl.prototype.__create = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this._state = 'create';
                        this.node.group = 'ui';
                        this.__listenMaps();
                        this.__register('create');
                        return [4 /*yield*/, this.onCreate()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    BasePnlCtrl.prototype.__enter = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        if (this._state !== 'enter') {
            this._state = 'enter';
            this.__register('enter');
        }
        this.onEnter.apply(this, __spread(params));
    };
    BasePnlCtrl.prototype.__remove = function () {
        this._state = 'remove';
        this.__unregister('enter');
        this.onRemove();
    };
    BasePnlCtrl.prototype.__clean = function () {
        this._state = 'clean';
        this.__unregister();
        this.onClean();
    };
    BasePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BasePnlCtrl.prototype.onEnter = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    BasePnlCtrl.prototype.onRemove = function () {
    };
    BasePnlCtrl.prototype.onClean = function () {
    };
    BasePnlCtrl.prototype.onPlayActionComplete = function () {
    };
    BasePnlCtrl.prototype.isEnter = function () {
        return this._state === 'enter';
    };
    BasePnlCtrl.prototype.hide = function () {
        this.emit(CoreEventType_1.default.HIDE_PNL, this);
    };
    BasePnlCtrl.prototype.close = function () {
        this.emit(CoreEventType_1.default.CLOSE_PNL, this);
    };
    BasePnlCtrl.prototype.setOpacity = function (val) {
        this.node.opacity = val;
        if (this.mask) {
            this.mask.opacity = Math.min(val, this.getMaskInitOpacity());
        }
    };
    BasePnlCtrl.prototype.showMask = function (val) {
        if (this.mask) {
            this.mask.active = val;
        }
    };
    BasePnlCtrl.prototype.setIndex = function (index) {
        this.node.zIndex = index;
        if (this.mask) {
            this.mask.zIndex = index - 1;
        }
    };
    BasePnlCtrl.prototype.getMaskInitOpacity = function () {
        return this.maskOpacity || 153;
    };
    BasePnlCtrl.prototype.setParam = function (opts) {
        var _a, _b, _c, _d, _e;
        this.isClean = (_a = opts.isClean) !== null && _a !== void 0 ? _a : this.isClean;
        this.isAct = (_b = opts.isAct) !== null && _b !== void 0 ? _b : this.isAct;
        this.isMask = (_c = opts.isMask) !== null && _c !== void 0 ? _c : this.isMask;
        this.maskOpacity = (_d = opts.maskOpacity) !== null && _d !== void 0 ? _d : this.maskOpacity;
        this.adaptHeight = (_e = opts.adaptHeight) !== null && _e !== void 0 ? _e : this.adaptHeight;
    };
    return BasePnlCtrl;
}(BaseViewCtrl_1.default));
exports.default = BasePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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