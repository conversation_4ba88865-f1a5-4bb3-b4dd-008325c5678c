
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/message/ChatModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '20f0bAdjqdCU7cnOEKTw1uF', 'ChatModel');
// app/script/model/message/ChatModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 聊天模块
 */
var ChatModel = /** @class */ (function (_super) {
    __extends(ChatModel, _super);
    function ChatModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.player = null;
        _this.alliance = null;
        _this.lobby = null;
        _this.chatMap = {}; //所有聊天map
        _this.pchatChannels = []; //私聊列表
        _this.curPChatChannel = '';
        _this.curLookChannel = ''; //当前正在看的频道
        _this.lastSendChatTime = 0; //上次发送时间
        _this.tolerateCount = 0;
        _this.restStartTime = 0; //休息时间
        _this.bannedStartTime = 0; //禁言按钮禁止时间
        _this.bannedStartMaxTime = 0; //禁言按钮禁止最大时间
        return _this;
    }
    ChatModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.player = this.getModel('player');
        this.alliance = this.getModel('alliance');
        this.lobby = this.getModel('lobby');
    };
    // 这里检测是否获取过
    ChatModel.prototype.init = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, list, canCloseTimeMap, pchatMap, uid, bannedData, blacklistMap, macLandCount, hideMap, channel, unreadPchatTimeMap, _loop_1, this_1, i, channel;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        this.chatMap = {};
                        this.pchatChannels = [];
                        return [4 /*yield*/, this.net.request('game/HD_GetChats', {})];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [], canCloseTimeMap = (data === null || data === void 0 ? void 0 : data.canCloseTimeMap) || {};
                        pchatMap = {};
                        uid = GameHelper_1.gameHpr.getUid();
                        bannedData = storageMgr.loadJson('chat_banned_time') || {};
                        this.bannedStartTime = bannedData.startTime || 0;
                        this.bannedStartMaxTime = bannedData.maxTime || 0;
                        blacklistMap = {};
                        GameHelper_1.gameHpr.friend.getBlacklists().forEach(function (m) { return blacklistMap[m.uid] = m.time; });
                        macLandCount = GameHelper_1.gameHpr.getMaxLandCountByPChat();
                        list.forEach(function (m) {
                            var _a;
                            var blacklistTime = blacklistMap[m.sender] || 0;
                            if (blacklistTime && m.time >= blacklistTime) {
                                return; //黑名单的消息不看
                            }
                            _this.checkChatItemInfo(m, m);
                            var chats = _this.chatMap[m.channel];
                            if (!chats) {
                                chats = _this.chatMap[m.channel] = [];
                            }
                            chats.unshift(m);
                            // 私聊部分
                            if (m.channel.startsWith('2_')) {
                                if (!pchatMap[m.channel]) {
                                    pchatMap[m.channel] = true;
                                    var _b = __read(m.channel.split('_'), 3), _ = _b[0], a = _b[1], b = _b[2];
                                    var otherUid = (a + b).replace(uid, '');
                                    var oplr = GameHelper_1.gameHpr.getPlayerInfo(otherUid);
                                    if (oplr) {
                                        _this.pchatChannels.push({
                                            channel: m.channel,
                                            head: oplr.headIcon,
                                            name: oplr.nickname,
                                            canCloseTime: macLandCount >= Constant_1.NOLIMIT_PCHAT_MAX_LAND ? 0 : (_a = canCloseTimeMap[m.channel]) !== null && _a !== void 0 ? _a : 0,
                                            getTime: Date.now(),
                                        });
                                    }
                                }
                                else {
                                    var pc = _this.pchatChannels.find(function (p) { return p.channel === m.channel; });
                                    if (pc) {
                                        pc.lastChatInfo = m;
                                    }
                                }
                            }
                        });
                        hideMap = this.player.getHidePChatChannels();
                        for (channel in hideMap) {
                            this.updateHidePChatChannel(channel, hideMap[channel]);
                        }
                        //
                        if (this.pchatChannels.length > 0) {
                            unreadPchatTimeMap = storageMgr.loadJson(this.getSavePChatKey('pchat_unread')) || {};
                            _loop_1 = function (i) {
                                var m = this_1.pchatChannels[i];
                                var chats = this_1.chatMap[m.channel];
                                if (!chats || chats.length === 0) {
                                    this_1.pchatChannels.splice(i, 1);
                                }
                                else {
                                    var time_1 = unreadPchatTimeMap[m.channel] || 0;
                                    var idx = time_1 === 0 ? -1 : chats.findIndex(function (m) { return m.time === time_1; });
                                    if (idx === -1) {
                                        idx = Math.max(0, chats.length - 1);
                                    }
                                    m.lastReadTime = time_1;
                                    m.count = chats.slice(0, idx).filter(function (m) { return m.sender !== uid; }).length;
                                    // m.lastChatInfo = m
                                }
                            };
                            this_1 = this;
                            for (i = this.pchatChannels.length - 1; i >= 0; i--) {
                                _loop_1(i);
                            }
                            // 找出当前私聊
                            this.pchatChannels.sort(function (a, b) {
                                var _a, _b;
                                var aw = ((_a = _this.chatMap[a.channel]) === null || _a === void 0 ? void 0 : _a[0].time) || 0;
                                var bw = ((_b = _this.chatMap[b.channel]) === null || _b === void 0 ? void 0 : _b[0].time) || 0;
                                return bw - aw;
                            });
                            channel = this.curPChatChannel = storageMgr.loadString(this.getSavePChatKey('pchat_channel')) || '';
                            if (!channel || !this.pchatChannels.has('channel', channel)) {
                                this.setCurPChatChannel(((_a = this.pchatChannels[0]) === null || _a === void 0 ? void 0 : _a.channel) || '');
                            }
                        }
                        else {
                            this.setCurPChatChannel('');
                        }
                        this.curLookChannel = '';
                        this.lastSendChatTime = this.lastSendChatTime || Date.now();
                        this.updateNewPChatDot();
                        this.net.on('game/OnChat', this.OnChat, this); //战场聊天
                        this.net.off('chat/OnLobbyChat', this.lobby.OnChat, this); //大厅聊天
                        this.net.on('chat/OnLobbyChat', this.lobby.OnChat, this.lobby); //大厅聊天
                        return [2 /*return*/];
                }
            });
        });
    };
    ChatModel.prototype.getSavePChatKey = function (key) {
        return key + '_' + GameHelper_1.gameHpr.getSid() + '_' + GameHelper_1.gameHpr.getUid();
    };
    ChatModel.prototype.saveUnreadPChatInfo = function () {
        var data = {};
        this.pchatChannels.forEach(function (m) {
            if (m.lastReadTime) {
                data[m.channel] = m.lastReadTime;
            }
        });
        storageMgr.saveJson(this.getSavePChatKey('pchat_unread'), data);
    };
    // 是否可以发联盟信息
    ChatModel.prototype.isCanSendAlli = function () {
        return this.player.isHasAlliance();
    };
    // 是否可以发私聊消息
    ChatModel.prototype.isCanSendSecret = function () {
        return this.pchatChannels.length > 0;
    };
    ChatModel.prototype.getPChatChannels = function () {
        return this.pchatChannels;
    };
    ChatModel.prototype.getCurPChatChannel = function () {
        return this.curPChatChannel;
    };
    ChatModel.prototype.setCurPChatChannel = function (val) {
        this.curPChatChannel = val;
        storageMgr.saveString(this.getSavePChatKey('pchat_channel'), val);
    };
    ChatModel.prototype.setCurLookChannel = function (val) {
        this.curLookChannel = val;
    };
    ChatModel.prototype.getCurLookChannel = function () {
        return this.curLookChannel;
    };
    ChatModel.prototype.getCurPChatChannelData = function () {
        var _this = this;
        return this.curPChatChannel ? this.pchatChannels.find(function (m) { return m.channel === _this.curPChatChannel; }) : null;
    };
    // 自己主动添加私聊
    ChatModel.prototype.addPChatByMe = function (uid) {
        var slefUid = GameHelper_1.gameHpr.getUid();
        var channel = '2_' + [Number(uid), Number(slefUid)].sort().join('_');
        var pchat = this.pchatChannels.find(function (m) { return m.channel.startsWith(channel); });
        if (!pchat) {
            pchat = this.addPChatInfo(channel + '_' + slefUid, uid);
        }
        this.curPChatChannel = pchat.channel;
        return { channel: pchat.channel, puid: uid, pchat: pchat };
    };
    ChatModel.prototype.addPChatInfo = function (channel, uid) {
        var canCloseTime = 0;
        if (channel.split('_')[3] === GameHelper_1.gameHpr.getUid() && GameHelper_1.gameHpr.getMaxLandCountByPChat() < Constant_1.NOLIMIT_PCHAT_MAX_LAND) {
            canCloseTime = Constant_1.REMOVE_PCHAT_TIME;
        }
        return this.pchatChannels.add({
            channel: channel,
            head: GameHelper_1.gameHpr.getPlayerHead(uid),
            name: GameHelper_1.gameHpr.getPlayerName(uid),
            count: 0,
            canCloseTime: canCloseTime,
            getTime: Date.now(),
        });
    };
    // 删除私聊
    ChatModel.prototype.removePChat = function (channel) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var chats, err, data, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        chats = this.chatMap[channel] || [];
                        err = '', data = null;
                        if (!(chats.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.net.request('game/HD_RemovePChat', { channel: channel }, true)];
                    case 1:
                        info = _b.sent();
                        err = info.err;
                        data = info.data;
                        _b.label = 2;
                    case 2:
                        if (data) {
                            this.player.addHidePChatChannels(channel, data.uid);
                            this.updateHidePChatChannel(channel, data.uid);
                        }
                        if (!err) {
                            this.pchatChannels.remove('channel', channel);
                            if (channel === this.curPChatChannel) {
                                this.setCurPChatChannel(((_a = this.pchatChannels[0]) === null || _a === void 0 ? void 0 : _a.channel) || '');
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 删除私聊频道 根据玩家uid
    ChatModel.prototype.removePChatChannel = function (channel, uid) {
        var _a;
        if (!channel) {
            return;
        }
        delete this.chatMap[channel];
        this.pchatChannels.remove('channel', channel);
        if (channel === this.curPChatChannel) {
            this.setCurPChatChannel(((_a = this.pchatChannels[0]) === null || _a === void 0 ? void 0 : _a.channel) || '');
        }
        this.emit(EventType_1.default.UPDATE_PCHAT_CHANNEL);
    };
    // 刷新隐藏私聊的频道
    ChatModel.prototype.updateHidePChatChannel = function (channel, uid) {
        var chats = this.chatMap[channel];
        if (chats) {
            var i = chats.findIndex(function (m) { return m.uid === uid; });
            if (i != -1) {
                chats.splice(i);
            }
        }
    };
    // 刷新私聊最后一次读
    ChatModel.prototype.updatePChatLastReadTime = function (data) {
        var _a;
        var chats = this.chatMap[data.channel] || [];
        var uid = GameHelper_1.gameHpr.getUid();
        data.lastReadTime = ((_a = chats.find(function (m) { return m.sender !== uid; })) === null || _a === void 0 ? void 0 : _a.time) || 0;
        data.count = 0;
        this.saveUnreadPChatInfo();
        this.updateNewPChatDot();
    };
    // 刷新私聊未读数量
    ChatModel.prototype.updatePChatCount = function (data) {
        if (data.type !== 2 || data.sender === GameHelper_1.gameHpr.getUid()) {
            return; //不是私聊 或者是自己发送的
        }
        var pc = this.pchatChannels.find(function (m) { return m.channel === data.channel; });
        if (!pc) {
            pc = this.addPChatInfo(data.channel, data.sender);
        }
        if (this.curLookChannel === data.channel) { //如果当前正在看这个频道
            pc.lastReadTime = data.time; //只更新时间
            pc.count = 0;
            this.saveUnreadPChatInfo();
        }
        else {
            pc.count += 1;
        }
        pc.lastChatInfo = data;
        if (!this.curPChatChannel) {
            this.curPChatChannel = data.channel;
            if (this.curLookChannel === '2_') {
                this.emit(EventType_1.default.CHANGE_CHAT_CHANNEL, { channel: this.curPChatChannel, puid: data.sender, pchat: pc });
            }
        }
        this.updateNewPChatDot();
    };
    ChatModel.prototype.updateNewPChatDot = function () {
        ReddotHelper_1.reddotHelper.set('new_pchat', this.pchatChannels.some(function (m) { return m.count > 0; }));
    };
    // 获取主动发起的私聊数量
    ChatModel.prototype.getInitiativePChatCount = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        return this.pchatChannels.filter(function (m) { return m.channel.split('_')[3] === uid; }).length;
    };
    // 获取聊天信息
    ChatModel.prototype.getChatsByChannel = function (channel) {
        var chats = this.chatMap[channel];
        if (!chats) {
            chats = this.chatMap[channel] = [];
        }
        return chats;
    };
    ChatModel.prototype.getChatsByType = function (type) {
        return this.getChatsByChannel(this.getChannelByType(type));
    };
    ChatModel.prototype.getChannelByType = function (type) {
        var channel = '0';
        if (type === 1 || type === 4) { //联盟
            var alliUid = this.player.getAllianceUid();
            if (alliUid) {
                var uid = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
                if (uid && uid !== '0') {
                    channel = '4_' + alliUid + '_' + uid;
                }
                else {
                    channel = '1_' + alliUid;
                }
            }
            else {
                channel = '';
            }
        }
        else if (type === 2) { //私聊
            channel = this.curPChatChannel || '2_';
        }
        else if (type === 0 || type === 5) { // 世界
            var uid = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL) || '';
            if (uid && uid !== '0') {
                channel = '5_' + uid;
            }
        }
        return channel;
    };
    // 收到服务器的聊天信息
    ChatModel.prototype.OnChat = function (notify) {
        var data = notify === null || notify === void 0 ? void 0 : notify.data;
        data.type = Number(data.channel.split('_')[0]) || 0;
        var list = this.getChatsByChannel(data.channel), uid = GameHelper_1.gameHpr.getUid();
        do {
            if (data.sender === uid) {
                var chat = list.find(function (m) { return m.uid === data.uid; });
                if (chat) {
                    chat.content = data.content;
                    chat.wait = false;
                    chat.bannedSurplusTime = data.bannedSurplusTime;
                    this.checkChatItemInfo(chat, data);
                    data = chat;
                    break;
                }
            }
            else if (GameHelper_1.gameHpr.friend.isInBlacklist(data.sender)) {
                return; //被加入黑名单直接返回
            }
            else {
                this.checkChatItemInfo(data, data);
            }
            this.addChat(list, data);
        } while (false);
        this.updatePChatCount(data);
        GameHelper_1.gameHpr.updateChatAllTime(list);
        this.emit(EventType_1.default.ADD_CHAT, data);
    };
    // 获取指定对话
    ChatModel.prototype.getTargetChat = function (type, childType, select) {
        this.changeSelectChannel(type, childType, { select: select });
        var target = null;
        if (type === 2) { // 直接进入私聊
            target = this.getPChatChannels().find(function (m) { return m.channel.startsWith(select[type]); });
        }
        return target;
    };
    // 更改选择的频道
    ChatModel.prototype.changeSelectChannel = function (type, childType, param) {
        if (param === void 0) { param = {}; }
        if (!ut.isEmptyObject(param.select)) { // 更改选择的频道
            var channel = param.select[type];
            if (type === 0) { // 世界频道 战场/大厅
                GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_TAB, childType);
                GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, channel);
            }
            else if (type === 1) { // 同盟频道 联盟/队伍
                GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.ALLI_CHAT_TAB, childType);
                GameHelper_1.gameHpr.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL, channel);
            }
            else if (type === 2) { // 私聊频道 好友/私聊
                this.setCurPChatChannel(channel);
            }
        }
    };
    // 发送聊天信息
    ChatModel.prototype.sendChat = function (type, childType, content, param) {
        var _this = this;
        var _a, _b, _c, _d, _e;
        if (param === void 0) { param = {}; }
        this.changeSelectChannel(type, childType, param);
        var channel = this.getChannelByType(type);
        if (channel === '2_') {
            return 2;
        }
        var isMilitaryAlliChannelChat = this.isMilitaryAlliChannelChat(channel);
        if (this.checkRestSurplusTime(isMilitaryAlliChannelChat) > 0) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BAN_OPT);
            return 1;
        }
        else if (this.checkOftenTime(isMilitaryAlliChannelChat)) {
            ViewHelper_1.viewHelper.showAlert('toast.send_chat_tolerate');
            return 1;
        }
        var now = this.lastSendChatTime = Date.now();
        var list = this.getChatsByChannel(channel);
        var uid = ut.UID(), user = GameHelper_1.gameHpr.user, isSpectate = GameHelper_1.gameHpr.isSpectate();
        var data = {
            uid: uid,
            type: type,
            channel: channel,
            sender: user.getUid(),
            content: content,
            emoji: param.emoji || 0,
            equip: GameHelper_1.gameHpr.checkChatEquip((_a = param.equip) === null || _a === void 0 ? void 0 : _a.strip()),
            portrayal: GameHelper_1.gameHpr.checkChatPortrayal((_b = param.portrayal) === null || _b === void 0 ? void 0 : _b.strip()),
            pointText: GameHelper_1.gameHpr.checkChatTextIsHasPoint(content),
            replyInfo: GameHelper_1.gameHpr.checkChatHasReplyInfo(param.replyChatInfo),
            time: now,
            wait: true,
            bannedSurplusTime: 0,
            battleInfo: param.battleInfo,
        };
        // 自己是观战者 就需要设置
        if (isSpectate) {
            data.senderNickname = user.getNickname();
            data.senderHeadicon = user.getHeadIcon();
            data.senderTitle = user.getTitle();
        }
        // 先发送到本地
        this.addChat(list, data);
        GameHelper_1.gameHpr.updateChatAllTime(list);
        // 发送到服务器
        this.net.request('game/HD_SendChat', {
            uid: uid, channel: channel, content: content,
            emoji: param.emoji,
            portrayalId: ((_c = param.portrayal) === null || _c === void 0 ? void 0 : _c.id) || 0,
            equipUid: ((_d = param.equip) === null || _d === void 0 ? void 0 : _d.uid) || '',
            battleInfo: param.battleInfo,
            replyUid: (_e = param.replyChatInfo) === null || _e === void 0 ? void 0 : _e.uid
        }).then(function (res) {
            var _a;
            if (res === null || res === void 0 ? void 0 : res.err) {
            }
            else if (!((_a = res === null || res === void 0 ? void 0 : res.data) === null || _a === void 0 ? void 0 : _a.bannedSurplusTime)) {
                _this.bannedStartTime = _this.bannedStartMaxTime = 0;
                storageMgr.remove('chat_banned_time');
            }
            else if (_this.bannedStartTime === 0) {
                _this.bannedStartTime = Date.now();
                _this.bannedStartMaxTime = Math.min(Constant_1.CHAT_BANNED_REST_MAX_TIME, res.data.bannedSurplusTime);
                storageMgr.saveJson('chat_banned_time', { startTime: _this.bannedStartTime, maxTime: _this.bannedStartMaxTime });
                _this.emit(EventType_1.default.UPDATE_SEND_CHAT_BUTTON);
            }
        });
        return 0;
    };
    ChatModel.prototype.addChat = function (list, data) {
        list.unshift(data);
        if (list.length > Constant_1.CHAT_MAX_COUNT) {
            list.pop();
        }
        return data;
    };
    // 获取聊天信息
    ChatModel.prototype.getChatInfoByUID = function (uid) {
        for (var k in this.chatMap) {
            var list = this.chatMap[k];
            var chat = list.find(function (m) { return m.uid === uid; });
            if (chat) {
                return chat;
            }
        }
        return null;
    };
    // 是否军师在联盟频道发言
    ChatModel.prototype.isMilitaryAlliChannelChat = function (channel) {
        /* if (!gameHpr.isRelease) {
            return true
        } else  */ if (!this.alliance.isMeMilitary()) {
            return false;
        }
        channel = channel || this.curLookChannel;
        var uid = this.player.getAllianceUid();
        return channel.startsWith('1_' + uid) || channel.startsWith('4_' + uid);
    };
    // 检测是否发送得太频繁了
    ChatModel.prototype.checkOftenTime = function (isMilitaryAlliChannelChat) {
        if (isMilitaryAlliChannelChat) {
            return false;
        }
        var now = Date.now();
        if (now - this.restStartTime < Constant_1.CHAT_REST_MAX_TIME) {
            return true;
        }
        else if (now - this.lastSendChatTime < Constant_1.CHAT_SEND_INTERVAL) {
            this.tolerateCount += 1;
        }
        else {
            this.tolerateCount = 0;
        }
        // cc.log(now - this.lastSendChatTime, this.tolerateCount)
        if (this.tolerateCount > Constant_1.CHAT_TOLERATE_MAX_COUNT) {
            this.restStartTime = now;
            return true;
        }
        this.restStartTime = 0;
        return false;
    };
    // 检测休息剩余时间
    ChatModel.prototype.checkRestSurplusTime = function (isMilitaryAlliChannelChat) {
        if (this.bannedStartTime > 0) {
            var t = Date.now() - this.bannedStartTime;
            if (t < this.bannedStartMaxTime) {
                return this.bannedStartMaxTime - t;
            }
            this.bannedStartTime = this.bannedStartMaxTime = 0;
            storageMgr.remove('chat_banned_time');
        }
        if (isMilitaryAlliChannelChat || this.restStartTime <= 0) {
            return 0;
        }
        var time = Date.now() - this.restStartTime;
        if (time >= Constant_1.CHAT_REST_MAX_TIME) {
            this.restStartTime = 0;
            return 0;
        }
        return Constant_1.CHAT_REST_MAX_TIME - time;
    };
    ChatModel.prototype.checkChatItemInfo = function (chatInfo, data) {
        chatInfo.equip = GameHelper_1.gameHpr.checkChatEquip(data.equipInfo);
        chatInfo.portrayal = GameHelper_1.gameHpr.checkChatPortrayal(data.portrayalInfo);
        chatInfo.pointText = GameHelper_1.gameHpr.checkChatTextIsHasPoint(data.content);
    };
    ChatModel = __decorate([
        mc.addmodel('chat')
    ], ChatModel);
    return ChatModel;
}(mc.BaseModel));
exports.default = ChatModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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