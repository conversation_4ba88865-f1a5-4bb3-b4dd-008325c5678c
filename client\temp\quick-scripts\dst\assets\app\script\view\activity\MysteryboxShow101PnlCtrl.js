
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/activity/MysteryboxShow101PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '41114vvBSNKNa/UXXkhXTsZ', 'MysteryboxShow101PnlCtrl');
// app/script/view/activity/MysteryboxShow101PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MysteryboxShow101PnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxShow101PnlCtrl, _super);
    function MysteryboxShow101PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.posNode_ = null; // path://root/pos_n
        _this.rootNode_ = null; // path://root/root_nbe_n
        _this.descLbl_ = null; // path://root/desc_l
        //@end
        _this.boxId = 0;
        return _this;
    }
    MysteryboxShow101PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxShow101PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MysteryboxShow101PnlCtrl.prototype.onEnter = function (id) {
        var _this = this;
        this.boxId = id;
        this.rootNode_.children.forEach(function (m) {
            m.Child('di', cc.MultiFrame).setFrame(0);
            m.Child('light').active = false;
            m.Child('val', cc.Sprite).spriteFrame = null;
            m.Child('count').active = false;
            m.Component(cc.Button).interactable = false;
            m.setPosition(0, 0);
            var target = _this.posNode_.Child(m.name).getPosition();
            m.stopAllActions();
            cc.tween(m)
                .to(0.3, { x: target.x, y: target.y }, { easing: cc.easing.sineOut })
                .call(function () { return m.Component(cc.Button).interactable = true; })
                .start();
        });
        this.descLbl_.setLocaleKey('');
        ut.wait(0.4, this).then(function () {
            if (_this.isValid) {
                _this.descLbl_.setLocaleKey('ui.please_select_mb_card');
            }
        });
    };
    MysteryboxShow101PnlCtrl.prototype.onRemove = function () {
    };
    MysteryboxShow101PnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/root_nbe_n
    MysteryboxShow101PnlCtrl.prototype.onClickRoot = function (event, data) {
        audioMgr.playSFX('click');
        this.do(event.target.name);
    };
    // path://close_be_n
    MysteryboxShow101PnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 购买盲盒
    MysteryboxShow101PnlCtrl.prototype.do = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, json, it;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuySkinBlindBox', { id: this.boxId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.descLbl_.setLocaleKey('');
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.setSkinItemList(data.skinItemList);
                        json = assetsMgr.getJsonData('pawnSkin', data.skinId);
                        if (!json) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.UNKNOWN)];
                        }
                        this.rootNode_.children.forEach(function (m) { return m.Component(cc.Button).interactable = false; });
                        it = this.rootNode_.Child(index);
                        it.scaleX = 1;
                        cc.tween(it)
                            .to(0.1, { scaleX: 0.1 })
                            .call(function () {
                            var _a;
                            var isHideCard = json.value === 2;
                            it.Child('di', cc.MultiFrame).setFrame(isHideCard ? 2 : 1);
                            var spr = it.Child('val', cc.Sprite);
                            if (it.Child('light').active = isHideCard) {
                                spr.spriteFrame = null;
                            }
                            else {
                                ResHelper_1.resHelper.loadPawnHeadIcon(data.skinId, spr, _this.key);
                            }
                            var count = ((_a = GameHelper_1.gameHpr.user.getSkinItemList().filter(function (m) { return m.id === data.skinId; })) === null || _a === void 0 ? void 0 : _a.length) || 1;
                            it.Child('count').active = true;
                            it.Child('count/val', cc.Label).string = Math.max(0, count - 1) + '';
                        })
                            .to(0.1, { scaleX: 1 })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxShow101PnlCtrl = __decorate([
        ccclass
    ], MysteryboxShow101PnlCtrl);
    return MysteryboxShow101PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxShow101PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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