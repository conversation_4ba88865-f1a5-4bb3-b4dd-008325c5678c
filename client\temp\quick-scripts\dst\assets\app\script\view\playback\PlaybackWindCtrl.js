
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/playback/PlaybackWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '63405XAMDhEsKnaiGA4N1GR', 'PlaybackWindCtrl');
// app/script/view/playback/PlaybackWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AreaObj_1 = require("../../model/area/AreaObj");
var BuildObj_1 = require("../../model/area/BuildObj");
var MapCellObj_1 = require("../../model/main/MapCellObj");
var BaseBuildCmpt_1 = require("../area/BaseBuildCmpt");
var HPBarCmpt_1 = require("../area/HPBarCmpt");
var PawnCmpt_1 = require("../area/PawnCmpt");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var ccclass = cc._decorator.ccclass;
var PlaybackWindCtrl = /** @class */ (function (_super) {
    __extends(PlaybackWindCtrl, _super);
    function PlaybackWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.skillDiNode_ = null; // path://root/map_n/skill_di_n
        _this.buildNode_ = null; // path://root/map_n/build_n
        _this.roleNode_ = null; // path://root/role_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        //@end
        _this.diNode = null;
        _this.touchCmpt = null;
        _this.model = null;
        _this.areaCenter = null;
        _this.area = null;
        _this.centre = cc.v2();
        _this.preCameraZoomRatio = 0;
        _this.areaSize = cc.v2(); //战场大小
        _this.buildSize = cc.v2(); //建筑区域
        _this.areaActSize = cc.v2(); //战场的实际大小
        _this.borderSize = cc.v2(); //地图边框宽度
        _this.buildOrigin = cc.v2(); //建筑起点
        _this.walls = []; //城墙列表
        _this.areaOutDecorate = []; //装饰
        _this.builds = []; //建筑列表
        _this.pawns = []; //士兵列表
        _this.wallLvNode = null;
        _this.hpBar = null; //血条
        _this.tempSeasonType = 0;
        _this._temp_vec2_0 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this.lastFrameIndex = -1;
        return _this;
    }
    PlaybackWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILDS] = this.onUpdateBuilds, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.ADD_ARMY] = this.onAddArmy, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.REMOVE_ARMY] = this.onRemoveArmy, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.ADD_PAWN] = this.onAddPawn, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.REMOVE_PAWN] = this.onRemovePawn, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.AREA_MAIN_HIT] = this.onAreaMainHit, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.PLAY_FLUTTER_HP] = this.onPlayFlutterHp, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.PLAY_FLUTTER_ANGER] = this.onPlayFlutterAnger, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.PLAY_BULLET_FLY] = this.onPlayBulletFly, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.PLAY_BATTLE_EFFECT] = this.onPlayBattleEffect, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.PLAY_BATTLE_SFX] = this.onPlayBattleSfx, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.PLAY_BATTLE_SCENE_SHAKE] = this.onPlayBattleSceneShake, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.FOCUS_PAWN] = this.onFocusPawn, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.REPLAYBACK] = this.onReplayback, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _r.enter = true, _r),
        ];
    };
    PlaybackWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.model = this.getModel('playback');
                        this.areaCenter = this.getModel('areaCenter');
                        this.diNode = this.mapNode_.FindChild('di');
                        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('playback/PlaybackUI')
                            // await audioMgr.loadByMod('pawn')
                        ];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.onReady = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var data, startInfo, world, land;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        data = this.model.getRecordData();
                        if (!data) {
                            return [2 /*return*/];
                        }
                        startInfo = this.model.launchFrame;
                        world = GameHelper_1.gameHpr.world;
                        this.tempSeasonType = world.getSeasonType();
                        land = ((_a = world.getMapCellByIndex(Math.abs(data.index))) === null || _a === void 0 ? void 0 : _a.landId) || 301;
                        world.setLookCell(new MapCellObj_1.default().init(data.index, land).updateInfo(startInfo));
                        // 设置临时的
                        this.area = new AreaObj_1.default().init(startInfo);
                        this.areaCenter.setLookArea(this.area);
                        // 区域大小
                        this.areaSize.set(this.area.areaSize);
                        this.buildSize.set(this.area.buildSize);
                        // 获取地图边框的宽度 至少都有2格
                        this.area.getBorderSize(this.borderSize);
                        // 重新计算地图的真实大小
                        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize);
                        // 计算建筑的起点
                        this.area.buildOrigin.add(this.borderSize, this.buildOrigin);
                        // 初始化城墙
                        return [4 /*yield*/, this.initWall()];
                    case 1:
                        // 初始化城墙
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.onEnter = function () {
        if (!this.model.launchFrame) {
            return ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.UNKNOWN, { ok: function () { return ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey()); } });
        }
        this.area.setActive(true);
        // 设置中心位置
        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5));
        // 初始化相机位置
        var zr = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO);
        CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr);
        // 刷新地图
        this.updateMap(this.centre.floor());
        //
        this.model.isWatching = true;
        // 开始回放
        this.model.setCurFrameIndex(0);
        this.onReplayback(2, false);
        // 显示UI
        ViewHelper_1.viewHelper.showPnl('playback/PlaybackUI', this.area);
        //
        this.touchCmpt.init();
        //
        GameHelper_1.gameHpr.playAreaBgm(true);
    };
    PlaybackWindCtrl.prototype.onLeave = function () {
        var _a;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO, CameraCtrl_1.cameraCtrl.zoomRatio);
        this.touchCmpt.clean();
        GameHelper_1.gameHpr.world.setLookCell(null);
        this.areaCenter.setLookArea(null);
        (_a = this.area) === null || _a === void 0 ? void 0 : _a.clean();
        this.area = null;
        this.cleanWalls();
        this.cleanAreaOutDecorate();
        this.cheanBuilds();
        while (this.pawns.length > 0) {
            this.pawns.pop().clean();
        }
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        AnimHelper_1.animHelper.clean();
        assetsMgr.releaseTempResByTag(this.key);
        audioMgr.releaseByMod('pawn');
        this.model.clean();
    };
    PlaybackWindCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新城市
    PlaybackWindCtrl.prototype.onUpdateBuilds = function (index) {
        var _a;
        if (this.model.isSimulating)
            return;
        if (index === ((_a = this.area) === null || _a === void 0 ? void 0 : _a.index)) {
            this.initBuilds();
        }
    };
    // 添加军队
    PlaybackWindCtrl.prototype.onAddArmy = function (data) {
        var _this = this;
        if (this.model.isSimulating)
            return;
        if (data.aIndex === this.area.index) {
            data.pawns.forEach(function (m) { return _this.createPawn(m); });
        }
    };
    // 删除军队
    PlaybackWindCtrl.prototype.onRemoveArmy = function (data) {
        var _this = this;
        if (this.model.isSimulating)
            return;
        if (data.aIndex === this.area.index) {
            data.pawns.forEach(function (m) { var _a; return (_a = _this.pawns.remove('uid', m.uid)) === null || _a === void 0 ? void 0 : _a.clean(true); });
        }
    };
    // 更新军队信息
    PlaybackWindCtrl.prototype.onUpdateArmy = function (data) {
        var _this = this;
        var _a;
        if (this.model.isSimulating)
            return;
        if (data.aIndex !== this.area.index) {
            return;
        }
        // 先删除没有的
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            var m = this.pawns[i];
            if (((_a = m.data) === null || _a === void 0 ? void 0 : _a.armyUid) !== data.uid) {
                continue;
            }
            else if (!data.pawns.has('uid', m.uid)) {
                m.clean(true);
                this.pawns.splice(i, 1);
            }
        }
        data.pawns.forEach(function (m) { return _this.createPawn(m); });
    };
    // 添加士兵
    PlaybackWindCtrl.prototype.onAddPawn = function (index, data) {
        if (this.model.isSimulating)
            return;
        if (this.area.index === index) {
            this.createPawn(data);
        }
    };
    // 删除士兵
    PlaybackWindCtrl.prototype.onRemovePawn = function (index, uid) {
        var _a;
        if (this.model.isSimulating)
            return;
        if (this.area.index === index) {
            (_a = this.pawns.remove('uid', uid)) === null || _a === void 0 ? void 0 : _a.clean(true);
        }
    };
    // 受到伤害
    PlaybackWindCtrl.prototype.onAreaMainHit = function (data) {
        var _a;
        if (this.model.isSimulating)
            return;
        if (data.index === this.area.index) {
            if (this.area.isBattleing()) {
                (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.play();
            }
            AnimHelper_1.animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key);
        }
    };
    // 播放受击伤害
    PlaybackWindCtrl.prototype.onPlayFlutterHp = function (data) {
        var _a, _b;
        if (this.model.isSimulating)
            return;
        if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) == data.index) {
            var pos = data.point ? this.getPixelByPoint(data.point).clone() : (_b = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.getPosition();
            if (pos) {
                AnimHelper_1.animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key);
            }
        }
    };
    // 播放增加怒气
    PlaybackWindCtrl.prototype.onPlayFlutterAnger = function (data) {
        var _a;
        if (this.model.isSimulating)
            return;
        if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
            if (pawn) {
                AnimHelper_1.animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key);
            }
        }
    };
    // 播放子弹飞行
    PlaybackWindCtrl.prototype.onPlayBulletFly = function (data) {
        if (this.model.isSimulating)
            return;
        if (this.area.index === data.index) {
            data.startPos = this.getPixelByPoint(data.startPoint).clone();
            data.targetPos = this.getPixelByPoint(data.targetPoint).clone();
            AnimHelper_1.animHelper.playBulletFly(data, this.topLayerNode_, this.key);
        }
    };
    // 播放战斗特效
    PlaybackWindCtrl.prototype.onPlayBattleEffect = function (data) {
        var _a;
        if (this.model.isSimulating) {
            return;
        }
        else if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.pos = this.getPixelByPoint(data.point).clone();
            var root = this.skillDiNode_;
            if (data.root === 'top') {
                root = this.topLayerNode_;
            }
            else if (data.root === 'role') {
                root = this.roleNode_;
                data.zIndex = (Constant_1.AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 3;
            }
            AnimHelper_1.animHelper.playBattleEffect(data, root, this.key);
        }
    };
    // 播放音效
    PlaybackWindCtrl.prototype.onPlayBattleSfx = function (index, url, data) {
        var _a;
        if (this.model.isSimulating) {
            return;
        }
        else if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) === index) {
            audioMgr.playSFX(url, data);
        }
    };
    // 播放屏幕抖动
    PlaybackWindCtrl.prototype.onPlayBattleSceneShake = function (index, time) {
        var _a;
        if (this.model.isSimulating) {
            return;
        }
        else if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) === index) {
            CameraCtrl_1.cameraCtrl.shake(time);
        }
    };
    // 聚焦士兵
    PlaybackWindCtrl.prototype.onFocusPawn = function (data) {
        var _a;
        if (((_a = this.area) === null || _a === void 0 ? void 0 : _a.index) !== (data === null || data === void 0 ? void 0 : data.index)) {
            return;
        }
        else if (CameraCtrl_1.cameraCtrl.getNoDragTime() < 5000) {
            return; //多久没拖动才可以聚焦
        }
        var pos = this.getPixelByPoint(data.point);
        if (CameraCtrl_1.cameraCtrl.isInScreenRangeByWorld(pos)) {
            CameraCtrl_1.cameraCtrl.moveTo(0.5, pos, true);
        }
    };
    // 重新回放
    PlaybackWindCtrl.prototype.onReplayback = function (mulIndex, isPaused) {
        var _a, _b, _c;
        if (mulIndex === void 0) { mulIndex = 2; }
        if (isPaused === void 0) { isPaused = false; }
        return __awaiter(this, void 0, void 0, function () {
            var fsp, startInfo, snapshoot, targetFrame, backFrame, curFrame, snapshoot, snapshoot1, snapshoot2, TIMES_LIMITS, timeLimits, idx, snapshoot, frameIdx;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0: return [4 /*yield*/, ut.lock('replayback')];
                    case 1:
                        _d.sent();
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        fsp = this.area.getFspModel();
                        if (!fsp) {
                            startInfo = this.model.launchFrame;
                            this.area.initBattleData(startInfo);
                            fsp = this.area.battleLocalBegin(startInfo, this.model.inputFrames, mulIndex, this.model.serverPlayback);
                            snapshoot = this.model.getSnapshoot(0);
                            if (!snapshoot) {
                                snapshoot = this.area.strip();
                                // snapshoot.battle = startInfo
                                this.model.appendSnapshoot(0, snapshoot);
                            }
                        }
                        fsp.setPause(true);
                        targetFrame = this.model.curFrame;
                        backFrame = targetFrame;
                        while (!this.model.hasSnapshoot(backFrame) && backFrame > 0) {
                            backFrame--;
                        }
                        curFrame = fsp.getCurrentFrameIndex();
                        if (curFrame === targetFrame && curFrame !== 0) {
                            fsp.setSpeedMulIndex(mulIndex);
                            fsp.setPause(isPaused);
                            ViewHelper_1.viewHelper.showLoadingWait(false);
                            this.emit(EventType_1.default.PLAYBACK_LOAD_END);
                            this.model.isSimulating = false;
                            ut.unlock('replayback');
                            return [2 /*return*/];
                        }
                        // 进度
                        if (backFrame > curFrame || curFrame > targetFrame) {
                            snapshoot = this.model.getSnapshoot(backFrame);
                            fsp.resyncLocal(snapshoot);
                            if (CC_PREVIEW) {
                                snapshoot1 = this.area.strip();
                                snapshoot2 = this.model.getSnapshootExist(snapshoot.currentFrameIndex);
                                if (!deepEqual(snapshoot1, snapshoot2, ['builds'])) {
                                    cc.log('resync', snapshoot1, snapshoot2);
                                }
                            }
                        }
                        TIMES_LIMITS = 3000;
                        timeLimits = TIMES_LIMITS;
                        // cc.log('FROM', backFrame, targetFrame)
                        this.model.isSimulating = true;
                        _d.label = 2;
                    case 2:
                        if (!(fsp.getCurrentFrameIndex() < targetFrame)) return [3 /*break*/, 5];
                        fsp.executeOneFrameForSimulate();
                        idx = fsp.getCurrentFrameIndex();
                        (_b = (_a = this.model).lateStep) === null || _b === void 0 ? void 0 : _b.call(_a, idx, this.area);
                        if (!this.model.hasSnapshoot(idx) && fsp.recordEnable()) {
                            snapshoot = this.area.strip();
                            this.model.appendSnapshoot(fsp.getCurrentFrameIndex(), snapshoot);
                        }
                        if (!(timeLimits-- < 0)) return [3 /*break*/, 4];
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 3:
                        _d.sent();
                        timeLimits = TIMES_LIMITS;
                        _d.label = 4;
                    case 4: return [3 /*break*/, 2];
                    case 5:
                        this.model.isSimulating = false;
                        if (!this.area.isBattleing()) {
                            ViewHelper_1.viewHelper.showLoadingWait(false);
                            this.emit(EventType_1.default.PLAYBACK_LOAD_END);
                            return [2 /*return*/, ut.unlock('replayback')];
                        }
                        frameIdx = fsp.getCurrentFrameIndex();
                        this.model.setCurFrameIndex(frameIdx);
                        // 绘制建筑
                        this.initBuilds();
                        // 绘制士兵
                        this.initPawns();
                        // 血条刷新
                        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.init(this.area);
                        fsp.setSpeedMulIndex(mulIndex);
                        fsp.setPause(isPaused);
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        this.emit(EventType_1.default.PLAYBACK_LOAD_END);
                        ut.unlock('replayback');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 战斗结束建筑修改
    PlaybackWindCtrl.prototype.onAreaBattleEnd = function (index, isWin) {
        if (isWin && index === this.area.index && !this.area.isMainCity()) {
            this.cheanBuilds();
            this.createBuild(new BuildObj_1.default().init(this.area.index, ut.UID(), cc.v2(), Constant_1.BUILD_TOWER_NID, 1));
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PlaybackWindCtrl.prototype.isActive = function () { return this.isValid && !!this.area; };
    PlaybackWindCtrl.prototype.getPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3));
    };
    // 初始化城墙
    PlaybackWindCtrl.prototype.initWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanWalls();
                        if (!!this.area.isBoss()) return [3 /*break*/, 5];
                        return [4 /*yield*/, assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, this.roleNode_);
                        this.hpBar = node.addComponent(HPBarCmpt_1.default).init(this.area);
                        if (!this.area.wall) return [3 /*break*/, 4];
                        // 初始化墙
                        return [4 /*yield*/, this.createWall()
                            // 加载一个墙的等级
                        ];
                    case 2:
                        // 初始化墙
                        _a.sent();
                        if (!(this.area.wall.lv > 0)) return [3 /*break*/, 4];
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)];
                    case 3:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_);
                            this.updateWallLv(this.area.wall.lv);
                        }
                        _a.label = 4;
                    case 4:
                        this.updateWallHpPosition();
                        _a.label = 5;
                    case 5: 
                    // 创建区域外的装饰
                    return [4 /*yield*/, this.createAreaOutDecorate()];
                    case 6:
                        // 创建区域外的装饰
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.createWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var skinId;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        while (this.walls.length > 0) {
                            this.walls.pop().destroy();
                        }
                        this.walls.length = 0;
                        skinId = 0;
                        return [4 /*yield*/, Promise.all(this.area.walls.map(function (m) { return __awaiter(_this, void 0, void 0, function () {
                                var url, pfb, node;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            url = "wall/" + skinId + "/WALL_" + skinId + "_" + m.type + "_" + m.dir;
                                            if (m.index) {
                                                url += '_' + m.index;
                                            }
                                            return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                                        case 1:
                                            pfb = _a.sent();
                                            if (pfb && this.isValid) {
                                                node = cc.instantiate2(pfb, this.buildNode_);
                                                node.Data = this.area.wall;
                                                node.setPosition(MapHelper_1.mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)));
                                                node.addComponent(ClickTouchCmpt_1.default).on(this.onClickWall, this);
                                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                                this.walls.push(node);
                                            }
                                            return [2 /*return*/];
                                    }
                                });
                            }); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.cleanWalls = function () {
        var _a, _b;
        (_a = this.wallLvNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.wallLvNode = null;
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.clean();
        this.hpBar = null;
        while (this.walls.length > 0) {
            this.walls.pop().destroy();
        }
        this.walls.length = 0;
    };
    // 创建区域外的装饰
    PlaybackWindCtrl.prototype.createAreaOutDecorate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var seasonType, cell, drawType, url, pfb, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanAreaOutDecorate();
                        seasonType = this.tempSeasonType;
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.area.index), drawType = cell.getLandDrawType();
                        url = "area_decorate/" + seasonType + "/AREA_DECORATE_" + drawType;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = cc.instantiate2(pfb, this.mapNode_.Child('bg'));
                            node.setPosition(this.borderSize.x * Constant_1.TILE_SIZE, this.borderSize.y * Constant_1.TILE_SIZE);
                            this.areaOutDecorate.push(node);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.cleanAreaOutDecorate = function () {
        while (this.areaOutDecorate.length > 0) {
            this.areaOutDecorate.pop().destroy();
        }
        this.areaOutDecorate.length = 0;
    };
    // 初始化建筑
    PlaybackWindCtrl.prototype.initBuilds = function () {
        return __awaiter(this, void 0, void 0, function () {
            var builds, uidMap, i;
            var _this = this;
            return __generator(this, function (_a) {
                builds = this.area.builds, uidMap = {};
                builds.forEach(function (m) { return uidMap[m.uid] = true; });
                for (i = this.builds.length - 1; i >= 0; i--) {
                    if (!uidMap[this.builds[i].uid]) {
                        this.builds.splice(i, 1)[0].clean();
                    }
                }
                return [2 /*return*/, Promise.all(builds.map(function (m) { return _this.createBuild(m); }))];
            });
        });
    };
    PlaybackWindCtrl.prototype.createBuild = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var build, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        build = this.builds.find(function (m) { return m.uid === data.uid; });
                        if (build) {
                            return [2 /*return*/, build.resync(data, this.area.owner)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        build = cc.instantiate2(pfb, this.buildNode_).getComponent(BaseBuildCmpt_1.default).init(data, this.buildOrigin, this.borderSize.y * Constant_1.TILE_SIZE, this.area.owner);
                        this.builds.push(build);
                        return [2 /*return*/, build];
                }
            });
        });
    };
    PlaybackWindCtrl.prototype.cheanBuilds = function () {
        while (this.builds.length > 0) {
            this.builds.pop().clean();
        }
    };
    // 刷新墙的等级
    PlaybackWindCtrl.prototype.updateWallLv = function (lv) {
        if (this.wallLvNode) {
            this.wallLvNode.Child('val', cc.Label).string = '' + lv;
        }
    };
    // 刷新血条位置
    PlaybackWindCtrl.prototype.updateWallHpPosition = function () {
        if (this.hpBar) {
            var node = this.hpBar.node;
            if (this.area.cityId === Constant_1.CITY_MAIN_NID) {
                var pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else if (this.area.isAncient()) {
                var pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else {
                var pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
                node.setPosition(pos.x, pos.y + 47);
            }
            node.zIndex = (Constant_1.AREA_MAX_ZINDEX - (node.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10;
        }
        if (this.wallLvNode) {
            var pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
            this.wallLvNode.setPosition(pos.x, pos.y + 16);
            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1;
        }
    };
    // 初始化士兵
    PlaybackWindCtrl.prototype.initPawns = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawns, uidMap, i;
            var _this = this;
            return __generator(this, function (_a) {
                pawns = this.area.getAllPawns(), uidMap = {};
                pawns.forEach(function (m) {
                    if (m.id === 3601 && m.curHp === 0) {
                        // 战旗死了不知为何还在队列中
                        return;
                    }
                    uidMap[m.uid] = true;
                });
                for (i = this.pawns.length - 1; i >= 0; i--) {
                    if (!uidMap[this.pawns[i].uid]) {
                        this.pawns.splice(i, 1)[0].clean();
                    }
                }
                return [2 /*return*/, Promise.all(pawns.map(function (m) { return _this.createPawn(m); }))];
            });
        });
    };
    PlaybackWindCtrl.prototype.createPawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
                        if (pawn) {
                            return [2 /*return*/, pawn.resync(data, true)];
                        }
                        else if (data.id === 3601 && data.curHp === 0) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.pawns.find(function (m) { return m.uid === data.uid; })) {
                            return [2 /*return*/, null];
                        }
                        else if (data.getState() === Enums_1.PawnState.NONE) {
                            data.changeState(Enums_1.PawnState.STAND); //这里直接切换到战斗状态
                        }
                        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt_1.default).init(data, this.borderSize, this.key);
                        this.pawns.push(pawn);
                        return [2 /*return*/, pawn];
                }
            });
        });
    };
    // 绘制地图
    PlaybackWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var seasonType = this.tempSeasonType;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.area.index), seasonColorConf = Constant_1.AREA_DI_COLOR_CONF[seasonType];
        var colorConf = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0];
        var areaSize = this.area.areaSize, oYindex = (areaSize.x + 1) % 2;
        // 设置整个背景颜色
        CameraCtrl_1.cameraCtrl.setBgColor(colorConf.bg);
        var buildOrigin = this.area.buildOrigin, buildSize = this.area.buildSize;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, GameHelper_1.gameHpr.world.getMaxTileRange());
        var isBoss = this.area.isBoss();
        this.diNode.Items(points, function (it, point, i) {
            var x = point.x - _this.borderSize.x, y = point.y - _this.borderSize.y;
            var bx = x - buildOrigin.x, by = y - buildOrigin.y;
            var index = it.Data = MapHelper_1.mapHelper.pointToIndexByNumer(x, y, areaSize);
            it.setPosition(MapHelper_1.mapHelper.getPixelByPoint(point, _this._temp_vec2_0));
            it.Color('#FFFFFF');
            if (MapHelper_1.mapHelper.isBorder(x, y, areaSize)) { //边界外
                it.Component(cc.Sprite).spriteFrame = null;
            }
            else if (MapHelper_1.mapHelper.isBorder(bx, by, buildSize)) { //战斗区域
                var idx = y % 2 === 0 ? index : index + oYindex;
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                it.Color(colorConf.battle[Number(idx % 2 !== 0)]);
            }
            else if (cell.isMainCity()) { //主城区域
                if (bx === 3 && by === 3) {
                    it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_main');
                }
                else {
                    it.Component(cc.Sprite).spriteFrame = null;
                }
            }
            else if (!isBoss) { //建筑区域
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {
                    it.Color(colorConf.build);
                }
                else if (cell.isAncient()) {
                    it.Color('#D6DBAA');
                }
            }
            else if (bx === 1 && by === 1) { //boss位置
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('land_area_boss_' + cell.landType);
            }
            else {
                it.Component(cc.Sprite).spriteFrame = null;
            }
        });
    };
    PlaybackWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandItemIcon(icon, this.tempSeasonType);
    };
    // 点击墙
    PlaybackWindCtrl.prototype.onClickWall = function () {
        if (!this.area.wall) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.area.isAncient()) {
            var build = this.area.getBuildById(this.area.cityId);
            if (build) {
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildCity', this.area.wall);
        }
    };
    PlaybackWindCtrl.prototype.update = function (dt) {
        var _a, _b;
        if (!this.area) {
            return;
        }
        // 刷新帧
        this.area.update(dt);
        // 检测是否需要填充地图
        // this.checkUpdateMap()
        var fsp = this.area.getFspModel();
        if (!fsp || !fsp.isRunning)
            return;
        var curFrame = fsp.getCurrentFrameIndex();
        if (curFrame === this.lastFrameIndex)
            return;
        (_b = (_a = this.model).lateStep) === null || _b === void 0 ? void 0 : _b.call(_a, curFrame, this.area);
        this.model.setCurFrameIndex(curFrame);
        this.lastFrameIndex = curFrame;
    };
    PlaybackWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_2);
        if (!this.centre.equals(point) || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
        }
    };
    PlaybackWindCtrl = __decorate([
        ccclass
    ], PlaybackWindCtrl);
    return PlaybackWindCtrl;
}(mc.BaseWindCtrl));
exports.default = PlaybackWindCtrl;
function deepEqual(o1, o2, ignoreKeys) {
    if (ignoreKeys === void 0) { ignoreKeys = []; }
    if (o1 === o2)
        return true;
    if (typeof o1 !== typeof o2)
        return false;
    if (typeof o1 !== 'object')
        return o1 === o2;
    if (o1 === null || o2 === null)
        return o1 === o2;
    if (Array.isArray(o1)) {
        if (o1.length !== o2.length)
            return false;
        for (var i = 0; i < o1.length; i++) {
            if (!deepEqual(o1[i], o2[i], ignoreKeys))
                return false;
        }
        return true;
    }
    var keys1 = Object.keys(o1), keys2 = Object.keys(o2);
    if (keys1.length !== keys2.length)
        return false;
    for (var i = 0; i < keys1.length; i++) {
        var key = keys1[i];
        if (ignoreKeys.includes(key))
            continue;
        if (!deepEqual(o1[key], o2[key], ignoreKeys))
            return false;
    }
    return true;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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