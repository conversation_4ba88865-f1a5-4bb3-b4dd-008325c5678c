
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/ModifyFriendNotePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc11cfvgCtCK6XngqD3Kw0I', 'ModifyFriendNotePnlCtrl');
// app/script/view/menu/ModifyFriendNotePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ModifyFriendNotePnlCtrl = /** @class */ (function (_super) {
    __extends(ModifyFriendNotePnlCtrl, _super);
    function ModifyFriendNotePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/input_eb
        //@end
        _this.data = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    ModifyFriendNotePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ModifyFriendNotePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ModifyFriendNotePnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        this.inputEb_.string = data.noteName;
    };
    ModifyFriendNotePnlCtrl.prototype.onRemove = function () {
        this.data = null;
    };
    ModifyFriendNotePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    ModifyFriendNotePnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var name = this.inputEb_.string.trim();
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_nickname');
        }
        else if (ut.getStringLen(name) > 8) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
        }
        GameHelper_1.gameHpr.friend.modifyFriendNote(this.data.uid, name).then(function (err) {
            if (err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
            }
            else if (err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_content');
            }
            else if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    ModifyFriendNotePnlCtrl = __decorate([
        ccclass
    ], ModifyFriendNotePnlCtrl);
    return ModifyFriendNotePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ModifyFriendNotePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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