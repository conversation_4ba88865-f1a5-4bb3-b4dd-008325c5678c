
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/BookRatingPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '49b4db0PQVCYIo41HzI27/o', 'BookRatingPnlCtrl');
// app/script/view/menu/BookRatingPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BookRatingPnlCtrl = /** @class */ (function (_super) {
    __extends(BookRatingPnlCtrl, _super);
    function BookRatingPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconNode_ = null; // path://root/icon_n
        _this.listSv_ = null; // path://root/list_sv
        _this.loadingNode_ = null; // path://root/list_sv/loading_n
        _this.editRatingNode_ = null; // path://root/edit_rating_be_n
        //@end
        _this.model = null;
        _this.curSelectVersion = '';
        _this.bookType = Enums_1.BookCommentType.NONE;
        _this.bookId = 0;
        _this.bookKey = '';
        _this.tempPraise = {}; //临时记录用于关闭界面时同步服务器
        return _this;
    }
    BookRatingPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.INIT_BOOK_COMMENT_COMPLETE] = this.onInitBookCommentComplete, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_BOOK_COMMENT] = this.onUpdateBookComment, _b.enter = true, _b),
        ];
    };
    BookRatingPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('book');
                return [2 /*return*/];
            });
        });
    };
    BookRatingPnlCtrl.prototype.onEnter = function (type, id) {
        this.bookType = type;
        this.bookId = id;
        this.bookKey = type + '_' + id;
        var icon = this.iconNode_.Child('val'), isRole = type === Enums_1.BookCommentType.PAWN || type === Enums_1.BookCommentType.PORTRAYAL;
        icon.anchorY = isRole ? 0 : 0.5;
        icon.y = isRole ? -36 : 0;
        if (type === Enums_1.BookCommentType.POLICY) {
            ResHelper_1.resHelper.loadPolicyIcon(id, icon, this.key);
            this.iconNode_.Child('name').setLocaleKey('policyText.name_' + id);
        }
        else if (type === Enums_1.BookCommentType.PAWN) {
            ResHelper_1.resHelper.loadPawnHeadIcon(id, icon, this.key);
            this.iconNode_.Child('name').setLocaleKey('pawnText.name_' + id);
        }
        else if (type === Enums_1.BookCommentType.EQUIP) {
            ResHelper_1.resHelper.loadEquipIcon(id, icon, this.key);
            this.iconNode_.Child('name').setLocaleKey('equipText.name_' + id);
        }
        else if (type === Enums_1.BookCommentType.PORTRAYAL) {
            ResHelper_1.resHelper.loadPawnHeadIcon(id, icon, this.key);
            this.iconNode_.Child('name').setLocaleKey('portrayalText.name_' + id);
        }
        this.updateStar();
        this.curSelectVersion = '';
        this.updateVersion();
    };
    BookRatingPnlCtrl.prototype.onRemove = function () {
        // 同步点赞
        var praises = {};
        for (var uid in this.tempPraise) {
            var _a = this.tempPraise[uid], oldPraise = _a.oldPraise, newPraise = _a.newPraise;
            if (oldPraise !== newPraise) {
                praises[uid] = newPraise + 2;
            }
        }
        this.tempPraise = {};
        if (!ut.isEmptyObject(praises)) {
            GameHelper_1.gameHpr.net.send('chat/HD_GalleryCommentPraise', { type: this.bookType, id: this.bookId, praises: praises });
        }
    };
    BookRatingPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/bottom/praise_nbe
    BookRatingPnlCtrl.prototype.onClickPraise = function (event, _) {
        audioMgr.playSFX('click');
        var it = event.target.parent.parent.parent;
        var data = it.Data;
        var index = Number(event.target.name);
        if (!data) {
            return;
        }
        var temp = this.tempPraise[data.uid];
        if (!temp) {
            temp = this.tempPraise[data.uid] = { oldPraise: data.mePraise, newPraise: data.mePraise };
        }
        // 先减掉之前的
        if (data.mePraise !== -1) {
            data.praise[data.mePraise] -= 1;
        }
        // 重新加
        if (data.mePraise !== index) {
            data.praise[index] += 1;
            data.mePraise = index;
        }
        else {
            data.mePraise = -1;
        }
        temp.newPraise = data.mePraise;
        this.updatePraise(it.Child('bottom/praise_nbe'), data);
    };
    // path://root/edit_rating_be_n
    BookRatingPnlCtrl.prototype.onClickEditRating = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/BookComment', this.bookType, this.bookId);
    };
    // path://root/list_sv/view/content/item/bottom/layout/translate_be
    BookRatingPnlCtrl.prototype.onClickTranslate = function (event, data) {
        cc.log('onClickTranslate', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 初始化完成
    BookRatingPnlCtrl.prototype.onInitBookCommentComplete = function (key) {
        if (this.bookKey === key) {
            this.updateStar();
            this.updateVersion();
        }
    };
    // 刷新评价
    BookRatingPnlCtrl.prototype.onUpdateBookComment = function (key) {
        if (this.bookKey === key) {
            this.updateStar();
            this.updateCommentList(true);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新心
    BookRatingPnlCtrl.prototype.updateStar = function () {
        var bookInfoMap = this.model.checkInitBookStarMap() || {}, info = bookInfoMap[this.bookKey];
        ViewHelper_1.viewHelper.updateBookStar(this.iconNode_.Child('rating'), (info === null || info === void 0 ? void 0 : info.star) || 0);
        this.iconNode_.Child('count', cc.Label).string = info ? '(' + info.commentCount + ')' : '(0)';
    };
    BookRatingPnlCtrl.prototype.updateVersion = function () {
        this.updateCommentList(true);
    };
    // 刷新评论列表
    BookRatingPnlCtrl.prototype.updateCommentList = function (init) {
        var _this = this;
        if (init) {
            var versionMap = this.model.checkInitComment(this.bookType, this.bookId);
            this.loadingNode_.active = !versionMap;
            this.editRatingNode_.Component(cc.Button).interactable = !!versionMap;
        }
        var list = this.model.getCommentListByVersion(this.bookKey, this.curSelectVersion);
        list.sort(function (a, b) {
            var aw = (a.praise[0] || 0) - (a.praise[1] || 0);
            var bw = (b.praise[0] || 0) - (b.praise[1] || 0);
            if (aw === bw) {
                return b.time - a.time;
            }
            return bw - aw;
        });
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Child('empty').active = !list.length;
        this.listSv_.Items(list, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val'), data.headIcon, _this.key);
            it.Child('head/name', cc.Label).string = data.nickname;
            ViewHelper_1.viewHelper.updateBookStar(it.Child('head/rating'), data.star * 2);
            it.Child('content', cc.Label).string = _this.checkContent(data.content);
            it.Child('bottom/layout/time', cc.Label).string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.time);
            var modify = it.Child('modify');
            if (modify.active = !!data.modifyCount) {
                modify.setLocaleKey('ui.modify_comment_count_desc', data.modifyCount);
            }
            _this.updatePraise(it.Child('bottom/praise_nbe'), data);
        });
    };
    BookRatingPnlCtrl.prototype.checkContent = function (content) {
        var arr = content.split('\n');
        if (arr.length > 6) {
            arr.length = 6;
            return arr.join('\n');
        }
        return content;
    };
    BookRatingPnlCtrl.prototype.updatePraise = function (node, data) {
        node.children.forEach(function (m, i) {
            m.Child('count', cc.Label).string = '' + Math.max(0, data.praise[i] || 0);
            m.Child('val', cc.MultiFrame).setFrame(data.mePraise === i);
        });
    };
    BookRatingPnlCtrl = __decorate([
        ccclass
    ], BookRatingPnlCtrl);
    return BookRatingPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BookRatingPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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