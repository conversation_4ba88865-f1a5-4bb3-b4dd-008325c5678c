
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/MessageCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a313bxFZlhNj5XTNzCj+zP6', 'MessageCmpt');
// app/script/view/common/MessageCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var MessageCmpt = /** @class */ (function (_super) {
    __extends(MessageCmpt, _super);
    function MessageCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.MSG_MAX_COUNT = 5; //消息最多个数
        _this.OUT_OFFSET_X = 16; //在外面初始x的偏移
        _this.SPACING_Y = 60; //间隙
        _this.item = null;
        _this.itemPool = []; //消息节点池子
        _this.items = []; //当前显示的消息节点
        _this.sfxPlayTime = 0;
        return _this;
    }
    MessageCmpt.prototype.onLoad = function () {
        this.item = this.FindChild('item');
        this.item.parent = null;
        this.itemPool.push(this.item);
    };
    MessageCmpt.prototype.onDestroy = function () {
        while (this.itemPool.length > 0) {
            this.itemPool.pop().destroy();
        }
        ut.destroyNode(this.item);
        this.item = null;
    };
    MessageCmpt.prototype.init = function () {
        var _this = this;
        this.clean();
        GameHelper_1.gameHpr.message.getMessages().forEach(function (m) {
            if (!m.delay) {
                _this.show(m);
            }
        });
        eventCenter.off(EventType_1.default.ADD_MESSAGE, this.onAddMessage, this);
        eventCenter.off(EventType_1.default.DEL_MESSAGE, this.onDelMessage, this);
        eventCenter.on(EventType_1.default.ADD_MESSAGE, this.onAddMessage, this);
        eventCenter.on(EventType_1.default.DEL_MESSAGE, this.onDelMessage, this);
    };
    MessageCmpt.prototype.clean = function () {
        while (this.items.length > 0) {
            this.putItemOne(this.items.pop());
        }
        this.node.removeAllChildren();
    };
    // 添加一个消息
    MessageCmpt.prototype.onAddMessage = function (msg) {
        this.show(msg);
    };
    // 删除一个消息
    MessageCmpt.prototype.onDelMessage = function (msg) {
        this.hide(msg.uid);
    };
    MessageCmpt.prototype.getItem = function () {
        var node = this.itemPool.pop() || cc.instantiate(this.item);
        node.parent = this.node;
        node.active = true;
        node.stopAllActions();
        node.opacity = 255;
        node.scaleY = 1;
        return node;
    };
    MessageCmpt.prototype.putItem = function (node) {
        var item = this.items[node.Data.i];
        if (item) {
            this.putItemOne(node);
        }
    };
    MessageCmpt.prototype.putItemOne = function (node) {
        node.parent = null;
        node.Data = null;
        node.Child('bg').off('click', this.onClickMsg, this);
        this.itemPool.push(node);
    };
    MessageCmpt.prototype.onClickMsg = function (event) {
        var uid = event.target.parent.Data;
        if (uid) {
            GameHelper_1.gameHpr.message.remove(uid);
            this.hide(uid);
        }
    };
    // 显示一条信息
    MessageCmpt.prototype.show = function (msg) {
        if (!this.sfxPlayTime || (Date.now() - this.sfxPlayTime) > 1500) {
            audioMgr.playSFX('common/sound_ui_010');
            this.sfxPlayTime = Date.now();
        }
        var it = this.getItem();
        it.Data = msg.uid;
        var lable = it.Child('val'), bg = it.Child('bg');
        lable.setLocaleKey.apply(lable, __spread([msg.key], msg.params));
        var w = cc.misc.clampf(lable.width * lable.scaleX + 32, 200, 400);
        var cnt = this.items.length;
        it.setPosition(-(w + this.OUT_OFFSET_X), (cnt >= this.MSG_MAX_COUNT ? cnt - 1 : cnt) * this.SPACING_Y);
        bg.on('click', this.onClickMsg, this);
        // 出来
        this.moveIn(it);
        // 刷新其他的
        if (this.items.length >= this.MSG_MAX_COUNT) {
            var a = this.items.shift();
            GameHelper_1.gameHpr.message.remove(a.Data);
            // 第一个移除去
            this.moveOut(a);
            // 其他全部向上
            this.updateMsg();
        }
        this.items.push(it);
    };
    // 隐藏一条信息
    MessageCmpt.prototype.hide = function (uid) {
        var it = this.items.delete(function (m) { return m.Data === uid; })[0];
        if (it) {
            // 先移除去
            this.moveOut(it);
            // 把剩下的往上移动
            this.updateMsg();
        }
    };
    // 进来
    MessageCmpt.prototype.moveIn = function (it) {
        it.stopAllActions();
        cc.tween(it).to(0.2, { x: 0 }, { easing: cc.easing.sineOut }).start();
    };
    // 出去
    MessageCmpt.prototype.moveOut = function (it) {
        var _this = this;
        it.stopAllActions();
        var outX = -(it.Child('bg').width + this.OUT_OFFSET_X);
        cc.tween(it).to(0.2, { x: outX, opacity: 0 }, { easing: cc.easing.sineIn }).call(function () { return _this.putItem(it); }).start();
    };
    // 向上移动
    MessageCmpt.prototype.moveY = function (it, i) {
        var endY = i * this.SPACING_Y;
        if (it.y !== endY) {
            it.stopAllActions();
            cc.tween(it).to(0.2, { y: endY }).start();
        }
    };
    MessageCmpt.prototype.updateMsg = function () {
        var _this = this;
        this.items.forEach(function (m, i) { return _this.moveY(m, i); });
    };
    MessageCmpt = __decorate([
        ccclass
    ], MessageCmpt);
    return MessageCmpt;
}(cc.Component));
exports.default = MessageCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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