
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BaseBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2721aJDkD1J0bjlAQDgFp+H', 'BaseBuildCmpt');
// app/script/view/area/BaseBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var MapHelper_1 = require("../../common/helper/MapHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 建筑
var BaseBuildCmpt = /** @class */ (function (_super) {
    __extends(BaseBuildCmpt, _super);
    function BaseBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.data = null;
        _this.owner = '';
        _this.body = null;
        _this.origin = cc.v2(); //起点
        _this.originY = 0; //实际地图的七点 像素
        _this.tempBodyPosition = cc.v2();
        _this.tempIndex = 0;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_position = cc.v2();
        return _this;
    }
    BaseBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        this.data = data;
        this.origin.set(origin);
        this.originY = originY;
        this.owner = owner;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        return this;
    };
    BaseBuildCmpt.prototype.clean = function () {
    };
    Object.defineProperty(BaseBuildCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseBuildCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    BaseBuildCmpt.prototype.getBody = function () { return this.body; };
    BaseBuildCmpt.prototype.getAnimTarget = function () { return this.body.Child('val') || this.body; }; //获取用于播放动画的节点
    BaseBuildCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    BaseBuildCmpt.prototype.getBodyOffsetTopPosition = function (y) {
        if (y === void 0) { y = 0; }
        var pos = this.getTempPosition();
        pos.y += y + this.getBuildY();
        return pos;
    };
    BaseBuildCmpt.prototype.getBuildY = function () {
        return (this.data.size.y - 1) * Constant_1.TILE_SIZE;
    };
    // 同步位置
    BaseBuildCmpt.prototype.syncPoint = function () {
        if (this.data) {
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    // 同步zindex
    BaseBuildCmpt.prototype.syncZindex = function () {
        if (this.data) {
            var y = this.node.y - this.originY;
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10;
            if (this.data.id === Constant_1.BUILD_DRILLGROUND_NID) {
                index += 1;
            }
            this.tempIndex = this.node.zIndex = index;
        }
    };
    // 根据像素点获取网格点
    BaseBuildCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    BaseBuildCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 重新同步
    BaseBuildCmpt.prototype.resync = function (data, owner) { return this; };
    // 刷新等级
    BaseBuildCmpt.prototype.updateLv = function (lv) { };
    // 设置是否可以点击
    BaseBuildCmpt.prototype.setCanClick = function (val) { };
    // 设置可以点击选择
    BaseBuildCmpt.prototype.setCanClickSelect = function (val) { };
    // 刷新升级动画
    BaseBuildCmpt.prototype.updateUpLvAnim = function () { };
    // 刷新训练士兵
    BaseBuildCmpt.prototype.updateDrillPawn = function () { };
    // 刷新打造装备
    BaseBuildCmpt.prototype.updateForgeEquip = function () { };
    BaseBuildCmpt = __decorate([
        ccclass
    ], BaseBuildCmpt);
    return BaseBuildCmpt;
}(cc.Component));
exports.default = BaseBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGFyZWFcXEJhc2VCdWlsZENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQW1HO0FBQ25HLDJEQUEwRDtBQUdwRCxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUU1QyxLQUFLO0FBRUw7SUFBMkMsaUNBQVk7SUFBdkQ7UUFBQSxxRUF3RkM7UUF0RlUsVUFBSSxHQUFhLElBQUksQ0FBQTtRQUNyQixXQUFLLEdBQVcsRUFBRSxDQUFBO1FBRWYsVUFBSSxHQUFZLElBQUksQ0FBQTtRQUNwQixZQUFNLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBLENBQUMsSUFBSTtRQUM5QixhQUFPLEdBQVcsQ0FBQyxDQUFBLENBQUMsWUFBWTtRQUNoQyxzQkFBZ0IsR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDdEMsZUFBUyxHQUFHLENBQUMsQ0FBQTtRQUVaLGtCQUFZLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBO1FBQy9CLGtCQUFZLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBO1FBQy9CLG9CQUFjLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBOztJQTJFN0MsQ0FBQztJQXpFVSw0QkFBSSxHQUFYLFVBQVksSUFBYyxFQUFFLE1BQWUsRUFBRSxPQUFlLEVBQUUsS0FBYTtRQUN2RSxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUN2QixJQUFJLENBQUMsT0FBTyxHQUFHLE9BQU8sQ0FBQTtRQUN0QixJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTtRQUNsQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUE7UUFDNUMsT0FBTyxJQUFJLENBQUE7SUFDZixDQUFDO0lBRU0sNkJBQUssR0FBWjtJQUNBLENBQUM7SUFFRCxzQkFBVyw2QkFBRTthQUFiLHNCQUFrQixhQUFPLElBQUksQ0FBQyxJQUFJLDBDQUFFLEVBQUUsQ0FBQSxDQUFDLENBQUM7OztPQUFBO0lBQ3hDLHNCQUFXLDhCQUFHO2FBQWQsc0JBQW1CLGFBQU8sSUFBSSxDQUFDLElBQUksMENBQUUsR0FBRyxDQUFBLENBQUMsQ0FBQzs7O09BQUE7SUFDMUMsc0JBQVcsZ0NBQUs7YUFBaEIsY0FBcUIsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQSxDQUFDLENBQUM7OztPQUFBO0lBQ3RDLCtCQUFPLEdBQWQsY0FBbUIsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFBLENBQUMsQ0FBQztJQUM5QixxQ0FBYSxHQUFwQixjQUF5QixPQUFPLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUEsQ0FBQyxDQUFDLEVBQUMsYUFBYTtJQUM1RSx1Q0FBZSxHQUF0QixjQUEyQixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFBLENBQUMsQ0FBQztJQUVsRSxnREFBd0IsR0FBL0IsVUFBZ0MsQ0FBYTtRQUFiLGtCQUFBLEVBQUEsS0FBYTtRQUN6QyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUE7UUFDbEMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFBO1FBQzdCLE9BQU8sR0FBRyxDQUFBO0lBQ2QsQ0FBQztJQUVNLGlDQUFTLEdBQWhCO1FBQ0ksT0FBTyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxvQkFBUyxDQUFBO0lBQzdDLENBQUM7SUFFRCxPQUFPO0lBQ0EsaUNBQVMsR0FBaEI7UUFDSSxJQUFJLElBQUksQ0FBQyxJQUFJLEVBQUU7WUFDWCxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFBO1NBQ2xFO0lBQ0wsQ0FBQztJQUVELFdBQVc7SUFDSixrQ0FBVSxHQUFqQjtRQUNJLElBQUksSUFBSSxDQUFDLElBQUksRUFBRTtZQUNYLElBQU0sQ0FBQyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUE7WUFDcEMsSUFBSSxLQUFLLEdBQUcsQ0FBQywwQkFBZSxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtZQUN0QyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLGdDQUFxQixFQUFFO2dCQUN4QyxLQUFLLElBQUksQ0FBQyxDQUFBO2FBQ2I7WUFDRCxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtTQUM1QztJQUNMLENBQUM7SUFFRCxhQUFhO0lBQ04sMENBQWtCLEdBQXpCLFVBQTBCLEtBQWM7UUFDcEMsT0FBTyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7SUFDbkYsQ0FBQztJQUVELGFBQWE7SUFDTiwwQ0FBa0IsR0FBekIsVUFBMEIsS0FBYztRQUNwQyxPQUFPLHFCQUFTLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQ2xHLENBQUM7SUFFRCxPQUFPO0lBQ0EsOEJBQU0sR0FBYixVQUFjLElBQVMsRUFBRSxLQUFhLElBQUksT0FBTyxJQUFJLENBQUEsQ0FBQyxDQUFDO0lBQ3ZELE9BQU87SUFDQSxnQ0FBUSxHQUFmLFVBQWdCLEVBQVUsSUFBSSxDQUFDO0lBQy9CLFdBQVc7SUFDSixtQ0FBVyxHQUFsQixVQUFtQixHQUFZLElBQUksQ0FBQztJQUNwQyxXQUFXO0lBQ0oseUNBQWlCLEdBQXhCLFVBQXlCLEdBQVksSUFBSSxDQUFDO0lBQzFDLFNBQVM7SUFDRixzQ0FBYyxHQUFyQixjQUEwQixDQUFDO0lBQzNCLFNBQVM7SUFDRix1Q0FBZSxHQUF0QixjQUEyQixDQUFDO0lBQzVCLFNBQVM7SUFDRix3Q0FBZ0IsR0FBdkIsY0FBNEIsQ0FBQztJQXZGWixhQUFhO1FBRGpDLE9BQU87T0FDYSxhQUFhLENBd0ZqQztJQUFELG9CQUFDO0NBeEZELEFBd0ZDLENBeEYwQyxFQUFFLENBQUMsU0FBUyxHQXdGdEQ7a0JBeEZvQixhQUFhIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVJFQV9NQVhfWklOREVYLCBCVUlMRF9EUklMTEdST1VORF9OSUQsIFRJTEVfU0laRSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcbmltcG9ydCB7IG1hcEhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL01hcEhlbHBlclwiO1xuaW1wb3J0IEJ1aWxkT2JqIGZyb20gXCIuLi8uLi9tb2RlbC9hcmVhL0J1aWxkT2JqXCI7XG5cbmNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHkgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbi8vIOW7uuetkVxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJhc2VCdWlsZENtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgcHVibGljIGRhdGE6IEJ1aWxkT2JqID0gbnVsbFxuICAgIHB1YmxpYyBvd25lcjogc3RyaW5nID0gJydcblxuICAgIHByb3RlY3RlZCBib2R5OiBjYy5Ob2RlID0gbnVsbFxuICAgIHByb3RlY3RlZCBvcmlnaW46IGNjLlZlYzIgPSBjYy52MigpIC8v6LW354K5XG4gICAgcHJvdGVjdGVkIG9yaWdpblk6IG51bWJlciA9IDAgLy/lrp7pmYXlnLDlm77nmoTkuIPngrkg5YOP57SgXG4gICAgcHJvdGVjdGVkIHRlbXBCb2R5UG9zaXRpb246IGNjLlZlYzIgPSBjYy52MigpXG4gICAgcHVibGljIHRlbXBJbmRleCA9IDBcblxuICAgIHByaXZhdGUgX3RlbXBfdmVjMl8xOiBjYy5WZWMyID0gY2MudjIoKVxuICAgIHByaXZhdGUgX3RlbXBfdmVjMl8yOiBjYy5WZWMyID0gY2MudjIoKVxuICAgIHByaXZhdGUgX3RlbXBfcG9zaXRpb246IGNjLlZlYzIgPSBjYy52MigpXG5cbiAgICBwdWJsaWMgaW5pdChkYXRhOiBCdWlsZE9iaiwgb3JpZ2luOiBjYy5WZWMyLCBvcmlnaW5ZOiBudW1iZXIsIG93bmVyOiBzdHJpbmcpIHtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YVxuICAgICAgICB0aGlzLm9yaWdpbi5zZXQob3JpZ2luKVxuICAgICAgICB0aGlzLm9yaWdpblkgPSBvcmlnaW5ZXG4gICAgICAgIHRoaXMub3duZXIgPSBvd25lclxuICAgICAgICB0aGlzLmJvZHkgPSB0aGlzLkZpbmRDaGlsZCgnYm9keScpXG4gICAgICAgIHRoaXMuYm9keS5nZXRQb3NpdGlvbih0aGlzLnRlbXBCb2R5UG9zaXRpb24pXG4gICAgICAgIHJldHVybiB0aGlzXG4gICAgfVxuXG4gICAgcHVibGljIGNsZWFuKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBnZXQgaWQoKSB7IHJldHVybiB0aGlzLmRhdGE/LmlkIH1cbiAgICBwdWJsaWMgZ2V0IHVpZCgpIHsgcmV0dXJuIHRoaXMuZGF0YT8udWlkIH1cbiAgICBwdWJsaWMgZ2V0IHBvaW50KCkgeyByZXR1cm4gdGhpcy5kYXRhLnBvaW50IH1cbiAgICBwdWJsaWMgZ2V0Qm9keSgpIHsgcmV0dXJuIHRoaXMuYm9keSB9XG4gICAgcHVibGljIGdldEFuaW1UYXJnZXQoKSB7IHJldHVybiB0aGlzLmJvZHkuQ2hpbGQoJ3ZhbCcpIHx8IHRoaXMuYm9keSB9IC8v6I635Y+W55So5LqO5pKt5pS+5Yqo55S755qE6IqC54K5XG4gICAgcHVibGljIGdldFRlbXBQb3NpdGlvbigpIHsgcmV0dXJuIHRoaXMuZ2V0UG9zaXRpb24odGhpcy5fdGVtcF9wb3NpdGlvbikgfVxuXG4gICAgcHVibGljIGdldEJvZHlPZmZzZXRUb3BQb3NpdGlvbih5OiBudW1iZXIgPSAwKSB7XG4gICAgICAgIGNvbnN0IHBvcyA9IHRoaXMuZ2V0VGVtcFBvc2l0aW9uKClcbiAgICAgICAgcG9zLnkgKz0geSArIHRoaXMuZ2V0QnVpbGRZKClcbiAgICAgICAgcmV0dXJuIHBvc1xuICAgIH1cblxuICAgIHB1YmxpYyBnZXRCdWlsZFkoKSB7XG4gICAgICAgIHJldHVybiAodGhpcy5kYXRhLnNpemUueSAtIDEpICogVElMRV9TSVpFXG4gICAgfVxuXG4gICAgLy8g5ZCM5q2l5L2N572uXG4gICAgcHVibGljIHN5bmNQb2ludCgpIHtcbiAgICAgICAgaWYgKHRoaXMuZGF0YSkge1xuICAgICAgICAgICAgdGhpcy5ub2RlLnNldFBvc2l0aW9uKHRoaXMuZ2V0QWN0UGl4ZWxCeVBvaW50KHRoaXMuZGF0YS5wb2ludCkpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDlkIzmraV6aW5kZXhcbiAgICBwdWJsaWMgc3luY1ppbmRleCgpIHtcbiAgICAgICAgaWYgKHRoaXMuZGF0YSkge1xuICAgICAgICAgICAgY29uc3QgeSA9IHRoaXMubm9kZS55IC0gdGhpcy5vcmlnaW5ZXG4gICAgICAgICAgICBsZXQgaW5kZXggPSAoQVJFQV9NQVhfWklOREVYIC0geSkgKiAxMFxuICAgICAgICAgICAgaWYgKHRoaXMuZGF0YS5pZCA9PT0gQlVJTERfRFJJTExHUk9VTkRfTklEKSB7XG4gICAgICAgICAgICAgICAgaW5kZXggKz0gMVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy50ZW1wSW5kZXggPSB0aGlzLm5vZGUuekluZGV4ID0gaW5kZXhcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOagueaNruWDj+e0oOeCueiOt+WPlue9keagvOeCuVxuICAgIHB1YmxpYyBnZXRBY3RQb2ludEJ5UGl4ZWwocGl4ZWw6IGNjLlZlYzIpIHtcbiAgICAgICAgcmV0dXJuIG1hcEhlbHBlci5nZXRQb2ludEJ5UGl4ZWwocGl4ZWwsIHRoaXMuX3RlbXBfdmVjMl8yKS5zdWJTZWxmKHRoaXMub3JpZ2luKVxuICAgIH1cblxuICAgIC8vIOagueaNrue9keagvOeCueiOt+WPluWDj+e0oOeCuVxuICAgIHB1YmxpYyBnZXRBY3RQaXhlbEJ5UG9pbnQocG9pbnQ6IGNjLlZlYzIpIHtcbiAgICAgICAgcmV0dXJuIG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQocG9pbnQuYWRkKHRoaXMub3JpZ2luLCB0aGlzLl90ZW1wX3ZlYzJfMSksIHRoaXMuX3RlbXBfdmVjMl8xKVxuICAgIH1cblxuICAgIC8vIOmHjeaWsOWQjOatpVxuICAgIHB1YmxpYyByZXN5bmMoZGF0YTogYW55LCBvd25lcjogc3RyaW5nKSB7IHJldHVybiB0aGlzIH1cbiAgICAvLyDliLfmlrDnrYnnuqdcbiAgICBwdWJsaWMgdXBkYXRlTHYobHY6IG51bWJlcikgeyB9XG4gICAgLy8g6K6+572u5piv5ZCm5Y+v5Lul54K55Ye7XG4gICAgcHVibGljIHNldENhbkNsaWNrKHZhbDogYm9vbGVhbikgeyB9XG4gICAgLy8g6K6+572u5Y+v5Lul54K55Ye76YCJ5oupXG4gICAgcHVibGljIHNldENhbkNsaWNrU2VsZWN0KHZhbDogYm9vbGVhbikgeyB9XG4gICAgLy8g5Yi35paw5Y2H57qn5Yqo55S7XG4gICAgcHVibGljIHVwZGF0ZVVwTHZBbmltKCkgeyB9XG4gICAgLy8g5Yi35paw6K6t57uD5aOr5YW1XG4gICAgcHVibGljIHVwZGF0ZURyaWxsUGF3bigpIHsgfVxuICAgIC8vIOWIt+aWsOaJk+mAoOijheWkh1xuICAgIHB1YmxpYyB1cGRhdGVGb3JnZUVxdWlwKCkgeyB9XG59Il19