
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/CancelMarchTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4aa5agfmwpFOZgfCUb+y+kC', 'CancelMarchTipPnlCtrl');
// app/script/view/common/CancelMarchTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var CancelMarchTipPnlCtrl = /** @class */ (function (_super) {
    __extends(CancelMarchTipPnlCtrl, _super);
    function CancelMarchTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.progressNode_ = null; // path://root/info/progress_n
        _this.contentNode_ = null; // path://root/info/content_n
        _this.okNode_ = null; // path://root/buttons/ok_be_n
        //@end
        _this.descNode = null;
        _this.timeNode = null;
        _this.progressLbl = null;
        _this.data = null;
        _this.cb = null;
        return _this;
    }
    CancelMarchTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CancelMarchTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.descNode = this.progressNode_.Child('desc');
                this.timeNode = this.progressNode_.Child('time');
                this.progressLbl = this.progressNode_.Child('bar', cc.Label);
                return [2 /*return*/];
            });
        });
    };
    CancelMarchTipPnlCtrl.prototype.onEnter = function (data, cb) {
        this.data = data;
        this.cb = cb;
        this.timeNode.Component(cc.LabelTimer).setEndTime(data.needTime).run(data.getYetMarchTime() * 0.001);
        this.contentNode_.setLocaleKey('ui.cancel_march_tip', "<fontFamily=Arial>" + data.armyName + "</>");
        this.updateOkButton(this.updateProgress());
    };
    CancelMarchTipPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    CancelMarchTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons/cancel_be
    CancelMarchTipPnlCtrl.prototype.onClickCancel = function (event, data) {
        this.cb && this.cb(false);
        this.hide();
    };
    // path://root/buttons/ok_be_n
    CancelMarchTipPnlCtrl.prototype.onClickOk = function (event, data) {
        this.cb && this.cb(true);
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CancelMarchTipPnlCtrl.prototype.updateOkButton = function (time) {
        var _this = this;
        var delay = 0;
        if (time > ut.Time.Minute * 30) {
            delay = 3;
        }
        else if (time > ut.Time.Minute * 20) {
            delay = 2;
        }
        else if (time > ut.Time.Minute * 10) {
            delay = 1;
        }
        this.okNode_.opacity = delay ? 150 : 255;
        this.okNode_.Component(cc.Button).interactable = !delay;
        var node = this.okNode_.Swih(delay ? 'time' : 'val')[0];
        if (delay) {
            node.Component(cc.LabelTimer).run(delay + 1, function () { return _this.isValid && _this.updateOkButton(0); });
        }
    };
    CancelMarchTipPnlCtrl.prototype.updateProgress = function () {
        var time = this.data.getYetMarchTime();
        var progress = Math.round(time / this.data.needTime * 100);
        var progressColor = '#936E5A';
        if (time >= ut.Time.Minute * 30) {
            progressColor = '#D7634D';
        }
        else if (time >= ut.Time.Minute * 5) {
            progressColor = '#E88A1B';
        }
        this.descNode.Color(progressColor);
        this.timeNode.Color(progressColor);
        this.progressLbl.Color(progressColor).string = '(' + progress + '%)';
        return time;
    };
    CancelMarchTipPnlCtrl.prototype.update = function (dt) {
        this.updateProgress();
    };
    CancelMarchTipPnlCtrl = __decorate([
        ccclass
    ], CancelMarchTipPnlCtrl);
    return CancelMarchTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CancelMarchTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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