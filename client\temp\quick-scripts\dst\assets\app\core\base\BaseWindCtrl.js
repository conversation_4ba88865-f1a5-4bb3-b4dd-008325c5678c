
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '47c70yIDzJGILpKcZhKA5RI', 'BaseWindCtrl');
// app/core/base/BaseWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseViewCtrl_1 = require("./BaseViewCtrl");
/**
 * 基础窗口
 */
var BaseWindCtrl = /** @class */ (function (_super) {
    __extends(BaseWindCtrl, _super);
    function BaseWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = ''; // 模块名
        _this.isClean = true;
        return _this;
    }
    BaseWindCtrl.prototype.__create = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this._state = 'create';
                        this.__listenMaps();
                        return [4 /*yield*/, this.onCreate()];
                    case 1:
                        _a.sent();
                        this.__register('create'); //等创建好了 再注册
                        return [2 /*return*/];
                }
            });
        });
    };
    BaseWindCtrl.prototype.__ready = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this._state = 'ready';
                return [2 /*return*/, this.onReady()];
            });
        });
    };
    BaseWindCtrl.prototype.__enter = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        if (this._state !== 'enter') {
            this._state = 'enter';
            this.__register('enter');
            this.onEnter.apply(this, params);
        }
    };
    BaseWindCtrl.prototype.__leave = function () {
        this._state = 'leave';
        this.__unregister('enter');
        this.onLeave();
    };
    BaseWindCtrl.prototype.__clean = function () {
        this._state = 'clean';
        this.__unregister();
        this.onClean();
    };
    BaseWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BaseWindCtrl.prototype.onReady = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BaseWindCtrl.prototype.onEnter = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    BaseWindCtrl.prototype.onLeave = function () {
    };
    BaseWindCtrl.prototype.onClean = function () {
    };
    BaseWindCtrl.prototype.isEnter = function () {
        return this._state === 'enter';
    };
    BaseWindCtrl.prototype.setParam = function (opts) {
        var _a;
        this.isClean = (_a = opts.isClean) !== null && _a !== void 0 ? _a : this.isClean;
    };
    return BaseWindCtrl;
}(BaseViewCtrl_1.default));
exports.default = BaseWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxiYXNlXFxCYXNlV2luZEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsK0NBQXlDO0FBRXpDOztHQUVHO0FBQ0g7SUFBMEMsZ0NBQVk7SUFBdEQ7UUFBQSxxRUEyREM7UUF6RFUsU0FBRyxHQUFXLEVBQUUsQ0FBQSxDQUFBLE1BQU07UUFDdEIsYUFBTyxHQUFZLElBQUksQ0FBQTs7SUF3RGxDLENBQUM7SUF0RGdCLCtCQUFRLEdBQXJCOzs7Ozt3QkFDSSxJQUFJLENBQUMsTUFBTSxHQUFHLFFBQVEsQ0FBQTt3QkFDdEIsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFBO3dCQUNuQixxQkFBTSxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUE7O3dCQUFyQixTQUFxQixDQUFBO3dCQUNyQixJQUFJLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFBLENBQUMsV0FBVzs7Ozs7S0FDeEM7SUFFWSw4QkFBTyxHQUFwQjs7O2dCQUNJLElBQUksQ0FBQyxNQUFNLEdBQUcsT0FBTyxDQUFBO2dCQUNyQixzQkFBTyxJQUFJLENBQUMsT0FBTyxFQUFFLEVBQUE7OztLQUN4QjtJQUVNLDhCQUFPLEdBQWQ7UUFBZSxnQkFBYzthQUFkLFVBQWMsRUFBZCxxQkFBYyxFQUFkLElBQWM7WUFBZCwyQkFBYzs7UUFDekIsSUFBSSxJQUFJLENBQUMsTUFBTSxLQUFLLE9BQU8sRUFBRTtZQUN6QixJQUFJLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQTtZQUNyQixJQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFBO1lBQ3hCLElBQUksQ0FBQyxPQUFPLE9BQVosSUFBSSxFQUFZLE1BQU0sRUFBQztTQUMxQjtJQUNMLENBQUM7SUFFTSw4QkFBTyxHQUFkO1FBQ0ksSUFBSSxDQUFDLE1BQU0sR0FBRyxPQUFPLENBQUE7UUFDckIsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUMxQixJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7SUFDbEIsQ0FBQztJQUVNLDhCQUFPLEdBQWQ7UUFDSSxJQUFJLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQTtRQUNyQixJQUFJLENBQUMsWUFBWSxFQUFFLENBQUE7UUFDbkIsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO0lBQ2xCLENBQUM7SUFFWSwrQkFBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFWSw4QkFBTyxHQUFwQjs7Ozs7O0tBQ0M7SUFFTSw4QkFBTyxHQUFkO1FBQWUsZ0JBQWM7YUFBZCxVQUFjLEVBQWQscUJBQWMsRUFBZCxJQUFjO1lBQWQsMkJBQWM7O0lBQzdCLENBQUM7SUFFTSw4QkFBTyxHQUFkO0lBQ0EsQ0FBQztJQUVNLDhCQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRU0sOEJBQU8sR0FBZDtRQUNJLE9BQU8sSUFBSSxDQUFDLE1BQU0sS0FBSyxPQUFPLENBQUE7SUFDbEMsQ0FBQztJQUVNLCtCQUFRLEdBQWYsVUFBZ0IsSUFBZTs7UUFDM0IsSUFBSSxDQUFDLE9BQU8sU0FBRyxJQUFJLENBQUMsT0FBTyxtQ0FBSSxJQUFJLENBQUMsT0FBTyxDQUFBO0lBQy9DLENBQUM7SUFDTCxtQkFBQztBQUFELENBM0RBLEFBMkRDLENBM0R5QyxzQkFBWSxHQTJEckQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZVZpZXdDdHJsIGZyb20gXCIuL0Jhc2VWaWV3Q3RybFwiXHJcblxyXG4vKipcclxuICog5Z+656GA56qX5Y+jXHJcbiAqL1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBCYXNlV2luZEN0cmwgZXh0ZW5kcyBCYXNlVmlld0N0cmwge1xyXG5cclxuICAgIHB1YmxpYyBrZXk6IHN0cmluZyA9ICcnLy8g5qih5Z2X5ZCNXHJcbiAgICBwdWJsaWMgaXNDbGVhbjogYm9vbGVhbiA9IHRydWVcclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgX19jcmVhdGUoKSB7XHJcbiAgICAgICAgdGhpcy5fc3RhdGUgPSAnY3JlYXRlJ1xyXG4gICAgICAgIHRoaXMuX19saXN0ZW5NYXBzKClcclxuICAgICAgICBhd2FpdCB0aGlzLm9uQ3JlYXRlKClcclxuICAgICAgICB0aGlzLl9fcmVnaXN0ZXIoJ2NyZWF0ZScpIC8v562J5Yib5bu65aW95LqGIOWGjeazqOWGjFxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhc3luYyBfX3JlYWR5KCkge1xyXG4gICAgICAgIHRoaXMuX3N0YXRlID0gJ3JlYWR5J1xyXG4gICAgICAgIHJldHVybiB0aGlzLm9uUmVhZHkoKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBfX2VudGVyKC4uLnBhcmFtczogYW55KSB7XHJcbiAgICAgICAgaWYgKHRoaXMuX3N0YXRlICE9PSAnZW50ZXInKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX3N0YXRlID0gJ2VudGVyJ1xyXG4gICAgICAgICAgICB0aGlzLl9fcmVnaXN0ZXIoJ2VudGVyJylcclxuICAgICAgICAgICAgdGhpcy5vbkVudGVyKC4uLnBhcmFtcylcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIF9fbGVhdmUoKSB7XHJcbiAgICAgICAgdGhpcy5fc3RhdGUgPSAnbGVhdmUnXHJcbiAgICAgICAgdGhpcy5fX3VucmVnaXN0ZXIoJ2VudGVyJylcclxuICAgICAgICB0aGlzLm9uTGVhdmUoKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBfX2NsZWFuKCkge1xyXG4gICAgICAgIHRoaXMuX3N0YXRlID0gJ2NsZWFuJ1xyXG4gICAgICAgIHRoaXMuX191bnJlZ2lzdGVyKClcclxuICAgICAgICB0aGlzLm9uQ2xlYW4oKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgb25SZWFkeSgpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25FbnRlciguLi5wYXJhbXM6IGFueSkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkxlYXZlKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBpc0VudGVyKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9zdGF0ZSA9PT0gJ2VudGVyJ1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzZXRQYXJhbShvcHRzOiBXaW5kUGFyYW0pIHtcclxuICAgICAgICB0aGlzLmlzQ2xlYW4gPSBvcHRzLmlzQ2xlYW4gPz8gdGhpcy5pc0NsZWFuXHJcbiAgICB9XHJcbn0iXX0=