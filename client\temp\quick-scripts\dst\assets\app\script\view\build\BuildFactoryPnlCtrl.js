
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildFactoryPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1f1140A/VJnqOtHn/tZR4n', 'BuildFactoryPnlCtrl');
// app/script/view/build/BuildFactoryPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ccclass = cc._decorator.ccclass;
var BuildFactoryPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildFactoryPnlCtrl, _super);
    function BuildFactoryPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.queueSv_ = null; // path://root/pages_n/1/drill/content/queue_sv
        //@end
        _this.PKEY_TAB = '';
        _this.PKEY_SELECT_ARMY = '';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.tab = 0;
        _this.selectPawnSlot = null;
        _this.selectArmy = null;
        _this.drillProgressTween = {};
        _this.tempArmySortWeightMap = {};
        _this.tempCreateArmy = null;
        _this.armyList = [];
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildFactoryPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdatePawnDrillQueue, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.REMOVE_ARMY] = this.onUpdateArmy, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI] = this.onUpdateArmy, _l.enter = true, _l),
        ];
    };
    BuildFactoryPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildFactoryPnlCtrl.prototype.onEnter = function (data, tab) {
        this.PKEY_TAB = 'FACTORY_TAB_' + data.id;
        this.PKEY_SELECT_ARMY = 'FACTORY_SELECT_ARMY_TAB_' + data.id;
        this.data = data;
        this.tempCreateArmy = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.TEMP_CREATE_ARMY);
        var cond = this.pagesNode_.Child('1/info/cond');
        cond.Child('need/title/val').setLocaleKey('ui.drill_cost', 'ui.button_produce');
        this.pagesNode_.Child('1/drill/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_produce');
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildFactoryPnlCtrl.prototype.onRemove = function () {
        this.selectPawnSlot = null;
        this.selectArmy = null;
        this.tempArmySortWeightMap = {};
        this.showCreateArmyFingerTip(false);
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy);
    };
    BuildFactoryPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildFactoryPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.FACTORY_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            // 显示当前的军队列表
            this.selectArmy = null;
            this.tempArmySortWeightMap = {};
            this.updateArmyList(true, node);
            // 显示可训练的士兵
            this.selectPawnSlot = null;
            this.updatePawnList(true, node);
            // 训练列表
            this.updateDrillQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildFactoryPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/info/pawn/list/view/content/pawn_be
    BuildFactoryPnlCtrl.prototype.onClickPawn = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, slot = it.Data;
        if (!slot) {
            return;
        }
        else if (slot.uid === ((_a = this.selectPawnSlot) === null || _a === void 0 ? void 0 : _a.uid)) {
            if (slot.pawn) {
                GameHelper_1.gameHpr.setNoLongerTip('look_pawn_info', true);
                ViewHelper_1.viewHelper.showPnl('area/PawnInfo', slot.pawn);
            }
            return;
        }
        else if (slot.pawn && !GameHelper_1.gameHpr.isNoLongerTip('look_pawn_info')) {
            ViewHelper_1.viewHelper.showPnl('common/NoLongerTip', {
                noKey: 'look_pawn_info',
                content: 'ui.look_pawn_info_tip',
                okText: 'ui.button_gotit',
                select: true,
            });
        }
        this.updatePawnSelect(slot, false);
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildFactoryPnlCtrl.prototype.onClickArmy = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data;
        if (!data) {
            if (this.tempCreateArmy) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_has_empty_army');
            }
            return this.showCreateArmyUI();
        } /* else if (!data.army) {
            return viewHelper.showAlert('toast.army_not_in_maincity')
        }  */
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/drill_be
    BuildFactoryPnlCtrl.prototype.onClickDrill = function (event, data) {
        var _this = this;
        var slot = this.selectPawnSlot, state = event.target.Data;
        if (!slot) {
            if (!this.selectArmy) { // 没有军队，提示创建军队
                if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                    return ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                        cb: function () {
                            if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                                _this.showCreateArmyFingerTip(true);
                            }
                        }
                    });
                }
                else {
                    var army_1 = this.player.getBaseArmys()[0];
                    return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_1, 'ui.button_produce');
                }
            }
            else { // 有军队，但是外出，先提示军队
                if (!this.selectArmy.army) { // 外出
                    if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                        return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
                    }
                    else {
                        var army_2 = this.player.getBaseArmys()[0];
                        return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_2, 'ui.button_produce');
                    }
                }
                else if (this.player.getCanRecruitPawns(this.data.id).filter(function (m) { return !!m.pawn; }).length === 0) { // 没有士兵，提示点击加号解锁
                    return ViewHelper_1.viewHelper.showAlert('ui.please_select_pawn_study');
                }
                else { // 已有士兵，提示选择一个士兵进行招募
                    return ViewHelper_1.viewHelper.showAlert('toast.please_select_ceir_item', { params: ['ui.pawn_type_5'] });
                }
            }
        }
        else if (state === 1) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_PAWN_FULL);
        }
        else if (state === 2) {
            if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
            }
            else {
                return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', this.player.getBaseArmys()[0], 'ui.button_produce');
            }
        }
        var area = this.areaCenter.getArea(this.data.aIndex);
        if (!area) {
            return;
        }
        else if (!this.selectArmy) {
            if (area.armys.length === 0 || area.armys.every(function (m) { return m.pawns.length >= Constant_1.ARMY_PAWN_MAX_COUNT; })) { //一个军队也没有
                ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                    cb: function () {
                        if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                            _this.showCreateArmyFingerTip(true);
                        }
                    }
                });
            }
            else {
                ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
            }
            return;
        }
        var selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = '';
        var army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid);
        if (!army) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_NOT_EXIST);
        }
        else if (army.uid.startsWith('temp_')) {
            armyName = army.name;
        }
        else {
            tempArmyUid = army.uid;
        }
        this.areaCenter.drillPawnToServer(this.data.aIndex, this.data.uid, slot.id, tempArmyUid, armyName).then(function (res) {
            var _a;
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            }
            else if (res.err === ECode_1.ecode.ANTI_CHEAT) {
                ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
            }
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                audioMgr.playSFX('build/sound_ui_00' + (_this.data.id === 2010 ? '7' : '6'));
                var army_3 = res.army;
                if (((_a = _this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === selectArmyUid) {
                    _this.tempCreateArmy = null;
                    _this.selectArmy && (_this.selectArmy.uid = army_3.uid);
                    if (_this.tempArmySortWeightMap[selectArmyUid]) {
                        _this.tempArmySortWeightMap[army_3.uid] = _this.tempArmySortWeightMap[selectArmyUid];
                        delete _this.tempArmySortWeightMap[selectArmyUid];
                    }
                    _this.player.getBaseArmys().push({
                        index: _this.data.aIndex,
                        uid: army_3.uid,
                        name: army_3.name,
                        state: Enums_1.ArmyState.DRILL,
                        pawns: [],
                    });
                }
                // this.updateDrillQueue()
                // const node = this.pagesNode_.Child(1)
                // this.updateArmyList(false, node)
                // this.updateRecruitCost(node)
            }
        });
    };
    // path://root/pages_n/1/drill/content/0/drill_pawn_be
    BuildFactoryPnlCtrl.prototype.onClickDrillPawn = function (event, _) {
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByDrillInfo(data), data);
        }
    };
    // path://root/pages_n/1/info/cond/none/buttons/restudy/study_be
    BuildFactoryPnlCtrl.prototype.onClickStudy = function (event, data) {
        var slot = this.selectPawnSlot;
        if (!slot) {
            return;
        }
        else if (slot.selectIds.length === 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_unlock_prev_slot', { params: ['ui.pawn_type_5'] });
        }
        ViewHelper_1.viewHelper.showPnl('build/StudySelect', slot);
    };
    // path://root/pages_n/1/info/cond/none/buttons/cond/button/goto_worship_be
    BuildFactoryPnlCtrl.prototype.onClickGotoWorship = function (event, data) {
        var _this = this;
        if (!this.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL)) {
            ViewHelper_1.viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + Enums_1.BUILD_NID.HERO_HALL] });
        }
        ViewHelper_1.viewHelper.showBuildInfoByMain(Enums_1.BUILD_NID.HERO_HALL, 1).then(function (ok) {
            if (ok && _this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/pages_n/1/drill/content/0/cancel_drill_be
    BuildFactoryPnlCtrl.prototype.onClickCancelDrill = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data, pawnObj = this.areaCenter.createPawnByDrillInfo(data);
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox(pawnObj.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelDrill(pawnObj, data); },
                cancel: function () { },
            });
        }
        this.cancelDrill(pawnObj, data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildFactoryPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.FACTORY_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新训练列表
    BuildFactoryPnlCtrl.prototype.onUpdatePawnDrillQueue = function () {
        this.updateDrillQueue();
        // this.updateArmyList(false)
        this.updateRecruitCost();
    };
    // 战斗开始
    BuildFactoryPnlCtrl.prototype.onAreaBattleBegin = function (index) {
        if (this.data.aIndex === index) {
            this.updateDrillQueue();
        }
    };
    // 战斗结束
    BuildFactoryPnlCtrl.prototype.onAreaBattleEnd = function (index) {
        if (this.data.aIndex === index) {
            this.updateDrillQueue();
        }
    };
    // 切换士兵皮肤
    BuildFactoryPnlCtrl.prototype.onChangePawnSkin = function (data) {
        var node = this.pagesNode_.Child(1);
        this.updatePawnList(false, node);
        this.updateDrillQueue(node);
    };
    // 重新刷新军队列表
    BuildFactoryPnlCtrl.prototype.onUpdateArmy = function () {
        this.updateArmyList(false);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildFactoryPnlCtrl.prototype.getTempCreateArmy = function (uid) {
        var _a;
        if (((_a = this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.tempCreateArmy;
        }
        return null;
    };
    BuildFactoryPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        var _a, _b;
        var item = {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
        if (!this.tempArmySortWeightMap[data.uid]) {
            var weight = item.army ? 2 : 1;
            weight = weight * 10 + (9 - (((_a = item.army) === null || _a === void 0 ? void 0 : _a.getActPawnCount()) || 0));
            weight = weight * 10 + (9 - (((_b = item.army) === null || _b === void 0 ? void 0 : _b.pawns.length) || 0));
            this.tempArmySortWeightMap[data.uid] = weight;
        }
        return item;
    };
    // 刷新军队列表
    BuildFactoryPnlCtrl.prototype.updateArmyList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        node = node || this.pagesNode_.Child(1);
        // 当前区域的军队
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        this.armyList = [null];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return _this.armyList.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        if (this.tempCreateArmy) {
            this.armyList.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy));
        }
        // 排个序
        this.armyList.sort(function (a, b) { return _this.tempArmySortWeightMap[b === null || b === void 0 ? void 0 : b.uid] - _this.tempArmySortWeightMap[a === null || a === void 0 ? void 0 : a.uid]; });
        var countNode = node.Child('army/title/count_bg');
        countNode.Child('cur', cc.Label).string = (this.armyList.length - 1) + '';
        countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount();
        var uid = (_c = (_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid) !== null && _c !== void 0 ? _c : this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? this.armyList.find(function (m) { return !!(m === null || m === void 0 ? void 0 : m.army) && (m === null || m === void 0 ? void 0 : m.uid) === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.stopAutoScroll();
        // this.armyList.push(null)
        var showDot = !this.armyList.some(function (m) { return !!m; });
        sv.Items(this.armyList, function (it, data, i) {
            var _a;
            it.Data = data;
            var army = data === null || data === void 0 ? void 0 : data.army;
            var root = it.Child('root'), info = root.Child('info');
            info.Child('dot').active = showDot;
            info.Child('add').active = !data;
            info.Child('count').active = !!data;
            info.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            var state = info.Child('state');
            if (army) {
                info.Child('count/val', cc.Label).string = army.pawns.length + '';
                var addLbl = info.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length;
                if (addLbl.node.active = dpc > 0) {
                    addLbl.string = '+' + dpc;
                }
                var isFull = state.active = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT;
                root.Child('bg').Component(cc.MultiFrame).setFrame(!isFull);
                info.opacity = isFull ? 150 : 255;
                if (isFull) {
                    state.setLocaleKey('ui.yet_full');
                }
                // 显示选择
                /* if (!curArmy && isFull) {
                } else */ if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
                }
            }
            else if (data) {
                info.opacity = 150;
                root.Child('bg').Component(cc.MultiFrame).setFrame(false);
                info.Child('count/val', cc.Label).string = (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0) + '';
                info.Child('count/add').active = false;
                state.active = true;
                state.setLocaleKey('ui.go_out');
                if (index === -1 && (!curArmy || curArmy.uid === data.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, data.uid);
                }
            }
            else {
                info.opacity = 255;
                state.active = false;
                root.Child('bg').Component(cc.MultiFrame).setFrame(true);
            }
        });
        // 将选中的移动到中间
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        // 刷新选中
        this.updateArmySelect(curArmy, node);
    };
    BuildFactoryPnlCtrl.prototype.updateArmySelect = function (item, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var uid = (item === null || item === void 0 ? void 0 : item.uid) || '';
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        node.Child('info/army').Swih(/* !item ? 'tip' : */ 'army_pawns');
        this.selectArmy = item;
        var army = item === null || item === void 0 ? void 0 : item.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
            pawns.pushArr(army.curingPawns);
            pawns.pushArr(army.drillPawns);
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            pawns.pushArr(item.pawns);
        }
        // 刷新士兵列表
        node.Child('info/army/army_pawns').children.forEach(function (it, i) {
            var _a;
            var data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!(army === null || army === void 0 ? void 0 : army.curingPawns.some(function (m) { return m.uid === data.uid; }));
            it.Swih('none', !!data);
            if (data) {
                var icon = it.Child('icon');
                icon.opacity = (isId || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? data : (((_a = data.portrayal) === null || _a === void 0 ? void 0 : _a.id) || data.id), icon, _this.key);
                it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + '';
            }
        });
        // 刷新按钮
        var cond = node.Child('info/cond'), button = cond.Child('need/buttons/drill_be');
        if (army) {
            button.Data = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT ? 1 : 0;
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            button.Data = 2; //不在主城内
        }
        else {
            button.Data = 0; //没有选择军队
        }
        button.opacity = (!this.selectPawnSlot || !item || button.Data) ? 120 : 255;
    };
    // 刷新士兵列表
    BuildFactoryPnlCtrl.prototype.updatePawnList = function (isLocation, node) {
        var _this = this;
        var _a;
        node = node || this.pagesNode_.Child(1);
        var root = node.Child('info');
        var selectPawnSlot = this.selectPawnSlot;
        var selectUid = (_a = this.selectPawnSlot) === null || _a === void 0 ? void 0 : _a.uid;
        var slots = this.player.getCanProduceMachines(this.data.id);
        var sv = root.Child('pawn/list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.Items(slots, function (it, slot, i) {
            it.Data = slot;
            ResHelper_1.resHelper.loadPawnHeadIcon(slot.viewId, it.Child('icon'), _this.key);
            // // 找到选择
            // if (!selectUid) {
            // } else if (!selectPawnSlot && slot.uid === selectUid) {
            // 	selectPawnSlot = slot
            // }
        });
        this.updatePawnSelect(selectPawnSlot, isLocation, node);
    };
    BuildFactoryPnlCtrl.prototype.updatePawnSelect = function (slot, isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        this.selectPawnSlot = slot;
        var uid = (slot === null || slot === void 0 ? void 0 : slot.uid) || '', selectIndex = -1;
        var root = node.Child('info'), sv = root.Child('pawn/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a, _b;
            var select = m.Child('bg/select').active = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select || !!((_b = m.Data) === null || _b === void 0 ? void 0 : _b.pawn);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
        // 显示士兵费用
        this.updateRecruitCost(node);
    };
    // 刷新招募费用
    BuildFactoryPnlCtrl.prototype.updateRecruitCost = function (node) {
        node = node || this.pagesNode_.Child(1);
        var root = node.Child('info'), cond = root.Child('cond');
        var slot = this.selectPawnSlot;
        if (!slot) { // 招募按钮一直显示，通过点击按钮做出相应的提示
            var it_1 = cond.Swih('need')[0];
            var cost = [Enums_1.CType.CEREAL, Enums_1.CType.CEREAL_C];
            it_1.Child('time/up').active = false;
            it_1.Child('time/val', cc.Label) /* .Color('#756963') */.string = '-';
            it_1.Child('cost').Items(cost || [], function (it, cost) {
                if (it && cost) {
                    it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost);
                    it.Child('val', cc.Label) /* .Color('#756963') */.string = '-';
                }
            });
            it_1.Child('buttons/drill_be/val').setLocaleKey('ui.button_produce');
            return;
        }
        var isUnlock = this.data.lv >= slot.lv;
        var it = cond.Swih(isUnlock ? 'need' : 'none')[0];
        // 按钮
        var buttons = it.Child('buttons');
        if (!isUnlock) {
            var button = buttons.Swih('lock')[0];
            button.Child('val').setLocaleKey('ui.lv_unlock', ["<color=#A18876>" + assetsMgr.lang(this.data.name) + "</c><color=#C34B3F>" + assetsMgr.lang('ui.short_lv', slot.lv) + "</c>", '']);
        }
        else {
            // 检测是否有训练士兵费用增加
            var cost = GameHelper_1.gameHpr.world.getSeason().changeBaseResCost(Enums_1.CEffect.RECRUIT_COST, slot.drillCost);
            // 减少时间
            var time = slot.drillTime;
            var cd = this.getDrillTimeCD();
            var upCount = this.player.getUpRecruitPawnCount(); //加速 不免费
            var policyFreeCount = this.player.getFreeRecruitPawnSurplusCount();
            ViewHelper_1.viewHelper.updateFreeCostView(it, cost, time, cd, false, policyFreeCount, upCount);
            var button = buttons.Child('drill_be');
            button.opacity = 255;
            button.Child('val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_produce' : 'ui.button_produce');
        }
    };
    // 获取训练时间
    BuildFactoryPnlCtrl.prototype.getDrillTimeCD = function () {
        var cd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.XL_CD) + this.data.getEffectValue(Enums_1.CEffect.XL_CD);
        return cd * 0.01;
    };
    // 刷新训练列表
    BuildFactoryPnlCtrl.prototype.updateDrillQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getPawnDrillQueues(this.data.uid);
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var pawnConf = this.player.getConfigPawnMap();
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.DRILL_QUEUE);
        node.Child('drill/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('drill/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1 || childrenCount < queueCount - 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var skinId = data ? (((_a = pawnConf[data.id]) === null || _a === void 0 ? void 0 : _a.skinId) || data.id) : 0;
            var has = it.Child('icon').active = it.Child('drill_pawn_be').active = !!data;
            (_b = it.Child('cancel_drill_be')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            (_c = it.Child('icon/progress')) === null || _c === void 0 ? void 0 : _c.setActive(has);
            if (data) {
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            }
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = data.getSurplusTime();
                time += stime;
                (_d = this.drillProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.drillProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                var st = stime * 0.001;
                it.Child('time', cc.LabelTimer).run(st);
                this.drillProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('drill/desc').active = time > 0;
        if (time > 0) {
            node.Child('drill/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_produce');
            node.Child('drill/desc/time/val', cc.LabelTimer).run(time * 0.001);
        }
    };
    BuildFactoryPnlCtrl.prototype.showCreateArmyUI = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        this.showCreateArmyFingerTip(false);
        return ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.tempCreateArmy = new ArmyObj_1.default().init(_this.data.aIndex, GameHelper_1.gameHpr.getUid(), name);
                _this.tempArmySortWeightMap = {};
                if (!_this.selectArmy) {
                    _this.selectArmy = {};
                }
                _this.selectArmy.uid = _this.tempCreateArmy.uid;
                _this.updateArmyList(true, _this.pagesNode_.Child(1));
            }
        });
    };
    // 显示创建军队提示手指
    BuildFactoryPnlCtrl.prototype.showCreateArmyFingerTip = function (val) {
        var node = this.pagesNode_.Child(1);
        var sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger');
        if (finger.active = val) {
            var count = sv.content.childrenCount;
            sv.stopAutoScroll();
            if (count >= 4) {
                sv.scrollToRight();
            }
            var it = sv.content.children[count - 1];
            var pos = ut.convertToNodeAR(it, sv.node);
            finger.setPosition(pos.x, pos.y - 12);
        }
    };
    // 取消招募
    BuildFactoryPnlCtrl.prototype.cancelDrill = function (data, info) {
        if (!data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        var isMachine = data.isMachine();
        NetHelper_1.netHelper.reqCancelDrillPawn({ index: index, buildUid: info.buid, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data_1 = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data_1.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data_1.army);
                GameHelper_1.gameHpr.player.updatePawnDrillQueue(data_1.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_b = data_1.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',
                        id: json.id,
                        cost: data_1.needCost,
                    });
                }
            }
        });
    };
    BuildFactoryPnlCtrl = __decorate([
        ccclass
    ], BuildFactoryPnlCtrl);
    return BuildFactoryPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildFactoryPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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