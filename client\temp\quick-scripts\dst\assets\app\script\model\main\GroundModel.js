
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/GroundModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6f313kEFnNOypbNrvdD59Wk', 'GroundModel');
// app/script/model/main/GroundModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 地面模块
 */
var GroundModel = /** @class */ (function (_super) {
    __extends(GroundModel, _super);
    function GroundModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.SEND_INTERVAL = 6000; //发送聊天的预期间隔 (毫秒)
        _this.TOLERATE_MAX_COUNT = 5; //最多容忍多少次在间隔内发送
        _this.REST_MAX_TIME = 15000; //如果太频繁就休息一下
        _this.cellEmojis = []; //当前的地图表情
        _this.lastSendChatTime = 0; //上次发送时间
        _this.tolerateCount = 0;
        _this.restStartTime = 0; //休息时间
        return _this;
    }
    // 获取地图表情
    GroundModel.prototype.getCellEmojis = function () {
        var now = Date.now();
        this.cellEmojis.delete(function (m) { return now - m.getTime >= m.duration; }); //先删掉过期的
        return this.cellEmojis;
    };
    // 添加地图表情
    GroundModel.prototype.addCellEmoji = function (data) {
        var uid = GameHelper_1.gameHpr.getUid();
        if (!data || data.uid === uid) { //如果是自己发的
            return;
        }
        var json = assetsMgr.getJsonData('chatEmoji', data.emoji);
        if (!json) {
            return;
        }
        this.cellEmojis.push({
            uid: data.uid || uid,
            index: data.index,
            emoji: data.emoji,
            getTime: Date.now(),
            duration: json.anim_time || 3000,
        });
        this.emit(EventType_1.default.PLAY_CELL_EMOJI);
    };
    // 发送地图表情
    GroundModel.prototype.sendCellEmoji = function (emoji, index) {
        if (!emoji) {
            return;
        }
        else if (this.checkOftenTime()) {
            return ViewHelper_1.viewHelper.showAlert('toast.send_chat_tolerate');
        }
        this.lastSendChatTime = Date.now();
        // 发送到服务器
        GameHelper_1.gameHpr.net.send('game/HD_SendCellEmoji', { emoji: emoji, index: index });
        // 本地先直接发
        this.addCellEmoji({ emoji: emoji, index: index });
    };
    // 检测是否发送得太频繁了
    GroundModel.prototype.checkOftenTime = function () {
        var now = Date.now();
        if (now - this.restStartTime < this.REST_MAX_TIME) {
            return true;
        }
        else if (now - this.lastSendChatTime < this.SEND_INTERVAL) {
            this.tolerateCount += 1;
        }
        else {
            this.tolerateCount = 0;
        }
        if (this.tolerateCount >= this.TOLERATE_MAX_COUNT - 1) {
            this.restStartTime = now;
        }
        else {
            this.restStartTime = 0;
        }
        return false;
    };
    // 检测休息剩余时间
    GroundModel.prototype.checkRestSurplusTime = function () {
        if (this.restStartTime <= 0) {
            return 0;
        }
        var time = Date.now() - this.restStartTime;
        if (time >= this.REST_MAX_TIME) {
            this.restStartTime = 0;
            return 0;
        }
        return this.REST_MAX_TIME - time;
    };
    GroundModel = __decorate([
        mc.addmodel('ground')
    ], GroundModel);
    return GroundModel;
}(mc.BaseModel));
exports.default = GroundModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxtYWluXFxHcm91bmRNb2RlbC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSwwREFBb0Q7QUFDcEQsNkRBQXdEO0FBQ3hELDZEQUEyRDtBQUUzRDs7R0FFRztBQUVIO0lBQXlDLCtCQUFZO0lBQXJEO1FBQUEscUVBbUZDO1FBakZvQixtQkFBYSxHQUFXLElBQUksQ0FBQSxDQUFDLGdCQUFnQjtRQUM3Qyx3QkFBa0IsR0FBVyxDQUFDLENBQUEsQ0FBQyxlQUFlO1FBQzlDLG1CQUFhLEdBQVcsS0FBSyxDQUFBLENBQUMsWUFBWTtRQUVuRCxnQkFBVSxHQUFvQixFQUFFLENBQUEsQ0FBQyxTQUFTO1FBRTFDLHNCQUFnQixHQUFXLENBQUMsQ0FBQSxDQUFDLFFBQVE7UUFDckMsbUJBQWEsR0FBVyxDQUFDLENBQUE7UUFDekIsbUJBQWEsR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNOztJQXlFNUMsQ0FBQztJQXZFRyxTQUFTO0lBQ0YsbUNBQWEsR0FBcEI7UUFDSSxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLENBQUE7UUFDdEIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxHQUFHLEdBQUcsQ0FBQyxDQUFDLE9BQU8sSUFBSSxDQUFDLENBQUMsUUFBUSxFQUE3QixDQUE2QixDQUFDLENBQUEsQ0FBQyxRQUFRO1FBQ25FLE9BQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQTtJQUMxQixDQUFDO0lBRUQsU0FBUztJQUNGLGtDQUFZLEdBQW5CLFVBQW9CLElBQVM7UUFDekIsSUFBTSxHQUFHLEdBQUcsb0JBQU8sQ0FBQyxNQUFNLEVBQUUsQ0FBQTtRQUM1QixJQUFJLENBQUMsSUFBSSxJQUFJLElBQUksQ0FBQyxHQUFHLEtBQUssR0FBRyxFQUFFLEVBQUUsU0FBUztZQUN0QyxPQUFNO1NBQ1Q7UUFDRCxJQUFNLElBQUksR0FBRyxTQUFTLENBQUMsV0FBVyxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDM0QsSUFBSSxDQUFDLElBQUksRUFBRTtZQUNQLE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDO1lBQ2pCLEdBQUcsRUFBRSxJQUFJLENBQUMsR0FBRyxJQUFJLEdBQUc7WUFDcEIsS0FBSyxFQUFFLElBQUksQ0FBQyxLQUFLO1lBQ2pCLEtBQUssRUFBRSxJQUFJLENBQUMsS0FBSztZQUNqQixPQUFPLEVBQUUsSUFBSSxDQUFDLEdBQUcsRUFBRTtZQUNuQixRQUFRLEVBQUUsSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJO1NBQ25DLENBQUMsQ0FBQTtRQUNGLElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQyxlQUFlLENBQUMsQ0FBQTtJQUN4QyxDQUFDO0lBRUQsU0FBUztJQUNGLG1DQUFhLEdBQXBCLFVBQXFCLEtBQWEsRUFBRSxLQUFhO1FBQzdDLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDUixPQUFNO1NBQ1Q7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEVBQUUsRUFBRTtZQUM5QixPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDBCQUEwQixDQUFDLENBQUE7U0FDMUQ7UUFDRCxJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFBO1FBQ2xDLFNBQVM7UUFDVCxvQkFBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsdUJBQXVCLEVBQUUsRUFBRSxLQUFLLE9BQUEsRUFBRSxLQUFLLE9BQUEsRUFBRSxDQUFDLENBQUE7UUFDM0QsU0FBUztRQUNULElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxLQUFLLE9BQUEsRUFBRSxLQUFLLE9BQUEsRUFBRSxDQUFDLENBQUE7SUFDdkMsQ0FBQztJQUVELGNBQWM7SUFDTixvQ0FBYyxHQUF0QjtRQUNJLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQTtRQUN0QixJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDL0MsT0FBTyxJQUFJLENBQUE7U0FDZDthQUFNLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ3pELElBQUksQ0FBQyxhQUFhLElBQUksQ0FBQyxDQUFBO1NBQzFCO2FBQU07WUFDSCxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQTtTQUN6QjtRQUNELElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxJQUFJLENBQUMsa0JBQWtCLEdBQUcsQ0FBQyxFQUFFO1lBQ25ELElBQUksQ0FBQyxhQUFhLEdBQUcsR0FBRyxDQUFBO1NBQzNCO2FBQU07WUFDSCxJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQTtTQUN6QjtRQUNELE9BQU8sS0FBSyxDQUFBO0lBQ2hCLENBQUM7SUFFRCxXQUFXO0lBQ0osMENBQW9CLEdBQTNCO1FBQ0ksSUFBSSxJQUFJLENBQUMsYUFBYSxJQUFJLENBQUMsRUFBRTtZQUN6QixPQUFPLENBQUMsQ0FBQTtTQUNYO1FBQ0QsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUE7UUFDNUMsSUFBSSxJQUFJLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRTtZQUM1QixJQUFJLENBQUMsYUFBYSxHQUFHLENBQUMsQ0FBQTtZQUN0QixPQUFPLENBQUMsQ0FBQTtTQUNYO1FBQ0QsT0FBTyxJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQTtJQUNwQyxDQUFDO0lBbEZnQixXQUFXO1FBRC9CLEVBQUUsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDO09BQ0QsV0FBVyxDQW1GL0I7SUFBRCxrQkFBQztDQW5GRCxBQW1GQyxDQW5Gd0MsRUFBRSxDQUFDLFNBQVMsR0FtRnBEO2tCQW5Gb0IsV0FBVyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENlbGxFbW9qaUluZm8gfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCJcclxuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiXHJcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCJcclxuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIlxyXG5cclxuLyoqXHJcbiAqIOWcsOmdouaooeWdl1xyXG4gKi9cclxuQG1jLmFkZG1vZGVsKCdncm91bmQnKVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBHcm91bmRNb2RlbCBleHRlbmRzIG1jLkJhc2VNb2RlbCB7XHJcblxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBTRU5EX0lOVEVSVkFMOiBudW1iZXIgPSA2MDAwIC8v5Y+R6YCB6IGK5aSp55qE6aKE5pyf6Ze06ZqUICjmr6vnp5IpXHJcbiAgICBwcml2YXRlIHJlYWRvbmx5IFRPTEVSQVRFX01BWF9DT1VOVDogbnVtYmVyID0gNSAvL+acgOWkmuWuueW/jeWkmuWwkeasoeWcqOmXtOmalOWGheWPkemAgVxyXG4gICAgcHJpdmF0ZSByZWFkb25seSBSRVNUX01BWF9USU1FOiBudW1iZXIgPSAxNTAwMCAvL+WmguaenOWkqumikee5geWwseS8keaBr+S4gOS4i1xyXG5cclxuICAgIHByaXZhdGUgY2VsbEVtb2ppczogQ2VsbEVtb2ppSW5mb1tdID0gW10gLy/lvZPliY3nmoTlnLDlm77ooajmg4VcclxuXHJcbiAgICBwcml2YXRlIGxhc3RTZW5kQ2hhdFRpbWU6IG51bWJlciA9IDAgLy/kuIrmrKHlj5HpgIHml7bpl7RcclxuICAgIHByaXZhdGUgdG9sZXJhdGVDb3VudDogbnVtYmVyID0gMFxyXG4gICAgcHJpdmF0ZSByZXN0U3RhcnRUaW1lOiBudW1iZXIgPSAwIC8v5LyR5oGv5pe26Ze0XHJcblxyXG4gICAgLy8g6I635Y+W5Zyw5Zu+6KGo5oOFXHJcbiAgICBwdWJsaWMgZ2V0Q2VsbEVtb2ppcygpIHtcclxuICAgICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXHJcbiAgICAgICAgdGhpcy5jZWxsRW1vamlzLmRlbGV0ZShtID0+IG5vdyAtIG0uZ2V0VGltZSA+PSBtLmR1cmF0aW9uKSAvL+WFiOWIoOaOiei/h+acn+eahFxyXG4gICAgICAgIHJldHVybiB0aGlzLmNlbGxFbW9qaXNcclxuICAgIH1cclxuXHJcbiAgICAvLyDmt7vliqDlnLDlm77ooajmg4VcclxuICAgIHB1YmxpYyBhZGRDZWxsRW1vamkoZGF0YTogYW55KSB7XHJcbiAgICAgICAgY29uc3QgdWlkID0gZ2FtZUhwci5nZXRVaWQoKVxyXG4gICAgICAgIGlmICghZGF0YSB8fCBkYXRhLnVpZCA9PT0gdWlkKSB7IC8v5aaC5p6c5piv6Ieq5bex5Y+R55qEXHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBqc29uID0gYXNzZXRzTWdyLmdldEpzb25EYXRhKCdjaGF0RW1vamknLCBkYXRhLmVtb2ppKVxyXG4gICAgICAgIGlmICghanNvbikge1xyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5jZWxsRW1vamlzLnB1c2goe1xyXG4gICAgICAgICAgICB1aWQ6IGRhdGEudWlkIHx8IHVpZCxcclxuICAgICAgICAgICAgaW5kZXg6IGRhdGEuaW5kZXgsXHJcbiAgICAgICAgICAgIGVtb2ppOiBkYXRhLmVtb2ppLFxyXG4gICAgICAgICAgICBnZXRUaW1lOiBEYXRlLm5vdygpLFxyXG4gICAgICAgICAgICBkdXJhdGlvbjoganNvbi5hbmltX3RpbWUgfHwgMzAwMCxcclxuICAgICAgICB9KVxyXG4gICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuUExBWV9DRUxMX0VNT0pJKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOWPkemAgeWcsOWbvuihqOaDhVxyXG4gICAgcHVibGljIHNlbmRDZWxsRW1vamkoZW1vamk6IG51bWJlciwgaW5kZXg6IG51bWJlcikge1xyXG4gICAgICAgIGlmICghZW1vamkpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmNoZWNrT2Z0ZW5UaW1lKCkpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5zZW5kX2NoYXRfdG9sZXJhdGUnKVxyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLmxhc3RTZW5kQ2hhdFRpbWUgPSBEYXRlLm5vdygpXHJcbiAgICAgICAgLy8g5Y+R6YCB5Yiw5pyN5Yqh5ZmoXHJcbiAgICAgICAgZ2FtZUhwci5uZXQuc2VuZCgnZ2FtZS9IRF9TZW5kQ2VsbEVtb2ppJywgeyBlbW9qaSwgaW5kZXggfSlcclxuICAgICAgICAvLyDmnKzlnLDlhYjnm7TmjqXlj5FcclxuICAgICAgICB0aGlzLmFkZENlbGxFbW9qaSh7IGVtb2ppLCBpbmRleCB9KVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOajgOa1i+aYr+WQpuWPkemAgeW+l+Wkqumikee5geS6hlxyXG4gICAgcHJpdmF0ZSBjaGVja09mdGVuVGltZSgpIHtcclxuICAgICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXHJcbiAgICAgICAgaWYgKG5vdyAtIHRoaXMucmVzdFN0YXJ0VGltZSA8IHRoaXMuUkVTVF9NQVhfVElNRSkge1xyXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxyXG4gICAgICAgIH0gZWxzZSBpZiAobm93IC0gdGhpcy5sYXN0U2VuZENoYXRUaW1lIDwgdGhpcy5TRU5EX0lOVEVSVkFMKSB7XHJcbiAgICAgICAgICAgIHRoaXMudG9sZXJhdGVDb3VudCArPSAxXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhpcy50b2xlcmF0ZUNvdW50ID0gMFxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodGhpcy50b2xlcmF0ZUNvdW50ID49IHRoaXMuVE9MRVJBVEVfTUFYX0NPVU5UIC0gMSkge1xyXG4gICAgICAgICAgICB0aGlzLnJlc3RTdGFydFRpbWUgPSBub3dcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aGlzLnJlc3RTdGFydFRpbWUgPSAwXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOajgOa1i+S8keaBr+WJqeS9meaXtumXtFxyXG4gICAgcHVibGljIGNoZWNrUmVzdFN1cnBsdXNUaW1lKCkge1xyXG4gICAgICAgIGlmICh0aGlzLnJlc3RTdGFydFRpbWUgPD0gMCkge1xyXG4gICAgICAgICAgICByZXR1cm4gMFxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCB0aW1lID0gRGF0ZS5ub3coKSAtIHRoaXMucmVzdFN0YXJ0VGltZVxyXG4gICAgICAgIGlmICh0aW1lID49IHRoaXMuUkVTVF9NQVhfVElNRSkge1xyXG4gICAgICAgICAgICB0aGlzLnJlc3RTdGFydFRpbWUgPSAwXHJcbiAgICAgICAgICAgIHJldHVybiAwXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiB0aGlzLlJFU1RfTUFYX1RJTUUgLSB0aW1lXHJcbiAgICB9XHJcbn0iXX0=