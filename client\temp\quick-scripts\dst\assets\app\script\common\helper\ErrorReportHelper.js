
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ErrorReportHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4c51cxq8HpAX7DTGOqe/LgG', 'ErrorReportHelper');
// app/script/common/helper/ErrorReportHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorReportHelper = void 0;
var LocalConfig_1 = require("../LocalConfig");
var GameHelper_1 = require("./GameHelper");
var TaHelper_1 = require("./TaHelper");
var MIN_WAIT_TIME = 5000; //间隔上传到服务器 单位毫秒
var MAX_LEN = 10; //错误保留最多数量
var ONE_REPORT_SIZE = 4; //单次上报数量
var ErrorReportHelper = /** @class */ (function () {
    function ErrorReportHelper() {
        var _this = this;
        this.errors = [];
        this.errorMap = {};
        this.isReporting = false;
        this.reportWaitTime = MIN_WAIT_TIME;
        this.reportCountLimitMap = {}; //上报次数限制
        if (cc.sys.platform === cc.sys.WECHAT_GAME || typeof qq !== 'undefined') {
            wx.onError(function (error) { return _this.addException(JSON.stringify(error), 'wx'); });
        }
        else {
            window['__errorHandler'] = function (errorMessage, file, line, message, error) {
                _this.addException(JSON.stringify({ errorMessage: errorMessage, line: line, file: file, message: message, error: error, }), 'app');
            };
        }
        this.check();
    }
    ErrorReportHelper.prototype.addException = function (exception, type) {
        if (this.errors.length > MAX_LEN) {
            return;
        }
        var name = type + exception;
        if (!this.errorMap[name]) {
            cc.log('addException ' + type + ':', exception);
            this.errorMap[name] = true;
            this.errors.push({ type: type, exception: exception, level: this.level, time: Date.now() });
        }
    };
    ErrorReportHelper.prototype.check = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!true) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.waitTimeout(this.reportWaitTime)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, this.tryReport()];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 0];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    ErrorReportHelper.prototype.waitTimeout = function (delay) {
        return __awaiter(this, void 0, Promise, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return setTimeout(resolve, delay); })];
            });
        });
    };
    ErrorReportHelper.prototype.tryReport = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.isReporting || this.errors.length <= 0) {
                            return [2 /*return*/];
                        }
                        this.isReporting = true;
                        return [4 /*yield*/, this.report()];
                    case 1:
                        _a.sent();
                        this.isReporting = false;
                        return [2 /*return*/];
                }
            });
        });
    };
    ErrorReportHelper.prototype.report = function () {
        return __awaiter(this, void 0, void 0, function () {
            var errors, uid, sid, platform, os, version, url, res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        errors = this.errors.slice(0, ONE_REPORT_SIZE);
                        uid = GameHelper_1.gameHpr.getUid() || TaHelper_1.taHelper.getDistinctId();
                        sid = GameHelper_1.gameHpr.getSid() || 0;
                        platform = GameHelper_1.gameHpr.getRunPlatform();
                        os = TaHelper_1.taHelper.getOsAndVersion();
                        version = GameHelper_1.gameHpr.getVersion();
                        url = LocalConfig_1.localConfig.errorReportUrl.test;
                        if (GameHelper_1.gameHpr.isRelease) {
                            url = LocalConfig_1.localConfig.errorReportUrl[GameHelper_1.gameHpr.getServerArea()] || url;
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({ url: url, data: { uid: uid, sid: sid, platform: platform, os: os, errors: errors, version: version } })];
                    case 1:
                        res = _a.sent();
                        if ((res === null || res === void 0 ? void 0 : res.status) === 1) {
                            this.errors.splice(0, ONE_REPORT_SIZE);
                            this.reportWaitTime = MIN_WAIT_TIME;
                        }
                        else {
                            this.reportWaitTime = Math.min(this.reportWaitTime * 2, ut.Time.Minute);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    ErrorReportHelper.prototype.setCurLevel = function (level) {
        this.level = level;
    };
    // 主动上报
    ErrorReportHelper.prototype.reportError = function (name, message) {
        this.addException(JSON.stringify({ name: name, message: message }), 'diy');
    };
    // 检测次数限制
    ErrorReportHelper.prototype.checkCountLimit = function (key, max) {
        var count = this.reportCountLimitMap[key] || 0;
        if (count < max) {
            this.reportCountLimitMap[key] = count + 1;
            return true;
        }
        return false;
    };
    return ErrorReportHelper;
}());
exports.errorReportHelper = new ErrorReportHelper();
if (cc.sys.isBrowser) {
    window['errorReportHelper'] = exports.errorReportHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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