
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/Fighter.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b26caBsaQRJrKBzc2qUq0LA', 'Fighter');
// app/script/model/fsp/Fighter.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BehaviorTree_1 = require("../behavior/BehaviorTree");
var SearchRange_1 = require("../../common/astar/SearchRange");
var MapHelper_1 = require("../../common/helper/MapHelper");
var Enums_1 = require("../../common/constant/Enums");
var AStarRange_1 = require("../../common/astar/AStarRange");
var BuffObj_1 = require("../area/BuffObj");
var EventType_1 = require("../../common/event/EventType");
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
// 一个战斗单位
var Fighter = /** @class */ (function () {
    function Fighter() {
        this.entity = null;
        this.camp = 0; //所属阵营
        this.attackIndex = 0; //出手顺序
        this.waitRound = 0; //等待回合
        this.ctrl = null;
        this.searchRange = null;
        this.astar = null; //A星寻路
        this.behavior = null; //行为树
        this.blackboard = null; //携带行为树数据
        this.canAttackTargets = null; //当前所有可以攻击的目标
        this.canAttackFighters = [];
        this.attackTarget = null; //当前攻击目标
        this.roundCount = 0; //当前回合
        this.attackCount = 0; //攻击次数
        // public beTargetedList: Fighter[] = [] //以该单位为目标的攻击者列表
        // public attackTargetWeight: number = 0 //攻击目标权重
        this.tempRandomVal = 0;
        this.tempMaxSearchCount = 0; //临时的是最大搜索次数
        this.tempAttackRangePointMap = {}; //同一个位置和攻击范围 那么可攻击点位就一样
        this.tempMovePathMap = {}; //同一个位置和目标 那么移动路径就一样
        this.tempLastPoint = cc.v2(); //临时记录上一次的位置
        // // 添加以该单位为攻击目标的Fighter
        // public beTargetedAddFighter(ifighter: IFighter): boolean {
        //     const fighter = ifighter as Fighter
        //     if(this.beTargetedList.indexOf(fighter) < 0) {
        //         this.beTargetedList.add(fighter)
        //     }
        //     //权重从小到大排序
        //     this.beTargetedList.sort((a, b) => a.attackTargetWeight - b.attackTargetWeight)
        //     return true
        // }
        // // 移除以该单位为攻击目标的Fighter
        // public beTargetedRemoveFighter(ifighter: IFighter): boolean {
        //     const fighter = ifighter as Fighter
        //     this.beTargetedList.remove(fighter)
        //     return true
        // }
        // // 获取以该单位为攻击目标权重最小的Fighter
        // public getBeTargetedMinWeightFighter(): IFighter {
        //     return this.beTargetedList[0]
        // }
        // // 判断该单位被集火是战斗力是否足够
        // public checkTargetBeAttackedEnough(): boolean {
        //     let fightPointSum = 0
        //     let targetFightPointSum = 0
        //     for (let i = 0; i < this.beTargetedList.length; i++) {
        //         const fighter = this.beTargetedList[i];
        //         fightPointSum += fighter.getFightPower(this)
        //         targetFightPointSum += this.getFightPower(fighter)
        //     }
        //     return fightPointSum > targetFightPointSum
        // }
    }
    Fighter.prototype.md5 = function () {
        return this.attackIndex + '_' + this.getPoint().ID() + '_' + this.entity.id + '_' + this.camp + '_' + this.entity.curHp + '_' + this.entity.getMaxHp();
    };
    Fighter.prototype.strip = function () {
        var _a, _b;
        var res = {
            uid: this.entity.uid,
            camp: this.camp,
            attackIndex: this.attackIndex,
            waitRound: this.waitRound,
            roundCount: this.roundCount,
            attackCount: this.attackCount,
            attackTarget: (_b = (_a = this.attackTarget) === null || _a === void 0 ? void 0 : _a.getUid()) !== null && _b !== void 0 ? _b : '',
        };
        if (this.isFlag()) {
            res.isFalg = true;
            res.hp = [this.entity.curHp, this.entity.getMaxHp()];
            res.point = this.entity.point.toJson();
        }
        else if (this.getUid().startsWith('pet_')) { //宠物
            res.isPet = true;
            res.hp = [this.entity.curHp, this.entity.getMaxHp()];
            res.point = this.entity.point.toJson();
            res.owner = this.entity.owner;
            res.id = this.entity.id;
            res.lv = this.entity.lv;
        }
        else if (this.isNoncombat()) {
            res.isNoncombat = true;
            res.id = this.entity.id;
            res.lv = this.entity.lv;
            res.enterDir = this.entity.enterDir;
            res.point = this.entity.point.toJson();
            res.hp = [this.entity.curHp, this.entity.getMaxHp()];
            res.owner = this.entity.owner;
            res.attackCount = this.attackCount;
        }
        return res;
    };
    Fighter.prototype.init = function (pawn, data, ctrl) {
        var _a, _b, _c, _d;
        this.entity = pawn;
        this.camp = data.camp;
        this.attackIndex = (_a = data.attackIndex) !== null && _a !== void 0 ? _a : 0;
        this.waitRound = (_b = data.waitRound) !== null && _b !== void 0 ? _b : 0;
        this.roundCount = (_c = data.roundCount) !== null && _c !== void 0 ? _c : 0;
        this.attackCount = (_d = data.attackCount) !== null && _d !== void 0 ? _d : 0;
        this.ctrl = ctrl;
        this.blackboard = { 0: {} };
        this.behavior = new BehaviorTree_1.default().load(pawn.behaviorId, this);
        this.searchRange = new SearchRange_1.default().init(ctrl.checkIsBattleArea.bind(ctrl));
        this.astar = new AStarRange_1.default().init(ctrl.checkHasPassToState.bind(ctrl));
        this.tempLastPoint.set(pawn.point);
        if (this.roundCount === 0) {
            this.attackCount = 0;
            // 初始怒气
            this.entity.initAnger();
            // // 清空buff列表
            // if (!this.entity.isBattleing()) {
            //     this.entity.cleanAllBuffs()
            // }
        }
        return this;
    };
    // 开始战斗时的检测
    Fighter.prototype.checkBattleBeginTrigger = function () {
        var _this = this;
        if (this.roundCount !== 0) {
            return;
        }
        this.roundCount = 1;
        var uid = this.getUid(), heroSkill = this.getPortrayalSkill();
        var oldMaxHp = this.entity.getInitMaxHp();
        // 陌刀专属：前3次攻击最大值
        var INSTABILITY_ATTACK = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if ((INSTABILITY_ATTACK === null || INSTABILITY_ATTACK === void 0 ? void 0 : INSTABILITY_ATTACK.intensifyType) === 1) {
            this.addBuff(Enums_1.BuffType.STEADY_ATTACK, uid, 1).times = 3;
        }
        // 检测装备
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.LOW_HP_SHIELD) { //藤盾：检测血量低于50%增加护盾
                _this.addBuff(Enums_1.BuffType.CHECK_LOW_HP, uid, 1);
            }
            else if (m.type === Enums_1.EquipEffectType.LOW_HP_ATTACk) { //战斧：检测血量低于50%增加攻击力
                _this.addBuff(Enums_1.BuffType.CHECK_LOW_HP_ATTACK, uid, 1);
            }
            else if (m.type === Enums_1.EquipEffectType.BATTLE_BEGIN_SHIELD) { //玉佩：为2格内友军添加护盾
                _this.addBuff(Enums_1.BuffType.CHECK_JADE_PENDANT, uid, 1);
            }
            else if (m.type === Enums_1.EquipEffectType.CONTINUE_ACTION) { //沙漏：添加是否有行动2回合的buff
                _this.addBuff(Enums_1.BuffType.CONTINUE_ACTION, uid, 1).value = m.odds;
            }
            else if (m.type === Enums_1.EquipEffectType.THOUSAND_UMBRELLA) { //千机伞：随机翻倍
                _this.addBuffValue(Enums_1.BuffType.THOUSAND_UMBRELLA, uid, _this.ctrl.getRandom().get(0, 1));
            }
            else if (m.type === Enums_1.EquipEffectType.CENTERING_HELMET) { //定心盔: 每5回合恢复生命
                _this.addBuffValue(Enums_1.BuffType.CHECK_CENTERING_HELMET, uid, 5);
            }
            else if (m.type === Enums_1.EquipEffectType.CRIMSONGOLD_SHIELD) { //赤金盾: 每3回合获得护盾
                _this.addBuffValue(Enums_1.BuffType.CHECK_CRIMSONGOLD_SHIELD, uid, 3);
            }
            else if (m.type === Enums_1.EquipEffectType.OBSIDIAN_ARMOR) { //黑曜铠: 前几回合减伤
                _this.addBuffValue(Enums_1.BuffType.OBSIDIAN_ARMOR_DEFENSE, uid, m.value);
            }
        });
        // 周泰：濒死时概率恢复生命
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.ZHOU_TAI) {
            this.addBuff(Enums_1.BuffType.DYING_RECOVER, uid, 1);
        }
        // 乐进：先登闪避建筑攻击
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.YUE_JIN) {
            this.addBuff(Enums_1.BuffType.PRESTAGE, uid, 1);
        }
        // 高顺：陷阵加生命和攻击力
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.GAO_SHUN) {
            var camp_1 = this.camp, armyUid_1 = this.getArmyUid();
            this.ctrl.getFighters().forEach(function (m) {
                if (m.getCamp() === camp_1 && m.getPawnType() < Enums_1.PawnType.MACHINE && m.getArmyUid() === armyUid_1) {
                    var hp = m.getMaxHp();
                    m.addBuffValue(Enums_1.BuffType.BREAK_ENEMY_RANKS, uid, heroSkill.value);
                    var add = m.getMaxHp() - hp;
                    if (add > 0) {
                        var v = m.onHeal(add, false);
                        m.changeState(Enums_1.PawnState.HEAL, { val: v, time: 0 });
                    }
                }
            });
        }
        // 于禁：造成的伤害提高，受到的伤害提高
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.YU_JIN) {
            var camp_2 = this.camp, armyUid_2 = this.getArmyUid();
            this.ctrl.getFighters().forEach(function (m) {
                if (m.getCamp() === camp_2 && m.getPawnType() < Enums_1.PawnType.MACHINE && m.getArmyUid() === armyUid_2) {
                    m.addBuffValue(Enums_1.BuffType.RESOLUTE, uid, heroSkill.value);
                }
            });
        }
        // 孙尚香 每5回合释放技能
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.SUN_SHANGXIANG) {
            this.addBuffValue(Enums_1.BuffType.CHECK_LITTLE_GIRL, uid, heroSkill.params);
        }
        // 霍去病 跳斩回怒
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.HUO_QUBING) {
            var camp_3 = this.camp, armyUid_3 = this.getArmyUid();
            this.ctrl.getFighters().forEach(function (m) {
                if (m.getCamp() === camp_3 && m.getId() === 3401 && m.getArmyUid() === armyUid_3) {
                    m.addBuffValue(Enums_1.BuffType.CHECK_JUMPSLASH_ADD_ANGER, uid, heroSkill.value);
                }
            });
        }
        // 辛弃疾 回首
        if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.XIN_QIJI) {
            this.addBuffValue(Enums_1.BuffType.KERIAN, uid, heroSkill.value).setRound(heroSkill.params);
        }
        // 调整血量
        var newMaxHp = this.getMaxHp();
        if (oldMaxHp !== newMaxHp) {
            this.entity.curHp = Math.min(this.entity.curHp + Math.max(newMaxHp - oldMaxHp, 0), newMaxHp);
        }
    };
    // 回合开始
    Fighter.prototype.beginAction = function () {
        var _a, _b, _c;
        this.entity.actioning = true;
        this.blackboard = { 0: {} };
        this.cleanCanAttackTargets();
        if (!this.behavior.isCanRun()) { //如果不能行动
            return this.setRoundEnd();
        }
        else if (this.attackTarget) { //检测攻击目标是否还存在
            var camp_4 = this.camp, heroSkillId = (_a = this.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id;
            if (this.getEquipEffectByType(Enums_1.EquipEffectType.SILVER_SNAKE_WHIP)) {
                this.changeAttackTarget(null); //银蛇鞭 每次都要重置目标
            }
            else if (this.isHasBuffs(Enums_1.BuffType.CHAOS, Enums_1.BuffType.RAGE)) {
                this.changeAttackTarget(null); //混乱、狂怒 每次都要重置目标
            }
            else if (heroSkillId === Enums_1.HeroType.CAO_XIU || heroSkillId === Enums_1.HeroType.HUANG_ZHONG) {
                this.changeAttackTarget(null); //曹休 黄忠 每次都要重置目标
            }
            else if (this.attackTarget.isDie()) {
                this.changeAttackTarget(null); //如果目标死亡 就重置目标
            }
            else if (!this.attackTarget.isBuild()) {
                this.changeAttackTarget(this.ctrl.getFighter(this.attackTarget.getUid()));
            }
            else if (this.ctrl.getFighters().some(function (m) { return m.camp !== camp_4 && !m.isDie() && (m.isPawn() || m.isFlag()); })) {
                this.changeAttackTarget(null); //如果当前是建筑 那么看是否有敌对士兵可以攻击 就重置目标
            }
        }
        // 检测下回合是否可以继续 如果有就删除 没有就添加
        if (!this.removeBuff(Enums_1.BuffType.CONTINUE_ACTION)) {
            var odds = ((_b = this.getEquipEffectByType(Enums_1.EquipEffectType.CONTINUE_ACTION)) === null || _b === void 0 ? void 0 : _b.odds) || 0;
            if (odds > 0 && this.ctrl.getRandom().chance(odds)) {
                this.addBuff(Enums_1.BuffType.CONTINUE_ACTION, this.getUid(), 1);
                this.addRoundEndDelayTime(250);
            }
        }
        // 检测是否有机甲重盾皮肤 是否有立盾最后一回合
        if (((_c = this.entity) === null || _c === void 0 ? void 0 : _c.skinId) === 3202105) {
            var buff = this.getBuff(Enums_1.BuffType.STAND_SHIELD);
            if (buff && buff.round <= 1) {
                this.addRoundEndActionTime(770, 770);
                this.getBlackboard(0)['isPlayJJShieldEnd'] = true;
            }
        }
        // 检测是否有鸩毒
        var POISONED_WINE = this.getBuff(Enums_1.BuffType.POISONED_WINE);
        if (POISONED_WINE && POISONED_WINE.round <= 1) {
            this.addRoundEndActionTime(770, 770);
            this.getBlackboard(0)['isPoisonedWineEnd'] = true;
        }
        cc.log(this.ctrl.getCurrentFrameIndex() + ' >>>>>>>>> ' + this.getId() + '(' + this.getPoint().Join() + ') [' + this.getUid() + '] ' + this.attackIndex + ' ' + this.camp + ' ' + this.ctrl.getRandom().seed);
    };
    Fighter.prototype.endAction = function () {
        this.roundCount += 1;
        this.entity.actioning = false;
        this.cleanCanAttackTargets();
        this.cleanBlackboard();
        this.cleanRoundBuffs();
        cc.log(this.ctrl.getCurrentFrameIndex() + ' <<<<<<<<< ' + this.getId() + '(' + this.getPoint().Join() + ') [' + this.getUid() + '] ');
        cc.log('-');
    };
    // 清理持续一回合的buff
    Fighter.prototype.cleanRoundBuffs = function () {
        this.removeMultiBuff(Enums_1.BuffType.HIT_SUCK_BLOOD, Enums_1.BuffType.ROYAL_BLUE_DODGE, Enums_1.BuffType.ROYAL_BLUE_DAMAGE);
        // 虎痴 每回合衰减20%
        var buff = this.getBuff(Enums_1.BuffType.TIGER_MANIA);
        if (buff) {
            buff.value -= 20;
            if (buff.value <= 0) {
                this.removeBuff(Enums_1.BuffType.TIGER_MANIA);
            }
        }
        // 三斧 增加次数
        buff = this.getBuff(Enums_1.BuffType.THREE_AXES);
        if (!buff) {
        }
        else if (buff.value < 3) {
            buff.value += 1;
        }
        else {
            this.removeBuff(Enums_1.BuffType.THREE_AXES);
        }
    };
    // 刷新buff
    Fighter.prototype.updateBuff = function () {
        var tondenBuff = null, isObsidianArmorDefense = false, isKerian = false, isSorrowfulDream = false;
        var anticipationDefenseValue = 0;
        var buffs = this.entity.buffs || [];
        for (var i = buffs.length - 1; i >= 0; i--) {
            var m = buffs[i];
            if (m.type === Enums_1.BuffType.SORROWFUL_DREAM) {
                isSorrowfulDream = true; //是否有 愁梦
            }
            if (!m.updateRound(-1)) {
                continue;
            }
            else if (m.type === Enums_1.BuffType.TONDEN_BEGIN) {
                tondenBuff = m; //屯垦令结束
            }
            else if (m.type === Enums_1.BuffType.OBSIDIAN_ARMOR_DEFENSE) {
                isObsidianArmorDefense = true; //黑曜铠 防御结束
            }
            else if (m.type === Enums_1.BuffType.KERIAN) {
                isKerian = true; //金戈结束
            }
            else if (m.type === Enums_1.BuffType.ANTICIPATION_DEFENSE) {
                anticipationDefenseValue = m.value; //李牧 蓄势减伤 结束
            }
            buffs.splice(i, 1);
        }
        var uid = this.getUid();
        // 屯垦令结束 添加恢复buff
        if (tondenBuff) {
            this.addBuffValue(Enums_1.BuffType.TONDEN_RECOVER, tondenBuff.provider, tondenBuff.value);
        }
        // 黑曜铠减伤结束
        if (isObsidianArmorDefense) {
            this.addBuff(Enums_1.BuffType.OBSIDIAN_ARMOR_NEGATIVE, uid, 1);
        }
        // 金戈 结束了 看是否有愁梦 如果没有就添加
        if (isKerian && !isSorrowfulDream) {
            this.addBuff(Enums_1.BuffType.SORROWFUL_DREAM, uid, 1);
        }
        // 蓄势 结束了 添加攻击
        if (anticipationDefenseValue > 0) {
            this.addBuffValue(Enums_1.BuffType.ANTICIPATION_ATTACK, uid, anticipationDefenseValue);
        }
    };
    // 执行行为树
    Fighter.prototype.behaviorTick = function (dt) {
        this.behavior.tick(dt);
    };
    Fighter.prototype.getBlackboard = function (id) {
        var data = this.blackboard[id];
        if (!data) {
            data = this.blackboard[id] = {};
        }
        return data;
    };
    Fighter.prototype.cleanBlackboard = function () {
        var retainKeys = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            retainKeys[_i] = arguments[_i];
        }
        var retainMap = {};
        var data = this.blackboard[0];
        if (data) {
            retainKeys.forEach(function (k) { return retainMap[k] = data[k]; });
        }
        this.blackboard = { 0: retainMap };
    };
    Fighter.prototype.isCanRun = function () {
        var _a;
        return !!((_a = this.behavior) === null || _a === void 0 ? void 0 : _a.isCanRun());
    };
    // 是否回合结束了
    Fighter.prototype.isRoundEnd = function () {
        return !!this.getBlackboard(0)['isRoundEnd'] || this.isDie();
    };
    // 设置回合结束
    Fighter.prototype.setRoundEnd = function () {
        this.getBlackboard(0)['isRoundEnd'] = true;
    };
    // 添加回合结束延迟时间
    Fighter.prototype.addRoundEndDelayTime = function (val) {
        var data = this.getBlackboard(0);
        var time = data['addRoundEndDelayTime'] || 0;
        data['addRoundEndDelayTime'] = time + val;
    };
    // 添加回合结束后的动作时间
    Fighter.prototype.addRoundEndActionTime = function (hitTime, sumTime) {
        this.getBlackboard(0)['addRoundEndActionHitTime'] = hitTime;
        this.addRoundEndDelayTime(sumTime);
    };
    // 设置战斗结束延迟时间
    Fighter.prototype.setBattleOverDelay = function (val) {
        this.getBlackboard(0)['battleOverDelay'] = val;
    };
    Fighter.prototype.updateBattleOver = function (dt) {
        var time = this.getBlackboard(0)['battleOverDelay'] || 0;
        if (time === 0) {
            return false;
        }
        time -= dt;
        if (time <= 0) {
            this.getBlackboard(0)['battleOverDelay'] = -1;
        }
        else {
            this.getBlackboard(0)['battleOverDelay'] = time;
        }
        return true;
    };
    Fighter.prototype.isBattleOver = function () {
        return this.getBlackboard(0)['battleOverDelay'] == -1;
    };
    // 设置位置
    Fighter.prototype.setPoint = function (point) {
        if (point) {
            this.tempLastPoint.set(this.entity.point);
            this.entity.point.set2(point.x, point.y);
        }
    };
    Fighter.prototype.cleanCanAttackTargets = function () {
        this.canAttackTargets = null;
        this.canAttackFighters = null;
        this.tempAttackRangePointMap = null;
        this.tempMovePathMap = null;
    };
    Fighter.prototype.getCanAttackFighters = function () {
        if (!this.canAttackFighters) {
            this.getCanAttackTargets();
        }
        return this.canAttackFighters || [];
    };
    // 获取攻击列表
    Fighter.prototype.getCanAttackTargets = function () {
        var _this = this;
        var _a;
        if (this.canAttackTargets) {
            return this.canAttackTargets;
        }
        // console.log('getCanAttackTargets uid: ' + this.getUid());
        this.tempMaxSearchCount = 1000;
        this.canAttackTargets = [];
        this.canAttackFighters = [];
        this.tempAttackRangePointMap = {};
        this.tempMovePathMap = {};
        var camp = this.camp, isChaos = this.ctrl.getRandom().chance(this.getBuffValue(Enums_1.BuffType.CHAOS));
        var uid = this.getUid();
        this.ctrl.getFighters().forEach(function (m) {
            if (m.getUid() === uid) {
                return;
            }
            var can = isChaos ? m.camp === camp : m.camp !== camp;
            if (can && !m.isDie() && (m.isPawn() || m.isFlag())) {
                _this.canAttackFighters.push(m);
            }
        });
        // 如果没有攻击对象了 那么看是否可以攻击中心
        if (this.canAttackFighters.length === 0 && this.ctrl.getMainCamp() !== this.camp) {
            this.ctrl.getMainDoors().forEach(function (m) { return _this.canAttackFighters.push(m); });
        }
        // 先排个距离序 为了后面更效率的计算权重
        var point = this.getPoint();
        this.canAttackFighters.sort(function (a, b) {
            var wa = MapHelper_1.mapHelper.getPointToPointDis(a.getPoint(), point) * 1000 + a.getAttackIndex();
            var wb = MapHelper_1.mapHelper.getPointToPointDis(b.getPoint(), point) * 1000 + b.getAttackIndex();
            return wa - wb;
        });
        // 开始计算权重
        var heroSkillId = (_a = this.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id;
        var isCx = heroSkillId === Enums_1.HeroType.CAO_XIU, ishz = heroSkillId === Enums_1.HeroType.HUANG_ZHONG, isTower = this.isTower();
        this.canAttackFighters.forEach(function (m) { return _this.addCanAttackTarget(m, isCx, ishz, isTower); });
        // 根据权重排序 找出最优的目标
        this.canAttackTargets.sort(function (a, b) { return b.weight - a.weight; });
        // this.canAttackTargets.forEach(m => {
        //     cc.log(m.point.ID(), m.weight)
        // })
        // cc.log('--------------------------------------------')
        return this.canAttackTargets;
    };
    // 获取可攻击的点位
    Fighter.prototype.getTargetCanAttackPoints = function (target) {
        var _this = this;
        var attackRange = this.getAttackRange();
        var attackRangeKey = target.getUid() + '_' + attackRange;
        var points = this.tempAttackRangePointMap[attackRangeKey];
        if (!points) {
            points = [];
            var pointMap_1 = {};
            target.getPoints().forEach(function (targetPoint) { return _this.searchRange.search(targetPoint, attackRange).forEach(function (m) {
                var id = m.ID();
                if (!pointMap_1[id]) {
                    pointMap_1[id] = true;
                    points.push(m);
                }
            }); });
            // 筛选位置
            var point_1 = this.getPoint();
            points = points.filter(function (m) { return m.equals(point_1) || !_this.ctrl.checkHasFighter(m.x, m.y); });
            this.tempAttackRangePointMap[attackRangeKey] = points;
        }
        return points;
    };
    Fighter.prototype.addCanAttackTarget = function (target, isCx, isHz, isTower) {
        var _a;
        var targetPoint = target.getPoint();
        var point = this.entity.point;
        var attackRange = this.getAttackRange(), moveRange = this.getMoveRange();
        var addTarget = true;
        // 获取距离
        var dis = this.getMinDis(target);
        // 是否克制
        var isRestrain = ((_a = this.getSkillByType(Enums_1.PawnSkillType.ATTACK_RESTRAIN)) === null || _a === void 0 ? void 0 : _a.target) === target.getPawnType();
        // 计算权重分
        var weight = 0, paths = [], moveWeight = 0;
        var attackTargetPoint;
        if (dis <= attackRange) {
            if (isCx) {
                weight = 2000000 + dis; //在攻击范围内 就只看距离 曹休看最远的
            }
            else if (isHz) {
                weight = 2000000 + Math.floor((1 - target.getHpRatio()) * 100); //看生命比最低的
                weight = weight * 100 + (99 - dis);
            }
            else if (isTower) {
                weight = 2000000 + (target.isHasBuff(Enums_1.BuffType.PRESTAGE) ? 1 : 0); //箭塔优先攻击有先登的目标
                weight = weight * 100 + (99 - dis);
            }
            else {
                weight = 2000000 + (99 - dis); //在攻击范围内 就只看距离
            }
            this.tempMaxSearchCount = 0; //如果已经有在攻击范围内的了就不要在搜索了
        }
        else if (this.tempMaxSearchCount > 0 && moveRange > 0) {
            var points = this.getTargetCanAttackPoints(target);
            var data = this.findPaths(point, points, moveRange, this.getSearchDir(target), targetPoint);
            if (!data.targetPoint) {
                // console.log('abandomTarget fighterUid: ' + this.getUid() + ' tagetUid: ' + target.getUid());
                //未获取到锁定点位 放弃该目标
                return;
            }
            if (data.frontWeight === 0) {
                data.frontWeight = 99 - dis; //无路可走看距离
            } /*  else if (data.paths.length > 0) {
                const pdis = mapHelper.getPointToPointDis(targetPoint, data.paths.last()) //这里是看路径最后一个位置和目标的距离
                data.frontWeight = data.frontWeight * 100 + (99 - pdis)
            } */
            weight = 1000000 + data.frontWeight;
            paths = data.paths;
            attackTargetPoint = data.targetPoint;
            moveWeight = data.moveWeight;
        }
        else {
            weight = 1000000 + (99 - dis); //只看距离
        }
        // weight = weight * 10 + (isRestrain ? 1 : 0)
        // weight = weight * 100 + points.length
        weight = weight * 10 + target.getAttackSpeed();
        weight = weight * 1000 + (999 - target.getAttackIndex());
        // 判断目标被集火的战斗力是否足够 未在攻击范围内的才做火力判断
        // if (target.checkTargetBeAttackedEnough() && dis > attackRange) {
        //     const ifighter = target.getBeTargetedMinWeightFighter()
        //     if (ifighter) {
        //         const minWFighter = ifighter as Fighter
        //         if (weight <= minWFighter.attackTargetWeight) {
        //             // 火力足够且权重小于目标列表最小权重 放弃该目标
        //             addTarget = false
        //         }
        //     }
        // }
        if (addTarget) {
            this.canAttackTargets.push({
                target: target,
                uid: target.getUid(),
                point: targetPoint,
                dis: dis,
                paths: paths,
                // points: points,
                attackIndex: target.getAttackIndex(),
                isRestrain: isRestrain,
                weight: weight,
                attackTargetPoint: attackTargetPoint,
                moveWeight: moveWeight
            });
        }
    };
    // 找出最佳路径
    Fighter.prototype.findPaths = function (point, points, moveRange, searchDir, targetFighterPoint) {
        var _a, _b;
        var camp = this.camp;
        var areaSize = this.ctrl.getAreaSize();
        var paths = [], weight = 0, frontWeight = 0, targetPoint = null, moveWeight = 0, lockWeight = 0;
        for (var i = 0, l = points.length; i < l; i++) {
            var target = points[i];
            var searchKey = point.ID() + '_' + target.ID() + '_' + searchDir + '_' + moveRange + '_' + camp;
            var pathsArray = this.tempMovePathMap[searchKey];
            if (!pathsArray) {
                pathsArray = this.astar.search(point, target, searchDir, moveRange, camp, this.tempMaxSearchCount);
                this.tempMovePathMap[searchKey] = pathsArray;
            }
            var pLen = pathsArray.length;
            if (pLen === 0) {
                continue;
            }
            else if (!((_b = (_a = pathsArray.last()) === null || _a === void 0 ? void 0 : _a.last()) === null || _b === void 0 ? void 0 : _b.equals(target))) {
                continue; //到不了就不动了
            }
            if (pLen < this.tempMaxSearchCount) {
                this.tempMaxSearchCount = pLen; //记录最小搜索次数
            }
            var w = 99 - pLen; //最短回合数
            w = w * 100 + this.getPathArrayWeight(pathsArray);
            w = w * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(pathsArray[0].last(), targetFighterPoint)); // 路径最后一个位置 和 目标位置 的距离  越近越好
            var fw = w; //外部最高优先级
            w = w * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point, target)); //自己起点位置 和 攻击目标位置 的距离  越近越好
            w = w * 1000 + target.toIndex(areaSize);
            var lw = w;
            // w = w * 100000 + this.getFightPower(targetFighter)            
            if (pLen > 1) {
                //大于1回合到达则与锁定该点的单位比较权重
                var lockPointInfo = this.ctrl.getLockMovePointFighter(target);
                // if (lockPointInfo) {
                //     console.log('findPaths pointLock uid: ' + lockPointInfo.fighter?.getUid() + ' point: ' + target + ' w: ' + w + ' tw: ' + lockPointInfo?.weight);                
                // }
                if (lockPointInfo && lockPointInfo.fighter !== this && lockPointInfo.fighter.getCamp() === this.getCamp() && (lockPointInfo === null || lockPointInfo === void 0 ? void 0 : lockPointInfo.weight) >= w) {
                    //目标点已被同阵营其他单位锁定 当前权重更小 则移除锁定点
                    lw = 0;
                    w = 0;
                    if (this.tempMaxSearchCount === pLen) {
                        // 最大搜索次数重置为默认
                        this.tempMaxSearchCount = 1000;
                    }
                }
            }
            if (lw > lockWeight) {
                lockWeight = lw;
                targetPoint = target;
            }
            if (w > weight) {
                weight = w;
                frontWeight = fw;
                paths = pathsArray[0] || [];
                //计算移动后的权重
                if (pLen === 1) {
                    moveWeight = 2000000000 + (100 - pathsArray[0].length);
                }
                else {
                    moveWeight = 99 - (pLen - 1);
                    moveWeight = moveWeight * 100 + (pathsArray.length - 1 === 1 ? 9 : pathsArray[1].length - 1) * 10 + (10 - pathsArray[pLen - 2].length + 1);
                    moveWeight = moveWeight * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(pathsArray[1][0], target)); //移动后离目标的距离
                    moveWeight = moveWeight * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point, target)); //自己起点位置 和 攻击目标位置 的距离  越近越好
                    moveWeight = moveWeight * 1000 + target.toIndex(areaSize);
                }
            }
        }
        return { paths: paths, frontWeight: frontWeight, targetPoint: targetPoint, weight: weight, moveWeight: moveWeight };
    };
    // 获取路径段权重
    // 暂时只看第一段和最后一段 第一段长越好 最后一段越短越好
    Fighter.prototype.getPathArrayWeight = function (pathsArray) {
        var w = pathsArray.length === 1 ? 9 : pathsArray[0].length - 1;
        return w * 10 + (10 - pathsArray.last().length + 1);
    };
    // 获取可以被击退的点
    Fighter.prototype.getCanByReqelPoint = function (point, range) {
        var _this = this;
        var p = this.getPoint();
        var d = MapHelper_1.mapHelper.getPointToPointDis(point, p);
        // 先找出目标周围可攻击的位置
        var points = this.searchRange.search(p, range);
        // 筛选位置
        points = points.filter(function (m) { return !_this.ctrl.checkHasFighter(m.x, m.y) && MapHelper_1.mapHelper.getPointToPointDis(point, m) > d; });
        if (points.length > 0) {
            var areaSize_1 = this.ctrl.getAreaSize();
            points.sort(function (a, b) {
                var aw = point.sub(a).magSqr() * 1000 + a.toIndex(areaSize_1);
                var bw = point.sub(b).magSqr() * 1000 + b.toIndex(areaSize_1);
                return bw - aw;
            });
            return points[0];
        }
        return null;
    };
    // 是否在攻击范围
    Fighter.prototype.checkInAttackRange = function (target, attackRange) {
        return this.getPoints().some(function (point) { return target.getPoints().some(function (m) { return MapHelper_1.mapHelper.getPointToPointDis(point, m) <= attackRange; }); });
    };
    Fighter.prototype.checkInMyAttackRange = function (target) {
        return this.checkInAttackRange(target, this.getAttackRange());
    };
    // 获取最近距离
    Fighter.prototype.getMinDis = function (target) {
        var minDis = MapHelper_1.mapHelper.getPointToPointDis(this.getPoint(), target.getPoint());
        if (this.isBoss() || target.isBoss()) {
            var targetPoints_1 = target.getPoints();
            this.getPoints().forEach(function (point) { return targetPoints_1.forEach(function (m) {
                var dis = MapHelper_1.mapHelper.getPointToPointDis(point, m);
                if (dis < minDis) {
                    minDis = dis;
                }
            }); });
        }
        return minDis;
    };
    // 获取可范围攻击的目标 只有士兵
    Fighter.prototype.getCanAttackPawnByRange = function (fighters, rang, cnt, ignoreUid) {
        return this.getCanAttackRangeFighter(fighters, rang, cnt, ignoreUid, function (m) { return m.isFlag(); });
    };
    // 获取可范围攻击的目标 包含军旗
    Fighter.prototype.getCanAttackFighterByRange = function (fighters, rang, cnt, ignoreUid) {
        return this.getCanAttackRangeFighter(fighters, rang, cnt, ignoreUid, null);
    };
    Fighter.prototype.getCanAttackRangeFighter = function (fighters, rang, cnt, ignoreUid, cb) {
        var _a = this.getCanAttackRangeTargets(this.getPoint(), fighters, rang, ignoreUid, cb), targets = _a.targets, overlaps = _a.overlaps;
        if (targets.length < cnt && overlaps.length > 0) {
            overlaps.sort(function (a, b) { return a.getTempRandomVal() - b.getTempRandomVal(); });
            targets.pushArr(overlaps.slice(0, cnt - targets.length));
            return targets;
        }
        return targets.slice(0, cnt);
    };
    Fighter.prototype.getCanAttackRangeFighterByPoint = function (point, fighters, rang, cnt, cb) {
        var _a = this.getCanAttackRangeTargets(point, fighters, rang, '', cb), targets = _a.targets, overlaps = _a.overlaps;
        if (targets.length < cnt && overlaps.length > 0) {
            overlaps.sort(function (a, b) { return a.getTempRandomVal() - b.getTempRandomVal(); });
            targets.pushArr(overlaps.slice(0, cnt - targets.length));
            return targets;
        }
        return targets.slice(0, cnt);
    };
    Fighter.prototype.getCanAttackRangeTargets = function (point, fighters, rang, ignoreUid, cb) {
        if (fighters.length === 0) {
            return { targets: [], overlaps: [] };
        }
        var targets = [];
        var pointMap = {}, overlaps = [];
        var random = this.ctrl.getRandom();
        fighters.forEach(function (m) {
            var p = m.getPoint(), id = p.ID();
            if (m.getUid() === ignoreUid || m.isDie() || (!m.isPawn() && !m.isFlag()) || MapHelper_1.mapHelper.getPointToPointDis(point, p) > rang) {
                return;
            }
            else if (cb && cb(m)) {
                return;
            }
            else if (!pointMap[id]) {
                pointMap[id] = true;
                targets.push(m); //优先选择没有重叠的
            }
            else {
                m.setTempRandomVal(random.get(1, 100) * 10000 + m.getAttackIndex());
                overlaps.push(m); //这里就是重叠一起的
            }
        });
        return { targets: targets, overlaps: overlaps };
    };
    Fighter.prototype.setAttackTarget = function (val) {
        this.attackTarget = val;
    };
    // 切换攻击目标
    Fighter.prototype.changeAttackTarget = function (val) {
        var _a, _b;
        var oldUid = (_a = this.attackTarget) === null || _a === void 0 ? void 0 : _a.getUid(), newUId = val === null || val === void 0 ? void 0 : val.getUid();
        this.attackTarget = val;
        // 切换目标
        if (oldUid !== newUId && !!val) {
            // 程咬金 记录目标
            if (((_b = this.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) !== Enums_1.HeroType.CHENG_YAOJIN) {
            }
            else if (val.isPawn()) {
                var buff = this.getBuff(Enums_1.BuffType.THREE_AXES);
                if (!buff) {
                    this.addBuffValue(Enums_1.BuffType.THREE_AXES, newUId, 1);
                }
                else if (buff.provider !== newUId) {
                    buff.provider = newUId;
                    buff.value = 1;
                }
            }
            else {
                this.removeBuff(Enums_1.BuffType.THREE_AXES);
            }
        }
        return this.attackTarget;
    };
    Fighter.prototype.getAreaIndex = function () { return this.entity.aIndex; };
    Fighter.prototype.getEnterDir = function () { var _a, _b; return (_b = (_a = this.entity) === null || _a === void 0 ? void 0 : _a.enterDir) !== null && _b !== void 0 ? _b : -1; };
    Fighter.prototype.getId = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.id; };
    Fighter.prototype.getUid = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.uid; };
    Fighter.prototype.getOwner = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.owner; };
    Fighter.prototype.getArmyUid = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.armyUid; };
    Fighter.prototype.getArmyName = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.armyName; };
    Fighter.prototype.getName = function () { var _a; return ((_a = this.entity) === null || _a === void 0 ? void 0 : _a.name) || ''; };
    Fighter.prototype.getLv = function () { return this.entity.lv; };
    Fighter.prototype.getCamp = function () { return this.camp; };
    Fighter.prototype.getSearchRange = function () { return this.searchRange; };
    Fighter.prototype.getPoint = function () { return this.entity.point; };
    Fighter.prototype.getLastPoint = function () { return this.tempLastPoint; };
    Fighter.prototype.getPawnType = function () { return this.entity.type; };
    Fighter.prototype.getSkillByType = function (type) { return this.entity.getSkillByType(type); };
    Fighter.prototype.getActiveSkill = function () { return this.entity.getActiveSkill(); };
    Fighter.prototype.getAttack = function () { return this.entity.attack; };
    Fighter.prototype.getInstabilityMaxAttack = function () { return this.entity.getInstabilityMaxAttack(); };
    Fighter.prototype.getAttackRange = function () { return this.entity.getAttackRange(); };
    Fighter.prototype.getMoveRange = function () { return this.entity.getMoveRange(); };
    Fighter.prototype.getAttackIndex = function () { return this.attackIndex; };
    Fighter.prototype.getAttackSpeed = function () { return this.entity.attackSpeed; };
    Fighter.prototype.getCurHp = function () { return this.entity.curHp; };
    Fighter.prototype.getMaxHp = function () { return this.entity.getMaxHp(); };
    Fighter.prototype.amendMaxHp = function (ignoreBuffType) { return this.entity.amendMaxHp(ignoreBuffType); };
    Fighter.prototype.getHpRatio = function () { return this.entity.getHpRatio(); };
    Fighter.prototype.getEquip = function () { return this.entity.equip; };
    Fighter.prototype.getEquipEffects = function () { return this.entity.getEquipEffects(); };
    Fighter.prototype.getEquipEffectByType = function (type) { return this.entity.getEquipEffectByType(type); };
    Fighter.prototype.getPortrayal = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.portrayal; };
    Fighter.prototype.getPortrayalSkill = function () { var _a, _b; return (_b = (_a = this.entity) === null || _a === void 0 ? void 0 : _a.portrayal) === null || _b === void 0 ? void 0 : _b.skill; };
    Fighter.prototype.getAttackTarget = function () { return this.attackTarget; };
    Fighter.prototype.setTempRandomVal = function (val) { this.tempRandomVal = val; };
    Fighter.prototype.getTempRandomVal = function () { return this.tempRandomVal; };
    // 初始化技能的攻击信息配置
    Fighter.prototype.initSkillAttackAnimTime = function () {
        var _a;
        (_a = this.entity.getActiveSkill()) === null || _a === void 0 ? void 0 : _a.initAttackAnimTime(this.entity);
    };
    Fighter.prototype.isPawn = function () { return !this.isBuild() && !this.isTower() && !this.isNoncombat() && !this.isFlag(); };
    Fighter.prototype.isBuild = function () { return this.getPawnType() === Enums_1.PawnType.BUILD; };
    Fighter.prototype.isTower = function () { return this.getPawnType() === Enums_1.PawnType.TOWER; };
    Fighter.prototype.isNoncombat = function () { return this.getPawnType() === Enums_1.PawnType.NONCOMBAT; };
    Fighter.prototype.isFlag = function () { return this.getId() === 3601; };
    Fighter.prototype.isHero = function () { var _a; return !!((_a = this.entity) === null || _a === void 0 ? void 0 : _a.isHero()); };
    Fighter.prototype.isPet = function () { var _a; return !!((_a = this.entity) === null || _a === void 0 ? void 0 : _a.uid.startsWith('pet_')); };
    Fighter.prototype.isBoss = function () { return this.entity.isBoss(); };
    Fighter.prototype.getPoints = function () {
        if (this.isBoss()) {
            return this.ctrl.getAreaMainPoints();
        }
        return [this.getPoint()];
    };
    Fighter.prototype.isDie = function () {
        return this.entity.isDie();
    };
    Fighter.prototype.isFullHp = function () {
        return this.entity.curHp >= this.entity.getMaxHp();
    };
    Fighter.prototype.setLv = function (val) {
        this.entity.lv = val;
    };
    Fighter.prototype.updateLv = function (val) {
        this.entity.lv = val;
        this.entity.updateAttrJson();
    };
    Fighter.prototype.updateTowerInfo = function (id, lv) {
    };
    Fighter.prototype.amendAttack = function (val) {
        return this.entity.amendAttack(val);
    };
    // 获取实际攻击力
    Fighter.prototype.getActAttack = function () {
        return this.entity.amendAttack(this.entity.attack);
    };
    Fighter.prototype.getIgnoreBuffAttack = function (type) {
        return this.entity.amendAttack(this.entity.attack, type);
    };
    // 获取不稳定攻击的随机攻击力 目前只限陌刀
    Fighter.prototype.getInstabilityRandomAttack = function (index) {
        var minDamage = this.getActAttack(), maxDamage = this.getInstabilityMaxAttack();
        var val = Math.round((maxDamage - minDamage) / 3);
        var a = minDamage + val, b = minDamage + val * 2;
        var arr = [[minDamage, a], [a + 1, b], [b + 1, maxDamage], [maxDamage, maxDamage]];
        var v = arr[cc.misc.clampf(index - 1, 0, 3)];
        return this.ctrl.getRandom().get(v[0], v[1]);
    };
    Fighter.prototype.getSearchDir = function (attackTarget) {
        attackTarget = attackTarget || this.attackTarget;
        if (!attackTarget) {
            return this.getEnterDir();
        }
        var dirA = this.getEnterDir(), dirB = attackTarget.getEnterDir();
        if (dirA === -1) { //自己没有方向 去目标的反方向
            return (dirB + 2) % 4;
        }
        return dirA;
    };
    Fighter.prototype.addAttackCount = function (val) {
        this.attackCount += val;
    };
    // 切换状态
    Fighter.prototype.changeState = function (state, data) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        data = data || {};
        data.appositionPawnCount = this.ctrl.getFighterCountByPoint(this.entity.point); //同位置士兵数量
        this.entity.changeState(state, data);
        var index = this.getAreaIndex(), uid = this.getUid();
        if (state === Enums_1.PawnState.HIT) {
            var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
            var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
            var isCrit = !!data.isCrit; //暴击
            var heal = (_c = data.heal) !== null && _c !== void 0 ? _c : 0; //回复
            var isDodge = damage === -1; //闪避
            var isParry = damage === -2; //格挡
            var isTurntheblade = damage === -3; //招架
            var isWithstand = damage === -4; //抵挡技能
            damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage;
            eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: index, uid: uid, value: -damage, trueDamage: -trueDamage, heal: heal, isCrit: isCrit, isDodge: isDodge, isParry: isParry, isTurntheblade: isTurntheblade, isWithstand: isWithstand, hasShield: !!((_d = this.entity) === null || _d === void 0 ? void 0 : _d.getShieldValue()) });
        }
        else if (state === Enums_1.PawnState.HEAL && data.val > 0) {
            eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: index, uid: uid, value: data.val });
        }
        else if (state === Enums_1.PawnState.DEDUCT_HP && (!!data.damage || !!data.trueDamage)) {
            var damage = (_e = data.damage) !== null && _e !== void 0 ? _e : 0;
            var trueDamage = (_f = data.trueDamage) !== null && _f !== void 0 ? _f : 0;
            var heal = (_g = data.heal) !== null && _g !== void 0 ? _g : 0; //回复
            eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: index, uid: uid, value: -damage, trueDamage: -trueDamage, heal: heal, hasShield: !!((_h = this.entity) === null || _h === void 0 ? void 0 : _h.getShieldValue()) });
        }
        else if (state === Enums_1.PawnState.ADD_ANGER && !!data.val) {
            eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: index, uid: uid, value: data.val, isAnger: true });
        }
    };
    // 受击前处理
    Fighter.prototype.hitPrepDamageHandle = function (damage, trueDamage) {
        if (damage <= 0 && trueDamage <= 0) {
            return { damage: damage, trueDamage: trueDamage };
        }
        // 检测受击伤害提升
        var hitDamageAmend = 1.0;
        if (this.getEquipEffectByType(Enums_1.EquipEffectType.DOUBLE_EDGED_SWORD)) {
            hitDamageAmend += 1; //月牙
        }
        // 检测buff
        this.getBuffs().forEach(function (m) {
            if (m.type === Enums_1.BuffType.ARMOR_PENETRATION) { //破甲
                hitDamageAmend += (m.value * m.lv) * 0.01;
            }
            else if (m.type === Enums_1.BuffType.PARALYSIS_UP) { //毒弓 易损
                hitDamageAmend += m.value;
            }
            else if (m.type === Enums_1.BuffType.RESOLUTE) { //于禁 毅重
                hitDamageAmend += m.value * 0.01;
            }
            else if (m.type === Enums_1.BuffType.OBSIDIAN_ARMOR_NEGATIVE) { //黑曜铠
                hitDamageAmend += m.value * 0.01;
            }
        });
        // 计算
        if (damage > 0) {
            damage = Math.round(damage * hitDamageAmend);
        }
        if (trueDamage > 0) {
            trueDamage = Math.round(trueDamage * hitDamageAmend);
        }
        return { damage: damage, trueDamage: trueDamage };
    };
    // 受击
    Fighter.prototype.onHit = function (damage, attackerOwners) {
        if (damage <= 0) {
            return { damage: damage, heal: 0, hitShield: 0 };
        }
        var val = damage, heal = 0, hitShield = 0;
        // 获取护盾buff
        var buffs = this.getShieldBuffs();
        for (var i = buffs.length - 1; i >= 0; i--) {
            var buff = buffs[i];
            if (val >= buff.value) {
                val -= Math.floor(buff.value);
                this.removeBuff(buff.type);
            }
            else {
                buff.value -= val;
                val = 0;
                break;
            }
        }
        hitShield = damage - val;
        //
        if (val > this.entity.curHp) {
            damage = damage - val + this.entity.curHp;
            this.entity.curHp = 0;
        }
        else {
            this.entity.curHp -= val;
        }
        // 鸩毒记录伤害
        if (val > 0) {
            var buff = this.getBuff(Enums_1.BuffType.POISONED_WINE);
            if (buff) {
                buff.value += val;
            }
        }
        if (this.isDie()) {
            // 周泰 有一定概率恢复10%生命
            var buff = this.getBuff(Enums_1.BuffType.DYING_RECOVER);
            if (buff && this.ctrl.getRandom().chance(buff.value)) {
                buff.value = Math.max(buff.value - 10, 50);
                heal += this.onHeal(Math.round(this.getMaxHp() * 0.2)); //回复血量
                heal = this.doHitAfterByHPLess(heal);
            }
            else {
                this.ctrl.doDieAfter(this);
                // 记录自己的击杀数量
                if (attackerOwners.has(GameHelper_1.gameHpr.getUid()) && !this.isFlag()) {
                    GameHelper_1.gameHpr.player.recordKillCount(this.getId(), 1);
                }
                // 刷新数量显示
                eventCenter.emit(EventType_1.default.UPDATE_BATTLE_ARMY_COUNT_SHOW);
            }
        }
        else {
            heal = this.doHitAfterByHPLess(heal);
        }
        cc.log(this.ctrl.getCurrentFrameIndex() + ' OnHit ' + this.entity.id + '(' + this.getPoint().Join() + ') ' + damage + '_' + val + ' ' + this.entity.curHp + '/' + this.getMaxHp() + ' [' + this.entity.uid + ']');
        return { damage: damage, heal: heal, hitShield: hitShield };
    };
    // 生命低于多少处理
    Fighter.prototype.doHitAfterByHPLess = function (heal) {
        var _this = this;
        if (!this.isPawn()) {
            return heal;
        }
        var hpRatio = this.getHpRatio();
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.LOW_HP_SHIELD) { //血量低于多少给护盾和回复血量
                var buff = _this.getBuff(Enums_1.BuffType.CHECK_LOW_HP);
                if (buff && hpRatio <= buff.value) {
                    _this.removeMultiBuff(Enums_1.BuffType.CHECK_LOW_HP); //先删除
                    _this.addBuffValue(Enums_1.BuffType.LOW_HP_SHIELD, _this.getUid(), _this.getLv() * m.value); //添加buff
                    heal += _this.onHeal(Math.round(_this.getMaxHp() * 0.3)); //回复血量
                }
            }
            else if (m.type === Enums_1.EquipEffectType.LOW_HP_ATTACk) { //血量低于多少给攻击力和吸血
                var buff = _this.getBuff(Enums_1.BuffType.CHECK_LOW_HP_ATTACK);
                if (buff && hpRatio <= buff.value) {
                    _this.removeMultiBuff(Enums_1.BuffType.CHECK_LOW_HP_ATTACK); //先删除
                    // 添加buff 提高攻击力
                    _this.addBuff(Enums_1.BuffType.LOW_HP_ADD_ATTACK, _this.getUid(), _this.getLv());
                    // 添加buff 增加吸血
                    _this.addBuff(Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD, _this.getUid(), 1).value = m.value;
                    //
                    _this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, _this.getPoint());
                }
            }
        });
        var heroSkill = this.getPortrayalSkill();
        if (heroSkill) {
            // 许褚 添加buff
            if (heroSkill.id === Enums_1.HeroType.XU_CHU && hpRatio < 0.5 && !this.isHasBuff(Enums_1.BuffType.NAKED_CLOTHES)) {
                this.addBuff(Enums_1.BuffType.NAKED_CLOTHES, this.getUid(), 1);
                this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, this.getPoint());
            }
        }
        // 韬略 血量低于多少回血
        var sb = this.getStrategyBuff(31501) || this.getStrategyBuff(30702);
        if (sb && hpRatio < (sb.params * 0.01) && !this.isHasBuff(Enums_1.BuffType.S_RECORD_LOW_RECOVER_HP)) {
            this.addBuff(Enums_1.BuffType.S_RECORD_LOW_RECOVER_HP, this.getUid(), 1);
            heal += this.onHeal(Math.round(this.getMaxHp() * sb.value * 0.01)); //回复血量
        }
        // 韬略 血量低于多少加攻击力
        sb = this.getStrategyBuff(40404);
        if (sb && hpRatio < (sb.params * 0.01) && !this.isHasBuff(Enums_1.BuffType.LOW_HP_ADD_ATTACK_S)) {
            this.addBuff(Enums_1.BuffType.LOW_HP_ADD_ATTACK_S, this.getUid(), 1).value = sb.value;
            this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, this.getPoint());
        }
        return heal;
    };
    // 回血
    Fighter.prototype.onHeal = function (val, suckbloodShield) {
        if (val === 0) {
            return val;
        }
        var maxHp = this.getMaxHp();
        // 是否有增益韬略
        var gain = this.getStrategyValue(50012) + this.getStrategyValue(40106) + this.getStrategyValue(40205) + this.getStrategyValue(40405);
        val += Math.round(val * gain * 0.01);
        // 是否有减益buff
        var buff = this.checkTriggerBuff(Enums_1.BuffType.SERIOUS_INJURY);
        if (buff) {
            val = Math.round(val * (1 - buff.value));
        }
        var hp = this.entity.curHp + val;
        if (hp <= maxHp) {
        }
        else if (suckbloodShield) { //是否有吸血护盾
            var shield = hp - maxHp;
            var buff_1 = this.getBuffOrAdd(Enums_1.BuffType.SUCKBLOOD_SHIELD, this.getUid());
            var maxShield = this.getLv() * 6;
            if (buff_1.value + shield > maxShield) {
                shield = maxShield - buff_1.value;
            }
            if (shield > 0) {
                shield += Math.round(shield * this.getStrategyValue(30601) * 0.01);
            }
            buff_1.value += shield;
            val = maxHp - this.entity.curHp + shield;
        }
        else {
            val = maxHp - this.entity.curHp;
        }
        this.entity.curHp = Math.min(this.entity.curHp + val, maxHp);
        // 播放恢复效果
        if (val > 0) {
            eventCenter.emit(EventType_1.default.PLAY_BATTLE_EFFECT, { type: 10000001, index: this.ctrl.getAreaIndex(), point: this.getPoint(), root: 'role' });
            //新手村统计治疗量
            if (GameHelper_1.gameHpr.isNoviceMode) {
                GameHelper_1.gameHpr.noviceServer.recordFighterData({ index: this.ctrl.getAreaIndex(), heal: val, attacker: this, data: { actDamage: 0 } });
            }
        }
        cc.log(this.ctrl.getCurrentFrameIndex() + ' OnHeal ' + this.entity.id + '(' + this.getPoint().Join() + ') ' + val + ' ' + this.entity.curHp + '/' + maxHp + ' [' + this.entity.uid + ']');
        return val;
    };
    Fighter.prototype.isHasAnger = function () { return this.entity.isHasAnger(); };
    Fighter.prototype.getAngerRatio = function () { return this.entity.getAngerRatio(); };
    Fighter.prototype.getCurAnger = function () { return this.entity.getCurAnger(); };
    // 是否可以超出怒气上限
    Fighter.prototype.isCanLimitMaxAnger = function () { return !!this.getStrategyBuff(50014); };
    // 是否无法添加怒气
    Fighter.prototype.isNotAddAngerByBuff = function () {
        if (this.isDie() || !this.entity.isHasAnger()) {
            return true;
        }
        // 是否有盾和立盾的时候不能涨怒气 还有沉默
        var uid = this.getUid();
        return this.entity.buffs.some(function (m) {
            return ((m.type === Enums_1.BuffType.SHIELD || m.type === Enums_1.BuffType.PROTECTION_SHIELD) && m.provider === uid) ||
                m.type === Enums_1.BuffType.RODELERO_SHIELD ||
                m.type === Enums_1.BuffType.RODELERO_SHIELD_001 ||
                m.type === Enums_1.BuffType.RODELERO_SHIELD_102 ||
                m.type === Enums_1.BuffType.STAND_SHIELD ||
                m.type === Enums_1.BuffType.SILENCE;
        });
    };
    // 增加怒气
    Fighter.prototype.addAnger = function (v) {
        if (v <= 0 || this.isNotAddAngerByBuff()) {
            return 0;
        }
        return this.addActAnger(v, this.isCanLimitMaxAnger());
    };
    Fighter.prototype.addActAnger = function (val, canLimit) {
        var anger = this.entity.curAnger + val, maxAnger = this.entity.getMaxAnger();
        if (canLimit) {
            this.entity.curAnger = anger;
        }
        else if (this.entity.curAnger < maxAnger) {
            val = anger < maxAnger ? val : maxAnger - this.entity.curAnger;
            this.entity.curAnger = Math.min(maxAnger, anger);
        }
        else {
            val = 0;
        }
        return val;
    };
    // 扣除怒气
    Fighter.prototype.deductAnger = function (v) {
        this.entity.curAnger = Math.max(0, this.entity.curAnger - v);
    };
    // 强制设置怒气
    Fighter.prototype.setAnger = function (val) {
        this.entity.curAnger = val;
    };
    // 设置满怒气
    Fighter.prototype.setFullAnger = function (ratio, check) {
        var val = Math.round(this.entity.getMaxAnger() * ratio);
        if (check) {
            return this.addAnger(val);
        }
        return this.addActAnger(val, this.isCanLimitMaxAnger());
    };
    // 是否可以释放技能
    Fighter.prototype.isCanUseSkill = function () {
        var maxAnger = this.entity.getMaxAnger();
        if (this.entity.curAnger === 0 && maxAnger === 0) {
            return false;
        }
        return this.entity.curAnger >= maxAnger;
    };
    Fighter.prototype.getBuffs = function () { return this.entity.buffs; };
    // 是否免疫负面效果
    Fighter.prototype.isImmuneNegativeBuff = function () {
        if (this.isHasBuffs(Enums_1.BuffType.PROTECTION_NIE, Enums_1.BuffType.RESOLUTE)) {
            return true;
        }
        else if (this.ctrl.getRandom().chance(this.getStrategyValue(40203))) {
            return true; //韬略
        }
        return false;
    };
    // 是否有负面效果
    Fighter.prototype.isHasNegativeBuff = function () {
        return this.entity.buffs.some(function (m) { return !m.effectType; });
    };
    Fighter.prototype._addBuff = function (type, provider, lv) {
        var _this = this;
        if (!this.isPawn()) {
            return BuffObj_1.default.EMPTY_BUFF; //只有士兵才有buff
        }
        var json = assetsMgr.getJsonData('buffBase', type);
        if (!json) {
            return BuffObj_1.default.EMPTY_BUFF;
        }
        else if (json.effect_type) {
        }
        else if (this.isImmuneNegativeBuff()) {
            if (type !== Enums_1.BuffType.DAMAGE_REDUCE) { //军旗不显示免疫了
                eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: this.getAreaIndex(), uid: this.getUid(), value: 0, isImmune: true });
            }
            return BuffObj_1.default.EMPTY_BUFF; //如果有免疫负面效果 那么就不添加
        }
        else {
            // 给5格内的徐盛加怒气
            this.ctrl.getHeroFighters(Enums_1.HeroType.XU_SHENG, this.camp, '').forEach(function (m) {
                if (MapHelper_1.mapHelper.getPointToPointDis(m.getPoint(), _this.getPoint()) <= 5) {
                    var val = m.addAnger(1);
                    m.changeState(Enums_1.PawnState.ADD_ANGER, { val: val });
                }
            });
        }
        var roundAdd = provider === this.getUid() ? 1 : 0;
        var buff = this.entity.buffs.find(function (m) { return m.type === type; });
        if (!buff) {
            buff = this.entity.buffs.add(new BuffObj_1.default()).init(type, provider, lv || 1, roundAdd);
            eventCenter.emit(EventType_1.default.UPDATE_BUFF);
        }
        else {
            buff.init(type, provider, lv || 1, roundAdd);
        }
        return buff;
    };
    Fighter.prototype.removeBuffByIndex = function (i, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        this.entity.buffs.splice(i, 1);
        if (isEmit) {
            eventCenter.emit(EventType_1.default.UPDATE_BUFF);
        }
    };
    // 添加buff
    Fighter.prototype.addBuff = function (type, provider, lv) {
        var buff = this._addBuff(type, provider, lv);
        return this.checkUpShieldBuffValue(buff);
    };
    Fighter.prototype.addBuffValue = function (type, provider, value) {
        var buff = this._addBuff(type, provider, 1);
        buff.value = value;
        return this.checkUpShieldBuffValue(buff);
    };
    // 检测提升护盾buff效果
    Fighter.prototype.checkUpShieldBuffValue = function (buff) {
        if (!buff.isHasShield()) {
            return buff;
        }
        // 韬略 提高护盾值
        var add = this.getStrategyValue(30601) * 0.01;
        // 装备 赤金盾
        var CRIMSONGOLD_SHIELD = this.getEquipEffectByType(Enums_1.EquipEffectType.CRIMSONGOLD_SHIELD);
        if (CRIMSONGOLD_SHIELD) {
            add += CRIMSONGOLD_SHIELD.value * 0.01;
        }
        buff.value += Math.round(buff.value * add);
        return buff;
    };
    Fighter.prototype.isHasBuff = function (type) {
        return this.entity.buffs.some(function (m) { return m.type === type; });
    };
    Fighter.prototype.isHasBuffs = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        return this.entity.buffs.some(function (m) { return types.has(m.type); });
    };
    Fighter.prototype.getBuff = function (type) {
        return this.entity.buffs.find(function (m) { return m.type === type; });
    };
    Fighter.prototype.getBuffValue = function (type) {
        var _a;
        return ((_a = this.entity.buffs.find(function (m) { return m.type === type; })) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    // 获取buff 没有就添加一个
    Fighter.prototype.getBuffOrAdd = function (type, provider) {
        return this.getBuff(type) || this.addBuff(type, provider, 1);
    };
    // 获取护盾buffs列表 并从大到小排序
    Fighter.prototype.getShieldBuffs = function () {
        return this.entity.buffs.filter(function (m) { return m.isHasShield(); }).sort(function (a, b) { return b.value - a.value; });
    };
    // 检测是否触发某个buff
    Fighter.prototype.checkTriggerBuff = function (type) {
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (buff.type === type) {
                if (buff.updateTimes(-1)) {
                    this.removeBuffByIndex(i);
                }
                return buff;
            }
        }
        return null;
    };
    // 检测是否触发某个buff
    Fighter.prototype.checkTriggerBuffOr = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (types.has(buff.type)) {
                if (buff.updateTimes(-1)) {
                    this.removeBuffByIndex(i);
                }
                return buff;
            }
        }
        return null;
    };
    // 删除buff
    Fighter.prototype.removeMultiBuff = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (types.has(buff.type)) {
                this.removeBuffByIndex(i);
            }
        }
    };
    Fighter.prototype.removeMultiBuffNoEmit = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (types.has(buff.type)) {
                this.removeBuffByIndex(i, false);
            }
        }
    };
    Fighter.prototype.removeBuff = function (type) {
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (buff.type === type) {
                this.removeBuffByIndex(i);
                return true;
            }
        }
        return false;
    };
    // 删除所有护盾buff和立盾
    Fighter.prototype.removeShieldBuffs = function () {
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (buff.type === Enums_1.BuffType.STAND_SHIELD || buff.isHasShield()) {
                this.removeBuffByIndex(i);
            }
        }
    };
    // 删除所有负面buff
    Fighter.prototype.removeAllDebuff = function () {
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (!buff.effectType) {
                this.removeBuffByIndex(i);
            }
        }
    };
    // 删除指定某人施加的buff
    Fighter.prototype.removeBuffByProvider = function (type, provider) {
        for (var i = this.entity.buffs.length - 1; i >= 0; i--) {
            var buff = this.entity.buffs[i];
            if (buff.type === type && buff.provider === provider) {
                this.removeBuffByIndex(i);
                return true;
            }
        }
        return false;
    };
    // 刷新韬略效果
    Fighter.prototype.updateStrategyEffect = function () {
        if (!this.entity || !this.isPawn()) {
            return;
        }
        var oldMaxHp = this.getBuffValue(Enums_1.BuffType.S_RECORD_HP) || this.getMaxHp();
        // 重新添加韬略
        this.entity.cleanStrategyBuffs();
        var strategyMap = this.ctrl.getCampStrategyMap(this.camp);
        for (var key in strategyMap) {
            var m = strategyMap[key];
            if (this.checkStrategyTarget(m)) {
                this.entity.addStrategyBuff(m);
            }
        }
        // 调整血量
        var newMaxHp = this.getMaxHp();
        if (oldMaxHp !== newMaxHp) {
            this.addBuff(Enums_1.BuffType.S_RECORD_HP, this.getUid(), 1).value = newMaxHp;
            this.entity.curHp = Math.min(this.entity.curHp + Math.max(newMaxHp - oldMaxHp, 0), newMaxHp);
        }
        // 韬略 全体前20回合造成的伤害提高10%
        var sv = this.getStrategyValue(50032);
        if (sv > 0) {
            this.getBuffOrAdd(Enums_1.BuffType.RECORD_ROUND_ADD_DAMAGE, this.getUid());
        }
    };
    Fighter.prototype.checkStrategyTarget = function (s) {
        if (s.targetType === 0) { //全体
            return true;
        }
        else if (s.targetType === 1) { //士兵id
            return this.getId() === s.targetValue;
        }
        else if (s.targetType === 2) { //士兵类型 1.枪兵 2.盾兵 3.弓兵 4.骑兵
            return this.entity.getPawnType() === s.targetValue;
        }
        else if (s.targetType === 3) { //军队
            var armyUid_4 = this.entity.armyUid;
            return s.fighters.some(function (m) { return m.getArmyUid() === armyUid_4; });
        }
        else if (s.targetType === 4) { //英雄
            return this.entity.isHero();
        }
        return false;
    };
    Fighter.prototype.getStrategyBuff = function (tp) {
        return this.entity.getStrategyBuff(tp);
    };
    // 获取韬略数值
    Fighter.prototype.getStrategyValue = function (tp) {
        return this.entity.getStrategyValue(tp);
    };
    // 获取韬略数值
    Fighter.prototype.getStrategyParams = function (tp) {
        var _a;
        return ((_a = this.entity.getStrategyBuff(tp)) === null || _a === void 0 ? void 0 : _a.params) || 0;
    };
    // 是否有某个韬略
    Fighter.prototype.isHasStrategys = function () {
        var _a;
        var tps = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            tps[_i] = arguments[_i];
        }
        return (_a = this.entity).isHasStrategys.apply(_a, __spread(tps));
    };
    // 获取该单位攻击指定目标的战斗力
    Fighter.prototype.getFightPower = function (target) {
        var attack = this.entity.attrJson.attack;
        //修正对建筑攻击力
        var defenderPawnType = target.getPawnType(), isBuild = defenderPawnType === Enums_1.PawnType.BUILD;
        if (isBuild) {
            attack = 1;
            var attackRestrain = this.getSkillByType(Enums_1.PawnSkillType.ATTACK_RESTRAIN), isRestrain = (attackRestrain === null || attackRestrain === void 0 ? void 0 : attackRestrain.target) === defenderPawnType;
            if (isRestrain) {
                attack = Math.round(attack * attackRestrain.value);
            }
        }
        return attack * this.getCurHp();
    };
    // 获取护盾值
    Fighter.prototype.getShieldVal = function () {
        var val = 0;
        var buffs = this.getShieldBuffs();
        for (var i = buffs.length - 1; i >= 0; i--) {
            var buff = buffs[i];
            val += buff.value;
        }
        return val;
    };
    // 修正克制
    Fighter.prototype.amendRestrainValue = function (val, tp) {
        if (this.getId() === 3104) { //目标暂时只有陌刀兵有修正克制
            var strategy = this.getStrategyBuff(50004);
            if (strategy) {
                var v = strategy.value * 0.01;
                if (tp === Enums_1.PawnSkillType.ATTACK_RESTRAIN) {
                    return v + 1;
                }
                else if (tp === Enums_1.PawnSkillType.DEFENSE_RESTRAIN) {
                    return v;
                }
            }
        }
        return val;
    };
    // 获取吸血值
    Fighter.prototype.getSuckBloodValue = function () {
        var val = 0;
        // 装备
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.SUCK_BLOOD) {
                val += m.value;
            }
        });
        // buff
        this.getBuffs().forEach(function (m) {
            if (m.type === Enums_1.BuffType.HIT_SUCK_BLOOD || m.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD || m.type === Enums_1.BuffType.PROTECTION_NIE) {
                val += m.value;
            }
        });
        return val;
    };
    // 刷新最大血量记录
    Fighter.prototype.updateMaxHpRecord = function (maxHp) {
        var buff = this.getBuff(Enums_1.BuffType.S_RECORD_HP);
        if (buff) {
            buff.value = maxHp;
        }
    };
    // 获取宠物主人uid
    Fighter.prototype.getPetMaster = function () {
        var arr = this.getUid().split('_');
        if (arr.length === 2 && arr[0] === 'pet') {
            return arr[1];
        }
        return '';
    };
    return Fighter;
}());
exports.default = Fighter;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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