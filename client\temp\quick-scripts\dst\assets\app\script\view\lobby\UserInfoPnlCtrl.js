
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/UserInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2445a4nK9dN6qTK8Y13QNYP', 'UserInfoPnlCtrl');
// app/script/view/lobby/UserInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var UserInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(UserInfoPnlCtrl, _super);
    function UserInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_n
        _this.infoNode_ = null; // path://root/info_n
        _this.inviteAuthTge_ = null; // path://root/info_n/invite_auth_t_te
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.data = null;
        _this.from = '';
        _this.preJob = 0;
        return _this;
    }
    UserInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    UserInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    UserInfoPnlCtrl.prototype.onEnter = function (data, from) {
        this.data = data;
        this.from = from;
        if (from === 'team') {
            this.preJob = data.job;
        }
        this.updateUI();
    };
    UserInfoPnlCtrl.prototype.onRemove = function () {
        if (this.from === 'team') {
            var job = this.data.job || 0;
            if (this.data.uid !== GameHelper_1.gameHpr.getUid() && GameHelper_1.gameHpr.team.isCaptain() && this.preJob !== job) {
                GameHelper_1.gameHpr.team.syncTeammateJob(this.data.uid, job);
            }
        }
    };
    UserInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/info_n/p/button/add_popularity_be
    UserInfoPnlCtrl.prototype.onClickAddPopularity = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.addPlayerPopularity(this.data, function (id) {
            if (_this.isValid && id) {
                _this.playAddPopularity(id);
            }
        });
    };
    // path://root/buttons_n/blacklist_be
    UserInfoPnlCtrl.prototype.onClickBlacklist = function (event, _) {
        ViewHelper_1.viewHelper.showBlacklist(this.data, event, this.buttonsNode_);
    };
    // path://root/buttons_n/add_friend_be
    UserInfoPnlCtrl.prototype.onClickAddFriend = function (event, _) {
        ViewHelper_1.viewHelper.showApplyFriend(this.data);
    };
    // path://root/buttons_n/invite_team_be
    UserInfoPnlCtrl.prototype.onClickInviteTeam = function (event, data) {
        var _this = this;
        if (!GameHelper_1.gameHpr.team.isCaptain()) {
            return ViewHelper_1.viewHelper.showAlert('toast.only_captain_can_invite');
        }
        GameHelper_1.gameHpr.team.inviteTeammate(this.data.uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // path://root/head_n/copy_uid_be
    UserInfoPnlCtrl.prototype.onClickCopyUid = function (event, data) {
        GameHelper_1.gameHpr.copyToClipboard(this.data.uid, 'toast.yet_copy_clipboard');
    };
    // path://root/info_n/invite_auth_t_te
    UserInfoPnlCtrl.prototype.onClickInviteAuth = function (event, data) {
        this.data.job = event.isChecked ? 2 : 0;
        this.emit(EventType_1.default.UPDATE_TEAM_LIST);
    };
    // path://root/buttons_n/kick_be
    UserInfoPnlCtrl.prototype.onClickKick = function (event, data) {
        var _this = this;
        var team = GameHelper_1.gameHpr.team;
        if (team.isInGame()) {
            return;
        }
        else if (team.isInApply()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_cancel_apply_opt');
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.remove_teammate_tip', {
            params: [ut.nameFormator(this.data.nickname, 7)],
            ok: function () { return team.removeTeammate(_this.data.uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                }
            }); },
            cancel: function () { }
        });
    };
    // path://root/buttons_n/exit_be
    UserInfoPnlCtrl.prototype.onClickExit = function (event, data) {
        var _this = this;
        var team = GameHelper_1.gameHpr.team;
        if (team.isInGame()) {
            return;
        }
        else if (team.isInApply()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_cancel_apply_opt');
        }
        team.exitTeam().then(function (err) {
            if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                _this.hide();
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    UserInfoPnlCtrl.prototype.updateUI = function () {
        var data = this.data, from = this.from, isCaptain = GameHelper_1.gameHpr.team.isCaptain(), isInGame = GameHelper_1.gameHpr.team.isInGame(), isInApply = GameHelper_1.gameHpr.team.isInGame();
        var isOwner = data.uid === GameHelper_1.gameHpr.getUid();
        ResHelper_1.resHelper.loadPlayerHead(this.headNode_.Child('val'), data.headIcon, this.key);
        this.headNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 11);
        // 个人简介
        ViewHelper_1.viewHelper.updatePlayerPersonalDesc(this.infoNode_.Child('5/val'), data.uid, data);
        // 称号
        ViewHelper_1.viewHelper.updatePlayerTitleText(this.headNode_.Child('title'), data.uid, this.key);
        // 段位
        ViewHelper_1.viewHelper.updatePlayerRankInfo(this.infoNode_.Child('rank'), data.uid, this.key, data);
        // 总局数
        ViewHelper_1.viewHelper.updateTotalGameCount(this.infoNode_.Child('6'), data.uid, data);
        // 人气
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), data.uid, this.key, data);
        // 状态 暂不显示
        this.infoNode_.Child('9').active = false;
        // const stateNode = this.infoNode_.Child('1')
        // if (stateNode.active = this.channel === 0) {
        //     stateNode.Child('val').setLocaleKey(data.playSid ? 'ui.in_game' : 'ui.in_ready_war')
        // }
        // 期望位置 开局模式
        this.updateReadyInfo(isOwner, this.data.uid);
        // 邀请权限
        if (this.inviteAuthTge_.setActive(isCaptain && !isOwner && data.job !== undefined)) {
            this.inviteAuthTge_.isChecked = this.data.isInviteAuth();
        }
        // 按钮
        var a = this.buttonsNode_.Child('kick_be').active = from === 'team' && !isOwner && isCaptain && !isInGame;
        var b = this.buttonsNode_.Child('exit_be').active = from === 'team' && isOwner && !isCaptain && !isInGame;
        var c = this.buttonsNode_.Child('blacklist_be').active = !isOwner;
        if (c) {
            this.buttonsNode_.Child('invite_team_be').active = from !== 'team' && !isInGame && !isInApply;
            var isInBlacklist = GameHelper_1.gameHpr.friend.isInBlacklist(data.uid);
            this.buttonsNode_.Child('blacklist_be/val', cc.MultiFrame).setFrame(isInBlacklist);
            this.buttonsNode_.Child('add_friend_be').active = !isInBlacklist && !GameHelper_1.gameHpr.friend.isFriend(data.uid);
        }
        else {
            this.buttonsNode_.Child('add_friend_be').active = false;
        }
        this.buttonsNode_.active = a || b || c;
    };
    UserInfoPnlCtrl.prototype.playAddPopularity = function (id) {
        // animHelper.playAddPopularity(id)
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), this.data.uid, this.key, this.data);
    };
    // 更新期望位置及开局模式信息
    UserInfoPnlCtrl.prototype.updateReadyInfo = function (isOwner, uid) {
        var data = GameHelper_1.gameHpr.team.getActTeammates().find(function (m) { return m.uid === uid; });
        if (!isOwner && !data) { // 不是自己也不是队友 隐藏信息
            this.infoNode_.Child('7').active = false;
            this.infoNode_.Child('8').active = false;
        }
        else {
            this.updateExpectPosition(isOwner, data === null || data === void 0 ? void 0 : data.expectPosition);
            this.updateFarmType(isOwner, data === null || data === void 0 ? void 0 : data.farmType);
        }
    };
    // 刷新期望位置
    UserInfoPnlCtrl.prototype.updateExpectPosition = function (isOwner, pos) {
        if (isOwner) {
            pos = GameHelper_1.gameHpr.user.getExpectPosition() - 1;
        }
        else if (!pos && pos !== 0) {
            pos = -1;
        }
        else {
            pos = pos - 1;
        }
        this.infoNode_.Child('7/val').setLocaleKey(pos === -1 ? '-' : 'ui.born_pos_' + pos);
    };
    // 刷新开局模式
    UserInfoPnlCtrl.prototype.updateFarmType = function (isOwner, type) {
        if (isOwner) {
            type = (GameHelper_1.gameHpr.user.getFarmType() || (GameHelper_1.gameHpr.user.isNewbie() ? 2 : 1)) - 1;
        }
        else if (!type && type !== 0) {
            type = -1;
        }
        else {
            type = type - 1;
        }
        this.infoNode_.Child('8/val').setLocaleKey(type === -1 ? '-' : 'ui.farm_type_' + type);
    };
    UserInfoPnlCtrl = __decorate([
        ccclass
    ], UserInfoPnlCtrl);
    return UserInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = UserInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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