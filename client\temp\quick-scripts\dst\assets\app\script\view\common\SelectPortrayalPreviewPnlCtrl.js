
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SelectPortrayalPreviewPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '07db4v2eh1LOL2zJUWt6+yb', 'SelectPortrayalPreviewPnlCtrl');
// app/script/view/common/SelectPortrayalPreviewPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectPortrayalPreviewPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectPortrayalPreviewPnlCtrl, _super);
    function SelectPortrayalPreviewPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        //@end
        _this.list = [];
        return _this;
    }
    SelectPortrayalPreviewPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectPortrayalPreviewPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectPortrayalPreviewPnlCtrl.prototype.onEnter = function (id, list) {
        this.list = list;
        this.titleLbl_.setLocaleKey('ui.hero_opt_gift_' + id);
        this.updateList();
    };
    SelectPortrayalPreviewPnlCtrl.prototype.onRemove = function () {
    };
    SelectPortrayalPreviewPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/list/view/content/item_be
    SelectPortrayalPreviewPnlCtrl.prototype.onClickItem = function (event, data) {
        this.updateSelectHeroInfo(event.target.Data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectPortrayalPreviewPnlCtrl.prototype.updateList = function () {
        var _this = this;
        var sv = this.rootNode_.Child('list', cc.ScrollView);
        sv.scrollToTopLeft();
        sv.content.x = 0;
        sv.Child('empty').active = !this.list.length;
        sv.Items(this.list, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, it.Child('val'), _this.key);
            it.Child('name').setLocaleKey(data.getChatName());
        });
        this.updateSelectHeroInfo(null);
    };
    // 刷新选择
    SelectPortrayalPreviewPnlCtrl.prototype.updateSelectHeroInfo = function (data) {
        var _this = this;
        var root = this.rootNode_;
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var _a;
            var select = (data === null || data === void 0 ? void 0 : data.id) === ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id);
            it.Child('select').active = select;
            it.Component(cc.Button).interactable = !select;
        });
        // 显示信息
        root.Child('empty').active = !data;
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        if (sv.setActive(!!data)) {
            sv.stopAutoScroll();
            info.y = 0;
            ViewHelper_1.viewHelper.updatePortrayalAttr(info, data, true);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = cc.misc.clampf(info.height + 4, 160, 320);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    SelectPortrayalPreviewPnlCtrl = __decorate([
        ccclass
    ], SelectPortrayalPreviewPnlCtrl);
    return SelectPortrayalPreviewPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectPortrayalPreviewPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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