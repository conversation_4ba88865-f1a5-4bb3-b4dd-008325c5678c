
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaArmyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ec3dakw2/tDeJ/37Uxmro8h', 'AreaArmyPnlCtrl');
// app/script/view/area/AreaArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ReddotCmpt_1 = require("../cmpt/ReddotCmpt");
var ccclass = cc._decorator.ccclass;
var AreaArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaArmyPnlCtrl, _super);
    function AreaArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabNode_ = null; // path://root/info/tab_n
        _this.showPawnEquipTge_ = null; // path://root/info/show_pawn_equip_te_t
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'AREA_ARMY_TAB';
        _this.tab = 0;
        _this.area = null;
        _this.player = null;
        _this.preShowPawnEquip = false;
        _this.hpBarList = [];
        return _this;
    }
    AreaArmyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_TREASURES] = this.onUpdateArmyTreasures, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_ARMY_NAME] = this.onUpdateArmyName, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_AREA_ARMY_LIST] = this.onUpdateAreaArmyList, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdateAreaArmyList, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdateAreaArmyList, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdateAreaArmyList, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.REMOVE_ARMY] = this.onRemoveArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.ADD_MARCH] = this.onAddMarch, _l.enter = true, _l),
        ];
    };
    AreaArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    AreaArmyPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var area = this.area = GameHelper_1.gameHpr.areaCenter.getLookArea();
        if (!area) {
            return this.hide();
        }
        this.preShowPawnEquip = this.showPawnEquipTge_.isChecked = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED)) !== null && _a !== void 0 ? _a : false;
        var _b = __read((GameHelper_1.gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || '').split(','), 2), tag = _b[0], type_ = _b[1];
        var type = Number(type_);
        if (tag !== area.index + '_' + area.owner) {
            this.listSv_.node.Data = -1;
            type = 0;
        }
        this.tabsTc_.Tabs(type !== null && type !== void 0 ? type : 0);
    };
    AreaArmyPnlCtrl.prototype.onRemove = function () {
        this.hpBarList = [];
        this.area = null;
        if (this.preShowPawnEquip !== this.showPawnEquipTge_.isChecked) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP_AND_SPEED, this.showPawnEquipTge_.isChecked);
        }
    };
    AreaArmyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/bottom/treasure_be
    AreaArmyPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        var treasures = data === null || data === void 0 ? void 0 : data.getAllPawnTreasures();
        if (treasures && treasures.length > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', treasures);
        }
    };
    // path://root/list_sv/view/content/item/top/name/edit/edit_name_be
    AreaArmyPnlCtrl.prototype.onClickEditName = function (event, _) {
        var data = event.target.parent.parent.parent.parent.Data;
        if (!data || !data.isOwner()) {
        }
        else if (this.area.isBattleing()) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/EditArmyName', data);
        }
    };
    // path://root/list_sv/view/content/item/pawns/pawn_be
    AreaArmyPnlCtrl.prototype.onClickPawn = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.drillInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByDrillInfo(data.drillInfo), data.drillInfo, 'area_army');
        }
        else if (data.curingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByCureInfo(data.curingInfo), data.curingInfo, 'area_army');
        }
        else if (data.lvingInfo) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', GameHelper_1.gameHpr.areaCenter.createPawnByLvingInfo(data.pawn, data.lvingInfo), data.lvingInfo, 'area_army');
        }
        else if (data.pawn) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn, null, 'area_army');
        }
    };
    // path://root/info/show_pawn_equip_te_t
    AreaArmyPnlCtrl.prototype.onClickShowPawnEquip = function (event, data) {
        audioMgr.playSFX('click');
        this.updateArmyList();
        ViewHelper_1.viewHelper.showNoLongerTip('area_army_show_equip', { content: 'ui.area_army_show_equip_tip' });
    };
    // path://root/list_sv/view/content/item/top/march_speed_be
    AreaArmyPnlCtrl.prototype.onClickMarchSpeed = function (event, _) {
        var _this = this;
        var it = event.target.parent.parent;
        var data = it === null || it === void 0 ? void 0 : it.Data;
        if (!data) {
            return;
        }
        else if (this.area.isBattleing()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        ViewHelper_1.viewHelper.showPnl('main/ModifyMarchSpeed', data, function (speed) {
            if (speed) {
                data.marchSpeed = speed;
                if (_this.isValid) {
                    _this.updateArmyMarchSpeed(it.Child('top'), data);
                }
            }
        });
    };
    // path://root/list_sv/view/content/item/bottom/force_revoke_be
    AreaArmyPnlCtrl.prototype.onClickForceRevoke = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data.owner === GameHelper_1.gameHpr.getUid() || data.aIndex !== GameHelper_1.gameHpr.player.getMainCityIndex() || data.isBattleing()) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.force_revoke_tip', {
            params: [GameHelper_1.gameHpr.getPlayerName(data.owner), data.name],
            ok: function () {
                GameHelper_1.gameHpr.world.forceRevoke(data.uid).then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        // this.hide()
                    }
                });
            },
            cancel: function () { }
        });
    };
    // path://root/tabs_tc_tce
    AreaArmyPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var tab = this.tab = Number(event.node.name);
        GameHelper_1.gameHpr.user.setTempPreferenceData(this.PKEY_TAB, this.area.index + '_' + this.area.owner + ',' + tab);
        this.updateArmyList();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    AreaArmyPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === pawn.armyUid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队宝箱
    AreaArmyPnlCtrl.prototype.onUpdateArmyTreasures = function (army) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === army.uid; });
        if (it) {
            this.updateArmyTreasure(it.Child('bottom'), it.Data);
        }
    };
    // 刷新军队名字
    AreaArmyPnlCtrl.prototype.onUpdateArmyName = function (uid, name) {
        var it = this.listSv_.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid; });
        if (it) {
            it.Child('top').Child('name/val', cc.Label).string = name;
        }
    };
    AreaArmyPnlCtrl.prototype.onUpdateAreaArmyList = function (index) {
        if (this.area.index === index) {
            this.updateArmyList();
        }
    };
    AreaArmyPnlCtrl.prototype.onRemoveArmy = function (army, index) {
        this.onUpdateAreaArmyList(index);
    };
    AreaArmyPnlCtrl.prototype.onUpdateArmy = function (army) {
        this.onUpdateAreaArmyList(army.index);
    };
    AreaArmyPnlCtrl.prototype.onAddMarch = function (march) {
        if (march.isArmy()) {
            this.onUpdateAreaArmyList(march.armyIndex);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新军队数量
    AreaArmyPnlCtrl.prototype.updateArmyCount = function () {
        var owner = this.area.owner, maxArmyCount = this.area.maxArmyCount;
        var countMap = { 0: 0, 1: 0 };
        // 计算各个军队数量
        this.area.armys.forEach(function (m) {
            if (m.getPawnActCount() === 0) {
                return;
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                countMap[0] += 1;
            }
            else {
                countMap[1] += 1;
            }
        });
        // // 从这个区域开始行军的军队数量
        // const index = this.area.index
        // gameHpr.world.getMarchs().filter(m => m.armyIndex === index).forEach(m => {
        //     if (m.autoRevoke) {
        //     } else if (gameHpr.checkIsOneAlliance(m.owner, owner)) {
        //         countMap[0] += 1
        //     } else {
        //         countMap[1] += 1
        //     }
        // })
        this.tabNode_.Child('val').setLocaleKey('ui.area_army_' + this.tab);
        this.tabNode_.Child('count', cc.Label).string = "(" + countMap[this.tab] + "/" + maxArmyCount + ")";
    };
    // 刷新宝箱红点
    AreaArmyPnlCtrl.prototype.updateTreasureDot = function () {
        var type = GameHelper_1.gameHpr.checkIsOneAlliance(this.area.owner) ? 0 : 1;
        for (var i = 0; i < 2; i++) {
            var keys = type === i && this.tab !== i ? ['treasure_' + this.area.index] : [];
            this.tabsTc_.Child(i + '/dot', ReddotCmpt_1.default).setKeys(keys);
        }
    };
    // 刷新军队列表
    AreaArmyPnlCtrl.prototype.updateArmyList = function () {
        var _this = this;
        this.updateArmyCount();
        this.updateTreasureDot();
        var marchs = {};
        GameHelper_1.gameHpr.world.getMarchs().forEach(function (m) { return marchs[m.armyUid] = m; });
        var owner = this.area.owner, uid = GameHelper_1.gameHpr.getUid();
        var arr = this.area.armys.filter(function (m) {
            if (m.pawns.length === 0 && m.owner !== uid) {
                return false;
            }
            return !_this.tab === GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner);
        });
        var pawnDrillMap = {}, lvingPawnLvMap = {}, curingPawnLvMap = {};
        this.player.getAllPawnDrillList().forEach(function (m) {
            var p = pawnDrillMap[m.auid];
            if (!p) {
                p = pawnDrillMap[m.auid] = [];
            }
            p.push(m);
        });
        this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m; });
        this.player.getCuringPawnsQueue().forEach(function (m) { return curingPawnLvMap[m.uid] = m; });
        this.listSv_.Child('empty').setActive(arr.length === 0);
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode, showEquip = this.showPawnEquipTge_.isChecked;
        var mainCityIndex = GameHelper_1.gameHpr.player.getMainCityIndex(), isAncient = this.area.isAncient();
        this.hpBarList = [];
        if (this.listSv_.node.Data !== this.tab) {
            this.listSv_.node.Data = this.tab;
            this.listSv_.stopAutoScroll();
            this.listSv_.content.y = 0;
        }
        this.listSv_.Items(arr, function (it, data) {
            var _a;
            it.Data = data;
            var top = it.Child('top'), bottom = it.Child('bottom');
            var pawns = data.pawns, isHasLving = false, isOwner = data.isOwner(), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner, uid);
            var isMarching = data.isMarching();
            it.opacity = isMarching ? 180 : 255;
            it.Color(isMarching ? '#E4DDD5' : isOwner ? '#E9DDC7' : isOneAlliance ? '#DAEBDD' : '#F4D4D4');
            var armyName = data.owner ? data.name : isAncient ? assetsMgr.lang('ui.ancient_army_name') : assetsMgr.lang(pawns[0] ? 'ui.pawn_type_' + (pawns[0].type || 6) : 'ui.neutral_pawn');
            top.Child('name/val', cc.Label).Color(isOwner ? '#564C49' : isOneAlliance ? '#4A85D5' : '#C34B3F').string = armyName;
            top.Child('name/edit').active = isOwner && !isNoviceMode && !isMarching;
            var other = top.Child('name/other'), alli = top.Child('alli');
            if (other.active = !isOwner && !!data.owner) {
                var plr = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
                if (plr) {
                    ResHelper_1.resHelper.loadPlayerHead(other.Child('head'), plr.headIcon, _this.key);
                    other.Child('name', cc.Label).string = ut.nameFormator(plr.nickname, 7);
                    // 联盟
                    if (alli.active = !!plr.allianceUid && !isOneAlliance) {
                        ResHelper_1.resHelper.loadAlliIcon(plr.allianceIcon, alli.Child('icon'), _this.key);
                        alli.Child('name', cc.Label).string = plr.allianceName;
                    }
                }
                else {
                    other.active = alli.active = false;
                }
            }
            else {
                alli.active = false;
            }
            var drills = ((_a = pawnDrillMap[data.uid]) === null || _a === void 0 ? void 0 : _a.slice()) || [];
            var list = isOwner ? pawns.concat(data.drillPawns).concat(data.curingPawns) : pawns;
            it.Child('pawns').Items(list, function (node, pawn) {
                var _a, _b;
                var icon = node.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                if (isId) {
                    node.Data = { id: pawn, drillInfo: drills.remove('id', pawn) };
                }
                else if (isCuring) {
                    node.Data = { id: pawn, curingInfo: curingPawnLvMap[pawn.uid] };
                }
                else {
                    node.Data = { pawn: pawn, id: pawn.id, lvingInfo: lvingPawnLvMap[pawn.uid] };
                }
                var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                var lv = isLving ? (_a = lvingPawnLvMap[pawn.uid]) === null || _a === void 0 ? void 0 : _a.lv : (isId ? 1 : pawn.lv);
                icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_b = pawn.portrayal) === null || _b === void 0 ? void 0 : _b.id) || pawn.id), icon, _this.key, false);
                node.Component(cc.Button).interactable = !isMarching;
                node.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                if (node.Child('hp').active = (!isId && !isCuring)) {
                    var spr = node.Child('hp/bar', cc.Sprite);
                    spr.fillRange = pawn.getHpRatio();
                    _this.hpBarList.push({ bar: spr, pawn: pawn });
                }
                var showNode = node.Child('show');
                if (showNode.active = showEquip && !isMarching) {
                    // 出手速度
                    showNode.Child('speed', cc.Label).string = (isId || isCuring) ? '' : '' + pawn.attackSpeed;
                    // 装备
                    if (showNode.Child('equip').active = !isId && !isCuring && !!(pawn === null || pawn === void 0 ? void 0 : pawn.isCanWearEquip())) {
                        var spr = showNode.Child('equip/val', cc.Sprite), equip = pawn.equip;
                        if (equip === null || equip === void 0 ? void 0 : equip.id) {
                            ResHelper_1.resHelper.loadEquipIcon(equip.id, spr, _this.key, equip.getSmeltCount());
                        }
                        else {
                            spr.spriteFrame = null;
                        }
                    }
                }
                if (isLving) {
                    isHasLving = true;
                }
            });
            bottom.Child('force_revoke_be').active = data.aIndex === mainCityIndex && !isOwner && !data.isBattleing();
            ViewHelper_1.viewHelper.updateArmyState(bottom, data, marchs[data.uid], isHasLving, isOneAlliance);
            _this.updateArmyMarchSpeed(top, data);
            _this.updateArmyTreasure(bottom, data);
        });
    };
    // 行军速度
    AreaArmyPnlCtrl.prototype.updateArmyMarchSpeed = function (it, data) {
        var node = it.Child('march_speed_be'), isOneAlliance = GameHelper_1.gameHpr.checkIsOneAlliance(data.owner);
        if (node.active = isOneAlliance && !GameHelper_1.gameHpr.isNoviceMode && !data.isMarching() && data.pawns.length > 0 && data.defaultMarchSpeed > 0) {
            var marchSpeedLbl = node.Component(cc.Label), line = node.Child('line'), isOwner = data.isOwner();
            node.Color(/* isOwner &&  */ data.marchSpeed === data.defaultMarchSpeed ? '#936E5A' : '#B6A591').setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            node.Component(cc.Button).interactable = isOwner;
            if (line.active = isOwner) {
                marchSpeedLbl._forceUpdateRenderData();
                line.width = marchSpeedLbl.node.width;
            }
        }
    };
    // 刷新宝箱信息
    AreaArmyPnlCtrl.prototype.updateArmyTreasure = function (it, data) {
        var node = it.Child('treasure_be'), treasureCount = data.getAllPawnTreasureCount();
        if (node.active = treasureCount > 0 && data.isOwner() && !data.isMarching()) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    AreaArmyPnlCtrl.prototype.update = function (dt) {
        this.hpBarList.forEach(function (m) {
            if (m.pawn) {
                m.bar.fillRange = m.pawn.getHpRatio();
            }
        });
    };
    AreaArmyPnlCtrl = __decorate([
        ccclass
    ], AreaArmyPnlCtrl);
    return AreaArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaArmyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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