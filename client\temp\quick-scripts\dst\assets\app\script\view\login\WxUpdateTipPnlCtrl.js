
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/WxUpdateTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '71706epsmVDRKqkWtobtep0', 'WxUpdateTipPnlCtrl');
// app/script/view/login/WxUpdateTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var WxHelper_1 = require("../../common/helper/WxHelper");
var ccclass = cc._decorator.ccclass;
var WxUpdateTipPnlCtrl = /** @class */ (function (_super) {
    __extends(WxUpdateTipPnlCtrl, _super);
    function WxUpdateTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentLbl_ = null; // path://root/content_l
        _this.buttonsNode_ = null; // path://root/buttons_n
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    WxUpdateTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    WxUpdateTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false });
                return [2 /*return*/];
            });
        });
    };
    WxUpdateTipPnlCtrl.prototype.onEnter = function (data) {
        var _this = this;
        var node = this.buttonsNode_.Swih('downloading')[0];
        node.Child('val', cc.LabelWaitDot).play(assetsMgr.lang('login.downloading'));
        this.contentLbl_.setLocaleKey('login.version_toolow_wx_tip');
        // 监听更新完成
        var updateManager = wx.getUpdateManager();
        updateManager.onUpdateReady(function () {
            if (_this.isValid) {
                _this.contentLbl_.setLocaleKey('login.version_update_done_tip');
                node.Child('val', cc.LabelWaitDot).stop();
                _this.buttonsNode_.Swih('restart_be');
            }
        });
        // 下载失败
        updateManager.onUpdateFailed(function () {
            WxHelper_1.wxHelper.errorAndFilter('onUpdateFailed', '新版本下载失败');
        });
    };
    WxUpdateTipPnlCtrl.prototype.onRemove = function () {
    };
    WxUpdateTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/restart_be
    WxUpdateTipPnlCtrl.prototype.onClickRestart = function (event, data) {
        if (ut.isWechatGame()) {
            wx.getUpdateManager().applyUpdate();
        }
    };
    WxUpdateTipPnlCtrl = __decorate([
        ccclass
    ], WxUpdateTipPnlCtrl);
    return WxUpdateTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = WxUpdateTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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