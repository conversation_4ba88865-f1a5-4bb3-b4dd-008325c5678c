
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/CTypeObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4ca7eMlvc9DRbwKxJet64Rq', 'CTypeObj');
// app/script/model/common/CTypeObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
// 通用类型对象
var CTypeObj = /** @class */ (function () {
    function CTypeObj() {
        this.type = Enums_1.CType.NONE;
        this.id = 0;
        this.count = 0;
    }
    CTypeObj.prototype.init = function (type, id, count) {
        this.type = type;
        this.id = id;
        this.count = count;
        return this;
    };
    CTypeObj.prototype.clone = function () {
        return new CTypeObj().init(this.type, this.id, this.count);
    };
    CTypeObj.prototype.fromString = function (val) {
        var _a = __read(val.split(',').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }), 3), type = _a[0], id = _a[1], count = _a[2];
        this.type = Number(type) || Enums_1.CType.NONE;
        this.id = Number(id) || 0;
        this.count = Number(count) || 0;
        return this;
    };
    CTypeObj.prototype.fromSvr = function (data) {
        this.type = data.type || Enums_1.CType.NONE;
        this.id = data.id || 0;
        this.count = data.count || 0;
        return this;
    };
    CTypeObj.prototype.getIds = function () {
        if (this.type !== Enums_1.CType.BUILD_LV || this.id < 10000) {
            return [this.id];
        }
        var a = Math.floor(this.id / 10000);
        var b = Math.floor(this.id % 10000);
        return [a, b];
    };
    CTypeObj.prototype.toJson = function () {
        return {
            type: this.type,
            id: this.id,
            count: this.count,
        };
    };
    return CTypeObj;
}());
exports.default = CTypeObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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