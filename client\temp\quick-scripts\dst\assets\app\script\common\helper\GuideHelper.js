
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/GuideHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e4d4azUkrJK1YotZqOIUvSh', 'GuideHelper');
// app/script/common/helper/GuideHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.guideHelper = void 0;
var CameraCtrl_1 = require("../camera/CameraCtrl");
/**
 * 引导相关
 */
var GuideHelper = /** @class */ (function () {
    function GuideHelper() {
        this._temp_vec2_1 = cc.v2();
        this._temp_vec2_2 = cc.v2();
        this.curWeakGuideFinger = null;
    }
    // 找节点
    GuideHelper.prototype.findNode = function (path, root, loadingNode) {
        return __awaiter(this, void 0, void 0, function () {
            var node, cnt, it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        loadingNode === null || loadingNode === void 0 ? void 0 : loadingNode.setActive(true);
                        node = cc.Canvas.instance, cnt = 0, it = null;
                        _a.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 3];
                        it = node.FindChild(path);
                        if (it && it.active) {
                            return [3 /*break*/, 3];
                        }
                        return [4 /*yield*/, ut.wait(0.2)];
                    case 2:
                        _a.sent();
                        if (cnt++ > 30) {
                            return [3 /*break*/, 3];
                        }
                        return [3 /*break*/, 1];
                    case 3:
                        loadingNode === null || loadingNode === void 0 ? void 0 : loadingNode.setActive(false);
                        if (it) {
                            return [2 /*return*/, this.getTargetInfo(it, root)];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    // 获取目标节点信息
    GuideHelper.prototype.getTargetInfo = function (node, root) {
        var _a = node.getBoundingBox(), xMin = _a.xMin, yMin = _a.yMin, xMax = _a.xMax, yMax = _a.yMax;
        var lbPos = this.convertToNodePosAR(node.parent, root, cc.v2(xMin, yMin));
        var rtPos = this.convertToNodePosAR(node.parent, root, cc.v2(xMax, yMax));
        var width = rtPos.x - lbPos.x;
        var height = rtPos.y - lbPos.y;
        return cc.rect(lbPos.x, lbPos.y, width, height);
    };
    GuideHelper.prototype.convertToNodePosAR = function (node, targetNode, nodePoint) {
        var tempVec3 = cc.v3();
        var mat4 = cc.mat4();
        // 先将节点转到世界坐标
        var worldMatrix = node.getWorldMatrix(mat4);
        if (nodePoint) {
            cc.Vec2.transformMat4(tempVec3, cc.v3(nodePoint), worldMatrix);
        }
        else {
            worldMatrix.getTranslation(tempVec3);
        }
        var camera = cc.Camera.findCamera(node);
        var targetCamera = cc.Camera.findCamera(targetNode);
        if (camera != targetCamera) { //不同摄像机进行视角变换
            camera.getWorldToScreenPoint(tempVec3, tempVec3); //camera视角下的世界坐标到屏幕坐标
            targetCamera.getScreenToWorldPoint(tempVec3, tempVec3); //屏幕坐标到targetCamera下的世界坐标
        }
        // 再将节点转到目标节点局部坐标
        return targetNode.convertToNodeSpaceAR(tempVec3.toVec2());
    };
    // 播放若引导手指
    GuideHelper.prototype.playWeakGuideFinger = function (data, root, key) {
        var _this = this;
        if (data.rect) {
            this.runWeakGuideFinger(data.rect, data.finger, root, key);
        }
        else if (data.node) {
            this.runWeakGuideFinger(this.getTargetInfo(data.node, root), data.finger, root, key);
        }
        else if (data.path) {
            this.findNode(data.path, root).then(function (rect) { return (root.isValid && rect) && _this.runWeakGuideFinger(rect, data.finger, root, key); });
        }
    };
    GuideHelper.prototype.runWeakGuideFinger = function (rect, data, root, key) {
        return __awaiter(this, void 0, void 0, function () {
            var node, center_1, center, offset;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        node = this.curWeakGuideFinger;
                        if (!!node) return [3 /*break*/, 2];
                        return [4 /*yield*/, nodePoolMgr.get('other/FINGER', key)];
                    case 1:
                        node = _a.sent();
                        if (!node || !root.isValid || !node.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(node)];
                        }
                        _a.label = 2;
                    case 2:
                        if (!data.moveCamera) return [3 /*break*/, 4];
                        center_1 = rect.center;
                        CameraCtrl_1.cameraCtrl.getWorldWinSize().mul(0.5, this._temp_vec2_1);
                        return [4 /*yield*/, CameraCtrl_1.cameraCtrl.moveTo(0.1, center_1.sub(this._temp_vec2_1, this._temp_vec2_2))];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        this.curWeakGuideFinger = node;
                        node.parent = root;
                        node.active = true;
                        center = rect.center, offset = data.offset || cc.v2();
                        node.setPosition(center.x + offset.x, center.y + offset.y);
                        node.angle = data.angle || 0;
                        return [2 /*return*/];
                }
            });
        });
    };
    GuideHelper.prototype.cleanWeak = function () {
        if (this.curWeakGuideFinger) {
            this.curWeakGuideFinger.parent = null;
            nodePoolMgr.put(this.curWeakGuideFinger);
            this.curWeakGuideFinger = null;
        }
    };
    return GuideHelper;
}());
exports.guideHelper = new GuideHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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