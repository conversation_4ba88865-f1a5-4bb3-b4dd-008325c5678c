
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildGranaryPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0991byt0DhLRYoIrgnxPVyw', 'BuildGranaryPnlCtrl');
// app/script/view/build/BuildGranaryPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BuildGranaryPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildGranaryPnlCtrl, _super);
    function BuildGranaryPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.data = null;
        return _this;
    }
    BuildGranaryPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a)
        ];
    };
    BuildGranaryPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildGranaryPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        ViewHelper_1.viewHelper._updateBuildBaseInfo(this.rootNode_.Child('info/top'), data, this.key);
        ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, this.rootNode_.Child('info/attrs'), this.rootNode_.Child('bottom'), this.getEffectsForView(), this.key);
    };
    BuildGranaryPnlCtrl.prototype.onRemove = function () {
    };
    BuildGranaryPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/bottom/buttons/up_be
    BuildGranaryPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildGranaryPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            this.rootNode_.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, this.rootNode_.Child('info/attrs'), this.rootNode_.Child('bottom'), this.getEffectsForView(), this.key);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildGranaryPnlCtrl.prototype.getEffectsForView = function () {
        var _a, _b, _c, _d, _e, _f, _g;
        var data = this.data, type = (_a = data.effect) === null || _a === void 0 ? void 0 : _a.type, isMaxLv = data.isMaxLv();
        var cap = type === Enums_1.CEffect.GRANARY_CAP ? GameHelper_1.gameHpr.player.getGranaryCap() : GameHelper_1.gameHpr.player.getWarehouseCap();
        var addVal = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.GW_CAP);
        var infos = [];
        if (type === Enums_1.CEffect.GRANARY_CAP) {
            var info = {
                curr: {
                    key: 'ui.build_eff_desc_' + type,
                    params: cap
                },
                nextVal: isMaxLv ? null : cap + (((_c = (_b = data.nextLvInfo) === null || _b === void 0 ? void 0 : _b.effect) === null || _c === void 0 ? void 0 : _c.value) || 0) - (((_d = data.effect) === null || _d === void 0 ? void 0 : _d.value) || 0) + addVal
            };
            infos.push(info);
        }
        else {
            for (var i = 0; i < 2; i++) {
                var info = {
                    curr: {
                        key: 'ui.build_eff_desc_' + type + '_' + (i + 1),
                        params: cap
                    },
                    nextVal: isMaxLv ? null : cap + (((_f = (_e = data.nextLvInfo) === null || _e === void 0 ? void 0 : _e.effect) === null || _f === void 0 ? void 0 : _f.value) || 0) - (((_g = data.effect) === null || _g === void 0 ? void 0 : _g.value) || 0) + addVal
                };
                infos.push(info);
            }
        }
        return infos;
    };
    BuildGranaryPnlCtrl = __decorate([
        ccclass
    ], BuildGranaryPnlCtrl);
    return BuildGranaryPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildGranaryPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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