
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/AlliApplyListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e13c8V56n1BLJ+0fodhdmk5', 'AlliApplyListPnlCtrl');
// app/script/view/build/AlliApplyListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AlliApplyListPnlCtrl = /** @class */ (function (_super) {
    __extends(AlliApplyListPnlCtrl, _super);
    function AlliApplyListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.countLbl_ = null; // path://root/title/lay/count_l
        _this.listSv_ = null; // path://root/content/list_sv
        _this.descEb_ = null; // path://root/content/desc_eb_ebee
        //@end
        _this.alliance = null;
        _this.preApplyDesc = '';
        return _this;
    }
    AlliApplyListPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ALLIANCE_APPLYS] = this.onUpdateAllianceApplys, _a.enter = true, _a),
        ];
    };
    AlliApplyListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.alliance = this.getModel('alliance');
                return [2 /*return*/];
            });
        });
    };
    AlliApplyListPnlCtrl.prototype.onEnter = function () {
        this.preApplyDesc = this.descEb_.string = this.alliance.getApplyDesc();
        this.updateList();
    };
    AlliApplyListPnlCtrl.prototype.onRemove = function () {
        // 同步介绍
        var applyDesc = this.alliance.getApplyDesc();
        if (this.preApplyDesc !== applyDesc && this.alliance.isCanSendMail()) {
            GameHelper_1.gameHpr.net.send('game/HD_ChangeAlliApplyDesc', { applyDesc: applyDesc });
        }
    };
    AlliApplyListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content/list_sv/view/content/item/bottom/pos/apply_pos_be
    AlliApplyListPnlCtrl.prototype.onClickApplyPos = function (event, data) {
        var index = event.target.Data;
        if (index) {
            this.emit(mc.Event.HIDE_ALL_PNL, 'build/AlliApplyList|build/BuildEmbassy');
            GameHelper_1.gameHpr.gotoTargetPos(index);
        }
    };
    // path://root/content/desc_eb_ebee
    AlliApplyListPnlCtrl.prototype.onClickDescEnded = function (event, data) {
        var desc = event.string.trim();
        if (ut.getStringLen(desc) > 40) {
            ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
            desc = event.string = ut.nameFormator(desc, 20, '');
        }
        else if (GameHelper_1.gameHpr.getTextNewlineCount(desc) >= 1) {
            ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
            var arr = desc.split('\n');
            if (arr.length >= 2) {
                desc = arr[0];
            }
        }
        this.alliance.setApplyDesc(desc);
    };
    // path://root/content/list_sv/view/content/item/bottom/cancel_be
    AlliApplyListPnlCtrl.prototype.onClickCancel = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showMessageBox('ui.operate_alli_apply_tip_2', {
                params: [ut.nameFormator(data.nickname, 7)],
                ok: function () { return _this.isValid && _this.do(data.uid, false); },
                cancel: function () { }
            });
        }
    };
    // path://root/content/list_sv/view/content/item/bottom/ok_be
    AlliApplyListPnlCtrl.prototype.onClickOk = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showMessageBox('ui.operate_alli_apply_tip_1', {
                params: [ut.nameFormator(data.nickname, 7)],
                ok: function () { return _this.isValid && _this.do(data.uid, true); },
                cancel: function () { }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AlliApplyListPnlCtrl.prototype.onUpdateAllianceApplys = function (isUpdate) {
        if (isUpdate) {
            this.updateList();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AlliApplyListPnlCtrl.prototype.updateList = function () {
        var _this = this;
        var now = Date.now();
        var list = this.alliance.getApplys(), len = list.length;
        this.listSv_.Child('empty').active = len === 0;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.List(len, function (it, i) {
            var data = it.Data = list[i];
            if (data.nickname === undefined) {
                var info = GameHelper_1.gameHpr.getPlayerInfo(data.uid);
                if (info) {
                    data.index = info.mainCityIndex;
                    data.dis = GameHelper_1.gameHpr.getSelfToMapCellDis(data.index);
                    data.nickname = info.nickname;
                    data.headIcon = info.headIcon;
                    data.cellCount = info.cells.size;
                }
            }
            var top = it.Child('top');
            ResHelper_1.resHelper.loadPlayerHead(top.Child('head'), data.headIcon, _this.key);
            top.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            if (data.offlineTime) {
                top.Child('online').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.offlineTime + (now - data.getTime)));
            }
            else {
                top.Child('online').Color('#4AB32E').setLocaleKey('ui.online');
            }
            top.Child('time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
            top.Child('dis').setLocaleKey('ui.dis_grid_count', data.dis);
            if (it.Child('desc').active = !!data.desc) {
                it.Child('desc/val', cc.Label).string = data.desc;
            }
            ViewHelper_1.viewHelper.updatePositionView(it.Child('bottom/pos/apply_pos_be'), data.index, false);
            it.Child('bottom/cell_count/val', cc.Label).string = data.cellCount + '';
        });
        this.countLbl_.string = '(' + len + ')';
    };
    AlliApplyListPnlCtrl.prototype.do = function (uid, isAgree) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.alliance.agreeJoin(uid, isAgree)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                        }
                        this.updateList();
                        return [2 /*return*/];
                }
            });
        });
    };
    AlliApplyListPnlCtrl = __decorate([
        ccclass
    ], AlliApplyListPnlCtrl);
    return AlliApplyListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliApplyListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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