
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/ad/NativeRewardAd.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '449a9UjrlJBb4L2K2Lr173H', 'NativeRewardAd');
// app/script/common/ad/NativeRewardAd.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../constant/Enums");
var JsbEvent_1 = require("../event/JsbEvent");
var JsbHelper_1 = require("../helper/JsbHelper");
var BaseRewardAd_1 = require("./BaseRewardAd");
// APP视频广告 只用于海外聚合
var NativeRewardAd = /** @class */ (function (_super) {
    __extends(NativeRewardAd, _super);
    function NativeRewardAd() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.inShow = false;
        _this.waitLoadTime = 0;
        return _this;
    }
    // 连续加载广告失败多久了
    NativeRewardAd.prototype.getFailTime = function () {
        return this.waitLoadTime * ut.Time.Second;
    };
    NativeRewardAd.prototype.isReady = function () {
        var isVaild = true;
        if (ut.isIos()) {
            isVaild = jsb.reflection.callStaticMethod('jsbHelp', 'isRewardVideoADReady');
        }
        else if (ut.isAndroid()) {
            isVaild = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'isRewardVideoADReady', '()Z');
        }
        return isVaild;
    };
    NativeRewardAd.prototype.update = function (dt) {
        if (this.inShow) {
            return;
        }
        else if (!this.isReady()) {
            this.waitLoadTime += dt;
        }
        else {
            this.waitLoadTime = 0;
        }
    };
    NativeRewardAd.prototype.show = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res, error_1, code;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.inShow = true;
                        this.pause();
                        res = null;
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.SHOW_REWARD_VIDEO_AD)];
                    case 2:
                        res = _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_1 = _a.sent();
                        logger.error(error_1);
                        return [3 /*break*/, 4];
                    case 4:
                        this.inShow = false;
                        this.resume();
                        code = res.status;
                        if (code === '0') {
                            this.state = Enums_1.AdState.PLAY_SUCCESS;
                            return [2 /*return*/, true];
                        }
                        else {
                            this.state = Enums_1.AdState.PLAY_FAIL;
                            return [2 /*return*/, false];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    NativeRewardAd.prototype.resetAdFailTime = function () {
        this.waitLoadTime = 0;
    };
    return NativeRewardAd;
}(BaseRewardAd_1.default));
exports.default = NativeRewardAd;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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