
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ViewGroupNestingCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6ff7a4lZFJHurDcWGr1Jcz7', 'ViewGroupNestingCmpt');
// app/script/view/cmpt/ViewGroupNestingCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ViewGroupNesting = /** @class */ (function (_super) {
    __extends(ViewGroupNesting, _super);
    function ViewGroupNesting() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.events = [];
        return _this;
    }
    ViewGroupNesting.prototype.onLoad = function () {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchHandle, this, true);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchHandle, this, true);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchHandle, this, true);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchHandle, this, true);
    };
    ViewGroupNesting.prototype.onTouchHandle = function (event) {
        if (event.sham || event.simulate || event.target === this.node)
            return;
        var cancelEvent = new cc.Event.EventTouch(event.getTouches(), event.bubbles);
        cancelEvent.type = event.type;
        cancelEvent.touch = event.touch;
        cancelEvent.sham = true;
        // https://forum.cocos.org/t/topic/112481
        // 问：这里为啥不直接dispatchEvent
        // 答：必须让ScrollView把真的event先消耗掉，我们再发射假的才可以，
        // 可以去CCNode.js下找一个_doDispatchEvent函数，里面用到了_cachedArray这么个全局变量，
        // 先发射假的话，真的那个数据就被清空了
        this.events.push(cancelEvent);
    };
    ViewGroupNesting.prototype.update = function () {
        if (this.events.length === 0)
            return;
        for (var index = 0; index < this.events.length; index++) {
            this.node.dispatchEvent(this.events[index]);
        }
        this.events.length = 0;
    };
    ViewGroupNesting = __decorate([
        ccclass
    ], ViewGroupNesting);
    return ViewGroupNesting;
}(cc.Component));
exports.default = ViewGroupNesting;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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