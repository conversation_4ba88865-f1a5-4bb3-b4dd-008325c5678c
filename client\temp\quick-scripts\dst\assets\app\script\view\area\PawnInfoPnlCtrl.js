
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/PawnInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0bf3bKAGhA8oj2JJzlsoGa', 'PawnInfoPnlCtrl');
// app/script/view/area/PawnInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PawnInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PawnInfoPnlCtrl, _super);
    function PawnInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_be_n
        _this.lvEditLbl_ = null; // path://root/head_be_n/lv/edit/num/lv_edit_l
        _this.attackSpeedEditLbl_ = null; // path://root/head_be_n/attack_speed/edit/num/attack_speed_edit_l
        _this.attrNode_ = null; // path://root/attr_n_be
        _this.skillNode_ = null; // path://root/skill_n
        _this.equipNode_ = null; // path://root/equip_n
        _this.fromNode_ = null; // path://root/from_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.buffNode_ = null; // path://root/buff/buff_n
        _this.selectSkinBoxNode_ = null; // path://select_skin_box_be_n
        _this.syncSkinNode_ = null; // path://select_skin_box_be_n/sync_skin_be_n
        _this.syncSkinMaskNode_ = null; // path://select_skin_box_be_n/sync_skin_mask_be_n
        _this.settingSyncSkinNode_ = null; // path://select_skin_box_be_n/setting_sync_skin_n
        _this.selectEquipBoxNode_ = null; // path://select_equip_box_be_n
        _this.syncEquipNode_ = null; // path://select_equip_box_be_n/sync_equip_be_n
        _this.syncEquipMaskNode_ = null; // path://select_equip_box_be_n/sync_equip_mask_be_n
        _this.settingSyncEquipNode_ = null; // path://select_equip_box_be_n/setting_sync_equip_n
        _this.selectPetBoxNode_ = null; // path://select_pet_box_be_n
        _this.selectArmyBoxNode_ = null; // path://select_army_box_be_n
        //@end
        _this.root = null;
        _this.data = null;
        _this.drillInfo = null;
        _this.fromTo = '';
        _this.preAttackSpeed = 0;
        _this.preEquipUid = '';
        _this.preSkinId = 0;
        _this.prePetId = 0;
        _this.preRootHeight = -1;
        _this.isCanEditSkin = false;
        _this.isCanEdit = false;
        _this.isConfPawn = false;
        _this.isBattleing = false;
        return _this;
    }
    PawnInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_TREASURE] = this.onUpdatePawnTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_BUFF] = this.onUpdateBuff, _c.enter = true, _c),
        ];
    };
    PawnInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.FindChild('root');
                this.selectSkinBoxNode_.active = false;
                this.selectEquipBoxNode_.active = false;
                this.selectArmyBoxNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    PawnInfoPnlCtrl.prototype.onEnter = function (data, drillInfo, fromTo) {
        var _a, _b;
        GameHelper_1.gameHpr.uiShowPawnData = data;
        this.data = data;
        this.drillInfo = drillInfo;
        this.fromTo = fromTo;
        this.preAttackSpeed = data.attackSpeed;
        this.preEquipUid = data.equip.uid;
        this.prePetId = data.petId;
        var isOwner = data.isOwner();
        if (isOwner && !GameHelper_1.gameHpr.isSpectate() && !GameHelper_1.gameHpr.user.isHasPawnSkinById(data.skinId)) {
            data.skinId = 0;
        }
        this.preSkinId = data.skinId;
        this.data.recordCurrHp(true); //先记录一下
        var isBattleing = this.isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex), hasOwner = !!data.owner;
        var isCanEdit = this.isCanEdit = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && !drillInfo && (!fromTo || fromTo === 'area_army');
        var isConfPawn = this.isConfPawn = !data.uid && !drillInfo && fromTo !== 'ceri' && fromTo !== 'book'; //配置士兵
        var isInArea = mc.currWindName === 'area'; //是否在战斗场景
        var isFromDrillground = fromTo === 'drillground';
        var isMachine = data.type >= Enums_1.PawnType.MACHINE; //是否器械
        var isHero = data.isHero(); //是否英雄
        var isCanEditEquip = isOwner && !isBattleing && data.getState() === Enums_1.PawnState.NONE && (!fromTo || fromTo === 'area_army');
        // 头像
        var editNode = this.headNode_.Child('edit'), headValNode = this.headNode_.Child('val');
        headValNode.active = true;
        if (data.isBoss()) { //boss加载mini头像
            headValNode.y = -32;
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(data.getViewId(), headValNode, this.key);
        }
        else {
            headValNode.y = -36;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.getViewId(), headValNode, this.key);
        }
        editNode.active = this.isCanEditSkin = this.headNode_.Component(cc.Button).interactable = !isHero && (isCanEdit || isConfPawn || fromTo === 'book');
        this.headNode_.Child('name/val').setLocaleKey(data.name);
        this.headNode_.Child('name/type').setLocaleKey(data.type ? data.typeName : '');
        // 同步皮肤设置
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        if (this.syncSkinNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
            this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 等级
        this.updateLv();
        // 出手速度
        var attackSpeed = this.headNode_.Child('attack_speed');
        if (attackSpeed.active = fromTo !== 'book' && fromTo !== 'ceri') {
            var isCanEditAs = !drillInfo && (isCanEdit || isConfPawn);
            attackSpeed.Child('edit').active = isCanEditAs;
            attackSpeed.Child('val').active = !isCanEditAs;
            if (isCanEditAs) {
                this.attackSpeedEditLbl_.string = data.attackSpeed + '';
            }
            else {
                attackSpeed.Child('val', cc.Label).string = data.attackSpeed + '';
            }
        }
        // 属性
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        // 技能
        this.updateSkills();
        // 装备 和 背包
        var isYyj = ((_b = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.YANG_YOUJI;
        var isEquip = !isMachine && (hasOwner || isConfPawn || !!drillInfo || isYyj);
        if (this.equipNode_.active = isEquip || !!this.preEquipUid) {
            var info = this.equipNode_.Child('info');
            // 装备
            info.Child('edit_equip_be').active = isCanEditEquip || isConfPawn;
            this.updateEquipInfo(info.Child('equip_show_be'));
            // 宠物
            info.Child('edit_pet_be').active = isCanEditEquip && isYyj;
            var petNode = info.Child('pet_show_be');
            if (petNode.active = isYyj) {
                this.updatePetInfo(petNode);
            }
            // 背包
            var bagNode = info.Child('bag');
            if (isEquip) {
                this.updateBag(bagNode, data.treasures);
            }
            else {
                bagNode.Swih('');
            }
        }
        // 同步装备设置
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        if (this.syncEquipNode_.active = !isConfPawn && isCanEditEquip) {
            var val = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
            this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
        }
        // 归属
        var from = this.fromNode_;
        var showMarchSpeed = data.marchSpeed > 0 && !data.uid, showArmy = !!data.armyName, showPlayer = hasOwner && !isOwner, showCost = (fromTo === 'book' || fromTo === 'ceri') && !GameHelper_1.gameHpr.isNoviceMode;
        if (from.active = showMarchSpeed || showArmy || showPlayer || showCost) {
            // 行军速度
            if (from.Child('march_speed').active = showMarchSpeed) {
                from.Child('march_speed/val', cc.Label).setLocaleKey('ui.march_speed_desc', data.marchSpeed);
            }
            // 所属军队
            var army = from.Child('army');
            if (army.active = showArmy) {
                army.Child('val/edit').active = isCanEdit && isInArea && !isFromDrillground;
                this.updateArmyInfo(army, data.armyName);
            }
            var playerInfo = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
            // 所属玩家
            if (from.Child('player').active = showPlayer) {
                from.Child('player/val', cc.Label).string = ut.nameFormator((playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.nickname) || '???', 8);
            }
            // 所属联盟
            var alliName = playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.allianceName;
            if (from.Child('alli').active = showPlayer && !!alliName) {
                from.Child('alli/val', cc.Label).string = alliName;
            }
            // 费用成本
            if (from.Child('cost').active = showCost) {
                ViewHelper_1.viewHelper.updatePawnCostFactor(from.Child('cost/lay/cost_desc_be'), data.id, data.baseJson);
            }
        }
        // 按钮
        do {
            var buttonRoot = this.buttonNode_.Child('root');
            var isEditPos = isCanEdit && isInArea && !isFromDrillground;
            if (isEditPos) {
                buttonRoot.Swih('edit_pos_be');
            }
            else if (drillInfo) {
                var node = buttonRoot.Swih('cancel_' + drillInfo.type + '_be')[0];
                if (drillInfo.type === 'drill') {
                    node.Child('val').setLocaleKey(data.isMachine() ? 'ui.button_cancel_sc' : 'ui.button_cancel_drill');
                }
                else if (drillInfo.type === 'cure') {
                    node.Child('val').setLocaleKey('ui.button_cancel_cure');
                }
            }
            else {
                this.buttonNode_.active = false;
                break;
            }
            var player = GameHelper_1.gameHpr.player;
            var isLving = player.isInPawnLvingQueue(data.uid);
            this.buttonNode_.active = true;
            this.buttonNode_.Child('uplv_be').active = isEditPos && !isMachine && !data.isMaxLv() && !isLving;
            var avatarNode = this.buttonNode_.Child('avatar_be');
            var isCanAvatar = isOwner && !isBattleing && !isHero && !drillInfo;
            if (avatarNode.active = isCanAvatar) {
                var army = GameHelper_1.gameHpr.areaCenter.getArmy(data.aIndex, data.armyUid);
                var isNotAvatar = !army || army.pawns.some(function (m) { return m.isHero(); }) || player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0 || player.getHeroSlots().every(function (m) { return !m.hero; }) || !player.checkCanAvatarPawn(data.id);
                avatarNode.opacity = isNotAvatar ? 100 : 255;
            }
        } while (false);
        // 刷新buff
        this.updateBuffs();
        this.buffNode_.stopAllActions();
        this.buffNode_.opacity = 0;
        cc.tween(this.buffNode_).delay(0.2).to(0.5, { opacity: 255 }).start();
    };
    PawnInfoPnlCtrl.prototype.onRemove = function () {
        GameHelper_1.gameHpr.uiShowPawnData = null;
        this.selectEquipBoxNode_.active = false;
        this.closeArmyList();
        this.syncInfoToServer();
        this.data.recordCurrHp(false);
        this.data = null;
    };
    PawnInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/head_be_n/attack_speed/edit/0/attack_speed_be
    PawnInfoPnlCtrl.prototype.onClickAttackSpeed = function (event, data) {
        var type = event.target.parent.name;
        this.data.changeAttackSpeed(type === '0' ? -1 : 1);
        this.attackSpeedEditLbl_.string = this.data.attackSpeed + '';
    };
    // path://root/button_n/root/edit_pos_be
    PawnInfoPnlCtrl.prototype.onClickEditPos = function (event, data) {
        this.emit(EventType_1.default.EDIT_PAWN_POS, this.data.aIndex, this.data.uid);
        ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
        this.hide();
    };
    // path://root/button_n/root/cancel_drill_be
    PawnInfoPnlCtrl.prototype.onClickCancelDrill = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox(this.data.isMachine() ? 'ui.cancel_sc_no_back_cost_tip' : 'ui.cancel_drill_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelDrill(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelDrill(this.drillInfo);
    };
    // path://root/button_n/root/cancel_lving_be
    PawnInfoPnlCtrl.prototype.onClickCancelLving = function (event, _) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_lving_no_back_cost_tip', {
                ok: function () {
                    _this.cancelLving(_this.drillInfo);
                },
                cancel: function () { },
            });
        }
        this.cancelLving(this.drillInfo);
    };
    // path://root/equip_n/info/edit_equip_be
    PawnInfoPnlCtrl.prototype.onClickEditEquip = function (event, data) {
        this.showEquipList();
    };
    // path://select_equip_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectEquipBox = function (event, data) {
        this.closeEquipList();
    };
    // path://select_equip_box_be_n/root/list/view/content/equip_item_be
    PawnInfoPnlCtrl.prototype.onClickEquipItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        this.data.changeEquip(data);
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        if (this.data.id === 3104) { //陌刀兵还需要刷新技能信息
            this.skillNode_.Child('root/skills').children.forEach(function (m) {
                if (!m.Data) {
                }
                else if (m.Data.type === Enums_1.PawnSkillType.INSTABILITY_ATTACK) {
                    m.Data.desc_params = [_this.data.getAttackText()];
                }
                else if (m.Data.type === Enums_1.PawnSkillType.PEOPLE_BROKEN) {
                    m.Data.desc_params = [_this.data.getAttackTextByIndex(2)];
                }
            });
        }
        if (GameHelper_1.gameHpr.guide.isGuideById(3)) {
            this.closeEquipList();
        }
        else {
            this.updateEquipListSelect(data);
        }
    };
    // path://root/from_n/army/val/edit/edit_army_be
    PawnInfoPnlCtrl.prototype.onClickEditArmy = function (event, data) {
        this.showArmyList();
    };
    // path://select_army_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectArmyBox = function (event, data) {
        this.closeArmyList();
    };
    // path://select_army_box_be_n/root/list/army_item_be
    PawnInfoPnlCtrl.prototype.onClickArmyItem = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data) {
            if (this.data.isHero() && data.isHasHero()) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
            }
            return this.changeArmy(data.uid, false);
        }
        else if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.changeArmy(name, true);
            }
        });
    };
    // path://root/skill_n/root/skills/skill_be
    PawnInfoPnlCtrl.prototype.onClickSkill = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkillInfoBox', event.target.Data);
    };
    // path://root/equip_n/info/equip_show_be
    PawnInfoPnlCtrl.prototype.onClickEquipShow = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data === null || data === void 0 ? void 0 : data.id) {
            ViewHelper_1.viewHelper.showPnl('common/EquipInfoBox', data);
        }
        else {
            this.showEquipList();
        }
    };
    // path://root/equip_n/info/bag/bag_be
    PawnInfoPnlCtrl.prototype.onClickBag = function (event, data) {
        if (this.data.owner !== GameHelper_1.gameHpr.getUid()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_OPEN_OTHER_TREASURE);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', [event.target.Data]);
        }
    };
    // path://root/attr_n_be
    PawnInfoPnlCtrl.prototype.onClickAttr = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/PawnAttrBox', this.data);
    };
    // path://select_skin_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectSkinBox = function (event, data) {
        this.closeSkinList(this.selectSkinBoxNode_.Child('skin_show').Data || 0);
    };
    // path://select_skin_box_be_n/root/list/view/content/skin_item_be
    PawnInfoPnlCtrl.prototype.onClickSkinItem = function (event, _) {
        var data = event.target.Data;
        data && this.updateSkinListSelect(data);
    };
    // path://root/head_be_n
    PawnInfoPnlCtrl.prototype.onClickHead = function (event, data) {
        audioMgr.playSFX('click');
        this.showSkinList();
    };
    // path://select_skin_box_be_n/root/button/buy_skin_be
    PawnInfoPnlCtrl.prototype.onClickBuySkin = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (data && data.gold > 0) {
            if (GameHelper_1.gameHpr.user.getGold() < data.gold) {
                return ViewHelper_1.viewHelper.showGoldNotEnough();
            }
            GameHelper_1.gameHpr.user.buyPawnSkin(data.id).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.closeSkinList(data.id);
                }
            });
        }
    };
    // path://select_equip_box_be_n/sync_equip_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquip = function (event, data) {
        this.settingSyncEquipNode_.active = true;
        this.syncEquipMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0) + '';
        this.settingSyncEquipNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_equip_box_be_n/sync_equip_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncEquipMask = function (event, data) {
        var it = this.settingSyncEquipNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF, val);
        this.settingSyncEquipNode_.active = false;
        this.syncEquipMaskNode_.active = false;
        this.syncEquipNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/uplv_be
    PawnInfoPnlCtrl.prototype.onClickUplv = function (event, data) {
        var _this = this;
        if (!this.data || this.data.isMaxLv() || this.data.isMachine()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('area/UpPawnLv', this.data, function (ok) { return __awaiter(_this, void 0, void 0, function () {
            var cond;
            var _a, _b;
            return __generator(this, function (_c) {
                if (!ok || !this.isValid) {
                    return [2 /*return*/, true];
                }
                else if (GameHelper_1.gameHpr.player.getUpScroll() < 1) {
                    ViewHelper_1.viewHelper.showAlert('toast.res_deficiency', { params: [Constant_1.CTYPE_NAME[Enums_1.CType.UP_SCROLL]] });
                    return [2 /*return*/, false];
                }
                cond = GameHelper_1.gameHpr.checkCondsByString((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.attrJson) === null || _b === void 0 ? void 0 : _b.lv_cond);
                if ((cond === null || cond === void 0 ? void 0 : cond.type) === Enums_1.CType.BUILD_LV) {
                    ViewHelper_1.viewHelper.showAlert('toast.build_cond_unmet', { params: ['buildText.name_' + cond.id, cond.count] });
                    return [2 /*return*/, false];
                }
                return [2 /*return*/, this.upLvByUseScroll()];
            });
        }); });
    };
    // path://root/buff/buff_n/buff_icon_be
    PawnInfoPnlCtrl.prototype.onClickBuffIcon = function (event, _) {
        var data = event.target.Data;
        if (!data) {
        }
        else if (data.iconType === 3) { //韬略
            ViewHelper_1.viewHelper.showPnl('area/PawnStrategyInfo', this.data);
        }
        else if (data.iconType === 4) { //政策
            ViewHelper_1.viewHelper.showPnl('area/PolicyBuffInfo', data.buffs);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/BuffInfoBox', data);
        }
    };
    // path://root/head_be_n/lv/edit/0/edit_lv_be
    PawnInfoPnlCtrl.prototype.onClickEditLv = function (event, data) {
        var type = event.target.parent.name;
        var val = type === '0' ? -1 : 1;
        var lv = this.data.lv + val;
        if (lv > 6) {
            lv = 1;
        }
        else if (lv < 1) {
            lv = 6;
        }
        this.localUplv(this.data, lv);
    };
    // path://root/button_n/avatar_be
    PawnInfoPnlCtrl.prototype.onClickAvatar = function (event, _) {
        var _this = this;
        var pawn = this.data;
        var army = GameHelper_1.gameHpr.areaCenter.getArmy(pawn.aIndex, pawn.armyUid);
        if (!army) {
            return;
        }
        else if (army.pawns.some(function (m) { return m.isHero(); })) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_ONLY_AVATAR_ONE);
        }
        else if (GameHelper_1.gameHpr.player.getBuildLv(Enums_1.BUILD_NID.HERO_HALL) <= 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_build_first', { params: ['buildText.name_' + Enums_1.BUILD_NID.HERO_HALL] });
        }
        else if (GameHelper_1.gameHpr.player.getHeroSlots().every(function (m) { return !m.hero; })) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_worship_hero'); //请先在英雄殿供奉一个英雄
        }
        ViewHelper_1.viewHelper.showPnl('area/SelectAvatarHero', pawn.id, function (portrayalId) {
            if (!_this.isValid || !portrayalId) {
                return;
            }
            GameHelper_1.gameHpr.player.changePawnPortrayal(pawn.aIndex, pawn.armyUid, pawn.uid, portrayalId).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                    ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
                    // 聚焦士兵
                    _this.emit(EventType_1.default.FOCUS_PAWN, { index: pawn.aIndex, uid: pawn.uid, point: pawn.point });
                }
            });
        });
    };
    // path://root/skill_n/root/portrayal_skill_be
    PawnInfoPnlCtrl.prototype.onClickPortrayalSkill = function (event, data) {
        audioMgr.playSFX('click');
        if (this.data.portrayal) {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', this.data.portrayal, 'pawn', this.data.owner);
        }
    };
    // path://root/equip_n/info/pet_show_be
    PawnInfoPnlCtrl.prototype.onClickPetShow = function (event, _) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        if (id) {
            ViewHelper_1.viewHelper.showPnl('common/PetInfoBox', id, Constant_1.SUMMON_LV[this.data.lv]);
        }
        else {
            this.showPetList();
        }
    };
    // path://root/equip_n/info/edit_pet_be
    PawnInfoPnlCtrl.prototype.onClickEditPet = function (event, data) {
        this.showPetList();
    };
    // path://select_pet_box_be_n
    PawnInfoPnlCtrl.prototype.onClickSelectPetBox = function (event, data) {
        this.closePetList();
    };
    // path://select_pet_box_be_n/root/list/view/content/pet_item_be
    PawnInfoPnlCtrl.prototype.onClickPetItem = function (event, _) {
        var id = event.target.Data;
        if (id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id]) {
            this.data.setPetId(id);
        }
        this.updatePetListSelect(id);
    };
    // path://select_skin_box_be_n/sync_skin_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkin = function (event, data) {
        this.settingSyncSkinNode_.active = true;
        this.syncSkinMaskNode_.active = true;
        var val = (GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0) + '';
        this.settingSyncSkinNode_.Child('lay').children.forEach(function (m) {
            m.Component(cc.Toggle).isChecked = m.name === val;
        });
    };
    // path://select_skin_box_be_n/sync_skin_mask_be_n
    PawnInfoPnlCtrl.prototype.onClickSyncSkinMask = function (event, data) {
        var it = this.settingSyncSkinNode_.Child('lay').children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        var val = it ? Number(it.name) : 0;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF, val);
        this.settingSyncSkinNode_.active = false;
        this.syncSkinMaskNode_.active = false;
        this.syncSkinNode_.Child('lay/val', cc.MultiFrame).setFrame(val);
    };
    // path://root/button_n/root/cancel_cure_be
    PawnInfoPnlCtrl.prototype.onClickCancelCure = function (event, data) {
        var _this = this;
        if (!this.drillInfo) {
            return;
        }
        else if (this.drillInfo.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(_this.drillInfo); },
                cancel: function () { },
            });
        }
        this.cancelCure(this.drillInfo);
    };
    // path://root/from_n/cost/lay/desc/cost_desc_be
    PawnInfoPnlCtrl.prototype.onClickCostDesc = function (event, data) {
        if (this.data) {
            ViewHelper_1.viewHelper.showPnl('common/PawnCostFactorDesc', this.data);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PawnInfoPnlCtrl.prototype.onUpdatePawnTreasure = function (pawn) {
        if (pawn.uid === this.data.uid) {
            this.updateBag(this.equipNode_.Child('info/bag'), pawn.treasures);
        }
    };
    PawnInfoPnlCtrl.prototype.onUpdateArmy = function (army) {
        if (this.data.armyUid === army.uid) {
            var uid_1 = this.data.uid;
            this.data = army.pawns.find(function (m) { return m.uid === uid_1; }) || this.data;
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.onUpdateBuff = function () {
        this.updateBuffs();
    };
    // 刷新士兵信息
    PawnInfoPnlCtrl.prototype.onUpdatePawnInfo = function () {
        this.updateLv();
        ViewHelper_1.viewHelper.updatePawnAttrs(this.attrNode_, this.data);
        this.updateSkills();
        var uplvButton = this.buttonNode_.Child('uplv_be');
        uplvButton.active = uplvButton.active && !this.data.isMaxLv();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateLv = function () {
        var data = this.data;
        var lvNode = this.headNode_.Child('lv'), isNoLv = data.isMachine() || data.isBuilding();
        if (lvNode.Child('edit').active = !isNoLv && (this.fromTo === 'book' || this.fromTo === 'ceri')) {
            lvNode.Child('name').Color('#A18876').setLocaleKey('ui.level');
            lvNode.Child('val').active = false;
            this.lvEditLbl_.string = data.lv + '';
        }
        else if (lvNode.Child('val').active = !isNoLv) {
            var hasOwner = !!data.owner, isOwner = data.isOwner(), isLving = GameHelper_1.gameHpr.player.isInPawnLvingQueue(data.uid);
            var lvColor = hasOwner && data.isMaxLv() ? '#B6A591' : '#A18876';
            lvNode.Child('name').Color(lvColor).setLocaleKey('ui.level');
            lvNode.Child('val/0', cc.Label).Color(lvColor).string = data.lv + '';
            lvNode.Child('val/up').active = isOwner && isLving && !this.drillInfo;
        }
        else {
            lvNode.Child('name').Color('#B6A591').setLocaleKey('ui.not_lv');
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkills = function () {
        this.skillNode_.active = ViewHelper_1.viewHelper.updatePawnSkills(this.skillNode_.Child('root'), this.data, this.key);
    };
    PawnInfoPnlCtrl.prototype.updateListPosition = function () {
        if (this.preRootHeight !== this.root.height) {
            this.preRootHeight = this.root.height;
            // 皮肤列表
            var node = this.selectSkinBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('skin_list'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncSkinNode_.setPosition(node.getPosition());
            this.settingSyncSkinNode_.scale = this.root.scale;
            node = this.selectSkinBoxNode_.Child('skin_show');
            node.setPosition(ut.convertToNodeAR(this.headNode_, this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            node = this.syncSkinNode_;
            node.setPosition(ut.convertToNodeAR(this.headNode_.Child('sync_pos'), this.selectSkinBoxNode_));
            node.scale = this.root.scale;
            // 装备列表
            node = this.selectEquipBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_list'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            this.settingSyncEquipNode_.setPosition(node.getPosition());
            this.settingSyncEquipNode_.scale = this.root.scale;
            node = this.selectEquipBoxNode_.Child('equip_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/equip_show_be'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            node = this.syncEquipNode_;
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/sync_pos'), this.selectEquipBoxNode_));
            node.scale = this.root.scale;
            // 宠物列表
            node = this.selectPetBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_list'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            node = this.selectPetBoxNode_.Child('pet_show');
            node.setPosition(ut.convertToNodeAR(this.equipNode_.Child('info/pet_show_be'), this.selectPetBoxNode_));
            node.scale = this.root.scale;
            // 军队列表
            node = this.selectArmyBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.fromNode_.Child('army/val/edit/list'), this.selectArmyBoxNode_));
            node.scale = this.root.scale;
        }
    };
    // 显示皮肤列表 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.showSkinList = function () {
        var _this = this;
        var _a, _b;
        this.headNode_.Child('edit').active = this.headNode_.Child('val').active = false;
        this.selectSkinBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectSkinBoxNode_.Child('root');
        var skins = GameHelper_1.gameHpr.user.getPawnSkins(this.data.id), len = skins.length;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(skins, function (it, data) {
            it.Data = data;
            var valNode = it.Child('val');
            valNode.opacity = data.unlock ? 255 : 120;
            ResHelper_1.resHelper.loadPawnHeadIcon((data === null || data === void 0 ? void 0 : data.id) || _this.data.id, valNode, _this.key);
        });
        var skinId = this.data.skinId;
        var index = skins.findIndex(function (m) { return m.id === skinId; });
        if (index === -1) {
            index = 0;
            skinId = this.data.skinId = (_b = (_a = skins[index]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0;
        }
        var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
        var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
        var minx = Math.max(w - pw, 0);
        sv.stopAutoScroll();
        sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        this.updateSkinListSelect(skins[index]);
    };
    PawnInfoPnlCtrl.prototype.closeSkinList = function (skinId) {
        this.headNode_.Child('val').active = true;
        this.headNode_.Child('edit').active = this.isCanEditSkin;
        this.selectSkinBoxNode_.active = false;
        if (this.data.skinId !== skinId) {
            this.data.skinId = skinId;
            ResHelper_1.resHelper.loadPawnHeadIcon(this.data.getViewId(), this.headNode_.Child('val'), this.key);
            if (this.fromTo !== 'book') {
                if (!this.data.uid && !this.drillInfo) {
                    GameHelper_1.gameHpr.player.changeConfigPawnInfoByData(this.data);
                }
                eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this.data);
            }
        }
    };
    PawnInfoPnlCtrl.prototype.updateSkinListSelect = function (skin) {
        var _a;
        var root = this.selectSkinBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var _a;
            var id = ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id) || 0;
            it.Child('select1').active = it.Child('select2').active = skin.id === id;
            it.Component(cc.Button).interactable = skin.id !== id;
        });
        // 显示选择的
        if (skin.unlock || !skin.id) {
            var node = this.selectSkinBoxNode_.Child('skin_show');
            node.Data = skin.id;
            ResHelper_1.resHelper.loadPawnHeadIcon(skin.id || this.data.id, node.Child('val'), this.key);
        }
        // 显示信息
        var desc = skin.id ? ((_a = assetsMgr.getJsonData('pawnSkin', skin.id)) === null || _a === void 0 ? void 0 : _a.desc) || '' : 'ui.default_pawn_skin';
        root.Child('desc').setLocaleKey(desc);
        var stateNode = root.Child('state');
        if (stateNode.active = !!skin.id && !skin.gold) {
            stateNode.Color(skin.unlock ? '#4AB32E' : '#A18876').setLocaleKey(skin.unlock ? 'ui.yet_owned' : 'ui.not_owned');
        }
        var buttonNode = root.Child('button');
        if (buttonNode.active = !!skin.gold) {
            var buyNode = buttonNode.Child('buy_skin_be');
            buyNode.Data = skin;
            buyNode.Child('lay/gold/val', cc.Label).string = skin.gold + '';
        }
    };
    // 刷新装备信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateEquipInfo = function (node, equip) {
        equip = node.Data = equip || this.data.equip;
        var isOwner = this.data.isOwner() && this.fromTo !== 'drillground';
        var isCanEditEquip = isOwner && !this.isBattleing && this.data.getState() === Enums_1.PawnState.NONE && (!this.fromTo || this.fromTo === 'area_army');
        node.Component(cc.Button).interactable = !!(equip === null || equip === void 0 ? void 0 : equip.id) || this.isConfPawn || isCanEditEquip;
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else if (isOwner || this.isCanEdit || this.isConfPawn) {
            node.Swih('add')[0].Child('dot').active = GameHelper_1.gameHpr.player.getEquips().length > 0;
        }
        else {
            node.Swih('');
        }
    };
    // 显示装备列表
    PawnInfoPnlCtrl.prototype.showEquipList = function () {
        var _this = this;
        var _a, _b;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectEquipBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectEquipBoxNode_.Child('root');
        var equips = GameHelper_1.gameHpr.player.getPawnEquips(this.data.id), len = equips.length;
        var randomArr = ut.stringToNumbers((_b = (_a = assetsMgr.getJson('equipBase').get('exclusive_pawn', this.data.id)) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.random, ',');
        var isHp = randomArr[0] > randomArr[1];
        equips.sort(function (a, b) {
            var aw = a.isExclusive() ? 1 : 0, bw = b.isExclusive() ? 1 : 0;
            if (isHp) {
                aw = aw * 10 + (a.hp ? 1 : 0);
                bw = bw * 10 + (b.hp ? 1 : 0);
            }
            else {
                aw = aw * 10 + (a.attack ? 1 : 0);
                bw = bw * 10 + (b.attack ? 1 : 0);
            }
            return bw - aw;
        });
        var equip = this.equipNode_.Child('info/equip_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items(equips, function (it, data) {
            it.Data = data;
            ResHelper_1.resHelper.loadEquipIcon(data.id, it.Child('val'), _this.key, data.getSmeltCount());
            it.Child('recommend').active = false;
        });
        if (equip === null || equip === void 0 ? void 0 : equip.uid) {
            var index = equips.findIndex(function (m) { return m.uid === equip.uid; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updateEquipListSelect(equip);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updateEquipListSelect = function (equip) {
        var _this = this;
        var root = this.selectEquipBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var data = it.Data;
            var select = it.Child('select').active = (equip === null || equip === void 0 ? void 0 : equip.uid) === (data === null || data === void 0 ? void 0 : data.uid);
            it.Component(cc.Button).interactable = !select;
        });
        // 显示选择的
        var node = this.selectEquipBoxNode_.Child('equip_show');
        if (equip === null || equip === void 0 ? void 0 : equip.id) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        root.Child('empty').active = !(equip === null || equip === void 0 ? void 0 : equip.id);
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        sv.stopAutoScroll();
        info.y = 0;
        if (sv.setActive(!!(equip === null || equip === void 0 ? void 0 : equip.id))) {
            ViewHelper_1.viewHelper.updateEquipView(info, equip, this.key);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = Math.min(320, info.height + 4);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    PawnInfoPnlCtrl.prototype.closeEquipList = function () {
        this.selectEquipBoxNode_.active = false;
        this.updateEquipInfo(this.equipNode_.Child('info/equip_show_be'));
    };
    // 刷新宠物信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updatePetInfo = function (node, id) {
        id = node.Data = id || this.data.petId || Constant_1.DEFAULT_PET_ID;
        ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Child('val'), this.key);
    };
    // 显示宠物列表
    PawnInfoPnlCtrl.prototype.showPetList = function () {
        var _this = this;
        if (this.data.isOwner() && this.isBattleing) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.BATTLEING);
        }
        this.selectPetBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectPetBoxNode_.Child('root');
        var pets = assetsMgr.getJson('pawnBase').datas.filter(function (m) { return m.type === Enums_1.PawnType.BEAST && !!m.velocity; }).sort(function (a, b) { return a.drill_time - b.drill_time; }), len = pets.length;
        var id = this.equipNode_.Child('info/pet_show_be').Data;
        var sv = root.Child('list', cc.ScrollView);
        var killRecordMap = GameHelper_1.gameHpr.player.getKillRecordMap();
        sv.Items(pets, function (it, json) {
            it.Data = json.id;
            var icon = it.Child('val');
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(json.id, icon, _this.key);
            icon.opacity = json.id === Constant_1.DEFAULT_PET_ID || killRecordMap[json.id] ? 255 : 120;
        });
        if (id) {
            var index = pets.findIndex(function (m) { return m.id === id; });
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, index * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePetListSelect(id);
    };
    // 刷新选择信息
    PawnInfoPnlCtrl.prototype.updatePetListSelect = function (id) {
        var root = this.selectPetBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            it.Child('select').active = id === it.Data;
            it.Component(cc.Button).interactable = id !== it.Data;
        });
        // 显示选择的
        var node = this.selectPetBoxNode_.Child('pet_show');
        if (id) {
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(id, node.Swih('val')[0], this.key);
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        var lv = id === Constant_1.DEFAULT_PET_ID || GameHelper_1.gameHpr.player.getKillRecordMap()[id] ? Constant_1.SUMMON_LV[this.data.lv] : 0;
        ViewHelper_1.viewHelper.updatePetView(root.Child('info'), id, lv);
    };
    PawnInfoPnlCtrl.prototype.closePetList = function () {
        this.selectPetBoxNode_.active = false;
        this.updatePetInfo(this.equipNode_.Child('info/pet_show_be'));
    };
    // 刷新军队信息 ----------------------------------------------------------------------------
    PawnInfoPnlCtrl.prototype.updateArmyInfo = function (node, armyName) {
        node = node || this.fromNode_.Child('army');
        armyName = armyName || this.data.armyName;
        node.Child('val/name/val', cc.Label).string = armyName;
    };
    // 显示军队列表
    PawnInfoPnlCtrl.prototype.showArmyList = function () {
        var _a;
        this.selectArmyBoxNode_.active = true;
        this.updateListPosition();
        var root = this.selectArmyBoxNode_.Child('root');
        var uid = this.data.armyUid;
        var arr = ((_a = GameHelper_1.gameHpr.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys) || [];
        var armys = [];
        arr.forEach(function (m) {
            if (m.uid == uid) {
                armys.unshift(m);
            }
            else if (m.isCanDrillPawn() && m.getActPawnCount() < Constant_1.ARMY_PAWN_MAX_COUNT) {
                armys.push(m);
            }
        });
        if (armys.length < GameHelper_1.gameHpr.player.getArmyMaxCount()) {
            armys.push(null);
        }
        var len = armys.length;
        root.Swih('list')[0].Items(armys, function (it, data, i) {
            it.Data = data;
            var select = uid === (data === null || data === void 0 ? void 0 : data.uid);
            it.Child('name', cc.Label).Color(select ? '#B6A591' : '#756963').string = (data === null || data === void 0 ? void 0 : data.name) || '';
            it.Child('line').active = i < (len - 1);
            it.Child('select').active = select;
            it.Child('add').active = !data;
            it.Component(cc.Button).interactable = !select;
        });
    };
    PawnInfoPnlCtrl.prototype.closeArmyList = function () {
        this.selectArmyBoxNode_.active = false;
    };
    // 改变军队
    PawnInfoPnlCtrl.prototype.changeArmy = function (newArmyUid, isNewCreate) {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, index, armyUid, uid, attackSpeed, equipUid, skinId, petId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pawn = this.data;
                        index = pawn.aIndex;
                        armyUid = pawn.armyUid;
                        uid = pawn.uid;
                        attackSpeed = pawn.attackSpeed;
                        equipUid = pawn.equip.uid;
                        skinId = pawn.skinId;
                        petId = pawn.petId;
                        if (!isNewCreate) {
                            pawn.armyUid = newArmyUid;
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnArmy({ index: index, armyUid: armyUid, uid: uid, newArmyUid: newArmyUid, isNewCreate: isNewCreate, attackSpeed: attackSpeed, equipUid: equipUid, skinId: skinId, petId: petId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            pawn.armyUid = armyUid;
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        pawn.changeArmy(data);
                        if (this.isValid) {
                            this.preAttackSpeed = attackSpeed;
                            this.preEquipUid = equipUid;
                            this.preSkinId = skinId;
                            this.updateArmyInfo();
                            this.closeArmyList();
                        }
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, index);
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 背包
    PawnInfoPnlCtrl.prototype.updateBag = function (node, treasures) {
        var _this = this;
        var _a;
        if (this.equipNode_.active && this.data) {
            var data = this.data;
            var cap = Math.max((treasures === null || treasures === void 0 ? void 0 : treasures.length) || 0, ((_a = data.baseJson) === null || _a === void 0 ? void 0 : _a.bag_cap) || 0);
            node.Items(cap, function (it, _, i) {
                var _a;
                var treasure = it.Data = treasures[i];
                it.Swih(treasure ? 'val' : 'empty');
                if (it.Component(cc.Button).interactable = !!treasure) {
                    var state = treasure.rewards.length > 0 ? 1 : 0;
                    ResHelper_1.resHelper.loadIcon('icon/treasure_' + (((_a = treasure.json) === null || _a === void 0 ? void 0 : _a.lv) || 1) + '_' + state, it.Child('val', cc.Sprite), _this.key);
                }
            });
        }
        else {
            node.Swih('');
        }
    };
    // 刷新buff
    PawnInfoPnlCtrl.prototype.updateBuffs = function () {
        var _this = this;
        var _a;
        var buffs = [], policyBuffs = [];
        var heroSkill = (_a = this.data.portrayal) === null || _a === void 0 ? void 0 : _a.skill;
        this.data.buffs.forEach(function (m) {
            var _a, _b;
            if (m.iconType === 4) {
                return policyBuffs.push(m);
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD) {
                var buff = buffs.find(function (b) { return !!b && (b.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || b.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD); });
                if (buff) {
                    buff.tempParam = m.value;
                }
                else {
                    buffs.push(m);
                }
                return;
            }
            else if (m.type === Enums_1.BuffType.DELAY_DEDUCT_HP) {
                m.tempParam = (_b = (_a = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.FIXED_DAMAGE)) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 0;
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.data.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                m.tempParam = [(effect === null || effect === void 0 ? void 0 : effect.value) || 1, (effect === null || effect === void 0 ? void 0 : effect.odds) || 1];
                var v = m.tempParam[m.value];
                if (v) {
                    m.tempParam[m.value] = v * 2;
                }
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 判断是否叠满
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.CAO_REN ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.XU_CHU ? heroSkill.value : 200;
            }
            else if (m.type === Enums_1.BuffType.CHECK_ABNEGATION) { //吕蒙 检测克己
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.LV_MENG ? heroSkill.value : 0;
            }
            else if (m.type === Enums_1.BuffType.CHECK_LITTLE_GIRL) { //孙尚香 枭姬
                m.tempParam = _this.data.isMaxLv() ? 2 : 1;
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇是否满层
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI ? heroSkill.value : 50;
            }
            else if (m.type === Enums_1.BuffType.RECURRENCE) { //孟获 再起
                m.tempParam = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.MENG_HUO ? heroSkill.value : 15;
            }
            if (m.icon) {
                buffs.push(m);
            }
        });
        buffs.sort(function (a, b) {
            var aw = a ? a.effectType : 100, bw = b ? b.effectType : 100;
            return bw - aw;
        });
        // 是否有韬略
        if (this.data.isHasStrategy()) {
            buffs.unshift({ iconType: 3, icon: 0 });
        }
        // 是否有政策
        if (policyBuffs.length > 0) {
            buffs.unshift({ iconType: 4, icon: 1000, buffs: policyBuffs });
        }
        this.buffNode_.Items(buffs, function (it, data) {
            it.Data = data;
            it.Component(cc.MultiFrame).setFrame(data.iconType);
            ResHelper_1.resHelper.loadBuffIcon(data.icon, it.Child('val'), _this.key, false);
        });
    };
    // 使用卷轴升级士兵
    PawnInfoPnlCtrl.prototype.upLvByUseScroll = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawn, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.syncInfoToServer(true)]; //先同步一下属性
                    case 1:
                        _b.sent(); //先同步一下属性
                        pawn = this.data;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqUseUpScrollUpPawnLv({ index: pawn.aIndex, armyUid: pawn.armyUid, uid: pawn.uid })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, false];
                        }
                        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                        ViewHelper_1.viewHelper.showAlert('toast.up_pawn_lv_succeed');
                        this.localUplv(pawn, data.lv);
                        if (this.fromTo === 'area_army') {
                            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, pawn.index);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    PawnInfoPnlCtrl.prototype.localUplv = function (pawn, lv) {
        pawn.lv = lv;
        pawn.updateAttrJson();
        pawn.curHp = pawn.getMaxHp();
        pawn.recordCurrHp(true);
        if (this.isValid) {
            this.onUpdatePawnInfo();
        }
    };
    // 同步信息到服务器
    PawnInfoPnlCtrl.prototype.syncInfoToServer = function (wait) {
        return __awaiter(this, void 0, void 0, function () {
            var data, isEquip, isBattleing, isChange, id, equipUid, skinId, attackSpeed, res, syncEquip, syncSkin, res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.fromTo === 'book') {
                            return [2 /*return*/];
                        }
                        data = this.data;
                        isEquip = this.preEquipUid !== data.equip.uid;
                        isBattleing = data.isBattleing() || GameHelper_1.gameHpr.isBattleingByIndex(data.aIndex);
                        isChange = this.preAttackSpeed !== data.attackSpeed || isEquip || this.preSkinId !== data.skinId || this.prePetId !== data.petId;
                        if (!(!data.uid && !this.drillInfo && isChange)) return [3 /*break*/, 2];
                        id = data.id, equipUid = data.equip.uid, skinId = data.skinId, attackSpeed = data.attackSpeed;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangeConfigPawnEquip({ id: id, equipUid: equipUid, skinId: skinId, attackSpeed: attackSpeed }, wait)];
                    case 1:
                        res = _a.sent();
                        if (!res.err) {
                            GameHelper_1.gameHpr.player.changeConfigPawnInfo(id, equipUid, skinId, attackSpeed);
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        return [3 /*break*/, 4];
                    case 2:
                        if (!(data.isOwner() && !isBattleing && isChange)) return [3 /*break*/, 4];
                        syncEquip = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_EQUIP_CONF) || 0;
                        syncSkin = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SYNC_PAWN_SKIN_CONF) || 0;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqChangePawnAttr({
                                index: data.aIndex,
                                armyUid: data.armyUid,
                                uid: data.uid,
                                attackSpeed: data.attackSpeed,
                                equipUid: data.equip.uid,
                                syncEquip: syncEquip,
                                skinId: data.skinId,
                                syncSkin: syncSkin,
                                petId: data.petId,
                            }, wait)];
                    case 3:
                        res = _a.sent();
                        if (!res.err && !!syncEquip && isEquip) {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_pawn_equp_suc_' + syncEquip, { params: ['pawnText.name_' + data.id], showTime: 2 });
                        }
                        this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, data.index);
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 取消招募
    PawnInfoPnlCtrl.prototype.cancelDrill = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        var isMachine = this.data.isMachine();
        NetHelper_1.netHelper.reqCancelDrillPawn({ index: index, buildUid: info.buid, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnDrillQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: isMachine ? 'ui.cancel_sc_tip' : 'ui.cancel_drill_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消训练
    PawnInfoPnlCtrl.prototype.cancelLving = function (info) {
        var _this = this;
        var index = info.index;
        var uid = info.uid;
        var id = info.id;
        var lv = info.lv;
        NetHelper_1.netHelper.reqCancelPawnLving({ index: index, uid: uid }).then(function (res) {
            var _a;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                GameHelper_1.gameHpr.player.updatePawnLevelingQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_a = data.needCost) === null || _a === void 0 ? void 0 : _a.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', { text: 'ui.cancel_lving_tip', id: id, cost: data.needCost });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    // 取消治疗
    PawnInfoPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    PawnInfoPnlCtrl = __decorate([
        ccclass
    ], PawnInfoPnlCtrl);
    return PawnInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PawnInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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