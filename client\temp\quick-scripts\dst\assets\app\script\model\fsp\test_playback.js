
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/test_playback.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e0e96lkF0xK3Z5Wlx7Qein0', 'test_playback');
// app/script/model/fsp/test_playback.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
// 回放战斗
window['playbackBattle'] = function (uid) {
    return __awaiter(this, void 0, void 0, function () {
        var err;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!cc.sys.isBrowser) {
                        return [2 /*return*/];
                    }
                    eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
                    return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                case 1:
                    err = _a.sent();
                    if (err) {
                        return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                    }
                    return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                case 2:
                    _a.sent();
                    eventCenter.emit(mc.Event.LOAD_END_WIND);
                    ViewHelper_1.viewHelper.gotoWind('playback');
                    return [2 /*return*/];
            }
        });
    });
};
// 回放战斗
window['playbackBattle2'] = function () {
    return __awaiter(this, void 0, void 0, function () {
        var json;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!cc.sys.isBrowser) {
                        return [2 /*return*/];
                    }
                    eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
                    json = {
                        "_id": {
                            "$oid": "68b03972146287f4d774eb28"
                        },
                        "uid": "1756379506763006",
                        "frames": [
                            {
                                "hp": [
                                    10,
                                    10
                                ],
                                "builds": [],
                                "buildInfo": [
                                    0,
                                    0
                                ],
                                "armys": [
                                    {
                                        "uid": "1756377306528001",
                                        "name": "编队1",
                                        "owner": "18190079",
                                        "pawns": [
                                            {
                                                "uid": "1756291482161001",
                                                "point": {
                                                    "x": 10,
                                                    "y": 5
                                                },
                                                "equip": {
                                                    "id": 6002,
                                                    "attrs": [
                                                        [
                                                            0,
                                                            1,
                                                            108
                                                        ]
                                                    ]
                                                },
                                                "portrayal": {
                                                    "id": 330201,
                                                    "attrs": [
                                                        [
                                                            0,
                                                            1,
                                                            67
                                                        ],
                                                        [
                                                            0,
                                                            2,
                                                            7
                                                        ],
                                                        [
                                                            1,
                                                            25,
                                                            15
                                                        ],
                                                        [
                                                            2,
                                                            50012,
                                                            0
                                                        ]
                                                    ]
                                                },
                                                "index": 154145,
                                                "id": 3302,
                                                "lv": 3,
                                                "skinId": 3302102,
                                                "curHp": 338,
                                                "curAnger": 0,
                                                "AttackSpeed": 7,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            }
                                        ],
                                        "index": 154145,
                                        "state": 0,
                                        "enterDir": 1
                                    },
                                    {
                                        "uid": "1756377561564017",
                                        "name": "",
                                        "owner": "",
                                        "pawns": [
                                            {
                                                "uid": "1756377561564001",
                                                "point": {
                                                    "x": 4,
                                                    "y": 8
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564002",
                                                "point": {
                                                    "x": 6,
                                                    "y": 8
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564003",
                                                "point": {
                                                    "x": 4,
                                                    "y": 2
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564004",
                                                "point": {
                                                    "x": 6,
                                                    "y": 2
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564005",
                                                "point": {
                                                    "x": 2,
                                                    "y": 4
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564006",
                                                "point": {
                                                    "x": 8,
                                                    "y": 4
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564007",
                                                "point": {
                                                    "x": 2,
                                                    "y": 6
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564008",
                                                "point": {
                                                    "x": 8,
                                                    "y": 6
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4202,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 302,
                                                "curAnger": 0,
                                                "AttackSpeed": 9,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564009",
                                                "point": {
                                                    "x": 5,
                                                    "y": 3
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4205,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 404,
                                                "curAnger": 0,
                                                "AttackSpeed": 7,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564010",
                                                "point": {
                                                    "x": 5,
                                                    "y": 7
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4205,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 404,
                                                "curAnger": 0,
                                                "AttackSpeed": 7,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564011",
                                                "point": {
                                                    "x": 3,
                                                    "y": 5
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4205,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 404,
                                                "curAnger": 0,
                                                "AttackSpeed": 7,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564012",
                                                "point": {
                                                    "x": 7,
                                                    "y": 5
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4205,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 404,
                                                "curAnger": 0,
                                                "AttackSpeed": 7,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564013",
                                                "point": {
                                                    "x": 5,
                                                    "y": 4
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4204,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 166,
                                                "curAnger": 0,
                                                "AttackSpeed": 6,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564014",
                                                "point": {
                                                    "x": 5,
                                                    "y": 6
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4204,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 166,
                                                "curAnger": 0,
                                                "AttackSpeed": 6,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564015",
                                                "point": {
                                                    "x": 4,
                                                    "y": 5
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4204,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 166,
                                                "curAnger": 0,
                                                "AttackSpeed": 6,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            },
                                            {
                                                "uid": "1756377561564016",
                                                "point": {
                                                    "x": 6,
                                                    "y": 5
                                                },
                                                "equip": {
                                                    "id": 0,
                                                    "attrs": []
                                                },
                                                "portrayal": null,
                                                "index": 154145,
                                                "id": 4204,
                                                "lv": 5,
                                                "skinId": 0,
                                                "curHp": 166,
                                                "curAnger": 0,
                                                "AttackSpeed": 6,
                                                "rodeleroCadetLv": 0,
                                                "petId": 0
                                            }
                                        ],
                                        "index": 154145,
                                        "state": 0,
                                        "enterDir": -1
                                    }
                                ],
                                "army": null,
                                "fighters": [
                                    {
                                        "uid": "1756291482161001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 1,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564006",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 2,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564008",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 3,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564002",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 4,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564004",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 5,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 6,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564003",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 7,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564005",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 8,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564007",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 9,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564012",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 10,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564009",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 11,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564010",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 12,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564011",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 13,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564016",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 14,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564013",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 15,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564014",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 16,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756377561564015",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 1,
                                        "attackIndex": 17,
                                        "waitRound": 0,
                                        "roundCount": 0,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    }
                                ],
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 336045,
                                "type": 0,
                                "currentFrameIndex": 0,
                                "camp": 1,
                                "fps": 20,
                                "cityId": 0
                            },
                            {
                                "hp": null,
                                "builds": null,
                                "buildInfo": null,
                                "armys": null,
                                "army": {
                                    "uid": "1756339489142001",
                                    "name": "Team3",
                                    "owner": "18190079",
                                    "pawns": [
                                        {
                                            "uid": "1756339911560001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 3,
                                            "skinId": 0,
                                            "curHp": 237,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756340333960001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 3,
                                            "skinId": 0,
                                            "curHp": 237,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756340756361001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 3,
                                            "skinId": 0,
                                            "curHp": 237,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756341178761001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756341601160001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756342023560001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756342445961001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756342868361001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756375500861001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 3,
                                            "skinId": 0,
                                            "curHp": 237,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        }
                                    ],
                                    "index": 154145,
                                    "state": 2,
                                    "enterDir": 1
                                },
                                "fighters": [
                                    {
                                        "uid": "1756339911560001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 18,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756340333960001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 19,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756340756361001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 20,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756341178761001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 21,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756341601160001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 22,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756342023560001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 23,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756342445961001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 24,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756342868361001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 25,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756375500861001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 26,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    }
                                ],
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 0,
                                "type": 1,
                                "currentFrameIndex": 1,
                                "camp": 0,
                                "fps": 0,
                                "cityId": 0
                            },
                            {
                                "hp": null,
                                "builds": null,
                                "buildInfo": null,
                                "armys": null,
                                "army": {
                                    "uid": "1756282194495001",
                                    "name": "编队4",
                                    "owner": "18190079",
                                    "pawns": [
                                        {
                                            "uid": "1756282479511001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756282764511001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756283049511001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756283334510001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756283619510001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756283904510001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756284189511001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756284474510001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756284759511001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 1,
                                            "skinId": 3302102,
                                            "curHp": 117,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        }
                                    ],
                                    "index": 154145,
                                    "state": 2,
                                    "enterDir": 1
                                },
                                "fighters": [
                                    {
                                        "uid": "1756282479511001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 44,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756282764511001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 45,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756283049511001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 46,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756283334510001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 47,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756283619510001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 48,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756283904510001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 49,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756284189511001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 50,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756284474510001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 51,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756284759511001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 52,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    }
                                ],
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 0,
                                "type": 1,
                                "currentFrameIndex": 2595,
                                "camp": 0,
                                "fps": 0,
                                "cityId": 0
                            },
                            {
                                "hp": null,
                                "builds": null,
                                "buildInfo": null,
                                "armys": null,
                                "army": {
                                    "uid": "1756303372437001",
                                    "name": "编队6",
                                    "owner": "18190079",
                                    "pawns": [
                                        {
                                            "uid": "1756303636461001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756303900460001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756304164460001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756304428461001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756304692460001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756304956460001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756305228560001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756305756560001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        },
                                        {
                                            "uid": "1756305492560001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3302,
                                            "lv": 3,
                                            "skinId": 3302102,
                                            "curHp": 163,
                                            "curAnger": 0,
                                            "AttackSpeed": 5,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        }
                                    ],
                                    "index": 154145,
                                    "state": 2,
                                    "enterDir": 1
                                },
                                "fighters": [
                                    {
                                        "uid": "1756303636461001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 66,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756303900460001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 67,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756304164460001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 68,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756304428461001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 69,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756304692460001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 70,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756304956460001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 71,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756305228560001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 72,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756305756560001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 73,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    },
                                    {
                                        "uid": "1756305492560001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 74,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    }
                                ],
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 0,
                                "type": 1,
                                "currentFrameIndex": 2596,
                                "camp": 0,
                                "fps": 0,
                                "cityId": 0
                            },
                            {
                                "hp": null,
                                "builds": null,
                                "buildInfo": null,
                                "armys": null,
                                "army": {
                                    "uid": "1756378970479001",
                                    "name": "编队5",
                                    "owner": "18190079",
                                    "pawns": [
                                        {
                                            "uid": "1756375923260001",
                                            "point": {
                                                "x": 10,
                                                "y": 5
                                            },
                                            "equip": {
                                                "id": 6001,
                                                "attrs": [
                                                    [
                                                        0,
                                                        2,
                                                        12
                                                    ]
                                                ]
                                            },
                                            "portrayal": null,
                                            "index": 154145,
                                            "id": 3205,
                                            "lv": 1,
                                            "skinId": 0,
                                            "curHp": 159,
                                            "curAnger": 5,
                                            "AttackSpeed": 7,
                                            "rodeleroCadetLv": 0,
                                            "petId": 0
                                        }
                                    ],
                                    "index": 154145,
                                    "state": 2,
                                    "enterDir": 1
                                },
                                "fighters": [
                                    {
                                        "uid": "1756375923260001",
                                        "attackTarget": "",
                                        "owner": "",
                                        "point": null,
                                        "hp": null,
                                        "buffs": null,
                                        "id": 0,
                                        "lv": 0,
                                        "camp": 2,
                                        "attackIndex": 88,
                                        "waitRound": 0,
                                        "roundCount": 1,
                                        "attackCount": 0,
                                        "enterDir": 0,
                                        "towerId": 0,
                                        "towerLv": 0,
                                        "isFalg": false,
                                        "isNoncombat": false,
                                        "isPet": false
                                    }
                                ],
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 0,
                                "type": 1,
                                "currentFrameIndex": 6102,
                                "camp": 0,
                                "fps": 0,
                                "cityId": 0
                            },
                            {
                                "hp": null,
                                "builds": null,
                                "buildInfo": null,
                                "armys": null,
                                "army": null,
                                "fighters": null,
                                "pawn": null,
                                "uid": "",
                                "owner": "",
                                "armyUid": "",
                                "randSeed": 0,
                                "type": 200,
                                "currentFrameIndex": 13824,
                                "camp": 0,
                                "fps": 0,
                                "cityId": 0
                            }
                        ],
                        "begin_time": {
                            "$numberLong": "1756378815571"
                        },
                        "end_time": {
                            "$numberLong": "1756379506763"
                        },
                        "sid": 100289,
                        "version": 14,
                        "index": 154145
                    };
                    return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordData(json)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                case 2:
                    _a.sent();
                    eventCenter.emit(mc.Event.LOAD_END_WIND);
                    ViewHelper_1.viewHelper.gotoWind('playback');
                    return [2 /*return*/];
            }
        });
    });
};
// 回放战斗 服务器也回放
window['playbackBattleWithServer'] = function (sid, uid, speed) {
    var _a, _b, _c;
    return __awaiter(this, void 0, void 0, function () {
        var url, res, record, info, _d, err, data;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    if (!cc.sys.isBrowser) {
                        return [2 /*return*/];
                    }
                    url = 'http://127.0.0.1:8181/getBattleRecord';
                    eventCenter.emit(mc.Event.LOAD_BEGIN_WIND);
                    return [4 /*yield*/, GameHelper_1.gameHpr.net.post({
                            url: url,
                            data: {
                                sid: sid, uid: uid
                            }
                        })];
                case 1:
                    res = _e.sent();
                    record = proto.BattleRecordInfo.fromObject((_a = res.data) === null || _a === void 0 ? void 0 : _a.data);
                    if (!((res === null || res === void 0 ? void 0 : res.status) === 1 && ((_b = res.data) === null || _b === void 0 ? void 0 : _b.data))) return [3 /*break*/, 3];
                    info = proto.BattleRecordInfo.fromObject((_c = res.data) === null || _c === void 0 ? void 0 : _c.data);
                    info.serverPlayback = true;
                    info.frames[0].checkFrameCount = 100;
                    return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordData(info)];
                case 2:
                    _e.sent();
                    return [3 /*break*/, 4];
                case 3: return [2 /*return*/];
                case 4: return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                case 5:
                    _e.sent();
                    eventCenter.emit(mc.Event.LOAD_END_WIND);
                    ViewHelper_1.viewHelper.gotoWind('playback');
                    return [4 /*yield*/, GameHelper_1.gameHpr.playback.reqPlayBack(record, speed)];
                case 6:
                    _d = _e.sent(), err = _d.err, data = _d.data;
                    if (err) {
                        return [2 /*return*/];
                    }
                    return [2 /*return*/];
            }
        });
    });
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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