
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/MysteryboxRulePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4af6ewHcR9IoIwTtwR9wB/6', 'MysteryboxRulePnlCtrl');
// app/script/view/common/MysteryboxRulePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var FrameAnimationCmpt_1 = require("../cmpt/FrameAnimationCmpt");
var ccclass = cc._decorator.ccclass;
var MysteryboxRulePnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxRulePnlCtrl, _super);
    function MysteryboxRulePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.animNode_ = null; // path://root/content/hide/anim_n
        //@end
        _this.SKIN_POS = {
            103: cc.v2(48, 34),
            104: cc.v2(-24, 22),
            105: cc.v2(0, 34),
        };
        _this.cb = null;
        return _this;
    }
    MysteryboxRulePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxRulePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MysteryboxRulePnlCtrl.prototype.onEnter = function (id, cb) {
        this.cb = cb;
        GameHelper_1.gameHpr.setNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE, true);
        this.loadHideSkin(id);
    };
    MysteryboxRulePnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb();
        this.cb = null;
    };
    MysteryboxRulePnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 动态加载隐藏款皮肤
    MysteryboxRulePnlCtrl.prototype.loadHideSkin = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.animNode_.setPosition(this.SKIN_POS[id] || cc.v2());
                        return [4 /*yield*/, this.animNode_.Component(FrameAnimationCmpt_1.default).init('mysterybox_hide_' + id, this.key)];
                    case 1:
                        _a.sent();
                        this.animNode_.Component(FrameAnimationCmpt_1.default).play('standby');
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxRulePnlCtrl = __decorate([
        ccclass
    ], MysteryboxRulePnlCtrl);
    return MysteryboxRulePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxRulePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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