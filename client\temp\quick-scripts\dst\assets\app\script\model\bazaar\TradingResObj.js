
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/bazaar/TradingResObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0566eQ7eFJC7Lf6uFJ7wG+q', 'TradingResObj');
// app/script/model/bazaar/TradingResObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var CTypeObj_1 = require("../common/CTypeObj");
// 一个交易资源
var TradingResObj = /** @class */ (function () {
    function TradingResObj() {
        this.uid = '';
        this.owner = ''; //出售者
        this.sell = null; //出售的资源
        this.buy = null; //需要用来购买的资源
        this.merchantCount = 0; //商人数量
        this.surplusTime = 0; //剩余时间
        this.noticeWaitTime = 0; //公示时间
        this.cancelTime = 0; //可下架的剩余时间
        this.transitTime = 0; //运送时间
        this.getTime = 0; //获取时间
    }
    TradingResObj.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.owner = data.owner;
        this.sell = new CTypeObj_1.default().init(data.sellType, 0, data.sellCount);
        this.buy = new CTypeObj_1.default().init(data.buyType, 0, data.buyCount);
        this.merchantCount = data.merchantCount || 1;
        this.surplusTime = data.surplusTime;
        this.noticeWaitTime = data.noticeWaitTime || 0;
        this.cancelTime = data.cancelTime || 0;
        this.transitTime = GameHelper_1.gameHpr.getSelfToOtherPlayerDis(data.owner) * (ut.Time.Hour / Constant_1.TRANSIT_TIME);
        this.getTime = Date.now();
        return this;
    };
    // 获取实际的剩余时间
    TradingResObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 获取取消出售等待时间
    TradingResObj.prototype.getNoticeWaitTime = function () {
        return this.noticeWaitTime > 0 ? Math.max(0, this.noticeWaitTime - (Date.now() - this.getTime)) : 0;
    };
    // 获取可下架的剩余时间
    TradingResObj.prototype.getCancelTime = function () {
        return this.cancelTime > 0 ? Math.max(0, this.cancelTime - (Date.now() - this.getTime)) : 0;
    };
    return TradingResObj;
}());
exports.default = TradingResObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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