
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/PraisePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '386d5SJhxxHr5H8WW4/ImtU', 'PraisePnlCtrl');
// app/script/view/main/PraisePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PraisePnlCtrl = /** @class */ (function (_super) {
    __extends(PraisePnlCtrl, _super);
    function PraisePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.banCloseNode_ = null; // path://ban_close_n
        //@end
        _this.openTime = 0;
        return _this;
    }
    PraisePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PraisePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                cc.game.on(cc.game.EVENT_SHOW, this.onEventShow, this);
                return [2 /*return*/];
            });
        });
    };
    PraisePnlCtrl.prototype.onEnter = function (data) {
        this.openTime = 0;
        this.delayClose();
    };
    PraisePnlCtrl.prototype.onRemove = function () {
        this.openTime = 0;
    };
    PraisePnlCtrl.prototype.onClean = function () {
        cc.game.off(cc.game.EVENT_SHOW, this.onEventShow, this);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons/cancel_be
    PraisePnlCtrl.prototype.onClickCancel = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('login/Feedback', function (ok) {
            if (ok) {
                GameHelper_1.gameHpr.user.setCheckPraiseCount(4, 100015);
                _this.close();
            }
        }).then(function () { return _this.isValid && _this.hide(); });
    };
    // path://root/buttons/ok_be
    PraisePnlCtrl.prototype.onClickOk = function (event, data) {
        this.hide();
        this.openTime = Date.now();
        cc.sys.openURL(GameHelper_1.gameHpr.getGameDownloadUrl());
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    PraisePnlCtrl.prototype.onEventShow = function () {
        if (this.openTime > 0 && Date.now() - this.openTime >= 3) {
            GameHelper_1.gameHpr.user.setCheckPraiseCount(4, 100014);
            this.close();
        }
    };
    // 被动打开时延时关闭
    PraisePnlCtrl.prototype.delayClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.banCloseNode_.active = true;
                        return [4 /*yield*/, ut.wait(Constant_1.DELAY_CLOSE_PNL_TIME, this)];
                    case 1:
                        _a.sent();
                        if (this.isValid) {
                            this.banCloseNode_.active = false;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PraisePnlCtrl = __decorate([
        ccclass
    ], PraisePnlCtrl);
    return PraisePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PraisePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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