
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/PersonalGameHistoryPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b004axjxdNAMZu43WDUgTo7', 'PersonalGameHistoryPnlCtrl');
// app/script/view/menu/PersonalGameHistoryPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PersonalGameHistoryPnlCtrl = /** @class */ (function (_super) {
    __extends(PersonalGameHistoryPnlCtrl, _super);
    function PersonalGameHistoryPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.historySv_ = null; // path://root/content/history_sv
        _this.nextNode_ = null; // path://root/content/bottom/next_n
        _this.serverTypeSelectNode_ = null; // path://root/content/bottom/server_type_select_be_n
        //@end
        _this.curSelectServerType = 'all';
        _this.curPage = 0;
        _this.maxPage = 0;
        return _this;
    }
    PersonalGameHistoryPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PersonalGameHistoryPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    PersonalGameHistoryPnlCtrl.prototype.onEnter = function () {
        this.curSelectServerType = 'all';
        this.nextNode_.Child('text/val', cc.Label).string = '';
        this.showGameRecordList();
        this.selectServerTypeItem(this.serverTypeSelectNode_, this.curSelectServerType, true);
        var selectNode = this.serverTypeSelectNode_.Child('mask/root/server_type_items_nbe');
        selectNode.Child('all').active = true;
    };
    PersonalGameHistoryPnlCtrl.prototype.onRemove = function () {
    };
    PersonalGameHistoryPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content/history_sv/view/content/item/detail_be
    PersonalGameHistoryPnlCtrl.prototype.onClickDetail = function (event, _data) {
        var data = event.target.parent.Data;
        data && ViewHelper_1.viewHelper.showPnl('menu/PersonalGameDetail', data);
    };
    // path://root/content/bottom/next_n/next_page_be@0
    PersonalGameHistoryPnlCtrl.prototype.onClickNextPage = function (event, data) {
        var add = data === '0' ? -1 : 1;
        var curPage = this.curPage || 0, maxPage = this.maxPage;
        var index = ut.loopValue(curPage + add, maxPage);
        if (index !== curPage) {
            this.curPage = index;
            this.showGameRecordList();
        }
    };
    // path://root/content/bottom/server_type_select_be_n
    PersonalGameHistoryPnlCtrl.prototype.onClickServerTypeSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/content/bottom/server_type_select_be_n/select_mask_be
    PersonalGameHistoryPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/content/bottom/server_type_select_be_n/mask/root/server_type_items_nbe
    PersonalGameHistoryPnlCtrl.prototype.onClickServerTypeItems = function (event, data) {
        var node = this.serverTypeSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = event.target.name;
        if (type !== this.curSelectServerType) {
            this.selectServerTypeItem(node, type);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 个人战绩
    PersonalGameHistoryPnlCtrl.prototype.showGameRecordList = function () {
        var _this = this;
        var empty = this.historySv_.node.Child('empty');
        empty.active = false;
        this.updatePageButton();
        var serverTypeStr = this.curSelectServerType;
        var serverType = serverTypeStr === 'all' ? -1 : (Number(serverTypeStr) || 0);
        var page = this.curPage + 1;
        GameHelper_1.gameHpr.net.request('lobby/HD_GetGameRecordList', { page: page, serverType: serverType }, true).then(function (_a) {
            var _b;
            var err = _a.err, data = _a.data;
            var list = (data === null || data === void 0 ? void 0 : data.datas) || [], len = list.length;
            empty.active = !len;
            _this.historySv_.stopAutoScroll();
            _this.historySv_.content.y = 0;
            _this.historySv_.List(len, function (it, i) {
                var data = it.Data = list[i];
                var _a = GameHelper_1.gameHpr.getServerNameById(data.sid), key = _a.key, id = _a.id;
                it.Child('name/val').setLocaleKey(key, id);
                it.Child('alliance/val', cc.Label).string = data.alliName || '-';
                ResHelper_1.resHelper.loadAlliIcon(data.alliHeadicon || 0, it.Child('alliance/icon'), _this.key);
                it.Child('time/val', cc.Label).string = ut.dateFormat('yyyy/MM/dd', data.startTime) + ' - ' + ut.dateFormat('yyyy/MM/dd', data.endTime);
                var isGiveupGame = data.rank === -2;
                it.Child('detail_be').active = !isGiveupGame;
                var layout = it.Child('layout').Swih(isGiveupGame ? 'giveup' : 'over')[0];
                if (!isGiveupGame) {
                    layout.Child('score/val', cc.Label).string = data.score;
                    layout.Child('ranking/val', cc.Label).string = data.rank >= 0 ? (data.rank + 1) : '-';
                }
                it.Child('heros').Items(data.useHero, function (it, data) {
                    ResHelper_1.resHelper.loadPawnHeadMiniIcon(data, it.Child('val'), _this.key);
                });
            });
            if (data && (data === null || data === void 0 ? void 0 : data.totalPage) !== -1 && page === 1) {
                _this.maxPage = (_b = data.totalPage) !== null && _b !== void 0 ? _b : (_this.maxPage || 1);
            }
            _this.updatePageButton();
        });
    };
    PersonalGameHistoryPnlCtrl.prototype.updatePageButton = function () {
        var curPage = this.curPage || 0, maxPage = this.maxPage || 0;
        curPage = Math.min(curPage + 1, maxPage);
        this.nextNode_.Child('text/val', cc.Label).string = curPage + '/' + maxPage;
        var pre = this.nextNode_.Child('next_page_be@0', cc.Button), next = this.nextNode_.Child('next_page_be@1', cc.Button);
        pre.Component(cc.MultiFrame).setFrame(curPage > 1);
        pre.interactable = curPage > 1;
        next.Component(cc.MultiFrame).setFrame(curPage < maxPage);
        next.interactable = curPage < maxPage;
    };
    // 选择排序
    PersonalGameHistoryPnlCtrl.prototype.selectServerTypeItem = function (node, type, init) {
        var oldType = this.curSelectServerType;
        node.Data = this.curSelectServerType = type;
        node.Child('val', cc.Label).setLocaleKey(type === 'all' ? 'ui.bazaar_filter_all' : 'ui.title_server_name_' + type);
        node.Child('mask/root/server_type_items_nbe').children.forEach(function (m) {
            var select = m.name === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
        if (init || oldType === type) {
            return;
        }
        this.curPage = 0;
        this.showGameRecordList();
    };
    PersonalGameHistoryPnlCtrl = __decorate([
        ccclass
    ], PersonalGameHistoryPnlCtrl);
    return PersonalGameHistoryPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PersonalGameHistoryPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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