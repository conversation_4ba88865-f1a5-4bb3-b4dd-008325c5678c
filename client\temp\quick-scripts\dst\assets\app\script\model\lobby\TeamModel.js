
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/lobby/TeamModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ad5e6OmQf5PDZv3dn/qidvA', 'TeamModel');
// app/script/model/lobby/TeamModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var SendInviteInfo_1 = require("./SendInviteInfo");
var TeammateInfo_1 = require("./TeammateInfo");
/**
 * 组队模块
 */
var TeamModel = /** @class */ (function (_super) {
    __extends(TeamModel, _super);
    function TeamModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.teamUid = '';
        _this.teammates = [];
        _this.sendInvites = []; //发出的邀请列表
        _this.receiveInvites = []; //收到的邀请 
        _this.cancelApplySurplusTime = -1; //取消报名剩余时间
        _this.playSid = 0; //游玩的sid
        _this.roomType = 0; //模块类型
        _this.teamUserNumMax = 0; //队伍最大数量
        _this.getTeamInfoTime = 0;
        return _this;
    }
    TeamModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    TeamModel.prototype.init = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_GetTeamInfo')];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        cc.log('init team', data);
                        this.teamUserNumMax = (_a = data === null || data === void 0 ? void 0 : data.userNumMax) !== null && _a !== void 0 ? _a : 40;
                        // 刷新队伍信息
                        this.updateTeamInfo(data === null || data === void 0 ? void 0 : data.teamInfo);
                        // 收到邀请信息
                        this.receiveInvites = (data === null || data === void 0 ? void 0 : data.teamInviteList) || [];
                        return [2 /*return*/];
                }
            });
        });
    };
    TeamModel.prototype.active = function () {
        this.net.on('lobby/OnTeamInvite', this.OnTeamInvite, this); //队伍邀请通知
        this.net.on('lobby/OnTeamDelTeammate', this.OnTeamDelTeammate, this); //被踢出队伍
        this.net.on('lobby/OnTeamUpdate', this.OnTeamUpdate, this); //队伍信息刷新
        this.net.on('lobby/OnTeamChangeMode', this.OnTeamChangeMode, this); //队伍切换模式
        this.net.on('lobby/OnTeamCancelApply', this.OnTeamCancelApply, this); //队伍取消报名
        this.net.on('lobby/OnTeammateInfoChange', this.OnTeammateInfoChange, this); // 队员信息变更
    };
    TeamModel.prototype.clean = function () {
        this.net.off('lobby/OnTeamInvite', this.OnTeamInvite, this); //队伍邀请通知
        this.net.off('lobby/OnTeamDelTeammate', this.OnTeamDelTeammate, this); //被踢出队伍
        this.net.off('lobby/OnTeamUpdate', this.OnTeamUpdate, this); //队伍信息刷新
        this.net.off('lobby/OnTeamChangeMode', this.OnTeamChangeMode, this); //队伍切换模式
        this.net.off('lobby/OnTeamCancelApply', this.OnTeamCancelApply, this); //队伍取消报名
        this.net.off('lobby/OnTeammateInfoChange', this.OnTeammateInfoChange, this); // 队员信息变更
    };
    TeamModel.prototype.getTeamUserMaxNum = function () { return this.teamUserNumMax; };
    TeamModel.prototype.getUid = function () { return this.teamUid; };
    TeamModel.prototype.getTeammates = function () { return this.teammates; };
    TeamModel.prototype.getSendApplys = function () { return this.sendInvites; };
    TeamModel.prototype.getReceiveInvites = function () { return this.receiveInvites; };
    TeamModel.prototype.getPlaySid = function () { return this.playSid || GameHelper_1.gameHpr.user.getPlaySid(); };
    TeamModel.prototype.setPlaySid = function (val) { this.playSid = val; };
    TeamModel.prototype.getRoomType = function () { return this.roomType; };
    TeamModel.prototype.setRoomType = function (val) { this.roomType = val; };
    TeamModel.prototype.hasTeam = function () {
        if (!this.teamUid) {
            return false;
        }
        return this.teammates.length > 1 || this.sendInvites.length > 0;
    };
    // 是否队长
    TeamModel.prototype.isCaptain = function () {
        return !this.teamUid || this.teamUid === GameHelper_1.gameHpr.getUid();
    };
    // 自己是否有邀请权限
    TeamModel.prototype.isMyInviteAuth = function () {
        var _a;
        if (this.isCaptain()) {
            return true;
        }
        var uid = GameHelper_1.gameHpr.getUid();
        return !!((_a = this.teammates.find(function (m) { return m.uid === uid; })) === null || _a === void 0 ? void 0 : _a.isInviteAuth());
    };
    // 是否已经在游戏中
    TeamModel.prototype.isInGame = function () {
        return !!this.playSid || !!GameHelper_1.gameHpr.user.getPlaySid();
    };
    // 是否已经报名
    TeamModel.prototype.isInApply = function () {
        return this.getCancelApplySurplusTime() !== -1;
    };
    // 取消剩余时间
    TeamModel.prototype.getCancelApplySurplusTime = function () {
        if (this.cancelApplySurplusTime === -1) {
            return -1;
        }
        return Math.max(0, this.cancelApplySurplusTime - (Date.now() - this.getTeamInfoTime));
    };
    TeamModel.prototype.setCancelApplySurplusTime = function (val) {
        this.cancelApplySurplusTime = val;
    };
    // 刷新队伍信息
    TeamModel.prototype.updateTeamInfo = function (data) {
        var _a, _b, _c, _d, _e, _f;
        cc.log('updateTeamInfo', data);
        this.teamUid = (_a = data === null || data === void 0 ? void 0 : data.uid) !== null && _a !== void 0 ? _a : '';
        this.teammates = ((_b = data === null || data === void 0 ? void 0 : data.userList) === null || _b === void 0 ? void 0 : _b.map(function (m) { return new TeammateInfo_1.default().init(m); })) || [];
        this.sendInvites = ((_c = data === null || data === void 0 ? void 0 : data.inviteList) === null || _c === void 0 ? void 0 : _c.map(function (m) { return new SendInviteInfo_1.default().init(m); })) || [];
        this.setCancelApplySurplusTime((_d = data === null || data === void 0 ? void 0 : data.cancelApplySurplusTime) !== null && _d !== void 0 ? _d : -1);
        this.playSid = (_e = data === null || data === void 0 ? void 0 : data.playSid) !== null && _e !== void 0 ? _e : 0;
        this.getTeamInfoTime = Date.now();
        var lobby = GameHelper_1.gameHpr.lobby;
        var roomType = (_f = data === null || data === void 0 ? void 0 : data.roomType) !== null && _f !== void 0 ? _f : -1;
        if (roomType !== -1) {
            lobby.setCurRoomType(roomType);
        }
        this.roomType = lobby.getCurRoomType();
    };
    // 获取实际的队员信息
    TeamModel.prototype.getActTeammates = function () {
        var list = [];
        if (this.teammates.length > 0) {
            var uid_1 = GameHelper_1.gameHpr.getUid();
            this.teammates.forEach(function (m) { return m.uid === uid_1 ? list.unshift(m) : list.push(m); });
        }
        else {
            list.push(new TeammateInfo_1.default().init({
                uid: GameHelper_1.gameHpr.getUid(),
                headIcon: GameHelper_1.gameHpr.user.getHeadIcon(),
                nickname: GameHelper_1.gameHpr.user.getNickname(),
                job: 1,
            }));
        }
        list.pushArr(this.sendInvites);
        return list;
    };
    // 发起邀请
    TeamModel.prototype.inviteTeammate = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (GameHelper_1.gameHpr.getUid() === uid) {
                            return [2 /*return*/, ECode_1.ecode.USER_ALREADY_IN_TEAM];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_InviteTeammate', { uid: uid, roomType: GameHelper_1.gameHpr.lobby.getCurRoomType() }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 邀请回复
    TeamModel.prototype.inviteTeammateResponse = function (uid, isAgree) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_InviteTeammateResponse', { uid: uid, isAgree: isAgree }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.TEAM_NOT_EXIST) {
                        }
                        else if (err === ECode_1.ecode.USER_ALREADY_IN_GAME || err === ECode_1.ecode.USER_ALREADY_IN_TEAM || err === ECode_1.ecode.NOVICE_PLAYER) {
                            this.receiveInvites = [];
                            return [2 /*return*/, ''];
                        }
                        else if (err) {
                            return [2 /*return*/, err];
                        }
                        else if (isAgree) {
                            this.updateTeamInfo(data === null || data === void 0 ? void 0 : data.teamInfo);
                            this.receiveInvites = [];
                            this.emit(EventType_1.default.UPDATE_TEAM_MODE, true);
                            return [2 /*return*/, ''];
                        }
                        this.receiveInvites.remove('uid', uid);
                        this.emit(EventType_1.default.UPDATE_TEAM_APPLYS);
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 删除队友
    TeamModel.prototype.removeTeammate = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_DelTeammate', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 退出队伍
    TeamModel.prototype.exitTeam = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.teamUid) {
                            return [2 /*return*/, ECode_1.ecode.TEAM_NOT_EXIST];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_ExitTeam', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.cleanTeam();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    TeamModel.prototype.cleanTeam = function () {
        this.receiveInvites = [];
        this.OnTeamUpdate({});
    };
    // 清理收到的邀请
    TeamModel.prototype.cleanReceiveInvite = function () {
        this.receiveInvites = [];
        this.emit(EventType_1.default.UPDATE_TEAM_APPLYS, true);
    };
    // 解散队伍
    TeamModel.prototype.dissolve = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_DidsbandTeam', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.cleanTeam();
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 同步队员的职位
    TeamModel.prototype.syncTeammateJob = function (uid, job) {
        this.net.send('lobby/HD_TeamChangeJob', { uid: uid, job: job });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 收到队伍邀请通知
    TeamModel.prototype.OnTeamInvite = function (data) {
        // cc.log('OnTeamInvite', data)
        if (!(data === null || data === void 0 ? void 0 : data.inviteInfo)) {
            return;
        }
        else if (!this.receiveInvites.some(function (m) { return m.uid === data.inviteInfo.uid; })) {
            this.receiveInvites.push(data.inviteInfo);
            this.emit(EventType_1.default.UPDATE_TEAM_APPLYS);
        }
    };
    // 被踢出队伍
    TeamModel.prototype.OnTeamDelTeammate = function (data) {
        if (!this.teamUid) {
            return;
        }
        ViewHelper_1.viewHelper.hidePnl('lobby/TeamList');
        ViewHelper_1.viewHelper.hidePnl('lobby/LobbyChat');
        ViewHelper_1.viewHelper.hidePnl('common/Chat');
        ViewHelper_1.viewHelper.showMessageBox((data === null || data === void 0 ? void 0 : data.type) === 0 ? 'ui.kick_team_desc' : 'ui.team_dissolve_tip');
        this.cleanTeam();
    };
    // 队伍信息更新
    TeamModel.prototype.OnTeamUpdate = function (data) {
        var type = GameHelper_1.gameHpr.lobby.getCurRoomType();
        this.updateTeamInfo(data === null || data === void 0 ? void 0 : data.teamInfo);
        if (type === GameHelper_1.gameHpr.lobby.getCurRoomType()) {
            this.emit(EventType_1.default.UPDATE_TEAM_LIST);
        }
        else {
            this.emit(EventType_1.default.UPDATE_TEAM_MODE);
        }
    };
    // 队伍切换模式
    TeamModel.prototype.OnTeamChangeMode = function (data) {
        if (!data || data.roomType === this.roomType) {
            return;
        }
        GameHelper_1.gameHpr.lobby.setCurRoomType(data.roomType);
        this.roomType = GameHelper_1.gameHpr.lobby.getCurRoomType();
        this.emit(EventType_1.default.UPDATE_TEAM_MODE);
    };
    // 队伍取消报名
    TeamModel.prototype.OnTeamCancelApply = function (data) {
        if (data.uid !== GameHelper_1.gameHpr.getUid()) {
            ViewHelper_1.viewHelper.showMessageBox('ui.team_cancel_apply_msg', { params: [data.nickname, 'ui.title_server_name_' + this.roomType] });
        }
        this.cancelApplySurplusTime = -1;
        GameHelper_1.gameHpr.lobby.setCancelApplyTime();
        this.emit(EventType_1.default.UPDATE_TEAM_LIST);
    };
    // 队员信息变更
    TeamModel.prototype.OnTeammateInfoChange = function (data) {
        var info = data.info, teammate = this.teammates.find(function (m) { return m.uid === info.uid; });
        if (teammate) {
            teammate.job = info.job;
            teammate.nickname = info.nickname;
            teammate.headIcon = info.headIcon;
            teammate.farmType = info.farmType;
            teammate.expectPosition = info.expectPosition;
            this.emit(EventType_1.default.UPDATE_TEAM_LIST);
        }
    };
    TeamModel = __decorate([
        mc.addmodel('team')
    ], TeamModel);
    return TeamModel;
}(mc.BaseModel));
exports.default = TeamModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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