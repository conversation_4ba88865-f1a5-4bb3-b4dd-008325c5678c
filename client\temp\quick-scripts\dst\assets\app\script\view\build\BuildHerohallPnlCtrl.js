
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildHerohallPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc906sm9kxNw7RfQAZZq58H', 'BuildHerohallPnlCtrl');
// app/script/view/build/BuildHerohallPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ccclass = cc._decorator.ccclass;
var BuildHerohallPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildHerohallPnlCtrl, _super);
    function BuildHerohallPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        //@end
        _this.PKEY_TAB = 'HERO_HALL_TAB';
        _this.player = null;
        _this.data = null;
        _this.heroIconMap = {};
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildHerohallPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_HERO_SLOT_INFO] = this.onUpdateHeroSlotInfo, _b.enter = true, _b),
        ];
    };
    BuildHerohallPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildHerohallPnlCtrl.prototype.onEnter = function (data, tab) {
        this.data = data;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (GameHelper_1.gameHpr.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildHerohallPnlCtrl.prototype.onRemove = function () {
    };
    BuildHerohallPnlCtrl.prototype.onClean = function () {
        this.heroIconMap = {};
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildHerohallPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        GameHelper_1.gameHpr.user.setTempPreferenceData(this.PKEY_TAB, type);
        if (type === '0') {
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.HERO_SLOT_LV_COND, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.getEffectsForView(), this.key);
        }
        else if (type === '1') {
            this.updateSlotInfo(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildHerohallPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/slots_nbe
    BuildHerohallPnlCtrl.prototype.onClickSlots = function (event, _) {
        var _this = this;
        var data = event.target.Data;
        if (!data) {
            return;
        }
        else if (data.hero) {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', data.hero, 'hero_hall', GameHelper_1.gameHpr.getUid());
        }
        else {
            var obj_1 = {}, index_1 = Number(event.target.name);
            this.player.getHeroSlots().forEach(function (m) { return m.hero && (obj_1[m.hero.id] = true); });
            var list = GameHelper_1.gameHpr.user.getPortrayals().filter(function (m) { return m.isUnlock() && !obj_1[m.id]; });
            ViewHelper_1.viewHelper.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.HERO, list, function (arr) {
                if (_this.isValid && arr.length > 0) {
                    _this.worshipHero(index_1, arr[0].id);
                }
            });
        }
    };
    // path://root/pages_n/1/strategys/title/strategy_desc_be
    BuildHerohallPnlCtrl.prototype.onClickStrategyDesc = function (event, data) {
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.strategy_desc_1' },
            { key: 'ui.strategy_desc_2' },
        ], 'ui.title_strategy_desc');
    };
    // path://root/pages_n/1/slots_nbe/0/can_avatar_be
    BuildHerohallPnlCtrl.prototype.onClickCanAvatar = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data && data.hero) {
            ViewHelper_1.viewHelper.showPnl('build/AvatarDesc', data.hero);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildHerohallPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.HERO_SLOT_LV_COND, this.key, data.lv);
        }
    };
    BuildHerohallPnlCtrl.prototype.onUpdateHeroSlotInfo = function () {
        this.updateSlotInfo();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildHerohallPnlCtrl.prototype.getEffectsForView = function () {
        var cnt = 0, nextVal = '';
        for (var i = 0, l = Constant_1.HERO_SLOT_LV_COND.length; i < l; i++) {
            var lv = Constant_1.HERO_SLOT_LV_COND[i];
            if (this.data.lv >= lv) {
                cnt += 1;
            }
            else {
                this.data.tempNextLv = lv;
                nextVal = assetsMgr.lang('ui.unit_ge', i + 1);
                break;
            }
        }
        return [{ curr: { key: 'ui.build_eff_desc_hero_hall', params: [assetsMgr.lang('ui.unit_ge', cnt)] }, nextVal: nextVal }];
    };
    BuildHerohallPnlCtrl.prototype.updateSlotInfo = function (node) {
        var _this = this;
        var _a;
        node = node || this.pagesNode_.Child(1);
        var heroSlots = this.player.getHeroSlots();
        var slotNode = node.Child('slots_nbe'), cnt = Constant_1.HERO_SLOT_LV_COND.length;
        for (var i = 0; i < cnt; i++) {
            var slot = slotNode.Child(i), data = heroSlots[i], lv = (_a = data === null || data === void 0 ? void 0 : data.lv) !== null && _a !== void 0 ? _a : Constant_1.HERO_SLOT_LV_COND[i];
            slot.Data = data;
            var stateNode = slot.Child('state');
            var isUnlock = this.data.lv >= lv;
            slot.Component(cc.Button).interactable = isUnlock;
            var root = slot.Child('root'), none = slot.Child('none');
            none.active = !isUnlock;
            var diType = 0;
            if (root.active = isUnlock) {
                var hero = data === null || data === void 0 ? void 0 : data.hero, iconNode = root.Child('mask/icon');
                root.Child('add').active = !hero;
                root.Child('chosen').active = !!(hero === null || hero === void 0 ? void 0 : hero.isChosenOne());
                var time = (data === null || data === void 0 ? void 0 : data.getReviveSurplusTime()) || 0;
                var state = (data === null || data === void 0 ? void 0 : data.getState()) || 0;
                if (root.Child('name').active = !!hero) {
                    root.Child('name/val').setLocaleKey(hero.getChatName());
                    ResHelper_1.resHelper.loadPortrayalImage(hero.id, iconNode, this.key);
                    iconNode.setPosition(hero.showBoxOffset);
                    iconNode.Component(cc.Sprite).setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(!time));
                    // iconNode.opacity = state === 1 ? 128 : 255
                }
                // 状态
                var canAvatar = root.Child('can_avatar_be').active = state === 1;
                if (stateNode.active = !canAvatar) {
                    stateNode.Child('val').Color(ViewHelper_1.viewHelper.getHeroStateBgColor(state)).setLocaleKey('ui.avatar_state_' + state);
                }
                if (root.Child('time').active = !!hero && !!time) {
                    root.Child('time/val', cc.LabelTimer).run(time * 0.001, function () { return _this.updateSlotInfo(node); });
                }
                diType = !hero || !!time ? 0 : hero.getUIDiType();
            }
            else {
                stateNode.active = true;
                stateNode.Child('val').setLocaleKey('ui.need_lv_unlock', lv);
            }
            // 是否阵亡
            slot.Child('di', cc.MultiFrame).setFrame(diType);
        }
        this.updateStrategyForPreloadHeroIcon(node);
    };
    // 预加载
    BuildHerohallPnlCtrl.prototype.updateStrategyForPreloadHeroIcon = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var sfs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.all(this.player.getHeroSlots().filter(function (m) { return m.hero; }).map(function (m) { return assetsMgr.loadTempRes("role/" + m.hero.id + "/role_" + m.hero.id + "_01", cc.SpriteFrame, _this.key); }))];
                    case 1:
                        sfs = _a.sent();
                        this.heroIconMap = {};
                        sfs.forEach(function (m) { return _this.heroIconMap[m.name] = m; });
                        this.updateStrategys(node);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新韬略效果列表
    BuildHerohallPnlCtrl.prototype.updateStrategys = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var heroSlots = this.player.getHeroSlots();
        var strategys = [];
        heroSlots.forEach(function (m) { var _a; return (_a = m.hero) === null || _a === void 0 ? void 0 : _a.strategys.forEach(function (s) {
            var data = s.isFollowPawn() ? strategys.find(function (x) { var _a; return x.strategy.type === s.type && ((_a = x.heros[0]) === null || _a === void 0 ? void 0 : _a.avatarPawnName) === m.hero.avatarPawnName; }) : strategys.find(function (x) { return x.strategy.type === s.type; });
            if (!data) {
                data = strategys.add({ strategy: s, heros: [] });
            }
            data.heros.push({ id: m.hero.id, state: m.getState(), avatarPawnName: m.hero.avatarPawnName });
        }); });
        var sv = node.Child('strategys/list', cc.ScrollView);
        sv.Child('empty').active = !strategys.length;
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.Items(strategys, function (it, data) {
            var strategy = data.strategy, heros = data.heros;
            var herosNode = it.Child('heros'), valRt = it.Child('val', cc.RichText);
            herosNode.Items(heros, function (n, hero) {
                n.Component(cc.Sprite).spriteFrame = _this.heroIconMap["role_" + hero.id + "_01"] || null;
                // n.opacity = hero.state === 1 ? 128 : 255
            });
            herosNode.Component(cc.Layout).updateLayout();
            valRt.maxWidth = it.width - herosNode.width - 28;
            var params = strategy.getDescParamsRange(heros[0].avatarPawnName, Math.min(strategy.magxOverlay, heros.length));
            valRt.setLocaleKey(strategy.desc, params);
            it.height = Math.max(valRt.node.height + 32, 88);
            it.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        });
    };
    // 供奉英雄
    BuildHerohallPnlCtrl.prototype.worshipHero = function (index, id) {
        this.player.worshipHero(index, id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    BuildHerohallPnlCtrl = __decorate([
        ccclass
    ], BuildHerohallPnlCtrl);
    return BuildHerohallPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildHerohallPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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