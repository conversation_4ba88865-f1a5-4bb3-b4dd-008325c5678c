
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LoginButtonPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '422ab+/bH5BupRwKxub4hel', 'LoginButtonPnlCtrl');
// app/script/view/login/LoginButtonPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var ccclass = cc._decorator.ccclass;
var LoginButtonPnlCtrl = /** @class */ (function (_super) {
    __extends(LoginButtonPnlCtrl, _super);
    function LoginButtonPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.buttonsNode_ = null; // path://buttons_n
        _this.areaBoxNode_ = null; // path://area_box_be_n
        //@end
        _this.model = null;
        _this.preButtonHeight = -1;
        return _this;
    }
    LoginButtonPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LoginButtonPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setParam({ isAct: false, isMask: false });
                        this.model = this.getModel('login');
                        return [4 /*yield*/, this.model.checkAuthUserInfo()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    LoginButtonPnlCtrl.prototype.onEnter = function (data) {
        // 先断开连接
        this.model.disconnect();
        // 初始化邀请uid
        GameHelper_1.gameHpr.initInviteMePlayerUid();
        var isGLobal = GameHelper_1.gameHpr.isGLobal(), isMobile = ut.isMobile(), isAndroid = ut.isAndroid(), isIos = ut.isIos();
        this.buttonsNode_.Child('guest_login_be').active = (isAndroid && !GameHelper_1.gameHpr.isRelease) || (!isMobile && !ut.isMiniGame() && cc.sys.isBrowser); //游客登录 只要不是微信平台和安卓平台
        this.checkCreateWechatGameButton(); //微信
        this.buttonsNode_.Child('facebook_login_be').active = isGLobal && isMobile; //facebook 只要是海外就显示
        this.buttonsNode_.Child('apple_login_be').active = isMobile && (isIos || isAndroid); //apple 只有苹果和安卓手机显示
        this.buttonsNode_.Child('google_login_be').active = isGLobal && (isAndroid || isIos); //apple google 只有安卓和苹果手机显示
        this.buttonsNode_.Child('area_be').active = isGLobal;
        this.areaBoxNode_.active = false;
        this.updateShowArea();
    };
    LoginButtonPnlCtrl.prototype.onRemove = function () {
        WxHelper_1.wxHelper.destroyButton(this.buttonsNode_.Child('wx_login_be'));
    };
    LoginButtonPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://buttons_n/guest_login_be
    LoginButtonPnlCtrl.prototype.onClickGuestLogin = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showMessageBox('login.guest_login_tip', {
            okText: 'login.button_goon_login',
            ok: function () {
                _this.model.guestLogin().then(function (data) { return _this.loginRet(data, '1-2-0'); });
            },
            cancel: function () { }
        });
    };
    // path://buttons_n/wx_login_be
    LoginButtonPnlCtrl.prototype.onClickWxLogin = function (event, data) {
        var _this = this;
        this.model.wxLogin().then(function (data) { return _this.loginRet(data, '1-2-1'); });
    };
    // path://buttons_n/facebook_login_be
    LoginButtonPnlCtrl.prototype.onClickFacebookLogin = function (event, data) {
        var _this = this;
        this.model.facebookLogin().then(function (data) { return _this.loginRet(data, '1-2-2'); });
    };
    // path://buttons_n/apple_login_be
    LoginButtonPnlCtrl.prototype.onClickAppleLogin = function (event, data) {
        var _this = this;
        this.model.appleLogin().then(function (data) { return _this.loginRet(data, '1-2-3'); });
    };
    // path://buttons_n/google_login_be
    LoginButtonPnlCtrl.prototype.onClickGoogleLogin = function (event, data) {
        var _this = this;
        this.model.googleLogin().then(function (data) { return _this.loginRet(data, '1-2-4'); });
    };
    // path://buttons_n/area_be
    LoginButtonPnlCtrl.prototype.onClickArea = function (event, data) {
        this.showAreaList();
    };
    // path://area_box_be_n
    LoginButtonPnlCtrl.prototype.onClickAreaBox = function (event, data) {
        this.areaBoxNode_.active = false;
    };
    // path://area_box_be_n/root/area_item_be
    LoginButtonPnlCtrl.prototype.onClickAreaItem = function (event, data) {
        this.closeAreaList(event.target.Data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    LoginButtonPnlCtrl.prototype.updateListPosition = function () {
        if (this.preButtonHeight !== this.buttonsNode_.height) {
            this.preButtonHeight = this.buttonsNode_.height;
            var node = this.areaBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.buttonsNode_.Child('area_be/list'), this.areaBoxNode_));
        }
    };
    LoginButtonPnlCtrl.prototype.showAreaList = function () {
        this.areaBoxNode_.active = true;
        this.updateListPosition();
        var area = GameHelper_1.gameHpr.getServerArea();
        this.areaBoxNode_.Child('root').Items(GameHelper_1.gameHpr.getServerAreaList(), function (it, data) {
            it.Data = data;
            it.Child('val').setLocaleKey('login.server_area_' + data);
            it.Child('select').active = data === area;
        });
    };
    LoginButtonPnlCtrl.prototype.closeAreaList = function (area) {
        this.areaBoxNode_.active = false;
        if (area !== GameHelper_1.gameHpr.getServerArea()) {
            GameHelper_1.gameHpr.setServerArea(area);
            GameHelper_1.gameHpr.gameRestart();
        }
    };
    LoginButtonPnlCtrl.prototype.updateShowArea = function () {
        var node = this.buttonsNode_.Child('area_be');
        if (node.active) {
            node.Child('val').setLocaleKey('login.server_area_' + GameHelper_1.gameHpr.getServerArea());
        }
    };
    // 检测创建微信小游戏按钮
    LoginButtonPnlCtrl.prototype.checkCreateWechatGameButton = function () {
        var _this = this;
        var _a;
        var node = this.buttonsNode_.Child('wx_login_be');
        var isWechatGame = node.active = ut.isWechatGame();
        if (!isWechatGame) {
            return;
        }
        var isAuth = node.Component(cc.Button).interactable = this.model.isAuthUserInfo();
        if (!isAuth) { //如果还没有授权过 创建按钮让他授权
            (_a = WxHelper_1.wxHelper.createUserInfoButton(node)) === null || _a === void 0 ? void 0 : _a.onTap(function (res) {
                if (_this.isValid) {
                    _this.model.setWxUserInfo(res === null || res === void 0 ? void 0 : res.userInfo);
                    _this.model.wxLogin().then(function (data) { return _this.loginRet(data, '1-2-1'); });
                }
            });
        }
    };
    // 登陆结束处理
    LoginButtonPnlCtrl.prototype.loginRet = function (data, step) {
        if (data.state === Enums_1.LoginState.FAILURE) {
            return ViewHelper_1.viewHelper.showMessageBox(data.err);
        }
        else if (this.isValid) {
            this.hide();
        }
        this.emit(EventType_1.default.BUTTON_LOGIN_DONE, data);
        TaHelper_1.taHelper.track('ta_tutorial_v2', { tutorial_step: step, uid: GameHelper_1.gameHpr.getUid() });
        // 上报登陆->视为完成注册
        EventReportHelper_1.eventReportHelper.reportFacebookEventOne('fb_mobile_complete_registration', { fb_registration_method: step });
        EventReportHelper_1.eventReportHelper.reportFirebaseEventOne('sign_up', { method: step });
        EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('af_sign_up');
    };
    LoginButtonPnlCtrl = __decorate([
        ccclass
    ], LoginButtonPnlCtrl);
    return LoginButtonPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LoginButtonPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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