
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/MultiFrame.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9e586XjwJVGDZg1ihDgEKcb', 'MultiFrame');
// app/core/component/MultiFrame.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var MultiFrame = /** @class */ (function (_super) {
    __extends(MultiFrame, _super);
    function MultiFrame() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.frames = [];
        return _this;
    }
    MultiFrame.prototype.addFrame = function (sf) {
        this.frames.push(sf);
    };
    MultiFrame.prototype.setFrame = function (idx) {
        var i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0);
        if (i >= this.frames.length || i < 0) {
            return;
        }
        this.node.Component(cc.Sprite).spriteFrame = this.frames[i];
    };
    MultiFrame.prototype.getFrame = function (idx) {
        var i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0);
        return this.frames[i];
    };
    MultiFrame.prototype.getIndex = function () {
        var frame = this.node.Component(cc.Sprite).spriteFrame;
        return this.frames.findIndex(function (m) { return frame === m; });
    };
    MultiFrame.prototype.frameCount = function () {
        return this.frames.length;
    };
    MultiFrame.prototype.getSpriteFrame = function () {
        return this.node.Component(cc.Sprite).spriteFrame;
    };
    MultiFrame.prototype.random = function () {
        var len = this.frames.length;
        if (len === 0) {
            return -1;
        }
        var i = ut.random(0, len - 1);
        this.node.Component(cc.Sprite).spriteFrame = this.frames[i];
        return i;
    };
    MultiFrame.prototype.clean = function () {
        this.frames.length = 0;
    };
    __decorate([
        property([cc.SpriteFrame])
    ], MultiFrame.prototype, "frames", void 0);
    MultiFrame = __decorate([
        ccclass,
        menu('自定义组件/MultiFrame'),
        requireComponent(cc.Sprite)
    ], MultiFrame);
    return MultiFrame;
}(cc.Component));
exports.default = MultiFrame;
cc.MultiFrame = MultiFrame;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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