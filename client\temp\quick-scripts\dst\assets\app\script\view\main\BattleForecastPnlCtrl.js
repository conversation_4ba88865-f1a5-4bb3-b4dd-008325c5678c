
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/BattleForecastPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0647194LcBCoKSdsnLEQuAx', 'BattleForecastPnlCtrl');
// app/script/view/main/BattleForecastPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AreaObj_1 = require("../../model/area/AreaObj");
var ccclass = cc._decorator.ccclass;
var BattleForecastPnlCtrl = /** @class */ (function (_super) {
    __extends(BattleForecastPnlCtrl, _super);
    function BattleForecastPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_n
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        _this.contentNode_ = null; // path://root_n/content_n
        _this.buttonsNode_ = null; // path://root_n/buttons_nbe_n
        _this.retNode_ = null; // path://root_n/ret_n
        _this.playbackNode_ = null; // path://root_n/playback_be_n
        //@end
        _this.FPS = 20;
        _this.FPS_MUL = 400; //播放倍数
        _this.selectArmys = [];
        _this.targetCell = null;
        _this.selectArmyKey = '';
        _this.isRuning = false;
        _this.area = null;
        _this.startTime = 0;
        _this.god = false;
        _this.owner = '';
        _this.attackIndexAcc = 0;
        _this.randSeed = 0;
        _this.rodeleroCadetLv = 0;
        _this.passPoints = [];
        _this.policyBuffs = []; //政策buff
        _this.addArmyDataMap = {};
        _this.originalEnemyArmys = [];
        _this.enemyArmyConf = null;
        return _this;
    }
    BattleForecastPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.PLAYBACK_END] = this.onPlaybackEnd, _a.enter = true, _a)
        ];
    };
    BattleForecastPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BattleForecastPnlCtrl.prototype.onEnter = function (selectArmys, targetCell, god) {
        var _a;
        this.retNode_.active = this.playbackNode_.Data = this.playbackNode_.active = false;
        this.contentNode_.active = false;
        this.buttonsNode_.active = false;
        this.closeNode_.active = false;
        this.titleLbl_.Color('#3F332F').setLocaleKey('ui.ttile_battle_forecast');
        this.selectArmys = selectArmys;
        this.targetCell = targetCell;
        this.god = !!god && !GameHelper_1.gameHpr.isRelease;
        this.passPoints = MapHelper_1.mapHelper.getPassPoints(Constant_1.DEFAULT_AREA_SIZE);
        this.selectArmyKey = this.genSelectArmyKey(selectArmys);
        var data = GameHelper_1.gameHpr.player.getBattleForecastRetData(targetCell.index, this.selectArmyKey);
        if (data) {
            var dir = selectArmys.length > 0 ? MapHelper_1.mapHelper.getAddArmyDir((_a = selectArmys[0]) === null || _a === void 0 ? void 0 : _a.index, this.targetCell.index) : 0;
            this.getEnemyArmys(data.enemyArmyConf, this.passPoints[dir], []);
            return this.onComplete(data.isWin, data.armyMap, data.time, false);
        }
        this.policyBuffs = GameHelper_1.gameHpr.getPolicyBattleBuffs('');
        this.init(god);
    };
    BattleForecastPnlCtrl.prototype.onRemove = function () {
        this.unscheduleAllCallbacks();
    };
    BattleForecastPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/buttons_nbe_n
    BattleForecastPnlCtrl.prototype.onClickButtons = function (event, data) {
        var type = event.target.name;
        if (type === 'cancel') {
            this.hide();
        }
        else if (type === 'ok') {
            if (this.isNeedDeductGold() && GameHelper_1.gameHpr.user.getGold() < Constant_1.BATTLE_FORECAST_COST) {
                return ViewHelper_1.viewHelper.showGoldNotEnough();
            }
            this.startForecast();
        }
        else if (type === 'stop') {
            this.isRuning = false;
            this.hide();
        }
    };
    // path://root_n/playback_be_n
    BattleForecastPnlCtrl.prototype.onClickPlayback = function (event, data) {
        this.playbackNode_.Data = true;
        this.startForecast(this.god);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 预测结束
    BattleForecastPnlCtrl.prototype.onPlaybackEnd = function () {
        var _a;
        var time = (((_a = this.area.getFspModel()) === null || _a === void 0 ? void 0 : _a.getCurrentFrameIndex()) || 1) * 50;
        var armyMap = {}, isWin = false;
        this.area.armys.forEach(function (m) {
            armyMap[m.uid] = m.toPawnsByHP();
            if (!isWin && !!m.owner && m.pawns.some(function (p) { return !p.isDie(); })) {
                isWin = true;
            }
        });
        // 加上还没到的
        this.selectArmys.forEach(function (m) {
            if (!armyMap[m.uid]) {
                armyMap[m.uid] = { pawns: m.pawns.map(function (p) { var _a, _b; return { uid: p.uid, curHp: m.marchTime <= time ? 0 : (((_a = p.hp) === null || _a === void 0 ? void 0 : _a[0]) || 0), maxHp: (((_b = p.hp) === null || _b === void 0 ? void 0 : _b[1]) || 0) }; }) };
            }
        });
        this.onComplete(isWin, armyMap, time, !this.god);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BattleForecastPnlCtrl.prototype.init = function (god) {
        this.contentNode_.active = true;
        this.buttonsNode_.active = true;
        this.closeNode_.active = true;
        this.buttonsNode_.Swih('stop', true);
        this.updateRootHeight(438);
        var node = this.contentNode_.Swih(0)[0], hasCost = false;
        var battleForecastFreeCount = GameHelper_1.gameHpr.user.getBattleForecastFreeCount();
        if (battleForecastFreeCount > 0) {
            node.Child('cost').setLocaleKey('ui.forecast_cost_2', battleForecastFreeCount);
        }
        else {
            hasCost = true;
            node.Child('cost').setLocaleKey('ui.forecast_cost_3', Constant_1.BATTLE_FORECAST_COST);
        }
        if (god) {
            this.startForecast(true);
        }
    };
    BattleForecastPnlCtrl.prototype.genSelectArmyKey = function (selectArmys) {
        var key = '', targetIndex = this.targetCell.index;
        selectArmys.forEach(function (m) {
            if (key) {
                key += '_';
            }
            var dir = MapHelper_1.mapHelper.getAddArmyDir(m.index, targetIndex);
            key += m.uid + '_' + dir;
            m.pawns.forEach(function (p) {
                var _a, _b;
                key += '_' + p.id + '_' + p.lv + '_' + (((_a = p.hp) === null || _a === void 0 ? void 0 : _a[0]) || 0) + '_' + (((_b = p.hp) === null || _b === void 0 ? void 0 : _b[1]) || 0) + '_' + (p.attackSpeed || 0);
                if (p.equip) {
                    key += '_' + p.equip.id;
                    if (p.equip.attrs.length > 0) {
                        key += '_' + p.equip.attrs.join2(function (a) { return a.attr ? a.attr.join('_') : a.join('_'); }, '_');
                    }
                }
            });
        });
        return key;
    };
    BattleForecastPnlCtrl.prototype.updateRootHeight = function (height) {
        this.rootNode_.height = height;
        this.rootNode_.children.forEach(function (m) { return m.Component(cc.Widget).updateAlignment(); });
    };
    // 是否需要扣除金币
    BattleForecastPnlCtrl.prototype.isNeedDeductGold = function () {
        return GameHelper_1.gameHpr.user.getBattleForecastFreeCount() <= 0; /*  && !gameHpr.user.isHasSubscription() */
    };
    // 给士兵添加政策buff
    BattleForecastPnlCtrl.prototype.addPawnPolicyBuffs = function (pawn) {
        this.policyBuffs.forEach(function (m) {
            if (m.type === Enums_1.BuffType.LV_1_POWER && pawn.lv !== 1) {
                return; //一级之力 需要1级
            }
            pawn.buffs.push(m);
        });
    };
    // 开始预测
    BattleForecastPnlCtrl.prototype.startForecast = function (god) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, list, targetCell, owner, index, armys, fighters, army, point, conf, lastMarchTime, dt, accFrameIndexMap, i, len, army_1, waitTime, accIndex, frameIndex, arr, frames_1, _loop_1, this_1, key;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        this.enemyArmyConf = null;
                        if (!!god) return [3 /*break*/, 2];
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_GetAreaInfo', { index: this.targetCell.index, noRecord: true })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        if (!this.isValid) {
                            return [2 /*return*/];
                        }
                        this.enemyArmyConf = data === null || data === void 0 ? void 0 : data.data;
                        _c.label = 2;
                    case 2:
                        this.closeNode_.active = false;
                        this.contentNode_.Swih(1)[0].Child('anim/val', cc.Animation).play();
                        this.buttonsNode_.Swih('stop')[0].Component(cc.Button).interactable = true;
                        list = this.selectArmys, targetCell = this.targetCell;
                        owner = this.owner = GameHelper_1.gameHpr.getUid();
                        this.attackIndexAcc = 0;
                        // this.randSeed = /* 114200 */ut.random(100000, 999999)
                        this.randSeed = Math.floor(Number(owner) / 100) + targetCell.index;
                        this.rodeleroCadetLv = ((_a = GameHelper_1.gameHpr.getPlayerInfo(owner)) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0;
                        index = targetCell.index, armys = [], fighters = [];
                        army = this.toArmyStrip(list[0], owner, fighters);
                        armys.push(army);
                        point = army.pawns[0].point;
                        conf = this.getEnemyArmys(this.enemyArmyConf, point, fighters);
                        armys.pushArr(conf.armys);
                        // 加入后面的
                        this.addArmyDataMap = {};
                        lastMarchTime = list[0].marchTime, dt = 1000 / this.FPS;
                        accFrameIndexMap = {};
                        for (i = 1, len = list.length; i < len; i++) {
                            army_1 = list[i];
                            waitTime = army_1.marchTime - lastMarchTime;
                            accIndex = accFrameIndexMap[waitTime] || 0;
                            frameIndex = Math.max(1, Math.floor(waitTime / dt)) + accIndex;
                            accFrameIndexMap[waitTime] = accIndex + 1;
                            arr = this.addArmyDataMap[frameIndex];
                            if (!arr) {
                                arr = this.addArmyDataMap[frameIndex] = [];
                            }
                            arr.push(army_1);
                        }
                        this.area = new AreaObj_1.default().init({
                            index: index,
                            owner: '',
                            hp: conf.hp,
                            cityId: 0,
                            armys: armys,
                        });
                        // 播放回放
                        if (god && this.playbackNode_.Data) {
                            frames_1 = [{
                                    type: 0,
                                    camp: 1,
                                    owner: '',
                                    cityId: 0,
                                    randSeed: 114200,
                                    fps: 20,
                                    hp: conf.hp,
                                    armys: armys,
                                    fighters: fighters,
                                }];
                            _loop_1 = function (key) {
                                var currentFrameIndex = Number(key), armys_1 = this_1.addArmyDataMap[key];
                                armys_1.forEach(function (m) {
                                    var fighters = [];
                                    var army = _this.toArmyStrip(m, _this.owner, fighters);
                                    frames_1.push({ type: 1, currentFrameIndex: currentFrameIndex, army: army, fighters: fighters });
                                });
                            };
                            this_1 = this;
                            for (key in this.addArmyDataMap) {
                                _loop_1(key);
                            }
                            GameHelper_1.gameHpr.playback.setRecordData({ index: index, frames: frames_1 }).then(function () { return ViewHelper_1.viewHelper.gotoWind('playback'); });
                            this.hide();
                        }
                        else {
                            // 直接开始后台模拟战斗
                            this.area.battleLocalBegin({
                                camp: 1,
                                randSeed: this.randSeed,
                                accAttackIndex: this.attackIndexAcc,
                                fps: this.FPS,
                                fighters: fighters,
                                mul: this.FPS_MUL,
                            }, {});
                            this.area.getFspModel().setCheckHasFrameData(this.onCheckHasFrameData.bind(this));
                            this.isRuning = true;
                            this.startTime = Date.now();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    BattleForecastPnlCtrl.prototype.getEnemyArmys = function (data, point, fighters) {
        var _this = this;
        var index = this.targetCell.index;
        var conf = data || GameHelper_1.gameHpr.getAreaPawnConfInfo(index, this.targetCell.landId, GameHelper_1.gameHpr.getSelfToMapCellDis(index));
        conf.armys.forEach(function (army) {
            army.state = Enums_1.ArmyState.FIGHT;
            army.pawns.sort(function (a, b) {
                var ap = assetsMgr.getJsonData('pawnBase', a.id).attack_speed;
                var bp = assetsMgr.getJsonData('pawnBase', b.id).attack_speed;
                var aw = ap * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(a.point, point));
                var bw = bp * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(b.point, point));
                return bw - aw;
            });
            army.pawns.forEach(function (pawn) { return fighters.push({
                uid: pawn.uid,
                camp: 1,
                attackIndex: ++_this.attackIndexAcc,
            }); });
        });
        this.originalEnemyArmys = conf.armys;
        return conf;
    };
    BattleForecastPnlCtrl.prototype.toArmyStrip = function (army, owner, fighters) {
        var _this = this;
        var dir = MapHelper_1.mapHelper.getAddArmyDir(army.index, this.targetCell.index);
        var point = this.passPoints[dir];
        var pawns = this.toPawnsStrip(army.pawns, army.index, point);
        pawns.sort(function (a, b) { return b.attackSpeed - a.attackSpeed; });
        pawns.forEach(function (pawn) {
            fighters.push({
                uid: pawn.uid,
                camp: 2,
                attackIndex: ++_this.attackIndexAcc,
            });
            _this.addPawnPolicyBuffs(pawn);
        });
        return {
            index: army.index,
            uid: army.uid,
            name: army.name,
            owner: owner,
            pawns: pawns,
            state: Enums_1.ArmyState.FIGHT,
            enterDir: dir,
        };
    };
    BattleForecastPnlCtrl.prototype.toPawnsStrip = function (pawns, index, point) {
        var _this = this;
        return pawns.map(function (m) {
            return {
                index: index,
                uid: m.uid,
                id: m.id,
                lv: m.lv,
                skinId: m.skinId,
                hp: m.hp ? [m.hp[0], m.hp[1]] : undefined,
                point: (_this.god && m.point) ? m.point : { x: point.x, y: point.y },
                equip: m.equip,
                portrayal: m.portrayal,
                rodeleroCadetLv: m.id === 3205 ? _this.rodeleroCadetLv : 0,
                attackSpeed: m.attackSpeed || 0,
                buffs: [],
            };
        });
    };
    BattleForecastPnlCtrl.prototype.onCheckHasFrameData = function (currentFrameIndex) {
        var _this = this;
        var _a;
        var armys = this.addArmyDataMap[currentFrameIndex];
        if (!armys) {
            return;
        }
        var fsp = (_a = this.area) === null || _a === void 0 ? void 0 : _a.getFspModel();
        if (!fsp) {
            return;
        }
        armys.forEach(function (m) {
            var fighters = [];
            var army = _this.toArmyStrip(m, _this.owner, fighters);
            fsp.checkHasFrameDataItem({ type: 1, army: army, fighters: fighters });
        });
    };
    BattleForecastPnlCtrl.prototype.update = function (dt) {
        var _a;
        if (!this.isRuning || !this.area) {
            return;
        }
        (_a = this.area.getFspModel()) === null || _a === void 0 ? void 0 : _a.update(dt);
    };
    BattleForecastPnlCtrl.prototype.onComplete = function (isWin, armyMap, time, deduct) {
        return __awaiter(this, void 0, void 0, function () {
            var originalArmys, delay, index, key;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.isRuning = false;
                        originalArmys = isWin ? this.selectArmys : this.originalEnemyArmys;
                        this.showRet(originalArmys, armyMap, time, deduct);
                        if (!deduct) return [3 /*break*/, 4];
                        delay = Math.max(100, 2400 - (Date.now() - this.startTime));
                        if (!(delay > 0)) return [3 /*break*/, 2];
                        this.unscheduleAllCallbacks();
                        return [4 /*yield*/, ut.wait(delay * 0.001, this)];
                    case 1:
                        _a.sent();
                        if (!this.isValid || !this.getActive()) {
                            return [2 /*return*/];
                        }
                        _a.label = 2;
                    case 2:
                        this.buttonsNode_.Child('stop', cc.Button).interactable = false;
                        index = this.targetCell.index, key = this.selectArmyKey;
                        return [4 /*yield*/, GameHelper_1.gameHpr.user.deductBattleForecastCost(this.targetCell.landLv, GameHelper_1.gameHpr.getSelfToMapCellDis(index))];
                    case 3:
                        _a.sent();
                        GameHelper_1.gameHpr.player.setBattleForecastRetMap(index, key, { isWin: isWin, armyMap: armyMap, time: time, enemyArmyConf: this.enemyArmyConf });
                        if (!this.isValid) {
                            return [2 /*return*/];
                        }
                        _a.label = 4;
                    case 4:
                        this.titleLbl_.Color(isWin ? '#4AB32E' : '#D7634D').setLocaleKey('ui.title_battle_forecast_ret_' + (isWin ? 1 : 0));
                        this.contentNode_.active = false;
                        this.buttonsNode_.active = false;
                        this.closeNode_.active = true;
                        this.retNode_.opacity = 255;
                        this.updateRootHeight(this.retNode_.height + 110);
                        return [2 /*return*/];
                }
            });
        });
    };
    BattleForecastPnlCtrl.prototype.showRet = function (originalArmys, armyMap, time, deduct) {
        var _this = this;
        this.retNode_.active = true;
        this.retNode_.opacity = 0;
        var height = 44;
        this.retNode_.Child('cost/time').setLocaleKey('ui.forecast_battle_time', ut.millisecondFormat(time, 'h:mm:ss'));
        this.retNode_.Child('cost/val').setLocaleKey(this.isNeedDeductGold() && deduct ? 'ui.yet_deduct_forecast_cost' : '', Constant_1.BATTLE_FORECAST_COST);
        var armysNode = this.retNode_.Child('armys');
        armysNode.Items(0);
        armysNode.Items(originalArmys, function (it, data) {
            var army = armyMap[data.uid], pawnMap = {};
            army === null || army === void 0 ? void 0 : army.pawns.forEach(function (m) { return pawnMap[m.uid] = m; });
            it.Child('name/val', cc.Label).string = data.name || assetsMgr.lang('ui.enemy_army_name');
            it.Child('pawns').Items(data.pawns, function (node, pawn) {
                var _a;
                var actPawn = pawnMap[pawn.uid], isDie = !actPawn || actPawn.curHp <= 0;
                var icon = node.Child('icon');
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id, icon, _this.key);
                node.Child('die').active = isDie;
                icon.opacity = isDie ? 120 : 255;
                icon.Component(cc.Sprite).setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(!isDie));
                node.Child('lv', cc.Label).string = pawn.lv <= 1 ? '' : '' + pawn.lv;
                node.Child('hp/bar', cc.Sprite).fillRange = isDie ? 0 : actPawn.curHp / actPawn.maxHp;
            });
            var cnt = Math.ceil(data.pawns.length / 9);
            var h = cnt * 52 + (cnt - 1) * 8 + 56;
            it.height = h;
            height += h;
        });
        height += Math.max(0, originalArmys.length - 1) * 8;
        this.retNode_.height = height;
        this.retNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        this.playbackNode_.active = this.god;
        this.playbackNode_.Data = false;
    };
    BattleForecastPnlCtrl = __decorate([
        ccclass
    ], BattleForecastPnlCtrl);
    return BattleForecastPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BattleForecastPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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