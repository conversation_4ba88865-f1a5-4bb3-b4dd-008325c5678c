
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/Enums.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '911122p36BFeJcaqiT9JFE9', 'Enums');
// app/script/common/constant/Enums.ts

"use strict";
/////////////// 所有枚举（全大写单词间用下划线隔开）///////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.BattleStatistics = exports.WinCondType = exports.BookCommentType = exports.StudyType = exports.NoticePermissionType = exports.SceneRoleStateType = exports.MapRoleType = exports.MapType = exports.MapSceneType = exports.SelectPortrayalType = exports.HeroType = exports.RoomStateType = exports.BUILD_NID = exports.SwitchType = exports.ReportErrorLevel = exports.PayPlatformType = exports.AdPlayState = exports.AdState = exports.AdType = exports.ShareType = exports.EquipEffectType = exports.MailStateType = exports.AllianceJobType = exports.BuffType = exports.PawnSkillType = exports.PawnInfoType = exports.TransitTargetType = exports.MarchTargetType = exports.MarchLineType = exports.MerchantState = exports.PawnState = exports.ArmyState = exports.PawnType = exports.DecorationID = exports.DecorationType = exports.LandType = exports.DragTouchType = exports.CEffect = exports.TaskState = exports.TCusCType = exports.TCType = exports.CType = exports.NoLongerTipKey = exports.PreferenceKey = exports.LobbyModeType = exports.ServerType = exports.NotifyType = exports.LobbyNotifyType = exports.LoginType = exports.LoginState = void 0;
exports.MonthlyCardType = void 0;
// 登录状态
var LoginState;
(function (LoginState) {
    LoginState[LoginState["SUCCEED"] = 0] = "SUCCEED";
    LoginState[LoginState["FAILURE"] = 1] = "FAILURE";
    LoginState[LoginState["NOT_ACCOUNT_TOKEN"] = 2] = "NOT_ACCOUNT_TOKEN";
    LoginState[LoginState["UNDONE_NOVICE"] = 3] = "UNDONE_NOVICE";
    LoginState[LoginState["NOT_SELECT_SERVER"] = 4] = "NOT_SELECT_SERVER";
    LoginState[LoginState["VERSION_TOOLOW"] = 5] = "VERSION_TOOLOW";
    LoginState[LoginState["VERSION_TOOTALL"] = 6] = "VERSION_TOOTALL";
    LoginState[LoginState["LOGOUT_TIME"] = 7] = "LOGOUT_TIME";
    LoginState[LoginState["BANACCOUNT_TIME"] = 8] = "BANACCOUNT_TIME";
    LoginState[LoginState["QUEUE_UP"] = 9] = "QUEUE_UP";
})(LoginState || (LoginState = {}));
exports.LoginState = LoginState;
// 登陆类型
var LoginType;
(function (LoginType) {
    LoginType["GUEST"] = "guest";
    LoginType["ACCOUNT"] = "account";
    LoginType["WX"] = "wx";
    LoginType["GOOGLE"] = "google";
    LoginType["APPLE"] = "apple";
    LoginType["FACEBOOK"] = "facebook";
    LoginType["TWITTER"] = "twitter";
    LoginType["LINE"] = "line";
})(LoginType || (LoginType = {}));
exports.LoginType = LoginType;
// 服务器类型
var ServerType;
(function (ServerType) {
    ServerType[ServerType["FREE"] = 0] = "FREE";
    ServerType[ServerType["NEWBIE"] = 1] = "NEWBIE";
    ServerType[ServerType["RANKED"] = 2] = "RANKED";
})(ServerType || (ServerType = {}));
exports.ServerType = ServerType;
// 大厅模式类型
var LobbyModeType;
(function (LobbyModeType) {
    LobbyModeType[LobbyModeType["FREE"] = 0] = "FREE";
    LobbyModeType[LobbyModeType["NEWBIE"] = 1] = "NEWBIE";
    LobbyModeType[LobbyModeType["RANKED"] = 2] = "RANKED";
    LobbyModeType[LobbyModeType["SNAIL_ISLE"] = 100] = "SNAIL_ISLE";
})(LobbyModeType || (LobbyModeType = {}));
exports.LobbyModeType = LobbyModeType;
// 大厅通知类型
var LobbyNotifyType;
(function (LobbyNotifyType) {
    LobbyNotifyType[LobbyNotifyType["NONE"] = 0] = "NONE";
    LobbyNotifyType[LobbyNotifyType["NQ_SYS_MSG"] = 1] = "NQ_SYS_MSG";
    LobbyNotifyType[LobbyNotifyType["NQ_SYS_NOTICE"] = 2] = "NQ_SYS_NOTICE";
    LobbyNotifyType[LobbyNotifyType["NQ_USER_TRUMPET"] = 3] = "NQ_USER_TRUMPET";
})(LobbyNotifyType || (LobbyNotifyType = {}));
exports.LobbyNotifyType = LobbyNotifyType;
// 服务器的通知类型
var NotifyType;
(function (NotifyType) {
    NotifyType[NotifyType["NONE"] = 0] = "NONE";
    NotifyType[NotifyType["OUTPUT"] = 1] = "OUTPUT";
    NotifyType[NotifyType["ARMY_DIST"] = 2] = "ARMY_DIST";
    NotifyType[NotifyType["ADD_CELL"] = 3] = "ADD_CELL";
    NotifyType[NotifyType["REMOVE_CELLS"] = 4] = "REMOVE_CELLS";
    NotifyType[NotifyType["BUILD_UP"] = 5] = "BUILD_UP";
    NotifyType[NotifyType["BT_QUEUE"] = 6] = "BT_QUEUE";
    NotifyType[NotifyType["MOVE_BUILD"] = 7] = "MOVE_BUILD";
    NotifyType[NotifyType["ADD_BUILD"] = 8] = "ADD_BUILD";
    NotifyType[NotifyType["REMOVE_BUILD"] = 9] = "REMOVE_BUILD";
    NotifyType[NotifyType["ADD_ARMY"] = 10] = "ADD_ARMY";
    NotifyType[NotifyType["MOVE_PAWN"] = 11] = "MOVE_PAWN";
    NotifyType[NotifyType["REMOVE_ARMY"] = 12] = "REMOVE_ARMY";
    NotifyType[NotifyType["ADD_MARCH"] = 13] = "ADD_MARCH";
    NotifyType[NotifyType["REMOVE_MARCH"] = 14] = "REMOVE_MARCH";
    NotifyType[NotifyType["AREA_BATTLE_BEGIN"] = 15] = "AREA_BATTLE_BEGIN";
    NotifyType[NotifyType["AREA_BATTLE_END"] = 16] = "AREA_BATTLE_END";
    NotifyType[NotifyType["BATTLE_DIST"] = 17] = "BATTLE_DIST";
    NotifyType[NotifyType["PAWN_DRILL_QUEUE"] = 18] = "PAWN_DRILL_QUEUE";
    NotifyType[NotifyType["UPDATE_ARMY"] = 19] = "UPDATE_ARMY";
    NotifyType[NotifyType["UPDATE_MERCHANT"] = 20] = "UPDATE_MERCHANT";
    NotifyType[NotifyType["ADD_TRANSIT"] = 21] = "ADD_TRANSIT";
    NotifyType[NotifyType["REMOVE_TRANSIT"] = 22] = "REMOVE_TRANSIT";
    NotifyType[NotifyType["UPDATE_CELL_HP"] = 23] = "UPDATE_CELL_HP";
    NotifyType[NotifyType["UPDATE_ALL_PAWN_HP"] = 24] = "UPDATE_ALL_PAWN_HP";
    NotifyType[NotifyType["CHANGE_PAWN_ATTR"] = 25] = "CHANGE_PAWN_ATTR";
    NotifyType[NotifyType["AREA_AVOID_WAR"] = 26] = "AREA_AVOID_WAR";
    NotifyType[NotifyType["ADD_BTCITY"] = 27] = "ADD_BTCITY";
    NotifyType[NotifyType["REMOVE_BTCITY"] = 28] = "REMOVE_BTCITY";
    NotifyType[NotifyType["CAPTURE"] = 29] = "CAPTURE";
    NotifyType[NotifyType["DISSOLVE_ALLIANCE"] = 30] = "DISSOLVE_ALLIANCE";
    NotifyType[NotifyType["PLAYER_CITY_OUTPUT"] = 31] = "PLAYER_CITY_OUTPUT";
    NotifyType[NotifyType["PLAYER_JOIN_ALLI"] = 32] = "PLAYER_JOIN_ALLI";
    NotifyType[NotifyType["NEW_MAIL"] = 33] = "NEW_MAIL";
    NotifyType[NotifyType["FORGE_EQUIP_RET"] = 34] = "FORGE_EQUIP_RET";
    NotifyType[NotifyType["UPDATE_INVITES"] = 35] = "UPDATE_INVITES";
    NotifyType[NotifyType["GAME_OVER"] = 36] = "GAME_OVER";
    NotifyType[NotifyType["MODIFY_NICKNAME"] = 37] = "MODIFY_NICKNAME";
    NotifyType[NotifyType["ADD_OUTPUT_TIME"] = 38] = "ADD_OUTPUT_TIME";
    NotifyType[NotifyType["UPDATE_PAWN_TREASURE"] = 39] = "UPDATE_PAWN_TREASURE";
    NotifyType[NotifyType["PAWN_LEVELING_QUEUE"] = 40] = "PAWN_LEVELING_QUEUE";
    NotifyType[NotifyType["UPDATE_ITEMS"] = 41] = "UPDATE_ITEMS";
    NotifyType[NotifyType["UPDATE_RP_GOLD"] = 42] = "UPDATE_RP_GOLD";
    NotifyType[NotifyType["PLAYER_EXIT_ALLI"] = 43] = "PLAYER_EXIT_ALLI";
    NotifyType[NotifyType["DELETE_PLAYER"] = 44] = "DELETE_PLAYER";
    NotifyType[NotifyType["PLAYER_TOWER_LV"] = 45] = "PLAYER_TOWER_LV";
    NotifyType[NotifyType["UPDATE_CERI_SLOT"] = 46] = "UPDATE_CERI_SLOT";
    NotifyType[NotifyType["CERI_STUDY_DONE"] = 47] = "CERI_STUDY_DONE";
    NotifyType[NotifyType["UPDATE_GENERAL_TASKS"] = 48] = "UPDATE_GENERAL_TASKS";
    NotifyType[NotifyType["CHANGE_TITLE"] = 49] = "CHANGE_TITLE";
    NotifyType[NotifyType["NEW_TREASURE"] = 50] = "NEW_TREASURE";
    NotifyType[NotifyType["UPDATE_WHEEL_COUNT"] = 51] = "UPDATE_WHEEL_COUNT";
    NotifyType[NotifyType["UPDATE_TODAY_INFO"] = 52] = "UPDATE_TODAY_INFO";
    NotifyType[NotifyType["PLAYER_POLICY"] = 53] = "PLAYER_POLICY";
    NotifyType[NotifyType["SYS_MSG"] = 54] = "SYS_MSG";
    NotifyType[NotifyType["UPDATE_TASKS"] = 55] = "UPDATE_TASKS";
    NotifyType[NotifyType["PLAYER_CADET_LV"] = 56] = "PLAYER_CADET_LV";
    NotifyType[NotifyType["UPDATE_LAND_SCORE"] = 57] = "UPDATE_LAND_SCORE";
    NotifyType[NotifyType["PLAYER_GIVEUP_GAME"] = 58] = "PLAYER_GIVEUP_GAME";
    NotifyType[NotifyType["USER_SUBSCRIPTION"] = 59] = "USER_SUBSCRIPTION";
    NotifyType[NotifyType["TRADE_PRICE"] = 60] = "TRADE_PRICE";
    NotifyType[NotifyType["CELL_EMOJI"] = 61] = "CELL_EMOJI";
    NotifyType[NotifyType["ALLI_BASE_INFO"] = 62] = "ALLI_BASE_INFO";
    NotifyType[NotifyType["UPDATE_ARMY_TREASURES"] = 63] = "UPDATE_ARMY_TREASURES";
    NotifyType[NotifyType["SMELT_EQUIP_RET"] = 64] = "SMELT_EQUIP_RET";
    NotifyType[NotifyType["UPDATE_SEASON"] = 65] = "UPDATE_SEASON";
    NotifyType[NotifyType["ANCIENT_INFO"] = 66] = "ANCIENT_INFO";
    NotifyType[NotifyType["AREA_PLAYER_CHANGE"] = 67] = "AREA_PLAYER_CHANGE";
    NotifyType[NotifyType["AREA_CHAT"] = 68] = "AREA_CHAT";
    NotifyType[NotifyType["CHANGE_PAWN_PORTRAYAL"] = 69] = "CHANGE_PAWN_PORTRAYAL";
    NotifyType[NotifyType["CHANGE_HERO_SLOT_INFO"] = 70] = "CHANGE_HERO_SLOT_INFO";
    NotifyType[NotifyType["ADD_PLAYER"] = 71] = "ADD_PLAYER";
    NotifyType[NotifyType["TODAY_TRUMPET_COUNT"] = 72] = "TODAY_TRUMPET_COUNT";
    NotifyType[NotifyType["TODAY_FREE_GOLD_TIME"] = 73] = "TODAY_FREE_GOLD_TIME";
    NotifyType[NotifyType["COMPENSATE"] = 74] = "COMPENSATE";
    NotifyType[NotifyType["CHANGE_CITY_SKIN"] = 75] = "CHANGE_CITY_SKIN";
    NotifyType[NotifyType["UPDATE_TITLES"] = 76] = "UPDATE_TITLES";
    NotifyType[NotifyType["ACTIVITY_RECORD"] = 77] = "ACTIVITY_RECORD";
    NotifyType[NotifyType["BATTLE_PASS_HAS_AWARD"] = 78] = "BATTLE_PASS_HAS_AWARD";
    NotifyType[NotifyType["CELL_TONDEN"] = 79] = "CELL_TONDEN";
    NotifyType[NotifyType["CELL_TONDEN_END"] = 80] = "CELL_TONDEN_END";
    NotifyType[NotifyType["CELL_TONDEN_COUNT"] = 81] = "CELL_TONDEN_COUNT";
    NotifyType[NotifyType["PAWN_CURING_QUEUE"] = 82] = "PAWN_CURING_QUEUE";
    NotifyType[NotifyType["PAWN_INJURY_ADD"] = 83] = "PAWN_INJURY_ADD";
    NotifyType[NotifyType["PAWN_INJURY_REMOVE"] = 84] = "PAWN_INJURY_REMOVE";
    NotifyType[NotifyType["UPDATE_POLICY_SLOT"] = 85] = "UPDATE_POLICY_SLOT";
    NotifyType[NotifyType["UPDATE_EQUIP_SLOT"] = 86] = "UPDATE_EQUIP_SLOT";
    NotifyType[NotifyType["UPDATE_PAWN_SLOT"] = 87] = "UPDATE_PAWN_SLOT";
    NotifyType[NotifyType["ALLI_SETTLE"] = 88] = "ALLI_SETTLE";
    NotifyType[NotifyType["WORLD_EVENT"] = 89] = "WORLD_EVENT";
    NotifyType[NotifyType["REMOVE_ARMY_BY_MARCH"] = 90] = "REMOVE_ARMY_BY_MARCH";
})(NotifyType || (NotifyType = {}));
exports.NotifyType = NotifyType;
// 偏好设置key
var PreferenceKey;
(function (PreferenceKey) {
    PreferenceKey["BARRAGE_OPEN"] = "BARRAGE_OPEN";
    PreferenceKey["BARRAGE_AREA_RATIO"] = "BARRAGE_AREA_RATIO_";
    PreferenceKey["ALLI_CHAT_CHANNEL"] = "ALLI_CHAT_CHANNEL_";
    PreferenceKey["WORLD_CHAT_CHANNEL"] = "WORLD_CHAT_CHANNEL_";
    PreferenceKey["WORLD_CHAT_TAB"] = "WORLD_CHAT_TAB_";
    PreferenceKey["ALLI_CHAT_TAB"] = "ALLI_CHAT_TAB_";
    PreferenceKey["SEND_INFO_TO_CHAT_TYPE"] = "SEND_INFO_TO_CHAT_TYPE_";
    PreferenceKey["MAIN_CAMERA_ZOOM"] = "MAIN_CAMERA_ZOOM";
    PreferenceKey["FIXATION_MENU_DATA"] = "FIXATION_MENU_DATA";
    PreferenceKey["NO_LONGER_TIP"] = "NO_LONGER_TIP";
    PreferenceKey["SELECT_ARMY_SORT"] = "SELECT_ARMY_SORT";
    PreferenceKey["INJURY_QUEUE_SORT"] = "INJURY_QUEUE_SORT";
    PreferenceKey["BATTLE_AUTO_BACK_TYPE"] = "BATTLE_AUTO_BACK_TYPE";
    PreferenceKey["SHOW_PAWN_LV"] = "SHOW_PAWN_LV_NEW";
    PreferenceKey["SHOW_ARMY_NAME"] = "SHOW_ARMY_NAME";
    PreferenceKey["SHOW_PAWN_EQUIP"] = "SHOW_PAWN_EQUIP";
    PreferenceKey["AREA_ZOOM_RATIO"] = "AREA_ZOOM_RATIO";
    PreferenceKey["YET_PLAY_NEW_CELL"] = "YET_PLAY_NEW_CELL";
    PreferenceKey["TEMP_CREATE_ARMY"] = "TEMP_CREATE_ARMY";
    PreferenceKey["SHOW_GUIDE_TASK_TIP"] = "SHOW_GUIDE_TASK_TIP";
    PreferenceKey["USE_EMOJIS_COUNT"] = "USE_EMOJIS_COUNT";
    PreferenceKey["LAST_USE_CELL_EMOJIS"] = "LAST_USE_CELL_EMOJIS";
    PreferenceKey["SYNC_PAWN_EQUIP_CONF"] = "SYNC_PAWN_EQUIP_CONF";
    PreferenceKey["SYNC_PAWN_SKIN_CONF"] = "SYNC_PAWN_SKIN_CONF";
    PreferenceKey["LOCK_EQUIP_EFFECT_CONF"] = "LOCK_EQUIP_EFFECT_CONF";
    PreferenceKey["MAIN_CITY_INDEX"] = "MAIN_CITY_INDEX";
    PreferenceKey["SHOW_ANCIENT_IN_MAP"] = "SHOW_ANCIENT_IN_MAP";
    PreferenceKey["SHOW_FORT_IN_MAP"] = "SHOW_FORT_IN_MAP";
    PreferenceKey["SHOW_ALLI_IN_MAP"] = "SHOW_ALLI_IN_MAP";
    PreferenceKey["SHOW_ARMY_IN_MAP"] = "SHOW_ARMY_IN_MAP";
    PreferenceKey["SHOW_FLAG_IN_MAP"] = "SHOW_FLAG_IN_MAP";
    PreferenceKey["SHOW_MARK_IN_MAP"] = "SHOW_MARK_IN_MAP";
    PreferenceKey["SHOW_BATTLE_IN_MAP"] = "SHOW_BATTLE_IN_MAP";
    PreferenceKey["SET_MARCH_LINE_OPACITY"] = "SET_MARCH_LINE_OPACITY";
    PreferenceKey["LAST_ALLI_ICON"] = "LAST_ALLI_ICON";
    PreferenceKey["ADD_EXTRA_TORANK"] = "ADD_EXTRA_TORANK";
    PreferenceKey["BAZAAR_FILTER_SELL"] = "BAZAAR_FILTER_sell";
    PreferenceKey["BAZAAR_FILTER_BUY"] = "BAZAAR_FILTER_buy";
    PreferenceKey["BAZAAR_SORT"] = "BAZAAR_SORT";
    PreferenceKey["SHOW_PAWN_EQUIP_AND_SPEED"] = "SHOW_PAWN_EQUIP_AND_SPEED";
    PreferenceKey["SHOW_ALLY_ARMY"] = "SHOW_ALLY_ARMY";
    PreferenceKey["SHOW_RISK_COUNT"] = "SHOW_RISK_COUNT";
    PreferenceKey["LAST_PLAY_SEASON_TYPE"] = "LAST_PLAY_SEASON_TYPE";
    PreferenceKey["LOBBY_SELECT_ROOM_TYPE"] = "LOBBY_SELECT_ROOM_TYPE";
    PreferenceKey["PORTRAYAL_LIST_SORT"] = "PORTRAYAL_LIST_SORT";
    PreferenceKey["LOCK_COMP_PORTRAYAL"] = "LOCK_COMP_PORTRAYAL";
    PreferenceKey["SHOW_EQUIP_ATTR_RANGE"] = "SHOW_EQUIP_ATTR_RANGE";
    PreferenceKey["SHOW_PORTRAYAL_ATTR_RANGE"] = "SHOW_PORTRAYAL_ATTR_RANGE";
    PreferenceKey["ARRIVE_SIMULTANEOUSLY"] = "ARRIVE_SIMULTANEOUSLY";
    PreferenceKey["ONTOP_FLAGS"] = "ONTOP_FLAGS";
    PreferenceKey["WAIT_SETTLE"] = "WAIT_SETTLE";
})(PreferenceKey || (PreferenceKey = {}));
exports.PreferenceKey = PreferenceKey;
// 不再提示key
var NoLongerTipKey;
(function (NoLongerTipKey) {
    NoLongerTipKey["APPLY_SUCCEED"] = "APPLY_SUCCEED";
    NoLongerTipKey["SHOW_MODE_RULE"] = "SHOW_MODE_RULE";
    NoLongerTipKey["SHOW_BUY_MYSTERYBOX_RULE"] = "SHOW_BUY_MYSTERYBOX_RULE";
    NoLongerTipKey["SHOW_BUILD_BARRACKS_DRILL"] = "SHOW_BUILD_BARRACKS_DRILL";
})(NoLongerTipKey || (NoLongerTipKey = {}));
exports.NoLongerTipKey = NoLongerTipKey;
// 通用类型
var CType;
(function (CType) {
    CType[CType["NONE"] = 0] = "NONE";
    CType[CType["CEREAL"] = 1] = "CEREAL";
    CType[CType["TIMBER"] = 2] = "TIMBER";
    CType[CType["STONE"] = 3] = "STONE";
    CType[CType["BUILD_LV"] = 4] = "BUILD_LV";
    CType[CType["GOLD"] = 5] = "GOLD";
    CType[CType["TREASURE"] = 6] = "TREASURE";
    CType[CType["EXP_BOOK"] = 7] = "EXP_BOOK";
    CType[CType["CEREAL_C"] = 8] = "CEREAL_C";
    CType[CType["IRON"] = 9] = "IRON";
    CType[CType["TITLE"] = 10] = "TITLE";
    CType[CType["PAWN_SKIN"] = 11] = "PAWN_SKIN";
    CType[CType["CELL_COUNT"] = 12] = "CELL_COUNT";
    CType[CType["UP_SCROLL"] = 13] = "UP_SCROLL";
    CType[CType["FIXATOR"] = 14] = "FIXATOR";
    CType[CType["EQUIP"] = 15] = "EQUIP";
    CType[CType["PAWN"] = 16] = "PAWN";
    CType[CType["POLICY"] = 17] = "POLICY";
    CType[CType["BASE_RES"] = 18] = "BASE_RES";
    CType[CType["BASE_RES_2"] = 19] = "BASE_RES_2";
    CType[CType["COMPLETE_GUIDE"] = 20] = "COMPLETE_GUIDE";
    CType[CType["WIN_POINT"] = 21] = "WIN_POINT";
    CType[CType["INGOT"] = 22] = "INGOT";
    CType[CType["WAR_TOKEN"] = 23] = "WAR_TOKEN";
    CType[CType["HERO_DEBRIS"] = 24] = "HERO_DEBRIS";
    CType[CType["HERO_OPT"] = 25] = "HERO_OPT";
    CType[CType["STAMINA"] = 26] = "STAMINA";
    CType[CType["UP_RECRUIT"] = 27] = "UP_RECRUIT";
    CType[CType["SKIN_ITEM"] = 28] = "SKIN_ITEM";
    CType[CType["CITY_SKIN"] = 29] = "CITY_SKIN";
    CType[CType["RANK_COIN"] = 30] = "RANK_COIN";
    CType[CType["HEAD_ICON"] = 31] = "HEAD_ICON";
    CType[CType["CHAT_EMOJI"] = 32] = "CHAT_EMOJI";
    CType[CType["BOTANY"] = 33] = "BOTANY";
    CType[CType["BATTLE_PASS"] = 34] = "BATTLE_PASS";
    CType[CType["BATTLE_PASS_SCORE"] = 35] = "BATTLE_PASS_SCORE";
    CType[CType["FREE_RECRUIT"] = 36] = "FREE_RECRUIT";
    CType[CType["FREE_LEVING"] = 37] = "FREE_LEVING";
    CType[CType["FREE_CURE"] = 38] = "FREE_CURE";
    CType[CType["FREE_FORGE"] = 39] = "FREE_FORGE";
    CType[CType["FREE_AD"] = 40] = "FREE_AD";
})(CType || (CType = {}));
exports.CType = CType;
// 任务条件类型
var TCType;
(function (TCType) {
    TCType[TCType["NONE"] = 0] = "NONE";
    TCType[TCType["CUSTOM"] = 3] = "CUSTOM";
    TCType[TCType["BUILD_LV"] = 4] = "BUILD_LV";
    TCType[TCType["CELL_COUNT"] = 12] = "CELL_COUNT";
    TCType[TCType["LAND_LV_COUNT"] = 1001] = "LAND_LV_COUNT";
    TCType[TCType["SIGN_DAY_COUNT"] = 1002] = "SIGN_DAY_COUNT";
    TCType[TCType["INVITE_FRIEND"] = 1003] = "INVITE_FRIEND";
    TCType[TCType["TODAY_TURNTABLE_COUNT"] = 1004] = "TODAY_TURNTABLE_COUNT";
    TCType[TCType["LAND_TYPE_COUNT"] = 1005] = "LAND_TYPE_COUNT";
    TCType[TCType["WIN_COUNT"] = 1006] = "WIN_COUNT";
    TCType[TCType["LIVER_EMPEROR"] = 1007] = "LIVER_EMPEROR";
    TCType[TCType["THOUSAND_MU"] = 1008] = "THOUSAND_MU";
    TCType[TCType["TODAY_CELL_COUNT"] = 1009] = "TODAY_CELL_COUNT";
    TCType[TCType["GIVE_RES_COUNT"] = 1010] = "GIVE_RES_COUNT";
    TCType[TCType["EQUIP_ALL_ATTR_MAX"] = 1011] = "EQUIP_ALL_ATTR_MAX";
    TCType[TCType["EQUIP_RECAST_COUNT"] = 1012] = "EQUIP_RECAST_COUNT";
    TCType[TCType["ONE_BLOOD_SURVIVE"] = 1013] = "ONE_BLOOD_SURVIVE";
    TCType[TCType["C_DODGE_FIVE_TIMES"] = 1014] = "C_DODGE_FIVE_TIMES";
    TCType[TCType["SAME_LEVEL_SECKILL"] = 1015] = "SAME_LEVEL_SECKILL";
    TCType[TCType["WHEEL_MUL"] = 1016] = "WHEEL_MUL";
    TCType[TCType["RECRUIT_PAWN_COUNT"] = 1017] = "RECRUIT_PAWN_COUNT";
    TCType[TCType["ARMY_COUNT"] = 1018] = "ARMY_COUNT";
    TCType[TCType["RECRUIT_PAWN_APPOINT"] = 1019] = "RECRUIT_PAWN_APPOINT";
    TCType[TCType["UPLV_PAWN_APPOINT"] = 1020] = "UPLV_PAWN_APPOINT";
    TCType[TCType["FORGE_EQUIP_APPOINT"] = 1021] = "FORGE_EQUIP_APPOINT";
    TCType[TCType["STUDY_TYPE_APPOINT"] = 1022] = "STUDY_TYPE_APPOINT";
    // SELECT_POLICY = 1023, //在内政选择任意一个政策 弃用
    TCType[TCType["ARMY_PAWN_COUNT"] = 1024] = "ARMY_PAWN_COUNT";
    TCType[TCType["BT_MAP_RES_BUILD"] = 1025] = "BT_MAP_RES_BUILD";
    TCType[TCType["BT_MAP_BUILD"] = 1026] = "BT_MAP_BUILD";
    TCType[TCType["UPLV_PAWN"] = 1027] = "UPLV_PAWN";
    TCType[TCType["RECHARGE_COUNT"] = 1028] = "RECHARGE_COUNT";
    TCType[TCType["NOT_GET_CRIT"] = 1029] = "NOT_GET_CRIT";
    TCType[TCType["RECRUIT_PAWN_TYPE"] = 1030] = "RECRUIT_PAWN_TYPE";
    TCType[TCType["RECRUIT_RANGED_PAWN"] = 1031] = "RECRUIT_RANGED_PAWN";
    TCType[TCType["OCCUPY_ANCIENT"] = 1032] = "OCCUPY_ANCIENT";
    TCType[TCType["PLAY_GAME_COUNT"] = 1033] = "PLAY_GAME_COUNT";
    TCType[TCType["WORSHIP_HERO"] = 1034] = "WORSHIP_HERO";
    TCType[TCType["FORGE_EXC_EQUIP"] = 1035] = "FORGE_EXC_EQUIP";
    TCType[TCType["KILL_ENEMY_COUNT"] = 1036] = "KILL_ENEMY_COUNT";
    TCType[TCType["ALL_CAPTURE_COUNT"] = 1037] = "ALL_CAPTURE_COUNT";
    TCType[TCType["SUM_TURNTABLE_COUNT"] = 1038] = "SUM_TURNTABLE_COUNT";
    TCType[TCType["FORGE_EQUIP"] = 1039] = "FORGE_EQUIP";
    TCType[TCType["WEAR_EQUIP"] = 1041] = "WEAR_EQUIP";
    TCType[TCType["SMELT_EQUIP"] = 1042] = "SMELT_EQUIP";
    //以下任务只用于新手村
    TCType[TCType["WEAR_EQUIP_ALL"] = 100001] = "WEAR_EQUIP_ALL";
    TCType[TCType["WEAR_EQUIP_DEF"] = 100002] = "WEAR_EQUIP_DEF";
})(TCType || (TCType = {}));
exports.TCType = TCType;
var TCusCType;
(function (TCusCType) {
    TCusCType[TCusCType["CHANGE_PAWN_EQUIP"] = 1001] = "CHANGE_PAWN_EQUIP";
    TCusCType[TCusCType["OPEN_TREASURE"] = 1002] = "OPEN_TREASURE";
    TCusCType[TCusCType["OCCUPY_ENEMY_LAND"] = 1003] = "OCCUPY_ENEMY_LAND";
    TCusCType[TCusCType["DESTROY_ENEMY_TOWER"] = 1004] = "DESTROY_ENEMY_TOWER";
    TCusCType[TCusCType["CAPTURE_ENEMY_CITY"] = 1005] = "CAPTURE_ENEMY_CITY";
    TCusCType[TCusCType["CURE_ONE_PAWN"] = 1006] = "CURE_ONE_PAWN";
})(TCusCType || (TCusCType = {}));
exports.TCusCType = TCusCType;
// 任务状态 0.未接取 1.未完成 2.可领奖 3.完成
var TaskState;
(function (TaskState) {
    TaskState[TaskState["NONE"] = 0] = "NONE";
    TaskState[TaskState["UNDONE"] = 1] = "UNDONE";
    TaskState[TaskState["CANGET"] = 2] = "CANGET";
    TaskState[TaskState["FINISH"] = 3] = "FINISH";
})(TaskState || (TaskState = {}));
exports.TaskState = TaskState;
// 通用效果类型
var CEffect;
(function (CEffect) {
    CEffect[CEffect["NONE"] = 0] = "NONE";
    CEffect[CEffect["BT_QUEUE"] = 1] = "BT_QUEUE";
    CEffect[CEffect["BUILD_CD"] = 2] = "BUILD_CD";
    CEffect[CEffect["GRANARY_CAP"] = 3] = "GRANARY_CAP";
    CEffect[CEffect["WAREHOUSE_CAP"] = 4] = "WAREHOUSE_CAP";
    CEffect[CEffect["XL_CD"] = 5] = "XL_CD";
    CEffect[CEffect["ALLIANCE_PERS"] = 6] = "ALLIANCE_PERS";
    CEffect[CEffect["MERCHANT_COUNT"] = 7] = "MERCHANT_COUNT";
    CEffect[CEffect["DRILL_QUEUE"] = 8] = "DRILL_QUEUE";
    CEffect[CEffect["WALL_HP"] = 9] = "WALL_HP";
    CEffect[CEffect["FORGE_CD"] = 10] = "FORGE_CD";
    CEffect[CEffect["ARMY_COUNT"] = 11] = "ARMY_COUNT";
    CEffect[CEffect["RES_OUTPUT"] = 12] = "RES_OUTPUT";
    CEffect[CEffect["MARCH_CD"] = 13] = "MARCH_CD";
    CEffect[CEffect["UPLVING_CD"] = 14] = "UPLVING_CD";
    CEffect[CEffect["GW_CAP"] = 15] = "GW_CAP";
    CEffect[CEffect["XL_2LV"] = 16] = "XL_2LV";
    CEffect[CEffect["TRANSIT_CD"] = 17] = "TRANSIT_CD";
    CEffect[CEffect["MAIN_MARCH_MUL"] = 18] = "MAIN_MARCH_MUL";
    CEffect[CEffect["CITY_BUILD_CD"] = 19] = "CITY_BUILD_CD";
    CEffect[CEffect["TREASURE_AWARD"] = 20] = "TREASURE_AWARD";
    CEffect[CEffect["FREE_RECAST"] = 21] = "FREE_RECAST";
    CEffect[CEffect["RARE_RES_OUTPUT"] = 22] = "RARE_RES_OUTPUT";
    CEffect[CEffect["MORE_RARE_RES"] = 23] = "MORE_RARE_RES";
    CEffect[CEffect["LV_UP_QUEUE"] = 24] = "LV_UP_QUEUE";
    CEffect[CEffect["TOWER_LV"] = 25] = "TOWER_LV";
    CEffect[CEffect["FARM_OUTPUT"] = 26] = "FARM_OUTPUT";
    CEffect[CEffect["QUARRY_OUTPUT"] = 27] = "QUARRY_OUTPUT";
    CEffect[CEffect["MILL_OUTPUT"] = 28] = "MILL_OUTPUT";
    CEffect[CEffect["CURE_QUEUE"] = 29] = "CURE_QUEUE";
    CEffect[CEffect["MARKET_SERVICE_CHARGE"] = 30] = "MARKET_SERVICE_CHARGE";
    CEffect[CEffect["CITY_COUNT_LIMIT"] = 31] = "CITY_COUNT_LIMIT";
    CEffect[CEffect["RECRUIT_COST"] = 33] = "RECRUIT_COST";
    CEffect[CEffect["UPLVING_COST"] = 34] = "UPLVING_COST";
    CEffect[CEffect["TOWER_HP"] = 35] = "TOWER_HP";
    CEffect[CEffect["OTHER_RES_ODDS"] = 36] = "OTHER_RES_ODDS";
    CEffect[CEffect["CURE_CD"] = 37] = "CURE_CD";
    CEffect[CEffect["DEFEND_HALO"] = 38] = "DEFEND_HALO";
    CEffect[CEffect["ATTACK_HALO"] = 39] = "ATTACK_HALO";
    CEffect[CEffect["ADD_MAX_HP"] = 40] = "ADD_MAX_HP";
    CEffect[CEffect["ADD_ATTACK"] = 41] = "ADD_ATTACK";
    CEffect[CEffect["LV_1_POWER"] = 42] = "LV_1_POWER";
    CEffect[CEffect["ALL_OCCUPY_REWARD"] = 43] = "ALL_OCCUPY_REWARD";
    CEffect[CEffect["OCCUPY_ROBBERY"] = 44] = "OCCUPY_ROBBERY";
    CEffect[CEffect["CELL_TONDEN_CD"] = 45] = "CELL_TONDEN_CD";
    CEffect[CEffect["CELL_TONDEN_TREASURE"] = 46] = "CELL_TONDEN_TREASURE";
    CEffect[CEffect["CURE_FREE_COUNT"] = 47] = "CURE_FREE_COUNT";
    CEffect[CEffect["FREE_RECAST_COUNT"] = 48] = "FREE_RECAST_COUNT";
    CEffect[CEffect["FREE_DRILL_COUNT"] = 49] = "FREE_DRILL_COUNT";
    CEffect[CEffect["FREE_LEVING_COUNT"] = 50] = "FREE_LEVING_COUNT";
    CEffect[CEffect["SEASON_ADD_SCROLLS"] = 51] = "SEASON_ADD_SCROLLS";
    CEffect[CEffect["SEASON_ADD_FIXATOR"] = 52] = "SEASON_ADD_FIXATOR";
    CEffect[CEffect["ADD_DMG_TO_MONSTER"] = 53] = "ADD_DMG_TO_MONSTER";
    CEffect[CEffect["ADD_DMG_TO_BUILD"] = 54] = "ADD_DMG_TO_BUILD";
    CEffect[CEffect["CIRCLE_OF_LIFE"] = 55] = "CIRCLE_OF_LIFE";
})(CEffect || (CEffect = {}));
exports.CEffect = CEffect;
// 拖拽触摸类型
var DragTouchType;
(function (DragTouchType) {
    DragTouchType[DragTouchType["NONE"] = 0] = "NONE";
    DragTouchType[DragTouchType["LONG_PRESS"] = 1] = "LONG_PRESS";
    DragTouchType[DragTouchType["DRAG_PRESS"] = 2] = "DRAG_PRESS";
    DragTouchType[DragTouchType["DRAG_MOVE"] = 3] = "DRAG_MOVE";
    DragTouchType[DragTouchType["CLICK"] = 4] = "CLICK";
})(DragTouchType || (DragTouchType = {}));
exports.DragTouchType = DragTouchType;
// 土地类型
var LandType;
(function (LandType) {
    LandType[LandType["NONE"] = 0] = "NONE";
    LandType[LandType["OBSTACLE"] = 1] = "OBSTACLE";
    LandType[LandType["PASS"] = 2] = "PASS";
    LandType[LandType["CEREAL"] = 3] = "CEREAL";
    LandType[LandType["TIMBER"] = 4] = "TIMBER";
    LandType[LandType["STONE"] = 5] = "STONE";
    LandType[LandType["MOUNTAIN"] = 6] = "MOUNTAIN";
    LandType[LandType["SEA"] = 7] = "SEA";
    LandType[LandType["LAKE"] = 8] = "LAKE";
    LandType[LandType["BEACH"] = 9] = "BEACH";
})(LandType || (LandType = {}));
exports.LandType = LandType;
// 地图装饰类型
var DecorationType;
(function (DecorationType) {
    DecorationType[DecorationType["NONE"] = 0] = "NONE";
    DecorationType[DecorationType["GRASS"] = 1] = "GRASS";
    DecorationType[DecorationType["MOUNTAIN"] = 2] = "MOUNTAIN";
    DecorationType[DecorationType["MUD"] = 3] = "MUD";
    DecorationType[DecorationType["Shadow"] = 4] = "Shadow";
    DecorationType[DecorationType["MAIN_CITY_ROUND_MOUNTAIN"] = 5] = "MAIN_CITY_ROUND_MOUNTAIN";
    DecorationType[DecorationType["MAIN_CITY_ROUND_LAKE"] = 6] = "MAIN_CITY_ROUND_LAKE";
    DecorationType[DecorationType["MUD_OUTER"] = 7] = "MUD_OUTER";
})(DecorationType || (DecorationType = {}));
exports.DecorationType = DecorationType;
// 地图装饰ID
var DecorationID;
(function (DecorationID) {
    DecorationID[DecorationID["NONE"] = 0] = "NONE";
    DecorationID[DecorationID["MUD"] = 5] = "MUD";
    DecorationID[DecorationID["MUD_OUTER"] = 101] = "MUD_OUTER";
})(DecorationID || (DecorationID = {}));
exports.DecorationID = DecorationID;
// 士兵类型
var PawnType;
(function (PawnType) {
    PawnType[PawnType["NONE"] = 0] = "NONE";
    PawnType[PawnType["PIKEMAN"] = 1] = "PIKEMAN";
    PawnType[PawnType["PELTAST"] = 2] = "PELTAST";
    PawnType[PawnType["ARCHER"] = 3] = "ARCHER";
    PawnType[PawnType["SOWAR"] = 4] = "SOWAR";
    PawnType[PawnType["MACHINE"] = 5] = "MACHINE";
    PawnType[PawnType["BEAST"] = 6] = "BEAST";
    PawnType[PawnType["CATERAN"] = 7] = "CATERAN";
    PawnType[PawnType["BUILD"] = 100] = "BUILD";
    PawnType[PawnType["TOWER"] = 101] = "TOWER";
    PawnType[PawnType["NONCOMBAT"] = 102] = "NONCOMBAT";
})(PawnType || (PawnType = {}));
exports.PawnType = PawnType;
// 军队状态
var ArmyState;
(function (ArmyState) {
    ArmyState[ArmyState["NONE"] = 0] = "NONE";
    ArmyState[ArmyState["MARCH"] = 1] = "MARCH";
    ArmyState[ArmyState["FIGHT"] = 2] = "FIGHT";
    ArmyState[ArmyState["DRILL"] = 3] = "DRILL";
    ArmyState[ArmyState["LVING"] = 4] = "LVING";
    ArmyState[ArmyState["TONDEN"] = 5] = "TONDEN";
    ArmyState[ArmyState["CURING"] = 6] = "CURING";
})(ArmyState || (ArmyState = {}));
exports.ArmyState = ArmyState;
// 士兵状态
var PawnState;
(function (PawnState) {
    PawnState[PawnState["NONE"] = 0] = "NONE";
    PawnState[PawnState["EDIT_MOVE"] = 1] = "EDIT_MOVE";
    PawnState[PawnState["STAND"] = 2] = "STAND";
    PawnState[PawnState["MOVE"] = 3] = "MOVE";
    PawnState[PawnState["ATTACK"] = 4] = "ATTACK";
    PawnState[PawnState["HIT"] = 5] = "HIT";
    PawnState[PawnState["DIAUP"] = 6] = "DIAUP";
    PawnState[PawnState["HEAL"] = 7] = "HEAL";
    PawnState[PawnState["DEDUCT_HP"] = 8] = "DEDUCT_HP";
    PawnState[PawnState["ADD_ANGER"] = 9] = "ADD_ANGER";
    PawnState[PawnState["FEAR"] = 10] = "FEAR";
    PawnState[PawnState["DIE"] = 11] = "DIE";
    PawnState[PawnState["SKILL"] = 100] = "SKILL";
    PawnState[PawnState["SKILL_MAX"] = 110] = "SKILL_MAX";
    PawnState[PawnState["MOVE_ATTACK"] = 200] = "MOVE_ATTACK";
    PawnState[PawnState["MOVE_ATTACK_MAX"] = 210] = "MOVE_ATTACK_MAX";
})(PawnState || (PawnState = {}));
exports.PawnState = PawnState;
// 商人状态
var MerchantState;
(function (MerchantState) {
    MerchantState[MerchantState["NONE"] = 0] = "NONE";
    MerchantState[MerchantState["TRADING"] = 1] = "TRADING";
    MerchantState[MerchantState["TRANSIT"] = 2] = "TRANSIT";
})(MerchantState || (MerchantState = {}));
exports.MerchantState = MerchantState;
// 行军线类型
var MarchLineType;
(function (MarchLineType) {
    MarchLineType[MarchLineType["MERCHANT"] = 0] = "MERCHANT";
    MarchLineType[MarchLineType["SELF_ARMY"] = 1] = "SELF_ARMY";
    MarchLineType[MarchLineType["OTHER_ARMY"] = 2] = "OTHER_ARMY";
    MarchLineType[MarchLineType["ALLI_ARMY"] = 3] = "ALLI_ARMY";
})(MarchLineType || (MarchLineType = {}));
exports.MarchLineType = MarchLineType;
// 行军目标类型
var MarchTargetType;
(function (MarchTargetType) {
    MarchTargetType[MarchTargetType["NONE"] = 0] = "NONE";
    MarchTargetType[MarchTargetType["STRIKE"] = 1] = "STRIKE";
    MarchTargetType[MarchTargetType["ATTACK"] = 2] = "ATTACK";
    MarchTargetType[MarchTargetType["MOVE"] = 3] = "MOVE";
    MarchTargetType[MarchTargetType["REVOKE"] = 4] = "REVOKE";
    MarchTargetType[MarchTargetType["AUTO_REVOKE"] = 5] = "AUTO_REVOKE";
    MarchTargetType[MarchTargetType["FORCE_REVOKE"] = 6] = "FORCE_REVOKE";
    MarchTargetType[MarchTargetType["TONDEN"] = 7] = "TONDEN";
})(MarchTargetType || (MarchTargetType = {}));
exports.MarchTargetType = MarchTargetType;
// 运行目标类型
var TransitTargetType;
(function (TransitTargetType) {
    TransitTargetType[TransitTargetType["NONE"] = 0] = "NONE";
    TransitTargetType[TransitTargetType["SHIPPED"] = 1] = "SHIPPED";
    TransitTargetType[TransitTargetType["SHIP_OUT"] = 2] = "SHIP_OUT";
    TransitTargetType[TransitTargetType["BACK"] = 3] = "BACK";
})(TransitTargetType || (TransitTargetType = {}));
exports.TransitTargetType = TransitTargetType;
// 显示士兵信息类型
var PawnInfoType;
(function (PawnInfoType) {
    PawnInfoType[PawnInfoType["NONE"] = 0] = "NONE";
    PawnInfoType[PawnInfoType["DRILL"] = 1] = "DRILL";
})(PawnInfoType || (PawnInfoType = {}));
exports.PawnInfoType = PawnInfoType;
// 士兵技能类型
var PawnSkillType;
(function (PawnSkillType) {
    PawnSkillType[PawnSkillType["NONE"] = 0] = "NONE";
    PawnSkillType[PawnSkillType["ATTACK_RESTRAIN"] = 1] = "ATTACK_RESTRAIN";
    PawnSkillType[PawnSkillType["DEFENSE_RESTRAIN"] = 2] = "DEFENSE_RESTRAIN";
    PawnSkillType[PawnSkillType["RESTRAIN"] = 3] = "RESTRAIN";
    PawnSkillType[PawnSkillType["REDUCTION_RANGED"] = 103] = "REDUCTION_RANGED";
    PawnSkillType[PawnSkillType["RESTRAIN_BEAST"] = 104] = "RESTRAIN_BEAST";
    PawnSkillType[PawnSkillType["THE_INJURY"] = 105] = "THE_INJURY";
    PawnSkillType[PawnSkillType["REDUCTION_DMG"] = 106] = "REDUCTION_DMG";
    PawnSkillType[PawnSkillType["EVADE"] = 107] = "EVADE";
    PawnSkillType[PawnSkillType["INSTABILITY_ATTACK"] = 108] = "INSTABILITY_ATTACK";
    PawnSkillType[PawnSkillType["CRIT"] = 109] = "CRIT";
    PawnSkillType[PawnSkillType["PEOPLE_BROKEN"] = 110] = "PEOPLE_BROKEN";
    PawnSkillType[PawnSkillType["PULL_STRING"] = 111] = "PULL_STRING";
    PawnSkillType[PawnSkillType["FULL_STRING"] = 112] = "FULL_STRING";
    PawnSkillType[PawnSkillType["THE_DODGE"] = 113] = "THE_DODGE";
    PawnSkillType[PawnSkillType["CADET"] = 114] = "CADET";
    PawnSkillType[PawnSkillType["TELSON"] = 115] = "TELSON";
    PawnSkillType[PawnSkillType["SHIELD_CRUSHING"] = 116] = "SHIELD_CRUSHING";
    PawnSkillType[PawnSkillType["LONGITUDINAL_CLEFT"] = 117] = "LONGITUDINAL_CLEFT";
    PawnSkillType[PawnSkillType["FIRE"] = 118] = "FIRE";
    PawnSkillType[PawnSkillType["SHIELD_BASH"] = 119] = "SHIELD_BASH";
    PawnSkillType[PawnSkillType["STILL_MOUNTAIN"] = 120] = "STILL_MOUNTAIN";
    PawnSkillType[PawnSkillType["INFECTION_PLAGUE"] = 121] = "INFECTION_PLAGUE";
    PawnSkillType[PawnSkillType["SKILL_201"] = 201] = "SKILL_201";
    PawnSkillType[PawnSkillType["SKILL_202"] = 202] = "SKILL_202";
    PawnSkillType[PawnSkillType["SKILL_203"] = 203] = "SKILL_203";
    PawnSkillType[PawnSkillType["SKILL_204"] = 204] = "SKILL_204";
    PawnSkillType[PawnSkillType["SKILL_205"] = 205] = "SKILL_205";
    PawnSkillType[PawnSkillType["SKILL_206"] = 206] = "SKILL_206";
    PawnSkillType[PawnSkillType["SKILL_207"] = 207] = "SKILL_207";
    PawnSkillType[PawnSkillType["SKILL_208"] = 208] = "SKILL_208";
    PawnSkillType[PawnSkillType["SKILL_209"] = 209] = "SKILL_209";
    PawnSkillType[PawnSkillType["SKILL_210"] = 210] = "SKILL_210";
    PawnSkillType[PawnSkillType["SKILL_211"] = 211] = "SKILL_211";
    PawnSkillType[PawnSkillType["SKILL_212"] = 212] = "SKILL_212";
    PawnSkillType[PawnSkillType["SKILL_213"] = 213] = "SKILL_213";
    PawnSkillType[PawnSkillType["SKILL_214"] = 214] = "SKILL_214";
    PawnSkillType[PawnSkillType["SKILL_215"] = 215] = "SKILL_215";
    PawnSkillType[PawnSkillType["SKILL_216"] = 216] = "SKILL_216";
    PawnSkillType[PawnSkillType["SKILL_217"] = 217] = "SKILL_217";
    PawnSkillType[PawnSkillType["SKILL_218"] = 218] = "SKILL_218";
    PawnSkillType[PawnSkillType["SKILL_219"] = 219] = "SKILL_219";
    PawnSkillType[PawnSkillType["SKILL_220"] = 220] = "SKILL_220";
    PawnSkillType[PawnSkillType["SKILL_221"] = 221] = "SKILL_221";
    PawnSkillType[PawnSkillType["SKILL_301"] = 301] = "SKILL_301";
    PawnSkillType[PawnSkillType["SKILL_302"] = 302] = "SKILL_302";
    PawnSkillType[PawnSkillType["SKILL_303"] = 303] = "SKILL_303";
    PawnSkillType[PawnSkillType["SKILL_304"] = 304] = "SKILL_304";
    PawnSkillType[PawnSkillType["SKILL_305"] = 305] = "SKILL_305";
    PawnSkillType[PawnSkillType["SKILL_306"] = 306] = "SKILL_306";
    PawnSkillType[PawnSkillType["SKILL_307"] = 307] = "SKILL_307";
    PawnSkillType[PawnSkillType["SKILL_308"] = 308] = "SKILL_308";
    PawnSkillType[PawnSkillType["SKILL_309"] = 309] = "SKILL_309";
    PawnSkillType[PawnSkillType["SKILL_310"] = 310] = "SKILL_310";
})(PawnSkillType || (PawnSkillType = {}));
exports.PawnSkillType = PawnSkillType;
// buff类型
var BuffType;
(function (BuffType) {
    BuffType[BuffType["NONE"] = 0] = "NONE";
    BuffType[BuffType["ARMOR_PENETRATION"] = 1] = "ARMOR_PENETRATION";
    BuffType[BuffType["SERIOUS_INJURY"] = 2] = "SERIOUS_INJURY";
    BuffType[BuffType["SHIELD"] = 3] = "SHIELD";
    BuffType[BuffType["STAND_SHIELD"] = 4] = "STAND_SHIELD";
    BuffType[BuffType["DIZZINESS"] = 5] = "DIZZINESS";
    BuffType[BuffType["PARALYSIS_UP"] = 6] = "PARALYSIS_UP";
    BuffType[BuffType["DESTROY_WEAPONS"] = 7] = "DESTROY_WEAPONS";
    BuffType[BuffType["POISONING_MAX_HP"] = 8] = "POISONING_MAX_HP";
    BuffType[BuffType["PARALYSIS"] = 9] = "PARALYSIS";
    BuffType[BuffType["DAMAGE_SUPERPOSITION"] = 10] = "DAMAGE_SUPERPOSITION";
    BuffType[BuffType["PARRY"] = 11] = "PARRY";
    BuffType[BuffType["INSPIRE"] = 12] = "INSPIRE";
    BuffType[BuffType["CHECK_LOW_HP"] = 13] = "CHECK_LOW_HP";
    BuffType[BuffType["LOW_HP_SHIELD"] = 14] = "LOW_HP_SHIELD";
    BuffType[BuffType["ATTACK_SHIELD"] = 15] = "ATTACK_SHIELD";
    BuffType[BuffType["HIT_SUCK_BLOOD"] = 16] = "HIT_SUCK_BLOOD";
    BuffType[BuffType["POISONING_CUR_HP"] = 17] = "POISONING_CUR_HP";
    BuffType[BuffType["SILENCE"] = 18] = "SILENCE";
    BuffType[BuffType["MORALE"] = 19] = "MORALE";
    BuffType[BuffType["VALOR"] = 20] = "VALOR";
    BuffType[BuffType["BLEED"] = 21] = "BLEED";
    BuffType[BuffType["DODGE"] = 22] = "DODGE";
    BuffType[BuffType["SUCKBLOOD_SHIELD"] = 23] = "SUCKBLOOD_SHIELD";
    BuffType[BuffType["ADD_EXECUTE_ATTACK"] = 24] = "ADD_EXECUTE_ATTACK";
    BuffType[BuffType["DAMAGE_DECREASE"] = 25] = "DAMAGE_DECREASE";
    BuffType[BuffType["RODELERO_SHIELD"] = 26] = "RODELERO_SHIELD";
    BuffType[BuffType["RODELERO_ADD_ATTACK"] = 27] = "RODELERO_ADD_ATTACK";
    BuffType[BuffType["DAMAGE_INCREASE"] = 28] = "DAMAGE_INCREASE";
    BuffType[BuffType["DAMAGE_REDUCE"] = 29] = "DAMAGE_REDUCE";
    BuffType[BuffType["DELAY_DEDUCT_HP"] = 30] = "DELAY_DEDUCT_HP";
    BuffType[BuffType["BATTLE_BEGIN_SHIELD"] = 31] = "BATTLE_BEGIN_SHIELD";
    BuffType[BuffType["CHECK_LOW_HP_ATTACK"] = 32] = "CHECK_LOW_HP_ATTACK";
    BuffType[BuffType["LOW_HP_ADD_ATTACK"] = 33] = "LOW_HP_ADD_ATTACK";
    BuffType[BuffType["LOW_HP_ADD_SUCKBLOOD"] = 34] = "LOW_HP_ADD_SUCKBLOOD";
    BuffType[BuffType["FRACTURE"] = 35] = "FRACTURE";
    BuffType[BuffType["STEADY_ATTACK"] = 36] = "STEADY_ATTACK";
    BuffType[BuffType["TURNTHEBLADE"] = 37] = "TURNTHEBLADE";
    BuffType[BuffType["WISDOM_COURAGE"] = 38] = "WISDOM_COURAGE";
    BuffType[BuffType["FEAR"] = 39] = "FEAR";
    BuffType[BuffType["GOD_WAR"] = 40] = "GOD_WAR";
    BuffType[BuffType["PROTECTION_SHIELD"] = 41] = "PROTECTION_SHIELD";
    BuffType[BuffType["PROTECTION_NIE"] = 42] = "PROTECTION_NIE";
    BuffType[BuffType["TOUGH"] = 43] = "TOUGH";
    BuffType[BuffType["CONTINUE_ACTION"] = 44] = "CONTINUE_ACTION";
    BuffType[BuffType["WITHSTAND"] = 45] = "WITHSTAND";
    BuffType[BuffType["TIMIDITY"] = 46] = "TIMIDITY";
    BuffType[BuffType["CHECK_KUROU"] = 47] = "CHECK_KUROU";
    BuffType[BuffType["KUROU_SHIELD"] = 48] = "KUROU_SHIELD";
    BuffType[BuffType["KUROU_ADD_ATTACK"] = 49] = "KUROU_ADD_ATTACK";
    BuffType[BuffType["WOOSUNG"] = 50] = "WOOSUNG";
    BuffType[BuffType["ASSAULT"] = 51] = "ASSAULT";
    BuffType[BuffType["CHAOS"] = 52] = "CHAOS";
    BuffType[BuffType["TIGER_MANIA"] = 53] = "TIGER_MANIA";
    BuffType[BuffType["LONG_RANGE_RAID"] = 54] = "LONG_RANGE_RAID";
    BuffType[BuffType["IRREMOVABILITY"] = 55] = "IRREMOVABILITY";
    BuffType[BuffType["SUCK_SHIELD"] = 56] = "SUCK_SHIELD";
    BuffType[BuffType["KILL_ADD_ATTACK"] = 57] = "KILL_ADD_ATTACK";
    BuffType[BuffType["LOW_HP_ADD_ATTACK_S"] = 58] = "LOW_HP_ADD_ATTACK_S";
    BuffType[BuffType["DDIE_ADD_ATTACK"] = 59] = "DDIE_ADD_ATTACK";
    BuffType[BuffType["CHECK_JADE_PENDANT"] = 60] = "CHECK_JADE_PENDANT";
    BuffType[BuffType["ABNEGATION_SHIELD"] = 61] = "ABNEGATION_SHIELD";
    BuffType[BuffType["ROYAL_BLUE_DODGE"] = 62] = "ROYAL_BLUE_DODGE";
    BuffType[BuffType["ROYAL_BLUE_DAMAGE"] = 63] = "ROYAL_BLUE_DAMAGE";
    BuffType[BuffType["CHECK_ABNEGATION"] = 64] = "CHECK_ABNEGATION";
    BuffType[BuffType["AROA"] = 65] = "AROA";
    BuffType[BuffType["NAKED_CLOTHES"] = 66] = "NAKED_CLOTHES";
    BuffType[BuffType["DYING_RECOVER"] = 67] = "DYING_RECOVER";
    BuffType[BuffType["BEHEADED_GENERAL"] = 68] = "BEHEADED_GENERAL";
    BuffType[BuffType["WIRE_CHAIN"] = 69] = "WIRE_CHAIN";
    BuffType[BuffType["OVERLORD"] = 70] = "OVERLORD";
    BuffType[BuffType["BARB"] = 71] = "BARB";
    BuffType[BuffType["FEED_INTENSIFY_RECORD"] = 72] = "FEED_INTENSIFY_RECORD";
    BuffType[BuffType["FEED_INTENSIFY"] = 73] = "FEED_INTENSIFY";
    BuffType[BuffType["WORTHY_MONARCH"] = 74] = "WORTHY_MONARCH";
    BuffType[BuffType["PRESTAGE"] = 75] = "PRESTAGE";
    BuffType[BuffType["THOUSAND_UMBRELLA"] = 76] = "THOUSAND_UMBRELLA";
    BuffType[BuffType["CHECK_CENTERING_HELMET"] = 77] = "CHECK_CENTERING_HELMET";
    BuffType[BuffType["BREAK_ENEMY_RANKS"] = 78] = "BREAK_ENEMY_RANKS";
    BuffType[BuffType["CHECK_LITTLE_GIRL"] = 79] = "CHECK_LITTLE_GIRL";
    BuffType[BuffType["RESOLUTE"] = 80] = "RESOLUTE";
    BuffType[BuffType["POISONED_WINE"] = 81] = "POISONED_WINE";
    BuffType[BuffType["THREE_AXES"] = 82] = "THREE_AXES";
    BuffType[BuffType["TONDEN_BEGIN"] = 83] = "TONDEN_BEGIN";
    BuffType[BuffType["CHECK_TONDEN"] = 84] = "CHECK_TONDEN";
    BuffType[BuffType["LONGITUDINAL_CLEFT_SHIELD"] = 85] = "LONGITUDINAL_CLEFT_SHIELD";
    BuffType[BuffType["TONDEN_RECOVER"] = 86] = "TONDEN_RECOVER";
    BuffType[BuffType["LIAN_PO_ATTACK"] = 87] = "LIAN_PO_ATTACK";
    BuffType[BuffType["LIAN_PO_DEFEND"] = 88] = "LIAN_PO_DEFEND";
    BuffType[BuffType["TYRANNICAL"] = 89] = "TYRANNICAL";
    BuffType[BuffType["COURAGEOUSLY"] = 90] = "COURAGEOUSLY";
    BuffType[BuffType["ARBATOR_ATTACK_COUNT"] = 91] = "ARBATOR_ATTACK_COUNT";
    BuffType[BuffType["CHECK_JUMPSLASH_ADD_ANGER"] = 92] = "CHECK_JUMPSLASH_ADD_ANGER";
    BuffType[BuffType["JUMPSLASH_DAMAGE"] = 93] = "JUMPSLASH_DAMAGE";
    BuffType[BuffType["CHECK_CRIMSONGOLD_SHIELD"] = 94] = "CHECK_CRIMSONGOLD_SHIELD";
    BuffType[BuffType["CRIMSONGOLD_SHIELD"] = 95] = "CRIMSONGOLD_SHIELD";
    BuffType[BuffType["IGNITION"] = 96] = "IGNITION";
    BuffType[BuffType["RECURRENCE"] = 97] = "RECURRENCE";
    BuffType[BuffType["PENETRATE"] = 98] = "PENETRATE";
    BuffType[BuffType["THUNDERS_DEFENSE"] = 99] = "THUNDERS_DEFENSE";
    BuffType[BuffType["THUNDERS_RECOVER"] = 100] = "THUNDERS_RECOVER";
    BuffType[BuffType["OBSIDIAN_ARMOR_DEFENSE"] = 101] = "OBSIDIAN_ARMOR_DEFENSE";
    BuffType[BuffType["OBSIDIAN_ARMOR_NEGATIVE"] = 102] = "OBSIDIAN_ARMOR_NEGATIVE";
    BuffType[BuffType["BLACK_IRON_STAFF_SHIELD"] = 103] = "BLACK_IRON_STAFF_SHIELD";
    BuffType[BuffType["BLACK_IRON_STAFF_MARK"] = 104] = "BLACK_IRON_STAFF_MARK";
    BuffType[BuffType["CHECK_SILVER_SNAKE_WHIP"] = 105] = "CHECK_SILVER_SNAKE_WHIP";
    BuffType[BuffType["HAUBERK_DEFENSE"] = 106] = "HAUBERK_DEFENSE";
    BuffType[BuffType["LONGYUAN_SWORD_ATTACK"] = 107] = "LONGYUAN_SWORD_ATTACK";
    BuffType[BuffType["INFECTION_PLAGUE"] = 108] = "INFECTION_PLAGUE";
    BuffType[BuffType["RAGE"] = 109] = "RAGE";
    BuffType[BuffType["RECORD_DIZZINESS_ROUND"] = 110] = "RECORD_DIZZINESS_ROUND";
    BuffType[BuffType["RECORD_PAWSTRIKE"] = 111] = "RECORD_PAWSTRIKE";
    BuffType[BuffType["KERIAN"] = 112] = "KERIAN";
    BuffType[BuffType["SORROWFUL_DREAM"] = 113] = "SORROWFUL_DREAM";
    BuffType[BuffType["RECORD_ROUND_ADD_DAMAGE"] = 114] = "RECORD_ROUND_ADD_DAMAGE";
    BuffType[BuffType["ANTICIPATION_DEFENSE"] = 115] = "ANTICIPATION_DEFENSE";
    BuffType[BuffType["ANTICIPATION_ATTACK"] = 116] = "ANTICIPATION_ATTACK";
    BuffType[BuffType["S_RECORD_HP"] = 1001] = "S_RECORD_HP";
    BuffType[BuffType["S_RECORD_LOW_RECOVER_HP"] = 1003] = "S_RECORD_LOW_RECOVER_HP";
    BuffType[BuffType["DEFEND_HALO"] = 1026] = "DEFEND_HALO";
    BuffType[BuffType["ATTACK_HALO"] = 1027] = "ATTACK_HALO";
    BuffType[BuffType["ADD_MAX_HP"] = 1028] = "ADD_MAX_HP";
    BuffType[BuffType["ADD_ATTACK"] = 1029] = "ADD_ATTACK";
    BuffType[BuffType["LV_1_POWER"] = 1030] = "LV_1_POWER";
    BuffType[BuffType["ADD_DMG_TO_MONSTER"] = 1042] = "ADD_DMG_TO_MONSTER";
    BuffType[BuffType["ADD_DMG_TO_BUILD"] = 1043] = "ADD_DMG_TO_BUILD";
    BuffType[BuffType["CIRCLE_OF_LIFE"] = 1044] = "CIRCLE_OF_LIFE";
    BuffType[BuffType["ENVIRONMENT_BUILD_DMG"] = 2001] = "ENVIRONMENT_BUILD_DMG";
    BuffType[BuffType["RODELERO_SHIELD_001"] = 26001] = "RODELERO_SHIELD_001";
    BuffType[BuffType["RODELERO_SHIELD_102"] = 26102] = "RODELERO_SHIELD_102";
    BuffType[BuffType["PARRY_001"] = 11001] = "PARRY_001";
    BuffType[BuffType["PARRY_102"] = 11102] = "PARRY_102";
    BuffType[BuffType["INSPIRE_001"] = 12001] = "INSPIRE_001";
})(BuffType || (BuffType = {}));
exports.BuffType = BuffType;
// 联盟职位
var AllianceJobType;
(function (AllianceJobType) {
    AllianceJobType[AllianceJobType["CREATER"] = 0] = "CREATER";
    AllianceJobType[AllianceJobType["CREATER_VICE"] = 1] = "CREATER_VICE";
    AllianceJobType[AllianceJobType["MILITARY"] = 2] = "MILITARY";
    AllianceJobType[AllianceJobType["MEMBER"] = 10] = "MEMBER";
})(AllianceJobType || (AllianceJobType = {}));
exports.AllianceJobType = AllianceJobType;
// 邮件状态
var MailStateType;
(function (MailStateType) {
    MailStateType[MailStateType["NONE"] = 0] = "NONE";
    MailStateType[MailStateType["NOT_CLAIM"] = 1] = "NOT_CLAIM";
    MailStateType[MailStateType["READ"] = 2] = "READ";
})(MailStateType || (MailStateType = {}));
exports.MailStateType = MailStateType;
// 装备效果类型
var EquipEffectType;
(function (EquipEffectType) {
    EquipEffectType[EquipEffectType["NONE"] = 0] = "NONE";
    EquipEffectType[EquipEffectType["BLOOD_RETURN"] = 1] = "BLOOD_RETURN";
    EquipEffectType[EquipEffectType["REDUCTION_INJURY"] = 2] = "REDUCTION_INJURY";
    EquipEffectType[EquipEffectType["CRIT"] = 3] = "CRIT";
    EquipEffectType[EquipEffectType["BEGIN_BLOOD"] = 4] = "BEGIN_BLOOD";
    EquipEffectType[EquipEffectType["SUCK_BLOOD"] = 5] = "SUCK_BLOOD";
    EquipEffectType[EquipEffectType["THE_INJURY"] = 6] = "THE_INJURY";
    EquipEffectType[EquipEffectType["INFALLIBLE"] = 7] = "INFALLIBLE";
    EquipEffectType[EquipEffectType["DODGE"] = 8] = "DODGE";
    EquipEffectType[EquipEffectType["EXECUTE"] = 9] = "EXECUTE";
    EquipEffectType[EquipEffectType["FIXED_DAMAGE"] = 10] = "FIXED_DAMAGE";
    EquipEffectType[EquipEffectType["NOT_DODGE"] = 11] = "NOT_DODGE";
    EquipEffectType[EquipEffectType["REDUCTION_RANGED"] = 12] = "REDUCTION_RANGED";
    EquipEffectType[EquipEffectType["CONTINUE_ACTION"] = 13] = "CONTINUE_ACTION";
    EquipEffectType[EquipEffectType["CUR_HP_DAMAGE"] = 14] = "CUR_HP_DAMAGE";
    EquipEffectType[EquipEffectType["RECOVER_ANGER"] = 15] = "RECOVER_ANGER";
    EquipEffectType[EquipEffectType["LOW_HP_SHIELD"] = 16] = "LOW_HP_SHIELD";
    EquipEffectType[EquipEffectType["MAX_HP_TRUE_DMG"] = 17] = "MAX_HP_TRUE_DMG";
    EquipEffectType[EquipEffectType["SAME_CORPS_REDUCE_DMG"] = 18] = "SAME_CORPS_REDUCE_DMG";
    EquipEffectType[EquipEffectType["ATTACK_GET_SHIELD"] = 19] = "ATTACK_GET_SHIELD";
    EquipEffectType[EquipEffectType["HIT_GET_SUCK_BLOOD"] = 20] = "HIT_GET_SUCK_BLOOD";
    EquipEffectType[EquipEffectType["_ADD_BASE_ATTACK"] = 21] = "_ADD_BASE_ATTACK";
    EquipEffectType[EquipEffectType["_ADD_BASE_HP"] = 22] = "_ADD_BASE_HP";
    EquipEffectType[EquipEffectType["_ADD_SHIELD_EFFECT"] = 23] = "_ADD_SHIELD_EFFECT";
    EquipEffectType[EquipEffectType["_ADD_RUSH_DAMAGE"] = 24] = "_ADD_RUSH_DAMAGE";
    EquipEffectType[EquipEffectType["_ADD_DASH_DAMAGE"] = 25] = "_ADD_DASH_DAMAGE";
    EquipEffectType[EquipEffectType["REDUCTION_MELEE"] = 26] = "REDUCTION_MELEE";
    EquipEffectType[EquipEffectType["GEN_FLAG_BY_DIE"] = 27] = "GEN_FLAG_BY_DIE";
    EquipEffectType[EquipEffectType["ADD_MOVE_RANGE"] = 28] = "ADD_MOVE_RANGE";
    EquipEffectType[EquipEffectType["_THE_INJURY_RANGED"] = 29] = "_THE_INJURY_RANGED";
    EquipEffectType[EquipEffectType["_THE_INJURY_MELEE"] = 30] = "_THE_INJURY_MELEE";
    EquipEffectType[EquipEffectType["RANGE_ATTACK"] = 31] = "RANGE_ATTACK";
    EquipEffectType[EquipEffectType["TODAY_ADD_HP"] = 32] = "TODAY_ADD_HP";
    EquipEffectType[EquipEffectType["TODAY_ADD_ATTACK"] = 33] = "TODAY_ADD_ATTACK";
    EquipEffectType[EquipEffectType["LOW_HP_ATTACk"] = 34] = "LOW_HP_ATTACk";
    EquipEffectType[EquipEffectType["BATTLE_BEGIN_SHIELD"] = 35] = "BATTLE_BEGIN_SHIELD";
    EquipEffectType[EquipEffectType["DOUBLE_EDGED_SWORD"] = 36] = "DOUBLE_EDGED_SWORD";
    EquipEffectType[EquipEffectType["FLAMING_ARMOR"] = 37] = "FLAMING_ARMOR";
    EquipEffectType[EquipEffectType["THOUSAND_UMBRELLA"] = 38] = "THOUSAND_UMBRELLA";
    EquipEffectType[EquipEffectType["CENTERING_HELMET"] = 39] = "CENTERING_HELMET";
    EquipEffectType[EquipEffectType["DIZZY_HEART_DROP"] = 40] = "DIZZY_HEART_DROP";
    EquipEffectType[EquipEffectType["CRIMSONGOLD_SHIELD"] = 41] = "CRIMSONGOLD_SHIELD";
    EquipEffectType[EquipEffectType["BURNING_HEART_RING"] = 42] = "BURNING_HEART_RING";
    EquipEffectType[EquipEffectType["BILLHOOK"] = 43] = "BILLHOOK";
    EquipEffectType[EquipEffectType["BELT_BLOODRAGE"] = 44] = "BELT_BLOODRAGE";
    EquipEffectType[EquipEffectType["OBSIDIAN_ARMOR"] = 45] = "OBSIDIAN_ARMOR";
    EquipEffectType[EquipEffectType["BLACK_IRON_STAFF"] = 46] = "BLACK_IRON_STAFF";
    EquipEffectType[EquipEffectType["SPIKY_BALL"] = 47] = "SPIKY_BALL";
    EquipEffectType[EquipEffectType["SILVER_SNAKE_WHIP"] = 48] = "SILVER_SNAKE_WHIP";
    EquipEffectType[EquipEffectType["LONGYUAN_SWORD"] = 49] = "LONGYUAN_SWORD";
    EquipEffectType[EquipEffectType["MINGGUANG_ARMOR"] = 50] = "MINGGUANG_ARMOR";
    EquipEffectType[EquipEffectType["BAIBI_SWORD"] = 51] = "BAIBI_SWORD";
    EquipEffectType[EquipEffectType["ADD_BASE_ATTACK_1"] = 2101] = "ADD_BASE_ATTACK_1";
    EquipEffectType[EquipEffectType["ADD_BASE_ATTACK_2"] = 2102] = "ADD_BASE_ATTACK_2";
    EquipEffectType[EquipEffectType["ADD_BASE_ATTACK_3"] = 2103] = "ADD_BASE_ATTACK_3";
    EquipEffectType[EquipEffectType["ADD_BASE_ATTACK_4"] = 2104] = "ADD_BASE_ATTACK_4";
    EquipEffectType[EquipEffectType["ADD_BASE_HP_1"] = 2201] = "ADD_BASE_HP_1";
    EquipEffectType[EquipEffectType["ADD_BASE_HP_2"] = 2202] = "ADD_BASE_HP_2";
    EquipEffectType[EquipEffectType["ADD_BASE_HP_3"] = 2203] = "ADD_BASE_HP_3";
    EquipEffectType[EquipEffectType["ADD_BASE_HP_4"] = 2204] = "ADD_BASE_HP_4";
})(EquipEffectType || (EquipEffectType = {}));
exports.EquipEffectType = EquipEffectType;
// 英雄
var HeroType;
(function (HeroType) {
    HeroType[HeroType["NONE"] = 0] = "NONE";
    HeroType[HeroType["CHEN_DAO"] = 1] = "CHEN_DAO";
    HeroType[HeroType["ZHAO_YUN"] = 2] = "ZHAO_YUN";
    HeroType[HeroType["ZHANG_HE"] = 3] = "ZHANG_HE";
    HeroType[HeroType["QIN_LIANGYU"] = 4] = "QIN_LIANGYU";
    HeroType[HeroType["BAI_QI"] = 5] = "BAI_QI";
    HeroType[HeroType["ZHOU_YU"] = 6] = "ZHOU_YU";
    HeroType[HeroType["LI_SIYE"] = 7] = "LI_SIYE";
    HeroType[HeroType["YU_JIN"] = 8] = "YU_JIN";
    HeroType[HeroType["LIAO_HUA"] = 9] = "LIAO_HUA";
    HeroType[HeroType["JIANG_WEI"] = 10] = "JIANG_WEI";
    HeroType[HeroType["WEN_YUAN"] = 11] = "WEN_YUAN";
    HeroType[HeroType["DIAN_WEI"] = 12] = "DIAN_WEI";
    HeroType[HeroType["XU_SHENG"] = 13] = "XU_SHENG";
    HeroType[HeroType["YUE_JIN"] = 14] = "YUE_JIN";
    HeroType[HeroType["SUN_JIAN"] = 15] = "SUN_JIAN";
    HeroType[HeroType["CAO_REN"] = 16] = "CAO_REN";
    HeroType[HeroType["ZHOU_TAI"] = 17] = "ZHOU_TAI";
    HeroType[HeroType["ZHANG_FEI"] = 18] = "ZHANG_FEI";
    HeroType[HeroType["GAO_SHUN"] = 19] = "GAO_SHUN";
    HeroType[HeroType["HUANG_GAI"] = 20] = "HUANG_GAI";
    HeroType[HeroType["LIAN_PO"] = 21] = "LIAN_PO";
    HeroType[HeroType["CAO_CAO"] = 22] = "CAO_CAO";
    HeroType[HeroType["HUANG_ZHONG"] = 23] = "HUANG_ZHONG";
    HeroType[HeroType["HAN_DANG"] = 24] = "HAN_DANG";
    HeroType[HeroType["LIU_BEI"] = 25] = "LIU_BEI";
    HeroType[HeroType["LIU_CHONG"] = 26] = "LIU_CHONG";
    HeroType[HeroType["HUANG_YUEYING"] = 27] = "HUANG_YUEYING";
    HeroType[HeroType["WANG_YI"] = 28] = "WANG_YI";
    HeroType[HeroType["LI_RU"] = 29] = "LI_RU";
    HeroType[HeroType["MA_ZHONG"] = 30] = "MA_ZHONG";
    HeroType[HeroType["YANG_YOUJI"] = 31] = "YANG_YOUJI";
    HeroType[HeroType["SUN_QUAN"] = 32] = "SUN_QUAN";
    HeroType[HeroType["CAO_XIU"] = 33] = "CAO_XIU";
    HeroType[HeroType["SUN_SHANGXIANG"] = 34] = "SUN_SHANGXIANG";
    HeroType[HeroType["ZHANG_LIAO"] = 35] = "ZHANG_LIAO";
    HeroType[HeroType["HUO_QUBING"] = 36] = "HUO_QUBING";
    HeroType[HeroType["LV_BU"] = 37] = "LV_BU";
    HeroType[HeroType["MA_CHAO"] = 38] = "MA_CHAO";
    HeroType[HeroType["XIANG_YU"] = 39] = "XIANG_YU";
    HeroType[HeroType["GUAN_YU"] = 40] = "GUAN_YU";
    HeroType[HeroType["GUAN_YIN_PING"] = 41] = "GUAN_YIN_PING";
    HeroType[HeroType["XU_CHU"] = 42] = "XU_CHU";
    HeroType[HeroType["PEI_XINGYAN"] = 43] = "PEI_XINGYAN";
    HeroType[HeroType["XIA_HOUYUAN"] = 44] = "XIA_HOUYUAN";
    HeroType[HeroType["TAI_SHICI"] = 45] = "TAI_SHICI";
    HeroType[HeroType["XU_HUANG"] = 46] = "XU_HUANG";
    HeroType[HeroType["LV_MENG"] = 47] = "LV_MENG";
    HeroType[HeroType["QIN_QIONG"] = 48] = "QIN_QIONG";
    HeroType[HeroType["LU_XUN"] = 49] = "LU_XUN";
    HeroType[HeroType["CHENG_YAOJIN"] = 50] = "CHENG_YAOJIN";
    HeroType[HeroType["DENG_AI"] = 51] = "DENG_AI";
    HeroType[HeroType["DONG_ZHUO"] = 52] = "DONG_ZHUO";
    HeroType[HeroType["MENG_HUO"] = 53] = "MENG_HUO";
    HeroType[HeroType["YANG_MIAOZHEN"] = 54] = "YANG_MIAOZHEN";
    HeroType[HeroType["LIANG_HONGYU"] = 55] = "LIANG_HONGYU";
    HeroType[HeroType["LI_MU"] = 56] = "LI_MU";
    HeroType[HeroType["XIN_QIJI"] = 57] = "XIN_QIJI";
    // WANG_SHUANG,    //王双
    // LING_TONG,      //凌统
    // XIA_HOUDUN,     //夏侯惇
    // MENG_TIAN,      //蒙恬
    // ZHE_BIE,        //哲别
    // JIANG_QIN,      //蒋钦
})(HeroType || (HeroType = {}));
exports.HeroType = HeroType;
// 分享类型
var ShareType;
(function (ShareType) {
    ShareType[ShareType["NONE"] = 0] = "NONE";
    ShareType[ShareType["INVITE"] = 1] = "INVITE";
    ShareType[ShareType["ON_SHARE_APP_MESSAGE"] = 2] = "ON_SHARE_APP_MESSAGE";
    ShareType[ShareType["ON_SHARE_TIMELINE"] = 3] = "ON_SHARE_TIMELINE";
    ShareType[ShareType["AD"] = 4] = "AD";
})(ShareType || (ShareType = {}));
exports.ShareType = ShareType;
// 广告类型
var AdType;
(function (AdType) {
    AdType[AdType["NONE"] = 0] = "NONE";
    AdType[AdType["REWARD"] = 1] = "REWARD";
    AdType[AdType["SHARE"] = 2] = "SHARE";
})(AdType || (AdType = {}));
exports.AdType = AdType;
// 广告状态
var AdState;
(function (AdState) {
    AdState[AdState["NONE"] = 0] = "NONE";
    AdState[AdState["LOADING"] = 1] = "LOADING";
    AdState[AdState["WAIT"] = 2] = "WAIT";
    AdState[AdState["PLAY_FAIL"] = 3] = "PLAY_FAIL";
    AdState[AdState["LOAD_FAIL"] = 4] = "LOAD_FAIL";
    AdState[AdState["LOAD_SUCCESS"] = 5] = "LOAD_SUCCESS";
    AdState[AdState["PLAY_SUCCESS"] = 6] = "PLAY_SUCCESS";
    AdState[AdState["PLAYING"] = 7] = "PLAYING";
})(AdState || (AdState = {}));
exports.AdState = AdState;
// 播放状态
var AdPlayState;
(function (AdPlayState) {
    AdPlayState[AdPlayState["NONE"] = 0] = "NONE";
    AdPlayState[AdPlayState["SUCCESS"] = 1] = "SUCCESS";
    AdPlayState[AdPlayState["FAIL"] = 2] = "FAIL";
    AdPlayState[AdPlayState["LOAD_CACHE_FAIL"] = 3] = "LOAD_CACHE_FAIL";
    AdPlayState[AdPlayState["SHARE_FAIL"] = 4] = "SHARE_FAIL";
})(AdPlayState || (AdPlayState = {}));
exports.AdPlayState = AdPlayState;
// 支付平台类型
var PayPlatformType;
(function (PayPlatformType) {
    PayPlatformType["NONE"] = "none";
    PayPlatformType["WX"] = "wx";
    PayPlatformType["QQ"] = "qq";
    PayPlatformType["APPLE"] = "apple";
    PayPlatformType["GOOGLE"] = "google";
    PayPlatformType["APP_WX"] = "app_wx";
})(PayPlatformType || (PayPlatformType = {}));
exports.PayPlatformType = PayPlatformType;
// 上报错误等级
var ReportErrorLevel;
(function (ReportErrorLevel) {
    ReportErrorLevel[ReportErrorLevel["NONE"] = 1] = "NONE";
    ReportErrorLevel[ReportErrorLevel["START"] = 9] = "START";
    ReportErrorLevel[ReportErrorLevel["LOGIN"] = 8] = "LOGIN";
    ReportErrorLevel[ReportErrorLevel["NOVICE"] = 5] = "NOVICE";
    ReportErrorLevel[ReportErrorLevel["MAIN"] = 5] = "MAIN";
    ReportErrorLevel[ReportErrorLevel["LOBBY"] = 5] = "LOBBY";
})(ReportErrorLevel || (ReportErrorLevel = {}));
exports.ReportErrorLevel = ReportErrorLevel;
// 服务器全局控制开关
var SwitchType;
(function (SwitchType) {
    SwitchType[SwitchType["ATT"] = 0] = "ATT";
})(SwitchType || (SwitchType = {}));
exports.SwitchType = SwitchType;
var BUILD_NID;
(function (BUILD_NID) {
    /** 城墙 */
    BUILD_NID[BUILD_NID["WALL"] = 2000] = "WALL";
    /** 主城 */
    BUILD_NID[BUILD_NID["MAIN"] = 2001] = "MAIN";
    /** 粮仓 */
    BUILD_NID[BUILD_NID["GRANARY"] = 2002] = "GRANARY";
    /** 仓库 */
    BUILD_NID[BUILD_NID["WAREHOUSE"] = 2003] = "WAREHOUSE";
    /** 兵营 */
    BUILD_NID[BUILD_NID["BARRACKS"] = 2004] = "BARRACKS";
    /** 大使馆 */
    BUILD_NID[BUILD_NID["EMBASSY"] = 2005] = "EMBASSY";
    /** 自由市场 */
    BUILD_NID[BUILD_NID["FREE_BAZAAR"] = 2006] = "FREE_BAZAAR";
    /** 铁匠铺 */
    BUILD_NID[BUILD_NID["SMITHY"] = 2008] = "SMITHY";
    /** 工厂 */
    BUILD_NID[BUILD_NID["PLANT"] = 2010] = "PLANT";
    /** 校场 */
    BUILD_NID[BUILD_NID["DRILLGROUND"] = 2011] = "DRILLGROUND";
    /** 里属亭 */
    BUILD_NID[BUILD_NID["TING"] = 2012] = "TING";
    /** 边塞营 */
    BUILD_NID[BUILD_NID["CAMP"] = 2013] = "CAMP";
    /** 联盟市场 */
    BUILD_NID[BUILD_NID["ALLI_BAZAAR"] = 2014] = "ALLI_BAZAAR";
    /** 英雄殿 */
    BUILD_NID[BUILD_NID["HERO_HALL"] = 2015] = "HERO_HALL";
    /** 医馆 */
    BUILD_NID[BUILD_NID["HOSPITAL"] = 2016] = "HOSPITAL";
    /** 要塞 */
    BUILD_NID[BUILD_NID["FORT"] = 2102] = "FORT";
    /** 箭塔 */
    BUILD_NID[BUILD_NID["TOWER"] = 2103] = "TOWER";
    /** 旗子 */
    BUILD_NID[BUILD_NID["FLAG"] = 2101] = "FLAG";
    /** 农场 */
    BUILD_NID[BUILD_NID["FARM"] = 2201] = "FARM";
    /** 伐木场 */
    BUILD_NID[BUILD_NID["TIMBER"] = 2202] = "TIMBER";
    /** 采石场 */
    BUILD_NID[BUILD_NID["QUARRY"] = 2203] = "QUARRY";
})(BUILD_NID || (BUILD_NID = {}));
exports.BUILD_NID = BUILD_NID;
// 游戏模式状态
var RoomStateType;
(function (RoomStateType) {
    RoomStateType[RoomStateType["NONE"] = 0] = "NONE";
    RoomStateType[RoomStateType["IN_MATCH"] = 1] = "IN_MATCH";
    RoomStateType[RoomStateType["IN_GAME"] = 2] = "IN_GAME";
    RoomStateType[RoomStateType["CLOSE"] = 3] = "CLOSE";
})(RoomStateType || (RoomStateType = {}));
exports.RoomStateType = RoomStateType;
// 选择画像类型
var SelectPortrayalType;
(function (SelectPortrayalType) {
    SelectPortrayalType[SelectPortrayalType["NEED_DEBRIS"] = 0] = "NEED_DEBRIS";
    SelectPortrayalType[SelectPortrayalType["BUY"] = 1] = "BUY";
    SelectPortrayalType[SelectPortrayalType["GIFT"] = 2] = "GIFT";
    SelectPortrayalType[SelectPortrayalType["HERO"] = 3] = "HERO";
})(SelectPortrayalType || (SelectPortrayalType = {}));
exports.SelectPortrayalType = SelectPortrayalType;
// 地图场景类型
var MapSceneType;
(function (MapSceneType) {
    MapSceneType[MapSceneType["NONE"] = 0] = "NONE";
    MapSceneType[MapSceneType["SNAIL_ISLE"] = 1] = "SNAIL_ISLE";
})(MapSceneType || (MapSceneType = {}));
exports.MapSceneType = MapSceneType;
// 地图类型
var MapType;
(function (MapType) {
    MapType[MapType["RECT"] = 0] = "RECT";
    MapType[MapType["SKEW"] = 1] = "SKEW";
})(MapType || (MapType = {}));
exports.MapType = MapType;
// 角色类型
var MapRoleType;
(function (MapRoleType) {
    MapRoleType[MapRoleType["DXF"] = 0] = "DXF";
    MapRoleType[MapRoleType["YMZ"] = 1] = "YMZ";
    MapRoleType[MapRoleType["HZL"] = 2] = "HZL";
    MapRoleType[MapRoleType["DJY"] = 3] = "DJY";
})(MapRoleType || (MapRoleType = {}));
exports.MapRoleType = MapRoleType;
// 场景角色状态
var SceneRoleStateType;
(function (SceneRoleStateType) {
    SceneRoleStateType[SceneRoleStateType["NONE"] = 0] = "NONE";
    SceneRoleStateType[SceneRoleStateType["MOVE"] = 1] = "MOVE";
    SceneRoleStateType[SceneRoleStateType["STROLL"] = 2] = "STROLL";
    SceneRoleStateType[SceneRoleStateType["WORK"] = 3] = "WORK";
})(SceneRoleStateType || (SceneRoleStateType = {}));
exports.SceneRoleStateType = SceneRoleStateType;
// 允许通知弹窗类型
var NoticePermissionType;
(function (NoticePermissionType) {
    NoticePermissionType[NoticePermissionType["MSG"] = 1] = "MSG";
    NoticePermissionType[NoticePermissionType["PUSH"] = 2] = "PUSH";
    NoticePermissionType[NoticePermissionType["SUBS"] = 3] = "SUBS";
})(NoticePermissionType || (NoticePermissionType = {}));
exports.NoticePermissionType = NoticePermissionType;
// 研究类型
var StudyType;
(function (StudyType) {
    StudyType[StudyType["NONE"] = 0] = "NONE";
    StudyType[StudyType["POLICY"] = 1] = "POLICY";
    StudyType[StudyType["PAWN"] = 2] = "PAWN";
    StudyType[StudyType["EQUIP"] = 3] = "EQUIP";
    StudyType[StudyType["EXCLUSIVE"] = 4] = "EXCLUSIVE";
})(StudyType || (StudyType = {}));
exports.StudyType = StudyType;
// 图鉴评价类型
var BookCommentType;
(function (BookCommentType) {
    BookCommentType[BookCommentType["NONE"] = 0] = "NONE";
    BookCommentType[BookCommentType["POLICY"] = 1] = "POLICY";
    BookCommentType[BookCommentType["PAWN"] = 2] = "PAWN";
    BookCommentType[BookCommentType["EQUIP"] = 3] = "EQUIP";
    BookCommentType[BookCommentType["PORTRAYAL"] = 4] = "PORTRAYAL";
})(BookCommentType || (BookCommentType = {}));
exports.BookCommentType = BookCommentType;
// 胜利条件
var WinCondType;
(function (WinCondType) {
    WinCondType[WinCondType["NONE"] = 0] = "NONE";
    WinCondType[WinCondType["TERRITORY_DISPUTE"] = 1] = "TERRITORY_DISPUTE";
    WinCondType[WinCondType["REBUILD_RUINS"] = 2] = "REBUILD_RUINS";
    WinCondType[WinCondType["KARMIC_MAHJONG"] = 3] = "KARMIC_MAHJONG";
})(WinCondType || (WinCondType = {}));
exports.WinCondType = WinCondType;
// 战斗数据类型
var BattleStatistics;
(function (BattleStatistics) {
    BattleStatistics[BattleStatistics["NONE"] = 0] = "NONE";
    BattleStatistics[BattleStatistics["SUM_KILL"] = 1] = "SUM_KILL";
    BattleStatistics[BattleStatistics["KILL_PAWN"] = 2] = "KILL_PAWN";
    BattleStatistics[BattleStatistics["SUM_DAMAGE"] = 3] = "SUM_DAMAGE";
    BattleStatistics[BattleStatistics["TO_PAWN_DAMGE"] = 4] = "TO_PAWN_DAMGE";
    BattleStatistics[BattleStatistics["HEAL_HP"] = 5] = "HEAL_HP";
    BattleStatistics[BattleStatistics["HIT_DAMAGE_MITIGATED"] = 6] = "HIT_DAMAGE_MITIGATED";
    BattleStatistics[BattleStatistics["DIE_COUNT"] = 7] = "DIE_COUNT";
    BattleStatistics[BattleStatistics["KILL_MAIN"] = 8] = "KILL_MAIN";
    BattleStatistics[BattleStatistics["ASSIST_KILL_MAIN"] = 9] = "ASSIST_KILL_MAIN";
    BattleStatistics[BattleStatistics["BUILD_DAMAGE"] = 10] = "BUILD_DAMAGE";
    BattleStatistics[BattleStatistics["DAMAGE_TO_BUILD"] = 11] = "DAMAGE_TO_BUILD";
    BattleStatistics[BattleStatistics["DAMAGE_TO_MONSTER"] = 12] = "DAMAGE_TO_MONSTER";
    BattleStatistics[BattleStatistics["KILL_MONSTER"] = 13] = "KILL_MONSTER";
    BattleStatistics[BattleStatistics["PAWN_DEAD"] = 14] = "PAWN_DEAD";
    BattleStatistics[BattleStatistics["HIT_DAMAGE_TAKEN"] = 15] = "HIT_DAMAGE_TAKEN";
    // 下面用于客户端显示用 暂时放这里 主要是怕和上面的类型冲突
    BattleStatistics[BattleStatistics["ACC_RECRUIT_PAWN_COUNT"] = 1001] = "ACC_RECRUIT_PAWN_COUNT";
    BattleStatistics[BattleStatistics["RECRUIT_PAWN_MAX_COUNT"] = 1002] = "RECRUIT_PAWN_MAX_COUNT";
    BattleStatistics[BattleStatistics["RECRUIT_PAWN_MAX_COUNT_ID"] = 1003] = "RECRUIT_PAWN_MAX_COUNT_ID";
    BattleStatistics[BattleStatistics["MAXLV_PAWN_COUNT"] = 1004] = "MAXLV_PAWN_COUNT";
    BattleStatistics[BattleStatistics["LAND_COUNT"] = 1005] = "LAND_COUNT";
    BattleStatistics[BattleStatistics["MAX_LAND_COUNT"] = 1006] = "MAX_LAND_COUNT";
    BattleStatistics[BattleStatistics["FIGHT_COUNT"] = 1007] = "FIGHT_COUNT";
})(BattleStatistics || (BattleStatistics = {}));
exports.BattleStatistics = BattleStatistics;
// 士兵皮肤获取条件
var PawnSkinCondType;
(function (PawnSkinCondType) {
    PawnSkinCondType[PawnSkinCondType["SHOP"] = 1] = "SHOP";
    PawnSkinCondType[PawnSkinCondType["TASK"] = 2] = "TASK";
    PawnSkinCondType[PawnSkinCondType["RANK_SHOP"] = 3] = "RANK_SHOP";
    PawnSkinCondType[PawnSkinCondType["EXCHANGE"] = 4] = "EXCHANGE";
    PawnSkinCondType[PawnSkinCondType["MYSTERY_BOX_1"] = 101] = "MYSTERY_BOX_1";
    PawnSkinCondType[PawnSkinCondType["MYSTERY_BOX_2"] = 102] = "MYSTERY_BOX_2";
    PawnSkinCondType[PawnSkinCondType["MYSTERY_BOX_3"] = 103] = "MYSTERY_BOX_3";
    PawnSkinCondType[PawnSkinCondType["MYSTERY_BOX_4"] = 104] = "MYSTERY_BOX_4";
})(PawnSkinCondType || (PawnSkinCondType = {}));
// 月卡类型
var MonthlyCardType;
(function (MonthlyCardType) {
    MonthlyCardType["SALE"] = "sub_month_card";
    MonthlyCardType["SUPER"] = "sub_super_month_card";
})(MonthlyCardType || (MonthlyCardType = {}));
exports.MonthlyCardType = MonthlyCardType;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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