
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/ScrollViewPlus.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ed29eQxXa9Ba6nz3PRunJMJ', 'ScrollViewPlus');
// app/core/component/ScrollViewPlus.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent, disallowMultiple = _a.disallowMultiple;
var ScrollViewPlus = /** @class */ (function (_super) {
    __extends(ScrollViewPlus, _super);
    function ScrollViewPlus() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isFrameRender = true; //是否开启分帧渲染
        _this.updateRate = 1; //渲染频率 多少帧渲染一个
        _this.updateRenderCount = 1; //分帧渲染个数
        _this.scrollView = null;
        _this.viewNode = null;
        _this.content = null;
        _this.item = null;
        _this.frameCount = 0; //多少帧
        _this.renderStartIndex = 0; //渲染起点下标
        _this.needRenderCount = 0; //需要渲染的item个数
        _this.currRenderCount = 0; //当前渲染的item个数
        _this.setItemCallback = null;
        _this.callbackTarget = null;
        _this.datas = null;
        _this.isCanScrollingUpdate = false; //是否可以滚动刷新了
        _this.canScrollingUpdate = false;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    ScrollViewPlus.prototype.onLoad = function () {
        this.init();
    };
    ScrollViewPlus.prototype.init = function () {
        if (this.scrollView) {
            return;
        }
        this.scrollView = this.getComponent(cc.ScrollView);
        this.content = this.scrollView.content;
        this.viewNode = this.content.parent;
        this.item = this.content.children[0];
        if (this.item) {
            this.item.active = false;
        }
        this.content.on(cc.Node.EventType.CHILD_REMOVED, this.onScrolling, this);
        this.content.on(cc.Node.EventType.CHILD_REORDER, this.onScrolling, this);
        this.node.on('scrolling', this.onScrolling, this);
    };
    // 滚动
    ScrollViewPlus.prototype.onScrolling = function () {
        var _this = this;
        if (!this.scrollView || this.content.childrenCount === 0 || !this.isCanScrollingUpdate) {
            return;
        }
        // cc.log('111 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.childrenCount)
        var viewSize = this.viewNode.getContentSize();
        var contentX = this.content.width * this.content.anchorX;
        var contentY = this.content.height * this.content.anchorY;
        var pos = this.content.getPosition(this._temp_vec2_1);
        pos.x += this.viewNode.width * this.viewNode.anchorX - contentX;
        pos.y += this.viewNode.height * this.viewNode.anchorY - contentY;
        // 遍历 ScrollView Content 内容节点的子节点，对每个子节点的包围盒做和 ScrollView 可视区域包围盒做碰撞判断
        this.content.children.forEach(function (node) {
            if (!node.active) {
                return;
            }
            var p = node.getPosition(_this._temp_vec2_2);
            p.x = pos.x + p.x + (contentX - node.width * node.anchorX);
            p.y = pos.y + p.y + (contentY - node.height * node.anchorY);
            if (p.x < -node.width || p.x > viewSize.width || p.y < -node.height || p.y > viewSize.height) { //如果没有相交就隐藏
                if (node.opacity !== 0) {
                    node.opacity = 0;
                }
            }
            else if (node.opacity !== 255) {
                node.opacity = 255;
            }
        });
        // cc.log('222 count=' + this.content.children.filter(m => m.active && m.opacity === 255).length + '/' + this.content.childrenCount)
    };
    ScrollViewPlus.prototype.update = function (dt) {
        // 渲染
        if (!this.item) {
            return;
        }
        else if (this.isFrameRender && this.currRenderCount < this.needRenderCount) {
            this.frameCount += 1;
            if (this.frameCount >= this.updateRate) {
                this.frameCount = 0;
                this.updateItems();
            }
        }
        else if (this.canScrollingUpdate && !this.isCanScrollingUpdate) {
            this.isCanScrollingUpdate = true;
            this.onScrolling();
        }
    };
    // 开始创建item
    ScrollViewPlus.prototype.updateItems = function () {
        var cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0;
        if (this.currRenderCount + cnt > this.needRenderCount) {
            cnt = this.needRenderCount - this.currRenderCount;
        }
        var i = this.renderStartIndex;
        for (; i < this.needRenderCount && cur < cnt; i++) {
            this.setItemData(this.content.children[i] || cc.instantiate2(this.item, this.content), i);
            cur += 1;
        }
        this.currRenderCount += cnt;
        this.renderStartIndex = i;
        if (this.currRenderCount >= this.needRenderCount) {
            // cc.log('updateItems done...', i, this.content.childrenCount)
            // 将剩余的隐藏
            for (var l = this.content.childrenCount; i < l; i++) {
                var node = this.content.children[i];
                node.active = false;
                node.Data = null;
            }
        }
    };
    ScrollViewPlus.prototype.setItemData = function (it, i) {
        var _a, _b;
        it.active = true;
        it.Data = null;
        it.opacity = 255;
        if (!this.setItemCallback) {
        }
        else if (this.callbackTarget) {
            this.setItemCallback.call(this.callbackTarget, it, (_a = this.datas) === null || _a === void 0 ? void 0 : _a[i], i);
        }
        else {
            this.setItemCallback(it, (_b = this.datas) === null || _b === void 0 ? void 0 : _b[i], i);
        }
        return it;
    };
    ScrollViewPlus.prototype.reset = function () {
        this.isCanScrollingUpdate = false;
        this.canScrollingUpdate = false;
    };
    ScrollViewPlus.prototype.items = function (list, prefab, cb, target) {
        if (typeof (list) === 'number') {
            this.needRenderCount = list;
            this.datas = null;
        }
        else {
            this.needRenderCount = list.length;
            this.datas = list;
        }
        if (prefab) {
            this.item = prefab;
        }
        this.setItemCallback = cb;
        this.callbackTarget = target;
        this.currRenderCount = 0;
        this.renderStartIndex = 0;
        this.canScrollingUpdate = true;
        if (this.needRenderCount === 0) {
            this.content.children.forEach(function (m) {
                m.active = false;
                m.Data = null;
            });
        }
    };
    ScrollViewPlus.prototype.updateNodeShow = function () {
        this.canScrollingUpdate = true;
    };
    __decorate([
        property()
    ], ScrollViewPlus.prototype, "isFrameRender", void 0);
    __decorate([
        property({ type: cc.Integer, range: [1, 10, 1], tooltip: CC_DEV && '多少帧渲染一次', slide: true })
    ], ScrollViewPlus.prototype, "updateRate", void 0);
    __decorate([
        property({ type: cc.Integer, range: [0, 30, 1], tooltip: CC_DEV && '一次渲染多少个,0渲染所有', slide: true })
    ], ScrollViewPlus.prototype, "updateRenderCount", void 0);
    ScrollViewPlus = __decorate([
        ccclass,
        disallowMultiple(),
        requireComponent(cc.ScrollView),
        menu('自定义组件/ScrollViewPlus')
    ], ScrollViewPlus);
    return ScrollViewPlus;
}(cc.Component));
exports.default = ScrollViewPlus;
cc.ScrollViewPlus = ScrollViewPlus;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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