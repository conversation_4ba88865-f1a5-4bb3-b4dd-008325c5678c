
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/crypto/CryptoHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '35003vKvXBEKaZzXH+hyAiB', 'CryptoHelper');
// app/script/common/crypto/CryptoHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cryptoHelper = void 0;
var GameHelper_1 = require("../helper/GameHelper");
var ByteArrayMD5_1 = require("./ByteArrayMD5");
var CryptoJS_1 = require("./CryptoJS");
var JSEncrypt_1 = require("./JSEncrypt");
//小程序，海外app用这个
var publicDer = "\n-----BEGIN PUBLIC KEY-----\nMFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMJ85/CKgzbKiJXVz+N29h8ejG/PaqDm\nXwc/x5oER7nNt55tYK4G3MG8NzfKrAiyTA5I5czk667MF3e9F+8bb8cCAwEAAQ==\n-----END PUBLIC KEY-----\n";
if (CC_JSB) { //国内app用这个，这段对小程序隐藏
    GameHelper_1.gameHpr.isInland() && (publicDer = "\n-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDB4AIdbEz26jRW+2P7Xn6Efoxc\nE0OHOnAPn8UP5JglYaCnK/U+VVhoxINie9pnjPOOxvTVbzK0BEH9cV2Zc4I4i5u9\nWZZ9hdXCrpZcxQqF5Cg1fTWyUTbNZrFwKKXvVajF00tXw+vJVUD3EuGWVBkD4jC0\naWQ9esLammAOmBUXTwIDAQAB\n-----END PUBLIC KEY-----\n");
}
var BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
var CryptoHelper = /** @class */ (function () {
    function CryptoHelper() {
        this.publicCrypt = null;
        this.publicCrypt = new JSEncrypt_1.default();
        this.publicCrypt.setPublicKey(publicDer);
    }
    /**
     * @param str
     * @param aes 使用aes加密，如果字符串过长，需要设成true
     * @description 可以加密长字符串，原理是用aes随机密钥加密长字符串，公钥加密aes的密钥
     */
    CryptoHelper.prototype.pkEncrypt = function (str, aes) {
        if (aes === void 0) { aes = true; }
        if (aes) {
            var aesKey = ut.getRandomString(16); //随机aes密钥
            var encryStr = CryptoJS_1.default.AES.encrypt(str, aesKey).toString(); //aes加密原串
            var encryKey = this.publicCrypt.encrypt(aesKey); //公钥加密aes的密钥
            return { encryStr: encryStr, encryKey: encryKey };
        }
        return this.publicCrypt.encrypt(str);
    };
    /**
     * @param data
     * @param sign
     * @description 公钥验签
     */
    CryptoHelper.prototype.pkVerify = function (data, sign) {
        return this.publicCrypt.verify(data, sign, CryptoJS_1.default.MD5);
    };
    CryptoHelper.prototype.md5 = function (data) {
        if (data instanceof Uint8Array) {
            return ByteArrayMD5_1.default(data);
        }
        return CryptoJS_1.default.MD5(data).toString();
    };
    CryptoHelper.prototype.aseDecrypt = function (encryptStr, key) {
        return CryptoJS_1.default.AES.decrypt(encryptStr, key).toString(CryptoJS_1.default.enc.Utf8);
    };
    // 编码函数：将数字转换为 Base62
    CryptoHelper.prototype.encodeBase62 = function (num) {
        if (num === 0) {
            return BASE62_CHARS[0];
        }
        var base = BASE62_CHARS.length;
        var encoded = '';
        while (num > 0) {
            var remainder = num % base;
            encoded = BASE62_CHARS[remainder] + encoded;
            num = Math.floor(num / base);
        }
        return encoded;
    };
    // 解码函数：将 Base62 字符串转换为数字
    CryptoHelper.prototype.decodeBase62 = function (str) {
        var base = BASE62_CHARS.length;
        var decoded = 0;
        for (var i = 0; i < str.length; i++) {
            var charIndex = BASE62_CHARS.indexOf(str[i]);
            decoded = decoded * base + charIndex;
        }
        return decoded;
    };
    return CryptoHelper;
}());
exports.cryptoHelper = new CryptoHelper();
if (cc.sys.isBrowser) {
    window['cryptoHelper'] = exports.cryptoHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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