
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/bazaar/BazaarModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5a24coiFw9PsYf4zU3wqvMe', 'BazaarModel');
// app/script/model/bazaar/BazaarModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var TradingResObj_1 = require("./TradingResObj");
/**
 * 市场模块
 */
var BazaarModel = /** @class */ (function (_super) {
    __extends(BazaarModel, _super);
    function BazaarModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 可以交易的资源
        _this.CAN_DEAL_RES = [Enums_1.CType.CEREAL, Enums_1.CType.TIMBER, Enums_1.CType.STONE, Enums_1.CType.EXP_BOOK, Enums_1.CType.IRON, Enums_1.CType.UP_SCROLL, Enums_1.CType.FIXATOR];
        // 可以兑换的资源
        _this.CAN_REPLACEMENT_RES = [Enums_1.CType.CEREAL, Enums_1.CType.TIMBER, Enums_1.CType.STONE];
        _this.net = null;
        _this.player = null;
        _this.tempTradingRess = []; //当前的交易资源列表
        _this.lastReqTradingResTime = 0;
        return _this;
    }
    BazaarModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.player = this.getModel('player');
    };
    BazaarModel.prototype.getMerchants = function () { return this.player.getMerchants(); };
    BazaarModel.prototype.getMerchantsByState = function (state) { return this.getMerchants().filter(function (m) { return m.state === state; }); };
    // 获取商人的状态分布
    BazaarModel.prototype.getMerchantStateMap = function () {
        var obj = {};
        this.getMerchants().forEach(function (m) {
            if (obj[m.state]) {
                obj[m.state] += 1;
            }
            else {
                obj[m.state] = 1;
            }
        });
        return obj;
    };
    // 获取所有交易资源
    BazaarModel.prototype.getTradingRess = function () {
        return __awaiter(this, void 0, void 0, function () {
            var now, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        now = Date.now();
                        if (this.lastReqTradingResTime > 0 && now - this.lastReqTradingResTime <= 10000) {
                            return [2 /*return*/, this.tempTradingRess];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetTradingRess')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.tempTradingRess = ((data === null || data === void 0 ? void 0 : data.tradingRess) || []).map(function (m) { return new TradingResObj_1.default().fromSvr(m); });
                        this.lastReqTradingResTime = now;
                        return [2 /*return*/, this.tempTradingRess];
                }
            });
        });
    };
    // 出售资源
    BazaarModel.prototype.sellRes = function (sellType, sellCount, buyType, buyCount, onlyAlli) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.CAN_DEAL_RES.has(sellType) || !this.CAN_DEAL_RES.has(buyType)) {
                            return [2 /*return*/, ECode_1.ecode.UNKNOWN];
                        }
                        else if (sellCount === 0 || buyCount === 0) {
                            return [2 /*return*/, ECode_1.ecode.PLEASE_INPUT_RES_COUNT];
                        }
                        else if (sellCount > GameHelper_1.gameHpr.getCountByCType(sellType)) {
                            return [2 /*return*/, ECode_1.ecode.RES_NOT_ENOUGH];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BazaarSellRes', { sellType: sellType, sellCount: sellCount, buyType: buyType, buyCount: buyCount, onlyAlli: onlyAlli }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.lastReqTradingResTime = 0;
                            this.player.updateRewardItemsByFlags(data.items);
                            this.player.updateMerchants(data.merchants);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 取消出售资源
    BazaarModel.prototype.cancelSell = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_BazaarCancelSell', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.lastReqTradingResTime = 0;
                            this.player.updateRewardItemsByFlags(data.items);
                            this.player.updateMerchants(data.merchants);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 购买资源
    BazaarModel.prototype.buyRes = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_BazaarBuyRes', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.lastReqTradingResTime = 0;
                            this.tempTradingRess.remove('uid', uid);
                            this.player.updateRewardItemsByFlags(data.items);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 赠送资源
    BazaarModel.prototype.giveRes = function (name, index, type, count) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.CAN_DEAL_RES.has(type)) {
                            return [2 /*return*/, ECode_1.ecode.UNKNOWN];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BazaarGiveRes', { name: name, index: index, type: type, count: count }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.player.updateRewardItemsByFlags(data.items);
                            this.player.updateMerchants(data.merchants);
                            this.player.addAccTotalGiveResCount(count); //记录累计赠送资源数量
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 置换资源
    BazaarModel.prototype.replacementRes = function (sellType, sellCount, buyType) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.CAN_REPLACEMENT_RES.has(sellType) || !this.CAN_REPLACEMENT_RES.has(buyType)) {
                            return [2 /*return*/, ECode_1.ecode.UNKNOWN];
                        }
                        else if (sellCount === 0) {
                            return [2 /*return*/, ECode_1.ecode.PLEASE_INPUT_RES_COUNT];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_BazaarSellToSys', { sellType: sellType, sellCount: sellCount, buyType: buyType }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.player.updateRewardItemsByFlags(data.items);
                            this.player.setTodayReplacementCount(data.todayReplacementCount);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取剩余可置换的次数
    BazaarModel.prototype.getSurplusReplacementCount = function () {
        var todayCount = Constant_1.REPLACEMENT_TODAY_COUNT_MAP[GameHelper_1.gameHpr.getServerType()] || 0;
        if (GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MARKET_SERVICE_CHARGE) > 0) {
            todayCount += 1; //交能易作
        }
        return Math.max(0, todayCount - this.player.getTodayReplacementCount());
    };
    BazaarModel = __decorate([
        mc.addmodel('bazaar')
    ], BazaarModel);
    return BazaarModel;
}(mc.BaseModel));
exports.default = BazaarModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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