
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/SearchTarget.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c67044Lw9JOtojoHpkMMtbo', 'SearchTarget');
// app/script/model/behavior/SearchTarget.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 寻找最近目标
var SearchTarget = /** @class */ (function (_super) {
    __extends(SearchTarget, _super);
    function SearchTarget() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    SearchTarget.prototype.onInit = function (conf) {
    };
    SearchTarget.prototype.onTick = function (dt) {
        if (!!this.getTreeBlackboardData('isSearchTarget') && !!this.target.attackTarget) {
            return BTConstant_1.BTState.FAILURE;
        }
        this.setTreeBlackboardData('isSearchTarget', true); //记录是否已经搜索过了
        return this.getTarget() ? BTConstant_1.BTState.SUCCESS : BTConstant_1.BTState.FAILURE;
    };
    SearchTarget.prototype.getTarget = function () {
        var targets = this.target.getCanAttackTargets();
        if (targets.length > 0) {
            var it = null;
            if (this.target.getEquipEffectByType(Enums_1.EquipEffectType.SILVER_SNAKE_WHIP)) {
                it = this.randomTargetInAR();
            }
            else if (this.target.isHasBuff(Enums_1.BuffType.RAGE)) {
                it = this.randomMinDisTargetInAR();
            }
            if (!it) {
                it = targets[0];
            }
            if (it.attackTargetPoint) {
                var targetPoint = it.attackTargetPoint;
                var movePoint = this.target.getPoint();
                if (it.paths.length > 0) {
                    movePoint = it.paths.last();
                }
                var attackTargetPoint = it.target.getPoint();
                var enemyCount = this.ctrl.getFighterCountByPoint(attackTargetPoint);
                if (it.target.getPawnType() === Enums_1.PawnType.BUILD || enemyCount === 1) {
                    //该点位只有一个敌人时 设置锁定目标点
                    this.ctrl.setLockMovePointFighter(targetPoint, this.target, it.moveWeight, movePoint);
                }
            }
            this.target.changeAttackTarget(it.target);
            // console.log('set target entity uid: ' + this.target.getUid() + ' point: ' + this.target.getPoint() + ' move: ' + targets[0].paths + ' pawnType: ' + this.target.getPawnType() + ' tUid: ' + this.target.attackTarget.getUid() + ' tPoint: ' + this.target.attackTarget.getPoint() + ' tType: ' + this.target.attackTarget.getPawnType());            
            return true;
        }
        this.target.changeAttackTarget(null);
        return false;
    };
    SearchTarget.prototype.randomTargetInAR = function () {
        var _this = this;
        var allTargets = this.target.getCanAttackTargets().filter(function (m) { return _this.target.checkInMyAttackRange(m.target); });
        if (allTargets.length > 0) {
            return allTargets[this.ctrl.getRandom().get(0, allTargets.length - 1)];
        }
        return null;
    };
    // 随机最近的目标
    SearchTarget.prototype.randomMinDisTargetInAR = function () {
        var _this = this;
        var allTargets = this.target.getCanAttackTargets().filter(function (m) { return _this.target.checkInMyAttackRange(m.target); });
        var arr = [], minDis = 100000;
        allTargets.forEach(function (m) {
            var dis = _this.target.getMinDis(m.target);
            if (dis < minDis) {
                minDis = dis;
                arr = [m];
            }
            else if (dis === minDis) {
                arr.push(m);
            }
        });
        return arr[this.ctrl.getRandom().get(0, arr.length - 1)];
    };
    return SearchTarget;
}(BaseAction_1.default));
exports.default = SearchTarget;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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