
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/AgreeFriendApplyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fbfb7+JrRpEbZbnW0foKifS', 'AgreeFriendApplyPnlCtrl');
// app/script/view/menu/AgreeFriendApplyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AgreeFriendApplyPnlCtrl = /** @class */ (function (_super) {
    __extends(AgreeFriendApplyPnlCtrl, _super);
    function AgreeFriendApplyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentRt_ = null; // path://root/content_rt
        _this.noApplyTge_ = null; // path://root/no_apply_t
        //@end
        _this.data = null;
        _this.isAgree = false;
        return _this;
    }
    AgreeFriendApplyPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AgreeFriendApplyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AgreeFriendApplyPnlCtrl.prototype.onEnter = function (data, isAgree) {
        this.data = data;
        this.isAgree = isAgree;
        this.contentRt_.setLocaleKey(isAgree ? 'ui.agree_friend_apply_1' : 'ui.agree_friend_apply_0', ut.nameFormator(data.nickname || '???', 7));
        if (this.noApplyTge_.setActive(!isAgree)) {
            this.noApplyTge_.isChecked = false;
        }
    };
    AgreeFriendApplyPnlCtrl.prototype.onRemove = function () {
        this.data = null;
    };
    AgreeFriendApplyPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    AgreeFriendApplyPnlCtrl.prototype.onClickButtons = function (event, data) {
        if (!this.data) {
            return;
        }
        else if (event.target.name === 'ok') {
            return this.do();
        }
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AgreeFriendApplyPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var friend, uid, _a, err, data, list;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        friend = GameHelper_1.gameHpr.friend, uid = this.data.uid;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_ApplyFriendResponse', { uid: uid, agree: this.isAgree, ban: this.noApplyTge_.isChecked })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            if (err === ECode_1.ecode.NOT_APPLY_FRIEND || err === ECode_1.ecode.ALREADY_FRIENDS || err === ECode_1.ecode.PLAYER_NOT_EXIST) {
                                list = friend.getApplys();
                                list.remove('uid', uid);
                                friend.updateApplys(list);
                            }
                            ViewHelper_1.viewHelper.showAlert(err);
                        }
                        else {
                            friend.updateFriends(data.friendsList || []);
                            friend.updateApplys(data.friendsApplys || []);
                            friend.updateBlacklist(data.blacklists || []);
                        }
                        this.hide();
                        this.isAgree && GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.SUBS);
                        return [2 /*return*/];
                }
            });
        });
    };
    AgreeFriendApplyPnlCtrl = __decorate([
        ccclass
    ], AgreeFriendApplyPnlCtrl);
    return AgreeFriendApplyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AgreeFriendApplyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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