
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/FacebookHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4cf16Qr2t5GiKSU6YpU7YZ6', 'FacebookHelper');
// app/script/common/helper/FacebookHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.facebookHelper = void 0;
var JsbEvent_1 = require("../event/JsbEvent");
var ErrorReportHelper_1 = require("./ErrorReportHelper");
var JsbHelper_1 = require("./JsbHelper");
/**
 * 处理海外脸书登录 相关
 */
var FacebookHelper = /** @class */ (function () {
    function FacebookHelper() {
    }
    // 登陆
    FacebookHelper.prototype.nativeLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.FACEBOOK_LOGIN)];
                    case 1:
                        res = (_a.sent()) || {};
                        if (res.result === 'success') {
                            return [2 /*return*/, { userId: res.uid, token: res.token, jwtToken: res.jwtToken }];
                        }
                        else {
                            ErrorReportHelper_1.errorReportHelper.reportError('NativeLogin Error', { type: 'facebook', status: res.errcode, extra: JSON.stringify(res) });
                        }
                        return [2 /*return*/, { errcode: res.errcode || -10086 }];
                }
            });
        });
    };
    // 需要传的参数 url title
    FacebookHelper.prototype.shareLink = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.FACEBOOK_SHARE_LINK, data)];
                    case 1:
                        res = _a.sent();
                        return [2 /*return*/, (res === null || res === void 0 ? void 0 : res.result) === 'success'];
                }
            });
        });
    };
    //需要传的参数 path title
    FacebookHelper.prototype.sharePhoto = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.FACEBOOK_SHARE_PHOTO, data)];
                    case 1:
                        res = _a.sent();
                        return [2 /*return*/, (res === null || res === void 0 ? void 0 : res.result) === 'success'];
                }
            });
        });
    };
    FacebookHelper.prototype.share = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (data.imgUrl && !data.imgUrl.startsWith('http')) {
                    return [2 /*return*/, this.sharePhoto({ path: data.imgUrl, title: data.title })];
                }
                return [2 /*return*/, this.shareLink(data)];
            });
        });
    };
    FacebookHelper.prototype.isLimitedLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.IS_LIMITED_FB_LOGIN)];
                    case 1:
                        res = (_a.sent()) || {};
                        return [2 /*return*/, res.result || false];
                }
            });
        });
    };
    return FacebookHelper;
}());
exports.facebookHelper = new FacebookHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcaGVscGVyXFxGYWNlYm9va0hlbHBlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw4Q0FBd0M7QUFDeEMseURBQXVEO0FBQ3ZELHlDQUF1QztBQUV2Qzs7R0FFRztBQUNIO0lBQUE7SUFvQ0EsQ0FBQztJQWxDRyxLQUFLO0lBQ1Esb0NBQVcsR0FBeEI7Ozs7OzRCQUNnQixxQkFBTSxxQkFBUyxDQUFDLElBQUksQ0FBQyxrQkFBUSxDQUFDLGNBQWMsQ0FBQyxFQUFBOzt3QkFBbkQsR0FBRyxHQUFHLENBQUEsU0FBNkMsS0FBSSxFQUFFO3dCQUMvRCxJQUFJLEdBQUcsQ0FBQyxNQUFNLEtBQUssU0FBUyxFQUFFOzRCQUMxQixzQkFBTyxFQUFFLE1BQU0sRUFBRSxHQUFHLENBQUMsR0FBRyxFQUFFLEtBQUssRUFBRSxHQUFHLENBQUMsS0FBSyxFQUFFLFFBQVEsRUFBRSxHQUFHLENBQUMsUUFBUSxFQUFFLEVBQUE7eUJBQ3ZFOzZCQUFNOzRCQUNILHFDQUFpQixDQUFDLFdBQVcsQ0FBQyxtQkFBbUIsRUFBRSxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsTUFBTSxFQUFFLEdBQUcsQ0FBQyxPQUFPLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFBO3lCQUM1SDt3QkFDRCxzQkFBTyxFQUFFLE9BQU8sRUFBRSxHQUFHLENBQUMsT0FBTyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUE7Ozs7S0FDNUM7SUFFRCxtQkFBbUI7SUFDTixrQ0FBUyxHQUF0QixVQUF1QixJQUFvQzs7Ozs7NEJBQzNDLHFCQUFNLHFCQUFTLENBQUMsSUFBSSxDQUFDLGtCQUFRLENBQUMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLEVBQUE7O3dCQUE5RCxHQUFHLEdBQUcsU0FBd0Q7d0JBQ3BFLHNCQUFPLENBQUEsR0FBRyxhQUFILEdBQUcsdUJBQUgsR0FBRyxDQUFFLE1BQU0sTUFBSyxTQUFTLEVBQUE7Ozs7S0FDbkM7SUFFRCxtQkFBbUI7SUFDTixtQ0FBVSxHQUF2QixVQUF3QixJQUFxQzs7Ozs7NEJBQzdDLHFCQUFNLHFCQUFTLENBQUMsSUFBSSxDQUFDLGtCQUFRLENBQUMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDLEVBQUE7O3dCQUEvRCxHQUFHLEdBQUcsU0FBeUQ7d0JBQ3JFLHNCQUFPLENBQUEsR0FBRyxhQUFILEdBQUcsdUJBQUgsR0FBRyxDQUFFLE1BQU0sTUFBSyxTQUFTLEVBQUE7Ozs7S0FDbkM7SUFFWSw4QkFBSyxHQUFsQixVQUFtQixJQUFvRDs7O2dCQUNuRSxJQUFJLElBQUksQ0FBQyxNQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsRUFBRTtvQkFDaEQsc0JBQU8sSUFBSSxDQUFDLFVBQVUsQ0FBQyxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBQTtpQkFDbkU7Z0JBQ0Qsc0JBQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsRUFBQTs7O0tBQzlCO0lBRVksdUNBQWMsR0FBM0I7Ozs7OzRCQUNjLHFCQUFNLHFCQUFTLENBQUMsSUFBSSxDQUFDLGtCQUFRLENBQUMsbUJBQW1CLENBQUMsRUFBQTs7d0JBQXhELEdBQUcsR0FBRyxDQUFBLFNBQWtELEtBQUksRUFBRTt3QkFDbEUsc0JBQU8sR0FBRyxDQUFDLE1BQU0sSUFBSSxLQUFLLEVBQUE7Ozs7S0FDN0I7SUFDTCxxQkFBQztBQUFELENBcENBLEFBb0NDLElBQUE7QUFFWSxRQUFBLGNBQWMsR0FBRyxJQUFJLGNBQWMsRUFBRSxDQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEpzYkV2ZW50IGZyb20gXCIuLi9ldmVudC9Kc2JFdmVudFwiXHJcbmltcG9ydCB7IGVycm9yUmVwb3J0SGVscGVyIH0gZnJvbSBcIi4vRXJyb3JSZXBvcnRIZWxwZXJcIlxyXG5pbXBvcnQgeyBqc2JIZWxwZXIgfSBmcm9tIFwiLi9Kc2JIZWxwZXJcIlxyXG5cclxuLyoqXHJcbiAqIOWkhOeQhua1t+WkluiEuOS5pueZu+W9lSDnm7jlhbNcclxuICovXHJcbmNsYXNzIEZhY2Vib29rSGVscGVyIHtcclxuXHJcbiAgICAvLyDnmbvpmYZcclxuICAgIHB1YmxpYyBhc3luYyBuYXRpdmVMb2dpbigpIHtcclxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBqc2JIZWxwZXIuY2FsbChKc2JFdmVudC5GQUNFQk9PS19MT0dJTikgfHwge31cclxuICAgICAgICBpZiAocmVzLnJlc3VsdCA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHVzZXJJZDogcmVzLnVpZCwgdG9rZW46IHJlcy50b2tlbiwgand0VG9rZW46IHJlcy5qd3RUb2tlbiB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgZXJyb3JSZXBvcnRIZWxwZXIucmVwb3J0RXJyb3IoJ05hdGl2ZUxvZ2luIEVycm9yJywgeyB0eXBlOiAnZmFjZWJvb2snLCBzdGF0dXM6IHJlcy5lcnJjb2RlLCBleHRyYTogSlNPTi5zdHJpbmdpZnkocmVzKSB9KVxyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4geyBlcnJjb2RlOiByZXMuZXJyY29kZSB8fCAtMTAwODYgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOmcgOimgeS8oOeahOWPguaVsCB1cmwgdGl0bGVcclxuICAgIHB1YmxpYyBhc3luYyBzaGFyZUxpbmsoZGF0YTogeyB1cmw6IHN0cmluZywgdGl0bGU6IHN0cmluZyB9KSB7XHJcbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQganNiSGVscGVyLmNhbGwoSnNiRXZlbnQuRkFDRUJPT0tfU0hBUkVfTElOSywgZGF0YSlcclxuICAgICAgICByZXR1cm4gcmVzPy5yZXN1bHQgPT09ICdzdWNjZXNzJ1xyXG4gICAgfVxyXG5cclxuICAgIC8v6ZyA6KaB5Lyg55qE5Y+C5pWwIHBhdGggdGl0bGVcclxuICAgIHB1YmxpYyBhc3luYyBzaGFyZVBob3RvKGRhdGE6IHsgcGF0aDogc3RyaW5nLCB0aXRsZTogc3RyaW5nIH0pIHtcclxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBqc2JIZWxwZXIuY2FsbChKc2JFdmVudC5GQUNFQk9PS19TSEFSRV9QSE9UTywgZGF0YSlcclxuICAgICAgICByZXR1cm4gcmVzPy5yZXN1bHQgPT09ICdzdWNjZXNzJ1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhc3luYyBzaGFyZShkYXRhOiB7IHRpdGxlOiBzdHJpbmcsIHVybDogc3RyaW5nLCBpbWdVcmw6IHN0cmluZyB9KSB7XHJcbiAgICAgICAgaWYgKGRhdGEuaW1nVXJsICYmICFkYXRhLmltZ1VybC5zdGFydHNXaXRoKCdodHRwJykpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2hhcmVQaG90byh7IHBhdGg6IGRhdGEuaW1nVXJsLCB0aXRsZTogZGF0YS50aXRsZSB9KVxyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gdGhpcy5zaGFyZUxpbmsoZGF0YSlcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgaXNMaW1pdGVkTG9naW4oKSB7XHJcbiAgICAgICAgbGV0IHJlcyA9IGF3YWl0IGpzYkhlbHBlci5jYWxsKEpzYkV2ZW50LklTX0xJTUlURURfRkJfTE9HSU4pIHx8IHt9XHJcbiAgICAgICAgcmV0dXJuIHJlcy5yZXN1bHQgfHwgZmFsc2VcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGZhY2Vib29rSGVscGVyID0gbmV3IEZhY2Vib29rSGVscGVyKCkiXX0=