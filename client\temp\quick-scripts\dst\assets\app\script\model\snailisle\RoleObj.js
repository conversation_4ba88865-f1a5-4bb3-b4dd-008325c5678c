
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/RoleObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '797cd23OWdAwo/EzXVH8zXI', 'RoleObj');
// app/script/model/snailisle/RoleObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var SIConstant_1 = require("./SIConstant");
var MoveRoleObj_1 = require("./MoveRoleObj");
// 
var RoleObj = /** @class */ (function (_super) {
    __extends(RoleObj, _super);
    function RoleObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.standRandomTime = [0, 5];
        _this.standTime = 0; //待机时间
        _this.workState = 0; //工作状态 1.开始 2.运行 3.离开
        _this.workStateTime = 0; //对应时间
        _this.bowTime = 0; //鞠躬时间
        _this.lastStrollPoint = null; //最后一次闲逛的位置
        return _this;
    }
    RoleObj.prototype.run = function () {
        this.reset();
    };
    RoleObj.prototype.reset = function () {
        // 闲逛
        this.stroll();
    };
    // 移动
    RoleObj.prototype.move = function (point, position) {
        var _this = this;
        this.action = 'move';
        this.searchPath(point, position).then(function (paths) {
            if (!paths) {
            }
            else if (paths.length > 0) {
                _this.setPath(paths);
            }
            else if (_this.astar) {
                _this.stand();
            }
        });
    };
    // 移动完成
    RoleObj.prototype.onMoveComplete = function () {
        if (this.state === 1) { //闲逛
            this.stand();
        }
        else if (this.state === 2) { //工作
            this.work(1);
        }
        else if (this.state === 3) { //鞠躬
        }
        else {
            this.stroll();
        }
    };
    RoleObj.prototype.update = function (dt) {
        //
        if (this.standTime > 0) {
            this.standTime -= dt;
            if (this.standTime <= 0) {
                this.stroll();
            }
        }
        if (this.workStateTime > 0) {
            this.workStateTime -= dt;
            if (this.workStateTime <= 0) {
                this.work(this.workState + 1);
            }
        }
        if (this.bowTime > 0) {
            this.bowTime -= dt;
            if (this.bowTime <= 0) {
                this.stand(ut.random(0, 1));
            }
        }
        // 刷新移动
        _super.prototype.update.call(this, dt);
    };
    // 待机
    RoleObj.prototype.stand = function (time) {
        this.state = 0;
        this.action = 'stand';
        this.standTime = time !== null && time !== void 0 ? time : ut.random(this.standRandomTime[0], this.standRandomTime[1]);
        if (this.standTime <= 0) {
            this.stroll();
        }
    };
    // 闲逛
    RoleObj.prototype.stroll = function () {
        this.state = 1;
        this.move(this.map.getStrollPoint(this));
    };
    // 准备工作
    RoleObj.prototype.readyWork = function () {
        this.state = 2;
        this.standTime = 0;
        // 获取工作的位置
        var pos = this.map.getWorkPoint(this);
        if (pos) {
            this.move(null, pos);
        }
        else {
            this.stand();
        }
    };
    // 准备鞠躬
    RoleObj.prototype.readyBow = function () {
        var isWork = this.state === 2;
        this.state = 3;
        this.standTime = 0;
        // 如果在工作
        if (isWork) {
            if (this.workState < 3) {
                this.work(3);
            }
        }
        else {
            this.bow();
        }
    };
    // 鞠躬
    RoleObj.prototype.bow = function () {
        this.setPath([]);
        this.action = 'bow';
        this.bowTime = 0.6;
    };
    // 工作开始!!!
    RoleObj.prototype.work = function (state) {
        this.workState = state;
        if (state === 1) {
            this.workStateTime = 0.7;
            this.action = 'work_begin';
            this.map.useBuildBegin(this);
        }
        else if (state === 2) {
            this.workStateTime = SIConstant_1.ROLE_WORK_TIME;
            this.action = 'work_run';
            this.map.useBuildRun(this);
        }
        else if (state === 3) {
            this.workStateTime = 0.57;
            this.action = 'work_end';
            this.map.useBuildEnd(this);
        }
        else if (state === 4) {
            this.workState = 0;
            this.workStateTime = 0;
            if (this.state === 3) {
                this.bow();
            }
            else {
                this.stroll();
            }
        }
    };
    return RoleObj;
}(MoveRoleObj_1.default));
exports.default = RoleObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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