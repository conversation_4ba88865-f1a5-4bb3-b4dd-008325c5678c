
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/MainDoor.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd0c6cYBub1PeILYmqNQlaQ/', 'MainDoor');
// app/script/model/fsp/MainDoor.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var EquipInfo_1 = require("../main/EquipInfo");
// 城门
var MainDoor = /** @class */ (function () {
    function MainDoor() {
        this.entity = null;
        this.camp = 0; //所属阵营
        this.point = cc.v2();
        // public beTargetedAddFighter(fighter: IFighter): boolean {
        //     return false
        // }
        // // 移除以该单位为攻击目标的Fighter
        // public beTargetedRemoveFighter(fighter: IFighter): boolean {
        //     return false
        // }
        // // 获取以该单位为攻击目标权重最小的Fighter
        // public getBeTargetedMinWeightFighter(): IFighter {
        //     return null
        // }
        // // 判断该单位被集火是战斗力是否足够
        // public checkTargetBeAttackedEnough(): boolean {
        //     return false
        // }
    }
    MainDoor.prototype.init = function (entity, camp, point) {
        this.entity = entity;
        this.camp = camp;
        this.point.set(point);
        return this;
    };
    MainDoor.prototype.getAreaIndex = function () { return this.entity.index; };
    MainDoor.prototype.getEnterDir = function () { return -1; };
    MainDoor.prototype.getId = function () { return 0; };
    MainDoor.prototype.getUid = function () { return this.entity.index + '_' + this.point.ID(); };
    MainDoor.prototype.getOwner = function () { var _a; return (_a = this.entity) === null || _a === void 0 ? void 0 : _a.owner; };
    MainDoor.prototype.getArmyUid = function () { return ''; };
    MainDoor.prototype.getArmyName = function () { return ''; };
    MainDoor.prototype.getName = function () { return ''; };
    MainDoor.prototype.getLv = function () { var _a; return ((_a = this.entity.wall) === null || _a === void 0 ? void 0 : _a.lv) || 1; };
    MainDoor.prototype.getCamp = function () { return this.camp; };
    MainDoor.prototype.getPoint = function () { return this.point; };
    MainDoor.prototype.getPoints = function () { return [this.point]; };
    MainDoor.prototype.getLastPoint = function () { return this.point; };
    MainDoor.prototype.getSearchRange = function () { return null; };
    MainDoor.prototype.getPawnType = function () { return Enums_1.PawnType.BUILD; };
    MainDoor.prototype.getSkillByType = function (type) { return null; };
    MainDoor.prototype.getActiveSkill = function () { return null; };
    MainDoor.prototype.getAttack = function () { return 0; };
    MainDoor.prototype.getActAttack = function () { return 0; };
    MainDoor.prototype.getIgnoreBuffAttack = function (type) { return 0; };
    MainDoor.prototype.getInstabilityMaxAttack = function () { return 0; };
    MainDoor.prototype.getAttackRange = function () { return 0; };
    MainDoor.prototype.getMoveRange = function () { return 0; };
    MainDoor.prototype.getAttackIndex = function () { return 0; };
    MainDoor.prototype.getAttackSpeed = function () { return 0; };
    MainDoor.prototype.getCurHp = function () { return this.entity.curHp; };
    MainDoor.prototype.getMaxHp = function () { return this.entity.maxHp; };
    MainDoor.prototype.amendMaxHp = function (ignoreBuffType) { return this.getMaxHp(); };
    MainDoor.prototype.getHpRatio = function () { return this.entity.getHpRatio(); };
    MainDoor.prototype.getEquip = function () { return new EquipInfo_1.default(); };
    MainDoor.prototype.getEquipEffects = function () { return []; };
    MainDoor.prototype.getEquipEffectByType = function (type) { return null; };
    MainDoor.prototype.getPortrayal = function () { return null; };
    MainDoor.prototype.getPortrayalSkill = function () { return null; };
    MainDoor.prototype.getCanByReqelPoint = function (point, range) { return null; };
    MainDoor.prototype.checkInAttackRange = function (target, attackRange) { return false; };
    MainDoor.prototype.checkInMyAttackRange = function (target) { return this.checkInAttackRange(target, this.getAttackRange()); };
    MainDoor.prototype.getMinDis = function (target) { return 0; };
    MainDoor.prototype.getAttackTarget = function () { return null; };
    MainDoor.prototype.getBlackboard = function (id) { return {}; };
    MainDoor.prototype.setTempRandomVal = function (val) { };
    MainDoor.prototype.getTempRandomVal = function () { return 0; };
    MainDoor.prototype.isPawn = function () { return false; };
    MainDoor.prototype.isBuild = function () { return true; };
    MainDoor.prototype.isTower = function () { return false; };
    MainDoor.prototype.isNoncombat = function () { return false; };
    MainDoor.prototype.isFlag = function () { return false; };
    MainDoor.prototype.isHero = function () { return false; };
    MainDoor.prototype.isBoss = function () { return false; };
    MainDoor.prototype.isDie = function () {
        return this.entity.curHp <= 0;
    };
    MainDoor.prototype.isFullHp = function () {
        return this.entity.curHp >= this.entity.maxHp;
    };
    MainDoor.prototype.setPoint = function (point) {
    };
    MainDoor.prototype.getSearchDir = function () {
        return -1;
    };
    MainDoor.prototype.amendAttack = function (val) {
        return val;
    };
    MainDoor.prototype.amendRestrainValue = function (val, tp) {
        return val;
    };
    MainDoor.prototype.getInstabilityRandomAttack = function (index) {
        return 0;
    };
    // 切换状态
    MainDoor.prototype.changeState = function (state, data) {
        if (state === Enums_1.PawnState.HIT) {
            eventCenter.emit(EventType_1.default.AREA_MAIN_HIT, {
                index: this.entity.index,
                point: this.point,
                value: -data.damage,
            });
        }
    };
    MainDoor.prototype.hitPrepDamageHandle = function (damage, trueDamage) {
        return { damage: damage, trueDamage: trueDamage };
    };
    // 受击
    MainDoor.prototype.onHit = function (damage, attackerOwners) {
        if (damage > this.entity.curHp) {
            damage = this.entity.curHp;
        }
        this.entity.curHp -= damage;
        return { damage: damage, heal: 0, hitShield: 0 };
    };
    // 回血
    MainDoor.prototype.onHeal = function (val) {
        if (this.entity.curHp + val > this.entity.maxHp) {
            val = this.entity.maxHp - this.entity.curHp;
        }
        this.entity.curHp += val;
        return val;
    };
    MainDoor.prototype.isHasAnger = function () { return false; };
    MainDoor.prototype.getAngerRatio = function () { return 0; };
    MainDoor.prototype.getCurAnger = function () { return 0; };
    // 增加怒气
    MainDoor.prototype.addAnger = function (val) {
        return 0;
    };
    // 设置怒气
    MainDoor.prototype.setAnger = function (val) {
    };
    // 扣怒气
    MainDoor.prototype.deductAnger = function (val) {
    };
    // 设置满怒气
    MainDoor.prototype.setFullAnger = function (ratio, check) {
        return 0;
    };
    MainDoor.prototype.getBuffs = function () { return []; };
    MainDoor.prototype.getBuff = function (type) {
        return null;
    };
    MainDoor.prototype.getBuffValue = function (type) {
        return 0;
    };
    MainDoor.prototype.getBuffOrAdd = function (type, provider) {
        return null;
    };
    // 添加buff
    MainDoor.prototype.addBuff = function (type, provider, lv) {
        if (lv === void 0) { lv = 1; }
        return null;
    };
    MainDoor.prototype.addBuffValue = function (type, provider, value) {
        return null;
    };
    MainDoor.prototype.checkTriggerBuff = function (type) {
        return null;
    };
    MainDoor.prototype.checkTriggerBuffOr = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        return null;
    };
    MainDoor.prototype.isHasBuff = function (type) {
        return false;
    };
    MainDoor.prototype.isHasBuffs = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        return false;
    };
    // 删除buff
    MainDoor.prototype.removeMultiBuff = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
    };
    MainDoor.prototype.removeBuff = function (type) {
        return false;
    };
    // 删除所有护盾buff
    MainDoor.prototype.removeShieldBuffs = function () {
    };
    MainDoor.prototype.getStrategyBuff = function (tp) {
        return null;
    };
    // 获取韬略数值
    MainDoor.prototype.getStrategyValue = function (tp) {
        return 0;
    };
    // 获取韬略数值
    MainDoor.prototype.getStrategyParams = function (tp) {
        return 0;
    };
    MainDoor.prototype.isHasStrategys = function () {
        var tps = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            tps[_i] = arguments[_i];
        }
        return false;
    };
    MainDoor.prototype.getShieldVal = function () {
        return 0;
    };
    MainDoor.prototype.getTargetCanAttackPoints = function (target) {
        return [];
    };
    MainDoor.prototype.getCanAttackFighters = function () {
        return [];
    };
    MainDoor.prototype.getCanAttackTargets = function () {
        return [];
    };
    MainDoor.prototype.getCanAttackPawnByRange = function (fighters, rang, cnt, ignoreUid) {
        return [];
    };
    MainDoor.prototype.getCanAttackFighterByRange = function (fighters, rang, cnt, ignoreUid) {
        return [];
    };
    MainDoor.prototype.getCanAttackRangeFighter = function (fighters, rang, cnt, ignoreUid, cb) {
        return [];
    };
    MainDoor.prototype.getCanAttackRangeFighterByPoint = function (point, fighters, rang, cnt, cb) {
        return [];
    };
    MainDoor.prototype.setAttackTarget = function (val) {
    };
    MainDoor.prototype.changeAttackTarget = function (val) {
        return null;
    };
    // 刷新最大血量记录
    MainDoor.prototype.updateMaxHpRecord = function (maxHp) {
    };
    // 获取宠物主人uid
    MainDoor.prototype.getPetMaster = function () {
        return '';
    };
    // 添加回合结束延迟时间
    MainDoor.prototype.addRoundEndDelayTime = function (val) {
    };
    return MainDoor;
}());
exports.default = MainDoor;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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