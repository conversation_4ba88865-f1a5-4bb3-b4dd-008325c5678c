
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/BaseStudyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5907a9sLtJInZD8v8dSg5V2', 'BaseStudyObj');
// app/script/model/main/BaseStudyObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
// 基础研究信息
var BaseStudyObj = /** @class */ (function () {
    function BaseStudyObj() {
        this.studyType = Enums_1.StudyType.NONE;
        this.lv = 0; //建筑等级
        this.id = 0;
        this.selectIds = []; //选择列表
        this.resetCount = 0; //重置次数
    }
    BaseStudyObj.prototype.fromSvr = function (data) {
        this.lv = data.lv || 0;
        this.id = data.id || 0;
        this.selectIds = data.selectIds || [];
        this.resetCount = data.resetCount || 0;
        return this;
    };
    BaseStudyObj.prototype.init = function () {
        return this; //需要子类重写
    };
    BaseStudyObj.prototype.getUIStudyType = function () {
        return this.studyType; //子类实现
    };
    Object.defineProperty(BaseStudyObj.prototype, "uid", {
        get: function () { return this.id + '_' + this.lv; },
        enumerable: false,
        configurable: true
    });
    // 是否可研究
    BaseStudyObj.prototype.isCanStudy = function () {
        return this.id <= 0 && this.selectIds.length > 0;
    };
    // 是否已研究
    BaseStudyObj.prototype.isYetStudy = function () {
        return this.id > 0;
    };
    // 重复研究的英雄信息
    BaseStudyObj.prototype.getRestudyHero = function () {
        var _a;
        return this.id < 0 ? (_a = GameHelper_1.gameHpr.player.getHeroSlotByPawnId(Math.abs(this.id))) === null || _a === void 0 ? void 0 : _a.hero : null;
    };
    return BaseStudyObj;
}());
exports.default = BaseStudyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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