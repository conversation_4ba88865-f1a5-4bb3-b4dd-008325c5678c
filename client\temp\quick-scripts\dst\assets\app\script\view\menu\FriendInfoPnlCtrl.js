
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/FriendInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b694b+scotKPoGQLB7nxdL9', 'FriendInfoPnlCtrl');
// app/script/view/menu/FriendInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var FriendInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(FriendInfoPnlCtrl, _super);
    function FriendInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_n
        _this.infoNode_ = null; // path://root/info_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.data = null;
        _this.from = '';
        return _this;
    }
    FriendInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_FRIEND_INFO] = this.onUpdateFriendInfo, _a.enter = true, _a),
        ];
    };
    FriendInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    FriendInfoPnlCtrl.prototype.onEnter = function (data, from) {
        this.data = data;
        this.from = from;
        this.updateUI();
    };
    FriendInfoPnlCtrl.prototype.onRemove = function () {
    };
    FriendInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/info_n/1/modify_note_be
    FriendInfoPnlCtrl.prototype.onClickModifyNote = function (event, data) {
        if (this.data) {
            ViewHelper_1.viewHelper.showPnl('menu/ModifyFriendNote', this.data);
        }
    };
    // path://root/info_n/p/button/add_popularity_be
    FriendInfoPnlCtrl.prototype.onClickAddPopularity = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.addPlayerPopularity(this.data, function (id) {
            if (_this.isValid && id) {
                _this.playAddPopularity(id);
            }
        });
    };
    // path://root/buttons_n/remove_be
    FriendInfoPnlCtrl.prototype.onClickRemove = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showMessageBox('ui.remove_friend_tip', {
            params: [ut.nameFormator(this.data.nickname, 7)],
            ok: function () { return GameHelper_1.gameHpr.friend.removeFriend(_this.data.uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                }
                ViewHelper_1.viewHelper.hidePnl('common/Chat');
            }); },
            cancel: function () { }
        });
    };
    // path://root/info_n/4/spectators_be
    FriendInfoPnlCtrl.prototype.onClickSpectators = function (event, data) {
        GameHelper_1.gameHpr.lobby.enterSpectators(this.data.uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    // path://root/buttons_n/give_gift_be
    FriendInfoPnlCtrl.prototype.onClickGiveGift = function (event, data) {
        if (this.data) {
            ViewHelper_1.viewHelper.showPnl('menu/GiveGift', this.data.uid, this.headNode_);
        }
    };
    // path://root/head_n/copy_uid_be
    FriendInfoPnlCtrl.prototype.onClickCopyUid = function (event, data) {
        GameHelper_1.gameHpr.copyToClipboard(this.data.uid, 'toast.yet_copy_clipboard');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    FriendInfoPnlCtrl.prototype.onUpdateFriendInfo = function (data) {
        if (this.data.uid === data.uid) {
            this.updateUI();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    FriendInfoPnlCtrl.prototype.updateUI = function () {
        var data = this.data, from = this.from;
        var isOwner = data.uid === GameHelper_1.gameHpr.getUid();
        ResHelper_1.resHelper.loadPlayerHead(this.headNode_.Child('val'), data.headIcon, this.key);
        this.headNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 11);
        // 个人简介
        ViewHelper_1.viewHelper.updatePlayerPersonalDesc(this.infoNode_.Child('5/val'), data.uid, data);
        // 称号
        ViewHelper_1.viewHelper.updatePlayerTitleText(this.headNode_.Child('title'), data.uid, this.key);
        // 段位
        ViewHelper_1.viewHelper.updatePlayerRankInfo(this.infoNode_.Child('rank'), data.uid, this.key, data);
        // 人气
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), data.uid, this.key, data);
        // 备注
        this.infoNode_.Child('1').active = !isOwner;
        this.infoNode_.Child('1/lay/val', cc.Label).string = data.noteName || '-';
        // 总局数
        ViewHelper_1.viewHelper.updateTotalGameCount(this.infoNode_.Child('6'), data.uid, data);
        var serverNode = this.infoNode_.Child('4/lay/server');
        this.infoNode_.Child('4/spectators_be').active = mc.currWindName === 'lobby' && from !== 'chat' && !!data.playSid && data.playSid !== GameHelper_1.gameHpr.user.getPlaySid();
        if (serverNode.active = !!data.playSid) {
            this.infoNode_.Child('4/lay/val').setLocaleKey('ui.in_game');
            var _a = GameHelper_1.gameHpr.getServerNameById(data.playSid), key = _a.key, id = _a.id;
            serverNode.setLocaleKey('ui.bracket', assetsMgr.lang(key, id));
        }
        else {
            this.infoNode_.Child('4/lay/val').setLocaleKey('ui.in_ready_war');
        }
        // 按钮
        if (this.buttonsNode_.active = !isOwner) {
        }
    };
    FriendInfoPnlCtrl.prototype.playAddPopularity = function (id) {
        // animHelper.playAddPopularity(id)
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), this.data.uid, this.key, this.data);
    };
    FriendInfoPnlCtrl = __decorate([
        ccclass
    ], FriendInfoPnlCtrl);
    return FriendInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = FriendInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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