
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/PlayForgeSoundCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5fa2bMcJB9NU4+QwwnrxdNe', 'PlayForgeSoundCmpt');
// app/script/view/build/PlayForgeSoundCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 播放打铁音效
 */
var PlayForgeSoundCmpt = /** @class */ (function (_super) {
    __extends(PlayForgeSoundCmpt, _super);
    function PlayForgeSoundCmpt() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PlayForgeSoundCmpt.prototype.onDisable = function () {
        audioMgr.stopSFX('build/sound_ui_008', 'forge');
    };
    PlayForgeSoundCmpt.prototype.onPlaySound = function () {
        audioMgr.playSFX('build/sound_ui_008', { tag: 'forge' });
    };
    PlayForgeSoundCmpt = __decorate([
        ccclass
    ], PlayForgeSoundCmpt);
    return PlayForgeSoundCmpt;
}(cc.Component));
exports.default = PlayForgeSoundCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxQbGF5Rm9yZ2VTb3VuZENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUF3QixFQUFFLENBQUMsVUFBVSxFQUFuQyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQWtCLENBQUM7QUFFNUM7O0dBRUc7QUFFSDtJQUFnRCxzQ0FBWTtJQUE1RDs7SUFTQSxDQUFDO0lBUEcsc0NBQVMsR0FBVDtRQUNJLFFBQVEsQ0FBQyxPQUFPLENBQUMsb0JBQW9CLEVBQUUsT0FBTyxDQUFDLENBQUE7SUFDbkQsQ0FBQztJQUVELHdDQUFXLEdBQVg7UUFDSSxRQUFRLENBQUMsT0FBTyxDQUFDLG9CQUFvQixFQUFFLEVBQUUsR0FBRyxFQUFFLE9BQU8sRUFBRSxDQUFDLENBQUE7SUFDNUQsQ0FBQztJQVJnQixrQkFBa0I7UUFEdEMsT0FBTztPQUNhLGtCQUFrQixDQVN0QztJQUFELHlCQUFDO0NBVEQsQUFTQyxDQVQrQyxFQUFFLENBQUMsU0FBUyxHQVMzRDtrQkFUb0Isa0JBQWtCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuLyoqXG4gKiDmkq3mlL7miZPpk4Hpn7PmlYhcbiAqL1xuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFBsYXlGb3JnZVNvdW5kQ21wdCBleHRlbmRzIGNjLkNvbXBvbmVudCB7XG5cbiAgICBvbkRpc2FibGUoKSB7XG4gICAgICAgIGF1ZGlvTWdyLnN0b3BTRlgoJ2J1aWxkL3NvdW5kX3VpXzAwOCcsICdmb3JnZScpXG4gICAgfVxuXG4gICAgb25QbGF5U291bmQoKSB7XG4gICAgICAgIGF1ZGlvTWdyLnBsYXlTRlgoJ2J1aWxkL3NvdW5kX3VpXzAwOCcsIHsgdGFnOiAnZm9yZ2UnIH0pXG4gICAgfVxufSJdfQ==