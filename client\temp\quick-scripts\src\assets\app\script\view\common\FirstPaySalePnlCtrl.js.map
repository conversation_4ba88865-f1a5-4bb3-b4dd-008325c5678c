{"version": 3, "sources": ["assets\\app\\script\\view\\common\\FirstPaySalePnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA4D;AAC5D,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAC5D,wDAAmD;AAI3C,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAiD,uCAAc;IAA/D;QAAA,qEAiKC;QA/JG,0BAA0B;QAClB,eAAS,GAAY,IAAI,CAAA,CAAC,gBAAgB;QAClD,MAAM;QAEE,UAAI,GAAc,IAAI,CAAA;QACtB,UAAI,GAAc,EAAE,CAAA;;IA0JhC,CAAC;IAxJU,6CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,sCAAQ,GAArB;;;gBACI,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAA;gBACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;;;KACpC;IAEM,qCAAO,GAAd,UAAe,IAAS;QACpB,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAEM,sCAAQ,GAAf;IACA,CAAC;IAEM,qCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,2CAA2C;IAC3C,yCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAChC,IAAI,MAAM,EAAE;YACR,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;gBACjC,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;aAC7H;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;gBACxC,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;aAC7H;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE;gBAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC9D,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;aAC/D;SACJ;IACL,CAAC;IAED,gCAAgC;IAChC,yCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,uBAAU,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAA;QAC1C,uBAAU,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,CAAC;IAED,iCAAiC;IACjC,0CAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QAArD,iBAIC;QAHG,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAE,EAAd,CAAc,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAA1B,CAA0B,CAAC,CAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,CAAC,CAAA;IAC3C,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEzG,kCAAI,GAAZ;QAAA,iBAgDC;QA/CG,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAA5B,CAA4B,CAAC,CAAA;QAC9F,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,cAAM,CAAC,cAAc,CAAA,EAAA,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAX,CAAW,CAAC,CAAA;QACvH,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,UAAU,EAAE,EAAf,CAAe,CAAC,CAAA;QAC5E,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;gCAE1C,CAAC;YACN,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,EACjB,OAAO,GAAG,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAC7C,IAAI,GAAG,OAAK,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,EAC5C,IAAI,GAAG,OAAK,SAAS,CAAC,KAAK,CAAC,iBAAe,CAAG,CAAC,CAAA;YACnD,IAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,UAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;YAC7F,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,UAAQ,CAAC,CAAA;YAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAA;YAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,IAAI;gBACxC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;gBACd,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;oBAClE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,IAAI,CAAA;oBAC3C,IAAM,MAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC/B,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;oBACjE,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,SAAS,CAAA;oBACrC,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;oBAClD,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE;wBACjC,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;wBAC/E,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAA;qBAC/F;iBACJ;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;oBACtC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,IAAI,CAAA;oBAC3C,IAAM,MAAI,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;oBAChC,uBAAU,CAAC,oBAAoB,CAAC,MAAI,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;oBAC5E,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,SAAS,CAAA;oBACrC,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;iBACrD;qBAAM;oBACH,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,KAAK,CAAA;oBAC5C,IAAM,MAAI,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;oBAChC,uBAAU,CAAC,oBAAoB,CAAC,MAAI,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;oBACrD,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,SAAS,CAAA;oBACrC,MAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,MAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;iBACnF;YACL,CAAC,CAAC,CAAA;;;QAlCN,KAAK;QACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA5B,CAAC;SAkCT;QACD,KAAK;QACL,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAE,EAAd,CAAc,CAAC,EAC3E,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAE,EAAd,CAAc,CAAC,CAAA;QAClD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7G,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACxB,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,oBAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;SACtG;IACL,CAAC;IAEO,gDAAkB,GAA1B,UAA2B,IAAa,EAAE,MAAe;QAAzD,iBAgBC;QAfG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACtD,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;YACD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAA5B,CAA4B,CAAC,CAAA;YAC9H,IAAI,IAAI,EAAE;gBACN,uBAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;aAC1D;iBAAM;gBACH,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAA;aAC9C;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;OAKG;IACK,gDAAkB,GAA1B,UAA2B,gBAAwB;QAC/C,WAAW;QACX,IAAM,WAAW,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAE9C,eAAe;QACf,IAAM,UAAU,GAAG,IAAI,IAAI,CACvB,WAAW,CAAC,WAAW,EAAE,EACzB,WAAW,CAAC,QAAQ,EAAE,EACtB,WAAW,CAAC,OAAO,EAAE,EACrB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CACb,CAAC,OAAO,EAAE,CAAA;QAEX,uBAAuB;QACvB,IAAI,gBAAgB,IAAI,UAAU,EAAE;YAChC,aAAa;YACb,IAAM,WAAW,GAAG,UAAU,GAAG,QAAQ,CAAA,CAAC,WAAW;YACrD,0BAA0B;YAC1B,OAAO,WAAW,GAAG,gBAAgB,CAAA;SACxC;aAAM;YACH,4BAA4B;YAC5B,OAAO,UAAU,GAAG,gBAAgB,CAAA;SACvC;IACL,CAAC;IAhKgB,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAiKvC;IAAD,0BAAC;CAjKD,AAiKC,CAjKgD,EAAE,CAAC,WAAW,GAiK9D;kBAjKoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { CType, TCType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport CTypeObj from \"../../model/common/CTypeObj\";\nimport TaskModel from \"../../model/common/TaskModel\";\nimport TaskObj from \"../../model/common/TaskObj\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class FirstPaySalePnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private rootNode_: cc.Node = null // path://root_n\n    //@end\n\n    private task: TaskModel = null\n    private list: TaskObj[] = []\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.setParam({ maskOpacity: 204 })\n        this.task = this.getModel('task')\n    }\n\n    public onEnter(data: any) {\n        this.init()\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root_n/rewards/day1/items/item_be\n    onClickItem(event: cc.Event.EventTouch, data: string) {\n        const reward = event.target.Data\n        if (reward) {\n            if (reward.type === CType.PAWN_SKIN) {\n                viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [assetsMgr.getJsonData('pawnSkin', reward.id)] })\n            } else if (reward.type === CType.HEAD_ICON) {\n                viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'headicon', list: [assetsMgr.getJsonData('headIcon', reward.id)] })\n            } else if (reward.type === CType.HERO_DEBRIS) {\n                const json = assetsMgr.getJsonData('portrayalBase', reward.id)\n                viewHelper.showPnl('common/PortrayalBaseInfo', json, 'shop')\n            }\n        }\n    }\n\n    // path://root_n/buttons/goto_be\n    onClickGoto(event: cc.Event.EventTouch, data: string) {\n        this.hide()\n        viewHelper.hidePnl('common/ActivitiesPnl')\n        viewHelper.showPnl('common/Shop', 0, 'ingot')\n    }\n\n    // path://root_n/buttons/claim_be\n    onClickClaim(event: cc.Event.EventTouch, data: string) {\n        const list = this.list.filter(m => m.isCanClaim())\n        list.forEach(m => this.claimGeneralReward(m))\n        this.emit(EventType.UPDATE_ACITIVITIES)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private init() {\n        const datas = assetsMgr.getJson('generalTask').datas.filter(m => m.cond.startsWith('1028,0,'))\n        this.list = this.task.getGeneralTasks().filter(m => m.cond?.type === TCType.RECHARGE_COUNT).sort((a, b) => a.id - b.id)\n        const both = this.list.length === 2 && !this.list.some(m => !m.isCanClaim())\n        this.rootNode_.Child('rewards/title').active = both\n        // 奖励\n        for (let i = 0; i < datas.length; i++) {\n            const data = datas[i],\n                rewards = gameHpr.stringToCTypes(data.reward),\n                task = this.list.find(m => m.id === data.id),\n                node = this.rootNode_.Child(`rewards/day_${i}`)\n            const isClaimed = task ? task.isClaimed() : true, canClaim = task ? task.isCanClaim() : false\n            node.Child('bg', cc.MultiFrame).setFrame(canClaim)\n            node.Child('title').active = !both\n            node.Child('items').Items(rewards, (it, data) => {\n                it.Data = data\n                if (data.type === CType.HERO_DEBRIS || data.type === CType.PAWN_SKIN) {\n                    it.Component(cc.Button).interactable = true\n                    const node = it.Swih('role')[0]\n                    resHelper.loadPawnHeadIcon(data.id, node.Child('icon'), this.key)\n                    node.Child('done').active = isClaimed\n                    node.Child('icon').opacity = isClaimed ? 150 : 255\n                    if (data.type === CType.HERO_DEBRIS) {\n                        this.rootNode_.Child('role_name').setLocaleKey('portrayalText.name_' + data.id)\n                        this.rootNode_.Child('role_type').setLocaleKey('pawnText.name_' + Math.floor(data.id / 100))\n                    }\n                } else if (data.type === CType.HEAD_ICON) {\n                    it.Component(cc.Button).interactable = true\n                    const node = it.Swih('other')[0]\n                    viewHelper.updateItemByCTypeOne(node, data, this.key, cc.size(76, 76), true)\n                    node.Child('done').active = isClaimed\n                    node.Child('icon').opacity = isClaimed ? 150 : 255\n                } else {\n                    it.Component(cc.Button).interactable = false\n                    const node = it.Swih('other')[0]\n                    viewHelper.updateItemByCTypeOne(node, data, this.key)\n                    node.Child('done').active = isClaimed\n                    node.Child('icon').opacity = node.Child('count').opacity = isClaimed ? 150 : 255\n                }\n            })\n        }\n        // 按钮\n        const showGoto = this.list.length === 2 && !this.list.some(m => m.isCanClaim()),\n            canClaim = this.list.some(m => m.isCanClaim())\n        const button = this.rootNode_.Child('buttons').Swih(showGoto ? 'goto_be' : canClaim ? 'claim_be' : 'time')[0]\n        if (!showGoto && !canClaim) {\n            button.Child('val', cc.LabelTimer).run(this.getNextClaimTimeMs(gameHpr.getServerNowTime()) * 0.001)\n        }\n    }\n\n    private claimGeneralReward(data: TaskObj, heroId?: number) {\n        this.task.claimGeneralTaskReward(data.id, heroId).then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            }\n            const hero = heroId ? new CTypeObj().init(CType.HERO_DEBRIS, heroId, 3) : data.rewards.find(m => m.type === CType.HERO_DEBRIS)\n            if (hero) {\n                viewHelper.showGainPortrayalDebris(hero.id, hero.count)\n            } else {\n                gameHpr.addGainMassage(data.rewards)\n                viewHelper.showAlert('toast.claim_succeed')\n            }\n            if (this.isValid) {\n                this.init()\n            }\n        })\n    }\n\n    /**\n     * 获取距离下次可领奖的剩余毫秒数\n     * 基于当前时间判断，每天6点后可领奖一次\n     * @param currentTimestamp 当前时间戳（毫秒）\n     * @returns 距离下次可领奖的毫秒数（0表示可以立即领奖）\n     */\n    private getNextClaimTimeMs(currentTimestamp: number): number {\n        // 创建当前日期对象\n        const currentDate = new Date(currentTimestamp)\n\n        // 获取当前日期的6点时间戳\n        const current6AM = new Date(\n            currentDate.getFullYear(),\n            currentDate.getMonth(),\n            currentDate.getDate(),\n            6, 0, 0, 0\n        ).getTime()\n\n        // 如果当前时间已经超过今天6点，则可以领奖\n        if (currentTimestamp >= current6AM) {\n            // 计算明天6点的时间戳\n            const tomorrow6AM = current6AM + 86400000 // 加上一天的毫秒数\n            // 返回距离明天6点的毫秒数（表示下次可领奖时间）\n            return tomorrow6AM - currentTimestamp\n        } else {\n            // 如果当前时间早于今天6点，返回距离今天6点的毫秒数\n            return current6AM - currentTimestamp\n        }\n    }\n}\n"]}