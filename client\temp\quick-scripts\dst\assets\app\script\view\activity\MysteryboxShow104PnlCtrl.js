
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/activity/MysteryboxShow104PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '99e0bUC/tVADLQmNRNb2qFK', 'MysteryboxShow104PnlCtrl');
// app/script/view/activity/MysteryboxShow104PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MysteryboxShow104PnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxShow104PnlCtrl, _super);
    function MysteryboxShow104PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.posNode_ = null; // path://root/pos_n
        _this.rootNode_ = null; // path://root/root_nbe_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.descLbl_ = null; // path://root/desc_l
        //@end
        _this.MYSTERY_BOX_ID = 104;
        _this.boxId = 0;
        _this.ignot = 0;
        _this.ANIMATIONS = ['mysterybox_anim_104_normal', 'mysterybox_anim_104_special'];
        _this.EFFECT_ANIMS = ['mysterybox_anim_103_effect1', 'mysterybox_anim_103_effect2'];
        _this.lockTouch = false;
        return _this;
    }
    MysteryboxShow104PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxShow104PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MysteryboxShow104PnlCtrl.prototype.onEnter = function (id, ignot) {
        this.boxId = id;
        this.ignot = ignot;
        this.init();
    };
    MysteryboxShow104PnlCtrl.prototype.onRemove = function () {
    };
    MysteryboxShow104PnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    MysteryboxShow104PnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    // path://root/root_nbe_n
    MysteryboxShow104PnlCtrl.prototype.onClickRoot = function (event, data) {
        if (this.lockTouch)
            return;
        audioMgr.playSFX('click');
        this.do(event.target.name);
    };
    // path://root/button_n/once_again_be
    MysteryboxShow104PnlCtrl.prototype.onClickOnceAgain = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.user.getIngot() < this.ignot) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
        }
        else if (!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
            ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', this.MYSTERY_BOX_ID, function () { return _this.isValid && _this.init(); });
        }
        else {
            this.init();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MysteryboxShow104PnlCtrl.prototype.init = function () {
        var _this = this;
        this.lockTouch = false;
        this.rootNode_.children.forEach(function (m) {
            var _a;
            m.active = true;
            m.opacity = 255;
            var effect = m.Child('item/effect', cc.Animation);
            effect.reset();
            m.Child('count').active = false;
            m.Component(cc.Button).interactable = false;
            var anim = m.Child('item', cc.Animation), name = (_a = m.Data) !== null && _a !== void 0 ? _a : _this.ANIMATIONS[0];
            anim.reset(name);
            m.Child('item/card/val', cc.Sprite).spriteFrame = null;
            m.setPosition(0, 0);
            var target = _this.posNode_.Child(m.name).getPosition();
            m.stopAllActions();
            cc.tween(m)
                .to(0.3, { x: target.x, y: target.y }, { easing: cc.easing.sineOut })
                .call(function () { return m.Component(cc.Button).interactable = true; })
                .start();
        });
        this.buttonNode_.active = false;
        this.descLbl_.setLocaleKey('');
        ut.wait(0.4, this).then(function () {
            if (_this.isValid) {
                _this.descLbl_.setLocaleKey('ui.please_select_mb_box_102');
            }
        });
    };
    // 购买盲盒
    MysteryboxShow104PnlCtrl.prototype.do = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, json, _loop_1, this_1, i, l, isHideCard, it, anim;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.lockTouch = true;
                        mc.lockTouch('item_skin');
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuySkinBlindBox', { id: this.boxId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            this.lockTouch = false;
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.descLbl_.setLocaleKey('');
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.setSkinItemList(data.skinItemList);
                        json = assetsMgr.getJsonData('pawnSkin', data.skinId);
                        if (!json) {
                            this.lockTouch = false;
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.UNKNOWN)];
                        }
                        _loop_1 = function (i, l) {
                            var it_1 = this_1.rootNode_.children[i];
                            it_1.Component(cc.Button).interactable = false;
                            if (it_1.name !== index) {
                                cc.tween(it_1)
                                    .to(0.3, { opacity: 0 })
                                    .call(function () { return it_1.active = false; })
                                    .start();
                            }
                        };
                        this_1 = this;
                        for (i = 0, l = this.rootNode_.children.length; i < l; i++) {
                            _loop_1(i, l);
                        }
                        isHideCard = json.value === 2;
                        it = this.rootNode_.Child(index);
                        anim = it.Data = isHideCard ? this.ANIMATIONS[1] : this.ANIMATIONS[0];
                        cc.tween(it)
                            .to(0.3, { x: 0, y: 0 }, { easing: cc.easing.sineIn })
                            .call(function () {
                            var item = it.Child('item');
                            var spr = item.Child('card/val', cc.Sprite);
                            ResHelper_1.resHelper.loadPawnHeadIcon(data.skinId, spr, _this.key);
                            var time = Date.now();
                            item.Component(cc.Animation).playAsync(anim).then(function () {
                                var _a;
                                // console.log('cost time: ', Date.now() - time)
                                var count = ((_a = GameHelper_1.gameHpr.user.getSkinItemList().filter(function (m) { return m.id === data.skinId; })) === null || _a === void 0 ? void 0 : _a.length) || 1;
                                it.Child('count').active = true;
                                it.Child('count/val', cc.Label).string = Math.max(0, count - 1) + '';
                                _this.closeNode_.active = true;
                                _this.buttonNode_.active = true;
                                _this.descLbl_.setLocaleKey('ui.click_close_desc');
                                _this.lockTouch = false;
                                mc.unlockTouch('item_skin');
                            });
                            ut.wait(0.72).then(function () {
                                var effectAni = item.Child('effect', cc.Animation);
                                effectAni.play(isHideCard ? _this.EFFECT_ANIMS[1] : _this.EFFECT_ANIMS[0]);
                            });
                        })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxShow104PnlCtrl = __decorate([
        ccclass
    ], MysteryboxShow104PnlCtrl);
    return MysteryboxShow104PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxShow104PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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