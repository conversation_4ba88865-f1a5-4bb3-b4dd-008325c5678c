
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/AdHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e84femO+alGVr/rU87EJJc+', 'AdHelper');
// app/script/common/helper/AdHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adHelper = void 0;
var LocalConfig_1 = require("../LocalConfig");
var NativeRewardAd_1 = require("../ad/NativeRewardAd");
var ShareAd_1 = require("../ad/ShareAd");
var WxRewardAd_1 = require("../ad/WxRewardAd");
var Enums_1 = require("../constant/Enums");
var GameHelper_1 = require("./GameHelper");
var TaHelper_1 = require("./TaHelper");
var JsbHelper_1 = require("./JsbHelper");
var EventReportHelper_1 = require("./EventReportHelper");
var DhHelper_1 = require("./DhHelper");
var JsbEvent_1 = require("../event/JsbEvent");
var REWARD_AD_MIN_WATCH_TIME = 3; //微信广告最短播放时间
/**
 * 广告
 */
var AdHelper = /** @class */ (function () {
    function AdHelper() {
        this.inited = false;
        this.rewardAd = null;
        this.shareAd = null;
        this.currAdType = null;
    }
    AdHelper.prototype.isInited = function () {
        return this.inited;
    };
    AdHelper.prototype.init = function () {
        var _this = this;
        if (this.inited) {
            return;
        }
        this.inited = true;
        if (ut.isWechatGame()) {
            this.rewardAd = new WxRewardAd_1.default();
        }
        else if (ut.isMobile()) {
            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.INIT_AD);
            if (GameHelper_1.gameHpr.isGLobal()) {
                this.rewardAd = new NativeRewardAd_1.default();
            }
            JsbHelper_1.jsbHelper.on(JsbEvent_1.default.GET_AD_INFO, function (result) {
                if (result.error) {
                }
                else {
                    result.currency_type = 'USD';
                    result.lifetimeCurrency_type = 'USD';
                    TaHelper_1.taHelper.track('ironSource_sdk_postbacks', result);
                    EventReportHelper_1.eventReportHelper.reportFacebookEvent('AdImpression', { valueToSum: result.pay_amount, ad_type: 'rewarded_video' });
                    EventReportHelper_1.eventReportHelper.reportAppflyerEvent('ad_impression', { af_revenue: result.pay_amount, af_currency: result.currency_type, af_adrev_ad_type: 'rewarded_video' });
                    DhHelper_1.dhHelper.reportAd(result);
                }
            });
        }
        // 分享广告
        this.shareAd = new ShareAd_1.default().init();
        // 一直刷新加载
        setInterval(function () {
            var _a;
            (_a = _this.rewardAd) === null || _a === void 0 ? void 0 : _a.update(1);
            _this.shareAd.update(1);
        }, ut.Time.Second);
    };
    AdHelper.prototype.getRewardFailTime = function () {
        var _a;
        return ((_a = this.rewardAd) === null || _a === void 0 ? void 0 : _a.getFailTime()) || 0;
    };
    AdHelper.prototype.resetAdFailTime = function () {
        var _a;
        (_a = this.rewardAd) === null || _a === void 0 ? void 0 : _a.resetAdFailTime();
    };
    // 获取当前广告类型
    AdHelper.prototype.initCurrentAdType = function () {
        var _a, _b;
        if ((_a = this.rewardAd) === null || _a === void 0 ? void 0 : _a.isReady()) {
            this.currAdType = Enums_1.AdType.REWARD;
        }
        else if ((_b = this.shareAd) === null || _b === void 0 ? void 0 : _b.isReady()) {
            this.currAdType = Enums_1.AdType.SHARE;
        }
        else {
            this.currAdType = Enums_1.AdType.REWARD;
        }
        return this.currAdType;
    };
    AdHelper.prototype.getCurrentAdType = function () {
        return this.currAdType;
    };
    AdHelper.prototype.resetAd = function () {
        this.currAdType = null;
    };
    AdHelper.prototype.isReady = function (init) {
        var _a, _b;
        if (init === void 0) { init = false; }
        if (!LocalConfig_1.localConfig.RELEASE) {
            return true;
        }
        else if (!this.inited) {
            return true; //如果没有初始化 这么放回成功 让他点按钮
        }
        else if (init) {
            this.initCurrentAdType();
        }
        var type = this.currAdType;
        if (type === Enums_1.AdType.REWARD) {
            return !!((_a = this.rewardAd) === null || _a === void 0 ? void 0 : _a.isReady());
        }
        else if (type == Enums_1.AdType.SHARE) {
            return !!((_b = this.shareAd) === null || _b === void 0 ? void 0 : _b.isReady());
        }
        return false;
    };
    AdHelper.prototype.show = function () {
        return __awaiter(this, void 0, void 0, function () {
            var status, type;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!LocalConfig_1.localConfig.RELEASE) {
                            return [2 /*return*/, true];
                        }
                        status = Enums_1.AdPlayState.FAIL;
                        type = this.currAdType;
                        if (!(type === Enums_1.AdType.REWARD && this.rewardAd)) return [3 /*break*/, 2];
                        TaHelper_1.taHelper.track('ta_adClick', { ad_id: 0 });
                        return [4 /*yield*/, this.showRewardAd()];
                    case 1:
                        status = _a.sent();
                        if (status === Enums_1.AdPlayState.SUCCESS) {
                            TaHelper_1.taHelper.track('ta_adSuccess', { ad_id: 0 });
                        }
                        return [3 /*break*/, 4];
                    case 2:
                        if (!(type === Enums_1.AdType.SHARE && this.shareAd)) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.shareAd.show()];
                    case 3:
                        status = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (status === Enums_1.AdPlayState.SUCCESS) {
                            this.resetAd();
                            return [2 /*return*/, true];
                        }
                        else if (status === Enums_1.AdPlayState.FAIL) {
                        }
                        else if (status === Enums_1.AdPlayState.SHARE_FAIL) {
                        }
                        else {
                            // viewHelper.showAlert('toast.wheel_turn_fail_1')
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    AdHelper.prototype.showRewardAd = function () {
        return __awaiter(this, void 0, Promise, function () {
            var isReady, now, suc, passTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.rewardAd.waitCacheReady()];
                    case 1:
                        isReady = _a.sent();
                        if (!isReady) {
                            return [2 /*return*/, Enums_1.AdPlayState.LOAD_CACHE_FAIL];
                        }
                        // reportHelper.reportGDTEvent('gt_ad_show', { play_id: index })
                        audioMgr.pauseAll();
                        now = Date.now();
                        return [4 /*yield*/, this.rewardAd.show()];
                    case 2:
                        suc = _a.sent();
                        passTime = Date.now() - now;
                        if (!ut.isWechatGame() && passTime < REWARD_AD_MIN_WATCH_TIME * ut.Time.Second) { //防作弊 - 兼容微信平台广告跳过卡功能
                            suc = false;
                        }
                        audioMgr.resumeAll();
                        // reportHelper.reportGDTEvent('gt_ad_show_end', { play_id: index })
                        return [2 /*return*/, suc ? Enums_1.AdPlayState.SUCCESS : Enums_1.AdPlayState.FAIL];
                }
            });
        });
    };
    return AdHelper;
}());
exports.adHelper = new AdHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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