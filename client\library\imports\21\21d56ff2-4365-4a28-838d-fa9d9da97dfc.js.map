{"version": 3, "sources": ["assets\\app\\script\\view\\common\\SubscriptionDescPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAC5D,2DAA0D;AAE1D,2DAA4D;AAC5D,qDAA8D;AAGtD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAqD,2CAAc;IAAnE;QAAA,qEAgLC;QA9KG,0BAA0B;QAClB,eAAS,GAAa,IAAI,CAAA,CAAC,uCAAuC;QAClE,eAAS,GAAY,IAAI,CAAA,CAAC,gCAAgC;QAC1D,oBAAc,GAAY,IAAI,CAAA,CAAC,sCAAsC;QACrE,kBAAY,GAAY,IAAI,CAAA,CAAC,mCAAmC;QAChE,6BAAuB,GAAY,IAAI,CAAA,CAAC,yDAAyD;QACzG,MAAM;QAEE,UAAI,GAAc,IAAI,CAAA;QACtB,0BAAoB,GAAU,EAAE,CAAA;QAChC,cAAQ,GAAoB,uBAAe,CAAC,IAAI,CAAA;;IAoK5D,CAAC;IAlKU,iDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,0CAAQ,GAArB;;;;;;wBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;wBACjC,qBAAM,qBAAS,CAAC,YAAY,EAAE,EAAA;;wBAA9B,SAA8B,CAAA;wBACjB,qBAAM,qBAAS,CAAC,gBAAgB,EAAE,EAAA;;wBAAzC,IAAI,GAAG,SAAkC;wBAC/C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;;;;;KAC3C;IAEM,yCAAO,GAAd,UAAe,IAAqB;QAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAM,KAAK,GAAG,qBAAU,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAA;QACxD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;QAC7D,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,CAAA;QAC3D,IAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;QAChE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAA;QAC9B,SAAS,IAAI,WAAW,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAA;QAC1F,IAAM,IAAI,GAAG,qBAAU,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;QAC9G,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;QACvH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACjE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;QACnE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,KAAK,uBAAe,CAAC,KAAK,CAAA;QAEzG,IAAM,MAAM,GAAG,IAAI,KAAK,uBAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChK,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,8BAA8B,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;QAEtF,IAAM,SAAS,GAAG,qBAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAA;QACvD,IAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,EAAxC,CAAwC,CAAC,CAAA;QACzF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAA;QAC3E,IAAI,CAAC,GAAG,EAAE;YACN,IAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC1E,IAAM,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAA;YAC1E,IAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAA;YAC5E,IAAM,GAAG,GAAG,GAAG,CAAA;YACf,IAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACpF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACnF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;YAClF,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;SACxF;IACL,CAAC;IAEM,0CAAQ,GAAf;IACA,CAAC;IAEM,yCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,2EAA2E;IAC3E,sDAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QAA7D,iBAGC;QAFG,IAAM,EAAE,GAAG,qBAAU,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,KAAI,CAAC,QAAQ,EAAxB,CAAwB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACtE,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAC7B,CAAC;IAED,2EAA2E;IAC3E,sDAAoB,GAApB,UAAqB,KAA0B,EAAE,IAAY;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAChD,CAAC;IAED,4EAA4E;IAC5E,uDAAqB,GAArB,UAAsB,KAA0B,EAAE,IAAY;QAC1D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;IAClD,CAAC;IAED,uCAAuC;IACvC,8CAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,oBAAO,CAAC,mBAAmB,EAAE,CAAC,CAAA;IACjD,CAAC;IAED,uCAAuC;IACvC,8CAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACjD,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,oBAAO,CAAC,mBAAmB,EAAE,CAAC,CAAA;IACjD,CAAC;IAED,kDAAkD;IAClD,mDAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACtD,IAAI,CAAC,OAAO,EAAE,CAAA;IAClB,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEzG,4CAAU,GAAlB,UAAmB,SAAiB;QAApC,iBAgBC;QAfG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACrB,OAAO,uBAAU,CAAC,cAAc,CAAC,+BAA+B,EAAE;gBAC9D,MAAM,EAAE,wBAAwB;gBAChC,EAAE,EAAE,cAAM,OAAA,uBAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAxC,CAAwC;gBAClD,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;aAAM,IAAI,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAE;YAClC,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SAC5D;QACD,qBAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;YACnC,MAAM,CAAC,KAAK,CAAC,wBAAwB,GAAG,EAAE,GAAG,YAAY,GAAG,KAAI,CAAC,OAAO,CAAC,CAAA;YACzE,IAAI,KAAI,CAAC,OAAO,IAAI,EAAE,EAAE;gBACpB,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,iDAAe,GAAvB,UAAwB,QAAyB,EAAE,IAAY;QAA/D,iBAeC;QAdG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACrB,OAAO,uBAAU,CAAC,cAAc,CAAC,+BAA+B,EAAE;gBAC9D,MAAM,EAAE,wBAAwB;gBAChC,EAAE,EAAE,cAAM,OAAA,uBAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAxC,CAAwC;gBAClD,MAAM,EAAE,cAAQ,CAAC;aACpB,CAAC,CAAA;SACL;aAAM,IAAI,CAAC,qBAAS,CAAC,YAAY,EAAE,EAAE;YAClC,OAAO,uBAAU,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAA;SAC5D;QACD,qBAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;YAC7C,IAAI,KAAI,CAAC,OAAO,IAAI,EAAE,EAAE;gBACpB,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACO,yCAAO,GAArB;;;;;;wBACI,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;4BACxC,sBAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAA;yBACrC;wBACK,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAA;wBACpB,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE;gCACvE,SAAS,EAAE,KAAK,CAAC,SAAS;gCAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;gCACtB,SAAS,EAAE,KAAK,CAAC,SAAS;gCAC1B,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gCACxB,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,YAAY,EAAE,KAAK,CAAC,YAAY;gCAChC,YAAY,EAAE,KAAK,CAAC,YAAY;gCAChC,SAAS,EAAE,KAAK,CAAC,SAAS;6BAC7B,EAAE,IAAI,CAAC,EAAA;;wBAVF,KAAgB,SAUd,EAVA,GAAG,SAAA,EAAE,IAAI,UAAA;6BAWb,CAAC,IAAI,CAAC,OAAO,EAAb,wBAAa;wBACb,sBAAM;;6BACC,CAAA,GAAG,KAAK,aAAK,CAAC,oBAAoB,IAAI,GAAG,KAAK,aAAK,CAAC,sBAAsB,CAAA,EAA1E,wBAA0E;wBACjF,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAA;wBACjC,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;wBAChC,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAA;;wBAAlE,SAAkE,CAAA;wBAClE,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBACjC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;4BACtC,IAAI,CAAC,OAAO,EAAE,CAAA;yBACjB;6BAAM;4BACH,qBAAS,CAAC,iBAAiB,EAAE,CAAA;4BAC7B,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;4BACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;yBAC9B;wBACD,sBAAM;;wBACH,IAAI,GAAG,EAAE;4BACZ,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;6BAAM;4BACH,uBAAU,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAA;4BACrD,IAAI,CAAC,IAAI,EAAE,CAAA;yBACd;;;;;;KACJ;IA/KgB,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CAgL3C;IAAD,8BAAC;CAhLD,AAgLC,CAhLoD,EAAE,CAAC,WAAW,GAgLlE;kBAhLoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { ecode } from \"../../common/constant/ECode\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { payHelper } from \"../../common/helper/PayHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport { jsbHelper } from \"../../common/helper/JsbHelper\";\nimport UserModel from \"../../model/common/UserModel\";\nimport { MONTH_CARD } from \"../../common/constant/Constant\";\nimport { MonthlyCardType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class SubscriptionDescPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private titleLbl_: cc.Label = null // path://root/bg/content/title/title_l\n    private infoNode_: cc.Node = null // path://root/bg/content/info_n\n    private totalDescNode_: cc.Node = null // path://root/bg/content/total_desc_n\n    private buttonsNode_: cc.Node = null // path://root/bg/content/buttons_n\n    private subscriptionButtonNode_: cc.Node = null // path://root/bg/content/buttons_n/subscription_button_n\n    //@end\n\n    private user: UserModel = null\n    private restoreSubscriptions: any[] = []\n    private cardType: MonthlyCardType = MonthlyCardType.SALE\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.user = this.getModel('user')\n        await payHelper.checkPayInit()\n        const list = await payHelper.getSubscriptions()\n        this.restoreSubscriptions = list.slice()\n    }\n\n    public onEnter(type: MonthlyCardType) {\n        this.cardType = type\n        const index = MONTH_CARD.findIndex(m => m.TYPE === type)\n        this.titleLbl_.setLocaleKey('ui.monthly_card_' + (index + 1))\n        const isFirsPay = !this.user.getRechargeCountRecord()[type]\n        const firstPayLbl = this.infoNode_.Child('1/content/layout/val')\n        firstPayLbl.active = isFirsPay\n        isFirsPay && firstPayLbl.setLocaleKey('ui.bracket', assetsMgr.lang('ui.first_pay_double'))\n        const data = MONTH_CARD[index]\n        this.infoNode_.Child('1/content/count', cc.Label).string = isFirsPay ? 'x' + data.FIRST * 2 : 'x' + data.FIRST\n        this.infoNode_.Child('1/tip_desc').setLocaleKey('ui.subscription_imm_tip', isFirsPay ? data.FIRST * 4 : data.FIRST * 3)\n        this.infoNode_.Child('2/count', cc.Label).string = 'x' + data.DAY\n        this.infoNode_.Child('3/count', cc.Label).string = 'x' + data.EXTRA\n        this.infoNode_.Child('2/line').active = this.infoNode_.Child('3').active = type === MonthlyCardType.SUPER\n\n        const params = type === MonthlyCardType.SALE ? [data.DURATION, data.DAY * data.DURATION] : [data.DURATION, data.DAY * data.DURATION, data.EXTRA * data.DURATION]\n        this.totalDescNode_.setLocaleKey('ui.subscription_total_value_' + (index + 1), params)\n\n        const monthCard = MONTH_CARD.find(m => m.TYPE === type)\n        const has = this.restoreSubscriptions.some(m => monthCard.RESTORES.includes(m.productId))\n        this.buttonsNode_.Swih(has ? 'restore_buy_be' : 'subscription_button_n')[0]\n        if (!has) {\n            const node1 = this.subscriptionButtonNode_.Child('subscription_1_be/root')\n            const node7 = this.subscriptionButtonNode_.Child('subscription_7_be/root')\n            const node30 = this.subscriptionButtonNode_.Child('subscription_30_be/root')\n            const key = '0'\n            const it1 = node1.Swih(key)[0], it7 = node7.Swih(key)[0], it30 = node30.Swih(key)[0]\n            it1.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'month', false)\n            it7.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'month', true)\n            it30.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'quarter', true)\n        }\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/bg/content/buttons_n/subscription_button_n/subscription_1_be\n    onClickSubscription1(event: cc.Event.EventTouch, data: string) {\n        const id = MONTH_CARD.find(m => m.TYPE === this.cardType).RECHARGES[0]\n        id && this.buyProduct(id)\n    }\n\n    // path://root/bg/content/buttons_n/subscription_button_n/subscription_7_be\n    onClickSubscription7(event: cc.Event.EventTouch, data: string) {\n        this.buySubScription(this.cardType, 'month')\n    }\n\n    // path://root/bg/content/buttons_n/subscription_button_n/subscription_30_be\n    onClickSubscription30(event: cc.Event.EventTouch, data: string) {\n        this.buySubScription(this.cardType, 'quarter')\n    }\n\n    // path://root/bg/content/desc/desc1_be\n    onClickDesc1(event: cc.Event.EventTouch, data: string) {\n        cc.sys.openURL(gameHpr.getPrivacyPolicyUrl())\n    }\n\n    // path://root/bg/content/desc/desc2_be\n    onClickDesc2(event: cc.Event.EventTouch, data: string) {\n        cc.sys.openURL(gameHpr.getUserAgreementUrl())\n    }\n\n    // path://root/bg/content/buttons_n/restore_buy_be\n    onClickRestoreBuy(event: cc.Event.EventTouch, data: string) {\n        this.restore()\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private buyProduct(productId: string) {\n        if (this.user.isGuest()) {\n            return viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {\n                okText: 'ui.button_bind_account',\n                ok: () => viewHelper.showPnl('common/BindAccount'),\n                cancel: () => { }\n            })\n        } else if (!payHelper.isInitFinish()) {\n            return viewHelper.showAlert('toast.please_wait_init_pay')\n        }\n        payHelper.buyProduct(productId).then(ok => {\n            logger.print('6.buyProduct end. suc=' + ok + ', isValid=' + this.isValid)\n            if (this.isValid && ok) {\n                this.hide()\n            }\n        })\n    }\n\n    private buySubScription(cardType: MonthlyCardType, type: string) {\n        if (this.user.isGuest()) {\n            return viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {\n                okText: 'ui.button_bind_account',\n                ok: () => viewHelper.showPnl('common/BindAccount'),\n                cancel: () => { }\n            })\n        } else if (!payHelper.isInitFinish()) {\n            return viewHelper.showAlert('toast.please_wait_init_pay')\n        }\n        payHelper.buySubscription(cardType, type).then(ok => {\n            if (this.isValid && ok) {\n                this.hide()\n            }\n        })\n    }\n\n    // 恢复购买\n    private async restore() {\n        if (this.restoreSubscriptions.length === 0) {\n            return this.onEnter(this.cardType)\n        }\n        const order = this.restoreSubscriptions[0]\n        const { err, data } = await gameHpr.net.request('lobby/HD_VerifySubOrder', {\n            productId: order.productId,\n            orderId: order.orderId,\n            cpOrderId: order.cpOrderId,\n            token: order.token,\n            platform: order.platform,\n            price: order.price,\n            purchaseTime: order.purchaseTime,\n            currencyType: order.currencyType,\n            payAmount: order.payAmount,\n        }, true)\n        if (!this.isValid) {\n            return\n        } else if (err === ecode.SUBSCRIPTION_TIMEOUT || err === ecode.ORDER_VERIFY_API_ERROR) { //过期了\n            this.restoreSubscriptions.shift()\n            viewHelper.showLoadingWait(true)\n            await jsbHelper.consumeOrder({ token: order.token, type: 'subs' })\n            viewHelper.showLoadingWait(false)\n            if (this.restoreSubscriptions.length > 0) {\n                this.restore()\n            } else {\n                payHelper.cleanRestoredSubs()\n                viewHelper.showAlert(err)\n                this.onEnter(this.cardType)\n            }\n            return\n        } else if (err) {\n            return viewHelper.showAlert(err)\n        } else {\n            viewHelper.showAlert('toast.restore_buy_sub_succeed')\n            this.hide()\n        }\n    }\n}\n"]}