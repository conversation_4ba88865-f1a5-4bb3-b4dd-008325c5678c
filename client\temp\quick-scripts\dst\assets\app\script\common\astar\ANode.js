
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/ANode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e395cofvLVL9qLLnwJmpbpo', 'ANode');
// app/script/common/astar/ANode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个节点
var ANode = /** @class */ (function () {
    function ANode() {
        this.uid = '0_0';
        this.point = cc.v2(0, 0);
        this.parent = null;
        this.dir = cc.v2(); //相对上一个格子的方向
        this.F = 0;
        this.G = 0;
        this.H = 0;
    }
    Object.defineProperty(ANode.prototype, "x", {
        get: function () { return this.point.x; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ANode.prototype, "y", {
        get: function () { return this.point.y; },
        enumerable: false,
        configurable: true
    });
    ANode.prototype.ID = function () { return this.uid; };
    ANode.prototype.init = function (x, y) {
        this.uid = x + '_' + y;
        this.point.set2(x, y);
        this.parent = null;
        this.F = 0;
        this.G = 0;
        this.H = 0;
        return this;
    };
    ANode.prototype.has = function (x, y) {
        return this.point.x === x && this.point.y === y;
    };
    ANode.prototype.updateParent = function (node, tag) {
        this.parent = node;
        this.point.sub(node.point, this.dir);
        this.G = node.G + tag;
        this.F = this.H + this.G;
    };
    return ANode;
}());
exports.default = ANode;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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