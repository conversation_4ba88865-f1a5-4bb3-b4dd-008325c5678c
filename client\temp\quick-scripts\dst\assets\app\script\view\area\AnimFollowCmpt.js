
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AnimFollowCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '84696938PxIE6NeemwvO7Wo', 'AnimFollowCmpt');
// app/script/view/area/AnimFollowCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var REPETITION_MAP = {
    'role_3702_23': 'role_3702_15',
    'role_3702_24': 'role_3702_16',
    'role_3702_25': 'role_3702_17',
    'role_3702_26': 'role_3702_18',
    'role_3702_27': 'role_3702_19',
    'role_3702_28': 'role_3702_20',
    'role_3702_29': 'role_3702_21',
    'role_3702_30': 'role_3702_22',
    'role_3702_31': 'role_3702_15',
    'role_3702_32': 'role_3702_16',
    'role_3702_33': 'role_3702_17',
    'role_3702_34': 'role_3702_18',
    'role_3702_35': 'role_3702_19',
    'role_3702_36': 'role_3702_20',
    'role_3702_37': 'role_3702_21',
    'role_3702_38': 'role_3702_22',
};
// 遗迹建筑
var AnimFollowCmpt = /** @class */ (function (_super) {
    __extends(AnimFollowCmpt, _super);
    function AnimFollowCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.target = null;
        _this.frames = [];
        _this.frameMap = {};
        _this.spr = null;
        _this.preFrameName = '';
        return _this;
    }
    AnimFollowCmpt.prototype.onLoad = function () {
        var _this = this;
        this.spr = this.getComponent(cc.Sprite);
        this.frameMap = {};
        this.frames.forEach(function (m) { return _this.frameMap[m.name] = m; });
    };
    AnimFollowCmpt.prototype.update = function (dt) {
        var _a, _b, _c;
        if (!this.spr || !((_a = this.target) === null || _a === void 0 ? void 0 : _a.spriteFrame)) {
            return;
        }
        var targetFrameName = this.target.spriteFrame.name;
        if (this.preFrameName !== targetFrameName) {
            this.preFrameName = targetFrameName;
            var name = (_b = REPETITION_MAP[targetFrameName]) !== null && _b !== void 0 ? _b : targetFrameName;
            this.spr.spriteFrame = (_c = this.frameMap[name + '_0']) !== null && _c !== void 0 ? _c : null;
        }
    };
    __decorate([
        property(cc.Sprite)
    ], AnimFollowCmpt.prototype, "target", void 0);
    __decorate([
        property([cc.SpriteFrame])
    ], AnimFollowCmpt.prototype, "frames", void 0);
    AnimFollowCmpt = __decorate([
        ccclass
    ], AnimFollowCmpt);
    return AnimFollowCmpt;
}(cc.Component));
exports.default = AnimFollowCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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