
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/DonateAncientSUpPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd86993Ds/9JYKTJmNrvQHha', 'DonateAncientSUpPnlCtrl');
// app/script/view/build/DonateAncientSUpPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var DonateAncientSUpPnlCtrl = /** @class */ (function (_super) {
    __extends(DonateAncientSUpPnlCtrl, _super);
    function DonateAncientSUpPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.boxSelectNode_ = null; // path://root/sell/box_select_be_n
        _this.inputEb_ = null; // path://root/sell/input_eb_ebee
        //@end
        _this.type = Enums_1.CType.NONE;
        _this.maxCount = 0;
        _this.cb = null;
        return _this;
    }
    DonateAncientSUpPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DonateAncientSUpPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DonateAncientSUpPnlCtrl.prototype.onEnter = function (maxCount, cb) {
        this.maxCount = maxCount;
        this.cb = cb;
        this.inputEb_.string = '';
        this.inputEb_.setPlaceholder('ui.max_donate_count', 'f_m', maxCount);
        this.selectBoxItem(this.boxSelectNode_, this.type || Enums_1.CType.CEREAL);
    };
    DonateAncientSUpPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0, 0);
        this.cb = null;
    };
    DonateAncientSUpPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/sell/box_select_be_n
    DonateAncientSUpPnlCtrl.prototype.onClickBoxSelect = function (event, data) {
        this.changeBoxList(event.target, true);
    };
    // path://root/sell/box_select_be_n/box_mask_be
    DonateAncientSUpPnlCtrl.prototype.onClickBoxMask = function (event, data) {
        this.changeBoxList(event.target.parent, false);
    };
    // path://root/sell/box_select_be_n/items_nbe
    DonateAncientSUpPnlCtrl.prototype.onClickItems = function (event, data) {
        var node = event.target, it = node.parent.parent;
        this.changeBoxList(it, false);
        this.selectBoxItem(it, Number(node.name));
    };
    // path://root/sell/input_eb_ebee
    DonateAncientSUpPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var count = Number(event.string.trim());
        if (!count) {
            event.string = '';
            return;
        }
        var cnt = count;
        if (count > this.maxCount) {
            cnt = this.maxCount;
        }
        var ac = GameHelper_1.gameHpr.getCountByCType(this.type);
        if (ac < cnt) {
            cnt = ac;
        }
        if (cnt !== count) {
            event.string = '' + cnt;
        }
    };
    // path://root/ok_be
    DonateAncientSUpPnlCtrl.prototype.onClickOk = function (event, data) {
        var count = Number(this.inputEb_.string.trim());
        if (!count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RES_COUNT);
        }
        else if (count > this.maxCount) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIETN_CONTRIBUTE_RES_LIMIT);
        }
        else if (GameHelper_1.gameHpr.getCountByCType(this.type) < count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        this.cb && this.cb(this.type, count);
        this.hide();
    };
    // path://root/sell/max_be
    DonateAncientSUpPnlCtrl.prototype.onClickMax = function (event, data) {
        this.inputEb_.string = Math.min(this.maxCount, GameHelper_1.gameHpr.getCountByCType(this.type)) + '';
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 打开关闭box弹出框
    DonateAncientSUpPnlCtrl.prototype.changeBoxList = function (node, val) {
        var list = node.Child('items_nbe');
        node.Child('box_mask_be').active = val;
        list.active = val;
        // 刷新列表选中的颜色
        if (val) {
            var type_1 = this.type + '';
            list.children.forEach(function (m) { return m.Child('select').active = m.name === type_1; });
        }
    };
    // 选择一个
    DonateAncientSUpPnlCtrl.prototype.selectBoxItem = function (node, type) {
        this.type = type;
        node.Child('val', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
    };
    DonateAncientSUpPnlCtrl = __decorate([
        ccclass
    ], DonateAncientSUpPnlCtrl);
    return DonateAncientSUpPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DonateAncientSUpPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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