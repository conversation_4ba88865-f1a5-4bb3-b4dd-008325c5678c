
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildAncientPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '844a1cPMIRLDaARRgvbFUu7', 'BuildAncientPnlCtrl');
// app/script/view/build/BuildAncientPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BuildAncientPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildAncientPnlCtrl, _super);
    function BuildAncientPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.data = null;
        _this.ancientInfo = null;
        _this.currTab = 0;
        return _this;
    }
    BuildAncientPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _a.enter = true, _a),
        ];
    };
    BuildAncientPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var info;
            return __generator(this, function (_a) {
                info = this.pagesNode_.FindChild('0/bottom/state');
                info.FindChild('need/1/add_cost_be/val').setLocaleKey('ui.button_donate_cost', Constant_1.CTYPE_NAME[Enums_1.CType.CEREAL]);
                info.FindChild('need/2/add_cost_be/val').setLocaleKey('ui.button_donate_cost', Constant_1.CTYPE_NAME[Enums_1.CType.TIMBER]);
                info.FindChild('need/3/add_cost_be/val').setLocaleKey('ui.button_donate_cost', Constant_1.CTYPE_NAME[Enums_1.CType.STONE]);
                info.FindChild('up/18/add_ups_cost_be/val').setLocaleKey('ui.button_donate_ups_cost', GameHelper_1.gameHpr.millisecondToStringForDay(Constant_1.ANCIENT_SUP_TIME));
                return [2 /*return*/];
            });
        });
    };
    BuildAncientPnlCtrl.prototype.onEnter = function (data) {
        this.ancientInfo = GameHelper_1.gameHpr.world.getAncientInfo(data.aIndex);
        if (!this.ancientInfo) {
            this.hide();
            return ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', data);
        }
        this.data = data;
        this.tabsTc_.Tabs(this.currTab);
    };
    BuildAncientPnlCtrl.prototype.onRemove = function () {
    };
    BuildAncientPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildAncientPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.currTab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            // this.showBaseInfo(node)
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            this.updateBuildAttrInfo();
        }
        else if (type === 1) {
            this.showDonateStatistics(node);
        }
        else if (type === 2) {
            this.showRecord(node);
        }
    };
    // path://root/pages_n/0/bottom/state/need/1/add_cost_be
    BuildAncientPnlCtrl.prototype.onClickAddCost = function (event, data) {
        var _this = this;
        var _a, _b;
        if (this.ancientInfo.pauseState) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIETN_PAUSE);
        }
        else if (!this.ancientInfo.ctbSurplusCount) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIENT_CONTRIBUTE_COUNT_LIMIT);
        }
        var type = Number(event.target.parent.name);
        var upCount = ((_a = this.data.upCost.find(function (m) { return m.type === type; })) === null || _a === void 0 ? void 0 : _a.count) || 0;
        var curCount = ((_b = this.ancientInfo.lvUpRes[type]) === null || _b === void 0 ? void 0 : _b.count) || 0;
        var maxCount = Math.max(upCount - curCount);
        ViewHelper_1.viewHelper.showPnl('build/DonateAncientLv', type, maxCount, function (count) {
            if (_this.isValid && !!count) {
                _this.donateCost(0, type, count);
            }
        });
    };
    // path://root/pages_n/0/bottom/state/up/18/add_ups_cost_be
    BuildAncientPnlCtrl.prototype.onClickAddUpsCost = function (event, data) {
        var _this = this;
        if (this.ancientInfo.pauseState) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIETN_PAUSE);
        }
        else if (!this.ancientInfo.ctbSurplusCount) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIENT_CONTRIBUTE_COUNT_LIMIT);
        }
        var upCount = this.getSUpMaxCost();
        var curCount = this.ancientInfo.speedUpRes;
        var maxCount = Math.max(upCount - curCount);
        ViewHelper_1.viewHelper.showPnl('build/DonateAncientSUp', maxCount, function (type, count) {
            if (_this.isValid && !!type && !!count) {
                _this.donateCost(1, type, count);
            }
        });
    };
    // path://root/pages_n/0/bottom/title/tip_be
    BuildAncientPnlCtrl.prototype.onClickTip = function (event, data) {
        ViewHelper_1.viewHelper.showDesc('ui.ancient_donate_tip_' + this.ancientInfo.state);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildAncientPnlCtrl.prototype.onUpdateAncientInfo = function (data) {
        if (this.ancientInfo.index === data.index) {
            this.updateBuildAttrInfo();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildAncientPnlCtrl.prototype.updateBuildAttrInfo = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(0);
        var data = this.data, isMaxLv = data.isMaxLv();
        var attr = node.Child('info/attrs');
        var top = attr.Child('top');
        // 显示下级信息和升级费用
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        if (data.nextLvInfo) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', data.nextLvInfo.lv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex);
        var _a = cell.getPawnInfo() || { id: 8001, lv: 1 }, id = _a.id, lv = _a.lv;
        var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv), nextJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + (data.lv + 1)), json = cell === null || cell === void 0 ? void 0 : cell.getPawnAttrJson();
        var items = attr.Child('items');
        items.children[0].active = false;
        for (var i = items.childrenCount - 1; i > 0; i--) {
            items.removeChild(items.children[i]);
        }
        var effect = data.effect;
        //效果
        var firstSurplusTime = this.ancientInfo.getFirstSurplusTime();
        var buffNode = node.Child('info/top/buff');
        if (buffNode.active = !!firstSurplusTime) {
            ResHelper_1.resHelper.loadAncientBuffIcon(effect.type, buffNode.Child('icon'), this.key);
            buffNode.Child('time', cc.LabelTimer).run(firstSurplusTime * 0.001);
        }
        var nextInfo = isMaxLv ? null : this.getNextAttrJson();
        items.AddItem(function (it, i) {
            var _a, _b;
            it.Child('curr/icon').active = false;
            it.Child('curr/add').active = false;
            var text = !!firstSurplusTime ? ("<color=#4AB32E>" + (((_a = data.getMaxEffect()) === null || _a === void 0 ? void 0 : _a.getValueText()) || '') + "</c>") : ((effect === null || effect === void 0 ? void 0 : effect.getValueText()) || '');
            it.Child('curr/val').setLocaleKey('ui.ancient_curr_eff_desc_' + effect.type, text);
            if (it.Child('next').active = !isMaxLv) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = ((_b = nextInfo.effect) === null || _b === void 0 ? void 0 : _b.getValueText()) || '';
            }
            it.Child('line').active = true;
        });
        // 耐久
        var hp = (cell === null || cell === void 0 ? void 0 : cell.getHpInfo()) || [0, 0], jsonHp = (attrJson === null || attrJson === void 0 ? void 0 : attrJson.hp) || 0, maxHp = hp[1], hpAdd = Math.max(0, maxHp - jsonHp);
        items.AddItem(function (it, i) {
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey('ui.build_eff_desc_35_1', ['ui.map_icon_name_ancient', hp.join('/')]);
            it.Child('curr/add').setLocaleKey(hpAdd > 0 ? assetsMgr.lang('ui.bracket', jsonHp + "+<color=#4AB32E>" + hpAdd + "</c>") : '');
            if (it.Child('next').active = !isMaxLv) {
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextJson.hp + hpAdd;
            }
            it.Child('line').active = false;
        });
        // 显示提示
        var tipBtn = node.Child('bottom/title/tip_be', cc.Button);
        tipBtn.interactable = tipBtn.Child('tip').active = !isMaxLv;
        // 按钮状态
        var buttonColor = this.ancientInfo.pauseState ? '#B6A591' : '#3F332F';
        // 捐献冷却剩余时间
        var ctbCdSurplusTime = this.ancientInfo.getCtbCdSurplusTime();
        // 显示升级费用
        var need = node.Child('bottom/state/need');
        if (need.active = !isMaxLv && this.ancientInfo.state === 0) {
            node.Child('bottom/title/tip_be/val').setLocaleKey('ui.up_cost');
            var type_1 = this.ancientInfo.getContributeLUpType(), ctbSurplusCount = this.ancientInfo.ctbSurplusCount;
            data.upCost.forEach(function (m) {
                var _a;
                var count = ((_a = _this.ancientInfo.lvUpRes[m.type]) === null || _a === void 0 ? void 0 : _a.count) || 0;
                var button = need.Child(m.type + '/add_cost_be'), isSatisfied = count >= m.count, isCanContribute = type_1 === -1 && !isSatisfied;
                var cd = need.Child(m.type + '/cd', cc.LabelTimer);
                need.Child(m.type + '/count/val', cc.Label).Color(isSatisfied ? '#756963' : '#D7634D').string = '' + count;
                need.Child(m.type + '/count/max', cc.Label).string = '/' + m.count;
                need.Child(m.type + '/count/satisfied').active = isSatisfied;
                need.Child(m.type + '/yet').active = type_1 === m.type;
                if (cd.setActive(ctbCdSurplusTime > 0 && isCanContribute)) {
                    cd.run(ctbCdSurplusTime * 0.001, function () { return _this.updateBuildAttrInfo(node); });
                }
                if (button.active = !ctbCdSurplusTime && isCanContribute) {
                    button.children.forEach(function (x) { return x.Color(buttonColor); });
                }
            });
            need.Child('tip').setLocaleKey('ui.ancient_donate_tip_2', " <color=" + (ctbSurplusCount ? '#756963' : '#D7634D') + ">" + ctbSurplusCount + "</c>");
        }
        // 显示加速费用
        var sup = node.Child('bottom/state/up');
        if (sup.active = !isMaxLv && this.ancientInfo.state === 1) {
            node.Child('bottom/title/tip_be/val').setLocaleKey('ui.up_speed_cost');
            var count = this.ancientInfo.speedUpRes, ctbSurplusCount = this.ancientInfo.ctbSurplusCount;
            var max = this.getSUpMaxCost();
            var isContributeSUp = this.ancientInfo.isContributeSUp();
            var button = sup.Child('18/add_ups_cost_be'), isSatisfied = count >= max;
            var cd = sup.Child('18/cd', cc.LabelTimer);
            sup.Child('18/count/val', cc.Label).Color(isSatisfied ? '#756963' : '#D7634D').string = '' + count;
            sup.Child('18/count/max', cc.Label).string = '/' + max;
            sup.Child('18/yet').active = isContributeSUp;
            if (cd.setActive(ctbCdSurplusTime > 0 && !isContributeSUp)) {
                cd.run(ctbCdSurplusTime * 0.001, function () { return _this.updateBuildAttrInfo(node); });
            }
            if (button.active = !ctbCdSurplusTime && !isContributeSUp) {
                button.children.forEach(function (x) { return x.Color(buttonColor); });
            }
            sup.Child('tip').setLocaleKey('ui.ancient_donate_tip_2', " <color=" + (ctbSurplusCount ? '#756963' : '#D7634D') + ">" + ctbSurplusCount + "</c>");
        }
        // 刷新按钮
        var buttons = node.Child('bottom/buttons');
        if (buttons.active = !isMaxLv) {
            if (this.ancientInfo.pauseState !== 0) {
                var btn = buttons.Swih('pause')[0];
                btn.Child('lay/time', cc.LabelTimer).Color('#D7634D').string = ut.secondFormat(this.ancientInfo.surplusTime * 0.001, 'h:mm:ss');
            }
            else {
                var btn = buttons.Swih(this.ancientInfo.state ? 'uping' : 'none')[0];
                if (this.ancientInfo.state === 0) {
                    btn.Child('lay/time', cc.Label).string = ut.secondFormat(data.attrJson.bt_time, 'h:mm:ss');
                }
                else if (this.ancientInfo.state === 1) {
                    var time = this.ancientInfo.getSurplusTime() * 0.001;
                    btn.Child('lay/time', cc.LabelTimer).Color('#4AB32E').run(time);
                }
            }
        }
    };
    BuildAncientPnlCtrl.prototype.getNextAttrJson = function () {
        var _a;
        var id = this.data.id, lv = this.data.lv, val = ((_a = this.data.effect) === null || _a === void 0 ? void 0 : _a.value) || 0;
        for (var i = lv; i <= 20; i++) {
            var json = assetsMgr.getJsonData('buildAttr', id * 1000 + i);
            var effect = GameHelper_1.gameHpr.stringToCEffects(json.effects)[0];
            if ((effect === null || effect === void 0 ? void 0 : effect.value) > val) {
                return { effect: effect, lv: i };
            }
        }
        return null;
    };
    BuildAncientPnlCtrl.prototype.getSUpMaxCost = function () {
        return Math.floor(this.data.upCost.reduce(function (val, cur) { return val + cur.count; }, 0) / Constant_1.ANCIENT_SUP_COST_MUL);
    };
    // 捐献资源
    BuildAncientPnlCtrl.prototype.donateCost = function (type, resType, count) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_AncientContribute', { index: this.data.aIndex, type: type, resType: resType, count: count }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            // if (err === ecode.ANCIENT_CONTRIBUTE_CD) {
                            //     this.ancientInfo.ctbCdSurplusTime = data.ctbCdSurplusTime
                            //     this.ancientInfo.getCtbCdTime = Date.now()
                            // }
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.ancientInfo.curContribute = data.curContribute;
                        this.ancientInfo.ctbSurplusCount = data.ctbSurplusCount;
                        GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                        this.updateBuildAttrInfo();
                        return [2 /*return*/];
                }
            });
        });
    };
    // ---------------------------------------------------捐献统计------------------------------------------------------------
    BuildAncientPnlCtrl.prototype.showDonateStatistics = function (node) {
        var _this = this;
        this.loadingNode_.active = !this.ancientInfo.tempDonateStatistics.length;
        this.ancientInfo.getDonateStatistics().then(function (list) {
            if (!_this.isValid) {
                return;
            }
            _this.loadingNode_.active = false;
            var sv = node.Component(cc.ScrollView), len = list.length;
            sv.Child('empty').active = !len;
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(len, function (it, i) {
                var data = list[i];
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head'), data.headIcon, _this.key);
                it.Child('name', cc.Label).string = ut.nameFormator(data.nickname, 7);
                it.Child('sum').setLocaleKey('ui.ancient_donate_sum', data.sum);
                it.Child('list').Items(data.items, function (it, res) { return ViewHelper_1.viewHelper.updateCostViewOne(it, res); });
            });
        });
    };
    // ---------------------------------------------------捐献记录------------------------------------------------------------
    BuildAncientPnlCtrl.prototype.showRecord = function (node) {
        var _this = this;
        this.loadingNode_.active = !this.ancientInfo.tempLogsList.length;
        this.ancientInfo.getLogs().then(function (list) {
            if (!_this.isValid) {
                return;
            }
            _this.loadingNode_.active = false;
            var sv = node.Component(cc.ScrollView), len = list.length;
            sv.Child('empty').active = !len;
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(len, function (it, i) {
                var data = list[i];
                it.Child('name', cc.Label).string = ut.nameFormator(data.nickname, 7);
                it.Child('time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                ViewHelper_1.viewHelper.updateCostViewOne(it.Child('res'), data.res);
            });
        });
    };
    BuildAncientPnlCtrl = __decorate([
        ccclass
    ], BuildAncientPnlCtrl);
    return BuildAncientPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildAncientPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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