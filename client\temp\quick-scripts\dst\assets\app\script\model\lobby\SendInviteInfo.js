
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/lobby/SendInviteInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1c626E0xZlGD66of2Ccuzdb', 'SendInviteInfo');
// app/script/model/lobby/SendInviteInfo.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseUserInfo_1 = require("../common/BaseUserInfo");
// 邀请信息
var SendInviteInfo = /** @class */ (function (_super) {
    __extends(SendInviteInfo, _super);
    function SendInviteInfo() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return SendInviteInfo;
}(BaseUserInfo_1.default));
exports.default = SendInviteInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxsb2JieVxcU2VuZEludml0ZUluZm8udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsdURBQWlEO0FBRWpELE9BQU87QUFDUDtJQUE0QyxrQ0FBWTtJQUF4RDs7SUFFQSxDQUFDO0lBQUQscUJBQUM7QUFBRCxDQUZBLEFBRUMsQ0FGMkMsc0JBQVksR0FFdkQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZVVzZXJJbmZvIGZyb20gXCIuLi9jb21tb24vQmFzZVVzZXJJbmZvXCJcclxuXHJcbi8vIOmCgOivt+S/oeaBr1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTZW5kSW52aXRlSW5mbyBleHRlbmRzIEJhc2VVc2VySW5mbyB7XHJcblxyXG59Il19