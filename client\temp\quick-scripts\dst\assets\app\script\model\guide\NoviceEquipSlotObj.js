
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceEquipSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '497b8YDsipGRZkL/QZXUD/I', 'NoviceEquipSlotObj');
// app/script/model/guide/NoviceEquipSlotObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var EquipSlotObj_1 = require("../main/EquipSlotObj");
// 装备槽位
var NoviceEquipSlotObj = /** @class */ (function (_super) {
    __extends(NoviceEquipSlotObj, _super);
    function NoviceEquipSlotObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.allSelectIds = [];
        return _this;
    }
    NoviceEquipSlotObj.prototype.strip = function () {
        return {
            lv: this.lv,
            id: this.id,
            selectIds: this.selectIds,
            resetCount: this.resetCount,
        };
    };
    NoviceEquipSlotObj.prototype.fromDB = function (data) {
        this.lv = data.lv;
        this.id = data.id;
        this.selectIds = data.selectIds;
        this.resetCount = data.resetCount;
        return this;
    };
    NoviceEquipSlotObj.prototype.toDB = function () {
        return {
            lv: this.lv,
            id: this.id,
            selectIds: this.selectIds,
            resetCount: this.resetCount,
        };
    };
    return NoviceEquipSlotObj;
}(EquipSlotObj_1.default));
exports.default = NoviceEquipSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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