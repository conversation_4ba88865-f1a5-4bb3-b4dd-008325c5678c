
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceBTCityObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '816f7lSr2VCz7cp63RKSBRF', 'NoviceBTCityObj');
// app/script/model/guide/NoviceBTCityObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个建筑队列
var NoviceBTCityObj = /** @class */ (function () {
    function NoviceBTCityObj() {
        this.aIndex = 0; //区域位置
        this.cityId = 0; //建筑id
        this.startTime = 0; //开始时间
        this.needTime = 0; //需要时间
    }
    NoviceBTCityObj.prototype.init = function (index, id, time) {
        this.aIndex = index;
        this.cityId = id;
        this.startTime = Date.now();
        this.needTime = time;
        return this;
    };
    NoviceBTCityObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            id: this.cityId,
            needTime: this.needTime,
            surplusTime: this.getSurplusTime(),
        };
    };
    NoviceBTCityObj.prototype.fromDB = function (data) {
        this.aIndex = data.aIndex;
        this.cityId = data.cityId;
        this.startTime = data.startTime;
        this.needTime = data.needTime;
        return this;
    };
    NoviceBTCityObj.prototype.getSurplusTime = function () {
        return Math.max(this.needTime - (Date.now() - this.startTime), 0);
    };
    return NoviceBTCityObj;
}());
exports.default = NoviceBTCityObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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