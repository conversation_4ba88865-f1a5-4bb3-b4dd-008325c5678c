
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/GuideModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c21173ybZ1G0a0AAzf9L6AG', 'GuideModel');
// app/script/model/guide/GuideModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LocalConfig_1 = require("../../common/LocalConfig");
var GuideConfig_1 = require("./GuideConfig");
var GuideObj_1 = require("./GuideObj");
var NoviceConfig_1 = require("./NoviceConfig");
/**
 * 新手引导模块
 */
var GuideModel = /** @class */ (function (_super) {
    __extends(GuideModel, _super);
    function GuideModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.user = null;
        _this.player = null;
        _this.noviceServer = null;
        _this.guides = []; //还没完成的引导
        _this.currGuide = null; //当前执行的新手引导
        _this.listenEvent = null; //当前监听的事件
        _this.currWaitCount = 0;
        _this.reqAllArmyState = 0;
        _this.LOCAL_STORAGE_IDS = [1, 2, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 5, 10010, 10011]; //本地存储的引导id
        _this.parentGuide = null; //父引导
        _this.drillAcc = false; //招募加速
        _this.forgeAcc = false; //打造加速
        _this.buildAcc = false; //建造加速
        _this.lastObj = null;
        _this.isCheckBattleResult = false;
        return _this;
        /* 和敌人对战 */
        //----------------------------------------------------other------------------------------------------------------------
        // private isResChecking = false
        // private tipCalaimTask = false
        // private async checkResFill() {//新手村取消资源赠送
        //     if (this.isResChecking) return
        //     if (this.parentGuide || !gameHpr.isNoviceMode) return
        //     if (mc.currWindName === 'area') {
        //         if (gameHpr.world.getLookCell().index !== this.player.getMainCityIndex()) {
        //             return
        //         }
        //         let isDrilling = this.noviceServer.isUserPawnDrilling()
        //         if (isDrilling) return
        //         this.isResChecking = true
        //         // 在主城 且 没有军队了
        //         const allArmys = await this.player.getAllArmys(5)
        //         this.isResChecking = false
        //         isDrilling = this.noviceServer.isUserPawnDrilling()
        //         if (isDrilling) return
        //         if (!isDrilling && !allArmys.length && !this.noviceServer.canDrillPawn('3101')) {
        //             // 检查任务奖励
        //             const player = this.player
        //             const tasks = player.getGuideTasks()
        //             const resTmp = [player.getCereal(), player.getTimber(), player.getStone()]
        //             let someCanClaim = false
        //             for (let task of tasks) {
        //                 if (task.isCanClaim()) {
        //                     someCanClaim = true
        //                     for (let reward of task.rewards) {
        //                         resTmp[reward.type - 1] += reward.count
        //                     }
        //                 }
        //             }
        //             if (!someCanClaim) {
        //                 // 确实没资源了 发放物资
        //                 this.beginSub(10010)
        //             } else if (!this.tipCalaimTask && this.noviceServer.canDrillPawn('3101', resTmp)) {
        //                 this.tipCalaimTask = true
        //                 // 领完任务奖励可以招募士兵
        //                 this.beginSub(10011)
        //             }
        //         }
        //     }
        // }
    }
    GuideModel.prototype.onCreate = function () {
        this.user = this.getModel('user');
        this.player = this.getModel('player');
        this.noviceServer = this.getModel('novice_server');
    };
    GuideModel.prototype.init = function (guides) {
        var e_1, _a;
        var _this = this;
        var _b;
        if (!this.currGuide) {
            this.clean();
            var guideTagMap_1 = {};
            guides.forEach(function (m) { return guideTagMap_1[m.id] = m.tag; });
            try {
                for (var _c = __values(this.LOCAL_STORAGE_IDS), _d = _c.next(); !_d.done; _d = _c.next()) {
                    var id = _d.value;
                    guideTagMap_1[id] = storageMgr.loadString(this.getSaveKey(id));
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
                }
                finally { if (e_1) throw e_1.error; }
            }
            // 兼容
            var curVersion = 5;
            var version = (_b = storageMgr.loadNumber('guide_version')) !== null && _b !== void 0 ? _b : 0;
            if (version !== curVersion) {
                storageMgr.saveNumber('guide_version', curVersion);
            }
            while (version < curVersion) {
                if (version === 0) {
                }
                else if (version === 1) {
                    // 只要还没完成新手村 就全部清空
                    if (guideTagMap_1[5] !== GuideConfig_1.GuideTagType.BATTLE_GUIDE_END) {
                        this.LOCAL_STORAGE_IDS.forEach(function (id) { return guideTagMap_1[id] = ''; });
                        storageMgr.remove('novice_data_' + this.user.getUid());
                    }
                }
                else if (version === 2) { // 兼容回血教程
                    guideTagMap_1[2] = GuideConfig_1.GuideTagType.PAWN_BACK_MAIN_GUIDE_END;
                    this.saveGuideTag(2, GuideConfig_1.GuideTagType.PAWN_BACK_MAIN_GUIDE_END);
                }
                else if (version === 3) { // 新回血教程上线取消version===2的临时解决方案
                    guideTagMap_1[2] = '';
                    this.saveGuideTag(2, '');
                }
                else if (version === 4) { // 新的教程 全部清空
                    // 只要还没完成新手村 就全部清空
                    if (guideTagMap_1[5] !== GuideConfig_1.GuideTagType.BATTLE_GUIDE_END) {
                        this.LOCAL_STORAGE_IDS.forEach(function (id) { return guideTagMap_1[id] = ''; });
                        storageMgr.remove('novice_data_' + this.user.getUid());
                    }
                }
                version += 1;
            }
            // 创建新手引导
            GuideConfig_1.GUIDE_CONFIG.datas.forEach(function (m) { return _this.guides.push(new GuideObj_1.default().fromSvr(m.id, guideTagMap_1[m.id])); });
        }
    };
    GuideModel.prototype.clean = function () {
        this.currGuide = null;
        this.currWaitCount = 0;
        this.reqAllArmyState = 0;
        this.cleanListenEvent();
        this.guides = [];
        ViewHelper_1.viewHelper.hidePnl('common/Guide');
    };
    // 保存引导标记
    GuideModel.prototype.saveGuideTag = function (id, tag) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // 本地存储
                        if (this.LOCAL_STORAGE_IDS.includes(id)) {
                            this.noviceServer.forceSave();
                            storageMgr.saveString(this.getSaveKey(id), tag);
                            return [2 /*return*/, true];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_SyncGuideTag', { id: id, tag: tag }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err === ''];
                }
            });
        });
    };
    GuideModel.prototype.strip = function () {
        var e_2, _a;
        var data = {};
        try {
            for (var _b = __values(this.LOCAL_STORAGE_IDS), _c = _b.next(); !_c.done; _c = _b.next()) {
                var local = _c.value;
                var key = this.getSaveKey(local);
                data[key] = storageMgr.loadString(key);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return data;
    };
    GuideModel.prototype.getSaveKey = function (id) {
        return 'novice_guide_' + this.user.getUid() + '_' + id;
    };
    // 获取引导是否完成
    GuideModel.prototype.getIsFinishGuideID = function (id) {
        if (this.isGuideById(id))
            return false;
        var guide = this.guides.find(function (m) { return m.id === id; });
        if (guide) {
            return guide.isFinish();
        }
        return false;
    };
    GuideModel.prototype.update = function (dt) {
        this.check();
        // this.checkResFill()
    };
    GuideModel.prototype.check = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var i, l, m, func;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!LocalConfig_1.localConfig.openGuide || (!GameHelper_1.gameHpr.isEnterWorld && !GameHelper_1.gameHpr.isEnterNovice && !GameHelper_1.gameHpr.isEnterLobby) || this.isWorking() || PopupPnlHelper_1.popupPnlHelper.isWorking()) {
                            this.checkBuildAccFinish();
                            return [2 /*return*/];
                        }
                        i = 0, l = this.guides.length;
                        _b.label = 1;
                    case 1:
                        if (!(i < l)) return [3 /*break*/, 5];
                        m = this.guides[i];
                        if (m.isFinish()) {
                            return [3 /*break*/, 4];
                        }
                        func = this[(_a = m.checkFunc) === null || _a === void 0 ? void 0 : _a.name];
                        if (!!func) return [3 /*break*/, 2];
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, func.call(this, m, m.checkFunc.args)];
                    case 3:
                        if (_b.sent()) {
                            this.begin(m);
                            return [3 /*break*/, 5];
                        }
                        _b.label = 4;
                    case 4:
                        i++;
                        return [3 /*break*/, 1];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    // 是否正在强引导
    GuideModel.prototype.isForceWorking = function () {
        if (!this.currGuide) {
            return false;
        }
        var step = this.currGuide.getCurrStep();
        if (!step) {
            return false;
        }
        return step.type !== GuideConfig_1.GuideStepType.CHECK_WAIT && step.type !== GuideConfig_1.GuideStepType.ON_EVENT_WAIT;
    };
    // 是否引导中
    GuideModel.prototype.isWorking = function () {
        return !!this.currGuide;
    };
    // 当前是否在跑第一个新手引导
    GuideModel.prototype.isOneGuideWorking = function () {
        return this.isGuideById(1);
    };
    // 当前是否某个引导
    GuideModel.prototype.isGuideById = function (id) {
        var _a;
        return ((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.id) === id;
    };
    GuideModel.prototype.getGuideStepIndex = function (id) {
        var guide = this.guides.find(function (m) { return m.id === id; });
        return guide ? guide.index : -1;
    };
    // 与当前tag比较 0表示相等 -1表示小于 1表示大于
    GuideModel.prototype.equalGuideTag = function (id, tag) {
        var guide = this.guides.find(function (m) { return m.id === id; });
        var steps = guide === null || guide === void 0 ? void 0 : guide.steps;
        if (steps) {
            var idx = steps.findIndex(function (m) { return m.tag === tag; });
            if (idx < guide.index)
                return -1;
            else if (idx === guide.index)
                return 0;
            else
                return 1;
        }
    };
    GuideModel.prototype.setCurGuideTag = function (id, tag) {
        var guide = this.guides.find(function (m) { return m.id === id; });
        if (!guide) {
            return;
        }
        guide.currTag = tag;
        guide.progressTag = tag;
        var index = guide.steps.findIndex(function (m) { return m.tag === tag; });
        if (index !== -1) {
            guide.index = index;
        }
        this.currGuide = null;
        this.begin(guide);
    };
    // 是否在新手村 看第一个引导是否标记
    GuideModel.prototype.isFirstGuideFinish = function () {
        var _a;
        if (LocalConfig_1.localConfig.RELEASE && !GameHelper_1.gameHpr.isRelease) {
            return true; //审核的测试服 直接跳过新手村
        }
        else if (!LocalConfig_1.localConfig.openGuide || !this.user.isCarryNoviceData()) {
            return true;
        }
        return (_a = this.guides.find(function (m) { return m.id === 5; })) === null || _a === void 0 ? void 0 : _a.isFinish();
    };
    // 是否修复主城了
    GuideModel.prototype.isRestoreMainCity = function () {
        var guide = this.guides.find(function (m) { return m.id === 1; });
        return !!guide && guide.progressTag !== '0' && guide.progressTag !== GuideConfig_1.GuideTagType.FIRST_GUIDE_BEGIN;
    };
    GuideModel.prototype.isCurrTag = function (tag) {
        var _a;
        return ((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.currTag) === tag;
    };
    GuideModel.prototype.isCurrProgressTag = function (tag) {
        var _a;
        return ((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.progressTag) === tag;
    };
    GuideModel.prototype.isSpecialStepByIndex = function (index) {
        var _a;
        return ((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.index) === index;
    };
    // 开始引导
    GuideModel.prototype.begin = function (data) {
        var _this = this;
        var _a;
        if (((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.id) === data.id) {
            return;
        }
        this.currGuide = data;
        ViewHelper_1.viewHelper.showPnl('common/Guide').then(function () {
            _this.emit(EventType_1.default.GUIDE_CANCEL_FORCE, true, true);
            if (!data.checkFunc.keepPnl) {
                _this.emit(mc.Event.HIDE_ALL_PNL);
            }
            _this.emit(EventType_1.default.CLOSE_SELECT_CELL);
            _this.handle();
        });
    };
    GuideModel.prototype.beginSub = function (id) {
        var cur = this.currGuide;
        if (cur && cur.id === id)
            return;
        if (cur) {
            if (!this.parentGuide) {
                this.parentGuide = { id: cur.id, tag: cur.currTag };
            }
        }
        var guide = this.guides.find(function (m) { return m.id === id; });
        guide.index = 0;
        this.begin(guide);
    };
    // 结束
    GuideModel.prototype.end = function () {
        this.cleanListenEvent();
        if (this.guides.some(function (m) { return !m.isFinish(); })) {
            ViewHelper_1.viewHelper.hidePnl('common/Guide');
        }
        else {
            this.emit(mc.Event.CLOSE_PNL, 'common/Guide', true);
        }
        if (this.currGuide) {
            this.saveGuideTag(this.currGuide.id, this.currGuide.complete());
            this.currGuide = null;
        }
    };
    // 直接完成某个引导
    GuideModel.prototype.completeGuide = function (id) {
        var _a;
        if (((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.id) === id) {
            return this.end();
        }
        var guide = this.guides.find(function (m) { return m.id === id; });
        if (guide && !guide.isFinish()) {
            this.saveGuideTag(guide.id, guide.complete());
        }
    };
    // 强行结束新手村所有引导
    GuideModel.prototype.forceEndNovice = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isError;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.checkFuncSyncServerData()];
                    case 1:
                        isError = _a.sent();
                        if (!isError) {
                            this.LOCAL_STORAGE_IDS.forEach(function (id) {
                                var guide = _this.guides.find(function (m) { return m.id === id; });
                                if (guide) {
                                    storageMgr.saveString(_this.getSaveKey(guide.id), guide.complete());
                                }
                            });
                            this.leave('1-end-force');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    GuideModel.prototype.leave = function (eventId) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                ViewHelper_1.viewHelper.showPnl('novice/NoviceGameOver', function () {
                    this.emit(EventType_1.default.GUIDE_TAG_CLEAN_NOVICE_WIND);
                    ViewHelper_1.viewHelper.gotoWind('login').then(function () { return storageMgr.remove('novice_data_' + GameHelper_1.gameHpr.getUid()); });
                    //上报
                    TaHelper_1.taHelper.trackNovice('ta_tutorial_v2', { tutorial_step: eventId, uid: GameHelper_1.gameHpr.getUid() });
                    EventReportHelper_1.eventReportHelper.reportFacebookEventOne('fb_mobile_tutorial_completion', { fb_success: 1, fb_content_id: 'rookie' });
                    EventReportHelper_1.eventReportHelper.reportFirebaseEventOne('tutorial_complete');
                    EventReportHelper_1.eventReportHelper.reportAppflyerEventOne('af_tutorial_complete');
                }.bind(this));
                return [2 /*return*/];
            });
        });
    };
    // 下一个步骤
    GuideModel.prototype.nextStep = function () {
        this.cleanListenEvent();
        this.emit(EventType_1.default.GUIDE_RESET);
        this.currWaitCount = 0;
        if (this.currGuide) {
            var preStep = this.currGuide.getCurrStep(); //获取上一个步骤
            preStep.taEvent && TaHelper_1.taHelper.trackNovice('ta_tutorial_v2', { tutorial_step: preStep.taEvent, uid: GameHelper_1.gameHpr.getUid() });
            this.currGuide.index += 1;
            this.handle();
        }
    };
    // 跳转步骤
    GuideModel.prototype.gotoNextStep = function (tag, force) {
        if (this.currGuide) {
            var index = this.currGuide.steps.findIndex(function (m) { return m.tag === tag; });
            if (index >= 0) {
                this.currGuide.index = index - 1;
                if (force) {
                    this.nextStep();
                }
            }
        }
    };
    // 设置到指定步骤
    GuideModel.prototype.setStep = function (tag) {
        if (!this.currGuide) {
            return;
        }
        var guide = this.currGuide;
        var index = this.currGuide.steps.findIndex(function (m) { return m.tag === tag; });
        if (index >= 0) {
            guide.index = index;
            this.currGuide = null;
            this.begin(guide);
        }
    };
    GuideModel.prototype.handle = function () {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function () {
            var index, step, conf, func, args, data;
            var _this = this;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        index = this.currGuide.index;
                        if (index >= this.currGuide.steps.length || index < 0) {
                            return [2 /*return*/, this.end()];
                        }
                        step = this.currGuide.steps[index];
                        if (step.tag && this.currGuide.currTag !== step.tag) {
                            this.currGuide.currTag = step.tag;
                        }
                        if (!(step.restartPoint && step.restartPoint !== this.currGuide.progressTag)) return [3 /*break*/, 2];
                        this.currGuide.progressTag = step.restartPoint;
                        return [4 /*yield*/, this.saveGuideTag(this.currGuide.id, step.restartPoint)];
                    case 1:
                        _f.sent();
                        _f.label = 2;
                    case 2:
                        if (!step.nodeChoose) return [3 /*break*/, 7];
                        conf = step.nodeChoose;
                        func = this[conf.func];
                        args = conf.args;
                        if (!func) return [3 /*break*/, 6];
                        return [4 /*yield*/, func.call(this, args)];
                    case 3:
                        data = _f.sent();
                        if (!!data) return [3 /*break*/, 5];
                        return [4 /*yield*/, ut.wait(0.2)];
                    case 4:
                        _f.sent();
                        this.currWaitCount += 1;
                        if (this.currWaitCount >= 30) {
                            this.end();
                        }
                        else {
                            this.handle();
                        }
                        return [2 /*return*/];
                    case 5:
                        data.desc = conf.desc;
                        data.finger = conf.finger;
                        data.hide = conf.hide;
                        data.moveCamera = conf.moveCamera;
                        data.descOffset = conf.descOffset;
                        data.fullScreen = conf.fullScreen;
                        data.hideChoose = conf.hideChoose;
                        this.emit(EventType_1.default.GUIDE_SHOW_NODE_CHOOSE, data);
                        return [3 /*break*/, 7];
                    case 6:
                        if (conf.name) {
                            this.emit(EventType_1.default.GUIDE_SHOW_NODE_CHOOSE, {
                                path: conf.name, desc: conf.desc, finger: conf.finger, hide: conf.hide,
                                moveCamera: conf.moveCamera, fullScreen: conf.fullScreen, hideChoose: conf.hideChoose
                            });
                        }
                        _f.label = 7;
                    case 7:
                        if (!(step.type === GuideConfig_1.GuideStepType.DELAY)) return [3 /*break*/, 9];
                        return [4 /*yield*/, ut.wait((_a = step.time) !== null && _a !== void 0 ? _a : 0)];
                    case 8:
                        _f.sent();
                        this.nextStep();
                        return [3 /*break*/, 23];
                    case 9:
                        if (!(step.type === GuideConfig_1.GuideStepType.DIALOG)) return [3 /*break*/, 10];
                        // console.log(step.content, step.autoClose, step.mask)
                        this.emit(EventType_1.default.GUIDE_SHOW_DIALOG, step.content, step.autoClose, step.mask);
                        return [3 /*break*/, 23];
                    case 10:
                        if (!(step.type === GuideConfig_1.GuideStepType.ON_EVENT)) return [3 /*break*/, 11];
                        this.setListenEvent(step.event);
                        return [3 /*break*/, 23];
                    case 11:
                        if (!(step.type === GuideConfig_1.GuideStepType.ON_EVENT_WAIT)) return [3 /*break*/, 12];
                        this.setListenEvent(step.event);
                        // viewHelper.hidePnl('common/Guide')
                        this.emit(EventType_1.default.GUIDE_CANCEL_FORCE, !!step.force, false);
                        return [3 /*break*/, 23];
                    case 12:
                        if (!(step.type === GuideConfig_1.GuideStepType.CHECK)) return [3 /*break*/, 17];
                        return [4 /*yield*/, this.callFunc(step.func)];
                    case 13:
                        if (!_f.sent()) return [3 /*break*/, 15];
                        return [4 /*yield*/, ut.wait((_b = step.time) !== null && _b !== void 0 ? _b : 0.5)];
                    case 14:
                        _f.sent();
                        if (((_c = this.currGuide) === null || _c === void 0 ? void 0 : _c.index) === index) {
                            this.handle();
                        }
                        return [3 /*break*/, 16];
                    case 15:
                        if (this.currGuide) {
                            this.nextStep();
                        }
                        _f.label = 16;
                    case 16: return [3 /*break*/, 23];
                    case 17:
                        if (!(step.type === GuideConfig_1.GuideStepType.CHECK_WAIT)) return [3 /*break*/, 22];
                        return [4 /*yield*/, this.callFunc(step.func)];
                    case 18:
                        if (!_f.sent()) return [3 /*break*/, 20];
                        if (this.currWaitCount === 0) {
                            this.currWaitCount = 1;
                            ViewHelper_1.viewHelper.hidePnl('common/Guide');
                        }
                        return [4 /*yield*/, ut.wait((_d = step.time) !== null && _d !== void 0 ? _d : 0.5)];
                    case 19:
                        _f.sent();
                        if (((_e = this.currGuide) === null || _e === void 0 ? void 0 : _e.index) === index) {
                            this.handle();
                        }
                        return [3 /*break*/, 21];
                    case 20:
                        if (this.currGuide) {
                            ViewHelper_1.viewHelper.showPnl('common/Guide').then(function () {
                                _this.emit(EventType_1.default.GUIDE_CANCEL_FORCE, true, true);
                                _this.emit(mc.Event.HIDE_ALL_PNL);
                                _this.emit(EventType_1.default.CLOSE_SELECT_CELL);
                                _this.nextStep();
                            });
                        }
                        _f.label = 21;
                    case 21: return [3 /*break*/, 23];
                    case 22:
                        this.nextStep();
                        _f.label = 23;
                    case 23: return [2 /*return*/];
                }
            });
        });
    };
    GuideModel.prototype.setListenEvent = function (event) {
        this.cleanListenEvent();
        this.listenEvent = event;
        eventCenter.on(event.name, this.onListenEvent, this);
    };
    GuideModel.prototype.cleanListenEvent = function () {
        if (this.listenEvent) {
            eventCenter.off(this.listenEvent.name, this.onListenEvent, this);
            this.listenEvent = null;
        }
    };
    GuideModel.prototype.onListenEvent = function (data) {
        var _a;
        if (!this.listenEvent) {
            return;
        }
        var ok = false;
        if (this.listenEvent.func) {
            var func = this[this.listenEvent.func];
            ok = func === null || func === void 0 ? void 0 : func.call(this, data, this.listenEvent.args);
        }
        else {
            ok = this.checkArgObj(data, this.listenEvent.args);
        }
        if (ok) {
            this.cleanListenEvent();
            if (((_a = this.currGuide.getCurrStep()) === null || _a === void 0 ? void 0 : _a.type) === GuideConfig_1.GuideStepType.ON_EVENT_WAIT) {
                // viewHelper.showPnl('common/Guide').then(() => {
                //     this.emit(mc.Event.HIDE_ALL_PNL)
                //     this.emit(EventType.CLOSE_SELECT_CELL)
                //     this.nextStep()
                // })
                this.emit(EventType_1.default.GUIDE_CANCEL_FORCE, true, true);
                // this.emit(mc.Event.HIDE_ALL_PNL)
                // this.emit(EventType.CLOSE_SELECT_CELL)
            }
            this.nextStep();
        }
    };
    GuideModel.prototype.checkArgObj = function (data, args) {
        if (!args) {
            return true;
        }
        else if (!data) {
            return false;
        }
        for (var key in args) {
            if (data[key] !== args[key]) {
                return false;
            }
        }
        return true;
    };
    // 调用方法
    GuideModel.prototype.callFunc = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var func;
            return __generator(this, function (_a) {
                func = this[data.name];
                return [2 /*return*/, func === null || func === void 0 ? void 0 : func.call(this, data.args)];
            });
        });
    };
    // 切换场景 调用一次 主要是为了快 update 有延迟
    GuideModel.prototype.changeWind = function (wind) {
        return __awaiter(this, void 0, void 0, function () {
            var step;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!PopupPnlHelper_1.popupPnlHelper.isWorking()) return [3 /*break*/, 1];
                        return [2 /*return*/];
                    case 1:
                        if (!this.currGuide) return [3 /*break*/, 5];
                        step = this.currGuide.steps[this.currGuide.index];
                        if (!((step === null || step === void 0 ? void 0 : step.type) !== GuideConfig_1.GuideStepType.CHECK_WAIT)) return [3 /*break*/, 2];
                        return [2 /*return*/];
                    case 2: return [4 /*yield*/, this.callFunc(step.func)];
                    case 3:
                        if (_a.sent()) {
                            return [2 /*return*/];
                        }
                        else if (this.currGuide) {
                            ViewHelper_1.viewHelper.showPnl('common/Guide').then(function () {
                                _this.emit(EventType_1.default.GUIDE_CANCEL_FORCE, true, true);
                                _this.emit(mc.Event.HIDE_ALL_PNL);
                                _this.emit(EventType_1.default.CLOSE_SELECT_CELL);
                                _this.nextStep();
                            });
                        }
                        _a.label = 4;
                    case 4: return [3 /*break*/, 6];
                    case 5:
                        this.check();
                        _a.label = 6;
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    //---------------------------------------------------检测启动方法----------------------------------------------------
    // 第一个新手引导
    GuideModel.prototype.checkFuncOneGuide = function (guide) {
        var _a;
        if (mc.currWindName !== 'novice' || guide.isFinish()) {
            return false;
        }
        else if (guide.progressTag === GuideConfig_1.GuideTagType.FIRST_GUIDE_BEGIN || guide.progressTag === '0') {
            var point = this.player.getMainCityPoint();
            point.y -= 1;
            // 如果已经被占领了 直接完成这个引导
            if ((_a = GameHelper_1.gameHpr.world.getMapCellByPoint(point)) === null || _a === void 0 ? void 0 : _a.owner) {
                this.saveGuideTag(guide.id, guide.complete());
                return false;
            }
        }
        else if (guide.progressTag === GuideConfig_1.GuideTagType.FIRST_TRIGGER_BATTLE) { //等待出发第一个战斗
            var point = this.player.getMainCityPoint();
            point.y -= 1;
            var index = MapHelper_1.mapHelper.pointToIndex(point);
            var area = this.noviceServer.getArea(index);
            if (!area) {
                this.saveGuideTag(guide.id, guide.complete());
                return false;
            }
            else if (!this.noviceServer.getAreaAttacker(area) && !this.noviceServer.isCheckHasMarchByTarget(area.index)) { //如果没有战斗 也没有过去的行军
                var uid_1 = this.user.getUid();
                if (!this.noviceServer.getArea(this.player.getMainCityIndex()).armys.some(function (m) { return m.owner === uid_1; })) {
                    this.saveGuideTag(guide.id, guide.complete());
                    return false;
                }
                guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.RESTORE_MAIN_CITY; }); //返回到修复主城
            }
        }
        else if (guide.progressTag === GuideConfig_1.GuideTagType.CHECK_FIRST_BATTLE_END) { //等待战斗结束
            var point = this.player.getMainCityPoint();
            point.y -= 1;
            var index = MapHelper_1.mapHelper.pointToIndex(point);
            var area_1 = this.noviceServer.getArea(index);
            if (!area_1) {
                this.saveGuideTag(guide.id, guide.complete());
                return false;
            }
            var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
            var baseRes_1 = cell.getBaseRes(), treasures_1 = area_1.armys.reduce(function (arr, cur) { return cur.getAllPawnTreasures().concat(arr); }, []);
            GameHelper_1.gameHpr.world.setLookCell(cell);
            if (area_1.owner) { //已经占领了 只需要弹个窗
                ViewHelper_1.viewHelper.gotoWind('area').then(function () { return GameHelper_1.gameHpr.showBattleEndView(baseRes_1, treasures_1); });
                return true;
            }
            else if (!area_1.isBattleing()) { //没有战斗就开启战斗
                this.noviceServer.battleBegin(area_1, this.user.getUid());
            }
            ViewHelper_1.viewHelper.gotoWind('area').then(function () {
                if (area_1.owner && !mc.getOpenPnls().has('key', 'area/BattleEnd')) {
                    GameHelper_1.gameHpr.showBattleEndView(baseRes_1, treasures_1);
                }
            });
        }
        else if (guide.progressTag === GuideConfig_1.GuideTagType.CHECK_CAN_XL_PAWN) { //等待训练士兵
            var area = this.noviceServer.getArea(this.player.getMainCityIndex());
            if (!area) {
                this.saveGuideTag(guide.id, guide.complete());
                return false;
            }
            else if (!area.builds.some(function (m) { return m.id === Constant_1.BUILD_BARRACKS_NID; })) {
                var build = area.addBuild({ index: this.player.getMainCityIndex(), uid: ut.UID(), point: { x: 1, y: 1 }, id: Constant_1.BUILD_BARRACKS_NID, lv: 1 });
                this.player.updateMainBuildInfo(build.strip());
            }
        }
        return true;
    };
    // 士兵回城教程
    GuideModel.prototype.checkFuncPawnBackMainGuide = function (guide) {
        if (mc.currWindName !== 'novice' || guide.isFinish()) {
            return false;
        }
        else if (this.reqAllArmyState !== 0) {
            return false;
        }
        else if (GameHelper_1.gameHpr.player.getDistArmysByIndex(this.player.getMainCityIndex()).length >= 10) { // 主城有空位
            return false;
        }
        this.reqAllArmyState = 1;
        //拥有2块地后触发
        if (GameHelper_1.gameHpr.getPlayerOweCells(GameHelper_1.gameHpr.getUid(), true).size >= 4) {
            this.reqAllArmyState = 2;
            this.begin(guide);
        }
        else {
            this.reqAllArmyState = 0;
        }
        return false;
    };
    // 穿戴装备教程
    GuideModel.prototype.checkFuncWearEquipGuide = function (guide) {
        if (mc.currWindName !== 'area' || guide.isFinish()) {
            return false;
        }
        else if (this.player.getEquips().length === 0) {
            return false; //是否有装备
        }
        var area = GameHelper_1.gameHpr.areaCenter.getLookArea(), uid = this.user.getUid();
        if (!area || area.isBattleing()) {
            return false;
        }
        return area === null || area === void 0 ? void 0 : area.armys.some(function (army) { return army.pawns.some(function (m) { return !m.isMachine() && !m.equip.id && m.owner === uid; }); });
    };
    GuideModel.prototype.checkFuncGearforge = function (guide) {
        if (mc.currWindName !== 'area' || guide.isFinish()) {
            return false;
        }
        else if (this.player.getEquips().length === 0) {
            return false;
        }
        var area = GameHelper_1.gameHpr.areaCenter.getLookArea();
        if (!area || area.isBattleing() || area.index !== this.player.getMainCityIndex()) {
            return false;
        }
        var pg = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildSmithy'; });
        if (pg) {
            var page = pg.FindChild('root/pages_n/1');
            if (page && page.active)
                return true;
        }
        return false;
    };
    GuideModel.prototype.checkFuncPart4Guide = function (guide) {
        return false;
        return mc.currWindName === 'novice';
    };
    //对战结束
    GuideModel.prototype.checkFuncPartBattle = function (guide) {
        return __awaiter(this, void 0, void 0, function () {
            var cell;
            return __generator(this, function (_a) {
                if (mc.currWindName === 'novice' && !guide.isFinish() && this.getIsCloseAllUI()) {
                    cell = GameHelper_1.gameHpr.world.getMapCellByIndex(NoviceConfig_1.NOVICE_ENEMY_MAINCITY_INDEX);
                    if (cell && cell.owner == GameHelper_1.gameHpr.getUid()) {
                        return [2 /*return*/, true];
                    }
                }
                return [2 /*return*/, false];
            });
        });
    };
    GuideModel.prototype.checkFuncShowPnl = function (data) {
        ViewHelper_1.viewHelper.showPnl(data.key);
        return false;
    };
    /* 大厅抽卡 */
    GuideModel.prototype.checkFuncNewbieZone = function (guide) {
        if (mc.currWindName !== 'lobby' || guide.isFinish() || !this.user.isNewbie() || !this.getIsCloseAllUI()
            || (GameHelper_1.gameHpr.user.getPortrayals().length > 0 && GameHelper_1.gameHpr.user.getPortrayals()[0].isUnlock())
            || (GameHelper_1.gameHpr.user.getPortrayals().length === 0 && GameHelper_1.gameHpr.user.getWarToken() < 10)) {
            return false;
        }
        return true;
    };
    GuideModel.prototype.checkFuncNeedToComposite = function (data) {
        if (GameHelper_1.gameHpr.user.getPortrayals().length > 0) {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    //---------------------------------------------------检测方法----------------------------------------------------
    // 修复主城
    GuideModel.prototype.checkFuncRestoreMainCity = function () {
        this.noviceServer.restoreMainCity();
        this.emit(EventType_1.default.GUIDE_PLAY_RESTORE_MAINCITY);
        return false;
    };
    // 检测是否有战斗 true表示等待
    GuideModel.prototype.checkFuncTriggerFirstBattle = function () {
        var point = this.player.getMainCityPoint();
        point.y -= 1;
        return !GameHelper_1.gameHpr.isBattleingByIndex(MapHelper_1.mapHelper.pointToIndex(point));
    };
    GuideModel.prototype.checkFuncEnterBattleArea = function () {
        var _a;
        if (mc.currWindName !== 'area') {
            return true;
        }
        var index = this.noviceServer.defenseBattleIdx;
        if (((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) === index) {
            return false;
        }
        return true;
    };
    // 开启战斗 
    GuideModel.prototype.checkFuncBeginFirstBattle = function () {
        if (GameHelper_1.gameHpr.getPlayerOweCells(GameHelper_1.gameHpr.getUid(), true).size > 0) {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CHECK_FIRST_BATTLE_END; }) - 1; //这里减1是 下一步会加1
        }
        else {
            this.noviceServer.beginFirstBattle();
        }
        return false;
    };
    //开启手动战斗
    GuideModel.prototype.checkFuncManulBattleBegin = function () {
        if (mc.currWindName !== 'area') {
            return true;
        }
        return !this.noviceServer.resumeBattle();
    };
    // 检测第一个宝箱是否打开
    GuideModel.prototype.checkFuncTreasureIsOpen = function () {
        var _a, _b;
        var guide = this.currGuide;
        var treasure = (_b = (_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.armys[0]) === null || _b === void 0 ? void 0 : _b.getAllPawnTreasures()[0];
        if (!treasure) {
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CLAIM_TREASURE_COMPLETE; }) - 1; //这里减1是 下一步会加1
        }
        else if (treasure.rewards.length > 0) { //这里直接跳到领取宝箱
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CLAIM_TREASURE; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    // 关闭宝箱和军队界面
    GuideModel.prototype.checkFuncCloseTreasureAndArmyPnl = function () {
        ViewHelper_1.viewHelper.hidePnl('common/TreasureList');
        ViewHelper_1.viewHelper.hidePnl('area/AreaArmy');
        return false;
    };
    // 检测是否在战斗区域
    GuideModel.prototype.checkFuncInArea = function () {
        if (mc.currWindName === 'novice') {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CHECK_CAN_BT_BARRACKS; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    // 检测是否可以修建兵营 true表示等待
    GuideModel.prototype.checkFuncCanBTBarracks = function () {
        if (mc.currWindName !== 'novice' && mc.currWindName !== 'area') {
            return true;
        }
        var guide = this.currGuide;
        var build = this.player.getMainBuilds().find(function (m) { return m.id === Constant_1.BUILD_BARRACKS_NID; });
        if (!build) {
            var json = assetsMgr.getJsonData('buildAttr', Constant_1.BUILD_BARRACKS_NID * 1000);
            return !GameHelper_1.gameHpr.checkCTypes(GameHelper_1.gameHpr.stringToCTypes(json.up_cost));
        }
        else if (build.lv >= 1 || this.user.getGold() < Constant_1.IN_DONE_BT_GOLD) { //如果修建了 直接跳到训练士兵
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CHECK_CAN_XL_PAWN; }) - 1; //这里减1是 下一步会加1
        }
        else { //如果修建了 但是没有立即完成 就去立即完成
            guide.index = guide.steps.findIndex(function (m) { return m.tag === GuideConfig_1.GuideTagType.CHECK_CAN_INDONE; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    // 检测是否可以训练士兵 true表示等待
    GuideModel.prototype.checkFuncCanXlPawn = function () {
        if (mc.currWindName === 'area') {
            var build = this.player.getMainBuilds().find(function (m) { return m.id === Constant_1.BUILD_BARRACKS_NID; });
            if (!build || build.lv < 1) {
                return true;
            }
            var json = assetsMgr.getJsonData('pawnBase', 3101);
            if (GameHelper_1.gameHpr.checkCTypes(GameHelper_1.gameHpr.stringToCTypes(json.drill_cost))) {
                this.setStep('build_complete');
            }
            return true;
        }
        else {
            return false;
        }
    };
    // 检测是否可以选择士兵
    GuideModel.prototype.checkFuncChoosePawn = function () {
        var pg = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildBarracks'; });
        if (pg) {
            var node = pg.FindChild('root/pages_n/1/info/pawn/list/view/content/pawn_be/select');
            if (node && node.active) {
                this.currGuide.index++;
            }
        }
        return false;
    };
    //是否关闭所有UI界面
    GuideModel.prototype.getIsCloseAllUI = function () {
        if (mc.getLoadQueues().length > 0) {
            return false;
        }
        if (mc.currWindName === 'novice') {
            if (mc.getOpenPnls().length > 3) {
                return false;
            }
        }
        else {
            if (mc.getOpenPnls().length > 4) {
                return false;
            }
        }
        return true;
    };
    // 关闭所有UI
    GuideModel.prototype.checkFuncCloseAllUI = function () {
        this.emit(mc.Event.HIDE_ALL_PNL);
        this.emit(EventType_1.default.CLOSE_SELECT_CELL);
        return false;
    };
    // 关闭所有UI
    GuideModel.prototype.checkFuncCloseAllUITrue = function () {
        this.emit(mc.Event.HIDE_ALL_PNL);
        this.emit(EventType_1.default.CLOSE_SELECT_CELL);
        return true;
    };
    // 关闭兵营
    GuideModel.prototype.checkFuncCloseBarracks = function () {
        ViewHelper_1.viewHelper.hidePnl('build/BuildBarracks');
        return false;
    };
    // 是否有空闲的士兵
    GuideModel.prototype.checkFuncHasIdlePawn = function () {
        return !this.noviceServer.getMyPlayerArmys().some(function (m) { var _a; return m.state === Enums_1.ArmyState.NONE && !((_a = m.drillPawns) === null || _a === void 0 ? void 0 : _a.length); });
    };
    // 检测跳斩场景
    GuideModel.prototype.checkGotoSceneForNextStep = function (data) {
        if (mc.currWindName === (data === null || data === void 0 ? void 0 : data.scene)) {
            return false;
        }
        else if (data.tag) {
            this.gotoNextStep(data.tag); //跳转到下一步
        }
        return false;
    };
    //同步服务器数据
    GuideModel.prototype.checkFuncSyncServerData = function () {
        return __awaiter(this, void 0, void 0, function () {
            var taskList, i, err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        taskList = GameHelper_1.gameHpr.player.getGuideTasks();
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < taskList.length)) return [3 /*break*/, 4];
                        if (!taskList[i].isCanClaim()) return [3 /*break*/, 3];
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.claimTaskReward(taskList[i].id)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_CompleteNovice', { gold: this.user.getGold(), warToken: this.user.getWarToken() })];
                    case 5:
                        err = (_a.sent()).err;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                            return [2 /*return*/, true];
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    GuideModel.prototype.checkFuncTaskReward = function () {
        var isCanClaim = false;
        var taskList = GameHelper_1.gameHpr.player.getGuideTasks();
        for (var i = 0; i < taskList.length; i++) {
            if (taskList[i].isCanClaim()) {
                isCanClaim = true;
                break;
            }
        }
        if (!isCanClaim) {
            this.currGuide.index++; //跳过下一步
        }
        return false;
    };
    // 打开游戏目标界面 true表示等待
    GuideModel.prototype.checkFuncOpenEndPnl = function () {
        // viewHelper.showMessageBox('ui.guide_end_desc', {
        //     lockClose: true,
        //     title: 'ui.guide_end_title',
        //     okText: 'ui.button_goto',
        //     ok: () => this.leave('1-end'),
        // })
        this.leave('1-end');
        return false;
    };
    GuideModel.prototype.checkFuncEnterMainNeed = function () {
        var _a;
        if (mc.currWindName === 'novice') {
            return false;
        }
        else if (mc.currWindName === 'area') {
            if (((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.cityId) !== Constant_1.CITY_MAIN_NID) {
                return true;
            }
            else {
                // 在主城里 跳过回城引导
                this.setStep('intro_main_city');
            }
            return false;
        }
        return true;
    };
    // 生成任务
    GuideModel.prototype.checkFuncGenGuideTask = function (_a) {
        var id = _a.id;
        if (Array.isArray(id)) {
            var set_1 = new Set(id);
            this.noviceServer.appendGuideTask(function (task) { return set_1.has(task.id); });
        }
        else {
            this.noviceServer.appendGuideTask(function (task) { return task.id === id; });
        }
        return false;
    };
    GuideModel.prototype.checkFuncInNovice = function () {
        return mc.currWindName !== 'novice';
    };
    // 生成敌人
    GuideModel.prototype.checkFuncGenEnemy = function () {
        var e_3, _a, e_4, _b;
        if (mc.currWindName !== 'novice') {
            return true;
        }
        else if (!this.noviceServer.getMyPlayerArmys().some(function (m) { return m.state === Enums_1.ArmyState.NONE; })) {
            return true; //没有空闲的军队
        }
        var cells = [];
        var values = GameHelper_1.gameHpr.novice.getUserCells(this.user.getUid()).values();
        try {
            // 找到一个权重最小的地块 没有我方的军队 否则找到一个最少士兵的地块
            for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {
                var cell = values_1_1.value;
                // 城市和要塞不攻打
                if (cell.cityId === Constant_1.CITY_MAIN_NID || cell.cityId === -Constant_1.CITY_MAIN_NID || cell.cityId === Constant_1.CITY_FORT_NID) {
                    continue;
                }
                var area = this.noviceServer.getArea(cell.index);
                if (area) {
                    cells.push({ weight: area.armys.length, cell: cell });
                }
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (cells.length === 0) {
            return true;
        }
        cells.sort(function (a, b) { return a.weight - b.weight; });
        var neighbors = [cc.v2(0, 1), cc.v2(0, -1), cc.v2(1, 0), cc.v2(-1, 0)];
        var canGenerCells = [];
        for (var i = 0, l = cells.length; i < l; i++) {
            var _c = cells[i], weight = _c.weight, cell = _c.cell;
            try {
                // 找到接壤的野地
                for (var neighbors_1 = (e_4 = void 0, __values(neighbors)), neighbors_1_1 = neighbors_1.next(); !neighbors_1_1.done; neighbors_1_1 = neighbors_1.next()) {
                    var n = neighbors_1_1.value;
                    var neighborCell = GameHelper_1.gameHpr.world.getMapCellByPoint(n.add(cell.point));
                    if (!neighborCell || neighborCell.owner) {
                        continue;
                    }
                    var area = this.noviceServer.getArea(neighborCell.index);
                    if (!area || area.isBattleing()) {
                        continue;
                    }
                    var w = (10 - weight) * 10000 + GameHelper_1.gameHpr.getToMapCellDis(neighborCell.index, NoviceConfig_1.NOVICE_MAINCITY_INDEX);
                    canGenerCells.push({ weight: w, cell: cell, area: area });
                }
            }
            catch (e_4_1) { e_4 = { error: e_4_1 }; }
            finally {
                try {
                    if (neighbors_1_1 && !neighbors_1_1.done && (_b = neighbors_1.return)) _b.call(neighbors_1);
                }
                finally { if (e_4) throw e_4.error; }
            }
        }
        var data = canGenerCells.sort(function (a, b) { return b.weight - a.weight; })[0];
        if (data) {
            var area = data.area, cell = data.cell;
            // 生成敌方要塞
            // gameHpr.novice.initEnemy(area.index, this.)
            // 生成进攻军队
            var index = area.index;
            var armyId = ut.UID();
            area.addArmy({
                index: index,
                uid: armyId,
                name: assetsMgr.lang('ui.default_army_name') + 1,
                owner: area.owner,
                pawns: [
                    { index: index, uid: ut.UID(), id: 3403, lv: 1, point: cc.v2(7, 6) },
                ],
            });
            var army = area.getArmyByUid(armyId).strip();
            // 不自动开打
            this.noviceServer.addManulBattle(cell.index);
            // 对方派兵攻打我方
            GameHelper_1.gameHpr.novice.occupyCell([army], cell.index, 0, false, false);
            this.noviceServer.defenseBattleIdx = cell.index;
            // 引导下一步
            return false;
        }
        return true;
    };
    // 检测是否有军队在里面参战了
    GuideModel.prototype.checkFuncYetHasArmyHasInDefBattle = function () {
        var area = this.noviceServer.getArea(this.noviceServer.defenseBattleIdx);
        if (!area) {
            return false;
        }
        var uid = GameHelper_1.gameHpr.getUid();
        if (area.armys.some(function (m) { return m.owner === uid; })) {
            this.gotoNextStep('battle_support'); //直接跳转到 查看任务
        }
        return false;
    };
    GuideModel.prototype.checkFuncDefendSuccess = function () {
        var _this = this;
        var listener = function (index) {
            if (index !== _this.noviceServer.defenseBattleIdx)
                return;
            var area = _this.noviceServer.getArea(index);
            if (area.owner === _this.user.getUid()) {
                // 还有敌军援军正在赶来忽略
                if (_this.noviceServer.getMarchs().some(function (m) { return m.owner !== _this.user.getUid() && m.targetIndex === index; })) {
                    return;
                }
                eventCenter.off(EventType_1.default.AREA_BATTLE_END, listener, _this);
                eventCenter.emit('defense_success');
            }
        };
        eventCenter.on(EventType_1.default.AREA_BATTLE_END, listener, this);
        return false;
    };
    // 领取任务奖励
    GuideModel.prototype.checkFuncClaimTask = function (_a) {
        var id = _a.id;
        var tasks = this.player.getGuideTasks();
        var task = tasks.find(function (m) { return m.id === id; });
        if (!task)
            return true;
        if (task.isCanClaim()) {
            this.player.claimTaskReward(id);
            return false;
        }
        return true;
    };
    GuideModel.prototype.checkFuncBattleResult = function () {
        var _this = this;
        if (!this.isCheckBattleResult) {
            this.isCheckBattleResult = true;
            var area = this.noviceServer.getArea(this.noviceServer.castleBattleIdx);
            if ((area === null || area === void 0 ? void 0 : area.owner) === this.user.getUid()) {
                // 占领成功
                this.setStep('occupy_castle_success');
                return true;
            }
        }
        var listener = function (index) {
            if (index !== _this.noviceServer.castleBattleIdx) {
                return;
            }
            eventCenter.off(EventType_1.default.AREA_BATTLE_END, listener, _this);
            _this.isCheckBattleResult = false;
            var area = _this.noviceServer.getArea(index);
            if (area.owner === _this.user.getUid()) {
                // 占领成功
                _this.setStep('occupy_castle_success');
            }
            else {
                // 占领失败
                _this.setStep('occupy_castle_fail');
            }
        };
        eventCenter.on(EventType_1.default.AREA_BATTLE_END, listener, this);
        return true;
    };
    // 攻占要塞失败 检测是否可以发放资源
    GuideModel.prototype.checkFuncOccupyFortFailCanSendRes = function () {
        var resObj = this.noviceServer.lastOccupyPawnRes[this.noviceServer.castleBattleIdx];
        // 判断当前资源是否满了
        if (this.noviceServer.isFullRes() || ut.isEmptyObject(resObj)) {
            this.setStep(GuideConfig_1.GuideTagType.OCCUPY_CASTLE_CHECK);
            return true;
        }
        return false;
    };
    // 攻占要塞失败 发放物资 废弃
    GuideModel.prototype.checkFuncOccupyFortFailSendRes = function () {
        var str = this.noviceServer.getLastOccupyPawnResToStr(this.noviceServer.castleBattleIdx);
        this.noviceServer.sendResources(str);
        return false;
    };
    GuideModel.prototype.checkFuncGoStep = function (_a) {
        var tag = _a.tag;
        this.setStep(tag);
        return true;
    };
    // 发放物资 然后跳转监测 废弃
    GuideModel.prototype.checkFuncSendResources = function (_a) {
        var ctypeStr = _a.ctypeStr;
        this.noviceServer.sendResources(ctypeStr);
        return false;
    };
    // 下次招募加速
    GuideModel.prototype.checkFuncDrillAcc = function () {
        this.drillAcc = true;
        return false;
    };
    // 下次打造加速
    GuideModel.prototype.checkFuncForgeAcc = function () {
        this.forgeAcc = true;
        return false;
    };
    GuideModel.prototype.checkFuncReverse = function () {
        var parent = this.parentGuide;
        if (!parent) {
            return false;
        }
        this.parentGuide = null;
        this.setCurGuideTag(parent.id, parent.tag);
        return true;
    };
    GuideModel.prototype.checkFuncLoop = function () {
        return true;
    };
    // 隐藏行军线
    GuideModel.prototype.checkFuncHideMarchLine = function (_a) {
        var opacity = _a.opacity;
        GameHelper_1.gameHpr.novice.setAllMarchOpacity({}, opacity);
        return false;
    };
    // 暂停区域的战斗
    GuideModel.prototype.checkFuncPauseAreaBattle = function (_a) {
        var index = _a.index, frameCount = _a.frameCount;
        GameHelper_1.gameHpr.novice.setPauseBattleAreaInfo(index, frameCount);
        return false;
    };
    // 检测特定步骤，任意点击跳过当步骤
    GuideModel.prototype.checkFuncWeakStep = function () {
        var step = this.currGuide.getCurrStep();
        return !!(step === null || step === void 0 ? void 0 : step.weak);
    };
    //---------------------------------------------------事件检测方法----------------------------------------------------
    // 是否离自己一格距离的位置
    GuideModel.prototype.checkFirstArea = function () {
        var _a;
        var point = this.player.getMainCityPoint();
        point.y -= 1;
        return ((_a = GameHelper_1.gameHpr.world.getMapCellByPoint(point)) === null || _a === void 0 ? void 0 : _a.owner) === this.user.getUid();
    };
    //---------------------------------------------------框选节点方法----------------------------------------------------
    // 框选最近的地块
    GuideModel.prototype.nodeChooseMapCell = function () {
        var _a, _b;
        if (mc.currWindName !== 'novice' && mc.currWindName !== 'main')
            return null;
        var point = this.player.getMainCityPoint();
        point.y -= 1;
        // 如果已经被占领了 直接完成这个引导
        if (((_a = GameHelper_1.gameHpr.world.getMapCellByPoint(point)) === null || _a === void 0 ? void 0 : _a.owner) && ((_b = this.player.getDistArmysByIndex(this.player.getMainCityIndex())) === null || _b === void 0 ? void 0 : _b.length) > 0) {
            return null;
        }
        var pos = point.mulSelf(Constant_1.TILE_SIZE);
        return { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
    };
    // 框选主城
    GuideModel.prototype.nodeChooseMainCity = function () {
        if (mc.currWindName !== 'novice' && mc.currWindName !== 'main')
            return null;
        var pos = this.player.getMainCityPoint().mulSelf(Constant_1.TILE_SIZE);
        var size = Constant_1.TILE_SIZE * 2;
        return { rect: cc.rect(pos.x, pos.y, size, size) };
    };
    // 检查是否去招募士兵
    GuideModel.prototype.checkGotoPawnDrill = function () {
        var guide = this.currGuide;
        var indices = guide.steps.map(function (element, index) { return element.tag === GuideConfig_1.GuideTagType.FIRST_CREATE_ARMY ? index : -1; }).filter(function (index) { return index !== -1; });
        if (this.player.getPawnSlots()[1].id > 0) {
            guide.index = indices[0] - 1; //跳转第1个tag,减1，下一步会加1
        }
        return false;
    };
    //固定选中刀盾兵
    GuideModel.prototype.nodeChooseTargetPawn = function () {
        var canvasNode = cc.Canvas.instance.node;
        var contentNode = canvasNode.FindChild('View/StudySelectPnl/root/select/items_sv/view/content');
        for (var i = 0; i < contentNode.childrenCount; i++) {
            var node = contentNode.children[i];
            if (node.Data === 3201) {
                return node && { node: node };
            }
        }
        return contentNode && { node: contentNode };
    };
    // 框选宝箱文本
    GuideModel.prototype.nodeChooseTreasureText = function () {
        var _a;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'area/AreaArmy'; });
        if (!ui) {
            return null;
        }
        var node = (_a = ui.FindChild('root/list_sv/view/content')) === null || _a === void 0 ? void 0 : _a.children.find(function (m) { return m.Child('bottom/treasure_be').active; });
        return node && { node: node.Child('bottom/treasure_be') };
    };
    // 框选创建军队
    GuideModel.prototype.nodeChooseCreateArmy = function () {
        var _a;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildBarracks'; });
        if (!ui) {
            return null;
        }
        var node = (_a = ui.FindChild('root/pages_n/1/army/list/view/content')) === null || _a === void 0 ? void 0 : _a.children.find(function (m) { return m.Child('root/info/add').active; });
        return node && { node: node };
    };
    // 框选没有装备的士兵
    GuideModel.prototype.nodeChooseNoEquipPawn = function () {
        var area = GameHelper_1.gameHpr.areaCenter.getLookArea();
        if (!area || area.isBattleing()) {
            return null;
        }
        var pawn = null, uid = this.user.getUid();
        for (var i = 0, l = area.armys.length; i < l; i++) {
            pawn = area.armys[i].pawns.find(function (m) { var _a; return !m.isMachine() && !((_a = m.equip) === null || _a === void 0 ? void 0 : _a.id) && m.owner === uid; });
            if (pawn) {
                break;
            }
        }
        if (!pawn) {
            return null;
        }
        var pos = area.getBorderSize().addSelf(pawn.point).mulSelf(Constant_1.TILE_SIZE);
        return { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
    };
    // 框选你的士兵
    GuideModel.prototype.nodeChooseArmy = function (data) {
        var _this = this;
        var _a;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'main/SelectArmy'; });
        if (!ui) {
            return null;
        }
        var idx = (_a = data === null || data === void 0 ? void 0 : data.index) !== null && _a !== void 0 ? _a : 0, node = null;
        if (idx === -1) { //选择血量百分比最少的
            var minHp_1 = ut.MAX_VALUE;
            ui.FindChild('root/list_sv/view/content').children.forEach(function (m) {
                if (!m.Data || m.Data.state !== Enums_1.ArmyState.NONE) {
                    return;
                }
                m.Data.pawns.forEach(function (p) {
                    var percent = p.hp[0] / p.hp[1];
                    if (percent < minHp_1) {
                        minHp_1 = percent;
                        node = m;
                    }
                });
            });
        }
        else {
            node = ui.FindChild('root/list_sv/view/content').children[idx];
        }
        if (!node) {
            node = ui.FindChild('root/list_sv/view/content').children[0];
        }
        if (node) {
            var btnPos_1 = node.Child('pos_be', cc.Button);
            btnPos_1.interactable = false;
            node.once(cc.Node.EventType.TOUCH_END, function () {
                btnPos_1.interactable = true;
                _this.emit(EventType_1.default.GUIDE_CLICK_ARMY);
            }, this);
            return { node: node };
        }
        return null;
    };
    GuideModel.prototype.nodeChooseMapCellLv2 = function () {
        var _a, _b;
        var point = this.player.getMainCityPoint();
        point.x += 2;
        // 如果已经被占领了 直接完成这个引导
        if (((_a = GameHelper_1.gameHpr.world.getMapCellByPoint(point)) === null || _a === void 0 ? void 0 : _a.owner) && ((_b = this.player.getDistArmysByIndex(this.player.getMainCityIndex())) === null || _b === void 0 ? void 0 : _b.length) > 0) {
            this.saveGuideTag(this.currGuide.id, this.currGuide.complete());
            return null;
        }
        var pos = point.mulSelf(Constant_1.TILE_SIZE);
        return { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
    };
    // 找到被攻打的地块
    GuideModel.prototype.nodeChooseMapCellInBattle = function () {
        if (mc.currWindName !== 'novice')
            return null;
        var index = this.noviceServer.defenseBattleIdx;
        if (index === -1)
            return null;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
        var pos = cell.point.mul(Constant_1.TILE_SIZE);
        return { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
    };
    // 找到装备详情按钮
    GuideModel.prototype.nodeChooseEquipBaseInfoBtn = function () {
        var e_5, _a;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildSmithy'; });
        if (!ui) {
            return null;
        }
        var node = ui.FindChild('root/pages_n/1/equip');
        try {
            for (var _b = __values(node.children), _c = _b.next(); !_c.done; _c = _b.next()) {
                var child = _c.value;
                if (child.name === 'line' && child.children) {
                    var target = child.Child('equip_base_info_be_n');
                    if (target)
                        return { node: target };
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return null;
    };
    // 找到进入新手区对局按钮
    GuideModel.prototype.nodeChooseEnterNewbieZoneBtn = function () {
        if (mc.currWindName !== 'lobby')
            return null;
        var wind = mc.currWind;
        if (!wind) {
            return null;
        }
        var node = wind.FindChild('root_n/content/bottom_n/team/buttons_n');
        return node && { node: node.Child('enter_game_be') };
    };
    /*  铁匠铺 必须在新手村触发 */
    GuideModel.prototype.checkFuncSmithy = function (guide) {
        var _a;
        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) {
            return false;
        }
        var lv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
        var equipSlots = GameHelper_1.gameHpr.player.getEquipSlots();
        if (lv > 0 && ((_a = equipSlots[1]) === null || _a === void 0 ? void 0 : _a.id)) {
            return true;
        }
        return false;
    };
    // 检测铁匠铺装备资源是否足够
    GuideModel.prototype.checkSmithyResResetDialogText = function () {
        var steps = this.currGuide.steps;
        var player = GameHelper_1.gameHpr.player;
        if (player.getIron() < 2) {
            steps[steps.length - 2].content = [{ text: 'guideText.dialog_smithy_003' }];
        }
        else if (player.getStone() < 357 || player.getTimber() < 357) {
            steps[steps.length - 2].content = [{ text: 'guideText.dialog_smithy_004' }];
        }
        return false;
    };
    // 是否在铁匠铺界面
    GuideModel.prototype.checkIsOpenSmithy = function (data) {
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildSmithy'; });
        if (ui && ui.Child('root/pages_n/1').active) {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    //是否在主城外需要进入主城
    GuideModel.prototype.checkEnterMainCity = function (data) {
        if (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index === NoviceConfig_1.NOVICE_MAINCITY_INDEX) {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    // 下次建造加速
    GuideModel.prototype.checkFuncBuildAcc = function () {
        this.buildAcc = true;
        return false;
    };
    // 是否已经研究装备
    GuideModel.prototype.checkEquipStudy = function (data) {
        var equipSlots = GameHelper_1.gameHpr.player.getEquipSlots();
        if (equipSlots[data.slotId].id) {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    //是否已经打造装备
    GuideModel.prototype.checkEquipFroge = function () {
        var equips = GameHelper_1.gameHpr.player.getEquips();
        if (equips.length > 0) {
            ViewHelper_1.viewHelper.hidePnl('build/BuildSmithy');
            return false;
        }
        return true;
    };
    //检测空闲士兵
    GuideModel.prototype.checkFreeArmy = function (callback) {
        return __awaiter(this, void 0, void 0, function () {
            var armys, i, armyIndex, isContinue;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _a.sent();
                        for (i = 0; i < armys.length; i++) {
                            armyIndex = armys[i].index;
                            if (armys[i].state === Enums_1.ArmyState.NONE) {
                                isContinue = callback && callback(armyIndex, armys[i]);
                                if (isContinue)
                                    continue;
                                return [2 /*return*/, true];
                            }
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    //检测英雄可以化身的空闲士兵
    GuideModel.prototype.checkFreeArmyForHero = function (callback) {
        return __awaiter(this, void 0, void 0, function () {
            var armys, i, armyIndex, j, pawn, portrayal;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _a.sent();
                        for (i = 0; i < armys.length; i++) {
                            armyIndex = armys[i].index;
                            if (armys[i].state === Enums_1.ArmyState.NONE) {
                                for (j = 0; j < armys[i].pawns.length; j++) {
                                    pawn = armys[i].pawns[j];
                                    portrayal = GameHelper_1.gameHpr.user.getPortrayals()[0];
                                    if (portrayal && pawn.id === portrayal.json.avatar_pawn) {
                                        callback && callback(armyIndex, armys[i]);
                                        return [2 /*return*/, true];
                                    }
                                }
                            }
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    // 是否有空闲士兵触发穿戴装备教程
    GuideModel.prototype.checkFuncWearEquip = function (guide) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || GameHelper_1.gameHpr.player.getEquips().length === 0 || !this.getIsCloseAllUI()) {
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, this.checkFreeArmy()];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    //检查穿戴装备的场景，是否需要退出当前场景
    GuideModel.prototype.checkWearEquipScene = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var armys, i, guide;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _a.sent();
                        for (i = 0; i < armys.length; i++) {
                            if (armys[i].state === Enums_1.ArmyState.NONE && armys[i].index === GameHelper_1.gameHpr.world.getLookCell().index) {
                                guide = this.currGuide;
                                guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
                                break;
                            }
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        this.currGuide.index++;
                        _a.label = 3;
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    // 查找空闲士兵的地块
    GuideModel.prototype.findPawnInLand = function () {
        return __awaiter(this, void 0, void 0, function () {
            var retData;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        retData = null;
                        return [4 /*yield*/, this.checkFreeArmy(function (armyIndex) {
                                if (armyIndex === NoviceConfig_1.NOVICE_MAINCITY_INDEX) {
                                    retData = _this.nodeChooseMainCity();
                                }
                                else {
                                    var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(armyIndex);
                                    var pos = cell.point.mul(Constant_1.TILE_SIZE);
                                    retData = { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
                                }
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, retData];
                }
            });
        });
    };
    // 找到士兵
    GuideModel.prototype.findOnePawn = function () {
        var roleContent = mc.currWind.FindChild('root/role_n');
        for (var i = 0; i < roleContent.childrenCount; i++) {
            var child = roleContent.children[i];
            var list = child.name.split('_');
            if (list.length > 1) {
                var info = assetsMgr.getJsonData('pawnBase', list[1]);
                if (info) {
                    return { node: child.Child('body') };
                }
            }
        }
        return null;
    };
    /*  铁匠铺  */
    /* 英雄化身 */
    //供奉英雄
    GuideModel.prototype.checkFuncWorshipHero = function (guide) {
        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) {
            return false;
        }
        var lv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_HEROHALL_NID);
        var portrayals = GameHelper_1.gameHpr.user.getPortrayals();
        if (lv > 0 && portrayals.length > 0) {
            return true;
        }
        return false;
    };
    // 检查英雄是否可以化身
    GuideModel.prototype.checkHeroPawn = function () {
        return __awaiter(this, void 0, void 0, function () {
            var isFreePawn;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.checkFreeArmyForHero()];
                    case 1:
                        isFreePawn = _a.sent();
                        if (isFreePawn) {
                            this.currGuide.index++;
                        }
                        else {
                            this.checkFuncGenGuideTask({ id: 100999 });
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    // 英雄化身
    GuideModel.prototype.checkFuncPawnToHero = function (guide) {
        return __awaiter(this, void 0, void 0, function () {
            var lv;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) {
                            return [2 /*return*/, false];
                        }
                        lv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_HEROHALL_NID);
                        if (!(lv > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.checkFreeArmyForHero()];
                    case 1: return [2 /*return*/, _a.sent()];
                    case 2: return [2 /*return*/, false];
                }
            });
        });
    };
    //检查英雄化身场景
    GuideModel.prototype.checkHeroScene = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.checkFreeArmyForHero(function (armyIndex) {
                                var _a;
                                if (armyIndex === ((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index)) {
                                    var guide = _this.currGuide;
                                    guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
                                }
                            })];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        this.currGuide.index += 2;
                        _a.label = 3;
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    // 查找空闲士兵的地块
    GuideModel.prototype.findPawnInLandForHero = function () {
        return __awaiter(this, void 0, void 0, function () {
            var retData;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        retData = null;
                        return [4 /*yield*/, this.checkFreeArmyForHero(function (armyIndex) {
                                if (armyIndex === NoviceConfig_1.NOVICE_MAINCITY_INDEX) {
                                    retData = _this.nodeChooseMainCity();
                                }
                                else {
                                    var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(armyIndex);
                                    var pos = cell.point.mul(Constant_1.TILE_SIZE);
                                    retData = { rect: cc.rect(pos.x, pos.y, Constant_1.TILE_SIZE, Constant_1.TILE_SIZE) };
                                }
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, retData];
                }
            });
        });
    };
    // 找到士兵
    GuideModel.prototype.findOnePawnForHero = function () {
        var roleContent = mc.currWind.FindChild('root/role_n');
        for (var i = 0; i < roleContent.childrenCount; i++) {
            var child = roleContent.children[i];
            var list = child.name.split('_');
            if (list.length > 1) {
                if (Number(list[1]) === GameHelper_1.gameHpr.user.getPortrayals()[0].json.avatar_pawn) {
                    return { node: child.Child('body') };
                }
            }
        }
        return null;
    };
    /* 英雄化身 */
    /* 建筑加速 */
    GuideModel.prototype.checkFuncBuildAccTrigger = function (guide) {
        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || GameHelper_1.gameHpr.noviceServer.isBuildInDone
            || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) {
            return false;
        }
        var btQueues = GameHelper_1.gameHpr.player.getBtQueues();
        for (var i = 0; i < btQueues.length; i++) {
            var btInfo = btQueues[i];
            if (btInfo.id === Constant_1.BUILD_SMITHY_NID && btInfo.lv === 1 && GameHelper_1.gameHpr.noviceServer.getGold() >= Constant_1.IN_DONE_BT_GOLD) {
                return true;
            }
        }
        return false;
    };
    GuideModel.prototype.checkBuildAccFinish = function () {
        if (this.isWorking() && !this.currGuide.isFinish() && this.currGuide.id === 12) {
            var btQueues = GameHelper_1.gameHpr.player.getBtQueues();
            if (0 === btQueues.length) {
                this.completeGuide(this.currGuide.id);
                this.setSwitchGuideParent(true);
            }
        }
    };
    GuideModel.prototype.checkGiveGold = function () {
        // const needGold = IN_DONE_BT_GOLD
        // if (gameHpr.noviceServer.getGold() < needGold) {
        //     gameHpr.noviceServer.setGold(needGold)
        // }
        return false;
    };
    GuideModel.prototype.setSwitchGuideParent = function (data) {
        var canvasNode = cc.Canvas.instance.node;
        if (data.isView) {
            var guidePnl = canvasNode.Child('Notice/GuidePnl');
            if (guidePnl) {
                guidePnl.parent = canvasNode.Child('View');
                guidePnl.setSiblingIndex(guidePnl.parent.childrenCount - 1);
            }
        }
        else {
            var guidePnl = canvasNode.Child('View/GuidePnl');
            if (guidePnl) {
                guidePnl.parent = canvasNode.Child('Notice');
                guidePnl.setSiblingIndex(1);
            }
        }
        return false;
    };
    /* 建筑加速 */
    /* 医馆 */
    GuideModel.prototype.checkFuncHospitalTrigger = function (guide) {
        return __awaiter(this, void 0, void 0, function () {
            var injuryPawns, auid, armys, i, army;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || mc.currWindName !== 'novice' || !this.getIsCloseAllUI()) {
                            return [2 /*return*/, false];
                        }
                        injuryPawns = GameHelper_1.gameHpr.noviceServer.getInjuryPawns();
                        if (!(injuryPawns.length > 0)) return [3 /*break*/, 3];
                        auid = injuryPawns[0].auid;
                        if (!auid) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _a.sent();
                        for (i = 0; i < armys.length; i++) {
                            army = armys[i];
                            if (army.uid === auid && army.state === Enums_1.ArmyState.FIGHT) {
                                return [2 /*return*/, false];
                            }
                        }
                        _a.label = 2;
                    case 2: return [2 /*return*/, true];
                    case 3: return [2 /*return*/, false];
                }
            });
        });
    };
    //是否需要创建军队
    GuideModel.prototype.checkNeedCreateArmy = function (data) {
        var armys = this.noviceServer.getMyPlayerArmys();
        if (armys.some(function (m) { return m.state === Enums_1.ArmyState.NONE && m.pawns.length + m.drillPawns.length < Constant_1.ARMY_PAWN_MAX_COUNT; })) {
            //选择军队
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    //选择治疗的军队
    GuideModel.prototype.checkSelectHealArmy = function () {
        var e_6, _a;
        var _b;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildHospital'; });
        if (!ui) {
            return null;
        }
        var node = ui.FindChild('root/pages_n/1/army/list/view/content');
        try {
            for (var _c = __values(node.children), _d = _c.next(); !_d.done; _d = _c.next()) {
                var child = _d.value;
                var army = (_b = child.Data) === null || _b === void 0 ? void 0 : _b.army;
                if (army && army.state === Enums_1.ArmyState.NONE && army.pawns.length + army.drillPawns.length < Constant_1.ARMY_PAWN_MAX_COUNT) {
                    return { node: child };
                }
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return null;
    };
    /* 医馆 */
    /* 石头地资源说明 */
    GuideModel.prototype.checkFuncLandStoneTrigger = function (guide) {
        return __awaiter(this, void 0, void 0, function () {
            var armys, i, pawns, j, treasures, k, treasure;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index === NoviceConfig_1.NOVICE_MAINCITY_INDEX)
                            || !this.getIsCloseAllUI()) {
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _a.sent();
                        for (i = 0; i < armys.length; i++) {
                            pawns = armys[i].pawns;
                            for (j = 0; j < pawns.length; j++) {
                                treasures = pawns[j].treasures;
                                for (k = 0; k < treasures.length; k++) {
                                    treasure = treasures[k];
                                    if (treasure.id === NoviceConfig_1.NOVICE_FIRST_STONE_TREASURE_ID) {
                                        if (mc.currWindName === 'novice' || (mc.currWindName === 'area' && GameHelper_1.gameHpr.world.getLookCell().index === armys[i].index)) {
                                            return [2 /*return*/, true];
                                        }
                                    }
                                }
                            }
                        }
                        return [2 /*return*/, false];
                }
            });
        });
    };
    GuideModel.prototype.checkFuncLandStoneWind = function (data) {
        if (mc.currWindName === 'novice') {
            var guide = this.currGuide;
            guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        }
        return false;
    };
    GuideModel.prototype.nodeChooseArmyTreasure = function (data) {
        var ui = mc.getOpenPnls().find(function (m) { return m.key === data.key; });
        if (!ui) {
            return null;
        }
        var targetNode = null;
        if (data.key === 'area/AreaArmy') {
            targetNode = ui.Child('root/list_sv/view/content');
        }
        else {
            targetNode = ui.Child('root/pages_n/0/list/view/content');
        }
        for (var i = 0; i < targetNode.childrenCount; i++) {
            var child = targetNode.children[i];
            if (child.active && child.Data) {
                var pawns = child.Data.pawns;
                for (var j = 0; j < pawns.length; j++) {
                    var treasures = pawns[j].treasures;
                    for (var k = 0; k < treasures.length; k++) {
                        var treasure = treasures[k];
                        if (treasure.id === NoviceConfig_1.NOVICE_FIRST_STONE_TREASURE_ID) {
                            var child_1 = targetNode.children[i];
                            var treasureNode = child_1.Child('treasure_be') || child_1.Child('bottom/treasure_be');
                            return { node: treasureNode };
                        }
                    }
                }
            }
        }
        return null;
    };
    GuideModel.prototype.checkFuncLandStoneToTreasure = function (data) {
        var guide = this.currGuide;
        guide.index = guide.steps.findIndex(function (m) { return m.tag === data.tag; }) - 1; //这里减1是 下一步会加1
        return false;
    };
    GuideModel.prototype.nodeChooseArmyTreasureOpen = function () {
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'common/TreasureList'; });
        if (!ui) {
            return null;
        }
        var content = ui.Child('root_n/list_sv/view/content');
        for (var i = 0; i < content.childrenCount; i++) {
            var child = content.children[i];
            if (child.active && child.Data.id === NoviceConfig_1.NOVICE_FIRST_STONE_TREASURE_ID) {
                return { node: child.Child('open_be') };
            }
        }
        return null;
    };
    /* 石头地资源说明 */
    /* 和敌人对战 */
    GuideModel.prototype.checkFuncBattleBeginTrigger = function (guide) {
        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || mc.currWindName !== 'novice' || !this.getIsCloseAllUI()) {
            return false;
        }
        if (GameHelper_1.gameHpr.noviceServer.noviceEnemyObj.getIsFinishFirstAttack()) {
            this.saveGuideTag(guide.id, guide.complete());
            return false;
        }
        return GameHelper_1.gameHpr.noviceServer.noviceEnemyObj.getIsAttackingPlayerArea();
    };
    GuideModel.prototype.checkFuncShowAttackTips = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('help/NoticeGuide', data)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, false];
                }
            });
        });
    };
    //和敌人对战首次结束
    GuideModel.prototype.checkFuncFirstBattleEndTrigger = function (guide) {
        if (guide.isFinish() || !GameHelper_1.gameHpr.isNoviceMode || !this.getIsCloseAllUI()) {
            return false;
        }
        var smithyLv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
        if (smithyLv >= 10) {
            this.saveGuideTag(guide.id, guide.complete());
            return false;
        }
        if (this.noviceServer.battleCountWithEnemy > 0) {
            return true;
        }
        return false;
    };
    //更新铁匠铺等级
    GuideModel.prototype.checkFuncUpdateSmithyLv = function (data) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var lv, area, smithyLv, buildOjb, i, build, slots;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        lv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
                        if (!(lv <= data.lv)) return [3 /*break*/, 4];
                        area = GameHelper_1.gameHpr.noviceServer.getArea(NoviceConfig_1.NOVICE_MAINCITY_INDEX);
                        smithyLv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
                        if (0 === smithyLv) {
                            buildOjb = area.addBuild({ uid: ut.UID(), index: area.index, id: Constant_1.BUILD_SMITHY_NID, point: cc.v2(0, 0), lv: 1 }, true);
                            buildOjb.point.set(area.getBuildCanPlacePos(buildOjb));
                            (_a = GameHelper_1.gameHpr.areaCenter.getArea(area.index)) === null || _a === void 0 ? void 0 : _a.addBuild(buildOjb.strip());
                        }
                        for (i = 0; i < area.builds.length; i++) {
                            build = area.builds[i];
                            if (build.id === Constant_1.BUILD_SMITHY_NID || (build.id === Constant_1.BUILD_MAIN_NID && build.lv < data.lv)) {
                                GameHelper_1.gameHpr.noviceServer.cancelBT(build.uid);
                                build.lv = data.lv;
                                GameHelper_1.gameHpr.noviceServer.notifyPlayer(Enums_1.NotifyType.BUILD_UP, build.strip()); //通知玩家
                                GameHelper_1.gameHpr.noviceServer.notifyArea(area.index, Enums_1.NotifyType.BUILD_UP, build.strip()); //通知
                                GameHelper_1.gameHpr.noviceServer.updateBuildUpLvInfo(build.id, build.lv);
                                if (build.id === Constant_1.BUILD_SMITHY_NID) {
                                    slots = GameHelper_1.gameHpr.noviceServer.resetNextSelect(Enums_1.StudyType.EQUIP, data.lv);
                                    GameHelper_1.gameHpr.noviceServer.notifyPlayer(Enums_1.NotifyType.UPDATE_EQUIP_SLOT, slots); //通知玩家
                                }
                            }
                        }
                        GameHelper_1.gameHpr.player.updateBtQueue(GameHelper_1.gameHpr.noviceServer.getBTQueues());
                        GameHelper_1.gameHpr.noviceServer.setIron(GameHelper_1.gameHpr.noviceServer.getIron() + data.iron);
                        GameHelper_1.gameHpr.player.setIron(GameHelper_1.gameHpr.player.getIron() + data.iron);
                        if (!(((_b = GameHelper_1.gameHpr.novice.getLookCell()) === null || _b === void 0 ? void 0 : _b.index) !== NoviceConfig_1.NOVICE_MAINCITY_INDEX)) return [3 /*break*/, 4];
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('novice')];
                    case 1:
                        _c.sent();
                        _c.label = 2;
                    case 2:
                        GameHelper_1.gameHpr.novice.setLookCell(GameHelper_1.gameHpr.novice.getMapCellByIndex(NoviceConfig_1.NOVICE_MAINCITY_INDEX));
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                    case 3:
                        _c.sent();
                        _c.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    GuideModel = __decorate([
        mc.addmodel('guide')
    ], GuideModel);
    return GuideModel;
}(mc.BaseModel));
exports.default = GuideModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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