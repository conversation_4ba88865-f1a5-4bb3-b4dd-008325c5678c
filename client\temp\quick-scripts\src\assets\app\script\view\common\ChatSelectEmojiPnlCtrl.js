"use strict";
cc._RF.push(module, 'f95ea2fwXxLeqPJXr+1jQCl', 'ChatSelectEmojiPnlCtrl');
// app/script/view/common/ChatSelectEmojiPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var ChatSelectEmojiPnlCtrl = /** @class */ (function (_super) {
    __extends(ChatSelectEmojiPnlCtrl, _super);
    function ChatSelectEmojiPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.lastUseNode_ = null; // path://root/last_use_n
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'CHAT_SELECT_EMOJI_TAB';
        _this.EMOJI_SIZE = cc.size(64, 64);
        _this.user = null;
        _this.cb = null;
        return _this;
    }
    ChatSelectEmojiPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ChatSelectEmojiPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        // 刷新历史使用
        this.updateLastUse();
        this.tabsTc_.Tabs(this.user.getTempPreferenceMap(this.PKEY_TAB) || 0);
    };
    ChatSelectEmojiPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    ChatSelectEmojiPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    ChatSelectEmojiPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        this.cb && this.cb(id);
        this.hide();
        this.addUseCount(id);
    };
    // path://root/last_use_n/last_use_be
    ChatSelectEmojiPnlCtrl.prototype.onClickLastUse = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        this.cb && this.cb(id);
        this.hide();
    };
    // path://root/tabs_tc_tce
    ChatSelectEmojiPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        this.updateEmojis(type);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    ChatSelectEmojiPnlCtrl.prototype.updateLastUse = function () {
        var _this = this;
        var list = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT) || [];
        var len = list.length;
        // 这里兼容一下
        var emojis = this.user.getUnlockChatEmojiIds();
        for (var i = list.length - 1; i >= 0; i--) {
            var id = list[i].id, type = Math.floor(id / 1000);
            if (type === 1) {
                continue;
            }
            else if (!emojis.has(id)) {
                list.splice(i, 1);
            }
        }
        if (list.length !== len) {
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT, list);
        }
        list.sort(function (a, b) { return b.count - a.count; });
        this.lastUseNode_.Items(list.slice(0, 5), function (it, data) {
            it.Data = data.id;
            var val = it.Child('val');
            val.scale = 1;
            ResHelper_1.resHelper.loadEmojiIcon(data.id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE); });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.addUseCount = function (id) {
        var list = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT) || [];
        var it = list.find(function (m) { return m.id === id; });
        if (it) {
            it.count += 1;
        }
        else {
            list.push({ id: id, count: 1 });
        }
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT, list);
    };
    ChatSelectEmojiPnlCtrl.prototype.updateEmojis = function (type) {
        var _this = this;
        var ids = this.getCanUseEmojis(type);
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Child('empty').active = type !== 0 && ids.length === 0;
        this.listSv_.List(ids.length, function (it, i) {
            var id = it.Data = ids[i];
            var val = it.Child('val');
            ResHelper_1.resHelper.loadEmojiIcon(id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE); });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.getCanUseEmojis = function (type) {
        type = type + 1;
        var datas = [];
        assetsMgr.getJson('chatEmoji').datas.forEach(function (m) { return (m.type === type && m.cond === 0) && datas.push(m.id); });
        if (type === 2) { //这里加上购买的动态表情
            datas.pushArr(this.user.getUnlockChatEmojiIds());
        }
        return datas;
    };
    ChatSelectEmojiPnlCtrl = __decorate([
        ccclass
    ], ChatSelectEmojiPnlCtrl);
    return ChatSelectEmojiPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ChatSelectEmojiPnlCtrl;

cc._RF.pop();