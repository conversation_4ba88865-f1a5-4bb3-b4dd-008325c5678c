
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ShareHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '062c1qVfV5IzaW8oFpO1o1B', 'ShareHelper');
// app/script/common/helper/ShareHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.shareHelper = void 0;
var Enums_1 = require("../constant/Enums");
var FacebookHelper_1 = require("./FacebookHelper");
var GameHelper_1 = require("./GameHelper");
var TaHelper_1 = require("./TaHelper");
var WxHelper_1 = require("./WxHelper");
var ShareHelper = /** @class */ (function () {
    function ShareHelper() {
        this.shareFailCount = 0;
        this.defaultShareList = [
            {
                key: 'login.share_text_01',
                url: 'slgshare01.png'
            },
            {
                key: 'login.share_text_02',
                url: 'slgshare02.png'
            },
        ];
    }
    Object.defineProperty(ShareHelper.prototype, "IMAGE_URL", {
        get: function () {
            if (GameHelper_1.gameHpr.isInland()) {
                return 'https://jwm-inland-file.twomiles.cn/share/';
            }
            return 'https://jwm-global-file.twomiles.cn/share/' + mc.lang + '/';
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ShareHelper.prototype, "shareFailCheckTimes", {
        get: function () {
            if (ut.isMiniGame()) {
                return [2.5, 2, 1.5, 1];
            }
            else {
                return [2, 3, 2.5, 2];
            }
        },
        enumerable: false,
        configurable: true
    });
    ShareHelper.prototype.init = function () {
        var _this = this;
        if (ut.isMiniGame()) {
            wx.showShareMenu({
                withShareTicket: true,
                menus: ['shareAppMessage', 'shareTimeline'],
            });
            var wxShare = wx.uma || wx;
            wxShare.onShareAppMessage(function () { return _this.getShareAppMessageInfo(); });
            if (wx.onShareTimeline) {
                wx.onShareTimeline(function () { return _this.getShareAppMessageInfo(); });
            }
        }
    };
    ShareHelper.prototype.getShareAppMessageInfo = function () {
        var info = this.defaultShareList.random();
        var queryInfo = {
            uid: GameHelper_1.gameHpr.getUid(),
            type: Enums_1.ShareType.ON_SHARE_APP_MESSAGE,
            imageName: info.url,
        };
        return {
            title: assetsMgr.lang(info.key),
            imageUrl: this.IMAGE_URL + info.url,
            query: GameHelper_1.gameHpr.encodeURIObj(queryInfo)
        };
    };
    ShareHelper.prototype.getRandomShareInfo = function () {
        var info = this.defaultShareList.random();
        return { title: assetsMgr.lang(info.key), url: this.IMAGE_URL + info.url };
    };
    // 分享
    // @ut.syncLock
    ShareHelper.prototype.share = function (title, imgUrl, queryInfo, loose, failToast) {
        var _a;
        if (loose === void 0) { loose = true; }
        return __awaiter(this, void 0, void 0, function () {
            var randomInfo, uid, now, result, url, passTime;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!title || !imgUrl) {
                            randomInfo = this.getRandomShareInfo();
                            title = title || randomInfo.title;
                            imgUrl = imgUrl || randomInfo.url;
                        }
                        uid = GameHelper_1.gameHpr.getUid();
                        queryInfo = queryInfo || {};
                        queryInfo.uid = uid;
                        queryInfo.type = (_a = queryInfo.type) !== null && _a !== void 0 ? _a : Enums_1.ShareType.NONE;
                        now = Date.now();
                        result = true;
                        if (!ut.isMiniGame()) return [3 /*break*/, 2];
                        return [4 /*yield*/, WxHelper_1.wxHelper.share(title, imgUrl, queryInfo, loose)];
                    case 1:
                        result = _b.sent();
                        return [3 /*break*/, 6];
                    case 2:
                        if (!ut.isMobile()) return [3 /*break*/, 5];
                        url = this.getUrl(imgUrl) + ("?af_force_deeplink=true&deep_link_value=" + this.getLinkParams(uid, queryInfo.type));
                        if (!GameHelper_1.gameHpr.isGLobal()) return [3 /*break*/, 4];
                        return [4 /*yield*/, FacebookHelper_1.facebookHelper.share({ url: url, title: title, imgUrl: imgUrl })];
                    case 3:
                        result = _b.sent();
                        _b.label = 4;
                    case 4: return [3 /*break*/, 6];
                    case 5:
                        loose = true;
                        result = true;
                        _b.label = 6;
                    case 6:
                        // 国内小程序、app微信及朋友圈版本检查分享时间，伪判断
                        if (ut.isWechatGame()) {
                            passTime = Date.now() - now;
                            if (this.shareFailCount >= this.shareFailCheckTimes.length) { //失败次数太多还是给他强制成功
                                result = true;
                            }
                            else if (!loose && result && passTime < this.shareFailCheckTimes[this.shareFailCount] * ut.Time.Second) { //如果分享的时间小于给定阈值，判断为失败
                                result = false;
                            }
                        }
                        if (result) {
                            this.shareFailCount = 0;
                            // 分享数据上报
                            TaHelper_1.taHelper.track('ta_share', { share_id: queryInfo.type, share_way: 1 });
                        }
                        else {
                            this.shareFailCount++;
                            if (!loose) {
                                this.showFailToast(failToast);
                            }
                        }
                        return [2 /*return*/, result];
                }
            });
        });
    };
    ShareHelper.prototype.getUrl = function (imageUrl) {
        if (GameHelper_1.gameHpr.isInland()) {
            return 'https://jwm-inland-file.twomiles.cn/website/index.html';
        }
        var key = 'slgshare01';
        if (imageUrl === null || imageUrl === void 0 ? void 0 : imageUrl.startsWith('http')) {
            key = imageUrl.replace(this.IMAGE_URL, '').split('.')[0];
        }
        return "https://ninety.onelink.me/lsT8/" + mc.lang + key;
    };
    ShareHelper.prototype.getLinkParams = function (uid, type) {
        return encodeURIComponent(uid + "|" + type);
    };
    ShareHelper.prototype.showFailToast = function (toast) {
        // if (toast === '') {
        //     return
        // } else if (!toast) {
        //     let toasts
        //     if (gameHelper.isGLobal()) {
        //         toast = 'toast.toast_shareRecord_failed'
        //     } else {
        //         if (this.shareFailCount < 2) {
        //             toasts = ["toast.share_fail_1", "toast.share_fail_2", "toast.share_fail_5"]
        //         }
        //         else {
        //             toasts = ["toast.share_fail_3", "toast.share_fail_4"]
        //         }
        //         let index = ut.randomIndex(toasts.length)
        //         toast = toasts[index]
        //     }
        // }
        // viewHelper.showAlert(toast)
    };
    // 跳转
    ShareHelper.prototype.jump = function (type, opts) {
        return __awaiter(this, void 0, void 0, function () {
            var info;
            return __generator(this, function (_a) {
                info = { loose: true, failTast: '' };
                if (type === Enums_1.ShareType.INVITE) {
                    info.queryInfo = { type: type };
                }
                return [2 /*return*/, this.share(info.title, info.imgUrl, info.queryInfo, info.loose, info.failTast)];
            });
        });
    };
    return ShareHelper;
}());
exports.shareHelper = new ShareHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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