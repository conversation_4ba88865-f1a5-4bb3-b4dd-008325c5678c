
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/RollListCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8c462gN5qlCHaUzWyaMRV+X', 'RollListCmpt');
// app/script/view/cmpt/RollListCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var RollListCmpt = /** @class */ (function (_super) {
    __extends(RollListCmpt, _super);
    function RollListCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.touchNode = null;
        _this.key = 'RollListCmpt'; //用于加载标记
        _this.ITEM_COUNT = 7;
        _this.ITEM_MIN_INDEX = 3;
        _this.ITEM_INDEX_ADD = 3;
        _this.touchId = -1;
        _this.isMove = false;
        _this.items = null;
        _this.autoRollItems = []; //自动滚动列表
        _this.autoRollDir = 0;
        _this.deltaX = 0;
        _this.centreDataIndex = -1; //当前中间的槽位数据index
        _this.cilickBeginIndex = -1; //点击时候的index
        _this.cilickEndIndex = -1; //点击时候的index
        _this.datas = [];
        _this.dataCount = 0;
        _this.lockTouch = false; //是否禁止滑动
        _this._temp_vec2 = cc.v2();
        return _this;
    }
    RollListCmpt.prototype.onLoad = function () {
        var _this = this;
        if (this.items) {
            return;
        }
        this.items = [];
        this.node.Items(this.ITEM_COUNT, function (it, _, i) {
            it.name = 'item_' + i;
            _this.items.push({ index: i, node: it, anim: it.getComponent(cc.Animation) });
        });
        this.touchNode.on(cc.Node.EventType.TOUCH_START, this.onListTouchStart, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onListTouchMove, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_END, this.onListTouchEnd, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onListTouchEnd, this);
        this.touchNode.SetSwallowTouches(false);
    };
    RollListCmpt.prototype.onDestroy = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    RollListCmpt.prototype.init = function (list) {
        if (!this.items) {
            this.onLoad();
        }
        this.datas = list;
        this.dataCount = list.length;
    };
    RollListCmpt.prototype.reset = function (id) {
        var startIndex = this.datas.findIndex(function (m) { return m.id === id; });
        this.cilickBeginIndex = -1;
        this.autoRollDir = 0;
        this.autoRollItems.length = 0;
        if (startIndex === -1) {
            startIndex = this.centreDataIndex == -1 ? 0 : this.centreDataIndex;
        }
        this.centreDataIndex = -1;
        var index = -this.ITEM_INDEX_ADD, len = this.dataCount, list = this.datas;
        for (var x = 0; x < this.ITEM_COUNT; x++) {
            var i = startIndex + index + x;
            if (i < 0) {
                i += len;
            }
            else if (i >= len) {
                i = 0;
            }
            var m = this.items[x];
            this.updateItemInfo(m, list[i]);
            this.setItemProgress(m, x);
        }
    };
    RollListCmpt.prototype.setLockTouch = function (val) {
        this.lockTouch = val;
    };
    // 列表的触摸
    RollListCmpt.prototype.onListTouchStart = function (event) {
        if (this.lockTouch || this.touchId !== -1 || this.isRolling()) {
            return;
        }
        this.isMove = false;
        this.deltaX = 0;
        this.touchId = event.getID();
        this.cilickBeginIndex = Math.round(this.items[0].index);
    };
    RollListCmpt.prototype.onListTouchMove = function (event) {
        var _this = this;
        if (this.lockTouch || this.touchId !== event.getID()) {
            return;
        }
        else if (event.getStartLocation().sub(event.getLocation(), this._temp_vec2).mag() < 7) {
            return;
        }
        this.isMove = true;
        var deltaX = this.deltaX = event.getDeltaX() / 230;
        var index = 0;
        this.items.forEach(function (m) {
            _this.setItemProgress(m, m.index + deltaX);
            if (Math.round(m.index) === _this.ITEM_MIN_INDEX) {
                index = m.data.index;
            }
        });
        if (this.centreDataIndex !== index) {
            this.updateNextItem(index);
        }
    };
    RollListCmpt.prototype.onListTouchEnd = function (event) {
        if (this.lockTouch || this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        this.deltaX = 0;
        if (this.isMove) {
            this.isMove = false;
            this.cilickEndIndex = Math.round(this.items[0].index);
            this.setAutoRoll(this.cilickEndIndex);
        }
    };
    // 刷新信息
    RollListCmpt.prototype.updateItemInfo = function (item, data) {
        var node = item.node;
        node.Data = item.data = data;
        ResHelper_1.resHelper.loadAlliIcon(data.id, item.node.Child('val'), this.key);
    };
    RollListCmpt.prototype.setItemProgress = function (it, index) {
        it.index = this.wrapItemIndex(index);
        it.anim.setCurrentTime(it.index, 'roll_list_item');
        var node = it.node;
        node.zIndex = Math.floor(it.node.scale * 1000);
        node.Color(node.scale > 0.98 ? '#FADFA7' : '#E9DDC7');
    };
    // 设置自动滚
    RollListCmpt.prototype.setAutoRoll = function (index) {
        var it = this.items[0];
        this.autoRollItems.length = 0;
        this.autoRollDir = ut.normalizeNumber(index - it.index);
        it.targetIndex = index = this.wrapItemIndex(index);
        if (this.autoRollDir !== 0) {
            this.autoRollItems.push(it);
            for (var i = 1, l = this.items.length; i < l; i++) {
                it = this.items[i];
                it.targetIndex = index = ut.loopValue(index + 1, l);
                this.autoRollItems.push(it);
            }
        }
    };
    RollListCmpt.prototype.wrapItemIndex = function (index) {
        index = index % this.ITEM_COUNT;
        if (index < -0.5) {
            index += this.ITEM_COUNT;
        }
        else if (index > this.ITEM_COUNT - 0.5) {
            index -= this.ITEM_COUNT;
        }
        return index;
    };
    RollListCmpt.prototype.wrapDataIndex = function (index) {
        index = index % this.dataCount;
        if (index < 0) {
            index += this.dataCount;
        }
        return index;
    };
    RollListCmpt.prototype.updateNextItem = function (index) {
        var _this = this;
        this.centreDataIndex = index;
        var endIndex = this.ITEM_COUNT - 1, add = this.ITEM_INDEX_ADD;
        this.items.forEach(function (m) {
            var i = Math.round(m.index);
            if (i <= 0) {
                _this.checkUpdateItemInfo(m, _this.datas[_this.wrapDataIndex(index - add)]);
            }
            else if (i >= endIndex) {
                _this.checkUpdateItemInfo(m, _this.datas[_this.wrapDataIndex(index + add)]);
            }
        });
    };
    RollListCmpt.prototype.checkUpdateItemInfo = function (item, data) {
        var _a;
        if (((_a = item.data) === null || _a === void 0 ? void 0 : _a.id) !== data.id) {
            this.updateItemInfo(item, data);
        }
    };
    RollListCmpt.prototype.update = function (dt) {
        if (this.autoRollDir !== 0) {
            var deltaX = dt * 5 * this.autoRollDir;
            for (var i = this.autoRollItems.length - 1; i >= 0; i--) {
                var it = this.autoRollItems[i];
                it.index += deltaX;
                if (it.index > this.ITEM_COUNT) {
                    it.index = 0;
                    this.autoRollItems.splice(i, 1);
                }
                else if (it.index < 0 && it.targetIndex === this.ITEM_COUNT - 1) {
                    it.index = this.ITEM_COUNT - 0.5 + deltaX;
                }
                else if ((this.autoRollDir > 0 && it.index >= it.targetIndex) || (this.autoRollDir < 0 && it.index <= it.targetIndex)) {
                    it.index = it.targetIndex;
                    this.autoRollItems.splice(i, 1);
                }
                this.setItemProgress(it, it.index);
            }
            if (this.autoRollItems.length === 0) {
                this.autoRollDir = 0;
            }
        }
    };
    RollListCmpt.prototype.isRolling = function () {
        return this.autoRollDir !== 0 || this.deltaX > 0;
    };
    RollListCmpt.prototype.isCanClickSelect = function () {
        return !this.isRolling();
    };
    // 自动滚动一页
    RollListCmpt.prototype.rollPage = function (val) {
        this.setAutoRoll(this.items[0].index + val);
    };
    // 滚动到指定页
    RollListCmpt.prototype.rollPageByIndex = function (index) {
        this.reset(index);
    };
    // 刷新指定数据信息
    RollListCmpt.prototype.updateItem = function (data) {
        var it = this.items.find(function (m) { var _a; return ((_a = m.data) === null || _a === void 0 ? void 0 : _a.id) === data.id; });
        it && this.updateItemInfo(it, data);
    };
    // 选择一个影片
    RollListCmpt.prototype.selectItem = function (id) {
        var it = this.items.find(function (m) { var _a; return ((_a = m.data) === null || _a === void 0 ? void 0 : _a.id) === id; });
        if (it) {
            it.data.id = id;
            this.updateItemInfo(it, it.data);
        }
    };
    // 获取正前方的节点
    RollListCmpt.prototype.getCurHitItem = function () {
        var len = this.ITEM_COUNT, item = this.items[len - 1];
        for (var i = len - 1; i >= 0; i--) {
            var it = this.items[i];
            if (item.node.zIndex < it.node.zIndex) {
                item = it;
            }
        }
        return item;
    };
    RollListCmpt.prototype.getCurHitItemData = function () {
        var _a;
        return (_a = this.getCurHitItem()) === null || _a === void 0 ? void 0 : _a.data;
    };
    __decorate([
        property(cc.Node)
    ], RollListCmpt.prototype, "touchNode", void 0);
    RollListCmpt = __decorate([
        ccclass
    ], RollListCmpt);
    return RollListCmpt;
}(cc.Component));
exports.default = RollListCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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