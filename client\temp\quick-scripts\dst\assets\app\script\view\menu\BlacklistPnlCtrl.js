
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/BlacklistPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd638cfkHDJF2a6RH7YN7nsN', 'BlacklistPnlCtrl');
// app/script/view/menu/BlacklistPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BlacklistPnlCtrl = /** @class */ (function (_super) {
    __extends(BlacklistPnlCtrl, _super);
    function BlacklistPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.model = null;
        return _this;
    }
    BlacklistPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BLACKLISTS] = this.onUpdateBlacklists, _a.enter = true, _a),
        ];
    };
    BlacklistPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('friend');
                return [2 /*return*/];
            });
        });
    };
    BlacklistPnlCtrl.prototype.onEnter = function (data) {
        this.showBlacklist();
    };
    BlacklistPnlCtrl.prototype.onRemove = function () {
    };
    BlacklistPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/remove_black_be
    BlacklistPnlCtrl.prototype.onClickRemoveBlack = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showMessageBox('ui.remove_blacklist_tip', {
                params: [ut.nameFormator(data.nickname, 7)],
                ok: function () { return _this.model.doBlacklist(data.uid, true).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); }); },
                cancel: function () { }
            });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BlacklistPnlCtrl.prototype.onUpdateBlacklists = function () {
        this.showBlacklist();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BlacklistPnlCtrl.prototype.showBlacklist = function () {
        var _this = this;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        var list = this.model.getBlacklists(), len = list.length;
        list.sort(function (a, b) { return b.time - a.time; });
        this.listSv_.Child('empty').active = !len;
        this.listSv_.List(len, function (it, i) {
            var data = it.Data = list[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            it.Child('time').setLocaleKey('ui.add_blacklist_time', ut.dateFormat('MM-dd hh:mm:ss', data.time));
        });
    };
    BlacklistPnlCtrl = __decorate([
        ccclass
    ], BlacklistPnlCtrl);
    return BlacklistPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BlacklistPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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