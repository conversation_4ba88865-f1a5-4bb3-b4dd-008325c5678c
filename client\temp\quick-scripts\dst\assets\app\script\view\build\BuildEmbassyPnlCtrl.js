
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildEmbassyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '62915Y1qj5KnKwJPnI19RSc', 'BuildEmbassyPnlCtrl');
// app/script/view/build/BuildEmbassyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var EquipInfo_1 = require("../../model/main/EquipInfo");
var ccclass = cc._decorator.ccclass;
var BuildEmbassyPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildEmbassyPnlCtrl, _super);
    function BuildEmbassyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.alliTabsTc_ = null; // path://root/pages_n/1/alliance/alli_tabs_tc_tce
        _this.alliPagesNode_ = null; // path://root/pages_n/1/alliance/alli_pages_n
        _this.applysNode_ = null; // path://root/pages_n/1/alliance/alli_pages_n/0/top/applys_n_be
        _this.noticeNode_ = null; // path://root/pages_n/1/alliance/alli_pages_n/0/top/notice_n
        //@end
        _this.alliance = null;
        _this.data = null;
        _this.currTab = 0;
        _this.currChildTab = 0;
        _this.currSelectBattleDate = '';
        _this.yetApplyCount = 0;
        return _this;
    }
    BuildEmbassyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ALLIANCE] = this.onUpdateAlliance, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_ALLIANCE_APPLYS] = this.onUpdateAllianceApplys, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_ALLIANCE_MEMBERS] = this.onUpdateAllianceMembers, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_ALLIANCE_MEMBER_EMBASSY_LV] = this.onUpdateAllianceMemberEmbassyLv, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_ALLIANCE_NOTICE] = this.onUpdateAllianceNotice, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_ALLIANCE_MEMBER_JOB] = this.onUpdateAllianceMemberJob, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_ALLIANCE_BASEINFO] = this.onUpdateAllianceBaseInfo, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_ALLI_LEADER_VOTE_INFO] = this.onUpdateAlliLeaderVoteInfo, _j.enter = true, _j),
        ];
    };
    BuildEmbassyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.alliance = this.getModel('alliance');
                this.pagesNode_.Child('1/empty/list', cc.ScrollView).Items(0);
                this.noticeNode_.Data = this.noticeNode_.Child('edit_notice_be').y;
                return [2 /*return*/];
            });
        });
    };
    BuildEmbassyPnlCtrl.prototype.onEnter = function (data) {
        this.initDateList();
        this.data = data;
        this.tabsTc_.Tabs(this.currTab);
    };
    BuildEmbassyPnlCtrl.prototype.onRemove = function () {
    };
    BuildEmbassyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildEmbassyPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.currTab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            this.updateBuildBaseInfo(node);
            this.updateBuildAttrInfo(node);
        }
        else if (type === 1) {
            this.updateAllianceInfo(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildEmbassyPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/empty/list/view/content/item/root/buttons/apply_be
    BuildEmbassyPnlCtrl.prototype.onClickApply = function (event, _) {
        var it = event.target.parent.parent.parent, data = it.Data;
        if (!data) {
            return;
        }
        else if (this.yetApplyCount >= Constant_1.ALLI_APPLY_MAX_COUNT) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.APPLY_ALLIANCE_TOOMANY);
        }
        this.apply(it);
    };
    // path://root/pages_n/1/empty/list/view/content/item/root/buttons/cancel_apply_be
    BuildEmbassyPnlCtrl.prototype.onClickCancelApply = function (event, _) {
        var _this = this;
        var it = event.target.parent.parent.parent, data = it.Data;
        data && GameHelper_1.gameHpr.world.cancelJoinAlliance(data.uid).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                it.Child('root/buttons').Swih('apply_be');
                ViewHelper_1.viewHelper.showAlert('toast.cancel_apply');
                _this.yetApplyCount -= 1;
            }
        });
    };
    // path://root/pages_n/1/empty/buttons/creare_be
    BuildEmbassyPnlCtrl.prototype.onClickCreare = function (event, data) {
        if (GameHelper_1.gameHpr.user.isGuest() && !GameHelper_1.gameHpr.getGuestCanCreateAlli()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_create_alli_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        ViewHelper_1.viewHelper.showPnl('build/CreateAlliance');
    };
    // path://root/pages_n/1/alliance/alli_tabs_tc_tce
    BuildEmbassyPnlCtrl.prototype.onClickAlliTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        this.showAllianceInfo(Number(event.node.name));
    };
    // path://root/pages_n/1/vote/list/view/content/item/head/pos_be
    BuildEmbassyPnlCtrl.prototype.onClickPos = function (event, data) {
        var index = event.target.Data;
        if (index) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos(index);
        }
    };
    // path://root/pages_n/1/alliance/alli_pages_n/1/list/view/content/member_be
    BuildEmbassyPnlCtrl.prototype.onClickMember = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('build/AlliMemberInfo', data);
        }
    };
    // path://root/pages_n/1/alliance/alli_pages_n/0/top/applys_n_be
    BuildEmbassyPnlCtrl.prototype.onClickApplys = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/AlliApplyList');
    };
    // path://root/pages_n/1/alliance/alli_pages_n/2/date_select_be
    BuildEmbassyPnlCtrl.prototype.onClickDateSelect = function (event, data) {
        this.changeDateList(event.target, true);
    };
    // path://root/pages_n/1/alliance/alli_pages_n/2/date_select_be/date_mask_be
    BuildEmbassyPnlCtrl.prototype.onClickDateMask = function (event, data) {
        this.changeDateList(event.target.parent, false);
    };
    // path://root/pages_n/1/alliance/alli_pages_n/2/date_select_be/mask/list/view/content/date_item_be
    BuildEmbassyPnlCtrl.prototype.onClickDateItem = function (event, _) {
        var node = this.alliPagesNode_.Child(2).Child('top/date_select_be');
        this.changeDateList(node, false);
        var date = event.target.Data;
        if (date && date !== this.currSelectBattleDate) {
            this.selectDateItem(node, date);
        }
    };
    // path://root/pages_n/1/alliance/alli_pages_n/2/battle_record_desc_be
    BuildEmbassyPnlCtrl.prototype.onClickBattleRecordDesc = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.battle_record_desc_0' },
            { key: 'ui.battle_record_desc_1' },
            { key: 'ui.battle_record_desc_2' },
            { key: 'ui.battle_record_desc_3' },
            { key: 'ui.battle_record_desc_4' },
        ]);
    };
    // path://root/pages_n/1/alliance/alli_pages_n/0/policy_nbe
    BuildEmbassyPnlCtrl.prototype.onClickPolicy = function (event, _) {
        var data = event.target.Data;
        if (!data) {
            return ViewHelper_1.viewHelper.showAlert('toast.need_sum_embassy_lv', { params: [GameHelper_1.gameHpr.getAlliPolicySlotConfByIndex(Number(event.target.name))] });
        }
        else if (data.info) {
            return ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.info.id);
        }
        var selects = this.alliance.getSelectPolicysByIndex(data.index);
        if (selects.length > 0) {
            ViewHelper_1.viewHelper.showPnl('build/AlliPolicySelect', this.alliance.getSelectPolicysByIndex(data.index), data.index);
        }
    };
    // path://root/pages_n/1/vote/list/view/content/item/button/vote_be
    BuildEmbassyPnlCtrl.prototype.onClickVote = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data, info = data === null || data === void 0 ? void 0 : data.info;
        if (!info) {
            return;
        }
        var times = this.alliance.getVoteTimes();
        if (times >= Constant_1.ALLI_LEADER_VOTE_MAX_COUNT) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ALLI_VOTE_LIMIT);
        }
        else if (times > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.change_vote_tip', {
                params: [info.nickname, Constant_1.ALLI_LEADER_VOTE_MAX_COUNT - times],
                ok: function () { return _this.doVote(data.uid); },
                cancel: function () { }
            });
        }
        else {
            this.doVote(data.uid);
        }
    };
    // path://root/pages_n/1/vote/buttons/creare_info_be
    BuildEmbassyPnlCtrl.prototype.onClickCreareInfo = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/CreateAlliance');
    };
    // path://root/pages_n/1/alliance/alli_pages_n/0/top/notice_n/edit_notice_be
    BuildEmbassyPnlCtrl.prototype.onClickEditNotice = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var notice = this.alliance.getNotice();
        ViewHelper_1.viewHelper.showPnl('build/EditAlliNotice', notice, function (ok, text) {
            if (!ok || !_this.isValid) {
                return;
            }
            else if (notice === text) {
                return;
            }
            else if (ut.getStringLen(text) > 200) {
                return ViewHelper_1.viewHelper.showAlert('toast.no_exceed_char', { params: [200] });
            }
            _this.alliance.changeNotice(text).then(function (err) {
                if (err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                    return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
                }
                else if (err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                    return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_content');
                }
                else if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.updateAlliNotice();
                }
            });
        });
    };
    // path://root/pages_n/1/alliance/alli_pages_n/0/policy_title/lay/desc/alli_lv_desc_be
    BuildEmbassyPnlCtrl.prototype.onClickAlliLvDesc = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.alli_policy_desc_0' },
            { key: 'ui.alli_policy_desc_1', params: [Object.values(GameHelper_1.gameHpr.getAlliPolicySlotConf()).join2(function (m) { return assetsMgr.lang('ui.short_lv', m); }, ', ')] },
            { key: 'ui.alli_policy_desc_2' },
            { key: 'ui.alli_policy_desc_3' },
        ]);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildEmbassyPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top').Child('icon/lv/val').setLocaleKey('ui.lv', data.lv);
            this.updateBuildAttrInfo(node);
        }
    };
    // 更新联盟信息
    BuildEmbassyPnlCtrl.prototype.onUpdateAlliance = function () {
        this.updateAllianceInfo(this.pagesNode_.Swih(1)[0]);
    };
    // 更新申请列表
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceApplys = function () {
        this.updateApplyNode();
    };
    // 更新成员列表
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceMembers = function () {
        this.updateAlliBase();
        this.updateAlliMembers();
    };
    // 更新成员大使馆等级
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceMemberEmbassyLv = function () {
        var node = this.alliPagesNode_.Child('0');
        var pers = node.Child('top').Child('icon/pers');
        pers.Child('cur', cc.Label).string = '' + this.alliance.getMembers().length;
        pers.Child('max', cc.Label).string = '/' + this.alliance.getPersLimit();
        // 总大使馆等级
        node.Child('info').Child('content/0', cc.Label).string = '' + this.alliance.getSumEmbassyLv();
        // 联盟内政
        this.updateAlliPolicySlot(node);
    };
    // 更新联盟公告
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceNotice = function () {
        if (this.currChildTab === 0) {
            this.updateAlliNotice();
        }
    };
    // 更新成员职位
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceMemberJob = function (member) {
        if (this.currChildTab === 0) {
            this.updateApplyNode();
        }
        else if (this.currChildTab === 1) {
            this.updateAlliMembers();
        }
    };
    // 刷新联盟政策
    BuildEmbassyPnlCtrl.prototype.onUpdateAllianceBaseInfo = function (uids) {
        if (uids.has(this.alliance.getUid())) {
            this.updateAlliPolicySlot();
            ViewHelper_1.viewHelper.hidePnl('build/AlliPolicySelect');
        }
    };
    // 刷新投票信息
    BuildEmbassyPnlCtrl.prototype.onUpdateAlliLeaderVoteInfo = function () {
        if (this.currTab === 1) {
            var node = this.pagesNode_.Child(1);
            this.showVoteInfo(node.Child('vote'));
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 基础上信息
    BuildEmbassyPnlCtrl.prototype.updateBuildBaseInfo = function (node) {
        var top = node.Child('info/top'), data = this.data;
        ResHelper_1.resHelper.loadBuildIcon(data.icon, top.Child('icon/val', cc.Sprite), this.key);
        top.Child('icon/name').setLocaleKey(data.name);
        top.Child('icon/lv/val').setLocaleKey('ui.lv', data.lv);
        var descLbl = top.Child('desc', cc.Label);
        if (this.alliance.isMeCreater() && !GameHelper_1.gameHpr.world.isKarmicMahjong()) {
            descLbl.setLocaleKey(data.desc + '_1');
        }
        else if (this.alliance.getUid()) {
            descLbl.setLocaleKey(data.desc + '_2');
        }
        else {
            descLbl.setLocaleKey(data.desc);
        }
        descLbl._forceUpdateRenderData();
        top.height = Math.max(220, descLbl.node.height + 168);
    };
    // 刷新属性
    BuildEmbassyPnlCtrl.prototype.updateBuildAttrInfo = function (node) {
        var data = this.data;
        var attrs = node.Child('info/attrs'), bottom = node.Child('bottom'), isMaxLv = data.isMaxLv();
        var isMeCreater = this.alliance.isMeCreater();
        if (attrs.setActive(isMeCreater || !!this.alliance.getUid())) {
            var effects = [], lv = data.lv;
            // 如果是创建者并且不是血战到底 要显示成员数
            if (isMeCreater && !GameHelper_1.gameHpr.world.isKarmicMahjong()) {
                var cnt = lv * 2, nextVal = isMaxLv ? '' : ((lv + 1) * 2) + '';
                effects.push({ curr: { key: 'ui.build_eff_desc_alli_pers', params: [cnt] }, nextVal: nextVal });
            }
            // 显示总大使馆等级
            var sumEmbassyLv = GameHelper_1.gameHpr.alliance.getSumEmbassyLv();
            effects.push({ curr: { key: 'ui.sum_embassy_lv2', params: [sumEmbassyLv] }, nextVal: (sumEmbassyLv + 1) + '' });
            ViewHelper_1.viewHelper._updateBuildAttrInfo(data, attrs, bottom, effects, this.key);
        }
        else if (bottom.active = !isMaxLv) { //单独刷新费用和按钮
            ViewHelper_1.viewHelper.updateBuildBottomInfo(data, bottom);
            // viewHelper.updateCostViewForBuild(bottom, data.upCost, data.attrJson.bt_time)
            // viewHelper.updateBuildButtons(bottom.Child('buttons'), data)
        }
    };
    // 初始化联盟信息
    BuildEmbassyPnlCtrl.prototype.updateAllianceInfo = function (node) {
        var has = !!this.alliance.getUid();
        if (!has) {
            return this.updateAllianceList(node.Swih('empty')[0]);
        }
        else if (this.alliance.isVoteState()) {
            return this.showVoteInfo(node.Swih('vote')[0]);
        }
        node.Swih('alliance');
        this.showAllianceInfo(this.currChildTab);
    };
    // 显示投票信息===================================================================================================================
    BuildEmbassyPnlCtrl.prototype.showVoteInfo = function (node) {
        var _this = this;
        var cdtime = node.Child('cdtime');
        var creater = this.alliance.getCreater();
        var isVoteState = !creater, uid = GameHelper_1.gameHpr.getUid();
        if (isVoteState) {
            node.Child('title/desc').setLocaleKey('ui.leader_vote_desc_1');
            cdtime.Child('name').setLocaleKey('ui.vote_surplus_time_1');
            cdtime.Child('time/val', cc.LabelTimer).run(this.alliance.getVoteSurplusTime() * 0.001);
        }
        else {
            node.Child('title/desc').setLocaleKey('ui.leader_vote_desc_2', ut.nameFormator(GameHelper_1.gameHpr.getPlayerName(creater), 7));
            cdtime.Child('name').setLocaleKey('ui.vote_surplus_time_2');
            cdtime.Child('time/val', cc.LabelTimer).run(this.alliance.getConfirmCreaterSurplusTime() * 0.001);
        }
        // 成员列表
        var leadervoteMap = {}, yetVoteCountMap = {};
        this.alliance.getLeadervotes().forEach(function (m) { return leadervoteMap[m.uid] = m.list; });
        var members = this.alliance.getMembers().map(function (m) {
            var info = GameHelper_1.gameHpr.getPlayerInfo(m.uid), votes = leadervoteMap[m.uid] || [], voteMap = {};
            votes.forEach(function (u) { return voteMap[u] = yetVoteCountMap[u] = true; });
            return { uid: m.uid, info: info, voteMap: voteMap, voteCount: votes.length, offlineTime: m.offlineTime };
        }).sort(function (a, b) {
            if (b.voteCount !== a.voteCount) {
                return b.voteCount - a.voteCount;
            }
            return a.offlineTime - b.offlineTime;
        });
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.Items(members, function (it, data) {
            it.Data = data;
            var info = data.info;
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val'), info.headIcon, _this.key);
            it.Child('head/name', cc.Label).string = ut.nameFormator(info.nickname || '???', 7);
            ViewHelper_1.viewHelper.updatePositionView(it.Child('head/pos_be'), info.mainCityIndex, false);
            it.Child('vote_count/val', cc.Label).string = data.voteCount + '';
            if (!isVoteState) {
                it.Child('button').Swih('');
            }
            else {
                it.Child('button').Swih(data.voteMap[uid] ? 'yetvote' : 'vote_be');
            }
            it.Color(creater === info.uid ? '#FADFA7' : '#E9DDC7');
        });
        // 参与人数
        var pers = node.Child('title/pers');
        pers.Child('cur', cc.Label).string = Object.keys(yetVoteCountMap).length + '';
        pers.Child('max', cc.Label).string = '/' + members.length;
        // 刷新按钮
        if (!!creater && creater === uid) {
            node.Child('buttons').Swih('creare_info_be');
        }
        else if (!creater) {
            node.Child('buttons').Swih('no')[0].Child('val').setLocaleKey('ui.in_vote');
        }
        else {
            node.Child('buttons').Swih('no')[0].Child('val').setLocaleKey('ui.wait_leader_confirm');
        }
    };
    // 投票
    BuildEmbassyPnlCtrl.prototype.doVote = function (uid) {
        this.alliance.leaderVote(uid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    // 显示联盟列表===================================================================================================================
    BuildEmbassyPnlCtrl.prototype.updateAllianceList = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var loading, list, sv;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        loading = node.Child('list/loading');
                        loading.active = true;
                        return [4 /*yield*/, GameHelper_1.gameHpr.world.getAlliances()];
                    case 1:
                        list = _a.sent();
                        if (!this.isValid) {
                            return [2 /*return*/];
                        }
                        loading.active = false;
                        node.Child('list/empty').active = list.length === 0;
                        sv = node.Child('list', cc.ScrollView);
                        sv.stopAutoScroll();
                        sv.content.y = 0;
                        this.yetApplyCount = 0;
                        sv.Items(list, function (it, data) {
                            it.Data = data;
                            var root = it.Child('root');
                            ResHelper_1.resHelper.loadAlliIcon(data.icon, root.Child('icon'), _this.key);
                            root.Child('name', cc.Label).string = data.name;
                            root.Child('pers/val', cc.Label).string = data.pers + '/' + data.persLimit;
                            root.Child('buttons').Swih(data.isApply ? 'cancel_apply_be' : 'apply_be');
                            if (it.Child('desc').active = !!data.applyDesc) {
                                it.Child('desc', cc.Label).string = data.applyDesc;
                            }
                            if (data.isApply) {
                                _this.yetApplyCount += 1;
                            }
                        });
                        // 刷新按钮
                        node.Child('buttons').Swih(this.data.lv >= Constant_1.CREATE_ALLI_MAX_LV ? 'creare_be' : 'no');
                        return [2 /*return*/];
                }
            });
        });
    };
    // 申请
    BuildEmbassyPnlCtrl.prototype.apply = function (it) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('build/SendAlliApply', it.Data, function (ok) {
            if (_this.isValid && ok) {
                it.Child('root/buttons').Swih('cancel_apply_be');
                _this.yetApplyCount += 1;
            }
        });
    };
    // 显示联盟信息===================================================================================================================
    BuildEmbassyPnlCtrl.prototype.showAllianceInfo = function (type) {
        if (!this.alliance.getUid()) {
            return;
        }
        this.currChildTab = type;
        var node = this.alliPagesNode_.Swih(type)[0];
        if (type === 0) {
            this.updateAlliBase(node);
        }
        else if (type === 1) {
            this.updateAlliMembers(node);
        }
        else if (type === 2) {
            this.updateBattleStat(node);
        }
        else if (type === 3) {
            this.updateLogs(node);
        }
    };
    // 联盟基础信息
    BuildEmbassyPnlCtrl.prototype.updateAlliBase = function (node) {
        node = node || this.alliPagesNode_.Child(0);
        var top = node.Child('top'), info = node.Child('info');
        var members = this.alliance.getMembers();
        var sumEmbassyLv = this.alliance.getSumEmbassyLv(), sumLandCount = 0, sumAlliScore = 0;
        var extraScore = GameHelper_1.gameHpr.getExtraScore();
        members.forEach(function (m) {
            m.landCount = GameHelper_1.gameHpr.getPlayerOweCellCount(m.uid);
            sumLandCount += m.landCount;
            sumAlliScore += (m.landScores[1] || 0) + (m.alliScores[1] || 0) + extraScore;
        });
        ResHelper_1.resHelper.loadAlliIcon(this.alliance.getIcon(), top.Child('icon'), this.key);
        top.Child('icon/name', cc.Label).string = this.alliance.getName();
        var pers = top.Child('icon/pers');
        pers.Child('cur', cc.Label).string = '' + members.length;
        pers.Child('max', cc.Label).string = '/' + this.alliance.getPersLimit();
        // 申请列表
        this.updateApplyNode();
        // 公告
        this.updateAlliNotice(node);
        // 信息
        info.Child('content/0', cc.Label).string = '' + sumEmbassyLv; //联盟等级
        info.Child('content/1', cc.Label).string = '' + sumLandCount; //总领地
        info.Child('content/2', cc.Label).string = '' + sumAlliScore; //总积分
        // 联盟内政
        this.updateAlliPolicySlot(node);
    };
    BuildEmbassyPnlCtrl.prototype.updateApplyNode = function () {
        if (this.applysNode_.active = !GameHelper_1.gameHpr.world.isKarmicMahjong() && this.alliance.getMemberJob() <= Enums_1.AllianceJobType.CREATER_VICE) {
            this.applysNode_.Child('dot').active = !!this.alliance.getApplys().length;
        }
    };
    BuildEmbassyPnlCtrl.prototype.updateAlliNotice = function (node) {
        var _this = this;
        node = node || this.alliPagesNode_.Child(0);
        var noticeText = this.alliance.getNotice(), isMeCreater = this.alliance.isMeCreater();
        var empty = this.noticeNode_.Child('empty'), edit = this.noticeNode_.Child('edit_notice_be');
        edit.active = isMeCreater;
        empty.active = !isMeCreater && !noticeText;
        this.noticeNode_.Child('text', cc.Label).string = noticeText;
        if (isMeCreater) {
            edit.y = noticeText ? this.noticeNode_.Data : empty.y;
            var cd = this.alliance.getEditNoticeCd(), hasCd = !!cd;
            edit.Component(cc.Button).interactable = !hasCd;
            edit.opacity = hasCd ? 150 : 255;
            if (edit.Child('cd').active = hasCd) {
                edit.Child('desc').setLocaleKey('ui.edit_alli_notice_again');
                edit.Child('cd', cc.LabelTimer).run(cd * 0.001, function () { return _this.isValid && _this.updateAlliNotice(node); });
            }
            else if (!noticeText) {
                edit.Child('desc').setLocaleKey('ui.edit_alli_notice');
            }
            else {
                edit.Child('desc').setLocaleKey('ui.edit_alli_notice');
            }
        }
    };
    // 刷新联盟政策
    BuildEmbassyPnlCtrl.prototype.updateAlliPolicySlot = function (node) {
        var _this = this;
        node = node || this.alliPagesNode_.Child(0);
        var policys = this.alliance.getPolicys(), sumLv = this.alliance.getSumEmbassyLv();
        var conf = GameHelper_1.gameHpr.getAlliPolicySlotConf(), keys = Object.keys(conf);
        node.Child('policy_nbe').Items(keys, function (it, k) {
            var lv = conf[k], i = Number(k);
            var data = policys[i], isActive = sumLv >= lv, isSelect = !!(data === null || data === void 0 ? void 0 : data.isYetStudy());
            if (!isActive && !isSelect) {
                it.Data = null;
                it.Child('icon').Swih('lock');
                it.Child('name').setLocaleKey('');
                return it.Child('lock').Color('#D7634D').setLocaleKey('ui.need_lv_unlock', lv);
            }
            it.Data = { index: i, info: data };
            it.Child('name').setLocaleKey(isSelect && isActive ? data.name : '');
            if (data) {
                var icon = it.Child('icon').Swih('val')[0].Component(cc.Sprite);
                ResHelper_1.resHelper.loadPolicyIcon(data.id, icon, _this.key, false);
                icon.setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(isActive));
                it.Child('lock').Color('#D7634D').setLocaleKey(isActive ? '' : 'ui.active_nend_lv', lv);
            }
            else if (_this.alliance.getSelectPolicysByIndex(i).length > 0) {
                it.Child('icon').Swih('add');
                it.Child('lock').Color('#4AB32E').setLocaleKey('ui.can_select');
            }
            else {
                it.Child('icon').Swih('add');
                it.Child('lock').Color('#A18876').setLocaleKey('ui.wait_select');
            }
        });
    };
    // 刷新成员列表信息
    BuildEmbassyPnlCtrl.prototype.updateAlliMembers = function (node) {
        var _this = this;
        node = node || this.alliPagesNode_.Child(1);
        var members = this.alliance.getMembers().sort(function (a, b) {
            if (a.offlineTime !== b.offlineTime) {
                return a.offlineTime - b.offlineTime;
            }
            else if (a.job !== b.job) {
                return a.job - b.job;
            }
            return a.joinTime - b.joinTime;
        });
        var memberCount = members.length;
        var now = Date.now();
        var pers = node.Child('title/pers');
        pers.Child('cur', cc.Label).string = '' + memberCount;
        pers.Child('max', cc.Label).string = '/' + this.alliance.getPersLimit();
        node.Child('title/bg/online').setLocaleKey('ui.aliance_online_pers', members.filter(function (m) { return !m.offlineTime; }).length);
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.List(memberCount, function (it, i) {
            var data = it.Data = members[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, _this.key);
            it.Child('name/val', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            var jobNode = it.Child('name/job');
            if (jobNode.active = data.job < Enums_1.AllianceJobType.MEMBER) {
                jobNode.setLocaleKey('(' + assetsMgr.lang('ui.alliance_job_' + data.job) + ')');
            }
            if (data.offlineTime) {
                it.Child('online').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.offlineTime + (now - data.getTime)));
            }
            else {
                it.Child('online').Color('#4AB32E').setLocaleKey('ui.online');
            }
            // 专属装备
            it.Child('exclusives').Items(data.exclusiveEquips.slice(0, 2), function (n, info) {
                var equip = new EquipInfo_1.default().fromSvr(info);
                ResHelper_1.resHelper.loadEquipIcon(equip.id, n, _this.key, equip.getSmeltCount());
            });
        });
    };
    // 刷新战斗统计
    BuildEmbassyPnlCtrl.prototype.updateBattleStat = function (node) {
        node = node || this.alliPagesNode_.Child(2);
        this.selectDateItem(node.Child('top/date_select_be'), this.currSelectBattleDate);
    };
    BuildEmbassyPnlCtrl.prototype.initDateList = function () {
        var _this = this;
        var list = this.alliance.getCanSelectBattleDateList();
        if (list.length > 0) {
            var node = this.alliPagesNode_.Child(2).Child('top/date_select_be'), len_1 = list.length - 1;
            var sv = node.Child('mask/list', cc.ScrollView);
            this.currSelectBattleDate = list[0];
            sv.stopAutoScroll();
            sv.node.height = Math.min(list.length * 52 + 8 + 16 + 4, 308);
            sv.Items(list, function (it, data, i) {
                var select = data === _this.currSelectBattleDate;
                it.Data = data;
                it.Child('val', cc.Label).Color(select ? '#C8BBAB' : '#A18876').string = data;
                it.Child('select').active = select;
                it.Child('line').active = i < len_1;
            });
        }
    };
    // 打开关闭日期弹出框
    BuildEmbassyPnlCtrl.prototype.changeDateList = function (node, val) {
        node.Child('date_mask_be').active = val;
        var listNode = node.Child('mask/list');
        if (val) {
            node.Child('mask').active = true;
            listNode.y = listNode.height + 2;
            cc.tween(listNode).to(0.15, { y: -4 }, { easing: cc.easing.sineOut }).start();
        }
        else {
            listNode.y = -4;
            cc.tween(listNode).to(0.1, { y: listNode.height + 2 }).call(function () { return node.Child('mask').active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start();
    };
    BuildEmbassyPnlCtrl.prototype.selectDateItem = function (node, date) {
        node.Data = this.currSelectBattleDate = date;
        node.Child('val', cc.Label).string = date;
        var sv = node.Child('mask/list', cc.ScrollView);
        sv.content.children.forEach(function (m) {
            var select = m.Data === date;
            m.Child('val').Color(select ? '#C8BBAB' : '#A18876');
            m.Child('select').active = select;
        });
        this.updateBattleRecordList(date);
    };
    BuildEmbassyPnlCtrl.prototype.updateBattleRecordList = function (date) {
        return __awaiter(this, void 0, void 0, function () {
            var node, empty, loading, sv, list, uid;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        node = this.alliPagesNode_.Child(2);
                        empty = node.Child('empty'), loading = node.Child('loading'), sv = node.Child('list', cc.ScrollView);
                        empty.active = false;
                        loading.active = true;
                        sv.List(0);
                        return [4 /*yield*/, this.alliance.getBattleRecordList(date)];
                    case 1:
                        list = _a.sent();
                        loading.active = false;
                        empty.active = !list.length;
                        list.sort(function (a, b) { return b.score - a.score; });
                        uid = GameHelper_1.gameHpr.getUid();
                        sv.List(list.length, function (it, i) {
                            var data = list[i], info = GameHelper_1.gameHpr.getPlayerInfo(data.owner);
                            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), info === null || info === void 0 ? void 0 : info.headIcon, _this.key);
                            it.Color(info.uid === uid ? '#FADFA7' : '#E9DDC7');
                            it.Child('head/name', cc.Label).string = ut.nameFormator((info === null || info === void 0 ? void 0 : info.nickname) || '???', 12);
                            it.Child('head/count').setLocaleKey('ui.battles_count', data.battleCount);
                            it.Child('head/score/val', cc.Label).string = '+' + data.score;
                            it.Child('0/0').setLocaleKey('ui.alli_battle_record_0_0', data.pawnDamage);
                            it.Child('0/1').setLocaleKey('ui.alli_battle_record_0_1', data.killCount);
                            it.Child('1/0').setLocaleKey('ui.alli_battle_record_1_0', data.PawnHitDamage);
                            it.Child('1/1').setLocaleKey('ui.alli_battle_record_1_1', data.deadCount);
                            it.Child('2/0').setLocaleKey('ui.alli_battle_record_2_0', data.buildDamage);
                            it.Child('3/0').setLocaleKey('ui.alli_battle_record_3_0', data.damageToBuild);
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新日志
    BuildEmbassyPnlCtrl.prototype.updateLogs = function (node) {
        var _this = this;
        node = node || this.alliPagesNode_.Child(3);
        var sv = node.Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var loading = sv.Child('loading');
        loading.active = true;
        this.alliance.getAlliLogs().then(function (list) {
            if (!_this.isValid) {
                return;
            }
            loading.active = false;
            sv.Items(list, function (it, data) {
                it.Child('time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                it.Child('content').setLocaleKey('ui.alli_logs_' + data.type, data.params);
            });
        });
    };
    __decorate([
        ut.syncLock
    ], BuildEmbassyPnlCtrl.prototype, "updateAllianceList", null);
    BuildEmbassyPnlCtrl = __decorate([
        ccclass
    ], BuildEmbassyPnlCtrl);
    return BuildEmbassyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildEmbassyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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