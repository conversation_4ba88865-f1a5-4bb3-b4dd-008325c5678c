
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaSelectEmojiPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e0ad83uTNNFxZlXegpL54tL', 'AreaSelectEmojiPnlCtrl');
// app/script/view/area/AreaSelectEmojiPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var AreaSelectEmojiPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaSelectEmojiPnlCtrl, _super);
    function AreaSelectEmojiPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.EMOJI_SIZE = cc.size(64, 64);
        _this.user = null;
        _this.cb = null;
        return _this;
    }
    AreaSelectEmojiPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AreaSelectEmojiPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    AreaSelectEmojiPnlCtrl.prototype.onEnter = function (cb) {
        var _this = this;
        this.cb = cb;
        var ids = this.getCanUseEmojis();
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Child('empty').active = ids.length === 0;
        this.listSv_.List(ids.length, function (it, i) {
            var id = it.Data = ids[i];
            var val = it.Child('val');
            ResHelper_1.resHelper.loadEmojiIcon(id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE, undefined, 1.1); });
        });
    };
    AreaSelectEmojiPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    AreaSelectEmojiPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    AreaSelectEmojiPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        this.cb && this.cb(event.target.Data);
        this.cb = null;
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AreaSelectEmojiPnlCtrl.prototype.getCanUseEmojis = function () {
        var datas = [];
        assetsMgr.getJson('chatEmoji').datas.forEach(function (m) { return (m.type === 2 && m.cond === 0) && datas.push(m.id); });
        datas.pushArr(this.user.getUnlockChatEmojiIds());
        return datas;
    };
    AreaSelectEmojiPnlCtrl = __decorate([
        ccclass
    ], AreaSelectEmojiPnlCtrl);
    return AreaSelectEmojiPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaSelectEmojiPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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