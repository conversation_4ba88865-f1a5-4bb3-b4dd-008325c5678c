
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/CityBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9d86es6P1FCAouP16H8utBZ', 'CityBuildCmpt');
// app/script/view/area/CityBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 城市建筑
var CityBuildCmpt = /** @class */ (function (_super) {
    __extends(CityBuildCmpt, _super);
    function CityBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.touchCmpt = null;
        return _this;
    }
    CityBuildCmpt.prototype.init = function (data, origin, owner) {
        _super.prototype.init.call(this, data, origin, owner);
        this.touchCmpt = this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this);
        this.syncPoint();
        this.syncZindex();
        // 要塞
        if (this.data.id === Constant_1.CITY_FORT_NID) {
            this.Child('body/val', cc.Animation).play('city_2102_' + this.getOwnType());
        }
        return this;
    };
    // 重新同步
    CityBuildCmpt.prototype.resync = function (data) {
        this.data = data;
        this.syncPoint();
        this.syncZindex();
        this.setCanClick(true);
        return this;
    };
    CityBuildCmpt.prototype.clean = function () {
        var _a;
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        this.node.destroy();
        this.data = null;
    };
    CityBuildCmpt.prototype.onClick = function () {
        audioMgr.playSFX('click');
        this.data && ViewHelper_1.viewHelper.showPnl(this.data.getUIUrl(), this.data);
    };
    // 设置是否可以点击
    CityBuildCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 获取归属类型 1.自己 2.盟友 3.敌方
    CityBuildCmpt.prototype.getOwnType = function () {
        if (this.owner === GameHelper_1.gameHpr.getUid()) {
            return 1;
        }
        else if (GameHelper_1.gameHpr.isOneAlliance(this.owner)) {
            return 2;
        }
        return 3;
    };
    CityBuildCmpt = __decorate([
        ccclass
    ], CityBuildCmpt);
    return CityBuildCmpt;
}(BaseBuildCmpt_1.default));
exports.default = CityBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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