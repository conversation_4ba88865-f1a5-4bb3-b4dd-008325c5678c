{"version": 3, "sources": ["assets\\app\\script\\common\\helper\\PayHelper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAuE;AACvE,2CAAyC;AACzC,2CAA2E;AAC3E,6DAAsE;AACtE,gDAA0C;AAC1C,8CAAwC;AACxC,yDAAuD;AACvD,yDAAuD;AACvD,2CAAsC;AACtC,yCAAuC;AACvC,2CAAyC;AAEzC,IAAM,sBAAsB,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAA;AAC1D,IAAM,qBAAqB,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,qBAAqB,CAAC,CAAA;AAEzG,sBAAsB;AACtB,SAAS,QAAQ,CAAC,MAAwB,EAAE,YAAqB,EAAE,kBAAuC;IACtG,IAAI,GAAG,GAAG,YAAU,YAAc,CAAA;IAClC,IAAI,MAAM,IAAI,CAAC,YAAY,EAAE;QACzB,GAAG,GAAG,MAAgB,CAAA;KACzB;IACD,IAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAA;IACvC,kBAAkB,CAAC,KAAK,GAAG;QAAgB,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;;;;;;wBACrD,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;4BACX,sBAAO,IAAI,EAAA;yBACd;wBACD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;wBACD,qBAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAA;;wBAAvC,MAAM,GAAG,SAA8B;wBAC7C,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;wBACjB,sBAAO,MAAM,EAAA;;;;KAChB,CAAA;IACD,OAAO,kBAAkB,CAAA;AAC7B,CAAC;AAED;;GAEG;AACH;IAAA;QAEY,WAAM,GAAY,KAAK,CAAA;QACvB,cAAS,GAAY,KAAK,CAAA;QAC1B,eAAU,GAAY,KAAK,CAAA;QAC3B,YAAO,GAAY,KAAK,CAAA;QACxB,WAAM,GAAQ,IAAI,CAAA;QAElB,iBAAY,GAAU,IAAI,CAAA;IAukBtC,CAAC;IArkBgB,wBAAI,GAAjB;;;;;wBACI,IAAI,IAAI,CAAC,MAAM,EAAE;4BACb,sBAAM;yBACT;wBACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;wBAClB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;wBACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;wBACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;wBAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;wBACxB,qBAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAA;;wBAArB,SAAqB,CAAA;wBACrB,qBAAM,IAAI,CAAC,iBAAiB,EAAE,EAAA;;wBAA9B,SAA8B,CAAA;wBAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;;;;;KACxB;IAED,SAAS;IACI,2BAAO,GAApB,UAAqB,KAAa;;;;;;;wBAC9B,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE;4BAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;4BACtB,sBAAM;yBACT;6BAAM,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;4BAC1D,sBAAM;yBACT;wBACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;;;6BACZ,CAAA,KAAK,GAAG,CAAC,CAAA;wBACZ,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;wBAChC,KAAK,EAAE,CAAA;wBACD,UAAU,GAAa,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAZ,CAAY,CAAC,CAAA;wBACvF,UAAU,CAAC,IAAI,CAAC,+BAAoB,CAAC,CAAA;wBACrC,UAAU,CAAC,OAAO,CAAC,qBAAU,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,GAAG,IAAK,OAAA,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAzB,CAAyB,EAAE,EAAE,CAAC,CAAC,CAAA;wBAC5E,IAAI,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;wBAC/G,qBAAM,qBAAS,CAAC,IAAI,CAAC,kBAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAA;;wBAAvE,KAA0B,SAA6C,EAArE,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,IAAI,UAAA;wBAC3B,IAAI,KAAK,KAAK,YAAY,EAAE;4BACxB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;4BAC3C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;4BACpB,sBAAM,CAAC,MAAM;yBAChB;6BAAM,IAAI,CAAC,KAAK,EAAE;4BACX,WAAW,GAAU,EAAE,CAAA;4BAC3B,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gCACR,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gCAC1C,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oCAClC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;oCAEnB,KAAA,OAAyF,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,CAAC,uBAAuB;oCAAxB,EAAtG,SAAS,QAAA,EAAE,KAAK,QAAA,EAAE,YAAY,QAAA,EAAE,cAAc,QAAA,EAAE,kBAAkB,QAAA,EAAE,gBAAgB,QAAA,CAAkB;oCAC7G,IAAI,kBAAkB,EAAE;wCACpB,WAAW,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,OAAA,EAAE,YAAY,cAAA,EAAE,cAAc,gBAAA,EAAE,kBAAkB,oBAAA,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAA;qCAC7G;yCAAM;wCACH,WAAW,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,OAAA,EAAE,YAAY,cAAA,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAA;qCACvE;iCACJ;6BACJ;iCAAM,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gCACvB,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,CAAA;6BAC1C;4BACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;;gCAChB,KAAoB,+BAAA,SAAA,WAAW,CAAA,CAAA,yGAAE;oCAAxB,OAAO;oCACR,GAAG,GAAG,OAAO,CAAC,SAAS,CAAA;oCAC3B,IAAI,OAAO,CAAC,OAAO,EAAE;wCACjB,GAAG,GAAG,OAAO,CAAC,OAAO,CAAA;qCACxB;yCAAM,IAAI,OAAO,CAAC,MAAM,EAAE;wCACvB,GAAG,GAAG,OAAO,CAAC,MAAM,CAAA;qCACvB;oCACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAA;iCAC7B;;;;;;;;;4BACD,4BAA4B;4BAC5B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA,mBAAmB,CAAC,CAAA;4BAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;4BACtB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;4BACpB,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,eAAe,CAAC,CAAA;4BAC3C,sBAAM;yBACT;6BAAM;4BACH,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;yBACxC;6BACG,CAAA,KAAK,GAAG,CAAC,CAAA,EAAT,wBAAS;wBACT,qBAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAA;;wBAAhB,SAAgB,CAAA;;;;wBAGxB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;;;;;KACvB;IAED,WAAW;IACE,qCAAiB,GAA9B;;;;;;;wBACI,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;4BAChB,sBAAM;yBACT;wBACyB,qBAAM,qBAAS,CAAC,gBAAgB,CAAC,kBAAQ,CAAC,mBAAmB,CAAC,EAAA;;wBAAlF,KAAoB,SAA8D,EAAhF,KAAK,WAAA,EAAE,MAAM,YAAA;6BACjB,CAAC,KAAK,EAAN,wBAAM;wBACA,aAAW,IAAI,CAAC,WAAW,EAAE,CAAA;wBAC7B,mBAAiB,EAAE,EAAE,eAAa,EAAE,CAAA;wBACpC,kBAAgB,EAAE,CAAA;wBACxB,IAAI,UAAQ,KAAK,uBAAe,CAAC,MAAM,EAAE;4BACrC,sBAAsB,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAa,CAAC,CAAC,CAAC,GAAG,IAAI,EAAvB,CAAuB,CAAC,CAAA;yBAC/D;6BAAM,IAAI,UAAQ,KAAK,uBAAe,CAAC,KAAK,EAAE;4BAC3C,qBAAqB,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAa,CAAC,CAAC,CAAC,GAAG,IAAI,EAAvB,CAAuB,CAAC,CAAA;yBAC9D;wBACD,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,gBAAc,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,IAAI,EAAnC,CAAmC,CAAC,CAAA;wBACrF,gBAAc,CAAC,+BAAoB,CAAC,GAAG,IAAI,CAAA,CAAC,OAAO;wBACnD,oBAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,UAAQ,EAAE,CAAC,CAAC,EAAzB,CAAyB,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC;4BACpF,IAAI,EAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,OAAO,CAAA,EAAE;gCACb,OAAO,KAAK,CAAA;6BACf;iCAAM,IAAI,CAAC,gBAAc,CAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,SAAS,CAAC,EAAE;gCACtC,IAAI,eAAa,CAAC,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,SAAS,CAAC,EAAE;oCAC7B,YAAU,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAA;iCAClC;gCACD,OAAO,KAAK,CAAA;6BACf;4BACD,OAAO,IAAI,CAAA;wBACf,CAAC,CAAC,CAAC,CAAA;wBACH,IAAI,CAAC,8BAA8B,CAAC,YAAU,CAAC,CAAA;wBAEpC,qBAAM,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAA;;wBAAlE,EAAE,GAAG,SAA6D;wBACxE,IAAI,EAAE,EAAE;4BACJ,uBAAU,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAA;yBAC/C;;;wBAED,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;;;;;;KAExD;IAGY,gCAAY,GAAzB;;;;;6BACQ,CAAC,IAAI,CAAC,UAAU,EAAhB,wBAAgB;wBAChB,qBAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAA;;wBAArB,SAAqB,CAAA;;;;;;KAE5B;IAEM,yBAAK,GAAZ;QACI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAC5B,CAAC;IAEM,gCAAY,GAAnB;QACI,OAAO,IAAI,CAAC,UAAU,CAAA;IAC1B,CAAC;IAEM,iCAAa,GAApB;QACI,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAA;IACxB,CAAC;IAED,eAAe;IACD,kDAA8B,GAA5C,UAA6C,UAAe;;;;;;wBAClD,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;wBAClB,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAA;;wBAAvF,KAAgB,SAAuE,EAArF,GAAG,SAAA,EAAE,IAAI,UAAA;wBACX,IAAI,GAAa,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,EAAE,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;wBACtD,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM;;;6BAAE,CAAA,CAAC,GAAG,CAAC,CAAA;wBAC5B,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;wBAClB,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAA;;wBAArE,SAAqE,CAAA;wBACrE,qCAAiB,CAAC,WAAW,CAAC,gCAAgC,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;;;wBAHjD,CAAC,EAAE,CAAA;;4BAK3C,sBAAO,IAAI,EAAA;;;;KACd;IAED,SAAS;IACI,oCAAgB,GAA7B;;;;;;;6BACQ,CAAC,IAAI,CAAC,YAAY,EAAlB,wBAAkB;wBACQ,qBAAM,qBAAS,CAAC,gBAAgB,CAAC,kBAAQ,CAAC,YAAY,CAAC,EAAA;;wBAA3E,KAAoB,SAAuD,EAAzE,KAAK,WAAA,EAAE,MAAM,YAAA;6BACjB,CAAC,KAAK,EAAN,wBAAM;wBACA,aAAW,IAAI,CAAC,WAAW,EAAE,CAAA;wBAC7B,mBAAiB,EAAE,EAAE,QAAQ,GAAG,EAAE,CAAA;wBACxC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,gBAAc,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,IAAI,EAAnC,CAAmC,CAAC,CAAA;wBAC/E,kBAAgB,EAAE,CAAA;wBACxB,IAAI,UAAQ,KAAK,uBAAe,CAAC,MAAM,EAAE;4BACrC,sBAAsB,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAa,CAAC,CAAC,CAAC,GAAG,IAAI,EAAvB,CAAuB,CAAC,CAAA;yBAC/D;6BAAM,IAAI,UAAQ,KAAK,uBAAe,CAAC,KAAK,EAAE;4BAC3C,qBAAqB,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,eAAa,CAAC,CAAC,CAAC,GAAG,IAAI,EAAvB,CAAuB,CAAC,CAAA;yBAC9D;wBACD,IAAI,CAAC,YAAY,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,OAAO,CAAC,UAAQ,EAAE,CAAC,CAAC,EAAzB,CAAyB,EAAE,MAAM,CAAC,UAAA,CAAC;4BACpE,IAAI,CAAC,CAAC,EAAE;gCACJ,OAAO,KAAK,CAAA;6BACf;iCAAM,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;gCACnB,OAAO,KAAK,CAAA;6BACf;iCAAM,IAAI,gBAAc,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,eAAa,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;gCACnE,OAAO,KAAK,CAAA;6BACf;4BACD,OAAO,IAAI,CAAA;wBACf,CAAC,MAAK,EAAE,CAAA;6BACJ,CAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAA,EAA5B,wBAA4B;wBACtB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;wBAC9F,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,YAAA,EAAE,CAAC,EAAA;;wBAAtG,KAAgB,SAAsF,EAApG,GAAG,SAAA,EAAE,IAAI,UAAA;wBACX,IAAI,GAAa,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,KAAI,EAAE,EAAE,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;wBAC/D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;4BACX,gBAAc,EAAE,CAAA;4BACtB,IAAI,CAAC,OAAO,CAAC,UAAA,EAAE,IAAI,OAAA,aAAW,CAAC,EAAE,CAAC,GAAG,IAAI,EAAtB,CAAsB,CAAC,CAAA;4BAC1C,qCAAiB,CAAC,WAAW,CAAC,sBAAsB,EAAE,EAAE,GAAG,KAAA,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAA;4BAC9E,QAAQ;4BACR,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAW,CAAC,CAAC,CAAC,OAAO,CAAC,EAAxB,CAAwB,CAAC,CAAA;yBAC1D;wBACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,EAA/B,CAA+B,CAAC,CAAA;;;;wBAGrE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;;4BAK9B,sBAAO,IAAI,CAAC,YAAY,EAAA;;;;KAC3B;IAEM,qCAAiB,GAAxB;QACI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;IAC5B,CAAC;IAED,aAAa;IACN,mCAAe,GAAtB,UAAuB,QAAyB,EAAE,IAAY,EAAE,WAAoB;;QAChF,IAAM,UAAU,GAAG,QAAQ,KAAK,uBAAe,CAAC,IAAI,CAAA;QACpD,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACtB,IAAI,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YAC9C,IAAM,KAAK,SAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0CAAE,KAAK,CAAA;YACpC,IAAI,KAAK,EAAE;gBACP,OAAO,KAAK,CAAA;aACf;SACJ;QACD,IAAI,UAAU,EAAE;YACZ,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;SACxE;aAAM;YACH,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;SACzE;IACL,CAAC;IAED,SAAS;IACF,uCAAmB,GAA1B,UAA2B,QAAgB,EAAE,IAAY;;QACrD,IAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,IAAM,GAAG,SAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0CAAE,cAAc,CAAA;QAC3C,OAAO,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,CAAC,CAAA;IACnB,CAAC;IAED,aAAa;IACN,2CAAuB,GAA9B,UAA+B,SAAiB,EAAE,OAAa;QAC3D,IAAI,OAAO,EAAE;YACT,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACvE,OAAO,0CAAyB,CAAC,GAAG,CAAC,CAAA;SACxC;QACD,OAAO,0CAAyB,CAAC,SAAS,CAAC,CAAA;IAC/C,CAAC;IAEO,oCAAgB,GAAxB,UAAyB,QAAgB,EAAE,IAAY,EAAE,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QAC3E,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,IAAM,UAAU,GAAG,QAAQ,KAAK,uBAAe,CAAC,IAAI,CAAA;QACpD,IAAI,KAAK,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YACzB,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,cAAY,IAAM,CAAC,CAAC,CAAC,iBAAe,IAAM,CAAA;SAChE;aAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;YACnB,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,qBAAqB,CAAA;SACzH;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,WAAW;IACJ,uCAAmB,GAA1B,UAA2B,EAAU;QACjC,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACtB,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAC3B,IAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,EAAE;gBACZ,OAAO,GAAG,CAAC,KAAK,CAAA;aACnB;SACJ;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,CAAA;SACZ;QACD,IAAM,IAAI,GAAG,oBAAO,CAAC,aAAa,EAAE,CAAA;QACpC,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAA;IACjD,CAAC;IAED,SAAS;IACF,0CAAsB,GAA7B;QACI,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACtB,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,+BAAoB,CAAC,CAAA;YAC7C,IAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,EAAE;gBACZ,OAAO,GAAG,CAAC,KAAK,CAAA;aACnB;SACJ;QACD,OAAO,oBAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAA;IACjD,CAAC;IAEO,8BAAU,GAAlB,UAAmB,GAAY;QAC3B,IAAI,GAAG,EAAE;YACL,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;SAC9B;aAAM;YACH,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;SAChC;QACD,uBAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;IACnC,CAAC;IAEM,+BAAW,GAAlB;QACI,IAAI,EAAE,CAAC,YAAY,EAAE,EAAE;YACnB,OAAO,uBAAe,CAAC,EAAE,CAAA;SAC5B;aAAM,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;YACtB,OAAO,uBAAe,CAAC,EAAE,CAAA;SAC5B;aAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;YACvB,OAAO,uBAAe,CAAC,IAAI,CAAA;SAC9B;aAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;YACnB,OAAO,uBAAe,CAAC,KAAK,CAAA;SAC/B;aAAM,IAAI,oBAAO,CAAC,QAAQ,EAAE,EAAE;YAC3B,OAAO,uBAAe,CAAC,MAAM,CAAA;SAChC;QACD,OAAO,uBAAe,CAAC,MAAM,CAAA;IACjC,CAAC;IAED,OAAO;IACM,8BAAU,GAAvB,UAAwB,SAAiB;;;;;;;wBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;wBACrB,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;wBACvD,cAAc;wBACd,qBAAM,IAAI,CAAC,YAAY,EAAE;4BACzB,UAAU;0BADe;;wBADzB,cAAc;wBACd,SAAyB,CAAA;wBACzB,UAAU;wBACV,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;4BACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAA;4BACpC,sBAAO,KAAK,EAAA;yBACf;wBAEK,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;wBACnC,OAAO,CAAC,GAAG,CAAC,sCAAsC,GAAG,SAAS,GAAG,aAAa,GAAG,QAAQ,CAAC,CAAA;wBAChF,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,SAAS,WAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,EAAA;;wBAAnF,GAAG,GAAG,SAA6E;wBACvF,IAAI,GAAG,CAAC,GAAG,EAAE;4BACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;4BAC7B,sBAAO,KAAK,EAAA;yBACf;wBACD,SAAS;wBACT,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAA;wBACpB,qBAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA;;wBAA1E,KAAK,GAAG,SAAkE;wBAChF,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;4BAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAA;4BACpC,sBAAO,KAAK,EAAA;yBACf;wBACD,aAAa;wBACb,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAA;wBACxC,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE,KAAK,CAAC,EAAA;;wBAAjE,GAAG,GAAG,SAA2D,CAAA;6BAC7D,GAAG,CAAC,GAAG,EAAP,wBAAO;6BAEH,CAAA,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,cAAc,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,cAAc,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,mBAAmB,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,sBAAsB,CAAA,EAAzJ,wBAAyJ;wBACzJ,QAAQ;wBACR,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;4BACpD,OAAO;0BAD6C;;wBADpD,QAAQ;wBACR,SAAoD,CAAA;wBACpD,OAAO;wBACP,oBAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;;;wBAEhE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;wBACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;wBAC7B,sBAAO,KAAK,EAAA;;wBAEhB,gCAAgC;wBAChC,QAAQ,KAAK,uBAAe,CAAC,MAAM,IAAI,qCAAiB,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;wBAI3M,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;wBAC1E,IAAI,IAAI,EAAE;4BACN,qCAAiB,CAAC,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;yBACxE;wBACD,WAAW;wBACX,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;wBACpC,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,EAAA;;wBAApD,SAAoD,CAAA;wBACpD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;wBAC1C,iBAAiB;wBACjB,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;4BACjC,uBAAU,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;4BACtD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,sBAAO,IAAI,EAAA;yBACd;wBAEK,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAA;;wBAD3G,SAAS;wBACT,GAAG,GAAG,SAAqG,CAAA;wBAC3G,IAAI,GAAG,CAAC,GAAG,EAAE;4BACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;4BAC7B,sBAAO,KAAK,EAAA;yBACf;6BAAM,IAAI,SAAS,KAAK,+BAAoB,EAAE;4BAC3C,oBAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAA;4BACnC,qCAAiB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAA;yBACtD;6BAAM;4BACH,oBAAO,CAAC,IAAI,CAAC,QAAQ,OAAC,GAAG,CAAC,IAAI,0CAAE,KAAK,CAAC,CAAA,CAAC,MAAM;4BAC7C,oBAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA,CAAC,QAAQ;4BAC3D,QAAQ,GAAG,OAAA,GAAG,CAAC,IAAI,0CAAE,QAAQ,KAAI,CAAC,CAAA;4BACxC,IAAI,QAAQ,EAAE;gCACV,oBAAO,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;6BACjE;yBACJ;wBACD,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;wBACzC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;wBACtB,sBAAO,IAAI,EAAA;;;;KACd;IAED,OAAO;IACO,yBAAK,GAAnB,UAAoB,QAAgB,EAAE,SAAiB,EAAE,SAAiB,EAAE,IAAY,EAAE,OAAa;;;;;;wBAC/F,SAAS,GAAG,EAAE,EAAE,IAAI,GAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAA;wBACrD,IAAI,QAAQ,KAAK,uBAAe,CAAC,MAAM,EAAE;4BACrC,SAAS,GAAG,kBAAQ,CAAC,UAAU,CAAA;4BAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;4BACnD,IAAI,OAAO,EAAE,EAAE,gBAAgB;gCACrB,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gCAChE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;gCAC3B,IAAI,CAAC,KAAK,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,KAAI,EAAE,CAAA;6BAChC;yBACJ;6BAAM,IAAI,QAAQ,KAAK,uBAAe,CAAC,KAAK,EAAE;4BAC3C,SAAS,GAAG,kBAAQ,CAAC,SAAS,CAAA;4BAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;4BACrB,sDAAsD;4BACtD,IAAI,OAAO,EAAE;gCACT,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;gCAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;6BACrC;yBACJ;6BAAM;4BACH,sBAAO,EAAE,EAAA;yBACZ;wBACyB,qBAAM,qBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAA;;wBAAzD,KAAoB,SAAqC,EAAvD,KAAK,WAAA,EAAE,MAAM,YAAA;wBACrB,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;wBACvC,sBAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,IAAI,EAAE,EAAE,OAAO,CAAC,EAAA;;;;KACvD;IAEO,2BAAO,GAAf,UAAgB,QAAgB,EAAE,GAAQ,EAAE,OAAa;;QACrD,IAAM,KAAK,GAAQ,EAAE,QAAQ,UAAA,EAAE,CAAA;QAC/B,IAAI,QAAQ,KAAK,uBAAe,CAAC,MAAM,EAAE;YACrC,kCAAkC;YAC5B,IAAA,KAAA,OAAsB,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAA/D,SAAS,QAAA,EAAE,MAAM,QAA8C,CAAA;YACtE,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;YAC3B,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,aAAa,CAAA;YAC/B,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAA;YAC/B,KAAK,CAAC,SAAS,GAAG,SAAS,CAAA;YAC3B,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAA;YAC3B,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YAC7C,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAA;SACrC;aAAM,IAAI,QAAQ,KAAK,uBAAe,CAAC,KAAK,EAAE;YAC3C,iFAAiF;YACjF,yEAAyE;YACzE,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,aAAa,CAAA;YACjC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,sBAAsB,CAAA;YACxC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,EAAE,CAAA;YACxB,8BAA8B;YAC9B,8BAA8B;YAC9B,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;YACpF,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAA;SACrC;QACD,IAAI,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YAChD,KAAK,CAAC,SAAS,GAAG,EAAE,CAAA;SACvB;QACD,IAAM,GAAG,SAAG,IAAI,CAAC,MAAM,0CAAG,KAAK,CAAC,SAAS,CAAC,CAAA;QAC1C,KAAK,CAAC,KAAK,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,KAAI,EAAE,CAAA;QAC9B,KAAK;QACL,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QACxE,IAAI,KAAK,CAAC,SAAS,EAAE;YACjB,KAAK,CAAC,YAAY,GAAG,KAAK,CAAA;SAC7B;aAAM;YACH,KAAK,CAAC,YAAY,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,YAAY,KAAI,MAAM,CAAA;YAChD,IAAI,OAAO,EAAE;gBACT,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aAClF;iBAAM;gBACH,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,cAAc,CAAC,IAAI,CAAC,CAAA;aACrD;YACD,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gBAChB,KAAK,CAAC,SAAS,IAAI,OAAO,CAAA,CAAC,kBAAkB;aAChD;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;;;OAKG;IACU,mCAAe,GAA5B,UAA6B,QAAyB,EAAE,IAAY;uCAAG,OAAO;;;;;wBACtE,SAAS,GAAG,EAAE,EAAE,UAAU,GAAG,QAAQ,KAAK,uBAAe,CAAC,IAAI,CAAA;wBAClE,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;4BAChB,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAA;yBACtD;6BAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;4BACb,OAAO,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;4BACzC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,cAAc,GAAG,OAAO,CAAA;yBACrH;6BAAM;4BACH,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAA;4BACpC,sBAAO,KAAK,EAAA;yBACf;wBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;wBACrB,MAAM,CAAC,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;wBAC1D,cAAc;wBACd,qBAAM,IAAI,CAAC,YAAY,EAAE;4BACzB,UAAU;0BADe;;wBADzB,cAAc;wBACd,SAAyB,CAAA;wBACzB,UAAU;wBACV,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;4BACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAA;4BACpC,sBAAO,KAAK,EAAA;yBACf;wBAEK,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;wBACnC,MAAM,CAAC,KAAK,CAAC,6BAA6B,GAAG,SAAS,GAAG,aAAa,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI,CAAC,CAAA;wBAC3F,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,SAAS,WAAA,EAAE,QAAQ,UAAA,EAAE,IAAI,MAAA,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAA;;wBAAtG,GAAG,GAAG,SAAgG;wBAC1G,IAAI,GAAG,CAAC,GAAG,EAAE;4BACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;4BAC7B,sBAAO,KAAK,EAAA;yBACf;wBACK,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;wBACrB,SAAS;wBACT,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;wBACZ,qBAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,UAAA,EAAE,IAAI,MAAA,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,EAAA;;wBAA5I,KAAK,GAAG,SAAoI;wBAClJ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;4BAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;4BACtB,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,QAAQ,CAAC,CAAA;4BACpC,sBAAO,KAAK,EAAA;yBACf;wBACD,aAAa;wBACb,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;wBACxC,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE,KAAK,CAAC,EAAA;;wBAAjE,GAAG,GAAG,SAA2D,CAAA;6BAC7D,CAAC,GAAG,CAAC,GAAG,EAAR,wBAAQ;;;6BACD,CAAA,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,oBAAoB,IAAI,GAAG,CAAC,GAAG,KAAK,aAAK,CAAC,sBAAsB,CAAA,EAAlF,wBAAkF;wBACzF,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAA,CAAC,KAAK;;wBAAxE,SAAkE,CAAA,CAAC,KAAK;wBACxE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;wBACX,qBAAM,IAAI,CAAC,0BAA0B,EAAE,EAAA;;wBAA5C,EAAE,GAAG,SAAuC;wBAClD,IAAI,EAAE,EAAE;4BACJ,sBAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAA;yBAC9C;wBACD,sBAAO,KAAK,EAAA;;wBAEZ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;wBACtB,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;wBAC7B,sBAAO,KAAK,EAAA;;wBAEhB,qCAAiB,CAAC,mBAAmB,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;wBAC3M,WAAW;wBACX,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;wBAC5B,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAA;;wBAAlE,SAAkE,CAAA;wBAClE,uBAAU,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;wBACtD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;wBACtB,sBAAO,IAAI,EAAA;;;;KACd;IAED,SAAS;IACK,8CAA0B,GAAxC;;;gBACI,sBAAO,IAAI,OAAO,CAAU,UAAA,OAAO;wBAC/B,uBAAU,CAAC,cAAc,CAAC,aAAK,CAAC,oBAAoB,EAAE;4BAClD,EAAE,EAAE,cAAM,OAAA,OAAO,CAAC,IAAI,CAAC,EAAb,CAAa;4BACvB,MAAM,EAAE,cAAM,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;4BAC5B,SAAS,EAAE,IAAI;yBAClB,CAAC,CAAA;oBACN,CAAC,CAAC,EAAA;;;KACL;IAED,SAAS;IACI,iCAAa,GAA1B;;;;;;wBACI,0CAA0C;wBAC1C,+DAA+D;wBAC/D,IAAI;wBACJ,uBAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;wBACnB,qBAAM,IAAI,CAAC,gBAAgB,EAAE,EAAA;;wBAApC,IAAI,GAAG,SAA6B;wBACpC,oBAAoB,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;wBACzC,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;4BACnC,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;4BACjC,sBAAO,uBAAU,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAA;yBAC3D;wBACD,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAA;;;;;KAC7C;IACa,mCAAe,GAA7B,UAA8B,oBAA2B;;;;;;wBAC/C,KAAK,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAA;wBACf,qBAAM,oBAAO,CAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,EAAE;gCACvE,SAAS,EAAE,KAAK,CAAC,SAAS;gCAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;gCACtB,SAAS,EAAE,KAAK,CAAC,SAAS;gCAC1B,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gCACxB,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,YAAY,EAAE,KAAK,CAAC,YAAY;gCAChC,YAAY,EAAE,KAAK,CAAC,YAAY;gCAChC,SAAS,EAAE,KAAK,CAAC,SAAS;6BAC7B,CAAC,EAAA;;wBAVI,KAAgB,SAUpB,EAVM,GAAG,SAAA,EAAE,IAAI,UAAA;6BAWb,CAAA,GAAG,KAAK,aAAK,CAAC,oBAAoB,IAAI,GAAG,KAAK,aAAK,CAAC,sBAAsB,CAAA,EAA1E,wBAA0E;wBAC1E,oBAAoB,CAAC,KAAK,EAAE,CAAA;wBAC5B,qBAAM,qBAAS,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAA;;wBAAlE,SAAkE,CAAA;wBAClE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;4BACjC,sBAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,EAAA;yBACpD;wBACD,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBACjC,sBAAO,uBAAU,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAAA;;wBACrD,IAAI,GAAG,EAAE;4BACZ,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;4BACjC,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBACnC;;;wBACD,uBAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBACjC,uBAAU,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAA;;;;;KACxD;IA9cD;QADC,QAAQ;iDAKR;IA2cL,gBAAC;CA/kBD,AA+kBC,IAAA;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAA;AACxC,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE;IAClB,MAAM,CAAC,WAAW,CAAC,GAAG,iBAAS,CAAA;CAClC", "file": "", "sourceRoot": "/", "sourcesContent": ["import { MONTH_CARD, R<PERSON><PERSON><PERSON><PERSON>_BATTLE_PASS } from \"../constant/Constant\"\nimport { ecode } from \"../constant/ECode\"\nimport { CType, MonthlyCardType, PayPlatformType } from \"../constant/Enums\"\nimport { RECHARGE_PRICE_USD_CONFIG } from \"../constant/RechargeConfig\"\nimport EventType from \"../event/EventType\"\nimport JsbEvent from \"../event/JsbEvent\"\nimport { errorReportHelper } from \"./ErrorReportHelper\"\nimport { eventReportHelper } from \"./EventReportHelper\"\nimport { gameHpr } from \"./GameHelper\"\nimport { jsbHelper } from \"./JsbHelper\"\nimport { viewHelper } from \"./ViewHelper\"\n\nconst GOOGLE_SUB_PRODUCT_IDS = ['jwm_card', 'jwm_ad_free']\nconst APPLE_SUB_PRODUCT_IDS = ['jwmCardMonth', 'jwmCardQuarter', 'jwmAd<PERSON>reeMonth', 'jwmSuperCardQuarter']\n\n// 同步锁 同时调用的时候 只会执行第一个\nfunction syncLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {\n    let key = `__lock_${propertyName}`\n    if (target && !propertyName) {\n        key = target as string\n    }\n    const method = propertyDescriptor.value\n    propertyDescriptor.value = async function (...args: any[]) {\n        if (this[key]) {\n            return null\n        }\n        this[key] = true\n        const result = await method.apply(this, args)\n        this[key] = false\n        return result\n    }\n    return propertyDescriptor\n}\n\n/**\n * 支付相关\n */\nclass PayHelper {\n\n    private inited: boolean = false\n    private initEnded: boolean = false\n    private initFinish: boolean = false\n    private initing: boolean = false\n    private iapCfg: any = null\n\n    private restoredSubs: any[] = null\n\n    public async init() {\n        if (this.inited) {\n            return\n        }\n        this.inited = true\n        this.initFinish = false\n        this.initEnded = false\n        this.iapCfg = null\n        this.restoredSubs = null\n        await this.iapInit(1)\n        await this.initLostOrderList()\n        this.initEnded = true\n    }\n\n    // 获取商品列表\n    public async iapInit(retry: number) {\n        if (cc.sys.isBrowser) {\n            this.initFinish = true\n            return\n        } else if (this.initFinish || this.initing || !ut.isMobile()) {\n            return\n        }\n        this.initing = true\n        while (retry > 0) {\n            console.log('ipInit ...', retry)\n            retry--\n            const productIds: string[] = assetsMgr.getJson('recharge').datas.map(m => m.product_id)\n            productIds.push(RECHARGE_BATTLE_PASS)\n            productIds.pushArr(MONTH_CARD.reduce((val, cur) => val.concat(cur.RECHARGES), []))\n            const args = ut.isAndroid() ? { key: productIds, subKey: GOOGLE_SUB_PRODUCT_IDS } : { key: productIds.concat(APPLE_SUB_PRODUCT_IDS).join(',') }\n            const { error, result, subs } = await jsbHelper.call(JsbEvent.IAP_INIT, args)\n            if (error === 'noproducts') {\n                console.log('iap init error, no products!')\n                this.initing = false\n                return //没有订单\n            } else if (!error) {\n                let productList: any[] = []\n                if (ut.isIos()) {\n                    let resultList = String(result).split('|')\n                    for (let i = 0; i < resultList.length; ++i) {\n                        const res = resultList[i]\n                        // jwmAdFreeWeek,¥15,CNY,15,3,¥3\n                        const [productId, price, currency_pay, currency_price, introductory_price, introductory_pay] = res.split(',') //jwm_gold_1,HK$8,HKD,8\n                        if (introductory_price) {\n                            productList.push({ productId, price, currency_pay, currency_price, introductory_price, introductory_pay })\n                        } else {\n                            productList.push({ productId, price, currency_pay, currency_price })\n                        }\n                    }\n                } else if (ut.isAndroid()) {\n                    productList = result.concat(subs || [])\n                }\n                this.iapCfg = {}\n                for (let product of productList) {\n                    let key = product.productId\n                    if (product.offerId) {\n                        key = product.offerId\n                    } else if (product.planId) {\n                        key = product.planId\n                    }\n                    this.iapCfg[key] = product\n                }\n                // logger.print(this.iapCfg)\n                console.log('iap init end,'/* , this.iapCfg */)\n                this.initFinish = true\n                this.initing = false\n                eventCenter.emit(EventType.INIT_PAY_FINISH)\n                return\n            } else {\n                console.log('iap init error,', error)\n            }\n            if (retry > 0) {//最后一次没必要等两秒\n                await ut.wait(2)\n            }\n        }\n        this.initing = false\n    }\n\n    // 获取为完成的订单\n    public async initLostOrderList() {\n        if (!ut.isMobile()) {\n            return\n        }\n        const { error, result } = await jsbHelper.getLangOrderList(JsbEvent.GET_LOST_ORDER_LIST)\n        if (!error) {\n            const platform = this.getPlatform()\n            const goldProductMap = {}, subInfoMap = {}\n            const subProductMap = {}\n            if (platform === PayPlatformType.GOOGLE) {\n                GOOGLE_SUB_PRODUCT_IDS.forEach(m => subProductMap[m] = true)\n            } else if (platform === PayPlatformType.APPLE) {\n                APPLE_SUB_PRODUCT_IDS.forEach(m => subProductMap[m] = true)\n            }\n            assetsMgr.getJson('recharge').datas.forEach(m => goldProductMap[m.product_id] = true)\n            goldProductMap[RECHARGE_BATTLE_PASS] = true //加上战令的\n            gameHpr.user.initNativeLostOrderList(result.map(m => this.toOrder(platform, m)).filter(m => {\n                if (!m?.orderId) {\n                    return false\n                } else if (!goldProductMap[m?.productId]) {\n                    if (subProductMap[m?.productId]) {\n                        subInfoMap[m.orderId] = m.token\n                    }\n                    return false\n                }\n                return true\n            }))\n            this.directConsumeSubscriptionOrder(subInfoMap)\n            // 是否有未完成的订单\n            const ok = await gameHpr.user.checkHasNotFinishOrder(this.getPlatform())\n            if (ok) {\n                viewHelper.showPnl('main/NotFinishOrderTip')\n            }\n        } else {\n            logger.print('GET_LOST_ORDER_LIST error,', error)\n        }\n    }\n\n    @syncLock\n    public async checkPayInit() {\n        if (!this.initFinish) {\n            await this.iapInit(1)\n        }\n    }\n\n    public clean() {\n        this.inited = false\n        this.initEnded = false\n        this.initFinish = false\n        this.initing = false\n        this.iapCfg = null\n        this.cleanRestoredSubs()\n    }\n\n    public isInitFinish() {\n        return this.initFinish\n    }\n\n    public isInitSucceed() {\n        return !!this.iapCfg\n    }\n\n    // 直接消费没有消费掉的订阅\n    private async directConsumeSubscriptionOrder(subInfoMap: any) {\n        const orderIds = Object.keys(subInfoMap)\n        const { err, data } = await gameHpr.net.request('lobby/HD_SubOrderCheck', { list: orderIds })\n        const list: string[] = data?.list || [], uid = gameHpr.getUid()\n        for (let i = 0, l = list.length; i < l; i++) {\n            const id = list[i]\n            await jsbHelper.consumeOrder({ token: subInfoMap[id], type: 'subs' })\n            errorReportHelper.reportError('directConsumeSubscriptionOrder', { uid, orderId: id })\n        }\n        return list\n    }\n\n    // 获取订阅列表\n    public async getSubscriptions() {\n        if (!this.restoredSubs) {\n            const { error, result } = await jsbHelper.getLangOrderList(JsbEvent.RESTORED_SUB)\n            if (!error) {\n                const platform = this.getPlatform()\n                const goldProductMap = {}, subInfos = []\n                assetsMgr.getJson('recharge').datas.forEach(m => goldProductMap[m.product_id] = true)\n                const subProductMap = {}\n                if (platform === PayPlatformType.GOOGLE) {\n                    GOOGLE_SUB_PRODUCT_IDS.forEach(m => subProductMap[m] = true)\n                } else if (platform === PayPlatformType.APPLE) {\n                    APPLE_SUB_PRODUCT_IDS.forEach(m => subProductMap[m] = true)\n                }\n                this.restoredSubs = result?.map(m => this.toOrder(platform, m)).filter(m => {\n                    if (!m) {\n                        return false\n                    } else if (!m.orderId) {\n                        return false\n                    } else if (goldProductMap[m.productId] || !subProductMap[m.productId]) {\n                        return false\n                    }\n                    return true\n                }) || []\n                if (this.restoredSubs.length > 0) {\n                    const orders = this.restoredSubs.map(m => { return { orderId: m.orderId, productId: m.productId, token: m.token } })\n                    const { err, data } = await gameHpr.net.request('lobby/HD_CheckInvalidSubOrder', { list: orders, platform })\n                    const list: string[] = data?.list || [], uid = gameHpr.getUid()\n                    if (list.length > 0) {\n                        const deleteIdMap = {}\n                        list.forEach(id => deleteIdMap[id] = true)\n                        errorReportHelper.reportError('CheckInvalidSubOrder', { uid, orderIds: list })\n                        // 删除过期的\n                        this.restoredSubs.delete(m => !!deleteIdMap[m.orderId])\n                    }\n                    this.restoredSubs.sort((a, b) => b.purchaseTime - a.purchaseTime)\n                }\n            } else {\n                this.restoredSubs = []\n            }\n            // logger.print(this.restoredSubs)\n            // console.log(JSON.stringify(this.restoredSubs))\n        }\n        return this.restoredSubs\n    }\n\n    public cleanRestoredSubs() {\n        this.restoredSubs = null\n    }\n\n    // 获取订阅商品价格文本\n    public getSubPriceText(cardType: MonthlyCardType, type: string, isSubscripe: boolean) {\n        const isSaleCard = cardType === MonthlyCardType.SALE\n        if (this.isInitSucceed()) {\n            let id = this.getSubProductKey(cardType, type)\n            const price = this.iapCfg[id]?.price\n            if (price) {\n                return price\n            }\n        }\n        if (isSaleCard) {\n            return (type === 'month' ? isSubscripe ? '$1.99' : '$2.59' : '$4.99')\n        } else {\n            return (type === 'month' ? isSubscripe ? '$6.99' : '$8.99' : '$19.99')\n        }\n    }\n\n    // 获取订阅价格\n    public getSubCurrencyPrice(cardType: string, type: string) {\n        const id = this.getSubProductKey(cardType, type)\n        const val = this.iapCfg[id]?.currency_price\n        return val ?? 0\n    }\n\n    // 获取商品价格 USD\n    public getProductCurrencyPrice(productId: string, subInfo?: any) {\n        if (subInfo) {\n            const key = this.getSubProductKey(subInfo.cardType, subInfo.type, true)\n            return RECHARGE_PRICE_USD_CONFIG[key]\n        }\n        return RECHARGE_PRICE_USD_CONFIG[productId]\n    }\n\n    private getSubProductKey(cardType: string, type: string, force: boolean = false) {\n        let key = ''\n        const isSaleCard = cardType === MonthlyCardType.SALE\n        if (force || ut.isAndroid()) {\n            key = isSaleCard ? `jwm-card-${type}` : `jwm-ad-free-${type}`\n        } else if (ut.isIos()) {\n            key = isSaleCard ? 'jwmCard' + ut.initialUpperCase(type) : type === 'month' ? 'jwmAdFreeMonth' : 'jwmSuperCardQuarter'\n        }\n        return key\n    }\n\n    // 获取商品价格文本\n    public getProductPriceText(id: string) {\n        if (this.isInitSucceed()) {\n            const iap = this.iapCfg[id]\n            if (iap?.price) {\n                return iap.price\n            }\n        }\n        const json = assetsMgr.getJson('recharge').get('product_id', id)[0]\n        if (!json) {\n            return ''\n        }\n        const area = gameHpr.getServerArea()\n        return json['money_' + area] || json.money_en\n    }\n\n    // 获取战令价格\n    public getBattlePassPriceText() {\n        if (this.isInitSucceed()) {\n            const iap = this.iapCfg[RECHARGE_BATTLE_PASS]\n            if (iap?.price) {\n                return iap.price\n            }\n        }\n        return gameHpr.isGLobal() ? '$8.99' : '￥59.9'\n    }\n\n    private lockScreen(val: boolean) {\n        if (val) {\n            mc.lockTouch('buy_product')\n        } else {\n            mc.unlockTouch('buy_product')\n        }\n        viewHelper.showLoadingWait(val)\n    }\n\n    public getPlatform() {\n        if (ut.isWechatGame()) {\n            return PayPlatformType.WX\n        } else if (ut.isQQGame()) {\n            return PayPlatformType.QQ\n        } else if (!ut.isMobile()) {\n            return PayPlatformType.NONE\n        } else if (ut.isIos()) {\n            return PayPlatformType.APPLE\n        } else if (gameHpr.isInland()) {\n            return PayPlatformType.APP_WX\n        }\n        return PayPlatformType.GOOGLE\n    }\n\n    // 购买商品\n    public async buyProduct(productId: string) {\n        this.lockScreen(true)\n        console.log('buyProduct initFinish=' + this.initFinish)\n        // 0.检测是否初始化完成\n        await this.checkPayInit()\n        // 是否初始化成功\n        if (!this.isInitSucceed()) {\n            this.lockScreen(false)\n            viewHelper.showAlert(ecode.PAY_FAIL)\n            return false\n        }\n        // 1.先请求服务器创建订单\n        const platform = this.getPlatform()\n        console.log('buyProduct createPayOrder productId=' + productId + ', platform=' + platform)\n        let res = await gameHpr.net.request('lobby/HD_CreatePayOrder', { productId, platform })\n        if (res.err) {\n            this.lockScreen(false)\n            viewHelper.showAlert(res.err)\n            return false\n        }\n        // 2.拉起支付\n        console.log('buyProduct doPay...')\n        const order = await this.doPay(platform, productId, res.data.uid, res.data.uuid)\n        if (!order.orderId) {\n            this.lockScreen(false)\n            viewHelper.showAlert(ecode.PAY_FAIL)\n            return false\n        }\n        // 3.发送到服务器验证\n        console.log('buyProduct verifyPayOrder begin')\n        res = await gameHpr.net.request('lobby/HD_VerifyPayOrder', order)\n        if (res.err) {\n            // 已领取或已退款 重复验证订单\n            if (res.err === ecode.ORDER_FINISHED || res.err === ecode.ORDER_REFUNDED || res.err === ecode.ORDER_VERIFY_REPEAT || res.err === ecode.ORDER_VERIFY_API_ERROR) {\n                // 先标记消费\n                await jsbHelper.consumeOrder({ token: order.token })\n                // 刷新列表\n                gameHpr.user.removeNotFinishOrders('orderId', order.orderId)\n            }\n            this.lockScreen(false)\n            viewHelper.showAlert(res.err)\n            return false\n        }\n        // facebook自送收集混乱 ios有数据 安卓得手动上报\n        platform === PayPlatformType.GOOGLE && eventReportHelper.reportFacebookEvent('fb_mobile_purchase', { valueToSum: order.payAmount * order.quantity, fb_currency: order.currencyType, fb_order_id: order.orderId })\n        // firebase facebook 自动采集 无需上报\n        // eventReportHelper.reportFirebaseEvent('purchase', { value: order.payAmount * order.quantity, currency: order.currencyType, transaction_id: order.orderId, items: [{ item_id: order.productId, item_name: order.productId }] })\n        // 上报\n        const json = assetsMgr.getJson('recharge').get('product_id', productId)[0]\n        if (json) {\n            eventReportHelper.reportAppflyerEvent('purchase_ingot_' + json.ingot)\n        }\n        // 4.标记消费结束\n        console.log('buyProduct consume...')\n        await jsbHelper.consumeOrder({ token: order.token })\n        console.log('buyProduct getPayRewards...')\n        // 购买的单次订阅 不用领取奖励\n        if (productId.endsWith('card_once')) {\n            viewHelper.showAlert('toast.mail_monthly_card_reward')\n            this.lockScreen(false)\n            return true\n        }\n        // 5.领取奖励\n        res = await gameHpr.net.request('lobby/HD_GetPayRewards', { uid: order.cpOrderId, orderId: order.orderId })\n        if (res.err) {\n            this.lockScreen(false)\n            viewHelper.showAlert(res.err)\n            return false\n        } else if (productId === RECHARGE_BATTLE_PASS) {\n            gameHpr.user.setBattlePassBuyPass()\n            eventReportHelper.checkReportShopPackage(productId)\n        } else {\n            gameHpr.user.setIngot(res.data?.ingot) //设置金币\n            gameHpr.user.addRechargeCountRecord(productId, 1, false) //添加购买次数\n            const addCount = res.data?.addCount || 0\n            if (addCount) {\n                gameHpr.addGainMassage({ type: CType.INGOT, count: addCount })\n            }\n        }\n        viewHelper.showAlert('toast.buy_succeed')\n        this.lockScreen(false)\n        return true\n    }\n\n    // 拉起支付\n    private async doPay(platform: string, productId: string, cpOrderId: string, uuid: string, subInfo?: any) {\n        let eventName = '', data: any = { pay_id: productId }\n        if (platform === PayPlatformType.GOOGLE) {\n            eventName = JsbEvent.GOOGLE_PAY\n            data.cpOrderId = cpOrderId + '_' + gameHpr.getUid()\n            if (subInfo) { //cardType, type\n                const id = this.getSubProductKey(subInfo.cardType, subInfo.type, true)\n                const cfg = this.iapCfg[id]\n                data.token = cfg?.token || ''\n            }\n        } else if (platform === PayPlatformType.APPLE) {\n            eventName = JsbEvent.APPLE_PAY\n            data.cpOrderId = uuid\n            // data.cpOrderId = cpOrderId + '_' + gameHpr.getUid()\n            if (subInfo) {\n                data.nonce = subInfo.nonce\n                data.signature = subInfo.signature\n            }\n        } else {\n            return {}\n        }\n        const { error, result } = await jsbHelper.call(eventName, data)\n        logger.print('2.doPay result=', result)\n        return this.toOrder(platform, result || {}, subInfo)\n    }\n\n    private toOrder(platform: string, res: any, subInfo?: any) {\n        const order: any = { platform }\n        if (platform === PayPlatformType.GOOGLE) {\n            // order.package = res.packageName\n            const [cpOrderId, userId] = (res.obfuscatedAccountId || '').split('_')\n            order.orderId = res.orderId\n            order.token = res.purchaseToken\n            order.productId = res.productId\n            order.cpOrderId = cpOrderId\n            order.userId = userId || ''\n            order.purchaseTime = Number(res.purchaseTime)\n            order.quantity = res.quantity || 1\n        } else if (platform === PayPlatformType.APPLE) {\n            // order.package = gameHpr.isInland() ? 'inland.jwm.twgame' : 'global.jwm.twgame'\n            // const [cpOrderId, userId] = (res.applicationUsername || '').split('_')\n            order.orderId = res.transactionID\n            order.token = res.receiptCipheredPayload\n            order.productId = res.id\n            // order.cpOrderId = cpOrderId\n            // order.userId = userId || ''\n            order.purchaseTime = res.purchaseTime ? Number(res.purchaseTime) * 1000 : Date.now()\n            order.quantity = res.quantity || 1\n        }\n        if (order.cpOrderId === 'null' || !order.cpOrderId) {\n            order.cpOrderId = ''\n        }\n        const iap = this.iapCfg?.[order.productId]\n        order.price = iap?.price || ''\n        // 订阅\n        order.payAmount = this.getProductCurrencyPrice(order.productId, subInfo)\n        if (order.payAmount) {\n            order.currencyType = 'USD'\n        } else {\n            order.currencyType = iap?.currency_pay || 'none'\n            if (subInfo) {\n                order.payAmount = this.getSubCurrencyPrice(subInfo.cardType, subInfo.type) || 0\n            } else {\n                order.payAmount = Number(iap?.currency_price) || 0\n            }\n            if (ut.isAndroid()) {\n                order.payAmount /= 1000000 //google需要除1000000\n            }\n        }\n        return order\n    }\n\n    /**\n     * 购买订阅 分特惠月卡及超级月卡两种，每种月卡又分了月度订阅和季度订阅\n     * @param cardType 月卡类型：sub_month_card:特惠月卡 sub_super_month_card:超级月卡\n     * @param type 订阅类型：month:月度 quarter:季度\n     * @returns \n     */\n    public async buySubscription(cardType: MonthlyCardType, type: string): Promise<boolean> {\n        let productId = '', isSaleCard = cardType === MonthlyCardType.SALE\n        if (ut.isAndroid()) {\n            productId = isSaleCard ? 'jwm_card' : 'jwm_ad_free'\n        } else if (ut.isIos()) {\n            const typeStr = ut.initialUpperCase(type)\n            productId = isSaleCard ? 'jwmCard' + typeStr : type === 'month' ? 'jwmAdFree' + typeStr : 'jwmSuperCard' + typeStr\n        } else {\n            viewHelper.showAlert(ecode.PAY_FAIL)\n            return false\n        }\n        this.lockScreen(true)\n        logger.print('0.buyProduct initFinish=' + this.initFinish)\n        // 0.检测是否初始化完成\n        await this.checkPayInit()\n        // 是否初始化成功\n        if (!this.isInitSucceed()) {\n            this.lockScreen(false)\n            viewHelper.showAlert(ecode.PAY_FAIL)\n            return false\n        }\n        // 1.先请求服务器创建订单\n        const platform = this.getPlatform()\n        logger.print('1.createSubOrder productId=' + productId + ', platform=' + platform + ', type=' + type)\n        let res = await gameHpr.net.request('lobby/HD_CreateSubOrder', { productId, platform, type, offerId: '' })\n        if (res.err) {\n            this.lockScreen(false)\n            viewHelper.showAlert(res.err)\n            return false\n        }\n        const data = res.data\n        // 2.拉起支付\n        logger.print('2.doPay...')\n        const order = await this.doPay(platform, productId, data.uid, data.uuid, { cardType, type, nonce: data.nounce || '', signature: data.sign || '' })\n        if (!order.orderId) {\n            this.lockScreen(false)\n            viewHelper.showAlert(ecode.PAY_FAIL)\n            return false\n        }\n        // 3.发送到服务器验证\n        logger.print('3.verifySubOrder order=', order)\n        res = await gameHpr.net.request('lobby/HD_VerifySubOrder', order)\n        if (!res.err) {\n        } else if (res.err === ecode.SUBSCRIPTION_TIMEOUT || res.err === ecode.ORDER_VERIFY_API_ERROR) { //过期了\n            await jsbHelper.consumeOrder({ token: order.token, type: 'subs' }) //消费掉\n            this.lockScreen(false)\n            const ok = await this.showSubscriptionTimeoutTip()\n            if (ok) {\n                return this.buySubscription(cardType, type)\n            }\n            return false\n        } else {\n            this.lockScreen(false)\n            viewHelper.showAlert(res.err)\n            return false\n        }\n        eventReportHelper.reportFacebookEvent('Subscribe', { fb_order_id: order.cpOrderId == '' ? order.orderId : order.cpOrderId, fb_currency: order.currencyType, valueToSum: order.payAmount * order.quantity })\n        // 4.标记消费结束\n        logger.print('4.consume...')\n        await jsbHelper.consumeOrder({ token: order.token, type: 'subs' })\n        viewHelper.showAlert('toast.mail_monthly_card_reward')\n        this.lockScreen(false)\n        return true\n    }\n\n    // 显示过期提示\n    private async showSubscriptionTimeoutTip() {\n        return new Promise<boolean>(resolve => {\n            viewHelper.showMessageBox(ecode.SUBSCRIPTION_TIMEOUT, {\n                ok: () => resolve(true),\n                cancel: () => resolve(false),\n                lockClose: true,\n            })\n        })\n    }\n\n    // 恢复购买订阅\n    public async restoreBuySub() {\n        // if (gameHpr.user.isHasSubscription()) {\n        //     return viewHelper.showAlert('toast.not_can_restore_sub')\n        // }\n        viewHelper.showLoadingWait(true)\n        const list = await this.getSubscriptions()\n        const restoreSubscriptions = list.slice()\n        if (restoreSubscriptions.length === 0) {\n            viewHelper.showLoadingWait(false)\n            return viewHelper.showAlert('toast.not_can_restore_sub')\n        }\n        this.doRestoreBuySub(restoreSubscriptions)\n    }\n    private async doRestoreBuySub(restoreSubscriptions: any[]) {\n        const order = restoreSubscriptions[0]\n        const { err, data } = await gameHpr.net.request('lobby/HD_VerifySubOrder', {\n            productId: order.productId,\n            orderId: order.orderId,\n            cpOrderId: order.cpOrderId,\n            token: order.token,\n            platform: order.platform,\n            price: order.price,\n            purchaseTime: order.purchaseTime,\n            currencyType: order.currencyType,\n            payAmount: order.payAmount,\n        })\n        if (err === ecode.SUBSCRIPTION_TIMEOUT || err === ecode.ORDER_VERIFY_API_ERROR) { //过期了\n            restoreSubscriptions.shift()\n            await jsbHelper.consumeOrder({ token: order.token, type: 'subs' })\n            if (restoreSubscriptions.length > 0) {\n                return this.doRestoreBuySub(restoreSubscriptions)\n            }\n            viewHelper.showLoadingWait(false)\n            return viewHelper.showAlert('toast.not_can_restore_sub')\n        } else if (err) {\n            viewHelper.showLoadingWait(false)\n            return viewHelper.showAlert(err)\n        }\n        viewHelper.showLoadingWait(false)\n        viewHelper.showAlert('toast.restore_buy_sub_succeed')\n    }\n}\n\nexport const payHelper = new PayHelper()\nif (cc.sys.isBrowser) {\n    window['payHelper'] = payHelper\n}"]}