
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/BaseMapModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc4c9l5zQlGhKT8wkKfkbU/', 'BaseMapModel');
// app/script/model/snailisle/BaseMapModel.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var MapHelper_1 = require("../../common/helper/MapHelper");
var MapSceneHelper_1 = require("./MapSceneHelper");
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
// 地图模型数据
var BaseMapModel = /** @class */ (function () {
    function BaseMapModel(uid) {
        this.uid = '';
        this.mapOrigin = cc.v2(); //地图原点
        this.gridSize = cc.size(0, 0); //格子大小
        this.mapSize = cc.size(0, 0); //实际大小
        this.mapPoints = []; //地图所有点
        this.canMovePoints = []; //地图所有点
        this.maxGridSize = 0;
        this.zIndexHeight = 0; //用于角色层级zindex
        this.active = false; //是否激活
        this.walls = {}; //障碍列表
        this.positions = {}; //像素位置
        this.roles = []; //场景中的所有角色
        this.canSaveRoles = []; //记录可以保存的客人列表
        this.removes = []; //需要删除的玩家列表
        this.buildListInfo = {}; //记录建造列表的位置信息
        this.groundPoints = []; //临时地面点
        this.openFurnInfoFrom = '';
        this.tempVec = cc.v2();
        this.uid = uid;
    }
    BaseMapModel.prototype.init = function (sceneType) {
        this.sceneType = sceneType;
        this.initJson(assetsMgr.getJsonData('_map_scene_grid', sceneType));
    };
    // 配置信息
    BaseMapModel.prototype.initJson = function (json) {
        var _this = this;
        var _a, _b, _c;
        this.mapType = json.type;
        this.mapOrigin.set2(json.origin.x, json.origin.y);
        this.gridSize.width = json.w;
        this.gridSize.height = json.h;
        this.mapSize = MapSceneHelper_1.mapSceneHelper.convertMapSize(this.mapType, this.gridSize);
        this.maxGridSize = this.gridSize.width + this.gridSize.height;
        this.zIndexHeight = Math.floor(this.mapSize.height) + this.mapOrigin.y;
        this.mapPoints.length = 0;
        for (var x = 0; x < this.gridSize.width; x++) {
            for (var y = 0; y < this.gridSize.height; y++) {
                this.mapPoints.push(cc.v2(x, y));
            }
        }
        // 墙
        (_a = json.walls) === null || _a === void 0 ? void 0 : _a.forEach(function (m) { return _this.walls[m.x + '_' + m.y] = true; });
        (_b = json.obstacle) === null || _b === void 0 ? void 0 : _b.forEach(function (m) { return MapHelper_1.mapHelper.genPointsBySize(ut.stringToVec2(m.size, 'x'), ut.stringToVec2(m.point)).forEach(function (p) { return _this.walls[p.x + '_' + p.y] = true; }); });
        // 代替点
        (_c = json.positions) === null || _c === void 0 ? void 0 : _c.forEach(function (m) {
            var position = cc.v2(m);
            _this.positions[_this.getActPointByPixel(position).Join('_')] = position;
        });
        //
        this.canMovePoints = this.mapPoints.filter(function (m) { return !_this.walls[m.x + '_' + m.y]; });
    };
    // 标志性id 如果是客房会 返回房号
    BaseMapModel.prototype.getId = function () { return this.sceneType; };
    // 获取场景名
    BaseMapModel.prototype.getSceneKey = function () { return Enums_1.MapSceneType[this.sceneType].toLowerCase(); };
    BaseMapModel.prototype.emit = function (type) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        eventCenter.emit.apply(eventCenter, __spread([type], params));
    };
    // 激活
    BaseMapModel.prototype.setActive = function (val) {
        this.active = val;
        this.roles.forEach(function (m) { return m.setActive(val); });
    };
    BaseMapModel.prototype.getRoles = function () {
        var _this = this;
        return this.roles.filter(function (m) { return !_this.removes.has(m.uid); });
    };
    BaseMapModel.prototype.addRole = function (role) {
        if (!this.hasRole(role.uid)) {
            this.roles.push(role);
        }
        else {
            cc.error(Enums_1.MapSceneType[this.sceneType] + ' 中已经有该角色了 uid=' + role.uid);
        }
    };
    BaseMapModel.prototype.removeRole = function (role) {
        if (this.removes.indexOf(role.uid) === -1) {
            this.removes.push(role.uid);
        }
    };
    BaseMapModel.prototype.getBuildItems = function (jsonName) {
        var _this = this;
        var items = {};
        assetsMgr.getJson(jsonName + 'Unlock').datas.forEach(function (m) {
            var list = items[m.type];
            if (!list) {
                list = items[m.type] = [];
            }
            list.push({ id: m.id, json: m, build: _this.getBuildById(m.id) || _this.createTempBuild(m.id) });
        });
        return items;
    };
    BaseMapModel.prototype.getBuildById = function (id) { return null; };
    BaseMapModel.prototype.createTempBuild = function (id) { return null; };
    BaseMapModel.prototype.update = function (dt) {
        // 遍历每个角色 模拟他们的行动
        for (var i = this.roles.length - 1; i >= 0; i--) {
            var role = this.roles[i];
            if (this.removes.remove(role.uid) || !role.isValid) {
                this.roles.splice(i, 1);
            }
            else {
                role.update(dt);
            }
        }
    };
    // 根据网格点获取像素位置
    BaseMapModel.prototype.getMovePositionByPoint = function (point) {
        return this.positions[point.Join('_')];
    };
    // 根据网格点获取像素点
    BaseMapModel.prototype.getActPixelByPoint = function (point, out) {
        return MapSceneHelper_1.mapSceneHelper.getPixelByPoint(point, this.mapType, out).addSelf(this.mapOrigin);
    };
    // 根据像素点获取网格点
    BaseMapModel.prototype.getActPointByPixel = function (pos, out) {
        return MapSceneHelper_1.mapSceneHelper.getPointByPixel(pos.sub(this.mapOrigin, this.tempVec), this.mapType, out);
    };
    // 设置zindex
    BaseMapModel.prototype.setRoleZindex = function (role, force) {
        if (!this.active || role.noUpdateZindex) {
            return;
        }
        else if (this.mapType === Enums_1.MapType.RECT) {
            role.zIndex = Math.floor(Constant_1.MAX_ZINDEX - role.position.y);
        }
        else if (this.mapType === Enums_1.MapType.SKEW) {
            var point = this.getActPointByPixel(role.position);
            if (role.checkZindexPoint) { //有这个代表是动态改变
                if (!point.equals(role.checkZindexPoint) || force) {
                    role.checkZindexPoint.set(point);
                    this.updateRoleZindex(role, point);
                }
            }
            else {
                this.updateRoleZindex(role, point); //否则直接刷新
            }
            this.clampRoleZindex(role);
        }
    };
    // 根据各个地图 刷新
    BaseMapModel.prototype.updateRoleZindex = function (role, point) { };
    // 再次修正 最后一位
    BaseMapModel.prototype.clampRoleZindex = function (role) {
        var y = MapSceneHelper_1.mapSceneHelper.getActPointByPixel(role.position.sub(this.mapOrigin, this.tempVec), this.mapType).y;
        if (role.checkZindex < 0) {
            role.zIndex = role.checkZindex - Math.floor(y % 1 * 10);
        }
        else {
            role.zIndex = role.checkZindex + (10 - Math.floor(y % 1 * 10));
        }
    };
    // 是否有这个角色
    BaseMapModel.prototype.hasRole = function (uid) {
        return this.removes.indexOf(uid) === -1 && this.roles.some(function (m) { return m && m.uid === uid; });
    };
    //
    BaseMapModel.prototype.getRole = function (uid) {
        if (this.removes.indexOf(uid) !== -1) {
            return null;
        }
        return this.roles.find(function (m) { return m.uid === uid; });
    };
    // 是否可通过
    BaseMapModel.prototype.checkCanPass = function (x, y) {
        return this.isInMap(x, y) && !this.walls[x + '_' + y];
    };
    // 是否在地图内
    BaseMapModel.prototype.isInMap = function (x, y) {
        return x >= 0 && x < this.gridSize.width && y >= 0 && y < this.gridSize.height;
    };
    // 是否在地图内 像素
    BaseMapModel.prototype.isInMapByPixel = function (position) {
        var pos = position.sub(this.mapOrigin, this.tempVec);
        return pos.x >= 0 && pos.x < this.mapSize.width && pos.y >= 0 && pos.y < this.mapSize.height;
    };
    // 获取最近的网格点 通过像素点
    BaseMapModel.prototype.getLatelyPointByPosition = function (position) {
        var point = this.getActPointByPixel(position);
        point.x = cc.misc.clampf(point.x, 0, this.gridSize.width - 1);
        point.y = cc.misc.clampf(point.y, 0, this.gridSize.height - 1);
        return point;
    };
    // 进入地图
    BaseMapModel.prototype.createRole = function (role, point, position) {
        role.initMapInfo(this);
        role.setPointAndPosition(point, position);
        this.addRole(role);
    };
    // 获取主区域
    BaseMapModel.prototype.getMainArea = function () { return this; };
    // 获取另一个任意门所在区域
    BaseMapModel.prototype.getNextDoorHomeData = function (role) { return this; };
    // 获取当前地图所有可以到达的点
    BaseMapModel.prototype.getCanMovePoints = function () { return this.canMovePoints; };
    // 获取所有设施
    BaseMapModel.prototype.getBuilds = function () { return []; };
    BaseMapModel.prototype.isHasBuildByType = function (type) {
        return this.getBuilds().some(function (m) { return m.type === type; });
    };
    // 使用家具开始的时候
    BaseMapModel.prototype.useBuildBegin = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    // 使用中
    BaseMapModel.prototype.useBuildRun = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    // 使用家具结束的时候
    BaseMapModel.prototype.useBuildEnd = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    // 获取设施的root位置
    BaseMapModel.prototype.getBuildRootPositionByType = function (type) { return null; };
    // 进入地图 根据特殊位置
    BaseMapModel.prototype.enterByPoint = function (role, point) {
        this.createRole(role, point);
        this.emit(EventType_1.default.ROLE_ENTER_SCENE, role, true);
    };
    // 清理临时信息
    BaseMapModel.prototype.cleanRoleTempInfo = function (role) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
    };
    // 离开地图
    BaseMapModel.prototype.leave = function (role) {
        this.removeRole(role);
        this.emit(EventType_1.default.ROLE_LEAVE_SCENE, role, 0);
    };
    BaseMapModel.prototype.checkChangeArea = function (role, target) { };
    // 获取闲逛位置
    BaseMapModel.prototype.getStrollPoint = function (role) {
        var point = this.getCanMovePoints().random(), cnt = 5;
        do {
            var mag = point.sub(role.point).mag();
            // 在3格和10格之内
            if (mag < 4 || mag > 10) {
                point = this.groundPoints.random();
            }
            else {
                break;
            }
        } while (--cnt >= 0);
        return point;
    };
    BaseMapModel.prototype.getWorkPoint = function (role) { return null; };
    return BaseMapModel;
}());
exports.default = BaseMapModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxzbmFpbGlzbGVcXEJhc2VNYXBNb2RlbC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEscURBQW9FO0FBQ3BFLDJEQUEwRDtBQUMxRCxtREFBa0Q7QUFDbEQsMkRBQTREO0FBQzVELDBEQUFxRDtBQUlyRCxTQUFTO0FBQ1Q7SUEwQkksc0JBQVksR0FBVztRQXhCaEIsUUFBRyxHQUFXLEVBQUUsQ0FBQTtRQUdoQixjQUFTLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUNuQyxhQUFRLEdBQVksRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUEsQ0FBQyxNQUFNO1FBQ3hDLFlBQU8sR0FBWSxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQSxDQUFDLE1BQU07UUFDdkMsY0FBUyxHQUFjLEVBQUUsQ0FBQSxDQUFDLE9BQU87UUFDakMsa0JBQWEsR0FBYyxFQUFFLENBQUEsQ0FBQyxPQUFPO1FBQ3JDLGdCQUFXLEdBQVcsQ0FBQyxDQUFBO1FBQ3ZCLGlCQUFZLEdBQVcsQ0FBQyxDQUFBLENBQUMsY0FBYztRQUN2QyxXQUFNLEdBQVksS0FBSyxDQUFBLENBQUMsTUFBTTtRQUM5QixVQUFLLEdBQVEsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUN0QixjQUFTLEdBQVEsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUMxQixVQUFLLEdBQWMsRUFBRSxDQUFBLENBQUMsVUFBVTtRQUNoQyxpQkFBWSxHQUFVLEVBQUUsQ0FBQSxDQUFDLGFBQWE7UUFFdEMsWUFBTyxHQUFhLEVBQUUsQ0FBQSxDQUFDLFdBQVc7UUFDbEMsa0JBQWEsR0FBUSxFQUFFLENBQUEsQ0FBQyxhQUFhO1FBQ3JDLGlCQUFZLEdBQWMsRUFBRSxDQUFBLENBQUMsT0FBTztRQUVwQyxxQkFBZ0IsR0FBVyxFQUFFLENBQUE7UUFFNUIsWUFBTyxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUc5QixJQUFJLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQTtJQUNsQixDQUFDO0lBRU0sMkJBQUksR0FBWCxVQUFZLFNBQXVCO1FBQy9CLElBQUksQ0FBQyxTQUFTLEdBQUcsU0FBUyxDQUFBO1FBQzFCLElBQUksQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxpQkFBaUIsRUFBRSxTQUFTLENBQUMsQ0FBQyxDQUFBO0lBQ3RFLENBQUM7SUFFRCxPQUFPO0lBQ0EsK0JBQVEsR0FBZixVQUFnQixJQUFTO1FBQXpCLGlCQXdCQzs7UUF2QkcsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFBO1FBQ3hCLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDakQsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUM1QixJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQzdCLElBQUksQ0FBQyxPQUFPLEdBQUcsK0JBQWMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7UUFDekUsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQTtRQUM3RCxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQTtRQUN0RSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7UUFDekIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzFDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtnQkFDM0MsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTthQUNuQztTQUNKO1FBQ0QsSUFBSTtRQUNKLE1BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxFQUFsQyxDQUFrQyxFQUFDO1FBQzVELE1BQUEsSUFBSSxDQUFDLFFBQVEsMENBQUUsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEscUJBQVMsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsSUFBSSxFQUFFLEdBQUcsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxFQUFsQyxDQUFrQyxDQUFDLEVBQWxJLENBQWtJLEVBQUM7UUFDL0osTUFBTTtRQUNOLE1BQUEsSUFBSSxDQUFDLFNBQVMsMENBQUUsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUNyQixJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ3pCLEtBQUksQ0FBQyxTQUFTLENBQUMsS0FBSSxDQUFDLGtCQUFrQixDQUFDLFFBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLFFBQVEsQ0FBQTtRQUMxRSxDQUFDLEVBQUM7UUFDRixFQUFFO1FBQ0YsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsS0FBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQTVCLENBQTRCLENBQUMsQ0FBQTtJQUNqRixDQUFDO0lBRUQsb0JBQW9CO0lBQ2IsNEJBQUssR0FBWixjQUFpQixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUEsQ0FBQyxDQUFDO0lBRXhDLFFBQVE7SUFDRCxrQ0FBVyxHQUFsQixjQUF1QixPQUFPLG9CQUFZLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFBLENBQUMsQ0FBQztJQUVoRSwyQkFBSSxHQUFkLFVBQWUsSUFBcUI7UUFBRSxnQkFBYzthQUFkLFVBQWMsRUFBZCxxQkFBYyxFQUFkLElBQWM7WUFBZCwrQkFBYzs7UUFBSSxXQUFXLENBQUMsSUFBSSxPQUFoQixXQUFXLFlBQU0sSUFBSSxHQUFLLE1BQU0sR0FBQztJQUFDLENBQUM7SUFFM0YsS0FBSztJQUNFLGdDQUFTLEdBQWhCLFVBQWlCLEdBQVk7UUFDekIsSUFBSSxDQUFDLE1BQU0sR0FBRyxHQUFHLENBQUE7UUFDakIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxFQUFoQixDQUFnQixDQUFDLENBQUE7SUFDN0MsQ0FBQztJQUVNLCtCQUFRLEdBQWY7UUFBQSxpQkFBNkU7UUFBekQsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsS0FBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUF4QixDQUF3QixDQUFDLENBQUE7SUFBQyxDQUFDO0lBRXJFLDhCQUFPLEdBQWYsVUFBZ0IsSUFBYTtRQUN6QixJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDekIsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDeEI7YUFBTTtZQUNILEVBQUUsQ0FBQyxLQUFLLENBQUMsb0JBQVksQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1NBQ3ZFO0lBQ0wsQ0FBQztJQUVNLGlDQUFVLEdBQWpCLFVBQWtCLElBQWlCO1FBQy9CLElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO1lBQ3ZDLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtTQUM5QjtJQUNMLENBQUM7SUFFTSxvQ0FBYSxHQUFwQixVQUFxQixRQUFnQjtRQUFyQyxpQkFVQztRQVRHLElBQU0sS0FBSyxHQUFHLEVBQUUsQ0FBQTtRQUNoQixTQUFTLENBQUMsT0FBTyxDQUFDLFFBQVEsR0FBRyxRQUFRLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUNsRCxJQUFJLElBQUksR0FBVSxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQy9CLElBQUksQ0FBQyxJQUFJLEVBQUU7Z0JBQ1AsSUFBSSxHQUFHLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFBO2FBQzVCO1lBQ0QsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsS0FBSyxFQUFFLEtBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLEtBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUNsRyxDQUFDLENBQUMsQ0FBQTtRQUNGLE9BQU8sS0FBSyxDQUFBO0lBQ2hCLENBQUM7SUFFTSxtQ0FBWSxHQUFuQixVQUFvQixFQUFVLElBQUksT0FBTyxJQUFJLENBQUEsQ0FBQyxDQUFDO0lBQ3hDLHNDQUFlLEdBQXRCLFVBQXVCLEVBQVUsSUFBSSxPQUFPLElBQUksQ0FBQSxDQUFDLENBQUM7SUFFM0MsNkJBQU0sR0FBYixVQUFjLEVBQVU7UUFDcEIsaUJBQWlCO1FBQ2pCLEtBQUssSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDN0MsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMxQixJQUFJLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUU7Z0JBQ2hELElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTthQUMxQjtpQkFBTTtnQkFDSCxJQUFJLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxDQUFBO2FBQ2xCO1NBQ0o7SUFDTCxDQUFDO0lBRUQsY0FBYztJQUNQLDZDQUFzQixHQUE3QixVQUE4QixLQUFjO1FBQ3hDLE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUE7SUFDMUMsQ0FBQztJQUVELGFBQWE7SUFDTix5Q0FBa0IsR0FBekIsVUFBMEIsS0FBYyxFQUFFLEdBQWE7UUFDbkQsT0FBTywrQkFBYyxDQUFDLGVBQWUsQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLE9BQU8sRUFBRSxHQUFHLENBQUMsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFBO0lBQzNGLENBQUM7SUFFRCxhQUFhO0lBQ04seUNBQWtCLEdBQXpCLFVBQTBCLEdBQVksRUFBRSxHQUFhO1FBQ2pELE9BQU8sK0JBQWMsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxJQUFJLENBQUMsT0FBTyxFQUFFLEdBQUcsQ0FBQyxDQUFBO0lBQ25HLENBQUM7SUFFRCxXQUFXO0lBQ0osb0NBQWEsR0FBcEIsVUFBcUIsSUFBaUcsRUFBRSxLQUFlO1FBQ25JLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEVBQUU7WUFDckMsT0FBTTtTQUNUO2FBQU0sSUFBSSxJQUFJLENBQUMsT0FBTyxLQUFLLGVBQU8sQ0FBQyxJQUFJLEVBQUU7WUFDdEMsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLHFCQUFVLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQTtTQUN6RDthQUFNLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxlQUFPLENBQUMsSUFBSSxFQUFFO1lBQ3RDLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUE7WUFDcEQsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsRUFBRSxZQUFZO2dCQUNyQyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxLQUFLLEVBQUU7b0JBQy9DLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUE7b0JBQ2hDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLENBQUE7aUJBQ3JDO2FBQ0o7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxLQUFLLENBQUMsQ0FBQSxDQUFDLFFBQVE7YUFDOUM7WUFDRCxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFBO1NBQzdCO0lBQ0wsQ0FBQztJQUVELFlBQVk7SUFDRix1Q0FBZ0IsR0FBMUIsVUFBMkIsSUFBUyxFQUFFLEtBQWMsSUFBSSxDQUFDO0lBRXpELFlBQVk7SUFDRixzQ0FBZSxHQUF6QixVQUEwQixJQUFTO1FBQy9CLElBQU0sQ0FBQyxHQUFHLCtCQUFjLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUM1RyxJQUFJLElBQUksQ0FBQyxXQUFXLEdBQUcsQ0FBQyxFQUFFO1lBQ3RCLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUE7U0FDMUQ7YUFBTTtZQUNILElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsR0FBRyxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQTtTQUNqRTtJQUNMLENBQUM7SUFFRCxVQUFVO0lBQ0gsOEJBQU8sR0FBZCxVQUFlLEdBQVc7UUFDdEIsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxLQUFLLEdBQUcsRUFBbEIsQ0FBa0IsQ0FBQyxDQUFBO0lBQ3ZGLENBQUM7SUFFRCxFQUFFO0lBQ0ssOEJBQU8sR0FBZCxVQUFlLEdBQVc7UUFDdEIsSUFBSSxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtZQUNsQyxPQUFPLElBQUksQ0FBQTtTQUNkO1FBQ0QsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssR0FBRyxFQUFiLENBQWEsQ0FBQyxDQUFBO0lBQzlDLENBQUM7SUFFRCxRQUFRO0lBQ0QsbUNBQVksR0FBbkIsVUFBb0IsQ0FBUyxFQUFFLENBQVM7UUFDcEMsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQTtJQUN6RCxDQUFDO0lBRUQsU0FBUztJQUNGLDhCQUFPLEdBQWQsVUFBZSxDQUFTLEVBQUUsQ0FBUztRQUMvQixPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFBO0lBQ2xGLENBQUM7SUFFRCxZQUFZO0lBQ0wscUNBQWMsR0FBckIsVUFBc0IsUUFBaUI7UUFDbkMsSUFBTSxHQUFHLEdBQUcsUUFBUSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN0RCxPQUFPLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLElBQUksR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQTtJQUNoRyxDQUFDO0lBRUQsaUJBQWlCO0lBQ1YsK0NBQXdCLEdBQS9CLFVBQWdDLFFBQWlCO1FBQzdDLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUMvQyxLQUFLLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFBO1FBQzdELEtBQUssQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUE7UUFDOUQsT0FBTyxLQUFLLENBQUE7SUFDaEIsQ0FBQztJQUVELE9BQU87SUFDQSxpQ0FBVSxHQUFqQixVQUFrQixJQUFhLEVBQUUsS0FBZSxFQUFFLFFBQWtCO1FBQ2hFLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDdEIsSUFBSSxDQUFDLG1CQUFtQixDQUFDLEtBQUssRUFBRSxRQUFRLENBQUMsQ0FBQTtRQUN6QyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ3RCLENBQUM7SUFFRCxRQUFRO0lBQ0Qsa0NBQVcsR0FBbEIsY0FBNEIsT0FBTyxJQUFJLENBQUEsQ0FBQyxDQUFDO0lBRXpDLGVBQWU7SUFDUiwwQ0FBbUIsR0FBMUIsVUFBMkIsSUFBYSxJQUFTLE9BQU8sSUFBSSxDQUFBLENBQUMsQ0FBQztJQUU5RCxpQkFBaUI7SUFDVix1Q0FBZ0IsR0FBdkIsY0FBdUMsT0FBTyxJQUFJLENBQUMsYUFBYSxDQUFBLENBQUMsQ0FBQztJQUVsRSxTQUFTO0lBQ0YsZ0NBQVMsR0FBaEIsY0FBcUIsT0FBTyxFQUFFLENBQUEsQ0FBQyxDQUFDO0lBRXpCLHVDQUFnQixHQUF2QixVQUF3QixJQUFZO1FBQ2hDLE9BQU8sSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxJQUFJLEtBQUssSUFBSSxFQUFmLENBQWUsQ0FBQyxDQUFBO0lBQ3RELENBQUM7SUFFRCxZQUFZO0lBQ0wsb0NBQWEsR0FBcEI7UUFBcUIsZ0JBQWM7YUFBZCxVQUFjLEVBQWQscUJBQWMsRUFBZCxJQUFjO1lBQWQsMkJBQWM7O0lBQUksQ0FBQztJQUV4QyxNQUFNO0lBQ0Msa0NBQVcsR0FBbEI7UUFBbUIsZ0JBQWM7YUFBZCxVQUFjLEVBQWQscUJBQWMsRUFBZCxJQUFjO1lBQWQsMkJBQWM7O0lBQUksQ0FBQztJQUV0QyxZQUFZO0lBQ0wsa0NBQVcsR0FBbEI7UUFBbUIsZ0JBQWM7YUFBZCxVQUFjLEVBQWQscUJBQWMsRUFBZCxJQUFjO1lBQWQsMkJBQWM7O0lBQUksQ0FBQztJQUV0QyxjQUFjO0lBQ1AsaURBQTBCLEdBQWpDLFVBQWtDLElBQVksSUFBYSxPQUFPLElBQUksQ0FBQSxDQUFDLENBQUM7SUFFeEUsY0FBYztJQUNQLG1DQUFZLEdBQW5CLFVBQW9CLElBQWEsRUFBRSxLQUFjO1FBQzdDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxFQUFFLEtBQUssQ0FBQyxDQUFBO1FBQzVCLElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDckQsQ0FBQztJQUVELFNBQVM7SUFDRix3Q0FBaUIsR0FBeEIsVUFBeUIsSUFBYTtRQUFFLGdCQUFjO2FBQWQsVUFBYyxFQUFkLHFCQUFjLEVBQWQsSUFBYztZQUFkLCtCQUFjOztJQUFJLENBQUM7SUFFM0QsT0FBTztJQUNBLDRCQUFLLEdBQVosVUFBYSxJQUFhO1FBQ3RCLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDckIsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGdCQUFnQixFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQTtJQUNsRCxDQUFDO0lBRU0sc0NBQWUsR0FBdEIsVUFBdUIsSUFBYSxFQUFFLE1BQVcsSUFBSSxDQUFDO0lBRXRELFNBQVM7SUFDRixxQ0FBYyxHQUFyQixVQUFzQixJQUFhO1FBQy9CLElBQUksS0FBSyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDLE1BQU0sRUFBRSxFQUFFLEdBQUcsR0FBRyxDQUFDLENBQUE7UUFDckQsR0FBRztZQUNDLElBQUksR0FBRyxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFBO1lBQ3JDLFlBQVk7WUFDWixJQUFJLEdBQUcsR0FBRyxDQUFDLElBQUksR0FBRyxHQUFHLEVBQUUsRUFBRTtnQkFDckIsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxFQUFFLENBQUE7YUFDckM7aUJBQU07Z0JBQ0gsTUFBSzthQUNSO1NBQ0osUUFBUSxFQUFFLEdBQUcsSUFBSSxDQUFDLEVBQUM7UUFDcEIsT0FBTyxLQUFLLENBQUE7SUFDaEIsQ0FBQztJQUVNLG1DQUFZLEdBQW5CLFVBQW9CLElBQWEsSUFBYSxPQUFPLElBQUksQ0FBQSxDQUFDLENBQUM7SUFDL0QsbUJBQUM7QUFBRCxDQWhSQSxBQWdSQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWFwU2NlbmVUeXBlLCBNYXBUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xyXG5pbXBvcnQgeyBtYXBIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9NYXBIZWxwZXJcIjtcclxuaW1wb3J0IHsgbWFwU2NlbmVIZWxwZXIgfSBmcm9tIFwiLi9NYXBTY2VuZUhlbHBlclwiO1xyXG5pbXBvcnQgeyBNQVhfWklOREVYIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xyXG5pbXBvcnQgRXZlbnRUeXBlIGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvRXZlbnRUeXBlXCI7XHJcbmltcG9ydCBSb2xlT2JqIGZyb20gXCIuL1JvbGVPYmpcIjtcclxuaW1wb3J0IEJhc2VSb2xlT2JqIGZyb20gXCIuL0Jhc2VSb2xlT2JqXCI7XHJcblxyXG4vLyDlnLDlm77mqKHlnovmlbDmja5cclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQmFzZU1hcE1vZGVsIHtcclxuXHJcbiAgICBwdWJsaWMgdWlkOiBzdHJpbmcgPSAnJ1xyXG4gICAgcHVibGljIG1hcFR5cGU6IE1hcFR5cGVcclxuICAgIHB1YmxpYyBzY2VuZVR5cGU6IE1hcFNjZW5lVHlwZVxyXG4gICAgcHVibGljIG1hcE9yaWdpbjogY2MuVmVjMiA9IGNjLnYyKCkgLy/lnLDlm77ljp/ngrlcclxuICAgIHB1YmxpYyBncmlkU2l6ZTogY2MuU2l6ZSA9IGNjLnNpemUoMCwgMCkgLy/moLzlrZDlpKflsI9cclxuICAgIHB1YmxpYyBtYXBTaXplOiBjYy5TaXplID0gY2Muc2l6ZSgwLCAwKSAvL+WunumZheWkp+Wwj1xyXG4gICAgcHVibGljIG1hcFBvaW50czogY2MuVmVjMltdID0gW10gLy/lnLDlm77miYDmnInngrlcclxuICAgIHB1YmxpYyBjYW5Nb3ZlUG9pbnRzOiBjYy5WZWMyW10gPSBbXSAvL+WcsOWbvuaJgOacieeCuVxyXG4gICAgcHVibGljIG1heEdyaWRTaXplOiBudW1iZXIgPSAwXHJcbiAgICBwdWJsaWMgekluZGV4SGVpZ2h0OiBudW1iZXIgPSAwIC8v55So5LqO6KeS6Imy5bGC57qnemluZGV4XHJcbiAgICBwdWJsaWMgYWN0aXZlOiBib29sZWFuID0gZmFsc2UgLy/mmK/lkKbmv4DmtLtcclxuICAgIHB1YmxpYyB3YWxsczogYW55ID0ge30gLy/pmpznoo3liJfooahcclxuICAgIHB1YmxpYyBwb3NpdGlvbnM6IGFueSA9IHt9IC8v5YOP57Sg5L2N572uXHJcbiAgICBwdWJsaWMgcm9sZXM6IFJvbGVPYmpbXSA9IFtdIC8v5Zy65pmv5Lit55qE5omA5pyJ6KeS6ImyXHJcbiAgICBwdWJsaWMgY2FuU2F2ZVJvbGVzOiBhbnlbXSA9IFtdIC8v6K6w5b2V5Y+v5Lul5L+d5a2Y55qE5a6i5Lq65YiX6KGoXHJcblxyXG4gICAgcHVibGljIHJlbW92ZXM6IHN0cmluZ1tdID0gW10gLy/pnIDopoHliKDpmaTnmoTnjqnlrrbliJfooahcclxuICAgIHB1YmxpYyBidWlsZExpc3RJbmZvOiBhbnkgPSB7fSAvL+iusOW9leW7uumAoOWIl+ihqOeahOS9jee9ruS/oeaBr1xyXG4gICAgcHVibGljIGdyb3VuZFBvaW50czogY2MuVmVjMltdID0gW10gLy/kuLTml7blnLDpnaLngrlcclxuXHJcbiAgICBwdWJsaWMgb3BlbkZ1cm5JbmZvRnJvbTogc3RyaW5nID0gJydcclxuXHJcbiAgICBwcml2YXRlIHRlbXBWZWM6IGNjLlZlYzIgPSBjYy52MigpXHJcblxyXG4gICAgY29uc3RydWN0b3IodWlkOiBzdHJpbmcpIHtcclxuICAgICAgICB0aGlzLnVpZCA9IHVpZFxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBpbml0KHNjZW5lVHlwZTogTWFwU2NlbmVUeXBlKSB7XHJcbiAgICAgICAgdGhpcy5zY2VuZVR5cGUgPSBzY2VuZVR5cGVcclxuICAgICAgICB0aGlzLmluaXRKc29uKGFzc2V0c01nci5nZXRKc29uRGF0YSgnX21hcF9zY2VuZV9ncmlkJywgc2NlbmVUeXBlKSlcclxuICAgIH1cclxuXHJcbiAgICAvLyDphY3nva7kv6Hmga9cclxuICAgIHB1YmxpYyBpbml0SnNvbihqc29uOiBhbnkpIHtcclxuICAgICAgICB0aGlzLm1hcFR5cGUgPSBqc29uLnR5cGVcclxuICAgICAgICB0aGlzLm1hcE9yaWdpbi5zZXQyKGpzb24ub3JpZ2luLngsIGpzb24ub3JpZ2luLnkpXHJcbiAgICAgICAgdGhpcy5ncmlkU2l6ZS53aWR0aCA9IGpzb24ud1xyXG4gICAgICAgIHRoaXMuZ3JpZFNpemUuaGVpZ2h0ID0ganNvbi5oXHJcbiAgICAgICAgdGhpcy5tYXBTaXplID0gbWFwU2NlbmVIZWxwZXIuY29udmVydE1hcFNpemUodGhpcy5tYXBUeXBlLCB0aGlzLmdyaWRTaXplKVxyXG4gICAgICAgIHRoaXMubWF4R3JpZFNpemUgPSB0aGlzLmdyaWRTaXplLndpZHRoICsgdGhpcy5ncmlkU2l6ZS5oZWlnaHRcclxuICAgICAgICB0aGlzLnpJbmRleEhlaWdodCA9IE1hdGguZmxvb3IodGhpcy5tYXBTaXplLmhlaWdodCkgKyB0aGlzLm1hcE9yaWdpbi55XHJcbiAgICAgICAgdGhpcy5tYXBQb2ludHMubGVuZ3RoID0gMFxyXG4gICAgICAgIGZvciAobGV0IHggPSAwOyB4IDwgdGhpcy5ncmlkU2l6ZS53aWR0aDsgeCsrKSB7XHJcbiAgICAgICAgICAgIGZvciAobGV0IHkgPSAwOyB5IDwgdGhpcy5ncmlkU2l6ZS5oZWlnaHQ7IHkrKykge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5tYXBQb2ludHMucHVzaChjYy52Mih4LCB5KSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICAvLyDloplcclxuICAgICAgICBqc29uLndhbGxzPy5mb3JFYWNoKG0gPT4gdGhpcy53YWxsc1ttLnggKyAnXycgKyBtLnldID0gdHJ1ZSlcclxuICAgICAgICBqc29uLm9ic3RhY2xlPy5mb3JFYWNoKG0gPT4gbWFwSGVscGVyLmdlblBvaW50c0J5U2l6ZSh1dC5zdHJpbmdUb1ZlYzIobS5zaXplLCAneCcpLCB1dC5zdHJpbmdUb1ZlYzIobS5wb2ludCkpLmZvckVhY2gocCA9PiB0aGlzLndhbGxzW3AueCArICdfJyArIHAueV0gPSB0cnVlKSlcclxuICAgICAgICAvLyDku6Pmm7/ngrlcclxuICAgICAgICBqc29uLnBvc2l0aW9ucz8uZm9yRWFjaChtID0+IHtcclxuICAgICAgICAgICAgY29uc3QgcG9zaXRpb24gPSBjYy52MihtKVxyXG4gICAgICAgICAgICB0aGlzLnBvc2l0aW9uc1t0aGlzLmdldEFjdFBvaW50QnlQaXhlbChwb3NpdGlvbikuSm9pbignXycpXSA9IHBvc2l0aW9uXHJcbiAgICAgICAgfSlcclxuICAgICAgICAvL1xyXG4gICAgICAgIHRoaXMuY2FuTW92ZVBvaW50cyA9IHRoaXMubWFwUG9pbnRzLmZpbHRlcihtID0+ICF0aGlzLndhbGxzW20ueCArICdfJyArIG0ueV0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qCH5b+X5oCnaWQg5aaC5p6c5piv5a6i5oi/5LyaIOi/lOWbnuaIv+WPt1xyXG4gICAgcHVibGljIGdldElkKCkgeyByZXR1cm4gdGhpcy5zY2VuZVR5cGUgfVxyXG5cclxuICAgIC8vIOiOt+WPluWcuuaZr+WQjVxyXG4gICAgcHVibGljIGdldFNjZW5lS2V5KCkgeyByZXR1cm4gTWFwU2NlbmVUeXBlW3RoaXMuc2NlbmVUeXBlXS50b0xvd2VyQ2FzZSgpIH1cclxuXHJcbiAgICBwcm90ZWN0ZWQgZW1pdCh0eXBlOiBzdHJpbmcgfCBudW1iZXIsIC4uLnBhcmFtczogYW55KSB7IGV2ZW50Q2VudGVyLmVtaXQodHlwZSwgLi4ucGFyYW1zKSB9XHJcblxyXG4gICAgLy8g5r+A5rS7XHJcbiAgICBwdWJsaWMgc2V0QWN0aXZlKHZhbDogYm9vbGVhbikge1xyXG4gICAgICAgIHRoaXMuYWN0aXZlID0gdmFsXHJcbiAgICAgICAgdGhpcy5yb2xlcy5mb3JFYWNoKG0gPT4gbS5zZXRBY3RpdmUodmFsKSlcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0Um9sZXMoKSB7IHJldHVybiB0aGlzLnJvbGVzLmZpbHRlcihtID0+ICF0aGlzLnJlbW92ZXMuaGFzKG0udWlkKSkgfVxyXG5cclxuICAgIHByaXZhdGUgYWRkUm9sZShyb2xlOiBSb2xlT2JqKSB7XHJcbiAgICAgICAgaWYgKCF0aGlzLmhhc1JvbGUocm9sZS51aWQpKSB7XHJcbiAgICAgICAgICAgIHRoaXMucm9sZXMucHVzaChyb2xlKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNjLmVycm9yKE1hcFNjZW5lVHlwZVt0aGlzLnNjZW5lVHlwZV0gKyAnIOS4reW3sue7j+acieivpeinkuiJsuS6hiB1aWQ9JyArIHJvbGUudWlkKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgcmVtb3ZlUm9sZShyb2xlOiBCYXNlUm9sZU9iaikge1xyXG4gICAgICAgIGlmICh0aGlzLnJlbW92ZXMuaW5kZXhPZihyb2xlLnVpZCkgPT09IC0xKSB7XHJcbiAgICAgICAgICAgIHRoaXMucmVtb3Zlcy5wdXNoKHJvbGUudWlkKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgZ2V0QnVpbGRJdGVtcyhqc29uTmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QgaXRlbXMgPSB7fVxyXG4gICAgICAgIGFzc2V0c01nci5nZXRKc29uKGpzb25OYW1lICsgJ1VubG9jaycpLmRhdGFzLmZvckVhY2gobSA9PiB7XHJcbiAgICAgICAgICAgIGxldCBsaXN0OiBhbnlbXSA9IGl0ZW1zW20udHlwZV1cclxuICAgICAgICAgICAgaWYgKCFsaXN0KSB7XHJcbiAgICAgICAgICAgICAgICBsaXN0ID0gaXRlbXNbbS50eXBlXSA9IFtdXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgbGlzdC5wdXNoKHsgaWQ6IG0uaWQsIGpzb246IG0sIGJ1aWxkOiB0aGlzLmdldEJ1aWxkQnlJZChtLmlkKSB8fCB0aGlzLmNyZWF0ZVRlbXBCdWlsZChtLmlkKSB9KVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgcmV0dXJuIGl0ZW1zXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldEJ1aWxkQnlJZChpZDogbnVtYmVyKSB7IHJldHVybiBudWxsIH1cclxuICAgIHB1YmxpYyBjcmVhdGVUZW1wQnVpbGQoaWQ6IG51bWJlcikgeyByZXR1cm4gbnVsbCB9XHJcblxyXG4gICAgcHVibGljIHVwZGF0ZShkdDogbnVtYmVyKSB7XHJcbiAgICAgICAgLy8g6YGN5Y6G5q+P5Liq6KeS6ImyIOaooeaLn+S7luS7rOeahOihjOWKqFxyXG4gICAgICAgIGZvciAobGV0IGkgPSB0aGlzLnJvbGVzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJvbGUgPSB0aGlzLnJvbGVzW2ldXHJcbiAgICAgICAgICAgIGlmICh0aGlzLnJlbW92ZXMucmVtb3ZlKHJvbGUudWlkKSB8fCAhcm9sZS5pc1ZhbGlkKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLnJvbGVzLnNwbGljZShpLCAxKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcm9sZS51cGRhdGUoZHQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qC55o2u572R5qC854K56I635Y+W5YOP57Sg5L2N572uXHJcbiAgICBwdWJsaWMgZ2V0TW92ZVBvc2l0aW9uQnlQb2ludChwb2ludDogY2MuVmVjMikge1xyXG4gICAgICAgIHJldHVybiB0aGlzLnBvc2l0aW9uc1twb2ludC5Kb2luKCdfJyldXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qC55o2u572R5qC854K56I635Y+W5YOP57Sg54K5XHJcbiAgICBwdWJsaWMgZ2V0QWN0UGl4ZWxCeVBvaW50KHBvaW50OiBjYy5WZWMyLCBvdXQ/OiBjYy5WZWMyKSB7XHJcbiAgICAgICAgcmV0dXJuIG1hcFNjZW5lSGVscGVyLmdldFBpeGVsQnlQb2ludChwb2ludCwgdGhpcy5tYXBUeXBlLCBvdXQpLmFkZFNlbGYodGhpcy5tYXBPcmlnaW4pXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qC55o2u5YOP57Sg54K56I635Y+W572R5qC854K5XHJcbiAgICBwdWJsaWMgZ2V0QWN0UG9pbnRCeVBpeGVsKHBvczogY2MuVmVjMiwgb3V0PzogY2MuVmVjMikge1xyXG4gICAgICAgIHJldHVybiBtYXBTY2VuZUhlbHBlci5nZXRQb2ludEJ5UGl4ZWwocG9zLnN1Yih0aGlzLm1hcE9yaWdpbiwgdGhpcy50ZW1wVmVjKSwgdGhpcy5tYXBUeXBlLCBvdXQpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g6K6+572uemluZGV4XHJcbiAgICBwdWJsaWMgc2V0Um9sZVppbmRleChyb2xlOiB7IHpJbmRleDogbnVtYmVyLCBwb3NpdGlvbjogY2MuVmVjMiwgY2hlY2taaW5kZXhQb2ludD86IGNjLlZlYzIsIG5vVXBkYXRlWmluZGV4PzogYm9vbGVhbiB9LCBmb3JjZT86IGJvb2xlYW4pIHtcclxuICAgICAgICBpZiAoIXRoaXMuYWN0aXZlIHx8IHJvbGUubm9VcGRhdGVaaW5kZXgpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLm1hcFR5cGUgPT09IE1hcFR5cGUuUkVDVCkge1xyXG4gICAgICAgICAgICByb2xlLnpJbmRleCA9IE1hdGguZmxvb3IoTUFYX1pJTkRFWCAtIHJvbGUucG9zaXRpb24ueSlcclxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMubWFwVHlwZSA9PT0gTWFwVHlwZS5TS0VXKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHBvaW50ID0gdGhpcy5nZXRBY3RQb2ludEJ5UGl4ZWwocm9sZS5wb3NpdGlvbilcclxuICAgICAgICAgICAgaWYgKHJvbGUuY2hlY2taaW5kZXhQb2ludCkgeyAvL+aciei/meS4quS7o+ihqOaYr+WKqOaAgeaUueWPmFxyXG4gICAgICAgICAgICAgICAgaWYgKCFwb2ludC5lcXVhbHMocm9sZS5jaGVja1ppbmRleFBvaW50KSB8fCBmb3JjZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJvbGUuY2hlY2taaW5kZXhQb2ludC5zZXQocG9pbnQpXHJcbiAgICAgICAgICAgICAgICAgICAgdGhpcy51cGRhdGVSb2xlWmluZGV4KHJvbGUsIHBvaW50KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVSb2xlWmluZGV4KHJvbGUsIHBvaW50KSAvL+WQpuWImeebtOaOpeWIt+aWsFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHRoaXMuY2xhbXBSb2xlWmluZGV4KHJvbGUpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOagueaNruWQhOS4quWcsOWbviDliLfmlrBcclxuICAgIHByb3RlY3RlZCB1cGRhdGVSb2xlWmluZGV4KHJvbGU6IGFueSwgcG9pbnQ6IGNjLlZlYzIpIHsgfVxyXG5cclxuICAgIC8vIOWGjeasoeS/ruatoyDmnIDlkI7kuIDkvY1cclxuICAgIHByb3RlY3RlZCBjbGFtcFJvbGVaaW5kZXgocm9sZTogYW55KSB7XHJcbiAgICAgICAgY29uc3QgeSA9IG1hcFNjZW5lSGVscGVyLmdldEFjdFBvaW50QnlQaXhlbChyb2xlLnBvc2l0aW9uLnN1Yih0aGlzLm1hcE9yaWdpbiwgdGhpcy50ZW1wVmVjKSwgdGhpcy5tYXBUeXBlKS55XHJcbiAgICAgICAgaWYgKHJvbGUuY2hlY2taaW5kZXggPCAwKSB7XHJcbiAgICAgICAgICAgIHJvbGUuekluZGV4ID0gcm9sZS5jaGVja1ppbmRleCAtIE1hdGguZmxvb3IoeSAlIDEgKiAxMClcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICByb2xlLnpJbmRleCA9IHJvbGUuY2hlY2taaW5kZXggKyAoMTAgLSBNYXRoLmZsb29yKHkgJSAxICogMTApKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyDmmK/lkKbmnInov5nkuKrop5LoibJcclxuICAgIHB1YmxpYyBoYXNSb2xlKHVpZDogc3RyaW5nKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMucmVtb3Zlcy5pbmRleE9mKHVpZCkgPT09IC0xICYmIHRoaXMucm9sZXMuc29tZShtID0+IG0gJiYgbS51aWQgPT09IHVpZClcclxuICAgIH1cclxuXHJcbiAgICAvL1xyXG4gICAgcHVibGljIGdldFJvbGUodWlkOiBzdHJpbmcpIHtcclxuICAgICAgICBpZiAodGhpcy5yZW1vdmVzLmluZGV4T2YodWlkKSAhPT0gLTEpIHtcclxuICAgICAgICAgICAgcmV0dXJuIG51bGxcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHRoaXMucm9sZXMuZmluZChtID0+IG0udWlkID09PSB1aWQpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5piv5ZCm5Y+v6YCa6L+HXHJcbiAgICBwdWJsaWMgY2hlY2tDYW5QYXNzKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuaXNJbk1hcCh4LCB5KSAmJiAhdGhpcy53YWxsc1t4ICsgJ18nICsgeV1cclxuICAgIH1cclxuXHJcbiAgICAvLyDmmK/lkKblnKjlnLDlm77lhoVcclxuICAgIHB1YmxpYyBpc0luTWFwKHg6IG51bWJlciwgeTogbnVtYmVyKSB7XHJcbiAgICAgICAgcmV0dXJuIHggPj0gMCAmJiB4IDwgdGhpcy5ncmlkU2l6ZS53aWR0aCAmJiB5ID49IDAgJiYgeSA8IHRoaXMuZ3JpZFNpemUuaGVpZ2h0XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5piv5ZCm5Zyo5Zyw5Zu+5YaFIOWDj+e0oFxyXG4gICAgcHVibGljIGlzSW5NYXBCeVBpeGVsKHBvc2l0aW9uOiBjYy5WZWMyKSB7XHJcbiAgICAgICAgY29uc3QgcG9zID0gcG9zaXRpb24uc3ViKHRoaXMubWFwT3JpZ2luLCB0aGlzLnRlbXBWZWMpXHJcbiAgICAgICAgcmV0dXJuIHBvcy54ID49IDAgJiYgcG9zLnggPCB0aGlzLm1hcFNpemUud2lkdGggJiYgcG9zLnkgPj0gMCAmJiBwb3MueSA8IHRoaXMubWFwU2l6ZS5oZWlnaHRcclxuICAgIH1cclxuXHJcbiAgICAvLyDojrflj5bmnIDov5HnmoTnvZHmoLzngrkg6YCa6L+H5YOP57Sg54K5XHJcbiAgICBwdWJsaWMgZ2V0TGF0ZWx5UG9pbnRCeVBvc2l0aW9uKHBvc2l0aW9uOiBjYy5WZWMyKSB7XHJcbiAgICAgICAgY29uc3QgcG9pbnQgPSB0aGlzLmdldEFjdFBvaW50QnlQaXhlbChwb3NpdGlvbilcclxuICAgICAgICBwb2ludC54ID0gY2MubWlzYy5jbGFtcGYocG9pbnQueCwgMCwgdGhpcy5ncmlkU2l6ZS53aWR0aCAtIDEpXHJcbiAgICAgICAgcG9pbnQueSA9IGNjLm1pc2MuY2xhbXBmKHBvaW50LnksIDAsIHRoaXMuZ3JpZFNpemUuaGVpZ2h0IC0gMSlcclxuICAgICAgICByZXR1cm4gcG9pbnRcclxuICAgIH1cclxuXHJcbiAgICAvLyDov5vlhaXlnLDlm75cclxuICAgIHB1YmxpYyBjcmVhdGVSb2xlKHJvbGU6IFJvbGVPYmosIHBvaW50PzogY2MuVmVjMiwgcG9zaXRpb24/OiBjYy5WZWMyKSB7XHJcbiAgICAgICAgcm9sZS5pbml0TWFwSW5mbyh0aGlzKVxyXG4gICAgICAgIHJvbGUuc2V0UG9pbnRBbmRQb3NpdGlvbihwb2ludCwgcG9zaXRpb24pXHJcbiAgICAgICAgdGhpcy5hZGRSb2xlKHJvbGUpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g6I635Y+W5Li75Yy65Z+fXHJcbiAgICBwdWJsaWMgZ2V0TWFpbkFyZWEoKTogYW55IHsgcmV0dXJuIHRoaXMgfVxyXG5cclxuICAgIC8vIOiOt+WPluWPpuS4gOS4quS7u+aEj+mXqOaJgOWcqOWMuuWfn1xyXG4gICAgcHVibGljIGdldE5leHREb29ySG9tZURhdGEocm9sZTogUm9sZU9iaik6IGFueSB7IHJldHVybiB0aGlzIH1cclxuXHJcbiAgICAvLyDojrflj5blvZPliY3lnLDlm77miYDmnInlj6/ku6XliLDovr7nmoTngrlcclxuICAgIHB1YmxpYyBnZXRDYW5Nb3ZlUG9pbnRzKCk6IGNjLlZlYzJbXSB7IHJldHVybiB0aGlzLmNhbk1vdmVQb2ludHMgfVxyXG5cclxuICAgIC8vIOiOt+WPluaJgOacieiuvuaWvVxyXG4gICAgcHVibGljIGdldEJ1aWxkcygpIHsgcmV0dXJuIFtdIH1cclxuXHJcbiAgICBwdWJsaWMgaXNIYXNCdWlsZEJ5VHlwZSh0eXBlOiBudW1iZXIpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5nZXRCdWlsZHMoKS5zb21lKG0gPT4gbS50eXBlID09PSB0eXBlKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOS9v+eUqOWutuWFt+W8gOWni+eahOaXtuWAmVxyXG4gICAgcHVibGljIHVzZUJ1aWxkQmVnaW4oLi4ucGFyYW1zOiBhbnkpIHsgfVxyXG5cclxuICAgIC8vIOS9v+eUqOS4rVxyXG4gICAgcHVibGljIHVzZUJ1aWxkUnVuKC4uLnBhcmFtczogYW55KSB7IH1cclxuXHJcbiAgICAvLyDkvb/nlKjlrrblhbfnu5PmnZ/nmoTml7blgJlcclxuICAgIHB1YmxpYyB1c2VCdWlsZEVuZCguLi5wYXJhbXM6IGFueSkgeyB9XHJcblxyXG4gICAgLy8g6I635Y+W6K6+5pa955qEcm9vdOS9jee9rlxyXG4gICAgcHVibGljIGdldEJ1aWxkUm9vdFBvc2l0aW9uQnlUeXBlKHR5cGU6IG51bWJlcik6IGNjLlZlYzIgeyByZXR1cm4gbnVsbCB9XHJcblxyXG4gICAgLy8g6L+b5YWl5Zyw5Zu+IOagueaNrueJueauiuS9jee9rlxyXG4gICAgcHVibGljIGVudGVyQnlQb2ludChyb2xlOiBSb2xlT2JqLCBwb2ludDogY2MuVmVjMikge1xyXG4gICAgICAgIHRoaXMuY3JlYXRlUm9sZShyb2xlLCBwb2ludClcclxuICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlJPTEVfRU5URVJfU0NFTkUsIHJvbGUsIHRydWUpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5riF55CG5Li05pe25L+h5oGvXHJcbiAgICBwdWJsaWMgY2xlYW5Sb2xlVGVtcEluZm8ocm9sZTogUm9sZU9iaiwgLi4ucGFyYW1zOiBhbnkpIHsgfVxyXG5cclxuICAgIC8vIOemu+W8gOWcsOWbvlxyXG4gICAgcHVibGljIGxlYXZlKHJvbGU6IFJvbGVPYmopIHtcclxuICAgICAgICB0aGlzLnJlbW92ZVJvbGUocm9sZSlcclxuICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlJPTEVfTEVBVkVfU0NFTkUsIHJvbGUsIDApXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGNoZWNrQ2hhbmdlQXJlYShyb2xlOiBSb2xlT2JqLCB0YXJnZXQ6IGFueSkgeyB9XHJcblxyXG4gICAgLy8g6I635Y+W6Zey6YCb5L2N572uXHJcbiAgICBwdWJsaWMgZ2V0U3Ryb2xsUG9pbnQocm9sZTogUm9sZU9iaikge1xyXG4gICAgICAgIGxldCBwb2ludCA9IHRoaXMuZ2V0Q2FuTW92ZVBvaW50cygpLnJhbmRvbSgpLCBjbnQgPSA1XHJcbiAgICAgICAgZG8ge1xyXG4gICAgICAgICAgICBsZXQgbWFnID0gcG9pbnQuc3ViKHJvbGUucG9pbnQpLm1hZygpXHJcbiAgICAgICAgICAgIC8vIOWcqDPmoLzlkowxMOagvOS5i+WGhVxyXG4gICAgICAgICAgICBpZiAobWFnIDwgNCB8fCBtYWcgPiAxMCkge1xyXG4gICAgICAgICAgICAgICAgcG9pbnQgPSB0aGlzLmdyb3VuZFBvaW50cy5yYW5kb20oKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgYnJlYWtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gd2hpbGUgKC0tY250ID49IDApXHJcbiAgICAgICAgcmV0dXJuIHBvaW50XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldFdvcmtQb2ludChyb2xlOiBSb2xlT2JqKTogY2MuVmVjMiB7IHJldHVybiBudWxsIH1cclxufVxyXG4iXX0=