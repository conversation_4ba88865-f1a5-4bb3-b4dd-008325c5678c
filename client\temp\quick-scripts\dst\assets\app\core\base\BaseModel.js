
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1c92jUY0xJjpWY4P7xfPVi', 'BaseModel');
// app/core/base/BaseModel.ts

"use strict";
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
// 基础数据模型
var BaseModel = /** @class */ (function () {
    function BaseModel(type) {
        this._type = '';
        this._type = type;
    }
    BaseModel.prototype.__create = function () {
        // logger.info(this.constructor['name'] + '(' + this.type + ')' + ' create!')
        this.onCreate();
    };
    BaseModel.prototype.__clean = function () {
        // logger.info(this.constructor['name'] + '(' + this.type + ')' + ' clean!')
        this.onClean();
    };
    BaseModel.prototype.onCreate = function () {
    };
    BaseModel.prototype.onClean = function () {
    };
    BaseModel.prototype.emit = function (type) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        eventCenter.emit.apply(eventCenter, __spreadArrays([type], params));
    };
    BaseModel.prototype.getModel = function (key) {
        return mc.getModel(key);
    };
    return BaseModel;
}());
exports.default = BaseModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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