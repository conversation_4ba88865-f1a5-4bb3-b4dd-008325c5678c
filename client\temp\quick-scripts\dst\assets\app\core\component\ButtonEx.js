
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/ButtonEx.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '356cc80BMlDxpmbLcfx1E6c', 'ButtonEx');
// app/core/component/ButtonEx.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BasePnlCtrl_1 = require("../base/BasePnlCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent, executeInEditMode = _a.executeInEditMode;
var SoundType;
(function (SoundType) {
    SoundType[SoundType["NONE"] = 0] = "NONE";
    SoundType[SoundType["DEFAULT"] = 1] = "DEFAULT";
    SoundType[SoundType["CUSTOM"] = 2] = "CUSTOM";
})(SoundType || (SoundType = {}));
var EventType;
(function (EventType) {
    EventType[EventType["NONE"] = 0] = "NONE";
    EventType[EventType["OPEN_PNL"] = 1] = "OPEN_PNL";
    EventType[EventType["HIDE_PNL"] = 2] = "HIDE_PNL";
    EventType[EventType["GOTO_WIND"] = 3] = "GOTO_WIND";
    EventType[EventType["CUSTOM"] = 4] = "CUSTOM";
})(EventType || (EventType = {}));
var ButtonEx = /** @class */ (function (_super) {
    __extends(ButtonEx, _super);
    function ButtonEx() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.duration = 0.1;
        _this.positionOffset = cc.v2();
        _this.angleOffset = 0;
        // 点击音效
        _this.sound = SoundType.NONE; //音效
        _this.soundPath = ''; //音效路径
        _this.soundVolume = 1.0; //音量
        // 点击事件
        _this.event = EventType.NONE; //事件类型
        _this.eventName = ''; //事件名字
        _this.eventParams = ''; //事件参数
        _this.button = null;
        _this.target = null;
        _this.__originalPosition = null;
        _this.__originalAngle = null;
        _this.isPlayAct = true; // 是否需要播放动作
        _this.isDown = false; // 是否按下
        _this.touchId = -1;
        _this.tempPnl = null;
        _this.__pressedFlag = false;
        return _this;
    }
    ButtonEx_1 = ButtonEx;
    ButtonEx.prototype.onLoad = function () {
        this.button = this.getComponent(cc.Button);
        this.target = this.button.target ? this.button.target : this.node;
        this.isPlayAct = !this.positionOffset.equals(cc.Vec2.ZERO);
        // 监听事件
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    };
    Object.defineProperty(ButtonEx.prototype, "originalPosition", {
        get: function () {
            if (!this.__originalPosition) {
                this.__originalPosition = this.target.getPosition();
            }
            return this.__originalPosition;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ButtonEx.prototype, "originalAngle", {
        get: function () {
            if (this.__originalAngle === null) {
                this.__originalAngle = this.target.angle;
            }
            return this.__originalAngle;
        },
        enumerable: false,
        configurable: true
    });
    ButtonEx.prototype.onEnable = function () {
        if (!CC_EDITOR && this.isPlayAct) {
            this.target.stopAllActions();
            if (this.__originalPosition) {
                this.target.setPosition(this.__originalPosition);
            }
            if (this.__originalAngle !== null) {
                this.target.angle = this.__originalAngle;
            }
        }
    };
    Object.defineProperty(ButtonEx.prototype, "interactable", {
        get: function () {
            return this.button.interactable;
        },
        // 是否可点击
        set: function (val) {
            this.button.interactable = val;
        },
        enumerable: false,
        configurable: true
    });
    ButtonEx.prototype.setPositionOffset = function (pos) {
        this.positionOffset.set(pos);
    };
    ButtonEx.prototype.getPositionOffset = function () {
        return this.positionOffset;
    };
    // 触摸开始
    ButtonEx.prototype.onTouchStart = function (event) {
        if (!this.interactable || this.touchId !== -1) {
            return;
        }
        this.touchId = event.getID();
        this.down();
    };
    // 触摸移动
    ButtonEx.prototype.onTouchMove = function (event) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return;
        }
        var hit = this.node._hitTest(event.getLocation());
        if (hit) {
            if (!this.isDown) {
                this.down();
            }
        }
        else {
            if (this.isDown) {
                this.restore();
            }
        }
    };
    // 触摸结束
    ButtonEx.prototype.onTouchEnd = function (event) {
        var _this = this;
        if (this.__pressedFlag || !this.interactable || this.touchId !== event.getID()) {
            return;
        }
        this.__pressedFlag = true;
        this.touchId = -1;
        this.restore();
        // 播放音效
        this.playSound();
        // 发送事件
        this.emit();
        // 防止一帧同时点击多次
        ut.waitNextFrame().then(function () { return _this.isValid && (_this.__pressedFlag = false); });
    };
    // 触摸取消
    ButtonEx.prototype.onTouchCancel = function (event) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        this.restore();
    };
    // 按下
    ButtonEx.prototype.down = function () {
        this.isDown = true;
        if (this.isPlayAct) {
            this.target.setPosition(this.originalPosition);
            this.target.angle = this.originalAngle;
            cc.tween(this.target).by(this.duration, { x: this.positionOffset.x, y: this.positionOffset.y, angle: this.angleOffset }).start();
        }
    };
    // 还原
    ButtonEx.prototype.restore = function () {
        this.isDown = false;
        if (this.isPlayAct) {
            cc.tween(this.target).to(this.duration, { x: this.originalPosition.x, y: this.originalPosition.y, angle: this.originalAngle }).start();
        }
    };
    // 播放声音
    ButtonEx.prototype.playSound = function () {
        if (this.sound === SoundType.NONE) {
            return;
        }
        var url = this.sound === SoundType.DEFAULT ? ButtonEx_1.DefaultClickPath : this.soundPath;
        if (url) {
            audioMgr.playSFX(url, { volume: this.soundVolume });
        }
    };
    // 发送事件
    ButtonEx.prototype.emit = function () {
        if (this.event === EventType.NONE) {
            return;
        }
        if (this.event === EventType.HIDE_PNL) {
            if (this.eventParams) {
                return eventCenter.emit(CoreEventType_1.default.HIDE_PNL, this.eventParams);
            }
            if (this.tempPnl) {
                return eventCenter.emit(CoreEventType_1.default.HIDE_PNL, this.tempPnl);
            }
            var node = this.node;
            this.tempPnl = node.getComponent(BasePnlCtrl_1.default);
            while (!this.tempPnl) {
                node = node.parent;
                this.tempPnl = node && node.getComponent(BasePnlCtrl_1.default);
            }
            if (this.tempPnl) {
                eventCenter.emit(CoreEventType_1.default.HIDE_PNL, this.tempPnl);
            }
            else {
                logger.error('button event [HIDE_PNL] not pnl?');
            }
        }
        else if (this.event === EventType.CUSTOM) {
            eventCenter.emit(this.eventName, this.eventParams || this.target);
        }
        else if (this.event === EventType.OPEN_PNL) {
            eventCenter.emit(CoreEventType_1.default.OPEN_PNL, this.eventParams);
        }
        else if (this.event === EventType.GOTO_WIND) {
            eventCenter.emit(CoreEventType_1.default.GOTO_WIND, this.eventParams);
        }
        else {
            logger.error("button event not " + this.event);
        }
    };
    var ButtonEx_1;
    ButtonEx.DefaultClickPath = ''; // 默认点击音效
    __decorate([
        property({ range: [0, 10] })
    ], ButtonEx.prototype, "duration", void 0);
    __decorate([
        property(cc.Vec2)
    ], ButtonEx.prototype, "positionOffset", void 0);
    __decorate([
        property()
    ], ButtonEx.prototype, "angleOffset", void 0);
    __decorate([
        property({ type: cc.Enum(SoundType) })
    ], ButtonEx.prototype, "sound", void 0);
    __decorate([
        property({ visible: function () { return this.sound === SoundType.CUSTOM; } })
    ], ButtonEx.prototype, "soundPath", void 0);
    __decorate([
        property({ visible: function () { return this.sound === SoundType.CUSTOM; } })
    ], ButtonEx.prototype, "soundVolume", void 0);
    __decorate([
        property({ type: cc.Enum(EventType) })
    ], ButtonEx.prototype, "event", void 0);
    __decorate([
        property({ visible: function () { return this.event === EventType.CUSTOM; } })
    ], ButtonEx.prototype, "eventName", void 0);
    __decorate([
        property({ visible: function () { return this.event !== EventType.NONE; } })
    ], ButtonEx.prototype, "eventParams", void 0);
    ButtonEx = ButtonEx_1 = __decorate([
        ccclass,
        executeInEditMode,
        requireComponent(cc.Button),
        menu('自定义组件/ButtonEx')
    ], ButtonEx);
    return ButtonEx;
}(cc.Component));
exports.default = ButtonEx;
cc.ButtonEx = ButtonEx;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxjb21wb25lbnRcXEJ1dHRvbkV4LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG1EQUE4QztBQUM5Qyx3REFBbUQ7QUFFN0MsSUFBQSxLQUFtRSxFQUFFLENBQUMsVUFBVSxFQUE5RSxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQUEsRUFBRSxJQUFJLFVBQUEsRUFBRSxnQkFBZ0Isc0JBQUEsRUFBRSxpQkFBaUIsdUJBQWtCLENBQUM7QUFFdkYsSUFBSyxTQUlKO0FBSkQsV0FBSyxTQUFTO0lBQ1YseUNBQUksQ0FBQTtJQUNKLCtDQUFPLENBQUE7SUFDUCw2Q0FBTSxDQUFBO0FBQ1YsQ0FBQyxFQUpJLFNBQVMsS0FBVCxTQUFTLFFBSWI7QUFFRCxJQUFLLFNBTUo7QUFORCxXQUFLLFNBQVM7SUFDVix5Q0FBSSxDQUFBO0lBQ0osaURBQVEsQ0FBQTtJQUNSLGlEQUFRLENBQUE7SUFDUixtREFBUyxDQUFBO0lBQ1QsNkNBQU0sQ0FBQTtBQUNWLENBQUMsRUFOSSxTQUFTLEtBQVQsU0FBUyxRQU1iO0FBTUQ7SUFBc0MsNEJBQVk7SUFBbEQ7UUFBQSxxRUE2TUM7UUExTVcsY0FBUSxHQUFXLEdBQUcsQ0FBQTtRQUV0QixvQkFBYyxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUVqQyxpQkFBVyxHQUFXLENBQUMsQ0FBQTtRQUUvQixPQUFPO1FBRUMsV0FBSyxHQUFjLFNBQVMsQ0FBQyxJQUFJLENBQUEsQ0FBQyxJQUFJO1FBRXRDLGVBQVMsR0FBVyxFQUFFLENBQUEsQ0FBQyxNQUFNO1FBRTdCLGlCQUFXLEdBQVcsR0FBRyxDQUFBLENBQUMsSUFBSTtRQUV0QyxPQUFPO1FBRUMsV0FBSyxHQUFjLFNBQVMsQ0FBQyxJQUFJLENBQUEsQ0FBQyxNQUFNO1FBRXhDLGVBQVMsR0FBVyxFQUFFLENBQUEsQ0FBQyxNQUFNO1FBRTdCLGlCQUFXLEdBQVcsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUUvQixZQUFNLEdBQWMsSUFBSSxDQUFBO1FBQ3hCLFlBQU0sR0FBWSxJQUFJLENBQUE7UUFDdEIsd0JBQWtCLEdBQVksSUFBSSxDQUFBO1FBQ2xDLHFCQUFlLEdBQVcsSUFBSSxDQUFBO1FBRTlCLGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQSxXQUFXO1FBQ3BDLFlBQU0sR0FBWSxLQUFLLENBQUEsQ0FBQSxPQUFPO1FBQzlCLGFBQU8sR0FBVyxDQUFDLENBQUMsQ0FBQTtRQUVwQixhQUFPLEdBQWdCLElBQUksQ0FBQTtRQUkzQixtQkFBYSxHQUFZLEtBQUssQ0FBQTs7SUF1SzFDLENBQUM7aUJBN01vQixRQUFRO0lBd0N6Qix5QkFBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUMxQyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQTtRQUNqRSxJQUFJLENBQUMsU0FBUyxHQUFHLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUMxRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDcEUsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDbEUsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDaEUsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDMUUsQ0FBQztJQUVELHNCQUFXLHNDQUFnQjthQUEzQjtZQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsa0JBQWtCLEVBQUU7Z0JBQzFCLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxDQUFBO2FBQ3REO1lBQ0QsT0FBTyxJQUFJLENBQUMsa0JBQWtCLENBQUE7UUFDbEMsQ0FBQzs7O09BQUE7SUFFRCxzQkFBVyxtQ0FBYTthQUF4QjtZQUNJLElBQUksSUFBSSxDQUFDLGVBQWUsS0FBSyxJQUFJLEVBQUU7Z0JBQy9CLElBQUksQ0FBQyxlQUFlLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUE7YUFDM0M7WUFDRCxPQUFPLElBQUksQ0FBQyxlQUFlLENBQUE7UUFDL0IsQ0FBQzs7O09BQUE7SUFFRCwyQkFBUSxHQUFSO1FBQ0ksSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQzlCLElBQUksQ0FBQyxNQUFNLENBQUMsY0FBYyxFQUFFLENBQUE7WUFDNUIsSUFBSSxJQUFJLENBQUMsa0JBQWtCLEVBQUU7Z0JBQ3pCLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFBO2FBQ25EO1lBQ0QsSUFBSSxJQUFJLENBQUMsZUFBZSxLQUFLLElBQUksRUFBRTtnQkFDL0IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQTthQUMzQztTQUNKO0lBQ0wsQ0FBQztJQUdELHNCQUFXLGtDQUFZO2FBR3ZCO1lBQ0ksT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQTtRQUNuQyxDQUFDO1FBTkQsUUFBUTthQUNSLFVBQXdCLEdBQVk7WUFDaEMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLEdBQUcsR0FBRyxDQUFBO1FBQ2xDLENBQUM7OztPQUFBO0lBS00sb0NBQWlCLEdBQXhCLFVBQXlCLEdBQVk7UUFDakMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDaEMsQ0FBQztJQUNNLG9DQUFpQixHQUF4QjtRQUNJLE9BQU8sSUFBSSxDQUFDLGNBQWMsQ0FBQTtJQUM5QixDQUFDO0lBRUQsT0FBTztJQUNDLCtCQUFZLEdBQXBCLFVBQXFCLEtBQTBCO1FBQzNDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssQ0FBQyxDQUFDLEVBQUU7WUFDM0MsT0FBTTtTQUNUO1FBQ0QsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUMsS0FBSyxFQUFFLENBQUE7UUFDNUIsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO0lBQ2YsQ0FBQztJQUVELE9BQU87SUFDQyw4QkFBVyxHQUFuQixVQUFvQixLQUEwQjtRQUMxQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLENBQUMsT0FBTyxLQUFLLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRTtZQUN0RCxPQUFNO1NBQ1Q7UUFDRCxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQTtRQUNuRCxJQUFJLEdBQUcsRUFBRTtZQUNMLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFO2dCQUNkLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTthQUNkO1NBQ0o7YUFBTTtZQUNILElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRTtnQkFDYixJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7YUFDakI7U0FDSjtJQUNMLENBQUM7SUFFRCxPQUFPO0lBQ0MsNkJBQVUsR0FBbEIsVUFBbUIsS0FBMEI7UUFBN0MsaUJBYUM7UUFaRyxJQUFJLElBQUksQ0FBQyxhQUFhLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQzVFLE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFBO1FBQ3pCLElBQUksQ0FBQyxPQUFPLEdBQUcsQ0FBQyxDQUFDLENBQUE7UUFDakIsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO1FBQ2QsT0FBTztRQUNQLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQTtRQUNoQixPQUFPO1FBQ1AsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1FBQ1gsYUFBYTtRQUNiLEVBQUUsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxJQUFJLENBQUMsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxLQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQyxFQUE1QyxDQUE0QyxDQUFDLENBQUE7SUFDL0UsQ0FBQztJQUVELE9BQU87SUFDQyxnQ0FBYSxHQUFyQixVQUFzQixLQUEwQjtRQUM1QyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksSUFBSSxJQUFJLENBQUMsT0FBTyxLQUFLLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRTtZQUN0RCxPQUFNO1NBQ1Q7UUFDRCxJQUFJLENBQUMsT0FBTyxHQUFHLENBQUMsQ0FBQyxDQUFBO1FBQ2pCLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtJQUNsQixDQUFDO0lBRUQsS0FBSztJQUNHLHVCQUFJLEdBQVo7UUFDSSxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtRQUNsQixJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDaEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUE7WUFDOUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQTtZQUN0QyxFQUFFLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFBO1NBQ25JO0lBQ0wsQ0FBQztJQUVELEtBQUs7SUFDRywwQkFBTyxHQUFmO1FBQ0ksSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7UUFDbkIsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ2hCLEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFBO1NBQ3pJO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyw0QkFBUyxHQUFqQjtRQUNJLElBQUksSUFBSSxDQUFDLEtBQUssS0FBSyxTQUFTLENBQUMsSUFBSSxFQUFFO1lBQy9CLE9BQU07U0FDVDtRQUNELElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsVUFBUSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFBO1FBQ3ZGLElBQUksR0FBRyxFQUFFO1lBQ0wsUUFBUSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUE7U0FDdEQ7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLHVCQUFJLEdBQVo7UUFDSSxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLElBQUksRUFBRTtZQUMvQixPQUFNO1NBQ1Q7UUFDRCxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLFFBQVEsRUFBRTtZQUNuQyxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7Z0JBQ2xCLE9BQU8sV0FBVyxDQUFDLElBQUksQ0FBQyx1QkFBYSxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7YUFDcEU7WUFDRCxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7Z0JBQ2QsT0FBTyxXQUFXLENBQUMsSUFBSSxDQUFDLHVCQUFhLENBQUMsUUFBUSxFQUFFLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQTthQUNoRTtZQUNELElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUE7WUFDcEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLHFCQUFXLENBQUMsQ0FBQTtZQUM3QyxPQUFPLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRTtnQkFDbEIsSUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7Z0JBQ2xCLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxJQUFJLElBQUksQ0FBQyxZQUFZLENBQUMscUJBQVcsQ0FBQyxDQUFBO2FBQ3hEO1lBQ0QsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFO2dCQUNkLFdBQVcsQ0FBQyxJQUFJLENBQUMsdUJBQWEsQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFBO2FBQ3pEO2lCQUFNO2dCQUNILE1BQU0sQ0FBQyxLQUFLLENBQUMsa0NBQWtDLENBQUMsQ0FBQTthQUNuRDtTQUNKO2FBQU0sSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLFNBQVMsQ0FBQyxNQUFNLEVBQUU7WUFDeEMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxXQUFXLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1NBQ3BFO2FBQU0sSUFBSSxJQUFJLENBQUMsS0FBSyxLQUFLLFNBQVMsQ0FBQyxRQUFRLEVBQUU7WUFDMUMsV0FBVyxDQUFDLElBQUksQ0FBQyx1QkFBYSxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUE7U0FDN0Q7YUFBTSxJQUFJLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLFNBQVMsRUFBRTtZQUMzQyxXQUFXLENBQUMsSUFBSSxDQUFDLHVCQUFhLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTtTQUM5RDthQUFNO1lBQ0gsTUFBTSxDQUFDLEtBQUssQ0FBQyxzQkFBb0IsSUFBSSxDQUFDLEtBQU8sQ0FBQyxDQUFBO1NBQ2pEO0lBQ0wsQ0FBQzs7SUF4S2EseUJBQWdCLEdBQVcsRUFBRSxDQUFBLENBQUEsU0FBUztJQWpDcEQ7UUFEQyxRQUFRLENBQUMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQzs4Q0FDQztJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO29EQUN1QjtJQUV6QztRQURDLFFBQVEsRUFBRTtpREFDb0I7SUFJL0I7UUFEQyxRQUFRLENBQUMsRUFBRSxJQUFJLEVBQUUsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDOzJDQUNFO0lBRXpDO1FBREMsUUFBUSxDQUFDLEVBQUUsT0FBTyxFQUFFLGNBQWMsT0FBTyxJQUFJLENBQUMsS0FBSyxLQUFLLFNBQVMsQ0FBQyxNQUFNLENBQUEsQ0FBQyxDQUFDLEVBQUUsQ0FBQzsrQ0FDaEQ7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxPQUFPLEVBQUUsY0FBYyxPQUFPLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLE1BQU0sQ0FBQSxDQUFDLENBQUMsRUFBRSxDQUFDO2lEQUM3QztJQUlqQztRQURDLFFBQVEsQ0FBQyxFQUFFLElBQUksRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUM7MkNBQ0U7SUFFekM7UUFEQyxRQUFRLENBQUMsRUFBRSxPQUFPLEVBQUUsY0FBYyxPQUFPLElBQUksQ0FBQyxLQUFLLEtBQUssU0FBUyxDQUFDLE1BQU0sQ0FBQSxDQUFDLENBQUMsRUFBRSxDQUFDOytDQUNoRDtJQUU5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLE9BQU8sRUFBRSxjQUFjLE9BQU8sSUFBSSxDQUFDLEtBQUssS0FBSyxTQUFTLENBQUMsSUFBSSxDQUFBLENBQUMsQ0FBQyxFQUFFLENBQUM7aURBQzVDO0lBdkJmLFFBQVE7UUFKNUIsT0FBTztRQUNQLGlCQUFpQjtRQUNqQixnQkFBZ0IsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO1FBQzNCLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQztPQUNGLFFBQVEsQ0E2TTVCO0lBQUQsZUFBQztDQTdNRCxBQTZNQyxDQTdNcUMsRUFBRSxDQUFDLFNBQVMsR0E2TWpEO2tCQTdNb0IsUUFBUTtBQStNN0IsRUFBRSxDQUFDLFFBQVEsR0FBRyxRQUFRLENBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZVBubEN0cmwgZnJvbSBcIi4uL2Jhc2UvQmFzZVBubEN0cmxcIjtcbmltcG9ydCBDb3JlRXZlbnRUeXBlIGZyb20gXCIuLi9ldmVudC9Db3JlRXZlbnRUeXBlXCI7XG5cbmNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHksIG1lbnUsIHJlcXVpcmVDb21wb25lbnQsIGV4ZWN1dGVJbkVkaXRNb2RlIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5lbnVtIFNvdW5kVHlwZSB7XG4gICAgTk9ORSxcbiAgICBERUZBVUxULFxuICAgIENVU1RPTSxcbn1cblxuZW51bSBFdmVudFR5cGUge1xuICAgIE5PTkUsXG4gICAgT1BFTl9QTkwsXG4gICAgSElERV9QTkwsXG4gICAgR09UT19XSU5ELFxuICAgIENVU1RPTSxcbn1cblxuQGNjY2xhc3NcbkBleGVjdXRlSW5FZGl0TW9kZVxuQHJlcXVpcmVDb21wb25lbnQoY2MuQnV0dG9uKVxuQG1lbnUoJ+iHquWumuS5iee7hOS7ti9CdXR0b25FeCcpXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBCdXR0b25FeCBleHRlbmRzIGNjLkNvbXBvbmVudCB7XG5cbiAgICBAcHJvcGVydHkoeyByYW5nZTogWzAsIDEwXSB9KVxuICAgIHByaXZhdGUgZHVyYXRpb246IG51bWJlciA9IDAuMVxuICAgIEBwcm9wZXJ0eShjYy5WZWMyKVxuICAgIHByaXZhdGUgcG9zaXRpb25PZmZzZXQ6IGNjLlZlYzIgPSBjYy52MigpXG4gICAgQHByb3BlcnR5KClcbiAgICBwcml2YXRlIGFuZ2xlT2Zmc2V0OiBudW1iZXIgPSAwXG5cbiAgICAvLyDngrnlh7vpn7PmlYhcbiAgICBAcHJvcGVydHkoeyB0eXBlOiBjYy5FbnVtKFNvdW5kVHlwZSkgfSlcbiAgICBwcml2YXRlIHNvdW5kOiBTb3VuZFR5cGUgPSBTb3VuZFR5cGUuTk9ORSAvL+mfs+aViFxuICAgIEBwcm9wZXJ0eSh7IHZpc2libGU6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXMuc291bmQgPT09IFNvdW5kVHlwZS5DVVNUT00gfSB9KVxuICAgIHByaXZhdGUgc291bmRQYXRoOiBzdHJpbmcgPSAnJyAvL+mfs+aViOi3r+W+hFxuICAgIEBwcm9wZXJ0eSh7IHZpc2libGU6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXMuc291bmQgPT09IFNvdW5kVHlwZS5DVVNUT00gfSB9KVxuICAgIHByaXZhdGUgc291bmRWb2x1bWU6IG51bWJlciA9IDEuMCAvL+mfs+mHj1xuXG4gICAgLy8g54K55Ye75LqL5Lu2XG4gICAgQHByb3BlcnR5KHsgdHlwZTogY2MuRW51bShFdmVudFR5cGUpIH0pXG4gICAgcHJpdmF0ZSBldmVudDogRXZlbnRUeXBlID0gRXZlbnRUeXBlLk5PTkUgLy/kuovku7bnsbvlnotcbiAgICBAcHJvcGVydHkoeyB2aXNpYmxlOiBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzLmV2ZW50ID09PSBFdmVudFR5cGUuQ1VTVE9NIH0gfSlcbiAgICBwcml2YXRlIGV2ZW50TmFtZTogc3RyaW5nID0gJycgLy/kuovku7blkI3lrZdcbiAgICBAcHJvcGVydHkoeyB2aXNpYmxlOiBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzLmV2ZW50ICE9PSBFdmVudFR5cGUuTk9ORSB9IH0pXG4gICAgcHJpdmF0ZSBldmVudFBhcmFtczogc3RyaW5nID0gJycgLy/kuovku7blj4LmlbBcblxuICAgIHByaXZhdGUgYnV0dG9uOiBjYy5CdXR0b24gPSBudWxsXG4gICAgcHJpdmF0ZSB0YXJnZXQ6IGNjLk5vZGUgPSBudWxsXG4gICAgcHJpdmF0ZSBfX29yaWdpbmFsUG9zaXRpb246IGNjLlZlYzIgPSBudWxsXG4gICAgcHJpdmF0ZSBfX29yaWdpbmFsQW5nbGU6IG51bWJlciA9IG51bGxcblxuICAgIHByaXZhdGUgaXNQbGF5QWN0OiBib29sZWFuID0gdHJ1ZS8vIOaYr+WQpumcgOimgeaSreaUvuWKqOS9nFxuICAgIHByaXZhdGUgaXNEb3duOiBib29sZWFuID0gZmFsc2UvLyDmmK/lkKbmjInkuItcbiAgICBwcml2YXRlIHRvdWNoSWQ6IG51bWJlciA9IC0xXG5cbiAgICBwcml2YXRlIHRlbXBQbmw6IEJhc2VQbmxDdHJsID0gbnVsbFxuXG4gICAgcHVibGljIHN0YXRpYyBEZWZhdWx0Q2xpY2tQYXRoOiBzdHJpbmcgPSAnJy8vIOm7mOiupOeCueWHu+mfs+aViFxuXG4gICAgcHJpdmF0ZSBfX3ByZXNzZWRGbGFnOiBib29sZWFuID0gZmFsc2VcblxuICAgIG9uTG9hZCgpIHtcbiAgICAgICAgdGhpcy5idXR0b24gPSB0aGlzLmdldENvbXBvbmVudChjYy5CdXR0b24pXG4gICAgICAgIHRoaXMudGFyZ2V0ID0gdGhpcy5idXR0b24udGFyZ2V0ID8gdGhpcy5idXR0b24udGFyZ2V0IDogdGhpcy5ub2RlXG4gICAgICAgIHRoaXMuaXNQbGF5QWN0ID0gIXRoaXMucG9zaXRpb25PZmZzZXQuZXF1YWxzKGNjLlZlYzIuWkVSTylcbiAgICAgICAgLy8g55uR5ZCs5LqL5Lu2XG4gICAgICAgIHRoaXMubm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9TVEFSVCwgdGhpcy5vblRvdWNoU3RhcnQsIHRoaXMpXG4gICAgICAgIHRoaXMubm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9NT1ZFLCB0aGlzLm9uVG91Y2hNb3ZlLCB0aGlzKVxuICAgICAgICB0aGlzLm5vZGUub24oY2MuTm9kZS5FdmVudFR5cGUuVE9VQ0hfRU5ELCB0aGlzLm9uVG91Y2hFbmQsIHRoaXMpXG4gICAgICAgIHRoaXMubm9kZS5vbihjYy5Ob2RlLkV2ZW50VHlwZS5UT1VDSF9DQU5DRUwsIHRoaXMub25Ub3VjaENhbmNlbCwgdGhpcylcbiAgICB9XG5cbiAgICBwdWJsaWMgZ2V0IG9yaWdpbmFsUG9zaXRpb24oKSB7XG4gICAgICAgIGlmICghdGhpcy5fX29yaWdpbmFsUG9zaXRpb24pIHtcbiAgICAgICAgICAgIHRoaXMuX19vcmlnaW5hbFBvc2l0aW9uID0gdGhpcy50YXJnZXQuZ2V0UG9zaXRpb24oKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLl9fb3JpZ2luYWxQb3NpdGlvblxuICAgIH1cblxuICAgIHB1YmxpYyBnZXQgb3JpZ2luYWxBbmdsZSgpIHtcbiAgICAgICAgaWYgKHRoaXMuX19vcmlnaW5hbEFuZ2xlID09PSBudWxsKSB7XG4gICAgICAgICAgICB0aGlzLl9fb3JpZ2luYWxBbmdsZSA9IHRoaXMudGFyZ2V0LmFuZ2xlXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuX19vcmlnaW5hbEFuZ2xlXG4gICAgfVxuXG4gICAgb25FbmFibGUoKSB7XG4gICAgICAgIGlmICghQ0NfRURJVE9SICYmIHRoaXMuaXNQbGF5QWN0KSB7XG4gICAgICAgICAgICB0aGlzLnRhcmdldC5zdG9wQWxsQWN0aW9ucygpXG4gICAgICAgICAgICBpZiAodGhpcy5fX29yaWdpbmFsUG9zaXRpb24pIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRhcmdldC5zZXRQb3NpdGlvbih0aGlzLl9fb3JpZ2luYWxQb3NpdGlvbilcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLl9fb3JpZ2luYWxBbmdsZSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0LmFuZ2xlID0gdGhpcy5fX29yaWdpbmFsQW5nbGVcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaYr+WQpuWPr+eCueWHu1xuICAgIHB1YmxpYyBzZXQgaW50ZXJhY3RhYmxlKHZhbDogYm9vbGVhbikge1xuICAgICAgICB0aGlzLmJ1dHRvbi5pbnRlcmFjdGFibGUgPSB2YWxcbiAgICB9XG4gICAgcHVibGljIGdldCBpbnRlcmFjdGFibGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmJ1dHRvbi5pbnRlcmFjdGFibGVcbiAgICB9XG5cbiAgICBwdWJsaWMgc2V0UG9zaXRpb25PZmZzZXQocG9zOiBjYy5WZWMyKSB7XG4gICAgICAgIHRoaXMucG9zaXRpb25PZmZzZXQuc2V0KHBvcylcbiAgICB9XG4gICAgcHVibGljIGdldFBvc2l0aW9uT2Zmc2V0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wb3NpdGlvbk9mZnNldFxuICAgIH1cblxuICAgIC8vIOinpuaRuOW8gOWni1xuICAgIHByaXZhdGUgb25Ub3VjaFN0YXJ0KGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoKSB7XG4gICAgICAgIGlmICghdGhpcy5pbnRlcmFjdGFibGUgfHwgdGhpcy50b3VjaElkICE9PSAtMSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy50b3VjaElkID0gZXZlbnQuZ2V0SUQoKVxuICAgICAgICB0aGlzLmRvd24oKVxuICAgIH1cblxuICAgIC8vIOinpuaRuOenu+WKqFxuICAgIHByaXZhdGUgb25Ub3VjaE1vdmUoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpIHtcbiAgICAgICAgaWYgKCF0aGlzLmludGVyYWN0YWJsZSB8fCB0aGlzLnRvdWNoSWQgIT09IGV2ZW50LmdldElEKCkpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGhpdCA9IHRoaXMubm9kZS5faGl0VGVzdChldmVudC5nZXRMb2NhdGlvbigpKVxuICAgICAgICBpZiAoaGl0KSB7XG4gICAgICAgICAgICBpZiAoIXRoaXMuaXNEb3duKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5kb3duKClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmlzRG93bikge1xuICAgICAgICAgICAgICAgIHRoaXMucmVzdG9yZSgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDop6bmkbjnu5PmnZ9cbiAgICBwcml2YXRlIG9uVG91Y2hFbmQoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gpIHtcbiAgICAgICAgaWYgKHRoaXMuX19wcmVzc2VkRmxhZyB8fCAhdGhpcy5pbnRlcmFjdGFibGUgfHwgdGhpcy50b3VjaElkICE9PSBldmVudC5nZXRJRCgpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9fcHJlc3NlZEZsYWcgPSB0cnVlXG4gICAgICAgIHRoaXMudG91Y2hJZCA9IC0xXG4gICAgICAgIHRoaXMucmVzdG9yZSgpXG4gICAgICAgIC8vIOaSreaUvumfs+aViFxuICAgICAgICB0aGlzLnBsYXlTb3VuZCgpXG4gICAgICAgIC8vIOWPkemAgeS6i+S7tlxuICAgICAgICB0aGlzLmVtaXQoKVxuICAgICAgICAvLyDpmLLmraLkuIDluKflkIzml7bngrnlh7vlpJrmrKFcbiAgICAgICAgdXQud2FpdE5leHRGcmFtZSgpLnRoZW4oKCkgPT4gdGhpcy5pc1ZhbGlkICYmICh0aGlzLl9fcHJlc3NlZEZsYWcgPSBmYWxzZSkpXG4gICAgfVxuXG4gICAgLy8g6Kem5pG45Y+W5raIXG4gICAgcHJpdmF0ZSBvblRvdWNoQ2FuY2VsKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoKSB7XG4gICAgICAgIGlmICghdGhpcy5pbnRlcmFjdGFibGUgfHwgdGhpcy50b3VjaElkICE9PSBldmVudC5nZXRJRCgpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnRvdWNoSWQgPSAtMVxuICAgICAgICB0aGlzLnJlc3RvcmUoKVxuICAgIH1cblxuICAgIC8vIOaMieS4i1xuICAgIHByaXZhdGUgZG93bigpIHtcbiAgICAgICAgdGhpcy5pc0Rvd24gPSB0cnVlXG4gICAgICAgIGlmICh0aGlzLmlzUGxheUFjdCkge1xuICAgICAgICAgICAgdGhpcy50YXJnZXQuc2V0UG9zaXRpb24odGhpcy5vcmlnaW5hbFBvc2l0aW9uKVxuICAgICAgICAgICAgdGhpcy50YXJnZXQuYW5nbGUgPSB0aGlzLm9yaWdpbmFsQW5nbGVcbiAgICAgICAgICAgIGNjLnR3ZWVuKHRoaXMudGFyZ2V0KS5ieSh0aGlzLmR1cmF0aW9uLCB7IHg6IHRoaXMucG9zaXRpb25PZmZzZXQueCwgeTogdGhpcy5wb3NpdGlvbk9mZnNldC55LCBhbmdsZTogdGhpcy5hbmdsZU9mZnNldCB9KS5zdGFydCgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDov5jljp9cbiAgICBwcml2YXRlIHJlc3RvcmUoKSB7XG4gICAgICAgIHRoaXMuaXNEb3duID0gZmFsc2VcbiAgICAgICAgaWYgKHRoaXMuaXNQbGF5QWN0KSB7XG4gICAgICAgICAgICBjYy50d2Vlbih0aGlzLnRhcmdldCkudG8odGhpcy5kdXJhdGlvbiwgeyB4OiB0aGlzLm9yaWdpbmFsUG9zaXRpb24ueCwgeTogdGhpcy5vcmlnaW5hbFBvc2l0aW9uLnksIGFuZ2xlOiB0aGlzLm9yaWdpbmFsQW5nbGUgfSkuc3RhcnQoKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5pKt5pS+5aOw6Z+zXG4gICAgcHJpdmF0ZSBwbGF5U291bmQoKSB7XG4gICAgICAgIGlmICh0aGlzLnNvdW5kID09PSBTb3VuZFR5cGUuTk9ORSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgbGV0IHVybCA9IHRoaXMuc291bmQgPT09IFNvdW5kVHlwZS5ERUZBVUxUID8gQnV0dG9uRXguRGVmYXVsdENsaWNrUGF0aCA6IHRoaXMuc291bmRQYXRoXG4gICAgICAgIGlmICh1cmwpIHtcbiAgICAgICAgICAgIGF1ZGlvTWdyLnBsYXlTRlgodXJsLCB7IHZvbHVtZTogdGhpcy5zb3VuZFZvbHVtZSB9KVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Y+R6YCB5LqL5Lu2XG4gICAgcHJpdmF0ZSBlbWl0KCkge1xuICAgICAgICBpZiAodGhpcy5ldmVudCA9PT0gRXZlbnRUeXBlLk5PTkUpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmV2ZW50ID09PSBFdmVudFR5cGUuSElERV9QTkwpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLmV2ZW50UGFyYW1zKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGV2ZW50Q2VudGVyLmVtaXQoQ29yZUV2ZW50VHlwZS5ISURFX1BOTCwgdGhpcy5ldmVudFBhcmFtcylcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICh0aGlzLnRlbXBQbmwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXZlbnRDZW50ZXIuZW1pdChDb3JlRXZlbnRUeXBlLkhJREVfUE5MLCB0aGlzLnRlbXBQbmwpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgbm9kZSA9IHRoaXMubm9kZVxuICAgICAgICAgICAgdGhpcy50ZW1wUG5sID0gbm9kZS5nZXRDb21wb25lbnQoQmFzZVBubEN0cmwpXG4gICAgICAgICAgICB3aGlsZSAoIXRoaXMudGVtcFBubCkge1xuICAgICAgICAgICAgICAgIG5vZGUgPSBub2RlLnBhcmVudFxuICAgICAgICAgICAgICAgIHRoaXMudGVtcFBubCA9IG5vZGUgJiYgbm9kZS5nZXRDb21wb25lbnQoQmFzZVBubEN0cmwpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAodGhpcy50ZW1wUG5sKSB7XG4gICAgICAgICAgICAgICAgZXZlbnRDZW50ZXIuZW1pdChDb3JlRXZlbnRUeXBlLkhJREVfUE5MLCB0aGlzLnRlbXBQbmwpXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGxvZ2dlci5lcnJvcignYnV0dG9uIGV2ZW50IFtISURFX1BOTF0gbm90IHBubD8nKVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZXZlbnQgPT09IEV2ZW50VHlwZS5DVVNUT00pIHtcbiAgICAgICAgICAgIGV2ZW50Q2VudGVyLmVtaXQodGhpcy5ldmVudE5hbWUsIHRoaXMuZXZlbnRQYXJhbXMgfHwgdGhpcy50YXJnZXQpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5ldmVudCA9PT0gRXZlbnRUeXBlLk9QRU5fUE5MKSB7XG4gICAgICAgICAgICBldmVudENlbnRlci5lbWl0KENvcmVFdmVudFR5cGUuT1BFTl9QTkwsIHRoaXMuZXZlbnRQYXJhbXMpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5ldmVudCA9PT0gRXZlbnRUeXBlLkdPVE9fV0lORCkge1xuICAgICAgICAgICAgZXZlbnRDZW50ZXIuZW1pdChDb3JlRXZlbnRUeXBlLkdPVE9fV0lORCwgdGhpcy5ldmVudFBhcmFtcylcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGxvZ2dlci5lcnJvcihgYnV0dG9uIGV2ZW50IG5vdCAke3RoaXMuZXZlbnR9YClcbiAgICAgICAgfVxuICAgIH1cbn1cblxuY2MuQnV0dG9uRXggPSBCdXR0b25FeCJdfQ==