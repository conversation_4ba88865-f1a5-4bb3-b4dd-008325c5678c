
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/BanAccountTimeTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f1b4e2HZaVIuKfkag9QT/Rt', 'BanAccountTimeTipPnlCtrl');
// app/script/view/login/BanAccountTimeTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BanAccountTimeTipPnlCtrl = /** @class */ (function (_super) {
    __extends(BanAccountTimeTipPnlCtrl, _super);
    function BanAccountTimeTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.maskNode_ = null; // path://mask_n
        _this.descLbl_ = null; // path://root/content/desc_l
        _this.timeLbl_ = null; // path://root/content/time_l
        _this.emailLbl_ = null; // path://root/content/email_l
        //@end
        // private totalClickCount = 0 // 总点击次数（>=300次或600次仅且各发一次）
        // private hangUpTime = 0 // 挂机时长（毫秒,15m或30m仅且各发一次）（切后台时要重置）
        // private clickRecords = []
        _this.timeRecords = [];
        return _this;
        // // 点击封禁
        // private recordClickCount() {
        //     this.totalClickCount++
        //     if (this.totalClickCount % 300 === 0 && this.clickRecords.length < 2) {
        //         this.clickRecords.push(this.totalClickCount)
        //         this.sendMsgUpdateBanTime()
        //     }
        // }
        // // 挂机封禁
        // private recordHangUpTime() {
        //     const time = Math.floor(this.hangUpTime) * 1000
        //     if ((time >= 15 * ut.Time.Minute && this.timeRecords.length < 1) || (time >= 30 * ut.Time.Minute && this.timeRecords.length < 2)) {
        //         this.timeRecords.push(time)
        //         this.sendMsgUpdateBanTime()
        //     }
        // }
        // // 满足条件后更新封禁时间
        // private async sendMsgUpdateBanTime() {
        //     const { err, data } = await gameHpr.net.request('lobby/HD_BanCheatTap', { count: this.totalClickCount, time: this.hangUpTime * 1000 }, true)
        //     if (err) {
        //         viewHelper.showAlert(err)
        //     } else if (data) {
        //         this.updateBanTimeText(data.banCheatSurplusTime)
        //     }
        // }
        // update(dt: number) {
        //     this.hangUpTime += dt
        //     this.recordHangUpTime()
        // }
    }
    BanAccountTimeTipPnlCtrl.prototype.listenEventMaps = function () {
        return [
        // { [EventType.EVENT_GAME_HIDE]: this.onEventGameHide },
        ];
    };
    BanAccountTimeTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                return [2 /*return*/];
            });
        });
    };
    BanAccountTimeTipPnlCtrl.prototype.onEnter = function (time, type) {
        this.updateBanTimeText(time);
        this.maskNode_.active = !time;
        this.descLbl_.setLocaleKey('login.ban_account_time_tip', type);
        this.emailLbl_.string = '<EMAIL>';
    };
    BanAccountTimeTipPnlCtrl.prototype.onRemove = function () {
    };
    BanAccountTimeTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons/exit_be
    BanAccountTimeTipPnlCtrl.prototype.onClickExit = function (event, data) {
        GameHelper_1.gameHpr.exitGame();
    };
    // 已弃用，可手动删除
    BanAccountTimeTipPnlCtrl.prototype.onClickTouch = function (event, data) {
        // this.recordClickCount()
    };
    // path://root/buttons/feedback_be
    BanAccountTimeTipPnlCtrl.prototype.onClickFeedback = function (event, data) {
        ViewHelper_1.viewHelper.showFeedback();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 进入后台
    BanAccountTimeTipPnlCtrl.prototype.onEventGameHide = function () {
        logger.info('游戏进入后台');
        // this.hangUpTime = 0
        this.timeRecords.length = 0;
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 更新封禁时间
    BanAccountTimeTipPnlCtrl.prototype.updateBanTimeText = function (time) {
        if (this.timeLbl_.node.active = !!time) {
            this.timeLbl_.setLocaleKey('login.time_remaining_desc', GameHelper_1.gameHpr.millisecondToString(time));
        }
    };
    BanAccountTimeTipPnlCtrl = __decorate([
        ccclass
    ], BanAccountTimeTipPnlCtrl);
    return BanAccountTimeTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BanAccountTimeTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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