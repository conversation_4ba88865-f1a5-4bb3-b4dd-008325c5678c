{"version": 3, "sources": ["assets\\app\\script\\common\\helper\\JsbHelper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8CAAwC;AACxC,2CAAsC;AAEtC;;GAEG;AACH;IAAA;QAEY,mBAAc,GAAQ,IAAI,CAAA;IA2StC,CAAC;IAzSG;;;;;;;OAOG;IACI,wBAAI,GAAX,UAAY,KAAa,EAAE,QAAmB,EAAE,OAAa,EAAE,MAAY;QACvE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;QACzC,IAAI,QAAQ,EAAE;YACV,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;SAC5C;IACL,CAAC;IAED;;;;;OAKG;IACU,wBAAI,GAAjB,UAAkB,KAAa,EAAE,OAAa;;;gBAC1C,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;gBACzC,sBAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA;;;KACjC;IAED,wBAAwB;IAChB,wBAAI,GAAZ,UAAa,KAAa,EAAE,OAAgB;QACxC,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,EAAE,EAAE;YAC5B,OAAO,GAAG,IAAI,CAAA;SACjB;QACD,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;YAChB,OAAM;SACT;QACD,UAAU,CAAC;YACP,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;gBAChB,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,mCAAmC,EAAE,SAAS,EAAE,yCAAyC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;aAC7I;iBAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;gBACnB,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;aACjF;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;;OAMG;IACI,sBAAE,GAAT,UAAU,KAAa,EAAE,QAAmB,EAAE,MAAY;QACtD,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;IAC3C,CAAC;IAEM,uBAAG,GAAV,UAAW,KAAa,EAAE,QAAmB,EAAE,MAAY;QACvD,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;IAC5C,CAAC;IAED;;;;;OAKG;IACI,wBAAI,GAAX,UAAY,KAAa,EAAE,IAAS;QAChC,IAAI,IAAI,EAAE;YACN,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;gBAClB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;oBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;iBACnB;qBAAM,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,EAAE;oBAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;iBACpB;aACJ;SACJ;QACD,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IACjC,CAAC;IAEY,kCAAc,GAA3B;;;;;;6BACQ,CAAA,EAAE,CAAC,SAAS,EAAE,IAAI,oBAAO,CAAC,QAAQ,EAAE,CAAA,EAApC,wBAAoC;wBACrB,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,CAAC,EAAA;;wBAAnD,IAAI,GAAK,CAAA,SAA0C,CAAA,KAA/C;wBACV,sBAAO,IAAI,EAAA;;;;;KAElB;IAEM,iCAAa,GAApB;QACI,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,EAAE,CAAA;SACZ;aAAM;YACH,OAAO,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAA;SACrE;IACL,CAAC;IAEO,qCAAiB,GAAzB;;QACI,IAAI,MAAM,GAAG,EAAE,CAAA;QACf,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YAChB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,yCAAyC,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,CAAA;YAChI,IAAI,GAAG,IAAI,GAAG,KAAK,MAAM,EAAE;gBACvB,MAAM,GAAG,GAAG,CAAA;aACf;SACJ;aAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;YACnB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAA;YAChF,IAAI,GAAG,IAAI,GAAG,KAAK,MAAM,EAAE;gBACvB,MAAM,GAAG,GAAG,CAAA;aACf;SACJ;QACD,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,IAAI,CAAA;SACd;QACD,mFAAmF;QACnF,IAAI,MAAM,CAAC,UAAU,CAAC,kCAAkC,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;YACvF,IAAA,KAAA,OAAW,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAA3B,CAAC,QAAA,EAAE,GAAG,QAAqB,CAAA;YAClC,IAAM,MAAI,GAAG,EAAE,CAAA;YACf,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;gBACd,IAAA,KAAA,OAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAApB,CAAC,QAAA,EAAE,CAAC,QAAgB,CAAA;gBAC3B,MAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;YACF,OAAO,MAAI,CAAA;SACd;QACK,IAAA,KAAA,OAAoB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAApC,GAAG,QAAA,EAAE,IAAI,QAAA,EAAE,IAAI,QAAqB,CAAA;QAC3C,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,IAAI,QAAE,MAAM,CAAC,IAAI,CAAC,mCAAI,SAAS,EAAE,CAAA;IACzD,CAAC;IAED,SAAS;IACI,oCAAgB,GAA7B;;;;;;wBACI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAClB,sBAAO,IAAI,EAAA;yBACd;6BAAM,IAAI,oBAAO,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI;4BACjC,sBAAO,IAAI,CAAC,iBAAiB,EAAE,EAAA;yBAClC;wBACW,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,kBAAkB,CAAC,EAAA;;wBAAlD,GAAG,GAAG,SAA4C;wBACxD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;4BACZ,sBAAO,GAAG,EAAA;yBACb;wBACD,sBAAO,IAAI,EAAA;;;;KACd;IAED,SAAS;IACF,kCAAc,GAArB;QACI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;YAClB,OAAO,IAAI,CAAA;SACd;aAAM,IAAI,oBAAO,CAAC,QAAQ,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAA;SAClC;aAAM,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YACvB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,2CAA2C,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,CAAA;YAClI,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,CAAA;YACnC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;SACjC;aAAM,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE;YACnB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAA;YAClF,IAAI,GAAG,KAAK,OAAO,EAAE;gBACjB,IAAI;oBACA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;iBACzB;gBAAC,OAAO,KAAK,EAAE,GAAG;aACtB;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;;OAMG;IACU,kCAAc,GAA3B,UAA4B,GAAW,EAAE,OAAe,EAAE,OAAgB;;;;;;6BAClE,EAAE,CAAC,KAAK,EAAE,EAAV,wBAAU;wBACO,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;;wBAAhF,MAAM,GAAK,CAAA,SAAqE,CAAA,OAA1E;wBACZ,sBAAO,CAAC,CAAC,MAAM,EAAA;;oBAEf,iBAAiB;oBACjB,sBAAO,KAAK,EAAA;;;;KAEnB;IAED,8BAA8B;IACjB,iCAAa,GAA1B,UAA2B,GAAW,EAAE,OAAgB;;;;;;6BAChD,EAAE,CAAC,KAAK,EAAE,EAAV,wBAAU;wBACO,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,eAAe,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;;wBAAtE,MAAM,GAAK,CAAA,SAA2D,CAAA,OAAhE;wBACZ,IAAI,MAAM,KAAK,MAAM;4BAAE,sBAAO,EAAE,EAAA;wBAChC,sBAAO,MAAM,EAAA;;oBAEb,iBAAiB;oBACjB,sBAAO,EAAE,EAAA;;;;KAEhB;IAED,gBAAgB;IACH,iCAAa,GAA1B,UAA2B,GAAW,EAAE,OAAgB;;;;;;6BAChD,EAAE,CAAC,KAAK,EAAE,EAAV,wBAAU;wBACO,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,eAAe,EAAE,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;;wBAAtE,MAAM,GAAK,CAAA,SAA2D,CAAA,OAAhE;wBACZ,sBAAO,CAAC,CAAC,MAAM,EAAA;;oBAEf,iBAAiB;oBACjB,sBAAO,KAAK,EAAA;;;;KAEnB;IAED,cAAc;IACD,+BAAW,GAAxB;;;;;;wBACI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAClB,sBAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAA;yBACvB;wBACe,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,CAAC;4BAC1D,oCAAoC;0BADsB;;wBAApD,KAAK,GAAK,CAAA,SAA0C,CAAA,MAA/C;wBACX,oCAAoC;wBACpC,IAAI,KAAK,KAAK,MAAM,EAAE;4BAClB,sBAAO,EAAE,GAAG,EAAE,IAAI,EAAE,EAAA;yBACvB;wBACD,sBAAO,EAAE,KAAK,OAAA,EAAE,EAAA;;;;KACnB;IAED,gDAAgD;IACzC,mCAAe,GAAtB;QACI,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,CAAC,CAAA;SACvC;IACL,CAAC;IAED,6BAA6B;IAChB,kCAAc,GAA3B;;;;;;wBACI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAClB,sBAAO,KAAK,EAAA;yBACf;wBACgB,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,gBAAgB,CAAC,EAAA;;wBAArD,MAAM,GAAK,CAAA,SAA0C,CAAA,OAA/C;wBACZ,sBAAO,CAAC,CAAC,MAAM,EAAA;;;;KAClB;IAEY,oCAAgB,GAA7B,UAA8B,SAAiB;;;;;;wBAC3C,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAClB,sBAAO,EAAE,MAAM,EAAE,EAAE,EAAE,EAAA;yBACxB;wBACkC,qBAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAA;;wBAAzD,KAA+B,SAA0B,EAAvD,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAA;wBAC9B,IAAI,KAAK;4BAAE,sBAAO,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,EAAA;;;6BAC5B,SAAS;wBACD,qBAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,SAAS,WAAA,EAAE,CAAC,EAAA;;wBAA5D,IAAI,GAAG,SAAqD;wBAChE,IAAI,IAAI,CAAC,KAAK;4BAAE,wBAAK;wBACrB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;wBAC1B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;;;wBAEvC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;wBACxC,sBAAO,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,EAAA;;;;KAC3B;IAED,SAAS;IACI,iCAAa,GAA1B,UAA2B,KAAe;;;;;;wBACtC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE;4BAClB,sBAAO,EAAE,EAAA;yBACZ;6BAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;4BACtC,sBAAO,IAAI,CAAC,cAAc,EAAA;yBAC7B;wBACD,KAAA,IAAI,CAAA;wBAAkB,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,eAAe,CAAC,EAAA;;wBAA/D,GAAK,cAAc,GAAG,SAAyC,CAAA;wBAC/D,sBAAO,IAAI,CAAC,cAAc,EAAA;;;;KAC7B;IAED,SAAS;IACF,kCAAc,GAArB;QACI,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAA;SACtE;aAAM,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YACvB,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,mCAAmC,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;SACvG;QACD,OAAO,IAAI,CAAA,CAAC,qBAAqB;IACrC,CAAC;IAED,WAAW;IACJ,8CAA0B,GAAjC;QACI,OAAO,IAAI,CAAA;QACX,IAAI,MAAM,GAAG,IAAI,CAAA;QACjB,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,4BAA4B,CAAC,CAAA;SACpF;aAAM,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YACvB,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,mCAAmC,EAAE,4BAA4B,EAAE,KAAK,CAAC,CAAA;SACrH;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAED,OAAO;IACM,gCAAY,GAAzB,UAA0B,IAAS;;;;;4BAEf,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,EAAA;;wBAAnD,GAAG,GAAG,SAA6C;wBACzD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;4BACZ,sBAAM;yBACT;6BAAM,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,MAAK,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;4BAC5C,sBAAM,CAAC,mBAAmB;yBAC7B;wBACD,qBAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAA;;wBAAhB,SAAgB,CAAA;;;4BACX,IAAI;;;;;;KAChB;IAEM,kCAAc,GAArB;QACI,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,OAAO,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAA;SACtE;aAAM,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;YACvB,OAAO,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,sCAAsC,EAAE,gBAAgB,EAAE,sBAAsB,CAAC,CAAA;SAC3H;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IACL,gBAAC;AAAD,CA7SA,AA6SC,IAAA;AAEY,QAAA,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,SAAS,EAAE,CAAA", "file": "", "sourceRoot": "/", "sourcesContent": ["import JsbEvent from \"../event/JsbEvent\"\nimport { gameHpr } from \"./GameHelper\"\n\n/**\n * 原生\n */\nclass JsbHelper {\n\n    private tempDeviceInfo: any = null\n\n    /**\n     * \n     * @param event 和native约定的事件\n     * @param jsonStr 参数字符串 \n     * @param callback \n     * @param target \n     * @description 异步调用方式，用于等待native异步回调，native会调用this.emit触发\n     */\n    public cast(event: string, callback?: Function, jsonObj?: any, target?: any) {\n        this.send(event, JSON.stringify(jsonObj))\n        if (callback) {\n            eventCenter.once(event, callback, target)\n        }\n    }\n\n    /**\n     * \n     * @param event \n     * @param parameters \n     * @description 同步调用方式，只是书写形式同步，不同于native同步返回，无需异步的调用还是用原生方法jsb.reflection.callStaticMethod\n     */\n    public async call(event: string, jsonObj?: any) {\n        this.send(event, JSON.stringify(jsonObj))\n        return eventCenter.wait(event)\n    }\n\n    // 统一生成json字符串形式传给native\n    private send(event: string, jsonStr?: string) {\n        if (!jsonStr || jsonStr === '') {\n            jsonStr = '{}'\n        }\n        if (!ut.isMobile()) {\n            return\n        }\n        setTimeout(() => {\n            if (ut.isAndroid()) {\n                jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'request', '(Ljava/lang/String;Ljava/lang/String;)V', event, jsonStr)\n            } else if (ut.isIos()) {\n                jsb.reflection.callStaticMethod('jsbHelp', 'request:JsonStr:', event, jsonStr)\n            }\n        })\n    }\n\n    /**\n     * \n     * @param event \n     * @param callback \n     * @param target \n     * @description 注册事件，用于native主动调用的场景\n     */\n    public on(event: string, callback?: Function, target?: any) {\n        eventCenter.on(event, callback, target)\n    }\n\n    public off(event: string, callback?: Function, target?: any) {\n        eventCenter.off(event, callback, target)\n    }\n\n    /**\n     * \n     * @param event \n     * @param jsonStr \n     * @description native回调时调用\n     */\n    public emit(event: string, json: any) {\n        if (json) {\n            for (let key in json) {\n                if (json[key] === 'true') {\n                    json[key] = true\n                } else if (json[key] === 'false') {\n                    json[key] = false\n                }\n            }\n        }\n        eventCenter.emit(event, json)\n    }\n\n    public async getPackageSign() {\n        if (ut.isAndroid() && gameHpr.isInland()) {\n            let { sign } = await this.call(JsbEvent.GET_PACKAGE_SIGN)\n            return sign\n        }\n    }\n\n    public getDeviceType() {\n        if (ut.isAndroid()) {\n            return ''\n        } else {\n            return jsb.reflection.callStaticMethod('jsbHelp', 'getDeviceName')\n        }\n    }\n\n    private getDeepLinkParams() {\n        let params = ''\n        if (ut.isAndroid()) {\n            const res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/AppsFlyerHelper', 'getShemeParams', '()Ljava/lang/String;')\n            if (res && res !== 'null') {\n                params = res\n            }\n        } else if (ut.isIos()) {\n            const res = jsb.reflection.callStaticMethod('AppsflyerHelper', 'getShemeParams')\n            if (res && res !== 'null') {\n                params = res\n            }\n        }\n        if (!params) {\n            return null\n        }\n        // https://nta-applinks.dhgames.com/index.html?openUI=menu_Portrayal&params=3100141\n        if (params.startsWith('https://nta-applinks.dhgames.com') || params.startsWith('jwm-sheme1://')) {\n            const [_, str] = params.split('?')\n            const data = {}\n            str.split('&').forEach(m => {\n                const [k, v] = m.split('=')\n                data[k] = v\n            })\n            return data\n        }\n        const [uid, type, date] = params.split('|')\n        return { uid, type, date: Number(date) ?? undefined }\n    }\n\n    // 获取安装参数\n    public async getInstallParams() {\n        if (!cc.sys.isNative) {\n            return null\n        } else if (gameHpr.isGLobal()) { //海外\n            return this.getDeepLinkParams()\n        }\n        const res = await this.call(JsbEvent.GET_INSTALL_PARAMS)\n        if (!res.error) {\n            return res\n        }\n        return null\n    }\n\n    // 获取启动参数\n    public getAwakeParams() {\n        if (!cc.sys.isNative) {\n            return null\n        } else if (gameHpr.isGLobal()) {\n            return this.getDeepLinkParams()\n        } else if (ut.isAndroid()) {\n            const res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/OpenInstallHelper', 'getAwakeParams', '()Ljava/lang/String;')\n            const obj = JSON.parse(res || '{}')\n            return !obj.error ? obj : null\n        } else if (ut.isIos()) {\n            const res = jsb.reflection.callStaticMethod('OpenInstallHelper', 'getAwakeParams')\n            if (res !== 'error') {\n                try {\n                    return JSON.parse(res)\n                } catch (error) { }\n            }\n        }\n        return null\n    }\n\n    /**\n     * @param key \n     * @param content value\n     * @param service ios必须传 类似key 在ios中 service就好像给宏取名字,表示存储的这个东西是做什么的.通过这两个key就可以指定唯一性 可以暂时当成另一个key\n     * @description 存数据到设备手机上，并且不会因为应用被卸载而删除\n     * @returns Bool\n     */\n    public async saveDeviceData(key: String, content: String, service?: String) {\n        if (ut.isIos()) {\n            let { result } = await this.call(JsbEvent.SAVE_DEVICE_DATA, { key, content, service })\n            return !!result\n        } else {\n            // android to do \n            return false\n        }\n    }\n\n    // 获取存储在设备手机上的数据 返回null为没有存储数据\n    public async getDeviceData(key: String, service?: String) {\n        if (ut.isIos()) {\n            let { result } = await this.call(JsbEvent.GET_DEVICE_DATA, { key, service })\n            if (result === 'null') return ''\n            return result\n        } else {\n            // android to do \n            return ''\n        }\n    }\n\n    // 删除存储在设备手机上的数据\n    public async delDeviceData(key: String, service?: String) {\n        if (ut.isIos()) {\n            let { result } = await this.call(JsbEvent.DEL_DEVICE_DATA, { key, service })\n            return !!result\n        } else {\n            // android to do \n            return false\n        }\n    }\n\n    // 获取消息推送token\n    public async getFcmToken() {\n        if (!cc.sys.isNative) {\n            return { err: true }\n        }\n        let { token } = await this.call(JsbEvent.GET_NOTICE_TOKEN)\n        // console.log('getFcmToken', token)\n        if (token === 'null') {\n            return { err: true }\n        }\n        return { token }\n    }\n\n    // 跳转到手机的九万亩的app设置页面 这个没有返回结果 因为不太好获取跳转之后返回游戏的时机\n    public openSelfSetting() {\n        if (cc.sys.isNative) {\n            this.cast(JsbEvent.OPEN_APP_SETTING)\n        }\n    }\n\n    // 检测是否有通知的权限 返回true or false\n    public async checkNoticePer() {\n        if (!cc.sys.isNative) {\n            return false\n        }\n        let { result } = await this.call(JsbEvent.CHECK_NOTICE_PER)\n        return !!result\n    }\n\n    public async getLangOrderList(eventName: string) {\n        if (!cc.sys.isNative) {\n            return { result: [] }\n        }\n        let { error, result, nextIndex } = await this.call(eventName)\n        if (error) return { error, result }\n        while (nextIndex) {\n            let info = await this.call(\"GET_MORE_LIST_INDEX\", { nextIndex })\n            if (info.error) break\n            nextIndex = info.nextIndex\n            result = result.concat(info.result)\n        }\n        console.log(\"result is \", result.length)\n        return { error, result }\n    }\n\n    // 获取设备信息\n    public async getDeviceInfo(force?: boolean) {\n        if (!cc.sys.isNative) {\n            return {}\n        } else if (!force && this.tempDeviceInfo) {\n            return this.tempDeviceInfo\n        }\n        this.tempDeviceInfo = await this.call(JsbEvent.GET_DEVICE_INFO)\n        return this.tempDeviceInfo\n    }\n\n    // 获取网络类型\n    public getNetWorkType() {\n        let type = 0\n        if (cc.sys.os === cc.sys.OS_IOS) {\n            type = jsb.reflection.callStaticMethod('jsbHelp', 'getNetWorkType')\n        } else if (ut.isAndroid()) {\n            type = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'getNetWorkType', '()I')\n        }\n        return type // NONE:0,LAN:1,WAN:2\n    }\n\n    // 获取网络访问权限\n    public getNetworkAccessPermission() {\n        return true\n        let access = true\n        if (cc.sys.os === cc.sys.OS_IOS) {\n            access = jsb.reflection.callStaticMethod('jsbHelp', 'getNetworkAccessPermission')\n        } else if (ut.isAndroid()) {\n            access = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'getNetworkAccessPermission', '()I')\n        }\n        return access\n    }\n\n    // 消费订单\n    public async consumeOrder(data: any) {\n        do {\n            const res = await this.call(JsbEvent.CONSUME_ORDER, data)\n            if (!res.error) {\n                return\n            } else if (res?.code === '8' && ut.isAndroid()) {\n                return //code=8 表示服务器已经消费了\n            }\n            await ut.wait(1)\n        } while (true)\n    }\n\n    public getAppsflyerId() {\n        if (cc.sys.os === cc.sys.OS_IOS) {\n            return jsb.reflection.callStaticMethod('jsbHelp', 'getAppsflyerId')\n        } else if (ut.isAndroid()) {\n            return jsb.reflection.callStaticMethod('org/cocos2dx/javascript/DeviceHelper', 'getAppsflyerId', '()Ljava/lang/String;')\n        }\n        return null\n    }\n}\n\nexport const jsbHelper = window['jsbHelper'] = new JsbHelper()"]}