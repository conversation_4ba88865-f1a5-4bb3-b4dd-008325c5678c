
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PolicyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c9fa44mubFBGqf2L2SHQO0u', 'PolicyObj');
// app/script/model/main/PolicyObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseStudyObj_1 = require("./BaseStudyObj");
// 一个政策槽位
var PolicyObj = /** @class */ (function (_super) {
    __extends(PolicyObj, _super);
    function PolicyObj() {
        var _this = _super.call(this) || this;
        _this.type = Enums_1.CEffect.NONE;
        _this.values = [];
        _this.studyType = Enums_1.StudyType.POLICY;
        return _this;
    }
    PolicyObj.prototype.init = function () {
        var json = assetsMgr.getJsonData('policy', this.id);
        if (json) {
            this.type = json.type;
            this.values = ut.stringToNumbers(json.value, ',');
        }
        return this;
    };
    Object.defineProperty(PolicyObj.prototype, "name", {
        get: function () { return 'policyText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PolicyObj.prototype, "desc", {
        get: function () { return 'policyText.desc_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PolicyObj.prototype, "descParams", {
        get: function () { return this.getValue(0) + ''; },
        enumerable: false,
        configurable: true
    });
    PolicyObj.prototype.getValue = function (index) {
        var count = this.values.length;
        if (count === 0) {
            return 0;
        }
        else if (index < 0) {
            index = 0;
        }
        else if (index >= count) {
            index = count - 1;
        }
        return this.values[index];
    };
    return PolicyObj;
}(BaseStudyObj_1.default));
exports.default = PolicyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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