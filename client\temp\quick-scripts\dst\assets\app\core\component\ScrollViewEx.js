
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/ScrollViewEx.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0ccbdIWP69L7KfMFkdHwLyV', 'ScrollViewEx');
// app/core/component/ScrollViewEx.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent, disallowMultiple = _a.disallowMultiple;
var ScrollViewEx = /** @class */ (function (_super) {
    __extends(ScrollViewEx, _super);
    function ScrollViewEx() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.virtual = true; //动态列表
        _this.childScrollViewName = ''; //子列表
        _this.updateRate = 1; //渲染频率 多少帧渲染一个
        _this.updateRenderCount = 1; //分帧渲染个数
        _this.scrollView = null;
        _this.viewSize = null;
        _this.content = null;
        _this.layout = null;
        _this.scrollType = 0; //滚动类型 0.纵向 1.横向
        _this.rowCount = 1; //一列或一行的个数
        _this.preContentPosition = cc.v2();
        _this.tempContentPosition = cc.v2();
        _this.frameCount = 0; //多少帧
        _this.renderStartIndex = 0; //渲染起点下标
        _this.needRenderCount = 0; //需要渲染的item个数
        _this.currRenderCount = 0; //当前渲染的item个数
        _this.item = null;
        _this.itemName = '';
        _this.itemSize = null;
        _this.itemAnchor = null;
        _this.childContentDefaultPosition = cc.v2(); //子ScrollView的默认位置
        _this.itemDefaultPosition = cc.v2(); //
        _this.pool = [];
        _this.datas = [];
        _this.dataCount = 0;
        _this.setItemCallback = null;
        _this.callbackTarget = null;
        _this.tempVec = cc.v2();
        return _this;
    }
    ScrollViewEx.prototype.onLoad = function () {
        this.init();
    };
    ScrollViewEx.prototype.init = function () {
        if (this.scrollView) {
            return;
        }
        this.scrollView = this.getComponent(cc.ScrollView);
        this.content = this.scrollView.content;
        this.viewSize = this.content.parent.getContentSize();
        this.layout = this.content.getComponent(cc.Layout);
        if (!this.layout) {
            return logger.error('需要Layout组件');
        }
        this.layout.enabled = false;
        this.item = this.content.children[0];
        if (!this.item) {
            return logger.error('必须满足content中有一个可拷贝的节点');
        }
        else if (this.childScrollViewName) {
            var sv = this.item.Child(this.childScrollViewName, cc.ScrollView);
            sv && sv.content.getPosition(this.childContentDefaultPosition);
        }
        this.item.getPosition(this.itemDefaultPosition);
        this.itemName = this.item.name;
        this.itemSize = this.item.getContentSize();
        this.itemAnchor = cc.v2(this.item.anchorX, this.item.anchorY);
        this.item.name = this.itemName + '_clone';
        this.item.active = false;
    };
    ScrollViewEx.prototype.reset = function () {
        if (!this.scrollView) {
            this.init();
        }
        this.viewSize = this.content.parent.getContentSize();
        var sum = this.dataCount;
        var layout = this.layout, LayoutType = cc.Layout.Type;
        var setItemPos = null, width = this.content.width, height = this.content.height;
        if (layout.type === LayoutType.VERTICAL) {
            height = sum * this.itemSize.height + (sum - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom;
            setItemPos = this.setItemPosByVertical;
            this.scrollType = 0;
            this.rowCount = 1;
        }
        else if (layout.type === LayoutType.HORIZONTAL) {
            width = sum * this.itemSize.width + (sum - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight;
            setItemPos = this.setItemPosByHorizontal;
            this.scrollType = 1;
            this.rowCount = 1;
        }
        else if (layout.type === LayoutType.GRID) {
            if (layout.startAxis === cc.Layout.AxisDirection.HORIZONTAL) {
                var w = width - layout.paddingLeft - layout.paddingRight + layout.spacingX;
                var row = this.rowCount = Math.floor(w / (this.itemSize.width + layout.spacingX));
                var count = Math.ceil(sum / row);
                height = count * this.itemSize.height + (count - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom;
                setItemPos = this.setItemPosByGridHorizontal;
                this.scrollType = 0;
            }
            else {
                var h = height - layout.paddingTop - layout.paddingBottom + layout.spacingY;
                var row = this.rowCount = Math.floor(h / (this.itemSize.height + layout.spacingY));
                var count = Math.ceil(sum / row);
                width = count * this.itemSize.width + (count - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight;
                setItemPos = this.setItemPosByGridVertical;
                this.scrollType = 1;
            }
        }
        //
        this.preContentPosition.set(this.content.getPosition(this.tempContentPosition));
        // 设置需要显示的个数
        if (!this.virtual) {
            this.renderStartIndex = 0;
            this.needRenderCount = sum;
        }
        else if (this.scrollType === 0) {
            var vh = this.viewSize.height - layout.paddingTop - layout.paddingBottom + layout.spacingY;
            this.needRenderCount = (Math.ceil(vh / (this.itemSize.height + layout.spacingY)) + 1) * this.rowCount;
            // 下面计算开始位置 先默认 anchorY = 1
            var cy = Math.abs(this.preContentPosition.y - layout.paddingTop + layout.spacingY);
            this.renderStartIndex = cc.misc.clampf(Math.floor(cy / (this.itemSize.height + layout.spacingY)) * this.rowCount, 0, this.dataCount - 1);
        }
        else if (this.scrollType === 1) {
            var vw = this.viewSize.width - layout.paddingLeft - layout.paddingRight + layout.spacingX;
            this.needRenderCount = (Math.ceil(vw / (this.itemSize.width + layout.spacingX)) + 1) * this.rowCount;
            // 下面计算开始位置 先默认 anchorY = 0
            var cx = Math.abs(this.preContentPosition.x + layout.paddingLeft - layout.spacingX);
            this.renderStartIndex = cc.misc.clampf(Math.floor(cx / (this.itemSize.width + layout.spacingX)) * this.rowCount, 0, this.dataCount - 1);
        }
        // 清空content
        this.pool.length = 0;
        // this.items.length = 0
        // while (this.datas.length > 0) {
        //     this.delItem(this.datas.pop())
        // }
        // 以防万一 讲道理是不会有了
        for (var i = this.content.childrenCount - 1; i >= 0; i--) {
            var it = this.content.children[i];
            if (it === this.item) {
                continue;
            }
            else if (this.pool.length < this.needRenderCount && cc.isValid(it)) {
                this.putItem(it);
            }
            else {
                it.destroy();
            }
        }
        // 设置content大小
        this.content.setContentSize(width, height);
        // 调整ScrollBar位置
        // @ts-ignore
        this.scrollView.horizontalScrollBar && this.scrollView.horizontalScrollBar._onScroll(cc.Vec2.ZERO);
        // @ts-ignore
        this.scrollView.verticalScrollBar && this.scrollView.verticalScrollBar._onScroll(cc.Vec2.ZERO);
        // 预设item坐标
        this.datas.length = 0;
        for (var i = 0; i < sum; i++) {
            this.datas.push(setItemPos.call(this, i, { index: i, x: 0, y: 0 }));
        }
    };
    // VERTICAL
    ScrollViewEx.prototype.setItemPosByVertical = function (i, out) {
        var layout = this.layout;
        // x
        out.x = this.itemDefaultPosition.x;
        // y
        var startY = this.content.anchorY === 1 ? layout.paddingTop : layout.paddingBottom;
        out.y = startY + this.itemSize.height * (1 - this.itemAnchor.y) + i * this.itemSize.height + i * layout.spacingY;
        if (this.content.anchorY === 1) {
            out.y = -out.y;
        }
        return out;
    };
    // HORIZONTAL
    ScrollViewEx.prototype.setItemPosByHorizontal = function (i, out) {
        var layout = this.layout;
        // x
        var startX = this.content.anchorX === 1 ? layout.paddingRight : layout.paddingLeft;
        out.x = startX + this.itemSize.width * this.itemAnchor.x + i * this.itemSize.width + i * layout.spacingX;
        if (this.content.anchorX === 1) {
            out.x = -out.x;
        }
        // y
        out.y = this.itemDefaultPosition.y;
        return out;
    };
    // GRID.HORIZONTAL 需要content.anchorY != 0.5
    ScrollViewEx.prototype.setItemPosByGridHorizontal = function (index, out) {
        var layout = this.layout;
        // x
        var startX = -this.content.anchorX * this.content.width + layout.paddingLeft;
        var i = index % this.rowCount;
        out.x = startX + this.itemSize.width * this.itemAnchor.x + i * this.itemSize.width + i * layout.spacingX;
        // y
        var startY = this.content.anchorY === 1 ? layout.paddingTop : layout.paddingBottom;
        i = Math.floor(index / this.rowCount);
        out.y = startY + this.itemSize.height * this.itemAnchor.y + i * this.itemSize.height + i * layout.spacingY;
        if (this.content.anchorY === 1) {
            out.y = -out.y;
        }
        return out;
    };
    // GRID.VERTICAL
    ScrollViewEx.prototype.setItemPosByGridVertical = function (index, out) {
        var layout = this.layout;
        // y
        var startY = -this.content.anchorY * this.content.height + layout.paddingBottom;
        var i = index % this.rowCount;
        out.y = startY + this.itemSize.height * this.itemAnchor.y + i * this.itemSize.height + i * layout.spacingY;
        // x
        var startX = this.content.anchorX === 1 ? layout.paddingRight : layout.paddingLeft;
        i = Math.floor(index / this.rowCount);
        out.x = startX + this.itemSize.width * this.itemAnchor.x + i * this.itemSize.width + i * layout.spacingX;
        if (this.content.anchorX === 1) {
            out.x = -out.x;
        }
        return out;
    };
    ScrollViewEx.prototype.getItem = function () {
        var it = this.pool.pop();
        if (!it) {
            it = cc.instantiate2(this.item, this.content);
            it.name = this.itemName;
        }
        it.active = true;
        return it;
    };
    ScrollViewEx.prototype.putItem = function (it) {
        it.active = false;
        it.Data = null;
        if (!this.pool.has('uuid', it.uuid)) {
            this.pool.push(it);
        }
        else {
            logger.error('ScrollViewEx.putItem has uuid?');
        }
    };
    // 删除节点
    ScrollViewEx.prototype.delItems = function (start, count) {
        count = start + count;
        for (var i = start; i < count && i < this.dataCount; i++) {
            this.delItem(this.datas[i]);
        }
    };
    ScrollViewEx.prototype.delItem = function (d) {
        if (!d.node) {
            return;
        }
        // 如果有子列表 这里记录他的位置
        if (this.childScrollViewName) {
            var sv = d.node.Child(this.childScrollViewName, cc.ScrollView);
            if (!sv) { }
            else if (d.childContentPosition) {
                d.childContentPosition.set(sv.content.getPosition(this.tempVec));
            }
            else {
                d.childContentPosition = sv.content.getPosition();
            }
        }
        // 回收
        this.putItem(d.node);
        d.node = null;
    };
    // 开始创建item
    ScrollViewEx.prototype.updateItems = function () {
        var cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0;
        if (this.currRenderCount + cnt > this.needRenderCount) {
            cnt = this.needRenderCount - this.currRenderCount;
        }
        var i = this.renderStartIndex, l = Math.min(this.renderStartIndex + this.needRenderCount, this.dataCount);
        while (i < l && cur < cnt) {
            var d = this.datas[i++];
            if (d.node) {
                continue;
            }
            var it = d.node = this.getItem();
            it.setPosition(d.x, d.y);
            // 如果有子列表 这里还原他的位置
            if (this.childScrollViewName) {
                var sv = d.node.Child(this.childScrollViewName, cc.ScrollView);
                if (!sv) { }
                else if (d.childContentPosition) {
                    sv.content.setPosition(d.childContentPosition);
                }
                else {
                    sv.content.setPosition(this.childContentDefaultPosition);
                }
            }
            // 回调
            if (this.setItemCallback) {
                this.callbackTarget ? this.setItemCallback.call(this.callbackTarget, it, d.index) : this.setItemCallback(it, d.index);
            }
            cur += 1;
        }
        this.currRenderCount += cnt;
        // if (this.currRenderCount === this.needRenderCount) {
        //     cc.log(this.content.childrenCount)
        // }
    };
    // 检测是否有item被滚动出去或进来
    ScrollViewEx.prototype.checkScroll = function () {
        if (this.datas.length === 0) {
            return;
        }
        var d = this.datas[this.renderStartIndex];
        if (!d.node) {
            this.currRenderCount = 0;
            return;
        }
        var index = this.renderStartIndex, dir = 0;
        if (this.scrollType === 0) {
            // 这里先默认 content.anchorY = 1 和 view.anchorY = 1
            var sy = this.preContentPosition.y + (d.y - d.node.height * d.node.anchorY);
            if (sy > 0) { //出去了
                dir = 1;
            }
            else if (sy < -(d.node.height + this.layout.spacingY)) {
                dir = -1;
            }
        }
        else if (this.scrollType === 1) {
            // 这里先默认 content.anchorX = 0 和 view.anchorX = 0
            var sx = this.preContentPosition.x + (d.x + d.node.width * d.node.anchorX);
            if (sx < 0) { //出去了
                dir = 1;
            }
            else if (sx > d.node.width + this.layout.spacingX) {
                dir = -1;
            }
        }
        if (dir === 0) {
            return;
        }
        else if (dir === 1) {
            this.delItems(index, this.rowCount);
            index = Math.min(index + this.rowCount, this.dataCount - 1);
            // cc.log('out ........', index)
        }
        else if (dir === -1) {
            var idx = Math.max(index - this.rowCount, 0);
            if (index !== idx) {
                this.delItems(index + this.needRenderCount - this.rowCount, this.rowCount);
                index = idx;
                // cc.log('in ........', index)
            }
        }
        if (this.renderStartIndex !== index) {
            this.renderStartIndex = index;
            this.currRenderCount = 0;
        }
    };
    ScrollViewEx.prototype.update = function (dt) {
        // 虚拟列表检测 滑动
        if (this.virtual && !this.preContentPosition.equals(this.content.getPosition(this.tempContentPosition))) {
            this.preContentPosition.set(this.tempContentPosition);
            this.checkScroll();
        }
        // 渲染
        if (this.currRenderCount < this.needRenderCount) {
            this.frameCount += 1;
            if (this.frameCount >= this.updateRate) {
                this.frameCount = 0;
                this.updateItems();
            }
        }
    };
    // 填充列表
    ScrollViewEx.prototype.list = function (len, cb, target) {
        this.dataCount = len;
        this.setItemCallback = cb;
        this.callbackTarget = target;
        this.reset();
        this.currRenderCount = 0;
    };
    // 添加
    ScrollViewEx.prototype.addByList = function () {
    };
    ScrollViewEx.prototype.getItemNode = function () {
        return this.item;
    };
    __decorate([
        property()
    ], ScrollViewEx.prototype, "virtual", void 0);
    __decorate([
        property({ tooltip: CC_DEV && '子的ScrollView,如果有' })
    ], ScrollViewEx.prototype, "childScrollViewName", void 0);
    __decorate([
        property({ type: cc.Integer, range: [1, 10, 1], tooltip: CC_DEV && '多少帧渲染一次', slide: true })
    ], ScrollViewEx.prototype, "updateRate", void 0);
    __decorate([
        property({ type: cc.Integer, range: [0, 30, 1], tooltip: CC_DEV && '一次渲染多少个,0渲染所有', slide: true })
    ], ScrollViewEx.prototype, "updateRenderCount", void 0);
    ScrollViewEx = __decorate([
        ccclass,
        disallowMultiple(),
        requireComponent(cc.ScrollView),
        menu('自定义组件/ScrollViewEx')
    ], ScrollViewEx);
    return ScrollViewEx;
}(cc.Component));
exports.default = ScrollViewEx;
cc.ScrollViewEx = ScrollViewEx;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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