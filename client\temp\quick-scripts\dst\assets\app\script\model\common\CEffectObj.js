
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/CEffectObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c64a1+KnphB25Rm4ICjQQ/M', 'CEffectObj');
// app/script/model/common/CEffectObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
// 通用效果
var CEffectObj = /** @class */ (function () {
    function CEffectObj() {
        this.type = Enums_1.CEffect.NONE;
        this.value = null;
        this.param = null;
        this.conf = {};
    }
    CEffectObj.prototype.init = function (type, value, param) {
        this.type = type;
        this.value = value;
        this.param = param;
        return this;
    };
    CEffectObj.prototype.fromString = function (val) {
        var _a = __read(val.split(',').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }), 3), type = _a[0], value = _a[1], param = _a[2];
        this.type = Number(type);
        this.conf = Constant_1.BUILD_EFFECT_TYPE_CONF[this.type] || {};
        if (this.conf.vtype === 'number') {
            this.value = Number(value);
        }
        else {
            this.value = value;
        }
        this.param = param;
        return this;
    };
    // 获取说明的类型
    CEffectObj.prototype.getDescType = function () {
        var _a, _b;
        return (_b = (_a = this.conf) === null || _a === void 0 ? void 0 : _a.desc) !== null && _b !== void 0 ? _b : this.type;
    };
    // 获取对应值文本
    CEffectObj.prototype.getValueText = function () {
        return (this.conf.prefix || '') + this.value + (this.conf.suffix || '');
    };
    CEffectObj.prototype.getParamNumber = function () {
        return Number(this.param) || 0;
    };
    CEffectObj.prototype.getInfoForBuild = function () {
        return { key: 'ui.build_eff_desc_' + this.type, params: [this.getValueText()] };
    };
    return CEffectObj;
}());
exports.default = CEffectObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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