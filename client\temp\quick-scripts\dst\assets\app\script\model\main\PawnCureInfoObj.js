
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PawnCureInfoObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd8ee3kbjNRAYKT4mC4CHw9Z', 'PawnCureInfoObj');
// app/script/model/main/PawnCureInfoObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 士兵招募信息
var PawnCureInfoObj = /** @class */ (function () {
    function PawnCureInfoObj() {
        this.type = 'cure';
        this.uid = '';
        this.index = 0;
        this.auid = ''; //军队uid
        this.id = 0; //士兵id
        this.lv = 0; //招募等级
        this.needTime = 0; //需要时间
        this.surplusTime = 0; //剩余时间
        this.getTime = 0; //获取时间
        this.json = null;
    }
    PawnCureInfoObj.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.index = data.index;
        this.auid = data.auid;
        this.id = data.id;
        this.lv = data.lv;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        this.json = assetsMgr.getJsonData('pawnBase', this.id);
        return this;
    };
    // 获取实际的剩余时间
    PawnCureInfoObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    PawnCureInfoObj.prototype.isCanCancel = function () {
        return this.surplusTime === 0;
    };
    return PawnCureInfoObj;
}());
exports.default = PawnCureInfoObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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