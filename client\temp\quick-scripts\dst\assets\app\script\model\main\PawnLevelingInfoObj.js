
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PawnLevelingInfoObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd63b6vteTRAAqWdbb9NXSG0', 'PawnLevelingInfoObj');
// app/script/model/main/PawnLevelingInfoObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 士兵练级信息
var PawnLevelingInfoObj = /** @class */ (function () {
    function PawnLevelingInfoObj() {
        this.type = 'lving';
        this.uid = '';
        this.index = 0;
        this.auid = ''; //军队uid
        this.puid = ''; //士兵uid
        this.id = 0; //士兵id
        this.lv = 0; //训练等级
        this.needTime = 0; //需要时间
        this.surplusTime = 0; //剩余时间
        this.getTime = 0; //获取时间
        this.json = null;
    }
    PawnLevelingInfoObj.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.index = data.index;
        this.auid = data.auid;
        this.puid = data.puid;
        this.id = data.id;
        this.lv = data.lv;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        this.json = assetsMgr.getJsonData('pawnAttr', this.id * 1000 + this.lv);
        return this;
    };
    // 获取实际的剩余时间
    PawnLevelingInfoObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    PawnLevelingInfoObj.prototype.isCanCancel = function () {
        return this.surplusTime === 0;
    };
    return PawnLevelingInfoObj;
}());
exports.default = PawnLevelingInfoObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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