
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/EndRound.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4d749fCTVxKYKYf0rbctsBr', 'EndRound');
// app/script/model/behavior/EndRound.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 结束回合
var EndRound = /** @class */ (function (_super) {
    __extends(EndRound, _super);
    function EndRound() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.initDelay = 0;
        _this.isChangeState = false;
        _this.isChangeFighterStateMap = {};
        return _this;
    }
    EndRound.prototype.onInit = function (conf) {
        this.initDelay = conf.parameters || 250;
    };
    EndRound.prototype.onOpen = function () {
        this.isChangeState = false;
        this.isChangeFighterStateMap = {};
        this.setBlackboardData('delay', this.initDelay + (this.getTreeBlackboardData('addRoundEndDelayTime') || 0));
    };
    EndRound.prototype.onTick = function (dt) {
        var _this = this;
        var _a, _b, _c, _d;
        if ((this.getTreeBlackboardData('batterCount') || 0) > 0) {
            return BTConstant_1.BTState.SUCCESS;
        }
        var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0, delay = (_b = this.getBlackboardData('delay')) !== null && _b !== void 0 ? _b : 0;
        this.setBlackboardData('currTime', currTime + dt);
        var actionTime = (_c = this.getTreeBlackboardData('addRoundEndActionHitTime')) !== null && _c !== void 0 ? _c : 0;
        // 动作时间
        if (actionTime > 0) {
            var heroSkill_1 = this.target.getPortrayalSkill(), attackTarget = this.target.getAttackTarget();
            // 张辽
            if ((heroSkill_1 === null || heroSkill_1 === void 0 ? void 0 : heroSkill_1.id) === Enums_1.HeroType.ZHANG_LIAO && attackTarget) {
                if (currTime < actionTime) {
                    this.changeState(Enums_1.PawnState.SKILL + 1, { skillName: 'batter' });
                    return BTConstant_1.BTState.RUNNING;
                }
                else if (!this.getBlackboardData('isAction')) {
                    this.setBlackboardData('isAction', true);
                    // 对范围敌人造成伤害
                    attackTarget.getCanAttackFighterByRange(this.target.getCanAttackFighters(), heroSkill_1.target, heroSkill_1.params, '').forEach(function (m) {
                        // 受击
                        _this.ctrl.onHitBaseTrueDamage(_this.target, m, { attackAmend: heroSkill_1.value * 0.01 });
                        // 播放效果
                        _this.ctrl.playBattleEffect([208002], m.getPoint());
                    });
                    // 添加减伤buff
                    this.target.addBuff(Enums_1.BuffType.ASSAULT, this.target.getUid(), 1);
                    this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.DAMAGE_REDUCTION, this.target.getPoint());
                }
            }
            if (this.getTreeBlackboardData('isPlayJJShieldEnd')) { //机甲皮肤的收盾动作
                if (currTime < actionTime) {
                    if (currTime === 0) {
                        this.target.removeBuff(Enums_1.BuffType.STAND_SHIELD);
                    }
                    this.changeState(Enums_1.PawnState.SKILL + 1, { sound: 'sound_127_1', skillName: 'shield_end' });
                    return BTConstant_1.BTState.RUNNING;
                }
                this.setTreeBlackboardData('isPlayJJShieldEnd', false);
            }
            else if (this.getTreeBlackboardData('isPoisonedWineEnd')) { //鸩毒 爆炸
                if (currTime < actionTime) {
                    if (currTime === 0) {
                        var POISONED_WINE = this.target.getBuff(Enums_1.BuffType.POISONED_WINE);
                        if (!POISONED_WINE) {
                            this.setTreeBlackboardData('isPoisonedWineEnd', false);
                            return BTConstant_1.BTState.RUNNING;
                        }
                        this.target.removeBuff(Enums_1.BuffType.POISONED_WINE);
                        var uid = this.target.getUid(), camp_1 = this.target.getCamp();
                        var arr = this.target.getCanAttackRangeFighter(this.ctrl.getFighters(), 2, 7, uid, function (m) { return m.getCamp() !== camp_1 || m.isFlag(); });
                        arr.push(this.target);
                        var damage_1 = Math.round(POISONED_WINE.value * (POISONED_WINE.lv + 9) * 0.01);
                        arr.forEach(function (m) {
                            var val = m.hitPrepDamageHandle(0, damage_1).trueDamage;
                            var v = m.onHit(val, []);
                            m.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: m.isDie() });
                        });
                        if (arr.length > 0) {
                            this.ctrl.playBattleEffect([210002], this.target.getPoint(), 'top');
                        }
                    }
                    return BTConstant_1.BTState.RUNNING;
                }
                this.setTreeBlackboardData('isPoisonedWineEnd', false);
            }
        }
        // 结束时间
        if (currTime >= delay || !this.isActioning(delay > this.initDelay || actionTime > 0)) {
            // 如果还没有攻击过 清理当前的目标 下次重新选
            if (!this.getTreeBlackboardData('isAttack')) {
                this.target.changeAttackTarget(null);
                // 夏侯渊 清理奔袭buff
                if (((_d = this.target.getPortrayalSkill()) === null || _d === void 0 ? void 0 : _d.id) === Enums_1.HeroType.XIA_HOUYUAN) {
                    this.target.removeBuff(Enums_1.BuffType.LONG_RANGE_RAID);
                }
            }
            // 标记结束回合
            this.setTreeBlackboardData('isRoundEnd', true);
            return BTConstant_1.BTState.SUCCESS;
        }
        else if (!this.isChangeState) {
            this.isChangeState = true;
            this.target.changeState(Enums_1.PawnState.STAND);
            // 是否可以继续回合 这里主要是播放触发动画
            if (this.target.isHasBuff(Enums_1.BuffType.CONTINUE_ACTION)) {
                this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.SAND_CLOCK, this.target.getPoint());
            }
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 是否行动过
    EndRound.prototype.isActioning = function (hasAddRoundEndDelayTime) {
        return hasAddRoundEndDelayTime || this.getTreeBlackboardData('isDeductHpAction') || this.getTreeBlackboardData('isBloodAction') || this.getTreeBlackboardData('isMove') || this.getTreeBlackboardData('isAttack');
    };
    // 设置当前士兵状态 更新视图信息
    EndRound.prototype.changeState = function (state, data) {
        if (!this.isChangeFighterStateMap[state]) {
            this.isChangeFighterStateMap[state] = true;
            this.target.changeState(state, data);
        }
    };
    return EndRound;
}(BaseAction_1.default));
exports.default = EndRound;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcRW5kUm91bmQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQW9FO0FBQ3BFLHFEQUE0RTtBQUM1RSwyQ0FBc0M7QUFDdEMsMkNBQXVDO0FBRXZDLE9BQU87QUFDUDtJQUFzQyw0QkFBVTtJQUFoRDtRQUFBLHFFQXFIQztRQW5IVyxlQUFTLEdBQVcsQ0FBQyxDQUFBO1FBQ3JCLG1CQUFhLEdBQVksS0FBSyxDQUFBO1FBQzlCLDZCQUF1QixHQUErQixFQUFFLENBQUE7O0lBaUhwRSxDQUFDO0lBL0dVLHlCQUFNLEdBQWIsVUFBYyxJQUFTO1FBQ25CLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLFVBQVUsSUFBSSxHQUFHLENBQUE7SUFDM0MsQ0FBQztJQUVNLHlCQUFNLEdBQWI7UUFDSSxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQTtRQUMxQixJQUFJLENBQUMsdUJBQXVCLEdBQUcsRUFBRSxDQUFBO1FBQ2pDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLFNBQVMsR0FBRyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxzQkFBc0IsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUE7SUFDL0csQ0FBQztJQUVNLHlCQUFNLEdBQWIsVUFBYyxFQUFVO1FBQXhCLGlCQXVGQzs7UUF0RkcsSUFBSSxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDdEQsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtTQUN6QjtRQUNELElBQUksUUFBUSxTQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsbUNBQUksQ0FBQyxFQUFFLEtBQUssU0FBRyxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxDQUFDLG1DQUFJLENBQUMsQ0FBQTtRQUM1RyxJQUFJLENBQUMsaUJBQWlCLENBQUMsVUFBVSxFQUFFLFFBQVEsR0FBRyxFQUFFLENBQUMsQ0FBQTtRQUNqRCxJQUFNLFVBQVUsU0FBRyxJQUFJLENBQUMscUJBQXFCLENBQUMsMEJBQTBCLENBQUMsbUNBQUksQ0FBQyxDQUFBO1FBQzlFLE9BQU87UUFDUCxJQUFJLFVBQVUsR0FBRyxDQUFDLEVBQUU7WUFDaEIsSUFBTSxXQUFTLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRSxFQUFFLFlBQVksR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLGVBQWUsRUFBRSxDQUFBO1lBQy9GLEtBQUs7WUFDTCxJQUFJLENBQUEsV0FBUyxhQUFULFdBQVMsdUJBQVQsV0FBUyxDQUFFLEVBQUUsTUFBSyxnQkFBUSxDQUFDLFVBQVUsSUFBSSxZQUFZLEVBQUU7Z0JBQ3ZELElBQUksUUFBUSxHQUFHLFVBQVUsRUFBRTtvQkFDdkIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLEtBQUssR0FBRyxDQUFDLEVBQUUsRUFBRSxTQUFTLEVBQUUsUUFBUSxFQUFFLENBQUMsQ0FBQTtvQkFDOUQsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtpQkFDekI7cUJBQU0sSUFBSSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsRUFBRTtvQkFDNUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxJQUFJLENBQUMsQ0FBQTtvQkFDeEMsWUFBWTtvQkFDWixZQUFZLENBQUMsMEJBQTBCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRSxFQUFFLFdBQVMsQ0FBQyxNQUFNLEVBQUUsV0FBUyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO3dCQUN6SCxLQUFLO3dCQUNMLEtBQUksQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsS0FBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRSxXQUFXLEVBQUUsV0FBUyxDQUFDLEtBQUssR0FBRyxJQUFJLEVBQUUsQ0FBQyxDQUFBO3dCQUN0RixPQUFPO3dCQUNQLEtBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTtvQkFDdEQsQ0FBQyxDQUFDLENBQUE7b0JBQ0YsV0FBVztvQkFDWCxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFBO29CQUM5RCxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLDZCQUFrQixDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTtpQkFDMUY7YUFDSjtZQUNELElBQUksSUFBSSxDQUFDLHFCQUFxQixDQUFDLG1CQUFtQixDQUFDLEVBQUUsRUFBRSxXQUFXO2dCQUM5RCxJQUFJLFFBQVEsR0FBRyxVQUFVLEVBQUU7b0JBQ3ZCLElBQUksUUFBUSxLQUFLLENBQUMsRUFBRTt3QkFDaEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsZ0JBQVEsQ0FBQyxZQUFZLENBQUMsQ0FBQTtxQkFDaEQ7b0JBQ0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLEtBQUssR0FBRyxDQUFDLEVBQUUsRUFBRSxLQUFLLEVBQUUsYUFBYSxFQUFFLFNBQVMsRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFBO29CQUN4RixPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO2lCQUN6QjtnQkFDRCxJQUFJLENBQUMscUJBQXFCLENBQUMsbUJBQW1CLEVBQUUsS0FBSyxDQUFDLENBQUE7YUFDekQ7aUJBQU0sSUFBSSxJQUFJLENBQUMscUJBQXFCLENBQUMsbUJBQW1CLENBQUMsRUFBRSxFQUFFLE9BQU87Z0JBQ2pFLElBQUksUUFBUSxHQUFHLFVBQVUsRUFBRTtvQkFDdkIsSUFBSSxRQUFRLEtBQUssQ0FBQyxFQUFFO3dCQUNoQixJQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLGFBQWEsQ0FBQyxDQUFBO3dCQUNqRSxJQUFJLENBQUMsYUFBYSxFQUFFOzRCQUNoQixJQUFJLENBQUMscUJBQXFCLENBQUMsbUJBQW1CLEVBQUUsS0FBSyxDQUFDLENBQUE7NEJBQ3RELE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7eUJBQ3pCO3dCQUNELElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFRLENBQUMsYUFBYSxDQUFDLENBQUE7d0JBQzlDLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLEVBQUUsTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUE7d0JBQzlELElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsd0JBQXdCLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxPQUFPLEVBQUUsS0FBSyxNQUFJLElBQUksQ0FBQyxDQUFDLE1BQU0sRUFBRSxFQUFsQyxDQUFrQyxDQUFDLENBQUE7d0JBQzdILEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO3dCQUNyQixJQUFNLFFBQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxhQUFhLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFBO3dCQUM5RSxHQUFHLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQzs0QkFDVCxJQUFNLEdBQUcsR0FBRyxDQUFDLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxFQUFFLFFBQU0sQ0FBQyxDQUFDLFVBQVUsQ0FBQTs0QkFDdkQsSUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLENBQUE7NEJBQzFCLENBQUMsQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxVQUFVLEVBQUUsQ0FBQyxDQUFDLE1BQU0sRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsQ0FBQTt3QkFDMUYsQ0FBQyxDQUFDLENBQUE7d0JBQ0YsSUFBSSxHQUFHLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTs0QkFDaEIsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUE7eUJBQ3RFO3FCQUNKO29CQUNELE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7aUJBQ3pCO2dCQUNELElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxtQkFBbUIsRUFBRSxLQUFLLENBQUMsQ0FBQTthQUN6RDtTQUNKO1FBQ0QsT0FBTztRQUNQLElBQUksUUFBUSxJQUFJLEtBQUssSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxTQUFTLElBQUksVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFO1lBQ2xGLHlCQUF5QjtZQUN6QixJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixDQUFDLFVBQVUsQ0FBQyxFQUFFO2dCQUN6QyxJQUFJLENBQUMsTUFBTSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQyxDQUFBO2dCQUNwQyxlQUFlO2dCQUNmLElBQUksT0FBQSxJQUFJLENBQUMsTUFBTSxDQUFDLGlCQUFpQixFQUFFLDBDQUFFLEVBQUUsTUFBSyxnQkFBUSxDQUFDLFdBQVcsRUFBRTtvQkFDOUQsSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQUMsZ0JBQVEsQ0FBQyxlQUFlLENBQUMsQ0FBQTtpQkFDbkQ7YUFDSjtZQUNELFNBQVM7WUFDVCxJQUFJLENBQUMscUJBQXFCLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxDQUFBO1lBQzlDLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRTtZQUM1QixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQTtZQUN6QixJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQ3hDLHVCQUF1QjtZQUN2QixJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLGdCQUFRLENBQUMsZUFBZSxDQUFDLEVBQUU7Z0JBQ2pELElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsNkJBQWtCLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTthQUNwRjtTQUNKO1FBQ0QsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBRUQsUUFBUTtJQUNBLDhCQUFXLEdBQW5CLFVBQW9CLHVCQUFnQztRQUNoRCxPQUFPLHVCQUF1QixJQUFJLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxlQUFlLENBQUMsSUFBSSxJQUFJLENBQUMscUJBQXFCLENBQUMsUUFBUSxDQUFDLElBQUksSUFBSSxDQUFDLHFCQUFxQixDQUFDLFVBQVUsQ0FBQyxDQUFBO0lBQ3JOLENBQUM7SUFFRCxrQkFBa0I7SUFDViw4QkFBVyxHQUFuQixVQUFvQixLQUFnQixFQUFFLElBQVM7UUFDM0MsSUFBSSxDQUFDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUN0QyxJQUFJLENBQUMsdUJBQXVCLENBQUMsS0FBSyxDQUFDLEdBQUcsSUFBSSxDQUFBO1lBQzFDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQTtTQUN2QztJQUNMLENBQUM7SUFDTCxlQUFDO0FBQUQsQ0FySEEsQUFxSEMsQ0FySHFDLG9CQUFVLEdBcUgvQyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJBVFRMRV9FRkZFQ1RfVFlQRSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcbmltcG9ydCB7IEJ1ZmZUeXBlLCBIZXJvVHlwZSwgUGF3blN0YXRlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IEJhc2VBY3Rpb24gZnJvbSBcIi4vQmFzZUFjdGlvblwiO1xuaW1wb3J0IHsgQlRTdGF0ZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIjtcblxuLy8g57uT5p2f5Zue5ZCIXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBFbmRSb3VuZCBleHRlbmRzIEJhc2VBY3Rpb24ge1xuXG4gICAgcHJpdmF0ZSBpbml0RGVsYXk6IG51bWJlciA9IDBcbiAgICBwcml2YXRlIGlzQ2hhbmdlU3RhdGU6IGJvb2xlYW4gPSBmYWxzZVxuICAgIHByaXZhdGUgaXNDaGFuZ2VGaWdodGVyU3RhdGVNYXA6IHsgW2tleTogbnVtYmVyXTogYm9vbGVhbiB9ID0ge31cblxuICAgIHB1YmxpYyBvbkluaXQoY29uZjogYW55KSB7XG4gICAgICAgIHRoaXMuaW5pdERlbGF5ID0gY29uZi5wYXJhbWV0ZXJzIHx8IDI1MFxuICAgIH1cblxuICAgIHB1YmxpYyBvbk9wZW4oKSB7XG4gICAgICAgIHRoaXMuaXNDaGFuZ2VTdGF0ZSA9IGZhbHNlXG4gICAgICAgIHRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGVNYXAgPSB7fVxuICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdkZWxheScsIHRoaXMuaW5pdERlbGF5ICsgKHRoaXMuZ2V0VHJlZUJsYWNrYm9hcmREYXRhKCdhZGRSb3VuZEVuZERlbGF5VGltZScpIHx8IDApKVxuICAgIH1cblxuICAgIHB1YmxpYyBvblRpY2soZHQ6IG51bWJlcikge1xuICAgICAgICBpZiAoKHRoaXMuZ2V0VHJlZUJsYWNrYm9hcmREYXRhKCdiYXR0ZXJDb3VudCcpIHx8IDApID4gMCkge1xuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuU1VDQ0VTU1xuICAgICAgICB9XG4gICAgICAgIGxldCBjdXJyVGltZTogbnVtYmVyID0gdGhpcy5nZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWUnKSA/PyAwLCBkZWxheSA9IHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2RlbGF5JykgPz8gMFxuICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdjdXJyVGltZScsIGN1cnJUaW1lICsgZHQpXG4gICAgICAgIGNvbnN0IGFjdGlvblRpbWUgPSB0aGlzLmdldFRyZWVCbGFja2JvYXJkRGF0YSgnYWRkUm91bmRFbmRBY3Rpb25IaXRUaW1lJykgPz8gMFxuICAgICAgICAvLyDliqjkvZzml7bpl7RcbiAgICAgICAgaWYgKGFjdGlvblRpbWUgPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBoZXJvU2tpbGwgPSB0aGlzLnRhcmdldC5nZXRQb3J0cmF5YWxTa2lsbCgpLCBhdHRhY2tUYXJnZXQgPSB0aGlzLnRhcmdldC5nZXRBdHRhY2tUYXJnZXQoKVxuICAgICAgICAgICAgLy8g5byg6L69XG4gICAgICAgICAgICBpZiAoaGVyb1NraWxsPy5pZCA9PT0gSGVyb1R5cGUuWkhBTkdfTElBTyAmJiBhdHRhY2tUYXJnZXQpIHtcbiAgICAgICAgICAgICAgICBpZiAoY3VyclRpbWUgPCBhY3Rpb25UaW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2hhbmdlU3RhdGUoUGF3blN0YXRlLlNLSUxMICsgMSwgeyBza2lsbE5hbWU6ICdiYXR0ZXInIH0pXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc0FjdGlvbicpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzQWN0aW9uJywgdHJ1ZSlcbiAgICAgICAgICAgICAgICAgICAgLy8g5a+56IyD5Zu05pWM5Lq66YCg5oiQ5Lyk5a6zXG4gICAgICAgICAgICAgICAgICAgIGF0dGFja1RhcmdldC5nZXRDYW5BdHRhY2tGaWdodGVyQnlSYW5nZSh0aGlzLnRhcmdldC5nZXRDYW5BdHRhY2tGaWdodGVycygpLCBoZXJvU2tpbGwudGFyZ2V0LCBoZXJvU2tpbGwucGFyYW1zLCAnJykuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOWPl+WHu1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jdHJsLm9uSGl0QmFzZVRydWVEYW1hZ2UodGhpcy50YXJnZXQsIG0sIHsgYXR0YWNrQW1lbmQ6IGhlcm9Ta2lsbC52YWx1ZSAqIDAuMDEgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaSreaUvuaViOaenFxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoWzIwODAwMl0sIG0uZ2V0UG9pbnQoKSlcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgLy8g5re75Yqg5YeP5LykYnVmZlxuICAgICAgICAgICAgICAgICAgICB0aGlzLnRhcmdldC5hZGRCdWZmKEJ1ZmZUeXBlLkFTU0FVTFQsIHRoaXMudGFyZ2V0LmdldFVpZCgpLCAxKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmN0cmwucGxheUJhdHRsZUVmZmVjdChCQVRUTEVfRUZGRUNUX1RZUEUuREFNQUdFX1JFRFVDVElPTiwgdGhpcy50YXJnZXQuZ2V0UG9pbnQoKSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAodGhpcy5nZXRUcmVlQmxhY2tib2FyZERhdGEoJ2lzUGxheUpKU2hpZWxkRW5kJykpIHsgLy/mnLrnlLLnmq7ogqTnmoTmlLbnm77liqjkvZxcbiAgICAgICAgICAgICAgICBpZiAoY3VyclRpbWUgPCBhY3Rpb25UaW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXJyVGltZSA9PT0gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50YXJnZXQucmVtb3ZlQnVmZihCdWZmVHlwZS5TVEFORF9TSElFTEQpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuU0tJTEwgKyAxLCB7IHNvdW5kOiAnc291bmRfMTI3XzEnLCBza2lsbE5hbWU6ICdzaGllbGRfZW5kJyB9KVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuc2V0VHJlZUJsYWNrYm9hcmREYXRhKCdpc1BsYXlKSlNoaWVsZEVuZCcsIGZhbHNlKVxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmdldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNQb2lzb25lZFdpbmVFbmQnKSkgeyAvL+m4qeavkiDniIbngrhcbiAgICAgICAgICAgICAgICBpZiAoY3VyclRpbWUgPCBhY3Rpb25UaW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXJyVGltZSA9PT0gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgUE9JU09ORURfV0lORSA9IHRoaXMudGFyZ2V0LmdldEJ1ZmYoQnVmZlR5cGUuUE9JU09ORURfV0lORSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghUE9JU09ORURfV0lORSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0VHJlZUJsYWNrYm9hcmREYXRhKCdpc1BvaXNvbmVkV2luZUVuZCcsIGZhbHNlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0LnJlbW92ZUJ1ZmYoQnVmZlR5cGUuUE9JU09ORURfV0lORSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVpZCA9IHRoaXMudGFyZ2V0LmdldFVpZCgpLCBjYW1wID0gdGhpcy50YXJnZXQuZ2V0Q2FtcCgpXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhcnIgPSB0aGlzLnRhcmdldC5nZXRDYW5BdHRhY2tSYW5nZUZpZ2h0ZXIodGhpcy5jdHJsLmdldEZpZ2h0ZXJzKCksIDIsIDcsIHVpZCwgbSA9PiBtLmdldENhbXAoKSAhPT0gY2FtcCB8fCBtLmlzRmxhZygpKVxuICAgICAgICAgICAgICAgICAgICAgICAgYXJyLnB1c2godGhpcy50YXJnZXQpXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkYW1hZ2UgPSBNYXRoLnJvdW5kKFBPSVNPTkVEX1dJTkUudmFsdWUgKiAoUE9JU09ORURfV0lORS5sdiArIDkpICogMC4wMSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyci5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbCA9IG0uaGl0UHJlcERhbWFnZUhhbmRsZSgwLCBkYW1hZ2UpLnRydWVEYW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2ID0gbS5vbkhpdCh2YWwsIFtdKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG0uY2hhbmdlU3RhdGUoUGF3blN0YXRlLkhJVCwgeyB0cnVlRGFtYWdlOiB2LmRhbWFnZSwgaGVhbDogdi5oZWFsLCBpc0RpZTogbS5pc0RpZSgpIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFyci5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoWzIxMDAwMl0sIHRoaXMudGFyZ2V0LmdldFBvaW50KCksICd0b3AnKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRUcmVlQmxhY2tib2FyZERhdGEoJ2lzUG9pc29uZWRXaW5lRW5kJywgZmFsc2UpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgLy8g57uT5p2f5pe26Ze0XG4gICAgICAgIGlmIChjdXJyVGltZSA+PSBkZWxheSB8fCAhdGhpcy5pc0FjdGlvbmluZyhkZWxheSA+IHRoaXMuaW5pdERlbGF5IHx8IGFjdGlvblRpbWUgPiAwKSkge1xuICAgICAgICAgICAgLy8g5aaC5p6c6L+Y5rKh5pyJ5pS75Ye76L+HIOa4heeQhuW9k+WJjeeahOebruaghyDkuIvmrKHph43mlrDpgIlcbiAgICAgICAgICAgIGlmICghdGhpcy5nZXRUcmVlQmxhY2tib2FyZERhdGEoJ2lzQXR0YWNrJykpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRhcmdldC5jaGFuZ2VBdHRhY2tUYXJnZXQobnVsbClcbiAgICAgICAgICAgICAgICAvLyDlpI/kvq/muIog5riF55CG5aWU6KKtYnVmZlxuICAgICAgICAgICAgICAgIGlmICh0aGlzLnRhcmdldC5nZXRQb3J0cmF5YWxTa2lsbCgpPy5pZCA9PT0gSGVyb1R5cGUuWElBX0hPVVlVQU4pIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy50YXJnZXQucmVtb3ZlQnVmZihCdWZmVHlwZS5MT05HX1JBTkdFX1JBSUQpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8g5qCH6K6w57uT5p2f5Zue5ZCIXG4gICAgICAgICAgICB0aGlzLnNldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNSb3VuZEVuZCcsIHRydWUpXG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5TVUNDRVNTXG4gICAgICAgIH0gZWxzZSBpZiAoIXRoaXMuaXNDaGFuZ2VTdGF0ZSkge1xuICAgICAgICAgICAgdGhpcy5pc0NoYW5nZVN0YXRlID0gdHJ1ZVxuICAgICAgICAgICAgdGhpcy50YXJnZXQuY2hhbmdlU3RhdGUoUGF3blN0YXRlLlNUQU5EKVxuICAgICAgICAgICAgLy8g5piv5ZCm5Y+v5Lul57un57ut5Zue5ZCIIOi/memHjOS4u+imgeaYr+aSreaUvuinpuWPkeWKqOeUu1xuICAgICAgICAgICAgaWYgKHRoaXMudGFyZ2V0LmlzSGFzQnVmZihCdWZmVHlwZS5DT05USU5VRV9BQ1RJT04pKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoQkFUVExFX0VGRkVDVF9UWVBFLlNBTkRfQ0xPQ0ssIHRoaXMudGFyZ2V0LmdldFBvaW50KCkpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgIH1cblxuICAgIC8vIOaYr+WQpuihjOWKqOi/h1xuICAgIHByaXZhdGUgaXNBY3Rpb25pbmcoaGFzQWRkUm91bmRFbmREZWxheVRpbWU6IGJvb2xlYW4pIHtcbiAgICAgICAgcmV0dXJuIGhhc0FkZFJvdW5kRW5kRGVsYXlUaW1lIHx8IHRoaXMuZ2V0VHJlZUJsYWNrYm9hcmREYXRhKCdpc0RlZHVjdEhwQWN0aW9uJykgfHwgdGhpcy5nZXRUcmVlQmxhY2tib2FyZERhdGEoJ2lzQmxvb2RBY3Rpb24nKSB8fCB0aGlzLmdldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNNb3ZlJykgfHwgdGhpcy5nZXRUcmVlQmxhY2tib2FyZERhdGEoJ2lzQXR0YWNrJylcbiAgICB9XG5cbiAgICAvLyDorr7nva7lvZPliY3lo6vlhbXnirbmgIEg5pu05paw6KeG5Zu+5L+h5oGvXG4gICAgcHJpdmF0ZSBjaGFuZ2VTdGF0ZShzdGF0ZTogUGF3blN0YXRlLCBkYXRhOiBhbnkpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzQ2hhbmdlRmlnaHRlclN0YXRlTWFwW3N0YXRlXSkge1xuICAgICAgICAgICAgdGhpcy5pc0NoYW5nZUZpZ2h0ZXJTdGF0ZU1hcFtzdGF0ZV0gPSB0cnVlXG4gICAgICAgICAgICB0aGlzLnRhcmdldC5jaGFuZ2VTdGF0ZShzdGF0ZSwgZGF0YSlcbiAgICAgICAgfVxuICAgIH1cbn0iXX0=