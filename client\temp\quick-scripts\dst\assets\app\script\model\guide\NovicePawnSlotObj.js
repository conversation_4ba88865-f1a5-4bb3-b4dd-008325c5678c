
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NovicePawnSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '892029eoNJDG6yKxFeah280', 'NovicePawnSlotObj');
// app/script/model/guide/NovicePawnSlotObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var PawnSlotObj_1 = require("../main/PawnSlotObj");
// 士兵槽位
var NovicePawnSlotObj = /** @class */ (function (_super) {
    __extends(NovicePawnSlotObj, _super);
    function NovicePawnSlotObj() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NovicePawnSlotObj.prototype.strip = function () {
        return {
            lv: this.lv,
            id: this.id,
            selectIds: this.selectIds,
            resetCount: this.resetCount,
        };
    };
    NovicePawnSlotObj.prototype.fromDB = function (data) {
        this.lv = data.lv;
        this.id = data.id;
        this.selectIds = data.selectIds;
        this.resetCount = data.resetCount;
        return this;
    };
    NovicePawnSlotObj.prototype.toDB = function () {
        return {
            lv: this.lv,
            id: this.id,
            selectIds: this.selectIds,
            resetCount: this.resetCount,
        };
    };
    return NovicePawnSlotObj;
}(PawnSlotObj_1.default));
exports.default = NovicePawnSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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