
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/LobbyModeCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7bddethc2ZCvqKkGIl0mqYo', 'LobbyModeCmpt');
// app/script/view/lobby/LobbyModeCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 滑动模式组件
var LobbyModeCmpt = /** @class */ (function (_super) {
    __extends(LobbyModeCmpt, _super);
    function LobbyModeCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.touchNode = null;
        _this.barNode = null;
        _this.ROLL_TIME = 0.3;
        _this.touchId = -1;
        _this.isMove = false;
        _this.items = [];
        _this.deltaX = 0;
        _this.modes = [];
        _this.currCentreMode = -1; //当前居中的mode
        _this.onUpdateMode = null;
        _this.onPlayModeRoll = null;
        _this.autoRolling = false; //自动滚动中
        _this.lockTouch = false; //是否禁止滑动
        _this._temp_vec2 = cc.v2();
        return _this;
    }
    LobbyModeCmpt.prototype.onLoad = function () {
        this.touchNode.on(cc.Node.EventType.TOUCH_START, this.onListTouchStart, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onListTouchMove, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_END, this.onListTouchEnd, this);
        this.touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onListTouchEnd, this);
        this.touchNode.SetSwallowTouches(false);
    };
    LobbyModeCmpt.prototype.init = function (onUpdateMode, onPlayModeRoll) {
        this.onUpdateMode = onUpdateMode;
        this.onPlayModeRoll = onPlayModeRoll;
        return this;
    };
    LobbyModeCmpt.prototype.reset = function (modes, curModeType) {
        var _this = this;
        var modeMap = {};
        this.modes = modes = modes.concat([100]);
        // 初始化当前的
        this.items = modes.map(function (type, i) {
            modeMap[type] = true;
            var node = _this.touchNode.Child(type);
            return { type: type, node: node, modeIndex: i };
        });
        // bar
        this.barNode.Items(modes.length, function (it, _, i) { return it.Data = i; });
        // 隐藏多余的
        this.touchNode.children.forEach(function (m) { return m.active = !!modeMap[m.name]; });
        // 刷新
        this.updateItemsByCentreMode(curModeType);
    };
    // 更新所有mode的位置 根据当前mode居中
    LobbyModeCmpt.prototype.updateItemsByCentreMode = function (type) {
        this.currCentreMode = type;
        var len = this.modes.length;
        var index = this.modes.indexOf(type);
        var indexs = [index];
        for (var i = 0; i < len - 1; i++) {
            var dir = i % 2 === 0 ? 1 : -1;
            var idx = ut.loopValue(index + dir, len);
            if (dir === 1) {
                indexs.push(idx);
            }
            else {
                indexs.unshift(idx);
            }
        }
        this.items.sort(function (a, b) { return indexs.indexOf(a.modeIndex) - indexs.indexOf(b.modeIndex); });
        // 刷新位置
        var ci = this.items.findIndex(function (m) { return m.type === type; }), winWidth = cc.winSize.width;
        this.items.forEach(function (m, i) {
            m.itemIndex = i;
            m.node.x = (i - ci) * winWidth;
        });
        // 刷新bar
        this.updateBar(index);
        // 刷新mode信息
        this.onUpdateMode(type);
    };
    // 刷新bar
    LobbyModeCmpt.prototype.updateBar = function (index) {
        this.barNode.children.forEach(function (m, i) {
            var select = index === i;
            m.Component(cc.Button).interactable = !select;
            m.Component(cc.MultiFrame).setFrame(select);
        });
    };
    // 刷新信息
    LobbyModeCmpt.prototype.updateMode = function () {
        this.updateItemsByCentreMode(this.currCentreMode);
    };
    LobbyModeCmpt.prototype.setLockTouch = function (val) {
        this.lockTouch = val;
    };
    // 列表的触摸
    LobbyModeCmpt.prototype.onListTouchStart = function (event) {
        if (this.lockTouch || this.touchId !== -1 || this.isRolling()) {
            return;
        }
        this.isMove = false;
        this.deltaX = 0;
        this.touchId = event.getID();
    };
    LobbyModeCmpt.prototype.onListTouchMove = function (event) {
        if (this.lockTouch || this.touchId !== event.getID()) {
            return;
        }
        else if (event.getStartLocation().sub(event.getLocation(), this._temp_vec2).mag() < 7) {
            return;
        }
        this.isMove = true;
        var deltaX = this.deltaX = event.getDeltaX();
        this.items.forEach(function (m) {
            m.node.x += deltaX;
        });
    };
    LobbyModeCmpt.prototype.onListTouchEnd = function (event) {
        var _this = this;
        if (this.lockTouch || this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        this.deltaX = 0;
        if (this.isMove) {
            this.isMove = false;
            var item = this.items.find(function (m) { return m.type === _this.currCentreMode; });
            var x = item.node.x;
            if (Math.abs(x) > cc.winSize.width / 4) {
                var dir = ut.normalizeNumber(x);
                this.autoRollToMode(this.items[item.itemIndex - dir].type);
            }
            else {
                this.autoRollToMode(item.type);
            }
        }
    };
    // 自动滚动到指定mode
    LobbyModeCmpt.prototype.autoRollToMode = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var isChange, target, winWidth, centre, cx, ci, dir, itemCount, i, i, offsetX, time;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mc.lockTouch('lobby_auto_roll');
                        isChange = this.currCentreMode !== type;
                        target = this.items.find(function (m) { return m.type === type; });
                        winWidth = cc.winSize.width;
                        // 滚动之前先把 目标反方向的所有移到另外一边 如果相同则不用
                        if (isChange) {
                            centre = this.items.find(function (m) { return m.type === _this.currCentreMode; });
                            cx = centre.node.x, ci = centre.itemIndex;
                            dir = ut.normalizeNumber(cx), itemCount = this.items.length;
                            if (dir === 1) { //右边移到左边
                                for (i = ci + 1; i < itemCount; i++) {
                                    this.items[i].node.x = ((0 - ci) + (ci - i)) * winWidth + cx;
                                }
                            }
                            else if (dir === -1) { //左边移到右边
                                for (i = ci - 1; i >= 0; i--) {
                                    this.items[i].node.x = ((itemCount - ci - 1) + (ci - i)) * winWidth + cx;
                                }
                            }
                        }
                        offsetX = 0 - target.node.x;
                        time = cc.misc.clampf(this.ROLL_TIME / winWidth * Math.abs(offsetX), 0.1, this.ROLL_TIME);
                        this.items.forEach(function (m) { return cc.tween(m.node).by(time, { x: offsetX }).start(); });
                        this.playModeRoll(true, isChange);
                        return [4 /*yield*/, ut.wait(time)];
                    case 1:
                        _a.sent();
                        this.playModeRoll(false, isChange);
                        mc.unlockTouch('lobby_auto_roll');
                        // 刷新位置
                        if (isChange) {
                            this.updateItemsByCentreMode(type);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 自动滚动到下一个
    LobbyModeCmpt.prototype.autoRollNextMode = function (add) {
        return __awaiter(this, void 0, void 0, function () {
            var type, index;
            return __generator(this, function (_a) {
                type = this.currCentreMode;
                index = this.modes.indexOf(type);
                if (index >= 0) {
                    this.autoRollToMode(this.modes[ut.loopValue(index + add, this.modes.length)]);
                }
                return [2 /*return*/];
            });
        });
    };
    // 自动滚动到指定mode
    LobbyModeCmpt.prototype.autoRollToIndex = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var type;
            return __generator(this, function (_a) {
                if (index >= 0 && index < this.modes.length) {
                    type = this.modes[index];
                    if (this.currCentreMode !== type) {
                        this.autoRollToMode(type);
                    }
                }
                return [2 /*return*/];
            });
        });
    };
    LobbyModeCmpt.prototype.playModeRoll = function (val, isChange) {
        this.autoRolling = val;
        this.onPlayModeRoll(val, isChange);
    };
    LobbyModeCmpt.prototype.isRolling = function () {
        return this.autoRolling || this.deltaX > 0;
    };
    __decorate([
        property(cc.Node)
    ], LobbyModeCmpt.prototype, "touchNode", void 0);
    __decorate([
        property(cc.Node)
    ], LobbyModeCmpt.prototype, "barNode", void 0);
    LobbyModeCmpt = __decorate([
        ccclass
    ], LobbyModeCmpt);
    return LobbyModeCmpt;
}(cc.Component));
exports.default = LobbyModeCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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