
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaUIPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2e3406YJLNEbbyLrMRcDmIK', 'AreaUIPnlCtrl');
// app/script/view/area/AreaUIPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AreaWatchChatCmpt_1 = require("./AreaWatchChatCmpt");
var ccclass = cc._decorator.ccclass;
var AreaUIPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaUIPnlCtrl, _super);
    function AreaUIPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.battleInfoNode_ = null; // path://battle_info_n_nbe
        _this.watchNode_ = null; // path://top/other_res/watch_n
        //@end
        _this.isHideTaskTip = false;
        _this.model = null;
        return _this;
    }
    AreaUIPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return [
            (_a = {}, _a[EventType_1.default.REINIT_AREA_UI] = this.onReinitAreaUI, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_BATTLE_ARMY_COUNT_SHOW] = this.onUpdateBattleArmyCountShow, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI] = this.onUpdateAreaBattleTimeUI, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_AREA_WATCH_PLAYER] = this.onUpdateAreaWatchPlayer, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_EXP_BOOK] = this.onUpdateWatchNodeY, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_IRON] = this.onUpdateWatchNodeY, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_UPSCROLL] = this.onUpdateWatchNodeY, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_FIXATOR] = this.onUpdateWatchNodeY, _h.enter = true, _h),
        ];
    };
    AreaUIPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isClean: false, isAct: false, isMask: false });
                return [2 /*return*/];
            });
        });
    };
    AreaUIPnlCtrl.prototype.onEnter = function (data) {
        this.model = data;
        this.updateBattleTime();
        this.updateWatchPlayerCount();
        this.updateWatchPlayerNodeY();
        this.watchNode_.Child('chat', AreaWatchChatCmpt_1.default).init(data.index);
    };
    AreaUIPnlCtrl.prototype.onRemove = function () {
        this.watchNode_.Child('chat', AreaWatchChatCmpt_1.default).clean();
        this.updateShowGuideTaskTip(false); //还原任务提示
    };
    AreaUIPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://battle_info_n_nbe
    AreaUIPnlCtrl.prototype.onClickBattleInfo = function (event, data) {
        if (this.model) {
            ViewHelper_1.viewHelper.showPnl('area/BattleInfo', this.model);
        }
    };
    // path://top/other_res/watch_n/people_be
    AreaUIPnlCtrl.prototype.onClickPeople = function (event, data) {
        if (this.model) {
            ViewHelper_1.viewHelper.showPnl('area/AreaWatchList', this.model.index);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    //
    AreaUIPnlCtrl.prototype.onReinitAreaUI = function (data) {
        this.model = data;
        this.updateBattleTime();
    };
    // 刷新战斗中军队数量显示
    AreaUIPnlCtrl.prototype.onUpdateBattleArmyCountShow = function () {
        this.updateBattleArmyCount();
    };
    // 刷新区域战斗时间显示
    AreaUIPnlCtrl.prototype.onUpdateAreaBattleTimeUI = function (index) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            this.updateBattleTime();
        }
    };
    // 刷新区域观战玩家列表
    AreaUIPnlCtrl.prototype.onUpdateAreaWatchPlayer = function (index) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            this.updateWatchPlayerCount();
        }
    };
    AreaUIPnlCtrl.prototype.onUpdateWatchNodeY = function () {
        this.updateWatchPlayerNodeY();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新战斗时间
    AreaUIPnlCtrl.prototype.updateBattleTime = function () {
        var _a;
        var isBattleing = this.battleInfoNode_.active = !!((_a = this.model) === null || _a === void 0 ? void 0 : _a.isBattleing());
        if (isBattleing) {
            var root = this.battleInfoNode_.Child('root');
            root.Child('time/val', cc.LabelTimer).setEndTime(Constant_1.BATTLE_MAX_TIME).run(this.model.getBattleElapsedTime() * 0.001);
            root.Child('mul/val', cc.Label).string = "(" + this.model.getFspModel().getMul() + "x)";
            this.updateBattleArmyCount();
        }
        this.updateShowGuideTaskTip(isBattleing);
    };
    // 刷新是否显示任务提示
    AreaUIPnlCtrl.prototype.updateShowGuideTaskTip = function (isBattleing) {
        if (this.isHideTaskTip !== isBattleing) {
            this.isHideTaskTip = isBattleing;
            if (isBattleing) {
                this.emit(EventType_1.default.CHANGE_SHOW_GUIDE_TASK_TIP, false);
            }
            else {
                this.emit(EventType_1.default.CHANGE_SHOW_GUIDE_TASK_TIP, GameHelper_1.gameHpr.task.isShowTaskTip());
            }
        }
    };
    // 刷新战斗中 军队数量
    AreaUIPnlCtrl.prototype.updateBattleArmyCount = function () {
        var _a;
        var fsp = (_a = this.model) === null || _a === void 0 ? void 0 : _a.getFspModel();
        if (!fsp) {
            return;
        }
        var root = this.battleInfoNode_.Child('root'), addTimesNode = this.battleInfoNode_.Child('add_times');
        var owner = this.model.owner, attacker = 0, defender = 0;
        // 计算各个军队数量
        this.model.armys.forEach(function (m) {
            if (m.getPawnActCount() === 0) {
                return;
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defender += 1;
            }
            else {
                attacker += 1;
            }
        });
        // 从这个区域开始行军的军队数量
        var index = this.model.index;
        GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.armyIndex === index; }).forEach(function (m) {
            if (m.autoRevoke) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defender += 1;
            }
            else {
                attacker += 1;
            }
        });
        root.Child('attacker/val', cc.Label).string = '' + attacker;
        root.Child('defender/val', cc.Label).string = '' + defender;
        addTimesNode.Child('attacker/val', cc.Label).string = ut.pad(fsp.getAttackerArmyAcc());
        addTimesNode.Child('defender/val', cc.Label).string = ut.pad(fsp.getDefenderArmyAcc());
    };
    // 刷新观战人数
    AreaUIPnlCtrl.prototype.updateWatchPlayerCount = function () {
        var count = GameHelper_1.gameHpr.areaCenter.getAreaWatchPlayersByIndex(this.model.index).length;
        var node = this.watchNode_.Child('people_be');
        if (node.active = count >= 2) {
            node.Child('val', cc.Label).string = '' + Math.min(99, count);
        }
    };
    AreaUIPnlCtrl.prototype.updateWatchPlayerNodeY = function () {
        var player = GameHelper_1.gameHpr.player, count = 0;
        if (player.getExpBook() > 0) {
            count += 1;
        }
        if (player.getIron() > 0) {
            count += 1;
        }
        if (player.getUpScroll() > 0) {
            count += 1;
        }
        if (player.getFixator() > 0) {
            count += 1;
        }
        this.watchNode_.y = -((count * 48 - 8) + 20);
    };
    AreaUIPnlCtrl = __decorate([
        ccclass
    ], AreaUIPnlCtrl);
    return AreaUIPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaUIPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGFyZWFcXEFyZWFVSVBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQWlFO0FBQ2pFLDBEQUFxRDtBQUNyRCw2REFBeUQ7QUFDekQsNkRBQTREO0FBRTVELHlEQUFvRDtBQUU1QyxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUEyQyxpQ0FBYztJQUF6RDtRQUFBLHFFQStLQztRQTdLRywwQkFBMEI7UUFDbEIscUJBQWUsR0FBWSxJQUFJLENBQUEsQ0FBQywyQkFBMkI7UUFDM0QsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQywrQkFBK0I7UUFDbEUsTUFBTTtRQUVFLG1CQUFhLEdBQVksS0FBSyxDQUFBO1FBQzlCLFdBQUssR0FBWSxJQUFJLENBQUE7O0lBdUtqQyxDQUFDO0lBcktVLHVDQUFlLEdBQXRCOztRQUNJLE9BQU87c0JBQ0QsR0FBQyxtQkFBUyxDQUFDLGNBQWMsSUFBRyxJQUFJLENBQUMsY0FBYyxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM1RCxHQUFDLG1CQUFTLENBQUMsNkJBQTZCLElBQUcsSUFBSSxDQUFDLDJCQUEyQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN4RixHQUFDLG1CQUFTLENBQUMsMEJBQTBCLElBQUcsSUFBSSxDQUFDLHdCQUF3QixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNsRixHQUFDLG1CQUFTLENBQUMsd0JBQXdCLElBQUcsSUFBSSxDQUFDLHVCQUF1QixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUMvRSxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDakUsR0FBQyxtQkFBUyxDQUFDLFdBQVcsSUFBRyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzdELEdBQUMsbUJBQVMsQ0FBQyxlQUFlLElBQUcsSUFBSSxDQUFDLGtCQUFrQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNqRSxHQUFDLG1CQUFTLENBQUMsY0FBYyxJQUFHLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxRQUFLLEdBQUUsSUFBSTtTQUNyRSxDQUFBO0lBQ0wsQ0FBQztJQUVZLGdDQUFRLEdBQXJCOzs7Z0JBQ0ksSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLE9BQU8sRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQTs7OztLQUNqRTtJQUVNLCtCQUFPLEdBQWQsVUFBZSxJQUFhO1FBQ3hCLElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFBO1FBQ3ZCLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO1FBQzdCLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO1FBQzdCLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSwyQkFBaUIsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDckUsQ0FBQztJQUVNLGdDQUFRLEdBQWY7UUFDSSxJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsMkJBQWlCLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUN4RCxJQUFJLENBQUMsc0JBQXNCLENBQUMsS0FBSyxDQUFDLENBQUEsQ0FBQyxRQUFRO0lBQy9DLENBQUM7SUFFTSwrQkFBTyxHQUFkO0lBQ0EsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0IsMkJBQTJCO0lBQzNCLHlDQUFpQixHQUFqQixVQUFrQixLQUEwQixFQUFFLElBQVk7UUFDdEQsSUFBSSxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ1osdUJBQVUsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3BEO0lBQ0wsQ0FBQztJQUVELHlDQUF5QztJQUN6QyxxQ0FBYSxHQUFiLFVBQWMsS0FBMEIsRUFBRSxJQUFZO1FBQ2xELElBQUksSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNaLHVCQUFVLENBQUMsT0FBTyxDQUFDLG9CQUFvQixFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7U0FDN0Q7SUFDTCxDQUFDO0lBQ0QsTUFBTTtJQUNOLGlIQUFpSDtJQUVqSCxFQUFFO0lBQ00sc0NBQWMsR0FBdEIsVUFBdUIsSUFBYTtRQUNoQyxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQTtRQUNqQixJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtJQUMzQixDQUFDO0lBRUQsY0FBYztJQUNOLG1EQUEyQixHQUFuQztRQUNJLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxDQUFBO0lBQ2hDLENBQUM7SUFFRCxhQUFhO0lBQ0wsZ0RBQXdCLEdBQWhDLFVBQWlDLEtBQWE7O1FBQzFDLElBQUksT0FBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLE1BQUssS0FBSyxFQUFFO1lBQzdCLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxDQUFBO1NBQzFCO0lBQ0wsQ0FBQztJQUVELGFBQWE7SUFDTCwrQ0FBdUIsR0FBL0IsVUFBZ0MsS0FBYTs7UUFDekMsSUFBSSxPQUFBLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssTUFBSyxLQUFLLEVBQUU7WUFDN0IsSUFBSSxDQUFDLHNCQUFzQixFQUFFLENBQUE7U0FDaEM7SUFDTCxDQUFDO0lBRU8sMENBQWtCLEdBQTFCO1FBQ0ksSUFBSSxDQUFDLHNCQUFzQixFQUFFLENBQUE7SUFDakMsQ0FBQztJQUNELGlIQUFpSDtJQUVqSCxTQUFTO0lBQ0Qsd0NBQWdCLEdBQXhCOztRQUNJLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsTUFBTSxHQUFHLENBQUMsUUFBQyxJQUFJLENBQUMsS0FBSywwQ0FBRSxXQUFXLEdBQUUsQ0FBQTtRQUM3RSxJQUFJLFdBQVcsRUFBRTtZQUNiLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO1lBQy9DLElBQUksQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxVQUFVLENBQUMsMEJBQWUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLG9CQUFvQixFQUFFLEdBQUcsS0FBSyxDQUFDLENBQUE7WUFDaEgsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxNQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLENBQUMsTUFBTSxFQUFFLE9BQUksQ0FBQTtZQUNsRixJQUFJLENBQUMscUJBQXFCLEVBQUUsQ0FBQTtTQUMvQjtRQUNELElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxXQUFXLENBQUMsQ0FBQTtJQUM1QyxDQUFDO0lBRUQsYUFBYTtJQUNMLDhDQUFzQixHQUE5QixVQUErQixXQUFvQjtRQUMvQyxJQUFJLElBQUksQ0FBQyxhQUFhLEtBQUssV0FBVyxFQUFFO1lBQ3BDLElBQUksQ0FBQyxhQUFhLEdBQUcsV0FBVyxDQUFBO1lBQ2hDLElBQUksV0FBVyxFQUFFO2dCQUNiLElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQywwQkFBMEIsRUFBRSxLQUFLLENBQUMsQ0FBQTthQUN6RDtpQkFBTTtnQkFDSCxJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsMEJBQTBCLEVBQUUsb0JBQU8sQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUMsQ0FBQTthQUNoRjtTQUNKO0lBQ0wsQ0FBQztJQUVELGFBQWE7SUFDTCw2Q0FBcUIsR0FBN0I7O1FBQ0ksSUFBTSxHQUFHLFNBQUcsSUFBSSxDQUFDLEtBQUssMENBQUUsV0FBVyxFQUFFLENBQUE7UUFDckMsSUFBSSxDQUFDLEdBQUcsRUFBRTtZQUNOLE9BQU07U0FDVDtRQUNELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLFlBQVksR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsQ0FBQTtRQUN2RyxJQUFJLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxRQUFRLEdBQUcsQ0FBQyxFQUFFLFFBQVEsR0FBRyxDQUFDLENBQUE7UUFDeEQsV0FBVztRQUNYLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7WUFDdEIsSUFBSSxDQUFDLENBQUMsZUFBZSxFQUFFLEtBQUssQ0FBQyxFQUFFO2dCQUMzQixPQUFNO2FBQ1Q7aUJBQU0sSUFBSSxvQkFBTyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLEVBQUU7Z0JBQ25ELFFBQVEsSUFBSSxDQUFDLENBQUE7YUFDaEI7aUJBQU07Z0JBQ0gsUUFBUSxJQUFJLENBQUMsQ0FBQTthQUNoQjtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsaUJBQWlCO1FBQ2pCLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFBO1FBQzlCLG9CQUFPLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxTQUFTLEtBQUssS0FBSyxFQUFyQixDQUFxQixDQUFDLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUNsRSxJQUFJLENBQUMsQ0FBQyxVQUFVLEVBQUU7YUFDakI7aUJBQU0sSUFBSSxvQkFBTyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLEVBQUU7Z0JBQ25ELFFBQVEsSUFBSSxDQUFDLENBQUE7YUFDaEI7aUJBQU07Z0JBQ0gsUUFBUSxJQUFJLENBQUMsQ0FBQTthQUNoQjtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLEdBQUcsUUFBUSxDQUFBO1FBQzNELElBQUksQ0FBQyxLQUFLLENBQUMsY0FBYyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsRUFBRSxHQUFHLFFBQVEsQ0FBQTtRQUMzRCxZQUFZLENBQUMsS0FBSyxDQUFDLGNBQWMsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLGtCQUFrQixFQUFFLENBQUMsQ0FBQTtRQUN0RixZQUFZLENBQUMsS0FBSyxDQUFDLGNBQWMsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLGtCQUFrQixFQUFFLENBQUMsQ0FBQTtJQUMxRixDQUFDO0lBRUQsU0FBUztJQUNELDhDQUFzQixHQUE5QjtRQUNJLElBQU0sS0FBSyxHQUFHLG9CQUFPLENBQUMsVUFBVSxDQUFDLDBCQUEwQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxDQUFBO1FBQ3BGLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFBO1FBQy9DLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxLQUFLLElBQUksQ0FBQyxFQUFFO1lBQzFCLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFLEtBQUssQ0FBQyxDQUFBO1NBQ2hFO0lBQ0wsQ0FBQztJQUVPLDhDQUFzQixHQUE5QjtRQUNJLElBQUksTUFBTSxHQUFHLG9CQUFPLENBQUMsTUFBTSxFQUFFLEtBQUssR0FBRyxDQUFDLENBQUE7UUFDdEMsSUFBSSxNQUFNLENBQUMsVUFBVSxFQUFFLEdBQUcsQ0FBQyxFQUFFO1lBQ3pCLEtBQUssSUFBSSxDQUFDLENBQUE7U0FDYjtRQUNELElBQUksTUFBTSxDQUFDLE9BQU8sRUFBRSxHQUFHLENBQUMsRUFBRTtZQUN0QixLQUFLLElBQUksQ0FBQyxDQUFBO1NBQ2I7UUFDRCxJQUFJLE1BQU0sQ0FBQyxXQUFXLEVBQUUsR0FBRyxDQUFDLEVBQUU7WUFDMUIsS0FBSyxJQUFJLENBQUMsQ0FBQTtTQUNiO1FBQ0QsSUFBSSxNQUFNLENBQUMsVUFBVSxFQUFFLEdBQUcsQ0FBQyxFQUFFO1lBQ3pCLEtBQUssSUFBSSxDQUFDLENBQUE7U0FDYjtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEtBQUssR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUE7SUFDaEQsQ0FBQztJQTlLZ0IsYUFBYTtRQURqQyxPQUFPO09BQ2EsYUFBYSxDQStLakM7SUFBRCxvQkFBQztDQS9LRCxBQStLQyxDQS9LMEMsRUFBRSxDQUFDLFdBQVcsR0ErS3hEO2tCQS9Lb0IsYUFBYSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJBVFRMRV9NQVhfVElNRSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcclxuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiO1xyXG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xyXG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xyXG5pbXBvcnQgQXJlYU9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9BcmVhT2JqXCI7XHJcbmltcG9ydCBBcmVhV2F0Y2hDaGF0Q21wdCBmcm9tIFwiLi9BcmVhV2F0Y2hDaGF0Q21wdFwiO1xyXG5cclxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQXJlYVVJUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcclxuXHJcbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxyXG4gICAgcHJpdmF0ZSBiYXR0bGVJbmZvTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9iYXR0bGVfaW5mb19uX25iZVxyXG4gICAgcHJpdmF0ZSB3YXRjaE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vdG9wL290aGVyX3Jlcy93YXRjaF9uXHJcbiAgICAvL0BlbmRcclxuXHJcbiAgICBwcml2YXRlIGlzSGlkZVRhc2tUaXA6IGJvb2xlYW4gPSBmYWxzZVxyXG4gICAgcHJpdmF0ZSBtb2RlbDogQXJlYU9iaiA9IG51bGxcclxuXHJcbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xyXG4gICAgICAgIHJldHVybiBbXHJcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5SRUlOSVRfQVJFQV9VSV06IHRoaXMub25SZWluaXRBcmVhVUksIGVudGVyOiB0cnVlIH0sXHJcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQkFUVExFX0FSTVlfQ09VTlRfU0hPV106IHRoaXMub25VcGRhdGVCYXR0bGVBcm15Q291bnRTaG93LCBlbnRlcjogdHJ1ZSB9LFxyXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FSRUFfQkFUVExFX1RJTUVfVUldOiB0aGlzLm9uVXBkYXRlQXJlYUJhdHRsZVRpbWVVSSwgZW50ZXI6IHRydWUgfSxcclxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9BUkVBX1dBVENIX1BMQVlFUl06IHRoaXMub25VcGRhdGVBcmVhV2F0Y2hQbGF5ZXIsIGVudGVyOiB0cnVlIH0sXHJcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfRVhQX0JPT0tdOiB0aGlzLm9uVXBkYXRlV2F0Y2hOb2RlWSwgZW50ZXI6IHRydWUgfSxcclxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9JUk9OXTogdGhpcy5vblVwZGF0ZVdhdGNoTm9kZVksIGVudGVyOiB0cnVlIH0sXHJcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfVVBTQ1JPTExdOiB0aGlzLm9uVXBkYXRlV2F0Y2hOb2RlWSwgZW50ZXI6IHRydWUgfSxcclxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9GSVhBVE9SXTogdGhpcy5vblVwZGF0ZVdhdGNoTm9kZVksIGVudGVyOiB0cnVlIH0sXHJcbiAgICAgICAgXVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcclxuICAgICAgICB0aGlzLnNldFBhcmFtKHsgaXNDbGVhbjogZmFsc2UsIGlzQWN0OiBmYWxzZSwgaXNNYXNrOiBmYWxzZSB9KVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IEFyZWFPYmopIHtcclxuICAgICAgICB0aGlzLm1vZGVsID0gZGF0YVxyXG4gICAgICAgIHRoaXMudXBkYXRlQmF0dGxlVGltZSgpXHJcbiAgICAgICAgdGhpcy51cGRhdGVXYXRjaFBsYXllckNvdW50KClcclxuICAgICAgICB0aGlzLnVwZGF0ZVdhdGNoUGxheWVyTm9kZVkoKVxyXG4gICAgICAgIHRoaXMud2F0Y2hOb2RlXy5DaGlsZCgnY2hhdCcsIEFyZWFXYXRjaENoYXRDbXB0KS5pbml0KGRhdGEuaW5kZXgpXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xyXG4gICAgICAgIHRoaXMud2F0Y2hOb2RlXy5DaGlsZCgnY2hhdCcsIEFyZWFXYXRjaENoYXRDbXB0KS5jbGVhbigpXHJcbiAgICAgICAgdGhpcy51cGRhdGVTaG93R3VpZGVUYXNrVGlwKGZhbHNlKSAvL+i/mOWOn+S7u+WKoeaPkOekulxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xyXG4gICAgfVxyXG5cclxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcclxuXHJcbiAgICAvLyBwYXRoOi8vYmF0dGxlX2luZm9fbl9uYmVcclxuICAgIG9uQ2xpY2tCYXR0bGVJbmZvKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcclxuICAgICAgICBpZiAodGhpcy5tb2RlbCkge1xyXG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2FyZWEvQmF0dGxlSW5mbycsIHRoaXMubW9kZWwpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIHBhdGg6Ly90b3Avb3RoZXJfcmVzL3dhdGNoX24vcGVvcGxlX2JlXHJcbiAgICBvbkNsaWNrUGVvcGxlKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcclxuICAgICAgICBpZiAodGhpcy5tb2RlbCkge1xyXG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2FyZWEvQXJlYVdhdGNoTGlzdCcsIHRoaXMubW9kZWwuaW5kZXgpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLy9AZW5kXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxuICAgIC8vXHJcbiAgICBwcml2YXRlIG9uUmVpbml0QXJlYVVJKGRhdGE6IEFyZWFPYmopIHtcclxuICAgICAgICB0aGlzLm1vZGVsID0gZGF0YVxyXG4gICAgICAgIHRoaXMudXBkYXRlQmF0dGxlVGltZSgpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5Yi35paw5oiY5paX5Lit5Yab6Zif5pWw6YeP5pi+56S6XHJcbiAgICBwcml2YXRlIG9uVXBkYXRlQmF0dGxlQXJteUNvdW50U2hvdygpIHtcclxuICAgICAgICB0aGlzLnVwZGF0ZUJhdHRsZUFybXlDb3VudCgpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5Yi35paw5Yy65Z+f5oiY5paX5pe26Ze05pi+56S6XHJcbiAgICBwcml2YXRlIG9uVXBkYXRlQXJlYUJhdHRsZVRpbWVVSShpbmRleDogbnVtYmVyKSB7XHJcbiAgICAgICAgaWYgKHRoaXMubW9kZWw/LmluZGV4ID09PSBpbmRleCkge1xyXG4gICAgICAgICAgICB0aGlzLnVwZGF0ZUJhdHRsZVRpbWUoKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyDliLfmlrDljLrln5/op4LmiJjnjqnlrrbliJfooahcclxuICAgIHByaXZhdGUgb25VcGRhdGVBcmVhV2F0Y2hQbGF5ZXIoaW5kZXg6IG51bWJlcikge1xyXG4gICAgICAgIGlmICh0aGlzLm1vZGVsPy5pbmRleCA9PT0gaW5kZXgpIHtcclxuICAgICAgICAgICAgdGhpcy51cGRhdGVXYXRjaFBsYXllckNvdW50KClcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBvblVwZGF0ZVdhdGNoTm9kZVkoKSB7XHJcbiAgICAgICAgdGhpcy51cGRhdGVXYXRjaFBsYXllck5vZGVZKClcclxuICAgIH1cclxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG4gICAgLy8g5Yi35paw5oiY5paX5pe26Ze0XHJcbiAgICBwcml2YXRlIHVwZGF0ZUJhdHRsZVRpbWUoKSB7XHJcbiAgICAgICAgY29uc3QgaXNCYXR0bGVpbmcgPSB0aGlzLmJhdHRsZUluZm9Ob2RlXy5hY3RpdmUgPSAhIXRoaXMubW9kZWw/LmlzQmF0dGxlaW5nKClcclxuICAgICAgICBpZiAoaXNCYXR0bGVpbmcpIHtcclxuICAgICAgICAgICAgY29uc3Qgcm9vdCA9IHRoaXMuYmF0dGxlSW5mb05vZGVfLkNoaWxkKCdyb290JylcclxuICAgICAgICAgICAgcm9vdC5DaGlsZCgndGltZS92YWwnLCBjYy5MYWJlbFRpbWVyKS5zZXRFbmRUaW1lKEJBVFRMRV9NQVhfVElNRSkucnVuKHRoaXMubW9kZWwuZ2V0QmF0dGxlRWxhcHNlZFRpbWUoKSAqIDAuMDAxKVxyXG4gICAgICAgICAgICByb290LkNoaWxkKCdtdWwvdmFsJywgY2MuTGFiZWwpLnN0cmluZyA9IGAoJHt0aGlzLm1vZGVsLmdldEZzcE1vZGVsKCkuZ2V0TXVsKCl9eClgXHJcbiAgICAgICAgICAgIHRoaXMudXBkYXRlQmF0dGxlQXJteUNvdW50KClcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy51cGRhdGVTaG93R3VpZGVUYXNrVGlwKGlzQmF0dGxlaW5nKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOWIt+aWsOaYr+WQpuaYvuekuuS7u+WKoeaPkOekulxyXG4gICAgcHJpdmF0ZSB1cGRhdGVTaG93R3VpZGVUYXNrVGlwKGlzQmF0dGxlaW5nOiBib29sZWFuKSB7XHJcbiAgICAgICAgaWYgKHRoaXMuaXNIaWRlVGFza1RpcCAhPT0gaXNCYXR0bGVpbmcpIHtcclxuICAgICAgICAgICAgdGhpcy5pc0hpZGVUYXNrVGlwID0gaXNCYXR0bGVpbmdcclxuICAgICAgICAgICAgaWYgKGlzQmF0dGxlaW5nKSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLkNIQU5HRV9TSE9XX0dVSURFX1RBU0tfVElQLCBmYWxzZSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuQ0hBTkdFX1NIT1dfR1VJREVfVEFTS19USVAsIGdhbWVIcHIudGFzay5pc1Nob3dUYXNrVGlwKCkpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8g5Yi35paw5oiY5paX5LitIOWGm+mYn+aVsOmHj1xyXG4gICAgcHJpdmF0ZSB1cGRhdGVCYXR0bGVBcm15Q291bnQoKSB7XHJcbiAgICAgICAgY29uc3QgZnNwID0gdGhpcy5tb2RlbD8uZ2V0RnNwTW9kZWwoKVxyXG4gICAgICAgIGlmICghZnNwKSB7XHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCByb290ID0gdGhpcy5iYXR0bGVJbmZvTm9kZV8uQ2hpbGQoJ3Jvb3QnKSwgYWRkVGltZXNOb2RlID0gdGhpcy5iYXR0bGVJbmZvTm9kZV8uQ2hpbGQoJ2FkZF90aW1lcycpXHJcbiAgICAgICAgbGV0IG93bmVyID0gdGhpcy5tb2RlbC5vd25lciwgYXR0YWNrZXIgPSAwLCBkZWZlbmRlciA9IDBcclxuICAgICAgICAvLyDorqHnrpflkITkuKrlhpvpmJ/mlbDph49cclxuICAgICAgICB0aGlzLm1vZGVsLmFybXlzLmZvckVhY2gobSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChtLmdldFBhd25BY3RDb3VudCgpID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm5cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChnYW1lSHByLmNoZWNrSXNPbmVBbGxpYW5jZShtLm93bmVyLCBvd25lcikpIHtcclxuICAgICAgICAgICAgICAgIGRlZmVuZGVyICs9IDFcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGF0dGFja2VyICs9IDFcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgLy8g5LuO6L+Z5Liq5Yy65Z+f5byA5aeL6KGM5Yab55qE5Yab6Zif5pWw6YePXHJcbiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLm1vZGVsLmluZGV4XHJcbiAgICAgICAgZ2FtZUhwci53b3JsZC5nZXRNYXJjaHMoKS5maWx0ZXIobSA9PiBtLmFybXlJbmRleCA9PT0gaW5kZXgpLmZvckVhY2gobSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChtLmF1dG9SZXZva2UpIHtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChnYW1lSHByLmNoZWNrSXNPbmVBbGxpYW5jZShtLm93bmVyLCBvd25lcikpIHtcclxuICAgICAgICAgICAgICAgIGRlZmVuZGVyICs9IDFcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGF0dGFja2VyICs9IDFcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgcm9vdC5DaGlsZCgnYXR0YWNrZXIvdmFsJywgY2MuTGFiZWwpLnN0cmluZyA9ICcnICsgYXR0YWNrZXJcclxuICAgICAgICByb290LkNoaWxkKCdkZWZlbmRlci92YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0gJycgKyBkZWZlbmRlclxyXG4gICAgICAgIGFkZFRpbWVzTm9kZS5DaGlsZCgnYXR0YWNrZXIvdmFsJywgY2MuTGFiZWwpLnN0cmluZyA9IHV0LnBhZChmc3AuZ2V0QXR0YWNrZXJBcm15QWNjKCkpXHJcbiAgICAgICAgYWRkVGltZXNOb2RlLkNoaWxkKCdkZWZlbmRlci92YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0gdXQucGFkKGZzcC5nZXREZWZlbmRlckFybXlBY2MoKSlcclxuICAgIH1cclxuXHJcbiAgICAvLyDliLfmlrDop4LmiJjkurrmlbBcclxuICAgIHByaXZhdGUgdXBkYXRlV2F0Y2hQbGF5ZXJDb3VudCgpIHtcclxuICAgICAgICBjb25zdCBjb3VudCA9IGdhbWVIcHIuYXJlYUNlbnRlci5nZXRBcmVhV2F0Y2hQbGF5ZXJzQnlJbmRleCh0aGlzLm1vZGVsLmluZGV4KS5sZW5ndGhcclxuICAgICAgICBjb25zdCBub2RlID0gdGhpcy53YXRjaE5vZGVfLkNoaWxkKCdwZW9wbGVfYmUnKVxyXG4gICAgICAgIGlmIChub2RlLmFjdGl2ZSA9IGNvdW50ID49IDIpIHtcclxuICAgICAgICAgICAgbm9kZS5DaGlsZCgndmFsJywgY2MuTGFiZWwpLnN0cmluZyA9ICcnICsgTWF0aC5taW4oOTksIGNvdW50KVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIHVwZGF0ZVdhdGNoUGxheWVyTm9kZVkoKSB7XHJcbiAgICAgICAgbGV0IHBsYXllciA9IGdhbWVIcHIucGxheWVyLCBjb3VudCA9IDBcclxuICAgICAgICBpZiAocGxheWVyLmdldEV4cEJvb2soKSA+IDApIHtcclxuICAgICAgICAgICAgY291bnQgKz0gMVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAocGxheWVyLmdldElyb24oKSA+IDApIHtcclxuICAgICAgICAgICAgY291bnQgKz0gMVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAocGxheWVyLmdldFVwU2Nyb2xsKCkgPiAwKSB7XHJcbiAgICAgICAgICAgIGNvdW50ICs9IDFcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHBsYXllci5nZXRGaXhhdG9yKCkgPiAwKSB7XHJcbiAgICAgICAgICAgIGNvdW50ICs9IDFcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy53YXRjaE5vZGVfLnkgPSAtKChjb3VudCAqIDQ4IC0gOCkgKyAyMClcclxuICAgIH1cclxufVxyXG4iXX0=