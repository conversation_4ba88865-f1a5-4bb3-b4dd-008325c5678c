
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/MaintainTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4caaczybxZG2b+UD7E42BnL', 'MaintainTipPnlCtrl');
// app/script/view/login/MaintainTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ccclass = cc._decorator.ccclass;
var MaintainTipPnlCtrl = /** @class */ (function (_super) {
    __extends(MaintainTipPnlCtrl, _super);
    function MaintainTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.timeNode_ = null; // path://root/1/time/time_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.requesting = false;
        _this.elapsed = 0;
        return _this;
    }
    MaintainTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MaintainTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MaintainTipPnlCtrl.prototype.onEnter = function (time) {
        this.timeNode_.Component(cc.LabelTimer).run(time * 0.001, function () { return GameHelper_1.gameHpr.gameRestart(); });
        this.elapsed = 0;
        TaHelper_1.taHelper.track('ta_maintenanceUi');
    };
    MaintainTipPnlCtrl.prototype.onRemove = function () {
    };
    MaintainTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/join_discord_be
    MaintainTipPnlCtrl.prototype.onClickJoinDiscord = function (event, data) {
        GameHelper_1.gameHpr.openDiscord();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MaintainTipPnlCtrl.prototype.update = function (dt) {
        this.elapsed += dt;
        if (this.elapsed >= 5 && !this.requesting) {
            this.elapsed = 0;
            this.checkTime();
        }
    };
    MaintainTipPnlCtrl.prototype.checkTime = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res, status, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.requesting = true;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({ url: GameHelper_1.gameHpr.getConfigByArea().maintainTimeUrl, retryCount: 1 })];
                    case 1:
                        res = _a.sent();
                        this.requesting = false;
                        status = res === null || res === void 0 ? void 0 : res.status, data = res === null || res === void 0 ? void 0 : res.data;
                        if (status !== 1) {
                        }
                        else if ((data === null || data === void 0 ? void 0 : data.maintainTime) === 0) {
                            GameHelper_1.gameHpr.gameRestart();
                        }
                        else if (data === null || data === void 0 ? void 0 : data.maintainTime) {
                            this.timeNode_.Component(cc.LabelTimer).run(data.maintainTime * 0.001, function () { return GameHelper_1.gameHpr.gameRestart(); });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    MaintainTipPnlCtrl = __decorate([
        ccclass
    ], MaintainTipPnlCtrl);
    return MaintainTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MaintainTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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