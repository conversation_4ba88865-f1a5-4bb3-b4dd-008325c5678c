
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/SelectCellCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8114afg85ZGdaXNvMrG05BR', 'SelectCellCmpt');
// app/script/view/cmpt/SelectCellCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 地块选择
var SelectCellCmpt = /** @class */ (function (_super) {
    __extends(SelectCellCmpt, _super);
    function SelectCellCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.speed = 10;
        _this.items = [];
        _this.preSize = cc.v2();
        _this._temp_vec2 = cc.v2();
        return _this;
    }
    SelectCellCmpt.prototype.onLoad = function () {
        this.items = this.node.children.map(function (m) { return m.FindChild('val'); });
    };
    SelectCellCmpt.prototype.open = function (pos, size) {
        this.node.active = true;
        this.node.setPosition(pos);
        if (!this.preSize.equals(size)) {
            this.preSize.set(size);
            this._temp_vec2.set2(size.x * Constant_1.TILE_SIZE_HALF.x - 6, size.y * Constant_1.TILE_SIZE_HALF.y - 6);
            var _a = this.node.children, a = _a[0], b = _a[1], c = _a[2], d = _a[3];
            a.setPosition(-this._temp_vec2.x, this._temp_vec2.y); // 左上
            b.setPosition(this._temp_vec2.x, this._temp_vec2.y); // 右上
            c.setPosition(this._temp_vec2.x, -this._temp_vec2.y); // 右下
            d.setPosition(-this._temp_vec2.x, -this._temp_vec2.y); // 左下
        }
        this.items.forEach(function (m) { return m.setPosition(0, 0); });
        this.speed = 10;
    };
    SelectCellCmpt.prototype.close = function () {
        this.node.Data = null;
        this.node.active = false;
    };
    SelectCellCmpt.prototype.update = function (dt) {
        var _this = this;
        var sx = dt * this.speed;
        this.items.forEach(function (m, i) {
            m.x += sx;
            m.y -= sx;
            var ok = i === 0;
            if (m.x >= 4) {
                m.x = 4;
                m.y = -4;
            }
            else if (m.x < 0) {
                m.x = 0;
                m.y = 0;
            }
            else {
                ok = false;
            }
            if (ok) {
                _this.speed *= -1;
            }
        });
    };
    SelectCellCmpt = __decorate([
        ccclass
    ], SelectCellCmpt);
    return SelectCellCmpt;
}(cc.Component));
exports.default = SelectCellCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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