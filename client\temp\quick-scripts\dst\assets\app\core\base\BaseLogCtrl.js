
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseLogCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '39f13aUbllIUpENCaBeQvjU', 'BaseLogCtrl');
// app/core/base/BaseLogCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var CoreEventType_1 = require("../event/CoreEventType");
// 日志
var BaseLogCtrl = /** @class */ (function (_super) {
    __extends(BaseLogCtrl, _super);
    function BaseLogCtrl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    BaseLogCtrl.prototype.onLoad = function () {
        this.node.zIndex = 100;
        this.node.group = 'ui';
        eventCenter.on(CoreEventType_1.default.MVC_LOGGER_PRINT, this.onLoggerListener, this);
        this.onCreate();
    };
    BaseLogCtrl.prototype.onDestroy = function () {
        eventCenter.off(CoreEventType_1.default.MVC_LOGGER_PRINT, this.onLoggerListener, this);
        this.onClean();
    };
    BaseLogCtrl.prototype.close = function () {
        this.node.destroy();
    };
    BaseLogCtrl.prototype.onCreate = function () {
    };
    BaseLogCtrl.prototype.onClean = function () {
    };
    BaseLogCtrl.prototype.onLoggerListener = function (type, content) {
    };
    return BaseLogCtrl;
}(cc.Component));
exports.default = BaseLogCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxiYXNlXFxCYXNlTG9nQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx3REFBa0Q7QUFFbEQsS0FBSztBQUNMO0lBQXlDLCtCQUFZO0lBQXJEOztJQTRCQSxDQUFDO0lBMUJHLDRCQUFNLEdBQU47UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxHQUFHLENBQUE7UUFDdEIsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFBO1FBQ3RCLFdBQVcsQ0FBQyxFQUFFLENBQUMsdUJBQWEsQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDM0UsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFBO0lBQ25CLENBQUM7SUFFRCwrQkFBUyxHQUFUO1FBQ0ksV0FBVyxDQUFDLEdBQUcsQ0FBQyx1QkFBYSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUM1RSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7SUFDbEIsQ0FBQztJQUVNLDJCQUFLLEdBQVo7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO0lBQ3ZCLENBQUM7SUFFTSw4QkFBUSxHQUFmO0lBRUEsQ0FBQztJQUVNLDZCQUFPLEdBQWQ7SUFFQSxDQUFDO0lBRU0sc0NBQWdCLEdBQXZCLFVBQXdCLElBQVksRUFBRSxPQUFlO0lBQ3JELENBQUM7SUFDTCxrQkFBQztBQUFELENBNUJBLEFBNEJDLENBNUJ3QyxFQUFFLENBQUMsU0FBUyxHQTRCcEQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29yZUV2ZW50VHlwZSBmcm9tIFwiLi4vZXZlbnQvQ29yZUV2ZW50VHlwZVwiXHJcblxyXG4vLyDml6Xlv5dcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQmFzZUxvZ0N0cmwgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIG9uTG9hZCgpIHtcclxuICAgICAgICB0aGlzLm5vZGUuekluZGV4ID0gMTAwXHJcbiAgICAgICAgdGhpcy5ub2RlLmdyb3VwID0gJ3VpJ1xyXG4gICAgICAgIGV2ZW50Q2VudGVyLm9uKENvcmVFdmVudFR5cGUuTVZDX0xPR0dFUl9QUklOVCwgdGhpcy5vbkxvZ2dlckxpc3RlbmVyLCB0aGlzKVxyXG4gICAgICAgIHRoaXMub25DcmVhdGUoKVxyXG4gICAgfVxyXG5cclxuICAgIG9uRGVzdHJveSgpIHtcclxuICAgICAgICBldmVudENlbnRlci5vZmYoQ29yZUV2ZW50VHlwZS5NVkNfTE9HR0VSX1BSSU5ULCB0aGlzLm9uTG9nZ2VyTGlzdGVuZXIsIHRoaXMpXHJcbiAgICAgICAgdGhpcy5vbkNsZWFuKClcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgY2xvc2UoKSB7XHJcbiAgICAgICAgdGhpcy5ub2RlLmRlc3Ryb3koKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkNyZWF0ZSgpIHtcclxuXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XHJcblxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkxvZ2dlckxpc3RlbmVyKHR5cGU6IHN0cmluZywgY29udGVudDogc3RyaW5nKSB7XHJcbiAgICB9XHJcbn0iXX0=