
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CaptureTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ead3d2k+RZGBZc4s8SIESAr', 'CaptureTipPnlCtrl');
// app/script/view/main/CaptureTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CaptureTipPnlCtrl = /** @class */ (function (_super) {
    __extends(CaptureTipPnlCtrl, _super);
    function CaptureTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.infoNode_ = null; // path://root/info_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        return _this;
    }
    //@end
    CaptureTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CaptureTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CaptureTipPnlCtrl.prototype.onEnter = function () {
        var info = GameHelper_1.gameHpr.player.getCaptureInfo() || {};
        var condType = GameHelper_1.gameHpr.world.getWinCondType();
        this.infoNode_.Child('1').setLocaleKey('ui.capture_tip_1', ut.dateFormat('MM-dd hh:mm:ss', (info === null || info === void 0 ? void 0 : info.time) || Date.now()));
        this.infoNode_.Child('2').setLocaleKey('ui.capture_tip_2', "<fontFamily=Arial>" + ut.nameFormator(GameHelper_1.gameHpr.getPlayerName(info === null || info === void 0 ? void 0 : info.uid), 8) + "</>");
        this.infoNode_.Child('3').setLocaleKey('ui.capture_tip_3_' + condType);
        this.buttonsNode_.Swih(condType);
    };
    CaptureTipPnlCtrl.prototype.onRemove = function () {
        // 这里回到主场景
        if (mc.currWindName === 'area') {
            ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
        }
    };
    CaptureTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/2/cancel_be
    CaptureTipPnlCtrl.prototype.onClickCancel = function (event, data) {
        this.hide();
        TaHelper_1.taHelper.track('ta_CaptureOpt', { restart_way: 1 });
    };
    // path://root/buttons_n/2/restart_be
    CaptureTipPnlCtrl.prototype.onClickRestart = function (event, data) {
        TaHelper_1.taHelper.track('ta_CaptureOpt', { restart_way: 2 });
        this.restart();
    };
    // path://root/buttons_n/3/wait_settle_be
    CaptureTipPnlCtrl.prototype.onClickWaitSettle = function (event, data) {
        this.hide();
        GameHelper_1.gameHpr.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.WAIT_SETTLE, true);
        TaHelper_1.taHelper.track('ta_CaptureOpt', { restart_way: 1 });
    };
    // path://root/buttons_n/3/now_settle_be
    CaptureTipPnlCtrl.prototype.onClickNowSettle = function (event, data) {
        var _this = this;
        TaHelper_1.taHelper.track('ta_CaptureOpt', { restart_way: 1 });
        GameHelper_1.gameHpr.player.nowSettleGame().then(function (ok) { return (_this.isValid && ok) && _this.hide(); });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CaptureTipPnlCtrl.prototype.restart = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_ReCreateMainCity', { lang: mc.lang }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.NOT_CITY_INDEX) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_main_city_index')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.init(data === null || data === void 0 ? void 0 : data.playerInfo)];
                    case 2:
                        _b.sent();
                        eventCenter.emit(EventType_1.default.RECREATE_MAIN_CITY);
                        return [4 /*yield*/, GameHelper_1.gameHpr.gotoTargetPos(GameHelper_1.gameHpr.player.getMainCityIndex(), false)];
                    case 3:
                        _b.sent();
                        if (this.isValid) {
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    CaptureTipPnlCtrl = __decorate([
        ccclass
    ], CaptureTipPnlCtrl);
    return CaptureTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CaptureTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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