
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceRecordObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6eba7smMuhJsYswcDi3LHJ1', 'NoviceRecordObj');
// app/script/model/guide/NoviceRecordObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var BattleRecordData = /** @class */ (function () {
    function BattleRecordData() {
        this.hp = [];
        this.builds = [];
        this.buildInfo = [];
        this.armys = [];
        this.army = null;
        this.fighters = [];
        this.pawn = null;
        this.uid = '';
        this.owner = '';
        this.armyUid = '';
        this.randSeed = 0;
        this.type = 0;
        this.currentFrameIndex = 0;
        this.camp = 0;
        this.fps = 0;
        this.cityId = 0;
    }
    return BattleRecordData;
}());
// 战斗记录
var NoviceRecordObj = /** @class */ (function () {
    function NoviceRecordObj() {
        this.attacker = '';
        this.uid = '';
        this.isWin = false;
        this.beginTime = 0;
        this.endTime = 0;
        this.index = 0;
        this.isCanPlay = true;
        this.armyUidList = [];
        this.invalidInfo = {};
        this.validInfo = {};
        this.deadInfo = [];
        this.frames = [];
        this.treasures = [];
        this.armyFighterMap = {}; //战斗详情
        this.tempArmyUidMap = {};
        this.pawnTotalCount = 0;
        this.enemyTotalCount = 0;
    }
    NoviceRecordObj.prototype.init = function (area, data) {
        var _a;
        this.attacker = data.attacker;
        this.uid = ut.UID();
        this.beginTime = Date.now();
        this.index = area.index;
        var recordData = new BattleRecordData();
        recordData.cityId = area.cityId;
        recordData.owner = area.getOwner();
        recordData.fps = data.fps;
        recordData.armys = area.armys.map(function (m) { return m.strip(); });
        recordData.builds = area.builds.map(function (m) { return m.strip(); });
        var buildId = (_a = assetsMgr.getJsonData('buildBase', area.cityId)) === null || _a === void 0 ? void 0 : _a.pawn_id;
        if (buildId) {
            var cityLv = 1;
            for (var i = 0; i < recordData.builds.length; i++) {
                if (area.cityId === recordData.builds[i].id) {
                    cityLv = recordData.builds[i].lv;
                    break;
                }
            }
            recordData.buildInfo = [buildId, cityLv];
        }
        else {
            recordData.buildInfo = null;
        }
        recordData.hp = [area.curHp, area.maxHp];
        recordData.fighters = data.fighters;
        recordData.randSeed = area.proxyAO.getFspModel().getBattleController().getRandom().seed;
        recordData.camp = GameHelper_1.gameHpr.noviceServer.getCamp(area.owner);
        this.frames.push(recordData);
        this.setArmyUidList(area);
    };
    NoviceRecordObj.prototype.setArmyUidList = function (area) {
        for (var i = 0; i < area.armys.length; i++) {
            var army = area.armys[i];
            if (!this.tempArmyUidMap[army.uid]) {
                this.tempArmyUidMap[army.uid] = true;
                if (army.owner === this.attacker) {
                    this.pawnTotalCount += army.pawns.length;
                    this.armyUidList.push(army.uid);
                    this.getFighterData(army);
                }
                else {
                    this.enemyTotalCount += army.pawns.length;
                }
            }
        }
    };
    NoviceRecordObj.prototype.addFrame = function (area, data) {
        if (this.index === area.index) {
            // 隔一帧行动
            if (0 === data.currentFrameIndex) {
                data.currentFrameIndex = 1;
            }
            this.frames.push(data);
            this.setArmyUidList(area);
        }
    };
    NoviceRecordObj.prototype.battleEnd = function (area, data) {
        if (area.index === this.index) {
            this.endTime = this.beginTime + data.battleEndInfo.battleTime;
            this.isWin = area.owner === GameHelper_1.gameHpr.getUid();
            var deadCount = this.pawnTotalCount;
            if (this.isWin) {
                var pawnCount = 0;
                for (var i = 0; i < area.armys.length; i++) {
                    var army = area.armys[i];
                    pawnCount += army.pawns.length;
                }
                deadCount = this.pawnTotalCount - pawnCount;
                this.invalidInfo[1] = this.enemyTotalCount;
                this.treasures = data.recordTreasures;
            }
            for (var i = 0; i < deadCount; i++) {
                this.deadInfo.push(0);
            }
            this.resetPawnHP(area);
        }
    };
    // 重置战斗详情士兵血量
    NoviceRecordObj.prototype.resetPawnHP = function (area) {
        var _a, _b;
        var hpMap = {};
        for (var i = 0; i < area.armys.length; i++) {
            var army = area.armys[i];
            for (var j = 0; j < army.pawns.length; j++) {
                hpMap[army.pawns[j].uid] = army.pawns[j].curHp;
            }
        }
        for (var i = 0; i < this.frames.length; i++) {
            var frame = this.frames[i];
            if (frame.armys) {
                for (var j = 0; j < frame.armys.length; j++) {
                    var pawns = ((_a = this.armyFighterMap[frame.armys[j].uid]) === null || _a === void 0 ? void 0 : _a.battle.pawns) || [];
                    for (var k = 0; k < pawns.length; k++) {
                        var pawn = pawns[k];
                        if (hpMap[pawn.uid]) {
                            pawn.hp[0] = hpMap[pawn.uid];
                        }
                    }
                }
            }
            if (frame.army) {
                var pawns = ((_b = this.armyFighterMap[frame.army.uid]) === null || _b === void 0 ? void 0 : _b.battle.pawns) || [];
                for (var k = 0; k < pawns.length; k++) {
                    var pawn = pawns[k];
                    if (hpMap[pawn.uid]) {
                        pawn.hp[0] = hpMap[pawn.uid];
                    }
                }
            }
        }
    };
    NoviceRecordObj.prototype.getSelfArmyWithPawnUID = function (uid) {
        var tempArmys = [];
        for (var i = 0; i < this.frames.length; i++) {
            var frame = this.frames[i];
            if (frame.armys) {
                tempArmys = tempArmys.concat(frame.armys);
            }
            if (frame.army) {
                tempArmys.push(frame.army);
            }
        }
        for (var j = 0; j < tempArmys.length; j++) {
            var army = tempArmys[j];
            if (army.owner === this.attacker) {
                for (var k = 0; k < army.pawns.length; k++) {
                    if (army.pawns[k].uid === uid) {
                        return army;
                    }
                }
            }
        }
        return null;
    };
    // 获取战斗详情
    NoviceRecordObj.prototype.getFighterData = function (army) {
        var data = this.armyFighterMap[army.uid];
        if (!data) {
            data = { armyName: army.name, time: Date.now(), battle: { battleInfo: {}, pawns: [] } };
            data.battle.battleInfo[Enums_1.BattleStatistics.SUM_DAMAGE] = 0; //伤害
            data.battle.battleInfo[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] = 0; //承伤
            data.battle.battleInfo[Enums_1.BattleStatistics.HEAL_HP] = 0; //治疗
            data.battle.battleInfo[Enums_1.BattleStatistics.SUM_KILL] = 0; //击杀
            data.battle.battleInfo[Enums_1.BattleStatistics.PAWN_DEAD] = 0; //阵亡
            for (var i = 0; i < army.pawns.length; i++) {
                var pawn = army.pawns[i];
                pawn.hp = [pawn.curHp, pawn.maxHp];
                data.battle.pawns.push(pawn);
            }
            this.armyFighterMap[army.uid] = data;
        }
        return data;
    };
    // 设置战斗详情
    NoviceRecordObj.prototype.setFighterData = function (fighterData) {
        var index = fighterData.index, heal = fighterData.heal, attackerHeal = fighterData.attackerHeal;
        if (index === this.index) {
            var attacker = fighterData.attacker;
            var defender = fighterData.defender;
            var _a = fighterData.data, sumDamage = _a.sumDamage, actDamage = _a.actDamage, trueDamage = _a.trueDamage;
            // 计算伤害
            var army = this.getSelfArmyWithPawnUID(attacker.getUid());
            if (army) {
                var fingerData = this.getFighterData(army);
                fingerData.battle.battleInfo[Enums_1.BattleStatistics.SUM_DAMAGE] += actDamage;
                fingerData.battle.battleInfo[Enums_1.BattleStatistics.HEAL_HP] += heal;
                if (defender && defender.isDie() && !defender.isBuild()) {
                    fingerData.battle.battleInfo[Enums_1.BattleStatistics.SUM_KILL]++;
                }
            }
            else if (defender) {
                // 承受伤害
                army = this.getSelfArmyWithPawnUID(defender.getUid());
                var fingerData = this.getFighterData(army);
                fingerData.battle.battleInfo[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] += actDamage;
                fingerData.battle.battleInfo[Enums_1.BattleStatistics.HEAL_HP] += attackerHeal;
                if (defender.isDie()) {
                    fingerData.battle.battleInfo[Enums_1.BattleStatistics.PAWN_DEAD]++;
                    var pawns = fingerData.battle.pawns;
                    for (var i = 0; i < pawns.length; i++) {
                        if (defender.getUid() === pawns[i].uid) {
                            pawns[i].curHp = 0;
                            pawns[i].hp[0] = 0;
                            break;
                        }
                    }
                    if (defender.getOwner() === GameHelper_1.gameHpr.getUid()) {
                        GameHelper_1.gameHpr.noviceServer.addPawnToHealing(defender);
                    }
                }
            }
        }
    };
    // 获取战斗统计
    NoviceRecordObj.prototype.getBattleStatistics = function () {
        var list = [];
        for (var key in this.armyFighterMap) {
            list.push(this.armyFighterMap[key]);
        }
        return list;
    };
    NoviceRecordObj.prototype.strip = function () {
        return {
            attacker: this.attacker,
            uid: this.uid,
            isWin: this.isWin,
            beginTime: this.beginTime,
            endTime: this.endTime,
            index: this.index,
            isCanPlay: this.isCanPlay,
            armyUidList: this.armyUidList,
            invalidInfo: this.invalidInfo,
            validInfo: this.validInfo,
            deadInfo: this.deadInfo,
            frames: this.frames,
            treasures: this.treasures,
            armyFighterMap: this.armyFighterMap,
        };
    };
    NoviceRecordObj.prototype.fromDB = function (data) {
        for (var key in data) {
            this[key] = data[key];
        }
        return this;
    };
    return NoviceRecordObj;
}());
exports.default = NoviceRecordObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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