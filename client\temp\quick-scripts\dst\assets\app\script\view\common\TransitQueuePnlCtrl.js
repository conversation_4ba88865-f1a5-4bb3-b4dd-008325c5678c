
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/TransitQueuePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c9c32PNaJZFEJ23I7Ds/PWn', 'TransitQueuePnlCtrl');
// app/script/view/common/TransitQueuePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TransitQueuePnlCtrl = /** @class */ (function (_super) {
    __extends(TransitQueuePnlCtrl, _super);
    function TransitQueuePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.listSv_ = null; // path://root_n/list_sv
        return _this;
    }
    //@end
    TransitQueuePnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.ADD_MARCH] = this.onAddMarch, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _b.enter = true, _b),
        ];
    };
    TransitQueuePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    TransitQueuePnlCtrl.prototype.onEnter = function (data) {
        this.emit(EventType_1.default.CLOSE_SELECT_CELL); //关闭地块信息面板
        this.updateList();
    };
    TransitQueuePnlCtrl.prototype.onRemove = function () {
    };
    TransitQueuePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    TransitQueuePnlCtrl.prototype.onAddMarch = function (data) {
        if (data.getMarchLineType() === Enums_1.MarchLineType.MERCHANT) {
            this.updateList();
        }
    };
    TransitQueuePnlCtrl.prototype.onRemoveMarch = function (data) {
        this.onAddMarch(data);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    TransitQueuePnlCtrl.prototype.updateList = function () {
        var list = GameHelper_1.gameHpr.world.getTransits().filter(function (m) { return m.isCanShowUI(); }).sort(function (a, b) { return a.getSurplusTime() - b.getSurplusTime(); });
        if (list.length === 0) {
            return this.hide();
        }
        this.listSv_.stopAutoScroll();
        this.listSv_.node.height = Math.min(list.length * 50 + 2, 430);
        this.listSv_.Items(list, function (it, data) {
            it.Data = data;
            it.Child('name', cc.Label).string = ut.nameFormator(GameHelper_1.gameHpr.getPlayerName(data.owner), 6);
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
            it.Child('target/name').setLocaleKey('ui.transit_target_' + data.targetType);
            var item = it.Child('target/item');
            if (item.active = data.goods.count > 0) {
                ViewHelper_1.viewHelper.updateCostViewOne(item, data.goods);
            }
        });
        this.rootNode_.Component(cc.Layout).updateLayout();
    };
    TransitQueuePnlCtrl = __decorate([
        ccclass
    ], TransitQueuePnlCtrl);
    return TransitQueuePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TransitQueuePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcVHJhbnNpdFF1ZXVlUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxREFBNEQ7QUFDNUQsMERBQXFEO0FBQ3JELDZEQUF5RDtBQUN6RCw2REFBNEQ7QUFHcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBaUQsdUNBQWM7SUFBL0Q7UUFBQSxxRUErREM7UUE3REcsMEJBQTBCO1FBQ2xCLGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQyxnQkFBZ0I7UUFDMUMsYUFBTyxHQUFrQixJQUFJLENBQUEsQ0FBQyx3QkFBd0I7O0lBMkRsRSxDQUFDO0lBMURHLE1BQU07SUFFQyw2Q0FBZSxHQUF0Qjs7UUFDSSxPQUFPO3NCQUNELEdBQUMsbUJBQVMsQ0FBQyxTQUFTLElBQUcsSUFBSSxDQUFDLFVBQVUsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDbkQsR0FBQyxtQkFBUyxDQUFDLFlBQVksSUFBRyxJQUFJLENBQUMsYUFBYSxFQUFFLFFBQUssR0FBRSxJQUFJO1NBQzlELENBQUE7SUFDTCxDQUFDO0lBRVksc0NBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0scUNBQU8sR0FBZCxVQUFlLElBQVM7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGlCQUFpQixDQUFDLENBQUEsQ0FBQyxVQUFVO1FBQ2pELElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTtJQUNyQixDQUFDO0lBRU0sc0NBQVEsR0FBZjtJQUNBLENBQUM7SUFFTSxxQ0FBTyxHQUFkO0lBQ0EsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFDM0IsTUFBTTtJQUNOLGlIQUFpSDtJQUV6Ryx3Q0FBVSxHQUFsQixVQUFtQixJQUFrQjtRQUNqQyxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxLQUFLLHFCQUFhLENBQUMsUUFBUSxFQUFFO1lBQ3BELElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTtTQUNwQjtJQUNMLENBQUM7SUFFTywyQ0FBYSxHQUFyQixVQUFzQixJQUFrQjtRQUNwQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQ3pCLENBQUM7SUFDRCxpSEFBaUg7SUFFekcsd0NBQVUsR0FBbEI7UUFDSSxJQUFNLElBQUksR0FBRyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsV0FBVyxFQUFFLEVBQWYsQ0FBZSxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSyxPQUFBLENBQUMsQ0FBQyxjQUFjLEVBQUUsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFLEVBQXZDLENBQXVDLENBQUMsQ0FBQTtRQUM3SCxJQUFJLElBQUksQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ25CLE9BQU8sSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1NBQ3JCO1FBQ0QsSUFBSSxDQUFDLE9BQU8sQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUM3QixJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEVBQUUsR0FBRyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUE7UUFDOUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFFLFVBQUMsRUFBRSxFQUFFLElBQUk7WUFDOUIsRUFBRSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7WUFDZCxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsb0JBQU8sQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ3pGLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLGNBQWMsRUFBRSxHQUFHLEtBQUssQ0FBQyxDQUFBO1lBQ2xFLEVBQUUsQ0FBQyxLQUFLLENBQUMsYUFBYSxDQUFDLENBQUMsWUFBWSxDQUFDLG9CQUFvQixHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtZQUM1RSxJQUFNLElBQUksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxDQUFBO1lBQ3BDLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssR0FBRyxDQUFDLEVBQUU7Z0JBQ3BDLHVCQUFVLENBQUMsaUJBQWlCLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTthQUNqRDtRQUNMLENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksRUFBRSxDQUFBO0lBQ3RELENBQUM7SUE5RGdCLG1CQUFtQjtRQUR2QyxPQUFPO09BQ2EsbUJBQW1CLENBK0R2QztJQUFELDBCQUFDO0NBL0RELEFBK0RDLENBL0RnRCxFQUFFLENBQUMsV0FBVyxHQStEOUQ7a0JBL0RvQixtQkFBbUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNYXJjaExpbmVUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xyXG5pbXBvcnQgRXZlbnRUeXBlIGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvRXZlbnRUeXBlXCI7XHJcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XHJcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XHJcbmltcG9ydCBCYXNlTWFyY2hPYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vQmFzZU1hcmNoT2JqXCI7XHJcblxyXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG5AY2NjbGFzc1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBUcmFuc2l0UXVldWVQbmxDdHJsIGV4dGVuZHMgbWMuQmFzZVBubEN0cmwge1xyXG5cclxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXHJcbiAgICBwcml2YXRlIHJvb3ROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3RfblxyXG4gICAgcHJpdmF0ZSBsaXN0U3ZfOiBjYy5TY3JvbGxWaWV3ID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL2xpc3Rfc3ZcclxuICAgIC8vQGVuZFxyXG5cclxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XHJcbiAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkFERF9NQVJDSF06IHRoaXMub25BZGRNYXJjaCwgZW50ZXI6IHRydWUgfSxcclxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlJFTU9WRV9NQVJDSF06IHRoaXMub25SZW1vdmVNYXJjaCwgZW50ZXI6IHRydWUgfSxcclxuICAgICAgICBdXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IGFueSkge1xyXG4gICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuQ0xPU0VfU0VMRUNUX0NFTEwpIC8v5YWz6Zet5Zyw5Z2X5L+h5oGv6Z2i5p2/XHJcbiAgICAgICAgdGhpcy51cGRhdGVMaXN0KClcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxyXG4gICAgLy9AZW5kXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxuICAgIHByaXZhdGUgb25BZGRNYXJjaChkYXRhOiBCYXNlTWFyY2hPYmopIHtcclxuICAgICAgICBpZiAoZGF0YS5nZXRNYXJjaExpbmVUeXBlKCkgPT09IE1hcmNoTGluZVR5cGUuTUVSQ0hBTlQpIHtcclxuICAgICAgICAgICAgdGhpcy51cGRhdGVMaXN0KClcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBvblJlbW92ZU1hcmNoKGRhdGE6IEJhc2VNYXJjaE9iaikge1xyXG4gICAgICAgIHRoaXMub25BZGRNYXJjaChkYXRhKVxyXG4gICAgfVxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbiAgICBwcml2YXRlIHVwZGF0ZUxpc3QoKSB7XHJcbiAgICAgICAgY29uc3QgbGlzdCA9IGdhbWVIcHIud29ybGQuZ2V0VHJhbnNpdHMoKS5maWx0ZXIobSA9PiBtLmlzQ2FuU2hvd1VJKCkpLnNvcnQoKGEsIGIpID0+IGEuZ2V0U3VycGx1c1RpbWUoKSAtIGIuZ2V0U3VycGx1c1RpbWUoKSlcclxuICAgICAgICBpZiAobGlzdC5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuaGlkZSgpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMubGlzdFN2Xy5zdG9wQXV0b1Njcm9sbCgpXHJcbiAgICAgICAgdGhpcy5saXN0U3ZfLm5vZGUuaGVpZ2h0ID0gTWF0aC5taW4obGlzdC5sZW5ndGggKiA1MCArIDIsIDQzMClcclxuICAgICAgICB0aGlzLmxpc3RTdl8uSXRlbXMobGlzdCwgKGl0LCBkYXRhKSA9PiB7XHJcbiAgICAgICAgICAgIGl0LkRhdGEgPSBkYXRhXHJcbiAgICAgICAgICAgIGl0LkNoaWxkKCduYW1lJywgY2MuTGFiZWwpLnN0cmluZyA9IHV0Lm5hbWVGb3JtYXRvcihnYW1lSHByLmdldFBsYXllck5hbWUoZGF0YS5vd25lciksIDYpXHJcbiAgICAgICAgICAgIGl0LkNoaWxkKCd0aW1lJywgY2MuTGFiZWxUaW1lcikucnVuKGRhdGEuZ2V0U3VycGx1c1RpbWUoKSAqIDAuMDAxKVxyXG4gICAgICAgICAgICBpdC5DaGlsZCgndGFyZ2V0L25hbWUnKS5zZXRMb2NhbGVLZXkoJ3VpLnRyYW5zaXRfdGFyZ2V0XycgKyBkYXRhLnRhcmdldFR5cGUpXHJcbiAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSBpdC5DaGlsZCgndGFyZ2V0L2l0ZW0nKVxyXG4gICAgICAgICAgICBpZiAoaXRlbS5hY3RpdmUgPSBkYXRhLmdvb2RzLmNvdW50ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci51cGRhdGVDb3N0Vmlld09uZShpdGVtLCBkYXRhLmdvb2RzKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5Db21wb25lbnQoY2MuTGF5b3V0KS51cGRhdGVMYXlvdXQoKVxyXG4gICAgfVxyXG59XHJcbiJdfQ==