
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ClickTouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bb8a5YifaZAObnek8vBmhuQ', 'ClickTouchCmpt');
// app/script/view/cmpt/ClickTouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ITouchCmpt_1 = require("./ITouchCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于点击地图上面的
 */
var ClickTouchCmpt = /** @class */ (function (_super) {
    __extends(ClickTouchCmpt, _super);
    function ClickTouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isPlayAction = false; //是否播放动画
        _this.isDown = false; // 是否按下
        _this._target = null;
        _this._node = null;
        _this._originalScale = undefined;
        _this.downCallback = null;
        _this.clickCallback = null;
        _this.clickTarget = null;
        _this.data = null;
        _this.tempVec = cc.v2();
        return _this;
    }
    ClickTouchCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    ClickTouchCmpt.prototype.on = function (callback, target) {
        this.clean();
        this.clickCallback = callback;
        this.clickTarget = target;
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.SetSwallowTouches(false);
        return this;
    };
    ClickTouchCmpt.prototype.off = function () {
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    };
    ClickTouchCmpt.prototype.clean = function () {
        this.off();
        this.downCallback = null;
        this.clickCallback = null;
        this.clickTarget = null;
        this._node = null;
        this._target = null;
        this.data = null;
        if (this.isDown) {
            this.isDown = false;
            GameHelper_1.gameHpr.clickTouchId = -1;
        }
    };
    ClickTouchCmpt.prototype.setPlayAction = function (val) {
        this.isPlayAction = val;
        return this;
    };
    ClickTouchCmpt.prototype.setTarget = function (target) {
        this._target = target;
        return this;
    };
    ClickTouchCmpt.prototype.setNode = function (node) {
        this._node = node;
        return this;
    };
    ClickTouchCmpt.prototype.setData = function (data) {
        this.data = data;
        return this;
    };
    ClickTouchCmpt.prototype.setDownCallback = function (val) {
        this.downCallback = val;
        return this;
    };
    Object.defineProperty(ClickTouchCmpt.prototype, "target", {
        get: function () {
            if (!this._target) {
                this._target = this.node;
            }
            return this._target;
        },
        enumerable: false,
        configurable: true
    });
    ClickTouchCmpt.prototype.getNode = function () {
        if (!this._node) {
            this._node = this.node;
        }
        return this._node;
    };
    Object.defineProperty(ClickTouchCmpt.prototype, "originalScale", {
        get: function () {
            if (this._originalScale === undefined) {
                this._originalScale = this.node.scale;
            }
            return this._originalScale;
        },
        enumerable: false,
        configurable: true
    });
    // 触摸开始
    ClickTouchCmpt.prototype.onTouchStart = function (event) {
        if (!this.interactable || GameHelper_1.gameHpr.clickTouchId !== -1) {
            return;
        }
        GameHelper_1.gameHpr.clickTouchId = event.getID();
        this.down();
    };
    // 触摸移动
    ClickTouchCmpt.prototype.onTouchMove = function (event) {
        if (!this.interactable || GameHelper_1.gameHpr.clickTouchId !== event.getID()) {
            return;
        }
        var hit = this.node._hitTest(event.getLocation());
        if (hit) {
            if (!this.isDown) {
                this.down();
            }
        }
        else if (this.isDown) {
            this.restore();
        }
    };
    // 触摸结束
    ClickTouchCmpt.prototype.onTouchEnd = function (event) {
        if (GameHelper_1.gameHpr.clickTouchId !== event.getID()) {
            return;
        }
        GameHelper_1.gameHpr.clickTouchId = -1;
        this.restore();
        if (this.interactable) {
            var location = event.getLocation();
            if (CameraCtrl_1.cameraCtrl.convertWorldSub(event.getStartLocation(), location).mag() <= Constant_1.CLICK_SPACE) {
                this.emit(location);
            }
        }
    };
    // 触摸取消
    ClickTouchCmpt.prototype.onTouchCancel = function (event) {
        if (GameHelper_1.gameHpr.clickTouchId !== event.getID()) {
            return;
        }
        GameHelper_1.gameHpr.clickTouchId = -1;
        this.restore();
    };
    // 按下
    ClickTouchCmpt.prototype.down = function () {
        this.isDown = true;
        if (this.downCallback) {
            this.downCallback(true);
        }
        else if (this.isPlayAction) {
            cc.tween(this.target).to(0.1, { scale: this.originalScale * 1.05 }).start();
        }
    };
    // 还原
    ClickTouchCmpt.prototype.restore = function () {
        this.isDown = false;
        if (this.downCallback) {
            this.downCallback(false);
        }
        else if (this.isPlayAction) {
            cc.tween(this.target).to(0.1, { scale: this.originalScale }).start();
        }
    };
    ClickTouchCmpt.prototype.emit = function (location) {
        var pos = CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(location, this.tempVec);
        if (this.clickTarget) {
            this.clickCallback.call(this.clickTarget, this.getNode(), this.data, pos);
        }
        else {
            this.clickCallback(this.getNode(), this.data, pos);
        }
    };
    ClickTouchCmpt = __decorate([
        ccclass
    ], ClickTouchCmpt);
    return ClickTouchCmpt;
}(ITouchCmpt_1.default));
exports.default = ClickTouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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