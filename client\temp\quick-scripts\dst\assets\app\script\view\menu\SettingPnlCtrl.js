
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/SettingPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e8320VxLjBOlpHasWrukaTh', 'SettingPnlCtrl');
// app/script/view/menu/SettingPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var version_1 = require("../../../../scene/version");
var LocalConfig_1 = require("../../common/LocalConfig");
var CommunityConfig_1 = require("../../common/constant/CommunityConfig");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var SettingPnlCtrl = /** @class */ (function (_super) {
    __extends(SettingPnlCtrl, _super);
    function SettingPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.bgmNode_ = null; // path://root/sound/bgm/bgm_be_n
        _this.bgmSliderNode_ = null; // path://root/sound/bgm/bgm_slider_se_n
        _this.sfxNode_ = null; // path://root/sound/sfx/sfx_be_n
        _this.sfxSliderNode_ = null; // path://root/sound/sfx/sfx_slider_se_n
        _this.languageNode_ = null; // path://root/language_n_be
        _this.exchangeNode_ = null; // path://root/exchange_n_be
        _this.feedbackNode_ = null; // path://root/buttons/feedback_n_be
        _this.fcmSetNode_ = null; // path://root/buttons/fcm_set_n_be
        _this.logoutNode_ = null; // path://root/buttons/logout_n_be
        _this.changeAccountNode_ = null; // path://root/buttons/change_account_n_be
        _this.communityNode_ = null; // path://root/community/community_n_nbe
        _this.versionNode_ = null; // path://root/version_be_n
        _this.languageBoxNode_ = null; // path://language_box_be_n
        //@end
        _this.root = null;
        _this.gameHubNode = null;
        _this.preRootHeight = -1;
        _this.preRootScale = 0;
        _this.wxFeedBackNode = null;
        _this.wxGameHubNode = null;
        _this.bgmSlider = null;
        _this.sfxSlider = null;
        return _this;
    }
    SettingPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[mc.Event.PNL_ENTER] = this.onPnlEnter, _a.enter = true, _a),
            (_b = {}, _b[mc.Event.PNL_LEAVE] = this.onPnlLeave, _b.enter = true, _b),
            (_c = {}, _c[mc.Event.LANGUAGE_CHANGED] = this.onLanguageChanged, _c.enter = true, _c),
        ];
    };
    SettingPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.FindChild('root');
                this.gameHubNode = this.communityNode_.FindChild('gamehub');
                this.versionNode_.Child('root', TextButtonCmpt_1.default).setKey('ui.version_desc', version_1.default.VERSION);
                return [2 /*return*/];
            });
        });
    };
    SettingPnlCtrl.prototype.onEnter = function () {
        var /* isWechatGame = ut.isWechatGame(), */ isGLobal = GameHelper_1.gameHpr.isGLobal(), isSpectate = GameHelper_1.gameHpr.isSpectate();
        var isMobile = ut.isMobile() || cc.sys.isBrowser;
        this.initSoundVolume();
        this.feedbackNode_.Component(cc.Button).interactable = false;
        // this.feedbackNode_.active = isWechatGame || !gameHpr.isNoviceMode
        this.gameHubNode.Component(cc.Button).interactable = false;
        this.exchangeNode_.active = !GameHelper_1.gameHpr.isNoviceMode && GameHelper_1.gameHpr.openCdk && !isSpectate && (cc.sys.isBrowser || !LocalConfig_1.localConfig.RELEASE || GameHelper_1.gameHpr.isRelease);
        this.languageNode_.active = isGLobal; //只有海外才有多语言
        this.languageBoxNode_.active = false;
        this.fcmSetNode_.active = isMobile && !isSpectate; //只有原生才有推送
        this.changeAccountNode_.active = isMobile && !isSpectate;
        this.logoutNode_.active = !isSpectate; //注销账号
        this.updateCommunityButtons();
        this.updateShowLanguage();
    };
    SettingPnlCtrl.prototype.onPlayActionComplete = function () {
        if (ut.isWechatGame()) {
            this.initFeedBackNode();
            this.initGameClubNode();
        }
        else {
            this.feedbackNode_.Component(cc.Button).interactable = true;
        }
    };
    SettingPnlCtrl.prototype.onRemove = function () {
        var _a, _b;
        (_a = this.wxFeedBackNode) === null || _a === void 0 ? void 0 : _a.hide();
        (_b = this.wxGameHubNode) === null || _b === void 0 ? void 0 : _b.hide();
    };
    SettingPnlCtrl.prototype.onClean = function () {
        WxHelper_1.wxHelper.destroyButton(this.feedbackNode_);
        WxHelper_1.wxHelper.destroyButton(this.gameHubNode);
        this.wxFeedBackNode = null;
        this.wxGameHubNode = null;
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/sound/bgm/bgm_be_n
    SettingPnlCtrl.prototype.onClickBgm = function (event, data) {
        audioMgr.playSFX('click');
        audioMgr.bgmVolume = audioMgr.bgmVolume ? 0 : 1;
        this.changeSoundNode(event.target, this.bgmSlider, audioMgr.bgmVolume);
    };
    // path://root/sound/sfx/sfx_be_n
    SettingPnlCtrl.prototype.onClickSfx = function (event, data) {
        audioMgr.playSFX('click');
        audioMgr.sfxVolume = audioMgr.sfxVolume ? 0 : 1;
        this.changeSoundNode(event.target, this.sfxSlider, audioMgr.sfxVolume);
    };
    // path://root/buttons/feedback_n_be
    SettingPnlCtrl.prototype.onClickFeedback = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showFeedback().then(function () { return _this.isValid && _this.hide(); });
    };
    // path://root/exchange_n_be
    SettingPnlCtrl.prototype.onClickExchange = function (event, data) {
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return ViewHelper_1.viewHelper.showAlert('toast.novice_not_opt_tip');
        }
        ViewHelper_1.viewHelper.showPnl('menu/Exchange');
    };
    // path://root/buttons/logout_n_be
    SettingPnlCtrl.prototype.onClickLogout = function (event, data) {
        if (GameHelper_1.gameHpr.isGuest()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.GUEST_NOT_LOGOUT);
        }
        else if (GameHelper_1.gameHpr.isRelease) {
            var time = GameHelper_1.gameHpr.getServerNowTime() - GameHelper_1.gameHpr.user.getCreateTime();
            if (time < Constant_1.LOGOUT_MAX_DAY) {
                return ViewHelper_1.viewHelper.showAlert('toast.surplus_logout_time', { params: [GameHelper_1.gameHpr.millisecondToStringForDay(Constant_1.LOGOUT_MAX_DAY - time)] });
            }
        }
        ViewHelper_1.viewHelper.showPnl('menu/LogoutTip');
        this.emit(mc.Event.HIDE_PNL, 'common/UIMenuChild');
    };
    // path://root/language_n_be
    SettingPnlCtrl.prototype.onClickLanguage = function (event, data) {
        this.showLanguageList();
    };
    // path://language_box_be_n
    SettingPnlCtrl.prototype.onClickLanguageBox = function (event, data) {
        this.languageBoxNode_.active = false;
    };
    // path://language_box_be_n/root/language_item_be
    SettingPnlCtrl.prototype.onClickLanguageItem = function (event, data) {
        var _a;
        var lang = (_a = event.target.Data) === null || _a === void 0 ? void 0 : _a.lang;
        if (lang) {
            this.languageBoxNode_.active = false;
            if (mc.lang !== lang) {
                mc.lang = lang;
                this.updateShowLanguage();
                // 切换当前语言后，世界频道那里也要同步切换
                GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, ['cn', 'tw', 'hk'].has(lang) ? 'zh' : lang);
            }
        }
    };
    // path://root/buttons/change_account_n_be
    SettingPnlCtrl.prototype.onClickChangeAccount = function (event, data) {
        GameHelper_1.gameHpr.changeAccount();
        this.emit(mc.Event.HIDE_PNL, 'common/UIMenuChild');
    };
    // path://root/buttons/fcm_set_n_be
    SettingPnlCtrl.prototype.onClickFcmSet = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/FcmSet');
    };
    // path://root/community/community_n_nbe
    SettingPnlCtrl.prototype.onClickCommunity = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data) {
            return;
        }
        else if (data.key === 'qq') {
            GameHelper_1.gameHpr.copyToClipboard(data.url, 'toast.yet_copy_clipboard');
        }
        else {
            cc.sys.openURL(data.url);
        }
    };
    // path://root/sound/bgm/bgm_slider_se_n
    SettingPnlCtrl.prototype.onClickBgmSlider = function (event, data) {
        var progress = event.progress;
        audioMgr.bgmVolume = progress;
        this.changeSoundNode(this.bgmNode_, this.bgmSlider, progress);
    };
    // path://root/sound/sfx/sfx_slider_se_n
    SettingPnlCtrl.prototype.onClickSfxSlider = function (event, data) {
        var progress = event.progress;
        audioMgr.sfxVolume = progress;
        this.changeSoundNode(this.sfxNode_, this.sfxSlider, progress);
    };
    // path://root/version_be_n
    SettingPnlCtrl.prototype.onClickVersion = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/VersionDesc');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 有pnl打开
    SettingPnlCtrl.prototype.onPnlEnter = function (pnl) {
        var _a, _b;
        if (pnl.node.zIndex > this.node.zIndex) {
            (_a = this.wxFeedBackNode) === null || _a === void 0 ? void 0 : _a.hide();
            (_b = this.wxGameHubNode) === null || _b === void 0 ? void 0 : _b.hide();
        }
    };
    // 有pnl隐藏
    SettingPnlCtrl.prototype.onPnlLeave = function (pnl) {
        var _a, _b;
        var topPnl = null, list = mc.getOpenPnls();
        for (var i = list.length - 1; i >= 0; i--) {
            var pnl_1 = list[i];
            if (pnl_1.key !== 'common/Top') {
                topPnl = pnl_1;
                break;
            }
        }
        if (topPnl.key === this.key) {
            (_a = this.wxFeedBackNode) === null || _a === void 0 ? void 0 : _a.show();
            (_b = this.wxGameHubNode) === null || _b === void 0 ? void 0 : _b.show();
        }
    };
    // 切换语言
    SettingPnlCtrl.prototype.onLanguageChanged = function () {
        this.updateCommunityButtons();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    SettingPnlCtrl.prototype.initSoundVolume = function () {
        this.bgmSlider = this.bgmSliderNode_.Component(cc.Slider);
        this.sfxSlider = this.sfxSliderNode_.Component(cc.Slider);
        this.bgmSlider.progress = audioMgr.bgmVolume;
        this.sfxSlider.progress = audioMgr.sfxVolume;
        this.changeSoundNode(this.bgmNode_, this.bgmSlider, audioMgr.bgmVolume);
        this.changeSoundNode(this.sfxNode_, this.sfxSlider, audioMgr.sfxVolume);
    };
    SettingPnlCtrl.prototype.changeSoundNode = function (node, slider, progress) {
        slider.progress = progress;
        node.Child('icon', cc.MultiFrame).setFrame(!!progress);
        slider.Child('bar').width = progress * slider.node.width + slider.handle.node.width / 2;
    };
    SettingPnlCtrl.prototype.initFeedBackNode = function () {
        if (this.wxFeedBackNode) {
            this.wxFeedBackNode.show();
        }
        else {
            this.wxFeedBackNode = WxHelper_1.wxHelper.createFeedbackButton(this.feedbackNode_);
        }
    };
    SettingPnlCtrl.prototype.initGameClubNode = function () {
        if (this.wxGameHubNode) {
            this.wxGameHubNode.show();
        }
        else {
            this.wxGameHubNode = WxHelper_1.wxHelper.createGameClubButton(this.gameHubNode);
        }
    };
    // 刷新社区按钮
    SettingPnlCtrl.prototype.updateCommunityButtons = function () {
        if (this.communityNode_.parent.active = !GameHelper_1.gameHpr.isSpectate()) {
            var titleNode = this.communityNode_.parent.Child('title'), titleLbl = titleNode.Child('val', cc.Label);
            titleLbl.setLocaleKey('ui.player_community');
            titleLbl._forceUpdateRenderData();
            var w = (titleNode.width - titleLbl.node.width - 16) / 2;
            titleNode.Child('0').width = titleNode.Child('1').width = w;
            // 获取配置
            var config = GameHelper_1.gameHpr.isGLobal() ? CommunityConfig_1.COMMUNITY_CONFIG_GLOBAL : CommunityConfig_1.COMMUNITY_CONFIG_INLAND;
            var arr_1 = config[mc.lang] || config.default;
            this.communityNode_.children.forEach(function (m) {
                var data = m.Data = arr_1.find(function (x) { return x.key === m.name; });
                m.active = !!data;
            });
            this.gameHubNode.active = ut.isWechatGame();
        }
    };
    // 显示语言
    SettingPnlCtrl.prototype.updateShowLanguage = function () {
        var _a;
        var node = this.languageNode_;
        if (node.active) {
            var lang_1 = mc.lang;
            node.Child('root/val', cc.Label).string = ((_a = Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === lang_1; })) === null || _a === void 0 ? void 0 : _a.text) || '';
        }
    };
    SettingPnlCtrl.prototype.updateListPosition = function () {
        if (this.preRootHeight !== this.root.height || this.preRootScale !== this.root.scale) {
            this.preRootHeight = this.root.height;
            this.preRootScale = this.root.scale;
            var node = this.languageBoxNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.languageNode_.Child('root/list'), this.languageBoxNode_));
            node.scale = this.root.scale;
        }
    };
    SettingPnlCtrl.prototype.showLanguageList = function () {
        this.languageBoxNode_.active = true;
        this.updateListPosition();
        var lang = mc.lang;
        this.languageBoxNode_.Child('root').Items(Constant_1.LANGUAGE_TEXT_LIST, function (it, data) {
            it.Data = data;
            it.Child('val', cc.Label).string = data.text;
            it.Child('select').active = data.lang === lang;
        });
    };
    SettingPnlCtrl = __decorate([
        ccclass
    ], SettingPnlCtrl);
    return SettingPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SettingPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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