
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/BuffInfoBoxPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f92f8zxjRtOc5e85MvwRdgd', 'BuffInfoBoxPnlCtrl');
// app/script/view/common/BuffInfoBoxPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BuffInfoBoxPnlCtrl = /** @class */ (function (_super) {
    __extends(BuffInfoBoxPnlCtrl, _super);
    function BuffInfoBoxPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconNode_ = null; // path://root/icon_n
        _this.descNode_ = null; // path://root/desc_n
        _this.tipNode_ = null; // path://root/tip_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BuffInfoBoxPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuffInfoBoxPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuffInfoBoxPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.iconNode_.Component(cc.MultiFrame).setFrame(data.iconType);
        ResHelper_1.resHelper.loadBuffIcon(data.icon, this.iconNode_.Child('val'), this.key);
        this.iconNode_.Child('name').setLocaleKey(data.name);
        this.iconNode_.Child('type').setLocaleKey('ui.buff_effect_type_' + data.effectType);
        if (data.round >= 0) {
            this.iconNode_.Child('round').setLocaleKey('ui.surplus_round_count', Math.min(data.round, data.needRound));
        }
        else {
            this.iconNode_.Child('round').setLocaleKey('ui.forever_round_count');
        }
        var desc = data.desc, params = data.getDescParams();
        if (data.type === Enums_1.BuffType.HIT_SUCK_BLOOD && data.value >= 30) {
            desc = 'buffText.desc_16_1'; //环刀特殊处理
        }
        else if (data.type === Enums_1.BuffType.TOUGH && data.value >= data.tempParam) {
            desc = 'buffText.desc_43_1'; //曹仁 满层坚韧特殊处理
        }
        else if (data.type === Enums_1.BuffType.CHECK_ABNEGATION && !params) {
            desc = 'buffText.desc_64_1'; //吕蒙 检测克己满的处理
            params = data.tempParam;
        }
        else if (data.type === Enums_1.BuffType.COURAGEOUSLY && data.value >= data.tempParam) {
            desc = 'buffText.desc_90'; //典韦 满层奋勇特殊处理
        }
        this.descNode_.setLocaleKey(desc, params);
        // 韬略buff
        if (this.tipNode_.active = !!data.tip) {
            (_a = this.tipNode_).setLocaleKey.apply(_a, __spread([data.tip], data.tipParams));
        }
    };
    BuffInfoBoxPnlCtrl.prototype.onRemove = function () {
    };
    BuffInfoBoxPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    BuffInfoBoxPnlCtrl = __decorate([
        ccclass
    ], BuffInfoBoxPnlCtrl);
    return BuffInfoBoxPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuffInfoBoxPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcQnVmZkluZm9Cb3hQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEscURBQXVEO0FBQ3ZELDJEQUEwRDtBQUdsRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFnRCxzQ0FBYztJQUE5RDtRQUFBLHFFQXlEQztRQXZERywwQkFBMEI7UUFDbEIsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLHFCQUFxQjtRQUMvQyxlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMscUJBQXFCO1FBQy9DLGNBQVEsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7O1FBNkNyRCxpSEFBaUg7UUFDakgsMkJBQTJCO1FBQzNCLE1BQU07UUFDTixpSEFBaUg7UUFFakgsaUhBQWlIO0lBRXJILENBQUM7SUFuREcsTUFBTTtJQUVDLDRDQUFlLEdBQXRCO1FBQ0ksT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRVkscUNBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0sb0NBQU8sR0FBZCxVQUFlLElBQWE7O1FBQ3hCLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1FBQy9ELHFCQUFTLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ3hFLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDcEQsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsWUFBWSxDQUFDLHNCQUFzQixHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtRQUNuRixJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxFQUFFO1lBQ2pCLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLFlBQVksQ0FBQyx3QkFBd0IsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUE7U0FDN0c7YUFBTTtZQUNILElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLFlBQVksQ0FBQyx3QkFBd0IsQ0FBQyxDQUFBO1NBQ3ZFO1FBQ0QsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksRUFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO1FBQ25ELElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxnQkFBUSxDQUFDLGNBQWMsSUFBSSxJQUFJLENBQUMsS0FBSyxJQUFJLEVBQUUsRUFBRTtZQUMzRCxJQUFJLEdBQUcsb0JBQW9CLENBQUEsQ0FBQyxRQUFRO1NBQ3ZDO2FBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLGdCQUFRLENBQUMsS0FBSyxJQUFJLElBQUksQ0FBQyxLQUFLLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTtZQUNyRSxJQUFJLEdBQUcsb0JBQW9CLENBQUEsQ0FBQyxhQUFhO1NBQzVDO2FBQU0sSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLGdCQUFRLENBQUMsZ0JBQWdCLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDM0QsSUFBSSxHQUFHLG9CQUFvQixDQUFBLENBQUMsYUFBYTtZQUN6QyxNQUFNLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQTtTQUMxQjthQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxnQkFBUSxDQUFDLFlBQVksSUFBSSxJQUFJLENBQUMsS0FBSyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDNUUsSUFBSSxHQUFHLGtCQUFrQixDQUFBLENBQUMsYUFBYTtTQUMxQztRQUNELElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUN6QyxTQUFTO1FBQ1QsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRTtZQUNuQyxDQUFBLEtBQUEsSUFBSSxDQUFDLFFBQVEsQ0FBQSxDQUFDLFlBQVkscUJBQUMsSUFBSSxDQUFDLEdBQUcsR0FBSyxJQUFJLENBQUMsU0FBUyxHQUFDO1NBQzFEO0lBQ0wsQ0FBQztJQUVNLHFDQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sb0NBQU8sR0FBZDtRQUNJLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDM0MsQ0FBQztJQWhEZ0Isa0JBQWtCO1FBRHRDLE9BQU87T0FDYSxrQkFBa0IsQ0F5RHRDO0lBQUQseUJBQUM7Q0F6REQsQUF5REMsQ0F6RCtDLEVBQUUsQ0FBQyxXQUFXLEdBeUQ3RDtrQkF6RG9CLGtCQUFrQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJ1ZmZUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgcmVzSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvUmVzSGVscGVyXCI7XG5pbXBvcnQgQnVmZk9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9CdWZmT2JqXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJ1ZmZJbmZvQm94UG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSBpY29uTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2ljb25fblxuICAgIHByaXZhdGUgZGVzY05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9kZXNjX25cbiAgICBwcml2YXRlIHRpcE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC90aXBfblxuICAgIC8vQGVuZFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IEJ1ZmZPYmopIHtcbiAgICAgICAgdGhpcy5pY29uTm9kZV8uQ29tcG9uZW50KGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKGRhdGEuaWNvblR5cGUpXG4gICAgICAgIHJlc0hlbHBlci5sb2FkQnVmZkljb24oZGF0YS5pY29uLCB0aGlzLmljb25Ob2RlXy5DaGlsZCgndmFsJyksIHRoaXMua2V5KVxuICAgICAgICB0aGlzLmljb25Ob2RlXy5DaGlsZCgnbmFtZScpLnNldExvY2FsZUtleShkYXRhLm5hbWUpXG4gICAgICAgIHRoaXMuaWNvbk5vZGVfLkNoaWxkKCd0eXBlJykuc2V0TG9jYWxlS2V5KCd1aS5idWZmX2VmZmVjdF90eXBlXycgKyBkYXRhLmVmZmVjdFR5cGUpXG4gICAgICAgIGlmIChkYXRhLnJvdW5kID49IDApIHtcbiAgICAgICAgICAgIHRoaXMuaWNvbk5vZGVfLkNoaWxkKCdyb3VuZCcpLnNldExvY2FsZUtleSgndWkuc3VycGx1c19yb3VuZF9jb3VudCcsIE1hdGgubWluKGRhdGEucm91bmQsIGRhdGEubmVlZFJvdW5kKSlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuaWNvbk5vZGVfLkNoaWxkKCdyb3VuZCcpLnNldExvY2FsZUtleSgndWkuZm9yZXZlcl9yb3VuZF9jb3VudCcpXG4gICAgICAgIH1cbiAgICAgICAgbGV0IGRlc2MgPSBkYXRhLmRlc2MsIHBhcmFtcyA9IGRhdGEuZ2V0RGVzY1BhcmFtcygpXG4gICAgICAgIGlmIChkYXRhLnR5cGUgPT09IEJ1ZmZUeXBlLkhJVF9TVUNLX0JMT09EICYmIGRhdGEudmFsdWUgPj0gMzApIHtcbiAgICAgICAgICAgIGRlc2MgPSAnYnVmZlRleHQuZGVzY18xNl8xJyAvL+eOr+WIgOeJueauiuWkhOeQhlxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gQnVmZlR5cGUuVE9VR0ggJiYgZGF0YS52YWx1ZSA+PSBkYXRhLnRlbXBQYXJhbSkge1xuICAgICAgICAgICAgZGVzYyA9ICdidWZmVGV4dC5kZXNjXzQzXzEnIC8v5pu55LuBIOa7oeWxguWdmumfp+eJueauiuWkhOeQhlxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gQnVmZlR5cGUuQ0hFQ0tfQUJORUdBVElPTiAmJiAhcGFyYW1zKSB7XG4gICAgICAgICAgICBkZXNjID0gJ2J1ZmZUZXh0LmRlc2NfNjRfMScgLy/lkJXokpkg5qOA5rWL5YWL5bex5ruh55qE5aSE55CGXG4gICAgICAgICAgICBwYXJhbXMgPSBkYXRhLnRlbXBQYXJhbVxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEudHlwZSA9PT0gQnVmZlR5cGUuQ09VUkFHRU9VU0xZICYmIGRhdGEudmFsdWUgPj0gZGF0YS50ZW1wUGFyYW0pIHtcbiAgICAgICAgICAgIGRlc2MgPSAnYnVmZlRleHQuZGVzY185MCcgLy/lhbjpn6Yg5ruh5bGC5aWL5YuH54m55q6K5aSE55CGXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5kZXNjTm9kZV8uc2V0TG9jYWxlS2V5KGRlc2MsIHBhcmFtcylcbiAgICAgICAgLy8g6Z+s55WlYnVmZlxuICAgICAgICBpZiAodGhpcy50aXBOb2RlXy5hY3RpdmUgPSAhIWRhdGEudGlwKSB7XG4gICAgICAgICAgICB0aGlzLnRpcE5vZGVfLnNldExvY2FsZUtleShkYXRhLnRpcCwgLi4uZGF0YS50aXBQYXJhbXMpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuICAgIH1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG4gICAgLy9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbn1cbiJdfQ==