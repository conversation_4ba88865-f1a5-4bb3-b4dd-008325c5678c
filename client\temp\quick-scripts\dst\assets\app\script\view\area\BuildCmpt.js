
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0ac0fi0kZVCTZRI+SlZjQhQ', 'BuildCmpt');
// app/script/view/area/BuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var DragTouchCmpt_1 = require("./DragTouchCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 建筑
var BuildCmpt = /** @class */ (function (_super) {
    __extends(BuildCmpt, _super);
    function BuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.lvNode = null; //等级节点
        _this.shadowNode = null;
        _this.upLvAnimNode = null; //升级动画
        _this.drillPawnNode = null; //训练的士兵
        _this.forgeEquipNode = null; //打造装备
        _this.touchCmpt = null;
        _this.prePosition = cc.v2();
        _this.isClickSelect = false; //是否可以点击选择
        _this.selecting = false; //是否选择
        _this.editState = ''; //编辑状态
        _this._temp_vec2_3 = cc.v2();
        return _this;
    }
    BuildCmpt.prototype.init = function (data, origin, originY, owner) {
        _super.prototype.init.call(this, data, origin, originY, owner);
        this.drillPawnNode = this.FindChild('drill_pawn');
        if (this.drillPawnNode) {
            this.drillPawnNode.zIndex = 10;
        }
        this.forgeEquipNode = this.FindChild('forge_equip');
        if (this.forgeEquipNode) {
            this.forgeEquipNode.zIndex = 10;
        }
        if (GameHelper_1.gameHpr.getUid() === owner && data.aIndex >= 0) {
            if (data.isCanDrag()) {
                this.touchCmpt = this.body.addComponent(DragTouchCmpt_1.default).init(this);
                this.initShadow();
            }
            else if (data.id === Constant_1.BUILD_MAIN_NID) {
                this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this).setTarget(this.body.FindChild('val')).setPlayAction(true);
            }
            else {
                this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this);
            }
        }
        else {
            this.touchCmpt = null;
        }
        this.initBaseInfo();
        return this;
    };
    BuildCmpt.prototype.initBaseInfo = function () {
        this.node.scale = 1;
        this.selecting = false;
        this.syncPoint();
        this.syncZindex();
        // 初始化等级
        this.initLv();
        // 显示是否在升级中
        this.updateUpLvAnim();
        // 是否训练士兵重
        this.updateDrillPawn();
        // 是否打造装备
        this.updateForgeEquip();
    };
    // 重新同步
    BuildCmpt.prototype.resync = function (data, owner) {
        this.data = data;
        this.syncPoint();
        this.syncZindex();
        this.updateLv(this.data.lv);
        this.updateUpLvAnim();
        this.updateDrillPawn();
        this.updateForgeEquip();
        this.setCanClick(true);
        this.setCanClickSelect(false);
        return this;
    };
    BuildCmpt.prototype.initLv = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.lvNode = cc.instantiate2(pfb, this.node);
                        this.lvNode.zIndex = 1;
                        if (this.data.id === Constant_1.BUILD_MAIN_NID) {
                            this.lvNode.setPosition((this.data.size.x - 1) / 2 * Constant_1.TILE_SIZE, this.getBuildY() + 48);
                        }
                        else {
                            this.lvNode.setPosition(0, this.getBuildY() + 8);
                        }
                        this.updateLv(this.data.lv);
                        this.lvNode.active = !this.selecting;
                        return [2 /*return*/];
                }
            });
        });
    };
    BuildCmpt.prototype.initShadow = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/SHADOW', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.shadowNode = cc.instantiate2(pfb, this.node);
                        this.shadowNode.zIndex = -1;
                        this.shadowNode.setPosition(0, -Constant_1.BUILD_DRAG_OFFSETY);
                        this.shadowNode.Items(this.data.points, function (it, point) {
                            it.Data = point.clone();
                            it.opacity = 80;
                            it.Color(cc.Color.GREEN);
                            it.setPosition(point.mul(Constant_1.TILE_SIZE));
                        });
                        this.shadowNode.active = false;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载升级动画
    BuildCmpt.prototype.loadUpLvAnim = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_BT', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.upLvAnimNode = cc.instantiate2(pfb, this.node);
                        this.upLvAnimNode.zIndex = 1;
                        if (this.data.id === Constant_1.BUILD_MAIN_NID) {
                            this.upLvAnimNode.setPosition((this.data.size.x - 1) / 2 * Constant_1.TILE_SIZE, this.getBuildY() + 40);
                        }
                        else {
                            this.upLvAnimNode.setPosition(0, this.getBuildY());
                        }
                        this.upLvAnimNode.active = false;
                        this.updateUpLvAnim();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载训练的士兵
    BuildCmpt.prototype.loadDrillPawnBody = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var root, node, pfb, anim;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.drillPawnNode) {
                            return [2 /*return*/];
                        }
                        root = this.drillPawnNode.Child('pawn');
                        node = root.children[0];
                        if ((node === null || node === void 0 ? void 0 : node.Data) === id) {
                            return [2 /*return*/];
                        }
                        root.removeAllChildren();
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/ROLE_' + id, cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, root);
                        node.Data = id;
                        if (this.data.id !== Constant_1.BUILD_HOSPITAL_NID) {
                            anim = this.data.id === Constant_1.BUILD_DRILLGROUND_NID || this.data.id === Constant_1.BUILD_PLANT_NID ? '_drill' : '_walk';
                            node.Child('body/anim', cc.Animation).play('role_' + id + anim);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    BuildCmpt.prototype.clean = function () {
        var _a, _b, _c, _d, _e, _f;
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        (_a = this.shadowNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.shadowNode = null;
        (_b = this.lvNode) === null || _b === void 0 ? void 0 : _b.destroy();
        this.lvNode = null;
        (_c = this.upLvAnimNode) === null || _c === void 0 ? void 0 : _c.destroy();
        this.upLvAnimNode = null;
        (_d = this.drillPawnNode) === null || _d === void 0 ? void 0 : _d.Child('pawn').removeAllChildren();
        (_e = this.drillPawnNode) === null || _e === void 0 ? void 0 : _e.setActive(false);
        if (this.forgeEquipNode) {
            this.forgeEquipNode.Child('equip').Data = null;
            this.forgeEquipNode.active = false;
        }
        (_f = this.touchCmpt) === null || _f === void 0 ? void 0 : _f.clean();
        this.node.destroy();
    };
    // 刷新等级
    BuildCmpt.prototype.updateLv = function (lv) {
        if (this.lvNode) {
            this.lvNode.Child('val', cc.Label).string = '' + lv;
        }
    };
    // 刷新升级动画
    BuildCmpt.prototype.updateUpLvAnim = function () {
        var _a, _b, _c;
        if (this.selecting) {
            return;
        }
        var bt = GameHelper_1.gameHpr.player.getBuildBtInfo(this.uid);
        if (!bt) {
            (_a = this.upLvAnimNode) === null || _a === void 0 ? void 0 : _a.setActive(false);
            (_b = this.lvNode) === null || _b === void 0 ? void 0 : _b.setActive(true);
        }
        else if (this.upLvAnimNode) {
            (_c = this.lvNode) === null || _c === void 0 ? void 0 : _c.setActive(false);
            this.upLvAnimNode.active = true;
            var isRuning = bt.isRuning();
            this.upLvAnimNode.Child('anim').active = isRuning;
            if (isRuning) {
                this.upLvAnimNode.Child('time', cc.LabelTimer).run(bt.getSurplusTime() * 0.001);
            }
            else {
                this.upLvAnimNode.Child('time', cc.LabelTimer).string = '0:00';
            }
        }
        else {
            this.loadUpLvAnim();
        }
    };
    // 刷新训练士兵
    BuildCmpt.prototype.updateDrillPawn = function () {
        var _a, _b, _c;
        if (!this.drillPawnNode) {
            return;
        }
        var list = null;
        if (this.owner === GameHelper_1.gameHpr.getUid() && this.data.aIndex >= 0) { //只有在自己的领地能看见训练
            list = this.data.id === Constant_1.BUILD_DRILLGROUND_NID ? GameHelper_1.gameHpr.player.getPawnLevelingQueues() : this.data.id === Constant_1.BUILD_HOSPITAL_NID ? GameHelper_1.gameHpr.player.getCuringPawnsQueue() : GameHelper_1.gameHpr.player.getPawnDrillQueues(this.data.uid);
        }
        if (!list || list.length === 0) {
            this.drillPawnNode.Child('pawn').removeAllChildren();
            this.drillPawnNode.active = false;
            return;
        }
        this.drillPawnNode.active = true;
        var data = list.sort(function (a, b) { return b.surplusTime - a.surplusTime; })[0];
        var skinId = 0;
        if (this.data.id === Constant_1.BUILD_DRILLGROUND_NID) { //这里取这个士兵的皮肤
            skinId = (_b = (_a = GameHelper_1.gameHpr.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.getPawnByPrecise(data.auid, data.puid)) === null || _b === void 0 ? void 0 : _b.getViewId();
        }
        else {
            skinId = (_c = GameHelper_1.gameHpr.player.getConfigPawnInfo(data.id)) === null || _c === void 0 ? void 0 : _c.skinId;
        }
        this.loadDrillPawnBody(skinId || data.id);
        this.drillPawnNode.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
        var lvLbl = this.drillPawnNode.Child('lv', cc.Label);
        if (lvLbl) {
            lvLbl.string = data.lv + '';
        }
        // 治疗特效
        if (this.data.id === Constant_1.BUILD_HOSPITAL_NID) {
            var effectAnim = this.drillPawnNode.Child('effect', cc.Animation);
            effectAnim.play();
        }
    };
    BuildCmpt.prototype.updateForgeEquip = function () {
        if (!this.forgeEquipNode) {
            return;
        }
        var data = null, root = this.forgeEquipNode.Child('equip');
        if (this.owner === GameHelper_1.gameHpr.getUid() && this.data.aIndex >= 0) { //只有在自己的领地能看见训练
            data = GameHelper_1.gameHpr.player.getCurrForgeEquip();
        }
        if (!data) {
            root.Data = null;
            this.forgeEquipNode.active = false;
            return;
        }
        this.forgeEquipNode.active = true;
        if (root.Data !== data.id) {
            root.Data = data.id;
            ResHelper_1.resHelper.loadEquipIcon(data.id, root, 'area', data.getSmeltCount());
        }
        this.forgeEquipNode.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
    };
    // 设置是否可以点击
    BuildCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 设置可以点击选择
    BuildCmpt.prototype.setCanClickSelect = function (val) {
        if (this.touchCmpt) {
            this.isClickSelect = val;
            this.touchCmpt.setCanClickSelect(val);
        }
    };
    // 设置偏移位置 根据point
    BuildCmpt.prototype.setOffsetPositionByPoint = function (point) {
        var pos = this.getActPixelByPoint(point);
        this.node.setPosition(pos.x, pos.y + Constant_1.BUILD_DRAG_OFFSETY);
    };
    // 刷新编辑状态和网格
    BuildCmpt.prototype.updateEditState = function (groundPoints, point) {
        var _a;
        var points = this.data.getActPoints(point);
        var isCanPlace = points.every(function (m) { return groundPoints[m.ID()]; });
        this.editState = isCanPlace ? '' : ECode_1.ecode.POINT_OCCUPIED;
        (_a = this.shadowNode) === null || _a === void 0 ? void 0 : _a.children.forEach(function (m) {
            var p = m.Data || cc.v2();
            var id = (p.x + point.x) + '_' + (p.y + point.y);
            m.Color(groundPoints[id] ? cc.Color.GREEN : cc.Color.RED);
        });
        return this.editState;
    };
    // 触摸事件
    BuildCmpt.prototype.onTouchEvent = function (type, event) {
        if (type === Enums_1.DragTouchType.LONG_PRESS) {
            this.playSelect();
        }
        else if (this.selecting) {
            if (type === Enums_1.DragTouchType.DRAG_MOVE) {
                this._temp_vec2_3.set(CameraCtrl_1.cameraCtrl.convertWorldSub(event.getLocation(), event.getStartLocation())).addSelf(this.prePosition);
                eventCenter.emit(EventType_1.default.MOVE_BUILD, this, this._temp_vec2_3);
            }
            else if (type === Enums_1.DragTouchType.DRAG_PRESS) {
                this.prePosition.set2(this.node.x, this.node.y);
            }
        }
        else if (type === Enums_1.DragTouchType.CLICK) {
            if (!this.isClickSelect) {
                this.onClick();
            }
            else if (this.touchCmpt) {
                this.touchCmpt.select();
                this.playSelect();
            }
        }
    };
    // 被点击
    BuildCmpt.prototype.onClick = function () {
        if (!this.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.data.lv <= 0) {
            ViewHelper_1.viewHelper.showAlert('toast.build_createing');
        }
        else {
            ViewHelper_1.viewHelper.showPnl(this.data.getUIUrl(), this.data);
        }
    };
    // 播放选择
    BuildCmpt.prototype.playSelect = function () {
        this.node.y += Constant_1.BUILD_DRAG_OFFSETY;
        this._onSelect();
        eventCenter.emit(EventType_1.default.LONG_PRESS_BUILD, this);
    };
    // 直接选择
    BuildCmpt.prototype.select = function (point) {
        var _a;
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.select();
        var pos = this.getActPixelByPoint(point);
        this.node.setPosition(pos.x, pos.y + Constant_1.BUILD_DRAG_OFFSETY);
        this._onSelect();
    };
    BuildCmpt.prototype._onSelect = function () {
        var _a, _b, _c, _d;
        this.selecting = true;
        this.node.zIndex = cc.macro.MAX_ZINDEX;
        (_a = this.shadowNode) === null || _a === void 0 ? void 0 : _a.setActive(true);
        (_b = this.lvNode) === null || _b === void 0 ? void 0 : _b.setActive(false);
        (_c = this.body.Child('di')) === null || _c === void 0 ? void 0 : _c.setActive(false);
        (_d = this.upLvAnimNode) === null || _d === void 0 ? void 0 : _d.setActive(false);
        this.prePosition.set2(this.node.x, this.node.y);
    };
    // 取消
    BuildCmpt.prototype.cancel = function () {
        if (!this.data) {
            return;
        }
        this._cancelSelect();
        var pos = this.getActPixelByPoint(this.point);
        this.node.setPosition(pos.x, pos.y + Constant_1.BUILD_DRAG_OFFSETY);
        this.laydown(pos);
    };
    BuildCmpt.prototype._cancelSelect = function () {
        var _a, _b, _c, _d;
        this.syncZindex();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.cancel();
        this.selecting = false;
        (_b = this.shadowNode) === null || _b === void 0 ? void 0 : _b.setActive(false);
        (_c = this.lvNode) === null || _c === void 0 ? void 0 : _c.setActive(true);
        (_d = this.body.Child('di')) === null || _d === void 0 ? void 0 : _d.setActive(true);
        this.updateUpLvAnim();
    };
    // 确定
    BuildCmpt.prototype.confirm = function (point) {
        this._cancelSelect();
        this.laydown(this.getActPixelByPoint(point));
        if (this.data && !this.point.equals(point)) {
            this.point.set(point);
            // 将位置同步到服务器
            NetHelper_1.netHelper.sendMoveAreaBuild({ index: this.data.aIndex, uid: this.data.uid, point: this.point.toJson() });
        }
    };
    // 放下
    BuildCmpt.prototype.laydown = function (pos) {
        cc.tween(this.node).to(0.1, { x: pos.x, y: pos.y }).start();
    };
    BuildCmpt = __decorate([
        ccclass
    ], BuildCmpt);
    return BuildCmpt;
}(BaseBuildCmpt_1.default));
exports.default = BuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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