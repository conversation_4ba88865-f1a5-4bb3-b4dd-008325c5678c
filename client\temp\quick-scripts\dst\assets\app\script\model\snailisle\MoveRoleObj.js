
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/MoveRoleObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aaf7729Lh1HZpRWwPO9RgZ/', 'MoveRoleObj');
// app/script/model/snailisle/MoveRoleObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var MapHelper_1 = require("../../common/helper/MapHelper");
var AStar_1 = require("./AStar");
var BaseRoleObj_1 = require("./BaseRoleObj");
var MapSceneHelper_1 = require("./MapSceneHelper");
// 移动npc
var MoveRoleObj = /** @class */ (function (_super) {
    __extends(MoveRoleObj, _super);
    function MoveRoleObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.checkZindexPoint = cc.v2(-1, -1); //用于移动zindex检测
        _this.pauseTime = 0; //暂停时间 -1永久暂停
        _this.initVelocity = 15;
        _this.velocity = 15; //移动速度 每秒移动的距离
        _this.speed = cc.v2(); //加速度
        _this.paths = []; //当前移动路径
        _this.tempPosition = cc.v2();
        _this.targetPosition = null; //当前目标位置
        _this.astar = new AStar_1.default();
        _this.action = 'move'; //动作
        _this.velocityRatio = 1;
        _this.dir = 1; //当前方向
        _this.searching = false; //是否搜索中
        _this.area = null;
        _this.tempVec1 = cc.v2();
        _this.tempVec2 = cc.v2();
        return _this;
    }
    // 初始化地图信息
    MoveRoleObj.prototype.initMapInfo = function (map) {
        _super.prototype.initMapInfo.call(this, map);
        this.searching = false;
        this.paths.length = 0;
        this.targetPosition = null;
        this.astar.setDir(8);
        this.astar.checkCanPass = map.checkCanPass.bind(map);
        this.setArea(map.getMainArea());
        //
        this.setVelocity(this.initVelocity);
        return this;
    };
    MoveRoleObj.prototype.setArea = function (area) {
        this.area = area;
        this.astar.area = area;
        this.astar.mapHeight = area.gridSize.height;
        this.astar.mapWidth = area.gridSize.width;
    };
    // 动态设置速度
    MoveRoleObj.prototype.setVelocity = function (val) {
        this.velocity = val;
        this.velocityRatio = this.velocity / this.initVelocity;
        // 更新当前速度
        if (this.targetPosition) {
            ut.angleToPoint(ut.getAngle(this.position, this.targetPosition), this.velocity, this.speed);
        }
        return this;
    };
    // 暂停
    MoveRoleObj.prototype.isPause = function () { return this.pauseTime > 0 || this.pauseTime === -1; };
    MoveRoleObj.prototype.setPause = function (val) {
        if (val === void 0) { val = -1; }
        this.pauseTime = val;
    };
    MoveRoleObj.prototype.update = function (dt) {
        // 暂停了
        if (this.pauseTime === -1) {
            return;
        }
        else if (this.pauseTime > 0) {
            this.pauseTime = Math.max(this.pauseTime - dt, 0);
            return;
        }
        // 刷新移动
        this.updateMove(dt);
    };
    // 寻路
    MoveRoleObj.prototype.searchPath = function (point, position) {
        return __awaiter(this, void 0, void 0, function () {
            var paths;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!point && !position) {
                            return [2 /*return*/, []];
                        }
                        else if (!this.astar || !this.map) {
                            return [2 /*return*/, null];
                        }
                        this.tempVec1.set(point || this.map.getActPointByPixel(position));
                        this.tempVec2.set(position || this.map.getActPixelByPoint(point));
                        this.searching = true;
                        return [4 /*yield*/, this.astar.searchPath(this.point, this.tempVec1)];
                    case 1:
                        paths = _a.sent();
                        if (paths.length > 0) { //设置最后一个为像素位置
                            paths.pop();
                            paths.push(this.tempVec2.clone());
                        }
                        else if (this.tempVec1.equals(this.point) && !this.tempVec2.equals(this.position)) {
                            paths.push(this.tempVec2.clone());
                        }
                        // cc.log(paths.join2(m => m.ID(), '|'))
                        this.searching = false;
                        return [2 /*return*/, paths];
                }
            });
        });
    };
    // 设置移动路径
    MoveRoleObj.prototype.setPath = function (paths) {
        if (MapHelper_1.mapHelper.clonePath(this.paths, paths)) {
            this.setTargetPosition();
        }
        else {
            this.targetPosition = null;
        }
    };
    // 设置目标点
    MoveRoleObj.prototype.setTargetPosition = function () {
        this.targetPosition = null;
        if (!this.map) {
        }
        else if (this.paths.length > 0) {
            var point = this.paths.shift();
            if (this.paths.length === 0) { //最后一个默认是像素位置
                this.targetPosition = this.tempPosition.set(point);
                this.point.set(this.map.getActPointByPixel(this.targetPosition));
            }
            else {
                this.point.set(point);
                var pos = this.map.getMovePositionByPoint(point);
                this.targetPosition = pos ? this.tempPosition.set(pos) : this.map.getActPixelByPoint(this.point, this.tempPosition);
            }
            // 设置速度
            ut.angleToPoint(ut.getAngle(this.position, this.targetPosition), this.velocity, this.speed);
            // 计算方向
            var dir = ut.normalizeNumber(this.targetPosition.x - this.position.x);
            if (dir !== 0 && dir !== this.dir) {
                this.dir = dir;
            }
            // cc.log(this.point.ID(), '|', this.tempPosition.ID())
        }
        else {
            this.onMoveComplete();
        }
    };
    // 刷新移动
    MoveRoleObj.prototype.updateMove = function (dt) {
        if (!this.targetPosition) {
            return;
        }
        // 刷新位置
        this.position.addSelf(MapSceneHelper_1.mapSceneHelper.amendMoveSpeed(this.speed, this.position, this.targetPosition, dt));
        // 刷新zindex
        this.map.setRoleZindex(this);
        // 是否到达目的
        if (this.position.equals(this.targetPosition)) {
            this.setTargetPosition();
        }
    };
    MoveRoleObj.prototype.clean = function () {
        this.setPause();
        if (this.astar) {
            this.astar.clean();
            this.astar = null;
        }
        _super.prototype.clean.call(this);
    };
    MoveRoleObj.prototype.onMoveComplete = function () { };
    return MoveRoleObj;
}(BaseRoleObj_1.default));
exports.default = MoveRoleObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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