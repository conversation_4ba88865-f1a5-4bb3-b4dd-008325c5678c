
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LogoutTimeTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '85c089iAMVBSqlZT6i8gj8I', 'LogoutTimeTipPnlCtrl');
// app/script/view/login/LogoutTimeTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var LogoutTimeTipPnlCtrl = /** @class */ (function (_super) {
    __extends(LogoutTimeTipPnlCtrl, _super);
    function LogoutTimeTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.timeLbl_ = null; // path://root/content/time_l
        return _this;
    }
    //@end
    LogoutTimeTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LogoutTimeTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                return [2 /*return*/];
            });
        });
    };
    LogoutTimeTipPnlCtrl.prototype.onEnter = function (time) {
        this.timeLbl_.setLocaleKey('login.time_remaining_desc', GameHelper_1.gameHpr.millisecondToString(time));
    };
    LogoutTimeTipPnlCtrl.prototype.onRemove = function () {
    };
    LogoutTimeTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons/revocation_be
    LogoutTimeTipPnlCtrl.prototype.onClickRevocation = function (event, data) {
        this.do();
    };
    // path://root/buttons/change_account_be
    LogoutTimeTipPnlCtrl.prototype.onClickChangeAccount = function (event, data) {
        this.hide();
        GameHelper_1.gameHpr.changeAccount();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    LogoutTimeTipPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_RevocationLogout', {}, true)];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox(err)];
                        }
                        this.hide();
                        this.emit(EventType_1.default.REVOCATION_LOGOUT_DONE);
                        return [2 /*return*/];
                }
            });
        });
    };
    LogoutTimeTipPnlCtrl = __decorate([
        ccclass
    ], LogoutTimeTipPnlCtrl);
    return LogoutTimeTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LogoutTimeTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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