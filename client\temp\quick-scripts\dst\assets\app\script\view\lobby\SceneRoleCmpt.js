
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/SceneRoleCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0e95fFr+21M7IHVTOqQDMQ/', 'SceneRoleCmpt');
// app/script/view/lobby/SceneRoleCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ccclass = cc._decorator.ccclass;
var SceneRoleCmpt = /** @class */ (function (_super) {
    __extends(SceneRoleCmpt, _super);
    function SceneRoleCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.bodyNode = null;
        _this.bodyScaleX = 0;
        _this.anim = null;
        _this.animPrefix = '';
        _this.soul = null; //灵魂
        _this.prePosition = cc.v2();
        _this.preZIndex = 0;
        _this.preAnimation = '';
        _this.preDir = 1;
        return _this;
    }
    SceneRoleCmpt.prototype.onLoad = function () {
        this.load();
    };
    SceneRoleCmpt.prototype.load = function () {
        if (!this.bodyNode) {
            this.bodyNode = this.FindChild('body');
            this.bodyScaleX = this.bodyNode.scaleX;
            this.anim = this.bodyNode.Child('anim', cc.Animation);
        }
    };
    SceneRoleCmpt.prototype.init = function (soul) {
        this.load();
        this.id = soul.id;
        this.animPrefix = Enums_1.MapRoleType[this.id].toLowerCase();
        this.node.opacity = 255;
        this.node.Data = soul.uid;
        this.soul = soul;
        this.updateAnimation();
        this.updatePosition();
        this.updateZindex();
        this.updateDir();
        return this;
    };
    SceneRoleCmpt.prototype.clean = function () {
        if (!this.isActive) {
            return;
        }
        this.node.Data = '';
        this.soul = null;
        this.preZIndex = 0;
        this.prePosition.set2(0, 0);
        this.bodyNode.stopAllActions();
        this.bodyNode.scale = 1;
        this.node.stopAllActions();
        nodePoolMgr.put(this.node);
    };
    Object.defineProperty(SceneRoleCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.node) === null || _a === void 0 ? void 0 : _a.Data; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(SceneRoleCmpt.prototype, "isActive", {
        get: function () { return this.isValid && this.node && this.node.Data; },
        enumerable: false,
        configurable: true
    });
    SceneRoleCmpt.prototype.getSoul = function () { return this.soul; };
    // 更新动画
    SceneRoleCmpt.prototype.updateAnimation = function () {
        if (this.soul.action !== this.preAnimation) {
            this.preAnimation = this.soul.action;
            this.anim.play(this.animPrefix + '_' + this.preAnimation);
        }
    };
    // 更新位置
    SceneRoleCmpt.prototype.updatePosition = function () {
        if (!this.prePosition.equals(this.soul.position)) {
            this.prePosition.set(this.soul.position);
            this.node.setPosition(this.prePosition);
        }
    };
    // 刷新zindex
    SceneRoleCmpt.prototype.updateZindex = function () {
        if (this.preZIndex !== this.soul.zIndex) {
            this.node.zIndex = this.preZIndex = this.soul.zIndex;
        }
    };
    // 刷新方向
    SceneRoleCmpt.prototype.updateDir = function () {
        if (this.preDir !== this.soul.dir) {
            this.preDir = this.soul.dir;
            this.bodyNode.scaleX = this.preDir * this.bodyScaleX;
        }
    };
    SceneRoleCmpt.prototype.update = function (dt) {
        if (!this.soul) {
            return;
        }
        else if (!this.soul.isPause()) {
            this.updateAnimation();
            this.updatePosition();
            this.updateZindex();
            this.updateDir();
        }
    };
    SceneRoleCmpt = __decorate([
        ccclass
    ], SceneRoleCmpt);
    return SceneRoleCmpt;
}(cc.Component));
exports.default = SceneRoleCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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