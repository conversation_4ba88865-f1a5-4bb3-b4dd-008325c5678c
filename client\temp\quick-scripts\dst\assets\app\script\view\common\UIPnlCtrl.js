
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/UIPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f3649PZhgFKEq4ZWbiImnHu', 'UIPnlCtrl');
// app/script/view/common/UIPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ReddotCmpt_1 = require("../cmpt/ReddotCmpt");
var ChatBarrageCmpt_1 = require("./ChatBarrageCmpt");
var MessageCmpt_1 = require("./MessageCmpt");
var ccclass = cc._decorator.ccclass;
var UIPnlCtrl = /** @class */ (function (_super) {
    __extends(UIPnlCtrl, _super);
    function UIPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.chatBarrageNode_ = null; // path://chat_barrage_n
        _this.messageNode_ = null; // path://message_n
        _this.rightBottomNode_ = null; // path://right_bottom_n
        _this.guideTaskNode_ = null; // path://right_bottom_n/guide_task_be_n
        _this.btingNode_ = null; // path://right_bottom_n/bting_be_n
        _this.marchingNode_ = null; // path://right_bottom_n/marching_be_n
        _this.transitingNode_ = null; // path://right_bottom_n/transiting_be_n
        _this.chatNode_ = null; // path://right_left/chat_n
        _this.sceneNode_ = null; // path://scene_n
        _this.goHomeNode_ = null; // path://scene_n/main/go_home_be_n
        _this.mapFlagNode_ = null; // path://scene_n/main/map_flag_be_n
        _this.shopNode_ = null; // path://scene_n/main/bottom/shop_be_n
        _this.mapNode_ = null; // path://scene_n/main/bottom/map_be_n
        _this.allArmyNode_ = null; // path://scene_n/main/bottom/all_army_be_n
        _this.buildNode_ = null; // path://scene_n/area/bottom/build_be_n
        _this.areaArmyNode_ = null; // path://scene_n/area/bottom/area_army_be_n
        _this.menuNode_ = null; // path://menu/menu_n
        //@end
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.chatBarrageCmpt = null;
        _this.startWorldPoint = cc.v2();
        _this.startPoint = cc.v2();
        _this.guideTaskProgressInfo = null;
        _this.updateGuideTaskStateTime = 0;
        _this.guideTempMap = {};
        return _this;
    }
    UIPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a),
            (_b = {}, _b[mc.Event.WIND_ENTER] = this.onWindEnter, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_REENTER_AREA] = this.onUpdateReenterArea, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_BT_QUEUE] = this.onUpdateBtQueue, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_MC_IN_CAM_RANGE] = this.onUpdateMcInCamRange, _e),
            (_f = {}, _f[EventType_1.default.ADD_MARCH] = this.onAddMarch, _f),
            (_g = {}, _g[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _h),
            (_j = {}, _j[EventType_1.default.ADD_CHAT] = this.onAddChat, _j),
            (_k = {}, _k[EventType_1.default.CHANGE_BARRAGE_AREA] = this.onChangeBarrageArea, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_GUIDE_TASK_LIST] = this.onUpdateGuideTaskList, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_GUIDE_TASK_STATE] = this.onUpdateGuideTaskState, _m),
            (_o = {}, _o[EventType_1.default.CHANGE_SHOW_GUIDE_TASK_TIP] = this.onChangeShowGuideTaskTip, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_ALLI_MAP_FLAG] = this.onUpdateAlliMapFlag, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_MARCH_OPACITY] = this.onUpdateMarchOpacity, _q),
            (_r = {}, _r[EventType_1.default.CLEAN_MESSAGE] = this.onCleanMessage, _r),
        ];
    };
    UIPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isClean: false, isAct: false, isMask: false });
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                this.chatBarrageCmpt = this.chatBarrageNode_.getComponent(ChatBarrageCmpt_1.default);
                // 监听鼠标的缩放
                this.node.on(cc.Node.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
                this.node.on(cc.Node.EventType.MOUSE_MOVE, this.onMouseMove, this);
                return [2 /*return*/];
            });
        });
    };
    UIPnlCtrl.prototype.onEnter = function (init) {
        // if (init) {
        // }
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode, isSpectate = GameHelper_1.gameHpr.isSpectate();
        this.menuNode_.Child('personal').active = !isSpectate;
        this.menuNode_.Child('portrayal').active = !isNoviceMode && !isSpectate;
        this.menuNode_.Child('rank').active = !isNoviceMode;
        this.menuNode_.Child('pointsets').active = !isNoviceMode;
        this.menuNode_.parent.Child('activity_be').active = !isNoviceMode;
        this.chatNode_.active = !isNoviceMode;
        //
        this.startPoint.set(cc.v2(cc.winSize.width / 2, cc.winSize.height / 2));
        this.startWorldPoint.set(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint));
        this.messageNode_.Component(MessageCmpt_1.default).init();
        this.onUpdateBtQueue();
        this.updateMarchQueue();
        this.updateTransitQueue();
        this.updateAlliMapFalg();
        this.onUpdateMcInCamRange(GameHelper_1.gameHpr.world.isMainCityInCameraRange());
        this.onUpdateMarchOpacity();
        // 弹幕
        this.chatBarrageCmpt.init();
        // 刷新任务按钮
        this.onChangeShowGuideTaskTip(GameHelper_1.gameHpr.task.isShowTaskTip());
        this.onUpdateGuideTaskList();
        this.onUpdateGuideTaskState(this.player.getCangetPlayerTask());
    };
    UIPnlCtrl.prototype.onRemove = function () {
        this.messageNode_.Component(MessageCmpt_1.default).clean();
        this.chatBarrageCmpt.clean();
    };
    UIPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://scene_n/area/back_main_be
    UIPnlCtrl.prototype.onClickBackMain = function (event, data) {
        ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
    };
    // path://scene_n/area/bottom/build_be_n
    UIPnlCtrl.prototype.onClickBuild = function (event, _) {
        var _a;
        var data = this.areaCenter.getArea((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index);
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/BuildList', data);
        }
        // 新手村上报
        if (GameHelper_1.gameHpr.isNoviceMode) {
            TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'buildList', uid: GameHelper_1.gameHpr.getUid() });
        }
    };
    // path://right_bottom_n/bting_be_n
    UIPnlCtrl.prototype.onClickBting = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/BTQueue');
    };
    // path://scene_n/main/bottom/map_be_n
    UIPnlCtrl.prototype.onClickMap = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('main/WorldMap');
    };
    // path://scene_n/main/go_home_be_n
    UIPnlCtrl.prototype.onClickGoHome = function (event, data) {
        var _a;
        this.goHomeNode_.active = false;
        GameHelper_1.gameHpr.world.setMainCityInCameraRange(true);
        var mainCityIndex = this.player.getMainCityIndex();
        if (!mainCityIndex) {
            mainCityIndex = ((_a = GameHelper_1.gameHpr.world.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.spectateIndex) || 180300;
        }
        if (mainCityIndex) {
            this.emit(EventType_1.default.MAP_MOVE_TO, cc.v2(0.5, 0.5).addSelf(MapHelper_1.mapHelper.indexToPoint(mainCityIndex)));
        }
    };
    // path://right_bottom_n/marching_be_n
    UIPnlCtrl.prototype.onClickMarching = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/MarchQueue');
    };
    // path://right_bottom_n/transiting_be_n
    UIPnlCtrl.prototype.onClickTransiting = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/TransitQueue');
    };
    // path://scene_n/main/bottom/shop_be_n
    UIPnlCtrl.prototype.onClickShop = function (event, data) {
        ViewHelper_1.viewHelper.showLoadingWait(true);
        ViewHelper_1.viewHelper.showPnl('common/Shop').then(function (pnl) { return pnl.isValid && ViewHelper_1.viewHelper.showLoadingWait(false); });
    };
    // path://right_bottom_n/guide_task_be_n
    UIPnlCtrl.prototype.onClickGuideTask = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/GuideTask');
        // 新手村上报
        if (GameHelper_1.gameHpr.isNoviceMode) {
            TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'guideTask', uid: GameHelper_1.gameHpr.getUid() });
        }
    };
    // path://scene_n/main/bottom/all_army_be_n
    UIPnlCtrl.prototype.onClickAllArmy = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('main/ArmyList');
        // 新手村上报
        if (GameHelper_1.gameHpr.isNoviceMode) {
            TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'mainArmyList', uid: GameHelper_1.gameHpr.getUid() });
        }
    };
    // path://scene_n/area/bottom/area_army_be_n
    UIPnlCtrl.prototype.onClickAreaArmy = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('area/AreaArmy');
        // 新手村上报
        if (GameHelper_1.gameHpr.isNoviceMode) {
            TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'areaArmyList', uid: GameHelper_1.gameHpr.getUid() });
        }
    };
    // path://menu/menu_n/open_menu_be
    UIPnlCtrl.prototype.onClickOpenMenu = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/UIMenuChild');
    };
    // path://scene_n/main/map_flag_be_n
    UIPnlCtrl.prototype.onClickMapFlag = function (event, data) {
        cc.log('onClickMapFlag', data);
    };
    // path://scene_n/area/bottom/hide_ctrl_be
    UIPnlCtrl.prototype.onClickHideCtrl = function (event, data) {
        var pos = ut.convertToNodeAR(event.target, this.node), offset = event.target.height / 2 + 12;
        ViewHelper_1.viewHelper.showPnl('area/AreaUIChild', pos.add(cc.v2(0, offset)));
    };
    // path://menu/activity_be
    UIPnlCtrl.prototype.onClickActivity = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/ActivitiesPnl');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 重新连接
    UIPnlCtrl.prototype.onNetReconnect = function () {
        this.onUpdateBtQueue();
        this.updateMarchQueue();
        this.updateTransitQueue();
        this.updateAlliMapFalg();
        this.onUpdateGuideTaskList();
        this.onUpdateGuideTaskState(this.player.getCangetPlayerTask());
    };
    UIPnlCtrl.prototype.onWindEnter = function (wind, prevKey) {
        var world = GameHelper_1.gameHpr.world;
        var isMain = wind.key === world.getSceneKey();
        var node = this.sceneNode_.Swih(isMain ? 'main' : wind.key)[0];
        // 是自己的地块并且是城市才会有建筑
        if (isMain) {
            var isSpectate = GameHelper_1.gameHpr.isSpectate(), isNovice = wind.key === 'novice';
            this.mapNode_.active = !isNovice;
            this.shopNode_.active = !isNovice && !isSpectate;
            this.rightBottomNode_.active = !isSpectate;
            this.allArmyNode_.active = !isSpectate;
            this.areaArmyNode_.active = !isSpectate;
            this.allArmyNode_.Child('dot', ReddotCmpt_1.default).setKeys(['treasure_main']);
        }
        else if (wind.key === 'area') {
            this.updateAreaArmyButton();
        }
    };
    // 重新进入战斗场景
    UIPnlCtrl.prototype.onUpdateReenterArea = function () {
        this.updateAreaArmyButton();
    };
    // 刷新建造队列
    UIPnlCtrl.prototype.onUpdateBtQueue = function () {
        var data = this.player.getBtQueues().find(function (m) { return m.surplusTime > 0; });
        var b = this.btingNode_.active = !!data;
        b && this.btingNode_.Child('root/time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
    };
    // 刷新主城是否在相机范围内
    UIPnlCtrl.prototype.onUpdateMcInCamRange = function (val) {
        this.goHomeNode_.active = !val;
    };
    // 添加行军
    UIPnlCtrl.prototype.onAddMarch = function (data) {
        if (data.getMarchLineType() === Enums_1.MarchLineType.MERCHANT) {
            this.updateTransitQueue();
        }
        else {
            this.updateMarchQueue();
        }
    };
    // 删除行军
    UIPnlCtrl.prototype.onRemoveMarch = function (data) {
        this.onAddMarch(data);
    };
    // 刷新所有行军
    UIPnlCtrl.prototype.onUpdateAllMarch = function () {
        this.updateMarchQueue();
    };
    // 添加聊天信息
    UIPnlCtrl.prototype.onAddChat = function (data) {
        var _a;
        (_a = this.chatBarrageCmpt) === null || _a === void 0 ? void 0 : _a.addBarrage(data);
    };
    // 刷新弹幕区域
    UIPnlCtrl.prototype.onChangeBarrageArea = function (type, val) {
        var _a;
        (_a = this.chatBarrageCmpt) === null || _a === void 0 ? void 0 : _a.changeSetting(type, val);
    };
    // 刷新新手任务列表
    UIPnlCtrl.prototype.onUpdateGuideTaskList = function () {
        var lastActive = this.guideTaskNode_.active;
        if (this.guideTaskNode_.active = this.player.getPlayerTaskCount() > 0) {
            this.onUpdateGuideTaskState(this.player.getCangetPlayerTask());
            if (!lastActive) {
                var root = this.guideTaskNode_.Child('root');
                AnimHelper_1.animHelper.slideIn(root, 'left', 0.5, 20);
                if (this.guideTaskProgressInfo) {
                    var _a = this.guideTaskProgressInfo, label = _a.label, task = _a.task;
                    label.string = task.getProgressText();
                }
            }
        }
    };
    // 刷新新手任务状态
    UIPnlCtrl.prototype.onUpdateGuideTaskState = function (task) {
        var _a;
        this.guideTaskProgressInfo = null;
        if (this.guideTaskNode_.active) {
            task = task || this.filterGuideTaskShow(this.player.getPlayerAllTasks());
            if (!task) {
                return this.guideTaskNode_.setActive(false);
            }
            var ok = task.isCanClaim();
            this.guideTaskNode_.Child('root/no').active = !ok;
            var guideTaskShowNode = this.guideTaskNode_.Child('root/guide_task/guide_task_be');
            guideTaskShowNode.Child('val', cc.Label).Color(ok ? '#FFEA00' : '#FEFCF0').setLocaleKey(task.desc, task.descParams);
            guideTaskShowNode.Child('done').active = ok;
            var text = task.getProgressText();
            if (guideTaskShowNode.Child('progress').active = !ok && !!text) {
                var lbl = guideTaskShowNode.Child('progress', cc.Label);
                lbl.string = text;
                this.guideTaskProgressInfo = { label: lbl, task: task, progress: (_a = task.cond) === null || _a === void 0 ? void 0 : _a.progress };
            }
        }
    };
    // 切换是否显示新手任务提示
    UIPnlCtrl.prototype.onChangeShowGuideTaskTip = function (val) {
        this.guideTaskNode_.Child('root/guide_task').active = val;
    };
    // 刷新地图标记
    UIPnlCtrl.prototype.onUpdateAlliMapFlag = function () {
        this.updateAlliMapFalg();
    };
    // 刷新行军线透明度 这里检测是否有0的
    UIPnlCtrl.prototype.onUpdateMarchOpacity = function () {
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return;
        }
        var hasZero = false;
        var progressMap = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.SET_MARCH_LINE_OPACITY) || {};
        for (var k in progressMap) {
            if (progressMap[k] === 0) {
                hasZero = true;
                break;
            }
        }
        ReddotHelper_1.reddotHelper.set('march_setting', hasZero);
    };
    UIPnlCtrl.prototype.onCleanMessage = function () {
        this.messageNode_.Component(MessageCmpt_1.default).clean();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 赛选显示任务
    UIPnlCtrl.prototype.filterGuideTaskShow = function (tasks) {
        var w = -1, task = null;
        tasks.forEach(function (m) {
            var val = m.getSortValByShow();
            if (w === -1 || val < w) {
                w = val;
                task = m;
            }
        });
        return task;
    };
    UIPnlCtrl.prototype.updateAreaArmyButton = function () {
        var _a;
        var cell = GameHelper_1.gameHpr.world.getLookCell();
        this.buildNode_.active = !!(cell === null || cell === void 0 ? void 0 : cell.isOwn()) && ((_a = cell === null || cell === void 0 ? void 0 : cell.city) === null || _a === void 0 ? void 0 : _a.id) === Constant_1.CITY_MAIN_NID;
        if (cell) {
            this.areaArmyNode_.Child('dot', ReddotCmpt_1.default).setKeys(['treasure_' + cell.index]);
        }
    };
    // 刷新行军队列
    UIPnlCtrl.prototype.updateMarchQueue = function () {
        var marchs = GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.isCanShowUI(); });
        var data = marchs.sort(function (a, b) {
            var atime = a.getSurplusTime(), btime = b.getSurplusTime();
            if (atime !== btime) {
                return atime - btime;
            }
            return a.notifyIndex - b.notifyIndex;
        })[0];
        if (this.marchingNode_.active = !!data) {
            this.marchingNode_.Child('root/time', cc.LabelTimer).Color(Constant_1.MARCH_ARMY_TIME_COLOR[data.getMarchLineType()]).run(data.getSurplusTime() * 0.001);
            this.marchingNode_.Child('root/warning').active = marchs.some(function (m) { return m.targetType === Enums_1.MarchTargetType.STRIKE; });
        }
    };
    // 刷新运行列表
    UIPnlCtrl.prototype.updateTransitQueue = function () {
        var data = GameHelper_1.gameHpr.world.getTransits().filter(function (m) { return m.isCanShowUI(); }).sort(function (a, b) { return a.getSurplusTime() - b.getSurplusTime(); })[0];
        var b = this.transitingNode_.active = !!data;
        b && this.transitingNode_.Child('root/time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
    };
    // 刷新地图标记
    UIPnlCtrl.prototype.updateAlliMapFalg = function () {
        this.mapNode_.Child('flag').active = !!Object.keys(GameHelper_1.gameHpr.alliance.getMapFlag()).length;
    };
    // 鼠标滚动事件
    UIPnlCtrl.prototype.onMouseWheel = function (event) {
        if (GameHelper_1.gameHpr.guide.isForceWorking()) {
            return;
        }
        var deltaScale = event.getScrollY() * 0.0001;
        CameraCtrl_1.cameraCtrl.zoomRatio += deltaScale;
        if (!CameraCtrl_1.cameraCtrl.scaleIsMinOrMax()) {
            var speed = CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint).subSelf(this.startWorldPoint);
            CameraCtrl_1.cameraCtrl.setPosition(CameraCtrl_1.cameraCtrl.getPosition().subSelf(speed));
        }
    };
    // 鼠标移动事件
    UIPnlCtrl.prototype.onMouseMove = function (event) {
        this.startPoint.set(event.getLocation());
        this.startWorldPoint.set(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint));
    };
    UIPnlCtrl.prototype.addFinger = function (fingerKey) {
        if (this.guideTempMap[fingerKey]) {
            return;
        }
        this.guideTempMap[fingerKey] = true;
        var root = this.guideTaskNode_.Child('root');
        if (!GameHelper_1.gameHpr.isNoLongerTip(fingerKey) && !AnimHelper_1.animHelper.getGuideFinger(fingerKey)) {
            AnimHelper_1.animHelper.addFinger(root, { tag: fingerKey, offset: { x: -100, y: 40 }, angle: 180 }, '_init_novice_');
        }
    };
    UIPnlCtrl.prototype.update = function (dt) {
        var _a, _b;
        if (this.player.getPlayerTaskCount() > 0) {
            this.updateGuideTaskStateTime += dt;
            if (this.updateGuideTaskStateTime >= 1) { //刷新状态
                this.updateGuideTaskStateTime -= 1;
                this.onUpdateGuideTaskState(this.player.getCangetPlayerTask());
            }
            else if (this.guideTaskProgressInfo) { //刷新任务进度信息
                var _c = this.guideTaskProgressInfo, label = _c.label, task = _c.task, progress = _c.progress;
                if (((_a = task.cond) === null || _a === void 0 ? void 0 : _a.progress) !== progress) {
                    this.guideTaskProgressInfo.progress = (_b = task.cond) === null || _b === void 0 ? void 0 : _b.progress;
                    label.string = task.getProgressText();
                }
            }
        }
    };
    UIPnlCtrl = __decorate([
        ccclass
    ], UIPnlCtrl);
    return UIPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = UIPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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