
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BehaviorTree.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8f8c07xrdxNe4UKdUZI8nMp', 'BehaviorTree');
// app/script/model/behavior/BehaviorTree.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var BevTreeFactory_1 = require("./BevTreeFactory");
var BTConstant_1 = require("./BTConstant");
// 行为树
var BehaviorTree = /** @class */ (function () {
    function BehaviorTree() {
        this.root = null;
    }
    BehaviorTree.prototype.load = function (id, target) {
        this.root = id ? this.loadNode(id, target, 0) : null;
        // 打印
        // this.printNode(this.root, 0)
        return this;
    };
    BehaviorTree.prototype.loadNode = function (id, target, index) {
        var _this = this;
        var json = assetsMgr.getJsonData('behavior', id);
        var node = BevTreeFactory_1.bevTreeFactory.newNode(json === null || json === void 0 ? void 0 : json.cls);
        if (!node) {
            logger.error('BehaviorTree load config error, cls=' + (json === null || json === void 0 ? void 0 : json.cls));
            return null;
        }
        node.init(json, target, index);
        // 添加子节点
        ut.stringToNumbers(json.children).forEach(function (m, i) { return node.addChild(_this.loadNode(m, target, i)); });
        return node;
    };
    BehaviorTree.prototype.tick = function (dt) {
        if (!this.root) {
            return;
        }
        var state = this.root.execute(dt);
    };
    BehaviorTree.prototype.isCanRun = function () { return !!this.root; };
    // 打印节点出来
    BehaviorTree.prototype.printNode = function (root, blk) {
        var str = '';
        for (var i = 0; i < blk; i++) {
            str += ' '; //缩进
        }
        cc.log(str + '|—' + root.json.cls);
        if (root.type == BTConstant_1.BTType.DECORATOR) {
            var dec = root;
            if (dec.child) {
                return this.printNode(dec.child, blk + 3);
            }
        }
        else if (root.type === BTConstant_1.BTType.COMPOSITE) {
            var comp = root;
            for (var i = 0, l = comp.getChildrenCount(); i < l; i++) {
                this.printNode(comp.children[i], blk + 3);
            }
        }
    };
    return BehaviorTree;
}());
exports.default = BehaviorTree;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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