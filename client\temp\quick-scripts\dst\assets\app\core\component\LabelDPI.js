
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelDPI.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bedffkn/YlJ56p0Ln9TCNta', 'LabelDPI');
// app/core/component/LabelDPI.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LabelDPI = /** @class */ (function (_super) {
    __extends(LabelDPI, _super);
    function LabelDPI() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.retinaScale = 1;
        return _this;
    }
    LabelDPI.prototype.onLoad = function () {
        var comp = this.getComponent(cc.Label) || this.getComponent(cc.RichText);
        if (comp) {
            comp['retinaScale'] = this.retinaScale;
        }
    };
    __decorate([
        property
    ], LabelDPI.prototype, "retinaScale", void 0);
    LabelDPI = __decorate([
        ccclass,
        menu('自定义组件/LabelDPI')
    ], LabelDPI);
    return LabelDPI;
}(cc.Component));
exports.default = LabelDPI;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxjb21wb25lbnRcXExhYmVsRFBJLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNNLElBQUEsS0FBZ0QsRUFBRSxDQUFDLFVBQVUsRUFBM0QsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFBLEVBQUUsSUFBSSxVQUFBLEVBQUUsZ0JBQWdCLHNCQUFrQixDQUFDO0FBSXBFO0lBQXNDLDRCQUFZO0lBQWxEO1FBQUEscUVBV0M7UUFSVyxpQkFBVyxHQUFXLENBQUMsQ0FBQTs7SUFRbkMsQ0FBQztJQU5HLHlCQUFNLEdBQU47UUFDSSxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUMxRSxJQUFJLElBQUksRUFBRTtZQUNOLElBQUksQ0FBQyxhQUFhLENBQUMsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFBO1NBQ3pDO0lBQ0wsQ0FBQztJQVBEO1FBREMsUUFBUTtpREFDc0I7SUFIZCxRQUFRO1FBRjVCLE9BQU87UUFDUCxJQUFJLENBQUMsZ0JBQWdCLENBQUM7T0FDRixRQUFRLENBVzVCO0lBQUQsZUFBQztDQVhELEFBV0MsQ0FYcUMsRUFBRSxDQUFDLFNBQVMsR0FXakQ7a0JBWG9CLFFBQVEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJcclxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSwgbWVudSwgcmVxdWlyZUNvbXBvbmVudCB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbkBjY2NsYXNzXHJcbkBtZW51KCfoh6rlrprkuYnnu4Tku7YvTGFiZWxEUEknKVxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBMYWJlbERQSSBleHRlbmRzIGNjLkNvbXBvbmVudCB7XHJcblxyXG4gICAgQHByb3BlcnR5XHJcbiAgICBwcml2YXRlIHJldGluYVNjYWxlOiBudW1iZXIgPSAxXHJcblxyXG4gICAgb25Mb2FkKCkge1xyXG4gICAgICAgIGNvbnN0IGNvbXAgPSB0aGlzLmdldENvbXBvbmVudChjYy5MYWJlbCkgfHwgdGhpcy5nZXRDb21wb25lbnQoY2MuUmljaFRleHQpXHJcbiAgICAgICAgaWYgKGNvbXApIHtcclxuICAgICAgICAgICAgY29tcFsncmV0aW5hU2NhbGUnXSA9IHRoaXMucmV0aW5hU2NhbGVcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXX0=