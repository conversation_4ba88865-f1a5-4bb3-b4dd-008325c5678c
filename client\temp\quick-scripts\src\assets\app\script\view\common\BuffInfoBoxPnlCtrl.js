"use strict";
cc._RF.push(module, 'f92f8zxjRtOc5e85MvwRdgd', 'BuffInfoBoxPnlCtrl');
// app/script/view/common/BuffInfoBoxPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BuffInfoBoxPnlCtrl = /** @class */ (function (_super) {
    __extends(BuffInfoBoxPnlCtrl, _super);
    function BuffInfoBoxPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconNode_ = null; // path://root/icon_n
        _this.descNode_ = null; // path://root/desc_n
        _this.tipNode_ = null; // path://root/tip_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BuffInfoBoxPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuffInfoBoxPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuffInfoBoxPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.iconNode_.Component(cc.MultiFrame).setFrame(data.iconType);
        ResHelper_1.resHelper.loadBuffIcon(data.icon, this.iconNode_.Child('val'), this.key);
        this.iconNode_.Child('name').setLocaleKey(data.name);
        this.iconNode_.Child('type').setLocaleKey('ui.buff_effect_type_' + data.effectType);
        if (data.round >= 0) {
            this.iconNode_.Child('round').setLocaleKey('ui.surplus_round_count', Math.min(data.round, data.needRound));
        }
        else {
            this.iconNode_.Child('round').setLocaleKey('ui.forever_round_count');
        }
        var desc = data.desc, params = data.getDescParams();
        if (data.type === Enums_1.BuffType.HIT_SUCK_BLOOD && data.value >= 30) {
            desc = 'buffText.desc_16_1'; //环刀特殊处理
        }
        else if (data.type === Enums_1.BuffType.TOUGH && data.value >= data.tempParam) {
            desc = 'buffText.desc_43_1'; //曹仁 满层坚韧特殊处理
        }
        else if (data.type === Enums_1.BuffType.CHECK_ABNEGATION && !params) {
            desc = 'buffText.desc_64_1'; //吕蒙 检测克己满的处理
            params = data.tempParam;
        }
        else if (data.type === Enums_1.BuffType.COURAGEOUSLY && data.value >= data.tempParam) {
            desc = 'buffText.desc_90'; //典韦 满层奋勇特殊处理
        }
        this.descNode_.setLocaleKey(desc, params);
        // 韬略buff
        if (this.tipNode_.active = !!data.tip) {
            (_a = this.tipNode_).setLocaleKey.apply(_a, __spread([data.tip], data.tipParams));
        }
    };
    BuffInfoBoxPnlCtrl.prototype.onRemove = function () {
    };
    BuffInfoBoxPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    BuffInfoBoxPnlCtrl = __decorate([
        ccclass
    ], BuffInfoBoxPnlCtrl);
    return BuffInfoBoxPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuffInfoBoxPnlCtrl;

cc._RF.pop();