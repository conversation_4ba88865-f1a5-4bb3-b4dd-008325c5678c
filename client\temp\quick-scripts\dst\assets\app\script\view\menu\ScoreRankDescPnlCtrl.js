
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/ScoreRankDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5e0e0cGkhZO07TAWbvtA3+q', 'ScoreRankDescPnlCtrl');
// app/script/view/menu/ScoreRankDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var ScoreRankDescPnlCtrl = /** @class */ (function (_super) {
    __extends(ScoreRankDescPnlCtrl, _super);
    function ScoreRankDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    ScoreRankDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ScoreRankDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ScoreRankDescPnlCtrl.prototype.onEnter = function (data) {
        this.contentNode_.Child('1/0').setLocaleKey('ui.score_rand_desc_1', data.curLandScore);
        this.contentNode_.Child('1/1').setLocaleKey('ui.score_historic_high_desc', data.landScore);
        this.contentNode_.Child('2/0').setLocaleKey('ui.score_rand_desc_2', data.curAlliScore);
        this.contentNode_.Child('2/1').setLocaleKey('ui.score_historic_high_desc', data.alliScore);
        this.contentNode_.Child('3').setLocaleKey('ui.score_rand_desc_3', data.extraScore);
    };
    ScoreRankDescPnlCtrl.prototype.onRemove = function () {
    };
    ScoreRankDescPnlCtrl.prototype.onClean = function () {
    };
    ScoreRankDescPnlCtrl = __decorate([
        ccclass
    ], ScoreRankDescPnlCtrl);
    return ScoreRankDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ScoreRankDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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