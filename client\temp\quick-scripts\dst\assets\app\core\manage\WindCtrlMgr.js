
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/WindCtrlMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eb804ZmyxpIU6KaNRbfkeC3', 'WindCtrlMgr');
// app/core/manage/WindCtrlMgr.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseWindCtrl_1 = require("../base/BaseWindCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var ResLoader_1 = require("../utils/ResLoader");
var WindCtrlMgr = /** @class */ (function () {
    function WindCtrlMgr() {
        this.node = null;
        this.currWind = null;
        this.nextWind = null;
        this.caches = new Map();
        this.isLockGoto = false;
    }
    // 清理缓存中的wind
    WindCtrlMgr.prototype.cleanCacheWind = function () {
        this.caches.forEach(function (node, key) {
            var wind = node.getComponent(ut.initialUpperCase(key) + "WindCtrl");
            wind.__clean();
            node.destroy();
            // 释放临时资源
            assetsMgr.releaseTempResByTag(key);
            // 最后释放场景
            ResLoader_1.loader.releaseRes("view/" + key + "/" + ut.initialUpperCase(key) + "Wind", cc.Prefab);
        });
        this.caches.clear();
    };
    // 加载wind
    WindCtrlMgr.prototype.load = function (key, progress) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadRes("view/" + key + "/" + ut.initialUpperCase(key) + "Wind", cc.Prefab, progress)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb) {
                            return [2 /*return*/, logger.error('load scene error!')];
                        }
                        return [2 /*return*/, this.ready(cc.instantiate(pfb), key)];
                }
            });
        });
    };
    WindCtrlMgr.prototype.putWind = function (wind) {
        // 释放音效
        audioMgr.releaseByMod(wind.key);
        // 释放所有pnl
        eventCenter.emit(CoreEventType_1.default.CLOSE_ALL_PNL);
        eventCenter.emit('HIDE_MESSAGE_BOX');
        // 先调用离开
        wind.__leave();
        // 释放场景
        if (wind.isClean) {
            wind.__clean();
            wind.node.destroy();
            // 释放临时资源
            assetsMgr.releaseTempResByTag(wind.key);
            // 最后释放场景
            ResLoader_1.loader.releaseRes("view/" + wind.key + "/" + ut.initialUpperCase(wind.key) + "Wind", cc.Prefab);
        }
        else {
            wind.node.parent = null;
            this.caches.set(wind.key, wind.node);
        }
    };
    // 准备场景
    WindCtrlMgr.prototype.ready = function (it, key) {
        return __awaiter(this, void 0, void 0, function () {
            var className, wind;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        className = ut.initialUpperCase(key) + "WindCtrl";
                        if (!cc.js.getClassByName(className)) {
                            return [2 /*return*/, logger.error('load wind error! not found class ' + className)];
                        }
                        wind = it.getComponent(className) || it.addComponent(className);
                        if (!wind || !(wind instanceof BaseWindCtrl_1.default)) {
                            return [2 /*return*/, logger.error('load wind error! not found class ' + className)];
                        }
                        it.parent = this.node;
                        this.nextWind = wind;
                        this.nextWind.key = key;
                        this.nextWind.node.zIndex = 0;
                        // 这里检查一下 是否还没有加载属性
                        if (!wind._isLoadProperty) {
                            logger.error('load wind error! not load property. at=' + className);
                            wind.loadProperty();
                        }
                        wind.setActive(false);
                        if (!!this.caches.delete(key)) return [3 /*break*/, 2];
                        return [4 /*yield*/, wind.__create()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: 
                    // 进入前的准备 每次进入场景都会调用
                    return [4 /*yield*/, wind.__ready()];
                    case 3:
                        // 进入前的准备 每次进入场景都会调用
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 显示场景
    WindCtrlMgr.prototype.show = function () {
        var _a;
        var _b;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        if (!this.nextWind) {
            return;
        }
        // 关闭当前场景
        this.currWind && this.putWind(this.currWind);
        // gc
        ut.waitNextFrame(2).then(function () {
            if (typeof wx !== 'undefined') {
                wx.triggerGC();
            }
            else {
                cc.sys.garbageCollect();
            }
        });
        // 进入下个场景
        var prevKey = ((_b = this.currWind) === null || _b === void 0 ? void 0 : _b.key) || '';
        this.currWind = this.nextWind;
        this.nextWind = null;
        this.currWind.setActive(true);
        this.currWind.node.zIndex = 10;
        (_a = this.currWind).__enter.apply(_a, __spread(params));
        // 发送进入场景事件
        eventCenter.emit(CoreEventType_1.default.WIND_ENTER, this.currWind, prevKey);
    };
    // 预加载场景
    WindCtrlMgr.prototype.preLoad = function (key, progress, complete) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var _done, _total, it;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (((_a = this.currWind) === null || _a === void 0 ? void 0 : _a.key) === key || ((_b = this.nextWind) === null || _b === void 0 ? void 0 : _b.key) === key) {
                            progress && progress(1);
                            complete && complete();
                            return [2 /*return*/]; // 如果已经有了直接返回
                        }
                        _done = 0, _total = 1;
                        it = this.caches.get(key) //是否有缓存
                        ;
                        if (!it) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.ready(it, key)];
                    case 1:
                        _c.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, this.load(key, function (done, total) {
                            _done = done;
                            _total = total + 1;
                            progress && progress(_done / _total);
                        })];
                    case 3:
                        _c.sent();
                        _c.label = 4;
                    case 4:
                        _done = _total;
                        progress && progress(1);
                        complete && complete();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载一个场景
    WindCtrlMgr.prototype.goto = function (key) {
        var _a, _b;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            var it;
            var _c;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (!key || this.isLockGoto) {
                            return [2 /*return*/];
                        }
                        else if (((_a = this.currWind) === null || _a === void 0 ? void 0 : _a.key) === key) { //是否已经打开了
                            return [2 /*return*/, (_c = this.currWind).__enter.apply(_c, __spread(params))];
                        }
                        else if (((_b = this.nextWind) === null || _b === void 0 ? void 0 : _b.key) === key) { //是否已经预加载了
                            return [2 /*return*/, this.show.apply(this, __spread(params))];
                        }
                        this.isLockGoto = true;
                        mc.lockTouch('__goto_wind__');
                        it = this.caches.get(key) //是否有缓存
                        ;
                        if (!it) return [3 /*break*/, 2];
                        eventCenter.emit(CoreEventType_1.default.READY_BEGIN_WIND);
                        return [4 /*yield*/, this.ready(it, key)];
                    case 1:
                        _d.sent();
                        eventCenter.emit(CoreEventType_1.default.READY_END_WIND);
                        return [3 /*break*/, 4];
                    case 2:
                        eventCenter.emit(CoreEventType_1.default.LOAD_BEGIN_WIND, key);
                        return [4 /*yield*/, this.load(key)];
                    case 3:
                        _d.sent();
                        eventCenter.emit(CoreEventType_1.default.LOAD_END_WIND, key);
                        _d.label = 4;
                    case 4:
                        mc.unlockTouch('__goto_wind__');
                        this.isLockGoto = false;
                        // 显示出来
                        this.show.apply(this, __spread(params));
                        return [2 /*return*/];
                }
            });
        });
    };
    return WindCtrlMgr;
}());
exports.default = WindCtrlMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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