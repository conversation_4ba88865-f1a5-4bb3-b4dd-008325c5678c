
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/utils/ResLoader.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '392e68aQY5AqK2/K8Ie7TET', 'ResLoader');
// app/core/utils/ResLoader.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loader = void 0;
/**
* 资源加载器 2.4.x
*/
var ResLoader = /** @class */ (function () {
    function ResLoader() {
        this.debug = true; //是否打印日志
        this.error = true; //是否打印错误日志
        // 远程资源列表
        this.remoteAssetMap = new Map();
        // 加载时间
        this.loadTimeMap = new Map();
        this.__load_id = 0; // 加载ID
        this.__load_urls = {}; //临时记录当前加载的资源
        this.__temp_doesn_res = {};
    }
    ResLoader.prototype.getLoadId = function () {
        return ++this.__load_id;
    };
    ResLoader.prototype.getLoadUrlCount = function (url) {
        var _a;
        return (_a = this.__load_urls[url]) !== null && _a !== void 0 ? _a : 0;
    };
    // 改变加载次数
    ResLoader.prototype.changeLoadUrlCount = function (url, val) {
        var count = this.__load_urls[url], cnt = (count !== null && count !== void 0 ? count : 0) + val;
        if (cnt > 0) {
            this.__load_urls[url] = cnt;
        }
        else if (count !== undefined) {
            delete this.__load_urls[url];
        }
        return cnt;
    };
    // 放弃加载
    ResLoader.prototype.giveupLoad = function (url) {
        delete this.__load_urls[url];
    };
    ResLoader.prototype.printError = function (msg) {
        var subst = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            subst[_i - 1] = arguments[_i];
        }
        if (this.debug && this.error) {
            logger.error.apply(logger, __spread([msg], subst));
        }
    };
    ResLoader.prototype.printInfo = function (msg) {
        var subst = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            subst[_i - 1] = arguments[_i];
        }
        if (this.debug) {
            logger.info.apply(logger, __spread([msg], subst));
        }
    };
    // 解析参数
    ResLoader.prototype.makeLoadResArgs = function (params) {
        var len = params.length;
        if (len < 1 || typeof (params[0]) !== 'string') {
            this.printError('makeLoadResArgs error', params);
            return { url: '' };
        }
        var args = { url: params[0] };
        for (var i = 1; i < len; i++) {
            var param = params[i];
            if (i === 1 && cc.js.isChildClassOf(param, cc.Asset)) {
                args.type = param;
            }
            else if (typeof (param) === 'function') {
                args.onProgess = param;
            }
        }
        return args;
    };
    // public async loadRes(url: string): Promise<cc.Asset>;
    // public async loadRes(url: string, onProgess: Function): Promise<cc.Asset>;
    // public async loadRes(url: string, type: typeof cc.Asset): Promise<cc.Asset>;
    // public async loadRes(url: string, type: typeof cc.Asset, onProgess: Function): Promise<cc.Asset>;
    ResLoader.prototype.loadRes = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        return __awaiter(this, void 0, Promise, function () {
            var id, asset;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        id = this.getLoadId();
                        this.debug && this.loadTimeMap.set(id, Date.now());
                        return [4 /*yield*/, this.load.apply(this, __spread(params))];
                    case 1:
                        asset = _a.sent();
                        return [2 /*return*/, this.loadResComplete(id, params[0], asset)];
                }
            });
        });
    };
    ResLoader.prototype.load = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            var _a, url, type, onProgess;
            var _this = this;
            return __generator(this, function (_b) {
                _a = this.makeLoadResArgs(params), url = _a.url, type = _a.type, onProgess = _a.onProgess;
                return [2 /*return*/, new Promise(function (resolve) {
                        var res = cc.resources.get(url, type);
                        if (!res) {
                            return _this.__load(url, type, onProgess, resolve);
                        }
                        else if (res.refCount > 0) {
                            return resolve(res);
                        }
                        else {
                            ut.waitNextFrame().then(function () { return _this.__load(url, type, onProgess, resolve); });
                        }
                    })];
            });
        });
    };
    ResLoader.prototype.__load = function (url, type, onProgess, onComplete) {
        var _this = this;
        if (this.__temp_doesn_res[url]) {
            return onComplete(null);
        }
        this.changeLoadUrlCount(url, 1);
        cc.resources.load(url, type, onProgess, function (err, asset) {
            if (!err) {
                _this.changeLoadUrlCount(url, -1);
                onComplete(asset);
            }
            else if (err.message.includes("Bundle resources doesn't contain")) {
                _this.printError('loadRes error -> ' + url, err.message);
                _this.changeLoadUrlCount(url, -1);
                _this.__temp_doesn_res[url] = true; //记录没有的路径
                onComplete(null);
            }
            else if (_this.getLoadUrlCount(url) > 0) {
                _this.printInfo('loadRes error! try reload. ' + url, err.message);
                ut.wait(0.1).then(function () {
                    if (_this.getLoadUrlCount(url) > 0) {
                        _this.__load(url, type, onProgess, onComplete); //尝试重新加载
                    }
                    else {
                        onComplete(null);
                    }
                });
            }
            else {
                onComplete(null);
            }
        });
    };
    // public async loadResDir(url: string): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, onProgess: Function): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, type: typeof cc.Asset): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, type: typeof cc.Asset, onProgess: Function): Promise<cc.Asset[]>;
    ResLoader.prototype.loadResDir = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        return __awaiter(this, void 0, Promise, function () {
            var id, assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        id = this.getLoadId();
                        this.debug && this.loadTimeMap.set(id, Date.now());
                        return [4 /*yield*/, this.loadDir.apply(this, __spread(params))];
                    case 1:
                        assets = _a.sent();
                        return [2 /*return*/, assets.map(function (m) { return _this.loadResComplete(id, params[0] + '/' + m.name, m); })];
                }
            });
        });
    };
    ResLoader.prototype.loadDir = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            var _a, url, type, onProgess;
            var _this = this;
            return __generator(this, function (_b) {
                _a = this.makeLoadResArgs(params), url = _a.url, type = _a.type, onProgess = _a.onProgess;
                return [2 /*return*/, new Promise(function (resolve) { return _this.__loadDir(url, type, onProgess, resolve); })];
            });
        });
    };
    ResLoader.prototype.__loadDir = function (url, type, onProgess, onComplete) {
        var _this = this;
        cc.resources.loadDir(url, type, onProgess, function (err, assets) {
            if (err) {
                if (err.message.includes("Bundle resources doesn't contain")) {
                    _this.printError('loadRes error -> ' + url, err.message);
                    onComplete([]);
                }
                else {
                    _this.printInfo('loadRes error! try reload. ' + url, err.message);
                    ut.wait(0.1).then(function () { return _this.__loadDir(url, type, onProgess, onComplete); }); //尝试重新加载
                }
            }
            else {
                onComplete(assets);
            }
        });
    };
    // 加载JPG
    ResLoader.prototype.loadRemote = function (url, ext) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var res = _this.remoteAssetMap.get(url);
                        if (res) {
                            return resolve(res);
                        }
                        _this.__loadRemote(url, ext, resolve);
                    })];
            });
        });
    };
    ResLoader.prototype.__loadRemote = function (url, ext, onComplete) {
        var _this = this;
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, asset) {
            if (err) {
                _this.printError('loadRes error -> ' + url, err.message);
                onComplete(null);
            }
            else {
                _this.remoteAssetMap.set(url, asset);
                onComplete(asset);
            }
            // 底层默认有重新加载 这里就不重新加载了
            // if (!err) {
            //     this.remoteAssetMap.set(url, asset)
            //     onComplete(asset)
            // } else if (!err.message.includes('download failed')) {
            //     this.printError('loadRes error -> ' + url, err.message)
            //     onComplete(null)
            // } else {
            //     this.printInfo('loadRes error! try reload. ' + url, err.message)
            //     ut.wait(0.1).then(() => this.__loadRemote(url, ext, onComplete)) //尝试重新加载
            // }
        });
    };
    // 加载资源完成
    ResLoader.prototype.loadResComplete = function (id, url, asset) {
        if (asset) {
            // 添加引用
            asset.addRef();
            // 打印
            if (this.debug) {
                var now = Date.now();
                var time = now - (this.loadTimeMap.get(id) || now);
                this.loadTimeMap.delete(id);
                this.printInfo("loadRes -> " + url + " [" + asset.refCount + "] " + time + "ms");
            }
        }
        return asset;
    };
    // 释放资源
    ResLoader.prototype.releaseRes = function (url, type) {
        var asset = cc.resources.get(url, type), isRemote = false;
        if (!asset) {
            asset = this.remoteAssetMap.get(url);
            isRemote = true;
        }
        if (!asset) {
            this.printError("releaseRes asset is null " + url);
            return 0;
        }
        // 减少引用并尝试自动释放
        // @ts-ignore
        asset.decRef(false);
        var refCount = asset.refCount;
        if (refCount <= 0) {
            cc.assetManager.releaseAsset(asset);
            // 如果是远程资源 还要删除缓存
            if (isRemote) {
                this.remoteAssetMap.delete(url);
            }
        }
        // 打印
        this.printInfo("releaseRes -> " + url + " [" + refCount + "]");
        return refCount;
    };
    // 释放所有引用
    ResLoader.prototype.releaseAsset = function (url, type) {
        var asset = cc.resources.get(url, type), isRemote = false;
        if (!asset) {
            asset = this.remoteAssetMap.get(url);
            isRemote = true;
        }
        if (!asset) {
            this.printError("releaseAsset asset is null " + url);
            return;
        }
        // 直接强行释放
        cc.assetManager.releaseAsset(asset);
        // 如果是远程资源 还要删除缓存
        if (isRemote) {
            this.remoteAssetMap.delete(url);
        }
        // 打印
        this.printInfo("releaseAsset -> " + url + " [0]");
    };
    ResLoader.prototype.clean = function () {
        this.remoteAssetMap.clear();
        this.loadTimeMap.clear();
        this.__load_id = 0;
        this.__load_urls = {};
        this.__temp_doesn_res = {};
    };
    return ResLoader;
}());
exports.loader = new ResLoader();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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