
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/RankShopPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '67bf4Baz7tMqar+x0Apxrwa', 'RankShopPnlCtrl');
// app/script/view/menu/RankShopPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var RankShopPnlCtrl = /** @class */ (function (_super) {
    __extends(RankShopPnlCtrl, _super);
    function RankShopPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rankCoinNode_ = null; // path://top/rank_coin_n
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.winPointLbl_ = null; // path://root/pages_n/0/bottom/win_point_l
        _this.mcitySkinNode_ = null; // path://root/pages_n/1/view/content/mcity_skin_n
        _this.pawnSkinNode_ = null; // path://root/pages_n/1/view/content/pawn_skin_n
        _this.headiconNode_ = null; // path://root/pages_n/1/view/content/headicon_n
        _this.chatEmojiNode_ = null; // path://root/pages_n/1/view/content/chat_emoji_n
        _this.warTokenNode_ = null; // path://root/pages_n/1/view/content/war_token_n
        _this.shopTabsTc_ = null; // path://root/pages_n/1/shop_tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'RANK_SHOP_TAB';
        _this.PKEY_SHOP_TAB = 'RANK_SHOP_SHOP_TAB';
        _this.ITEM_ADAPT_SIZE = cc.size(64, 64);
        _this.user = null;
        _this.shopSv = null;
        _this.currShopTab = 0;
        return _this;
    }
    RankShopPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    RankShopPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var throttleUpdate;
            var _this = this;
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.shopSv = this.pagesNode_.Child('1', cc.ScrollView);
                throttleUpdate = this.throttle(function (event) { return _this.onScrolling(event); }, 200);
                this.shopSv.node.on('scrolling', throttleUpdate, this);
                this.rankCoinNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    RankShopPnlCtrl.prototype.onPlayActionComplete = function () {
        this.rankCoinNode_.active = true;
        this.emit(EventType_1.default.HIDE_TOP_NODE, false);
    };
    RankShopPnlCtrl.prototype.onEnter = function () {
        var _a;
        this.updateCurrRankCoin();
        this.tabsTc_.Tabs((_a = this.user.getTempPreferenceMap(this.PKEY_TAB)) !== null && _a !== void 0 ? _a : 0);
    };
    RankShopPnlCtrl.prototype.onRemove = function () {
        this.rankCoinNode_.active = false;
        this.emit(EventType_1.default.HIDE_TOP_NODE, true);
    };
    RankShopPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    RankShopPnlCtrl.prototype.onClickTabs = function (event, data) {
        var _a;
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            this.showSeasonReward(node);
            var _b = GameHelper_1.gameHpr.resolutionRankScore(this.user.getRankScore(), this.user.getAccTotalRankCount()), id = _b.id, winPoint = _b.winPoint;
            var params = id >= 0 ? assetsMgr.lang('ui.game_over_rank_text', ['ui.rank_name_' + id, winPoint]) : 'ui.rank_name_none';
            this.winPointLbl_.setLocaleKey('ui.curr_ranking', params);
        }
        else if (type === 1) {
            this.showShop(node);
            this.currShopTab = (_a = this.user.getTempPreferenceMap(this.PKEY_SHOP_TAB)) !== null && _a !== void 0 ? _a : 0;
            this.shopTabsTc_.Tabs(this.currShopTab);
        }
    };
    // path://root/pages_n/1/shop_tabs_tc_tce
    RankShopPnlCtrl.prototype.onClickShopTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.currShopTab = type;
        this.user.setTempPreferenceData(this.PKEY_SHOP_TAB, type);
        var y = 0;
        if (type === 0) { // 主城位置 0
            y = this.mcitySkinNode_.y;
        }
        else if (type === 1) { // 士兵位置 1
            y = this.pawnSkinNode_.y;
        }
        else if (type === 2) { // 头像位置 2
            y = this.headiconNode_.y;
        }
        else if (type === 3) { // 表情位置 3
            y = this.chatEmojiNode_.y;
        }
        this.shopSv.stopAutoScroll();
        this.shopSv.content.y = Math.abs(y);
    };
    // path://root/pages_n/1/view/content/mcity_skin_n/list/buy_be@city
    RankShopPnlCtrl.prototype.onClickBuy = function (event, param) {
        var _this = this;
        var data = event.target.Data;
        if (!data) {
            return;
        }
        var rankCoin = GameHelper_1.gameHpr.user.getRankCoin();
        if (param === 'city') { // 城市皮肤
            ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'city_skin', list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (rankCoin < data.rank_coin) {
                    return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RANK_COIN_NOT_ENOUGH);
                }
                _this.exchangeCitySkin(ret.id);
            });
        }
        else if (param === 'pawn') { // 士兵皮肤
            ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (rankCoin < data.rank_coin) {
                    return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RANK_COIN_NOT_ENOUGH);
                }
                _this.exchangePawnSkin(ret.id);
            });
        }
        else if (param === 'head') { // 头像
            ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'headicon', list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (rankCoin < data.rank_coin) {
                    return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RANK_COIN_NOT_ENOUGH);
                }
                _this.exchangeHeadicon(ret.id);
            });
        }
        else if (param === 'emoji') { // 表情
            ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'chat_emoji', list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (rankCoin < data.rank_coin) {
                    return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RANK_COIN_NOT_ENOUGH);
                }
                _this.exchangeChatEmoji(ret.id);
            });
        }
    };
    // path://root/pages_n/1/view/content/war_token_n/list/war_token_be
    RankShopPnlCtrl.prototype.onClickWarToken = function (event, data) {
        cc.log('onClickWarToken', data);
    };
    // path://top/rank_coin_n/rank_shop_desc_be
    RankShopPnlCtrl.prototype.onClickRankShopDesc = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/DescList', [
            { text: 'ui.rank_shop_desc_01' },
            { text: 'ui.rank_shop_desc_02' },
            { text: 'ui.rank_shop_desc_03' },
        ]);
    };
    // path://root/pages_n/0/view/content/item/buttons/claim_be
    RankShopPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        var it = event.target.parent.parent;
        var data = it.Data;
        if (!data) {
            return;
        }
        this.user.claimRankSeasonReward(data.id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
                data.isClaim = true;
                var _a = GameHelper_1.gameHpr.resolutionRankScore(_this.user.getRankScore(), _this.user.getAccTotalRankCount()), id = _a.id, winPoint = _a.winPoint;
                _this.updateSeasonRewardButton(it, data, id);
                _this.updateCurrRankCoin();
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 赛季奖励
    RankShopPnlCtrl.prototype.showSeasonReward = function (node) {
        var _this = this;
        var rankScore = this.user.getRankScore();
        var _a = GameHelper_1.gameHpr.resolutionRankScore(rankScore, this.user.getAccTotalRankCount()), id = _a.id, winPoint = _a.winPoint;
        var list = [], seasonNo = 1, claimMap = {};
        this.user.getRankRewardRecords().forEach(function (m) { return claimMap[m] = true; });
        var lastScore = 0;
        var datas = assetsMgr.getJson('seasonReward').datas;
        for (var i = 0, l = datas.length; i < l; i++) {
            var m = datas[i];
            var str = m['s' + seasonNo];
            if (str) {
                list.push({ id: m.id, score: m.rank_score - lastScore, nextScore: i + 1 < l ? datas[i + 1].rank_score - m.rank_score : 0, rewards: GameHelper_1.gameHpr.stringToCTypes(str), isClaim: claimMap[m.id] });
                lastScore = m.rank_score;
            }
        }
        var skinTypes = [Enums_1.CType.PAWN_SKIN, Enums_1.CType.CITY_SKIN], socialTypes = [Enums_1.CType.HEAD_ICON, Enums_1.CType.CHAT_EMOJI];
        var sv = node.Component(cc.ScrollView), len = list.length - 1;
        sv.stopAutoScroll();
        sv.Items(list, function (it, data, i) {
            it.Data = data;
            var icon = it.Child('rank_icon');
            icon.opacity = (data.id <= id /*  || (data.nextScore && winPoint >= data.nextScore) */) ? 255 : 150;
            ResHelper_1.resHelper.loadRankScoreIcon(data.id, icon, _this.key);
            it.Child('rank_name').setLocaleKey('ui.rank_name_' + data.id);
            it.Child('items').Items(data.rewards, function (n, item) {
                n.Child('root/done').active = data.isClaim;
                if (item.type === Enums_1.CType.HEAD_ICON) {
                    n.Child('root/icon').setContentSize(_this.ITEM_ADAPT_SIZE);
                }
                ViewHelper_1.viewHelper.updateItemByCTypeOne(n.Child('root'), item, _this.key, _this.ITEM_ADAPT_SIZE, true);
                n.off('click');
                if (n.Component(cc.Button).interactable = (skinTypes.indexOf(item.type) > -1 || socialTypes.indexOf(item.type) > -1)) {
                    var key_1 = '', type_1 = '', arr_1 = [];
                    if (item.type === Enums_1.CType.PAWN_SKIN) {
                        key_1 = 'menu/CollectionSkinInfo';
                        type_1 = 'pawn_skin';
                        arr_1 = [assetsMgr.getJsonData('pawnSkin', item.id)];
                    }
                    else if (item.type === Enums_1.CType.CITY_SKIN) {
                        key_1 = 'menu/CollectionSkinInfo';
                        type_1 = 'city_skin';
                        arr_1 = [assetsMgr.getJsonData('citySkin', item.id)];
                    }
                    else if (item.type === Enums_1.CType.HEAD_ICON) {
                        key_1 = 'menu/CollectionEmojiInfo';
                        type_1 = 'headicon';
                        arr_1 = [assetsMgr.getJsonData('headIcon', item.id)];
                    }
                    else if (item.type === Enums_1.CType.CHAT_EMOJI) {
                        key_1 = 'menu/CollectionEmojiInfo';
                        type_1 = 'chat_emoji';
                        arr_1 = [assetsMgr.getJsonData('chatEmoji', item.id)];
                    }
                    n.on('click', function () { return ViewHelper_1.viewHelper.showPnl(key_1, { type: type_1, list: arr_1 }); });
                }
            });
            it.Child('progress/bg', cc.MultiFrame).setFrame(data.id <= id ? 1 : 0);
            var bar = it.Child('progress/bar');
            bar.active = it.Child('line').active = i > 0;
            var curScore = 0;
            if (!bar.active) {
                curScore = data.id <= id ? data.score : winPoint;
            }
            else if (data.id <= id) {
                curScore = data.score;
                bar.Child('val', cc.Sprite).fillRange = 1;
            }
            else if (data.id === id + 1) {
                curScore = winPoint;
                bar.Child('val', cc.Sprite).fillRange = winPoint / (data.score || 1);
            }
            else {
                bar.Child('val', cc.Sprite).fillRange = 0;
            }
            var text = curScore >= data.score ? (data.score + '') : (curScore + '/' + data.score);
            it.Child('progress/val', cc.Label).Color(curScore >= data.score ? '#FADFA7' : '#FFFFFF').string = text;
            // 按钮
            _this.updateSeasonRewardButton(it, data, id);
        });
    };
    RankShopPnlCtrl.prototype.updateSeasonRewardButton = function (it, data, id) {
        if (data.isClaim) {
            it.Child('buttons').Swih('done');
        }
        else if (data.id <= id) {
            it.Child('buttons').Swih('claim_be');
        }
        else {
            it.Child('buttons').Swih('lock');
        }
    };
    // 商城
    RankShopPnlCtrl.prototype.showShop = function (node) {
        // 主城皮肤
        this.updateCitySkinList();
        // 士兵皮肤
        this.updatePawnSkinList();
        // 表情
        this.updateChatEmojiList();
        // 头像
        this.updateHeadiconList();
        // 兵符
        this.updateWarTokenList();
    };
    RankShopPnlCtrl.prototype.updateCurrRankCoin = function () {
        this.rankCoinNode_.Child('val', cc.Label).string = '' + this.user.getRankCoin();
    };
    RankShopPnlCtrl.prototype.updateCitySkinList = function () {
        var _this = this;
        var list = assetsMgr.getJson('citySkin').get('cond', 3);
        var show = this.mcitySkinNode_.active = list.length > 0;
        if (!show) {
            return;
        }
        var ownMap = {};
        this.user.getUnlockCitySkinIds().forEach(function (m) { return ownMap[m] = 1; });
        list.sort(function (a, b) {
            var aw = ownMap[a.id] || 0, bw = ownMap[b.id] || 0;
            return aw - bw;
        });
        this.mcitySkinNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadCityIcon(json.id, it.Child('icon/val'), _this.key);
            var isOwn = it.Child('own').active = !!ownMap[json.id];
            if (it.Child('coin').active = !isOwn) {
                it.Child('coin/val', cc.Label).string = '' + json.rank_coin;
            }
        });
    };
    RankShopPnlCtrl.prototype.exchangeCitySkin = function (id) {
        var _this = this;
        this.user.rsExchangeCitySkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                _this.updateCitySkinList();
                _this.updateCurrRankCoin();
            }
        });
    };
    RankShopPnlCtrl.prototype.updatePawnSkinList = function () {
        var _this = this;
        var list = assetsMgr.getJson('pawnSkin').get('cond', 3);
        var show = this.pawnSkinNode_.active = list.length > 0;
        if (!show) {
            return;
        }
        var ownMap = {};
        this.user.getUnlockPawnSkinIds().forEach(function (m) { return ownMap[m] = 1; });
        list.sort(function (a, b) {
            var aw = ownMap[a.id] || 0, bw = ownMap[b.id] || 0;
            return aw - bw;
        });
        this.pawnSkinNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadPawnHeadIcon(json.id, it.Child('icon/val'), _this.key);
            it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.pawn_id), 5));
            var isOwn = it.Child('own').active = !!ownMap[json.id];
            if (it.Child('coin').active = !isOwn) {
                it.Child('coin/val', cc.Label).string = '' + json.rank_coin;
            }
        });
    };
    RankShopPnlCtrl.prototype.exchangePawnSkin = function (id) {
        var _this = this;
        this.user.rsExchangePawnSkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                _this.updatePawnSkinList();
                _this.updateCurrRankCoin();
            }
        });
    };
    RankShopPnlCtrl.prototype.updateChatEmojiList = function () {
        var _this = this;
        var list = assetsMgr.getJson('chatEmoji').get('cond', 3);
        var show = this.chatEmojiNode_.active = list.length > 0;
        if (!show) {
            return;
        }
        var ownMap = {};
        this.user.getUnlockChatEmojiIds().forEach(function (m) { return ownMap[m] = 1; });
        list.sort(function (a, b) {
            var aw = ownMap[a.id] || 0, bw = ownMap[b.id] || 0;
            return aw - bw;
        });
        this.chatEmojiNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadEmojiIcon(json.id, it.Child('icon/val'), _this.key);
            var isOwn = it.Child('own').active = !!ownMap[json.id];
            if (it.Child('coin').active = !isOwn) {
                it.Child('coin/val', cc.Label).string = '' + json.rank_coin;
            }
        });
    };
    RankShopPnlCtrl.prototype.exchangeChatEmoji = function (id) {
        var _this = this;
        this.user.rsExchangeChatEmoji(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                _this.updateChatEmojiList();
                _this.updateCurrRankCoin();
            }
        });
    };
    RankShopPnlCtrl.prototype.updateHeadiconList = function () {
        var _this = this;
        var list = assetsMgr.getJson('headIcon').get('cond', 3);
        var show = this.headiconNode_.active = list.length > 0;
        if (!show) {
            return;
        }
        var ownMap = {};
        this.user.getUnlockHeadIcons().forEach(function (m) { return ownMap[m] = 1; });
        list.sort(function (a, b) {
            var aw = ownMap[a.icon] || 0, bw = ownMap[b.icon] || 0;
            return aw - bw;
        });
        this.headiconNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadPlayerHead(it.Child('icon/val'), json.icon, _this.key, true);
            var isOwn = it.Child('own').active = !!ownMap[json.icon];
            if (it.Child('coin').active = !isOwn) {
                it.Child('coin/val', cc.Label).string = '' + json.rank_coin;
            }
        });
    };
    RankShopPnlCtrl.prototype.exchangeHeadicon = function (id) {
        var _this = this;
        this.user.rsExchangeHeadicon(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                _this.updateHeadiconList();
                _this.updateCurrRankCoin();
            }
        });
    };
    RankShopPnlCtrl.prototype.updateWarTokenList = function () {
        this.warTokenNode_.active = false;
        // this.warTokenNode_.Child('title/count').setLocaleKey('')
        // this.warTokenNode_.Child('list').Items(RANK_SHOP_WAR_TOKEN_CONFIG, (it, data, i) => {
        // 	it.Data = data
        // 	resHelper.loadIcon('icon/rank_shop_wt_' + (i + 1), it.Child('icon/val'), this.key)
        // 	it.Child('name').setLocaleKey('ui.war_token_name', data.warToken)
        // 	it.Child('coin/val', cc.Label).string = '' + data.coin
        // })
    };
    RankShopPnlCtrl.prototype.onScrolling = function (event) {
        var content = event.content, showPawnSkin = content.y >= Math.abs(this.pawnSkinNode_.y) && content.y < Math.abs(this.headiconNode_.y), showHeadicon = content.y >= Math.abs(this.headiconNode_.y) && content.y < Math.abs(this.chatEmojiNode_.y), showChatEmoji = content.y >= Math.abs(this.chatEmojiNode_.y);
        var tab = showPawnSkin ? 1 : showHeadicon ? 2 : showChatEmoji ? 3 : 0;
        if (this.currShopTab !== tab) {
            this.shopTabsTc_.Swih(tab);
            this.user.setTempPreferenceData(this.PKEY_SHOP_TAB, tab);
        }
    };
    RankShopPnlCtrl.prototype.throttle = function (func, delay) {
        var timer = null;
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (!timer) {
                func.apply(this, args);
                timer = setTimeout(function () {
                    timer = null;
                }, delay);
            }
        };
    };
    RankShopPnlCtrl = __decorate([
        ccclass
    ], RankShopPnlCtrl);
    return RankShopPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = RankShopPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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