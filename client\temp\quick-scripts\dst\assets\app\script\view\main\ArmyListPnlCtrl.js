
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/ArmyListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9c98fj2wCNEv4PGkOm+oFHe', 'ArmyListPnlCtrl');
// app/script/view/main/ArmyListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var ArmyListPnlCtrl = /** @class */ (function (_super) {
    __extends(ArmyListPnlCtrl, _super);
    function ArmyListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.player = null;
        return _this;
    }
    ArmyListPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ARMY_TREASURE] = this.onUpdateArmyTreasure, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ARMY_AREA_INDEX] = this.onUpdateArmyAreaIndex, _b.enter = true, _b),
        ];
    };
    ArmyListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    ArmyListPnlCtrl.prototype.onEnter = function () {
        this.tabsTc_.Tabs(0);
    };
    ArmyListPnlCtrl.prototype.onRemove = function () {
    };
    ArmyListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/pages_n/0/list/view/content/item/pos_be
    ArmyListPnlCtrl.prototype.onClickPos = function (event, _) {
        var _a, _b;
        var data = event.target.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos((_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
    };
    // path://root/tabs_tc_tce
    ArmyListPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        if (type === '0') {
            this.showAllArmy(node);
        }
        else if (type === '1') {
            this.showArmyMarchRecord(node);
        }
        else if (type === '2') {
            this.showArmyBattleRecord(node);
        }
    };
    // path://root/pages_n/2/view/content/item/5/playback_be
    ArmyListPnlCtrl.prototype.onClickPlayback = function (event, _) {
        var data = event.target.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid;
        if (uid) {
            this.playbackBattle(uid);
        }
    };
    // path://root/pages_n/0/list/view/content/item/treasure_be
    ArmyListPnlCtrl.prototype.onClickTreasure = function (event, _) {
        var _a;
        var data = event.target.parent.Data;
        if (!data) {
            // } else if (gameHpr.isBattleingByIndex(data.index)) {
            //     return viewHelper.showAlert(ecode.BATTLEING)
        }
        else if (((_a = data.treasures) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            ViewHelper_1.viewHelper.showPnl('common/TreasureList', data.treasures);
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/send_to_chat_be
    ArmyListPnlCtrl.prototype.onClickSendToChat = function (event, _data) {
        var _this = this;
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, index = data.index;
        if (uid) {
            // mapHelper.indexToPoint(data.armyIndex).Join()
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_battle_record_to_chat_tip' }, function (type, childType, select) {
                if (GameHelper_1.gameHpr.chat.sendChat(type, childType, '', { select: select, battleInfo: { uid: uid, index: index } }) === 0) {
                    var target = GameHelper_1.gameHpr.chat.getTargetChat(type, childType, select);
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, target: target }).then(function () { return _this.isValid && _this.hide(); });
                }
            });
        }
    };
    // path://root/pages_n/2/view/content/item/5/buttons/battle_statistics_be
    ArmyListPnlCtrl.prototype.onClickBattleStatistics = function (event, _) {
        var data = event.target.parent.parent.Data, uid = data === null || data === void 0 ? void 0 : data.uid, uids = (data === null || data === void 0 ? void 0 : data.armyUidList) || [];
        if (uid && uids.length > 0) {
            this.showBattleStatistics(uid, uids);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新士兵宝箱
    ArmyListPnlCtrl.prototype.onUpdateArmyTreasure = function (auid) {
        var _a;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var army = this.player.getTempArmyList().find(function (m) { return m.uid === auid; });
            var treasures = it.Data.treasures = (_a = army === null || army === void 0 ? void 0 : army.treasures) !== null && _a !== void 0 ? _a : it.Data.treasures;
            this.updateArmyTreasure(it, treasures || []);
        }
    };
    // 刷新军队所在区域位置
    ArmyListPnlCtrl.prototype.onUpdateArmyAreaIndex = function (auid) {
        var _a, _b;
        var it = this.pagesNode_.Child('0/list', cc.ScrollView).content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === auid; });
        if (it === null || it === void 0 ? void 0 : it.Data) {
            var data_1 = it.Data;
            data_1.march = GameHelper_1.gameHpr.world.getMarchs().find(function (m) { return m.armyUid === data_1.uid; });
            data_1.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = data_1.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data_1.index, this.player.getMainCityIndex());
            this.updateArmyPos(it, data_1);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 显示所有军队
    ArmyListPnlCtrl.prototype.showAllArmy = function (node) {
        var _this = this;
        var sv = node.Child('list', cc.ScrollView);
        sv.content.Swih('');
        var emptyNode = sv.Child('empty'), countLbl = node.Child('title/bg/val', cc.Label);
        var armyMaxCount = this.player.getArmyMaxCount();
        emptyNode.active = false;
        countLbl.setLocaleKey('ui.own_army_count', '0/' + armyMaxCount);
        this.loadingNode_.active = true;
        this.player.getAllArmys(3, false).then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            var index = _this.player.getMainCityIndex();
            var marchs = {};
            GameHelper_1.gameHpr.world.getMarchs().forEach(function (x) { return marchs[x.armyUid] = x; });
            var lvingPawnLvMap = {};
            _this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
            list.forEach(function (m) {
                var _a, _b;
                m.march = marchs[m.uid];
                m.tonden = GameHelper_1.gameHpr.world.getArmyTondenInfo(m.index, m.uid);
                m.dis = GameHelper_1.gameHpr.getToMapCellDis((_b = (_a = m.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : m.index, index);
            });
            list.sort(function (a, b) {
                var aw = _this.getArmySortState(a, lvingPawnLvMap), bw = _this.getArmySortState(b, lvingPawnLvMap);
                return aw === bw ? a.dis - b.dis : bw - aw;
            });
            var len = list.length;
            emptyNode.active = len === 0;
            countLbl.setLocaleKey('ui.own_army_count', len + '/' + _this.player.getArmyMaxCount());
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(len, function (it, i) {
                var data = it.Data = list[i];
                it.Child('name', cc.Label).string = data.name;
                _this.updateArmyPos(it, data);
                var pawns = data.pawns, isHasLving = false;
                it.Child('pawns').Items(pawns.concat(data.drillPawns).concat(data.curingPawns), function (node2, pawn) {
                    var _a;
                    var icon = node2.Child('icon'), isId = typeof (pawn) === 'number', isCuring = !!pawn.deadTime;
                    var isLving = !isId && !!lvingPawnLvMap[pawn.uid] && !isCuring;
                    var lv = isLving ? lvingPawnLvMap[pawn.uid] : pawn.lv;
                    ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? pawn : (((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id), icon, _this.key);
                    icon.opacity = (isId || isLving || isCuring) ? 120 : 255;
                    node2.Child('lv', cc.Label).Color(isLving ? '#21DC2D' : '#FFFFFF').string = (isId || lv <= 1) ? '' : '' + lv;
                    if (node2.Child('hp').active = (!isId && !isCuring)) {
                        node2.Child('hp/bar', cc.Sprite).fillRange = pawn.hp[0] / pawn.hp[1];
                    }
                    if (isLving) {
                        isHasLving = true;
                    }
                });
                ViewHelper_1.viewHelper.updateArmyState(it, data, data.march, isHasLving, true);
                _this.updateArmyTreasure(it, data.treasures);
            });
        });
    };
    ArmyListPnlCtrl.prototype.getArmySortState = function (army, lvingPawnLvMap) {
        if (army.state !== Enums_1.ArmyState.NONE) {
            return army.state;
        }
        else if (army.drillPawns.length > 0) {
            return Enums_1.ArmyState.DRILL;
        }
        else if (army.curingPawns.length > 0) {
            return Enums_1.ArmyState.CURING;
        }
        else if (army.pawns.some(function (m) { return !!lvingPawnLvMap[m.uid]; })) {
            return Enums_1.ArmyState.LVING;
        }
        else if (army.tonden) {
            return Enums_1.ArmyState.TONDEN;
        }
        return army.state;
    };
    ArmyListPnlCtrl.prototype.updateArmyPos = function (node, data) {
        var _a, _b;
        var isMarching = !!data.march, isHasDis = data.dis > 0;
        var posNode = node.Child('pos_be'), disNode = node.Child('dis');
        if (posNode.active = isHasDis || isMarching) {
            ViewHelper_1.viewHelper.updatePositionView(posNode, (_b = (_a = data.march) === null || _a === void 0 ? void 0 : _a.targetIndex) !== null && _b !== void 0 ? _b : data.index);
        }
        if (disNode.active = !isHasDis && !posNode.active) {
            disNode.setLocaleKey('ui.in_main_city');
        }
    };
    // 刷新宝箱信息
    ArmyListPnlCtrl.prototype.updateArmyTreasure = function (it, treasures) {
        var node = it.Child('treasure_be'), treasureCount = treasures.length;
        if (node.active = treasureCount > 0) {
            node.Child('treasure', TextButtonCmpt_1.default).setKey('ui.get_treasure_count', treasureCount);
        }
    };
    // 显示军队行军记录列表
    ArmyListPnlCtrl.prototype.showArmyMarchRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyMarchRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey(GameHelper_1.gameHpr.isNoviceMode ? 'ui.army_march_record_empty_1' : 'ui.army_march_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.Items(list, function (it, data) {
                it.Data = data;
                it.Child('0/name', cc.Label).string = data.armyName;
                it.Child('0/time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                _this.updateRecordInfo(it, data);
            });
        });
    };
    // 显示军队战斗记录列表
    ArmyListPnlCtrl.prototype.showArmyBattleRecord = function (node) {
        var _this = this;
        var sv = node.Component(cc.ScrollView), emptyNode = node.Child('empty');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        emptyNode.active = false;
        this.player.getArmyBattleRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            var len = list.length;
            _this.loadingNode_.active = false;
            if (emptyNode.active = len === 0) {
                emptyNode.setLocaleKey('ui.army_battle_record_empty');
            }
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(list.length, function (it, i) {
                var data = list[i], isCanPlay = !!data.isCanPlay;
                it.Child('win/bg').Color(data.isWin ? '#EB9E4E' : '#96B2C8');
                it.Child('win/bg/val').setLocaleKey('ui.battle_result_' + Number(!!data.isWin));
                it.Child('win/time', cc.Label).string = ut.dateFormat('yyyy-MM-dd hh:mm:ss', data.beginTime);
                _this.updateRecordInfo(it.Child('0'), { type: 0, armyIndex: data.index });
                it.Child('1/val').setLocaleKey('ui.end_battle_time', ut.millisecondFormat(data.endTime - data.beginTime, 'h:mm:ss'));
                it.Child('2/val').setLocaleKey('ui.battle_army_count', data.armyUidList.length);
                it.Child('3/val').setLocaleKey('ui.alli_battle_record_0_1', (data.invalidInfo[1] || 0) + (data.validInfo[1] || 0));
                it.Child('4/val').setLocaleKey('ui.alli_battle_record_1_1', data.deadInfo.length);
                it.Child('5').Data = data;
                it.Child('5/buttons/battle_statistics_be').active = isCanPlay && !!data.armyUidList.length;
                it.Child('5/buttons/send_to_chat_be').active = isCanPlay && !GameHelper_1.gameHpr.isNoviceMode;
                it.Child('5/playback_be', cc.Button).interactable = isCanPlay;
            });
        });
    };
    ArmyListPnlCtrl.prototype.updateRecordInfo = function (it, data) {
        var descNode = it.Child('desc'), texts = Constant_1.ARMY_RECORD_DESC_CONF[data.type];
        if (descNode.active = !!texts) {
            descNode.setLocaleKey('ui.army_record_desc_' + data.type, texts.map(function (m) {
                if (m === 'index') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.armyIndex), MapHelper_1.mapHelper.indexToPoint(data.armyIndex).Join()) + "</c>";
                }
                else if (m === 'target') {
                    return " <color=#564C49>" + assetsMgr.lang('ui.position', GameHelper_1.gameHpr.getCellBaseNameByIndex(data.targetIndex), MapHelper_1.mapHelper.indexToPoint(data.targetIndex).Join()) + "</c>";
                }
                return '';
            }));
        }
    };
    // 显示战斗统计
    ArmyListPnlCtrl.prototype.showBattleStatistics = function (battleUid, uids) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetArmyRecordsByUids({ battleUid: battleUid, uids: uids })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if ((_a = data.list) === null || _a === void 0 ? void 0 : _a.length) {
                            ViewHelper_1.viewHelper.showPnl('main/BattleStatistics', data.list);
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.no_data_tip');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 回放战斗
    ArmyListPnlCtrl.prototype.playbackBattle = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                    case 2:
                        _a.sent();
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    ArmyListPnlCtrl = __decorate([
        ccclass
    ], ArmyListPnlCtrl);
    return ArmyListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ArmyListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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