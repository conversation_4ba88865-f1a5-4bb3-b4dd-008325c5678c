
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/MarchQueuePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f5628TLOiRCMo1MoowGHgB1', 'MarchQueuePnlCtrl');
// app/script/view/common/MarchQueuePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MarchQueuePnlCtrl = /** @class */ (function (_super) {
    __extends(MarchQueuePnlCtrl, _super);
    function MarchQueuePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.readyCancelMarch = null; //准备取消的行军
        return _this;
    }
    MarchQueuePnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[EventType_1.default.ADD_MARCH] = this.onAddMarch, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _c.enter = true, _c),
        ];
    };
    MarchQueuePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MarchQueuePnlCtrl.prototype.onEnter = function (data) {
        this.emit(EventType_1.default.CLOSE_SELECT_CELL); //关闭地块信息面板
        this.updateList();
    };
    MarchQueuePnlCtrl.prototype.onRemove = function () {
        this.readyCancelMarch = null;
    };
    MarchQueuePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/cancel_be
    MarchQueuePnlCtrl.prototype.onClickCancel = function (event, _) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        this.readyCancelMarch = data;
        ViewHelper_1.viewHelper.showPnl('common/CancelMarchTip', data, function (ok) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.readyCancelMarch = null;
            if (!ok) {
                return;
            }
            GameHelper_1.gameHpr.world.cancelMarch(data.uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                } /* else if (this.isValid) {
                    this.hide()
                } */
            });
        });
    };
    // path://root/list_sv/view/content/item/force_revoke_be
    MarchQueuePnlCtrl.prototype.onClickForceRevoke = function (event, _) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        this.readyCancelMarch = data;
        ViewHelper_1.viewHelper.showMessageBox('ui.force_revoke_tip', {
            params: [GameHelper_1.gameHpr.getPlayerName(data.owner), data.armyName],
            ok: function () {
                _this.readyCancelMarch = null;
                GameHelper_1.gameHpr.world.forceRevoke(data.armyUid).then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        _this.hide();
                    }
                });
            },
            cancel: function () {
                _this.readyCancelMarch = null;
            }
        });
    };
    // path://root/list_sv/view/content/item/target/target_be
    MarchQueuePnlCtrl.prototype.onClickTarget = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data) {
            this.hide();
            GameHelper_1.gameHpr.gotoTargetPos(data.targetIndex);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    MarchQueuePnlCtrl.prototype.onAddMarch = function (data) {
        if (data.getMarchLineType() !== Enums_1.MarchLineType.MERCHANT) {
            this.updateList();
        }
    };
    MarchQueuePnlCtrl.prototype.onRemoveMarch = function (data) {
        this.onAddMarch(data);
    };
    MarchQueuePnlCtrl.prototype.onUpdateAllMarch = function () {
        this.updateList();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    MarchQueuePnlCtrl.prototype.updateList = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        var list = GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.isCanShowUI(); }).sort(function (a, b) {
            var aOwn = a.owner === uid ? 1 : 0, bOwn = b.owner === uid ? 1 : 0;
            if (aOwn !== bOwn) {
                return bOwn - aOwn;
            }
            var atime = a.getSurplusTime(), btime = b.getSurplusTime();
            if (atime === btime || a.needTime === b.needTime) {
                return a.notifyIndex - b.notifyIndex;
            }
            return atime - btime;
        });
        if (this.readyCancelMarch && !list.has('uid', this.readyCancelMarch.uid)) {
            ViewHelper_1.viewHelper.hidePnl('common/CancelMarchTip'); //如果当前打开准备取消的行军 那么主动隐藏掉
        }
        if (list.length === 0) {
            return this.hide();
        }
        this.listSv_.node.height = Math.min(730, list.length * 52 + 2);
        this.listSv_.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        this.listSv_.Items(list, function (it, data) {
            it.Data = data;
            it.Child('name', cc.Label).string = ut.nameFormator(data.armyName, 6);
            it.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001);
            it.Child('target/name').Color(Constant_1.MARCH_ARMY_NAME_COLOR[data.getMarchLineType()]).setLocaleKey('ui.march_target_' + data.targetType);
            ViewHelper_1.viewHelper.updatePositionView(it.Child('target/target_be'), data.targetIndex);
            it.Child('cancel_be').active = data.isCanCancel();
            it.Child('force_revoke_be').active = data.isCanForceRevoke();
        });
        this.listSv_.scrollToTop();
    };
    MarchQueuePnlCtrl = __decorate([
        ccclass
    ], MarchQueuePnlCtrl);
    return MarchQueuePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MarchQueuePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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