
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/shader/OutlineShaderCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '51ce4h2xhlAz7d4dvLxl6Ac', 'OutlineShaderCtrl');
// app/script/common/shader/OutlineShaderCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 描边组件
 * 静态使用方法：
 * 1. 将OutlineShaderCtrl.ts挂载到节点上
 * 2. 指定sprite组件
 * 3. sprite组件材质使用OutlineSprite
 * 动态使用示例:
 *  const ost = sprite.node.addComponent(OutlineShaderCtrl)
 *  ost.setTarget(sprite)
 *  ost.setOutlineSize(1) //设置描边大小
 *  ost.node.color = cc.Color.WHITE //设置描边颜色
 */
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
var OutlineShaderCtrl = /** @class */ (function (_super) {
    __extends(OutlineShaderCtrl, _super);
    function OutlineShaderCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sprite = null;
        _this.blur = 1;
        _this.color = cc.Color.WHITE;
        _this._previouseMaterial = null;
        _this._trim = null;
        _this._sizeMode = null;
        _this._type = null;
        _this._visible = false;
        // 检测spriteFrame是否发生变化
        _this._frame = null;
        return _this;
    }
    OutlineShaderCtrl.prototype.onLoad = function () {
        if (!this.sprite) {
            this.sprite = this.getComponent(cc.Sprite);
        }
    };
    OutlineShaderCtrl.prototype.onEnable = function () {
        if (this._visible) {
            this._show();
        }
    };
    OutlineShaderCtrl.prototype.onDisable = function () {
        if (this._visible) {
            this._hide();
        }
    };
    OutlineShaderCtrl.prototype.onDestroy = function () {
        this.setVisible(false);
        this.sprite = null;
    };
    OutlineShaderCtrl.prototype.setTarget = function (sprite) {
        this.sprite = sprite;
        // this._visible && this._show()
    };
    OutlineShaderCtrl.prototype.setOutlineSize = function (blur) {
        this.blur = blur;
        // this._visible && this._show()
    };
    OutlineShaderCtrl.prototype.setColor = function (color) {
        this.color = color;
        // this._visible && this._show()
    };
    OutlineShaderCtrl.prototype.setVisible = function (v) {
        v ? this._show() : this._hide();
        this._visible = v;
    };
    OutlineShaderCtrl.prototype.getVisible = function () {
        return this._visible;
    };
    OutlineShaderCtrl.prototype.toggle = function () {
        this.setVisible(!this._visible);
    };
    OutlineShaderCtrl.prototype._show = function () {
        this._recoverMeshMode();
        this._applyMeshMode();
        this._updateVerts();
        this.unschedule(this.checkFrameChange);
        this.schedule(this.checkFrameChange);
    };
    OutlineShaderCtrl.prototype._hide = function () {
        this._recoverMeshMode();
        this.unschedule(this.checkFrameChange);
    };
    // 更新frame的顶点数据
    OutlineShaderCtrl.prototype._updateVerts = function () {
        var sprite = this.sprite;
        var frame = sprite === null || sprite === void 0 ? void 0 : sprite.spriteFrame;
        if (!frame) {
            return;
        }
        var oSize = frame.getOriginalSize();
        var tw = frame['_texture'].width;
        var th = frame['_texture'].height;
        var disU = this.blur / tw;
        var disV = this.blur / th;
        var rect = frame.getRect(), offset = frame.getOffset(), ow = oSize.width, oh = oSize.height, rw = rect.width, rh = rect.height;
        var trimX = offset.x + (ow - rw) / 2;
        var trimY = -offset.y + (oh - rh) / 2;
        var l = trimX;
        var b = trimY;
        var r = l + rect.width;
        var t = b + rect.height;
        var lt = { x: l, y: t };
        var rt = { x: r, y: t };
        var lb = { x: l, y: b };
        var rb = { x: r, y: b };
        var pos = { lt: lt, rt: rt, lb: lb, rb: rb };
        var uv = this._getUVs(frame);
        var _a = this._updateSliceVerts(pos, uv, disU, disV, frame.isRotated()), vts = _a.vts, triangles = _a.triangles;
        var x = [], y = [], nu = [], nv = [];
        for (var i = 0; i < vts.length; i++) {
            x.push(vts[i].x);
            y.push(vts[i].y);
            nu.push(vts[i].u);
            nv.push(vts[i].v);
        }
        frame['vertices'] = {
            x: x, y: y, nu: nu, nv: nv, triangles: triangles
        };
        var mat = sprite.getMaterial(0);
        var rangeX = cc.v2(uv.lt.x, uv.rb.x);
        var rangeY = cc.v2(uv.rb.y, uv.lt.y);
        mat.setProperty('rangeX', rangeX);
        mat.setProperty('rangeY', rangeY);
        mat.setProperty('outlineSize', cc.v2(disU, disV));
        mat.setProperty('color', this.color);
    };
    // 需要将sprite的type设置为mesh
    OutlineShaderCtrl.prototype._applyMeshMode = function () {
        var sprite = this.sprite;
        if (sprite) {
            this._trim = sprite.trim;
            this._previouseMaterial = sprite.getMaterial(0);
            this._type = sprite.type;
            this._sizeMode = sprite.sizeMode;
            sprite.trim = false;
            var mat = assetsMgr.getMaterial('OutlineSprite');
            mat && sprite.setMaterial(0, mat);
            sprite.type = cc.Sprite.Type.MESH;
            sprite.sizeMode = cc.Sprite.SizeMode.RAW;
        }
    };
    // 恢复sprite的原有设置
    OutlineShaderCtrl.prototype._recoverMeshMode = function () {
        var lastSprite = this.sprite;
        if (lastSprite && this._previouseMaterial) {
            lastSprite.trim = this._trim;
            lastSprite.setMaterial(0, this._previouseMaterial);
            lastSprite.type = this._type;
            lastSprite.sizeMode = this._sizeMode;
        }
        this._frame = null;
        this._trim = null;
        this._previouseMaterial = null;
        this._sizeMode = null;
        this._type = null;
    };
    OutlineShaderCtrl.prototype._getUVs = function (frame) {
        //@ts-ignore
        var uv = frame.uv;
        var minU = Infinity, minV = Infinity, maxU = -Infinity, maxV = -Infinity;
        for (var i = 0; i < 4; i++) {
            var srcOffset = i * 2;
            var x = uv[srcOffset];
            var y = uv[srcOffset + 1];
            minU = Math.min(minU, x);
            minV = Math.min(minV, y);
            maxU = Math.max(maxU, x);
            maxV = Math.max(maxV, y);
        }
        var lt = { x: minU, y: maxV };
        var rt = { x: maxU, y: maxV };
        var lb = { x: minU, y: minV };
        var rb = { x: maxU, y: minV };
        return { lt: lt, rt: rt, lb: lb, rb: rb };
    };
    OutlineShaderCtrl.prototype._updateSliceVerts = function (pos, uv, disU, disV, isRotated) {
        if (isRotated === void 0) { isRotated = false; }
        var extendEdge = this.blur;
        var moveLeft = { x: -extendEdge, y: 0 };
        var moveRight = { x: extendEdge, y: 0 };
        var moveUp = { x: 0, y: extendEdge };
        var moveDown = { x: 0, y: -extendEdge };
        var p6 = pos.lt;
        var p7 = pos.rt;
        var p10 = pos.lb;
        var p11 = pos.rb;
        var p2 = this._vertAdd(pos.lt, moveUp);
        var p3 = this._vertAdd(pos.rt, moveUp);
        var p14 = this._vertAdd(pos.lb, moveDown);
        var p15 = this._vertAdd(pos.rb, moveDown);
        var p5 = this._vertAdd(pos.lt, moveLeft);
        var p9 = this._vertAdd(pos.lb, moveLeft);
        var p8 = this._vertAdd(pos.rt, moveRight);
        var p12 = this._vertAdd(pos.rb, moveRight);
        var p1 = this._vertAdd(p5, moveUp);
        var p4 = this._vertAdd(p8, moveUp);
        var p13 = this._vertAdd(p9, moveDown);
        var p16 = this._vertAdd(p12, moveDown);
        var matrix = [
            [p1, p2, p3, p4],
            [p5, p6, p7, p8],
            [p9, p10, p11, p12],
            [p13, p14, p15, p16],
        ];
        var uvMoveLeft = { x: -disU, y: 0 };
        var uvMoveRight = { x: disU, y: 0 };
        var uvMoveUp = { x: 0, y: disV };
        var uvMoveDown = { x: 0, y: -disV };
        var uv6 = uv.lt;
        var uv7 = uv.rt;
        var uv10 = uv.lb;
        var uv11 = uv.rb;
        var uv2 = this._vertAdd(uv.lt, uvMoveUp);
        var uv3 = this._vertAdd(uv.rt, uvMoveUp);
        var uv14 = this._vertAdd(uv.lb, uvMoveDown);
        var uv15 = this._vertAdd(uv.rb, uvMoveDown);
        var uv5 = this._vertAdd(uv.lt, uvMoveLeft);
        var uv9 = this._vertAdd(uv.lb, uvMoveLeft);
        var uv8 = this._vertAdd(uv.rt, uvMoveRight);
        var uv12 = this._vertAdd(uv.rb, uvMoveRight);
        var uv1 = this._vertAdd(uv5, uvMoveUp);
        var uv4 = this._vertAdd(uv8, uvMoveUp);
        var uv13 = this._vertAdd(uv9, uvMoveDown);
        var uv16 = this._vertAdd(uv12, uvMoveDown);
        var UVmatrix = [
            [uv1, uv2, uv3, uv4],
            [uv5, uv6, uv7, uv8],
            [uv9, uv10, uv11, uv12],
            [uv13, uv14, uv15, uv16],
        ];
        if (isRotated) {
            UVmatrix = this._rotateMatrixRight(UVmatrix);
        }
        var vts = [];
        for (var i = 0; i < matrix.length; i++) {
            for (var j = 0; j < matrix[i].length; j++) {
                matrix[i][j].u = UVmatrix[i][j].x;
                matrix[i][j].v = UVmatrix[i][j].y;
                vts.push(matrix[i][j]);
            }
        }
        var centerQuad = this._createQuad([5, 6, 9, 10]);
        var tlQuad = this._createQuad([0, 1, 4, 5]);
        var topQuad = this._createQuad([1, 2, 5, 6]);
        var trQuad = this._createQuad([2, 3, 6, 7]);
        var rightQuad = this._createQuad([3, 7, 11, 15]);
        var brQuad = this._createQuad([7, 6, 15, 14]);
        var bottomQuad = this._createQuad([6, 5, 14, 13]);
        var blQuad = this._createQuad([5, 4, 13, 12]);
        var leftQuad = this._createQuad([4, 0, 12, 8]);
        var triangles = __spread(centerQuad, tlQuad, topQuad, trQuad, rightQuad, brQuad, bottomQuad, blQuad, leftQuad);
        return { vts: vts, triangles: triangles };
    };
    OutlineShaderCtrl.prototype._vertAdd = function (v1, v2) {
        return {
            x: v1.x + v2.x,
            y: v1.y + v2.y
        };
    };
    OutlineShaderCtrl.prototype._createQuad = function (idxs) {
        var e_1, _a;
        var indicesTemp = [0, 1, 3, 0, 3, 2];
        var triangles = [];
        try {
            for (var indicesTemp_1 = __values(indicesTemp), indicesTemp_1_1 = indicesTemp_1.next(); !indicesTemp_1_1.done; indicesTemp_1_1 = indicesTemp_1.next()) {
                var indice = indicesTemp_1_1.value;
                var p = idxs[indice];
                triangles.push(p);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (indicesTemp_1_1 && !indicesTemp_1_1.done && (_a = indicesTemp_1.return)) _a.call(indicesTemp_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return triangles;
    };
    OutlineShaderCtrl.prototype.checkFrameChange = function () {
        if (this.sprite) {
            if (this._frame !== this.sprite.spriteFrame) {
                this._frame = this.sprite.spriteFrame;
                this._updateVerts();
            }
        }
    };
    // 顺时针旋转矩阵
    OutlineShaderCtrl.prototype._rotateMatrixRight = function (matrix) {
        var rows = matrix.length;
        var cols = matrix[0].length;
        var rotatedMatrix = [];
        for (var i = 0; i < rows; i++) {
            for (var j = 0; j < cols; j++) {
                if (!rotatedMatrix[j])
                    rotatedMatrix[j] = [];
                rotatedMatrix[j][rows - 1 - i] = matrix[i][j];
            }
        }
        return rotatedMatrix;
    };
    __decorate([
        property(cc.Sprite)
    ], OutlineShaderCtrl.prototype, "sprite", void 0);
    __decorate([
        property(cc.Float)
    ], OutlineShaderCtrl.prototype, "blur", void 0);
    __decorate([
        property(cc.Color)
    ], OutlineShaderCtrl.prototype, "color", void 0);
    OutlineShaderCtrl = __decorate([
        ccclass,
        menu('Shader/OutlineShaderCtrl')
    ], OutlineShaderCtrl);
    return OutlineShaderCtrl;
}(cc.Component));
exports.default = OutlineShaderCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcc2hhZGVyXFxPdXRsaW5lU2hhZGVyQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Ozs7O0dBV0c7QUFDRyxJQUFBLEtBQThCLEVBQUUsQ0FBQyxVQUFVLEVBQXpDLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBQSxFQUFFLElBQUksVUFBa0IsQ0FBQztBQUlsRDtJQUErQyxxQ0FBWTtJQUEzRDtRQUFBLHFFQXFVQztRQWxVVyxZQUFNLEdBQWMsSUFBSSxDQUFBO1FBRXhCLFVBQUksR0FBRyxDQUFDLENBQUE7UUFFUixXQUFLLEdBQWEsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUE7UUFRaEMsd0JBQWtCLEdBQWdCLElBQUksQ0FBQTtRQUN0QyxXQUFLLEdBQVksSUFBSSxDQUFBO1FBQ3JCLGVBQVMsR0FBdUIsSUFBSSxDQUFBO1FBQ3BDLFdBQUssR0FBbUIsSUFBSSxDQUFBO1FBRTVCLGNBQVEsR0FBWSxLQUFLLENBQUE7UUF1UmpDLHNCQUFzQjtRQUNkLFlBQU0sR0FBbUIsSUFBSSxDQUFBOztJQXlCekMsQ0FBQztJQTVURyxrQ0FBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDZCxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1NBQzdDO0lBQ0wsQ0FBQztJQVNELG9DQUFRLEdBQVI7UUFDSSxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUU7WUFDZixJQUFJLENBQUMsS0FBSyxFQUFFLENBQUE7U0FDZjtJQUNMLENBQUM7SUFFRCxxQ0FBUyxHQUFUO1FBQ0ksSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFO1lBQ2YsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFBO1NBQ2Y7SUFDTCxDQUFDO0lBRUQscUNBQVMsR0FBVDtRQUNJLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDdEIsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7SUFDdEIsQ0FBQztJQUVNLHFDQUFTLEdBQWhCLFVBQWlCLE1BQWlCO1FBQzlCLElBQUksQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFBO1FBQ3BCLGdDQUFnQztJQUNwQyxDQUFDO0lBRU0sMENBQWMsR0FBckIsVUFBc0IsSUFBWTtRQUM5QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixnQ0FBZ0M7SUFDcEMsQ0FBQztJQUVNLG9DQUFRLEdBQWYsVUFBZ0IsS0FBZTtRQUMzQixJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTtRQUNsQixnQ0FBZ0M7SUFDcEMsQ0FBQztJQUVNLHNDQUFVLEdBQWpCLFVBQWtCLENBQVU7UUFDeEIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUMvQixJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQTtJQUNyQixDQUFDO0lBRU0sc0NBQVUsR0FBakI7UUFDSSxPQUFPLElBQUksQ0FBQyxRQUFRLENBQUE7SUFDeEIsQ0FBQztJQUVNLGtDQUFNLEdBQWI7UUFDSSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO0lBQ25DLENBQUM7SUFFTyxpQ0FBSyxHQUFiO1FBQ0ksSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUE7UUFDdkIsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ3JCLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQTtRQUNuQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO1FBQ3RDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUE7SUFDeEMsQ0FBQztJQUVPLGlDQUFLLEdBQWI7UUFDSSxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtRQUN2QixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO0lBQzFDLENBQUM7SUFFRCxlQUFlO0lBQ1Asd0NBQVksR0FBcEI7UUFDSSxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFBO1FBQzFCLElBQU0sS0FBSyxHQUFHLE1BQU0sYUFBTixNQUFNLHVCQUFOLE1BQU0sQ0FBRSxXQUFXLENBQUE7UUFDakMsSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNSLE9BQU07U0FDVDtRQUNELElBQU0sS0FBSyxHQUFHLEtBQUssQ0FBQyxlQUFlLEVBQUUsQ0FBQTtRQUNyQyxJQUFNLEVBQUUsR0FBRyxLQUFLLENBQUMsVUFBVSxDQUFDLENBQUMsS0FBSyxDQUFBO1FBQ2xDLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxVQUFVLENBQUMsQ0FBQyxNQUFNLENBQUE7UUFFbkMsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksR0FBRyxFQUFFLENBQUE7UUFDekIsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksR0FBRyxFQUFFLENBQUE7UUFFekIsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLE9BQU8sRUFBRSxFQUN0QixNQUFNLEdBQUcsS0FBSyxDQUFDLFNBQVMsRUFBRSxFQUMxQixFQUFFLEdBQUcsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLEdBQUcsS0FBSyxDQUFDLE1BQU0sRUFDbkMsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLEVBQUUsRUFBRSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDckMsSUFBSSxLQUFLLEdBQUcsTUFBTSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDckMsSUFBSSxLQUFLLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUN0QyxJQUFJLENBQUMsR0FBRyxLQUFLLENBQUE7UUFDYixJQUFJLENBQUMsR0FBRyxLQUFLLENBQUE7UUFDYixJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtRQUN0QixJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQTtRQUV2QixJQUFJLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFBO1FBQ3ZCLElBQUksRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7UUFDdkIsSUFBSSxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQTtRQUN2QixJQUFJLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFBO1FBRXZCLElBQU0sR0FBRyxHQUFHLEVBQUUsRUFBRSxJQUFBLEVBQUUsRUFBRSxJQUFBLEVBQUUsRUFBRSxJQUFBLEVBQUUsRUFBRSxJQUFBLEVBQUUsQ0FBQTtRQUM5QixJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBRXhCLElBQUEsS0FBcUIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLEdBQUcsRUFBRSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxLQUFLLENBQUMsU0FBUyxFQUFFLENBQUMsRUFBakYsR0FBRyxTQUFBLEVBQUUsU0FBUyxlQUFtRSxDQUFBO1FBRXpGLElBQU0sQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsQ0FBQTtRQUN0QyxLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLE1BQU0sRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUNqQyxDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNoQixDQUFDLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNoQixFQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNqQixFQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtTQUNwQjtRQUNELEtBQUssQ0FBQyxVQUFVLENBQUMsR0FBRztZQUNoQixDQUFDLEdBQUEsRUFBRSxDQUFDLEdBQUEsRUFBRSxFQUFFLElBQUEsRUFBRSxFQUFFLElBQUEsRUFBRSxTQUFTLFdBQUE7U0FDMUIsQ0FBQTtRQUVELElBQU0sR0FBRyxHQUFHLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDakMsSUFBSSxNQUFNLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3BDLElBQUksTUFBTSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUVwQyxHQUFHLENBQUMsV0FBVyxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUNqQyxHQUFHLENBQUMsV0FBVyxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUNqQyxHQUFHLENBQUMsV0FBVyxDQUFDLGFBQWEsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQ2pELEdBQUcsQ0FBQyxXQUFXLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUN4QyxDQUFDO0lBRUQsd0JBQXdCO0lBQ2hCLDBDQUFjLEdBQXRCO1FBQ0ksSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQTtRQUMxQixJQUFJLE1BQU0sRUFBRTtZQUNSLElBQUksQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQTtZQUN4QixJQUFJLENBQUMsa0JBQWtCLEdBQUcsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMvQyxJQUFJLENBQUMsS0FBSyxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUE7WUFDeEIsSUFBSSxDQUFDLFNBQVMsR0FBRyxNQUFNLENBQUMsUUFBUSxDQUFBO1lBQ2hDLE1BQU0sQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFBO1lBQ25CLElBQUksR0FBRyxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsZUFBZSxDQUFDLENBQUE7WUFDaEQsR0FBRyxJQUFJLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFBO1lBQ2pDLE1BQU0sQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFBO1lBQ2pDLE1BQU0sQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFBO1NBQzNDO0lBQ0wsQ0FBQztJQUVELGdCQUFnQjtJQUNSLDRDQUFnQixHQUF4QjtRQUNJLElBQUksVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDNUIsSUFBSSxVQUFVLElBQUksSUFBSSxDQUFDLGtCQUFrQixFQUFFO1lBQ3ZDLFVBQVUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtZQUM1QixVQUFVLENBQUMsV0FBVyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtZQUNsRCxVQUFVLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7WUFDNUIsVUFBVSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFBO1NBQ3ZDO1FBQ0QsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7UUFDbEIsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7UUFDakIsSUFBSSxDQUFDLGtCQUFrQixHQUFHLElBQUksQ0FBQTtRQUM5QixJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQTtRQUNyQixJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQTtJQUNyQixDQUFDO0lBRU8sbUNBQU8sR0FBZixVQUFnQixLQUFxQjtRQUNqQyxZQUFZO1FBQ1osSUFBSSxFQUFFLEdBQUcsS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUNsQixJQUFJLElBQUksR0FBRyxRQUFRLEVBQUUsSUFBSSxHQUFHLFFBQVEsRUFBRSxJQUFJLEdBQUcsQ0FBQyxRQUFRLEVBQUUsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDO1FBQ3pFLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDeEIsSUFBSSxTQUFTLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQztZQUN0QixJQUFJLENBQUMsR0FBRyxFQUFFLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDdEIsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDLFNBQVMsR0FBRyxDQUFDLENBQUMsQ0FBQztZQUMxQixJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDekIsSUFBSSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ3pCLElBQUksR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztZQUN6QixJQUFJLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7U0FDNUI7UUFFRCxJQUFJLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFBO1FBQzdCLElBQUksRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUE7UUFDN0IsSUFBSSxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQTtRQUM3QixJQUFJLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFBO1FBQzdCLE9BQU8sRUFBRSxFQUFFLElBQUEsRUFBRSxFQUFFLElBQUEsRUFBRSxFQUFFLElBQUEsRUFBRSxFQUFFLElBQUEsRUFBRSxDQUFBO0lBQzdCLENBQUM7SUFFTyw2Q0FBaUIsR0FBekIsVUFBMEIsR0FBUSxFQUFFLEVBQU0sRUFBRSxJQUFZLEVBQUUsSUFBWSxFQUFFLFNBQTBCO1FBQTFCLDBCQUFBLEVBQUEsaUJBQTBCO1FBQzlGLElBQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUE7UUFFNUIsSUFBTSxRQUFRLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFBO1FBQ3pDLElBQU0sU0FBUyxHQUFHLEVBQUUsQ0FBQyxFQUFFLFVBQVUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7UUFDekMsSUFBTSxNQUFNLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxVQUFVLEVBQUUsQ0FBQTtRQUN0QyxJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsVUFBVSxFQUFFLENBQUE7UUFFekMsSUFBSSxFQUFFLEdBQUcsR0FBRyxDQUFDLEVBQUUsQ0FBQTtRQUNmLElBQUksRUFBRSxHQUFHLEdBQUcsQ0FBQyxFQUFFLENBQUE7UUFDZixJQUFJLEdBQUcsR0FBRyxHQUFHLENBQUMsRUFBRSxDQUFBO1FBQ2hCLElBQUksR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLENBQUE7UUFFaEIsSUFBSSxFQUFFLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFLE1BQU0sQ0FBQyxDQUFBO1FBQ3RDLElBQUksRUFBRSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUN0QyxJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUE7UUFDekMsSUFBSSxHQUFHLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUFBO1FBRXpDLElBQUksRUFBRSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQTtRQUN4QyxJQUFJLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUE7UUFDeEMsSUFBSSxFQUFFLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFBO1FBQ3pDLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQTtRQUUxQyxJQUFJLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUNsQyxJQUFJLEVBQUUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsRUFBRSxNQUFNLENBQUMsQ0FBQTtRQUNsQyxJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQTtRQUNyQyxJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEdBQUcsRUFBRSxRQUFRLENBQUMsQ0FBQTtRQUV0QyxJQUFJLE1BQU0sR0FBRztZQUNULENBQUMsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDO1lBQ2hCLENBQUMsRUFBRSxFQUFFLEVBQUUsRUFBRSxFQUFFLEVBQUUsRUFBRSxDQUFDO1lBQ2hCLENBQUMsRUFBRSxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO1lBQ25CLENBQUMsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxDQUFDO1NBQ3ZCLENBQUE7UUFFRCxJQUFNLFVBQVUsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUE7UUFDckMsSUFBTSxXQUFXLEdBQUcsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQTtRQUNyQyxJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFBO1FBQ2xDLElBQU0sVUFBVSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsQ0FBQTtRQUVyQyxJQUFJLEdBQUcsR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFBO1FBQ2YsSUFBSSxHQUFHLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQTtRQUNmLElBQUksSUFBSSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUE7UUFDaEIsSUFBSSxJQUFJLEdBQUcsRUFBRSxDQUFDLEVBQUUsQ0FBQTtRQUVoQixJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUE7UUFDeEMsSUFBSSxHQUFHLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUFBO1FBQ3hDLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxVQUFVLENBQUMsQ0FBQTtRQUMzQyxJQUFJLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsVUFBVSxDQUFDLENBQUE7UUFFM0MsSUFBSSxHQUFHLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLFVBQVUsQ0FBQyxDQUFBO1FBQzFDLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxVQUFVLENBQUMsQ0FBQTtRQUMxQyxJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsV0FBVyxDQUFDLENBQUE7UUFDM0MsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFLFdBQVcsQ0FBQyxDQUFBO1FBRTVDLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxFQUFFLFFBQVEsQ0FBQyxDQUFBO1FBQ3RDLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxFQUFFLFFBQVEsQ0FBQyxDQUFBO1FBQ3RDLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxFQUFFLFVBQVUsQ0FBQyxDQUFBO1FBQ3pDLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFFLFVBQVUsQ0FBQyxDQUFBO1FBRTFDLElBQUksUUFBUSxHQUFHO1lBQ1gsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUM7WUFDcEIsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLENBQUM7WUFDcEIsQ0FBQyxHQUFHLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUM7WUFDdkIsQ0FBQyxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUM7U0FDM0IsQ0FBQTtRQUNELElBQUksU0FBUyxFQUFFO1lBQ1gsUUFBUSxHQUFHLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxRQUFRLENBQUMsQ0FBQTtTQUMvQztRQUNELElBQU0sR0FBRyxHQUFHLEVBQUUsQ0FBQTtRQUNkLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQ3BDLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUN2QyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQ2pDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtnQkFDakMsR0FBRyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTthQUN6QjtTQUNKO1FBRUQsSUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDbEQsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDN0MsSUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDOUMsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDN0MsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDbEQsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDL0MsSUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDbkQsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDL0MsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFFaEQsSUFBTSxTQUFTLFlBQU8sVUFBVSxFQUFLLE1BQU0sRUFBSyxPQUFPLEVBQUssTUFBTSxFQUFLLFNBQVMsRUFBSyxNQUFNLEVBQUssVUFBVSxFQUFLLE1BQU0sRUFBSyxRQUFRLENBQUMsQ0FBQTtRQUVuSSxPQUFPLEVBQUUsR0FBRyxLQUFBLEVBQUUsU0FBUyxXQUFBLEVBQUUsQ0FBQTtJQUM3QixDQUFDO0lBRU8sb0NBQVEsR0FBaEIsVUFBaUIsRUFBUSxFQUFFLEVBQVE7UUFDL0IsT0FBTztZQUNILENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxDQUFDO1lBQ2QsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUM7U0FDakIsQ0FBQTtJQUNMLENBQUM7SUFFTyx1Q0FBVyxHQUFuQixVQUFvQixJQUFjOztRQUM5QixJQUFNLFdBQVcsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFDdEMsSUFBTSxTQUFTLEdBQUcsRUFBRSxDQUFBOztZQUNwQixLQUFtQixJQUFBLGdCQUFBLFNBQUEsV0FBVyxDQUFBLHdDQUFBLGlFQUFFO2dCQUEzQixJQUFJLE1BQU0sd0JBQUE7Z0JBQ1gsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO2dCQUNwQixTQUFTLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQ3BCOzs7Ozs7Ozs7UUFDRCxPQUFPLFNBQVMsQ0FBQTtJQUNwQixDQUFDO0lBSU8sNENBQWdCLEdBQXhCO1FBQ0ksSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQ2IsSUFBSSxJQUFJLENBQUMsTUFBTSxLQUFLLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxFQUFFO2dCQUN6QyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFBO2dCQUNyQyxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUE7YUFDdEI7U0FDSjtJQUNMLENBQUM7SUFFRCxVQUFVO0lBQ0YsOENBQWtCLEdBQTFCLFVBQThCLE1BQWE7UUFDdkMsSUFBTSxJQUFJLEdBQUcsTUFBTSxDQUFDLE1BQU0sQ0FBQTtRQUMxQixJQUFNLElBQUksR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFBO1FBQzdCLElBQU0sYUFBYSxHQUFHLEVBQUUsQ0FBQTtRQUV4QixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzNCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxJQUFJLEVBQUUsQ0FBQyxFQUFFLEVBQUU7Z0JBQzNCLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDO29CQUFFLGFBQWEsQ0FBQyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUM7Z0JBQzdDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQzthQUNqRDtTQUNKO1FBRUQsT0FBTyxhQUFhLENBQUM7SUFDekIsQ0FBQztJQWpVRDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO3FEQUNZO0lBRWhDO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUM7bURBQ0g7SUFFaEI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQztvREFDcUI7SUFQdkIsaUJBQWlCO1FBRnJDLE9BQU87UUFDUCxJQUFJLENBQUMsMEJBQTBCLENBQUM7T0FDWixpQkFBaUIsQ0FxVXJDO0lBQUQsd0JBQUM7Q0FyVUQsQUFxVUMsQ0FyVThDLEVBQUUsQ0FBQyxTQUFTLEdBcVUxRDtrQkFyVW9CLGlCQUFpQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5o+P6L6557uE5Lu2XG4gKiDpnZnmgIHkvb/nlKjmlrnms5XvvJpcbiAqIDEuIOWwhk91dGxpbmVTaGFkZXJDdHJsLnRz5oyC6L295Yiw6IqC54K55LiKXG4gKiAyLiDmjIflrppzcHJpdGXnu4Tku7ZcbiAqIDMuIHNwcml0Zee7hOS7tuadkOi0qOS9v+eUqE91dGxpbmVTcHJpdGVcbiAqIOWKqOaAgeS9v+eUqOekuuS+izpcbiAqICBjb25zdCBvc3QgPSBzcHJpdGUubm9kZS5hZGRDb21wb25lbnQoT3V0bGluZVNoYWRlckN0cmwpXG4gKiAgb3N0LnNldFRhcmdldChzcHJpdGUpXG4gKiAgb3N0LnNldE91dGxpbmVTaXplKDEpIC8v6K6+572u5o+P6L655aSn5bCPXG4gKiAgb3N0Lm5vZGUuY29sb3IgPSBjYy5Db2xvci5XSElURSAvL+iuvue9ruaPj+i+ueminOiJslxuICovXG5jb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5LCBtZW51IH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5AY2NjbGFzc1xuQG1lbnUoJ1NoYWRlci9PdXRsaW5lU2hhZGVyQ3RybCcpXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBPdXRsaW5lU2hhZGVyQ3RybCBleHRlbmRzIGNjLkNvbXBvbmVudCB7XG5cbiAgICBAcHJvcGVydHkoY2MuU3ByaXRlKVxuICAgIHByaXZhdGUgc3ByaXRlOiBjYy5TcHJpdGUgPSBudWxsXG4gICAgQHByb3BlcnR5KGNjLkZsb2F0KVxuICAgIHByaXZhdGUgYmx1ciA9IDFcbiAgICBAcHJvcGVydHkoY2MuQ29sb3IpXG4gICAgcHJpdmF0ZSBjb2xvcjogY2MuQ29sb3IgPSBjYy5Db2xvci5XSElURVxuXG4gICAgb25Mb2FkKCkge1xuICAgICAgICBpZiAoIXRoaXMuc3ByaXRlKSB7XG4gICAgICAgICAgICB0aGlzLnNwcml0ZSA9IHRoaXMuZ2V0Q29tcG9uZW50KGNjLlNwcml0ZSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgX3ByZXZpb3VzZU1hdGVyaWFsOiBjYy5NYXRlcmlhbCA9IG51bGxcbiAgICBwcml2YXRlIF90cmltOiBib29sZWFuID0gbnVsbFxuICAgIHByaXZhdGUgX3NpemVNb2RlOiBjYy5TcHJpdGUuU2l6ZU1vZGUgPSBudWxsXG4gICAgcHJpdmF0ZSBfdHlwZTogY2MuU3ByaXRlLlR5cGUgPSBudWxsXG5cbiAgICBwcml2YXRlIF92aXNpYmxlOiBib29sZWFuID0gZmFsc2VcblxuICAgIG9uRW5hYmxlKCk6IHZvaWQge1xuICAgICAgICBpZiAodGhpcy5fdmlzaWJsZSkge1xuICAgICAgICAgICAgdGhpcy5fc2hvdygpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBvbkRpc2FibGUoKTogdm9pZCB7XG4gICAgICAgIGlmICh0aGlzLl92aXNpYmxlKSB7XG4gICAgICAgICAgICB0aGlzLl9oaWRlKClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIG9uRGVzdHJveSgpOiB2b2lkIHtcbiAgICAgICAgdGhpcy5zZXRWaXNpYmxlKGZhbHNlKVxuICAgICAgICB0aGlzLnNwcml0ZSA9IG51bGxcbiAgICB9XG5cbiAgICBwdWJsaWMgc2V0VGFyZ2V0KHNwcml0ZTogY2MuU3ByaXRlKSB7XG4gICAgICAgIHRoaXMuc3ByaXRlID0gc3ByaXRlXG4gICAgICAgIC8vIHRoaXMuX3Zpc2libGUgJiYgdGhpcy5fc2hvdygpXG4gICAgfVxuXG4gICAgcHVibGljIHNldE91dGxpbmVTaXplKGJsdXI6IG51bWJlcikge1xuICAgICAgICB0aGlzLmJsdXIgPSBibHVyXG4gICAgICAgIC8vIHRoaXMuX3Zpc2libGUgJiYgdGhpcy5fc2hvdygpXG4gICAgfVxuXG4gICAgcHVibGljIHNldENvbG9yKGNvbG9yOiBjYy5Db2xvcikge1xuICAgICAgICB0aGlzLmNvbG9yID0gY29sb3JcbiAgICAgICAgLy8gdGhpcy5fdmlzaWJsZSAmJiB0aGlzLl9zaG93KClcbiAgICB9XG5cbiAgICBwdWJsaWMgc2V0VmlzaWJsZSh2OiBib29sZWFuKSB7XG4gICAgICAgIHYgPyB0aGlzLl9zaG93KCkgOiB0aGlzLl9oaWRlKClcbiAgICAgICAgdGhpcy5fdmlzaWJsZSA9IHZcbiAgICB9XG5cbiAgICBwdWJsaWMgZ2V0VmlzaWJsZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Zpc2libGVcbiAgICB9XG5cbiAgICBwdWJsaWMgdG9nZ2xlKCkge1xuICAgICAgICB0aGlzLnNldFZpc2libGUoIXRoaXMuX3Zpc2libGUpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBfc2hvdygpIHtcbiAgICAgICAgdGhpcy5fcmVjb3Zlck1lc2hNb2RlKClcbiAgICAgICAgdGhpcy5fYXBwbHlNZXNoTW9kZSgpXG4gICAgICAgIHRoaXMuX3VwZGF0ZVZlcnRzKClcbiAgICAgICAgdGhpcy51bnNjaGVkdWxlKHRoaXMuY2hlY2tGcmFtZUNoYW5nZSlcbiAgICAgICAgdGhpcy5zY2hlZHVsZSh0aGlzLmNoZWNrRnJhbWVDaGFuZ2UpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBfaGlkZSgpIHtcbiAgICAgICAgdGhpcy5fcmVjb3Zlck1lc2hNb2RlKClcbiAgICAgICAgdGhpcy51bnNjaGVkdWxlKHRoaXMuY2hlY2tGcmFtZUNoYW5nZSlcbiAgICB9XG5cbiAgICAvLyDmm7TmlrBmcmFtZeeahOmhtueCueaVsOaNrlxuICAgIHByaXZhdGUgX3VwZGF0ZVZlcnRzKCkge1xuICAgICAgICBjb25zdCBzcHJpdGUgPSB0aGlzLnNwcml0ZVxuICAgICAgICBjb25zdCBmcmFtZSA9IHNwcml0ZT8uc3ByaXRlRnJhbWVcbiAgICAgICAgaWYgKCFmcmFtZSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgb1NpemUgPSBmcmFtZS5nZXRPcmlnaW5hbFNpemUoKVxuICAgICAgICBjb25zdCB0dyA9IGZyYW1lWydfdGV4dHVyZSddLndpZHRoXG4gICAgICAgIGNvbnN0IHRoID0gZnJhbWVbJ190ZXh0dXJlJ10uaGVpZ2h0XG5cbiAgICAgICAgbGV0IGRpc1UgPSB0aGlzLmJsdXIgLyB0d1xuICAgICAgICBsZXQgZGlzViA9IHRoaXMuYmx1ciAvIHRoXG5cbiAgICAgICAgbGV0IHJlY3QgPSBmcmFtZS5nZXRSZWN0KCksXG4gICAgICAgICAgICBvZmZzZXQgPSBmcmFtZS5nZXRPZmZzZXQoKSxcbiAgICAgICAgICAgIG93ID0gb1NpemUud2lkdGgsIG9oID0gb1NpemUuaGVpZ2h0LFxuICAgICAgICAgICAgcncgPSByZWN0LndpZHRoLCByaCA9IHJlY3QuaGVpZ2h0XG4gICAgICAgIGxldCB0cmltWCA9IG9mZnNldC54ICsgKG93IC0gcncpIC8gMjtcbiAgICAgICAgbGV0IHRyaW1ZID0gLW9mZnNldC55ICsgKG9oIC0gcmgpIC8gMjtcbiAgICAgICAgbGV0IGwgPSB0cmltWFxuICAgICAgICBsZXQgYiA9IHRyaW1ZXG4gICAgICAgIGxldCByID0gbCArIHJlY3Qud2lkdGhcbiAgICAgICAgbGV0IHQgPSBiICsgcmVjdC5oZWlnaHRcblxuICAgICAgICBsZXQgbHQgPSB7IHg6IGwsIHk6IHQgfVxuICAgICAgICBsZXQgcnQgPSB7IHg6IHIsIHk6IHQgfVxuICAgICAgICBsZXQgbGIgPSB7IHg6IGwsIHk6IGIgfVxuICAgICAgICBsZXQgcmIgPSB7IHg6IHIsIHk6IGIgfVxuXG4gICAgICAgIGNvbnN0IHBvcyA9IHsgbHQsIHJ0LCBsYiwgcmIgfVxuICAgICAgICBjb25zdCB1diA9IHRoaXMuX2dldFVWcyhmcmFtZSlcblxuICAgICAgICBjb25zdCB7IHZ0cywgdHJpYW5nbGVzIH0gPSB0aGlzLl91cGRhdGVTbGljZVZlcnRzKHBvcywgdXYsIGRpc1UsIGRpc1YsIGZyYW1lLmlzUm90YXRlZCgpKVxuXG4gICAgICAgIGNvbnN0IHggPSBbXSwgeSA9IFtdLCBudSA9IFtdLCBudiA9IFtdXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICB4LnB1c2godnRzW2ldLngpXG4gICAgICAgICAgICB5LnB1c2godnRzW2ldLnkpXG4gICAgICAgICAgICBudS5wdXNoKHZ0c1tpXS51KVxuICAgICAgICAgICAgbnYucHVzaCh2dHNbaV0udilcbiAgICAgICAgfVxuICAgICAgICBmcmFtZVsndmVydGljZXMnXSA9IHtcbiAgICAgICAgICAgIHgsIHksIG51LCBudiwgdHJpYW5nbGVzXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBtYXQgPSBzcHJpdGUuZ2V0TWF0ZXJpYWwoMClcbiAgICAgICAgbGV0IHJhbmdlWCA9IGNjLnYyKHV2Lmx0LngsIHV2LnJiLngpXG4gICAgICAgIGxldCByYW5nZVkgPSBjYy52Mih1di5yYi55LCB1di5sdC55KVxuXG4gICAgICAgIG1hdC5zZXRQcm9wZXJ0eSgncmFuZ2VYJywgcmFuZ2VYKVxuICAgICAgICBtYXQuc2V0UHJvcGVydHkoJ3JhbmdlWScsIHJhbmdlWSlcbiAgICAgICAgbWF0LnNldFByb3BlcnR5KCdvdXRsaW5lU2l6ZScsIGNjLnYyKGRpc1UsIGRpc1YpKVxuICAgICAgICBtYXQuc2V0UHJvcGVydHkoJ2NvbG9yJywgdGhpcy5jb2xvcilcbiAgICB9XG5cbiAgICAvLyDpnIDopoHlsIZzcHJpdGXnmoR0eXBl6K6+572u5Li6bWVzaFxuICAgIHByaXZhdGUgX2FwcGx5TWVzaE1vZGUoKSB7XG4gICAgICAgIGNvbnN0IHNwcml0ZSA9IHRoaXMuc3ByaXRlXG4gICAgICAgIGlmIChzcHJpdGUpIHtcbiAgICAgICAgICAgIHRoaXMuX3RyaW0gPSBzcHJpdGUudHJpbVxuICAgICAgICAgICAgdGhpcy5fcHJldmlvdXNlTWF0ZXJpYWwgPSBzcHJpdGUuZ2V0TWF0ZXJpYWwoMClcbiAgICAgICAgICAgIHRoaXMuX3R5cGUgPSBzcHJpdGUudHlwZVxuICAgICAgICAgICAgdGhpcy5fc2l6ZU1vZGUgPSBzcHJpdGUuc2l6ZU1vZGVcbiAgICAgICAgICAgIHNwcml0ZS50cmltID0gZmFsc2VcbiAgICAgICAgICAgIGxldCBtYXQgPSBhc3NldHNNZ3IuZ2V0TWF0ZXJpYWwoJ091dGxpbmVTcHJpdGUnKVxuICAgICAgICAgICAgbWF0ICYmIHNwcml0ZS5zZXRNYXRlcmlhbCgwLCBtYXQpXG4gICAgICAgICAgICBzcHJpdGUudHlwZSA9IGNjLlNwcml0ZS5UeXBlLk1FU0hcbiAgICAgICAgICAgIHNwcml0ZS5zaXplTW9kZSA9IGNjLlNwcml0ZS5TaXplTW9kZS5SQVdcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaBouWkjXNwcml0ZeeahOWOn+acieiuvue9rlxuICAgIHByaXZhdGUgX3JlY292ZXJNZXNoTW9kZSgpIHtcbiAgICAgICAgbGV0IGxhc3RTcHJpdGUgPSB0aGlzLnNwcml0ZVxuICAgICAgICBpZiAobGFzdFNwcml0ZSAmJiB0aGlzLl9wcmV2aW91c2VNYXRlcmlhbCkge1xuICAgICAgICAgICAgbGFzdFNwcml0ZS50cmltID0gdGhpcy5fdHJpbVxuICAgICAgICAgICAgbGFzdFNwcml0ZS5zZXRNYXRlcmlhbCgwLCB0aGlzLl9wcmV2aW91c2VNYXRlcmlhbClcbiAgICAgICAgICAgIGxhc3RTcHJpdGUudHlwZSA9IHRoaXMuX3R5cGVcbiAgICAgICAgICAgIGxhc3RTcHJpdGUuc2l6ZU1vZGUgPSB0aGlzLl9zaXplTW9kZVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuX2ZyYW1lID0gbnVsbFxuICAgICAgICB0aGlzLl90cmltID0gbnVsbFxuICAgICAgICB0aGlzLl9wcmV2aW91c2VNYXRlcmlhbCA9IG51bGxcbiAgICAgICAgdGhpcy5fc2l6ZU1vZGUgPSBudWxsXG4gICAgICAgIHRoaXMuX3R5cGUgPSBudWxsXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBfZ2V0VVZzKGZyYW1lOiBjYy5TcHJpdGVGcmFtZSkge1xuICAgICAgICAvL0B0cy1pZ25vcmVcbiAgICAgICAgbGV0IHV2ID0gZnJhbWUudXY7XG4gICAgICAgIGxldCBtaW5VID0gSW5maW5pdHksIG1pblYgPSBJbmZpbml0eSwgbWF4VSA9IC1JbmZpbml0eSwgbWF4ViA9IC1JbmZpbml0eTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA0OyBpKyspIHtcbiAgICAgICAgICAgIGxldCBzcmNPZmZzZXQgPSBpICogMjtcbiAgICAgICAgICAgIGxldCB4ID0gdXZbc3JjT2Zmc2V0XTtcbiAgICAgICAgICAgIGxldCB5ID0gdXZbc3JjT2Zmc2V0ICsgMV07XG4gICAgICAgICAgICBtaW5VID0gTWF0aC5taW4obWluVSwgeCk7XG4gICAgICAgICAgICBtaW5WID0gTWF0aC5taW4obWluViwgeSk7XG4gICAgICAgICAgICBtYXhVID0gTWF0aC5tYXgobWF4VSwgeCk7XG4gICAgICAgICAgICBtYXhWID0gTWF0aC5tYXgobWF4ViwgeSk7XG4gICAgICAgIH1cblxuICAgICAgICBsZXQgbHQgPSB7IHg6IG1pblUsIHk6IG1heFYgfVxuICAgICAgICBsZXQgcnQgPSB7IHg6IG1heFUsIHk6IG1heFYgfVxuICAgICAgICBsZXQgbGIgPSB7IHg6IG1pblUsIHk6IG1pblYgfVxuICAgICAgICBsZXQgcmIgPSB7IHg6IG1heFUsIHk6IG1pblYgfVxuICAgICAgICByZXR1cm4geyBsdCwgcnQsIGxiLCByYiB9XG4gICAgfVxuXG4gICAgcHJpdmF0ZSBfdXBkYXRlU2xpY2VWZXJ0cyhwb3M6IFBvcywgdXY6IFVWLCBkaXNVOiBudW1iZXIsIGRpc1Y6IG51bWJlciwgaXNSb3RhdGVkOiBib29sZWFuID0gZmFsc2UpIHtcbiAgICAgICAgY29uc3QgZXh0ZW5kRWRnZSA9IHRoaXMuYmx1clxuXG4gICAgICAgIGNvbnN0IG1vdmVMZWZ0ID0geyB4OiAtZXh0ZW5kRWRnZSwgeTogMCB9XG4gICAgICAgIGNvbnN0IG1vdmVSaWdodCA9IHsgeDogZXh0ZW5kRWRnZSwgeTogMCB9XG4gICAgICAgIGNvbnN0IG1vdmVVcCA9IHsgeDogMCwgeTogZXh0ZW5kRWRnZSB9XG4gICAgICAgIGNvbnN0IG1vdmVEb3duID0geyB4OiAwLCB5OiAtZXh0ZW5kRWRnZSB9XG5cbiAgICAgICAgbGV0IHA2ID0gcG9zLmx0XG4gICAgICAgIGxldCBwNyA9IHBvcy5ydFxuICAgICAgICBsZXQgcDEwID0gcG9zLmxiXG4gICAgICAgIGxldCBwMTEgPSBwb3MucmJcblxuICAgICAgICBsZXQgcDIgPSB0aGlzLl92ZXJ0QWRkKHBvcy5sdCwgbW92ZVVwKVxuICAgICAgICBsZXQgcDMgPSB0aGlzLl92ZXJ0QWRkKHBvcy5ydCwgbW92ZVVwKVxuICAgICAgICBsZXQgcDE0ID0gdGhpcy5fdmVydEFkZChwb3MubGIsIG1vdmVEb3duKVxuICAgICAgICBsZXQgcDE1ID0gdGhpcy5fdmVydEFkZChwb3MucmIsIG1vdmVEb3duKVxuXG4gICAgICAgIGxldCBwNSA9IHRoaXMuX3ZlcnRBZGQocG9zLmx0LCBtb3ZlTGVmdClcbiAgICAgICAgbGV0IHA5ID0gdGhpcy5fdmVydEFkZChwb3MubGIsIG1vdmVMZWZ0KVxuICAgICAgICBsZXQgcDggPSB0aGlzLl92ZXJ0QWRkKHBvcy5ydCwgbW92ZVJpZ2h0KVxuICAgICAgICBsZXQgcDEyID0gdGhpcy5fdmVydEFkZChwb3MucmIsIG1vdmVSaWdodClcblxuICAgICAgICBsZXQgcDEgPSB0aGlzLl92ZXJ0QWRkKHA1LCBtb3ZlVXApXG4gICAgICAgIGxldCBwNCA9IHRoaXMuX3ZlcnRBZGQocDgsIG1vdmVVcClcbiAgICAgICAgbGV0IHAxMyA9IHRoaXMuX3ZlcnRBZGQocDksIG1vdmVEb3duKVxuICAgICAgICBsZXQgcDE2ID0gdGhpcy5fdmVydEFkZChwMTIsIG1vdmVEb3duKVxuXG4gICAgICAgIGxldCBtYXRyaXggPSBbXG4gICAgICAgICAgICBbcDEsIHAyLCBwMywgcDRdLFxuICAgICAgICAgICAgW3A1LCBwNiwgcDcsIHA4XSxcbiAgICAgICAgICAgIFtwOSwgcDEwLCBwMTEsIHAxMl0sXG4gICAgICAgICAgICBbcDEzLCBwMTQsIHAxNSwgcDE2XSxcbiAgICAgICAgXVxuXG4gICAgICAgIGNvbnN0IHV2TW92ZUxlZnQgPSB7IHg6IC1kaXNVLCB5OiAwIH1cbiAgICAgICAgY29uc3QgdXZNb3ZlUmlnaHQgPSB7IHg6IGRpc1UsIHk6IDAgfVxuICAgICAgICBjb25zdCB1dk1vdmVVcCA9IHsgeDogMCwgeTogZGlzViB9XG4gICAgICAgIGNvbnN0IHV2TW92ZURvd24gPSB7IHg6IDAsIHk6IC1kaXNWIH1cblxuICAgICAgICBsZXQgdXY2ID0gdXYubHRcbiAgICAgICAgbGV0IHV2NyA9IHV2LnJ0XG4gICAgICAgIGxldCB1djEwID0gdXYubGJcbiAgICAgICAgbGV0IHV2MTEgPSB1di5yYlxuXG4gICAgICAgIGxldCB1djIgPSB0aGlzLl92ZXJ0QWRkKHV2Lmx0LCB1dk1vdmVVcClcbiAgICAgICAgbGV0IHV2MyA9IHRoaXMuX3ZlcnRBZGQodXYucnQsIHV2TW92ZVVwKVxuICAgICAgICBsZXQgdXYxNCA9IHRoaXMuX3ZlcnRBZGQodXYubGIsIHV2TW92ZURvd24pXG4gICAgICAgIGxldCB1djE1ID0gdGhpcy5fdmVydEFkZCh1di5yYiwgdXZNb3ZlRG93bilcblxuICAgICAgICBsZXQgdXY1ID0gdGhpcy5fdmVydEFkZCh1di5sdCwgdXZNb3ZlTGVmdClcbiAgICAgICAgbGV0IHV2OSA9IHRoaXMuX3ZlcnRBZGQodXYubGIsIHV2TW92ZUxlZnQpXG4gICAgICAgIGxldCB1djggPSB0aGlzLl92ZXJ0QWRkKHV2LnJ0LCB1dk1vdmVSaWdodClcbiAgICAgICAgbGV0IHV2MTIgPSB0aGlzLl92ZXJ0QWRkKHV2LnJiLCB1dk1vdmVSaWdodClcblxuICAgICAgICBsZXQgdXYxID0gdGhpcy5fdmVydEFkZCh1djUsIHV2TW92ZVVwKVxuICAgICAgICBsZXQgdXY0ID0gdGhpcy5fdmVydEFkZCh1djgsIHV2TW92ZVVwKVxuICAgICAgICBsZXQgdXYxMyA9IHRoaXMuX3ZlcnRBZGQodXY5LCB1dk1vdmVEb3duKVxuICAgICAgICBsZXQgdXYxNiA9IHRoaXMuX3ZlcnRBZGQodXYxMiwgdXZNb3ZlRG93bilcblxuICAgICAgICBsZXQgVVZtYXRyaXggPSBbXG4gICAgICAgICAgICBbdXYxLCB1djIsIHV2MywgdXY0XSxcbiAgICAgICAgICAgIFt1djUsIHV2NiwgdXY3LCB1djhdLFxuICAgICAgICAgICAgW3V2OSwgdXYxMCwgdXYxMSwgdXYxMl0sXG4gICAgICAgICAgICBbdXYxMywgdXYxNCwgdXYxNSwgdXYxNl0sXG4gICAgICAgIF1cbiAgICAgICAgaWYgKGlzUm90YXRlZCkge1xuICAgICAgICAgICAgVVZtYXRyaXggPSB0aGlzLl9yb3RhdGVNYXRyaXhSaWdodChVVm1hdHJpeClcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB2dHMgPSBbXVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1hdHJpeC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBtYXRyaXhbaV0ubGVuZ3RoOyBqKyspIHtcbiAgICAgICAgICAgICAgICBtYXRyaXhbaV1bal0udSA9IFVWbWF0cml4W2ldW2pdLnhcbiAgICAgICAgICAgICAgICBtYXRyaXhbaV1bal0udiA9IFVWbWF0cml4W2ldW2pdLnlcbiAgICAgICAgICAgICAgICB2dHMucHVzaChtYXRyaXhbaV1bal0pXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBjZW50ZXJRdWFkID0gdGhpcy5fY3JlYXRlUXVhZChbNSwgNiwgOSwgMTBdKVxuICAgICAgICBjb25zdCB0bFF1YWQgPSB0aGlzLl9jcmVhdGVRdWFkKFswLCAxLCA0LCA1XSlcbiAgICAgICAgY29uc3QgdG9wUXVhZCA9IHRoaXMuX2NyZWF0ZVF1YWQoWzEsIDIsIDUsIDZdKVxuICAgICAgICBjb25zdCB0clF1YWQgPSB0aGlzLl9jcmVhdGVRdWFkKFsyLCAzLCA2LCA3XSlcbiAgICAgICAgY29uc3QgcmlnaHRRdWFkID0gdGhpcy5fY3JlYXRlUXVhZChbMywgNywgMTEsIDE1XSlcbiAgICAgICAgY29uc3QgYnJRdWFkID0gdGhpcy5fY3JlYXRlUXVhZChbNywgNiwgMTUsIDE0XSlcbiAgICAgICAgY29uc3QgYm90dG9tUXVhZCA9IHRoaXMuX2NyZWF0ZVF1YWQoWzYsIDUsIDE0LCAxM10pXG4gICAgICAgIGNvbnN0IGJsUXVhZCA9IHRoaXMuX2NyZWF0ZVF1YWQoWzUsIDQsIDEzLCAxMl0pXG4gICAgICAgIGNvbnN0IGxlZnRRdWFkID0gdGhpcy5fY3JlYXRlUXVhZChbNCwgMCwgMTIsIDhdKVxuXG4gICAgICAgIGNvbnN0IHRyaWFuZ2xlcyA9IFsuLi5jZW50ZXJRdWFkLCAuLi50bFF1YWQsIC4uLnRvcFF1YWQsIC4uLnRyUXVhZCwgLi4ucmlnaHRRdWFkLCAuLi5iclF1YWQsIC4uLmJvdHRvbVF1YWQsIC4uLmJsUXVhZCwgLi4ubGVmdFF1YWRdXG5cbiAgICAgICAgcmV0dXJuIHsgdnRzLCB0cmlhbmdsZXMgfVxuICAgIH1cblxuICAgIHByaXZhdGUgX3ZlcnRBZGQodjE6IFZlcnQsIHYyOiBWZXJ0KTogVmVydCB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB4OiB2MS54ICsgdjIueCxcbiAgICAgICAgICAgIHk6IHYxLnkgKyB2Mi55XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcml2YXRlIF9jcmVhdGVRdWFkKGlkeHM6IG51bWJlcltdKTogbnVtYmVyW10ge1xuICAgICAgICBjb25zdCBpbmRpY2VzVGVtcCA9IFswLCAxLCAzLCAwLCAzLCAyXVxuICAgICAgICBjb25zdCB0cmlhbmdsZXMgPSBbXVxuICAgICAgICBmb3IgKGxldCBpbmRpY2Ugb2YgaW5kaWNlc1RlbXApIHtcbiAgICAgICAgICAgIGxldCBwID0gaWR4c1tpbmRpY2VdXG4gICAgICAgICAgICB0cmlhbmdsZXMucHVzaChwKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cmlhbmdsZXNcbiAgICB9XG5cbiAgICAvLyDmo4DmtYtzcHJpdGVGcmFtZeaYr+WQpuWPkeeUn+WPmOWMllxuICAgIHByaXZhdGUgX2ZyYW1lOiBjYy5TcHJpdGVGcmFtZSA9IG51bGxcbiAgICBwcml2YXRlIGNoZWNrRnJhbWVDaGFuZ2UoKTogdm9pZCB7XG4gICAgICAgIGlmICh0aGlzLnNwcml0ZSkge1xuICAgICAgICAgICAgaWYgKHRoaXMuX2ZyYW1lICE9PSB0aGlzLnNwcml0ZS5zcHJpdGVGcmFtZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX2ZyYW1lID0gdGhpcy5zcHJpdGUuc3ByaXRlRnJhbWVcbiAgICAgICAgICAgICAgICB0aGlzLl91cGRhdGVWZXJ0cygpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDpobrml7bpkojml4vovaznn6npmLVcbiAgICBwcml2YXRlIF9yb3RhdGVNYXRyaXhSaWdodDxUPihtYXRyaXg6IFRbXVtdKSB7XG4gICAgICAgIGNvbnN0IHJvd3MgPSBtYXRyaXgubGVuZ3RoXG4gICAgICAgIGNvbnN0IGNvbHMgPSBtYXRyaXhbMF0ubGVuZ3RoXG4gICAgICAgIGNvbnN0IHJvdGF0ZWRNYXRyaXggPSBbXVxuXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcm93czsgaSsrKSB7XG4gICAgICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IGNvbHM7IGorKykge1xuICAgICAgICAgICAgICAgIGlmICghcm90YXRlZE1hdHJpeFtqXSkgcm90YXRlZE1hdHJpeFtqXSA9IFtdO1xuICAgICAgICAgICAgICAgIHJvdGF0ZWRNYXRyaXhbal1bcm93cyAtIDEgLSBpXSA9IG1hdHJpeFtpXVtqXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiByb3RhdGVkTWF0cml4O1xuICAgIH1cbn1cblxudHlwZSBWZXJ0ID0ge1xuICAgIHg6IG51bWJlcixcbiAgICB5OiBudW1iZXIsXG4gICAgdT86IG51bWJlcixcbiAgICB2PzogbnVtYmVyLFxufVxuXG50eXBlIFBvcyA9IHtcbiAgICBsdDogVmVydCxcbiAgICBydDogVmVydCxcbiAgICBsYjogVmVydCxcbiAgICByYjogVmVydCxcbn1cblxudHlwZSBVViA9IHtcbiAgICBsdDogVmVydCxcbiAgICBydDogVmVydCxcbiAgICBsYjogVmVydCxcbiAgICByYjogVmVydCxcbn0iXX0=