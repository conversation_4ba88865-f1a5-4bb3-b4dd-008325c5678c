
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/CreateArmyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e0d9eVmTQZMRKPU8oc869ru', 'CreateArmyPnlCtrl');
// app/script/view/common/CreateArmyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CreateArmyPnlCtrl = /** @class */ (function (_super) {
    __extends(CreateArmyPnlCtrl, _super);
    function CreateArmyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/input_eb
        _this.tipLbl_ = null; // path://root/tip_l
        //@end
        _this.cb = null;
        return _this;
    }
    CreateArmyPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CreateArmyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CreateArmyPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        this.inputEb_.string = this.getArmyName();
        this.tipLbl_.setLocaleKey('ui.create_army_max_count_tip');
        if (!GameHelper_1.gameHpr.isNoviceMode) {
            GameHelper_1.gameHpr.setNoLongerTip('no_army', true);
        }
    };
    CreateArmyPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    CreateArmyPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    CreateArmyPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var name = this.inputEb_.string.trim();
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_army_name');
        }
        else if (ut.getStringLen(name) > 12 || GameHelper_1.gameHpr.getTextNewlineCount(name) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        NetHelper_1.netHelper.reqCheckArmyName({ name: name }).then(function (res) {
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            }
            else if (res.err === ECode_1.ecode.NAME_EXIST) {
                return ViewHelper_1.viewHelper.showAlert('toast.army_name_exist');
            }
            else if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            _this.cb && _this.cb(name);
            _this.hide();
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CreateArmyPnlCtrl.prototype.getArmyName = function () {
        var arr = [], defaultName = assetsMgr.lang('ui.default_army_name');
        GameHelper_1.gameHpr.player.getBaseArmys().forEach(function (m) {
            if (m.name.startsWith(defaultName)) {
                arr.push(Number(m.name.replace(defaultName, '')) || 1);
            }
        });
        var id = 1, len = arr.length;
        while (arr.has(id) && len > 0) {
            id += 1;
            len -= 1;
        }
        return defaultName + id;
    };
    CreateArmyPnlCtrl = __decorate([
        ccclass
    ], CreateArmyPnlCtrl);
    return CreateArmyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CreateArmyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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