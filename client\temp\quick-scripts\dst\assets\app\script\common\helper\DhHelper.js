
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/DhHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2263eWWtV1Nyoxfam61qDAZ', 'DhHelper');
// app/script/common/helper/DhHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DH_LOG_EVENT = exports.dhHelper = void 0;
var version_1 = require("../../../../scene/version");
var CryptoHelper_1 = require("../crypto/CryptoHelper");
var LocalConfig_1 = require("../LocalConfig");
var GameHelper_1 = require("./GameHelper");
var JsbHelper_1 = require("./JsbHelper");
var ReddotHelper_1 = require("./ReddotHelper");
var GAME_CD = '3013';
var SALT = 'fbiubi9gewaga=niu1n3091mlnoahgawng';
// 卓航 上报
var DhHelper = /** @class */ (function () {
    function DhHelper() {
        this.OPEN = true;
    }
    DhHelper.prototype.init = function () {
        this.OPEN = GameHelper_1.gameHpr.isGLobal();
    };
    DhHelper.prototype.getHDSid = function () {
        var serverArea = GameHelper_1.gameHpr.getServerArea();
        if (serverArea === 'hk') {
            return 1;
        }
        else if (serverArea === 'us') {
            return 2;
        }
        return 0;
    };
    DhHelper.prototype.report = function (eventType, event_info, create_ts) {
        return __awaiter(this, void 0, void 0, function () {
            var device_info, di, _a, err, token, user, user_info, area, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.OPEN) {
                            return [2 /*return*/];
                        }
                        device_info = {
                            'adid': '',
                            'idfv': '',
                            'imei': '',
                            'android_id': '',
                            'appsflyer_id': '',
                            'device_token': '',
                            'mac_address': '',
                            'device_model': '',
                            'device_name': '',
                            'os_version': '',
                            'network_type': '',
                            // 'oaid':'',///OAID 广告标识符 (Open Advertising Identifier)，仅安卓
                            'language': mc.lang,
                            'app_version': version_1.default.VERSION,
                        };
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getDeviceInfo()];
                    case 1:
                        di = _b.sent();
                        Object.assign(device_info, di);
                        if (!device_info.network_type) {
                            device_info.network_type = cc.sys.getNetworkType();
                        }
                        device_info.network_type = String(device_info.network_type);
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getFcmToken()];
                    case 2:
                        _a = _b.sent(), err = _a.err, token = _a.token;
                        device_info.device_token = token;
                        user = GameHelper_1.gameHpr.user;
                        user_info = {
                            bundle_id: GameHelper_1.gameHpr.getBundleId(),
                            server_id: this.getHDSid(),
                            user_id: user.getUid(),
                            user_name: user.getNickname(),
                            account: user.getUid(),
                            platform: GameHelper_1.gameHpr.getRunPlatform(),
                            session_id: user.getSessionId(),
                        };
                        area = GameHelper_1.gameHpr.getServerArea();
                        if (area !== 'hk') {
                            user_info.account = area + '_' + user.getUid();
                        }
                        data = {
                            event_code: eventType.code,
                            event_type: eventType.type,
                            event_name: eventType.name,
                            game_cd: GAME_CD,
                            create_ts: String(create_ts || GameHelper_1.gameHpr.getServerNowTime() * 1000000),
                            event_value: JSON.stringify({ device_info: device_info, user_info: user_info, event_info: event_info })
                        };
                        this.request(data);
                        return [2 /*return*/];
                }
            });
        });
    };
    DhHelper.prototype.request = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        cc.log('1 dhlog----------', data.event_name, data);
                        var url = 'https://ulog.dhgames.com:8180/external/push_log_add_ip';
                        if (!GameHelper_1.gameHpr.isRelease) {
                            url = 'https://ulog-test.dhgames.cn:8181/external/push_log_add_ip';
                        }
                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', url, true);
                        var now = GameHelper_1.gameHpr.getServerNowTime();
                        var date = String(Math.floor(now / 1000));
                        var auth = CryptoHelper_1.cryptoHelper.md5(SALT + date).toLowerCase();
                        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                        xhr.setRequestHeader('X-Ulog-Date', date);
                        xhr.setRequestHeader('Authorization', auth);
                        xhr.timeout = 10000;
                        xhr.ontimeout = function (e) {
                            cc.error('dh http timeout', url);
                            resolve(null);
                        };
                        xhr.onerror = function (e) {
                            cc.error('dh http disconnect error' + e, url);
                            resolve(null);
                        };
                        xhr.onreadystatechange = function () {
                            var response = xhr.responseText;
                            if (xhr.readyState === 4) {
                                if (xhr.status === 200) {
                                    var respData = JSON.parse(response);
                                    if ((respData === null || respData === void 0 ? void 0 : respData.error_code) != 0) {
                                        cc.error("dhlog error", respData);
                                    }
                                    cc.log('2 dhlog----------', respData);
                                    resolve(respData);
                                }
                                else {
                                    resolve(null);
                                }
                            }
                        };
                        // cc.log({ 'data': JSON.stringify(data) })
                        var info = GameHelper_1.gameHpr.encodeURIObj({ 'data': JSON.stringify(data) });
                        // cc.log(info)
                        xhr.send(info);
                    })];
            });
        });
    };
    // 上报广告
    DhHelper.prototype.reportAd = function (data) {
        if (!this.OPEN) {
            return;
        }
        var defaultData = {
            auctionId: '',
            adUnit: '',
            country: GameHelper_1.gameHpr.getServerArea(),
            ab: '',
            segmentName: '',
            placement: '',
            adNetwork: '',
            instanceName: '',
            instanceId: '',
            revenue: data.pay_amount,
            precision: '',
            lifetimeRevenue: data.lifetimePay_amount,
            encryptedCPM: '',
        };
        this.report(exports.DH_LOG_EVENT.AD_IRONSOURCE, Object.assign(defaultData, data));
    };
    // 客服 检查未查看消息数
    DhHelper.prototype.checkCSUnreadCommentNum = function () {
        return __awaiter(this, void 0, void 0, function () {
            var url, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.OPEN || !LocalConfig_1.localConfig.RELEASE) {
                            return [2 /*return*/, ReddotHelper_1.reddotHelper.set('dh_cs_unread', false)];
                        }
                        url = 'https://aics-cli.dev-dh.com/dh_aics_gate/work/unread_comment_num';
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({
                                url: url,
                                data: {
                                    game: 'NTA',
                                    bundle_id: GameHelper_1.gameHpr.getBundleId() || 'twgame.global.acers',
                                    sid: this.getHDSid() + '',
                                    role_id: GameHelper_1.gameHpr.getUid(),
                                }
                            })];
                    case 1:
                        data = _a.sent();
                        ReddotHelper_1.reddotHelper.set('dh_cs_unread', !!(data === null || data === void 0 ? void 0 : data.unread_count));
                        return [2 /*return*/];
                }
            });
        });
    };
    return DhHelper;
}());
exports.dhHelper = new DhHelper();
if (cc.sys.isBrowser) {
    window['dhHelper'] = exports.dhHelper;
}
exports.DH_LOG_EVENT = {
    // event
    AD_IRONSOURCE: {
        code: '2100210025',
        type: 'event',
        name: 'ad_ironsource',
    },
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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