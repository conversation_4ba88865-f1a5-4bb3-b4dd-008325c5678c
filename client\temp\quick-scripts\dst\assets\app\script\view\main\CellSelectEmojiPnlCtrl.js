
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CellSelectEmojiPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '260b4hCWB1AAbECtkx0x4ZL', 'CellSelectEmojiPnlCtrl');
// app/script/view/main/CellSelectEmojiPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CellSelectEmojiPnlCtrl = /** @class */ (function (_super) {
    __extends(CellSelectEmojiPnlCtrl, _super);
    function CellSelectEmojiPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.banNode_ = null; // path://root/ban_n
        _this.onlyAlliNode_ = null; // path://root/only_alli_n
        //@end
        _this.PKEY_TAB = 'CELL_SELECT_EMOJI_TAB';
        _this.EMOJI_SIZE = cc.size(64, 64);
        _this.user = null;
        _this.isOwn = false;
        _this.cb = null;
        return _this;
    }
    CellSelectEmojiPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CellSelectEmojiPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    CellSelectEmojiPnlCtrl.prototype.onEnter = function (isOwn, cb) {
        this.isOwn = isOwn;
        this.cb = cb;
        var tab = this.user.getTempPreferenceMap(this.PKEY_TAB) || 2;
        if (!this.isOwn && tab !== 2) {
            tab = 2;
            this.user.setTempPreferenceData(this.PKEY_TAB, tab);
        }
        this.tabsTc_.Tabs(tab);
        this.updateBanTime();
    };
    CellSelectEmojiPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    CellSelectEmojiPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    CellSelectEmojiPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        this.cb && this.cb(event.target.Data);
        this.cb = null;
        this.hide();
    };
    // path://root/tabs_tc_tce
    CellSelectEmojiPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        if (!this.isOwn && type === 1) {
            this.tabsTc_.Swih(2);
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_MY_CELL_SEND_EMOJI);
        }
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        this.updateEmojis(type);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CellSelectEmojiPnlCtrl.prototype.updateBanTime = function () {
        var _this = this;
        var time = GameHelper_1.gameHpr.ground.checkRestSurplusTime();
        if (this.banNode_.active = time > 0) {
            this.listSv_.node.opacity = 150;
            this.banNode_.Child('time/val', cc.LabelTimer).run(time * 0.001, function () { return _this.isValid && _this.updateBanTime(); });
        }
        else {
            this.listSv_.node.opacity = 255;
        }
    };
    CellSelectEmojiPnlCtrl.prototype.updateEmojis = function (type) {
        var _this = this;
        var ids = this.getCanUseEmojis(type);
        this.onlyAlliNode_.active = type === 2;
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Child('empty').active = type !== 0 && ids.length === 0;
        this.listSv_.List(ids.length, function (it, i) {
            var id = it.Data = ids[i];
            var val = it.Child('val');
            ResHelper_1.resHelper.loadEmojiIcon(id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE, undefined, 1.1); });
        });
    };
    CellSelectEmojiPnlCtrl.prototype.getCanUseEmojis = function (type) {
        type = type + 1;
        var datas = [], unlockMap = {};
        // 这里加上购买的动态表情
        this.user.getUnlockChatEmojiIds().forEach(function (id) { return unlockMap[id] = true; });
        //
        assetsMgr.getJson('chatEmoji').datas.forEach(function (m) {
            if (m.type === type && m.use_map === 1 && (m.cond === 0 || unlockMap[m.id])) {
                datas.push(m.id);
            }
        });
        return datas;
    };
    CellSelectEmojiPnlCtrl = __decorate([
        ccclass
    ], CellSelectEmojiPnlCtrl);
    return CellSelectEmojiPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CellSelectEmojiPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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