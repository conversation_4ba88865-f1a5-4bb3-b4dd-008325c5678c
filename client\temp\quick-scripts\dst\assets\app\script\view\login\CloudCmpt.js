
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/CloudCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9fb78IInVJKIY26yNAkvFaU', 'CloudCmpt');
// app/script/view/login/CloudCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var CloudCmpt = /** @class */ (function (_super) {
    __extends(CloudCmpt, _super);
    function CloudCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.speed = 50;
        _this.isRandInitPos = true;
        _this.minX = 0;
        _this.initX = 0;
        return _this;
    }
    CloudCmpt.prototype.onLoad = function () {
        this.minX = -(this.node.width + 10);
        this.initX = Math.ceil(cc.winSize.width / 4) + 10;
        this.node.x = this.isRandInitPos ? ut.randomRange(this.minX, this.initX) : this.initX;
    };
    CloudCmpt.prototype.update = function (dt) {
        this.node.x -= this.speed * dt;
        if (this.node.x <= this.minX) {
            this.node.x = this.initX;
        }
    };
    __decorate([
        property
    ], CloudCmpt.prototype, "speed", void 0);
    __decorate([
        property
    ], CloudCmpt.prototype, "isRandInitPos", void 0);
    CloudCmpt = __decorate([
        ccclass
    ], CloudCmpt);
    return CloudCmpt;
}(cc.Component));
exports.default = CloudCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGxvZ2luXFxDbG91ZENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUF3QixFQUFFLENBQUMsVUFBVSxFQUFuQyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQWtCLENBQUM7QUFHNUM7SUFBdUMsNkJBQVk7SUFBbkQ7UUFBQSxxRUFzQkM7UUFuQlcsV0FBSyxHQUFXLEVBQUUsQ0FBQTtRQUVsQixtQkFBYSxHQUFZLElBQUksQ0FBQTtRQUU3QixVQUFJLEdBQVcsQ0FBQyxDQUFBO1FBQ2hCLFdBQUssR0FBVyxDQUFDLENBQUE7O0lBYzdCLENBQUM7SUFaRywwQkFBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLElBQUksR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFDLENBQUE7UUFDbkMsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxPQUFPLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtRQUNqRCxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFBO0lBQ3pGLENBQUM7SUFFRCwwQkFBTSxHQUFOLFVBQU8sRUFBVTtRQUNiLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLElBQUksQ0FBQyxLQUFLLEdBQUcsRUFBRSxDQUFBO1FBQzlCLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLElBQUksSUFBSSxDQUFDLElBQUksRUFBRTtZQUMxQixJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO1NBQzNCO0lBQ0wsQ0FBQztJQWxCRDtRQURDLFFBQVE7NENBQ2lCO0lBRTFCO1FBREMsUUFBUTtvREFDNEI7SUFMcEIsU0FBUztRQUQ3QixPQUFPO09BQ2EsU0FBUyxDQXNCN0I7SUFBRCxnQkFBQztDQXRCRCxBQXNCQyxDQXRCc0MsRUFBRSxDQUFDLFNBQVMsR0FzQmxEO2tCQXRCb0IsU0FBUyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHkgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG5AY2NjbGFzc1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBDbG91ZENtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIEBwcm9wZXJ0eVxyXG4gICAgcHJpdmF0ZSBzcGVlZDogbnVtYmVyID0gNTBcclxuICAgIEBwcm9wZXJ0eVxyXG4gICAgcHJpdmF0ZSBpc1JhbmRJbml0UG9zOiBib29sZWFuID0gdHJ1ZVxyXG5cclxuICAgIHByaXZhdGUgbWluWDogbnVtYmVyID0gMFxyXG4gICAgcHJpdmF0ZSBpbml0WDogbnVtYmVyID0gMFxyXG5cclxuICAgIG9uTG9hZCgpIHtcclxuICAgICAgICB0aGlzLm1pblggPSAtKHRoaXMubm9kZS53aWR0aCArIDEwKVxyXG4gICAgICAgIHRoaXMuaW5pdFggPSBNYXRoLmNlaWwoY2Mud2luU2l6ZS53aWR0aCAvIDQpICsgMTBcclxuICAgICAgICB0aGlzLm5vZGUueCA9IHRoaXMuaXNSYW5kSW5pdFBvcyA/IHV0LnJhbmRvbVJhbmdlKHRoaXMubWluWCwgdGhpcy5pbml0WCkgOiB0aGlzLmluaXRYXHJcbiAgICB9XHJcblxyXG4gICAgdXBkYXRlKGR0OiBudW1iZXIpIHtcclxuICAgICAgICB0aGlzLm5vZGUueCAtPSB0aGlzLnNwZWVkICogZHRcclxuICAgICAgICBpZiAodGhpcy5ub2RlLnggPD0gdGhpcy5taW5YKSB7XHJcbiAgICAgICAgICAgIHRoaXMubm9kZS54ID0gdGhpcy5pbml0WFxyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdfQ==