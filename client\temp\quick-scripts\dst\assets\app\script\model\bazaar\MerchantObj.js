
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/bazaar/MerchantObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6d952FA8lJGU6g8iLU0GOtt', 'MerchantObj');
// app/script/model/bazaar/MerchantObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
// 一个商人
var MerchantObj = /** @class */ (function () {
    function MerchantObj() {
        this.state = 0;
    }
    MerchantObj.prototype.fromSvr = function (data) {
        this.state = data.state || Enums_1.MerchantState.NONE;
        return this;
    };
    return MerchantObj;
}());
exports.default = MerchantObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiYXphYXJcXE1lcmNoYW50T2JqLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEscURBQTJEO0FBRTNELE9BQU87QUFDUDtJQUFBO1FBRVcsVUFBSyxHQUFrQixDQUFDLENBQUE7SUFNbkMsQ0FBQztJQUpVLDZCQUFPLEdBQWQsVUFBZSxJQUFTO1FBQ3BCLElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssSUFBSSxxQkFBYSxDQUFDLElBQUksQ0FBQTtRQUM3QyxPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFDTCxrQkFBQztBQUFELENBUkEsQUFRQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWVyY2hhbnRTdGF0ZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIlxyXG5cclxuLy8g5LiA5Liq5ZWG5Lq6XHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE1lcmNoYW50T2JqIHtcclxuXHJcbiAgICBwdWJsaWMgc3RhdGU6IE1lcmNoYW50U3RhdGUgPSAwXHJcblxyXG4gICAgcHVibGljIGZyb21TdnIoZGF0YTogYW55KSB7XHJcbiAgICAgICAgdGhpcy5zdGF0ZSA9IGRhdGEuc3RhdGUgfHwgTWVyY2hhbnRTdGF0ZS5OT05FXHJcbiAgICAgICAgcmV0dXJuIHRoaXNcclxuICAgIH1cclxufSJdfQ==