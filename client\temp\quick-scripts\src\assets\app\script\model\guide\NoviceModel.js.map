{"version": 3, "sources": ["assets\\app\\script\\model\\guide\\NoviceModel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAA2D;AAC3D,2DAA0J;AAE1J,qDAAyH;AACzH,0DAAoD;AACpD,6DAAwD;AACxD,2DAAyD;AAMzD,mDAA6C;AAE7C,+CAAyC;AACzC,iDAA2C;AAC3C,6CAAuC;AACvC,iDAA2C;AAC3C,iDAA2C;AAC3C,+CAAmG;AAGnG,iDAA2C;AAE3C,2EAAyE;AACzE,6DAA2D;AAE3D;;GAEG;AAEH;IAAyC,+BAAY;IAArD;QAAA,qEA06BC;QAx6BW,UAAI,GAAc,IAAI,CAAA;QACtB,gBAAU,GAAoB,IAAI,CAAA;QAClC,YAAM,GAAsB,IAAI,CAAA;QAEhC,aAAO,GAAW,CAAC,CAAA,CAAC,SAAS;QAC7B,cAAQ,GAAW,CAAC,CAAA,CAAC,OAAO;QAC5B,iBAAW,GAAW,CAAC,CAAA;QACvB,kBAAY,GAAW,CAAC,CAAA,CAAC,SAAS;QAClC,mBAAa,GAAW,CAAC,CAAA,CAAC,MAAM;QAChC,YAAM,GAAe,IAAI,CAAA,CAAC,MAAM;QAChC,cAAQ,GAAiB,EAAE,CAAA,CAAC,SAAS;QACrC,kBAAY,GAAmB,EAAE,CAAA,CAAC,QAAQ;QAC1C,YAAM,GAAe,EAAE,CAAA,CAAC,MAAM;QAC9B,cAAQ,GAAiB,EAAE,CAAA,CAAC,MAAM;QAClC,mBAAa,GAA0C,EAAE,CAAA,CAAC,QAAQ;QAClE,qBAAe,GAAmC,EAAE,CAAA,CAAC,UAAU;QAC/D,oBAAc,GAAiC,EAAE,CAAA,CAAC,UAAU;QAC5D,oBAAc,GAAiC,EAAE,CAAA,CAAC,QAAQ;QAE1D,oBAAc,GAAkC,EAAE,CAAA,CAAC,SAAS;QAC5D,uBAAiB,GAA+B,EAAE,CAAA,CAAC,UAAU;QAC7D,qBAAe,GAAa,EAAE,CAAA,CAAC,WAAW;QAC1C,sBAAgB,GAAe,IAAI,CAAA,CAAC,iBAAiB;QACrD,2BAAqB,GAAY,KAAK,CAAA;QAEtC,gBAAU,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC7B,kBAAY,GAAY,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA,CAAC,iBAAiB;QACvD,oBAAc,GAAW,CAAC,CAAA,CAAC,iBAAiB;QAC5C,gBAAU,GAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAA,CAAC,MAAM;QAC7D,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,WAAW;QACrC,cAAQ,GAAe,IAAI,CAAA,CAAC,SAAS;QACrC,2BAAqB,GAAY,KAAK,CAAA,CAAC,YAAY;QAEnD,0BAAoB,GAAW,CAAC,CAAC,CAAA;QACjC,+BAAyB,GAAW,CAAC,CAAA;QAErC,oBAAc,GAA8B,EAAE,CAAA,CAAA,cAAc;QAC5D,oBAAc,GAAgC,EAAE,CAAA,CAAA,UAAU;QAC1D,YAAM,GAAc,IAAI,CAAA;QAExB,oBAAc,GAA8B,EAAE,CAAA,CAAA,qBAAqB;QAEnE,oBAAc,GAA2B,EAAE,CAAA,CAAA,QAAQ;QAEpD,uBAAiB,GAAY,KAAK,CAAA,CAAA,UAAU;;IA43BvD,CAAC;IA13BU,8BAAQ,GAAf;QACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAEM,2BAAK,GAAZ;IAEA,CAAC;IAEM,iCAAW,GAAlB,cAAuB,OAAO,QAAQ,CAAA,CAAC,CAAC;IAEjC,+BAAS,GAAhB,UAAiB,GAAW,EAAE,KAAa;QACvC,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,oBAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAC3E,IAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QAC1E,oBAAO,CAAC,cAAc,CAAC,KAAK,EAAE;YAC1B,QAAQ,UAAA;YACR,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACjC,aAAa,EAAE,KAAK;SACvB,CAAC,CAAA;IACN,CAAC;IAEY,0BAAI,GAAjB;;;;;;;;wBACI,IAAI,CAAC,OAAO,GAAG,uBAAuB,CAAA,CAAC,CAAA;wBACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;wBAC1B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAA;wBACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;wBACzB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;wBACvB,IAAI,CAAC,MAAM,GAAG,IAAI,oBAAU,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAA;wBAC1C,KAAK,GAAG,CAAC,CAAA;wBACC,qBAAM,SAAS,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC;4BAC/E,QAAQ;0BADuE;;wBAAzE,OAAO,GAAG,SAA+D;wBAC/E,QAAQ;wBACR,qBAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAA;;wBADrC,QAAQ;wBACR,SAAqC,CAAA;wBACrC,qBAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAA;;wBAA/B,SAA+B,CAAA;wBAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAS,EAAE,CAAA;wBACvB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAA;wBAC1D,YAAY;wBACZ,oBAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;wBACtC,UAAU;wBACV,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;wBAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACjC,OAAO;wBACP,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;wBAClB,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAA;wBACxB,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,oBAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;wBAC5E,oBAAO,CAAC,cAAc,CAAC,MAAM,EAAE;4BAC3B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,oCAAqB;4BAC1G,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;4BAChC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;yBACrC,CAAC,CAAA;wBAEF,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;4BACT,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;4BACnB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,oBAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;4BACrE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;4BAChC,oBAAO,CAAC,cAAc,CAAC,KAAK,EAAE;gCAC1B,QAAQ,UAAA;gCACR,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gCACjC,aAAa,EAAE,CAAC;6BACnB,CAAC,CAAA;yBACL;wBAED,OAAO;wBACP,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;wBACZ,KAAK,GAAU,IAAI,CAAC,KAAK,CAAA;wBACzB,KAAK,GAAiB,EAAE,CAAA;wBAC9B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;wBACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;wBACxB,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;4BACpC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;4BACf,IAAI,GAAG,IAAI,oBAAU,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;4BAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;4BACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACxB,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE,EAAE,MAAM;gCACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;6BACnB;4BACD,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,UAAU;gCACxB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;6BAC7C;yBACJ;wBACD,aAAa;wBACb,IAAI,IAAI,CAAC,QAAQ,EAAE;4BACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;yBACrD;wBACD,gBAAgB;wBAChB,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;4BACX,OAAO;4BACP,KAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAA;4BAC/B,aAAa;4BACb,qBAAS,CAAC,2BAA2B,OAAC,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,0CAAE,KAAK,CAAC,CAAA;wBAC7E,CAAC,CAAC,CAAA;wBACF,YAAY;wBACZ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAS,CAAC,YAAY,CAAC,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAA;wBAEpE,OAAO,SAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,0CAAE,KAAK,CAAA;wBAC7D,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAA;wBACrG,IAAI,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;4BAC1C,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,UAAC,CAAC,EAAE,GAAG,IAAK,OAAA,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,IAAI,EAAlC,CAAkC,EAAC;4BAChE,IAAI,CAAC,qBAAqB,EAAE,CAAA;yBAC/B;6BAAM;4BACH,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC,UAAC,CAAC,EAAE,GAAG;;gCACpB,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,EAAE,MAAK,wBAAa,EAAE;oCAC9D,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;iCACzC;4BACL,CAAC,EAAC;yBACL;wBACD,OAAO;wBACP,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAA;wBAC9B,0CAA0C;wBAC1C,oBAAO,CAAC,GAAG,CAAC,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;wBACtE,oBAAoB;wBACpB,IAAI,CAAC,UAAU,EAAE,CAAA;wBACjB,IAAI,CAAC,cAAc,EAAE,CAAA;wBACrB,IAAI,CAAC,gBAAgB,EAAE,CAAA;wBACvB,IAAI,CAAC,gBAAgB,EAAE,CAAA;wBACvB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA;wBACjB,qCAAiB,CAAC,IAAI,EAAE,CAAA;;;;;KAC3B;IAED,YAAY;IACE,yCAAmB,GAAjC,UAAkC,KAAa;;;;;;wBAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;wBACpB,QAAQ,GAAG,0BAA0B,GAAG,KAAK,CAAA;wBACjC,qBAAM,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,EAAA;;wBAA/D,SAAS,GAAG,SAAmD;wBACnE,IAAI,SAAS,EAAE;4BACX,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gCACxC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gCACxB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;gCACf,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCACrC,IAAI,CAAC,IAAI,EAAE;oCACP,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;iCACzC;gCACG,IAAI,GAAG,EAAE,cAAc,EAAE,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAA;gCACjH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;6BAClB;yBACJ;wBACD,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,CAAA;;;;;KACnD;IAED,eAAe;IACD,mCAAa,GAA3B,UAA4B,KAAa;;;;;;wBACrC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;wBAClB,QAAQ,GAAG,gBAAgB,GAAG,KAAK,CAAA;wBACvB,qBAAM,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,EAAA;;wBAA/D,SAAS,GAAG,SAAmD;wBACrE,IAAI,SAAS,EAAE;4BACX,KAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE;gCACxB,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gCAC9B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;6BAClC;yBACJ;wBACD,SAAS,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,CAAA;;;;;KACnD;IAEO,gCAAU,GAAlB;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAtB,CAAsB,CAAC,CAAA;IAC1E,CAAC;IAEO,oCAAc,GAAtB;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAA;QACvB,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QAC1C,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAA;SAC/C;IACL,CAAC;IAEO,sCAAgB,GAAxB;QACI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC9D,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;YACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,qBAAW,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;SAC1F;IACL,CAAC;IAEO,sCAAgB,GAAxB;QAAA,iBAGC;QAFG,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;QACxB,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,mBAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAA/D,CAA+D,CAAC,CAAA;IAC/G,CAAC;IAED,YAAY;IACL,sCAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;IACnE,CAAC;IAEM,wCAAkB,GAAzB,UAA0B,GAAW;QACjC,OAAO,KAAK,CAAA;IAChB,CAAC;IAEY,kCAAY,GAAzB;;YAA8B,sBAAO,EAAE,EAAA;;KAAE;IAC5B,uCAAiB,GAA9B,UAA+B,GAAW,EAAE,IAAY;;YAAI,sBAAO,EAAE,EAAA;;KAAE;IAC1D,wCAAkB,GAA/B;;YAAoC,sBAAO,EAAE,EAAA;;KAAE;IAE/C,WAAW;IACJ,oCAAc,GAArB,cAA0B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;IAC5C,qCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IAC9C,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAEvD,WAAW;IACJ,qCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,WAAW,KAAK,mBAAW,CAAC,cAAc,CAAA,CAAC,CAAC;IAE5E,+BAAS,GAAhB,cAAiC,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;IAC9C,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAA,CAAC,CAAC;IACtD,mCAAa,GAApB,cAAyB,OAAO,CAAC,CAAA,CAAC,CAAC,EAAC,UAAU;IACvC,qCAAe,GAAtB,cAA2B,OAAO,IAAI,CAAA,CAAC,CAAC;IACjC,gCAAU,GAAjB,cAAsB,OAAO,KAAK,CAAA,CAAC,CAAC;IAEpC,YAAY;IACL,mCAAa,GAApB,cAAyB,OAAO,CAAC,CAAA,CAAC,CAAC;IAEnC,WAAW;IACJ,8CAAwB,GAA/B,UAAgC,EAAU;;QACtC,OAAO,aAAA,IAAI,CAAC,YAAY,0CAAE,YAAY,0CAAG,EAAE,MAAK,EAAE,CAAA;IACtD,CAAC;IAED,WAAW;IACJ,qCAAe,GAAtB,UAAuB,EAAU;;QAC7B,OAAO,aAAA,IAAI,CAAC,YAAY,0CAAE,WAAW,0CAAG,EAAE,MAAK,CAAC,CAAA;IACpD,CAAC;IAED,OAAO;IACA,mCAAa,GAApB,UAAqB,IAAY;QAC7B,OAAO,IAAI,CAAA;IACf,CAAC;IAED,SAAS;IACF,oCAAc,GAArB;QACI,IAAM,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;QAC5B,IAAI,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAA;YACzB,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;SACjC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE,EAAE,UAAU;YAC9C,uBAAU,CAAC,IAAI,CAAC,qBAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,qBAAS,CAAC,QAAQ,EAAE,0BAAe,EAAE,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,gBAAgB,CAAC,CAAC,CAAA;SAC/L;aAAM;YACH,uBAAU,CAAC,cAAc,CAAC,0BAAe,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,qBAAS,CAAC,QAAQ,CAAC,CAAA;SACtH;IACL,CAAC;IACD,SAAS;IACF,oCAAc,GAArB;QACI,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,uBAAU,CAAC,SAAS,CAAA;QAC7F,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,CAAA;QACtD,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAC/E,CAAC;IAED,SAAS;IACF,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IAC7C,WAAW;IACJ,uCAAiB,GAAxB,UAAyB,KAAc,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA,CAAC,CAAC;IAClG,uCAAiB,GAAxB,UAAyB,KAAa;;QAClC,IAAI,OAAA,IAAI,CAAC,QAAQ,0CAAE,KAAK,MAAK,KAAK,EAAE;YAChC,OAAO,IAAI,CAAC,QAAQ,CAAA;SACvB;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,sBAAsB;IACf,sCAAgB,GAAvB;QACI,IAAM,IAAI,GAAG,qBAAS,CAAC,QAAQ,CAAA;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;IACnD,CAAC;IAED,SAAS;IACF,mCAAa,GAApB,cAAwD,OAAO,EAAE,CAAA,CAAC,CAAC;IAC5D,oCAAc,GAArB,UAAsB,KAAa,IAAgB,OAAO,IAAI,CAAA,CAAC,CAAC;IAEhE,SAAS;IACI,wCAAkB,GAA/B,UAAgC,KAAa;;;;;;KAE5C;IAED,WAAW;IACJ,sCAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAChD,gDAA0B,GAAjC,cAAoD,OAAO,EAAE,CAAA,CAAC,CAAC;IAE/D,WAAW;IACJ,wCAAkB,GAAzB,cAA8B,OAAO,IAAI,CAAC,eAAe,CAAA,CAAC,CAAC;IAC3D,SAAS;IACF,0CAAoB,GAA3B;QACI,IAAI,GAAG,GAAG,KAAK,CAAA;QACf,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;gBAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;gBAC9B,GAAG,GAAG,IAAI,CAAA;aACb;SACJ;QACD,IAAI,GAAG,EAAE;YACL,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,yBAAyB,CAAC,CAAA;SACjD;IACL,CAAC;IAED,WAAW;IACJ,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IACzD,SAAS;IACF,uCAAiB,GAAxB,cAA6B,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;IACzD,WAAW;IACJ,uCAAiB,GAAxB,UAAyB,KAAa,EAAE,IAAY;QAChD,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,MAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;IAC/C,CAAC;IACD,YAAY;IACL,0CAAoB,GAA3B,cAAgC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC;IAEhF,SAAS;IACF,4CAAsB,GAA7B,UAA8B,IAAY,IAAI,OAAO,EAAE,CAAA,CAAC,CAAC;IACzD,WAAW;IACJ,qCAAe,GAAtB,UAAuB,GAAW,IAAkB,OAAO,EAAS,CAAA,CAAC,CAAC;IACtE,SAAS;IACF,yCAAmB,GAA1B,UAA2B,GAAW,IAAkC,OAAO,EAAE,CAAA,CAAC,CAAC;IACnF,SAAS;IACF,0CAAoB,GAA3B,cAAgC,OAAO,EAAE,CAAA,CAAC,CAAC;IAE3C,OAAO;IACA,+BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;IAClC,+BAAS,GAAhB,UAAiB,GAAY;QACzB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACpB,gBAAgB;QAChB,IAAM,SAAS,GAAG,CAAC,oBAAO,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAA;QAC7D,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;YAC1C,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAA;YACtC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAA;SACzD;IACL,CAAC;IAED,aAAa;IACN,6CAAuB,GAA9B,cAAmC,OAAO,IAAI,CAAC,qBAAqB,CAAA,CAAC,CAAC;IAC/D,8CAAwB,GAA/B,UAAgC,GAAY,IAAI,IAAI,CAAC,qBAAqB,GAAG,GAAG,CAAA,CAAC,CAAC;IAElF,WAAW;IACJ,mCAAa,GAApB,cAAyB,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAEzC,yCAAmB,GAA3B;QACI,IAAM,IAAI,GAAG,uBAAU,CAAC,eAAe,EAAE,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,oBAAS,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,oBAAS,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAc,CAAC,CAAA;YACpJ,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SACnC;IACL,CAAC;IAED,YAAY;IACL,qCAAe,GAAtB;QACI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1B,OAAO,IAAI,CAAC,YAAY,CAAA;IAC5B,CAAC;IAED,YAAY;IACL,uCAAiB,GAAxB;QACI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1B,OAAO,IAAI,CAAC,cAAc,CAAA;IAC9B,CAAC;IAED,aAAa;IACN,0CAAoB,GAA3B,UAA4B,MAAe;QAA3C,iBAGC;QAFG,IAAM,MAAM,GAAG,qBAAS,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;QAC9E,OAAO,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAI,CAAC,QAAQ,CAAC,qBAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;IACnG,CAAC;IAED,YAAY;IACJ,6CAAuB,GAA/B,UAAgC,IAAgB;QAAhD,iBAiBC;QAhBG,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;gBACzB,IAAM,CAAC,GAAG,KAAI,CAAC,QAAQ,CAAC,qBAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;gBAClD,IAAI,CAAC,CAAC,EAAE;oBACJ,OAAM;iBACT;gBACD,gBAAgB;gBAChB,+DAA+D;gBAC/D,OAAO;gBACP,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1B,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBAC7B;YACL,CAAC,CAAC,CAAA;SACL;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;IAC5B,CAAC;IAEM,qCAAe,GAAtB;QACI,OAAO,IAAI,CAAC,cAAc,CAAA;IAC9B,CAAC;IAED,SAAS;IACF,mCAAa,GAApB,UAAqB,GAAW;QAC5B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;IACnC,CAAC;IAED,aAAa;IACN,qCAAe,GAAtB,UAAuB,IAAY;QAC/B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;YACjC,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;YACpC,IAAI,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACvB,OAAO,GAAG,CAAA;aACb;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,OAAO;IACA,0CAAoB,GAA3B,UAA4B,IAAY;QACpC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAA;SACd;QACD,IAAI,GAAG,GAAe,IAAI,CAAA;QAC1B,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,GAAG,EAAE;YACL,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;SACjC;QACD,IAAI,CAAC,GAAG,EAAE;YACN,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;SACnC;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,EAAE;IACM,wCAAkB,GAA1B,UAA2B,KAAa;QACpC,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAClE,OAAM;SACT;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpC,CAAC;IAEM,wCAAkB,GAAzB;QAAA,iBAYC;QAXG,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,eAAe,CAAA;SAC9B;QACD,IAAM,KAAK,GAAa,EAAE,CAAA;QAC1B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAA,CAAC;YAC1B,KAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YAChC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACjB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC5B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,0CAAoB,GAA3B,UAA4B,KAAa;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YAClC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;YACpC,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAC5B,OAAO,IAAI,CAAA;SACd;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,2CAAqB,GAA7B;QACI,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,qBAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA;IAClG,CAAC;IAED,SAAS;IACD,yCAAmB,GAA3B,UAA4B,IAAS,EAAE,IAAgB,EAAE,IAAc;;QACnE,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE,aAAa;YAC/D,IAAM,KAAK,SAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,KAAK,CAAA;YACpD,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAC,IAAI,CAAC,KAAK,EAAC;YACzB,CAAC,IAAI,IAAI,qBAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAA;SACxD;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACpB,OAAM;SACT;QACD,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAChG;QACD,SAAS;QACT,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAClC,eAAe;QACf,oBAAO,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3C,QAAQ;QACR,CAAC,IAAI,IAAI,qBAAS,CAAC,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAChE,CAAC;IAED,aAAa;IACN,wCAAkB,GAAzB,UAA0B,IAAgB;QACtC,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE;YACtB,OAAO,KAAK,CAAA,CAAC,QAAQ;SACxB;QACD,iBAAiB;QACjB,IAAM,MAAM,GAAG,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YAChD,IAAI,CAAC,QAAQ,EAAE;gBACX,SAAQ;aACX;iBAAM,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;gBACjC,OAAO,IAAI,CAAA;aACd;iBAAM,IAAI,QAAQ,CAAC,QAAQ,IAAI,gBAAQ,CAAC,IAAI,IAAI,qBAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;gBAC3F,OAAO,IAAI,CAAA,CAAC,wBAAwB;aACvC;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IACM,+CAAyB,GAAhC,UAAiC,KAAa;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC1C,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,cAAc;IACP,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,iCAAW,GAAlB,UAAmB,IAAgB;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;IACxB,CAAC;IAED,WAAW;IACJ,wCAAkB,GAAzB,UAA0B,KAAa;QACnC,OAAO,CAAC,CAAA;IACZ,CAAC;IAED,SAAS;IACF,oCAAc,GAArB,UAAsB,KAAa,EAAE,EAAU;IAE/C,CAAC;IAED,WAAW;IACJ,kCAAY,GAAnB;QACI,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACxB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC1B,OAAO,GAAG,CAAA;IACd,CAAC;IACM,+BAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAA,CAAC,CAAC;IAClC,iCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IAE7C,SAAS;IACF,wCAAkB,GAAzB,UAA0B,KAAa;QACnC,IAAI,GAAG,GAAG,KAAK,CAAA;QACf,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE;gBACnE,IAAM,IAAI,GAAG,CAAC,CAAC,UAAU,CAAA;gBACzB,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;oBAC/B,GAAG,GAAG,IAAI,CAAA;iBACb;aACJ;QACL,CAAC,CAAC,CAAA;QACF,IAAI,GAAG,EAAE;YACL,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,gBAAgB,CAAC,CAAA;SACxC;IACL,CAAC;IAED,QAAQ;IACD,iCAAW,GAAlB,UAAmB,GAAW;QAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC5C,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACrD,CAAC;IAED,WAAW;IACJ,wCAAkB,GAAzB,UAA0B,WAAgB,EAAE,YAA0B;QAA1B,6BAAA,EAAA,kBAA0B;QAClE,oBAAO,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;;YAClC,IAAM,QAAQ,SAAG,WAAW,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,mCAAI,YAAY,CAAA;YAClE,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,CAAC,CAAA;IAC7C,CAAC;IAED,SAAS;IACF,sCAAgB,GAAvB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEM,mCAAa,GAApB,UAAqB,KAAa,EAAE,IAAc;IAElD,CAAC;IAED,aAAa;IACN,4CAAsB,GAA7B,UAA8B,KAAa,EAAE,UAAkB;QAC3D,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,yBAAyB,GAAG,UAAU,CAAA;IAC/C,CAAC;IAED,WAAW;IACG,mCAAa,GAA3B,UAA4B,KAAsB;;;;;4BAC5B,qBAAM,oBAAO,CAAC,MAAM,CAAC,WAAW,EAAE,EAAA;;wBAAhD,WAAW,GAAG,SAAkC;wBAChD,QAAQ,GAAG,EAAE,CAAA;wBACjB,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BACzC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;yBACtD;wBACD,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BACnC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;4BACvC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,EAAE;gCACpC,sBAAO,KAAK,EAAA;6BACf;yBACJ;wBACD,sBAAO,IAAI,EAAA;;;;KACd;IAED,OAAO;IACM,gCAAU,GAAvB,UAAwB,KAAsB,EAAE,KAAa,EAAE,YAAoB,EAAE,WAAoB,EAAE,aAAsB;;;;;;4BACxH,qBAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAA;;wBAApC,IAAI,CAAC,CAAA,SAA+B,CAAA,EAAE;4BAClC,uBAAU,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAA;4BACrD,sBAAO,EAAE,EAAA;yBACZ;wBACG,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,oCAAqB,CAAC,CAAC,CAAC,CAAC,mCAAoB,CAAA;wBAC1E,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;wBACrF,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;4BACd,IAAI,SAAS,GAAG,CAAC,CAAC,CAAA;4BAClB,IAAI,YAAY,KAAK,CAAC,EAAE,EAAE,UAAU;gCAChC,SAAS,GAAG,CAAC,CAAC,CAAA;6BACjB;iCAAM,IAAI,YAAY,KAAK,CAAC,EAAE,EAAE,IAAI;gCACjC,SAAS,GAAG,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;6BAChD;iCAAM,IAAI,YAAY,KAAK,CAAC,EAAE,EAAE,KAAK;gCAClC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAA;6BACzB;4BACD,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BAC5C,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;4BACzC,IAAI,IAAI,EAAE;gCACN,KAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;gCACtD,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;6BACnE;wBACL,CAAC,CAAC,CAAA;wBACF,sBAAO,EAAE,EAAA;;;;KACZ;IAED,OAAO;IACM,kCAAY,GAAzB,UAA0B,KAAsB,EAAE,KAAa,EAAE,QAAyB,EAAE,WAAoB;QAA/C,yBAAA,EAAA,gBAAyB;;;;;;4BACjF,qBAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAA;;wBAApC,IAAI,CAAC,CAAA,SAA+B,CAAA,EAAE;4BAClC,uBAAU,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAA;4BACrD,sBAAO,EAAE,EAAA;yBACZ;wBACG,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,oCAAqB,CAAC,CAAC,CAAC,CAAC,mCAAoB,CAAA;wBACrE,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;wBACrF,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;4BACd,IAAM,IAAI,GAAG,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;4BAC5C,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;4BACzC,IAAI,IAAI,EAAE;gCACN,KAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;gCAC/C,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;6BACnE;wBACL,CAAC,CAAC,CAAA;wBACF,sBAAO,EAAE,EAAA;;;;KACZ;IAED,OAAO;IACM,iCAAW,GAAxB,UAAyB,GAAW;;;gBAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBAC5B,sBAAO,EAAE,EAAA;;;KACZ;IAED,OAAO;IACM,iCAAW,GAAxB,UAAyB,GAAW;;;gBAChC,sBAAO,EAAE,EAAA;;;KACZ;IAED,OAAO;IACM,gCAAU,GAAvB,UAAwB,KAAa,EAAE,EAAU;;;;gBACvC,KAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,KAAK,OAAA,EAAE,EAAE,IAAA,EAAE,CAAC,EAAtD,GAAG,SAAA,EAAE,IAAI,UAAA,CAA6C;gBAC9D,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE;oBACd,oBAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;iBAClD;gBACD,sBAAO,GAAG,EAAA;;;KACb;IAED,OAAO;IACM,mCAAa,GAA1B,UAA2B,KAAa;;;;gBAC9B,KAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,EAArD,GAAG,SAAA,EAAE,IAAI,UAAA,CAA4C;gBAC7D,sBAAO,GAAG,EAAA;;;KACb;IAEM,4BAAM,GAAb,UAAc,EAAU;;QACpB,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE;YAC/B,IAAM,GAAG,SAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,0CAAE,WAAW,EAAE,CAAA;YAC7E,IAAI,CAAC,GAAG,EAAE;gBACN,IAAI,EAAE,CAAC,YAAY,KAAK,QAAQ,EAAE;oBAC9B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAA;oBAC9B,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAA;iBACrC;aACJ;iBAAM,IAAI,EAAE,CAAC,YAAY,KAAK,QAAQ,EAAE;gBACrC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAA;gBAC9B,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAA;gBAClC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;aACtB;iBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,oBAAoB,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE;gBACvF,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;aACrB;SACJ;IACL,CAAC;IAEO,uCAAiB,GAAzB,UAA0B,IAAS;QAAnC,iBAgBC;QAfG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAApD,CAAoD,CAAC,CAAA;QAC5E,aAAa;QACb,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,gBAAgB,CAAC,CAAA;SACxC;QACD,OAAO;QACP,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;SAC/B;QACD,OAAO;QACP,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,oBAAoB,CAAC,CAAA;SAC5C;IACL,CAAC;IAEO,wCAAkB,GAA1B,UAA2B,IAAgB,EAAE,IAAS;QAAtD,iBAmGC;;QAlGG,IAAI,IAAI,KAAK,kBAAU,CAAC,QAAQ,EAAE,EAAE,MAAM;YACtC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACtC,IAAI,IAAI,EAAE;gBACN,qCAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBACvC,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA;gBAC7B,QAAQ;gBACR,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACpC,OAAO;gBACP,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,CAAC,IAAI,EAAC;gBACtB,eAAe;gBACf,IAAI,OAAA,IAAI,CAAC,IAAI,0CAAE,EAAE,MAAK,wBAAa,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;oBACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;iBACtC;gBACD,OAAO;gBACP,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;oBACvB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;iBAC/B;gBACD,aAAa;gBACb,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACnC,UAAU;gBACV,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;oBAC3B,oBAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAA;iBAC5C;gBACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;aACpC;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,MAAM;YACjD,IAAI,CAAC,OAAO,CAAC,UAAA,KAAK;;gBACd,MAAA,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,0CAAE,UAAU,CAAC,EAAE,EAAC;gBACpC,KAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA,CAAC,UAAU;YAChD,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;SACpC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,aAAa,EAAE,EAAE,MAAM;YAClD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;SACnC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,cAAc,EAAE,EAAE,QAAQ;YACrD,yCAAyC;YACzC,oBAAoB;YACpB,yDAAyD;SAC5D;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,SAAS,EAAE,EAAE,MAAM;YAC9C,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,kBAAQ,EAAE,CAAC,CAAA;YAC1F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;SACxC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,MAAM;YACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC7B;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,IAAM,KAAK,GAAG,oBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;YAC/C,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;gBACzD,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,oBAAU,EAAE,CAAC,CAAA;gBAClG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;aAC1C;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,cAAc,EAAE,EAAE,MAAM;YACnD,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YACjD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;SACxD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,WAAW,EAAE,EAAE,MAAM;YAChD,UAAI,IAAI,CAAC,IAAI,0CAAE,MAAM,EAAE;gBACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAA;aACvD;iBAAM;gBACH,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aACxC;YACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,CAAC,CAAA;YAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SACtC;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,cAAc,EAAE,EAAE,MAAM;YACnD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACZ,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAC1C;iBAAM;gBACH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,qBAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA;aACnG;YACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,yBAAyB,CAAC,CAAA;SACjD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,UAAU,EAAE,EAAE,QAAQ;YACjD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,mBAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC5D,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAClD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,aAAa,EAAE,EAAE,QAAQ;YACpD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACtC,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,MAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,0CAAE,UAAU,CAAC,IAAI,CAAC,IAAI,EAAC;gBAChD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChD,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAC,CAAC,WAAW;gBACjD,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAC,CAAC,WAAW;aACjD;YACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAClD;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,YAAY,EAAE,EAAE,QAAQ;YACnD,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,GAAG,EAAE;gBACL,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;gBACtB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAA;aACnD;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,eAAe,EAAE,EAAE,UAAU;YACxD,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,GAAG,EAAE;gBACL,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAA;gBACtC,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAA;aACpD;SACJ;aAAM,IAAI,IAAI,KAAK,kBAAU,CAAC,aAAa,EAAE,EAAE,QAAQ;YACpD,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,GAAG,EAAE;gBACL,GAAG,CAAC,OAAO,GAAG,oBAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;aAC7D;SACJ;IACL,CAAC;IAEM,kCAAY,GAAnB,UAAoB,GAAW;;QAC3B,aAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,0CAAE,KAAK,CAAA;IAC1C,CAAC;IAEM,0CAAoB,GAA3B,UAA4B,KAAa;QACrC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;IACrC,CAAC;IAED,YAAY;IACJ,iCAAW,GAAnB,UAAoB,IAAgB;QAChC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAClB,OAAM;SACT;QACD,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;SAChD;QACD,MAAM,CAAC,MAAM,GAAG,CAAC,CAAA;QAEjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAS,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACnE,IAAI,OAAO,GAAG,qBAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1C,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1C,IAAI,KAAK,GAAG,OAAO,GAAG,qBAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAA;YACpD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAClB,IAAI,IAAI,GAAG,EAAE,kBAAkB,EAAE,KAAK,EAAE,cAAc,EAAE,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;gBAC5G,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gBACrC,IAAI,CAAC,IAAI,EAAE;oBACP,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA;iBACzC;gBACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aAClB;SACJ;IACL,CAAC;IAED,QAAQ;IACA,uCAAiB,GAAzB,UAA0B,KAAa;QACnC,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,IAAI,EAAE;YACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,SAAS,EAAE;oBAChH,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAA;iBAChC;aACJ;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,yBAAyB;IAClB,uCAAiB,GAAxB,UAAyB,cAA+B,EAAE,kBAA0B;QAChF,IAAI,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,SAAS,EAAE;YAC9F,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAA;YAClD,IAAI,CAAC,IAAI,EAAE;gBACP,SAAS;gBACT,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtD,IAAI,GAAG,GAAG,EAAE,CAAA;gBACZ,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAA;gBACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,IAAI,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;oBAC1B,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;oBACxB,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;oBACxB,IAAI,KAAK,GAAG,CAAC,GAAG,qBAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAA;oBACxC,IAAI,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;oBAC/C,IAAI,WAAW,EAAE;wBACb,IAAI,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,GAAG,EAAE;4BAC5C,GAAG,IAAI,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;yBACzD;6BACI,IAAI,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,SAAS,EAAE;4BACvD,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;yBAC5G;qBACJ;yBACI;wBACD,GAAG,IAAI,CAAC,CAAA;qBACX;iBACJ;gBACD,IAAI,QAAQ,GAAG,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,8BAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,oCAAyB,CAAC,GAAG,CAAC,CAAA;gBACpH,IAAI,OAAO,QAAQ,IAAI,QAAQ,EAAE;oBAC7B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;oBACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;oBACtD,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAA;iBAChC;gBACD,IAAI,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;gBAC9D,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAA;aACnE;YACD,OAAO,IAAI,CAAA;SACd;QACD,OAAO,cAAc,CAAC,IAAI,CAAA;IAC9B,CAAC;IAEM,gCAAU,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAClC,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QACrB,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QACrC,OAAO,MAAM,CAAA;IACjB,CAAC;IAz6BgB,WAAW;QAD/B,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;OACD,WAAW,CA06B/B;IAAD,kBAAC;CA16BD,AA06BC,CA16BwC,EAAE,CAAC,SAAS,GA06BpD;kBA16BoB,WAAW", "file": "", "sourceRoot": "/", "sourcesContent": ["import { cameraCtrl } from \"../../common/camera/CameraCtrl\"\nimport { CITY_MAIN_NID, DECORATION_MUD_CONF, DECORATION_MUD_OUTER_CONF, MAP_EXTRA_SIZE, MAP_SHOW_OFFSET, TILE_SIZE } from \"../../common/constant/Constant\"\nimport { AlliBaseInfo, ArmyShortInfo, GameConfigInfo, PlayerInfo } from \"../../common/constant/DataType\"\nimport { ArmyState, DecorationType, LandType, NotifyType, PreferenceKey, WinCondType } from \"../../common/constant/Enums\"\nimport EventType from \"../../common/event/EventType\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport { mapHelper } from \"../../common/helper/MapHelper\"\nimport AreaCenterModel from \"../area/AreaCenterModel\"\nimport CTypeObj from \"../common/CTypeObj\"\nimport PolicyObj from \"../main/PolicyObj\"\nimport UserModel from \"../common/UserModel\"\nimport AncientObj from \"../main/AncientObj\"\nimport AvoidWarObj from \"../main/AvoidWarObj\"\nimport BaseMarchObj from \"../main/BaseMarchObj\"\nimport BTCityObj from \"../main/BTCityObj\"\nimport MapCellObj from \"../main/MapCellObj\"\nimport MarchObj from \"../main/MarchObj\"\nimport SeasonInfo from \"../main/SeasonInfo\"\nimport TransitObj from \"../main/TransitObj\"\nimport { NOVICE_ARMY_MOVE_MUL, NOVICE_FIRST_MOVE_MUL, NOVICE_MAINCITY_INDEX } from \"./NoviceConfig\"\nimport NoviceServerModel from \"./NoviceServerModel\"\nimport TondenObj from \"../main/TondenObj\"\nimport RandomObj from \"../common/RandomObj\"\nimport { DecorationJItem } from \"../../common/constant/JsonType\"\nimport { mapUionFindHelper } from \"../../common/helper/MapUionFindHelper\"\nimport { viewHelper } from \"../../common/helper/ViewHelper\"\n\n/**\n * 新手村模块\n */\*************('novice')\nexport default class NoviceModel extends mc.BaseModel {\n\n    private user: UserModel = null\n    private areaCenter: AreaCenterModel = null\n    private server: NoviceServerModel = null\n\n    private runTime: number = 0 //服务器运行时间\n    private initTime: number = 0 //初始化时间\n    private winCondType: number = 0\n    private winCondValue: number = 0 //胜利条件对应值\n    private winCondMaxDay: number = 0 //最大天数\n    private season: SeasonInfo = null //季节信息\n    private mapCells: MapCellObj[] = [] //地图所有单元格\n    private gameConfInfo: GameConfigInfo = {} //游戏配置信息\n    private marchs: MarchObj[] = [] //行军列表\n    private transits: TransitObj[] = [] //运送列表\n    private battleDistMap: { [key: number]: { list: string[] } } = {} //战斗分布信息\n    private avoidWarDistMap: { [key: number]: AvoidWarObj } = {} //当前免战分布信息\n    private btCityQueueMap: { [key: number]: BTCityObj } = {} //当前修建城市信息\n    private tondenQueueMap: { [key: number]: TondenObj } = {} //当前屯田信息\n\n    private allPlayerInfos: { [key: string]: PlayerInfo } = {} //所有玩家的信息\n    private yetPlayNewCellMap: { [key: number]: boolean } = {} //已经观看过的地块\n    private notPlayNewCells: number[] = [] //还没有观看过的地块\n    private tempMainCityCell: MapCellObj = null //临时的主城地块用于更新关联使用\n    private tempHasUpdateCellInfo: boolean = false\n\n    private preWinSize: cc.Vec2 = cc.v2()\n    private maxTileRange: cc.Vec2 = cc.v2(10, 10) //当前地图显示格子数量范围 半径\n    private maxMarchLength: number = 0 //当前地图最大可显示的行军线长度\n    private cameraInfo: any = { zoomRatio: -1, position: cc.v2() } //相机信息\n    private centre: cc.Vec2 = cc.v2() //当前地图的中心位置\n    private lookCell: MapCellObj = null //当前查看的地块\n    private mainCityInCameraRange: boolean = false //主城是否在相机范围内\n\n    private pauseBattleAreaIndex: number = -1\n    private pauseBattleAreaFrameCount: number = 0\n\n    private tempMudIconMap: { [key: number]: string } = {}//主城周围土地装饰icon\n    private tempCityMudMap: { [key: number]: number[] } = {}//城市周围土地下标\n    private random: RandomObj = null\n\n    private islandRoundMap: { [key: string]: number } = {}//岛屿周围区域，50*50格子以外的区域\n\n    private decorationsMap: { [key: number]: any } = {}//地块装饰信息\n\n    public isCanAttackPlayer: boolean = false//是否可以攻击玩家\n\n    public onCreate() {\n        this.user = this.getModel('user')\n        this.areaCenter = this.getModel('areaCenter')\n        this.server = this.getModel('novice_server')\n    }\n\n    public clean() {\n\n    }\n\n    public getSceneKey() { return 'novice' }\n\n    public initEnemy(UID: string, index: number) {\n        const enemy = this.allPlayerInfos[UID] = gameHpr.getEmptyPlayerInfo(UID, 0)\n        const nickname = assetsMgr.getJsonData('guideText', 'enemy_name')[mc.lang]\n        gameHpr.initPlayerInfo(enemy, {\n            nickname,\n            headIcon: this.user.getHeadIcon(),\n            mainCityIndex: index,\n        })\n    }\n\n    public async init() {\n        this.runTime = /* data.runTime || 0 */0\n        this.initTime = Date.now()\n        this.winCondType = 1\n        this.winCondValue = 30000\n        this.winCondMaxDay = 30\n        this.season = new SeasonInfo().init({ type: 2 })\n        const mapId = 0\n        const mapJson = await assetsMgr.loadOnceRes('maps/maps_' + mapId, cc.JsonAsset)\n        //加载地图装饰\n        await this.loadDecorationsJson(mapId)\n        await this.loadRoundJson(mapId)\n        this.random = new RandomObj()\n        const data = this.server.init(this.user.getUid(), mapJson)\n        // 初始化player\n        gameHpr.player.init(data.player, true)\n        // 初始化user\n        this.user.setPortrayals(data.user.portrayals)\n        this.user.setGold(data.user.gold)\n        // 玩家信息\n        this.allPlayerInfos = {}\n        const uid = this.user.getUid()\n        const player = this.allPlayerInfos[uid] = gameHpr.getEmptyPlayerInfo(uid, 0)\n        gameHpr.initPlayerInfo(player, {\n            nickname: this.user.getNickname(), headIcon: this.user.getHeadIcon(), mainCityIndex: NOVICE_MAINCITY_INDEX,\n            policys: data.player.policySlots,\n            towerLvMap: data.player.towerLvMap,\n        })\n\n        if (data.enemy.id) {\n            const UID = data.enemy.id\n            const enemy = this.allPlayerInfos[UID] = gameHpr.getEmptyPlayerInfo(UID, 0)\n            const nickname = data.enemy.name\n            gameHpr.initPlayerInfo(enemy, {\n                nickname,\n                headIcon: this.user.getHeadIcon(),\n                mainCityIndex: 0,\n            })\n        }\n\n        // 地块信息\n        this.mapCells = []\n        const cells: any[] = data.cells\n        const citys: MapCellObj[] = []\n        this.tempCityMudMap = {}\n        this.tempMudIconMap = {}\n        for (let i = 0, l = cells.length; i < l; i++) {\n            const info = cells[i]\n            const cell = new MapCellObj().init(i, info.landId)\n            cell.updateInfo(info)\n            this.mapCells.push(cell)\n            if (cell.cityId === CITY_MAIN_NID) { //是否主城\n                citys.push(cell)\n            }\n            if (cell.owner) { //记录玩家的拥有的\n                this.addPlayerInfoByCell(info, cell, true)\n            }\n        }\n        // 如果有的话 刷新一下\n        if (this.lookCell) {\n            this.lookCell = this.mapCells[this.lookCell.index]\n        }\n        // 这里将关联的地块也设置一下\n        citys.forEach(m => {\n            // 地块关联\n            this.updateMainCityRangeCell(m)\n            // 刷新玩家的地块边界线\n            mapHelper.updatePlayerCellBorderLines(this.getPlayerInfo(m.owner)?.cells)\n        })\n        // 初始化地图中心位置\n        this.centre.set(mapHelper.indexToPoint(gameHpr.player.getMainCityIndex()))\n        // 这里检测是否有新的地块还没播放效果\n        const myCells = this.getPlayerInfo(this.user.getUid())?.cells\n        this.notPlayNewCells.length = 0\n        this.yetPlayNewCellMap = this.user.getLocalPreferenceDataBySid(PreferenceKey.YET_PLAY_NEW_CELL) || {}\n        if (ut.isEmptyObject(this.yetPlayNewCellMap)) {\n            myCells?.forEach((m, key) => this.yetPlayNewCellMap[key] = true)\n            this.saveYetPlayNewCellMap()\n        } else {\n            myCells?.forEach((m, key) => {\n                if (!this.yetPlayNewCellMap[key] && m.city?.id !== CITY_MAIN_NID) {\n                    this.notPlayNewCells.push(Number(key))\n                }\n            })\n        }\n        // 监听消息\n        this.areaCenter.initNetEvent()\n        // this.getModel<ChatModel>('chat').init()\n        gameHpr.net.on('game/OnUpdateWorldInfo', this.OnUpdateWorldInfo, this)\n        // // 获取 行军路线 战斗分布信息\n        this.initMarchs()\n        this.initBattleDist()\n        this.initAvoidWarDist()\n        this.initBTCityQueues()\n        this.server.run()\n        mapUionFindHelper.init()\n    }\n\n    //加载地图装饰UI配置\n    private async loadDecorationsJson(mapId: number) {\n        this.decorationsMap = {}\n        let itemsUrl = 'decorations/decorations_' + mapId\n        let itemsJson = await assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)\n        if (itemsJson) {\n            for (let i = 0; i < itemsJson.json.length; i++) {\n                let item = itemsJson.json[i]\n                let index = item[0]\n                let list = this.decorationsMap[index]\n                if (!list) {\n                    list = this.decorationsMap[index] = []\n                }\n                let info = { decorationJson: assetsMgr.getJsonData('decoration', item[1]), decorationActIndex: item[2] || index }\n                list.push(info)\n            }\n        }\n        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset)\n    }\n\n    //加载地图岛屿周围的接壤部分\n    private async loadRoundJson(mapId: number) {\n        this.islandRoundMap = {}\n        const itemsUrl = 'rounds/rounds_' + mapId\n        const itemsJson = await assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)\n        if (itemsJson) {\n            for (let key in itemsJson.json) {\n                let item = itemsJson.json[key]\n                this.islandRoundMap[key] = item\n            }\n        }\n        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset)\n    }\n\n    private initMarchs() {\n        this.marchs = this.server.getMarchs().map(m => new MarchObj().init(m))\n    }\n\n    private initBattleDist() {\n        this.battleDistMap = {}\n        const obj = this.server.getBattleDistMap()\n        for (let key in obj) {\n            this.battleDistMap[key] = { list: obj[key] }\n        }\n    }\n\n    private initAvoidWarDist() {\n        this.avoidWarDistMap = {}\n        const obj = this.server.getAvoidWarDistMap(), now = Date.now()\n        for (let key in obj) {\n            this.avoidWarDistMap[key] = new AvoidWarObj().init(key, Math.max(0, obj[key] - now), 0)\n        }\n    }\n\n    private initBTCityQueues() {\n        this.btCityQueueMap = {}\n        this.server.getBTCityQueues().forEach(m => this.btCityQueueMap[m.aIndex] = new BTCityObj().init(m.strip()))\n    }\n\n    // 获取服务器运行时间\n    public getServerRunTime() {\n        return Math.max(0, this.runTime + (Date.now() - this.initTime))\n    }\n\n    public isHasLocalLandData(sid: number) {\n        return false\n    }\n\n    public async getAlliances() { return [] }\n    public async applyJoinAlliance(uid: string, desc: string) { return '' }\n    public async cancelJoinAlliance() { return '' }\n\n    // 获取胜利条件数值\n    public getWinCondType() { return this.winCondType }\n    public getWinCondValue() { return this.winCondValue }\n    public getWinCondMaxDay() { return this.winCondMaxDay }\n\n    // 是否血战到底模式\n    public isKarmicMahjong() { return this.winCondType === WinCondType.KARMIC_MAHJONG }\n\n    public getSeason(): SeasonInfo { return this.season }\n    public getSeasonPolicys() { return this.season.getPolicys() }\n    public getSeasonType() { return 2 } //新手村用秋季的图\n    public getGameOverInfo() { return null }\n    public isGameOver() { return false }\n\n    // 获取当前已沦陷人数\n    public getCaptureNum() { return 0 }\n\n    // 获取专属装备效果\n    public getExclusiveEquipEffects(id: number) {\n        return this.gameConfInfo?.exclusiveMap?.[id] || []\n    }\n\n    // 获取士兵基础费用\n    public getPawnBaseCost(id: number) {\n        return this.gameConfInfo?.pawnCostMap?.[id] || 0\n    }\n\n    // 获取时间\n    public getEventValue(type: number) {\n        return null\n    }\n\n    // 初始相机信息\n    public initCameraInfo() {\n        const uid = gameHpr.getUid()\n        if (uid !== this.cameraInfo.uid) {\n            this.cameraInfo.uid = uid\n            this.cameraInfo.zoomRatio = -1\n        }\n        if (this.cameraInfo.zoomRatio === -1) { //第一次让相机居中\n            cameraCtrl.init(mapHelper.getPixelByPoint(cc.v2(0.5, 0.5).addSelf(this.centre)), mapHelper.MAP_SIZE, MAP_SHOW_OFFSET, this.user.getLocalPreferenceDataBySid(PreferenceKey.MAIN_CAMERA_ZOOM))\n        } else {\n            cameraCtrl.initByPosition(MAP_SHOW_OFFSET, this.cameraInfo.position, this.cameraInfo.zoomRatio, mapHelper.MAP_SIZE)\n        }\n    }\n    // 保存相机信息\n    public saveCameraInfo() {\n        const zoom = this.cameraInfo.saveZoomRatio = this.cameraInfo.zoomRatio = cameraCtrl.zoomRatio\n        this.cameraInfo.position.set(cameraCtrl.getPosition())\n        this.user.setLocalPreferenceDataBySid(PreferenceKey.MAIN_CAMERA_ZOOM, zoom)\n    }\n\n    // 所有地块信息\n    public getMapCells() { return this.mapCells }\n    // 获取单个地块信息\n    public getMapCellByPoint(point: cc.Vec2) { return this.getMapCellByIndex(mapHelper.pointToIndex(point)) }\n    public getMapCellByIndex(index: number) {\n        if (this.lookCell?.index === index) {\n            return this.lookCell\n        }\n        return this.mapCells[index]\n    }\n\n    // 这里检测地块信息是否存在 用于重新连接\n    public isHasMapCellInfo() {\n        const size = mapHelper.MAP_SIZE\n        return this.mapCells.length === size.x * size.y\n    }\n\n    // 获取遗迹列表\n    public getAncientMap(): { [key: number]: AncientObj } { return {} }\n    public getAncientInfo(index: number): AncientObj { return null }\n\n    // 同步地块信息\n    public async syncServerCellInfo(index: number) {\n\n    }\n\n    // 获取战斗分布信息\n    public getBattleDistMap() { return this.battleDistMap }\n    public getCanShowToMiniMapBattles(): MapCellObj[] { return [] }\n\n    // 获取免战分布信息\n    public getAvoidWarDistMap() { return this.avoidWarDistMap }\n    // 清理普通免战\n    public cleanGeneralAvoidWar() {\n        let has = false\n        for (let k in this.avoidWarDistMap) {\n            if (!this.avoidWarDistMap[k].type) {\n                delete this.avoidWarDistMap[k]\n                has = true\n            }\n        }\n        if (has) {\n            this.emit(EventType.UPDATE_AVOIDWAR_DIST_INFO)\n        }\n    }\n\n    // 获取修建城市列表\n    public getBTCityQueueMap() { return this.btCityQueueMap }\n    // 获取屯田信息\n    public getTondenQueueMap() { return this.tondenQueueMap }\n    // 获取军队屯田信息\n    public getArmyTondenInfo(index: number, auid: string) {\n        const info = this.tondenQueueMap[index]\n        return info?.armyUid === auid ? info : null\n    }\n    // 获取正在屯田的数量\n    public getArmyTodeningCount() { return Object.keys(this.tondenQueueMap).length }\n\n    // 获取市场价格\n    public getTradePriceMapByType(type: number) { return {} }\n    // 获取联盟基础信息\n    public getAlliBaseInfo(uid: string): AlliBaseInfo { return {} as any }\n    // 获取联盟政策\n    public getAlliPolicysByUid(uid: string): { [key: number]: PolicyObj } { return {} }\n    // 获取盟主位置\n    public getAlliCreaterIndexs() { return [] }\n\n    // 中心位置\n    public getCentre() { return this.centre }\n    public setCentre(val: cc.Vec2) {\n        this.centre.set(val)\n        // 检测主城是否在摄像机范围内\n        const isInRange = !gameHpr.player.checkMainNotInScreenRange()\n        if (this.mainCityInCameraRange !== isInRange) {\n            this.mainCityInCameraRange = isInRange\n            this.emit(EventType.UPDATE_MC_IN_CAM_RANGE, isInRange)\n        }\n    }\n\n    // 主城是否在相机范围内\n    public isMainCityInCameraRange() { return this.mainCityInCameraRange }\n    public setMainCityInCameraRange(val: boolean) { this.mainCityInCameraRange = val }\n\n    // 获取相机保存信息\n    public getCameraInfo() { return this.cameraInfo }\n\n    private checkUpdateWorldWin() {\n        const size = cameraCtrl.getWorldWinSize()\n        if (!this.preWinSize.equals(size)) {\n            this.preWinSize.set(size)\n            this.maxTileRange.set2(Math.ceil(Math.ceil(size.x / TILE_SIZE) / 2) + MAP_EXTRA_SIZE, Math.ceil(Math.ceil(size.y / TILE_SIZE) / 2) + MAP_EXTRA_SIZE)\n            this.maxMarchLength = size.mag()\n        }\n    }\n\n    // 获取最多显示格子数\n    public getMaxTileRange() {\n        this.checkUpdateWorldWin()\n        return this.maxTileRange\n    }\n\n    // 获取行军线最大长度\n    public getMaxMarchLength() {\n        this.checkUpdateWorldWin()\n        return this.maxMarchLength\n    }\n\n    // 获取范围内的所有地块\n    public getRangeCellsByPoint(centre: cc.Vec2) {\n        const points = mapHelper.getRangePointsByPoint(centre, this.getMaxTileRange())\n        return points.map(p => { return { point: p, cell: this.mapCells[mapHelper.pointToIndex(p)] } })\n    }\n\n    // 更新主城周围的信息\n    private updateMainCityRangeCell(cell: MapCellObj) {\n        if (cell.getAcreage() > 1) {\n            cell.getOwnPoints().forEach(m => {\n                const c = this.mapCells[mapHelper.pointToIndex(m)]\n                if (!c) {\n                    return\n                }\n                // 刷新地块底 用石头地作为底\n                // mapHelper.updateCityLandDi(c, LandType.STONE, this.mapCells)\n                // 设置关联\n                if (!m.equals(cell.actPoint)) {\n                    c.setCityDepend(cell.city)\n                }\n            })\n        }\n        this.randCityMud(cell)\n        this.tempMudIconMap = {}\n    }\n\n    public getAllPlayerMap() {\n        return this.allPlayerInfos\n    }\n\n    // 获取玩家信息\n    public getPlayerInfo(uid: string) {\n        return this.allPlayerInfos[uid]\n    }\n\n    // 根据名字获取玩家信息\n    public getPlayerByName(name: string) {\n        for (let key in this.allPlayerInfos) {\n            const plr = this.allPlayerInfos[key]\n            if (plr.nickname === name) {\n                return plr\n            }\n        }\n        return null\n    }\n\n    // 获取玩家\n    public getPlayerByNameOrUID(name: string) {\n        if (!name) {\n            return null\n        }\n        let plr: PlayerInfo = null\n        const uid = Number(name)\n        if (uid) {\n            plr = this.getPlayerInfo(name)\n        }\n        if (!plr) {\n            plr = this.getPlayerByName(name)\n        }\n        return plr\n    }\n\n    //\n    private addNotPlayNewCells(index: number) {\n        if (this.yetPlayNewCellMap[index] || this.notPlayNewCells.has(index)) {\n            return\n        }\n        this.notPlayNewCells.push(index)\n    }\n\n    public getNotPlayNewCells() {\n        if (this.notPlayNewCells.length === 0) {\n            return this.notPlayNewCells\n        }\n        const cells: number[] = []\n        this.notPlayNewCells.forEach(m => {\n            this.yetPlayNewCellMap[m] = true\n            cells.push(m)\n        })\n        this.saveYetPlayNewCellMap()\n        this.notPlayNewCells = []\n        return cells\n    }\n\n    public setYetPlayNewCellMap(index: number) {\n        if (!this.yetPlayNewCellMap[index]) {\n            this.notPlayNewCells.remove(index)\n            this.yetPlayNewCellMap[index] = true\n            this.saveYetPlayNewCellMap()\n            return true\n        }\n        return false\n    }\n\n    private saveYetPlayNewCellMap() {\n        this.user.setLocalPreferenceDataBySid(PreferenceKey.YET_PLAY_NEW_CELL, this.yetPlayNewCellMap)\n    }\n\n    // 添加玩家信息\n    private addPlayerInfoByCell(data: any, cell: MapCellObj, init?: boolean) {\n        if (!data) {\n            return\n        } else if (data.owner !== cell.owner && cell.owner) { //如果之前有人了 先删掉\n            const cells = this.allPlayerInfos[cell.owner]?.cells\n            cells?.delete(cell.index)\n            !init && mapHelper.updatePlayerCellBorderLines(cells)\n        } else if (!data.owner) {\n            return\n        }\n        let player = this.allPlayerInfos[data.owner]\n        if (!player) {\n            player = this.allPlayerInfos[data.owner] = gameHpr.getEmptyPlayerInfo(data.owner, data.index)\n        }\n        // 添加地块信息\n        player.cells.set(cell.index, cell)\n        // 如果玩家信息记录玩家信息\n        gameHpr.initPlayerInfo(player, data.player)\n        // 刷新边框线\n        !init && mapHelper.updatePlayerCellBorderLines(player.cells)\n    }\n\n    // 检测是否可以攻占地块\n    public checkCanOccupyCell(cell: MapCellObj) {\n        if (cell.isOneAlliance()) {\n            return false //盟友不能攻占\n        }\n        // 检测这块地4周是否有我方的地\n        const points = mapHelper.getOnePointOuter(cell.actPoint, cell.getSize())\n        for (let i = 0; i < points.length; i++) {\n            let nearCell = this.getMapCellByPoint(points[i])\n            if (!nearCell) {\n                continue\n            } else if (nearCell.isOneAlliance()) {\n                return true\n            } else if (nearCell.landType == LandType.PASS && mapHelper.checkBridgeCanPass(cell, nearCell)) {\n                return true //如果是桥需要看 桥的另外一头是否有我方的地 \n            }\n        }\n        return false\n    }\n    public checkCanOccupyCellByIndex(index: number) {\n        const cell = this.getMapCellByIndex(index)\n        return !!cell && this.checkCanOccupyCell(cell)\n    }\n\n    // 设置当前查看的地块信息\n    public getLookCell() { return this.lookCell }\n    public setLookCell(cell: MapCellObj) {\n        this.lookCell = cell\n    }\n\n    // 获取城市皮肤id\n    public getCitySkinByIndex(index: number) {\n        return 0\n    }\n\n    // 刷新城市皮肤\n    public updateCitySkin(index: number, id: number) {\n\n    }\n\n    // 获取所有行军信息\n    public getAllMarchs(): BaseMarchObj[] {\n        const arr = []\n        arr.pushArr(this.marchs)\n        arr.pushArr(this.transits)\n        return arr\n    }\n    public getMarchs() { return this.marchs }\n    public getTransits() { return this.transits }\n\n    // 刷新行军相关\n    public updateMarchByIndex(index: number) {\n        let has = false\n        this.marchs.forEach(m => {\n            if (index === -1 || m.startIndex === index || m.targetIndex === index) {\n                const type = m.targetType\n                if (m.updateTargetType() !== type) {\n                    has = true\n                }\n            }\n        })\n        if (has) {\n            this.emit(EventType.UPDATE_ALL_MARCH)\n        }\n    }\n\n    // 删除行军线\n    public removeMarch(uid: string) {\n        const march = this.marchs.remove('uid', uid)\n        march && this.emit(EventType.REMOVE_MARCH, march)\n    }\n\n    // 设置行军线透明度\n    public setAllMarchOpacity(progressMap: any, defaultRatio: number = 100) {\n        gameHpr.world.getAllMarchs().forEach(m => {\n            const progress = progressMap[m.getMarchLineType()] ?? defaultRatio\n            m.opacity = Math.floor(255 * progress * 0.01)\n        })\n        this.emit(EventType.UPDATE_MARCH_OPACITY)\n    }\n\n    // 获取城市产出\n    public getCityOutputMap(): { [key: number]: CTypeObj } {\n        return {}\n    }\n\n    public setCityOutput(index: number, type: CTypeObj) {\n\n    }\n\n    // 设置暂停区域战斗时间\n    public setPauseBattleAreaInfo(index: number, frameCount: number) {\n        this.pauseBattleAreaIndex = index\n        this.pauseBattleAreaFrameCount = frameCount\n    }\n\n    // 检测是否可以行动\n    private async isCanDoAction(armys: ArmyShortInfo[]) {\n        let serverArmys = await gameHpr.player.getAllArmys()\n        let stateMap = {}\n        for (let i = 0; i < serverArmys.length; i++) {\n            stateMap[serverArmys[i].uid] = serverArmys[i].state\n        }\n        for (let i = 0; i < armys.length; i++) {\n            armys[i].state = stateMap[armys[i].uid]\n            if (armys[i].state === ArmyState.FIGHT) {\n                return false\n            }\n        }\n        return true\n    }\n\n    // 攻占地块\n    public async occupyCell(armys: ArmyShortInfo[], index: number, autoBackType: number, isSameSpeed: boolean, isGuideBattle: boolean) {\n        if (!await this.isCanDoAction(armys)) {\n            viewHelper.showAlert('toast.battling_cant_operation')\n            return ''\n        }\n        let speed = isGuideBattle ? -NOVICE_FIRST_MOVE_MUL : -NOVICE_ARMY_MOVE_MUL\n        speed = isSameSpeed ? this.server.getArmysMarchSameSpeed(armys, index, speed) : speed\n        armys.forEach(data => {\n            let backIndex = -1\n            if (autoBackType === 1) { //最近要塞或者主城\n                backIndex = -2\n            } else if (autoBackType === 2) { //主城\n                backIndex = gameHpr.player.getMainCityIndex()\n            } else if (autoBackType === 3) { //出发点\n                backIndex = data.index\n            }\n            const area = this.server.getArea(data.index)\n            const army = area?.getArmyByUid(data.uid)\n            if (army) {\n                this.server.armyAutoBackIndexMap[army.uid] = backIndex\n                this.server.addMarchArmy(army.aIndex, army, index, speed, false)\n            }\n        })\n        return ''\n    }\n\n    // 移动军队\n    public async moveCellArmy(armys: ArmyShortInfo[], index: number, fastMode: boolean = false, isSameSpeed: boolean) {\n        if (!await this.isCanDoAction(armys)) {\n            viewHelper.showAlert('toast.battling_cant_operation')\n            return ''\n        }\n        let speed = fastMode ? -NOVICE_FIRST_MOVE_MUL : -NOVICE_ARMY_MOVE_MUL\n        speed = isSameSpeed ? this.server.getArmysMarchSameSpeed(armys, index, speed) : speed\n        armys.forEach(data => {\n            const area = this.server.getArea(data.index)\n            const army = area?.getArmyByUid(data.uid)\n            if (army) {\n                this.server.armyAutoBackIndexMap[army.uid] = -1\n                this.server.addMarchArmy(army.aIndex, army, index, speed, false)\n            }\n        })\n        return ''\n    }\n\n    // 取消行军\n    public async cancelMarch(uid: string) {\n        this.server.cancelMarch(uid)\n        return ''\n    }\n\n    // 取消行军\n    public async forceRevoke(uid: string) {\n        return ''\n    }\n\n    // 修建城市\n    public async createCity(index: number, id: number) {\n        const { err, data } = this.server.HD_CreateCity({ index, id })\n        if (!err && data) {\n            gameHpr.player.updateOutputByFlags(data.output)\n        }\n        return err\n    }\n\n    // 拆除城市\n    public async dismantleCity(index: number) {\n        const { err, data } = this.server.HD_DismantleCity({ index })\n        return err\n    }\n\n    public update(dt: number) {\n        if (this.pauseBattleAreaIndex > 0) {\n            const fsp = this.areaCenter.getArea(this.pauseBattleAreaIndex)?.getFspModel()\n            if (!fsp) {\n                if (mc.currWindName === 'novice') {\n                    this.pauseBattleAreaIndex = -1\n                    this.pauseBattleAreaFrameCount = 0\n                }\n            } else if (mc.currWindName === 'novice') {\n                this.pauseBattleAreaIndex = -1\n                this.pauseBattleAreaFrameCount = 0\n                fsp.setPause(false)\n            } else if (!fsp.isPause() && fsp.getCurrentFrameIndex() >= this.pauseBattleAreaFrameCount) {\n                fsp.setPause(true)\n            }\n        }\n    }\n\n    private OnUpdateWorldInfo(data: any) {\n        this.tempMainCityCell = null\n        data.list.forEach(m => this.updateWorldInfoOne(m.type, m['data_' + m.type]))\n        // 有更新地块信息发通知\n        if (this.tempHasUpdateCellInfo) {\n            this.emit(EventType.UPDATE_CELL_INFO)\n        }\n        // 添加关联\n        if (this.tempMainCityCell) {\n            this.updateMainCityRangeCell(this.tempMainCityCell)\n            this.tempMainCityCell = null\n        }\n        // 播放效果\n        if (this.notPlayNewCells.length > 0) {\n            this.emit(EventType.PLAY_NEW_CELL_EFFECT)\n        }\n    }\n\n    private updateWorldInfoOne(type: NotifyType, data: any) {\n        if (type === NotifyType.ADD_CELL) { //添加地块\n            const cell = this.mapCells[data.index]\n            if (cell) {\n                mapUionFindHelper.checkNearUnLock(cell)\n                const isPlayer = !!cell.owner\n                // 刷新边框线\n                this.addPlayerInfoByCell(data, cell)\n                // 刷新信息\n                cell?.updateInfo(data)\n                // 添加到等待播放特效的列表\n                if (cell.city?.id !== CITY_MAIN_NID && cell.isOwn()) {\n                    this.addNotPlayNewCells(data.index)\n                }\n                // 刷新关联\n                if (cell.getAcreage() > 1) {\n                    this.tempMainCityCell = cell\n                }\n                // 当前行军是否有这块地\n                this.updateMarchByIndex(data.index)\n                // 是否自己的地块\n                if (cell.isOwn() && !isPlayer) {\n                    gameHpr.player.addTodayOccupyCellCount(1)\n                }\n                this.tempHasUpdateCellInfo = true\n            }\n        } else if (type === NotifyType.REMOVE_CELLS) { //删除地块\n            data.forEach(index => {\n                this.mapCells[index]?.updateInfo({})\n                this.areaCenter.removeArea(index) //删除之后重新申请\n            })\n            this.tempHasUpdateCellInfo = true\n        } else if (type === NotifyType.DELETE_PLAYER) { //删除玩家\n            delete this.allPlayerInfos[data]\n        } else if (type === NotifyType.UPDATE_CELL_HP) { //刷新地块血量\n            // const cell = this.mapCells[data.index]\n            // cell.hp = data.hp\n            // this.areaCenter.getArea(data.index)?.updateHp(data.hp)\n        } else if (type === NotifyType.ADD_MARCH) { //添加行军\n            const march = this.marchs.find(m => m.uid === data.uid) || this.marchs.add(new MarchObj())\n            march.init(data)\n            this.emit(EventType.ADD_MARCH, march)\n        } else if (type === NotifyType.REMOVE_MARCH) { //删除行军\n            this.removeMarch(data.uid)\n        } else if (type === NotifyType.ADD_TRANSIT) { //添加运送\n            const index = gameHpr.player.getMainCityIndex()\n            if (data.targetIndex === index || data.startIndex === index) {\n                const transit = this.transits.find(m => m.uid === data.uid) || this.transits.add(new TransitObj())\n                transit.init(data)\n                this.emit(EventType.ADD_MARCH, transit)\n            }\n        } else if (type === NotifyType.REMOVE_TRANSIT) { //删除运送\n            const transit = this.transits.remove('uid', data)\n            transit && this.emit(EventType.REMOVE_MARCH, transit)\n        } else if (type === NotifyType.BATTLE_DIST) { //战斗分布\n            if (data.uids?.length) {\n                this.battleDistMap[data.index] = { list: data.uids }\n            } else {\n                delete this.battleDistMap[data.index]\n            }\n            this.emit(EventType.UPDATE_BATTLE_DIST_INFO)\n            this.updateMarchByIndex(data.index)\n        } else if (type === NotifyType.AREA_AVOID_WAR) { //免战分布\n            if (!data.time) {\n                delete this.avoidWarDistMap[data.index]\n            } else {\n                this.avoidWarDistMap[data.index] = new AvoidWarObj().init(data.index, data.time, data.type || 0)\n            }\n            this.emit(EventType.UPDATE_AVOIDWAR_DIST_INFO)\n        } else if (type === NotifyType.ADD_BTCITY) { //添加修建城市\n            this.btCityQueueMap[data.index] = new BTCityObj().init(data)\n            this.emit(EventType.UPDATE_BT_CITY, data.index)\n        } else if (type === NotifyType.REMOVE_BTCITY) { //删除修建城市\n            delete this.btCityQueueMap[data.index]\n            if (data.cell) {\n                this.mapCells[data.index]?.updateInfo(data.cell)\n                const area = this.areaCenter.getArea(data.index)\n                area?.updateBuilds(data.builds || []) //刷新区域的建筑信息\n                area?.updateCity(data.cell.cityId) //刷新区域的城市信息\n            }\n            this.emit(EventType.UPDATE_BT_CITY, data.index)\n        } else if (type === NotifyType.CHANGE_TITLE) { //改变玩家称号\n            const plr = this.getPlayerInfo(data.uid)\n            if (plr) {\n                plr.title = data.title\n                this.emit(EventType.UPDATE_PLAYER_NICKNAME, plr)\n            }\n        } else if (type === NotifyType.PLAYER_TOWER_LV) { //刷新玩家箭塔等级\n            const plr = this.getPlayerInfo(data.uid)\n            if (plr) {\n                plr.towerLvMap = data.towerLvMap || {}\n                this.emit(EventType.UPDATE_PLAYER_TOWER_LV, data)\n            }\n        } else if (type === NotifyType.PLAYER_POLICY) { //刷新玩家政策\n            const plr = this.getPlayerInfo(data.uid)\n            if (plr) {\n                plr.policys = gameHpr.fromSvrByPolicys(data.policys || {})\n            }\n        }\n    }\n\n    public getUserCells(uid: string) {\n        return this.allPlayerInfos[uid]?.cells\n    }\n\n    public getDecorationByIndex(index: number) {\n        return this.decorationsMap[index]\n    }\n\n    // 随机主城周围的土地\n    private randCityMud(cell: MapCellObj) {\n        if (cell.isAncient()) {\n            return\n        }\n        let muList = this.tempCityMudMap[cell.index]\n        if (!muList) {\n            muList = this.tempCityMudMap[cell.index] = []\n        }\n        muList.length = 0\n\n        this.random.setSeed(cell.index)\n        let mudIndex = this.random.get(0, mapHelper.cityMudList.length - 1)\n        let mudList = mapHelper.cityMudList[mudIndex]\n        for (let i = 0; i < mudList.length; i++) {\n            let targetX = cell.point.x + mudList[i][0]\n            let targetY = cell.point.y + mudList[i][1]\n            let index = targetY * mapHelper.MAP_SIZE.x + targetX\n            if (this.mapCells[index]) {\n                muList.push(index)\n                let info = { decorationActIndex: index, decorationJson: assetsMgr.getJsonData('decoration', mudList[i][2]) }\n                let list = this.decorationsMap[index]\n                if (!list) {\n                    this.decorationsMap[index] = list = []\n                }\n                list.push(info)\n            }\n        }\n    }\n\n    //获取主城土地\n    private getMudJsonByIndex(index: number) {\n        let list = this.decorationsMap[index]\n        if (list) {\n            for (let i = 0; i < list.length; i++) {\n                if (list[i].decorationJson.type === DecorationType.MUD || list[i].decorationJson.type === DecorationType.MUD_OUTER) {\n                    return list[i].decorationJson\n                }\n            }\n        }\n        return null\n    }\n\n    //获取装饰icon，主城土地icon需要转换一次\n    public getDecorationIcon(decorationJson: DecorationJItem, decorationActIndex: number) {\n        if (decorationJson.type == DecorationType.MUD || decorationJson.type == DecorationType.MUD_OUTER) {\n            let icon = this.tempMudIconMap[decorationActIndex]\n            if (!icon) {\n                //方向：左上右下\n                let directionList = [[-1, 0], [0, 1], [1, 0], [0, -1]]\n                let key = ''\n                let point = this.mapCells[decorationActIndex].point\n                for (let i = 0; i < directionList.length; i++) {\n                    let dir = directionList[i]\n                    let x = point.x + dir[0]\n                    let y = point.y + dir[1]\n                    let index = y * mapHelper.MAP_SIZE.x + x\n                    let nearMudJson = this.getMudJsonByIndex(index)\n                    if (nearMudJson) {\n                        if (decorationJson.type === DecorationType.MUD) {\n                            key += nearMudJson.type === DecorationType.MUD ? 1 : 0\n                        }\n                        else if (decorationJson.type === DecorationType.MUD_OUTER) {\n                            key += (nearMudJson.type === DecorationType.MUD || nearMudJson.type === DecorationType.MUD_OUTER) ? 1 : 0\n                        }\n                    }\n                    else {\n                        key += 0\n                    }\n                }\n                let targetId = decorationJson.type == DecorationType.MUD ? DECORATION_MUD_CONF[key] : DECORATION_MUD_OUTER_CONF[key]\n                if (typeof targetId == 'object') {\n                    this.random.setSeed(decorationActIndex)\n                    let endIndex = this.random.get(0, targetId.length - 1)\n                    targetId = targetId[endIndex]\n                }\n                let targetData = assetsMgr.getJsonData('decoration', targetId)\n                icon = this.tempMudIconMap[decorationActIndex] = targetData.icon\n            }\n            return icon\n        }\n        return decorationJson.icon\n    }\n\n    public getRoundId(x: number, y: number) {\n        let key = x + '_' + y\n        let landId = this.islandRoundMap[key]\n        return landId\n    }\n}"]}