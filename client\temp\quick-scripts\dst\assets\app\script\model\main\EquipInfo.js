
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/EquipInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3f147VIDmxJyaYoDBoW4SOI', 'EquipInfo');
// app/script/model/main/EquipInfo.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EquipEffectObj_1 = require("./EquipEffectObj");
// 装备信息
var EquipInfo = /** @class */ (function () {
    function EquipInfo() {
        this.uid = '';
        this.id = 0;
        this.attrs = [];
        this.lastAttrs = [];
        this.recastCount = 0;
        this.nextForgeFree = false;
        this.json = null;
        this.attack = 0;
        this.hp = 0;
        this.effects = null; //效果
        this.skillIntensify = null; //技能强化 0.技能id 1.技能等级
        this.mainAttrs = []; //主属性 用于显示
        this.tempLockEffect = 0; //临时 用于记录需要锁定的效果
        this.smeltEffects = []; //融炼的效果列表
    }
    EquipInfo.prototype.init = function (uid, id, attrs) {
        this.setId(uid, id);
        this.setAttr(attrs || []);
        return this;
    };
    EquipInfo.prototype.fromSvr = function (data) {
        this.setId(data.uid, data.id);
        this.updateInfo(data);
        return this;
    };
    EquipInfo.prototype.updateInfo = function (data) {
        this.setAttr(data.attrs);
        this.lastAttrs = data.lastAttrs || [];
        this.recastCount = data.recastCount || 0;
        this.nextForgeFree = !!data.nextForgeFree;
    };
    EquipInfo.prototype.strip = function () {
        return { uid: this.uid, attrs: ut.deepClone(this.attrs) };
    };
    EquipInfo.prototype.toDB = function () {
        return {
            uid: this.uid,
            attrs: this.attrs,
            lastAttrs: this.lastAttrs,
            recastCount: this.recastCount,
            nextForgeFree: this.nextForgeFree,
        };
    };
    Object.defineProperty(EquipInfo.prototype, "exclusive_pawn", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.exclusive_pawn) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EquipInfo.prototype, "restore_cost", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.restore_cost) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EquipInfo.prototype, "smelt_type", {
        get: function () { var _a, _b; return (_b = (_a = this.json) === null || _a === void 0 ? void 0 : _a.smelt_type) !== null && _b !== void 0 ? _b : -1; },
        enumerable: false,
        configurable: true
    });
    // 是否融炼装备
    EquipInfo.prototype.isSmelt = function () { return this.smeltEffects.length > 0; };
    EquipInfo.prototype.getSmeltCount = function () { return this.smeltEffects.length; };
    // 是否专属装备
    EquipInfo.prototype.isExclusive = function () { return !!this.exclusive_pawn; };
    Object.defineProperty(EquipInfo.prototype, "baseName", {
        get: function () { return 'equipText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(EquipInfo.prototype, "name", {
        // ui名字
        get: function () {
            if (this.isSmelt()) {
                return assetsMgr.lang('ui.smelting_equip_name_' + Math.min(2, this.smeltEffects.length), this.baseName);
            }
            return this.baseName;
        },
        enumerable: false,
        configurable: true
    });
    // 获取用于聊天的名字
    EquipInfo.prototype.getChatName = function () {
        if (this.isSmelt()) {
            return assetsMgr.lang('ui.smelting_equip_name_' + Math.min(2, this.smeltEffects.length), this.baseName);
        }
        return assetsMgr.lang(this.baseName);
    };
    EquipInfo.prototype.setId = function (uid, id) {
        var _a;
        if (uid) {
            this.uid = uid;
            this.id = Number(uid.split('_')[0]);
        }
        else if (id) {
            if (id > 10000) {
                id = Math.floor(id / 10000);
            }
            this.id = id;
            this.uid = id + '_0';
        }
        else {
            return;
        }
        this.json = assetsMgr.getJsonData('equipBase', this.id);
        if ((_a = this.json) === null || _a === void 0 ? void 0 : _a.skill_intensify) {
            this.skillIntensify = ut.stringToNumbers(this.json.skill_intensify, ',');
        }
        else {
            this.skillIntensify = null;
        }
    };
    EquipInfo.prototype.clean = function () {
        this.uid = '';
        this.id = 0;
        this.setAttr([]);
    };
    EquipInfo.prototype.setAttr = function (attrs) {
        this.attrs = (attrs || []).map(function (m) { return Array.isArray(m) ? { attr: m } : m; });
        this.updateAttr();
    };
    EquipInfo.prototype.updateAttr = function () {
        var _this = this;
        this.attack = 0;
        this.hp = 0;
        this.effects = [];
        this.mainAttrs = [];
        this.smeltEffects = [];
        var needTodayAdds = [];
        // 主属性
        this.attrs.forEach(function (m) {
            var _a = __read(m.attr, 5), fieldType = _a[0], type = _a[1], value = _a[2], odds = _a[3], viceId = _a[4];
            if (fieldType === 0) { //属性
                if (type === 1) {
                    _this.hp += value;
                }
                else if (type === 2) {
                    _this.attack += value;
                }
                var attr = _this.mainAttrs.find(function (v) { return v.type === type; });
                if (!attr) {
                    attr = { type: type, value: 0, initValue: 0, base: [] };
                    _this.mainAttrs.push(attr);
                }
                attr.value += value;
                if (!viceId) {
                    attr.initValue = value;
                    attr.base = _this.getMainAttrRangeValue(type);
                }
            }
            else if (fieldType === 2) { //效果
                _this.effects.push(new EquipEffectObj_1.default().init(type, value, odds));
                if (type === Enums_1.EquipEffectType.TODAY_ADD_HP) {
                    needTodayAdds.push([1, value]);
                }
                else if (type === Enums_1.EquipEffectType.TODAY_ADD_ATTACK) {
                    needTodayAdds.push([2, value]);
                }
                if (viceId && !_this.smeltEffects.has('id', viceId)) {
                    _this.smeltEffects.push({ type: type, id: viceId });
                }
            }
        });
        // 需要每日加的
        needTodayAdds.forEach(function (m) {
            var _a = __read(m, 2), type = _a[0], todayAdd = _a[1];
            var attr = _this.mainAttrs.find(function (v) { return v.type === type; });
            if (attr) {
                attr.todayAdd = todayAdd;
            }
            else {
                _this.mainAttrs.push({ type: type, todayAdd: todayAdd, value: 0, initValue: 0, base: [] });
            }
        });
        // 排序
        this.effects.sort(function (a, b) {
            var _a, _b;
            var as = ((_a = assetsMgr.getJsonData('equipEffect', a.type)) === null || _a === void 0 ? void 0 : _a.sort) || 0;
            var bs = ((_b = assetsMgr.getJsonData('equipEffect', b.type)) === null || _b === void 0 ? void 0 : _b.sort) || 0;
            return as - bs;
        });
    };
    EquipInfo.prototype.getMainAttrRangeValue = function (type) {
        if (type === 1) {
            return ut.stringToNumbers(this.json.hp, ',');
        }
        else if (type === 2) {
            return ut.stringToNumbers(this.json.attack, ',');
        }
        return [0, 0];
    };
    // 是否满属性
    EquipInfo.prototype.isMaxAttr = function () {
        if (this.attrs.length < 2 || this.isSmelt() || this.id === 6025) { //时光旗排除
            return false;
        }
        for (var i = 0, l = this.attrs.length; i < l; i++) {
            var _a = __read(this.attrs[i].attr, 5), fieldType = _a[0], tp = _a[1], value = _a[2], odds = _a[3], viceId = _a[4];
            if (viceId) {
                continue; //融炼数据不算
            }
            else if (fieldType === 0) {
                if (tp === 1) {
                    if (!!this.json.hp && ut.stringToNumbers(this.json.hp, ',')[1] !== value) {
                        return false;
                    }
                }
                else if (tp === 2) {
                    if (!!this.json.attack && ut.stringToNumbers(this.json.attack, ',')[1] !== value) {
                        return false;
                    }
                }
            }
            else if (fieldType === 2) {
                var json = assetsMgr.getJsonData('equipEffect', tp);
                if (json) {
                    if (json.value && ut.stringToNumbers(json.value, ',')[1] !== value) {
                        return false;
                    }
                    if (json.odds && ut.stringToNumbers(json.odds, ',')[1] !== odds) {
                        return false;
                    }
                }
            }
        }
        return true;
    };
    EquipInfo.prototype.checkExclusivePawn = function (id) {
        return this.exclusive_pawn === 0 || this.exclusive_pawn === id;
    };
    EquipInfo.prototype.getEffectByType = function (type) {
        return this.effects.find(function (m) { return m.type === type; });
    };
    EquipInfo.prototype.getOriginalAttrTypeMap = function () {
        var effectTypeMap = {};
        this.attrs.forEach(function (m) {
            var _a = __read(m.attr, 5), fieldType = _a[0], type = _a[1], value = _a[2], odds = _a[3], viceId = _a[4];
            if (fieldType === 2 && !viceId) {
                effectTypeMap[type] = true;
            }
        });
        return effectTypeMap;
    };
    return EquipInfo;
}());
exports.default = EquipInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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