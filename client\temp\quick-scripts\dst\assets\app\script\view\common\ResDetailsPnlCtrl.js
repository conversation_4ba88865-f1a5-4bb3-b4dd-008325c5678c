
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ResDetailsPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0d9cfExgSVA1ot0V+P7Ia/i', 'ResDetailsPnlCtrl');
// app/script/view/common/ResDetailsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ResDetailsPnlCtrl = /** @class */ (function (_super) {
    __extends(ResDetailsPnlCtrl, _super);
    function ResDetailsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.landsSv_ = null; // path://root/pages/lands_sv
        _this.sumNode_ = null; // path://root/pages/sum_n
        _this.buyAddOpNode_ = null; // path://root/pages/sum_n/5/buy_add_op_be_n
        _this.curLbl_ = null; // path://root/pages/cur_l
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.type = 0;
        _this.resMap = {};
        return _this;
    }
    ResDetailsPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ADD_OUTPUT_TIME] = this.onUpdateAddOutputTime, _a.enter = true, _a),
        ];
    };
    ResDetailsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.buyAddOpNode_.Child('val', cc.Label).string = Constant_1.ADD_OUTPUT_GOLD + '';
                return [2 /*return*/];
            });
        });
    };
    ResDetailsPnlCtrl.prototype.onEnter = function (type) {
        this.initResDist();
        this.tabsTc_.Tabs(type);
    };
    ResDetailsPnlCtrl.prototype.onRemove = function () {
    };
    ResDetailsPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    ResDetailsPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        this.showResDetails(Number(event.node.name));
    };
    // path://root/pages/sum_n/5/buy_add_op_be_n
    ResDetailsPnlCtrl.prototype.onClickBuyAddOp = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.world.isGameOver()) {
            return ViewHelper_1.viewHelper.showAlert('toast.gold_increase_output');
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return;
        }
        var type = this.type;
        var hasAdd = !!GameHelper_1.gameHpr.player.getAddOutputSurplusTime()[type];
        ViewHelper_1.viewHelper.showMessageBox(hasAdd ? 'ui.add_output_desc_1' : 'ui.add_output_desc_0', {
            params: [Constant_1.ADD_OUTPUT_GOLD, Constant_1.ADD_OUTPUT_RATIO, Constant_1.CTYPE_NAME[type]],
            ok: function () { return GameHelper_1.gameHpr.player.buyAddOutputTime(type).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.showResDetails(type);
                }
            }); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ResDetailsPnlCtrl.prototype.onUpdateAddOutputTime = function () {
        this.showResDetails(this.type);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ResDetailsPnlCtrl.prototype.initResDist = function () {
        var _a;
        var _this = this;
        var _b, _c;
        this.resMap = (_a = {}, _a[Enums_1.CType.CEREAL] = { arr: [], sum: 0 }, _a[Enums_1.CType.TIMBER] = { arr: [], sum: 0 }, _a[Enums_1.CType.STONE] = { arr: [], sum: 0 }, _a);
        (_c = (_b = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _b === void 0 ? void 0 : _b.cells) === null || _c === void 0 ? void 0 : _c.forEach(function (cell) {
            if (cell.isHasRes()) {
                var json_1 = cell.getResJson() || {};
                Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; }).forEach(function (m) {
                    var info = _this.resMap[Constant_1.RES_FIELDS_CTYPE[m]], val = json_1[m];
                    var it = info.arr.find(function (x) { return (x.cell.landId === cell.landId || x.cell.cityId === cell.cityId) && x.val === val; });
                    if (it) {
                        it.count += 1;
                    }
                    else {
                        info.arr.push({ cell: cell, val: val, count: 1 });
                    }
                    info.sum += val;
                });
            }
        });
        for (var key in this.resMap) {
            this.resMap[key].arr.sort(function (a, b) { return b.val - a.val; });
        }
    };
    ResDetailsPnlCtrl.prototype.showResDetails = function (type) {
        this.type = type;
        var player = GameHelper_1.gameHpr.player;
        var info = this.resMap[type], len = info.arr.length;
        this.landsSv_.Child('empty').active = len === 0;
        this.landsSv_.Items(info.arr, function (it, data, i) {
            it.Child('name').setLocaleKey(data.cell.getName());
            it.Child('val', cc.Label).string = data.val + '';
            it.Child('count', cc.Label).string = data.count + '';
            // it.Child('line').active = i <= len - 2
        });
        // 初始资源
        var output = player.isCapture() ? 0 : Constant_1.INIT_RES_OUTPUT, addNum = 0;
        this.sumNode_.Child('3/val', cc.Label).string = output + '';
        // 土地资源
        output += info.sum;
        this.sumNode_.Child('1/val', cc.Label).string = info.sum + '';
        // 内政加成 固定
        var policyAddRes = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.RES_OUTPUT);
        if (this.sumNode_.Child('2').active = policyAddRes > 0) {
            output += policyAddRes;
            this.sumNode_.Child('2/val', cc.Label).string = policyAddRes + '';
        }
        // 商城购买 百分比
        var time = player.getAddOutputSurplusTime()[type] || 0;
        addNum = time > 0 ? Math.floor(output * Constant_1.ADD_OUTPUT_RATIO * 0.01) : 0;
        output += addNum;
        this.sumNode_.Child('5/desc/val').setLocaleKey('ui.add_output_desc', '+' + Constant_1.ADD_OUTPUT_RATIO);
        this.sumNode_.Child('5/val', cc.Label).string = addNum + '';
        // 购买加成
        if (this.sumNode_.Child('5').active = !GameHelper_1.gameHpr.isNoviceMode) {
            this.updateAddOutputStateTime(this.sumNode_.Child('5/desc/state'), time);
        }
        // 粮耗
        var cost = type === Enums_1.CType.CEREAL ? player.getCerealConsume() : 0;
        if (this.sumNode_.Child('7').active = cost > 0) {
            output -= cost;
            this.sumNode_.Child('7/val', cc.Label).string = '-' + cost;
        }
        // 总
        this.sumNode_.Child('6/val', cc.Label).string = output + '';
        var cap = type === Enums_1.CType.CEREAL ? player.getGranaryCap() : player.getWarehouseCap();
        this.curLbl_.setLocaleKey('ui.cur_res_cap', GameHelper_1.gameHpr.getCountByCType(type) + '/' + cap);
    };
    ResDetailsPnlCtrl.prototype.updateAddOutputStateTime = function (node, time) {
        var hasAdd = time > 0, color = hasAdd ? '#59A733' : '#D7634D';
        this.buyAddOpNode_.Child('desc').setLocaleKey(hasAdd ? 'ui.button_lengthen' : 'ui.button_enable');
        node.children.forEach(function (m) { return m.Color(color); });
        node.Child('val').setLocaleKey(hasAdd ? 'ui.takeeffecting' : 'ui.not_takeeffect');
        node.Child('s').active = hasAdd;
        var lbl = node.Child('time', cc.LabelTimer);
        if (lbl.setActive(hasAdd)) {
            lbl.run(time * 0.001);
        }
    };
    ResDetailsPnlCtrl = __decorate([
        ccclass
    ], ResDetailsPnlCtrl);
    return ResDetailsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResDetailsPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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