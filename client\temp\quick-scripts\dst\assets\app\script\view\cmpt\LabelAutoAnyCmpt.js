
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/LabelAutoAnyCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '49a21eCQZZHEK24pMOr/5yy', 'LabelAutoAnyCmpt');
// app/script/view/cmpt/LabelAutoAnyCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 自动换行
 */
var LabelAutoAnyCmpt = /** @class */ (function (_super) {
    __extends(LabelAutoAnyCmpt, _super);
    function LabelAutoAnyCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.maxWidth = 0;
        _this.label = null;
        _this.preContent = '';
        return _this;
    }
    LabelAutoAnyCmpt.prototype.onLoad = function () {
        this.label = this.getComponent(cc.Label);
    };
    LabelAutoAnyCmpt.prototype.check = function () {
        if (!this.label) {
            this.onLoad();
        }
        if (this.preContent === this.label.string) {
            return this.node.width * this.node.scaleX;
        }
        this.preContent = this.label.string;
        this.label._forceUpdateRenderData();
        if (this.label.overflow === cc.Label.Overflow.NONE) {
            if (this.node.width > this.maxWidth) {
                this.label.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
                this.node.width = this.maxWidth;
                this.label._forceUpdateRenderData();
            }
        }
        else if (this.node.height < this.label.lineHeight * 2) {
            this.label.overflow = cc.Label.Overflow.NONE;
            this.label._forceUpdateRenderData();
        }
        return this.node.width * this.node.scaleX;
    };
    __decorate([
        property
    ], LabelAutoAnyCmpt.prototype, "maxWidth", void 0);
    LabelAutoAnyCmpt = __decorate([
        ccclass
    ], LabelAutoAnyCmpt);
    return LabelAutoAnyCmpt;
}(cc.Component));
exports.default = LabelAutoAnyCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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