
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/CreateAlliancePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0115ajC6cpCfY3tgmkmAD7C', 'CreateAlliancePnlCtrl');
// app/script/view/build/CreateAlliancePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var RollListCmpt_1 = require("../cmpt/RollListCmpt");
var ccclass = cc._decorator.ccclass;
var CreateAlliancePnlCtrl = /** @class */ (function (_super) {
    __extends(CreateAlliancePnlCtrl, _super);
    function CreateAlliancePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconListNode_ = null; // path://root/icon/icon_list_n
        _this.inputEb_ = null; // path://root/input_eb
        _this.needNode_ = null; // path://root/need_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.rollList = null;
        _this.isKarmicMahjong = false; //是否血战到底模式
        return _this;
    }
    CreateAlliancePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CreateAlliancePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var datas, i;
            return __generator(this, function (_a) {
                this.rollList = this.iconListNode_.getComponent(RollListCmpt_1.default);
                datas = [];
                for (i = 0; i < 50; i++) {
                    datas.push({ id: (1001 + i) * 100 + 1, index: i });
                }
                this.rollList.init(datas);
                return [2 /*return*/];
            });
        });
    };
    CreateAlliancePnlCtrl.prototype.onEnter = function () {
        this.inputEb_.string = '';
        this.isKarmicMahjong = GameHelper_1.gameHpr.world.isKarmicMahjong();
        var icon = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.LAST_ALLI_ICON) || (ut.random(1001, 1050) * 100 + 1);
        this.rollList.reset(icon);
        if (this.needNode_.active = !this.isKarmicMahjong) {
            this.needNode_.Child('cost').Items(GameHelper_1.gameHpr.stringToCTypes(Constant_1.CREATE_ALLI_COST) || [], function (it, cost) { return ViewHelper_1.viewHelper.updateCostViewOne(it, cost, true); });
        }
        /*  if (gameHpr.getPlayerOweCellCount() >= CREATE_ALLI_COND || this.isKarmicMahjong) { */
        this.buttonsNode_.Swih('ok_be');
        /* } else {
            this.buttonsNode_.Swih('cond')[0].Child('val').setLocaleKey('ui.create_alli_cond_desc', CREATE_ALLI_COND)
        } */
    };
    CreateAlliancePnlCtrl.prototype.onRemove = function () {
    };
    CreateAlliancePnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/ok_be
    CreateAlliancePnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var _a;
        var name = this.inputEb_.string.trim();
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_alli_name');
        }
        else if (ut.getStringLen(name) > 12 || GameHelper_1.gameHpr.getTextNewlineCount(name) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        var icon = ((_a = this.rollList.getCurHitItemData()) === null || _a === void 0 ? void 0 : _a.id) || (ut.random(1001, 1050) * 100 + 1);
        if (this.isKarmicMahjong) { //血战到底模式 不提示
            this.do(name, icon);
        }
        else {
            ViewHelper_1.viewHelper.showMessageBox('ui.create_alli_tip', { ok: function () { return _this.do(name, icon); }, cancel: function () { } });
        }
    };
    // path://root/icon/page_nbe
    CreateAlliancePnlCtrl.prototype.onClickPage = function (event, data) {
        if (!this.rollList.isRolling()) {
            var val = event.target.name === '0' ? 1 : -1;
            this.rollList.rollPage(val);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 创建联盟
    CreateAlliancePnlCtrl.prototype.do = function (name, icon) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        err = '';
                        if (!this.isKarmicMahjong) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.alliance.createAllianceInfo(name, icon)];
                    case 1:
                        err = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, GameHelper_1.gameHpr.alliance.createAlliance(name, icon)];
                    case 3:
                        err = _a.sent();
                        _a.label = 4;
                    case 4:
                        if (err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name')];
                        }
                        else if (err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.hide();
                        ViewHelper_1.viewHelper.showAlert('toast.create_succeed');
                        return [2 /*return*/];
                }
            });
        });
    };
    CreateAlliancePnlCtrl = __decorate([
        ccclass
    ], CreateAlliancePnlCtrl);
    return CreateAlliancePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CreateAlliancePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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