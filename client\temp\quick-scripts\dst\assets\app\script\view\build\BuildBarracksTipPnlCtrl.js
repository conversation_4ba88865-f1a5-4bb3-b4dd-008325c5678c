
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildBarracksTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b6f27kAUbxHxbqi9Y+b+jpz', 'BuildBarracksTipPnlCtrl');
// app/script/view/build/BuildBarracksTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var BuildBarracksTipPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildBarracksTipPnlCtrl, _super);
    function BuildBarracksTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        _this.noArmyLbl_ = null; // path://root/content_n/0/no_army_l
        _this.name0Lbl_ = null; // path://root/content_n/0/bg/1/name0_l
        _this.moveToLbl_ = null; // path://root/content_n/1/move_to_l
        _this.createArmyLbl_ = null; // path://root/content_n/1/create_army_l
        _this.name1Lbl_ = null; // path://root/content_n/1/bg/1/name1_l
        _this.noLongerTge_ = null; // path://root/no_longer_t
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BuildBarracksTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuildBarracksTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildBarracksTipPnlCtrl.prototype.onEnter = function (data, key) {
        if (key === void 0) { key = ''; }
        var name = data ? 1 : 0;
        this.contentNode_.Swih(name);
        if (!data) {
            this.name0Lbl_.string = assetsMgr.lang('ui.default_army_name') + 1;
        }
        else {
            if (key) {
                this.noArmyLbl_.setLocaleKey('ui.now_havent_army');
                this.moveToLbl_.setLocaleKey('ui.move_army_to_city', key);
                this.createArmyLbl_.setLocaleKey('ui.create_army_desc', key);
            }
            else {
                this.noArmyLbl_.setLocaleKey('ui.now_havent_army_cure');
                this.moveToLbl_.setLocaleKey('ui.move_army_to_city_cure');
                this.createArmyLbl_.setLocaleKey('ui.create_army_desc_cure');
            }
            this.name1Lbl_.string = assetsMgr.lang('ui.default_army_name') + data.pawns.length;
        }
        this.noLongerTge_.isChecked = !!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL);
    };
    BuildBarracksTipPnlCtrl.prototype.onRemove = function () {
        GameHelper_1.gameHpr.setNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL, this.noLongerTge_.isChecked);
    };
    BuildBarracksTipPnlCtrl.prototype.onClean = function () {
    };
    BuildBarracksTipPnlCtrl = __decorate([
        ccclass
    ], BuildBarracksTipPnlCtrl);
    return BuildBarracksTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildBarracksTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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