
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/Tower.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '990a1CEUNNGA7vHCtYXoQ8T', 'Tower');
// app/script/model/fsp/Tower.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var AStarRange_1 = require("../../common/astar/AStarRange");
var SearchRange_1 = require("../../common/astar/SearchRange");
var Enums_1 = require("../../common/constant/Enums");
var PawnObj_1 = require("../area/PawnObj");
var BehaviorTree_1 = require("../behavior/BehaviorTree");
var Fighter_1 = require("./Fighter");
// 箭塔
var Tower = /** @class */ (function (_super) {
    __extends(Tower, _super);
    function Tower() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.area = null;
        return _this;
    }
    Tower.prototype.initTower = function (data, id, lv, camp, area, ctrl) {
        var _a, _b, _c, _d;
        this.area = area;
        this.entity = new PawnObj_1.default().init(id, null, lv);
        this.entity.aIndex = area.index;
        this.entity.uid = data.uid;
        this.entity.point.set(data.point);
        this.attackIndex = (_a = data.attackIndex) !== null && _a !== void 0 ? _a : 0;
        this.waitRound = (_b = data.waitRound) !== null && _b !== void 0 ? _b : 0;
        this.roundCount = (_c = data.roundCount) !== null && _c !== void 0 ? _c : 0;
        this.attackCount = (_d = data.attackCount) !== null && _d !== void 0 ? _d : 0;
        this.camp = camp;
        this.ctrl = ctrl;
        this.blackboard = { 0: {} };
        this.behavior = new BehaviorTree_1.default().load(this.entity.behaviorId, this);
        this.searchRange = new SearchRange_1.default().init(ctrl.checkIsBattleArea.bind(ctrl));
        this.astar = new AStarRange_1.default().init(ctrl.checkHasPassToState.bind(ctrl));
        return this;
    };
    Tower.prototype.md5 = function () {
        return this.attackIndex + '_' + this.getPoint().ID() + '_' + this.entity.id + '_' + this.camp + '_' + this.getAttack();
    };
    Tower.prototype.getAreaIndex = function () { return this.area.index; };
    Tower.prototype.isPawn = function () { return false; };
    Tower.prototype.isBuild = function () { return false; };
    Tower.prototype.isTower = function () { return true; };
    Tower.prototype.isNoncombat = function () { return false; };
    Tower.prototype.isFlag = function () { return false; };
    Tower.prototype.isHero = function () { return false; };
    Tower.prototype.isBoss = function () { return false; };
    Tower.prototype.isDie = function () {
        return this.area.curHp <= 0;
    };
    Tower.prototype.updateTowerInfo = function (id, lv) {
        this.entity.id = id;
        this.entity.lv = lv;
        this.entity.updateAttrJson();
    };
    // 切换状态
    Tower.prototype.changeState = function (state, data) {
        var _a;
        if (state === Enums_1.PawnState.ATTACK) {
            var url = ((_a = this.entity.baseJson) === null || _a === void 0 ? void 0 : _a.attack_sound) || '';
            if (url && this.area.active) {
                audioMgr.playSFX('pawn/' + url);
            }
        }
    };
    // 受击
    Tower.prototype.onHit = function (damage, attackerOwners) {
        return { damage: 0, heal: 0, hitShield: 0 };
    };
    // 回血
    Tower.prototype.onHeal = function (val) {
        return 0;
    };
    // 增加怒气
    Tower.prototype.addAnger = function (val) {
        return 0;
    };
    // 设置怒气
    Tower.prototype.setAnger = function (val) {
    };
    Tower.prototype.getBuff = function (type) {
        return null;
    };
    // 添加buff
    Tower.prototype.addBuff = function (type, provider, lv) {
        if (lv === void 0) { lv = 1; }
        return null;
    };
    Tower.prototype.checkTriggerBuff = function (type) {
        return null;
    };
    Tower.prototype.checkTriggerBuffOr = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
        return null;
    };
    Tower.prototype.isHasBuff = function (type) {
        return false;
    };
    // 删除buff
    Tower.prototype.removeMultiBuff = function () {
        var types = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            types[_i] = arguments[_i];
        }
    };
    // 删除所有护盾buff
    Tower.prototype.removeShieldBuffs = function () {
    };
    Tower.prototype.strip = function () {
        var res = _super.prototype.strip.call(this);
        //@ts-ignore
        res.towerLv = this.entity.lv;
        //@ts-ignore
        res.point = this.entity.point.toJson();
        return res;
    };
    return Tower;
}(Fighter_1.default));
exports.default = Tower;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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