
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/RiskTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '498e66glKtG2KeNDjE87LOh', 'RiskTipPnlCtrl');
// app/script/view/main/RiskTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var RiskTipPnlCtrl = /** @class */ (function (_super) {
    __extends(RiskTipPnlCtrl, _super);
    function RiskTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.rootNode_ = null; // path://root_n
        _this.noLongerTge_ = null; // path://root_n/no_longer_t
        //@end
        _this.selectArmys = [];
        _this.targetCell = null;
        _this.cb = null;
        return _this;
    }
    RiskTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    RiskTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    RiskTipPnlCtrl.prototype.onEnter = function (selectArmys, targetCell, cb) {
        var _a;
        this.selectArmys = selectArmys;
        this.targetCell = targetCell;
        this.cb = cb;
        // 前3次不显示 不再提示
        var count = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_RISK_COUNT)) !== null && _a !== void 0 ? _a : 0;
        if (this.noLongerTge_.setActive(count >= 3)) {
            this.noLongerTge_.isChecked = false;
        }
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_RISK_COUNT, count + 1);
        // 延迟关闭
        this.delayClose();
    };
    RiskTipPnlCtrl.prototype.onRemove = function () {
        if (this.noLongerTge_.getActive()) {
            GameHelper_1.gameHpr.setNoLongerTip('show_risk_tip', this.noLongerTge_.isChecked);
        }
        this.cb = null;
    };
    RiskTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    RiskTipPnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    // path://root_n/buttons/forecast_be
    RiskTipPnlCtrl.prototype.onClickForecast = function (event, data) {
        var _this = this;
        var _a;
        if (!((_a = this.selectArmys) === null || _a === void 0 ? void 0 : _a.length) || !this.targetCell) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
        }
        ViewHelper_1.viewHelper.showPnl('main/BattleForecast', this.selectArmys, this.targetCell).then(function () { return _this.isValid && _this.hide(); });
    };
    // path://root_n/buttons/ok_be
    RiskTipPnlCtrl.prototype.onClickOk = function (event, data) {
        this.cb && this.cb();
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 被动打开时延时关闭
    RiskTipPnlCtrl.prototype.delayClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.closeNode_.active = false;
                        return [4 /*yield*/, ut.wait(Constant_1.DELAY_CLOSE_PNL_TIME, this)];
                    case 1:
                        _a.sent();
                        if (this.isValid) {
                            this.closeNode_.active = true;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    RiskTipPnlCtrl = __decorate([
        ccclass
    ], RiskTipPnlCtrl);
    return RiskTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = RiskTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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