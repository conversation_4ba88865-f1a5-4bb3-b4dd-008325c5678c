{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\PointsetsChancePnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAQ,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAc;IAAlE;QAAA,qEAwCC;QAtCG,0BAA0B;QAClB,iBAAW,GAAY,IAAI,CAAA,CAAC,+BAA+B;QACnE,MAAM;QAEW,YAAM,GAAG;YACtB,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;YAC9B,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;YAC/B,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;SAEnC,CAAA;;QAsBD,iHAAiH;QACjH,2BAA2B;QAC3B,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IAErH,CAAC;IA3BU,gDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,yCAAQ,GAArB;;;;;;KACC;IAEM,wCAAO,GAAd,UAAe,IAAS;QACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,IAAI;YACzC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;YACnD,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACrD,CAAC,CAAC,CAAA;IACN,CAAC;IAEM,yCAAQ,GAAf;IACA,CAAC;IAEM,wCAAO,GAAd;IACA,CAAC;IA/BgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CAwC1C;IAAD,6BAAC;CAxCD,AAwCC,CAxCmD,EAAE,CAAC,WAAW,GAwCjE;kBAxCoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass } = cc._decorator;\n\n@ccclass\nexport default class PointsetsChancePnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private chanceNode_: cc.Node = null // path://root/content/chance_n\n    //@end\n\n    private readonly CHANCE = [\n        { count: 'x1', chance: '90%' },\n        { count: 'x3', chance: '9.5%' },\n        { count: 'x27', chance: '0.5%' },\n        // { count: 'x81', chance: '0.1%' },\n    ]\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: any) {\n        this.chanceNode_.Items(this.CHANCE, (it, data) => {\n            it.Child('count/val', cc.Label).string = data.count\n            it.Child('chance', cc.Label).string = data.chance\n        })\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}