{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\CLoginInfoPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA+D;AAC/D,qDAA4D;AAC5D,0DAAqD;AACrD,6DAAyD;AACzD,6DAA4D;AAC5D,wDAAmD;AACnD,kEAA6D;AAIrD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA+C,qCAAc;IAA7D;QAAA,qEAyIC;QAvIG,0BAA0B;QAClB,aAAO,GAAgB,IAAI,CAAA,CAAC,4BAA4B;QACxD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;QAC7D,MAAM;QAEW,qBAAe,GAAY,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAE3D,SAAS;QACQ,sBAAgB,GAAG;YAChC,aAAK,CAAC,SAAS;YACf,aAAK,CAAC,SAAS;YACf,aAAK,CAAC,UAAU;YAChB,aAAK,CAAC,WAAW;YACjB,aAAK,CAAC,QAAQ;SACjB,CAAA;QAEO,UAAI,GAAc,IAAI,CAAA;QACtB,aAAO,GAAY,IAAI,CAAA;;IAsHnC,CAAC;IApHU,2CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,oCAAQ,GAArB;;;gBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;;;KACpC;IAEM,mCAAO,GAAd;QACI,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAEM,oCAAQ,GAAf;IACA,CAAC;IAEM,mCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,gCAAgC;IAChC,uCAAW,GAAX,UAAY,KAA0B,EAAE,KAAa;QAArD,iBAqCC;;QApCS,IAAA,KAAiB,KAAK,CAAC,MAAM,CAAC,IAAI,EAAhC,EAAE,QAAA,EAAE,MAAM,YAAsB,CAAA;QACxC,IAAI,OAAA,IAAI,CAAC,OAAO,0CAAE,EAAE,MAAK,EAAE,WAAI,IAAI,CAAC,OAAO,0CAAE,UAAU,GAAE,EAAE;YACvD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,OAAM;aACT;iBAAM,IAAI,OAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,aAAK,CAAC,QAAQ,EAAE,EAAE,MAAM;gBACjE,uBAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;oBAC5D,IAAI,KAAI,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE;wBACtB,KAAI,CAAC,kBAAkB,CAAC,KAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;qBAC5C;gBACL,CAAC,CAAC,CAAA;gBACF,OAAM;aACT;YACD,IAAM,KAAK,GAAG,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3D,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,OAAO,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,EAAE,UAAC,EAAW,IAAO,EAAE,IAAI,KAAI,CAAC,kBAAkB,CAAC,KAAI,CAAC,OAAO,CAAC,CAAA,CAAC,CAAC,CAAC,CAAA;aAC1H;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACxC;aAAM,IAAI,MAAM,EAAE;YACf,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;gBACjC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBACzD,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACrF;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;gBACxC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBACzD,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACrF;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,UAAU,EAAE;gBACzC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC1D,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACvF;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAAE;gBAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;gBAC9D,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;aAC/D;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;gBACvC,IAAM,IAAE,GAAG,MAAM,CAAC,EAAE,EAAE,GAAG,GAAG,wBAAa,CAAC,IAAE,CAAC,CAAA;gBAC7C,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAA;gBACnJ,uBAAU,CAAC,OAAO,CAAC,+BAA+B,EAAE,IAAE,EAAE,IAAI,CAAC,CAAA;aAChE;SACJ;IACL,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEzG,gCAAI,GAAZ;QAAA,iBA8BC;QA7BG,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,OAAA,CAAC,CAAC,IAAI,0CAAE,IAAI,MAAK,cAAM,CAAC,cAAc,CAAA,EAAA,CAAC,CAAA;QAC5F,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAA5B,CAA4B,CAAC,CAAA;QAC9F,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;;YACpC,IAAM,MAAM,GAAG,IAAI,kBAAQ,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACrD,EAAE,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,QAAA,EAAE,CAAA;YACjC,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACtD,uBAAU,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAI,CAAC,GAAG,EAAE,KAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YACnF,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,CAAA;YACzF,IAAA,KAAA,OAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,EAAhC,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,EAAE,QAAA,EACX,MAAM,SAAG,KAAI,CAAC,OAAO,0CAAE,EAAE,EACzB,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC,EAAE,EACxB,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,EACvB,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC5B,IAAM,MAAM,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAA;YAC1C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAA;YACrB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAClD,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;YACvE,IAAM,QAAQ,GAAG,GAAG,WAAI,KAAI,CAAC,OAAO,0CAAE,UAAU,GAAE,CAAA;YAClD,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAA;YACxB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YAC9C,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,YAAY,GAAG,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAA;YACzF,IAAI,GAAG,EAAE;gBACL,KAAK,GAAG,EAAE,GAAG,CAAC,CAAA;aACjB;QACL,CAAC,CAAC,CAAA;QACF,IAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAA;QACjH,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;IAClC,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,IAAa,EAAE,MAAe;QAAzD,iBAiBC;QAhBG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACtD,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;YACD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,WAAW,EAA5B,CAA4B,CAAC,CAAA;YAC9H,IAAI,IAAI,EAAE;gBACN,uBAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;aAC1D;iBAAM;gBACH,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAA;aAC9C;YACD,IAAI,KAAI,CAAC,OAAO,EAAE;gBACd,KAAI,CAAC,IAAI,EAAE,CAAA;gBACX,KAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,CAAC,CAAA;aAC1C;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAxIgB,iBAAiB;QADrC,OAAO;OACa,iBAAiB,CAyIrC;IAAD,wBAAC;CAzID,AAyIC,CAzI8C,EAAE,CAAC,WAAW,GAyI5D;kBAzIoB,iBAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { HERO_OPT_GIFT } from \"../../common/constant/Constant\";\nimport { CType, TCType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport CTypeObj from \"../../model/common/CTypeObj\";\nimport PortrayalInfo from \"../../model/common/PortrayalInfo\";\nimport TaskModel from \"../../model/common/TaskModel\";\nimport TaskObj from \"../../model/common/TaskObj\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class CLoginInfoPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private descRt_: cc.RichText = null // path://root/title/desc_rt\n    private contentNode_: cc.Node = null // path://root/content_n\n    //@end\n\n    private readonly ITEM_ADAPT_SIZE: cc.Size = cc.size(64, 64)\n\n    // 可点击的类型\n    private readonly INTERACTABLE_ARR = [\n        CType.PAWN_SKIN,\n        CType.HEAD_ICON,\n        CType.CHAT_EMOJI,\n        CType.HERO_DEBRIS,\n        CType.HERO_OPT\n    ]\n\n    private task: TaskModel = null\n    private curTask: TaskObj = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.task = this.getModel('task')\n    }\n\n    public onEnter() {\n        this.init()\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/content_n/item_be\n    onClickItem(event: cc.Event.EventTouch, _data: string) {\n        const { id, reward } = event.target.Data\n        if (this.curTask?.id === id && this.curTask?.isCanClaim()) {\n            if (!this.curTask) {\n                return\n            } else if (this.curTask.rewards[0]?.type === CType.HERO_OPT) { //自选礼包\n                viewHelper.showHeroOptSelect(this.curTask.rewards[0].id).then(id => {\n                    if (this.isValid && !!id) {\n                        this.claimGeneralReward(this.curTask, id)\n                    }\n                })\n                return\n            }\n            const items = gameHpr.checkRewardFull(this.curTask.rewards)\n            if (items.length > 0) {\n                return viewHelper.showPnl('common/ResFullTip', items, (ok: boolean) => { ok && this.claimGeneralReward(this.curTask) })\n            }\n            this.claimGeneralReward(this.curTask)\n        } else if (reward) {\n            if (reward.type === CType.PAWN_SKIN) {\n                const json = assetsMgr.getJsonData('pawnSkin', reward.id)\n                viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [json] })\n            } else if (reward.type === CType.HEAD_ICON) {\n                const json = assetsMgr.getJsonData('headIcon', reward.id)\n                viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'headicon', list: [json] })\n            } else if (reward.type === CType.CHAT_EMOJI) {\n                const json = assetsMgr.getJsonData('chatEmoji', reward.id)\n                viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'chat_emoji', list: [json] })\n            } else if (reward.type === CType.HERO_DEBRIS) {\n                const json = assetsMgr.getJsonData('portrayalBase', reward.id)\n                viewHelper.showPnl('common/PortrayalBaseInfo', json, 'shop')\n            } else if (reward.type === CType.HERO_OPT) {\n                const id = reward.id, arr = HERO_OPT_GIFT[id]\n                const list = arr ? arr.map(m => new PortrayalInfo().init(m)) : assetsMgr.getJson('portrayalBase').datas.map(m => new PortrayalInfo().init(m.id, m))\n                viewHelper.showPnl('common/SelectPortrayalPreview', id, list)\n            }\n        }\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private init() {\n        let dayNo = 0\n        this.task.updateGeneralTaskState()\n        this.curTask = this.task.getGeneralTasks().find(m => m.cond?.type === TCType.SIGN_DAY_COUNT)\n        const datas = assetsMgr.getJson('generalTask').datas.filter(m => m.cond.startsWith('1002,0,'))\n        this.contentNode_.Items(datas, (it, json) => {\n            const reward = new CTypeObj().fromString(json.reward)\n            it.Data = { id: json.id, reward }\n            const root = it.Child('root'), noNode = it.Child('no')\n            viewHelper.updateItemByCTypeOne(root, reward, this.key, this.ITEM_ADAPT_SIZE, true)\n            root.Child('text').active = reward.type === CType.HERO_DEBRIS || reward.type === CType.HERO_OPT\n            const [a, b, no] = json.cond.split(','),\n                taskId = this.curTask?.id,\n                ing = taskId === json.id,\n                done = it.Child('done'),\n                reddot = it.Child('dot')\n            const isDone = !taskId || taskId > json.id\n            done.active == isDone\n            noNode.opacity = root.opacity = isDone ? 150 : 255\n            noNode.Color(ing ? '#936E5A' : '#756963').setLocaleKey('ui.day_no', no)\n            const canClaim = ing && this.curTask?.isCanClaim()\n            reddot.active = canClaim\n            it.Component(cc.MultiFrame).setFrame(canClaim)\n            it.Component(cc.Button).interactable = this.INTERACTABLE_ARR.has(reward.type) || canClaim\n            if (ing) {\n                dayNo = no - 1\n            }\n        })\n        const str = assetsMgr.lang('ui.desc_daily_sign_in', dayNo).replace('<color=#21DE29>', '<color=#21DE29><size=30>')\n        this.descRt_.setLocaleKey(str)\n    }\n\n    private claimGeneralReward(data: TaskObj, heroId?: number) {\n        this.task.claimGeneralTaskReward(data.id, heroId).then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            }\n            const hero = heroId ? new CTypeObj().init(CType.HERO_DEBRIS, heroId, 3) : data.rewards.find(m => m.type === CType.HERO_DEBRIS)\n            if (hero) {\n                viewHelper.showGainPortrayalDebris(hero.id, hero.count)\n            } else {\n                gameHpr.addGainMassage(data.rewards)\n                viewHelper.showAlert('toast.claim_succeed')\n            }\n            if (this.isValid) {\n                this.init()\n                this.emit(EventType.UPDATE_ACITIVITIES)\n            }\n        })\n    }\n}\n"]}