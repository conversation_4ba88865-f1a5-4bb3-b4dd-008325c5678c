{"version": 3, "sources": ["assets\\app\\script\\view\\common\\ChatSelectEmojiPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA4D;AAC5D,2DAA0D;AAGlD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAc;IAAlE;QAAA,qEA6HC;QA3HG,0BAA0B;QAClB,kBAAY,GAAY,IAAI,CAAA,CAAC,yBAAyB;QACtD,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QACrE,MAAM;QAEW,cAAQ,GAAW,uBAAuB,CAAA;QACnD,gBAAU,GAAY,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QAErC,UAAI,GAAc,IAAI,CAAA;QACtB,QAAE,GAAa,IAAI,CAAA;;IAiH/B,CAAC;IA/GU,gDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,yCAAQ,GAArB;;;gBACI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;;;KACpC;IAEM,wCAAO,GAAd,UAAe,EAAY;QACvB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,SAAS;QACT,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IACzE,CAAC;IAEM,yCAAQ,GAAf;QACI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAA;IAClB,CAAC;IAEM,wCAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,2CAA2C;IAC3C,4CAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IACxB,CAAC;IAED,qCAAqC;IACrC,+CAAc,GAAd,UAAe,KAA0B,EAAE,IAAY;QACnD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QACtB,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAED,0BAA0B;IAC1B,4CAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;IAC3B,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEzG,8CAAa,GAArB;QAAA,iBAuBC;QAtBG,IAAM,IAAI,GAAU,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAA;QAC1F,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;QACvB,SAAS;QACT,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAChD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,CAAA;YACnD,IAAI,IAAI,KAAK,CAAC,EAAE;gBACZ,SAAQ;aACX;iBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;aACpB;SACJ;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;SACzE;QACD,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAjB,CAAiB,CAAC,CAAA;QACtC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAC,EAAE,EAAE,IAAI;YAC/C,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAA;YACjB,IAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC3B,GAAG,CAAC,KAAK,GAAG,CAAC,CAAA;YACb,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,UAAU,CAAC,EAA/C,CAA+C,CAAC,CAAA;QAC/G,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,4CAAW,GAAnB,UAAoB,EAAU;QAC1B,IAAM,IAAI,GAAU,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAA;QAC1F,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE,EAAX,CAAW,CAAC,CAAA;QACtC,IAAI,EAAE,EAAE;YACJ,EAAE,CAAC,KAAK,IAAI,CAAC,CAAA;SAChB;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAA,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAA;SAC9B;QACD,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,qBAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAC1E,CAAC;IAEO,6CAAY,GAApB,UAAqB,IAAY;QAAjC,iBAUC;QATG,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,CAAA;QACnE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,CAAC;YAChC,IAAM,EAAE,GAAG,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YAC3B,IAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC3B,qBAAS,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,UAAU,CAAC,EAA/C,CAA+C,CAAC,CAAA;QAC1G,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,gDAAe,GAAvB,UAAwB,IAAY;QAChC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAA;QACf,IAAM,KAAK,GAAa,EAAE,CAAA;QAC1B,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAArD,CAAqD,CAAC,CAAA;QACxG,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,aAAa;YAC3B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAA;SACnD;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IA5HgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA6H1C;IAAD,6BAAC;CA7HD,AA6HC,CA7HmD,EAAE,CAAC,WAAW,GA6HjE;kBA7HoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { Pre<PERSON><PERSON><PERSON> } from \"../../common/constant/Enums\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport UserModel from \"../../model/common/UserModel\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class ChatSelectEmojiPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private lastUseNode_: cc.Node = null // path://root/last_use_n\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n    //@end\n\n    private readonly PKEY_TAB: string = 'CHAT_SELECT_EMOJI_TAB'\n    private EMOJI_SIZE: cc.Size = cc.size(64, 64)\n\n    private user: UserModel = null\n    private cb: Function = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n        this.user = this.getModel('user')\n    }\n\n    public onEnter(cb: Function) {\n        this.cb = cb\n        // 刷新历史使用\n        this.updateLastUse()\n        this.tabsTc_.Tabs(this.user.getTempPreferenceMap(this.PKEY_TAB) || 0)\n    }\n\n    public onRemove() {\n        this.cb = null\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/item_be\n    onClickItem(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        const id = event.target.Data\n        this.cb && this.cb(id)\n        this.hide()\n        this.addUseCount(id)\n    }\n\n    // path://root/last_use_n/last_use_be\n    onClickLastUse(event: cc.Event.EventTouch, data: string) {\n        audioMgr.playSFX('click')\n        const id = event.target.Data\n        this.cb && this.cb(id)\n        this.hide()\n    }\n\n    // path://root/tabs_tc_tce\n    onClickTabs(event: cc.Toggle, data: string) {\n        !data && audioMgr.playSFX('click')\n        const type = Number(event.node.name)\n        this.user.setTempPreferenceData(this.PKEY_TAB, type)\n        this.updateEmojis(type)\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private updateLastUse() {\n        const list: any[] = this.user.getLocalPreferenceData(PreferenceKey.USE_EMOJIS_COUNT) || []\n        const len = list.length\n        // 这里兼容一下\n        const emojis = this.user.getUnlockChatEmojiIds()\n        for (let i = list.length - 1; i >= 0; i--) {\n            const id = list[i].id, type = Math.floor(id / 1000)\n            if (type === 1) {\n                continue\n            } else if (!emojis.has(id)) {\n                list.splice(i, 1)\n            }\n        }\n        if (list.length !== len) {\n            this.user.setLocalPreferenceData(PreferenceKey.USE_EMOJIS_COUNT, list)\n        }\n        list.sort((a, b) => b.count - a.count)\n        this.lastUseNode_.Items(list.slice(0, 5), (it, data) => {\n            it.Data = data.id\n            const val = it.Child('val')\n            val.scale = 1\n            resHelper.loadEmojiIcon(data.id, val, this.key).then(() => this.isValid && val.adaptScale(this.EMOJI_SIZE))\n        })\n    }\n\n    private addUseCount(id: number) {\n        const list: any[] = this.user.getLocalPreferenceData(PreferenceKey.USE_EMOJIS_COUNT) || []\n        const it = list.find(m => m.id === id)\n        if (it) {\n            it.count += 1\n        } else {\n            list.push({ id, count: 1 })\n        }\n        this.user.setLocalPreferenceData(PreferenceKey.USE_EMOJIS_COUNT, list)\n    }\n\n    private updateEmojis(type: number) {\n        const ids = this.getCanUseEmojis(type)\n        this.listSv_.stopAutoScroll()\n        this.listSv_.content.y = 0\n        this.listSv_.Child('empty').active = type !== 0 && ids.length === 0\n        this.listSv_.List(ids.length, (it, i) => {\n            const id = it.Data = ids[i]\n            const val = it.Child('val')\n            resHelper.loadEmojiIcon(id, val, this.key).then(() => this.isValid && val.adaptScale(this.EMOJI_SIZE))\n        })\n    }\n\n    private getCanUseEmojis(type: number) {\n        type = type + 1\n        const datas: number[] = []\n        assetsMgr.getJson('chatEmoji').datas.forEach(m => (m.type === type && m.cond === 0) && datas.push(m.id))\n        if (type === 2) { //这里加上购买的动态表情\n            datas.pushArr(this.user.getUnlockChatEmojiIds())\n        }\n        return datas\n    }\n}\n"]}