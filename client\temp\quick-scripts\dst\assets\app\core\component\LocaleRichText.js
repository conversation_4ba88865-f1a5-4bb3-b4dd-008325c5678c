
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LocaleRichText.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e769d+YgOxJk7O+jV4RDTuh', 'LocaleRichText');
// app/core/component/LocaleRichText.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu, requireComponent = _a.requireComponent;
var LocaleRichText = /** @class */ (function (_super) {
    __extends(LocaleRichText, _super);
    function LocaleRichText() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.fontName = '';
        _this._label = null;
        _this._string = '';
        _this._lang = '';
        _this._font = '';
        _this._json = null;
        _this._params = [];
        _this._temp_params = []; //转换好过后的参数
        _this._change = false;
        _this._is_empty_string = false; //是否主动设置空字符串
        return _this;
    }
    Object.defineProperty(LocaleRichText.prototype, "label", {
        get: function () {
            if (!this._label) {
                this._label = this.getComponent(cc.RichText);
            }
            return this._label;
        },
        enumerable: false,
        configurable: true
    });
    LocaleRichText.prototype.onEnable = function () {
        if (!mc.lang) {
            return;
        }
        else if (this._lang !== mc.lang) {
            this._lang = mc.lang;
            this._font = '';
            this._json = null;
            this.updateTempParams();
        }
        else if (this.label.font && !this.label.font.isValid) {
            this._font = '';
        }
        if (!this._json) {
            this.updateJson();
        }
        else if (!this._font) {
            this.updateFont();
        }
        this.updateString();
        this._change = mc.canChangeLang;
        if (this._change) {
            eventCenter.on(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    LocaleRichText.prototype.onDisable = function () {
        if (this._change) {
            this._change = false;
            eventCenter.off(CoreEventType_1.default.LANGUAGE_CHANGED, this.onLanguageChanged, this);
        }
    };
    // 语言切换
    LocaleRichText.prototype.onLanguageChanged = function (lang) {
        this._lang = lang;
        this._font = '';
        this.updateTempParams();
        this.updateJson();
        this.updateString();
    };
    Object.defineProperty(LocaleRichText.prototype, "string", {
        get: function () { return this.label.string; },
        set: function (val) {
            this.label.string = val;
            this._is_empty_string = val === '';
        },
        enumerable: false,
        configurable: true
    });
    LocaleRichText.prototype.updateLang = function () {
        this._lang = mc.lang;
    };
    // 刷新string
    LocaleRichText.prototype.updateString = function () {
        var val = this._json ? this._json[this._lang] : undefined;
        if (val !== undefined) {
            this._string = ut.stringFormat(val, this._temp_params);
        }
        else if (this.key) {
            this._string = ut.stringFormat(this.key, this._temp_params);
        }
        else if (this._is_empty_string) {
            this._string = '';
        }
        else {
            this._string = '404';
        }
        if (this._string !== this.label.string) {
            this.label.string = this._string;
        }
    };
    // 设置参数
    LocaleRichText.prototype.setParams = function (params) {
        var _this = this;
        this._params.length = 0;
        params === null || params === void 0 ? void 0 : params.forEach(function (m) { return Array.isArray(m) ? _this._params.pushArr(m) : _this._params.push(m); });
        this.updateTempParams();
    };
    // 刷新参数
    LocaleRichText.prototype.updateTempParams = function () {
        var _this = this;
        this._temp_params = this._params.map(function (m) {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                var _a = __read(m.split('.'), 2), name = _a[0], id = _a[1];
                var json = assetsMgr.getJsonData(name, id) || {};
                var val = json[_this._lang];
                return val !== undefined ? val : m;
            }
            return m;
        });
    };
    // 刷新json
    LocaleRichText.prototype.updateJson = function () {
        if (this.key) {
            var _a = __read(this.key.split('.'), 2), name = _a[0], id = _a[1];
            this._json = assetsMgr.getJsonData(name, id) || {};
            this.updateFont();
        }
        else {
            this._json = null;
        }
    };
    // 刷新字体
    LocaleRichText.prototype.updateFont = function () {
        if (this.fontName) {
            if (this.fontName !== this._font) {
                this.setFont(this.fontName);
            }
        }
        else if (this._json && this._json.font && this._json.font !== this._font) {
            this.setFont(this._json.font);
        }
    };
    LocaleRichText.prototype.setFont = function (fontUrl) {
        this._font = fontUrl;
        var font = assetsMgr.getFont(fontUrl);
        if (font) {
            this.label.string = '';
            this.label.font = font;
        }
        else {
            this.label.font = null;
            this._font = '';
        }
    };
    // 设置key
    LocaleRichText.prototype.setKey = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        if (!this._lang) {
            this._lang = mc.lang;
        }
        if (this.key !== key || !this._json) {
            this.key = key;
            this.string = '';
            this.updateJson();
        }
        this._is_empty_string = key === '';
        this.setParams(params);
        this.updateString();
    };
    __decorate([
        property()
    ], LocaleRichText.prototype, "key", void 0);
    __decorate([
        property()
    ], LocaleRichText.prototype, "fontName", void 0);
    LocaleRichText = __decorate([
        ccclass,
        menu('多语言组件/LocaleRichText'),
        requireComponent(cc.RichText)
    ], LocaleRichText);
    return LocaleRichText;
}(BaseLocale_1.default));
exports.default = LocaleRichText;
cc.LocaleRichText = LocaleRichText;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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