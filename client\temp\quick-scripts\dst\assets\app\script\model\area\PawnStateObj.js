
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/PawnStateObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fe90e8UhEFMfqAcx4q4FIRx', 'PawnStateObj');
// app/script/model/area/PawnStateObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 士兵状态信息
var PawnStateObj = /** @class */ (function () {
    function PawnStateObj() {
        this.uid = '';
        this.data = null;
    }
    PawnStateObj.prototype.init = function (state, data) {
        this.uid = ut.UID();
        this.type = state;
        this.data = data;
        return this;
    };
    return PawnStateObj;
}());
exports.default = PawnStateObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxhcmVhXFxQYXduU3RhdGVPYmoudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFQSxTQUFTO0FBQ1Q7SUFBQTtRQUVXLFFBQUcsR0FBVyxFQUFFLENBQUE7UUFFaEIsU0FBSSxHQUFRLElBQUksQ0FBQTtJQVEzQixDQUFDO0lBTlUsMkJBQUksR0FBWCxVQUFZLEtBQWdCLEVBQUUsSUFBVTtRQUNwQyxJQUFJLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQyxHQUFHLEVBQUUsQ0FBQTtRQUNuQixJQUFJLENBQUMsSUFBSSxHQUFHLEtBQUssQ0FBQTtRQUNqQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFDTCxtQkFBQztBQUFELENBWkEsQUFZQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGF3blN0YXRlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xyXG5cclxuLy8g5aOr5YW154q25oCB5L+h5oGvXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFBhd25TdGF0ZU9iaiB7XHJcblxyXG4gICAgcHVibGljIHVpZDogc3RyaW5nID0gJydcclxuICAgIHB1YmxpYyB0eXBlOiBQYXduU3RhdGVcclxuICAgIHB1YmxpYyBkYXRhOiBhbnkgPSBudWxsXHJcblxyXG4gICAgcHVibGljIGluaXQoc3RhdGU6IFBhd25TdGF0ZSwgZGF0YT86IGFueSkge1xyXG4gICAgICAgIHRoaXMudWlkID0gdXQuVUlEKClcclxuICAgICAgICB0aGlzLnR5cGUgPSBzdGF0ZVxyXG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGFcclxuICAgICAgICByZXR1cm4gdGhpc1xyXG4gICAgfVxyXG59Il19