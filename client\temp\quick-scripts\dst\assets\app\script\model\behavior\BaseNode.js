
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BaseNode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'efcb77htA9PM4dfClEDqz2o', 'BaseNode');
// app/script/model/behavior/BaseNode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var BTConstant_1 = require("./BTConstant");
// 基础节点
var BaseNode = /** @class */ (function () {
    function BaseNode() {
        this.id = 0;
        this.index = 0;
        this.target = null; //目标
        this.json = null;
        this.key = 0; //唯一标识
    }
    BaseNode.prototype.init = function (conf, target, index) {
        this.json = conf;
        this.id = conf.id;
        this.index = index;
        this.target = target;
        this.key = this.id * 100 + index;
        this.onInit(conf);
    };
    BaseNode.prototype.execute = function (dt) {
        // cc.log('node execute [' + this.json.cls + ']' + this.index)
        if (!this.getBlackboardData('isOpen')) {
            this.open();
        }
        this.enter();
        var state = this.onTick(dt);
        this.leave(state);
        return state;
    };
    BaseNode.prototype.open = function () {
        this.setBlackboardData('isOpen', true);
        this.onOpen();
    };
    BaseNode.prototype.enter = function () {
        this.onEnter();
    };
    BaseNode.prototype.leave = function (state) {
        this.onLeave(state);
    };
    BaseNode.prototype.onInit = function (conf) { };
    BaseNode.prototype.onOpen = function () { }; //第一次打开这个节点
    BaseNode.prototype.onEnter = function () { };
    BaseNode.prototype.onTick = function (dt) { return BTConstant_1.BTState.ERROR; };
    BaseNode.prototype.onLeave = function (state) { };
    BaseNode.prototype.onClean = function () { }; //清理
    BaseNode.prototype.addChild = function (node) { }; //添加子节点
    Object.defineProperty(BaseNode.prototype, "ctrl", {
        get: function () {
            return this.target.ctrl;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseNode.prototype, "blackboard", {
        // 交互数据
        get: function () {
            return this.target.getBlackboard(this.key);
        },
        enumerable: false,
        configurable: true
    });
    BaseNode.prototype.setBlackboardData = function (key, val) {
        this.blackboard[key] = val;
    };
    BaseNode.prototype.getBlackboardData = function (key) {
        return this.blackboard[key];
    };
    BaseNode.prototype.removeBlackboardData = function (key) {
        delete this.blackboard[key];
    };
    // 全局的交互数据
    BaseNode.prototype.getTreeBlackboardData = function (key) {
        return this.target.getBlackboard(0)[key];
    };
    BaseNode.prototype.setTreeBlackboardData = function (key, val) {
        this.target.getBlackboard(0)[key] = val;
    };
    return BaseNode;
}());
exports.default = BaseNode;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQmFzZU5vZGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSwyQ0FBOEM7QUFFOUMsT0FBTztBQUNQO0lBQUE7UUFFVyxPQUFFLEdBQVcsQ0FBQyxDQUFBO1FBQ2QsVUFBSyxHQUFXLENBQUMsQ0FBQTtRQUVqQixXQUFNLEdBQVksSUFBSSxDQUFBLENBQUMsSUFBSTtRQUUzQixTQUFJLEdBQVEsSUFBSSxDQUFBO1FBQ2hCLFFBQUcsR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNO0lBd0VqQyxDQUFDO0lBdEVVLHVCQUFJLEdBQVgsVUFBWSxJQUFTLEVBQUUsTUFBZSxFQUFFLEtBQWE7UUFDakQsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFBO1FBQ2xCLElBQUksQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFBO1FBQ3BCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEVBQUUsR0FBRyxHQUFHLEdBQUcsS0FBSyxDQUFBO1FBQ2hDLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUE7SUFDckIsQ0FBQztJQUVNLDBCQUFPLEdBQWQsVUFBZSxFQUFVO1FBQ3JCLDhEQUE4RDtRQUM5RCxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQ25DLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtTQUNkO1FBQ0QsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ1osSUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUM3QixJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQ2pCLE9BQU8sS0FBSyxDQUFBO0lBQ2hCLENBQUM7SUFFTyx1QkFBSSxHQUFaO1FBQ0ksSUFBSSxDQUFDLGlCQUFpQixDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUN0QyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUE7SUFDakIsQ0FBQztJQUVPLHdCQUFLLEdBQWI7UUFDSSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7SUFDbEIsQ0FBQztJQUVPLHdCQUFLLEdBQWIsVUFBYyxLQUFjO1FBQ3hCLElBQUksQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUE7SUFDdkIsQ0FBQztJQUVNLHlCQUFNLEdBQWIsVUFBYyxJQUFTLElBQUksQ0FBQztJQUNyQix5QkFBTSxHQUFiLGNBQWtCLENBQUMsRUFBQyxXQUFXO0lBQ3hCLDBCQUFPLEdBQWQsY0FBbUIsQ0FBQztJQUNiLHlCQUFNLEdBQWIsVUFBYyxFQUFVLElBQUksT0FBTyxvQkFBTyxDQUFDLEtBQUssQ0FBQSxDQUFDLENBQUM7SUFDM0MsMEJBQU8sR0FBZCxVQUFlLEtBQWMsSUFBSSxDQUFDO0lBQzNCLDBCQUFPLEdBQWQsY0FBbUIsQ0FBQyxFQUFDLElBQUk7SUFDbEIsMkJBQVEsR0FBZixVQUFnQixJQUFjLElBQUksQ0FBQyxFQUFDLE9BQU87SUFFM0Msc0JBQVcsMEJBQUk7YUFBZjtZQUNJLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUE7UUFDM0IsQ0FBQzs7O09BQUE7SUFHRCxzQkFBVyxnQ0FBVTtRQURyQixPQUFPO2FBQ1A7WUFDSSxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtRQUM5QyxDQUFDOzs7T0FBQTtJQUVNLG9DQUFpQixHQUF4QixVQUF5QixHQUFXLEVBQUUsR0FBUTtRQUMxQyxJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQTtJQUM5QixDQUFDO0lBRU0sb0NBQWlCLEdBQXhCLFVBQXlCLEdBQVc7UUFDaEMsT0FBTyxJQUFJLENBQUMsVUFBVSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQy9CLENBQUM7SUFFTSx1Q0FBb0IsR0FBM0IsVUFBNEIsR0FBVztRQUNuQyxPQUFPLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDL0IsQ0FBQztJQUVELFVBQVU7SUFDSCx3Q0FBcUIsR0FBNUIsVUFBNkIsR0FBVztRQUNwQyxPQUFPLElBQUksQ0FBQyxNQUFNLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQzVDLENBQUM7SUFFTSx3Q0FBcUIsR0FBNUIsVUFBNkIsR0FBVyxFQUFFLEdBQVE7UUFDOUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsR0FBRyxDQUFBO0lBQzNDLENBQUM7SUFDTCxlQUFDO0FBQUQsQ0FoRkEsQUFnRkMsSUFBQSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGaWdodGVyIGZyb20gXCIuLi9mc3AvRmlnaHRlclwiXHJcbmltcG9ydCB7IEJUU3RhdGUsIEJUVHlwZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIlxyXG5cclxuLy8g5Z+656GA6IqC54K5XHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJhc2VOb2RlIHtcclxuXHJcbiAgICBwdWJsaWMgaWQ6IG51bWJlciA9IDBcclxuICAgIHB1YmxpYyBpbmRleDogbnVtYmVyID0gMFxyXG4gICAgcHVibGljIHR5cGU6IEJUVHlwZVxyXG4gICAgcHVibGljIHRhcmdldDogRmlnaHRlciA9IG51bGwgLy/nm67moIdcclxuXHJcbiAgICBwdWJsaWMganNvbjogYW55ID0gbnVsbFxyXG4gICAgcHVibGljIGtleTogbnVtYmVyID0gMCAvL+WUr+S4gOagh+ivhlxyXG5cclxuICAgIHB1YmxpYyBpbml0KGNvbmY6IGFueSwgdGFyZ2V0OiBGaWdodGVyLCBpbmRleDogbnVtYmVyKSB7XHJcbiAgICAgICAgdGhpcy5qc29uID0gY29uZlxyXG4gICAgICAgIHRoaXMuaWQgPSBjb25mLmlkXHJcbiAgICAgICAgdGhpcy5pbmRleCA9IGluZGV4XHJcbiAgICAgICAgdGhpcy50YXJnZXQgPSB0YXJnZXRcclxuICAgICAgICB0aGlzLmtleSA9IHRoaXMuaWQgKiAxMDAgKyBpbmRleFxyXG4gICAgICAgIHRoaXMub25Jbml0KGNvbmYpXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGV4ZWN1dGUoZHQ6IG51bWJlcikge1xyXG4gICAgICAgIC8vIGNjLmxvZygnbm9kZSBleGVjdXRlIFsnICsgdGhpcy5qc29uLmNscyArICddJyArIHRoaXMuaW5kZXgpXHJcbiAgICAgICAgaWYgKCF0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc09wZW4nKSkge1xyXG4gICAgICAgICAgICB0aGlzLm9wZW4oKVxyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLmVudGVyKClcclxuICAgICAgICBjb25zdCBzdGF0ZSA9IHRoaXMub25UaWNrKGR0KVxyXG4gICAgICAgIHRoaXMubGVhdmUoc3RhdGUpXHJcbiAgICAgICAgcmV0dXJuIHN0YXRlXHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBvcGVuKCkge1xyXG4gICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzT3BlbicsIHRydWUpXHJcbiAgICAgICAgdGhpcy5vbk9wZW4oKVxyXG4gICAgfVxyXG5cclxuICAgIHByaXZhdGUgZW50ZXIoKSB7XHJcbiAgICAgICAgdGhpcy5vbkVudGVyKClcclxuICAgIH1cclxuXHJcbiAgICBwcml2YXRlIGxlYXZlKHN0YXRlOiBCVFN0YXRlKSB7XHJcbiAgICAgICAgdGhpcy5vbkxlYXZlKHN0YXRlKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkluaXQoY29uZjogYW55KSB7IH1cclxuICAgIHB1YmxpYyBvbk9wZW4oKSB7IH0gLy/nrKzkuIDmrKHmiZPlvIDov5nkuKroioLngrlcclxuICAgIHB1YmxpYyBvbkVudGVyKCkgeyB9XHJcbiAgICBwdWJsaWMgb25UaWNrKGR0OiBudW1iZXIpIHsgcmV0dXJuIEJUU3RhdGUuRVJST1IgfVxyXG4gICAgcHVibGljIG9uTGVhdmUoc3RhdGU6IEJUU3RhdGUpIHsgfVxyXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7IH0gLy/muIXnkIZcclxuICAgIHB1YmxpYyBhZGRDaGlsZChub2RlOiBCYXNlTm9kZSkgeyB9IC8v5re75Yqg5a2Q6IqC54K5XHJcblxyXG4gICAgcHVibGljIGdldCBjdHJsKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLnRhcmdldC5jdHJsXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5Lqk5LqS5pWw5o2uXHJcbiAgICBwdWJsaWMgZ2V0IGJsYWNrYm9hcmQoKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMudGFyZ2V0LmdldEJsYWNrYm9hcmQodGhpcy5rZXkpXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHNldEJsYWNrYm9hcmREYXRhKGtleTogc3RyaW5nLCB2YWw6IGFueSkge1xyXG4gICAgICAgIHRoaXMuYmxhY2tib2FyZFtrZXldID0gdmFsXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldEJsYWNrYm9hcmREYXRhKGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuYmxhY2tib2FyZFtrZXldXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHJlbW92ZUJsYWNrYm9hcmREYXRhKGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgZGVsZXRlIHRoaXMuYmxhY2tib2FyZFtrZXldXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5YWo5bGA55qE5Lqk5LqS5pWw5o2uXHJcbiAgICBwdWJsaWMgZ2V0VHJlZUJsYWNrYm9hcmREYXRhKGtleTogc3RyaW5nKSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMudGFyZ2V0LmdldEJsYWNrYm9hcmQoMClba2V5XVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBzZXRUcmVlQmxhY2tib2FyZERhdGEoa2V5OiBzdHJpbmcsIHZhbDogYW55KSB7XHJcbiAgICAgICAgdGhpcy50YXJnZXQuZ2V0QmxhY2tib2FyZCgwKVtrZXldID0gdmFsXHJcbiAgICB9XHJcbn0iXX0=