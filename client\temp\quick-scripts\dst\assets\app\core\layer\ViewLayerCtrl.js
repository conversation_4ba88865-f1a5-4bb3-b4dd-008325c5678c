
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/layer/ViewLayerCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd3a79ReadFM5Lz1RYt+cfrO', 'ViewLayerCtrl');
// app/core/layer/ViewLayerCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLayerCtrl_1 = require("../base/BaseLayerCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ViewLayerCtrl = /** @class */ (function (_super) {
    __extends(ViewLayerCtrl, _super);
    function ViewLayerCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ctrlMgr = null;
        return _this;
    }
    ViewLayerCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        return [
            (_a = {}, _a[CoreEventType_1.default.OPEN_PNL] = this.onOpenPnl, _a),
            (_b = {}, _b[CoreEventType_1.default.HIDE_PNL] = this.onHidePnl, _b),
            (_c = {}, _c[CoreEventType_1.default.HIDE_ALL_PNL] = this.onHideAllPnl, _c),
            (_d = {}, _d[CoreEventType_1.default.CLOSE_PNL] = this.onClosePnl, _d),
            (_e = {}, _e[CoreEventType_1.default.CLOSE_ALL_PNL] = this.onCloseAllPnl, _e),
            (_f = {}, _f[CoreEventType_1.default.CLOSE_MOD_PNL] = this.onCloseModPnl, _f),
            (_g = {}, _g[CoreEventType_1.default.PRELOAD_PNL] = this.onPreloadPnl, _g),
            (_h = {}, _h[CoreEventType_1.default.CLEAN_ALL_UNUSED] = this.onCleanAllUnused, _h),
            (_j = {}, _j[CoreEventType_1.default.GIVEUP_LOAD_PNL] = this.onGiveupLoadPnl, _j),
            (_k = {}, _k[CoreEventType_1.default.CLEAN_LOAD_PNL_QUEUE] = this.onCleanLoadPnlQueue, _k),
        ];
    };
    ViewLayerCtrl.prototype.onCreate = function () {
        this.node.group = 'ui';
    };
    ViewLayerCtrl.prototype.onClean = function () {
    };
    ViewLayerCtrl.prototype.setCtrlMgr = function (mgr) {
        this.ctrlMgr = mgr;
        this.ctrlMgr.node = this.node;
    };
    ViewLayerCtrl.prototype.setPnlIndexConf = function (conf) {
        this.ctrlMgr.setPnlIndexConf(conf);
    };
    ViewLayerCtrl.prototype.getOpenPnls = function () {
        return this.ctrlMgr.getOpened();
    };
    ViewLayerCtrl.prototype.getLoadQueues = function () {
        return this.ctrlMgr.getLoadQueues();
    };
    ViewLayerCtrl.prototype.onOpenPnl = function (key, complete) {
        var _a;
        var params = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            params[_i - 2] = arguments[_i];
        }
        (_a = this.ctrlMgr).show.apply(_a, __spread([key], params)).then(function (pnl) { return complete && complete(pnl); });
    };
    ViewLayerCtrl.prototype.onHidePnl = function (key) {
        this.ctrlMgr.hide(key);
    };
    ViewLayerCtrl.prototype.onHideAllPnl = function (val, ignores) {
        this.ctrlMgr.hideAll(val, ignores);
    };
    ViewLayerCtrl.prototype.onClosePnl = function (key, force) {
        this.ctrlMgr.clean(key, force);
    };
    ViewLayerCtrl.prototype.onCloseAllPnl = function (val, force) {
        this.ctrlMgr.cleanAll(val, force);
    };
    ViewLayerCtrl.prototype.onCloseModPnl = function (mod) {
        this.ctrlMgr.cleanByMod(mod);
    };
    ViewLayerCtrl.prototype.onPreloadPnl = function (key, complete, progress) {
        this.ctrlMgr.preloadPnl(key).then(function (pnl) {
            complete && complete(pnl);
            progress && progress(1, 1);
        });
    };
    ViewLayerCtrl.prototype.onCleanAllUnused = function () {
        this.ctrlMgr.cleanAllUnused();
    };
    ViewLayerCtrl.prototype.onGiveupLoadPnl = function (id) {
        this.ctrlMgr.giveupLoadById(id);
    };
    ViewLayerCtrl.prototype.onCleanLoadPnlQueue = function (isUnlock) {
        this.ctrlMgr.cleanLoadQueue(isUnlock);
    };
    ViewLayerCtrl = __decorate([
        ccclass
    ], ViewLayerCtrl);
    return ViewLayerCtrl;
}(BaseLayerCtrl_1.default));
exports.default = ViewLayerCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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