
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/BaseMarchObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eb5bc7ZDGlH9okty7wsEDU+', 'BaseMarchObj');
// app/script/model/main/BaseMarchObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
// 一个行军信息
var BaseMarchObj = /** @class */ (function () {
    function BaseMarchObj() {
        this.uid = '';
        this.owner = ''; //拥有者
        this.startIndex = 0; //开始位置
        this.targetIndex = 0; //目标位置
        this.targetUid = '';
        this.needTime = 0; //需要行军时间
        this.surplusTime = 0; //剩余行军时间
        this.initTime = 0; //初始化的时间
        this.startPos = cc.v2(); //开始位置
        this.targetPos = cc.v2(); //目标位置
        this.distance = 0; //距离长度
        this.angle = 0; //方向 角度
        this.roleMoveSpeed = cc.v2(); //移动速度
        this.targetType = 0; //目标类型
        this.hideTime = 0;
        this.opacity = 0;
    }
    BaseMarchObj.prototype.initBase = function () {
        var _a;
        // 转换位置
        this.initPosition(this.startIndex, this.startPos);
        this.initPosition(this.targetIndex, this.targetPos);
        // 角度
        this.angle = ut.getAngle(this.startPos, this.targetPos);
        // 计算距离
        var xy = this.startPos.sub(this.targetPos);
        this.distance = xy.mag();
        // 计算移动速度
        var v = this.distance / (this.needTime * 0.001); //每秒移动的距离
        ut.angleToPoint(this.angle, v, this.roleMoveSpeed);
        // 初始化行军线透明度
        var progressMap = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.SET_MARCH_LINE_OPACITY) || {};
        var progress = (_a = progressMap[this.getMarchLineType()]) !== null && _a !== void 0 ? _a : 100;
        this.opacity = Math.floor(255 * progress * 0.01);
    };
    // 初始化目标类型
    BaseMarchObj.prototype.initTargetType = function () {
        var _a;
        this.targetUid = ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(this.targetIndex)) === null || _a === void 0 ? void 0 : _a.owner) || '';
    };
    BaseMarchObj.prototype.initPosition = function (index, out) {
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
        if (cell) {
            var diff = cc.v2(-1, -1).addSelf(cell.getSize()).multiply(Constant_1.TILE_SIZE_HALF);
            MapHelper_1.mapHelper.getPixelByPoint(cell.actPoint, out).addSelf(diff);
        }
        else {
            MapHelper_1.mapHelper.getPixelByPoint(MapHelper_1.mapHelper.indexToPoint(index), out);
        }
    };
    // 获取实际的剩余时间
    BaseMarchObj.prototype.getSurplusTime = function () {
        return Math.max(this.surplusTime - (Date.now() - this.initTime), 0);
    };
    // 获取已经走的时间
    BaseMarchObj.prototype.getYetMarchTime = function () {
        return this.needTime - this.getSurplusTime();
    };
    // 获取移动的起点位置
    BaseMarchObj.prototype.getMoveStartPos = function (surplusTime) {
        var d = (1 - surplusTime / this.needTime) * this.distance; //经过的距离
        return ut.angleToPoint(this.angle, d).addSelf(this.startPos);
    };
    // 获取经过的距离
    BaseMarchObj.prototype.getElapsedDistance = function () {
        return (1 - this.getSurplusTime() / this.needTime) * this.distance; //经过的距离
    };
    // 获取剩余的距离
    BaseMarchObj.prototype.getSurplusDistance = function () {
        return this.getSurplusTime() / this.needTime * this.distance; //经过的距离
    };
    // 检测是否和另外一个行军是同一条线 
    BaseMarchObj.prototype.checkOtherMarchLine = function (other) {
        if (!other) {
            return false;
        }
        return (this.startIndex === other.startIndex && this.targetIndex === other.targetIndex)
            || (this.startIndex === other.targetIndex && this.targetIndex === other.startIndex);
    };
    // 是否军队
    BaseMarchObj.prototype.isArmy = function () { return false; };
    // 是否英雄
    BaseMarchObj.prototype.isHero = function () { return false; };
    BaseMarchObj.prototype.getArmyName = function () { return '???'; };
    // 获取移动角色的url
    BaseMarchObj.prototype.getMarchRoleUrl = function () { return ''; };
    BaseMarchObj.prototype.getMarchInfoUrl = function () { return ''; };
    BaseMarchObj.prototype.getMarchRoleAnim = function () { return ''; };
    // 行军线类型
    BaseMarchObj.prototype.getMarchLineType = function () { return 0; };
    // 是否可以显示行军 到场景
    BaseMarchObj.prototype.isCanShowMarch = function () { return true; };
    // 是否可以显示到UI
    BaseMarchObj.prototype.isCanShowUI = function () { return this.targetType !== 0; };
    return BaseMarchObj;
}());
exports.default = BaseMarchObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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