
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LoginWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '26592MuefROo7sBBVXvgIZg', 'LoginWindCtrl');
// app/script/view/login/LoginWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var version_1 = require("../../../../scene/version");
var LocalConfig_1 = require("../../common/LocalConfig");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AppleHelper_1 = require("../../common/helper/AppleHelper");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var LoadProgressHelper_1 = require("../../common/helper/LoadProgressHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var LoginWindCtrl = /** @class */ (function (_super) {
    __extends(LoginWindCtrl, _super);
    function LoginWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.infoNode_ = null; // path://info_n
        _this.loadingNode_ = null; // path://loading_n
        _this.verLbl_ = null; // path://ver_l
        //@end
        _this.model = null;
        _this.sceneKey = 'main';
        _this.initItemY = 0;
        _this.nextItemY = 0;
        _this.curLoadItem = null;
        _this.curLoadProgressLbl = null;
        _this.curPercent = 0;
        _this.dstPercent = 0;
        _this.showLoginButton = false;
        return _this;
    }
    LoginWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_DISCONNECT] = this.onNetDisconnect, _a),
            (_b = {}, _b[EventType_1.default.CLIENT_VERSION_LOW] = this.onClientVersionLow, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.RELOGIN] = this.onRelogin, _c.enter = true, _c),
        ];
    };
    LoginWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var item;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.model = this.getModel('login');
                        item = this.infoNode_.children[0];
                        this.initItemY = item.y;
                        item.active = false;
                        ErrorReportHelper_1.errorReportHelper.setCurLevel(Enums_1.ReportErrorLevel.LOGIN);
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('login/LoginUI')];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    LoginWindCtrl.prototype.onEnter = function (data) {
        var volData = localStorage.getItem('slg_bgm_volume');
        var vol = volData !== null ? volData : '1';
        if (vol === '1') {
            audioMgr.playBGM('app/sound_bgm_005', 0.65);
        }
        ViewHelper_1.viewHelper.showPnl('login/LoginUI');
        GameHelper_1.gameHpr.resetByLogin();
        TaHelper_1.taHelper.logout();
        this.verLbl_.string = version_1.default.VERSION;
        this.ready();
    };
    LoginWindCtrl.prototype.onClean = function () {
        // 这里释放掉 app场景start场景loginWind 共同的资源
        // 如果资源更换了 这里需要重新记录 scene/bg
        // const arr = [
        //     '1981c977-1f01-4d4c-9bcb-718709cc876d',
        //     '125d773b-78de-4a52-8bbd-a758f7bf9986',
        //     'dc8949e3-e876-457b-8324-3b15c372c76d',
        //     'fb594c88-3b49-40fb-997d-c20c68617b25',
        //     'dac52bb3-1195-4ddc-b1b1-d3ad76213eea',
        // ]
        // arr.forEach(key => cc.assetManager.assets.get(key)?.decRef())
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    LoginWindCtrl.prototype.onNetDisconnect = function () {
        var _this = this;
        var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_close');
        ViewHelper_1.viewHelper.showMessageBox(text, {
            ok: function () {
                if (_this.isValid) {
                    _this.emit(mc.Event.HIDE_ALL_PNL, '', 'login/LoginUI');
                    _this.ready();
                }
            },
            okText: 'login.button_reconnect',
            lockClose: true,
        });
    };
    LoginWindCtrl.prototype.onClientVersionLow = function (data) {
        this.showVersionLowTip(data);
    };
    // 重新登陆
    LoginWindCtrl.prototype.onRelogin = function () {
        this.ready();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 设置加载说明
    LoginWindCtrl.prototype.setDescLoad = function (val, isProgress) {
        var _this = this;
        if (!this.isValid) {
            return;
        }
        this.curLoadProgressLbl = null;
        this.infoNode_.AddItem(function (it) {
            var root = it.Child('root');
            root.setLocaleKey(val);
            root.Component(cc.Label)._forceUpdateRenderData();
            it.y = _this.nextItemY;
            _this.nextItemY -= (root.height + 8);
            if (isProgress) {
                _this.curLoadProgressLbl = root.Swih(isProgress ? 1 : 0)[0].Component(cc.Label);
            }
            else {
                root.Swih(0);
            }
            _this.curLoadItem = it;
            it.scale = 0.3;
            cc.tween(it).to(0.1, { scale: 1 }).start();
        });
    };
    LoginWindCtrl.prototype.setDescDone = function (suc, text) {
        var _a, _b;
        if (!this.isValid) {
            return;
        }
        this.infoNode_.active = true;
        var it = (_b = (_a = this.curLoadItem) === null || _a === void 0 ? void 0 : _a.Child('root')) === null || _b === void 0 ? void 0 : _b.Swih(suc ? 2 : 3)[0];
        if (it && !suc) {
            var str = GameHelper_1.gameHpr.getTextByNetworkStatus('login.failed');
            it.Child('val').setLocaleKey(text !== null && text !== void 0 ? text : str);
        }
    };
    LoginWindCtrl.prototype.showPnl = function (key) {
        var _this = this;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        ViewHelper_1.viewHelper.showPnl.apply(ViewHelper_1.viewHelper, __spread([key], params)).then(function () {
            if (_this.isValid) {
                _this.infoNode_.active = false;
            }
        });
    };
    // 准备
    LoginWindCtrl.prototype.ready = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data, text;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.isValid) return [3 /*break*/, 1];
                        return [2 /*return*/];
                    case 1:
                        if (!!this.model.isInitAsset1) return [3 /*break*/, 5];
                        this.loadingNode_.active = true;
                        this.infoNode_.active = false;
                        // 加载一些必要的资源
                        return [4 /*yield*/, Promise.all([
                                assetsMgr.loadCommonRes('login', cc.JsonAsset),
                                assetsMgr.loadCommonRes('ecode', cc.JsonAsset),
                                assetsMgr.loadCommonRes('portrayalBase', cc.JsonAsset),
                                assetsMgr.loadCommonRes('portrayalSkill', cc.JsonAsset),
                                assetsMgr.loadCommonRes('pawnBase', cc.JsonAsset),
                                assetsMgr.loadCommonRes('strategy', cc.JsonAsset),
                                assetsMgr.loadCommonRes('f_m', cc.Font),
                                assetsMgr.loadCommonRes('f_m_login', cc.Font),
                                assetsMgr.loadCommonRes('f_b_login', cc.Font),
                                assetsMgr.loadCommonRes('f_h_login', cc.Font),
                                assetsMgr.loadCommonRes('f_b_out', cc.Font),
                                assetsMgr.loadCommonRes('PNL_MASK', cc.Prefab),
                                ViewHelper_1.viewHelper.showPnl('common/Bottom'),
                            ])];
                    case 2:
                        // 加载一些必要的资源
                        _a.sent();
                        return [4 /*yield*/, this.loadNotice('NetWaitNot|MessageBoxNot')];
                    case 3:
                        _a.sent();
                        this.model.isInitAsset1 = true;
                        // 登录前检测各种弹窗
                        return [4 /*yield*/, this.check()];
                    case 4:
                        // 登录前检测各种弹窗
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        // 显示隐私协议
                        this.emit(EventType_1.default.SHOW_LOGIN_UI_PRIVACY);
                        data = null;
                        if (!ut.isWechatGame()) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.checkWxHasUpdate()];
                    case 6:
                        data = _a.sent();
                        return [3 /*break*/, 11];
                    case 7:
                        if (!ut.isMobile()) return [3 /*break*/, 9];
                        return [4 /*yield*/, this.checkAppUpdate()];
                    case 8:
                        data = _a.sent();
                        return [3 /*break*/, 11];
                    case 9: return [4 /*yield*/, this.initServerInfo()];
                    case 10:
                        data = _a.sent();
                        _a.label = 11;
                    case 11:
                        if (!data) {
                        }
                        else if (data.maintainTime) {
                            return [2 /*return*/, this.showPnl('login/MaintainTip', data.maintainTime)];
                        }
                        else if (data.hasWxUpdate) {
                            return [2 /*return*/, this.showPnl('login/WxUpdateTip')];
                        }
                        else {
                            return [2 /*return*/, this.showPnl('login/AppUpdateTip', data)];
                        }
                        if (!this.isValid) {
                            text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_close');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox(text, {
                                    ok: function () { return ViewHelper_1.viewHelper.gotoWind('login'); },
                                    okText: 'login.button_reconnect',
                                    lockClose: true,
                                })];
                        }
                        if (logger.openPrint) {
                            ViewHelper_1.viewHelper.showPnl('other/Clearlove').then(function (pnl) { return pnl.hide(); });
                        }
                        this.loadingNode_.active = false;
                        // 连接服务器
                        this.connect();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 登录前检测各种弹窗
    LoginWindCtrl.prototype.check = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(ut.isIos() && cc.sys.isNative)) return [3 /*break*/, 2];
                        return [4 /*yield*/, AppleHelper_1.appleHelper.checkAdAuth()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    LoginWindCtrl.prototype.initServerInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var reqTime, res, status, data, now, delta;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        reqTime = Date.now();
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({ url: GameHelper_1.gameHpr.getServerInfoUrl(), showConnectFail: true, data: {} })];
                    case 1:
                        res = _a.sent();
                        if (res) {
                            status = res.status, data = res.data;
                            now = Date.now();
                            delta = Math.floor((now - reqTime) * 0.5) //延迟
                            ;
                            GameHelper_1.gameHpr.initServerTime(now, data === null || data === void 0 ? void 0 : data.serverInitTime, data === null || data === void 0 ? void 0 : data.serverZoneOffset, delta);
                            GameHelper_1.gameHpr.initServerConfigByHttp(data);
                            // 所有服务器维护时间
                            if ((data === null || data === void 0 ? void 0 : data.maintainTime) && data.maintainTime > 0) {
                                return [2 /*return*/, { maintainTime: data.maintainTime }];
                            }
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    // 检测微信更新
    LoginWindCtrl.prototype.checkWxHasUpdate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.initServerInfo()];
                    case 1:
                        data = _a.sent();
                        if (data) {
                            return [2 /*return*/, data];
                        }
                        return [2 /*return*/, new Promise(function (resolve) {
                                if (!wx['canIUse'] || !wx.canIUse('getUpdateManager') || !wx['getUpdateManager']) {
                                    return resolve(data);
                                }
                                wx.getUpdateManager().onCheckForUpdate(function (res) {
                                    if (res.hasUpdate) {
                                        data = data || {};
                                        data.hasWxUpdate = true;
                                    }
                                    resolve(data);
                                });
                            })];
                }
            });
        });
    };
    // 检测热更新 这里是强制更新 在登陆之前
    LoginWindCtrl.prototype.checkAppUpdate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var reqTime, res, status, data, version, _a, testVersion, platform, now, delta;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        reqTime = Date.now();
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.post({ url: GameHelper_1.gameHpr.getConfigByArea().checkUpdateUrl, showConnectFail: true, data: { shopPlatform: GameHelper_1.gameHpr.getShopPlatform() } })];
                    case 1:
                        res = _b.sent();
                        if (!res) {
                            return [2 /*return*/, null];
                        }
                        status = res.status, data = res.data;
                        version = GameHelper_1.gameHpr.getVersion();
                        _a = __read(((data === null || data === void 0 ? void 0 : data.testVersion) || '').split('|'), 2), testVersion = _a[0], platform = _a[1];
                        if (data && testVersion !== data.clientVersion) {
                            GameHelper_1.gameHpr.isRelease = LocalConfig_1.localConfig.RELEASE && (testVersion !== version || (!!platform && !platform.includes(GameHelper_1.gameHpr.getRunPlatform())));
                        }
                        now = Date.now();
                        delta = Math.floor((now - reqTime) * 0.5) //延迟
                        ;
                        GameHelper_1.gameHpr.initServerTime(now, data === null || data === void 0 ? void 0 : data.serverInitTime, data === null || data === void 0 ? void 0 : data.serverZoneOffset, delta);
                        GameHelper_1.gameHpr.initServerConfigByHttp(data);
                        // 所有服务器维护时间
                        if (GameHelper_1.gameHpr.isRelease && (data === null || data === void 0 ? void 0 : data.maintainTime) && data.maintainTime > 0) {
                            return [2 /*return*/, { maintainTime: data.maintainTime }];
                        }
                        // 游戏下载路径
                        GameHelper_1.gameHpr.gameDownloadUrl = (data === null || data === void 0 ? void 0 : data.gameDownloadUrl) || '';
                        // 检测是否强制更新
                        if (status !== 1 || !data) {
                        }
                        else if (!ut.checkVersion(version, data.clientVersion)) {
                            data.force = true; //强制更新
                            return [2 /*return*/, data];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    // 连接服务器
    LoginWindCtrl.prototype.connect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var model, ok, cnt, max, _ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.infoNode_.active = true;
                        this.infoNode_.Swih('');
                        this.nextItemY = this.initItemY;
                        this.setDescLoad('login.connect_server');
                        model = this.model;
                        ok = false, cnt = 0, max = 5;
                        _a.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 8];
                        return [4 /*yield*/, model.connect()];
                    case 2:
                        ok = _a.sent();
                        if (!ok) return [3 /*break*/, 3];
                        return [3 /*break*/, 8];
                    case 3:
                        if (!(cnt >= max)) return [3 /*break*/, 5];
                        cnt = 0;
                        max += 2;
                        return [4 /*yield*/, ViewHelper_1.viewHelper.showConnectFail()];
                    case 4:
                        _ok = _a.sent();
                        if (!_ok) {
                            return [3 /*break*/, 8];
                        }
                        return [3 /*break*/, 7];
                    case 5:
                        cnt += 1;
                        return [4 /*yield*/, ut.wait(2)];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7: return [3 /*break*/, 1];
                    case 8:
                        if (this.isValid) {
                            this.setDescDone(ok);
                            ok && this.login();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 登录
    LoginWindCtrl.prototype.login = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data, text;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setDescLoad('login.login_game');
                        return [4 /*yield*/, this.tryLogin()];
                    case 1:
                        data = _a.sent();
                        if (!!this.isValid) return [3 /*break*/, 2];
                        return [2 /*return*/];
                    case 2:
                        if (!(data.state === Enums_1.LoginState.FAILURE)) return [3 /*break*/, 3];
                        if (data.err === ECode_1.ecode.ROOM_CLOSE) {
                            text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_close');
                            ViewHelper_1.viewHelper.showMessageBox(text, {
                                lockClose: true,
                                okText: 'login.button_reconnect',
                                ok: function () { return _this.ready(); },
                            });
                        }
                        return [2 /*return*/, this.setDescDone(false, data.err)];
                    case 3:
                        if (!(data.state === Enums_1.LoginState.VERSION_TOOLOW)) return [3 /*break*/, 4];
                        this.showVersionLowTip(data.data);
                        return [2 /*return*/, this.setDescDone(false)];
                    case 4:
                        if (!(data.state === Enums_1.LoginState.QUEUE_UP)) return [3 /*break*/, 6];
                        this.showPnl('login/LineupTip');
                        this.model.disconnect();
                        return [4 /*yield*/, eventCenter.wait(EventType_1.default.QUEUE_UP_DONE)];
                    case 5:
                        _a.sent();
                        if (!this.isValid) {
                            return [2 /*return*/];
                        }
                        return [2 /*return*/, this.connect()];
                    case 6:
                        this.setDescDone(true);
                        // 上传推送消息token
                        this.model.uploadFcmToken();
                        if (!(GameHelper_1.gameHpr.user.getLogoutSurplusTime() > 0)) return [3 /*break*/, 8];
                        this.showPnl('login/LogoutTimeTip', GameHelper_1.gameHpr.user.getLogoutSurplusTime());
                        return [4 /*yield*/, eventCenter.wait(EventType_1.default.REVOCATION_LOGOUT_DONE)];
                    case 7:
                        _a.sent();
                        _a.label = 8;
                    case 8:
                        // 封禁
                        if (data.state === Enums_1.LoginState.BANACCOUNT_TIME) {
                            return [2 /*return*/, this.showPnl('login/BanAccountTimeTip', data.time, data.type)];
                        }
                        else if (data.state === Enums_1.LoginState.UNDONE_NOVICE) {
                            return [2 /*return*/, this.loadNovice()]; //加载新手村
                        }
                        else if (data.state === Enums_1.LoginState.NOT_SELECT_SERVER) {
                            return [2 /*return*/, this.loadLobby()]; //加载大厅
                        }
                        // 直接进入游戏
                        this.enterGame(data.data);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 尝试登陆
    LoginWindCtrl.prototype.tryLogin = function (accountToken) {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.model.tryLogin(accountToken)];
                    case 1:
                        data = _a.sent();
                        if (!!this.isValid) return [3 /*break*/, 2];
                        return [2 /*return*/, null];
                    case 2:
                        if (!(data.state === Enums_1.LoginState.NOT_ACCOUNT_TOKEN)) return [3 /*break*/, 4];
                        // 已经用按钮登陆过了
                        if (accountToken) {
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE }];
                        }
                        // 显示登录按钮
                        this.showPnl('login/LoginButton');
                        this.showLoginButton = true;
                        return [4 /*yield*/, eventCenter.wait(EventType_1.default.BUTTON_LOGIN_DONE)];
                    case 3:
                        // 等待登陆完成
                        data = _a.sent();
                        if (!this.isValid) {
                            return [2 /*return*/, null];
                        }
                        this.loadingNode_.active = true;
                        // 再次尝试登陆
                        return [2 /*return*/, this.tryLogin(data.accountToken)];
                    case 4:
                        this.loadingNode_.active = false;
                        return [2 /*return*/, data];
                }
            });
        });
    };
    // 进入游戏
    LoginWindCtrl.prototype.enterGame = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var info;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setDescLoad('login.req_base_info');
                        return [4 /*yield*/, this.model.enterGame(data)];
                    case 1:
                        info = _a.sent();
                        if (info.state === Enums_1.LoginState.FAILURE) {
                            return [2 /*return*/, this.setDescDone(false, info.err)];
                        }
                        else if (info.state === Enums_1.LoginState.VERSION_TOOLOW) {
                            this.showVersionLowTip(info.data);
                            return [2 /*return*/, this.setDescDone(false)];
                        }
                        else if (info.state === Enums_1.LoginState.VERSION_TOOTALL) { //版本过高
                            ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.VERSION_TOOTALL);
                            return [2 /*return*/, this.setDescDone(false)];
                        }
                        else if (info.state === Enums_1.LoginState.NOT_SELECT_SERVER) {
                            this.setDescDone(true);
                            return [2 /*return*/, this.loadLobby()]; //加载大厅
                        }
                        else {
                            data = info.data;
                        }
                        this.setDescDone(true);
                        // 开始加载游戏
                        this.loadGame(data.rst);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 显示版本过低提示
    LoginWindCtrl.prototype.showVersionLowTip = function (data) {
        if (ut.isMobile()) {
            return ViewHelper_1.viewHelper.showPnl('login/AppUpdateTip', data);
        }
        return ViewHelper_1.viewHelper.showPnl('login/VersionLowTip');
    };
    // 加载资源
    LoginWindCtrl.prototype.loadAssets = function () {
        var _this = this;
        this.infoNode_.active = true;
        this.setDescLoad('login.load_assets', true);
        audioMgr.init(); //音效
        cc.ButtonEx.DefaultClickPath = 'click'; //设置默认点击音效
        this.curPercent = this.dstPercent = 0;
        var loadProgress = LoadProgressHelper_1.loadProgressHelper.create();
        if (!this.model.isInitAsset2) {
            loadProgress.add(6, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, assetsMgr.init(cb)];
            }); }); })
                .add(3, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ResHelper_1.resHelper.init(cb)];
            }); }); })
                .add(5, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, this.loadAllNotice(cb)];
            }); }); });
        }
        return loadProgress;
    };
    // 加载新手村
    LoginWindCtrl.prototype.loadNovice = function () {
        var _this = this;
        if (!this.isValid) {
            return;
        }
        this.sceneKey = 'novice';
        GameHelper_1.gameHpr.isNoviceMode = true;
        var loadProgress = this.loadAssets();
        MapHelper_1.mapHelper.MAP_SIZE.set(NoviceConfig_1.NOVICE_MAP_SIZE);
        var novice = this.getModel('novice');
        if (!this.model.isInitAsset2) {
            loadProgress.add(2, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ResHelper_1.resHelper.initLandSkin(0, cb)];
            }); }); });
            loadProgress.add(1, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, novice.init()];
            }); }); });
            loadProgress.spawn(6, [
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UI', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UIMenuChild', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/Top', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind(this.sceneKey, cb)];
                }); }); },
            ]);
        }
        else {
            loadProgress.add(1, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, novice.init()];
            }); }); });
            loadProgress.add(3, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind(this.sceneKey, cb)];
            }); }); });
        }
        loadProgress.run(function (p) { return _this.dstPercent = p; });
    };
    // 加载大厅
    LoginWindCtrl.prototype.loadLobby = function () {
        var _this = this;
        if (!this.isValid) {
            return;
        }
        this.sceneKey = 'lobby';
        GameHelper_1.gameHpr.isNoviceMode = false;
        var loadProgress = this.loadAssets();
        loadProgress.add(3, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind(this.sceneKey, cb)];
        }); }); });
        loadProgress.run(function (p) { return _this.dstPercent = p; });
    };
    // 加载游戏
    LoginWindCtrl.prototype.loadGame = function (data) {
        var _this = this;
        if (!this.isValid) {
            return;
        }
        this.sceneKey = 'main';
        GameHelper_1.gameHpr.isNoviceMode = false;
        GameHelper_1.gameHpr.initServerConfig(data);
        var sid = GameHelper_1.gameHpr.getSid();
        this.curPercent = this.dstPercent = 0;
        var loadProgress = this.loadAssets();
        if (!this.model.isInitAsset2) {
            loadProgress.add(1, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, GameHelper_1.gameHpr.player.init(data.player)];
            }); }); });
            loadProgress.add(5, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, GameHelper_1.gameHpr.world.init(sid, data.world, cb)];
            }); }); }); //57
            loadProgress.spawn(6, [
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UI', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/UIMenuChild', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadPnl('common/Top', function (done, total) { return cb(done / total); })];
                }); }); },
                function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                    return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind(this.sceneKey, cb)];
                }); }); },
            ]);
        }
        else {
            loadProgress.add(1, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, GameHelper_1.gameHpr.player.init(data.player)];
            }); }); })
                .add(5, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, GameHelper_1.gameHpr.world.init(sid, data.world, cb)];
            }); }); })
                .add(3, function (cb) { return __awaiter(_this, void 0, void 0, function () { return __generator(this, function (_a) {
                return [2 /*return*/, ViewHelper_1.viewHelper.preloadWind(this.sceneKey, cb)];
            }); }); });
        }
        loadProgress.run(function (p) { return _this.dstPercent = p; });
    };
    LoginWindCtrl.prototype.loadNotice = function (val) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return _this.emit(mc.Event.LOAD_NOTICE, val, resolve); })];
            });
        });
    };
    LoginWindCtrl.prototype.loadAllNotice = function (progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return _this.emit(mc.Event.LOAD_ALL_NOTICE, resolve, function (done, total) { return progessCallback(done / total); }); })];
            });
        });
    };
    // 进入游戏
    LoginWindCtrl.prototype.go = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.model.isInitAsset2 = true;
                        this.setDescDone(true);
                        this.setDescLoad('login.wait_enter_' + this.sceneKey);
                        if (this.showLoginButton) {
                            TaHelper_1.taHelper.track('ta_tutorial_v2', { tutorial_step: "0-" + this.sceneKey });
                        }
                        return [4 /*yield*/, ut.wait(1)];
                    case 1:
                        _a.sent();
                        ViewHelper_1.viewHelper.gotoWind(this.sceneKey);
                        return [2 /*return*/];
                }
            });
        });
    };
    LoginWindCtrl.prototype.update = function (dt) {
        if (this.curPercent >= this.dstPercent || !this.curLoadProgressLbl) {
            return;
        }
        this.curPercent = Math.min(this.curPercent + 1 * dt, this.dstPercent);
        if (this.curPercent >= 1) {
            this.go();
        }
        else {
            this.curLoadProgressLbl.string = Math.min(Math.floor(this.curPercent * 100), 99) + '';
        }
    };
    LoginWindCtrl = __decorate([
        ccclass
    ], LoginWindCtrl);
    return LoginWindCtrl;
}(mc.BaseWindCtrl));
exports.default = LoginWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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