
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/playback/PlaybackUIPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4a4cemaX+lCZJfgWzEA4AxM', 'PlaybackUIPnlCtrl');
// app/script/view/playback/PlaybackUIPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PlaybackUIPnlCtrl = /** @class */ (function (_super) {
    __extends(PlaybackUIPnlCtrl, _super);
    function PlaybackUIPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pauseNode_ = null; // path://pause_be_n
        _this.playNode_ = null; // path://play_be_n
        _this.progressNode_ = null; // path://progress_n
        _this.sliderNode_ = null; // path://progress_n/slider_se_n
        //@end
        _this.model = null;
        _this.area = null;
        _this.speedMulIndex = 0;
        _this.startWorldPoint = cc.v2();
        _this.startPoint = cc.v2();
        _this.paused = false;
        _this.tempFrameIndex = -1;
        _this.touchId = -1;
        return _this;
    }
    PlaybackUIPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.PLAYBACK_END] = this.onPlaybackEnd, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.PLAYBACK_LOAD_END] = this.onLoadEnd, _b.enter = true, _b),
        ];
    };
    PlaybackUIPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var handle;
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false, isClean: false });
                this.model = this.getModel('playback');
                // 监听鼠标的缩放
                this.node.on(cc.Node.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
                this.node.on(cc.Node.EventType.MOUSE_MOVE, this.onMouseMove, this);
                this.sliderNode_.on(cc.Node.EventType.TOUCH_START, this.onSliderTouchStart, this);
                this.sliderNode_.on(cc.Node.EventType.TOUCH_END, this.onSliderTouchEnd, this);
                this.sliderNode_.on(cc.Node.EventType.TOUCH_CANCEL, this.onSliderTouchEnd, this);
                handle = this.sliderNode_.Child('Handle');
                handle.on(cc.Node.EventType.TOUCH_START, this.onSliderTouchStart, this);
                handle.on(cc.Node.EventType.TOUCH_END, this.onSliderTouchEnd, this);
                handle.on(cc.Node.EventType.TOUCH_CANCEL, this.onSliderTouchEnd, this);
                return [2 /*return*/];
            });
        });
    };
    PlaybackUIPnlCtrl.prototype.onEnter = function (area) {
        var _a;
        this.area = area;
        this.setParam({ isClean: true });
        var fsp = area.getFspModel();
        this.updateSpeedMul((_a = fsp === null || fsp === void 0 ? void 0 : fsp.getSpeedMulIndex()) !== null && _a !== void 0 ? _a : 2);
        this.setPause(!!(fsp === null || fsp === void 0 ? void 0 : fsp.isPause()));
    };
    PlaybackUIPnlCtrl.prototype.onRemove = function () {
        this.tempFrameIndex = -1;
        if (this.paused) {
            audioMgr.resumeAll();
        }
    };
    PlaybackUIPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://back_main_be
    PlaybackUIPnlCtrl.prototype.onClickBackMain = function (event, data) {
        ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
    };
    // path://progress_n/speed/speed_be@0
    PlaybackUIPnlCtrl.prototype.onClickSpeed = function (event, data) {
        var _a;
        var val = data === '0' ? -1 : 1;
        var i = cc.misc.clampf(this.speedMulIndex + val, 0, Constant_1.PLAYBACK_MULS.length - 1);
        if (this.speedMulIndex !== i) {
            (_a = this.getFspModel()) === null || _a === void 0 ? void 0 : _a.setSpeedMulIndex(i);
            this.updateSpeedMul(i);
        }
    };
    // path://pause_be_n
    PlaybackUIPnlCtrl.prototype.onClickPause = function (event, data) {
        this.pause();
    };
    // path://play_be_n
    PlaybackUIPnlCtrl.prototype.onClickPlay = function (event, data) {
        this.resume();
    };
    // path://progress_n/slider_se_n
    PlaybackUIPnlCtrl.prototype.onClickSlider = function (event, data) {
        this.model.setProgress(event.progress);
        this.updateProgress();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PlaybackUIPnlCtrl.prototype.onPlaybackEnd = function () {
        this.setPause(true);
    };
    PlaybackUIPnlCtrl.prototype.onLoadEnd = function () {
        this.setPause(false);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PlaybackUIPnlCtrl.prototype.getFspModel = function () {
        return this.area.getFspModel();
    };
    PlaybackUIPnlCtrl.prototype.pause = function () {
        var _a;
        if (this.paused) {
            return;
        }
        (_a = this.getFspModel()) === null || _a === void 0 ? void 0 : _a.setPause(true);
        this.setPause(true);
    };
    PlaybackUIPnlCtrl.prototype.resume = function () {
        var model = this.getFspModel();
        if (model) {
            model.setPause(false);
            this.setPause(false);
        }
        else {
            this.model.setCurFrameIndex(0);
            this.emit(EventType_1.default.REPLAYBACK, this.speedMulIndex, false);
        }
    };
    PlaybackUIPnlCtrl.prototype.setPause = function (val) {
        var _a;
        this.paused = val;
        this.pauseNode_.active = !val;
        this.playNode_.active = val;
        if (!((_a = this.getFspModel()) === null || _a === void 0 ? void 0 : _a.isRunning)) {
        }
        else if (val) {
            audioMgr.pauseAll();
        }
        else {
            audioMgr.resumeAll();
        }
    };
    PlaybackUIPnlCtrl.prototype.updateSpeedMul = function (i) {
        var _a;
        this.speedMulIndex = i;
        this.progressNode_.Child('speed/val', cc.Label).string = ((_a = Constant_1.PLAYBACK_MULS[i]) === null || _a === void 0 ? void 0 : _a.text) || '1x';
    };
    // 鼠标滚动事件
    PlaybackUIPnlCtrl.prototype.onMouseWheel = function (event) {
        if (GameHelper_1.gameHpr.guide.isForceWorking()) {
            return;
        }
        var deltaScale = event.getScrollY() * 0.0001;
        CameraCtrl_1.cameraCtrl.zoomRatio += deltaScale;
        if (!CameraCtrl_1.cameraCtrl.scaleIsMinOrMax()) {
            var speed = CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint).subSelf(this.startWorldPoint);
            CameraCtrl_1.cameraCtrl.setPosition(CameraCtrl_1.cameraCtrl.getPosition().subSelf(speed));
        }
    };
    // 鼠标移动事件
    PlaybackUIPnlCtrl.prototype.onMouseMove = function (event) {
        this.startPoint.set(event.getLocation());
        this.startWorldPoint.set(CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(this.startPoint));
    };
    PlaybackUIPnlCtrl.prototype.onSliderTouchStart = function (event) {
        if (this.touchId !== -1) {
            return;
        }
        this.touchId = event.getID();
        this.tempFrameIndex = this.model.getCurFrameIndex();
        ViewHelper_1.viewHelper.showLoadingWait(true);
        this.pause();
    };
    PlaybackUIPnlCtrl.prototype.onSliderTouchEnd = function (event) {
        if (this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        if (this.tempFrameIndex !== this.model.getCurFrameIndex()) {
            this.emit(EventType_1.default.REPLAYBACK, this.speedMulIndex, false);
        }
        else {
            ViewHelper_1.viewHelper.showLoadingWait(false);
            this.resume();
        }
        this.tempFrameIndex = -1;
    };
    PlaybackUIPnlCtrl.prototype.updateProgress = function () {
        var endTime = this.model.getDuration() || 0;
        var time = Math.min(this.model.getCurTime(), endTime);
        var progress = time / endTime;
        this.sliderNode_.Component(cc.Slider).progress = progress;
        this.sliderNode_.Child('bar', cc.Sprite).fillRange = progress;
        this.progressNode_.Child('time/val', cc.Label).string = ut.millisecondFormat(time);
        this.progressNode_.Child('time/max', cc.Label).string = '/' + ut.millisecondFormat(endTime);
    };
    PlaybackUIPnlCtrl.prototype.update = function (dt) {
        if (!this.paused) {
            this.updateProgress();
        }
    };
    PlaybackUIPnlCtrl = __decorate([
        ccclass
    ], PlaybackUIPnlCtrl);
    return PlaybackUIPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PlaybackUIPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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