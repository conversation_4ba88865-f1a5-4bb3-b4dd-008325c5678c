
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/RulePositionCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '45f8aMrhk1KVa2bbF9QZ/VG', 'RulePositionCmpt');
// app/script/view/lobby/RulePositionCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 自动适位置
 */
var RulePositionCmpt = /** @class */ (function (_super) {
    __extends(RulePositionCmpt, _super);
    function RulePositionCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.target = null;
        _this.offsetX = 0;
        _this.preWidth = -1;
        return _this;
    }
    RulePositionCmpt.prototype.update = function () {
        if (!this.target) {
            return;
        }
        else if (this.preWidth !== this.target.width * this.target.scaleX) {
            this.do();
        }
    };
    RulePositionCmpt.prototype.do = function () {
        this.preWidth = this.target.width * this.target.scaleX;
        var x = this.target.x + this.preWidth * 0.5 + this.node.width * 0.5;
        this.node.x = x + this.offsetX;
    };
    __decorate([
        property(cc.Node)
    ], RulePositionCmpt.prototype, "target", void 0);
    __decorate([
        property
    ], RulePositionCmpt.prototype, "offsetX", void 0);
    RulePositionCmpt = __decorate([
        ccclass
    ], RulePositionCmpt);
    return RulePositionCmpt;
}(cc.Component));
exports.default = RulePositionCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGxvYmJ5XFxSdWxlUG9zaXRpb25DbXB0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFNLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRTVDOztHQUVHO0FBRUg7SUFBOEMsb0NBQVk7SUFBMUQ7UUFBQSxxRUFzQkM7UUFuQlcsWUFBTSxHQUFZLElBQUksQ0FBQTtRQUV0QixhQUFPLEdBQVcsQ0FBQyxDQUFBO1FBRW5CLGNBQVEsR0FBVyxDQUFDLENBQUMsQ0FBQTs7SUFlakMsQ0FBQztJQWJHLGlDQUFNLEdBQU47UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRTtZQUNkLE9BQU07U0FDVDthQUFNLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRTtZQUNqRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUE7U0FDWjtJQUNMLENBQUM7SUFFTyw2QkFBRSxHQUFWO1FBQ0ksSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQTtRQUN0RCxJQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsUUFBUSxHQUFHLEdBQUcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxHQUFHLENBQUE7UUFDckUsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUE7SUFDbEMsQ0FBQztJQWxCRDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDO29EQUNZO0lBRTlCO1FBREMsUUFBUTtxREFDa0I7SUFMVixnQkFBZ0I7UUFEcEMsT0FBTztPQUNhLGdCQUFnQixDQXNCcEM7SUFBRCx1QkFBQztDQXRCRCxBQXNCQyxDQXRCNkMsRUFBRSxDQUFDLFNBQVMsR0FzQnpEO2tCQXRCb0IsZ0JBQWdCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbi8qKlxyXG4gKiDoh6rliqjpgILkvY3nva5cclxuICovXHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFJ1bGVQb3NpdGlvbkNtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxyXG4gICAgcHJpdmF0ZSB0YXJnZXQ6IGNjLk5vZGUgPSBudWxsXHJcbiAgICBAcHJvcGVydHlcclxuICAgIHByaXZhdGUgb2Zmc2V0WDogbnVtYmVyID0gMFxyXG5cclxuICAgIHByaXZhdGUgcHJlV2lkdGg6IG51bWJlciA9IC0xXHJcblxyXG4gICAgdXBkYXRlKCkge1xyXG4gICAgICAgIGlmICghdGhpcy50YXJnZXQpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnByZVdpZHRoICE9PSB0aGlzLnRhcmdldC53aWR0aCAqIHRoaXMudGFyZ2V0LnNjYWxlWCkge1xyXG4gICAgICAgICAgICB0aGlzLmRvKClcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBkbygpIHtcclxuICAgICAgICB0aGlzLnByZVdpZHRoID0gdGhpcy50YXJnZXQud2lkdGggKiB0aGlzLnRhcmdldC5zY2FsZVhcclxuICAgICAgICBjb25zdCB4ID0gdGhpcy50YXJnZXQueCArIHRoaXMucHJlV2lkdGggKiAwLjUgKyB0aGlzLm5vZGUud2lkdGggKiAwLjVcclxuICAgICAgICB0aGlzLm5vZGUueCA9IHggKyB0aGlzLm9mZnNldFhcclxuICAgIH1cclxufSJdfQ==