
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/AnimHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '537bdNKsFtESo87YS8+C6/v', 'AnimHelper');
// app/script/common/helper/AnimHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.animHelper = void 0;
var PawnAnimationCmpt_1 = require("../../view/area/PawnAnimationCmpt");
var IgnoreFlashLightCmpt_1 = require("../../view/cmpt/IgnoreFlashLightCmpt");
var Constant_1 = require("../constant/Constant");
var FlashLightShaderCtrl_1 = require("../shader/FlashLightShaderCtrl");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
/**
 * 动画帮助
 */
var AnimHelper = /** @class */ (function () {
    function AnimHelper() {
        this.guideFingerMap = {};
        this.flutterHpDataList = [];
        this.nodePoolMap = {};
    }
    AnimHelper.prototype.clean = function () {
        for (var key in this.nodePoolMap) {
            this.nodePoolMap[key].destroy();
        }
        this.nodePoolMap = {};
    };
    AnimHelper.prototype.hideNodeByKey = function (key) {
        var node = this.nodePoolMap[key];
        if (node) {
            node.opacity = 0;
        }
    };
    // 播放扫光
    AnimHelper.prototype.playFlashLight = function (list) {
        // audioMgr.playSFX('common/sound07')
        this.flashLight(list);
    };
    AnimHelper.prototype.playFlashLightByNode = function (node, count) {
        var e_1, _a;
        if (node.getComponent(cc.Sprite)) {
            var flashLight = node.getComponent(FlashLightShaderCtrl_1.default);
            if (!flashLight) {
                flashLight = node.addComponent(FlashLightShaderCtrl_1.default);
            }
            flashLight.setCount(count);
            flashLight.play();
        }
        try {
            for (var _b = __values(node.children), _c = _b.next(); !_c.done; _c = _b.next()) {
                var child = _c.value;
                this.playFlashLightByNode(child, count);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
    };
    AnimHelper.prototype.flashLight = function (list) {
        var _this = this;
        list.forEach(function (m) {
            if (m.getComponent(IgnoreFlashLightCmpt_1.default)) {
                return; //这里特殊处理 如果有这个节点
            }
            else if (m.getComponent(cc.Sprite)) {
                var shader = m.getComponent(FlashLightShaderCtrl_1.default);
                if (!shader) {
                    shader = m.addComponent(FlashLightShaderCtrl_1.default);
                }
                shader.play();
            }
            if (m.childrenCount > 0) {
                _this.flashLight(m.children);
            }
        });
    };
    // 飘文本 资产
    AnimHelper.prototype.playFlutterAsset = function (val, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_TEXT_ASSET', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterAsset !root.isValid')];
                        }
                        it.parent = root;
                        it.Child('val', cc.Label).string = ut.numberToString(val);
                        it.setPosition(position.x - 20, position.y);
                        it.opacity = 255;
                        cc.tween(it)
                            .by(0.3, { y: 30 }, { easing: cc.easing.sineOut })
                            .by(1, { y: 40, opacity: 0 }, { easing: cc.easing.sineIn })
                            .call(function () { return nodePoolMgr.put(it); }).start();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 飘文本 资产
    AnimHelper.prototype.playFlutterCellRes = function (icon, val, delay, isMore, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, y;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_CELL_RES', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid || !root.Data) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterCellRes !root.isValid')];
                        }
                        it.parent = root;
                        it.Child('lay/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(icon);
                        it.Child('lay/val', cc.Label).setLocaleKey('ui.res_desc', val);
                        y = position.y - 12;
                        it.setPosition(position.x, y);
                        it.opacity = 0;
                        it.scale = 0.5;
                        if (isMore) {
                            cc.tween(it)
                                .delay(delay)
                                .call(function () { return it.opacity = 255; })
                                .to(0.3, { y: y + 30, scale: 1 })
                                .to(0.5, { y: y + 70, opacity: 0 })
                                .call(function () { return nodePoolMgr.put(it); }).start();
                        }
                        else {
                            cc.tween(it)
                                .delay(delay)
                                .call(function () { return it.opacity = 255; })
                                .to(0.3, { y: y + 30, scale: 1 }, { easing: cc.easing.sineOut })
                                .delay(0.3)
                                .to(1.5, { y: y + 70, opacity: 0 }, { easing: cc.easing.sineIn })
                                .call(function () { return nodePoolMgr.put(it); }).start();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 飘宝箱
    AnimHelper.prototype.playFlutterTreasure = function (icon, val, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, spr, url, sf, y;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_TREASURE', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid || !root.Data) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterTreasure !root.isValid')];
                        }
                        it.parent = root;
                        spr = it.Child('lay/icon', cc.Sprite), url = 'icon/' + icon;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.SpriteFrame, key)];
                    case 2:
                        sf = _a.sent();
                        if (!root.isValid || !spr.isValid) {
                            return [2 /*return*/];
                        }
                        spr.spriteFrame = sf || null;
                        it.Child('lay/val', cc.Label).string = '+' + val;
                        y = position.y - 12;
                        it.setPosition(position.x, y);
                        it.opacity = 0;
                        it.scale = 0.5;
                        cc.tween(it)
                            .delay(0.3)
                            .call(function () { return it.opacity = 255; })
                            .to(0.3, { y: y + 30, scale: 1 }, { easing: cc.easing.sineOut })
                            .delay(0.5)
                            .to(1.5, { y: y + 70, opacity: 0 }, { easing: cc.easing.sineIn })
                            .call(function () { return nodePoolMgr.put(it); }).start();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放领取产出
    AnimHelper.prototype.playFlutterCellOutput = function (delay, item, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, y;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_CELL_OUTPUT', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid || !root.Data) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterCellRes !root.isValid')];
                        }
                        it.parent = root;
                        it.Child('lay/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[item.type]);
                        it.Child('lay/val', cc.Label).string = '+' + item.count;
                        y = position.y + 24;
                        it.setPosition(position.x, y);
                        it.opacity = 0;
                        cc.tween(it)
                            .delay(delay)
                            .call(function () { return it.opacity = 255; })
                            .to(1, { y: y + 64, opacity: 50 })
                            .call(function () { return nodePoolMgr.put(it); }).start();
                        return [2 /*return*/];
                }
            });
        });
    };
    // // 播放创建主城
    // public async playCreateMainCity(root: cc.Node, position: cc.Vec2, key: string) {
    //     const it = await nodePoolMgr.get('effect/CREATE_MAIN_CITY', key)
    //     if (!root.isValid || !root.Data) {
    //         nodePoolMgr.put(it)
    //         return logger.info('playCreateMainCity !root.isValid')
    //     }
    //     it.parent = root
    //     it.setPosition(position)
    //     audioMgr.playSFX('main/get_new_cell')
    //     await it.Child('anim', cc.Animation).playAsync()
    //     nodePoolMgr.put(it)
    // }
    // 播放新地块特效
    AnimHelper.prototype.playNewCellEffect = function (root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('effect/NEW_CELL_EFFECT', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid || !root.Data) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playNewCellEffect !root.isValid')];
                        }
                        it.parent = root;
                        it.setPosition(position);
                        audioMgr.playSFX('main/get_new_cell');
                        return [4 /*yield*/, it.Child('anim', cc.Animation).playAsync()];
                    case 2:
                        _a.sent();
                        nodePoolMgr.put(it);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放要塞摧毁
    AnimHelper.prototype.playFortDestroyEffect = function (root, position) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root || !root.isValid) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('effect/CITY_DESTROY_2102')];
                    case 1:
                        it = _a.sent();
                        if (!it || !it.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = root;
                        it.setPosition(position);
                        it.opacity = 255;
                        return [4 /*yield*/, it.Child('anim', cc.Animation).playAsync()];
                    case 2:
                        _a.sent();
                        nodePoolMgr.put(it);
                        return [2 /*return*/];
                }
            });
        });
    };
    // // 播放攻占胜利特效
    // public async playOccupyCellEffect(root: cc.Node, position: cc.Vec2, zIndex: number, key: string) {
    //     const it = await nodePoolMgr.get('effect/OCCUPY_CELL_EFFECT', key)
    //     if (!root.isValid || !root.Data) {
    //         nodePoolMgr.put(it)
    //         return null
    //     }
    //     it.parent = root
    //     it.setPosition(position)
    //     it.zIndex = zIndex
    //     audioMgr.playSFX('main/get_new_cell')
    //     await it.Child('anim', cc.Animation).playAsync()
    //     return it
    // }
    // 播放修复主城
    AnimHelper.prototype.playRestoreMaincity = function (root, position, key, loadComplete) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        audioMgr.preload('novice/xjzc');
                        return [4 /*yield*/, this.playRestoreMaincityCost(root, position)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, nodePoolMgr.get('effect/RESTORE_MAIN_CITY', key)];
                    case 2:
                        it = _a.sent();
                        if (!root.isValid || !root.Data) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playRestoreMaincity !root.isValid')];
                        }
                        loadComplete();
                        it.parent = root;
                        it.setPosition(position);
                        return [4 /*yield*/, audioMgr.playSFX('novice/xjzc')];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, it.Child('anim', cc.Animation).playAsync()];
                    case 4:
                        _a.sent();
                        nodePoolMgr.put(it);
                        return [2 /*return*/];
                }
            });
        });
    };
    AnimHelper.prototype.playRestoreMaincityCost = function (root, position) {
        var _this = this;
        var top = cc.find('Canvas/View/TopPnl/top');
        if (!top)
            return;
        var camera = cc.Camera.findCamera(root);
        var cameraUI = cc.Camera.findCamera(top);
        var worldTarget = root.convertToWorldSpaceAR(position);
        var screenTarget = camera.getWorldToScreenPoint(worldTarget);
        var interval = 0.07;
        var city = root.parent.FindChild('city/CITY_10010');
        if (city) {
            cc.tween(city).delay(.6).repeat(2, cc.tween(city).to(.1, { scale: 1.1 }).to(.1, { scale: 1 })).start();
        }
        var cereal = cc.find('cereal_n/val', top);
        if (cereal) {
            var node = cc.instantiate(cereal);
            var world = cereal.convertToWorldSpaceAR(cc.Vec2.ZERO);
            var local = top.convertToNodeSpaceAR(world);
            node.parent = top;
            node.setPosition(local);
            var worldPos = cameraUI.getScreenToWorldPoint(screenTarget);
            var localPos = top.convertToNodeSpaceAR(worldPos);
            this._playIconMoveAction(node, local, cc.v2(localPos.x, localPos.y));
        }
        var timber = cc.find('timber_n/val', top);
        if (timber) {
            var world = timber.convertToWorldSpaceAR(cc.Vec2.ZERO);
            var local_1 = top.convertToNodeSpaceAR(world);
            var worldPos = cameraUI.getScreenToWorldPoint(screenTarget);
            var localPos_1 = top.convertToNodeSpaceAR(worldPos);
            for (var i = 0; i < 2; i++) {
                ut.wait((i + 1) * interval).then(function () {
                    var node = cc.instantiate(timber);
                    node.parent = top;
                    node.setPosition(local_1);
                    _this._playIconMoveAction(node, local_1, cc.v2(localPos_1.x, localPos_1.y));
                });
            }
        }
        var stone = cc.find('stone_n/val', top);
        if (stone) {
            var world = stone.convertToWorldSpaceAR(cc.Vec2.ZERO);
            var local_2 = top.convertToNodeSpaceAR(world);
            var worldPos = cameraUI.getScreenToWorldPoint(screenTarget);
            var localPos_2 = top.convertToNodeSpaceAR(worldPos);
            for (var i = 0; i < 2; i++) {
                ut.wait((i + 3) * interval).then(function () {
                    var node = cc.instantiate(stone);
                    node.parent = top;
                    node.setPosition(local_2);
                    _this._playIconMoveAction(node, local_2, cc.v2(localPos_2.x, localPos_2.y));
                });
            }
        }
        return ut.wait(.3);
    };
    AnimHelper.prototype._playIconMoveAction = function (node, src, dst, delay) {
        if (delay === void 0) { delay = 0; }
        node.scale = 1;
        var bezierAct = cc.bezierTo(.8, [src, cc.v2(dst.x, src.y - 100), cc.v2(dst.x, dst.y)]);
        return new Promise(function (resolve) { return cc.tween(node).delay(delay).parallel(cc.tween(node).then(bezierAct.easing(cc.easeSineOut())), cc.tween(node).delay(.5).to(.3, { scale: 0.2 }, { easing: cc.easeSineIn().easing })).then(cc.destroySelf()).call(resolve).start(); });
    };
    // 飘血
    AnimHelper.prototype.readyPlayFlutterHp = function (data, position, topLayerNode, key) {
        if (!data || !position || !topLayerNode || !topLayerNode.isValid) {
            return;
        }
        var arr = [], uid = data.uid;
        if (data.value || (!data.trueDamage && !data.heal)) {
            var type = ['isDodge', 'isParry', 'isTurntheblade', 'isWithstand', 'isImmune', 'isAnger'].find(function (k) { return data[k]; });
            if (type) {
            }
            else if (data.value === 0) {
                type = 'isMiss';
            }
            else if (data.value > 0) {
                type = 'isHeal';
            }
            else {
                type = data.hasShield ? 'isHasShield' : 'isDamage';
            }
            arr.push({ type: type, value: data.value, isCrit: data.isCrit });
        }
        if (data.trueDamage) {
            var type = data.hasShield ? 'isHasShield' : 'isTrueDamage';
            arr.push({ type: type, value: data.trueDamage });
        }
        if (data.heal) {
            arr.push({ type: 'isHeal', value: data.heal });
        }
        var positionDataMap = this.flutterHpDataList.find(function (m) { return m.position.equals(position) && m.root.uuid === topLayerNode.uuid && m.key === key; });
        if (!positionDataMap) {
            positionDataMap = this.flutterHpDataList.add({ position: position, root: topLayerNode, key: key, dataMap: {} });
        }
        var dataMap = positionDataMap.dataMap;
        arr.forEach(function (m) {
            var k = m.type + '_' + uid;
            var d = dataMap[k];
            if (!d) {
                dataMap[k] = m;
            }
            else if (m.type === 'isDamage' || m.type === 'isTrueDamage' || m.type === 'isHeal' || m.type === 'isHasShield' || m.type === 'isAnger') {
                d.value += m.value;
            }
        });
    };
    AnimHelper.prototype.update = function (dt) {
        var _this = this;
        this.flutterHpDataList.forEach(function (m) {
            var root = m.root, position = m.position, key = m.key, dataMap = m.dataMap;
            if (!root.isValid) {
                return;
            }
            var len = Object.keys(dataMap).length;
            var one = len % 2 !== 0, sx = Math.min(80 / len, 20);
            var idx = 0, delay = Math.min(1 / len, 0.2);
            var arr = [], posXArr = [];
            for (var k in dataMap) {
                var d = dataMap[k];
                var dir = idx % 2 === 0 ? -1 : 1;
                var cnt = Math.floor((idx + (one ? 1 : 0)) / 2);
                posXArr.push(position.x + (sx * cnt + (one ? 0 : sx * 0.5)) * dir);
                arr.push(d);
                idx += 1;
            }
            arr.forEach(function (m, i) {
                m.x = posXArr.randomRemove();
                m.delay = i * delay;
                _this.playFlutterHp(m, root, position, key);
            });
        });
        this.flutterHpDataList = [];
    };
    // 飘血
    AnimHelper.prototype.playFlutterHp = function (data, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, type, val, y;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(root === null || root === void 0 ? void 0 : root.isValid)) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_HP_GENERAL', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterHp !root.isValid')];
                        }
                        else if (!it || !it.isValid) {
                            return [2 /*return*/];
                        }
                        it.parent = root;
                        type = data.type, val = data.value;
                        if (type === 'isDodge') { //闪避
                            it.Swih('text')[0].setLocaleKey('ui.dodge_out');
                        }
                        else if (type === 'isParry') { //格挡
                            it.Swih('text')[0].setLocaleKey('ui.parry_out');
                        }
                        else if (type === 'isTurntheblade') { //招架
                            it.Swih('text')[0].setLocaleKey('ui.turntheblade_out');
                        }
                        else if (type === 'isWithstand') { //抵挡
                            it.Swih('text')[0].setLocaleKey('ui.withstand_out');
                        }
                        else if (type === 'isImmune') { //免疫
                            it.Swih('text')[0].setLocaleKey('ui.immune_out');
                        }
                        else if (type === 'isAnger') { //怒气
                            it.Swih('val')[0].Component(cc.Label).Color('#FFD452').string = '+' + Math.abs(val);
                        }
                        else if (type === 'isHasShield') {
                            it.Swih('val')[0].Component(cc.Label).Color('#A4A4A4').string = '' + Math.abs(val);
                        }
                        else if (type === 'isTrueDamage') {
                            it.Swih('val')[0].Component(cc.Label).Color('#FFFFFF').string = '' + Math.abs(val);
                        }
                        else if (type === 'isMiss') {
                            it.Swih('val')[0].Component(cc.Label).Color('#FF9162').string = 'miss';
                        }
                        else {
                            it.Swih('val')[0].Component(cc.Label).Color(type === 'isHeal' ? '#21DC2D' : '#FF9162').string = '' + Math.abs(val);
                        }
                        if (!(data === null || data === void 0 ? void 0 : data.delay)) return [3 /*break*/, 3];
                        it.opacity = 0;
                        return [4 /*yield*/, ut.wait(data.delay)];
                    case 2:
                        _a.sent();
                        if (!root.isValid || !it.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        _a.label = 3;
                    case 3:
                        it.x = (data === null || data === void 0 ? void 0 : data.x) || position.x + ut.randomRange(-20, 20);
                        y = it.y = position.y + 20;
                        it.opacity = 255;
                        it.scale = 0.6;
                        if (data === null || data === void 0 ? void 0 : data.isCrit) {
                            cc.tween(it)
                                .to(0.12, { y: y + 40, scale: 3 }, { easing: cc.easing.sineOut })
                                .to(0.18, { y: y + 10, scale: 0.85 }, { easing: cc.easing.sineOut })
                                .to(0.7, { y: y + 24, opacity: 0, scale: 1.1 }, { easing: cc.easing.sineIn })
                                .call(function () { return nodePoolMgr.put(it); })
                                .start();
                        }
                        else {
                            cc.tween(it)
                                .to(0.18, { y: y + 12, scale: 1 }, { easing: cc.easing.sineOut })
                                .to(0.72, { y: y + 24, opacity: 0, scale: 1.1 }, { easing: cc.easing.sineIn })
                                .call(function () { return nodePoolMgr.put(it); })
                                .start();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放添加怒气
    AnimHelper.prototype.playFlutterAnger = function (val, root, position, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, y;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('flutter/FLUTTER_ANGER', key)];
                    case 1:
                        it = _a.sent();
                        if (!root.isValid) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playFlutterAnger !root.isValid')];
                        }
                        else if (!it || !it.isValid) {
                            return [2 /*return*/];
                        }
                        it.parent = root;
                        it.Child('val', cc.Label).string = '+' + val;
                        it.x = position.x + ut.randomRange(-20, 20);
                        y = it.y = position.y + 20;
                        it.opacity = 255;
                        it.scale = 0.6;
                        cc.tween(it)
                            .to(0.18, { y: y + 12, scale: 1 }, { easing: cc.easing.sineOut })
                            .to(0.72, { y: y + 24, opacity: 0, scale: 1.1 }, { easing: cc.easing.sineIn })
                            .call(function () { return nodePoolMgr.put(it); })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放子弹飞行
    AnimHelper.prototype.playBulletFly = function (data, root, key) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var url, it, isLocalCache, currTime, now, satrtPos, targetPos, shootOffset, diff, len, speed, ratio, sp, cmpt, time;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!root || !root.isValid) {
                            return [2 /*return*/];
                        }
                        url = 'bullet/BULLET_' + data.bulletId;
                        it = this.nodePoolMap[url], isLocalCache = true, currTime = data.currTime;
                        if (!!it) return [3 /*break*/, 2];
                        now = Date.now();
                        return [4 /*yield*/, nodePoolMgr.get(url, key)];
                    case 1:
                        it = _b.sent();
                        currTime = data.currTime + (Date.now() - now);
                        if (!root.isValid) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, logger.info('playBulletFly !root.isValid')];
                        }
                        else if (!it || !it.isValid || currTime >= data.needTime) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = root;
                        // 是否加入缓存
                        if (data.bulletId === 5038) {
                            this.nodePoolMap[url] = it;
                        }
                        else {
                            isLocalCache = false;
                        }
                        _b.label = 2;
                    case 2:
                        satrtPos = data.startPos, targetPos = data.targetPos;
                        cc.tween(it).stop();
                        it.stopAllActions();
                        it.opacity = 255;
                        it.angle = ut.getAngle(satrtPos, targetPos) - 90;
                        shootOffset = ((_a = it.Child('body/shoot')) === null || _a === void 0 ? void 0 : _a.y) || 0;
                        if (shootOffset) {
                            satrtPos.addSelf(satrtPos.sub(targetPos).normalizeSelf().mulSelf(shootOffset));
                        }
                        diff = satrtPos.sub(targetPos), len = diff.mag();
                        speed = diff.normalizeSelf();
                        ratio = currTime / data.needTime;
                        sp = speed.mulSelf(len * ratio).addSelf(satrtPos);
                        it.setPosition(sp);
                        cmpt = it.getComponent(PawnAnimationCmpt_1.default).init(it.Child('body/anim', cc.Sprite), data.bulletId, key);
                        cmpt.play('stand');
                        time = data.needTime - currTime;
                        cmpt.resetMove()
                            .setMoveParabolaHeight(data.bulletId === 5003 || data.bulletId === 5022 ? 20 : 0)
                            .moveNodeOne(currTime, data.needTime, satrtPos, targetPos, function () {
                            if (!it.isValid) {
                            }
                            else if (data.bulletId === 5038) {
                                cc.tween(it).delay(1).to(0.1, { opacity: 0 }).start();
                            }
                            else {
                                it.opacity = 0;
                            }
                        });
                        if (!isLocalCache) {
                            ut.wait(time * 0.001).then(function () { return nodePoolMgr.put(it); });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放战斗特效
    AnimHelper.prototype.playBattleEffect = function (data, root, key) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var now, it, currTime, delay, t, cmpt;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!root || !root.isValid) {
                            return [2 /*return*/];
                        }
                        now = Date.now();
                        return [4 /*yield*/, nodePoolMgr.get('effect/BATTLE_EFFECT_' + data.type, key)];
                    case 1:
                        it = _c.sent();
                        if (!it || !it.isValid || !root.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        currTime = 0, delay = (_a = data.delay) !== null && _a !== void 0 ? _a : 0;
                        t = (Date.now() - now) * 0.001;
                        if (t >= delay) {
                            currTime = t - delay;
                            delay = 0;
                        }
                        else {
                            delay -= t;
                        }
                        it.parent = root;
                        it.setPosition(data.pos);
                        if (data.zIndex) {
                            it.zIndex = data.zIndex;
                        }
                        if ((_b = data.params) === null || _b === void 0 ? void 0 : _b.scaleX) {
                            it.scaleX = data.params.scaleX;
                        }
                        it.opacity = 0;
                        cmpt = it.getComponent(PawnAnimationCmpt_1.default).init(it.Child('body/anim', cc.Sprite), data.type, key);
                        cmpt.delayPlay(delay * 1000, 'stand', function () { } /* () => nodePoolMgr.put(it) */, currTime * 1000);
                        ut.wait(delay + cmpt.needPlayTime * 0.001 - currTime).then(function () { return nodePoolMgr.put(it); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放雪花
    AnimHelper.prototype.playSnowflake = function (root, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it, size;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root || !root.isValid) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('effect/SNOWFLAKE', key)];
                    case 1:
                        it = _a.sent();
                        if (!it || !root.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = root;
                        size = MapHelper_1.mapHelper.MAP_SIZE;
                        it.setPosition(size.x * 0.5 * Constant_1.TILE_SIZE, size.y * 0.5 * Constant_1.TILE_SIZE);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放地图表情
    AnimHelper.prototype.playCellEmoji = function (node, id, uid, startTime, key) {
        return __awaiter(this, void 0, void 0, function () {
            var nameLbl, it, type, root, emojiAnim, startPauseTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!node || !node.isValid) {
                            return [2 /*return*/, null];
                        }
                        nameLbl = node.Child('name', cc.Label);
                        if (nameLbl) {
                            nameLbl.string = '';
                        }
                        return [4 /*yield*/, nodePoolMgr.get('emoji/EMOJI_' + id, key)];
                    case 1:
                        it = _a.sent();
                        if (!it || !node.isValid) {
                            nodePoolMgr.put(it);
                            return [2 /*return*/, null];
                        }
                        node.opacity = 255;
                        type = Math.floor(id / 1000);
                        root = node.Child('root');
                        it.parent = root;
                        it.setPosition(0, 0);
                        it.scaleX = 1;
                        emojiAnim = it.Child('val', cc.Animation);
                        if (!(type === 2)) return [3 /*break*/, 3];
                        startPauseTime = 0.22 - startTime;
                        if (!emojiAnim) {
                        }
                        else if (startPauseTime > 0) {
                            emojiAnim.stop();
                            ut.wait(startPauseTime, emojiAnim).then(function () {
                                if (emojiAnim.isValid) {
                                    emojiAnim.play();
                                }
                            });
                        }
                        else {
                            emojiAnim.play();
                        }
                        return [4 /*yield*/, root.Component(cc.Animation).playAsync('play_cell_emoji', startTime)];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 7];
                    case 3:
                        nameLbl.string = !uid || uid === GameHelper_1.gameHpr.getUid() ? '' : ut.nameFormator(GameHelper_1.gameHpr.getPlayerName(uid), 4);
                        if (!emojiAnim) return [3 /*break*/, 5];
                        return [4 /*yield*/, emojiAnim.playAsync('emoji_' + id, startTime)];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5:
                        cc.tween(node).to(0.3, { opacity: 0 }).start();
                        return [4 /*yield*/, ut.wait(0.3)];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7: return [2 /*return*/, node];
                }
            });
        });
    };
    AnimHelper.prototype.slideIn = function (node, forward, time, bounceMag) {
        if (time === void 0) { time = .3; }
        if (bounceMag === void 0) { bounceMag = 15; }
        return __awaiter(this, void 0, void 0, function () {
            var size, sign, y_1, y2_1, t1_1, x_1, x2_1, t1_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        size = node.getBoundingBoxToWorld();
                        sign = forward === 'right' || forward === 'up' ? -1 : 1;
                        if (node['__slide_action'])
                            return [2 /*return*/];
                        node['__slide_action'] = true;
                        if (!(forward === 'up' || forward === 'down')) return [3 /*break*/, 2];
                        y_1 = node.y;
                        node.y += sign * size.height;
                        y2_1 = y_1 - sign * bounceMag;
                        t1_1 = (bounceMag / size.height) * time;
                        return [4 /*yield*/, new Promise(function (resolve) {
                                cc.tween(node)
                                    .to(time, { y: y2_1 }, { easing: cc.easeSineIn().easing })
                                    .to(t1_1 * 1.5, { y: y_1 }, { easing: cc.easeBackOut().easing })
                                    .call(resolve).start();
                            })];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 2:
                        x_1 = node.x;
                        node.x += sign * size.width;
                        x2_1 = x_1 - sign * bounceMag;
                        t1_2 = (bounceMag / size.width) * time;
                        return [4 /*yield*/, new Promise(function (resolve) {
                                cc.tween(node)
                                    .to(time, { x: x2_1 }, { easing: cc.easeSineIn().easing })
                                    .to(t1_2 * 1.5, { x: x_1 }, { easing: cc.easeBackOut().easing })
                                    .call(resolve).start();
                            })];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        delete node['__slide_action'];
                        return [2 /*return*/];
                }
            });
        });
    };
    AnimHelper.prototype.flyOneReward = function (it, startPos, endPos, anim, delay, multi) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        cc.tween(it)
                            .to(0.25, { position: startPos, scale: 1.5 })
                            .delay(delay ? delay : 0.2)
                            .to(0.5, { position: endPos, scale: 1 }, { easing: multi ? cc.easing.backIn : cc.easing.sineIn })
                            .call(function () {
                            anim.stopAllActions();
                            it.opacity = 0;
                            resolve();
                        }).start();
                    })];
            });
        });
    };
    // 添加手指
    AnimHelper.prototype.addFinger = function (node, opts, key) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!node || !node.isValid || this.guideFingerMap[opts.tag]) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get('guide/FINGER', key)];
                    case 1:
                        it = _a.sent();
                        if (!it || !node.isValid) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = node;
                        it.setPosition(opts.offset || cc.Vec2.ZERO);
                        it.angle = opts.angle || 0;
                        this.guideFingerMap[opts.tag] = it;
                        return [2 /*return*/];
                }
            });
        });
    };
    AnimHelper.prototype.removeFinger = function (tag) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                it = this.guideFingerMap[tag];
                if (it) {
                    nodePoolMgr.put(it);
                }
                return [2 /*return*/];
            });
        });
    };
    AnimHelper.prototype.hideFinger = function (tag, val) {
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_a) {
                it = this.guideFingerMap[tag];
                if (it) {
                    it['_hide_opacity'] = it.opacity = val ? 0 : 255;
                }
                return [2 /*return*/];
            });
        });
    };
    AnimHelper.prototype.hideAllFinger = function (val) {
        var _a;
        for (var key in this.guideFingerMap) {
            var it = this.guideFingerMap[key];
            it.opacity = val ? 0 : (_a = it['_hide_opacity']) !== null && _a !== void 0 ? _a : 255;
        }
    };
    AnimHelper.prototype.cleanAllFinger = function () {
        for (var key in this.guideFingerMap) {
            this.guideFingerMap[key].destroy();
        }
        this.guideFingerMap = {};
    };
    AnimHelper.prototype.getGuideFinger = function (tag) {
        return this.guideFingerMap[tag];
    };
    return AnimHelper;
}());
exports.animHelper = new AnimHelper();
if (cc.sys.isBrowser) {
    window['animHelper'] = exports.animHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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