
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelRollNumber.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a99e6I+L/JOWINCotk265XT', 'LabelRollNumber');
// app/core/component/LabelRollNumber.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent, menu = _a.menu;
var TextType;
(function (TextType) {
    TextType[TextType["NONE"] = 0] = "NONE";
    TextType[TextType["COMMA"] = 1] = "COMMA";
})(TextType || (TextType = {}));
// 滚动数字
var LabelRollNumber = /** @class */ (function (_super) {
    __extends(LabelRollNumber, _super);
    function LabelRollNumber() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.duration = 0.5;
        _this.minExpectNumber = 10; //小于该值 直接设置
        _this.type = TextType.NONE;
        _this.prefix = '';
        _this._label = null;
        _this._start = 0;
        _this._end = 0;
        _this.elapsed = 0;
        _this.time = 0;
        return _this;
    }
    Object.defineProperty(LabelRollNumber.prototype, "label", {
        get: function () {
            if (!this._label) {
                this._label = this.getComponent(cc.Label);
            }
            return this._label;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LabelRollNumber.prototype, "string", {
        get: function () {
            return this.label.string;
        },
        enumerable: false,
        configurable: true
    });
    LabelRollNumber.prototype.getCurrNumber = function () {
        var val = this.label.string;
        if (this.type === TextType.COMMA) {
            val = val.replace(/,/g, '');
        }
        if (this.prefix) {
            val = val.replace(this.prefix, '');
        }
        return Number(val);
    };
    LabelRollNumber.prototype.format = function (val) {
        var str = val + '';
        if (this.type === TextType.COMMA) {
            str = ut.formatNumberByComma(val);
        }
        return this.prefix + str;
    };
    LabelRollNumber.prototype.update = function (dt) {
        if (this.time === 0) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed >= this.time) {
            this.time = 0;
            this.label.string = this.format(this._end);
        }
        else {
            var current = Math.floor(this._start + (this._end - this._start) * this.sineOut());
            this.label.string = this.format(current);
        }
    };
    LabelRollNumber.prototype.sineOut = function () {
        return Math.sin((this.elapsed / this.time) * Math.PI * 0.5);
    };
    LabelRollNumber.prototype.setPrefix = function (val) {
        this.prefix = val;
        return this;
    };
    LabelRollNumber.prototype.set = function (val) {
        this.time = 0;
        this.label.string = this.format(val);
        return this;
    };
    LabelRollNumber.prototype.to = function (end, duration) {
        this._start = this.getCurrNumber();
        if (this._start === end) {
            return;
        }
        else if (Math.abs(end - this._start) < this.minExpectNumber) {
            return this.set(end);
        }
        this._end = end;
        this.elapsed = 0;
        this.time = duration || this.duration;
    };
    LabelRollNumber.prototype.by = function (val, duration) {
        if (!val) {
            return;
        }
        this._start = this.getCurrNumber();
        if (Math.abs(val) < this.minExpectNumber) {
            return this.set(this._start + val);
        }
        this._end = Math.max(this._start + val, 0);
        this.elapsed = 0;
        this.time = duration || this.duration;
    };
    LabelRollNumber.prototype.run = function (val, opts) {
        if (opts === null || opts === void 0 ? void 0 : opts.play) {
            if ((opts === null || opts === void 0 ? void 0 : opts.init) !== undefined) {
                this.set(opts.init);
            }
            this.to(val, opts === null || opts === void 0 ? void 0 : opts.duration);
        }
        else {
            this.set(val);
        }
    };
    __decorate([
        property()
    ], LabelRollNumber.prototype, "duration", void 0);
    __decorate([
        property()
    ], LabelRollNumber.prototype, "minExpectNumber", void 0);
    __decorate([
        property({ type: cc.Enum(TextType) })
    ], LabelRollNumber.prototype, "type", void 0);
    __decorate([
        property()
    ], LabelRollNumber.prototype, "prefix", void 0);
    LabelRollNumber = __decorate([
        ccclass,
        menu('自定义组件/LabelRollNumber'),
        requireComponent(cc.Label)
    ], LabelRollNumber);
    return LabelRollNumber;
}(cc.Component));
exports.default = LabelRollNumber;
cc.LabelRollNumber = LabelRollNumber;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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