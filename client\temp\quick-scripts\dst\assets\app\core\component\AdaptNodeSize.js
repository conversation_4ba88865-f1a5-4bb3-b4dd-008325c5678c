
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/AdaptNodeSize.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '15c51nUwVNPkL9wsqC12i+w', 'AdaptNodeSize');
// app/core/component/AdaptNodeSize.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
var AdaptType;
(function (AdaptType) {
    AdaptType[AdaptType["NONE"] = 0] = "NONE";
    AdaptType[AdaptType["WIDTH"] = 1] = "WIDTH";
    AdaptType[AdaptType["HEIGHT"] = 2] = "HEIGHT";
})(AdaptType || (AdaptType = {}));
/**
 * 适配节点大小
 */
var AdaptNodeSize = /** @class */ (function (_super) {
    __extends(AdaptNodeSize, _super);
    function AdaptNodeSize() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.target = null;
        _this.adaptType = AdaptType.NONE;
        _this.addOffset = cc.v2();
        _this.minSize = cc.v2(0, 0);
        _this.isUpdateWidget = false;
        _this.preScaleX = -1;
        _this.preScaleY = -1;
        _this.preWidth = -1;
        _this.preHeight = -1;
        return _this;
    }
    AdaptNodeSize.prototype.update = function () {
        if (!this.target) {
            return;
        }
        var sx = this.target.scaleX, sy = this.target.scaleY;
        var tw = this.target.width, th = this.target.height;
        if (this.preScaleX !== sx || this.preScaleY !== sy || this.preWidth !== tw || this.preHeight !== th) {
            this.preScaleX = sx;
            this.preScaleY = sx;
            this.preWidth = tw;
            this.preHeight = th;
            var width = this.node.width, height = this.node.height;
            if (this.adaptType === AdaptType.NONE) {
                width = tw * sx + this.addOffset.x;
                height = th * sy + this.addOffset.y;
            }
            else if (this.adaptType === AdaptType.WIDTH) {
                width = tw * sx + this.addOffset.x;
            }
            else if (this.adaptType === AdaptType.HEIGHT) {
                height = th * sy + this.addOffset.y;
            }
            if (this.minSize.x > 0) {
                width = Math.max(this.minSize.x, width);
            }
            if (this.minSize.y > 0) {
                height = Math.max(this.minSize.y, height);
            }
            this.node.width = width;
            this.node.height = height;
            if (this.isUpdateWidget) {
                this.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            }
        }
    };
    __decorate([
        property(cc.Node)
    ], AdaptNodeSize.prototype, "target", void 0);
    __decorate([
        property({ type: cc.Enum(AdaptType) })
    ], AdaptNodeSize.prototype, "adaptType", void 0);
    __decorate([
        property(cc.Vec2)
    ], AdaptNodeSize.prototype, "addOffset", void 0);
    __decorate([
        property(cc.Vec2)
    ], AdaptNodeSize.prototype, "minSize", void 0);
    __decorate([
        property()
    ], AdaptNodeSize.prototype, "isUpdateWidget", void 0);
    AdaptNodeSize = __decorate([
        ccclass,
        menu('自定义组件/AdaptNodeSize')
    ], AdaptNodeSize);
    return AdaptNodeSize;
}(cc.Component));
exports.default = AdaptNodeSize;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxjb21wb25lbnRcXEFkYXB0Tm9kZVNpemUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUE4QixFQUFFLENBQUMsVUFBVSxFQUF6QyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQUEsRUFBRSxJQUFJLFVBQWtCLENBQUM7QUFFbEQsSUFBSyxTQUlKO0FBSkQsV0FBSyxTQUFTO0lBQ1YseUNBQUksQ0FBQTtJQUNKLDJDQUFLLENBQUE7SUFDTCw2Q0FBTSxDQUFBO0FBQ1YsQ0FBQyxFQUpJLFNBQVMsS0FBVCxTQUFTLFFBSWI7QUFFRDs7R0FFRztBQUdIO0lBQTJDLGlDQUFZO0lBQXZEO1FBQUEscUVBbURDO1FBaERXLFlBQU0sR0FBWSxJQUFJLENBQUE7UUFFdEIsZUFBUyxHQUFjLFNBQVMsQ0FBQyxJQUFJLENBQUE7UUFFckMsZUFBUyxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUU1QixhQUFPLEdBQVksRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7UUFFOUIsb0JBQWMsR0FBWSxLQUFLLENBQUE7UUFFL0IsZUFBUyxHQUFXLENBQUMsQ0FBQyxDQUFBO1FBQ3RCLGVBQVMsR0FBVyxDQUFDLENBQUMsQ0FBQTtRQUN0QixjQUFRLEdBQVcsQ0FBQyxDQUFDLENBQUE7UUFDckIsZUFBUyxHQUFXLENBQUMsQ0FBQyxDQUFBOztJQW1DbEMsQ0FBQztJQWpDRyw4QkFBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDZCxPQUFNO1NBQ1Q7UUFDRCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxFQUFFLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUE7UUFDdEQsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsRUFBRSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFBO1FBQ3JELElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxFQUFFLElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxFQUFFLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxFQUFFLElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxFQUFFLEVBQUU7WUFDakcsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUE7WUFDbkIsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUE7WUFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUE7WUFDbEIsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUE7WUFDbkIsSUFBSSxLQUFLLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFBO1lBQ3RELElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxTQUFTLENBQUMsSUFBSSxFQUFFO2dCQUNuQyxLQUFLLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQTtnQkFDbEMsTUFBTSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUE7YUFDdEM7aUJBQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxLQUFLLFNBQVMsQ0FBQyxLQUFLLEVBQUU7Z0JBQzNDLEtBQUssR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFBO2FBQ3JDO2lCQUFNLElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSyxTQUFTLENBQUMsTUFBTSxFQUFFO2dCQUM1QyxNQUFNLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQTthQUN0QztZQUNELElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO2dCQUNwQixLQUFLLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQTthQUMxQztZQUNELElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO2dCQUNwQixNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQTthQUM1QztZQUNELElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQTtZQUN2QixJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7WUFDekIsSUFBSSxJQUFJLENBQUMsY0FBYyxFQUFFO2dCQUNyQixJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLHlCQUFJLENBQUMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQywwQ0FBRSxlQUFlLEtBQUUsQ0FBQyxDQUFBO2FBQzdFO1NBQ0o7SUFDTCxDQUFDO0lBL0NEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7aURBQ1k7SUFFOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxJQUFJLEVBQUUsRUFBRSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDO29EQUNNO0lBRTdDO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7b0RBQ2tCO0lBRXBDO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7a0RBQ29CO0lBRXRDO1FBREMsUUFBUSxFQUFFO3lEQUM0QjtJQVh0QixhQUFhO1FBRmpDLE9BQU87UUFDUCxJQUFJLENBQUMscUJBQXFCLENBQUM7T0FDUCxhQUFhLENBbURqQztJQUFELG9CQUFDO0NBbkRELEFBbURDLENBbkQwQyxFQUFFLENBQUMsU0FBUyxHQW1EdEQ7a0JBbkRvQixhQUFhIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSwgbWVudSB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbmVudW0gQWRhcHRUeXBlIHtcclxuICAgIE5PTkUsXHJcbiAgICBXSURUSCwgLy/lj6rpgILphY3lrr3luqZcclxuICAgIEhFSUdIVCwgLy/lj6rpgILphY3pq5jluqZcclxufVxyXG5cclxuLyoqXHJcbiAqIOmAgumFjeiKgueCueWkp+Wwj1xyXG4gKi9cclxuQGNjY2xhc3NcclxuQG1lbnUoJ+iHquWumuS5iee7hOS7ti9BZGFwdE5vZGVTaXplJylcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQWRhcHROb2RlU2l6ZSBleHRlbmRzIGNjLkNvbXBvbmVudCB7XHJcblxyXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXHJcbiAgICBwcml2YXRlIHRhcmdldDogY2MuTm9kZSA9IG51bGxcclxuICAgIEBwcm9wZXJ0eSh7IHR5cGU6IGNjLkVudW0oQWRhcHRUeXBlKSB9KVxyXG4gICAgcHJpdmF0ZSBhZGFwdFR5cGU6IEFkYXB0VHlwZSA9IEFkYXB0VHlwZS5OT05FXHJcbiAgICBAcHJvcGVydHkoY2MuVmVjMilcclxuICAgIHByaXZhdGUgYWRkT2Zmc2V0OiBjYy5WZWMyID0gY2MudjIoKVxyXG4gICAgQHByb3BlcnR5KGNjLlZlYzIpXHJcbiAgICBwcml2YXRlIG1pblNpemU6IGNjLlZlYzIgPSBjYy52MigwLCAwKVxyXG4gICAgQHByb3BlcnR5KClcclxuICAgIHByaXZhdGUgaXNVcGRhdGVXaWRnZXQ6IGJvb2xlYW4gPSBmYWxzZVxyXG5cclxuICAgIHByaXZhdGUgcHJlU2NhbGVYOiBudW1iZXIgPSAtMVxyXG4gICAgcHJpdmF0ZSBwcmVTY2FsZVk6IG51bWJlciA9IC0xXHJcbiAgICBwcml2YXRlIHByZVdpZHRoOiBudW1iZXIgPSAtMVxyXG4gICAgcHJpdmF0ZSBwcmVIZWlnaHQ6IG51bWJlciA9IC0xXHJcblxyXG4gICAgdXBkYXRlKCkge1xyXG4gICAgICAgIGlmICghdGhpcy50YXJnZXQpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IHN4ID0gdGhpcy50YXJnZXQuc2NhbGVYLCBzeSA9IHRoaXMudGFyZ2V0LnNjYWxlWVxyXG4gICAgICAgIGNvbnN0IHR3ID0gdGhpcy50YXJnZXQud2lkdGgsIHRoID0gdGhpcy50YXJnZXQuaGVpZ2h0XHJcbiAgICAgICAgaWYgKHRoaXMucHJlU2NhbGVYICE9PSBzeCB8fCB0aGlzLnByZVNjYWxlWSAhPT0gc3kgfHwgdGhpcy5wcmVXaWR0aCAhPT0gdHcgfHwgdGhpcy5wcmVIZWlnaHQgIT09IHRoKSB7XHJcbiAgICAgICAgICAgIHRoaXMucHJlU2NhbGVYID0gc3hcclxuICAgICAgICAgICAgdGhpcy5wcmVTY2FsZVkgPSBzeFxyXG4gICAgICAgICAgICB0aGlzLnByZVdpZHRoID0gdHdcclxuICAgICAgICAgICAgdGhpcy5wcmVIZWlnaHQgPSB0aFxyXG4gICAgICAgICAgICBsZXQgd2lkdGggPSB0aGlzLm5vZGUud2lkdGgsIGhlaWdodCA9IHRoaXMubm9kZS5oZWlnaHRcclxuICAgICAgICAgICAgaWYgKHRoaXMuYWRhcHRUeXBlID09PSBBZGFwdFR5cGUuTk9ORSkge1xyXG4gICAgICAgICAgICAgICAgd2lkdGggPSB0dyAqIHN4ICsgdGhpcy5hZGRPZmZzZXQueFxyXG4gICAgICAgICAgICAgICAgaGVpZ2h0ID0gdGggKiBzeSArIHRoaXMuYWRkT2Zmc2V0LnlcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmFkYXB0VHlwZSA9PT0gQWRhcHRUeXBlLldJRFRIKSB7XHJcbiAgICAgICAgICAgICAgICB3aWR0aCA9IHR3ICogc3ggKyB0aGlzLmFkZE9mZnNldC54XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5hZGFwdFR5cGUgPT09IEFkYXB0VHlwZS5IRUlHSFQpIHtcclxuICAgICAgICAgICAgICAgIGhlaWdodCA9IHRoICogc3kgKyB0aGlzLmFkZE9mZnNldC55XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHRoaXMubWluU2l6ZS54ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgd2lkdGggPSBNYXRoLm1heCh0aGlzLm1pblNpemUueCwgd2lkdGgpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHRoaXMubWluU2l6ZS55ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgaGVpZ2h0ID0gTWF0aC5tYXgodGhpcy5taW5TaXplLnksIGhlaWdodClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aGlzLm5vZGUud2lkdGggPSB3aWR0aFxyXG4gICAgICAgICAgICB0aGlzLm5vZGUuaGVpZ2h0ID0gaGVpZ2h0XHJcbiAgICAgICAgICAgIGlmICh0aGlzLmlzVXBkYXRlV2lkZ2V0KSB7XHJcbiAgICAgICAgICAgICAgICB0aGlzLm5vZGUuY2hpbGRyZW4uZm9yRWFjaChtID0+IG0uQ29tcG9uZW50KGNjLldpZGdldCk/LnVwZGF0ZUFsaWdubWVudCgpKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il19