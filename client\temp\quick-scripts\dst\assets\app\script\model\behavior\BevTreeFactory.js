
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BevTreeFactory.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd0ed9ESQzVCAb5l/OxhSrKt', 'BevTreeFactory');
// app/script/model/behavior/BevTreeFactory.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bevTreeFactory = void 0;
var Attack_1 = require("./Attack");
var EndRound_1 = require("./EndRound");
var Move_1 = require("./Move");
var SearchTarget_1 = require("./SearchTarget");
var Parallel_1 = require("./Parallel");
var Priority_1 = require("./Priority");
var Sequence_1 = require("./Sequence");
var CanMove_1 = require("./CanMove");
var HasAttackTarget_1 = require("./HasAttackTarget");
var InAttackRange_1 = require("./InAttackRange");
var Probability_1 = require("./Probability");
var SearchCanAttackTarget_1 = require("./SearchCanAttackTarget");
var CheckBeginBlood_1 = require("./CheckBeginBlood");
var CheckUseSkillAttack_1 = require("./CheckUseSkillAttack");
var CheckBeginDeductHp_1 = require("./CheckBeginDeductHp");
var CheckRoundBegin_1 = require("./CheckRoundBegin");
var BevTreeFactory = /** @class */ (function () {
    function BevTreeFactory() {
        this.classMap = {};
        this.classMap['Attack'] = Attack_1.default;
        this.classMap['EndRound'] = EndRound_1.default;
        this.classMap['Move'] = Move_1.default;
        this.classMap['SearchTarget'] = SearchTarget_1.default;
        this.classMap['Parallel'] = Parallel_1.default;
        this.classMap['Priority'] = Priority_1.default;
        this.classMap['Sequence'] = Sequence_1.default;
        this.classMap['CanMove'] = CanMove_1.default;
        this.classMap['HasAttackTarget'] = HasAttackTarget_1.default;
        this.classMap['InAttackRange'] = InAttackRange_1.default;
        this.classMap['Probability'] = Probability_1.default;
        this.classMap['SearchCanAttackTarget'] = SearchCanAttackTarget_1.default;
        this.classMap['CheckBeginBlood'] = CheckBeginBlood_1.default;
        this.classMap['CheckBeginDeductHp'] = CheckBeginDeductHp_1.default;
        this.classMap['CheckRoundBegin'] = CheckRoundBegin_1.default;
        this.classMap['CheckUseSkillAttack'] = CheckUseSkillAttack_1.default;
    }
    BevTreeFactory.prototype.newNode = function (type) {
        var cls = this.classMap[type];
        return cls ? new cls() : null;
    };
    return BevTreeFactory;
}());
exports.bevTreeFactory = new BevTreeFactory();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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