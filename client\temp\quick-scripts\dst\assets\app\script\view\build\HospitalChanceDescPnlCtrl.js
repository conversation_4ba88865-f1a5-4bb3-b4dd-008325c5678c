
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/HospitalChanceDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8fcd8/YhahLnr5qtJQwXc9e', 'HospitalChanceDescPnlCtrl');
// app/script/view/build/HospitalChanceDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var HospitalChanceDescPnlCtrl = /** @class */ (function (_super) {
    __extends(HospitalChanceDescPnlCtrl, _super);
    function HospitalChanceDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.player = null;
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    HospitalChanceDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    HospitalChanceDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    HospitalChanceDescPnlCtrl.prototype.onEnter = function (data) {
        var _this = this;
        this.contentNode_.Swih('');
        this.loadingNode_.active = true;
        this.player.reqDeadPawnLvMap().then(function (map) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            _this.contentNode_.Swih('', true);
            var keys = Object.keys(Constant_1.GO_HOSPITAL_CHANCE);
            _this.contentNode_.Child('chance').Items(keys, function (it, key) {
                var lv = Number(key);
                it.Child('lv').setLocaleKey('ui.short_lv', lv);
                var deadCount = map[lv] || 0, minChance = GameHelper_1.gameHpr.isNoviceMode ? NoviceConfig_1.NOVICE_GO_HOSPITAL_CHANCE[lv] : Constant_1.GO_HOSPITAL_CHANCE[lv];
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    it.Child('0', cc.Label).string = Math.max(100 - deadCount * NoviceConfig_1.NOVICE_INJURY_LV_REDUCE_RATE[lv], minChance) + '%';
                }
                else {
                    it.Child('0', cc.Label).string = Math.max(100 - deadCount * 5, minChance) + '%';
                }
                it.Child('1', cc.Label).string = minChance + '%';
            });
        });
    };
    HospitalChanceDescPnlCtrl.prototype.onRemove = function () {
    };
    HospitalChanceDescPnlCtrl.prototype.onClean = function () {
    };
    HospitalChanceDescPnlCtrl = __decorate([
        ccclass
    ], HospitalChanceDescPnlCtrl);
    return HospitalChanceDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = HospitalChanceDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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