
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CellTondenInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ab9855d3y9M8bMMZv0aRbgs', 'CellTondenInfoPnlCtrl');
// app/script/view/main/CellTondenInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var CellTondenInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CellTondenInfoPnlCtrl, _super);
    function CellTondenInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.treasureNode_ = null; // path://root/treasure_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    CellTondenInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CellTondenInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CellTondenInfoPnlCtrl.prototype.onEnter = function (json, landType) {
        var _this = this;
        var player = GameHelper_1.gameHpr.player;
        var needStamina = (json.need_stamina || 0) * Constant_1.TONDEN_STAMINA_MUL;
        var curStamina = player.getStamina();
        var add = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CELL_TONDEN_TREASURE);
        var counts = ut.stringToNumbers(json.treasures_count, ',').map(function (m) { return m + add; });
        var treasuresCountStr = add ? "<color=#4AB32E>" + counts.join('~') + "</c><color=#B6A591>(+" + add + ")</c>" : counts.join('~');
        if (GameHelper_1.gameHpr.isFreeServer()) {
            this.treasureNode_.Child('count').setLocaleKey('ui.tonden_end_treasure_count_free', treasuresCountStr);
        }
        else {
            this.treasureNode_.Child('count').setLocaleKey('ui.tonden_end_treasure_count', "<img src='stamina' /><color=" + (curStamina < needStamina ? '#D7634D' : '#3F332F') + ">" + needStamina + "</c>", treasuresCountStr);
        }
        var treasures = [];
        ut.stringToNumbers(json === null || json === void 0 ? void 0 : json.treasures_lv, ',').forEach(function (m, i) { return m > 0 && treasures.push({ lv: i + 1, val: m }); });
        var sum = treasures.reduce(function (val, cur) { return val + cur.val; }, 0) || 1;
        this.treasureNode_.Child('items').Items(treasures, function (it, data) {
            ResHelper_1.resHelper.loadIcon('icon/treasure_' + data.lv + '_0', it.Child('icon'), _this.key);
            it.Child('desc').setLocaleKey('ui.treasure_name_' + data.lv);
            it.Child('val', cc.Label).string = Math.round(data.val / sum * 100) + '%';
        });
    };
    CellTondenInfoPnlCtrl.prototype.onRemove = function () {
    };
    CellTondenInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    CellTondenInfoPnlCtrl = __decorate([
        ccclass
    ], CellTondenInfoPnlCtrl);
    return CellTondenInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CellTondenInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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