
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BaseCondition.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f886avVOepLsqoM8d0fXyt6', 'BaseCondition');
// app/script/model/behavior/BaseCondition.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseNode_1 = require("./BaseNode");
var BTConstant_1 = require("./BTConstant");
// 条件节点
var BaseCondition = /** @class */ (function (_super) {
    __extends(BaseCondition, _super);
    function BaseCondition() {
        var _this = _super.call(this) || this;
        _this.type = BTConstant_1.BTType.CONDITION;
        return _this;
    }
    return BaseCondition;
}(BaseNode_1.default));
exports.default = BaseCondition;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQmFzZUNvbmRpdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1Q0FBa0M7QUFDbEMsMkNBQXNDO0FBRXRDLE9BQU87QUFDUDtJQUEyQyxpQ0FBUTtJQUUvQztRQUFBLFlBQ0ksaUJBQU8sU0FFVjtRQURHLEtBQUksQ0FBQyxJQUFJLEdBQUcsbUJBQU0sQ0FBQyxTQUFTLENBQUE7O0lBQ2hDLENBQUM7SUFDTCxvQkFBQztBQUFELENBTkEsQUFNQyxDQU4wQyxrQkFBUSxHQU1sRCIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCYXNlTm9kZSBmcm9tIFwiLi9CYXNlTm9kZVwiO1xyXG5pbXBvcnQgeyBCVFR5cGUgfSBmcm9tIFwiLi9CVENvbnN0YW50XCI7XHJcblxyXG4vLyDmnaHku7boioLngrlcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQmFzZUNvbmRpdGlvbiBleHRlbmRzIEJhc2VOb2RlIHtcclxuXHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlcigpXHJcbiAgICAgICAgdGhpcy50eXBlID0gQlRUeXBlLkNPTkRJVElPTlxyXG4gICAgfVxyXG59Il19