
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/AlliPolicySelectPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef013Vb6dBJLbXAb7gPzsCU', 'AlliPolicySelectPnlCtrl');
// app/script/view/build/AlliPolicySelectPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AlliPolicySelectPnlCtrl = /** @class */ (function (_super) {
    __extends(AlliPolicySelectPnlCtrl, _super);
    function AlliPolicySelectPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.itemsSv_ = null; // path://root/select/items_sv
        _this.infoNode_ = null; // path://root/info_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.index = 0;
        _this.selectIds = [];
        _this.selectId = 0;
        return _this;
    }
    AlliPolicySelectPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AlliPolicySelectPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AlliPolicySelectPnlCtrl.prototype.onEnter = function (ids, index) {
        this.selectIds = ids;
        this.index = index;
        this.buttonsNode_.Swih(GameHelper_1.gameHpr.alliance.isCanSendMail() ? 'ok_be' : 'wait');
        this.updateSelectList(ids);
    };
    AlliPolicySelectPnlCtrl.prototype.onRemove = function () {
    };
    AlliPolicySelectPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/select/items_sv/view/content/item_be
    AlliPolicySelectPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        if (id) {
            this.updateSelect(id);
        }
    };
    // path://root/info_n/lock_evaluate_be
    AlliPolicySelectPnlCtrl.prototype.onClickLockEvaluate = function (event, data) {
        if (this.selectId) {
            ViewHelper_1.viewHelper.showPnl('menu/BookRating', Enums_1.BookCommentType.POLICY, this.selectId);
        }
    };
    // path://root/buttons_n/ok_be
    AlliPolicySelectPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var id = this.selectId;
        if (!id || !this.selectIds.has(id)) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_ceir_item', { params: ['ui.ceri_type_name_' + Enums_1.StudyType.POLICY] });
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.select_alli_policy_tip', {
            params: ['policyText.name_' + id],
            ok: function () { return _this.isValid && _this.do(id); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新选择列表
    AlliPolicySelectPnlCtrl.prototype.updateSelectList = function (selectIds) {
        var _this = this;
        this.itemsSv_.Items(selectIds, function (it, id) {
            it.Data = id;
            ResHelper_1.resHelper.loadPolicyIcon(id, it.Child('val', cc.Sprite), _this.key);
            it.Child('select').active = false;
        });
        this.updateSelect(0);
    };
    // 刷新选择
    AlliPolicySelectPnlCtrl.prototype.updateSelect = function (id) {
        var _a;
        this.selectId = id;
        this.itemsSv_.content.children.forEach(function (m) {
            var select = m.Child('select').active = m.Data === id;
            m.Component(cc.Button).interactable = !select;
        });
        // 显示信息
        var type = Enums_1.StudyType.POLICY;
        var node = this.infoNode_.Swih(id ? type : 0, false, 'lock_evaluate_be')[0];
        if (!id) {
            node.setLocaleKey('toast.please_select_ceir_item', 'ui.ceri_type_name_' + type);
        }
        else {
            var json = assetsMgr.getJsonData('policy', id);
            node.Child('name').setLocaleKey('policyText.name_' + json.id);
            node.Child('desc').setLocaleKey('policyText.desc_' + json.id, json.value.split(',')[0])._forceUpdateRenderData();
        }
        // 显示评价
        var evaluate = this.infoNode_.Child('lock_evaluate_be');
        if (evaluate.active = !!id) {
            var starInfo = GameHelper_1.gameHpr.book.getBookStarInfoByStudy(type, id);
            ViewHelper_1.viewHelper.updateBookStar(evaluate.Child('lay/rating'), starInfo.star);
            evaluate.Child('lay/val', cc.Label).string = '(' + starInfo.commentCount + ')';
        }
        // 自适应高度
        (_a = node.Component(cc.Layout)) === null || _a === void 0 ? void 0 : _a.updateLayout();
        var height = Math.max(node.height + 60, 160);
        if (height !== this.infoNode_.height) {
            this.infoNode_.height = height;
            this.infoNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        }
    };
    // 选择政策
    AlliPolicySelectPnlCtrl.prototype.do = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_AlliSelectPolicy', { index: this.index, id: id })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.hide();
                        return [2 /*return*/];
                }
            });
        });
    };
    AlliPolicySelectPnlCtrl = __decorate([
        ccclass
    ], AlliPolicySelectPnlCtrl);
    return AlliPolicySelectPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliPolicySelectPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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