
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/LockEquipEffectPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '13cd4/tIjlE25eLdHD5rFn8', 'LockEquipEffectPnlCtrl');
// app/script/view/build/LockEquipEffectPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var LockEquipEffectPnlCtrl = /** @class */ (function (_super) {
    __extends(LockEquipEffectPnlCtrl, _super);
    function LockEquipEffectPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descNode_ = null; // path://root/desc_n
        _this.effectNode_ = null; // path://root/effect_n
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    LockEquipEffectPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LockEquipEffectPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    LockEquipEffectPnlCtrl.prototype.onEnter = function (data, lockType, cb) {
        this.cb = cb;
        var count = 1;
        var color = GameHelper_1.gameHpr.player.getFixator() < count ? '#D7634D' : '#756963';
        this.descNode_.Child('val').setLocaleKey('ui.lock_equip_effect_desc', "<color=" + color + ">" + count + "</c>");
        var effects = data.effects, smeltEffectMap = null;
        if (data.smeltEffects.length > 0) {
            smeltEffectMap = {};
            data.smeltEffects.forEach(function (m) { return smeltEffectMap[m.type] = m.id; });
            effects = effects.slice().sort(function (a, b) {
                var aw = smeltEffectMap[a.type] ? 1 : 0, bw = smeltEffectMap[b.type] ? 1 : 0;
                return aw - bw;
            });
        }
        this.effectNode_.Items(effects, function (it, effect) {
            it.Data = effect.type;
            var lock = !!(smeltEffectMap === null || smeltEffectMap === void 0 ? void 0 : smeltEffectMap[effect.type]);
            it.Child('root').Swih(lock ? 'lock' : 'can');
            var toggle = it.Component(cc.Toggle);
            toggle.isChecked = !lock && lockType === effect.type;
            toggle.interactable = !lock;
            var lbl = it.Child('val', cc.RichText);
            lbl.node.opacity = lock ? 150 : 255;
            lbl.setLocaleKey.apply(lbl, __spread([effect.name], effect.getDescParams()));
            it.height = Math.max(lbl.node.height, 40);
        });
    };
    LockEquipEffectPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    LockEquipEffectPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    LockEquipEffectPnlCtrl.prototype.onClickOk = function (event, data) {
        var _a;
        var it = this.effectNode_.children.find(function (m) { return m.Component(cc.Toggle).isChecked; });
        if (it && GameHelper_1.gameHpr.player.getFixator() < 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.res_deficiency', { params: [Constant_1.CTYPE_NAME[Enums_1.CType.FIXATOR]] });
        }
        this.cb && this.cb((_a = it === null || it === void 0 ? void 0 : it.Data) !== null && _a !== void 0 ? _a : 0);
        this.hide();
    };
    LockEquipEffectPnlCtrl = __decorate([
        ccclass
    ], LockEquipEffectPnlCtrl);
    return LockEquipEffectPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LockEquipEffectPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxMb2NrRXF1aXBFZmZlY3RQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQTREO0FBQzVELHFEQUFvRDtBQUNwRCw2REFBeUQ7QUFDekQsNkRBQTREO0FBR3BELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQW9ELDBDQUFjO0lBQWxFO1FBQUEscUVBb0VDO1FBbEVHLDBCQUEwQjtRQUNsQixlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMscUJBQXFCO1FBQy9DLGlCQUFXLEdBQVksSUFBSSxDQUFBLENBQUMsdUJBQXVCO1FBQzNELE1BQU07UUFFRSxRQUFFLEdBQWEsSUFBSSxDQUFBOztRQXdEM0IsTUFBTTtRQUNOLGlIQUFpSDtRQUVqSCxpSEFBaUg7SUFFckgsQ0FBQztJQTNEVSxnREFBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLHlDQUFRLEdBQXJCOzs7Ozs7S0FDQztJQUVNLHdDQUFPLEdBQWQsVUFBZSxJQUFlLEVBQUUsUUFBZ0IsRUFBRSxFQUFZO1FBQzFELElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFBO1FBQ1osSUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFBO1FBQ2YsSUFBTSxLQUFLLEdBQUcsb0JBQU8sQ0FBQyxNQUFNLENBQUMsVUFBVSxFQUFFLEdBQUcsS0FBSyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQTtRQUN6RSxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsMkJBQTJCLEVBQUUsWUFBVSxLQUFLLFNBQUksS0FBSyxTQUFNLENBQUMsQ0FBQTtRQUNyRyxJQUFJLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxFQUFFLGNBQWMsR0FBOEIsSUFBSSxDQUFBO1FBQzVFLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQzlCLGNBQWMsR0FBRyxFQUFFLENBQUE7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxjQUFjLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEVBQTdCLENBQTZCLENBQUMsQ0FBQTtZQUM3RCxPQUFPLEdBQUcsT0FBTyxDQUFDLEtBQUssRUFBRSxDQUFDLElBQUksQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDO2dCQUNoQyxJQUFJLEVBQUUsR0FBRyxjQUFjLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxFQUFFLEdBQUcsY0FBYyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQzVFLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQTtZQUNsQixDQUFDLENBQUMsQ0FBQTtTQUNMO1FBQ0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLFVBQUMsRUFBRSxFQUFFLE1BQU07WUFDdkMsRUFBRSxDQUFDLElBQUksR0FBRyxNQUFNLENBQUMsSUFBSSxDQUFBO1lBQ3JCLElBQU0sSUFBSSxHQUFHLENBQUMsRUFBQyxjQUFjLGFBQWQsY0FBYyx1QkFBZCxjQUFjLENBQUcsTUFBTSxDQUFDLElBQUksRUFBQyxDQUFBO1lBQzVDLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUM1QyxJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQTtZQUN0QyxNQUFNLENBQUMsU0FBUyxHQUFHLENBQUMsSUFBSSxJQUFJLFFBQVEsS0FBSyxNQUFNLENBQUMsSUFBSSxDQUFBO1lBQ3BELE1BQU0sQ0FBQyxZQUFZLEdBQUcsQ0FBQyxJQUFJLENBQUE7WUFDM0IsSUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1lBQ3hDLEdBQUcsQ0FBQyxJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUE7WUFDbkMsR0FBRyxDQUFDLFlBQVksT0FBaEIsR0FBRyxZQUFjLE1BQU0sQ0FBQyxJQUFJLEdBQUssTUFBTSxDQUFDLGFBQWEsRUFBRSxHQUFDO1lBQ3hELEVBQUUsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQTtRQUM3QyxDQUFDLENBQUMsQ0FBQTtJQUNOLENBQUM7SUFFTSx5Q0FBUSxHQUFmO1FBQ0ksSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLHdDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQixvQkFBb0I7SUFDcEIsMENBQVMsR0FBVCxVQUFVLEtBQTBCLEVBQUUsSUFBWTs7UUFDOUMsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsU0FBUyxFQUFoQyxDQUFnQyxDQUFDLENBQUE7UUFDaEYsSUFBSSxFQUFFLElBQUksb0JBQU8sQ0FBQyxNQUFNLENBQUMsVUFBVSxFQUFFLEdBQUcsQ0FBQyxFQUFFO1lBQ3ZDLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsc0JBQXNCLEVBQUUsRUFBRSxNQUFNLEVBQUUsQ0FBQyxxQkFBVSxDQUFDLGFBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQTtTQUMvRjtRQUNELElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsT0FBQyxFQUFFLGFBQUYsRUFBRSx1QkFBRixFQUFFLENBQUUsSUFBSSxtQ0FBSSxDQUFDLENBQUMsQ0FBQTtRQUNqQyxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7SUFDZixDQUFDO0lBOURnQixzQkFBc0I7UUFEMUMsT0FBTztPQUNhLHNCQUFzQixDQW9FMUM7SUFBRCw2QkFBQztDQXBFRCxBQW9FQyxDQXBFbUQsRUFBRSxDQUFDLFdBQVcsR0FvRWpFO2tCQXBFb0Isc0JBQXNCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ1RZUEVfTkFNRSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvQ29uc3RhbnRcIjtcbmltcG9ydCB7IENUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgRXF1aXBJbmZvIGZyb20gXCIuLi8uLi9tb2RlbC9tYWluL0VxdWlwSW5mb1wiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBMb2NrRXF1aXBFZmZlY3RQbmxDdHJsIGV4dGVuZHMgbWMuQmFzZVBubEN0cmwge1xuXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cbiAgICBwcml2YXRlIGRlc2NOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvZGVzY19uXG4gICAgcHJpdmF0ZSBlZmZlY3ROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvZWZmZWN0X25cbiAgICAvL0BlbmRcblxuICAgIHByaXZhdGUgY2I6IEZ1bmN0aW9uID0gbnVsbFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IEVxdWlwSW5mbywgbG9ja1R5cGU6IG51bWJlciwgY2I6IEZ1bmN0aW9uKSB7XG4gICAgICAgIHRoaXMuY2IgPSBjYlxuICAgICAgICBjb25zdCBjb3VudCA9IDFcbiAgICAgICAgY29uc3QgY29sb3IgPSBnYW1lSHByLnBsYXllci5nZXRGaXhhdG9yKCkgPCBjb3VudCA/ICcjRDc2MzREJyA6ICcjNzU2OTYzJ1xuICAgICAgICB0aGlzLmRlc2NOb2RlXy5DaGlsZCgndmFsJykuc2V0TG9jYWxlS2V5KCd1aS5sb2NrX2VxdWlwX2VmZmVjdF9kZXNjJywgYDxjb2xvcj0ke2NvbG9yfT4ke2NvdW50fTwvYz5gKVxuICAgICAgICBsZXQgZWZmZWN0cyA9IGRhdGEuZWZmZWN0cywgc21lbHRFZmZlY3RNYXA6IHsgW2tleTogbnVtYmVyXTogbnVtYmVyIH0gPSBudWxsXG4gICAgICAgIGlmIChkYXRhLnNtZWx0RWZmZWN0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBzbWVsdEVmZmVjdE1hcCA9IHt9XG4gICAgICAgICAgICBkYXRhLnNtZWx0RWZmZWN0cy5mb3JFYWNoKG0gPT4gc21lbHRFZmZlY3RNYXBbbS50eXBlXSA9IG0uaWQpXG4gICAgICAgICAgICBlZmZlY3RzID0gZWZmZWN0cy5zbGljZSgpLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgICAgICAgICBsZXQgYXcgPSBzbWVsdEVmZmVjdE1hcFthLnR5cGVdID8gMSA6IDAsIGJ3ID0gc21lbHRFZmZlY3RNYXBbYi50eXBlXSA/IDEgOiAwXG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3IC0gYndcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lZmZlY3ROb2RlXy5JdGVtcyhlZmZlY3RzLCAoaXQsIGVmZmVjdCkgPT4ge1xuICAgICAgICAgICAgaXQuRGF0YSA9IGVmZmVjdC50eXBlXG4gICAgICAgICAgICBjb25zdCBsb2NrID0gISFzbWVsdEVmZmVjdE1hcD8uW2VmZmVjdC50eXBlXVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ3Jvb3QnKS5Td2loKGxvY2sgPyAnbG9jaycgOiAnY2FuJylcbiAgICAgICAgICAgIGNvbnN0IHRvZ2dsZSA9IGl0LkNvbXBvbmVudChjYy5Ub2dnbGUpXG4gICAgICAgICAgICB0b2dnbGUuaXNDaGVja2VkID0gIWxvY2sgJiYgbG9ja1R5cGUgPT09IGVmZmVjdC50eXBlXG4gICAgICAgICAgICB0b2dnbGUuaW50ZXJhY3RhYmxlID0gIWxvY2tcbiAgICAgICAgICAgIGNvbnN0IGxibCA9IGl0LkNoaWxkKCd2YWwnLCBjYy5SaWNoVGV4dClcbiAgICAgICAgICAgIGxibC5ub2RlLm9wYWNpdHkgPSBsb2NrID8gMTUwIDogMjU1XG4gICAgICAgICAgICBsYmwuc2V0TG9jYWxlS2V5KGVmZmVjdC5uYW1lLCAuLi5lZmZlY3QuZ2V0RGVzY1BhcmFtcygpKVxuICAgICAgICAgICAgaXQuaGVpZ2h0ID0gTWF0aC5tYXgobGJsLm5vZGUuaGVpZ2h0LCA0MClcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgICAgIHRoaXMuY2IgPSBudWxsXG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L29rX2JlXG4gICAgb25DbGlja09rKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgaXQgPSB0aGlzLmVmZmVjdE5vZGVfLmNoaWxkcmVuLmZpbmQobSA9PiBtLkNvbXBvbmVudChjYy5Ub2dnbGUpLmlzQ2hlY2tlZClcbiAgICAgICAgaWYgKGl0ICYmIGdhbWVIcHIucGxheWVyLmdldEZpeGF0b3IoKSA8IDEpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QucmVzX2RlZmljaWVuY3knLCB7IHBhcmFtczogW0NUWVBFX05BTUVbQ1R5cGUuRklYQVRPUl1dIH0pXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jYiAmJiB0aGlzLmNiKGl0Py5EYXRhID8/IDApXG4gICAgICAgIHRoaXMuaGlkZSgpXG4gICAgfVxuICAgIC8vQGVuZFxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG59XG4iXX0=