
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/JsbHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f0959nt7kZD4ouaBQwexIDW', 'JsbHelper');
// app/script/common/helper/JsbHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jsbHelper = void 0;
var JsbEvent_1 = require("../event/JsbEvent");
var GameHelper_1 = require("./GameHelper");
/**
 * 原生
 */
var JsbHelper = /** @class */ (function () {
    function JsbHelper() {
        this.tempDeviceInfo = null;
    }
    /**
     *
     * @param event 和native约定的事件
     * @param jsonStr 参数字符串
     * @param callback
     * @param target
     * @description 异步调用方式，用于等待native异步回调，native会调用this.emit触发
     */
    JsbHelper.prototype.cast = function (event, callback, jsonObj, target) {
        this.send(event, JSON.stringify(jsonObj));
        if (callback) {
            eventCenter.once(event, callback, target);
        }
    };
    /**
     *
     * @param event
     * @param parameters
     * @description 同步调用方式，只是书写形式同步，不同于native同步返回，无需异步的调用还是用原生方法jsb.reflection.callStaticMethod
     */
    JsbHelper.prototype.call = function (event, jsonObj) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.send(event, JSON.stringify(jsonObj));
                return [2 /*return*/, eventCenter.wait(event)];
            });
        });
    };
    // 统一生成json字符串形式传给native
    JsbHelper.prototype.send = function (event, jsonStr) {
        if (!jsonStr || jsonStr === '') {
            jsonStr = '{}';
        }
        if (!ut.isMobile()) {
            return;
        }
        setTimeout(function () {
            if (ut.isAndroid()) {
                jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'request', '(Ljava/lang/String;Ljava/lang/String;)V', event, jsonStr);
            }
            else if (ut.isIos()) {
                jsb.reflection.callStaticMethod('jsbHelp', 'request:JsonStr:', event, jsonStr);
            }
        });
    };
    /**
     *
     * @param event
     * @param callback
     * @param target
     * @description 注册事件，用于native主动调用的场景
     */
    JsbHelper.prototype.on = function (event, callback, target) {
        eventCenter.on(event, callback, target);
    };
    JsbHelper.prototype.off = function (event, callback, target) {
        eventCenter.off(event, callback, target);
    };
    /**
     *
     * @param event
     * @param jsonStr
     * @description native回调时调用
     */
    JsbHelper.prototype.emit = function (event, json) {
        if (json) {
            for (var key in json) {
                if (json[key] === 'true') {
                    json[key] = true;
                }
                else if (json[key] === 'false') {
                    json[key] = false;
                }
            }
        }
        eventCenter.emit(event, json);
    };
    JsbHelper.prototype.getPackageSign = function () {
        return __awaiter(this, void 0, void 0, function () {
            var sign;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(ut.isAndroid() && GameHelper_1.gameHpr.isInland())) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.call(JsbEvent_1.default.GET_PACKAGE_SIGN)];
                    case 1:
                        sign = (_a.sent()).sign;
                        return [2 /*return*/, sign];
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    JsbHelper.prototype.getDeviceType = function () {
        if (ut.isAndroid()) {
            return '';
        }
        else {
            return jsb.reflection.callStaticMethod('jsbHelp', 'getDeviceName');
        }
    };
    JsbHelper.prototype.getDeepLinkParams = function () {
        var _a;
        var params = '';
        if (ut.isAndroid()) {
            var res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/AppsFlyerHelper', 'getShemeParams', '()Ljava/lang/String;');
            if (res && res !== 'null') {
                params = res;
            }
        }
        else if (ut.isIos()) {
            var res = jsb.reflection.callStaticMethod('AppsflyerHelper', 'getShemeParams');
            if (res && res !== 'null') {
                params = res;
            }
        }
        if (!params) {
            return null;
        }
        // https://nta-applinks.dhgames.com/index.html?openUI=menu_Portrayal&params=3100141
        if (params.startsWith('https://nta-applinks.dhgames.com') || params.startsWith('jwm-sheme1://')) {
            var _b = __read(params.split('?'), 2), _ = _b[0], str = _b[1];
            var data_1 = {};
            str.split('&').forEach(function (m) {
                var _a = __read(m.split('='), 2), k = _a[0], v = _a[1];
                data_1[k] = v;
            });
            return data_1;
        }
        var _c = __read(params.split('|'), 3), uid = _c[0], type = _c[1], date = _c[2];
        return { uid: uid, type: type, date: (_a = Number(date)) !== null && _a !== void 0 ? _a : undefined };
    };
    // 获取安装参数
    JsbHelper.prototype.getInstallParams = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/, null];
                        }
                        else if (GameHelper_1.gameHpr.isGLobal()) { //海外
                            return [2 /*return*/, this.getDeepLinkParams()];
                        }
                        return [4 /*yield*/, this.call(JsbEvent_1.default.GET_INSTALL_PARAMS)];
                    case 1:
                        res = _a.sent();
                        if (!res.error) {
                            return [2 /*return*/, res];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    // 获取启动参数
    JsbHelper.prototype.getAwakeParams = function () {
        if (!cc.sys.isNative) {
            return null;
        }
        else if (GameHelper_1.gameHpr.isGLobal()) {
            return this.getDeepLinkParams();
        }
        else if (ut.isAndroid()) {
            var res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/OpenInstallHelper', 'getAwakeParams', '()Ljava/lang/String;');
            var obj = JSON.parse(res || '{}');
            return !obj.error ? obj : null;
        }
        else if (ut.isIos()) {
            var res = jsb.reflection.callStaticMethod('OpenInstallHelper', 'getAwakeParams');
            if (res !== 'error') {
                try {
                    return JSON.parse(res);
                }
                catch (error) { }
            }
        }
        return null;
    };
    /**
     * @param key
     * @param content value
     * @param service ios必须传 类似key 在ios中 service就好像给宏取名字,表示存储的这个东西是做什么的.通过这两个key就可以指定唯一性 可以暂时当成另一个key
     * @description 存数据到设备手机上，并且不会因为应用被卸载而删除
     * @returns Bool
     */
    JsbHelper.prototype.saveDeviceData = function (key, content, service) {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!ut.isIos()) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.call(JsbEvent_1.default.SAVE_DEVICE_DATA, { key: key, content: content, service: service })];
                    case 1:
                        result = (_a.sent()).result;
                        return [2 /*return*/, !!result];
                    case 2: 
                    // android to do 
                    return [2 /*return*/, false];
                }
            });
        });
    };
    // 获取存储在设备手机上的数据 返回null为没有存储数据
    JsbHelper.prototype.getDeviceData = function (key, service) {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!ut.isIos()) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.call(JsbEvent_1.default.GET_DEVICE_DATA, { key: key, service: service })];
                    case 1:
                        result = (_a.sent()).result;
                        if (result === 'null')
                            return [2 /*return*/, ''];
                        return [2 /*return*/, result];
                    case 2: 
                    // android to do 
                    return [2 /*return*/, ''];
                }
            });
        });
    };
    // 删除存储在设备手机上的数据
    JsbHelper.prototype.delDeviceData = function (key, service) {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!ut.isIos()) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.call(JsbEvent_1.default.DEL_DEVICE_DATA, { key: key, service: service })];
                    case 1:
                        result = (_a.sent()).result;
                        return [2 /*return*/, !!result];
                    case 2: 
                    // android to do 
                    return [2 /*return*/, false];
                }
            });
        });
    };
    // 获取消息推送token
    JsbHelper.prototype.getFcmToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var token;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/, { err: true }];
                        }
                        return [4 /*yield*/, this.call(JsbEvent_1.default.GET_NOTICE_TOKEN)
                            // console.log('getFcmToken', token)
                        ];
                    case 1:
                        token = (_a.sent()).token;
                        // console.log('getFcmToken', token)
                        if (token === 'null') {
                            return [2 /*return*/, { err: true }];
                        }
                        return [2 /*return*/, { token: token }];
                }
            });
        });
    };
    // 跳转到手机的九万亩的app设置页面 这个没有返回结果 因为不太好获取跳转之后返回游戏的时机
    JsbHelper.prototype.openSelfSetting = function () {
        if (cc.sys.isNative) {
            this.cast(JsbEvent_1.default.OPEN_APP_SETTING);
        }
    };
    // 检测是否有通知的权限 返回true or false
    JsbHelper.prototype.checkNoticePer = function () {
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, this.call(JsbEvent_1.default.CHECK_NOTICE_PER)];
                    case 1:
                        result = (_a.sent()).result;
                        return [2 /*return*/, !!result];
                }
            });
        });
    };
    JsbHelper.prototype.getLangOrderList = function (eventName) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, error, result, nextIndex, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/, { result: [] }];
                        }
                        return [4 /*yield*/, this.call(eventName)];
                    case 1:
                        _a = _b.sent(), error = _a.error, result = _a.result, nextIndex = _a.nextIndex;
                        if (error)
                            return [2 /*return*/, { error: error, result: result }];
                        _b.label = 2;
                    case 2:
                        if (!nextIndex) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.call("GET_MORE_LIST_INDEX", { nextIndex: nextIndex })];
                    case 3:
                        info = _b.sent();
                        if (info.error)
                            return [3 /*break*/, 4];
                        nextIndex = info.nextIndex;
                        result = result.concat(info.result);
                        return [3 /*break*/, 2];
                    case 4:
                        console.log("result is ", result.length);
                        return [2 /*return*/, { error: error, result: result }];
                }
            });
        });
    };
    // 获取设备信息
    JsbHelper.prototype.getDeviceInfo = function (force) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/, {}];
                        }
                        else if (!force && this.tempDeviceInfo) {
                            return [2 /*return*/, this.tempDeviceInfo];
                        }
                        _a = this;
                        return [4 /*yield*/, this.call(JsbEvent_1.default.GET_DEVICE_INFO)];
                    case 1:
                        _a.tempDeviceInfo = _b.sent();
                        return [2 /*return*/, this.tempDeviceInfo];
                }
            });
        });
    };
    // 获取网络类型
    JsbHelper.prototype.getNetWorkType = function () {
        var type = 0;
        if (cc.sys.os === cc.sys.OS_IOS) {
            type = jsb.reflection.callStaticMethod('jsbHelp', 'getNetWorkType');
        }
        else if (ut.isAndroid()) {
            type = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'getNetWorkType', '()I');
        }
        return type; // NONE:0,LAN:1,WAN:2
    };
    // 获取网络访问权限
    JsbHelper.prototype.getNetworkAccessPermission = function () {
        return true;
        var access = true;
        if (cc.sys.os === cc.sys.OS_IOS) {
            access = jsb.reflection.callStaticMethod('jsbHelp', 'getNetworkAccessPermission');
        }
        else if (ut.isAndroid()) {
            access = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/JsbHelper', 'getNetworkAccessPermission', '()I');
        }
        return access;
    };
    // 消费订单
    JsbHelper.prototype.consumeOrder = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.call(JsbEvent_1.default.CONSUME_ORDER, data)];
                    case 1:
                        res = _a.sent();
                        if (!res.error) {
                            return [2 /*return*/];
                        }
                        else if ((res === null || res === void 0 ? void 0 : res.code) === '8' && ut.isAndroid()) {
                            return [2 /*return*/]; //code=8 表示服务器已经消费了
                        }
                        return [4 /*yield*/, ut.wait(1)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (true) return [3 /*break*/, 0];
                        _a.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    JsbHelper.prototype.getAppsflyerId = function () {
        if (cc.sys.os === cc.sys.OS_IOS) {
            return jsb.reflection.callStaticMethod('jsbHelp', 'getAppsflyerId');
        }
        else if (ut.isAndroid()) {
            return jsb.reflection.callStaticMethod('org/cocos2dx/javascript/DeviceHelper', 'getAppsflyerId', '()Ljava/lang/String;');
        }
        return null;
    };
    return JsbHelper;
}());
exports.jsbHelper = window['jsbHelper'] = new JsbHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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