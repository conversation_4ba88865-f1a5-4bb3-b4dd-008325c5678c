
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SkinExchangePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '370cbU1qoBOx4ET/Pb82HVE', 'SkinExchangePnlCtrl');
// app/script/view/common/SkinExchangePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SkinExchangePnlCtrl = /** @class */ (function (_super) {
    __extends(SkinExchangePnlCtrl, _super);
    function SkinExchangePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.skinsSv_ = null; // path://root/skin/skins_sv
        _this.exchangeNode_ = null; // path://root/exchange_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.user = null;
        _this.curSelectSkinId = 0;
        _this.curSelectSkinCount = 0;
        _this.needSkinCount = 0;
        return _this;
    }
    SkinExchangePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SkinExchangePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    SkinExchangePnlCtrl.prototype.onEnter = function (id) {
        this.curSelectSkinId = 0;
        this.curSelectSkinCount = 0;
        this.needSkinCount = 0;
        this.init(id);
    };
    SkinExchangePnlCtrl.prototype.onRemove = function () {
    };
    SkinExchangePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/skin/skins_sv/view/content/skin_be
    SkinExchangePnlCtrl.prototype.onClickSkin = function (event, data) {
        var _this = this;
        var _a, _b;
        var id = (_b = (_a = event.target) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.id;
        if (this.curSelectSkinId === id) {
            return;
        }
        this.curSelectSkinId = id;
        this.skinsSv_.content.children.forEach(function (m) { var _a; return _this.updateSelectSkin(m.Child('root'), ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.id) === id); });
        this.updateSkinInfo(true);
        this.updateButtons();
    };
    // path://root/buttons_n/exchange_be
    SkinExchangePnlCtrl.prototype.onClickExchange = function (event, data) {
        var _this = this;
        if (!this.curSelectSkinId) {
            return ViewHelper_1.viewHelper.showAlert('ui.please_select_skin');
        }
        ViewHelper_1.viewHelper.showPnl('common/SkinExchangeTip', this.curSelectSkinId, function (ok) {
            ok && _this.reqExchangeSkin();
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SkinExchangePnlCtrl.prototype.init = function (id) {
        var _this = this;
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test', skins = this.user.getUnlockPawnSkinIds(), list = assetsMgr.getJson('pawnSkin').get('cond', 5).map(function (m) {
            var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            var timeDiff = ut.timediff(Date.now(), startTime);
            if (startTime && timeDiff <= 0) { // 避免剧透
                return {
                    info: m,
                    time: timeDiff
                };
            }
        })
            .filter(function (m) { return !!m; })
            .sort(function (a, b) {
            var hasA = skins.has(a.info.id), hasB = skins.has(b.info.id);
            if (hasA || hasB) {
                return hasA ? 1 : -1;
            }
            else {
                return b.time - a.time;
            }
        });
        this.skinsSv_.Items(list, function (it, _data) {
            var data = it.Data = _data.info;
            var root = it.Child('root'), has = skins.has(data.id);
            it.opacity = has ? 150 : 255;
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, root.Child('icon'), _this.key);
            it.Child('val').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + data.pawn_id), 4));
            _this.updateSelectSkin(root, id ? id === data.id : false);
        });
        if (id) { // 跳转指定id
            this.curSelectSkinId = id;
            var idx_1 = list.findIndex(function (m) { return m.info.id === id; });
            ut.waitNextFrame().then(function () { return _this.isValid && _this.skinsSv_.SelectItemToCentre(idx_1); });
        }
        this.updateSkinInfo(!!id);
        this.updateButtons();
    };
    SkinExchangePnlCtrl.prototype.updateSelectSkin = function (it, select) {
        it.Child('select').active = select;
        it.Child('bg/select').active = select;
    };
    SkinExchangePnlCtrl.prototype.updateSkinInfo = function (val) {
        var _a;
        this.exchangeNode_.Child('empty').active = !val;
        this.exchangeNode_.Child('need_skin').active = val;
        if (val && this.curSelectSkinId) {
            var _b = __read(ut.stringToNumbers((_a = assetsMgr.getJsonData('pawnSkin', this.curSelectSkinId)) === null || _a === void 0 ? void 0 : _a.value, ','), 2), id_1 = _b[0], count = _b[1];
            this.needSkinCount = count;
            var root = this.exchangeNode_.Child('need_skin/root');
            ResHelper_1.resHelper.loadPawnHeadIcon(id_1, root.Child('icon'), this.key);
            this.curSelectSkinCount = this.user.getSkinItemList().filter(function (m) { return m.id === id_1 && m.state >= 0; }).length;
            root.parent.Child('count/cur', cc.Label).Color(this.curSelectSkinCount < count ? '#C83C04' : '#333333').string = this.curSelectSkinCount + '';
            root.parent.Child('count/need', cc.Label).string = count + '';
        }
    };
    SkinExchangePnlCtrl.prototype.updateButtons = function () {
        var skins = this.user.getUnlockPawnSkinIds();
        this.buttonsNode_.Swih(skins.has(this.curSelectSkinId) ? 'existed' : 'exchange_be');
    };
    SkinExchangePnlCtrl.prototype.reqExchangeSkin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.curSelectSkinCount < this.needSkinCount) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.skin_exchange_fail')];
                        }
                        if (this.user.getUnlockPawnSkinIds().has(this.curSelectSkinId)) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_ComposeSkinItems', { id: this.curSelectSkinId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        if (data) {
                            this.user.setSkinItemList(data.list);
                            this.user.setUnlockPawnSkinIds(data.unlockPawnSkinIds);
                            this.emit(EventType_1.default.UPDATE_EXCHANGE_SKINS);
                            this.emit(EventType_1.default.UPDATE_MYSTERYBOX);
                            ViewHelper_1.viewHelper.showAlert('toast.exchange_succ');
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SkinExchangePnlCtrl = __decorate([
        ccclass
    ], SkinExchangePnlCtrl);
    return SkinExchangePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SkinExchangePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2tpbkV4Y2hhbmdlUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMERBQXFEO0FBQ3JELDZEQUF5RDtBQUN6RCwyREFBMEQ7QUFDMUQsNkRBQTREO0FBR3BELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQWlELHVDQUFjO0lBQS9EO1FBQUEscUVBbUpDO1FBakpHLDBCQUEwQjtRQUNsQixjQUFRLEdBQWtCLElBQUksQ0FBQSxDQUFDLDRCQUE0QjtRQUMzRCxtQkFBYSxHQUFZLElBQUksQ0FBQSxDQUFDLHlCQUF5QjtRQUN2RCxrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLHdCQUF3QjtRQUM3RCxNQUFNO1FBRUUsVUFBSSxHQUFjLElBQUksQ0FBQTtRQUN0QixxQkFBZSxHQUFXLENBQUMsQ0FBQTtRQUMzQix3QkFBa0IsR0FBVyxDQUFDLENBQUE7UUFDOUIsbUJBQWEsR0FBVyxDQUFDLENBQUE7O0lBd0lyQyxDQUFDO0lBdElVLDZDQUFlLEdBQXRCO1FBQ0ksT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRVksc0NBQVEsR0FBckI7OztnQkFDSSxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUE7Ozs7S0FDcEM7SUFFTSxxQ0FBTyxHQUFkLFVBQWUsRUFBVTtRQUNyQixJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQTtRQUN4QixJQUFJLENBQUMsa0JBQWtCLEdBQUcsQ0FBQyxDQUFBO1FBQzNCLElBQUksQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFBO1FBQ3RCLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUE7SUFDakIsQ0FBQztJQUVNLHNDQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0scUNBQU8sR0FBZDtJQUNBLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLGlEQUFpRDtJQUNqRCx5Q0FBVyxHQUFYLFVBQVksS0FBMEIsRUFBRSxJQUFZO1FBQXBELGlCQVNDOztRQVJHLElBQU0sRUFBRSxlQUFHLEtBQUssQ0FBQyxNQUFNLDBDQUFFLElBQUksMENBQUUsRUFBRSxDQUFBO1FBQ2pDLElBQUksSUFBSSxDQUFDLGVBQWUsS0FBSyxFQUFFLEVBQUU7WUFDN0IsT0FBTTtTQUNUO1FBQ0QsSUFBSSxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUE7UUFDekIsSUFBSSxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsWUFBSSxPQUFBLEtBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLE9BQUEsQ0FBQyxDQUFDLElBQUksMENBQUUsRUFBRSxNQUFLLEVBQUUsQ0FBQyxDQUFBLEVBQUEsQ0FBQyxDQUFBO1FBQ3RHLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDekIsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO0lBQ3hCLENBQUM7SUFFRCxvQ0FBb0M7SUFDcEMsNkNBQWUsR0FBZixVQUFnQixLQUEwQixFQUFFLElBQVk7UUFBeEQsaUJBT0M7UUFORyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN2QixPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLHVCQUF1QixDQUFDLENBQUE7U0FDdkQ7UUFDRCx1QkFBVSxDQUFDLE9BQU8sQ0FBQyx3QkFBd0IsRUFBRSxJQUFJLENBQUMsZUFBZSxFQUFFLFVBQUMsRUFBRTtZQUNsRSxFQUFFLElBQUksS0FBSSxDQUFDLGVBQWUsRUFBRSxDQUFBO1FBQ2hDLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFakgsaUhBQWlIO0lBRXpHLGtDQUFJLEdBQVosVUFBYSxFQUFXO1FBQXhCLGlCQXFDQztRQXBDRyxJQUFNLFVBQVUsR0FBRyxvQkFBTyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsb0JBQU8sQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxFQUNuRSxLQUFLLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsRUFBRSxFQUN4QyxJQUFJLEdBQUcsU0FBUyxDQUFDLE9BQU8sQ0FBQyxVQUFVLENBQUMsQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxVQUFBLENBQUM7WUFDL0MsSUFBQSxLQUFBLE9BQXVCLENBQUMsQ0FBQyxDQUFDLGFBQWEsR0FBRyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUMsYUFBYSxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxJQUFBLEVBQW5GLFNBQVMsUUFBQSxFQUFFLE9BQU8sUUFBaUUsQ0FBQTtZQUMxRixJQUFNLFFBQVEsR0FBRyxFQUFFLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQTtZQUNuRCxJQUFJLFNBQVMsSUFBSSxRQUFRLElBQUksQ0FBQyxFQUFFLEVBQUUsT0FBTztnQkFDckMsT0FBTztvQkFDSCxJQUFJLEVBQUUsQ0FBQztvQkFDUCxJQUFJLEVBQUUsUUFBUTtpQkFDakIsQ0FBQTthQUNKO1FBQ0wsQ0FBQyxDQUFDO2FBQ0csTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLENBQUMsRUFBSCxDQUFHLENBQUM7YUFDaEIsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUM7WUFDUCxJQUFNLElBQUksR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtZQUM5RCxJQUFJLElBQUksSUFBSSxJQUFJLEVBQUU7Z0JBQ2QsT0FBTyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7YUFDeEI7aUJBQU07Z0JBQ0gsT0FBTyxDQUFDLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUE7YUFDekI7UUFDTCxDQUFDLENBQUMsQ0FBQTtRQUNWLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxVQUFDLEVBQUUsRUFBRSxLQUFLO1lBQ2hDLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxJQUFJLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQTtZQUNqQyxJQUFNLElBQUksR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEdBQUcsR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtZQUN2RCxFQUFFLENBQUMsT0FBTyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUE7WUFDNUIscUJBQVMsQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ2pFLEVBQUUsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUNqRyxLQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxLQUFLLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQzVELENBQUMsQ0FBQyxDQUFBO1FBQ0YsSUFBSSxFQUFFLEVBQUUsRUFBRSxTQUFTO1lBQ2YsSUFBSSxDQUFDLGVBQWUsR0FBRyxFQUFFLENBQUE7WUFDekIsSUFBTSxLQUFHLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxLQUFLLEVBQUUsRUFBaEIsQ0FBZ0IsQ0FBQyxDQUFBO1lBQ2pELEVBQUUsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxJQUFJLENBQUMsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksS0FBSSxDQUFDLFFBQVEsQ0FBQyxrQkFBa0IsQ0FBQyxLQUFHLENBQUMsRUFBckQsQ0FBcUQsQ0FBQyxDQUFBO1NBQ3ZGO1FBQ0QsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUE7UUFDekIsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO0lBQ3hCLENBQUM7SUFFTyw4Q0FBZ0IsR0FBeEIsVUFBeUIsRUFBVyxFQUFFLE1BQWU7UUFDakQsRUFBRSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEdBQUcsTUFBTSxDQUFBO1FBQ2xDLEVBQUUsQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLENBQUMsTUFBTSxHQUFHLE1BQU0sQ0FBQTtJQUN6QyxDQUFDO0lBRU8sNENBQWMsR0FBdEIsVUFBdUIsR0FBWTs7UUFDL0IsSUFBSSxDQUFDLGFBQWEsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFBO1FBQy9DLElBQUksQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDLE1BQU0sR0FBRyxHQUFHLENBQUE7UUFFbEQsSUFBSSxHQUFHLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRTtZQUN2QixJQUFBLEtBQUEsT0FBYyxFQUFFLENBQUMsZUFBZSxPQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxlQUFlLENBQUMsMENBQUUsS0FBSyxFQUFFLEdBQUcsQ0FBQyxJQUFBLEVBQXBHLElBQUUsUUFBQSxFQUFFLEtBQUssUUFBMkYsQ0FBQTtZQUMzRyxJQUFJLENBQUMsYUFBYSxHQUFHLEtBQUssQ0FBQTtZQUMxQixJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO1lBQ3ZELHFCQUFTLENBQUMsZ0JBQWdCLENBQUMsSUFBRSxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzVELElBQUksQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxFQUFFLEtBQUssSUFBRSxJQUFJLENBQUMsQ0FBQyxLQUFLLElBQUksQ0FBQyxFQUEzQixDQUEyQixDQUFDLENBQUMsTUFBTSxDQUFBO1lBQ3JHLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxFQUFFLENBQUE7WUFDN0ksSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsWUFBWSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxHQUFHLEVBQUUsQ0FBQTtTQUNoRTtJQUNMLENBQUM7SUFFTywyQ0FBYSxHQUFyQjtRQUNJLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQTtRQUM5QyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUMsQ0FBQTtJQUN2RixDQUFDO0lBRWEsNkNBQWUsR0FBN0I7Ozs7Ozt3QkFDSSxJQUFJLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFOzRCQUM5QyxzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQywwQkFBMEIsQ0FBQyxFQUFBO3lCQUMxRDt3QkFDRCxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFOzRCQUM1RCxzQkFBTTt5QkFDVDt3QkFDcUIscUJBQU0sb0JBQU8sQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLDJCQUEyQixFQUFFLEVBQUUsRUFBRSxFQUFFLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQyxFQUFBOzt3QkFBcEcsS0FBZ0IsU0FBb0YsRUFBbEcsR0FBRyxTQUFBLEVBQUUsSUFBSSxVQUFBO3dCQUNqQixJQUFJLEdBQUcsRUFBRTs0QkFDTCxzQkFBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsRUFBQTt5QkFDbkM7d0JBQ0QsSUFBSSxJQUFJLEVBQUU7NEJBQ04sSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBOzRCQUNwQyxJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBOzRCQUN0RCxJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMscUJBQXFCLENBQUMsQ0FBQTs0QkFDMUMsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGlCQUFpQixDQUFDLENBQUE7NEJBQ3RDLHVCQUFVLENBQUMsU0FBUyxDQUFDLHFCQUFxQixDQUFDLENBQUE7NEJBQzNDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTt5QkFDZDs7Ozs7S0FDSjtJQWxKZ0IsbUJBQW1CO1FBRHZDLE9BQU87T0FDYSxtQkFBbUIsQ0FtSnZDO0lBQUQsMEJBQUM7Q0FuSkQsQUFtSkMsQ0FuSmdELEVBQUUsQ0FBQyxXQUFXLEdBbUo5RDtrQkFuSm9CLG1CQUFtQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBFdmVudFR5cGUgZnJvbSBcIi4uLy4uL2NvbW1vbi9ldmVudC9FdmVudFR5cGVcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgVXNlck1vZGVsIGZyb20gXCIuLi8uLi9tb2RlbC9jb21tb24vVXNlck1vZGVsXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNraW5FeGNoYW5nZVBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgc2tpbnNTdl86IGNjLlNjcm9sbFZpZXcgPSBudWxsIC8vIHBhdGg6Ly9yb290L3NraW4vc2tpbnNfc3ZcbiAgICBwcml2YXRlIGV4Y2hhbmdlTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2V4Y2hhbmdlX25cbiAgICBwcml2YXRlIGJ1dHRvbnNOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvYnV0dG9uc19uXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIHVzZXI6IFVzZXJNb2RlbCA9IG51bGxcbiAgICBwcml2YXRlIGN1clNlbGVjdFNraW5JZDogbnVtYmVyID0gMFxuICAgIHByaXZhdGUgY3VyU2VsZWN0U2tpbkNvdW50OiBudW1iZXIgPSAwXG4gICAgcHJpdmF0ZSBuZWVkU2tpbkNvdW50OiBudW1iZXIgPSAwXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgICAgIHRoaXMudXNlciA9IHRoaXMuZ2V0TW9kZWwoJ3VzZXInKVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGlkOiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5jdXJTZWxlY3RTa2luSWQgPSAwXG4gICAgICAgIHRoaXMuY3VyU2VsZWN0U2tpbkNvdW50ID0gMFxuICAgICAgICB0aGlzLm5lZWRTa2luQ291bnQgPSAwXG4gICAgICAgIHRoaXMuaW5pdChpZClcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgfVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcblxuICAgIC8vIHBhdGg6Ly9yb290L3NraW4vc2tpbnNfc3Yvdmlldy9jb250ZW50L3NraW5fYmVcbiAgICBvbkNsaWNrU2tpbihldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGlkID0gZXZlbnQudGFyZ2V0Py5EYXRhPy5pZFxuICAgICAgICBpZiAodGhpcy5jdXJTZWxlY3RTa2luSWQgPT09IGlkKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmN1clNlbGVjdFNraW5JZCA9IGlkXG4gICAgICAgIHRoaXMuc2tpbnNTdl8uY29udGVudC5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gdGhpcy51cGRhdGVTZWxlY3RTa2luKG0uQ2hpbGQoJ3Jvb3QnKSwgbS5EYXRhPy5pZCA9PT0gaWQpKVxuICAgICAgICB0aGlzLnVwZGF0ZVNraW5JbmZvKHRydWUpXG4gICAgICAgIHRoaXMudXBkYXRlQnV0dG9ucygpXG4gICAgfVxuXG4gICAgLy8gcGF0aDovL3Jvb3QvYnV0dG9uc19uL2V4Y2hhbmdlX2JlXG4gICAgb25DbGlja0V4Y2hhbmdlKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1clNlbGVjdFNraW5JZCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd1aS5wbGVhc2Vfc2VsZWN0X3NraW4nKVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCgnY29tbW9uL1NraW5FeGNoYW5nZVRpcCcsIHRoaXMuY3VyU2VsZWN0U2tpbklkLCAob2spID0+IHtcbiAgICAgICAgICAgIG9rICYmIHRoaXMucmVxRXhjaGFuZ2VTa2luKClcbiAgICAgICAgfSlcbiAgICB9XG4gICAgLy9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICBwcml2YXRlIGluaXQoaWQ/OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3Qgc2VydmVyQXJlYSA9IGdhbWVIcHIuaXNSZWxlYXNlID8gZ2FtZUhwci5nZXRTZXJ2ZXJBcmVhKCkgOiAndGVzdCcsXG4gICAgICAgICAgICBza2lucyA9IHRoaXMudXNlci5nZXRVbmxvY2tQYXduU2tpbklkcygpLFxuICAgICAgICAgICAgbGlzdCA9IGFzc2V0c01nci5nZXRKc29uKCdwYXduU2tpbicpLmdldCgnY29uZCcsIDUpLm1hcChtID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBbc3RhcnRUaW1lLCBlbmRUaW1lXSA9IChtWydsaW1pdF90aW1lXycgKyBzZXJ2ZXJBcmVhXSB8fCBtLmxpbWl0X3RpbWVfaGspLnNwbGl0KCd8JylcbiAgICAgICAgICAgICAgICBjb25zdCB0aW1lRGlmZiA9IHV0LnRpbWVkaWZmKERhdGUubm93KCksIHN0YXJ0VGltZSlcbiAgICAgICAgICAgICAgICBpZiAoc3RhcnRUaW1lICYmIHRpbWVEaWZmIDw9IDApIHsgLy8g6YG/5YWN5Ymn6YCPXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpbmZvOiBtLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGltZTogdGltZURpZmZcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgLmZpbHRlcihtID0+ICEhbSlcbiAgICAgICAgICAgICAgICAuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBoYXNBID0gc2tpbnMuaGFzKGEuaW5mby5pZCksIGhhc0IgPSBza2lucy5oYXMoYi5pbmZvLmlkKVxuICAgICAgICAgICAgICAgICAgICBpZiAoaGFzQSB8fCBoYXNCKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaGFzQSA/IDEgOiAtMTtcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBiLnRpbWUgLSBhLnRpbWVcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgIHRoaXMuc2tpbnNTdl8uSXRlbXMobGlzdCwgKGl0LCBfZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGl0LkRhdGEgPSBfZGF0YS5pbmZvXG4gICAgICAgICAgICBjb25zdCByb290ID0gaXQuQ2hpbGQoJ3Jvb3QnKSwgaGFzID0gc2tpbnMuaGFzKGRhdGEuaWQpXG4gICAgICAgICAgICBpdC5vcGFjaXR5ID0gaGFzID8gMTUwIDogMjU1XG4gICAgICAgICAgICByZXNIZWxwZXIubG9hZFBhd25IZWFkSWNvbihkYXRhLmlkLCByb290LkNoaWxkKCdpY29uJyksIHRoaXMua2V5KVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ3ZhbCcpLnNldExvY2FsZUtleSh1dC5uYW1lRm9ybWF0b3IoYXNzZXRzTWdyLmxhbmcoJ3Bhd25UZXh0Lm5hbWVfJyArIGRhdGEucGF3bl9pZCksIDQpKVxuICAgICAgICAgICAgdGhpcy51cGRhdGVTZWxlY3RTa2luKHJvb3QsIGlkID8gaWQgPT09IGRhdGEuaWQgOiBmYWxzZSlcbiAgICAgICAgfSlcbiAgICAgICAgaWYgKGlkKSB7IC8vIOi3s+i9rOaMh+WummlkXG4gICAgICAgICAgICB0aGlzLmN1clNlbGVjdFNraW5JZCA9IGlkXG4gICAgICAgICAgICBjb25zdCBpZHggPSBsaXN0LmZpbmRJbmRleChtID0+IG0uaW5mby5pZCA9PT0gaWQpXG4gICAgICAgICAgICB1dC53YWl0TmV4dEZyYW1lKCkudGhlbigoKSA9PiB0aGlzLmlzVmFsaWQgJiYgdGhpcy5za2luc1N2Xy5TZWxlY3RJdGVtVG9DZW50cmUoaWR4KSlcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnVwZGF0ZVNraW5JbmZvKCEhaWQpXG4gICAgICAgIHRoaXMudXBkYXRlQnV0dG9ucygpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSB1cGRhdGVTZWxlY3RTa2luKGl0OiBjYy5Ob2RlLCBzZWxlY3Q6IGJvb2xlYW4pIHtcbiAgICAgICAgaXQuQ2hpbGQoJ3NlbGVjdCcpLmFjdGl2ZSA9IHNlbGVjdFxuICAgICAgICBpdC5DaGlsZCgnYmcvc2VsZWN0JykuYWN0aXZlID0gc2VsZWN0XG4gICAgfVxuXG4gICAgcHJpdmF0ZSB1cGRhdGVTa2luSW5mbyh2YWw6IGJvb2xlYW4pIHtcbiAgICAgICAgdGhpcy5leGNoYW5nZU5vZGVfLkNoaWxkKCdlbXB0eScpLmFjdGl2ZSA9ICF2YWxcbiAgICAgICAgdGhpcy5leGNoYW5nZU5vZGVfLkNoaWxkKCduZWVkX3NraW4nKS5hY3RpdmUgPSB2YWxcblxuICAgICAgICBpZiAodmFsICYmIHRoaXMuY3VyU2VsZWN0U2tpbklkKSB7XG4gICAgICAgICAgICBjb25zdCBbaWQsIGNvdW50XSA9IHV0LnN0cmluZ1RvTnVtYmVycyhhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ3Bhd25Ta2luJywgdGhpcy5jdXJTZWxlY3RTa2luSWQpPy52YWx1ZSwgJywnKVxuICAgICAgICAgICAgdGhpcy5uZWVkU2tpbkNvdW50ID0gY291bnRcbiAgICAgICAgICAgIGNvbnN0IHJvb3QgPSB0aGlzLmV4Y2hhbmdlTm9kZV8uQ2hpbGQoJ25lZWRfc2tpbi9yb290JylcbiAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkUGF3bkhlYWRJY29uKGlkLCByb290LkNoaWxkKCdpY29uJyksIHRoaXMua2V5KVxuICAgICAgICAgICAgdGhpcy5jdXJTZWxlY3RTa2luQ291bnQgPSB0aGlzLnVzZXIuZ2V0U2tpbkl0ZW1MaXN0KCkuZmlsdGVyKG0gPT4gbS5pZCA9PT0gaWQgJiYgbS5zdGF0ZSA+PSAwKS5sZW5ndGhcbiAgICAgICAgICAgIHJvb3QucGFyZW50LkNoaWxkKCdjb3VudC9jdXInLCBjYy5MYWJlbCkuQ29sb3IodGhpcy5jdXJTZWxlY3RTa2luQ291bnQgPCBjb3VudCA/ICcjQzgzQzA0JyA6ICcjMzMzMzMzJykuc3RyaW5nID0gdGhpcy5jdXJTZWxlY3RTa2luQ291bnQgKyAnJ1xuICAgICAgICAgICAgcm9vdC5wYXJlbnQuQ2hpbGQoJ2NvdW50L25lZWQnLCBjYy5MYWJlbCkuc3RyaW5nID0gY291bnQgKyAnJ1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHJpdmF0ZSB1cGRhdGVCdXR0b25zKCkge1xuICAgICAgICBjb25zdCBza2lucyA9IHRoaXMudXNlci5nZXRVbmxvY2tQYXduU2tpbklkcygpXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLlN3aWgoc2tpbnMuaGFzKHRoaXMuY3VyU2VsZWN0U2tpbklkKSA/ICdleGlzdGVkJyA6ICdleGNoYW5nZV9iZScpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBhc3luYyByZXFFeGNoYW5nZVNraW4oKSB7XG4gICAgICAgIGlmICh0aGlzLmN1clNlbGVjdFNraW5Db3VudCA8IHRoaXMubmVlZFNraW5Db3VudCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5za2luX2V4Y2hhbmdlX2ZhaWwnKVxuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLnVzZXIuZ2V0VW5sb2NrUGF3blNraW5JZHMoKS5oYXModGhpcy5jdXJTZWxlY3RTa2luSWQpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IGVyciwgZGF0YSB9ID0gYXdhaXQgZ2FtZUhwci5uZXQucmVxdWVzdCgnbG9iYnkvSERfQ29tcG9zZVNraW5JdGVtcycsIHsgaWQ6IHRoaXMuY3VyU2VsZWN0U2tpbklkIH0pXG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgIH1cbiAgICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgICAgIHRoaXMudXNlci5zZXRTa2luSXRlbUxpc3QoZGF0YS5saXN0KVxuICAgICAgICAgICAgdGhpcy51c2VyLnNldFVubG9ja1Bhd25Ta2luSWRzKGRhdGEudW5sb2NrUGF3blNraW5JZHMpXG4gICAgICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlVQREFURV9FWENIQU5HRV9TS0lOUylcbiAgICAgICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuVVBEQVRFX01ZU1RFUllCT1gpXG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QuZXhjaGFuZ2Vfc3VjYycpXG4gICAgICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICB9XG4gICAgfVxufVxuXG4iXX0=