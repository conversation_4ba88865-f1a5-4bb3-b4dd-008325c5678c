
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SendTrumpetPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '26e7c5ooKRPk4UhbRCxUbpR', 'SendTrumpetPnlCtrl');
// app/script/view/common/SendTrumpetPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SendTrumpetPnlCtrl = /** @class */ (function (_super) {
    __extends(SendTrumpetPnlCtrl, _super);
    function SendTrumpetPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/input_eb
        _this.descNode_ = null; // path://root/desc_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.goldLbl_ = null; // path://root/button_n/send_be/lay/gold/gold_l
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    SendTrumpetPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SendTrumpetPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SendTrumpetPnlCtrl.prototype.onEnter = function (data) {
        var isCanSend = GameHelper_1.gameHpr.user.getAccTotalGameCount() >= 1;
        this.buttonNode_.Swih(isCanSend ? 'send_be' : 'cond');
        this.descNode_.active = isCanSend;
        var cost = Constant_1.SEND_TRUMPET_COST + GameHelper_1.gameHpr.user.getTodaySendTrumpetCount() * Constant_1.SEND_TRUMPET_ACC_COST;
        this.goldLbl_.string = cost + '';
    };
    SendTrumpetPnlCtrl.prototype.onRemove = function () {
    };
    SendTrumpetPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/button_n/send_be
    SendTrumpetPnlCtrl.prototype.onClickSend = function (event, data) {
        var _this = this;
        var content = this.inputEb_.string.trim();
        if (!content) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_CONTENT);
        }
        else if (ut.getStringLen(content) > 100 || GameHelper_1.gameHpr.getTextNewlineCount(content) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
        }
        else if (GameHelper_1.gameHpr.isRelease && GameHelper_1.gameHpr.isGuest()) {
            return ViewHelper_1.viewHelper.showAlert('toast.guest_no_send_trumpet');
        }
        else if (GameHelper_1.gameHpr.user.getAccTotalGameCount() < 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_complete_one_game');
        }
        GameHelper_1.gameHpr.user.sendTrumpet(content).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.inputEb_.string = '';
                _this.hide();
                ViewHelper_1.viewHelper.hidePnl('common/Chat');
            }
        });
    };
    SendTrumpetPnlCtrl = __decorate([
        ccclass
    ], SendTrumpetPnlCtrl);
    return SendTrumpetPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SendTrumpetPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2VuZFRydW1wZXRQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJEQUEwRjtBQUMxRixxREFBb0Q7QUFDcEQsNkRBQXlEO0FBQ3pELDZEQUE0RDtBQUVwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFnRCxzQ0FBYztJQUE5RDtRQUFBLHFFQTREQztRQTFERywwQkFBMEI7UUFDbEIsY0FBUSxHQUFlLElBQUksQ0FBQSxDQUFDLHVCQUF1QjtRQUNuRCxlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMscUJBQXFCO1FBQy9DLGlCQUFXLEdBQVksSUFBSSxDQUFBLENBQUMsdUJBQXVCO1FBQ25ELGNBQVEsR0FBYSxJQUFJLENBQUEsQ0FBQywrQ0FBK0M7O1FBaURqRixNQUFNO1FBQ04saUhBQWlIO1FBRWpILGlIQUFpSDtJQUVySCxDQUFDO0lBckRHLE1BQU07SUFFQyw0Q0FBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLHFDQUFRLEdBQXJCOzs7Ozs7S0FDQztJQUVNLG9DQUFPLEdBQWQsVUFBZSxJQUFTO1FBQ3BCLElBQU0sU0FBUyxHQUFHLG9CQUFPLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFLElBQUksQ0FBQyxDQUFBO1FBQzFELElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQTtRQUNyRCxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxTQUFTLENBQUE7UUFDakMsSUFBTSxJQUFJLEdBQUcsNEJBQWlCLEdBQUcsb0JBQU8sQ0FBQyxJQUFJLENBQUMsd0JBQXdCLEVBQUUsR0FBRyxnQ0FBcUIsQ0FBQTtRQUNoRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFBO0lBQ3BDLENBQUM7SUFFTSxxQ0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLG9DQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiwrQkFBK0I7SUFDL0Isd0NBQVcsR0FBWCxVQUFZLEtBQTBCLEVBQUUsSUFBWTtRQUFwRCxpQkFvQkM7UUFuQkcsSUFBTSxPQUFPLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDM0MsSUFBSSxDQUFDLE9BQU8sRUFBRTtZQUNWLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLG9CQUFvQixDQUFDLENBQUE7U0FDMUQ7YUFBTSxJQUFJLEVBQUUsQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLEdBQUcsR0FBRyxJQUFJLG9CQUFPLENBQUMsbUJBQW1CLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ25GLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsOEJBQThCLENBQUMsQ0FBQTtTQUM5RDthQUFNLElBQUksb0JBQU8sQ0FBQyxTQUFTLElBQUksb0JBQU8sQ0FBQyxPQUFPLEVBQUUsRUFBRTtZQUMvQyxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLDZCQUE2QixDQUFDLENBQUE7U0FDN0Q7YUFBTSxJQUFJLG9CQUFPLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFLEdBQUcsQ0FBQyxFQUFFO1lBQ2hELE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsZ0NBQWdDLENBQUMsQ0FBQTtTQUNoRTtRQUNELG9CQUFPLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQSxHQUFHO1lBQ3RDLElBQUksR0FBRyxFQUFFO2dCQUNMLE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDbkM7aUJBQU0sSUFBSSxLQUFJLENBQUMsT0FBTyxFQUFFO2dCQUNyQixLQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUE7Z0JBQ3pCLEtBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtnQkFDWCx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxhQUFhLENBQUMsQ0FBQTthQUNwQztRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQXREZ0Isa0JBQWtCO1FBRHRDLE9BQU87T0FDYSxrQkFBa0IsQ0E0RHRDO0lBQUQseUJBQUM7Q0E1REQsQUE0REMsQ0E1RCtDLEVBQUUsQ0FBQyxXQUFXLEdBNEQ3RDtrQkE1RG9CLGtCQUFrQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNFTkRfVFJVTVBFVF9BQ0NfQ09TVCwgU0VORF9UUlVNUEVUX0NPU1QgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTZW5kVHJ1bXBldFBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgaW5wdXRFYl86IGNjLkVkaXRCb3ggPSBudWxsIC8vIHBhdGg6Ly9yb290L2lucHV0X2ViXG4gICAgcHJpdmF0ZSBkZXNjTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2Rlc2NfblxuICAgIHByaXZhdGUgYnV0dG9uTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2J1dHRvbl9uXG4gICAgcHJpdmF0ZSBnb2xkTGJsXzogY2MuTGFiZWwgPSBudWxsIC8vIHBhdGg6Ly9yb290L2J1dHRvbl9uL3NlbmRfYmUvbGF5L2dvbGQvZ29sZF9sXG4gICAgLy9AZW5kXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIoZGF0YTogYW55KSB7XG4gICAgICAgIGNvbnN0IGlzQ2FuU2VuZCA9IGdhbWVIcHIudXNlci5nZXRBY2NUb3RhbEdhbWVDb3VudCgpID49IDFcbiAgICAgICAgdGhpcy5idXR0b25Ob2RlXy5Td2loKGlzQ2FuU2VuZCA/ICdzZW5kX2JlJyA6ICdjb25kJylcbiAgICAgICAgdGhpcy5kZXNjTm9kZV8uYWN0aXZlID0gaXNDYW5TZW5kXG4gICAgICAgIGNvbnN0IGNvc3QgPSBTRU5EX1RSVU1QRVRfQ09TVCArIGdhbWVIcHIudXNlci5nZXRUb2RheVNlbmRUcnVtcGV0Q291bnQoKSAqIFNFTkRfVFJVTVBFVF9BQ0NfQ09TVFxuICAgICAgICB0aGlzLmdvbGRMYmxfLnN0cmluZyA9IGNvc3QgKyAnJ1xuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3QvYnV0dG9uX24vc2VuZF9iZVxuICAgIG9uQ2xpY2tTZW5kKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgY29udGVudCA9IHRoaXMuaW5wdXRFYl8uc3RyaW5nLnRyaW0oKVxuICAgICAgICBpZiAoIWNvbnRlbnQpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlY29kZS5QTEVBU0VfSU5QVVRfQ09OVEVOVClcbiAgICAgICAgfSBlbHNlIGlmICh1dC5nZXRTdHJpbmdMZW4oY29udGVudCkgPiAxMDAgfHwgZ2FtZUhwci5nZXRUZXh0TmV3bGluZUNvdW50KGNvbnRlbnQpID4gMSkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC50ZXh0X2xlbl9saW1pdF9jb250ZW50JylcbiAgICAgICAgfSBlbHNlIGlmIChnYW1lSHByLmlzUmVsZWFzZSAmJiBnYW1lSHByLmlzR3Vlc3QoKSkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5ndWVzdF9ub19zZW5kX3RydW1wZXQnKVxuICAgICAgICB9IGVsc2UgaWYgKGdhbWVIcHIudXNlci5nZXRBY2NUb3RhbEdhbWVDb3VudCgpIDwgMSkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2VfY29tcGxldGVfb25lX2dhbWUnKVxuICAgICAgICB9XG4gICAgICAgIGdhbWVIcHIudXNlci5zZW5kVHJ1bXBldChjb250ZW50KS50aGVuKGVyciA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVycilcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5pbnB1dEViXy5zdHJpbmcgPSAnJ1xuICAgICAgICAgICAgICAgIHRoaXMuaGlkZSgpXG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5oaWRlUG5sKCdjb21tb24vQ2hhdCcpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuICAgIC8vQGVuZFxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG59XG4iXX0=