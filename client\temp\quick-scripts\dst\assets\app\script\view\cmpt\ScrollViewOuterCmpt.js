
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ScrollViewOuterCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '24314/3WIJOY4H2mPWlifjZ', 'ScrollViewOuterCmpt');
// app/script/view/cmpt/ScrollViewOuterCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ScrollViewInnerCmpt_1 = require("./ScrollViewInnerCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 代替外部的ScrollView
var ScrollViewOuterCmpt = /** @class */ (function (_super) {
    __extends(ScrollViewOuterCmpt, _super);
    function ScrollViewOuterCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.innerPath = 'list';
        _this.planDir = -2;
        _this.scrollingInnerSv = null;
        _this.tempVec = cc.v2();
        return _this;
    }
    //是否为子物体
    //注意，这里递归, 如果child藏的太深, 可能影响效率。其实也还好，只是开始滑动时执行一次。
    ScrollViewOuterCmpt.prototype._isHisChild = function (child, undeterminedParent) {
        if (child === undeterminedParent) {
            return true;
        }
        return child.parent ? this._isHisChild(child.parent, undeterminedParent) : false;
    };
    //判断Target是否是InnerScrollView的子物体, 如果是，就返回这个InnerScrollView。
    //注意，这里遍历所有InnerScrollView, 如果InnerScrollView数量太多，可能影响效率。其实也还好，只是开始滑动时执行一次。
    ScrollViewOuterCmpt.prototype._findScrollingInnerSv = function (target) {
        var _this = this;
        var node = this.Find(function (m) { return _this._isHisChild(target, m.Child(_this.innerPath)); });
        return node && node.Child(this.innerPath, ScrollViewInnerCmpt_1.default);
    };
    //检查实际与计划方向的一致性
    ScrollViewOuterCmpt.prototype.isDifferentBetweenSettingAndPlan = function (sv) {
        if (this.planDir === 0) {
            return false;
        }
        if (this.planDir === 1 && sv.horizontal) {
            return false;
        }
        if (this.planDir === -1 && sv.vertical) {
            return false;
        }
        return true;
    };
    //#region 重写cc.ScrollView的方法
    ScrollViewOuterCmpt.prototype.hasNestedViewGroup = function (event, captureListeners) {
        if (event.eventPhase !== cc.Event.CAPTURING_PHASE) {
            return false;
        }
        return false;
    };
    ScrollViewOuterCmpt.prototype._onTouchBegan = function (event, captureListeners) {
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return;
        }
        // 重置计划方向
        this.planDir = -2;
        this.scrollingInnerSv = null;
        if (this.content) {
            // @ts-ignore
            this._handlePressLogic(event.touch);
        }
        // @ts-ignore
        this._touchMoved = false;
        // @ts-ignore
        this._stopPropagationIfTargetIsMe(event);
    };
    ScrollViewOuterCmpt.prototype._onTouchMoved = function (event, captureListeners) {
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return;
        }
        //在滑动时, 设置开始时滑动的方向为计划方向
        //为什么在Outer中做这件事？
        //因为Outer的_onTouchMoved比Inner的早执行, 如果在Inner里做, Outer中就得忽略一帧，体验可能会不好。
        var deltaMove = event.getLocation().sub(event.getStartLocation(), this.tempVec);
        if (this.planDir === -2 && deltaMove.mag() > Constant_1.CLICK_SPACE) {
            this.scrollingInnerSv = this._findScrollingInnerSv(event.target);
            if (this.scrollingInnerSv) {
                var contentSize = this.scrollingInnerSv.content.getContentSize();
                var scrollViewSize = this.scrollingInnerSv.node.getContentSize();
                if ((this.scrollingInnerSv.vertical && (contentSize.height > scrollViewSize.height)) || (this.scrollingInnerSv.horizontal && (contentSize.width > scrollViewSize.width))) {
                    this.planDir = Math.abs(deltaMove.x) > Math.abs(deltaMove.y) ? 1 : -1;
                }
                else {
                    this.planDir = 0;
                }
            }
            else {
                this.planDir = 0;
            }
        }
        if (this.content && !this.isDifferentBetweenSettingAndPlan(this)) {
            // @ts-ignore
            this._handleMoveLogic(event.touch);
        }
        if (!this.cancelInnerEvents) {
            return;
        }
        //只取消会捕获事件的直接子物体(如Button)上的事件
        if (!this.scrollingInnerSv) {
            if (deltaMove.mag() > Constant_1.CLICK_SPACE) {
                // @ts-ignore
                if (!this._touchMoved && event.target !== this.node) {
                    var cancelEvent = new cc.Event.EventTouch(event.getTouches(), event.bubbles);
                    cancelEvent.type = cc.Node.EventType.TOUCH_CANCEL;
                    cancelEvent.touch = event.touch;
                    // @ts-ignore
                    cancelEvent.simulate = true;
                    event.target.dispatchEvent(cancelEvent);
                    // @ts-ignore
                    this._touchMoved = true;
                }
            }
            // @ts-ignore
            this._stopPropagationIfTargetIsMe(event);
        }
    };
    __decorate([
        property
    ], ScrollViewOuterCmpt.prototype, "innerPath", void 0);
    ScrollViewOuterCmpt = __decorate([
        ccclass
    ], ScrollViewOuterCmpt);
    return ScrollViewOuterCmpt;
}(cc.ScrollView));
exports.default = ScrollViewOuterCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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