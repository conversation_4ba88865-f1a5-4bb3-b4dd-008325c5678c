
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SavePortrayalAttrPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '390f2yBG4dJqY83ZyAQhAsg', 'SavePortrayalAttrPnlCtrl');
// app/script/view/common/SavePortrayalAttrPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SavePortrayalAttrPnlCtrl = /** @class */ (function (_super) {
    __extends(SavePortrayalAttrPnlCtrl, _super);
    function SavePortrayalAttrPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.attrsNode_ = null; // path://root/attrs_n
        _this.buttonsNode_ = null; // path://root/buttons_nbe_n
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    SavePortrayalAttrPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SavePortrayalAttrPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SavePortrayalAttrPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        ViewHelper_1.viewHelper.updatePortrayalShortAttr(this.attrsNode_, data.attrs, data.avatarPawnName);
        this.buttonsNode_.Child('0/lay/war_token/val', cc.Label).string = Constant_1.RESTORE_PORTRAYAL_WAR_TOKEN_COST + '';
        this.buttonsNode_.Child('1/lay/gold/val', cc.Label).string = Constant_1.RESTORE_PORTRAYAL_GOLD_COST + '';
    };
    SavePortrayalAttrPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(-1);
        this.cb = null;
    };
    SavePortrayalAttrPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe_n
    SavePortrayalAttrPnlCtrl.prototype.onClickButtons = function (event, data) {
        var type = Number(event.target.name), user = GameHelper_1.gameHpr.user;
        if (type === 0) {
            if (Constant_1.RESTORE_PORTRAYAL_WAR_TOKEN_COST > user.getWarToken()) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.WAR_TOKEN_NOT_ENOUGH);
            }
        }
        else if (Constant_1.RESTORE_PORTRAYAL_GOLD_COST > user.getGold()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.GOLD_NOT_ENOUGH);
        }
        this.cb && this.cb(type);
        this.cb = null;
        this.hide();
    };
    SavePortrayalAttrPnlCtrl = __decorate([
        ccclass
    ], SavePortrayalAttrPnlCtrl);
    return SavePortrayalAttrPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SavePortrayalAttrPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2F2ZVBvcnRyYXlhbEF0dHJQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJEQUErRztBQUMvRyxxREFBb0Q7QUFDcEQsNkRBQXlEO0FBQ3pELDZEQUE0RDtBQUVwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFzRCw0Q0FBYztJQUFwRTtRQUFBLHFFQXFEQztRQW5ERywwQkFBMEI7UUFDbEIsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQyxzQkFBc0I7UUFDakQsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQyw0QkFBNEI7UUFDakUsTUFBTTtRQUVFLFFBQUUsR0FBYSxJQUFJLENBQUE7O1FBeUMzQixNQUFNO1FBQ04saUhBQWlIO1FBRWpILGlIQUFpSDtJQUVySCxDQUFDO0lBNUNVLGtEQUFlLEdBQXRCO1FBQ0ksT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRVksMkNBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0sMENBQU8sR0FBZCxVQUFlLElBQVMsRUFBRSxFQUFZO1FBQ2xDLElBQUksQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFBO1FBQ1osdUJBQVUsQ0FBQyx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFBO1FBQ3JGLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLHFCQUFxQixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsMkNBQWdDLEdBQUcsRUFBRSxDQUFBO1FBQ3ZHLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLGdCQUFnQixFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsc0NBQTJCLEdBQUcsRUFBRSxDQUFBO0lBQ2pHLENBQUM7SUFFTSwyQ0FBUSxHQUFmO1FBQ0ksSUFBSSxDQUFDLEVBQUUsSUFBSSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDdEIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLDBDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiw0QkFBNEI7SUFDNUIsaURBQWMsR0FBZCxVQUFlLEtBQTBCLEVBQUUsSUFBWTtRQUNuRCxJQUFNLElBQUksR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxJQUFJLENBQUE7UUFDM0QsSUFBSSxJQUFJLEtBQUssQ0FBQyxFQUFFO1lBQ1osSUFBSSwyQ0FBZ0MsR0FBRyxJQUFJLENBQUMsV0FBVyxFQUFFLEVBQUU7Z0JBQ3ZELE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLG9CQUFvQixDQUFDLENBQUE7YUFDMUQ7U0FDSjthQUFNLElBQUksc0NBQTJCLEdBQUcsSUFBSSxDQUFDLE9BQU8sRUFBRSxFQUFFO1lBQ3JELE9BQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsYUFBSyxDQUFDLGVBQWUsQ0FBQyxDQUFBO1NBQ3JEO1FBQ0QsSUFBSSxDQUFDLEVBQUUsSUFBSSxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3hCLElBQUksQ0FBQyxFQUFFLEdBQUcsSUFBSSxDQUFBO1FBQ2QsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO0lBQ2YsQ0FBQztJQS9DZ0Isd0JBQXdCO1FBRDVDLE9BQU87T0FDYSx3QkFBd0IsQ0FxRDVDO0lBQUQsK0JBQUM7Q0FyREQsQUFxREMsQ0FyRHFELEVBQUUsQ0FBQyxXQUFXLEdBcURuRTtrQkFyRG9CLHdCQUF3QiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJFU1RPUkVfUE9SVFJBWUFMX0dPTERfQ09TVCwgUkVTVE9SRV9QT1JUUkFZQUxfV0FSX1RPS0VOX0NPU1QgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBTYXZlUG9ydHJheWFsQXR0clBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgYXR0cnNOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvYXR0cnNfblxuICAgIHByaXZhdGUgYnV0dG9uc05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9idXR0b25zX25iZV9uXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIGNiOiBGdW5jdGlvbiA9IG51bGxcblxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cblxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcbiAgICB9XG5cbiAgICBwdWJsaWMgb25FbnRlcihkYXRhOiBhbnksIGNiOiBGdW5jdGlvbikge1xuICAgICAgICB0aGlzLmNiID0gY2JcbiAgICAgICAgdmlld0hlbHBlci51cGRhdGVQb3J0cmF5YWxTaG9ydEF0dHIodGhpcy5hdHRyc05vZGVfLCBkYXRhLmF0dHJzLCBkYXRhLmF2YXRhclBhd25OYW1lKVxuICAgICAgICB0aGlzLmJ1dHRvbnNOb2RlXy5DaGlsZCgnMC9sYXkvd2FyX3Rva2VuL3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSBSRVNUT1JFX1BPUlRSQVlBTF9XQVJfVE9LRU5fQ09TVCArICcnXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCcxL2xheS9nb2xkL3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSBSRVNUT1JFX1BPUlRSQVlBTF9HT0xEX0NPU1QgKyAnJ1xuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICAgICAgdGhpcy5jYiAmJiB0aGlzLmNiKC0xKVxuICAgICAgICB0aGlzLmNiID0gbnVsbFxuICAgIH1cblxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xuICAgIH1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cbiAgICAvLyBwYXRoOi8vcm9vdC9idXR0b25zX25iZV9uXG4gICAgb25DbGlja0J1dHRvbnMoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCB0eXBlID0gTnVtYmVyKGV2ZW50LnRhcmdldC5uYW1lKSwgdXNlciA9IGdhbWVIcHIudXNlclxuICAgICAgICBpZiAodHlwZSA9PT0gMCkge1xuICAgICAgICAgICAgaWYgKFJFU1RPUkVfUE9SVFJBWUFMX1dBUl9UT0tFTl9DT1NUID4gdXNlci5nZXRXYXJUb2tlbigpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVjb2RlLldBUl9UT0tFTl9OT1RfRU5PVUdIKVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKFJFU1RPUkVfUE9SVFJBWUFMX0dPTERfQ09TVCA+IHVzZXIuZ2V0R29sZCgpKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoZWNvZGUuR09MRF9OT1RfRU5PVUdIKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYih0eXBlKVxuICAgICAgICB0aGlzLmNiID0gbnVsbFxuICAgICAgICB0aGlzLmhpZGUoKVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxufVxuIl19