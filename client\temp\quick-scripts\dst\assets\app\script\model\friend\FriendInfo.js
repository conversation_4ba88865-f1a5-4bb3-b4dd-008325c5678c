
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/friend/FriendInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7ef32YZ7QtGW6EUWB5NKIO8', 'FriendInfo');
// app/script/model/friend/FriendInfo.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var BaseUserInfo_1 = require("../common/BaseUserInfo");
// 好友信息
var FriendInfo = /** @class */ (function (_super) {
    __extends(FriendInfo, _super);
    function FriendInfo() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.time = 0; //添加时间
        _this.playSid = 0; //当前区服id
        _this.offlineTime = 0; //离线时间
        _this.noteName = ''; //备注名
        _this.notReadCount = 0; //未读消息数量
        _this.giftList = []; //礼物列表 {uid, id, giftType, boxId}
        _this.getTime = 0;
        _this.lastChatInfo = null;
        return _this;
    }
    FriendInfo.prototype.init = function (data) {
        _super.prototype.init.call(this, data);
        this.time = data.time || 0;
        this.playSid = data.playSid || 0;
        this.offlineTime = data.offlineTime || 0;
        this.noteName = data.noteName || '';
        this.notReadCount = data.notReadCount || 0;
        this.giftList = data.giftList || [];
        this.getTime = Date.now();
        this.lastChatInfo = data.lastChatInfo;
        return this;
    };
    FriendInfo.prototype.getOfflineTime = function () {
        var now = Date.now();
        // 这里先从联盟中取
        var member = GameHelper_1.gameHpr.alliance.getMember(this.uid);
        if (member) {
            return member.offlineTime + (now - member.getTime);
        }
        return this.offlineTime + (now - this.getTime);
    };
    return FriendInfo;
}(BaseUserInfo_1.default));
exports.default = FriendInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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