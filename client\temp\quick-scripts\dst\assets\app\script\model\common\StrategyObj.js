
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/StrategyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c2d7aRFXXVFFZAVZERbMDDq', 'StrategyObj');
// app/script/model/common/StrategyObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 画像信息
var StrategyObj = /** @class */ (function () {
    function StrategyObj() {
        this.type = 0;
        this.targetId = []; //适用目标
        this.targetType = 0; //适用目标
        this.targetValue = 0;
        this.fighters = [];
        this.value = 0;
        this.params = 0;
        this.json = null;
    }
    StrategyObj.prototype.init = function (id) {
        var _a;
        this.type = id;
        this.json = assetsMgr.getJsonData('strategy', id);
        this.targetId = ut.stringToNumbers(((_a = this.json) === null || _a === void 0 ? void 0 : _a.target_id) || '', ',');
        return this;
    };
    StrategyObj.prototype.clone = function (targetType, targetValue, f) {
        var obj = new StrategyObj().init(this.type);
        obj.targetType = targetType;
        obj.targetValue = targetValue;
        obj.addFighter(f);
        return obj;
    };
    Object.defineProperty(StrategyObj.prototype, "initValue", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StrategyObj.prototype, "initParams", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.params; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StrategyObj.prototype, "magxOverlay", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.overlay; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(StrategyObj.prototype, "desc", {
        get: function () { return 'strategyText.' + this.type; },
        enumerable: false,
        configurable: true
    });
    StrategyObj.prototype.isFollowPawn = function () {
        return this.targetId[0] === 10;
    };
    StrategyObj.prototype.getBaseList = function (val) {
        if (!val) {
            return [];
        }
        var arr = [];
        for (var i = 1; i <= this.magxOverlay; i++) {
            arr.push(val * i);
        }
        return arr;
    };
    StrategyObj.prototype.getDescBaseParams = function (avatarPawnName) {
        var _a, _b, _c, _d;
        var arr = [];
        if (this.isFollowPawn()) {
            arr.push(avatarPawnName);
        }
        var valueTexts = this.getBaseList(this.initValue), valueLen = valueTexts.length;
        if (valueLen === 1) {
            arr.push(this.initValue + (((_a = this.json) === null || _a === void 0 ? void 0 : _a.v_suffix) || ''));
        }
        else if (valueLen > 1) {
            arr.push("<color=#000001>[" + valueTexts.join('/') + "]" + (((_b = this.json) === null || _b === void 0 ? void 0 : _b.v_suffix) || '') + "</c>");
        }
        var paramsTexts = this.getBaseList(this.initParams), paramsLen = paramsTexts.length;
        if (paramsLen === 1) {
            arr.push(this.initParams + (((_c = this.json) === null || _c === void 0 ? void 0 : _c.p_suffix) || ''));
        }
        else if (paramsLen > 1) {
            arr.push("<color=#000001>[" + paramsTexts.join('/') + "]" + (((_d = this.json) === null || _d === void 0 ? void 0 : _d.p_suffix) || '') + "</c>");
        }
        return arr;
    };
    // 获取说明参数
    StrategyObj.prototype.getDescParamsByPawn = function (avatarPawnName) {
        var _a, _b, _c, _d;
        var arr = [], value = this.value || this.initValue, params = this.params || this.initParams;
        if (this.isFollowPawn()) {
            arr.push(avatarPawnName);
        }
        if (!value) {
        }
        else if (this.magxOverlay <= 1) {
            arr.push(value + (((_a = this.json) === null || _a === void 0 ? void 0 : _a.v_suffix) || ''));
        }
        else {
            arr.push("<color=#000001>" + (value + (((_b = this.json) === null || _b === void 0 ? void 0 : _b.v_suffix) || '')) + "</c>");
        }
        if (!params) {
        }
        else if (this.magxOverlay <= 1) {
            arr.push(params + (((_c = this.json) === null || _c === void 0 ? void 0 : _c.p_suffix) || ''));
        }
        else {
            arr.push("<color=#000001>" + (params + (((_d = this.json) === null || _d === void 0 ? void 0 : _d.p_suffix) || '')) + "</c>");
        }
        return arr;
    };
    // 获取说明参数
    StrategyObj.prototype.getDescParamsRange = function (avatarPawnName, lv, color) {
        var _a, _b, _c, _d;
        if (color === void 0) { color = '#000001'; }
        var arr = [], value = this.initValue, params = this.initParams;
        if (this.isFollowPawn()) {
            arr.push(avatarPawnName);
        }
        if (!value) {
        }
        else if (this.magxOverlay <= 1) {
            arr.push(value + (((_a = this.json) === null || _a === void 0 ? void 0 : _a.v_suffix) || ''));
        }
        else {
            var valueTexts = this.getBaseList(this.initValue).map(function (m, i) { return (i + 1) === lv ? "<color=" + color + ">" + m + "</c>" : m; });
            arr.push("<color=#B6A591>[" + valueTexts.join('/') + "]</c>" + (((_b = this.json) === null || _b === void 0 ? void 0 : _b.v_suffix) || ''));
        }
        if (!params) {
        }
        else if (this.magxOverlay <= 1) {
            arr.push(params + (((_c = this.json) === null || _c === void 0 ? void 0 : _c.p_suffix) || ''));
        }
        else {
            var paramsTexts = this.getBaseList(this.initParams).map(function (m, i) { return (i + 1) === lv ? "<color=" + color + ">" + m + "</c>" : m; });
            arr.push("<color=#B6A591>[" + paramsTexts.join('/') + "]</c>" + (((_d = this.json) === null || _d === void 0 ? void 0 : _d.p_suffix) || ''));
        }
        return arr;
    };
    StrategyObj.prototype.addFighter = function (f) {
        if (!this.fighters.some(function (m) { return m.getUid() === f.getUid(); })) {
            this.fighters.push(f);
            this.updateCurValue();
        }
    };
    StrategyObj.prototype.removeFighter = function (uid) {
        for (var i = 0, l = this.fighters.length; i < l; i++) {
            if (this.fighters[i].getUid() === uid) {
                this.fighters.splice(i, 1);
                this.updateCurValue();
                return true;
            }
        }
        return false;
    };
    StrategyObj.prototype.updateCurValue = function () {
        var lv = Math.min(this.fighters.length, this.magxOverlay);
        this.value = this.initValue * lv;
        this.params = this.initParams * lv;
    };
    return StrategyObj;
}());
exports.default = StrategyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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