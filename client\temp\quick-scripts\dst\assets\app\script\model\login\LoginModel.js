
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/login/LoginModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b9435sewJNPELYwz5BuaKk1', 'LoginModel');
// app/script/model/login/LoginModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var version_1 = require("../../../../scene/version");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var JsbEvent_1 = require("../../common/event/JsbEvent");
var NetEvent_1 = require("../../common/event/NetEvent");
var AppleHelper_1 = require("../../common/helper/AppleHelper");
var FacebookHelper_1 = require("../../common/helper/FacebookHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GoogleHelper_1 = require("../../common/helper/GoogleHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
/**
 * 登录模块
 */
var LoginModel = /** @class */ (function (_super) {
    __extends(LoginModel, _super);
    function LoginModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.DEFAULT_MAX_RECONNECT_ATTEMPTS = 60; //最大重连次数
        _this.net = null;
        _this.user = null;
        _this.reconnectAttempts = 0;
        _this.reconnectionDelay = 5; //重连间隔（秒）
        _this._isWxAuthUserInfo = false; //微信小程序是否授权过用户信息
        _this.tempWxUsreInfo = null; //临时的微信用户信息
        _this.fcmToken = ''; //推送token
        _this.isInitAsset1 = false; //是否初始化资源
        _this.isInitAsset2 = false; //是否初始化资源
        return _this;
    }
    LoginModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
    };
    // 服务器断开连接
    LoginModel.prototype.onDisconnect = function (err) {
        this.emit(NetEvent_1.default.NET_DISCONNECT, err);
    };
    // 连接服务器
    LoginModel.prototype.connect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.net.offAll();
                        return [4 /*yield*/, this.net.connect(GameHelper_1.gameHpr.getServerUrl())];
                    case 1:
                        ok = _a.sent();
                        if (ok) {
                            this.net.on('disconnect', this.onDisconnect, this);
                        }
                        return [2 /*return*/, ok];
                }
            });
        });
    };
    // 重新连接
    LoginModel.prototype.reconnect = function () {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!GameHelper_1.gameHpr.isNoviceMode) {
                            this.net.offAll();
                        }
                        this.reconnectAttempts = 0;
                        this.reconnectionDelay = 2;
                        ok = false;
                        _a.label = 1;
                    case 1:
                        if (!(this.reconnectAttempts < this.DEFAULT_MAX_RECONNECT_ATTEMPTS)) return [3 /*break*/, 4];
                        if (this.net.isKick()) {
                            return [2 /*return*/, false];
                        }
                        return [4 /*yield*/, this.net.connect(GameHelper_1.gameHpr.getServerUrl())];
                    case 2:
                        ok = _a.sent();
                        if (ok) {
                            this.net.on('disconnect', this.onDisconnect, this);
                            return [3 /*break*/, 4];
                        }
                        return [4 /*yield*/, ut.wait(this.reconnectionDelay)];
                    case 3:
                        _a.sent();
                        this.reconnectAttempts += 1;
                        this.reconnectionDelay = Math.min(5, this.reconnectionDelay + 1); //下一次久一点
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, ok];
                }
            });
        });
    };
    // 断开网络
    LoginModel.prototype.disconnect = function () {
        this.net.off('disconnect');
        this.net.close();
    };
    // 加载游客id
    LoginModel.prototype.loadGuestId = function () {
        return __awaiter(this, void 0, void 0, function () {
            var id;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        id = storageMgr.loadString('guest_id');
                        if (!!id) return [3 /*break*/, 2];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getDeviceData('guest_id', 'slg_account')];
                    case 1:
                        id = _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/, id || ut.UID()];
                }
            });
        });
    };
    LoginModel.prototype.saveGuestId = function (id) {
        storageMgr.saveString('guest_id', id);
        JsbHelper_1.jsbHelper.saveDeviceData('guest_id', id, 'slg_account');
    };
    // 检测微信是否授权过用户信息
    LoginModel.prototype.checkAuthUserInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this;
                        return [4 /*yield*/, WxHelper_1.wxHelper.isAuthUserInfo()];
                    case 1:
                        _a._isWxAuthUserInfo = _b.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    LoginModel.prototype.isAuthUserInfo = function () {
        return this._isWxAuthUserInfo;
    };
    // 获取推送token
    LoginModel.prototype.uploadFcmToken = function () {
        return __awaiter(this, void 0, void 0, function () {
            var count, _a, err, token;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!cc.sys.isNative) {
                            return [2 /*return*/];
                        }
                        count = 0;
                        _b.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 4];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getFcmToken()];
                    case 2:
                        _a = _b.sent(), err = _a.err, token = _a.token;
                        if (!err) {
                            this.net.send('lobby/HD_UploadFcmToken', { token: token });
                            return [2 /*return*/];
                        }
                        else if (count >= 60) {
                            return [2 /*return*/];
                        }
                        count += 1;
                        return [4 /*yield*/, ut.wait(1)];
                    case 3:
                        _b.sent();
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 设置微信用户信息
    LoginModel.prototype.setWxUserInfo = function (data) {
        this.tempWxUsreInfo = data && {
            nickname: data.nickName,
            headIcon: data.avatarUrl,
        };
    };
    LoginModel.prototype.getWxUserInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.tempWxUsreInfo) return [3 /*break*/, 2];
                        return [4 /*yield*/, WxHelper_1.wxHelper.getUserInfo()];
                    case 1:
                        data = _a.sent();
                        this.setWxUserInfo(data);
                        _a.label = 2;
                    case 2: return [2 /*return*/, this.tempWxUsreInfo];
                }
            });
        });
    };
    // 获取大厅服务器是否需要排队
    LoginModel.prototype.getLobbyServerIsNeedQueueUp = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.post({ url: GameHelper_1.gameHpr.getHttpServerUrl() + '/getServerLoad' })];
                    case 1:
                        res = _b.sent();
                        return [2 /*return*/, !!((_a = res === null || res === void 0 ? void 0 : res.data) === null || _a === void 0 ? void 0 : _a.lobbyFull)];
                }
            });
        });
    };
    // 尝试登录
    LoginModel.prototype.tryLogin = function (accountToken) {
        return __awaiter(this, void 0, Promise, function () {
            var res, _a, err, data, ok, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        accountToken = accountToken || storageMgr.loadString('account_token');
                        if (!accountToken || accountToken === '0') {
                            if (accountToken !== '0') {
                                TaHelper_1.taHelper.track('ta_tutorial_v2', { tutorial_step: '0-1' }); //首个场景打开
                            }
                            return [2 /*return*/, { state: Enums_1.LoginState.NOT_ACCOUNT_TOKEN }];
                        }
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getDeviceInfo(true)];
                    case 1:
                        res = _b.sent();
                        return [4 /*yield*/, this.net.request('lobby/HD_TryLogin', {
                                accountToken: accountToken,
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                appsflyerId: (res === null || res === void 0 ? void 0 : res.appsflyer_id) || '',
                                advertisingId: (res === null || res === void 0 ? void 0 : res.adid) || '',
                                version: version_1.default.VERSION,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!(err === ECode_1.ecode.NOT_ACCOUNT_TOKEN || err === ECode_1.ecode.TOKEN_INVALID)) return [3 /*break*/, 3];
                        return [2 /*return*/, { state: Enums_1.LoginState.NOT_ACCOUNT_TOKEN }];
                    case 3:
                        if (!(err === ECode_1.ecode.VERSION_TOOLOW)) return [3 /*break*/, 4];
                        return [2 /*return*/, { state: Enums_1.LoginState.VERSION_TOOLOW, data: data }];
                    case 4:
                        if (!(err === ECode_1.ecode.CUR_LOBBY_FULL)) return [3 /*break*/, 6];
                        return [4 /*yield*/, ut.wait(0.5)];
                    case 5:
                        _b.sent();
                        return [2 /*return*/, this.tryLogin(accountToken)];
                    case 6:
                        if (!(err === ECode_1.ecode.LOBBY_QUEUE_UP)) return [3 /*break*/, 8];
                        return [4 /*yield*/, this.getLobbyServerIsNeedQueueUp()];
                    case 7:
                        ok = _b.sent();
                        if (ok) {
                            return [2 /*return*/, { state: Enums_1.LoginState.QUEUE_UP }]; //满了 需要排队
                        }
                        return [2 /*return*/, this.tryLogin(accountToken)]; //没满继续登陆
                    case 8:
                        if (err === ECode_1.ecode.BAN_ACCOUNT) { //被封禁了
                            return [2 /*return*/, { state: Enums_1.LoginState.BANACCOUNT_TIME, type: data.banAccountType, time: data.banAccountSurplusTime }];
                        }
                        else if (err) {
                            cc.log('tryLogin err!', err);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: err }];
                        }
                        _b.label = 9;
                    case 9:
                        info = data.user;
                        this.user.init(info);
                        // ta登陆
                        TaHelper_1.taHelper.login(this.user.getUid());
                        // 这里大厅服 不再返回账号token 统一使用登陆服的token 重复使用
                        storageMgr.saveString('account_token', data.accountToken || '0');
                        if (!(info === null || info === void 0 ? void 0 : info.sid)) { //表示还没有选服务器
                            return [2 /*return*/, { state: GameHelper_1.gameHpr.guide.isFirstGuideFinish() ? Enums_1.LoginState.NOT_SELECT_SERVER : Enums_1.LoginState.UNDONE_NOVICE }]; //新手村 是否完成 如果没有就去新手村
                        }
                        return [2 /*return*/, { state: Enums_1.LoginState.SUCCEED, data: { sid: info.sid } }];
                }
            });
        });
    };
    // 游客登录
    LoginModel.prototype.guestLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var guestId, ok, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        guestId = ut.getBrowserParamByKey('id');
                        if (!!guestId) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.loadGuestId()];
                    case 1:
                        guestId = _b.sent();
                        _b.label = 2;
                    case 2: return [4 /*yield*/, this.reconnect()];
                    case 3:
                        ok = _b.sent();
                        if (!ok) {
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.failed' }];
                        }
                        return [4 /*yield*/, this.net.request('login/HD_GuestLogin', {
                                guestId: guestId,
                                nickname: assetsMgr.lang('login.guest_nickname'),
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                            }, true)];
                    case 4:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.saveGuestId((data === null || data === void 0 ? void 0 : data.guestId) || '');
                        }
                        return [2 /*return*/, this.loginVerifyRet(err, data === null || data === void 0 ? void 0 : data.accountToken)];
                }
            });
        });
    };
    // 微信登录
    LoginModel.prototype.wxLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var code, userInfo, ok, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, WxHelper_1.wxHelper.getLoginCode()
                            // 获取用户信息
                        ];
                    case 1:
                        code = _b.sent();
                        return [4 /*yield*/, this.getWxUserInfo()];
                    case 2:
                        userInfo = (_b.sent()) || {};
                        return [4 /*yield*/, this.reconnect()];
                    case 3:
                        ok = _b.sent();
                        if (!ok) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.failed' }];
                        }
                        return [4 /*yield*/, this.net.request('login/HD_WxLogin', {
                                code: code,
                                nickname: userInfo.nickname || '',
                                headicon: userInfo.headIcon || '',
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                            })];
                    case 4:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        return [2 /*return*/, this.loginVerifyRet(err, data === null || data === void 0 ? void 0 : data.accountToken)];
                }
            });
        });
    };
    // facebook登陆
    LoginModel.prototype.facebookLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, ok, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, FacebookHelper_1.facebookHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.login_error' }];
                        }
                        return [4 /*yield*/, this.reconnect()];
                    case 2:
                        ok = _b.sent();
                        if (!ok) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.failed' }];
                        }
                        return [4 /*yield*/, this.net.request('login/HD_FacebookLogin', {
                                userId: loginInfo.userId,
                                token: loginInfo.token,
                                jwtToken: loginInfo.jwtToken,
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                            })];
                    case 3:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        return [2 /*return*/, this.loginVerifyRet(err, data === null || data === void 0 ? void 0 : data.accountToken)];
                }
            });
        });
    };
    // 绑定facebook账号
    LoginModel.prototype.facebookBind = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, FacebookHelper_1.facebookHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, 'login.login_error'];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_FacebookBind', {
                                userId: loginInfo.userId,
                                token: loginInfo.token,
                                jwtToken: loginInfo.jwtToken,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        if (!err) {
                            this.user.setLoginType(data.loginType);
                            this.user.setNickname(data.nickname);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 苹果登陆
    LoginModel.prototype.appleLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, ok, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, AppleHelper_1.appleHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.login_error' }];
                        }
                        return [4 /*yield*/, this.reconnect()];
                    case 2:
                        ok = _b.sent();
                        if (!ok) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.failed' }];
                        }
                        return [4 /*yield*/, this.net.request('login/HD_AppleLogin', {
                                code: loginInfo.code,
                                userId: loginInfo.userId,
                                token: loginInfo.token,
                                nickname: loginInfo.nickname,
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                            })];
                    case 3:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        return [2 /*return*/, this.loginVerifyRet(err, data === null || data === void 0 ? void 0 : data.accountToken)];
                }
            });
        });
    };
    // 绑定苹果账号
    LoginModel.prototype.appleBind = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, AppleHelper_1.appleHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, 'login.login_error'];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_AppleBind', {
                                code: loginInfo.code,
                                userId: loginInfo.userId,
                                token: loginInfo.token,
                                nickname: loginInfo.nickname,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        if (!err) {
                            this.user.setLoginType(data.loginType);
                            this.user.setNickname(data.nickname);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // google登陆
    LoginModel.prototype.googleLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, ok, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, GoogleHelper_1.googleHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.login_error' }];
                        }
                        return [4 /*yield*/, this.reconnect()];
                    case 2:
                        ok = _b.sent();
                        if (!ok) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: 'login.failed' }];
                        }
                        return [4 /*yield*/, this.net.request('login/HD_GoogleLogin', {
                                idToken: loginInfo.idToken,
                                nickname: loginInfo.nickname,
                                headicon: loginInfo.headicon,
                                distinctId: TaHelper_1.taHelper.getDistinctId(),
                                os: TaHelper_1.taHelper.getOsAndVersion(),
                                inviteUid: GameHelper_1.gameHpr.getInviteMePlayerUid(),
                                lang: mc.lang,
                                platform: GameHelper_1.gameHpr.getShopPlatform(),
                            })];
                    case 3:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        // 在ios用户使用谷歌登录之后 如果玩家的账号是新号 那需要调用
                        if (ut.isIos() && (data === null || data === void 0 ? void 0 : data.uid) && (data === null || data === void 0 ? void 0 : data.email)) {
                            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.FIREBASE_LOGIN, null, { email: data.email, uid: data.uid });
                        }
                        return [2 /*return*/, this.loginVerifyRet(err, data === null || data === void 0 ? void 0 : data.accountToken)];
                }
            });
        });
    };
    // 绑定google账号
    LoginModel.prototype.googleBind = function () {
        return __awaiter(this, void 0, void 0, function () {
            var loginInfo, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showNetWait(true);
                        return [4 /*yield*/, GoogleHelper_1.googleHelper.nativeLogin()];
                    case 1:
                        loginInfo = _b.sent();
                        if (loginInfo.errcode) {
                            ViewHelper_1.viewHelper.showNetWait(false);
                            return [2 /*return*/, 'login.login_error'];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GoogleBind', {
                                idToken: loginInfo.idToken,
                                nickname: loginInfo.nickname,
                                headicon: loginInfo.headicon,
                            })];
                    case 2:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        ViewHelper_1.viewHelper.showNetWait(false);
                        if (!err) {
                            this.user.setLoginType(data.loginType);
                            this.user.setNickname(data.nickname);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 返回登陆验证结果
    LoginModel.prototype.loginVerifyRet = function (err, accountToken) {
        if (err) {
            return { state: Enums_1.LoginState.FAILURE, err: err };
        }
        storageMgr.saveString('account_token', accountToken || '0');
        //
        return { state: Enums_1.LoginState.SUCCEED, accountToken: accountToken };
    };
    // 进入游戏
    LoginModel.prototype.enterGame = function (info, isReconnect) {
        return __awaiter(this, void 0, Promise, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_Entry', {
                            sid: info.sid,
                            distinctId: TaHelper_1.taHelper.getDistinctId(),
                            version: version_1.default.VERSION,
                            isReconnect: !!isReconnect,
                            lang: mc.lang,
                            os: TaHelper_1.taHelper.getOs(),
                            platform: GameHelper_1.gameHpr.getShopPlatform(),
                        })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.VERSION_TOOLOW) {
                            return [2 /*return*/, { state: Enums_1.LoginState.VERSION_TOOLOW, data: data }]; //提示版本过低
                        }
                        else if (err === ECode_1.ecode.VERSION_TOOTALL) {
                            return [2 /*return*/, { state: Enums_1.LoginState.VERSION_TOOTALL, err: err }]; //提示版本过高
                        }
                        else if (err === ECode_1.ecode.ROOM_CLOSE || err === ECode_1.ecode.ROOM_FULL || err === ECode_1.ecode.NOT_CITY_INDEX) {
                            return [2 /*return*/, { state: Enums_1.LoginState.NOT_SELECT_SERVER }]; //服务器已关闭 从新选服务器
                        }
                        else if (err === ECode_1.ecode.PLAYER_NOT_EXIST) {
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE }]; //登陆失败 重新连接
                        }
                        else if (err) {
                            return [2 /*return*/, { state: Enums_1.LoginState.FAILURE, err: err }];
                        }
                        return [2 /*return*/, { state: Enums_1.LoginState.SUCCEED, data: data }];
                }
            });
        });
    };
    LoginModel = __decorate([
        mc.addmodel('login')
    ], LoginModel);
    return LoginModel;
}(mc.BaseModel));
exports.default = LoginModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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