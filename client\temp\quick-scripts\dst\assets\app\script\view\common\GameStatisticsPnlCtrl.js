
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/GameStatisticsPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '69e0cBDSr1GIq09x3iHtjra', 'GameStatisticsPnlCtrl');
// app/script/view/common/GameStatisticsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var GameStatisticsPnlCtrl = /** @class */ (function (_super) {
    __extends(GameStatisticsPnlCtrl, _super);
    function GameStatisticsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    GameStatisticsPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GameStatisticsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    GameStatisticsPnlCtrl.prototype.onEnter = function (data) {
        var recruitCount = data[1001] || 0, pawnId = data[1003] || 0, pawnCount = data[1002] || 0;
        var pawnCountNode = this.rootNode_.Child('0');
        if (pawnId > 0 && pawnCount > 0) {
            pawnCountNode.setLocaleKey('ui.over_standing_desc_0', recruitCount, 'pawnText.name_' + pawnId, pawnCount, data[1004] || 0);
        }
        else {
            pawnCountNode.setLocaleKey('ui.over_standing_desc_0_1', recruitCount, data[1004] || 0);
        }
        this.rootNode_.Child('1').setLocaleKey('ui.battle_record_info_1', data[1] || 0);
        this.rootNode_.Child('1/0').setLocaleKey('ui.battle_record_info_14', data[14] || 0);
        this.rootNode_.Child('2').setLocaleKey('ui.battle_record_info_2', data[2] || 0);
        this.rootNode_.Child('3').setLocaleKey('ui.battle_record_info_3', data[3] || 0);
        this.rootNode_.Child('3/0').setLocaleKey('ui.battle_record_info_6', data[6] || 0);
        this.rootNode_.Child('4').setLocaleKey('ui.battle_record_info_4', data[4] || 0);
        this.rootNode_.Child('5').setLocaleKey('ui.battle_record_info_11', data[11] || 0);
        this.rootNode_.Child('6').setLocaleKey('ui.battle_record_info_10', data[10] || 0);
        this.rootNode_.Child('7').setLocaleKey('ui.battle_record_info_8', data[8] || 0);
        this.rootNode_.Child('7/0').setLocaleKey('ui.battle_record_info_9', data[9] || 0);
        this.rootNode_.Child('8').setLocaleKey('ui.battle_record_info_7', data[7] || 0);
    };
    GameStatisticsPnlCtrl.prototype.onRemove = function () {
    };
    GameStatisticsPnlCtrl.prototype.onClean = function () {
    };
    GameStatisticsPnlCtrl = __decorate([
        ccclass
    ], GameStatisticsPnlCtrl);
    return GameStatisticsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GameStatisticsPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcR2FtZVN0YXRpc3RpY3NQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFRLElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQW1ELHlDQUFjO0lBQWpFO1FBQUEscUVBK0NDO1FBN0NHLDBCQUEwQjtRQUNsQixlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMsZ0JBQWdCOztRQXFDbEQsaUhBQWlIO1FBQ2pILDJCQUEyQjtRQUMzQixNQUFNO1FBQ04saUhBQWlIO1FBRWpILGlIQUFpSDtJQUVySCxDQUFDO0lBM0NHLE1BQU07SUFFQywrQ0FBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLHdDQUFRLEdBQXJCOzs7Ozs7S0FDQztJQUVNLHVDQUFPLEdBQWQsVUFBZSxJQUFTO1FBQ3BCLElBQU0sWUFBWSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsU0FBUyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDM0YsSUFBTSxhQUFhLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDL0MsSUFBSSxNQUFNLEdBQUcsQ0FBQyxJQUFJLFNBQVMsR0FBRyxDQUFDLEVBQUU7WUFDN0IsYUFBYSxDQUFDLFlBQVksQ0FBQyx5QkFBeUIsRUFBRSxZQUFZLEVBQUUsZ0JBQWdCLEdBQUcsTUFBTSxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7U0FDN0g7YUFBTTtZQUNILGFBQWEsQ0FBQyxZQUFZLENBQUMsMkJBQTJCLEVBQUUsWUFBWSxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtTQUN6RjtRQUNELElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLFlBQVksQ0FBQyx5QkFBeUIsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7UUFDL0UsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLDBCQUEwQixFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUNuRixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxZQUFZLENBQUMseUJBQXlCLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQy9FLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLFlBQVksQ0FBQyx5QkFBeUIsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7UUFDL0UsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLHlCQUF5QixFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUNqRixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxZQUFZLENBQUMseUJBQXlCLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQy9FLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLFlBQVksQ0FBQywwQkFBMEIsRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7UUFDakYsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsWUFBWSxDQUFDLDBCQUEwQixFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUNqRixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxZQUFZLENBQUMseUJBQXlCLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFBO1FBQy9FLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyx5QkFBeUIsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7UUFDakYsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsWUFBWSxDQUFDLHlCQUF5QixFQUFFLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtJQUNuRixDQUFDO0lBRU0sd0NBQVEsR0FBZjtJQUNBLENBQUM7SUFFTSx1Q0FBTyxHQUFkO0lBQ0EsQ0FBQztJQXRDZ0IscUJBQXFCO1FBRHpDLE9BQU87T0FDYSxxQkFBcUIsQ0ErQ3pDO0lBQUQsNEJBQUM7Q0EvQ0QsQUErQ0MsQ0EvQ2tELEVBQUUsQ0FBQyxXQUFXLEdBK0NoRTtrQkEvQ29CLHFCQUFxQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEdhbWVTdGF0aXN0aWNzUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcclxuXHJcbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxyXG4gICAgcHJpdmF0ZSByb290Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X25cclxuICAgIC8vQGVuZFxyXG5cclxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XHJcbiAgICAgICAgcmV0dXJuIFtdXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IGFueSkge1xyXG4gICAgICAgIGNvbnN0IHJlY3J1aXRDb3VudCA9IGRhdGFbMTAwMV0gfHwgMCwgcGF3bklkID0gZGF0YVsxMDAzXSB8fCAwLCBwYXduQ291bnQgPSBkYXRhWzEwMDJdIHx8IDBcclxuICAgICAgICBjb25zdCBwYXduQ291bnROb2RlID0gdGhpcy5yb290Tm9kZV8uQ2hpbGQoJzAnKVxyXG4gICAgICAgIGlmIChwYXduSWQgPiAwICYmIHBhd25Db3VudCA+IDApIHtcclxuICAgICAgICAgICAgcGF3bkNvdW50Tm9kZS5zZXRMb2NhbGVLZXkoJ3VpLm92ZXJfc3RhbmRpbmdfZGVzY18wJywgcmVjcnVpdENvdW50LCAncGF3blRleHQubmFtZV8nICsgcGF3bklkLCBwYXduQ291bnQsIGRhdGFbMTAwNF0gfHwgMClcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBwYXduQ291bnROb2RlLnNldExvY2FsZUtleSgndWkub3Zlcl9zdGFuZGluZ19kZXNjXzBfMScsIHJlY3J1aXRDb3VudCwgZGF0YVsxMDA0XSB8fCAwKVxyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnMScpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzEnLCBkYXRhWzFdIHx8IDApXHJcbiAgICAgICAgdGhpcy5yb290Tm9kZV8uQ2hpbGQoJzEvMCcpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzE0JywgZGF0YVsxNF0gfHwgMClcclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnMicpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzInLCBkYXRhWzJdIHx8IDApXHJcbiAgICAgICAgdGhpcy5yb290Tm9kZV8uQ2hpbGQoJzMnKS5zZXRMb2NhbGVLZXkoJ3VpLmJhdHRsZV9yZWNvcmRfaW5mb18zJywgZGF0YVszXSB8fCAwKVxyXG4gICAgICAgIHRoaXMucm9vdE5vZGVfLkNoaWxkKCczLzAnKS5zZXRMb2NhbGVLZXkoJ3VpLmJhdHRsZV9yZWNvcmRfaW5mb182JywgZGF0YVs2XSB8fCAwKVxyXG4gICAgICAgIHRoaXMucm9vdE5vZGVfLkNoaWxkKCc0Jykuc2V0TG9jYWxlS2V5KCd1aS5iYXR0bGVfcmVjb3JkX2luZm9fNCcsIGRhdGFbNF0gfHwgMClcclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnNScpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzExJywgZGF0YVsxMV0gfHwgMClcclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnNicpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzEwJywgZGF0YVsxMF0gfHwgMClcclxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnNycpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzgnLCBkYXRhWzhdIHx8IDApXHJcbiAgICAgICAgdGhpcy5yb290Tm9kZV8uQ2hpbGQoJzcvMCcpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3JlY29yZF9pbmZvXzknLCBkYXRhWzldIHx8IDApXHJcbiAgICAgICAgdGhpcy5yb290Tm9kZV8uQ2hpbGQoJzgnKS5zZXRMb2NhbGVLZXkoJ3VpLmJhdHRsZV9yZWNvcmRfaW5mb183JywgZGF0YVs3XSB8fCAwKVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25DbGVhbigpIHtcclxuICAgIH1cclxuXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXHJcbiAgICAvL0BlbmRcclxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbn1cclxuIl19