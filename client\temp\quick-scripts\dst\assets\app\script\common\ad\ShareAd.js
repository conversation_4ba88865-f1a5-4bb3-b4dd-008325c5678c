
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/ad/ShareAd.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '67e2cTjTilFTrQU4RWiCZUe', 'ShareAd');
// app/script/common/ad/ShareAd.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../constant/Enums");
var AdHelper_1 = require("../helper/AdHelper");
var GameHelper_1 = require("../helper/GameHelper");
var ShareHelper_1 = require("../helper/ShareHelper");
// 分享广告
var ShareAd = /** @class */ (function () {
    function ShareAd() {
        this.lastShareTime = 0;
        this.sharedCount = 0;
        this._config = null;
    }
    Object.defineProperty(ShareAd.prototype, "config", {
        get: function () {
            if (!this._config) {
                this._config = {
                    cds: [5, 6.5, 7.2, 8, 9, 10, 11, 12, 13, 14],
                };
            }
            return this._config;
        },
        enumerable: false,
        configurable: true
    });
    ShareAd.prototype.init = function () {
        var data = storageMgr.loadJson('share_ad') || {};
        this.lastShareTime = data.lastShareTime || 0;
        this.sharedCount = data.sharedCount || 0;
        this.checkRefresh();
        return this;
    };
    ShareAd.prototype.toDB = function () {
        return {
            sharedCount: this.sharedCount,
            lastShareTime: this.lastShareTime
        };
    };
    ShareAd.prototype.show = function () {
        return __awaiter(this, void 0, void 0, function () {
            var suc;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ShareHelper_1.shareHelper.jump(Enums_1.ShareType.AD)];
                    case 1:
                        suc = _a.sent();
                        if (suc) {
                            this.sharedCount++;
                            this.lastShareTime = Date.now();
                            storageMgr.saveJson('share_ad', this.toDB());
                            AdHelper_1.adHelper.resetAdFailTime();
                        }
                        return [2 /*return*/, suc ? Enums_1.AdPlayState.SUCCESS : Enums_1.AdPlayState.SHARE_FAIL];
                }
            });
        });
    };
    ShareAd.prototype.isReady = function () {
        if (!this.config || !this.config.cds) {
            return false;
        }
        else if (this.sharedCount >= this.config.cds.length) {
            return false;
        }
        var loadFailTime = AdHelper_1.adHelper.getRewardFailTime();
        var cd = this.config.cds[this.sharedCount] * ut.Time.Minute;
        return loadFailTime >= cd;
    };
    ShareAd.prototype.checkRefresh = function () {
        var lastShareTime = this.lastShareTime;
        if (lastShareTime <= 0 || GameHelper_1.gameHpr.getToDaySurpluTime(lastShareTime) <= 0) {
            this.sharedCount = 0;
            storageMgr.saveJson('share_ad', this.toDB());
        }
    };
    ShareAd.prototype.update = function (dt) {
        this.checkRefresh();
    };
    return ShareAd;
}());
exports.default = ShareAd;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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