
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/LandScoreDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '37810ufmndG2rAbnwqc6GHj', 'LandScoreDescPnlCtrl');
// app/script/view/main/LandScoreDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var LandScoreDescPnlCtrl = /** @class */ (function (_super) {
    __extends(LandScoreDescPnlCtrl, _super);
    function LandScoreDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    LandScoreDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LandScoreDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    LandScoreDescPnlCtrl.prototype.onEnter = function (landLv) {
        var _a, _b;
        var landCountMap = GameHelper_1.gameHpr.player.getOccupyLandCountMap();
        var sum = 0;
        for (var key in landCountMap) {
            var arr_1 = landCountMap[key];
            sum += (arr_1[0] + arr_1[1]);
        }
        this.rootNode_.Child('0').setLocaleKey('ui.land_score_desc_0');
        this.rootNode_.Child('0_1').setLocaleKey('ui.land_score_desc_0_1', sum, GameHelper_1.gameHpr.player.getLandScore());
        this.rootNode_.Child('1').setLocaleKey('ui.land_score_desc_1');
        var landDescLbl = this.rootNode_.Child('2/desc', cc.Label);
        landDescLbl.setLocaleKey('ui.land_score_desc_2', landLv);
        landDescLbl._forceUpdateRenderData();
        var arr = (Constant_1.LAND_SCORE_CONF[landLv] || []).slice();
        arr.push([((_a = arr.last()) === null || _a === void 0 ? void 0 : _a[0]) || 0, 0]);
        var landDescListNode = this.rootNode_.Child('2/lay');
        landDescListNode.x = landDescLbl.node.x + landDescLbl.node.width + 12;
        landDescListNode.Items(arr, function (it, data, i) {
            var score = data[1];
            if (score > 0) {
                var pre = arr[i - 1];
                it.setLocaleKey('ui.land_score_desc_3', pre ? pre[0] + 1 : 0, data[0], data[1]);
            }
            else {
                it.setLocaleKey('ui.land_score_desc_3_0', data[0]);
            }
        });
        this.rootNode_.Child('4').setLocaleKey('ui.land_score_desc_4', ((_b = landCountMap[landLv]) === null || _b === void 0 ? void 0 : _b[1]) || 0, landLv);
    };
    LandScoreDescPnlCtrl.prototype.onRemove = function () {
    };
    LandScoreDescPnlCtrl.prototype.onClean = function () {
    };
    LandScoreDescPnlCtrl = __decorate([
        ccclass
    ], LandScoreDescPnlCtrl);
    return LandScoreDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LandScoreDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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