
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ChatPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e2e315TtNtMw4BDb3j5VTdm', 'ChatPnlCtrl');
// app/script/view/common/ChatPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var FriendInfo_1 = require("../../model/friend/FriendInfo");
var LabelAutoAnyCmpt_1 = require("../cmpt/LabelAutoAnyCmpt");
var RichTextAutoAnyCmpt_1 = require("../cmpt/RichTextAutoAnyCmpt");
var ccclass = cc._decorator.ccclass;
var ChatPnlCtrl = /** @class */ (function (_super) {
    __extends(ChatPnlCtrl, _super);
    function ChatPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.chatNode_ = null; // path://root/chat_n
        _this.worldTabsTc_ = null; // path://root/chat_n/sv/0/top/world_tabs_tc_tce
        _this.allianceTabsTc_ = null; // path://root/chat_n/sv/1/top/alliance_tabs_tc_tce
        _this.persTabsTc_ = null; // path://root/chat_n/sv/2/top/pers_tabs_tc_tce
        _this.persAddButtonsNode_ = null; // path://root/chat_n/sv/2/top/pers_add_buttons_nbe_n
        _this.bottomEditNode_ = null; // path://root/chat_n/bottom_edit_n
        _this.inputEb_ = null; // path://root/chat_n/bottom_edit_n/input_eb
        _this.sendNode_ = null; // path://root/chat_n/bottom_edit_n/send_be_n
        _this.emojiNode_ = null; // path://root/chat_n/bottom_edit_n/emoji_be_n
        _this.barrageSettingNode_ = null; // path://root/chat_n/barrage_setting_be_n
        _this.alliChannelNode_ = null; // path://root/chat_n/alli_channel_be_n
        _this.alliChannelItemsNode_ = null; // path://root/chat_n/alli_channel_be_n/mask/root/alli_channel_items_nbe_n
        _this.worldChannelNode_ = null; // path://root/chat_n/world_channel_be_n
        _this.worldChannelItemsNode_ = null; // path://root/chat_n/world_channel_be_n/mask/root/world_channel_items_nbe_n
        _this.replyBoxNode_ = null; // path://root/chat_n/reply_box_n
        _this.loadingNode_ = null; // path://loading_n
        //@end
        _this.PKEY_TAB = 'CHAT_TAB';
        _this.PKEY_PERS_TAB = 'CHAT_PERS_TAB';
        _this.user = null;
        _this.chat = null;
        _this.lobby = null;
        _this.team = null;
        _this.friend = null;
        _this.type = 0;
        _this.childType = 0;
        _this.targetChatInfo = null; // FriendInfo  PChatChannelInfo
        _this.chatSv = null;
        _this.chatItem = null;
        _this.persChatItem = null;
        _this.tempChatNodeMap = {};
        _this.curFirstChatUid = '';
        _this.pchatCloseProgressTween = null;
        return _this;
    }
    ChatPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        return [
            (_a = {}, _a[EventType_1.default.ADD_CHAT] = this.onAddCaht, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.CHANGE_CHAT_CHANNEL] = this.onChangeChatChannel, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_SEND_CHAT_BUTTON] = this.onUpdateSendChatButton, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_ALLI_CHAT_CHANNEL] = this.onUpdateAlliChatChannel, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.TRANSLATE_TEXT_COMPLETE] = this.onTranslateTextComplete, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_ALLI_CHANEEL_MEMEBERS] = this.onUpdateAlliTopInfo, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_PCHAT_CHANNEL] = this.onUpdatePChatChannel, _g.enter = true, _g),
            (_h = {}, _h[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.ADD_LOBBY_CHAT] = this.onAddCaht, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_TEAM_LIST] = this.onUpdateTeamList, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.ADD_FRIEND_CHAT] = this.onAddFriendCaht, _l.enter = true, _l),
        ];
    };
    ChatPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.chat = this.getModel('chat');
                this.lobby = this.getModel('lobby');
                this.team = this.getModel('team');
                this.friend = this.getModel('friend');
                this.chatItem = this.chatNode_.FindChild('item');
                this.chatItem.active = true;
                this.chatItem.parent = null;
                this.persChatItem = this.chatNode_.FindChild('pers_item');
                this.persChatItem.active = true;
                this.persChatItem.parent = null;
                return [2 /*return*/];
            });
        });
    };
    ChatPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.targetChatInfo = data === null || data === void 0 ? void 0 : data.target;
        var type = (data === null || data === void 0 ? void 0 : data.target) ? 2 : (_a = data === null || data === void 0 ? void 0 : data.tab) !== null && _a !== void 0 ? _a : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0);
        this.tabsTc_.Tabs(type);
        if (data === null || data === void 0 ? void 0 : data.text) {
            this.inputEb_.string += data.text;
            this.inputEb_.focus();
        }
        this.initPopupBoxList();
    };
    ChatPnlCtrl.prototype.onRemove = function () {
        var _a;
        this.chat.setCurLookChannel('');
        this.lobby.setCurLookChannel('');
        (_a = this.pchatCloseProgressTween) === null || _a === void 0 ? void 0 : _a.stop();
        this.pchatCloseProgressTween = null;
        this.tempChatNodeMap = {};
        this.chatNode_.Child('sv').children.forEach(function (m) {
            if (m.name === '2') {
                var page = m.Child('page');
                for (var i = 0; i < page.children.length; i++) {
                    var sv = page.Child(i + '/list', cc.ScrollView);
                    sv.content.children.forEach(function (n) {
                        n.Child('root').active = false;
                        n.Child('time').active = false;
                    });
                }
            }
            else {
                m.Child('list', cc.ScrollView).content.children.forEach(function (n) {
                    n.Child('root').active = false;
                    n.Child('time').active = false;
                });
            }
        });
        this.alliChannelNode_.Data = null;
        this.worldChannelNode_.Data = null;
    };
    ChatPnlCtrl.prototype.onClean = function () {
        ut.destroyNode(this.chatItem);
        this.chatItem = null;
        ut.destroyNode(this.persChatItem);
        this.persChatItem = null;
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    ChatPnlCtrl.prototype.onClickTabs = function (event, data) {
        var _a;
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name), isAlliChannel = type === 1, isWorldChannel = type === 0, isPersChannel = type === 2;
        this.type = type;
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.chatNode_.Child('sv').Swih(type)[0];
        this.chatSv = node.Child('list', cc.ScrollView);
        this.setLoading(false);
        if (this.worldChannelNode_.active = isWorldChannel) {
            this.bottomEditNode_.active = true;
            var worldTab = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_TAB) || 0;
            this.worldTabsTc_.Tabs(worldTab);
            !this.worldChannelNode_.Data && this.updateWorldChannels(true);
            this.updateSendButton();
        }
        else if (isAlliChannel) {
            this.bottomEditNode_.active = true;
            var alliTab = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.ALLI_CHAT_TAB) || 0;
            this.allianceTabsTc_.Tabs(alliTab);
        }
        else if (isPersChannel) {
            this.alliChannelNode_.active = false;
            this.bottomEditNode_.active = false;
            this.barrageSettingNode_.active = false;
            var persTab = ((_a = this.targetChatInfo) === null || _a === void 0 ? void 0 : _a.channel) ? 1 : (this.user.getTempPreferenceMap(this.PKEY_PERS_TAB) || 0);
            this.persTabsTc_.Tabs(persTab);
        }
        this.closeReply();
        this.updateSpecialBarrageSetting(type);
    };
    // path://root/chat_n/sv/0/top/world_tabs_tc_tce
    ChatPnlCtrl.prototype.onClickWorldTabs = function (event, data) {
        var type = Number(event.node.name);
        this.childType = type;
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_TAB, type);
        if (!this.tempChatNodeMap[this.type + '_' + type]) {
            this.setChatItemActive();
        }
        this.closeReply();
        this.updateWorldChannels(true);
    };
    // path://root/chat_n/sv/1/top/alliance_tabs_tc_tce
    ChatPnlCtrl.prototype.onClickAllianceTabs = function (event, data) {
        var type = Number(event.node.name), tip = '';
        if (type === 0 && !this.chat.isCanSendAlli()) {
            tip = 'toast.please_join_alli';
        }
        else if (type === 1) {
            tip = this.team.hasTeam() ? '' : this.team.isInGame() ? 'ui.not_have_team' : 'toast.please_join_team';
        }
        this.childType = type;
        var isAlli = type === 0;
        this.alliChannelNode_.active = isAlli;
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.ALLI_CHAT_TAB, type);
        this.updateAlliTopInfo();
        if (!this.tempChatNodeMap[this.type + '_' + type]) {
            this.setChatItemActive();
        }
        this.closeReply();
        if (isAlli) {
            this.updateAlliChannels(true, tip);
        }
        else {
            this.updateChatLists(!!data, tip);
        }
        this.updateSendButton(!tip);
    };
    // path://root/chat_n/sv/2/top/pers_tabs_tc_tce
    ChatPnlCtrl.prototype.onClickPersTabs = function (event, data) {
        var type = Number(event.node.name);
        this.childType = type;
        this.persAddButtonsNode_.Swih(type);
        this.user.setTempPreferenceData(this.PKEY_PERS_TAB, type);
        this.chatSv = type === 0 ? this.chatNode_.Child('sv/2/page/0/list', cc.ScrollView) : this.chatNode_.Child('sv/2/page/1/list', cc.ScrollView);
        if (!this.tempChatNodeMap[this.type + '_' + type]) {
            this.setChatItemActive();
        }
        if (this.targetChatInfo) { // 直接进入指定对话
            if (this.targetChatInfo.channel) {
                this.chat.setCurPChatChannel(this.targetChatInfo.channel);
                this.updatePChatsList();
            }
            else {
                this.enterFriendChat(this.targetChatInfo);
            }
        }
        else {
            if (type === 0) {
                this.updateFriendsList();
            }
            else {
                this.updatePChatsList();
            }
        }
    };
    // path://root/chat_n/bottom_edit_n/send_be_n
    ChatPnlCtrl.prototype.onClickSend = function (event, data) {
        var isFriend = this.isFriendChat();
        var content = this.inputEb_.string.trim();
        if (!content) {
            return;
        }
        else if (ut.getStringLen(content) > 120) {
            return ViewHelper_1.viewHelper.showAlert('toast.send_chat_content_toolong');
        }
        else if (!isFriend && GameHelper_1.gameHpr.getTextNewlineCount(content) > 4) {
            return ViewHelper_1.viewHelper.showAlert('toast.send_chat_content_toolong');
        }
        if (isFriend) {
            var uid = this.friend.getCurrLookChatFriendUID();
            var ok = this.friend.sendChat(uid, content);
            if (ok === 1) {
                return this.updateSendButton();
            }
            else if (ok === 0) {
                this.inputEb_.string = '';
                this.updateFriendsChatList();
            }
        }
        else {
            var ok = 0;
            if (this.isLobbyChat()) {
                ok = this.lobby.sendChat(this.type, content, { replyChatInfo: this.replyBoxNode_.Data });
            }
            else {
                ok = this.chat.sendChat(this.type, this.childType, content, { replyChatInfo: this.replyBoxNode_.Data });
            }
            if (ok === 1) {
                return this.updateSendButton();
            }
            else if (ok !== 0) {
                return;
            }
            this.inputEb_.string = '';
            if (this.chatSv) {
                this.closeReply();
                this.updateChatList();
            }
        }
    };
    // path://root/chat_n/item/root/head_be
    ChatPnlCtrl.prototype.onClickHead = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.parent.parent.Data;
        if (this.isFriendChat()) { // 好友
            var friend = null;
            var isOwner = data.sender === GameHelper_1.gameHpr.getUid(), user = this.user;
            if (isOwner) {
                friend = new FriendInfo_1.default().init({
                    uid: data.sender,
                    nickname: user.getNickname(),
                    headIcon: user.getHeadIcon(),
                    playSid: user.getPlaySid(),
                });
            }
            else {
                friend = GameHelper_1.gameHpr.friend.getFriends().find(function (m) { return m.uid === data.sender; });
            }
            friend && ViewHelper_1.viewHelper.showPnl('menu/FriendInfo', friend, 'chat');
        }
        else {
            var info = GameHelper_1.gameHpr.getPlayerInfo(data === null || data === void 0 ? void 0 : data.sender);
            if (info && !info.spectateIndex && !this.isLobbyChat()) { // 不是大厅聊天
                ViewHelper_1.viewHelper.showPnl('common/PlayerInfo', info, 'chat');
            }
            else if ((data === null || data === void 0 ? void 0 : data.sender) && (!!(info === null || info === void 0 ? void 0 : info.spectateIndex) || !!(data === null || data === void 0 ? void 0 : data.senderNickname))) {
                ViewHelper_1.viewHelper.showPnl('lobby/UserInfo', GameHelper_1.gameHpr.lobby.getUser(data.sender).set({ nickname: data.senderNickname, headIcon: data.senderHeadicon }), 'chat');
            }
            else {
                ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_NOT_EXIST);
            }
        }
    };
    // path://root/chat_n/bottom_edit_n/emoji_be_n
    ChatPnlCtrl.prototype.onClickEmoji = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var isFriend = this.isFriendChat(), isLobby = this.isLobbyChat();
        ViewHelper_1.viewHelper.showPnl('common/ChatSelectEmoji', function (id) {
            if (!_this.isValid) {
                return;
            }
            var ok = isFriend ? _this.friend.sendChat(_this.friend.getCurrLookChatFriendUID(), '', { emoji: id }) :
                isLobby ? _this.lobby.sendChat(_this.type, '', { emoji: id, replyChatInfo: _this.replyBoxNode_.Data }) :
                    _this.chat.sendChat(_this.type, _this.childType, '', { emoji: id, replyChatInfo: _this.replyBoxNode_.Data });
            if (ok === 1) {
                return _this.updateSendButton();
            }
            else if (ok !== 0) {
            }
            else if (_this.chatSv) {
                _this.closeReply();
                _this.updateChatLists();
            }
        });
    };
    // path://root/chat_n/barrage_setting_be_n
    ChatPnlCtrl.prototype.onClickBarrageSetting = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/chat_n/barrage_setting_be_n/select_mask_be
    ChatPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/chat_n/barrage_setting_be_n/mask/root/setting_items_nbe
    ChatPnlCtrl.prototype.onClickSettingItems = function (event, data) {
        var node = this.barrageSettingNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var _a = GameHelper_1.gameHpr.getChatChannelBarrageSettingInfo(this.type), key = _a.key, getType = _a.getType;
        var type = Number(event.target.name);
        if (type !== getType) {
            if (this.type) {
                this.user.setLocalPreferenceDataBySid(key, type);
            }
            else {
                this.user.setLocalPreferenceData(key, type);
            }
            this.updateBarrageSetting(type);
            this.emit(EventType_1.default.CHANGE_BARRAGE_AREA, this.type, type);
        }
    };
    // path://root/chat_n/item/root/content/text/translate_be
    ChatPnlCtrl.prototype.onClickTranslate = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (!data) {
            return;
        }
        var isFriend = this.isFriendChat();
        var chatInfo = GameHelper_1.gameHpr.translateText(data.data, isFriend ? 'friend' : 'chat');
        this.updateChatItem(data.node, chatInfo);
    };
    // path://root/chat_n/bottom_edit_n/trumpet_be
    ChatPnlCtrl.prototype.onClickTrumpet = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/SendTrumpet');
    };
    // path://root/chat_n/alli_channel_be_n
    ChatPnlCtrl.prototype.onClickAlliChannel = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/chat_n/alli_channel_be_n/mask/root/alli_channel_items_nbe_n
    ChatPnlCtrl.prototype.onClickAlliChannelItems = function (event, _) {
        var _this = this;
        var _a;
        var node = this.alliChannelNode_;
        var data = event.target.Data;
        if (!data) {
            if (GameHelper_1.gameHpr.alliance.getChatChannels().length >= 5) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ALLI_CHAT_CHANNEL_LIMIT);
            }
            return ViewHelper_1.viewHelper.showPnl('common/CreateAlliChannel', function (res) {
                if (res && _this.isValid) {
                    ViewHelper_1.viewHelper.changePopupBoxList(node, false);
                    _this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL, res.info.uid);
                    _this.changeAlliChannel(res.info);
                }
            });
        }
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var oldChannel = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        if (data.uid !== oldChannel) {
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL, data.uid);
            this.changeAlliChannel(data);
        }
    };
    // path://root/chat_n/alli_channel_be_n/mask/root/alli_channel_items_nbe_n/item/remove_alli_channel_be
    ChatPnlCtrl.prototype.onClickRemoveAlliChannel = function (event, _) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!(data === null || data === void 0 ? void 0 : data.uid) || data.uid === '0') {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.remove_alli_channel_tip', {
            params: [data.name],
            ok: function () { return GameHelper_1.gameHpr.alliance.removeChatChannel(data.uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    ViewHelper_1.viewHelper.changePopupBoxList(_this.alliChannelNode_, false);
                }
            }); },
            cancel: function () { },
        });
    };
    // path://root/chat_n/world_channel_be_n
    ChatPnlCtrl.prototype.onClickWorldChannel = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/chat_n/world_channel_be_n/mask/root/world_channel_items_nbe_n
    ChatPnlCtrl.prototype.onClickWorldChannelItems = function (event, _data) {
        var _a;
        var node = this.worldChannelNode_;
        var data = event.target.Data;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var oldChannel = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        if (data.uid !== oldChannel) {
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, data.uid);
            this.changeWorldChannel(data);
        }
    };
    // path://root/chat_n/sv/1/top/alli_online_be
    ChatPnlCtrl.prototype.onClickAlliOnline = function (event, data) {
        if (this.childType === 0) {
            ViewHelper_1.viewHelper.showPnl('common/AlliChannelMember');
        }
        else {
            ViewHelper_1.viewHelper.showPnl('lobby/TeamList', false);
        }
    };
    // path://root/chat_n/item/root/content/text/reply_be
    ChatPnlCtrl.prototype.onClickReply = function (event, _) {
        audioMgr.playSFX('click');
        var info = event.target.parent.parent.Data;
        if (info) {
            var data = info.data;
            this.replyBoxNode_.active = true;
            this.replyBoxNode_.Data = data;
            var name = data.senderNickname || GameHelper_1.gameHpr.getPlayerName(data.sender);
            var content = name + ': ' + data.content;
            this.replyBoxNode_.Child('val', cc.Label).string = ut.nameFormator(assetsMgr.lang('ui.chat_reply', content), 20);
            this.inputEb_.focus();
        }
    };
    // path://root/chat_n/reply_box_n/close_reply_be
    ChatPnlCtrl.prototype.onClickCloseReply = function (event, data) {
        this.closeReply();
    };
    // path://root/chat_n/item/root/content/reply_text_be
    ChatPnlCtrl.prototype.onClickReplyText = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var uid = event.target.Data;
        if (uid) {
            var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid; });
            if (it) {
                var di = it.Child('root/di');
                var content = this.chatSv.content;
                var viewHeight = this.chatSv.node.height;
                var maxScrollOffset = content.height - viewHeight;
                var showTime = !!((_a = it.Data) === null || _a === void 0 ? void 0 : _a.showTime);
                var itemHeight = showTime ? it.height - 40 : it.height;
                if (maxScrollOffset > 0 && it.y > viewHeight) {
                    // 计算 item 顶部到 content 底部的距离
                    var itemTop = it.y - (showTime ? 40 : 0) - itemHeight * 0.5;
                    // 以居中为例
                    var desiredOffset = itemTop - viewHeight / 2;
                    var clampedOffset = cc.misc.clampf(desiredOffset, 0, maxScrollOffset);
                    var normalizedY = clampedOffset / maxScrollOffset;
                    this.chatSv.scrollTo(cc.v2(0, normalizedY), 0.1);
                }
                di.height = itemHeight + 4;
                di.opacity = 80;
                cc.tween(di)
                    .delay(0.4)
                    .to(0.5, { opacity: 0 }, { easing: cc.easing.sineOut })
                    .start();
            }
            else {
                ViewHelper_1.viewHelper.showAlert('toast.reply_chat_info_expire');
            }
        }
    };
    // path://root/chat_n/sv/2/top/pers_add_buttons_nbe_n
    ChatPnlCtrl.prototype.onClickPersAddButtons = function (event, data) {
        var _this = this;
        var name = event.target.name;
        if (name === '0') { // 添加好友
            ViewHelper_1.viewHelper.showPnl('menu/Personal', 1);
        }
        else if (name === '1') { // 添加私聊
            if (!GameHelper_1.gameHpr.checkAddPChatCond()) {
                return;
            }
            ViewHelper_1.viewHelper.showPnl('common/AddPChat', function (uid) {
                if (uid && _this.isValid) {
                    _this.onChangeChatChannel(_this.chat.addPChatByMe(uid));
                }
            });
        }
    };
    // path://root/chat_n/sv/2/page/0/friends/view/content/friend_chat_be
    ChatPnlCtrl.prototype.onClickFriendChat = function (event, _data) {
        var data = event.target.Data;
        this.enterFriendChat(data);
    };
    // path://root/chat_n/sv/2/page/0/chats/top/back/back_list_be
    ChatPnlCtrl.prototype.onClickBackList = function (event, data) {
        this.backToFriendsList();
    };
    // path://root/chat_n/sv/2/page/1/players/view/content/pchat_be
    ChatPnlCtrl.prototype.onClickPchat = function (event, _data) {
        var node = event.target, data = node.Data, parent = node.parent;
        parent.children.forEach(function (m) {
            if (m.Data) {
                m.opacity = 120;
                m.Child('layout/close_pchat_be').active = false;
                m.Child('layout/name', cc.Label).string = ut.nameFormator(m.Data.name || '???', 7);
            }
        });
        this.chat.setCurPChatChannel(data.channel);
        this.selectPChatItem(node, data);
    };
    // path://root/chat_n/sv/2/page/1/players/view/content/pchat_be/close_pchat_be
    ChatPnlCtrl.prototype.onClickClosePchat = function (event, _data) {
        var _this = this;
        var data = this.chat.getCurPChatChannelData();
        if (!data) {
            return;
        }
        else if (GameHelper_1.gameHpr.getMaxLandCountByPChat() < Constant_1.NOLIMIT_PCHAT_MAX_LAND) {
            var stime = Math.max(0, data.canCloseTime - (Date.now() - data.getTime));
            if (stime > 0) { //需要12小时之后才能删除
                return ViewHelper_1.viewHelper.showMessageBox('ui.close_pchat_tip', {
                    params: [GameHelper_1.gameHpr.millisecondToString(stime)]
                });
            }
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.remove_pchat_tip', {
            params: ["<fontFamily=Arial>" + ut.nameFormator(data.name, 7) + "</>"],
            ok: function () { return _this.isValid && _this.removePChat(data.channel); },
            cancel: function () { }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ChatPnlCtrl.prototype.onNetReconnect = function () {
        this.updateChatLists();
    };
    ChatPnlCtrl.prototype.onUpdateTeamList = function () {
        if (this.type === 1 && this.childType === 1 && !this.team.hasTeam()) {
            this.tabsTc_.Tabs(0);
        }
    };
    // 添加聊天信息
    ChatPnlCtrl.prototype.onAddCaht = function (data) {
        var _a, _b;
        if (!this.isValid) {
            return;
        }
        else if (data.channel !== (this.isLobbyChat() ? (_a = this.lobby) === null || _a === void 0 ? void 0 : _a.getCurLookChannel() : (_b = this.chat) === null || _b === void 0 ? void 0 : _b.getCurLookChannel())) {
            return;
        }
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateChatItem(it, data);
        }
        else {
            this.updateChatLists();
        }
    };
    // 添加聊天信息
    ChatPnlCtrl.prototype.onAddFriendCaht = function (data) {
        if (!this.isFriendChat() || data.uid !== this.friend.getCurrLookChatFriendUID()) {
            return;
        }
        var chat = data.chat;
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === chat.uid; });
        if (it) {
            this.updateFriendChatItem(it, chat);
        }
        else {
            this.updateFriendsChatList();
        }
    };
    // 切换聊天频道
    ChatPnlCtrl.prototype.onChangeChatChannel = function (data) {
        if (data.channel === this.chat.getCurLookChannel()) {
            return;
        }
        var type = Number(data.channel.split('_')[0]);
        if (this.type !== type) {
            if (type === 2 && data.pchat) {
                this.targetChatInfo = data.pchat;
            }
            return this.tabsTc_.Tabs(type);
        }
        else if (type === 2) {
            this.chat.setCurPChatChannel(data.pchat.channel);
            this.updatePChatsList();
        }
    };
    ChatPnlCtrl.prototype.onUpdateSendChatButton = function () {
        this.updateSendButton();
    };
    // 刷新联盟频道
    ChatPnlCtrl.prototype.onUpdateAlliChatChannel = function () {
        this.updateAlliChannels();
    };
    // 翻译完成
    ChatPnlCtrl.prototype.onTranslateTextComplete = function (type, data) {
        if (type !== 'chat') {
            return;
        }
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateChatItem(it, data);
        }
    };
    // 联盟频道顶部信息
    ChatPnlCtrl.prototype.onUpdateAlliTopInfo = function (data) {
        this.updateAlliTopInfo();
    };
    // 刷新私聊频道
    ChatPnlCtrl.prototype.onUpdatePChatChannel = function () {
        this.updatePChatsList();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ChatPnlCtrl.prototype.initPopupBoxList = function () {
        this.closeReply();
        ViewHelper_1.viewHelper.changePopupBoxList(this.barrageSettingNode_, false);
        ViewHelper_1.viewHelper.changePopupBoxList(this.worldChannelNode_, false);
        ViewHelper_1.viewHelper.changePopupBoxList(this.alliChannelNode_, false);
    };
    ChatPnlCtrl.prototype.updateChatLists = function (init, emptyTip) {
        if (init === void 0) { init = false; }
        if (emptyTip === void 0) { emptyTip = ''; }
        if (this.isFriendChat()) {
            this.updateFriendsChatList(init);
        }
        else {
            this.updateChatList(init, emptyTip);
        }
    };
    // 刷新聊天列表
    ChatPnlCtrl.prototype.updateChatList = function (init, emptyTip) {
        var _a, _b;
        if (init === void 0) { init = false; }
        if (emptyTip === void 0) { emptyTip = ''; }
        return __awaiter(this, void 0, void 0, function () {
            var sv, isLobby, channel, list, count, firstUid, tempMap, height, y, emptyNode_;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        sv = this.chatSv;
                        isLobby = this.isLobbyChat();
                        channel = isLobby ? this.lobby.getChannelByType(this.type) : this.chat.getChannelByType(this.type);
                        list = [];
                        if (!isLobby) return [3 /*break*/, 2];
                        this.lobby.setCurLookChannel(channel);
                        return [4 /*yield*/, this.lobby.getChatsByChannel(channel)];
                    case 1:
                        list = _c.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        this.chat.setCurLookChannel(channel);
                        list = this.chat.getChatsByChannel(channel);
                        _c.label = 3;
                    case 3:
                        if (!list) {
                            return [2 /*return*/];
                        }
                        count = list.length - 1;
                        GameHelper_1.gameHpr.updateChatAllTime(list);
                        firstUid = ((_a = list[0]) === null || _a === void 0 ? void 0 : _a.uid) || '', tempMap = {};
                        if (!this.curFirstChatUid) {
                            this.curFirstChatUid = firstUid;
                        }
                        if (this.curFirstChatUid === firstUid) {
                            (_b = this.tempChatNodeMap[this.type + '_' + this.childType]) === null || _b === void 0 ? void 0 : _b.forEach(function (m) {
                                var _a;
                                if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                                    tempMap[m.Data.uid] = true;
                                }
                            });
                        }
                        else {
                            this.tempChatNodeMap[this.type + '_' + this.childType] = null;
                        }
                        sv.stopAutoScroll();
                        height = sv.content.height, y = sv.content.y;
                        sv.Items(list, this.chatItem, function (it, data, i) {
                            if (!tempMap[data.uid]) {
                                _this.updateChatItem(it, data);
                            }
                            if (i === count) {
                                sv.Component(cc.ScrollViewPlus).isFrameRender = false;
                                _this.updateScrollViewContent(sv, height, y);
                            }
                        });
                        emptyNode_ = sv.node.Child('empty');
                        if (emptyNode_ && this.type !== 2) {
                            emptyNode_.active = !!emptyTip; /* || !list.length */
                            emptyNode_.Child('val').setLocaleKey(emptyTip);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    ChatPnlCtrl.prototype.updateChatItem = function (it, data) {
        if (this.isFriendChat()) {
            this.updateFriendChatItem(it, data);
        }
        else {
            this.updateItem(it, data);
        }
    };
    ChatPnlCtrl.prototype.updateItem = function (it, data) {
        var _a, _b, _c, _d;
        it.Data = data;
        var timeLbl = it.Child('time', cc.Label);
        if (timeLbl.setActive(!!data.showTime)) {
            timeLbl.setLocaleKey('ui.text', data.showTime);
        }
        var height = data.showTime ? 40 : 0;
        var root = it.Child('root'), system = it.Child('system');
        if (system.active = data.sender === '-1' && !!((_a = data.params) === null || _a === void 0 ? void 0 : _a.length)) {
            root.active = false;
            system.Component(cc.RichText).string = assetsMgr.lang.apply(assetsMgr, __spread([data.params[0]], data.params.slice(1)));
            system.y = data.showTime ? -56 : -16;
            it.height = height + 32 + 12;
            return;
        }
        var isMe = GameHelper_1.gameHpr.getUid() === data.sender;
        root.active = true;
        root.y = data.showTime ? -40 : 0;
        root.Child('head_be').x = isMe ? 278 : -278;
        var name = root.Child('name'), content = root.Child('content');
        var reply = content.Child('reply_text_be'), textRt = content.Child('text', cc.RichText), translateLbl = content.Child('translate', cc.Label);
        var lineNode = content.Child('line'), translateLoading = content.Child('loading'), emoji = content.Child('emoji');
        name.anchorX = content.anchorX = isMe ? 1 : 0;
        name.x = content.x = isMe ? 240 : -240;
        emoji.x = isMe ? -50 : 50;
        var player = GameHelper_1.gameHpr.getPlayerInfo(data.sender);
        var nickname = data.senderNickname || (player === null || player === void 0 ? void 0 : player.nickname);
        var headIcon = data.senderHeadicon || (player === null || player === void 0 ? void 0 : player.headIcon);
        var title = data.senderTitle || (player === null || player === void 0 ? void 0 : player.title);
        ResHelper_1.resHelper.loadPlayerHead(root.Child('head_be/val', cc.Sprite), headIcon, this.key);
        var nameLbl = root.Child('name', cc.Label);
        nameLbl.string = ut.nameFormator(nickname || '???', 12);
        nameLbl._forceUpdateRenderData();
        var _e = __read(data.channel.split('_'), 1), channelType = _e[0], job = Enums_1.AllianceJobType.MEMBER;
        if (channelType === '1' || channelType === '4') {
            job = GameHelper_1.gameHpr.alliance.getMemberJob(data.sender);
        }
        this.updateTtile(it, title || 0, name.x, nameLbl.node.width * 0.5, name.anchorX, job, !!(player === null || player === void 0 ? void 0 : player.spectateIndex) || !!data.senderNickname);
        var hasEmoji = !!data.emoji && !data.bannedSurplusTime;
        textRt.node.active = translateLbl.node.active = lineNode.active = translateLoading.active = !hasEmoji;
        var textWidth = 0;
        if (emoji.active = hasEmoji) { //是表情
            content.Data = { type: 'emoji', node: it, data: data };
            var node = emoji.Swih(data.wait ? 'loading' : 'root')[0];
            if (!data.wait) {
                var scale = ((_b = assetsMgr.getJsonData('chatEmoji', data.emoji)) === null || _b === void 0 ? void 0 : _b.scale) || 0;
                if (scale && !isMe) {
                    scale *= -1;
                }
                ResHelper_1.resHelper.loadEmojiNode(data.emoji, node, scale, this.key, true);
            }
        }
        else {
            emoji.Child('root').removeAllChildren();
            var translateButton = content.Child('text/translate_be'), replyButton = content.Child('text/reply_be');
            translateLbl.node.anchorX = textRt.node.anchorX = content.anchorX;
            translateLbl.node.x = textRt.node.x = 0;
            var isBanned = data.bannedSurplusTime > 0, isTranslate = !isMe && !data.translate && !!data.content && GameHelper_1.gameHpr.isGLobal();
            if (isBanned) {
                translateButton.active = false;
                content.Data = { type: 'banned', node: it, data: data };
                textRt.string = this.wColor('#B6A591', assetsMgr.lang('ui.banned_chat_desc', GameHelper_1.gameHpr.millisecondToStringForDay(data.bannedSurplusTime)));
            }
            else if (data.equip) { //是否装备
                translateButton.active = false;
                content.Data = { type: 'equip', data: data.equip };
                textRt.string = this.wClickItem('#49983C', data.equip.getChatName());
            }
            else if (data.portrayal) { //是否画像
                translateButton.active = false;
                content.Data = { type: 'portrayal', data: data.portrayal };
                textRt.string = this.wClickItem(data.portrayal.getChatNameColor(), data.portrayal.getChatName());
            }
            else if (data.battleInfo) { //是否是战报
                translateButton.active = false;
                content.Data = { type: 'battleInfo', data: data.battleInfo };
                textRt.string = this.wClickItem('#49983C', assetsMgr.lang('ui.title_army_battle_record') + "(" + MapHelper_1.mapHelper.indexToPoint(data.battleInfo.index).Join() + ")");
            }
            else {
                translateButton.active = isTranslate;
                content.Data = { type: 'text', node: it, data: data };
                textRt.string = data.pointText || data.content;
            }
            textRt.node.opacity = !isBanned && data.wait ? 120 : 255;
            textWidth = textRt.Component(RichTextAutoAnyCmpt_1.default).check();
            lineNode.active = !!data.translate;
            translateLoading.active = !!((_c = data.translate) === null || _c === void 0 ? void 0 : _c.req);
            if (translateLbl.setActive(!!((_d = data.translate) === null || _d === void 0 ? void 0 : _d.text))) {
                translateLbl.string = data.translate.text;
                lineNode.Child('val').width = translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
            }
            else {
                lineNode.Child('val').width = textWidth;
            }
            // 文本后面的按钮
            if (replyButton.active = !isMe && !isBanned && this.type !== 2) { // 单对单不需要回复
                // replyButton.x = textRt.node.width + replyButton.width * 0.5 + 4
            }
            if (translateButton.active) {
                translateButton.x = textRt.node.width + translateButton.width * 0.5;
            }
        }
        if (reply.active = !!data.replyInfo) {
            reply.Data = data.replyInfo.uid;
            var lbl = reply.Child('val', cc.Label);
            lbl.string = ut.nameFormator(assetsMgr.lang('ui.chat_reply', data.replyInfo.text), 24);
            lbl._forceUpdateRenderData();
            var replyTextWidth = lbl.node.width * lbl.node.scaleX + 8;
            if (isMe) {
                reply.width = Math.max(replyTextWidth, textWidth);
                reply.x = -reply.width;
            }
            else {
                reply.width = replyTextWidth;
                reply.x = 0;
            }
        }
        content.Component(cc.Layout).updateLayout();
        height += Math.max(60, Math.ceil(content.height - content.y + 4));
        it.height = height;
    };
    // 包装颜色
    ChatPnlCtrl.prototype.wColor = function (color, text) {
        return "<color=" + color + ">" + text + "</color>";
    };
    ChatPnlCtrl.prototype.wClickItem = function (color, text) {
        return this.wColor(color, "<on click=\"onClickItem\">[" + text + "]</on>");
    };
    // 显示称号
    ChatPnlCtrl.prototype.updateTtile = function (it, id, x, width, anchorX, job, isSpectate) {
        var json = assetsMgr.getJsonData('title', id);
        var jobLbl = it.Child('root/job', cc.Label), titleLbl = it.Child('root/title', cc.Label), spectator = it.Child('root/spectator');
        var offsetX = x, offsetWidth = width;
        if (jobLbl.setActive(job !== Enums_1.AllianceJobType.MEMBER)) {
            jobLbl.setLocaleKey('ui.angle_bracket', 'ui.alliance_job_' + job);
            jobLbl.node.anchorX = anchorX;
            offsetX = jobLbl.node.x = offsetX + (offsetWidth + 4) * (anchorX ? -1 : 1);
            jobLbl._forceUpdateRenderData();
            offsetWidth = jobLbl.node.width;
        }
        if (titleLbl.setActive(!!json && job === Enums_1.AllianceJobType.MEMBER)) {
            titleLbl.Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey('ui.bracket', 'titleText.' + json.id);
            titleLbl.node.anchorX = anchorX;
            offsetX = titleLbl.node.x = offsetX + (offsetWidth + 8) * (anchorX ? -1 : 1);
            titleLbl._forceUpdateRenderData();
            offsetWidth = titleLbl.node.width;
        }
        if (spectator.active = isSpectate && !this.isLobbyChat() && this.type !== 2) {
            spectator.anchorX = anchorX;
            spectator.x = offsetX + (offsetWidth + 8) * (anchorX ? -1 : 1);
        }
    };
    ChatPnlCtrl.prototype.updateScrollViewContent = function (sv, preHeight, preY) {
        if (sv.isScrolling()) {
            return;
        }
        // cc.log('1============', sv.content.height, isDelay)
        var height = sv.node.height;
        sv.content.Component(cc.Layout).updateLayout();
        // cc.log('2============', sv.content.height)
        if (sv.content.height <= height) {
            sv.scrollToTop();
        }
        else { //在最下面的位置
            sv.scrollToBottom();
        }
    };
    // 刷新联盟顶部信息
    ChatPnlCtrl.prototype.updateAlliTopInfo = function (node) {
        var _a;
        node = node || this.chatNode_.Child('sv/1');
        var alliance = GameHelper_1.gameHpr.alliance;
        var uid = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        var isTeam = this.childType === 1;
        var onlineNode = node.Child('top/alli_online_be/online_count');
        var showOnline = false;
        var online = 0, all = '';
        if (isTeam) {
            showOnline = this.team.hasTeam();
            all = this.team.hasTeam() ? '40' /* this.team.getTeammates().length */ : '0';
            online = this.team.hasTeam() ? this.team.getActTeammates().length : 0;
        }
        else {
            showOnline = this.chat.isCanSendAlli();
            all = '40'; //alliance.getChannelMemberInfos(uid).length
            online = alliance.getChannelMemberInfos(uid).filter(function (m) { return m.offlineTime === 0; }).length;
        }
        if (onlineNode.parent.active = showOnline) {
            onlineNode.Child('cur', cc.Label).string = online + '';
            onlineNode.Child('max', cc.Label).string = '/' + all;
        }
    };
    // 刷新联盟频道
    ChatPnlCtrl.prototype.updateAlliChannels = function (init, tip) {
        var _a;
        this.alliChannelNode_.Data = true;
        var isAuth = GameHelper_1.gameHpr.alliance.isCanCreateChatChannel();
        var channels = isAuth ? [null] : [];
        // 加入主频道
        var mainChannel = { uid: '0', name: assetsMgr.lang('ui.main_channel'), color: '5BB8FF' };
        channels.push(mainChannel);
        // 加入联盟频道
        channels.pushArr(GameHelper_1.gameHpr.alliance.getChatChannels());
        var curChannelUid = (_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        var len = channels.length - 1;
        this.alliChannelItemsNode_.Items(channels, function (it, data, i) {
            it.Data = data;
            it.Child('add').active = !data;
            var has = it.Child('val').active = it.Child('color').active = !!data;
            if (has) {
                it.Child('val', cc.Label).string = data.name;
                it.Child('color').color = ut.colorFromHEX(data.color ? '#' + data.color : Constant_1.CHAT_BARRAGE_COLOR[1]);
                it.Child('val').Color(curChannelUid === data.uid ? '#B6A591' : '#756963');
            }
            it.Child('remove_alli_channel_be').active = has && isAuth && data.uid !== '0';
            it.Child('line').active = i < len;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            data = mainChannel;
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL, data.uid);
            this.changeAlliChannel(data, init, tip);
        }
        else if (init) {
            this.changeAlliChannel(data, init, tip);
        }
    };
    // 切换联盟频道
    ChatPnlCtrl.prototype.changeAlliChannel = function (data, init, tip) {
        var node = this.alliChannelNode_;
        node.Child('val', cc.Label).string = data.name;
        node.Child('color').color = ut.colorFromHEX(data.color ? '#' + data.color : Constant_1.CHAT_BARRAGE_COLOR[1]);
        this.alliChannelItemsNode_.children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('val').Color(select ? '#B6A591' : '#756963');
            }
        });
        this.updateSpecialBarrageSetting(1);
        this.updateAlliTopInfo();
        this.updateChatLists(init, tip);
    };
    // 联盟弹幕设置 0:世界，1：联盟，2：单人
    ChatPnlCtrl.prototype.updateSpecialBarrageSetting = function (tab) {
        var getType = GameHelper_1.gameHpr.getChatChannelBarrageSettingInfo(tab).getType;
        this.updateBarrageSetting(getType);
    };
    // 刷新世界频道
    ChatPnlCtrl.prototype.updateWorldChannels = function (init) {
        var _a;
        this.worldChannelNode_.Data = true;
        var isLobby = this.isLobbyChat();
        var sames = ['cn', 'hk', 'tw'], channels = [{ uid: '0', name: isLobby ? 'ui.lobby_chat_channel' : 'ui.world_chat_channel' }];
        if (sames.has(mc.lang)) {
            channels.push({ uid: 'zh', name: '简繁中文' });
        }
        else {
            var data_1 = Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === mc.lang; });
            channels.push({ uid: mc.lang, name: data_1.text });
        }
        var len = channels.length - 1;
        var curChannelUid = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        this.worldChannelItemsNode_.Items(channels, function (it, data, i) {
            it.Data = data;
            var val = it.Child('val', cc.Label);
            if (i) {
                val.string = data.name;
            }
            else {
                val.setLocaleKey(data.name);
            }
            val.Color(curChannelUid === data.uid ? '#B6A591' : '#756963');
            it.Child('line').active = i < len;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            data = channels[0];
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, data.uid);
            this.changeWorldChannel(data);
        }
        else if (init) {
            this.changeWorldChannel(data);
        }
    };
    // 切换世界频道
    ChatPnlCtrl.prototype.changeWorldChannel = function (data) {
        var isWorld = data.uid === '0', worldVal = this.worldChannelNode_.Child('world'), xVal = this.worldChannelNode_.Child('x', cc.Label);
        if (isWorld) {
            xVal.node.active = false;
            worldVal.active = true;
            worldVal.setLocaleKey(data.name);
        }
        else {
            xVal.node.active = true;
            worldVal.active = false;
            xVal.string = data.name;
        }
        this.worldChannelItemsNode_.children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('val').Color(select ? '#B6A591' : '#756963');
            }
        });
        this.updateSpecialBarrageSetting(0);
        this.updateChatLists();
    };
    // 刷新私聊列表
    ChatPnlCtrl.prototype.updatePChatsList = function (node) {
        var _this = this;
        node = node || this.chatNode_.Child('sv/2/page').Swih(1)[0];
        var sv = node.Child('players', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var chats = this.chat.getPChatChannels(), len = chats.length;
        chats.sort(function (a, b) { return (b.count || 0) - (a.count || 0); });
        sv.Items(chats, function (it, data) {
            it.Data = data;
            it.opacity = 120;
            it.Child('dot').active = (data === null || data === void 0 ? void 0 : data.count) > 0;
            var layout = it.Child('layout');
            layout.Child('close_pchat_be').active = false;
            layout.Child('name', cc.Label).string = ut.nameFormator(data.name || '???', 7);
            ResHelper_1.resHelper.loadPlayerHead(layout.Child('head/val', cc.Sprite), data.head, _this.key);
        });
        var list = node.Child('list', cc.ScrollView);
        var empty = list.Child('empty');
        var forbiden = false;
        if (empty.active = !len) {
            this.bottomEditNode_.active = true;
            this.barrageSettingNode_.active = true;
            this.updateChatLists();
        }
        else {
            forbiden = true;
            var curChannel_1 = this.chat.getCurPChatChannel();
            var index = sv.content.children.findIndex(function (m) { return m.Data.channel === curChannel_1; }), item = sv.content.children[index];
            if (!item) {
                index = 0;
                item = sv.content.children[0];
                this.chat.setCurPChatChannel(item.Data.channel);
            }
            sv.SelectItemToCentre(index);
            this.selectPChatItem(item, item.Data);
        }
        this.updateSendButton(forbiden);
    };
    ChatPnlCtrl.prototype.selectPChatItem = function (node, data) {
        var _a;
        node.Data = data;
        node.opacity = 255;
        node.Child('dot').active = false;
        var layout = node.Child('layout');
        layout.Child('name', cc.Label).string = ut.nameFormator(data.name || '???', 5);
        layout.Child('close_pchat_be').active = true;
        node.Component(cc.Button).interactable = !!data;
        (_a = this.pchatCloseProgressTween) === null || _a === void 0 ? void 0 : _a.stop();
        if (data) {
            var stime = Math.max(0, data.canCloseTime - (Date.now() - data.getTime));
            var progress = layout.Child('close_pchat_be/progress', cc.Sprite);
            if (progress.setActive(!!stime)) {
                progress.fillRange = stime / Constant_1.REMOVE_PCHAT_TIME;
                this.pchatCloseProgressTween = cc.tween(progress).to(stime * 0.001, { fillRange: 0 }).start();
            }
            this.chat.updatePChatLastReadTime(data);
        }
        this.bottomEditNode_.active = true;
        this.barrageSettingNode_.active = true;
        this.updateChatLists();
        this.targetChatInfo = null;
    };
    // 删除私聊
    ChatPnlCtrl.prototype.removePChat = function (channel) {
        var _this = this;
        this.chat.removePChat(channel).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updatePChatsList();
                // const node = this.chatNode_.Child('sv/2/page/1/players', cc.ScrollView)
                // const item = node.content.children[0]
                // this.selectPChatItem(item, item.Data)
            }
        });
    };
    ChatPnlCtrl.prototype.isLobbyChat = function () {
        return this.childType === 1 && (this.type === 1 || this.type === 0);
    };
    ChatPnlCtrl.prototype.isFriendChat = function () {
        return this.type === 2 && this.childType === 0;
    };
    ChatPnlCtrl.prototype.updateFriendsList = function (node) {
        var _this = this;
        node = node || this.chatNode_.Child('sv/2/page').Swih(0)[0];
        var sv = node.Swih('friends')[0].Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        node.Child('list').active = false;
        var friends = GameHelper_1.gameHpr.friend.getFriends(), len = friends.length;
        friends.sort(function (a, b) {
            if (a.notReadCount !== b.notReadCount) {
                return b.notReadCount - a.notReadCount;
            }
            var aGiftCount = a.giftList.length, bGiftCount = b.giftList.length;
            if (aGiftCount !== bGiftCount) {
                return bGiftCount - aGiftCount;
            }
            return a.offlineTime - b.offlineTime;
        });
        sv.List(len, function (it, i) {
            var data = it.Data = friends[i], layout = it.Child('layout');
            it.Child('dot').active = data.notReadCount > 0;
            it.Child('content', cc.Label).string = data.lastChatInfo ? (data.lastChatInfo.content || (data.lastChatInfo.emoji ? "[" + assetsMgr.lang('ui.title_emoji') + "]" : '')) : '';
            it.Child('time', cc.Label).string = data.lastChatInfo ? ut.dateFormat('h:mm', data.lastChatInfo.time) : '';
            layout.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            var serverNode = layout.Child('online/server');
            serverNode.active = false;
            if (data.offlineTime) {
                layout.Child('online/val').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.getOfflineTime()));
            }
            else if (data.playSid) {
                layout.Child('online/val').Color('#59A733').setLocaleKey('ui.in_game');
                serverNode.active = true;
                var _a = GameHelper_1.gameHpr.getServerNameById(data.playSid), key = _a.key, id = _a.id;
                serverNode.setLocaleKey('ui.bracket', assetsMgr.lang(key, id));
            }
            else {
                layout.Child('online/val').Color('#59A733').setLocaleKey('ui.in_ready_war');
            }
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
        });
        sv.node.Child('empty').active = !len;
        this.bottomEditNode_.active = false;
        this.barrageSettingNode_.active = false;
    };
    ChatPnlCtrl.prototype.backToFriendsList = function () {
        this.chatNode_.Child('sv/2/page/0').Swih('friends');
        this.barrageSettingNode_.active = false;
        this.bottomEditNode_.active = false;
        this.friend.setCurrLookChatFriendUID('');
        this.updateFriendsList();
    };
    ChatPnlCtrl.prototype.enterFriendChat = function (data) {
        this.bottomEditNode_.active = true;
        this.barrageSettingNode_.active = false;
        var list = this.chatNode_.Child('sv/2/page/0').Swih('list')[0];
        var playerNode_ = list.Child('top/player');
        playerNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
        ResHelper_1.resHelper.loadPlayerHead(playerNode_.Child('head/val', cc.Sprite), data.headIcon, this.key);
        this.friend.setCurrLookChatFriendUID(data.uid);
        this.loadPersChatList(data);
        this.targetChatInfo = null;
    };
    ChatPnlCtrl.prototype.loadPersChatList = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.chatSv.Items(0);
                        this.sendNode_.Swih('val');
                        this.setLoading(true);
                        return [4 /*yield*/, this.friend.checkInitFriendChats(data.uid)];
                    case 1:
                        _a.sent();
                        this.setLoading(false);
                        this.friend.tagChatRead(data);
                        this.updateFriendsChatList(true);
                        this.updateSendButton();
                        return [2 /*return*/];
                }
            });
        });
    };
    ChatPnlCtrl.prototype.setLoading = function (val) {
        this.loadingNode_.active = val;
        this.sendNode_.Component(cc.Button).interactable = !val;
        this.sendNode_.Component(cc.MultiFrame).setFrame(!val);
    };
    // 刷新聊天列表
    ChatPnlCtrl.prototype.updateFriendsChatList = function (init) {
        var _this = this;
        var _a, _b;
        var uid = this.friend.getCurrLookChatFriendUID();
        var list = this.friend.getChatsByFriend(uid), count = list.length - 1;
        GameHelper_1.gameHpr.updateChatAllTime(list);
        var firstUid = ((_a = list[0]) === null || _a === void 0 ? void 0 : _a.uid) || '', tempMap = {};
        if (!this.curFirstChatUid) {
            this.curFirstChatUid = firstUid;
        }
        if (this.curFirstChatUid === firstUid) {
            (_b = this.tempChatNodeMap[this.type + '_' + this.childType]) === null || _b === void 0 ? void 0 : _b.forEach(function (m) {
                var _a;
                if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                    tempMap[m.Data.uid] = true;
                }
            });
        }
        else {
            this.tempChatNodeMap[this.type + '_' + this.childType] = null;
        }
        var height = this.chatSv.content.height, y = this.chatSv.content.y;
        this.chatSv.stopAutoScroll();
        this.chatSv.Items(list, this.persChatItem, function (it, data, i) {
            if (!tempMap[data.uid]) {
                _this.updateFriendChatItem(it, data);
            }
            if (i === count) {
                _this.chatSv.Component(cc.ScrollViewPlus).isFrameRender = false;
                _this.updateFriendsScrollViewContent(_this.chatSv, height, y, init);
            }
        });
    };
    ChatPnlCtrl.prototype.updateFriendChatItem = function (it, data) {
        var _a, _b, _c;
        it.Data = data;
        var timeLbl = it.Child('time', cc.Label);
        if (timeLbl.setActive(!!data.showTime)) {
            timeLbl.setLocaleKey('ui.text', data.showTime);
        }
        var height = data.showTime ? 40 : 0;
        var isMe = GameHelper_1.gameHpr.getUid() === data.sender;
        var root = it.Child('root');
        root.active = true;
        root.y = data.showTime ? -40 : 0;
        root.Child('head_be').x = isMe ? 278 : -278;
        // root.Child('head_be', cc.Button).interactable = !isMe
        var name = root.Child('name'), content = root.Child('content'), emoji = root.Child('emoji'), loading = root.Child('loading');
        name.anchorX = content.anchorX = isMe ? 1 : 0;
        name.x = content.x = isMe ? 240 : -240;
        loading.x = emoji.x = isMe ? 190 : -190;
        var player = isMe ? { headIcon: GameHelper_1.gameHpr.user.getHeadIcon(), nickname: GameHelper_1.gameHpr.user.getNickname(), noteName: '' } : this.friend.getFriendByUID(data.sender);
        ResHelper_1.resHelper.loadPlayerHead(root.Child('head_be/val', cc.Sprite), player === null || player === void 0 ? void 0 : player.headIcon, this.key);
        var nameLbl = root.Child('name', cc.Label);
        nameLbl.string = ut.nameFormator((player === null || player === void 0 ? void 0 : player.nickname) || '???', 12);
        nameLbl._forceUpdateRenderData();
        this.updateNoteName(it, player === null || player === void 0 ? void 0 : player.noteName, name.x, nameLbl.node.width * 0.5, name.anchorX);
        var hasEmoji = !!data.emoji;
        content.active = !hasEmoji;
        content.Data = null;
        if (hasEmoji) { //是表情
            loading.active = !!data.wait;
            if (emoji.active = !data.wait) {
                var scale = ((_a = assetsMgr.getJsonData('chatEmoji', data.emoji)) === null || _a === void 0 ? void 0 : _a.scale) || 0;
                if (scale && !isMe) {
                    scale *= -1;
                }
                ResHelper_1.resHelper.loadEmojiNode(data.emoji, emoji, scale, this.key, true);
            }
            height += (36 + 100);
        }
        else {
            // emoji.removeAllChildren()
            // loading.active = emoji.active = false
            // const contentLbl = content.Component(cc.Label)
            emoji.removeAllChildren();
            loading.active = emoji.active = false;
            var contentLbl = content.Child('content', cc.Label), translateLbl = content.Child('translate', cc.Label);
            var translateButton = content.Child('content/translate_be');
            translateLbl.node.anchorX = contentLbl.node.anchorX = content.anchorX;
            translateLbl.node.x = contentLbl.node.x = 0;
            translateButton.active = !isMe && !data.translate && !!data.content;
            content.Data = { node: it, data: data };
            contentLbl.Color(data.wait ? '#7F7F7F' : '#333333').string = data.content;
            var lineNode = content.Child('line'), translateLoading = content.Child('loading');
            lineNode.active = !!data.translate;
            translateLoading.active = !!((_b = data.translate) === null || _b === void 0 ? void 0 : _b.req);
            contentLbl.Component(LabelAutoAnyCmpt_1.default).check();
            if (translateButton.active) {
                translateButton.x = contentLbl.node.width + 50;
            }
            if (translateLbl.setActive(!!((_c = data.translate) === null || _c === void 0 ? void 0 : _c.text))) {
                translateLbl.string = data.translate.text;
                translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
                lineNode.Child('val').width = translateLbl.node.width * 0.5;
            }
            else {
                lineNode.Child('val').width = contentLbl.node.width * 0.5;
            }
            content.Component(cc.Layout).updateLayout();
            height += Math.max(60, Math.ceil(content.height + 32));
        }
        it.height = height;
    };
    // 显示备注
    ChatPnlCtrl.prototype.updateNoteName = function (it, noteName, x, width, anchorX) {
        var titleNode = it.Child('root/title');
        if (titleNode.active = !!noteName) {
            titleNode.Component(cc.Label).string = '(' + noteName + ')';
            titleNode.anchorX = anchorX;
            titleNode.x = x + (width + 8) * (anchorX ? -1 : 1);
        }
    };
    ChatPnlCtrl.prototype.updateFriendsScrollViewContent = function (sv, preHeight, preY, init) {
        if (sv.isScrolling()) {
            return;
        }
        var content = sv.content, height = sv.node.height;
        content.Component(cc.Layout).updateLayout();
        if (content.height <= height) {
            sv.scrollToTop();
        }
        else if ( /* init ||  */preY >= preHeight - height - 50) { //在最下面的位置
            sv.scrollToBottom();
        }
    };
    // 刷新发送按钮
    ChatPnlCtrl.prototype.updateSendButton = function (forbiden) {
        var _this = this;
        var time = this.isFriendChat() ? this.friend.checkRestSurplusTime() : this.isLobbyChat() ? this.lobby.checkRestSurplusTime(this.type === 1) : this.chat.checkRestSurplusTime(this.chat.isMilitaryAlliChannelChat());
        var b = this.sendNode_.Component(cc.Button).interactable = forbiden !== null && forbiden !== void 0 ? forbiden : !time;
        this.sendNode_.Component(cc.MultiFrame).setFrame(b);
        this.emojiNode_.Component(cc.Button).interactable = b;
        this.emojiNode_.Child('val').Color(b ? '#756963' : '#B6A591');
        if (time > 0) {
            this.sendNode_.Swih('time')[0].Component(cc.LabelTimer).run(time * 0.001, function () { return _this.isValid && _this.updateSendButton(); });
        }
        else {
            this.sendNode_.Swih('val');
        }
    };
    // 刷新弹幕设置内容
    ChatPnlCtrl.prototype.updateBarrageSetting = function (type) {
        var node = this.barrageSettingNode_;
        node.Data = type;
        node.Child('val', cc.Label).setLocaleKey('ui.barrage_area_' + type);
        node.Child('mask/root/setting_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
    };
    ChatPnlCtrl.prototype.setChatItemActive = function () {
        var _a, _b;
        var chatSv = this.chatSv;
        if (!chatSv) {
            return;
        }
        var tempList = this.tempChatNodeMap[this.type + '_' + this.childType] = [];
        var svp = chatSv.getComponent(cc.ScrollViewPlus);
        if (svp.isFrameRender) {
            return;
        }
        var children = chatSv.content.children;
        this.curFirstChatUid = ((_b = (_a = children[0]) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.uid) || '';
        // 优先显示视野内的
        var surfaceLine = -chatSv.content.y + chatSv.content.parent.height * 0.5;
        var minDis = chatSv.content.height;
        var idx = -1;
        for (var i = 0, l = children.length; i < l; i++) {
            var child = children[i];
            var dis = Math.abs(child.y - surfaceLine);
            if (dis < minDis) {
                minDis = dis;
                idx = i;
            }
        }
        if (idx !== -1) {
            for (var l = idx, r = idx + 1; l >= 0 || r < children.length;) {
                l >= 0 && tempList.push(children[l--]);
                r < children.length && tempList.push(children[r++]);
            }
            tempList.reverse();
        }
    };
    ChatPnlCtrl.prototype.closeReply = function () {
        this.replyBoxNode_.active = false;
        this.replyBoxNode_.Data = null;
    };
    ChatPnlCtrl.prototype.playbackBatle = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                    case 2:
                        _a.sent();
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    ChatPnlCtrl.prototype.update = function (dt) {
        var tempList = this.tempChatNodeMap[this.type + '_' + this.childType];
        if ((tempList === null || tempList === void 0 ? void 0 : tempList.length) > 0) {
            var node = tempList.pop();
            if (node === null || node === void 0 ? void 0 : node.Data) {
                this.updateChatItem(node, node.Data);
            }
        }
    };
    ChatPnlCtrl = __decorate([
        ccclass
    ], ChatPnlCtrl);
    return ChatPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ChatPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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