
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/HDFeedbackPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '26ddbMuHe5H97I0JLVtIYHh', 'HDFeedbackPnlCtrl');
// app/script/view/login/HDFeedbackPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ccclass = cc._decorator.ccclass;
var HDFeedbackPnlCtrl = /** @class */ (function (_super) {
    __extends(HDFeedbackPnlCtrl, _super);
    function HDFeedbackPnlCtrl() {
        //@autocode property begin
        //@end
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // private readonly URL: string = 'https://cocos-test-webview.dhgames.com/?bundleId=jwm.bundleid.v1'
        _this.URL = 'https://dh-aics.dhgames.cn/?bundleId=jwm.bundleid.v1';
        _this.SCHEME = 'testkey';
        _this.webView = null;
        _this.onWebRecv = null;
        _this.inited = false;
        _this.isRegister = false;
        return _this;
    }
    HDFeedbackPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    HDFeedbackPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                this.webView = this.FindChild('wv', cc.WebView);
                this.webView.node.on('loaded', function () {
                    if (_this.isValid) {
                        _this.registerEnv();
                    }
                });
                this.webView.node.on('loading', function (event, data) {
                    console.log('loading.............', event, data);
                });
                this.webView.node.on('error', function (event, data) {
                    console.log('error.............', event, data);
                });
                this.webView.url = this.URL;
                return [2 /*return*/];
            });
        });
    };
    HDFeedbackPnlCtrl.prototype.onEnter = function (data) {
    };
    HDFeedbackPnlCtrl.prototype.onRemove = function () {
        var _a;
        if (cc.sys.isNative) {
            this.webView.setOnJSCallback(null);
        }
        else if (cc.sys.isBrowser && this.onWebRecv) {
            window.removeEventListener('message', this.onWebRecv);
        }
        (_a = this.webView) === null || _a === void 0 ? void 0 : _a.destroy();
        this.inited = false;
        this.isRegister = false;
    };
    HDFeedbackPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    HDFeedbackPnlCtrl.prototype.registerEnv = function () {
        var _this = this;
        if (this.inited) {
            return;
        }
        this.inited = true;
        var platform = '';
        if (cc.sys.isNative) {
            this.webView.setJavascriptInterfaceScheme(this.SCHEME);
            this.webView.setOnJSCallback(function (target, url) {
                console.log(url);
                var data = url.replace(_this.SCHEME + '://arg=', '');
                // console.log('OnJSCallback', data)
                try {
                    _this.onMessage(JSON.parse(Base64.decode(data)));
                }
                catch (error) { }
            });
            platform = 'native';
        }
        else if (cc.sys.isBrowser) {
            var onWebRecv = this.onWebRecv = function (event) { return _this.onMessage(JSON.parse(Base64.decode(event.data))); };
            window.removeEventListener('message', onWebRecv);
            window.addEventListener('message', onWebRecv, false);
            platform = 'web';
        }
        this.registerPlatform(platform);
    };
    HDFeedbackPnlCtrl.prototype.registerPlatform = function (platform) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.inited) return [3 /*break*/, 2];
                        this.send('platform', platform);
                        return [4 /*yield*/, ut.wait(2, this)];
                    case 1:
                        _a.sent();
                        if (this.isRegister) {
                            return [3 /*break*/, 2];
                        }
                        return [3 /*break*/, 0];
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    HDFeedbackPnlCtrl.prototype.onMessage = function (data) {
        // console.log('onMessage', data)
        if (!data) {
            return;
        }
        else if (data.native_method === 'getGameData') {
            this.isRegister = true;
            var user = GameHelper_1.gameHpr.user;
            var lang = mc.lang;
            if (lang === 'hk') {
                lang = 'zh';
            }
            else if (lang === 'idl') {
                lang = 'id';
            }
            var data_1 = {
                native_method: 'getGameData',
                getGameData: {
                    getGameInfo: {
                        gameName: 'NTA',
                        bundleId: GameHelper_1.gameHpr.getBundleId() || 'twgame.global.acers',
                        language: lang,
                    },
                    getUrlInfo: {
                        aics_http_addr: 'https://aics-cli.dev-dh.com',
                        aics_ws_addr: 'wss://aics-cli.dev-dh.com/dh_ws/ws_app',
                    },
                    getRoleInfo: {
                        sid: GameHelper_1.gameHpr.getServerArea() + '_' + user.getPlaySid(),
                        userId: user.getUid() || TaHelper_1.taHelper.getDistinctId(),
                        userName: user.getNickname(),
                        avatar: user.getHeadIcon(),
                    }
                }
            };
            this.send('editor', Base64.encode(JSON.stringify(data_1)));
        }
        else if (data.native_method === 'destroy') {
            this.close();
        }
    };
    // 发送方法
    HDFeedbackPnlCtrl.prototype.send = function (type, data) {
        if (!type || !data) {
            return;
        }
        data = JSON.stringify({
            type: type,
            data: data
        });
        if (cc.sys.isNative) {
            var evt = {
                origin: 'native',
                data: data,
            };
            // native发送，因为只能发送基本类型的数据 转成字符串
            var jsStr = "receiveMessage(" + JSON.stringify(evt) + ")";
            // this.webView.evaluateJS(`
            //     console.log = function(...args) {
            //         try{
            //             document.location = 'testkey://arg=' + args.join(",");
            //         }catch(e){}
            //     }    
            //     ${jsStr}
            // `)
            this.webView.evaluateJS(jsStr);
            // console.log('send', jsStr)
        }
        else if (cc.sys.isBrowser) {
            // @ts-ignore
            this.webView._impl._iframe.contentWindow.postMessage(data, this.URL);
        }
    };
    HDFeedbackPnlCtrl = __decorate([
        ccclass
    ], HDFeedbackPnlCtrl);
    return HDFeedbackPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = HDFeedbackPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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