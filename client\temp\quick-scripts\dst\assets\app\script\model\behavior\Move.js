
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/Move.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '44b23xYA4pEQaBzcT2pH5zZ', 'Move');
// app/script/model/behavior/Move.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var MapHelper_1 = require("../../common/helper/MapHelper");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 朝目标移动
var Move = /** @class */ (function (_super) {
    __extends(Move, _super);
    function Move() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isChangeFighterState = false; //是否改变士兵的状态了
        return _this;
    }
    Move.prototype.onInit = function (conf) {
    };
    Move.prototype.onOpen = function () {
        this.setTreeBlackboardData('isMove', true); //记录已经移动过了
        this.isChangeFighterState = false;
    };
    Move.prototype.onClean = function () {
    };
    Move.prototype.onTick = function (dt) {
        var _a, _b;
        if (!this.target.attackTarget) {
            return BTConstant_1.BTState.FAILURE;
        }
        var paths = this.getBlackboardData('paths');
        var needMoveTime = (_a = this.getBlackboardData('needMoveTime')) !== null && _a !== void 0 ? _a : 0;
        if (!paths) {
            var points = this.searchPath(this.target.attackTarget);
            paths = points.map(function (m) { return m.toJson(); });
            this.setBlackboardData('paths', paths);
            needMoveTime = MapHelper_1.mapHelper.getMoveNeedTime(points, this.target.entity.getMoveVelocity());
            this.setBlackboardData('needMoveTime', needMoveTime);
        }
        if (paths.length === 0) {
            return BTConstant_1.BTState.SUCCESS;
        }
        // 一直检测时间
        var currMoveTime = (_b = this.getBlackboardData('currMoveTime')) !== null && _b !== void 0 ? _b : 0;
        if (currMoveTime >= needMoveTime) {
            var movePathLen = Math.max(0, paths.length - 1);
            // this.setTreeBlackboardData('movePathLen', movePathLen)
            // 夏侯渊
            var heroSkill = this.target.getPortrayalSkill();
            if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.XIA_HOUYUAN) {
                var buff = this.target.getBuffOrAdd(Enums_1.BuffType.LONG_RANGE_RAID, this.target.getUid());
                if (buff.value < heroSkill.value) {
                    buff.value = Math.min(buff.value + movePathLen, heroSkill.value);
                }
            }
            this.target.setPoint(paths.last());
            this.target.changeState(Enums_1.PawnState.STAND);
            this.ctrl.updateFighterPointMap();
            return BTConstant_1.BTState.SUCCESS;
        }
        this.setBlackboardData('currMoveTime', currMoveTime + dt);
        // 设置士兵状态 更新视图信息
        if (!this.isChangeFighterState) {
            this.isChangeFighterState = true;
            this.target.changeState(Enums_1.PawnState.MOVE, this.blackboard);
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 搜索路径
    Move.prototype.searchPath = function (attackTarget) {
        var uid = attackTarget.getUid();
        var data = this.target.getCanAttackTargets().find(function (m) { return m.uid === uid; });
        if (data && data.paths.length > 0) {
            return data.paths;
        }
        return []; //到不了 就不动了
        // const point = this.target.getPoint()
        // const searchDir = this.target.getSearchDir(attackTarget)
        // const moveRange = this.target.entity.moveRange
        // const camp = this.target.getCamp()
        // // 找出到目标的路径
        // const pathsArray = this.target.astar.search(point, attackTarget.getPoint(), searchDir, moveRange, camp)
        // return pathsArray[0] || []
    };
    return Move;
}(BaseAction_1.default));
exports.default = Move;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcTW92ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxREFBNEU7QUFFNUUsMkRBQTBEO0FBQzFELDJDQUFzQztBQUN0QywyQ0FBdUM7QUFFdkMsUUFBUTtBQUNSO0lBQWtDLHdCQUFVO0lBQTVDO1FBQUEscUVBMkVDO1FBekVXLDBCQUFvQixHQUFZLEtBQUssQ0FBQSxDQUFDLFlBQVk7O0lBeUU5RCxDQUFDO0lBdkVVLHFCQUFNLEdBQWIsVUFBYyxJQUFTO0lBRXZCLENBQUM7SUFFTSxxQkFBTSxHQUFiO1FBQ0ksSUFBSSxDQUFDLHFCQUFxQixDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsQ0FBQSxDQUFDLFVBQVU7UUFDckQsSUFBSSxDQUFDLG9CQUFvQixHQUFHLEtBQUssQ0FBQTtJQUNyQyxDQUFDO0lBRU0sc0JBQU8sR0FBZDtJQUNBLENBQUM7SUFFTSxxQkFBTSxHQUFiLFVBQWMsRUFBVTs7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxFQUFFO1lBQzNCLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxJQUFJLEtBQUssR0FBWSxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxDQUFDLENBQUE7UUFDcEQsSUFBSSxZQUFZLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQyxtQ0FBSSxDQUFDLENBQUE7UUFDdEUsSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNSLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQTtZQUN4RCxLQUFLLEdBQUcsTUFBTSxDQUFDLEdBQUcsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxNQUFNLEVBQUUsRUFBVixDQUFVLENBQUMsQ0FBQTtZQUNuQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQ3RDLFlBQVksR0FBRyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsZUFBZSxFQUFFLENBQUMsQ0FBQTtZQUN0RixJQUFJLENBQUMsaUJBQWlCLENBQUMsY0FBYyxFQUFFLFlBQVksQ0FBQyxDQUFBO1NBQ3ZEO1FBQ0QsSUFBSSxLQUFLLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNwQixPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO1NBQ3pCO1FBQ0QsU0FBUztRQUNULElBQUksWUFBWSxTQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxjQUFjLENBQUMsbUNBQUksQ0FBQyxDQUFBO1FBQ3RFLElBQUksWUFBWSxJQUFJLFlBQVksRUFBRTtZQUM5QixJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQ2pELHlEQUF5RDtZQUN6RCxNQUFNO1lBQ04sSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1lBQ2pELElBQUksQ0FBQSxTQUFTLGFBQVQsU0FBUyx1QkFBVCxTQUFTLENBQUUsRUFBRSxNQUFLLGdCQUFRLENBQUMsV0FBVyxFQUFFO2dCQUN4QyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxnQkFBUSxDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUE7Z0JBQ3JGLElBQUksSUFBSSxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUMsS0FBSyxFQUFFO29CQUM5QixJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxXQUFXLEVBQUUsU0FBUyxDQUFDLEtBQUssQ0FBQyxDQUFBO2lCQUNuRTthQUNKO1lBQ0QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUE7WUFDbEMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUN4QyxJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUE7WUFDakMsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtTQUN6QjtRQUNELElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxjQUFjLEVBQUUsWUFBWSxHQUFHLEVBQUUsQ0FBQyxDQUFBO1FBQ3pELGdCQUFnQjtRQUNoQixJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzVCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUE7WUFDaEMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFBO1NBQzNEO1FBQ0QsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBRUQsT0FBTztJQUNDLHlCQUFVLEdBQWxCLFVBQW1CLFlBQXNCO1FBQ3JDLElBQU0sR0FBRyxHQUFHLFlBQVksQ0FBQyxNQUFNLEVBQUUsQ0FBQTtRQUNqQyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLG1CQUFtQixFQUFFLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxHQUFHLEVBQWIsQ0FBYSxDQUFDLENBQUE7UUFDdkUsSUFBSSxJQUFJLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQy9CLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQTtTQUNwQjtRQUNELE9BQU8sRUFBRSxDQUFBLENBQUMsVUFBVTtRQUNwQix1Q0FBdUM7UUFDdkMsMkRBQTJEO1FBQzNELGlEQUFpRDtRQUNqRCxxQ0FBcUM7UUFDckMsY0FBYztRQUNkLDBHQUEwRztRQUMxRyw2QkFBNkI7SUFDakMsQ0FBQztJQUNMLFdBQUM7QUFBRCxDQTNFQSxBQTJFQyxDQTNFaUMsb0JBQVUsR0EyRTNDIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnVmZlR5cGUsIEhlcm9UeXBlLCBQYXduU3RhdGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VudW1zXCI7XHJcbmltcG9ydCB7IElGaWdodGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9JbnRlcmZhY2VcIjtcclxuaW1wb3J0IHsgbWFwSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvTWFwSGVscGVyXCI7XHJcbmltcG9ydCBCYXNlQWN0aW9uIGZyb20gXCIuL0Jhc2VBY3Rpb25cIjtcclxuaW1wb3J0IHsgQlRTdGF0ZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIjtcclxuXHJcbi8vIOacneebruagh+enu+WKqFxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBNb3ZlIGV4dGVuZHMgQmFzZUFjdGlvbiB7XHJcblxyXG4gICAgcHJpdmF0ZSBpc0NoYW5nZUZpZ2h0ZXJTdGF0ZTogYm9vbGVhbiA9IGZhbHNlIC8v5piv5ZCm5pS55Y+Y5aOr5YW155qE54q25oCB5LqGXHJcblxyXG4gICAgcHVibGljIG9uSW5pdChjb25mOiBhbnkpIHtcclxuXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uT3BlbigpIHtcclxuICAgICAgICB0aGlzLnNldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNNb3ZlJywgdHJ1ZSkgLy/orrDlvZXlt7Lnu4/np7vliqjov4fkuoZcclxuICAgICAgICB0aGlzLmlzQ2hhbmdlRmlnaHRlclN0YXRlID0gZmFsc2VcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25DbGVhbigpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25UaWNrKGR0OiBudW1iZXIpIHtcclxuICAgICAgICBpZiAoIXRoaXMudGFyZ2V0LmF0dGFja1RhcmdldCkge1xyXG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5GQUlMVVJFXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBwYXRoczogUG9pbnRbXSA9IHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ3BhdGhzJylcclxuICAgICAgICBsZXQgbmVlZE1vdmVUaW1lOiBudW1iZXIgPSB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCduZWVkTW92ZVRpbWUnKSA/PyAwXHJcbiAgICAgICAgaWYgKCFwYXRocykge1xyXG4gICAgICAgICAgICBjb25zdCBwb2ludHMgPSB0aGlzLnNlYXJjaFBhdGgodGhpcy50YXJnZXQuYXR0YWNrVGFyZ2V0KVxyXG4gICAgICAgICAgICBwYXRocyA9IHBvaW50cy5tYXAobSA9PiBtLnRvSnNvbigpKVxyXG4gICAgICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdwYXRocycsIHBhdGhzKVxyXG4gICAgICAgICAgICBuZWVkTW92ZVRpbWUgPSBtYXBIZWxwZXIuZ2V0TW92ZU5lZWRUaW1lKHBvaW50cywgdGhpcy50YXJnZXQuZW50aXR5LmdldE1vdmVWZWxvY2l0eSgpKVxyXG4gICAgICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCduZWVkTW92ZVRpbWUnLCBuZWVkTW92ZVRpbWUpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChwYXRocy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuU1VDQ0VTU1xyXG4gICAgICAgIH1cclxuICAgICAgICAvLyDkuIDnm7Tmo4DmtYvml7bpl7RcclxuICAgICAgICBsZXQgY3Vyck1vdmVUaW1lOiBudW1iZXIgPSB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdjdXJyTW92ZVRpbWUnKSA/PyAwXHJcbiAgICAgICAgaWYgKGN1cnJNb3ZlVGltZSA+PSBuZWVkTW92ZVRpbWUpIHtcclxuICAgICAgICAgICAgY29uc3QgbW92ZVBhdGhMZW4gPSBNYXRoLm1heCgwLCBwYXRocy5sZW5ndGggLSAxKVxyXG4gICAgICAgICAgICAvLyB0aGlzLnNldFRyZWVCbGFja2JvYXJkRGF0YSgnbW92ZVBhdGhMZW4nLCBtb3ZlUGF0aExlbilcclxuICAgICAgICAgICAgLy8g5aSP5L6v5riKXHJcbiAgICAgICAgICAgIGNvbnN0IGhlcm9Ta2lsbCA9IHRoaXMudGFyZ2V0LmdldFBvcnRyYXlhbFNraWxsKClcclxuICAgICAgICAgICAgaWYgKGhlcm9Ta2lsbD8uaWQgPT09IEhlcm9UeXBlLlhJQV9IT1VZVUFOKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBidWZmID0gdGhpcy50YXJnZXQuZ2V0QnVmZk9yQWRkKEJ1ZmZUeXBlLkxPTkdfUkFOR0VfUkFJRCwgdGhpcy50YXJnZXQuZ2V0VWlkKCkpXHJcbiAgICAgICAgICAgICAgICBpZiAoYnVmZi52YWx1ZSA8IGhlcm9Ta2lsbC52YWx1ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGJ1ZmYudmFsdWUgPSBNYXRoLm1pbihidWZmLnZhbHVlICsgbW92ZVBhdGhMZW4sIGhlcm9Ta2lsbC52YWx1ZSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB0aGlzLnRhcmdldC5zZXRQb2ludChwYXRocy5sYXN0KCkpXHJcbiAgICAgICAgICAgIHRoaXMudGFyZ2V0LmNoYW5nZVN0YXRlKFBhd25TdGF0ZS5TVEFORClcclxuICAgICAgICAgICAgdGhpcy5jdHJsLnVwZGF0ZUZpZ2h0ZXJQb2ludE1hcCgpXHJcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlNVQ0NFU1NcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3Vyck1vdmVUaW1lJywgY3Vyck1vdmVUaW1lICsgZHQpXHJcbiAgICAgICAgLy8g6K6+572u5aOr5YW154q25oCBIOabtOaWsOinhuWbvuS/oeaBr1xyXG4gICAgICAgIGlmICghdGhpcy5pc0NoYW5nZUZpZ2h0ZXJTdGF0ZSkge1xyXG4gICAgICAgICAgICB0aGlzLmlzQ2hhbmdlRmlnaHRlclN0YXRlID0gdHJ1ZVxyXG4gICAgICAgICAgICB0aGlzLnRhcmdldC5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuTU9WRSwgdGhpcy5ibGFja2JvYXJkKVxyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5pCc57Si6Lev5b6EXHJcbiAgICBwcml2YXRlIHNlYXJjaFBhdGgoYXR0YWNrVGFyZ2V0OiBJRmlnaHRlcikge1xyXG4gICAgICAgIGNvbnN0IHVpZCA9IGF0dGFja1RhcmdldC5nZXRVaWQoKVxyXG4gICAgICAgIGNvbnN0IGRhdGEgPSB0aGlzLnRhcmdldC5nZXRDYW5BdHRhY2tUYXJnZXRzKCkuZmluZChtID0+IG0udWlkID09PSB1aWQpXHJcbiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5wYXRocy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBkYXRhLnBhdGhzXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBbXSAvL+WIsOS4jeS6hiDlsLHkuI3liqjkuoZcclxuICAgICAgICAvLyBjb25zdCBwb2ludCA9IHRoaXMudGFyZ2V0LmdldFBvaW50KClcclxuICAgICAgICAvLyBjb25zdCBzZWFyY2hEaXIgPSB0aGlzLnRhcmdldC5nZXRTZWFyY2hEaXIoYXR0YWNrVGFyZ2V0KVxyXG4gICAgICAgIC8vIGNvbnN0IG1vdmVSYW5nZSA9IHRoaXMudGFyZ2V0LmVudGl0eS5tb3ZlUmFuZ2VcclxuICAgICAgICAvLyBjb25zdCBjYW1wID0gdGhpcy50YXJnZXQuZ2V0Q2FtcCgpXHJcbiAgICAgICAgLy8gLy8g5om+5Ye65Yiw55uu5qCH55qE6Lev5b6EXHJcbiAgICAgICAgLy8gY29uc3QgcGF0aHNBcnJheSA9IHRoaXMudGFyZ2V0LmFzdGFyLnNlYXJjaChwb2ludCwgYXR0YWNrVGFyZ2V0LmdldFBvaW50KCksIHNlYXJjaERpciwgbW92ZVJhbmdlLCBjYW1wKVxyXG4gICAgICAgIC8vIHJldHVybiBwYXRoc0FycmF5WzBdIHx8IFtdXHJcbiAgICB9XHJcbn0iXX0=