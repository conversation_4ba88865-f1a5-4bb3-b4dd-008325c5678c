
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'acb21udletEzYN+0VChPcVO', 'NoviceConfig');
// app/script/model/guide/NoviceConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NOVICE_DEFAULT_WARTOKEN_COUNT = exports.NOVICE_DEFAULT_GOLD_COUNT = exports.NOVICE_LAND_TREASURE_REWARD_MAP = exports.NOVICE_CURE_TIME_PARAM_MAP = exports.NOVICE_CURE_RES_PARAM_MAP = exports.NOVICE_HOSPITAL_SPEED_MUL = exports.NOVICE_FIRST_STONE_TREASURE_ID = exports.NOVICE_INJURY_MAX_RATE = exports.NOVICE_INJURY_LV_REDUCE_RATE = exports.NOVICE_EQUIP_SLOTS_FIRST_ID = exports.NOVICE_FIRST_PAWN_SLOTS = exports.NOVICE_POLICY_SLOTS = exports.NOVICE_GO_HOSPITAL_CHANCE = exports.NOVICE_ENEMY_MAINCITY_POINTS = exports.NOVICE_ENEMY_MAINCITY_OTHER_INDEXS = exports.NOVICE_ENEMY_MAINCITY_INDEX = exports.NOVICE_MAIN_MAX_LEVEL = exports.NOVICE_FIRST_BUILD_CREATE_MUL = exports.NOVICE_BUILD_CREATE_MUL = exports.NOVICE_FIRST_FORGE_SPEED_MUL = exports.NOVICE_FORGE_SPEED_MUL = exports.NOVICE_FIRST_RECRUIT_SPEED_MUL = exports.NOVICE_RECRUIT_SPEED_MUL = exports.NOVICE_RECRUIT_UP_COUNT = exports.NOVICE_BUILD_SPEED_MUL = exports.NOVICE_FIRST_BATTLE_ENEMY_HP = exports.NOVICE_FIRST_BATTLE_PAWN_POINT = exports.NOVICE_ARMY_MOVE_MUL = exports.NOVICE_FIRST_MOVE_MUL = exports.NOVICE_IGNORE_BUILD = exports.NOVICE_MAINCITY_POINTS = exports.NOVICE_MAINCITY_OTHER_INDEXS = exports.NOVICE_MAINCITY_INDEX = exports.NOVICE_MAP_SIZE = void 0;
// 新手村地图大小
var NOVICE_MAP_SIZE = cc.v2(50, 50);
exports.NOVICE_MAP_SIZE = NOVICE_MAP_SIZE;
// 主城位置
var NOVICE_MAINCITY_INDEX = 1275;
exports.NOVICE_MAINCITY_INDEX = NOVICE_MAINCITY_INDEX;
var NOVICE_MAINCITY_OTHER_INDEXS = [1276, 1325, 1326];
exports.NOVICE_MAINCITY_OTHER_INDEXS = NOVICE_MAINCITY_OTHER_INDEXS;
var NOVICE_MAINCITY_POINTS = [cc.v2(25, 25), cc.v2(25, 26), cc.v2(26, 25), cc.v2(26, 26)];
exports.NOVICE_MAINCITY_POINTS = NOVICE_MAINCITY_POINTS;
//敌人主城位置
var NOVICE_ENEMY_MAINCITY_INDEX = 870;
exports.NOVICE_ENEMY_MAINCITY_INDEX = NOVICE_ENEMY_MAINCITY_INDEX;
var NOVICE_ENEMY_MAINCITY_OTHER_INDEXS = [871, 920, 921];
exports.NOVICE_ENEMY_MAINCITY_OTHER_INDEXS = NOVICE_ENEMY_MAINCITY_OTHER_INDEXS;
var NOVICE_ENEMY_MAINCITY_POINTS = [cc.v2(20, 17), cc.v2(21, 17), cc.v2(20, 18), cc.v2(21, 18)];
exports.NOVICE_ENEMY_MAINCITY_POINTS = NOVICE_ENEMY_MAINCITY_POINTS;
// 新手村忽略的建筑
var NOVICE_IGNORE_BUILD = {
    2010: true,
    2011: true,
    2005: true,
    2006: true,
    2012: true,
    2013: true,
    2015: true,
};
exports.NOVICE_IGNORE_BUILD = NOVICE_IGNORE_BUILD;
// 新手村主城最大等级
var NOVICE_MAIN_MAX_LEVEL = 10;
exports.NOVICE_MAIN_MAX_LEVEL = NOVICE_MAIN_MAX_LEVEL;
// 第一次战斗时 移动加速倍数
var NOVICE_FIRST_MOVE_MUL = 30;
exports.NOVICE_FIRST_MOVE_MUL = NOVICE_FIRST_MOVE_MUL;
// 新手村行军倍数
var NOVICE_ARMY_MOVE_MUL = 5;
exports.NOVICE_ARMY_MOVE_MUL = NOVICE_ARMY_MOVE_MUL;
// 新手村建筑升级倍速
var NOVICE_BUILD_SPEED_MUL = 6.67;
exports.NOVICE_BUILD_SPEED_MUL = NOVICE_BUILD_SPEED_MUL;
// -1表示不限制
var NOVICE_RECRUIT_UP_COUNT = -1;
exports.NOVICE_RECRUIT_UP_COUNT = NOVICE_RECRUIT_UP_COUNT;
// 新手村招募加速倍数
var NOVICE_FIRST_RECRUIT_SPEED_MUL = 20;
exports.NOVICE_FIRST_RECRUIT_SPEED_MUL = NOVICE_FIRST_RECRUIT_SPEED_MUL;
var NOVICE_RECRUIT_SPEED_MUL = 20;
exports.NOVICE_RECRUIT_SPEED_MUL = NOVICE_RECRUIT_SPEED_MUL;
// 新手村打造加速倍数
var NOVICE_FIRST_FORGE_SPEED_MUL = 5;
exports.NOVICE_FIRST_FORGE_SPEED_MUL = NOVICE_FIRST_FORGE_SPEED_MUL;
var NOVICE_FORGE_SPEED_MUL = 5;
exports.NOVICE_FORGE_SPEED_MUL = NOVICE_FORGE_SPEED_MUL;
// 新手村建造加速倍数
var NOVICE_FIRST_BUILD_CREATE_MUL = 1;
exports.NOVICE_FIRST_BUILD_CREATE_MUL = NOVICE_FIRST_BUILD_CREATE_MUL;
var NOVICE_BUILD_CREATE_MUL = 1;
exports.NOVICE_BUILD_CREATE_MUL = NOVICE_BUILD_CREATE_MUL;
// 新手村医疗加速倍数
var NOVICE_HOSPITAL_SPEED_MUL = 4;
exports.NOVICE_HOSPITAL_SPEED_MUL = NOVICE_HOSPITAL_SPEED_MUL;
// 第一次战斗时 士兵的位置
var NOVICE_FIRST_BATTLE_PAWN_POINT = [cc.v2(4, 4), cc.v2(6, 3), cc.v2(6, 4)];
exports.NOVICE_FIRST_BATTLE_PAWN_POINT = NOVICE_FIRST_BATTLE_PAWN_POINT;
// 第一次战斗时 野怪的血量
var NOVICE_FIRST_BATTLE_ENEMY_HP = {
    4101: 22,
    4102: 30,
};
exports.NOVICE_FIRST_BATTLE_ENEMY_HP = NOVICE_FIRST_BATTLE_ENEMY_HP;
// 各等级士兵战败后回馆概率(百分比)
var NOVICE_GO_HOSPITAL_CHANCE = {
    1: 10,
    2: 20,
    3: 30,
    4: 40,
    5: 50,
    6: 60,
};
exports.NOVICE_GO_HOSPITAL_CHANCE = NOVICE_GO_HOSPITAL_CHANCE;
// 每个等级治疗士兵消耗粮食所占百分比 k=>lv v=>百分比
var NOVICE_CURE_RES_PARAM_MAP = {
    1: 0.2,
    2: 0.4,
    3: 0.5,
    4: 0.6,
    5: 0.7,
    6: 0.8,
};
exports.NOVICE_CURE_RES_PARAM_MAP = NOVICE_CURE_RES_PARAM_MAP;
// 每个等级治疗士兵消耗时间所占百分比 k=>lv v=>百分比
var NOVICE_CURE_TIME_PARAM_MAP = {
    1: 0.2,
    2: 0.4,
    3: 0.5,
    4: 0.6,
    5: 0.7,
    6: 0.8,
};
exports.NOVICE_CURE_TIME_PARAM_MAP = NOVICE_CURE_TIME_PARAM_MAP;
var NOVICE_INJURY_MAX_RATE = 100; //伤兵最大治疗率
exports.NOVICE_INJURY_MAX_RATE = NOVICE_INJURY_MAX_RATE;
var NOVICE_INJURY_LV_REDUCE_RATE = { 1: 1, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5 }; //伤兵死亡次数减少概率
exports.NOVICE_INJURY_LV_REDUCE_RATE = NOVICE_INJURY_LV_REDUCE_RATE;
var NOVICE_FIRST_PAWN_SLOTS = [3201, 3204, 3206]; //新手首个士兵槽位
exports.NOVICE_FIRST_PAWN_SLOTS = NOVICE_FIRST_PAWN_SLOTS;
var NOVICE_POLICY_SLOTS = [1001, 1042, 1044, 1009, 1014, 1028]; //新手引导固定这6个政策可选
exports.NOVICE_POLICY_SLOTS = NOVICE_POLICY_SLOTS;
var NOVICE_EQUIP_SLOTS_FIRST_ID = 6013; //首个装备-流星锤
exports.NOVICE_EQUIP_SLOTS_FIRST_ID = NOVICE_EQUIP_SLOTS_FIRST_ID;
var NOVICE_FIRST_STONE_TREASURE_ID = 511; //石头地首个宝箱id
exports.NOVICE_FIRST_STONE_TREASURE_ID = NOVICE_FIRST_STONE_TREASURE_ID;
//土地宝箱奖励，1：荒地，3：粮食地，4：木头地，5：石头地，key：类型_等级，value：宝箱ID
var NOVICE_LAND_TREASURE_REWARD_MAP = {
    '1_1': [1002, [1006, 1007], [1008, 1009]],
    '3_2': [[1010, 1011, 1012]],
    '3_3': [],
    '3_4': [],
    '3_5': [],
    '4_2': [[1013, 1014, 1015]],
    '4_3': [],
    '4_4': [],
    '4_5': [],
    '5_2': [[NOVICE_FIRST_STONE_TREASURE_ID, 1016, 1017], [1018, 1019]],
    '5_3': [[502, 502, 502, 502, 502, NOVICE_FIRST_STONE_TREASURE_ID]],
    '5_4': [],
    '5_5': [],
};
exports.NOVICE_LAND_TREASURE_REWARD_MAP = NOVICE_LAND_TREASURE_REWARD_MAP;
var NOVICE_DEFAULT_GOLD_COUNT = 30; //新手默认金币
exports.NOVICE_DEFAULT_GOLD_COUNT = NOVICE_DEFAULT_GOLD_COUNT;
var NOVICE_DEFAULT_WARTOKEN_COUNT = 0; //新手默认兵符，额外增加的兵符
exports.NOVICE_DEFAULT_WARTOKEN_COUNT = NOVICE_DEFAULT_WARTOKEN_COUNT;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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