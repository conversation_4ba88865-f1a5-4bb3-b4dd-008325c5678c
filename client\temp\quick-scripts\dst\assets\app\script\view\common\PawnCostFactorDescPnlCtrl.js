
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PawnCostFactorDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '17d18Lpc/RHuZq2bQuTcv1j', 'PawnCostFactorDescPnlCtrl');
// app/script/view/common/PawnCostFactorDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var PawnCostFactorDescPnlCtrl = /** @class */ (function (_super) {
    __extends(PawnCostFactorDescPnlCtrl, _super);
    function PawnCostFactorDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    PawnCostFactorDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PawnCostFactorDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    PawnCostFactorDescPnlCtrl.prototype.onEnter = function (data) {
        var _a, _b;
        var pawnNode = this.contentNode_.Child('pawn');
        var root = pawnNode.Child('root'), isMachine = data.isMachine();
        this.contentNode_.Child('1').setLocaleKey(isMachine ? 'ui.machine_cost_factor_desc' : 'ui.pawn_cost_factor_desc');
        root.Child('0').active = root.Child('1').active = !isMachine;
        root.Child('2').active = isMachine;
        ResHelper_1.resHelper.loadPawnHeadIcon(data.id, root.Child('role'), this.key);
        var val = ((_a = GameHelper_1.gameHpr.getPawnCost(data.id, 0, data.baseJson).find(function (m) { return m.type === Enums_1.CType.CEREAL; })) === null || _a === void 0 ? void 0 : _a.count) || 0;
        var factor = Math.round((val / Constant_1.PAWN_COST_FACTOR_BASE_VALUE) * 100), color = factor <= 125 ? '#4AB32E' : '#D7634D';
        pawnNode.Child('desc').setLocaleKey('ui.pawn_cost_factor_' + (GameHelper_1.gameHpr.isInLobby() ? 'lobby' : 'game'), data.name, "<color=" + color + ">" + factor + "%</c>");
        var costNode = this.contentNode_.Child('cost'), isUp = data.lv > 1;
        var costMap = {};
        GameHelper_1.gameHpr.getPawnCost(data.id, data.lv - 1, isUp ? data.attrJson : data.baseJson).forEach(function (m) { return costMap[m.type] = m; });
        costNode.Child('name').setLocaleKey(isUp ? 'ui.pawn_leveling' : 'ui.pawn_drill');
        costNode.Child('cereal/val', cc.Label).string = (((_b = costMap[Enums_1.CType.CEREAL]) === null || _b === void 0 ? void 0 : _b.count) || 0) + '';
        if (costNode.Child('exp_book').active = !!costMap[Enums_1.CType.EXP_BOOK]) {
            costNode.Child('exp_book/val', cc.Label).string = costMap[Enums_1.CType.EXP_BOOK].count + '';
        }
        costNode.Child('time/val', cc.Label).string = ut.secondFormat(GameHelper_1.gameHpr.getPawnCostTime(data.id, data.lv - 1, isUp ? data.attrJson : data.baseJson), 'h:mm:ss');
    };
    PawnCostFactorDescPnlCtrl.prototype.onRemove = function () {
    };
    PawnCostFactorDescPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    PawnCostFactorDescPnlCtrl = __decorate([
        ccclass
    ], PawnCostFactorDescPnlCtrl);
    return PawnCostFactorDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PawnCostFactorDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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