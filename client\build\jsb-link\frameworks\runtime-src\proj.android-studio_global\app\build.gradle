import org.apache.tools.ant.taskdefs.condition.Os

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics' 

android {
    namespace "twgame.global.acers"
    compileSdkVersion PROP_COMPILE_SDK_VERSION.toInteger()
    buildToolsVersion PROP_BUILD_TOOLS_VERSION

    defaultConfig {
        applicationId "twgame.global.acers"
        minSdkVersion PROP_MIN_SDK_VERSION
        targetSdkVersion PROP_TARGET_SDK_VERSION
        versionCode 47
        versionName "4.0.0"

        externalNativeBuild {
            ndkBuild {
                if (!project.hasProperty("PROP_NDK_MODE") || PROP_NDK_MODE.compareTo('none') != 0) {
                    // skip the NDK Build step if PROP_NDK_MODE is none
                    targets 'cocos2djs'
                    arguments 'NDK_TOOLCHAIN_VERSION=clang'
                    
                    def module_paths = [project.file("/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x"),
                                        project.file("/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos"),
                                        project.file("/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/external")]
                    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
                        arguments 'NDK_MODULE_PATH=' + module_paths.join(";")
                    }
                    else {
                        arguments 'NDK_MODULE_PATH=' + module_paths.join(':')
                    }

                    arguments '-j' + Runtime.runtime.availableProcessors()
                }
            }
            ndk {
                abiFilters PROP_APP_ABI.split(':')
            }
        }
    }

    sourceSets.main {
        java.srcDirs "../src", "src"
        res.srcDirs "../res", 'res'
        jniLibs.srcDirs "../libs", 'libs'
        manifest.srcFile "AndroidManifest.xml"
    }

    externalNativeBuild {
        ndkBuild {
            if (!project.hasProperty("PROP_NDK_MODE") || PROP_NDK_MODE.compareTo('none') != 0) {
                // skip the NDK Build step if PROP_NDK_MODE is none
                path "jni/Android.mk"
            }
        }
    }

    signingConfigs {

       release {
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }

        debug {
            storeFile file(RELEASE_STORE_FILE)
            storePassword RELEASE_STORE_PASSWORD
            keyPassword RELEASE_KEY_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
        }
    }

    buildTypes {
        release {
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }

            externalNativeBuild {
                ndkBuild {
                    arguments 'NDK_DEBUG=0'
                }
            }
        }

        debug {
            debuggable true
            jniDebuggable true
            renderscriptDebuggable true
            externalNativeBuild {
                ndkBuild {
                    arguments 'NDK_DEBUG=1'
                }
            }
        }
    }
}

android.applicationVariants.all { variant ->
    // delete previous files first
    delete "${buildDir}/intermediates/merged_assets/${variant.dirName}"

    variant.mergeAssets.doLast {
        def sourceDir = "${buildDir}/../../../../.."

        copy {
            from "${sourceDir}"
            include "assets/**"
            include "src/**"
            include "jsb-adapter/**"
            into outputDir
        }

        copy {
            from "${sourceDir}/main.js"
            from "${sourceDir}/project.json"
            into outputDir
        }
    }
}

repositories {
    mavenCentral()
    maven {url 'https://android-sdk.is.com/'}

    maven {url 'https://artifact.bytedance.com/repository/pangle'}
}

dependencies {
    implementation fileTree(dir: '../libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation fileTree(dir: "/Applications/CocosCreator/Creator/2.4.11/CocosCreator.app/Contents/Resources/cocos2d-x/cocos/platform/android/java/libs", include: ['*.jar'])
    implementation project(':libcocos2dx')

    implementation 'com.facebook.android:facebook-android-sdk:16.0.1'
    implementation 'com.alibaba:fastjson:1.1.72.android'
    implementation 'com.google.android.gms:play-services-auth:20.4.1'

//    appsflyer
    implementation 'com.appsflyer:af-android-sdk:6.14.2'
    implementation "com.android.installreferrer:installreferrer:2.2"

    implementation 'com.android.billingclient:billing:8.0.0'

    implementation platform('com.google.firebase:firebase-bom:32.3.1')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.google.firebase:firebase-auth'

    implementation 'com.google.android.gms:play-services-appset:16.0.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0'
    implementation 'com.google.android.gms:play-services-basement:18.1.0'

//    implementation 'com.ironsource.sdk:mediationsdk:8.5.0'
//
//// Add Applovin Network
//    implementation 'com.ironsource.adapters:applovinadapter:4.3.48'
//    implementation 'com.applovin:applovin-sdk:13.0.1'
//// Add Facebook Network
//    implementation 'com.ironsource.adapters:facebookadapter:4.3.48'
//    implementation 'com.facebook.android:audience-network-sdk:6.18.0'
//// Add AdMob and Ad Manager Network
//    implementation 'com.google.android.gms:play-services-ads:23.5.0'
//    implementation 'com.ironsource.adapters:admobadapter:4.3.47'
//// Add Pangle Network
//    implementation 'com.ironsource.adapters:pangleadapter:4.3.31'
//    implementation 'com.pangle.global:ads-sdk:6.3.0.4'
}