
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/WorldMapTouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '970a1vU0DdPV5VbnRcncgU2', 'WorldMapTouchCmpt');
// app/script/view/main/WorldMapTouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于移动世界地图的触摸组建
 */
var WorldMapTouchCmpt = /** @class */ (function (_super) {
    __extends(WorldMapTouchCmpt, _super);
    function WorldMapTouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ZOOM_RATIO_RANGE = cc.v2(1, 4);
        _this.touchs = [];
        _this.touchCount = 2;
        _this.startZoomRatio = 0;
        _this.startTouchDis = 0;
        _this.startWorldPoint = cc.v2();
        _this.root = null;
        _this.touchCallback = null; //触摸回调
        _this.isDownClick = false; //是否按下点击
        _this.recordRootPosition = cc.v2();
        _this.zoomRatio = 0;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        return _this;
    }
    WorldMapTouchCmpt.prototype.onLoad = function () {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.MOUSE_MOVE, this.onMouseMove, this);
        this.node.on(cc.Node.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
    };
    WorldMapTouchCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    WorldMapTouchCmpt.prototype.init = function (root) {
        this.root = root;
        return this;
    };
    WorldMapTouchCmpt.prototype.enter = function (zoom, touchCallback) {
        this.reset();
        this.zoomRatio = zoom;
        this.touchCallback = touchCallback;
    };
    WorldMapTouchCmpt.prototype.clean = function () {
        this.touchCallback = null;
    };
    WorldMapTouchCmpt.prototype.reset = function () {
        this.touchs.length = 0;
        this.startZoomRatio = 0;
        this.touchCount = 2;
        this.startTouchDis = 0;
        this.startWorldPoint.set2(0, 0);
    };
    WorldMapTouchCmpt.prototype.recordPosition = function () {
        this.recordRootPosition.set(this.root.getPosition(this._temp_vec2_2));
    };
    WorldMapTouchCmpt.prototype.convertWorldSub = function (startLocation, location) {
        this.root.convertToNodeSpaceAR(startLocation, this._temp_vec2_4);
        this.root.convertToNodeSpaceAR(location, this._temp_vec2_5);
        return this._temp_vec2_4.subSelf(this._temp_vec2_5);
    };
    WorldMapTouchCmpt.prototype.onTouchStart = function (event) {
        var id = event.getID(), touches = event.getTouches();
        if (touches.length < this.touchs.length) {
            this.touchs.length = 0;
        }
        if (this.touchs.some(function (m) { return m.id === id; }) || this.touchs.length >= this.touchCount) {
            return;
        }
        this.isDownClick = true;
        this.touchs.push({ id: id, startLocation: event.getStartLocation(), location: event.getLocation() });
        if (this.touchs.length === 2) {
            // 记录当前摄像机缩放比
            this.startZoomRatio = this.zoomRatio;
            // 记录两点的距离
            var _a = __read(this.touchs, 2), a = _a[0], b = _a[1];
            var c = a.location.sub(b.location, this._temp_vec2_1);
            this.startTouchDis = c.mag();
            // 记录中点 以及算出在世界坐标的位置
            var startPoint = c.divSelf(2).addSelf(b.location);
            this.startWorldPoint.set(this.root.convertToNodeSpaceAR(startPoint, this._temp_vec2_2));
        }
        else {
            this.recordPosition();
            this.startTouchDis = -1;
        }
    };
    WorldMapTouchCmpt.prototype.onTouchMove = function (event) {
        var id = event.getID();
        var touch = this.touchs.find(function (m) { return m.id === id; });
        if (!touch) {
            return;
        }
        touch.location.set(event.getLocation());
        if (this.touchs.length === 2) {
            // 获取两点的距离 然后根据上一次记录的距离 计算出比例
            var _a = __read(this.touchs, 2), a = _a[0], b = _a[1];
            var dis = a.location.sub(b.location, this._temp_vec2_1).mag();
            if (dis === this.startTouchDis) {
                return;
            }
            var isMin = dis < this.startTouchDis;
            var delta = isMin ? -(this.startTouchDis / dis - 1) : (dis / this.startTouchDis - 1);
            var r = isMin ? 0.6 : 0.5;
            // cc.log('dis', dis, this.startTouchDis, delta)
            var val = cc.misc.clampf(this.startZoomRatio + delta * r, this.ZOOM_RATIO_RANGE.x, this.ZOOM_RATIO_RANGE.y);
            if (this.zoomRatio !== val) {
                this.updateZoom(val);
            }
        }
        else {
            var a = this.convertWorldSub(touch.startLocation, touch.location), mag = a.mag();
            // 只要滑的距离够长 这次就不算为点击了
            if (this.isDownClick && mag > Constant_1.CLICK_SPACE) { //拖动
                this.isDownClick = false;
            }
            // 拖动
            if (mag >= 4) {
                this.touchCallback({ type: 'drag', position: this.recordRootPosition.sub(a, this._temp_vec2_3) });
            }
        }
    };
    WorldMapTouchCmpt.prototype.onTouchEnd = function (event) {
        var id = event.getID();
        var i = this.touchs.findIndex(function (m) { return m.id === id; });
        if (i === -1) {
            return;
        }
        var touch = this.touchs.splice(i, 1)[0];
        touch.location.set(event.getLocation());
        if (this.touchs.length === 1) {
            touch = this.touchs[0];
            touch.startLocation.set(touch.location);
            this.recordPosition();
        }
        else if (this.startTouchDis !== -1) { //这里表示是触摸了两个点之后松开 要做摄像机自动还原缩放 
        }
        else if (this.isDownClick && this.convertWorldSub(touch.startLocation, touch.location).mag() <= Constant_1.CLICK_SPACE) { //如果只有一个 那么就是点击
            var position = this.root.convertToNodeSpaceAR(touch.location, this._temp_vec2_2);
            this.touchCallback({ type: 'click', position: position });
        }
    };
    // 鼠标移动事件
    WorldMapTouchCmpt.prototype.onMouseMove = function (event) {
        this.startWorldPoint.set(this.root.convertToNodeSpaceAR(event.getLocation(), this._temp_vec2_2));
    };
    // 鼠标滚动事件
    WorldMapTouchCmpt.prototype.onMouseWheel = function (event) {
        var deltaScale = event.getScrollY() * 0.01;
        var val = cc.misc.clampf(this.zoomRatio + deltaScale * 0.2, this.ZOOM_RATIO_RANGE.x, this.ZOOM_RATIO_RANGE.y);
        if (this.zoomRatio != val) {
            this.updateZoom(val);
        }
    };
    WorldMapTouchCmpt.prototype.updateZoom = function (val) {
        var zoom = this.zoomRatio;
        var pointerPosition = this._temp_vec2_1.set(this.startWorldPoint);
        var prePointerPos = this._temp_vec2_5.set(pointerPosition);
        this.zoomRatio = val;
        this.startWorldPoint.set(pointerPosition.divSelf(zoom).mulSelf(val));
        var diff = pointerPosition.subSelf(prePointerPos);
        var pos = this.root.getPosition(this._temp_vec2_4).subSelf(diff);
        this.touchCallback({ type: 'zoom', zoom: val, position: pos });
    };
    WorldMapTouchCmpt = __decorate([
        ccclass
    ], WorldMapTouchCmpt);
    return WorldMapTouchCmpt;
}(cc.Component));
exports.default = WorldMapTouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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