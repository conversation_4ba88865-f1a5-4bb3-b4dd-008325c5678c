
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceAreaObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2d2a1o6i35CpIlHW/8vcsS5', 'NoviceAreaObj');
// app/script/model/guide/NoviceAreaObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var AreaObj_1 = require("../area/AreaObj");
var ArmyObj_1 = require("../area/ArmyObj");
var BuildObj_1 = require("../area/BuildObj");
var NoviceConfig_1 = require("./NoviceConfig");
// 一个区域
var NoviceAreaObj = /** @class */ (function (_super) {
    __extends(NoviceAreaObj, _super);
    function NoviceAreaObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.landId = 0;
        _this.landLv = 0;
        _this.landType = 0;
        _this.doorPoints = []; //城门口位置 固定4个 0.上 1.右 2.下 3.左
        _this.battleMapPoints = [];
        _this.proxyAO = null; //战斗时的代理区域对象 目前仅用于新手村
        return _this;
    }
    NoviceAreaObj.prototype.init = function (data) {
        var _a, _b, _c, _d;
        this.index = data.index;
        this.landId = data.landId || 0;
        var landInfo = assetsMgr.getJsonData('land', this.landId);
        this.landLv = landInfo.lv;
        this.landType = landInfo.type;
        if (!data.hp) {
            data = GameHelper_1.gameHpr.getAreaPawnConfInfo(data.index, data.landId, this.getToMapCellDis(data.index));
        }
        this.owner = data.owner || '';
        this.cityId = data.cityId || 0;
        this.curHp = (_b = (_a = data.hp) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : 0;
        this.maxHp = (_d = (_c = data.hp) === null || _c === void 0 ? void 0 : _c[1]) !== null && _d !== void 0 ? _d : 0;
        this.wall = null;
        this.walls = [];
        this.builds = (data.builds || []).map(function (m) { return new BuildObj_1.default().fromSvr(m); });
        this.updateCity(this.cityId, true);
        this.updateArmys(data.armys);
        this.updateSize();
        return this;
    };
    NoviceAreaObj.prototype.getToMapCellDis = function (index) {
        var t = MapHelper_1.mapHelper.indexToPoint(index).clone();
        var s = MapHelper_1.mapHelper.getMinDis(t, NoviceConfig_1.NOVICE_MAINCITY_POINTS);
        var d = MapHelper_1.mapHelper.getPointToPointDis(s, t);
        return d;
    };
    NoviceAreaObj.prototype.getToMapCellDisEnemy = function (index) {
        var t = MapHelper_1.mapHelper.indexToPoint(index).clone();
        var s = MapHelper_1.mapHelper.getMinDis(t, NoviceConfig_1.NOVICE_ENEMY_MAINCITY_POINTS);
        var d = MapHelper_1.mapHelper.getPointToPointDis(s, t);
        return d;
    };
    NoviceAreaObj.prototype.updateSize = function () {
        _super.prototype.updateSize.call(this);
        this.doorPoints = this.getDoorPoints(this.areaSize, this.buildSize, this.buildOrigin);
        this.battleMapPoints = this.getBattlePoints(this.areaSize, this.buildSize, this.buildOrigin, this.passPoints);
    };
    NoviceAreaObj.prototype.toBaseData = function () {
        return {
            index: this.index,
            landId: this.landId,
            owner: this.owner,
            cityId: this.cityId,
            hp: [this.curHp, this.maxHp],
        };
    };
    NoviceAreaObj.prototype.strip = function () {
        // let area = this.proxyAO ?? this
        // return {
        //     index: this.index,
        //     owner: this.owner,
        //     cityId: this.cityId,
        //     builds: this.builds.map(m => m.strip()),
        //     battle: area.getFspModel()?.strip(),
        //     hp: [this.curHp, this.maxHp],
        //     armys: area.armys.filter(m => m.state !== ArmyState.MARCH).map(m => m.strip()),
        // }
        return this.toDB();
    };
    NoviceAreaObj.prototype.toDB = function () {
        var _a;
        var area = (_a = this.proxyAO) !== null && _a !== void 0 ? _a : this;
        var model = area.getFspModel();
        var battle = null;
        if (model) {
            battle = model.strip();
            //@ts-ignore
            battle.fighters = model.battleController.fighters.map(function (m) {
                if (m.isTower()) {
                    // 序列化塔的信息
                    var ret = m.strip();
                    var towerInfo = {
                        point: m.entity.point,
                        towerId: m.entity.id,
                        towerLv: m.entity.lv,
                    };
                    return Object.assign(ret, towerInfo);
                }
                else {
                    return m.strip();
                }
            });
        }
        return {
            index: this.index,
            owner: this.owner,
            cityId: this.cityId,
            landId: this.landId,
            builds: this.builds.map(function (m) { return m.strip(); }),
            battle: battle,
            hp: [area.curHp, area.maxHp],
            armys: area.armys.map(function (m) { return m.strip(); }),
            buildInfo: null,
        };
    };
    NoviceAreaObj.prototype.isBattleing = function () {
        var _a;
        return !!((_a = GameHelper_1.gameHpr.areaCenter.getArea(this.index)) === null || _a === void 0 ? void 0 : _a.isBattleing());
    };
    NoviceAreaObj.prototype.getLandType = function () {
        return this.landType;
    };
    NoviceAreaObj.prototype.getDoorPoints = function (areaSize, buildSize, buildOrigin) {
        var cx = Math.floor(areaSize.x / 2), cy = Math.floor(areaSize.y / 2);
        var points = [cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0)];
        points[0].set2(cx, buildOrigin.y + buildSize.y); //0.上
        points[1].set2(buildOrigin.x + buildSize.x, cy); //1.右
        points[2].set2(cx, buildOrigin.y - 1); //2.下
        points[3].set2(buildOrigin.x - 1, cy); //3.左
        return points;
    };
    // 获取战斗区域位置
    NoviceAreaObj.prototype.getBattlePoints = function (areaSize, buildSize, buildOrigin, pass) {
        var arr = [];
        var bulidPoints = MapHelper_1.mapHelper.genPointsBySize(buildSize, buildOrigin); //建造所有位置
        for (var x = 0; x < areaSize.x; x++) {
            var _loop_1 = function (y) {
                var point = cc.v2(x, y);
                if (pass.some(function (m) { return m.equals(point); }) || bulidPoints.some(function (m) { return m.equals(point); })) {
                    return "continue";
                }
                arr.push(point);
            };
            for (var y = 0; y < areaSize.y; y++) {
                _loop_1(y);
            }
        }
        return arr;
    };
    NoviceAreaObj.prototype.getEnemyArmys = function (attacker) {
        var _a, _b;
        if (attacker === void 0) { attacker = ''; }
        if (!attacker || attacker === GameHelper_1.gameHpr.getUid()) {
            return ((_a = GameHelper_1.gameHpr.getAreaPawnConfInfo(this.index, this.landId, this.getToMapCellDis(this.index))) === null || _a === void 0 ? void 0 : _a.armys) || [];
        }
        return ((_b = GameHelper_1.gameHpr.getAreaPawnConfInfo(this.index, this.landId, this.getToMapCellDisEnemy(this.index))) === null || _b === void 0 ? void 0 : _b.armys) || [];
    };
    // 删除军队
    NoviceAreaObj.prototype.removeArmy = function (uid) {
        var army = this.armys.remove('uid', uid);
        if (army) {
            var server = GameHelper_1.gameHpr.noviceServer;
            if (army.owner) {
                server.changeArmyDistIndex(army.owner, uid, -1);
            }
            if (!army.isBattleing()) { //战斗中不用通知
                server.notifyArea(this.index, Enums_1.NotifyType.REMOVE_ARMY, uid);
            }
        }
    };
    // 更新军队信息
    NoviceAreaObj.prototype.updateArmy = function (data) {
        // if (!data.pawns?.length && !data.drillPawns?.length) {
        //     return this.removeArmy(data.uid)
        // }
        var army = this.armys.find(function (m) { return m.uid === data.uid; });
        if (army) {
            army.fromSvr(data);
        }
        else {
            this.armys.add(new ArmyObj_1.default().fromSvr(data));
        }
    };
    NoviceAreaObj.prototype.getHasArmyPlayers = function () {
        var arr = [];
        this.armys.forEach(function (m) {
            if (m.state !== Enums_1.ArmyState.MARCH && m.owner && !arr.has(m.owner)) {
                arr.push(m.owner);
            }
        });
        return arr;
    };
    // 更新血量
    NoviceAreaObj.prototype.updateMaxHP = function () {
        // 获取对应建筑
        var pawnId = 0;
        if (this.cityId === Constant_1.CITY_MAIN_NID) {
            pawnId = 7003;
        }
        else if (this.cityId === Constant_1.CITY_FORT_NID) {
            pawnId = 7002;
        }
        else if (this.owner) {
            pawnId = 7001;
        }
        var json = null;
        if (pawnId === 0) {
            json = assetsMgr.getJsonData('landAttr', this.landLv * 1000 + 1);
        }
        else {
            json = assetsMgr.getJsonData('pawnAttr', pawnId * 1000 + GameHelper_1.gameHpr.getPlayerTowerLvByPawn(GameHelper_1.gameHpr.getUid(), pawnId));
        }
        if (json) {
            this.maxHp = json.hp;
            if (this.owner && this.owner !== GameHelper_1.gameHpr.getUid()) {
                this.maxHp = 3;
            }
        }
        else {
            this.maxHp = 40;
        }
        this.curHp = this.maxHp;
    };
    NoviceAreaObj.prototype.isCanUpSpeedMarchCity = function () {
        return this.cityId === Constant_1.CITY_MAIN_NID || this.cityId === Constant_1.CITY_FORT_NID;
    };
    // 是否可以恢复士兵血量
    NoviceAreaObj.prototype.isRecoverPawnHP = function () {
        return this.isCanUpSpeedMarchCity();
    };
    NoviceAreaObj.prototype.recoverAllPawnHP = function () {
        this.armys.forEach(function (m) { return m.state !== Enums_1.ArmyState.FIGHT && m.recoverAllPawn(); });
    };
    NoviceAreaObj.prototype.getAllPawnPointsToMapBool = function (ignoreArmy) {
        var data = {};
        this.armys.forEach(function (m) {
            if (m.state === Enums_1.ArmyState.MARCH || m.uid === ignoreArmy) {
                return;
            }
            m.pawns.forEach(function (p) { return data[p.point.ID()] = true; });
        });
        return data;
    };
    NoviceAreaObj.prototype.getIdleBattlePointsToMapBool = function (ignoreArmy) {
        var _this = this;
        var data = {};
        var pawnPointMap = this.getAllPawnPointsToMapBool(ignoreArmy);
        this.battleMapPoints.forEach(function (p) {
            var key = p.ID();
            if (!pawnPointMap[key] && !_this.banPlacePawnPointMap[key]) {
                data[key] = true;
            }
        });
        return data;
    };
    // 获取士兵的位置 根据城门口开始
    NoviceAreaObj.prototype.getPawnPointsByDoor = function (ignoreArmy, dir, count) {
        var points = this.getIdleBattlePointsToMapBool(ignoreArmy);
        var i = 0;
        var list = [];
        while (count > 0 && i < 4) {
            var arr = this.getPawnPointsByDoorOne(points, this.doorPoints[dir], dir, count);
            count -= arr.length;
            list.pushArr(arr);
            dir = ut.loopValue(dir + 1, 4);
            i += 1;
        }
        return list;
    };
    // 一个方向
    NoviceAreaObj.prototype.getPawnPointsByDoorOne = function (points, start, dir, count) {
        var size = this.areaSize;
        var arr = [];
        var cnt = cc.v2(0, 0);
        var half = size.div(2);
        var sd = dir >= 2 ? -1 : 1;
        if (dir === 0) {
            half.y = size.y - start.y;
        }
        else if (dir === 1) {
            half.x = size.x - start.x;
        }
        else if (dir === 2) {
            half.y = start.y;
        }
        else if (dir === 3) {
            half.x = start.x;
        }
        var o = 0; //计数
        var v = 0; //变量
        var i = 0, pLen = size.x * size.y + 4;
        while (arr.length < count && i < pLen) {
            i += 1;
            var point = start.add(cnt);
            var key = point.ID();
            if (points[key]) {
                points[key] = false;
                arr.push(point);
            }
            o += 1;
            var d = 1;
            if (o % 2 != 0) {
                d = -1;
                v += 1;
            }
            if (dir % 2 == 0) {
                if (v <= half.x) {
                    cnt.x = v * d;
                }
                else {
                    var y = Math.abs(cnt.y);
                    if (y < half.y) {
                        cnt.y = (y + 1) * sd;
                    }
                    else {
                        sd *= -1;
                        cnt.y = sd;
                        half.y = size.y - half.y - 1;
                    }
                    cnt.x, o, v = 0, 0, 0;
                }
            }
            else {
                if (v <= half.y) {
                    cnt.y = v * d;
                }
                else {
                    var x = Math.abs(cnt.x);
                    if (x < half.x) {
                        cnt.x = (x + 1) * sd;
                    }
                    else {
                        sd *= -1;
                        cnt.x = sd;
                        half.x = size.x - half.x - 1;
                    }
                    cnt.y, o, v = 0, 0, 0;
                }
            }
        }
        return arr;
    };
    NoviceAreaObj.prototype.getBuildCanPlacePos = function (build) {
        var buildPointMap = {};
        this.builds.forEach(function (m) { return m.getActPoints().forEach(function (p) { return buildPointMap[p.ID()] = true; }); });
        var buildSizeX = this.buildSize.x - 2, buildSizeY = this.buildSize.y - 2;
        // 先看默认位置
        if (build.baseJson.pos) {
            var defaultPoint = ut.stringToVec2(build.baseJson.pos);
            if (!build.getActPoints(defaultPoint).some(function (m) { return buildPointMap[m.ID()] || m.x <= 0 || m.x > buildSizeX || m.y <= 0 || m.y > buildSizeY; })) {
                return defaultPoint;
            }
        }
        // 没有就随机找一个
        var arr = [], points = build.points;
        this.buildMapPoints.forEach(function (point) {
            if (!points.some(function (m) {
                var x = m.x + point.x, y = m.y + point.y;
                if (x <= 0 || x > buildSizeX || y <= 0 || y > buildSizeY) {
                    return true;
                }
                else {
                    return buildPointMap[x + '_' + y];
                }
            })) {
                arr.push(point);
            }
        });
        var cnt = arr.length;
        if (cnt === 0) {
            return null;
        }
        return arr[ut.random(0, cnt - 1)].clone();
    };
    NoviceAreaObj.prototype.getDrillPawnPoint = function () {
        var dir = 2; //城门下面
        var points = this.getPawnPointsByDoor('', dir, 1);
        if (points.length === 0) {
            return this.doorPoints[dir];
        }
        return points[0];
    };
    NoviceAreaObj.prototype.checkCanPlace = function (build, point) {
        var points = build.points, pointMap = {};
        points.forEach(function (m) { return pointMap[(m.x + point.x) + '_' + (m.y + point.y)] = true; });
        return this.builds.some(function (m) { return m.uid !== build.uid && !m.getActPoints().some(function (p) { return pointMap[p.ID()]; }); });
    };
    NoviceAreaObj.prototype.changePawnEquip = function (pawn, equip, syncEquip) {
        var pawns = [];
        if (syncEquip === 1) { //场景中的
            var owner_1 = pawn.owner;
            this.armys.forEach(function (army) {
                if (owner_1 === army.owner && army.state !== Enums_1.ArmyState.MARCH) {
                    pawns.pushArr(army.pawns);
                }
            });
        }
        else if (syncEquip === 2) { //军队中的
            var army = this.getArmyByUid(pawn.armyUid);
            if (army) {
                pawns.pushArr(army.pawns);
            }
        }
        else {
            pawns.push(pawn);
        }
        var datas = [];
        pawns.forEach(function (m) {
            if (m.id === pawn.id) {
                m.changeEquip(equip.strip(), false);
                datas.push(m.strip());
            }
        });
        return datas;
    };
    return NoviceAreaObj;
}(AreaObj_1.default));
exports.default = NoviceAreaObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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