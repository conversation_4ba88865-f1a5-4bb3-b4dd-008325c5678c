
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/ad/BaseRewardAd.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '67ed0Ayg2hBkIoCg0GeNb/7', 'BaseRewardAd');
// app/script/common/ad/BaseRewardAd.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../constant/Enums");
// 视频广告
var BaseRewardAd = /** @class */ (function () {
    function BaseRewardAd() {
        this.inited = false;
        this.state = Enums_1.AdState.NONE;
        this.noSuitAdCount = 0; //失败次数 用于是否要用分享广告
        this.reloadTime = 0; //重新加载时间
        this.isPause = false;
    }
    // 连续加载广告失败多久了
    BaseRewardAd.prototype.getFailTime = function () {
        return this.noSuitAdCount * 10 * ut.Time.Second;
    };
    BaseRewardAd.prototype.resetAdFailTime = function () {
    };
    // 与明早8点differ的时间距离现在还剩多长时间
    BaseRewardAd.prototype.getFixTimeSurplyTime = function (startTime, differ, day) {
        if (day === void 0) { day = 1; }
        if (startTime <= 0) {
            return 0;
        }
        var offset = ut.Time.Hour * 2; //固定偏移
        var now = Date.now() + offset;
        var date = new Date(startTime + offset);
        var time = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0).getTime(); //当前天的00:00
        time -= date.getTimezoneOffset() * ut.Time.Minute; //恒定值：-480
        time += differ * ut.Time.Hour + offset;
        time += ut.Time.Day * day;
        return Math.floor((time - now) * 0.001);
    };
    BaseRewardAd.prototype.pause = function () {
        if (!this.isPause) {
            this.isPause = true;
            cc.game.pause();
        }
    };
    BaseRewardAd.prototype.resume = function () {
        if (this.isPause) {
            this.isPause = false;
            cc.game.resume();
        }
    };
    BaseRewardAd.prototype.update = function (dt) { };
    BaseRewardAd.prototype.isReady = function () { return false; };
    BaseRewardAd.prototype.waitCacheReady = function () {
        return __awaiter(this, void 0, Promise, function () { return __generator(this, function (_a) {
            return [2 /*return*/, true];
        }); });
    };
    BaseRewardAd.prototype.show = function () {
        return __awaiter(this, void 0, Promise, function () { return __generator(this, function (_a) {
            return [2 /*return*/, false];
        }); });
    };
    return BaseRewardAd;
}());
exports.default = BaseRewardAd;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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