
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/SendAlliApplyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '81394tKUzVGjLWHP6W14znY', 'SendAlliApplyPnlCtrl');
// app/script/view/build/SendAlliApplyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SendAlliApplyPnlCtrl = /** @class */ (function (_super) {
    __extends(SendAlliApplyPnlCtrl, _super);
    function SendAlliApplyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.alliNameLbl_ = null; // path://root/name/alli_name_l
        _this.inputEb_ = null; // path://root/input_eb
        //@end
        _this.uid = '';
        _this.cb = null;
        return _this;
    }
    SendAlliApplyPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SendAlliApplyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SendAlliApplyPnlCtrl.prototype.onEnter = function (data, cb) {
        this.uid = data.uid;
        this.cb = cb;
        this.alliNameLbl_.string = data.name;
        this.inputEb_.string = '';
    };
    SendAlliApplyPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(false);
        this.cb = null;
    };
    SendAlliApplyPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    SendAlliApplyPnlCtrl.prototype.onClickOk = function (event, data) {
        var desc = this.inputEb_.string.trim();
        if (ut.getStringLen(desc) > 36) {
            desc = ut.nameFormator(desc, 18, '');
        }
        else if (GameHelper_1.gameHpr.getTextNewlineCount(desc) >= 1) {
            var arr = desc.split('\n');
            if (arr.length >= 2) {
                desc = arr[0];
            }
        }
        this.do(desc);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SendAlliApplyPnlCtrl.prototype.do = function (desc) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.world.applyJoinAlliance(this.uid, desc)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.cb && this.cb(true);
                            this.cb = null;
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SendAlliApplyPnlCtrl = __decorate([
        ccclass
    ], SendAlliApplyPnlCtrl);
    return SendAlliApplyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SendAlliApplyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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