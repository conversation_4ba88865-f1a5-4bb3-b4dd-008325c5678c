
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/NoLongerTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '34d073y2AdNBoQ9CQ+iERhB', 'NoLongerTipPnlCtrl');
// app/script/view/common/NoLongerTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var NoLongerTipPnlCtrl = /** @class */ (function (_super) {
    __extends(NoLongerTipPnlCtrl, _super);
    function NoLongerTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.rootNode_ = null; // path://root_n
        _this.contentLbl_ = null; // path://root_n/content_l
        _this.noLongerTge_ = null; // path://root_n/no_longer_t
        _this.buttonsNode_ = null; // path://root_n/buttons_nbe_n
        //@end
        _this.noKey = '';
        _this.okCb = null;
        _this.cancelCb = null;
        return _this;
    }
    NoLongerTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoLongerTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    NoLongerTipPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.noKey = data.noKey;
        this.okCb = data.ok;
        this.cancelCb = data.cancel;
        (_a = this.contentLbl_).setLocaleKey.apply(_a, __spread([data.content], (data.params || [])));
        this.contentLbl_._forceUpdateRenderData();
        this.rootNode_.height = Math.max(360, this.contentLbl_.node.height + 300);
        this.rootNode_.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
        this.noLongerTge_.isChecked = !!data.select;
        // 是否显示取消按钮
        this.buttonsNode_.Child('cancel').active = !!this.cancelCb;
        // 设置按钮名字
        this.buttonsNode_.Child('ok/val', cc.Label).setLocaleKey(data.okText || 'login.button_ok');
        this.buttonsNode_.Child('cancel/val', cc.Label).setLocaleKey(data.cancelText || 'login.button_cancel');
        if (data.hideClose) { //隐藏背景关闭
            this.closeNode_.active = false;
        }
        else if (data.select) { //如果默认选中得 就让他延迟关闭
            this.delayClose();
        }
        else {
            this.closeNode_.active = true;
        }
    };
    NoLongerTipPnlCtrl.prototype.onRemove = function () {
    };
    NoLongerTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/buttons_nbe_n
    NoLongerTipPnlCtrl.prototype.onClickButtons = function (event, _) {
        GameHelper_1.gameHpr.setNoLongerTip(this.noKey, this.noLongerTge_.isChecked);
        this.hide();
        var name = event.target.name;
        if (name === 'ok') {
            this.okCb && this.okCb();
        }
        else if (name === 'cancel') {
            this.cancelCb && this.cancelCb();
        }
        this.okCb = null;
        this.cancelCb = null;
    };
    // path://close_be_n
    NoLongerTipPnlCtrl.prototype.onClickClose = function (event, data) {
        GameHelper_1.gameHpr.setNoLongerTip(this.noKey, this.noLongerTge_.isChecked);
        this.hide();
        this.okCb = null;
        this.cancelCb = null;
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 被动打开时延时关闭
    NoLongerTipPnlCtrl.prototype.delayClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.closeNode_.active = false;
                        return [4 /*yield*/, ut.wait(Constant_1.DELAY_CLOSE_PNL_TIME, this)];
                    case 1:
                        _a.sent();
                        if (this.isValid) {
                            this.closeNode_.active = true;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    NoLongerTipPnlCtrl = __decorate([
        ccclass
    ], NoLongerTipPnlCtrl);
    return NoLongerTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoLongerTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcTm9Mb25nZXJUaXBQbmxDdHJsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQXNFO0FBQ3RFLDZEQUF5RDtBQUVqRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFnRCxzQ0FBYztJQUE5RDtRQUFBLHFFQXVGQztRQXJGRywwQkFBMEI7UUFDbEIsZ0JBQVUsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7UUFDL0MsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLGdCQUFnQjtRQUMxQyxpQkFBVyxHQUFhLElBQUksQ0FBQSxDQUFDLDBCQUEwQjtRQUN2RCxrQkFBWSxHQUFjLElBQUksQ0FBQSxDQUFDLDRCQUE0QjtRQUMzRCxrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLDhCQUE4QjtRQUNuRSxNQUFNO1FBRUUsV0FBSyxHQUFXLEVBQUUsQ0FBQTtRQUNsQixVQUFJLEdBQWEsSUFBSSxDQUFBO1FBQ3JCLGNBQVEsR0FBYSxJQUFJLENBQUE7O0lBMkVyQyxDQUFDO0lBekVVLDRDQUFlLEdBQXRCO1FBQ0ksT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRVkscUNBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0sb0NBQU8sR0FBZCxVQUFlLElBQVM7O1FBQ3BCLElBQUksQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQTtRQUN2QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUE7UUFDbkIsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFBO1FBQzNCLENBQUEsS0FBQSxJQUFJLENBQUMsV0FBVyxDQUFBLENBQUMsWUFBWSxxQkFBQyxJQUFJLENBQUMsT0FBTyxHQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUMsR0FBQztRQUNuRSxJQUFJLENBQUMsV0FBVyxDQUFDLHNCQUFzQixFQUFFLENBQUE7UUFDekMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLEdBQUcsQ0FBQyxDQUFBO1FBQ3pFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMseUJBQUksQ0FBQyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLDBDQUFFLGVBQWUsS0FBRSxDQUFDLENBQUE7UUFDL0UsSUFBSSxDQUFDLFlBQVksQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDM0MsV0FBVztRQUNYLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQTtRQUMxRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLE1BQU0sSUFBSSxpQkFBaUIsQ0FBQyxDQUFBO1FBQzFGLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxVQUFVLElBQUkscUJBQXFCLENBQUMsQ0FBQTtRQUN0RyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUUsRUFBRSxRQUFRO1lBQzFCLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtTQUNqQzthQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLGlCQUFpQjtZQUN2QyxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7U0FDcEI7YUFBTTtZQUNILElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtTQUNoQztJQUNMLENBQUM7SUFFTSxxQ0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLG9DQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiw4QkFBOEI7SUFDOUIsMkNBQWMsR0FBZCxVQUFlLEtBQTBCLEVBQUUsQ0FBUztRQUNoRCxvQkFBTyxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLElBQUksQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLENBQUE7UUFDL0QsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1FBQ1gsSUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUE7UUFDOUIsSUFBSSxJQUFJLEtBQUssSUFBSSxFQUFFO1lBQ2YsSUFBSSxDQUFDLElBQUksSUFBSSxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7U0FDM0I7YUFBTSxJQUFJLElBQUksS0FBSyxRQUFRLEVBQUU7WUFDMUIsSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFLENBQUE7U0FDbkM7UUFDRCxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQTtJQUN4QixDQUFDO0lBRUQsb0JBQW9CO0lBQ3BCLHlDQUFZLEdBQVosVUFBYSxLQUEwQixFQUFFLElBQVk7UUFDakQsb0JBQU8sQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLFNBQVMsQ0FBQyxDQUFBO1FBQy9ELElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtRQUNYLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2hCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFBO0lBQ3hCLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRWpILGlIQUFpSDtJQUVqSCxZQUFZO0lBQ0UsdUNBQVUsR0FBeEI7Ozs7O3dCQUNJLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTt3QkFDOUIscUJBQU0sRUFBRSxDQUFDLElBQUksQ0FBQywrQkFBb0IsRUFBRSxJQUFJLENBQUMsRUFBQTs7d0JBQXpDLFNBQXlDLENBQUE7d0JBQ3pDLElBQUksSUFBSSxDQUFDLE9BQU8sRUFBRTs0QkFDZCxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7eUJBQ2hDOzs7OztLQUNKO0lBdEZnQixrQkFBa0I7UUFEdEMsT0FBTztPQUNhLGtCQUFrQixDQXVGdEM7SUFBRCx5QkFBQztDQXZGRCxBQXVGQyxDQXZGK0MsRUFBRSxDQUFDLFdBQVcsR0F1RjdEO2tCQXZGb0Isa0JBQWtCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgREVMQVlfQ0xPU0VfUE5MX1RJTUUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xuXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBOb0xvbmdlclRpcFBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgY2xvc2VOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL2Nsb3NlX2JlX25cbiAgICBwcml2YXRlIHJvb3ROb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3RfblxuICAgIHByaXZhdGUgY29udGVudExibF86IGNjLkxhYmVsID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL2NvbnRlbnRfbFxuICAgIHByaXZhdGUgbm9Mb25nZXJUZ2VfOiBjYy5Ub2dnbGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X24vbm9fbG9uZ2VyX3RcbiAgICBwcml2YXRlIGJ1dHRvbnNOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Rfbi9idXR0b25zX25iZV9uXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIG5vS2V5OiBzdHJpbmcgPSAnJ1xuICAgIHByaXZhdGUgb2tDYjogRnVuY3Rpb24gPSBudWxsXG4gICAgcHJpdmF0ZSBjYW5jZWxDYjogRnVuY3Rpb24gPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIoZGF0YTogYW55KSB7XG4gICAgICAgIHRoaXMubm9LZXkgPSBkYXRhLm5vS2V5XG4gICAgICAgIHRoaXMub2tDYiA9IGRhdGEub2tcbiAgICAgICAgdGhpcy5jYW5jZWxDYiA9IGRhdGEuY2FuY2VsXG4gICAgICAgIHRoaXMuY29udGVudExibF8uc2V0TG9jYWxlS2V5KGRhdGEuY29udGVudCwgLi4uKGRhdGEucGFyYW1zIHx8IFtdKSlcbiAgICAgICAgdGhpcy5jb250ZW50TGJsXy5fZm9yY2VVcGRhdGVSZW5kZXJEYXRhKClcbiAgICAgICAgdGhpcy5yb290Tm9kZV8uaGVpZ2h0ID0gTWF0aC5tYXgoMzYwLCB0aGlzLmNvbnRlbnRMYmxfLm5vZGUuaGVpZ2h0ICsgMzAwKVxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gbS5Db21wb25lbnQoY2MuV2lkZ2V0KT8udXBkYXRlQWxpZ25tZW50KCkpXG4gICAgICAgIHRoaXMubm9Mb25nZXJUZ2VfLmlzQ2hlY2tlZCA9ICEhZGF0YS5zZWxlY3RcbiAgICAgICAgLy8g5piv5ZCm5pi+56S65Y+W5raI5oyJ6ZKuXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCdjYW5jZWwnKS5hY3RpdmUgPSAhIXRoaXMuY2FuY2VsQ2JcbiAgICAgICAgLy8g6K6+572u5oyJ6ZKu5ZCN5a2XXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCdvay92YWwnLCBjYy5MYWJlbCkuc2V0TG9jYWxlS2V5KGRhdGEub2tUZXh0IHx8ICdsb2dpbi5idXR0b25fb2snKVxuICAgICAgICB0aGlzLmJ1dHRvbnNOb2RlXy5DaGlsZCgnY2FuY2VsL3ZhbCcsIGNjLkxhYmVsKS5zZXRMb2NhbGVLZXkoZGF0YS5jYW5jZWxUZXh0IHx8ICdsb2dpbi5idXR0b25fY2FuY2VsJylcbiAgICAgICAgaWYgKGRhdGEuaGlkZUNsb3NlKSB7IC8v6ZqQ6JeP6IOM5pmv5YWz6ZetXG4gICAgICAgICAgICB0aGlzLmNsb3NlTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnNlbGVjdCkgeyAvL+WmguaenOm7mOiupOmAieS4reW+lyDlsLHorqnku5blu7bov5/lhbPpl61cbiAgICAgICAgICAgIHRoaXMuZGVsYXlDbG9zZSgpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmNsb3NlTm9kZV8uYWN0aXZlID0gdHJ1ZVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xuICAgIH1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cbiAgICAvLyBwYXRoOi8vcm9vdF9uL2J1dHRvbnNfbmJlX25cbiAgICBvbkNsaWNrQnV0dG9ucyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgXzogc3RyaW5nKSB7XG4gICAgICAgIGdhbWVIcHIuc2V0Tm9Mb25nZXJUaXAodGhpcy5ub0tleSwgdGhpcy5ub0xvbmdlclRnZV8uaXNDaGVja2VkKVxuICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICBjb25zdCBuYW1lID0gZXZlbnQudGFyZ2V0Lm5hbWVcbiAgICAgICAgaWYgKG5hbWUgPT09ICdvaycpIHtcbiAgICAgICAgICAgIHRoaXMub2tDYiAmJiB0aGlzLm9rQ2IoKVxuICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdjYW5jZWwnKSB7XG4gICAgICAgICAgICB0aGlzLmNhbmNlbENiICYmIHRoaXMuY2FuY2VsQ2IoKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMub2tDYiA9IG51bGxcbiAgICAgICAgdGhpcy5jYW5jZWxDYiA9IG51bGxcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vY2xvc2VfYmVfblxuICAgIG9uQ2xpY2tDbG9zZShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgIGdhbWVIcHIuc2V0Tm9Mb25nZXJUaXAodGhpcy5ub0tleSwgdGhpcy5ub0xvbmdlclRnZV8uaXNDaGVja2VkKVxuICAgICAgICB0aGlzLmhpZGUoKVxuICAgICAgICB0aGlzLm9rQ2IgPSBudWxsXG4gICAgICAgIHRoaXMuY2FuY2VsQ2IgPSBudWxsXG4gICAgfVxuICAgIC8vQGVuZFxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8g6KKr5Yqo5omT5byA5pe25bu25pe25YWz6ZetXG4gICAgcHJpdmF0ZSBhc3luYyBkZWxheUNsb3NlKCkge1xuICAgICAgICB0aGlzLmNsb3NlTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgYXdhaXQgdXQud2FpdChERUxBWV9DTE9TRV9QTkxfVElNRSwgdGhpcylcbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCkge1xuICAgICAgICAgICAgdGhpcy5jbG9zZU5vZGVfLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdfQ==