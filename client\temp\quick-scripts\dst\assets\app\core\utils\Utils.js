
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/utils/Utils.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'de9065oPUdAX6JvWpmJLpr3', 'Utils');
// app/core/utils/Utils.ts

var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var Utils = /** @class */ (function () {
    function Utils() {
        // 时间
        this.Time = {
            Day: 24 * 60 * 60 * 1000,
            Hour: 60 * 60 * 1000,
            Minute: 60 * 1000,
            Second: 1000,
        };
        // 2^50
        this.MAX_VALUE = 1125899906842624;
        this.MIN_VALUE = -1125899906842624;
        this._accumulation = 0;
        this._last_now = 0;
        this._temp_vec2_1 = cc.v2();
        this._temp_vec2_2 = cc.v2();
        this._temp_vec3_1 = cc.v3();
        this._temp_mat4 = cc.mat4();
        this._pad_tbl = {};
        this._clone_cache = [];
        this._lock_map = {};
    }
    Utils.prototype.now = function () {
        return Date.now();
    };
    Utils.prototype.dateZeroTime = function (msd) {
        var date = new Date(msd !== null && msd !== void 0 ? msd : this.now());
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0).getTime();
    };
    Utils.prototype.millisecondToString = function (msd) {
        var second = msd / 1000;
        var day = Math.floor(second / 86400);
        var hour = Math.floor(second / 3600);
        var minute = Math.floor(second / 60);
        if (day > 0) {
            return day + '天';
        }
        else if (hour > 0) {
            return hour + '小时';
        }
        else if (minute > 0) {
            return minute + '分钟';
        }
        else {
            return Math.floor(second) + '秒';
        }
    };
    // 将毫秒数格式化
    Utils.prototype.millisecondFormat = function (msd, format) {
        if (format === void 0) { format = 'mm:ss'; }
        var second = msd / 1000;
        if (/(d+)/i.test(format)) {
            var day = Math.floor(this.numberFixed(second / 86400));
            second -= day * 86400;
            format = format.replace(/(d+)/g, RegExp.$1.length === 1 ? day + '' : this.pad(day));
        }
        if (/(h+)/i.test(format)) {
            var hour = Math.floor(this.numberFixed(second / 3600));
            second -= hour * 3600;
            format = format.replace(/(h+)/g, RegExp.$1.length === 1 ? hour + '' : this.pad(hour));
        }
        if (/(m+)/i.test(format)) {
            var minute = Math.floor(this.numberFixed(second / 60));
            second -= minute * 60;
            format = format.replace(/(m+)/g, RegExp.$1.length === 1 ? minute + '' : this.pad(minute));
        }
        if (/(s+)/i.test(format)) {
            format = format.replace(/(s+)/g, RegExp.$1.length === 1 ? Math.floor(second) + '' : this.pad(Math.floor(second)));
        }
        return format;
    };
    Utils.prototype.secondFormat = function (val, format) {
        if (format === void 0) { format = 'mm:ss'; }
        return this.millisecondFormat(val * 1000, format);
    };
    // 将日期毫秒数格式化 format('yyyy-MM-dd hh:mm:ss')
    Utils.prototype.dateFormat = function (format, msd) {
        var date = msd ? new Date(msd) : new Date();
        var re = /(y+)/i;
        if (re.test(format)) {
            var t = re.exec(format)[1];
            format = format.replace(t, (date.getFullYear() + '').substring(4 - t.length));
        }
        var obj = {
            'M+': date.getMonth() + 1,
            'd+': date.getDate(),
            'h+': date.getHours(),
            'm+': date.getMinutes(),
            's+': date.getSeconds(),
            'q+': Math.floor((date.getMonth() + 3) / 3),
            'S+': date.getMilliseconds(),
        };
        for (var k in obj) {
            var r = new RegExp('(' + k + ')');
            if (r.test(format)) {
                var t = r.exec(format)[1];
                format = format.replace(t, t.length === 1 ? String(obj[k]) : this.pad(obj[k]));
            }
        }
        return format;
    };
    // 首字母变成大写
    Utils.prototype.initialUpperCase = function (str) {
        return str.length > 0 ? (str[0].toUpperCase() + str.substring(1, str.length)) : str;
    };
    // 将数字转换为String
    Utils.prototype.simplifyMoneyCh = function (money, num) {
        if (num === void 0) { num = 1000; }
        var value = Math.abs(money);
        if (value >= 100000000 && value >= num) {
            return parseFloat((money / 100000000).toFixed(2)) + '亿';
        }
        else if (value >= 10000 && value >= num) {
            return parseFloat((money / 10000).toFixed(2)) + '万';
        }
        else if (value >= 1000 && value >= num) {
            return parseFloat((money / 1000).toFixed(2)) + '千';
        }
        return money + '';
    };
    // 将数字转换为String
    Utils.prototype.simplifyMoneyEn = function (money, num) {
        if (num === void 0) { num = 1000; }
        var value = Math.abs(money);
        if (value < num) {
            return parseFloat(money.toFixed(2)) + '';
        }
        else if (value >= 1000000000000) {
            return parseFloat((money / 1000000000000).toFixed(2)) + 'T';
        }
        else if (value >= 1000000000) {
            return parseFloat((money / 1000000000).toFixed(2)) + 'G';
        }
        else if (value >= 1000000) {
            return parseFloat((money / 1000000).toFixed(2)) + 'M';
        }
        else {
            return parseFloat((money / 1000).toFixed(2)) + 'K';
        }
    };
    // 将数字转换为String
    Utils.prototype.simplifyMoney = function (money, num) {
        if (num === void 0) { num = 1000000; }
        var value = Math.abs(money);
        if (value >= 100000000 && value >= num) {
            return parseFloat((money / 1000000).toFixed(2)) + 'M';
        }
        else if (value >= 1000000 && value >= num) {
            return parseFloat((money / 1000).toFixed(2)) + 'K';
        }
        return money + '';
    };
    // 名字省略
    Utils.prototype.nameFormator = function (name, max, extra) {
        if (extra === void 0) { extra = '...'; }
        if (!name) {
            return '';
        }
        name = String(name);
        if (name.length <= max) {
            return name;
        }
        var cnt = 0, len = 0;
        max = max * 2;
        for (var i = 0, l = name.length; i < l; i++) {
            var val = name.charCodeAt(i) > 255 ? 2 : 1;
            if (len + val <= max - 2) {
                cnt += 1;
            }
            len += val;
            if (len > max) {
                break;
            }
        }
        if (len <= max) {
            return name;
        }
        else if (extra) {
            return name.substring(0, cnt) + extra;
        }
        return name.substring(0, cnt + 1);
    };
    // 获取字符串长度 一个汉字2个长度
    Utils.prototype.getStringLen = function (str) {
        if (!str) {
            return 0;
        }
        var count = 0;
        for (var i = 0, l = str.length; i < l; i++) {
            var c = str.charCodeAt(i);
            var val = (c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f) ? 1 : 2;
            count += val;
        }
        return count;
    };
    // 将数字以逗号隔开
    Utils.prototype.formatNumberByComma = function (num) {
        return num.toString().replace(/\d(?=(?:\d{3})+\b)/g, '$&,');
    };
    // 随机一个整数 包括min和max
    Utils.prototype.random = function (min, max) {
        if (max === undefined) {
            max = min;
            min = 0;
        }
        if (min >= max) {
            return min;
        }
        return Math.floor(Math.random() * (Math.max(max - min, 0) + 1)) + min;
    };
    // 概率
    Utils.prototype.chance = function (odds, mul) {
        if (mul === void 0) { mul = 100; }
        return odds > 0 && this.random(0, 100 * mul - 1) < odds * mul;
    };
    // 随机一个负数到正数的范围
    Utils.prototype.randomRange = function (min, max) {
        return Math.floor(Math.random() * min) + Math.floor(Math.random() * max);
    };
    // 随机一个下标出来
    Utils.prototype.randomIndex = function (len, count, ignore) {
        if (len === 0) {
            return -1;
        }
        var indexs = [], _count = count;
        ignore = Array.isArray(ignore) ? ignore : [ignore];
        _count = _count || 1;
        _count = _count > len ? len : _count;
        for (var i = 0; i < len; i++) {
            if (ignore.indexOf(i) !== -1) {
                continue;
            }
            indexs.push(i);
        }
        var ret = [];
        while (indexs.length > 0 && ret.length < _count) {
            var idx = this.random(indexs.length - 1);
            ret.push(indexs.splice(idx, 1)[0]);
        }
        if (ret.length === 0) {
            return -1;
        }
        return count === 1 ? ret[0] : ret;
    };
    // 根据权重随机
    Utils.prototype.randomIndexByWeight = function (arr, key) {
        if (!arr || !Array.isArray(arr) || !arr.length) {
            return -1;
        }
        var totalWeight = arr.reduce(function (val, cur) { return val + Number(key ? cur[key] : cur) * 100; }, 0);
        if (totalWeight === 0) {
            return this.random(0, arr.length - 1);
        }
        var offset = this.random(0, totalWeight - 1);
        for (var i = 0, l = arr.length; i < l; i++) {
            var val = (key ? arr[i][key] : arr[i]) * 100;
            if (offset < val) {
                return i;
            }
            else {
                offset -= val;
            }
        }
        return this.random(0, arr.length - 1);
    };
    // 打乱数组
    Utils.prototype.shuffleArray = function (arry) {
        var _a;
        var brr = arry.slice(); // 创建原数组的副本，以免修改原始数组
        for (var i = arry.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1)); // 生成随机索引
            // 交换当前元素与随机索引位置上的元素
            _a = __read([brr[j], brr[i]], 2), brr[i] = _a[0], brr[j] = _a[1];
        }
        return brr;
    };
    // 获取两点之间的角度
    Utils.prototype.getAngle = function (a, b) {
        return cc.misc.radiansToDegrees(Math.atan2(b.y - a.y, b.x - a.x));
    };
    // 规范角度 
    Utils.prototype.normAngle = function (angle) {
        return angle > 0 ? angle - 360 : angle;
    };
    Utils.prototype.sin = function (angle) {
        return Math.sin(cc.misc.degreesToRadians(angle));
    };
    Utils.prototype.cos = function (angle) {
        return Math.cos(cc.misc.degreesToRadians(angle));
    };
    // 根据角度和距离 获取坐标
    Utils.prototype.angleToPoint = function (angle, dis, out) {
        out = out || this._temp_vec2_1;
        out.x = this.cos(angle) * dis;
        out.y = this.sin(angle) * dis;
        return out;
    };
    // 获取某个节点在某个节点里面的坐标
    Utils.prototype.convertToNodeAR = function (node, targetNode, out) {
        out = out || this._temp_vec2_2.set2(0, 0);
        if (!(node === null || node === void 0 ? void 0 : node.isValid) || !(targetNode === null || targetNode === void 0 ? void 0 : targetNode.isValid)) {
            return out;
        }
        // 先将节点转到世界坐标
        node.getWorldMatrix(this._temp_mat4).getTranslation(this._temp_vec3_1);
        // 再将节点转到目标节点局部坐标
        return targetNode.convertToNodeSpaceAR(this._temp_vec3_1.toVec2(), out);
    };
    // 数字 字符串补0,根据长度补出前面差的0
    Utils.prototype.pad = function (num, length) {
        if (length === void 0) { length = 2; }
        var len = length - num.toString().length;
        if (len <= 0) {
            return num + '';
        }
        else if (!this._pad_tbl[len]) {
            this._pad_tbl[len] = (new Array(len + 1)).join('0');
        }
        return this._pad_tbl[len] + num;
    };
    // 将一个数字 分解成多个类型的数字
    Utils.prototype.decomposeNumberToTypes = function (num, types, out) {
        if (types === void 0) { types = [100000, 10000, 1000, 100, 10, 1]; }
        var ret = out || {}, type, count;
        types.sort(function (a, b) { return b - a; }); //先从大到小排个序
        for (var i = 0; i < types.length; i++) {
            type = types[i];
            count = Math.floor(num / types[i]);
            if (count >= 1) {
                ret[type] = count;
                num -= type * count;
            }
        }
        // 如果还有 就默认去最后一个 算一个
        if (num > 0) {
            type = types[types.length - 1];
            count = ret[type] || 0;
            ret[type] = count ? count + 1 : 1;
        }
        return ret;
    };
    // 将一个字符串转换成向量
    Utils.prototype.stringToVec2 = function (str, separator) {
        if (separator === void 0) { separator = ','; }
        if (!str) {
            return cc.v2();
        }
        var _a = __read(str.split(separator), 2), x = _a[0], y = _a[1];
        return cc.v2(parseFloat(x), parseFloat(y));
    };
    // 将一个字符串拆分为数组
    Utils.prototype.stringToNumbers = function (str, separator) {
        if (separator === void 0) { separator = '|'; }
        if (str === null || str === undefined || str === '') {
            return [];
        }
        else if (typeof str === 'number') {
            return [str];
        }
        return str.split(separator).filter(function (m) { return m.trim() !== ''; }).map(function (m) { return Number(m); });
    };
    // 将一个常数变成1
    Utils.prototype.normalizeNumber = function (val) {
        return val === 0 ? 0 : val / Math.abs(val);
    };
    // 将一个数字转换为带正负符号的字符串
    Utils.prototype.numberToString = function (val) {
        return (val >= 0 ? '+' : '') + val;
    };
    // 字符串填充参数
    Utils.prototype.stringFormat = function (text, params) {
        if (!text || !params || params.length === 0) {
            return text;
        }
        params.forEach(function (p, i) { return (text = text.replace(new RegExp('\\{' + i + '\\}', 'g'), p)); });
        return text;
    };
    // 等待 单位秒
    Utils.prototype.wait = function (delay, target) {
        return __awaiter(this, void 0, Promise, function () {
            var timer;
            return __generator(this, function (_a) {
                if (delay <= 0) {
                    return [2 /*return*/, Promise.resolve()];
                }
                timer = target || cc.Canvas.instance;
                return [2 /*return*/, new Promise(function (resolve) { return timer.scheduleOnce(resolve, delay); })];
            });
        });
    };
    // 等待 
    Utils.prototype.waitTimeout = function (delay) {
        return __awaiter(this, void 0, Promise, function () {
            return __generator(this, function (_a) {
                if (delay <= 0) {
                    return [2 /*return*/, Promise.resolve()];
                }
                return [2 /*return*/, new Promise(function (resolve) { return setTimeout(resolve, delay); })];
            });
        });
    };
    // 
    Utils.prototype.setTimeout = function (cb, delay, target) {
        var timer = target || cc.Canvas.instance;
        timer.scheduleOnce(cb, delay * 0.001);
        return cb;
    };
    Utils.prototype.clearTimeout = function (cb, target) {
        var timer = target || cc.Canvas.instance;
        timer.unschedule(cb);
    };
    // 等待下一帧
    Utils.prototype.waitNextFrame = function (frams, target) {
        return __awaiter(this, void 0, Promise, function () {
            return __generator(this, function (_a) {
                frams = Math.max(1, frams || 1);
                return [2 /*return*/, new Promise(function (resolve) {
                        var timer = target || cc.Canvas.instance;
                        function callback() {
                            frams -= 1;
                            if (frams <= 0) {
                                timer.unschedule(callback);
                                resolve();
                            }
                        }
                        timer.schedule(callback, 0);
                    })];
            });
        });
    };
    // 锁
    Utils.prototype.lock = function (tag, waitInterval) {
        if (waitInterval === void 0) { waitInterval = 0.1; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this._lock_map[tag]) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.wait(waitInterval)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 0];
                    case 2:
                        this._lock_map[tag] = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    Utils.prototype.unlock = function (tag) {
        delete this._lock_map[tag];
    };
    // 根据字符串切换颜色
    Utils.prototype.colorFromHEX = function (hex) {
        return cc.Color.WHITE.fromHEX(hex);
    };
    // 生成唯一ID
    Utils.prototype.UID = function () {
        var now = Date.now();
        var id = now * 1000 + 1;
        if (now !== this._last_now) {
            this._last_now = now;
            this._accumulation = 0;
        }
        else if (this._accumulation >= 999) {
            this._last_now = now + 1;
            this._accumulation = 0;
            id = this._last_now * 1000 + 1;
        }
        else {
            this._accumulation += 1;
            id += this._accumulation;
        }
        return id + '';
    };
    // 是否对象
    Utils.prototype.isObject = function (o) {
        return Object.prototype.toString.call(o) === '[object Object]';
    };
    // 判断对象是否空对象{}
    Utils.prototype.isEmptyObject = function (o) {
        if (!o) {
            return true;
        }
        for (var k in o) {
            return false;
        }
        return true;
    };
    // 拷贝对象
    Utils.prototype.cloneObject = function (obj) {
        var ret = {};
        for (var k in obj) {
            ret[k] = obj[k];
        }
        return ret;
    };
    // 深度拷贝对象
    Utils.prototype.deepClone = function (obj, inDeep) {
        if (inDeep === void 0) { inDeep = false; }
        if (!obj) {
            return null;
        }
        else if (!inDeep) {
            this._clone_cache = [];
        }
        var objClone = Array.isArray(obj) ? [] : {};
        if (obj && typeof obj === 'object') {
            for (var key in obj) {
                if (!obj.hasOwnProperty(key)) {
                    continue;
                }
                //判断ojb子元素是否为对象，如果是，递归复制
                var value = obj[key];
                if (value && typeof value === 'object') {
                    if (this._clone_cache.indexOf(value) === -1) {
                        this._clone_cache.push(value);
                        objClone[key] = this.deepClone(value, true);
                    }
                }
                else {
                    objClone[key] = value; //如果不是，简单复制
                }
            }
        }
        if (!inDeep) {
            this._clone_cache = null;
        }
        return objClone;
    };
    // 深度比较两个对象是否相等
    Utils.prototype.compareObject = function (x, y, leftChain, rightChain) {
        if (leftChain === void 0) { leftChain = []; }
        if (rightChain === void 0) { rightChain = []; }
        if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
            return true; //如果都是NaN 直接返回
        }
        else if (x === y) {
            return true; //一样直接返回
            // } else if ((typeof x === 'function' && typeof y === 'function') ||
            //     (x instanceof Date && y instanceof Date) ||
            //     (x instanceof RegExp && y instanceof RegExp) ||
            //     (x instanceof String && y instanceof String) ||
            //     (x instanceof Number && y instanceof Number)) {
            //     return x.toString() === y.toString()  //如果是方法
        }
        else if (!(x instanceof Object && y instanceof Object)) {
            return false;
        }
        else if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
            return false;
        }
        else if (x.constructor !== y.constructor) {
            return false;
        }
        else if (x.prototype !== y.prototype) {
            return false;
        }
        else if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
            return false; // Check for infinitive linking loops
        }
        var p;
        // Quick checking of one object being a subset of another.
        // todo: cache the structure of arguments[0] for performance
        for (p in y) {
            if (y[p] === undefined) {
                continue;
            }
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            }
            else if (typeof y[p] !== typeof x[p]) {
                return false;
            }
        }
        for (p in x) {
            if (x[p] === undefined) {
                continue;
            }
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            }
            else if (typeof y[p] !== typeof x[p]) {
                return false;
            }
            var tf = typeof (x[p]);
            if (tf === 'object' || tf === 'function') {
                leftChain.push(x);
                rightChain.push(y);
                if (!this.compareObject(x[p], y[p], leftChain, rightChain)) {
                    return false;
                }
                leftChain.pop();
                rightChain.pop();
            }
            else if (x[p] !== y[p]) {
                return false;
            }
        }
        return true;
    };
    // 循环值
    Utils.prototype.loopValue = function (index, len) {
        index = index % len;
        if (index < 0) {
            return index + len;
        }
        return index;
    };
    // 组装列表
    Utils.prototype.items = function (arr, datas, item, parent, cb) {
        var i = 0, len = datas.length;
        for (var l = arr.length; i < l; i++) {
            if (i < len) {
                cb(arr[i], datas[i], i);
            }
            else {
                arr[i].active = false;
            }
        }
        for (; i < len; i++) {
            var it = cc.instantiate2(item, parent);
            cb(it, datas[i], i);
            arr.push(it);
        }
    };
    // 设置屏幕常亮
    Utils.prototype.setKeepScreenOn = function (val) {
        // @ts-ignore
        CC_JSB && jsb.Device.setKeepScreenOn(val);
    };
    Utils.prototype.boolToNumber = function (val) {
        return val ? 1 : 0;
    };
    // 对象给对象赋值
    Utils.prototype.setValue = function (fields, data, target) {
        target = target || {};
        fields.split('|').forEach(function (m) { return target[m] = data[m]; });
        return target;
    };
    // http请求
    Utils.prototype.httpRequest = function (method, url, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        method = method.toUpperCase();
                        if (method !== 'POST' && method !== 'GET') {
                            logger.info('http method error');
                            return resolve({ status: 0 });
                        }
                        logger.info('http request method=' + method + ', url=' + url);
                        var xhr = new XMLHttpRequest();
                        xhr.onreadystatechange = function () {
                            if (xhr.readyState === 4 && xhr.status === 200) {
                                var json = null;
                                if (xhr.responseText) {
                                    try {
                                        json = JSON.parse(xhr.responseText);
                                    }
                                    catch (error) {
                                    }
                                }
                                resolve({ status: 200, data: json });
                            }
                        };
                        xhr.timeout = 5000;
                        xhr.ontimeout = function (e) {
                            logger.info('http timeout');
                            resolve({ status: 1 });
                        };
                        xhr.onerror = function (e) {
                            logger.info('http disconnect');
                            resolve({ status: 2 });
                        };
                        xhr.open(method, url, true);
                        if (method === 'POST' && data) {
                            xhr.setRequestHeader("Content-Type", "application/json");
                            xhr.send(JSON.stringify(data));
                        }
                        else {
                            xhr.send(null);
                        }
                    })];
            });
        });
    };
    // 判断是否是ios
    Utils.prototype.isIos = function () {
        return cc.sys.os === cc.sys.OS_IOS;
    };
    // 判断是否是安卓
    Utils.prototype.isAndroid = function () {
        return cc.sys.os === cc.sys.OS_ANDROID;
    };
    // 是否手机平台
    Utils.prototype.isMobile = function () {
        return cc.sys.isNative && (this.isIos() || this.isAndroid());
    };
    // 是否微信游戏
    Utils.prototype.isWechatGame = function () {
        return cc.sys.platform === cc.sys.WECHAT_GAME;
    };
    // 是否qq游戏
    Utils.prototype.isQQGame = function () {
        return typeof qq !== 'undefined';
    };
    // 判断是否是小程序
    Utils.prototype.isMiniGame = function () {
        return this.isWechatGame() || this.isQQGame();
    };
    // 随机字符串
    Utils.prototype.getRandomString = function (len) {
        if (len === void 0) { len = 8; }
        var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
        var maxPos = $chars.length;
        var str = '';
        for (var i = 0; i < len; i++) {
            str += $chars.charAt(Math.floor(Math.random() * maxPos));
        }
        return str;
    };
    // 创建一个数组
    Utils.prototype.newArray = function (count, val) {
        return new Array(count).fill(val);
    };
    // 同步锁 同时调用的时候 只会执行第一个
    Utils.prototype.syncLock = function (target, propertyName, propertyDescriptor) {
        var key = "__lock_" + propertyName;
        if (target && !propertyName) {
            key = target;
        }
        var method = propertyDescriptor.value;
        propertyDescriptor.value = function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            return __awaiter(this, void 0, void 0, function () {
                var result;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (this[key]) {
                                return [2 /*return*/, null];
                            }
                            this[key] = true;
                            return [4 /*yield*/, method.apply(this, args)];
                        case 1:
                            result = _a.sent();
                            this[key] = false;
                            return [2 /*return*/, result];
                    }
                });
            });
        };
        return propertyDescriptor;
    };
    // 同步等待 同时调用多个的时候 会等待上一个完成后继续下一个
    Utils.prototype.syncWait = function (target, propertyName, propertyDescriptor) {
        var key = "__sync_wait_" + propertyName;
        if (target && !propertyName) {
            key = target;
        }
        var method = propertyDescriptor.value;
        function func(self, list) {
            return __awaiter(this, void 0, void 0, function () {
                var it, result;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (list.length === 0) {
                                return [2 /*return*/];
                            }
                            it = list[0];
                            return [4 /*yield*/, method.apply(self, it.args)];
                        case 1:
                            result = _a.sent();
                            it.resolve(result);
                            list.shift(); //删除第一个
                            if (list.length > 0) {
                                func(this, list);
                            }
                            return [2 /*return*/];
                    }
                });
            });
        }
        propertyDescriptor.value = function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            return __awaiter(this, void 0, void 0, function () {
                var _this = this;
                return __generator(this, function (_a) {
                    return [2 /*return*/, new Promise(function (resolve) {
                            var list = _this[key];
                            if (!list) {
                                list = _this[key] = [];
                            }
                            list.push({ resolve: resolve, args: args });
                            if (list.length === 1) {
                                func(_this, list);
                            }
                        })];
                });
            });
        };
        return propertyDescriptor;
    };
    // 获取浏览器参数
    Utils.prototype.getBrowserParams = function () {
        var _a;
        if (!cc.sys.isBrowser) {
            return {};
        }
        var obj = {};
        (_a = location === null || location === void 0 ? void 0 : location.search) === null || _a === void 0 ? void 0 : _a.replace('?', '').split('&').forEach(function (m) {
            var _a = __read(m.split('='), 2), key = _a[0], val = _a[1];
            if (key) {
                obj[key] = val;
            }
        });
        return obj;
    };
    Utils.prototype.getBrowserParamByKey = function (key) {
        var _a;
        var obj = this.getBrowserParams();
        return (_a = obj === null || obj === void 0 ? void 0 : obj[key]) !== null && _a !== void 0 ? _a : '';
    };
    // 是否在多边形里面
    Utils.prototype.isInPolygon = function (point, polygonPoints) {
        var counter = 0, i, xinters;
        var p1, p2;
        var pointCount = polygonPoints.length;
        p1 = polygonPoints[0];
        for (i = 1; i <= pointCount; i++) {
            p2 = polygonPoints[i % pointCount];
            if (point.x > Math.min(p1.x, p2.x) && point.x <= Math.max(p1.x, p2.x)) {
                if (point.y <= Math.max(p1.y, p2.y)) {
                    if (p1.x != p2.x) {
                        xinters = (point.x - p1.x) * (p2.y - p1.y) / (p2.x - p1.x) + p1.y;
                        if (p1.y == p2.y || point.y <= xinters) {
                            counter++;
                        }
                    }
                }
            }
            p1 = p2;
        }
        return (counter & 1) !== 0;
    };
    // 检测版本 a >= b 为true，反之则为false
    Utils.prototype.checkVersion = function (a, b) {
        var vA = this.stringToNumbers(a, '.');
        var vB = this.stringToNumbers(b, '.');
        var vALen = vA.length;
        var vBLen = vB.length;
        for (var i = 0; i < vALen; i++) {
            var a_1 = vA[i], b_1 = 0;
            if (i < vBLen) {
                b_1 = vB[i];
            }
            if (a_1 < b_1) {
                return false;
            }
            else if (a_1 > b_1) {
                break;
            }
        }
        return true;
    };
    // 切换到前台事件
    Utils.prototype.waitGameShow = function () {
        return new Promise(function (resolve) {
            var cb = function () {
                cc.game.off(cc.game.EVENT_SHOW, cb);
                resolve();
            };
            cc.game.on(cc.game.EVENT_SHOW, cb);
        });
    };
    // 查找节点
    Utils.prototype.findNode = function (path, count) {
        if (count === void 0) { count = 30; }
        return __awaiter(this, void 0, void 0, function () {
            var node, cnt, it;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        node = cc.Canvas.instance, cnt = 0, it = null;
                        _a.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 3];
                        it = node.FindChild(path);
                        if (it && it.active) {
                            return [3 /*break*/, 3];
                        }
                        return [4 /*yield*/, this.wait(0.2)];
                    case 2:
                        _a.sent();
                        if (cnt++ > count) {
                            return [3 /*break*/, 3];
                        }
                        return [3 /*break*/, 1];
                    case 3: return [2 /*return*/, it];
                }
            });
        });
    };
    // 消耗节点
    Utils.prototype.destroyNode = function (node) {
        if (node && cc.isValid(node)) {
            node.destroy();
            return true;
        }
        return false;
    };
    // 时间差
    Utils.prototype.timediff = function (start, date) {
        var _a = __read(date.split('-'), 6), year = _a[0], month = _a[1], day = _a[2], h = _a[3], m = _a[4], s = _a[5];
        h = h || '00';
        m = m || '00';
        s = s || '00';
        var time = new Date(year + "/" + month + "/" + day + " " + h + ":" + m + ":" + s).getTime();
        // time -= 8 * 3600 * 1000// 东八区时区矫正
        return time - start;
    };
    // 将number精度一下
    Utils.prototype.numberFixed = function (val, fractionDigits) {
        if (fractionDigits === void 0) { fractionDigits = 4; }
        return parseFloat(val.toFixed(fractionDigits));
    };
    return Utils;
}());
window['ut'] = new Utils();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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