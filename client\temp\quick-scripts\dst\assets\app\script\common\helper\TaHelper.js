
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/TaHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dc0c6BAa+9Geppkg7+tAvAY', 'TaHelper');
// app/script/common/helper/TaHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.taHelper = void 0;
var version_1 = require("../../../../scene/version");
var LocalConfig_1 = require("../LocalConfig");
var Enums_1 = require("../constant/Enums");
var JsbEvent_1 = require("../event/JsbEvent");
var GameHelper_1 = require("./GameHelper");
var JsbHelper_1 = require("./JsbHelper");
var WxHelper_1 = require("./WxHelper");
// 数数科技 上报
var TaHelper = /** @class */ (function () {
    function TaHelper() {
        this._ta = null;
        this.DEBUG_MODE = 'debugOnly'; //"none": 不开启Debug, "debug": 开启 Debug 模式，并入库, "debugOnly": 开启 Debug 模式，不入库
        this.OPEN = true;
        this.TICK_INTERVAL = 30 * 60; //定时上报间隔
        // private readonly TICK_INTERVAL: number = 10 //定时上报间隔
        this.tick = 0;
    }
    Object.defineProperty(TaHelper.prototype, "ta", {
        get: function () {
            var _a;
            if (!this._ta) {
                this.OPEN = !ut.isWechatGame() || WxHelper_1.wxHelper.isRelease();
                this.DEBUG_MODE = LocalConfig_1.localConfig.RELEASE ? 'none' : this.DEBUG_MODE;
                var ta = window['ta_twomiles']; //如果初始化的时候已经生成过ta实例，直接拿来用
                this._ta = ta || version_1.default.newThinkingAnalyticsAPI({ appId: LocalConfig_1.localConfig.RELEASE ? '' : 'debug-appid', debugMode: this.DEBUG_MODE });
                if (LocalConfig_1.localConfig.RELEASE) {
                    // console.log('on game event >>>>')
                    this.timeEvent('ta_game_hide');
                    cc.game.on(cc.game.EVENT_SHOW, function () {
                        // console.log('EVENT_SHOW >>>>')
                        this.track('ta_game_show', { uid: GameHelper_1.gameHpr.getUid(), source: GameHelper_1.gameHpr.getEnterQuery() ? 1 : 0 });
                        this.timeEvent('ta_game_hide');
                    }, this);
                    cc.game.on(cc.game.EVENT_HIDE, function () {
                        // console.log('EVENT_HIDE >>>>')
                        this.track('ta_game_hide', { uid: GameHelper_1.gameHpr.getUid() });
                        this.timeEvent('ta_game_show');
                    }, this);
                }
                // 设置静态属性
                this.ta.setSuperProperties({
                    client_version: GameHelper_1.gameHpr.getVersion(),
                    platform: GameHelper_1.gameHpr.getShopPlatform(),
                    os: ((_a = this._ta.getPresetProperties()) === null || _a === void 0 ? void 0 : _a.os) || 'none',
                    area_type: GameHelper_1.gameHpr.getServerArea()
                    // timeZone: (0 - new Date().getTimezoneOffset() / 60),
                });
                if (ut.isMobile()) {
                    var appsflyer_id = JsbHelper_1.jsbHelper.getAppsflyerId();
                    if (appsflyer_id && appsflyer_id !== "") {
                        this._ta.identify(appsflyer_id);
                    }
                }
            }
            return this._ta;
        },
        enumerable: false,
        configurable: true
    });
    TaHelper.prototype.init = function () {
        if (this.isIgnore()) {
            return;
        }
        // 设置动态属性
        var area = GameHelper_1.gameHpr.getServerArea();
        this.ta.setDynamicSuperProperties(function () {
            return {
                sid: GameHelper_1.gameHpr.user.getPlaySid() || 0,
                uid: area === 'hk' ? GameHelper_1.gameHpr.getUid() : (area + '_' + GameHelper_1.gameHpr.getUid()),
            };
        });
        JsbHelper_1.jsbHelper.cast('START_APPSFLYER', null, { ta_distinct_id: this.ta.getDistinctId() });
    };
    TaHelper.prototype.getDistinctId = function () {
        return this.ta.getDistinctId();
    };
    TaHelper.prototype.getOs = function () {
        var _a;
        return ((_a = this.ta.getPresetProperties()) === null || _a === void 0 ? void 0 : _a.os) || 'none';
    };
    TaHelper.prototype.getOsVersion = function () {
        var _a;
        return ((_a = this.ta.getPresetProperties()) === null || _a === void 0 ? void 0 : _a.osVersion) || '';
    };
    TaHelper.prototype.getOsAndVersion = function () {
        var os = this.getOs(), version = this.getOsVersion();
        return version ? os + ';' + version : os;
    };
    // 只有正式国内环境才需要上报ta
    TaHelper.prototype.isIgnore = function () {
        if (!this.OPEN) {
            return true;
        }
        else if (this.DEBUG_MODE === 'none') {
            return cc.sys.isBrowser;
        }
        else if (!LocalConfig_1.localConfig.RELEASE) {
            return true;
        }
        return false;
    };
    TaHelper.prototype.login = function (id) {
        if (this.isIgnore()) {
            return;
        }
        this.ta.logout();
        var area = GameHelper_1.gameHpr.getServerArea();
        if (area === 'hk') {
            this.ta.login(id);
        }
        else {
            this.ta.login(area + '_' + id);
        }
        // 将数数的id传给af 达成数据互通
        JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.APPSFLYER_TE, null, { ta_distinct_id: this.getDistinctId(), ta_account_id: this.ta.getAccountId() });
        // ios用户(谷歌登录)每次进游戏的时候 要调用
        if (ut.isIos() && GameHelper_1.gameHpr.user.getLoginType() === Enums_1.LoginType.GOOGLE) {
            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.FIREBASE_UPLOAD_EMAIL);
        }
    };
    TaHelper.prototype.logout = function () {
        if (this.isIgnore()) {
            return;
        }
        this.ta.logout();
    };
    TaHelper.prototype.update = function (dt) {
        if (this.isIgnore()) {
            return;
        }
        this.tick += dt;
        if (this.tick >= this.TICK_INTERVAL) {
            this.tick -= this.TICK_INTERVAL;
            this.trackTick();
        }
    };
    // 上报定时数据
    TaHelper.prototype.trackTick = function () {
    };
    TaHelper.prototype.userSet = function (data) {
        if (this.isIgnore()) {
            return;
        }
        this.ta.userSet(data);
    };
    TaHelper.prototype.userSetOnce = function (data) {
        if (this.isIgnore()) {
            return;
        }
        this.ta.userSetOnce(data);
    };
    // 上报一个事件
    TaHelper.prototype.track = function (eventId, properties) {
        // console.log('tatrack ' + eventId)
        if (this.isIgnore()) {
            return;
        }
        properties = properties || {};
        // let serverZoneOffset = gameHpr.getServerZoneOffset()
        // if (serverZoneOffset !== null) {
        //     properties['#zone_offset'] = serverZoneOffset
        // }
        // properties['#time'] = ut.dateFormat('YYYY-MM-dd hh:mm:ss.SSS', gameHpr.getServerNowTime())
        this.ta.track(eventId, properties, new Date(GameHelper_1.gameHpr.getServerNowTime()), function (res) {
            logger.print(res);
            // console.log(res)
        });
    };
    // 记录事件时长
    TaHelper.prototype.timeEvent = function (eventId) {
        if (this.isIgnore()) {
            return;
        }
        this.ta.timeEvent(eventId);
    };
    // 上报新手村事件
    TaHelper.prototype.trackNovice = function (eventId, properties) {
        var player = GameHelper_1.gameHpr.player;
        properties['maincitylevel'] = player.getMainBuildLv();
        properties['cereal'] = player.getCereal();
        properties['timber'] = player.getTimber();
        properties['stone'] = player.getStone();
        properties['pawncount'] = GameHelper_1.gameHpr.noviceServer.getPawns().length;
        properties['landcount'] = GameHelper_1.gameHpr.getPlayerOweCellCount();
        // cc.log(properties)
        this.track(eventId, properties);
    };
    return TaHelper;
}());
exports.taHelper = new TaHelper();
if (cc.sys.isBrowser) {
    window['taHelper'] = exports.taHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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