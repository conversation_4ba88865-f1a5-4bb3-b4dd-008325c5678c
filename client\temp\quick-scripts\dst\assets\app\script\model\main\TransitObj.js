
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/TransitObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9fbb499ffJN3ambmOwwNM08', 'TransitObj');
// app/script/model/main/TransitObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var CTypeObj_1 = require("../common/CTypeObj");
var BaseMarchObj_1 = require("./BaseMarchObj");
// 一个运送信息
var TransitObj = /** @class */ (function (_super) {
    __extends(TransitObj, _super);
    function TransitObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.merchantCount = 0; //商人数量
        _this.goods = null; //携带
        _this.backGoods = null; //需要带回来的
        return _this;
    }
    TransitObj.prototype.init = function (data) {
        this.uid = data.uid;
        this.owner = data.owner;
        this.merchantCount = data.merchantCount;
        this.goods = new CTypeObj_1.default().init(data.goodsType, 0, data.goodsCount);
        this.backGoods = new CTypeObj_1.default().init(data.backGoodsType, 0, data.backGoodsCount);
        this.startIndex = data.startIndex;
        this.targetIndex = data.targetIndex;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.initTime = Date.now();
        this.initBase();
        this.targetType = this.initTargetType();
        return this;
    };
    // 目标类型
    TransitObj.prototype.initTargetType = function () {
        _super.prototype.initTargetType.call(this);
        var uid = GameHelper_1.gameHpr.getUid();
        var index = GameHelper_1.gameHpr.player.getMainCityIndex();
        if (this.targetIndex === index) {
            return this.owner === uid ? Enums_1.TransitTargetType.BACK : Enums_1.TransitTargetType.SHIPPED;
        }
        else if (this.owner === uid) {
            return Enums_1.TransitTargetType.SHIP_OUT;
        } /*  else if (this.startIndex === index) {
            return TransitTargetType.BACK
        } */
        return Enums_1.TransitTargetType.NONE;
    };
    // 获取移动角色的url
    TransitObj.prototype.getMarchRoleUrl = function () {
        return 'march/TRANSIT';
    };
    TransitObj.prototype.getMarchInfoUrl = function () {
        return 'march/MARCH_TRANSIT_INFO';
    };
    TransitObj.prototype.getMarchRoleAnim = function () {
        return 'role_transit_walk';
    };
    // 行军线类型
    TransitObj.prototype.getMarchLineType = function () {
        return Enums_1.MarchLineType.MERCHANT;
    };
    return TransitObj;
}(BaseMarchObj_1.default));
exports.default = TransitObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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