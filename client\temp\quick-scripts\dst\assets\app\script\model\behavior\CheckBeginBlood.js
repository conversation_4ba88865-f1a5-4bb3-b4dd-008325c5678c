
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/CheckBeginBlood.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c44f7+lLR9BkapqIAGKhEzL', 'CheckBeginBlood');
// app/script/model/behavior/CheckBeginBlood.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 检测回合开始回血
var CheckBeginBlood = /** @class */ (function (_super) {
    __extends(CheckBeginBlood, _super);
    function CheckBeginBlood() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.bloodNeedTime = 0;
        return _this;
    }
    CheckBeginBlood.prototype.onInit = function (conf) {
        this.bloodNeedTime = conf.parameters || 1000;
    };
    CheckBeginBlood.prototype.onOpen = function () {
        var isCanBlood = !!this.target.getEquipEffectByType(Enums_1.EquipEffectType.BEGIN_BLOOD)
            || this.target.isHasBuffs(Enums_1.BuffType.TOUGH, Enums_1.BuffType.THUNDERS_RECOVER, Enums_1.BuffType.CIRCLE_OF_LIFE)
            || this.target.isHasStrategys(40204, 50028)
            || this.target.getBuffValue(Enums_1.BuffType.TONDEN_RECOVER) > 0;
        this.setBlackboardData('isBeginBlood', !isCanBlood || this.target.isFullHp());
        this.setTreeBlackboardData('isBloodAction', false);
    };
    CheckBeginBlood.prototype.onLeave = function (state) {
        this.setBlackboardData('isBeginBlood', state === BTConstant_1.BTState.SUCCESS);
    };
    CheckBeginBlood.prototype.onTick = function (dt) {
        var _a;
        if (this.getTreeBlackboardData('isBatter') || this.getBlackboardData('isBeginBlood') || this.target.isFullHp()) {
            return BTConstant_1.BTState.SUCCESS;
        }
        var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= this.bloodNeedTime) {
            return BTConstant_1.BTState.SUCCESS;
        }
        else if (currTime === 0) {
            var maxHp = this.target.getMaxHp();
            // 屯垦令
            var val = this.target.getBuffValue(Enums_1.BuffType.TONDEN_RECOVER);
            // 政策 生生不息
            val += this.target.getBuffValue(Enums_1.BuffType.CIRCLE_OF_LIFE);
            // 梁红玉 擂鼓恢复
            var buff = this.target.getBuff(Enums_1.BuffType.THUNDERS_RECOVER);
            if (buff) {
                val += Math.round(maxHp * buff.value * 0.01);
            }
            // 韬略
            val += this.target.getStrategyValue(50028);
            // 酒葫芦
            var effect = this.target.getEquipEffectByType(Enums_1.EquipEffectType.BEGIN_BLOOD);
            if (effect) {
                val += Math.round(maxHp * effect.value * 0.01);
            }
            // 曹仁 满层坚韧
            buff = this.target.getBuff(Enums_1.BuffType.TOUGH);
            if (buff) {
                var heroSkill = this.target.getPortrayalSkill();
                if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.CAO_REN && buff.value >= heroSkill.value) {
                    val += Math.round(maxHp * heroSkill.params);
                }
            }
            // 韬略 恢复当前生命
            val += Math.round(this.target.getCurHp() * this.target.getStrategyValue(40204) * 0.01);
            if (!val) {
                return BTConstant_1.BTState.SUCCESS;
            }
            val = this.target.onHeal(val);
            this.target.changeState(Enums_1.PawnState.HEAL, { val: val });
            this.setTreeBlackboardData('isBloodAction', true);
        }
        this.setBlackboardData('currTime', currTime + dt);
        return BTConstant_1.BTState.RUNNING;
    };
    return CheckBeginBlood;
}(BaseAction_1.default));
exports.default = CheckBeginBlood;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQ2hlY2tCZWdpbkJsb29kLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHFEQUE2RjtBQUM3RiwyQ0FBc0M7QUFDdEMsMkNBQXVDO0FBRXZDLFdBQVc7QUFDWDtJQUE2QyxtQ0FBVTtJQUF2RDtRQUFBLHFFQWtFQztRQWhFVyxtQkFBYSxHQUFXLENBQUMsQ0FBQTs7SUFnRXJDLENBQUM7SUE5RFUsZ0NBQU0sR0FBYixVQUFjLElBQVM7UUFDbkIsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQTtJQUNoRCxDQUFDO0lBRU0sZ0NBQU0sR0FBYjtRQUNJLElBQU0sVUFBVSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLHVCQUFlLENBQUMsV0FBVyxDQUFDO2VBQzNFLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFRLENBQUMsS0FBSyxFQUFFLGdCQUFRLENBQUMsZ0JBQWdCLEVBQUUsZ0JBQVEsQ0FBQyxjQUFjLENBQUM7ZUFDMUYsSUFBSSxDQUFDLE1BQU0sQ0FBQyxjQUFjLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQztlQUN4QyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxnQkFBUSxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsQ0FBQTtRQUM1RCxJQUFJLENBQUMsaUJBQWlCLENBQUMsY0FBYyxFQUFFLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTtRQUM3RSxJQUFJLENBQUMscUJBQXFCLENBQUMsZUFBZSxFQUFFLEtBQUssQ0FBQyxDQUFBO0lBQ3RELENBQUM7SUFFTSxpQ0FBTyxHQUFkLFVBQWUsS0FBYztRQUN6QixJQUFJLENBQUMsaUJBQWlCLENBQUMsY0FBYyxFQUFFLEtBQUssS0FBSyxvQkFBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO0lBQ3JFLENBQUM7SUFFTSxnQ0FBTSxHQUFiLFVBQWMsRUFBVTs7UUFDcEIsSUFBSSxJQUFJLENBQUMscUJBQXFCLENBQUMsVUFBVSxDQUFDLElBQUksSUFBSSxDQUFDLGlCQUFpQixDQUFDLGNBQWMsQ0FBQyxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUU7WUFDNUcsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtTQUN6QjtRQUNELElBQUksUUFBUSxTQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsbUNBQUksQ0FBQyxDQUFBO1FBQzlELElBQUksUUFBUSxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEMsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtTQUN6QjthQUFNLElBQUksUUFBUSxLQUFLLENBQUMsRUFBRTtZQUN2QixJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxDQUFBO1lBQ3BDLE1BQU07WUFDTixJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxnQkFBUSxDQUFDLGNBQWMsQ0FBQyxDQUFBO1lBQzNELFVBQVU7WUFDVixHQUFHLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsZ0JBQVEsQ0FBQyxjQUFjLENBQUMsQ0FBQTtZQUN4RCxXQUFXO1lBQ1gsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsZ0JBQVEsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFBO1lBQ3pELElBQUksSUFBSSxFQUFFO2dCQUNOLEdBQUcsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxDQUFBO2FBQy9DO1lBQ0QsS0FBSztZQUNMLEdBQUcsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQzFDLE1BQU07WUFDTixJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLHVCQUFlLENBQUMsV0FBVyxDQUFDLENBQUE7WUFDNUUsSUFBSSxNQUFNLEVBQUU7Z0JBQ1IsR0FBRyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUE7YUFDakQ7WUFDRCxVQUFVO1lBQ1YsSUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLGdCQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7WUFDMUMsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO2dCQUNqRCxJQUFJLENBQUEsU0FBUyxhQUFULFNBQVMsdUJBQVQsU0FBUyxDQUFFLEVBQUUsTUFBSyxnQkFBUSxDQUFDLE9BQU8sSUFBSSxJQUFJLENBQUMsS0FBSyxJQUFJLFNBQVMsQ0FBQyxLQUFLLEVBQUU7b0JBQ3JFLEdBQUcsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssR0FBRyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUE7aUJBQzlDO2FBQ0o7WUFDRCxZQUFZO1lBQ1osR0FBRyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxHQUFHLElBQUksQ0FBQyxDQUFBO1lBQ3RGLElBQUksQ0FBQyxHQUFHLEVBQUU7Z0JBQ04sT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTthQUN6QjtZQUNELEdBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUM3QixJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLElBQUksRUFBRSxFQUFFLEdBQUcsS0FBQSxFQUFFLENBQUMsQ0FBQTtZQUNoRCxJQUFJLENBQUMscUJBQXFCLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ3BEO1FBQ0QsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUE7UUFDakQsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBQ0wsc0JBQUM7QUFBRCxDQWxFQSxBQWtFQyxDQWxFNEMsb0JBQVUsR0FrRXREIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnVmZlR5cGUsIEVxdWlwRWZmZWN0VHlwZSwgSGVyb1R5cGUsIFBhd25TdGF0ZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIjtcbmltcG9ydCBCYXNlQWN0aW9uIGZyb20gXCIuL0Jhc2VBY3Rpb25cIjtcbmltcG9ydCB7IEJUU3RhdGUgfSBmcm9tIFwiLi9CVENvbnN0YW50XCI7XG5cbi8vIOajgOa1i+WbnuWQiOW8gOWni+WbnuihgFxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQ2hlY2tCZWdpbkJsb29kIGV4dGVuZHMgQmFzZUFjdGlvbiB7XG5cbiAgICBwcml2YXRlIGJsb29kTmVlZFRpbWU6IG51bWJlciA9IDBcblxuICAgIHB1YmxpYyBvbkluaXQoY29uZjogYW55KSB7XG4gICAgICAgIHRoaXMuYmxvb2ROZWVkVGltZSA9IGNvbmYucGFyYW1ldGVycyB8fCAxMDAwXG4gICAgfVxuXG4gICAgcHVibGljIG9uT3BlbigpIHtcbiAgICAgICAgY29uc3QgaXNDYW5CbG9vZCA9ICEhdGhpcy50YXJnZXQuZ2V0RXF1aXBFZmZlY3RCeVR5cGUoRXF1aXBFZmZlY3RUeXBlLkJFR0lOX0JMT09EKVxuICAgICAgICAgICAgfHwgdGhpcy50YXJnZXQuaXNIYXNCdWZmcyhCdWZmVHlwZS5UT1VHSCwgQnVmZlR5cGUuVEhVTkRFUlNfUkVDT1ZFUiwgQnVmZlR5cGUuQ0lSQ0xFX09GX0xJRkUpXG4gICAgICAgICAgICB8fCB0aGlzLnRhcmdldC5pc0hhc1N0cmF0ZWd5cyg0MDIwNCwgNTAwMjgpXG4gICAgICAgICAgICB8fCB0aGlzLnRhcmdldC5nZXRCdWZmVmFsdWUoQnVmZlR5cGUuVE9OREVOX1JFQ09WRVIpID4gMFxuICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdpc0JlZ2luQmxvb2QnLCAhaXNDYW5CbG9vZCB8fCB0aGlzLnRhcmdldC5pc0Z1bGxIcCgpKVxuICAgICAgICB0aGlzLnNldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNCbG9vZEFjdGlvbicsIGZhbHNlKVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkxlYXZlKHN0YXRlOiBCVFN0YXRlKSB7XG4gICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzQmVnaW5CbG9vZCcsIHN0YXRlID09PSBCVFN0YXRlLlNVQ0NFU1MpXG4gICAgfVxuXG4gICAgcHVibGljIG9uVGljayhkdDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLmdldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNCYXR0ZXInKSB8fCB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc0JlZ2luQmxvb2QnKSB8fCB0aGlzLnRhcmdldC5pc0Z1bGxIcCgpKSB7XG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5TVUNDRVNTXG4gICAgICAgIH1cbiAgICAgICAgbGV0IGN1cnJUaW1lOiBudW1iZXIgPSB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdjdXJyVGltZScpID8/IDBcbiAgICAgICAgaWYgKGN1cnJUaW1lID49IHRoaXMuYmxvb2ROZWVkVGltZSkge1xuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuU1VDQ0VTU1xuICAgICAgICB9IGVsc2UgaWYgKGN1cnJUaW1lID09PSAwKSB7XG4gICAgICAgICAgICBjb25zdCBtYXhIcCA9IHRoaXMudGFyZ2V0LmdldE1heEhwKClcbiAgICAgICAgICAgIC8vIOWxr+WepuS7pFxuICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMudGFyZ2V0LmdldEJ1ZmZWYWx1ZShCdWZmVHlwZS5UT05ERU5fUkVDT1ZFUilcbiAgICAgICAgICAgIC8vIOaUv+etliDnlJ/nlJ/kuI3mga9cbiAgICAgICAgICAgIHZhbCArPSB0aGlzLnRhcmdldC5nZXRCdWZmVmFsdWUoQnVmZlR5cGUuQ0lSQ0xFX09GX0xJRkUpXG4gICAgICAgICAgICAvLyDmooHnuqLnjokg5pOC6byT5oGi5aSNXG4gICAgICAgICAgICBsZXQgYnVmZiA9IHRoaXMudGFyZ2V0LmdldEJ1ZmYoQnVmZlR5cGUuVEhVTkRFUlNfUkVDT1ZFUilcbiAgICAgICAgICAgIGlmIChidWZmKSB7XG4gICAgICAgICAgICAgICAgdmFsICs9IE1hdGgucm91bmQobWF4SHAgKiBidWZmLnZhbHVlICogMC4wMSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIOmfrOeVpVxuICAgICAgICAgICAgdmFsICs9IHRoaXMudGFyZ2V0LmdldFN0cmF0ZWd5VmFsdWUoNTAwMjgpXG4gICAgICAgICAgICAvLyDphZLokavoiqZcbiAgICAgICAgICAgIGNvbnN0IGVmZmVjdCA9IHRoaXMudGFyZ2V0LmdldEVxdWlwRWZmZWN0QnlUeXBlKEVxdWlwRWZmZWN0VHlwZS5CRUdJTl9CTE9PRClcbiAgICAgICAgICAgIGlmIChlZmZlY3QpIHtcbiAgICAgICAgICAgICAgICB2YWwgKz0gTWF0aC5yb3VuZChtYXhIcCAqIGVmZmVjdC52YWx1ZSAqIDAuMDEpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyDmm7nku4Eg5ruh5bGC5Z2a6Z+nXG4gICAgICAgICAgICBidWZmID0gdGhpcy50YXJnZXQuZ2V0QnVmZihCdWZmVHlwZS5UT1VHSClcbiAgICAgICAgICAgIGlmIChidWZmKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaGVyb1NraWxsID0gdGhpcy50YXJnZXQuZ2V0UG9ydHJheWFsU2tpbGwoKVxuICAgICAgICAgICAgICAgIGlmIChoZXJvU2tpbGw/LmlkID09PSBIZXJvVHlwZS5DQU9fUkVOICYmIGJ1ZmYudmFsdWUgPj0gaGVyb1NraWxsLnZhbHVlKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbCArPSBNYXRoLnJvdW5kKG1heEhwICogaGVyb1NraWxsLnBhcmFtcylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyDpn6znlaUg5oGi5aSN5b2T5YmN55Sf5ZG9XG4gICAgICAgICAgICB2YWwgKz0gTWF0aC5yb3VuZCh0aGlzLnRhcmdldC5nZXRDdXJIcCgpICogdGhpcy50YXJnZXQuZ2V0U3RyYXRlZ3lWYWx1ZSg0MDIwNCkgKiAwLjAxKVxuICAgICAgICAgICAgaWYgKCF2YWwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5TVUNDRVNTXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB2YWwgPSB0aGlzLnRhcmdldC5vbkhlYWwodmFsKVxuICAgICAgICAgICAgdGhpcy50YXJnZXQuY2hhbmdlU3RhdGUoUGF3blN0YXRlLkhFQUwsIHsgdmFsIH0pXG4gICAgICAgICAgICB0aGlzLnNldFRyZWVCbGFja2JvYXJkRGF0YSgnaXNCbG9vZEFjdGlvbicsIHRydWUpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWUnLCBjdXJyVGltZSArIGR0KVxuICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgfVxufSJdfQ==