
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AdaptWidthCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c682cTjk+FOq5M2BV16gBn/', 'AdaptWidthCmpt');
// app/script/view/cmpt/AdaptWidthCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 自动适宽度
 */
var AdaptWidthCmpt = /** @class */ (function (_super) {
    __extends(AdaptWidthCmpt, _super);
    function AdaptWidthCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.maxWidth = 0;
        _this.preWidth = 0;
        return _this;
    }
    AdaptWidthCmpt.prototype.setMaxWidth = function (val) {
        if (this.maxWidth !== val) {
            this.maxWidth = val;
            this.do();
        }
    };
    AdaptWidthCmpt.prototype.update = function () {
        if (this.preWidth !== this.node.width) {
            this.do();
        }
    };
    AdaptWidthCmpt.prototype.do = function () {
        var w = this.preWidth = this.node.width;
        var scale = w > this.maxWidth ? this.maxWidth / w : 1;
        this.node.scale = scale;
    };
    __decorate([
        property
    ], AdaptWidthCmpt.prototype, "maxWidth", void 0);
    AdaptWidthCmpt = __decorate([
        ccclass
    ], AdaptWidthCmpt);
    return AdaptWidthCmpt;
}(cc.Component));
exports.default = AdaptWidthCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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