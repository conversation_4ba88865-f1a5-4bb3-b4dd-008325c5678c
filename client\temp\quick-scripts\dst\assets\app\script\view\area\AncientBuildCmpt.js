
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AncientBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'de2ef8h2fZIPpcnDYYevUvO', 'AncientBuildCmpt');
// app/script/view/area/AncientBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AncientObj_1 = require("../../model/main/AncientObj");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var AncientBTAnimRoleConf_1 = require("./AncientBTAnimRoleConf");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 遗迹建筑
var AncientBuildCmpt = /** @class */ (function (_super) {
    __extends(AncientBuildCmpt, _super);
    function AncientBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.lvNode = null; //等级节点
        _this.upLvAnimNode = null; //升级动画
        _this.touchCmpt = null;
        _this.ancientInfo = null;
        return _this;
    }
    AncientBuildCmpt.prototype.init = function (data, origin, originY, owner) {
        _super.prototype.init.call(this, data, origin, originY, owner);
        this.initAncientInfo(data);
        this.touchCmpt = this.body.addComponent(ClickTouchCmpt_1.default).on(this.onClick, this);
        this.syncPoint();
        this.syncZindex();
        // 初始化等级
        this.initLv();
        this.updateLv(this.ancientInfo.lv);
        // 显示是否在升级中
        this.updateUpLvAnim();
        return this;
    };
    // 重新同步
    AncientBuildCmpt.prototype.resync = function (data) {
        this.data = data;
        this.initAncientInfo(data);
        this.syncPoint();
        this.syncZindex();
        this.setCanClick(true);
        this.updateLv(this.ancientInfo.lv);
        this.updateUpLvAnim();
        return this;
    };
    // 初始化遗迹信息
    AncientBuildCmpt.prototype.initAncientInfo = function (data) {
        if (data.aIndex < 0) {
            this.ancientInfo = new AncientObj_1.default().init(GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex));
            this.ancientInfo.updateInfo({ lv: data.lv });
        }
        else {
            this.ancientInfo = GameHelper_1.gameHpr.world.getAncientInfo(data.aIndex);
        }
    };
    AncientBuildCmpt.prototype.initLv = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.lvNode = cc.instantiate2(pfb, this.node);
                        this.lvNode.zIndex = 1;
                        this.lvNode.setPosition(Math.floor(this.data.size.x * 0.5) * Constant_1.TILE_SIZE, 36 + (this.data.id === Constant_1.CITY_LUOYANG_ID ? 80 : 0));
                        this.lvNode.Child('val', cc.Label).string = '' + this.ancientInfo.lv;
                        this.lvNode.active = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载升级动画
    AncientBuildCmpt.prototype.loadUpLvAnim = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_ANCIENT_BT', cc.Prefab, 'area')];
                    case 1:
                        pfb = _a.sent();
                        if (!this.node || !this.node.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        this.upLvAnimNode = cc.instantiate2(pfb, this.node);
                        this.upLvAnimNode.zIndex = 1;
                        this.upLvAnimNode.setPosition(0, 0);
                        this.upLvAnimNode.active = false;
                        this.updateUpLvAnim();
                        return [2 /*return*/];
                }
            });
        });
    };
    AncientBuildCmpt.prototype.clean = function () {
        var _a;
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        this.node.destroy();
        this.data = null;
    };
    AncientBuildCmpt.prototype.onClick = function () {
        if (!this.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.ancientInfo && this.data.aIndex >= 0 && GameHelper_1.gameHpr.checkIsOneAlliance(this.ancientInfo.owner)) {
            ViewHelper_1.viewHelper.showPnl(this.data.getUIUrl(), this.data);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', this.data);
        }
    };
    // 设置是否可以点击
    AncientBuildCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 刷新等级
    AncientBuildCmpt.prototype.updateLv = function (lv) {
        var _a;
        if (this.lvNode) {
            this.lvNode.Child('val', cc.Label).string = '' + lv;
        }
        if (this.data.isMaxLv()) {
            this.Child('body/val', cc.MultiFrame).setFrame(5);
        }
        else if ((_a = this.ancientInfo) === null || _a === void 0 ? void 0 : _a.owner) {
            this.Child('body/val', cc.MultiFrame).setFrame(Math.floor(lv / 5) + 1);
        }
        else {
            this.Child('body/val', cc.MultiFrame).setFrame(0);
        }
    };
    // 刷新升级动画
    AncientBuildCmpt.prototype.updateUpLvAnim = function () {
        var _a, _b;
        if (!this.ancientInfo.owner || this.data.isMaxLv() || this.ancientInfo.state !== 1 || !!this.ancientInfo.pauseState) {
            (_a = this.upLvAnimNode) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else if (this.upLvAnimNode) {
            this.upLvAnimNode.active = true;
            this.upLvAnimNode.Child('time', cc.LabelTimer).run(this.ancientInfo.getSurplusTime() * 0.001);
            var roles = ((_b = AncientBTAnimRoleConf_1.ANCIENT_BTANIM_ROLE_POSITION[this.data.id]) === null || _b === void 0 ? void 0 : _b[Math.floor(this.ancientInfo.lv / 5)]) || [];
            this.upLvAnimNode.Child('root').Items(roles, function (it, data) {
                it.setPosition(data.x, data.y);
                it.scaleX = data.scaleX;
                it.Component(cc.Animation).play('ancient_bt', ut.random(1, 5) * 0.1);
            });
        }
        else {
            this.loadUpLvAnim();
        }
    };
    AncientBuildCmpt = __decorate([
        ccclass
    ], AncientBuildCmpt);
    return AncientBuildCmpt;
}(BaseBuildCmpt_1.default));
exports.default = AncientBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGFyZWFcXEFuY2llbnRCdWlsZENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQTRFO0FBQzVFLDZEQUF5RDtBQUN6RCw2REFBNEQ7QUFFNUQsMERBQXFEO0FBQ3JELHlEQUFvRDtBQUNwRCxpRUFBdUU7QUFDdkUsaURBQTRDO0FBRXRDLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRTVDLE9BQU87QUFFUDtJQUE4QyxvQ0FBYTtJQUEzRDtRQUFBLHFFQStIQztRQTdIVyxZQUFNLEdBQVksSUFBSSxDQUFBLENBQUMsTUFBTTtRQUM3QixrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLE1BQU07UUFDbkMsZUFBUyxHQUFtQixJQUFJLENBQUE7UUFFaEMsaUJBQVcsR0FBZSxJQUFJLENBQUE7O0lBeUgxQyxDQUFDO0lBdkhVLCtCQUFJLEdBQVgsVUFBWSxJQUFjLEVBQUUsTUFBZSxFQUFFLE9BQWUsRUFBRSxLQUFhO1FBQ3ZFLGlCQUFNLElBQUksWUFBQyxJQUFJLEVBQUUsTUFBTSxFQUFFLE9BQU8sRUFBRSxLQUFLLENBQUMsQ0FBQTtRQUN4QyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQzFCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsd0JBQWMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQzlFLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQTtRQUNoQixJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7UUFDakIsUUFBUTtRQUNSLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQTtRQUNiLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUNsQyxXQUFXO1FBQ1gsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ3JCLE9BQU8sSUFBSSxDQUFBO0lBQ2YsQ0FBQztJQUVELE9BQU87SUFDQSxpQ0FBTSxHQUFiLFVBQWMsSUFBYztRQUN4QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQzFCLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQTtRQUNoQixJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7UUFDakIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUN0QixJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRSxDQUFDLENBQUE7UUFDbEMsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ3JCLE9BQU8sSUFBSSxDQUFBO0lBQ2YsQ0FBQztJQUVELFVBQVU7SUFDRiwwQ0FBZSxHQUF2QixVQUF3QixJQUFjO1FBQ2xDLElBQUksSUFBSSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDakIsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLG9CQUFVLEVBQUUsQ0FBQyxJQUFJLENBQUMsb0JBQU8sQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUE7WUFDdEYsSUFBSSxDQUFDLFdBQVcsQ0FBQyxVQUFVLENBQUMsRUFBRSxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUE7U0FDL0M7YUFBTTtZQUNILElBQUksQ0FBQyxXQUFXLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTtTQUMvRDtJQUNMLENBQUM7SUFFYSxpQ0FBTSxHQUFwQjs7Ozs7NEJBQ2dCLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsZ0JBQWdCLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsRUFBQTs7d0JBQXRFLEdBQUcsR0FBRyxTQUFnRTt3QkFDNUUsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sSUFBSSxDQUFDLEdBQUcsRUFBRTs0QkFDMUMsc0JBQU07eUJBQ1Q7d0JBQ0QsSUFBSSxDQUFDLE1BQU0sR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7d0JBQzdDLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTt3QkFDdEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDLEdBQUcsb0JBQVMsRUFBRSxFQUFFLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSywwQkFBZSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7d0JBQ3pILElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQTt3QkFDcEUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBOzs7OztLQUM1QjtJQUVELFNBQVM7SUFDSSx1Q0FBWSxHQUF6Qjs7Ozs7NEJBQ2dCLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsd0JBQXdCLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxNQUFNLENBQUMsRUFBQTs7d0JBQTlFLEdBQUcsR0FBRyxTQUF3RTt3QkFDcEYsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sSUFBSSxDQUFDLEdBQUcsRUFBRTs0QkFDMUMsc0JBQU07eUJBQ1Q7d0JBQ0QsSUFBSSxDQUFDLFlBQVksR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7d0JBQ25ELElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTt3QkFDNUIsSUFBSSxDQUFDLFlBQVksQ0FBQyxXQUFXLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO3dCQUNuQyxJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7d0JBQ2hDLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTs7Ozs7S0FDeEI7SUFFTSxnQ0FBSyxHQUFaOztRQUNJLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFBO1FBQzdCLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDMUIsTUFBQSxJQUFJLENBQUMsU0FBUywwQ0FBRSxLQUFLLEdBQUU7UUFDdkIsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUNuQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtJQUNwQixDQUFDO0lBRU8sa0NBQU8sR0FBZjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFO1lBQ1osT0FBTTtTQUNUO1FBQ0QsUUFBUSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN6QixJQUFJLElBQUksQ0FBQyxXQUFXLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLElBQUksQ0FBQyxJQUFJLG9CQUFPLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUNqRyx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUN0RDthQUFNO1lBQ0gsdUJBQVUsQ0FBQyxPQUFPLENBQUMsd0JBQXdCLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1NBQzFEO0lBQ0wsQ0FBQztJQUVELFdBQVc7SUFDSixzQ0FBVyxHQUFsQixVQUFtQixHQUFZO1FBQzNCLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTtZQUNoQixJQUFJLENBQUMsU0FBUyxDQUFDLFlBQVksR0FBRyxHQUFHLENBQUE7U0FDcEM7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNBLG1DQUFRLEdBQWYsVUFBZ0IsRUFBVTs7UUFDdEIsSUFBSSxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQ2IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsRUFBRSxHQUFHLEVBQUUsQ0FBQTtTQUN0RDtRQUNELElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBRTtZQUNyQixJQUFJLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFBO1NBQ3BEO2FBQU0sVUFBSSxJQUFJLENBQUMsV0FBVywwQ0FBRSxLQUFLLEVBQUU7WUFDaEMsSUFBSSxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQTtTQUN6RTthQUFNO1lBQ0gsSUFBSSxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQTtTQUNwRDtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0YseUNBQWMsR0FBckI7O1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLElBQUksSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLFVBQVUsRUFBRTtZQUNqSCxNQUFBLElBQUksQ0FBQyxZQUFZLDBDQUFFLFNBQVMsQ0FBQyxLQUFLLEVBQUM7U0FDdEM7YUFBTSxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDMUIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBO1lBQy9CLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsY0FBYyxFQUFFLEdBQUcsS0FBSyxDQUFDLENBQUE7WUFDN0YsSUFBTSxLQUFLLEdBQVUsT0FBQSxvREFBNEIsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQywwQ0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQyxNQUFLLEVBQUUsQ0FBQTtZQUM1RyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLFVBQUMsRUFBRSxFQUFFLElBQUk7Z0JBQ2xELEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0JBQzlCLEVBQUUsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQTtnQkFDdkIsRUFBRSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsU0FBUyxDQUFDLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQTtZQUN4RSxDQUFDLENBQUMsQ0FBQTtTQUNMO2FBQU07WUFDSCxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUE7U0FDdEI7SUFDTCxDQUFDO0lBOUhnQixnQkFBZ0I7UUFEcEMsT0FBTztPQUNhLGdCQUFnQixDQStIcEM7SUFBRCx1QkFBQztDQS9IRCxBQStIQyxDQS9INkMsdUJBQWEsR0ErSDFEO2tCQS9Ib0IsZ0JBQWdCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ0lUWV9MVU9ZQU5HX0lELCBUSUxFX1NJWkUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBCdWlsZE9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9CdWlsZE9ialwiO1xuaW1wb3J0IEFuY2llbnRPYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vQW5jaWVudE9ialwiO1xuaW1wb3J0IENsaWNrVG91Y2hDbXB0IGZyb20gXCIuLi9jbXB0L0NsaWNrVG91Y2hDbXB0XCI7XG5pbXBvcnQgeyBBTkNJRU5UX0JUQU5JTV9ST0xFX1BPU0lUSU9OIH0gZnJvbSBcIi4vQW5jaWVudEJUQW5pbVJvbGVDb25mXCI7XG5pbXBvcnQgQmFzZUJ1aWxkQ21wdCBmcm9tIFwiLi9CYXNlQnVpbGRDbXB0XCI7XG5cbmNvbnN0IHsgY2NjbGFzcywgcHJvcGVydHkgfSA9IGNjLl9kZWNvcmF0b3I7XG5cbi8vIOmBl+i/ueW7uuetkVxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEFuY2llbnRCdWlsZENtcHQgZXh0ZW5kcyBCYXNlQnVpbGRDbXB0IHtcblxuICAgIHByaXZhdGUgbHZOb2RlOiBjYy5Ob2RlID0gbnVsbCAvL+etiee6p+iKgueCuVxuICAgIHByaXZhdGUgdXBMdkFuaW1Ob2RlOiBjYy5Ob2RlID0gbnVsbCAvL+WNh+e6p+WKqOeUu1xuICAgIHByaXZhdGUgdG91Y2hDbXB0OiBDbGlja1RvdWNoQ21wdCA9IG51bGxcblxuICAgIHByaXZhdGUgYW5jaWVudEluZm86IEFuY2llbnRPYmogPSBudWxsXG5cbiAgICBwdWJsaWMgaW5pdChkYXRhOiBCdWlsZE9iaiwgb3JpZ2luOiBjYy5WZWMyLCBvcmlnaW5ZOiBudW1iZXIsIG93bmVyOiBzdHJpbmcpIHtcbiAgICAgICAgc3VwZXIuaW5pdChkYXRhLCBvcmlnaW4sIG9yaWdpblksIG93bmVyKVxuICAgICAgICB0aGlzLmluaXRBbmNpZW50SW5mbyhkYXRhKVxuICAgICAgICB0aGlzLnRvdWNoQ21wdCA9IHRoaXMuYm9keS5hZGRDb21wb25lbnQoQ2xpY2tUb3VjaENtcHQpLm9uKHRoaXMub25DbGljaywgdGhpcylcbiAgICAgICAgdGhpcy5zeW5jUG9pbnQoKVxuICAgICAgICB0aGlzLnN5bmNaaW5kZXgoKVxuICAgICAgICAvLyDliJ3lp4vljJbnrYnnuqdcbiAgICAgICAgdGhpcy5pbml0THYoKVxuICAgICAgICB0aGlzLnVwZGF0ZUx2KHRoaXMuYW5jaWVudEluZm8ubHYpXG4gICAgICAgIC8vIOaYvuekuuaYr+WQpuWcqOWNh+e6p+S4rVxuICAgICAgICB0aGlzLnVwZGF0ZVVwTHZBbmltKClcbiAgICAgICAgcmV0dXJuIHRoaXNcbiAgICB9XG5cbiAgICAvLyDph43mlrDlkIzmraVcbiAgICBwdWJsaWMgcmVzeW5jKGRhdGE6IEJ1aWxkT2JqKSB7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGFcbiAgICAgICAgdGhpcy5pbml0QW5jaWVudEluZm8oZGF0YSlcbiAgICAgICAgdGhpcy5zeW5jUG9pbnQoKVxuICAgICAgICB0aGlzLnN5bmNaaW5kZXgoKVxuICAgICAgICB0aGlzLnNldENhbkNsaWNrKHRydWUpXG4gICAgICAgIHRoaXMudXBkYXRlTHYodGhpcy5hbmNpZW50SW5mby5sdilcbiAgICAgICAgdGhpcy51cGRhdGVVcEx2QW5pbSgpXG4gICAgICAgIHJldHVybiB0aGlzXG4gICAgfVxuXG4gICAgLy8g5Yid5aeL5YyW6YGX6L+55L+h5oGvXG4gICAgcHJpdmF0ZSBpbml0QW5jaWVudEluZm8oZGF0YTogQnVpbGRPYmopIHtcbiAgICAgICAgaWYgKGRhdGEuYUluZGV4IDwgMCkge1xuICAgICAgICAgICAgdGhpcy5hbmNpZW50SW5mbyA9IG5ldyBBbmNpZW50T2JqKCkuaW5pdChnYW1lSHByLndvcmxkLmdldE1hcENlbGxCeUluZGV4KGRhdGEuYUluZGV4KSlcbiAgICAgICAgICAgIHRoaXMuYW5jaWVudEluZm8udXBkYXRlSW5mbyh7IGx2OiBkYXRhLmx2IH0pXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmFuY2llbnRJbmZvID0gZ2FtZUhwci53b3JsZC5nZXRBbmNpZW50SW5mbyhkYXRhLmFJbmRleClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgYXN5bmMgaW5pdEx2KCkge1xuICAgICAgICBjb25zdCBwZmIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXMoJ2J1aWxkL0JVSUxEX0xWJywgY2MuUHJlZmFiLCAnYXJlYScpXG4gICAgICAgIGlmICghdGhpcy5ub2RlIHx8ICF0aGlzLm5vZGUuaXNWYWxpZCB8fCAhcGZiKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmx2Tm9kZSA9IGNjLmluc3RhbnRpYXRlMihwZmIsIHRoaXMubm9kZSlcbiAgICAgICAgdGhpcy5sdk5vZGUuekluZGV4ID0gMVxuICAgICAgICB0aGlzLmx2Tm9kZS5zZXRQb3NpdGlvbihNYXRoLmZsb29yKHRoaXMuZGF0YS5zaXplLnggKiAwLjUpICogVElMRV9TSVpFLCAzNiArICh0aGlzLmRhdGEuaWQgPT09IENJVFlfTFVPWUFOR19JRCA/IDgwIDogMCkpXG4gICAgICAgIHRoaXMubHZOb2RlLkNoaWxkKCd2YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0gJycgKyB0aGlzLmFuY2llbnRJbmZvLmx2XG4gICAgICAgIHRoaXMubHZOb2RlLmFjdGl2ZSA9IHRydWVcbiAgICB9XG5cbiAgICAvLyDliqDovb3ljYfnuqfliqjnlLtcbiAgICBwdWJsaWMgYXN5bmMgbG9hZFVwTHZBbmltKCkge1xuICAgICAgICBjb25zdCBwZmIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXMoJ2J1aWxkL0JVSUxEX0FOQ0lFTlRfQlQnLCBjYy5QcmVmYWIsICdhcmVhJylcbiAgICAgICAgaWYgKCF0aGlzLm5vZGUgfHwgIXRoaXMubm9kZS5pc1ZhbGlkIHx8ICFwZmIpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlID0gY2MuaW5zdGFudGlhdGUyKHBmYiwgdGhpcy5ub2RlKVxuICAgICAgICB0aGlzLnVwTHZBbmltTm9kZS56SW5kZXggPSAxXG4gICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlLnNldFBvc2l0aW9uKDAsIDApXG4gICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlLmFjdGl2ZSA9IGZhbHNlXG4gICAgICAgIHRoaXMudXBkYXRlVXBMdkFuaW0oKVxuICAgIH1cblxuICAgIHB1YmxpYyBjbGVhbigpIHtcbiAgICAgICAgdGhpcy51bnNjaGVkdWxlQWxsQ2FsbGJhY2tzKClcbiAgICAgICAgdGhpcy5ub2RlLnN0b3BBbGxBY3Rpb25zKClcbiAgICAgICAgdGhpcy50b3VjaENtcHQ/LmNsZWFuKClcbiAgICAgICAgdGhpcy5ub2RlLmRlc3Ryb3koKVxuICAgICAgICB0aGlzLmRhdGEgPSBudWxsXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBvbkNsaWNrKCkge1xuICAgICAgICBpZiAoIXRoaXMuZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgYXVkaW9NZ3IucGxheVNGWCgnY2xpY2snKVxuICAgICAgICBpZiAodGhpcy5hbmNpZW50SW5mbyAmJiB0aGlzLmRhdGEuYUluZGV4ID49IDAgJiYgZ2FtZUhwci5jaGVja0lzT25lQWxsaWFuY2UodGhpcy5hbmNpZW50SW5mby5vd25lcikpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCh0aGlzLmRhdGEuZ2V0VUlVcmwoKSwgdGhpcy5kYXRhKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdidWlsZC9CdWlsZEFuY2llbnRCYXNlJywgdGhpcy5kYXRhKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g6K6+572u5piv5ZCm5Y+v5Lul54K55Ye7XG4gICAgcHVibGljIHNldENhbkNsaWNrKHZhbDogYm9vbGVhbikge1xuICAgICAgICBpZiAodGhpcy50b3VjaENtcHQpIHtcbiAgICAgICAgICAgIHRoaXMudG91Y2hDbXB0LmludGVyYWN0YWJsZSA9IHZhbFxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yi35paw562J57qnXG4gICAgcHVibGljIHVwZGF0ZUx2KGx2OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMubHZOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmx2Tm9kZS5DaGlsZCgndmFsJywgY2MuTGFiZWwpLnN0cmluZyA9ICcnICsgbHZcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5kYXRhLmlzTWF4THYoKSkge1xuICAgICAgICAgICAgdGhpcy5DaGlsZCgnYm9keS92YWwnLCBjYy5NdWx0aUZyYW1lKS5zZXRGcmFtZSg1KVxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuYW5jaWVudEluZm8/Lm93bmVyKSB7XG4gICAgICAgICAgICB0aGlzLkNoaWxkKCdib2R5L3ZhbCcsIGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKE1hdGguZmxvb3IobHYgLyA1KSArIDEpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLkNoaWxkKCdib2R5L3ZhbCcsIGNjLk11bHRpRnJhbWUpLnNldEZyYW1lKDApXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDljYfnuqfliqjnlLtcbiAgICBwdWJsaWMgdXBkYXRlVXBMdkFuaW0oKSB7XG4gICAgICAgIGlmICghdGhpcy5hbmNpZW50SW5mby5vd25lciB8fCB0aGlzLmRhdGEuaXNNYXhMdigpIHx8IHRoaXMuYW5jaWVudEluZm8uc3RhdGUgIT09IDEgfHwgISF0aGlzLmFuY2llbnRJbmZvLnBhdXNlU3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlPy5zZXRBY3RpdmUoZmFsc2UpXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy51cEx2QW5pbU5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlLmFjdGl2ZSA9IHRydWVcbiAgICAgICAgICAgIHRoaXMudXBMdkFuaW1Ob2RlLkNoaWxkKCd0aW1lJywgY2MuTGFiZWxUaW1lcikucnVuKHRoaXMuYW5jaWVudEluZm8uZ2V0U3VycGx1c1RpbWUoKSAqIDAuMDAxKVxuICAgICAgICAgICAgY29uc3Qgcm9sZXM6IGFueVtdID0gQU5DSUVOVF9CVEFOSU1fUk9MRV9QT1NJVElPTlt0aGlzLmRhdGEuaWRdPy5bTWF0aC5mbG9vcih0aGlzLmFuY2llbnRJbmZvLmx2IC8gNSldIHx8IFtdXG4gICAgICAgICAgICB0aGlzLnVwTHZBbmltTm9kZS5DaGlsZCgncm9vdCcpLkl0ZW1zKHJvbGVzLCAoaXQsIGRhdGEpID0+IHtcbiAgICAgICAgICAgICAgICBpdC5zZXRQb3NpdGlvbihkYXRhLngsIGRhdGEueSlcbiAgICAgICAgICAgICAgICBpdC5zY2FsZVggPSBkYXRhLnNjYWxlWFxuICAgICAgICAgICAgICAgIGl0LkNvbXBvbmVudChjYy5BbmltYXRpb24pLnBsYXkoJ2FuY2llbnRfYnQnLCB1dC5yYW5kb20oMSwgNSkgKiAwLjEpXG4gICAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5sb2FkVXBMdkFuaW0oKVxuICAgICAgICB9XG4gICAgfVxufSJdfQ==