
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/AreaCenterModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1813aKPqRBJYIhvIDSfAGSQ', 'AreaCenterModel');
// app/script/model/area/AreaCenterModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var AreaObj_1 = require("./AreaObj");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ECode_1 = require("../../common/constant/ECode");
var NetHelper_1 = require("../../common/helper/NetHelper");
var PawnObj_1 = require("./PawnObj");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 战场管理中心
 */
var AreaCenterModel = /** @class */ (function (_super) {
    __extends(AreaCenterModel, _super);
    function AreaCenterModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.AREA_MAX_LIFE_TIME = 60 * 1000 * 1; //区域最大存在时间
        _this.areas = new Map();
        _this.watchPlayers = new Map(); //区域观战玩家
        _this.net = null;
        _this.player = null;
        _this.lookArea = null; //当前查看的区域
        return _this;
    }
    AreaCenterModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.player = this.getModel('player');
    };
    // 初始化监听
    AreaCenterModel.prototype.initNetEvent = function () {
        this.net.on('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this);
        this.net.on('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this);
    };
    AreaCenterModel.prototype.clean = function () {
        this.net.off('game/OnUpdateAreaInfo', this.OnUpdateAreaInfo, this);
        this.net.off('game/OnFSPCheckFrame', this.OnFSPCheckFrame, this);
        this.cleanAreas();
        this.watchPlayers.clear();
    };
    AreaCenterModel.prototype.getArea = function (index) {
        var _a;
        if (((_a = this.lookArea) === null || _a === void 0 ? void 0 : _a.index) === index) {
            return this.lookArea;
        }
        return this.areas.get(GameHelper_1.gameHpr.amendAreaIndex(index));
    };
    AreaCenterModel.prototype.addArea = function (area) {
        this.areas.set(area.index, area);
        return area;
    };
    AreaCenterModel.prototype.removeArea = function (index) {
        var _a;
        (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.clean();
        this.delArea(index);
    };
    AreaCenterModel.prototype.delArea = function (index) {
        this.areas.delete(index);
        NetHelper_1.netHelper.sendLeaveArea({ index: index });
    };
    AreaCenterModel.prototype.cleanAreas = function () {
        var _a;
        this.areas.forEach(function (m) { return m.clean(); });
        this.areas.clear();
        if (((_a = this.lookArea) === null || _a === void 0 ? void 0 : _a.index) >= 0) {
            this.lookArea = null;
        }
    };
    // 获取当前场景的区域信息
    AreaCenterModel.prototype.getLookArea = function () {
        var _a, _b;
        if (!this.lookArea) {
            this.lookArea = this.getArea((_b = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : -1);
        }
        return this.lookArea;
    };
    AreaCenterModel.prototype.setLookArea = function (area) {
        this.lookArea = area;
    };
    // 获取自己的主城市
    AreaCenterModel.prototype.getMeMainArea = function () {
        return this.areas.get(this.player.getMainCityIndex());
    };
    // 获取军队
    AreaCenterModel.prototype.getArmy = function (index, uid) {
        var _a;
        return (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(uid);
    };
    // 请求战场信息
    AreaCenterModel.prototype.reqAreaInfo = function (index, reinit) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, areaInfo, area;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetAreaInfo({ index: index })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        areaInfo = data === null || data === void 0 ? void 0 : data.data;
                        if (!areaInfo) {
                            return [2 /*return*/, null];
                        }
                        else if (areaInfo.battle) {
                            this.checkRemoveBattleArea();
                        }
                        area = new AreaObj_1.default().init(areaInfo);
                        this.areas.set(index, area);
                        // 是否有战斗
                        if (areaInfo.battle) {
                            area.battleBegin(areaInfo.battle, !reinit);
                        }
                        // 如果有不一样 表示没更新到 这里兼容同步一下地块信息
                        if (area.owner && ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(index)) === null || _a === void 0 ? void 0 : _a.owner) !== area.owner) {
                            GameHelper_1.gameHpr.world.syncServerCellInfo(index);
                        }
                        return [2 /*return*/, area];
                }
            });
        });
    };
    // 获取战场信息没有就请求
    AreaCenterModel.prototype.reqAreaByIndex = function (index, reinit) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var cell, owner, area;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (index === -1) {
                            return [2 /*return*/, null];
                        }
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(index);
                        owner = cell.owner || '';
                        index = (_b = (_a = cell === null || cell === void 0 ? void 0 : cell.city) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : index; //修正一下index 有可能点到其他点
                        area = this.areas.get(index);
                        if (!(!area || area.owner !== owner)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.reqAreaInfo(index, reinit)];
                    case 1:
                        area = _c.sent();
                        _c.label = 2;
                    case 2: return [2 /*return*/, area];
                }
            });
        });
    };
    // 升级建筑
    AreaCenterModel.prototype.upBuildToServer = function (item) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!GameHelper_1.gameHpr.checkCTypes(item.upCost)) {
                            return [2 /*return*/, ECode_1.ecode.RES_NOT_ENOUGH];
                        }
                        else if (this.player.getBtQueues().has('id', item.id)) {
                            return [2 /*return*/, ECode_1.ecode.YET_IN_BTQUEUE];
                        }
                        return [4 /*yield*/, NetHelper_1.netHelper.reqUpAreaBuild({ index: item.aIndex, uid: item.uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, err];
                        }
                        this.player.updateOutputByFlags(data.output);
                        this.player.updateBtQueue(data.queues);
                        // 买量数据上报
                        if (item.id === Enums_1.BUILD_NID.MAIN) {
                            if (item.lv === 4) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_5');
                            }
                            else if (item.lv === 9) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_10');
                            }
                            else if (item.lv === 14) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_15');
                            }
                            else if (item.lv === 19) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('cap_lv_20');
                            }
                        }
                        else if (item.id === Enums_1.BUILD_NID.SMITHY) {
                            if (item.lv === 9) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_10');
                            }
                            else if (item.lv === 14) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_15');
                            }
                            else if (item.lv === 19) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('smithy_lv_20');
                            }
                        }
                        else if (item.id === Enums_1.BUILD_NID.CAMP) {
                            if (item.lv === 0) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('frontier_garrison');
                            }
                            else if (item.lv === 4) {
                                EventReportHelper_1.eventReportHelper.reportGlobalEventOne('frontier_lv_5');
                            }
                        }
                        return [2 /*return*/, ''];
                }
            });
        });
    };
    // 修建建筑
    AreaCenterModel.prototype.unlockBuildToServer = function (index, id) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqAddAreaBuild({ index: index, id: id })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            this.player.updateOutputByFlags(data.output);
                            this.player.updateBtQueue(data.queues);
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.addBuild(data.build);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 训练士兵
    AreaCenterModel.prototype.drillPawnToServer = function (index, buildUid, id, armyUid, armyName) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqDrillPawn({ index: index, buildUid: buildUid, id: id, armyUid: armyUid, armyName: armyName })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyDrillPawns(data.army);
                            this.player.updateRewardItemsByFlags(data.output);
                            this.player.setFreeRecruitPawnCount(data.freeRecruitPawnCount || 0);
                            this.player.setUpRecruitPawnCount(data.upRecruitPawnCount || 0);
                            this.player.updatePawnDrillQueue(data.queues);
                        }
                        return [2 /*return*/, { err: err, army: data === null || data === void 0 ? void 0 : data.army }];
                }
            });
        });
    };
    // 治疗士兵
    AreaCenterModel.prototype.curePawnToServer = function (index, armyUid, armyName, pawnUid) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqCurePawn({ index: index, armyUid: armyUid, armyName: armyName, pawnUid: pawnUid })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            (_a = this.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                            this.player.updateRewardItemsByFlags(data.output);
                            this.player.setFreeCurePawnCount(data.freeCurePawnCount || 0);
                            this.player.updatePawnCuringQueue(data.queues);
                        }
                        return [2 /*return*/, { err: err, list: data === null || data === void 0 ? void 0 : data.queues, army: data === null || data === void 0 ? void 0 : data.army }];
                }
            });
        });
    };
    // 士兵练级
    AreaCenterModel.prototype.pawnLvingToServer = function (index, auid, puid) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqPawnLving({ index: index, auid: auid, puid: puid })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        if (!err) {
                            this.player.updateRewardItemsByFlags(data.cost);
                            this.player.setFreeLevingPawnCount(data.freeLevingPawnCount || 0);
                            this.player.updatePawnLevelingQueue(data.queues);
                        }
                        return [2 /*return*/, { err: err, armyUid: (_a = data === null || data === void 0 ? void 0 : data.army) === null || _a === void 0 ? void 0 : _a.uid }];
                }
            });
        });
    };
    // 创建一个士兵 根据招募信息
    AreaCenterModel.prototype.createPawnByDrillInfo = function (data) {
        var _a, _b;
        var conf = this.player.getConfigPawnInfo(data.id);
        var pawn = new PawnObj_1.default().init(data.id, conf.equip, 1, conf.skinId).initAnger();
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed;
        pawn.lv = data.lv;
        pawn.armyUid = data.auid;
        pawn.armyName = ((_b = (_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(data.auid)) === null || _b === void 0 ? void 0 : _b.name) || '';
        return pawn;
    };
    // 创建一个士兵 根据治疗信息
    AreaCenterModel.prototype.createPawnByCureInfo = function (data) {
        var _a, _b;
        var conf = this.player.getConfigPawnInfo(data.id);
        var pawn = new PawnObj_1.default().init(data.id, conf.equip, 1, conf.skinId).initAnger();
        pawn.attackSpeed = conf.attackSpeed || pawn.attackSpeed;
        pawn.lv = data.lv;
        pawn.armyUid = data.auid;
        pawn.armyName = ((_b = (_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getArmyByUid(data.auid)) === null || _b === void 0 ? void 0 : _b.name) || '';
        return pawn;
    };
    // 创建一个士兵 根据练级信息
    AreaCenterModel.prototype.createPawnByLvingInfo = function (pawn, data) {
        var _a;
        var newPawn = new PawnObj_1.default().init(pawn.id, pawn.equip, data.lv, pawn.skinId).initAnger();
        newPawn.aIndex = pawn.aIndex;
        newPawn.owner = pawn.owner;
        newPawn.uid = pawn.uid;
        newPawn.armyUid = pawn.armyUid;
        newPawn.armyName = pawn.armyName;
        newPawn.attackSpeed = pawn.attackSpeed;
        newPawn.treasures = pawn.treasures;
        newPawn.portrayal = (_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.clone();
        if (newPawn.portrayal) {
            newPawn.updateAttr();
        }
        return newPawn;
    };
    // 检测删除多的战斗区域
    AreaCenterModel.prototype.checkRemoveBattleArea = function () {
        var _a;
        var loolAreaIndex = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index;
        var leaveTime = 0, count = 0, area = null;
        this.areas.forEach(function (m) {
            if (!m.active && m.index >= 0 && m.isBattleing() && loolAreaIndex !== m.index && m.leaveTime > 0) {
                count += 1;
                if (leaveTime < m.leaveTime) {
                    leaveTime = m.leaveTime;
                    area = m;
                }
            }
        });
        if (count >= 5 && area) {
            area.clean();
            this.delArea(area.index);
            // cc.log('checkRemoveBattleArea index=' + area.index)
        }
    };
    // 获取观战玩家列表
    AreaCenterModel.prototype.getAreaWatchPlayersByIndex = function (index) {
        return this.watchPlayers.get(index) || [];
    };
    AreaCenterModel.prototype.update = function (dt) {
        var _this = this;
        var _a;
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode;
        var loolAreaIndex = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index;
        var now = Date.now(), life = this.AREA_MAX_LIFE_TIME;
        this.areas.forEach(function (m) {
            if (!isNoviceMode && !m.active && m.index >= 0 && loolAreaIndex !== m.index && m.leaveTime > 0 && now - m.leaveTime >= life) {
                m.clean();
                _this.delArea(m.index);
                // cc.log('delArea index=' + m.index)
            }
            else {
                m.update(dt);
            }
        });
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新战场信息
    AreaCenterModel.prototype.OnUpdateAreaInfo = function (res) {
        var _a;
        var type = res.type, index = res.index, data = res['data_' + type];
        cc.log('OnUpdateAreaInfo', Enums_1.NotifyType[type], index, data);
        var area = this.getArea(index);
        if (type === Enums_1.NotifyType.AREA_PLAYER_CHANGE) { //通知战场人员变动
            if (data === null || data === void 0 ? void 0 : data.length) {
                this.watchPlayers.set(index, data);
            }
            else {
                this.watchPlayers.delete(index);
            }
            this.emit(EventType_1.default.UPDATE_AREA_WATCH_PLAYER, index);
        }
        else if (type === Enums_1.NotifyType.AREA_CHAT) { //通知战场聊天
            this.emit(EventType_1.default.ADD_AREA_CHAT, index, data);
        }
        else if (!area) {
        }
        else if (type === Enums_1.NotifyType.BUILD_UP) { //建筑升级
            area.buildUp(data.uid, data.lv);
        }
        else if (type === Enums_1.NotifyType.MOVE_BUILD) { //移动建筑
            var build = area.getBuildByUid(data.uid);
            if (build) {
                build.point.set(data.point);
                this.emit(EventType_1.default.UPDATE_BUILD_POINT, build);
            }
        }
        else if (type === Enums_1.NotifyType.ADD_BUILD) { //添加建筑
            area.addBuild(data);
        }
        else if (type === Enums_1.NotifyType.REMOVE_BUILD) { //删除建筑
            area.removeBuild(data);
        }
        else if (type === Enums_1.NotifyType.ADD_ARMY) { //添加军队
            area.addArmy(data);
        }
        else if (type === Enums_1.NotifyType.REMOVE_ARMY) { //删除军队
            area.removeArmy(data);
        }
        else if (type === Enums_1.NotifyType.REMOVE_ARMY_BY_MARCH) { //删除军队
            area.removeArmyByMarch(data);
        }
        else if (type === Enums_1.NotifyType.UPDATE_ARMY) { //更新军队
            area.updateArmy(data);
        }
        else if (type === Enums_1.NotifyType.UPDATE_ALL_PAWN_HP) { //更新所有士兵血量
            area.updateAllArmy(data);
        }
        else if (type === Enums_1.NotifyType.MOVE_PAWN) { //移动士兵
            var army = area.getArmyByUid(data.armyUid);
            if (army) {
                army.setPawnsPoint(data.pawns || []);
                if (!army.isOwner()) { //不是我自己就通知一下
                    eventCenter.emit(EventType_1.default.UPDATE_ARMY, army);
                }
            }
        }
        else if (type === Enums_1.NotifyType.CHANGE_PAWN_ATTR) { //改变士兵属性
            data === null || data === void 0 ? void 0 : data.forEach(function (m) {
                var pawn = area.getPawnByPrecise(m.armyUid, m.uid);
                if (pawn) {
                    pawn.setAttackSpeed(m.attackSpeed);
                    pawn.changeSkin(m.skinId);
                    pawn.changeEquip(m.equip);
                }
            });
            this.emit(EventType_1.default.UPDATE_AREA_ARMY_LIST, index);
        }
        else if (type === Enums_1.NotifyType.CHANGE_PAWN_PORTRAYAL) { //改变士兵化身
            var pawn = area.getPawnByPrecise(data.armyUid, data.uid);
            if (pawn) {
                pawn.setPortrayal(data.portrayal);
            }
        }
        else if (type === Enums_1.NotifyType.UPDATE_PAWN_TREASURE) { //刷新士兵宝箱
            var pawn = area.getPawnByPrecise(data.armyUid, data.uid);
            if (pawn) {
                pawn.updateTreasures(data.treasures);
                this.emit(EventType_1.default.UPDATE_PAWN_TREASURE, pawn);
                area.updateTreasureReddot();
            }
        }
        else if (type === Enums_1.NotifyType.UPDATE_ARMY_TREASURES) { //刷新军队宝箱
            var army_1 = area.getArmyByUid(data.armyUid);
            if (army_1) {
                data.pawnTreasures.forEach(function (m) {
                    var pawn = army_1.pawns.find(function (p) { return p.uid === m.uid; });
                    if (pawn) {
                        pawn.updateTreasures(m.treasures);
                    }
                });
                this.emit(EventType_1.default.UPDATE_ARMY_TREASURES, army_1);
                area.updateTreasureReddot();
            }
        }
        else if (type === Enums_1.NotifyType.AREA_BATTLE_BEGIN) { //发生战斗
            area.initBattleData(data);
            area.battleBegin(data.battle);
        }
        else if (type === Enums_1.NotifyType.AREA_BATTLE_END) { //战斗结束
            area.battleEndByServer(data);
        }
        else if (type === Enums_1.NotifyType.CELL_TONDEN_END) { //屯田结束
            if (area.active && ((_a = area.getArmyByUid(data.auid)) === null || _a === void 0 ? void 0 : _a.isOwner())) {
                ViewHelper_1.viewHelper.showPnl('area/TondenEnd', data.treasures);
            }
        }
    };
    // 战斗帧数据
    AreaCenterModel.prototype.OnFSPCheckFrame = function (res) {
        var _a, _b;
        var data = (res === null || res === void 0 ? void 0 : res.data) || {};
        var fsp = ((_a = this.getArea(data.index)) === null || _a === void 0 ? void 0 : _a.getFspModel()) || ((_b = this.lookArea) === null || _b === void 0 ? void 0 : _b.getFspModel());
        fsp === null || fsp === void 0 ? void 0 : fsp.onFSPCheckFrame(data);
    };
    AreaCenterModel = __decorate([
        mc.addmodel('areaCenter')
    ], AreaCenterModel);
    return AreaCenterModel;
}(mc.BaseModel));
exports.default = AreaCenterModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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