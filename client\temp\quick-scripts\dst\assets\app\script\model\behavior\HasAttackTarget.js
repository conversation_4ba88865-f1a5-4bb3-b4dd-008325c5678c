
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/HasAttackTarget.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '48cdfekdW5A17ksWYXAd8zu', 'HasAttackTarget');
// app/script/model/behavior/HasAttackTarget.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseCondition_1 = require("./BaseCondition");
var BTConstant_1 = require("./BTConstant");
// 是否有攻击目标
var HasAttackTarget = /** @class */ (function (_super) {
    __extends(HasAttackTarget, _super);
    function HasAttackTarget() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    HasAttackTarget.prototype.onTick = function (dt) {
        if (!this.target.attackTarget) {
            return BTConstant_1.BTState.FAILURE;
        }
        else if (this.target.attackTarget.isDie()) {
            this.target.changeAttackTarget(null);
            return BTConstant_1.BTState.FAILURE;
        }
        return BTConstant_1.BTState.SUCCESS;
    };
    return HasAttackTarget;
}(BaseCondition_1.default));
exports.default = HasAttackTarget;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcSGFzQXR0YWNrVGFyZ2V0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGlEQUE0QztBQUM1QywyQ0FBdUM7QUFFdkMsVUFBVTtBQUNWO0lBQTZDLG1DQUFhO0lBQTFEOztJQVdBLENBQUM7SUFUVSxnQ0FBTSxHQUFiLFVBQWMsRUFBVTtRQUNwQixJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLEVBQUU7WUFDM0IsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtTQUN6QjthQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUMsS0FBSyxFQUFFLEVBQUU7WUFDekMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQTtZQUNwQyxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO1NBQ3pCO1FBQ0QsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBQ0wsc0JBQUM7QUFBRCxDQVhBLEFBV0MsQ0FYNEMsdUJBQWEsR0FXekQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZUNvbmRpdGlvbiBmcm9tIFwiLi9CYXNlQ29uZGl0aW9uXCI7XG5pbXBvcnQgeyBCVFN0YXRlIH0gZnJvbSBcIi4vQlRDb25zdGFudFwiO1xuXG4vLyDmmK/lkKbmnInmlLvlh7vnm67moIdcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEhhc0F0dGFja1RhcmdldCBleHRlbmRzIEJhc2VDb25kaXRpb24ge1xuXG4gICAgcHVibGljIG9uVGljayhkdDogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy50YXJnZXQuYXR0YWNrVGFyZ2V0KSB7XG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5GQUlMVVJFXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXJnZXQuYXR0YWNrVGFyZ2V0LmlzRGllKCkpIHtcbiAgICAgICAgICAgIHRoaXMudGFyZ2V0LmNoYW5nZUF0dGFja1RhcmdldChudWxsKVxuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuRkFJTFVSRVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBCVFN0YXRlLlNVQ0NFU1NcbiAgICB9XG59Il19