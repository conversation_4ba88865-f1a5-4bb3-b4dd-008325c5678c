
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStarConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cf38eImGmRKu6AtcTzy46XN', 'AStarConfig');
// app/script/common/astar/AStarConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DIR_POINTS_8 = exports.DIR_POINTS_4_TO_SEARCH = exports.DIR_POINTS_4 = void 0;
// 4个方向
var DIR_POINTS_4 = [];
exports.DIR_POINTS_4 = DIR_POINTS_4;
for (var i = -1; i <= 1; i++) {
    for (var j = -1; j <= 1; j++) {
        var tag = Math.abs(i) + Math.abs(j);
        if (tag === 1) {
            DIR_POINTS_4.push({ point: cc.v2(i, j), tag: 1 });
        }
    }
}
var DIR_POINTS_4_TO_SEARCH = [
    [
        { point: cc.v2(0, 1), tag: 1 },
        { point: cc.v2(1, 0), tag: 1 },
        { point: cc.v2(0, -1), tag: 1 },
        { point: cc.v2(-1, 0), tag: 1 },
    ],
    [
        { point: cc.v2(1, 0), tag: 1 },
        { point: cc.v2(0, -1), tag: 1 },
        { point: cc.v2(-1, 0), tag: 1 },
        { point: cc.v2(0, 1), tag: 1 },
    ],
    [
        { point: cc.v2(0, -1), tag: 1 },
        { point: cc.v2(-1, 0), tag: 1 },
        { point: cc.v2(0, 1), tag: 1 },
        { point: cc.v2(1, 0), tag: 1 },
    ],
    [
        { point: cc.v2(-1, 0), tag: 1 },
        { point: cc.v2(0, 1), tag: 1 },
        { point: cc.v2(1, 0), tag: 1 },
        { point: cc.v2(0, -1), tag: 1 },
    ],
];
exports.DIR_POINTS_4_TO_SEARCH = DIR_POINTS_4_TO_SEARCH;
// 8个方向
var DIR_POINTS_8 = [];
exports.DIR_POINTS_8 = DIR_POINTS_8;
for (var i = -1; i <= 1; i++) {
    for (var j = -1; j <= 1; j++) {
        if (i === 0 && j === 0) {
            continue;
        }
        var tag = Math.abs(i) + Math.abs(j);
        DIR_POINTS_8.push({ point: cc.v2(i, j), tag: tag === 1 ? tag : 1.414 });
    }
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcYXN0YXJcXEFTdGFyQ29uZmlnLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1BLE9BQU87QUFDUCxJQUFNLFlBQVksR0FBcUIsRUFBRSxDQUFBO0FBa0RyQyxvQ0FBWTtBQWpEaEIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO0lBQzFCLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRTtRQUMxQixJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDckMsSUFBSSxHQUFHLEtBQUssQ0FBQyxFQUFFO1lBQ1gsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtTQUNwRDtLQUNKO0NBQ0o7QUFDRCxJQUFNLHNCQUFzQixHQUFHO0lBQzNCO1FBQ0ksRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRTtRQUM5QixFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFO1FBQzlCLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRTtRQUMvQixFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7S0FDbEM7SUFDRDtRQUNJLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7UUFDOUIsRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFO1FBQy9CLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRTtRQUMvQixFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFO0tBQ2pDO0lBQ0Q7UUFDSSxFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7UUFDL0IsRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFO1FBQy9CLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7UUFDOUIsRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRTtLQUNqQztJQUNEO1FBQ0ksRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFO1FBQy9CLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7UUFDOUIsRUFBRSxLQUFLLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUMsRUFBRTtRQUM5QixFQUFFLEtBQUssRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUU7S0FDbEM7Q0FDSixDQUFBO0FBaUJHLHdEQUFzQjtBQWYxQixPQUFPO0FBQ1AsSUFBTSxZQUFZLEdBQXFCLEVBQUUsQ0FBQTtBQWVyQyxvQ0FBWTtBQWRoQixLQUFLLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7SUFDMUIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFO1FBQzFCLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFO1lBQ3BCLFNBQVE7U0FDWDtRQUNELElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUNyQyxZQUFZLENBQUMsSUFBSSxDQUFDLEVBQUUsS0FBSyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsRUFBRSxHQUFHLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUE7S0FDMUU7Q0FDSiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG50eXBlIFRlbXBSYW5nZVBvaW50ID0ge1xyXG4gICAgcG9pbnQ6IGNjLlZlYzJcclxuICAgIHRhZzogbnVtYmVyXHJcbn1cclxuXHJcbi8vIDTkuKrmlrnlkJFcclxuY29uc3QgRElSX1BPSU5UU180OiBUZW1wUmFuZ2VQb2ludFtdID0gW11cclxuZm9yIChsZXQgaSA9IC0xOyBpIDw9IDE7IGkrKykge1xyXG4gICAgZm9yIChsZXQgaiA9IC0xOyBqIDw9IDE7IGorKykge1xyXG4gICAgICAgIGNvbnN0IHRhZyA9IE1hdGguYWJzKGkpICsgTWF0aC5hYnMoailcclxuICAgICAgICBpZiAodGFnID09PSAxKSB7XHJcbiAgICAgICAgICAgIERJUl9QT0lOVFNfNC5wdXNoKHsgcG9pbnQ6IGNjLnYyKGksIGopLCB0YWc6IDEgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuY29uc3QgRElSX1BPSU5UU180X1RPX1NFQVJDSCA9IFtcclxuICAgIFsgLy8wLuS7juS4iuaWuei/m+WFpVxyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKDAsIDEpLCB0YWc6IDEgfSwgLy/kuIpcclxuICAgICAgICB7IHBvaW50OiBjYy52MigxLCAwKSwgdGFnOiAxIH0sIC8v5Y+zXHJcbiAgICAgICAgeyBwb2ludDogY2MudjIoMCwgLTEpLCB0YWc6IDEgfSwgLy/kuItcclxuICAgICAgICB7IHBvaW50OiBjYy52MigtMSwgMCksIHRhZzogMSB9LCAvL+W3plxyXG4gICAgXSxcclxuICAgIFsgLy8xLuS7juWPs+aWuei/m+WFpVxyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKDEsIDApLCB0YWc6IDEgfSwgLy/lj7NcclxuICAgICAgICB7IHBvaW50OiBjYy52MigwLCAtMSksIHRhZzogMSB9LCAvL+S4i1xyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKC0xLCAwKSwgdGFnOiAxIH0sIC8v5bemXHJcbiAgICAgICAgeyBwb2ludDogY2MudjIoMCwgMSksIHRhZzogMSB9LCAvL+S4ilxyXG4gICAgXSxcclxuICAgIFsgLy8yLuS7juS4i+aWuei/m+WFpVxyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKDAsIC0xKSwgdGFnOiAxIH0sIC8v5LiLXHJcbiAgICAgICAgeyBwb2ludDogY2MudjIoLTEsIDApLCB0YWc6IDEgfSwgLy/lt6ZcclxuICAgICAgICB7IHBvaW50OiBjYy52MigwLCAxKSwgdGFnOiAxIH0sIC8v5LiKXHJcbiAgICAgICAgeyBwb2ludDogY2MudjIoMSwgMCksIHRhZzogMSB9LCAvL+WPs1xyXG4gICAgXSxcclxuICAgIFsgLy8zLuS7juW3puaWuei/m+WFpVxyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKC0xLCAwKSwgdGFnOiAxIH0sIC8v5bemXHJcbiAgICAgICAgeyBwb2ludDogY2MudjIoMCwgMSksIHRhZzogMSB9LCAvL+S4ilxyXG4gICAgICAgIHsgcG9pbnQ6IGNjLnYyKDEsIDApLCB0YWc6IDEgfSwgLy/lj7NcclxuICAgICAgICB7IHBvaW50OiBjYy52MigwLCAtMSksIHRhZzogMSB9LCAvL+S4i1xyXG4gICAgXSxcclxuXVxyXG5cclxuLy8gOOS4quaWueWQkVxyXG5jb25zdCBESVJfUE9JTlRTXzg6IFRlbXBSYW5nZVBvaW50W10gPSBbXVxyXG5mb3IgKGxldCBpID0gLTE7IGkgPD0gMTsgaSsrKSB7XHJcbiAgICBmb3IgKGxldCBqID0gLTE7IGogPD0gMTsgaisrKSB7XHJcbiAgICAgICAgaWYgKGkgPT09IDAgJiYgaiA9PT0gMCkge1xyXG4gICAgICAgICAgICBjb250aW51ZVxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCB0YWcgPSBNYXRoLmFicyhpKSArIE1hdGguYWJzKGopXHJcbiAgICAgICAgRElSX1BPSU5UU184LnB1c2goeyBwb2ludDogY2MudjIoaSwgaiksIHRhZzogdGFnID09PSAxID8gdGFnIDogMS40MTQgfSlcclxuICAgIH1cclxufVxyXG5cclxuZXhwb3J0IHtcclxuICAgIFRlbXBSYW5nZVBvaW50LFxyXG4gICAgRElSX1BPSU5UU180LFxyXG4gICAgRElSX1BPSU5UU180X1RPX1NFQVJDSCxcclxuICAgIERJUl9QT0lOVFNfOCxcclxufSJdfQ==