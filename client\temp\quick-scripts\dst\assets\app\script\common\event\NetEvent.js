
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/event/NetEvent.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd95a30h+3FBnrv8G8rWkebx', 'NetEvent');
// app/script/common/event/NetEvent.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 网络事件
 */
exports.default = {
    NET_DISCONNECT: 'NET_DISCONNECT',
    NET_RECONNECT: 'NET_RECONNECT',
    NET_REQ_BEGIN: 'NET_REQ_BEGIN',
    NET_REQ_END: 'NET_REQ_END',
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcZXZlbnRcXE5ldEV2ZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0dBRUc7QUFDSCxrQkFBZTtJQUNYLGNBQWMsRUFBRSxnQkFBZ0I7SUFDaEMsYUFBYSxFQUFFLGVBQWU7SUFDOUIsYUFBYSxFQUFFLGVBQWU7SUFDOUIsV0FBVyxFQUFFLGFBQWE7Q0FDN0IsQ0FBQSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxyXG4gKiDnvZHnu5zkuovku7ZcclxuICovXHJcbmV4cG9ydCBkZWZhdWx0IHtcclxuICAgIE5FVF9ESVNDT05ORUNUOiAnTkVUX0RJU0NPTk5FQ1QnLCAvL+e9kee7nOaWreW8gFxyXG4gICAgTkVUX1JFQ09OTkVDVDogJ05FVF9SRUNPTk5FQ1QnLCAvL+e9kee7nOmHjeaWsOi/nuaOpVxyXG4gICAgTkVUX1JFUV9CRUdJTjogJ05FVF9SRVFfQkVHSU4nLCAvL+e9kee7nOivt+axguW8gOWni1xyXG4gICAgTkVUX1JFUV9FTkQ6ICdORVRfUkVRX0VORCcsIC8v572R57uc6K+35rGC57uT5p2fXHJcbn0iXX0=