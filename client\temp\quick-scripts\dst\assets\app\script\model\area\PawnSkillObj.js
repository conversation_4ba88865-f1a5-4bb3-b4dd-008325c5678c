
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/PawnSkillObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e9caa6xOHBPcZaeVf4n5Aa2', 'PawnSkillObj');
// app/script/model/area/PawnSkillObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
// 士兵技能信息
var PawnSkillObj = /** @class */ (function () {
    function PawnSkillObj() {
        this.id = 0;
        this.baseId = 0;
        this.lv = 0;
        this.intensifyType = 0; //强化类型
        this.needAttackTime = 0; //需要攻击的时间
        this.needHitTime = 0; //需要击中的时间
        this.bulletId = 0; //子弹id
        this.json = null;
    }
    PawnSkillObj.prototype.init = function (id) {
        this.id = id;
        this.baseId = Math.floor(id / 100);
        this.lv = id % 100;
        this.json = assetsMgr.getJsonData('pawnSkill', id);
        this.type = this.json.type;
        this.initAttackAnimTime();
        return this;
    };
    PawnSkillObj.prototype.initAttackAnimTime = function (pawn) {
        var _a, _b, _c;
        var attackAnimTimeStr = this.json.anim_time;
        if (!pawn) {
        }
        else if (pawn.isHero()) {
            var idStr_1 = this.baseId + '';
            (_b = (_a = assetsMgr.getJsonData('portrayalBase', pawn.portrayal.id)) === null || _a === void 0 ? void 0 : _a.skill_anim_time) === null || _b === void 0 ? void 0 : _b.split('|').find(function (str) {
                var arr = str.split(':');
                if (arr.length === 1) {
                    attackAnimTimeStr = arr[0];
                }
                else if (arr.length === 2 && arr[0] === idStr_1) {
                    attackAnimTimeStr = arr[1];
                }
                else {
                    return false;
                }
                return true;
            });
        }
        else if (pawn.skinId > 0) {
            attackAnimTimeStr = ((_c = assetsMgr.getJsonData('pawnSkin', pawn.skinId)) === null || _c === void 0 ? void 0 : _c.skill_anim_time) || attackAnimTimeStr;
        }
        if (attackAnimTimeStr) {
            var _d = __read(ut.stringToNumbers(attackAnimTimeStr, ','), 3), a = _d[0], b = _d[1], c = _d[2];
            this.needAttackTime = Math.floor(a * 1000);
            this.needHitTime = Math.floor(b * 1000);
            this.bulletId = c;
        }
    };
    Object.defineProperty(PawnSkillObj.prototype, "use_type", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.use_type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnSkillObj.prototype, "target", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnSkillObj.prototype, "value", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.value; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnSkillObj.prototype, "params", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.params; },
        enumerable: false,
        configurable: true
    });
    // 设置强化类型
    PawnSkillObj.prototype.setIntensifyType = function (type) {
        this.intensifyType = type || 0;
    };
    // 是否专属强化
    PawnSkillObj.prototype.isExclusiveIntensify = function () {
        return this.intensifyType === 1;
    };
    return PawnSkillObj;
}());
exports.default = PawnSkillObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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