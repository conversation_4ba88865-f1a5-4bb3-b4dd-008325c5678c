
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/BuffObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '05eb3QvRrhJcbVDvzhwuBIz', 'BuffObj');
// app/script/model/area/BuffObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
// buff
var BuffObj = /** @class */ (function () {
    function BuffObj() {
        this.lv = 0;
        this.provider = ''; //施加者
        this.needRound = 0;
        this.round = 0; //持续回合数 -1.表示永久
        this.times = 0; //触发次数 -1.表示永久
        this.value = 0;
        this.base = null;
        this.attr = null;
    }
    BuffObj.prototype.init = function (type, provider, lv, roundAdd) {
        this.type = type;
        this.provider = provider;
        this.lv = lv || 1;
        this.base = assetsMgr.getJsonData('buffBase', this.type);
        var json = this.attr = assetsMgr.getJsonData('buffAttr', this.type * 1000 + this.lv);
        if (json) {
            if (!roundAdd && !!json.round_add) {
                roundAdd = json.round_add;
            }
            this.round = json.round > 0 ? json.round + roundAdd : -1;
            this.times = json.times || -1;
            this.value = json.value || 0;
        }
        this.needRound = this.round > 0 ? this.round - roundAdd : -1;
        return this;
    };
    BuffObj.prototype.fromSvr = function (data) {
        this.type = data.type;
        this.lv = data.lv || 1;
        this.provider = data.provider || '';
        this.needRound = data.needRound || -1;
        this.round = data.round || -1;
        this.times = data.times || -1;
        this.value = data.value || 0;
        this.base = assetsMgr.getJsonData('buffBase', this.type);
        this.attr = assetsMgr.getJsonData('buffAttr', this.type * 1000 + this.lv);
        return this;
    };
    BuffObj.prototype.strip = function () {
        return {
            type: this.type,
            lv: this.lv,
            provider: this.provider,
            round: this.round,
            needRound: this.needRound,
            times: this.times,
            value: this.value
        };
    };
    Object.defineProperty(BuffObj.prototype, "icon", {
        get: function () { var _a; return (_a = this.base) === null || _a === void 0 ? void 0 : _a.icon; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "iconType", {
        get: function () { var _a; return ((_a = this.base) === null || _a === void 0 ? void 0 : _a.icon_type) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "effectType", {
        get: function () { var _a; return ((_a = this.base) === null || _a === void 0 ? void 0 : _a.effect_type) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "name", {
        get: function () { var _a; return (_a = this.base) === null || _a === void 0 ? void 0 : _a.name; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "desc", {
        get: function () { var _a; return (_a = this.base) === null || _a === void 0 ? void 0 : _a.desc; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "tip", {
        get: function () { var _a; return (_a = this.base) === null || _a === void 0 ? void 0 : _a.tip; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuffObj.prototype, "tipParams", {
        get: function () { var _a; return (_a = this.base) === null || _a === void 0 ? void 0 : _a.tip_params.split('|'); },
        enumerable: false,
        configurable: true
    });
    // 获取说明参数
    BuffObj.prototype.getDescParams = function () {
        if (this.type === Enums_1.BuffType.PARRY
            || this.type === Enums_1.BuffType.PARRY_001
            || this.type === Enums_1.BuffType.PARRY_102
            || this.type === Enums_1.BuffType.RAGE
            || this.type === Enums_1.BuffType.STEADY_ATTACK) {
            return this.times;
        }
        else if (this.type === Enums_1.BuffType.STAND_SHIELD //立盾
            || this.type === Enums_1.BuffType.SERIOUS_INJURY //重伤
            || this.type === Enums_1.BuffType.DESTROY_WEAPONS //缴械
            || this.type === Enums_1.BuffType.POISONING_CUR_HP //中毒
            || this.type === Enums_1.BuffType.DAMAGE_INCREASE //军旗 伤害提升
            || this.type === Enums_1.BuffType.DAMAGE_REDUCE //军旗 伤害降低
            || this.type === Enums_1.BuffType.KUROU_ADD_ATTACK //黄盖 苦肉加攻击力
            || this.type === Enums_1.BuffType.NAKED_CLOTHES //许褚 裸衣
            || this.type === Enums_1.BuffType.RECORD_PAWSTRIKE //丧彪 记录爪击伤害
        ) {
            return Math.floor(this.value * 100) + '%';
        }
        else if (this.type === Enums_1.BuffType.HIT_SUCK_BLOOD
            || this.type === Enums_1.BuffType.DEFEND_HALO //政策 守卫光环
            || this.type === Enums_1.BuffType.ATTACK_HALO //政策 进攻号角
            || this.type === Enums_1.BuffType.ADD_DMG_TO_MONSTER //政策 野怪克星
            || this.type === Enums_1.BuffType.ADD_DMG_TO_BUILD //政策 摧坚巧工
            || this.type === Enums_1.BuffType.DODGE
            || this.type === Enums_1.BuffType.DAMAGE_DECREASE
            || this.type === Enums_1.BuffType.RODELERO_ADD_ATTACK //守护之剑
            || this.type === Enums_1.BuffType.TURNTHEBLADE //招架
            || this.type === Enums_1.BuffType.PROTECTION_NIE //徐盛 庇护
            || this.type === Enums_1.BuffType.ASSAULT //张辽 突袭
            || this.type === Enums_1.BuffType.CHAOS //王异 混乱
            || this.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK_S //韬略 加攻击力
            || this.type === Enums_1.BuffType.ROYAL_BLUE_DODGE //赵云 龙胆 闪避
            || this.type === Enums_1.BuffType.ROYAL_BLUE_DAMAGE //赵云 龙胆 伤害
            || this.type === Enums_1.BuffType.DYING_RECOVER //周泰 濒死恢复
            || this.type === Enums_1.BuffType.WIRE_CHAIN //周瑜 链索
            || this.type === Enums_1.BuffType.BREAK_ENEMY_RANKS //高顺 陷阵
            || this.type === Enums_1.BuffType.RESOLUTE //于禁 毅重
            || this.type === Enums_1.BuffType.LIAN_PO_ATTACK //廉颇 攻
            || this.type === Enums_1.BuffType.LIAN_PO_DEFEND //廉颇 守
            || this.type === Enums_1.BuffType.JUMPSLASH_DAMAGE //霍去病 跳斩伤害
            || this.type === Enums_1.BuffType.THUNDERS_DEFENSE //梁红玉 擂鼓防御
            || this.type === Enums_1.BuffType.THUNDERS_RECOVER //梁红玉 擂鼓恢复
            || this.type === Enums_1.BuffType.OBSIDIAN_ARMOR_DEFENSE //黑曜铠 防御
            || this.type === Enums_1.BuffType.OBSIDIAN_ARMOR_NEGATIVE //黑曜铠 受到伤害提高
            || this.type === Enums_1.BuffType.INFECTION_PLAGUE //老鼠 瘟疫
            || this.type === Enums_1.BuffType.ANTICIPATION_ATTACK //李牧 蓄势攻击
            || this.type === Enums_1.BuffType.LONGYUAN_SWORD_ATTACK //龙渊剑
        ) {
            return this.value + '%';
        }
        else if (this.type === Enums_1.BuffType.LV_1_POWER) { //政策 一级之力
            var str = this.value + '%';
            return [str, str];
        }
        else if (this.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || this.type === Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD) {
            return this.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK ? [Math.floor(this.value * 100) + '%', (this.tempParam || 0) + '%'] : [(this.tempParam || 0) + '%', Math.floor(this.value * 100) + '%'];
        }
        else if (this.type === Enums_1.BuffType.DELAY_DEDUCT_HP) { //金丝软甲 延迟扣血
            return [this.value, this.tempParam];
        }
        else if (this.type === Enums_1.BuffType.POISONING_MAX_HP) { //中毒 最大生命
            return Number((this.value * 100).toFixed(1)) + '%';
        }
        else if (this.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
            return this.tempParam ? [this.tempParam[0] + '%', this.tempParam[1] + '%'] : ['1%', '1%'];
        }
        else if (this.type === Enums_1.BuffType.WISDOM_COURAGE) { //姜维 智勇
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.JIANG_WEI);
            return [json.target * this.value, (json.params * this.value) + '%'];
        }
        else if (this.type === Enums_1.BuffType.VALOR) { //李嗣业 勇猛
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.LI_SIYE);
            return [json.target * this.value, (json.params * this.value) + '%'];
        }
        else if (this.type === Enums_1.BuffType.MORALE) { //曹操 士气
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO);
            return [json.params * this.value, json.target * this.value];
        }
        else if (this.type === Enums_1.BuffType.TOUGH) { //曹仁 坚韧
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_REN);
            return json.target * this.value;
        }
        else if (this.type === Enums_1.BuffType.WOOSUNG) { //关羽 武圣
            return [this.value + '%', this.times];
        }
        else if (this.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
            return Math.min(this.tempParam, this.value) + '%';
        }
        else if (this.type === Enums_1.BuffType.LONG_RANGE_RAID) { //夏侯渊 奔袭
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.XIA_HOUYUAN);
            return (json.params * this.value) + '%';
        }
        else if (this.type === Enums_1.BuffType.CHECK_ABNEGATION) { //吕蒙 检测克己
            var json = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.LV_MENG);
            return Math.max(0, json.target - this.value);
        }
        else if (this.type === Enums_1.BuffType.ARMOR_PENETRATION) { //徐晃 破甲
            return (this.value * this.lv) + '%';
        }
        else if (this.type === Enums_1.BuffType.BEHEADED_GENERAL) { //秦琼 斩将
            return [this.value + '%', this.times];
        }
        else if (this.type === Enums_1.BuffType.CHECK_LITTLE_GIRL) { //孙尚香 枭姬
            return [this.value, this.tempParam];
        }
        else if (this.type === Enums_1.BuffType.POISONED_WINE) { //李儒 鸩毒
            return [(this.lv + 9) + '%', this.value];
        }
        else if (this.type === Enums_1.BuffType.THREE_AXES) { //程咬金 三斧
            return Math.max(0, 4 - this.value);
        }
        else if (this.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
            return [(this.value * 4) + '%', (this.value * 6) + '%'];
        }
        else if (this.type === Enums_1.BuffType.TYRANNICAL) { //董卓 暴虐
            return (this.value * 4) + '%';
        }
        else if (this.type === Enums_1.BuffType.RECURRENCE) { //孟获 再起
            return (this.value * this.tempParam) + '%';
        }
        else if (this.type === Enums_1.BuffType.KERIAN) { //辛弃疾 金戈
            return [this.value + '%', '30%'];
        }
        return this.value;
    };
    BuffObj.prototype.updateRound = function (val) {
        if (this.round === -1) {
            return false;
        }
        this.round += val;
        return this.round <= 0;
    };
    BuffObj.prototype.updateTimes = function (val) {
        if (this.times === -1) {
            return false;
        }
        this.times += val;
        return this.times <= 0;
    };
    BuffObj.prototype.updateLv = function (lv) {
        this.lv = lv || 1;
        var json = this.attr = assetsMgr.getJsonData('buffAttr', this.type * 1000 + this.lv);
        if (json) {
            this.value = json.value || 0;
        }
    };
    // 是否有护盾
    BuffObj.prototype.isHasShield = function () {
        return !!Constant_1.SHIELD_BUFF[this.type];
    };
    BuffObj.prototype.addValueMul = function (mul) {
        this.value = Math.round(this.value * mul);
        return this;
    };
    BuffObj.prototype.setValue = function (val) {
        this.value = val;
    };
    BuffObj.prototype.setRound = function (val) {
        var roundAdd = Math.max(0, this.round - this.needRound);
        this.round = val;
        this.needRound = this.round > 0 ? this.round - roundAdd : -1;
        return this;
    };
    BuffObj.prototype.addRound = function (val) {
        if (val > 0) {
            this.setRound(this.round + val);
        }
        return this;
    };
    BuffObj.EMPTY_BUFF = new BuffObj();
    return BuffObj;
}());
exports.default = BuffObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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