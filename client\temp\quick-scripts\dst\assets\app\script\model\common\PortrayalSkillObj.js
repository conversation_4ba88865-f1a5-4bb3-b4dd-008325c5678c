
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/PortrayalSkillObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7f4a6hnQKhEhKQPzLvMVfiO', 'PortrayalSkillObj');
// app/script/model/common/PortrayalSkillObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 画像技能效果
var PortrayalSkillObj = /** @class */ (function () {
    function PortrayalSkillObj() {
        this.id = 0;
        this.value = 0;
        this.json = null;
    }
    PortrayalSkillObj.prototype.init = function (id, value) {
        this.id = id;
        this.value = value;
        this.json = assetsMgr.getJsonData('portrayalSkill', id);
        return this;
    };
    Object.defineProperty(PortrayalSkillObj.prototype, "name", {
        get: function () { return 'portrayalSkillText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalSkillObj.prototype, "desc", {
        get: function () { return 'portrayalSkillText.desc_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalSkillObj.prototype, "target", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.target; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalSkillObj.prototype, "params", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.params; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalSkillObj.prototype, "value_max", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.value_max; },
        enumerable: false,
        configurable: true
    });
    // 获取说明参数
    PortrayalSkillObj.prototype.getDescParams = function (showRnage) {
        var _a, _b, _c;
        var arr = [];
        if ((_a = this.json) === null || _a === void 0 ? void 0 : _a.value) {
            var val = this.value + '';
            if (showRnage) {
                var value_max_1 = this.value_max;
                var valueArr = ut.stringToNumbers(this.json.value, ',');
                var text = valueArr.join2(function (m, i) { return i === value_max_1 ? "<color=#7EC373>" + m + "</c>" : m + ''; }, '-');
                val = this.value + "<color=#B6A591>[" + text + "]</c>";
            }
            arr.push(val + this.json.suffix);
        }
        if ((_b = this.json) === null || _b === void 0 ? void 0 : _b.target) {
            arr.push((_c = this.json) === null || _c === void 0 ? void 0 : _c.target);
        }
        return arr;
    };
    // 是否天选
    PortrayalSkillObj.prototype.isChosenOne = function () {
        return ut.stringToNumbers(this.json.value, ',')[this.value_max] === this.value;
    };
    return PortrayalSkillObj;
}());
exports.default = PortrayalSkillObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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