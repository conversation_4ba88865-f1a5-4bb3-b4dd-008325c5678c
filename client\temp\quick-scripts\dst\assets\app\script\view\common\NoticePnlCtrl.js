
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/NoticePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd1003UJvWdEwoiWpuCvlwrb', 'NoticePnlCtrl');
// app/script/view/common/NoticePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var NoticePnlCtrl = /** @class */ (function (_super) {
    __extends(NoticePnlCtrl, _super);
    function NoticePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        _this.loadingNode_ = null; // path://root/loading_n
        return _this;
    }
    //@end
    NoticePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoticePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    NoticePnlCtrl.prototype.onEnter = function (data) {
        this.load();
    };
    NoticePnlCtrl.prototype.onRemove = function () {
    };
    NoticePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/page/content/item/open_be
    NoticePnlCtrl.prototype.onClickOpen = function (event, _data) {
        var msg = event.target.parent;
        msg.parent.children.forEach(function (m) { return m.active = true; });
        msg.active = false;
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    NoticePnlCtrl.prototype.load = function () {
        return __awaiter(this, void 0, void 0, function () {
            var empty, list;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        empty = this.listSv_.Child('empty');
                        empty.active = false;
                        this.listSv_.content.children[0].active = false;
                        this.loadingNode_.active = true;
                        return [4 /*yield*/, GameHelper_1.gameHpr.getGameNoticeText()];
                    case 1:
                        list = _a.sent();
                        this.loadingNode_.active = false;
                        empty.active = list.length === 0;
                        this.listSv_.Items(list, function (it, data) {
                            it.Child('top/new').active = data.Type === 0;
                            it.Child('top/title', cc.RichText).string = data.Title;
                            it.Child('content').Items(data.Contents, function (node, text) {
                                var rt = node.Child('val', cc.RichText);
                                if (text.includes('<size=')) { // 小标题
                                    rt.fontSize = 48;
                                    rt.lineHeight = 56;
                                    rt.Color('#3F332F');
                                    rt.string = text;
                                    node.x = -2;
                                    node.height = 36;
                                }
                                else if (!text) { // 占位空行
                                    rt.string = '';
                                    node.x = 12;
                                    node.height = 10;
                                }
                                else { // 更新内容
                                    rt.fontSize = 44;
                                    rt.lineHeight = 52;
                                    rt.Color('#756963');
                                    rt.string = text;
                                    node.x = 12;
                                    node.height = rt.node.height * rt.node.scaleY;
                                }
                            });
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    NoticePnlCtrl = __decorate([
        ccclass
    ], NoticePnlCtrl);
    return NoticePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoticePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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