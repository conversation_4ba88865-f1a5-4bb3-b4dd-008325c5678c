
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/AntiCheatPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9924a1U7R9IH65B+TTbqg4F', 'AntiCheatPnlCtrl');
// app/script/view/main/AntiCheatPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AntiCheatPnlCtrl = /** @class */ (function (_super) {
    __extends(AntiCheatPnlCtrl, _super);
    function AntiCheatPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.selectNode_ = null; // path://root/select_n
        _this.headNode_ = null; // path://root/head_n
        _this.countDownNode_ = null; // path://root/count_down_n
        _this.refreshNode_ = null; // path://root/buttons/refresh_be_n
        //@end
        _this.net = null;
        _this.heads = [];
        _this.options = [];
        _this.selectId = 0;
        _this.refreshInterval = 0;
        _this.labelTimer = null;
        _this.answerKey = '';
        return _this;
    }
    AntiCheatPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AntiCheatPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.net = this.getModel('net');
                this.labelTimer = this.countDownNode_.Child('val', cc.LabelTimer);
                return [2 /*return*/];
            });
        });
    };
    AntiCheatPnlCtrl.prototype.onEnter = function () {
        this.selectId = 0;
        this.refreshInterval = 0;
        this.updateViewInfo();
        this.updateRefreshButton();
    };
    AntiCheatPnlCtrl.prototype.onRemove = function () {
    };
    AntiCheatPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/head_n/head_be
    AntiCheatPnlCtrl.prototype.onClickHead = function (event, _data) {
        var _a;
        var id = (_a = event.target.Data) === null || _a === void 0 ? void 0 : _a.id;
        if (id) {
            this.selectAnswer(id);
        }
    };
    // path://root/buttons/refresh_be_n
    AntiCheatPnlCtrl.prototype.onClickRefresh = function (event, data) {
        this.updateViewInfo();
        this.refreshInterval = 3;
        this.updateRefreshButton();
    };
    // path://root/buttons/ok_be
    AntiCheatPnlCtrl.prototype.onClickOk = function (event, data) {
        this.answerAntiCheat(false);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 更新界面信息
    AntiCheatPnlCtrl.prototype.updateViewInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, answer, type, i, option;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetAntiCheatQuestion', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                        }
                        else if (data) {
                            this.answerKey = data.item;
                            answer = Number(data.item.split('item_')[1]), type = answer === 5 ? 6 : answer;
                            // 更新答案
                            this.selectNode_.setLocaleKey('ui.anti_cheat_select', 'ui.pawn_type_' + type);
                            // 更新倒计时
                            this.updateSurplusTime(data.surplusTime);
                            // 更新选项
                            this.options.length = 0;
                            for (i = 0; i < data.options.length; i++) {
                                option = assetsMgr.getJsonData('antiCheat', data.options[i]);
                                this.options.push(option);
                            }
                            this.updateOptions();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 更新倒计时
    AntiCheatPnlCtrl.prototype.updateSurplusTime = function (time) {
        var _this = this;
        this.labelTimer.run(time * 0.001, function () { return _this.answerAntiCheat(true); });
        this.labelTimer.setPause(false);
    };
    // 打乱选项
    AntiCheatPnlCtrl.prototype.updateOptions = function () {
        var _this = this;
        var width = this.headNode_.width, height = this.headNode_.height, offsetX = width / this.options.length;
        this.headNode_.Items(ut.shuffleArray(this.options), function (it, data, i) {
            it.Data = data;
            it.Child('select').active = false;
            var icon = it.Child('val');
            var scale = ut.random(85, 115) * 0.01;
            icon.angle = ut.random(-30, 30);
            icon.scaleY = scale;
            icon.scaleX = ut.chance(50) ? -scale : scale;
            it.x = offsetX * i + (it.width * scale) / 2 + 10;
            it.y = ut.random(-it.height * scale / 3, it.height * scale / 3);
            ResHelper_1.resHelper.loadIcon('role/' + data.icon, icon, _this.key);
            _this.heads.push(it);
        });
    };
    // 选择
    AntiCheatPnlCtrl.prototype.selectAnswer = function (id) {
        this.selectId = id;
        for (var i = 0; i < this.heads.length; i++) {
            var head = this.heads[i];
            head.Child('select').active = head.Data.id === id ? true : false;
        }
    };
    // 提交答案
    AntiCheatPnlCtrl.prototype.answerAntiCheat = function (timeOver) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, key;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.selectId === 0 && !timeOver) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.select_one_tip')];
                        }
                        this.labelTimer.setPause(true);
                        return [4 /*yield*/, this.net.request('game/HD_AntiCheatAnswer', { answer: timeOver ? -1 : this.selectId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            ViewHelper_1.viewHelper.showAlert(err);
                        }
                        else if (data) {
                            if (data.rst) { // 通过检测
                                ViewHelper_1.viewHelper.showAlert('toast.anti_cheat_succ');
                                this.hide();
                            }
                            else {
                                this.taReport();
                                if (data.notPassCount >= 3) { // 未通过3次，踢下线，走登录封禁
                                    this.hide();
                                }
                                else {
                                    this.selectId = 0;
                                    key = data.notPassCount ? 'toast.anti_cheat_fail_' + data.notPassCount : 'toast.anti_cheat_weak_fail';
                                    ViewHelper_1.viewHelper.showAlert(key, {
                                        cb: function () {
                                            if (data.notPassCount || !data.surplusTime) { // 请求新的   
                                                _this.updateViewInfo();
                                            }
                                            else if (data.surplusTime) { // 打乱旧的，还有剩余时间才打乱，没有剩余时间直接请求新的
                                                _this.updateSurplusTime(data.surplusTime);
                                                _this.updateOptions();
                                            }
                                        }
                                    });
                                }
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // ta上报
    AntiCheatPnlCtrl.prototype.taReport = function () {
        var _this = this;
        var corr_answer_id = 0;
        var target = this.options.find(function (m) { return m[_this.answerKey] === 1; });
        if (target) {
            corr_answer_id = target.id;
            var others = this.options.filter(function (m) { return m.id !== corr_answer_id; });
            var remaining = others.map(function (m) { return m.id; }).sort(function (a, b) { return a - b; });
            var all_answerIds = [corr_answer_id].concat(remaining);
            TaHelper_1.taHelper.track('ta_cheat_wrong', { all_answerIds: all_answerIds, sele_answer_id: this.selectId, corr_answer_id: corr_answer_id });
        }
    };
    AntiCheatPnlCtrl.prototype.updateRefreshButton = function () {
        var _this = this;
        var time = Math.floor(this.refreshInterval);
        this.refreshInterval = Math.max(0, time);
        this.refreshNode_.opacity = this.refreshInterval ? 150 : 255;
        this.refreshNode_.Component(cc.Button).interactable = !this.refreshInterval;
        var node = this.refreshNode_.Swih(this.refreshInterval ? 'time' : 'val')[0];
        if (this.refreshInterval) {
            node.Component(cc.LabelTimer).run(this.refreshInterval + 1, function () { return _this.isValid && _this.updateRefreshButton(); });
        }
    };
    AntiCheatPnlCtrl.prototype.update = function (dt) {
        if (this.refreshInterval > 0) {
            this.refreshInterval -= dt;
        }
    };
    AntiCheatPnlCtrl = __decorate([
        ccclass
    ], AntiCheatPnlCtrl);
    return AntiCheatPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AntiCheatPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXEFudGlDaGVhdFBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsMkRBQTBEO0FBQzFELHlEQUF3RDtBQUN4RCw2REFBNEQ7QUFHcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBOEMsb0NBQWM7SUFBNUQ7UUFBQSxxRUErTEM7UUE3TEEsMEJBQTBCO1FBQ2xCLGlCQUFXLEdBQVksSUFBSSxDQUFBLENBQUMsdUJBQXVCO1FBQ25ELGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQyxxQkFBcUI7UUFDL0Msb0JBQWMsR0FBWSxJQUFJLENBQUEsQ0FBQywyQkFBMkI7UUFDMUQsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQyxtQ0FBbUM7UUFDeEUsTUFBTTtRQUVFLFNBQUcsR0FBaUIsSUFBSSxDQUFBO1FBRXhCLFdBQUssR0FBYyxFQUFFLENBQUE7UUFDckIsYUFBTyxHQUFVLEVBQUUsQ0FBQTtRQUNuQixjQUFRLEdBQUcsQ0FBQyxDQUFBO1FBQ1oscUJBQWUsR0FBRyxDQUFDLENBQUE7UUFDbkIsZ0JBQVUsR0FBa0IsSUFBSSxDQUFBO1FBRWhDLGVBQVMsR0FBVyxFQUFFLENBQUE7O0lBOEsvQixDQUFDO0lBNUtPLDBDQUFlLEdBQXRCO1FBQ0MsT0FBTyxFQUFFLENBQUE7SUFDVixDQUFDO0lBRVksbUNBQVEsR0FBckI7OztnQkFDQyxJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUE7Z0JBQy9CLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQTs7OztLQUNqRTtJQUVNLGtDQUFPLEdBQWQ7UUFDQyxJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQTtRQUNqQixJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQTtRQUN4QixJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDckIsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUE7SUFDM0IsQ0FBQztJQUVNLG1DQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sa0NBQU8sR0FBZDtRQUNDLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDeEMsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0IsNkJBQTZCO0lBQzdCLHNDQUFXLEdBQVgsVUFBWSxLQUEwQixFQUFFLEtBQWE7O1FBQ3BELElBQU0sRUFBRSxTQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSwwQ0FBRSxFQUFFLENBQUE7UUFDaEMsSUFBSSxFQUFFLEVBQUU7WUFDUCxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1NBQ3JCO0lBQ0YsQ0FBQztJQUVELG1DQUFtQztJQUNuQyx5Q0FBYyxHQUFkLFVBQWUsS0FBMEIsRUFBRSxJQUFZO1FBQ3RELElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUNyQixJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsQ0FBQTtRQUN4QixJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQTtJQUMzQixDQUFDO0lBRUQsNEJBQTRCO0lBQzVCLG9DQUFTLEdBQVQsVUFBVSxLQUEwQixFQUFFLElBQVk7UUFDakQsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQTtJQUM1QixDQUFDO0lBQ0QsTUFBTTtJQUNOLGlIQUFpSDtJQUVqSCxpSEFBaUg7SUFFakgsU0FBUztJQUNLLHlDQUFjLEdBQTVCOzs7Ozs0QkFDdUIscUJBQU0sSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsOEJBQThCLEVBQUUsRUFBRSxFQUFFLElBQUksQ0FBQyxFQUFBOzt3QkFBaEYsS0FBZ0IsU0FBZ0UsRUFBOUUsR0FBRyxTQUFBLEVBQUUsSUFBSSxVQUFBO3dCQUNqQixJQUFJLEdBQUcsRUFBRTs0QkFDUix1QkFBVSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQTt5QkFDekI7NkJBQU0sSUFBSSxJQUFJLEVBQUU7NEJBQ2hCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQTs0QkFDcEIsTUFBTSxHQUFHLE1BQU0sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLElBQUksR0FBRyxNQUFNLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQTs0QkFDcEYsT0FBTzs0QkFDUCxJQUFJLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQyxzQkFBc0IsRUFBRSxlQUFlLEdBQUcsSUFBSSxDQUFDLENBQUE7NEJBQzdFLFFBQVE7NEJBQ1IsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTs0QkFDeEMsT0FBTzs0QkFDUCxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7NEJBQ3ZCLEtBQVMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7Z0NBQ3ZDLE1BQU0sR0FBRyxTQUFTLENBQUMsV0FBVyxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7Z0NBQ2xFLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBOzZCQUN6Qjs0QkFDRCxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7eUJBQ3BCOzs7OztLQUNEO0lBRUQsUUFBUTtJQUNBLDRDQUFpQixHQUF6QixVQUEwQixJQUFZO1FBQXRDLGlCQUdDO1FBRkEsSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsSUFBSSxHQUFHLEtBQUssRUFBRSxjQUFNLE9BQUEsS0FBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsRUFBMUIsQ0FBMEIsQ0FBQyxDQUFBO1FBQ25FLElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFBO0lBQ2hDLENBQUM7SUFFRCxPQUFPO0lBQ0Msd0NBQWEsR0FBckI7UUFBQSxpQkFnQkM7UUFmQSxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssRUFBRSxNQUFNLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEVBQ2pFLE9BQU8sR0FBRyxLQUFLLEdBQUcsSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUE7UUFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsVUFBQyxFQUFFLEVBQUUsSUFBUyxFQUFFLENBQUM7WUFDcEUsRUFBRSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7WUFDZCxFQUFFLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7WUFDakMsSUFBTSxJQUFJLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUM1QixJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxHQUFHLENBQUMsR0FBRyxJQUFJLENBQUE7WUFDdkMsSUFBSSxDQUFDLEtBQUssR0FBRyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFBO1lBQy9CLElBQUksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO1lBQ25CLElBQUksQ0FBQyxNQUFNLEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQTtZQUM1QyxFQUFFLENBQUMsQ0FBQyxHQUFHLE9BQU8sR0FBRyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsS0FBSyxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLENBQUE7WUFDaEQsRUFBRSxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLE1BQU0sR0FBRyxLQUFLLEdBQUcsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxNQUFNLEdBQUcsS0FBSyxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQy9ELHFCQUFTLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7WUFDdkQsS0FBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUE7UUFDcEIsQ0FBQyxDQUFDLENBQUE7SUFDSCxDQUFDO0lBRUQsS0FBSztJQUNHLHVDQUFZLEdBQXBCLFVBQXFCLEVBQVU7UUFDOUIsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUE7UUFDbEIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzNDLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDMUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQTtTQUNoRTtJQUNGLENBQUM7SUFFRCxPQUFPO0lBQ08sMENBQWUsR0FBN0IsVUFBOEIsUUFBaUI7Ozs7Ozs7d0JBQzlDLElBQUksSUFBSSxDQUFDLFFBQVEsS0FBSyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUU7NEJBQ3JDLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLHNCQUFzQixDQUFDLEVBQUE7eUJBQ25EO3dCQUNELElBQUksQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxDQUFBO3dCQUNSLHFCQUFNLElBQUksQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLHlCQUF5QixFQUFFLEVBQUUsTUFBTSxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBQTs7d0JBQWxILEtBQWdCLFNBQWtHLEVBQWhILEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQTt3QkFDakIsSUFBSSxHQUFHLEVBQUU7NEJBQ1IsdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUE7eUJBQ3pCOzZCQUFNLElBQUksSUFBSSxFQUFFOzRCQUNoQixJQUFJLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRSxPQUFPO2dDQUN0Qix1QkFBVSxDQUFDLFNBQVMsQ0FBQyx1QkFBdUIsQ0FBQyxDQUFBO2dDQUM3QyxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7NkJBQ1g7aUNBQU07Z0NBQ04sSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFBO2dDQUNmLElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLEVBQUUsRUFBRSxrQkFBa0I7b0NBQy9DLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtpQ0FDWDtxQ0FBTTtvQ0FDTixJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQTtvQ0FDWCxHQUFHLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsd0JBQXdCLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsNEJBQTRCLENBQUE7b0NBQzNHLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBRTt3Q0FDekIsRUFBRSxFQUFFOzRDQUNILElBQUksSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxVQUFVO2dEQUN2RCxLQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7NkNBQ3JCO2lEQUFNLElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRSxFQUFFLDhCQUE4QjtnREFDNUQsS0FBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTtnREFDeEMsS0FBSSxDQUFDLGFBQWEsRUFBRSxDQUFBOzZDQUNwQjt3Q0FDRixDQUFDO3FDQUNELENBQUMsQ0FBQTtpQ0FDRjs2QkFDRDt5QkFDRDs7Ozs7S0FDRDtJQUVELE9BQU87SUFDQyxtQ0FBUSxHQUFoQjtRQUFBLGlCQVlDO1FBWEEsSUFBSSxjQUFjLEdBQUcsQ0FBQyxDQUFBO1FBQ3RCLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEtBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEVBQXZCLENBQXVCLENBQUMsQ0FBQTtRQUM5RCxJQUFJLE1BQU0sRUFBRTtZQUNYLGNBQWMsR0FBRyxNQUFNLENBQUMsRUFBRSxDQUFBO1lBRTFCLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEVBQUUsS0FBSyxjQUFjLEVBQXZCLENBQXVCLENBQUMsQ0FBQTtZQUNoRSxJQUFNLFNBQVMsR0FBRyxNQUFNLENBQUMsR0FBRyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEVBQUUsRUFBSixDQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQyxJQUFLLE9BQUEsQ0FBQyxHQUFHLENBQUMsRUFBTCxDQUFLLENBQUMsQ0FBQTtZQUU3RCxJQUFNLGFBQWEsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQTtZQUN4RCxtQkFBUSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLGFBQWEsZUFBQSxFQUFFLGNBQWMsRUFBRSxJQUFJLENBQUMsUUFBUSxFQUFFLGNBQWMsZ0JBQUEsRUFBRSxDQUFDLENBQUE7U0FDbEc7SUFDRixDQUFDO0lBRU8sOENBQW1CLEdBQTNCO1FBQUEsaUJBU0M7UUFSQSxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQTtRQUM3QyxJQUFJLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQ3hDLElBQUksQ0FBQyxZQUFZLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFBO1FBQzVELElBQUksQ0FBQyxZQUFZLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLEdBQUcsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFBO1FBQzNFLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDN0UsSUFBSSxJQUFJLENBQUMsZUFBZSxFQUFFO1lBQ3pCLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsZUFBZSxHQUFHLENBQUMsRUFBRSxjQUFNLE9BQUEsS0FBSSxDQUFDLE9BQU8sSUFBSSxLQUFJLENBQUMsbUJBQW1CLEVBQUUsRUFBMUMsQ0FBMEMsQ0FBQyxDQUFBO1NBQzdHO0lBQ0YsQ0FBQztJQUVELGlDQUFNLEdBQU4sVUFBTyxFQUFVO1FBQ2hCLElBQUksSUFBSSxDQUFDLGVBQWUsR0FBRyxDQUFDLEVBQUU7WUFDN0IsSUFBSSxDQUFDLGVBQWUsSUFBSSxFQUFFLENBQUE7U0FDMUI7SUFDRixDQUFDO0lBOUxtQixnQkFBZ0I7UUFEcEMsT0FBTztPQUNhLGdCQUFnQixDQStMcEM7SUFBRCx1QkFBQztDQS9MRCxBQStMQyxDQS9MNkMsRUFBRSxDQUFDLFdBQVcsR0ErTDNEO2tCQS9Mb0IsZ0JBQWdCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvUmVzSGVscGVyXCI7XHJcbmltcG9ydCB7IHRhSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVGFIZWxwZXJcIjtcclxuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcclxuaW1wb3J0IE5ldHdvcmtNb2RlbCBmcm9tIFwiLi4vLi4vbW9kZWwvY29tbW9uL05ldHdvcmtNb2RlbFwiO1xyXG5cclxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQW50aUNoZWF0UG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcclxuXHJcblx0Ly9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cclxuXHRwcml2YXRlIHNlbGVjdE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9zZWxlY3RfblxyXG5cdHByaXZhdGUgaGVhZE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9oZWFkX25cclxuXHRwcml2YXRlIGNvdW50RG93bk5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9jb3VudF9kb3duX25cclxuXHRwcml2YXRlIHJlZnJlc2hOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvYnV0dG9ucy9yZWZyZXNoX2JlX25cclxuXHQvL0BlbmRcclxuXHJcblx0cHJpdmF0ZSBuZXQ6IE5ldHdvcmtNb2RlbCA9IG51bGxcclxuXHJcblx0cHJpdmF0ZSBoZWFkczogY2MuTm9kZVtdID0gW11cclxuXHRwcml2YXRlIG9wdGlvbnM6IGFueVtdID0gW11cclxuXHRwcml2YXRlIHNlbGVjdElkID0gMFxyXG5cdHByaXZhdGUgcmVmcmVzaEludGVydmFsID0gMFxyXG5cdHByaXZhdGUgbGFiZWxUaW1lcjogY2MuTGFiZWxUaW1lciA9IG51bGxcclxuXHJcblx0cHJpdmF0ZSBhbnN3ZXJLZXk6IHN0cmluZyA9ICcnXHJcblxyXG5cdHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XHJcblx0XHRyZXR1cm4gW11cclxuXHR9XHJcblxyXG5cdHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcclxuXHRcdHRoaXMubmV0ID0gdGhpcy5nZXRNb2RlbCgnbmV0JylcclxuXHRcdHRoaXMubGFiZWxUaW1lciA9IHRoaXMuY291bnREb3duTm9kZV8uQ2hpbGQoJ3ZhbCcsIGNjLkxhYmVsVGltZXIpXHJcblx0fVxyXG5cclxuXHRwdWJsaWMgb25FbnRlcigpIHtcclxuXHRcdHRoaXMuc2VsZWN0SWQgPSAwXHJcblx0XHR0aGlzLnJlZnJlc2hJbnRlcnZhbCA9IDBcclxuXHRcdHRoaXMudXBkYXRlVmlld0luZm8oKVxyXG5cdFx0dGhpcy51cGRhdGVSZWZyZXNoQnV0dG9uKClcclxuXHR9XHJcblxyXG5cdHB1YmxpYyBvblJlbW92ZSgpIHtcclxuXHR9XHJcblxyXG5cdHB1YmxpYyBvbkNsZWFuKCkge1xyXG5cdFx0YXNzZXRzTWdyLnJlbGVhc2VUZW1wUmVzQnlUYWcodGhpcy5rZXkpXHJcblx0fVxyXG5cclxuXHQvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cdC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxyXG5cclxuXHQvLyBwYXRoOi8vcm9vdC9oZWFkX24vaGVhZF9iZVxyXG5cdG9uQ2xpY2tIZWFkKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfZGF0YTogc3RyaW5nKSB7XHJcblx0XHRjb25zdCBpZCA9IGV2ZW50LnRhcmdldC5EYXRhPy5pZFxyXG5cdFx0aWYgKGlkKSB7XHJcblx0XHRcdHRoaXMuc2VsZWN0QW5zd2VyKGlkKVxyXG5cdFx0fVxyXG5cdH1cclxuXHJcblx0Ly8gcGF0aDovL3Jvb3QvYnV0dG9ucy9yZWZyZXNoX2JlX25cclxuXHRvbkNsaWNrUmVmcmVzaChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XHJcblx0XHR0aGlzLnVwZGF0ZVZpZXdJbmZvKClcclxuXHRcdHRoaXMucmVmcmVzaEludGVydmFsID0gM1xyXG5cdFx0dGhpcy51cGRhdGVSZWZyZXNoQnV0dG9uKClcclxuXHR9XHJcblxyXG5cdC8vIHBhdGg6Ly9yb290L2J1dHRvbnMvb2tfYmVcclxuXHRvbkNsaWNrT2soZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG5cdFx0dGhpcy5hbnN3ZXJBbnRpQ2hlYXQoZmFsc2UpXHJcblx0fVxyXG5cdC8vQGVuZFxyXG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5cdC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG5cdC8vIOabtOaWsOeVjOmdouS/oeaBr1xyXG5cdHByaXZhdGUgYXN5bmMgdXBkYXRlVmlld0luZm8oKSB7XHJcblx0XHRjb25zdCB7IGVyciwgZGF0YSB9ID0gYXdhaXQgdGhpcy5uZXQucmVxdWVzdCgnZ2FtZS9IRF9HZXRBbnRpQ2hlYXRRdWVzdGlvbicsIHt9LCB0cnVlKVxyXG5cdFx0aWYgKGVycikge1xyXG5cdFx0XHR2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXHJcblx0XHR9IGVsc2UgaWYgKGRhdGEpIHtcclxuXHRcdFx0dGhpcy5hbnN3ZXJLZXkgPSBkYXRhLml0ZW1cclxuXHRcdFx0Y29uc3QgYW5zd2VyID0gTnVtYmVyKGRhdGEuaXRlbS5zcGxpdCgnaXRlbV8nKVsxXSksIHR5cGUgPSBhbnN3ZXIgPT09IDUgPyA2IDogYW5zd2VyXHJcblx0XHRcdC8vIOabtOaWsOetlOahiFxyXG5cdFx0XHR0aGlzLnNlbGVjdE5vZGVfLnNldExvY2FsZUtleSgndWkuYW50aV9jaGVhdF9zZWxlY3QnLCAndWkucGF3bl90eXBlXycgKyB0eXBlKVxyXG5cdFx0XHQvLyDmm7TmlrDlgJLorqHml7ZcclxuXHRcdFx0dGhpcy51cGRhdGVTdXJwbHVzVGltZShkYXRhLnN1cnBsdXNUaW1lKVxyXG5cdFx0XHQvLyDmm7TmlrDpgInpoblcclxuXHRcdFx0dGhpcy5vcHRpb25zLmxlbmd0aCA9IDBcclxuXHRcdFx0Zm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLm9wdGlvbnMubGVuZ3RoOyBpKyspIHtcclxuXHRcdFx0XHRjb25zdCBvcHRpb24gPSBhc3NldHNNZ3IuZ2V0SnNvbkRhdGEoJ2FudGlDaGVhdCcsIGRhdGEub3B0aW9uc1tpXSlcclxuXHRcdFx0XHR0aGlzLm9wdGlvbnMucHVzaChvcHRpb24pXHJcblx0XHRcdH1cclxuXHRcdFx0dGhpcy51cGRhdGVPcHRpb25zKClcclxuXHRcdH1cclxuXHR9XHJcblxyXG5cdC8vIOabtOaWsOWAkuiuoeaXtlxyXG5cdHByaXZhdGUgdXBkYXRlU3VycGx1c1RpbWUodGltZTogbnVtYmVyKSB7XHJcblx0XHR0aGlzLmxhYmVsVGltZXIucnVuKHRpbWUgKiAwLjAwMSwgKCkgPT4gdGhpcy5hbnN3ZXJBbnRpQ2hlYXQodHJ1ZSkpXHJcblx0XHR0aGlzLmxhYmVsVGltZXIuc2V0UGF1c2UoZmFsc2UpXHJcblx0fVxyXG5cclxuXHQvLyDmiZPkubHpgInpoblcclxuXHRwcml2YXRlIHVwZGF0ZU9wdGlvbnMoKSB7XHJcblx0XHRjb25zdCB3aWR0aCA9IHRoaXMuaGVhZE5vZGVfLndpZHRoLCBoZWlnaHQgPSB0aGlzLmhlYWROb2RlXy5oZWlnaHQsXHJcblx0XHRcdG9mZnNldFggPSB3aWR0aCAvIHRoaXMub3B0aW9ucy5sZW5ndGhcclxuXHRcdHRoaXMuaGVhZE5vZGVfLkl0ZW1zKHV0LnNodWZmbGVBcnJheSh0aGlzLm9wdGlvbnMpLCAoaXQsIGRhdGE6IGFueSwgaSkgPT4ge1xyXG5cdFx0XHRpdC5EYXRhID0gZGF0YVxyXG5cdFx0XHRpdC5DaGlsZCgnc2VsZWN0JykuYWN0aXZlID0gZmFsc2VcclxuXHRcdFx0Y29uc3QgaWNvbiA9IGl0LkNoaWxkKCd2YWwnKVxyXG5cdFx0XHRjb25zdCBzY2FsZSA9IHV0LnJhbmRvbSg4NSwgMTE1KSAqIDAuMDFcclxuXHRcdFx0aWNvbi5hbmdsZSA9IHV0LnJhbmRvbSgtMzAsIDMwKVxyXG5cdFx0XHRpY29uLnNjYWxlWSA9IHNjYWxlXHJcblx0XHRcdGljb24uc2NhbGVYID0gdXQuY2hhbmNlKDUwKSA/IC1zY2FsZSA6IHNjYWxlXHJcblx0XHRcdGl0LnggPSBvZmZzZXRYICogaSArIChpdC53aWR0aCAqIHNjYWxlKSAvIDIgKyAxMFxyXG5cdFx0XHRpdC55ID0gdXQucmFuZG9tKC1pdC5oZWlnaHQgKiBzY2FsZSAvIDMsIGl0LmhlaWdodCAqIHNjYWxlIC8gMylcclxuXHRcdFx0cmVzSGVscGVyLmxvYWRJY29uKCdyb2xlLycgKyBkYXRhLmljb24sIGljb24sIHRoaXMua2V5KVxyXG5cdFx0XHR0aGlzLmhlYWRzLnB1c2goaXQpXHJcblx0XHR9KVxyXG5cdH1cclxuXHJcblx0Ly8g6YCJ5oupXHJcblx0cHJpdmF0ZSBzZWxlY3RBbnN3ZXIoaWQ6IG51bWJlcikge1xyXG5cdFx0dGhpcy5zZWxlY3RJZCA9IGlkXHJcblx0XHRmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuaGVhZHMubGVuZ3RoOyBpKyspIHtcclxuXHRcdFx0Y29uc3QgaGVhZCA9IHRoaXMuaGVhZHNbaV1cclxuXHRcdFx0aGVhZC5DaGlsZCgnc2VsZWN0JykuYWN0aXZlID0gaGVhZC5EYXRhLmlkID09PSBpZCA/IHRydWUgOiBmYWxzZVxyXG5cdFx0fVxyXG5cdH1cclxuXHJcblx0Ly8g5o+Q5Lqk562U5qGIXHJcblx0cHJpdmF0ZSBhc3luYyBhbnN3ZXJBbnRpQ2hlYXQodGltZU92ZXI6IGJvb2xlYW4pIHtcclxuXHRcdGlmICh0aGlzLnNlbGVjdElkID09PSAwICYmICF0aW1lT3Zlcikge1xyXG5cdFx0XHRyZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LnNlbGVjdF9vbmVfdGlwJylcclxuXHRcdH1cclxuXHRcdHRoaXMubGFiZWxUaW1lci5zZXRQYXVzZSh0cnVlKVxyXG5cdFx0Y29uc3QgeyBlcnIsIGRhdGEgfSA9IGF3YWl0IHRoaXMubmV0LnJlcXVlc3QoJ2dhbWUvSERfQW50aUNoZWF0QW5zd2VyJywgeyBhbnN3ZXI6IHRpbWVPdmVyID8gLTEgOiB0aGlzLnNlbGVjdElkIH0sIHRydWUpXHJcblx0XHRpZiAoZXJyKSB7XHJcblx0XHRcdHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVycilcclxuXHRcdH0gZWxzZSBpZiAoZGF0YSkge1xyXG5cdFx0XHRpZiAoZGF0YS5yc3QpIHsgLy8g6YCa6L+H5qOA5rWLXHJcblx0XHRcdFx0dmlld0hlbHBlci5zaG93QWxlcnQoJ3RvYXN0LmFudGlfY2hlYXRfc3VjYycpXHJcblx0XHRcdFx0dGhpcy5oaWRlKClcclxuXHRcdFx0fSBlbHNlIHtcclxuXHRcdFx0XHR0aGlzLnRhUmVwb3J0KClcclxuXHRcdFx0XHRpZiAoZGF0YS5ub3RQYXNzQ291bnQgPj0gMykgeyAvLyDmnKrpgJrov4cz5qyh77yM6Lii5LiL57q/77yM6LWw55m75b2V5bCB56aBXHJcblx0XHRcdFx0XHR0aGlzLmhpZGUoKVxyXG5cdFx0XHRcdH0gZWxzZSB7XHJcblx0XHRcdFx0XHR0aGlzLnNlbGVjdElkID0gMFxyXG5cdFx0XHRcdFx0Y29uc3Qga2V5ID0gZGF0YS5ub3RQYXNzQ291bnQgPyAndG9hc3QuYW50aV9jaGVhdF9mYWlsXycgKyBkYXRhLm5vdFBhc3NDb3VudCA6ICd0b2FzdC5hbnRpX2NoZWF0X3dlYWtfZmFpbCdcclxuXHRcdFx0XHRcdHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGtleSwge1xyXG5cdFx0XHRcdFx0XHRjYjogKCkgPT4ge1xyXG5cdFx0XHRcdFx0XHRcdGlmIChkYXRhLm5vdFBhc3NDb3VudCB8fCAhZGF0YS5zdXJwbHVzVGltZSkgeyAvLyDor7fmsYLmlrDnmoQgICBcclxuXHRcdFx0XHRcdFx0XHRcdHRoaXMudXBkYXRlVmlld0luZm8oKVxyXG5cdFx0XHRcdFx0XHRcdH0gZWxzZSBpZiAoZGF0YS5zdXJwbHVzVGltZSkgeyAvLyDmiZPkubHml6fnmoTvvIzov5jmnInliankvZnml7bpl7TmiY3miZPkubHvvIzmsqHmnInliankvZnml7bpl7Tnm7TmjqXor7fmsYLmlrDnmoRcclxuXHRcdFx0XHRcdFx0XHRcdHRoaXMudXBkYXRlU3VycGx1c1RpbWUoZGF0YS5zdXJwbHVzVGltZSlcclxuXHRcdFx0XHRcdFx0XHRcdHRoaXMudXBkYXRlT3B0aW9ucygpXHJcblx0XHRcdFx0XHRcdFx0fVxyXG5cdFx0XHRcdFx0XHR9XHJcblx0XHRcdFx0XHR9KVxyXG5cdFx0XHRcdH1cclxuXHRcdFx0fVxyXG5cdFx0fVxyXG5cdH1cclxuXHJcblx0Ly8gdGHkuIrmiqVcclxuXHRwcml2YXRlIHRhUmVwb3J0KCkge1xyXG5cdFx0bGV0IGNvcnJfYW5zd2VyX2lkID0gMFxyXG5cdFx0Y29uc3QgdGFyZ2V0ID0gdGhpcy5vcHRpb25zLmZpbmQobSA9PiBtW3RoaXMuYW5zd2VyS2V5XSA9PT0gMSlcclxuXHRcdGlmICh0YXJnZXQpIHtcclxuXHRcdFx0Y29ycl9hbnN3ZXJfaWQgPSB0YXJnZXQuaWRcclxuXHJcblx0XHRcdGNvbnN0IG90aGVycyA9IHRoaXMub3B0aW9ucy5maWx0ZXIobSA9PiBtLmlkICE9PSBjb3JyX2Fuc3dlcl9pZClcclxuXHRcdFx0Y29uc3QgcmVtYWluaW5nID0gb3RoZXJzLm1hcChtID0+IG0uaWQpLnNvcnQoKGEsIGIpID0+IGEgLSBiKVxyXG5cclxuXHRcdFx0Y29uc3QgYWxsX2Fuc3dlcklkcyA9IFtjb3JyX2Fuc3dlcl9pZF0uY29uY2F0KHJlbWFpbmluZylcclxuXHRcdFx0dGFIZWxwZXIudHJhY2soJ3RhX2NoZWF0X3dyb25nJywgeyBhbGxfYW5zd2VySWRzLCBzZWxlX2Fuc3dlcl9pZDogdGhpcy5zZWxlY3RJZCwgY29ycl9hbnN3ZXJfaWQgfSlcclxuXHRcdH1cclxuXHR9XHJcblxyXG5cdHByaXZhdGUgdXBkYXRlUmVmcmVzaEJ1dHRvbigpIHtcclxuXHRcdGNvbnN0IHRpbWUgPSBNYXRoLmZsb29yKHRoaXMucmVmcmVzaEludGVydmFsKVxyXG5cdFx0dGhpcy5yZWZyZXNoSW50ZXJ2YWwgPSBNYXRoLm1heCgwLCB0aW1lKVxyXG5cdFx0dGhpcy5yZWZyZXNoTm9kZV8ub3BhY2l0eSA9IHRoaXMucmVmcmVzaEludGVydmFsID8gMTUwIDogMjU1XHJcblx0XHR0aGlzLnJlZnJlc2hOb2RlXy5Db21wb25lbnQoY2MuQnV0dG9uKS5pbnRlcmFjdGFibGUgPSAhdGhpcy5yZWZyZXNoSW50ZXJ2YWxcclxuXHRcdGNvbnN0IG5vZGUgPSB0aGlzLnJlZnJlc2hOb2RlXy5Td2loKHRoaXMucmVmcmVzaEludGVydmFsID8gJ3RpbWUnIDogJ3ZhbCcpWzBdXHJcblx0XHRpZiAodGhpcy5yZWZyZXNoSW50ZXJ2YWwpIHtcclxuXHRcdFx0bm9kZS5Db21wb25lbnQoY2MuTGFiZWxUaW1lcikucnVuKHRoaXMucmVmcmVzaEludGVydmFsICsgMSwgKCkgPT4gdGhpcy5pc1ZhbGlkICYmIHRoaXMudXBkYXRlUmVmcmVzaEJ1dHRvbigpKVxyXG5cdFx0fVxyXG5cdH1cclxuXHJcblx0dXBkYXRlKGR0OiBudW1iZXIpIHtcclxuXHRcdGlmICh0aGlzLnJlZnJlc2hJbnRlcnZhbCA+IDApIHtcclxuXHRcdFx0dGhpcy5yZWZyZXNoSW50ZXJ2YWwgLT0gZHRcclxuXHRcdH1cclxuXHR9XHJcbn1cclxuIl19