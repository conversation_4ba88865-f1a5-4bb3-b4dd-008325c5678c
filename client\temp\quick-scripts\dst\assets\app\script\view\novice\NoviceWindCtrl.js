
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/novice/NoviceWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e47266Z3AtAvZr7wR2VbzXY', 'NoviceWindCtrl');
// app/script/view/novice/NoviceWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var MapUionFindHelper_1 = require("../../common/helper/MapUionFindHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var GuideConfig_1 = require("../../model/guide/GuideConfig");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var CellInfoCmpt_1 = require("../main/CellInfoCmpt");
var MapAnimNodePool_1 = require("../main/MapAnimNodePool");
var MarchCmpt_1 = require("../main/MarchCmpt");
var SceneEffectCmpt_1 = require("../main/SceneEffectCmpt");
var ccclass = cc._decorator.ccclass;
var NoviceWindCtrl = /** @class */ (function (_super) {
    __extends(NoviceWindCtrl, _super);
    function NoviceWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.cellEffectNode_ = null; // path://root/map_n/cell_effect_n
        _this.cellEmojiNode_ = null; // path://root/map_n/cell_emoji_n
        _this.selectCellNode_ = null; // path://root/select_cell_n
        _this.textNode_ = null; // path://root/text_n
        _this.marchLineNode_ = null; // path://root/march/march_line_n
        _this.marchRoleNode_ = null; // path://root/march/march_role_n
        _this.sceneEffectNode_ = null; // path://root/scene_effect_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.INIT_KEY = '_init_novice_';
        _this.diNode = null; //装饰-地块下面
        _this.decorationNode = null; //装饰-地块上面
        _this.mountainNode = null; //山脉
        _this.seawaveNode = null; //海浪
        _this.lineNode = null;
        _this.landNode = null;
        _this.cityNode = null; //城市层
        _this.maskNode = null; //遮罩层
        _this.btinfoNode = null; //修建信息层
        _this.iconNode = null; //小图标层
        _this.battleNode = null; //战斗中图标层
        _this.cellInfoCmpt = null;
        _this.touchCmpt = null;
        _this.sceneEffect = null;
        _this.model = null;
        _this.player = null;
        _this.centre = cc.v2(); //当前的中心位置
        _this.preCameraZoomRatio = 0;
        _this.preCameraPosition = cc.v2();
        _this.marchs = []; //当前所有行军
        _this.tempShowCellMap = {}; //当前屏幕显示的地块信息
        _this.reqSelectArmysing = false; //当前是否请求军队列表中
        _this.cityAnimNodePool = null; //城市节点管理
        _this.seawaveAnimNodePool = null; //海浪节点管理
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        _this._temp_city = {};
        return _this;
    }
    NoviceWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_CELL_INFO] = this.onUpdateCellInfo, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.ADD_MARCH] = this.onAddMarch, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.REMOVE_MARCH] = this.onRemoveMarch, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_ALL_MARCH] = this.onUpdateAllMarch, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CLOSE_SELECT_CELL] = this.onCloseSelectCell, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_BATTLE_DIST_INFO] = this.onUpdateBattleDistInfo, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO] = this.onUpdateAvoidWarDistInfo, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_BT_CITY] = this.onUpdateBtCity, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmyDistInfo, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.MAP_MOVE_TO] = this.onMapMoveTo, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_PLAYER_NICKNAME] = this.onUpdatePlayerNickname, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_PLAYER_HEAD_ICON] = this.onUpdatePlayerHeadIcon, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_MARCH_OPACITY] = this.onUpdateMarchOpacity, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.PLAY_NEW_CELL_EFFECT] = this.onPlayNewCellEffect, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.GUIDE_PLAY_RESTORE_MAINCITY] = this.onGuidePlayRestoreMaincity, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.GUIDE_TAG_CLEAN_NOVICE_WIND] = this.onGuideTagCleanNoviceWind, _t),
        ];
    };
    NoviceWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var sceneEffectUrl, curType;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setParam({ isClean: false });
                        this.decorationNode = this.mapNode_.FindChild('decoration');
                        this.diNode = this.mapNode_.FindChild('di');
                        this.mountainNode = this.mapNode_.FindChild('mountain');
                        this.seawaveNode = this.mapNode_.FindChild('seawave');
                        this.lineNode = this.mapNode_.FindChild('line');
                        this.landNode = this.mapNode_.FindChild('land');
                        this.cityNode = this.mapNode_.FindChild('city');
                        this.maskNode = this.mapNode_.FindChild('mask');
                        this.btinfoNode = this.mapNode_.FindChild('btinfo');
                        this.iconNode = this.mapNode_.FindChild('icon');
                        this.battleNode = this.mapNode_.FindChild('battle');
                        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                        this.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt_1.default);
                        this.cityAnimNodePool = new MapAnimNodePool_1.default().init(this.cityNode, ResHelper_1.resHelper.getCityPrefab.bind(ResHelper_1.resHelper));
                        this.seawaveAnimNodePool = new MapAnimNodePool_1.default().init(this.seawaveNode, ResHelper_1.resHelper.getSeawavePrefab.bind(ResHelper_1.resHelper));
                        this.seawaveAnimNodePool.setAnimInfo('land_104', 1.76);
                        this.model = this.getModel('novice');
                        this.player = this.getModel('player');
                        this.selectCellNode_.active = false;
                        this.cellInfoCmpt.close();
                        this.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt_1.default);
                        sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl();
                        if (!sceneEffectUrl) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        this.sceneEffect.clean();
                        _a.label = 3;
                    case 3: return [4 /*yield*/, ResHelper_1.resHelper.initNovice(this.INIT_KEY)
                        // 检测季节
                    ];
                    case 4:
                        _a.sent();
                        curType = this.model.getSeasonType();
                        if (!!ResHelper_1.resHelper.checkLandSkin(curType)) return [3 /*break*/, 6];
                        return [4 /*yield*/, ResHelper_1.resHelper.initLandSkin(curType)];
                    case 5:
                        _a.sent();
                        _a.label = 6;
                    case 6:
                        this.maskNode.children[0].color = cc.Color.WHITE.fromHEX(Constant_1.MAP_MASK_ITEM_COLOR[this.model.getSeasonType()]);
                        this.maskNode.children[0].opacity = Constant_1.MAP_MASK_ITEM_OPACITY[this.model.getSeasonType()];
                        return [2 /*return*/];
                }
            });
        });
    };
    NoviceWindCtrl.prototype.onEnter = function (data) {
        this.model.initCameraInfo();
        this.cellEffectNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.updateMap(this.model.getCentre()); //刷新地图显示
        this.initMarch(); //初始化行军
        this.playNewCellEffect();
        this.touchCmpt.init(this.onClickMap.bind(this));
        GameHelper_1.gameHpr.playMainBgm();
        CameraCtrl_1.cameraCtrl.setBgColor(Constant_1.CAMERA_BG_COLOR[this.model.getSeasonType()]);
        GameHelper_1.gameHpr.novice.isCanAttackPlayer = true;
    };
    NoviceWindCtrl.prototype.onLeave = function () {
        this.model.saveCameraInfo();
        this.touchCmpt.clean();
        this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
        this.cellInfoCmpt.close();
        this.reqSelectArmysing = false;
        this.cleanMarch();
        this.cellEffectNode_.removeAllChildren();
        this.cellEffectNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        assetsMgr.releaseTempResByTag(this.key);
        CameraCtrl_1.cameraCtrl.setBgColor('#D1F1F3');
        GameHelper_1.gameHpr.novice.isCanAttackPlayer = false;
    };
    NoviceWindCtrl.prototype.onClean = function () {
        var _a;
        this.cellInfoCmpt.clean();
        (_a = this.sceneEffect) === null || _a === void 0 ? void 0 : _a.clean();
        GameHelper_1.gameHpr.setNoLongerTip('first_guide_task', true);
        ResHelper_1.resHelper.cleanNovice();
        AnimHelper_1.animHelper.cleanAllFinger();
        nodePoolMgr.releaseByTag(this.INIT_KEY);
        assetsMgr.releaseTempResByTag(this.INIT_KEY);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/cell_info/buttons/enter_be
    NoviceWindCtrl.prototype.onClickEnter = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            // if (cell.cityId === CITY_MAIN_NID) {
            // 	let val = gameHpr.guide.equalGuideTag(3, GuideTagType.FREE_BATTLE_GUIDE_CELL_LV2)
            // 	if (val !== -1) {
            // 		return viewHelper.showAlert('toast.finish_task_first')
            // 	}
            // }
            this.model.setLookCell(cell);
            ViewHelper_1.viewHelper.gotoWind('area');
            this.hideSelectCell(false);
        }
    };
    // path://root/cell_info/buttons/player_info_be
    NoviceWindCtrl.prototype.onClickPlayerInfo = function (event, data) {
    };
    // path://root/cell_info/buttons/build_be
    NoviceWindCtrl.prototype.onClickBuild = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/CityList', cell);
    };
    // path://root/cell_info/buttons/dismantle_be
    NoviceWindCtrl.prototype.onClickDismantle = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOwn()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('main/DismantleCityTip', cell);
    };
    // path://root/cell_info/buttons/occupy_be
    NoviceWindCtrl.prototype.onClickOccupy = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell) {
            return;
        }
        else if (!this.model.checkCanOccupyCell(cell)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_ATTACK_ADJOIN_CELL);
        }
        else if (cell.isAvoidWar()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.AVOID_WAR_NOT_ATTACK);
        }
        this.occupyCell(cell.actIndex);
    };
    // path://root/cell_info/buttons/move_be
    NoviceWindCtrl.prototype.onClickMove = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (!cell || !cell.isOneAlliance()) {
            return;
        }
        this.moveToCell(cell.actIndex);
    };
    // path://root/cell_info/info/stamina/stamina_desc_be
    NoviceWindCtrl.prototype.onClickStaminaDesc = function (event, data) {
        var cell = this.cellInfoCmpt.getCell();
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    NoviceWindCtrl.prototype.onNetReconnect = function () {
        if (!GameHelper_1.gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close();
        }
        this.updateMap(this.model.getCentre()); //刷新地图显示
        this.initMarch(); //初始化行军
    };
    // 更新地块信息
    NoviceWindCtrl.prototype.onUpdateCellInfo = function () {
        this.updateMap(this.centre);
    };
    // 添加行军
    NoviceWindCtrl.prototype.onAddMarch = function (data) {
        var _this = this;
        if (!data.isCanShowMarch()) {
            return this.onRemoveMarch(data);
        }
        var march = this.marchs.find(function (m) { return m.uid === data.uid; });
        if (march) {
            march.init(data, this.marchRoleNode_, this.key);
            this.checkMarchLineOffset(data);
        }
        else {
            this.marchLineNode_.AddItem(function (it) {
                march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
                _this.checkMarchLineOffset(data);
            });
        }
    };
    // 删除行军
    NoviceWindCtrl.prototype.onRemoveMarch = function (data) {
        var march = this.marchs.remove('uid', data.uid);
        if (march) {
            march.clean();
            this.checkMarchLineOffset(data);
        }
    };
    // 刷新所有行军
    NoviceWindCtrl.prototype.onUpdateAllMarch = function () {
        this.initMarch();
    };
    // 关闭选择地块
    NoviceWindCtrl.prototype.onCloseSelectCell = function (play) {
        this.hideSelectCell(!!play);
    };
    // 刷新战斗状态
    NoviceWindCtrl.prototype.onUpdateBattleDistInfo = function () {
        var cells = [], distMap = this.model.getBattleDistMap();
        for (var index in distMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push(cell);
        }
        this.battleNode.Items(cells, function (it, data) { return it.setPosition(data.actPosition); });
    };
    // 刷新免战状态
    NoviceWindCtrl.prototype.onUpdateAvoidWarDistInfo = function () {
        this.updateIconNode();
    };
    // 刷新修建信息
    NoviceWindCtrl.prototype.onUpdateBtCity = function (index) {
        var _a;
        this.updateMap(this.centre);
        if (((_a = this.cellInfoCmpt.getCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === index) {
            this.cellInfoCmpt.updateInfo();
        }
    };
    // 刷新地图上面的军队分布情况  这里主动绘制一次
    NoviceWindCtrl.prototype.onUpdateArmyDistInfo = function () {
        this.updateIconNode();
        this.cellInfoCmpt.updateArmyInfo();
    };
    // 移动地图
    NoviceWindCtrl.prototype.onMapMoveTo = function (point, showCellInfo) {
        var _this = this;
        if (this.centre.equals(point)) {
            return showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point));
        }
        else if (!this.tempShowCellMap[MapHelper_1.mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点
            var start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point);
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(start), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, CameraCtrl_1.cameraCtrl.zoomRatio);
            this.updateMap(start.floor());
            this.checkInCameraMarchLine();
        }
        // 移动
        CameraCtrl_1.cameraCtrl.moveTo(0.25, MapHelper_1.mapHelper.getPixelByPoint(point).subSelf(CameraCtrl_1.cameraCtrl.getWinSizeHalf())).then(function () {
            if (_this.isActive && showCellInfo) {
                _this.showSelectCell(_this.model.getMapCellByPoint(point));
            }
        });
    };
    // 刷新玩家昵称
    NoviceWindCtrl.prototype.onUpdatePlayerNickname = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            this.updatePlayerNickname(it, data);
        }
    };
    // 刷新玩家头像
    NoviceWindCtrl.prototype.onUpdatePlayerHeadIcon = function (data) {
        var it = this.textNode_.children.find(function (m) { var _a; return m.active && ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.owner) === data.uid; });
        if (it) {
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY);
        }
    };
    // 刷新行军线透明度
    NoviceWindCtrl.prototype.onUpdateMarchOpacity = function () {
        this.marchs.forEach(function (m) { return m.updateOpacity(); });
    };
    // 播放新的地块效果
    NoviceWindCtrl.prototype.onPlayNewCellEffect = function () {
        this.playNewCellEffect();
    };
    // 若引导
    NoviceWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'novice') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    // 播放修复主城
    NoviceWindCtrl.prototype.onGuidePlayRestoreMaincity = function () {
        var _this = this;
        var index = this.player.getMainCityIndex();
        var cell = this.tempShowCellMap[index] || GameHelper_1.gameHpr.world.getMapCellByIndex(index);
        AnimHelper_1.animHelper.playRestoreMaincity(this.cellEffectNode_, cell.actPosition, this.key, function () {
            if (_this.isActive) {
                _this.cityNode.children.find(function (m) { return m.active = false; });
            }
        }).then(function () {
            if (_this.isActive) {
                _this.updateMap(_this.centre, true);
            }
        });
    };
    NoviceWindCtrl.prototype.onGuideTagCleanNoviceWind = function () {
        this.setParam({ isClean: true });
    };
    Object.defineProperty(NoviceWindCtrl.prototype, "isActive", {
        // ----------------------------------------- custom function ----------------------------------------------------
        get: function () { return this.isValid && this.isEnter(); },
        enumerable: false,
        configurable: true
    });
    // 点击地图
    NoviceWindCtrl.prototype.onClickMap = function (worldLocation) {
        var _a;
        var cell = this.model.getMapCellByPoint(MapHelper_1.mapHelper.getPointByPixel(worldLocation));
        if (((_a = cell === null || cell === void 0 ? void 0 : cell.city) === null || _a === void 0 ? void 0 : _a.id) === Constant_1.CITY_MAIN_NID && GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.FIRST_GUIDE_BEGIN)) {
            audioMgr.playSFX('click');
            this.emit(EventType_1.default.GUIDE_CKICK_BUILD_MAIN_CITY);
        }
        else if (cell && !this.selectCellNode_.Data) {
            audioMgr.playSFX('click');
            this.showSelectCell(cell);
            CameraCtrl_1.cameraCtrl.redressPositionByRange(cell.actPosition, Constant_1.SELECT_CELL_INFO_BOX);
        }
        else {
            this.hideSelectCell();
        }
    };
    // 绘制地图
    NoviceWindCtrl.prototype.updateMap = function (centre, isOneShowHead) {
        var _this = this;
        var _a, _b, _c, _d;
        this.preCameraZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.centre.set(centre);
        this.model.setCentre(centre);
        // 绘制地面
        var isRestoreMainCity = GameHelper_1.gameHpr.guide.isRestoreMainCity();
        var armyDistMap = isRestoreMainCity ? this.player.getArmyDistMap() : {}, battleDist = this.model.getBattleDistMap(), avoidWarDist = this.model.getAvoidWarDistMap();
        var btCityMap = this.model.getBTCityQueueMap();
        var li = 0, mi = 0, ii = 0, bi = 0, linei = 0, di = 0, mti = 0, dti = 0, texts = [], btCitys = [];
        this.seawaveAnimNodePool.reset();
        this.cityAnimNodePool.reset();
        this.tempShowCellMap = {};
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange());
        var areaSize = MapHelper_1.mapHelper.MAP_SIZE;
        var cityTmp = this._temp_city;
        this._temp_city = cc.js.createMap();
        var tempDecorationLoadMap = {};
        for (var i = 0; i < points.length; i++) {
            var point = points[i];
            var cell = this.model.getMapCellByPoint(point);
            var position = (cell === null || cell === void 0 ? void 0 : cell.position) || MapHelper_1.mapHelper.getPixelByPoint(point);
            if (cell) {
                var btInfo = btCityMap[cell.index];
                this.tempShowCellMap[cell.index] = cell;
                if (cell.cityId > 0) {
                    var cityId = cell.cityId;
                    if (cell.cityId === Constant_1.CITY_MAIN_NID && !isRestoreMainCity) {
                        cityId = Constant_1.CITY_MAIN_NID * 10;
                    }
                    var animName = cell.cityId === Constant_1.CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined;
                    this.cityAnimNodePool.showNode(cityId, cell.actPosition, true, animName);
                    if (cell.cityId === Constant_1.CITY_MAIN_NID) {
                        texts.push(cell); //这里先获取后面用来显示文本
                    }
                    this._temp_city[cell.index] = cell.cityId;
                }
                else if (cell.cityId === 0 && cell.icon && (!btInfo || btInfo.id === 0)) {
                    if (cell.landType === Enums_1.LandType.SEA || cell.landType === Enums_1.LandType.BEACH || MapUionFindHelper_1.mapUionFindHelper.getIsUnLock(cell.index)) {
                        var itemNdoe = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, cell.position);
                        itemNdoe.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType());
                        itemNdoe.zIndex = 0;
                    }
                    else {
                        ResHelper_1.resHelper.getNodeByIndex(this.landNode, li++, cell.position).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType());
                    }
                }
                // 要塞摧毁动画
                if (cell.cityId === 0 && cityTmp[cell.index] === Constant_1.CITY_FORT_NID) {
                    AnimHelper_1.animHelper.playFortDestroyEffect(this.cellEffectNode_, cell.position);
                }
                if (btInfo) { //绘制修建信息
                    btCitys.push({ btInfo: btInfo, cell: cell });
                    // 只绘制修建 不绘制拆除
                    if (cell.cityId === 0 && btInfo.id > 0) {
                        this.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false);
                    }
                }
                if (!!armyDistMap[cell.index]) { //绘制地图军队分布图标
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_a = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _a === void 0 ? void 0 : _a.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position); //显示到左下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('army_min_icon');
                }
                if (!!avoidWarDist[cell.index]) { //绘制免战图标
                    // 下面是否主城
                    var y = (!cell.isMainCity() && ((_b = this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))) === null || _b === void 0 ? void 0 : _b.isMainCity())) ? -6 : -22;
                    var pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)); //显示到右下角
                    ResHelper_1.resHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('avoidwar_min_icon');
                }
                if (!!battleDist[cell.index]) { //绘制战斗图标
                    ResHelper_1.resHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition);
                }
                // 绘制边框线
                if (isRestoreMainCity) {
                    var borderLines = cell.owner ? cell.borderLines : [];
                    if (borderLines.length > 0) {
                        var lineItemNode = ResHelper_1.resHelper.getNodeByIndex(this.lineNode, linei++, position);
                        ViewHelper_1.viewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor());
                    }
                }
                // 绘制遮罩
                if (!cell.owner && cell.landType !== Enums_1.LandType.SEA) {
                    ResHelper_1.resHelper.getNodeByIndex(this.maskNode, mi++, position);
                }
                //绘制地图装饰
                var decorationList = this.model.getDecorationByIndex(cell.index);
                if (decorationList) {
                    for (var i_1 = 0; i_1 < decorationList.length; i_1++) {
                        var _e = decorationList[i_1], decorationJson = _e.decorationJson, decorationActIndex = _e.decorationActIndex;
                        var tempKey = decorationActIndex + '_' + decorationJson.id;
                        if (!tempDecorationLoadMap[tempKey]) {
                            tempDecorationLoadMap[tempKey] = true;
                            var iconName = this.model.getDecorationIcon(decorationJson, decorationActIndex);
                            var frame = ResHelper_1.resHelper.getLandItemIcon(iconName, this.model.getSeasonType());
                            var actPosition = this.model.getMapCells()[decorationActIndex].position;
                            var itemNode = null;
                            if (decorationJson.type == Enums_1.DecorationType.Shadow || decorationJson.type == Enums_1.DecorationType.MUD || decorationJson.type == Enums_1.DecorationType.MUD_OUTER) {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.diNode, di++, actPosition);
                            }
                            else if (MapUionFindHelper_1.mapUionFindHelper.getIsUnLock(decorationActIndex)) {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, actPosition);
                            }
                            else {
                                itemNode = ResHelper_1.resHelper.getNodeByIndex(this.decorationNode, dti++, actPosition);
                            }
                            var zIndex = 1;
                            switch (decorationJson.type) {
                                case Enums_1.DecorationType.MOUNTAIN:
                                case Enums_1.DecorationType.MAIN_CITY_ROUND_MOUNTAIN:
                                case Enums_1.DecorationType.MAIN_CITY_ROUND_LAKE:
                                    zIndex = cc.macro.MAX_ZINDEX;
                                    break;
                                case Enums_1.DecorationType.Shadow:
                                    zIndex = 0;
                                    break;
                            }
                            itemNode.zIndex = zIndex;
                            itemNode.Component(cc.Sprite).spriteFrame = frame;
                            this.resetDecorationPoint(itemNode, iconName);
                        }
                    }
                }
            }
            else {
                var landId = this.model.getRoundId(point.x, point.y);
                if (landId) {
                    var itemInfo = assetsMgr.getJsonData('land', landId);
                    var itemNode = ResHelper_1.resHelper.getNodeByIndex(this.mountainNode, mti++, position);
                    itemNode.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandItemIcon(itemInfo.icon, this.model.getSeasonType());
                    itemNode.zIndex = 0;
                }
            }
        }
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.decorationNode, dti);
        ResHelper_1.resHelper.hideNodeByIndex(this.diNode, di);
        ResHelper_1.resHelper.hideNodeByIndex(this.landNode, li);
        ResHelper_1.resHelper.hideNodeByIndex(this.lineNode, linei);
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
        ResHelper_1.resHelper.hideNodeByIndex(this.iconNode, ii);
        ResHelper_1.resHelper.hideNodeByIndex(this.battleNode, bi);
        ResHelper_1.resHelper.hideNodeByIndex(this.mountainNode, mti);
        this.seawaveAnimNodePool.hideOtherNode();
        this.cityAnimNodePool.hideOtherNode();
        // 当前正在显示的
        var showIndex = (_d = (_c = this.cellInfoCmpt.getCell()) === null || _c === void 0 ? void 0 : _c.actIndex) !== null && _d !== void 0 ? _d : -1;
        if (showIndex === -1 && !isRestoreMainCity) {
            showIndex = this.player.getMainCityIndex();
        }
        // 绘制文本层
        this.textNode_.Items(texts, function (it, data) {
            var _a;
            var pos = data.actPosition;
            it.setPosition(pos.x, pos.y + 76);
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.owner) !== data.owner) {
                var info = _this.model.getPlayerInfo(data.owner);
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head', cc.Sprite), info === null || info === void 0 ? void 0 : info.headIcon, _this.INIT_KEY);
                _this.updatePlayerNickname(it, info);
            }
            if (isOneShowHead) {
                it.opacity = 0;
                cc.tween(it).to(0.3, { opacity: 255 }).start();
            }
            else {
                it.opacity = 255;
            }
            it.active = showIndex !== data.actIndex;
            it.Data = { index: data.actIndex, owner: data.owner };
        });
        // 绘制修建城市的信息
        this.btinfoNode.Items(btCitys, function (it, data) {
            var _a;
            var info = data.btInfo;
            it.setPosition(data.cell.actPosition);
            var timeNode = it.Child('time');
            if (((_a = it.Data) === null || _a === void 0 ? void 0 : _a.index) !== info.index) {
                var surplusTime = info.getSurplusTime();
                timeNode.Color(info.id ? '#21DC2D' : '#FF9162');
                timeNode.Component(cc.LabelTimer).run(surplusTime * 0.001);
                // 动画
                var anim_1 = it.Child('anim', cc.Animation);
                var elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001;
                var tween = cc.tween(it);
                tween.stop();
                if (elapsedTime < 0.62) {
                    anim_1.play('cting_begin', elapsedTime);
                    tween.delay(0.62 - elapsedTime).call(function () { return _this.isValid && anim_1.play('cting_loop'); }).start();
                }
                else {
                    anim_1.play('cting_loop');
                }
            }
            timeNode.active = showIndex !== info.index;
            it.Data = { index: info.index };
        });
    };
    //设置装饰偏移
    NoviceWindCtrl.prototype.resetDecorationPoint = function (itemNode, icon) {
        var frame = ResHelper_1.resHelper.getLandItemIcon(icon, this.model.getSeasonType());
        var size = frame.getOriginalSize();
        if (size.width / Constant_1.TILE_SIZE % 2 == 0) {
            itemNode.x += Constant_1.TILE_SIZE / 2;
        }
        if (size.height / Constant_1.TILE_SIZE % 2 == 0) {
            itemNode.y += Constant_1.TILE_SIZE / 2;
        }
    };
    NoviceWindCtrl.prototype.getSeaLandIcon = function (point, minx, miny, maxx, maxy) {
        if (point.x < minx) {
            return point.y < miny ? 7 : (point.y < maxy ? 3 : 4);
        }
        else if (point.x < maxx) {
            return point.y < miny ? 2 : 0;
        }
        return point.y < miny ? 6 : (point.y < maxy ? 1 : 5);
    };
    NoviceWindCtrl.prototype.setSeaLand = function (it, type, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy);
        it.Component(cc.Sprite).spriteFrame = this.getLandIcon(type + "_" + Math.min(dir, 4));
    };
    // 海浪
    NoviceWindCtrl.prototype.setSeawaveLand = function (position, point, minx, miny, maxx, maxy) {
        var dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4;
        var it = this.seawaveAnimNodePool.showNode(no, position, true);
        it.angle = angle * -90;
    };
    NoviceWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandIcon(icon);
    };
    NoviceWindCtrl.prototype.updatePlayerNickname = function (it, data) {
        var _a;
        var nameLbl = it.Child('name/val', cc.Label);
        nameLbl.string = ut.nameFormator((_a = data === null || data === void 0 ? void 0 : data.nickname) !== null && _a !== void 0 ? _a : '???', 7);
        nameLbl._forceUpdateRenderData();
        var width = Math.ceil(nameLbl.node.width * nameLbl.node.scaleX);
        var titleLbl = it.Child('name/title', cc.Label);
        if (titleLbl.setActive(!!(data === null || data === void 0 ? void 0 : data.title))) {
            var json = assetsMgr.getJsonData('title', data.title);
            titleLbl.Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey('titleText.' + (json === null || json === void 0 ? void 0 : json.id));
            titleLbl._forceUpdateRenderData();
            width = Math.max(titleLbl.node.width, width);
            nameLbl.node.y = -10;
        }
        else {
            nameLbl.node.y = 0;
        }
        it.Child('name').width = Math.max(100, width);
    };
    // 刷新显示文本节点
    NoviceWindCtrl.prototype.updateHideTextByIndex = function (index) {
        if (index === void 0) { index = -1; }
        this.textNode_.children.forEach(function (m) { return m.active = !!m.Data && m.Data.index !== index; });
        this.btinfoNode.children.forEach(function (m) { return m.Data && (m.Child('time').active = m.Data.index !== index); });
    };
    // 刷新图标层
    NoviceWindCtrl.prototype.updateIconNode = function () {
        var _this = this;
        var offset1 = cc.v2(-22, -22);
        var offset2 = cc.v2(22, -22);
        var cells = [], armyDistMap = this.player.getArmyDistMap(), avoidwarDistMap = this.model.getAvoidWarDistMap();
        for (var index in armyDistMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' });
        }
        for (var index in avoidwarDistMap) {
            var cell = this.tempShowCellMap[index];
            cell && cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_min_icon' });
        }
        this.iconNode.Items(cells, function (it, data) {
            it.setPosition(_this._temp_vec2_3.set(data.offset).addSelf(data.position));
            it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon(data.icon);
        });
    };
    // 显示选择地块
    NoviceWindCtrl.prototype.showSelectCell = function (cell) {
        if (!cell || cell.landType == Enums_1.LandType.SEA || cell.landType == Enums_1.LandType.BEACH) {
            return;
        }
        else if (cell.actIndex !== cell.index) {
            cell = this.model.getMapCellByIndex(cell.actIndex);
        }
        var pos = this.selectCellNode_.Data = cell.actPosition;
        this.selectCellNode_.Component(SelectCellCmpt_1.default).open(pos, cell.getSize());
        this.cellInfoCmpt.open(pos, cell);
        // 隐藏文本节点
        this.updateHideTextByIndex(cell.actIndex);
        // console.log(gameHpr.noviceServer.getArea(cell.index), cell.getArea())
    };
    // 隐藏
    NoviceWindCtrl.prototype.hideSelectCell = function (play) {
        if (play === void 0) { play = true; }
        if (this.selectCellNode_.Data) {
            this.selectCellNode_.Component(SelectCellCmpt_1.default).close();
            this.cellInfoCmpt.close(play);
            this.updateHideTextByIndex();
        }
    };
    // 初始化行军
    NoviceWindCtrl.prototype.initMarch = function () {
        var _this = this;
        this.cleanMarch();
        var list = this.model.getAllMarchs().filter(function (m) { return m.isCanShowMarch(); });
        this.marchLineNode_.Items(list, function (it, data) {
            var march = _this.marchs.add(it.Component(MarchCmpt_1.default).init(data, _this.marchRoleNode_, _this.key));
            march.isCheckLineOffset = false;
        });
        this.marchs.forEach(function (m) { return !m.isCheckLineOffset && _this.checkMarchLineOffset(m.getData()); });
    };
    NoviceWindCtrl.prototype.cleanMarch = function () {
        while (this.marchs.length > 0) {
            this.marchs.pop().clean();
        }
        this.marchs = [];
        // resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况
        this.marchRoleNode_.removeAllChildren();
    };
    // 检测行军线偏移
    NoviceWindCtrl.prototype.checkMarchLineOffset = function (data) {
        var others = [];
        this.marchs.forEach(function (m) {
            var d = m.getData();
            if (data.checkOtherMarchLine(d)) {
                m.angleOffset = data.startIndex === d.startIndex ? 0 : -180;
                m.isCheckLineOffset = true;
                others.push(m);
            }
        });
        var len = others.length;
        others.forEach(function (m, i) { return m.updateLineOffset(i, len); });
    };
    // 攻击地块
    NoviceWindCtrl.prototype.occupyCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 2, 0)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_TIME) {
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_time_tip')];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
                            this.hideSelectCell(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        // else if (gameHpr.getSelfToMapCellDis(index) >= 6) {
                        // 	let cell = gameHpr.world.getMapCellByIndex(index)
                        // 	if (!cell.owner) {
                        // 		return viewHelper.showAlert('toast.novice_not_attack_cell')
                        // 	}
                        // }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, function (armys, isSameSpeed, autoBackType) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                var isGuideBattle = GameHelper_1.gameHpr.guide.isCurrTag(GuideConfig_1.GuideTagType.FIRST_BATTLE_MOVE);
                                _this.model.occupyCell(armys, index, autoBackType, isSameSpeed, isGuideBattle).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 移动到地块
    NoviceWindCtrl.prototype.moveToCell = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, list, canGotoCount;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.reqSelectArmysing) {
                            return [2 /*return*/];
                        }
                        this.reqSelectArmysing = true;
                        return [4 /*yield*/, this.player.getSelectArmys(index, 1)];
                    case 1:
                        _a = _b.sent(), err = _a.err, list = _a.list, canGotoCount = _a.canGotoCount;
                        this.reqSelectArmysing = false;
                        if (!this.isActive) {
                            return [2 /*return*/];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (list.length === 0) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_idle_army')];
                        }
                        ViewHelper_1.viewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, function (armys, isSameSpeed) {
                            if (_this.isActive) {
                                _this.hideSelectCell(false);
                                _this.model.moveCellArmy(armys, index, false, isSameSpeed).then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); });
                            }
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播放新的地块效果
    NoviceWindCtrl.prototype.playNewCellEffect = function () {
        var _this = this;
        this.model.getNotPlayNewCells().forEach(function (index) {
            var cell = _this.tempShowCellMap[index];
            if (cell) {
                var json_1 = cell.getResJson() || {}, keys = Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; });
                var pos_1 = cell.actPosition, isMore_1 = keys.length > 1;
                keys.forEach(function (key, i) { return AnimHelper_1.animHelper.playFlutterCellRes(key, json_1[key], 0.3 + i * 0.2, isMore_1, _this.topLayerNode_, pos_1, _this.key); });
                AnimHelper_1.animHelper.playNewCellEffect(_this.cellEffectNode_, pos_1, _this.key);
                // 隐藏行军线
                _this.marchs.forEach(function (march) { return march.isHasIndex(index) && march.hide(1.2); });
            }
        });
    };
    NoviceWindCtrl.prototype.testPlayNewCell = function (x, y) {
        for (var i = 0; i < 5; i++) {
            var position = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone();
            AnimHelper_1.animHelper.playFlutterCellRes('stone', 30, 0.3, false, this.topLayerNode_, position, this.key);
            AnimHelper_1.animHelper.playNewCellEffect(this.cellEffectNode_, position, this.key);
        }
    };
    NoviceWindCtrl.prototype.update = function (dt) {
        var _a;
        //
        (_a = this.seawaveAnimNodePool) === null || _a === void 0 ? void 0 : _a.update(dt);
        // 检测是否需要填充地图
        this.checkUpdateMap();
        // 检测是否在相机范围
        this.checkInCameraRange();
    };
    NoviceWindCtrl.prototype.checkUpdateMap = function () {
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2_1);
        var size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y));
        if (size >= Constant_1.MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio) {
            this.updateMap(point);
            this.checkInCameraMarchLine();
        }
    };
    // 检测只会在在相机范围内的行军线
    NoviceWindCtrl.prototype.checkInCameraMarchLine = function () {
        this.marchs.forEach(function (m) { return m.checkUpdateInCamera(); });
    };
    NoviceWindCtrl.prototype.checkInCameraRange = function () {
        var _a;
        var position = CameraCtrl_1.cameraCtrl.getPosition();
        if (this.preCameraPosition.equals(position)) {
            return;
        }
        this.preCameraPosition.set(position);
        // 选择地块框
        if ((_a = this.cellInfoCmpt) === null || _a === void 0 ? void 0 : _a.checkNotInScreenRange()) {
            this.hideSelectCell(false);
        }
    };
    NoviceWindCtrl = __decorate([
        ccclass
    ], NoviceWindCtrl);
    return NoviceWindCtrl;
}(mc.BaseWindCtrl));
exports.default = NoviceWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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