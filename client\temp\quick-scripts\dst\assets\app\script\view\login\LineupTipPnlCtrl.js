
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LineupTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0ea8edJOt5FAKcpmYR/IAwN', 'LineupTipPnlCtrl');
// app/script/view/login/LineupTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var LineupTipPnlCtrl = /** @class */ (function (_super) {
    __extends(LineupTipPnlCtrl, _super);
    function LineupTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.timeNode_ = null; // path://root/time_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.login = null;
        _this.requesting = false;
        _this.elapsed = 0;
        return _this;
    }
    LineupTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LineupTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false });
                this.login = this.getModel('login');
                return [2 /*return*/];
            });
        });
    };
    LineupTipPnlCtrl.prototype.onEnter = function (data) {
        this.timeNode_.Component(cc.LabelTimer).setEndTime(ut.MAX_VALUE).run(0);
        this.elapsed = 0;
    };
    LineupTipPnlCtrl.prototype.onRemove = function () {
    };
    LineupTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/join_discord_be
    LineupTipPnlCtrl.prototype.onClickJoinDiscord = function (event, data) {
        GameHelper_1.gameHpr.openDiscord();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    LineupTipPnlCtrl.prototype.update = function (dt) {
        this.elapsed += dt;
        if (this.elapsed >= 10 && !this.requesting) {
            this.elapsed = 0;
            this.check();
        }
    };
    LineupTipPnlCtrl.prototype.check = function () {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.requesting = true;
                        return [4 /*yield*/, this.login.getLobbyServerIsNeedQueueUp()];
                    case 1:
                        ok = _a.sent();
                        this.requesting = false;
                        if (!ok) {
                            this.hide();
                            this.emit(EventType_1.default.QUEUE_UP_DONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    LineupTipPnlCtrl = __decorate([
        ccclass
    ], LineupTipPnlCtrl);
    return LineupTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LineupTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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