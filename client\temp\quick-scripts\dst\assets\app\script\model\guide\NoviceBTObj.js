
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceBTObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '13a19T7l3RGmobEXsIra7L/', 'NoviceBTObj');
// app/script/model/guide/NoviceBTObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个建筑队列
var NoviceBTObj = /** @class */ (function () {
    function NoviceBTObj() {
        this.aIndex = 0; //区域位置
        this.bUid = ''; //建筑uid
        this.bid = 0; //建筑id
        this.bLv = 0; //要建造的等级
        this.startTime = 0; //开始时间
        this.needTime = 0; //需要时间
    }
    NoviceBTObj.prototype.init = function (index, uid, id, lv, time) {
        this.aIndex = index;
        this.bUid = uid;
        this.bid = id;
        this.bLv = lv;
        this.startTime = 0;
        this.needTime = time;
        return this;
    };
    NoviceBTObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            uid: this.bUid,
            id: this.bid,
            lv: this.bLv,
            needTime: this.needTime,
            surplusTime: this.getSurplusTime(),
        };
    };
    NoviceBTObj.prototype.fromDB = function (data) {
        this.aIndex = data.aIndex;
        this.bUid = data.bUid;
        this.bid = data.bid;
        this.bLv = data.bLv;
        this.startTime = data.startTime;
        this.needTime = data.needTime;
        return this;
    };
    NoviceBTObj.prototype.getSurplusTime = function () {
        return Math.max(this.needTime - (Date.now() - this.startTime), 0);
    };
    return NoviceBTObj;
}());
exports.default = NoviceBTObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxndWlkZVxcTm92aWNlQlRPYmoudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxTQUFTO0FBQ1Q7SUFBQTtRQUVXLFdBQU0sR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNO1FBQ3pCLFNBQUksR0FBVyxFQUFFLENBQUEsQ0FBQyxPQUFPO1FBQ3pCLFFBQUcsR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNO1FBQ3RCLFFBQUcsR0FBVyxDQUFDLENBQUEsQ0FBQyxRQUFRO1FBQ3hCLGNBQVMsR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNO1FBQzVCLGFBQVEsR0FBVyxDQUFDLENBQUEsQ0FBQyxNQUFNO0lBb0N0QyxDQUFDO0lBbENVLDBCQUFJLEdBQVgsVUFBWSxLQUFhLEVBQUUsR0FBVyxFQUFFLEVBQVUsRUFBRSxFQUFVLEVBQUUsSUFBWTtRQUN4RSxJQUFJLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtRQUNuQixJQUFJLENBQUMsSUFBSSxHQUFHLEdBQUcsQ0FBQTtRQUNmLElBQUksQ0FBQyxHQUFHLEdBQUcsRUFBRSxDQUFBO1FBQ2IsSUFBSSxDQUFDLEdBQUcsR0FBRyxFQUFFLENBQUE7UUFDYixJQUFJLENBQUMsU0FBUyxHQUFHLENBQUMsQ0FBQTtRQUNsQixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQTtRQUNwQixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSwyQkFBSyxHQUFaO1FBQ0ksT0FBTztZQUNILEtBQUssRUFBRSxJQUFJLENBQUMsTUFBTTtZQUNsQixHQUFHLEVBQUUsSUFBSSxDQUFDLElBQUk7WUFDZCxFQUFFLEVBQUUsSUFBSSxDQUFDLEdBQUc7WUFDWixFQUFFLEVBQUUsSUFBSSxDQUFDLEdBQUc7WUFDWixRQUFRLEVBQUUsSUFBSSxDQUFDLFFBQVE7WUFDdkIsV0FBVyxFQUFFLElBQUksQ0FBQyxjQUFjLEVBQUU7U0FDckMsQ0FBQTtJQUNMLENBQUM7SUFFTSw0QkFBTSxHQUFiLFVBQWMsSUFBUztRQUNuQixJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDekIsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFBO1FBQ3JCLElBQUksQ0FBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQTtRQUNuQixJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUE7UUFDbkIsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFBO1FBQy9CLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQTtRQUM3QixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSxvQ0FBYyxHQUFyQjtRQUNJLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTtJQUNyRSxDQUFDO0lBQ0wsa0JBQUM7QUFBRCxDQTNDQSxBQTJDQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIOS4gOS4quW7uuetkemYn+WIl1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBOb3ZpY2VCVE9iaiB7XHJcblxyXG4gICAgcHVibGljIGFJbmRleDogbnVtYmVyID0gMCAvL+WMuuWfn+S9jee9rlxyXG4gICAgcHVibGljIGJVaWQ6IHN0cmluZyA9ICcnIC8v5bu6562RdWlkXHJcbiAgICBwdWJsaWMgYmlkOiBudW1iZXIgPSAwIC8v5bu6562RaWRcclxuICAgIHB1YmxpYyBiTHY6IG51bWJlciA9IDAgLy/opoHlu7rpgKDnmoTnrYnnuqdcclxuICAgIHB1YmxpYyBzdGFydFRpbWU6IG51bWJlciA9IDAgLy/lvIDlp4vml7bpl7RcclxuICAgIHB1YmxpYyBuZWVkVGltZTogbnVtYmVyID0gMCAvL+mcgOimgeaXtumXtFxyXG5cclxuICAgIHB1YmxpYyBpbml0KGluZGV4OiBudW1iZXIsIHVpZDogc3RyaW5nLCBpZDogbnVtYmVyLCBsdjogbnVtYmVyLCB0aW1lOiBudW1iZXIpIHtcclxuICAgICAgICB0aGlzLmFJbmRleCA9IGluZGV4XHJcbiAgICAgICAgdGhpcy5iVWlkID0gdWlkXHJcbiAgICAgICAgdGhpcy5iaWQgPSBpZFxyXG4gICAgICAgIHRoaXMuYkx2ID0gbHZcclxuICAgICAgICB0aGlzLnN0YXJ0VGltZSA9IDBcclxuICAgICAgICB0aGlzLm5lZWRUaW1lID0gdGltZVxyXG4gICAgICAgIHJldHVybiB0aGlzXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHN0cmlwKCkge1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGluZGV4OiB0aGlzLmFJbmRleCxcclxuICAgICAgICAgICAgdWlkOiB0aGlzLmJVaWQsXHJcbiAgICAgICAgICAgIGlkOiB0aGlzLmJpZCxcclxuICAgICAgICAgICAgbHY6IHRoaXMuYkx2LFxyXG4gICAgICAgICAgICBuZWVkVGltZTogdGhpcy5uZWVkVGltZSxcclxuICAgICAgICAgICAgc3VycGx1c1RpbWU6IHRoaXMuZ2V0U3VycGx1c1RpbWUoKSxcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGZyb21EQihkYXRhOiBhbnkpIHtcclxuICAgICAgICB0aGlzLmFJbmRleCA9IGRhdGEuYUluZGV4XHJcbiAgICAgICAgdGhpcy5iVWlkID0gZGF0YS5iVWlkXHJcbiAgICAgICAgdGhpcy5iaWQgPSBkYXRhLmJpZFxyXG4gICAgICAgIHRoaXMuYkx2ID0gZGF0YS5iTHZcclxuICAgICAgICB0aGlzLnN0YXJ0VGltZSA9IGRhdGEuc3RhcnRUaW1lXHJcbiAgICAgICAgdGhpcy5uZWVkVGltZSA9IGRhdGEubmVlZFRpbWVcclxuICAgICAgICByZXR1cm4gdGhpc1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRTdXJwbHVzVGltZSgpIHtcclxuICAgICAgICByZXR1cm4gTWF0aC5tYXgodGhpcy5uZWVkVGltZSAtIChEYXRlLm5vdygpIC0gdGhpcy5zdGFydFRpbWUpLCAwKVxyXG4gICAgfVxyXG59Il19