
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scene/version.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1f093OGN9BAZJA4qPbRVw1n', 'version');
// scene/version.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var GAME_NAME_SPACE = 'slg';
var VERSION = '4.0.0';
var APP_TYPE = 'global'; //global, inland
var _lang = ''; //语言
var _app_type = '';
var _server_area = ''; // 服务器区域 china, hk
function getAppType() {
    var _a;
    if (_app_type) {
        return _app_type;
    }
    else if (cc.sys.isBrowser) {
        (_a = location === null || location === void 0 ? void 0 : location.search) === null || _a === void 0 ? void 0 : _a.replace('?', '').split('&').forEach(function (m) {
            var _a = __read(m.split('='), 2), key = _a[0], val = _a[1];
            if (key === 'p') {
                _app_type = val.startsWith('g') ? 'global' : 'inland';
            }
        });
    }
    if (!_app_type) {
        _app_type = APP_TYPE;
    }
    return _app_type;
}
function getSaveServerAreaKey() {
    return '__' + GAME_NAME_SPACE + '_server_area__';
}
function getSaveLangKey() {
    return '__' + GAME_NAME_SPACE + '_lang__';
}
// 获取服务器区域列表
function getServerAreaList() {
    if (getAppType() === 'inland') {
        return ['china'];
    }
    return ['hk' /* , 'us' */];
}
// 获取服务器区域
function getServerArea() {
    var e_1, _a;
    if (_server_area) {
        return _server_area;
    }
    else if (getAppType() === 'inland') {
        _server_area = 'china';
        return _server_area;
    }
    _server_area = localStorage.getItem(getSaveServerAreaKey()) || '';
    if (!_server_area) { //如果没有 就根据语言给他分配一个
        var localizeList = [
            {
                area: 'hk', codeList: [
                    'zh-tw',
                    'zh_tw',
                    'zh',
                    'zh-sg',
                    'zh_sg',
                    'zh-hant',
                    'zh-mo',
                    'zh_cn_#hant',
                    'zh_hant',
                    'zh_mo',
                    'zh_hk',
                    'zh-hk',
                    'ja',
                    'ko',
                    'en-PH',
                    'tl-PH',
                    'id',
                    'th',
                    'ms',
                    'vi',
                ]
            },
            {
                area: 'us', codeList: [
                    'en-US',
                    'en-CA',
                    'fr-CA',
                ]
            },
        ];
        var langugeCode_1 = cc.sys.languageCode;
        try {
            for (var localizeList_1 = __values(localizeList), localizeList_1_1 = localizeList_1.next(); !localizeList_1_1.done; localizeList_1_1 = localizeList_1.next()) {
                var val = localizeList_1_1.value;
                if (val.codeList.some(function (m) { return langugeCode_1.startsWith(m); })) {
                    _server_area = val.area;
                    break;
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (localizeList_1_1 && !localizeList_1_1.done && (_a = localizeList_1.return)) _a.call(localizeList_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        _server_area = _server_area || 'hk'; //默认香港服
        localStorage.setItem(getSaveServerAreaKey(), _server_area);
    }
    var serverAreaList = getServerAreaList();
    if (serverAreaList.indexOf(_server_area) === -1) {
        _server_area = serverAreaList[0];
        localStorage.setItem(getSaveServerAreaKey(), _server_area);
    }
    return _server_area;
}
function setServerArea(val) {
    if (_server_area === val) {
        return;
    }
    else if (getServerAreaList().indexOf(val) === -1) {
        return;
    }
    _server_area = val;
    localStorage.setItem(getSaveServerAreaKey(), _server_area);
}
// 获取本地语言
function getLocalizeLanByCode() {
    var e_2, _a;
    if (getAppType() === 'inland') {
        return 'cn';
    }
    var localizeList = [
        { localize: 'hk', codeList: ['zh-hant', 'zh-mo', 'zh_cn_#hant', 'zh_hant', 'zh_mo', 'zh_hk', 'zh-hk'] },
        { localize: 'tw', codeList: ['zh-tw', 'zh_tw'] },
        { localize: 'cn', codeList: ['zh', 'zh-cn', 'zh-sg', 'zh_sg'] },
        { localize: 'jp', codeList: ['ja'] },
        { localize: 'kr', codeList: ['ko'] },
        { localize: 'idl', codeList: ['id'] },
        { localize: 'th', codeList: ['th'] },
        { localize: 'vi', codeList: ['vi'] },
    ];
    var lang = localStorage.getItem(getSaveLangKey()) || '';
    if (lang === 'en' || localizeList.some(function (m) { return m.localize === lang; })) {
        return lang;
    }
    var langugeCode = cc.sys.languageCode;
    try {
        for (var localizeList_2 = __values(localizeList), localizeList_2_1 = localizeList_2.next(); !localizeList_2_1.done; localizeList_2_1 = localizeList_2.next()) {
            var val = localizeList_2_1.value;
            if (val.codeList.some(function (m) { return langugeCode.startsWith(m); })) {
                lang = val.localize;
                break;
            }
        }
    }
    catch (e_2_1) { e_2 = { error: e_2_1 }; }
    finally {
        try {
            if (localizeList_2_1 && !localizeList_2_1.done && (_a = localizeList_2.return)) _a.call(localizeList_2);
        }
        finally { if (e_2) throw e_2.error; }
    }
    return lang || 'en'; //默认英文
}
function getLocalLang() {
    if (!_lang) {
        _lang = getLocalizeLanByCode();
        localStorage.setItem(getSaveLangKey(), _lang);
    }
    return _lang;
}
function newThinkingAnalyticsAPI(data) {
    var isGLobal = getAppType() === 'global';
    var appId = data === null || data === void 0 ? void 0 : data.appId;
    if (!appId) {
        appId = isGLobal ? '64cf813ae2ef43608f067452f0dfad7e' : '655d3dd4d2e4421fa8aef4fc0bf06ca9';
    }
    var ta = new ThinkingAnalyticsAPI({
        appId: appId,
        serverUrl: isGLobal ? 'https://receiver-ta-global.twomiles.cn' : 'https://receiver-ta.twomiles.cn',
        autoTrack: {
            appShow: false,
            appHide: false,
            appCrash: true,
            appInstall: true,
        },
        debugMode: (data === null || data === void 0 ? void 0 : data.debugMode) || 'none',
        enableNative: true,
        enableLog: false,
    });
    ta.init();
    return ta;
}
exports.default = {
    GAME_NAME_SPACE: GAME_NAME_SPACE,
    VERSION: VERSION,
    getAppType: getAppType,
    getLocalLang: getLocalLang,
    getServerAreaList: getServerAreaList,
    getServerArea: getServerArea,
    setServerArea: setServerArea,
    newThinkingAnalyticsAPI: newThinkingAnalyticsAPI,
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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