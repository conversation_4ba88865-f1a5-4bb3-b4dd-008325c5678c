
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/PawnFrameAnimationCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e6b8byFYU9KZKXCzRYyU/8w', 'PawnFrameAnimationCmpt');
// app/script/view/cmpt/PawnFrameAnimationCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var FrameAnimConf_1 = require("../../common/constant/FrameAnimConf");
var PawnAnimConf_1 = require("../area/PawnAnimConf");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 士兵的帧动画组件
var PawnFrameAnimationCmpt = /** @class */ (function (_super) {
    __extends(PawnFrameAnimationCmpt, _super);
    function PawnFrameAnimationCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sfs = [];
        _this.sprite = null; //动画精灵
        _this.frames = {};
        _this.currFrameIndex = 0; //当前帧
        _this.frameNamePrefix = '';
        _this.loading = false;
        _this.conf = null;
        _this.id = 0;
        _this.anim = null; //当前播放的动画配置
        _this.playInterval = 0;
        _this.playElapsed = 0;
        _this.playFrameIndex = 0;
        _this.playCallback = null; //播放回调
        _this.delayPlayAnim = 0; //延迟播放
        _this.ANIM_NAMES = ['skill', 'attack', 'walk', 'move']; // 动画播放顺序
        _this.curIndex = 0; //当前播放第几个动画
        return _this;
    }
    PawnFrameAnimationCmpt.prototype.init = function (spr, id, key) {
        var _this = this;
        this.id = id;
        this.sprite = spr;
        var isBuySkin = key === 'menu/CollectionSkinInfo';
        if (isBuySkin) {
            this.conf = FrameAnimConf_1.SHOP_PAWN_SKIN_ANIM_CONF[id];
        }
        else {
            this.conf = PawnAnimConf_1.PAWN_ANIM_CONF[PawnAnimConf_1.PAWN_ANIM_CONF_TRANSITION[id] || id];
        }
        if (!this.conf) {
            cc.error('PawnAnimationCmpt init error. id: ' + id);
        }
        this.frameNamePrefix = this.conf.type + '_' + id + '_';
        var frames = this.getAnimConfAllFrames();
        if (frames.length > 0 && this.sfs.length === 0) {
            this.loadFrames(frames, key);
        }
        else {
            this.frames = {};
            this.sfs.forEach(function (m) { return _this.frames[m.name] = m; });
        }
        this.currFrameIndex = 0;
        return this;
    };
    PawnFrameAnimationCmpt.prototype.clean = function () {
        this.frames = {};
        this.anim = null;
        this.playCallback = null;
        this.currFrameIndex = 0;
    };
    PawnFrameAnimationCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    // 获取所有动画需要的帧
    PawnFrameAnimationCmpt.prototype.getAnimConfAllFrames = function () {
        var frames = [], obj = {};
        this.conf.anims.forEach(function (m) { return m.frameIndexs.forEach(function (frame) {
            if (frame !== '00' && !obj[frame]) {
                obj[frame] = true;
                frames.push(frame);
            }
        }); });
        return frames;
    };
    // 加载所有帧
    PawnFrameAnimationCmpt.prototype.loadFrames = function (frames, key) {
        return __awaiter(this, void 0, void 0, function () {
            var url, sfs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.loading) {
                            return [2 /*return*/];
                        }
                        this.loading = true;
                        url = this.conf.type + '/' + this.id + '/' + this.frameNamePrefix;
                        return [4 /*yield*/, Promise.all(frames.map(function (m) { return assetsMgr.loadTempRes(url + m, cc.SpriteFrame, key); }))];
                    case 1:
                        sfs = _a.sent();
                        this.frames = {};
                        sfs.forEach(function (m, i) {
                            if (m) {
                                _this.frames[m.name] = m;
                            }
                            else {
                                cc.error(frames[i]);
                            }
                        });
                        this.loading = false;
                        this.setFrame(this.currFrameIndex);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 每帧刷新
    PawnFrameAnimationCmpt.prototype.updateFrame = function (dt) {
        // 刷新精灵动画
        if (!this.loading && this.anim) {
            if (this.delayPlayAnim > 0) {
                this.delayPlayAnim -= dt;
                if (this.delayPlayAnim <= 0) {
                    this.node.opacity = 255;
                }
            }
            else {
                this.playElapsed += dt;
                if (this.playElapsed >= this.playInterval) {
                    this.playElapsed -= this.playInterval;
                    this.setFrame(this.playFrameIndex);
                    if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {
                        this.playFrameIndex += 1;
                    }
                    else if (this.anim.loop) {
                        // this.playFrameIndex = 0
                        this.curIndex = ut.loopValue(this.curIndex + 1, this.ANIM_NAMES.length);
                        this.play();
                    }
                    else {
                        this.anim = null;
                        this.playCallback && this.playCallback();
                    }
                }
            }
        }
    };
    // 播放动画
    PawnFrameAnimationCmpt.prototype.play = function (name, cb, startTime, intervalMul) {
        name = name || this.ANIM_NAMES[this.curIndex];
        var anim = this.anim = this.conf.anims.find(function (m) { return m.name === name; });
        if (!anim) { // 可能有些皮肤没有技能动画，继续往下找
            this.curIndex = ut.loopValue(this.curIndex + 1, this.ANIM_NAMES.length);
            name = this.ANIM_NAMES[this.curIndex];
            anim = this.anim = this.conf.anims.find(function (m) { return m.name === name; });
        }
        if (!anim) {
            return cb && cb();
        }
        this.playCallback = cb;
        this.playInterval = (anim.interval || 1) * (intervalMul || 1);
        startTime = startTime || 0;
        var index = Math.floor(startTime / this.playInterval);
        this.playFrameIndex = index + 1;
        this.playElapsed = startTime % this.playInterval;
        this.setFrame(index);
    };
    // 设置一帧
    PawnFrameAnimationCmpt.prototype.setFrame = function (index) {
        this.currFrameIndex = index;
        if (this.anim && !this.loading) {
            var name = this.anim.frameIndexs[index];
            if (name) {
                this.sprite.spriteFrame = this.frames[this.frameNamePrefix + name];
            }
        }
    };
    __decorate([
        property([cc.SpriteFrame])
    ], PawnFrameAnimationCmpt.prototype, "sfs", void 0);
    PawnFrameAnimationCmpt = __decorate([
        ccclass
    ], PawnFrameAnimationCmpt);
    return PawnFrameAnimationCmpt;
}(cc.Component));
exports.default = PawnFrameAnimationCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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