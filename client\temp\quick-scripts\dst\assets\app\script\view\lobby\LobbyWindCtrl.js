
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/LobbyWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '28b5dexKKVMZZn3P4lG6cKj', 'LobbyWindCtrl');
// app/script/view/lobby/LobbyWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AdaptWidthCmpt_1 = require("../cmpt/AdaptWidthCmpt");
var LobbyModeCmpt_1 = require("./LobbyModeCmpt");
var LongPressLikeCmpt_1 = require("./LongPressLikeCmpt");
var SnailIsleCmpt_1 = require("./SnailIsleCmpt");
var ccclass = cc._decorator.ccclass;
var LobbyWindCtrl = /** @class */ (function (_super) {
    __extends(LobbyWindCtrl, _super);
    function LobbyWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.modeNode_ = null; // path://root_n/content/mode_n
        _this.bottomNode_ = null; // path://root_n/content/bottom_n
        _this.teamsNode_ = null; // path://root_n/content/bottom_n/team/teams_n_nbe
        _this.invitesNode_ = null; // path://root_n/content/bottom_n/team/teams_n_nbe/x/invites_n
        _this.readyNode_ = null; // path://root_n/content/bottom_n/team/layout/ready_n
        _this.buttonsNode_ = null; // path://root_n/content/bottom_n/team/layout/buttons_n
        _this.likeTwNode_ = null; // path://root_n/content/bottom_n/twomiles/like_tw_n
        _this.ingotNode_ = null; // path://root_n/top/ingot_n
        _this.warTokenNode_ = null; // path://root_n/top/war_token_n
        _this.goldNode_ = null; // path://root_n/top/gold_n
        _this.loadGameNode_ = null; // path://load_game_n
        //@end
        _this.model = null;
        _this.team = null;
        _this.user = null;
        _this.ingotValLbl = null;
        _this.warTokenValLbl = null;
        _this.goldValLbl = null;
        _this.curLoadGameProgressBar = null;
        _this.snailIsleCmpt = null;
        _this.modeCmpt = null;
        _this.cancelApplyProgressTween = null;
        _this.curMode = 0;
        _this.curRoomState = null;
        _this.isLoadGame = false;
        _this.curLoadGamePercent = 0;
        return _this;
    }
    LobbyWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_INGOT] = this.onUpdateIngot, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_WAR_TOKEN] = this.onUpdateWarToken, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_TEAM_MODE] = this.onUpdateTeamMode, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_TEAM_LIST] = this.onUpdateTeamList, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_TEAM_APPLYS] = this.onUpdateTeamInvite, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.MODIFY_NICKNAME_SUC] = this.onUpdatePlayerHeadIcon, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_PLAYER_HEAD_ICON] = this.onUpdatePlayerHeadIcon, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.SHOW_GAME_LOADING] = this.onShowGameLoading, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.SHOW_SNAILISLE_DESC] = this.onShowSnailisleDesc, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_LIKE_JWM_COUNT] = this.onUpdateLikeJwmCount, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_ACTIVITY_RECORD] = this.onUpdateActivityRecord, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_PLANT] = this.onUpdatePlant, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.HIDE_TOP_NODE] = this.onHideTopNode, _q),
        ];
    };
    LobbyWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var twomilesDescNoe;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.model = this.getModel('lobby');
                        this.team = this.getModel('team');
                        this.user = this.getModel('user');
                        this.ingotValLbl = this.ingotNode_.FindChild('val', cc.LabelRollNumber);
                        this.warTokenValLbl = this.warTokenNode_.FindChild('val', cc.LabelRollNumber);
                        this.goldValLbl = this.goldNode_.FindChild('val', cc.LabelRollNumber);
                        this.curLoadGameProgressBar = this.loadGameNode_.Child('progress/bar', cc.Sprite);
                        this.snailIsleCmpt = this.modeNode_.Child('root/100', SnailIsleCmpt_1.default);
                        this.likeTwNode_.getComponent(LongPressLikeCmpt_1.default).init();
                        twomilesDescNoe = this.bottomNode_.FindChild('twomiles/desc');
                        twomilesDescNoe.getComponent(cc.Widget).updateAlignment();
                        twomilesDescNoe.FindChild('val', AdaptWidthCmpt_1.default).setMaxWidth(twomilesDescNoe.width);
                        this.modeCmpt = this.modeNode_.getComponent(LobbyModeCmpt_1.default).init(this.onUpdateMode.bind(this), this.onPlayModeRoll.bind(this));
                        return [4 /*yield*/, this.model.init()];
                    case 1:
                        _a.sent();
                        this.snailIsleCmpt.create();
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyWindCtrl.prototype.onEnter = function (data) {
        GameHelper_1.gameHpr.resetByLobby();
        this.model.active();
        this.openLoadGame(false);
        this.reinit();
        GameHelper_1.gameHpr.playLobbyBgm();
        this.checkFirstNewbieEnd();
    };
    LobbyWindCtrl.prototype.onClean = function () {
        var _a, _b;
        (_a = this.cancelApplyProgressTween) === null || _a === void 0 ? void 0 : _a.stop();
        this.cancelApplyProgressTween = null;
        (_b = this.snailIsleCmpt) === null || _b === void 0 ? void 0 : _b.clean();
        this.snailIsleCmpt = null;
        this.model.clean();
        this.user.clean();
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/content/bottom_n/team/teams_n_nbe
    LobbyWindCtrl.prototype.onClickTeams = function (event, _) {
        audioMgr.playSFX('click');
        if (event.target.name === 'x') {
            return ViewHelper_1.viewHelper.showPnl('lobby/TeamList', !this.team.isInGame());
        }
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('lobby/UserInfo', data, 'team'); // TeammateInfo'
        }
        else if (!GameHelper_1.gameHpr.team.isMyInviteAuth()) {
            ViewHelper_1.viewHelper.showAlert('toast.not_invite_auth');
        }
        else {
            ViewHelper_1.viewHelper.showPnl('lobby/TeamList', !this.team.isInGame());
        }
    };
    // path://root_n/chat_be
    LobbyWindCtrl.prototype.onClickChat = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('lobby/LobbyChat');
    };
    // path://root_n/content/bottom_n/team/layout/buttons_n/enter_game_be
    LobbyWindCtrl.prototype.onClickEnterGame = function (event, data) {
        var _this = this;
        var _a, _b;
        if (((_a = this.curRoomState) === null || _a === void 0 ? void 0 : _a.state) === Enums_1.RoomStateType.CLOSE) {
            return ViewHelper_1.viewHelper.showAlert('login.server_maintaining_desc');
        }
        else if (((_b = this.curRoomState) === null || _b === void 0 ? void 0 : _b.state) === Enums_1.RoomStateType.IN_MATCH) {
            return ViewHelper_1.viewHelper.showAlert('toast.now_open_server_tip');
        }
        else if (this.team.hasTeam() && !this.user.getPlaySid()) {
            return ViewHelper_1.viewHelper.showAlert('toast.no_enter_game_need_exit_team'); //如果有队伍 但是没有playSid 那么不能直接进入游戏
        }
        else if (!this.team.isInGame() && this.user.isPassNewbie() && this.curMode === Enums_1.ServerType.NEWBIE) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.pass_enter_newbie_tip', {
                ok: function () { return _this.enterGame(); },
                cancel: function () { }
            });
        }
        this.enterGame();
    };
    // path://root_n/content/bottom_n/team/layout/buttons_n/apply_be
    LobbyWindCtrl.prototype.onClickApply = function (event, data) {
        var _this = this;
        if (this.curRoomState.state === Enums_1.RoomStateType.CLOSE) {
            return ViewHelper_1.viewHelper.showAlert('login.server_maintaining_desc');
        }
        else if (this.curRoomState.state === Enums_1.RoomStateType.IN_MATCH) {
            return ViewHelper_1.viewHelper.showAlert('toast.now_open_server_no_apply');
        }
        else if (this.user.isCanPlayNewbie() && !this.user.isNewbie() && !this.user.isPassNewbie() && this.curMode !== Enums_1.ServerType.NEWBIE) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.newbie_apply_other_game_tip', {
                params: ['ui.title_server_name_' + this.curMode],
                ok: function () { return _this.apply(); },
                cancel: function () { }
            });
        }
        this.apply();
    };
    // path://root_n/content/bottom_n/team/layout/buttons_n/cancel_apply/cancel_apply_be
    LobbyWindCtrl.prototype.onClickCancelApply = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (this.curRoomState.state === Enums_1.RoomStateType.IN_MATCH) {
            return ViewHelper_1.viewHelper.showAlert('toast.now_open_server_no_capply');
        }
        var time = this.team.getCancelApplySurplusTime();
        if (time > 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.cancel_apply_tip', { params: [GameHelper_1.gameHpr.millisecondToString(time)] });
        }
        else if (this.team.hasTeam()) {
            return ViewHelper_1.viewHelper.showMessageBox(this.team.isCaptain() ? 'ui.captain_cancel_apply_tip' : 'ui.team_cancel_apply_tip', {
                ok: function () { return _this.isValid && _this.cancelApply(); },
                cancel: function () { }
            });
        }
        else {
            this.cancelApply();
        }
    };
    // path://root_n/content/mode_n/next_mode_nbe
    LobbyWindCtrl.prototype.onClickNextMode = function (event, data) {
        var add = event.target.name === '0' ? -1 : 1;
        this.modeCmpt.autoRollNextMode(add);
    };
    // path://root_n/content/mode_n/root/1/server_rule_be
    LobbyWindCtrl.prototype.onClickServerRule = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('lobby/ModeRuleDesc', this.curMode);
    };
    // path://root_n/content/mode_n/bar/mode_bar_be
    LobbyWindCtrl.prototype.onClickModeBar = function (event, data) {
        audioMgr.playSFX('click');
        this.modeCmpt.autoRollToIndex(event.target.Data);
    };
    // path://root_n/content/bottom_n/team/layout/ready_n/ready_be
    LobbyWindCtrl.prototype.onClickReady = function (event, data) {
        var isCanSelect = this.curMode !== Enums_1.ServerType.NEWBIE && !this.user.isNewbie();
        ViewHelper_1.viewHelper.showPnl('lobby/ReadyInfo', isCanSelect);
    };
    // path://root_n/menu_right/menu_right/open_menu_be
    LobbyWindCtrl.prototype.onClickOpenMenu = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/UIMenuChild');
    };
    // path://root_n/top/gold_n/add_gold_be
    LobbyWindCtrl.prototype.onClickAddGold = function (event, data) {
        ViewHelper_1.viewHelper.showBuyGoldTipPnl();
    };
    // path://root_n/content/bottom_n/team/teams_n_nbe/x/invites_n/item/root/team_invite_be
    LobbyWindCtrl.prototype.onClickTeamInvite = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('lobby/TeamInvite');
    };
    // path://root_n/menu_right/activity_be
    LobbyWindCtrl.prototype.onClickActivity = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/ActivitiesPnl');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 重新连接
    LobbyWindCtrl.prototype.onNetReconnect = function () {
        this.reinit();
    };
    // 刷新元宝
    LobbyWindCtrl.prototype.onUpdateIngot = function () {
        var val = this.user.getIngot();
        this.ingotValLbl.Color(val < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.to(val);
    };
    // 刷新兵符
    LobbyWindCtrl.prototype.onUpdateWarToken = function () {
        this.warTokenValLbl.to(this.user.getWarToken());
    };
    // 刷新金币
    LobbyWindCtrl.prototype.onUpdateGold = function () {
        this.goldValLbl.to(this.user.getGold());
    };
    // 刷新队伍信息
    LobbyWindCtrl.prototype.onUpdateTeamMode = function () {
        this.modeCmpt.updateMode();
    };
    // 刷新队伍信息
    LobbyWindCtrl.prototype.onUpdateTeamList = function () {
        this.updateTeamInfo();
    };
    // 刷新组队邀请信息
    LobbyWindCtrl.prototype.onUpdateTeamInvite = function () {
        this.updateTeamInvite();
    };
    // 刷新头像信息
    LobbyWindCtrl.prototype.onUpdatePlayerHeadIcon = function () {
        var it = this.teamsNode_.Child('0');
        ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val'), this.user.getHeadIcon(), this.key);
        it.Child('name', cc.Label).string = ut.nameFormator(this.user.getNickname() || '???', 4);
    };
    // 加载游戏
    LobbyWindCtrl.prototype.onShowGameLoading = function (sid) {
        this.openLoadGame(true);
        var item = this.loadGameNode_.Child('mode/item'), type = GameHelper_1.gameHpr.getServerType(sid);
        item.Child('title').setLocaleKey('ui.title_server_name_' + type);
        item.Child('title/val').setLocaleKey('ui.title_server_name_' + type);
        item.Child('icon/val', cc.Sprite).spriteFrame = this.modeNode_.Child('root/' + type + '/icon/val', cc.Sprite).spriteFrame;
    };
    // 显示蜗牛岛说明
    LobbyWindCtrl.prototype.onShowSnailisleDesc = function (val) {
        this.bottomNode_.Child('twomiles/desc').active = val;
    };
    // 刷新点赞九万亩数量
    LobbyWindCtrl.prototype.onUpdateLikeJwmCount = function (state) {
        var _a;
        if (!this.likeTwNode_.active) {
            this.likeTwNode_.active = true;
        }
        (_a = this.snailIsleCmpt) === null || _a === void 0 ? void 0 : _a.updateLikeJwmCount(state);
    };
    // 刷新活动记录
    LobbyWindCtrl.prototype.onUpdateActivityRecord = function () {
        // this.followdcNode_.active = !this.user.getActivityRecord()[1]
    };
    // 刷新种植
    LobbyWindCtrl.prototype.onUpdatePlant = function () {
        var _a;
        (_a = this.snailIsleCmpt) === null || _a === void 0 ? void 0 : _a.updatePlant();
    };
    // 显隐顶部元宝金币节点
    LobbyWindCtrl.prototype.onHideTopNode = function (val) {
        this.goldNode_.active = this.ingotNode_.active = val;
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    LobbyWindCtrl.prototype.reinit = function () {
        var _this = this;
        this.onUpdateActivityRecord();
        this.updateTopInfo();
        this.model.getCanSelectRooms().forEach(function (m) { return _this.updateModeInfo(m); });
        this.snailIsleCmpt.init();
        this.curMode = this.model.checkInitRoomType();
        this.modeCmpt.reset(this.model.getCanSelectRooms(), this.curMode);
    };
    // 检测是否首次完成新手区
    LobbyWindCtrl.prototype.checkFirstNewbieEnd = function () {
        var passNewbieIndex = this.user.getPassNewbieIndex();
        if (!this.team.isInGame() && passNewbieIndex > 0 && this.user.getAccTotalGameCount() <= passNewbieIndex) {
            if (!storageMgr.loadBool('show_first_newbie_end_tip')) {
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'lobby/FirstNewbieEndTip' });
            }
        }
    };
    // 刷新顶部信息
    LobbyWindCtrl.prototype.updateTopInfo = function () {
        var ingot = this.user.getIngot();
        this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.set(ingot);
        this.warTokenValLbl.set(this.user.getWarToken());
        this.goldValLbl.set(this.user.getGold());
    };
    // 刷新模式
    LobbyWindCtrl.prototype.onUpdateMode = function (type) {
        // 设置类型
        this.model.setCurRoomType(type);
        // 设置队伍的类型
        if (this.team.hasTeam() && this.team.isCaptain() && !this.team.isInGame() && !this.team.isInApply()) {
            this.team.setRoomType(type);
        }
        // 上一个
        var prevType = this.curMode;
        this.curMode = type;
        if (type < Enums_1.LobbyModeType.SNAIL_ISLE) {
            // 跟新mode
            this.updateModeInfo(type);
            // 刷新队伍信息
            this.updateTeamInfo();
            // 首次显示规则
            if (type !== Enums_1.ServerType.NEWBIE && !GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_MODE_RULE + '_' + type)) {
                GameHelper_1.gameHpr.setNoLongerTip(Enums_1.NoLongerTipKey.SHOW_MODE_RULE + '_' + type, true);
                ViewHelper_1.viewHelper.showPnl('lobby/ModeRuleDesc', type);
            }
        }
        // 刷新蜗牛岛
        if (type === Enums_1.LobbyModeType.SNAIL_ISLE) {
            this.snailIsleCmpt.enter();
            this.likeTwNode_.active = this.model.isInitLikeJwmCount();
        }
        else if (prevType !== this.curMode && prevType === Enums_1.LobbyModeType.SNAIL_ISLE) {
            this.snailIsleCmpt.leave();
        }
        // 切换底部信息
        this.bottomNode_.Swih(Constant_1.LOBBY_MODE_BUTTOM_NAME[type]);
    };
    LobbyWindCtrl.prototype.updateModeInfo = function (type) {
        var node = this.modeNode_.Child('root/' + type);
        // 显示时间
        var info = this.curRoomState = this.model.getRoomStateInfoByType(type);
        var nameNode = node.Child('desc/name'), timer = node.Child('desc/val', cc.LabelTimer);
        nameNode.setLocaleKey('ui.room_mode_state_desc_' + info.state);
        timer.setActive(true);
        if (info.state === Enums_1.RoomStateType.NONE) { //未开启
            timer.string = ut.dateFormat('MM-dd hh:mm', info.time);
        }
        else if (info.state === Enums_1.RoomStateType.IN_MATCH) { //即将开启
            timer.setEndTime(0).setFormat('mm:ss').run(info.time * 0.001);
        }
        else if (info.state === Enums_1.RoomStateType.IN_GAME) { //开启中
            timer.setEndTime(ut.MAX_VALUE).setFormat(function (time) { return GameHelper_1.gameHpr.millisecondToStringForDay(time * 1000); }).run(info.time * 0.001);
        }
        else {
            timer.setActive(false);
        }
        // 排位显示段位
        if (type === Enums_1.ServerType.RANKED) {
            var _a = GameHelper_1.gameHpr.resolutionRankScore(this.user.getRankScore(), 1), id = _a.id, winPoint = _a.winPoint;
            ResHelper_1.resHelper.loadRankScoreIcon(id, node.Child('rank_icon'), this.key);
        }
    };
    // 刷新队伍信息
    LobbyWindCtrl.prototype.updateTeamInfo = function () {
        var _this = this;
        var _a, _b, _c, _d;
        var hasTeam = this.team.hasTeam();
        var isPlay = this.team.isInGame();
        var isApply = this.team.isInApply();
        var inMatch = ((_a = this.curRoomState) === null || _a === void 0 ? void 0 : _a.state) === Enums_1.RoomStateType.IN_MATCH; //即将开启
        var isCaptain = this.team.isCaptain();
        var isNewbie = this.user.isNewbie();
        var isCurrNewbieServer = this.model.getCurRoomType() === Enums_1.ServerType.NEWBIE;
        var isCurrRoom = this.team.getRoomType() === this.model.getCurRoomType(); //是否当前队伍的选择的类型
        var isCanAdd = !isCurrNewbieServer && !isNewbie && !isPlay && !isApply && this.team.isMyInviteAuth(); /* && (isCurrRoom || !hasTeam) */
        var teamUid = this.team.getUid();
        // 刷新队伍列表
        var list = this.team.getActTeammates();
        var count = this.teamsNode_.childrenCount - 1;
        for (var i = 0; i < count; i++) {
            var it = this.teamsNode_.children[i], data = list[i];
            it.Data = data;
            it.Component(cc.Button).interactable = !!data || isCanAdd;
            var captain = (_b = it.Child('captain')) === null || _b === void 0 ? void 0 : _b.setActive(!!data && data.uid === teamUid && hasTeam);
            it.Child('auth').active = !captain && (data === null || data === void 0 ? void 0 : data.job) === 2;
            if (data) {
                ResHelper_1.resHelper.loadPlayerHead(it.Child('head').Swih('val')[0], data.headIcon, this.key);
            }
            else {
                it.Child('head').Swih(isCanAdd ? 'add' : 'ban');
            }
            it.Child('name', cc.Label).string = data ? ut.nameFormator(data.nickname || '???', 4) : '';
            var wait = it.Child('wait').setActive(!!data && data.job === undefined);
            it.Child('name').opacity = it.Child('head/val').opacity = wait ? 120 : 255;
        }
        // 队伍人数
        var can = this.teamsNode_.Child('x', cc.Button).interactable = hasTeam || (!isCurrNewbieServer && !isNewbie);
        if (can) {
            var it = this.teamsNode_.Child('x/root').Swih('team', false, 'bg')[0];
            it.Child('count/val', cc.Label).string = list.length + '/';
            it.Child('count/max', cc.Label).string = this.team.getTeamUserMaxNum() + '';
        }
        else {
            this.teamsNode_.Child('x/root').Swih('ban', false, 'bg');
        }
        // 主按钮
        this.readyNode_.active = this.curMode !== Enums_1.ServerType.NEWBIE && !isNewbie;
        if (isPlay || isNewbie || inMatch || (isCurrNewbieServer && !isApply)) { //如果有游玩的区 或者是新手 直接进入游戏
            if (isCurrRoom || (isCurrNewbieServer && !isPlay) || inMatch) {
                var isClose = ((_c = this.curRoomState) === null || _c === void 0 ? void 0 : _c.state) === Enums_1.RoomStateType.CLOSE;
                var button = this.buttonsNode_.Swih('enter_game_be')[0];
                button.opacity = isClose || inMatch ? 150 : 255;
                var isYetEnter = isPlay && !!this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CITY_INDEX);
                button.Child('val').setLocaleKey(isYetEnter ? 'ui.button_goon_game' : 'ui.button_enter_game');
            }
            else if (!isCurrNewbieServer && isNewbie) {
                this.buttonsNode_.Swih('ban')[0].Child('val').setLocaleKey('ui.play_game_cond_desc_0'); //需要先完成新手区
            }
            else if (isPlay) {
                this.buttonsNode_.Swih('ban')[0].Child('val').setLocaleKey('ui.play_game_cond_desc_1', 'ui.title_server_name_' + this.team.getRoomType()); //对局中
            }
            else {
                this.buttonsNode_.Swih('');
            }
            this.readyNode_.active = false;
        }
        else if (isApply) {
            if (isCurrRoom) {
                var node = this.buttonsNode_.Swih('cancel_apply')[0];
                var time = this.team.getCancelApplySurplusTime();
                var bar = node.Child('cancel_apply_be/bar', cc.Sprite);
                (_d = this.cancelApplyProgressTween) === null || _d === void 0 ? void 0 : _d.stop();
                if (bar.setActive(time > 0)) {
                    bar.fillRange = time / Constant_1.SERVER_APPLY_CANCEL_CD;
                    this.cancelApplyProgressTween = cc.tween(bar).to(time * 0.001, { fillRange: 0 }).start();
                }
                else {
                    this.cancelApplyProgressTween = null;
                }
            }
            else {
                this.readyNode_.active = false;
                this.buttonsNode_.Swih('ban')[0].Child('val').setLocaleKey('ui.play_game_cond_desc_2', 'ui.title_server_name_' + this.team.getRoomType()); //报名中
            }
        }
        else if (isCaptain) {
            var node_1 = this.buttonsNode_.Swih('apply_be')[0];
            var time = this.model.getNextApplyCDTime();
            var lbl_1 = node_1.Child('time', cc.LabelTimer);
            node_1.Component(cc.Button).interactable = !time;
            if (lbl_1.setActive(time > 0)) {
                lbl_1.run(time * 0.001, function () {
                    if (_this.isValid) {
                        lbl_1.setActive(false);
                        node_1.Component(cc.Button).interactable = true;
                    }
                });
            }
        }
        else {
            this.buttonsNode_.Swih('wait_apply');
        }
        // 邀请
        this.updateTeamInvite();
    };
    LobbyWindCtrl.prototype.updateTeamInvite = function () {
        var _this = this;
        var datas = this.team.getReceiveInvites().sort(function (a, b) { return b.time - a.time; }).slice(0, 2), len = datas.length;
        this.invitesNode_.Items(datas, function (it, data, i) {
            var root = it.Child('root');
            root.Child('arrow').active = len === 1 || i < len - 1;
            ResHelper_1.resHelper.loadPlayerHead(root.Child('team_invite_be/head/val'), data.headIcon, _this.key);
        });
    };
    // 播放mode滚动
    LobbyWindCtrl.prototype.onPlayModeRoll = function (val, isChange) {
        this.modeNode_.Child('next_mode_nbe').children.forEach(function (m) {
            m.Component(cc.Button).interactable = !val;
            m.opacity = val ? 80 : 255;
        });
        if (isChange) {
            this.bottomNode_.opacity = val ? 100 : 255;
        }
    };
    // 打开加载游戏
    LobbyWindCtrl.prototype.openLoadGame = function (val) {
        this.curLoadGameProgressBar.fillRange = 0;
        this.isLoadGame = val;
        this.curLoadGamePercent = 0;
        this.rootNode_.active = !val;
        this.loadGameNode_.active = val;
    };
    // 报名
    LobbyWindCtrl.prototype.apply = function () {
        var curRoomType = this.model.getCurRoomType();
        var time = this.curRoomState.state === Enums_1.RoomStateType.NONE ? this.curRoomState.time : 0;
        this.model.apply().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (!ViewHelper_1.viewHelper.showNoLongerTip(Enums_1.NoLongerTipKey.APPLY_SUCCEED, {
                select: true,
                content: 'ui.apply_succeed_tip',
                params: ['ui.title_server_name_' + curRoomType, ut.dateFormat('MM/dd hh:mm', time)],
                okText: 'ui.button_gotit',
                ok: function () { return GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.MSG); },
            })) {
                GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.MSG);
            }
        });
    };
    // 取消报名
    LobbyWindCtrl.prototype.cancelApply = function () {
        this.model.cancelApply().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    // 进入游戏
    LobbyWindCtrl.prototype.enterGame = function () {
        this.model.enterGame().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
        });
    };
    LobbyWindCtrl.prototype.update = function (dt) {
        this.model.update(dt);
        if (this.isLoadGame) {
            var dstPercent = this.model.getLoadGamePercent();
            if (this.curLoadGamePercent >= dstPercent) {
                return;
            }
            this.curLoadGamePercent = Math.min(this.curLoadGamePercent + 1 * dt, dstPercent);
            if (this.curLoadGamePercent >= 1) {
                this.isLoadGame = false;
                this.curLoadGameProgressBar.fillRange = 1;
                mc.unlockTouch('load_game');
                ViewHelper_1.viewHelper.gotoWind('main');
            }
            else {
                this.curLoadGameProgressBar.fillRange = this.curLoadGamePercent;
            }
        }
    };
    LobbyWindCtrl = __decorate([
        ccclass
    ], LobbyWindCtrl);
    return LobbyWindCtrl;
}(mc.BaseWindCtrl));
exports.default = LobbyWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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