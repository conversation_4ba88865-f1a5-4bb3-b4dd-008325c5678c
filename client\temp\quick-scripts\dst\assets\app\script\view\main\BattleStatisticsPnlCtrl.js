
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/BattleStatisticsPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8adc3yz59FIq7ldoXcbxXnT', 'BattleStatisticsPnlCtrl');
// app/script/view/main/BattleStatisticsPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BattleStatisticsPnlCtrl = /** @class */ (function (_super) {
    __extends(BattleStatisticsPnlCtrl, _super);
    function BattleStatisticsPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BattleStatisticsPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BattleStatisticsPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BattleStatisticsPnlCtrl.prototype.onEnter = function (list) {
        var _this = this;
        var infoMap = {};
        list.forEach(function (m) {
            var battleInfo = m.battle.battleInfo, pawns = m.battle.pawns;
            infoMap[Enums_1.BattleStatistics.SUM_DAMAGE] = (infoMap[Enums_1.BattleStatistics.SUM_DAMAGE] || 0) + battleInfo[Enums_1.BattleStatistics.SUM_DAMAGE]; //伤害
            infoMap[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] = (infoMap[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] || 0) + battleInfo[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED]; //承伤
            infoMap[Enums_1.BattleStatistics.SUM_KILL] = (infoMap[Enums_1.BattleStatistics.SUM_KILL] || 0) + battleInfo[Enums_1.BattleStatistics.SUM_KILL]; //击杀
            infoMap[Enums_1.BattleStatistics.PAWN_DEAD] = (infoMap[Enums_1.BattleStatistics.PAWN_DEAD] || 0) + pawns.filter(function (p) { return !p.hp[0]; }).length; //阵亡
        });
        this.contentNode_.Child('top/content/' + Enums_1.BattleStatistics.SUM_DAMAGE, cc.Label).string = '' + (infoMap[Enums_1.BattleStatistics.SUM_DAMAGE] || 0);
        this.contentNode_.Child('top/content/' + Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED, cc.Label).string = '' + (infoMap[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] || 0);
        this.contentNode_.Child('top/content/' + Enums_1.BattleStatistics.SUM_KILL, cc.Label).string = '' + (infoMap[Enums_1.BattleStatistics.SUM_KILL] || 0);
        this.contentNode_.Child('top/content/' + Enums_1.BattleStatistics.PAWN_DEAD, cc.Label).string = '' + (infoMap[Enums_1.BattleStatistics.PAWN_DEAD] || 0);
        var sv = this.contentNode_.Child('armys', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(list.length, function (it, i) {
            var data = list[i], battle = data.battle, pawns = battle.pawns, battleInfo = battle.battleInfo;
            it.Child('title/name', cc.Label).string = data.armyName;
            it.Child('title/time').setLocaleKey('ui.join_battle_time', ut.dateFormat('h:mm:ss', data.time));
            it.Child('content/pawns').Items(pawns, function (node, pawn) {
                var _a;
                var curHp = pawn.hp[0], maxHp = pawn.hp[1], isDie = curHp <= 0;
                var icon = node.Child('icon');
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(((_a = pawn.portrayal) === null || _a === void 0 ? void 0 : _a.id) || pawn.id, icon, _this.key);
                node.Child('die').active = isDie;
                icon.opacity = isDie ? 120 : 255;
                icon.Component(cc.Sprite).setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(!isDie));
                node.Child('lv', cc.Label).string = pawn.lv <= 1 ? '' : '' + pawn.lv;
                node.Child('hp/bar', cc.Sprite).fillRange = isDie ? 0 : curHp / maxHp;
            });
            var content = it.Child('content');
            content.Child(Enums_1.BattleStatistics.SUM_DAMAGE + '/val', cc.Label).string = '' + (battleInfo[Enums_1.BattleStatistics.SUM_DAMAGE] || 0);
            content.Child(Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED + '/val', cc.Label).string = '' + (battleInfo[Enums_1.BattleStatistics.HIT_DAMAGE_MITIGATED] || 0);
            content.Child(Enums_1.BattleStatistics.SUM_KILL + '/val', cc.Label).string = '' + (battleInfo[Enums_1.BattleStatistics.SUM_KILL] || 0);
            content.Child(Enums_1.BattleStatistics.HEAL_HP + '/val', cc.Label).string = '' + (battleInfo[Enums_1.BattleStatistics.HEAL_HP] || 0);
        });
    };
    BattleStatisticsPnlCtrl.prototype.onRemove = function () {
    };
    BattleStatisticsPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    BattleStatisticsPnlCtrl = __decorate([
        ccclass
    ], BattleStatisticsPnlCtrl);
    return BattleStatisticsPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BattleStatisticsPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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