
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/LoadProgressHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '90000ZlaT9PdKcl4banxScP', 'LoadProgressHelper');
// app/script/common/helper/LoadProgressHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadProgressHelper = void 0;
/**
 * 进度管理器
 */
var LoadProgress = /** @class */ (function () {
    function LoadProgress() {
        this._tasks = [];
        this._totalWeight = 0;
        this._progress = null;
        this._target = null;
    }
    LoadProgress.prototype.add = function (weight, runner) {
        this._totalWeight += weight;
        this._tasks.push({
            weight: weight,
            runners: [runner],
        });
        return this;
    };
    LoadProgress.prototype.spawn = function (weight, runners) {
        this._totalWeight += weight;
        this._tasks.push({
            weight: weight,
            runners: runners,
        });
        return this;
    };
    LoadProgress.prototype.run = function (progress, target) {
        return __awaiter(this, void 0, void 0, function () {
            var weight, _loop_1, this_1, i, l;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this._progress = progress;
                        this._target = target;
                        this.onProgress(0);
                        weight = 0;
                        _loop_1 = function (i, l) {
                            var task, percent;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        task = this_1._tasks[i];
                                        percent = 1;
                                        return [4 /*yield*/, Promise.all(task.runners.map(function (runner) { return __awaiter(_this, void 0, void 0, function () {
                                                var _this = this;
                                                return __generator(this, function (_a) {
                                                    switch (_a.label) {
                                                        case 0: return [4 /*yield*/, runner(function (p) {
                                                                if (p < percent) {
                                                                    percent = p;
                                                                    var w = weight + task.weight * p;
                                                                    _this.onProgress(w / _this._totalWeight);
                                                                }
                                                            })];
                                                        case 1:
                                                            _a.sent();
                                                            return [2 /*return*/];
                                                    }
                                                });
                                            }); }))];
                                    case 1:
                                        _a.sent();
                                        weight += task.weight;
                                        this_1.onProgress(weight / this_1._totalWeight);
                                        return [2 /*return*/];
                                }
                            });
                        };
                        this_1 = this;
                        i = 0, l = this._tasks.length;
                        _a.label = 1;
                    case 1:
                        if (!(i < l)) return [3 /*break*/, 4];
                        return [5 /*yield**/, _loop_1(i, l)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    LoadProgress.prototype.onProgress = function (percent) {
        if (!this._progress) {
        }
        else if (this._target) {
            this._progress.call(this._target, percent);
        }
        else {
            this._progress(percent);
        }
    };
    return LoadProgress;
}());
var LoadProgressHelper = /** @class */ (function () {
    function LoadProgressHelper() {
    }
    LoadProgressHelper.prototype.create = function () {
        return new LoadProgress();
    };
    return LoadProgressHelper;
}());
exports.loadProgressHelper = new LoadProgressHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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