
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/EventNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3f42cwkIORFJpHBxl/ecKsx', 'EventNotCtrl');
// app/script/view/notice/EventNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var VersionDesc_1 = require("../../common/constant/VersionDesc");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var DhHelper_1 = require("../../common/helper/DhHelper");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var SceneEffectCtrlHelper_1 = require("../../common/helper/SceneEffectCtrlHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var EventNotCtrl = /** @class */ (function (_super) {
    __extends(EventNotCtrl, _super);
    function EventNotCtrl() {
        //@autocode property begin
        //@end
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.world = null;
        _this.areaCenter = null;
        _this.message = null;
        _this.guide = null;
        _this.weakGuide = null;
        _this.noviceServer = null;
        _this.novice = null;
        _this.isDisconnect = false; //是否断开连接
        _this.isOpenGameUI = false;
        return _this;
    }
    EventNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return [
            (_a = {}, _a[mc.Event.WIND_ENTER] = this.onWindEnter, _a),
            (_b = {}, _b[mc.Event.PNL_ENTER] = this.onPnlEnter, _b),
            (_c = {}, _c[mc.Event.PNL_LEAVE] = this.onPnlLeave, _c),
            (_d = {}, _d[mc.Event.LANGUAGE_CHANGED] = this.onLanguageChanged, _d),
            (_e = {}, _e[EventType_1.default.CHANGE_SCREEN_UI] = this.onChangeScreenUI, _e),
            (_f = {}, _f[NetEvent_1.default.NET_DISCONNECT] = this.onNetDisconnect, _f),
            (_g = {}, _g[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _g),
            (_h = {}, _h[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _h.enter = true, _h),
        ];
    };
    EventNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.world = this.getModel('world');
                this.areaCenter = this.getModel('areaCenter');
                this.message = this.getModel('message');
                this.guide = this.getModel('guide');
                this.weakGuide = this.getModel('weak_guide');
                this.noviceServer = this.getModel('novice_server');
                this.novice = this.getModel('novice');
                this.FindChild('debug_be').active = logger.openPrint;
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://debug_be
    EventNotCtrl.prototype.onClickDebug = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('other/Clearlove');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 场景进入
    EventNotCtrl.prototype.onWindEnter = function (wind, prevKey) {
        // 场景切换的时候 把点击重置一下
        GameHelper_1.gameHpr.clickTouchId = -1;
        GameHelper_1.gameHpr.buildTouchId = -1;
        // 第一次进入主场景
        if (wind.key === 'novice' && !GameHelper_1.gameHpr.isEnterNovice) {
            this.initNovice();
        }
        else if (wind.key === 'lobby' && !GameHelper_1.gameHpr.isEnterLobby) {
            this.initLobby();
        }
        else if (wind.key === 'main' && !GameHelper_1.gameHpr.isEnterWorld) {
            var checkVersion = !GameHelper_1.gameHpr.isEnterLobby;
            if (checkVersion) { //如果还没进过大厅 这里初始化一下
                GameHelper_1.gameHpr.isEnterLobby = true;
                this.init(Enums_1.ReportErrorLevel.MAIN);
                this.initCommon();
                GameHelper_1.gameHpr.lobby.initBaseInfo();
            }
            this.initWorld(checkVersion);
        }
        else {
            this.changeWind(wind);
        }
        // 只要是进入登陆界面和大厅界面 就关闭游戏的运行
        if (wind.key === 'login' || wind.key === 'lobby') {
            GameHelper_1.gameHpr.isGameRuning = false;
            cc.log('isGameRuning', GameHelper_1.gameHpr.isGameRuning);
        }
        else if (!GameHelper_1.gameHpr.isGameRuning) {
            GameHelper_1.gameHpr.isGameRuning = true;
            cc.log('isGameRuning', GameHelper_1.gameHpr.isGameRuning);
        }
        // 打开UI界面
        this.onChangeScreenUI(wind.key === 'main' || wind.key === 'novice' || wind.key === 'area', !prevKey || prevKey === 'login' || prevKey === 'lobby');
        // 新手村上报
        if (GameHelper_1.gameHpr.isNoviceMode) {
            var cell = GameHelper_1.gameHpr.world.getLookCell();
            if (cell) {
                TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'scene_' + wind.key + '_' + cell.actPoint.Join('_'), uid: GameHelper_1.gameHpr.getUid() });
            }
            else {
                TaHelper_1.taHelper.trackNovice('ta_rookie_path', { path_id: 'scene_' + wind.key, uid: GameHelper_1.gameHpr.getUid() });
            }
        }
    };
    // 有UI打开
    EventNotCtrl.prototype.onPnlEnter = function (ui) {
        if (!GameHelper_1.gameHpr.isNoviceMode) {
        }
        else if (ui.key === 'common/GuideTask' || ui.key === 'build/BuildBarracks') {
            AnimHelper_1.animHelper.hideFinger('first_guide_task', true);
        }
    };
    // 有UI关闭了
    EventNotCtrl.prototype.onPnlLeave = function (ui) {
        PopupPnlHelper_1.popupPnlHelper.next(ui.key);
        if (!GameHelper_1.gameHpr.isNoviceMode) {
        }
        else if (ui.key === 'common/GuideTask' || ui.key === 'build/BuildBarracks') {
            AnimHelper_1.animHelper.hideFinger('first_guide_task', false);
        }
    };
    // 切换语言
    EventNotCtrl.prototype.onLanguageChanged = function (lang) {
        GameHelper_1.gameHpr.cleanUnlockBuildCondText();
        GameHelper_1.gameHpr.user.languageChange();
    };
    // 切换屏幕UI的显示和隐藏
    EventNotCtrl.prototype.onChangeScreenUI = function (val, init) {
        if (init === void 0) { init = false; }
        if (this.isOpenGameUI === val) {
            return;
        }
        this.isOpenGameUI = val;
        if (val) {
            ViewHelper_1.viewHelper.showPnl('common/UI', init);
            ViewHelper_1.viewHelper.showPnl('common/Top', init);
        }
        else {
            ViewHelper_1.viewHelper.hidePnl('common/UI');
            ViewHelper_1.viewHelper.hidePnl('common/UIMenuChild');
            ViewHelper_1.viewHelper.hidePnl('common/Top');
        }
    };
    EventNotCtrl.prototype.onNetDisconnect = function () {
        if (!GameHelper_1.gameHpr.isNoviceMode) {
            this.isDisconnect = true;
        }
    };
    EventNotCtrl.prototype.onNetReconnect = function () {
        this.isDisconnect = false;
        GameHelper_1.gameHpr.task.init();
        GameHelper_1.gameHpr.user.getMails(true);
        this.onNetListener();
        var key = mc.currWindName;
        if (key === 'main' || key === 'area') {
            this.checkGamePopupPnl();
        }
    };
    // 若引导
    EventNotCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'ui') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.node, '_ui_weak_finger_');
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    EventNotCtrl.prototype.init = function (errorLevel) {
        this.open();
        ErrorReportHelper_1.errorReportHelper.setCurLevel(errorLevel);
    };
    EventNotCtrl.prototype.initCommon = function () {
        // 初始化支付
        PayHelper_1.payHelper.init();
        // 获取任务
        GameHelper_1.gameHpr.task.init();
        // 首次进入游戏
        GameHelper_1.gameHpr.user.enter();
        // 检测获取卓航客服红点
        DhHelper_1.dhHelper.checkCSUnreadCommentNum();
        // 监听消息
        this.onNetListener();
    };
    // 监听
    EventNotCtrl.prototype.onNetListener = function () {
        var net = GameHelper_1.gameHpr.net;
        // 监听通知
        net.on('lobby/OnNotify', this.OnNotify, this);
        // 监听翻译
        net.on('lobby/OnTranslateText', this.OnTranslateText, this);
        // 踢出游戏
        net.on('game/OnKick', this.OnKick, this);
        // 服务器关闭
        net.on('game/OnGameClose', this.OnGameClose, this);
    };
    // 第一次进入新手村
    EventNotCtrl.prototype.initNovice = function () {
        GameHelper_1.gameHpr.isEnterNovice = true;
        this.init(Enums_1.ReportErrorLevel.NOVICE);
        this.weakGuide.init();
    };
    // 第一次进入大厅
    EventNotCtrl.prototype.initLobby = function () {
        GameHelper_1.gameHpr.isEnterLobby = true;
        this.init(Enums_1.ReportErrorLevel.LOBBY);
        this.initCommon();
        this.checkNoticePopupPnl();
        if (!storageMgr.loadBool('first_enter_lobby')) {
            TaHelper_1.taHelper.track('ta_tutorial_v2', { tutorial_step: 'first_enter_lobby', uid: GameHelper_1.gameHpr.getUid() }); //第一次大厅
            storageMgr.saveBool('first_enter_lobby', true);
        }
    };
    // 第一次进入游戏
    EventNotCtrl.prototype.initWorld = function (checkVersion) {
        GameHelper_1.gameHpr.isEnterWorld = true;
        SceneEffectCtrlHelper_1.sceneEffectCtrlHelper.init();
        // 初始化若引导
        this.weakGuide.init();
        // 是否第一次创建主城
        if (GameHelper_1.gameHpr.user.isNewbie() && !storageMgr.loadBool('first_enter_world')) {
            TaHelper_1.taHelper.track('ta_tutorial_v2', { tutorial_step: 'first_enter_main', uid: GameHelper_1.gameHpr.getUid() }); //第一次进入主场景
            storageMgr.saveBool('first_enter_world', true);
            GameHelper_1.gameHpr.noviceServer.cleanSaveData();
            // popupPnlHelper.add({ key: 'main/FirstEnter' })
            GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.PUSH);
        }
        else {
            checkVersion && this.checkNoticePopupPnl();
            this.checkGamePopupPnl();
        }
    };
    // 检测是否有公告
    EventNotCtrl.prototype.checkNoticePopupPnl = function () {
        var _a;
        var newVersion = GameHelper_1.gameHpr.gameNoticeVersion;
        if (newVersion) {
            var version = (_a = storageMgr.loadNumber('notice_version')) !== null && _a !== void 0 ? _a : 0;
            if (version !== newVersion) {
                storageMgr.saveNumber('notice_version', newVersion);
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'common/Notice' });
            }
        }
    };
    // 检测版本更新内容
    EventNotCtrl.prototype.checkVersionPopupPnl = function () {
        var _a;
        var newVersion = (_a = VersionDesc_1.versionDesc[0]) === null || _a === void 0 ? void 0 : _a.version;
        if (newVersion) {
            var version = storageMgr.loadString('last_update_version') || '';
            if (version !== newVersion) {
                storageMgr.saveString('last_update_version', newVersion);
                if (version) { //如果之前没有 就不弹了
                    PopupPnlHelper_1.popupPnlHelper.add({ key: 'common/VersionDesc' });
                }
            }
        }
    };
    // 检测游戏内的弹窗
    EventNotCtrl.prototype.checkGamePopupPnl = function () {
        var _a;
        if (GameHelper_1.gameHpr.isSpectate()) {
            return;
        }
        else if (this.world.isGameOver()) { //检测是否游戏结束
            PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/GameOver' });
        }
        else if (GameHelper_1.gameHpr.player.isCapture()) { //是否被沦陷
            if (!this.world.isKarmicMahjong()) {
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/CaptureTip' }); //不是血战到底 直接弹沦陷
            }
            else if (GameHelper_1.gameHpr.alliance.isMeCreater() || ((_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.isSettled)) {
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/GameOver' }); //是盟主或者已经结算 直接弹结算界面
            }
            else {
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/CaptureTip' });
            }
        }
    };
    // 切换场景
    EventNotCtrl.prototype.changeWind = function (wind) {
        this.guide.changeWind(wind);
        if (wind.key === 'main') {
            if (GameHelper_1.gameHpr.user.checkTriggerPraiseTip()) { //检测是否触发好评弹窗
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/Praise' });
            }
        }
    };
    // 通知
    EventNotCtrl.prototype.OnNotify = function (data) {
        var _this = this;
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return;
        }
        cc.log('OnNotify', data);
        data === null || data === void 0 ? void 0 : data.list.forEach(function (m) {
            var data = m['data_' + m.type];
            if (m.type === Enums_1.LobbyNotifyType.NQ_SYS_MSG) { //系统消息
                _this.emit(EventType_1.default.SYS_MSG_NOTICE, data);
            }
            else if (m.type === Enums_1.LobbyNotifyType.NQ_SYS_NOTICE) { //系统公告
                _this.emit(EventType_1.default.GAME_NOTICE, data);
            }
            else if (m.type === Enums_1.LobbyNotifyType.NQ_USER_TRUMPET) { //用户喇叭
                data.trumpet = true;
                _this.emit(EventType_1.default.GAME_NOTICE, data);
            }
        });
    };
    // 翻译返回
    EventNotCtrl.prototype.OnTranslateText = function (data) {
        var _a = __read(data.uid.split('_'), 2), type = _a[0], uid = _a[1];
        var info = null;
        if (type === 'chat') {
            info = GameHelper_1.gameHpr.chat.getChatInfoByUID(uid);
        }
        else if (type === 'friend') {
            info = GameHelper_1.gameHpr.friend.getChatInfoByUID(uid);
        }
        else if (type === 'mail') {
            info = GameHelper_1.gameHpr.user.getTempMails().find(function (m) { return m.uid === uid; });
        }
        else if (type === 'lobby') {
            info = GameHelper_1.gameHpr.lobby.getChatInfoByUID(uid);
        }
        if (info) {
            info.translate = { text: data.text || info.content };
            this.emit(EventType_1.default.TRANSLATE_TEXT_COMPLETE, type, info);
        }
    };
    // 被踢了
    EventNotCtrl.prototype.OnKick = function (data) {
        GameHelper_1.gameHpr.net.setKick();
        if (!data) {
        }
        else if (data.type === 0) { //挤号
            ViewHelper_1.viewHelper.showMessageBox('ui.kick_msg', {
                lockClose: true,
                okText: 'ui.button_relogin',
                cancelText: 'ui.button_exit_game',
                cancel: function () { return GameHelper_1.gameHpr.exitGame(); },
                ok: function () { return ViewHelper_1.viewHelper.gotoWind('login'); },
            });
        }
        else if (data.type === 1) { //封停
            ViewHelper_1.viewHelper.showPnl('login/BanAccountTimeTip', 0, (data === null || data === void 0 ? void 0 : data.banType) || 0);
        }
        else if (data.type === 2) { //被踢掉
            ViewHelper_1.viewHelper.showMessageBox('ui.force_kick_msg', {
                lockClose: true,
                clickButtonClose: false,
                ok: function () { return GameHelper_1.gameHpr.exitGame(); },
            });
        }
    };
    // 游戏关闭
    EventNotCtrl.prototype.OnGameClose = function (data) {
        GameHelper_1.gameHpr.net.setKick();
        var key = (data === null || data === void 0 ? void 0 : data.isClose) ? 'ui.server_close_tip' : 'ui.server_maintain_tip';
        ViewHelper_1.viewHelper.showMessageBox(key, {
            lockClose: true,
            okText: 'ui.button_okay',
            ok: function () {
                if (data === null || data === void 0 ? void 0 : data.isClose) {
                    ViewHelper_1.viewHelper.gotoWind('login');
                }
                else {
                    GameHelper_1.gameHpr.gameRestart();
                }
            }
        });
    };
    EventNotCtrl.prototype.update = function (dt) {
        if (GameHelper_1.gameHpr.net.isKick()) {
            return;
        }
        ReddotHelper_1.reddotHelper.update(dt);
        if (this.isDisconnect) {
            return;
        }
        this.guide.update(dt);
        // 下面是游戏场景相关
        if (!GameHelper_1.gameHpr.isGameRuning) {
            return;
        }
        else if (GameHelper_1.gameHpr.isNoviceMode) {
            this.noviceServer.update(dt);
            this.novice.update(dt);
        }
        else {
            this.world.update(dt);
        }
        this.message.update(dt);
        this.areaCenter.update(dt);
        this.weakGuide.update(dt);
        SceneEffectCtrlHelper_1.sceneEffectCtrlHelper.update(dt);
        AnimHelper_1.animHelper.update(dt);
    };
    EventNotCtrl = __decorate([
        ccclass
    ], EventNotCtrl);
    return EventNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = EventNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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