
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendSprite.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0ef00uyA4pDK4BsdgnbvPEj', 'ExtendSprite');
// app/core/extend/ExtendSprite.ts

/**
 * Sprite扩展方法
 */
cc.Sprite.prototype.setLocaleKey = function (key) {
    var locale = this.Component(cc.LocaleSprite);
    if (locale) {
        locale.setKey(key);
    }
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxleHRlbmRcXEV4dGVuZFNwcml0ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDQTs7R0FFRztBQUVILEVBQUUsQ0FBQyxNQUFNLENBQUMsU0FBUyxDQUFDLFlBQVksR0FBRyxVQUFVLEdBQVc7SUFDcEQsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsWUFBWSxDQUFDLENBQUE7SUFDOUMsSUFBSSxNQUFNLEVBQUU7UUFDUixNQUFNLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0tBQ3JCO0FBQ0wsQ0FBQyxDQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8qKlxyXG4gKiBTcHJpdGXmianlsZXmlrnms5VcclxuICovXHJcblxyXG5jYy5TcHJpdGUucHJvdG90eXBlLnNldExvY2FsZUtleSA9IGZ1bmN0aW9uIChrZXk6IHN0cmluZykge1xyXG4gICAgY29uc3QgbG9jYWxlID0gdGhpcy5Db21wb25lbnQoY2MuTG9jYWxlU3ByaXRlKVxyXG4gICAgaWYgKGxvY2FsZSkge1xyXG4gICAgICAgIGxvY2FsZS5zZXRLZXkoa2V5KVxyXG4gICAgfVxyXG59Il19