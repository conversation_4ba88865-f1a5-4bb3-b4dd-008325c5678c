{"version": 3, "sources": ["assets\\app\\script\\view\\area\\BattleRulePnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAQ,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA+C,qCAAc;IAA7D;;IA6BA,CAAC;IA3BG,0BAA0B;IAC1B,MAAM;IAEC,2CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,oCAAQ,GAArB;;;;;;KACC;IAEM,mCAAO,GAAd;IAEA,CAAC;IAEM,oCAAQ,GAAf;IACA,CAAC;IAEM,mCAAO,GAAd;IACA,CAAC;IApBgB,iBAAiB;QADrC,OAAO;OACa,iBAAiB,CA6BrC;IAAD,wBAAC;CA7BD,AA6BC,CA7B8C,EAAE,CAAC,WAAW,GA6B5D;kBA7BoB,iBAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass } = cc._decorator;\r\n\r\n@ccclass\r\nexport default class BattleRulePnlCtrl extends mc.BasePnlCtrl {\r\n\r\n    //@autocode property begin\r\n    //@end\r\n\r\n    public listenEventMaps() {\r\n        return []\r\n    }\r\n\r\n    public async onCreate() {\r\n    }\r\n\r\n    public onEnter() {\r\n\r\n    }\r\n\r\n    public onRemove() {\r\n    }\r\n\r\n    public onClean() {\r\n    }\r\n\r\n    // ----------------------------------------- button listener function -------------------------------------------\r\n    //@autocode button listener\r\n    //@end\r\n    // ----------------------------------------- event listener function --------------------------------------------\r\n\r\n    // ----------------------------------------- custom function ----------------------------------------------------\r\n\r\n}\r\n"]}