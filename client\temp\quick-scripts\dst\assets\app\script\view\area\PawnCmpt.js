
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/PawnCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '79c8fQaFpRErq1Em9pLIvcb', 'PawnCmpt');
// app/script/view/area/PawnCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WxHelper_1 = require("../../common/helper/WxHelper");
var OutlineShaderCtrl_1 = require("../../common/shader/OutlineShaderCtrl");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var FrameAnimationCmpt_1 = require("../cmpt/FrameAnimationCmpt");
var HPBarCmpt_1 = require("./HPBarCmpt");
var PawnAnimConf_1 = require("./PawnAnimConf");
var PawnAnimationCmpt_1 = require("./PawnAnimationCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 士兵
var PawnCmpt = /** @class */ (function (_super) {
    __extends(PawnCmpt, _super);
    function PawnCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.data = null;
        _this.body = null;
        _this.curSkinId = 0;
        _this.curPortrayalId = 0;
        _this.animNode = null;
        _this.animCmpt = null;
        _this.touchCmpt = null;
        _this.hpBar = null;
        _this.animNodeInitY = 0;
        _this.origin = cc.v2(); //起点
        _this.originY = 0;
        _this.tempBodyPosition = cc.v2();
        _this.prePoint = cc.v2();
        _this.prePositionY = -1;
        _this.preAnger = -1;
        _this.curShieldValue = -1; //当前护盾值
        _this.preShieldValue = -1;
        _this.preActioning = false;
        _this.preStateUid = '';
        _this.preAnimName = '';
        _this.currAnimName = '';
        _this.isDie = false; //是否在视图层死亡
        _this.isShowStandShield = false; //是否显示立盾
        _this.isShowBuffMap = {}; //外显buff
        _this.mutualBuffType = 0; //当前显示的互斥buff
        _this.isLoadBuffMap = {};
        _this.buffNodes = []; //buff节点列表
        _this.buffIconCmpt = null; //buff图标容器
        _this.isDiaup = false;
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_position = cc.v2();
        _this.tempIndex = 0;
        return _this;
    }
    PawnCmpt.prototype.init = function (data, origin, key) {
        var _this = this;
        this.reset(data.uid);
        this.data = data;
        this.key = key;
        this.curSkinId = data.skinId;
        this.curPortrayalId = data.getPortrayalId();
        this.origin.set(origin);
        this.originY = origin.y * Constant_1.TILE_SIZE;
        this.body = this.FindChild('body');
        this.body.getPosition(this.tempBodyPosition);
        this.updatePosition(true);
        this.updateZIndex();
        this.updateAnger();
        this.updateShieldValue();
        this.preStateUid = '';
        this.animNode = this.FindChild('body/anim');
        this.animCmpt = this.node.addComponent(PawnAnimationCmpt_1.default).init(this.animNode.getComponent(cc.Sprite), data.getViewId(), key);
        if (this.animNodeInitY === 0) {
            this.animNodeInitY = this.animNode.y;
        }
        this.animNode.scale = 1;
        this.animNode.y = this.animNodeInitY;
        // 设置体型
        this.updateAnimScale(1 + data.getBuffValue(Enums_1.BuffType.FEED_INTENSIFY) * 0.02);
        // 非战斗单位不用加点击
        var isNoncombat = data.isNoncombat();
        if (!isNoncombat) {
            this.touchCmpt = this.FindChild('touch').addComponent(ClickTouchCmpt_1.default).on(this.onClick, this); /* .setPlayAction(true).setTarget(this.body) */
        }
        if (data.isBuilding() || isNoncombat) {
            // 秦良玉的矛 特殊处理下
            this.body.scaleX = data.id === Constant_1.SPEAR_PAWN_ID ? data.enterDir : 1;
            if (data.id !== Constant_1.FIRE_PAWN_ID || data.enterDir === 2) {
                this.playAnimation('create', function () {
                    if (_this.isValid) {
                        _this.loadHPBar();
                        _this.playAnimation('idle');
                    }
                });
                return this;
            }
        }
        else if (data.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) {
            this.loadHPBar();
            this.playAnimation('stand_shield'); //如果有立盾
            return this;
        }
        this.loadHPBar();
        this.playAnimation('idle');
        return this;
    };
    PawnCmpt.prototype.reset = function (uid) {
        this.stopSFXByKey('move_sound', uid);
        this.putAllBuff();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
    };
    PawnCmpt.prototype.putAllBuff = function () {
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            this.putBuff(Number(k));
        }
        this.putBuff(this.mutualBuffType);
    };
    // 重新同步一下信息
    PawnCmpt.prototype.resync = function (data, jump) {
        var _a, _b, _c, _d, _e;
        if (jump === void 0) { jump = false; }
        this.reset(data.uid);
        // this.animCmpt?.stop()
        (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.resetMove();
        var point = this.getActPoint();
        this.data = data;
        if (!jump && point && !data.isBattleing() && data.aIndex >= 0 && !data.point.equals(point)) {
            this.prePoint.set(data.point);
            this.updatePositionForMove(point, data.point.clone());
        }
        else {
            this.prePoint.set2(-1, -1);
            this.updatePosition();
            this.updateZIndex();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(data);
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.updateAnger(data.getAngerRatio());
        // this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)
        this.showArmyName(GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_NAME));
        this.showPawnEquip(this.isCanShowPawnEquip());
        this.setCanClick(true);
        this.preStateUid = '';
        (_d = this.animCmpt) === null || _d === void 0 ? void 0 : _d.initModel();
        // 秦良玉没矛状态
        if (((_e = data.getPortrayalSkill()) === null || _e === void 0 ? void 0 : _e.id) === Enums_1.HeroType.QIN_LIANGYU && data.getState() === Enums_1.PawnState.STAND) {
            if (data.isHasBuff(Enums_1.BuffType.BARB)) {
                this.playAnimation('idle_barb');
            }
            else {
                this.playAnimation('idle');
            }
        }
        return this;
    };
    PawnCmpt.prototype.clean = function (release) {
        var _a, _b, _c, _d, _e, _f;
        if (!this.isValid || !this.node) {
            return cc.error('clean error?');
        }
        this.unscheduleAllCallbacks();
        this.stopSFXByKey('move_sound', this.uid);
        this.node.stopAllActions();
        (_a = this.touchCmpt) === null || _a === void 0 ? void 0 : _a.clean();
        (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.clean();
        this.isShowStandShield = false;
        this.isShowBuffMap = {};
        this.mutualBuffType = 0;
        this.isLoadBuffMap = {};
        nodePoolMgr.put((_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.node);
        this.hpBar = null;
        this.buffNodes.forEach(function (m) { return nodePoolMgr.put(m); });
        this.buffNodes.length = 0;
        (_d = this.buffIconCmpt) === null || _d === void 0 ? void 0 : _d.clean();
        nodePoolMgr.put((_e = this.buffIconCmpt) === null || _e === void 0 ? void 0 : _e.node);
        this.buffIconCmpt = null;
        this.node.destroy();
        release && assetsMgr.releaseTempRes((_f = this.data) === null || _f === void 0 ? void 0 : _f.getPrefabUrl(), this.key);
        this.data = null;
    };
    Object.defineProperty(PawnCmpt.prototype, "id", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "cuid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.cuid; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnCmpt.prototype, "point", {
        get: function () { return this.data.point; },
        enumerable: false,
        configurable: true
    });
    PawnCmpt.prototype.getBody = function () { return this.body; };
    PawnCmpt.prototype.getTempPosition = function () { return this.getPosition(this._temp_position); };
    PawnCmpt.prototype.getAbsUid = function () { return this.uid + (this.curPortrayalId || this.curSkinId || this.id); };
    PawnCmpt.prototype.getActPoint = function () {
        return this.getActPointByPixel(this.getTempPosition());
    };
    // 根据像素点获取网格点
    PawnCmpt.prototype.getActPointByPixel = function (pixel) {
        return MapHelper_1.mapHelper.getPointByPixel(pixel, this._temp_vec2_2).subSelf(this.origin);
    };
    // 根据网格点获取像素点
    PawnCmpt.prototype.getActPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.origin, this._temp_vec2_1), this._temp_vec2_1);
    };
    // 播放化身
    PawnCmpt.prototype.playAvatarHeroAnim = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var it, anim;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, nodePoolMgr.get('pawn/AVATAR_HERO', this.key)];
                    case 1:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 20;
                        it.active = true;
                        anim = it.Child('val', FrameAnimationCmpt_1.default);
                        return [4 /*yield*/, anim.init('avatar_' + id, this.key)];
                    case 2:
                        _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        anim.play('avatar');
                        this.playSFX('sound_074');
                        ut.wait(2.42).then(function () { return nodePoolMgr.put(it); });
                        return [4 /*yield*/, ut.wait(1.98)];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.playAnimation = function (name, cb, startTime, intervalMul) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f;
        if (name === 'move' && this.data.getState() === Enums_1.PawnState.MOVE) {
            this.playSFXByKey('move_sound', '', { loop: true, tag: this.uid });
        }
        else {
            this.fadeOutSFXByKey('move_sound', this.uid);
        }
        if (this.isDiaup && name !== 'idle') {
            this.isDiaup = false;
        }
        if (name !== 'die' && name !== 'stand_shield' && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isHasBuff(Enums_1.BuffType.STAND_SHIELD))) {
            this.currAnimName = name;
            if (name !== 'hit') {
            }
            else if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) { //曹仁需要播放受击动画
                (_d = this.animCmpt) === null || _d === void 0 ? void 0 : _d.play('stand_hit', function () { return _this.isValid && _this.playAnimation('stand_shield'); }, startTime);
            }
            else {
                ut.wait(0.5, this).then(function () {
                    var _a, _b, _c;
                    if (_this.isValid && ((_b = (_a = _this.data) === null || _a === void 0 ? void 0 : _a.state) === null || _b === void 0 ? void 0 : _b.type) === Enums_1.PawnState.HIT) {
                        _this.data.changeState(Enums_1.PawnState.STAND);
                        _this.currAnimName = ((_c = _this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.STAND_SHIELD)) ? 'stand_shield' : 'idle';
                    }
                });
            }
            return; //如果是立盾状态就不播放其他动画了
        }
        else if (((_e = this.animCmpt) === null || _e === void 0 ? void 0 : _e.playAnimName) === 'create' && name !== 'die') {
            return cb && cb(); //如果还在播放创建 其他动画就不要播放了 死亡还是可以的
        }
        if (this.isPullStringState(name)) {
            name = name + '_pull'; //检测是否有拉弦状态
        }
        else if (this.isBarbState(name)) {
            name = name + '_barb'; //检测秦良玉手上是否有矛
        }
        if (this.data.id === Constant_1.FIRE_PAWN_ID && name === 'idle') {
            name = 'fire_' + this.data.lv; //火
        }
        this.currAnimName = name;
        (_f = this.animCmpt) === null || _f === void 0 ? void 0 : _f.play(name, cb, startTime);
    };
    // 播放音效
    PawnCmpt.prototype.playSFXByKey = function (key, suffix, opts) {
        if (suffix === void 0) { suffix = ''; }
        if (this.data.isHero()) {
            var prev = this.data.portrayal.json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        else if (this.data.skinId > 0) {
            var json = assetsMgr.getJsonData('pawnSkin', this.data.skinId);
            var prev = json === null || json === void 0 ? void 0 : json[key];
            if (prev) {
                return this.playSFX(prev + suffix, opts);
            }
        }
        if (this.data.baseJson) {
            var url = this.data.baseJson[key];
            if (url) {
                this.playSFX(url + suffix, opts);
            }
        }
    };
    // 播放音效
    PawnCmpt.prototype.playSFX = function (url, opts) {
        var _a;
        if (!url) {
            return;
        }
        url = (_a = PawnAnimConf_1.PAWN_SOUND_CONF_TRANSITION[url]) !== null && _a !== void 0 ? _a : url;
        audioMgr.playSFX('pawn/' + url, opts);
    };
    PawnCmpt.prototype.stopSFX = function (url, tag) {
        if (!url) {
            return;
        }
        audioMgr.stopSFX('pawn/' + url, tag);
    };
    PawnCmpt.prototype.stopSFXByKey = function (key, tag) {
        if (!this.data) {
            return;
        }
        var url = null;
        if (this.data.isHero()) {
            url = this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = this.data.baseJson[key];
        }
        this.stopSFX(url, tag);
    };
    PawnCmpt.prototype.fadeOutSFXByKey = function (key, tag) {
        var url = null;
        if (this.data.isHero()) {
            url = 'pawn/' + this.data.portrayal.json[key];
        }
        else if (this.data.baseJson) {
            url = 'pawn/' + this.data.baseJson[key];
        }
        if (url) {
            audioMgr.fadeOutSFX(0.15, url, tag);
        }
    };
    // 是否拉弦状态
    PawnCmpt.prototype.isPullStringState = function (name) {
        var _a;
        if (((_a = this.data) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.PAWN_CROSSBOW_ID) {
            return false;
        }
        else if (name !== 'idle' && name !== 'move' && name !== 'hit' && name !== 'diaup' && name !== 'die') {
            return false;
        }
        return !!this.data.getSkillByType(Enums_1.PawnSkillType.PULL_STRING) && this.data.getCurAnger() !== 0;
    };
    // 是否无矛状态
    PawnCmpt.prototype.isBarbState = function (name) {
        var _a, _b, _c;
        if (((_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) !== Enums_1.HeroType.QIN_LIANGYU || !((_c = this.data) === null || _c === void 0 ? void 0 : _c.isHasBuff(Enums_1.BuffType.BARB))) {
            return false;
        }
        return name === 'idle' || name === 'hit' || name === 'diaup';
    };
    // 设置方向
    PawnCmpt.prototype.setDir = function (dir) {
        if (this.data.isBuilding()) {
            this.body.scaleX = 1; //建筑不需要翻转
        }
        else if (dir !== 0) {
            this.body.scaleX = ut.normalizeNumber(dir);
        }
    };
    // 加载血条
    PawnCmpt.prototype.loadHPBar = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var it;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.data.id === Constant_1.FIRE_PAWN_ID) {
                            return [2 /*return*/, (_a = this.body.Child('bar')) === null || _a === void 0 ? void 0 : _a.Color(GameHelper_1.gameHpr.getBattleFireBarColor(this.data))];
                        }
                        else if (this.data.maxHp === 0 || this.data.isNoncombat()) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, nodePoolMgr.get(this.data.getHPBarPrefabUrl(), this.key)];
                    case 1:
                        it = _b.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        it.parent = this.node;
                        it.zIndex = 10;
                        it.active = true;
                        if (this.data.isBoss()) {
                            it.setPosition(0, 113);
                        }
                        else {
                            it.setPosition(0, this.data.isHero() ? 56 : 46);
                        }
                        this.hpBar = it.addComponent(HPBarCmpt_1.default).init(this.data);
                        this.hpBar.updateAnger(this.data.getAngerRatio());
                        this.hpBar.updateShieldValue(this.data.getShieldValue(), this.data.curHp, this.data.getMaxHp());
                        // this.showPawnLv(gameHpr.user.getLocalPreferenceData(PreferenceKey.SHOW_PAWN_LV) ?? 1)
                        this.showArmyName(GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_NAME));
                        this.showPawnEquip(this.isCanShowPawnEquip());
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.showPawnLv = function (val) {
        if (this.hpBar) {
            this.hpBar.Child('root').opacity = val ? 255 : 0;
            var armyNameLbl = this.hpBar.Child('army_name', cc.Label);
            if (armyNameLbl) {
                armyNameLbl.string = (val === 1 && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : '';
            }
            this.hpBar.Child('lv', cc.Label).string = (val === 1 && this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : '';
        }
    };
    // 显示军队的名字
    PawnCmpt.prototype.showArmyName = function (val) {
        if (this.hpBar) {
            var armyNameLbl = this.hpBar.Child('army_name', cc.Label);
            if (armyNameLbl) {
                armyNameLbl.string = (val && this.data.armyName) ? ut.nameFormator(this.data.armyName, 4) : '';
            }
            this.hpBar.Child('lv', cc.Label).string = (this.data.lv && !this.data.isMachine() && !this.data.isBuilding()) ? this.data.lv + '' : '';
        }
    };
    // 是否可以显示装备信息 这里如果还没有铁匠铺就不会显示
    PawnCmpt.prototype.isCanShowPawnEquip = function () {
        var player = GameHelper_1.gameHpr.player;
        return !!GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP) && (player.isCapture() || player.getMainBuilds().some(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID && m.lv >= 1; }));
    };
    // 是否可以显示没有装备的提示
    PawnCmpt.prototype.isCanShowNotEquipTip = function () {
        return !this.data.equip.id && this.data.isCanWearEquip() && this.data.isOwner() && GameHelper_1.gameHpr.player.getEquips().length > 0;
    };
    // 显示装备信息
    PawnCmpt.prototype.showPawnEquip = function (val) {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
        }
        else if (this.hpBar.Child('equip').active = val && this.data.isCanWearEquip()) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
    };
    PawnCmpt.prototype.updateShowPawnEquip = function () {
        var _a, _b;
        if (!this.hpBar || !this.hpBar.Child('equip')) {
            return;
        }
        else if (this.hpBar.Child('equip').active) {
            this.updateShowPawnEquipInfo();
            (_a = this.hpBar.Child('not_equip')) === null || _a === void 0 ? void 0 : _a.setActive(false);
        }
        else {
            (_b = this.hpBar.Child('not_equip')) === null || _b === void 0 ? void 0 : _b.setActive(this.isCanShowNotEquipTip());
        }
        this.hpBar.initInfo();
    };
    PawnCmpt.prototype.updateShowPawnEquipInfo = function () {
        var _a, _b;
        this.hpBar.Child('equip/add').active = !((_a = this.data.equip) === null || _a === void 0 ? void 0 : _a.id) && this.data.isOwner();
        var spr = this.hpBar.Child('equip/val', cc.Sprite);
        if ((_b = this.data.equip) === null || _b === void 0 ? void 0 : _b.id) {
            ResHelper_1.resHelper.loadEquipIcon(this.data.equip.id, spr, this.key, this.data.equip.getSmeltCount());
        }
        else {
            spr.spriteFrame = null;
        }
    };
    PawnCmpt.prototype.showOutline = function (v) {
        var outline = this.getComponent(OutlineShaderCtrl_1.default);
        if (!outline) {
            outline = this.addComponent(OutlineShaderCtrl_1.default);
            outline.setTarget(outline.FindChild('body/anim').Component(cc.Sprite));
            outline.setOutlineSize(4);
            outline.setColor(cc.Color.WHITE);
        }
        outline.setVisible(v);
    };
    // 被点击了
    PawnCmpt.prototype.onClick = function () {
        if (this.data) {
            audioMgr.playSFX('click');
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.data);
            // this.showOutline(true)
        }
    };
    // 设置是否可以点击
    PawnCmpt.prototype.setCanClick = function (val) {
        if (this.touchCmpt) {
            this.touchCmpt.interactable = val;
        }
    };
    // 移动位置
    PawnCmpt.prototype.movePoint = function (point) {
        this.node.setPosition(this.getActPixelByPoint(point));
    };
    // 取消编辑
    PawnCmpt.prototype.cancel = function () {
        this.node.setPosition(this.getActPixelByPoint(this.data.point));
    };
    // 确认编辑
    PawnCmpt.prototype.confirm = function () {
        var point = this.getActPoint();
        this.point.set(point);
        this.prePoint.set(point);
    };
    PawnCmpt.prototype.update = function (dt) {
        if (!this.data) {
            return;
        }
        this.updateZIndex();
        this.updateAnger();
        this.updateBuff();
        this.updateShieldValue();
        this.updateState();
        this.updateCheckDie();
    };
    // 同步zindex
    PawnCmpt.prototype.updateZIndex = function () {
        var _a, _b;
        if (this.prePositionY !== this.node.y || (this.preActioning !== !!((_a = this.data) === null || _a === void 0 ? void 0 : _a.actioning) || this.preAnimName !== this.currAnimName)) {
            this.prePositionY = this.node.y;
            this.preActioning = !!((_b = this.data) === null || _b === void 0 ? void 0 : _b.actioning);
            this.preAnimName = this.currAnimName;
            var y = this.prePositionY - this.originY;
            var add = 0;
            if (this.data.getPawnType() === Enums_1.PawnType.NONCOMBAT) {
                add = 3; //非战斗单位在最上层
            }
            else if (this.preActioning) {
                var state = this.data.getState();
                add = state === Enums_1.PawnState.ATTACK || state >= Enums_1.PawnState.SKILL ? 2 : 1;
            }
            else if (this.preAnimName === 'die') {
                return;
            }
            else {
                add = !this.preAnimName || this.preAnimName === 'create' || this.preAnimName === 'stand_shield' || this.preAnimName === 'idle' || this.preAnimName === 'idle_pull' ? 0 : 1;
            }
            var index = (Constant_1.AREA_MAX_ZINDEX - y) * 10 + add;
            if (index !== this.node.zIndex) {
                this.tempIndex = this.node.zIndex = index;
            }
            // cc.log('updateZIndex', this.data.id, this.data.uid, this.preActioning, this.preAnimName, add)
        }
    };
    // 同步位置
    PawnCmpt.prototype.updatePosition = function (init) {
        if (init || !this.prePoint.equals(this.data.point)) {
            this.prePoint.set(this.data.point);
            this.node.setPosition(this.getActPixelByPoint(this.data.point));
        }
    };
    PawnCmpt.prototype.updatePositionForMove = function (sp, ep) {
        return __awaiter(this, void 0, void 0, function () {
            var data, points, area, time;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        data = this.data;
                        points = [sp, ep];
                        area = GameHelper_1.gameHpr.areaCenter.getArea(data.index);
                        if (!area) return [3 /*break*/, 2];
                        return [4 /*yield*/, GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y); }).search(sp, ep)];
                    case 1:
                        points = _a.sent();
                        _a.label = 2;
                    case 2:
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 3:
                        _a.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新怒气
    PawnCmpt.prototype.updateAnger = function () {
        var _a;
        if (this.preAnger !== this.data.curAnger) {
            this.preAnger = this.data.curAnger;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateAnger(this.data.getAngerRatio());
        }
    };
    // 刷新白盾
    PawnCmpt.prototype.updateShieldValue = function () {
        var _a;
        if (this.preShieldValue !== this.curShieldValue) {
            this.preShieldValue = this.curShieldValue;
            (_a = this.hpBar) === null || _a === void 0 ? void 0 : _a.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 刷新buff效果
    PawnCmpt.prototype.updateBuff = function () {
        var _this = this;
        this.curShieldValue = 0;
        var showBuffTypeMap = {}, mutualBuff = 0, standShield = false, feedIntensifyValue = 0;
        this.data.buffs.forEach(function (m) {
            if (Constant_1.SHIELD_BUFF[m.type]) { //记录护盾值
                _this.curShieldValue += m.value;
            }
            if (Constant_1.NEED_SHOW_BUFF[m.type]) {
                showBuffTypeMap[m.type] = true;
            }
            else if (Constant_1.NEED_MUTUAL_BUFF[m.type]) {
                mutualBuff = m.type; //互斥buff
            }
            else if (m.type === Enums_1.BuffType.STAND_SHIELD) { //立盾
                standShield = true;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //投喂强化
                feedIntensifyValue = m.value;
            }
        });
        // 刷新外显buff
        for (var k in Constant_1.NEED_SHOW_BUFF) {
            var type = Number(k);
            this.updateShowBuff(type, showBuffTypeMap[type]);
        }
        // 刷新互斥buff
        this.updateMutualBuff(mutualBuff);
        // 刷新立盾
        this.updateStandShield(standShield);
        // 体型
        this.updateAnimScale(1 + feedIntensifyValue * 0.02);
    };
    // 刷新立盾
    PawnCmpt.prototype.updateStandShield = function (val) {
        if (!this.isShowStandShield && val) {
            this.isShowStandShield = true;
            this.playAnimation('stand_shield');
        }
        else if (this.isShowStandShield && !val) {
            this.isShowStandShield = false;
            if (this.currAnimName !== 'shield_end') {
                this.playAnimation('idle');
            }
        }
    };
    // 刷新外显buff
    PawnCmpt.prototype.updateShowBuff = function (type, val) {
        if (this.isLoadBuffMap[type]) {
        }
        else if (!this.isShowBuffMap[type] && val) {
            this.showBuff(type);
            this.isShowBuffMap[type] = true;
        }
        else if (this.isShowBuffMap[type] && !val) {
            this.isShowBuffMap[type] = false;
            this.putBuff(type);
        }
    };
    // 刷新互斥buff效果 就是只会显示一个效果
    PawnCmpt.prototype.updateMutualBuff = function (type) {
        if (this.mutualBuffType === type) {
            return;
        }
        else if (this.mutualBuffType) {
            this.putBuff(this.mutualBuffType);
            this.mutualBuffType = 0;
        }
        if (type && !this.isLoadBuffMap[type]) {
            this.mutualBuffType = type;
            this.showBuff(type);
        }
    };
    // 显示一个buff
    PawnCmpt.prototype.showBuff = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var showType, name, it, cmpt;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
                        name = 'BUFF_' + showType;
                        it = this.buffNodes.find(function (m) { return m.name === name; });
                        if (!it) return [3 /*break*/, 1];
                        this.isLoadBuffMap[showType] = false;
                        return [3 /*break*/, 5];
                    case 1:
                        if (!!this.isLoadBuffMap) return [3 /*break*/, 2];
                        return [2 /*return*/, WxHelper_1.wxHelper.errorAndFilter('showBuff', '!this.isLoadBuffMap')];
                    case 2:
                        if (!this.isLoadBuffMap[showType]) return [3 /*break*/, 3];
                        return [2 /*return*/];
                    case 3:
                        this.isLoadBuffMap[showType] = true;
                        return [4 /*yield*/, nodePoolMgr.get('buff/' + name, this.key)];
                    case 4:
                        it = _a.sent();
                        if (!this.isValid || !this.data) {
                            return [2 /*return*/, nodePoolMgr.put(it)];
                        }
                        this.isLoadBuffMap[showType] = false;
                        this.buffNodes.push(it);
                        _a.label = 5;
                    case 5:
                        it.opacity = 255;
                        it.parent = this.node;
                        it.zIndex = Constant_1.BUFF_NODE_ZINDEX[showType] || 10;
                        cmpt = it.Component(PawnAnimationCmpt_1.default).init(it.Child('body/anim', cc.Sprite), showType, this.key);
                        if (showType === Enums_1.BuffType.SHIELD
                            || showType === Enums_1.BuffType.INSPIRE
                            || showType === Enums_1.BuffType.PROTECTION_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_001
                            || showType === Enums_1.BuffType.RODELERO_SHIELD_102
                            || showType === Enums_1.BuffType.ABNEGATION_SHIELD
                            || showType === Enums_1.BuffType.POISONED_WINE) {
                            cmpt.play('trigger', function () { return cmpt.isValid && cmpt.play('stand'); });
                        }
                        else {
                            cmpt.play('stand');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PawnCmpt.prototype.putBuff = function (type) {
        var showType = Constant_1.BUFF_SHOW_TYPE_TRAN[type] || type;
        var name = 'BUFF_' + showType;
        var node = this.buffNodes.remove('name', name);
        if (!node) {
        }
        else if (type === Enums_1.BuffType.SHIELD
            || showType === Enums_1.BuffType.PROTECTION_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD
            || type === Enums_1.BuffType.RODELERO_SHIELD_001
            || type === Enums_1.BuffType.RODELERO_SHIELD_102
            || type === Enums_1.BuffType.ABNEGATION_SHIELD) {
            node.Component(PawnAnimationCmpt_1.default).play('die', function () { return nodePoolMgr.put(node); });
        }
        else {
            nodePoolMgr.put(node);
        }
    };
    // 刷新体型
    PawnCmpt.prototype.updateAnimScale = function (val) {
        if (this.animNode.scale !== val) {
            this.animNode.scale = val;
            this.animNode.y = this.animNodeInitY + (this.animNodeInitY + 36) * (val - 1);
        }
    };
    // 检测是否死亡
    PawnCmpt.prototype.updateCheckDie = function () {
        var _this = this;
        var _a, _b;
        if (!this.isDie && ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isDie())) {
            this.isDie = true;
            var name = (_b = this.animCmpt) === null || _b === void 0 ? void 0 : _b.playAnimName;
            if (name !== 'hit' && name !== 'die' && name !== 'hit_pull' && name !== 'die_pull') {
                this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, _this.data.aIndex, _this.data.uid); });
            }
        }
    };
    // 同步状态信息
    PawnCmpt.prototype.updateState = function () {
        var _a;
        if (GameHelper_1.gameHpr.playback.isSimulating) {
            return;
        }
        else if (!((_a = this.data) === null || _a === void 0 ? void 0 : _a.state) || this.preStateUid === this.data.state.uid || this.isDie) {
            return;
        }
        this.preStateUid = this.data.state.uid;
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
        var state = this.data.state.type, data = this.data.state.data;
        // cc.log('updateState', this.uid, this.point.ID(), PawnState[state])
        // this.data.actioning = this.data.actioning || (state !== PawnState.STAND && data?.appositionPawnCount > 1) //只要不是待机 就代表行动
        if (state === Enums_1.PawnState.STAND) { //待机
            this.doStand();
        }
        else if (state === Enums_1.PawnState.MOVE || state === Enums_1.PawnState.EDIT_MOVE) { //移动
            this.doMove(data);
        }
        else if (state === Enums_1.PawnState.ATTACK) { //攻击
            this.doAttack(data);
        }
        else if (state === Enums_1.PawnState.HIT) { //受击
            this.doHit(data);
        }
        else if (state === Enums_1.PawnState.DIAUP) { //击飞
            this.doDiaup(data);
        }
        else if (state === Enums_1.PawnState.HEAL) { //回血
            this.doHeal(data);
        }
        else if (state === Enums_1.PawnState.DEDUCT_HP) { //掉血
            this.doDeductHp(data);
        }
        else if (state === Enums_1.PawnState.ADD_ANGER) { //加怒气
            this.doAddAnger(data);
        }
        else if (state === Enums_1.PawnState.FEAR) { //恐惧
            this.doFear(data);
        }
        else if (state === Enums_1.PawnState.DIE) { //直接死亡
            this.doDie(data);
        }
        else if (state >= Enums_1.PawnState.SKILL && state <= Enums_1.PawnState.SKILL_MAX) { //技能
            this.doSkill(data);
        }
        else {
            this.playAnimation('idle');
        }
        // 通知聚焦
        // if (state === PawnState.MOVE || state === PawnState.ATTACK || (state >= PawnState.SKILL && state <= PawnState.SKILL_MAX)) {
        //     eventCenter.emit(EventType.FOCUS_PAWN, { index: this.data.aIndex, uid: this.data.uid, point: this.data.point })
        // }
    };
    // 待机
    PawnCmpt.prototype.doStand = function () {
        var _a, _b;
        var animName = (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.playAnimName;
        if (animName === 'move' || animName === 'move_pull') { //只有移动的时候才强行切换成idle
            this.playAnimation('idle');
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.initInfo();
        this.updatePosition();
    };
    // 移动
    PawnCmpt.prototype.doMove = function (data) {
        var _this = this;
        var _a, _b;
        this.updatePosition();
        var paths = (data.paths || []).map(function (m) { return _this.getActPixelByPoint(cc.v2(m)).clone(); });
        var currMoveTime = (_a = data.currMoveTime) !== null && _a !== void 0 ? _a : 0;
        var needMoveTime = (_b = data.needMoveTime) !== null && _b !== void 0 ? _b : 0;
        // 计算各个距离信息
        var sumDis = 0, arr = [];
        for (var i = 1, l = paths.length; i < l; i++) {
            var curr = paths[i], prep = paths[i - 1], speed = curr.sub(prep, this._temp_vec2_3);
            var dis = speed.mag();
            sumDis += dis;
            arr.push({ dis: dis, progress: sumDis, speed: speed.normalize(), prep: prep, pos: curr });
        }
        var ratio = currMoveTime / needMoveTime;
        var startPos = null, list = [];
        for (var i = 0, l = arr.length; i < l; i++) {
            var m = arr[i], pr = m.progress / sumDis;
            if (ratio > pr) {
                continue;
            }
            else if (!startPos) { //找出起点
                var dr = m.dis / sumDis;
                var r = Math.max(ratio - (pr - dr), 0);
                var d = sumDis * r; //超出的一段距离
                startPos = m.speed.mul(d).addSelf(m.prep);
                m.dis -= d; //减去已经走过的路
            }
            list.push({ time: m.dis / sumDis * needMoveTime, endPos: m.pos });
        }
        if (!startPos) {
            return;
        }
        // 开始移动
        this.playAnimation('move');
        this.node.setPosition(startPos);
        this.animCmpt.resetMove();
        this.runMove(list);
    };
    PawnCmpt.prototype.runMove = function (list) {
        var _this = this;
        if (!this.isValid) {
        }
        else if (list.length > 0) {
            var d = list.shift(), pos = d.endPos;
            this.setDir(pos.x - this.node.x);
            this.animCmpt.moveNodeOne(0, d.time, this.getPosition(), pos, function () { return _this.runMove(list); });
        }
        else if (this.data.getState() === Enums_1.PawnState.MOVE || this.data.getState() === Enums_1.PawnState.EDIT_MOVE) {
            this.playAnimation('idle');
        }
    };
    // 攻击
    PawnCmpt.prototype.doAttack = function (data) {
        var _this = this;
        var _a;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var suffix = data.instabilityAttackIndex || '';
        this.updatePosition();
        this.setDir(targetPoint.x - this.point.x);
        this.playSFXByKey('attack_sound', suffix);
        this.playAnimation('attack' + suffix, function () { return _this.isValid && _this.playAnimation('idle'); }, currAttackTime);
    };
    // 技能
    PawnCmpt.prototype.doSkill = function (data) {
        var _this = this;
        var _a, _b, _c;
        var currAttackTime = (_a = data.currAttackTime) !== null && _a !== void 0 ? _a : 0;
        var targetPoint = data.targetPoint || this.point;
        var skill = data.skill, heroSkill = this.data.getPortrayalSkill();
        // 位移
        if (!this.prePoint.equals(this.data.point) && ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_208
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_212
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_213
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_219
            || (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_306
            || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG)) {
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.setDir(pos.x - this.node.x);
            var params = skill.params;
            if (!heroSkill) {
            }
            else if (heroSkill.id === Enums_1.HeroType.ZHANG_FEI
                || heroSkill.id === Enums_1.HeroType.XU_CHU
                || heroSkill.id === Enums_1.HeroType.PEI_XINGYAN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_QIONG
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.GAO_SHUN
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.HUO_QUBING
                || (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DIAN_WEI) {
                params = heroSkill.params; //张飞 高顺 许褚 裴行俨 秦琼
            }
            else if (this.data.skinId === 3404103) {
                params = '0.36,0.6'; //重骑冬季皮肤
            }
            var _d = __read(ut.stringToNumbers(params, ','), 2), delay = _d[0], time = _d[1];
            this.animCmpt.resetMove().setMoveDelay((delay !== null && delay !== void 0 ? delay : 0) * 1000).moveNodeOne(0, (time !== null && time !== void 0 ? time : 0.1) * 1000, this.getPosition(), pos, function () { return _this.setDir(targetPoint.x - _this.point.x); });
        }
        else {
            this.updatePosition();
            this.setDir(targetPoint.x - this.point.x);
        }
        if (data.sound !== undefined) {
            this.playSFX(data.sound);
        }
        else {
            this.playSFXByKey('skill_sound');
        }
        var isStandShield = (skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_205; //立盾
        var isSpearthrowing = (heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.QIN_LIANGYU && !data.skillName;
        this.playAnimation(data.skillName || 'skill', function () {
            if (!_this.isValid) {
            }
            else if (isStandShield) {
                _this.playAnimation('stand_shield');
            }
            else if (isSpearthrowing) {
                _this.playAnimation('idle_barb');
            }
            else {
                _this.playAnimation('idle');
            }
        }, currAttackTime);
        // 播放粉碎大地 地面效果
        if ((skill === null || skill === void 0 ? void 0 : skill.type) === Enums_1.PawnSkillType.SKILL_215) {
            var type = skill.type, delay = 0.9;
            // 黄盖特殊处理下
            if (((_c = (_b = this.data.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.HUANG_GAI) {
                type = 215001;
                delay = 0.6;
            }
            eventCenter.emit(EventType_1.default.PLAY_BATTLE_EFFECT, {
                type: type, delay: delay,
                index: this.data.aIndex,
                point: this.data.point,
            });
        }
        // 秦良玉 收矛
        if (data.skillName === 'recycle_spear') {
            eventCenter.emit(EventType_1.default.PLAY_BULLET_FLY, {
                bulletId: 5022,
                currTime: currAttackTime,
                needTime: 1000,
                index: this.data.aIndex,
                startPoint: targetPoint,
                targetPoint: this.data.point,
            });
        }
    };
    // 受击
    PawnCmpt.prototype.doHit = function (data) {
        var _this = this;
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var isCrit = !!data.isCrit; //暴击
        var heal = (_c = data.heal) !== null && _c !== void 0 ? _c : 0; //回复
        var isDodge = damage === -1; //闪避
        var isParry = damage === -2; //格挡
        var isTurntheblade = damage === -3; //招架
        var isWithstand = damage === -4; //抵挡
        damage = isDodge || isParry || isTurntheblade || isWithstand ? 0 : damage;
        var attackPoint = data.attackPoint || this.point;
        var isDie = this.isDie = !!data.isDie;
        var time = (_d = data.time) !== null && _d !== void 0 ? _d : 0; //经过的时间
        var sound = data.sound; //受击音效
        var uid = this.uid;
        var isDiaup = this.isDiaup;
        this.isDiaup = false;
        this.setDir(attackPoint.x - this.point.x);
        (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        if (damage + trueDamage === 0) {
            return this.playAnimation('idle');
        }
        else if (isDie) {
            if (this.data.getPawnType() !== Enums_1.PawnType.NONCOMBAT) {
                this.node.zIndex = 0;
            }
            this.putAllBuff();
        }
        else if (isDiaup) { //如果没有死亡且上一个动作是击飞
            return this.playAnimation('idle');
        }
        var animName = 'hit';
        if (isDie) {
            animName = 'die';
            this.playSFXByKey('die_sound');
        }
        else if (sound) {
            this.playSFX(sound);
        }
        this.playAnimation(animName, function () {
            if (isDie) {
                eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid);
            }
            else if (_this.isValid) {
                _this.playAnimation('idle');
            }
        });
    };
    // 直接死亡
    PawnCmpt.prototype.doDie = function (data) {
        var _a;
        var index = this.data.aIndex;
        var uid = this.uid;
        if (!this.data.isNoncombat()) {
            this.node.zIndex = 0;
        }
        this.putAllBuff();
        this.playSFX((_a = this.data.baseJson) === null || _a === void 0 ? void 0 : _a.die_sound);
        this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
    };
    // 击飞
    PawnCmpt.prototype.doDiaup = function (data) {
        var _this = this;
        var _a, _b;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        var attackPoint = data.attackPoint || this.prePoint;
        this.setDir(attackPoint.x - this.point.x);
        if (time > 0) {
            this.prePoint.set(this.data.point);
            this.playSFX('sound_037_1');
            this.playAnimation('diaup');
            this.isDiaup = true;
            var pos = this.getActPixelByPoint(this.data.point);
            this.animCmpt.resetMove().setMoveParabolaHeight((_b = data.parabolaHeight) !== null && _b !== void 0 ? _b : 20).moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 恐惧
    PawnCmpt.prototype.doFear = function (data) {
        var _this = this;
        var _a;
        var time = (_a = data.time) !== null && _a !== void 0 ? _a : 0; //经过的时间
        if (time > 0 && !this.prePoint.equals(this.data.point)) {
            this.setDir(this.point.x - this.prePoint.x);
            this.prePoint.set(this.data.point);
            var pos = this.getActPixelByPoint(this.data.point);
            this.playAnimation('move', null, 0, 0.5);
            this.animCmpt.resetMove().moveNodeOne(0, time, this.getPosition(), pos, function () {
                if (_this.isValid && !_this.isDie) {
                    _this.playAnimation('idle');
                }
            });
        }
        else {
            this.playAnimation('idle');
            this.updatePosition();
        }
    };
    // 回血
    PawnCmpt.prototype.doHeal = function (data) {
        var _a, _b, _c, _d;
        var index = this.data.aIndex;
        var val = (_a = data.val) !== null && _a !== void 0 ? _a : 0;
        var time = (_b = data.time) !== null && _b !== void 0 ? _b : 0;
        var uid = this.uid;
        (_c = this.hpBar) === null || _c === void 0 ? void 0 : _c.play();
        if (this.preShieldValue > 0) { //这里主动刷新一下护盾
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.updateShieldValue(this.preShieldValue, this.data.curHp, this.data.getMaxHp());
        }
    };
    // 掉血
    PawnCmpt.prototype.doDeductHp = function (data) {
        var _a, _b, _c, _d, _e;
        var index = this.data.aIndex;
        var damage = (_a = data.damage) !== null && _a !== void 0 ? _a : 0;
        var trueDamage = (_b = data.trueDamage) !== null && _b !== void 0 ? _b : 0;
        var time = (_c = data.time) !== null && _c !== void 0 ? _c : 0;
        var isDie = this.isDie = !!data.isDie;
        var uid = this.uid;
        if (isDie) {
            (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.setActive(false);
            this.node.zIndex = 0;
            this.putAllBuff();
            this.playAnimation('die', function () { return eventCenter.emit(EventType_1.default.REMOVE_PAWN, index, uid); });
        }
        else {
            (_e = this.hpBar) === null || _e === void 0 ? void 0 : _e.play();
        }
    };
    // 添加怒气
    PawnCmpt.prototype.doAddAnger = function (data) {
        this.updateAnger();
        // this.doStand()
    };
    PawnCmpt = __decorate([
        ccclass
    ], PawnCmpt);
    return PawnCmpt;
}(cc.Component));
exports.default = PawnCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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