
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/AppleHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc5f6/Pz6hOXpQQu4VPLile', 'AppleHelper');
// app/script/common/helper/AppleHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.appleHelper = void 0;
var JsbEvent_1 = require("../event/JsbEvent");
var ErrorReportHelper_1 = require("./ErrorReportHelper");
var JsbHelper_1 = require("./JsbHelper");
var ViewHelper_1 = require("./ViewHelper");
/**
 * 处理IOS 苹果登录相关
 */
var AppleHelper = /** @class */ (function () {
    function AppleHelper() {
    }
    // 登陆
    AppleHelper.prototype.nativeLogin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var res;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.APPLE_LOGIN)];
                    case 1:
                        res = (_a.sent()) || {};
                        if (res.result === 'success') {
                            return [2 /*return*/, { code: res.code, nickname: res.nickName, token: res.token, userId: res.userId }];
                        }
                        else if (res.result === 'logined') {
                            return [2 /*return*/, { userId: res.userId, logined: true }];
                        }
                        else {
                            ErrorReportHelper_1.errorReportHelper.reportError('NativeLogin Error', { type: 'apple', status: res.errcode, extra: JSON.stringify(res) });
                        }
                        return [2 /*return*/, { errcode: res.errcode || -10086 }];
                }
            });
        });
    };
    /**
     * 返回值为0就是未授权 返回值为非0就不用再授权
     * 返回值为0：尚未授权
     * 返回值为1：某种情况下开启了限制广告追踪
     * 返回值为2：已经拒绝授权
     * 返回值为3：已经同意授权
     * 返回值为99：未知情况 属于出错了 可以当返回值2处理
     * 返回值为100：苹果系统低于ios14 那就不用处理
     */
    AppleHelper.prototype.isAttceptAtt = function () {
        return jsb.reflection.callStaticMethod('jsbHelp', 'isAcceptAtt');
    };
    // 返回值为0表示玩家同意授权
    AppleHelper.prototype.requestAtt = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.REQUEST_PERMISSION)];
            });
        });
    };
    // 返回值为0表示玩家同意授权
    AppleHelper.prototype.openAppSetting = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.OPEN_APP_SETTING)];
            });
        });
    };
    // 返回true表示要显示苹果登录按钮
    AppleHelper.prototype.isShowAppleButton = function () {
        return jsb.reflection.callStaticMethod('jsbHelp', 'isAvailableIos13');
    };
    // xx
    AppleHelper.prototype.checkXX = function () {
        return __awaiter(this, void 0, Promise, function () {
            return __generator(this, function (_a) {
                // const data = await netMgr.get('login/getSwitches', { gameVer: version.VERSION, platform: 'ios' })
                // return (data?.switches || []).has(SwitchType.ATT)
                return [2 /*return*/, false];
            });
        });
    };
    // 显示广告授权
    AppleHelper.prototype.showAdAuth = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return ViewHelper_1.viewHelper.showPnl('login/AdAuth', function () { return _this.requestAtt().then(resolve); }); })];
            });
        });
    };
    // 显示广告授权2号
    AppleHelper.prototype.showAdAuth2 = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return ViewHelper_1.viewHelper.showPnl('login/AuthTip', resolve); })];
            });
        });
    };
    // 检测广告授权
    AppleHelper.prototype.checkAdAuth = function () {
        return __awaiter(this, void 0, void 0, function () {
            var xx, att;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.checkXX()];
                    case 1:
                        xx = _a.sent();
                        att = this.isAttceptAtt();
                        if (!xx) return [3 /*break*/, 6];
                        if (!(att === 0 && storageMgr.loadNumber('showAdAuth1') !== 1)) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.showAdAuth()];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 3:
                        if (!(att === 1 && storageMgr.loadNumber('showAdAuth2') !== 1)) return [3 /*break*/, 5];
                        return [4 /*yield*/, this.showAdAuth2()];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5: return [3 /*break*/, 8];
                    case 6:
                        if (!(att === 0 && storageMgr.loadNumber('showAdAuth1') !== 1)) return [3 /*break*/, 8];
                        return [4 /*yield*/, this.requestAtt()];
                    case 7:
                        _a.sent();
                        _a.label = 8;
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    return AppleHelper;
}());
exports.appleHelper = new AppleHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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