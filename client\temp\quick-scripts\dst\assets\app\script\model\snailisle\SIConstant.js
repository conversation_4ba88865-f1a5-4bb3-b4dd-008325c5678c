
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/SIConstant.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6cd62tQp7NNZbM/JNL2jMBm', 'SIConstant');
// app/script/model/snailisle/SIConstant.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LONG_PRESS_LIKE_TIME = exports.ROLE_WORK_TIME = void 0;
// 工作时间
var ROLE_WORK_TIME = 40;
exports.ROLE_WORK_TIME = ROLE_WORK_TIME;
// 点赞长按时间
var LONG_PRESS_LIKE_TIME = 1;
exports.LONG_PRESS_LIKE_TIME = LONG_PRESS_LIKE_TIME;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxzbmFpbGlzbGVcXFNJQ29uc3RhbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0EsT0FBTztBQUNQLElBQU0sY0FBYyxHQUFHLEVBQUUsQ0FBQTtBQU1yQix3Q0FBYztBQUpsQixTQUFTO0FBQ1QsSUFBTSxvQkFBb0IsR0FBRyxDQUFDLENBQUE7QUFJMUIsb0RBQW9CIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIOW3peS9nOaXtumXtFxyXG5jb25zdCBST0xFX1dPUktfVElNRSA9IDQwXHJcblxyXG4vLyDngrnotZ7plb/mjInml7bpl7RcclxuY29uc3QgTE9OR19QUkVTU19MSUtFX1RJTUUgPSAxXHJcblxyXG5leHBvcnQge1xyXG4gICAgUk9MRV9XT1JLX1RJTUUsXHJcbiAgICBMT05HX1BSRVNTX0xJS0VfVElNRSxcclxufSJdfQ==