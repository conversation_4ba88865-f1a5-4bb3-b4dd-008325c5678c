
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/SearchPoint.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '44834JbCTRNurdjSbnfQZHe', 'SearchPoint');
// app/script/common/astar/SearchPoint.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var MapHelper_1 = require("../helper/MapHelper");
/**
 * 搜索点
 */
var SearchPoint = /** @class */ (function () {
    function SearchPoint() {
        this.opened = []; //开放列表
        this.closed = {}; //关闭列表
        this.checkHasPass = null; //检测方法
        this.hasPointMap = {};
    }
    SearchPoint.prototype.init = function (checkHasPass) {
        this.checkHasPass = checkHasPass;
        return this;
    };
    // 检查节点
    SearchPoint.prototype.findNode = function (tx, ty) {
        var id = tx + '_' + ty;
        if (this.closed[id]) {
            return null; //找过了
        }
        else if (this.hasPointMap[id] || !this.checkHasPass(tx, ty)) {
            this.closed[id] = true;
            this.opened.push(cc.v2(tx, ty));
            return null; //有障碍
        }
        return cc.v2(tx, ty);
    };
    SearchPoint.prototype.search = function (start, points, range) {
        var _a;
        var _this = this;
        this.opened = [start];
        this.closed = (_a = {}, _a[start.ID()] = true, _a);
        this.hasPointMap = {};
        points.forEach(function (m) { return _this.hasPointMap[m.ID()] = true; });
        var point = null;
        // 开始搜寻
        while (this.opened.length > 0) {
            var p = this.opened.shift();
            if (range > 0 && MapHelper_1.mapHelper.getPointToPointDis(p, start) > range) {
                continue;
            }
            point = point || this.findNode(p.x, p.y - 1); //上
            point = point || this.findNode(p.x + 1, p.y); //右
            point = point || this.findNode(p.x, p.y + 1); //下
            point = point || this.findNode(p.x - 1, p.y); //左
            if (point) {
                return point;
            }
        }
        return point;
    };
    return SearchPoint;
}());
exports.default = SearchPoint;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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