
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/Constant.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '479ff5A9XFA/Zmr6hM7rR5U', 'Constant');
// app/script/common/constant/Constant.ts

"use strict";
/////////////// 所有常量（全大写单词间用下划线隔开）///////////////
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_BUILD_SIZE = exports.DEFAULT_AREA_SIZE = exports.DEFAULT_CITY_SIZE = exports.CREATE_ALLI_MAX_LV = exports.ARMY_PAWN_MAX_COUNT = exports.MODIFY_NICKNAME_GOLD = exports.IN_DONE_FORGE_GOLD = exports.IN_DONE_BT_GOLD = exports.DEFAULT_BT_QUEUE_COUNT = exports.INIT_RES_OUTPUT = exports.INIT_RES_COUNT = exports.INIT_RES_CAP = exports.AX_CAVALRY_ID = exports.PAWN_CROSSBOW_ID = exports.BUILD_TOWER_NID = exports.BUILD_FORT_NID = exports.BUILD_HOSPITAL_NID = exports.BUILD_HEROHALL_NID = exports.BUILD_ALLI_BAZAAR_NID = exports.BUILD_PLANT_NID = exports.BUILD_DRILLGROUND_NID = exports.BUILD_SMITHY_NID = exports.BUILD_BAZAAR_NID = exports.BUILD_EMBASSY_NID = exports.BUILD_BARRACKS_NID = exports.BUILD_WAREHOUSE_NID = exports.BUILD_GRANARY_NID = exports.BUILD_MAIN_NID = exports.BUILD_WALL_NID = exports.BUILD_QUARRY_ID = exports.BUILD_TIMBER_ID = exports.BUILD_FARM_ID = exports.CITY_LUOYANG_ID = exports.CITY_YANJING_ID = exports.CITY_JINLING_ID = exports.CITY_CHANGAN_ID = exports.ANCIENT_WALL_ID = exports.CITY_FORT_NID = exports.BUILD_FLAG_NID = exports.CITY_MAIN_NID = exports.DELAY_CLOSE_PNL_TIME = exports.LONG_PRESS_TIME = exports.MAX_ZINDEX = exports.AREA_MAX_ZINDEX = exports.BUILD_DRAG_OFFSETY = exports.MAP_EXTRA_SIZE = exports.TILE_SIZE_HALF = exports.TILE_SIZE = exports.MAP_SHOW_OFFSET = exports.CLICK_SPACE = void 0;
exports.REPLACEMENT_SERVICE_CHARGE = exports.CHAT_BARRAGE_COLOR = exports.NEED_MUTUAL_BUFF = exports.NEED_SHOW_BUFF = exports.BUFF_NODE_ZINDEX = exports.BATTLE_EFFECT_TYPE = exports.SHIELD_BUFF = exports.BUFF_SHOW_TYPE_TRAN = exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = exports.CREATE_ALLI_COND = exports.CREATE_ALLI_COST = exports.CAN_EXIT_ALLI_TIME = exports.RESET_STUDY_SLOT_GOLD = exports.PAWN_SLOT_CONF = exports.EQUIP_SMELT_NEED_LV = exports.EQUIP_SLOT_EXCLUSIVE_LV = exports.EQUIP_SLOT_CONF = exports.POLICY_SLOT_CONF = exports.ADD_OUTPUT_TIME = exports.ADD_OUTPUT_RATIO = exports.ADD_OUTPUT_GOLD = exports.FREE_HEAD_ICONS = exports.FIXATION_MENU_MAX_COUNT = exports.FIXATION_MENU_CONFIG = exports.PLAYBACK_MULS = exports.ARMY_RECORD_DESC_CONF = exports.MAIL_STATE_COLOR = exports.ARMY_STATE_COLOR = exports.MARCH_ARMY_TIME_COLOR = exports.MARCH_ARMY_NAME_COLOR = exports.BUILD_EFFECT_TYPE_CONF = exports.CTYPE_NAME = exports.CTYPE_ICON = exports.CTYPE_ICON_URL = exports.RES_FIELDS_CTYPE = exports.PAWN_BUBBLE_OFFSETY = exports.CELL_RES_FIELDS = exports.SELECT_CELL_INFO_BOX = exports.RIVER_LINE_CONF = exports.BORDER_LINE_CONF = exports.DECORATION_MUD_OUTER_CONF = exports.DECORATION_MUD_CONF = exports.LAND_DI_CONF = exports.BATTLE_MAX_TIME = exports.TRANSIT_TIME = exports.MAIN_CITY_MARCH_SPEED = exports.UP_MARCH_SPEED_MUL = exports.DEFAULT_MAX_ADD_PAWN_TIMES = exports.DEFAULT_MAX_ARMY_COUNT = exports.BOSS_BUILD_SIZE = void 0;
exports.ALLI_APPLY_MAX_COUNT = exports.MAX_MAP_MARK_COUNT = exports.RES_MAP = exports.FIRE_PAWN_ID = exports.SPEAR_PAWN_ID = exports.DEFAULT_PET_ID = exports.SUMMON_LV = exports.HERO_SLOT_LV_COND = exports.HERO_REVIVES_TIME = exports.HERO_OPT_GIFT = exports.BUY_OPT_HERO_COST = exports.RESTORE_PORTRAYAL_GOLD_COST = exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = exports.PORTRAYAL_COMP_NEED_COUNT = exports.POINTSETS_ONE_GOLD_COST = exports.POINTSETS_ONE_COST = exports.NEXT_APPLY_CD = exports.SERVER_APPLY_CANCEL_CD = exports.SEND_TRUMPET_ACC_COST = exports.SEND_TRUMPET_COST = exports.CHAT_BANNED_REST_MAX_TIME = exports.CHAT_REST_MAX_TIME = exports.CHAT_TOLERATE_MAX_COUNT = exports.CHAT_SEND_INTERVAL = exports.CHAT_MAX_COUNT = exports.COLOR_NORMAL = exports.ANCIENT_SUP_TIME = exports.ANCIENT_SUP_COST_MUL = exports.SEASON_DURATION_TIME = exports.CAN_MIN_MARCH_SPEED = exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = exports.BATTLE_FORECAST_FREE_COUNT = exports.BATTLE_FORECAST_COST = exports.FRIENDS_MIN_LAND_COUNT = exports.SHOW_TIME_MAX_INTERVAL = exports.NOLIMIT_PCHAT_MAX_LAND = exports.OCCUPY_PLAYER_CELL_MIN_DIS = exports.REMOVE_PCHAT_TIME = exports.LAND_SCORE_CONF = exports.CONCURRENT_GAME_LIMIT = exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = exports.NOT_OCCUPY_BY_SERVER_RUNTIME = exports.ALLI_JOB_COUNT = exports.ALLI_JOB_DESC = exports.LOGOUT_MAX_DAY = exports.LANGUAGE_TEXT_LIST = exports.UP_RECRUIT_PAWN_MUL = exports.RES_TRANSIT_CAP = exports.REPLACEMENT_TODAY_COUNT_MAP = exports.REPLACEMENT_MIN_RES_COUNT = void 0;
exports.PAWN_COST_FACTOR_BASE_VALUE = exports.CURE_TIME_PARAM_MAP = exports.CURE_RES_PARAM_MAP = exports.ACHIEVEMENT_COLOR = exports.RETURNED_FREE_TYPE = exports.MONTH_CARD = exports.DIFFICULTY_BG_COLOR = exports.AREA_DI_COLOR_CONF = exports.MAP_MASK_ITEM_OPACITY_GOLD = exports.MAP_MASK_ITEM_OPACITY = exports.MAP_MASK_ITEM_COLOR = exports.CAMERA_BG_COLOR = exports.FACTORY_SLOT_CONF = exports.PAWN_COST_LV_LIST = exports.ALLI_LEADER_VOTE_MAX_COUNT = exports.STUDY_TO_BOOKTYPE = exports.PORTRAYAL_CHOSENONE_ODDS = exports.GO_HOSPITAL_CHANCE = exports.HOSPITAL_PAWN_LIMIT = exports.TONDEN_STAMINA_MUL = exports.TODAY_TONDEN_MAX_COUNT = exports.NOTICE_PERMISSION_CD = exports.PRIZE_QUESTION_TIME = exports.PRIZE_QUESTION_ID = exports.RECHARGE_BATTLE_PASS_EXP = exports.RECHARGE_BATTLE_PASS = exports.BATTLE_FIRE_COLOR = exports.BATTLE_HPBAR_COLOR = exports.RANK_SHOP_WAR_TOKEN_CONFIG = exports.SKEW_SIZE_HALF = exports.SKEW_SIZE = exports.SKEW_ANGLE = exports.LOBBY_MODE_BUTTOM_NAME = void 0;
var Enums_1 = require("./Enums");
// 点击间隔
var CLICK_SPACE = 10;
exports.CLICK_SPACE = CLICK_SPACE;
// 一格的大小
var TILE_SIZE = 80;
exports.TILE_SIZE = TILE_SIZE;
// 地图边界额外宽度
var MAP_EXTRA_SIZE = 2;
exports.MAP_EXTRA_SIZE = MAP_EXTRA_SIZE;
// 地图显示偏移
var MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8);
exports.MAP_SHOW_OFFSET = MAP_SHOW_OFFSET;
//
var TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5);
exports.TILE_SIZE_HALF = TILE_SIZE_HALF;
// 设施拖拽时候的高
var BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24;
exports.BUILD_DRAG_OFFSETY = BUILD_DRAG_OFFSETY;
// 区域最高y坐标
var AREA_MAX_ZINDEX = 21 * TILE_SIZE;
exports.AREA_MAX_ZINDEX = AREA_MAX_ZINDEX;
// 层级最大值
var MAX_ZINDEX = 10000;
exports.MAX_ZINDEX = MAX_ZINDEX;
// 长按时间
var LONG_PRESS_TIME = 0.4;
exports.LONG_PRESS_TIME = LONG_PRESS_TIME;
// 延迟关闭pnl时间
var DELAY_CLOSE_PNL_TIME = 0.4;
exports.DELAY_CLOSE_PNL_TIME = DELAY_CLOSE_PNL_TIME;
// 主城id
var CITY_MAIN_NID = 1001;
exports.CITY_MAIN_NID = CITY_MAIN_NID;
// 要塞id
var CITY_FORT_NID = 2102;
exports.CITY_FORT_NID = CITY_FORT_NID;
// 4个遗迹
var ANCIENT_WALL_ID = 3000; //城墙
exports.ANCIENT_WALL_ID = ANCIENT_WALL_ID;
var CITY_CHANGAN_ID = 3001; //长安
exports.CITY_CHANGAN_ID = CITY_CHANGAN_ID;
var CITY_JINLING_ID = 3002; //金陵
exports.CITY_JINLING_ID = CITY_JINLING_ID;
var CITY_YANJING_ID = 3003; //燕京
exports.CITY_YANJING_ID = CITY_YANJING_ID;
var CITY_LUOYANG_ID = 3004; //洛阳
exports.CITY_LUOYANG_ID = CITY_LUOYANG_ID;
// 农场
var BUILD_FARM_ID = 2201;
exports.BUILD_FARM_ID = BUILD_FARM_ID;
// 伐木场
var BUILD_TIMBER_ID = 2202;
exports.BUILD_TIMBER_ID = BUILD_TIMBER_ID;
// 采石场
var BUILD_QUARRY_ID = 2203;
exports.BUILD_QUARRY_ID = BUILD_QUARRY_ID;
// 城墙建筑id
var BUILD_WALL_NID = 2000;
exports.BUILD_WALL_NID = BUILD_WALL_NID;
// 主城id
var BUILD_MAIN_NID = 2001;
exports.BUILD_MAIN_NID = BUILD_MAIN_NID;
// 仓库建筑id
var BUILD_GRANARY_NID = 2002;
exports.BUILD_GRANARY_NID = BUILD_GRANARY_NID;
// 粮仓建筑id
var BUILD_WAREHOUSE_NID = 2003;
exports.BUILD_WAREHOUSE_NID = BUILD_WAREHOUSE_NID;
// 兵营建筑id
var BUILD_BARRACKS_NID = 2004;
exports.BUILD_BARRACKS_NID = BUILD_BARRACKS_NID;
// 大使馆建筑id
var BUILD_EMBASSY_NID = 2005;
exports.BUILD_EMBASSY_NID = BUILD_EMBASSY_NID;
// 市场建筑id
var BUILD_BAZAAR_NID = 2006;
exports.BUILD_BAZAAR_NID = BUILD_BAZAAR_NID;
// 铁匠铺所建筑id
var BUILD_SMITHY_NID = 2008;
exports.BUILD_SMITHY_NID = BUILD_SMITHY_NID;
// 校场建筑id
var BUILD_DRILLGROUND_NID = 2011;
exports.BUILD_DRILLGROUND_NID = BUILD_DRILLGROUND_NID;
// 工厂建筑id
var BUILD_PLANT_NID = 2010;
exports.BUILD_PLANT_NID = BUILD_PLANT_NID;
// 联盟市场建筑id
var BUILD_ALLI_BAZAAR_NID = 2014;
exports.BUILD_ALLI_BAZAAR_NID = BUILD_ALLI_BAZAAR_NID;
// 英雄殿建筑id
var BUILD_HEROHALL_NID = 2015;
exports.BUILD_HEROHALL_NID = BUILD_HEROHALL_NID;
// 医馆建筑id
var BUILD_HOSPITAL_NID = 2016;
exports.BUILD_HOSPITAL_NID = BUILD_HOSPITAL_NID;
// 旗子id
var BUILD_FLAG_NID = 2101;
exports.BUILD_FLAG_NID = BUILD_FLAG_NID;
// 要塞建筑id
var BUILD_FORT_NID = 2102;
exports.BUILD_FORT_NID = BUILD_FORT_NID;
// 箭塔建筑id
var BUILD_TOWER_NID = 2103;
exports.BUILD_TOWER_NID = BUILD_TOWER_NID;
// 强弩兵ID
var PAWN_CROSSBOW_ID = 3305;
exports.PAWN_CROSSBOW_ID = PAWN_CROSSBOW_ID;
// 斧骑兵ID
var AX_CAVALRY_ID = 3406;
exports.AX_CAVALRY_ID = AX_CAVALRY_ID;
// 初始资源产量
var INIT_RES_OUTPUT = 120;
exports.INIT_RES_OUTPUT = INIT_RES_OUTPUT;
// 初始容量
var INIT_RES_CAP = 1000;
exports.INIT_RES_CAP = INIT_RES_CAP;
// 初始资源
var INIT_RES_COUNT = 700;
exports.INIT_RES_COUNT = INIT_RES_COUNT;
// 默认修建队列
var DEFAULT_BT_QUEUE_COUNT = 2;
exports.DEFAULT_BT_QUEUE_COUNT = DEFAULT_BT_QUEUE_COUNT;
// 立即完成修建需要的金币数
var IN_DONE_BT_GOLD = 30;
exports.IN_DONE_BT_GOLD = IN_DONE_BT_GOLD;
// 立即完成打造需要的金币数
var IN_DONE_FORGE_GOLD = 30;
exports.IN_DONE_FORGE_GOLD = IN_DONE_FORGE_GOLD;
// 修改昵称需要的金币数
var MODIFY_NICKNAME_GOLD = 500;
exports.MODIFY_NICKNAME_GOLD = MODIFY_NICKNAME_GOLD;
// 军队最大士兵个数
var ARMY_PAWN_MAX_COUNT = 9;
exports.ARMY_PAWN_MAX_COUNT = ARMY_PAWN_MAX_COUNT;
// 大使馆多少级可以创建联盟
var CREATE_ALLI_MAX_LV = 3;
exports.CREATE_ALLI_MAX_LV = CREATE_ALLI_MAX_LV;
// 
var DEFAULT_CITY_SIZE = cc.v2(1, 1); //默认的城市地块大小
exports.DEFAULT_CITY_SIZE = DEFAULT_CITY_SIZE;
var DEFAULT_AREA_SIZE = cc.v2(11, 11); //默认的区域大小
exports.DEFAULT_AREA_SIZE = DEFAULT_AREA_SIZE;
var DEFAULT_BUILD_SIZE = cc.v2(1, 1); //默认的建筑面积大小
exports.DEFAULT_BUILD_SIZE = DEFAULT_BUILD_SIZE;
var BOSS_BUILD_SIZE = cc.v2(3, 3); //boss
exports.BOSS_BUILD_SIZE = BOSS_BUILD_SIZE;
var DEFAULT_MAX_ARMY_COUNT = 5; //默认区域的最大容纳军队数量
exports.DEFAULT_MAX_ARMY_COUNT = DEFAULT_MAX_ARMY_COUNT;
var DEFAULT_MAX_ADD_PAWN_TIMES = 20; //默认最大补兵次数
exports.DEFAULT_MAX_ADD_PAWN_TIMES = DEFAULT_MAX_ADD_PAWN_TIMES;
// 加速行军倍数
var UP_MARCH_SPEED_MUL = 3;
exports.UP_MARCH_SPEED_MUL = UP_MARCH_SPEED_MUL;
// 城边加速倍数
var MAIN_CITY_MARCH_SPEED = {
    1: 4,
    2: 3.5,
    3: 3,
    4: 2.5,
    5: 2,
    6: 1.5,
};
exports.MAIN_CITY_MARCH_SPEED = MAIN_CITY_MARCH_SPEED;
// 运送时间 格/小时
var TRANSIT_TIME = 300;
exports.TRANSIT_TIME = TRANSIT_TIME;
// 一场战斗最多持续时间 秒
var BATTLE_MAX_TIME = 3600 * 3;
exports.BATTLE_MAX_TIME = BATTLE_MAX_TIME;
// 地块底配置
var LAND_DI_CONF = [
    { list: [0, 0, 0, 0], no: '01' },
    { list: [0, 1, 1, 0], no: '02' },
    { list: [0, 1, 1, 1], no: '03' },
    { list: [0, 0, 1, 1], no: '04' },
    { list: [1, 1, 1, 0], no: '05' },
    { list: [1, 1, 1, 1], no: '06' },
    { list: [1, 0, 1, 1], no: '07' },
    { list: [1, 1, 0, 0], no: '08' },
    { list: [1, 1, 0, 1], no: '09' },
    { list: [1, 0, 0, 1], no: '10' },
    { list: [0, 0, 1, 0], no: '11' },
    { list: [1, 0, 1, 0], no: '12' },
    { list: [1, 0, 0, 0], no: '13' },
    { list: [0, 1, 0, 0], no: '14' },
    { list: [0, 1, 0, 1], no: '15' },
    { list: [0, 0, 0, 1], no: '16' },
];
exports.LAND_DI_CONF = LAND_DI_CONF;
// 地块底配置，方向：左上右下
var DECORATION_MUD_CONF = {
    '0011': 1,
    '1011': 2,
    '1001': 3,
    '0111': 4,
    '1101': 6,
    '0110': 7,
    '1110': 8,
    '1100': 9,
    '0010': 10,
    '1010': 11,
    '1000': 12,
    '0001': 13,
    '0101': 14,
    '0100': 15,
    '0000': 21,
    '1111': [5, 16],
};
exports.DECORATION_MUD_CONF = DECORATION_MUD_CONF;
// 地块底配置，方向：左上右下
var DECORATION_MUD_OUTER_CONF = {
    '0011': 97,
    '1011': 98,
    '1001': 99,
    '0111': 100,
    '1101': 102,
    '0110': 103,
    '1110': 104,
    '1100': 105,
    '0010': 106,
    '1010': 107,
    '1000': 108,
    '0001': 109,
    '0101': 110,
    '0100': 111,
    '1111': [101],
};
exports.DECORATION_MUD_OUTER_CONF = DECORATION_MUD_OUTER_CONF;
// 边框线配置
var BORDER_LINE_CONF = [
    { size: cc.size(80, 4), pos: cc.v2(0, 38) },
    { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    { size: cc.size(80, 4), pos: cc.v2(0, -38) },
    { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    { size: cc.size(4, 4), pos: cc.v2(-38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, 38) },
    { size: cc.size(4, 4), pos: cc.v2(38, -38) },
    { size: cc.size(4, 4), pos: cc.v2(-38, -38) },
];
exports.BORDER_LINE_CONF = BORDER_LINE_CONF;
// 河流边框线配置
var RIVER_LINE_CONF = {
    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) },
    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) },
    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) },
    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) },
    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) },
    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) },
    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) },
    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) },
    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) },
};
exports.RIVER_LINE_CONF = RIVER_LINE_CONF;
// 选择地块信息框大小
var SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320);
exports.SELECT_CELL_INFO_BOX = SELECT_CELL_INFO_BOX;
// 士兵气泡高度
var PAWN_BUBBLE_OFFSETY = 100;
exports.PAWN_BUBBLE_OFFSETY = PAWN_BUBBLE_OFFSETY;
// 地块资源配置列表字段
var CELL_RES_FIELDS = ['cereal', 'timber', 'stone'];
exports.CELL_RES_FIELDS = CELL_RES_FIELDS;
// 资源字段反向映射
var RES_FIELDS_CTYPE = {
    'cereal': Enums_1.CType.CEREAL,
    'timber': Enums_1.CType.TIMBER,
    'stone': Enums_1.CType.STONE,
};
exports.RES_FIELDS_CTYPE = RES_FIELDS_CTYPE;
// 通用类型对应图标url
var CTYPE_ICON_URL = (_a = {},
    _a[Enums_1.CType.TITLE] = 'icon/title_empty',
    _a[Enums_1.CType.WIN_POINT] = 'icon/win_point',
    _a[Enums_1.CType.HERO_OPT] = 'icon/hero_opt',
    _a[Enums_1.CType.UP_RECRUIT] = 'icon/up_recruit',
    _a);
exports.CTYPE_ICON_URL = CTYPE_ICON_URL;
// 通用类型对应图标url
var CTYPE_ICON = (_b = {},
    _b[Enums_1.CType.CEREAL] = 'cereal',
    _b[Enums_1.CType.TIMBER] = 'timber',
    _b[Enums_1.CType.STONE] = 'stone',
    _b[Enums_1.CType.GOLD] = 'gold',
    _b[Enums_1.CType.INGOT] = 'ingot',
    _b[Enums_1.CType.WAR_TOKEN] = 'war_token',
    _b[Enums_1.CType.EXP_BOOK] = 'exp_book',
    _b[Enums_1.CType.CEREAL_C] = 'cereal_c',
    _b[Enums_1.CType.IRON] = 'iron',
    _b[Enums_1.CType.UP_SCROLL] = 'up_scroll',
    _b[Enums_1.CType.FIXATOR] = 'fixator',
    _b[Enums_1.CType.BASE_RES] = 'base_res',
    _b[Enums_1.CType.BASE_RES_2] = 'base_res_2',
    _b[Enums_1.CType.STAMINA] = 'stamina',
    _b[Enums_1.CType.RANK_COIN] = 'rank_coin',
    _b);
exports.CTYPE_ICON = CTYPE_ICON;
// 通用类型对应的名字
var CTYPE_NAME = (_c = {},
    _c[Enums_1.CType.CEREAL] = 'ui.cereal',
    _c[Enums_1.CType.TIMBER] = 'ui.timber',
    _c[Enums_1.CType.STONE] = 'ui.stone',
    _c[Enums_1.CType.GOLD] = 'ui.gold',
    _c[Enums_1.CType.INGOT] = 'ui.ingot',
    _c[Enums_1.CType.WAR_TOKEN] = 'ui.war_token',
    _c[Enums_1.CType.EXP_BOOK] = 'ui.exp_book',
    _c[Enums_1.CType.CEREAL_C] = 'ui.cereal_c',
    _c[Enums_1.CType.IRON] = 'ui.iron',
    _c[Enums_1.CType.UP_SCROLL] = 'ui.up_scroll',
    _c[Enums_1.CType.FIXATOR] = 'ui.fixator',
    _c[Enums_1.CType.BASE_RES] = 'ui.base_res',
    _c);
exports.CTYPE_NAME = CTYPE_NAME;
// 建筑效果配置
var BUILD_EFFECT_TYPE_CONF = (_d = {},
    _d[Enums_1.CEffect.BT_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GRANARY_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.WAREHOUSE_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ALLIANCE_PERS] = { vtype: 'number' },
    _d[Enums_1.CEffect.MERCHANT_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.DRILL_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.WALL_HP] = { vtype: 'number' },
    _d[Enums_1.CEffect.FORGE_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.ARMY_COUNT] = { vtype: 'number' },
    _d[Enums_1.CEffect.RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MARCH_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.UPLVING_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.GW_CAP] = { vtype: 'number' },
    _d[Enums_1.CEffect.XL_2LV] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TRANSIT_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MAIN_MARCH_MUL] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_BUILD_CD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.TREASURE_AWARD] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.FREE_RECAST] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.RARE_RES_OUTPUT] = { vtype: 'number' },
    _d[Enums_1.CEffect.MORE_RARE_RES] = { vtype: 'number' },
    _d[Enums_1.CEffect.LV_UP_QUEUE] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_LV] = { vtype: 'number' },
    _d[Enums_1.CEffect.FARM_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.QUARRY_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MILL_OUTPUT] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_QUEUE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.MARKET_SERVICE_CHARGE] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CITY_COUNT_LIMIT] = { vtype: 'number' },
    _d[Enums_1.CEffect.TOWER_HP] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.OTHER_RES_ODDS] = { vtype: 'number', suffix: '%' },
    _d[Enums_1.CEffect.CURE_CD] = { vtype: 'number', suffix: '%' },
    _d);
exports.BUILD_EFFECT_TYPE_CONF = BUILD_EFFECT_TYPE_CONF;
// 行军军队名字颜色
var MARCH_ARMY_NAME_COLOR = (_e = {},
    _e[Enums_1.MarchLineType.SELF_ARMY] = '#59A733',
    _e[Enums_1.MarchLineType.OTHER_ARMY] = '#C34B3F',
    _e[Enums_1.MarchLineType.ALLI_ARMY] = '#4F8FBA',
    _e);
exports.MARCH_ARMY_NAME_COLOR = MARCH_ARMY_NAME_COLOR;
// 行军军队时间颜色
var MARCH_ARMY_TIME_COLOR = (_f = {},
    _f[Enums_1.MarchLineType.SELF_ARMY] = '#FFFFFF',
    _f[Enums_1.MarchLineType.OTHER_ARMY] = '#FF9162',
    _f[Enums_1.MarchLineType.ALLI_ARMY] = '#7FD6FF',
    _f);
exports.MARCH_ARMY_TIME_COLOR = MARCH_ARMY_TIME_COLOR;
// 军队状态颜色
var ARMY_STATE_COLOR = (_g = {},
    _g[Enums_1.ArmyState.NONE] = '#936E5A',
    _g[Enums_1.ArmyState.MARCH] = '#936E5A',
    _g[Enums_1.ArmyState.FIGHT] = '#C34B3F',
    _g[Enums_1.ArmyState.DRILL] = '#59A733',
    _g[Enums_1.ArmyState.LVING] = '#59A733',
    _g[Enums_1.ArmyState.CURING] = '#59A733',
    _g[Enums_1.ArmyState.TONDEN] = '#59A733',
    _g);
exports.ARMY_STATE_COLOR = ARMY_STATE_COLOR;
// 邮件状态颜色
var MAIL_STATE_COLOR = (_h = {},
    _h[Enums_1.MailStateType.NONE] = '#C34B3F',
    _h[Enums_1.MailStateType.NOT_CLAIM] = '#C34B3F',
    _h[Enums_1.MailStateType.READ] = '#A18876',
    _h);
exports.MAIL_STATE_COLOR = MAIL_STATE_COLOR;
var COLOR_NORMAL = {
    DONE: '#21DC2D'
};
exports.COLOR_NORMAL = COLOR_NORMAL;
// 军队记录说明的配置
var ARMY_RECORD_DESC_CONF = {
    0: ['index'],
    1: ['index', 'target'],
    2: ['index', 'target'],
    3: ['index', 'target'],
    4: ['index'],
    5: ['index', 'target'],
    6: ['index'],
    7: ['index', 'target'],
};
exports.ARMY_RECORD_DESC_CONF = ARMY_RECORD_DESC_CONF;
// 回放倍数
var PLAYBACK_MULS = [
    { val: 4, text: '0.25x' },
    { val: 2, text: '0.5x' },
    { val: 1, text: '1x' },
    { val: 0.5, text: '2x' },
    { val: 0.25, text: '4x' },
];
exports.PLAYBACK_MULS = PLAYBACK_MULS;
// 固定到菜单配置
var FIXATION_MENU_CONFIG = {
    2001: 'build/BuildMainInfo',
    2004: 'build/BuildBarracks',
    2005: 'build/BuildEmbassy',
    2006: 'build/BuildBazaar',
    2008: 'build/BuildSmithy',
    2010: 'build/BuildFactory',
    2011: 'build/BuildDrillground',
    2012: 'build/BuildTower',
    2013: 'build/BuildTower',
    2014: 'build/BuildBazaar',
    2015: 'build/BuildHerohall',
    2016: 'build/BuildHospital',
};
exports.FIXATION_MENU_CONFIG = FIXATION_MENU_CONFIG;
var FIXATION_MENU_MAX_COUNT = 3; //固定到菜单最多个数
exports.FIXATION_MENU_MAX_COUNT = FIXATION_MENU_MAX_COUNT;
// 免费头像列表
var FREE_HEAD_ICONS = [
    'head_icon_free_001',
    'head_icon_free_002',
    'head_icon_free_003',
    'head_icon_free_004',
    'head_icon_free_005',
    'head_icon_free_006',
    'head_icon_free_007',
    'head_icon_free_008',
];
exports.FREE_HEAD_ICONS = FREE_HEAD_ICONS;
// 商城购买添加产量需要的金币
var ADD_OUTPUT_GOLD = 50;
exports.ADD_OUTPUT_GOLD = ADD_OUTPUT_GOLD;
var ADD_OUTPUT_RATIO = 20; //商城购买添加产量比例
exports.ADD_OUTPUT_RATIO = ADD_OUTPUT_RATIO;
var ADD_OUTPUT_TIME = 1 * 86400 * 1000; //商城购买添加产量持续时间
exports.ADD_OUTPUT_TIME = ADD_OUTPUT_TIME;
// 内政政策槽位配置
var POLICY_SLOT_CONF = [3, 5, 10, 15, 20];
exports.POLICY_SLOT_CONF = POLICY_SLOT_CONF;
// 装备槽位配置
var EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20];
exports.EQUIP_SLOT_CONF = EQUIP_SLOT_CONF;
var EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true };
exports.EQUIP_SLOT_EXCLUSIVE_LV = EQUIP_SLOT_EXCLUSIVE_LV;
// 装备融炼解锁等级
var EQUIP_SMELT_NEED_LV = [14, 20];
exports.EQUIP_SMELT_NEED_LV = EQUIP_SMELT_NEED_LV;
// 士兵槽位配置
var PAWN_SLOT_CONF = [1, 2, 4, 7, 1001, 1010, 1020]; // 兵营额外的槽位的key：1000+英雄殿槽位lv
exports.PAWN_SLOT_CONF = PAWN_SLOT_CONF;
// 研究每重置一次需要的金币
var RESET_STUDY_SLOT_GOLD = 50;
exports.RESET_STUDY_SLOT_GOLD = RESET_STUDY_SLOT_GOLD;
// 多长时间可以退出联盟
var CAN_EXIT_ALLI_TIME = 3600000 * 12;
exports.CAN_EXIT_ALLI_TIME = CAN_EXIT_ALLI_TIME;
// 创建联盟费用
var CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000';
exports.CREATE_ALLI_COST = CREATE_ALLI_COST;
var CREATE_ALLI_COND = 100;
exports.CREATE_ALLI_COND = CREATE_ALLI_COND;
// 单个玩家给其他玩家改变人气间隔
var ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30;
exports.ONE_USER_POPULARITY_CHANGE_INTERVAL = ONE_USER_POPULARITY_CHANGE_INTERVAL;
// 外显buff 同时显示 层级
var BUFF_NODE_ZINDEX = (_j = {},
    _j[Enums_1.BuffType.SHIELD] = 1,
    _j[Enums_1.BuffType.PROTECTION_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_001] = 1,
    _j[Enums_1.BuffType.RODELERO_SHIELD_102] = 1,
    _j[Enums_1.BuffType.ABNEGATION_SHIELD] = 1,
    _j[Enums_1.BuffType.PARRY] = 2,
    _j[Enums_1.BuffType.PARRY_001] = 2,
    _j[Enums_1.BuffType.PARRY_102] = 2,
    _j[Enums_1.BuffType.WITHSTAND] = 2,
    _j[Enums_1.BuffType.JUMPSLASH_DAMAGE] = 2,
    _j[Enums_1.BuffType.BEHEADED_GENERAL] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_DEFENSE] = 2,
    _j[Enums_1.BuffType.ANTICIPATION_ATTACK] = 2,
    _j[Enums_1.BuffType.DIZZINESS] = 3,
    _j[Enums_1.BuffType.PARALYSIS] = 3,
    _j[Enums_1.BuffType.PARALYSIS_UP] = 3,
    _j[Enums_1.BuffType.WIRE_CHAIN] = 3,
    _j[Enums_1.BuffType.SILENCE] = 4,
    _j[Enums_1.BuffType.CHAOS] = 4,
    _j[Enums_1.BuffType.POISONED_WINE] = 4,
    _j[Enums_1.BuffType.LIAN_PO_ATTACK] = 5,
    _j[Enums_1.BuffType.LIAN_PO_DEFEND] = 5,
    _j);
exports.BUFF_NODE_ZINDEX = BUFF_NODE_ZINDEX;
// 外显buff 同时显示
var NEED_SHOW_BUFF = (_k = {},
    _k[Enums_1.BuffType.SHIELD] = true,
    _k[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _k[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _k[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _k[Enums_1.BuffType.PARRY] = true,
    _k[Enums_1.BuffType.PARRY_001] = true,
    _k[Enums_1.BuffType.PARRY_102] = true,
    _k[Enums_1.BuffType.WITHSTAND] = true,
    _k[Enums_1.BuffType.JUMPSLASH_DAMAGE] = true,
    _k[Enums_1.BuffType.BEHEADED_GENERAL] = true,
    _k[Enums_1.BuffType.ANTICIPATION_DEFENSE] = true,
    _k[Enums_1.BuffType.ANTICIPATION_ATTACK] = true,
    _k[Enums_1.BuffType.DIZZINESS] = true,
    _k[Enums_1.BuffType.PARALYSIS] = true,
    _k[Enums_1.BuffType.PARALYSIS_UP] = true,
    _k[Enums_1.BuffType.WIRE_CHAIN] = true,
    _k[Enums_1.BuffType.SILENCE] = true,
    _k[Enums_1.BuffType.CHAOS] = true,
    _k[Enums_1.BuffType.POISONED_WINE] = true,
    _k[Enums_1.BuffType.LIAN_PO_ATTACK] = true,
    _k[Enums_1.BuffType.LIAN_PO_DEFEND] = true,
    _k);
exports.NEED_SHOW_BUFF = NEED_SHOW_BUFF;
// 外显buff 互斥显示
var NEED_MUTUAL_BUFF = (_l = {},
    _l[Enums_1.BuffType.ARMOR_PENETRATION] = true,
    _l[Enums_1.BuffType.INSPIRE] = true,
    _l[Enums_1.BuffType.INSPIRE_001] = true,
    _l[Enums_1.BuffType.WORTHY_MONARCH] = true,
    _l[Enums_1.BuffType.DESTROY_WEAPONS] = true,
    _l[Enums_1.BuffType.POISONING_MAX_HP] = true,
    _l[Enums_1.BuffType.POISONING_CUR_HP] = true,
    _l[Enums_1.BuffType.INFECTION_PLAGUE] = true,
    _l[Enums_1.BuffType.BLEED] = true,
    _l[Enums_1.BuffType.DAMAGE_INCREASE] = true,
    _l[Enums_1.BuffType.DAMAGE_REDUCE] = true,
    _l[Enums_1.BuffType.GOD_WAR] = true,
    _l[Enums_1.BuffType.FEAR] = true,
    _l[Enums_1.BuffType.TIMIDITY] = true,
    _l[Enums_1.BuffType.TIGER_MANIA] = true,
    _l[Enums_1.BuffType.IRREMOVABILITY] = true,
    _l[Enums_1.BuffType.OVERLORD] = true,
    _l[Enums_1.BuffType.IGNITION] = true,
    _l[Enums_1.BuffType.RAGE] = true,
    _l);
exports.NEED_MUTUAL_BUFF = NEED_MUTUAL_BUFF;
// 外显buff 类型转换
var BUFF_SHOW_TYPE_TRAN = (_m = {},
    _m[Enums_1.BuffType.POISONING_CUR_HP] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.INFECTION_PLAGUE] = Enums_1.BuffType.POISONING_MAX_HP,
    _m[Enums_1.BuffType.DAMAGE_REDUCE] = Enums_1.BuffType.DESTROY_WEAPONS,
    _m[Enums_1.BuffType.WORTHY_MONARCH] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.GOD_WAR] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.TIGER_MANIA] = Enums_1.BuffType.INSPIRE,
    _m[Enums_1.BuffType.IRREMOVABILITY] = Enums_1.BuffType.TIMIDITY,
    _m[Enums_1.BuffType.OVERLORD] = Enums_1.BuffType.TIMIDITY,
    _m);
exports.BUFF_SHOW_TYPE_TRAN = BUFF_SHOW_TYPE_TRAN;
// 护盾buff
var SHIELD_BUFF = (_o = {},
    _o[Enums_1.BuffType.SHIELD] = true,
    _o[Enums_1.BuffType.PROTECTION_SHIELD] = true,
    _o[Enums_1.BuffType.LOW_HP_SHIELD] = true,
    _o[Enums_1.BuffType.ATTACK_SHIELD] = true,
    _o[Enums_1.BuffType.SUCKBLOOD_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_001] = true,
    _o[Enums_1.BuffType.RODELERO_SHIELD_102] = true,
    _o[Enums_1.BuffType.BATTLE_BEGIN_SHIELD] = true,
    _o[Enums_1.BuffType.KUROU_SHIELD] = true,
    _o[Enums_1.BuffType.SUCK_SHIELD] = true,
    _o[Enums_1.BuffType.ABNEGATION_SHIELD] = true,
    _o[Enums_1.BuffType.LONGITUDINAL_CLEFT_SHIELD] = true,
    _o[Enums_1.BuffType.CRIMSONGOLD_SHIELD] = true,
    _o[Enums_1.BuffType.BLACK_IRON_STAFF_SHIELD] = true,
    _o);
exports.SHIELD_BUFF = SHIELD_BUFF;
// 战斗特效类型
// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾
var BATTLE_EFFECT_TYPE = {
    ATTACK: [10000002],
    DAMAGE_REDUCTION: [10000004],
    SHIELD: [10000005],
    VALOR: [10000002, 10000003],
    WISDOM_COURAGE: [10000002, 10000004],
    KUROU: [10000002, 10000005],
    SAND_CLOCK: [10000006],
    TONDEN: [114001],
};
exports.BATTLE_EFFECT_TYPE = BATTLE_EFFECT_TYPE;
// 聊天弹幕颜色
var CHAT_BARRAGE_COLOR = {
    0: '#FFFFFF',
    1: '#5BB8FF',
    2: '#FF81F7',
};
exports.CHAT_BARRAGE_COLOR = CHAT_BARRAGE_COLOR;
// 和大自然交换资源手续费
var REPLACEMENT_SERVICE_CHARGE = 60;
exports.REPLACEMENT_SERVICE_CHARGE = REPLACEMENT_SERVICE_CHARGE;
// 和大自然交换最少资源
var REPLACEMENT_MIN_RES_COUNT = 100;
exports.REPLACEMENT_MIN_RES_COUNT = REPLACEMENT_MIN_RES_COUNT;
// 每日置换次数
var REPLACEMENT_TODAY_COUNT_MAP = {
    0: 3,
    1: 3,
    2: 3,
};
exports.REPLACEMENT_TODAY_COUNT_MAP = REPLACEMENT_TODAY_COUNT_MAP;
// 各个资源占用运送的容量
var RES_TRANSIT_CAP = (_p = {},
    _p[Enums_1.CType.CEREAL] = 1,
    _p[Enums_1.CType.TIMBER] = 1,
    _p[Enums_1.CType.STONE] = 1,
    _p[Enums_1.CType.EXP_BOOK] = 100,
    _p[Enums_1.CType.IRON] = 100,
    _p[Enums_1.CType.UP_SCROLL] = 500,
    _p[Enums_1.CType.FIXATOR] = 500,
    _p);
exports.RES_TRANSIT_CAP = RES_TRANSIT_CAP;
// 加速招募倍数
var UP_RECRUIT_PAWN_MUL = 0.125;
exports.UP_RECRUIT_PAWN_MUL = UP_RECRUIT_PAWN_MUL;
// 多语言名字
var LANGUAGE_TEXT_LIST = [
    { lang: 'en', text: 'ENGLISH' },
    { lang: 'cn', text: '简体中文' },
    { lang: 'hk', text: '繁體(港澳)' },
    { lang: 'tw', text: '繁體(臺灣)' },
    { lang: 'jp', text: '日本語' },
    { lang: 'kr', text: '한국어' },
    { lang: 'idl', text: 'Bahasa Indonesia' },
    { lang: 'th', text: 'ภาษาไทย' },
    { lang: 'vi', text: 'Tiếng Việt' },
];
exports.LANGUAGE_TEXT_LIST = LANGUAGE_TEXT_LIST;
// 注销账号等待时间
var LOGOUT_MAX_DAY = 86400000 * 7;
exports.LOGOUT_MAX_DAY = LOGOUT_MAX_DAY;
// 联盟职位说明
var ALLI_JOB_DESC = {
    0: [1, 2, 3, 4, 5, 7, 8],
    1: [3, 4, 5, 7],
    2: [6, 8],
    10: [0],
};
exports.ALLI_JOB_DESC = ALLI_JOB_DESC;
// 职位数量
var ALLI_JOB_COUNT = {
    0: 1,
    1: 1,
    2: 2,
    10: 40,
};
exports.ALLI_JOB_COUNT = ALLI_JOB_COUNT;
// 开服多久内不可攻占
var NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3;
exports.NOT_OCCUPY_BY_SERVER_RUNTIME = NOT_OCCUPY_BY_SERVER_RUNTIME;
var NOT_OCCUPY_BY_MAX_LAND_COUNT = 100;
exports.NOT_OCCUPY_BY_MAX_LAND_COUNT = NOT_OCCUPY_BY_MAX_LAND_COUNT;
// 不同类型的区最多可玩几个区
var CONCURRENT_GAME_LIMIT = 1;
exports.CONCURRENT_GAME_LIMIT = CONCURRENT_GAME_LIMIT;
// 领地积分配置
var LAND_SCORE_CONF = {
    1: [[50, 2], [100, 1]],
    2: [[40, 4], [80, 2]],
    3: [[30, 6], [60, 3]],
    4: [[20, 8], [40, 4]],
    5: [[10, 10], [20, 5]],
};
exports.LAND_SCORE_CONF = LAND_SCORE_CONF;
// 多久才可以删除私聊
var REMOVE_PCHAT_TIME = 3600000 * 12;
exports.REMOVE_PCHAT_TIME = REMOVE_PCHAT_TIME;
// 攻占玩家领地要求最低距离
var OCCUPY_PLAYER_CELL_MIN_DIS = 5;
exports.OCCUPY_PLAYER_CELL_MIN_DIS = OCCUPY_PLAYER_CELL_MIN_DIS;
// 多少地可以无限制私聊
var NOLIMIT_PCHAT_MAX_LAND = 150;
exports.NOLIMIT_PCHAT_MAX_LAND = NOLIMIT_PCHAT_MAX_LAND;
// 聊天 显示时间的最大间隔
var SHOW_TIME_MAX_INTERVAL = 60000 * 1;
exports.SHOW_TIME_MAX_INTERVAL = SHOW_TIME_MAX_INTERVAL;
// 可申请添加好友最小地块数
var FRIENDS_MIN_LAND_COUNT = 100;
exports.FRIENDS_MIN_LAND_COUNT = FRIENDS_MIN_LAND_COUNT;
// 战斗预测费用
var BATTLE_FORECAST_COST = 30;
exports.BATTLE_FORECAST_COST = BATTLE_FORECAST_COST;
// 战斗预测免费次数
var BATTLE_FORECAST_FREE_COUNT = 5;
exports.BATTLE_FORECAST_FREE_COUNT = BATTLE_FORECAST_FREE_COUNT;
// 一键打开宝箱要求
var OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100;
exports.OPEN_ALL_TREASURE_MIN_LAND_COUNT = OPEN_ALL_TREASURE_MIN_LAND_COUNT;
// 最小可修改的行军速度
var CAN_MIN_MARCH_SPEED = 46;
exports.CAN_MIN_MARCH_SPEED = CAN_MIN_MARCH_SPEED;
// 一个季节持续的时间
var SEASON_DURATION_TIME = 3 * 86400000;
exports.SEASON_DURATION_TIME = SEASON_DURATION_TIME;
// 遗迹加速资源倍数
var ANCIENT_SUP_COST_MUL = 40;
exports.ANCIENT_SUP_COST_MUL = ANCIENT_SUP_COST_MUL;
// 遗迹加速时间
var ANCIENT_SUP_TIME = 6 * 60000;
exports.ANCIENT_SUP_TIME = ANCIENT_SUP_TIME;
// 聊天相关
var CHAT_MAX_COUNT = 50;
exports.CHAT_MAX_COUNT = CHAT_MAX_COUNT;
var CHAT_SEND_INTERVAL = 6000; //发送聊天的预期间隔 (毫秒)
exports.CHAT_SEND_INTERVAL = CHAT_SEND_INTERVAL;
var CHAT_TOLERATE_MAX_COUNT = 3; //最多容忍多少次在间隔内发送
exports.CHAT_TOLERATE_MAX_COUNT = CHAT_TOLERATE_MAX_COUNT;
var CHAT_REST_MAX_TIME = 30000; //如果太频繁就休息一下
exports.CHAT_REST_MAX_TIME = CHAT_REST_MAX_TIME;
var CHAT_BANNED_REST_MAX_TIME = 60000 * 10; //禁言休息时间
exports.CHAT_BANNED_REST_MAX_TIME = CHAT_BANNED_REST_MAX_TIME;
// 发送喇叭费用
var SEND_TRUMPET_COST = 50;
exports.SEND_TRUMPET_COST = SEND_TRUMPET_COST;
var SEND_TRUMPET_ACC_COST = 25;
exports.SEND_TRUMPET_ACC_COST = SEND_TRUMPET_ACC_COST;
// 多长时间可以取消报名
var SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000;
exports.SERVER_APPLY_CANCEL_CD = SERVER_APPLY_CANCEL_CD;
// 下次报名的等待时间
var NEXT_APPLY_CD = 6 * 1000;
exports.NEXT_APPLY_CD = NEXT_APPLY_CD;
// 点将一次的费用
var POINTSETS_ONE_COST = 10;
exports.POINTSETS_ONE_COST = POINTSETS_ONE_COST;
// 点击5次 金币费用
var POINTSETS_ONE_GOLD_COST = 598;
exports.POINTSETS_ONE_GOLD_COST = POINTSETS_ONE_GOLD_COST;
// 残卷合成画像 需要数量
var PORTRAYAL_COMP_NEED_COUNT = 3;
exports.PORTRAYAL_COMP_NEED_COUNT = PORTRAYAL_COMP_NEED_COUNT;
// 还原画像费用
var RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50;
exports.RESTORE_PORTRAYAL_WAR_TOKEN_COST = RESTORE_PORTRAYAL_WAR_TOKEN_COST;
var RESTORE_PORTRAYAL_GOLD_COST = 598;
exports.RESTORE_PORTRAYAL_GOLD_COST = RESTORE_PORTRAYAL_GOLD_COST;
// 购买自选英雄费用 元宝
var BUY_OPT_HERO_COST = 999;
exports.BUY_OPT_HERO_COST = BUY_OPT_HERO_COST;
// 英雄自选礼包
var HERO_OPT_GIFT = {
    // 陈到, 徐盛, 张辽
    1: [310101, 320101, 340101],
    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃
    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],
    // 3: 全自选
    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃
    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],
};
exports.HERO_OPT_GIFT = HERO_OPT_GIFT;
// 英雄复活时间
var HERO_REVIVES_TIME = 3600000 * 5;
exports.HERO_REVIVES_TIME = HERO_REVIVES_TIME;
// 英雄槽位等级开启条件
var HERO_SLOT_LV_COND = [1, 10, 20];
exports.HERO_SLOT_LV_COND = HERO_SLOT_LV_COND;
// 养由基召唤时的对应等级
var SUMMON_LV = {
    1: 1,
    2: 2,
    3: 4,
    4: 6,
    5: 8,
    6: 10,
};
exports.SUMMON_LV = SUMMON_LV;
// 默认的宠物id
var DEFAULT_PET_ID = 4101;
exports.DEFAULT_PET_ID = DEFAULT_PET_ID;
// 矛
var SPEAR_PAWN_ID = 3701;
exports.SPEAR_PAWN_ID = SPEAR_PAWN_ID;
// 火
var FIRE_PAWN_ID = 3702;
exports.FIRE_PAWN_ID = FIRE_PAWN_ID;
// 资源
var RES_MAP = (_q = {},
    _q[Enums_1.CType.CEREAL] = true,
    _q[Enums_1.CType.TIMBER] = true,
    _q[Enums_1.CType.STONE] = true,
    _q[Enums_1.CType.BASE_RES] = true,
    _q[Enums_1.CType.EXP_BOOK] = true,
    _q[Enums_1.CType.IRON] = true,
    _q[Enums_1.CType.UP_SCROLL] = true,
    _q[Enums_1.CType.FIXATOR] = true,
    _q);
exports.RES_MAP = RES_MAP;
// 最多可标记多少个
var MAX_MAP_MARK_COUNT = 10;
exports.MAX_MAP_MARK_COUNT = MAX_MAP_MARK_COUNT;
// 申请联盟个数限制
var ALLI_APPLY_MAX_COUNT = 3;
exports.ALLI_APPLY_MAX_COUNT = ALLI_APPLY_MAX_COUNT;
// 大厅模式对应的底部
var LOBBY_MODE_BUTTOM_NAME = (_r = {},
    _r[Enums_1.LobbyModeType.FREE] = 'team',
    _r[Enums_1.LobbyModeType.NEWBIE] = 'team',
    _r[Enums_1.LobbyModeType.RANKED] = 'team',
    _r[Enums_1.LobbyModeType.SNAIL_ISLE] = 'twomiles',
    _r);
exports.LOBBY_MODE_BUTTOM_NAME = LOBBY_MODE_BUTTOM_NAME;
// 斜度
var SKEW_ANGLE = 45;
exports.SKEW_ANGLE = SKEW_ANGLE;
// 斜着的外宽高
var SKEW_SIZE = cc.size(16, 8);
exports.SKEW_SIZE = SKEW_SIZE;
// 斜着的内宽高 32 16
var SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5);
exports.SKEW_SIZE_HALF = SKEW_SIZE_HALF;
// 段位商城的兵符配置
var RANK_SHOP_WAR_TOKEN_CONFIG = [
    { warToken: 10, coin: 10 },
    { warToken: 100, coin: 100 },
    { warToken: 1000, coin: 1000 },
    { warToken: 10000, coin: 10000 },
];
exports.RANK_SHOP_WAR_TOKEN_CONFIG = RANK_SHOP_WAR_TOKEN_CONFIG;
// 战斗的血条颜色
var BATTLE_HPBAR_COLOR = {
    m: { bar: '#8BE273', bg: '#162D20' },
    f: { bar: '#6DB5E2', bg: '#121D3A' },
    0: { bar: '#EE2A4A', bg: '#3B1316' },
    1: { bar: '#FF64B8', bg: '#41142C' },
    2: { bar: '#AD64FF', bg: '#281240' },
    3: { bar: '#FF9648', bg: '#4A2B14' },
};
exports.BATTLE_HPBAR_COLOR = BATTLE_HPBAR_COLOR;
// 战斗的火焰颜色
var BATTLE_FIRE_COLOR = {
    m: '#53B977',
    f: '#40A4E9',
    0: '#B90900',
    1: '#FF76F7',
    2: '#AD64FF',
    3: '#FFA836',
};
exports.BATTLE_FIRE_COLOR = BATTLE_FIRE_COLOR;
// 战令价格配置
var RECHARGE_BATTLE_PASS = 'jwm_up_book'; // $8.99
exports.RECHARGE_BATTLE_PASS = RECHARGE_BATTLE_PASS;
// 战令经验购买配置
var RECHARGE_BATTLE_PASS_EXP = [50, 100]; // 50元宝购买100经验
exports.RECHARGE_BATTLE_PASS_EXP = RECHARGE_BATTLE_PASS_EXP;
// 有奖问卷调查id
var PRIZE_QUESTION_ID = 99900001;
exports.PRIZE_QUESTION_ID = PRIZE_QUESTION_ID;
// 有奖问卷调查期限
var PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00'];
exports.PRIZE_QUESTION_TIME = PRIZE_QUESTION_TIME;
// 打开允许通知弹窗的公共CD
var NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000; // 24小时
exports.NOTICE_PERMISSION_CD = NOTICE_PERMISSION_CD;
// 每日屯田次数
var TODAY_TONDEN_MAX_COUNT = 10;
exports.TODAY_TONDEN_MAX_COUNT = TODAY_TONDEN_MAX_COUNT;
// 屯田奖励点消耗倍数
var TONDEN_STAMINA_MUL = 3;
exports.TONDEN_STAMINA_MUL = TONDEN_STAMINA_MUL;
// 医馆伤兵上限
var HOSPITAL_PAWN_LIMIT = 200;
exports.HOSPITAL_PAWN_LIMIT = HOSPITAL_PAWN_LIMIT;
// 各等级士兵战败后回馆概率(百分比)
var GO_HOSPITAL_CHANCE = {
    1: 10,
    2: 20,
    3: 30,
    4: 40,
    5: 50,
    6: 60,
};
exports.GO_HOSPITAL_CHANCE = GO_HOSPITAL_CHANCE;
// 画像的天选几率
var PORTRAYAL_CHOSENONE_ODDS = 0.005;
exports.PORTRAYAL_CHOSENONE_ODDS = PORTRAYAL_CHOSENONE_ODDS;
// 研究类型转评论类型
var STUDY_TO_BOOKTYPE = (_s = {},
    _s[Enums_1.StudyType.POLICY] = Enums_1.BookCommentType.POLICY,
    _s[Enums_1.StudyType.PAWN] = Enums_1.BookCommentType.PAWN,
    _s[Enums_1.StudyType.EQUIP] = Enums_1.BookCommentType.EQUIP,
    _s[Enums_1.StudyType.EXCLUSIVE] = Enums_1.BookCommentType.EQUIP,
    _s);
exports.STUDY_TO_BOOKTYPE = STUDY_TO_BOOKTYPE;
// 盟主投票最大次数
var ALLI_LEADER_VOTE_MAX_COUNT = 4;
exports.ALLI_LEADER_VOTE_MAX_COUNT = ALLI_LEADER_VOTE_MAX_COUNT;
// 招募动态资源每级系数
var PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10];
exports.PAWN_COST_LV_LIST = PAWN_COST_LV_LIST;
// 工厂解锁配置
var FACTORY_SLOT_CONF = [5];
exports.FACTORY_SLOT_CONF = FACTORY_SLOT_CONF;
// 摄像机背景颜色
var CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3'];
exports.CAMERA_BG_COLOR = CAMERA_BG_COLOR;
//
var MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2'];
exports.MAP_MASK_ITEM_COLOR = MAP_MASK_ITEM_COLOR;
var MAP_MASK_ITEM_OPACITY = [63, 42, 38, 63];
exports.MAP_MASK_ITEM_OPACITY = MAP_MASK_ITEM_OPACITY;
var MAP_MASK_ITEM_OPACITY_GOLD = [45, 21, 20, 32];
exports.MAP_MASK_ITEM_OPACITY_GOLD = MAP_MASK_ITEM_OPACITY_GOLD;
// 区域内的地面颜色
var AREA_DI_COLOR_CONF = [
    // 春
    {
        0: { bg: '#D8C069', battle: ['#ECE5A2', '#E0DA94'], build: '#D7BD65' },
        3: { bg: '#AFC864', battle: ['#F4E88D', '#F0DC84'], build: '#D6BD67' },
        4: { bg: '#AFC864', battle: ['#DCEC95', '#D0E186'], build: '#AFC864' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#AFC864', battle: ['#DCDF7E', '#CCD775'], build: '#EBE38F' },
    },
    // 夏
    {
        0: { bg: '#D8C069', battle: ['#E8E6AC', '#DEDB9F'], build: '#D8C069' },
        3: { bg: '#88CB6E', battle: ['#F4E88D', '#F0DC84'], build: '#D4BD6A' },
        4: { bg: '#88CA6E', battle: ['#DCEC95', '#D0E186'], build: '#86C96D' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#88CA6E', battle: ['#D7DF85', '#C7D77C'], build: '#EAE195' },
    },
    // 秋
    {
        0: { bg: '#D8C069', battle: ['#EBDD8F', '#E5CF7B'], build: '#EDE399' },
        3: { bg: '#E1B668', battle: ['#F4E88D', '#F0DC84'], build: '#E1B567' },
        4: { bg: '#E1B668', battle: ['#DCEC95', '#D0E186'], build: '#DAC26A' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' },
        10: { bg: '#E1B668', battle: ['#EBDD8F', '#E5CF7B'], build: '#EDE399' },
    },
    // 冬
    {
        0: { bg: '#C5ECE8', battle: ['#E8E6AC', '#DEDB9F'], build: '#D8BF67' },
        3: { bg: '#A6DFE0', battle: ['#F4E88D', '#F0DC84'], build: '#A6DFE0' },
        4: { bg: '#A6DFE0', battle: ['#DCEC95', '#D0E186'], build: '#A6DFE0' },
        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#D8C069' },
        10: { bg: '#A6DFE0', battle: ['#C7ECED', '#B4E0E4'], build: '#D9EEEC' },
    },
];
exports.AREA_DI_COLOR_CONF = AREA_DI_COLOR_CONF;
// 难度底板颜色
var DIFFICULTY_BG_COLOR = {
    1: '#81A514',
    2: '#6683AB',
    3: '#9C58BF',
    4: '#CE59A0',
    5: '#C34B3F',
};
exports.DIFFICULTY_BG_COLOR = DIFFICULTY_BG_COLOR;
// 月卡固定值
var MONTH_CARD = [
    {
        TYPE: Enums_1.MonthlyCardType.SALE,
        FIRST: 100,
        DURATION: 31,
        DAY: 30,
        EXTRA: 0,
        PRODUCT_IDS_ANDROID: ['jwm-card-month', 'jwm-card-quarter'],
        PRODUCT_IDS_IOS: ['jwmCardMonth', 'jwmCardQuarter'],
        RECHARGES: ['jwm_card_once'],
        RESTORES: ['jwm_card', 'jwmCard'],
    },
    {
        TYPE: Enums_1.MonthlyCardType.SUPER,
        FIRST: 400,
        DURATION: 31,
        DAY: 80,
        EXTRA: 5,
        PRODUCT_IDS_ANDROID: ['jwm-ad-free-month', 'jwm-ad-free-quarter'],
        PRODUCT_IDS_IOS: ['jwmAdFreeMonth', 'jwmSuperCardQuarter'],
        RECHARGES: ['jwm_super_card_once'],
        RESTORES: ['jwm_ad_free', 'jwmAdFree', 'jwmSuperCard'],
    },
];
exports.MONTH_CARD = MONTH_CARD;
// 需要返回的免费
var RETURNED_FREE_TYPE = (_t = {},
    _t[Enums_1.CType.UP_RECRUIT] = true,
    _t[Enums_1.CType.FREE_RECRUIT] = true,
    _t[Enums_1.CType.FREE_LEVING] = true,
    _t[Enums_1.CType.FREE_CURE] = true,
    _t);
exports.RETURNED_FREE_TYPE = RETURNED_FREE_TYPE;
// 成就颜色
var ACHIEVEMENT_COLOR = {
    1: '#7A6364',
    2: '#559743',
    3: '#2FA895',
    4: '#D2772E',
    5: '#5A6CD5',
    6: '#C252EC',
    7: '#D93149',
};
exports.ACHIEVEMENT_COLOR = ACHIEVEMENT_COLOR;
// 每个等级治疗士兵消耗粮食所占百分比 k=>lv v=>百分比
var CURE_RES_PARAM_MAP = {
    1: 0.2,
    2: 0.4,
    3: 0.5,
    4: 0.6,
    5: 0.7,
    6: 0.8,
};
exports.CURE_RES_PARAM_MAP = CURE_RES_PARAM_MAP;
// 每个等级治疗士兵消耗时间所占百分比 k=>lv v=>百分比
var CURE_TIME_PARAM_MAP = {
    1: 0.2,
    2: 0.4,
    3: 0.6,
    4: 0.8,
    5: 0.9,
    6: 1,
};
exports.CURE_TIME_PARAM_MAP = CURE_TIME_PARAM_MAP;
// 系数基础值
var PAWN_COST_FACTOR_BASE_VALUE = 250;
exports.PAWN_COST_FACTOR_BASE_VALUE = PAWN_COST_FACTOR_BASE_VALUE;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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