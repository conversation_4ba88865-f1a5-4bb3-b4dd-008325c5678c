{"version": 3, "sources": ["assets\\app\\script\\model\\guide\\NoviceConfig.ts"], "names": [], "mappings": ";;;;;;;AAAA,UAAU;AACV,IAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AAgHjC,0CAAe;AA/GnB,OAAO;AACP,IAAM,qBAAqB,GAAG,IAAI,CAAA;AA+G9B,sDAAqB;AA9GzB,IAAM,4BAA4B,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AA+GnD,oEAA4B;AA9GhC,IAAM,sBAAsB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;AA+GvF,wDAAsB;AA7G1B,QAAQ;AACR,IAAM,2BAA2B,GAAG,GAAG,CAAA;AA2HnC,kEAA2B;AA1H/B,IAAM,kCAAkC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AA2HtD,gFAAkC;AA1HtC,IAAM,4BAA4B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;AA2H7F,oEAA4B;AAzHhC,WAAW;AACX,IAAM,mBAAmB,GAAG;IACxB,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;CACb,CAAA;AAgGG,kDAAmB;AA/FvB,YAAY;AACZ,IAAM,qBAAqB,GAAG,EAAE,CAAA;AA2G5B,sDAAqB;AAzGzB,gBAAgB;AAChB,IAAM,qBAAqB,GAAG,EAAE,CAAA;AA4F5B,sDAAqB;AA3FzB,UAAU;AACV,IAAM,oBAAoB,GAAG,CAAC,CAAA;AA2F1B,oDAAoB;AAzFxB,YAAY;AACZ,IAAM,sBAAsB,GAAG,IAAI,CAAA;AA2F/B,wDAAsB;AAzF1B,UAAU;AACV,IAAM,uBAAuB,GAAG,CAAC,CAAC,CAAA;AAyF9B,0DAAuB;AAxF3B,YAAY;AACZ,IAAM,8BAA8B,GAAG,EAAE,CAAA;AAyFrC,wEAA8B;AAxFlC,IAAM,wBAAwB,GAAG,EAAE,CAAA;AAuF/B,4DAAwB;AArF5B,YAAY;AACZ,IAAM,4BAA4B,GAAG,CAAC,CAAA;AAuFlC,oEAA4B;AAtFhC,IAAM,sBAAsB,GAAG,CAAC,CAAA;AAqF5B,wDAAsB;AApF1B,YAAY;AACZ,IAAM,6BAA6B,GAAG,CAAC,CAAA;AAsFnC,sEAA6B;AArFjC,IAAM,uBAAuB,GAAG,CAAC,CAAA;AAoF7B,0DAAuB;AAnF3B,YAAY;AACZ,IAAM,yBAAyB,GAAG,CAAC,CAAA;AA+F/B,8DAAyB;AA7F7B,eAAe;AACf,IAAM,8BAA8B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAuE1E,wEAA8B;AAtElC,eAAe;AACf,IAAM,4BAA4B,GAAG;IACjC,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,EAAE;CACX,CAAA;AAmEG,oEAA4B;AAjEhC,oBAAoB;AACpB,IAAM,yBAAyB,GAAG;IAC9B,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;CACR,CAAA;AAsEG,8DAAyB;AArE7B,iCAAiC;AACjC,IAAM,yBAAyB,GAAG;IAC9B,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;CACT,CAAA;AAqEG,8DAAyB;AAnE7B,iCAAiC;AACjC,IAAM,0BAA0B,GAAG;IAC/B,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;CACT,CAAA;AA4DG,gEAA0B;AA3D9B,IAAM,sBAAsB,GAAG,GAAG,CAAA,CAAA,SAAS;AAuDvC,wDAAsB;AAtD1B,IAAM,4BAA4B,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA,CAAA,YAAY;AAqDnF,oEAA4B;AAnDhC,IAAM,uBAAuB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA,CAAA,UAAU;AAiDxD,0DAAuB;AAhD3B,IAAM,mBAAmB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA,CAAA,eAAe;AA+C3E,kDAAmB;AA9CvB,IAAM,2BAA2B,GAAG,IAAI,CAAA,CAAA,UAAU;AAgD9C,kEAA2B;AA/C/B,IAAM,8BAA8B,GAAG,GAAG,CAAA,CAAA,WAAW;AAkDjD,wEAA8B;AAhDlC,oDAAoD;AACpD,IAAM,+BAA+B,GAAG;IACpC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3B,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3B,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,CAAC,CAAC,8BAA8B,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,8BAA8B,CAAC,CAAC;IAClE,KAAK,EAAE,EAAE;IACT,KAAK,EAAE,EAAE;CACZ,CAAA;AAqCG,0EAA+B;AAnCnC,IAAM,yBAAyB,GAAG,EAAE,CAAA,CAAA,QAAQ;AAoCxC,8DAAyB;AAnC7B,IAAM,6BAA6B,GAAG,CAAC,CAAA,CAAA,gBAAgB;AAoCnD,sEAA6B", "file": "", "sourceRoot": "/", "sourcesContent": ["// 新手村地图大小\nconst NOVICE_MAP_SIZE = cc.v2(50, 50)\n// 主城位置\nconst NOVICE_MAINCITY_INDEX = 1275\nconst NOVICE_MAINCITY_OTHER_INDEXS = [1276, 1325, 1326]\nconst NOVICE_MAINCITY_POINTS = [cc.v2(25, 25), cc.v2(25, 26), cc.v2(26, 25), cc.v2(26, 26)]\n\n//敌人主城位置\nconst NOVICE_ENEMY_MAINCITY_INDEX = 870\nconst NOVICE_ENEMY_MAINCITY_OTHER_INDEXS = [871, 920, 921]\nconst NOVICE_ENEMY_MAINCITY_POINTS = [cc.v2(20, 17), cc.v2(21, 17), cc.v2(20, 18), cc.v2(21, 18)]\n\n// 新手村忽略的建筑\nconst NOVICE_IGNORE_BUILD = {\n    2010: true,\n    2011: true,\n    2005: true,\n    2006: true,\n    2012: true,\n    2013: true,\n    2015: true,\n}\n// 新手村主城最大等级\nconst NOVICE_MAIN_MAX_LEVEL = 10\n\n// 第一次战斗时 移动加速倍数\nconst NOVICE_FIRST_MOVE_MUL = 30\n// 新手村行军倍数\nconst NOVICE_ARMY_MOVE_MUL = 5\n\n// 新手村建筑升级倍速\nconst NOVICE_BUILD_SPEED_MUL = 6.67\n\n// -1表示不限制\nconst NOVICE_RECRUIT_UP_COUNT = -1\n// 新手村招募加速倍数\nconst NOVICE_FIRST_RECRUIT_SPEED_MUL = 20\nconst NOVICE_RECRUIT_SPEED_MUL = 20\n\n// 新手村打造加速倍数\nconst NOVICE_FIRST_FORGE_SPEED_MUL = 5\nconst NOVICE_FORGE_SPEED_MUL = 5\n// 新手村建造加速倍数\nconst NOVICE_FIRST_BUILD_CREATE_MUL = 1\nconst NOVICE_BUILD_CREATE_MUL = 1\n// 新手村医疗加速倍数\nconst NOVICE_HOSPITAL_SPEED_MUL = 4\n\n// 第一次战斗时 士兵的位置\nconst NOVICE_FIRST_BATTLE_PAWN_POINT = [cc.v2(4, 4), cc.v2(6, 3), cc.v2(6, 4)]\n// 第一次战斗时 野怪的血量\nconst NOVICE_FIRST_BATTLE_ENEMY_HP = {\n    4101: 22, //野猪\n    4102: 30, //野狼\n}\n\n// 各等级士兵战败后回馆概率(百分比)\nconst NOVICE_GO_HOSPITAL_CHANCE = {\n    1: 10,\n    2: 20,\n    3: 30,\n    4: 40,\n    5: 50,\n    6: 60,\n}\n// 每个等级治疗士兵消耗粮食所占百分比 k=>lv v=>百分比\nconst NOVICE_CURE_RES_PARAM_MAP = {\n    1: 0.2,\n    2: 0.4,\n    3: 0.5,\n    4: 0.6,\n    5: 0.7,\n    6: 0.8,\n}\n\n// 每个等级治疗士兵消耗时间所占百分比 k=>lv v=>百分比\nconst NOVICE_CURE_TIME_PARAM_MAP = {\n    1: 0.2,\n    2: 0.4,\n    3: 0.5,\n    4: 0.6,\n    5: 0.7,\n    6: 0.8,\n}\nconst NOVICE_INJURY_MAX_RATE = 100//伤兵最大治疗率\nconst NOVICE_INJURY_LV_REDUCE_RATE = { 1: 1, 2: 5, 3: 5, 4: 5, 5: 5, 6: 5 }//伤兵死亡次数减少概率\n\nconst NOVICE_FIRST_PAWN_SLOTS = [3201, 3204, 3206]//新手首个士兵槽位\nconst NOVICE_POLICY_SLOTS = [1001, 1042, 1044, 1009, 1014, 1028]//新手引导固定这6个政策可选\nconst NOVICE_EQUIP_SLOTS_FIRST_ID = 6013//首个装备-流星锤\nconst NOVICE_FIRST_STONE_TREASURE_ID = 511//石头地首个宝箱id\n\n//土地宝箱奖励，1：荒地，3：粮食地，4：木头地，5：石头地，key：类型_等级，value：宝箱ID\nconst NOVICE_LAND_TREASURE_REWARD_MAP = {\n    '1_1': [1002, [1006, 1007], [1008, 1009]],\n    '3_2': [[1010, 1011, 1012]],\n    '3_3': [],\n    '3_4': [],\n    '3_5': [],\n    '4_2': [[1013, 1014, 1015]],\n    '4_3': [],\n    '4_4': [],\n    '4_5': [],\n    '5_2': [[NOVICE_FIRST_STONE_TREASURE_ID, 1016, 1017], [1018, 1019]],\n    '5_3': [[502, 502, 502, 502, 502, NOVICE_FIRST_STONE_TREASURE_ID]],\n    '5_4': [],\n    '5_5': [],\n}\n\nconst NOVICE_DEFAULT_GOLD_COUNT = 30//新手默认金币\nconst NOVICE_DEFAULT_WARTOKEN_COUNT = 0//新手默认兵符，额外增加的兵符\n\nexport {\n    NOVICE_MAP_SIZE,\n    NOVICE_MAINCITY_INDEX,\n    NOVICE_MAINCITY_OTHER_INDEXS,\n    NOVICE_MAINCITY_POINTS,\n    NOVICE_IGNORE_BUILD,\n    NOVICE_FIRST_MOVE_MUL,\n    NOVICE_ARMY_MOVE_MUL,\n    NOVICE_FIRST_BATTLE_PAWN_POINT,\n    NOVICE_FIRST_BATTLE_ENEMY_HP,\n    NOVICE_BUILD_SPEED_MUL,\n    NOVICE_RECRUIT_UP_COUNT,\n    NOVICE_RECRUIT_SPEED_MUL,\n    NOVICE_FIRST_RECRUIT_SPEED_MUL,\n    NOVICE_FORGE_SPEED_MUL,\n    NOVICE_FIRST_FORGE_SPEED_MUL,\n    NOVICE_BUILD_CREATE_MUL,\n    NOVICE_FIRST_BUILD_CREATE_MUL,\n    NOVICE_MAIN_MAX_LEVEL,\n    NOVICE_ENEMY_MAINCITY_INDEX,\n    NOVICE_ENEMY_MAINCITY_OTHER_INDEXS,\n    NOVICE_ENEMY_MAINCITY_POINTS,\n    NOVICE_GO_HOSPITAL_CHANCE,\n    NOVICE_POLICY_SLOTS,\n    NOVICE_FIRST_PAWN_SLOTS,\n    NOVICE_EQUIP_SLOTS_FIRST_ID,\n    NOVICE_INJURY_LV_REDUCE_RATE,\n    NOVICE_INJURY_MAX_RATE,\n    NOVICE_FIRST_STONE_TREASURE_ID,\n    NOVICE_HOSPITAL_SPEED_MUL,\n    NOVICE_CURE_RES_PARAM_MAP,\n    NOVICE_CURE_TIME_PARAM_MAP,\n    NOVICE_LAND_TREASURE_REWARD_MAP,\n    NOVICE_DEFAULT_GOLD_COUNT,\n    NOVICE_DEFAULT_WARTOKEN_COUNT,\n}"]}