{"version": 3, "sources": ["assets\\app\\script\\view\\cmpt\\common\\SelectArrowsCmpt.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAM,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,OAAO;AAEP;IAA8C,oCAAY;IAA1D;QAAA,qEAqBC;QAnBW,WAAK,GAAW,EAAE,CAAA;QAClB,WAAK,GAAc,EAAE,CAAA;;IAkBjC,CAAC;IAhBG,iCAAM,GAAN;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAA;IAChE,CAAC;IAED,iCAAM,GAAN,UAAO,EAAU;QACb,IAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,IAAI,EAAE,EAAT,CAAS,CAAC,CAAA;QAClC,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACxB,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE;YACX,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;YACR,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAA;SACnB;aAAM,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE;YACjB,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;YACR,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAA;SACnB;IACL,CAAC;IApBgB,gBAAgB;QADpC,OAAO;OACa,gBAAgB,CAqBpC;IAAD,uBAAC;CArBD,AAqBC,CArB6C,EAAE,CAAC,SAAS,GAqBzD;kBArBoB,gBAAgB", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass, property } = cc._decorator;\r\n\r\n// 箭头选择\r\n@ccclass\r\nexport default class SelectArrowsCmpt extends cc.Component {\r\n\r\n    private speed: number = 13\r\n    private items: cc.Node[] = []\r\n\r\n    onLoad() {\r\n        this.items = this.node.children.map(m => m.FindChild('val'))\r\n    }\r\n\r\n    update(dt: number) {\r\n        const sx = dt * this.speed\r\n        this.items.forEach(m => m.x += sx)\r\n        const it = this.items[0]\r\n        if (it.x >= 4) {\r\n            it.x = 4\r\n            this.speed *= -1\r\n        } else if (it.x < 0) {\r\n            it.x = 0\r\n            this.speed *= -1\r\n        }\r\n    }\r\n}"]}