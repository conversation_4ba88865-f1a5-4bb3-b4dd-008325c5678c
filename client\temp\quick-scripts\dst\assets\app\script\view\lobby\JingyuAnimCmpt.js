
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/JingyuAnimCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '29fbeekCyJF3YBJTxbbzFy8', 'JingyuAnimCmpt');
// app/script/view/lobby/JingyuAnimCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
/**
 * 鲸鱼动画
 */
var JingyuAnimCmpt = /** @class */ (function (_super) {
    __extends(JingyuAnimCmpt, _super);
    function JingyuAnimCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.intervalRange = [];
        _this.interval = 0;
        _this.elapsed = 0;
        return _this;
    }
    JingyuAnimCmpt.prototype.onLoad = function () {
        this.initInterval();
    };
    JingyuAnimCmpt.prototype.initInterval = function () {
        this.interval = ut.random(this.intervalRange[0], this.intervalRange[1]);
        this.elapsed = 0;
        this.Component(cc.Sprite).spriteFrame = null;
    };
    JingyuAnimCmpt.prototype.update = function (dt) {
        var _this = this;
        if (this.interval === -1) {
            return;
        }
        else if (this.interval === 0) {
            this.initInterval();
        }
        this.elapsed += dt;
        if (this.elapsed >= this.interval) {
            this.interval = -1;
            this.Component(cc.Animation).playAsync().then(function () {
                if (_this.isValid) {
                    _this.interval = 0;
                }
            });
        }
    };
    __decorate([
        property([cc.Integer])
    ], JingyuAnimCmpt.prototype, "intervalRange", void 0);
    JingyuAnimCmpt = __decorate([
        ccclass
    ], JingyuAnimCmpt);
    return JingyuAnimCmpt;
}(cc.Component));
exports.default = JingyuAnimCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGxvYmJ5XFxKaW5neXVBbmltQ21wdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBTSxJQUFBLEtBQThCLEVBQUUsQ0FBQyxVQUFVLEVBQXpDLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBQSxFQUFFLElBQUksVUFBa0IsQ0FBQztBQUVsRDs7R0FFRztBQUVIO0lBQTRDLGtDQUFZO0lBQXhEO1FBQUEscUVBa0NDO1FBL0JXLG1CQUFhLEdBQWEsRUFBRSxDQUFBO1FBRTVCLGNBQVEsR0FBVyxDQUFDLENBQUE7UUFDcEIsYUFBTyxHQUFXLENBQUMsQ0FBQTs7SUE0Qi9CLENBQUM7SUExQkcsK0JBQU0sR0FBTjtRQUNJLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQTtJQUN2QixDQUFDO0lBRU8scUNBQVksR0FBcEI7UUFDSSxJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDdkUsSUFBSSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUE7UUFDaEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQTtJQUNoRCxDQUFDO0lBRUQsK0JBQU0sR0FBTixVQUFPLEVBQVU7UUFBakIsaUJBZUM7UUFkRyxJQUFJLElBQUksQ0FBQyxRQUFRLEtBQUssQ0FBQyxDQUFDLEVBQUU7WUFDdEIsT0FBTTtTQUNUO2FBQU0sSUFBSSxJQUFJLENBQUMsUUFBUSxLQUFLLENBQUMsRUFBRTtZQUM1QixJQUFJLENBQUMsWUFBWSxFQUFFLENBQUE7U0FDdEI7UUFDRCxJQUFJLENBQUMsT0FBTyxJQUFJLEVBQUUsQ0FBQTtRQUNsQixJQUFJLElBQUksQ0FBQyxPQUFPLElBQUksSUFBSSxDQUFDLFFBQVEsRUFBRTtZQUMvQixJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQyxDQUFBO1lBQ2xCLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFNBQVMsQ0FBQyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQztnQkFDMUMsSUFBSSxLQUFJLENBQUMsT0FBTyxFQUFFO29CQUNkLEtBQUksQ0FBQyxRQUFRLEdBQUcsQ0FBQyxDQUFBO2lCQUNwQjtZQUNMLENBQUMsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBOUJEO1FBREMsUUFBUSxDQUFDLENBQUMsRUFBRSxDQUFDLE9BQU8sQ0FBQyxDQUFDO3lEQUNhO0lBSG5CLGNBQWM7UUFEbEMsT0FBTztPQUNhLGNBQWMsQ0FrQ2xDO0lBQUQscUJBQUM7Q0FsQ0QsQUFrQ0MsQ0FsQzJDLEVBQUUsQ0FBQyxTQUFTLEdBa0N2RDtrQkFsQ29CLGNBQWMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5LCBtZW51IH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuLyoqXHJcbiAqIOmyuOmxvOWKqOeUu1xyXG4gKi9cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgSmluZ3l1QW5pbUNtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIEBwcm9wZXJ0eShbY2MuSW50ZWdlcl0pXHJcbiAgICBwcml2YXRlIGludGVydmFsUmFuZ2U6IG51bWJlcltdID0gW11cclxuXHJcbiAgICBwcml2YXRlIGludGVydmFsOiBudW1iZXIgPSAwXHJcbiAgICBwcml2YXRlIGVsYXBzZWQ6IG51bWJlciA9IDBcclxuXHJcbiAgICBvbkxvYWQoKSB7XHJcbiAgICAgICAgdGhpcy5pbml0SW50ZXJ2YWwoKVxyXG4gICAgfVxyXG5cclxuICAgIHByaXZhdGUgaW5pdEludGVydmFsKCkge1xyXG4gICAgICAgIHRoaXMuaW50ZXJ2YWwgPSB1dC5yYW5kb20odGhpcy5pbnRlcnZhbFJhbmdlWzBdLCB0aGlzLmludGVydmFsUmFuZ2VbMV0pXHJcbiAgICAgICAgdGhpcy5lbGFwc2VkID0gMFxyXG4gICAgICAgIHRoaXMuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSBudWxsXHJcbiAgICB9XHJcblxyXG4gICAgdXBkYXRlKGR0OiBudW1iZXIpIHtcclxuICAgICAgICBpZiAodGhpcy5pbnRlcnZhbCA9PT0gLTEpIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmludGVydmFsID09PSAwKSB7XHJcbiAgICAgICAgICAgIHRoaXMuaW5pdEludGVydmFsKClcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5lbGFwc2VkICs9IGR0XHJcbiAgICAgICAgaWYgKHRoaXMuZWxhcHNlZCA+PSB0aGlzLmludGVydmFsKSB7XHJcbiAgICAgICAgICAgIHRoaXMuaW50ZXJ2YWwgPSAtMVxyXG4gICAgICAgICAgICB0aGlzLkNvbXBvbmVudChjYy5BbmltYXRpb24pLnBsYXlBc3luYygpLnRoZW4oKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuaW50ZXJ2YWwgPSAwXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il19