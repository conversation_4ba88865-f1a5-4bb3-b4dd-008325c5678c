
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseMvcCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e7f8e+k99xDjZ7+XRvP5YYO', 'BaseMvcCtrl');
// app/core/base/BaseMvcCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var CmptTypeFix = {
    n: { fix: 'Node_', type: cc.Node },
    l: { fix: 'Lbl_', type: cc.Label },
    s: { fix: 'Spr_', type: cc.Sprite },
    b: { fix: 'Btn_', type: cc.Button },
    t: { fix: 'Tge_', type: cc.Toggle },
    a: { fix: 'Ani_', type: cc.Animation },
    sv: { fix: 'Sv_', type: cc.ScrollView },
    rt: { fix: 'Rt_', type: cc.RichText },
    tc: { fix: 'Tc_', type: cc.ToggleContainer },
    sp: { fix: 'Sp_', type: sp.Skeleton },
    eb: { fix: 'Eb_', type: cc.EditBox },
};
var EventTypeFix = {
    be: cc.Button,
    te: cc.Toggle,
    se: cc.Slider,
    tce: cc.ToggleContainer,
    nbe: cc.Button,
    ebee: cc.EditBox,
};
// 所有控制器的基础类
var BaseMvcCtrl = /** @class */ (function (_super) {
    __extends(BaseMvcCtrl, _super);
    function BaseMvcCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._isLoadProperty = false;
        return _this;
    }
    // 预加载
    BaseMvcCtrl.prototype.__preload = function () {
        this.loadProperty();
    };
    // 加载属性
    BaseMvcCtrl.prototype.loadProperty = function () {
        if (this._isLoadProperty) {
            return;
        }
        // 遍历节点
        var children = this.node.children;
        for (var i = 0, l = children.length; i < l; i++) {
            this.__detectionNodeNames(children[i]);
        }
        this._isLoadProperty = true;
    };
    BaseMvcCtrl.prototype.__isEvent = function (val) {
        return !!EventTypeFix[val.split('@')[0]];
    };
    BaseMvcCtrl.prototype.__isVar = function (val) {
        return CmptTypeFix[val] || val === 'w' || val === 'wg';
    };
    BaseMvcCtrl.prototype.__getHeadVar = function (arr) {
        var head = arr[0];
        arr.shift();
        while (arr.length > 0) {
            var val = arr[0];
            if (this.__isVar(val) || this.__isEvent(val)) {
                break;
            }
            head += ut.initialUpperCase(val);
            arr.shift();
        }
        return head;
    };
    BaseMvcCtrl.prototype.__detectionNodeNames = function (node) {
        var _this = this;
        var arr = node.name.split('_');
        var hasWdt = false;
        if (arr.length >= 2 && !!arr[0]) {
            var head_1 = this.__getHeadVar(arr);
            var hasW = !!arr.remove('w');
            var _loop_1 = function (i, l) {
                var it = arr[i];
                var ct = CmptTypeFix[it];
                if (ct) { // 组件
                    var vname = head_1 + ct.fix;
                    if (this_1[vname] !== undefined) {
                        if (ct.fix === 'Node_') {
                            this_1[vname] = node;
                        }
                        else {
                            var cmpt = node.getComponent(ct.type);
                            if (cmpt) {
                                this_1[vname] = cmpt;
                            }
                            else {
                                logger.error(vname + ' 没有对应的组件 at=' + this_1['__classname__']);
                            }
                        }
                    }
                    else {
                        logger.error(vname + ' 没有找到对应的属性名 at=' + this_1['__classname__']);
                    }
                }
                else if (it === 'wg') { // 挂件
                    var wdt = this_1.__addWdtComponent(node, ut.initialUpperCase(head_1) + 'WdtCtrl');
                    if (hasW) {
                        var vname = head_1 + 'Wdt_';
                        if (this_1[vname] !== undefined) {
                            this_1[vname] = wdt;
                        }
                    }
                    hasWdt = true;
                }
                else { // 事件
                    var _a = it.split('@'), e = _a[0], data_1 = _a[1];
                    var et_1 = EventTypeFix[e];
                    if (!et_1) {
                    }
                    else if (e === 'nbe') {
                        node.children.forEach(function (m) { return _this.__addClickEvent(m, head_1, et_1, data_1, false); });
                    }
                    else if (e === 'ebee') {
                        this_1.__addEditBoxEvent(node, head_1, e, data_1);
                    }
                    else {
                        this_1.__addClickEvent(node, head_1, et_1, data_1);
                    }
                }
            };
            var this_1 = this;
            for (var i = 0, l = arr.length; i < l; i++) {
                _loop_1(i, l);
            }
        }
        if (!hasWdt) {
            var children = node.children;
            for (var i = 0, l = children.length; i < l; i++) {
                this.__detectionNodeNames(children[i]);
            }
        }
    };
    // 添加EditBox点击事件
    BaseMvcCtrl.prototype.__addEditBoxEvent = function (node, head, eType, data) {
        var fname = 'onClick' + ut.initialUpperCase(head) + 'Ended';
        var func = this[fname];
        if (func && typeof (func) === 'function') {
            var cmpt = node.getComponent(cc.EditBox);
            if (cmpt) {
                if (eType === 'ebee') {
                    cmpt.editingDidEnded[0] = this.__newEventHandler(fname, data);
                }
                else {
                    logger.error(fname + ' 没有对应的events at=' + this['__classname__'] + '.' + node.name);
                }
            }
            else {
                logger.error(fname + ' 没有对应的组件 at=' + this['__classname__'] + '.' + node.name);
            }
        }
        else {
            logger.error(fname + ' 没有找到对应的方法名 at=' + this['__classname__'] + '.' + node.name);
        }
    };
    BaseMvcCtrl.prototype.__addClickEvent = function (node, head, cmptType, data, log) {
        if (log === void 0) { log = true; }
        var fname = 'onClick' + ut.initialUpperCase(head);
        var func = this[fname];
        if (func && typeof (func) === 'function') {
            var cmpt = node.getComponent(cmptType);
            if (cmpt) {
                var events = this.__getEvents(cmpt);
                if (events) {
                    events[0] = this.__newEventHandler(fname, data);
                }
                else {
                    logger.error(fname + ' 没有对应的events at=' + this['__classname__'] + '.' + node.name);
                }
            }
            else {
                log && logger.error(fname + ' 没有对应的组件 at=' + this['__classname__'] + '.' + node.name);
            }
        }
        else {
            logger.error(fname + ' 没有找到对应的方法名 at=' + this['__classname__'] + '.' + node.name);
        }
    };
    BaseMvcCtrl.prototype.__getEvents = function (cmpt) {
        if (cmpt instanceof cc.Toggle || cmpt instanceof cc.ToggleContainer) {
            return cmpt.checkEvents;
        }
        else if (cmpt instanceof cc.Button) {
            return cmpt.clickEvents;
        }
        else if (cmpt instanceof cc.Slider) {
            return cmpt.slideEvents;
        }
        return null;
    };
    BaseMvcCtrl.prototype.__newEventHandler = function (handler, data) {
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = this['__classname__'];
        eventHandler.handler = handler;
        eventHandler.customEventData = data || '';
        return eventHandler;
    };
    BaseMvcCtrl.prototype.__addWdtComponent = function (node, className) {
        if (!cc.js.getClassByName(className)) {
            logger.error('addWdtComponent error! not found class ' + className);
            return null;
        }
        var wdt = node.getComponent(className);
        if (!wdt) {
            wdt = node.addComponent(className);
        }
        return wdt;
    };
    BaseMvcCtrl.prototype.__wrapListenMaps = function (listens, out, target) {
        listens.forEach(function (map) {
            var data = { type: '', cb: null, target: target, tag: 'create' };
            for (var key in map) {
                var val = map[key];
                if (typeof (val) === 'function') {
                    data.type = key;
                    data.cb = val;
                }
                else if (key === 'enter') {
                    data.tag = key;
                }
            }
            out.push(data);
        });
        return out;
    };
    return BaseMvcCtrl;
}(cc.Component));
exports.default = BaseMvcCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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