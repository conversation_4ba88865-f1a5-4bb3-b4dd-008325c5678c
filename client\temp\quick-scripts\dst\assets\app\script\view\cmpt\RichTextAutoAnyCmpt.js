
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/RichTextAutoAnyCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '27f5fX3bK1Mz5xYzpkJE7I+', 'RichTextAutoAnyCmpt');
// app/script/view/cmpt/RichTextAutoAnyCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 自动换行
 */
var RichTextAutoAnyCmpt = /** @class */ (function (_super) {
    __extends(RichTextAutoAnyCmpt, _super);
    function RichTextAutoAnyCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.maxWidth = 0;
        _this.label = null;
        _this.preContent = '';
        return _this;
    }
    RichTextAutoAnyCmpt.prototype.onLoad = function () {
        this.label = this.getComponent(cc.RichText);
    };
    RichTextAutoAnyCmpt.prototype.check = function () {
        if (!this.label) {
            this.onLoad();
        }
        if (this.preContent === this.label.string) {
            return this.node.width * this.node.scaleX;
        }
        this.preContent = this.label.string;
        if (this.label.maxWidth === 0) {
            if (this.node.width > this.maxWidth) {
                this.label.maxWidth = this.maxWidth;
                this.node.width = this.maxWidth;
            }
        }
        else if (this.node.height < this.label.lineHeight * 2) {
            this.label.maxWidth = 0;
        }
        return this.node.width * this.node.scaleX;
    };
    __decorate([
        property
    ], RichTextAutoAnyCmpt.prototype, "maxWidth", void 0);
    RichTextAutoAnyCmpt = __decorate([
        ccclass
    ], RichTextAutoAnyCmpt);
    return RichTextAutoAnyCmpt;
}(cc.Component));
exports.default = RichTextAutoAnyCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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