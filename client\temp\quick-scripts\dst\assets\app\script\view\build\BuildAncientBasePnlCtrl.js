
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildAncientBasePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5d6174iiNxOMZnJwuonbVWz', 'BuildAncientBasePnlCtrl');
// app/script/view/build/BuildAncientBasePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var BuildAncientBasePnlCtrl = /** @class */ (function (_super) {
    __extends(BuildAncientBasePnlCtrl, _super);
    function BuildAncientBasePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconNode_ = null; // path://root/icon_n
        _this.infoNode_ = null; // path://root/info_n
        _this.descLbl_ = null; // path://root/info_n/desc_l
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BuildAncientBasePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuildAncientBasePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildAncientBasePnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(data.aIndex);
        ResHelper_1.resHelper.loadBuildIcon(data.icon, this.iconNode_.Child('val', cc.Sprite), this.key);
        this.iconNode_.Child('name').setLocaleKey(data.name);
        this.iconNode_.Child('lv').setLocaleKey('ui.lv', data.lv);
        this.descLbl_.setLocaleKey(data.desc);
        var attr = this.infoNode_.Child('attr');
        var _b = cell.getPawnInfo() || { id: 8001, lv: 1 }, id = _b.id, lv = _b.lv;
        var attrJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + lv);
        var hp = (cell === null || cell === void 0 ? void 0 : cell.getHpInfo()) || [0, 0], jsonHp = (attrJson === null || attrJson === void 0 ? void 0 : attrJson.hp) || 0, maxHp = hp[1];
        attr.Child('hp/val').setLocaleKey('ui.build_hp', hp.join('/'));
        attr.Child('hp/add').setLocaleKey(jsonHp < maxHp ? assetsMgr.lang('ui.bracket', jsonHp + "+<color=#B6A591>" + (maxHp - jsonHp) + "</c>") : '');
        var effect = data.effect;
        // 首次效果
        var first = attr.Child('first'), firstSurplusTime = (_a = GameHelper_1.gameHpr.world.getAncientInfo(data.aIndex)) === null || _a === void 0 ? void 0 : _a.getFirstSurplusTime();
        if (first.active = !!firstSurplusTime) {
            first.Child('val', cc.LabelTimer).run(firstSurplusTime * 0.001);
            effect = data.getMaxEffect();
        }
        // 效果
        var curr = attr.Child('curr');
        if (curr.active = !!effect) {
            curr.Child('val').Color(firstSurplusTime ? '#4AB32E' : '#936E5A').setLocaleKey('ui.ancient_curr_eff_desc_' + effect.type, effect.getValueText() || '');
        }
    };
    BuildAncientBasePnlCtrl.prototype.onRemove = function () {
    };
    BuildAncientBasePnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    BuildAncientBasePnlCtrl = __decorate([
        ccclass
    ], BuildAncientBasePnlCtrl);
    return BuildAncientBasePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildAncientBasePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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