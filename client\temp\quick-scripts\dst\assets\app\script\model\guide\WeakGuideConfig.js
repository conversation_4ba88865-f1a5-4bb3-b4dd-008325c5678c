
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/WeakGuideConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ee3a6h95HxFwZfZRFmjKFm+', 'WeakGuideConfig');
// app/script/model/guide/WeakGuideConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WEAK_GUIDE_CONFIG = void 0;
var EventType_1 = require("../../common/event/EventType");
var GuideConfig_1 = require("./GuideConfig");
// 配置
var WEAK_GUIDE_CONFIG = {
    datas: [
        {
            id: 1,
            steps: [
                {
                    type: GuideConfig_1.GuideStepType.DELAY,
                    time: 0.1,
                    tag: '0',
                },
                {
                    type: GuideConfig_1.GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_210',
                        args: { key: 'build/BuildBarracks' },
                    },
                    nodeChoose: {
                        scene: 'area',
                        // func: 'nodeChooseBarracks',
                        name: 'Wind/AreaWind/root/map_n/build_n/BUILD_2004/body',
                        finger: {
                            offset: cc.v2(0, 24),
                            angle: 180,
                        },
                        moveCamera: true,
                    },
                },
                {
                    type: GuideConfig_1.GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncIsRecruitTab',
                    },
                },
                {
                    type: GuideConfig_1.GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.BUTTON_LOGIN_DONE,
                    },
                    nodeChoose: {
                        scene: 'ui',
                        name: 'View/BuildBarracksPnl/root/tabs_tc_tce/1',
                        finger: {
                            offset: cc.v2(0, 20),
                            angle: 180,
                        },
                    },
                    tag: '1',
                },
            ],
        }
    ]
};
exports.WEAK_GUIDE_CONFIG = WEAK_GUIDE_CONFIG;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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