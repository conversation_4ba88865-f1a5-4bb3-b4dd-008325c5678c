{"version": 3, "sources": ["assets\\app\\script\\view\\menu\\CollectionPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0DAAqD;AACrD,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAGpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA+C,qCAAc;IAA7D;QAAA,qEAqtBC;QAntBA,0BAA0B;QAClB,aAAO,GAAuB,IAAI,CAAA,CAAC,0BAA0B;QAC7D,gBAAU,GAAY,IAAI,CAAA,CAAC,sBAAsB;QACjD,iBAAW,GAAuB,IAAI,CAAA,CAAC,yCAAyC;QAChF,mBAAa,GAAuB,IAAI,CAAA,CAAC,2CAA2C;QAC5F,MAAM;QAEW,cAAQ,GAAW,gBAAgB,CAAA;QACnC,yBAAmB,GAAW,2BAA2B,CAAA;QACzD,2BAAqB,GAAW,6BAA6B,CAAA;QAC7D,oBAAc,GAAW,sBAAsB,CAAA;QAC/C,yBAAmB,GAAW,2BAA2B,CAAA;QACzD,yBAAmB,GAAW,2BAA2B,CAAA;QACzD,yBAAmB,GAAW,2BAA2B,CAAA;QACzD,0BAAoB,GAAW,4BAA4B,CAAA;QAC3D,4BAAsB,GAAW,8BAA8B,CAAA;QAC/D,sBAAgB,GAAW,wBAAwB,CAAA;QACnD,2BAAqB,GAAW,6BAA6B,CAAA;QAEtE,UAAI,GAAc,IAAI,CAAA;QACtB,YAAM,GAAW,CAAC,CAAA;QAClB,iBAAW,GAAW,CAAC,CAAA;QACvB,cAAQ,GAAU,EAAE,CAAA;QACpB,gBAAU,GAAU,EAAE,CAAA;QACtB,mBAAa,GAAU,EAAE,CAAA;QACzB,oBAAc,GAAY,IAAI,CAAA;QAC9B,sBAAgB,GAAY,IAAI,CAAA;QAChC,oBAAc,GAAW,CAAC,CAAA;;IAwrBnC,CAAC;IAtrBO,2CAAe,GAAtB;;QACC,OAAO;sBACJ,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,qBAAqB,EAAE,QAAK,GAAE,IAAI;SAC5E,CAAA;IACF,CAAC;IAEY,oCAAQ,GAArB;;;gBACC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;;;KACjC;IAEM,mCAAO,GAAd,UAAe,IAAS;QACvB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IACtE,CAAC;IAEM,oCAAQ,GAAf;IACA,CAAC;IAEM,mCAAO,GAAd;QACC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACxC,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,0BAA0B;IAC1B,uCAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;;QACzC,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACtD,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;YACnD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC9G,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YACxG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;YACzD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACzE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC1B;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;YAC3E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC5B;IACF,CAAC;IAED,yCAAyC;IACzC,2CAAe,GAAf,UAAgB,KAAgB,EAAE,IAAY;;QAC7C,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACrC,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YACnH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC7G,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;SAC1B;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC7G,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;SAC1B;IACF,CAAC;IAED,2CAA2C;IAC3C,6CAAiB,GAAjB,UAAkB,KAAgB,EAAE,IAAY;;QAC/C,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;QACjE,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACrC,IAAI,IAAI,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC7G,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;SAC1B;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAC9G,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;SAC3B;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,QAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,CAAC,mCAAI,CAAC,EAAE,IAAI,CAAC,CAAA;YAChH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;SAC7B;IACF,CAAC;IAED,2CAA2C;IAC3C,6CAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAA;QAChC,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,0DAA0D;IAC1D,6CAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;QACzD,uBAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;IAChE,CAAC;IAED,oEAAoE;IACpE,4CAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACxD,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAA;QAChC,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAChD,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,IAAI,KAAK,IAAI,CAAC,aAAa,EAAE,EAAE;YAClC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC/B;IACF,CAAC;IAED,sCAAsC;IACtC,yCAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAClC,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,iEAAiE;IACjE,8CAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QAC1D,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAClC,uBAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAChD,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACtC,IAAI,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE,EAAE;YACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SACjC;IACF,CAAC;IAED,uDAAuD;IACvD,4CAAgB,GAAhB,UAAiB,KAA0B,EAAE,KAAa;QACzD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAf,CAAe,CAAC,CAAA;QACvD,IAAM,IAAI,GAAG,EAAE,CAAA;QACf,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAA,CAAC;YAC3B,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YAC7B,IAAI,SAAS,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACpB;QACF,CAAC,CAAC,CAAA;QACF,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,CAAA;QACvE,IAAI,IAAI,EAAE;YACT,uBAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;SACtD;aAAM;YACN,uBAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAA;SACvC;IACF,CAAC;IAED,yCAAyC;IACzC,wCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACpD,uBAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACtC,CAAC;IAED,yCAAyC;IACzC,wCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACpD,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;IACrC,CAAC;IAED,kDAAkD;IAClD,uCAAW,GAAX,UAAY,KAA0B,EAAE,KAAa;QAArD,iBAeC;QAdA,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAChD,uBAAU,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,UAAC,GAAG;YAC5H,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;gBAC1B,OAAM;aACN;iBAAM,IAAI,oBAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBACtC,OAAM;aACN;YACD,IAAI,IAAI,KAAK,WAAW,EAAE;gBACzB,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aAClC;iBAAM,IAAI,IAAI,KAAK,WAAW,EAAE;gBAChC,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aAClC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,oDAAoD;IACpD,yCAAa,GAAb,UAAc,KAA0B,EAAE,KAAa;QAAvD,iBAeC;QAdA,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QAChD,uBAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,UAAC,GAAG;YAC5G,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE;gBAC1B,OAAM;aACN;iBAAM,IAAI,oBAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;gBACtC,OAAM;aACN;YACD,IAAI,IAAI,KAAK,UAAU,EAAE;gBACxB,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aAClC;iBAAM,IAAI,IAAI,KAAK,YAAY,EAAE;gBACjC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;aACnC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,MAAM;IACN,iHAAiH;IAEzG,yCAAa,GAArB;QACC,IAAI,CAAC,eAAe,EAAE,CAAA;IACvB,CAAC;IAEO,iDAAqB,GAA7B;QACC,IAAI,CAAC,cAAc,EAAE,CAAA;IACtB,CAAC;IAED,iHAAiH;IAEjH,OAAO;IACC,yCAAa,GAArB;QACC,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;SAChE;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAC3B,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;aACrE;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;aACrE;SACD;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAC3B,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;aACrE;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;aACtE;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;aACxE;SACD;QACD,OAAO,UAAU,CAAA;IAClB,CAAC;IAED,OAAO;IACC,yCAAa,GAArB,UAAsB,GAAW;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;SACzD;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;aAC9D;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;aAC9D;SACD;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;aAC9D;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAA;aAC/D;iBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;gBAClC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAA;aACjE;SACD;IACF,CAAC;IAED,OAAO;IACC,2CAAe,GAAvB;QACC,IAAI,YAAY,GAAG,CAAC,CAAA;QACpB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;SACpE;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;SAEzE;QACD,OAAO,YAAY,CAAA;IACpB,CAAC;IAED,OAAO;IACC,2CAAe,GAAvB,UAAwB,GAAW;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAA;SAC3D;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAA;SAChE;IACF,CAAC;IAGD,oGAAoG;IACpG,OAAO;IACC,0CAAc,GAAtB,UAAuB,IAAa,EAAE,IAAY,EAAE,IAAc;QACjE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAA;SAC1E;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAA;SAC/G;aAAM;YACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAA;SACrG;QACD,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;YACxD,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAA;YACtC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YACpD,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,EAAE;YACV,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAA;aACrB;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;oBAC3B,IAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;qBAAM;oBACN,IAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;aACD;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;oBAC3B,IAAI,CAAC,eAAe,EAAE,CAAA;iBACtB;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;oBAClC,IAAI,CAAC,gBAAgB,EAAE,CAAA;iBACvB;qBAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;oBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAA;iBACzB;aACD;SACD;IACF,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,IAAa,EAAE,IAAY,EAAE,IAAc;QACnE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC1B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,UAAU;YACjC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAA;YACpG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;SACzB;aAAM;YACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAA;SAC1G;QACD,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC;YAC1D,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAA;YACtC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;YACpD,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,IAAI,EAAE;YACV,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAA;aACrB;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,IAAI,CAAC,eAAe,EAAE,CAAA;aACtB;SACD;IACF,CAAC;IAEO,0CAAc,GAAtB,UAAuB,IAAa;;QACnC,IAAI,SAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,mCAAI,CAAC,CAAA;QAC9E,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAClC,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACzC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;gBAC5I,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAI,GAAG,SAAI,GAAG,MAAG,CAAA;aACxD;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;gBAC/G,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,OAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,UAAI,GAAG,MAAG,CAAA;aACvF;SACD;IACF,CAAC;IAEO,0CAAc,GAAtB,UAAuB,IAAc;QAArC,iBA6DC;QA5DA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,IAAM,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QACrC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC,EAA1D,CAA0D,CAAC,CAAA;QACrI,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAb,CAAa,CAAC,CAAA;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAChE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;YAC5B,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YACxC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACxC,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,OAAO;gBACxB,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,KAAI,CAAC,CAAC,CAAA;gBACxB,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,KAAI,CAAC,CAAC,CAAA;aACxB;YACD,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACzC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACzC,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,OAAO;gBACxB,EAAE,GAAG,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAA;gBACpC,EAAE,GAAG,EAAE,GAAG,OAAO,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAA;aACpC;iBAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,OAAO;gBAC/B,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,KAAI,CAAC,CAAC,CAAA;gBACpC,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,KAAI,CAAC,CAAC,CAAA;aACpC;YACD,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5C,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,IAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC5C,OAAO,EAAE,GAAG,EAAE,CAAA;QACf,CAAC,CAAC,CAAA;QACF,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAChB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,CAAC;YACxC,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACjE,IAAM,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YACzE,IAAM,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC3C,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;YAChD,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAA;YAClD,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAA;YACtB,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACzB,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY;gBACxB,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBAC3B,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBAC3B,qBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aAC3D;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM;gBACnC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBACzB,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBACzB,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACzB,qBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aACzD;iBAAM,EAAE,MAAM;gBACd,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAA;gBACtB,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBACzB,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACzB,qBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;aACzD;YACD,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;YACnE,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,IAAG,CAAC,EAAE;gBACvD,uBAAU,CAAC,0BAA0B,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;aACtD;YACD,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,GAAE,CAAC,CAAA;YAC7D,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,GAAE,CAAA;QAClD,CAAC,CAAC,CAAA;IACH,CAAC;IAED,kGAAkG;IAClG,SAAS;IACK,2CAAe,GAA7B,UAA8B,IAAc;;;;;;;wBAC3C,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;wBACvC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;wBACnC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;wBAChC,qBAAM,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAA;;wBAAvC,GAAG,GAAG,SAAiC;wBAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;yBAClB;6BAAM,IAAI,GAAG,EAAE;4BACf,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;4BACV,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;wBAEK,UAAU,GAAG,oBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,aAAa,GAAG,EAAE,CAAA;wBAC3F,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAvB,CAAuB,CAAC,CAAA;wBACxE,WAAW,GAAG,EAAE,CAAA;wBACtB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAtC,CAAsC,CAAC,CAAA;wBACpF,WAAW,GAAgD,EAAE,CAAA;wBACnE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;wBACvB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;4BACpC,IAAI,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;4BAC5B,IAAI,CAAC,IAAI,EAAE;gCACV,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA;gCAC7B,KAAI,CAAC,cAAc,EAAE,CAAA;6BACrB;4BACD,IAAI,GAAG,GAAG,CAAC,CAAA,CAAC,IAAI;4BAChB,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;gCAChB,GAAG,GAAG,CAAC,CAAA,CAAC,IAAI;6BACZ;iCAAM,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;gCACvB,GAAG,GAAG,CAAC,CAAA,CAAC,IAAI;6BACZ;4BACD,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;4BACpB,IAAI,CAAC,IAAI,EAAE;gCACV,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAA;6BACrB;4BACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;4BACZ,IAAI,GAAG,KAAK,CAAC,EAAE;gCACd,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAjB,CAAiB,CAAC,CAAA;6BACtC;wBACF,CAAC,CAAC,CAAA;wBAEF,IAAI,CAAC,cAAc,EAAE,CAAA,CAAC,QAAQ;wBACxB,IAAI,GAAG,EAAE,CAAA;wBACT,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;wBAC/B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;wBACrE,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;4BAChD,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,EAAE;gCAC3D,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;gCAC9B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;oCAC5B,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;wCACnB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAA;wCACpD,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,OAAO;4CACxB,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAA;yCAC/B;6CAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;4CAChC,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;4CAC7C,MAAM,IAAI,IAAI,CAAC,EAAE,CAAA;yCACjB;wCACD,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;qCACxE;iCACD;qCAAM;oCACN,IAAI,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAA;oCAC5C,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,OAAO;wCACxB,MAAM,GAAG,QAAQ,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;qCAC5D;yCAAM,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;wCAChC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;wCACjD,MAAM,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;qCACnD;oCACD,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;iCAChE;6BACD;wBACF,CAAC,CAAC,CAAA;wBACF,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC,CAAA;wBAExC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;wBACpB,EAAE,CAAC,cAAc,EAAE,CAAA;wBACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;wBAChB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAC,EAAE,EAAE,CAAC;4BAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;4BACpD,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;4BAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;4BACxG,qBAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;4BACvE,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAA;4BACrG,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAA;4BAC5E,IAAM,UAAU,GAAG,CAAC,CAAC,QAAQ,CAAA;4BAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;4BAC/E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;4BAC3D,IAAM,KAAK,GAAG,UAAU,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;4BACpE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;4BACzC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;wBAC3F,CAAC,CAAC,CAAA;;;;;KACF;IAED,SAAS;IACK,2CAAe,GAA7B,UAA8B,IAAc;;;;;gBAC3C,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAEjC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,CAAC,EAAZ,CAAY,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;gBACnI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC1D,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;gBAC9D,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;gBAEhD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;gBACrE,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;oBACzB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;wBACf,OAAO,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAV,CAAU,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAV,CAAU,CAAC,CAAA;oBAC3E,CAAC,CAAC,CAAA;iBACF;gBAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;gBACf,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;gBAC5C,EAAE,CAAC,cAAc,EAAE,CAAA;gBACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;gBAChB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;oBAClB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;oBACrB,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC/B,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;oBACrD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;oBAC3D,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBAClE,CAAC,CAAC,CAAA;;;;KACF;IAEa,uCAAW,GAAzB,UAA0B,EAAU,EAAE,IAAY;;;;;;wBAC7C,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,KAAK,CAAC,CAAA;6BACjC,UAAU,EAAV,wBAAU;wBACP,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAA;;wBAA5C,GAAG,GAAG,SAAsC,CAAA;;4BAEtC,qBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA;;wBAArC,GAAG,GAAG,SAA+B,CAAA;;;wBAEtC,IAAI,GAAG,EAAE;4BACR,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACxB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;4BACvC,IAAI,UAAU,EAAE;gCACf,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;6BAC9C;iCAAM;gCACN,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;6BACzC;yBACD;;;;;KACD;IAEa,uCAAW,GAAzB,UAA0B,EAAU,EAAE,IAAY;;;;;;wBAC7C,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,KAAK,CAAC,CAAA;6BACjC,UAAU,EAAV,wBAAU;wBACP,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAA;;wBAA5C,GAAG,GAAG,SAAsC,CAAA;;4BAEtC,qBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA;;wBAArC,GAAG,GAAG,SAA+B,CAAA;;;wBAEtC,IAAI,GAAG,EAAE;4BACR,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACxB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;4BACvC,IAAI,UAAU,EAAE;gCACf,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;6BAC9C;iCAAM;gCACN,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;6BACzC;yBACD;;;;;KACD;IAED,kGAAkG;IAElG,OAAO;IACC,2CAAe,GAAvB,UAAwB,IAAc;QAAtC,iBA4BC;QA3BA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,yBAAyB;QACzB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,MAAM,EAArB,CAAqB,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QAChJ,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACzC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;QAC3D,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;QAEnD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QACrE,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;YACzB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,IAAI,EAAZ,CAAY,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,IAAI,EAAZ,CAAY,CAAC,CAAA;YAC/E,CAAC,CAAC,CAAA;SACF;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAChB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;YAClB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YACpD,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YACxD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC3D,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YACjC,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;YAChD,qBAAS,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;IACH,CAAC;IAED,YAAY;IACJ,4CAAgB,GAAxB,UAAyB,IAAc;QAAvC,iBA4BC;QA3BA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,iBAAiB;QACjB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAA5B,CAA4B,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QAC5J,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACzC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAA;QAC5D,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;QAEnD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;QACtE,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;YACzB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACf,OAAO,MAAM,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAV,CAAU,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,CAAC,CAAC,EAAE,EAAV,CAAU,CAAC,CAAA;YAC7E,CAAC,CAAC,CAAA;SACF;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAChB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;YAClB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YACtD,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;YACxD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC1D,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAA;YACxB,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACH,CAAC;IAED,SAAS;IACD,8CAAkB,GAA1B,UAA2B,IAAc;QAAzC,iBA6BC;QA5BA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAA;QACnH,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACzC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,CAAA;QAC7D,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAA;QAEnD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QACxE,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,QAAQ;YACzB,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC;gBACf,OAAO,OAAO,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAb,CAAa,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAb,CAAa,CAAC,CAAA;YACrF,CAAC,CAAC,CAAA;SACF;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,CAAA;QAC5C,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAChB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,UAAC,EAAE,EAAE,CAAC;YAClB,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YACrB,EAAE,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YACvD,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAA;YACvD,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAChC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC1C,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;YAChE,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAA;YACxB,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;QAChD,CAAC,CAAC,CAAA;IACH,CAAC;IAEa,uCAAW,GAAzB,UAA0B,EAAU,EAAE,IAAY;;;;;;wBAC7C,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,KAAK,CAAC,CAAA;6BACjC,UAAU,EAAV,wBAAU;wBACP,qBAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAA;;wBAA5C,GAAG,GAAG,SAAsC,CAAA;;4BAEtC,qBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAA;;wBAArC,GAAG,GAAG,SAA+B,CAAA;;;wBAEtC,IAAI,GAAG,EAAE;4BACR,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;4BACzC,IAAI,UAAU,EAAE;gCACf,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;6BAC9C;iCAAM;gCACN,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;6BACzC;yBACD;;;;;KACD;IAEa,wCAAY,GAA1B,UAA2B,EAAU,EAAE,IAAY;;;;;;wBAC9C,GAAG,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,KAAK,CAAC,CAAA;6BACjC,UAAU,EAAV,wBAAU;wBACP,qBAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAA;;wBAA7C,GAAG,GAAG,SAAuC,CAAA;;4BAEvC,qBAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAA;;wBAAtC,GAAG,GAAG,SAAgC,CAAA;;;wBAEvC,IAAI,GAAG,EAAE;4BACR,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,OAAO,EAAE;4BACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;4BACzC,IAAI,UAAU,EAAE;gCACf,uBAAU,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;6BAC9C;iCAAM;gCACN,uBAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;6BACzC;yBACD;;;;;KACD;IAptBmB,iBAAiB;QADrC,OAAO;OACa,iBAAiB,CAqtBrC;IAAD,wBAAC;CArtBD,AAqtBC,CArtB8C,EAAE,CAAC,WAAW,GAqtB5D;kBArtBoB,iBAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["import EventType from \"../../common/event/EventType\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport UserModel from \"../../model/common/UserModel\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class CollectionPnlCtrl extends mc.BasePnlCtrl {\n\n\t//@autocode property begin\n\tprivate tabsTc_: cc.ToggleContainer = null // path://root/tabs_tc_tce\n\tprivate pagesNode_: cc.Node = null // path://root/pages_n\n\tprivate skinTabsTc_: cc.ToggleContainer = null // path://root/pages_n/1/skin_tabs_tc_tce\n\tprivate socialTabsTc_: cc.ToggleContainer = null // path://root/pages_n/2/social_tabs_tc_tce\n\t//@end\n\n\tprivate readonly PKEY_TAB: string = 'COLLECTION_TAB'\n\tprivate readonly PKEY_SKIN_CHILD_TAB: string = 'COLLECTION_SKIN_CHILD_TAB'\n\tprivate readonly PKEY_SOCOAL_CHILD_TAB: string = 'COLLECTION_SOCOAL_CHILD_TAB'\n\tprivate readonly PKEY_HERO_SORT: string = 'COLLECTION_HERO_SORT'\n\tprivate readonly PKEY_PAWN_SKIN_SORT: string = 'COLLECTION_PAWN_SKIN_SORT'\n\tprivate readonly PKEY_CITY_SKIN_SORT: string = 'COLLECTION_CITY_SKIN_SORT'\n\tprivate readonly PKEY_HEAD_ICON_SORT: string = 'COLLECTION_HEAD_ICON_SORT'\n\tprivate readonly PKEY_CHAT_EMOJI_SORT: string = 'COLLECTION_CHAT_EMOJI_SORT'\n\tprivate readonly PKEY_PLANT_BOTANY_SORT: string = 'COLLECTION_PLANT_BOTANY_SORT'\n\tprivate readonly PKEY_HERO_FILTER: string = 'COLLECTION_HERO_FILTER'\n\tprivate readonly PKEY_PAWN_SKIN_FILTER: string = 'COLLECTION_PAWN_SKIN_FILTER'\n\n\tprivate user: UserModel = null\n\tprivate curTab: number = 0\n\tprivate curChildTab: number = 0\n\tprivate skinList: any[] = []\n\tprivate socialList: any[] = []\n\tprivate portrayalList: any[] = []\n\tprivate sortSelectNode: cc.Node = null\n\tprivate filterSelectNode: cc.Node = null\n\tprivate itemSkinsCount: number = 0\n\n\tpublic listenEventMaps() {\n\t\treturn [\n\t\t\t{ [EventType.UPDATE_EXCHANGE_SKINS]: this.onUpdateSkins, enter: true },\n\t\t\t{ [EventType.UPDATE_PORTRAYAL_INFO]: this.onUpdatePortrayalInfo, enter: true },\n\t\t]\n\t}\n\n\tpublic async onCreate() {\n\t\tthis.user = this.getModel('user')\n\t}\n\n\tpublic onEnter(data: any) {\n\t\tthis.tabsTc_.Tabs(this.user.getTempPreferenceMap(this.PKEY_TAB) || 0)\n\t}\n\n\tpublic onRemove() {\n\t}\n\n\tpublic onClean() {\n\t\tassetsMgr.releaseTempResByTag(this.key)\n\t}\n\n\t// ----------------------------------------- button listener function -------------------------------------------\n\t//@autocode button listener\n\n\t// path://root/tabs_tc_tce\n\tonClickTabs(event: cc.Toggle, data: string) {\n\t\t!data && audioMgr.playSFX('click')\n\t\tconst type = this.curTab = Number(event.node.name)\n\t\tthis.user.setTempPreferenceData(this.PKEY_TAB, type)\n\t\tconst node = this.pagesNode_.Swih(type)[0]\n\t\tthis.sortSelectNode = node.Child('top/sort_select_be')\n\t\tif (type === 0) {\n\t\t\tthis.filterSelectNode = node.Child('top/filter_be')\n\t\t\tthis.selectFilterItem(this.filterSelectNode, this.user.getTempPreferenceMap(this.PKEY_HERO_FILTER) ?? 0, true)\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_HERO_SORT) ?? 1, true)\n\t\t\tthis.updateHeroList(node)\n\t\t} else if (type === 1) {\n\t\t\tthis.filterSelectNode = node.Child('top/other/filter_be')\n\t\t\tconst tab = this.user.getTempPreferenceMap(this.PKEY_SKIN_CHILD_TAB) || 0\n\t\t\tthis.skinTabsTc_.Tabs(tab)\n\t\t} else if (type === 2) {\n\t\t\tconst tab = this.user.getTempPreferenceMap(this.PKEY_SOCOAL_CHILD_TAB) || 0\n\t\t\tthis.socialTabsTc_.Tabs(tab)\n\t\t}\n\t}\n\n\t// path://root/pages_n/1/skin_tabs_tc_tce\n\tonClickSkinTabs(event: cc.Toggle, data: string) {\n\t\t!data && audioMgr.playSFX('click')\n\t\tconst type = this.curChildTab = Number(event.node.name)\n\t\tthis.user.setTempPreferenceData(this.PKEY_SKIN_CHILD_TAB, type)\n\t\tconst node = this.pagesNode_.Child(1)\n\t\tif (type === 0) {\n\t\t\tthis.selectFilterItem(this.filterSelectNode, this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER) ?? 0, true)\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT) ?? 1, true)\n\t\t\tthis.updatePawnSkins(node)\n\t\t} else if (type === 1) {\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT) ?? 1, true)\n\t\t\tthis.updateCitySkins(node)\n\t\t}\n\t}\n\n\t// path://root/pages_n/2/social_tabs_tc_tce\n\tonClickSocialTabs(event: cc.Toggle, data: string) {\n\t\t!data && audioMgr.playSFX('click')\n\t\tconst type = this.curChildTab = Number(event.node.name)\n\t\tthis.user.setTempPreferenceData(this.PKEY_SOCOAL_CHILD_TAB, type)\n\t\tconst node = this.pagesNode_.Child(2)\n\t\tif (type === 0) {\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT) ?? 1, true)\n\t\t\tthis.updateHeadIcons(node)\n\t\t} else if (type === 1) {\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT) ?? 1, true)\n\t\t\tthis.updateChatEmojis(node)\n\t\t} else if (type === 2) {\n\t\t\tthis.selectSortItem(this.sortSelectNode, this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT) ?? 1, true)\n\t\t\tthis.updatePlantBotanys(node)\n\t\t}\n\t}\n\n\t// path://root/pages_n/0/top/sort_select_be\n\tonClickSortSelect(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = this.sortSelectNode\n\t\tviewHelper.changePopupBoxList(node, true, true)\n\t}\n\n\t// path://root/pages_n/0/top/sort_select_be/select_mask_be\n\tonClickSelectMask(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.changePopupBoxList(event.target.parent, false, true)\n\t}\n\n\t// path://root/pages_n/0/top/sort_select_be/mask/root/sort_items_nbe\n\tonClickSortItems(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = this.sortSelectNode\n\t\tviewHelper.changePopupBoxList(node, false, true)\n\t\tconst type = Number(event.target.name)\n\t\tif (type !== this.getSelectSort()) {\n\t\t\tthis.selectSortItem(node, type)\n\t\t}\n\t}\n\n\t// path://root/pages_n/0/top/filter_be\n\tonClickFilter(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = this.filterSelectNode\n\t\tviewHelper.changePopupBoxList(node, true, true)\n\t}\n\n\t// path://root/pages_n/0/top/filter_be/mask/root/filter_items_nbe\n\tonClickFilterItems(event: cc.Event.EventTouch, data: string) {\n\t\tconst node = this.filterSelectNode\n\t\tviewHelper.changePopupBoxList(node, false, true)\n\t\tconst type = Number(event.target.name)\n\t\tif (type !== this.getSelectFilter()) {\n\t\t\tthis.selectFilterItem(node, type)\n\t\t}\n\t}\n\n\t// path://root/pages_n/0/list/view/content/portrayal_be\n\tonClickPortrayal(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst idMap = {}\n\t\tthis.user.getPortrayals().forEach(m => idMap[m.id] = m)\n\t\tconst list = []\n\t\tthis.portrayalList.forEach(m => {\n\t\t\tconst portrayal = idMap[m.id]\n\t\t\tif (portrayal) {\n\t\t\t\tlist.push(portrayal)\n\t\t\t}\n\t\t})\n\t\tconst data = event.target.Data, info = list.find(m => m.id === data.id)\n\t\tif (info) {\n\t\t\tviewHelper.showPnl('common/PortrayalInfo', info, list)\n\t\t} else {\n\t\t\tviewHelper.showAlert('toast.dont_have')\n\t\t}\n\t}\n\n\t// path://root/pages_n/0/buttons/compo_be\n\tonClickCompo(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showPnl('menu/CompDebris')\n\t}\n\n\t// path://root/pages_n/0/buttons/point_be\n\tonClickPoint(event: cc.Event.EventTouch, data: string) {\n\t\tviewHelper.showPnl('menu/Pointsets')\n\t}\n\n\t// path://root/pages_n/1/list/view/content/skin_be\n\tonClickSkin(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst data = event.target.Data, type = data.type\n\t\tviewHelper.showPnl('menu/CollectionSkinInfo', { type: type, list: this.skinList, index: data.index, skins: data.skins }, (ret) => {\n\t\t\tif (!this.isValid || !ret) {\n\t\t\t\treturn\n\t\t\t} else if (gameHpr.costDeductTip(ret)) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (type === 'pawn_skin') {\n\t\t\t\tthis.buyPawnSkin(ret.id, ret.cond)\n\t\t\t} else if (type === 'city_skin') {\n\t\t\t\tthis.buyCitySkin(ret.id, ret.cond)\n\t\t\t}\n\t\t})\n\t}\n\n\t// path://root/pages_n/2/list/view/content/social_be\n\tonClickSocial(event: cc.Event.EventTouch, _data: string) {\n\t\taudioMgr.playSFX('click')\n\t\tconst data = event.target.Data, type = data.type\n\t\tviewHelper.showPnl('menu/CollectionEmojiInfo', { type: type, list: this.socialList, index: data.index }, (ret) => {\n\t\t\tif (!this.isValid || !ret) {\n\t\t\t\treturn\n\t\t\t} else if (gameHpr.costDeductTip(ret)) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (type === 'headicon') {\n\t\t\t\tthis.buyHeadIcon(ret.id, ret.cond)\n\t\t\t} else if (type === 'chat_emoji') {\n\t\t\t\tthis.buyChatEmoji(ret.id, ret.cond)\n\t\t\t}\n\t\t})\n\t}\n\n\t//@end\n\t// ----------------------------------------- event listener function --------------------------------------------\n\n\tprivate onUpdateSkins() {\n\t\tthis.updatePawnSkins()\n\t}\n\n\tprivate onUpdatePortrayalInfo() {\n\t\tthis.updateHeroList()\n\t}\n\n\t// ----------------------------------------- custom function ----------------------------------------------------\n\n\t// 选择排序\n\tprivate getSelectSort() {\n\t\tlet selectSort = 1\n\t\tif (this.curTab === 0) {\n\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_HERO_SORT)\n\t\t} else if (this.curTab === 1) {\n\t\t\tif (this.curChildTab === 0) {\n\t\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT)\n\t\t\t} else if (this.curChildTab === 1) {\n\t\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT)\n\t\t\t}\n\t\t} else if (this.curTab === 2) {\n\t\t\tif (this.curChildTab === 0) {\n\t\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT)\n\t\t\t} else if (this.curChildTab === 1) {\n\t\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT)\n\t\t\t} else if (this.curChildTab === 2) {\n\t\t\t\tselectSort = this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT)\n\t\t\t}\n\t\t}\n\t\treturn selectSort\n\t}\n\n\t// 设置排序\n\tprivate setSelectSort(val: number) {\n\t\tif (this.curTab === 0) {\n\t\t\tthis.user.setTempPreferenceData(this.PKEY_HERO_SORT, val)\n\t\t} else if (this.curTab === 1) {\n\t\t\tif (this.curChildTab === 0) {\n\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_PAWN_SKIN_SORT, val)\n\t\t\t} else if (this.curChildTab === 1) {\n\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_CITY_SKIN_SORT, val)\n\t\t\t}\n\t\t} else if (this.curTab === 2) {\n\t\t\tif (this.curChildTab === 0) {\n\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_HEAD_ICON_SORT, val)\n\t\t\t} else if (this.curChildTab === 1) {\n\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_CHAT_EMOJI_SORT, val)\n\t\t\t} else if (this.curChildTab === 2) {\n\t\t\t\tthis.user.setTempPreferenceData(this.PKEY_PLANT_BOTANY_SORT, val)\n\t\t\t}\n\t\t}\n\t}\n\n\t// 选择筛选\n\tprivate getSelectFilter() {\n\t\tlet selectFilter = 0\n\t\tif (this.curTab === 0) {\n\t\t\tselectFilter = this.user.getTempPreferenceMap(this.PKEY_HERO_FILTER)\n\t\t} else if (this.curTab === 1) {\n\t\t\tselectFilter = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER)\n\n\t\t}\n\t\treturn selectFilter\n\t}\n\n\t// 设置筛选\n\tprivate setSelectFilter(val: number) {\n\t\tif (this.curTab === 0) {\n\t\t\tthis.user.setTempPreferenceData(this.PKEY_HERO_FILTER, val)\n\t\t} else if (this.curTab === 1) {\n\t\t\tthis.user.setTempPreferenceData(this.PKEY_PAWN_SKIN_FILTER, val)\n\t\t}\n\t}\n\n\n\t// 英雄画像---------------------------------------------------------------------------------------------\n\t// 选择排序\n\tprivate selectSortItem(node: cc.Node, type: number, init?: boolean) {\n\t\tnode.Data = type\n\t\tthis.setSelectSort(type)\n\t\tif (this.curTab === 0) {\n\t\t\tnode.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type)\n\t\t} else if (this.curTab === 1) {\n\t\t\tnode.Child('val', cc.Label).setLocaleKey(type !== 2 ? 'ui.portrayal_list_sort_' + type : 'ui.skin_series_sort')\n\t\t} else {\n\t\t\tnode.Child('val', cc.Label).setLocaleKey(type ? 'ui.portrayal_list_sort_' + type : 'ui.default_sort')\n\t\t}\n\t\tnode.Child('mask/root/sort_items_nbe').children.forEach(m => {\n\t\t\tconst select = Number(m.name) === type\n\t\t\tm.Child('val').Color(select ? '#A18876' : '#C2B3A1')\n\t\t\tm.Child('select').active = select\n\t\t})\n\t\tif (!init) {\n\t\t\tif (this.curTab === 0) {\n\t\t\t\tthis.updateHeroList()\n\t\t\t} else if (this.curTab === 1) {\n\t\t\t\tif (this.curChildTab === 0) {\n\t\t\t\t\tthis.updatePawnSkins()\n\t\t\t\t} else {\n\t\t\t\t\tthis.updateCitySkins()\n\t\t\t\t}\n\t\t\t} else if (this.curTab === 2) {\n\t\t\t\tif (this.curChildTab === 0) {\n\t\t\t\t\tthis.updateHeadIcons()\n\t\t\t\t} else if (this.curChildTab === 1) {\n\t\t\t\t\tthis.updateChatEmojis()\n\t\t\t\t} else if (this.curChildTab === 2) {\n\t\t\t\t\tthis.updatePlantBotanys()\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate selectFilterItem(node: cc.Node, type: number, init?: boolean) {\n\t\tnode.Data = type\n\t\tthis.setSelectFilter(type)\n\t\tif (this.curTab <= 1) { // 画像和皮肤才用\n\t\t\tnode.Child('lay/val', cc.Label).setLocaleKey(type ? 'ui.pawn_type_' + type : 'ui.bazaar_filter_all')\n\t\t\tthis.udpateCountVal(type)\n\t\t} else {\n\t\t\tnode.Child('val', cc.Label).setLocaleKey(type ? 'ui.portrayal_list_sort_' + type : 'ui.bazaar_filter_all')\n\t\t}\n\t\tnode.Child('mask/root/filter_items_nbe').children.forEach(m => {\n\t\t\tconst select = Number(m.name) === type\n\t\t\tm.Child('val').Color(select ? '#A18876' : '#C2B3A1')\n\t\t\tm.Child('select').active = select\n\t\t})\n\t\tif (!init) {\n\t\t\tif (this.curTab === 0) {\n\t\t\t\tthis.updateHeroList()\n\t\t\t} else if (this.curTab === 1) {\n\t\t\t\tthis.updatePawnSkins()\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate udpateCountVal(type?: number) {\n\t\ttype = type ?? this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_FILTER) ?? 0\n\t\tconst node = this.filterSelectNode\n\t\tconst countNode = node.Child('lay/count')\n\t\tif (countNode.active = !type) {\n\t\t\tif (this.curTab === 0) {\n\t\t\t\tconst datas = assetsMgr.getJson('portrayalBase').datas, cur = this.user.getPortrayals().filter(m => m.isUnlock()).length, max = datas.length\n\t\t\t\tcountNode.Component(cc.Label).string = `(${cur}/${max})`\n\t\t\t} else if (this.curTab === 1) {\n\t\t\t\tconst datas = assetsMgr.getJson('pawnSkin').datas, skins = this.user.getUnlockPawnSkinIds(), len = datas.length\n\t\t\t\tcountNode.Component(cc.Label).string = `(${skins.length + this.itemSkinsCount}/${len})`\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate updateHeroList(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(0)\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tconst filter = this.getSelectFilter()\n\t\tthis.portrayalList = assetsMgr.getJson('portrayalBase').datas.filter(m => !filter || filter === Math.floor(m.avatar_pawn / 100 % 10))\n\t\tconst map = {}\n\t\tthis.user.getPortrayals().forEach(m => map[m.id] = m)\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_HERO_SORT)\n\t\tthis.portrayalList.sort((a, b) => {\n\t\t\tconst objA = map[a.id], objB = map[b.id]\n\t\t\tlet aw = objA ? 1 : 0, bw = objB ? 1 : 0\n\t\t\tif (sort === 2) { //按残卷数量\n\t\t\t\taw = (objA?.debris || 0)\n\t\t\t\tbw = (objB?.debris || 0)\n\t\t\t}\n\t\t\taw = aw * 10 + (objA?.isUnlock() ? 1 : 0)\n\t\t\tbw = bw * 10 + (objB?.isUnlock() ? 1 : 0)\n\t\t\tif (sort === 0) { //按兵种类型\n\t\t\t\taw = aw * 1000000 + (1000000 - a.id)\n\t\t\t\tbw = bw * 1000000 + (1000000 - b.id)\n\t\t\t} else if (sort === 1) { //按获取时间\n\t\t\t\taw = aw * 10000 + (objA?.index || 0)\n\t\t\t\tbw = bw * 10000 + (objB?.index || 0)\n\t\t\t}\n\t\t\taw = aw * 10 + (objA?.isChosenOne() ? 1 : 0)\n\t\t\tbw = bw * 10 + (objB?.isChosenOne() ? 1 : 0)\n\t\t\treturn bw - aw\n\t\t})\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(this.portrayalList.length, (it, i) => {\n\t\t\tconst json = it.Data = this.portrayalList[i], data = map[json.id]\n\t\t\tconst shadowNode = it.Child('mask/icon'), iconNode = it.Child('mask/val')\n\t\t\tconst pos = ut.stringToVec2(json.ui_offset)\n\t\t\ticonNode.Component(cc.Sprite).spriteFrame = null\n\t\t\tshadowNode.Component(cc.Sprite).spriteFrame = null\n\t\t\ticonNode.opacity = 255\n\t\t\tconst nameNode = it.Child('name')\n\t\t\tnameNode.Color('#C58461')\n\t\t\tif (!data) { // 没碎片 只显示剪影\n\t\t\t\tshadowNode.setPosition(pos)\n\t\t\t\tshadowNode.Color('#BCA092')\n\t\t\t\tresHelper.loadPortrayalImage(json.id, shadowNode, this.key)\n\t\t\t} else if (data.isUnlock()) { // 已解锁\n\t\t\t\tnameNode.Color('#DB543B')\n\t\t\t\ticonNode.Color('#FFFFFF')\n\t\t\t\ticonNode.setPosition(pos)\n\t\t\t\tresHelper.loadPortrayalImage(json.id, iconNode, this.key)\n\t\t\t} else { // 未解锁\n\t\t\t\ticonNode.opacity = 120\n\t\t\t\ticonNode.Color('#EEE2CB')\n\t\t\t\ticonNode.setPosition(pos)\n\t\t\t\tresHelper.loadPortrayalImage(json.id, iconNode, this.key)\n\t\t\t}\n\t\t\tnameNode.Child('val').setLocaleKey('portrayalText.name_' + json.id)\n\t\t\tif (it.Child('debris_count').active = data?.debris > 0) {\n\t\t\t\tviewHelper.updatePortrayalDebrisCount(it, data.debris)\n\t\t\t}\n\t\t\tit.Child('di', cc.MultiFrame).setFrame(!!data?.getUIDiType())\n\t\t\tit.Child('chosen').active = !!data?.isChosenOne()\n\t\t})\n\t}\n\n\t// 皮肤---------------------------------------------------------------------------------------------\n\t// 显示士兵皮肤\n\tprivate async updatePawnSkins(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\tnode.Child('top/other').Swih('filter_be')\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tconst err = await this.user.reqSkinItemList()\n\t\tif (!this.isValid) {\n\t\t} else if (err) {\n\t\t\tsv.List(0)\n\t\t\treturn viewHelper.showAlert(err)\n\t\t}\n\n\t\tconst serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test', canBuySkinMap = {}\n\t\tthis.user.getCanBuyPawnSkins(serverArea).forEach(m => canBuySkinMap[m.id] = m)\n\t\tconst pawnSkinMap = {}\n\t\tthis.user.getUnlockPawnSkinIds().forEach((m, i) => pawnSkinMap[m] = { data: m, index: i })\n\t\tconst itemSkinMap: { [key: number]: { [key: number]: any[] } } = {}\n\t\tthis.itemSkinsCount = 0\n\t\tthis.user.getSkinItemList().forEach(m => {\n\t\t\tlet item = itemSkinMap[m.id]\n\t\t\tif (!item) {\n\t\t\t\titemSkinMap[m.id] = item = {}\n\t\t\t\tthis.itemSkinsCount++\n\t\t\t}\n\t\t\tlet sta = 0 //正常\n\t\t\tif (m.state > 0) {\n\t\t\t\tsta = 1 //锁定\n\t\t\t} else if (m.state < 0) {\n\t\t\t\tsta = 2 //封禁\n\t\t\t}\n\t\t\tlet list = item[sta]\n\t\t\tif (!list) {\n\t\t\t\titem[sta] = list = []\n\t\t\t}\n\t\t\tlist.push(m)\n\t\t\tif (sta === 1) {\n\t\t\t\tlist.sort((a, b) => a.state - b.state)\n\t\t\t}\n\t\t})\n\n\t\tthis.udpateCountVal() // 刷新下数量\n\t\tconst list = []\n\t\tconst filter = this.getSelectFilter()\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_PAWN_SKIN_SORT)\n\t\tassetsMgr.getJson('pawnSkin').datas.forEach((m, i) => {\n\t\t\tif (!filter || filter === Math.floor(m.pawn_id / 100) % 10) {\n\t\t\t\tconst item = itemSkinMap[m.id]\n\t\t\t\tif (!ut.isEmptyObject(item)) {\n\t\t\t\t\tfor (let k in item) {\n\t\t\t\t\t\tlet skin = item[k][0], index = Number(k), weight = 0\n\t\t\t\t\t\tif (sort === 0) { //按兵种类型\n\t\t\t\t\t\t\tweight = 10000000 - m.id + 1000\n\t\t\t\t\t\t} else if (sort === 1) { // 按获取时间\n\t\t\t\t\t\t\tweight = index === 0 ? 10 : index > 0 ? 9 : 8\n\t\t\t\t\t\t\tweight += skin.id\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlist.push({ json: m, itemSkins: item[k], state: index, weight: weight })\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet pawnSkin = pawnSkinMap[m.id], weight = 0\n\t\t\t\t\tif (sort === 0) { //按兵种类型\n\t\t\t\t\t\tweight = 10000000 - m.id + 1 + (canBuySkinMap[m.id] ? 1 : 0)\n\t\t\t\t\t} else if (sort === 1) { // 按获取时间\n\t\t\t\t\t\tconst index = pawnSkin ? (pawnSkin.index + 1) : 0\n\t\t\t\t\t\tweight = index * 10 + (canBuySkinMap[m.id] ? 1 : 0)\n\t\t\t\t\t}\n\t\t\t\t\tlist.push({ json: m, pawnSkin: pawnSkin?.data, weight: weight })\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\tlist.sort((a, b) => b.weight - a.weight)\n\n\t\tthis.skinList = list\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(list.length, (it, i) => {\n\t\t\tconst data = list[i], json = data.json, id = json.id\n\t\t\tconst node = it.Swih('pawn')[0]\n\t\t\tnode.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang(resHelper.getPawnName(json.pawn_id)), 5))\n\t\t\tresHelper.loadPawnHeadIcon(id, node.Child('icon/val'), this.key, false)\n\t\t\tconst itemSkins = data.itemSkins, itemSkin = itemSkins ? itemSkins[0] : null, state = itemSkin?.state\n\t\t\tit.Data = { type: 'pawn_skin', json: json, index: i, skins: data.itemSkins }\n\t\t\tconst isItemSkin = !!itemSkin\n\t\t\tnode.Child('count', cc.Label).string = isItemSkin ? 'x' + itemSkins.length : ''\n\t\t\tnode.Child('clock').active = isItemSkin ? state > 0 : false\n\t\t\tconst isBan = isItemSkin && state < 0, icon = node.Child('icon/val')\n\t\t\ticon.Color(isBan ? '#A29D95' : '#FFFFFF')\n\t\t\ticon.opacity = (data.pawnSkin || isItemSkin) ? (isItemSkin && state < 0 ? 140 : 255) : 140\n\t\t})\n\t}\n\n\t// 显示主城皮肤\n\tprivate async updateCitySkins(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(1)\n\t\t// 排除未上的皮肤\n\t\tconst datas = assetsMgr.getJson('citySkin').datas.filter(m => m.cond !== 0), skins = this.user.getUnlockCitySkinIds(), len = datas.length\n\t\tconst countNode = node.Child('top/other').Swih('count')[0]\n\t\tcountNode.Child('bg/cur', cc.Label).string = skins.length + ''\n\t\tcountNode.Child('bg/max', cc.Label).string = '/' + len\n\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_CITY_SKIN_SORT)\n\t\tif (sort === 1) { // 按获取时间\n\t\t\tdatas.sort((a, b) => {\n\t\t\t\treturn skins.findIndex(m => m === b.id) - skins.findIndex(m => m === a.id)\n\t\t\t})\n\t\t}\n\n\t\tthis.skinList = datas\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(len, (it, i) => {\n\t\t\tconst data = datas[i]\n\t\t\tconst node = it.Swih('city')[0]\n\t\t\tit.Data = { type: 'city_skin', json: data, index: i }\n\t\t\tnode.Child('icon').opacity = skins.has(data.id) ? 255 : 140\n\t\t\tresHelper.loadCityIcon(data.id, node.Child('icon/val'), this.key)\n\t\t})\n\t}\n\n\tprivate async buyPawnSkin(id: number, cond: number) {\n\t\tlet err = '', isExchange = cond === 3\n\t\tif (isExchange) {\n\t\t\terr = await this.user.rsExchangePawnSkin(id)\n\t\t} else {\n\t\t\terr = await this.user.buyPawnSkin(id)\n\t\t}\n\t\tif (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (this.isValid) {\n\t\t\tthis.skinTabsTc_.Tabs(this.curChildTab)\n\t\t\tif (isExchange) {\n\t\t\t\tviewHelper.showAlert('toast.exchange_succeed')\n\t\t\t} else {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate async buyCitySkin(id: number, cond: number) {\n\t\tlet err = '', isExchange = cond === 3\n\t\tif (isExchange) {\n\t\t\terr = await this.user.rsExchangeCitySkin(id)\n\t\t} else {\n\t\t\terr = await this.user.buyCitySkin(id)\n\t\t}\n\t\tif (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (this.isValid) {\n\t\t\tthis.skinTabsTc_.Tabs(this.curChildTab)\n\t\t\tif (isExchange) {\n\t\t\t\tviewHelper.showAlert('toast.exchange_succeed')\n\t\t\t} else {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t}\n\t}\n\n\t// 社交---------------------------------------------------------------------------------------------\n\n\t// 显示头像\n\tprivate updateHeadIcons(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(2)\n\t\t// 排除已停止售卖的 platform=none\n\t\tconst datas = assetsMgr.getJson('headIcon').datas.filter(m => m.platform !== 'none'), heads = this.user.getUnlockHeadIcons(), len = datas.length\n\t\tconst countNode = node.Child('top/count')\n\t\tcountNode.Child('cur', cc.Label).string = heads.length + ''\n\t\tcountNode.Child('max', cc.Label).string = '/' + len\n\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_HEAD_ICON_SORT)\n\t\tif (sort === 1) { // 按获取时间\n\t\t\tdatas.sort((a, b) => {\n\t\t\t\treturn heads.findIndex(m => m === b.icon) - heads.findIndex(m => m === a.icon)\n\t\t\t})\n\t\t}\n\n\t\tthis.socialList = datas\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(len, (it, i) => {\n\t\t\tconst data = datas[i]\n\t\t\tit.Data = { type: 'headicon', json: data, index: i }\n\t\t\tit.Child('bg').active = it.Child('count').active = false\n\t\t\tit.Child('icon').opacity = heads.has(data.icon) ? 255 : 140\n\t\t\tconst icon = it.Child('icon/val')\n\t\t\ticon.width !== 96 && icon.setContentSize(96, 96)\n\t\t\tresHelper.loadPlayerHead(icon, data.icon, this.key, true)\n\t\t})\n\t}\n\n\t// 显示emoji表情\n\tprivate updateChatEmojis(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(2)\n\t\t// 排除 地图表情 type=3\n\t\tconst datas = assetsMgr.getJson('chatEmoji').datas.filter(m => m.type !== 3 && m.cond !== 0), emojis = this.user.getUnlockChatEmojiIds(), len = datas.length\n\t\tconst countNode = node.Child('top/count')\n\t\tcountNode.Child('cur', cc.Label).string = emojis.length + ''\n\t\tcountNode.Child('max', cc.Label).string = '/' + len\n\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_CHAT_EMOJI_SORT)\n\t\tif (sort === 1) { // 按获取时间\n\t\t\tdatas.sort((a, b) => {\n\t\t\t\treturn emojis.findIndex(m => m === b.id) - emojis.findIndex(m => m === a.id)\n\t\t\t})\n\t\t}\n\n\t\tthis.socialList = datas\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(len, (it, i) => {\n\t\t\tconst data = datas[i]\n\t\t\tit.Data = { type: 'chat_emoji', json: data, index: i }\n\t\t\tit.Child('bg').active = it.Child('count').active = false\n\t\t\tit.Child('icon').opacity = emojis.has(data.id) ? 255 : 140\n\t\t\tconst icon = it.Child('icon/val')\n\t\t\ticon.removeAllChildren()\n\t\t\tresHelper.loadEmojiIcon(data.id, icon, this.key)\n\t\t})\n\t}\n\n\t// 显示植物表情\n\tprivate updatePlantBotanys(node?: cc.Node) {\n\t\tnode = node || this.pagesNode_.Child(2)\n\t\tconst datas = assetsMgr.getJson('botany').datas.slice(), botanys = this.user.getUnlockBotanys(), len = datas.length\n\t\tconst countNode = node.Child('top/count')\n\t\tcountNode.Child('cur', cc.Label).string = botanys.length + ''\n\t\tcountNode.Child('max', cc.Label).string = '/' + len\n\n\t\tconst sort = this.user.getTempPreferenceMap(this.PKEY_PLANT_BOTANY_SORT)\n\t\tif (sort === 1) { // 按获取时间\n\t\t\tdatas.sort((a, b) => {\n\t\t\t\treturn botanys.findIndex(m => m.id === b.id) - botanys.findIndex(m => m.id === a.id)\n\t\t\t})\n\t\t}\n\n\t\tthis.socialList = datas\n\t\tconst sv = node.Child('list', cc.ScrollView)\n\t\tsv.stopAutoScroll()\n\t\tsv.content.y = 0\n\t\tsv.List(len, (it, i) => {\n\t\t\tconst data = datas[i]\n\t\t\tit.Data = { type: 'plant_emoji', json: data, index: i }\n\t\t\tit.Child('bg').active = it.Child('count').active = true\n\t\t\tconst has = botanys.has(data.id)\n\t\t\tit.Child('icon').opacity = has ? 255 : 140\n\t\t\tit.Child('count', cc.Label).string = has ? 'x' + data.count : ''\n\t\t\tconst icon = it.Child('icon/val')\n\t\t\ticon.removeAllChildren()\n\t\t\tresHelper.loadGiftIcon(data.id, icon, this.key)\n\t\t})\n\t}\n\n\tprivate async buyHeadIcon(id: number, cond: number) {\n\t\tlet err = '', isExchange = cond === 3\n\t\tif (isExchange) {\n\t\t\terr = await this.user.rsExchangeHeadicon(id)\n\t\t} else {\n\t\t\terr = await this.user.buyHeadIcon(id)\n\t\t}\n\t\tif (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (this.isValid) {\n\t\t\tthis.socialTabsTc_.Tabs(this.curChildTab)\n\t\t\tif (isExchange) {\n\t\t\t\tviewHelper.showAlert('toast.exchange_succeed')\n\t\t\t} else {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate async buyChatEmoji(id: number, cond: number) {\n\t\tlet err = '', isExchange = cond === 3\n\t\tif (isExchange) {\n\t\t\terr = await this.user.rsExchangeChatEmoji(id)\n\t\t} else {\n\t\t\terr = await this.user.buyChatEmoji(id)\n\t\t}\n\t\tif (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (this.isValid) {\n\t\t\tthis.socialTabsTc_.Tabs(this.curChildTab)\n\t\t\tif (isExchange) {\n\t\t\t\tviewHelper.showAlert('toast.exchange_succeed')\n\t\t\t} else {\n\t\t\t\tviewHelper.showAlert('toast.buy_succeed')\n\t\t\t}\n\t\t}\n\t}\n}\n"]}