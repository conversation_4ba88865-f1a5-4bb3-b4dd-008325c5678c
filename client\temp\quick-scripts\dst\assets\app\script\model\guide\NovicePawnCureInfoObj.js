
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NovicePawnCureInfoObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6924bG2oIJENqtJy4UEQJJN', 'NovicePawnCureInfoObj');
// app/script/model/guide/NovicePawnCureInfoObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoviceInjuryPawnInfo = void 0;
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnCureInfoObj_1 = require("../main/PawnCureInfoObj");
// 治疗中伤兵
var NovicePawnCureInfoObj = /** @class */ (function (_super) {
    __extends(NovicePawnCureInfoObj, _super);
    function NovicePawnCureInfoObj() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NovicePawnCureInfoObj.prototype.fromSvr = function (data) {
        _super.prototype.fromSvr.call(this, data);
        this.cureCount = data.cureCount;
        this.curing = data.curing;
        this.deadTime = data.deadTime;
        return this;
    };
    NovicePawnCureInfoObj.prototype.strip = function () {
        return {
            uid: this.uid,
            index: this.index,
            auid: this.auid,
            id: this.id,
            lv: this.lv,
            needTime: this.needTime,
            surplusTime: this.surplusTime,
        };
    };
    NovicePawnCureInfoObj.prototype.toDB = function () {
        return {
            uid: this.uid,
            index: this.index,
            auid: this.auid,
            id: this.id,
            lv: this.lv,
            needTime: this.needTime,
            surplusTime: this.surplusTime,
            deadTime: this.deadTime,
            cureCount: this.cureCount,
            curing: this.curing,
        };
    };
    // 治疗
    NovicePawnCureInfoObj.prototype.curePawn = function (info, army) {
        this.uid = info.uid;
        this.id = info.id;
        this.lv = info.lv;
        this.cureCount = info.cureCount;
        this.deadTime = Date.now();
        this.curing = 1;
        this.auid = army.uid;
        this.index = army.index;
        var costTime = assetsMgr.getJsonData('pawnBase', this.id).drill_time;
        costTime = GameHelper_1.gameHpr.noviceServer.getHospitaCureSpeedTime(costTime, this.lv).time;
        this.needTime = costTime;
        this.surplusTime = 0;
        return this;
    };
    // 开始治疗
    NovicePawnCureInfoObj.prototype.checkStartCure = function (extraTime) {
        if (0 === this.surplusTime) {
            this.deadTime = Date.now() + extraTime;
            this.surplusTime = this.needTime;
        }
    };
    return NovicePawnCureInfoObj;
}(PawnCureInfoObj_1.default));
exports.default = NovicePawnCureInfoObj;
// 可治疗伤兵
var NoviceInjuryPawnInfo = /** @class */ (function () {
    function NoviceInjuryPawnInfo() {
    }
    NoviceInjuryPawnInfo.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv;
        this.cureCount = data.cureCount;
        this.curing = data.curing;
        this.deadTime = data.deadTime;
        this.auid = data.auid;
        return this;
    };
    NoviceInjuryPawnInfo.prototype.strip = function () {
        return {
            uid: this.uid,
            id: this.id,
            lv: this.lv,
            cureCount: this.cureCount,
            curing: this.curing,
            deadTime: this.deadTime,
            auid: this.auid
        };
    };
    NoviceInjuryPawnInfo.prototype.toDB = function () {
        return {
            uid: this.uid,
            id: this.id,
            lv: this.lv,
            cureCount: this.cureCount,
            curing: this.curing,
            deadTime: this.deadTime,
            auid: this.auid
        };
    };
    // 取消治疗
    NoviceInjuryPawnInfo.prototype.cancelCurePawn = function (info) {
        this.uid = info.uid;
        this.id = info.id;
        this.lv = info.lv;
        this.cureCount = info.cureCount;
        this.curing = info.curing;
        this.deadTime = info.deadTime;
        return this;
    };
    return NoviceInjuryPawnInfo;
}());
exports.NoviceInjuryPawnInfo = NoviceInjuryPawnInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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