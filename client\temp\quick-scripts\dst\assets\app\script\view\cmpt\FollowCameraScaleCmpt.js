
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/FollowCameraScaleCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4f5e2aHc7tMjKbswTgw/gi3', 'FollowCameraScaleCmpt');
// app/script/view/cmpt/FollowCameraScaleCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var FollowCameraScaleCmpt = /** @class */ (function (_super) {
    __extends(FollowCameraScaleCmpt, _super);
    function FollowCameraScaleCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.initScale = 1;
        _this.preZoomRatio = 1;
        _this.preScale = 1;
        return _this;
    }
    FollowCameraScaleCmpt.prototype.onLoad = function () {
        this.preZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.preScale = this.node.scale = this.initScale * (2 - this.preZoomRatio);
    };
    FollowCameraScaleCmpt.prototype.update = function () {
        if (this.preZoomRatio !== CameraCtrl_1.cameraCtrl.zoomRatio || this.preScale !== this.node.scale) {
            this.preZoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
            this.preScale = this.node.scale = this.initScale * (2 - this.preZoomRatio);
        }
    };
    __decorate([
        property
    ], FollowCameraScaleCmpt.prototype, "initScale", void 0);
    FollowCameraScaleCmpt = __decorate([
        ccclass
    ], FollowCameraScaleCmpt);
    return FollowCameraScaleCmpt;
}(cc.Component));
exports.default = FollowCameraScaleCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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