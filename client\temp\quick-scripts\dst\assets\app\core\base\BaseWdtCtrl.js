
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseWdtCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3bb58bH9bhMWKJHmA6d0JTc', 'BaseWdtCtrl');
// app/core/base/BaseWdtCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseViewCtrl_1 = require("./BaseViewCtrl");
// 基础挂件控制器
var BaseWdtCtrl = /** @class */ (function (_super) {
    __extends(BaseWdtCtrl, _super);
    function BaseWdtCtrl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    BaseWdtCtrl.prototype.onLoad = function () {
        this._state = 'create';
        this.__listenMaps();
        this.__register();
        this.onCreate();
    };
    BaseWdtCtrl.prototype.onDestroy = function () {
        this._state = 'clean';
        this.__unregister();
        this.onClean();
    };
    BaseWdtCtrl.prototype.onCreate = function () {
    };
    BaseWdtCtrl.prototype.onClean = function () {
    };
    return BaseWdtCtrl;
}(BaseViewCtrl_1.default));
exports.default = BaseWdtCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxiYXNlXFxCYXNlV2R0Q3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwrQ0FBMEM7QUFFMUMsVUFBVTtBQUNWO0lBQXlDLCtCQUFZO0lBQXJEOztJQW9CQSxDQUFDO0lBbEJHLDRCQUFNLEdBQU47UUFDSSxJQUFJLENBQUMsTUFBTSxHQUFHLFFBQVEsQ0FBQTtRQUN0QixJQUFJLENBQUMsWUFBWSxFQUFFLENBQUE7UUFDbkIsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQTtJQUNuQixDQUFDO0lBRUQsK0JBQVMsR0FBVDtRQUNJLElBQUksQ0FBQyxNQUFNLEdBQUcsT0FBTyxDQUFBO1FBQ3JCLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQTtRQUNuQixJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7SUFDbEIsQ0FBQztJQUVNLDhCQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sNkJBQU8sR0FBZDtJQUNBLENBQUM7SUFDTCxrQkFBQztBQUFELENBcEJBLEFBb0JDLENBcEJ3QyxzQkFBWSxHQW9CcEQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZVZpZXdDdHJsIGZyb20gXCIuL0Jhc2VWaWV3Q3RybFwiO1xyXG5cclxuLy8g5Z+656GA5oyC5Lu25o6n5Yi25ZmoXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJhc2VXZHRDdHJsIGV4dGVuZHMgQmFzZVZpZXdDdHJsIHtcclxuXHJcbiAgICBvbkxvYWQoKSB7XHJcbiAgICAgICAgdGhpcy5fc3RhdGUgPSAnY3JlYXRlJ1xyXG4gICAgICAgIHRoaXMuX19saXN0ZW5NYXBzKClcclxuICAgICAgICB0aGlzLl9fcmVnaXN0ZXIoKVxyXG4gICAgICAgIHRoaXMub25DcmVhdGUoKVxyXG4gICAgfVxyXG5cclxuICAgIG9uRGVzdHJveSgpIHtcclxuICAgICAgICB0aGlzLl9zdGF0ZSA9ICdjbGVhbidcclxuICAgICAgICB0aGlzLl9fdW5yZWdpc3RlcigpXHJcbiAgICAgICAgdGhpcy5vbkNsZWFuKClcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25DcmVhdGUoKSB7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XHJcbiAgICB9XHJcbn0iXX0=