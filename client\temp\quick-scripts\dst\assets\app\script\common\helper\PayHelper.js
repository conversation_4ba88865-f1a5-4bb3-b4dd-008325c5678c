
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/PayHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b04Wlx39CPa1OYHnx2Vuj', 'PayHelper');
// app/script/common/helper/PayHelper.ts

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.payHelper = void 0;
var Constant_1 = require("../constant/Constant");
var ECode_1 = require("../constant/ECode");
var Enums_1 = require("../constant/Enums");
var RechargeConfig_1 = require("../constant/RechargeConfig");
var EventType_1 = require("../event/EventType");
var JsbEvent_1 = require("../event/JsbEvent");
var ErrorReportHelper_1 = require("./ErrorReportHelper");
var EventReportHelper_1 = require("./EventReportHelper");
var GameHelper_1 = require("./GameHelper");
var JsbHelper_1 = require("./JsbHelper");
var ViewHelper_1 = require("./ViewHelper");
var GOOGLE_SUB_PRODUCT_IDS = ['jwm_card', 'jwm_ad_free'];
var APPLE_SUB_PRODUCT_IDS = ['jwmCardMonth', 'jwmCardQuarter', 'jwmAdFreeMonth', 'jwmSuperCardQuarter'];
// 同步锁 同时调用的时候 只会执行第一个
function syncLock(target, propertyName, propertyDescriptor) {
    var key = "__lock_" + propertyName;
    if (target && !propertyName) {
        key = target;
    }
    var method = propertyDescriptor.value;
    propertyDescriptor.value = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            var result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this[key]) {
                            return [2 /*return*/, null];
                        }
                        this[key] = true;
                        return [4 /*yield*/, method.apply(this, args)];
                    case 1:
                        result = _a.sent();
                        this[key] = false;
                        return [2 /*return*/, result];
                }
            });
        });
    };
    return propertyDescriptor;
}
/**
 * 支付相关
 */
var PayHelper = /** @class */ (function () {
    function PayHelper() {
        this.inited = false;
        this.initEnded = false;
        this.initFinish = false;
        this.initing = false;
        this.iapCfg = null;
        this.restoredSubs = null;
    }
    PayHelper.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.inited) {
                            return [2 /*return*/];
                        }
                        this.inited = true;
                        this.initFinish = false;
                        this.initEnded = false;
                        this.iapCfg = null;
                        this.restoredSubs = null;
                        return [4 /*yield*/, this.iapInit(1)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, this.initLostOrderList()];
                    case 2:
                        _a.sent();
                        this.initEnded = true;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取商品列表
    PayHelper.prototype.iapInit = function (retry) {
        return __awaiter(this, void 0, void 0, function () {
            var productIds, args, _a, error, result, subs, productList, resultList, i, res, _b, productId, price, currency_pay, currency_price, introductory_price, introductory_pay, productList_1, productList_1_1, product, key;
            var e_1, _c;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (cc.sys.isBrowser) {
                            this.initFinish = true;
                            return [2 /*return*/];
                        }
                        else if (this.initFinish || this.initing || !ut.isMobile()) {
                            return [2 /*return*/];
                        }
                        this.initing = true;
                        _d.label = 1;
                    case 1:
                        if (!(retry > 0)) return [3 /*break*/, 5];
                        console.log('ipInit ...', retry);
                        retry--;
                        productIds = assetsMgr.getJson('recharge').datas.map(function (m) { return m.product_id; });
                        productIds.push(Constant_1.RECHARGE_BATTLE_PASS);
                        productIds.pushArr(Constant_1.MONTH_CARD.reduce(function (val, cur) { return val.concat(cur.RECHARGES); }, []));
                        args = ut.isAndroid() ? { key: productIds, subKey: GOOGLE_SUB_PRODUCT_IDS } : { key: productIds.concat(APPLE_SUB_PRODUCT_IDS).join(',') };
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.IAP_INIT, args)];
                    case 2:
                        _a = _d.sent(), error = _a.error, result = _a.result, subs = _a.subs;
                        if (error === 'noproducts') {
                            console.log('iap init error, no products!');
                            this.initing = false;
                            return [2 /*return*/]; //没有订单
                        }
                        else if (!error) {
                            productList = [];
                            if (ut.isIos()) {
                                resultList = String(result).split('|');
                                for (i = 0; i < resultList.length; ++i) {
                                    res = resultList[i];
                                    _b = __read(res.split(','), 6) //jwm_gold_1,HK$8,HKD,8
                                    , productId = _b[0], price = _b[1], currency_pay = _b[2], currency_price = _b[3], introductory_price = _b[4], introductory_pay = _b[5];
                                    if (introductory_price) {
                                        productList.push({ productId: productId, price: price, currency_pay: currency_pay, currency_price: currency_price, introductory_price: introductory_price, introductory_pay: introductory_pay });
                                    }
                                    else {
                                        productList.push({ productId: productId, price: price, currency_pay: currency_pay, currency_price: currency_price });
                                    }
                                }
                            }
                            else if (ut.isAndroid()) {
                                productList = result.concat(subs || []);
                            }
                            this.iapCfg = {};
                            try {
                                for (productList_1 = (e_1 = void 0, __values(productList)), productList_1_1 = productList_1.next(); !productList_1_1.done; productList_1_1 = productList_1.next()) {
                                    product = productList_1_1.value;
                                    key = product.productId;
                                    if (product.offerId) {
                                        key = product.offerId;
                                    }
                                    else if (product.planId) {
                                        key = product.planId;
                                    }
                                    this.iapCfg[key] = product;
                                }
                            }
                            catch (e_1_1) { e_1 = { error: e_1_1 }; }
                            finally {
                                try {
                                    if (productList_1_1 && !productList_1_1.done && (_c = productList_1.return)) _c.call(productList_1);
                                }
                                finally { if (e_1) throw e_1.error; }
                            }
                            // logger.print(this.iapCfg)
                            console.log('iap init end,' /* , this.iapCfg */);
                            this.initFinish = true;
                            this.initing = false;
                            eventCenter.emit(EventType_1.default.INIT_PAY_FINISH);
                            return [2 /*return*/];
                        }
                        else {
                            console.log('iap init error,', error);
                        }
                        if (!(retry > 0)) return [3 /*break*/, 4];
                        return [4 /*yield*/, ut.wait(2)];
                    case 3:
                        _d.sent();
                        _d.label = 4;
                    case 4: return [3 /*break*/, 1];
                    case 5:
                        this.initing = false;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取为完成的订单
    PayHelper.prototype.initLostOrderList = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, error, result, platform_1, goldProductMap_1, subInfoMap_1, subProductMap_1, ok;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!ut.isMobile()) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getLangOrderList(JsbEvent_1.default.GET_LOST_ORDER_LIST)];
                    case 1:
                        _a = _b.sent(), error = _a.error, result = _a.result;
                        if (!!error) return [3 /*break*/, 3];
                        platform_1 = this.getPlatform();
                        goldProductMap_1 = {}, subInfoMap_1 = {};
                        subProductMap_1 = {};
                        if (platform_1 === Enums_1.PayPlatformType.GOOGLE) {
                            GOOGLE_SUB_PRODUCT_IDS.forEach(function (m) { return subProductMap_1[m] = true; });
                        }
                        else if (platform_1 === Enums_1.PayPlatformType.APPLE) {
                            APPLE_SUB_PRODUCT_IDS.forEach(function (m) { return subProductMap_1[m] = true; });
                        }
                        assetsMgr.getJson('recharge').datas.forEach(function (m) { return goldProductMap_1[m.product_id] = true; });
                        goldProductMap_1[Constant_1.RECHARGE_BATTLE_PASS] = true; //加上战令的
                        GameHelper_1.gameHpr.user.initNativeLostOrderList(result.map(function (m) { return _this.toOrder(platform_1, m); }).filter(function (m) {
                            if (!(m === null || m === void 0 ? void 0 : m.orderId)) {
                                return false;
                            }
                            else if (!goldProductMap_1[m === null || m === void 0 ? void 0 : m.productId]) {
                                if (subProductMap_1[m === null || m === void 0 ? void 0 : m.productId]) {
                                    subInfoMap_1[m.orderId] = m.token;
                                }
                                return false;
                            }
                            return true;
                        }));
                        this.directConsumeSubscriptionOrder(subInfoMap_1);
                        return [4 /*yield*/, GameHelper_1.gameHpr.user.checkHasNotFinishOrder(this.getPlatform())];
                    case 2:
                        ok = _b.sent();
                        if (ok) {
                            ViewHelper_1.viewHelper.showPnl('main/NotFinishOrderTip');
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        logger.print('GET_LOST_ORDER_LIST error,', error);
                        _b.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    PayHelper.prototype.checkPayInit = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!this.initFinish) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.iapInit(1)];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    PayHelper.prototype.clean = function () {
        this.inited = false;
        this.initEnded = false;
        this.initFinish = false;
        this.initing = false;
        this.iapCfg = null;
        this.cleanRestoredSubs();
    };
    PayHelper.prototype.isInitFinish = function () {
        return this.initFinish;
    };
    PayHelper.prototype.isInitSucceed = function () {
        return !!this.iapCfg;
    };
    // 直接消费没有消费掉的订阅
    PayHelper.prototype.directConsumeSubscriptionOrder = function (subInfoMap) {
        return __awaiter(this, void 0, void 0, function () {
            var orderIds, _a, err, data, list, uid, i, l, id;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        orderIds = Object.keys(subInfoMap);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_SubOrderCheck', { list: orderIds })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [], uid = GameHelper_1.gameHpr.getUid();
                        i = 0, l = list.length;
                        _b.label = 2;
                    case 2:
                        if (!(i < l)) return [3 /*break*/, 5];
                        id = list[i];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: subInfoMap[id], type: 'subs' })];
                    case 3:
                        _b.sent();
                        ErrorReportHelper_1.errorReportHelper.reportError('directConsumeSubscriptionOrder', { uid: uid, orderId: id });
                        _b.label = 4;
                    case 4:
                        i++;
                        return [3 /*break*/, 2];
                    case 5: return [2 /*return*/, list];
                }
            });
        });
    };
    // 获取订阅列表
    PayHelper.prototype.getSubscriptions = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, error, result, platform_2, goldProductMap_2, subInfos, subProductMap_2, orders, _b, err, data, list, uid, deleteIdMap_1;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        if (!!this.restoredSubs) return [3 /*break*/, 5];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.getLangOrderList(JsbEvent_1.default.RESTORED_SUB)];
                    case 1:
                        _a = _c.sent(), error = _a.error, result = _a.result;
                        if (!!error) return [3 /*break*/, 4];
                        platform_2 = this.getPlatform();
                        goldProductMap_2 = {}, subInfos = [];
                        assetsMgr.getJson('recharge').datas.forEach(function (m) { return goldProductMap_2[m.product_id] = true; });
                        subProductMap_2 = {};
                        if (platform_2 === Enums_1.PayPlatformType.GOOGLE) {
                            GOOGLE_SUB_PRODUCT_IDS.forEach(function (m) { return subProductMap_2[m] = true; });
                        }
                        else if (platform_2 === Enums_1.PayPlatformType.APPLE) {
                            APPLE_SUB_PRODUCT_IDS.forEach(function (m) { return subProductMap_2[m] = true; });
                        }
                        this.restoredSubs = (result === null || result === void 0 ? void 0 : result.map(function (m) { return _this.toOrder(platform_2, m); }).filter(function (m) {
                            if (!m) {
                                return false;
                            }
                            else if (!m.orderId) {
                                return false;
                            }
                            else if (goldProductMap_2[m.productId] || !subProductMap_2[m.productId]) {
                                return false;
                            }
                            return true;
                        })) || [];
                        if (!(this.restoredSubs.length > 0)) return [3 /*break*/, 3];
                        orders = this.restoredSubs.map(function (m) { return { orderId: m.orderId, productId: m.productId, token: m.token }; });
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_CheckInvalidSubOrder', { list: orders, platform: platform_2 })];
                    case 2:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [], uid = GameHelper_1.gameHpr.getUid();
                        if (list.length > 0) {
                            deleteIdMap_1 = {};
                            list.forEach(function (id) { return deleteIdMap_1[id] = true; });
                            ErrorReportHelper_1.errorReportHelper.reportError('CheckInvalidSubOrder', { uid: uid, orderIds: list });
                            // 删除过期的
                            this.restoredSubs.delete(function (m) { return !!deleteIdMap_1[m.orderId]; });
                        }
                        this.restoredSubs.sort(function (a, b) { return b.purchaseTime - a.purchaseTime; });
                        _c.label = 3;
                    case 3: return [3 /*break*/, 5];
                    case 4:
                        this.restoredSubs = [];
                        _c.label = 5;
                    case 5: return [2 /*return*/, this.restoredSubs];
                }
            });
        });
    };
    PayHelper.prototype.cleanRestoredSubs = function () {
        this.restoredSubs = null;
    };
    // 获取订阅商品价格文本
    PayHelper.prototype.getSubPriceText = function (cardType, type, isSubscripe) {
        var _a;
        var isSaleCard = cardType === Enums_1.MonthlyCardType.SALE;
        if (this.isInitSucceed()) {
            var id = this.getSubProductKey(cardType, type);
            var price = (_a = this.iapCfg[id]) === null || _a === void 0 ? void 0 : _a.price;
            if (price) {
                return price;
            }
        }
        if (isSaleCard) {
            return (type === 'month' ? isSubscripe ? '$1.99' : '$2.59' : '$4.99');
        }
        else {
            return (type === 'month' ? isSubscripe ? '$6.99' : '$8.99' : '$19.99');
        }
    };
    // 获取订阅价格
    PayHelper.prototype.getSubCurrencyPrice = function (cardType, type) {
        var _a;
        var id = this.getSubProductKey(cardType, type);
        var val = (_a = this.iapCfg[id]) === null || _a === void 0 ? void 0 : _a.currency_price;
        return val !== null && val !== void 0 ? val : 0;
    };
    // 获取商品价格 USD
    PayHelper.prototype.getProductCurrencyPrice = function (productId, subInfo) {
        if (subInfo) {
            var key = this.getSubProductKey(subInfo.cardType, subInfo.type, true);
            return RechargeConfig_1.RECHARGE_PRICE_USD_CONFIG[key];
        }
        return RechargeConfig_1.RECHARGE_PRICE_USD_CONFIG[productId];
    };
    PayHelper.prototype.getSubProductKey = function (cardType, type, force) {
        if (force === void 0) { force = false; }
        var key = '';
        var isSaleCard = cardType === Enums_1.MonthlyCardType.SALE;
        if (force || ut.isAndroid()) {
            key = isSaleCard ? "jwm-card-" + type : "jwm-ad-free-" + type;
        }
        else if (ut.isIos()) {
            key = isSaleCard ? 'jwmCard' + ut.initialUpperCase(type) : type === 'month' ? 'jwmAdFreeMonth' : 'jwmSuperCardQuarter';
        }
        return key;
    };
    // 获取商品价格文本
    PayHelper.prototype.getProductPriceText = function (id) {
        if (this.isInitSucceed()) {
            var iap = this.iapCfg[id];
            if (iap === null || iap === void 0 ? void 0 : iap.price) {
                return iap.price;
            }
        }
        var json = assetsMgr.getJson('recharge').get('product_id', id)[0];
        if (!json) {
            return '';
        }
        var area = GameHelper_1.gameHpr.getServerArea();
        return json['money_' + area] || json.money_en;
    };
    // 获取战令价格
    PayHelper.prototype.getBattlePassPriceText = function () {
        if (this.isInitSucceed()) {
            var iap = this.iapCfg[Constant_1.RECHARGE_BATTLE_PASS];
            if (iap === null || iap === void 0 ? void 0 : iap.price) {
                return iap.price;
            }
        }
        return GameHelper_1.gameHpr.isGLobal() ? '$8.99' : '￥59.9';
    };
    PayHelper.prototype.lockScreen = function (val) {
        if (val) {
            mc.lockTouch('buy_product');
        }
        else {
            mc.unlockTouch('buy_product');
        }
        ViewHelper_1.viewHelper.showLoadingWait(val);
    };
    PayHelper.prototype.getPlatform = function () {
        if (ut.isWechatGame()) {
            return Enums_1.PayPlatformType.WX;
        }
        else if (ut.isQQGame()) {
            return Enums_1.PayPlatformType.QQ;
        }
        else if (!ut.isMobile()) {
            return Enums_1.PayPlatformType.NONE;
        }
        else if (ut.isIos()) {
            return Enums_1.PayPlatformType.APPLE;
        }
        else if (GameHelper_1.gameHpr.isInland()) {
            return Enums_1.PayPlatformType.APP_WX;
        }
        return Enums_1.PayPlatformType.GOOGLE;
    };
    // 购买商品
    PayHelper.prototype.buyProduct = function (productId) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var platform, res, order, json, addCount;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        this.lockScreen(true);
                        console.log('buyProduct initFinish=' + this.initFinish);
                        // 0.检测是否初始化完成
                        return [4 /*yield*/, this.checkPayInit()
                            // 是否初始化成功
                        ];
                    case 1:
                        // 0.检测是否初始化完成
                        _c.sent();
                        // 是否初始化成功
                        if (!this.isInitSucceed()) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PAY_FAIL);
                            return [2 /*return*/, false];
                        }
                        platform = this.getPlatform();
                        console.log('buyProduct createPayOrder productId=' + productId + ', platform=' + platform);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_CreatePayOrder', { productId: productId, platform: platform })];
                    case 2:
                        res = _c.sent();
                        if (res.err) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(res.err);
                            return [2 /*return*/, false];
                        }
                        // 2.拉起支付
                        console.log('buyProduct doPay...');
                        return [4 /*yield*/, this.doPay(platform, productId, res.data.uid, res.data.uuid)];
                    case 3:
                        order = _c.sent();
                        if (!order.orderId) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PAY_FAIL);
                            return [2 /*return*/, false];
                        }
                        // 3.发送到服务器验证
                        console.log('buyProduct verifyPayOrder begin');
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifyPayOrder', order)];
                    case 4:
                        res = _c.sent();
                        if (!res.err) return [3 /*break*/, 7];
                        if (!(res.err === ECode_1.ecode.ORDER_FINISHED || res.err === ECode_1.ecode.ORDER_REFUNDED || res.err === ECode_1.ecode.ORDER_VERIFY_REPEAT || res.err === ECode_1.ecode.ORDER_VERIFY_API_ERROR)) return [3 /*break*/, 6];
                        // 先标记消费
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token })
                            // 刷新列表
                        ];
                    case 5:
                        // 先标记消费
                        _c.sent();
                        // 刷新列表
                        GameHelper_1.gameHpr.user.removeNotFinishOrders('orderId', order.orderId);
                        _c.label = 6;
                    case 6:
                        this.lockScreen(false);
                        ViewHelper_1.viewHelper.showAlert(res.err);
                        return [2 /*return*/, false];
                    case 7:
                        // facebook自送收集混乱 ios有数据 安卓得手动上报
                        platform === Enums_1.PayPlatformType.GOOGLE && EventReportHelper_1.eventReportHelper.reportFacebookEvent('fb_mobile_purchase', { valueToSum: order.payAmount * order.quantity, fb_currency: order.currencyType, fb_order_id: order.orderId });
                        json = assetsMgr.getJson('recharge').get('product_id', productId)[0];
                        if (json) {
                            EventReportHelper_1.eventReportHelper.reportAppflyerEvent('purchase_ingot_' + json.ingot);
                        }
                        // 4.标记消费结束
                        console.log('buyProduct consume...');
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token })];
                    case 8:
                        _c.sent();
                        console.log('buyProduct getPayRewards...');
                        // 购买的单次订阅 不用领取奖励
                        if (productId.endsWith('card_once')) {
                            ViewHelper_1.viewHelper.showAlert('toast.mail_monthly_card_reward');
                            this.lockScreen(false);
                            return [2 /*return*/, true];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetPayRewards', { uid: order.cpOrderId, orderId: order.orderId })];
                    case 9:
                        // 5.领取奖励
                        res = _c.sent();
                        if (res.err) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(res.err);
                            return [2 /*return*/, false];
                        }
                        else if (productId === Constant_1.RECHARGE_BATTLE_PASS) {
                            GameHelper_1.gameHpr.user.setBattlePassBuyPass();
                            EventReportHelper_1.eventReportHelper.checkReportShopPackage(productId);
                        }
                        else {
                            GameHelper_1.gameHpr.user.setIngot((_a = res.data) === null || _a === void 0 ? void 0 : _a.ingot); //设置金币
                            GameHelper_1.gameHpr.user.addRechargeCountRecord(productId, 1, false); //添加购买次数
                            addCount = ((_b = res.data) === null || _b === void 0 ? void 0 : _b.addCount) || 0;
                            if (addCount) {
                                GameHelper_1.gameHpr.addGainMassage({ type: Enums_1.CType.INGOT, count: addCount });
                            }
                        }
                        ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                        this.lockScreen(false);
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 拉起支付
    PayHelper.prototype.doPay = function (platform, productId, cpOrderId, uuid, subInfo) {
        return __awaiter(this, void 0, void 0, function () {
            var eventName, data, id, cfg, _a, error, result;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        eventName = '', data = { pay_id: productId };
                        if (platform === Enums_1.PayPlatformType.GOOGLE) {
                            eventName = JsbEvent_1.default.GOOGLE_PAY;
                            data.cpOrderId = cpOrderId + '_' + GameHelper_1.gameHpr.getUid();
                            if (subInfo) { //cardType, type
                                id = this.getSubProductKey(subInfo.cardType, subInfo.type, true);
                                cfg = this.iapCfg[id];
                                data.token = (cfg === null || cfg === void 0 ? void 0 : cfg.token) || '';
                            }
                        }
                        else if (platform === Enums_1.PayPlatformType.APPLE) {
                            eventName = JsbEvent_1.default.APPLE_PAY;
                            data.cpOrderId = uuid;
                            // data.cpOrderId = cpOrderId + '_' + gameHpr.getUid()
                            if (subInfo) {
                                data.nonce = subInfo.nonce;
                                data.signature = subInfo.signature;
                            }
                        }
                        else {
                            return [2 /*return*/, {}];
                        }
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.call(eventName, data)];
                    case 1:
                        _a = _b.sent(), error = _a.error, result = _a.result;
                        logger.print('2.doPay result=', result);
                        return [2 /*return*/, this.toOrder(platform, result || {}, subInfo)];
                }
            });
        });
    };
    PayHelper.prototype.toOrder = function (platform, res, subInfo) {
        var _a;
        var order = { platform: platform };
        if (platform === Enums_1.PayPlatformType.GOOGLE) {
            // order.package = res.packageName
            var _b = __read((res.obfuscatedAccountId || '').split('_'), 2), cpOrderId = _b[0], userId = _b[1];
            order.orderId = res.orderId;
            order.token = res.purchaseToken;
            order.productId = res.productId;
            order.cpOrderId = cpOrderId;
            order.userId = userId || '';
            order.purchaseTime = Number(res.purchaseTime);
            order.quantity = res.quantity || 1;
        }
        else if (platform === Enums_1.PayPlatformType.APPLE) {
            // order.package = gameHpr.isInland() ? 'inland.jwm.twgame' : 'global.jwm.twgame'
            // const [cpOrderId, userId] = (res.applicationUsername || '').split('_')
            order.orderId = res.transactionID;
            order.token = res.receiptCipheredPayload;
            order.productId = res.id;
            // order.cpOrderId = cpOrderId
            // order.userId = userId || ''
            order.purchaseTime = res.purchaseTime ? Number(res.purchaseTime) * 1000 : Date.now();
            order.quantity = res.quantity || 1;
        }
        if (order.cpOrderId === 'null' || !order.cpOrderId) {
            order.cpOrderId = '';
        }
        var iap = (_a = this.iapCfg) === null || _a === void 0 ? void 0 : _a[order.productId];
        order.price = (iap === null || iap === void 0 ? void 0 : iap.price) || '';
        // 订阅
        order.payAmount = this.getProductCurrencyPrice(order.productId, subInfo);
        if (order.payAmount) {
            order.currencyType = 'USD';
        }
        else {
            order.currencyType = (iap === null || iap === void 0 ? void 0 : iap.currency_pay) || 'none';
            if (subInfo) {
                order.payAmount = this.getSubCurrencyPrice(subInfo.cardType, subInfo.type) || 0;
            }
            else {
                order.payAmount = Number(iap === null || iap === void 0 ? void 0 : iap.currency_price) || 0;
            }
            if (ut.isAndroid()) {
                order.payAmount /= 1000000; //google需要除1000000
            }
        }
        return order;
    };
    /**
     * 购买订阅 分特惠月卡及超级月卡两种，每种月卡又分了月度订阅和季度订阅
     * @param cardType 月卡类型：sub_month_card:特惠月卡 sub_super_month_card:超级月卡
     * @param type 订阅类型：month:月度 quarter:季度
     * @returns
     */
    PayHelper.prototype.buySubscription = function (cardType, type) {
        return __awaiter(this, void 0, Promise, function () {
            var productId, isSaleCard, typeStr, platform, res, data, order, ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        productId = '', isSaleCard = cardType === Enums_1.MonthlyCardType.SALE;
                        if (ut.isAndroid()) {
                            productId = isSaleCard ? 'jwm_card' : 'jwm_ad_free';
                        }
                        else if (ut.isIos()) {
                            typeStr = ut.initialUpperCase(type);
                            productId = isSaleCard ? 'jwmCard' + typeStr : type === 'month' ? 'jwmAdFree' + typeStr : 'jwmSuperCard' + typeStr;
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PAY_FAIL);
                            return [2 /*return*/, false];
                        }
                        this.lockScreen(true);
                        logger.print('0.buyProduct initFinish=' + this.initFinish);
                        // 0.检测是否初始化完成
                        return [4 /*yield*/, this.checkPayInit()
                            // 是否初始化成功
                        ];
                    case 1:
                        // 0.检测是否初始化完成
                        _a.sent();
                        // 是否初始化成功
                        if (!this.isInitSucceed()) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PAY_FAIL);
                            return [2 /*return*/, false];
                        }
                        platform = this.getPlatform();
                        logger.print('1.createSubOrder productId=' + productId + ', platform=' + platform + ', type=' + type);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_CreateSubOrder', { productId: productId, platform: platform, type: type, offerId: '' })];
                    case 2:
                        res = _a.sent();
                        if (res.err) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(res.err);
                            return [2 /*return*/, false];
                        }
                        data = res.data;
                        // 2.拉起支付
                        logger.print('2.doPay...');
                        return [4 /*yield*/, this.doPay(platform, productId, data.uid, data.uuid, { cardType: cardType, type: type, nonce: data.nounce || '', signature: data.sign || '' })];
                    case 3:
                        order = _a.sent();
                        if (!order.orderId) {
                            this.lockScreen(false);
                            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PAY_FAIL);
                            return [2 /*return*/, false];
                        }
                        // 3.发送到服务器验证
                        logger.print('3.verifySubOrder order=', order);
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifySubOrder', order)];
                    case 4:
                        res = _a.sent();
                        if (!!res.err) return [3 /*break*/, 5];
                        return [3 /*break*/, 9];
                    case 5:
                        if (!(res.err === ECode_1.ecode.SUBSCRIPTION_TIMEOUT || res.err === ECode_1.ecode.ORDER_VERIFY_API_ERROR)) return [3 /*break*/, 8];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token, type: 'subs' })]; //消费掉
                    case 6:
                        _a.sent(); //消费掉
                        this.lockScreen(false);
                        return [4 /*yield*/, this.showSubscriptionTimeoutTip()];
                    case 7:
                        ok = _a.sent();
                        if (ok) {
                            return [2 /*return*/, this.buySubscription(cardType, type)];
                        }
                        return [2 /*return*/, false];
                    case 8:
                        this.lockScreen(false);
                        ViewHelper_1.viewHelper.showAlert(res.err);
                        return [2 /*return*/, false];
                    case 9:
                        EventReportHelper_1.eventReportHelper.reportFacebookEvent('Subscribe', { fb_order_id: order.cpOrderId == '' ? order.orderId : order.cpOrderId, fb_currency: order.currencyType, valueToSum: order.payAmount * order.quantity });
                        // 4.标记消费结束
                        logger.print('4.consume...');
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token, type: 'subs' })];
                    case 10:
                        _a.sent();
                        ViewHelper_1.viewHelper.showAlert('toast.mail_monthly_card_reward');
                        this.lockScreen(false);
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 显示过期提示
    PayHelper.prototype.showSubscriptionTimeoutTip = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.SUBSCRIPTION_TIMEOUT, {
                            ok: function () { return resolve(true); },
                            cancel: function () { return resolve(false); },
                            lockClose: true,
                        });
                    })];
            });
        });
    };
    // 恢复购买订阅
    PayHelper.prototype.restoreBuySub = function () {
        return __awaiter(this, void 0, void 0, function () {
            var list, restoreSubscriptions;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // if (gameHpr.user.isHasSubscription()) {
                        //     return viewHelper.showAlert('toast.not_can_restore_sub')
                        // }
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, this.getSubscriptions()];
                    case 1:
                        list = _a.sent();
                        restoreSubscriptions = list.slice();
                        if (restoreSubscriptions.length === 0) {
                            ViewHelper_1.viewHelper.showLoadingWait(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_can_restore_sub')];
                        }
                        this.doRestoreBuySub(restoreSubscriptions);
                        return [2 /*return*/];
                }
            });
        });
    };
    PayHelper.prototype.doRestoreBuySub = function (restoreSubscriptions) {
        return __awaiter(this, void 0, void 0, function () {
            var order, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        order = restoreSubscriptions[0];
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifySubOrder', {
                                productId: order.productId,
                                orderId: order.orderId,
                                cpOrderId: order.cpOrderId,
                                token: order.token,
                                platform: order.platform,
                                price: order.price,
                                purchaseTime: order.purchaseTime,
                                currencyType: order.currencyType,
                                payAmount: order.payAmount,
                            })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!(err === ECode_1.ecode.SUBSCRIPTION_TIMEOUT || err === ECode_1.ecode.ORDER_VERIFY_API_ERROR)) return [3 /*break*/, 3];
                        restoreSubscriptions.shift();
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token, type: 'subs' })];
                    case 2:
                        _b.sent();
                        if (restoreSubscriptions.length > 0) {
                            return [2 /*return*/, this.doRestoreBuySub(restoreSubscriptions)];
                        }
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.not_can_restore_sub')];
                    case 3:
                        if (err) {
                            ViewHelper_1.viewHelper.showLoadingWait(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        _b.label = 4;
                    case 4:
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        ViewHelper_1.viewHelper.showAlert('toast.restore_buy_sub_succeed');
                        return [2 /*return*/];
                }
            });
        });
    };
    __decorate([
        syncLock
    ], PayHelper.prototype, "checkPayInit", null);
    return PayHelper;
}());
exports.payHelper = new PayHelper();
if (cc.sys.isBrowser) {
    window['payHelper'] = exports.payHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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