
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cef26WojfNIuqcojO46eFYk', 'NoviceModel');
// app/script/model/guide/NoviceModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var AvoidWarObj_1 = require("../main/AvoidWarObj");
var BTCityObj_1 = require("../main/BTCityObj");
var MapCellObj_1 = require("../main/MapCellObj");
var MarchObj_1 = require("../main/MarchObj");
var SeasonInfo_1 = require("../main/SeasonInfo");
var TransitObj_1 = require("../main/TransitObj");
var NoviceConfig_1 = require("./NoviceConfig");
var RandomObj_1 = require("../common/RandomObj");
var MapUionFindHelper_1 = require("../../common/helper/MapUionFindHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
/**
 * 新手村模块
 */
var NoviceModel = /** @class */ (function (_super) {
    __extends(NoviceModel, _super);
    function NoviceModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.user = null;
        _this.areaCenter = null;
        _this.server = null;
        _this.runTime = 0; //服务器运行时间
        _this.initTime = 0; //初始化时间
        _this.winCondType = 0;
        _this.winCondValue = 0; //胜利条件对应值
        _this.winCondMaxDay = 0; //最大天数
        _this.season = null; //季节信息
        _this.mapCells = []; //地图所有单元格
        _this.gameConfInfo = {}; //游戏配置信息
        _this.marchs = []; //行军列表
        _this.transits = []; //运送列表
        _this.battleDistMap = {}; //战斗分布信息
        _this.avoidWarDistMap = {}; //当前免战分布信息
        _this.btCityQueueMap = {}; //当前修建城市信息
        _this.tondenQueueMap = {}; //当前屯田信息
        _this.allPlayerInfos = {}; //所有玩家的信息
        _this.yetPlayNewCellMap = {}; //已经观看过的地块
        _this.notPlayNewCells = []; //还没有观看过的地块
        _this.tempMainCityCell = null; //临时的主城地块用于更新关联使用
        _this.tempHasUpdateCellInfo = false;
        _this.preWinSize = cc.v2();
        _this.maxTileRange = cc.v2(10, 10); //当前地图显示格子数量范围 半径
        _this.maxMarchLength = 0; //当前地图最大可显示的行军线长度
        _this.cameraInfo = { zoomRatio: -1, position: cc.v2() }; //相机信息
        _this.centre = cc.v2(); //当前地图的中心位置
        _this.lookCell = null; //当前查看的地块
        _this.mainCityInCameraRange = false; //主城是否在相机范围内
        _this.pauseBattleAreaIndex = -1;
        _this.pauseBattleAreaFrameCount = 0;
        _this.tempMudIconMap = {}; //主城周围土地装饰icon
        _this.tempCityMudMap = {}; //城市周围土地下标
        _this.random = null;
        _this.islandRoundMap = {}; //岛屿周围区域，50*50格子以外的区域
        _this.decorationsMap = {}; //地块装饰信息
        _this.isCanAttackPlayer = false; //是否可以攻击玩家
        return _this;
    }
    NoviceModel.prototype.onCreate = function () {
        this.user = this.getModel('user');
        this.areaCenter = this.getModel('areaCenter');
        this.server = this.getModel('novice_server');
    };
    NoviceModel.prototype.clean = function () {
    };
    NoviceModel.prototype.getSceneKey = function () { return 'novice'; };
    NoviceModel.prototype.initEnemy = function (UID, index) {
        var enemy = this.allPlayerInfos[UID] = GameHelper_1.gameHpr.getEmptyPlayerInfo(UID, 0);
        var nickname = assetsMgr.getJsonData('guideText', 'enemy_name')[mc.lang];
        GameHelper_1.gameHpr.initPlayerInfo(enemy, {
            nickname: nickname,
            headIcon: this.user.getHeadIcon(),
            mainCityIndex: index,
        });
    };
    NoviceModel.prototype.init = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var mapId, mapJson, data, uid, player, UID, enemy, nickname, cells, citys, i, l, info, cell, myCells;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.runTime = /* data.runTime || 0 */ 0;
                        this.initTime = Date.now();
                        this.winCondType = 1;
                        this.winCondValue = 30000;
                        this.winCondMaxDay = 30;
                        this.season = new SeasonInfo_1.default().init({ type: 2 });
                        mapId = 0;
                        return [4 /*yield*/, assetsMgr.loadOnceRes('maps/maps_' + mapId, cc.JsonAsset)
                            //加载地图装饰
                        ];
                    case 1:
                        mapJson = _b.sent();
                        //加载地图装饰
                        return [4 /*yield*/, this.loadDecorationsJson(mapId)];
                    case 2:
                        //加载地图装饰
                        _b.sent();
                        return [4 /*yield*/, this.loadRoundJson(mapId)];
                    case 3:
                        _b.sent();
                        this.random = new RandomObj_1.default();
                        data = this.server.init(this.user.getUid(), mapJson);
                        // 初始化player
                        GameHelper_1.gameHpr.player.init(data.player, true);
                        // 初始化user
                        this.user.setPortrayals(data.user.portrayals);
                        this.user.setGold(data.user.gold);
                        // 玩家信息
                        this.allPlayerInfos = {};
                        uid = this.user.getUid();
                        player = this.allPlayerInfos[uid] = GameHelper_1.gameHpr.getEmptyPlayerInfo(uid, 0);
                        GameHelper_1.gameHpr.initPlayerInfo(player, {
                            nickname: this.user.getNickname(), headIcon: this.user.getHeadIcon(), mainCityIndex: NoviceConfig_1.NOVICE_MAINCITY_INDEX,
                            policys: data.player.policySlots,
                            towerLvMap: data.player.towerLvMap,
                        });
                        if (data.enemy.id) {
                            UID = data.enemy.id;
                            enemy = this.allPlayerInfos[UID] = GameHelper_1.gameHpr.getEmptyPlayerInfo(UID, 0);
                            nickname = data.enemy.name;
                            GameHelper_1.gameHpr.initPlayerInfo(enemy, {
                                nickname: nickname,
                                headIcon: this.user.getHeadIcon(),
                                mainCityIndex: 0,
                            });
                        }
                        // 地块信息
                        this.mapCells = [];
                        cells = data.cells;
                        citys = [];
                        this.tempCityMudMap = {};
                        this.tempMudIconMap = {};
                        for (i = 0, l = cells.length; i < l; i++) {
                            info = cells[i];
                            cell = new MapCellObj_1.default().init(i, info.landId);
                            cell.updateInfo(info);
                            this.mapCells.push(cell);
                            if (cell.cityId === Constant_1.CITY_MAIN_NID) { //是否主城
                                citys.push(cell);
                            }
                            if (cell.owner) { //记录玩家的拥有的
                                this.addPlayerInfoByCell(info, cell, true);
                            }
                        }
                        // 如果有的话 刷新一下
                        if (this.lookCell) {
                            this.lookCell = this.mapCells[this.lookCell.index];
                        }
                        // 这里将关联的地块也设置一下
                        citys.forEach(function (m) {
                            var _a;
                            // 地块关联
                            _this.updateMainCityRangeCell(m);
                            // 刷新玩家的地块边界线
                            MapHelper_1.mapHelper.updatePlayerCellBorderLines((_a = _this.getPlayerInfo(m.owner)) === null || _a === void 0 ? void 0 : _a.cells);
                        });
                        // 初始化地图中心位置
                        this.centre.set(MapHelper_1.mapHelper.indexToPoint(GameHelper_1.gameHpr.player.getMainCityIndex()));
                        myCells = (_a = this.getPlayerInfo(this.user.getUid())) === null || _a === void 0 ? void 0 : _a.cells;
                        this.notPlayNewCells.length = 0;
                        this.yetPlayNewCellMap = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.YET_PLAY_NEW_CELL) || {};
                        if (ut.isEmptyObject(this.yetPlayNewCellMap)) {
                            myCells === null || myCells === void 0 ? void 0 : myCells.forEach(function (m, key) { return _this.yetPlayNewCellMap[key] = true; });
                            this.saveYetPlayNewCellMap();
                        }
                        else {
                            myCells === null || myCells === void 0 ? void 0 : myCells.forEach(function (m, key) {
                                var _a;
                                if (!_this.yetPlayNewCellMap[key] && ((_a = m.city) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.CITY_MAIN_NID) {
                                    _this.notPlayNewCells.push(Number(key));
                                }
                            });
                        }
                        // 监听消息
                        this.areaCenter.initNetEvent();
                        // this.getModel<ChatModel>('chat').init()
                        GameHelper_1.gameHpr.net.on('game/OnUpdateWorldInfo', this.OnUpdateWorldInfo, this);
                        // // 获取 行军路线 战斗分布信息
                        this.initMarchs();
                        this.initBattleDist();
                        this.initAvoidWarDist();
                        this.initBTCityQueues();
                        this.server.run();
                        MapUionFindHelper_1.mapUionFindHelper.init();
                        return [2 /*return*/];
                }
            });
        });
    };
    //加载地图装饰UI配置
    NoviceModel.prototype.loadDecorationsJson = function (mapId) {
        return __awaiter(this, void 0, void 0, function () {
            var itemsUrl, itemsJson, i, item, index, list, info;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.decorationsMap = {};
                        itemsUrl = 'decorations/decorations_' + mapId;
                        return [4 /*yield*/, assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)];
                    case 1:
                        itemsJson = _a.sent();
                        if (itemsJson) {
                            for (i = 0; i < itemsJson.json.length; i++) {
                                item = itemsJson.json[i];
                                index = item[0];
                                list = this.decorationsMap[index];
                                if (!list) {
                                    list = this.decorationsMap[index] = [];
                                }
                                info = { decorationJson: assetsMgr.getJsonData('decoration', item[1]), decorationActIndex: item[2] || index };
                                list.push(info);
                            }
                        }
                        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset);
                        return [2 /*return*/];
                }
            });
        });
    };
    //加载地图岛屿周围的接壤部分
    NoviceModel.prototype.loadRoundJson = function (mapId) {
        return __awaiter(this, void 0, void 0, function () {
            var itemsUrl, itemsJson, key, item;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.islandRoundMap = {};
                        itemsUrl = 'rounds/rounds_' + mapId;
                        return [4 /*yield*/, assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)];
                    case 1:
                        itemsJson = _a.sent();
                        if (itemsJson) {
                            for (key in itemsJson.json) {
                                item = itemsJson.json[key];
                                this.islandRoundMap[key] = item;
                            }
                        }
                        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset);
                        return [2 /*return*/];
                }
            });
        });
    };
    NoviceModel.prototype.initMarchs = function () {
        this.marchs = this.server.getMarchs().map(function (m) { return new MarchObj_1.default().init(m); });
    };
    NoviceModel.prototype.initBattleDist = function () {
        this.battleDistMap = {};
        var obj = this.server.getBattleDistMap();
        for (var key in obj) {
            this.battleDistMap[key] = { list: obj[key] };
        }
    };
    NoviceModel.prototype.initAvoidWarDist = function () {
        this.avoidWarDistMap = {};
        var obj = this.server.getAvoidWarDistMap(), now = Date.now();
        for (var key in obj) {
            this.avoidWarDistMap[key] = new AvoidWarObj_1.default().init(key, Math.max(0, obj[key] - now), 0);
        }
    };
    NoviceModel.prototype.initBTCityQueues = function () {
        var _this = this;
        this.btCityQueueMap = {};
        this.server.getBTCityQueues().forEach(function (m) { return _this.btCityQueueMap[m.aIndex] = new BTCityObj_1.default().init(m.strip()); });
    };
    // 获取服务器运行时间
    NoviceModel.prototype.getServerRunTime = function () {
        return Math.max(0, this.runTime + (Date.now() - this.initTime));
    };
    NoviceModel.prototype.isHasLocalLandData = function (sid) {
        return false;
    };
    NoviceModel.prototype.getAlliances = function () {
        return __awaiter(this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, []];
        }); });
    };
    NoviceModel.prototype.applyJoinAlliance = function (uid, desc) {
        return __awaiter(this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, ''];
        }); });
    };
    NoviceModel.prototype.cancelJoinAlliance = function () {
        return __awaiter(this, void 0, void 0, function () { return __generator(this, function (_a) {
            return [2 /*return*/, ''];
        }); });
    };
    // 获取胜利条件数值
    NoviceModel.prototype.getWinCondType = function () { return this.winCondType; };
    NoviceModel.prototype.getWinCondValue = function () { return this.winCondValue; };
    NoviceModel.prototype.getWinCondMaxDay = function () { return this.winCondMaxDay; };
    // 是否血战到底模式
    NoviceModel.prototype.isKarmicMahjong = function () { return this.winCondType === Enums_1.WinCondType.KARMIC_MAHJONG; };
    NoviceModel.prototype.getSeason = function () { return this.season; };
    NoviceModel.prototype.getSeasonPolicys = function () { return this.season.getPolicys(); };
    NoviceModel.prototype.getSeasonType = function () { return 2; }; //新手村用秋季的图
    NoviceModel.prototype.getGameOverInfo = function () { return null; };
    NoviceModel.prototype.isGameOver = function () { return false; };
    // 获取当前已沦陷人数
    NoviceModel.prototype.getCaptureNum = function () { return 0; };
    // 获取专属装备效果
    NoviceModel.prototype.getExclusiveEquipEffects = function (id) {
        var _a, _b;
        return ((_b = (_a = this.gameConfInfo) === null || _a === void 0 ? void 0 : _a.exclusiveMap) === null || _b === void 0 ? void 0 : _b[id]) || [];
    };
    // 获取士兵基础费用
    NoviceModel.prototype.getPawnBaseCost = function (id) {
        var _a, _b;
        return ((_b = (_a = this.gameConfInfo) === null || _a === void 0 ? void 0 : _a.pawnCostMap) === null || _b === void 0 ? void 0 : _b[id]) || 0;
    };
    // 获取时间
    NoviceModel.prototype.getEventValue = function (type) {
        return null;
    };
    // 初始相机信息
    NoviceModel.prototype.initCameraInfo = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        if (uid !== this.cameraInfo.uid) {
            this.cameraInfo.uid = uid;
            this.cameraInfo.zoomRatio = -1;
        }
        if (this.cameraInfo.zoomRatio === -1) { //第一次让相机居中
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(cc.v2(0.5, 0.5).addSelf(this.centre)), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CAMERA_ZOOM));
        }
        else {
            CameraCtrl_1.cameraCtrl.initByPosition(Constant_1.MAP_SHOW_OFFSET, this.cameraInfo.position, this.cameraInfo.zoomRatio, MapHelper_1.mapHelper.MAP_SIZE);
        }
    };
    // 保存相机信息
    NoviceModel.prototype.saveCameraInfo = function () {
        var zoom = this.cameraInfo.saveZoomRatio = this.cameraInfo.zoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.cameraInfo.position.set(CameraCtrl_1.cameraCtrl.getPosition());
        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CAMERA_ZOOM, zoom);
    };
    // 所有地块信息
    NoviceModel.prototype.getMapCells = function () { return this.mapCells; };
    // 获取单个地块信息
    NoviceModel.prototype.getMapCellByPoint = function (point) { return this.getMapCellByIndex(MapHelper_1.mapHelper.pointToIndex(point)); };
    NoviceModel.prototype.getMapCellByIndex = function (index) {
        var _a;
        if (((_a = this.lookCell) === null || _a === void 0 ? void 0 : _a.index) === index) {
            return this.lookCell;
        }
        return this.mapCells[index];
    };
    // 这里检测地块信息是否存在 用于重新连接
    NoviceModel.prototype.isHasMapCellInfo = function () {
        var size = MapHelper_1.mapHelper.MAP_SIZE;
        return this.mapCells.length === size.x * size.y;
    };
    // 获取遗迹列表
    NoviceModel.prototype.getAncientMap = function () { return {}; };
    NoviceModel.prototype.getAncientInfo = function (index) { return null; };
    // 同步地块信息
    NoviceModel.prototype.syncServerCellInfo = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    // 获取战斗分布信息
    NoviceModel.prototype.getBattleDistMap = function () { return this.battleDistMap; };
    NoviceModel.prototype.getCanShowToMiniMapBattles = function () { return []; };
    // 获取免战分布信息
    NoviceModel.prototype.getAvoidWarDistMap = function () { return this.avoidWarDistMap; };
    // 清理普通免战
    NoviceModel.prototype.cleanGeneralAvoidWar = function () {
        var has = false;
        for (var k in this.avoidWarDistMap) {
            if (!this.avoidWarDistMap[k].type) {
                delete this.avoidWarDistMap[k];
                has = true;
            }
        }
        if (has) {
            this.emit(EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO);
        }
    };
    // 获取修建城市列表
    NoviceModel.prototype.getBTCityQueueMap = function () { return this.btCityQueueMap; };
    // 获取屯田信息
    NoviceModel.prototype.getTondenQueueMap = function () { return this.tondenQueueMap; };
    // 获取军队屯田信息
    NoviceModel.prototype.getArmyTondenInfo = function (index, auid) {
        var info = this.tondenQueueMap[index];
        return (info === null || info === void 0 ? void 0 : info.armyUid) === auid ? info : null;
    };
    // 获取正在屯田的数量
    NoviceModel.prototype.getArmyTodeningCount = function () { return Object.keys(this.tondenQueueMap).length; };
    // 获取市场价格
    NoviceModel.prototype.getTradePriceMapByType = function (type) { return {}; };
    // 获取联盟基础信息
    NoviceModel.prototype.getAlliBaseInfo = function (uid) { return {}; };
    // 获取联盟政策
    NoviceModel.prototype.getAlliPolicysByUid = function (uid) { return {}; };
    // 获取盟主位置
    NoviceModel.prototype.getAlliCreaterIndexs = function () { return []; };
    // 中心位置
    NoviceModel.prototype.getCentre = function () { return this.centre; };
    NoviceModel.prototype.setCentre = function (val) {
        this.centre.set(val);
        // 检测主城是否在摄像机范围内
        var isInRange = !GameHelper_1.gameHpr.player.checkMainNotInScreenRange();
        if (this.mainCityInCameraRange !== isInRange) {
            this.mainCityInCameraRange = isInRange;
            this.emit(EventType_1.default.UPDATE_MC_IN_CAM_RANGE, isInRange);
        }
    };
    // 主城是否在相机范围内
    NoviceModel.prototype.isMainCityInCameraRange = function () { return this.mainCityInCameraRange; };
    NoviceModel.prototype.setMainCityInCameraRange = function (val) { this.mainCityInCameraRange = val; };
    // 获取相机保存信息
    NoviceModel.prototype.getCameraInfo = function () { return this.cameraInfo; };
    NoviceModel.prototype.checkUpdateWorldWin = function () {
        var size = CameraCtrl_1.cameraCtrl.getWorldWinSize();
        if (!this.preWinSize.equals(size)) {
            this.preWinSize.set(size);
            this.maxTileRange.set2(Math.ceil(Math.ceil(size.x / Constant_1.TILE_SIZE) / 2) + Constant_1.MAP_EXTRA_SIZE, Math.ceil(Math.ceil(size.y / Constant_1.TILE_SIZE) / 2) + Constant_1.MAP_EXTRA_SIZE);
            this.maxMarchLength = size.mag();
        }
    };
    // 获取最多显示格子数
    NoviceModel.prototype.getMaxTileRange = function () {
        this.checkUpdateWorldWin();
        return this.maxTileRange;
    };
    // 获取行军线最大长度
    NoviceModel.prototype.getMaxMarchLength = function () {
        this.checkUpdateWorldWin();
        return this.maxMarchLength;
    };
    // 获取范围内的所有地块
    NoviceModel.prototype.getRangeCellsByPoint = function (centre) {
        var _this = this;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.getMaxTileRange());
        return points.map(function (p) { return { point: p, cell: _this.mapCells[MapHelper_1.mapHelper.pointToIndex(p)] }; });
    };
    // 更新主城周围的信息
    NoviceModel.prototype.updateMainCityRangeCell = function (cell) {
        var _this = this;
        if (cell.getAcreage() > 1) {
            cell.getOwnPoints().forEach(function (m) {
                var c = _this.mapCells[MapHelper_1.mapHelper.pointToIndex(m)];
                if (!c) {
                    return;
                }
                // 刷新地块底 用石头地作为底
                // mapHelper.updateCityLandDi(c, LandType.STONE, this.mapCells)
                // 设置关联
                if (!m.equals(cell.actPoint)) {
                    c.setCityDepend(cell.city);
                }
            });
        }
        this.randCityMud(cell);
        this.tempMudIconMap = {};
    };
    NoviceModel.prototype.getAllPlayerMap = function () {
        return this.allPlayerInfos;
    };
    // 获取玩家信息
    NoviceModel.prototype.getPlayerInfo = function (uid) {
        return this.allPlayerInfos[uid];
    };
    // 根据名字获取玩家信息
    NoviceModel.prototype.getPlayerByName = function (name) {
        for (var key in this.allPlayerInfos) {
            var plr = this.allPlayerInfos[key];
            if (plr.nickname === name) {
                return plr;
            }
        }
        return null;
    };
    // 获取玩家
    NoviceModel.prototype.getPlayerByNameOrUID = function (name) {
        if (!name) {
            return null;
        }
        var plr = null;
        var uid = Number(name);
        if (uid) {
            plr = this.getPlayerInfo(name);
        }
        if (!plr) {
            plr = this.getPlayerByName(name);
        }
        return plr;
    };
    //
    NoviceModel.prototype.addNotPlayNewCells = function (index) {
        if (this.yetPlayNewCellMap[index] || this.notPlayNewCells.has(index)) {
            return;
        }
        this.notPlayNewCells.push(index);
    };
    NoviceModel.prototype.getNotPlayNewCells = function () {
        var _this = this;
        if (this.notPlayNewCells.length === 0) {
            return this.notPlayNewCells;
        }
        var cells = [];
        this.notPlayNewCells.forEach(function (m) {
            _this.yetPlayNewCellMap[m] = true;
            cells.push(m);
        });
        this.saveYetPlayNewCellMap();
        this.notPlayNewCells = [];
        return cells;
    };
    NoviceModel.prototype.setYetPlayNewCellMap = function (index) {
        if (!this.yetPlayNewCellMap[index]) {
            this.notPlayNewCells.remove(index);
            this.yetPlayNewCellMap[index] = true;
            this.saveYetPlayNewCellMap();
            return true;
        }
        return false;
    };
    NoviceModel.prototype.saveYetPlayNewCellMap = function () {
        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.YET_PLAY_NEW_CELL, this.yetPlayNewCellMap);
    };
    // 添加玩家信息
    NoviceModel.prototype.addPlayerInfoByCell = function (data, cell, init) {
        var _a;
        if (!data) {
            return;
        }
        else if (data.owner !== cell.owner && cell.owner) { //如果之前有人了 先删掉
            var cells = (_a = this.allPlayerInfos[cell.owner]) === null || _a === void 0 ? void 0 : _a.cells;
            cells === null || cells === void 0 ? void 0 : cells.delete(cell.index);
            !init && MapHelper_1.mapHelper.updatePlayerCellBorderLines(cells);
        }
        else if (!data.owner) {
            return;
        }
        var player = this.allPlayerInfos[data.owner];
        if (!player) {
            player = this.allPlayerInfos[data.owner] = GameHelper_1.gameHpr.getEmptyPlayerInfo(data.owner, data.index);
        }
        // 添加地块信息
        player.cells.set(cell.index, cell);
        // 如果玩家信息记录玩家信息
        GameHelper_1.gameHpr.initPlayerInfo(player, data.player);
        // 刷新边框线
        !init && MapHelper_1.mapHelper.updatePlayerCellBorderLines(player.cells);
    };
    // 检测是否可以攻占地块
    NoviceModel.prototype.checkCanOccupyCell = function (cell) {
        if (cell.isOneAlliance()) {
            return false; //盟友不能攻占
        }
        // 检测这块地4周是否有我方的地
        var points = MapHelper_1.mapHelper.getOnePointOuter(cell.actPoint, cell.getSize());
        for (var i = 0; i < points.length; i++) {
            var nearCell = this.getMapCellByPoint(points[i]);
            if (!nearCell) {
                continue;
            }
            else if (nearCell.isOneAlliance()) {
                return true;
            }
            else if (nearCell.landType == Enums_1.LandType.PASS && MapHelper_1.mapHelper.checkBridgeCanPass(cell, nearCell)) {
                return true; //如果是桥需要看 桥的另外一头是否有我方的地 
            }
        }
        return false;
    };
    NoviceModel.prototype.checkCanOccupyCellByIndex = function (index) {
        var cell = this.getMapCellByIndex(index);
        return !!cell && this.checkCanOccupyCell(cell);
    };
    // 设置当前查看的地块信息
    NoviceModel.prototype.getLookCell = function () { return this.lookCell; };
    NoviceModel.prototype.setLookCell = function (cell) {
        this.lookCell = cell;
    };
    // 获取城市皮肤id
    NoviceModel.prototype.getCitySkinByIndex = function (index) {
        return 0;
    };
    // 刷新城市皮肤
    NoviceModel.prototype.updateCitySkin = function (index, id) {
    };
    // 获取所有行军信息
    NoviceModel.prototype.getAllMarchs = function () {
        var arr = [];
        arr.pushArr(this.marchs);
        arr.pushArr(this.transits);
        return arr;
    };
    NoviceModel.prototype.getMarchs = function () { return this.marchs; };
    NoviceModel.prototype.getTransits = function () { return this.transits; };
    // 刷新行军相关
    NoviceModel.prototype.updateMarchByIndex = function (index) {
        var has = false;
        this.marchs.forEach(function (m) {
            if (index === -1 || m.startIndex === index || m.targetIndex === index) {
                var type = m.targetType;
                if (m.updateTargetType() !== type) {
                    has = true;
                }
            }
        });
        if (has) {
            this.emit(EventType_1.default.UPDATE_ALL_MARCH);
        }
    };
    // 删除行军线
    NoviceModel.prototype.removeMarch = function (uid) {
        var march = this.marchs.remove('uid', uid);
        march && this.emit(EventType_1.default.REMOVE_MARCH, march);
    };
    // 设置行军线透明度
    NoviceModel.prototype.setAllMarchOpacity = function (progressMap, defaultRatio) {
        if (defaultRatio === void 0) { defaultRatio = 100; }
        GameHelper_1.gameHpr.world.getAllMarchs().forEach(function (m) {
            var _a;
            var progress = (_a = progressMap[m.getMarchLineType()]) !== null && _a !== void 0 ? _a : defaultRatio;
            m.opacity = Math.floor(255 * progress * 0.01);
        });
        this.emit(EventType_1.default.UPDATE_MARCH_OPACITY);
    };
    // 获取城市产出
    NoviceModel.prototype.getCityOutputMap = function () {
        return {};
    };
    NoviceModel.prototype.setCityOutput = function (index, type) {
    };
    // 设置暂停区域战斗时间
    NoviceModel.prototype.setPauseBattleAreaInfo = function (index, frameCount) {
        this.pauseBattleAreaIndex = index;
        this.pauseBattleAreaFrameCount = frameCount;
    };
    // 检测是否可以行动
    NoviceModel.prototype.isCanDoAction = function (armys) {
        return __awaiter(this, void 0, void 0, function () {
            var serverArmys, stateMap, i, i;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        serverArmys = _a.sent();
                        stateMap = {};
                        for (i = 0; i < serverArmys.length; i++) {
                            stateMap[serverArmys[i].uid] = serverArmys[i].state;
                        }
                        for (i = 0; i < armys.length; i++) {
                            armys[i].state = stateMap[armys[i].uid];
                            if (armys[i].state === Enums_1.ArmyState.FIGHT) {
                                return [2 /*return*/, false];
                            }
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 攻占地块
    NoviceModel.prototype.occupyCell = function (armys, index, autoBackType, isSameSpeed, isGuideBattle) {
        return __awaiter(this, void 0, void 0, function () {
            var speed;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.isCanDoAction(armys)];
                    case 1:
                        if (!(_a.sent())) {
                            ViewHelper_1.viewHelper.showAlert('toast.battling_cant_operation');
                            return [2 /*return*/, ''];
                        }
                        speed = isGuideBattle ? -NoviceConfig_1.NOVICE_FIRST_MOVE_MUL : -NoviceConfig_1.NOVICE_ARMY_MOVE_MUL;
                        speed = isSameSpeed ? this.server.getArmysMarchSameSpeed(armys, index, speed) : speed;
                        armys.forEach(function (data) {
                            var backIndex = -1;
                            if (autoBackType === 1) { //最近要塞或者主城
                                backIndex = -2;
                            }
                            else if (autoBackType === 2) { //主城
                                backIndex = GameHelper_1.gameHpr.player.getMainCityIndex();
                            }
                            else if (autoBackType === 3) { //出发点
                                backIndex = data.index;
                            }
                            var area = _this.server.getArea(data.index);
                            var army = area === null || area === void 0 ? void 0 : area.getArmyByUid(data.uid);
                            if (army) {
                                _this.server.armyAutoBackIndexMap[army.uid] = backIndex;
                                _this.server.addMarchArmy(army.aIndex, army, index, speed, false);
                            }
                        });
                        return [2 /*return*/, ''];
                }
            });
        });
    };
    // 移动军队
    NoviceModel.prototype.moveCellArmy = function (armys, index, fastMode, isSameSpeed) {
        if (fastMode === void 0) { fastMode = false; }
        return __awaiter(this, void 0, void 0, function () {
            var speed;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.isCanDoAction(armys)];
                    case 1:
                        if (!(_a.sent())) {
                            ViewHelper_1.viewHelper.showAlert('toast.battling_cant_operation');
                            return [2 /*return*/, ''];
                        }
                        speed = fastMode ? -NoviceConfig_1.NOVICE_FIRST_MOVE_MUL : -NoviceConfig_1.NOVICE_ARMY_MOVE_MUL;
                        speed = isSameSpeed ? this.server.getArmysMarchSameSpeed(armys, index, speed) : speed;
                        armys.forEach(function (data) {
                            var area = _this.server.getArea(data.index);
                            var army = area === null || area === void 0 ? void 0 : area.getArmyByUid(data.uid);
                            if (army) {
                                _this.server.armyAutoBackIndexMap[army.uid] = -1;
                                _this.server.addMarchArmy(army.aIndex, army, index, speed, false);
                            }
                        });
                        return [2 /*return*/, ''];
                }
            });
        });
    };
    // 取消行军
    NoviceModel.prototype.cancelMarch = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.server.cancelMarch(uid);
                return [2 /*return*/, ''];
            });
        });
    };
    // 取消行军
    NoviceModel.prototype.forceRevoke = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, ''];
            });
        });
    };
    // 修建城市
    NoviceModel.prototype.createCity = function (index, id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                _a = this.server.HD_CreateCity({ index: index, id: id }), err = _a.err, data = _a.data;
                if (!err && data) {
                    GameHelper_1.gameHpr.player.updateOutputByFlags(data.output);
                }
                return [2 /*return*/, err];
            });
        });
    };
    // 拆除城市
    NoviceModel.prototype.dismantleCity = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                _a = this.server.HD_DismantleCity({ index: index }), err = _a.err, data = _a.data;
                return [2 /*return*/, err];
            });
        });
    };
    NoviceModel.prototype.update = function (dt) {
        var _a;
        if (this.pauseBattleAreaIndex > 0) {
            var fsp = (_a = this.areaCenter.getArea(this.pauseBattleAreaIndex)) === null || _a === void 0 ? void 0 : _a.getFspModel();
            if (!fsp) {
                if (mc.currWindName === 'novice') {
                    this.pauseBattleAreaIndex = -1;
                    this.pauseBattleAreaFrameCount = 0;
                }
            }
            else if (mc.currWindName === 'novice') {
                this.pauseBattleAreaIndex = -1;
                this.pauseBattleAreaFrameCount = 0;
                fsp.setPause(false);
            }
            else if (!fsp.isPause() && fsp.getCurrentFrameIndex() >= this.pauseBattleAreaFrameCount) {
                fsp.setPause(true);
            }
        }
    };
    NoviceModel.prototype.OnUpdateWorldInfo = function (data) {
        var _this = this;
        this.tempMainCityCell = null;
        data.list.forEach(function (m) { return _this.updateWorldInfoOne(m.type, m['data_' + m.type]); });
        // 有更新地块信息发通知
        if (this.tempHasUpdateCellInfo) {
            this.emit(EventType_1.default.UPDATE_CELL_INFO);
        }
        // 添加关联
        if (this.tempMainCityCell) {
            this.updateMainCityRangeCell(this.tempMainCityCell);
            this.tempMainCityCell = null;
        }
        // 播放效果
        if (this.notPlayNewCells.length > 0) {
            this.emit(EventType_1.default.PLAY_NEW_CELL_EFFECT);
        }
    };
    NoviceModel.prototype.updateWorldInfoOne = function (type, data) {
        var _this = this;
        var _a, _b, _c;
        if (type === Enums_1.NotifyType.ADD_CELL) { //添加地块
            var cell = this.mapCells[data.index];
            if (cell) {
                MapUionFindHelper_1.mapUionFindHelper.checkNearUnLock(cell);
                var isPlayer = !!cell.owner;
                // 刷新边框线
                this.addPlayerInfoByCell(data, cell);
                // 刷新信息
                cell === null || cell === void 0 ? void 0 : cell.updateInfo(data);
                // 添加到等待播放特效的列表
                if (((_a = cell.city) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.CITY_MAIN_NID && cell.isOwn()) {
                    this.addNotPlayNewCells(data.index);
                }
                // 刷新关联
                if (cell.getAcreage() > 1) {
                    this.tempMainCityCell = cell;
                }
                // 当前行军是否有这块地
                this.updateMarchByIndex(data.index);
                // 是否自己的地块
                if (cell.isOwn() && !isPlayer) {
                    GameHelper_1.gameHpr.player.addTodayOccupyCellCount(1);
                }
                this.tempHasUpdateCellInfo = true;
            }
        }
        else if (type === Enums_1.NotifyType.REMOVE_CELLS) { //删除地块
            data.forEach(function (index) {
                var _a;
                (_a = _this.mapCells[index]) === null || _a === void 0 ? void 0 : _a.updateInfo({});
                _this.areaCenter.removeArea(index); //删除之后重新申请
            });
            this.tempHasUpdateCellInfo = true;
        }
        else if (type === Enums_1.NotifyType.DELETE_PLAYER) { //删除玩家
            delete this.allPlayerInfos[data];
        }
        else if (type === Enums_1.NotifyType.UPDATE_CELL_HP) { //刷新地块血量
            // const cell = this.mapCells[data.index]
            // cell.hp = data.hp
            // this.areaCenter.getArea(data.index)?.updateHp(data.hp)
        }
        else if (type === Enums_1.NotifyType.ADD_MARCH) { //添加行军
            var march = this.marchs.find(function (m) { return m.uid === data.uid; }) || this.marchs.add(new MarchObj_1.default());
            march.init(data);
            this.emit(EventType_1.default.ADD_MARCH, march);
        }
        else if (type === Enums_1.NotifyType.REMOVE_MARCH) { //删除行军
            this.removeMarch(data.uid);
        }
        else if (type === Enums_1.NotifyType.ADD_TRANSIT) { //添加运送
            var index = GameHelper_1.gameHpr.player.getMainCityIndex();
            if (data.targetIndex === index || data.startIndex === index) {
                var transit = this.transits.find(function (m) { return m.uid === data.uid; }) || this.transits.add(new TransitObj_1.default());
                transit.init(data);
                this.emit(EventType_1.default.ADD_MARCH, transit);
            }
        }
        else if (type === Enums_1.NotifyType.REMOVE_TRANSIT) { //删除运送
            var transit = this.transits.remove('uid', data);
            transit && this.emit(EventType_1.default.REMOVE_MARCH, transit);
        }
        else if (type === Enums_1.NotifyType.BATTLE_DIST) { //战斗分布
            if ((_b = data.uids) === null || _b === void 0 ? void 0 : _b.length) {
                this.battleDistMap[data.index] = { list: data.uids };
            }
            else {
                delete this.battleDistMap[data.index];
            }
            this.emit(EventType_1.default.UPDATE_BATTLE_DIST_INFO);
            this.updateMarchByIndex(data.index);
        }
        else if (type === Enums_1.NotifyType.AREA_AVOID_WAR) { //免战分布
            if (!data.time) {
                delete this.avoidWarDistMap[data.index];
            }
            else {
                this.avoidWarDistMap[data.index] = new AvoidWarObj_1.default().init(data.index, data.time, data.type || 0);
            }
            this.emit(EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO);
        }
        else if (type === Enums_1.NotifyType.ADD_BTCITY) { //添加修建城市
            this.btCityQueueMap[data.index] = new BTCityObj_1.default().init(data);
            this.emit(EventType_1.default.UPDATE_BT_CITY, data.index);
        }
        else if (type === Enums_1.NotifyType.REMOVE_BTCITY) { //删除修建城市
            delete this.btCityQueueMap[data.index];
            if (data.cell) {
                (_c = this.mapCells[data.index]) === null || _c === void 0 ? void 0 : _c.updateInfo(data.cell);
                var area = this.areaCenter.getArea(data.index);
                area === null || area === void 0 ? void 0 : area.updateBuilds(data.builds || []); //刷新区域的建筑信息
                area === null || area === void 0 ? void 0 : area.updateCity(data.cell.cityId); //刷新区域的城市信息
            }
            this.emit(EventType_1.default.UPDATE_BT_CITY, data.index);
        }
        else if (type === Enums_1.NotifyType.CHANGE_TITLE) { //改变玩家称号
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.title = data.title;
                this.emit(EventType_1.default.UPDATE_PLAYER_NICKNAME, plr);
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_TOWER_LV) { //刷新玩家箭塔等级
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.towerLvMap = data.towerLvMap || {};
                this.emit(EventType_1.default.UPDATE_PLAYER_TOWER_LV, data);
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_POLICY) { //刷新玩家政策
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.policys = GameHelper_1.gameHpr.fromSvrByPolicys(data.policys || {});
            }
        }
    };
    NoviceModel.prototype.getUserCells = function (uid) {
        var _a;
        return (_a = this.allPlayerInfos[uid]) === null || _a === void 0 ? void 0 : _a.cells;
    };
    NoviceModel.prototype.getDecorationByIndex = function (index) {
        return this.decorationsMap[index];
    };
    // 随机主城周围的土地
    NoviceModel.prototype.randCityMud = function (cell) {
        if (cell.isAncient()) {
            return;
        }
        var muList = this.tempCityMudMap[cell.index];
        if (!muList) {
            muList = this.tempCityMudMap[cell.index] = [];
        }
        muList.length = 0;
        this.random.setSeed(cell.index);
        var mudIndex = this.random.get(0, MapHelper_1.mapHelper.cityMudList.length - 1);
        var mudList = MapHelper_1.mapHelper.cityMudList[mudIndex];
        for (var i = 0; i < mudList.length; i++) {
            var targetX = cell.point.x + mudList[i][0];
            var targetY = cell.point.y + mudList[i][1];
            var index = targetY * MapHelper_1.mapHelper.MAP_SIZE.x + targetX;
            if (this.mapCells[index]) {
                muList.push(index);
                var info = { decorationActIndex: index, decorationJson: assetsMgr.getJsonData('decoration', mudList[i][2]) };
                var list = this.decorationsMap[index];
                if (!list) {
                    this.decorationsMap[index] = list = [];
                }
                list.push(info);
            }
        }
    };
    //获取主城土地
    NoviceModel.prototype.getMudJsonByIndex = function (index) {
        var list = this.decorationsMap[index];
        if (list) {
            for (var i = 0; i < list.length; i++) {
                if (list[i].decorationJson.type === Enums_1.DecorationType.MUD || list[i].decorationJson.type === Enums_1.DecorationType.MUD_OUTER) {
                    return list[i].decorationJson;
                }
            }
        }
        return null;
    };
    //获取装饰icon，主城土地icon需要转换一次
    NoviceModel.prototype.getDecorationIcon = function (decorationJson, decorationActIndex) {
        if (decorationJson.type == Enums_1.DecorationType.MUD || decorationJson.type == Enums_1.DecorationType.MUD_OUTER) {
            var icon = this.tempMudIconMap[decorationActIndex];
            if (!icon) {
                //方向：左上右下
                var directionList = [[-1, 0], [0, 1], [1, 0], [0, -1]];
                var key = '';
                var point = this.mapCells[decorationActIndex].point;
                for (var i = 0; i < directionList.length; i++) {
                    var dir = directionList[i];
                    var x = point.x + dir[0];
                    var y = point.y + dir[1];
                    var index = y * MapHelper_1.mapHelper.MAP_SIZE.x + x;
                    var nearMudJson = this.getMudJsonByIndex(index);
                    if (nearMudJson) {
                        if (decorationJson.type === Enums_1.DecorationType.MUD) {
                            key += nearMudJson.type === Enums_1.DecorationType.MUD ? 1 : 0;
                        }
                        else if (decorationJson.type === Enums_1.DecorationType.MUD_OUTER) {
                            key += (nearMudJson.type === Enums_1.DecorationType.MUD || nearMudJson.type === Enums_1.DecorationType.MUD_OUTER) ? 1 : 0;
                        }
                    }
                    else {
                        key += 0;
                    }
                }
                var targetId = decorationJson.type == Enums_1.DecorationType.MUD ? Constant_1.DECORATION_MUD_CONF[key] : Constant_1.DECORATION_MUD_OUTER_CONF[key];
                if (typeof targetId == 'object') {
                    this.random.setSeed(decorationActIndex);
                    var endIndex = this.random.get(0, targetId.length - 1);
                    targetId = targetId[endIndex];
                }
                var targetData = assetsMgr.getJsonData('decoration', targetId);
                icon = this.tempMudIconMap[decorationActIndex] = targetData.icon;
            }
            return icon;
        }
        return decorationJson.icon;
    };
    NoviceModel.prototype.getRoundId = function (x, y) {
        var key = x + '_' + y;
        var landId = this.islandRoundMap[key];
        return landId;
    };
    NoviceModel = __decorate([
        mc.addmodel('novice')
    ], NoviceModel);
    return NoviceModel;
}(mc.BaseModel));
exports.default = NoviceModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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