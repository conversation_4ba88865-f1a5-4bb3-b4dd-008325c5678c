
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PawnAttrBoxPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0218drSnHNFH4w+EujpXLUb', 'PawnAttrBoxPnlCtrl');
// app/script/view/common/PawnAttrBoxPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ccclass = cc._decorator.ccclass;
var PawnAttrBoxPnlCtrl = /** @class */ (function (_super) {
    __extends(PawnAttrBoxPnlCtrl, _super);
    function PawnAttrBoxPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
    }
    //@end
    PawnAttrBoxPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PawnAttrBoxPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    PawnAttrBoxPnlCtrl.prototype.onEnter = function (data) {
        var node = this.rootNode_;
        var lay = node.Child('hp/lay');
        var maxHp = data.getMaxHp();
        if (maxHp !== data.attrJson.hp) {
            var val = maxHp - data.attrJson.hp;
            lay.children.forEach(function (m) { return m.active = true; });
            lay.Child('val', cc.Label).string = data.getHpText() + '(' + data.attrJson.hp;
            lay.Child('0', cc.Label).Color(val < 0 ? '#C34A32' : '#49983C').string = ut.numberToString(val);
        }
        else {
            lay.Swih('val')[0].Component(cc.Label).string = data.getHpText();
        }
        lay = node.Child('anger/lay');
        var maxAnger = data.getMaxAnger();
        if (maxAnger !== data.attrJson.anger) {
            var val = maxAnger - data.attrJson.anger;
            lay.children.forEach(function (m) { return m.active = true; });
            lay.Child('val', cc.Label).string = data.getAngerText() + '(' + data.attrJson.anger;
            lay.Child('0', cc.Label).Color(val < 0 ? '#C34A32' : '#49983C').string = ut.numberToString(val);
        }
        else {
            lay.Swih('val')[0].Component(cc.Label).string = data.getAngerText();
        }
        this.updateAttack(node.Child('attack/lay'), data);
        node.Child('attack_range/val', cc.Label).setLocaleKey('ui.range_desc', data.getAttackRange());
        node.Child('move_range/val', cc.Label).setLocaleKey('ui.range_desc', data.getMoveRange());
        if (node.Child('cereal_c').active = !!data.baseJson.cereal_cost) {
            node.Child('cereal_c/val', cc.Label).string = data.baseJson.cereal_cost + '';
        }
    };
    PawnAttrBoxPnlCtrl.prototype.onRemove = function () {
    };
    PawnAttrBoxPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    PawnAttrBoxPnlCtrl.prototype.updateAttack = function (node, data) {
        var skill = data.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        var attack = data.amendAttack(data.attack);
        if (attack !== data.attrJson.attack) {
            node.children.forEach(function (m) { return m.active = true; });
            var val = attack - data.attrJson.attack;
            node.Child('val', cc.Label).string = this.getAttackText(attack, data, skill) + '(' + this.getBaseAttackText(data, skill);
            node.Child('0', cc.Label).Color(val < 0 ? '#C34A32' : '#49983C').string = ut.numberToString(val);
        }
        else {
            node.Swih('val')[0].Component(cc.Label).string = this.getAttackText(attack, data, skill);
        }
    };
    PawnAttrBoxPnlCtrl.prototype.getAttackText = function (attack, data, skill) {
        if (skill) {
            var maxAttack = skill.value + Math.max(0, data.attack - data.attrJson.attack);
            return attack + '-' + data.amendAttack(maxAttack);
        }
        return attack + '';
    };
    PawnAttrBoxPnlCtrl.prototype.getBaseAttackText = function (data, skill) {
        if (skill) {
            return '[' + data.attrJson.attack + '-' + skill.value + ']';
        }
        return data.attrJson.attack + '';
    };
    PawnAttrBoxPnlCtrl = __decorate([
        ccclass
    ], PawnAttrBoxPnlCtrl);
    return PawnAttrBoxPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PawnAttrBoxPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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