
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/MapMarkPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7e6b0PTfj5M9bH7fMG5lVnL', 'MapMarkPnlCtrl');
// app/script/view/main/MapMarkPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MapMarkPnlCtrl = /** @class */ (function (_super) {
    __extends(MapMarkPnlCtrl, _super);
    function MapMarkPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.flagsNode_ = null; // path://root/flags_tce_n
        _this.infoNode_ = null; // path://root/info_n
        _this.textNode_ = null; // path://root/text_n
        _this.inputDescEb_ = null; // path://root/text_n/input_desc_eb
        _this.ontopTge_ = null; // path://root/ontop_t_te
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.point = cc.v2();
        _this.index = -1;
        _this.selectFlag = 0;
        return _this;
    }
    MapMarkPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MapMarkPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MapMarkPnlCtrl.prototype.onEnter = function (point, isOntop) {
        this.point.set(point);
        this.index = MapHelper_1.mapHelper.pointToIndex(point);
        this.ontopTge_.setActive(isOntop);
        // 显示标记点
        this.updateFlags();
    };
    MapMarkPnlCtrl.prototype.onRemove = function () {
        this.inputDescEb_.string = '';
    };
    MapMarkPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/flags_tce_n
    MapMarkPnlCtrl.prototype.onClickFlags = function (event, data) {
        !data && audioMgr.playSFX('click');
        var flag = this.selectFlag = Number(event.node.name);
        this.updateSelectFlag(flag);
    };
    // path://root/buttons_n/add_be
    MapMarkPnlCtrl.prototype.onClickAdd = function (event, data) {
        var _this = this;
        var index = this.index;
        var flag = this.selectFlag, desc = this.inputDescEb_.string.trim();
        if (ut.getStringLen(desc) > 18 || GameHelper_1.gameHpr.getTextNewlineCount(desc) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        var info = GameHelper_1.gameHpr.player.getMapMark(index);
        if (info) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.same_mark_tip', {
                params: [info.flag - 1, flag - 1],
                ok: function () { return _this.add(index, flag, desc); },
                cancel: function () { },
            });
        }
        this.add(index, flag, desc);
    };
    // path://root/buttons_n/cancel_be
    MapMarkPnlCtrl.prototype.onClickCancel = function (event, _) {
        var flag = this.selectFlag;
        var it = this.flagsNode_.Child(flag), data = it.Data;
        if (data) {
            it.Data = null;
            it.Child('root/bg').opacity = 128;
            this.updateSelectFlag(flag);
            GameHelper_1.gameHpr.player.removeMapMark(data.index);
        }
        // 删除记录
        GameHelper_1.gameHpr.updateOntopFlagData(flag, -1, 0);
    };
    // path://root/buttons_n/goto_be
    MapMarkPnlCtrl.prototype.onClickGoto = function (event, _) {
        var _a;
        var data = (_a = this.flagsNode_.Child(this.selectFlag)) === null || _a === void 0 ? void 0 : _a.Data;
        if (data) {
            this.emit(mc.Event.HIDE_ALL_PNL, 'main/MapMark|main/WorldMap');
            GameHelper_1.gameHpr.gotoTargetPos(data.index);
        }
    };
    // path://root/ontop_t_te
    MapMarkPnlCtrl.prototype.onClickOntop = function (event, _) {
        var _a;
        var data = (_a = this.flagsNode_.Child(this.selectFlag)) === null || _a === void 0 ? void 0 : _a.Data;
        if (!data) {
            return;
        }
        else if (!event.isChecked) {
            return GameHelper_1.gameHpr.updateOntopFlagData(this.selectFlag, -1, 0);
        }
        else if (GameHelper_1.gameHpr.getOntopFlagCount() >= 10) {
            event.isChecked = false;
            return ViewHelper_1.viewHelper.showAlert('toast.ontop_flag_count_full');
        }
        GameHelper_1.gameHpr.updateOntopFlagData(this.selectFlag, data.index, 0);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MapMarkPnlCtrl.prototype.updateFlags = function () {
        var _a;
        var markMap = GameHelper_1.gameHpr.player.getMapMarks();
        var flagMap = {};
        for (var key in markMap) {
            var mark = markMap[key];
            flagMap[mark.flag] = { index: Number(key), desc: mark.desc };
        }
        var select = (_a = markMap[this.index]) === null || _a === void 0 ? void 0 : _a.flag;
        this.flagsNode_.children.forEach(function (m) {
            var flag = Number(m.name);
            var info = m.Data = flagMap[flag];
            m.Child('root/bg').opacity = info ? 255 : 128;
            if (!info && !select) {
                select = flag;
            }
        });
        this.flagsNode_.Component(cc.ToggleContainer).Tabs(select || 1);
    };
    MapMarkPnlCtrl.prototype.updateSelectFlag = function (flag) {
        var _a, _b;
        var node = this.flagsNode_.Child(flag), data = node.Data;
        var point = data ? MapHelper_1.mapHelper.indexToPoint(data.index) : this.point, has = !!data;
        this.infoNode_.Child('name/val').setLocaleKey('ui.flag_name', flag - 1);
        this.infoNode_.Child('x', cc.Label).string = 'X: ' + point.x;
        this.infoNode_.Child('y', cc.Label).string = 'Y: ' + point.y;
        this.inputDescEb_.setActive(!has);
        if (this.textNode_.Child('text').active = has) {
            this.textNode_.Child('text/val', cc.Label).string = (data === null || data === void 0 ? void 0 : data.desc) || '-';
        }
        this.buttonsNode_.Child('add_be').active = !has;
        this.buttonsNode_.Child('cancel_be').active = has;
        // 显示置顶
        if (this.ontopTge_.getActive()) {
            this.buttonsNode_.Child('goto_be').active = has;
            this.ontopTge_.node.opacity = has ? 255 : 100;
            this.ontopTge_.interactable = has;
            if (has) {
                var index = (_b = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS)) === null || _a === void 0 ? void 0 : _a['0_' + flag]) !== null && _b !== void 0 ? _b : -1;
                this.ontopTge_.isChecked = index >= 0;
            }
            else {
                this.ontopTge_.isChecked = false;
            }
        }
        else {
            this.buttonsNode_.Child('goto_be').active = has && data.index !== this.index;
        }
    };
    MapMarkPnlCtrl.prototype.add = function (index, flag, desc) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_MapMarkPoint', { index: index, flag: flag, desc: desc }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.hide();
                        }
                        info = GameHelper_1.gameHpr.player.getMapMark(index);
                        if (info) {
                            GameHelper_1.gameHpr.updateOntopFlagData(info.flag, -1, 0);
                        }
                        GameHelper_1.gameHpr.player.addMapMark(index, flag, desc);
                        return [2 /*return*/];
                }
            });
        });
    };
    MapMarkPnlCtrl = __decorate([
        ccclass
    ], MapMarkPnlCtrl);
    return MapMarkPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MapMarkPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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