
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/ad/WxRewardAd.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'efe57rN2IZA24S++phKTFpu', 'WxRewardAd');
// app/script/common/ad/WxRewardAd.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../constant/Enums");
var ViewHelper_1 = require("../helper/ViewHelper");
var BaseRewardAd_1 = require("./BaseRewardAd");
// 微信视频广告
var WxRewardAd = /** @class */ (function (_super) {
    __extends(WxRewardAd, _super);
    function WxRewardAd() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.videoAd = null;
        _this.adOutOfUseTimeOut = 0; //加载超时
        _this.adClose = null;
        return _this;
    }
    WxRewardAd.prototype.create = function () {
        this.inited = true;
        var videoAd = this.videoAd = wx.createRewardedVideoAd({
            adUnitId: 'adunit-83a69099d3d35a8a',
            multiton: false,
        });
        videoAd.onLoad(this.onLoad.bind(this));
        videoAd.onError(this.onError.bind(this));
        videoAd.onClose(this.onClose.bind(this));
        this.state = Enums_1.AdState.LOADING;
        this.reloadTime = 0;
        this.adOutOfUseTimeOut = 0;
    };
    // 加载成功
    WxRewardAd.prototype.onLoad = function () {
        console.log('videoAd onLoad');
        this.state = Enums_1.AdState.LOAD_SUCCESS;
        this.adOutOfUseTimeOut = 0;
    };
    // 加载失败
    WxRewardAd.prototype.onError = function (err) {
        // console.log('@@video onError', err)
        this.state = Enums_1.AdState.WAIT;
        if (!err) {
        }
        else if (err.errMsg == 'no advertisement' || err.errCode == 0 || err.errCode == 1004) {
            this.reloadTime -= 10;
            this.noSuitAdCount += 1;
        }
        else {
            this.reloadTime -= 20;
            this.noSuitAdCount += 2;
        }
    };
    WxRewardAd.prototype.onClose = function (res) {
        this.adClose && this.adClose(res);
    };
    // 是否可以播放
    WxRewardAd.prototype.isReady = function () {
        if (cc.sys.isBrowser) {
            return true;
        }
        else if (!ut.isWechatGame()) {
            return false;
        }
        else if (!this.inited) {
            this.create();
        }
        return this.state === Enums_1.AdState.LOAD_SUCCESS;
    };
    WxRewardAd.prototype.update = function (dt) {
        if (!this.inited) {
            this.create();
        }
        if (!this.videoAd) {
            return;
        }
        else if (this.state === Enums_1.AdState.WAIT) {
            this.reloadTime += dt;
            if (this.reloadTime >= 15) {
                this.reloadTime = -2;
                this.state = Enums_1.AdState.LOADING;
                // console.log('retry load WAIT')
                this.videoAd.load();
            }
        }
        else if (this.state == Enums_1.AdState.LOADING) {
            this.adOutOfUseTimeOut += dt;
            if (this.adOutOfUseTimeOut >= 40) { //加载超时
                this.adOutOfUseTimeOut = 0;
                this.state = Enums_1.AdState.WAIT;
                // console.log('retry load LOADING')
                this.videoAd.load();
            }
        }
    };
    // 显示广告
    WxRewardAd.prototype.show = function () {
        return __awaiter(this, void 0, void 0, function () {
            var onClose, res, error_1;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (cc.sys.isBrowser) {
                            return [2 /*return*/, true];
                        }
                        else if (!ut.isWechatGame() || !this.videoAd) {
                            return [2 /*return*/, false];
                        }
                        this.isPause = false;
                        onClose = new Promise(function (resolve) {
                            _this.adClose = resolve;
                        });
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 5, , 6]);
                        mc.lockTouch('ad_show');
                        this.state = Enums_1.AdState.PLAYING;
                        return [4 /*yield*/, this.videoAd.load()];
                    case 2:
                        _a.sent();
                        this.pause();
                        return [4 /*yield*/, this.videoAd.show()
                            // 等待关闭
                        ];
                    case 3:
                        _a.sent();
                        return [4 /*yield*/, onClose];
                    case 4:
                        res = _a.sent();
                        this.adClose = null;
                        // 防止中途已经被修改了状态
                        if (this.state === Enums_1.AdState.PLAYING) {
                            this.state = Enums_1.AdState.WAIT;
                            this.reloadTime = 0;
                        }
                        this.resume();
                        mc.unlockTouch('ad_show');
                        if (res && res.isEnded || res === undefined) {
                            return [2 /*return*/, true]; //正常播放结束，可以下发游戏奖励
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.ad_not_finish');
                            return [2 /*return*/, false]; //播放中途退出，不下发游戏奖励
                        }
                        return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        this.state = Enums_1.AdState.WAIT;
                        this.reloadTime = 14;
                        this.adClose = null;
                        console.error(error_1);
                        this.resume();
                        ViewHelper_1.viewHelper.showAlert('toast.wheel_turn_fail_1');
                        return [2 /*return*/, false];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    return WxRewardAd;
}(BaseRewardAd_1.default));
exports.default = WxRewardAd;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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