
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/DismantleCityTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1226bBh7a1Exqot2EVzI+1Z', 'DismantleCityTipPnlCtrl');
// app/script/view/main/DismantleCityTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var DismantleCityTipPnlCtrl = /** @class */ (function (_super) {
    __extends(DismantleCityTipPnlCtrl, _super);
    function DismantleCityTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        //@end
        _this.cell = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    DismantleCityTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DismantleCityTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DismantleCityTipPnlCtrl.prototype.onEnter = function (cell) {
        var _a;
        this.cell = cell;
        this.contentNode_.Child('val').setLocaleKey('ui.dismantle_city_tip_1', cell.getName());
        var time = ((_a = assetsMgr.getJsonData('city', cell.cityId)) === null || _a === void 0 ? void 0 : _a.dt_time) || 0;
        var cd = ut.numberFixed(1 - GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CITY_BUILD_CD) * 0.01);
        this.contentNode_.Child('time/val', cc.Label).Color(cd > 0 && cd < 1 ? '#4AB32E' : '#936E5A').string = ut.secondFormat(cd ? Math.floor(ut.numberFixed(time * cd)) : time, 'h:mm:ss');
    };
    DismantleCityTipPnlCtrl.prototype.onRemove = function () {
    };
    DismantleCityTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    DismantleCityTipPnlCtrl.prototype.onClickButtons = function (event, data) {
        var _this = this;
        this.hide();
        if (event.target.name === 'ok') {
            GameHelper_1.gameHpr.world.dismantleCity(this.cell.index).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                _this.emit(EventType_1.default.CLOSE_SELECT_CELL);
            });
        }
    };
    DismantleCityTipPnlCtrl = __decorate([
        ccclass
    ], DismantleCityTipPnlCtrl);
    return DismantleCityTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DismantleCityTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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