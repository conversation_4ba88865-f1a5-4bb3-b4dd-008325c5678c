
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ShopBuyTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0b6e81NunRAArC7mPQcO1da', 'ShopBuyTipPnlCtrl');
// app/script/view/common/ShopBuyTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var ShopBuyTipPnlCtrl = /** @class */ (function (_super) {
    __extends(ShopBuyTipPnlCtrl, _super);
    function ShopBuyTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descNode_ = null; // path://root/desc_n
        _this.iconNode_ = null; // path://root/icon_n
        //@end
        _this.cb = null;
        _this.root = null;
        _this.spr = null;
        _this.json = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    ShopBuyTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ShopBuyTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.iconNode_.Child('root');
                this.spr = this.iconNode_.Child('val', cc.Sprite);
                return [2 /*return*/];
            });
        });
    };
    ShopBuyTipPnlCtrl.prototype.onEnter = function (data, cb) {
        var _this = this;
        var json = this.json = data.data;
        var cost = data.cost;
        this.root.removeAllChildren();
        this.spr.spriteFrame = null;
        this.spr.node.removeAllChildren();
        this.spr.node.scale = 1;
        this.spr.node.setContentSize(100, 100);
        var nameNode = this.iconNode_.Child('name');
        if (data.type === 'city_skin') {
            this.descNode_.Child('val').setLocaleKey(data.textKey, cost, 'ui.title_main_city_skin');
            ResHelper_1.resHelper.loadCityIcon(json.id, this.spr, this.key).then(function (m) { return _this.isValid && _this.spr.node.adaptScale(_this.iconNode_.getContentSize()); });
            nameNode.active = false;
        }
        else if (data.type === 'chat_emoji') {
            this.descNode_.Child('val').setLocaleKey(data.textKey, cost, 'ui.chat_emoji_title');
            ResHelper_1.resHelper.loadEmojiNode(json.id, this.root, 1, this.key);
            nameNode.active = false;
        }
        else if (data.type === 'headicon') {
            this.descNode_.Child('val').setLocaleKey(data.textKey, cost, 'ui.headicon_title');
            ResHelper_1.resHelper.loadPlayerHead(this.spr, json.icon, this.key, true);
            nameNode.active = false;
        }
        else if (data.type === 'hero') {
            this.descNode_.Child('val').setLocaleKey(data.textKey, cost, 'portrayalText.name_' + json.id);
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(json.id, this.spr, this.key);
            nameNode.active = true;
            nameNode.setLocaleKey('ui.debris_count', Constant_1.PORTRAYAL_COMP_NEED_COUNT);
        }
        this.cb = cb;
    };
    ShopBuyTipPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
        this.json = null;
    };
    ShopBuyTipPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    ShopBuyTipPnlCtrl.prototype.onClickButtons = function (event, data) {
        this.cb && this.cb(event.target.name === 'ok');
        this.hide();
    };
    ShopBuyTipPnlCtrl = __decorate([
        ccclass
    ], ShopBuyTipPnlCtrl);
    return ShopBuyTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ShopBuyTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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