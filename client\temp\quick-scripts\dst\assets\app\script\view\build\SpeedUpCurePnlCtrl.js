
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/SpeedUpCurePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '53e58D0qwVEOKfCnO388Sss', 'SpeedUpCurePnlCtrl');
// app/script/view/build/SpeedUpCurePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LongClickTouchCmpt_1 = require("../cmpt/LongClickTouchCmpt");
var ccclass = cc._decorator.ccclass;
var SpeedUpCurePnlCtrl = /** @class */ (function (_super) {
    __extends(SpeedUpCurePnlCtrl, _super);
    function SpeedUpCurePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.timeNode_ = null; // path://root/desc/time/time_n
        _this.tipRt_ = null; // path://root/tip_rt
        _this.subNode_ = null; // path://root/need_count/sub_n
        _this.inputEb_ = null; // path://root/need_count/input_eb_ebee
        _this.addNode_ = null; // path://root/need_count/add_n
        //@end
        _this.data = null;
        _this.player = null;
        _this.currBookCount = 1;
        _this.totalBookCount = 0;
        _this.currTime = 0; // 毫秒
        _this.totalTime = 0; // 秒
        return _this;
    }
    SpeedUpCurePnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdateCostBook, _a.enter = true, _a),
        ];
    };
    SpeedUpCurePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                this.onRegisterLongTouchEvent();
                return [2 /*return*/];
            });
        });
    };
    SpeedUpCurePnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        this.currBookCount = 1;
        this.checkCostAndTime();
        this.initTipDesc();
        this.updateCureTime();
        this.updateExpBookCount();
    };
    SpeedUpCurePnlCtrl.prototype.onRemove = function () {
    };
    SpeedUpCurePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/need_count/input_eb_ebee
    SpeedUpCurePnlCtrl.prototype.onClickInputEnded = function (event, data) {
        this.checkCostAndTime();
        var count = Math.min(Number(event.string.trim()) || 1, this.getMaxBookCount());
        this.currBookCount = count;
        this.updateCureTime();
        this.updateExpBookCount();
    };
    // path://root/need_count/max_be
    SpeedUpCurePnlCtrl.prototype.onClickMax = function (event, data) {
        this.checkCostAndTime();
        this.currBookCount = Math.max(1, this.getMaxBookCount());
        this.updateCureTime();
        this.updateExpBookCount();
    };
    // path://root/ok_be
    SpeedUpCurePnlCtrl.prototype.onClickOk = function (event, data) {
        this.reqSpeedUpCure();
    };
    // path://root/need_count/min_be
    SpeedUpCurePnlCtrl.prototype.onClickMin = function (event, data) {
        this.checkCostAndTime();
        this.currBookCount = 1;
        this.updateCureTime();
        this.updateExpBookCount();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    SpeedUpCurePnlCtrl.prototype.onUpdateCostBook = function () {
        this.checkCostAndTime();
        this.initTipDesc();
        this.currBookCount = Math.min(this.currBookCount, this.getMaxBookCount());
        this.updateCureTime();
        this.updateExpBookCount();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    SpeedUpCurePnlCtrl.prototype.onRegisterLongTouchEvent = function () {
        this.subNode_.Component(LongClickTouchCmpt_1.default).on(this.clickSubEvent, this);
        this.addNode_.Component(LongClickTouchCmpt_1.default).on(this.clickAddEvent, this);
    };
    SpeedUpCurePnlCtrl.prototype.clickAddEvent = function () {
        if (this.currBookCount < this.player.getExpBook() && this.currBookCount < this.getMaxBookCount()) {
            this.checkCostAndTime();
            this.currBookCount++;
            this.updateCureTime();
            this.updateExpBookCount();
        }
    };
    SpeedUpCurePnlCtrl.prototype.clickSubEvent = function () {
        if (this.currBookCount > 1) {
            this.checkCostAndTime();
            this.currBookCount--;
            this.updateExpBookCount();
            this.updateCureTime();
        }
    };
    SpeedUpCurePnlCtrl.prototype.checkCostAndTime = function () {
        var datas = this.player.getCuringPawnsQueue();
        if (datas.length <= 0)
            return;
        var time = 0, currTime = 0, cost = [];
        for (var i = 0; i < 6; i++) {
            var data = datas[i];
            if (!data) {
                continue;
            }
            if (i !== 0) {
                currTime += data.needTime || 0;
            }
            else if (data) {
                currTime += data.getSurplusTime();
            }
            time += data.json.drill_time;
            cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(data.json.drill_cost));
            for (var j = 1; j < data.lv; j++) {
                var cfg = assetsMgr.getJsonData('pawnAttr', data.id * 1000 + j);
                time += cfg.lv_time;
                cost.pushArr(GameHelper_1.gameHpr.stringToCTypes(cfg.lv_cost));
            }
        }
        this.currTime = currTime;
        this.totalTime = time * 1000;
        var finalCost = [];
        GameHelper_1.gameHpr.mergeTypeObjsCount.apply(GameHelper_1.gameHpr, __spread([finalCost], cost));
        this.totalBookCount = finalCost.filter(function (m) { return m.type === Enums_1.CType.EXP_BOOK; }).reduce(function (val, cur) { return val + cur.count; }, 0);
    };
    SpeedUpCurePnlCtrl.prototype.initTipDesc = function () {
        this.tipRt_.string = assetsMgr.lang('ui.speedup_cure_desc', [ut.millisecondFormat(this.unitTime())]);
    };
    // 一本经验书减少的时间
    SpeedUpCurePnlCtrl.prototype.unitTime = function () {
        return this.totalTime / (this.totalBookCount * 3);
    };
    // 当前最多可消耗多少本经验书
    SpeedUpCurePnlCtrl.prototype.getMaxBookCount = function () {
        return Math.ceil(this.currTime / this.unitTime());
    };
    // 更新时间
    SpeedUpCurePnlCtrl.prototype.updateCureTime = function () {
        var realTime = Math.max(0, (this.currTime - (this.currBookCount * this.unitTime())) * 0.001);
        this.timeNode_.Component(cc.LabelTimer).run(realTime);
    };
    SpeedUpCurePnlCtrl.prototype.updateExpBookCount = function () {
        this.inputEb_.string = this.currBookCount + '';
    };
    SpeedUpCurePnlCtrl.prototype.reqSpeedUpCure = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.player.getCuringPawnsQueue().length <= 0) {
                            this.hide();
                        }
                        if (this.currBookCount > this.player.getExpBook()) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH)];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_SpeedUpCuringPawn', { index: this.data.aIndex, costBook: this.currBookCount }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (this.isValid) {
                            if (err) {
                                return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                            }
                            else {
                                this.player.setExpBook(data.expBook);
                                this.player.updatePawnCuringQueue(data.queues);
                                ViewHelper_1.viewHelper.showAlert('toast.speed_up_cure_succ');
                                this.hide();
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    SpeedUpCurePnlCtrl = __decorate([
        ccclass
    ], SpeedUpCurePnlCtrl);
    return SpeedUpCurePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SpeedUpCurePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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