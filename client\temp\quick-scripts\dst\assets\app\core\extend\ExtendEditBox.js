
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendEditBox.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3fd703O6pZNkIFhh1uc0hOK', 'ExtendEditBox');
// app/core/extend/ExtendEditBox.ts

"use strict";
/**
 * EditBox扩展方法
 */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLocale_1 = require("../base/BaseLocale");
cc.EditBox.prototype.setPlaceholder = function (key, font) {
    var params = [];
    for (var _i = 2; _i < arguments.length; _i++) {
        params[_i - 2] = arguments[_i];
    }
    var locale = this.Component(BaseLocale_1.default);
    if (locale) {
        return locale.setKey.apply(locale, __spread([key], params));
    }
    else if (font) {
        this.placeholderLabel.font = assetsMgr.getFont(font);
    }
    this.placeholder = assetsMgr.lang.apply(assetsMgr, __spread([key], params));
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxleHRlbmRcXEV4dGVuZEVkaXRCb3gudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUNBOztHQUVHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRUgsaURBQTJDO0FBRTNDLEVBQUUsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLGNBQWMsR0FBRyxVQUFVLEdBQVcsRUFBRSxJQUFZO0lBQUUsZ0JBQWdCO1NBQWhCLFVBQWdCLEVBQWhCLHFCQUFnQixFQUFoQixJQUFnQjtRQUFoQiwrQkFBZ0I7O0lBQ3ZGLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsb0JBQVUsQ0FBQyxDQUFBO0lBQ3pDLElBQUksTUFBTSxFQUFFO1FBQ1IsT0FBTyxNQUFNLENBQUMsTUFBTSxPQUFiLE1BQU0sWUFBUSxHQUFHLEdBQUssTUFBTSxHQUFDO0tBQ3ZDO1NBQU0sSUFBSSxJQUFJLEVBQUU7UUFDYixJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUE7S0FDdkQ7SUFDRCxJQUFJLENBQUMsV0FBVyxHQUFHLFNBQVMsQ0FBQyxJQUFJLE9BQWQsU0FBUyxZQUFNLEdBQUcsR0FBSyxNQUFNLEVBQUMsQ0FBQTtBQUNyRCxDQUFDLENBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJcclxuLyoqXHJcbiAqIEVkaXRCb3jmianlsZXmlrnms5VcclxuICovXHJcblxyXG5pbXBvcnQgQmFzZUxvY2FsZSBmcm9tIFwiLi4vYmFzZS9CYXNlTG9jYWxlXCJcclxuXHJcbmNjLkVkaXRCb3gucHJvdG90eXBlLnNldFBsYWNlaG9sZGVyID0gZnVuY3Rpb24gKGtleTogc3RyaW5nLCBmb250OiBzdHJpbmcsIC4uLnBhcmFtczogYW55W10pIHtcclxuICAgIGNvbnN0IGxvY2FsZSA9IHRoaXMuQ29tcG9uZW50KEJhc2VMb2NhbGUpXHJcbiAgICBpZiAobG9jYWxlKSB7XHJcbiAgICAgICAgcmV0dXJuIGxvY2FsZS5zZXRLZXkoa2V5LCAuLi5wYXJhbXMpXHJcbiAgICB9IGVsc2UgaWYgKGZvbnQpIHtcclxuICAgICAgICB0aGlzLnBsYWNlaG9sZGVyTGFiZWwuZm9udCA9IGFzc2V0c01nci5nZXRGb250KGZvbnQpXHJcbiAgICB9XHJcbiAgICB0aGlzLnBsYWNlaG9sZGVyID0gYXNzZXRzTWdyLmxhbmcoa2V5LCAuLi5wYXJhbXMpXHJcbn1cclxuIl19