
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/TaskObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '31750UQ095KMpaOdRxv0UOk', 'TaskObj');
// app/script/model/common/TaskObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GotoHelper_1 = require("../../common/helper/GotoHelper");
var TaskCondObj_1 = require("./TaskCondObj");
// 任务信息
var TaskObj = /** @class */ (function () {
    function TaskObj() {
        this.id = 0;
        this.json = null;
        this.rewards = []; //奖励列表
        this.cond = null; //任务条件
        this.state = Enums_1.TaskState.UNDONE;
        this.treasureRewards = [];
        this.serverRunDay = 0; //服务器运行天数
        this.descParams = [];
    }
    TaskObj.prototype.init = function (taskInfo, jsonName) {
        this.id = taskInfo.id;
        var json = this.json = assetsMgr.getJsonData(jsonName, this.id);
        if (!json) {
            return null;
        }
        this.descParams = String(json.params).split('|');
        this.serverRunDay = taskInfo.serverRunDay || GameHelper_1.gameHpr.getServerRunDay();
        if (json.recreate_reward && GameHelper_1.gameHpr.player.getReCreateMainCityCount() > 0) {
            this.rewards = GameHelper_1.gameHpr.stringToCTypes(json.recreate_reward);
        }
        else if (json.reward) {
            this.rewards = GameHelper_1.gameHpr.stringToCTypes(json.reward);
        }
        else {
            this.rewards = GameHelper_1.gameHpr.stringToCTypes(json['reward_' + cc.misc.clampf(this.serverRunDay, 1, 4)]);
        }
        this.cond = GameHelper_1.gameHpr.stringToTaskConds(json.cond)[0];
        this.state = Enums_1.TaskState.UNDONE;
        this.cond.progress = taskInfo.progress || 0;
        this.treasureRewards = (taskInfo.treasureRewards || []).map(function (m) { return GameHelper_1.gameHpr.fromSvrTreasureInfo(m); });
        if (this.treasureRewards.length === 0) {
            var it = this.rewards.find(function (m) { return m.type === Enums_1.CType.TREASURE; });
            if (it) {
                for (var i = 0; i < it.count; i++) {
                    this.treasureRewards.push(GameHelper_1.gameHpr.fromSvrTreasureInfo({ id: it.id, rewards: [] }));
                }
            }
        }
        if (this.cond.type === Enums_1.TCType.WEAR_EQUIP_DEF) { //动态初始化
            this.checkUpdateComplete();
        }
        return this;
    };
    // 特殊处理
    TaskObj.prototype.init2 = function (id) {
        this.id = id;
        this.state = Enums_1.TaskState.UNDONE;
        this.cond = new TaskCondObj_1.default().fromString(id + "," + id + ",0");
        return this;
    };
    Object.defineProperty(TaskObj.prototype, "desc", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.desc) || ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TaskObj.prototype, "tip", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.tip) || ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TaskObj.prototype, "help", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.help) || ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TaskObj.prototype, "needSelectReward", {
        get: function () { var _a; return !!((_a = this.json) === null || _a === void 0 ? void 0 : _a.select_reward); },
        enumerable: false,
        configurable: true
    });
    // 是否可领奖
    TaskObj.prototype.isCanClaim = function () { return this.state === Enums_1.TaskState.CANGET; };
    // 是否完成
    TaskObj.prototype.isComplete = function () { return this.state >= Enums_1.TaskState.CANGET; };
    // 是否已领取
    TaskObj.prototype.isClaimed = function () { return this.state === Enums_1.TaskState.FINISH; };
    // 是否可以分享
    TaskObj.prototype.isCanShare = function () {
        var _a;
        if (this.state !== Enums_1.TaskState.UNDONE || ((_a = this.cond) === null || _a === void 0 ? void 0 : _a.type) !== Enums_1.TCType.INVITE_FRIEND) {
            return false;
        }
        return !this.cond.isComplete();
    };
    // 是否可以查看详情
    TaskObj.prototype.isCanLookDetails = function () {
        if (this.state !== Enums_1.TaskState.UNDONE || !this.cond || this.cond.type !== Enums_1.TCType.SIGN_DAY_COUNT) {
            return false;
        }
        return !this.cond.isComplete();
    };
    // 是否可以前往
    TaskObj.prototype.isCanGoto = function () {
        if (this.state !== Enums_1.TaskState.UNDONE || !this.cond || this.cond.type === Enums_1.TCType.INVITE_FRIEND || this.cond.type === Enums_1.TCType.SIGN_DAY_COUNT) {
            return false;
        }
        else if (this.cond.isComplete()) {
            return false;
        }
        return GotoHelper_1.gotoHelper.isCanGotoByTaskCond(this.cond.type);
    };
    // 是否进行中
    TaskObj.prototype.isDoing = function () {
        var _a, _b;
        if (!this.cond || this.isComplete()) {
            return false;
        }
        var player = GameHelper_1.gameHpr.player;
        var type = this.cond.type, id = this.cond.id, count = this.cond.count;
        if (type === Enums_1.TCType.BUILD_LV) { //建筑等级
            return player.getBtQueues().some(function (m) { return m.id === id; });
        }
        else if (type === Enums_1.TCType.RECRUIT_PAWN_COUNT) { //招募任意士兵
            return player.getAllPawnDrillList().length > 0;
        }
        else if (type === Enums_1.TCType.RECRUIT_PAWN_APPOINT) { //招募指定士兵
            return player.getAllPawnDrillList().some(function (m) { return m.id === id; });
        }
        else if (type === Enums_1.TCType.UPLV_PAWN_APPOINT) { //升级指定士兵到指定等级
            return player.getPawnLevelingQueues().some(function (m) { return m.id === id; });
        }
        else if (type === Enums_1.TCType.FORGE_EQUIP_APPOINT) { //打造指定装备
            return ((_a = player.getCurrForgeEquip()) === null || _a === void 0 ? void 0 : _a.id) === id;
            // } else if (type === TCType.STUDY_TYPE_APPOINT) { //研究指定类型
            //     return player.getCeriSlots().some(m => m.type === id && m.isRuning())
        }
        else if (type === Enums_1.TCType.BT_MAP_RES_BUILD) { //任意修建一个农场或伐木场或采石场
        }
        else if (type === Enums_1.TCType.BT_MAP_BUILD) { //修建指定地图建筑
            // gameHpr.world.getBTCityQueueMap()
        }
        else if (type === Enums_1.TCType.UPLV_PAWN) { //升级任意士兵到指定等级
            return player.getPawnLevelingQueues().some(function (m) { return m.lv === count; });
        }
        else if (type === Enums_1.TCType.RECRUIT_PAWN_TYPE) { //招募指定类型士兵
            return player.getAllPawnDrillList().some(function (m) { var _a; return ((_a = m.json) === null || _a === void 0 ? void 0 : _a.type) === id; });
        }
        else if (type === Enums_1.TCType.RECRUIT_RANGED_PAWN) { //招募远程士兵
            return player.getAllPawnDrillList().some(function (m) {
                var _a;
                var range = ((_a = assetsMgr.getJsonData('pawnAttr', m.id * 1000 + 1)) === null || _a === void 0 ? void 0 : _a.attack_range) || 0;
                return range > 1;
            });
        }
        else if (type === Enums_1.TCType.FORGE_EXC_EQUIP) { //打造x件专属装备
            var id_1 = (_b = player.getCurrForgeEquip()) === null || _b === void 0 ? void 0 : _b.id;
            var json = id_1 ? assetsMgr.getJsonData('equipBase', id_1) : null;
            return !!(json === null || json === void 0 ? void 0 : json.exclusive_pawn);
        }
        else if (type === Enums_1.TCType.FORGE_EQUIP) { //打造x件装备
            return !!player.getCurrForgeEquip();
        }
        else if (type === Enums_1.TCType.SMELT_EQUIP) { //熔炼x件装备
            return !!player.getCurrSmeltEquip();
        }
        else if (type === Enums_1.TCType.LAND_LV_COUNT || type === Enums_1.TCType.LAND_TYPE_COUNT || type === Enums_1.TCType.CELL_COUNT || type === Enums_1.TCType.TODAY_CELL_COUNT) { //正在占领地块
            var curCount_1 = 0;
            var indexMap_1 = {};
            var checkIndexFunc_1 = function (index) {
                if (indexMap_1[index] || index === GameHelper_1.gameHpr.player.getMainCityIndex()) {
                    return false;
                }
                indexMap_1[index] = true;
                var cell = GameHelper_1.gameHpr.world.getMapCells()[index];
                if (type === Enums_1.TCType.LAND_LV_COUNT && cell.landLv === id) {
                    return true;
                }
                if (type === Enums_1.TCType.LAND_TYPE_COUNT && cell.landType === id && cell.landLv > 1) {
                    return true;
                }
                if (type === Enums_1.TCType.CELL_COUNT || type === Enums_1.TCType.TODAY_CELL_COUNT) {
                    return true;
                }
                return false;
            };
            var cells = GameHelper_1.gameHpr.getPlayerOweCells(GameHelper_1.gameHpr.getUid(), true);
            cells.forEach(function (cell) {
                if (checkIndexFunc_1(cell.index)) {
                    curCount_1++;
                }
            });
            var armyMap = player.getArmyDistMap();
            for (var key in armyMap) {
                var armys = armyMap[key];
                for (var i = 0; i < armys.length; i++) {
                    if (armys[i].state === Enums_1.ArmyState.FIGHT) {
                        var index = armys[i].index;
                        if (checkIndexFunc_1(index)) {
                            curCount_1++;
                        }
                    }
                }
            }
            var marchs = GameHelper_1.gameHpr.world.getMarchs();
            for (var i = 0; i < marchs.length; i++) {
                var march = marchs[i];
                if (march.owner === GameHelper_1.gameHpr.getUid()) {
                    var index = march.targetIndex;
                    if (checkIndexFunc_1(index)) {
                        curCount_1++;
                    }
                }
            }
            if (curCount_1 >= count) {
                return true;
            }
        }
        return false;
    };
    // 检测状态
    TaskObj.prototype.checkUpdateComplete = function () {
        if (this.isComplete() && this.cond.type !== Enums_1.TCType.ARMY_COUNT && this.cond.type !== Enums_1.TCType.ARMY_PAWN_COUNT) {
            return this.state;
        }
        else if (this.checkTaskCondition(this.cond)) {
            this.state = Enums_1.TaskState.CANGET;
        }
        else {
            this.state = Enums_1.TaskState.UNDONE;
        }
        return this.state;
    };
    TaskObj.prototype.checkTaskCondition = function (cond) {
        var _a, _b;
        if (!cond) {
            return false;
        }
        var user = GameHelper_1.gameHpr.user, player = GameHelper_1.gameHpr.player, world = GameHelper_1.gameHpr.world;
        if (cond.type === Enums_1.TCType.BUILD_LV) {
            var build = player.getMainBuilds().filter(function (m) { return m.id == cond.id; }).sort(function (a, b) { return b.lv - a.lv; })[0];
            cond.progress = (build === null || build === void 0 ? void 0 : build.lv) || 0;
        }
        else if (cond.type === Enums_1.TCType.CELL_COUNT) { //拥有领地数
            cond.progress = GameHelper_1.gameHpr.getPlayerOweCellCount(GameHelper_1.gameHpr.getUid(), cond.id === 1);
        }
        else if (cond.type === Enums_1.TCType.LAND_LV_COUNT) { //拥有x级地块数量
            cond.progress = GameHelper_1.gameHpr.getPlayerLandCountByLv(cond.id);
        }
        else if (cond.type === Enums_1.TCType.SIGN_DAY_COUNT) { //签到天数
            cond.progress = user.getSignDays();
        }
        else if (cond.type === Enums_1.TCType.INVITE_FRIEND) { //邀请好友
            cond.progress = user.getInviteFriendNotUseCount();
        }
        else if (cond.type === Enums_1.TCType.TODAY_TURNTABLE_COUNT) { //每日装盘次数
            cond.progress = user.getWheelCurrCount();
        }
        else if (cond.type === Enums_1.TCType.LAND_TYPE_COUNT) { //拥有x类型地块属性
            cond.progress = GameHelper_1.gameHpr.getPlayerLandCountByType(cond.id);
        }
        else if (cond.type === Enums_1.TCType.WIN_COUNT) { //获得游戏胜利次数
            cond.progress = user.getTotalGameCount()[0] || 0;
        }
        else if (cond.type === Enums_1.TCType.TODAY_CELL_COUNT) { //每日打地数量
            cond.progress = player.getTodayOccupyCellCount();
        }
        else if (cond.type === Enums_1.TCType.GIVE_RES_COUNT) { //赠送资源数量
            cond.progress = world.isGameOver() ? 0 : player.getAccTotalGiveResCount();
        }
        else if (cond.type === Enums_1.TCType.WHEEL_MUL) { //在大转盘转到x倍
            cond.progress = user.getMaxWheelMul() >= cond.id ? 1 : 0;
        }
        else if (cond.type === Enums_1.TCType.ARMY_COUNT) { //拥有多少军队
            cond.progress = player.getBaseArmys().length;
        }
        else if (cond.type === Enums_1.TCType.FORGE_EQUIP_APPOINT) { //打造指定装备
            cond.progress = !!player.getEquipById(cond.id) ? 1 : 0;
        }
        else if (cond.type === Enums_1.TCType.STUDY_TYPE_APPOINT) { //研究指定类型
            if (cond.id === Enums_1.StudyType.POLICY) {
                cond.progress = player.getStudyPolicySlots().length;
            }
            else if (cond.id === Enums_1.StudyType.PAWN) {
                cond.progress = player.getStudyPawnSlots().length;
            }
            else if (cond.id === Enums_1.StudyType.EQUIP) {
                cond.progress = player.getStudyEquipSlots().length;
            }
            else if (cond.id === Enums_1.StudyType.EXCLUSIVE) {
                cond.progress = player.getStudyEquipSlots().filter(function (m) { var _a; return !!((_a = m.equip) === null || _a === void 0 ? void 0 : _a.isExclusive()); }).length;
            }
        }
        else if (cond.type === Enums_1.TCType.BT_MAP_RES_BUILD) { //任意修建一个农场或伐木场或采石场
            var cells = (_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cells, count_1 = 0;
            cells === null || cells === void 0 ? void 0 : cells.forEach(function (cell) {
                if (cell.cityId > 0 && cell.isHasRes()) {
                    count_1 += 1;
                }
            });
            cond.progress = count_1;
        }
        else if (cond.type === Enums_1.TCType.BT_MAP_BUILD) { //修建指定地图建筑
            var cells = (_b = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _b === void 0 ? void 0 : _b.cells, count_2 = 0;
            cells === null || cells === void 0 ? void 0 : cells.forEach(function (cell) {
                if (cell.cityId === cond.id) {
                    count_2 += 1;
                }
            });
            cond.progress = count_2;
        }
        else if (cond.type == Enums_1.TCType.WORSHIP_HERO) { //供奉x个英雄
            cond.progress = player.getHeroSlots().filter(function (m) { var _a; return !!((_a = m.hero) === null || _a === void 0 ? void 0 : _a.id); }).length;
        }
        else if (cond.type == Enums_1.TCType.FORGE_EXC_EQUIP) { //打造x个专属装备
            cond.progress = player.getEquips().filter(function (m) { return m.isExclusive(); }).length;
        }
        else if (cond.type == Enums_1.TCType.FORGE_EQUIP) { //打造x个装备
            cond.progress = player.getEquips().length;
        }
        else if (cond.type === Enums_1.TCType.LIVER_EMPEROR) { //肝帝
            // cond.progress = !gameHpr.world.isGameOver() && user.getSumOnlineTime() >= cond.id * ut.Time.Hour ? gameHpr.getPlayerOweCellCount() : 0
        }
        else if (cond.type === Enums_1.TCType.THOUSAND_MU) { //一万亩
            // cond.progress = gameHpr.world.isGameOver() ? 0 : gameHpr.getPlayerOweCellCount()
        }
        else if (cond.type === Enums_1.TCType.EQUIP_ALL_ATTR_MAX) { //装备所有属性都是最高属性
            // cond.progress = gameHpr.world.isGameOver() ? 0 : user.getEquips().some(m => m.isMaxAttr()) ? 1 : 0
        }
        else if (cond.type === Enums_1.TCType.EQUIP_RECAST_COUNT) { //装备重铸次数
            // cond.progress = gameHpr.world.isGameOver() ? 0 : user.getEquips().reduce((val, cur) => val + cur.recastCount, 0)
        }
        else if (cond.type === Enums_1.TCType.CUSTOM) {
            // 
        }
        else if (cond.type === Enums_1.TCType.PLAY_GAME_COUNT) { //完成对局次数
            cond.progress = user.getAccTotalGameCount();
        }
        else if (cond.type === Enums_1.TCType.ALL_CAPTURE_COUNT) { //当前对局已沦陷人数
            cond.progress = world.getCaptureNum();
        }
        else if (cond.type === Enums_1.TCType.WEAR_EQUIP_ALL) { //所有士兵穿戴装备
            if (GameHelper_1.gameHpr.isNoviceMode) {
                var _c = GameHelper_1.gameHpr.noviceServer.HD_GetPlayerArmys(0, -1), err = _c.err, data = _c.data;
                var allArmys = data.list;
                var pawnCount = 0;
                var wearCount = 0;
                for (var i = 0; i < allArmys.length; i++) {
                    pawnCount += allArmys[i].pawns.length;
                    wearCount += allArmys[i].pawns.length;
                    for (var j = 0; j < allArmys[i].pawns.length; j++) {
                        var pawn = allArmys[i].pawns[j];
                        if (!pawn.equip || !pawn.equip.uid) {
                            wearCount--;
                        }
                    }
                }
                cond.progress = wearCount;
                cond.count = pawnCount || 1;
            }
        }
        else if (cond.type === Enums_1.TCType.WEAR_EQUIP_DEF) { //给兵营里士兵选择默认装备
            var configPawnMap = player.getConfigPawnMap();
            for (var key in configPawnMap) {
                if (!!configPawnMap[key].equipUid) {
                    cond.progress = 1;
                    break;
                }
            }
        }
        else if (cond.type === Enums_1.TCType.WEAR_EQUIP) { //给士兵穿戴装备
            if (GameHelper_1.gameHpr.isNoviceMode) {
                var _d = GameHelper_1.gameHpr.noviceServer.HD_GetPlayerArmys(0, -1), err = _d.err, data = _d.data;
                var armys = data.list;
                for (var i = 0; i < armys.length; i++) {
                    var army = armys[i];
                    for (var j = 0; j < army.pawns.length; j++) {
                        var pawn = army.pawns[j];
                        if (GotoHelper_1.gotoHelper.isWearEquip(pawn, cond.id)) {
                            cond.progress = 1;
                            break;
                        }
                    }
                    if (cond.progress > 0)
                        break;
                }
            }
        }
        return cond.isComplete();
    };
    // 获取进度
    TaskObj.prototype.getProgress = function () {
        var _a;
        if (!this.cond || !((_a = this.json) === null || _a === void 0 ? void 0 : _a.show_progress)) {
            return [0, 0];
        }
        return [this.cond.progress, this.cond.count];
    };
    TaskObj.prototype.getProgressText = function () {
        var _a = __read(this.getProgress(), 2), a = _a[0], b = _a[1];
        return b > 0 ? "(" + a + "/" + b + ")" : '';
    };
    TaskObj.prototype.getSortVal = function () {
        var _a;
        var w = 0;
        if (this.isCanClaim()) {
            return w;
        }
        else if (this.cond) {
            w = ((_a = this.json) === null || _a === void 0 ? void 0 : _a.sort) || 100;
        }
        return w * 100 + (100 - (this.cond.progress / (this.cond.count || 1)) * 100);
    };
    TaskObj.prototype.getSortValByShow = function () {
        var _a;
        var w = 1;
        if (this.isCanClaim()) {
            return w;
        }
        else if (this.isDoing()) {
            w = w * 10 + 1;
        }
        else {
            w = w * 10 + 0;
        }
        w = w * 100 + (((_a = this.json) === null || _a === void 0 ? void 0 : _a.sort) || 0);
        return w;
    };
    return TaskObj;
}());
exports.default = TaskObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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