
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SeasonLandItemAnim.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f3beavYKeNJF63FIIcWO0Ep', 'SeasonLandItemAnim');
// app/script/view/main/SeasonLandItemAnim.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var _a = cc._decorator, property = _a.property, ccclass = _a.ccclass;
// 换季 地块资源动画
var SeasonLandItemAnim = /** @class */ (function (_super) {
    __extends(SeasonLandItemAnim, _super);
    function SeasonLandItemAnim() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.spr = null;
        _this.anim = null;
        _this.valSpr = null;
        _this.icon = '';
        _this.seasonType = 0;
        return _this;
    }
    SeasonLandItemAnim.prototype.onLoad = function () {
        this.spr = this.getComponent(cc.Sprite);
        this.anim = this.getComponent(cc.Animation);
        this.valSpr = this.FindChild('val', cc.Sprite);
    };
    SeasonLandItemAnim.prototype.setLand = function (icon, landLv, seasonType, changeAnimInfo) {
        if (!this.spr) {
            this.onLoad();
        }
        this.anim.stop();
        this.icon = icon;
        this.seasonType = seasonType;
        if (changeAnimInfo && changeAnimInfo.startTime < 1.83) {
            if (landLv <= 1) {
                this.spr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(icon, seasonType);
                this.valSpr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(icon, changeAnimInfo.type);
                this.valSpr.node.opacity = 255;
            }
            else {
                this.spr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(icon, changeAnimInfo.type);
                this.valSpr.spriteFrame = null;
            }
            // this.play(changeAnimInfo)
            var animName = 'land_item_change_' + landLv, startTime = changeAnimInfo.startTime;
            // cc.log(animName, startTime)
            this.anim.play(animName, startTime);
            var delay = landLv <= 1 ? 1.83 - startTime : 0.9 - startTime;
            if (delay <= 0) {
                this.spr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(icon, seasonType);
                this.valSpr.spriteFrame = null;
            }
        }
        else {
            this.spr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(icon, seasonType);
            this.valSpr.spriteFrame = null;
        }
    };
    SeasonLandItemAnim.prototype.onChangeIcon = function () {
        this.spr.spriteFrame = ResHelper_1.resHelper.getLandItemIcon(this.icon, this.seasonType);
        this.valSpr.spriteFrame = null;
    };
    SeasonLandItemAnim = __decorate([
        ccclass
    ], SeasonLandItemAnim);
    return SeasonLandItemAnim;
}(cc.Component));
exports.default = SeasonLandItemAnim;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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