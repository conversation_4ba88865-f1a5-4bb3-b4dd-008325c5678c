require('ts-node').register();
const fs = require('fs');
const path = require('path');
const mri = require('mri');
const Stroke = require("./libs/stroke").default;
const Shadow = require("./libs/shadow").default;
const Fill = require("./libs/fill").default;
const Font = require("./libs/Font").default;
const Style = require("./Style").default;
const Layout = require("./Layout").default;
const Pack = require("./Pack").default;

const { createCanvas, loadImage } = require('canvas');
const { FillType } = require("./libs/fill");
const { GradientType } = require("./libs/gradient");

const Metric = require("./libs/metric").default;
const Ui = require("./libs/ui").default;
const PatternTexture = require("./libs/patternTexture").default;
const Gradient = require("./libs/gradient").default;

const { toBmfInfo } = require("./export");
const { getString } = require("./export/types/text").default;

const getFont = () => {
    let font = new Font()
    font.size = fontSize
    font.lineHeight = lineHeight
    font.sharp = fontSharp
    font.updateBaselines()
    return font
}
const getGradient = () => {
    let gradient = new Gradient()
    gradient.type = GradientType.LINEAR
    gradient.angle = 0
    gradient.addColor(0, 'rgba(255,255,255,1)')
    gradient.addColor(1)
    return gradient
}
const getPatternTexture = () => {
    let patternTexture = new PatternTexture()
    patternTexture.scale = 1
    patternTexture.repetition = 'repeat'
    return patternTexture
}


const getFill = () => {
    let fill = new Fill()
    fill.color = fontColor
    fill.type = FillType.SOLID
    fill.gradient = getGradient()
    fill.patternTexture = getPatternTexture()
    return fill
}

const getStroke = () => {
    let stroke = new Stroke()
    stroke.width = strokeWidth
    stroke.lineCap = 'round'
    stroke.lineJoin = 'round'
    stroke.color = strokeColor
    stroke.type = FillType.SOLID
    stroke.gradient = getGradient()
    stroke.patternTexture = getPatternTexture()
    return stroke
}
const getShadow = () => {
    let shadow = new Shadow()
    shadow.color = shadowColor
    shadow.blur = shadowBlur
    shadow.offsetX = shadowOffsetX
    shadow.offsetY = shadowOffsetY
    return shadow
}

const getLayout = () => {
    let layout = new Layout()
    layout.padding = layoutPadding
    layout.spacing = layoutSpacing
    // layout.width =1024
    // layout.height =1024
    layout.auto = true
    layout.fixedSize = false
    return layout
}

const getUi = () => {
    let ui = new Ui()
    ui.previewText = "previewText"
    return ui
}

const getMetric = (x, y) => {
    let metric = new Metric()
    metric.xAdvance = 0
    metric.xOffset = x
    metric.yOffset = y
    return metric
}

const print = () => {
    console.log(`[ Font ]`)
    console.log(`Font Family: ${fontFamily}`)
    console.log(`Font Path: ${fontPath}`)
    console.log(`Font Size: ${fontSize} px`)
    console.log(`Font Sharp: ${fontSharp}%`)

    console.log(`[ Layout ]`)
    console.log(`Padding: ${layoutPadding} px`)
    console.log(`Spacing: ${layoutSpacing} px`)

    console.log(`[ Fill ]`)
    console.log(`Type: Solid`) // 默认
    console.log(`Color: ${fontColor}`)

    console.log(`Stroke: [${useStroke}]`)
    console.log(`Width: ${strokeWidth} px`)
    console.log(`Color: ${strokeColor}`)

    // console.log(`Shadow: [${useShadow}]`)
    // console.log(`Offset X: ${shadowOffsetX} px`)
    // console.log(`Offset Y: ${shadowOffsetY} px`)
    // console.log(`Blur: ${shadowBlur} px`)
    // console.log(`Color: ${shadowColor} px`)
    console.log(`Offset X: ${xOffset}`)
    console.log(`Offset Y: ${yOffset}`)

    // console.log(`Background Color: ${bgColor}`)

    console.log(`Input Path: ${inputPath}`)
    console.log(`Out Path: ${outPath}`)
    console.log(`===============params end===================`)
}

const traverse = async (url) => {
    let x = path.extname(url)
    if (x !== ".txt") {
        return
    }
    console.log(`==>  处理输入文件: ${url}`)
    // 先读取.txt文件内容
    let data = fs.readFileSync(url).toString("utf-8")
    // data = data.replace(/ /g, '')
    if (data === "") {
        return void console.log("跳过空文件")
    } else if (!outPath) {
        outPath = path.dirname(url)
    }
    // 触发
    let name = path.basename(url).replace(x, "")
    await logic(data, name)
}

let fontData = null
const logic = async (txt, outName) => {
    let style = new Style()
    style.font = getFont()
    // 读取font
    if (fontPath) {
        fontData = fs.readFileSync(fontPath)
    }
    if (fontData) {
        await style.font.addFont(fontData.buffer)
    }

    style.fill = getFill()
    style.stroke = getStroke()
    style.shadow = getShadow()

    style.useShadow = useShadow
    style.useStroke = useStroke

    let layout = getLayout()
    let ui = getUi()
    let metric = getMetric(xOffset, yOffset)

    let packer = new Pack(outName, txt, style, layout, ui, metric)

    let canvas = createCanvas(ui.width, ui.height)
    let ctx = canvas.getContext("2d")

    if (style.bgColor) {
        ctx.fillStyle = style.bgColor
        ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    packer.glyphList.forEach((glyph) => {
        if (glyph.source && glyph.source.width !== 0 && glyph.source.height !== 0) {
            ctx.drawImage(glyph.source, glyph.x + layout.padding, glyph.y + layout.padding)
        }
    })

    // const url = canvas.toDataURL('image/png')
    const info = toBmfInfo(packer, style.font.mainFamily)
    console.log(`==>  输出文件: ${outPath}/${outName}.png, ${outPath}/${outName}.fnt`)
    const str = getString(info)
    fs.writeFileSync(`${outPath}/${outName}.png`, canvas.toBuffer());
    fs.writeFileSync(`${outPath}/${outName}.fnt`, str);
}

let fontSize // 字体大小
    , fontSharp // 字体锐利
    , lineHeight
    , fontFamily // 字体名称
    , fontPath // 字体路径 如果要使用自定义字体，需要传入fontPath来注册字体
    , layoutPadding // 图片边缘间隔
    , layoutSpacing // 图片边缘间隔
    , fontColor // 字体颜色
    , bgColor // 背景色 格式是 "rgba(0,0,0,0)"
    , useStroke // 使用强字体
    , useShadow // 使用阴影字体
    , strokeWidth
    , strokeColor
    , shadowOffsetX
    , shadowOffsetY
    , shadowBlur
    , shadowColor
    , inputPath // 路径or文件
    , outPath //输出路径
    , xOffset
    , yOffset

    ; (async function () {
        // 参数示例 --inputPath resource --fontPath resource/SourceHanSansCN-Medium.otf --useStroke --fontColor "rgba(255,255,255,100)" --strokeWidth 3
        global.atob = (base64) => {
            return Buffer.from(base64, "base64").toString()
        }
        // 处理参数
        const args = mri(process.argv)
        fontSize = args.fontSize || 72
        fontSharp = args.fontSharp || 80
        lineHeight = args.lineHeight || 1
        fontFamily = args.fontFamily || "sans-serif"
        fontPath = args.fontPath || ""
        layoutPadding = args.layoutPadding || 1
        layoutSpacing = args.layoutSpacing || 1
        fontColor = "#FFFFFF"
        xOffset = Number(String(args.xOffset).replace('_', '-')) || 0
        yOffset = Number(String(args.yOffset).replace('_', '-')) || 0

        bgColor = args.bgColor || "rgba(0,0,0,0)"

        useStroke = args.useStroke || false
        useShadow = args.useShadow || false
        strokeWidth = args.strokeWidth || 1
        strokeColor = args.strokeColor || "#000000"

        shadowOffsetX = args.shadowOffsetX || 1
        shadowOffsetY = args.shadowOffsetY || 1
        shadowBlur = args.shadowBlur || 1
        shadowColor = args.shadowColor || "#000000"

        inputPath = args.inputPath
        if (!inputPath) {
            console.log("请键入 --inputPath")
            process.exit(-1)
        }
        outPath = args.outPath

        print()
        await traverse(inputPath)

        console.log("end...")
    })()
