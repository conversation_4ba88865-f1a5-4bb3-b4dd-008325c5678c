
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/BaseRoleObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3cf0bp1jIpB7ZLhkRbm6Vq6', 'BaseRoleObj');
// app/script/model/snailisle/BaseRoleObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ISceneMapObj_1 = require("./ISceneMapObj");
// npc
var BaseRoleObj = /** @class */ (function (_super) {
    __extends(BaseRoleObj, _super);
    function BaseRoleObj(type) {
        var _this = _super.call(this) || this;
        _this.map = null; //所在地图id
        _this.position = cc.v2(); //当前实际像素位置
        _this.initPosition = cc.v2(); //
        _this.active = false;
        _this.state = 0;
        _this.type = type;
        return _this;
    }
    BaseRoleObj.prototype.init = function () {
        this.uid = ut.UID();
        return this;
    };
    // 初始化地图信息
    BaseRoleObj.prototype.initMapInfo = function (map) {
        this.map = map;
        this.active = map.active;
        return this;
    };
    Object.defineProperty(BaseRoleObj.prototype, "isValid", {
        get: function () { return !!this.map; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseRoleObj.prototype, "id", {
        get: function () { return this.type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseRoleObj.prototype, "bodyUrl", {
        get: function () { return Enums_1.MapRoleType[this.type]; },
        enumerable: false,
        configurable: true
    });
    BaseRoleObj.prototype.getSceneType = function () { return this.map && this.map.sceneType; };
    BaseRoleObj.prototype.getMapUid = function () { return this.map && this.map.uid; };
    // 设置位置
    BaseRoleObj.prototype.setPointAndPosition = function (point, position) {
        point = point || this.map.getActPointByPixel(position);
        position = position || this.map.getActPixelByPoint(point);
        this.point.set(point);
        this.position.set(position);
        this.map.setRoleZindex(this);
        if (this.initPosition.equals(cc.Vec2.ZERO)) {
            this.initPosition.set(position);
        }
    };
    // 激活
    BaseRoleObj.prototype.setActive = function (val) {
        this.active = val;
        if (val && this.map) { //刷新一下zindex
            this.map.setRoleZindex(this);
        }
    };
    // 清理
    BaseRoleObj.prototype.clean = function () {
        this.map.removeRole(this);
        this.map = null;
    };
    BaseRoleObj.prototype.run = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
    };
    BaseRoleObj.prototype.update = function (dt) {
    };
    return BaseRoleObj;
}(ISceneMapObj_1.default));
exports.default = BaseRoleObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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