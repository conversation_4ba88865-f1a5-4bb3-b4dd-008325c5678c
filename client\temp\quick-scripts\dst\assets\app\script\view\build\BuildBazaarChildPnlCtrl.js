
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildBazaarChildPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2f033INJRdH8rfW+mFeOEeE', 'BuildBazaarChildPnlCtrl');
// app/script/view/build/BuildBazaarChildPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BuildBazaarChildPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildBazaarChildPnlCtrl, _super);
    function BuildBazaarChildPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.pagesNode_ = null; // path://root/pages_n
        _this.onlyAliiLookTge_ = null; // path://root/pages_n/1/layout/only_alii_look_t
        _this.alliMemberSelectNode_ = null; // path://root/pages_n/2/target/layout/alli_member_select_be_n
        _this.okBtn_ = null; // path://root/ok_b_be
        //@end
        _this.model = null;
        _this.data = null;
        _this.curType = 0; // 1:卖出；2：赠送
        _this.giveName = '';
        return _this;
    }
    BuildBazaarChildPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuildBazaarChildPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('bazaar');
                return [2 /*return*/];
            });
        });
    };
    BuildBazaarChildPnlCtrl.prototype.onEnter = function (type, data, giveName) {
        this.curType = type;
        this.data = data;
        if (this.onlyAliiLookTge_.setActive(!!GameHelper_1.gameHpr.alliance.getUid() && GameHelper_1.gameHpr.isFreeServer())) {
            this.onlyAliiLookTge_.isChecked = false;
        }
        // 联盟市场 必须要加入联盟才可以交易
        if (this.data.id === Constant_1.BUILD_ALLI_BAZAAR_NID && !GameHelper_1.gameHpr.player.isHasAlliance()) {
            giveName = '';
        }
        if (giveName) {
            this.giveName = giveName;
        }
        //
        function initBoxList(types, node) {
            node.Items(types, function (it, type) {
                it.Data = type;
                it.Child('icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
                it.Child('val').setLocaleKey(Constant_1.CTYPE_NAME[type]);
            });
        }
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 1) { // 初始化卖出
            this.titleLbl_.setLocaleKey('ui.button_sell');
            node.Child('sell/input_ebee', cc.EditBox).setPlaceholder('ui.amount', 'f_m');
            node.Child('price/input_ebee', cc.EditBox).setPlaceholder('ui.amount', 'f_m');
            initBoxList(this.model.CAN_DEAL_RES, node.Child('sell/box_select_be/list'));
            initBoxList(this.model.CAN_DEAL_RES, node.Child('price/box_select_be/list'));
            node.Child('layout/info/3/val').setLocaleKey('ui.one_mer_transit_cap', GameHelper_1.gameHpr.player.getMerchantTransitCap());
            this.updateSellInfo(node);
        }
        else if (type === 2) { // 初始化赠送
            this.titleLbl_.setLocaleKey('ui.button_give');
            this.alliMemberSelectNode_.Swih('default');
            node.Child('give/input_ebee', cc.EditBox).setPlaceholder('ui.amount', 'f_m');
            node.Child('target/layout/input_name', cc.EditBox).setPlaceholder('ui.input_give_name_desc', 'f_m');
            initBoxList(this.model.CAN_DEAL_RES, node.Child('give/box_select_be/list'));
            node.Child('query').Swih('');
            node.Child('3/val').setLocaleKey('ui.one_mer_transit_cap', GameHelper_1.gameHpr.player.getMerchantTransitCap());
            this.updateGiveInfo(node);
        }
    };
    BuildBazaarChildPnlCtrl.prototype.onRemove = function () {
        this.giveName = '';
        this.pagesNode_.Child('2/target/layout/input_name', cc.EditBox).string = '';
    };
    BuildBazaarChildPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/pages_n/1/price/box_select_be
    BuildBazaarChildPnlCtrl.prototype.onClickBoxSelect = function (event, data) {
        this.changeBoxList(event.target, true);
    };
    // path://root/pages_n/1/price/box_select_be/box_mask_be
    BuildBazaarChildPnlCtrl.prototype.onClickBoxMask = function (event, data) {
        this.changeBoxList(event.target.parent, false);
    };
    // path://root/pages_n/1/price/box_select_be/list/box_item_be
    BuildBazaarChildPnlCtrl.prototype.onClickBoxItem = function (event, data) {
        var _a, _b;
        var node = event.target, it = node.parent.parent;
        var type = (_b = (_a = it.parent) === null || _a === void 0 ? void 0 : _a.parent) === null || _b === void 0 ? void 0 : _b.name;
        this.changeBoxList(it, false);
        this.selectBoxItem(it, node.Data);
        if (type === '1') {
            this.updateSellMerchantInfo();
        }
        else if (type === '2') {
            this.updateGiveMerchantInfo();
        }
    };
    // path://root/pages_n/1/price/input_ebee
    BuildBazaarChildPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var type = event.node.parent.parent.name;
        if (type === '1') {
            this.updateSellMerchantInfo();
        }
        else if (type === '2') {
            this.updateGiveMerchantInfo();
        }
    };
    // path://root/pages_n/2/query_player_be
    BuildBazaarChildPnlCtrl.prototype.onClickQueryPlayer = function (event, data) {
        this.queryPlayer(true, '');
    };
    // path://root/ok_b_be
    BuildBazaarChildPnlCtrl.prototype.onClickOk = function (event, data) {
        if (this.curType === 1) {
            this.sellOk();
        }
        else if (this.curType === 2) {
            this.giveOk();
        }
    };
    // path://root/pages_n/1/layout/look_res_cap_be
    BuildBazaarChildPnlCtrl.prototype.onClickLookResCap = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/ResTransitCapDesc');
    };
    // path://root/pages_n/2/target/layout/alli_member_select_be_n
    BuildBazaarChildPnlCtrl.prototype.onClickAlliMemberSelect = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('build/allianceMembers', function (member) { return member && _this.updateSelectMember(member); });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildBazaarChildPnlCtrl.prototype.changeNumByMerchantCap = function (type, num) {
        var cap = Constant_1.RES_TRANSIT_CAP[type] || 1;
        return num * cap;
    };
    BuildBazaarChildPnlCtrl.prototype.sellOk = function () {
        var _this = this;
        var node = this.pagesNode_.Child(1);
        if (!node.Data) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.MERCHANT_NOT_ENOUGH); //商人不足
        }
        var sellType = node.Child('sell/box_select_be').Data, sellEb = node.Child('sell/input_ebee', cc.EditBox);
        var buyType = node.Child('price/box_select_be').Data, buyEb = node.Child('price/input_ebee', cc.EditBox);
        var sellCount = Number(sellEb.string.trim()) || 0;
        var buyCount = Number(buyEb.string.trim()) || 0;
        var onlyAlli = !!GameHelper_1.gameHpr.alliance.getUid() && GameHelper_1.gameHpr.isFreeServer() && this.onlyAliiLookTge_.isChecked;
        this.model.sellRes(sellType, sellCount, buyType, buyCount, onlyAlli).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                sellEb.string = '';
                buyEb.string = '';
                _this.onlyAliiLookTge_.isChecked = false;
                _this.updateSellMerchantInfo(node);
                ViewHelper_1.viewHelper.showAlert('toast.sell_res_succeed');
                _this.emit(EventType_1.default.UPDATE_TRADING);
                _this.hide();
            }
        });
    };
    BuildBazaarChildPnlCtrl.prototype.giveOk = function () {
        var _this = this;
        var node = this.pagesNode_.Child(2);
        if (!node.Data) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.MERCHANT_NOT_ENOUGH); //商人不足
        }
        var nameEb = node.Child('target/layout/input_name', cc.EditBox);
        var name = nameEb.string.trim() || this.giveName;
        var type = node.Child('give/box_select_be').Data, giveEb = node.Child('give/input_ebee', cc.EditBox);
        var count = Number(giveEb.string.trim()) || 0;
        if (count === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RES_COUNT);
        }
        else if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_give_player');
        }
        this.model.giveRes(name, -1, type, count).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                giveEb.string = '';
                // nameEb.string = ''
                // node.Child('query').Swih('')
                _this.updateGiveMerchantInfo(node);
                ViewHelper_1.viewHelper.showAlert('toast.give_res_succeed');
                _this.hide();
            }
        });
    };
    // 卖出 ---------------------------------------------------------------------------------------------------------------------------------
    BuildBazaarChildPnlCtrl.prototype.updateSellInfo = function (node) {
        node = node || this.pagesNode_.Child(1);
        var sellBox = node.Child('sell/box_select_be'), priceBox = node.Child('price/box_select_be');
        this.changeBoxList(sellBox, false);
        this.changeBoxList(priceBox, false);
        sellBox.Data = sellBox.Data || this.model.CAN_DEAL_RES[0];
        priceBox.Data = priceBox.Data || this.model.CAN_DEAL_RES[1];
        sellBox['_mutual'] = priceBox;
        priceBox['_mutual'] = sellBox;
        this.selectBoxItem(sellBox, sellBox.Data);
        this.selectBoxItem(priceBox, priceBox.Data);
        this.updateSellMerchantInfo(node);
        // 公示时间 12-4 = 6
        // node.Child('publicity_time_be', TextButtonCmpt).setKey('ui.button_need_publicity_time', gameHpr.millisecondToString(gameHpr.getBazaarPublicityTime()))
        this.okBtn_.interactable = true;
        this.okBtn_.Component(cc.MultiFrame).setFrame(true);
    };
    // 刷新商人信息
    BuildBazaarChildPnlCtrl.prototype.updateSellMerchantInfo = function (node) {
        node = node || this.pagesNode_.Child(1);
        var sellInput = node.Child('sell/input_ebee', cc.EditBox), priceInput = node.Child('price/input_ebee', cc.EditBox);
        var sellType = node.Child('sell/box_select_be').Data, priceType = node.Child('price/box_select_be').Data;
        var sellNum = Number(sellInput.string.trim()) || 0;
        var priceNum = Number(priceInput.string.trim()) || 0;
        sellInput.string = sellNum ? sellNum + '' : '';
        priceInput.string = priceNum ? priceNum + '' : '';
        sellNum = this.changeNumByMerchantCap(sellType, sellNum);
        priceNum = this.changeNumByMerchantCap(priceType, priceNum);
        this.updateMerchantInfo(node, Math.max(sellNum, priceNum));
    };
    BuildBazaarChildPnlCtrl.prototype.updateMerchantInfo = function (node, cap) {
        var merchants = this.model.getMerchants(), idelMerchantCount = this.model.getMerchantsByState(Enums_1.MerchantState.NONE).length;
        var parent = this.curType === 1 ? node.Child('layout/merchant') : node;
        parent.Child('merchant/val', cc.Label).string = idelMerchantCount + '/' + merchants.length;
        // 计算需要的商人数量
        var oneCap = GameHelper_1.gameHpr.player.getMerchantTransitCap();
        var needMerchantCount = Math.ceil(cap / oneCap); //Math.max(idelMerchantCount, Math.ceil(cap / oneCap))
        parent.Child('need_merchant').setLocaleKey('ui.need_merchant', needMerchantCount);
        var maxCap = needMerchantCount * oneCap;
        var b = node.Data = cap <= maxCap;
        var parent2 = this.curType === 1 ? node.Child('layout/info') : node;
        parent2.Child('cap/val', cc.Label).Color(b ? '#A18876' : '#F38C1D').string = cap + '';
        parent2.Child('cap/max', cc.Label).string = '/' + maxCap;
    };
    // 打开关闭box弹出框
    BuildBazaarChildPnlCtrl.prototype.changeBoxList = function (node, val) {
        node.Child('box_mask_be').active = val;
        node.Child('list').active = val;
        // 刷新列表选中的颜色
        if (val) {
            var type_1 = node.Data;
            node.Child('list').children.forEach(function (m) {
                var color = m.Data === type_1 ? '#49983C' : '#756963';
                m.Child('val').Color(color);
            });
        }
    };
    // 选择一个
    BuildBazaarChildPnlCtrl.prototype.selectBoxItem = function (node, type) {
        var _a;
        var curType = node.Data;
        var banType = (_a = node['_mutual']) === null || _a === void 0 ? void 0 : _a.Data;
        node.Data = type;
        node.Child('content/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
        node.Child('content/val').setLocaleKey(Constant_1.CTYPE_NAME[type]);
        if (curType && type === banType) {
            this.selectBoxItem(node['_mutual'], curType);
        }
    };
    // 赠送 ---------------------------------------------------------------------------------------------------------------------------------
    BuildBazaarChildPnlCtrl.prototype.updateGiveInfo = function (node) {
        node = node || this.pagesNode_.Child(2);
        this.alliMemberSelectNode_.Swih(this.giveName ? 'member' : 'default');
        if (this.giveName) {
            node.Child('target/layout/input_name', cc.EditBox).string = this.giveName;
            this.onClickQueryPlayer(null, null);
            this.giveName = '';
        }
        else {
            node.Child('target/layout/input_name', cc.EditBox).string = '';
        }
        var giveBox = node.Child('give/box_select_be');
        this.changeBoxList(giveBox, false);
        this.selectBoxItem(giveBox, giveBox.Data || this.model.CAN_DEAL_RES[0]);
        this.updateGiveMerchantInfo(node);
        this.okBtn_.interactable = true;
        this.okBtn_.Component(cc.MultiFrame).setFrame(true);
    };
    BuildBazaarChildPnlCtrl.prototype.updateGiveMerchantInfo = function (node) {
        node = node || this.pagesNode_.Child(2);
        var giveInput = node.Child('give/input_ebee', cc.EditBox);
        var type = node.Child('give/box_select_be').Data;
        var giveNum = Math.min(Number(giveInput.string.trim()) || 0, GameHelper_1.gameHpr.getCountByCType(type));
        giveInput.string = giveNum ? giveNum + '' : '';
        this.updateMerchantInfo(node, this.changeNumByMerchantCap(type, giveNum));
    };
    BuildBazaarChildPnlCtrl.prototype.updateSelectMember = function (member) {
        var _a;
        this.giveName = member.nickname;
        var node = this.alliMemberSelectNode_.Swih('member')[0];
        ResHelper_1.resHelper.loadPlayerHead(node.Child('head/val'), member.headIcon, this.key);
        node.Child('name', cc.Label).string = ut.nameFormator((_a = member.nickname) !== null && _a !== void 0 ? _a : '', 5);
        this.queryPlayer(false, member.uid);
        this.pagesNode_.Child('2/target/layout/input_name', cc.EditBox).string = '';
    };
    BuildBazaarChildPnlCtrl.prototype.queryPlayer = function (isInput, uid) {
        var _a;
        var node = this.pagesNode_.Child(2);
        var name = '';
        if (isInput) {
            name = node.Child('target/layout/input_name', cc.EditBox).string.trim();
        }
        else if (uid) {
            name = uid;
        }
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_give_player');
        }
        var query = node.Child('query');
        var plr = GameHelper_1.gameHpr.queryLocalPlayer(name, -1);
        if (plr) {
            isInput && this.alliMemberSelectNode_.Swih('default');
            query.children.forEach(function (m) { return m.active = true; });
            query.Child('name', cc.LabelSysFont).string = ut.nameFormator((_a = plr.nickname) !== null && _a !== void 0 ? _a : '', 6);
            // 获取时间
            var dis = GameHelper_1.gameHpr.getSelfToMapCellDis(plr.mainCityIndex), discount = 1 - GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.TRANSIT_CD) * 0.01; // 减少运输时间 20%/40%/60%
            var time = dis * (ut.Time.Hour / Constant_1.TRANSIT_TIME) * discount;
            query.Child('desc').Color('#A18876').setLocaleKey('ui.transit_time_desc', ut.millisecondFormat(time, 'h:mm:ss'));
        }
        else {
            query.Swih('desc')[0].Color('#F38C1D').setLocaleKey(ECode_1.ecode.PLAYER_NOT_EXIST);
        }
    };
    BuildBazaarChildPnlCtrl = __decorate([
        ccclass
    ], BuildBazaarChildPnlCtrl);
    return BuildBazaarChildPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildBazaarChildPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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