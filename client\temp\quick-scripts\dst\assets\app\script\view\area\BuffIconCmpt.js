
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BuffIconCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c870f7ZlDVC9ICqsWZ0oj5f', 'BuffIconCmpt');
// app/script/view/area/BuffIconCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// buff图标容器
var BuffIconCmpt = /** @class */ (function (_super) {
    __extends(BuffIconCmpt, _super);
    function BuffIconCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.spr = null;
        _this.icons = [];
        _this.iconCount = 0;
        _this.index = 0;
        _this.elapsed = 0;
        return _this;
    }
    BuffIconCmpt.prototype.init = function (key) {
        this.key = key;
        this.spr = this.FindChild('val', cc.Sprite);
        this.iconCount = 0;
        return this;
    };
    BuffIconCmpt.prototype.clean = function () {
        while (this.icons.length > 0) {
            var name = this.icons.pop().name;
            var _a = name.split('_'), a = _a[0], b = _a[1], id = _a[2];
            assetsMgr.releaseTempRes('buff/' + id + '/' + name, this.key);
        }
        this.iconCount = 0;
    };
    BuffIconCmpt.prototype.add = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var name, sf;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        name = 'buff_icon_' + type;
                        sf = this.icons.find(function (m) { return m.name === name; });
                        if (!!sf) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes('buff/' + type + '/' + name, cc.SpriteFrame, this.key)];
                    case 1:
                        sf = _a.sent();
                        if (!sf || !this.isValid) {
                            return [2 /*return*/];
                        }
                        this.icons.push(sf);
                        this.iconCount = this.icons.length;
                        _a.label = 2;
                    case 2:
                        this.spr.spriteFrame = sf;
                        this.node.active = this.iconCount > 0;
                        this.index = this.icons.findIndex(function (m) { return m.name === sf.name; });
                        return [2 /*return*/];
                }
            });
        });
    };
    BuffIconCmpt.prototype.remove = function (type) {
        var name = 'buff_icon_' + type;
        for (var i = this.icons.length - 1; i >= 0; i--) {
            var sf = this.icons[i];
            if (sf.name === name) {
                this.icons.splice(i, 1);
                this.iconCount = this.icons.length;
                this.node.active = this.iconCount > 0;
                // 删除的是当前显示的
                if (this.index === i && this.iconCount > 0) {
                    this.showNext();
                }
                return;
            }
        }
    };
    BuffIconCmpt.prototype.showNext = function () {
        this.index = ut.loopValue(this.index + 1, this.icons.length);
        this.spr.spriteFrame = this.icons[this.index];
        this.elapsed = 0;
    };
    BuffIconCmpt.prototype.update = function (dt) {
        if (this.iconCount <= 1) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed >= 4) {
            this.showNext();
        }
    };
    BuffIconCmpt = __decorate([
        ccclass
    ], BuffIconCmpt);
    return BuffIconCmpt;
}(cc.Component));
exports.default = BuffIconCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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