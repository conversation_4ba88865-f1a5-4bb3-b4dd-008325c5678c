
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildMainInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4ddc9BbGYpPmrbgT4WCZJuO', 'BuildMainInfoPnlCtrl');
// app/script/view/build/BuildMainInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ccclass = cc._decorator.ccclass;
var BuildMainInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildMainInfoPnlCtrl, _super);
    function BuildMainInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.landNode_ = null; // path://root/pages_n/0/info/land_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.policySv_ = null; // path://root/pages_n/1/info/policy_sv
        //@end
        _this.PKEY_TAB = 'MAIN_INFO_TAB';
        _this.tab = 0;
        _this.user = null;
        _this.player = null;
        _this.data = null;
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildMainInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_POLICY_SLOTS] = this.onUpdatePolicySlots, _b.enter = true, _b),
        ];
    };
    BuildMainInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildMainInfoPnlCtrl.prototype.onEnter = function (data, tab) {
        this.data = data;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildMainInfoPnlCtrl.prototype.onRemove = function () {
    };
    BuildMainInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildMainInfoPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        if (type === 0) {
            this.landNode_.Child('val').setLocaleKey('ui.cur_land_count', GameHelper_1.gameHpr.getPlayerOweCellCount(GameHelper_1.gameHpr.getUid()));
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            this.showPolicyInfo(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildMainInfoPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/policy/slot_nbe
    BuildMainInfoPnlCtrl.prototype.onClickSlot = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.Data, lv = Number(event.target.name);
        var isUnlock = this.data.lv >= lv;
        if (!isUnlock) {
            return ViewHelper_1.viewHelper.showAlert('ui.lv_unlock_new', { params: [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_1'] });
        }
        else if (!!(data === null || data === void 0 ? void 0 : data.isYetStudy())) {
            return ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.id, 'book');
        }
        else if (!data || data.selectIds.length === 0) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_unlock_prev_policy');
        }
        ViewHelper_1.viewHelper.showPnl('build/StudySelect', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildMainInfoPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.POLICY_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新政策
    BuildMainInfoPnlCtrl.prototype.onUpdatePolicySlots = function () {
        if (this.tab === 1) {
            this.showPolicyInfo(this.pagesNode_.Child(1));
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 内政
    BuildMainInfoPnlCtrl.prototype.showPolicyInfo = function (node) {
        var _this = this;
        var slot = node.Child('policy/slot_nbe'), buildLv = this.data.lv;
        var policys = this.player.getPolicySlots();
        slot.children.forEach(function (it) {
            var lv = Number(it.name);
            var data = it.Data = policys[lv];
            var isUnlock = buildLv >= lv, isSelect = !!(data === null || data === void 0 ? void 0 : data.isYetStudy());
            if (!isUnlock) { //还未解锁
                it.Swih('lock');
            }
            else if (!isSelect) { //还未选择
                var canSelect = !!(data === null || data === void 0 ? void 0 : data.selectIds.length);
                it.Swih('add')[0].Child('dot').active = canSelect;
            }
            // 选择信息
            if (isSelect) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, it.Swih('icon')[0], _this.key);
            }
        });
        this.updatePolicyEffectInfo();
    };
    // 内政效果
    BuildMainInfoPnlCtrl.prototype.updatePolicyEffectInfo = function () {
        var _this = this;
        var initItemHeight = 92;
        var datas = GameHelper_1.gameHpr.getPlayerPolicysBaseInfo();
        this.policySv_.stopAutoScroll();
        this.policySv_.Items(datas, function (it, data, i) {
            it.Child('name/val').setLocaleKey('policyText.name_' + data.id);
            var value = data.values[Math.min(data.up - 1, data.values.length - 1)];
            var params = "<color=#4AB32E>" + value + "</c>";
            var descRt = it.Child('desc', cc.RichText);
            descRt.setLocaleKey('policyText.desc_' + data.id, params);
            it.Child('policys').Items(data.styles, function (item, style) {
                ResHelper_1.resHelper.loadPolicyIcon(data.id, item.Child('icon'), _this.key);
                var styleSpr = item.Child('style', cc.Sprite);
                if (style >= 10) { // 季节政策
                    ResHelper_1.resHelper.loadIcon('icon/season_' + (style % 10), styleSpr, _this.key);
                }
                else if (style === 2) { // 联盟政策
                    ResHelper_1.resHelper.loadAlliIcon(GameHelper_1.gameHpr.alliance.getIcon(), styleSpr, _this.key);
                }
                else {
                    styleSpr.Component(cc.Sprite).spriteFrame = null;
                }
            });
            var h = Math.max(initItemHeight, (initItemHeight - descRt.lineHeight) + descRt.node.height);
            if (it.height !== h) {
                it.height = h;
                it.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            }
        });
    };
    BuildMainInfoPnlCtrl = __decorate([
        ccclass
    ], BuildMainInfoPnlCtrl);
    return BuildMainInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildMainInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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