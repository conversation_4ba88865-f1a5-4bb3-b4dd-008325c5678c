
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/AreaObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ca016vDzJZP3oiq43TAx8a9', 'AreaObj');
// app/script/model/area/AreaObj.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var FSPModel_1 = require("../fsp/FSPModel");
var ArmyObj_1 = require("./ArmyObj");
var BuildObj_1 = require("./BuildObj");
var PawnObj_1 = require("./PawnObj");
// 一个战场
var AreaObj = /** @class */ (function () {
    function AreaObj() {
        this.index = 0; //所属地图的哪个单元
        this.owner = '';
        this.cityId = 0;
        this.curHp = 0; //当前区域血量
        this.maxHp = 0;
        this.wall = null; //城墙
        this.builds = []; //建筑列表
        this.armys = []; //军队列表
        this.maxArmyCount = 0; //最大容纳军队数量
        this.maxAddPawnTimes = 0; //最大补兵次数
        this.battleTempPawns = []; //战斗中 临时生成的士兵
        this.areaSize = cc.v2(); //地图区域大小
        this.buildSize = cc.v2(); //建筑区域大小
        this.buildOrigin = cc.v2(); //建筑区域起点
        this.buildMapPoints = []; //建筑的所有地面点
        this.mainPoints = []; //主位置 用于受攻击的点
        this.passPoints = []; //关口位置 固定4个 0.上 1.右 2.下 3.左
        this.passPointMap = {};
        this.banPlacePawnPointMap = {}; //禁止放置士兵的位置
        this.fencePointMap = {}; //栅栏点位置
        this.flames = []; //火焰点位
        this.alliFlags = []; //联盟旗帜点位
        this.ancientDecorates = []; //遗迹装饰点位
        this.walls = []; //城墙列表
        this.buildGroundPointMap = {}; //可以摆放的建筑地面
        this.active = false; //视图层是否显示
        this.leaveTime = 0; //离开视图的时间
        this.pawnAnimations = []; //士兵的动画组件 在这里统一每帧调用
        this.battleEndData = null; //战斗结束时的数据
        this.fspModel = null;
        this.battleTime = 0; //战斗经过时间
        this.getBattleTime = 0;
    }
    AreaObj.prototype.init = function (data) {
        var _a;
        this.index = data.index;
        this.owner = data.owner || '';
        this.cityId = data.cityId || 0;
        this.curHp = data.hp[0];
        this.maxHp = (_a = data.hp[1]) !== null && _a !== void 0 ? _a : 0;
        this.wall = null;
        this.walls = [];
        this.battleTempPawns = [];
        this.updateBuilds(data.builds || []);
        this.updateArmys(data.armys);
        if (this.index < 0 && data.buildInfo) {
            this.updateCityByPawnInfo(data.buildInfo[0], data.buildInfo[1], true);
        }
        else {
            this.updateCity(this.cityId, true);
        }
        this.updateSize();
        return this;
    };
    AreaObj.prototype.strip = function () {
        var _a;
        return {
            index: this.index,
            owner: this.owner,
            cityId: this.cityId,
            hp: [this.curHp, this.maxHp],
            builds: this.builds.map(function (m) { return m.strip(); }),
            armys: this.armys.map(function (m) { return m.strip(); }),
            buildInfo: this.wall ? [this.wall.id, this.wall.lv] : null,
            battle: (_a = this.fspModel) === null || _a === void 0 ? void 0 : _a.strip()
        };
    };
    AreaObj.prototype.updateSize = function () {
        var _this = this;
        var isBoss = this.isBoss();
        if (this.cityId > 0) {
            var json = assetsMgr.getJsonData('city', this.cityId);
            this.areaSize.set(ut.stringToVec2(json.area_size, 'x'));
            this.buildSize.set(ut.stringToVec2(json.build_size, 'x'));
            this.maxArmyCount = json.max_army || Constant_1.DEFAULT_MAX_ARMY_COUNT;
            this.maxAddPawnTimes = json.max_add_pawn || Constant_1.DEFAULT_MAX_ADD_PAWN_TIMES;
        }
        else {
            this.areaSize.set(Constant_1.DEFAULT_AREA_SIZE);
            this.buildSize.set(isBoss ? Constant_1.BOSS_BUILD_SIZE : Constant_1.DEFAULT_BUILD_SIZE);
            this.maxArmyCount = Constant_1.DEFAULT_MAX_ARMY_COUNT;
            this.maxAddPawnTimes = Constant_1.DEFAULT_MAX_ADD_PAWN_TIMES;
        }
        // 计算建筑区域的起点 用地图区域 - 建筑区域 / 2
        this.areaSize.sub(this.buildSize, this.buildOrigin).mulSelf(0.5).floor();
        // 计算出城墙的列表
        if (this.wall) {
            this.walls = MapHelper_1.mapHelper.getWallBuildInfo(this.buildSize);
        }
        // 建筑地面
        this.buildMapPoints = MapHelper_1.mapHelper.genPointsBySize(this.buildSize).filter(function (p) { return !_this.walls.some(function (m) { return m.point.equals(p); }); });
        // 关口
        this.passPoints = MapHelper_1.mapHelper.getPassPoints(this.areaSize);
        this.passPointMap = {};
        this.passPoints.forEach(function (m) { return _this.passPointMap[m.ID()] = true; });
        // 不能摆放士兵的位置
        this.banPlacePawnPointMap = {};
        MapHelper_1.mapHelper.getBanPlacePawnPoints(this.areaSize).forEach(function (m) { return _this.banPlacePawnPointMap[m.ID()] = true; });
        // 边界装饰位置
        this.fencePointMap = MapHelper_1.mapHelper.getAreaFencePoints(this.areaSize, this.flames);
        // 获取联盟旗帜位置
        this.alliFlags = MapHelper_1.mapHelper.getAlliFlagPoints(this.areaSize);
        // 获取遗迹装饰位置
        this.ancientDecorates = MapHelper_1.mapHelper.getAncientDecoratePoints(this.buildSize);
        // 主
        if (isBoss) {
            this.mainPoints = MapHelper_1.mapHelper.getMainPointsByBoss(this.buildSize, this.buildOrigin);
        }
        else {
            this.mainPoints = MapHelper_1.mapHelper.getMainPoints(this.areaSize, this.buildSize, this.buildOrigin);
        }
    };
    AreaObj.prototype.clean = function () {
        var _a;
        (_a = this.fspModel) === null || _a === void 0 ? void 0 : _a.stop();
        this.fspModel = null;
    };
    AreaObj.prototype.getIndex = function () { return this.index; };
    AreaObj.prototype.getOwner = function () { return this.owner; };
    // 获取地图边框的宽度 至少都有2格
    AreaObj.prototype.getBorderSize = function (out) {
        out = out || cc.v2();
        var areaSize = this.areaSize, winGrid = CameraCtrl_1.cameraCtrl.getWinGirdSize();
        out.x = Math.max(Math.ceil((winGrid.x - areaSize.x) / 2), 6);
        out.y = Math.max(Math.ceil((winGrid.y - areaSize.y) / 2), 6);
        return out;
    };
    // 是否自己占领的土地
    AreaObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否主城
    AreaObj.prototype.isMainCity = function () {
        return this.cityId === Constant_1.CITY_MAIN_NID;
    };
    // 是否遗迹
    AreaObj.prototype.isAncient = function () {
        return this.cityId >= Constant_1.CITY_CHANGAN_ID && this.cityId <= Constant_1.CITY_LUOYANG_ID;
    };
    // 是否boss
    AreaObj.prototype.isBoss = function () {
        return this.maxHp === -1;
    };
    AreaObj.prototype.getBoss = function () {
        if (this.owner) {
            return null;
        }
        for (var i = 0; i < this.armys.length; i++) {
            var pawn = this.armys[i].pawns.find(function (m) { return m.isBoss(); });
            if (pawn) {
                return pawn;
            }
        }
        return null;
    };
    // 检测是否战斗区域
    AreaObj.prototype.checkIsBattleArea = function (x, y) {
        if (x < 0 || y < 0 || x >= this.areaSize.x || y >= this.areaSize.y) {
            return false;
        }
        else if (this.checkIsBuildArea(x, y)) {
            return false;
        }
        return true;
    };
    // 检测是否建筑区域
    AreaObj.prototype.checkIsBuildArea = function (x, y) {
        return x >= this.buildOrigin.x && x < (this.buildOrigin.x + this.buildSize.x) && y >= this.buildOrigin.y && y < (this.buildOrigin.y + this.buildSize.y);
    };
    // 是否激活
    AreaObj.prototype.setActive = function (val) {
        var _a;
        this.active = val;
        if (!val && this.isBattleing() && !!((_a = this.battleEndData) === null || _a === void 0 ? void 0 : _a.index)) { //如果离开场景的时候 服务器已经结束了
            this.battleEndByLocal();
        }
        var player = GameHelper_1.gameHpr.player, isMainCity = this.index === player.getMainCityIndex() && this.owner === GameHelper_1.gameHpr.getUid();
        if (val || isMainCity) {
            this.leaveTime = 0;
        }
        else {
            this.leaveTime = Date.now();
        }
        NetHelper_1.netHelper.sendWatchArea(this.index, val);
        // 这里兼容下 主城建筑没刷新的问题
        if (val && isMainCity) {
            var obj_1 = {};
            player.getMainBuilds().forEach(function (m) { return obj_1[m.uid] = m; });
            this.builds.forEach(function (m) {
                var o = obj_1[m.uid];
                if (!o || m.lv !== o.lv) {
                    player.updateMainBuildInfo({ uid: m.uid, id: m.id, lv: m.lv });
                }
            });
        }
    };
    //#region -------------------------------------------- 建筑相关 --------------------------------------------
    AreaObj.prototype.getBuildByUid = function (uid) {
        var _a;
        if (((_a = this.wall) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.wall;
        }
        return this.builds.find(function (m) { return m.uid === uid; });
    };
    AreaObj.prototype.getBuildsById = function (id) {
        var _a;
        if (((_a = this.wall) === null || _a === void 0 ? void 0 : _a.id) === id) {
            return [this.wall];
        }
        return this.builds.filter(function (m) { return m.id === id; });
    };
    AreaObj.prototype.getBuildById = function (id) {
        var _a;
        if (((_a = this.wall) === null || _a === void 0 ? void 0 : _a.id) === id) {
            return this.wall;
        }
        return this.builds.find(function (m) { return m.id === id; });
    };
    AreaObj.prototype.getBuildByPawnId = function (id) {
        var _a;
        if (((_a = this.wall) === null || _a === void 0 ? void 0 : _a.getBuildPawnId()) === id) {
            return this.wall;
        }
        return this.builds.find(function (m) { return m.getBuildPawnId() === id; });
    };
    // 刷新可以摆放的建筑位置
    AreaObj.prototype.updateBuildGroundPoints = function (data) {
        var _this = this;
        this.buildGroundPointMap = {};
        var pointMap = {};
        this.builds.forEach(function (m) {
            if (m.uid !== data.uid) {
                m.getActPoints().forEach(function (p) { return pointMap[p.ID()] = true; });
            }
        });
        this.buildMapPoints.forEach(function (p) {
            var id = p.ID();
            if (!pointMap[id]) {
                _this.buildGroundPointMap[id] = true;
            }
        });
        return this.buildGroundPointMap;
    };
    AreaObj.prototype.getBuildGroundPointMap = function () {
        return this.buildGroundPointMap;
    };
    // 是否在可建筑里面
    AreaObj.prototype.isBuildBorder = function (point) {
        var size = this.buildSize;
        return point.x < 1 || point.y < 1 || point.x >= size.x - 1 || point.y >= size.y - 1;
    };
    // 是否都满级
    AreaObj.prototype.isBuildsMaxLv = function (id) {
        return !this.builds.some(function (m) { return m.id === id && !m.isMaxLv(); });
    };
    // 修正建筑点
    AreaObj.prototype.amendBuildPoint = function (point, size) {
        var sx = size.x - 1, sy = size.y - 1;
        point.x = cc.misc.clampf(point.x, 1, this.buildSize.x - 2 - sx);
        point.y = cc.misc.clampf(point.y, 1, this.buildSize.y - 2 - sy);
        return point;
    };
    // 添加建筑
    AreaObj.prototype.addBuild = function (data, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        var build = this.builds.find(function (m) { return m.uid === data.uid; });
        if (!build) {
            build = this.builds.add(new BuildObj_1.default().fromSvr(data));
            isEmit && eventCenter.emit(EventType_1.default.ADD_BUILD, build);
        }
        return build;
    };
    // 删除建筑
    AreaObj.prototype.removeBuild = function (uid, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        var build = this.builds.remove('uid', uid);
        if (build && isEmit) {
            eventCenter.emit(EventType_1.default.REMOVE_BUILD, build);
        }
    };
    // 刷新建筑
    AreaObj.prototype.updateBuilds = function (builds) {
        var _this = this;
        this.builds = [];
        builds.forEach(function (m) {
            if (m.id === Constant_1.BUILD_WALL_NID || m.id === Constant_1.ANCIENT_WALL_ID) {
                _this.wall = new BuildObj_1.default().fromSvr(m);
            }
            else {
                _this.builds.push(new BuildObj_1.default().fromSvr(m));
            }
        });
    };
    // 建筑升级
    AreaObj.prototype.buildUp = function (uid, lv) {
        var _a;
        var build = ((_a = this.wall) === null || _a === void 0 ? void 0 : _a.uid) === uid ? this.wall : this.getBuildByUid(uid);
        if (build) {
            build.updateLv(lv);
            eventCenter.emit(EventType_1.default.UPDATE_BUILD_LV, build);
        }
    };
    // 刷新城市信息
    AreaObj.prototype.updateCity = function (cityId, init) {
        var _a;
        this.cityId = cityId;
        if (this.cityId === Constant_1.CITY_MAIN_NID) {
            return;
        }
        else if (this.isAncient()) { //遗迹
        }
        else if (this.isBoss()) { //boss
        }
        else if (this.cityId === Constant_1.CITY_FORT_NID) { //要塞
            var lv = GameHelper_1.gameHpr.getPlayerTowerLvByPawn(this.owner, 7002);
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_FORT_NID, lv: lv, point: cc.v2() })];
        }
        else if (this.owner) { //哨站
            var lv = GameHelper_1.gameHpr.getPlayerTowerLvByPawn(this.owner, 7001);
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_TOWER_NID, lv: lv, point: cc.v2() })];
        }
        else { //添加旗子
            var lv = ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(this.index)) === null || _a === void 0 ? void 0 : _a.landLv) || 1;
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_FLAG_NID, lv: lv, point: cc.v2() })];
        }
        if (!init) {
            eventCenter.emit(EventType_1.default.UPDATE_BUILDS, this.index);
        }
    };
    // 刷新城市信息 目前只用于回放
    AreaObj.prototype.updateCityByPawnInfo = function (pawnId, pawnLv, init) {
        var _a;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.index);
        if (pawnId === 8001) { //遗迹
        }
        else if (this.isBoss()) { //boss
        }
        else if (pawnId === 7001) {
            this.cityId = 0;
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_TOWER_NID, lv: pawnLv, point: cc.v2() })];
            cell === null || cell === void 0 ? void 0 : cell.updateCity(this.cityId);
        }
        else if (pawnId === 7002) {
            this.cityId = Constant_1.CITY_FORT_NID;
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_FORT_NID, lv: pawnLv, point: cc.v2() })];
            cell === null || cell === void 0 ? void 0 : cell.updateCity(this.cityId);
        }
        else if (pawnId === 7003 && this.wall) {
            this.wall.lv = pawnLv;
            this.wall.updateAttrJson();
        }
        else {
            var lv = ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(this.index)) === null || _a === void 0 ? void 0 : _a.landLv) || 1;
            this.builds = [new BuildObj_1.default().fromSvr({ index: this.index, uid: ut.UID(), id: Constant_1.BUILD_FLAG_NID, lv: lv, point: cc.v2() })];
        }
        if (!init) {
            eventCenter.emit(EventType_1.default.UPDATE_BUILDS, this.index);
        }
    };
    //#region ----------------------------------------- 军队相关 -----------------------------------------
    // 刷新军队列表
    AreaObj.prototype.updateArmys = function (armys) {
        this.armys = (armys || []).map(function (m) { return new ArmyObj_1.default().fromSvr(m); });
    };
    // 获取当前战场的所有士兵 加上军旗
    AreaObj.prototype.getAllPawns = function () {
        var arr = [];
        this.armys.forEach(function (m) {
            if (!m.isMarching()) {
                arr.pushArr(m.pawns);
            }
        });
        return arr.concat(this.battleTempPawns);
    };
    AreaObj.prototype.getArmyByUid = function (uid) {
        return this.armys.find(function (m) { return m.uid === uid; });
    };
    // 添加军队
    AreaObj.prototype.addArmy = function (data) {
        this.updateArmy(data);
    };
    // 删除军队
    AreaObj.prototype.removeArmy = function (uid, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        var army = this.armys.remove('uid', uid);
        if (army && isEmit) {
            eventCenter.emit(EventType_1.default.REMOVE_ARMY, army, this.index);
            if (army.isOwner()) {
                this.updateTreasureReddot(); //刷新宝箱红点
            }
        }
    };
    // 删除行军军队 这里没有真正删掉只是改变了状态 但区域内实际不会显示
    AreaObj.prototype.removeArmyByMarch = function (uid) {
        var army = this.getArmyByUid(uid);
        if (army) {
            army.state = Enums_1.ArmyState.MARCH;
            eventCenter.emit(EventType_1.default.REMOVE_ARMY, army, this.index);
            if (army.isOwner()) {
                this.updateTreasureReddot(); //刷新宝箱红点
            }
        }
    };
    // 更新军队信息
    AreaObj.prototype.updateArmy = function (data) {
        var _a, _b, _c;
        if (!((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) && !((_b = data.drillPawns) === null || _b === void 0 ? void 0 : _b.length) && !((_c = data.curingPawns) === null || _c === void 0 ? void 0 : _c.length)) {
            return this.removeArmy(data.uid);
        }
        var army = this.armys.find(function (m) { return m.uid === data.uid; });
        if (army) {
            eventCenter.emit(EventType_1.default.UPDATE_ARMY, army.fromSvr(data));
        }
        else {
            eventCenter.emit(EventType_1.default.ADD_ARMY, this.armys.add(new ArmyObj_1.default().fromSvr(data)));
        }
        this.updateTreasureReddot(); //刷新宝箱红点
    };
    // 训练士兵时刷新军队
    AreaObj.prototype.updateArmyDrillPawns = function (data) {
        var _a, _b, _c;
        if (!this.isBattleing()) {
            return this.updateArmy(data);
        }
        var army = this.armys.find(function (m) { return m.uid === data.uid; });
        if (army) {
            army.drillPawns = data.drillPawns || [];
            if (army.getActPawnCount() === 0) {
                this.removeArmy(army.uid);
            }
        }
        else if (!!((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || !!((_b = data.drillPawns) === null || _b === void 0 ? void 0 : _b.length) || !!((_c = data.curingPawns) === null || _c === void 0 ? void 0 : _c.length)) {
            eventCenter.emit(EventType_1.default.ADD_ARMY, this.armys.add(new ArmyObj_1.default().fromSvr(data)));
        }
    };
    // 治疗士兵时刷新军队
    AreaObj.prototype.updateArmyCurePawns = function (data) {
        var _a, _b, _c;
        if (!this.isBattleing()) {
            return this.updateArmy(data);
        }
        var army = this.armys.find(function (m) { return m.uid === data.uid; });
        if (army) {
            army.curingPawns = data.curingPawns || [];
            if (army.getActPawnCount() === 0) {
                this.removeArmy(army.uid);
            }
        }
        else if (!!((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || !!((_b = data.drillPawns) === null || _b === void 0 ? void 0 : _b.length) || !!((_c = data.curingPawns) === null || _c === void 0 ? void 0 : _c.length)) {
            eventCenter.emit(EventType_1.default.ADD_ARMY, this.armys.add(new ArmyObj_1.default().fromSvr(data)));
        }
    };
    // 更新所有军队
    AreaObj.prototype.updateAllArmy = function (armys) {
        this.updateArmys(armys);
        eventCenter.emit(EventType_1.default.UPDATE_ALL_ARMY, this.index);
        this.updateTreasureReddot(); //刷新宝箱红点
    };
    // 删除士兵
    AreaObj.prototype.removeArmyPawn = function (auid, uid, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (!auid || !uid) {
            return;
        }
        var army = this.armys.find(function (m) { return m.uid === auid; });
        if (!army) {
            return;
        }
        army.removePawn(uid);
        // 如果没有了 要删除军队 回放不用删除
        if (this.index > 0 && army.getActPawnCount() === 0) {
            this.removeArmy(army.uid, isEmit);
        }
        else if (isEmit) {
            eventCenter.emit(EventType_1.default.REMOVE_PAWN, this.index, uid);
        }
        this.updateTreasureReddot(); //刷新宝箱红点
    };
    // 获取士兵根据唯一UID
    AreaObj.prototype.getPawn = function (uid) {
        for (var i = 0, l = this.armys.length; i < l; i++) {
            var pawn = this.armys[i].pawns.find(function (m) { return m.uid === uid; });
            if (pawn) {
                return pawn;
            }
        }
        return null;
    };
    // 获取临时士兵
    AreaObj.prototype.getBattleTempPawn = function (uid) {
        return this.battleTempPawns.find(function (m) { return m.uid === uid; });
    };
    AreaObj.prototype.removeBattleTempPawn = function (uid) {
        this.battleTempPawns.remove('uid', uid);
    };
    // 获取士兵 精确获取
    AreaObj.prototype.getPawnByPrecise = function (armyUid, uid) {
        var _a;
        return (_a = this.getArmyByUid(armyUid)) === null || _a === void 0 ? void 0 : _a.pawns.find(function (m) { return m.uid === uid; });
    };
    // 添加士兵
    AreaObj.prototype.addPawn = function (auid, data) {
        var army = this.getArmyByUid(auid);
        if (army) {
            army.drillPawns.remove(data.id); //这里暂时删掉 因为一般是从战斗中过来的
            var pawn = army.addPawn(data);
            eventCenter.emit(EventType_1.default.ADD_PAWN, this.index, pawn);
            return pawn;
        }
        return null;
    };
    // 刷新宝箱红点
    AreaObj.prototype.updateTreasureReddot = function () {
        if (this.active) {
            var uid_1 = GameHelper_1.gameHpr.getUid();
            var val = this.armys.some(function (army) { return army.owner === uid_1 && !army.isMarching() && army.pawns.some(function (m) { return m.treasures.length > 0; }); });
            ReddotHelper_1.reddotHelper.set('treasure_' + this.index, val);
        }
    };
    // 添加建筑军旗
    AreaObj.prototype.addBuildFlag = function (data) {
        var pawn = new PawnObj_1.default().init(3601, null, 1);
        pawn.uid = data.uid;
        pawn.curHp = data.hp[0] || 0;
        pawn.maxHp = data.hp[1] || 0;
        pawn.aIndex = this.index;
        pawn.owner = data.owner;
        pawn.point.set(data.point);
        this.battleTempPawns.push(pawn);
        return pawn;
    };
    // 添加宠物士兵
    AreaObj.prototype.addPetPawn = function (data) {
        var pawn = new PawnObj_1.default().init(data.id, null, data.lv);
        pawn.uid = data.uid;
        pawn.aIndex = this.index;
        pawn.owner = data.owner;
        pawn.point.set(data.point);
        this.battleTempPawns.push(pawn);
        return pawn;
    };
    // 添加地面非战斗单位
    AreaObj.prototype.addNoncombat = function (data) {
        var _a;
        var pawn = new PawnObj_1.default().init(data.id, null, data.lv || 1);
        pawn.uid = data.uid;
        pawn.aIndex = this.index;
        pawn.owner = data.owner;
        pawn.enterDir = (_a = data.enterDir) !== null && _a !== void 0 ? _a : -1;
        pawn.point.set(data.point);
        this.battleTempPawns.push(pawn);
        return pawn;
    };
    //#region ----------------------------------------- 战斗相关 -----------------------------------------
    AreaObj.prototype.getUid = function () { return 'area_' + this.index; };
    AreaObj.prototype.getPawnType = function () { return Enums_1.PawnType.BUILD; };
    AreaObj.prototype.update = function (dt) {
        if (this.fspModel) {
            this.fspModel.update(dt);
        }
        else {
            this.updatePawnAnimationFrame(dt * 1000);
        }
    };
    // 刷新士兵的动画帧
    AreaObj.prototype.updatePawnAnimationFrame = function (dt) {
        for (var i = this.pawnAnimations.length - 1; i >= 0; i--) {
            var cmpt = this.pawnAnimations[i];
            if (cmpt.isValid) {
                cmpt.updateFrame(dt);
            }
            else {
                this.pawnAnimations.splice(i, 1);
            }
        }
    };
    AreaObj.prototype.addPawnAnimation = function (cmpt) {
        if (!this.pawnAnimations.has('uuid', cmpt.uuid)) {
            this.pawnAnimations.push(cmpt);
        }
    };
    AreaObj.prototype.removePawnAnimation = function (uuid) {
        this.pawnAnimations.remove('uuid', uuid);
    };
    AreaObj.prototype.isBattleing = function () { return !!this.fspModel; };
    AreaObj.prototype.getFspModel = function () { return this.fspModel; };
    // 获取战斗经过的时间
    AreaObj.prototype.getBattleElapsedTime = function () {
        var _a;
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return ((_a = this.fspModel) === null || _a === void 0 ? void 0 : _a.getBattleTime()) || 0;
        }
        return this.battleTime + (Date.now() - this.getBattleTime);
    };
    // 初始化战斗前的数据
    AreaObj.prototype.initBattleData = function (data) {
        var _a;
        this.curHp = data.hp[0];
        this.maxHp = (_a = data.hp[1]) !== null && _a !== void 0 ? _a : 1;
        this.battleTempPawns = [];
        this.updateArmys(data.armys);
    };
    AreaObj.prototype.battleReady = function () {
        this.armys.forEach(function (m) {
            if (m.isMarching()) {
                return;
            }
            m.state = Enums_1.ArmyState.FIGHT;
            m.pawns.forEach(function (pawn) { return pawn.changeState(Enums_1.PawnState.STAND); });
        });
    };
    // 战斗开始
    AreaObj.prototype.battleBegin = function (data, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (!data) {
            return;
        }
        this.battleEndData = null;
        this.battleTime = data.battleTime || 0;
        this.getBattleTime = Date.now();
        this.battleReady();
        // 启动帧同步
        this.fspModel = new FSPModel_1.default().init(this, data);
        // 通知发生战斗
        if (isEmit) {
            eventCenter.emit(EventType_1.default.AREA_BATTLE_BEGIN, this.index);
            this.updateTreasureReddot(); //刷新红点 因为进入战斗的时候不会收到ADD_ARMY通知
        }
    };
    // 开始本地战斗
    AreaObj.prototype.battleLocalBegin = function (data, frameDataMap, mulIndex, serverPlayBack) {
        this.battleEndData = null;
        this.battleTime = 0;
        this.getBattleTime = Date.now();
        // 启动帧同步
        this.fspModel = new FSPModel_1.default().init(this, data, frameDataMap, mulIndex, serverPlayBack);
        return this.fspModel;
    };
    // 战斗结束 从服务器来
    AreaObj.prototype.battleEndByServer = function (data) {
        var _a;
        // cc.log('battleEndByServer', !!this.battleEndData, data)
        if (this.battleEndData) {
            return this.battleEnd(data);
        }
        this.battleEndData = data;
        // 如果是时间到了就直接结束
        if ((_a = data === null || data === void 0 ? void 0 : data.data) === null || _a === void 0 ? void 0 : _a.isBattleEndTime) {
            this.battleEndByLocal();
        }
    };
    // 战斗结束 从本地来
    AreaObj.prototype.battleEndByLocal = function () {
        var _a, _b, _c;
        // cc.log('battleEndByLocal', !!this.battleEndData, this.index)
        var isWin = this.index < 0 ? (_a = this.fspModel) === null || _a === void 0 ? void 0 : _a.isWin() : false;
        (_b = this.fspModel) === null || _b === void 0 ? void 0 : _b.stop();
        var battleEndInfo = {};
        if (this.fspModel) {
            battleEndInfo.battleTime = (_c = this.fspModel) === null || _c === void 0 ? void 0 : _c.getBattleTime();
            battleEndInfo.currentFrameIndex = this.fspModel.getCurrentFrameIndex();
        }
        this.fspModel = null;
        if (this.index < 0) {
            var playBack = GameHelper_1.gameHpr.playback;
            if (!playBack.isSimulating) {
                isWin && GameHelper_1.gameHpr.showBattleEndView(GameHelper_1.gameHpr.getCellBaseRes(Math.abs(this.index), this.cityId), playBack.getRecordData().treasures);
                eventCenter.emit(EventType_1.default.AREA_BATTLE_END, this.index, isWin);
            }
        }
        else if (this.battleEndData) {
            this.battleEnd(this.battleEndData);
        }
        else {
            this.battleEndData = {};
        }
        // 如果是新手村 向本地服务器发送消息
        if (GameHelper_1.gameHpr.isNoviceMode) {
            var attacker = '';
            if (this.isDie() && this.armys.length > 0 && this.armys[0].owner !== this.owner) {
                attacker = this.armys[0].owner;
            }
            GameHelper_1.gameHpr.noviceServer.triggerBattleEnd(this.index, attacker, battleEndInfo);
        }
    };
    AreaObj.prototype.battleEnd = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var areaInfo, info, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        areaInfo = data.data;
                        if (!(!areaInfo || areaInfo.index === -1)) return [3 /*break*/, 2];
                        return [4 /*yield*/, NetHelper_1.netHelper.reqGetAreaInfo({ index: this.index })];
                    case 1:
                        info = _b.sent();
                        areaInfo = (_a = info.data) === null || _a === void 0 ? void 0 : _a.data;
                        _b.label = 2;
                    case 2:
                        this.battleEndData = null;
                        uid = GameHelper_1.gameHpr.getUid();
                        if (this.active && data.attacker === uid && areaInfo.owner === uid) { //显示结束弹窗
                            GameHelper_1.gameHpr.showBattleEndView(GameHelper_1.gameHpr.getCellBaseRes(this.index, this.cityId), data.treasures, data.noTreasureByNotStamina, data.fullLostCount);
                        }
                        this.init(areaInfo || { index: this.index });
                        eventCenter.emit(EventType_1.default.AREA_BATTLE_END, this.index);
                        this.updateTreasureReddot(); //刷新红点
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaObj.prototype.isDie = function () {
        if (this.isBoss()) {
            return !this.armys.some(function (m) { return m.pawns.some(function (m) { return !m.owner && !m.isDie(); }); });
        }
        return this.curHp <= 0;
    };
    AreaObj.prototype.getHpRatio = function () {
        return this.curHp / this.maxHp;
    };
    AreaObj.prototype.isHasAnger = function () {
        return false;
    };
    AreaObj.prototype.getBattleCamp = function () {
        var _a, _b, _c;
        return (_c = (_b = (_a = this.fspModel) === null || _a === void 0 ? void 0 : _a.getBattleController()) === null || _b === void 0 ? void 0 : _b.getFighterCampIndex('')) !== null && _c !== void 0 ? _c : -1;
    };
    // 播放战斗特效
    AreaObj.prototype.playBattleEffect = function (types, point, root, params) {
        var _this = this;
        types === null || types === void 0 ? void 0 : types.forEach(function (type) { return eventCenter.emit(EventType_1.default.PLAY_BATTLE_EFFECT, { type: type, index: _this.index, point: point, root: root || 'role', params: params }); });
    };
    return AreaObj;
}());
exports.default = AreaObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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