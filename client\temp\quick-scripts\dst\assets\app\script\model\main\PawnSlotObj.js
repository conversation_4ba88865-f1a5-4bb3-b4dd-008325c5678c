
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/PawnSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '614dc6VOa1EmYGobahy2hB5', 'PawnSlotObj');
// app/script/model/main/PawnSlotObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnObj_1 = require("../area/PawnObj");
var CTypeObj_1 = require("../common/CTypeObj");
var BaseStudyObj_1 = require("./BaseStudyObj");
// 一个士兵槽位
var PawnSlotObj = /** @class */ (function (_super) {
    __extends(PawnSlotObj, _super);
    function PawnSlotObj() {
        var _this = _super.call(this) || this;
        _this.pawn = null;
        _this.json = null;
        _this.drillCost = [];
        _this.drillTime = 0;
        _this.cerealCost = null;
        _this.studyType = Enums_1.StudyType.PAWN;
        return _this;
    }
    PawnSlotObj.prototype.init = function () {
        this.json = assetsMgr.getJsonData('pawnBase', this.id);
        if (this.json) {
            this.updateCost();
        }
        return this;
    };
    PawnSlotObj.prototype.initPawn = function (mainCityIndex, conf) {
        if (this.id > 0) {
            this.pawn = new PawnObj_1.default().init(this.id, conf.equip, 1, conf.skinId).initAnger();
            this.pawn.aIndex = mainCityIndex;
            if (conf.attackSpeed) {
                this.pawn.attackSpeed = conf.attackSpeed;
            }
        }
        else {
            this.pawn = null;
        }
        return this;
    };
    Object.defineProperty(PawnSlotObj.prototype, "skinId", {
        get: function () { var _a; return ((_a = this.pawn) === null || _a === void 0 ? void 0 : _a.skinId) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnSlotObj.prototype, "viewId", {
        get: function () {
            var _a;
            if (this.id >= 0) {
                return this.skinId || this.id;
            }
            var id = Math.abs(this.id);
            var skinId = (_a = GameHelper_1.gameHpr.player.getConfigPawnInfo(id)) === null || _a === void 0 ? void 0 : _a.skinId;
            return skinId || id;
        },
        enumerable: false,
        configurable: true
    });
    PawnSlotObj.prototype.updateCost = function () {
        if (this.json) {
            this.drillCost = GameHelper_1.gameHpr.getPawnCost(this.id, 0, this.json);
            this.cerealCost = new CTypeObj_1.default().init(Enums_1.CType.CEREAL_C, 0, this.json.cereal_cost || 0);
            this.drillCost.push(this.cerealCost);
            this.drillTime = GameHelper_1.gameHpr.getPawnCostTime(this.id, 0, this.json);
        }
    };
    return PawnSlotObj;
}(BaseStudyObj_1.default));
exports.default = PawnSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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