
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/DescListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '773e2RpSWNFH7+h4bCz0L9/', 'DescListPnlCtrl');
// app/script/view/common/DescListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var DescListPnlCtrl = /** @class */ (function (_super) {
    __extends(DescListPnlCtrl, _super);
    function DescListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    DescListPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DescListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DescListPnlCtrl.prototype.onEnter = function (list, isNo) {
        var isTh = mc.lang === 'th';
        this.rootNode_.Items(list, function (it, data, i) {
            if (isNo) {
                it.setLocaleKey('ui.text', (i + 1) + (isTh ? '.' : '. ') + assetsMgr.lang(data.text, data.params || []));
            }
            else {
                it.setLocaleKey(data.text, data.params || []);
            }
        });
    };
    DescListPnlCtrl.prototype.onRemove = function () {
    };
    DescListPnlCtrl.prototype.onClean = function () {
    };
    DescListPnlCtrl = __decorate([
        ccclass
    ], DescListPnlCtrl);
    return DescListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DescListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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