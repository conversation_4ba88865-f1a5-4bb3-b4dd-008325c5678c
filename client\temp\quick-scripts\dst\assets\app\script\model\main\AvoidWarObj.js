
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/AvoidWarObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3403dBpDzVIepSaJn5PKJhI', 'AvoidWarObj');
// app/script/model/main/AvoidWarObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 免战信息
var AvoidWarObj = /** @class */ (function () {
    function AvoidWarObj() {
        this.index = 0;
        this.time = 0;
        this.getTime = 0;
        this.type = 0;
    }
    AvoidWarObj.prototype.init = function (index, time, type) {
        this.index = Number(index);
        this.time = time;
        this.getTime = Date.now();
        this.type = type;
        return this;
    };
    AvoidWarObj.prototype.getSurplusTime = function () {
        return Math.max(0, this.time - (Date.now() - this.getTime));
    };
    return AvoidWarObj;
}());
exports.default = AvoidWarObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxtYWluXFxBdm9pZFdhck9iai50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUNBLE9BQU87QUFDUDtJQUFBO1FBRVcsVUFBSyxHQUFXLENBQUMsQ0FBQTtRQUNqQixTQUFJLEdBQVcsQ0FBQyxDQUFBO1FBQ2hCLFlBQU8sR0FBVyxDQUFDLENBQUE7UUFDbkIsU0FBSSxHQUFXLENBQUMsQ0FBQTtJQWEzQixDQUFDO0lBWFUsMEJBQUksR0FBWCxVQUFZLEtBQWEsRUFBRSxJQUFZLEVBQUUsSUFBWTtRQUNqRCxJQUFJLENBQUMsS0FBSyxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUMxQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsT0FBTyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQTtRQUN6QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSxvQ0FBYyxHQUFyQjtRQUNJLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUksR0FBRyxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQTtJQUMvRCxDQUFDO0lBQ0wsa0JBQUM7QUFBRCxDQWxCQSxBQWtCQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIOWFjeaImOS/oeaBr1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBBdm9pZFdhck9iaiB7XHJcblxyXG4gICAgcHVibGljIGluZGV4OiBudW1iZXIgPSAwXHJcbiAgICBwdWJsaWMgdGltZTogbnVtYmVyID0gMFxyXG4gICAgcHVibGljIGdldFRpbWU6IG51bWJlciA9IDBcclxuICAgIHB1YmxpYyB0eXBlOiBudW1iZXIgPSAwXHJcblxyXG4gICAgcHVibGljIGluaXQoaW5kZXg6IHN0cmluZywgdGltZTogbnVtYmVyLCB0eXBlOiBudW1iZXIpIHtcclxuICAgICAgICB0aGlzLmluZGV4ID0gTnVtYmVyKGluZGV4KVxyXG4gICAgICAgIHRoaXMudGltZSA9IHRpbWVcclxuICAgICAgICB0aGlzLmdldFRpbWUgPSBEYXRlLm5vdygpXHJcbiAgICAgICAgdGhpcy50eXBlID0gdHlwZVxyXG4gICAgICAgIHJldHVybiB0aGlzXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIGdldFN1cnBsdXNUaW1lKCkge1xyXG4gICAgICAgIHJldHVybiBNYXRoLm1heCgwLCB0aGlzLnRpbWUgLSAoRGF0ZS5ub3coKSAtIHRoaXMuZ2V0VGltZSkpXHJcbiAgICB9XHJcbn0iXX0=