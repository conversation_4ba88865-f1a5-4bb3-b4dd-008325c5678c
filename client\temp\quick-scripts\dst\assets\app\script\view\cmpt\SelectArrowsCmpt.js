
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/SelectArrowsCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ffccbRJuVhBd56QaYT9ilwG', 'SelectArrowsCmpt');
// app/script/view/cmpt/common/SelectArrowsCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 箭头选择
var SelectArrowsCmpt = /** @class */ (function (_super) {
    __extends(SelectArrowsCmpt, _super);
    function SelectArrowsCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.speed = 13;
        _this.items = [];
        return _this;
    }
    SelectArrowsCmpt.prototype.onLoad = function () {
        this.items = this.node.children.map(function (m) { return m.FindChild('val'); });
    };
    SelectArrowsCmpt.prototype.update = function (dt) {
        var sx = dt * this.speed;
        this.items.forEach(function (m) { return m.x += sx; });
        var it = this.items[0];
        if (it.x >= 4) {
            it.x = 4;
            this.speed *= -1;
        }
        else if (it.x < 0) {
            it.x = 0;
            this.speed *= -1;
        }
    };
    SelectArrowsCmpt = __decorate([
        ccclass
    ], SelectArrowsCmpt);
    return SelectArrowsCmpt;
}(cc.Component));
exports.default = SelectArrowsCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNtcHRcXGNvbW1vblxcU2VsZWN0QXJyb3dzQ21wdC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBTSxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUU1QyxPQUFPO0FBRVA7SUFBOEMsb0NBQVk7SUFBMUQ7UUFBQSxxRUFxQkM7UUFuQlcsV0FBSyxHQUFXLEVBQUUsQ0FBQTtRQUNsQixXQUFLLEdBQWMsRUFBRSxDQUFBOztJQWtCakMsQ0FBQztJQWhCRyxpQ0FBTSxHQUFOO1FBQ0ksSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFsQixDQUFrQixDQUFDLENBQUE7SUFDaEUsQ0FBQztJQUVELGlDQUFNLEdBQU4sVUFBTyxFQUFVO1FBQ2IsSUFBTSxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7UUFDMUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBVCxDQUFTLENBQUMsQ0FBQTtRQUNsQyxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ3hCLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDWCxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUNSLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFDLENBQUE7U0FDbkI7YUFBTSxJQUFJLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ2pCLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ1IsSUFBSSxDQUFDLEtBQUssSUFBSSxDQUFDLENBQUMsQ0FBQTtTQUNuQjtJQUNMLENBQUM7SUFwQmdCLGdCQUFnQjtRQURwQyxPQUFPO09BQ2EsZ0JBQWdCLENBcUJwQztJQUFELHVCQUFDO0NBckJELEFBcUJDLENBckI2QyxFQUFFLENBQUMsU0FBUyxHQXFCekQ7a0JBckJvQixnQkFBZ0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5IH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuLy8g566t5aS06YCJ5oupXHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNlbGVjdEFycm93c0NtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIHByaXZhdGUgc3BlZWQ6IG51bWJlciA9IDEzXHJcbiAgICBwcml2YXRlIGl0ZW1zOiBjYy5Ob2RlW10gPSBbXVxyXG5cclxuICAgIG9uTG9hZCgpIHtcclxuICAgICAgICB0aGlzLml0ZW1zID0gdGhpcy5ub2RlLmNoaWxkcmVuLm1hcChtID0+IG0uRmluZENoaWxkKCd2YWwnKSlcclxuICAgIH1cclxuXHJcbiAgICB1cGRhdGUoZHQ6IG51bWJlcikge1xyXG4gICAgICAgIGNvbnN0IHN4ID0gZHQgKiB0aGlzLnNwZWVkXHJcbiAgICAgICAgdGhpcy5pdGVtcy5mb3JFYWNoKG0gPT4gbS54ICs9IHN4KVxyXG4gICAgICAgIGNvbnN0IGl0ID0gdGhpcy5pdGVtc1swXVxyXG4gICAgICAgIGlmIChpdC54ID49IDQpIHtcclxuICAgICAgICAgICAgaXQueCA9IDRcclxuICAgICAgICAgICAgdGhpcy5zcGVlZCAqPSAtMVxyXG4gICAgICAgIH0gZWxzZSBpZiAoaXQueCA8IDApIHtcclxuICAgICAgICAgICAgaXQueCA9IDBcclxuICAgICAgICAgICAgdGhpcy5zcGVlZCAqPSAtMVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdfQ==