
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/GameOverPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b3d22DLYe9OWZB39z6v5L7x', 'GameOverPnlCtrl');
// app/script/view/main/GameOverPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var GameOverPnlCtrl = /** @class */ (function (_super) {
    __extends(GameOverPnlCtrl, _super);
    function GameOverPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.titleLbl_ = null; // path://root_n/title/title_l
        _this.contentNode_ = null; // path://root_n/content_n
        _this.buttonsNode_ = null; // path://root_n/buttons_n
        //@end
        _this.data = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    GameOverPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GameOverPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var overInfo, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.setParam({ maskOpacity: 180 });
                        this.data = null;
                        overInfo = GameHelper_1.gameHpr.world.getGameOverInfo();
                        // 如果不是血战到底 overInfo 必须有值
                        if (!overInfo && GameHelper_1.gameHpr.world.getWinCondType() !== Enums_1.WinCondType.KARMIC_MAHJONG) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetGameRecordOne', { sid: GameHelper_1.gameHpr.getSid() })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.data = { overInfo: overInfo, record: (data === null || data === void 0 ? void 0 : data.data) || {} };
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    GameOverPnlCtrl.prototype.onEnter = function () {
        var _a, _b, _c;
        this.emit(mc.Event.HIDE_ALL_PNL, '', 'main/GameOver');
        var overInfo = (_a = this.data) === null || _a === void 0 ? void 0 : _a.overInfo, record = (_b = this.data) === null || _b === void 0 ? void 0 : _b.record;
        var isWin = (record === null || record === void 0 ? void 0 : record.win) ? 1 : 0, closeTime = this.rootNode_.Child('close_time');
        this.titleLbl_.setLocaleKey('ui.game_over_win_' + isWin);
        var winKey = 0;
        closeTime.active = !!overInfo;
        // 对局结算
        if (overInfo) {
            winKey = (record === null || record === void 0 ? void 0 : record.win) ? 0 : 1;
            this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + isWin, overInfo.winName);
            closeTime.setLocaleKey('ui.server_close_desc', GameHelper_1.gameHpr.millisecondToStringForDay(overInfo.closeTime));
            this.buttonsNode_.Swih('0');
        }
        else {
            var hasAlli = GameHelper_1.gameHpr.world.getAlliBaseInfo(GameHelper_1.gameHpr.alliance.getUid());
            winKey = hasAlli ? 2 : 1;
            this.rootNode_.Child('desc').setLocaleKey('ui.game_over_desc_' + (hasAlli ? 3 : 2));
            this.buttonsNode_.Swih('back_lobby_be');
        }
        this.rootNode_.Child('icon').Swih(winKey);
        this.rootNode_.Child('title', cc.MultiFrame).setFrame(winKey);
        this.rootNode_.Child('rank/val').setLocaleKey('ui.game_over_rank', ((_c = record === null || record === void 0 ? void 0 : record.rank) !== null && _c !== void 0 ? _c : -1) + 1);
        var isRankServer = GameHelper_1.gameHpr.getServerType(record === null || record === void 0 ? void 0 : record.sid) === 2; //是否评分区
        var rankNode = this.contentNode_.Child('rank');
        if (rankNode.active = isRankServer && !!record) {
            var _d = GameHelper_1.gameHpr.resolutionRankScore(record.curRankScore || 0, 1), id = _d.id, winPoint = _d.winPoint, maxPoint = _d.maxPoint;
            var addRankScore = record.addRankScore || 0;
            ResHelper_1.resHelper.loadRankScoreIcon(id, rankNode.Child('icon'), this.key);
            rankNode.Child('lay/val').setLocaleKey('ui.rank_name_' + (id >= 0 ? id : 'none'));
            rankNode.Child('lay/point').setLocaleKey('ui.rank_score_num_3', winPoint);
            rankNode.Child('lay/add', cc.Label).Color(addRankScore >= 0 ? '#ECFF47' : '#F55756').string = '(' + ((addRankScore >= 0 ? '+' : '-') + addRankScore) + ')';
            rankNode.Child('progress/bar', cc.Sprite).fillRange = maxPoint === -1 ? 1 : winPoint / maxPoint;
            rankNode.Child('progress/text', cc.Label).string = maxPoint === -1 ? (winPoint + '') : (winPoint + '/' + maxPoint);
        }
        var award = this.contentNode_.Child('award');
        if (award.active = !!(record === null || record === void 0 ? void 0 : record.addWarToken)) {
            award.setLocaleKey('ui.game_over_award_desc', record.addWarToken);
        }
        if (!storageMgr.loadBool('ta_track_gameoverui')) {
            storageMgr.saveBool('ta_track_gameoverui', true);
            TaHelper_1.taHelper.track('ta_gameOverUI', { isWin: !!(record === null || record === void 0 ? void 0 : record.win) });
        }
    };
    GameOverPnlCtrl.prototype.onRemove = function () {
    };
    GameOverPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/lock_standings_be
    GameOverPnlCtrl.prototype.onClickLockStandings = function (event, _) {
        var _a;
        var data = (_a = this.data) === null || _a === void 0 ? void 0 : _a.record;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('menu/PersonalGameDetail', data);
        }
    };
    // path://root_n/buttons_n/0/back_lobby_be
    GameOverPnlCtrl.prototype.onClickBackLobby = function (event, data) {
        var _a;
        if (!((_a = this.data) === null || _a === void 0 ? void 0 : _a.overInfo) || !ViewHelper_1.viewHelper.showNoLongerTip('back_lobby_tip', {
            content: 'ui.back_lobby_tip',
            okText: 'login.button_ok',
            ok: function () { return ViewHelper_1.viewHelper.backLobby(); },
            cancel: function () { },
        })) {
            ViewHelper_1.viewHelper.backLobby();
        }
    };
    GameOverPnlCtrl = __decorate([
        ccclass
    ], GameOverPnlCtrl);
    return GameOverPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GameOverPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXEdhbWVPdmVyUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxxREFBMEQ7QUFDMUQsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCx5REFBd0Q7QUFDeEQsNkRBQTREO0FBRXBELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQTZDLG1DQUFjO0lBQTNEO1FBQUEscUVBeUdDO1FBdkdHLDBCQUEwQjtRQUNsQixlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMsZ0JBQWdCO1FBQzFDLGVBQVMsR0FBYSxJQUFJLENBQUEsQ0FBQyw4QkFBOEI7UUFDekQsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQywwQkFBMEI7UUFDdkQsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQywwQkFBMEI7UUFDL0QsTUFBTTtRQUVFLFVBQUksR0FBNEMsSUFBSSxDQUFBOztRQTRGNUQsTUFBTTtRQUNOLGlIQUFpSDtRQUVqSCxpSEFBaUg7SUFDckgsQ0FBQztJQTlGVSx5Q0FBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLGtDQUFRLEdBQXJCOzs7Ozs7d0JBQ0ksSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLFdBQVcsRUFBRSxHQUFHLEVBQUUsQ0FBQyxDQUFBO3dCQUNuQyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTt3QkFDWixRQUFRLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsZUFBZSxFQUFFLENBQUE7d0JBQzlDLHlCQUF5Qjt3QkFDekIsSUFBSSxDQUFDLFFBQVEsSUFBSSxvQkFBTyxDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsS0FBSyxtQkFBVyxDQUFDLGNBQWMsRUFBRTs0QkFDNUUsc0JBQU07eUJBQ1Q7d0JBQ3FCLHFCQUFNLG9CQUFPLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQywyQkFBMkIsRUFBRSxFQUFFLEdBQUcsRUFBRSxvQkFBTyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBQTs7d0JBQWpHLEtBQWdCLFNBQWlGLEVBQS9GLEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQTt3QkFDakIsSUFBSSxDQUFDLEdBQUcsRUFBRTs0QkFDTixJQUFJLENBQUMsSUFBSSxHQUFHLEVBQUUsUUFBUSxVQUFBLEVBQUUsTUFBTSxFQUFFLENBQUEsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksS0FBSSxFQUFFLEVBQUUsQ0FBQTt5QkFDckQ7Ozs7O0tBQ0o7SUFFTSxpQ0FBTyxHQUFkOztRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxZQUFZLEVBQUUsRUFBRSxFQUFFLGVBQWUsQ0FBQyxDQUFBO1FBQ3JELElBQU0sUUFBUSxTQUFHLElBQUksQ0FBQyxJQUFJLDBDQUFFLFFBQVEsRUFBRSxNQUFNLFNBQUcsSUFBSSxDQUFDLElBQUksMENBQUUsTUFBTSxDQUFBO1FBQ2hFLElBQU0sS0FBSyxHQUFHLENBQUEsTUFBTSxhQUFOLE1BQU0sdUJBQU4sTUFBTSxDQUFFLEdBQUcsRUFBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFlBQVksQ0FBQyxDQUFBO1FBQ2pGLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLG1CQUFtQixHQUFHLEtBQUssQ0FBQyxDQUFBO1FBQ3hELElBQUksTUFBTSxHQUFHLENBQUMsQ0FBQTtRQUNkLFNBQVMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLFFBQVEsQ0FBQTtRQUM3QixPQUFPO1FBQ1AsSUFBSSxRQUFRLEVBQUU7WUFDVixNQUFNLEdBQUcsQ0FBQSxNQUFNLGFBQU4sTUFBTSx1QkFBTixNQUFNLENBQUUsR0FBRyxFQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUM1QixJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsb0JBQW9CLEdBQUcsS0FBSyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQTtZQUN6RixTQUFTLENBQUMsWUFBWSxDQUFDLHNCQUFzQixFQUFFLG9CQUFPLENBQUMseUJBQXlCLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUE7WUFDckcsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7U0FDOUI7YUFBTTtZQUNILElBQU0sT0FBTyxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLGVBQWUsQ0FBQyxvQkFBTyxDQUFDLFFBQVEsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFBO1lBQ3hFLE1BQU0sR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ3hCLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxvQkFBb0IsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ25GLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUFBO1NBQzFDO1FBQ0QsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQ3pDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQzdELElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxtQkFBbUIsRUFBRSxPQUFDLE1BQU0sYUFBTixNQUFNLHVCQUFOLE1BQU0sQ0FBRSxJQUFJLG1DQUFJLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUE7UUFDNUYsSUFBTSxZQUFZLEdBQUcsb0JBQU8sQ0FBQyxhQUFhLENBQUMsTUFBTSxhQUFOLE1BQU0sdUJBQU4sTUFBTSxDQUFFLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQSxDQUFDLE9BQU87UUFDckUsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDaEQsSUFBSSxRQUFRLENBQUMsTUFBTSxHQUFHLFlBQVksSUFBSSxDQUFDLENBQUMsTUFBTSxFQUFFO1lBQ3RDLElBQUEsS0FBNkIsb0JBQU8sQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLENBQUMsWUFBWSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBbkYsRUFBRSxRQUFBLEVBQUUsUUFBUSxjQUFBLEVBQUUsUUFBUSxjQUE2RCxDQUFBO1lBQzNGLElBQU0sWUFBWSxHQUFHLE1BQU0sQ0FBQyxZQUFZLElBQUksQ0FBQyxDQUFBO1lBQzdDLHFCQUFTLENBQUMsaUJBQWlCLENBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ2pFLFFBQVEsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsWUFBWSxDQUFDLGVBQWUsR0FBRyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQTtZQUNqRixRQUFRLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDLFlBQVksQ0FBQyxxQkFBcUIsRUFBRSxRQUFRLENBQUMsQ0FBQTtZQUN6RSxRQUFRLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsS0FBSyxDQUFDLFlBQVksSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsTUFBTSxHQUFHLEdBQUcsR0FBRyxDQUFDLENBQUMsWUFBWSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsR0FBRyxZQUFZLENBQUMsR0FBRyxHQUFHLENBQUE7WUFDMUosUUFBUSxDQUFDLEtBQUssQ0FBQyxjQUFjLEVBQUUsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFNBQVMsR0FBRyxRQUFRLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxHQUFHLFFBQVEsQ0FBQTtZQUMvRixRQUFRLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLFFBQVEsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxHQUFHLEdBQUcsR0FBRyxRQUFRLENBQUMsQ0FBQTtTQUNySDtRQUNELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQzlDLElBQUksS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUMsTUFBTSxhQUFOLE1BQU0sdUJBQU4sTUFBTSxDQUFFLFdBQVcsQ0FBQSxFQUFFO1lBQ3RDLEtBQUssQ0FBQyxZQUFZLENBQUMseUJBQXlCLEVBQUUsTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFBO1NBQ3BFO1FBQ0QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMscUJBQXFCLENBQUMsRUFBRTtZQUM3QyxVQUFVLENBQUMsUUFBUSxDQUFDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxDQUFBO1lBQ2hELG1CQUFRLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUMsTUFBTSxhQUFOLE1BQU0sdUJBQU4sTUFBTSxDQUFFLEdBQUcsQ0FBQSxFQUFFLENBQUMsQ0FBQTtTQUM1RDtJQUNMLENBQUM7SUFFTSxrQ0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLGlDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQixrQ0FBa0M7SUFDbEMsOENBQW9CLEdBQXBCLFVBQXFCLEtBQTBCLEVBQUUsQ0FBUzs7UUFDdEQsSUFBTSxJQUFJLFNBQUcsSUFBSSxDQUFDLElBQUksMENBQUUsTUFBTSxDQUFBO1FBQzlCLElBQUksSUFBSSxFQUFFO1lBQ04sdUJBQVUsQ0FBQyxPQUFPLENBQUMseUJBQXlCLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDdEQ7SUFDTCxDQUFDO0lBRUQsMENBQTBDO0lBQzFDLDBDQUFnQixHQUFoQixVQUFpQixLQUEwQixFQUFFLElBQVk7O1FBQ3JELElBQUksUUFBQyxJQUFJLENBQUMsSUFBSSwwQ0FBRSxRQUFRLENBQUEsSUFBSSxDQUFDLHVCQUFVLENBQUMsZUFBZSxDQUFDLGdCQUFnQixFQUFFO1lBQ3RFLE9BQU8sRUFBRSxtQkFBbUI7WUFDNUIsTUFBTSxFQUFFLGlCQUFpQjtZQUN6QixFQUFFLEVBQUUsY0FBTSxPQUFBLHVCQUFVLENBQUMsU0FBUyxFQUFFLEVBQXRCLENBQXNCO1lBQ2hDLE1BQU0sRUFBRSxjQUFRLENBQUM7U0FDcEIsQ0FBQyxFQUFFO1lBQ0EsdUJBQVUsQ0FBQyxTQUFTLEVBQUUsQ0FBQTtTQUN6QjtJQUNMLENBQUM7SUFwR2dCLGVBQWU7UUFEbkMsT0FBTztPQUNhLGVBQWUsQ0F5R25DO0lBQUQsc0JBQUM7Q0F6R0QsQUF5R0MsQ0F6RzRDLEVBQUUsQ0FBQyxXQUFXLEdBeUcxRDtrQkF6R29CLGVBQWUiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHYW1lT3ZlckluZm8gfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCI7XG5pbXBvcnQgeyBXaW5Db25kVHlwZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHRhSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVGFIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEdhbWVPdmVyUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSByb290Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X25cbiAgICBwcml2YXRlIHRpdGxlTGJsXzogY2MuTGFiZWwgPSBudWxsIC8vIHBhdGg6Ly9yb290X24vdGl0bGUvdGl0bGVfbFxuICAgIHByaXZhdGUgY29udGVudE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL2NvbnRlbnRfblxuICAgIHByaXZhdGUgYnV0dG9uc05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdF9uL2J1dHRvbnNfblxuICAgIC8vQGVuZFxuXG4gICAgcHJpdmF0ZSBkYXRhOiB7IG92ZXJJbmZvOiBHYW1lT3ZlckluZm8sIHJlY29yZDogYW55IH0gPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgICAgIHRoaXMuc2V0UGFyYW0oeyBtYXNrT3BhY2l0eTogMTgwIH0pXG4gICAgICAgIHRoaXMuZGF0YSA9IG51bGxcbiAgICAgICAgbGV0IG92ZXJJbmZvID0gZ2FtZUhwci53b3JsZC5nZXRHYW1lT3ZlckluZm8oKVxuICAgICAgICAvLyDlpoLmnpzkuI3mmK/ooYDmiJjliLDlupUgb3ZlckluZm8g5b+F6aG75pyJ5YC8XG4gICAgICAgIGlmICghb3ZlckluZm8gJiYgZ2FtZUhwci53b3JsZC5nZXRXaW5Db25kVHlwZSgpICE9PSBXaW5Db25kVHlwZS5LQVJNSUNfTUFISk9ORykge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyBlcnIsIGRhdGEgfSA9IGF3YWl0IGdhbWVIcHIubmV0LnJlcXVlc3QoJ2xvYmJ5L0hEX0dldEdhbWVSZWNvcmRPbmUnLCB7IHNpZDogZ2FtZUhwci5nZXRTaWQoKSB9KVxuICAgICAgICBpZiAoIWVycikge1xuICAgICAgICAgICAgdGhpcy5kYXRhID0geyBvdmVySW5mbywgcmVjb3JkOiBkYXRhPy5kYXRhIHx8IHt9IH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKCkge1xuICAgICAgICB0aGlzLmVtaXQobWMuRXZlbnQuSElERV9BTExfUE5MLCAnJywgJ21haW4vR2FtZU92ZXInKVxuICAgICAgICBjb25zdCBvdmVySW5mbyA9IHRoaXMuZGF0YT8ub3ZlckluZm8sIHJlY29yZCA9IHRoaXMuZGF0YT8ucmVjb3JkXG4gICAgICAgIGNvbnN0IGlzV2luID0gcmVjb3JkPy53aW4gPyAxIDogMCwgY2xvc2VUaW1lID0gdGhpcy5yb290Tm9kZV8uQ2hpbGQoJ2Nsb3NlX3RpbWUnKVxuICAgICAgICB0aGlzLnRpdGxlTGJsXy5zZXRMb2NhbGVLZXkoJ3VpLmdhbWVfb3Zlcl93aW5fJyArIGlzV2luKVxuICAgICAgICBsZXQgd2luS2V5ID0gMFxuICAgICAgICBjbG9zZVRpbWUuYWN0aXZlID0gISFvdmVySW5mb1xuICAgICAgICAvLyDlr7nlsYDnu5PnrpdcbiAgICAgICAgaWYgKG92ZXJJbmZvKSB7XG4gICAgICAgICAgICB3aW5LZXkgPSByZWNvcmQ/LndpbiA/IDAgOiAxXG4gICAgICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnZGVzYycpLnNldExvY2FsZUtleSgndWkuZ2FtZV9vdmVyX2Rlc2NfJyArIGlzV2luLCBvdmVySW5mby53aW5OYW1lKVxuICAgICAgICAgICAgY2xvc2VUaW1lLnNldExvY2FsZUtleSgndWkuc2VydmVyX2Nsb3NlX2Rlc2MnLCBnYW1lSHByLm1pbGxpc2Vjb25kVG9TdHJpbmdGb3JEYXkob3ZlckluZm8uY2xvc2VUaW1lKSlcbiAgICAgICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLlN3aWgoJzAnKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgaGFzQWxsaSA9IGdhbWVIcHIud29ybGQuZ2V0QWxsaUJhc2VJbmZvKGdhbWVIcHIuYWxsaWFuY2UuZ2V0VWlkKCkpXG4gICAgICAgICAgICB3aW5LZXkgPSBoYXNBbGxpID8gMiA6IDFcbiAgICAgICAgICAgIHRoaXMucm9vdE5vZGVfLkNoaWxkKCdkZXNjJykuc2V0TG9jYWxlS2V5KCd1aS5nYW1lX292ZXJfZGVzY18nICsgKGhhc0FsbGkgPyAzIDogMikpXG4gICAgICAgICAgICB0aGlzLmJ1dHRvbnNOb2RlXy5Td2loKCdiYWNrX2xvYmJ5X2JlJylcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnaWNvbicpLlN3aWgod2luS2V5KVxuICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgndGl0bGUnLCBjYy5NdWx0aUZyYW1lKS5zZXRGcmFtZSh3aW5LZXkpXG4gICAgICAgIHRoaXMucm9vdE5vZGVfLkNoaWxkKCdyYW5rL3ZhbCcpLnNldExvY2FsZUtleSgndWkuZ2FtZV9vdmVyX3JhbmsnLCAocmVjb3JkPy5yYW5rID8/IC0xKSArIDEpXG4gICAgICAgIGNvbnN0IGlzUmFua1NlcnZlciA9IGdhbWVIcHIuZ2V0U2VydmVyVHlwZShyZWNvcmQ/LnNpZCkgPT09IDIgLy/mmK/lkKbor4TliIbljLpcbiAgICAgICAgY29uc3QgcmFua05vZGUgPSB0aGlzLmNvbnRlbnROb2RlXy5DaGlsZCgncmFuaycpXG4gICAgICAgIGlmIChyYW5rTm9kZS5hY3RpdmUgPSBpc1JhbmtTZXJ2ZXIgJiYgISFyZWNvcmQpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgaWQsIHdpblBvaW50LCBtYXhQb2ludCB9ID0gZ2FtZUhwci5yZXNvbHV0aW9uUmFua1Njb3JlKHJlY29yZC5jdXJSYW5rU2NvcmUgfHwgMCwgMSlcbiAgICAgICAgICAgIGNvbnN0IGFkZFJhbmtTY29yZSA9IHJlY29yZC5hZGRSYW5rU2NvcmUgfHwgMFxuICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRSYW5rU2NvcmVJY29uKGlkLCByYW5rTm9kZS5DaGlsZCgnaWNvbicpLCB0aGlzLmtleSlcbiAgICAgICAgICAgIHJhbmtOb2RlLkNoaWxkKCdsYXkvdmFsJykuc2V0TG9jYWxlS2V5KCd1aS5yYW5rX25hbWVfJyArIChpZCA+PSAwID8gaWQgOiAnbm9uZScpKVxuICAgICAgICAgICAgcmFua05vZGUuQ2hpbGQoJ2xheS9wb2ludCcpLnNldExvY2FsZUtleSgndWkucmFua19zY29yZV9udW1fMycsIHdpblBvaW50KVxuICAgICAgICAgICAgcmFua05vZGUuQ2hpbGQoJ2xheS9hZGQnLCBjYy5MYWJlbCkuQ29sb3IoYWRkUmFua1Njb3JlID49IDAgPyAnI0VDRkY0NycgOiAnI0Y1NTc1NicpLnN0cmluZyA9ICcoJyArICgoYWRkUmFua1Njb3JlID49IDAgPyAnKycgOiAnLScpICsgYWRkUmFua1Njb3JlKSArICcpJ1xuICAgICAgICAgICAgcmFua05vZGUuQ2hpbGQoJ3Byb2dyZXNzL2JhcicsIGNjLlNwcml0ZSkuZmlsbFJhbmdlID0gbWF4UG9pbnQgPT09IC0xID8gMSA6IHdpblBvaW50IC8gbWF4UG9pbnRcbiAgICAgICAgICAgIHJhbmtOb2RlLkNoaWxkKCdwcm9ncmVzcy90ZXh0JywgY2MuTGFiZWwpLnN0cmluZyA9IG1heFBvaW50ID09PSAtMSA/ICh3aW5Qb2ludCArICcnKSA6ICh3aW5Qb2ludCArICcvJyArIG1heFBvaW50KVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGF3YXJkID0gdGhpcy5jb250ZW50Tm9kZV8uQ2hpbGQoJ2F3YXJkJylcbiAgICAgICAgaWYgKGF3YXJkLmFjdGl2ZSA9ICEhcmVjb3JkPy5hZGRXYXJUb2tlbikge1xuICAgICAgICAgICAgYXdhcmQuc2V0TG9jYWxlS2V5KCd1aS5nYW1lX292ZXJfYXdhcmRfZGVzYycsIHJlY29yZC5hZGRXYXJUb2tlbilcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXN0b3JhZ2VNZ3IubG9hZEJvb2woJ3RhX3RyYWNrX2dhbWVvdmVydWknKSkge1xuICAgICAgICAgICAgc3RvcmFnZU1nci5zYXZlQm9vbCgndGFfdHJhY2tfZ2FtZW92ZXJ1aScsIHRydWUpXG4gICAgICAgICAgICB0YUhlbHBlci50cmFjaygndGFfZ2FtZU92ZXJVSScsIHsgaXNXaW46ICEhcmVjb3JkPy53aW4gfSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3Rfbi9sb2NrX3N0YW5kaW5nc19iZVxuICAgIG9uQ2xpY2tMb2NrU3RhbmRpbmdzKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMuZGF0YT8ucmVjb3JkXG4gICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ21lbnUvUGVyc29uYWxHYW1lRGV0YWlsJywgZGF0YSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290X24vYnV0dG9uc19uLzAvYmFja19sb2JieV9iZVxuICAgIG9uQ2xpY2tCYWNrTG9iYnkoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBpZiAoIXRoaXMuZGF0YT8ub3ZlckluZm8gfHwgIXZpZXdIZWxwZXIuc2hvd05vTG9uZ2VyVGlwKCdiYWNrX2xvYmJ5X3RpcCcsIHtcbiAgICAgICAgICAgIGNvbnRlbnQ6ICd1aS5iYWNrX2xvYmJ5X3RpcCcsXG4gICAgICAgICAgICBva1RleHQ6ICdsb2dpbi5idXR0b25fb2snLFxuICAgICAgICAgICAgb2s6ICgpID0+IHZpZXdIZWxwZXIuYmFja0xvYmJ5KCksXG4gICAgICAgICAgICBjYW5jZWw6ICgpID0+IHsgfSxcbiAgICAgICAgfSkpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuYmFja0xvYmJ5KClcbiAgICAgICAgfVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbn1cbiJdfQ==