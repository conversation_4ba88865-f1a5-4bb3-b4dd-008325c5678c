{"version": 3, "sources": ["assets\\app\\script\\view\\lobby\\SelectPlantSeedPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAc;IAAlE;QAAA,qEA+DC;QA7DG,0BAA0B;QAClB,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,eAAS,GAAY,IAAI,CAAA,CAAC,+BAA+B;;QAuDjE,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IACrH,CAAC;IA1DG,MAAM;IAEC,gDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,yCAAQ,GAArB;;;;;;KACC;IAEM,wCAAO,GAAd,UAAe,IAAS;QAAxB,iBAWC;QAVG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YAC3D,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC3B,qBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;YAChE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAA;YACb,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,KAAK,CAAA;QAC7C,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAA;IACjC,CAAC;IAEM,yCAAQ,GAAf;IACA,CAAC;IAEM,wCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,2CAA2C;IAC3C,4CAAW,GAAX,UAAY,KAAgB,EAAE,IAAY;QACtC,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;QACrD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAA,EAAE;YACtC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;SAChE;IACL,CAAC;IAED,oBAAoB;IACpB,0CAAS,GAAT,UAAU,KAA0B,EAAE,IAAY;QAAlD,iBAYC;;QAXG,IAAM,IAAI,SAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC,0CAAE,IAAI,CAAC,IAAI,CAAA;QAC7G,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,uBAAU,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAA;SACjE;QACD,oBAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG;YACnC,IAAI,GAAG,EAAE;gBACL,OAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;aACnC;iBAAM,IAAI,KAAI,CAAC,OAAO,EAAE;gBACrB,KAAI,CAAC,IAAI,EAAE,CAAA;aACd;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IA1DgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA+D1C;IAAD,6BAAC;CA/DD,AA+DC,CA/DmD,EAAE,CAAC,WAAW,GA+DjE;kBA/DoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class SelectPlantSeedPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private goldNode_: cc.Node = null // path://root/ok_be/lay/gold_n\n    //@end\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: any) {\n        this.listSv_.stopAutoScroll()\n        this.listSv_.content.y = 0\n        this.listSv_.Items(assetsMgr.getJson('botany').datas, (it, json) => {\n            it.Data = json\n            const val = it.Child('val')\n            resHelper.loadBotanySeedIcon(json.id, it.Child('val'), this.key)\n            val.scale = 1\n            it.Component(cc.Toggle).isChecked = false\n        })\n        this.goldNode_.active = false\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/item_te\n    onClickItem(event: cc.Toggle, data: string) {\n        const json = event.isChecked ? event.node.Data : null\n        if (this.goldNode_.active = !!json?.gold) {\n            this.goldNode_.Child('val', cc.Label).string = json.gold + ''\n        }\n    }\n\n    // path://root/ok_be\n    onClickOk(event: cc.Event.EventTouch, data: string) {\n        const json = this.listSv_.content.Component(cc.ToggleContainer).toggleItems.find(m => m.isChecked)?.node.Data\n        if (!json) {\n            return viewHelper.showAlert('toast.please_select_botany_seed')\n        }\n        gameHpr.user.planting(json.id).then(err => {\n            if (err) {\n                return viewHelper.showAlert(err)\n            } else if (this.isValid) {\n                this.hide()\n            }\n        })\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n}\n"]}