
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/SceneConf.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fbfaed0ZvhEvYioH1sW+sCx', 'SceneConf');
// app/script/common/constant/SceneConf.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MAIN_LAND_SEASON_SKIN = void 0;
var MAIN_LAND_SEASON_SKIN = [
    {
        date: [3, 4, 5],
        file: 'land_0',
        conf: {
            land: {},
            sceneEffectUrl: 'SE_HUABAN',
        },
    },
    {
        date: [6, 7, 8],
        file: 'land_1',
        conf: {
            land: {},
            sceneEffectUrl: '',
        },
    },
    {
        date: [9, 10, 11],
        file: 'land_2',
        conf: {
            land: {},
            sceneEffectUrl: 'SE_SHUYE',
        },
    },
    {
        date: [12, 1, 2],
        file: 'land_3',
        conf: {
            land: {},
            sceneEffectUrl: 'SE_SNOWFLAKE',
        },
    }
];
exports.MAIN_LAND_SEASON_SKIN = MAIN_LAND_SEASON_SKIN;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcY29uc3RhbnRcXFNjZW5lQ29uZi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxJQUFNLHFCQUFxQixHQUFHO0lBQzFCO1FBQ0ksSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDZixJQUFJLEVBQUUsUUFBUTtRQUNkLElBQUksRUFBRTtZQUNGLElBQUksRUFBRSxFQUFFO1lBQ1IsY0FBYyxFQUFFLFdBQVc7U0FDOUI7S0FDSjtJQUNEO1FBQ0ksSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDZixJQUFJLEVBQUUsUUFBUTtRQUNkLElBQUksRUFBRTtZQUNGLElBQUksRUFBRSxFQUFFO1lBQ1IsY0FBYyxFQUFFLEVBQUU7U0FDckI7S0FDSjtJQUNEO1FBQ0ksSUFBSSxFQUFFLENBQUMsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLENBQUM7UUFDakIsSUFBSSxFQUFFLFFBQVE7UUFDZCxJQUFJLEVBQUU7WUFDRixJQUFJLEVBQUUsRUFBRTtZQUNSLGNBQWMsRUFBRSxVQUFVO1NBQzdCO0tBQ0o7SUFDRDtRQUNJLElBQUksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQ2hCLElBQUksRUFBRSxRQUFRO1FBQ2QsSUFBSSxFQUFFO1lBQ0YsSUFBSSxFQUFFLEVBQUU7WUFDUixjQUFjLEVBQUUsY0FBYztTQUNqQztLQUNKO0NBQ0osQ0FBQTtBQUdHLHNEQUFxQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG5jb25zdCBNQUlOX0xBTkRfU0VBU09OX1NLSU4gPSBbIC8v5Li755WM6Z2i5Zyw5Z2X55qu6IKk6YWN572uXHJcbiAgICB7IC8vIDIsMyw0XHJcbiAgICAgICAgZGF0ZTogWzMsIDQsIDVdLCAvL+aYpVxyXG4gICAgICAgIGZpbGU6ICdsYW5kXzAnLFxyXG4gICAgICAgIGNvbmY6IHtcclxuICAgICAgICAgICAgbGFuZDoge30sXHJcbiAgICAgICAgICAgIHNjZW5lRWZmZWN0VXJsOiAnU0VfSFVBQkFOJyxcclxuICAgICAgICB9LFxyXG4gICAgfSxcclxuICAgIHsgLy8gNSw2LDdcclxuICAgICAgICBkYXRlOiBbNiwgNywgOF0sIC8v5aSPXHJcbiAgICAgICAgZmlsZTogJ2xhbmRfMScsXHJcbiAgICAgICAgY29uZjoge1xyXG4gICAgICAgICAgICBsYW5kOiB7fSxcclxuICAgICAgICAgICAgc2NlbmVFZmZlY3RVcmw6ICcnLFxyXG4gICAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgeyAvLyA4LDksMTBcclxuICAgICAgICBkYXRlOiBbOSwgMTAsIDExXSwgLy/np4tcclxuICAgICAgICBmaWxlOiAnbGFuZF8yJyxcclxuICAgICAgICBjb25mOiB7XHJcbiAgICAgICAgICAgIGxhbmQ6IHt9LFxyXG4gICAgICAgICAgICBzY2VuZUVmZmVjdFVybDogJ1NFX1NIVVlFJyxcclxuICAgICAgICB9LFxyXG4gICAgfSxcclxuICAgIHsgLy8gMTEsMTIsMVxyXG4gICAgICAgIGRhdGU6IFsxMiwgMSwgMl0sIC8v5YasXHJcbiAgICAgICAgZmlsZTogJ2xhbmRfMycsXHJcbiAgICAgICAgY29uZjoge1xyXG4gICAgICAgICAgICBsYW5kOiB7fSxcclxuICAgICAgICAgICAgc2NlbmVFZmZlY3RVcmw6ICdTRV9TTk9XRkxBS0UnLCAvL+aSreaUvumbquiKsVxyXG4gICAgICAgIH0sXHJcbiAgICB9XHJcbl1cclxuXHJcbmV4cG9ydCB7XHJcbiAgICBNQUlOX0xBTkRfU0VBU09OX1NLSU4sXHJcbn0iXX0=