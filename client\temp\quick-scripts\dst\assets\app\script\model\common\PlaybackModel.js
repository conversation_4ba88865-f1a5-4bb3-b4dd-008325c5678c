
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/PlaybackModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '18eb2xWygdClItzrpgA1L8o', 'PlaybackModel');
// app/script/model/common/PlaybackModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var NetHelper_1 = require("../../common/helper/NetHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AreaObj_1 = require("../area/AreaObj");
// 每帧计算次数
var CAL_TIMES_PER_FRAME = 500;
var FPS = 20;
/**
 * 回放模块
 */
var PlayBackModel = /** @class */ (function (_super) {
    __extends(PlayBackModel, _super);
    function PlayBackModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 操作帧数据
        _this.inputFrames = {};
        // 进度数据
        _this.curFrame = 0;
        // 是否正在观看 需要在模拟前设置
        _this.isWatching = false;
        _this.isSimulating = false;
        _this.serverPlayback = false;
        return _this;
    }
    PlayBackModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
    };
    // 设置回放战斗记录
    PlayBackModel.prototype.setRecordById = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, NetHelper_1.netHelper.reqGetBattleRecord({ uid: uid })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [4 /*yield*/, this.setRecordData(data === null || data === void 0 ? void 0 : data.record)];
                    case 2:
                        _b.sent();
                        return [2 /*return*/, err];
                }
            });
        });
    };
    PlayBackModel.prototype.getRecordData = function () {
        return this.recordData;
    };
    PlayBackModel.prototype.setCurFrameIndex = function (frameIndex) {
        this.curFrame = frameIndex;
    };
    PlayBackModel.prototype.getCurFrameIndex = function () {
        return this.curFrame;
    };
    PlayBackModel.prototype.getCurTime = function () {
        return this.curFrame * 1000 / FPS;
    };
    PlayBackModel.prototype.getDuration = function () {
        if (!this.recordData || !this.recordData.endTime) {
            return 0;
        }
        return this.recordData.endTime - this.recordData.beginTime;
    };
    PlayBackModel.prototype.getSnapshoot = function (frameIndex) {
        var _a, _b;
        return this.handleSnapshoot((_b = (_a = this.recordData) === null || _a === void 0 ? void 0 : _a.snapshoot) === null || _b === void 0 ? void 0 : _b[frameIndex]);
    };
    PlayBackModel.prototype.hasSnapshoot = function (frameIndex) {
        var _a, _b;
        return ((_b = (_a = this.recordData) === null || _a === void 0 ? void 0 : _a.snapshoot) === null || _b === void 0 ? void 0 : _b[frameIndex]) ? true : false;
    };
    PlayBackModel.prototype.handleSnapshoot = function (snapshoot) {
        if (!snapshoot)
            return null;
        var snapshootCopy = ut.deepClone(snapshoot);
        Object.assign(snapshootCopy, snapshootCopy.battle);
        return snapshootCopy;
    };
    PlayBackModel.prototype.getSnapshootExist = function (frameIndex) {
        var _a, _b;
        var snapshoot = (_b = (_a = this.recordData) === null || _a === void 0 ? void 0 : _a.snapshoot) === null || _b === void 0 ? void 0 : _b[frameIndex];
        return snapshoot;
    };
    PlayBackModel.prototype.setRecordData = function (data) {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var index, inputs, _e, _f, m, idx, startFrame;
            var e_1, _g;
            return __generator(this, function (_h) {
                switch (_h.label) {
                    case 0:
                        if (!data) {
                            this.curFrame = 0;
                            this.recordData = null;
                            return [2 /*return*/];
                        }
                        index = -data.index;
                        inputs = {};
                        try {
                            for (_e = __values(data.frames), _f = _e.next(); !_f.done; _f = _e.next()) {
                                m = _f.value;
                                idx = m.currentFrameIndex;
                                if (!inputs[idx]) {
                                    inputs[idx] = [];
                                }
                                inputs[idx].push(m);
                                if (m.type === 0) {
                                    m.index = index;
                                    (_a = m.builds) === null || _a === void 0 ? void 0 : _a.forEach(function (b) { return b.index = index; });
                                    (_b = m.armys) === null || _b === void 0 ? void 0 : _b.forEach(function (a) {
                                        var _a;
                                        a.index = index;
                                        (_a = a.pawns) === null || _a === void 0 ? void 0 : _a.forEach(function (p) { return p.index = index; });
                                    });
                                }
                                else {
                                    if (m.army) {
                                        m.army.index = index;
                                        (_c = m.army.pawns) === null || _c === void 0 ? void 0 : _c.forEach(function (p) { return p.index = index; });
                                    }
                                    if (m.pawn) {
                                        m.pawn.index = index;
                                    }
                                }
                                (_d = m.fighters) === null || _d === void 0 ? void 0 : _d.forEach(function (m) { return m.roundCount = 0; }); //这里将所有士兵的回合数设置成0
                            }
                        }
                        catch (e_1_1) { e_1 = { error: e_1_1 }; }
                        finally {
                            try {
                                if (_f && !_f.done && (_g = _e.return)) _g.call(_e);
                            }
                            finally { if (e_1) throw e_1.error; }
                        }
                        data.index = index;
                        this.recordData = data;
                        this.inputFrames = inputs;
                        startFrame = data.frames.find(function (item) { return item.type === 0; });
                        startFrame.index = data.index;
                        this.launchFrame = startFrame;
                        this.serverPlayback = data.serverPlayback;
                        data.snapshoot = {};
                        if (!!data.endTime) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.launchSimulate()];
                    case 1:
                        _h.sent();
                        _h.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 请求回放战斗
    PlayBackModel.prototype.reqPlayBack = function (record, speed) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_BattlePlayBack', { record: record, speed: speed !== null && speed !== void 0 ? speed : 1 })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, { err: err, data: data }];
                }
            });
        });
    };
    PlayBackModel.prototype.getProgress = function () {
        var record = this.recordData;
        if (!record) {
            return 0;
        }
        var beginTime = record.beginTime, endTime = record.endTime;
        if (!endTime || endTime === beginTime) {
            return 0;
        }
        var frameTime = Math.floor(1000 / FPS) * this.curFrame;
        return frameTime / (endTime - beginTime);
    };
    PlayBackModel.prototype.setProgress = function (progress) {
        var record = this.recordData;
        if (!record) {
            return;
        }
        var totalTime = record.endTime - record.beginTime;
        var frameIndex = Math.floor(totalTime * progress * FPS / 1000);
        if (progress === 0 || progress === 1 || Math.abs(this.curFrame - frameIndex) >= 10) {
            this.curFrame = frameIndex;
        }
    };
    // 计算结果，用于无记录的战斗 比如测试战斗
    PlayBackModel.prototype.launchSimulate = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var startInfo, area, fsp, times, snapshoots, frame0, frameIdx, recordEnable, frame, duration;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        startInfo = this.launchFrame;
                        area = new AreaObj_1.default().init(startInfo);
                        area.battleLocalBegin(startInfo, this.inputFrames);
                        fsp = area.getFspModel();
                        fsp.setPause(true);
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        this.isSimulating = true;
                        times = CAL_TIMES_PER_FRAME;
                        snapshoots = this.recordData.snapshoot;
                        frame0 = area.strip();
                        snapshoots[0] = frame0;
                        frameIdx = 0;
                        this.isWatching = true;
                        _b.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 3];
                        while (times-- > 0 && fsp.isRunning) {
                            fsp.executeOneFrameForSimulate();
                            frameIdx = fsp.getCurrentFrameIndex();
                            (_a = this.lateStep) === null || _a === void 0 ? void 0 : _a.call(this, frameIdx, area);
                            recordEnable = fsp.recordEnable();
                            if (recordEnable) {
                                frame = area.strip();
                                snapshoots[frame.battle.currentFrameIndex] = frame;
                            }
                        }
                        if (!fsp.isRunning) {
                            return [3 /*break*/, 3];
                        }
                        times = CAL_TIMES_PER_FRAME;
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 2:
                        _b.sent();
                        return [3 /*break*/, 1];
                    case 3:
                        fsp.setPause(false);
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        this.isSimulating = false;
                        duration = frameIdx * Math.floor(1000 / FPS);
                        this.recordData.beginTime = 0;
                        this.recordData.endTime = duration;
                        return [2 /*return*/];
                }
            });
        });
    };
    Object.defineProperty(PlayBackModel.prototype, "lateStep", {
        get: function () {
            var _a;
            return (_a = this.recordData) === null || _a === void 0 ? void 0 : _a.onStep;
        },
        enumerable: false,
        configurable: true
    });
    PlayBackModel.prototype.appendSnapshoot = function (frameIdx, frame) {
        if (!this.recordData)
            return;
        this.recordData.snapshoot[frameIdx] = frame;
    };
    PlayBackModel.prototype.clean = function () {
        this.recordData = null;
        this.inputFrames = {};
        this.launchFrame = null;
        this.curFrame = 0;
        this.isWatching = false;
    };
    PlayBackModel = __decorate([
        mc.addmodel('playback')
    ], PlayBackModel);
    return PlayBackModel;
}(mc.BaseModel));
exports.default = PlayBackModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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