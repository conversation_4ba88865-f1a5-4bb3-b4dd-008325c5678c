
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/NetHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '86e1cEtafNOr6k9QQyX5XfF', 'NetHelper');
// app/script/common/helper/NetHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.netHelper = void 0;
var ECode_1 = require("../constant/ECode");
var GameHelper_1 = require("./GameHelper");
/**
 * 网络请求
 */
var NetHelper = /** @class */ (function () {
    function NetHelper() {
        this._noviceServer = null;
    }
    Object.defineProperty(NetHelper.prototype, "noviceServer", {
        get: function () { return this._noviceServer || (this._noviceServer = mc.getModel('novice_server')); },
        enumerable: false,
        configurable: true
    });
    // 请求区域信息
    NetHelper.prototype.reqGetAreaInfo = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetAreaInfo(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetAreaInfo', data)];
            });
        });
    };
    NetHelper.prototype.sendLeaveArea = function (data) {
        if (!GameHelper_1.gameHpr.isNoviceMode && GameHelper_1.gameHpr.net.isConnected()) {
            GameHelper_1.gameHpr.net.send('game/HD_LeaveArea', data);
        }
    };
    // 观战区域
    NetHelper.prototype.sendWatchArea = function (index, state) {
        if (!GameHelper_1.gameHpr.isNoviceMode && GameHelper_1.gameHpr.net.isConnected()) {
            GameHelper_1.gameHpr.net.send('game/HD_WatchArea', { index: index, state: state });
        }
    };
    // 获取军队信息
    NetHelper.prototype.reqGetPlayerArmys = function (wait) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetPlayerArmys(0, -1)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetPlayerArmys', {}, wait)];
            });
        });
    };
    // 获取选择军队
    NetHelper.prototype.reqGetSelectArmys = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetPlayerArmys(0, data.index)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetSelectArmys', data, true)];
            });
        });
    };
    NetHelper.prototype.reqMoveAreaPawns = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_MoveAreaPawns(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_MoveAreaPawns', data, true)];
            });
        });
    };
    NetHelper.prototype.reqOpenTreasure = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_OpenTreasure(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_OpenTreasure', data, true)];
            });
        });
    };
    NetHelper.prototype.reqClaimTreasure = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ClaimTreasure(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ClaimTreasure', data, true)];
            });
        });
    };
    NetHelper.prototype.reqAddAreaBuild = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_AddAreaBuild(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_AddAreaBuild', data, true)];
            });
        });
    };
    NetHelper.prototype.reqUpAreaBuild = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_UpAreaBuild(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_UpAreaBuild', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCancelBT = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CancelBT(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CancelBT', data, true)];
            });
        });
    };
    NetHelper.prototype.reqInDoneBt = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_InDoneBt()];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_InDoneBt', {}, true)];
            });
        });
    };
    NetHelper.prototype.reqUseUpScrollUpPawnLv = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_UseUpScrollUpPawnLv(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_UseUpScrollUpPawnLv', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCheckArmyName = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    if (ut.getStringLen(data.name) > 12) {
                        return [2 /*return*/, { err: ECode_1.ecode.TEXT_LEN_LIMIT, data: null }];
                    }
                    else if (GameHelper_1.gameHpr.noviceServer.checkArmyNameEqual(data.name)) {
                        return [2 /*return*/, { err: ECode_1.ecode.NAME_EXIST, data: null }];
                    }
                    return [2 /*return*/, GameHelper_1.gameHpr.net.request('lobby/HD_CheckSensitiveName', data, true)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CheckArmyName', data, true)];
            });
        });
    };
    NetHelper.prototype.reqDrillPawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_DrillPawn(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_DrillPawn', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCancelDrillPawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CancelDrillPawn(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CancelDrillPawn', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCurePawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CureInjuryPawn(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CureInjuryPawn', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCancelCurePawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CancelCurePawn(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CancelCurePawn', data, true)];
            });
        });
    };
    NetHelper.prototype.reqGiveUpInjuryPawn = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GiveupInjuryPawn(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GiveupInjuryPawn', data, true)];
            });
        });
    };
    NetHelper.prototype.sendMoveAreaBuild = function (data) {
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return this.noviceServer.HD_MoveAreaBuild(data);
        }
        GameHelper_1.gameHpr.net.send('game/HD_MoveAreaBuild', data);
    };
    NetHelper.prototype.reqChangePawnArmy = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ChangePawnArmy(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ChangePawnArmy', data, true)];
            });
        });
    };
    NetHelper.prototype.reqChangeConfigPawnEquip = function (data, wait) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ChangeConfigPawnEquip(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ChangeConfigPawnEquip', data, wait)];
            });
        });
    };
    NetHelper.prototype.reqChangePawnAttr = function (data, wait) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ChangePawnAttr(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ChangePawnAttr', data, wait)];
            });
        });
    };
    NetHelper.prototype.reqPawnLving = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_PawnLving(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_PawnLving', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCancelPawnLving = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CancelPawnLving(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CancelPawnLving', data, true)];
            });
        });
    };
    NetHelper.prototype.reqForgeEquip = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ForgeEquip(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ForgeEquip', data, true)];
            });
        });
    };
    NetHelper.prototype.reqRestoreForge = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_RestoreForge(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_RestoreForge', data, true)];
            });
        });
    };
    NetHelper.prototype.HD_InDoneForge = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_InDoneForge()];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_InDoneForge', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCeriStartStudy = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CeriStartStudy(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CeriStartStudy', data, true)];
            });
        });
    };
    NetHelper.prototype.reqStudySelect = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_StudySelect(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_StudySelect', data, true)];
            });
        });
    };
    NetHelper.prototype.reqCeriResetSelect = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_CeriResetSelect(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_CeriResetSelect', data, true)];
            });
        });
    };
    NetHelper.prototype.reqClaimTaskReward = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ClaimTaskReward(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ClaimTaskReward', data, true)];
            });
        });
    };
    NetHelper.prototype.reqPortrayalComp = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_PortrayalComp(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('lobby/HD_PortrayalComp', data, true)];
            });
        });
    };
    NetHelper.prototype.reqWorshipHero = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_WorshipHero(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_WorshipHero', data, true)];
            });
        });
    };
    NetHelper.prototype.reqChangePawnPortrayal = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_ChangePawnPortrayal(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_ChangePawnPortrayal', data, true)];
            });
        });
    };
    NetHelper.prototype.reqGetBattleRecordsList = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetBattleRecordsList()];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetBattleRecordsList')];
            });
        });
    };
    NetHelper.prototype.reqGetBattleRecord = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetBattleRecord(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetBattleRecord', data)];
            });
        });
    };
    NetHelper.prototype.reqGetArmyRecordsByUids = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetArmyRecordsByUids(data)];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetArmyRecordsByUids', data, true)];
            });
        });
    };
    NetHelper.prototype.reqGetPawnDeadLvMap = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    return [2 /*return*/, this.noviceServer.HD_GetPawnDeadLvMap()];
                }
                return [2 /*return*/, GameHelper_1.gameHpr.net.request('game/HD_GetPawnDeadLvMap', data)];
            });
        });
    };
    return NetHelper;
}());
exports.netHelper = new NetHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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