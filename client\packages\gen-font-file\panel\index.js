const fs = require('fire-fs')
const path = require('fire-path')
const Electron = require('electron')
const child_process = require('child_process')

const PACKAGE_NAME = 'gen-font-file'

const CfgUtil = Editor.require('packages://' + PACKAGE_NAME + '/core/CfgUtil.js')
const excelItem = Editor.require('packages://' + PACKAGE_NAME + '/panel/item/excelItem.js')

Editor.Panel.extend({

	template: fs.readFileSync(Editor.url('packages://' + PACKAGE_NAME + '/panel/index.html', 'utf8')) + '',

	$: {
		logTextArea: '#logTextArea',
	},

	ready() {
		const logCtrl = this.$logTextArea
		const logListScrollToBottom = function () {
			setTimeout(function () {
				logCtrl.scrollTop = logCtrl.scrollHeight
			}, 10)
		}
		excelItem.init()
		window['plugin'] = new window.Vue({
			el: this.shadowRoot,
			created() {
				this.initConfig()
			},
			data: {
				logView: '',
				defaultSelectPath: null,
				projectArray: [],

				tempTextFile: { url: '', name: '' },
				tempFontFile: { url: '', name: '' },
				tempFontSize: 40, //字体大小
				tempFontColor: '#FFFFFF', //字体颜色
				tempFontOffset: '', //字体偏移
				tempFontOutline: '', //字体描边
				tempSaveFontPath: null,
			},
			methods: {
				initConfig() {
					CfgUtil.initCfg(function (data) {
						if (data) {
							this.setDefaultSelectPath(data.defaultSelectPath || Editor.Project.path)
							this.projectArray = data.projectArray || []
						}
					}.bind(this))
				},

				saveConfig() {
					CfgUtil.saveCfgByData({
						defaultSelectPath: this.defaultSelectPath,
						projectArray: this.projectArray,
					})
				},

				setDefaultSelectPath(url) {
					this.defaultSelectPath = url;
					if (!fs.existsSync(this.defaultSelectPath)) {
						this.defaultSelectPath = Editor.Project.path
					}
				},

				onBtnClickOpenDefaultSelectPath() {
					if (fs.existsSync(this.defaultSelectPath)) {
						Electron.shell.showItemInFolder(this.defaultSelectPath)
						Electron.shell.beep()
					} else {
						this.addLog('目录不存在：' + this.defaultSelectPath)
					}
				},

				onBtnClickSelectDefaultSelectPath() {
					const res = Editor.Dialog.openFile({
						title: "选择默认选择路径",
						defaultPath: this.defaultSelectPath || Editor.Project.path,
						properties: ['openDirectory'],
					})
					if (res !== -1) {
						const dir = res[0]
						if (dir !== this.defaultSelectPath) {
							this.setDefaultSelectPath(dir)
							this.saveConfig()
						}
					}
				},

				onBtnClickSelectTempTextFilePath() {
					const res = Editor.Dialog.openFile({
						title: "选择文本文件",
						defaultPath: this.defaultSelectPath,
						properties: ['openFile'],
					})
					if (res !== -1) {
						const dir = res[0]
						if (dir !== this.tempTextFile.url) {
							this.tempTextFile.url = dir
							this.tempTextFile.name = this.stringFormator(dir.replace(path.extname(dir), ''), 8)
						}
					}
				},

				onBtnClickSelectTempFontFilePath() {
					const res = Editor.Dialog.openFile({
						title: "选择字体文件",
						defaultPath: this.defaultSelectPath,
						properties: ['openFile'],
					})
					if (res !== -1) {
						const dir = res[0]
						if (dir !== this.tempFontFile.url) {
							this.tempFontFile.url = dir
							this.tempFontFile.name = this.stringFormator(dir.replace(path.extname(dir), ''), 8)
						}
					}
				},

				onBtnClickSelectTempSaveFontPath() {
					const res = Editor.Dialog.openFile({
						title: "选择输出字体文件的根目录",
						defaultPath: this.tempSaveFontPath || Editor.Project.path,
						properties: ['openDirectory'],
					})
					if (res !== -1) {
						const dir = res[0]
						if (dir !== this.tempSaveFontPath) {
							this.tempSaveFontPath = dir
						}
					}
				},

				onBtnClickSelectAll(event) {
					const b = event.currentTarget.value
					this.projectArray.forEach(m => m.select = b)
					this.saveConfig()
				},

				onBtnClickAdd() {
					if (!this.tempTextFile.url) {
						return this.addLog('请选择文本文件')
					} else if (!this.tempFontFile.url) {
						return this.addLog('请选择字体文件')
					} else if (!this.tempSaveFontPath) {
						return this.addLog('请选择输出位置')
					}
					const item = {
						textUrl: this.tempTextFile.url,
						textName: this.tempTextFile.name, //文本
						fontUrl: this.tempFontFile.url,
						fontName: this.tempFontFile.name, //字体
						fontSize: this.tempFontSize || 40,
						fontColor: this.tempFontColor || '#FFFFFF',
						offset: this.tempFontOffset || '0,0',
						outline: this.tempFontOutline || '-',
						savePath: this.tempSaveFontPath,
						savePathName: this.stringFormator(this.tempSaveFontPath, 30),
						select: true,
					}
					this.projectArray.push(item)
					this.saveConfig()
					this.addLog('添加一条 text=' + item.textName + ', font=' + item.fontName + ', fontSize=' + item.fontSize + ', offset=' + item.offset + ', outline=' + item.outline)
					this.tempTextFile.url = ''
					this.tempTextFile.name = ''
					this.tempFontFile.url = ''
					this.tempFontFile.name = ''
					this.tempFontOffset = ''
					this.tempFontOutline = ''
				},

				onBtnClickRemove() {
					for (let i = this.projectArray.length - 1; i >= 0; i--) {
						const item = this.projectArray[i]
						if (item.select) {
							this.projectArray.splice(i, 1)
							this.addLog('删除一条 text=' + item.textName + ', font=' + item.fontName + ', fontSize=' + item.fontSize + ', offset=' + item.offset + ', outline=' + item.outline)
						}
					}
					this.saveConfig()
				},

				onBtnClickGen() {
					this.saveConfig()
					this.addLog('输出中请等待...')
					setTimeout(() => this.do(), 10)
				},

				do() {
					const now = Date.now()
					const BMF_ROOT_PATH = Editor.url('packages://' + PACKAGE_NAME + '/bmf/', 'utf8')
					const needUpdateDir = {}
					for (let i = this.projectArray.length - 1; i >= 0; i--) {
						const item = this.projectArray[i]
						if (!item.select) {
							continue
						}
						const inputPath = item.textUrl
						const fontPath = item.fontUrl
						const outPath = item.savePath
						const fontSize = item.fontSize
						const fontColor = item.fontColor || '#FFFFFF'
						let cmd = `cd ${BMF_ROOT_PATH};node index.js --inputPath ${inputPath} --outPath ${outPath} --fontPath ${fontPath} --fontSize ${fontSize} --fontColor ${fontColor}`
						if (item.offset) {
							const [xOffset, yOffset] = item.offset.split(',')
							cmd += ` --xOffset ${xOffset.replace('-', '_') || 0} --yOffset ${yOffset.replace('-', '_') || 0}`
						}
						if (item.outline && item.outline !== '-') {
							const [strokeWidth, strokeColor] = item.outline.split(',')
							cmd += ` --useStroke --strokeWidth ${strokeWidth} --strokeColor "${strokeColor}"`
						}
						try {
							// 使用 -Command 参数和 - 分隔符来传递脚本块
							const command = [
								'powershell.exe',
								'-NoProfile',        // 不加载个人配置文件，加快启动
								'-ExecutionPolicy',
								'Bypass',           // 绕过执行策略
								'-Command',
								cmd
							];
							child_process.execSync(command.join(' '), { encoding: 'utf-8' })
						}
						catch (error) {
							this.addLog(error.message)
							this.addLog(error.status)
						}
						const textName = path.basename(inputPath).split('.')[0]
						const op = outPath.replace(/\\/g, '/')
						needUpdateDir[op] = true
						this.addLog('输出文件: ' + op + '/' + textName + '.png, ' + op + '/' + textName + '.fnt')
					}
					for (let key in needUpdateDir) {
						const url = key.replace(Editor.Project.path.replace(/\\/g, '/'), 'db:/')
						Editor.assetdb.refresh(url, (err) => { })
					}
					this.addLog('全部输出完成 ' + ((Date.now() - now) * 0.001).toFixed(3) + 's')
				},

				addLog(str) {
					// const time = new Date()
					this.logView += /* '[' + time.toLocaleString() + ']: ' +  */str + '\n'
					logListScrollToBottom()
				},

				stringFormator(str, max, extra = '...') {
					if (!str) {
						return ''
					}
					str = String(str)
					if (str.length <= max) {
						return str
					}
					let cnt = 0, len = 0
					max = max * 2
					for (let i = str.length - 1; i >= 0; i--) {
						const val = str.charCodeAt(i) > 255 ? 2 : 1
						if (len + val <= max - 2) {
							cnt += 1
						}
						len += val
						if (len > max) {
							break
						}
					}
					return len <= max ? str : extra + str.substr(str.length - cnt)
				},
			},
		})
	},
})