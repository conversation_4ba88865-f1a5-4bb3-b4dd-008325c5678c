
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStar4.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '097caGMA9tH17TvEmI4PqxE', 'AStar4');
// app/script/common/astar/AStar4.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var ANode_1 = require("./ANode");
var AStarConfig_1 = require("./AStarConfig");
/**
 * A星寻路
 */
var AStar4 = /** @class */ (function () {
    function AStar4() {
        this.opened = [];
        this.closed = {};
        this.checkHasPass = null; //检测方法
        this._temp_vec2 = cc.v2();
    }
    AStar4.prototype.init = function (checkHasPass) {
        this.checkHasPass = checkHasPass;
        return this;
    };
    // 新建一个节点
    AStar4.prototype.newNode = function (x, y) {
        return new ANode_1.default().init(x, y);
    };
    // 寻路
    AStar4.prototype.search = function (start, end, maxSearchCount, dir, camp) {
        if (start.equals(end)) {
            return [];
        }
        var dirPoints = AStarConfig_1.DIR_POINTS_4_TO_SEARCH[dir] || AStarConfig_1.DIR_POINTS_4_TO_SEARCH[2];
        var dirCount = dirPoints.length;
        this.opened.length = 0;
        this.closed = {};
        // 把第一个点装进开起列表
        this.opened.push(this.newNode(start.x, start.y));
        // 开始搜索
        var node = null;
        while (this.opened.length > 0) {
            node = this.opened.shift();
            if (node.G >= maxSearchCount || node.point.equals(end)) {
                break;
            }
            this.closed[node.uid] = true;
            var _loop_1 = function (i) {
                var d = dirPoints[i];
                var x = node.x + d.point.x;
                var y = node.y + d.point.y;
                if (!end.equals2(x, y) || !this_1.checkHasPass(x, y, camp) || this_1.closed[x + '_' + y]) {
                    return "continue";
                }
                // 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
                var it = this_1.opened.find(function (m) { return m.point.equals2(x, y); });
                if (!it) {
                    var temp = this_1.newNode(x, y);
                    temp.H = Math.floor(end.sub(temp.point, this_1._temp_vec2).mag());
                    temp.updateParent(node, d.tag);
                    this_1.opened.push(temp);
                }
                else if (node.G + d.tag < it.G) {
                    it.updateParent(node, d.tag);
                }
            };
            var this_1 = this;
            // 找周围的是否可以移动
            for (var i = 0; i < dirCount; i++) {
                _loop_1(i);
            }
            // 排序
            this.opened.sort(function (a, b) { return a.F - b.F; });
        }
        return this.genPoints(node, start);
    };
    AStar4.prototype.genPoints = function (node, start) {
        var points = [];
        while (node.parent !== null) {
            points.push(node.point);
            node = node.parent;
        }
        points.push(start);
        points.reverse();
        this.opened.length = 0;
        this.closed = {};
        return points;
    };
    return AStar4;
}());
exports.default = AStar4;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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