
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/FrameAnimationCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '30ce3axMIRGrpg5E+kPBhyQ', 'FrameAnimationCmpt');
// app/script/view/cmpt/FrameAnimationCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var FrameAnimConf_1 = require("../../common/constant/FrameAnimConf");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 帧动画
var FrameAnimationCmpt = /** @class */ (function (_super) {
    __extends(FrameAnimationCmpt, _super);
    function FrameAnimationCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sfs = [];
        _this._sprite = null; //动画精灵
        _this.frames = {};
        _this.currFrameIndex = 0; //当前帧
        _this.loading = false;
        _this.conf = null;
        _this.anim = null; //当前播放的动画配置
        _this.playInterval = 0;
        _this.playElapsed = 0;
        _this.playFrameIndex = 0;
        _this.playCallback = null; //播放回调
        _this.delayPlayAnim = 0; //延迟播放
        return _this;
    }
    Object.defineProperty(FrameAnimationCmpt.prototype, "sprite", {
        get: function () {
            if (!this._sprite) {
                this._sprite = this.getComponent(cc.Sprite);
            }
            return this._sprite;
        },
        enumerable: false,
        configurable: true
    });
    FrameAnimationCmpt.prototype.init = function (id, key) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.clean();
                        this.conf = FrameAnimConf_1.FRAME_ANIM_CONF[id];
                        if (!this.conf) {
                            cc.error('FrameAnimationCmpt init error. id: ' + id);
                        }
                        this.sprite.spriteFrame = null;
                        this.sfs.forEach(function (m) { return _this.frames[m.name.split('_').last()] = m; });
                        return [4 /*yield*/, this.loadFrames(this.getAnimConfAllFrames(), key)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    FrameAnimationCmpt.prototype.clean = function () {
        this.conf = null;
        this.frames = {};
        this.anim = null;
        this.playCallback = null;
        this.currFrameIndex = 0;
        this.delayPlayAnim = 0;
    };
    FrameAnimationCmpt.prototype.onDestroy = function () {
        this.clean();
    };
    // 获取所有动画需要的帧
    FrameAnimationCmpt.prototype.getAnimConfAllFrames = function () {
        var _this = this;
        var _a;
        var frames = [], obj = {};
        (_a = this.conf) === null || _a === void 0 ? void 0 : _a.anims.forEach(function (m) { return m.frameIndexs.forEach(function (frame) {
            if (frame !== '00' && !obj[frame] && !_this.frames[frame]) {
                obj[frame] = true;
                frames.push(frame);
            }
        }); });
        return frames;
    };
    // 加载所有帧
    FrameAnimationCmpt.prototype.loadFrames = function (frames, key) {
        return __awaiter(this, void 0, void 0, function () {
            var url, sfs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.loading || frames.length === 0 || !this.conf) {
                            return [2 /*return*/];
                        }
                        this.loading = true;
                        url = this.conf.url;
                        return [4 /*yield*/, Promise.all(frames.map(function (m) { return assetsMgr.loadTempRes(url + m, cc.SpriteFrame, key); }))];
                    case 1:
                        sfs = _a.sent();
                        this.loading = false;
                        if (this.isValid) {
                            sfs.forEach(function (m) { return _this.frames[m.name.split('_').last()] = m; });
                            this.setFrame(this.currFrameIndex);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    FrameAnimationCmpt.prototype.update = function (dt) {
        this.updateFrame(dt * 1000);
    };
    // 每帧刷新
    FrameAnimationCmpt.prototype.updateFrame = function (dt) {
        // 刷新精灵动画
        if (!this.loading && this.anim) {
            if (this.delayPlayAnim > 0) {
                this.delayPlayAnim -= dt;
                if (this.delayPlayAnim <= 0) {
                    this.node.opacity = 255;
                }
            }
            else {
                this.playElapsed += dt;
                if (this.playElapsed >= this.playInterval) {
                    this.playElapsed -= this.playInterval;
                    this.setFrame(this.playFrameIndex);
                    if (this.playFrameIndex < this.anim.frameIndexs.length - 1) {
                        this.playFrameIndex += 1;
                    }
                    else if (this.anim.loop) {
                        this.playFrameIndex = 0;
                    }
                    else {
                        this.anim = null;
                        this.playCallback && this.playCallback();
                    }
                }
            }
        }
    };
    // 播放动画
    FrameAnimationCmpt.prototype.play = function (name, cb, startTime) {
        var _a;
        var anim = this.anim = (_a = this.conf) === null || _a === void 0 ? void 0 : _a.anims.find(function (m) { return m.name === name; });
        if (!anim) {
            return cb && cb();
        }
        this.playCallback = cb;
        this.playInterval = anim.interval || 1;
        startTime = startTime || 0;
        var index = Math.floor(startTime / this.playInterval);
        this.playFrameIndex = index + 1;
        this.playElapsed = startTime % this.playInterval;
        this.setFrame(index);
    };
    FrameAnimationCmpt.prototype.delayPlay = function (delay, name, cb, startTime) {
        if (delay > 0) {
            this.delayPlayAnim = delay;
        }
        else {
            this.node.opacity = 255;
        }
        this.play(name, cb, startTime);
    };
    FrameAnimationCmpt.prototype.stop = function () {
        this.setFrame(0);
        this.anim = null;
        this.playCallback && this.playCallback();
        this.playCallback = null;
    };
    // 设置一帧
    FrameAnimationCmpt.prototype.setFrame = function (index) {
        this.currFrameIndex = index;
        if (this.anim && !this.loading) {
            var name = this.anim.frameIndexs[index];
            if (name) {
                this.sprite.spriteFrame = this.frames[name];
            }
        }
    };
    Object.defineProperty(FrameAnimationCmpt.prototype, "playAnimName", {
        get: function () {
            var _a;
            return ((_a = this.anim) === null || _a === void 0 ? void 0 : _a.name) || '';
        },
        enumerable: false,
        configurable: true
    });
    __decorate([
        property([cc.SpriteFrame])
    ], FrameAnimationCmpt.prototype, "sfs", void 0);
    FrameAnimationCmpt = __decorate([
        ccclass
    ], FrameAnimationCmpt);
    return FrameAnimationCmpt;
}(cc.Component));
exports.default = FrameAnimationCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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