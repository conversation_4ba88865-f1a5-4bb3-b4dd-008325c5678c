
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStarNode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '01f5cgiK89DxaDTUNwGv5cm', 'AStarNode');
// app/script/common/astar/AStarNode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个节点
var AStarNode = /** @class */ (function () {
    function AStarNode() {
        this.uid = '0_0';
        this.point = cc.v2(0, 0);
        this.parent = null;
        this.dir = cc.v2(); //相对上一个格子的方向
        this.F = 0;
        this.G = 0;
        this.H = 0;
        this.T = 0; //拐弯次数
        this.P = 0; //是否有人
        this.SP = 0; //总人数
        this.CP = 0; //连续的人数
    }
    Object.defineProperty(AStarNode.prototype, "x", {
        get: function () { return this.point.x; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AStarNode.prototype, "y", {
        get: function () { return this.point.y; },
        enumerable: false,
        configurable: true
    });
    AStarNode.prototype.ID = function () { return this.uid; };
    AStarNode.prototype.init = function (x, y) {
        this.uid = x + '_' + y;
        this.point.set2(x, y);
        this.parent = null;
        this.F = 0;
        this.G = 0;
        this.H = 0;
        this.T = 0;
        this.P = 0;
        this.CP = 0;
        return this;
    };
    AStarNode.prototype.has = function (x, y) {
        return this.point.x === x && this.point.y === y;
    };
    AStarNode.prototype.updateParent = function (node, tag, p) {
        this.parent = node;
        this.point.sub(node.point, this.dir);
        this.T = node.T;
        if (!node.dir.equals2(0, 0) && !node.dir.equals(this.dir)) {
            this.T += 1; //说明转弯了
        }
        if (p > 0) {
            this.CP += p;
        }
        else {
            this.CP = 0;
        }
        this.P = p;
        this.SP = node.SP + p;
        this.G = node.G + tag;
        this.F = this.H + this.G + this.T + this.SP;
    };
    return AStarNode;
}());
exports.default = AStarNode;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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