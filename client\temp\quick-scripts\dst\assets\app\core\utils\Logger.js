
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/utils/Logger.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6baadjM1IhKz4X2x37dFl1D', 'Logger');
// app/core/utils/Logger.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CoreEventType_1 = require("../event/CoreEventType");
var Logger = /** @class */ (function () {
    function Logger() {
        this.open = true; //是否打开
        this.openPrint = true;
    }
    Logger.prototype.info = function (msg) {
        var subst = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            subst[_i - 1] = arguments[_i];
        }
        if (!this.open) {
            return;
        }
        var text = this.wrapLogger(msg, subst);
        if (!cc.sys.isMobile) {
            var log = console.log || cc.log;
            log.call(this, text);
        }
        else {
            console.log(text);
        }
        // eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'info', text)
    };
    Logger.prototype.error = function (msg) {
        var subst = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            subst[_i - 1] = arguments[_i];
        }
        if (!this.open) {
            return;
        }
        var text = this.wrapLogger(msg, subst);
        if (!cc.sys.isMobile) {
            var error = console.error || cc.error;
            error.call(this, text);
        }
        else {
            console.error(text);
        }
        // eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'error', text)
    };
    Logger.prototype.print = function (msg) {
        var subst = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            subst[_i - 1] = arguments[_i];
        }
        this.info.apply(this, __spread([msg], subst));
        if (this.openPrint) {
            var text = this.wrapLogger(msg, subst);
            eventCenter.emit(CoreEventType_1.default.MVC_LOGGER_PRINT, 'print', text);
        }
    };
    Logger.prototype.wrapLogger = function (msg, subst) {
        var _this = this;
        return '[' + ut.dateFormat('h:mm:ss') + '] ' + this.formatter(msg) + ' ' + subst.join2(function (m) { return _this.formatter(m); }, ' ');
    };
    Logger.prototype.formatter = function (val) {
        var _this = this;
        if (val === null) {
            return 'null';
        }
        else if (val === undefined) {
            return 'undefined';
        }
        else if (Array.isArray(val)) {
            return '[' + val.join2(function (m) { return _this.formatter(m); }, ',') + ']';
        }
        else if (typeof (val) === 'object') {
            return this.warpObject(val);
        }
        else {
            return String(val);
        }
    };
    Logger.prototype.warpObject = function (obj) {
        try {
            if (obj.__classname__) {
                return obj.__classname__ + " { name: " + obj.name + " }";
            }
            return JSON.stringify(obj);
        }
        catch (error) {
            return '无法解析';
        }
    };
    return Logger;
}());
window['logger'] = new Logger();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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