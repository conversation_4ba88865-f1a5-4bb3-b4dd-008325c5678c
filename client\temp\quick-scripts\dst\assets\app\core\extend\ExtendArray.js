
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendArray.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3eff8OyVlxED5YHzgpf6MC+', 'ExtendArray');
// app/core/extend/ExtendArray.ts

if (!Array.prototype.remove) {
    Object.defineProperty(Array.prototype, 'remove', {
        value: function (key, value) {
            var i = value !== undefined ? this.findIndex(function (m) { return !!m && m[key] === value; }) : this.indexOf(key);
            return i === -1 ? undefined : this.splice(i, 1)[0];
        },
        enumerable: false
    });
}
if (!Array.prototype.delete) {
    Object.defineProperty(Array.prototype, 'delete', {
        value: function (cb) {
            var arr = [];
            for (var i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    arr.push(this.splice(i, 1)[0]);
                }
            }
            return arr;
        },
        enumerable: false
    });
}
if (!Array.prototype.random) {
    Object.defineProperty(Array.prototype, 'random', {
        value: function () {
            if (this.length === 0) {
                return undefined;
            }
            var i = Math.floor(Math.random() * this.length);
            return this[i];
        },
        enumerable: false
    });
}
if (!Array.prototype.randomRemove) {
    Object.defineProperty(Array.prototype, 'randomRemove', {
        value: function () {
            if (this.length === 0) {
                return undefined;
            }
            var i = Math.floor(Math.random() * this.length);
            return this.splice(i, 1)[0];
        },
        enumerable: false
    });
}
if (!Array.prototype.has) {
    Object.defineProperty(Array.prototype, 'has', {
        value: function (key, value) {
            return value !== undefined ? this.some(function (m) { return m[key] === value; }) : this.indexOf(key) !== -1;
        },
        enumerable: false
    });
}
if (!Array.prototype.append) {
    Object.defineProperty(Array.prototype, 'append', {
        value: function (val) {
            this.push(val);
            return this;
        },
        enumerable: false
    });
}
if (!Array.prototype.add) {
    Object.defineProperty(Array.prototype, 'add', {
        value: function (val) {
            this.push(val);
            return val;
        },
        enumerable: false
    });
}
if (!Array.prototype.last) {
    Object.defineProperty(Array.prototype, 'last', {
        value: function () {
            return this[this.length - 1];
        },
        enumerable: false
    });
}
// 拼接数组 对象
if (!Array.prototype.join2) {
    Object.defineProperty(Array.prototype, 'join2', {
        value: function (cb, separator) {
            if (separator === void 0) { separator = ','; }
            return this.map(function (value, index) { return cb(value, index) || ''; }).join(separator);
        },
        enumerable: false
    });
}
if (!Array.prototype.pushArr) {
    Object.defineProperty(Array.prototype, 'pushArr', {
        value: function (arr) {
            if (!arr || arr.length === 0) {
                return this.length;
            }
            for (var i = 0, l = arr.length; i < l; i++) {
                this.push(arr[i]);
            }
            return this.length;
        },
        enumerable: false
    });
}
if (!Array.prototype.pushNoHas) {
    Object.defineProperty(Array.prototype, 'pushNoHas', {
        value: function (data, key, value) {
            if (key === undefined) {
                key = data;
            }
            if (!this.has(key, value)) {
                this.push(data);
                return true;
            }
            return false;
        },
        enumerable: false
    });
}
if (!Array.prototype.set) {
    Object.defineProperty(Array.prototype, 'set', {
        value: function (arr) {
            this.length = 0;
            return Array.prototype.push.apply(this, arr);
        },
        enumerable: false
    });
}
if (!Array.prototype.findLastIndex) {
    Object.defineProperty(Array.prototype, 'findLastIndex', {
        value: function (cb) {
            for (var i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    return i;
                }
            }
            return -1;
        },
        enumerable: false
    });
}
if (!Array.prototype.findLast) {
    Object.defineProperty(Array.prototype, 'findLast', {
        value: function (cb) {
            for (var i = this.length - 1; i >= 0; i--) {
                var val = this[i];
                if (cb(val, i)) {
                    return val;
                }
            }
            return null;
        },
        enumerable: false
    });
}
if (!Array.prototype.shuffle) {
    Object.defineProperty(Array.prototype, 'shuffle', {
        value: function () {
            return this.sort(function () { return Math.random() < 0.5 ? 1 : -1; });
        },
        enumerable: false
    });
}
if (!Object.values) {
    Object.values = function (obj) {
        if (obj !== Object(obj)) {
            throw new TypeError('Object.values called on a non-object');
        }
        var val = [];
        for (var key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                val.push(obj[key]);
            }
        }
        return val;
    };
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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