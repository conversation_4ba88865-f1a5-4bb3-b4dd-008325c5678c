
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/event/CoreEventType.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c7974bEysVJJoSwzvizrpPA', 'CoreEventType');
// app/core/event/CoreEventType.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    MVC_LOGGER_PRINT: 'MVC_LOGGER_PRINT_101',
    MVC_ERROR_MSG: 'MVC_ERROR_MSG_102',
    LANGUAGE_CHANGED: 'LANGUAGE_CHANGED_103',
    LOADING_WAIT_BEGIN: 'LOADING_WAIT_BEGIN_501',
    LOADING_WAIT_END: 'LOADING_WAIT_END_502',
    OPEN_PNL: 'OPEN_PNL_201',
    HIDE_PNL: 'HIDE_PNL_202',
    HIDE_ALL_PNL: 'HIDE_ALL_PNL_203',
    CLOSE_PNL: 'CLOSE_PNL_204',
    CLOSE_ALL_PNL: 'CLOSE_ALL_PNL_205',
    CLOSE_MOD_PNL: 'CLOSE_MOD_PNL_206',
    PRELOAD_PNL: 'PRELOAD_PNL_207',
    LOAD_BEGIN_PNL: 'LOAD_BEGIN_PNL_208',
    LOAD_END_PNL: 'LOAD_END_PNL_209',
    PNL_ENTER: 'PNL_ENTER_210',
    PNL_LEAVE: 'PNL_LEAVE_211',
    PNL_ENTER_PLAY_DONE: 'PNL_ENTER_PLAY_DONE_215',
    CLEAN_ALL_UNUSED: 'CLEAN_ALL_UNUSED_212',
    GIVEUP_LOAD_PNL: 'GIVEUP_LOAD_PNL_213',
    CLEAN_LOAD_PNL_QUEUE: 'CLEAN_LOAD_PNL_QUEUE_214',
    GOTO_WIND: 'GOTO_WIND_301',
    PRELOAD_WIND: 'PRELOAD_WIND_302',
    WIND_ENTER: 'WIND_ENTER_303',
    CLEAN_CACHE_WIND: 'CLEAN_CACHE_WIND_304',
    LOAD_BEGIN_WIND: 'LOAD_BEGIN_WIND_305',
    LOAD_END_WIND: 'LOAD_END_WIND_306',
    READY_BEGIN_WIND: 'READY_BEGIN_WIND_307',
    READY_END_WIND: 'READY_END_WIND_308',
    LOAD_NOTICE: 'LOAD_NOTICE_402',
    LOAD_ALL_NOTICE: 'LOAD_ALL_NOTICE_401',
    NOTICE_ENTER_PLAY_DONE: 'NOTICE_ENTER_PLAY_DONE_403',
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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