
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/MapCellObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eb170ovYElF7IwOR7FRq3FH', 'MapCellObj');
// app/script/model/main/MapCellObj.ts

"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var CityObj_1 = require("./CityObj");
// 一个地图单元格
var MapCellObj = /** @class */ (function () {
    function MapCellObj() {
        this.index = 0;
        this.point = cc.v2();
        this.position = cc.v2(); //实际位置
        this.owner = ''; //拥有者
        this.json = null;
        this.cityId = 0;
        this.city = null;
        this.cityLandType = Enums_1.LandType.NONE;
        this.borderLines = []; //边框线 就是这个格子显示哪些线
        this.tempProtectOwner = undefined; //临时的保护模式拥有者
    }
    MapCellObj.prototype.init = function (index, land) {
        this.index = index;
        this.point.set(MapHelper_1.mapHelper.indexToPoint(index));
        MapHelper_1.mapHelper.getPixelByPoint(this.point, this.position);
        this.json = assetsMgr.getJsonData('land', land);
        this.cityLandType = this.landType;
        return this;
    };
    // 更新一个地块的信息
    MapCellObj.prototype.updateInfo = function (data) {
        this.owner = data.owner || '';
        // 建筑
        this.updateCity(data.cityId || 0);
        return this;
    };
    // 刷新城市
    MapCellObj.prototype.updateCity = function (cityId, cityName) {
        this.cityId = cityId || 0;
        if (this.cityId > 0) {
            this.city = new CityObj_1.default().init(this.index, this.cityId, this.point);
            this.city.setName(cityName);
        }
        else {
            this.city = null;
        }
    };
    Object.defineProperty(MapCellObj.prototype, "actIndex", {
        // 实际位置
        get: function () { var _a, _b; return (_b = (_a = this.city) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : this.index; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "actPoint", {
        // 实际位置
        get: function () { var _a, _b; return (_b = (_a = this.city) === null || _a === void 0 ? void 0 : _a.point) !== null && _b !== void 0 ? _b : this.point; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "actPosition", {
        // 获取实际位置
        get: function () { var _a, _b; return (_b = (_a = this.city) === null || _a === void 0 ? void 0 : _a.position) !== null && _b !== void 0 ? _b : this.position; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "actCityPoint", {
        // 获取实际位置点
        get: function () { var _a, _b; return (_b = (_a = this.city) === null || _a === void 0 ? void 0 : _a.actPoint) !== null && _b !== void 0 ? _b : this.point; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "landId", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "landType", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "landLv", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.lv; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(MapCellObj.prototype, "icon", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.icon; },
        enumerable: false,
        configurable: true
    });
    // 获取当前地块大小
    MapCellObj.prototype.getSize = function () { var _a; return ((_a = this.city) === null || _a === void 0 ? void 0 : _a.size) || Constant_1.DEFAULT_CITY_SIZE; };
    // 获取地块名字
    MapCellObj.prototype.getName = function () { var _a; return ((_a = this.city) === null || _a === void 0 ? void 0 : _a.name) || this.json.name; };
    // 获取基础名字
    MapCellObj.prototype.getBaseName = function () { var _a; return ((_a = this.city) === null || _a === void 0 ? void 0 : _a.name) || ('ui.land_base_' + this.landType); };
    // 占地面积
    MapCellObj.prototype.getAcreage = function () { return this.getSize().SelfMul(); };
    // 获取对应区域信息
    MapCellObj.prototype.getArea = function () { return GameHelper_1.gameHpr.areaCenter.getArea(this.actIndex); };
    // 获取区域最大军队数量
    MapCellObj.prototype.getMaxArmyCount = function () {
        var _a;
        if (this.cityId > 0) {
            return ((_a = assetsMgr.getJsonData('city', this.cityId)) === null || _a === void 0 ? void 0 : _a.max_army) || Constant_1.DEFAULT_MAX_ARMY_COUNT;
        }
        return Constant_1.DEFAULT_MAX_ARMY_COUNT;
    };
    // 获取城市视图id
    MapCellObj.prototype.getCityViewId = function () {
        if (!this.cityId || !this.owner) {
            return this.cityId;
        }
        var id = GameHelper_1.gameHpr.world.getCitySkinByIndex(this.index);
        if (id && Math.floor(id / 1000) === this.cityId) {
            return id;
        }
        return this.cityId;
    };
    // 获取右下角位置
    MapCellObj.prototype.getRightPosition = function (out) {
        out = out || cc.v2();
        if (this.getAcreage() === 4) {
            return out.set2(this.position.x + Constant_1.TILE_SIZE, this.position.y);
        }
        return out.set(this.position);
    };
    // 设置城市依赖
    MapCellObj.prototype.setCityDepend = function (city) {
        this.city = city;
        this.cityId = -((city === null || city === void 0 ? void 0 : city.id) || 0);
    };
    // 获取边界线颜色
    MapCellObj.prototype.getBorderLineColor = function () {
        if (this.isOwn()) {
            return '#36844D';
        }
        else if (this.isOneAlliance()) {
            return '#4B8AA8';
        }
        return '#B63B43';
    };
    // 获取归属类型 1.自己 2.盟友 3.敌方
    MapCellObj.prototype.getOwnType = function () {
        if (this.isOwn()) {
            return 1;
        }
        else if (this.isOneAlliance()) {
            return 2;
        }
        return 3;
    };
    // 获取资源配置
    MapCellObj.prototype.getResJson = function () {
        if (this.city && !this.isMainCity() && !this.isFort()) {
            var id = this.landType * 100 + this.landLv;
            return assetsMgr.getJsonData('cityResAttr', this.city.id * 1000 + id);
        }
        return this.json;
    };
    // 获取基础资源
    MapCellObj.prototype.getBaseRes = function () {
        var _this = this;
        var arr = [];
        Constant_1.CELL_RES_FIELDS.forEach(function (m) {
            var val = _this.json[m];
            if (val) {
                arr.push({ type: Constant_1.RES_FIELDS_CTYPE[m], val: val });
            }
        });
        return arr;
    };
    // 是否不可攻占
    MapCellObj.prototype.isCanOccupy = function () {
        return !!this.json.occupy;
    };
    // 是否有资源
    MapCellObj.prototype.isHasRes = function () {
        return !this.city || (!this.isMainCity() && !this.isFort() && !this.isAncient());
    };
    // 是否主城
    MapCellObj.prototype.isMainCity = function () {
        var _a;
        return ((_a = this.city) === null || _a === void 0 ? void 0 : _a.id) === Constant_1.CITY_MAIN_NID;
    };
    // 是否要塞
    MapCellObj.prototype.isFort = function () {
        var _a;
        return ((_a = this.city) === null || _a === void 0 ? void 0 : _a.id) === Constant_1.CITY_FORT_NID;
    };
    // 是否遗迹
    MapCellObj.prototype.isAncient = function () {
        return !!this.city && this.city.id >= Constant_1.CITY_CHANGAN_ID && this.city.id <= Constant_1.CITY_LUOYANG_ID;
    };
    // 是否可以加速行军
    MapCellObj.prototype.isCanUpSpeedMarchCity = function () {
        return !!this.city && (this.isMainCity() || this.isFort() || this.isAncient());
    };
    // 是否可以恢复士兵血量
    MapCellObj.prototype.isRecoverPawnHP = function () {
        return this.isCanUpSpeedMarchCity();
    };
    // 是否自己的
    MapCellObj.prototype.isOwn = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否和自己一个联盟
    MapCellObj.prototype.isOneAlliance = function () {
        return this.isOwn() || GameHelper_1.gameHpr.isOneAlliance(this.owner);
    };
    // 是否免战
    MapCellObj.prototype.isAvoidWar = function () {
        return !!GameHelper_1.gameHpr.world.getAvoidWarDistMap()[this.actIndex];
    };
    // 获取免战时间和类型
    MapCellObj.prototype.getAvoidWarTypeAndTime = function () {
        var _a, _b;
        if (this.cityId < 0) {
            return [0, 0];
        }
        else if (this.cityId !== Constant_1.CITY_MAIN_NID) { //主城不看保护时间
            var time2 = this.getProtectAvoidTime();
            if (time2 > 0) {
                return [2, time2]; //只要有保护模式 就显示保护模式的剩余时间
            }
        }
        var time1 = (_b = (_a = GameHelper_1.gameHpr.world.getAvoidWarDistMap()[this.actIndex]) === null || _a === void 0 ? void 0 : _a.getSurplusTime()) !== null && _b !== void 0 ? _b : 0;
        if (time1 > 0) {
            return [1, time1];
        }
        return [0, 0];
    };
    // 是否可以显示免战
    MapCellObj.prototype.isCanShowAvoidWar = function () {
        return this.getAvoidWarTypeAndTime()[0] === 1;
    };
    // 获取保护模式下的时间
    MapCellObj.prototype.getProtectAvoidTime = function () {
        return GameHelper_1.gameHpr.getPlayerProtectAvoidTime(this.getProtectOwner());
    };
    // 获取保护模式下的拥有者
    MapCellObj.prototype.getProtectOwner = function () {
        var _a;
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return '';
        }
        else if (GameHelper_1.gameHpr.world.getSeasonType()) {
            return ''; //只有春天才有保护模式
        }
        else if (this.tempProtectOwner === undefined) {
            // 获取6格内的主城信息
            this.tempProtectOwner = ((_a = MapHelper_1.mapHelper.getManhattanMainCityInfo(this.actPoint, 6)) === null || _a === void 0 ? void 0 : _a.owner) || '';
        }
        return this.tempProtectOwner;
    };
    // 检测是否保护模式 不可攻击
    MapCellObj.prototype.checkAttackByProtect = function () {
        var uid = this.getProtectOwner();
        return !!uid && uid !== GameHelper_1.gameHpr.getUid();
    };
    // 是否在修建中
    MapCellObj.prototype.isBTCitying = function () {
        return !!GameHelper_1.gameHpr.world.getBTCityQueueMap()[this.actIndex];
    };
    // 获取修建信息
    MapCellObj.prototype.getBTCityInfo = function () {
        return GameHelper_1.gameHpr.world.getBTCityQueueMap()[this.actIndex];
    };
    // 是否屯田中
    MapCellObj.prototype.isTondening = function () {
        return !!GameHelper_1.gameHpr.world.getTondenQueueMap()[this.actIndex];
    };
    // 获取一个地块所拥有的所有地块位置
    MapCellObj.prototype.getOwnPoints = function () {
        if (this.getAcreage() <= 1) {
            return [this.actPoint];
        }
        return MapHelper_1.mapHelper.genPointsBySize(this.getSize(), this.actPoint);
    };
    // 获取地块对应attr
    MapCellObj.prototype.getLandAttr = function () {
        var level = GameHelper_1.gameHpr.getLandAttrLvByDis(GameHelper_1.gameHpr.getSelfToMapCellDis(this.index), this.landLv);
        return assetsMgr.getJsonData('landAttr', this.landLv * 1000 + level);
    };
    // 获取地块难度
    MapCellObj.prototype.getLandDifficultyLv = function (json) {
        // if (this.isAncient()) {
        //     return [4, 10] //地狱10
        // }
        json = json || this.getLandAttr();
        var uiLv = (json === null || json === void 0 ? void 0 : json.uiLv) || 1001;
        return [Math.floor(uiLv / 1000), uiLv % 1000];
    };
    // 获取地块的绘制类型
    MapCellObj.prototype.getLandDrawType = function () {
        if (this.isMainCity()) {
            return 10;
        }
        else if (this.isAncient()) {
            return /* 11 */ 10; //暂时用主城的
        }
        else if (this.landLv === 1) {
            return 0; //荒地
        }
        return this.landType;
    };
    // 获取区域的建筑id 用于战斗的
    MapCellObj.prototype.getAreaBuildIcon = function () {
        if (this.isMainCity()) {
            return { id: 2000, icon: 'build_2000' }; //城墙
        }
        else if (this.isFort()) {
            return { id: 2102, icon: 'build_2102_' + this.getOwnType() }; //要塞
        }
        else if (this.isAncient()) {
            return { id: this.cityId, icon: 'build_' + this.cityId };
        }
        else if (this.owner) {
            return { id: 2103, icon: 'build_2103' };
        }
        return { id: 2101, icon: 'build_2101' };
    };
    MapCellObj.prototype.getPawnInfo = function () {
        var _a, _b, _c;
        var id = 0, lv = 1, isAncient = this.isAncient();
        if (this.isMainCity()) { //城墙士兵
            id = 7003;
        }
        else if (this.isFort()) { //要塞
            id = 7002;
        }
        else if (isAncient) { //遗迹
            id = 8001;
        }
        else if (this.owner) { //哨站
            id = 7001;
        }
        else {
            return null;
        }
        if (this.index < 0) {
            lv = ((_b = (_a = GameHelper_1.gameHpr.areaCenter.getLookArea()) === null || _a === void 0 ? void 0 : _a.getBuildByPawnId(id)) === null || _b === void 0 ? void 0 : _b.lv) || 1;
        }
        else if (isAncient) {
            lv = ((_c = GameHelper_1.gameHpr.world.getAncientInfo(Math.abs(this.index))) === null || _c === void 0 ? void 0 : _c.lv) || 1;
        }
        else {
            lv = GameHelper_1.gameHpr.getPlayerTowerLvByPawn(this.owner, id);
        }
        return { id: id, lv: lv };
    };
    // 获取血量信息
    MapCellObj.prototype.getHpInfo = function () {
        var area = this.getArea();
        if (area === null || area === void 0 ? void 0 : area.isBattleing()) { //目前只有是在战斗中才用区域的血量
            return [area.curHp, area.maxHp];
        }
        else if (GameHelper_1.gameHpr.isNoviceMode && this.isFort()) {
            area = GameHelper_1.gameHpr.noviceServer.getArea(this.actIndex);
            if (area) {
                return [area.curHp, area.maxHp]; //新手村用区域的血量 因为有自定义调整
            }
        }
        var cell = this.city ? (GameHelper_1.gameHpr.world.getMapCellByIndex(this.city.index) || this) : this;
        return cell.getJsonHp();
    };
    MapCellObj.prototype.getJsonHp = function () {
        var _a, _b;
        var info = this.getPawnInfo(), hp = 0;
        if (info) { //有关联士兵
            hp = ((_a = assetsMgr.getJsonData('pawnAttr', info.id * 1000 + info.lv)) === null || _a === void 0 ? void 0 : _a.hp) || 0;
            // 加上遗迹的
            var mul = GameHelper_1.gameHpr.getAncientEffectByPlayer(this.owner, Enums_1.CEffect.TOWER_HP) * 0.01 + 1;
            hp = Math.round(hp * mul);
        }
        else {
            hp = ((_b = assetsMgr.getJsonData('landAttr', this.landLv * 1000 + 1)) === null || _b === void 0 ? void 0 : _b.hp) || 0;
        }
        return [hp, hp];
    };
    // 获取血量
    MapCellObj.prototype.getHpText = function () {
        return this.getHpInfo().join('/');
    };
    // 获取地块关联的士兵属性
    MapCellObj.prototype.getPawnAttrJson = function () {
        var info = this.getPawnInfo();
        return info ? assetsMgr.getJsonData('pawnAttr', info.id * 1000 + info.lv) : null;
    };
    // 获取野地怪物战斗力
    MapCellObj.prototype.getMonsterAttack = function () {
        if (this.owner) {
            return 0; //如果被占领 那么直接为0
        }
        var data = GameHelper_1.gameHpr.getAreaPawnConfInfo(this.index, this.landId, GameHelper_1.gameHpr.getSelfToMapCellDis(this.index));
        return data.armys.reduce(function (val, cur) { return val + GameHelper_1.gameHpr.getPawnsAttack(cur.pawns); }, 0);
    };
    // 获取当前领地积分
    MapCellObj.prototype.getLandScore = function () {
        var e_1, _a;
        var _b;
        var landCountMap = GameHelper_1.gameHpr.player.getOccupyLandCountMap();
        var landCount = ((_b = landCountMap[this.landLv]) === null || _b === void 0 ? void 0 : _b[1]) || 0;
        var arr = Constant_1.LAND_SCORE_CONF[this.landLv] || [];
        try {
            for (var arr_1 = __values(arr), arr_1_1 = arr_1.next(); !arr_1_1.done; arr_1_1 = arr_1.next()) {
                var m = arr_1_1.value;
                if (landCount < m[0]) {
                    return m[1];
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (arr_1_1 && !arr_1_1.done && (_a = arr_1.return)) _a.call(arr_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return 0;
    };
    // 获取产出类型
    MapCellObj.prototype.getOutputType = function () {
        if (this.cityId > 0 && this.isHasRes() && !this.isBTCitying()) {
            return GameHelper_1.gameHpr.world.getCityOutputMap()[this.index];
        }
        GameHelper_1.gameHpr.world.setCityOutput(this.index, null);
        return null;
    };
    return MapCellObj;
}());
exports.default = MapCellObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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