
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/WeakGuideObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fbec30veyJKT5M4xxkIcndl', 'WeakGuideObj');
// app/script/model/guide/WeakGuideObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var GuideObj_1 = require("./GuideObj");
// 一个引导
var WeakGuideObj = /** @class */ (function (_super) {
    __extends(WeakGuideObj, _super);
    function WeakGuideObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.data = null;
        return _this;
    }
    WeakGuideObj.prototype.init = function (conf) {
        this.id = conf.id;
        this.progressTag = '0';
        this.steps = conf.steps;
        this.checkFunc = conf.checkFunc;
        this.resetIndex();
        return this;
    };
    return WeakGuideObj;
}(GuideObj_1.default));
exports.default = WeakGuideObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxndWlkZVxcV2Vha0d1aWRlT2JqLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHVDQUFpQztBQUVqQyxPQUFPO0FBQ1A7SUFBMEMsZ0NBQVE7SUFBbEQ7UUFBQSxxRUFZQztRQVZVLFVBQUksR0FBUSxJQUFJLENBQUE7O0lBVTNCLENBQUM7SUFSVSwyQkFBSSxHQUFYLFVBQVksSUFBUztRQUNqQixJQUFJLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQyxFQUFFLENBQUE7UUFDakIsSUFBSSxDQUFDLFdBQVcsR0FBRyxHQUFHLENBQUE7UUFDdEIsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO1FBQ3ZCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQTtRQUMvQixJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7UUFDakIsT0FBTyxJQUFJLENBQUE7SUFDZixDQUFDO0lBQ0wsbUJBQUM7QUFBRCxDQVpBLEFBWUMsQ0FaeUMsa0JBQVEsR0FZakQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgR3VpZGVPYmogZnJvbSBcIi4vR3VpZGVPYmpcIlxyXG5cclxuLy8g5LiA5Liq5byV5a+8XHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFdlYWtHdWlkZU9iaiBleHRlbmRzIEd1aWRlT2JqIHtcclxuXHJcbiAgICBwdWJsaWMgZGF0YTogYW55ID0gbnVsbFxyXG5cclxuICAgIHB1YmxpYyBpbml0KGNvbmY6IGFueSkge1xyXG4gICAgICAgIHRoaXMuaWQgPSBjb25mLmlkXHJcbiAgICAgICAgdGhpcy5wcm9ncmVzc1RhZyA9ICcwJ1xyXG4gICAgICAgIHRoaXMuc3RlcHMgPSBjb25mLnN0ZXBzXHJcbiAgICAgICAgdGhpcy5jaGVja0Z1bmMgPSBjb25mLmNoZWNrRnVuY1xyXG4gICAgICAgIHRoaXMucmVzZXRJbmRleCgpXHJcbiAgICAgICAgcmV0dXJuIHRoaXNcclxuICAgIH1cclxufSJdfQ==