
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/AddPopularityTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '44a79+frx5Cio5YUL/+XpJt', 'AddPopularityTipPnlCtrl');
// app/script/view/common/AddPopularityTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AddPopularityTipPnlCtrl = /** @class */ (function (_super) {
    __extends(AddPopularityTipPnlCtrl, _super);
    function AddPopularityTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        _this.goldNode_ = null; // path://root/ok_be/lay/gold_n
        //@end
        _this.data = null;
        _this.cb = null;
        return _this;
    }
    AddPopularityTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AddPopularityTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AddPopularityTipPnlCtrl.prototype.onEnter = function (data, cb) {
        var _this = this;
        this.data = data;
        this.cb = cb;
        var ownMap = {};
        var datas = [];
        GameHelper_1.gameHpr.user.getUnlockBotanys().forEach(function (m) {
            if (m.count) {
                ownMap[m.id] = m;
            }
        });
        assetsMgr.getJson('evaluateGift').datas.forEach(function (m) {
            if (m.type !== 3 || ownMap[m.id]) {
                datas.push(m);
            }
        });
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Items(datas, function (it, json) {
            it.Data = json;
            var val = it.Child('val');
            ResHelper_1.resHelper.loadGiftIcon(json.id, val, _this.key);
            val.scale = 1;
            it.Component(cc.Toggle).isChecked = false;
            var own = ownMap[json.id];
            it.Child('count', cc.Label).string = own ? ('x' + own.count) : '';
        });
        this.goldNode_.active = false;
    };
    AddPopularityTipPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    AddPopularityTipPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_te
    AddPopularityTipPnlCtrl.prototype.onClickItem = function (event, data) {
        var json = event.isChecked ? event.node.Data : null;
        this.updateBtton(json);
    };
    // path://root/ok_be
    AddPopularityTipPnlCtrl.prototype.onClickOk = function (event, data) {
        this.do();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AddPopularityTipPnlCtrl.prototype.updateBtton = function (json) {
        // this.listSv_.content.children.forEach(m => {
        //     m.Child('val').scale = json?.id === m.Data?.id ? 1.2 : 1
        // })
        if (this.goldNode_.active = !!(json === null || json === void 0 ? void 0 : json.gold)) {
            this.goldNode_.Child('val', cc.Label).string = json.gold + '';
        }
    };
    AddPopularityTipPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var it, user, josn, _a, err, data, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        it = this.listSv_.content.Component(cc.ToggleContainer).toggleItems.find(function (m) { return m.isChecked; });
                        if (!it) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.please_select_evaluate')];
                        }
                        user = GameHelper_1.gameHpr.user;
                        josn = it.node.Data;
                        if (!josn) {
                            return [2 /*return*/];
                        }
                        else if (user.getGold() < josn.gold) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showGoldNotEnough()];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_ChangeUserPopularity', { uid: this.data.uid, id: josn.id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        info = this.data.popularityInfo;
                        if (!info) {
                            info = this.data.popularityInfo = { records: [], time: 0, list: [], reqing: false };
                        }
                        info.time = Date.now();
                        info.list = ((data === null || data === void 0 ? void 0 : data.list) || [{ value: [101, 0] }]).map(function (m) { return m.value; });
                        info.records.push({ uid: GameHelper_1.gameHpr.getUid(), nickname: user.getNickname(), value: josn.id, time: info.time });
                        user.setGold(data === null || data === void 0 ? void 0 : data.gold);
                        user.setUnlockBotanys(data.unlockBotanys);
                        this.cb && this.cb(josn.id);
                        this.cb = null;
                        this.hide();
                        return [2 /*return*/];
                }
            });
        });
    };
    AddPopularityTipPnlCtrl = __decorate([
        ccclass
    ], AddPopularityTipPnlCtrl);
    return AddPopularityTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AddPopularityTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcQWRkUG9wdWxhcml0eVRpcFBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0EsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFFcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBcUQsMkNBQWM7SUFBbkU7UUFBQSxxRUErR0M7UUE3R0csMEJBQTBCO1FBQ2xCLGFBQU8sR0FBa0IsSUFBSSxDQUFBLENBQUMsc0JBQXNCO1FBQ3BELGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQywrQkFBK0I7UUFDakUsTUFBTTtRQUVFLFVBQUksR0FBc0IsSUFBSSxDQUFBO1FBQzlCLFFBQUUsR0FBYSxJQUFJLENBQUE7O0lBdUcvQixDQUFDO0lBckdVLGlEQUFlLEdBQXRCO1FBQ0ksT0FBTyxFQUFFLENBQUE7SUFDYixDQUFDO0lBRVksMENBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0seUNBQU8sR0FBZCxVQUFlLElBQXVCLEVBQUUsRUFBWTtRQUFwRCxpQkEyQkM7UUExQkcsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUE7UUFDWixJQUFNLE1BQU0sR0FBRyxFQUFFLENBQUE7UUFDakIsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFBO1FBQ2hCLG9CQUFPLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztZQUNyQyxJQUFJLENBQUMsQ0FBQyxLQUFLLEVBQUU7Z0JBQ1QsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDbkI7UUFDTCxDQUFDLENBQUMsQ0FBQTtRQUNGLFNBQVMsQ0FBQyxPQUFPLENBQUMsY0FBYyxDQUFDLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7WUFDN0MsSUFBSSxDQUFDLENBQUMsSUFBSSxLQUFLLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFO2dCQUM5QixLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFBO2FBQ2hCO1FBQ0wsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFJLENBQUMsT0FBTyxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQzdCLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDMUIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxFQUFFLFVBQUMsRUFBRSxFQUFFLElBQUk7WUFDL0IsRUFBRSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7WUFDZCxJQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQzNCLHFCQUFTLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsR0FBRyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUM5QyxHQUFHLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTtZQUNiLEVBQUUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUE7WUFDekMsSUFBTSxHQUFHLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtZQUMzQixFQUFFLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUE7UUFDckUsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7SUFDakMsQ0FBQztJQUVNLDBDQUFRLEdBQWY7UUFDSSxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDckIsSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLHlDQUFPLEdBQWQ7UUFDSSxTQUFTLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQzNDLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDJDQUEyQztJQUMzQyw2Q0FBVyxHQUFYLFVBQVksS0FBZ0IsRUFBRSxJQUFZO1FBQ3RDLElBQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUE7UUFDckQsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQTtJQUMxQixDQUFDO0lBRUQsb0JBQW9CO0lBQ3BCLDJDQUFTLEdBQVQsVUFBVSxLQUEwQixFQUFFLElBQVk7UUFDOUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFakgsaUhBQWlIO0lBRXpHLDZDQUFXLEdBQW5CLFVBQW9CLElBQVM7UUFDekIsK0NBQStDO1FBQy9DLCtEQUErRDtRQUMvRCxLQUFLO1FBQ0wsSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQSxFQUFFO1lBQ3RDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLEdBQUcsRUFBRSxDQUFBO1NBQ2hFO0lBQ0wsQ0FBQztJQUVhLG9DQUFFLEdBQWhCOzs7Ozs7d0JBQ1UsRUFBRSxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsZUFBZSxDQUFDLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxTQUFTLEVBQVgsQ0FBVyxDQUFDLENBQUE7d0JBQ2hHLElBQUksQ0FBQyxFQUFFLEVBQUU7NEJBQ0wsc0JBQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsOEJBQThCLENBQUMsRUFBQTt5QkFDOUQ7d0JBQ0ssSUFBSSxHQUFHLG9CQUFPLENBQUMsSUFBSSxDQUFBO3dCQUNuQixJQUFJLEdBQUcsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUE7d0JBQ3pCLElBQUksQ0FBQyxJQUFJLEVBQUU7NEJBQ1Asc0JBQU07eUJBQ1Q7NkJBQU0sSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFLEdBQUcsSUFBSSxDQUFDLElBQUksRUFBRTs0QkFDbkMsc0JBQU8sdUJBQVUsQ0FBQyxpQkFBaUIsRUFBRSxFQUFBO3lCQUN4Qzt3QkFDcUIscUJBQU0sb0JBQU8sQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLCtCQUErQixFQUFFLEVBQUUsR0FBRyxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRSxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUE7O3dCQUFySCxLQUFnQixTQUFxRyxFQUFuSCxHQUFHLFNBQUEsRUFBRSxJQUFJLFVBQUE7d0JBQ2pCLElBQUksR0FBRyxFQUFFOzRCQUNMLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxFQUFBO3lCQUNuQzt3QkFDRyxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUE7d0JBQ25DLElBQUksQ0FBQyxJQUFJLEVBQUU7NEJBQ1AsSUFBSSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsY0FBYyxHQUFHLEVBQUUsT0FBTyxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsTUFBTSxFQUFFLEtBQUssRUFBRSxDQUFBO3lCQUN0Rjt3QkFDRCxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQTt3QkFDdEIsSUFBSSxDQUFDLElBQUksR0FBRyxDQUFDLENBQUEsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksS0FBSSxDQUFDLEVBQUUsS0FBSyxFQUFFLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxLQUFLLEVBQVAsQ0FBTyxDQUFDLENBQUE7d0JBQ25FLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLG9CQUFPLENBQUMsTUFBTSxFQUFFLEVBQUUsUUFBUSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUE7d0JBQzNHLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQyxDQUFBO3dCQUN4QixJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxDQUFBO3dCQUN6QyxJQUFJLENBQUMsRUFBRSxJQUFJLElBQUksQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO3dCQUMzQixJQUFJLENBQUMsRUFBRSxHQUFHLElBQUksQ0FBQTt3QkFDZCxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7Ozs7O0tBQ2Q7SUE5R2dCLHVCQUF1QjtRQUQzQyxPQUFPO09BQ2EsdUJBQXVCLENBK0czQztJQUFELDhCQUFDO0NBL0dELEFBK0dDLENBL0dvRCxFQUFFLENBQUMsV0FBVyxHQStHbEU7a0JBL0dvQix1QkFBdUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJUG9wdWxhcml0eVBsYXllciB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvSW50ZXJmYWNlXCI7XHJcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XHJcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xyXG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xyXG5cclxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQWRkUG9wdWxhcml0eVRpcFBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XHJcblxyXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cclxuICAgIHByaXZhdGUgbGlzdFN2XzogY2MuU2Nyb2xsVmlldyA9IG51bGwgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdlxyXG4gICAgcHJpdmF0ZSBnb2xkTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L29rX2JlL2xheS9nb2xkX25cclxuICAgIC8vQGVuZFxyXG5cclxuICAgIHByaXZhdGUgZGF0YTogSVBvcHVsYXJpdHlQbGF5ZXIgPSBudWxsXHJcbiAgICBwcml2YXRlIGNiOiBGdW5jdGlvbiA9IG51bGxcclxuXHJcbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xyXG4gICAgICAgIHJldHVybiBbXVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBhc3luYyBvbkNyZWF0ZSgpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25FbnRlcihkYXRhOiBJUG9wdWxhcml0eVBsYXllciwgY2I6IEZ1bmN0aW9uKSB7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YVxyXG4gICAgICAgIHRoaXMuY2IgPSBjYlxyXG4gICAgICAgIGNvbnN0IG93bk1hcCA9IHt9XHJcbiAgICAgICAgY29uc3QgZGF0YXMgPSBbXVxyXG4gICAgICAgIGdhbWVIcHIudXNlci5nZXRVbmxvY2tCb3RhbnlzKCkuZm9yRWFjaChtID0+IHtcclxuICAgICAgICAgICAgaWYgKG0uY291bnQpIHtcclxuICAgICAgICAgICAgICAgIG93bk1hcFttLmlkXSA9IG1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgYXNzZXRzTWdyLmdldEpzb24oJ2V2YWx1YXRlR2lmdCcpLmRhdGFzLmZvckVhY2gobSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChtLnR5cGUgIT09IDMgfHwgb3duTWFwW20uaWRdKSB7XHJcbiAgICAgICAgICAgICAgICBkYXRhcy5wdXNoKG0pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KVxyXG4gICAgICAgIHRoaXMubGlzdFN2Xy5zdG9wQXV0b1Njcm9sbCgpXHJcbiAgICAgICAgdGhpcy5saXN0U3ZfLmNvbnRlbnQueSA9IDBcclxuICAgICAgICB0aGlzLmxpc3RTdl8uSXRlbXMoZGF0YXMsIChpdCwganNvbikgPT4ge1xyXG4gICAgICAgICAgICBpdC5EYXRhID0ganNvblxyXG4gICAgICAgICAgICBjb25zdCB2YWwgPSBpdC5DaGlsZCgndmFsJylcclxuICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRHaWZ0SWNvbihqc29uLmlkLCB2YWwsIHRoaXMua2V5KVxyXG4gICAgICAgICAgICB2YWwuc2NhbGUgPSAxXHJcbiAgICAgICAgICAgIGl0LkNvbXBvbmVudChjYy5Ub2dnbGUpLmlzQ2hlY2tlZCA9IGZhbHNlXHJcbiAgICAgICAgICAgIGNvbnN0IG93biA9IG93bk1hcFtqc29uLmlkXVxyXG4gICAgICAgICAgICBpdC5DaGlsZCgnY291bnQnLCBjYy5MYWJlbCkuc3RyaW5nID0gb3duID8gKCd4JyArIG93bi5jb3VudCkgOiAnJ1xyXG4gICAgICAgIH0pXHJcbiAgICAgICAgdGhpcy5nb2xkTm9kZV8uYWN0aXZlID0gZmFsc2VcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XHJcbiAgICAgICAgdGhpcy5jYiAmJiB0aGlzLmNiKDApXHJcbiAgICAgICAgdGhpcy5jYiA9IG51bGxcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25DbGVhbigpIHtcclxuICAgICAgICBhc3NldHNNZ3IucmVsZWFzZVRlbXBSZXNCeVRhZyh0aGlzLmtleSlcclxuICAgIH1cclxuXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXHJcblxyXG4gICAgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdi92aWV3L2NvbnRlbnQvaXRlbV90ZVxyXG4gICAgb25DbGlja0l0ZW0oZXZlbnQ6IGNjLlRvZ2dsZSwgZGF0YTogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QganNvbiA9IGV2ZW50LmlzQ2hlY2tlZCA/IGV2ZW50Lm5vZGUuRGF0YSA6IG51bGxcclxuICAgICAgICB0aGlzLnVwZGF0ZUJ0dG9uKGpzb24pXHJcbiAgICB9XHJcblxyXG4gICAgLy8gcGF0aDovL3Jvb3Qvb2tfYmVcclxuICAgIG9uQ2xpY2tPayhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XHJcbiAgICAgICAgdGhpcy5kbygpXHJcbiAgICB9XHJcbiAgICAvL0BlbmRcclxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGV2ZW50IGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcblxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbiAgICBwcml2YXRlIHVwZGF0ZUJ0dG9uKGpzb246IGFueSkge1xyXG4gICAgICAgIC8vIHRoaXMubGlzdFN2Xy5jb250ZW50LmNoaWxkcmVuLmZvckVhY2gobSA9PiB7XHJcbiAgICAgICAgLy8gICAgIG0uQ2hpbGQoJ3ZhbCcpLnNjYWxlID0ganNvbj8uaWQgPT09IG0uRGF0YT8uaWQgPyAxLjIgOiAxXHJcbiAgICAgICAgLy8gfSlcclxuICAgICAgICBpZiAodGhpcy5nb2xkTm9kZV8uYWN0aXZlID0gISFqc29uPy5nb2xkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuZ29sZE5vZGVfLkNoaWxkKCd2YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0ganNvbi5nb2xkICsgJydcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcHJpdmF0ZSBhc3luYyBkbygpIHtcclxuICAgICAgICBjb25zdCBpdCA9IHRoaXMubGlzdFN2Xy5jb250ZW50LkNvbXBvbmVudChjYy5Ub2dnbGVDb250YWluZXIpLnRvZ2dsZUl0ZW1zLmZpbmQobSA9PiBtLmlzQ2hlY2tlZClcclxuICAgICAgICBpZiAoIWl0KSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydCgndG9hc3QucGxlYXNlX3NlbGVjdF9ldmFsdWF0ZScpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IHVzZXIgPSBnYW1lSHByLnVzZXJcclxuICAgICAgICBjb25zdCBqb3NuID0gaXQubm9kZS5EYXRhXHJcbiAgICAgICAgaWYgKCFqb3NuKSB7XHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH0gZWxzZSBpZiAodXNlci5nZXRHb2xkKCkgPCBqb3NuLmdvbGQpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0dvbGROb3RFbm91Z2goKVxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCB7IGVyciwgZGF0YSB9ID0gYXdhaXQgZ2FtZUhwci5uZXQucmVxdWVzdCgnbG9iYnkvSERfQ2hhbmdlVXNlclBvcHVsYXJpdHknLCB7IHVpZDogdGhpcy5kYXRhLnVpZCwgaWQ6IGpvc24uaWQgfSwgdHJ1ZSlcclxuICAgICAgICBpZiAoZXJyKSB7XHJcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBpbmZvID0gdGhpcy5kYXRhLnBvcHVsYXJpdHlJbmZvXHJcbiAgICAgICAgaWYgKCFpbmZvKSB7XHJcbiAgICAgICAgICAgIGluZm8gPSB0aGlzLmRhdGEucG9wdWxhcml0eUluZm8gPSB7IHJlY29yZHM6IFtdLCB0aW1lOiAwLCBsaXN0OiBbXSwgcmVxaW5nOiBmYWxzZSB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGluZm8udGltZSA9IERhdGUubm93KClcclxuICAgICAgICBpbmZvLmxpc3QgPSAoZGF0YT8ubGlzdCB8fCBbeyB2YWx1ZTogWzEwMSwgMF0gfV0pLm1hcChtID0+IG0udmFsdWUpXHJcbiAgICAgICAgaW5mby5yZWNvcmRzLnB1c2goeyB1aWQ6IGdhbWVIcHIuZ2V0VWlkKCksIG5pY2tuYW1lOiB1c2VyLmdldE5pY2tuYW1lKCksIHZhbHVlOiBqb3NuLmlkLCB0aW1lOiBpbmZvLnRpbWUgfSlcclxuICAgICAgICB1c2VyLnNldEdvbGQoZGF0YT8uZ29sZClcclxuICAgICAgICB1c2VyLnNldFVubG9ja0JvdGFueXMoZGF0YS51bmxvY2tCb3RhbnlzKVxyXG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYihqb3NuLmlkKVxyXG4gICAgICAgIHRoaXMuY2IgPSBudWxsXHJcbiAgICAgICAgdGhpcy5oaWRlKClcclxuICAgIH1cclxufVxyXG4iXX0=