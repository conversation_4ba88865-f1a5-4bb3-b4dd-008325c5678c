# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# Android SDK version that will be used as the compile project
PROP_COMPILE_SDK_VERSION=35

# Android SDK version that will be used as the earliest version of android this application can run on
PROP_MIN_SDK_VERSION=16

# Android SDK version that will be used as the latest version of android this application has been tested on
PROP_TARGET_SDK_VERSION=35

# Android Build Tools version that will be used as the compile project
PROP_BUILD_TOOLS_VERSION=28.0.3

# List of CPU Archtexture to build that application with
# Available architextures (armeabi-v7a | arm64-v8a | x86)
# To build for multiple architexture, use the `:` between them
# Example - PROP_APP_ABI=arm64-v8a:armeabi-v7a
PROP_APP_ABI=arm64-v8a:armeabi-v7a

# fill in sign information for release mode
RELEASE_STORE_FILE=/Users/<USER>/Desktop/projects/slg-client/apk/android_global/twgame-jwm-google.keystore
RELEASE_STORE_PASSWORD=yujianShuai
RELEASE_KEY_ALIAS=sheiZuishuai
RELEASE_KEY_PASSWORD=yujianShuai

android.injected.testOnly=false

# android.enableJetifier=true