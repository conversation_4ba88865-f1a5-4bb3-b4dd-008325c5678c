
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SubscriptionDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '21d56/yQ2VKKION+p2dqX38', 'SubscriptionDescPnlCtrl');
// app/script/view/common/SubscriptionDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var ccclass = cc._decorator.ccclass;
var SubscriptionDescPnlCtrl = /** @class */ (function (_super) {
    __extends(SubscriptionDescPnlCtrl, _super);
    function SubscriptionDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/bg/content/title/title_l
        _this.infoNode_ = null; // path://root/bg/content/info_n
        _this.totalDescNode_ = null; // path://root/bg/content/total_desc_n
        _this.buttonsNode_ = null; // path://root/bg/content/buttons_n
        _this.subscriptionButtonNode_ = null; // path://root/bg/content/buttons_n/subscription_button_n
        //@end
        _this.user = null;
        _this.restoreSubscriptions = [];
        _this.cardType = Enums_1.MonthlyCardType.SALE;
        return _this;
    }
    SubscriptionDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SubscriptionDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var list;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.user = this.getModel('user');
                        return [4 /*yield*/, PayHelper_1.payHelper.checkPayInit()];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, PayHelper_1.payHelper.getSubscriptions()];
                    case 2:
                        list = _a.sent();
                        this.restoreSubscriptions = list.slice();
                        return [2 /*return*/];
                }
            });
        });
    };
    SubscriptionDescPnlCtrl.prototype.onEnter = function (type) {
        this.cardType = type;
        var index = Constant_1.MONTH_CARD.findIndex(function (m) { return m.TYPE === type; });
        this.titleLbl_.setLocaleKey('ui.monthly_card_' + (index + 1));
        var isFirsPay = !this.user.getRechargeCountRecord()[type];
        var firstPayLbl = this.infoNode_.Child('1/content/layout/val');
        firstPayLbl.active = isFirsPay;
        isFirsPay && firstPayLbl.setLocaleKey('ui.bracket', assetsMgr.lang('ui.first_pay_double'));
        var data = Constant_1.MONTH_CARD[index];
        this.infoNode_.Child('1/content/count', cc.Label).string = isFirsPay ? 'x' + data.FIRST * 2 : 'x' + data.FIRST;
        this.infoNode_.Child('1/tip_desc').setLocaleKey('ui.subscription_imm_tip', isFirsPay ? data.FIRST * 4 : data.FIRST * 3);
        this.infoNode_.Child('2/count', cc.Label).string = 'x' + data.DAY;
        this.infoNode_.Child('3/count', cc.Label).string = 'x' + data.EXTRA;
        this.infoNode_.Child('2/line').active = this.infoNode_.Child('3').active = type === Enums_1.MonthlyCardType.SUPER;
        var params = type === Enums_1.MonthlyCardType.SALE ? [data.DURATION, data.DAY * data.DURATION] : [data.DURATION, data.DAY * data.DURATION, data.EXTRA * data.DURATION];
        this.totalDescNode_.setLocaleKey('ui.subscription_total_value_' + (index + 1), params);
        var monthCard = Constant_1.MONTH_CARD.find(function (m) { return m.TYPE === type; });
        var has = this.restoreSubscriptions.some(function (m) { return monthCard.RESTORES.includes(m.productId); });
        this.buttonsNode_.Swih(has ? 'restore_buy_be' : 'subscription_button_n')[0];
        if (!has) {
            var node1 = this.subscriptionButtonNode_.Child('subscription_1_be/root');
            var node7 = this.subscriptionButtonNode_.Child('subscription_7_be/root');
            var node30 = this.subscriptionButtonNode_.Child('subscription_30_be/root');
            var key = '0';
            var it1 = node1.Swih(key)[0], it7 = node7.Swih(key)[0], it30 = node30.Swih(key)[0];
            it1.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'month', false);
            it7.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'month', true);
            it30.Child('val', cc.Label).string = PayHelper_1.payHelper.getSubPriceText(type, 'quarter', true);
        }
    };
    SubscriptionDescPnlCtrl.prototype.onRemove = function () {
    };
    SubscriptionDescPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_1_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription1 = function (event, data) {
        var _this = this;
        var id = Constant_1.MONTH_CARD.find(function (m) { return m.TYPE === _this.cardType; }).RECHARGES[0];
        id && this.buyProduct(id);
    };
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_7_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription7 = function (event, data) {
        this.buySubScription(this.cardType, 'month');
    };
    // path://root/bg/content/buttons_n/subscription_button_n/subscription_30_be
    SubscriptionDescPnlCtrl.prototype.onClickSubscription30 = function (event, data) {
        this.buySubScription(this.cardType, 'quarter');
    };
    // path://root/bg/content/desc/desc1_be
    SubscriptionDescPnlCtrl.prototype.onClickDesc1 = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getPrivacyPolicyUrl());
    };
    // path://root/bg/content/desc/desc2_be
    SubscriptionDescPnlCtrl.prototype.onClickDesc2 = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getUserAgreementUrl());
    };
    // path://root/bg/content/buttons_n/restore_buy_be
    SubscriptionDescPnlCtrl.prototype.onClickRestoreBuy = function (event, data) {
        this.restore();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SubscriptionDescPnlCtrl.prototype.buyProduct = function (productId) {
        var _this = this;
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        PayHelper_1.payHelper.buyProduct(productId).then(function (ok) {
            logger.print('6.buyProduct end. suc=' + ok + ', isValid=' + _this.isValid);
            if (_this.isValid && ok) {
                _this.hide();
            }
        });
    };
    SubscriptionDescPnlCtrl.prototype.buySubScription = function (cardType, type) {
        var _this = this;
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        PayHelper_1.payHelper.buySubscription(cardType, type).then(function (ok) {
            if (_this.isValid && ok) {
                _this.hide();
            }
        });
    };
    // 恢复购买
    SubscriptionDescPnlCtrl.prototype.restore = function () {
        return __awaiter(this, void 0, void 0, function () {
            var order, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.restoreSubscriptions.length === 0) {
                            return [2 /*return*/, this.onEnter(this.cardType)];
                        }
                        order = this.restoreSubscriptions[0];
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifySubOrder', {
                                productId: order.productId,
                                orderId: order.orderId,
                                cpOrderId: order.cpOrderId,
                                token: order.token,
                                platform: order.platform,
                                price: order.price,
                                purchaseTime: order.purchaseTime,
                                currencyType: order.currencyType,
                                payAmount: order.payAmount,
                            }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!!this.isValid) return [3 /*break*/, 2];
                        return [2 /*return*/];
                    case 2:
                        if (!(err === ECode_1.ecode.SUBSCRIPTION_TIMEOUT || err === ECode_1.ecode.ORDER_VERIFY_API_ERROR)) return [3 /*break*/, 4];
                        this.restoreSubscriptions.shift();
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token, type: 'subs' })];
                    case 3:
                        _b.sent();
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        if (this.restoreSubscriptions.length > 0) {
                            this.restore();
                        }
                        else {
                            PayHelper_1.payHelper.cleanRestoredSubs();
                            ViewHelper_1.viewHelper.showAlert(err);
                            this.onEnter(this.cardType);
                        }
                        return [2 /*return*/];
                    case 4:
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.restore_buy_sub_succeed');
                            this.hide();
                        }
                        _b.label = 5;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    SubscriptionDescPnlCtrl = __decorate([
        ccclass
    ], SubscriptionDescPnlCtrl);
    return SubscriptionDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SubscriptionDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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