
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildHospitalPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '173e2FGrUpPEKdeEFfXjoOt', 'BuildHospitalPnlCtrl');
// app/script/view/build/BuildHospitalPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ArmyObj_1 = require("../../model/area/ArmyObj");
var PawnObj_1 = require("../../model/area/PawnObj");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var NoviceConfig_1 = require("../../model/guide/NoviceConfig");
var ccclass = cc._decorator.ccclass;
var BuildHospitalPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildHospitalPnlCtrl, _super);
    function BuildHospitalPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.sortSelectNode_ = null; // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
        _this.queueSv_ = null; // path://root/pages_n/1/cure/content/queue_sv
        _this.upTimeNode_ = null; // path://root/pages_n/1/cure/x/up_time_be_n
        //@end
        _this.PKEY_TAB = 'HOSPITAL_TAB';
        _this.PKEY_SELECT_ARMY = 'HOSPITAL_SELECT_ARMY';
        _this.PKEY_SELECT_PAWN = 'HOSPITAL_SELECT_PAWN';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.cureProgressTween = {};
        _this.currSelectSort = 0; // 当前选择的排序方式
        _this.preSelectIndex = -1; // 治疗的目标在伤兵中的下标
        _this.tempCreateArmy = null;
        _this.selectArmy = null;
        _this.tempArmySortWeightMap = {};
        return _this;
    }
    BuildHospitalPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_INJURY_QUEUE] = this.onUpdatePawnInjuryQueue, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnCuringQueue, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.ADD_ARMY] = this.onUpdateArmy, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.REMOVE_ARMY] = this.onUpdateArmy, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateArmy, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_ARMY_DIST_INFO] = this.onUpdateArmy, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI] = this.onUpdateArmy, _o.enter = true, _o),
        ];
    };
    BuildHospitalPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                return [2 /*return*/];
            });
        });
    };
    BuildHospitalPnlCtrl.prototype.onEnter = function (data, tab) {
        var _a;
        this.data = data;
        this.tempCreateArmy = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.TEMP_CREATE_ARMY);
        var cond = this.pagesNode_.Child('1/info/cond');
        cond.Child('need/title/val').setLocaleKey('ui.drill_cost', 'ui.button_cure');
        this.pagesNode_.Child('1/cure/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_cure');
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
        // 排序选择
        this.selectSortItem(this.sortSelectNode_, (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) !== null && _a !== void 0 ? _a : 5, true);
    };
    BuildHospitalPnlCtrl.prototype.onRemove = function () {
        this.selectArmy = null;
        this.tempArmySortWeightMap = {};
        ViewHelper_1.viewHelper.closePopupBoxList(this.sortSelectNode_);
        this.showCreateArmyFingerTip(false);
        if (this.currSelectSort !== GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT)) {
            GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.INJURY_QUEUE_SORT, this.currSelectSort);
        }
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.TEMP_CREATE_ARMY, this.tempCreateArmy);
    };
    BuildHospitalPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildHospitalPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === 1) {
            // 显示当前的军队列表
            this.selectArmy = null;
            this.tempArmySortWeightMap = {};
            this.updateArmyList(true, node);
            // 显示可治疗的士兵
            this.updateInjuryList(true, node);
            // 费用
            this.updateCureCost(node);
            // 治疗列表
            this.updateCureQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildHospitalPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/info/pawn/list/view/content/pawn_be
    BuildHospitalPnlCtrl.prototype.onClickPawn = function (event, _data) {
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data, uid = data === null || data === void 0 ? void 0 : data.data.uid;
        if (!data) {
            return;
        }
        else if (uid === this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN)) {
            uid && ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data.pawn);
            return;
        }
        this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, uid);
        this.preSelectIndex = this.player.getInjuryPawns().findIndex(function (m) { return m.uid === uid; });
        this.updatePawnSelect(false);
        this.updateCureCost();
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildHospitalPnlCtrl.prototype.onClickArmy = function (event, _data) {
        var _a;
        audioMgr.playSFX('click');
        var it = event.target, data = it.Data;
        if (!data) {
            if (this.tempCreateArmy) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_has_empty_army');
            }
            return this.showCreateArmyUI();
        } /* else if (!data.army) {
            return viewHelper.showAlert('toast.army_not_in_maincity')
        } */
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/state/cure_be
    BuildHospitalPnlCtrl.prototype.onClickCure = function (event, data) {
        var _this = this;
        var area = this.areaCenter.getArea(this.data.aIndex);
        if (!area) {
            return;
        }
        else if (!this.selectArmy) { // 没有军队，提示创建军队
            if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                return ViewHelper_1.viewHelper.showAlert('toast.please_create_army', {
                    cb: function () {
                        if (_this.isValid && !_this.player.isArmyCountFull() && !GameHelper_1.gameHpr.isNoLongerTip('no_army')) {
                            _this.showCreateArmyFingerTip(true);
                        }
                    }
                });
            }
            else {
                var army_1 = this.player.getBaseArmys()[0];
                return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_1);
            }
        }
        else if (!this.selectArmy.army) { // 有军队，但是外出，先提示军队
            if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
            }
            else {
                var army_2 = this.player.getBaseArmys()[0];
                return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_2);
            }
        }
        var len = this.player.getInjuryPawns().length, pawnUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        if (len < 0) { // 没有伤兵
            return ViewHelper_1.viewHelper.showAlert('toast.no_curing_pawn');
        }
        else if (!pawnUid) { // 已有士兵，提示选择一个士兵进行治疗
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_ceir_item', { params: ['ui.wounded_pawn'] });
        }
        var selectArmyUid = this.selectArmy.uid, tempArmyUid = '', armyName = '';
        var army = area.getArmyByUid(selectArmyUid) || this.getTempCreateArmy(selectArmyUid);
        if (!army) {
            if (GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUILD_BARRACKS_DRILL)) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ARMY_NOT_EXIST);
            }
            else {
                var army_3 = this.player.getBaseArmys()[0];
                return ViewHelper_1.viewHelper.showPnl('build/BuildBarracksTip', army_3);
            }
        }
        else if (army.uid.startsWith('temp_')) {
            armyName = army.name;
        }
        else {
            tempArmyUid = army.uid;
        }
        this.areaCenter.curePawnToServer(this.data.aIndex, tempArmyUid, armyName, pawnUid).then(function (res) {
            var _a;
            if (!_this.isValid) {
            }
            else if (res.err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
            }
            else if (res.err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                return ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name');
            } /* else if (res.err === ecode.ANTI_CHEAT) {
                viewHelper.showPnl('main/AntiCheat')
            } */
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                audioMgr.playSFX('build/sound_ui_00' + (_this.data.id === 2010 ? '7' : '6'));
                var army_4 = res.army;
                if (((_a = _this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === selectArmyUid) {
                    _this.tempCreateArmy = null;
                    if (_this.selectArmy)
                        _this.selectArmy.uid = army_4.uid;
                    if (_this.tempArmySortWeightMap[selectArmyUid]) {
                        _this.tempArmySortWeightMap[army_4.uid] = _this.tempArmySortWeightMap[selectArmyUid];
                        delete _this.tempArmySortWeightMap[selectArmyUid];
                    }
                    _this.player.getBaseArmys().push({
                        index: _this.data.aIndex,
                        uid: army_4.uid,
                        name: army_4.name,
                        state: Enums_1.ArmyState.CURING,
                        pawns: [],
                    });
                }
                var node = _this.pagesNode_.Child(1);
                _this.updateCureQueue(node);
                _this.updateInjuryList(false, node);
                // this.updateArmyList(false, node)
                // this.updateCureCost(node)
                _this.updateCureCost(node);
            }
        });
    };
    // path://root/pages_n/1/cure/content/0/cure_pawn_be
    BuildHospitalPnlCtrl.prototype.onClickCurePawn = function (event, _data) {
        audioMgr.playSFX('click');
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByCureInfo(data), data);
        }
    };
    // path://root/pages_n/1/info/cond/need/buttons/delete_be
    BuildHospitalPnlCtrl.prototype.onClickDelete = function (event, _data) {
        var _this = this;
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || '';
        if (uid) {
            if (this.player.getCuringPawnsQueue().some(function (m) { return m.uid === uid; })) { // 治疗中无法删除
                this.updateCureButton(uid);
                return ViewHelper_1.viewHelper.showAlert('toast.delete_curing_pawn_tip');
            }
            var data = this.player.getInjuryPawns().find(function (m) { return m.uid === uid; });
            if (data) {
                ViewHelper_1.viewHelper.showMessageBox('ui.giveup_cure_tip', {
                    params: [assetsMgr.lang('ui.build_lv', ['pawnText.name_' + data.id, data.lv])],
                    ok: function () { return _this.isValid && _this.giveUpCure(uid); },
                    cancel: function () { },
                });
            }
        }
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n
    BuildHospitalPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/select_mask_be
    BuildHospitalPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/pages_n/1/info/pawn/sort/sort_select_be_n/mask/root/sort_items_nbe
    BuildHospitalPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = this.sortSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = Number(event.target.name);
        if (type !== this.currSelectSort) {
            this.selectSortItem(node, type);
        }
    };
    // path://root/pages_n/1/cure/x/up_time_be_n
    BuildHospitalPnlCtrl.prototype.onClickUpTime = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/SpeedUpCure', this.data);
    };
    // path://root/pages_n/1/cure/content/0/cancel_cure_be
    BuildHospitalPnlCtrl.prototype.onClickCancelCure = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_cure_no_back_cost_tip', {
                ok: function () { return _this.isValid && _this.cancelCure(data); },
                cancel: function () { },
            });
        }
        this.cancelCure(data);
    };
    // path://root/pages_n/0/info/chance_be
    BuildHospitalPnlCtrl.prototype.onClickChance = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('build/HospitalChanceDesc');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildHospitalPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('lv').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper.updateBuildAttrInfo(node, data);
        }
    };
    BuildHospitalPnlCtrl.prototype.onUpdatePawnInjuryQueue = function () {
        this.updateInjuryList(false);
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.onUpdatePawnCuringQueue = function () {
        this.updateCureQueue();
        // this.updateArmyList(false)
        this.updateCureCost();
    };
    // 战斗开始
    BuildHospitalPnlCtrl.prototype.onAreaBattleBegin = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 战斗结束
    BuildHospitalPnlCtrl.prototype.onAreaBattleEnd = function (index) {
        if (this.data.aIndex === index) {
            this.updateCureQueue();
        }
    };
    // 切换士兵皮肤
    BuildHospitalPnlCtrl.prototype.onChangePawnSkin = function (data) {
        this.updateInjuryList(false);
        this.updateCureQueue();
    };
    // 切换士兵装备
    BuildHospitalPnlCtrl.prototype.onChangePawnEquip = function () {
        this.updateInjuryList(false);
    };
    // 重新刷新军队列表
    BuildHospitalPnlCtrl.prototype.onUpdateArmy = function () {
        this.updateArmyList(false);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildHospitalPnlCtrl.prototype.getTempCreateArmy = function (uid) {
        var _a;
        if (((_a = this.tempCreateArmy) === null || _a === void 0 ? void 0 : _a.uid) === uid) {
            return this.tempCreateArmy;
        }
        return null;
    };
    // 选择排序
    BuildHospitalPnlCtrl.prototype.selectSortItem = function (node, type, init) {
        node.Data = this.currSelectSort = type;
        node.Child('val', cc.Label).setLocaleKey('ui.portrayal_list_sort_' + type);
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            // m.Child('val').Color(select ? '#E6DCC8' : '#B6A591')
            m.Child('select').active = select;
        });
        if (!init) {
            this.updateInjuryList(false);
        }
    };
    // 放弃治疗
    BuildHospitalPnlCtrl.prototype.giveUpCure = function (uid) {
        var _this = this;
        NetHelper_1.netHelper.reqGiveUpInjuryPawn({ uid: uid }).then(function (res) {
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                _this.player.updateInjuryPawns(data.injuryPawns);
                _this.updateInjuryList(false);
                var selectUid = _this.user.getTempPreferenceMap(_this.PKEY_SELECT_PAWN);
                _this.updateCureButton(selectUid);
            }
        });
    };
    BuildHospitalPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        var _a, _b;
        var item = {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
        if (!this.tempArmySortWeightMap[data.uid]) {
            var weight = item.army ? 2 : 1;
            weight = weight * 10 + (9 - (((_a = item.army) === null || _a === void 0 ? void 0 : _a.getActPawnCount()) || 0));
            weight = weight * 10 + (9 - (((_b = item.army) === null || _b === void 0 ? void 0 : _b.pawns.length) || 0));
            this.tempArmySortWeightMap[data.uid] = weight;
        }
        return item;
    };
    // 刷新军队列表
    BuildHospitalPnlCtrl.prototype.updateArmyList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        node = node || this.pagesNode_.Child(1);
        // 当前区域的军队
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        var armys = [null];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return armys.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        if (this.tempCreateArmy) {
            armys.push(this.addArmyToList(this.tempCreateArmy, this.tempCreateArmy));
        }
        // 排个序
        armys.sort(function (a, b) { return _this.tempArmySortWeightMap[b === null || b === void 0 ? void 0 : b.uid] - _this.tempArmySortWeightMap[a === null || a === void 0 ? void 0 : a.uid]; });
        var countNode = node.Child('army/title/count_bg');
        countNode.Child('cur', cc.Label).string = armys.length + '';
        countNode.Child('max', cc.Label).string = '/' + this.player.getArmyMaxCount();
        var uid = (_c = (_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.uid) !== null && _c !== void 0 ? _c : this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? armys.find(function (m) { return !!(m === null || m === void 0 ? void 0 : m.army) && (m === null || m === void 0 ? void 0 : m.uid) === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.stopAutoScroll();
        // armys.push(null)
        var showDot = !armys.some(function (m) { return !!m; }) && this.player.getInjuryPawns().length > 0;
        sv.Items(armys, function (it, data, i) {
            var _a, _b;
            it.Data = data;
            var army = data === null || data === void 0 ? void 0 : data.army;
            var root = it.Child('root'), info = root.Child('info');
            info.Child('dot').active = showDot;
            info.Child('add').active = !data;
            info.Child('count').active = !!data;
            info.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            var state = info.Child('state');
            if (army) {
                info.Child('count/val', cc.Label).string = (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0) + '';
                var addLbl = info.Child('count/add', cc.Label), dpc = army.drillPawns.length + army.curingPawns.length;
                if (addLbl.node.active = dpc > 0) {
                    addLbl.string = '+' + dpc;
                }
                var isFull = state.active = army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT;
                root.Child('bg').Component(cc.MultiFrame).setFrame(!isFull);
                info.opacity = isFull ? 150 : 255;
                if (isFull) {
                    state.setLocaleKey('ui.yet_full');
                }
                // 显示选择
                /* if (!curArmy && isFull) {
                } else  */ if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
                }
            }
            else if (data) {
                info.opacity = 150;
                root.Child('bg').Component(cc.MultiFrame).setFrame(false);
                info.Child('count/val', cc.Label).string = (((_b = data.pawns) === null || _b === void 0 ? void 0 : _b.length) || 0) + '';
                info.Child('count/add').active = false;
                state.active = true;
                state.setLocaleKey('ui.go_out');
                if (index === -1 && (!curArmy || curArmy.uid === data.uid)) {
                    curArmy = data;
                    index = i;
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, data.uid);
                }
            }
            else {
                info.opacity = 255;
                state.active = false;
                root.Child('bg').Component(cc.MultiFrame).setFrame(true);
            }
        });
        // 将选中的移动到中间
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        // 刷新选中
        this.updateArmySelect(curArmy, node);
        /* // 将选中的移动到中间
        if (index !== -1) {
            const lay = sv.content.Component(cc.Layout)
            lay.updateLayout()
            const width = sv.content.children[0].width
            const tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft //当前位置
            const pw = sv.content.parent.width
            const cx = pw * 0.5 //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0)
        } else {
            sv.scrollToLeft()
        } */
    };
    BuildHospitalPnlCtrl.prototype.updateArmySelect = function (item, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        this.selectArmy = item;
        var uid = (item === null || item === void 0 ? void 0 : item.uid) || '';
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        var army = item === null || item === void 0 ? void 0 : item.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
            pawns.pushArr(army.curingPawns);
            pawns.pushArr(army.drillPawns);
        }
        else if (item === null || item === void 0 ? void 0 : item.pawns) {
            pawns.pushArr(item.pawns);
        }
        // 刷新士兵列表
        node.Child('info/army_pawns').children.forEach(function (it, i) {
            var _a;
            var data = pawns[i], isId = typeof (data) === 'number', isCuring = !!data && !!(army === null || army === void 0 ? void 0 : army.curingPawns.some(function (m) { return m.uid === data.uid; }));
            it.Swih('none', !!data);
            if (data) {
                var icon = it.Child('icon');
                icon.opacity = (isId || isCuring) ? 120 : 255;
                ResHelper_1.resHelper.loadPawnHeadMiniIcon(isId ? data : (((_a = data.portrayal) === null || _a === void 0 ? void 0 : _a.id) || data.id), icon, _this.key);
                it.Child('lv', cc.Label).string = isId || data.lv <= 1 ? '' : data.lv + '';
            }
        });
        // 刷新按钮
        var buttons = node.Child('info/cond/need/buttons'), button = buttons.Child('state').Swih('cure_be')[0];
        button.Data = army ? (army.getActPawnCount() >= Constant_1.ARMY_PAWN_MAX_COUNT ? 1 : 0) : 2;
        button.opacity = !!item && button.Data ? 120 : 255;
        if (pawns.length < Constant_1.ARMY_PAWN_MAX_COUNT) {
            var uid_1 = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
            this.updateCureButton(uid_1, buttons.parent);
        }
    };
    // 刷新士兵列表
    BuildHospitalPnlCtrl.prototype.updateInjuryList = function (isLocation, node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var pawns = this.player.getInjuryPawns(), pawnCount = pawns.length;
        var mapObj = {};
        this.player.getCuringPawnsQueue().forEach(function (m) { return mapObj[m.uid] = true; });
        pawns.sort(function (a, b) {
            var aState = mapObj[a.uid] ? 1 : 0, bState = mapObj[b.uid] ? 1 : 0;
            if (aState !== bState) {
                return aState - bState;
            }
            switch (_this.currSelectSort) {
                case 0: // 兵种升序 > 等级降序 > 时间降序
                    if (a.id !== b.id)
                        return a.id - b.id;
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    return b.deadTime - a.deadTime;
                case 4: // 时间降序 > 兵种升序 > 等级降序 
                    if (a.deadTime !== b.deadTime)
                        return b.deadTime - a.deadTime;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.lv - a.lv;
                case 5: // 等级降序  > 兵种升序 > 时间降序
                    if (a.lv !== b.lv)
                        return b.lv - a.lv;
                    if (a.id !== b.id)
                        return a.id - b.id;
                    return b.deadTime - a.deadTime;
            }
            return b.lv - a.lv;
        });
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN), nextUid = selectUid;
        if (!pawns.some(function (m) { return m.uid === selectUid; }) || mapObj[selectUid]) { // 士兵治疗完成 或 士兵治疗中，需要自动切换治疗目标
            nextUid = 0;
        }
        var curingQueue = this.player.getCuringPawnsQueue();
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.Child('empty').active = !pawnCount;
        sv.Items(pawns, function (it, data, i) {
            var conf = _this.player.getConfigPawnInfo(data.id), pawn = new PawnObj_1.default().init(data.id, conf.equip, data.lv, conf.skinId);
            it.Data = { data: data, pawn: pawn };
            it.Child('icon').opacity = curingQueue.some(function (m) { return m.uid === data.uid; }) ? 120 : 255;
            it.Child('lv/val', cc.Label).string = data.lv + '';
            var iconNode = it.Child('icon');
            ResHelper_1.resHelper.loadPawnHeadIcon(conf.skinId || data.id, iconNode, _this.key);
            if (!isLocation && i >= _this.preSelectIndex && !nextUid && !mapObj[data.uid]) { // 初次进入不用框选士兵
                nextUid = _this.user.setTempPreferenceData(_this.PKEY_SELECT_PAWN, data.uid);
            }
        });
        // 没有找到下一个，就找上一个
        if (!nextUid) {
            for (var i = pawns.length - 1; i >= 0; i--) {
                var pawn = pawns[i];
                if (!isLocation && !nextUid && i <= this.preSelectIndex && !mapObj[pawn.uid]) { // 初次进入不用框选士兵
                    nextUid = this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, pawn.uid);
                }
            }
        }
        // 将选中的移动到中间
        if (this.preSelectIndex !== -1) {
            var lay = sv.content.Component(cc.Layout);
            lay.updateLayout();
            var width = sv.content.children[0].width;
            var tx = (width + lay.spacingX) * this.preSelectIndex + width * 0.5 + lay.paddingLeft; //当前位置
            var pw = sv.content.parent.width;
            var cx = pw * 0.5; //中间位置
            sv.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - sv.content.width), 0);
        }
        else {
            sv.scrollToLeft();
        }
        this.updatePawnSelect(isLocation, node);
        if (pawnCount <= 0) {
            this.updateCureCost();
        }
        // 刷新数量
        this.pagesNode_.Child('1/info/pawn/title/val').setLocaleKey('ui.select_wounded', pawnCount, Constant_1.HOSPITAL_PAWN_LIMIT);
    };
    BuildHospitalPnlCtrl.prototype.updatePawnSelect = function (isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        var selectUid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var selectIndex = -1;
        var sv = node.Child('info/pawn/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a, _b;
            /* const uid = m.Data?.data?.uid
            m.Child('bg/select').active = m.Child('select').active = uid === selectUid */
            var select = m.Child('bg/select').active = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.data.uid) === selectUid;
            m.Component(cc.Button).interactable = !select || !!((_b = m.Data) === null || _b === void 0 ? void 0 : _b.pawn);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
    };
    // 刷新治疗费用
    BuildHospitalPnlCtrl.prototype.updateCureCost = function (node) {
        var _a, _b;
        node = node || this.pagesNode_.Child(1);
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN);
        var data = (_b = (_a = node.Child('info/pawn/list', cc.ScrollView).Find(function (m) { var _a, _b; return ((_b = (_a = m.Data) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.uid) === uid; })) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.data;
        var need = node.Child('info/cond/need');
        need.Child('buttons/delete_be').active = !!data;
        if (!data) {
            var cost_1 = [Enums_1.CType.CEREAL, Enums_1.CType.CEREAL_C];
            need.Child('time/up').active = false;
            need.Child('time/val', cc.Label) /* .Color('#756963') */.string = '-';
            need.Child('cost').Items(cost_1 || [], function (it, cost) {
                if (it && cost) {
                    it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(cost);
                    it.Child('val', cc.Label) /* .Color('#756963') */.string = '-';
                }
            });
            var cureBtn = need.Child('buttons/state').Swih('cure_be')[0];
            cureBtn.opacity = 120;
            cureBtn.Child('val').setLocaleKey('ui.button_cure');
            return;
        }
        // 计算当前等级总耗时、总耗资
        var baseCfg = assetsMgr.getJsonData('pawnBase', data.id);
        var cost = [], cureTime = 0;
        for (var i = 0; i < data.lv; i++) {
            cost.pushArr(GameHelper_1.gameHpr.getPawnCost(data.id, i));
            cureTime += GameHelper_1.gameHpr.getPawnCostTime(data.id, i);
        }
        // 粮耗
        var crealCost = new CTypeObj_1.default().init(Enums_1.CType.CEREAL_C, 0, baseCfg.cereal_cost || 0);
        cost.push(crealCost);
        // 检测是否有治疗士兵费用增加
        var finalCost = [];
        GameHelper_1.gameHpr.mergeTypeObjsCount.apply(GameHelper_1.gameHpr, __spread([finalCost], cost));
        // 剔除经验书，后续用来加速
        for (var i = finalCost.length - 1; i >= 0; i--) {
            if (finalCost[i].type === Enums_1.CType.EXP_BOOK) {
                finalCost.splice(i, 1);
            }
        }
        // 计算比例
        finalCost.forEach(function (m) {
            if (m.type !== Enums_1.CType.CEREAL_C) {
                m.count = Math.max(Math.floor(Constant_1.CURE_RES_PARAM_MAP[data.lv] * m.count), 1);
            }
        });
        // 治疗消耗信息
        var cd = this.getCureTimeCD(), policyFreeCount = this.player.getFreeCurePawnSurplusCount();
        if (GameHelper_1.gameHpr.isNoviceMode) {
            cureTime = Math.floor(NoviceConfig_1.NOVICE_CURE_TIME_PARAM_MAP[data.lv] * cureTime);
            cd = GameHelper_1.gameHpr.noviceServer.getHospitaCureSpeedTime(cureTime, data.lv).cd;
            need.Child('time/up').active = true;
        }
        else {
            cureTime = Math.floor(Constant_1.CURE_TIME_PARAM_MAP[data.lv] * cureTime);
        }
        ViewHelper_1.viewHelper.updateFreeCostView(need, finalCost, cureTime, cd, false, policyFreeCount);
        need.Child('buttons/state/cure_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_cure' : 'ui.button_cure');
        // 刷新按钮
        this.updateCureButton(data.uid, need);
    };
    BuildHospitalPnlCtrl.prototype.updateCureButton = function (uid, node) {
        node = node || this.pagesNode_.Child('1/info/cond/need');
        var info = GameHelper_1.gameHpr.player.getCuringPawnsQueue().find(function (m) { return m.uid === uid; });
        var buttons = node.Child('buttons'), button = buttons.Child('state/cure_be');
        button.opacity = button.Data ? 120 : 255;
        buttons.Child('delete_be').opacity = !!info ? 120 : 255;
        if (info) { // 在治疗队列中
            buttons.Child('state').Swih('curing')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.army_state_6' : 'ui.queueing');
        }
        else {
            buttons.Child('state').Swih('cure_be');
        }
    };
    // 获取治疗减CDBuff
    BuildHospitalPnlCtrl.prototype.getCureTimeCD = function () {
        var _a;
        var cd = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_CD);
        if (((_a = this.data.effect) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.CEffect.CURE_CD) {
            cd += this.data.effect.value;
        }
        return cd * 0.01;
    };
    // 刷新治疗列表
    BuildHospitalPnlCtrl.prototype.updateCureQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getCuringPawnsQueue();
        this.upTimeNode_.active = false; //list.length > 0
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var pawnConf = this.player.getConfigPawnMap();
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CURE_QUEUE);
        node.Child('cure/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('cure/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1 || childrenCount < queueCount - 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var skinId = data ? (((_a = pawnConf[data.id]) === null || _a === void 0 ? void 0 : _a.skinId) || data.id) : 0;
            var has = it.Child('icon').active = it.Child('cure_pawn_be').active = !!data;
            (_b = it.Child('cancel_cure_be')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            (_c = it.Child('icon/progress')) === null || _c === void 0 ? void 0 : _c.setActive(has);
            it.Child('lv/val', cc.Label).string = data ? data.lv + '' : '';
            ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = data.getSurplusTime();
                time += stime;
                (_d = this.cureProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.cureProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                var st = stime * 0.001;
                it.Child('time', cc.LabelTimer).run(st);
                this.cureProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('cure/desc').active = time > 0;
        if (time > 0) {
            node.Child('cure/desc/title').setLocaleKey('ui.drill_all_desc', 'ui.button_cure');
            node.Child('cure/desc/time/val', cc.LabelTimer).run(time * 0.001);
        }
    };
    BuildHospitalPnlCtrl.prototype.showCreateArmyUI = function () {
        var _this = this;
        if (GameHelper_1.gameHpr.player.isArmyCountFull()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_FULL_ARMY);
        }
        this.showCreateArmyFingerTip(false);
        return ViewHelper_1.viewHelper.showPnl('common/CreateArmy', function (name) {
            if (_this.isValid) {
                _this.tempCreateArmy = new ArmyObj_1.default().init(_this.data.aIndex, GameHelper_1.gameHpr.getUid(), name);
                _this.tempArmySortWeightMap = {};
                if (!_this.selectArmy) {
                    _this.selectArmy = {};
                }
                _this.selectArmy.uid = _this.tempCreateArmy.uid;
                _this.updateArmyList(true, _this.pagesNode_.Child(1));
            }
        });
    };
    // 显示创建军队提示手指
    BuildHospitalPnlCtrl.prototype.showCreateArmyFingerTip = function (val) {
        return; //不显示手指
        var node = this.pagesNode_.Child(1);
        var sv = node.Child('army/list', cc.ScrollView), finger = sv.Child('finger');
        if (finger.active = val) {
            var count = sv.content.childrenCount;
            sv.stopAutoScroll();
            if (count >= 4) {
                sv.scrollToRight();
            }
            var it = sv.content.children[count - 1];
            var pos = ut.convertToNodeAR(it, sv.node);
            finger.setPosition(pos.x, pos.y - 12);
        }
    };
    // 取消治疗
    BuildHospitalPnlCtrl.prototype.cancelCure = function (info) {
        var _this = this;
        if (!this.data) {
            return;
        }
        var index = info.index;
        var uid = info.uid;
        var json = info.json;
        NetHelper_1.netHelper.reqCancelCurePawn({ index: index, uid: uid }).then(function (res) {
            var _a, _b;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.output);
                (_a = GameHelper_1.gameHpr.areaCenter.getArea(index)) === null || _a === void 0 ? void 0 : _a.updateArmyCurePawns(data.army);
                GameHelper_1.gameHpr.player.updatePawnCuringQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                _this.emit(EventType_1.default.UPDATE_PAWN_INJURY_QUEUE);
                if ((_b = data.needCost) === null || _b === void 0 ? void 0 : _b.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', {
                        text: 'ui.cancel_cure_tip',
                        id: json.id,
                        cost: data.needCost,
                    });
                }
            }
        });
    };
    BuildHospitalPnlCtrl = __decorate([
        ccclass
    ], BuildHospitalPnlCtrl);
    return BuildHospitalPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildHospitalPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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