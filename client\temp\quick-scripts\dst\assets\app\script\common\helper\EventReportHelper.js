
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/EventReportHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '27016kQpYZJj7+d/NRZ3GQp', 'EventReportHelper');
// app/script/common/helper/EventReportHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventReportHelper = void 0;
var Constant_1 = require("../constant/Constant");
var JsbEvent_1 = require("../event/JsbEvent");
var GameHelper_1 = require("./GameHelper");
var JsbHelper_1 = require("./JsbHelper");
// 事件上报
var EventReportHelper = /** @class */ (function () {
    function EventReportHelper() {
        this.recordMap = null;
    }
    EventReportHelper.prototype.reportEvent = function (type, eventId, params) {
        if (!ut.isMobile()) {
            return;
        }
        else if (GameHelper_1.gameHpr.isGLobal()) {
            if (params) {
                params.eventId = eventId;
                JsbHelper_1.jsbHelper.cast(type, null, params);
            }
            else {
                JsbHelper_1.jsbHelper.cast(type, null, { eventId: eventId });
            }
        }
    };
    // 海外facebook事件上报 
    EventReportHelper.prototype.reportFacebookEvent = function (eventId, params) {
        this.reportEvent(JsbEvent_1.default.FACEBOOK_EVENT, eventId, params);
    };
    // 海外firebase事件上报 
    EventReportHelper.prototype.reportFirebaseEvent = function (eventId, params) {
        this.reportEvent(JsbEvent_1.default.FIREBASE_EVENT, eventId, params);
    };
    // 海外appflyer事件上报
    EventReportHelper.prototype.reportAppflyerEvent = function (eventId, params) {
        this.reportEvent(JsbEvent_1.default.APPFLYER_EVENT, eventId, params);
    };
    EventReportHelper.prototype.checkRecord = function (eventId) {
        if (!this.recordMap) {
            this.recordMap = storageMgr.loadJson('event_report_record') || {};
        }
        return !!this.recordMap[eventId];
    };
    EventReportHelper.prototype.recordReport = function (eventId) {
        if (!this.checkRecord(eventId)) {
            this.recordMap[eventId] = true;
            storageMgr.saveJson('event_report_record', this.recordMap);
            return true;
        }
        return false;
    };
    // facebook
    EventReportHelper.prototype.reportFacebookEventOne = function (eventId, params) {
        if (this.recordReport(eventId)) {
            this.reportFacebookEvent(eventId, params);
        }
    };
    // firebase
    EventReportHelper.prototype.reportFirebaseEventOne = function (eventId, params) {
        if (this.recordReport(eventId)) {
            this.reportFirebaseEvent(eventId, params);
        }
    };
    // appflyer
    EventReportHelper.prototype.reportAppflyerEventOne = function (eventId, params) {
        if (this.recordReport(eventId)) {
            this.reportAppflyerEvent(eventId, params);
        }
    };
    EventReportHelper.prototype.reportGlobalEventOne = function (eventId, params) {
        if (this.recordReport(eventId)) {
            // this.reportFacebookEvent(eventId, params) //不再单独上报appflyer
            this.reportAppflyerEvent(eventId, params);
            this.reportFirebaseEvent(eventId, params);
        }
    };
    EventReportHelper.prototype.reportGlobalEvent = function (eventId, params) {
        this.reportFacebookEvent(eventId, params);
        this.reportFirebaseEvent(eventId, params);
        this.reportAppflyerEvent(eventId, params);
    };
    // 检测上报地块数
    EventReportHelper.prototype.checkReportOweCellCount = function () {
        var count = GameHelper_1.gameHpr.getPlayerOweCellCount();
        if (count >= 300) {
            this.reportGlobalEventOne('plot_300');
        }
        else if (count >= 250) {
            this.reportGlobalEventOne('plot_250');
        }
        else if (count >= 200) {
            this.reportGlobalEventOne('plot_200');
        }
        else if (count >= 150) {
            this.reportGlobalEventOne('plot_150');
        }
        else if (count >= 100) {
            this.reportGlobalEventOne('plot_100');
        }
        else if (count >= 80) {
            this.reportGlobalEventOne('plot_80');
        }
        else if (count >= 50) {
            this.reportGlobalEventOne('plot_50');
        }
        else if (count >= 20) {
            this.reportGlobalEventOne('plot_20');
        }
    };
    // 检测上报攻占地块
    EventReportHelper.prototype.checkReportOccupyCell = function () {
        var _a, _b, _c, _d, _e;
        var diffMap = GameHelper_1.gameHpr.getPlayerLandByDiffcult();
        // 攻占地狱1地块累计1个 hell1_plot_1
        // 攻占地狱1地块累计2个 hell1_plot_2
        // 攻占地狱1地块累计3个 hell1_plot_3
        this.reportOccupyCell(((_a = diffMap[6]) === null || _a === void 0 ? void 0 : _a[1]) || 0, 3, 'hell1_plot_');
        // 攻占地狱2地块累计1个 hell2_plot_1
        // 攻占地狱2地块累计2个 hell2_plot_2
        // 攻占地狱2地块累计3个 hell2_plot_3
        this.reportOccupyCell(((_b = diffMap[6]) === null || _b === void 0 ? void 0 : _b[2]) || 0, 3, 'hell2_plot_');
        // 攻占困难1地块累计1个 hard1_plot_1
        // 攻占困难1地块累计2个 hard1_plot_2
        // 攻占困难1地块累计3个 hard1_plot_3
        this.reportOccupyCell(((_c = diffMap[5]) === null || _c === void 0 ? void 0 : _c[1]) || 0, 3, 'hard1_plot_');
        // 攻占困难2地块累计1个 hard2_plot_1
        // 攻占困难2地块累计2个 hard2_plot_2
        // 攻占困难2地块累计3个 hard2_plot_3
        this.reportOccupyCell(((_d = diffMap[5]) === null || _d === void 0 ? void 0 : _d[2]) || 0, 3, 'hard2_plot_');
        // 攻占困难3地块累计1个 hard3_plot_1
        // 攻占困难3地块累计2个 hard3_plot_2
        // 攻占困难3地块累计3个 hard3_plot_3
        this.reportOccupyCell(((_e = diffMap[5]) === null || _e === void 0 ? void 0 : _e[3]) || 0, 3, 'hard3_plot_');
    };
    EventReportHelper.prototype.reportOccupyCell = function (count, times, key) {
        for (var i = 1; i <= times; i++) {
            if (count >= i) {
                this.reportGlobalEventOne(key + i);
            }
        }
    };
    // 检测上报武将获取
    EventReportHelper.prototype.checkReportGetPortrayal = function () {
        var count = GameHelper_1.gameHpr.user.getPortrayals().filter(function (m) { return m.isUnlock(); }).length;
        if (count >= 45) {
            this.reportGlobalEventOne('hero_45');
        }
        else if (count >= 40) {
            this.reportGlobalEventOne('hero_40');
        }
        else if (count >= 35) {
            this.reportGlobalEventOne('hero_35');
        }
        else if (count >= 30) {
            this.reportGlobalEventOne('hero_30');
        }
    };
    // 检测上报排位等级
    EventReportHelper.prototype.checkReportRankScore = function () {
        var score = GameHelper_1.gameHpr.user.getRankScore(), count = GameHelper_1.gameHpr.user.getAccTotalRankCount(), _a = GameHelper_1.gameHpr.resolutionRankScore(score, count), id = _a.id, winPoint = _a.winPoint;
        if (id >= 24) { // 王者
            this.reportGlobalEventOne('rank_ace');
        }
        else if (id >= 23) { // 大师1
            this.reportGlobalEventOne('rank_masterl');
        }
        else if (id >= 22) { // 大师2
            this.reportGlobalEventOne('rank_masterll');
        }
        else if (id >= 21) { // 大师3
            this.reportGlobalEventOne('rank_masterlll');
        }
        else if (id >= 20) { // 钻1
            this.reportGlobalEventOne('rank_diamondl');
        }
        else if (id >= 19) { // 钻2
            this.reportGlobalEventOne('rank_diamondll');
        }
        else if (id >= 18) { // 钻3
            this.reportGlobalEventOne('rank_diamondlll');
        }
    };
    // 检测上报礼包购买行为
    EventReportHelper.prototype.checkReportShopPackage = function (id) {
        if (id === Constant_1.RECHARGE_BATTLE_PASS) {
            this.reportGlobalEventOne('purchase_kit');
        }
    };
    return EventReportHelper;
}());
exports.eventReportHelper = new EventReportHelper();
if (cc.sys.isBrowser) {
    window['eventReportHelper'] = exports.eventReportHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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