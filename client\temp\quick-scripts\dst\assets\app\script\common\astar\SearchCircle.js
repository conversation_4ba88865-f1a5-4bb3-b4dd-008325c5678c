
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/SearchCircle.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f79b7QhHIlFhad1GPgtkQcN', 'SearchCircle');
// app/script/common/astar/SearchCircle.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 范围搜索
 */
var SearchCircle = /** @class */ (function () {
    function SearchCircle() {
        this.opened = []; //开放列表
        this.closed = {}; //关闭列表
        this.points = [];
        this.checkIsBattleArea = null; //检测方法
        this.otherPawns = {};
        this._temp_vec2_1 = cc.v2();
        this._temp_vec2_2 = cc.v2();
    }
    SearchCircle.prototype.init = function (checkIsBattleArea) {
        this.checkIsBattleArea = checkIsBattleArea;
        return this;
    };
    // 检查节点
    SearchCircle.prototype.findNode = function (tx, ty) {
        if (!this.checkIsBattleArea(tx, ty)) { //有障碍
            return;
        }
        var key = tx + '_' + ty;
        if (!this.closed[key]) {
            var point = cc.v2(tx, ty);
            this.closed[key] = true;
            this.opened.push(point);
            if (!this.otherPawns[key]) {
                this.points.push(point);
            }
        }
    };
    SearchCircle.prototype.search = function (start, count, otherPawns) {
        var _a;
        var _this = this;
        this.opened = [start];
        this.closed = (_a = {}, _a[start.ID()] = true, _a);
        this.points = [];
        this.otherPawns = otherPawns;
        var cnt = count + 2;
        // 开始搜寻
        while (this.opened.length > 0 && this.points.length <= cnt) {
            var p = this.opened.shift();
            this.findNode(p.x, p.y + 1); //上
            this.findNode(p.x + 1, p.y); //右
            this.findNode(p.x, p.y - 1); //下
            this.findNode(p.x - 1, p.y); //左
            this.opened.sort(function (a, b) { return start.sub(a, _this._temp_vec2_1).magSqr() - start.sub(b, _this._temp_vec2_2).magSqr(); });
        }
        if (this.points.length > count) {
            this.points.sort(function (a, b) { return start.sub(a, _this._temp_vec2_1).magSqr() - start.sub(b, _this._temp_vec2_2).magSqr(); });
            this.points.splice(count);
        }
        return this.points;
    };
    return SearchCircle;
}());
exports.default = SearchCircle;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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