
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PortrayalBaseInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6a891q7q2ZGFJaxLVqt1YKz', 'PortrayalBaseInfoPnlCtrl');
// app/script/view/common/PortrayalBaseInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PortrayalBaseInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PortrayalBaseInfoPnlCtrl, _super);
    function PortrayalBaseInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pictureNode_ = null; // path://picture_n
        _this.nameNode_ = null; // path://name/name_n
        _this.roootNode_ = null; // path://rooot_n
        _this.nextPortrayalNode_ = null; // path://next_portrayal_nbe_n
        _this.changeNode_ = null; // path://change_be_n
        //@end
        _this.json = null;
        _this.list = [];
        _this.curShowType = 1;
        return _this;
    }
    PortrayalBaseInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PortrayalBaseInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                return [2 /*return*/];
            });
        });
    };
    PortrayalBaseInfoPnlCtrl.prototype.onEnter = function (json, from) {
        if (!ut.isObject(json)) {
            json = assetsMgr.getJsonData('portrayalBase', json);
        }
        this.curShowType = 1;
        this.list = assetsMgr.getJson('portrayalBase').datas;
        if (!json) {
            json = this.list[0];
        }
        this.updateUI(json);
        this.nextPortrayalNode_.active = from !== 'shop';
    };
    PortrayalBaseInfoPnlCtrl.prototype.onRemove = function () {
    };
    PortrayalBaseInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://rooot_n/1/attrs/lock_strategy_be
    PortrayalBaseInfoPnlCtrl.prototype.onClickLockStrategy = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/StrategyListBox', ut.stringToNumbers(this.json.strategy), this.avatarPawnName);
    };
    // path://change_be_n
    PortrayalBaseInfoPnlCtrl.prototype.onClickChange = function (event, data) {
        this.changeShow(this.curShowType === 0 ? 1 : 0);
    };
    // path://next_portrayal_nbe_n
    PortrayalBaseInfoPnlCtrl.prototype.onClickNextPortrayal = function (event, _) {
        var _this = this;
        var index = this.list.findIndex(function (m) { return m.id === _this.json.id; });
        var add = event.target.name === '0' ? -1 : 1;
        var idx = ut.loopValue(index + add, this.list.length);
        var data = this.list[idx];
        if (data && this.json.id !== data.id) {
            this.updateUI(data);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 副名
    PortrayalBaseInfoPnlCtrl.prototype.getViceName = function (id) {
        if (assetsMgr.getJsonData('portrayalText', 'vice_' + id)) {
            return 'portrayalText.vice_' + id;
        }
        return '';
    };
    Object.defineProperty(PortrayalBaseInfoPnlCtrl.prototype, "avatarPawnName", {
        get: function () {
            return 'pawnText.name_' + this.json.avatar_pawn;
        },
        enumerable: false,
        configurable: true
    });
    PortrayalBaseInfoPnlCtrl.prototype.updateUI = function (json) {
        this.json = json;
        // 名字
        ViewHelper_1.viewHelper.showPortrayalName(this.nameNode_, 'portrayalText.name_' + json.id, this.getViceName(json.id));
        // 刷新立绘
        var iconNode = this.pictureNode_.Child('icon');
        ResHelper_1.resHelper.loadPortrayalImage(json.id, iconNode, this.key);
        iconNode.setPosition(ut.stringToVec2(json.ui_ssr_offset));
        // 属性
        this.changeShow(this.curShowType);
    };
    PortrayalBaseInfoPnlCtrl.prototype.changeShow = function (type) {
        this.curShowType = type;
        var node = this.roootNode_.Swih(type)[0];
        if (type === 0) {
            this.updateHero(node);
        }
        else if (type === 1) {
            this.updateAttr(node);
        }
        this.changeNode_.Child('val', cc.MultiFrame).setFrame(type);
    };
    // 刷新属性
    PortrayalBaseInfoPnlCtrl.prototype.updateAttr = function (node) {
        var _this = this;
        node = node || this.roootNode_.Child(1);
        var json = this.json;
        var root = node.Swih('attrs')[0];
        // 名字
        ResHelper_1.resHelper.loadHeroSkillIcon(json.skill, root.Child('icon/val'), this.key);
        root.Child('icon/name/val').setLocaleKey('portrayalSkillText.name_' + json.skill);
        root.Child('icon/name/pawn').setLocaleKey('ui.avatar_pawn_desc', this.avatarPawnName);
        // 属性
        root.Child('attr').Items([1, 2], function (it, type) {
            it.Child('icon', cc.MultiFrame).setFrame(type - 1);
            _this.getMainAttrRangeValue(type).forEach(function (v, i) { return it.Child('base/' + i, cc.Label).string = v + ''; });
        });
        // 技能
        root.Child('skill').setLocaleKey('portrayalSkillText.desc_' + json.skill, GameHelper_1.gameHpr.getPortrayalSkillDescParams(json.skill));
    };
    PortrayalBaseInfoPnlCtrl.prototype.getMainAttrRangeValue = function (type) {
        if (type === 1) {
            return ut.stringToNumbers(this.json.hp, ',');
        }
        else if (type === 2) {
            return ut.stringToNumbers(this.json.attack, ',');
        }
        return [0, 0];
    };
    // 刷新角色
    PortrayalBaseInfoPnlCtrl.prototype.updateHero = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var root, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        node = node || this.roootNode_.Child(0);
                        root = node.Child('root');
                        root.removeAllChildren();
                        return [4 /*yield*/, ResHelper_1.resHelper.loadHeroMarchPrefab(this.json.id, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!this.isValid || !pfb) {
                            return [2 /*return*/];
                        }
                        else if (pfb.name !== 'ROLE_' + this.json.id) {
                            return [2 /*return*/];
                        }
                        root.removeAllChildren();
                        node = cc.instantiate2(pfb, root);
                        node.Child('body/anim', cc.Animation).play("role_" + this.json.id + "_walk");
                        return [2 /*return*/];
                }
            });
        });
    };
    PortrayalBaseInfoPnlCtrl = __decorate([
        ccclass
    ], PortrayalBaseInfoPnlCtrl);
    return PortrayalBaseInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PortrayalBaseInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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