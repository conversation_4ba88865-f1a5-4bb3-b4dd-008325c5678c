{"version": 3, "sources": ["assets\\app\\script\\model\\guide\\GuideConfig.ts"], "names": [], "mappings": ";;;;;;;AAAA,2DAAoE;AACpE,0DAAqD;AAErD,OAAO;AACP,IAAK,aAQJ;AARD,WAAK,aAAa;IACd,iDAAI,CAAA;IACJ,mDAAK,CAAA;IACL,qDAAM,CAAA;IACN,yDAAQ,CAAA;IACR,mEAAa,CAAA;IACb,mDAAK,CAAA;IACL,6DAAU,CAAA;AACd,CAAC,EARI,aAAa,KAAb,aAAa,QAQjB;AAq0EG,sCAAa;AAn0EjB,OAAO;AACP,IAAK,YAgEJ;AAhED,WAAK,YAAY;IACb,uDAAuC,CAAA;IACvC,uDAAuC,CAAA;IACvC,uDAAuC,CAAA;IACvC,uDAAuC,CAAA;IACvC,6DAA6C,CAAA;IAC7C,iEAAiD,CAAA;IACjD,iDAAiC,CAAA;IACjC,mEAAmD,CAAA;IACnD,uDAAuC,CAAA;IACvC,+DAA+C,CAAA;IAC/C,qDAAqC,CAAA;IACrC,2DAA2C,CAAA;IAC3C,uDAAuC,CAAA;IACvC,qDAAqC,CAAA;IAErC,yEAAyD,CAAA;IACzD,qEAAqD,CAAA;IAErD,2DAA2C,CAAA;IAC3C,mEAAmD,CAAA;IACnD,+DAA+C,CAAA;IAE/C,iEAAiD,CAAA;IAEjD,mEAAmD,CAAA;IACnD,yEAAyD,CAAA;IAEzD,iEAAiD,CAAA;IACjD,6DAA6C,CAAA;IAE7C,iDAAiC,CAAA;IAEjC,yDAAyC,CAAA;IACzC,qDAAqC,CAAA;IACrC,2DAA2C,CAAA;IAE3C,yDAAyC,CAAA;IACzC,qDAAqC,CAAA;IAErC,mDAAmC,CAAA;IACnC,yDAAyC,CAAA;IACzC,uDAAuC,CAAA;IAEvC,qDAAqC,CAAA;IACrC,iDAAiC,CAAA;IAEjC,2DAA2C,CAAA;IAE3C,6DAA6C,CAAA;IAC7C,yDAAyC,CAAA;IACzC,yEAAyD,CAAA;IAEzD,iEAAiD,CAAA;IACjD,6DAA6C,CAAA;IAC7C,+DAA+C,CAAA;IAC/C,uEAAuD,CAAA;IAEvD,iEAAiD,CAAA;IAEjD,qEAAqD,CAAA;IAErD,2EAA2D,CAAA;IAC3D,+DAA+C,CAAA;AACnD,CAAC,EAhEI,YAAY,KAAZ,YAAY,QAgEhB;AAmwEG,oCAAY;AA/uEhB,IAAK,iBASJ;AATD,WAAK,iBAAiB;IAClB,yDAAQ,CAAA;IACR,2EAAa,CAAA;IACb,mFAAiB,CAAA;IAEjB,SAAS;IACT,8EAAqB,CAAA;IACrB,SAAS;IACT,gFAAsB,CAAA;AAC1B,CAAC,EATI,iBAAiB,KAAjB,iBAAiB,QASrB;AAwuEG,8CAAiB;AAtuErB,IAAM,cAAc,GAAG;IACnB,EAAE,EAAE,CAAC;IACL,SAAS,EAAE;QACP,IAAI,EAAE,qBAAqB;KAC9B;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,KAAK;SACjB;QACD,wBAAwB;QACxB;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;gBACvC,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,qBAAqB;aAC9B;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,yBAAyB;aAClC;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,qBAAqB;aAC9B;YACD,YAAY,EAAE,YAAY,CAAC,gBAAgB;YAC3C,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,gBAAgB;YAClC,OAAO,EAAE,KAAK;SACjB;KACJ;CACJ,CAAA;AAED,SAAS;AACT,6BAA6B;AAC7B,aAAa;AACb,mBAAmB;AACnB,uCAAuC;AACvC,SAAS;AACT,oBAAoB;AACpB,eAAe;AACf,YAAY;AACZ,yCAAyC;AACzC,6BAA6B;AAC7B,uCAAuC;AACvC,wBAAwB;AACxB,wCAAwC;AACxC,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,wDAAwD;AACxD,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,uDAAuD;AACvD,iBAAiB;AACjB,4BAA4B;AAC5B,gDAAgD;AAChD,uCAAuC;AACvC,4BAA4B;AAC5B,6CAA6C;AAC7C,gCAAgC;AAChC,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,yCAAyC;AACzC,oDAAoD;AACpD,iBAAiB;AACjB,4BAA4B;AAC5B,4EAA4E;AAC5E,8BAA8B;AAC9B,4BAA4B;AAC5B,6CAA6C;AAC7C,gCAAgC;AAChC,qBAAqB;AACrB,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,yDAAyD;AACzD,iBAAiB;AACjB,yBAAyB;AACzB,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,oDAAoD;AACpD,iBAAiB;AACjB,4BAA4B;AAC5B,uDAAuD;AACvD,4BAA4B;AAC5B,8CAA8C;AAC9C,iCAAiC;AACjC,iCAAiC;AACjC,qBAAqB;AACrB,0DAA0D;AAC1D,8CAA8C;AAC9C,iBAAiB;AACjB,sCAAsC;AACtC,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,oDAAoD;AACpD,iBAAiB;AACjB,4BAA4B;AAC5B,uDAAuD;AACvD,sCAAsC;AACtC,4BAA4B;AAC5B,8CAA8C;AAC9C,iCAAiC;AACjC,iCAAiC;AACjC,qBAAqB;AACrB,0DAA0D;AAC1D,8CAA8C;AAC9C,iBAAiB;AACjB,sCAAsC;AACtC,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,0BAA0B;AAC1B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,yCAAyC;AACzC,oDAAoD;AACpD,iBAAiB;AACjB,4BAA4B;AAC5B,yDAAyD;AACzD,8BAA8B;AAC9B,4BAA4B;AAC5B,6CAA6C;AAC7C,gCAAgC;AAChC,oBAAoB;AACpB,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,yDAAyD;AACzD,iBAAiB;AACjB,sDAAsD;AACtD,uCAAuC;AACvC,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,0DAA0D;AAC1D,yCAAyC;AACzC,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,mDAAmD;AACnD,uBAAuB;AACvB,uDAAuD;AACvD,iBAAiB;AACjB,4BAA4B;AAC5B,8CAA8C;AAC9C,4BAA4B;AAC5B,4CAA4C;AAC5C,kCAAkC;AAClC,qBAAqB;AACrB,uCAAuC;AACvC,8BAA8B;AAC9B,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,0CAA0C;AAC1C,yCAAyC;AACzC,iBAAiB;AACjB,4BAA4B;AAC5B,2EAA2E;AAC3E,8BAA8B;AAC9B,4BAA4B;AAC5B,4CAA4C;AAC5C,kCAAkC;AAClC,qBAAqB;AACrB,mCAAmC;AACnC,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,0DAA0D;AAC1D,0CAA0C;AAC1C,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,6DAA6D;AAC7D,2DAA2D;AAC3D,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,yCAAyC;AACzC,wDAAwD;AACxD,iBAAiB;AACjB,4BAA4B;AAC5B,qEAAqE;AACrE,8BAA8B;AAC9B,4BAA4B;AAC5B,4CAA4C;AAC5C,kCAAkC;AAClC,qBAAqB;AACrB,oCAAoC;AACpC,iBAAiB;AACjB,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,0BAA0B;AAC1B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,yCAAyC;AACzC,wDAAwD;AACxD,iBAAiB;AACjB,4BAA4B;AAC5B,qFAAqF;AACrF,iBAAiB;AACjB,wCAAwC;AACxC,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,0DAA0D;AAC1D,0DAA0D;AAC1D,0DAA0D;AAC1D,iBAAiB;AACjB,oDAAoD;AACpD,qCAAqC;AACrC,aAAa;AACb,YAAY;AACZ,wCAAwC;AACxC,kDAAkD;AAClD,yCAAyC;AACzC,aAAa;AACb,QAAQ;AACR,IAAI;AAEJ,yBAAyB;AACzB,iBAAiB;AACjB,mBAAmB;AACnB,sBAAsB;AACtB,SAAS;AACT,mBAAmB;AACnB,eAAe;AACf,gBAAgB;AAChB,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,6DAA6D;AAC7D,4DAA4D;AAC5D,iBAAiB;AACjB,2CAA2C;AAC3C,aAAa;AACb,gBAAgB;AAChB,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,0DAA0D;AAC1D,iEAAiE;AACjE,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,uDAAuD;AACvD,iBAAiB;AACjB,6CAA6C;AAC7C,YAAY;AACZ,QAAQ;AACR,IAAI;AAEJ,4BAA4B;AAC5B,iBAAiB;AACjB,mBAAmB;AACnB,sBAAsB;AACtB,SAAS;AACT,mBAAmB;AACnB,eAAe;AACf,gBAAgB;AAChB,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,6DAA6D;AAC7D,4DAA4D;AAC5D,iBAAiB;AACjB,mDAAmD;AACnD,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,uDAAuD;AACvD,iBAAiB;AACjB,qDAAqD;AACrD,YAAY;AACZ,QAAQ;AACR,IAAI;AAEJ,SAAS;AACT,2BAA2B;AAC3B,aAAa;AACb,mBAAmB;AACnB,sCAAsC;AACtC,wBAAwB;AACxB,SAAS;AACT,oBAAoB;AACpB,eAAe;AACf,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,8DAA8D;AAC9D,6DAA6D;AAC7D,iBAAiB;AACjB,uCAAuC;AACvC,wBAAwB;AACxB,yCAAyC;AACzC,aAAa;AACb,gBAAgB;AAChB,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,kEAAkE;AAClE,qDAAqD;AACrD,iBAAiB;AACjB,uCAAuC;AACvC,aAAa;AACb,YAAY;AACZ,wCAAwC;AACxC,uCAAuC;AACvC,yCAAyC;AACzC,YAAY;AACZ,QAAQ;AACR,IAAI;AAEJ,UAAU;AACV,IAAM,eAAe,GAAG;IACpB,EAAE,EAAE,CAAC;IACL,SAAS,EAAE;QACP,IAAI,EAAE,qBAAqB;KAC9B;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,kCAAkC,EAAE;aAC/C;YACD,IAAI,EAAE,IAAI;YACV,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,0BAA0B;gBAChC,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,2BAA2B,EAAE;aAC1D;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,kCAAkC,EAAE;gBAC5C,EAAE,IAAI,EAAE,kCAAkC,EAAE;aAC/C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;aAClC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,uDAAuD;aAChE;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,qBAAqB;aAC9B;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,qCAAqC;gBAC3C,UAAU,EAAE,IAAI;aACnB;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,sBAAsB;aAC/B;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,kCAAkC,EAAE;aAC/C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,wBAAwB;aACjC;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE;aACnC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,uDAAuD;gBAC7D,IAAI,EAAE,uCAAuC;aAChD;YACD,GAAG,EAAE,YAAY,CAAC,2BAA2B;YAC7C,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,sBAAsB,EAAE;aACxC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,wEAAwE;aACjF;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;SACZ;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,gBAAgB;aACzB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,iCAAiC;aAC1C;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,kCAAkC,EAAE,IAAI,EAAE,iBAAiB,CAAC,aAAa,EAAE;gBACnF,EAAE,IAAI,EAAE,kCAAkC,EAAE,IAAI,EAAE,iBAAiB,CAAC,aAAa,EAAE;gBACnF,EAAE,IAAI,EAAE,kCAAkC,EAAE;aAC/C;YACD,YAAY,EAAE,YAAY,CAAC,qBAAqB;YAChD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,4BAA4B;aACrC;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,qBAAqB;aAC9B;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,4EAA4E;gBAClF,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;iBACxB;gBACD,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI;aACnB;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,qBAAqB;YACvC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,WAAW;AACX,IAAM,iBAAiB,GAAG;IACtB,EAAE,EAAE,CAAC;IACL,SAAS,EAAE;QACP,IAAI,EAAE,4BAA4B;KACrC;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,YAAY,CAAC,0BAA0B;YACrD,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,KAAK;SACjB;QACD,SAAS;QACT;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,qBAAqB;aAC9B;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;aACtC;YACD,GAAG,EAAE,YAAY,CAAC,0BAA0B;YAC5C,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;aACtC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,2BAA2B;gBACjC,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBACrB,KAAK,EAAE,CAAC;iBACX;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE;aACnC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,gDAAgD;aACzD;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,IAAI;SACb;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,gBAAgB;aACnC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE;gBACnB,IAAI,EAAE,gCAAgC;gBACtC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC;aAC7B;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE;aACnC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,+BAA+B;gBACrC,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBACrB,KAAK,EAAE,CAAC;iBACX;aACJ;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;aACtC;YACD,GAAG,EAAE,YAAY,CAAC,wBAAwB;YAC1C,OAAO,EAAE,KAAK;SACjB;KACJ;CACJ,CAAA;AAED,QAAQ;AACR,IAAM,WAAW,GAAG;IAChB,EAAE,EAAE,CAAC;IACL,SAAS,EAAE;QACP,IAAI,EAAE,iBAAiB;QACvB,OAAO,EAAE,IAAI;KAChB;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,KAAK;SACjB;QACD,WAAW;QACX;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,kBAAkB,EAAE;aACjD;YACD,OAAO,EAAE,KAAK;SACjB;QACD,WAAW;QACX;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,eAAe,EAAE;aAC9C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;aACtC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,oBAAoB;gBAC1B,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;oBACpB,KAAK,EAAE,GAAG;iBACb;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,KAAK;SACjB;QACD,OAAO;QACP;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;aACxB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,iDAAiD;gBACvD,UAAU,EAAE,IAAI;aACnB;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,mBAAmB,EAAE;aACrC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,2CAA2C;aACpD;YACD,GAAG,EAAE,YAAY,CAAC,eAAe;YACjC,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,wCAAwC;aACjD;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,kBAAkB,EAAE;aAC5D;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,mBAAmB,EAAE;aACrC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,qEAAqE;aAC9E;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,qBAAqB;aAC9B;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,0BAA0B;aACnC;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;SACZ;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,mBAAmB;aAC5B;YACD,GAAG,EAAE,YAAY,CAAC,kBAAkB;YACpC,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,oEAAoE;aAC7E;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,+BAA+B;aACxC;YACD,YAAY,EAAE,YAAY,CAAC,iBAAiB;YAC5C,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,iBAAiB;YACnC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,MAAM;AACN,IAAM,cAAc,GAAG;IACnB,EAAE,EAAE,CAAC;IACL,SAAS,EAAE;QACP,IAAI,EAAE,oBAAoB;KAC7B;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,YAAY,CAAC,sBAAsB;YACjD,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;gBACvC,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,GAAG,EAAE,YAAY,CAAC,sBAAsB;YACxC,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,cAAc,EAAE;aAC7C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;aAC1B;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,sCAAsC;aAC/C;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;aACtC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,gBAAgB;gBACtB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;oBACpB,KAAK,EAAE,GAAG;iBACb;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;aACxB;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,iDAAiD;gBACvD,UAAU,EAAE,IAAI;aACnB;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;aACjC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,aAAa;aACtB;YACD,GAAG,EAAE,YAAY,CAAC,cAAc;YAChC,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,kDAAkD;aAC3D;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,6EAA6E;aACtF;YACD,OAAO,EAAE,KAAK;SACjB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,6BAA6B,EAAE;aAC1C;YACD,YAAY,EAAE,YAAY,CAAC,oBAAoB;YAC/C,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,oBAAoB;YACtC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,MAAM;AACN,6BAA6B;AAC7B,cAAc;AACd,mBAAmB;AACnB,wCAAwC;AACxC,SAAS;AACT,oBAAoB;AACpB,eAAe;AACf,YAAY;AACZ,yCAAyC;AACzC,6BAA6B;AAC7B,iEAAiE;AACjE,wBAAwB;AACxB,8BAA8B;AAC9B,aAAa;AACb,oBAAoB;AACpB,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,+CAA+C;AAC/C,iBAAiB;AACjB,wDAAwD;AACxD,8BAA8B;AAC9B,aAAa;AACb,sBAAsB;AACtB,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,8CAA8C;AAC9C,8DAA8D;AAC9D,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,mDAAmD;AACnD,uBAAuB;AACvB,uDAAuD;AACvD,iBAAiB;AACjB,4BAA4B;AAC5B,8CAA8C;AAC9C,oCAAoC;AACpC,4BAA4B;AAC5B,4CAA4C;AAC5C,kCAAkC;AAClC,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,kBAAkB;AAClB,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,0CAA0C;AAC1C,yCAAyC;AACzC,iBAAiB;AACjB,4BAA4B;AAC5B,2EAA2E;AAC3E,mCAAmC;AACnC,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,yDAAyD;AACzD,wDAAwD;AACxD,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,mDAAmD;AACnD,wDAAwD;AACxD,iBAAiB;AACjB,4BAA4B;AAC5B,qEAAqE;AACrE,iBAAiB;AACjB,iDAAiD;AACjD,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,oEAAoE;AACpE,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,mDAAmD;AACnD,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,4EAA4E;AAC5E,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,oFAAoF;AACpF,4BAA4B;AAC5B,4CAA4C;AAC5C,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,yBAAyB;AACzB,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,wCAAwC;AACxC,iBAAiB;AACjB,4BAA4B;AAC5B,0DAA0D;AAC1D,iBAAiB;AACjB,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,wCAAwC;AACxC,iBAAiB;AACjB,yDAAyD;AACzD,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,8FAA8F;AAC9F,iBAAiB;AACjB,yDAAyD;AACzD,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,wCAAwC;AACxC,gDAAgD;AAChD,+BAA+B;AAC/B,aAAa;AACb,QAAQ;AACR,IAAI;AAEJ,4BAA4B;AAC5B,cAAc;AACd,mBAAmB;AACnB,uCAAuC;AACvC,SAAS;AACT,oBAAoB;AACpB,eAAe;AACf,YAAY;AACZ,yCAAyC;AACzC,6BAA6B;AAC7B,iEAAiE;AACjE,wBAAwB;AACxB,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,+CAA+C;AAC/C,iBAAiB;AACjB,kDAAkD;AAClD,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,sBAAsB;AACtB,0CAA0C;AAC1C,6DAA6D;AAC7D,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,wBAAwB;AACxB,aAAa;AACb,YAAY;AACZ,qDAAqD;AACrD,uBAAuB;AACvB,0CAA0C;AAC1C,2CAA2C;AAC3C,iBAAiB;AACjB,4BAA4B;AAC5B,gEAAgE;AAChE,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,uDAAuD;AACvD,iBAAiB;AACjB,4BAA4B;AAC5B,iDAAiD;AACjD,oCAAoC;AACpC,4BAA4B;AAC5B,4CAA4C;AAC5C,kCAAkC;AAClC,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,0CAA0C;AAC1C,yCAAyC;AACzC,iBAAiB;AACjB,4BAA4B;AAC5B,2EAA2E;AAC3E,mCAAmC;AACnC,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,mDAAmD;AACnD,kDAAkD;AAClD,iBAAiB;AACjB,4BAA4B;AAC5B,6CAA6C;AAC7C,iBAAiB;AACjB,gDAAgD;AAChD,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,mDAAmD;AACnD,0DAA0D;AAC1D,iBAAiB;AACjB,4BAA4B;AAC5B,oEAAoE;AACpE,4BAA4B;AAC5B,6CAA6C;AAC7C,gCAAgC;AAChC,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,gFAAgF;AAChF,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,yCAAyC;AACzC,wBAAwB;AACxB,aAAa;AACb,YAAY;AACZ,4CAA4C;AAC5C,uBAAuB;AACvB,2DAA2D;AAC3D,iBAAiB;AACjB,4BAA4B;AAC5B,yEAAyE;AACzE,4BAA4B;AAC5B,6CAA6C;AAC7C,gCAAgC;AAChC,qBAAqB;AACrB,8BAA8B;AAC9B,iBAAiB;AACjB,8BAA8B;AAC9B,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,wDAAwD;AACxD,iBAAiB;AACjB,yDAAyD;AACzD,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,0CAA0C;AAC1C,yBAAyB;AACzB,wDAAwD;AACxD,iBAAiB;AACjB,yDAAyD;AACzD,+BAA+B;AAC/B,aAAa;AACb,YAAY;AACZ,wCAAwC;AACxC,gDAAgD;AAChD,+BAA+B;AAC/B,aAAa;AACb,QAAQ;AACR,IAAI;AAEJ,SAAS;AACT,IAAM,aAAa,GAAG;IAClB,EAAE,EAAE,EAAE;IACN,SAAS,EAAE;QACP,IAAI,EAAE,0BAA0B;QAChC,OAAO,EAAE,IAAI;KAChB;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,gCAAgC,EAAE;gBAC1C,EAAE,IAAI,EAAE,gCAAgC,EAAE;aAC7C;YACD,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,eAAe;aACxB;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;aAClC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,sCAAsC;gBAC5C,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;oBACpB,KAAK,EAAE,GAAG;iBACb;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,4BAA4B;aACrC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,yCAAyC;gBAC/C,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;oBACpB,KAAK,EAAE,GAAG;iBACb;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;aAC1B;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,8CAA8C;gBACpD,MAAM,EAAE;oBACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;oBACpB,KAAK,EAAE,GAAG;iBACb;gBACD,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;aACzB;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,mBAAmB;YACrC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,OAAO;AACP,IAAM,aAAa,GAAG;IAClB,EAAE,EAAE,EAAE;IACN,SAAS,EAAE;QACP,IAAI,EAAE,0BAA0B;QAChC,OAAO,EAAE,IAAI;KAChB;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,YAAY,CAAC,oBAAoB;YAC/C,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,+BAA+B,EAAE;gBACzC,EAAE,IAAI,EAAE,+BAA+B,EAAE;gBACzC,EAAE,IAAI,EAAE,+BAA+B,EAAE;aAC5C;YACD,YAAY,EAAE,YAAY,CAAC,kBAAkB;YAC7C,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,mCAAmC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,qBAAqB,EAAE,CAAC;aAC9G;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,kBAAkB,EAAE;aACpC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,0BAA0B;gBAChC,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,kBAAkB;YACpC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,QAAQ;AACR,IAAM,cAAc,GAAG;IACnB,EAAE,EAAE,EAAE;IACN,SAAS,EAAE;QACP,IAAI,EAAE,2BAA2B;KACpC;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,YAAY,CAAC,sBAAsB;YACjD,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,iCAAiC,EAAE;aAC9C;YACD,GAAG,EAAE,YAAY,CAAC,sBAAsB;YACxC,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,wBAAwB;gBAC9B,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,qBAAqB,EAAE;aACpD;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;aACjC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,+CAA+C;gBACrD,IAAI,EAAE,sCAAsC;aAC/C;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,qBAAqB,EAAE;aACvC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,wBAAwB;gBAC9B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;aACjC;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,8BAA8B;gBACpC,IAAI,EAAE,EAAE,GAAG,EAAE,YAAY,CAAC,yBAAyB,EAAE;aACxD;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;aACjC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,8CAA8C;gBACpD,IAAI,EAAE,sCAAsC;aAC/C;YACD,GAAG,EAAE,YAAY,CAAC,qBAAqB;YACvC,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,qBAAqB,EAAE;aACvC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,wBAAwB;gBAC9B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;aACjC;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;aAC1C;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,4BAA4B;aACrC;YACD,GAAG,EAAE,YAAY,CAAC,yBAAyB;YAC3C,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,YAAY,CAAC,oBAAoB;YAC/C,OAAO,EAAE,OAAO;SACnB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,iCAAiC,EAAE,IAAI,EAAE,iBAAiB,CAAC,iBAAiB,EAAE;aACzF;YACD,YAAY,EAAE,YAAY,CAAC,oBAAoB;YAC/C,OAAO,EAAE,OAAO;SACnB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,oBAAoB;YACtC,OAAO,EAAE,OAAO;SACnB;KACJ;CACJ,CAAA;AAED,IAAM,gBAAgB,GAAG;IACrB,EAAE,EAAE,EAAE;IACN,SAAS,EAAE;QACP,IAAI,EAAE,6BAA6B;KACtC;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,mCAAmC,EAAE;gBAC7C,EAAE,IAAI,EAAE,mCAAmC,EAAE;aAChD;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,mCAAmC,EAAE,IAAI,EAAE,mCAAmC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACvH;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,QAAQ;YAC5B,KAAK,EAAE;gBACH,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,kBAAkB,EAAE;aACpC;YACD,UAAU,EAAE;gBACR,IAAI,EAAE,0BAA0B;gBAChC,IAAI,EAAE,IAAI;aACb;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,sBAAsB;YACxC,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAED,IAAM,mBAAmB,GAAG;IACxB,EAAE,EAAE,EAAE;IACN,SAAS,EAAE;QACP,IAAI,EAAE,gCAAgC;KACzC;IACD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE;QACH;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE,GAAG;YACT,YAAY,EAAE,GAAG;YACjB,GAAG,EAAE,GAAG;YACR,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,mCAAmC,EAAE;gBAC7C,EAAE,IAAI,EAAE,mCAAmC,EAAE;gBAC7C,EAAE,IAAI,EAAE,mCAAmC,EAAE;aAChD;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,KAAK;YACzB,IAAI,EAAE;gBACF,IAAI,EAAE,yBAAyB;gBAC/B,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;aAC7B;YACD,YAAY,EAAE,YAAY,CAAC,wBAAwB;YACnD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,MAAM;YAC1B,OAAO,EAAE;gBACL,EAAE,IAAI,EAAE,mCAAmC,EAAE;aAChD;YACD,OAAO,EAAE,MAAM;SAClB;QACD;YACI,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,GAAG,EAAE,YAAY,CAAC,wBAAwB;YAC1C,OAAO,EAAE,MAAM;SAClB;KACJ;CACJ,CAAA;AAGD,KAAK;AACL,IAAM,YAAY,GAAG;IAEjB,KAAK,EAAE;QACH;YACI,EAAE,EAAE,CAAC;YACL,SAAS,EAAE;gBACP,IAAI,EAAE,mBAAmB;aAC5B;YACD,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE;gBACH;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,GAAG;oBACT,YAAY,EAAE,YAAY,CAAC,iBAAiB;oBAC5C,GAAG,EAAE,GAAG;oBACR,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;wBACnC,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,2BAA2B;qBAC9C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,oBAAoB;wBAC1B,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;4BACpB,KAAK,EAAE,GAAG;yBACb;qBACJ;oBACD,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,0BAA0B;qBACnC;oBACD,YAAY,EAAE,YAAY,CAAC,iBAAiB;oBAC5C,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,CAAC;iBACV;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,YAAY,EAAE,YAAY,CAAC,iBAAiB;oBAC5C,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;qBACtC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,mBAAmB;wBACzB,IAAI,EAAE,2BAA2B;wBACjC,UAAU,EAAE,IAAI;wBAChB,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACrB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE;qBACnC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,kDAAkD;qBAC3D;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,IAAI;iBACb;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,gBAAgB;qBACnC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,gBAAgB;wBACtB,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC;4BACtB,KAAK,EAAE,EAAE;4BACT,IAAI,EAAE,IAAI;yBACb;qBACJ;oBACD,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE;qBACnC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,+BAA+B;qBACxC;oBACD,OAAO,EAAE,KAAK;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE,IAAI,EAAE,2BAA2B,EAAE;qBACzE;oBACD,YAAY,EAAE,YAAY,CAAC,oBAAoB;oBAC/C,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,6BAA6B;qBACtC;oBACD,GAAG,EAAE,YAAY,CAAC,oBAAoB;oBACtC,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;qBACtC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,mBAAmB;wBACzB,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACrB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;qBACxB;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,iDAAiD;wBACvD,UAAU,EAAE,IAAI;qBACnB;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,GAAG;iBACZ;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE,IAAI,EAAE,2BAA2B,EAAE;qBACzE;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,2BAA2B;qBACpC;oBACD,YAAY,EAAE,YAAY,CAAC,sBAAsB;oBACjD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;qBAClC;oBACD,GAAG,EAAE,YAAY,CAAC,sBAAsB;oBACxC,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,GAAG;iBACZ;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;wBACnC,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,yBAAyB;wBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;qBACjC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,+CAA+C;qBACxD;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,yBAAyB;wBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,qBAAqB,EAAE;qBACvC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,wBAAwB;wBAC9B,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;4BACpB,KAAK,EAAE,GAAG;yBACb;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,yBAAyB;qBAClC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;qBACtC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,+DAA+D;wBACrE,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACrB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,oBAAoB;qBACvC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,gEAAgE;wBACtE,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACrB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,GAAG,EAAE,YAAY,CAAC,cAAc;oBAChC,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,kCAAkC;qBAC3C;oBACD,GAAG,EAAE,YAAY,CAAC,uBAAuB;oBACzC,YAAY,EAAE,YAAY,CAAC,iBAAiB;oBAC5C,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,iBAAiB;qBAC1B;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;qBAC1B;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,sCAAsC;qBAC/C;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,UAAU;oBAC9B,IAAI,EAAE;wBACF,IAAI,EAAE,wBAAwB;wBAC9B,IAAI,EAAE,6BAAkB;qBAC3B;oBACD,GAAG,EAAE,YAAY,CAAC,qBAAqB;oBACvC,OAAO,EAAE,MAAM;iBAClB;gBACD,OAAO;gBACP;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;qBACtC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,oBAAoB;wBAC1B,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;4BACpB,KAAK,EAAE,GAAG;yBACb;qBACJ;oBACD,GAAG,EAAE,YAAY,CAAC,qBAAqB;oBACvC,OAAO,EAAE,MAAM,CAAC,MAAM;iBACzB;gBACD,OAAO;gBACP;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;qBACxB;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,iDAAiD;wBACvD,UAAU,EAAE,IAAI;qBACnB;oBACD,OAAO,EAAE,MAAM,CAAC,MAAM;iBACzB,EAAE;oBACC,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;qBAClC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,2CAA2C;qBACpD;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,IAAI;iBACb;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;qBAClC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,sDAAoD,6BAAkB,sBAAmB;wBAC/F,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;4BACrB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,oBAAoB;qBAC7B;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,YAAY,EAAE,YAAY,CAAC,iBAAiB;oBAC5C,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,mBAAmB;qBACtC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,oBAAoB;wBAC1B,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;4BACpB,KAAK,EAAE,GAAG;yBACb;qBACJ;oBACD,OAAO,EAAE,MAAM,CAAC,MAAM;iBACzB;gBACD,OAAO;gBACP;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE;qBACxB;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,iDAAiD;wBACvD,UAAU,EAAE,IAAI;qBACnB;oBACD,OAAO,EAAE,MAAM,CAAC,MAAM;iBACzB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,GAAG;oBACT,GAAG,EAAE,gBAAgB;iBACxB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,qBAAqB,EAAE;qBACvC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,2CAA2C;wBACjD,IAAI,EAAE,2BAA2B;wBACjC,UAAU,EAAE,IAAI;wBAChB,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;4BACnB,KAAK,EAAE,CAAC;yBACX;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,IAAI;iBACb;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;qBAC1C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,0CAA0C;qBACnD;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,mBAAmB,EAAE;qBACrC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,sBAAsB;wBAC5B,IAAI,EAAE,2BAA2B;wBACjC,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;yBACxB;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,IAAI;iBACb;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,eAAe;wBACrB,IAAI,EAAE,EAAE,GAAG,EAAE,mBAAmB,EAAE;qBACrC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,yBAAyB;qBAClC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,oBAAoB;qBAC7B;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,yBAAyB;wBAC/B,IAAI,EAAE,EAAE,GAAG,EAAE,mBAAmB,EAAE;qBACrC;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,0EAA0E;wBAChF,IAAI,EAAE,2BAA2B;qBACpC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;qBAC1C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,sBAAsB;qBAC/B;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;qBAC1C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,2CAA2C;wBACjD,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;yBACxB;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,GAAG;iBACZ;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,mBAAmB;qBAC5B;oBACD,GAAG,EAAE,YAAY,CAAC,iBAAiB;oBACnC,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,qBAAqB;qBAC9B;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;qBAC1C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,0EAA0E;wBAChF,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;yBACxB;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,QAAQ;oBAC5B,KAAK,EAAE;wBACH,IAAI,EAAE,mBAAS,CAAC,uBAAuB;qBAC1C;oBACD,UAAU,EAAE;wBACR,IAAI,EAAE,sEAAsE;wBAC5E,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE;4BACJ,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;yBACxB;qBACJ;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE,IAAI;oBACV,YAAY,EAAE,YAAY,CAAC,gBAAgB;iBAC9C;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,IAAI,EAAE,wBAAwB;qBACjC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,MAAM;oBAC1B,OAAO,EAAE;wBACL,EAAE,IAAI,EAAE,yBAAyB,EAAE;wBACnC,EAAE,IAAI,EAAE,yBAAyB,EAAE;qBACtC;oBACD,OAAO,EAAE,MAAM;iBAClB;gBACD;oBACI,IAAI,EAAE,aAAa,CAAC,IAAI;oBACxB,GAAG,EAAE,YAAY,CAAC,gBAAgB;oBAClC,OAAO,EAAE,MAAM;iBAClB;aACJ;SACJ;QACD,aAAa;QACb,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,mBAAmB;QACnB,WAAW;QACX,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,mBAAmB;QACnB,cAAc;QACd,eAAe;KAClB;CACJ,CAAA;AAOG,oCAAY", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BUILD_BARRACKS_NID } from \"../../common/constant/Constant\";\nimport EventType from \"../../common/event/EventType\";\n\n// 步骤类型\nenum GuideStepType {\n    NONE,\n    DELAY, //延迟一段时间自动进入下一步 { time }\n    DIALOG, //对话框完成后进入下一步 { content }\n    ON_EVENT, //监听事件后进入下一步 { eventName, args:{key,val} }\n    ON_EVENT_WAIT,\n    CHECK, //检测成功后跳转到下一步 { func }\n    CHECK_WAIT, //检测等待成功后下一步 { func, time }\n}\n\n// 标记类型\nenum GuideTagType {\n    FIRST_GUIDE_BEGIN = 'FIRST_GUIDE_BEGIN',//第一个引导开始\n    RESTORE_MAIN_CITY = 'RESTORE_MAIN_CITY',//修复主城\n    FIRST_CREATE_ARMY = 'FIRST_CREATE_ARMY',//第一次创建军队\n    FIRST_BATTLE_MOVE = 'FIRST_BATTLE_MOVE',//第一次战斗移动\n    FIRST_TRIGGER_BATTLE = 'FIRST_TRIGGER_BATTLE',//第一次触发战斗\n    CHECK_FIRST_BATTLE_END = 'CHECK_FIRST_BATTLE_END',//检测第一次战斗是否结束\n    CLAIM_TREASURE = 'CLAIM_TREASURE',//准备领取宝箱\n    CLAIM_TREASURE_COMPLETE = 'CLAIM_TREASURE_COMPLETE',//宝箱领取完成\n    READY_BT_BARRACKS = 'READY_BT_BARRACKS',//准备修建兵营\n    CHECK_CAN_BT_BARRACKS = 'CHECK_CAN_BT_BARRACKS',//检测是否可以修建兵营\n    CHECK_CAN_INDONE = 'CHECK_CAN_INDONE',//检测是否可以训练立即完成\n    CHOOSE_BTING_BUTTON = 'CHOOSE_BTING_BUTTON',//款选建筑队列按钮\n    CHECK_CAN_XL_PAWN = 'CHECK_CAN_XL_PAWN',//检测是否可以训练士兵\n    FIRST_GUIDE_DONE = 'FIRST_GUIDE_DONE',//第一个引导完成\n\n    PAWN_BACK_MAIN_GUIDE_BEGIN = 'PAWN_BACK_MAIN_GUIDE_BEGIN',//士兵回城引导开始\n    PAWN_BACK_MAIN_GUIDE_END = 'PAWN_BACK_MAIN_GUIDE_END',//士兵回城引导结束\n\n    PAWN_DRILL_COMPLETE = 'PAWN_DRILL_COMPLETE',//士兵训练完成引导开始\n    ARMY_COUNT_EXTEND_BEGIN = 'ARMY_COUNT_EXTEND_BEGIN',//军队数量扩展引导开始\n    ARMY_COUNT_EXTEND_END = 'ARMY_COUNT_EXTEND_END',//军队数量扩展引导结束\n\n    BUILD_MAIN_UPGRADE_LV2 = 'BUILD_MAIN_UPGRADE_LV2',//主城升到2级\n\n    FREE_BATTLE_GUIDE_BEGIN = 'FREE_BATTLE_GUIDE_BEGIN',//自由打一块地引导开始\n    FREE_BATTLE_GUIDE_CELL_LV2 = 'FREE_BATTLE_GUIDE_CELL_LV2',//自由打一块地引导2级地块\n\n    WEAR_EQUIP_GUIDE_BEGIN = 'WEAR_EQUIP_GUIDE_BEGIN',//穿戴装备引导开始\n    WEAR_EQUIP_GUIDE_END = 'WEAR_EQUIP_GUIDE_END',\n\n    FIND_FREE_PAWN = 'FIND_FREE_PAWN',//找到空闲士兵\n\n    BATTLE_GUIDE_BEGIN = \"BATTLE_GUIDE_BEGIN\",\n    BATTLE_GUIDE_END = \"BATTLE_GUIDE_END\",\n    OCCUPY_CASTLE_CHECK = \"OCCUPY_CASTLE_CHECK\",//检测要塞占领结果\n\n    PAET_4_GUIDE_BEGIN = 'PAET_4_GUIDE_BEGIN',\n    PART_4_GUIDE_END = \"PART_4_GUIDE_END\",\n\n    ENTER_MAIN_CITY = \"ENTER_MAIN_CITY\",//进入主城\n    EQUIP_STUDY_FINISH = \"EQUIP_STUDY_FINISH\",//装备研究结束\n    SMITHY_GUIDE_DONE = 'SMITHY_GUIDE_DONE',//铁匠铺引导完成\n\n    HERO_GUIDE_BEGIN = 'HERO_GUIDE_BEGIN',//英雄化身\n    HERO_GUIDE_END = 'HERO_GUIDE_END',\n\n    BUILD_ACC_GUIDE_END = 'BUILD_ACC_GUIDE_END',//建筑加速引导结束\n\n    HOSPITAL_GUIDE_BEGIN = 'HOSPITAL_GUIDE_BEGIN',//医馆引导开始\n    HOSPITAL_GUIDE_END = 'HOSPITAL_GUIDE_END',//医馆引导结束\n    HOSPITAL_GUIDE_SELECT_ARMY = 'HOSPITAL_GUIDE_SELECT_ARMY',//医馆选择军队\n\n    LAND_STONE_GUIDE_BEGIN = 'LAND_STONE_GUIDE_BEGIN',//石头地引导开始\n    LAND_STONE_GUIDE_END = 'LAND_STONE_GUIDE_END',//石头地引导结束\n    LAND_STONE_GUIDE_WIND = 'LAND_STONE_GUIDE_WIND',\n    LAND_STONE_GUIDE_TREASURE = 'LAND_STONE_GUIDE_TREASURE',\n\n    BATTLE_BEGIN_GUIDE_END = 'BATTLE_BEGIN_GUIDE_END',//战斗开始引导结束\n\n    ENEMY_FIRST_BATTLE_GUIDE = 'ENEMY_FIRST_BATTLE_GUIDE',//战斗开始引导结束\n\n    NEWBIE_ZONE_GUIDE_COMPOSITE = 'NEWBIE_ZONE_GUIDE_COMPOSITE',//大厅抽卡合成\n    NEWBIE_ZONE_GUIDE_END = 'NEWBIE_ZONE_GUIDE_END',//大厅抽卡引导结束\n}\n\ntype GuideStepInfo = {\n    type: GuideStepType;\n    time?: number; //秒\n    restartPoint?: string; //重启点\n    content?: { text: string, desc?: string }[]; //对话框内容\n    autoClose?: boolean; //是否自动关闭\n    event?: { name: string, args?: any }; //事件信息\n    nodeChoose?: {\n        args?: any; name?: string, func?: string, desc?: string, finger?: any, hide?: boolean, moveCamera?: boolean, scene?: string, descOffset?: cc.Vec2, fullScreen?: boolean, hideChoose?: boolean\n    } //框选信息\n    func?: { name: string, args?: any } //检测方法\n    tag?: string; //标记\n    taEvent?: string; //上报事件\n    mask?: boolean; //是否显示遮罩\n    force?: boolean; //是否开启强制引导\n    weak?: boolean; // 是否为弱引导（任意点击即可跳过当前步骤）\n}\n\nenum GuideTextArgsType {\n    None = 0,\n    FirstHeroName,\n    TreasureIronCount,\n\n    // 士兵化身英雄\n    PawnToHeroId = 100999,\n    // 英雄自选礼包\n    HeroPackageId = 100900,\n}\n\nconst guideBattleEnd = {\n    id: 5,\n    checkFunc: {\n        name: 'checkFuncPartBattle',\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.5,\n            restartPoint: '0',\n            tag: '0',\n            taEvent: '5-1', //战斗结束\n        },\n        // 对话 恭喜你击败了强大的敌人，通过了试炼！\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_battle_010' },\n                { text: 'guideText.dialog_battle_011' },\n            ],\n            taEvent: '5-2', //攻占成功后的对话\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncTaskReward'\n            },\n            taEvent: '5-3', //攻占成功后的对话\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_battle_012' },\n            ],\n            taEvent: '5-4', //有奖励未领取\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncSyncServerData'\n            },\n            taEvent: '5-5'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncOpenEndPnl'\n            },\n            restartPoint: GuideTagType.BATTLE_GUIDE_END, //存档点 攻占要塞成功\n            taEvent: '5-6'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.BATTLE_GUIDE_END,\n            taEvent: '5-7', //新手村结束\n        }\n    ]\n}\n\n// 军队多选教程\n// const guideMultiAttack = {\n//     id: 4,\n//     checkFunc: {\n//         name: 'checkFuncPart4Guide',\n//     },\n//     isSub: false,\n//     steps: [\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.1, //秒\n//             restartPoint: '0', //存档点\n//             tag: '0',\n//             taEvent: '4-1', //第二段教程开始\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncCloseAllUI', //关闭所有UI\n//             },\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_OPEN_MAP_CELL,\n//             },\n//             nodeChoose: {\n//                 func: 'nodeChooseMapCellLv2',\n//                 // moveCamera: true,\n//                 finger: {\n//                     offset: cc.v2(0, -32),\n//                     angle: 0,\n//                 },\n//                 hide: true,\n//             },\n//             taEvent: '4-2', //选择领土\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_210',\n//                 args: { key: 'main/SelectArmy' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/NoviceWind/root/cell_info/buttons/occupy_be',\n//                 hide: true,\n//                 finger: {\n//                     offset: cc.v2(0, -26),\n//                     angle: 0,\n//                 },\n//             },\n//             taEvent: '4-3', //移动军队\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_part4_001' }\n//             ],\n//             mask: true\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_ARMY,\n//             },\n//             nodeChoose: {\n//                 func: \"nodeChooseArmy\",      //框选军队1\n//                 finger: {\n//                     offset: cc.v2(325, 10),\n//                     angle: 90,\n//                     flip: true\n//                 },\n//                 desc: 'guideText.army_select_desc_002',\n//                 descOffset: cc.v2(0, -350),\n//             },\n//             taEvent: '4-4', //框选军队1\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_ARMY,\n//             },\n//             nodeChoose: {\n//                 func: \"nodeChooseArmy\",      //框选军队2\n//                 args: { index: 1 },\n//                 finger: {\n//                     offset: cc.v2(325, 10),\n//                     angle: 90,\n//                     flip: true\n//                 },\n//                 desc: 'guideText.army_select_desc_003',\n//                 descOffset: cc.v2(0, -350),\n//             },\n//             taEvent: '4-5', //框选军队2\n//         },\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.26,\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_LEAVE_211',\n//                 args: { key: 'main/SelectArmy' },\n//             },\n//             nodeChoose: {\n//                 name: 'View/SelectArmyPnl/root/ok_be',\n//                 hide: true,\n//                 finger: {\n//                     offset: cc.v2(0, -32),\n//                     angle: 0,\n//                 }\n//             },\n//             taEvent: '4-6', //派出军队\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_part4_002' }\n//             ],\n//             restartPoint: 'UP_MAIN_LV_READY', //存档点\n//             tag: 'UP_MAIN_LV_READY',\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncHideMarchLine', //隐藏行军线\n//                 args: { opacity: 20 },\n//             },\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT, //款选主城\n//             event: {\n//                 name: EventType.GUIDE_OPEN_MAP_CELL,\n//             },\n//             nodeChoose: {\n//                 func: 'nodeChooseMainCity',\n//                 finger: {\n//                     offset: cc.v2(0, 80),\n//                     angle: 180,\n//                 },\n//                 // moveCamera: true,\n//                 hide: true,\n//             },\n//             taEvent: '4-7', //框选主城\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'WIND_ENTER_303',\n//                 args: { key: 'area' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n//                 hide: true,\n//                 finger: {\n//                     offset: cc.v2(0, 30),\n//                     angle: 180,\n//                 },\n//                 hideChoose: true\n//             },\n//             taEvent: '4-8', //框选进入\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncHideMarchLine', //显示行军线\n//                 args: { opacity: 100 },\n//             },\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncPauseAreaBattle', //暂停区域战斗\n//                 args: { index: 1277, frameCount: 1100 },\n//             },\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_210',\n//                 args: { key: 'build/BuildMainInfo' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/AreaWind/root/role_n/BUILD_2001/body',\n//                 hide: true,\n//                 finger: {\n//                     offset: cc.v2(0, 72),\n//                     angle: 180,\n//                 },\n//                 moveCamera: true,\n//             },\n//             taEvent: '4-9', //框选主城\n//         },\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.26,\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_LEAVE_211',\n//                 args: { key: 'build/BuildMainInfo' },\n//             },\n//             nodeChoose: {\n//                 name: 'View/BuildMainInfoPnl/root/pages_n/0/bottom/buttons/up_be',\n//             },\n//             taEvent: '4-10', //框选升级按钮\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_part4_003' },\n//                 { text: 'guideText.dialog_part4_004' },\n//                 { text: 'guideText.dialog_part4_005' },\n//             ],\n//             restartPoint: 'UP_MAIN_LV_END', //存档点\n//             tag: 'UP_MAIN_LV_END',\n//         },\n//         {\n//             type: GuideStepType.NONE,\n//             tag: GuideTagType.PART_4_GUIDE_END,\n//             taEvent: '4-12', //第二段教程完成\n//         },\n//     ]\n// }\n\n// const guideSendRes = {\n//     id: 10010,\n//     checkFunc: {\n//         name: 'no',\n//     },\n//     isSub: true,\n//     steps: [\n//         // 对话\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_send_res_001' },\n//                 { text: 'guideText.dialog_send_res_002' }\n//             ],\n//             taEvent: '10010-1', //领取穷困物资\n//         },\n//         // 对话\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncSendResources', // 发放物资\n//                 args: { ctypeStr: '1,0,200|2,0,200|3,0,200' },\n//             },\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncReverse', // 返回之前的引导\n//             },\n//             taEvent: '10010-2', //领取穷困物资完成\n//         }\n//     ]\n// }\n\n// const guideSendResTip = {\n//     id: 10011,\n//     checkFunc: {\n//         name: 'no',\n//     },\n//     isSub: true,\n//     steps: [\n//         // 对话\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_send_res_001' },\n//                 { text: 'guideText.dialog_send_res_003' }\n//             ],\n//             taEvent: '10011-1', //穷困，但是有任务奖励可以领取\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncReverse', // 返回之前的引导\n//             },\n//             taEvent: '10011-2', //穷困，但是有任务奖励可以领取完成\n//         }\n//     ]\n// }\n\n// 装备重铸教程\n// const guideGearforge = {\n//     id: 6,\n//     checkFunc: {\n//         name: 'checkFuncGearforge',\n//         keepPnl: true\n//     },\n//     isSub: false,\n//     steps: [\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_gearforge_001' },\n//                 { text: 'guideText.dialog_gearforge_002' }\n//             ],\n//             restartPoint: '0', //存档点\n//             tag: '0',\n//             taEvent: '6-1', //装备重铸引导开始\n//         },\n//         // 框选\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n//             },\n//             nodeChoose: {\n//                 func: 'nodeChooseEquipBaseInfoBtn',      //框选装备\n//                 desc: 'guideText.choose_desc_007',\n//             },\n//             taEvent: '6-2', //查看装备信息\n//         },\n//         {\n//             type: GuideStepType.NONE,\n//             tag: 'guideGearforgEnd',\n//             taEvent: '6-3', //装备重铸引导结束\n//         }\n//     ]\n// }\n\n// 新手区对局引导\nconst guideNewbieZone = {\n    id: 7,\n    checkFunc: {\n        name: 'checkFuncNewbieZone'\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_newbie_zone_001' },\n            ],\n            mask: true,\n            restartPoint: '0', // 存档点\n            tag: '0',\n            taEvent: '7-1'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncNeedToComposite',\n                args: { tag: GuideTagType.NEWBIE_ZONE_GUIDE_COMPOSITE }\n            },\n            taEvent: '7-2'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_newbie_zone_003' },\n                { text: 'guideText.dialog_newbie_zone_004' },\n            ],\n            taEvent: '7-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_210',\n                args: { key: 'menu/Pointsets' },\n            },\n            nodeChoose: {\n                name: 'Wind/LobbyWind/root_n/menu_right/menu_right/pointsets',\n            },\n            taEvent: '7-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'POINTSETS_OPEN_SHOW',\n            },\n            nodeChoose: {\n                name: 'View/PointsetsPnl/pointsets_nbe_n/1',\n                hideChoose: true\n            },\n            taEvent: '7-5'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'POINTSETS_CLOSE_SHOW',\n            },\n            nodeChoose: {\n                name: 'View/PointsetsPnl',\n                hide: true\n            },\n            taEvent: '7-6'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_newbie_zone_005' },\n            ],\n            taEvent: '7-7'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/PointsetsPnl/back',\n            },\n            taEvent: '7-8'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_210',\n                args: { key: 'menu/Collection' },\n            },\n            nodeChoose: {\n                name: 'Wind/LobbyWind/root_n/menu_right/menu_right/portrayal',\n                desc: 'guideText.dialog_newbie_zone_desc_001'\n            },\n            tag: GuideTagType.NEWBIE_ZONE_GUIDE_COMPOSITE,\n            taEvent: '7-9'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_210',\n                args: { key: 'common/PortrayalInfo' }\n            },\n            nodeChoose: {\n                name: 'View/CollectionPnl/root/pages_n/0/list/view/content/portrayal_be_clone',\n            },\n            taEvent: '7-10'\n        },\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PORTRAYAL_COMP',\n            },\n            nodeChoose: {\n                name: 'View/PortrayalInfoPnl/comp_be_n',\n            },\n            taEvent: '7-11'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_newbie_zone_006', args: GuideTextArgsType.FirstHeroName },\n                { text: 'guideText.dialog_newbie_zone_007', args: GuideTextArgsType.FirstHeroName },\n                { text: 'guideText.dialog_newbie_zone_008' },\n            ],\n            restartPoint: GuideTagType.NEWBIE_ZONE_GUIDE_END,\n            taEvent: '7-12'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/PortrayalInfoPnl/back',\n            },\n            taEvent: '7-13'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncCloseAllUI'\n            },\n            taEvent: '7-14'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'Wind/LobbyWind/root_n/content/bottom_n/team/layout/buttons_n/enter_game_be',\n                finger: {\n                    offset: cc.v2(0, -40),\n                },\n                hide: true,\n                fullScreen: true\n            },\n            taEvent: '7-15'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.NEWBIE_ZONE_GUIDE_END,\n            taEvent: '7-16'\n        }\n    ]\n}\n\n// 军队回城补血教程\nconst guidePawnBackMain = {\n    id: 2, //军队回城教程\n    checkFunc: {\n        name: 'checkFuncPawnBackMainGuide',\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.5, //秒\n            restartPoint: GuideTagType.PAWN_BACK_MAIN_GUIDE_BEGIN, //存档点\n            tag: '0',\n            taEvent: '2-1', //军队回城教程开始\n        },\n        // 关闭所有界面\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncCloseAllUI',\n            },\n            taEvent: '2-2'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_110010' },\n            ],\n            tag: GuideTagType.PAWN_BACK_MAIN_GUIDE_BEGIN,\n            taEvent: '2-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT, //款选主城\n            event: {\n                name: EventType.GUIDE_OPEN_MAP_CELL,\n            },\n            nodeChoose: {\n                func: 'nodeChooseMainCity',\n                desc: 'guideText.choose_desc_005',\n                moveCamera: true,\n                finger: {\n                    offset: cc.v2(0, -80),\n                    angle: 0,\n                },\n                hide: true,\n            },\n            taEvent: '2-4', //点击主城\n        },\n        {\n            type: GuideStepType.ON_EVENT, //框选移动\n            event: {\n                name: 'PNL_ENTER_210',\n                args: { key: 'main/SelectArmy' },\n            },\n            nodeChoose: {\n                name: 'Wind/NoviceWind/root/cell_info/buttons/move_be',\n            },\n            taEvent: '2-5', //点击移动\n        },\n        {\n            type: GuideStepType.DELAY,\n            time: 0.26,\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_ARMY,\n            },\n            nodeChoose: {\n                func: \"nodeChooseArmy\",      //框选军队\n                args: { index: -1 },\n                desc: 'guideText.army_select_desc_005',\n                descOffset: cc.v2(0, -350),\n            },\n            taEvent: '2-6', //选择残血军队\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_LEAVE_211',\n                args: { key: 'main/SelectArmy' },\n            },\n            nodeChoose: {\n                name: 'View/SelectArmyPnl/root/ok_be',\n                hide: true,\n                finger: {\n                    offset: cc.v2(0, -32),\n                    angle: 0,\n                }\n            },\n            taEvent: '2-7', //点击确定按钮\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_110020' },\n            ],\n            tag: GuideTagType.PAWN_BACK_MAIN_GUIDE_END,\n            taEvent: '2-8', //军队回城结束\n        },\n    ]\n}\n\n// 铁匠铺教程\nconst guideSmithy = {\n    id: 8,\n    checkFunc: {\n        name: 'checkFuncSmithy',\n        keepPnl: true\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_smithy_001' }\n            ],\n            restartPoint: '0', //存档点\n            tag: '0',\n            taEvent: '8-1'\n        },\n        // 检查是否在铁匠铺\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkIsOpenSmithy',\n                args: { tag: GuideTagType.EQUIP_STUDY_FINISH }\n            },\n            taEvent: '8-2'\n        },\n        // 检查是否进入主城\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkEnterMainCity',\n                args: { tag: GuideTagType.ENTER_MAIN_CITY }\n            },\n            taEvent: '8-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT, //款选主城\n            event: {\n                name: EventType.GUIDE_OPEN_MAP_CELL,\n            },\n            nodeChoose: {\n                func: 'nodeChooseMainCity',\n                moveCamera: true,\n                finger: {\n                    offset: cc.v2(0, 80),\n                    angle: 180,\n                },\n                hide: true,\n            },\n            taEvent: '8-4'\n        },\n        // 框选进入\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'WIND_ENTER_303',\n                args: { key: 'area' },\n            },\n            nodeChoose: {\n                name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n                hideChoose: true\n            },\n            taEvent: '8-5'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'build/BuildSmithy' },\n            },\n            nodeChoose: {\n                name: 'Wind/AreaWind/root/role_n/BUILD_2008/body',\n            },\n            tag: GuideTagType.ENTER_MAIN_CITY,\n            taEvent: '8-6'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/BuildSmithyPnl/root/tabs_tc_tce/1',\n            },\n            taEvent: '8-7'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkEquipStudy',\n                args: { slotId: 1, tag: GuideTagType.EQUIP_STUDY_FINISH }\n            },\n            taEvent: '8-8'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'build/StudySelect' },\n            },\n            nodeChoose: {\n                name: 'View/BuildSmithyPnl/root/pages_n/1/equip/list/view/content/equip_be',\n            },\n            taEvent: '8-9'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'STUDY_SELECT_FINISH',\n            },\n            nodeChoose: {\n                name: 'View/StudySelectPnl/root',\n            },\n            taEvent: '8-10'\n        },\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncForgeAcc'\n            },\n            tag: GuideTagType.EQUIP_STUDY_FINISH,\n            taEvent: '8-11'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/BuildSmithyPnl/root/pages_n/1/cond/info/need/buttons/forge_be',\n            },\n            taEvent: '8-12'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkSmithyResResetDialogText'\n            },\n            restartPoint: GuideTagType.SMITHY_GUIDE_DONE,\n            taEvent: '8-13'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_smithy_002' }\n            ],\n            taEvent: '8-14'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.SMITHY_GUIDE_DONE,\n            taEvent: '8-15'\n        }\n    ]\n}\n\n//穿戴装备\nconst guideWearEquip = {\n    id: 9,\n    checkFunc: {\n        name: 'checkFuncWearEquip'\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.5, //秒\n            restartPoint: GuideTagType.WEAR_EQUIP_GUIDE_BEGIN, //存档点\n            tag: '0',\n            taEvent: '9-1'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_smithy_005' },\n                { text: 'guideText.dialog_smithy_006' },\n            ],\n            tag: GuideTagType.WEAR_EQUIP_GUIDE_BEGIN, //存档点\n            taEvent: '9-2'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkWearEquipScene',\n                args: { tag: GuideTagType.FIND_FREE_PAWN },\n            },\n            taEvent: '9-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT, //框选返回按钮\n            event: {\n                name: 'WIND_ENTER_303',\n                args: { key: 'novice' },\n            },\n            nodeChoose: {\n                name: 'View/UIPnl/scene_n/area/back_main_be',\n            },\n            taEvent: '9-4'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_OPEN_MAP_CELL,\n            },\n            nodeChoose: {\n                func: 'findPawnInLand',\n                moveCamera: true,\n                finger: {\n                    offset: cc.v2(0, 40),\n                    angle: 180,\n                },\n                hide: true,\n            },\n            taEvent: '9-5'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'WIND_ENTER_303',\n                args: { key: 'area' },\n            },\n            nodeChoose: {\n                name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n                hideChoose: true\n            },\n            taEvent: '9-5'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'area/PawnInfo' },\n            },\n            nodeChoose: {\n                func: 'findOnePawn'\n            },\n            tag: GuideTagType.FIND_FREE_PAWN,\n            taEvent: '9-7'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/PawnInfoPnl/root/equip_n/info/equip_show_be',\n            },\n            taEvent: '9-8'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'View/PawnInfoPnl/select_equip_box_be_n/root/list/view/content/equip_item_be',\n            },\n            taEvent: '9-9'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_smithy_007' },\n            ],\n            restartPoint: GuideTagType.WEAR_EQUIP_GUIDE_END,\n            taEvent: '9-10'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.WEAR_EQUIP_GUIDE_END,\n            taEvent: '9-11'\n        }\n    ]\n}\n\n//英雄化身\n// const guideWorshipHero = {\n//     id: 10,\n//     checkFunc: {\n//         name: 'checkFuncWorshipHero',\n//     },\n//     isSub: false,\n//     steps: [\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.5, //秒\n//             restartPoint: GuideTagType.HERO_GUIDE_BEGIN, //存档点\n//             tag: '0',\n//             taEvent: '10-1'\n//         },\n//         // 关闭所有界面\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncCloseAllUI',\n//             },\n//             tag: GuideTagType.HERO_GUIDE_BEGIN, //存档点\n//             taEvent: '10-2'\n//         },\n//         // 检查是否进入主城\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkEnterMainCity',\n//                 args: { tag: GuideTagType.ENTER_MAIN_CITY }\n//             },\n//             taEvent: '10-3'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT, //款选主城\n//             event: {\n//                 name: EventType.GUIDE_OPEN_MAP_CELL,\n//             },\n//             nodeChoose: {\n//                 func: 'nodeChooseMainCity',\n//                 moveCamera: true,\n//                 finger: {\n//                     offset: cc.v2(0, 80),\n//                     angle: 180,\n//                 },\n//                 hide: true,\n//             },\n//             taEvent: '10-4'\n//         },\n//         // 框选进入\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'WIND_ENTER_303',\n//                 args: { key: 'area' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n//                 hideChoose: true\n//             },\n//             taEvent: '10-5'\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_hero_001' },\n//                 { text: 'guideText.dialog_hero_002' }\n//             ],\n//             taEvent: '10-6'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_PLAY_DONE_215',\n//                 args: { key: 'build/BuildHerohall' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/AreaWind/root/role_n/BUILD_2015/body',\n//             },\n//             tag: GuideTagType.ENTER_MAIN_CITY,\n//             taEvent: '10-7'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n//             },\n//             nodeChoose: {\n//                 name: 'View/BuildHerohallPnl/root/tabs_tc_tce/1',\n//             },\n//             taEvent: '10-8'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_PLAY_DONE_215',\n//                 args: { key: 'common/SelectPortrayal' },\n//             },\n//             nodeChoose: {\n//                 name: 'View/BuildHerohallPnl/root/pages_n/1/slots_nbe/0',\n//             },\n//             taEvent: '10-9'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n//             },\n//             nodeChoose: {\n//                 name: 'View/SelectPortrayalPnl/root_n/list/view/content/item_be',\n//                 finger: {\n//                     offset: cc.v2(0, -80)\n//                 },\n//                 hide: true,\n//             },\n//         },\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.1,\n//             taEvent: '10-10'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'WORSHIP_HERO',\n//             },\n//             nodeChoose: {\n//                 name: 'View/SelectPortrayalPnl/root_n',\n//             },\n//             taEvent: '10-11'\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkHeroPawn'\n//             },\n//             restartPoint: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '10-12'\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_hero_003', args: GuideTextArgsType.PawnToHeroId }\n//             ],\n//             restartPoint: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '10-13'\n//         },\n//         {\n//             type: GuideStepType.NONE,\n//             tag: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '10-14'\n//         },\n//     ]\n// }\n\n// const guidePawnToHero = {\n//     id: 11,\n//     checkFunc: {\n//         name: 'checkFuncPawnToHero',\n//     },\n//     isSub: false,\n//     steps: [\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.5, //秒\n//             restartPoint: GuideTagType.HERO_GUIDE_BEGIN, //存档点\n//             tag: '0',\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkFuncCloseAllUI',\n//             },\n//             tag: GuideTagType.HERO_GUIDE_BEGIN,\n//             taEvent: '11-1'\n//         },\n//         {\n//             type: GuideStepType.CHECK,\n//             func: {\n//                 name: 'checkHeroScene',\n//                 args: { tag: GuideTagType.FIND_FREE_PAWN }\n//             },\n//             taEvent: '11-2'\n//         },\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.1\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT, //框选返回按钮\n//             event: {\n//                 name: 'WIND_ENTER_303',\n//                 args: { key: 'novice' },\n//             },\n//             nodeChoose: {\n//                 name: 'View/UIPnl/scene_n/area/back_main_be',\n//             },\n//             taEvent: '11-3'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_OPEN_MAP_CELL,\n//             },\n//             nodeChoose: {\n//                 func: 'findPawnInLandForHero',\n//                 moveCamera: true,\n//                 finger: {\n//                     offset: cc.v2(0, 40),\n//                     angle: 180,\n//                 },\n//                 hide: true,\n//             },\n//             taEvent: '11-4'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'WIND_ENTER_303',\n//                 args: { key: 'area' },\n//             },\n//             nodeChoose: {\n//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n//                 hideChoose: true\n//             },\n//             taEvent: '11-5'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_PLAY_DONE_215',\n//                 args: { key: 'area/PawnInfo' },\n//             },\n//             nodeChoose: {\n//                 func: 'findOnePawnForHero'\n//             },\n//             tag: GuideTagType.FIND_FREE_PAWN,\n//             taEvent: '11-6'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: 'PNL_ENTER_PLAY_DONE_215',\n//                 args: { key: 'area/SelectAvatarHero' },\n//             },\n//             nodeChoose: {\n//                 name: 'View/PawnInfoPnl/root/button_n/avatar_be',\n//                 finger: {\n//                     offset: cc.v2(0, -32),\n//                     angle: 0,\n//                 },\n//                 hide: true,\n//             },\n//             taEvent: '11-7'\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n//             },\n//             nodeChoose: {\n//                 name: 'View/SelectAvatarHeroPnl/root_n/content/list/item_be',\n//             },\n//             taEvent: '11-8'\n//         },\n//         {\n//             type: GuideStepType.DELAY,\n//             time: 0.1\n//         },\n//         {\n//             type: GuideStepType.ON_EVENT,\n//             event: {\n//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n//             },\n//             nodeChoose: {\n//                 name: 'View/SelectAvatarHeroPnl/root_n/buttons/ok_be',\n//                 finger: {\n//                     offset: cc.v2(0, -36),\n//                     angle: 0,\n//                 },\n//                 hide: true,\n//             },\n//             taEvent: '11-9'\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_hero_004' }\n//             ],\n//             restartPoint: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '11-10'\n//         },\n//         {\n//             type: GuideStepType.DIALOG,\n//             content: [\n//                 { text: 'guideText.dialog_hero_004' }\n//             ],\n//             restartPoint: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '11-11'\n//         },\n//         {\n//             type: GuideStepType.NONE,\n//             tag: GuideTagType.HERO_GUIDE_END,\n//             taEvent: '11-12'\n//         },\n//     ]\n// }\n\n// 建筑加速教程\nconst guideBuildAcc = {\n    id: 12,\n    checkFunc: {\n        name: 'checkFuncBuildAccTrigger',\n        keepPnl: true\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_build_acc_001' },\n                { text: 'guideText.dialog_build_acc_002' }\n            ],\n            restartPoint: '0', //存档点\n            tag: '0',\n            taEvent: '12-1'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkGiveGold'\n            },\n            taEvent: '12-2'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'common/BTQueue' },\n            },\n            nodeChoose: {\n                name: 'View/UIPnl/right_bottom_n/bting_be_n',\n                finger: {\n                    offset: cc.v2(0, 36),\n                    angle: 180,\n                },\n                hide: true,\n            },\n            taEvent: '12-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'NOTICE_ENTER_PLAY_DONE_403',\n            },\n            nodeChoose: {\n                name: 'View/BTQueuePnl/root/title/in_done_n_be',\n                finger: {\n                    offset: cc.v2(0, 36),\n                    angle: 180,\n                },\n                hide: true,\n            },\n            taEvent: '12-4'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'setSwitchGuideParent',\n                args: { isView: false }\n            },\n            taEvent: '12-5'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n            },\n            nodeChoose: {\n                name: 'Notice/MessageBoxNot/root_n/buttons_nbe_n/ok',\n                finger: {\n                    offset: cc.v2(0, 36),\n                    angle: 180,\n                },\n                hide: true,\n            },\n            taEvent: '12-6'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'setSwitchGuideParent',\n                args: { isView: true }\n            },\n            taEvent: '12-7'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.BUILD_ACC_GUIDE_END,\n            taEvent: '12-8'\n        }\n    ]\n}\n\n// 医馆教程\nconst guideHospital = {\n    id: 13,\n    checkFunc: {\n        name: 'checkFuncHospitalTrigger',\n        keepPnl: true\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1, //秒\n            restartPoint: GuideTagType.HOSPITAL_GUIDE_BEGIN, //存档点\n            tag: '0',\n            taEvent: '13-1'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_hospital_001' },\n                { text: 'guideText.dialog_hospital_002' },\n                { text: 'guideText.dialog_hospital_003' },\n            ],\n            restartPoint: GuideTagType.HOSPITAL_GUIDE_END, //存档点\n            taEvent: '13-2'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncShowAttackTips',\n                args: [{ tips: 'guideText.dialog_notice_guide_003', icon1: 1, icon2: 1, iconName2: 'buildText.name_2016' }],\n            },\n            taEvent: '13-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_LEAVE_211',\n                args: { key: 'help/NoticeGuide' },\n            },\n            nodeChoose: {\n                name: 'View/NoticeGuidePnl/root',\n                hide: true\n            },\n            taEvent: '13-4'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.HOSPITAL_GUIDE_END,\n            taEvent: '13-5'\n        }\n    ]\n}\n\n// 石头地教程\nconst guideLandStone = {\n    id: 14,\n    checkFunc: {\n        name: 'checkFuncLandStoneTrigger',\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1, //秒\n            restartPoint: GuideTagType.LAND_STONE_GUIDE_BEGIN, //存档点\n            tag: '0',\n            taEvent: '14-1'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_land_stone_001' }\n            ],\n            tag: GuideTagType.LAND_STONE_GUIDE_BEGIN, //存档点\n            taEvent: '14-2'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncLandStoneWind',\n                args: { tag: GuideTagType.LAND_STONE_GUIDE_WIND }\n            },\n            taEvent: '14-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'area/AreaArmy' },\n            },\n            nodeChoose: {\n                name: 'View/UIPnl/scene_n/area/bottom/area_army_be_n',\n                desc: 'guideText.dialog_land_stone_desc_001',\n            },\n            taEvent: '14-4'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'common/TreasureList' },\n            },\n            nodeChoose: {\n                func: 'nodeChooseArmyTreasure',\n                args: { key: 'area/AreaArmy' }\n            },\n            taEvent: '14-5'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncLandStoneToTreasure',\n                args: { tag: GuideTagType.LAND_STONE_GUIDE_TREASURE }\n            },\n            taEvent: '14-6'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'main/ArmyList' },\n            },\n            nodeChoose: {\n                name: 'View/UIPnl/scene_n/main/bottom/all_army_be_n',\n                desc: 'guideText.dialog_land_stone_desc_001',\n            },\n            tag: GuideTagType.LAND_STONE_GUIDE_WIND,\n            taEvent: '14-7'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_ENTER_PLAY_DONE_215',\n                args: { key: 'common/TreasureList' },\n            },\n            nodeChoose: {\n                func: 'nodeChooseArmyTreasure',\n                args: { key: 'main/ArmyList' }\n            },\n            taEvent: '14-8'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: EventType.GUIDE_CLICK_CHOOSE_RECT\n            },\n            nodeChoose: {\n                func: 'nodeChooseArmyTreasureOpen'\n            },\n            tag: GuideTagType.LAND_STONE_GUIDE_TREASURE,\n            taEvent: '14-9'\n        },\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1,\n            restartPoint: GuideTagType.LAND_STONE_GUIDE_END, //存档点\n            taEvent: '14-10'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_land_stone_002', args: GuideTextArgsType.TreasureIronCount }\n            ],\n            restartPoint: GuideTagType.LAND_STONE_GUIDE_END, //存档点\n            taEvent: '14-11'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.LAND_STONE_GUIDE_END,\n            taEvent: '14-12'\n        }\n    ]\n}\n\nconst guideBattleBegin = {\n    id: 15,\n    checkFunc: {\n        name: 'checkFuncBattleBeginTrigger'\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1, //秒\n            restartPoint: '0', //存档点\n            tag: '0',\n            taEvent: '15-1'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_enemy_battle_001' },\n                { text: 'guideText.dialog_enemy_battle_002' },\n            ],\n            taEvent: '15-2'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncShowAttackTips',\n                args: [{ tips: 'guideText.dialog_notice_guide_001', desc: 'guideText.dialog_notice_guide_002', icon1: 0, icon2: 0 }],\n            },\n            taEvent: '15-3'\n        },\n        {\n            type: GuideStepType.ON_EVENT,\n            event: {\n                name: 'PNL_LEAVE_211',\n                args: { key: 'help/NoticeGuide' },\n            },\n            nodeChoose: {\n                name: 'View/NoticeGuidePnl/root',\n                hide: true\n            },\n            taEvent: '15-4'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.BATTLE_BEGIN_GUIDE_END,\n            taEvent: '15-5'\n        }\n    ]\n}\n\nconst guideFirstBattleEnd = {\n    id: 16,\n    checkFunc: {\n        name: 'checkFuncFirstBattleEndTrigger'\n    },\n    isSub: false,\n    steps: [\n        {\n            type: GuideStepType.DELAY,\n            time: 0.1, //秒\n            restartPoint: '0', //存档点\n            tag: '0',\n            taEvent: '16-1'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_enemy_battle_003' },\n                { text: 'guideText.dialog_enemy_battle_004' },\n                { text: 'guideText.dialog_enemy_battle_005' },\n            ],\n            taEvent: '16-2'\n        },\n        {\n            type: GuideStepType.CHECK,\n            func: {\n                name: 'checkFuncUpdateSmithyLv',\n                args: { lv: 10, iron: 12 },\n            },\n            restartPoint: GuideTagType.ENEMY_FIRST_BATTLE_GUIDE, //存档点\n            taEvent: '16-3'\n        },\n        {\n            type: GuideStepType.DIALOG,\n            content: [\n                { text: 'guideText.dialog_enemy_battle_006' },\n            ],\n            taEvent: '16-4'\n        },\n        {\n            type: GuideStepType.NONE,\n            tag: GuideTagType.ENEMY_FIRST_BATTLE_GUIDE,\n            taEvent: '16-5'\n        }\n    ]\n}\n\n\n// 配置\nconst GUIDE_CONFIG = {\n\n    datas: [\n        {\n            id: 1,\n            checkFunc: {\n                name: 'checkFuncOneGuide',\n            },\n            isSub: false,\n            steps: [\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.2, //秒\n                    restartPoint: GuideTagType.FIRST_GUIDE_BEGIN, //存档点\n                    tag: '0',\n                    taEvent: '1-1', //第一个引导开始\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100010' },\n                        { text: 'guideText.dialog_100011' },\n                    ],\n                    tag: GuideTagType.FIRST_GUIDE_BEGIN,\n                    taEvent: '1-2',\n                },\n                {\n                    type: GuideStepType.ON_EVENT, //款选主城\n                    event: {\n                        name: EventType.GUIDE_CKICK_BUILD_MAIN_CITY,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseMainCity',\n                        finger: {\n                            offset: cc.v2(0, 80),\n                            angle: 180,\n                        }\n                    },\n                    taEvent: '1-3', //框选主城\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncRestoreMainCity', //修复主城\n                    },\n                    restartPoint: GuideTagType.RESTORE_MAIN_CITY, //存档点\n                    taEvent: '1-4',\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 4, //秒\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100012' },\n                    ],\n                    tag: GuideTagType.RESTORE_MAIN_CITY,\n                    restartPoint: GuideTagType.RESTORE_MAIN_CITY, //存档点\n                    taEvent: '1-5', //主城修复完成\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_OPEN_MAP_CELL,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseMapCell',\n                        desc: 'guideText.choose_desc_001',\n                        moveCamera: true,\n                        finger: {\n                            offset: cc.v2(0, -40),\n                            angle: 0,\n                        }\n                    },\n                    taEvent: '1-6', //框选地块\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_210',\n                        args: { key: 'main/SelectArmy' },\n                    },\n                    nodeChoose: {\n                        name: 'Wind/NoviceWind/root/cell_info/buttons/occupy_be',\n                    },\n                    tag: GuideTagType.FIRST_BATTLE_MOVE,\n                    taEvent: '1-7', //框选攻占按钮\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.26,\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_CLICK_ARMY,\n                    },\n                    nodeChoose: {\n                        func: \"nodeChooseArmy\", //框选军队1\n                        finger: {\n                            offset: cc.v2(325, 10),\n                            angle: 90,\n                            flip: true\n                        }\n                    },\n                    taEvent: '1-8', //框选军队1\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_LEAVE_211',\n                        args: { key: 'main/SelectArmy' },\n                    },\n                    nodeChoose: {\n                        name: 'View/SelectArmyPnl/root/ok_be',\n                    },\n                    taEvent: '1-9', //框选士兵界面确定按钮\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100020', desc: 'guideText.dialog_desc_001' },\n                    ],\n                    restartPoint: GuideTagType.FIRST_TRIGGER_BATTLE, //存档点\n                    taEvent: '1-10',\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncTriggerFirstBattle', //检测是否有战斗\n                    },\n                    tag: GuideTagType.FIRST_TRIGGER_BATTLE,\n                    taEvent: '1-11',\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100030' },\n                    ],\n                    taEvent: '1-12', //排查\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_OPEN_MAP_CELL,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseMapCell',\n                        finger: {\n                            offset: cc.v2(0, -40),\n                            angle: 0,\n                        }\n                    },\n                    taEvent: '1-13', //框选地块\n                },\n                {\n                    type: GuideStepType.ON_EVENT, //框选进入按钮 有战斗区域的\n                    event: {\n                        name: 'WIND_ENTER_303',\n                        args: { key: 'area' },\n                    },\n                    nodeChoose: {\n                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n                        hideChoose: true\n                    },\n                    taEvent: '1-14', //框选进入按钮\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.5,\n                },\n                {\n                    type: GuideStepType.DIALOG, //准备进入战斗\n                    content: [\n                        { text: 'guideText.dialog_100040', desc: 'guideText.dialog_desc_002' },\n                    ],\n                    taEvent: '1-15',\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncBeginFirstBattle', //开启战斗\n                    },\n                    restartPoint: GuideTagType.CHECK_FIRST_BATTLE_END,\n                    taEvent: '1-16', //手动开启战斗\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_LEAVE_211',\n                        args: { key: 'area/BattleEnd' },\n                    },\n                    tag: GuideTagType.CHECK_FIRST_BATTLE_END,\n                    taEvent: '1-17', //战斗结束\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.2,\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100050' },\n                        { text: 'guideText.dialog_100051' },\n                    ],\n                    taEvent: '1-18',\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_PLAY_DONE_215',\n                        args: { key: 'area/AreaArmy' },\n                    },\n                    nodeChoose: {\n                        name: 'View/UIPnl/scene_n/area/bottom/area_army_be_n',\n                    },\n                    taEvent: '1-19', //款选区域军队按钮\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_PLAY_DONE_215',\n                        args: { key: 'common/TreasureList' },\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseTreasureText',\n                        finger: {\n                            offset: cc.v2(0, 24),\n                            angle: 180,\n                        }\n                    },\n                    taEvent: '1-20', //款选宝箱按钮\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncTreasureIsOpen', //这里检测第一个宝箱是否打开了\n                    },\n                    taEvent: '1-21',\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_OPEN_TREASURE,\n                    },\n                    nodeChoose: {\n                        name: 'View/TreasureListPnl/root_n/list_sv/view/content/item/open_be',\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -28),\n                            angle: 0,\n                        }\n                    },\n                    taEvent: '1-22', //款选打开宝箱按钮\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_CLAIM_TREASURE,\n                    },\n                    nodeChoose: {\n                        name: 'View/TreasureListPnl/root_n/list_sv/view/content/item/claim_be',\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -28),\n                            angle: 0,\n                        }\n                    },\n                    tag: GuideTagType.CLAIM_TREASURE,\n                    taEvent: '1-23', //款选领取宝箱按钮\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncCloseTreasureAndArmyPnl', //关闭所有界面\n                    },\n                    tag: GuideTagType.CLAIM_TREASURE_COMPLETE,\n                    restartPoint: GuideTagType.READY_BT_BARRACKS,\n                    taEvent: '1-24',\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100060' },\n                    ],\n                    tag: GuideTagType.READY_BT_BARRACKS,\n                    taEvent: '1-25', //领取完成对话\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncInArea', //检测是否在战斗区域\n                    },\n                    taEvent: '1-26',\n                },\n                {\n                    type: GuideStepType.ON_EVENT, //框选返回按钮\n                    event: {\n                        name: 'WIND_ENTER_303',\n                        args: { key: 'novice' },\n                    },\n                    nodeChoose: {\n                        name: 'View/UIPnl/scene_n/area/back_main_be',\n                    },\n                    taEvent: '1-27', //框选返回按钮\n                },\n                {\n                    type: GuideStepType.CHECK_WAIT, //检测是否可以修建兵营\n                    func: {\n                        name: 'checkFuncCanBTBarracks',\n                        args: BUILD_BARRACKS_NID,\n                    },\n                    tag: GuideTagType.CHECK_CAN_BT_BARRACKS,\n                    taEvent: '1-28',\n                },\n                // 框选主城\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_OPEN_MAP_CELL,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseMainCity',\n                        finger: {\n                            offset: cc.v2(0, 80),\n                            angle: 180,\n                        }\n                    },\n                    tag: GuideTagType.CHECK_CAN_BT_BARRACKS,\n                    taEvent: '1-29' //款选主城\n                },\n                // 框选进入\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'WIND_ENTER_303',\n                        args: { key: 'area' },\n                    },\n                    nodeChoose: {\n                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n                        hideChoose: true\n                    },\n                    taEvent: '1-30' //框选进入\n                }, {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100070' },\n                    ],\n                    taEvent: '1-31'\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_210',\n                        args: { key: 'area/BuildList' },\n                    },\n                    nodeChoose: {\n                        name: 'View/UIPnl/scene_n/area/bottom/build_be_n',\n                    },\n                    taEvent: '1-32', //框选建筑按钮\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.26,\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_LEAVE_211',\n                        args: { key: 'area/BuildList' },\n                    },\n                    nodeChoose: {\n                        name: `View/BuildListPnl/root/list_sv/view/content/item_${BUILD_BARRACKS_NID}/button/unlock_be`,\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -28),\n                            angle: 0,\n                        }\n                    },\n                    taEvent: '1-33', //框选修建按钮\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncCanXlPawn', //检测是否可以训练士兵\n                    },\n                    tag: GuideTagType.CHECK_CAN_XL_PAWN,\n                    restartPoint: GuideTagType.CHECK_CAN_XL_PAWN, //存档点,\n                    taEvent: '1-34'\n                },  // 框选主城\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_OPEN_MAP_CELL,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseMainCity',\n                        finger: {\n                            offset: cc.v2(0, 80),\n                            angle: 180,\n                        }\n                    },\n                    taEvent: '1-35' //款选主城\n                },\n                // 框选进入\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'WIND_ENTER_303',\n                        args: { key: 'area' },\n                    },\n                    nodeChoose: {\n                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',\n                        hideChoose: true\n                    },\n                    taEvent: '1-36' //框选进入\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.5,\n                    tag: 'build_complete',\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100080' },\n                    ],\n                    taEvent: '1-37', //兵营修建好了对话\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_210',\n                        args: { key: 'build/BuildBarracks' },\n                    },\n                    nodeChoose: {\n                        name: 'Wind/AreaWind/root/role_n/BUILD_2004/body',\n                        desc: 'guideText.choose_desc_004',\n                        moveCamera: true,\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, 0),\n                            angle: 0,\n                        }\n                    },\n                    taEvent: '1-38', //框选兵营\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.26,\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n                    },\n                    nodeChoose: {\n                        name: 'View/BuildBarracksPnl/root/tabs_tc_tce/1',\n                    },\n                    taEvent: '1-39', //框选招募步兵页签\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100015' },\n                    ],\n                    taEvent: '1-40'\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_ENTER_210',\n                        args: { key: 'common/CreateArmy' },\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseCreateArmy',\n                        desc: 'guideText.choose_desc_009',\n                        finger: {\n                            offset: cc.v2(0, -40),\n                        },\n                    },\n                    taEvent: '1-41', //款选创建军队\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.26,\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: 'PNL_LEAVE_211',\n                        args: { key: 'common/CreateArmy' },\n                    },\n                    nodeChoose: {\n                        name: 'View/CreateArmyPnl/root',\n                    },\n                    taEvent: '1-42', //款选整个创建军队界面\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkGotoPawnDrill'\n                    },\n                    taEvent: '1-43'\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100016' },\n                    ],\n                    taEvent: '1-44'\n                },\n                {\n                    type: GuideStepType.ON_EVENT,//框选研究士兵+号按钮\n                    event: {\n                        name: 'PNL_ENTER_PLAY_DONE_215',\n                        args: { key: 'build/StudySelect' },\n                    },\n                    nodeChoose: {\n                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/pawn/list/view/content/pawn_be',\n                        desc: 'guideText.choose_desc_008'\n                    },\n                    taEvent: '1-45'\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100017' },\n                    ],\n                    taEvent: '1-46'\n                },\n                {\n                    type: GuideStepType.ON_EVENT,//框选第一个士兵研究，固定斧盾兵\n                    event: {\n                        name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n                    },\n                    nodeChoose: {\n                        func: 'nodeChooseTargetPawn',\n                    },\n                    taEvent: '1-47'\n                },\n                {\n                    type: GuideStepType.ON_EVENT, //框选开始研究\n                    event: {\n                        name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n                    },\n                    nodeChoose: {\n                        name: 'View/StudySelectPnl/root/buttons/study_be',\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -36),\n                        },\n                    },\n                    taEvent: '1-48'\n                },\n                {\n                    type: GuideStepType.DELAY,\n                    time: 0.1\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100018' },\n                    ],\n                    taEvent: '1-49'\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncDrillAcc', //招募加速\n                    },\n                    tag: GuideTagType.FIRST_CREATE_ARMY,\n                    taEvent: '1-50'\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncChoosePawn',//检测是否可以选择士兵\n                    },\n                    taEvent: '1-51'\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n                    },\n                    nodeChoose: {\n                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/pawn/list/view/content/pawn_be',\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -50),\n                        },\n                    },\n                    taEvent: '1-52', //款选招募\n                },\n                {\n                    type: GuideStepType.ON_EVENT,\n                    event: {\n                        name: EventType.GUIDE_CLICK_CHOOSE_RECT,\n                    },\n                    nodeChoose: {\n                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/cond/need/buttons/drill_be',\n                        hide: true,\n                        finger: {\n                            offset: cc.v2(0, -36),\n                        },\n                    },\n                    taEvent: '1-53', //款选招募\n                },\n                {\n                    type: GuideStepType.DELAY,//等回调先执行再关闭界面\n                    time: 0.01,\n                    restartPoint: GuideTagType.FIRST_GUIDE_DONE, //完成招募\n                },\n                {\n                    type: GuideStepType.CHECK,\n                    func: {\n                        name: 'checkFuncCloseBarracks', //关闭兵营界面\n                    },\n                    taEvent: '1-54'\n                },\n                {\n                    type: GuideStepType.DIALOG,\n                    content: [\n                        { text: 'guideText.dialog_100090' },\n                        { text: 'guideText.dialog_100091' },\n                    ],\n                    taEvent: '1-55'\n                },\n                {\n                    type: GuideStepType.NONE,\n                    tag: GuideTagType.FIRST_GUIDE_DONE,\n                    taEvent: '1-56',//第一个教程结束\n                }\n            ]\n        },\n        guideHospital,\n        // guideMultiAttack,\n        guidePawnBackMain,\n        // guideSendRes,\n        // guideSendResTip,\n        guideSmithy,\n        guideWearEquip,\n        // guideWorshipHero,\n        // guidePawnToHero,\n        guideBuildAcc,\n        guideLandStone,\n        guideBattleBegin,\n        guideFirstBattleEnd,\n        guideBattleEnd,\n        guideNewbieZone,\n    ]\n}\n\nexport {\n    GuideStepType,\n    GuideTagType,\n    GuideStepInfo,\n    GuideTextArgsType,\n    GUIDE_CONFIG,\n}\n"]}