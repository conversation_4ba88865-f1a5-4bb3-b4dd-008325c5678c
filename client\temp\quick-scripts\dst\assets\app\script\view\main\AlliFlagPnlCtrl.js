
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/AlliFlagPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e518eKKSS1O/adZHblQl8ya', 'AlliFlagPnlCtrl');
// app/script/view/main/AlliFlagPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AlliFlagPnlCtrl = /** @class */ (function (_super) {
    __extends(AlliFlagPnlCtrl, _super);
    function AlliFlagPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.flagsNode_ = null; // path://root/flags_tce_n
        _this.infoNode_ = null; // path://root/info_n
        _this.textNode_ = null; // path://root/text_n
        _this.inputDescEb_ = null; // path://root/text_n/input_desc_eb
        _this.ontopTge_ = null; // path://root/ontop_t_te
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.point = cc.v2();
        _this.index = -1;
        _this.selectFlag = 0;
        _this.isCanEdit = false;
        return _this;
    }
    AlliFlagPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AlliFlagPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AlliFlagPnlCtrl.prototype.onEnter = function (point, isOntop) {
        this.point.set(point);
        this.index = MapHelper_1.mapHelper.pointToIndex(point);
        this.isCanEdit = GameHelper_1.gameHpr.alliance.isMeMilitary();
        this.ontopTge_.setActive(isOntop);
        // 显示标记点
        this.updateFlags();
    };
    AlliFlagPnlCtrl.prototype.onRemove = function () {
        this.inputDescEb_.string = '';
    };
    AlliFlagPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/flags_tce_n
    AlliFlagPnlCtrl.prototype.onClickFlags = function (event, data) {
        !data && audioMgr.playSFX('click');
        var flag = this.selectFlag = Number(event.node.name);
        this.updateSelectFlag(flag);
    };
    // path://root/buttons_n/add_be
    AlliFlagPnlCtrl.prototype.onClickAdd = function (event, data) {
        var _this = this;
        if (!this.isCanEdit) {
            return ViewHelper_1.viewHelper.showAlert('toast.only_military_can_flag');
        }
        var index = this.index;
        var flag = this.selectFlag, desc = this.inputDescEb_.string.trim();
        if (ut.getStringLen(desc) > 18 || GameHelper_1.gameHpr.getTextNewlineCount(desc) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        var info = GameHelper_1.gameHpr.alliance.getMapFlagInfo(index);
        if (info) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.same_mark_tip', {
                params: [info.flag - 1, flag - 1],
                ok: function () { return _this.add(index, flag, desc); },
                cancel: function () { },
            });
        }
        this.add(index, flag, desc);
    };
    // path://root/buttons_n/cancel_be
    AlliFlagPnlCtrl.prototype.onClickCancel = function (event, _) {
        if (!this.isCanEdit) {
            return;
        }
        var flag = this.selectFlag;
        var it = this.flagsNode_.Child(flag), data = it.Data;
        if (data) {
            it.Data = null;
            it.Child('root/bg').opacity = 128;
            this.updateSelectFlag(flag);
            GameHelper_1.gameHpr.alliance.removeMapFlag(data.index);
        }
        // 删除记录
        GameHelper_1.gameHpr.updateOntopFlagData(flag, -1, 1);
    };
    // path://root/buttons_n/goto_be
    AlliFlagPnlCtrl.prototype.onClickGoto = function (event, _) {
        var _a;
        var data = (_a = this.flagsNode_.Child(this.selectFlag)) === null || _a === void 0 ? void 0 : _a.Data;
        if (data) {
            this.emit(mc.Event.HIDE_ALL_PNL, 'main/AlliFlag|main/WorldMap');
            GameHelper_1.gameHpr.gotoTargetPos(data.index);
        }
    };
    // path://root/ontop_t_te
    AlliFlagPnlCtrl.prototype.onClickOntop = function (event, _) {
        var _a;
        var data = (_a = this.flagsNode_.Child(this.selectFlag)) === null || _a === void 0 ? void 0 : _a.Data;
        if (!data) {
            return;
        }
        else if (!event.isChecked) {
            return GameHelper_1.gameHpr.updateOntopFlagData(this.selectFlag, -1, 1);
        }
        else if (GameHelper_1.gameHpr.getOntopFlagCount() >= 10) {
            event.isChecked = false;
            return ViewHelper_1.viewHelper.showAlert('toast.ontop_flag_count_full');
        }
        GameHelper_1.gameHpr.updateOntopFlagData(this.selectFlag, data.index, 1);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AlliFlagPnlCtrl.prototype.updateFlags = function () {
        var _this = this;
        var _a;
        var markMap = GameHelper_1.gameHpr.alliance.getMapFlag();
        var flagMap = {};
        for (var key in markMap) {
            var mark = markMap[key];
            flagMap[mark.flag] = { index: Number(key), desc: mark.desc };
        }
        var select = (_a = markMap[this.index]) === null || _a === void 0 ? void 0 : _a.flag;
        this.flagsNode_.children.forEach(function (m) {
            var flag = Number(m.name);
            var info = m.Data = flagMap[flag];
            m.Child('root/bg').opacity = info ? 255 : 128;
            if ((_this.isCanEdit ? !info : !!info) && !select) {
                select = flag;
            }
        });
        this.flagsNode_.Component(cc.ToggleContainer).Tabs(select || 1);
    };
    AlliFlagPnlCtrl.prototype.updateSelectFlag = function (flag) {
        var _a, _b;
        var node = this.flagsNode_.Child(flag), data = node.Data;
        var point = data ? MapHelper_1.mapHelper.indexToPoint(data.index) : this.point, has = !!data;
        this.infoNode_.Child('name/val').setLocaleKey('ui.flag_name', flag - 1);
        this.infoNode_.Child('x', cc.Label).string = 'X: ' + point.x;
        this.infoNode_.Child('y', cc.Label).string = 'Y: ' + point.y;
        var isEdit = this.isCanEdit, addButton = this.buttonsNode_.Child('add_be');
        this.inputDescEb_.setActive(!has && isEdit);
        if (this.textNode_.Child('text').active = has || !isEdit) {
            this.textNode_.Child('text/val', cc.Label).string = (data === null || data === void 0 ? void 0 : data.desc) || '-';
        }
        addButton.active = !has;
        addButton.opacity = isEdit ? 255 : 150;
        this.buttonsNode_.Child('cancel_be').active = has && isEdit;
        // 显示置顶
        if (this.ontopTge_.getActive()) {
            this.buttonsNode_.Child('goto_be').active = has;
            this.ontopTge_.node.opacity = has ? 255 : 100;
            this.ontopTge_.interactable = has;
            if (has) {
                var index = (_b = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS)) === null || _a === void 0 ? void 0 : _a['1_' + flag]) !== null && _b !== void 0 ? _b : -1;
                this.ontopTge_.isChecked = index >= 0;
            }
            else {
                this.ontopTge_.isChecked = false;
            }
        }
        else {
            this.buttonsNode_.Child('goto_be').active = has && data.index !== this.index;
        }
    };
    AlliFlagPnlCtrl.prototype.add = function (index, flag, desc) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, info;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_AlliMapFlag', { index: index, flag: flag, desc: desc }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.hide();
                        }
                        info = GameHelper_1.gameHpr.alliance.getMapFlagInfo(index);
                        if (info) {
                            GameHelper_1.gameHpr.updateOntopFlagData(info.flag, -1, 1);
                        }
                        GameHelper_1.gameHpr.alliance.addMapFlag(index, flag, desc);
                        return [2 /*return*/];
                }
            });
        });
    };
    AlliFlagPnlCtrl = __decorate([
        ccclass
    ], AlliFlagPnlCtrl);
    return AlliFlagPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliFlagPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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