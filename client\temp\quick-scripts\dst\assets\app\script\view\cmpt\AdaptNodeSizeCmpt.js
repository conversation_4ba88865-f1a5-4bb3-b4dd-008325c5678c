
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AdaptNodeSizeCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8220ehpFmdFma3kJzWtMHTq', 'AdaptNodeSizeCmpt');
// app/script/view/cmpt/AdaptNodeSizeCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, menu = _a.menu;
/**
 * 适配节点大小
 */
var AdaptNodeWidthCmpt = /** @class */ (function (_super) {
    __extends(AdaptNodeWidthCmpt, _super);
    function AdaptNodeWidthCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.targets = [];
        _this.min = 0;
        _this.preWidth = -1;
        return _this;
    }
    AdaptNodeWidthCmpt.prototype.update = function () {
        if (this.targets.length === 0) {
            return;
        }
        var w = 0;
        this.targets.forEach(function (m) {
            if (!m.active || !m.isValid) {
                return;
            }
            var nw = m.width * m.scaleX;
            if (nw > w) {
                w = nw;
            }
        });
        if (w !== this.preWidth) {
            this.preWidth = w;
            this.node.width = Math.max(this.min, w);
        }
    };
    __decorate([
        property([cc.Node])
    ], AdaptNodeWidthCmpt.prototype, "targets", void 0);
    __decorate([
        property()
    ], AdaptNodeWidthCmpt.prototype, "min", void 0);
    AdaptNodeWidthCmpt = __decorate([
        ccclass
    ], AdaptNodeWidthCmpt);
    return AdaptNodeWidthCmpt;
}(cc.Component));
exports.default = AdaptNodeWidthCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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