
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/BuildObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd7b28H+zZVG9KPf92t0iECp', 'BuildObj');
// app/script/model/area/BuildObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
// 战场里面的建筑设施
var BuildObj = /** @class */ (function () {
    function BuildObj() {
        this.aIndex = 0; //所属哪个区域
        this.uid = '';
        this.id = 0;
        this.lv = 0; //等级 必须从0开始因为创建的时候是0级
        this.point = cc.v2();
        this.points = [];
        this.attrId = 0;
        this.baseJson = null;
        this.attrJson = null;
        this.size = cc.v2();
        this.effect = null; //效果
        this.maxEffect = null; //最高级效果
        this.upCost = []; //下级升级费用
        this.nextLvInfo = null; //下一级的属性信息
        this.tempNextLv = 0;
    }
    BuildObj.prototype.init = function (index, uid, point, id, lv) {
        this.aIndex = index;
        this.uid = uid;
        this.id = id;
        this.lv = lv;
        this.point.set(point);
        this.initJson();
        return this;
    };
    BuildObj.prototype.fromSvr = function (data) {
        this.aIndex = data.index;
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv;
        this.point.set(data.point);
        this.initJson();
        return this;
    };
    BuildObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            uid: this.uid,
            point: this.point.toJson(),
            id: this.id,
            lv: this.lv,
        };
    };
    BuildObj.prototype.initJson = function () {
        this.baseJson = assetsMgr.getJsonData('buildBase', this.id);
        this.size = ut.stringToVec2(this.baseJson.size, 'x');
        this.points = MapHelper_1.mapHelper.genPointsBySize(this.size);
        this.updateAttrJson();
    };
    BuildObj.prototype.updateAttrJson = function () {
        var _a, _b;
        this.attrId = this.getAttrId();
        this.attrJson = assetsMgr.getJsonData('buildAttr', this.attrId);
        this.effect = GameHelper_1.gameHpr.stringToCEffects((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.effects)[0];
        this.upCost = GameHelper_1.gameHpr.stringToCTypes((_b = this.attrJson) === null || _b === void 0 ? void 0 : _b.up_cost);
        var nlv = this.lv + 1;
        var json = assetsMgr.getJsonData('buildAttr', this.getAttrId(nlv));
        this.nextLvInfo = json && {
            lv: nlv,
            json: json,
            effect: GameHelper_1.gameHpr.stringToCEffects(json.effects)[0]
        };
    };
    Object.defineProperty(BuildObj.prototype, "name", {
        get: function () { return 'buildText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuildObj.prototype, "desc", {
        get: function () { return 'buildText.desc_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuildObj.prototype, "type", {
        get: function () { return this.baseJson.type; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BuildObj.prototype, "icon", {
        get: function () {
            // if (this.isWall()) {
            //     const lv = Math.ceil(this.lv / 5)
            //     return 'build_' + this.id + '_0' + lv
            // }
            return 'build_' + this.id;
        },
        enumerable: false,
        configurable: true
    });
    BuildObj.prototype.getPrefabUrl = function () { return 'build/BUILD_' + this.id; };
    BuildObj.prototype.getUIUrl = function () { return 'build/' + this.baseJson.ui; };
    BuildObj.prototype.getAttrId = function (lv) {
        lv = lv !== null && lv !== void 0 ? lv : this.lv;
        return this.id * 1000 + lv;
    };
    // 是否满级
    BuildObj.prototype.isMaxLv = function () {
        return !this.nextLvInfo;
    };
    // 是否城墙
    BuildObj.prototype.isWall = function () {
        return this.id === Constant_1.BUILD_WALL_NID;
    };
    // 是否可以拖拽
    BuildObj.prototype.isCanDrag = function () {
        var _a;
        return !!((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.drag);
    };
    // 是否遗迹
    BuildObj.prototype.isAncient = function () {
        return this.id >= Constant_1.CITY_CHANGAN_ID && this.id <= Constant_1.CITY_LUOYANG_ID;
    };
    // 刷新等级
    BuildObj.prototype.updateLv = function (lv) {
        this.lv = lv;
        this.updateAttrJson();
    };
    // 获取关联的士兵id
    BuildObj.prototype.getBuildPawnId = function () {
        var _a;
        return (_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.pawn_id;
    };
    // 获取实际的点位
    BuildObj.prototype.getActPoints = function (point) {
        point = point || this.point;
        return this.points.map(function (m) { return m.add(point); });
    };
    // 获取最高级的效果
    BuildObj.prototype.getMaxEffect = function () {
        var json = assetsMgr.getJsonData('buildAttr', this.getAttrId(20));
        return json ? GameHelper_1.gameHpr.stringToCEffects(json.effects)[0] : null;
    };
    // 获取效果值
    BuildObj.prototype.getEffectValue = function (type) {
        var _a;
        if (((_a = this.effect) === null || _a === void 0 ? void 0 : _a.type) === type) {
            return this.effect.value;
        }
        return 0;
    };
    // 获取用于视图的属性列表
    BuildObj.prototype.getEffectsForView = function () {
        var _a;
        var currEffect = this.effect, nextEffect = (_a = this.nextLvInfo) === null || _a === void 0 ? void 0 : _a.effect;
        if (!currEffect) {
            return [];
        }
        else if (nextEffect) {
            return [{ curr: currEffect.getInfoForBuild(), nextVal: nextEffect.getValueText() }];
        }
        return [{ curr: currEffect.getInfoForBuild() }];
    };
    return BuildObj;
}());
exports.default = BuildObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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