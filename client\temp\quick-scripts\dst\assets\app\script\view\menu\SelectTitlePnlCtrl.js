
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/SelectTitlePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd2f8edK6GNNfKz98B/jSVjB', 'SelectTitlePnlCtrl');
// app/script/view/menu/SelectTitlePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var SelectTitlePnlCtrl = /** @class */ (function (_super) {
    __extends(SelectTitlePnlCtrl, _super);
    function SelectTitlePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.cb = null;
        _this.selectTitle = 0;
        _this.selectNode = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    SelectTitlePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectTitlePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectTitlePnlCtrl.prototype.onEnter = function (cb) {
        var _this = this;
        this.cb = cb;
        this.selectTitle = GameHelper_1.gameHpr.user.getTitle();
        var list = [{}].concat(GameHelper_1.gameHpr.user.getUnlockTitles());
        var now = Date.now();
        this.listSv_.List(list.length, function (it, i) {
            var data = list[i];
            var json = it.Data = assetsMgr.getJsonData('title', data.id);
            var id = (json === null || json === void 0 ? void 0 : json.id) || 0;
            ResHelper_1.resHelper.loadTitleIcon(it.Child('icon'), (json === null || json === void 0 ? void 0 : json.quality) || 1, (json === null || json === void 0 ? void 0 : json.icon) || 'title_none', _this.key);
            it.Child('name').Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey(json ? 'titleText.' + id : 'ui.nought');
            if (json === null || json === void 0 ? void 0 : json.duration) {
                var time = data.remainTime - (now - data.getTime);
                it.Child('time').setLocaleKey('ui.remain_day', Math.ceil(time / ut.Time.Day));
            }
            else {
                it.Child('time').setLocaleKey('');
            }
            it.Child('desc').setLocaleKey(json ? json.desc : 'ui.no_wear_title', String((json === null || json === void 0 ? void 0 : json.params) || '').split('|'));
            if (it.Child('select').active = id === _this.selectTitle) {
                _this.selectNode = it;
            }
        });
    };
    SelectTitlePnlCtrl.prototype.onRemove = function () {
    };
    SelectTitlePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    SelectTitlePnlCtrl.prototype.onClickItem = function (event, data) {
        var _a, _b, _c;
        var it = event.target;
        if (it.uuid !== ((_a = this.selectNode) === null || _a === void 0 ? void 0 : _a.uuid)) {
            it.Child('select').active = true;
            (_b = this.selectNode) === null || _b === void 0 ? void 0 : _b.Child('select').setActive(false);
            this.selectNode = it;
            this.selectTitle = ((_c = it.Data) === null || _c === void 0 ? void 0 : _c.id) || 0;
        }
    };
    // path://root/ok_be
    SelectTitlePnlCtrl.prototype.onClickOk = function (event, data) {
        this.cb && this.cb(this.selectTitle);
        this.hide();
    };
    SelectTitlePnlCtrl = __decorate([
        ccclass
    ], SelectTitlePnlCtrl);
    return SelectTitlePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectTitlePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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