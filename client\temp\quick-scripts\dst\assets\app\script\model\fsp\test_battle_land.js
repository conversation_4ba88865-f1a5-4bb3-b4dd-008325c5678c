
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/test_battle_land.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '72bd5lCp3lOnpu7Yx5i9Srh', 'test_battle_land');
// app/script/model/fsp/test_battle_land.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ARMY_CONF = void 0;
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var MapCellObj_1 = require("../main/MapCellObj");
var testConfig_1 = require("./testConfig");
Object.defineProperty(exports, "ARMY_CONF", { enumerable: true, get: function () { return testConfig_1.ARMY_CONF; } });
window['battleTestLand'] = function (id, type, dirType) {
    if (dirType === void 0) { dirType = 2; }
    var armyArr = testConfig_1.ARMY_CONF[id];
    if (!armyArr) {
        return console.log('没找到相关配置 id: ' + id);
    }
    var mainCityPoint = GameHelper_1.gameHpr.player.getMainCityPoint(), dis = id % 1000;
    var targetPoint = cc.v2(mainCityPoint.x, mainCityPoint.y + dis + 1);
    var mainCityIndex = MapHelper_1.mapHelper.pointToIndex(mainCityPoint);
    var targetIndex = MapHelper_1.mapHelper.pointToIndex(targetPoint);
    var getPoint = function (dir) {
        var point = cc.v2(5, 0); //默认下面进
        if (dir === 0) {
            point = cc.v2(5, 10);
        }
        else if (dir === 1) {
            point = cc.v2(10, 5);
        }
        else if (dir === 3) {
            point = cc.v2(0, 5);
        }
        return point.toJson();
    };
    // 目标地块
    var targetCell = new MapCellObj_1.default().init(targetIndex, type * 100 + Math.floor(id / 1000));
    // 我的军队
    var selectArmys = armyArr.map(function (m, i) {
        return {
            index: mainCityIndex,
            uid: ut.UID(),
            name: "编队" + i,
            pawns: m.pawns.map(function (p) {
                return {
                    uid: ut.UID(),
                    id: p.id,
                    lv: p.lv,
                    index: mainCityIndex,
                    point: (m.dir ? getPoint(m.dir) : getPoint(dirType)),
                    equip: p.equip,
                };
            }),
            marchTime: m.marchTime || 0,
        };
    });
    ViewHelper_1.viewHelper.hidePnl('main/BattleForecast');
    ViewHelper_1.viewHelper.showPnl('main/BattleForecast', selectArmys, targetCell, true);
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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