
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/layer/WindLayerCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '68689n45lpJo552fZbq7ziN', 'WindLayerCtrl');
// app/core/layer/WindLayerCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseLayerCtrl_1 = require("../base/BaseLayerCtrl");
var CoreEventType_1 = require("../event/CoreEventType");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var WindLayerCtrl = /** @class */ (function (_super) {
    __extends(WindLayerCtrl, _super);
    function WindLayerCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ctrlMgr = null;
        return _this;
    }
    WindLayerCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[CoreEventType_1.default.GOTO_WIND] = this.onGotoWind, _a),
            (_b = {}, _b[CoreEventType_1.default.PRELOAD_WIND] = this.onPreloadWind, _b),
            (_c = {}, _c[CoreEventType_1.default.CLEAN_CACHE_WIND] = this.onCleanCacheWind, _c),
        ];
    };
    WindLayerCtrl.prototype.onCreate = function () {
    };
    WindLayerCtrl.prototype.onClean = function () {
    };
    WindLayerCtrl.prototype.setCtrlMgr = function (mgr) {
        this.ctrlMgr = mgr;
        this.ctrlMgr.node = this.node;
    };
    // 获取当前场景
    WindLayerCtrl.prototype.getCurrWind = function () {
        return this.ctrlMgr.currWind;
    };
    WindLayerCtrl.prototype.onGotoWind = function (key, complete) {
        var _a;
        var params = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            params[_i - 2] = arguments[_i];
        }
        (_a = this.ctrlMgr).goto.apply(_a, __spreadArrays([key], params)).then(function () { return complete && complete(); });
    };
    WindLayerCtrl.prototype.onPreloadWind = function (key, complete, progress) {
        this.ctrlMgr.preLoad(key, progress, complete);
    };
    // 清理所有缓存中的wind
    WindLayerCtrl.prototype.onCleanCacheWind = function () {
        this.ctrlMgr.cleanCacheWind();
    };
    WindLayerCtrl = __decorate([
        ccclass
    ], WindLayerCtrl);
    return WindLayerCtrl;
}(BaseLayerCtrl_1.default));
exports.default = WindLayerCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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