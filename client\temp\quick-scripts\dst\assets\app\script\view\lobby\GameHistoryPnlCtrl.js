
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/GameHistoryPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bd6609zdRZJXIGiWZt+2giB', 'GameHistoryPnlCtrl');
// app/script/view/lobby/GameHistoryPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var GameHistoryPnlCtrl = /** @class */ (function (_super) {
    __extends(GameHistoryPnlCtrl, _super);
    function GameHistoryPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.historySv_ = null; // path://root/content/history_sv
        _this.nextNode_ = null; // path://root/content/bottom/next_n
        _this.serverTypeSelectNode_ = null; // path://root/content/bottom/server_type_select_be_n
        //@end
        _this.curSelectServerType = 'all';
        _this.curPage = 0;
        _this.maxPage = 0;
        return _this;
    }
    GameHistoryPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GameHistoryPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    GameHistoryPnlCtrl.prototype.onEnter = function (mode) {
        var type = mode;
        this.curSelectServerType = '' + type;
        this.nextNode_.Child('text/val', cc.Label).string = '';
        this.showGameHistoryList();
        this.selectServerTypeItem(this.serverTypeSelectNode_, this.curSelectServerType, true);
        var selectNode = this.serverTypeSelectNode_.Child('mask/root/server_type_items_nbe');
        selectNode.Child('all').active = false;
    };
    GameHistoryPnlCtrl.prototype.onRemove = function () {
    };
    GameHistoryPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content/history_sv/view/content/item/detail_be
    GameHistoryPnlCtrl.prototype.onClickDetail = function (event, _data) {
        var data = event.target.parent.Data;
        data && ViewHelper_1.viewHelper.showPnl('lobby/GameDetail', data);
    };
    // path://root/content/bottom/next_n/next_page_be@0
    GameHistoryPnlCtrl.prototype.onClickNextPage = function (event, data) {
        var add = data === '0' ? -1 : 1;
        var curPage = this.curPage || 0, maxPage = this.maxPage;
        var index = ut.loopValue(curPage + add, maxPage);
        if (index !== curPage) {
            this.curPage = index;
            this.showGameHistoryList();
        }
    };
    // path://root/content/bottom/server_type_select_be_n
    GameHistoryPnlCtrl.prototype.onClickServerTypeSelect = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/content/bottom/server_type_select_be_n/select_mask_be
    GameHistoryPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/content/bottom/server_type_select_be_n/mask/root/server_type_items_nbe
    GameHistoryPnlCtrl.prototype.onClickServerTypeItems = function (event, data) {
        var node = this.serverTypeSelectNode_;
        ViewHelper_1.viewHelper.changePopupBoxList(node, false);
        var type = event.target.name;
        if (type !== this.curSelectServerType) {
            this.selectServerTypeItem(node, type);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 个人战绩
    GameHistoryPnlCtrl.prototype.showGameHistoryList = function () {
        var _this = this;
        var empty = this.historySv_.node.Child('empty'), uid = GameHelper_1.gameHpr.user.getUid();
        empty.active = false;
        this.updatePageButton();
        var serverTypeStr = this.curSelectServerType;
        var serverType = serverTypeStr === 'all' ? -1 : (Number(serverTypeStr) || 0);
        var page = this.curPage + 1;
        GameHelper_1.gameHpr.net.request('chat/HD_GetGameHistoryList', { page: page, serverType: serverType }, true).then(function (_a) {
            var _b;
            var err = _a.err, data = _a.data;
            var list = (data === null || data === void 0 ? void 0 : data.datas) || [], len = list.length;
            empty.active = !len;
            _this.historySv_.stopAutoScroll();
            _this.historySv_.content.y = 0;
            _this.historySv_.List(len, function (it, i) {
                ViewHelper_1.viewHelper.updateGameHistoryItem(it, list[i], _this.key);
                it.Child('line').active = i < len - 1;
            });
            if (data && data.totalPage !== -1 && page === 1) {
                _this.maxPage = (_b = data.totalPage) !== null && _b !== void 0 ? _b : (_this.maxPage || 1);
            }
            _this.updatePageButton();
        });
    };
    GameHistoryPnlCtrl.prototype.updatePageButton = function () {
        var curPage = this.curPage || 0, maxPage = this.maxPage || 0;
        curPage = Math.min(curPage + 1, maxPage);
        this.nextNode_.Child('text/val', cc.Label).string = curPage + '/' + maxPage;
        var pre = this.nextNode_.Child('next_page_be@0', cc.Button), next = this.nextNode_.Child('next_page_be@1', cc.Button);
        pre.Component(cc.MultiFrame).setFrame(curPage > 1);
        pre.interactable = curPage > 1;
        next.Component(cc.MultiFrame).setFrame(curPage < maxPage);
        next.interactable = curPage < maxPage;
    };
    // 选择排序
    GameHistoryPnlCtrl.prototype.selectServerTypeItem = function (node, type, init) {
        var oldType = this.curSelectServerType;
        node.Data = this.curSelectServerType = type;
        node.Child('val', cc.Label).setLocaleKey(type === 'all' ? 'ui.bazaar_filter_all' : 'ui.title_server_name_' + type);
        node.Child('mask/root/server_type_items_nbe').children.forEach(function (m) {
            var select = m.name === type;
            m.Child('val').Color(select ? '#B6A591' : '#756963');
            m.Child('select').active = select;
        });
        if (init || oldType === type) {
            return;
        }
        this.curPage = 0;
        this.showGameHistoryList();
    };
    GameHistoryPnlCtrl = __decorate([
        ccclass
    ], GameHistoryPnlCtrl);
    return GameHistoryPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GameHistoryPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGxvYmJ5XFxHYW1lSGlzdG9yeVBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsNkRBQXlEO0FBQ3pELDZEQUE0RDtBQUVwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFnRCxzQ0FBYztJQUE5RDtRQUFBLHFFQXFJQztRQW5JQSwwQkFBMEI7UUFDbEIsZ0JBQVUsR0FBa0IsSUFBSSxDQUFBLENBQUMsaUNBQWlDO1FBQ2xFLGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQ0FBb0M7UUFDOUQsMkJBQXFCLEdBQVksSUFBSSxDQUFBLENBQUMscURBQXFEO1FBQ25HLE1BQU07UUFFRSx5QkFBbUIsR0FBRyxLQUFLLENBQUE7UUFDM0IsYUFBTyxHQUFHLENBQUMsQ0FBQTtRQUNYLGFBQU8sR0FBRyxDQUFDLENBQUE7O0lBMkhwQixDQUFDO0lBekhPLDRDQUFlLEdBQXRCO1FBQ0MsT0FBTyxFQUFFLENBQUE7SUFDVixDQUFDO0lBRVkscUNBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0sb0NBQU8sR0FBZCxVQUFlLElBQVk7UUFDMUIsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFBO1FBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsTUFBTSxHQUFHLEVBQUUsQ0FBQTtRQUN0RCxJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQTtRQUMxQixJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLHFCQUFxQixFQUFFLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsQ0FBQTtRQUNyRixJQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMscUJBQXFCLENBQUMsS0FBSyxDQUFDLGlDQUFpQyxDQUFDLENBQUE7UUFDdEYsVUFBVSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO0lBQ3ZDLENBQUM7SUFFTSxxQ0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLG9DQUFPLEdBQWQ7UUFDQyxTQUFTLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQ3hDLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLDZEQUE2RDtJQUM3RCwwQ0FBYSxHQUFiLFVBQWMsS0FBMEIsRUFBRSxLQUFhO1FBQ3RELElBQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQTtRQUNyQyxJQUFJLElBQUksdUJBQVUsQ0FBQyxPQUFPLENBQUMsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDckQsQ0FBQztJQUVELG1EQUFtRDtJQUNuRCw0Q0FBZSxHQUFmLFVBQWdCLEtBQTBCLEVBQUUsSUFBWTtRQUN2RCxJQUFNLEdBQUcsR0FBRyxJQUFJLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ2pDLElBQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxFQUFFLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFBO1FBQ3pELElBQU0sS0FBSyxHQUFHLEVBQUUsQ0FBQyxTQUFTLENBQUMsT0FBTyxHQUFHLEdBQUcsRUFBRSxPQUFPLENBQUMsQ0FBQTtRQUNsRCxJQUFJLEtBQUssS0FBSyxPQUFPLEVBQUU7WUFDdEIsSUFBSSxDQUFDLE9BQU8sR0FBRyxLQUFLLENBQUE7WUFDcEIsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUE7U0FDMUI7SUFDRixDQUFDO0lBRUQscURBQXFEO0lBQ3JELG9EQUF1QixHQUF2QixVQUF3QixLQUEwQixFQUFFLElBQVk7UUFDL0QsUUFBUSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUN6Qix1QkFBVSxDQUFDLGtCQUFrQixDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDbEQsQ0FBQztJQUVELG9FQUFvRTtJQUNwRSw4Q0FBaUIsR0FBakIsVUFBa0IsS0FBMEIsRUFBRSxJQUFZO1FBQ3pELHVCQUFVLENBQUMsa0JBQWtCLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsS0FBSyxDQUFDLENBQUE7SUFDMUQsQ0FBQztJQUVELHFGQUFxRjtJQUNyRixtREFBc0IsR0FBdEIsVUFBdUIsS0FBMEIsRUFBRSxJQUFZO1FBQzlELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxxQkFBcUIsQ0FBQTtRQUN2Qyx1QkFBVSxDQUFDLGtCQUFrQixDQUFDLElBQUksRUFBRSxLQUFLLENBQUMsQ0FBQTtRQUMxQyxJQUFNLElBQUksR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQTtRQUM5QixJQUFJLElBQUksS0FBSyxJQUFJLENBQUMsbUJBQW1CLEVBQUU7WUFDdEMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQTtTQUNyQztJQUNGLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRWpILGlIQUFpSDtJQUVqSCxPQUFPO0lBQ0MsZ0RBQW1CLEdBQTNCO1FBQUEsaUJBcUJDO1FBcEJBLElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsRUFBRSxHQUFHLEdBQUcsb0JBQU8sQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUE7UUFDOUUsS0FBSyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7UUFDcEIsSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUE7UUFDdkIsSUFBSSxhQUFhLEdBQUcsSUFBSSxDQUFDLG1CQUFtQixDQUFBO1FBQzVDLElBQU0sVUFBVSxHQUFHLGFBQWEsS0FBSyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQTtRQUM5RSxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsT0FBTyxHQUFHLENBQUMsQ0FBQTtRQUM3QixvQkFBTyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsNEJBQTRCLEVBQUUsRUFBRSxJQUFJLE1BQUEsRUFBRSxVQUFVLFlBQUEsRUFBRSxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxVQUFDLEVBQWE7O2dCQUFYLEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQTtZQUM5RixJQUFNLElBQUksR0FBVSxDQUFBLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLEtBQUksRUFBRSxFQUFFLEdBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFBO1lBQ3hELEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxHQUFHLENBQUE7WUFDbkIsS0FBSSxDQUFDLFVBQVUsQ0FBQyxjQUFjLEVBQUUsQ0FBQTtZQUNoQyxLQUFJLENBQUMsVUFBVSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQzdCLEtBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxVQUFDLEVBQUUsRUFBRSxDQUFDO2dCQUMvQix1QkFBVSxDQUFDLHFCQUFxQixDQUFDLEVBQUUsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO2dCQUN2RCxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQTtZQUN0QyxDQUFDLENBQUMsQ0FBQTtZQUNGLElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxTQUFTLEtBQUssQ0FBQyxDQUFDLElBQUksSUFBSSxLQUFLLENBQUMsRUFBRTtnQkFDaEQsS0FBSSxDQUFDLE9BQU8sU0FBRyxJQUFJLENBQUMsU0FBUyxtQ0FBSSxDQUFDLEtBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxDQUFDLENBQUE7YUFDcEQ7WUFDRCxLQUFJLENBQUMsZ0JBQWdCLEVBQUUsQ0FBQTtRQUN4QixDQUFDLENBQUMsQ0FBQTtJQUNILENBQUM7SUFFTyw2Q0FBZ0IsR0FBeEI7UUFDQyxJQUFJLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxJQUFJLENBQUMsRUFBRSxPQUFPLEdBQUcsSUFBSSxDQUFDLE9BQU8sSUFBSSxDQUFDLENBQUE7UUFDNUQsT0FBTyxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsT0FBTyxHQUFHLENBQUMsRUFBRSxPQUFPLENBQUMsQ0FBQTtRQUN4QyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxPQUFPLEdBQUcsR0FBRyxHQUFHLE9BQU8sQ0FBQTtRQUMzRSxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLEVBQzVELElBQUksR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUE7UUFDekQsR0FBRyxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxDQUFDLENBQUMsQ0FBQTtRQUNsRCxHQUFHLENBQUMsWUFBWSxHQUFHLE9BQU8sR0FBRyxDQUFDLENBQUE7UUFDOUIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsUUFBUSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUMsQ0FBQTtRQUN6RCxJQUFJLENBQUMsWUFBWSxHQUFHLE9BQU8sR0FBRyxPQUFPLENBQUE7SUFDdEMsQ0FBQztJQUVELE9BQU87SUFDQyxpREFBb0IsR0FBNUIsVUFBNkIsSUFBYSxFQUFFLElBQVksRUFBRSxJQUFjO1FBQ3ZFLElBQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQTtRQUN4QyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxtQkFBbUIsR0FBRyxJQUFJLENBQUE7UUFDM0MsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLFlBQVksQ0FBQyxJQUFJLEtBQUssS0FBSyxDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDLENBQUE7UUFDbEgsSUFBSSxDQUFDLEtBQUssQ0FBQyxpQ0FBaUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO1lBQy9ELElBQU0sTUFBTSxHQUFHLENBQUMsQ0FBQyxJQUFJLEtBQUssSUFBSSxDQUFBO1lBQzlCLENBQUMsQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQTtZQUNwRCxDQUFDLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxNQUFNLENBQUE7UUFDbEMsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFJLElBQUksSUFBSSxPQUFPLEtBQUssSUFBSSxFQUFFO1lBQzdCLE9BQU07U0FDTjtRQUNELElBQUksQ0FBQyxPQUFPLEdBQUcsQ0FBQyxDQUFBO1FBQ2hCLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFBO0lBQzNCLENBQUM7SUFwSW1CLGtCQUFrQjtRQUR0QyxPQUFPO09BQ2Esa0JBQWtCLENBcUl0QztJQUFELHlCQUFDO0NBcklELEFBcUlDLENBckkrQyxFQUFFLENBQUMsV0FBVyxHQXFJN0Q7a0JBcklvQixrQkFBa0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcblxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5AY2NjbGFzc1xuZXhwb3J0IGRlZmF1bHQgY2xhc3MgR2FtZUhpc3RvcnlQbmxDdHJsIGV4dGVuZHMgbWMuQmFzZVBubEN0cmwge1xuXG5cdC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG5cdHByaXZhdGUgaGlzdG9yeVN2XzogY2MuU2Nyb2xsVmlldyA9IG51bGwgLy8gcGF0aDovL3Jvb3QvY29udGVudC9oaXN0b3J5X3N2XG5cdHByaXZhdGUgbmV4dE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9jb250ZW50L2JvdHRvbS9uZXh0X25cblx0cHJpdmF0ZSBzZXJ2ZXJUeXBlU2VsZWN0Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvYm90dG9tL3NlcnZlcl90eXBlX3NlbGVjdF9iZV9uXG5cdC8vQGVuZFxuXG5cdHByaXZhdGUgY3VyU2VsZWN0U2VydmVyVHlwZSA9ICdhbGwnXG5cdHByaXZhdGUgY3VyUGFnZSA9IDBcblx0cHJpdmF0ZSBtYXhQYWdlID0gMFxuXG5cdHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG5cdFx0cmV0dXJuIFtdXG5cdH1cblxuXHRwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG5cdH1cblxuXHRwdWJsaWMgb25FbnRlcihtb2RlOiBudW1iZXIpIHtcblx0XHRjb25zdCB0eXBlID0gbW9kZVxuXHRcdHRoaXMuY3VyU2VsZWN0U2VydmVyVHlwZSA9ICcnICsgdHlwZVxuXHRcdHRoaXMubmV4dE5vZGVfLkNoaWxkKCd0ZXh0L3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSAnJ1xuXHRcdHRoaXMuc2hvd0dhbWVIaXN0b3J5TGlzdCgpXG5cdFx0dGhpcy5zZWxlY3RTZXJ2ZXJUeXBlSXRlbSh0aGlzLnNlcnZlclR5cGVTZWxlY3ROb2RlXywgdGhpcy5jdXJTZWxlY3RTZXJ2ZXJUeXBlLCB0cnVlKVxuXHRcdGNvbnN0IHNlbGVjdE5vZGUgPSB0aGlzLnNlcnZlclR5cGVTZWxlY3ROb2RlXy5DaGlsZCgnbWFzay9yb290L3NlcnZlcl90eXBlX2l0ZW1zX25iZScpXG5cdFx0c2VsZWN0Tm9kZS5DaGlsZCgnYWxsJykuYWN0aXZlID0gZmFsc2Vcblx0fVxuXG5cdHB1YmxpYyBvblJlbW92ZSgpIHtcblx0fVxuXG5cdHB1YmxpYyBvbkNsZWFuKCkge1xuXHRcdGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuXHR9XG5cblx0Ly8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gYnV0dG9uIGxpc3RlbmVyIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblx0Ly9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cblx0Ly8gcGF0aDovL3Jvb3QvY29udGVudC9oaXN0b3J5X3N2L3ZpZXcvY29udGVudC9pdGVtL2RldGFpbF9iZVxuXHRvbkNsaWNrRGV0YWlsKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBfZGF0YTogc3RyaW5nKSB7XG5cdFx0Y29uc3QgZGF0YSA9IGV2ZW50LnRhcmdldC5wYXJlbnQuRGF0YVxuXHRcdGRhdGEgJiYgdmlld0hlbHBlci5zaG93UG5sKCdsb2JieS9HYW1lRGV0YWlsJywgZGF0YSlcblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvYm90dG9tL25leHRfbi9uZXh0X3BhZ2VfYmVAMFxuXHRvbkNsaWNrTmV4dFBhZ2UoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuXHRcdGNvbnN0IGFkZCA9IGRhdGEgPT09ICcwJyA/IC0xIDogMVxuXHRcdGNvbnN0IGN1clBhZ2UgPSB0aGlzLmN1clBhZ2UgfHwgMCwgbWF4UGFnZSA9IHRoaXMubWF4UGFnZVxuXHRcdGNvbnN0IGluZGV4ID0gdXQubG9vcFZhbHVlKGN1clBhZ2UgKyBhZGQsIG1heFBhZ2UpXG5cdFx0aWYgKGluZGV4ICE9PSBjdXJQYWdlKSB7XG5cdFx0XHR0aGlzLmN1clBhZ2UgPSBpbmRleFxuXHRcdFx0dGhpcy5zaG93R2FtZUhpc3RvcnlMaXN0KClcblx0XHR9XG5cdH1cblxuXHQvLyBwYXRoOi8vcm9vdC9jb250ZW50L2JvdHRvbS9zZXJ2ZXJfdHlwZV9zZWxlY3RfYmVfblxuXHRvbkNsaWNrU2VydmVyVHlwZVNlbGVjdChldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG5cdFx0YXVkaW9NZ3IucGxheVNGWCgnY2xpY2snKVxuXHRcdHZpZXdIZWxwZXIuY2hhbmdlUG9wdXBCb3hMaXN0KGV2ZW50LnRhcmdldCwgdHJ1ZSlcblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvYm90dG9tL3NlcnZlcl90eXBlX3NlbGVjdF9iZV9uL3NlbGVjdF9tYXNrX2JlXG5cdG9uQ2xpY2tTZWxlY3RNYXNrKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcblx0XHR2aWV3SGVscGVyLmNoYW5nZVBvcHVwQm94TGlzdChldmVudC50YXJnZXQucGFyZW50LCBmYWxzZSlcblx0fVxuXG5cdC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvYm90dG9tL3NlcnZlcl90eXBlX3NlbGVjdF9iZV9uL21hc2svcm9vdC9zZXJ2ZXJfdHlwZV9pdGVtc19uYmVcblx0b25DbGlja1NlcnZlclR5cGVJdGVtcyhldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgZGF0YTogc3RyaW5nKSB7XG5cdFx0Y29uc3Qgbm9kZSA9IHRoaXMuc2VydmVyVHlwZVNlbGVjdE5vZGVfXG5cdFx0dmlld0hlbHBlci5jaGFuZ2VQb3B1cEJveExpc3Qobm9kZSwgZmFsc2UpXG5cdFx0Y29uc3QgdHlwZSA9IGV2ZW50LnRhcmdldC5uYW1lXG5cdFx0aWYgKHR5cGUgIT09IHRoaXMuY3VyU2VsZWN0U2VydmVyVHlwZSkge1xuXHRcdFx0dGhpcy5zZWxlY3RTZXJ2ZXJUeXBlSXRlbShub2RlLCB0eXBlKVxuXHRcdH1cblx0fVxuXHQvL0BlbmRcblx0Ly8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuXHQvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG5cdC8vIOS4quS6uuaImOe7qVxuXHRwcml2YXRlIHNob3dHYW1lSGlzdG9yeUxpc3QoKSB7XG5cdFx0Y29uc3QgZW1wdHkgPSB0aGlzLmhpc3RvcnlTdl8ubm9kZS5DaGlsZCgnZW1wdHknKSwgdWlkID0gZ2FtZUhwci51c2VyLmdldFVpZCgpXG5cdFx0ZW1wdHkuYWN0aXZlID0gZmFsc2Vcblx0XHR0aGlzLnVwZGF0ZVBhZ2VCdXR0b24oKVxuXHRcdGxldCBzZXJ2ZXJUeXBlU3RyID0gdGhpcy5jdXJTZWxlY3RTZXJ2ZXJUeXBlXG5cdFx0Y29uc3Qgc2VydmVyVHlwZSA9IHNlcnZlclR5cGVTdHIgPT09ICdhbGwnID8gLTEgOiAoTnVtYmVyKHNlcnZlclR5cGVTdHIpIHx8IDApXG5cdFx0Y29uc3QgcGFnZSA9IHRoaXMuY3VyUGFnZSArIDFcblx0XHRnYW1lSHByLm5ldC5yZXF1ZXN0KCdjaGF0L0hEX0dldEdhbWVIaXN0b3J5TGlzdCcsIHsgcGFnZSwgc2VydmVyVHlwZSB9LCB0cnVlKS50aGVuKCh7IGVyciwgZGF0YSB9KSA9PiB7XG5cdFx0XHRjb25zdCBsaXN0OiBhbnlbXSA9IGRhdGE/LmRhdGFzIHx8IFtdLCBsZW4gPSBsaXN0Lmxlbmd0aFxuXHRcdFx0ZW1wdHkuYWN0aXZlID0gIWxlblxuXHRcdFx0dGhpcy5oaXN0b3J5U3ZfLnN0b3BBdXRvU2Nyb2xsKClcblx0XHRcdHRoaXMuaGlzdG9yeVN2Xy5jb250ZW50LnkgPSAwXG5cdFx0XHR0aGlzLmhpc3RvcnlTdl8uTGlzdChsZW4sIChpdCwgaSkgPT4ge1xuXHRcdFx0XHR2aWV3SGVscGVyLnVwZGF0ZUdhbWVIaXN0b3J5SXRlbShpdCwgbGlzdFtpXSwgdGhpcy5rZXkpXG5cdFx0XHRcdGl0LkNoaWxkKCdsaW5lJykuYWN0aXZlID0gaSA8IGxlbiAtIDFcblx0XHRcdH0pXG5cdFx0XHRpZiAoZGF0YSAmJiBkYXRhLnRvdGFsUGFnZSAhPT0gLTEgJiYgcGFnZSA9PT0gMSkge1xuXHRcdFx0XHR0aGlzLm1heFBhZ2UgPSBkYXRhLnRvdGFsUGFnZSA/PyAodGhpcy5tYXhQYWdlIHx8IDEpXG5cdFx0XHR9XG5cdFx0XHR0aGlzLnVwZGF0ZVBhZ2VCdXR0b24oKVxuXHRcdH0pXG5cdH1cblxuXHRwcml2YXRlIHVwZGF0ZVBhZ2VCdXR0b24oKSB7XG5cdFx0bGV0IGN1clBhZ2UgPSB0aGlzLmN1clBhZ2UgfHwgMCwgbWF4UGFnZSA9IHRoaXMubWF4UGFnZSB8fCAwXG5cdFx0Y3VyUGFnZSA9IE1hdGgubWluKGN1clBhZ2UgKyAxLCBtYXhQYWdlKVxuXHRcdHRoaXMubmV4dE5vZGVfLkNoaWxkKCd0ZXh0L3ZhbCcsIGNjLkxhYmVsKS5zdHJpbmcgPSBjdXJQYWdlICsgJy8nICsgbWF4UGFnZVxuXHRcdGNvbnN0IHByZSA9IHRoaXMubmV4dE5vZGVfLkNoaWxkKCduZXh0X3BhZ2VfYmVAMCcsIGNjLkJ1dHRvbiksXG5cdFx0XHRuZXh0ID0gdGhpcy5uZXh0Tm9kZV8uQ2hpbGQoJ25leHRfcGFnZV9iZUAxJywgY2MuQnV0dG9uKVxuXHRcdHByZS5Db21wb25lbnQoY2MuTXVsdGlGcmFtZSkuc2V0RnJhbWUoY3VyUGFnZSA+IDEpXG5cdFx0cHJlLmludGVyYWN0YWJsZSA9IGN1clBhZ2UgPiAxXG5cdFx0bmV4dC5Db21wb25lbnQoY2MuTXVsdGlGcmFtZSkuc2V0RnJhbWUoY3VyUGFnZSA8IG1heFBhZ2UpXG5cdFx0bmV4dC5pbnRlcmFjdGFibGUgPSBjdXJQYWdlIDwgbWF4UGFnZVxuXHR9XG5cblx0Ly8g6YCJ5oup5o6S5bqPXG5cdHByaXZhdGUgc2VsZWN0U2VydmVyVHlwZUl0ZW0obm9kZTogY2MuTm9kZSwgdHlwZTogc3RyaW5nLCBpbml0PzogYm9vbGVhbikge1xuXHRcdGNvbnN0IG9sZFR5cGUgPSB0aGlzLmN1clNlbGVjdFNlcnZlclR5cGVcblx0XHRub2RlLkRhdGEgPSB0aGlzLmN1clNlbGVjdFNlcnZlclR5cGUgPSB0eXBlXG5cdFx0bm9kZS5DaGlsZCgndmFsJywgY2MuTGFiZWwpLnNldExvY2FsZUtleSh0eXBlID09PSAnYWxsJyA/ICd1aS5iYXphYXJfZmlsdGVyX2FsbCcgOiAndWkudGl0bGVfc2VydmVyX25hbWVfJyArIHR5cGUpXG5cdFx0bm9kZS5DaGlsZCgnbWFzay9yb290L3NlcnZlcl90eXBlX2l0ZW1zX25iZScpLmNoaWxkcmVuLmZvckVhY2gobSA9PiB7XG5cdFx0XHRjb25zdCBzZWxlY3QgPSBtLm5hbWUgPT09IHR5cGVcblx0XHRcdG0uQ2hpbGQoJ3ZhbCcpLkNvbG9yKHNlbGVjdCA/ICcjQjZBNTkxJyA6ICcjNzU2OTYzJylcblx0XHRcdG0uQ2hpbGQoJ3NlbGVjdCcpLmFjdGl2ZSA9IHNlbGVjdFxuXHRcdH0pXG5cdFx0aWYgKGluaXQgfHwgb2xkVHlwZSA9PT0gdHlwZSkge1xuXHRcdFx0cmV0dXJuXG5cdFx0fVxuXHRcdHRoaXMuY3VyUGFnZSA9IDBcblx0XHR0aGlzLnNob3dHYW1lSGlzdG9yeUxpc3QoKVxuXHR9XG59XG4iXX0=