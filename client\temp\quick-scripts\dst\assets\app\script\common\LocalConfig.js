
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/LocalConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '44172Z78UpHRZz9vc7f+QD7', 'LocalConfig');
// app/script/common/LocalConfig.ts

"use strict";
/**
 * 本地配置文件（该文件是用来忽略各个开发者本地不一样的配置）
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.localConfig = void 0;
var LocalConfig = /** @class */ (function () {
    function LocalConfig() {
        this.RELEASE = false; //是否开发模式
        this.openGuide = true; //是否开启新手引导
        this.openLog = false; //打印日志
        this.openPrint = false; //是否打开打印
        this.servers = {
            china: {
                host: 'nine.twomiles.cn',
                port: 3653,
                useSSL: true
            },
            hk: {
                host: 'nine-hk.twomiles.cn',
                port: 3653,
                useSSL: true
            },
            us: {
                host: 'nine-us.twomiles.cn',
                port: 3653,
                useSSL: true
            }
        };
        this.server_test = {
            // host: '127.0.0.1', //本地
            // port: 4653,
            host: 'nine-test.twomiles.cn',
            port: 4653,
            useSSL: true
            // host: 'nine-hk.twomiles.cn', //香港
            // port: 3653,
            // useSSL: true
            // host: 'nine-us.twomiles.cn', //硅谷
            // port: 3653,
            // useSSL: true
            // host: 'nine.twomiles.cn', //国内
            // port: 3653,
            // useSSL: true,
            // host: 'nine-dxf.twomiles.cn', //测试服
            // port: 4653,
            // useSSL: true,
            // host: 'nine-dxf.twomiles.cn', //提审服
            // port: 3653,
            // useSSL: true
        };
        this.httpServerUrl = {
            test: 'https://nine-test.twomiles.cn:8181',
            china: 'https://nine.twomiles.cn:8080',
            hk: 'https://nine-hk.twomiles.cn:8080'
        };
        this.config = {
            china: {
                checkUpdateUrl: this.httpServerUrl.china + '/getHotUpdateInfo',
                maintainTimeUrl: this.httpServerUrl.china + '/getUpdateMaintainTime',
                hotUpdateStartUrl: this.httpServerUrl.china + '/hotUpdateStart',
                hotUpdateEndUrl: this.httpServerUrl.china + '/hotUpdateEnd',
                packageUrl: 'https://jwm-inland-file.twomiles.cn/hotUpdate/file/',
                manifestUrl: 'https://jwm-inland-file.twomiles.cn/hotUpdate/',
                downloadUrl: {
                    ios: 'https://www.baidu.com/',
                    google: 'https://www.baidu.com/',
                },
            },
            hk: {
                checkUpdateUrl: 'https://nine-test.twomiles.cn:8181/getHotUpdateInfo',
                // checkUpdateUrl: this.httpServerUrl.hk + '/getHotUpdateInfo',
                maintainTimeUrl: this.httpServerUrl.hk + '/getUpdateMaintainTime',
                hotUpdateStartUrl: this.httpServerUrl.hk + '/hotUpdateStart',
                hotUpdateEndUrl: this.httpServerUrl.hk + '/hotUpdateEnd',
                packageUrl: 'https://jwm-global-file.twomiles.cn/hotUpdate/file/',
                manifestUrl: 'https://jwm-global-file.twomiles.cn/hotUpdate/',
                downloadUrl: {
                    ios: 'https://apps.apple.com/app/id6446716937',
                    google: 'https://play.google.com/store/apps/details?id=twgame.global.acers',
                },
            }
        };
        this.errorReportUrl = {
            test: 'http://127.0.0.1:8181/errorReport',
            china: this.httpServerUrl.china + '/errorReport',
            hk: this.httpServerUrl.hk + '/errorReport',
        };
        this.getServerInfoUrl = {
            // test: 'http://127.0.0.1:8181/getServerInfo',
            test: 'https://nine-test.twomiles.cn:8181/getServerInfo',
            // test: this.httpServerUrl.hk + '/getServerInfo',
            china: this.httpServerUrl.china + '/getServerInfo',
            hk: this.httpServerUrl.hk + '/getServerInfo',
        };
        this.openGuide = this.RELEASE ? true : this.openGuide;
        this.server_test = this.RELEASE ? { host: 'nine-test.twomiles.cn', port: 4653, useSSL: true } : this.server_test;
        this.httpServerUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181' : this.httpServerUrl.test;
        this.errorReportUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181/errorReport' : this.errorReportUrl.test;
        this.getServerInfoUrl.test = this.RELEASE ? 'https://nine-test.twomiles.cn:8181/getServerInfo' : this.getServerInfoUrl.test;
    }
    return LocalConfig;
}());
exports.localConfig = new LocalConfig();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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