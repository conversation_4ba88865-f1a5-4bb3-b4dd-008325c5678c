
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/ForgeEquipInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '116f5e/OGNEqLadOICuaSPT', 'ForgeEquipInfo');
// app/script/model/main/ForgeEquipInfo.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
// 打造信息
var ForgeEquipInfo = /** @class */ (function () {
    function ForgeEquipInfo() {
        this.uid = '';
        this.id = 0;
        this.needTime = 0;
        this.surplusTime = 0;
        this.getTime = 0;
        this.isYetForge = false; //是否已经打造了
    }
    ForgeEquipInfo.prototype.fromSvr = function (data) {
        this.uid = data.uid;
        this.id = Number(this.uid.split('_')[0]);
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        return this;
    };
    Object.defineProperty(ForgeEquipInfo.prototype, "name", {
        get: function () { return 'equipText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    // 获取实际的剩余时间
    ForgeEquipInfo.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    ForgeEquipInfo.prototype.getSmeltCount = function () {
        var _a;
        return (_a = GameHelper_1.gameHpr.player.getEquipByUid(this.uid)) === null || _a === void 0 ? void 0 : _a.getSmeltCount();
    };
    return ForgeEquipInfo;
}());
exports.default = ForgeEquipInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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