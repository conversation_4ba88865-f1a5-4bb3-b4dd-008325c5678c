
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaWatchListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '50d21sB1XtJbqZ5Ryr+xu4h', 'AreaWatchListPnlCtrl');
// app/script/view/area/AreaWatchListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AreaWatchListPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaWatchListPnlCtrl, _super);
    function AreaWatchListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.index = 0;
        return _this;
    }
    AreaWatchListPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AreaWatchListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AreaWatchListPnlCtrl.prototype.onEnter = function (index) {
        var _this = this;
        this.index = index;
        var list = [], now = GameHelper_1.gameHpr.getServerNowTime();
        GameHelper_1.gameHpr.areaCenter.getAreaWatchPlayersByIndex(index).forEach(function (m) {
            var info = GameHelper_1.gameHpr.getPlayerInfo(m.uid);
            if (info) {
                list.push({ info: info, time: Math.max(1000, now - m.time) });
            }
        });
        list = list.slice(0, 50).reverse();
        var len = list.length;
        this.titleLbl_.setLocaleKey('ui.title_area_watch_list', len);
        this.listSv_.List(len, function (it, i) {
            var _a = list[i], info = _a.info, time = _a.time;
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head'), info.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(info.nickname, 9);
            it.Child('time').setLocaleKey('ui.yet_watch_time', GameHelper_1.gameHpr.millisecondToString(time));
            var alliNode = it.Child('alli');
            alliNode.Color(info.allianceIcon ? '#FFFFFF' : '#EAE1CC');
            ResHelper_1.resHelper.loadAlliIcon(info.allianceIcon, alliNode, _this.key);
        });
    };
    AreaWatchListPnlCtrl.prototype.onRemove = function () {
    };
    AreaWatchListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/emoji_be
    AreaWatchListPnlCtrl.prototype.onClickEmoji = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('area/AreaSelectEmoji', function (id) {
            if (_this.isValid && id) {
                _this.sendEmoji(id);
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AreaWatchListPnlCtrl.prototype.sendEmoji = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_AreaSendChat', { index: this.index, emoji: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWatchListPnlCtrl = __decorate([
        ccclass
    ], AreaWatchListPnlCtrl);
    return AreaWatchListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaWatchListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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