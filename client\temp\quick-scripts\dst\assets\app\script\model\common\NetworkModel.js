
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/NetworkModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9dc63SQz8dBXpHTJ+rd5wK6', 'NetworkModel');
// app/script/model/common/NetworkModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var NetworkModel = /** @class */ (function (_super) {
    __extends(NetworkModel, _super);
    function NetworkModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.client = null; //Socket
        _this.reqId = 0; //请求id
        _this.reqMap = new Map(); //请求列表
        _this.events = []; //当前事件列表
        _this.isEventGame = false;
        _this.kick = false; //是否被踢
        return _this;
    }
    NetworkModel.prototype.onCreate = function () {
        if (!this.isEventGame) {
            this.isEventGame = true;
            cc.game.on(cc.game.EVENT_HIDE, this.onGameHide, this);
            if (typeof wx !== 'undefined') {
                wx.onShow(this.onGameShow.bind(this));
            }
            else {
                cc.game.on(cc.game.EVENT_SHOW, this.onGameShow, this);
            }
        }
    };
    NetworkModel.prototype.onGameShow = function (options) {
        this.emit(EventType_1.default.EVENT_GAME_SHOW, options);
    };
    NetworkModel.prototype.onGameHide = function () {
        this.emit(EventType_1.default.EVENT_GAME_HIDE);
    };
    NetworkModel.prototype.clean = function () {
        this.offAll();
        this.reset();
    };
    NetworkModel.prototype.setKick = function (val) {
        if (val === void 0) { val = true; }
        this.kick = val;
        this.offAll();
    };
    NetworkModel.prototype.reset = function () {
        this.kick = false;
        this.reqId = 0;
        this.cleanReqMap();
    };
    NetworkModel.prototype.cleanReqMap = function () {
        var _this = this;
        this.reqMap.forEach(function (m) {
            _this.showNetReqEnd(m.wait);
            m.cb && m.cb({ err: GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_error') });
        });
        this.reqMap.clear();
    };
    NetworkModel.prototype.doconnect = function (prop) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var clientId = 'mqttjs_' + Math.random().toString(16).substring(2, 10);
                        _this.client = new Paho.MQTT.Client(prop.host, prop.port, '/mqtt', clientId);
                        _this.client.connect({
                            onSuccess: function (evt) {
                                console.log('mqant connect success!');
                                _this.reset();
                                resolve(true);
                            },
                            onFailure: function (evt) {
                                console.log('mqant connect failure!');
                                // wxHelper.errorAndFilter('connect fail', evt?.errorCode, evt?.errorMessage)
                                resolve(false);
                            },
                            mqttVersion: 3,
                            useSSL: !!prop.useSSL,
                            cleanSession: true,
                            keepAliveInterval: 30,
                        });
                    })];
            });
        });
    };
    // 连接网络
    NetworkModel.prototype.connect = function (prop) {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log('mqant connect:', prop.host + ':' + prop.port);
                        this.close();
                        return [4 /*yield*/, this.doconnect(prop)];
                    case 1:
                        ok = _a.sent();
                        if (ok) {
                            // 注册连接断开处理事件
                            this.client.onConnectionLost = function (evt) {
                                if (evt.errorCode === 0 || evt.errorCode === 5) {
                                    return;
                                }
                                _this.cleanReqMap();
                                _this.client = null;
                                _this.emit('disconnect', evt);
                                console.log('mqant disconnect.');
                            };
                            // 注册消息接收处理事件
                            this.client.onMessageArrived = this.recvMessage.bind(this);
                            // 打印ping
                            // this.client._setTrace(({ severity, message }) => {
                            //     if (message.includes('Pinger.doPing')) {
                            //         cc.log(new Date(), message)
                            //     }
                            // })
                        }
                        return [2 /*return*/, ok];
                }
            });
        });
    };
    // 关闭网络
    NetworkModel.prototype.close = function () {
        if (this.client) {
            this.offAll();
            if (this.isConnected()) {
                this.client.disconnect();
            }
            this.client = null;
            console.log('mqant close.');
        }
    };
    NetworkModel.prototype.isConnected = function () {
        var _a;
        return !!((_a = this.client) === null || _a === void 0 ? void 0 : _a.isConnected());
    };
    // 主动ping
    NetworkModel.prototype.ping = function () {
        var _a;
        (_a = this.client) === null || _a === void 0 ? void 0 : _a.ping();
    };
    // 设置心跳
    NetworkModel.prototype.setKeepAliveInterval = function (val) {
        var _a;
        (_a = this.client) === null || _a === void 0 ? void 0 : _a.setKeepAliveInterval(val);
    };
    NetworkModel.prototype.showNetReqBegin = function (show) {
        if (show) {
            this.emit(NetEvent_1.default.NET_REQ_BEGIN);
        }
    };
    NetworkModel.prototype.showNetReqEnd = function (show) {
        if (show) {
            this.emit(NetEvent_1.default.NET_REQ_END);
        }
    };
    // 发送
    NetworkModel.prototype.send = function (route, msg, cb, wait) {
        if (!route || !this.isConnected()) {
            return this.emit(NetEvent_1.default.NET_DISCONNECT);
        }
        else if (arguments.length === 2 && typeof msg === 'function') {
            cb = msg;
            msg = {};
        }
        else {
            msg = msg || {};
        }
        var msgName = route.replace('/', '_').toUpperCase();
        var cls = proto[msgName + '_C2S'];
        if (!cls) {
            return;
        }
        else if (cb) {
            this.reqId += 1;
            this.reqMap.set(this.reqId, { cb: cb, wait: !!wait, msgName: msgName });
            route = route + '/' + this.reqId;
        }
        this.showNetReqBegin(!!wait);
        this.client.send(route, cls.encode(msg).finish(), 1);
    };
    NetworkModel.prototype.request = function (route, msg, wait) {
        return __awaiter(this, void 0, void 0, function () {
            var msgName, cls;
            var _this = this;
            return __generator(this, function (_a) {
                if (!route || !this.isConnected()) {
                    this.emit(NetEvent_1.default.NET_DISCONNECT);
                    return [2 /*return*/, { err: GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_error') }];
                }
                else if (typeof (msg) === 'boolean') {
                    wait = msg;
                    msg = {};
                }
                else if (!msg) {
                    msg = {};
                }
                msgName = route.replace('/', '_').toUpperCase();
                cls = proto[msgName + '_C2S'];
                if (!cls) {
                    cc.error('route error, msgName=' + msgName + '_C2S');
                    return [2 /*return*/, { err: 'toast.route_error' }];
                }
                return [2 /*return*/, new Promise(function (resolve) {
                        _this.reqId += 1;
                        _this.reqMap.set(_this.reqId, { cb: resolve, wait: !!wait, msgName: msgName });
                        route = route + '/' + _this.reqId;
                        _this.showNetReqBegin(!!wait);
                        _this.client.send(route, cls.encode(msg).finish(), 1);
                    })];
            });
        });
    };
    NetworkModel.prototype.recvMessage = function (evt) {
        var _a;
        var _b = __read(evt.destinationName.split('/'), 3), moduleType = _b[0], func = _b[1], msgid = _b[2];
        // cc.log(moduleType, func, evt.payloadBytes.length + 'B')
        if (msgid) {
            var id = parseInt(msgid);
            var req = this.reqMap.get(id);
            if (req) {
                this.reqMap.delete(id);
                this.showNetReqEnd(req.wait);
                var body = proto.S2C_RESULT.decode(evt.payloadBytes);
                var err = body.error;
                if (!err) {
                }
                else if (err.startsWith('Service(type') && err.endsWith('not found')) {
                    err = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_error');
                }
                else if (err === 'deadline exceeded') {
                    err = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_timeout');
                }
                else if (err.includes('runtime error')) {
                    err = 'login.net_runtime_error';
                }
                try {
                    var data = (_a = proto[req.msgName + '_S2C']) === null || _a === void 0 ? void 0 : _a.decode(body.data);
                    data = data === null || data === void 0 ? void 0 : data.toJSON();
                    req.cb && req.cb({ err: err, data: data });
                }
                catch (error) {
                    ErrorReportHelper_1.errorReportHelper.reportError('recvMessage Parse Error', { route: req.msgName, error: error });
                    req.cb && req.cb({ err: 'login.net_parse_error', data: null });
                }
            }
        }
        else if (moduleType && func) {
            var msgName = moduleType.toUpperCase() + '_' + func.toUpperCase() + '_NOTIFY';
            var cls = proto[msgName];
            if (cls) {
                try {
                    var data = cls.decode(evt.payloadBytes);
                    data = data.toJSON();
                    this.emit(moduleType + '/' + func, data);
                }
                catch (error) {
                    ErrorReportHelper_1.errorReportHelper.reportError('recvMessage NOTIFY Error', { route: msgName, error: error });
                }
            }
            else {
                cc.error('error NOTIFY route:', msgName);
                ErrorReportHelper_1.errorReportHelper.reportError('recvMessage NOTIFY Not route', { route: msgName });
            }
        }
        else {
            cc.error('recvMessage error msg:', evt.destinationName);
            ErrorReportHelper_1.errorReportHelper.reportError('recvMessage error', { destinationName: evt.destinationName });
        }
    };
    // 监听
    NetworkModel.prototype.on = function (route, cb, target) {
        eventCenter.off(route, cb, target);
        eventCenter.on(route, cb, target);
        this.events.push({ route: route, cb: cb, target: target });
    };
    // 注销监听
    NetworkModel.prototype.off = function (route, cb, target) {
        eventCenter.off(route, cb, target);
        this.events.delete(function (m) { return m.route === route && m.cb.toString() == m.cb.toString() && m.target == m.target; });
    };
    NetworkModel.prototype.offAll = function () {
        this.events.forEach(function (m) { return eventCenter.off(m.route, m.cb, m.target); });
        this.events.length = 0;
    };
    NetworkModel.prototype.isKick = function () {
        return this.kick;
    };
    // http请求
    NetworkModel.prototype.post = function (opts) {
        return __awaiter(this, void 0, void 0, function () {
            var cnt, max, res, ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        cnt = 0, max = (opts === null || opts === void 0 ? void 0 : opts.retryCount) || 5;
                        this.showNetReqBegin(opts.wait);
                        _a.label = 1;
                    case 1:
                        if (!true) return [3 /*break*/, 9];
                        return [4 /*yield*/, ut.httpRequest('POST', opts.url, opts.data || {})];
                    case 2:
                        res = _a.sent();
                        if (!((res === null || res === void 0 ? void 0 : res.status) === 0 || (res === null || res === void 0 ? void 0 : res.status) === 200)) return [3 /*break*/, 3];
                        this.showNetReqEnd(opts.wait);
                        return [2 /*return*/, res.data];
                    case 3:
                        if (!(cnt < max)) return [3 /*break*/, 5];
                        cnt += 1;
                        return [4 /*yield*/, ut.wait(2)];
                    case 4:
                        _a.sent();
                        return [3 /*break*/, 8];
                    case 5:
                        if (!opts.showConnectFail) return [3 /*break*/, 7];
                        cnt = 0;
                        max += 2;
                        this.showNetReqEnd(opts.wait);
                        return [4 /*yield*/, ViewHelper_1.viewHelper.showConnectFail()];
                    case 6:
                        ok = _a.sent();
                        if (!ok) {
                            return [2 /*return*/, null];
                        }
                        this.showNetReqBegin(opts.wait);
                        return [3 /*break*/, 8];
                    case 7:
                        this.showNetReqEnd(opts.wait);
                        return [2 /*return*/, null];
                    case 8: return [3 /*break*/, 1];
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    NetworkModel = __decorate([
        mc.addmodel('net')
    ], NetworkModel);
    return NetworkModel;
}(mc.BaseModel));
exports.default = NetworkModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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