
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/WorldModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6a45chXzVpHk6V7j7hMhQ6W', 'WorldModel');
// app/script/model/main/WorldModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var MapUionFindHelper_1 = require("../../common/helper/MapUionFindHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var RandomObj_1 = require("../common/RandomObj");
var AncientObj_1 = require("./AncientObj");
var AvoidWarObj_1 = require("./AvoidWarObj");
var BTCityObj_1 = require("./BTCityObj");
var MapCellObj_1 = require("./MapCellObj");
var MarchObj_1 = require("./MarchObj");
var SeasonInfo_1 = require("./SeasonInfo");
var TondenObj_1 = require("./TondenObj");
var TransitObj_1 = require("./TransitObj");
/**
 * 世界（地图）
 */
var WorldModel = /** @class */ (function (_super) {
    __extends(WorldModel, _super);
    function WorldModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.net = null;
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.runTime = 0; //服务器运行时间
        _this.initTime = 0; //初始化时间
        _this.winCondType = 0;
        _this.winCondValue = 0; //胜利条件对应值
        _this.winCondMaxDay = 0; //最大天数
        _this.winCondCloseDay = 0; //关闭服务器时间
        _this.season = null; //季节信息
        _this.captureNum = 0; //当前已沦陷人数
        _this.gameOverInfo = null; //游戏结束信息
        _this.mapCells = []; //地图所有单元格
        _this.gameConfInfo = {}; //游戏配置信息
        _this.worldEventMap = {}; //世界事件
        _this.citySkinMap = {}; //城市皮肤列表
        _this.marchs = []; //行军列表
        _this.transits = []; //运送列表
        _this.battleDistMap = {}; //战斗分布信息
        _this.avoidWarDistMap = {}; //当前免战分布信息
        _this.btCityQueueMap = {}; //当前修建城市信息
        _this.alliBaseInfoMap = {}; //所有联盟基础信息
        _this.ancientMap = {}; //遗迹信息
        _this.tondenQueueMap = {}; //当前屯田信息
        _this.allPlayerInfos = {}; //所有玩家的信息
        _this.yetPlayNewCellMap = {}; //已经观看过的地块
        _this.notPlayNewCells = []; //还没有观看过的地块
        _this.notPlayCellTondenMap = {}; //还没有观看过屯田结束
        _this.preWinSize = cc.v2();
        _this.maxTileRange = cc.v2(10, 10); //当前地图显示格子数量范围 半径
        _this.maxMarchLength = 0; //当前地图最大可显示的行军线长度
        _this.cameraInfo = { zoomRatio: -1, position: cc.v2() }; //相机信息
        _this.centre = cc.v2(); //当前地图的中心位置
        _this.lookCell = null; //当前查看的地块
        _this.lastReqAlliancesTime = 0; //最后一次请求联盟列表时间
        _this.tempAlliances = []; //联盟列表
        _this.tempWorldUpdateList = [];
        _this.tempMainCityCells = []; //临时的主城地块用于更新关联使用t
        _this.tempHasUpdateCellInfo = false;
        _this.tempHasUpdateCellInfoByMe = false;
        _this.tempCityOutputMap = {}; //城市产出 这里只记录产出什么类型
        _this.mainCityInCameraRange = false; //主城是否在相机范围内
        // private localLandData: { sid?: number, lands?: number[] } = {} //本地地块数据
        _this.tempMudIconMap = {}; //主城周围土地装饰icon
        _this.tempCityMudMap = {}; //城市周围土地下标
        _this.random = null;
        _this.islandRoundMap = {}; //岛屿周围区域，600*600格子以外的区域
        _this.decorationsMap = {}; //地块装饰信息
        return _this;
    }
    WorldModel.prototype.onCreate = function () {
        this.net = this.getModel('net');
        this.user = this.getModel('user');
        this.player = this.getModel('player');
        this.areaCenter = this.getModel('areaCenter');
        // this.localLandData = storageMgr.loadJson(gameHpr.getAppType() + '_land_data') || {}
    };
    WorldModel.prototype.clean = function () {
        this.cameraInfo.zoomRatio = -1;
        this.lookCell = null;
        this.allPlayerInfos = {};
        this.tempAlliances.length = 0;
        this.tempMainCityCells = [];
        this.mapCells = [];
        this.tempWorldUpdateList = [];
        this.net.off('game/OnUpdateWorldInfo', this.OnUpdateWorldInfo, this);
    };
    WorldModel.prototype.getSceneKey = function () { return 'main'; };
    WorldModel.prototype.init = function (sid, data, progessCallback) {
        var _a, _b, _c, _d, _e, _f, _g;
        return __awaiter(this, void 0, void 0, function () {
            var k, player, cells, cityByteIdMap, _loop_1, owner, mapUrl, mapJson, lands, citys, i, l, landId, cell, info, k, spectateIndex, mainCityIndex, myCells, mainIndex;
            var _this = this;
            return __generator(this, function (_h) {
                switch (_h.label) {
                    case 0:
                        cc.log('initWorld', sid, data);
                        this.runTime = data.runTime || 0;
                        this.initTime = Date.now();
                        this.winCondType = ((_a = data.winCond) === null || _a === void 0 ? void 0 : _a[0]) || 1;
                        this.winCondValue = ((_b = data.winCond) === null || _b === void 0 ? void 0 : _b[1]) || 30000;
                        this.winCondMaxDay = ((_c = data.winCond) === null || _c === void 0 ? void 0 : _c[2]) || -1;
                        this.winCondCloseDay = ((_d = data.winCond) === null || _d === void 0 ? void 0 : _d[3]) || 3;
                        this.season = new SeasonInfo_1.default().init(data.season);
                        this.setGameOverInfo(data.gameOver);
                        this.captureNum = data.captureNum || 0;
                        this.lastReqAlliancesTime = 0;
                        this.tempWorldUpdateList = [];
                        this.tempCityOutputMap = {};
                        // 玩家信息
                        this.allPlayerInfos = {};
                        for (k in data.players) {
                            player = this.allPlayerInfos[k] = GameHelper_1.gameHpr.getEmptyPlayerInfo(k, 0);
                            GameHelper_1.gameHpr.initPlayerInfo(player, data.players[k]);
                        }
                        cells = {}, cityByteIdMap = {};
                        assetsMgr.getJson('city').datas.forEach(function (m) { return cityByteIdMap[m.byte_id] = m.id; });
                        _loop_1 = function (owner) {
                            var cellsInfo = data.cells[owner];
                            var _a = ProtoHelper_1.protoHelper.uncompressPlayerCellsBytes(cellsInfo), playerCells = _a.playerCells, citiesMap = _a.citiesMap;
                            playerCells.forEach(function (m) {
                                var cityByteId = citiesMap[m] || 0;
                                cells[m] = { index: m, owner: owner, cityId: cityByteIdMap[cityByteId] || 0 };
                            });
                        };
                        for (owner in data.cells) {
                            _loop_1(owner);
                        }
                        mapUrl = 'maps/maps_' + (data.mapId || 1);
                        return [4 /*yield*/, assetsMgr.loadOnceRes(mapUrl, cc.JsonAsset)
                            //加载地图装饰
                        ];
                    case 1:
                        mapJson = _h.sent();
                        //加载地图装饰
                        return [4 /*yield*/, this.loadDecorationsJson(data.mapId)];
                    case 2:
                        //加载地图装饰
                        _h.sent();
                        return [4 /*yield*/, this.loadRoundJson(data.mapId)];
                    case 3:
                        _h.sent();
                        this.random = new RandomObj_1.default();
                        lands = mapJson.json;
                        // cc.log(mapJson, lands)
                        // 地块信息
                        this.mapCells = [];
                        this.ancientMap = {};
                        this.tempCityMudMap = {};
                        this.tempMudIconMap = {};
                        citys = [];
                        // console.time('1111')
                        for (i = 0, l = lands.length; i < l; i++) {
                            landId = lands[i];
                            cell = new MapCellObj_1.default().init(i, landId);
                            info = cells[i] || {};
                            this.updateMapCellInfo(cell, info);
                            this.mapCells.push(cell);
                            if (cell.cityId === Constant_1.CITY_MAIN_NID) { //是否主城
                                citys.push(cell);
                            }
                            if (cell.isAncient()) { //是否遗迹
                                this.ancientMap[cell.index] = new AncientObj_1.default().init(cell);
                            }
                            if (cell.owner) { //记录玩家的拥有的
                                this.addPlayerInfoByCell(info, cell, true);
                            }
                        }
                        // 如果有的话 刷新一下
                        if (this.lookCell) {
                            this.lookCell = this.mapCells[this.lookCell.index];
                        }
                        // 所有城市建筑
                        citys.forEach(function (m) {
                            var _a;
                            // 地块关联
                            _this.updateMainCityRangeCell(m);
                            // 刷新玩家的地块边界线
                            MapHelper_1.mapHelper.updatePlayerCellBorderLines((_a = _this.getPlayerInfo(m.owner)) === null || _a === void 0 ? void 0 : _a.cells);
                        });
                        for (k in this.ancientMap) {
                            this.updateMainCityRangeCell(this.ancientMap[k].cell);
                        }
                        spectateIndex = (_e = this.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _e === void 0 ? void 0 : _e.spectateIndex;
                        if (!spectateIndex) {
                            this.player.updateFixationMenuData();
                        }
                        mainCityIndex = this.player.getMainCityIndex() || spectateIndex || 180300;
                        this.player.updateMainCityRect(mainCityIndex);
                        // 初始化地图中心位置
                        this.centre.set(MapHelper_1.mapHelper.indexToPoint(mainCityIndex));
                        myCells = (_f = this.getPlayerInfo(this.user.getUid())) === null || _f === void 0 ? void 0 : _f.cells;
                        this.notPlayNewCells.length = 0;
                        mainIndex = (_g = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CITY_INDEX)) !== null && _g !== void 0 ? _g : -1;
                        if (mainIndex !== mainCityIndex) {
                            this.yetPlayNewCellMap = {};
                            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CITY_INDEX, mainCityIndex);
                        }
                        else {
                            this.yetPlayNewCellMap = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.YET_PLAY_NEW_CELL) || {};
                        }
                        if (ut.isEmptyObject(this.yetPlayNewCellMap)) {
                            myCells === null || myCells === void 0 ? void 0 : myCells.forEach(function (m, key) { return _this.yetPlayNewCellMap[key] = true; });
                            this.saveYetPlayNewCellMap();
                        }
                        else {
                            myCells === null || myCells === void 0 ? void 0 : myCells.forEach(function (m, key) {
                                var _a;
                                if (!_this.yetPlayNewCellMap[key] && ((_a = m.city) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.CITY_MAIN_NID) {
                                    _this.notPlayNewCells.push(Number(key));
                                }
                            });
                        }
                        EventReportHelper_1.eventReportHelper.checkReportOweCellCount();
                        EventReportHelper_1.eventReportHelper.checkReportOccupyCell();
                        // 监听消息
                        this.areaCenter.initNetEvent();
                        this.net.on('game/OnUpdateWorldInfo', this.OnUpdateWorldInfo, this);
                        // 初始化聊天
                        GameHelper_1.gameHpr.chat.init();
                        if (!progessCallback) return [3 /*break*/, 16];
                        return [4 /*yield*/, this.initGameInfo()];
                    case 4:
                        _h.sent();
                        progessCallback(0.01);
                        return [4 /*yield*/, this.initWorldEvent()];
                    case 5:
                        _h.sent();
                        progessCallback(0.05);
                        return [4 /*yield*/, this.initCitySkins()];
                    case 6:
                        _h.sent();
                        progessCallback(0.1);
                        return [4 /*yield*/, this.initMarchs()];
                    case 7:
                        _h.sent();
                        progessCallback(0.2);
                        return [4 /*yield*/, this.initTransits()];
                    case 8:
                        _h.sent();
                        progessCallback(0.3);
                        return [4 /*yield*/, this.initBattleDist()];
                    case 9:
                        _h.sent();
                        progessCallback(0.45);
                        return [4 /*yield*/, this.initAvoidWarDist()];
                    case 10:
                        _h.sent();
                        progessCallback(0.5);
                        return [4 /*yield*/, this.initBTCityQueues()];
                    case 11:
                        _h.sent();
                        progessCallback(0.6);
                        return [4 /*yield*/, this.initTondenQueues()];
                    case 12:
                        _h.sent();
                        progessCallback(0.7);
                        return [4 /*yield*/, this.initAncientInfos()];
                    case 13:
                        _h.sent();
                        progessCallback(0.8);
                        return [4 /*yield*/, this.initAlliBaseInfo()];
                    case 14:
                        _h.sent();
                        progessCallback(0.9);
                        return [4 /*yield*/, ResHelper_1.resHelper.initLandSkin(this.season.type)];
                    case 15:
                        _h.sent();
                        progessCallback(1);
                        return [3 /*break*/, 18];
                    case 16: return [4 /*yield*/, Promise.all([
                            this.initGameInfo(),
                            this.initWorldEvent(),
                            this.initCitySkins(),
                            this.initMarchs(),
                            this.initTransits(),
                            this.initBattleDist(),
                            this.initAvoidWarDist(),
                            this.initBTCityQueues(),
                            this.initTondenQueues(),
                            this.initAncientInfos(),
                            this.initAlliBaseInfo(),
                            ResHelper_1.resHelper.initLandSkin(this.season.type),
                        ])];
                    case 17:
                        _h.sent();
                        _h.label = 18;
                    case 18:
                        // console.timeEnd('4444')
                        assetsMgr.releaseOnceRes(mapUrl, cc.JsonAsset);
                        MapUionFindHelper_1.mapUionFindHelper.init();
                        return [2 /*return*/];
                }
            });
        });
    };
    //加载地图装饰UI配置
    WorldModel.prototype.loadDecorationsJson = function (mapId) {
        return __awaiter(this, void 0, void 0, function () {
            var itemsUrl, itemsJson, i, item, index, list, info;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.decorationsMap = {};
                        itemsUrl = 'decorations/decorations_' + mapId;
                        return [4 /*yield*/, assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)];
                    case 1:
                        itemsJson = _a.sent();
                        if (itemsJson) {
                            for (i = 0; i < itemsJson.json.length; i++) {
                                item = itemsJson.json[i];
                                index = item[0];
                                list = this.decorationsMap[index];
                                if (!list) {
                                    list = this.decorationsMap[index] = [];
                                }
                                info = { decorationJson: assetsMgr.getJsonData('decoration', item[1]), decorationActIndex: item[2] || index };
                                list.push(info);
                            }
                        }
                        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset);
                        return [2 /*return*/];
                }
            });
        });
    };
    //加载地图岛屿周围的接壤部分
    WorldModel.prototype.loadRoundJson = function (mapId) {
        return __awaiter(this, void 0, void 0, function () {
            var itemsUrl, itemsJson, key, item;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.islandRoundMap = {};
                        itemsUrl = 'rounds/rounds_' + mapId;
                        return [4 /*yield*/, assetsMgr.loadOnceRes(itemsUrl, cc.JsonAsset)];
                    case 1:
                        itemsJson = _a.sent();
                        if (itemsJson) {
                            for (key in itemsJson.json) {
                                item = itemsJson.json[key];
                                this.islandRoundMap[key] = item;
                            }
                        }
                        assetsMgr.releaseOnceRes(itemsUrl, cc.JsonAsset);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化游戏信息
    WorldModel.prototype.initGameInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, exclusiveMap, exclusiveMapData, key, pawnCostMap, pawnSlots, k;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetWorldRandomInfo')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        exclusiveMap = {};
                        exclusiveMapData = (data === null || data === void 0 ? void 0 : data.exclusiveMap) || {};
                        for (key in exclusiveMapData) {
                            exclusiveMap[key] = exclusiveMapData[key].arr;
                        }
                        pawnCostMap = (data === null || data === void 0 ? void 0 : data.pawnCostMap) || {};
                        this.gameConfInfo = { exclusiveMap: exclusiveMap, pawnCostMap: pawnCostMap };
                        pawnSlots = this.player.getPawnSlots();
                        for (k in pawnSlots) {
                            pawnSlots[k].updateCost();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化世界事件
    WorldModel.prototype.initWorldEvent = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetWorldEvent')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.worldEventMap = data.eventMap || {};
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化城市皮肤
    WorldModel.prototype.initCitySkins = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetCitySkins')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.citySkinMap = (data === null || data === void 0 ? void 0 : data.citySkins) || {};
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化行军路线
    WorldModel.prototype.initMarchs = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, list;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetMarchs')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.marchs = list.map(function (m) { return new MarchObj_1.default().init(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化运送路线
    WorldModel.prototype.initTransits = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, list;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetTransits')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [];
                        this.transits = list.map(function (m) { return new TransitObj_1.default().init(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化战斗分布信息
    WorldModel.prototype.initBattleDist = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetBattleDist')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.battleDistMap = (data === null || data === void 0 ? void 0 : data.battleDistMap) || {};
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化免战分布信息
    WorldModel.prototype.initAvoidWarDist = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, obj1, obj2, key, key;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetAvoidWarDist')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        obj1 = (data === null || data === void 0 ? void 0 : data.avoidWarAreas) || {};
                        obj2 = (data === null || data === void 0 ? void 0 : data.avoidWarAreas2) || {};
                        this.avoidWarDistMap = {};
                        for (key in obj1) {
                            this.avoidWarDistMap[key] = new AvoidWarObj_1.default().init(key, obj1[key], 0);
                        }
                        for (key in obj2) {
                            this.avoidWarDistMap[key] = new AvoidWarObj_1.default().init(key, obj2[key], 1);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化修建城市队列
    WorldModel.prototype.initBTCityQueues = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, arr;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetBTCityQueues')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        arr = (data === null || data === void 0 ? void 0 : data.btCityQueues) || [];
                        this.btCityQueueMap = {};
                        arr.forEach(function (m) { return _this.btCityQueueMap[m.index] = new BTCityObj_1.default().init(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化遗迹信息
    WorldModel.prototype.initAncientInfos = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, list;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetAncientInfo')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        list = (data === null || data === void 0 ? void 0 : data.list) || [];
                        list.forEach(function (m) { var _a; return (_a = _this.ancientMap[m.index]) === null || _a === void 0 ? void 0 : _a.updateInfo(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化联盟内政
    WorldModel.prototype.initAlliBaseInfo = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, allis;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetAllAlliBaseInfo')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        allis = (data === null || data === void 0 ? void 0 : data.allis) || [];
                        this.alliBaseInfoMap = {};
                        allis.forEach(function (m) { return _this.alliBaseInfoMap[m.uid] = _this.updateAlliBaseInfo(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化屯田队列
    WorldModel.prototype.initTondenQueues = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, obj, key;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_GetTondenDist')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        obj = (data === null || data === void 0 ? void 0 : data.tondens) || {};
                        this.tondenQueueMap = {};
                        for (key in obj) {
                            this.tondenQueueMap[key] = new TondenObj_1.default().init(Number(key), obj[key]);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取服务器运行时间
    WorldModel.prototype.getServerRunTime = function () {
        if (this.runTime === 0 || this.initTime === 0) {
            return 0;
        }
        return this.runTime + (Date.now() - this.initTime);
    };
    // 获取胜利条件数值
    WorldModel.prototype.getWinCondType = function () { return this.winCondType; };
    WorldModel.prototype.getWinCondValue = function () { return this.winCondValue; };
    WorldModel.prototype.getWinCondMaxDay = function () { return this.winCondMaxDay; };
    WorldModel.prototype.getWinCondCloseDay = function () { return this.winCondCloseDay; };
    // 是否血战到底模式
    WorldModel.prototype.isKarmicMahjong = function () { return this.winCondType === Enums_1.WinCondType.KARMIC_MAHJONG; };
    // 季节
    WorldModel.prototype.getSeason = function () { return this.season; };
    WorldModel.prototype.getSeasonType = function () { return this.season.type; };
    WorldModel.prototype.getSeasonPolicys = function () { return this.season.getPolicys(); };
    // 获取当前已沦陷人数
    WorldModel.prototype.getCaptureNum = function () { return this.captureNum; };
    // 是否游戏结束
    WorldModel.prototype.isGameOver = function () { return !!this.gameOverInfo; };
    WorldModel.prototype.getGameOverInfo = function () { return this.gameOverInfo; };
    WorldModel.prototype.setGameOverInfo = function (data) {
        this.gameOverInfo = data;
        if (data) {
            this.gameOverInfo.getTime = Date.now();
        }
    };
    // 获取专属装备效果
    WorldModel.prototype.getExclusiveEquipEffects = function (id) {
        var _a, _b;
        return GameHelper_1.gameHpr.isInLobby() ? [] : ((_b = (_a = this.gameConfInfo) === null || _a === void 0 ? void 0 : _a.exclusiveMap) === null || _b === void 0 ? void 0 : _b[id]) || [];
    };
    // 获取士兵基础费用
    WorldModel.prototype.getPawnBaseCost = function (id) {
        var _a, _b;
        return ((_b = (_a = this.gameConfInfo) === null || _a === void 0 ? void 0 : _a.pawnCostMap) === null || _b === void 0 ? void 0 : _b[id]) || 0;
    };
    // 获取时间
    WorldModel.prototype.getEventValue = function (type) {
        return this.worldEventMap[type];
    };
    // 初始相机信息
    WorldModel.prototype.initCameraInfo = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        if (uid !== this.cameraInfo.uid) {
            this.cameraInfo.uid = uid;
            this.cameraInfo.zoomRatio = -1;
        }
        if (this.cameraInfo.zoomRatio === -1) { //第一次让相机居中
            CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(cc.v2(0.5, 0.5).addSelf(this.centre)), MapHelper_1.mapHelper.MAP_SIZE, Constant_1.MAP_SHOW_OFFSET, this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CAMERA_ZOOM));
        }
        else {
            CameraCtrl_1.cameraCtrl.initByPosition(Constant_1.MAP_SHOW_OFFSET, this.cameraInfo.position, this.cameraInfo.zoomRatio, MapHelper_1.mapHelper.MAP_SIZE);
        }
    };
    // 保存相机信息
    WorldModel.prototype.saveCameraInfo = function () {
        var zoom = this.cameraInfo.saveZoomRatio = this.cameraInfo.zoomRatio = CameraCtrl_1.cameraCtrl.zoomRatio;
        this.cameraInfo.position.set(CameraCtrl_1.cameraCtrl.getPosition());
        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.MAIN_CAMERA_ZOOM, zoom);
    };
    // 所有地块信息
    WorldModel.prototype.getMapCells = function () { return this.mapCells; };
    // 获取单个地块信息
    WorldModel.prototype.getMapCellByPoint = function (point) { return this.getMapCellByIndex(MapHelper_1.mapHelper.pointToIndex(point)); };
    WorldModel.prototype.getMapCellByIndex = function (index) {
        var _a;
        if (((_a = this.lookCell) === null || _a === void 0 ? void 0 : _a.index) === index) {
            return this.lookCell;
        }
        return this.mapCells[index];
    };
    // 刷新单个地块信息
    WorldModel.prototype.updateMapCellInfo = function (cell, data) {
        var isCityDestroy = cell && cell.cityId && !data.cityId;
        cell === null || cell === void 0 ? void 0 : cell.updateInfo(data);
        if (isCityDestroy && this.tempCityMudMap[cell.index]) {
            var mudList = this.tempCityMudMap[cell.index];
            for (var i = 0; i < mudList.length; i++) {
                var index = mudList[i];
                var list = this.decorationsMap[index];
                if (list) {
                    for (var j = 0; j < list.length; j++) {
                        if (list[j].type === Enums_1.DecorationType.MUD) {
                            list.splice(j, 1);
                            break;
                        }
                    }
                }
            }
            delete this.tempCityMudMap[cell.index];
        }
    };
    // 同步地块信息
    WorldModel.prototype.syncServerCellInfo = function (index) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, err, data, key;
            var _this = this;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_SyncCellInfo', { index: index })];
                    case 1:
                        _b = _c.sent(), err = _b.err, data = _b.data;
                        key = 'data_' + Enums_1.NotifyType.ADD_CELL;
                        (_a = data === null || data === void 0 ? void 0 : data.cells) === null || _a === void 0 ? void 0 : _a.forEach(function (m) {
                            var _a;
                            return _this.updateWorldInfos([(_a = { type: Enums_1.NotifyType.ADD_CELL }, _a[key] = m, _a)]);
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取遗迹列表
    WorldModel.prototype.getAncientMap = function () { return this.ancientMap; };
    WorldModel.prototype.getAncientInfo = function (index) { return this.ancientMap[index]; };
    // 获取战斗分布信息
    WorldModel.prototype.getBattleDistMap = function () { return this.battleDistMap; };
    WorldModel.prototype.getCanShowToMiniMapBattles = function () {
        var cells = [];
        var _loop_2 = function (key) {
            var uids = this_1.battleDistMap[key].list || [];
            var cell = this_1.getMapCellByIndex(Number(key));
            if (!cell || !cell.owner) {
                return "continue";
            }
            else if (uids.every(function (m) { return GameHelper_1.gameHpr.checkIsOneAlliance(m, cell.owner); })) {
                delete this_1.battleDistMap[key];
                return "continue";
            }
            cells.push(cell);
        };
        var this_1 = this;
        for (var key in this.battleDistMap) {
            _loop_2(key);
        }
        return cells;
    };
    // 获取免战分布信息
    WorldModel.prototype.getAvoidWarDistMap = function () { return this.avoidWarDistMap; };
    // 清理普通免战
    WorldModel.prototype.cleanGeneralAvoidWar = function () {
        var has = false;
        for (var k in this.avoidWarDistMap) {
            if (!this.avoidWarDistMap[k].type) {
                delete this.avoidWarDistMap[k];
                has = true;
            }
        }
        if (has) {
            this.emit(EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO);
        }
    };
    // 获取修建城市列表
    WorldModel.prototype.getBTCityQueueMap = function () { return this.btCityQueueMap; };
    // 获取屯田信息
    WorldModel.prototype.getTondenQueueMap = function () { return this.tondenQueueMap; };
    // 获取军队屯田信息
    WorldModel.prototype.getArmyTondenInfo = function (index, auid) {
        var info = this.tondenQueueMap[index];
        return (info === null || info === void 0 ? void 0 : info.armyUid) === auid ? info : null;
    };
    // 获取正在屯田的数量
    WorldModel.prototype.getArmyTodeningCount = function () {
        // 先获取自己的军队uid，再在屯田的map中筛选
        var armys = this.player.getBaseArmys();
        var count = 0, keys = Object.keys(this.tondenQueueMap);
        for (var i = 0; i < keys.length; i++) {
            var key = keys[i], data = this.tondenQueueMap[key];
            if (armys.has('uid', data.armyUid)) {
                count++;
            }
        }
        return count;
    };
    // 联盟基础信息
    WorldModel.prototype.updateAlliBaseInfo = function (data) {
        return {
            uid: data.uid,
            icon: data.icon,
            policys: GameHelper_1.gameHpr.fromSvrByPolicys(data.policys),
            sumEmbassyLv: data.sumEmbassyLv,
            createrIndex: data.createrIndex,
        };
    };
    // 获取联盟基础信息
    WorldModel.prototype.getAlliBaseInfo = function (uid) {
        return this.alliBaseInfoMap[uid];
    };
    // 获取联盟政策
    WorldModel.prototype.getAlliPolicysByUid = function (uid) {
        var _a;
        return ((_a = this.alliBaseInfoMap[uid]) === null || _a === void 0 ? void 0 : _a.policys) || {};
    };
    // 获取盟主位置
    WorldModel.prototype.getAlliCreaterIndexs = function () {
        var indexs = [];
        for (var k in this.alliBaseInfoMap) {
            var alli = this.alliBaseInfoMap[k];
            if (alli.createrIndex > 0) {
                indexs.push({ index: alli.createrIndex, icon: alli.icon });
            }
        }
        return indexs;
    };
    // 中心位置
    WorldModel.prototype.getCentre = function () { return this.centre; };
    WorldModel.prototype.setCentre = function (val) {
        this.centre.set(val);
        // 检测主城是否在摄像机范围内
        var isInRange = !this.player.checkMainNotInScreenRange();
        if (this.mainCityInCameraRange !== isInRange) {
            this.mainCityInCameraRange = isInRange;
            this.emit(EventType_1.default.UPDATE_MC_IN_CAM_RANGE, isInRange);
        }
    };
    // 主城是否在相机范围内
    WorldModel.prototype.isMainCityInCameraRange = function () { return this.mainCityInCameraRange; };
    WorldModel.prototype.setMainCityInCameraRange = function (val) { this.mainCityInCameraRange = val; };
    // 获取相机保存信息
    WorldModel.prototype.getCameraInfo = function () { return this.cameraInfo; };
    WorldModel.prototype.checkUpdateWorldWin = function () {
        var size = CameraCtrl_1.cameraCtrl.getWorldWinSize();
        if (!this.preWinSize.equals(size)) {
            this.preWinSize.set(size);
            this.maxTileRange.set2(Math.ceil(Math.ceil(size.x / Constant_1.TILE_SIZE) / 2) + Constant_1.MAP_EXTRA_SIZE, Math.ceil(Math.ceil(size.y / Constant_1.TILE_SIZE) / 2) + Constant_1.MAP_EXTRA_SIZE);
            this.maxMarchLength = size.mag();
        }
    };
    // 获取最多显示格子数
    WorldModel.prototype.getMaxTileRange = function () {
        this.checkUpdateWorldWin();
        return this.maxTileRange;
    };
    // 获取行军线最大长度
    WorldModel.prototype.getMaxMarchLength = function () {
        this.checkUpdateWorldWin();
        return this.maxMarchLength;
    };
    // 获取范围内的所有地块
    WorldModel.prototype.getRangeCellsByPoint = function (centre) {
        var _this = this;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, this.getMaxTileRange());
        return points.map(function (p) { return { point: p, cell: _this.mapCells[MapHelper_1.mapHelper.pointToIndex(p)] }; });
    };
    // 更新主城周围的信息
    WorldModel.prototype.updateMainCityRangeCell = function (cell) {
        var _this = this;
        if (cell.getAcreage() > 1) {
            var point_1 = cell.actPoint;
            cell.getOwnPoints().forEach(function (m) {
                var c = _this.mapCells[MapHelper_1.mapHelper.pointToIndex(m)];
                if (!c) {
                    return;
                }
                // 刷新地块底 用石头地作为底
                // mapHelper.updateCityLandDi(c, LandType.STONE, this.mapCells)
                // 设置关联
                if (!m.equals(point_1)) {
                    c.setCityDepend(cell.city);
                }
            });
        }
        this.randCityMud(cell);
        this.tempMudIconMap = {};
    };
    WorldModel.prototype.getAllPlayerMap = function () {
        return this.allPlayerInfos;
    };
    // 获取玩家信息
    WorldModel.prototype.getPlayerInfo = function (uid) {
        return this.allPlayerInfos[uid];
    };
    // 根据名字获取玩家信息
    WorldModel.prototype.getPlayerByName = function (name) {
        for (var key in this.allPlayerInfos) {
            var plr = this.allPlayerInfos[key];
            if (plr.nickname === name) {
                return plr;
            }
        }
        return null;
    };
    // 获取玩家
    WorldModel.prototype.getPlayerByNameOrUID = function (name) {
        if (!name) {
            return null;
        }
        var plr = null;
        var uid = Number(name);
        if (uid) {
            plr = this.getPlayerInfo(name);
        }
        if (!plr) {
            plr = this.getPlayerByName(name);
        }
        return plr;
    };
    //
    WorldModel.prototype.addNotPlayNewCells = function (index) {
        if (this.yetPlayNewCellMap[index] || this.notPlayNewCells.has(index)) {
            return;
        }
        this.notPlayNewCells.push(index);
    };
    WorldModel.prototype.getNotPlayNewCells = function () {
        var _this = this;
        if (this.notPlayNewCells.length === 0) {
            return this.notPlayNewCells;
        }
        var cells = [];
        this.notPlayNewCells.forEach(function (m) {
            _this.yetPlayNewCellMap[m] = true;
            cells.push(m);
        });
        this.saveYetPlayNewCellMap();
        this.notPlayNewCells = [];
        return cells;
    };
    WorldModel.prototype.setYetPlayNewCellMap = function (index) {
        if (!this.yetPlayNewCellMap[index]) {
            this.notPlayNewCells.remove(index);
            this.yetPlayNewCellMap[index] = true;
            this.saveYetPlayNewCellMap();
            return true;
        }
        return false;
    };
    WorldModel.prototype.saveYetPlayNewCellMap = function () {
        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.YET_PLAY_NEW_CELL, this.yetPlayNewCellMap);
    };
    WorldModel.prototype.getNotPlayCellTondens = function () {
        var cells = [];
        for (var key in this.notPlayCellTondenMap) {
            cells.push({ index: Number(key), treasureIds: this.notPlayCellTondenMap[key] });
        }
        this.notPlayCellTondenMap = {};
        return cells;
    };
    // 添加玩家信息
    WorldModel.prototype.addPlayerInfoByCell = function (data, cell, init) {
        var _a;
        if (!data) {
            return;
        }
        else if (data.owner !== cell.owner && cell.owner) { //如果之前有人了 先删掉
            var cells = (_a = this.allPlayerInfos[cell.owner]) === null || _a === void 0 ? void 0 : _a.cells;
            cells === null || cells === void 0 ? void 0 : cells.delete(cell.index);
            !init && MapHelper_1.mapHelper.updatePlayerCellBorderLines(cells);
        }
        else if (!data.owner) {
            return;
        }
        var player = this.allPlayerInfos[data.owner];
        if (!player) {
            player = this.allPlayerInfos[data.owner] = GameHelper_1.gameHpr.getEmptyPlayerInfo(data.owner, data.index);
        }
        // 添加地块信息
        player.cells.set(cell.index, cell);
        player.maxLandCount = Math.max(player.cells.size, player.maxLandCount);
        // 记录最大地块数
        if (!init) {
            // 如果玩家信息记录玩家信息
            GameHelper_1.gameHpr.initPlayerInfo(player, data.player);
            // 刷新边框线
            MapHelper_1.mapHelper.updatePlayerCellBorderLines(player.cells);
        }
    };
    // 检测是否可以攻占地块
    WorldModel.prototype.checkCanOccupyCell = function (cell) {
        if (cell.isOneAlliance()) {
            return false; //盟友不能攻占
        }
        // 检测这块地4周是否有我方的地
        var points = MapHelper_1.mapHelper.getOnePointOuter(cell.actPoint, cell.getSize());
        for (var i = 0; i < points.length; i++) {
            var nearCell = this.getMapCellByPoint(points[i]);
            if (!nearCell) {
                continue;
            }
            else if (nearCell.isOneAlliance()) {
                return true;
            }
            else if (nearCell.landType == Enums_1.LandType.PASS && MapHelper_1.mapHelper.checkBridgeCanPass(cell, nearCell)) {
                return true; //如果是桥需要看 桥的另外一头是否有我方的地 
            }
        }
        return false;
    };
    WorldModel.prototype.checkCanOccupyCellByIndex = function (index) {
        var cell = this.getMapCellByIndex(index);
        return !!cell && this.checkCanOccupyCell(cell);
    };
    // 设置当前查看的地块信息
    WorldModel.prototype.getLookCell = function () { return this.lookCell; };
    WorldModel.prototype.setLookCell = function (cell) {
        this.lookCell = cell;
    };
    // 获取城市皮肤id
    WorldModel.prototype.getCitySkinByIndex = function (index) {
        return this.citySkinMap[index] || 0;
    };
    // 刷新城市皮肤
    WorldModel.prototype.updateCitySkin = function (index, id) {
        if (!id) {
            delete this.citySkinMap[index];
        }
        else {
            this.citySkinMap[index] = id;
        }
        this.emit(EventType_1.default.UPDATE_CITY_SKIN, index);
    };
    // 获取所有行军信息
    WorldModel.prototype.getAllMarchs = function () {
        var arr = [];
        arr.pushArr(this.marchs);
        arr.pushArr(this.transits);
        return arr;
    };
    WorldModel.prototype.getMarchs = function () { return this.marchs; };
    WorldModel.prototype.getTransits = function () { return this.transits; };
    // 刷新行军相关
    WorldModel.prototype.updateMarchByIndex = function (index) {
        var has = false;
        this.marchs.forEach(function (m) {
            if (index === -1 || m.startIndex === index || m.targetIndex === index) {
                var type = m.targetType;
                if (m.updateTargetType() !== type) {
                    has = true;
                }
            }
        });
        if (has) {
            this.emit(EventType_1.default.UPDATE_ALL_MARCH);
        }
    };
    // 删除行军线
    WorldModel.prototype.removeMarch = function (uid) {
        var march = this.marchs.remove('uid', uid);
        march && this.emit(EventType_1.default.REMOVE_MARCH, march);
    };
    // 设置行军线透明度
    WorldModel.prototype.setAllMarchOpacity = function (progressMap, defaultRatio) {
        if (defaultRatio === void 0) { defaultRatio = 100; }
        GameHelper_1.gameHpr.world.getAllMarchs().forEach(function (m) {
            var _a;
            var progress = (_a = progressMap[m.getMarchLineType()]) !== null && _a !== void 0 ? _a : defaultRatio;
            m.opacity = Math.floor(255 * progress * 0.01);
        });
        this.emit(EventType_1.default.UPDATE_MARCH_OPACITY);
    };
    // 获取城市产出
    WorldModel.prototype.getCityOutputMap = function () {
        return this.tempCityOutputMap;
    };
    WorldModel.prototype.setCityOutput = function (index, obj) {
        if (obj) {
            this.tempCityOutputMap[index] = obj;
        }
        else {
            delete this.tempCityOutputMap[index];
        }
    };
    // 攻占地块
    WorldModel.prototype.occupyCell = function (armys, index, autoBackType, isSameSpeed) {
        return __awaiter(this, void 0, void 0, function () {
            var indexs, uids, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        indexs = [], uids = [];
                        armys.forEach(function (m) {
                            indexs.push(m.index);
                            uids.push(m.uid);
                        });
                        return [4 /*yield*/, this.net.request('game/HD_OccupyCell', { indexs: indexs, uids: uids, target: index, autoBackType: autoBackType, isSameSpeed: isSameSpeed }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.NOT_IN_OCCUPY_TIME) { //提示只能在固定时间攻击
                            ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_time_tip');
                            return [2 /*return*/, ''];
                        }
                        else if (err === ECode_1.ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击
                            ViewHelper_1.viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip');
                            return [2 /*return*/, ''];
                        }
                        else if (err === ECode_1.ecode.ANTI_CHEAT) { // 防作弊检测
                            ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
                            return [2 /*return*/, ''];
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 屯田
    WorldModel.prototype.cellTonden = function (army, index) {
        return __awaiter(this, void 0, void 0, function () {
            var tondenCount, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        tondenCount = Math.max(0, Constant_1.TODAY_TONDEN_MAX_COUNT - GameHelper_1.gameHpr.player.getCellTondenCount());
                        if (tondenCount <= this.getArmyTodeningCount()) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.CELL_TONDEN_LIMIT)];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_CellTonden', { index: army.index, uid: army.uid, target: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.ANTI_CHEAT) { //防作弊检测
                            ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
                            return [2 /*return*/, ''];
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 取消屯田
    WorldModel.prototype.cancelTonden = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_CancelCellTonden', { index: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 移动军队
    WorldModel.prototype.moveCellArmy = function (armys, index, isSameSpeed) {
        return __awaiter(this, void 0, void 0, function () {
            var indexs, uids, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        indexs = [], uids = [];
                        armys.forEach(function (m) {
                            indexs.push(m.index);
                            uids.push(m.uid);
                        });
                        return [4 /*yield*/, this.net.request('game/HD_MoveCellArmy', { indexs: indexs, uids: uids, target: index, isSameSpeed: isSameSpeed }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 获取联盟列表
    WorldModel.prototype.getAlliances = function (interval) {
        if (interval === void 0) { interval = 3; }
        return __awaiter(this, void 0, void 0, function () {
            var now, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        now = Date.now();
                        if (interval > 0 && this.lastReqAlliancesTime > 0 && now - this.lastReqAlliancesTime <= interval * 1000) {
                            return [2 /*return*/, this.tempAlliances];
                        }
                        return [4 /*yield*/, this.net.request('game/HD_GetAlliances')];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqAlliancesTime = now;
                        this.tempAlliances = (data === null || data === void 0 ? void 0 : data.list) || [];
                        return [2 /*return*/, this.tempAlliances];
                }
            });
        });
    };
    // 申请加入联盟
    WorldModel.prototype.applyJoinAlliance = function (uid, desc) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, alli;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ApplyJoinAlliance', { uid: uid, desc: desc }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            alli = this.tempAlliances.find(function (m) { return m.uid === uid; });
                            if (alli) {
                                alli.isApply = true;
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 取消申请加入联盟
    WorldModel.prototype.cancelJoinAlliance = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, alli;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_CancelJoinAlliance', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            alli = this.tempAlliances.find(function (m) { return m.uid === uid; });
                            if (alli) {
                                alli.isApply = false;
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 取消行军
    WorldModel.prototype.cancelMarch = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_CancelMarch', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 强制撤离军队
    WorldModel.prototype.forceRevoke = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ForceRevokeArmy', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 修建城市
    WorldModel.prototype.createCity = function (index, id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_CreateCity', { index: index, id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err && data) {
                            this.player.updateOutputByFlags(data.output);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 拆除城市
    WorldModel.prototype.dismantleCity = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_DismantleCity', { index: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 领取城市产出
    WorldModel.prototype.claimCityOutput = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.net.request('game/HD_ClaimCityOutput', { index: index }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.player.updateRewardItemsByFlags(data.rewards);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    WorldModel.prototype.update = function (dt) {
        // 每帧最多执行多少个
        if (this.tempWorldUpdateList.length > 0) {
            this.updateWorldInfos(this.tempWorldUpdateList.splice(0, 200));
        }
    };
    // ----------------------------------------- net listener function --------------------------------------------
    // 更新世界信息
    WorldModel.prototype.OnUpdateWorldInfo = function (data) {
        if (data.list) {
            this.tempWorldUpdateList.pushArr(data.list);
        }
    };
    WorldModel.prototype.updateWorldInfos = function (datas) {
        var _this = this;
        cc.log('OnUpdateWorldInfo', datas);
        this.tempMainCityCells = [];
        this.tempHasUpdateCellInfo = false;
        this.tempHasUpdateCellInfoByMe = false;
        datas.forEach(function (m) { return _this.updateWorldInfoOne(m.type, m['data_' + m.type]); });
        // 有更新地块信息发通知
        if (this.tempHasUpdateCellInfo) {
            this.emit(EventType_1.default.UPDATE_CELL_INFO);
        }
        // 有打野地块成功
        if (this.tempHasUpdateCellInfoByMe) {
        }
        // 添加关联
        this.tempMainCityCells.forEach(function (m) { return _this.updateMainCityRangeCell(m); });
        this.tempMainCityCells = [];
        // 播放效果
        if (this.notPlayNewCells.length > 0) {
            this.emit(EventType_1.default.PLAY_NEW_CELL_EFFECT);
        }
        // 播放屯田结束效果
        if (Object.keys(this.notPlayCellTondenMap).length > 0) {
            this.emit(EventType_1.default.PLAY_CELL_TONDEN_EFFECT);
        }
    };
    WorldModel.prototype.updateWorldInfoOne = function (type, data) {
        var _this = this;
        var _a, _b, _c, _d;
        if (type === Enums_1.NotifyType.ADD_CELL) { //添加地块
            var cell = this.mapCells[data.index];
            if (cell) {
                MapUionFindHelper_1.mapUionFindHelper.checkNearUnLock(cell);
                var isPlayer = !!cell.owner;
                // 刷新边框线
                this.addPlayerInfoByCell(data, cell);
                // 刷新信息
                this.updateMapCellInfo(cell, data);
                var isOwn = cell.isOwn();
                // 添加到等待播放特效的列表
                if (((_a = cell.city) === null || _a === void 0 ? void 0 : _a.id) !== Constant_1.CITY_MAIN_NID && isOwn) {
                    this.addNotPlayNewCells(data.index);
                }
                // 刷新关联
                if (cell.getAcreage() > 1) {
                    this.tempMainCityCells.push(cell);
                }
                // 当前行军是否有这块地
                this.updateMarchByIndex(data.index);
                // 是否自己的地块
                if (isOwn) {
                    if (!isPlayer) {
                        this.tempHasUpdateCellInfoByMe = true;
                        this.player.addTodayOccupyCellCount(1);
                    }
                    EventReportHelper_1.eventReportHelper.checkReportOweCellCount();
                    EventReportHelper_1.eventReportHelper.checkReportOccupyCell();
                }
                // 是否遗迹
                if (cell.isAncient() && !this.ancientMap[cell.index]) {
                    this.ancientMap[cell.index] = new AncientObj_1.default().init(cell);
                }
                this.tempHasUpdateCellInfo = true;
            }
            else {
                ErrorReportHelper_1.errorReportHelper.reportError('updateWorldInfoOne', 'ADD_CELL error, cell == null, this.mapCells.length=' + this.mapCells.length + ', index=' + data.index);
            }
        }
        else if (type === Enums_1.NotifyType.REMOVE_CELLS) { //删除地块
            data.forEach(function (index) {
                _this.updateMapCellInfo(_this.mapCells[index], {});
                _this.areaCenter.removeArea(index); //删除之后重新申请
            });
            this.tempHasUpdateCellInfo = true;
        }
        else if (type === Enums_1.NotifyType.ADD_PLAYER) { //添加玩家
            this.allPlayerInfos[data.uid] = GameHelper_1.gameHpr.initPlayerInfo(GameHelper_1.gameHpr.getEmptyPlayerInfo(data.uid, data.mainCityIndex), data);
        }
        else if (type === Enums_1.NotifyType.DELETE_PLAYER) { //删除玩家
            delete this.allPlayerInfos[data];
        }
        else if (type === Enums_1.NotifyType.PLAYER_GIVEUP_GAME) { //玩家放弃对局
            var info = this.allPlayerInfos[data];
            if (info) {
                info.isGiveupGame = true;
            }
        }
        else if (type === Enums_1.NotifyType.UPDATE_CELL_HP) { //刷新地块血量
            // const cell = this.mapCells[data.index]
            // cell.hp = data.hp
            // this.areaCenter.getArea(data.index)?.updateHp(data.hp)
        }
        else if (type === Enums_1.NotifyType.ADD_MARCH) { //添加行军
            var march = this.marchs.find(function (m) { return m.uid === data.uid; }) || this.marchs.add(new MarchObj_1.default());
            march.init(data);
            this.emit(EventType_1.default.ADD_MARCH, march);
            // cc.log(data.uid, data.armyName, data.owner)
        }
        else if (type === Enums_1.NotifyType.REMOVE_MARCH) { //删除行军
            this.removeMarch(data.uid);
        }
        else if (type === Enums_1.NotifyType.ADD_TRANSIT) { //添加运送
            var index = this.player.getMainCityIndex();
            if (data.targetIndex === index || data.startIndex === index) {
                var transit = this.transits.find(function (m) { return m.uid === data.uid; }) || this.transits.add(new TransitObj_1.default());
                transit.init(data);
                this.emit(EventType_1.default.ADD_MARCH, transit);
            }
        }
        else if (type === Enums_1.NotifyType.REMOVE_TRANSIT) { //删除运送
            var transit = this.transits.remove('uid', data);
            transit && this.emit(EventType_1.default.REMOVE_MARCH, transit);
        }
        else if (type === Enums_1.NotifyType.BATTLE_DIST) { //战斗分布
            if ((_b = data.uids) === null || _b === void 0 ? void 0 : _b.length) {
                this.battleDistMap[data.index] = { list: data.uids };
            }
            else {
                delete this.battleDistMap[data.index];
            }
            this.emit(EventType_1.default.UPDATE_BATTLE_DIST_INFO);
            this.updateMarchByIndex(data.index);
        }
        else if (type === Enums_1.NotifyType.AREA_AVOID_WAR) { //免战分布
            if (!data.time || data.time <= 0) {
                delete this.avoidWarDistMap[data.index];
            }
            else {
                this.avoidWarDistMap[data.index] = new AvoidWarObj_1.default().init(data.index, data.time, data.type || 0);
            }
            this.emit(EventType_1.default.UPDATE_AVOIDWAR_DIST_INFO);
        }
        else if (type === Enums_1.NotifyType.ADD_BTCITY) { //添加修建城市
            this.btCityQueueMap[data.index] = new BTCityObj_1.default().init(data);
            this.emit(EventType_1.default.UPDATE_BT_CITY, data.index);
        }
        else if (type === Enums_1.NotifyType.REMOVE_BTCITY) { //删除修建城市
            delete this.btCityQueueMap[data.index];
            if (data.cell) {
                this.updateMapCellInfo(this.mapCells[data.index], data.cell);
                var area = this.areaCenter.getArea(data.index);
                area === null || area === void 0 ? void 0 : area.updateBuilds(data.builds || []); //刷新区域的建筑信息
                area === null || area === void 0 ? void 0 : area.updateCity(data.cell.cityId); //刷新区域的城市信息
            }
            this.emit(EventType_1.default.UPDATE_BT_CITY, data.index);
        }
        else if (type === Enums_1.NotifyType.CAPTURE) { //有玩家被攻陷
            if (data.uid === this.user.getUid()) {
                GameHelper_1.gameHpr.message.clean(); //直接清理通知
                this.player.setCaptureInfo({ uid: data.attacker, time: data.time });
                // 血战模式下 盟主沦陷不弹沦陷界面
                if (this.isKarmicMahjong() && GameHelper_1.gameHpr.alliance.isMeCreater()) {
                }
                else if (mc.currWindName !== 'main') {
                    ViewHelper_1.viewHelper.gotoWind('main').then(function () { return PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/CaptureTip' }); });
                }
                else {
                    PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/CaptureTip' });
                }
            }
            this.captureNum += 1; //沦陷次数+1
            GameHelper_1.gameHpr.rank.clean(); //重新获取排行信息
            this.emit(EventType_1.default.SYS_MSG_NOTICE, { id: 10001, parames: [GameHelper_1.gameHpr.getPlayerName(data.attacker), GameHelper_1.gameHpr.getPlayerName(data.uid)] });
        }
        else if (type === Enums_1.NotifyType.DISSOLVE_ALLIANCE) { //解散联盟
            this.lastReqAlliancesTime = 0; //让下次从新申请
            var alli = this.player.getAlliance(), isMe = alli.getUid() === data;
            if (isMe) {
                alli.clean();
            }
            for (var key in this.allPlayerInfos) {
                var m = this.allPlayerInfos[key];
                if (m.allianceUid === data) {
                    m.allianceUid = '';
                    m.allianceName = '';
                    m.allianceIcon = 0;
                }
            }
            delete this.alliBaseInfoMap[data]; //删除基础信息
            this.emit(EventType_1.default.UPDATE_ALLIANCE_BASEINFO, [data]);
            if (isMe) {
                this.emit(EventType_1.default.UPDATE_ALLIANCE);
                this.emit(EventType_1.default.UPDATE_CELL_INFO); //通知刷新边界线
                this.emit(EventType_1.default.UPDATE_ALL_MARCH); //刷新行军线
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_JOIN_ALLI) { //有玩家加入联盟
            this.lastReqAlliancesTime = 0; //让下次从新申请
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.allianceUid = data.baseInfo.uid;
                plr.allianceName = data.allianceName;
                plr.allianceIcon = data.baseInfo.icon;
                if (data.uid === this.user.getUid() || plr.allianceUid === this.player.getAllianceUid()) {
                    this.emit(EventType_1.default.UPDATE_CELL_INFO); //通知刷新边界线
                    // this.emit(EventType.UPDATE_ALL_MARCH) //刷新行军线
                }
            }
            this.alliBaseInfoMap[data.baseInfo.uid] = this.updateAlliBaseInfo(data.baseInfo);
            this.emit(EventType_1.default.UPDATE_ALLIANCE_BASEINFO, [data.baseInfo.uid]);
        }
        else if (type === Enums_1.NotifyType.PLAYER_EXIT_ALLI) { //有玩家退出联盟
            this.lastReqAlliancesTime = 0; //让下次从新申请
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                var allianceUid = plr.allianceUid;
                plr.allianceUid = '';
                plr.allianceName = '';
                plr.allianceIcon = 0;
                // 跟自己有关系
                if (plr.uid === this.user.getUid() || allianceUid === this.player.getAllianceUid()) {
                    this.emit(EventType_1.default.UPDATE_CELL_INFO); //通知刷新边界线
                }
            }
            this.alliBaseInfoMap[data.baseInfo.uid] = this.updateAlliBaseInfo(data.baseInfo);
            this.emit(EventType_1.default.UPDATE_ALLIANCE_BASEINFO, [data.baseInfo.uid]);
        }
        else if (type === Enums_1.NotifyType.MODIFY_NICKNAME) { //有玩家修改昵称
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.nickname = data.nickname;
                this.emit(EventType_1.default.UPDATE_PLAYER_NICKNAME, plr);
            }
        }
        else if (type === Enums_1.NotifyType.CHANGE_TITLE) { //改变玩家称号
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.title = data.title;
                this.emit(EventType_1.default.UPDATE_PLAYER_NICKNAME, plr);
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_TOWER_LV) { //刷新玩家箭塔等级
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.towerLvMap = data.towerLvMap || {};
                this.emit(EventType_1.default.UPDATE_PLAYER_TOWER_LV, data);
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_POLICY) { //刷新玩家政策
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.policys = GameHelper_1.gameHpr.fromSvrByPolicys(data.policys || {});
            }
        }
        else if (type === Enums_1.NotifyType.PLAYER_CADET_LV) { //刷新玩家见习勇者层数
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.rodeleroCadetLv = data.lv || 0;
            }
        }
        else if (type === Enums_1.NotifyType.CELL_EMOJI) { //地图表情
            GameHelper_1.gameHpr.ground.addCellEmoji(data);
        }
        else if (type === Enums_1.NotifyType.ALLI_BASE_INFO) { //盟主基础信息
            data.forEach(function (m) { return _this.alliBaseInfoMap[m.uid] = _this.updateAlliBaseInfo(m); });
            this.emit(EventType_1.default.UPDATE_ALLIANCE_BASEINFO, data.map(function (m) { return m.uid; }));
        }
        else if (type === Enums_1.NotifyType.PLAYER_CITY_OUTPUT) { //玩家城市产出
            var plr = this.getPlayerInfo(data.uid);
            if (plr) {
                plr.cityOutputMap = GameHelper_1.gameHpr.wrapCityOutputMap(data.outputMap || {}, plr.cityOutputMap);
            }
            this.tempHasUpdateCellInfo = true;
            this.emit(EventType_1.default.UPDATE_CITY_OUTPUT);
        }
        else if (type === Enums_1.NotifyType.UPDATE_SEASON) { //刷新季节
            var type_1 = this.season.type;
            this.season.init(data);
            if (type_1 !== this.season.type) {
                this.emit(EventType_1.default.CHANGE_SEASON);
                this.emit(mc.Event.HIDE_ALL_PNL);
                PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/SeasonSwitch' });
            }
        }
        else if (type === Enums_1.NotifyType.ANCIENT_INFO) { //遗迹信息
            var info = this.ancientMap[data.index];
            if (!info) {
                info = this.ancientMap[data.index] = new AncientObj_1.default().init(this.getMapCellByIndex(data.index));
            }
            info.updateInfo(data, true);
            this.emit(EventType_1.default.UPDATE_ANCIENT_INFO, info);
        }
        else if (type === Enums_1.NotifyType.CHANGE_CITY_SKIN) { //城市皮肤
            if (this.citySkinMap[data.index] !== data.id) {
                this.updateCitySkin(data.index, data.id);
            }
        }
        else if (type === Enums_1.NotifyType.CELL_TONDEN) { //屯田信息
            if (data.time > 0) {
                this.tondenQueueMap[data.index] = new TondenObj_1.default().init(data.index, data);
            }
            else {
                delete this.tondenQueueMap[data.index];
            }
            if (!!((_c = data.treasureIds) === null || _c === void 0 ? void 0 : _c.length) && ((_d = this.getMapCellByIndex(data.index)) === null || _d === void 0 ? void 0 : _d.isOwn())) {
                var list = this.notPlayCellTondenMap[data.index];
                if (!list) {
                    this.notPlayCellTondenMap[data.index] = list = [];
                }
                list.pushArr(data.treasureIds);
            }
            this.emit(EventType_1.default.UPDATE_TONDEN, data.index);
        }
        else if (type === Enums_1.NotifyType.SYS_MSG) { //系统消息
            this.emit(EventType_1.default.SYS_MSG_NOTICE, data);
        }
        else if (type === Enums_1.NotifyType.GAME_OVER) { //游戏结束
            this.setGameOverInfo(data);
            PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/GameOver' });
        }
        else if (type === Enums_1.NotifyType.ALLI_SETTLE) { //联盟结算
            var allianceName_1 = '';
            data.list.forEach(function (uid) {
                var info = _this.getPlayerInfo(uid);
                if (info) {
                    info.isSettled = true;
                    if (!allianceName_1) {
                        allianceName_1 = info.allianceName;
                    }
                }
            });
            // 自己联盟 弹出结算界面
            if (GameHelper_1.gameHpr.alliance.getUid() === data.uid) {
                delete this.alliBaseInfoMap[data.uid];
                if (!data.isGameOver) {
                    PopupPnlHelper_1.popupPnlHelper.add({ key: 'main/GameOver' });
                }
            }
            // 重新获取排行信息
            GameHelper_1.gameHpr.rank.clean();
            // 公告：'xxx'联盟已出局
            if (allianceName_1 && !data.isGameOver) {
                this.emit(EventType_1.default.SYS_MSG_NOTICE, { id: 10008, parames: [allianceName_1] });
            }
        }
        else if (type === Enums_1.NotifyType.WORLD_EVENT) { //世界事件
            this.worldEventMap = data || {};
        }
    };
    WorldModel.prototype.getDecorationByIndex = function (index) {
        return this.decorationsMap[index];
    };
    // 随机主城周围的土地
    WorldModel.prototype.randCityMud = function (cell) {
        if (cell.isAncient()) {
            return;
        }
        var muList = this.tempCityMudMap[cell.index];
        if (!muList) {
            muList = this.tempCityMudMap[cell.index] = [];
        }
        muList.length = 0;
        this.random.setSeed(cell.index);
        var mudIndex = this.random.get(0, MapHelper_1.mapHelper.cityMudList.length - 1);
        var mudList = MapHelper_1.mapHelper.cityMudList[mudIndex];
        for (var i = 0; i < mudList.length; i++) {
            var targetX = cell.point.x + mudList[i][0];
            var targetY = cell.point.y + mudList[i][1];
            var index = targetY * MapHelper_1.mapHelper.MAP_SIZE.x + targetX;
            if (this.mapCells[index]) {
                muList.push(index);
                var info = { decorationActIndex: index, decorationJson: assetsMgr.getJsonData('decoration', mudList[i][2]) };
                var list = this.decorationsMap[index];
                if (!list) {
                    this.decorationsMap[index] = list = [];
                }
                list.push(info);
            }
        }
    };
    //获取主城土地
    WorldModel.prototype.getMudJsonByIndex = function (index) {
        var list = this.decorationsMap[index];
        if (list) {
            for (var i = 0; i < list.length; i++) {
                if (list[i].decorationJson.type === Enums_1.DecorationType.MUD || list[i].decorationJson.type === Enums_1.DecorationType.MUD_OUTER) {
                    return list[i].decorationJson;
                }
            }
        }
        return null;
    };
    //获取装饰icon，主城土地icon需要转换一次
    WorldModel.prototype.getDecorationIcon = function (decorationJson, decorationActIndex) {
        if (decorationJson.type == Enums_1.DecorationType.MUD || decorationJson.type == Enums_1.DecorationType.MUD_OUTER) {
            var icon = this.tempMudIconMap[decorationActIndex];
            if (!icon) {
                //方向：左上右下
                var directionList = [[-1, 0], [0, 1], [1, 0], [0, -1]];
                var key = '';
                var point = this.mapCells[decorationActIndex].point;
                for (var i = 0; i < directionList.length; i++) {
                    var dir = directionList[i];
                    var x = point.x + dir[0];
                    var y = point.y + dir[1];
                    var index = y * MapHelper_1.mapHelper.MAP_SIZE.x + x;
                    var nearMudJson = this.getMudJsonByIndex(index);
                    if (nearMudJson) {
                        if (decorationJson.type === Enums_1.DecorationType.MUD) {
                            key += nearMudJson.type === Enums_1.DecorationType.MUD ? 1 : 0;
                        }
                        else if (decorationJson.type === Enums_1.DecorationType.MUD_OUTER) {
                            key += (nearMudJson.type === Enums_1.DecorationType.MUD || nearMudJson.type === Enums_1.DecorationType.MUD_OUTER) ? 1 : 0;
                        }
                    }
                    else {
                        key += 0;
                    }
                }
                var targetId = decorationJson.type == Enums_1.DecorationType.MUD ? Constant_1.DECORATION_MUD_CONF[key] : Constant_1.DECORATION_MUD_OUTER_CONF[key];
                if (typeof targetId == 'object') {
                    this.random.setSeed(decorationActIndex);
                    var endIndex = this.random.get(0, targetId.length - 1);
                    targetId = targetId[endIndex];
                }
                var targetData = assetsMgr.getJsonData('decoration', targetId);
                icon = this.tempMudIconMap[decorationActIndex] = targetData.icon;
            }
            return icon;
        }
        return decorationJson.icon;
    };
    WorldModel.prototype.getRoundId = function (x, y) {
        var key = x + '_' + y;
        var landId = this.islandRoundMap[key];
        return landId;
    };
    WorldModel = __decorate([
        mc.addmodel('world')
    ], WorldModel);
    return WorldModel;
}(mc.BaseModel));
exports.default = WorldModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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