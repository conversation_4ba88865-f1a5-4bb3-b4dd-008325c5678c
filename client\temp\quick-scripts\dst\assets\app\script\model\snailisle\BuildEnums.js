
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/BuildEnums.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a2888r+6XxOA5tLuK8YRVQA', 'BuildEnums');
// app/script/model/snailisle/BuildEnums.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SnailisleBuildType = void 0;
// 蜗牛岛设施类型
var SnailisleBuildType;
(function (SnailisleBuildType) {
    SnailisleBuildType[SnailisleBuildType["RESTFLOOR"] = 1] = "RESTFLOOR";
    SnailisleBuildType[SnailisleBuildType["RECRFLOOR"] = 2] = "RECRFLOOR";
    SnailisleBuildType[SnailisleBuildType["TASTFLOOR"] = 3] = "TASTFLOOR";
    SnailisleBuildType[SnailisleBuildType["SOFA"] = 4] = "SOFA";
    SnailisleBuildType[SnailisleBuildType["SOFA2"] = 5] = "SOFA2";
    SnailisleBuildType[SnailisleBuildType["BLACKBOARD"] = 6] = "BLACKBOARD";
    SnailisleBuildType[SnailisleBuildType["COMPUTER1"] = 7] = "COMPUTER1";
    SnailisleBuildType[SnailisleBuildType["COMPUTER2"] = 8] = "COMPUTER2";
})(SnailisleBuildType || (SnailisleBuildType = {}));
exports.SnailisleBuildType = SnailisleBuildType;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxzbmFpbGlzbGVcXEJ1aWxkRW51bXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0EsVUFBVTtBQUNWLElBQUssa0JBU0o7QUFURCxXQUFLLGtCQUFrQjtJQUNuQixxRUFBYSxDQUFBO0lBQ2IscUVBQVMsQ0FBQTtJQUNULHFFQUFTLENBQUE7SUFDVCwyREFBSSxDQUFBO0lBQ0osNkRBQUssQ0FBQTtJQUNMLHVFQUFVLENBQUE7SUFDVixxRUFBUyxDQUFBO0lBQ1QscUVBQVMsQ0FBQTtBQUNiLENBQUMsRUFUSSxrQkFBa0IsS0FBbEIsa0JBQWtCLFFBU3RCO0FBR0csZ0RBQWtCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIOicl+eJm+Wym+iuvuaWveexu+Wei1xyXG5lbnVtIFNuYWlsaXNsZUJ1aWxkVHlwZSB7XHJcbiAgICBSRVNURkxPT1IgPSAxLCAvL+S8keaBr+WMuuWcsOadvyAxICB4XHJcbiAgICBSRUNSRkxPT1IsIC8v5aix5LmQ5Yy65Zyw5p2/IDIgIHhcclxuICAgIFRBU1RGTE9PUiwgLy/lk4HojLbljLrlnLDmnb8gMyAgeFxyXG4gICAgU09GQSwgLy/mspnlj5EgNFxyXG4gICAgU09GQTIsIC8v5rKZ5Y+RIDVcclxuICAgIEJMQUNLQk9BUkQsIC8v6buR5p2/IDZcclxuICAgIENPTVBVVEVSMSwgLy/nlLXohJExIDdcclxuICAgIENPTVBVVEVSMiwgLy/nlLXohJEyIDhcclxufVxyXG5cclxuZXhwb3J0IHtcclxuICAgIFNuYWlsaXNsZUJ1aWxkVHlwZSxcclxufSJdfQ==