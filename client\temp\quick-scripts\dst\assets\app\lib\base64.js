
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/lib/base64.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fd840mAOXJMAKgUQBNQzAzJ', 'base64');
// app/lib/base64.js

"use strict";

//
// THIS FILE IS AUTOMATICALLY GENERATED! DO NOT EDIT BY HAND!
//
// var Base64 = window['Base64'] = {};
window['Base64'] = function (global, factory) {
  // typeof exports === 'object' && typeof module !== 'undefined'
  //     ? module.exports = factory()
  //     : typeof define === 'function' && define.amd
  //         ? define(factory) :
  //         // cf. https://github.com/dankogai/js-base64/issues/119
  //         (function () {
  //             // existing version for noConflict()
  //             var _Base64 = global.Base64;
  //             var gBase64 = factory();
  //             gBase64.noConflict = function () {
  //                 global.Base64 = _Base64;
  //                 return gBase64;
  //             };
  //             if (global.Meteor) { // Meteor.js
  //                 Base64 = gBase64;
  //             }
  //             global.Base64 = gBase64;
  //         })();
  return factory();
}(window, function () {
  // 'use strict';

  /**
   *  base64.ts
   *
   *  Licensed under the BSD 3-Clause License.
   *    http://opensource.org/licenses/BSD-3-Clause
   *
   *  References:
   *    http://en.wikipedia.org/wiki/Base64
   *
   * <AUTHOR> Kogai (https://github.com/dankogai)
   */
  var version = '3.7.7';
  /**
   * @deprecated use lowercase `version`.
   */

  var VERSION = version; // var _hasBuffer = typeof Buffer === 'function';

  var _TD = typeof TextDecoder === 'function' ? new TextDecoder() : undefined;

  var _TE = typeof TextEncoder === 'function' ? new TextEncoder() : undefined;

  var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  var b64chs = Array.prototype.slice.call(b64ch);

  var b64tab = function (a) {
    var tab = {};
    a.forEach(function (c, i) {
      return tab[c] = i;
    });
    return tab;
  }(b64chs);

  var b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;

  var _fromCC = String.fromCharCode.bind(String);

  var _U8Afrom = typeof Uint8Array.from === 'function' ? Uint8Array.from.bind(Uint8Array) : function (it) {
    return new Uint8Array(Array.prototype.slice.call(it, 0));
  };

  var _mkUriSafe = function _mkUriSafe(src) {
    return src.replace(/=/g, '').replace(/[+\/]/g, function (m0) {
      return m0 == '+' ? '-' : '_';
    });
  };

  var _tidyB64 = function _tidyB64(s) {
    return s.replace(/[^A-Za-z0-9\+\/]/g, '');
  };
  /**
   * polyfill version of `btoa`
   */


  var btoaPolyfill = function btoaPolyfill(bin) {
    // console.log('polyfilled');
    var u32,
        c0,
        c1,
        c2,
        asc = '';
    var pad = bin.length % 3;

    for (var i = 0; i < bin.length;) {
      if ((c0 = bin.charCodeAt(i++)) > 255 || (c1 = bin.charCodeAt(i++)) > 255 || (c2 = bin.charCodeAt(i++)) > 255) throw new TypeError('invalid character found');
      u32 = c0 << 16 | c1 << 8 | c2;
      asc += b64chs[u32 >> 18 & 63] + b64chs[u32 >> 12 & 63] + b64chs[u32 >> 6 & 63] + b64chs[u32 & 63];
    }

    return pad ? asc.slice(0, pad - 3) + "===".substring(pad) : asc;
  };
  /**
   * does what `window.btoa` of web browsers do.
   * @param {String} bin binary string
   * @returns {string} Base64-encoded string
   */


  var _btoa = btoaPolyfill;

  var _fromUint8Array = function _fromUint8Array(u8a) {
    // cf. https://stackoverflow.com/questions/12710001/how-to-convert-uint8-array-to-base64-encoded-string/12713326#12713326
    var maxargs = 0x1000;
    var strs = [];

    for (var i = 0, l = u8a.length; i < l; i += maxargs) {
      strs.push(_fromCC.apply(null, u8a.subarray(i, i + maxargs)));
    }

    return _btoa(strs.join(''));
  };
  /**
   * converts a Uint8Array to a Base64 string.
   * @param {boolean} [urlsafe] URL-and-filename-safe a la RFC4648 §5
   * @returns {string} Base64 string
   */


  var fromUint8Array = function fromUint8Array(u8a, urlsafe) {
    if (urlsafe === void 0) {
      urlsafe = false;
    }

    return urlsafe ? _mkUriSafe(_fromUint8Array(u8a)) : _fromUint8Array(u8a);
  }; // This trick is found broken https://github.com/dankogai/js-base64/issues/130
  // const utob = (src: string) => unescape(encodeURIComponent(src));
  // reverting good old fationed regexp


  var cb_utob = function cb_utob(c) {
    if (c.length < 2) {
      var cc = c.charCodeAt(0);
      return cc < 0x80 ? c : cc < 0x800 ? _fromCC(0xc0 | cc >>> 6) + _fromCC(0x80 | cc & 0x3f) : _fromCC(0xe0 | cc >>> 12 & 0x0f) + _fromCC(0x80 | cc >>> 6 & 0x3f) + _fromCC(0x80 | cc & 0x3f);
    } else {
      var cc = 0x10000 + (c.charCodeAt(0) - 0xD800) * 0x400 + (c.charCodeAt(1) - 0xDC00);
      return _fromCC(0xf0 | cc >>> 18 & 0x07) + _fromCC(0x80 | cc >>> 12 & 0x3f) + _fromCC(0x80 | cc >>> 6 & 0x3f) + _fromCC(0x80 | cc & 0x3f);
    }
  };

  var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
  /**
   * @deprecated should have been internal use only.
   * @param {string} src UTF-8 string
   * @returns {string} UTF-16 string
   */

  var utob = function utob(u) {
    return u.replace(re_utob, cb_utob);
  }; //


  var _encode = _TE ? function (s) {
    return _fromUint8Array(_TE.encode(s));
  } : function (s) {
    return _btoa(utob(s));
  };
  /**
   * converts a UTF-8-encoded string to a Base64 string.
   * @param {boolean} [urlsafe] if `true` make the result URL-safe
   * @returns {string} Base64 string
   */


  var encode = function encode(src, urlsafe) {
    if (urlsafe === void 0) {
      urlsafe = false;
    }

    return urlsafe ? _mkUriSafe(_encode(src)) : _encode(src);
  };
  /**
   * converts a UTF-8-encoded string to URL-safe Base64 RFC4648 §5.
   * @returns {string} Base64 string
   */


  var encodeURI = function encodeURI(src) {
    return encode(src, true);
  }; // This trick is found broken https://github.com/dankogai/js-base64/issues/130
  // const btou = (src: string) => decodeURIComponent(escape(src));
  // reverting good old fationed regexp


  var re_btou = /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;

  var cb_btou = function cb_btou(cccc) {
    switch (cccc.length) {
      case 4:
        var cp = (0x07 & cccc.charCodeAt(0)) << 18 | (0x3f & cccc.charCodeAt(1)) << 12 | (0x3f & cccc.charCodeAt(2)) << 6 | 0x3f & cccc.charCodeAt(3),
            offset = cp - 0x10000;
        return _fromCC((offset >>> 10) + 0xD800) + _fromCC((offset & 0x3FF) + 0xDC00);

      case 3:
        return _fromCC((0x0f & cccc.charCodeAt(0)) << 12 | (0x3f & cccc.charCodeAt(1)) << 6 | 0x3f & cccc.charCodeAt(2));

      default:
        return _fromCC((0x1f & cccc.charCodeAt(0)) << 6 | 0x3f & cccc.charCodeAt(1));
    }
  };
  /**
   * @deprecated should have been internal use only.
   * @param {string} src UTF-16 string
   * @returns {string} UTF-8 string
   */


  var btou = function btou(b) {
    return b.replace(re_btou, cb_btou);
  };
  /**
   * polyfill version of `atob`
   */


  var atobPolyfill = function atobPolyfill(asc) {
    // console.log('polyfilled');
    asc = asc.replace(/\s+/g, '');
    if (!b64re.test(asc)) throw new TypeError('malformed base64.');
    asc += '=='.slice(2 - (asc.length & 3));
    var u24,
        bin = '',
        r1,
        r2;

    for (var i = 0; i < asc.length;) {
      u24 = b64tab[asc.charAt(i++)] << 18 | b64tab[asc.charAt(i++)] << 12 | (r1 = b64tab[asc.charAt(i++)]) << 6 | (r2 = b64tab[asc.charAt(i++)]);
      bin += r1 === 64 ? _fromCC(u24 >> 16 & 255) : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255) : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);
    }

    return bin;
  };
  /**
   * does what `window.atob` of web browsers do.
   * @param {String} asc Base64-encoded string
   * @returns {string} binary string
   */


  var _atob =
  /* typeof atob === 'function' ? function (asc) { return atob(_tidyB64(asc)); } :  */
  atobPolyfill; //

  var _toUint8Array = function _toUint8Array(a) {
    return _U8Afrom(_atob(a).split('').map(function (c) {
      return c.charCodeAt(0);
    }));
  };
  /**
   * converts a Base64 string to a Uint8Array.
   */


  var toUint8Array = function toUint8Array(a) {
    return _toUint8Array(_unURI(a));
  }; //


  var _decode = _TD ? function (a) {
    return _TD.decode(_toUint8Array(a));
  } : function (a) {
    return btou(_atob(a));
  };

  var _unURI = function _unURI(a) {
    return _tidyB64(a.replace(/[-_]/g, function (m0) {
      return m0 == '-' ? '+' : '/';
    }));
  };
  /**
   * converts a Base64 string to a UTF-8 string.
   * @param {String} src Base64 string.  Both normal and URL-safe are supported
   * @returns {string} UTF-8 string
   */


  var decode = function decode(src) {
    return _decode(_unURI(src));
  };
  /**
   * check if a value is a valid Base64 string
   * @param {String} src a value to check
    */


  var isValid = function isValid(src) {
    if (typeof src !== 'string') return false;
    var s = src.replace(/\s+/g, '').replace(/={0,2}$/, '');
    return !/[^\s0-9a-zA-Z\+/]/.test(s) || !/[^\s0-9a-zA-Z\-_]/.test(s);
  }; //


  var _noEnum = function _noEnum(v) {
    return {
      value: v,
      enumerable: false,
      writable: true,
      configurable: true
    };
  };
  /**
   * extend String.prototype with relevant methods
   */


  var extendString = function extendString() {
    var _add = function _add(name, body) {
      return Object.defineProperty(String.prototype, name, _noEnum(body));
    };

    _add('fromBase64', function () {
      return decode(this);
    });

    _add('toBase64', function (urlsafe) {
      return encode(this, urlsafe);
    });

    _add('toBase64URI', function () {
      return encode(this, true);
    });

    _add('toBase64URL', function () {
      return encode(this, true);
    });

    _add('toUint8Array', function () {
      return toUint8Array(this);
    });
  };
  /**
   * extend Uint8Array.prototype with relevant methods
   */


  var extendUint8Array = function extendUint8Array() {
    var _add = function _add(name, body) {
      return Object.defineProperty(Uint8Array.prototype, name, _noEnum(body));
    };

    _add('toBase64', function (urlsafe) {
      return fromUint8Array(this, urlsafe);
    });

    _add('toBase64URI', function () {
      return fromUint8Array(this, true);
    });

    _add('toBase64URL', function () {
      return fromUint8Array(this, true);
    });
  };
  /**
   * extend Builtin prototypes with relevant methods
   */


  var extendBuiltins = function extendBuiltins() {
    extendString();
    extendUint8Array();
  };

  var gBase64 = {
    version: version,
    VERSION: VERSION,
    atob: _atob,
    atobPolyfill: atobPolyfill,
    btoa: _btoa,
    btoaPolyfill: btoaPolyfill,
    fromBase64: decode,
    toBase64: encode,
    encode: encode,
    encodeURI: encodeURI,
    encodeURL: encodeURI,
    utob: utob,
    btou: btou,
    decode: decode,
    isValid: isValid,
    fromUint8Array: fromUint8Array,
    toUint8Array: toUint8Array,
    extendString: extendString,
    extendUint8Array: extendUint8Array,
    extendBuiltins: extendBuiltins
  }; //
  // export Base64 to the namespace
  //
  // ES5 is yet to have Object.assign() that may make transpilers unhappy.
  // gBase64.Base64 = Object.assign({}, gBase64);
  // gBase64.Base64 = {};
  // Object.keys(gBase64).forEach(function (k) { return gBase64.Base64[k] = gBase64[k]; });

  return gBase64;
});

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxsaWJcXGJhc2U2NC5qcyJdLCJuYW1lcyI6WyJ3aW5kb3ciLCJnbG9iYWwiLCJmYWN0b3J5IiwidmVyc2lvbiIsIlZFUlNJT04iLCJfVEQiLCJUZXh0RGVjb2RlciIsInVuZGVmaW5lZCIsIl9URSIsIlRleHRFbmNvZGVyIiwiYjY0Y2giLCJiNjRjaHMiLCJBcnJheSIsInByb3RvdHlwZSIsInNsaWNlIiwiY2FsbCIsImI2NHRhYiIsImEiLCJ0YWIiLCJmb3JFYWNoIiwiYyIsImkiLCJiNjRyZSIsIl9mcm9tQ0MiLCJTdHJpbmciLCJmcm9tQ2hhckNvZGUiLCJiaW5kIiwiX1U4QWZyb20iLCJVaW50OEFycmF5IiwiZnJvbSIsIml0IiwiX21rVXJpU2FmZSIsInNyYyIsInJlcGxhY2UiLCJtMCIsIl90aWR5QjY0IiwicyIsImJ0b2FQb2x5ZmlsbCIsImJpbiIsInUzMiIsImMwIiwiYzEiLCJjMiIsImFzYyIsInBhZCIsImxlbmd0aCIsImNoYXJDb2RlQXQiLCJUeXBlRXJyb3IiLCJzdWJzdHJpbmciLCJfYnRvYSIsIl9mcm9tVWludDhBcnJheSIsInU4YSIsIm1heGFyZ3MiLCJzdHJzIiwibCIsInB1c2giLCJhcHBseSIsInN1YmFycmF5Iiwiam9pbiIsImZyb21VaW50OEFycmF5IiwidXJsc2FmZSIsImNiX3V0b2IiLCJjYyIsInJlX3V0b2IiLCJ1dG9iIiwidSIsIl9lbmNvZGUiLCJlbmNvZGUiLCJlbmNvZGVVUkkiLCJyZV9idG91IiwiY2JfYnRvdSIsImNjY2MiLCJjcCIsIm9mZnNldCIsImJ0b3UiLCJiIiwiYXRvYlBvbHlmaWxsIiwidGVzdCIsInUyNCIsInIxIiwicjIiLCJjaGFyQXQiLCJfYXRvYiIsIl90b1VpbnQ4QXJyYXkiLCJzcGxpdCIsIm1hcCIsInRvVWludDhBcnJheSIsIl91blVSSSIsIl9kZWNvZGUiLCJkZWNvZGUiLCJpc1ZhbGlkIiwiX25vRW51bSIsInYiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJ3cml0YWJsZSIsImNvbmZpZ3VyYWJsZSIsImV4dGVuZFN0cmluZyIsIl9hZGQiLCJuYW1lIiwiYm9keSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXh0ZW5kVWludDhBcnJheSIsImV4dGVuZEJ1aWx0aW5zIiwiZ0Jhc2U2NCIsImF0b2IiLCJidG9hIiwiZnJvbUJhc2U2NCIsInRvQmFzZTY0IiwiZW5jb2RlVVJMIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0FBLE1BQU0sQ0FBQyxRQUFELENBQU4sR0FBb0IsVUFBVUMsTUFBVixFQUFrQkMsT0FBbEIsRUFBMkI7RUFDM0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0EsT0FBT0EsT0FBTyxFQUFkO0FBQ0gsQ0FwQm1CLENBb0JsQkYsTUFwQmtCLEVBb0JWLFlBQVk7RUFDbEI7O0VBQ0E7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNJLElBQUlHLE9BQU8sR0FBRyxPQUFkO0VBQ0E7QUFDSjtBQUNBOztFQUNJLElBQUlDLE9BQU8sR0FBR0QsT0FBZCxDQWpCa0IsQ0FrQmxCOztFQUNBLElBQUlFLEdBQUcsR0FBRyxPQUFPQyxXQUFQLEtBQXVCLFVBQXZCLEdBQW9DLElBQUlBLFdBQUosRUFBcEMsR0FBd0RDLFNBQWxFOztFQUNBLElBQUlDLEdBQUcsR0FBRyxPQUFPQyxXQUFQLEtBQXVCLFVBQXZCLEdBQW9DLElBQUlBLFdBQUosRUFBcEMsR0FBd0RGLFNBQWxFOztFQUNBLElBQUlHLEtBQUssR0FBRyxtRUFBWjtFQUNBLElBQUlDLE1BQU0sR0FBR0MsS0FBSyxDQUFDQyxTQUFOLENBQWdCQyxLQUFoQixDQUFzQkMsSUFBdEIsQ0FBMkJMLEtBQTNCLENBQWI7O0VBQ0EsSUFBSU0sTUFBTSxHQUFJLFVBQVVDLENBQVYsRUFBYTtJQUN2QixJQUFJQyxHQUFHLEdBQUcsRUFBVjtJQUNBRCxDQUFDLENBQUNFLE9BQUYsQ0FBVSxVQUFVQyxDQUFWLEVBQWFDLENBQWIsRUFBZ0I7TUFBRSxPQUFPSCxHQUFHLENBQUNFLENBQUQsQ0FBSCxHQUFTQyxDQUFoQjtJQUFvQixDQUFoRDtJQUNBLE9BQU9ILEdBQVA7RUFDSCxDQUpZLENBSVZQLE1BSlUsQ0FBYjs7RUFLQSxJQUFJVyxLQUFLLEdBQUcseUVBQVo7O0VBQ0EsSUFBSUMsT0FBTyxHQUFHQyxNQUFNLENBQUNDLFlBQVAsQ0FBb0JDLElBQXBCLENBQXlCRixNQUF6QixDQUFkOztFQUNBLElBQUlHLFFBQVEsR0FBRyxPQUFPQyxVQUFVLENBQUNDLElBQWxCLEtBQTJCLFVBQTNCLEdBQ1RELFVBQVUsQ0FBQ0MsSUFBWCxDQUFnQkgsSUFBaEIsQ0FBcUJFLFVBQXJCLENBRFMsR0FFVCxVQUFVRSxFQUFWLEVBQWM7SUFBRSxPQUFPLElBQUlGLFVBQUosQ0FBZWhCLEtBQUssQ0FBQ0MsU0FBTixDQUFnQkMsS0FBaEIsQ0FBc0JDLElBQXRCLENBQTJCZSxFQUEzQixFQUErQixDQUEvQixDQUFmLENBQVA7RUFBMkQsQ0FGakY7O0VBR0EsSUFBSUMsVUFBVSxHQUFHLFNBQWJBLFVBQWEsQ0FBVUMsR0FBVixFQUFlO0lBQzVCLE9BQU9BLEdBQUcsQ0FDTEMsT0FERSxDQUNNLElBRE4sRUFDWSxFQURaLEVBQ2dCQSxPQURoQixDQUN3QixRQUR4QixFQUNrQyxVQUFVQyxFQUFWLEVBQWM7TUFBRSxPQUFPQSxFQUFFLElBQUksR0FBTixHQUFZLEdBQVosR0FBa0IsR0FBekI7SUFBK0IsQ0FEakYsQ0FBUDtFQUVILENBSEQ7O0VBSUEsSUFBSUMsUUFBUSxHQUFHLFNBQVhBLFFBQVcsQ0FBVUMsQ0FBVixFQUFhO0lBQUUsT0FBT0EsQ0FBQyxDQUFDSCxPQUFGLENBQVUsbUJBQVYsRUFBK0IsRUFBL0IsQ0FBUDtFQUE0QyxDQUExRTtFQUNBO0FBQ0o7QUFDQTs7O0VBQ0ksSUFBSUksWUFBWSxHQUFHLFNBQWZBLFlBQWUsQ0FBVUMsR0FBVixFQUFlO0lBQzlCO0lBQ0EsSUFBSUMsR0FBSjtJQUFBLElBQVNDLEVBQVQ7SUFBQSxJQUFhQyxFQUFiO0lBQUEsSUFBaUJDLEVBQWpCO0lBQUEsSUFBcUJDLEdBQUcsR0FBRyxFQUEzQjtJQUNBLElBQUlDLEdBQUcsR0FBR04sR0FBRyxDQUFDTyxNQUFKLEdBQWEsQ0FBdkI7O0lBQ0EsS0FBSyxJQUFJeEIsQ0FBQyxHQUFHLENBQWIsRUFBZ0JBLENBQUMsR0FBR2lCLEdBQUcsQ0FBQ08sTUFBeEIsR0FBaUM7TUFDN0IsSUFBSSxDQUFDTCxFQUFFLEdBQUdGLEdBQUcsQ0FBQ1EsVUFBSixDQUFlekIsQ0FBQyxFQUFoQixDQUFOLElBQTZCLEdBQTdCLElBQ0EsQ0FBQ29CLEVBQUUsR0FBR0gsR0FBRyxDQUFDUSxVQUFKLENBQWV6QixDQUFDLEVBQWhCLENBQU4sSUFBNkIsR0FEN0IsSUFFQSxDQUFDcUIsRUFBRSxHQUFHSixHQUFHLENBQUNRLFVBQUosQ0FBZXpCLENBQUMsRUFBaEIsQ0FBTixJQUE2QixHQUZqQyxFQUdJLE1BQU0sSUFBSTBCLFNBQUosQ0FBYyx5QkFBZCxDQUFOO01BQ0pSLEdBQUcsR0FBSUMsRUFBRSxJQUFJLEVBQVAsR0FBY0MsRUFBRSxJQUFJLENBQXBCLEdBQXlCQyxFQUEvQjtNQUNBQyxHQUFHLElBQUloQyxNQUFNLENBQUM0QixHQUFHLElBQUksRUFBUCxHQUFZLEVBQWIsQ0FBTixHQUNENUIsTUFBTSxDQUFDNEIsR0FBRyxJQUFJLEVBQVAsR0FBWSxFQUFiLENBREwsR0FFRDVCLE1BQU0sQ0FBQzRCLEdBQUcsSUFBSSxDQUFQLEdBQVcsRUFBWixDQUZMLEdBR0Q1QixNQUFNLENBQUM0QixHQUFHLEdBQUcsRUFBUCxDQUhaO0lBSUg7O0lBQ0QsT0FBT0ssR0FBRyxHQUFHRCxHQUFHLENBQUM3QixLQUFKLENBQVUsQ0FBVixFQUFhOEIsR0FBRyxHQUFHLENBQW5CLElBQXdCLE1BQU1JLFNBQU4sQ0FBZ0JKLEdBQWhCLENBQTNCLEdBQWtERCxHQUE1RDtFQUNILENBaEJEO0VBaUJBO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7OztFQUNJLElBQUlNLEtBQUssR0FBR1osWUFBWjs7RUFDQSxJQUFJYSxlQUFlLEdBQUcsU0FBbEJBLGVBQWtCLENBQVVDLEdBQVYsRUFBZTtJQUNqQztJQUNBLElBQUlDLE9BQU8sR0FBRyxNQUFkO0lBQ0EsSUFBSUMsSUFBSSxHQUFHLEVBQVg7O0lBQ0EsS0FBSyxJQUFJaEMsQ0FBQyxHQUFHLENBQVIsRUFBV2lDLENBQUMsR0FBR0gsR0FBRyxDQUFDTixNQUF4QixFQUFnQ3hCLENBQUMsR0FBR2lDLENBQXBDLEVBQXVDakMsQ0FBQyxJQUFJK0IsT0FBNUMsRUFBcUQ7TUFDakRDLElBQUksQ0FBQ0UsSUFBTCxDQUFVaEMsT0FBTyxDQUFDaUMsS0FBUixDQUFjLElBQWQsRUFBb0JMLEdBQUcsQ0FBQ00sUUFBSixDQUFhcEMsQ0FBYixFQUFnQkEsQ0FBQyxHQUFHK0IsT0FBcEIsQ0FBcEIsQ0FBVjtJQUNIOztJQUNELE9BQU9ILEtBQUssQ0FBQ0ksSUFBSSxDQUFDSyxJQUFMLENBQVUsRUFBVixDQUFELENBQVo7RUFDSCxDQVJEO0VBU0E7QUFDSjtBQUNBO0FBQ0E7QUFDQTs7O0VBQ0ksSUFBSUMsY0FBYyxHQUFHLFNBQWpCQSxjQUFpQixDQUFVUixHQUFWLEVBQWVTLE9BQWYsRUFBd0I7SUFDekMsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBckIsRUFBd0I7TUFBRUEsT0FBTyxHQUFHLEtBQVY7SUFBa0I7O0lBQzVDLE9BQU9BLE9BQU8sR0FBRzdCLFVBQVUsQ0FBQ21CLGVBQWUsQ0FBQ0MsR0FBRCxDQUFoQixDQUFiLEdBQXNDRCxlQUFlLENBQUNDLEdBQUQsQ0FBbkU7RUFDSCxDQUhELENBOUVrQixDQWtGbEI7RUFDQTtFQUNBOzs7RUFDQSxJQUFJVSxPQUFPLEdBQUcsU0FBVkEsT0FBVSxDQUFVekMsQ0FBVixFQUFhO0lBQ3ZCLElBQUlBLENBQUMsQ0FBQ3lCLE1BQUYsR0FBVyxDQUFmLEVBQWtCO01BQ2QsSUFBSWlCLEVBQUUsR0FBRzFDLENBQUMsQ0FBQzBCLFVBQUYsQ0FBYSxDQUFiLENBQVQ7TUFDQSxPQUFPZ0IsRUFBRSxHQUFHLElBQUwsR0FBWTFDLENBQVosR0FDRDBDLEVBQUUsR0FBRyxLQUFMLEdBQWN2QyxPQUFPLENBQUMsT0FBUXVDLEVBQUUsS0FBSyxDQUFoQixDQUFQLEdBQ1Z2QyxPQUFPLENBQUMsT0FBUXVDLEVBQUUsR0FBRyxJQUFkLENBRFgsR0FFS3ZDLE9BQU8sQ0FBQyxPQUFTdUMsRUFBRSxLQUFLLEVBQVIsR0FBYyxJQUF2QixDQUFQLEdBQ0d2QyxPQUFPLENBQUMsT0FBU3VDLEVBQUUsS0FBSyxDQUFSLEdBQWEsSUFBdEIsQ0FEVixHQUVHdkMsT0FBTyxDQUFDLE9BQVF1QyxFQUFFLEdBQUcsSUFBZCxDQUxyQjtJQU1ILENBUkQsTUFTSztNQUNELElBQUlBLEVBQUUsR0FBRyxVQUNILENBQUMxQyxDQUFDLENBQUMwQixVQUFGLENBQWEsQ0FBYixJQUFrQixNQUFuQixJQUE2QixLQUQxQixJQUVGMUIsQ0FBQyxDQUFDMEIsVUFBRixDQUFhLENBQWIsSUFBa0IsTUFGaEIsQ0FBVDtNQUdBLE9BQVF2QixPQUFPLENBQUMsT0FBU3VDLEVBQUUsS0FBSyxFQUFSLEdBQWMsSUFBdkIsQ0FBUCxHQUNGdkMsT0FBTyxDQUFDLE9BQVN1QyxFQUFFLEtBQUssRUFBUixHQUFjLElBQXZCLENBREwsR0FFRnZDLE9BQU8sQ0FBQyxPQUFTdUMsRUFBRSxLQUFLLENBQVIsR0FBYSxJQUF0QixDQUZMLEdBR0Z2QyxPQUFPLENBQUMsT0FBUXVDLEVBQUUsR0FBRyxJQUFkLENBSGI7SUFJSDtFQUNKLENBbkJEOztFQW9CQSxJQUFJQyxPQUFPLEdBQUcsK0NBQWQ7RUFDQTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztFQUNJLElBQUlDLElBQUksR0FBRyxTQUFQQSxJQUFPLENBQVVDLENBQVYsRUFBYTtJQUFFLE9BQU9BLENBQUMsQ0FBQ2hDLE9BQUYsQ0FBVThCLE9BQVYsRUFBbUJGLE9BQW5CLENBQVA7RUFBcUMsQ0FBL0QsQ0EvR2tCLENBZ0hsQjs7O0VBQ0EsSUFBSUssT0FBTyxHQUFHMUQsR0FBRyxHQUFHLFVBQVU0QixDQUFWLEVBQWE7SUFBRSxPQUFPYyxlQUFlLENBQUMxQyxHQUFHLENBQUMyRCxNQUFKLENBQVcvQixDQUFYLENBQUQsQ0FBdEI7RUFBd0MsQ0FBMUQsR0FBNkQsVUFBVUEsQ0FBVixFQUFhO0lBQUUsT0FBT2EsS0FBSyxDQUFDZSxJQUFJLENBQUM1QixDQUFELENBQUwsQ0FBWjtFQUF3QixDQUFySDtFQUNBO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7OztFQUNJLElBQUkrQixNQUFNLEdBQUcsU0FBVEEsTUFBUyxDQUFVbkMsR0FBVixFQUFlNEIsT0FBZixFQUF3QjtJQUNqQyxJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFyQixFQUF3QjtNQUFFQSxPQUFPLEdBQUcsS0FBVjtJQUFrQjs7SUFDNUMsT0FBT0EsT0FBTyxHQUNSN0IsVUFBVSxDQUFDbUMsT0FBTyxDQUFDbEMsR0FBRCxDQUFSLENBREYsR0FFUmtDLE9BQU8sQ0FBQ2xDLEdBQUQsQ0FGYjtFQUdILENBTEQ7RUFNQTtBQUNKO0FBQ0E7QUFDQTs7O0VBQ0ksSUFBSW9DLFNBQVMsR0FBRyxTQUFaQSxTQUFZLENBQVVwQyxHQUFWLEVBQWU7SUFBRSxPQUFPbUMsTUFBTSxDQUFDbkMsR0FBRCxFQUFNLElBQU4sQ0FBYjtFQUEyQixDQUE1RCxDQWpJa0IsQ0FrSWxCO0VBQ0E7RUFDQTs7O0VBQ0EsSUFBSXFDLE9BQU8sR0FBRyw2RUFBZDs7RUFDQSxJQUFJQyxPQUFPLEdBQUcsU0FBVkEsT0FBVSxDQUFVQyxJQUFWLEVBQWdCO0lBQzFCLFFBQVFBLElBQUksQ0FBQzFCLE1BQWI7TUFDSSxLQUFLLENBQUw7UUFDSSxJQUFJMkIsRUFBRSxHQUFJLENBQUMsT0FBT0QsSUFBSSxDQUFDekIsVUFBTCxDQUFnQixDQUFoQixDQUFSLEtBQStCLEVBQWhDLEdBQ0YsQ0FBQyxPQUFPeUIsSUFBSSxDQUFDekIsVUFBTCxDQUFnQixDQUFoQixDQUFSLEtBQStCLEVBRDdCLEdBRUYsQ0FBQyxPQUFPeUIsSUFBSSxDQUFDekIsVUFBTCxDQUFnQixDQUFoQixDQUFSLEtBQStCLENBRjdCLEdBR0YsT0FBT3lCLElBQUksQ0FBQ3pCLFVBQUwsQ0FBZ0IsQ0FBaEIsQ0FIZDtRQUFBLElBR21DMkIsTUFBTSxHQUFHRCxFQUFFLEdBQUcsT0FIakQ7UUFJQSxPQUFRakQsT0FBTyxDQUFDLENBQUNrRCxNQUFNLEtBQUssRUFBWixJQUFrQixNQUFuQixDQUFQLEdBQ0ZsRCxPQUFPLENBQUMsQ0FBQ2tELE1BQU0sR0FBRyxLQUFWLElBQW1CLE1BQXBCLENBRGI7O01BRUosS0FBSyxDQUFMO1FBQ0ksT0FBT2xELE9BQU8sQ0FBRSxDQUFDLE9BQU9nRCxJQUFJLENBQUN6QixVQUFMLENBQWdCLENBQWhCLENBQVIsS0FBK0IsRUFBaEMsR0FDUixDQUFDLE9BQU95QixJQUFJLENBQUN6QixVQUFMLENBQWdCLENBQWhCLENBQVIsS0FBK0IsQ0FEdkIsR0FFUixPQUFPeUIsSUFBSSxDQUFDekIsVUFBTCxDQUFnQixDQUFoQixDQUZBLENBQWQ7O01BR0o7UUFDSSxPQUFPdkIsT0FBTyxDQUFFLENBQUMsT0FBT2dELElBQUksQ0FBQ3pCLFVBQUwsQ0FBZ0IsQ0FBaEIsQ0FBUixLQUErQixDQUFoQyxHQUNSLE9BQU95QixJQUFJLENBQUN6QixVQUFMLENBQWdCLENBQWhCLENBREEsQ0FBZDtJQWJSO0VBZ0JILENBakJEO0VBa0JBO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7OztFQUNJLElBQUk0QixJQUFJLEdBQUcsU0FBUEEsSUFBTyxDQUFVQyxDQUFWLEVBQWE7SUFBRSxPQUFPQSxDQUFDLENBQUMxQyxPQUFGLENBQVVvQyxPQUFWLEVBQW1CQyxPQUFuQixDQUFQO0VBQXFDLENBQS9EO0VBQ0E7QUFDSjtBQUNBOzs7RUFDSSxJQUFJTSxZQUFZLEdBQUcsU0FBZkEsWUFBZSxDQUFVakMsR0FBVixFQUFlO0lBQzlCO0lBQ0FBLEdBQUcsR0FBR0EsR0FBRyxDQUFDVixPQUFKLENBQVksTUFBWixFQUFvQixFQUFwQixDQUFOO0lBQ0EsSUFBSSxDQUFDWCxLQUFLLENBQUN1RCxJQUFOLENBQVdsQyxHQUFYLENBQUwsRUFDSSxNQUFNLElBQUlJLFNBQUosQ0FBYyxtQkFBZCxDQUFOO0lBQ0pKLEdBQUcsSUFBSSxLQUFLN0IsS0FBTCxDQUFXLEtBQUs2QixHQUFHLENBQUNFLE1BQUosR0FBYSxDQUFsQixDQUFYLENBQVA7SUFDQSxJQUFJaUMsR0FBSjtJQUFBLElBQVN4QyxHQUFHLEdBQUcsRUFBZjtJQUFBLElBQW1CeUMsRUFBbkI7SUFBQSxJQUF1QkMsRUFBdkI7O0lBQ0EsS0FBSyxJQUFJM0QsQ0FBQyxHQUFHLENBQWIsRUFBZ0JBLENBQUMsR0FBR3NCLEdBQUcsQ0FBQ0UsTUFBeEIsR0FBaUM7TUFDN0JpQyxHQUFHLEdBQUc5RCxNQUFNLENBQUMyQixHQUFHLENBQUNzQyxNQUFKLENBQVc1RCxDQUFDLEVBQVosQ0FBRCxDQUFOLElBQTJCLEVBQTNCLEdBQ0FMLE1BQU0sQ0FBQzJCLEdBQUcsQ0FBQ3NDLE1BQUosQ0FBVzVELENBQUMsRUFBWixDQUFELENBQU4sSUFBMkIsRUFEM0IsR0FFQSxDQUFDMEQsRUFBRSxHQUFHL0QsTUFBTSxDQUFDMkIsR0FBRyxDQUFDc0MsTUFBSixDQUFXNUQsQ0FBQyxFQUFaLENBQUQsQ0FBWixLQUFrQyxDQUZsQyxJQUdDMkQsRUFBRSxHQUFHaEUsTUFBTSxDQUFDMkIsR0FBRyxDQUFDc0MsTUFBSixDQUFXNUQsQ0FBQyxFQUFaLENBQUQsQ0FIWixDQUFOO01BSUFpQixHQUFHLElBQUl5QyxFQUFFLEtBQUssRUFBUCxHQUFZeEQsT0FBTyxDQUFDdUQsR0FBRyxJQUFJLEVBQVAsR0FBWSxHQUFiLENBQW5CLEdBQ0RFLEVBQUUsS0FBSyxFQUFQLEdBQVl6RCxPQUFPLENBQUN1RCxHQUFHLElBQUksRUFBUCxHQUFZLEdBQWIsRUFBa0JBLEdBQUcsSUFBSSxDQUFQLEdBQVcsR0FBN0IsQ0FBbkIsR0FDSXZELE9BQU8sQ0FBQ3VELEdBQUcsSUFBSSxFQUFQLEdBQVksR0FBYixFQUFrQkEsR0FBRyxJQUFJLENBQVAsR0FBVyxHQUE3QixFQUFrQ0EsR0FBRyxHQUFHLEdBQXhDLENBRmpCO0lBR0g7O0lBQ0QsT0FBT3hDLEdBQVA7RUFDSCxDQWpCRDtFQWtCQTtBQUNKO0FBQ0E7QUFDQTtBQUNBOzs7RUFDSSxJQUFJNEMsS0FBSztFQUFHO0VBQW9GTixZQUFoRyxDQXhMa0IsQ0F5TGxCOztFQUNBLElBQUlPLGFBQWEsR0FBRyxTQUFoQkEsYUFBZ0IsQ0FBVWxFLENBQVYsRUFBYTtJQUFFLE9BQU9VLFFBQVEsQ0FBQ3VELEtBQUssQ0FBQ2pFLENBQUQsQ0FBTCxDQUFTbUUsS0FBVCxDQUFlLEVBQWYsRUFBbUJDLEdBQW5CLENBQXVCLFVBQVVqRSxDQUFWLEVBQWE7TUFBRSxPQUFPQSxDQUFDLENBQUMwQixVQUFGLENBQWEsQ0FBYixDQUFQO0lBQXlCLENBQS9ELENBQUQsQ0FBZjtFQUFvRixDQUF2SDtFQUNBO0FBQ0o7QUFDQTs7O0VBQ0ksSUFBSXdDLFlBQVksR0FBRyxTQUFmQSxZQUFlLENBQVVyRSxDQUFWLEVBQWE7SUFBRSxPQUFPa0UsYUFBYSxDQUFDSSxNQUFNLENBQUN0RSxDQUFELENBQVAsQ0FBcEI7RUFBa0MsQ0FBcEUsQ0E5TGtCLENBK0xsQjs7O0VBQ0EsSUFBSXVFLE9BQU8sR0FBR25GLEdBQUcsR0FBRyxVQUFVWSxDQUFWLEVBQWE7SUFBRSxPQUFPWixHQUFHLENBQUNvRixNQUFKLENBQVdOLGFBQWEsQ0FBQ2xFLENBQUQsQ0FBeEIsQ0FBUDtFQUFzQyxDQUF4RCxHQUEyRCxVQUFVQSxDQUFWLEVBQWE7SUFBRSxPQUFPeUQsSUFBSSxDQUFDUSxLQUFLLENBQUNqRSxDQUFELENBQU4sQ0FBWDtFQUF3QixDQUFuSDs7RUFDQSxJQUFJc0UsTUFBTSxHQUFHLFNBQVRBLE1BQVMsQ0FBVXRFLENBQVYsRUFBYTtJQUFFLE9BQU9rQixRQUFRLENBQUNsQixDQUFDLENBQUNnQixPQUFGLENBQVUsT0FBVixFQUFtQixVQUFVQyxFQUFWLEVBQWM7TUFBRSxPQUFPQSxFQUFFLElBQUksR0FBTixHQUFZLEdBQVosR0FBa0IsR0FBekI7SUFBK0IsQ0FBbEUsQ0FBRCxDQUFmO0VBQXVGLENBQW5IO0VBQ0E7QUFDSjtBQUNBO0FBQ0E7QUFDQTs7O0VBQ0ksSUFBSXVELE1BQU0sR0FBRyxTQUFUQSxNQUFTLENBQVV6RCxHQUFWLEVBQWU7SUFBRSxPQUFPd0QsT0FBTyxDQUFDRCxNQUFNLENBQUN2RCxHQUFELENBQVAsQ0FBZDtFQUE4QixDQUE1RDtFQUNBO0FBQ0o7QUFDQTtBQUNBOzs7RUFDSSxJQUFJMEQsT0FBTyxHQUFHLFNBQVZBLE9BQVUsQ0FBVTFELEdBQVYsRUFBZTtJQUN6QixJQUFJLE9BQU9BLEdBQVAsS0FBZSxRQUFuQixFQUNJLE9BQU8sS0FBUDtJQUNKLElBQUlJLENBQUMsR0FBR0osR0FBRyxDQUFDQyxPQUFKLENBQVksTUFBWixFQUFvQixFQUFwQixFQUF3QkEsT0FBeEIsQ0FBZ0MsU0FBaEMsRUFBMkMsRUFBM0MsQ0FBUjtJQUNBLE9BQU8sQ0FBQyxvQkFBb0I0QyxJQUFwQixDQUF5QnpDLENBQXpCLENBQUQsSUFBZ0MsQ0FBQyxvQkFBb0J5QyxJQUFwQixDQUF5QnpDLENBQXpCLENBQXhDO0VBQ0gsQ0FMRCxDQTVNa0IsQ0FrTmxCOzs7RUFDQSxJQUFJdUQsT0FBTyxHQUFHLFNBQVZBLE9BQVUsQ0FBVUMsQ0FBVixFQUFhO0lBQ3ZCLE9BQU87TUFDSEMsS0FBSyxFQUFFRCxDQURKO01BQ09FLFVBQVUsRUFBRSxLQURuQjtNQUMwQkMsUUFBUSxFQUFFLElBRHBDO01BQzBDQyxZQUFZLEVBQUU7SUFEeEQsQ0FBUDtFQUdILENBSkQ7RUFLQTtBQUNKO0FBQ0E7OztFQUNJLElBQUlDLFlBQVksR0FBRyxTQUFmQSxZQUFlLEdBQVk7SUFDM0IsSUFBSUMsSUFBSSxHQUFHLFNBQVBBLElBQU8sQ0FBVUMsSUFBVixFQUFnQkMsSUFBaEIsRUFBc0I7TUFBRSxPQUFPQyxNQUFNLENBQUNDLGNBQVAsQ0FBc0I5RSxNQUFNLENBQUNYLFNBQTdCLEVBQXdDc0YsSUFBeEMsRUFBOENSLE9BQU8sQ0FBQ1MsSUFBRCxDQUFyRCxDQUFQO0lBQXNFLENBQXpHOztJQUNBRixJQUFJLENBQUMsWUFBRCxFQUFlLFlBQVk7TUFBRSxPQUFPVCxNQUFNLENBQUMsSUFBRCxDQUFiO0lBQXNCLENBQW5ELENBQUo7O0lBQ0FTLElBQUksQ0FBQyxVQUFELEVBQWEsVUFBVXRDLE9BQVYsRUFBbUI7TUFBRSxPQUFPTyxNQUFNLENBQUMsSUFBRCxFQUFPUCxPQUFQLENBQWI7SUFBK0IsQ0FBakUsQ0FBSjs7SUFDQXNDLElBQUksQ0FBQyxhQUFELEVBQWdCLFlBQVk7TUFBRSxPQUFPL0IsTUFBTSxDQUFDLElBQUQsRUFBTyxJQUFQLENBQWI7SUFBNEIsQ0FBMUQsQ0FBSjs7SUFDQStCLElBQUksQ0FBQyxhQUFELEVBQWdCLFlBQVk7TUFBRSxPQUFPL0IsTUFBTSxDQUFDLElBQUQsRUFBTyxJQUFQLENBQWI7SUFBNEIsQ0FBMUQsQ0FBSjs7SUFDQStCLElBQUksQ0FBQyxjQUFELEVBQWlCLFlBQVk7TUFBRSxPQUFPWixZQUFZLENBQUMsSUFBRCxDQUFuQjtJQUE0QixDQUEzRCxDQUFKO0VBQ0gsQ0FQRDtFQVFBO0FBQ0o7QUFDQTs7O0VBQ0ksSUFBSWlCLGdCQUFnQixHQUFHLFNBQW5CQSxnQkFBbUIsR0FBWTtJQUMvQixJQUFJTCxJQUFJLEdBQUcsU0FBUEEsSUFBTyxDQUFVQyxJQUFWLEVBQWdCQyxJQUFoQixFQUFzQjtNQUFFLE9BQU9DLE1BQU0sQ0FBQ0MsY0FBUCxDQUFzQjFFLFVBQVUsQ0FBQ2YsU0FBakMsRUFBNENzRixJQUE1QyxFQUFrRFIsT0FBTyxDQUFDUyxJQUFELENBQXpELENBQVA7SUFBMEUsQ0FBN0c7O0lBQ0FGLElBQUksQ0FBQyxVQUFELEVBQWEsVUFBVXRDLE9BQVYsRUFBbUI7TUFBRSxPQUFPRCxjQUFjLENBQUMsSUFBRCxFQUFPQyxPQUFQLENBQXJCO0lBQXVDLENBQXpFLENBQUo7O0lBQ0FzQyxJQUFJLENBQUMsYUFBRCxFQUFnQixZQUFZO01BQUUsT0FBT3ZDLGNBQWMsQ0FBQyxJQUFELEVBQU8sSUFBUCxDQUFyQjtJQUFvQyxDQUFsRSxDQUFKOztJQUNBdUMsSUFBSSxDQUFDLGFBQUQsRUFBZ0IsWUFBWTtNQUFFLE9BQU92QyxjQUFjLENBQUMsSUFBRCxFQUFPLElBQVAsQ0FBckI7SUFBb0MsQ0FBbEUsQ0FBSjtFQUNILENBTEQ7RUFNQTtBQUNKO0FBQ0E7OztFQUNJLElBQUk2QyxjQUFjLEdBQUcsU0FBakJBLGNBQWlCLEdBQVk7SUFDN0JQLFlBQVk7SUFDWk0sZ0JBQWdCO0VBQ25CLENBSEQ7O0VBSUEsSUFBSUUsT0FBTyxHQUFHO0lBQ1Z0RyxPQUFPLEVBQUVBLE9BREM7SUFFVkMsT0FBTyxFQUFFQSxPQUZDO0lBR1ZzRyxJQUFJLEVBQUV4QixLQUhJO0lBSVZOLFlBQVksRUFBRUEsWUFKSjtJQUtWK0IsSUFBSSxFQUFFMUQsS0FMSTtJQU1WWixZQUFZLEVBQUVBLFlBTko7SUFPVnVFLFVBQVUsRUFBRW5CLE1BUEY7SUFRVm9CLFFBQVEsRUFBRTFDLE1BUkE7SUFTVkEsTUFBTSxFQUFFQSxNQVRFO0lBVVZDLFNBQVMsRUFBRUEsU0FWRDtJQVdWMEMsU0FBUyxFQUFFMUMsU0FYRDtJQVlWSixJQUFJLEVBQUVBLElBWkk7SUFhVlUsSUFBSSxFQUFFQSxJQWJJO0lBY1ZlLE1BQU0sRUFBRUEsTUFkRTtJQWVWQyxPQUFPLEVBQUVBLE9BZkM7SUFnQlYvQixjQUFjLEVBQUVBLGNBaEJOO0lBaUJWMkIsWUFBWSxFQUFFQSxZQWpCSjtJQWtCVlcsWUFBWSxFQUFFQSxZQWxCSjtJQW1CVk0sZ0JBQWdCLEVBQUVBLGdCQW5CUjtJQW9CVkMsY0FBYyxFQUFFQTtFQXBCTixDQUFkLENBblBrQixDQXlRbEI7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7O0VBQ0EsT0FBT0MsT0FBUDtBQUNILENBclNtQixDQUFwQiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy9cclxuLy8gVEhJUyBGSUxFIElTIEFVVE9NQVRJQ0FMTFkgR0VORVJBVEVEISBETyBOT1QgRURJVCBCWSBIQU5EIVxyXG4vL1xyXG4vLyB2YXIgQmFzZTY0ID0gd2luZG93WydCYXNlNjQnXSA9IHt9O1xyXG53aW5kb3dbJ0Jhc2U2NCddID0gKGZ1bmN0aW9uIChnbG9iYWwsIGZhY3RvcnkpIHtcclxuICAgIC8vIHR5cGVvZiBleHBvcnRzID09PSAnb2JqZWN0JyAmJiB0eXBlb2YgbW9kdWxlICE9PSAndW5kZWZpbmVkJ1xyXG4gICAgLy8gICAgID8gbW9kdWxlLmV4cG9ydHMgPSBmYWN0b3J5KClcclxuICAgIC8vICAgICA6IHR5cGVvZiBkZWZpbmUgPT09ICdmdW5jdGlvbicgJiYgZGVmaW5lLmFtZFxyXG4gICAgLy8gICAgICAgICA/IGRlZmluZShmYWN0b3J5KSA6XHJcbiAgICAvLyAgICAgICAgIC8vIGNmLiBodHRwczovL2dpdGh1Yi5jb20vZGFua29nYWkvanMtYmFzZTY0L2lzc3Vlcy8xMTlcclxuICAgIC8vICAgICAgICAgKGZ1bmN0aW9uICgpIHtcclxuICAgIC8vICAgICAgICAgICAgIC8vIGV4aXN0aW5nIHZlcnNpb24gZm9yIG5vQ29uZmxpY3QoKVxyXG4gICAgLy8gICAgICAgICAgICAgdmFyIF9CYXNlNjQgPSBnbG9iYWwuQmFzZTY0O1xyXG4gICAgLy8gICAgICAgICAgICAgdmFyIGdCYXNlNjQgPSBmYWN0b3J5KCk7XHJcbiAgICAvLyAgICAgICAgICAgICBnQmFzZTY0Lm5vQ29uZmxpY3QgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgZ2xvYmFsLkJhc2U2NCA9IF9CYXNlNjQ7XHJcbiAgICAvLyAgICAgICAgICAgICAgICAgcmV0dXJuIGdCYXNlNjQ7XHJcbiAgICAvLyAgICAgICAgICAgICB9O1xyXG4gICAgLy8gICAgICAgICAgICAgaWYgKGdsb2JhbC5NZXRlb3IpIHsgLy8gTWV0ZW9yLmpzXHJcbiAgICAvLyAgICAgICAgICAgICAgICAgQmFzZTY0ID0gZ0Jhc2U2NDtcclxuICAgIC8vICAgICAgICAgICAgIH1cclxuICAgIC8vICAgICAgICAgICAgIGdsb2JhbC5CYXNlNjQgPSBnQmFzZTY0O1xyXG4gICAgLy8gICAgICAgICB9KSgpO1xyXG4gICAgcmV0dXJuIGZhY3RvcnkoKVxyXG59KHdpbmRvdywgZnVuY3Rpb24gKCkge1xyXG4gICAgLy8gJ3VzZSBzdHJpY3QnO1xyXG4gICAgLyoqXHJcbiAgICAgKiAgYmFzZTY0LnRzXHJcbiAgICAgKlxyXG4gICAgICogIExpY2Vuc2VkIHVuZGVyIHRoZSBCU0QgMy1DbGF1c2UgTGljZW5zZS5cclxuICAgICAqICAgIGh0dHA6Ly9vcGVuc291cmNlLm9yZy9saWNlbnNlcy9CU0QtMy1DbGF1c2VcclxuICAgICAqXHJcbiAgICAgKiAgUmVmZXJlbmNlczpcclxuICAgICAqICAgIGh0dHA6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvQmFzZTY0XHJcbiAgICAgKlxyXG4gICAgICogQGF1dGhvciBEYW4gS29nYWkgKGh0dHBzOi8vZ2l0aHViLmNvbS9kYW5rb2dhaSlcclxuICAgICAqL1xyXG4gICAgdmFyIHZlcnNpb24gPSAnMy43LjcnO1xyXG4gICAgLyoqXHJcbiAgICAgKiBAZGVwcmVjYXRlZCB1c2UgbG93ZXJjYXNlIGB2ZXJzaW9uYC5cclxuICAgICAqL1xyXG4gICAgdmFyIFZFUlNJT04gPSB2ZXJzaW9uO1xyXG4gICAgLy8gdmFyIF9oYXNCdWZmZXIgPSB0eXBlb2YgQnVmZmVyID09PSAnZnVuY3Rpb24nO1xyXG4gICAgdmFyIF9URCA9IHR5cGVvZiBUZXh0RGVjb2RlciA9PT0gJ2Z1bmN0aW9uJyA/IG5ldyBUZXh0RGVjb2RlcigpIDogdW5kZWZpbmVkO1xyXG4gICAgdmFyIF9URSA9IHR5cGVvZiBUZXh0RW5jb2RlciA9PT0gJ2Z1bmN0aW9uJyA/IG5ldyBUZXh0RW5jb2RlcigpIDogdW5kZWZpbmVkO1xyXG4gICAgdmFyIGI2NGNoID0gJ0FCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5Ky89JztcclxuICAgIHZhciBiNjRjaHMgPSBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChiNjRjaCk7XHJcbiAgICB2YXIgYjY0dGFiID0gKGZ1bmN0aW9uIChhKSB7XHJcbiAgICAgICAgdmFyIHRhYiA9IHt9O1xyXG4gICAgICAgIGEuZm9yRWFjaChmdW5jdGlvbiAoYywgaSkgeyByZXR1cm4gdGFiW2NdID0gaTsgfSk7XHJcbiAgICAgICAgcmV0dXJuIHRhYjtcclxuICAgIH0pKGI2NGNocyk7XHJcbiAgICB2YXIgYjY0cmUgPSAvXig/OltBLVphLXpcXGQrXFwvXXs0fSkqPyg/OltBLVphLXpcXGQrXFwvXXsyfSg/Oj09KT98W0EtWmEtelxcZCtcXC9dezN9PT8pPyQvO1xyXG4gICAgdmFyIF9mcm9tQ0MgPSBTdHJpbmcuZnJvbUNoYXJDb2RlLmJpbmQoU3RyaW5nKTtcclxuICAgIHZhciBfVThBZnJvbSA9IHR5cGVvZiBVaW50OEFycmF5LmZyb20gPT09ICdmdW5jdGlvbidcclxuICAgICAgICA/IFVpbnQ4QXJyYXkuZnJvbS5iaW5kKFVpbnQ4QXJyYXkpXHJcbiAgICAgICAgOiBmdW5jdGlvbiAoaXQpIHsgcmV0dXJuIG5ldyBVaW50OEFycmF5KEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGl0LCAwKSk7IH07XHJcbiAgICB2YXIgX21rVXJpU2FmZSA9IGZ1bmN0aW9uIChzcmMpIHtcclxuICAgICAgICByZXR1cm4gc3JjXHJcbiAgICAgICAgICAgIC5yZXBsYWNlKC89L2csICcnKS5yZXBsYWNlKC9bK1xcL10vZywgZnVuY3Rpb24gKG0wKSB7IHJldHVybiBtMCA9PSAnKycgPyAnLScgOiAnXyc7IH0pO1xyXG4gICAgfTtcclxuICAgIHZhciBfdGlkeUI2NCA9IGZ1bmN0aW9uIChzKSB7IHJldHVybiBzLnJlcGxhY2UoL1teQS1aYS16MC05XFwrXFwvXS9nLCAnJyk7IH07XHJcbiAgICAvKipcclxuICAgICAqIHBvbHlmaWxsIHZlcnNpb24gb2YgYGJ0b2FgXHJcbiAgICAgKi9cclxuICAgIHZhciBidG9hUG9seWZpbGwgPSBmdW5jdGlvbiAoYmluKSB7XHJcbiAgICAgICAgLy8gY29uc29sZS5sb2coJ3BvbHlmaWxsZWQnKTtcclxuICAgICAgICB2YXIgdTMyLCBjMCwgYzEsIGMyLCBhc2MgPSAnJztcclxuICAgICAgICB2YXIgcGFkID0gYmluLmxlbmd0aCAlIDM7XHJcbiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBiaW4ubGVuZ3RoOykge1xyXG4gICAgICAgICAgICBpZiAoKGMwID0gYmluLmNoYXJDb2RlQXQoaSsrKSkgPiAyNTUgfHxcclxuICAgICAgICAgICAgICAgIChjMSA9IGJpbi5jaGFyQ29kZUF0KGkrKykpID4gMjU1IHx8XHJcbiAgICAgICAgICAgICAgICAoYzIgPSBiaW4uY2hhckNvZGVBdChpKyspKSA+IDI1NSlcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2ludmFsaWQgY2hhcmFjdGVyIGZvdW5kJyk7XHJcbiAgICAgICAgICAgIHUzMiA9IChjMCA8PCAxNikgfCAoYzEgPDwgOCkgfCBjMjtcclxuICAgICAgICAgICAgYXNjICs9IGI2NGNoc1t1MzIgPj4gMTggJiA2M11cclxuICAgICAgICAgICAgICAgICsgYjY0Y2hzW3UzMiA+PiAxMiAmIDYzXVxyXG4gICAgICAgICAgICAgICAgKyBiNjRjaHNbdTMyID4+IDYgJiA2M11cclxuICAgICAgICAgICAgICAgICsgYjY0Y2hzW3UzMiAmIDYzXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHBhZCA/IGFzYy5zbGljZSgwLCBwYWQgLSAzKSArIFwiPT09XCIuc3Vic3RyaW5nKHBhZCkgOiBhc2M7XHJcbiAgICB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBkb2VzIHdoYXQgYHdpbmRvdy5idG9hYCBvZiB3ZWIgYnJvd3NlcnMgZG8uXHJcbiAgICAgKiBAcGFyYW0ge1N0cmluZ30gYmluIGJpbmFyeSBzdHJpbmdcclxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IEJhc2U2NC1lbmNvZGVkIHN0cmluZ1xyXG4gICAgICovXHJcbiAgICB2YXIgX2J0b2EgPSBidG9hUG9seWZpbGw7XHJcbiAgICB2YXIgX2Zyb21VaW50OEFycmF5ID0gZnVuY3Rpb24gKHU4YSkge1xyXG4gICAgICAgIC8vIGNmLiBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy8xMjcxMDAwMS9ob3ctdG8tY29udmVydC11aW50OC1hcnJheS10by1iYXNlNjQtZW5jb2RlZC1zdHJpbmcvMTI3MTMzMjYjMTI3MTMzMjZcclxuICAgICAgICB2YXIgbWF4YXJncyA9IDB4MTAwMDtcclxuICAgICAgICB2YXIgc3RycyA9IFtdO1xyXG4gICAgICAgIGZvciAodmFyIGkgPSAwLCBsID0gdThhLmxlbmd0aDsgaSA8IGw7IGkgKz0gbWF4YXJncykge1xyXG4gICAgICAgICAgICBzdHJzLnB1c2goX2Zyb21DQy5hcHBseShudWxsLCB1OGEuc3ViYXJyYXkoaSwgaSArIG1heGFyZ3MpKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBfYnRvYShzdHJzLmpvaW4oJycpKTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIGNvbnZlcnRzIGEgVWludDhBcnJheSB0byBhIEJhc2U2NCBzdHJpbmcuXHJcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW59IFt1cmxzYWZlXSBVUkwtYW5kLWZpbGVuYW1lLXNhZmUgYSBsYSBSRkM0NjQ4IMKnNVxyXG4gICAgICogQHJldHVybnMge3N0cmluZ30gQmFzZTY0IHN0cmluZ1xyXG4gICAgICovXHJcbiAgICB2YXIgZnJvbVVpbnQ4QXJyYXkgPSBmdW5jdGlvbiAodThhLCB1cmxzYWZlKSB7XHJcbiAgICAgICAgaWYgKHVybHNhZmUgPT09IHZvaWQgMCkgeyB1cmxzYWZlID0gZmFsc2U7IH1cclxuICAgICAgICByZXR1cm4gdXJsc2FmZSA/IF9ta1VyaVNhZmUoX2Zyb21VaW50OEFycmF5KHU4YSkpIDogX2Zyb21VaW50OEFycmF5KHU4YSk7XHJcbiAgICB9O1xyXG4gICAgLy8gVGhpcyB0cmljayBpcyBmb3VuZCBicm9rZW4gaHR0cHM6Ly9naXRodWIuY29tL2RhbmtvZ2FpL2pzLWJhc2U2NC9pc3N1ZXMvMTMwXHJcbiAgICAvLyBjb25zdCB1dG9iID0gKHNyYzogc3RyaW5nKSA9PiB1bmVzY2FwZShlbmNvZGVVUklDb21wb25lbnQoc3JjKSk7XHJcbiAgICAvLyByZXZlcnRpbmcgZ29vZCBvbGQgZmF0aW9uZWQgcmVnZXhwXHJcbiAgICB2YXIgY2JfdXRvYiA9IGZ1bmN0aW9uIChjKSB7XHJcbiAgICAgICAgaWYgKGMubGVuZ3RoIDwgMikge1xyXG4gICAgICAgICAgICB2YXIgY2MgPSBjLmNoYXJDb2RlQXQoMCk7XHJcbiAgICAgICAgICAgIHJldHVybiBjYyA8IDB4ODAgPyBjXHJcbiAgICAgICAgICAgICAgICA6IGNjIDwgMHg4MDAgPyAoX2Zyb21DQygweGMwIHwgKGNjID4+PiA2KSlcclxuICAgICAgICAgICAgICAgICAgICArIF9mcm9tQ0MoMHg4MCB8IChjYyAmIDB4M2YpKSlcclxuICAgICAgICAgICAgICAgICAgICA6IChfZnJvbUNDKDB4ZTAgfCAoKGNjID4+PiAxMikgJiAweDBmKSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgKyBfZnJvbUNDKDB4ODAgfCAoKGNjID4+PiA2KSAmIDB4M2YpKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICArIF9mcm9tQ0MoMHg4MCB8IChjYyAmIDB4M2YpKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICB2YXIgY2MgPSAweDEwMDAwXHJcbiAgICAgICAgICAgICAgICArIChjLmNoYXJDb2RlQXQoMCkgLSAweEQ4MDApICogMHg0MDBcclxuICAgICAgICAgICAgICAgICsgKGMuY2hhckNvZGVBdCgxKSAtIDB4REMwMCk7XHJcbiAgICAgICAgICAgIHJldHVybiAoX2Zyb21DQygweGYwIHwgKChjYyA+Pj4gMTgpICYgMHgwNykpXHJcbiAgICAgICAgICAgICAgICArIF9mcm9tQ0MoMHg4MCB8ICgoY2MgPj4+IDEyKSAmIDB4M2YpKVxyXG4gICAgICAgICAgICAgICAgKyBfZnJvbUNDKDB4ODAgfCAoKGNjID4+PiA2KSAmIDB4M2YpKVxyXG4gICAgICAgICAgICAgICAgKyBfZnJvbUNDKDB4ODAgfCAoY2MgJiAweDNmKSkpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICB2YXIgcmVfdXRvYiA9IC9bXFx1RDgwMC1cXHVEQkZGXVtcXHVEQzAwLVxcdURGRkZGXXxbXlxceDAwLVxceDdGXS9nO1xyXG4gICAgLyoqXHJcbiAgICAgKiBAZGVwcmVjYXRlZCBzaG91bGQgaGF2ZSBiZWVuIGludGVybmFsIHVzZSBvbmx5LlxyXG4gICAgICogQHBhcmFtIHtzdHJpbmd9IHNyYyBVVEYtOCBzdHJpbmdcclxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IFVURi0xNiBzdHJpbmdcclxuICAgICAqL1xyXG4gICAgdmFyIHV0b2IgPSBmdW5jdGlvbiAodSkgeyByZXR1cm4gdS5yZXBsYWNlKHJlX3V0b2IsIGNiX3V0b2IpOyB9O1xyXG4gICAgLy9cclxuICAgIHZhciBfZW5jb2RlID0gX1RFID8gZnVuY3Rpb24gKHMpIHsgcmV0dXJuIF9mcm9tVWludDhBcnJheShfVEUuZW5jb2RlKHMpKTsgfSA6IGZ1bmN0aW9uIChzKSB7IHJldHVybiBfYnRvYSh1dG9iKHMpKTsgfTtcclxuICAgIC8qKlxyXG4gICAgICogY29udmVydHMgYSBVVEYtOC1lbmNvZGVkIHN0cmluZyB0byBhIEJhc2U2NCBzdHJpbmcuXHJcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW59IFt1cmxzYWZlXSBpZiBgdHJ1ZWAgbWFrZSB0aGUgcmVzdWx0IFVSTC1zYWZlXHJcbiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfSBCYXNlNjQgc3RyaW5nXHJcbiAgICAgKi9cclxuICAgIHZhciBlbmNvZGUgPSBmdW5jdGlvbiAoc3JjLCB1cmxzYWZlKSB7XHJcbiAgICAgICAgaWYgKHVybHNhZmUgPT09IHZvaWQgMCkgeyB1cmxzYWZlID0gZmFsc2U7IH1cclxuICAgICAgICByZXR1cm4gdXJsc2FmZVxyXG4gICAgICAgICAgICA/IF9ta1VyaVNhZmUoX2VuY29kZShzcmMpKVxyXG4gICAgICAgICAgICA6IF9lbmNvZGUoc3JjKTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIGNvbnZlcnRzIGEgVVRGLTgtZW5jb2RlZCBzdHJpbmcgdG8gVVJMLXNhZmUgQmFzZTY0IFJGQzQ2NDggwqc1LlxyXG4gICAgICogQHJldHVybnMge3N0cmluZ30gQmFzZTY0IHN0cmluZ1xyXG4gICAgICovXHJcbiAgICB2YXIgZW5jb2RlVVJJID0gZnVuY3Rpb24gKHNyYykgeyByZXR1cm4gZW5jb2RlKHNyYywgdHJ1ZSk7IH07XHJcbiAgICAvLyBUaGlzIHRyaWNrIGlzIGZvdW5kIGJyb2tlbiBodHRwczovL2dpdGh1Yi5jb20vZGFua29nYWkvanMtYmFzZTY0L2lzc3Vlcy8xMzBcclxuICAgIC8vIGNvbnN0IGJ0b3UgPSAoc3JjOiBzdHJpbmcpID0+IGRlY29kZVVSSUNvbXBvbmVudChlc2NhcGUoc3JjKSk7XHJcbiAgICAvLyByZXZlcnRpbmcgZ29vZCBvbGQgZmF0aW9uZWQgcmVnZXhwXHJcbiAgICB2YXIgcmVfYnRvdSA9IC9bXFx4QzAtXFx4REZdW1xceDgwLVxceEJGXXxbXFx4RTAtXFx4RUZdW1xceDgwLVxceEJGXXsyfXxbXFx4RjAtXFx4RjddW1xceDgwLVxceEJGXXszfS9nO1xyXG4gICAgdmFyIGNiX2J0b3UgPSBmdW5jdGlvbiAoY2NjYykge1xyXG4gICAgICAgIHN3aXRjaCAoY2NjYy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgY2FzZSA0OlxyXG4gICAgICAgICAgICAgICAgdmFyIGNwID0gKCgweDA3ICYgY2NjYy5jaGFyQ29kZUF0KDApKSA8PCAxOClcclxuICAgICAgICAgICAgICAgICAgICB8ICgoMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgxKSkgPDwgMTIpXHJcbiAgICAgICAgICAgICAgICAgICAgfCAoKDB4M2YgJiBjY2NjLmNoYXJDb2RlQXQoMikpIDw8IDYpXHJcbiAgICAgICAgICAgICAgICAgICAgfCAoMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgzKSksIG9mZnNldCA9IGNwIC0gMHgxMDAwMDtcclxuICAgICAgICAgICAgICAgIHJldHVybiAoX2Zyb21DQygob2Zmc2V0ID4+PiAxMCkgKyAweEQ4MDApXHJcbiAgICAgICAgICAgICAgICAgICAgKyBfZnJvbUNDKChvZmZzZXQgJiAweDNGRikgKyAweERDMDApKTtcclxuICAgICAgICAgICAgY2FzZSAzOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mcm9tQ0MoKCgweDBmICYgY2NjYy5jaGFyQ29kZUF0KDApKSA8PCAxMilcclxuICAgICAgICAgICAgICAgICAgICB8ICgoMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgxKSkgPDwgNilcclxuICAgICAgICAgICAgICAgICAgICB8ICgweDNmICYgY2NjYy5jaGFyQ29kZUF0KDIpKSk7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gX2Zyb21DQygoKDB4MWYgJiBjY2NjLmNoYXJDb2RlQXQoMCkpIDw8IDYpXHJcbiAgICAgICAgICAgICAgICAgICAgfCAoMHgzZiAmIGNjY2MuY2hhckNvZGVBdCgxKSkpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIEBkZXByZWNhdGVkIHNob3VsZCBoYXZlIGJlZW4gaW50ZXJuYWwgdXNlIG9ubHkuXHJcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gc3JjIFVURi0xNiBzdHJpbmdcclxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IFVURi04IHN0cmluZ1xyXG4gICAgICovXHJcbiAgICB2YXIgYnRvdSA9IGZ1bmN0aW9uIChiKSB7IHJldHVybiBiLnJlcGxhY2UocmVfYnRvdSwgY2JfYnRvdSk7IH07XHJcbiAgICAvKipcclxuICAgICAqIHBvbHlmaWxsIHZlcnNpb24gb2YgYGF0b2JgXHJcbiAgICAgKi9cclxuICAgIHZhciBhdG9iUG9seWZpbGwgPSBmdW5jdGlvbiAoYXNjKSB7XHJcbiAgICAgICAgLy8gY29uc29sZS5sb2coJ3BvbHlmaWxsZWQnKTtcclxuICAgICAgICBhc2MgPSBhc2MucmVwbGFjZSgvXFxzKy9nLCAnJyk7XHJcbiAgICAgICAgaWYgKCFiNjRyZS50ZXN0KGFzYykpXHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ21hbGZvcm1lZCBiYXNlNjQuJyk7XHJcbiAgICAgICAgYXNjICs9ICc9PScuc2xpY2UoMiAtIChhc2MubGVuZ3RoICYgMykpO1xyXG4gICAgICAgIHZhciB1MjQsIGJpbiA9ICcnLCByMSwgcjI7XHJcbiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhc2MubGVuZ3RoOykge1xyXG4gICAgICAgICAgICB1MjQgPSBiNjR0YWJbYXNjLmNoYXJBdChpKyspXSA8PCAxOFxyXG4gICAgICAgICAgICAgICAgfCBiNjR0YWJbYXNjLmNoYXJBdChpKyspXSA8PCAxMlxyXG4gICAgICAgICAgICAgICAgfCAocjEgPSBiNjR0YWJbYXNjLmNoYXJBdChpKyspXSkgPDwgNlxyXG4gICAgICAgICAgICAgICAgfCAocjIgPSBiNjR0YWJbYXNjLmNoYXJBdChpKyspXSk7XHJcbiAgICAgICAgICAgIGJpbiArPSByMSA9PT0gNjQgPyBfZnJvbUNDKHUyNCA+PiAxNiAmIDI1NSlcclxuICAgICAgICAgICAgICAgIDogcjIgPT09IDY0ID8gX2Zyb21DQyh1MjQgPj4gMTYgJiAyNTUsIHUyNCA+PiA4ICYgMjU1KVxyXG4gICAgICAgICAgICAgICAgICAgIDogX2Zyb21DQyh1MjQgPj4gMTYgJiAyNTUsIHUyNCA+PiA4ICYgMjU1LCB1MjQgJiAyNTUpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gYmluO1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogZG9lcyB3aGF0IGB3aW5kb3cuYXRvYmAgb2Ygd2ViIGJyb3dzZXJzIGRvLlxyXG4gICAgICogQHBhcmFtIHtTdHJpbmd9IGFzYyBCYXNlNjQtZW5jb2RlZCBzdHJpbmdcclxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IGJpbmFyeSBzdHJpbmdcclxuICAgICAqL1xyXG4gICAgdmFyIF9hdG9iID0gLyogdHlwZW9mIGF0b2IgPT09ICdmdW5jdGlvbicgPyBmdW5jdGlvbiAoYXNjKSB7IHJldHVybiBhdG9iKF90aWR5QjY0KGFzYykpOyB9IDogICovYXRvYlBvbHlmaWxsO1xyXG4gICAgLy9cclxuICAgIHZhciBfdG9VaW50OEFycmF5ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIF9VOEFmcm9tKF9hdG9iKGEpLnNwbGl0KCcnKS5tYXAoZnVuY3Rpb24gKGMpIHsgcmV0dXJuIGMuY2hhckNvZGVBdCgwKTsgfSkpOyB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBjb252ZXJ0cyBhIEJhc2U2NCBzdHJpbmcgdG8gYSBVaW50OEFycmF5LlxyXG4gICAgICovXHJcbiAgICB2YXIgdG9VaW50OEFycmF5ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIF90b1VpbnQ4QXJyYXkoX3VuVVJJKGEpKTsgfTtcclxuICAgIC8vXHJcbiAgICB2YXIgX2RlY29kZSA9IF9URCA/IGZ1bmN0aW9uIChhKSB7IHJldHVybiBfVEQuZGVjb2RlKF90b1VpbnQ4QXJyYXkoYSkpOyB9IDogZnVuY3Rpb24gKGEpIHsgcmV0dXJuIGJ0b3UoX2F0b2IoYSkpOyB9O1xyXG4gICAgdmFyIF91blVSSSA9IGZ1bmN0aW9uIChhKSB7IHJldHVybiBfdGlkeUI2NChhLnJlcGxhY2UoL1stX10vZywgZnVuY3Rpb24gKG0wKSB7IHJldHVybiBtMCA9PSAnLScgPyAnKycgOiAnLyc7IH0pKTsgfTtcclxuICAgIC8qKlxyXG4gICAgICogY29udmVydHMgYSBCYXNlNjQgc3RyaW5nIHRvIGEgVVRGLTggc3RyaW5nLlxyXG4gICAgICogQHBhcmFtIHtTdHJpbmd9IHNyYyBCYXNlNjQgc3RyaW5nLiAgQm90aCBub3JtYWwgYW5kIFVSTC1zYWZlIGFyZSBzdXBwb3J0ZWRcclxuICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IFVURi04IHN0cmluZ1xyXG4gICAgICovXHJcbiAgICB2YXIgZGVjb2RlID0gZnVuY3Rpb24gKHNyYykgeyByZXR1cm4gX2RlY29kZShfdW5VUkkoc3JjKSk7IH07XHJcbiAgICAvKipcclxuICAgICAqIGNoZWNrIGlmIGEgdmFsdWUgaXMgYSB2YWxpZCBCYXNlNjQgc3RyaW5nXHJcbiAgICAgKiBAcGFyYW0ge1N0cmluZ30gc3JjIGEgdmFsdWUgdG8gY2hlY2tcclxuICAgICAgKi9cclxuICAgIHZhciBpc1ZhbGlkID0gZnVuY3Rpb24gKHNyYykge1xyXG4gICAgICAgIGlmICh0eXBlb2Ygc3JjICE9PSAnc3RyaW5nJylcclxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgICAgIHZhciBzID0gc3JjLnJlcGxhY2UoL1xccysvZywgJycpLnJlcGxhY2UoLz17MCwyfSQvLCAnJyk7XHJcbiAgICAgICAgcmV0dXJuICEvW15cXHMwLTlhLXpBLVpcXCsvXS8udGVzdChzKSB8fCAhL1teXFxzMC05YS16QS1aXFwtX10vLnRlc3Qocyk7XHJcbiAgICB9O1xyXG4gICAgLy9cclxuICAgIHZhciBfbm9FbnVtID0gZnVuY3Rpb24gKHYpIHtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICB2YWx1ZTogdiwgZW51bWVyYWJsZTogZmFsc2UsIHdyaXRhYmxlOiB0cnVlLCBjb25maWd1cmFibGU6IHRydWVcclxuICAgICAgICB9O1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogZXh0ZW5kIFN0cmluZy5wcm90b3R5cGUgd2l0aCByZWxldmFudCBtZXRob2RzXHJcbiAgICAgKi9cclxuICAgIHZhciBleHRlbmRTdHJpbmcgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgdmFyIF9hZGQgPSBmdW5jdGlvbiAobmFtZSwgYm9keSkgeyByZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KFN0cmluZy5wcm90b3R5cGUsIG5hbWUsIF9ub0VudW0oYm9keSkpOyB9O1xyXG4gICAgICAgIF9hZGQoJ2Zyb21CYXNlNjQnLCBmdW5jdGlvbiAoKSB7IHJldHVybiBkZWNvZGUodGhpcyk7IH0pO1xyXG4gICAgICAgIF9hZGQoJ3RvQmFzZTY0JywgZnVuY3Rpb24gKHVybHNhZmUpIHsgcmV0dXJuIGVuY29kZSh0aGlzLCB1cmxzYWZlKTsgfSk7XHJcbiAgICAgICAgX2FkZCgndG9CYXNlNjRVUkknLCBmdW5jdGlvbiAoKSB7IHJldHVybiBlbmNvZGUodGhpcywgdHJ1ZSk7IH0pO1xyXG4gICAgICAgIF9hZGQoJ3RvQmFzZTY0VVJMJywgZnVuY3Rpb24gKCkgeyByZXR1cm4gZW5jb2RlKHRoaXMsIHRydWUpOyB9KTtcclxuICAgICAgICBfYWRkKCd0b1VpbnQ4QXJyYXknLCBmdW5jdGlvbiAoKSB7IHJldHVybiB0b1VpbnQ4QXJyYXkodGhpcyk7IH0pO1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogZXh0ZW5kIFVpbnQ4QXJyYXkucHJvdG90eXBlIHdpdGggcmVsZXZhbnQgbWV0aG9kc1xyXG4gICAgICovXHJcbiAgICB2YXIgZXh0ZW5kVWludDhBcnJheSA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICB2YXIgX2FkZCA9IGZ1bmN0aW9uIChuYW1lLCBib2R5KSB7IHJldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkoVWludDhBcnJheS5wcm90b3R5cGUsIG5hbWUsIF9ub0VudW0oYm9keSkpOyB9O1xyXG4gICAgICAgIF9hZGQoJ3RvQmFzZTY0JywgZnVuY3Rpb24gKHVybHNhZmUpIHsgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHVybHNhZmUpOyB9KTtcclxuICAgICAgICBfYWRkKCd0b0Jhc2U2NFVSSScsIGZ1bmN0aW9uICgpIHsgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHRydWUpOyB9KTtcclxuICAgICAgICBfYWRkKCd0b0Jhc2U2NFVSTCcsIGZ1bmN0aW9uICgpIHsgcmV0dXJuIGZyb21VaW50OEFycmF5KHRoaXMsIHRydWUpOyB9KTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIGV4dGVuZCBCdWlsdGluIHByb3RvdHlwZXMgd2l0aCByZWxldmFudCBtZXRob2RzXHJcbiAgICAgKi9cclxuICAgIHZhciBleHRlbmRCdWlsdGlucyA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICBleHRlbmRTdHJpbmcoKTtcclxuICAgICAgICBleHRlbmRVaW50OEFycmF5KCk7XHJcbiAgICB9O1xyXG4gICAgdmFyIGdCYXNlNjQgPSB7XHJcbiAgICAgICAgdmVyc2lvbjogdmVyc2lvbixcclxuICAgICAgICBWRVJTSU9OOiBWRVJTSU9OLFxyXG4gICAgICAgIGF0b2I6IF9hdG9iLFxyXG4gICAgICAgIGF0b2JQb2x5ZmlsbDogYXRvYlBvbHlmaWxsLFxyXG4gICAgICAgIGJ0b2E6IF9idG9hLFxyXG4gICAgICAgIGJ0b2FQb2x5ZmlsbDogYnRvYVBvbHlmaWxsLFxyXG4gICAgICAgIGZyb21CYXNlNjQ6IGRlY29kZSxcclxuICAgICAgICB0b0Jhc2U2NDogZW5jb2RlLFxyXG4gICAgICAgIGVuY29kZTogZW5jb2RlLFxyXG4gICAgICAgIGVuY29kZVVSSTogZW5jb2RlVVJJLFxyXG4gICAgICAgIGVuY29kZVVSTDogZW5jb2RlVVJJLFxyXG4gICAgICAgIHV0b2I6IHV0b2IsXHJcbiAgICAgICAgYnRvdTogYnRvdSxcclxuICAgICAgICBkZWNvZGU6IGRlY29kZSxcclxuICAgICAgICBpc1ZhbGlkOiBpc1ZhbGlkLFxyXG4gICAgICAgIGZyb21VaW50OEFycmF5OiBmcm9tVWludDhBcnJheSxcclxuICAgICAgICB0b1VpbnQ4QXJyYXk6IHRvVWludDhBcnJheSxcclxuICAgICAgICBleHRlbmRTdHJpbmc6IGV4dGVuZFN0cmluZyxcclxuICAgICAgICBleHRlbmRVaW50OEFycmF5OiBleHRlbmRVaW50OEFycmF5LFxyXG4gICAgICAgIGV4dGVuZEJ1aWx0aW5zOiBleHRlbmRCdWlsdGluc1xyXG4gICAgfTtcclxuICAgIC8vXHJcbiAgICAvLyBleHBvcnQgQmFzZTY0IHRvIHRoZSBuYW1lc3BhY2VcclxuICAgIC8vXHJcbiAgICAvLyBFUzUgaXMgeWV0IHRvIGhhdmUgT2JqZWN0LmFzc2lnbigpIHRoYXQgbWF5IG1ha2UgdHJhbnNwaWxlcnMgdW5oYXBweS5cclxuICAgIC8vIGdCYXNlNjQuQmFzZTY0ID0gT2JqZWN0LmFzc2lnbih7fSwgZ0Jhc2U2NCk7XHJcbiAgICAvLyBnQmFzZTY0LkJhc2U2NCA9IHt9O1xyXG4gICAgLy8gT2JqZWN0LmtleXMoZ0Jhc2U2NCkuZm9yRWFjaChmdW5jdGlvbiAoaykgeyByZXR1cm4gZ0Jhc2U2NC5CYXNlNjRba10gPSBnQmFzZTY0W2tdOyB9KTtcclxuICAgIHJldHVybiBnQmFzZTY0O1xyXG59KSk7XHJcbiJdfQ==