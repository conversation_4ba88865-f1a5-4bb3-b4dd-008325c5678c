
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/CreateAlliChannelPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3f3d22xvAlG5b05gH4Gs8LX', 'CreateAlliChannelPnlCtrl');
// app/script/view/common/CreateAlliChannelPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CreateAlliChannelPnlCtrl = /** @class */ (function (_super) {
    __extends(CreateAlliChannelPnlCtrl, _super);
    function CreateAlliChannelPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/name/input_eb
        _this.addMemberNode_ = null; // path://root/member/add_member_be_n
        _this.showColorNode_ = null; // path://root/color/color/custom_color_be/show_color_n
        _this.showDrawboardNode_ = null; // path://show_drawboard_be_n
        _this.colorBoardSpr_ = null; // path://show_drawboard_be_n/root/color_board_s
        _this.circleNode_ = null; // path://show_drawboard_be_n/root/color_board_s/circle_n
        _this.hueSpr_ = null; // path://show_drawboard_be_n/root/hue_s
        _this.huePointNode_ = null; // path://show_drawboard_be_n/root/hue_s/hue_point_n
        _this.rgbNode_ = null; // path://show_drawboard_be_n/root/rgb_n
        _this.rEditBoxEb_ = null; // path://show_drawboard_be_n/root/rgb_n/r/r_editBox_eb
        _this.gEditBoxEb_ = null; // path://show_drawboard_be_n/root/rgb_n/g/g_editBox_eb
        _this.bEditBoxEb_ = null; // path://show_drawboard_be_n/root/rgb_n/b/b_editBox_eb
        //@end
        _this.cb = null;
        _this.alliance = null;
        _this.preRootHeight = -1;
        _this.root = null;
        _this.rSlider = null;
        _this.gSlider = null;
        _this.bSlider = null;
        _this.h = 240;
        _this.s = 100;
        _this.v = 100;
        _this.colorBoardData = null;
        _this.colorBoardTexture = new cc.Texture2D();
        _this.curSelectColor = ut.colorFromHEX(Constant_1.CHAT_BARRAGE_COLOR[1]); // 默认颜色
        _this.memberUids = []; // 当前频道成员uid
        return _this;
    }
    CreateAlliChannelPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CreateAlliChannelPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.alliance = this.getModel('alliance');
                this.root = this.FindChild('root');
                this.initSliderProperty();
                this.createHueData();
                this.addEvent();
                this.showColorNode_.color = this.curSelectColor;
                return [2 /*return*/];
            });
        });
    };
    CreateAlliChannelPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        this.memberUids = [GameHelper_1.gameHpr.user.getUid()];
        this.updateChannelMember();
    };
    CreateAlliChannelPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb();
        this.cb = null;
    };
    CreateAlliChannelPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/button/ok_be
    CreateAlliChannelPnlCtrl.prototype.onClickOk = function (event, data) {
        var name = this.inputEb_.string.trim();
        if (!name) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_alli_channel_name');
        }
        else if (ut.getStringLen(name) > 12 || GameHelper_1.gameHpr.getTextNewlineCount(name) > 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name');
        }
        else if (this.memberUids.length <= 1) {
            return ViewHelper_1.viewHelper.showAlert('toast.create_alli_channel_no_member');
        }
        this.do(name);
    };
    // path://root/color/color/custom_color_be
    CreateAlliChannelPnlCtrl.prototype.onClickCustomColor = function (event, data) {
        this.showDrawboard();
    };
    // path://show_drawboard_be_n
    CreateAlliChannelPnlCtrl.prototype.onClickShowDrawboard = function (event, data) {
        this.showDrawboardNode_.active = false;
    };
    // path://root/member/add_member_be_n
    CreateAlliChannelPnlCtrl.prototype.onClickAddMember = function (event, data) {
        var _this = this;
        ViewHelper_1.viewHelper.showPnl('common/AddAlliMember', this.memberUids, function (uids) {
            _this.memberUids = uids;
            _this.updateChannelMember();
        }, true);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CreateAlliChannelPnlCtrl.prototype.do = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            var color, memberFilter, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        color = this.curSelectColor.toHEX().toUpperCase(), memberFilter = this.memberUids.length !== this.alliance.getMembers().length;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_CreateAlliChatChannel', { name: name, color: color, memberUids: this.memberUids, memberFilter: memberFilter }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.TEXT_LEN_LIMIT) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_name')];
                        }
                        else if (err === ECode_1.ecode.TEXT_HAS_SENSITIVE) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.has_sensitive_word_name')];
                        }
                        else if (err === ECode_1.ecode.NAME_EXIST) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.army_name_exist')];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        GameHelper_1.gameHpr.alliance.addChatChannel(data);
                        if (this.isValid) {
                            this.inputEb_.string = '';
                            this.cb && this.cb(data);
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 频道人数
    CreateAlliChannelPnlCtrl.prototype.updateChannelMember = function () {
        var _this = this;
        var members = this.alliance.getMembers(), uidMap = {};
        this.memberUids.forEach(function (m) { return uidMap[m] = true; });
        var list = members.filter(function (m) { return uidMap[m.uid]; }).slice(0, 4);
        this.addMemberNode_.Child('head').Items(list, function (it, data) {
            ResHelper_1.resHelper.loadPlayerHead(it, data.headIcon, _this.key);
        });
        var tip = this.addMemberNode_.Child('tip');
        tip.Child('icon').active = this.memberUids.length <= 1;
        tip.Child('desc').setLocaleKey(this.memberUids.length <= 1 ? 'ui.click_add' : 'ui.member_count', this.memberUids.length);
    };
    // 显示画板
    CreateAlliChannelPnlCtrl.prototype.showDrawboard = function () {
        this.showDrawboardNode_.active = true;
        this.updateDrawboardPosition();
        var color = this.curSelectColor;
        this.initHuePoint(color);
        this.updateColorBoardData(color);
        this.updateColorCircle(color);
        this.updateColorInfo(color);
    };
    // 画板位置自适应
    CreateAlliChannelPnlCtrl.prototype.updateDrawboardPosition = function () {
        if (this.preRootHeight !== this.root.height) {
            this.preRootHeight = this.root.height;
            var node = this.showDrawboardNode_.Child('root');
            node.setPosition(ut.convertToNodeAR(this.root.Child('color/color_line'), this.showDrawboardNode_));
            node.scale = this.root.scale;
        }
    };
    // 滑动条初始化
    CreateAlliChannelPnlCtrl.prototype.initSliderProperty = function () {
        this.rSlider = this.rgbNode_.Child('r/slider', cc.Slider);
        this.gSlider = this.rgbNode_.Child('g/slider', cc.Slider);
        this.bSlider = this.rgbNode_.Child('b/slider', cc.Slider);
    };
    // 色相
    CreateAlliChannelPnlCtrl.prototype.createHueData = function () {
        var texture2D = new cc.Texture2D();
        var hueData = new Uint8Array(360 * 4);
        for (var i = 0; i < 360; i++) {
            var color = this.hsv2rgb(i, 100, 100), index = i * 4;
            hueData[index] = color.getR();
            hueData[index + 1] = color.getG();
            hueData[index + 2] = color.getB();
        }
        texture2D.setFlipY(true);
        texture2D.initWithData(hueData, cc.Texture2D.PixelFormat.RGB888, 1, 360);
        this.hueSpr_.spriteFrame = new cc.SpriteFrame(texture2D);
    };
    // 色相定位
    CreateAlliChannelPnlCtrl.prototype.initHuePoint = function (color) {
        var hsv = color.toHSV();
        this.h = hsv.h * 360;
        this.s = hsv.s * 100;
        this.v = hsv.v * 100;
        this.huePointNode_.y = Math.round(hsv.h * this.hueSpr_.node.height);
    };
    // 调色板
    CreateAlliChannelPnlCtrl.prototype.updateColorBoardData = function (color) {
        var h = color ? color.toHSV().h * 360 : this.h;
        this.colorBoardData = new Uint8Array(101 * 101 * 4);
        for (var i = 0; i < 101; i++) {
            for (var j = 0; j < 101; j++) {
                var c = this.hsv2rgb(h, j, 100 - i), index = (i * 101 + j) * 4;
                this.colorBoardData[index] = c.getR();
                this.colorBoardData[index + 1] = c.getG();
                this.colorBoardData[index + 2] = c.getB();
                this.colorBoardData[index + 3] = c.getA();
            }
        }
        this.colorBoardTexture.initWithData(this.colorBoardData, cc.Texture2D.PixelFormat.RGBA8888, 101, 101);
        this.colorBoardSpr_.spriteFrame = new cc.SpriteFrame(this.colorBoardTexture);
    };
    // 调色板定位
    CreateAlliChannelPnlCtrl.prototype.updateColorCircle = function (color) {
        var _color = color !== null && color !== void 0 ? color : this.hsv2rgb(this.h, this.s, this.v);
        var isBlack = 0.299 * _color.getR() + 0.578 * _color.getG() + 0.114 * _color.getB() >= 192;
        this.circleNode_.color = isBlack ? cc.Color.BLACK : cc.Color.WHITE;
        var s = color ? color.toHSV().s * 100 : this.s, v = color ? color.toHSV().v * 100 : this.v;
        this.circleNode_.setPosition(cc.v2(s / 100 * this.colorBoardSpr_.node.width, v / 100 * this.colorBoardSpr_.node.height));
    };
    // 色值属性
    CreateAlliChannelPnlCtrl.prototype.updateColorInfo = function (color) {
        color = color !== null && color !== void 0 ? color : this.hsv2rgb(this.h, this.s, this.v);
        this.updateRGBInfo(color);
        this.curSelectColor = color;
        this.showColorNode_.color = color;
    };
    // 更新RGB值
    CreateAlliChannelPnlCtrl.prototype.updateRGBInfo = function (color) {
        var R = color.getR(), G = color.getG(), B = color.getB();
        this.rSlider.progress = R / 255;
        this.rEditBoxEb_.string = R + '';
        this.gSlider.progress = G / 255;
        this.gEditBoxEb_.string = G + '';
        this.bSlider.progress = B / 255;
        this.bEditBoxEb_.string = B + '';
    };
    // 添加触摸事件
    CreateAlliChannelPnlCtrl.prototype.addEvent = function () {
        this.hueSpr_.node.on(cc.Node.EventType.TOUCH_START, this.onHueTouch, this);
        this.hueSpr_.node.on(cc.Node.EventType.TOUCH_MOVE, this.onHueTouch, this);
        this.huePointNode_.on(cc.Node.EventType.TOUCH_MOVE, this.onHueTouch, this);
        this.colorBoardSpr_.node.on(cc.Node.EventType.TOUCH_START, this.onColorBoardTouch, this);
        this.colorBoardSpr_.node.on(cc.Node.EventType.TOUCH_MOVE, this.onColorBoardTouch, this);
        this.rSlider.node.on('slide', this.onRgbChanged, this);
        this.gSlider.node.on('slide', this.onRgbChanged, this);
        this.bSlider.node.on('slide', this.onRgbChanged, this);
        this.rEditBoxEb_.node.on('editing-did-ended', this.editTextEnd, this);
        this.gEditBoxEb_.node.on('editing-did-ended', this.editTextEnd, this);
        this.bEditBoxEb_.node.on('editing-did-ended', this.editTextEnd, this);
    };
    // 色相事件
    CreateAlliChannelPnlCtrl.prototype.onHueTouch = function (event) {
        var pos = this.hueSpr_.node.convertToNodeSpaceAR(event.getLocation()), height = this.hueSpr_.node.height, val = cc.misc.clampf(pos.y, 0, height);
        this.huePointNode_.y = val;
        var index = Math.round(val / height * 360);
        index = Math.max(0, Math.min(index, 359));
        this.h = index;
        this.updateColorBoardData();
        this.updateColorCircle();
        this.updateColorInfo();
    };
    // 调色板事件
    CreateAlliChannelPnlCtrl.prototype.onColorBoardTouch = function (event) {
        var node = this.colorBoardSpr_.node, width = node.width, height = node.height, pos = node.convertToNodeSpaceAR(event.getLocation());
        pos.x = Math.max(0, Math.min(pos.x, width));
        pos.y = Math.max(0, Math.min(pos.y, height));
        this.s = Math.round(pos.x / width * 100);
        this.v = Math.round(pos.y / height * 100);
        this.s = Math.max(0, Math.min(this.s, 100));
        this.v = Math.max(0, Math.min(this.v, 100));
        this.updateColorCircle();
        this.updateColorInfo();
    };
    // 色值微调事件
    CreateAlliChannelPnlCtrl.prototype.onRgbChanged = function () {
        var r = Math.round(255 * this.rSlider.progress);
        this.rEditBoxEb_.string = r + '';
        var g = Math.round(255 * this.gSlider.progress);
        this.gEditBoxEb_.string = g + '';
        var b = Math.round(255 * this.bSlider.progress);
        this.bEditBoxEb_.string = b + '';
        var hsv = this.rgb2hsv(r, g, b);
        this.h = hsv[0];
        this.s = hsv[1];
        this.v = hsv[2];
        this.huePointNode_.y = Math.round(this.h / 360 * this.hueSpr_.node.height);
        this.updateColorBoardData();
        this.updateColorCircle();
        this.updateColorInfo();
    };
    CreateAlliChannelPnlCtrl.prototype.editTextEnd = function () {
        var rText = this.rEditBoxEb_.string;
        var r = parseInt(rText);
        if (isNaN(r)) {
            r = 255;
        }
        r = Math.max(0, Math.min(r, 255));
        this.rEditBoxEb_.string = r + '';
        this.rSlider.progress = r / 255;
        var gText = this.gEditBoxEb_.string;
        var g = parseInt(gText);
        if (isNaN(g)) {
            g = 255;
        }
        g = Math.max(0, Math.min(g, 255));
        this.gEditBoxEb_.string = g + '';
        this.gSlider.progress = g / 255;
        var bText = this.bEditBoxEb_.string;
        var b = parseInt(bText);
        if (isNaN(b)) {
            b = 255;
        }
        b = Math.max(0, Math.min(b, 255));
        this.bEditBoxEb_.string = b + '';
        this.bSlider.progress = b / 255;
        var hsv = this.rgb2hsv(r, g, b);
        this.h = hsv[0];
        this.s = hsv[1];
        this.v = hsv[2];
        var height = this.hueSpr_.node.height;
        this.huePointNode_.y = Math.round(this.h / 360 * height);
        this.updateColorBoardData();
        this.updateColorCircle();
        this.updateColorInfo();
    };
    CreateAlliChannelPnlCtrl.prototype.rgb2hsv = function (r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        var max = Math.max(r, g, b), min = Math.min(r, g, b), diff = max - min, v = max, s = max === 0 ? 0 : diff / max;
        var h = 0;
        if (max === min) {
            h = 0;
        }
        else if (max === r && g >= b) {
            h = 60 * ((g - b) / diff);
        }
        else if (max === r && g < b) {
            h = 60 * ((g - b) / diff) + 360;
        }
        else if (max === g) {
            h = 60 * ((b - r) / diff) + 120;
        }
        else if (max === b) {
            h = 60 * ((r - g) / diff) + 240;
        }
        return [Math.round(h), Math.round(s * 100), Math.round(v * 100)];
    };
    CreateAlliChannelPnlCtrl.prototype.hsv2rgb = function (h, s, v) {
        h /= 1;
        s /= 100;
        v /= 100;
        var r = 0, g = 0, b = 0;
        if (s === 0) {
            r = g = b = v;
        }
        else {
            var _h = h / 60;
            var i = Math.floor(_h);
            var f = _h - i;
            var p = v * (1 - s);
            var q = v * (1 - f * s);
            var t = v * (1 - (1 - f) * s);
            switch (i) {
                case 0:
                    r = v;
                    g = t;
                    b = p;
                    break;
                case 1:
                    r = q;
                    g = v;
                    b = p;
                    break;
                case 2:
                    r = p;
                    g = v;
                    b = t;
                    break;
                case 3:
                    r = p;
                    g = q;
                    b = v;
                    break;
                case 4:
                    r = t;
                    g = p;
                    b = v;
                    break;
                case 5:
                    r = v;
                    g = p;
                    b = q;
                    break;
            }
        }
        return cc.color(Math.round(r * 255), Math.round(g * 255), Math.round(b * 255), 255);
    };
    CreateAlliChannelPnlCtrl = __decorate([
        ccclass
    ], CreateAlliChannelPnlCtrl);
    return CreateAlliChannelPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CreateAlliChannelPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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