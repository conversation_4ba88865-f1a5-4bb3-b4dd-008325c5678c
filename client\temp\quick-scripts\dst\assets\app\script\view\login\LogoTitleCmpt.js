
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LogoTitleCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '85a18M26+VMnZ4vByC17za6', 'LogoTitleCmpt');
// app/script/view/login/LogoTitleCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LogoTitleCmpt = /** @class */ (function (_super) {
    __extends(LogoTitleCmpt, _super);
    function LogoTitleCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.sfs = [];
        return _this;
    }
    LogoTitleCmpt.prototype.onLoad = function () {
        var lang = mc.lang;
        var key = 'en';
        if (lang === 'hk' || lang === 'tw') {
            key = 'hk';
        }
        else if (lang === 'cn' || lang === 'jp' || lang === 'kr' || lang === 'th' || lang === 'vi') {
            key = lang;
        }
        var name = 'logo_title_' + key;
        this.getComponent(cc.Sprite).spriteFrame = this.sfs.find(function (m) { return m.name === name; }) || this.sfs[0];
    };
    __decorate([
        property([cc.SpriteFrame])
    ], LogoTitleCmpt.prototype, "sfs", void 0);
    LogoTitleCmpt = __decorate([
        ccclass
    ], LogoTitleCmpt);
    return LogoTitleCmpt;
}(cc.Component));
exports.default = LogoTitleCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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