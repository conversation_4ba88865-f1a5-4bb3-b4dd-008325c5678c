
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/NetWaitNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2c09dhQqGZDrof2kY/IvSKc', 'NetWaitNotCtrl');
// app/script/view/notice/NetWaitNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var NetWaitNotCtrl = /** @class */ (function (_super) {
    __extends(NetWaitNotCtrl, _super);
    function NetWaitNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.maskNode_ = null; // path://mask_n
        _this.roootNode_ = null; // path://rooot_n
        //@end
        _this.opening = false;
        _this.delay = 1; //延迟多少秒显示画面
        _this.hideTime = 30; //延迟多少秒后强行关闭界面
        _this.elapsed = 0;
        return _this;
    }
    NetWaitNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_REQ_BEGIN] = this.onEventOpen, _a),
            (_b = {}, _b[NetEvent_1.default.NET_REQ_END] = this.onEventHide, _b),
        ];
    };
    NetWaitNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.maskNode_.active = false;
                this.roootNode_.active = false;
                this.node.zIndex = 1;
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    NetWaitNotCtrl.prototype.onEventOpen = function (delay) {
        if (this.opening) {
            return;
        }
        this.open();
        this.opening = true;
        this.maskNode_.active = true;
        this.roootNode_.active = false;
        this.delay = delay !== null && delay !== void 0 ? delay : 1;
        this.elapsed = 0;
    };
    NetWaitNotCtrl.prototype.onEventHide = function () {
        this.hide();
        this.opening = false;
        this.maskNode_.active = false;
        this.roootNode_.active = false;
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    NetWaitNotCtrl.prototype.update = function (dt) {
        if (!this.opening) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed >= this.delay && this.elapsed < this.hideTime) {
            this.elapsed += this.hideTime;
            this.roootNode_.active = true;
        }
        if (this.elapsed - this.hideTime >= this.hideTime) {
            if (mc.currWindName !== 'login') { // 登录界面无用
                var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.net_timeout');
                ViewHelper_1.viewHelper.showMessageBox(text);
            }
            this.onEventHide();
        }
    };
    NetWaitNotCtrl = __decorate([
        ccclass
    ], NetWaitNotCtrl);
    return NetWaitNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = NetWaitNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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