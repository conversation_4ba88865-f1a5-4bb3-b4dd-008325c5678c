
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/CollectionEmojiInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6d15ejO1O5IzaRAlajtzi9e', 'CollectionEmojiInfoPnlCtrl');
// app/script/view/menu/CollectionEmojiInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CollectionEmojiInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CollectionEmojiInfoPnlCtrl, _super);
    function CollectionEmojiInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.nameNode_ = null; // path://root/name_n
        _this.countNode_ = null; // path://root/count_n
        _this.iconNode_ = null; // path://root/icon_n
        _this.nextNode_ = null; // path://root/next_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.root = null;
        _this.spr = null;
        _this.unlocks = [];
        _this.type = '';
        _this.list = [];
        _this.curPage = 0;
        _this.maxPage = 0;
        _this.cb = null;
        return _this;
    }
    CollectionEmojiInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CollectionEmojiInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.root = this.iconNode_.Child('root');
                this.spr = this.iconNode_.Child('val', cc.Sprite);
                return [2 /*return*/];
            });
        });
    };
    CollectionEmojiInfoPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        this.updateViewInfo(data);
    };
    CollectionEmojiInfoPnlCtrl.prototype.onRemove = function () {
    };
    CollectionEmojiInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/next_n/next_page_be@0
    CollectionEmojiInfoPnlCtrl.prototype.onClickNextPage = function (event, data) {
        var add = data === '0' ? -1 : 1;
        var curPage = this.curPage || 0, maxPage = this.maxPage;
        var index = ut.loopValue(curPage + add, maxPage);
        if (index !== curPage) {
            this.curPage = index;
            this.updateViewInfo({ type: this.type, list: this.list, index: this.curPage });
        }
    };
    // path://root/buttons_n/use_be
    CollectionEmojiInfoPnlCtrl.prototype.onClickUse = function (event, _data) {
        var data = event.target.Data;
        GameHelper_1.gameHpr.user.changeHeadicon(data.icon);
        ViewHelper_1.viewHelper.showPnl('menu/Personal', 0);
        this.hide();
    };
    // path://root/buttons_n/buy_be
    CollectionEmojiInfoPnlCtrl.prototype.onClickBuy = function (event, _data) {
        this.cb && this.cb(this.list[this.curPage]);
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 更新
    CollectionEmojiInfoPnlCtrl.prototype.updateViewInfo = function (data) {
        this.type = data.type;
        this.list = data.list;
        this.curPage = data.index || 0;
        this.maxPage = data.list.length - 1;
        var info = this.list[this.curPage];
        this.root.removeAllChildren();
        this.spr.spriteFrame = null;
        this.spr.node.removeAllChildren();
        this.nextNode_.active = !!this.maxPage;
        this.nameNode_.active = false;
        this.countNode_.active = false;
        if (data.type === 'headicon') {
            this.titleLbl_.setLocaleKey('ui.headicon_title');
            ResHelper_1.resHelper.loadPlayerHead(this.spr, info.icon, this.key, true);
            this.unlocks = GameHelper_1.gameHpr.user.getUnlockHeadIcons();
            var unlock = this.unlocks.has(info.icon);
            var btn = null;
            if (unlock) {
                btn = this.buttonsNode_.Swih('use_be')[0];
            }
            else if (info.cond === 1 || info.cond === 3 || (info.cond === 4 && GameHelper_1.gameHpr.checkShopNewProduct(info))) {
                btn = this.buttonsNode_.Swih('buy_be')[0];
                ViewHelper_1.viewHelper.updateCostText(btn, info);
            }
            else {
                btn = this.buttonsNode_.Swih('cond')[0];
                btn.Child('val').setLocaleKey('ui.get_cond_' + info.cond);
                btn.Child('emoji').active = false;
            }
            btn.Data = info;
        }
        else if (data.type === 'chat_emoji') {
            this.titleLbl_.setLocaleKey('ui.title_emoji');
            ResHelper_1.resHelper.loadEmojiNode(info.id, this.root, 1, this.key);
            this.unlocks = GameHelper_1.gameHpr.user.getUnlockChatEmojiIds();
            var unlock = this.unlocks.has(info.id);
            var btn = null;
            if (unlock || info.cond === 0) {
                btn = this.buttonsNode_.Swih('cond')[0];
                btn.Child('emoji').active = true;
                btn.Child('val').setLocaleKey('ui.use_to_chat');
            }
            else if (info.cond === 1 || info.cond === 3 || (info.cond === 4 && GameHelper_1.gameHpr.checkShopNewProduct(info))) {
                btn = this.buttonsNode_.Swih('buy_be')[0];
                ViewHelper_1.viewHelper.updateCostText(btn, info);
            }
            else {
                btn = this.buttonsNode_.Swih('cond')[0];
                btn.Child('emoji').active = false;
                btn.Child('val').setLocaleKey('ui.get_cond_' + info.cond);
            }
            btn.Data = info;
        }
        else if (data.type === 'plant_emoji') {
            this.titleLbl_.setLocaleKey('ui.button_botany');
            this.nameNode_.active = true;
            this.nameNode_.Child('val').setLocaleKey('ui.botany_name_' + info.id);
            ResHelper_1.resHelper.loadGiftIcon(info.id, this.spr, this.key);
            this.unlocks = GameHelper_1.gameHpr.user.getUnlockBotanys();
            var plant = this.unlocks.find(function (m) { return m.id === info.id; });
            this.countNode_.active = true;
            this.countNode_.Child('val', cc.Label).string = plant ? plant.count + '' : '0';
            var btn = this.buttonsNode_.Swih('cond')[0];
            btn.Child('emoji').active = false;
            btn.Child('val').setLocaleKey('ui.give_to_player');
            btn.Data = info;
        }
    };
    CollectionEmojiInfoPnlCtrl = __decorate([
        ccclass
    ], CollectionEmojiInfoPnlCtrl);
    return CollectionEmojiInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CollectionEmojiInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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