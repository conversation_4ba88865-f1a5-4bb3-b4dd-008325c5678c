
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/LongClickTouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a80afwU/utF3pUWsVivbBNE', 'LongClickTouchCmpt');
// app/script/view/cmpt/LongClickTouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于长按点击的触摸组件
 */
var LongClickTouchCmpt = /** @class */ (function (_super) {
    __extends(LongClickTouchCmpt, _super);
    function LongClickTouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.longTime = 0.5;
        _this.triggerInterval = 0.1;
        _this.touchId = -1;
        _this.touchEvent = null;
        _this.clickTime = 0;
        _this.clickInterval = 0;
        _this.clickCallback = null;
        _this.clickTarget = null;
        _this.interactable = true;
        return _this;
    }
    LongClickTouchCmpt.prototype.on = function (clickCallback, target) {
        this.clickCallback = clickCallback;
        this.clickTarget = target;
        this.node.on(cc.Node.EventType.TOUCH_START, this.onClickStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onClickEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onClickEnd, this);
    };
    LongClickTouchCmpt.prototype.onClickStart = function (event) {
        if (this.touchId !== -1 || !this.interactable) {
            return;
        }
        audioMgr.playSFX('click');
        this.touchEvent = event;
        this.touchId = event.getID();
        this.clickTime = 0;
        this.clickInterval = this.longTime;
        this.node.stopAllActions();
        this.node.scale = 1;
        cc.tween(this.node)
            .to(0.1, { scale: 1.05 })
            .start();
    };
    LongClickTouchCmpt.prototype.onClickEnd = function (event) {
        if (this.touchId !== event.getID()) {
            return;
        }
        this.touchId = -1;
        if (!this.interactable) {
            this.touchEvent = null;
            this.node.scale = 1;
            return;
        }
        else if (this.touchEvent && this.clickInterval === this.longTime) {
            this.onClick(this.touchEvent);
        }
        this.touchEvent = null;
        this.node.stopAllActions();
        cc.tween(this.node)
            .to(0.1, { scale: 1 })
            .start();
    };
    LongClickTouchCmpt.prototype.update = function (dt) {
        if (!this.touchEvent) {
            return;
        }
        this.clickTime += dt;
        if (this.clickTime >= this.clickInterval) {
            this.clickTime = 0;
            this.clickInterval = this.triggerInterval;
            this.onClick(this.touchEvent);
        }
    };
    LongClickTouchCmpt.prototype.onClick = function (touchEvent) {
        if (this.clickTarget) {
            this.clickCallback.call(this.clickTarget, touchEvent);
        }
        else {
            this.clickCallback(touchEvent);
        }
    };
    __decorate([
        property
    ], LongClickTouchCmpt.prototype, "longTime", void 0);
    __decorate([
        property
    ], LongClickTouchCmpt.prototype, "triggerInterval", void 0);
    LongClickTouchCmpt = __decorate([
        ccclass
    ], LongClickTouchCmpt);
    return LongClickTouchCmpt;
}(cc.Component));
exports.default = LongClickTouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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