
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ScrollViewInnerCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '48315uk2O1E6JVuWdC9Vxih', 'ScrollViewInnerCmpt');
// app/script/view/cmpt/ScrollViewInnerCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ScrollViewOuterCmpt_1 = require("./ScrollViewOuterCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 代替内部的ScrollView
var ScrollViewInnerCmpt = /** @class */ (function (_super) {
    __extends(ScrollViewInnerCmpt, _super);
    function ScrollViewInnerCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.outer = null;
        _this.tempVec = cc.v2();
        return _this;
    }
    ScrollViewInnerCmpt.prototype._onTouchMoved = function (event, captureListeners) {
        // @ts-ignore
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return;
        }
        // @ts-ignore
        if (this.content && !this.outer.isDifferentBetweenSettingAndPlan(this)) {
            // @ts-ignore
            this._handleMoveLogic(event.touch);
        }
        if (!this.cancelInnerEvents) {
            return;
        }
        if (event.getLocation().sub(event.getStartLocation(), this.tempVec).mag() > Constant_1.CLICK_SPACE) {
            // @ts-ignore
            if (!this._touchMoved && event.target !== this.node) {
                var cancelEvent = new cc.Event.EventTouch(event.getTouches(), event.bubbles);
                cancelEvent.type = cc.Node.EventType.TOUCH_CANCEL;
                cancelEvent.touch = event.touch;
                // @ts-ignore
                cancelEvent.simulate = true;
                event.target.dispatchEvent(cancelEvent);
                // @ts-ignore
                this._touchMoved = true;
            }
        }
        // @ts-ignore
        this._stopPropagationIfTargetIsMe(event);
    };
    __decorate([
        property(ScrollViewOuterCmpt_1.default)
    ], ScrollViewInnerCmpt.prototype, "outer", void 0);
    ScrollViewInnerCmpt = __decorate([
        ccclass
    ], ScrollViewInnerCmpt);
    return ScrollViewInnerCmpt;
}(cc.ScrollView));
exports.default = ScrollViewInnerCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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