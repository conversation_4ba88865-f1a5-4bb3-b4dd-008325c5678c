
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PlayerInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd6e15t0vblIFJjEzwEAAhQH', 'PlayerInfoPnlCtrl');
// app/script/view/common/PlayerInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var PlayerInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PlayerInfoPnlCtrl, _super);
    function PlayerInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.headNode_ = null; // path://root/head_n
        _this.infoNode_ = null; // path://root/info_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.data = null;
        return _this;
    }
    PlayerInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    PlayerInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    PlayerInfoPnlCtrl.prototype.onEnter = function (data, from) {
        var _a;
        this.data = data;
        var isOwner = data.uid === GameHelper_1.gameHpr.getUid(), cellCount = ((_a = data.cells) === null || _a === void 0 ? void 0 : _a.size) || 0;
        var isCapture = !cellCount, isSpectate = GameHelper_1.gameHpr.isSpectate(data.uid);
        ResHelper_1.resHelper.loadPlayerHead(this.headNode_.Child('val'), data.headIcon, this.key);
        this.headNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 11);
        // 个人简介
        ViewHelper_1.viewHelper.updatePlayerPersonalDesc(this.infoNode_.Child('5/val'), data.uid);
        // 联盟
        var alliNode = this.infoNode_.Child('alliance/lay');
        alliNode.Child('icon').active = !!data.allianceName && !!data.allianceIcon;
        alliNode.Child('val', cc.Label).string = data.allianceName || '-';
        ResHelper_1.resHelper.loadAlliIcon(data.allianceIcon, alliNode.Child('icon/val'), this.key);
        // 段位
        ViewHelper_1.viewHelper.updatePlayerRankInfo(this.infoNode_.Child('rank'), data.uid, this.key, data);
        // 总局数
        ViewHelper_1.viewHelper.updateTotalGameCount(this.infoNode_.Child('6'), data.uid);
        // 称号
        ViewHelper_1.viewHelper.updatePlayerTitleText(this.headNode_.Child('title'), data.uid, this.key, data);
        // 人气
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), data.uid, this.key);
        // 主城位置
        var mainPosNode = this.infoNode_.Child('1');
        if (mainPosNode.active = !isOwner && from !== 'cellinfo' && !isSpectate) {
            var posNode = mainPosNode.Child('main_pos_be');
            mainPosNode.Child('capture').active = isCapture;
            if (posNode.active = !isCapture) {
                ViewHelper_1.viewHelper.updatePositionView(posNode, data.mainCityIndex, false);
            }
        }
        // 领地数
        var cellCountNode = this.infoNode_.Child('2');
        if (cellCountNode.active = !isSpectate) {
            cellCountNode.Child('val', cc.Label).string = cellCount + '';
        }
        // 按钮
        // if (this.buttonsNode_.active = !isOwner && !isSpectate && !gameHpr.isSpectate()) {
        //     const isCanGive = !isCapture && (gameHpr.isFreeServer() || gameHpr.isOneAlliance(data.uid))
        //     this.buttonsNode_.Child('button/give_res_be').opacity = isCanGive ? 255 : 120
        //     const isInBlacklist = gameHpr.friend.isInBlacklist(data.uid)
        //     this.buttonsNode_.Child('blacklist_be/val', cc.MultiFrame).setFrame(isInBlacklist)
        //     this.buttonsNode_.Child('add_friend_be').active = !isInBlacklist && !gameHpr.friend.isFriend(data.uid)
        // }
        if (this.buttonsNode_.active = !isOwner) {
            if (this.buttonsNode_.Child('button').active = !isSpectate && !GameHelper_1.gameHpr.isSpectate()) {
                var isCanGive = !isCapture && (GameHelper_1.gameHpr.isFreeServer() || GameHelper_1.gameHpr.isOneAlliance(data.uid));
                this.buttonsNode_.Child('button/give_res_be').opacity = isCanGive ? 255 : 120;
            }
            var isInBlacklist = GameHelper_1.gameHpr.friend.isInBlacklist(data.uid);
            this.buttonsNode_.Child('blacklist_be/val', cc.MultiFrame).setFrame(isInBlacklist);
            this.buttonsNode_.Child('add_friend_be').active = !isInBlacklist && !GameHelper_1.gameHpr.friend.isFriend(data.uid);
        }
    };
    PlayerInfoPnlCtrl.prototype.onRemove = function () {
        this.data = null;
    };
    PlayerInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/info_n/1/main_pos_be
    PlayerInfoPnlCtrl.prototype.onClickMainPos = function (event, data) {
        var index = event.target.Data;
        if (index) {
            this.emit(mc.Event.HIDE_ALL_PNL);
            GameHelper_1.gameHpr.gotoTargetPos(index);
        }
    };
    // path://root/info_n/p/button/add_popularity_be
    PlayerInfoPnlCtrl.prototype.onClickAddPopularity = function (event, _) {
        var _this = this;
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.addPlayerPopularity(this.data, function (id) {
            if (_this.isValid && id) {
                _this.playAddPopularity(id);
            }
        });
    };
    // path://root/info_n/p/button/popularity_record_be
    PlayerInfoPnlCtrl.prototype.onClickPopularityRecord = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/PopularityRecord');
    };
    // path://root/buttons_n/button/give_res_be
    PlayerInfoPnlCtrl.prototype.onClickGiveRes = function (event, data) {
        var _this = this;
        var isFreeServer = GameHelper_1.gameHpr.isFreeServer();
        if (!isFreeServer && !GameHelper_1.gameHpr.isOneAlliance(this.data.uid)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ONLY_ALLI_GIVE); //只能给盟友赠送
        }
        var id = isFreeServer ? Constant_1.BUILD_BAZAAR_NID : Constant_1.BUILD_ALLI_BAZAAR_NID;
        if (!GameHelper_1.gameHpr.player.getMainBuilds().some(function (m) { return m.id === id; })) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_bt_bazaar');
        }
        else if (this.data) {
            ViewHelper_1.viewHelper.showBuildInfoByMain(id, this.data.uid).then(function (ok) {
                if (_this.isValid && ok) {
                    _this.hide();
                }
            });
        }
    };
    // path://root/buttons_n/button/send_pchat_be
    PlayerInfoPnlCtrl.prototype.onClickSendPchat = function (event, data) {
        var _this = this;
        GameHelper_1.gameHpr.addPChat(this.data.uid).then(function (ok) { return (ok && _this.isValid) && _this.hide(); });
    };
    // path://root/head_n/copy_uid_be
    PlayerInfoPnlCtrl.prototype.onClickCopyUid = function (event, data) {
        GameHelper_1.gameHpr.copyToClipboard(this.data.uid, 'toast.yet_copy_clipboard');
    };
    // path://root/buttons_n/blacklist_be
    PlayerInfoPnlCtrl.prototype.onClickBlacklist = function (event, _) {
        ViewHelper_1.viewHelper.showBlacklist(this.data, event, this.buttonsNode_);
    };
    // path://root/buttons_n/add_friend_be
    PlayerInfoPnlCtrl.prototype.onClickAddFriend = function (event, _) {
        ViewHelper_1.viewHelper.showApplyFriend(this.data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    PlayerInfoPnlCtrl.prototype.playAddPopularity = function (id) {
        ViewHelper_1.viewHelper.updatePlayerPopularity(this.infoNode_.Child('3'), this.infoNode_.Child('p/button'), this.data.uid, this.key);
    };
    PlayerInfoPnlCtrl = __decorate([
        ccclass
    ], PlayerInfoPnlCtrl);
    return PlayerInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PlayerInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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