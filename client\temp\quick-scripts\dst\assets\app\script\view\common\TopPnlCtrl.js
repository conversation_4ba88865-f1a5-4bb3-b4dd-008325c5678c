
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/TopPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a4897V/vhhOsI6B84DE+Sbv', 'TopPnlCtrl');
// app/script/view/common/TopPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TopPnlCtrl = /** @class */ (function (_super) {
    __extends(TopPnlCtrl, _super);
    function TopPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.goldNode_ = null; // path://top/centre/gold_n
        _this.staminaNode_ = null; // path://top/centre/stamina_n
        _this.seasonNode_ = null; // path://top/centre/season_n
        _this.seasonPointerNode_ = null; // path://top/centre/season_n/progress/season_pointer_n
        _this.cerealNode_ = null; // path://top/cereal_n
        _this.timberNode_ = null; // path://top/timber_n
        _this.stoneNode_ = null; // path://top/stone_n
        _this.expBookNode_ = null; // path://top/other_res/exp_book_n
        _this.ironNode_ = null; // path://top/other_res/iron_n
        _this.upScrollNode_ = null; // path://top/other_res/up_scroll_n
        _this.fixatorNode_ = null; // path://top/other_res/fixator_n
        _this.serverTimeNode_ = null; // path://top/server_time_n
        _this.topLayerNode_ = null; // path://top_layer_n
        //@end
        _this.goldValLbl = null;
        _this.cerealValLbl = null;
        _this.timberValLbl = null;
        _this.stoneValLbl = null;
        _this.expBookValLbl = null;
        _this.ironValLbl = null;
        _this.upScrollValLbl = null;
        _this.fixatorValLbl = null;
        _this.staminaValLbl = null;
        _this.pointerNode = null;
        _this.user = null;
        _this.player = null;
        _this.seasonElapsedTime = -1; //当前季节经过的时间
        _this.seasonStartTime = 0;
        // ----------------------------------------- custom function ----------------------------------------------------
        _this._cerealTextPos = null;
        _this._timberTextPos = null;
        _this._stoneTextPos = null;
        return _this;
    }
    TopPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a),
            (_b = {}, _b[EventType_1.default.RECREATE_MAIN_CITY] = this.onRecreateMainCity, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_CEREAL] = this.onUpdateCereal, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_TIMBER] = this.onUpdateTimber, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_STONE] = this.onUpdateStone, _f),
            (_g = {}, _g[EventType_1.default.UPDATE_EXP_BOOK] = this.onUpdateExpBook, _g),
            (_h = {}, _h[EventType_1.default.UPDATE_IRON] = this.onUpdateIron, _h),
            (_j = {}, _j[EventType_1.default.UPDATE_UPSCROLL] = this.onUpdateUpScroll, _j),
            (_k = {}, _k[EventType_1.default.UPDATE_FIXATOR] = this.onUpdateFixator, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_STAMINA] = this.onUpdateStamina, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_RES_CAP] = this.onUpdateResCap, _m),
            (_o = {}, _o[EventType_1.default.CHANGE_SEASON] = this.onChangeSeason, _o),
            (_p = {}, _p[EventType_1.default.HIDE_TOP_NODE] = this.onHideTopNode, _p),
        ];
    };
    TopPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isClean: false, isAct: false, isMask: false });
                this.goldValLbl = this.goldNode_.FindChild('val', cc.LabelRollNumber);
                this.cerealValLbl = this.cerealNode_.FindChild('lay/val', cc.LabelRollNumber);
                this.timberValLbl = this.timberNode_.FindChild('lay/val', cc.LabelRollNumber);
                this.stoneValLbl = this.stoneNode_.FindChild('lay/val', cc.LabelRollNumber);
                this.expBookValLbl = this.expBookNode_.FindChild('val', cc.LabelRollNumber);
                this.ironValLbl = this.ironNode_.FindChild('val', cc.LabelRollNumber);
                this.upScrollValLbl = this.upScrollNode_.FindChild('val', cc.LabelRollNumber);
                this.fixatorValLbl = this.fixatorNode_.FindChild('val', cc.LabelRollNumber);
                this.staminaValLbl = this.staminaNode_.FindChild('lay/val', cc.LabelRollNumber);
                this.pointerNode = this.seasonPointerNode_.Child('val');
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.goldNode_.Child('add_gold_be').active = !GameHelper_1.gameHpr.isNoviceMode;
                return [2 /*return*/];
            });
        });
    };
    TopPnlCtrl.prototype.onEnter = function (init) {
        // if (init) {
        // }
        this.init();
    };
    TopPnlCtrl.prototype.onRemove = function () {
    };
    TopPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://top/cereal_n/output_be@1
    TopPnlCtrl.prototype.onClickOutput = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/ResDetails', Number(data));
    };
    // path://top/centre/stamina_n/stamina_be
    TopPnlCtrl.prototype.onClickStamina = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/StaminaDesc');
    };
    // path://top/other_res/exp_book_n/other_res_be
    TopPnlCtrl.prototype.onClickOtherRes = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/OtherResDesc');
    };
    // path://top/centre/season_n/season_be
    TopPnlCtrl.prototype.onClickSeason = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SeasonInfo');
    };
    // path://top/centre/gold_n/add_gold_be
    TopPnlCtrl.prototype.onClickAddGold = function (event, data) {
        ViewHelper_1.viewHelper.showBuyGoldTipPnl();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 重新连接
    TopPnlCtrl.prototype.onNetReconnect = function () {
        this.onEnter(true);
    };
    // 重新创建主城
    TopPnlCtrl.prototype.onRecreateMainCity = function () {
        this.init();
    };
    // 刷新金币
    TopPnlCtrl.prototype.onUpdateGold = function (add) {
        var gold = this.user.getGold();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.to(gold);
    };
    // 刷新粮食
    TopPnlCtrl.prototype.onUpdateCereal = function (add) {
        this.cerealValLbl.to(this.player.getCereal());
        this.updateOpAndFull(this.cerealNode_, this.player.getCereal() / this.player.getGranaryCap(), this.player.getCerealOp());
    };
    // 刷新木头
    TopPnlCtrl.prototype.onUpdateTimber = function (add) {
        this.timberValLbl.to(this.player.getTimber());
        this.updateOpAndFull(this.timberNode_, this.player.getTimber() / this.player.getWarehouseCap(), this.player.getTimberOp());
    };
    // 刷新石头
    TopPnlCtrl.prototype.onUpdateStone = function (add) {
        this.stoneValLbl.to(this.player.getStone());
        this.updateOpAndFull(this.stoneNode_, this.player.getStone() / this.player.getWarehouseCap(), this.player.getStoneOp());
    };
    // 刷新经验书
    TopPnlCtrl.prototype.onUpdateExpBook = function (add) {
        var val = this.player.getExpBook();
        if (this.expBookNode_.active = val > 0) {
            this.expBookValLbl.to(val);
        }
    };
    // 刷新铁
    TopPnlCtrl.prototype.onUpdateIron = function (add) {
        var val = this.player.getIron();
        if (this.ironNode_.active = val > 0) {
            this.ironValLbl.to(val);
        }
    };
    // 刷新卷轴
    TopPnlCtrl.prototype.onUpdateUpScroll = function (add) {
        var val = this.player.getUpScroll();
        if (this.upScrollNode_.active = val > 0) {
            this.upScrollValLbl.to(val);
        }
    };
    // 刷新固定器
    TopPnlCtrl.prototype.onUpdateFixator = function (add) {
        var val = this.player.getFixator();
        if (this.fixatorNode_.active = val > 0) {
            this.fixatorValLbl.to(val);
        }
    };
    // 刷新奖励点
    TopPnlCtrl.prototype.onUpdateStamina = function (add) {
        this.updateStamina();
    };
    // 更新资源容量
    TopPnlCtrl.prototype.onUpdateResCap = function () {
        var warehouseCap = this.player.getWarehouseCap();
        this.updateOpAndFull(this.cerealNode_, this.player.getCereal() / this.player.getGranaryCap(), this.player.getCerealOp());
        this.updateOpAndFull(this.timberNode_, this.player.getTimber() / warehouseCap, this.player.getTimberOp());
        this.updateOpAndFull(this.stoneNode_, this.player.getStone() / warehouseCap, this.player.getStoneOp());
    };
    // 改变季节
    TopPnlCtrl.prototype.onChangeSeason = function () {
        this.updateSeason();
    };
    // 显隐顶部粮食节点
    TopPnlCtrl.prototype.onHideTopNode = function (val) {
        this.cerealNode_.active = val;
    };
    Object.defineProperty(TopPnlCtrl.prototype, "cerealTextPos", {
        get: function () { return this._cerealTextPos || (this._cerealTextPos = ut.convertToNodeAR(this.cerealNode_.FindChild('lay'), this.topLayerNode_).clone()); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TopPnlCtrl.prototype, "timberTextPos", {
        get: function () { return this._timberTextPos || (this._timberTextPos = ut.convertToNodeAR(this.timberNode_.FindChild('lay'), this.topLayerNode_).clone()); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(TopPnlCtrl.prototype, "stoneTextPos", {
        get: function () { return this._stoneTextPos || (this._stoneTextPos = ut.convertToNodeAR(this.stoneNode_.FindChild('lay'), this.topLayerNode_).clone()); },
        enumerable: false,
        configurable: true
    });
    TopPnlCtrl.prototype.init = function () {
        // 服务器时间
        // if (this.serverTimeNode_.active = !ut.isMiniGame()) {
        if (this.serverTimeNode_.active = false) {
            this.serverTimeNode_.Child('val', cc.LabelTimer).setFormat(function (time) { return ut.dateFormat('hh:mm:ss', time * 1000); }).run(GameHelper_1.gameHpr.getServerNowTime() * 0.001);
        }
        var isSpectate = GameHelper_1.gameHpr.isSpectate();
        this.goldNode_.active = !isSpectate;
        this.staminaNode_.active = !isSpectate;
        this.cerealNode_.active = !isSpectate;
        this.timberNode_.active = !isSpectate;
        this.stoneNode_.active = !isSpectate;
        this.Child('top/other_res').active = !isSpectate;
        this.seasonNode_.x = isSpectate ? -252 : 0;
        if (isSpectate) {
            return this.updateSeason();
        }
        var gold = this.user.getGold();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.set(gold);
        var serverType = GameHelper_1.gameHpr.getServerType(), serverSubType = GameHelper_1.gameHpr.getServerSubType();
        this.staminaNode_.Child('lay/max', cc.Label).string = serverType === 0 && serverSubType === 1 ? '/150' : '/50';
        this.updateStamina(true);
        this.cerealValLbl.set(this.player.getCereal());
        this.updateOpAndFull(this.cerealNode_, this.player.getCereal() / this.player.getGranaryCap(), this.player.getCerealOp());
        this.timberValLbl.set(this.player.getTimber());
        this.updateOpAndFull(this.timberNode_, this.player.getTimber() / this.player.getWarehouseCap(), this.player.getTimberOp());
        this.stoneValLbl.set(this.player.getStone());
        this.updateOpAndFull(this.stoneNode_, this.player.getStone() / this.player.getWarehouseCap(), this.player.getStoneOp());
        var val = this.player.getExpBook();
        if (this.expBookNode_.active = val > 0) {
            this.expBookValLbl.set(val);
        }
        val = this.player.getIron();
        if (this.ironNode_.active = val > 0) {
            this.ironValLbl.set(val);
        }
        val = this.player.getUpScroll();
        if (this.upScrollNode_.active = val > 0) {
            this.upScrollValLbl.set(val);
        }
        val = this.player.getFixator();
        if (this.fixatorNode_.active = val > 0) {
            this.fixatorValLbl.set(val);
        }
        // 季节
        this.updateSeason();
    };
    TopPnlCtrl.prototype.updateOpAndFull = function (node, cap, opVal) {
        node.Child('lay/op', cc.Label).Color(opVal < 0 ? '#F26B50' : '#B6A591').string = ut.numberToString(Math.floor(opVal));
        node.Child('cap/bar', cc.Sprite).Color(cap >= 1 ? '#F26B50' : '#64B7F9').fillRange = cap;
    };
    // 刷新奖励点
    TopPnlCtrl.prototype.updateStamina = function (init) {
        if (!this.staminaNode_.setActive(!GameHelper_1.gameHpr.isFreeServer())) {
            return;
        }
        var stamina = this.player.getStamina();
        this.staminaValLbl.Color(stamina <= 0 ? '#F26B50' : '#564C49');
        this.staminaValLbl.run(stamina, { play: !init });
    };
    // 刷新季节
    TopPnlCtrl.prototype.updateSeason = function () {
        if (!this.seasonNode_.setActive(!GameHelper_1.gameHpr.isNoviceMode && GameHelper_1.gameHpr.isGameRuning)) {
            this.seasonStartTime = 0;
            return;
        }
        var season = GameHelper_1.gameHpr.world.getSeason();
        var type = season.type;
        this.seasonNode_.Child('icon', cc.MultiFrame).setFrame(type);
        this.seasonPointerNode_.x = type * 31;
        var pointer = this.pointerNode;
        pointer.stopAllActions();
        var time = season.getSurplusTime();
        this.seasonElapsedTime = Math.max(0, Constant_1.SEASON_DURATION_TIME - time);
        this.seasonStartTime = Date.now();
        pointer.x = this.seasonElapsedTime / Constant_1.SEASON_DURATION_TIME * 22 + 2;
    };
    TopPnlCtrl.prototype.update = function () {
        if (this.seasonStartTime > 0) {
            var elapsedTime = this.seasonElapsedTime + (Date.now() - this.seasonStartTime);
            if (elapsedTime < Constant_1.SEASON_DURATION_TIME) {
                this.pointerNode.x = elapsedTime / Constant_1.SEASON_DURATION_TIME * 22 + 2;
            }
            else {
                this.pointerNode.x = 24;
                this.seasonStartTime = 0;
            }
        }
    };
    TopPnlCtrl = __decorate([
        ccclass
    ], TopPnlCtrl);
    return TopPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TopPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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