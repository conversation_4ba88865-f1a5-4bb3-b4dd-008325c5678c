
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/SearchRange.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '89ef07AV/JJMZ0CUyu1XbCd', 'SearchRange');
// app/script/common/astar/SearchRange.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 范围搜索
 */
var SearchRange = /** @class */ (function () {
    function SearchRange() {
        this.opened = []; //开放列表
        this.closed = []; //关闭列表
        this.sp = {}; //记录每个格子的移动力
        this.checkIsBattleArea = null; //检测方法
    }
    SearchRange.prototype.init = function (checkIsBattleArea) {
        this.checkIsBattleArea = checkIsBattleArea;
        return this;
    };
    //检查节点
    SearchRange.prototype.findNode = function (point, tx, ty) {
        var step = (this.sp[point.ID()] || 0) - 1;
        if (step < 0 || !this.checkIsBattleArea(tx, ty)) {
            return; //如果已经没有移动力了 或者 有障碍
        }
        var id = tx + '_' + ty;
        var i = this.closed.findIndex(function (m) { return m.equals2(tx, ty); });
        if (i >= 0 && this.sp[id] >= step) {
            return; //如果已经找过 则检查这个点的行动力是否小于当前这个行动力
        }
        // 如果都不是，则代表是个可以拓展的节点 把新的节点的移动力继承
        this.sp[id] = step;
        var p = null;
        if (i >= 0) {
            p = this.closed[i];
        }
        else { //新的点
            p = cc.v2(tx, ty);
            this.closed.push(p);
        }
        // 如果这个点还有移动点 那么就继续扩展
        if (step > 0) {
            this.opened.push(p);
        }
    };
    SearchRange.prototype.search = function (start, step, isRetainCentrePoint) {
        this.opened = [start];
        this.closed = [start];
        this.sp = {};
        this.sp[start.ID()] = step;
        // 开始搜寻
        while (this.opened.length > 0) {
            var p = this.opened.pop();
            this.findNode(p, p.x, p.y - 1); //上
            this.findNode(p, p.x + 1, p.y); //右
            this.findNode(p, p.x, p.y + 1); //下
            this.findNode(p, p.x - 1, p.y); //左
        }
        if (!isRetainCentrePoint) { //是否保留中心点
            this.closed.shift();
        }
        return this.closed;
    };
    return SearchRange;
}());
exports.default = SearchRange;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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