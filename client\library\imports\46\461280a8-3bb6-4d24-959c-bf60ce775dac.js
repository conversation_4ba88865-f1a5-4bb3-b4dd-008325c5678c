"use strict";
cc._RF.push(module, '46128CoO7ZNJJWcv2DOd12s', 'CollectionSkinInfoPnlCtrl');
// app/script/view/menu/CollectionSkinInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var FrameAnimConf_1 = require("../../common/constant/FrameAnimConf");
var GameHelper_1 = require("../../common/helper/GameHelper");
var GotoHelper_1 = require("../../common/helper/GotoHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var PawnFrameAnimationCmpt_1 = require("../cmpt/PawnFrameAnimationCmpt");
var ccclass = cc._decorator.ccclass;
var CollectionSkinInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(CollectionSkinInfoPnlCtrl, _super);
    function CollectionSkinInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.bgNode_ = null; // path://root/bg_n
        _this.nameNode_ = null; // path://root/name_n
        _this.iconNode_ = null; // path://root/icon_n
        _this.timeNode_ = null; // path://root/time_n
        _this.skinExchangeNode_ = null; // path://root/skin_exchange_be_n
        _this.nextNode_ = null; // path://root/next_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.json = null;
        _this.animCmpt = null;
        _this.type = '';
        _this.list = [];
        _this.itemSkin = null;
        _this.curPage = 0;
        _this.maxPage = 0;
        _this.cb = null;
        return _this;
    }
    CollectionSkinInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CollectionSkinInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        this.updateViewInfo(data);
    };
    CollectionSkinInfoPnlCtrl.prototype.onRemove = function () {
    };
    CollectionSkinInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/lay/give_be
    CollectionSkinInfoPnlCtrl.prototype.onClickGive = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/Personal', 0);
    };
    // path://root/buttons_n/lay/use_be
    CollectionSkinInfoPnlCtrl.prototype.onClickUse = function (event, _data) {
        if (GameHelper_1.gameHpr.isInLobby()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_game_use');
        }
        else if (this.type === 'pawn_skin') {
            if (!this.checkPawnUnlock()) {
                return ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [ResHelper_1.resHelper.getPawnName(this.json.pawn_id)] });
            }
            this.syncInfoToServer();
        }
        else {
            this.changeCitySkin();
        }
    };
    // path://root/buttons_n/buy_be
    CollectionSkinInfoPnlCtrl.prototype.onClickBuy = function (event, _data) {
        var _a;
        this.cb && this.cb(((_a = this.list[this.curPage]) === null || _a === void 0 ? void 0 : _a.json) || this.list[this.curPage]);
        this.hide();
    };
    // path://root/skin_exchange_be_n
    CollectionSkinInfoPnlCtrl.prototype.onClickSkinExchange = function (event, _data) {
        var _this = this;
        var _a;
        var id = (_a = assetsMgr.getJson('pawnSkin').datas.find(function (m) { return m.value.toString().includes(_this.json.id + ','); })) === null || _a === void 0 ? void 0 : _a.id;
        ViewHelper_1.viewHelper.showPnl('common/SkinExchange', id);
    };
    // path://root/next_n/next_page_be@0
    CollectionSkinInfoPnlCtrl.prototype.onClickNextPage = function (event, data) {
        var _a;
        var add = data === '0' ? -1 : 1;
        var curPage = this.curPage || 0, maxPage = this.maxPage;
        var index = ut.loopValue(curPage + add, maxPage);
        if (index !== curPage) {
            this.curPage = index;
            var skins = (_a = this.list[index]) === null || _a === void 0 ? void 0 : _a.itemSkins;
            this.updateViewInfo({ list: this.list, type: this.type, index: this.curPage, skins: skins });
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CollectionSkinInfoPnlCtrl.prototype.checkPawnUnlock = function () {
        return GameHelper_1.gameHpr.player.getAllCanRecruitPawnIds().has(this.json.pawn_id);
    };
    CollectionSkinInfoPnlCtrl.prototype.updateViewInfo = function (data) {
        var _a, _b;
        this.type = data.type;
        this.list = data.list;
        this.itemSkin = ((_a = data.skins) === null || _a === void 0 ? void 0 : _a.length) > 0 ? data.skins[0] : null;
        this.curPage = data.index || 0;
        this.maxPage = this.list.length;
        this.json = ((_b = this.list[this.curPage]) === null || _b === void 0 ? void 0 : _b.json) || this.list[this.curPage];
        this.titleLbl_.setLocaleKey(this.json.desc);
        var isPawnSkin = this.type === 'pawn_skin';
        this.nextNode_.active = !!(this.maxPage - 1);
        this.nameNode_.active = isPawnSkin;
        this.timeNode_.active = isPawnSkin && !!this.itemSkin;
        this.bgNode_.Swih(isPawnSkin ? 'pawn' : 'city');
        this.skinExchangeNode_.active = isPawnSkin && this.json.cond > 102 && this.json.type !== 5; // 101牛仔 102机甲没有炫彩且隐藏皮肤不显示
        if (isPawnSkin) {
            var skin = this.itemSkin, state = skin === null || skin === void 0 ? void 0 : skin.state;
            var unlockList = GameHelper_1.gameHpr.user.getUnlockPawnSkinIds(), have = unlockList.has(this.json.id);
            if (this.json.cond > 100 || (this.json.cond === 5 && !have)) { // 盲盒皮肤 || 5 炫彩皮肤(一旦拥有会和盲盒皮肤分开存放)
                if (skin) { // 已经有了
                    if (state < 0) { // 封禁中
                        var cond = this.buttonsNode_.Swih('cond')[0];
                        cond.Child('val').setLocaleKey('toast.ban_item_skin');
                    }
                    else { // 正常 || 锁定中
                        var lay = this.buttonsNode_.Swih('lay')[0];
                        lay.Swih('', true);
                        var giveBtn = lay.Child('give_be', cc.Button);
                        var canGive = state === 0;
                        giveBtn.interactable = canGive;
                        giveBtn.Child('root', cc.MultiFrame).setFrame(canGive);
                        giveBtn.Child('count/val', cc.Label).string = this.itemSkin ? data.skins.length + '' : '';
                        var useBtn = lay.Child('use_be');
                        useBtn.opacity = this.checkPawnUnlock() && !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
                        if (this.timeNode_.active = state > 0) {
                            this.timeNode_.Child('val').setLocaleKey('ui.item_skin_surplus_time', GameHelper_1.gameHpr.millisecondToCountDown(Math.max(0, state)));
                        }
                    }
                }
                else {
                    var cond = this.buttonsNode_.Swih('cond')[0];
                    var type = (this.json.cond > 100 || this.json.cond === 5) ? 2 : this.json.cond;
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type);
                }
            }
            else {
                var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                var buyList = GameHelper_1.gameHpr.user.getCanBuyPawnSkins(serverArea);
                var canBuy = buyList.has('id', this.json.id);
                if (have) {
                    var useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0];
                    useBtn.opacity = this.checkPawnUnlock() && !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
                }
                else if (canBuy || this.json.cond === 3) {
                    var buy = this.buttonsNode_.Swih('buy_be')[0];
                    buy.Data = this.json;
                    ViewHelper_1.viewHelper.updateCostText(buy, this.json);
                }
                else {
                    var cond = this.buttonsNode_.Swih('cond')[0];
                    var type = this.json.cond === 1 ? 2 : this.json.cond;
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type);
                }
            }
        }
        else {
            var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
            var buyList = GameHelper_1.gameHpr.user.getCanBuyCitySkins(serverArea);
            var unlockList = GameHelper_1.gameHpr.user.getUnlockCitySkinIds();
            var canBuy = buyList.has('id', this.json.id);
            var have = unlockList.has(this.json.id);
            if (have) {
                var useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0];
                useBtn.opacity = !GameHelper_1.gameHpr.isInLobby() ? 255 : 150;
            }
            else if (canBuy || this.json.cond === 3) {
                var buy = this.buttonsNode_.Swih('buy_be')[0];
                buy.Data = this.json;
                ViewHelper_1.viewHelper.updateCostText(buy, this.json);
            }
            else {
                var cond = this.buttonsNode_.Swih('cond')[0];
                var type = this.json.cond === 1 ? 2 : this.json.cond;
                cond.Child('val').setLocaleKey('ui.get_cond_' + type);
            }
        }
        this.loadSkin(isPawnSkin);
    };
    CollectionSkinInfoPnlCtrl.prototype.loadSkin = function (isPawnSkin) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var json, root, pfb, node, conf;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        json = this.json;
                        root = this.iconNode_.Child('mask').Swih(isPawnSkin ? 'root' : 'val')[0];
                        if (!isPawnSkin) return [3 /*break*/, 2];
                        root.removeAllChildren();
                        this.animCmpt = null;
                        this.nameNode_.Child('val').setLocaleKey(ResHelper_1.resHelper.getPawnName(json.pawn_id));
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/ROLE_' + json.id, cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        node = cc.instantiate2(pfb, root);
                        conf = FrameAnimConf_1.SHOP_PAWN_SKIN_ANIM_CONF[json.id];
                        if (!!conf) {
                            this.animCmpt = node.addComponent(PawnFrameAnimationCmpt_1.default).init(node.FindChild('body/anim', cc.Sprite), json.id, this.key);
                            this.animCmpt.play();
                        }
                        else {
                            (_a = node.Child('body/anim', cc.Animation)) === null || _a === void 0 ? void 0 : _a.play('role_' + json.id + '_walk');
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        ResHelper_1.resHelper.loadCityIcon(json.id, root, this.key);
                        _b.label = 3;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.syncInfoToServer = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawnId, skinId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        pawnId = this.json.pawn_id, skinId = this.json.id;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_UsePawnSkin', { pawnId: pawnId, skinId: skinId })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.PAWN_NOT_EXIST) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] })];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (data.hasPawn) {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] });
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.replace_def_pawn_skin_succ', { params: [ResHelper_1.resHelper.getPawnName(pawnId)] });
                        }
                        GameHelper_1.gameHpr.player.changeConfigPawnSkinId(pawnId, skinId);
                        this.hide();
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.changeCitySkin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var playerInfo, index, id, skinId, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        playerInfo = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid()), index = (playerInfo === null || playerInfo === void 0 ? void 0 : playerInfo.mainCityIndex) || -1, id = this.json.id, skinId = id === playerInfo.cells.get(index).cityId ? 0 : id;
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_ChangeCitySkin', { index: index, skinId: skinId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        GameHelper_1.gameHpr.world.updateCitySkin(index, skinId);
                        ViewHelper_1.viewHelper.hidePnl('menu/Collection');
                        this.hide();
                        GotoHelper_1.gotoHelper.gotoMainCity();
                        return [2 /*return*/];
                }
            });
        });
    };
    CollectionSkinInfoPnlCtrl.prototype.update = function (dt) {
        var _a;
        (_a = this.animCmpt) === null || _a === void 0 ? void 0 : _a.updateFrame(dt * 1000);
    };
    CollectionSkinInfoPnlCtrl = __decorate([
        ccclass
    ], CollectionSkinInfoPnlCtrl);
    return CollectionSkinInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CollectionSkinInfoPnlCtrl;

cc._RF.pop();