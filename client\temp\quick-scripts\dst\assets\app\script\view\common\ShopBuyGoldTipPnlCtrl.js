
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ShopBuyGoldTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f0eda2YGEJJw54DXD9vpgcJ', 'ShopBuyGoldTipPnlCtrl');
// app/script/view/common/ShopBuyGoldTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ShopBuyGoldTipPnlCtrl = /** @class */ (function (_super) {
    __extends(ShopBuyGoldTipPnlCtrl, _super);
    function ShopBuyGoldTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.ingotLbl_ = null; // path://root/content/ingot/ingot_l
        _this.goldLbl_ = null; // path://root/content/gold/gold_l
        _this.inputEb_ = null; // path://root/input/bg/input_eb_ebee
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    ShopBuyGoldTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ShopBuyGoldTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ShopBuyGoldTipPnlCtrl.prototype.onEnter = function () {
        var user = GameHelper_1.gameHpr.user;
        this.ingotLbl_.string = user.getIngot() + '';
        this.goldLbl_.string = user.getGold() + '';
    };
    ShopBuyGoldTipPnlCtrl.prototype.onRemove = function () {
    };
    ShopBuyGoldTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/input/bg/input_eb_ebee
    ShopBuyGoldTipPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var count = Number(event.string.trim());
        if (!count) {
            event.string = '';
            return;
        }
        var cnt = count, maxCount = GameHelper_1.gameHpr.user.getIngot();
        if (count > maxCount) {
            cnt = maxCount;
        }
        if (cnt !== count) {
            event.string = '' + cnt;
        }
    };
    // path://root/input/max_be
    ShopBuyGoldTipPnlCtrl.prototype.onClickMax = function (event, data) {
        var ingot = GameHelper_1.gameHpr.user.getIngot();
        this.inputEb_.string = ingot ? ingot + '' : '';
    };
    // path://root/buttons/ok_be
    ShopBuyGoldTipPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var count = Number(this.inputEb_.string.trim());
        if (!count) {
            return ViewHelper_1.viewHelper.showAlert('ui.input_buy_gold_count_desc');
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.ingot_exchange_gold_alert', {
            params: [count],
            ok: function () {
                GameHelper_1.gameHpr.user.exchangeGold(count).then(function (err) {
                    if (err) {
                        return ViewHelper_1.viewHelper.showAlert(err);
                    }
                    else if (_this.isValid) {
                        _this.inputEb_.string = '';
                        _this.hide();
                        ViewHelper_1.viewHelper.showAlert('toast.exchange_succeed');
                    }
                });
            },
            cancel: function () { }
        });
    };
    ShopBuyGoldTipPnlCtrl = __decorate([
        ccclass
    ], ShopBuyGoldTipPnlCtrl);
    return ShopBuyGoldTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ShopBuyGoldTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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