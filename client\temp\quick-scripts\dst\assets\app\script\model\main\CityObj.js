
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/CityObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0426YHdFVPCbIfzQ9jhPUl', 'CityObj');
// app/script/model/main/CityObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var MapHelper_1 = require("../../common/helper/MapHelper");
// 一个地图城市
var CityObj = /** @class */ (function () {
    function CityObj() {
        this.id = 0;
        this.index = 0; //所在地图的index
        this.point = cc.v2();
        this.actPoint = cc.v2();
        this.position = cc.v2();
        this.name = ''; //城市名字
        this.json = null;
        this.size = cc.v2();
    }
    CityObj.prototype.init = function (idnex, id, point) {
        this.index = idnex;
        this.id = id;
        this.point.set(point);
        var json = this.json = assetsMgr.getJsonData('city', id);
        this.size = ut.stringToVec2(json.cell_size, 'x');
        // 实际的点位置
        this.actPoint.set(point);
        this.actPoint.x += (this.size.x - 1) * 0.5;
        this.actPoint.y += (this.size.y - 1) * 0.5;
        // 计算位置
        MapHelper_1.mapHelper.getPixelByPoint(point, this.position);
        this.position.x += (this.size.x - 1) * Constant_1.TILE_SIZE_HALF.x;
        this.position.y += (this.size.y - 1) * Constant_1.TILE_SIZE_HALF.y;
        return this;
    };
    Object.defineProperty(CityObj.prototype, "icon", {
        get: function () { var _a; return ((_a = this.json) === null || _a === void 0 ? void 0 : _a.icon) || ''; },
        enumerable: false,
        configurable: true
    });
    CityObj.prototype.setName = function (val) {
        this.name = val || 'cityText.name_' + this.id;
    };
    return CityObj;
}());
exports.default = CityObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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