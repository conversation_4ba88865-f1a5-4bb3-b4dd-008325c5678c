
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/NoticeCtrlMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '14e5aJLEFFMyZGKk+zJGoFD', 'NoticeCtrlMgr');
// app/core/manage/NoticeCtrlMgr.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var BaseNoticeCtrl_1 = require("../base/BaseNoticeCtrl");
var ResLoader_1 = require("../utils/ResLoader");
var NoticeCtrlMgr = /** @class */ (function () {
    function NoticeCtrlMgr() {
        this.node = null;
        this.caches = new Map();
    }
    NoticeCtrlMgr.prototype.readyNot = function (pfb) {
        return __awaiter(this, void 0, void 0, function () {
            var it, className, notice;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.caches.has(pfb.name)) {
                            return [2 /*return*/];
                        }
                        it = cc.instantiate2(pfb, this.node);
                        className = it.name + "Ctrl";
                        if (!cc.js.getClassByName(className)) {
                            return [2 /*return*/, logger.error('loadNotice error! not found class ' + className)];
                        }
                        notice = it.getComponent(className) || it.addComponent(className);
                        if (!notice || !(notice instanceof BaseNoticeCtrl_1.default)) {
                            return [2 /*return*/, logger.error('loadNotice error! not found class ' + className)];
                        }
                        this.adaptRootSize(notice);
                        notice.hide();
                        return [4 /*yield*/, notice.__create()];
                    case 1:
                        _a.sent();
                        this.caches.set(pfb.name, notice);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载所有not
    NoticeCtrlMgr.prototype.loadAll = function (root, complete, progress) {
        return __awaiter(this, void 0, void 0, function () {
            var debug, assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        debug = ResLoader_1.loader.debug;
                        ResLoader_1.loader.debug = false;
                        return [4 /*yield*/, ResLoader_1.loader.loadResDir(root, cc.Prefab, progress)];
                    case 1:
                        assets = _a.sent();
                        ResLoader_1.loader.debug = debug;
                        return [4 /*yield*/, Promise.all(assets.map(function (pfb) { return _this.readyNot(pfb); }))];
                    case 2:
                        _a.sent();
                        complete && complete();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载指定not
    NoticeCtrlMgr.prototype.loadNot = function (root, val, complete) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.all(val.split('|').map(function (name) { return __awaiter(_this, void 0, void 0, function () {
                            var pfb;
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0:
                                        if (!name) {
                                            return [2 /*return*/];
                                        }
                                        return [4 /*yield*/, ResLoader_1.loader.loadRes(root + '/' + name, cc.Prefab)];
                                    case 1:
                                        pfb = _a.sent();
                                        return [2 /*return*/, this.readyNot(pfb)];
                                }
                            });
                        }); }))];
                    case 1:
                        _a.sent();
                        complete && complete();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 适应大小
    NoticeCtrlMgr.prototype.adaptRootSize = function (not) {
        if (!not) {
            return;
        }
        var root = not.Child('root') || not.Child('root_n');
        if (!root) {
            return;
        }
        var wsize = cc.winSize;
        var dsize = cc.view.getDesignResolutionSize();
        var rsize = root.getContentSize();
        // 算出宽度比例
        var scale = (rsize.width / dsize.width * wsize.width) / rsize.width;
        // 如果高度超过了
        var height = wsize.height - not.adaptHeight;
        if (rsize.height * scale > height) {
            scale = height / rsize.height;
        }
        root.scale = not.initScale = Math.min(1.2, scale);
    };
    return NoticeCtrlMgr;
}());
exports.default = NoticeCtrlMgr;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxtYW5hZ2VcXE5vdGljZUN0cmxNZ3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx5REFBbUQ7QUFDbkQsZ0RBQTJDO0FBRTNDO0lBQUE7UUFFVyxTQUFJLEdBQVksSUFBSSxDQUFBO1FBRW5CLFdBQU0sR0FBZ0MsSUFBSSxHQUFHLEVBQTBCLENBQUE7SUFnRW5GLENBQUM7SUE5RGlCLGdDQUFRLEdBQXRCLFVBQXVCLEdBQWM7Ozs7Ozt3QkFDakMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7NEJBQzNCLHNCQUFNO3lCQUNUO3dCQUNLLEVBQUUsR0FBWSxFQUFFLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7d0JBQzdDLFNBQVMsR0FBRyxFQUFFLENBQUMsSUFBSSxHQUFHLE1BQU0sQ0FBQTt3QkFDbEMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsY0FBYyxDQUFDLFNBQVMsQ0FBQyxFQUFFOzRCQUNsQyxzQkFBTyxNQUFNLENBQUMsS0FBSyxDQUFDLG9DQUFvQyxHQUFHLFNBQVMsQ0FBQyxFQUFBO3lCQUN4RTt3QkFDSyxNQUFNLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLENBQUMsWUFBWSxDQUFDLFNBQVMsQ0FBQyxDQUFBO3dCQUN2RSxJQUFJLENBQUMsTUFBTSxJQUFJLENBQUMsQ0FBQyxNQUFNLFlBQVksd0JBQWMsQ0FBQyxFQUFFOzRCQUNoRCxzQkFBTyxNQUFNLENBQUMsS0FBSyxDQUFDLG9DQUFvQyxHQUFHLFNBQVMsQ0FBQyxFQUFBO3lCQUN4RTt3QkFDRCxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFBO3dCQUMxQixNQUFNLENBQUMsSUFBSSxFQUFFLENBQUE7d0JBQ2IscUJBQU0sTUFBTSxDQUFDLFFBQVEsRUFBRSxFQUFBOzt3QkFBdkIsU0FBdUIsQ0FBQTt3QkFDdkIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQTs7Ozs7S0FDcEM7SUFFRCxVQUFVO0lBQ0csK0JBQU8sR0FBcEIsVUFBcUIsSUFBWSxFQUFFLFFBQW1CLEVBQUUsUUFBZ0Q7Ozs7Ozs7d0JBQzlGLEtBQUssR0FBRyxrQkFBTSxDQUFDLEtBQUssQ0FBQTt3QkFDMUIsa0JBQU0sQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFBO3dCQUNMLHFCQUFNLGtCQUFNLENBQUMsVUFBVSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsTUFBTSxFQUFFLFFBQVEsQ0FBQyxFQUFBOzt3QkFBM0QsTUFBTSxHQUFHLFNBQWtEO3dCQUNqRSxrQkFBTSxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUE7d0JBQ3BCLHFCQUFNLE9BQU8sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxVQUFBLEdBQUcsSUFBSSxPQUFBLEtBQUksQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQWxCLENBQWtCLENBQUMsQ0FBQyxFQUFBOzt3QkFBeEQsU0FBd0QsQ0FBQTt3QkFDeEQsUUFBUSxJQUFJLFFBQVEsRUFBRSxDQUFBOzs7OztLQUN6QjtJQUVELFVBQVU7SUFDRywrQkFBTyxHQUFwQixVQUFxQixJQUFZLEVBQUUsR0FBVyxFQUFFLFFBQW1COzs7Ozs0QkFDL0QscUJBQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxVQUFPLElBQUk7Ozs7O3dDQUM1QyxJQUFJLENBQUMsSUFBSSxFQUFFOzRDQUNQLHNCQUFNO3lDQUNUO3dDQUNXLHFCQUFNLGtCQUFNLENBQUMsT0FBTyxDQUFDLElBQUksR0FBRyxHQUFHLEdBQUcsSUFBSSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsRUFBQTs7d0NBQXhELEdBQUcsR0FBRyxTQUFrRDt3Q0FDOUQsc0JBQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsRUFBQTs7OzZCQUM1QixDQUFDLENBQUMsRUFBQTs7d0JBTkgsU0FNRyxDQUFBO3dCQUNILFFBQVEsSUFBSSxRQUFRLEVBQUUsQ0FBQTs7Ozs7S0FDekI7SUFFRCxPQUFPO0lBQ0MscUNBQWEsR0FBckIsVUFBc0IsR0FBbUI7UUFDckMsSUFBSSxDQUFDLEdBQUcsRUFBRTtZQUNOLE9BQU07U0FDVDtRQUNELElBQU0sSUFBSSxHQUFHLEdBQUcsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksR0FBRyxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUNyRCxJQUFJLENBQUMsSUFBSSxFQUFFO1lBQ1AsT0FBTTtTQUNUO1FBQ0QsSUFBTSxLQUFLLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQTtRQUN4QixJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsSUFBSSxDQUFDLHVCQUF1QixFQUFFLENBQUE7UUFDL0MsSUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO1FBQ25DLFNBQVM7UUFDVCxJQUFJLEtBQUssR0FBRyxDQUFDLEtBQUssQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFDLEtBQUssR0FBRyxLQUFLLENBQUMsS0FBSyxDQUFDLEdBQUcsS0FBSyxDQUFDLEtBQUssQ0FBQTtRQUNuRSxVQUFVO1FBQ1YsSUFBTSxNQUFNLEdBQUcsS0FBSyxDQUFDLE1BQU0sR0FBRyxHQUFHLENBQUMsV0FBVyxDQUFBO1FBQzdDLElBQUksS0FBSyxDQUFDLE1BQU0sR0FBRyxLQUFLLEdBQUcsTUFBTSxFQUFFO1lBQy9CLEtBQUssR0FBRyxNQUFNLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQTtTQUNoQztRQUNELElBQUksQ0FBQyxLQUFLLEdBQUcsR0FBRyxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxLQUFLLENBQUMsQ0FBQTtJQUNyRCxDQUFDO0lBQ0wsb0JBQUM7QUFBRCxDQXBFQSxBQW9FQyxJQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJhc2VOb3RpY2VDdHJsIGZyb20gXCIuLi9iYXNlL0Jhc2VOb3RpY2VDdHJsXCJcbmltcG9ydCB7IGxvYWRlciB9IGZyb20gXCIuLi91dGlscy9SZXNMb2FkZXJcIlxuXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBOb3RpY2VDdHJsTWdyIHtcblxuICAgIHB1YmxpYyBub2RlOiBjYy5Ob2RlID0gbnVsbFxuXG4gICAgcHJpdmF0ZSBjYWNoZXM6IE1hcDxzdHJpbmcsIEJhc2VOb3RpY2VDdHJsPiA9IG5ldyBNYXA8c3RyaW5nLCBCYXNlTm90aWNlQ3RybD4oKVxuXG4gICAgcHJpdmF0ZSBhc3luYyByZWFkeU5vdChwZmI6IGNjLlByZWZhYikge1xuICAgICAgICBpZiAodGhpcy5jYWNoZXMuaGFzKHBmYi5uYW1lKSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaXQ6IGNjLk5vZGUgPSBjYy5pbnN0YW50aWF0ZTIocGZiLCB0aGlzLm5vZGUpXG4gICAgICAgIGNvbnN0IGNsYXNzTmFtZSA9IGl0Lm5hbWUgKyBgQ3RybGBcbiAgICAgICAgaWYgKCFjYy5qcy5nZXRDbGFzc0J5TmFtZShjbGFzc05hbWUpKSB7XG4gICAgICAgICAgICByZXR1cm4gbG9nZ2VyLmVycm9yKCdsb2FkTm90aWNlIGVycm9yISBub3QgZm91bmQgY2xhc3MgJyArIGNsYXNzTmFtZSlcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBub3RpY2UgPSBpdC5nZXRDb21wb25lbnQoY2xhc3NOYW1lKSB8fCBpdC5hZGRDb21wb25lbnQoY2xhc3NOYW1lKVxuICAgICAgICBpZiAoIW5vdGljZSB8fCAhKG5vdGljZSBpbnN0YW5jZW9mIEJhc2VOb3RpY2VDdHJsKSkge1xuICAgICAgICAgICAgcmV0dXJuIGxvZ2dlci5lcnJvcignbG9hZE5vdGljZSBlcnJvciEgbm90IGZvdW5kIGNsYXNzICcgKyBjbGFzc05hbWUpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5hZGFwdFJvb3RTaXplKG5vdGljZSlcbiAgICAgICAgbm90aWNlLmhpZGUoKVxuICAgICAgICBhd2FpdCBub3RpY2UuX19jcmVhdGUoKVxuICAgICAgICB0aGlzLmNhY2hlcy5zZXQocGZiLm5hbWUsIG5vdGljZSlcbiAgICB9XG5cbiAgICAvLyDliqDovb3miYDmnIlub3RcbiAgICBwdWJsaWMgYXN5bmMgbG9hZEFsbChyb290OiBzdHJpbmcsIGNvbXBsZXRlPzogRnVuY3Rpb24sIHByb2dyZXNzPzogKGRvbmU6IG51bWJlciwgdG90YWw6IG51bWJlcikgPT4gdm9pZCkge1xuICAgICAgICBjb25zdCBkZWJ1ZyA9IGxvYWRlci5kZWJ1Z1xuICAgICAgICBsb2FkZXIuZGVidWcgPSBmYWxzZVxuICAgICAgICBjb25zdCBhc3NldHMgPSBhd2FpdCBsb2FkZXIubG9hZFJlc0Rpcihyb290LCBjYy5QcmVmYWIsIHByb2dyZXNzKVxuICAgICAgICBsb2FkZXIuZGVidWcgPSBkZWJ1Z1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChhc3NldHMubWFwKHBmYiA9PiB0aGlzLnJlYWR5Tm90KHBmYikpKVxuICAgICAgICBjb21wbGV0ZSAmJiBjb21wbGV0ZSgpXG4gICAgfVxuXG4gICAgLy8g5Yqg6L295oyH5a6abm90XG4gICAgcHVibGljIGFzeW5jIGxvYWROb3Qocm9vdDogc3RyaW5nLCB2YWw6IHN0cmluZywgY29tcGxldGU/OiBGdW5jdGlvbikge1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbCh2YWwuc3BsaXQoJ3wnKS5tYXAoYXN5bmMgKG5hbWUpID0+IHtcbiAgICAgICAgICAgIGlmICghbmFtZSkge1xuICAgICAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgcGZiID0gYXdhaXQgbG9hZGVyLmxvYWRSZXMocm9vdCArICcvJyArIG5hbWUsIGNjLlByZWZhYilcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnJlYWR5Tm90KHBmYilcbiAgICAgICAgfSkpXG4gICAgICAgIGNvbXBsZXRlICYmIGNvbXBsZXRlKClcbiAgICB9XG5cbiAgICAvLyDpgILlupTlpKflsI9cbiAgICBwcml2YXRlIGFkYXB0Um9vdFNpemUobm90OiBCYXNlTm90aWNlQ3RybCkge1xuICAgICAgICBpZiAoIW5vdCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgcm9vdCA9IG5vdC5DaGlsZCgncm9vdCcpIHx8IG5vdC5DaGlsZCgncm9vdF9uJylcbiAgICAgICAgaWYgKCFyb290KSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCB3c2l6ZSA9IGNjLndpblNpemVcbiAgICAgICAgY29uc3QgZHNpemUgPSBjYy52aWV3LmdldERlc2lnblJlc29sdXRpb25TaXplKClcbiAgICAgICAgY29uc3QgcnNpemUgPSByb290LmdldENvbnRlbnRTaXplKClcbiAgICAgICAgLy8g566X5Ye65a695bqm5q+U5L6LXG4gICAgICAgIGxldCBzY2FsZSA9IChyc2l6ZS53aWR0aCAvIGRzaXplLndpZHRoICogd3NpemUud2lkdGgpIC8gcnNpemUud2lkdGhcbiAgICAgICAgLy8g5aaC5p6c6auY5bqm6LaF6L+H5LqGXG4gICAgICAgIGNvbnN0IGhlaWdodCA9IHdzaXplLmhlaWdodCAtIG5vdC5hZGFwdEhlaWdodFxuICAgICAgICBpZiAocnNpemUuaGVpZ2h0ICogc2NhbGUgPiBoZWlnaHQpIHtcbiAgICAgICAgICAgIHNjYWxlID0gaGVpZ2h0IC8gcnNpemUuaGVpZ2h0XG4gICAgICAgIH1cbiAgICAgICAgcm9vdC5zY2FsZSA9IG5vdC5pbml0U2NhbGUgPSBNYXRoLm1pbigxLjIsIHNjYWxlKVxuICAgIH1cbn0iXX0=