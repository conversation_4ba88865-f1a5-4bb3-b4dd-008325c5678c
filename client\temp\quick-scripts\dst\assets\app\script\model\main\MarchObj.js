
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/MarchObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd7292ppeqVId5L+Hr7gYSjl', 'MarchObj');
// app/script/model/main/MarchObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var BaseMarchObj_1 = require("./BaseMarchObj");
// 一个行军信息
var MarchObj = /** @class */ (function (_super) {
    __extends(MarchObj, _super);
    function MarchObj() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.armyUid = ''; //军队uid
        _this.armyName = ''; //军队名字
        _this.armyIndex = 0; //军队位置
        _this.roleId = 0; //行军角色id
        _this.autoRevoke = false; //是否被遣返
        _this.forceRevoke = false; //是否被强制撤离
        _this.cellTonden = false; //是否屯田
        _this.notifyIndex = 0; //通知序号 用于UI排序显示
        return _this;
    }
    MarchObj.prototype.init = function (data) {
        this.uid = data.uid;
        this.owner = data.owner;
        this.armyUid = data.armyUid;
        this.armyName = data.armyName;
        this.armyIndex = data.armyIndex;
        this.roleId = data.roleId;
        this.startIndex = data.startIndex;
        this.targetIndex = data.targetIndex;
        this.needTime = data.needTime;
        this.autoRevoke = !!data.autoRevoke;
        this.forceRevoke = !!data.forceRevoke;
        this.cellTonden = !!data.cellTonden;
        this.notifyIndex = data.notifyIndex || 0;
        this.surplusTime = data.surplusTime;
        this.initTime = Date.now();
        this.initBase();
        this.targetType = this.initTargetType();
        return this;
    };
    // 目标类型
    MarchObj.prototype.initTargetType = function () {
        _super.prototype.initTargetType.call(this);
        var uid = GameHelper_1.gameHpr.getUid(), iwOwner = this.owner === uid, isCanForceRevoke = this.isCanForceRevoke();
        if (!iwOwner && this.targetUid !== uid && !isCanForceRevoke) {
            return Enums_1.MarchTargetType.NONE;
        }
        else if (this.forceRevoke && iwOwner) {
            return Enums_1.MarchTargetType.FORCE_REVOKE;
        }
        else if (this.autoRevoke) {
            return Enums_1.MarchTargetType.AUTO_REVOKE;
        }
        else if (this.cellTonden) {
            return Enums_1.MarchTargetType.TONDEN;
        }
        else if (this.armyIndex === this.targetIndex) {
            return Enums_1.MarchTargetType.REVOKE;
        }
        else if (iwOwner) { //自己的军队
            if (this.targetUid === uid) {
                return Enums_1.MarchTargetType.MOVE;
            }
            else if (GameHelper_1.gameHpr.world.checkCanOccupyCellByIndex(this.targetIndex)) {
                return Enums_1.MarchTargetType.ATTACK;
            }
            else {
                return Enums_1.MarchTargetType.MOVE;
            }
        }
        else if (this.targetUid === uid) { //到我地块的军队
            if (GameHelper_1.gameHpr.isOneAlliance(this.owner)) {
                return Enums_1.MarchTargetType.MOVE;
            }
            else {
                return Enums_1.MarchTargetType.STRIKE;
            }
        }
        else if (isCanForceRevoke) { //可以撤离的军队
            return Enums_1.MarchTargetType.MOVE;
        }
        return Enums_1.MarchTargetType.NONE;
    };
    MarchObj.prototype.updateTargetType = function () {
        this.targetType = this.initTargetType();
        return this.targetType;
    };
    // 是否可以强制撤离
    MarchObj.prototype.isCanForceRevoke = function () {
        if (this.forceRevoke || this.owner === GameHelper_1.gameHpr.getUid() || !GameHelper_1.gameHpr.isOneAlliance(this.owner)) {
            return false; //已经撤离 或者自己的军队 和 敌方军队 不可强制撤离
        }
        var mainIndex = GameHelper_1.gameHpr.player.getMainCityIndex();
        if (this.targetIndex === mainIndex) {
            return true; //前往我的主城
        }
        else if (this.armyIndex === mainIndex) {
            return true; //军队在我的主城
        }
        return false;
    };
    // 是否军队
    MarchObj.prototype.isArmy = function () { return true; };
    // 是否英雄
    MarchObj.prototype.isHero = function () { return String(this.roleId).length === 6; };
    MarchObj.prototype.getArmyName = function () { return this.armyName; };
    // 获取移动角色的url
    MarchObj.prototype.getMarchRoleUrl = function () {
        return 'march/ROLE_' + this.roleId;
    };
    MarchObj.prototype.getMarchInfoUrl = function () {
        return 'march/MARCH_ARMY_INFO';
    };
    MarchObj.prototype.getMarchRoleAnim = function () {
        return 'role_' + this.roleId + '_walk';
    };
    // 行军线类型
    MarchObj.prototype.getMarchLineType = function () {
        if (this.owner === GameHelper_1.gameHpr.getUid()) {
            return Enums_1.MarchLineType.SELF_ARMY;
        }
        else if (GameHelper_1.gameHpr.isOneAlliance(this.owner)) {
            return Enums_1.MarchLineType.ALLI_ARMY;
        }
        return Enums_1.MarchLineType.OTHER_ARMY;
    };
    // 是否可以取消行军
    MarchObj.prototype.isCanCancel = function () {
        var _a;
        return this.owner === GameHelper_1.gameHpr.getUid()
            && !this.autoRevoke
            && ((_a = GameHelper_1.gameHpr.world.getMapCellByIndex(this.startIndex)) === null || _a === void 0 ? void 0 : _a.isOneAlliance())
            && (this.targetType === Enums_1.MarchTargetType.ATTACK || this.targetType === Enums_1.MarchTargetType.MOVE || this.targetType === Enums_1.MarchTargetType.TONDEN);
    };
    // 是否可以显示行军
    MarchObj.prototype.isCanShowMarch = function () {
        var _a;
        if (this.getMarchLineType() !== Enums_1.MarchLineType.OTHER_ARMY || this.targetType !== Enums_1.MarchTargetType.NONE) {
            return true;
        }
        // const uid = gameHpr.getUid()
        // const t = this.targetUid
        // // 起始位置和目标位置是自己的就显示 或者目标位置是盟友的
        // return t === uid || gameHpr.world.getMapCellByIndex(this.targetIndex)?.isOneAlliance()
        // 目标位置是自己的就显示 或者目标位置是盟友的
        return (_a = GameHelper_1.gameHpr.world.getMapCellByIndex(this.targetIndex)) === null || _a === void 0 ? void 0 : _a.isOneAlliance();
    };
    return MarchObj;
}(BaseMarchObj_1.default));
exports.default = MarchObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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