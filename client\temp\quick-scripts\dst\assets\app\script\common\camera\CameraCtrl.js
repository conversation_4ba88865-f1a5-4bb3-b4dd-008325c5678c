
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/camera/CameraCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '14fcewMZ/tKPIaKCwZmCubT', 'CameraCtrl');
// app/script/common/camera/CameraCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cameraCtrl = void 0;
var Constant_1 = require("../../common/constant/Constant");
var MapHelper_1 = require("../helper/MapHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 摄像机控制
 */
var CameraCtrl = /** @class */ (function (_super) {
    __extends(CameraCtrl, _super);
    function CameraCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.camera = null;
        _this.ZOOM_RATIO_MAXRANGE = cc.v2(0.8, 1.2); //缩放最大范围
        _this.INIT_ZOOM_RATIO = 0.87; //初始的缩放比例
        _this.SHAKE_STRENGTH = 16; //震动强度
        _this.smoothing = 10; // 缓冲
        _this.targetNode = null;
        _this.targetPositionOgn = cc.v2();
        _this.targetPositionCam = cc.v2();
        _this.followCallback = null; //跟随回调
        _this.isFollowBottom = false; //跟随的时候 是否下对其
        _this.followOffset = cc.v2(); //跟随偏移
        _this.moveing = false; //是否移动中
        _this.banMove = false;
        _this.shakeing = false; //是否震屏中
        _this.camPosition = cc.v2();
        _this.preZoomRatio = -1;
        _this.mapSize = cc.v2(); // 地图大小
        _this.tempMapSize = cc.v2(); // 地图大小
        _this.tempOrigin = cc.v2(); // 地图大小
        _this.maxRange = cc.v2(); // 最大范围
        _this.minRange = cc.v2(); // 最小范围
        _this.worldMapSize = cc.v2(); //世界地图大小
        _this.worldWinSize = cc.v2(); //世界屏幕大小
        _this.minRangeOffset = cc.v2(); //最小范围偏移
        _this.winSize = cc.v2();
        _this.winSizeHalf = cc.v2();
        _this.winGirdSize = cc.v2();
        _this.recordPos = cc.v2(); // 当前位置 用于拖动前的记录
        _this.dragSpeed = 1; //拖动速度
        _this.lastDragTime = 0; //最后一次拖动时间
        _this.shakeDuration = 0; //震动持续时间（秒）
        _this.shakeEnableDecay = true; //是否启用衰减效果
        _this.shakeOriginalPos = cc.v2(0, 0); // 记录原始位置
        _this.shakeTimer = 0;
        _this.tempPosition = cc.v2();
        _this._temp_vec2_0 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        _this._temp_vec2_5 = cc.v2();
        _this._temp_vec2_6 = cc.v2();
        _this._temp_vec2_7 = cc.v2();
        _this._temp_vec2_8 = cc.v2();
        _this._temp_vec2_9 = cc.v2();
        _this._temp_vec2_10 = cc.v2();
        _this._temp_vec2_11 = cc.v2();
        _this._temp_vec2_12 = cc.v2();
        _this._temp_vec2_13 = cc.v2();
        return _this;
    }
    CameraCtrl.prototype.onLoad = function () {
        exports.cameraCtrl = this;
        if (cc.sys.isBrowser) {
            window['cameraCtrl'] = this;
        }
        this.camera = this.getComponent(cc.Camera);
        this.recordPosition();
        this.winSize.x = cc.winSize.width;
        this.winSize.y = cc.winSize.height;
        this.winSize.mul(0.5, this.winSizeHalf);
        this.winSize.div(Constant_1.TILE_SIZE, this.winGirdSize).ceil();
    };
    CameraCtrl.prototype.init = function (centre, size, minRangeOffset, zoomRatio) {
        this.initByPosition(minRangeOffset || cc.Vec2.ZERO, centre.sub(this.winSizeHalf, this._temp_vec2_6), zoomRatio || this.INIT_ZOOM_RATIO, size);
    };
    CameraCtrl.prototype.initByPosition = function (minRangeOffset, pos, zoomRatio, size) {
        size = size || MapHelper_1.mapHelper.MAP_SIZE;
        this.minRangeOffset.set(minRangeOffset);
        this.setMaxRange(size.mul(Constant_1.TILE_SIZE, this._temp_vec2_4));
        this.setTarget(null);
        this.setZoomRatio(zoomRatio, true);
        this.setPosition(pos);
    };
    CameraCtrl.prototype.reset = function () {
        this.camera.zoomRatio = this.INIT_ZOOM_RATIO;
        this.node.setPosition(0, 0);
    };
    // 获取摄像机
    CameraCtrl.prototype.getCamera = function () {
        return this.camera;
    };
    // 获取摄像机位置
    CameraCtrl.prototype.getPosition = function () {
        return this.node.getPosition(this.tempPosition);
    };
    // 获取相机在世界坐标中心点位置
    CameraCtrl.prototype.getCentrePosition = function () {
        return this.getPosition().addSelf(this.winSizeHalf);
    };
    CameraCtrl.prototype.getWinSizeHalf = function () {
        return this.winSizeHalf;
    };
    // 屏幕大小的格子数
    CameraCtrl.prototype.getWinGirdSize = function () {
        return this.winGirdSize;
    };
    CameraCtrl.prototype.getWorldWinSize = function () {
        return this.worldWinSize;
    };
    // 将屏幕点击点转换到世界坐标
    CameraCtrl.prototype.getScreenToWorldPoint = function (point, out) {
        out = out || this._temp_vec2_0;
        return this.camera.getScreenToWorldPoint(point, out);
    };
    // 将世界点转换到屏幕坐标
    CameraCtrl.prototype.getWorldToScreenPoint = function (point, out) {
        out = out || this._temp_vec2_0;
        return this.camera.getWorldToScreenPoint(point, out);
    };
    // 世界点 是否在屏幕范围内
    CameraCtrl.prototype.isInScreenRangeByWorld = function (pos) {
        var p = this.getWorldToScreenPoint(pos);
        var max = this.worldWinSize;
        return p.x < 0 || p.x > max.x || p.y < 0 || p.y > max.y;
    };
    Object.defineProperty(CameraCtrl.prototype, "zoomRatio", {
        get: function () {
            return this.camera.zoomRatio;
        },
        // 设置缩放比
        set: function (val) {
            this.setZoomRatio(val, false);
        },
        enumerable: false,
        configurable: true
    });
    CameraCtrl.prototype.setZoomRatio = function (val, init) {
        val = cc.misc.clampf(val, this.ZOOM_RATIO_MAXRANGE.x, this.ZOOM_RATIO_MAXRANGE.y);
        if (this.preZoomRatio !== val) {
            this.camera.zoomRatio = this.preZoomRatio = val;
            this.updateWorldSize();
            this.updateZoomRatioInfo();
        }
        else if (init) {
            this.updateMinRange();
        }
    };
    // 刷新缩放比例相关信息
    CameraCtrl.prototype.updateZoomRatioInfo = function () {
        this.updateMinRange();
        this.setPosition(this.getPosition());
    };
    CameraCtrl.prototype.updateMinRange = function () {
        this.getScreenToWorldPoint(cc.Vec2.ZERO).sub(this.getPosition(), this.minRange);
        this.minRange.addSelf(this.minRangeOffset);
    };
    // 还原到设点的大小
    CameraCtrl.prototype.zoomRatioRestore = function () {
        // const zr = this.zoomRatio
        // const val = cc.misc.clampf(zr, this.ZOOM_RATIO_MAXRANGE.x, this.ZOOM_RATIO_MAXRANGE.y)
        // if (zr !== val) {
        //     cc.tween(this.camera).to(0.15, { zoomRatio: val }).call(() => this.updateZoomRatioInfo()).start()
        // }
    };
    CameraCtrl.prototype.scaleIsMinOrMax = function () {
        var zr = this.zoomRatio;
        return zr === this.ZOOM_RATIO_MAXRANGE.x || zr === this.ZOOM_RATIO_MAXRANGE.y;
    };
    CameraCtrl.prototype.updateWorldSize = function () {
        this.getWorldToScreenPoint(this.mapSize, this.tempMapSize);
        this.getWorldToScreenPoint(cc.Vec2.ZERO, this.tempOrigin);
        this.tempMapSize.subSelf(this.tempOrigin); //这里要减去 底部的距离 不然高度不能居中
        var pos = this.getPosition();
        this.getScreenToWorldPoint(this.tempMapSize).sub(pos, this.worldMapSize);
        this.getScreenToWorldPoint(this.winSize).sub(pos, this.worldWinSize);
    };
    // 获取最大范围
    CameraCtrl.prototype.getMaxRange = function () {
        return this.maxRange;
    };
    // 设置最大范围
    CameraCtrl.prototype.setMaxRange = function (size) {
        if (size) {
            this.mapSize.x = Math.max(size.x, this.winSize.x);
            this.mapSize.y = Math.max(size.y, this.winSize.y);
            this.maxRange.x = Math.max(0, this.mapSize.x - this.winSize.x);
            this.maxRange.y = Math.max(0, this.mapSize.y - this.winSize.y);
            this.updateWorldSize();
        }
        else {
            this.mapSize.set2(0, 0);
            this.maxRange.set2(-1, -1);
        }
    };
    // 设置跟随速度
    CameraCtrl.prototype.setSmoothing = function (speed) {
        this.smoothing = speed;
    };
    CameraCtrl.prototype.setPosition = function (pos) {
        this.node.setPosition(this.clampPosition(pos));
    };
    // 修正位置 根据maxRange
    CameraCtrl.prototype.clampPosition = function (pos) {
        if (!this.maxRange.equals2(-1, -1)) {
            if (this.worldMapSize.x < this.worldWinSize.x) {
                pos.x = -this.minRange.x - (this.worldWinSize.x - this.worldMapSize.x) * 0.5;
            }
            else {
                pos.x = cc.misc.clampf(pos.x, -this.minRange.x, this.maxRange.x + this.minRange.x);
            }
            if (this.worldMapSize.y < this.worldWinSize.y) {
                pos.y = -this.minRange.y - (this.worldWinSize.y - this.worldMapSize.y) * 0.5;
            }
            else {
                pos.y = cc.misc.clampf(pos.y, -this.minRange.y, this.maxRange.y + this.minRange.y);
            }
        }
        return pos;
    };
    CameraCtrl.prototype.isOutOfBoudary = function (pos) {
        pos = pos || this.getPosition();
        if (!this.maxRange.equals2(-1, -1)) {
            var outSizeWidth = this.worldMapSize.x < this.worldWinSize.x; //超过宽
            var outSizeHeight = this.worldMapSize.y < this.worldWinSize.y; //超过高
            var outPosX = (pos.x < -this.minRange.x || pos.x > this.maxRange.x + this.minRange.x); //超过最大最小x
            var outPosY = (pos.y < -this.minRange.y || pos.y > this.maxRange.y + this.minRange.y); // 超过最大最小y
            if (outSizeWidth && outSizeHeight)
                return true;
            if (outSizeWidth) {
                return outPosY;
            }
            else if (outSizeHeight) {
                return outPosX;
            }
            else {
                return (outPosX || outPosY);
            }
        }
        return false;
    };
    // 获取直线在相机区域内的相机点
    CameraCtrl.prototype.checkLineToRectPoint = function (a, b) {
        var _this = this;
        var points = [];
        var w = Math.floor(320 * (2 - this.zoomRatio)), w2 = w * 2;
        var position = this.getPosition().sub(cc.v2(w, w), this._temp_vec2_8);
        var size = this.worldWinSize.add(cc.v2(w2, w2), this._temp_vec2_9);
        var max = position.add(size, this._temp_vec2_10);
        var isInRangeA = a.x >= position.x && a.x <= max.x && a.y >= position.y && a.y <= max.y;
        var isInRangeB = b.x >= position.x && b.x <= max.x && b.y >= position.y && b.y <= max.y;
        // 如果在范围内直接返回
        if (isInRangeA && isInRangeB) {
            return [a, b];
        }
        var zs = cc.v2(position.x, max.y), ys = cc.v2(max.x, max.y), yx = cc.v2(max.x, position.y);
        // 分别检测每个线段之间是否相交
        var point = MapHelper_1.mapHelper.segmentsIntr(a, b, position, zs); //左
        point && points.push(point);
        point = MapHelper_1.mapHelper.segmentsIntr(a, b, zs, ys); //上
        point && points.push(point);
        point = MapHelper_1.mapHelper.segmentsIntr(a, b, ys, yx); //右
        point && points.push(point);
        point = MapHelper_1.mapHelper.segmentsIntr(a, b, yx, position); //下
        point && points.push(point);
        // 如果只有一个相机 那说明有一个点在范围内  起点永远在前 终点在后
        if (points.length === 2) { //这里排序将起点放前面
            points.sort(function (m1, m2) { return m1.sub(a, _this._temp_vec2_11).magSqr() - m2.sub(a, _this._temp_vec2_12).magSqr(); });
        }
        else if (points.length !== 1) {
        }
        else if (isInRangeA) {
            points.unshift(a);
        }
        else if (isInRangeB) {
            points.push(b);
        }
        return points;
    };
    // =============================拖动===============================
    CameraCtrl.prototype.setBanMove = function (val) {
        this.banMove = val;
    };
    // 记录位置
    CameraCtrl.prototype.recordPosition = function () {
        this.node.getPosition(this.recordPos);
    };
    // 拖动
    CameraCtrl.prototype.drag = function (speed) {
        if (this.targetNode || this.moveing || this.banMove) {
            return; //如果有跟随目标 就不能拖动
        }
        this.lastDragTime = Date.now();
        this.recordPos.add(speed.mulSelf(this.dragSpeed), this._temp_vec2_7);
        this.setPosition(this._temp_vec2_7);
    };
    CameraCtrl.prototype.dragStart = function () {
        this.recordPosition();
    };
    CameraCtrl.prototype.convertWorldSub = function (startLocation, location) {
        this.camera.getScreenToWorldPoint(startLocation, this._temp_vec2_2);
        this.camera.getScreenToWorldPoint(location, this._temp_vec2_7);
        return this._temp_vec2_2.subSelf(this._temp_vec2_7);
    };
    // 获取多长时间没有拖动了
    CameraCtrl.prototype.getNoDragTime = function () {
        return Date.now() - this.lastDragTime;
    };
    // =============================跟随===============================
    // 设置目标
    CameraCtrl.prototype.setTarget = function (target, cb) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.moveing) return [3 /*break*/, 1];
                        return [2 /*return*/];
                    case 1:
                        if (!target) return [3 /*break*/, 3];
                        return [4 /*yield*/, ut.waitNextFrame(1, this)
                            // this.cameraInertiaCmpt?.stopAutoScroll()
                        ];
                    case 2:
                        _a.sent();
                        // this.cameraInertiaCmpt?.stopAutoScroll()
                        this.targetPositionOgn.set(target.getPosition(this._temp_vec2_2));
                        this.targetPositionCam = this.getTargetPosition(target);
                        this.camPosition.set(this.getPosition());
                        return [3 /*break*/, 4];
                    case 3:
                        this.targetPositionOgn.set2(-1, -1);
                        this.isFollowBottom = false;
                        this.followOffset.set2(0, 0);
                        _a.label = 4;
                    case 4:
                        this.targetNode = target;
                        this.followCallback = cb;
                        if (cb && this.camPosition.equals(this.targetPositionCam)) {
                            cb();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 设置跟随底部
    CameraCtrl.prototype.setFollowBottom = function (val) {
        this.isFollowBottom = val;
        return this;
    };
    // 设置跟随偏移
    CameraCtrl.prototype.setFollowOffset = function (x, y) {
        this.followOffset.set2(x, y);
        return this;
    };
    CameraCtrl.prototype.setTargetOnce = function (target) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        _this.setTarget(target, function () {
                            _this.setTarget(null);
                            resolve();
                        });
                    })];
            });
        });
    };
    // 获取目标位置
    CameraCtrl.prototype.getTargetPosition = function (target) {
        if (!target || !target.isValid) {
            return this.getPosition();
        }
        var pos = target.convertToWorldSpaceAR(this.followOffset, this._temp_vec2_3);
        if (this.isFollowBottom) {
            pos.x -= this.winSizeHalf.x;
            pos.y += (this.worldWinSize.y - this.winSize.y);
        }
        else {
            pos.subSelf(this.winSizeHalf);
        }
        // pos.addSelf(this.followOffset)
        return this.clampPosition(pos);
    };
    // 直接移动到对应位置
    CameraCtrl.prototype.moveTo = function (time, pos, isLeftBottom) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                if (this.targetNode || this.moveing) {
                    this.moveing = false;
                    return [2 /*return*/]; // 如果有跟随目标 就不能移动
                }
                this.moveing = true;
                // this.cameraInertiaCmpt?.stopAutoScroll()
                return [2 /*return*/, new Promise(function (resolve) {
                        if (isLeftBottom) {
                            _this.worldWinSize.mul(0.5, _this._temp_vec2_3);
                            pos = pos.subSelf(_this._temp_vec2_3);
                        }
                        pos = _this.clampPosition(pos);
                        cc.tween(_this.node)
                            .to(time, { x: pos.x, y: pos.y }, { easing: cc.easing.quadOut })
                            .call(function () {
                            _this.moveing = false;
                            resolve();
                        })
                            .start();
                    })];
            });
        });
    };
    // 修正位置 根据矩形范围 算出偏移
    CameraCtrl.prototype.redressPositionByRange = function (pos, rect) {
        var screenPos = this.getWorldToScreenPoint(pos);
        var x = 0, y = 0;
        if (screenPos.x < rect.x) { //左
            x = -(rect.x - screenPos.x);
        }
        else if (this.winSize.x - screenPos.x < rect.width) { //右
            x = rect.width - (this.winSize.x - screenPos.x);
        }
        if (screenPos.y < rect.y) { //下
            y = -(rect.y - screenPos.y);
        }
        else if (this.winSize.y - screenPos.y < rect.height) { //上
            y = rect.height - (this.winSize.y - screenPos.y);
        }
        if (x !== 0 || y !== 0) {
            var origin = this.getScreenToWorldPoint(this._temp_vec2_5.set2(0, 0), this._temp_vec2_5);
            var offset = this.getScreenToWorldPoint(this._temp_vec2_6.set2(x, y), this._temp_vec2_6);
            offset.subSelf(origin);
            this.moveTo(0.2, this.getPosition().add(offset, this._temp_vec2_5));
        }
    };
    CameraCtrl.prototype.lateUpdate = function (dt) {
        if (!this.targetNode || this.moveing || this.shakeing) {
            return;
        }
        else if (!this.targetNode.isValid) {
            return this.setTarget(null);
        }
        var targetPosition = this.targetNode.getPosition(this._temp_vec2_2);
        if (!this.targetPositionOgn.equals(targetPosition)) {
            this.targetPositionOgn.set(targetPosition);
            this.targetPositionCam = this.getTargetPosition(this.targetNode);
            this.camPosition.set(this.getPosition());
        }
        if (this.camPosition.equals(this.targetPositionCam)) {
            return;
        }
        if (this.smoothing > 0) {
            if (this.camPosition.sub(this.targetPositionCam, this._temp_vec2_1).mag() < 2) {
                this.camPosition.set(this.targetPositionCam);
                this.followCallback && this.followCallback();
            }
            else {
                this.camPosition.set(this.camPosition.lerp(this.targetPositionCam, this.smoothing * dt, this._temp_vec2_5));
            }
            this._temp_vec2_1.set2(Math.round(this.camPosition.x), Math.round(this.camPosition.y));
            this.setPosition(this._temp_vec2_1);
        }
        else {
            this.camPosition.set(this.targetPositionCam);
            this.followCallback && this.followCallback();
            this.setPosition(this.camPosition);
        }
    };
    CameraCtrl.prototype.setBgColor = function (hex) {
        this.camera.backgroundColor = cc.Color.WHITE.fromHEX(hex);
    };
    // 震屏
    CameraCtrl.prototype.shake = function (time) {
        if (this.shakeing) {
            return;
        }
        this.shakeDuration = time;
        this.shakeing = true;
        this.shakeTimer = 0;
        this.shakeOriginalPos.set(this.node.getPosition()); // 每次震动都重新记录位置
    };
    CameraCtrl.prototype.update = function (dt) {
        // 震屏
        if (this.shakeing) {
            // 计时器更新
            this.shakeTimer += dt;
            // 计算衰减系数（如果需要）
            var decay = this.shakeEnableDecay ? Math.min(1, 1 - (this.shakeTimer / this.shakeDuration)) : 1;
            // 生成随机偏移
            var shakeStrength = 2 * this.SHAKE_STRENGTH * decay;
            var offset = cc.v2((Math.random() - 0.5) * shakeStrength, (Math.random() - 0.5) * shakeStrength);
            // 应用偏移到相机位置
            this.node.setPosition(this.shakeOriginalPos.add(offset, this._temp_vec2_13));
            // 判断震动结束
            if (this.shakeTimer >= this.shakeDuration) {
                this.node.setPosition(this.shakeOriginalPos);
                this.shakeing = false;
                this.shakeTimer = 0;
            }
        }
    };
    CameraCtrl = __decorate([
        ccclass
    ], CameraCtrl);
    return CameraCtrl;
}(cc.Component));
exports.default = CameraCtrl;
exports.cameraCtrl = null;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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