
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/CheckBeginDeductHp.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2e4fecwB5lKPbUAjCJYJNjt', 'CheckBeginDeductHp');
// app/script/model/behavior/CheckBeginDeductHp.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 检测回合开始扣血
var CheckBeginDeductHp = /** @class */ (function (_super) {
    __extends(CheckBeginDeductHp, _super);
    function CheckBeginDeductHp() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.needTime = 0;
        _this.isHasFire = false;
        return _this;
    }
    CheckBeginDeductHp.prototype.onInit = function (conf) {
        this.needTime = conf.parameters || 1000;
    };
    CheckBeginDeductHp.prototype.onOpen = function () {
        var point = this.target.getPoint();
        var isHasBuff = this.target.isHasBuffs(Enums_1.BuffType.POISONING_MAX_HP, Enums_1.BuffType.POISONING_CUR_HP, Enums_1.BuffType.BLEED, Enums_1.BuffType.DELAY_DEDUCT_HP, Enums_1.BuffType.IGNITION, Enums_1.BuffType.INFECTION_PLAGUE);
        this.isHasFire = this.ctrl.checkHasFighterById(point.x, point.y, Constant_1.FIRE_PAWN_ID);
        this.setBlackboardData('isBeginDeductHp', !isHasBuff && !this.isHasFire);
        this.setTreeBlackboardData('isDeductHpAction', false);
    };
    CheckBeginDeductHp.prototype.onLeave = function (state) {
        this.setBlackboardData('isBeginDeductHp', state === BTConstant_1.BTState.SUCCESS);
    };
    CheckBeginDeductHp.prototype.onTick = function (dt) {
        var _a;
        var _this = this;
        var _b, _c;
        if (this.getTreeBlackboardData('isBatter') || this.getBlackboardData('isBeginDeductHp')) {
            return BTConstant_1.BTState.SUCCESS;
        }
        var currTime = (_b = this.getBlackboardData('currTime')) !== null && _b !== void 0 ? _b : 0;
        if (currTime >= this.needTime) {
            return BTConstant_1.BTState.SUCCESS;
        }
        else if (currTime === 0) {
            var trueDamage_1 = 0;
            var removes_1 = [], uidMap_1 = {}, owners = [];
            // 检测buff
            this.target.getBuffs().forEach(function (m) {
                if (m.type === Enums_1.BuffType.POISONING_MAX_HP) { //造成目标最大生命值的伤害
                    trueDamage_1 += Math.max(1, Math.round(_this.target.getMaxHp() * m.value));
                }
                else if (m.type === Enums_1.BuffType.POISONING_CUR_HP) { //造成目标当前生命值的伤害
                    trueDamage_1 += Math.max(1, Math.round(_this.target.getCurHp() * m.value));
                }
                else if (m.type === Enums_1.BuffType.BLEED) { //流血
                    trueDamage_1 += m.value;
                }
                else if (m.type === Enums_1.BuffType.IGNITION) { //点燃
                    trueDamage_1 += m.value;
                }
                else if (m.type === Enums_1.BuffType.INFECTION_PLAGUE) { //瘟疫
                    trueDamage_1 += Math.round(_this.target.getMaxHp() * m.value * 0.01);
                }
                else if (m.type == Enums_1.BuffType.DELAY_DEDUCT_HP) { //金丝软甲 延迟扣血
                    var effect = _this.target.getEquipEffectByType(Enums_1.EquipEffectType.FIXED_DAMAGE);
                    if (effect) {
                        if (m.value <= effect.value) { //低于固定伤害 直接扣除 并删除buff
                            trueDamage_1 += m.value;
                            removes_1.push(m.type);
                        }
                        else { //否在扣除50%
                            var v_1 = Math.round(m.value * 0.5);
                            trueDamage_1 += v_1;
                            m.value -= v_1;
                        }
                    }
                }
                else {
                    return;
                }
                uidMap_1[m.provider] = true;
            });
            // 删除buff
            (_a = this.target).removeMultiBuff.apply(_a, __spread(removes_1));
            // 检测火
            var fire = null;
            if (this.isHasFire) {
                var point_1 = this.target.getPoint(), camp = this.target.getCamp();
                fire = this.ctrl.getFighters().find(function (m) { return m.getId() === Constant_1.FIRE_PAWN_ID && m.getPoint().equals(point_1); });
                if (fire) {
                    var lv = fire.getLv();
                    if (fire.getCamp() === camp) { //如果是友方就增涨1怒气
                        var v_2 = this.target.addAnger(1);
                        this.target.changeState(Enums_1.PawnState.ADD_ANGER, { val: v_2 });
                    }
                    else {
                        trueDamage_1 += lv * fire.getCurHp();
                        owners.push(fire.getOwner());
                    }
                    // 触发火势 加等级到最高之后减等级
                    fire.attackCount += 1;
                    var maxLv = fire.getMaxHp();
                    if (fire.attackCount < maxLv) {
                        lv = fire.attackCount + 1;
                    }
                    else {
                        lv = maxLv - (fire.attackCount - maxLv + 1);
                    }
                    if (lv > 0) {
                        fire.setLv(lv);
                        fire.changeState(Enums_1.PawnState.SKILL + lv, { skillName: 'fire_' + lv });
                        fire = null;
                    }
                }
            }
            if (trueDamage_1 <= 0) {
                return BTConstant_1.BTState.SUCCESS;
            }
            for (var key in uidMap_1) {
                owners.push((_c = this.ctrl.getFighter(key)) === null || _c === void 0 ? void 0 : _c.getOwner());
            }
            trueDamage_1 = this.target.hitPrepDamageHandle(0, trueDamage_1).trueDamage;
            var v = this.target.onHit(trueDamage_1, owners);
            var isDie = this.target.isDie();
            this.target.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: isDie });
            this.setTreeBlackboardData('isDeductHpAction', true);
            // 删除
            if (fire) {
                fire.changeState(Enums_1.PawnState.DIE);
                this.ctrl.removeNoncombat(fire.getUid());
            }
        }
        this.setBlackboardData('currTime', currTime + dt);
        return BTConstant_1.BTState.RUNNING;
    };
    return CheckBeginDeductHp;
}(BaseAction_1.default));
exports.default = CheckBeginDeductHp;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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