
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/WeakGuideModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc01dMJ6cdKFrN0qNvSJ1u2', 'WeakGuideModel');
// app/script/model/guide/WeakGuideModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PopupPnlHelper_1 = require("../../common/helper/PopupPnlHelper");
var GuideConfig_1 = require("./GuideConfig");
var WeakGuideObj_1 = require("./WeakGuideObj");
var WeakGuideConfig_1 = require("./WeakGuideConfig");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
/**
 * 弱引导模块
 */
var WeakGuideModel = /** @class */ (function (_super) {
    __extends(WeakGuideModel, _super);
    function WeakGuideModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.user = null;
        _this.guides = []; //还没完成的引导
        _this.currGuide = null; //当前执行的新手引导
        _this.listenEvent = null; //当前监听的事件
        _this.currWaitCount = 0;
        return _this;
    }
    WeakGuideModel.prototype.onCreate = function () {
        this.user = this.getModel('user');
    };
    WeakGuideModel.prototype.init = function () {
        var _this = this;
        if (!this.currGuide) {
            this.currWaitCount = 0;
            this.currGuide = null;
            this.cleanListenEvent();
            this.guides.length = 0;
            WeakGuideConfig_1.WEAK_GUIDE_CONFIG.datas.forEach(function (m) { return m.checkFunc && _this.guides.push(new WeakGuideObj_1.default().init(m)); });
        }
    };
    // 保存引导标记
    WeakGuideModel.prototype.saveGuides = function () {
        // storageMgr.saveJson('weak_guides', this.guides.map(m => { return { id: m.id, tag: m.progressTag } }))
    };
    WeakGuideModel.prototype.update = function (dt) {
        // this.check()
    };
    WeakGuideModel.prototype.check = function () {
        if (!GameHelper_1.gameHpr.isEnterWorld || !!this.currGuide || GameHelper_1.gameHpr.guide.isWorking() || PopupPnlHelper_1.popupPnlHelper.isWorking()) {
            return;
        }
        // 检测开启条件
        for (var i = 0, l = this.guides.length; i < l; i++) {
            var m = this.guides[i];
            if (!m.checkFunc || m.isFinish()) {
                continue;
            }
            var func = this[m.checkFunc.name];
            if (!func) {
                continue;
            }
            else if (func.call(this, m, m.checkFunc.args)) {
                this.begin(m);
                break;
            }
        }
    };
    WeakGuideModel.prototype.add = function (id) {
        var conf = WeakGuideConfig_1.WEAK_GUIDE_CONFIG.datas.find(function (m) { return m.id === id; });
        if (conf) {
            this.begin(new WeakGuideObj_1.default().init(conf));
        }
    };
    // 开始引导
    WeakGuideModel.prototype.begin = function (data) {
        var _a;
        if (((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.id) === data.id) {
            return;
        }
        this.currGuide = data;
        this.handle();
    };
    // 结束
    WeakGuideModel.prototype.end = function () {
        this.cleanListenEvent();
        GuideHelper_1.guideHelper.cleanWeak();
        if (this.currGuide) {
            this.currGuide.complete();
            this.saveGuides();
            this.currGuide = null;
        }
    };
    WeakGuideModel.prototype.stop = function () {
        var _a;
        this.cleanListenEvent();
        GuideHelper_1.guideHelper.cleanWeak();
        (_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.resetIndex();
        this.currGuide = null;
    };
    // 直接完成某个引导
    WeakGuideModel.prototype.completeGuide = function (id) {
        var _a;
        if (((_a = this.currGuide) === null || _a === void 0 ? void 0 : _a.id) === id) {
            return this.end();
        }
        var guide = this.guides.find(function (m) { return m.id === id; });
        if (guide && !guide.isFinish()) {
            guide.complete();
            this.saveGuides();
        }
    };
    // 下一个步骤
    WeakGuideModel.prototype.nextStep = function () {
        this.cleanListenEvent();
        GuideHelper_1.guideHelper.cleanWeak();
        this.currWaitCount = 0;
        if (this.currGuide) {
            this.currGuide.index += 1;
            this.handle();
        }
    };
    // 强制下一步
    WeakGuideModel.prototype.forceNextStep = function (tag) {
        if (this.currGuide) {
            this.currGuide.index = this.currGuide.steps.findIndex(function (m) { return m.tag === tag; }) - 1; //这里减1是 下一步会加1
            this.nextStep();
        }
    };
    WeakGuideModel.prototype.handle = function () {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function () {
            var index, step, conf, func, data;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        index = this.currGuide.index;
                        if (index >= this.currGuide.steps.length || index < 0) {
                            return [2 /*return*/, this.end()];
                        }
                        step = this.currGuide.steps[index];
                        // 记录重启点
                        if (step.restartPoint && step.restartPoint !== this.currGuide.progressTag) {
                            this.currGuide.progressTag = step.restartPoint;
                            this.saveGuides();
                        }
                        // 框选节点
                        if (step.nodeChoose) {
                            conf = step.nodeChoose;
                            func = this[conf.func];
                            if (func) {
                                data = func.call(this);
                                if (!data) {
                                    return [2 /*return*/, this.stop()];
                                }
                                data.scene = conf.scene;
                                data.desc = conf.desc;
                                data.finger = conf.finger;
                                this.emit(EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE, data);
                            }
                            else if (conf.name) {
                                this.emit(EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE, { scene: conf.scene, path: conf.name, desc: conf.desc, finger: conf.finger });
                            }
                        }
                        if (!(step.type === GuideConfig_1.GuideStepType.DELAY)) return [3 /*break*/, 2];
                        return [4 /*yield*/, ut.wait((_a = step.time) !== null && _a !== void 0 ? _a : 0)];
                    case 1:
                        _f.sent();
                        this.nextStep();
                        return [3 /*break*/, 10];
                    case 2:
                        if (!(step.type === GuideConfig_1.GuideStepType.ON_EVENT)) return [3 /*break*/, 3];
                        this.setListenEvent(step.event);
                        return [3 /*break*/, 10];
                    case 3:
                        if (!(step.type === GuideConfig_1.GuideStepType.CHECK)) return [3 /*break*/, 7];
                        if (!this.callFunc(step.func)) return [3 /*break*/, 5];
                        return [4 /*yield*/, ut.wait((_b = step.time) !== null && _b !== void 0 ? _b : 1)];
                    case 4:
                        _f.sent();
                        if (((_c = this.currGuide) === null || _c === void 0 ? void 0 : _c.index) === index) {
                            this.handle();
                        }
                        return [3 /*break*/, 6];
                    case 5:
                        if (this.currGuide) {
                            this.nextStep();
                        }
                        _f.label = 6;
                    case 6: return [3 /*break*/, 10];
                    case 7:
                        if (!(step.type === GuideConfig_1.GuideStepType.CHECK_WAIT)) return [3 /*break*/, 10];
                        if (!this.callFunc(step.func)) return [3 /*break*/, 9];
                        if (this.currWaitCount === 0) {
                            this.currWaitCount = 1;
                            // viewHelper.hidePnl('common/WeakGuide')
                        }
                        return [4 /*yield*/, ut.wait((_d = step.time) !== null && _d !== void 0 ? _d : 1)];
                    case 8:
                        _f.sent();
                        if (((_e = this.currGuide) === null || _e === void 0 ? void 0 : _e.index) === index) {
                            this.handle();
                        }
                        return [3 /*break*/, 10];
                    case 9:
                        if (this.currGuide) {
                            // viewHelper.showPnl('common/WeakGuide')
                            this.nextStep();
                        }
                        _f.label = 10;
                    case 10: return [2 /*return*/];
                }
            });
        });
    };
    WeakGuideModel.prototype.setListenEvent = function (event) {
        this.cleanListenEvent();
        this.listenEvent = event;
        eventCenter.on(event.name, this.onListenEvent, this);
    };
    WeakGuideModel.prototype.cleanListenEvent = function () {
        if (this.listenEvent) {
            eventCenter.off(this.listenEvent.name, this.onListenEvent, this);
            this.listenEvent = null;
        }
    };
    WeakGuideModel.prototype.onListenEvent = function (data) {
        if (!this.listenEvent) {
            return;
        }
        var ok = false;
        if (this.listenEvent.func) {
            var func = this[this.listenEvent.func];
            ok = func === null || func === void 0 ? void 0 : func.call(this, data, this.listenEvent.args);
        }
        else {
            ok = this.checkArgObj(data, this.listenEvent.args);
        }
        if (ok) {
            this.cleanListenEvent();
            this.nextStep();
        }
    };
    WeakGuideModel.prototype.checkArgObj = function (data, args) {
        if (!args) {
            return true;
        }
        else if (!data) {
            return false;
        }
        for (var key in args) {
            if (data[key] !== args[key]) {
                return false;
            }
        }
        return true;
    };
    // 调用方法
    WeakGuideModel.prototype.callFunc = function (data) {
        var func = this[data.name];
        return func === null || func === void 0 ? void 0 : func.call(this, data.args);
    };
    //---------------------------------------------------检测启动方法----------------------------------------------------
    //---------------------------------------------------事件检测方法----------------------------------------------------
    // 检测是否招募页签 true表示等待
    WeakGuideModel.prototype.checkFuncIsRecruitTab = function () {
        var _a;
        var ui = mc.getOpenPnls().find(function (m) { return m.key === 'build/BuildBarracks'; });
        if (!ui || !ui.node.active) {
            return true;
        }
        else if ((_a = ui.FindChild('tabs_tc_tce/1', cc.Toggle)) === null || _a === void 0 ? void 0 : _a.isChecked) {
            this.stop();
        }
        return false;
    };
    //---------------------------------------------------框选节点方法----------------------------------------------------
    // 框选兵营
    WeakGuideModel.prototype.nodeChooseBarracks = function () {
    };
    WeakGuideModel = __decorate([
        mc.addmodel('weak_guide')
    ], WeakGuideModel);
    return WeakGuideModel;
}(mc.BaseModel));
exports.default = WeakGuideModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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