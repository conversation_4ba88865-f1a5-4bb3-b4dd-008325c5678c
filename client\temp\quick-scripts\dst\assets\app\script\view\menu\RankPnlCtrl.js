
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/RankPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '30766zalaJG15U850B+8fo4', 'RankPnlCtrl');
// app/script/view/menu/RankPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var RankPnlCtrl = /** @class */ (function (_super) {
    __extends(RankPnlCtrl, _super);
    function RankPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pagesNode_ = null; // path://root/pages_n
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.model = null;
        return _this;
    }
    RankPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_SCORE_RANK] = this.onUpdateScoreRank, _a.enter = true, _a),
        ];
    };
    RankPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('rank');
                return [2 /*return*/];
            });
        });
    };
    RankPnlCtrl.prototype.onEnter = function (data) {
        this.tabsTc_.Tabs(0);
    };
    RankPnlCtrl.prototype.onRemove = function () {
    };
    RankPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    RankPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        if (type === '0') {
            this.showPlayerScoreList(node, GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ADD_EXTRA_TORANK) ? 1 : 0);
        }
        else if (type === '1') {
            this.showAlliRankList(node);
        }
    };
    // path://root/click_score_be
    RankPnlCtrl.prototype.onClickClickScore = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/ScoreRankDesc', this.model.getMeScoreInfo());
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    RankPnlCtrl.prototype.onUpdateScoreRank = function (type) {
        this.showPlayerScoreList(this.pagesNode_.Child(1), type);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 游戏积分列表
    RankPnlCtrl.prototype.showPlayerScoreList = function (node, type) {
        var _this = this;
        if (GameHelper_1.gameHpr.world.isGameOver()) {
            type = 1; //游戏结算强行显示+奖励积分
        }
        var sv = node.Child('list', cc.ScrollView), me = node.Child('me');
        sv.content.Swih('');
        this.loadingNode_.active = true;
        me.active = false;
        var isSpectate = GameHelper_1.gameHpr.isSpectate();
        this.model.getPlayerScoreList(type).then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            sv.stopAutoScroll();
            // 显示自己
            if (me.active = !isSpectate) {
                var meInfo = _this.model.getMeScoreInfo();
                _this.updatePlayerScoreItem(me, meInfo, meInfo.no, type);
                sv.node.height = 728;
            }
            else {
                sv.node.height = 804;
            }
            // 刷新列表
            sv.node.children.forEach(function (m) { var _a; return (_a = m.Component(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
            sv.content.y = 0;
            sv.List((list === null || list === void 0 ? void 0 : list.length) || 0, function (it, i) { return _this.updatePlayerScoreItem(it, list[i], i, type); });
        });
    };
    RankPnlCtrl.prototype.updatePlayerScoreItem = function (it, data, no, type) {
        ResHelper_1.resHelper.loadPlayerHead(it.Child('head'), data.headIcon, this.key);
        var score = data.landScore + data.alliScore;
        if (type) {
            score += data.extraScore;
        }
        it.Child('no', cc.Label).string = no < 0 ? '-' : (no + 1) + '';
        it.Child('name', cc.Label).string = ut.nameFormator(data.nickname, 7);
        var alliIcon = it.Child('icon');
        ResHelper_1.resHelper.loadAlliIcon(data.alliIcon || 0, alliIcon, this.key);
        alliIcon.Color(data.alliIcon ? '#FFFFFF' : '#B19379');
        it.Child('alli', cc.Label).string = data.alliName ? ut.nameFormator(data.alliName, 7) : '-';
        it.Child('score/val', cc.Label).string = score + '';
    };
    // 联盟列表
    RankPnlCtrl.prototype.showAlliRankList = function (node) {
        var _this = this;
        var sv = node.Child('list', cc.ScrollView);
        sv.content.Swih('');
        this.loadingNode_.active = true;
        this.model.getAlliRankList().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.List(list.length, function (it, i) {
                var data = list[i];
                it.Child('no', cc.Label).string = (i + 1) + '';
                ResHelper_1.resHelper.loadAlliIcon(data.icon, it.Child('icon'), _this.key);
                it.Child('name', cc.Label).string = ut.nameFormator(data.name, 7);
                it.Child('pers/val', cc.Label).string = data.pers[0] + '/' + data.pers[1]; //.join('/')
                it.Child('money/val', cc.Label).string = ut.simplifyMoneyEn(data.resAcc);
                it.Child('score/val', cc.Label).string = (data.score || 0) + '';
                var alli = GameHelper_1.gameHpr.world.getAlliBaseInfo(data.uid);
                var alliPolicys = (alli === null || alli === void 0 ? void 0 : alli.policys) || {}, sumEmbassyLv = (alli === null || alli === void 0 ? void 0 : alli.sumEmbassyLv) || 0, indexs = Object.keys(alliPolicys);
                it.Child('policy').Items(indexs, function (it, index) {
                    var _a;
                    var lv = GameHelper_1.gameHpr.getAlliPolicySlotConfByIndex(Number(index));
                    var icon = it.Child('val', cc.Sprite);
                    ResHelper_1.resHelper.loadPolicyIcon((_a = alliPolicys[index]) === null || _a === void 0 ? void 0 : _a.id, icon, _this.key);
                    icon.setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(sumEmbassyLv >= lv));
                });
                it.Child('name').y = indexs.length ? 16 : 0;
            });
        });
    };
    RankPnlCtrl = __decorate([
        ccclass
    ], RankPnlCtrl);
    return RankPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = RankPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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