
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendComponent.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1113bA/Q5dG8alsW5XyU5Vl', 'ExtendComponent');
// app/core/extend/ExtendComponent.ts

/**
 * Component扩展方法
 */
cc.Component.prototype.getActive = function () {
    return this.node.active;
};
cc.Component.prototype.setActive = function (val) {
    this.node.active = val;
    return val;
};
cc.Component.prototype.getPosition = function (out) {
    return this.node.getPosition(out);
};
cc.Component.prototype.FindChild = function (name, className) {
    return this.node.FindChild(name, className);
};
cc.Component.prototype.Child = function (name, className) {
    return this.node.Child(name, className);
};
cc.Component.prototype.Component = function (className) {
    return this.node.Component(className);
};
cc.Component.prototype.Color = function (val) {
    this.node.Color(val);
    return this;
};
cc.Component.prototype.getColor = function () {
    return this.node.color;
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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