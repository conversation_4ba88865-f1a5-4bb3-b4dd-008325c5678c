
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/AppUpdateTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3c84aEIGmdCs6wp6BfUgGO2', 'AppUpdateTipPnlCtrl');
// app/script/view/login/AppUpdateTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var HotUpdateHelper_1 = require("../../common/helper/HotUpdateHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ccclass = cc._decorator.ccclass;
var AppUpdateTipPnlCtrl = /** @class */ (function (_super) {
    __extends(AppUpdateTipPnlCtrl, _super);
    function AppUpdateTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tipNode_ = null; // path://root/tip_n
        _this.updateNode_ = null; // path://root/update_n
        _this.logSv_ = null; // path://root/log_sv
        //@end
        _this.newVersion = '';
        _this.progressBar = null;
        _this.progressDesc = null;
        _this.progressInfo = null;
        _this.progressPercent = null;
        _this.autoRetryCount = 0;
        return _this;
    }
    AppUpdateTipPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.HOT_UPDATE_EVENT] = this.onHotUpdateEvent, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.HOT_UPDATE_LOG] = this.onHotUpdateLog, _b.enter = true, _b),
        ];
    };
    AppUpdateTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false });
                this.progressBar = this.updateNode_.FindChild('progress/bar', cc.Sprite);
                this.progressDesc = this.updateNode_.FindChild('progress/desc', cc.Label);
                this.progressInfo = this.updateNode_.FindChild('progress/info', cc.Label);
                this.progressPercent = this.updateNode_.FindChild('progress/percent', cc.Label);
                HotUpdateHelper_1.hotUpdateHelper.setDebug(true);
                return [2 /*return*/];
            });
        });
    };
    AppUpdateTipPnlCtrl.prototype.onEnter = function (data) {
        this.newVersion = data.clientVersion;
        this.tipNode_.active = true;
        this.updateNode_.active = false;
        this.logSv_.setActive(false);
        var isUpdateNative = !ut.checkVersion(GameHelper_1.gameHpr.getVersion(), data.clientBigVersion); //是否需要更新大版本
        this.tipNode_.Child('desc').setLocaleKey(isUpdateNative ? 'login.version_toolow_app_tip_2' : 'login.version_toolow_app_tip_1');
        this.tipNode_.Child('buttons/update_be').active = !isUpdateNative && !data.force;
        this.tipNode_.Child('buttons/download_be').active = isUpdateNative;
        // this.tipNode_.Child('buttons/cancel_be').active = !data.force
        this.tipNode_.Child('buttons/cancel_be').active = false;
        this.tipNode_.Child('download_desc').active = isUpdateNative;
        // 强制更新
        if (data.force && !isUpdateNative) {
            this.checkUpdate();
        }
    };
    AppUpdateTipPnlCtrl.prototype.onRemove = function () {
    };
    AppUpdateTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tip_n/buttons/cancel_be
    AppUpdateTipPnlCtrl.prototype.onClickCancel = function (event, data) {
        // this.hide()
        // // 如果是有服务器选择 点击取消 就重新选择服务器
        // if (gameHpr.user.getSid()) {
        //     gameHpr.resetSelectServer(true).then(res => this.emit(EventType.RESELECT_SERVER))
        // }
    };
    // path://root/tip_n/buttons/update_be
    AppUpdateTipPnlCtrl.prototype.onClickUpdate = function (event, data) {
        this.checkUpdate();
    };
    // path://root/tip_n/buttons/download_be
    AppUpdateTipPnlCtrl.prototype.onClickDownload = function (event, data) {
        var url = GameHelper_1.gameHpr.getGameDownloadUrl();
        if (url) {
            cc.sys.openURL(url);
        }
    };
    // path://root/update_n/buttons/retry_be
    AppUpdateTipPnlCtrl.prototype.onClickRetry = function (event, data) {
        this.showProgress(true);
        this.progressDesc.setLocaleKey('');
        this.updateNode_.Child('buttons').Swih('updateing');
        HotUpdateHelper_1.hotUpdateHelper.redownload();
        // 上报
        TaHelper_1.taHelper.track('ta_hotUpdate', { cost_time: 0, error_code: -200 });
    };
    // path://root/log_be
    AppUpdateTipPnlCtrl.prototype.onClickLog = function (event, data) {
        this.logSv_.setActive(!this.logSv_.getActive());
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AppUpdateTipPnlCtrl.prototype.onHotUpdateEvent = function (event, failed) {
        var code = event.getEventCode();
        if (failed) { //下载失败 重试
            this.autoRetryCount++;
            if (this.autoRetryCount <= 3) { //自动重试3次
                HotUpdateHelper_1.hotUpdateHelper.redownload();
            }
            else {
                this.autoRetryCount = 0;
                this.showProgress(false);
                var text = GameHelper_1.gameHpr.getTextByNetworkStatus('login.hot_update_failed');
                this.progressDesc.Color('#C34A32').setLocaleKey(text);
                this.updateNode_.Child('buttons').Swih('retry_be');
            }
        }
        else if (code === jsb.EventAssetsManager.NEW_VERSION_FOUND) { //发现新版本
            this.showProgress(true);
            this.progressDesc.setLocaleKey('');
        }
        else if (code === jsb.EventAssetsManager.ALREADY_UP_TO_DATE) { //已经是最新的版本
            this.hide();
            this.emit(EventType_1.default.RELOGIN);
        }
        else if (code === jsb.EventAssetsManager.UPDATE_PROGRESSION) { //更新进度
            var percent = event.getPercent();
            if (percent) {
                this.progressBar.fillRange = percent;
                this.progressPercent.string = Math.floor(percent * 100) + '%';
                var b = HotUpdateHelper_1.hotUpdateHelper.convertBytesToString(event.getDownloadedBytes());
                var t = HotUpdateHelper_1.hotUpdateHelper.convertBytesToString(event.getTotalBytes());
                this.progressInfo.string = b + '/' + t;
            }
        }
        else if (code === jsb.EventAssetsManager.UPDATE_FINISHED) { //更新完成
            ut.wait(0.2, this).then(function () { return GameHelper_1.gameHpr.gameRestart(); });
        }
    };
    AppUpdateTipPnlCtrl.prototype.onHotUpdateLog = function (msg) {
        this.logSv_.AddItem(function (it, i) { return it.Component(cc.Label).string = msg; });
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AppUpdateTipPnlCtrl.prototype.checkUpdate = function () {
        this.tipNode_.active = false;
        this.updateNode_.active = true;
        this.progressDesc.Color('#756963').setLocaleKey('login.check_updateing');
        this.progressBar.fillRange = 0;
        this.progressInfo.string = '0/0';
        this.progressPercent.string = '0%';
        this.showProgress(false);
        this.updateNode_.Child('buttons').Swih('updateing');
        // 开始更新
        var conf = GameHelper_1.gameHpr.getConfigByArea();
        var manifestUrl = conf.manifestUrl + GameHelper_1.gameHpr.getRunPlatform() + '/' + this.newVersion + '/';
        HotUpdateHelper_1.hotUpdateHelper.start(conf.packageUrl, manifestUrl, this.newVersion);
    };
    AppUpdateTipPnlCtrl.prototype.showProgress = function (val) {
        this.progressInfo.setActive(val);
        this.progressPercent.setActive(val);
    };
    AppUpdateTipPnlCtrl.prototype.update = function (dt) {
        HotUpdateHelper_1.hotUpdateHelper.update(dt);
    };
    AppUpdateTipPnlCtrl = __decorate([
        ccclass
    ], AppUpdateTipPnlCtrl);
    return AppUpdateTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AppUpdateTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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