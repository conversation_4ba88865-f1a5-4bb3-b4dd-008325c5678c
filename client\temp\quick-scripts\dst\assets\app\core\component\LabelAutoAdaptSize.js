
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/component/LabelAutoAdaptSize.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '95385OGT9FAkaMqH75dXiyO', 'LabelAutoAdaptSize');
// app/core/component/LabelAutoAdaptSize.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property, requireComponent = _a.requireComponent, menu = _a.menu;
/**
 * 自动适应大小
 */
var LabelAutoAdaptSize = /** @class */ (function (_super) {
    __extends(LabelAutoAdaptSize, _super);
    function LabelAutoAdaptSize() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.size = cc.v2();
        _this.minScaleWidth = 0.8; //最小容忍宽度缩小比例
        _this.checkStringChange = true; //是否检测内容变化
        _this.initScale = 1;
        _this.label = null;
        _this.initFontSize = 20;
        _this.initLineHeight = 20;
        _this.initOverflow = cc.Label.Overflow.NONE;
        _this.fontSizeRatio = 1;
        _this.preSize = cc.v2();
        _this.preString = null;
        _this.preFontSize = 20;
        _this.lineCount = 1; //当前行数
        _this.isEnable = false;
        return _this;
    }
    LabelAutoAdaptSize.prototype.onLoad = function () {
        this.label = this.getComponent(cc.Label);
        this.init();
    };
    LabelAutoAdaptSize.prototype.init = function () {
        this.initScale = this.node.scale;
        this.preFontSize = this.initFontSize = this.label.fontSize;
        this.initLineHeight = this.label.lineHeight;
        this.initOverflow = this.label.overflow;
        this.fontSizeRatio = this.initFontSize / this.label.lineHeight;
        if (this.size.y < this.initLineHeight) {
            this.size.y = this.initLineHeight;
        }
        this.lineCount = 1;
    };
    LabelAutoAdaptSize.prototype.onEnable = function () {
        if (!this.isEnable) {
            this.isEnable = true;
            if (this.label.overflow === cc.Label.Overflow.RESIZE_HEIGHT) {
                if (this.size.x === 0) {
                    this.size.x = this.node.width;
                }
                else {
                    this.node.width = this.size.x;
                }
            }
            this.label._forceUpdateRenderData();
        }
    };
    LabelAutoAdaptSize.prototype.update = function (dt) {
        var w = this.node.width, h = this.node.height;
        if (this.preString === null || (this.checkStringChange && this.preString !== this.label.string)) {
            this.preString = this.label.string;
            this.label._forceUpdateRenderData();
            w = this.node.width, h = this.node.height;
        }
        else if (this.preFontSize !== this.label.fontSize) {
            // cc.log(this.preString)
        }
        else if (w === 0 || this.preSize.equals2(w, h)) {
            return;
        }
        this.preSize.set2(w, h);
        if (this.label.overflow === cc.Label.Overflow.NONE) { //如果是none模式
            if (this.preFontSize !== this.label.fontSize) {
                this.label.lineHeight = this.initLineHeight;
                this.setFontSize(this.initFontSize);
                return this.preSize.set2(-1, -1);
            }
            else if (w <= this.size.x && h <= this.size.y) {
                this.node.scale = this.initScale;
                return;
            }
            // 是否满足缩小
            var scale = h > this.size.y ? this.size.y / h : this.size.x / w;
            if (scale >= this.minScaleWidth || this.size.y <= this.label.lineHeight) {
                this.node.scale = scale * this.initScale;
                return;
            }
            // 太小就要换行了
            this.label.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
            this.node.width = this.size.x;
            this.node.scale = this.initScale;
            this.updateLineHeight(2, 1); //先尝试从2行开始
        }
        else if (this.label.overflow === cc.Label.Overflow.RESIZE_HEIGHT) { //换行模式
            var add = h > this.size.y ? 1 : -1;
            this.updateLineHeight(this.lineCount + add, add);
        }
    };
    // RESIZE_HEIGHT模式下开始向下适应高
    LabelAutoAdaptSize.prototype.updateLineHeight = function (count, add) {
        this.lineCount = count;
        if (this.lineCount <= 1) {
            this.lineCount = 1;
            this.label.lineHeight = this.initLineHeight;
            this.setFontSize(this.initFontSize);
            if (this.initOverflow === cc.Label.Overflow.NONE) {
                this.label.overflow = cc.Label.Overflow.NONE; //只有一行的时候 还原成以前的模式
            }
            else {
                this.label._forceUpdateRenderData();
                if (this.node.height > this.size.y) {
                    this.updateLineHeight(2, 1);
                }
                else {
                    this.preSize.set2(this.node.width, this.node.height);
                    this.adaptSpineSize(this.preSize);
                }
            }
        }
        else {
            var lh = this.label.lineHeight = Math.min(this.initLineHeight, Math.floor(this.size.y / count));
            this.setFontSize(Math.floor(lh * this.fontSizeRatio));
            this.label._forceUpdateRenderData();
            var h = this.node.height;
            if ((add > 0 && h > this.size.y) || (add < 0 && h < this.size.y)) {
                this.updateLineHeight(count + add, add);
            }
            else if (add < 0 && h > this.size.y) {
                this.updateLineHeight(count + 1, 1);
            }
            else {
                this.preSize.set2(this.node.width, h);
                this.adaptSpineSize(this.preSize);
            }
        }
    };
    // RESIZE_HEIGHT模式下适应大小
    LabelAutoAdaptSize.prototype.adaptSpineSize = function (selfSize) {
        // if (selfSize.y < this.size.y) {
        //     this.label.lineHeight += 1
        //     this.label.fontSize = Math.floor(this.label.lineHeight * this.fontSizeRatio)
        //     this.label._forceUpdateRenderData()
        // }
    };
    LabelAutoAdaptSize.prototype.setFontSize = function (val) {
        this.preFontSize = this.label.fontSize = val;
    };
    LabelAutoAdaptSize.prototype.setSize = function (val) {
        if (this.size.equals(val)) {
            return;
        }
        this.size.set(val);
        this.preSize.set2(0, 0);
        if (this.label) {
            this.init();
        }
        if (this.isEnable) {
            this.isEnable = false;
            this.onEnable();
        }
    };
    LabelAutoAdaptSize.prototype.setMinScaleWidth = function (val) {
        this.minScaleWidth = val;
    };
    __decorate([
        property(cc.Vec2)
    ], LabelAutoAdaptSize.prototype, "size", void 0);
    __decorate([
        property
    ], LabelAutoAdaptSize.prototype, "minScaleWidth", void 0);
    __decorate([
        property
    ], LabelAutoAdaptSize.prototype, "checkStringChange", void 0);
    LabelAutoAdaptSize = __decorate([
        ccclass,
        menu('自定义组件/LabelAutoAdaptSize'),
        requireComponent(cc.Label)
    ], LabelAutoAdaptSize);
    return LabelAutoAdaptSize;
}(cc.Component));
exports.default = LabelAutoAdaptSize;
cc.LabelAutoAdaptSize = LabelAutoAdaptSize;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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