
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/activity/MysteryboxShow105PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '97e6988DGNHAaLOuvm7BcGl', 'MysteryboxShow105PnlCtrl');
// app/script/view/activity/MysteryboxShow105PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MysteryboxShow105PnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxShow105PnlCtrl, _super);
    function MysteryboxShow105PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.posNode_ = null; // path://root/pos_n
        _this.rootNode_ = null; // path://root/root_nbe_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.descLbl_ = null; // path://root/desc_l
        //@end
        _this.MYSTERY_BOX_ID = 104;
        _this.boxId = 0;
        _this.ignot = 0;
        _this.ANIMATIONS = ['mysterybox_anim_105_normal', 'mysterybox_anim_105_special'];
        _this.EFFECT_ANIMS = ['mysterybox_anim_103_effect1', 'mysterybox_anim_103_effect2'];
        _this.lockTouch = false;
        return _this;
    }
    MysteryboxShow105PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxShow105PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MysteryboxShow105PnlCtrl.prototype.onEnter = function (id, ignot) {
        this.boxId = id;
        this.ignot = ignot;
        this.init();
    };
    MysteryboxShow105PnlCtrl.prototype.onRemove = function () {
    };
    MysteryboxShow105PnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    MysteryboxShow105PnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    // path://root/root_nbe_n
    MysteryboxShow105PnlCtrl.prototype.onClickRoot = function (event, data) {
        if (this.lockTouch)
            return;
        audioMgr.playSFX('click');
        this.do(event.target.name);
    };
    // path://root/button_n/once_again_be
    MysteryboxShow105PnlCtrl.prototype.onClickOnceAgain = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.user.getIngot() < this.ignot) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
        }
        else if (!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
            ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', this.MYSTERY_BOX_ID, function () { return _this.isValid && _this.init(); });
        }
        else {
            this.init();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MysteryboxShow105PnlCtrl.prototype.init = function () {
        var _this = this;
        this.lockTouch = false;
        this.rootNode_.children.forEach(function (m) {
            var _a;
            m.active = true;
            m.opacity = 255;
            var effect = m.Child('item/effect', cc.Animation);
            effect.reset();
            m.Child('item/box').active = true;
            m.Child('count').active = false;
            m.Component(cc.Button).interactable = false;
            var anim = m.Child('item', cc.Animation), name = (_a = m.Data) !== null && _a !== void 0 ? _a : _this.ANIMATIONS[0];
            anim.reset(name);
            m.Child('item/card/val', cc.Sprite).spriteFrame = null;
            m.setPosition(0, 0);
            var target = _this.posNode_.Child(m.name).getPosition();
            m.stopAllActions();
            cc.tween(m)
                .to(0.3, { x: target.x, y: target.y }, { easing: cc.easing.sineOut })
                .call(function () { return m.Component(cc.Button).interactable = true; })
                .start();
        });
        this.buttonNode_.active = false;
        this.descLbl_.setLocaleKey('');
        ut.wait(0.4, this).then(function () {
            if (_this.isValid) {
                _this.descLbl_.setLocaleKey('ui.please_select_mb_box_102');
            }
        });
    };
    // 购买盲盒
    MysteryboxShow105PnlCtrl.prototype.do = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, json, _loop_1, this_1, i, l, isHideCard, it, anim;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.lockTouch = true;
                        mc.lockTouch('item_skin');
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuySkinBlindBox', { id: this.boxId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            this.lockTouch = false;
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.descLbl_.setLocaleKey('');
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.setSkinItemList(data.skinItemList);
                        json = assetsMgr.getJsonData('pawnSkin', data.skinId);
                        if (!json) {
                            this.lockTouch = false;
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.UNKNOWN)];
                        }
                        _loop_1 = function (i, l) {
                            var it_1 = this_1.rootNode_.children[i];
                            it_1.Component(cc.Button).interactable = false;
                            if (it_1.name !== index) {
                                cc.tween(it_1)
                                    .to(0.3, { opacity: 0 })
                                    .call(function () { return it_1.active = false; })
                                    .start();
                            }
                        };
                        this_1 = this;
                        for (i = 0, l = this.rootNode_.children.length; i < l; i++) {
                            _loop_1(i, l);
                        }
                        isHideCard = json.value === 2;
                        it = this.rootNode_.Child(index);
                        anim = it.Data = isHideCard ? this.ANIMATIONS[1] : this.ANIMATIONS[0];
                        cc.tween(it)
                            .to(0.3, { x: 0, y: 0 }, { easing: cc.easing.sineIn })
                            .call(function () {
                            var item = it.Child('item');
                            var spr = item.Child('card/val', cc.Sprite);
                            ResHelper_1.resHelper.loadPawnHeadIcon(data.skinId, spr, _this.key);
                            var time = Date.now();
                            item.Component(cc.Animation).playAsync(anim).then(function () {
                                var _a;
                                // console.log('cost time: ', Date.now() - time)
                                var count = ((_a = GameHelper_1.gameHpr.user.getSkinItemList().filter(function (m) { return m.id === data.skinId; })) === null || _a === void 0 ? void 0 : _a.length) || 1;
                                it.Child('item/box').active = false;
                                it.Child('count').active = true;
                                it.Child('count/val', cc.Label).string = Math.max(0, count - 1) + '';
                                _this.closeNode_.active = true;
                                _this.buttonNode_.active = true;
                                _this.descLbl_.setLocaleKey('ui.click_close_desc');
                                _this.lockTouch = false;
                                mc.unlockTouch('item_skin');
                                eventCenter.emit(EventType_1.default.UPDATE_MYSTERYBOX);
                            });
                            ut.wait(0.45).then(function () {
                                var effectAni = item.Child('effect', cc.Animation);
                                effectAni.play(isHideCard ? _this.EFFECT_ANIMS[1] : _this.EFFECT_ANIMS[0]);
                            });
                        })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxShow105PnlCtrl = __decorate([
        ccclass
    ], MysteryboxShow105PnlCtrl);
    return MysteryboxShow105PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxShow105PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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