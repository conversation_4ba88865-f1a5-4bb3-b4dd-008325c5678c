
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/LogoutTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0df7atc8FF5YU8gFNiU1Tm', 'LogoutTipPnlCtrl');
// app/script/view/menu/LogoutTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var LogoutTipPnlCtrl = /** @class */ (function (_super) {
    __extends(LogoutTipPnlCtrl, _super);
    function LogoutTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.step = 0;
        _this.realNameEb = null;
        _this.mailboxEb = null;
        _this.realName = '';
        _this.mailbox = '';
        return _this;
    }
    LogoutTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    LogoutTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.realNameEb = this.contentNode_.FindChild('2/1/input', cc.EditBox);
                this.mailboxEb = this.contentNode_.FindChild('2/2/input', cc.EditBox);
                return [2 /*return*/];
            });
        });
    };
    LogoutTipPnlCtrl.prototype.onEnter = function (data) {
        this.updateContent(0);
    };
    LogoutTipPnlCtrl.prototype.onRemove = function () {
    };
    LogoutTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/1/jump_web_be
    LogoutTipPnlCtrl.prototype.onClickJumpWeb = function (event, data) {
        this.buttonsNode_.Child('logout_be', cc.Button).interactable = true;
        cc.sys.openURL(GameHelper_1.gameHpr.getLogoutAgreementUrl());
    };
    // path://root/buttons_n/logout_be
    LogoutTipPnlCtrl.prototype.onClickLogout = function (event, data) {
        var step = this.step + 1;
        if (step === 2 && !GameHelper_1.gameHpr.isRelease) {
            step = 4; //过审时不需要输入真实名字
        }
        this.updateContent(step);
    };
    // path://root/buttons_n/cancel_be
    LogoutTipPnlCtrl.prototype.onClickCancel = function (event, data) {
        this.hide();
    };
    // path://root/buttons_n/ok_be
    LogoutTipPnlCtrl.prototype.onClickOk = function (event, data) {
        this.hide();
        GameHelper_1.gameHpr.changeAccount();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    LogoutTipPnlCtrl.prototype.updateContent = function (step) {
        var _this = this;
        this.buttonsNode_.Child('logout_be').active = step !== 6 && step !== 4;
        this.buttonsNode_.Child('logout_cd').active = step === 4;
        this.buttonsNode_.Child('cancel_be').active = step !== 6;
        this.buttonsNode_.Child('ok_be').active = step === 6;
        if (step === 0) {
            this.step = step;
            this.contentNode_.Swih(step);
            this.buttonsNode_.Child('logout_be', cc.Button).interactable = true;
        }
        else if (step === 1) {
            this.step = step;
            this.contentNode_.Swih(step);
            this.buttonsNode_.Child('logout_be', cc.Button).interactable = false;
        }
        else if (step === 2) {
            this.step = step;
            this.contentNode_.Swih(step);
            this.realNameEb.string = '';
            this.mailboxEb.string = '';
            this.realName = '';
            this.mailbox = '';
        }
        else if (step === 3) {
            this.check(this.realNameEb.string.trim(), this.mailboxEb.string.trim(), '');
        }
        else if (step === 4) {
            this.step = step;
            this.contentNode_.Swih(step);
            this.buttonsNode_.Child('logout_cd/lay/time', cc.LabelTimer).run(10, function () {
                _this.buttonsNode_.Child('logout_be').active = true;
                _this.buttonsNode_.Child('logout_cd').active = false;
            });
        }
        else if (step === 5) {
            this.do(this.realName, this.mailbox, '');
        }
        else if (step === 6) {
            this.step = step;
            this.contentNode_.Swih(step);
        }
    };
    LogoutTipPnlCtrl.prototype.check = function (name, email, identity) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!name) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NAME_IS_NULL)];
                        }
                        else if (!email) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.please_input_mailbox')];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_CheckRealName', { name: name, email: email, identity: identity }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.realName = name;
                        this.mailbox = email;
                        this.updateContent(4);
                        return [2 /*return*/];
                }
            });
        });
    };
    LogoutTipPnlCtrl.prototype.do = function (name, email, identity) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_DelAccount', { name: name, email: email, identity: identity }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err === ECode_1.ecode.LOGOUT_WAIT_TIME_UNMET) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.surplus_logout_time', { params: [GameHelper_1.gameHpr.millisecondToStringForDay(data.surplusLogoutTime)] })];
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.updateContent(6);
                        return [2 /*return*/];
                }
            });
        });
    };
    LogoutTipPnlCtrl = __decorate([
        ccclass
    ], LogoutTipPnlCtrl);
    return LogoutTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LogoutTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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