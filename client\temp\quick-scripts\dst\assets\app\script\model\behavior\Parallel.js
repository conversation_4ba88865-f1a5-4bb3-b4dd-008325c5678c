
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/Parallel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '38e0fQSwVhOW7mt1Tn4Tn0P', 'Parallel');
// app/script/model/behavior/Parallel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseComposite_1 = require("./BaseComposite");
var BTConstant_1 = require("./BTConstant");
// 并行 所有子节点成功才返回成功
var Parallel = /** @class */ (function (_super) {
    __extends(Parallel, _super);
    function Parallel() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Parallel.prototype.onOpen = function () {
    };
    Parallel.prototype.onTick = function (dt) {
        var state = BTConstant_1.BTState.SUCCESS;
        for (var i = 0, l = this.getChildrenCount(); i < l; i++) {
            var sta = this.children[i].execute(dt);
            if (sta === BTConstant_1.BTState.FAILURE) {
                state = sta;
            }
        }
        return state;
    };
    return Parallel;
}(BaseComposite_1.default));
exports.default = Parallel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcUGFyYWxsZWwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsaURBQTRDO0FBQzVDLDJDQUF1QztBQUV2QyxrQkFBa0I7QUFDbEI7SUFBc0MsNEJBQWE7SUFBbkQ7O0lBZUEsQ0FBQztJQWJVLHlCQUFNLEdBQWI7SUFDQSxDQUFDO0lBRU0seUJBQU0sR0FBYixVQUFjLEVBQVU7UUFDcEIsSUFBSSxLQUFLLEdBQUcsb0JBQU8sQ0FBQyxPQUFPLENBQUE7UUFDM0IsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxFQUFFLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDckQsSUFBSSxHQUFHLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLENBQUE7WUFDdEMsSUFBSSxHQUFHLEtBQUssb0JBQU8sQ0FBQyxPQUFPLEVBQUU7Z0JBQ3pCLEtBQUssR0FBRyxHQUFHLENBQUE7YUFDZDtTQUNKO1FBQ0QsT0FBTyxLQUFLLENBQUE7SUFDaEIsQ0FBQztJQUNMLGVBQUM7QUFBRCxDQWZBLEFBZUMsQ0FmcUMsdUJBQWEsR0FlbEQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZUNvbXBvc2l0ZSBmcm9tIFwiLi9CYXNlQ29tcG9zaXRlXCI7XHJcbmltcG9ydCB7IEJUU3RhdGUgfSBmcm9tIFwiLi9CVENvbnN0YW50XCI7XHJcblxyXG4vLyDlubbooYwg5omA5pyJ5a2Q6IqC54K55oiQ5Yqf5omN6L+U5Zue5oiQ5YqfXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFBhcmFsbGVsIGV4dGVuZHMgQmFzZUNvbXBvc2l0ZSB7XHJcblxyXG4gICAgcHVibGljIG9uT3BlbigpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25UaWNrKGR0OiBudW1iZXIpIHtcclxuICAgICAgICBsZXQgc3RhdGUgPSBCVFN0YXRlLlNVQ0NFU1NcclxuICAgICAgICBmb3IgKGxldCBpID0gMCwgbCA9IHRoaXMuZ2V0Q2hpbGRyZW5Db3VudCgpOyBpIDwgbDsgaSsrKSB7XHJcbiAgICAgICAgICAgIGxldCBzdGEgPSB0aGlzLmNoaWxkcmVuW2ldLmV4ZWN1dGUoZHQpXHJcbiAgICAgICAgICAgIGlmIChzdGEgPT09IEJUU3RhdGUuRkFJTFVSRSkge1xyXG4gICAgICAgICAgICAgICAgc3RhdGUgPSBzdGFcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gc3RhdGVcclxuICAgIH1cclxufSJdfQ==