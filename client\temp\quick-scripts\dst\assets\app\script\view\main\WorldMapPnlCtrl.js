
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/WorldMapPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '33156HejVtJ96ZcQVLawVU9', 'WorldMapPnlCtrl');
// app/script/view/main/WorldMapPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var WorldMapTouchCmpt_1 = require("./WorldMapTouchCmpt");
var ccclass = cc._decorator.ccclass;
var WorldMapPnlCtrl = /** @class */ (function (_super) {
    __extends(WorldMapPnlCtrl, _super);
    function WorldMapPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map/map_n
        _this.rootNode_ = null; // path://root/map/map_n/mask/root_n
        _this.markNode_ = null; // path://root/buttons/mark_be_n
        _this.alliFlagNode_ = null; // path://root/buttons/alli_flag_be_n
        _this.restoreNode_ = null; // path://root/buttons/restore_be_n
        _this.inputXEb_ = null; // path://root/buttons/input_x_eb_ebee
        _this.inputYEb_ = null; // path://root/buttons/input_y_eb_ebee
        _this.showChangeNode_ = null; // path://root/buttons/show_change_n
        _this.closeShowChangeNode_ = null; // path://root/buttons/show_change_n/close_show_change_n
        _this.ontopFlagNode_ = null; // path://root/ontop_flag_n
        //@end
        _this.ONE_GRID_WIDTH = 1; //一个的像素大小
        _this.diNode = null;
        _this.pointer = null;
        _this.ancientNode = null;
        _this.fortNode = null;
        _this.alliNode = null;
        _this.armyNode = null;
        _this.flagNode = null;
        _this.markNode = null;
        _this.battleNode = null;
        _this.touchCmpt = null;
        _this.rootSize = cc.v2();
        _this.zoom = 1; //当前的缩放比例
        _this.isNeedReDraw = false;
        _this.pointerPoint = cc.v2(); //指针的point位置
        _this._temp_vec2 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._mapRender = null;
        _this._dataViewCache = null;
        return _this;
    }
    WorldMapPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_CELL_INFO] = this.onUpdateCellInfo, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_BATTLE_DIST_INFO] = this.onUpdateBattleDistInfo, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_MAP_MARK] = this.onUpdateMapMark, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_ONTOP_FLAG] = this.onUpdateOntopFlag, _d.enter = true, _d),
        ];
    };
    WorldMapPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // this.setParam({ isAct: false })
                this.diNode = this.rootNode_.FindChild('di');
                this.pointer = this.rootNode_.FindChild('pointer');
                this.ancientNode = this.rootNode_.FindChild('ancients');
                this.fortNode = this.rootNode_.FindChild('forts');
                this.alliNode = this.rootNode_.FindChild('allis');
                this.armyNode = this.rootNode_.FindChild('armys');
                this.flagNode = this.rootNode_.FindChild('flags');
                this.markNode = this.rootNode_.FindChild('marks');
                this.battleNode = this.rootNode_.FindChild('battle');
                this.mapNode_.width = this.rootNode_.width = MapHelper_1.mapHelper.MAP_SIZE.x;
                this.mapNode_.height = this.rootNode_.height = MapHelper_1.mapHelper.MAP_SIZE.y;
                this.mapNode_.getComponent(cc.Widget).updateAlignment();
                this.mapNode_.children.forEach(function (m) { var _a; return (_a = m.getComponent(cc.Widget)) === null || _a === void 0 ? void 0 : _a.updateAlignment(); });
                this.mapNode_.scaleX = this.mapNode_.parent.width / this.mapNode_.width;
                this.mapNode_.scaleY = this.mapNode_.parent.height / this.mapNode_.height;
                this.zoom = 1;
                this.isNeedReDraw = true;
                this.rootSize.set2(this.mapNode_.width, this.mapNode_.height);
                this.touchCmpt = this.FindChild('root/map').addComponent(WorldMapTouchCmpt_1.default).init(this.rootNode_);
                this._mapRender = this.rootNode_.Child("render", cc.Sprite);
                this.showChangeNode_.active = false;
                this.closeShowChangeNode_.on(cc.Node.EventType.TOUCH_START, this.onClickCloseShowChange, this);
                this.closeShowChangeNode_.SetSwallowTouches(false);
                return [2 /*return*/];
            });
        });
    };
    WorldMapPnlCtrl.prototype.onEnter = function (data) {
        var _a, _b, _c, _d, _e, _f, _g;
        this.markNode_.active = !GameHelper_1.gameHpr.isNoviceMode && !GameHelper_1.gameHpr.isSpectate();
        this.alliFlagNode_.active = this.markNode_.active && GameHelper_1.gameHpr.player.isHasAlliance();
        var point = MapHelper_1.mapHelper.getPointByPixel(CameraCtrl_1.cameraCtrl.getCentrePosition(), this._temp_vec2);
        this.setPointerByPoint(point);
        this.drawMap();
        this.updateMapZoom(this.zoom);
        this.showPointerToCenter();
        this.showAncients();
        this.showForts();
        this.showAllis();
        this.showArmys();
        this.showFlags();
        this.showMarks();
        this.showBattle();
        this.updateOntopFlag();
        var user = GameHelper_1.gameHpr.user;
        var root = this.showChangeNode_.Child('root');
        this.armyNode.active = root.Child('show_army_te', cc.Toggle).isChecked = (_a = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_IN_MAP)) !== null && _a !== void 0 ? _a : true;
        this.ancientNode.active = root.Child('show_ancient_te', cc.Toggle).isChecked = (_b = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ANCIENT_IN_MAP)) !== null && _b !== void 0 ? _b : true;
        this.fortNode.active = root.Child('show_fort_te', cc.Toggle).isChecked = (_c = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_FORT_IN_MAP)) !== null && _c !== void 0 ? _c : true;
        this.battleNode.active = root.Child('show_battle_te', cc.Toggle).isChecked = (_d = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_BATTLE_IN_MAP)) !== null && _d !== void 0 ? _d : true;
        this.alliNode.active = root.Child('show_alli_te', cc.Toggle).isChecked = (_e = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ALLI_IN_MAP)) !== null && _e !== void 0 ? _e : true;
        this.flagNode.active = root.Child('show_flag_te', cc.Toggle).isChecked = (_f = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_FLAG_IN_MAP)) !== null && _f !== void 0 ? _f : true;
        this.markNode.active = root.Child('show_mark_te', cc.Toggle).isChecked = (_g = user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_MARK_IN_MAP)) !== null && _g !== void 0 ? _g : true;
        this.touchCmpt.enter(this.zoom, this.onTouchMap.bind(this));
    };
    WorldMapPnlCtrl.prototype.onRemove = function () {
        this.touchCmpt.clean();
        this.showChangeNode_.active = false;
    };
    WorldMapPnlCtrl.prototype.onClean = function () {
        var _a;
        this.closeShowChangeNode_.off(cc.Node.EventType.TOUCH_START, this.onClickCloseShowChange, this);
        this._dataViewCache = null;
        (_a = this._mapRender.spriteFrame) === null || _a === void 0 ? void 0 : _a.destroy();
        this._mapRender.spriteFrame = null;
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons/go_be
    WorldMapPnlCtrl.prototype.onClickGo = function (event, data) {
        if (mc.currWindName === GameHelper_1.gameHpr.world.getSceneKey()) {
            this.hide();
            this.emit(EventType_1.default.MAP_MOVE_TO, this.pointerPoint.clone());
        }
    };
    // path://root/buttons/input_x_eb_ebee
    WorldMapPnlCtrl.prototype.onClickInputXEnded = function (event, data) {
        this.setPointerByInputPoint();
    };
    // path://root/buttons/input_y_eb_ebee
    WorldMapPnlCtrl.prototype.onClickInputYEnded = function (event, data) {
        this.setPointerByInputPoint();
    };
    // path://root/buttons/restore_be_n
    WorldMapPnlCtrl.prototype.onClickRestore = function (event, data) {
        audioMgr.playSFX('click');
        this.showPointerToCenter();
    };
    // path://root/top/other_player_be
    WorldMapPnlCtrl.prototype.onClickOtherPlayer = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('main/WorldMapDesc');
    };
    // path://root/buttons/mark_be_n
    WorldMapPnlCtrl.prototype.onClickMark = function (event, data) {
        var cell = GameHelper_1.gameHpr.world.getMapCellByPoint(this.pointerPoint);
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/MapMark', cell.actPoint, true);
        }
    };
    // path://root/buttons/alli_flag_be_n
    WorldMapPnlCtrl.prototype.onClickAlliFlag = function (event, data) {
        var cell = GameHelper_1.gameHpr.world.getMapCellByPoint(this.pointerPoint);
        if (cell) {
            ViewHelper_1.viewHelper.showPnl('main/AlliFlag', cell.actPoint, true);
        }
    };
    // path://root/buttons/open_show_change_be
    WorldMapPnlCtrl.prototype.onClickOpenShowChange = function (event, data) {
        audioMgr.playSFX('click');
        this.showChangeNode_.active = true;
    };
    // path://root/buttons/show_change_n/root/show_army_te
    WorldMapPnlCtrl.prototype.onClickShowArmy = function (event, data) {
        audioMgr.playSFX('click');
        this.armyNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_ancient_te
    WorldMapPnlCtrl.prototype.onClickShowAncient = function (event, data) {
        audioMgr.playSFX('click');
        this.ancientNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ANCIENT_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_fort_te
    WorldMapPnlCtrl.prototype.onClickShowFort = function (event, data) {
        audioMgr.playSFX('click');
        this.fortNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_FORT_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_battle_te
    WorldMapPnlCtrl.prototype.onClickShowBattle = function (event, data) {
        audioMgr.playSFX('click');
        this.battleNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_BATTLE_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_alli_te
    WorldMapPnlCtrl.prototype.onClickShowAlli = function (event, data) {
        audioMgr.playSFX('click');
        this.alliNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ALLI_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_flag_te
    WorldMapPnlCtrl.prototype.onClickShowFlag = function (event, data) {
        audioMgr.playSFX('click');
        this.flagNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_FLAG_IN_MAP, event.isChecked);
    };
    // path://root/buttons/show_change_n/root/show_mark_te
    WorldMapPnlCtrl.prototype.onClickShowMark = function (event, data) {
        audioMgr.playSFX('click');
        this.markNode.active = event.isChecked;
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_MARK_IN_MAP, event.isChecked);
    };
    // path://root/ontop_flag_n/ontop_flag_be
    WorldMapPnlCtrl.prototype.onClickOntopFlag = function (event, _) {
        this.hide();
        GameHelper_1.gameHpr.gotoTargetPos(event.target.Data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    WorldMapPnlCtrl.prototype.onUpdateCellInfo = function () {
        this.isNeedReDraw = true;
    };
    WorldMapPnlCtrl.prototype.onUpdateBattleDistInfo = function () {
        this.showBattle();
    };
    WorldMapPnlCtrl.prototype.onUpdateMapMark = function () {
        this.showMarks();
    };
    // 刷新置顶标记
    WorldMapPnlCtrl.prototype.onUpdateOntopFlag = function () {
        this.updateOntopFlag();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    WorldMapPnlCtrl.prototype.onClickCloseShowChange = function (event, data) {
        this.showChangeNode_.active = false;
    };
    WorldMapPnlCtrl.prototype.drawMap = function () {
        return __awaiter(this, void 0, void 0, function () {
            var render, size, w, h, spf, deepth, count, arrData, tex, players, uid, cnt, selfColors, allianceColors, otherColors, _a, _b, _i, key, info, isSelf, isAlliance, _c, _d, _e, index, cell, point, idx, colors, e_1_1;
            var e_1, _f;
            return __generator(this, function (_g) {
                switch (_g.label) {
                    case 0:
                        if (!this.isNeedReDraw) {
                            return [2 /*return*/];
                        }
                        this.isNeedReDraw = false;
                        render = this._mapRender;
                        size = MapHelper_1.mapHelper.MAP_SIZE;
                        w = size.x;
                        h = size.y;
                        spf = render.spriteFrame;
                        if (!spf) {
                            spf = new cc.SpriteFrame();
                            render.spriteFrame = spf;
                        }
                        deepth = 4;
                        count = w * h * deepth;
                        arrData = this._dataViewCache;
                        if (!arrData) {
                            arrData = new Uint8Array(count);
                        }
                        else {
                            arrData.fill(0);
                        }
                        tex = spf.getTexture();
                        if (!tex) {
                            tex = new cc.Texture2D();
                            tex.setFlipY(true);
                            spf.setTexture(tex);
                        }
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 1:
                        _g.sent();
                        players = GameHelper_1.gameHpr.world.getAllPlayerMap();
                        uid = GameHelper_1.gameHpr.getUid();
                        cnt = 0;
                        selfColors = [54, 132, 77];
                        allianceColors = [75, 138, 168];
                        otherColors = [182, 59, 67];
                        _a = [];
                        for (_b in players)
                            _a.push(_b);
                        _i = 0;
                        _g.label = 2;
                    case 2:
                        if (!(_i < _a.length)) return [3 /*break*/, 11];
                        key = _a[_i];
                        info = players[key];
                        isSelf = info.uid === uid;
                        isAlliance = GameHelper_1.gameHpr.isOneAlliance(info.uid);
                        if (!(isSelf || isAlliance || info.cells.size >= 50)) return [3 /*break*/, 10];
                        _g.label = 3;
                    case 3:
                        _g.trys.push([3, 8, 9, 10]);
                        _c = (e_1 = void 0, __values(info.cells)), _d = _c.next();
                        _g.label = 4;
                    case 4:
                        if (!!_d.done) return [3 /*break*/, 7];
                        _e = __read(_d.value, 2), index = _e[0], cell = _e[1];
                        point = cell.point;
                        idx = (point.y * w + point.x) * deepth;
                        colors = isSelf ? selfColors : isAlliance ? allianceColors : otherColors;
                        arrData[idx] = colors[0];
                        arrData[idx + 1] = colors[1];
                        arrData[idx + 2] = colors[2];
                        arrData[idx + 3] = 255;
                        cnt++;
                        if (!(cnt > 50000)) return [3 /*break*/, 6];
                        cnt = 0;
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 5:
                        _g.sent();
                        _g.label = 6;
                    case 6:
                        _d = _c.next();
                        return [3 /*break*/, 4];
                    case 7: return [3 /*break*/, 10];
                    case 8:
                        e_1_1 = _g.sent();
                        e_1 = { error: e_1_1 };
                        return [3 /*break*/, 10];
                    case 9:
                        try {
                            if (_d && !_d.done && (_f = _c.return)) _f.call(_c);
                        }
                        finally { if (e_1) throw e_1.error; }
                        return [7 /*endfinally*/];
                    case 10:
                        _i++;
                        return [3 /*break*/, 2];
                    case 11:
                        tex.setFilters(cc.Texture2D.Filter.NEAREST, cc.Texture2D.Filter.NEAREST);
                        tex.initWithData(arrData, cc.Texture2D.PixelFormat.RGBA8888, w, h);
                        render.node.width = size.x;
                        render.node.height = size.y;
                        this._dataViewCache = arrData;
                        return [2 /*return*/];
                }
            });
        });
    };
    // 设置指针位置
    WorldMapPnlCtrl.prototype.setPointer = function (pos) {
        this.pointer.setPosition(pos);
        var point = pos.div(this.ONE_GRID_WIDTH * this.zoom, this.pointerPoint).floor();
        this.inputXEb_.string = point.x + '';
        this.inputYEb_.string = point.y + '';
    };
    WorldMapPnlCtrl.prototype.setPointerByPoint = function (point) {
        this.setPointer(point.mul(this.ONE_GRID_WIDTH * this.zoom, this._temp_vec2));
    };
    WorldMapPnlCtrl.prototype.setPointerByInputPoint = function () {
        var size = MapHelper_1.mapHelper.MAP_SIZE;
        var x = Number(this.inputXEb_.string.trim()) || 0;
        x = cc.misc.clampf(x, 0, size.x - 1);
        var y = Number(this.inputYEb_.string.trim()) || 0;
        y = cc.misc.clampf(y, 0, size.y - 1);
        this.inputXEb_.string = x + '';
        this.inputYEb_.string = y + '';
        this.setPointerByPoint(this._temp_vec2_1.set2(x, y));
        this.checkPointerInScreen();
        if (this.restoreNode_.active) {
            this.showPointerToCenter();
        }
    };
    // 触摸地图
    WorldMapPnlCtrl.prototype.onTouchMap = function (data) {
        if (data.type === 'drag') {
            this.setRootPosition(data.position);
        }
        else if (data.type === 'click') {
            this.setPointer(data.position);
            this.checkPointerInScreen();
        }
        else if (data.type === 'zoom') {
            this.updateMapZoom(data.zoom);
            this.setRootPosition(data.position);
        }
    };
    // 刷新缩放
    WorldMapPnlCtrl.prototype.updateMapZoom = function (val) {
        var zoom = this.zoom;
        var pointerPosition = this.pointer.getPosition(this._temp_vec2_2);
        this.zoom = val;
        this.diNode.scale = val;
        this._mapRender.node.scale = val;
        this.rootNode_.width = this.rootSize.x * val;
        this.rootNode_.height = this.rootSize.y * val;
        this.pointer.setPosition(pointerPosition.divSelf(zoom).mulSelf(val));
        this.updateShowByZoom(this.ancientNode, zoom, val);
        this.updateShowByZoom(this.fortNode, zoom, val);
        this.updateShowByZoom(this.alliNode, zoom, val);
        this.updateShowByZoom(this.armyNode, zoom, val);
        this.updateShowByZoom(this.flagNode, zoom, val);
        this.updateShowByZoom(this.markNode, zoom, val);
        this.updateShowByZoom(this.battleNode, zoom, val);
    };
    WorldMapPnlCtrl.prototype.setRootPosition = function (pos) {
        pos.x = cc.misc.clampf(pos.x, this.mapNode_.width - this.rootNode_.width, 0);
        pos.y = cc.misc.clampf(pos.y, this.mapNode_.height - this.rootNode_.height, 0);
        this.rootNode_.setPosition(pos);
        this.checkPointerInScreen();
    };
    // 检测指针是否在屏幕中
    WorldMapPnlCtrl.prototype.checkPointerInScreen = function () {
        var p = ut.convertToNodeAR(this.pointer, this.mapNode_, this._temp_vec2_2);
        var wh = this.pointer.width * 0.5, hh = this.pointer.height * 0.5;
        this.restoreNode_.active = p.x <= -wh || p.x >= this.mapNode_.width + wh || p.y <= -hh || p.y >= this.mapNode_.height + hh;
    };
    // 显示指针到居中
    WorldMapPnlCtrl.prototype.showPointerToCenter = function () {
        var pos = this.pointer.getPosition(this._temp_vec2_2);
        pos.x = this.mapNode_.width * 0.5 - pos.x;
        pos.y = this.mapNode_.height * 0.5 - pos.y;
        this.setRootPosition(pos);
    };
    // 显示古城
    WorldMapPnlCtrl.prototype.showAncients = function () {
        var _this = this;
        var ancientMap = GameHelper_1.gameHpr.world.getAncientMap(), keys = Object.keys(ancientMap);
        var tempVec = cc.v2();
        this.ancientNode.Items(keys, function (it, key) {
            var data = ancientMap[key].cell;
            it.Data = data.actIndex;
            var point = data.actPoint;
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
        });
    };
    // 显示要塞
    WorldMapPnlCtrl.prototype.showForts = function () {
        var _this = this;
        var forts = GameHelper_1.gameHpr.getPlayerForts();
        var tempVec = cc.v2();
        this.fortNode.Items(forts, function (it, data) {
            it.Data = data.actIndex;
            var point = data.actPoint;
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
        });
    };
    // 显示联盟
    WorldMapPnlCtrl.prototype.showAllis = function () {
        var _this = this;
        var list = GameHelper_1.gameHpr.world.getAlliCreaterIndexs();
        var tempVec = cc.v2();
        this.alliNode.Items(list, function (it, data) {
            var index = it.Data = data.index;
            var point = MapHelper_1.mapHelper.indexToPoint(index);
            ResHelper_1.resHelper.loadAlliIcon(data.icon, it.Child('bg'), _this.key);
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
        });
    };
    // 显示军队
    WorldMapPnlCtrl.prototype.showArmys = function () {
        var _this = this;
        var world = GameHelper_1.gameHpr.world, tempVec = cc.v2();
        this.armyNode.Items(Object.keys(GameHelper_1.gameHpr.player.getArmyDistMap()), function (it, data) {
            var cell = world.getMapCellByIndex(Number(data));
            it.Data = cell.actIndex;
            var point = cell.actPoint;
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
        });
    };
    // 显示标记
    WorldMapPnlCtrl.prototype.showFlags = function () {
        var _this = this;
        var tempVec = cc.v2();
        var mapFlag = GameHelper_1.gameHpr.alliance.getMapFlag();
        var flags = Object.keys(mapFlag);
        this.flagNode.Items(flags, function (it, data) {
            var index = it.Data = Number(data);
            var point = MapHelper_1.mapHelper.indexToPoint(index);
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
            it.Child('bg/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(mapFlag[index].flag);
        });
    };
    // 显示个人标记
    WorldMapPnlCtrl.prototype.showMarks = function () {
        var _this = this;
        var tempVec = cc.v2();
        var marks = GameHelper_1.gameHpr.player.getMapMarks();
        var flags = Object.keys(marks);
        this.markNode.Items(flags, function (it, data) {
            var index = it.Data = Number(data);
            var point = MapHelper_1.mapHelper.indexToPoint(index);
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
            it.Child('bg/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(marks[index].flag);
        });
    };
    // 显示战斗
    WorldMapPnlCtrl.prototype.showBattle = function () {
        var _this = this;
        var tempVec = cc.v2();
        var list = GameHelper_1.gameHpr.world.getCanShowToMiniMapBattles();
        this.battleNode.Items(list, function (it, cell) {
            it.Data = cell.actIndex;
            var point = cell.actPoint;
            it.setPosition(point.mul(_this.ONE_GRID_WIDTH * _this.zoom, tempVec));
            it.zIndex = 1000 - point.y;
        });
    };
    WorldMapPnlCtrl.prototype.updateShowByZoom = function (node, zoom, val) {
        var tempVec = cc.v2();
        node.children.forEach(function (m) {
            var pointerPosition = m.getPosition(tempVec);
            m.setPosition(pointerPosition.divSelf(zoom).mulSelf(val));
        });
    };
    // 刷新置顶标记
    WorldMapPnlCtrl.prototype.updateOntopFlag = function () {
        var flags = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS) || {};
        var keys = Object.keys(flags);
        if (this.ontopFlagNode_.active = !!keys.length) {
            var arr = keys.map(function (m) { return ut.stringToNumbers(m, '_'); }).sort(function (a, b) { return (a[0] * 100 + a[1]) - (b[0] * 100 + b[1]); });
            this.ontopFlagNode_.Items(arr, function (it, _a) {
                var _b = __read(_a, 2), type = _b[0], flag = _b[1];
                it.Data = flags[type + '_' + flag];
                it.Child('bg', cc.MultiFrame).setFrame(Number(type));
                it.Child('bg/val', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getMapFlagNumber(Number(flag));
            });
        }
    };
    __decorate([
        ut.syncLock
    ], WorldMapPnlCtrl.prototype, "drawMap", null);
    WorldMapPnlCtrl = __decorate([
        ccclass
    ], WorldMapPnlCtrl);
    return WorldMapPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = WorldMapPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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