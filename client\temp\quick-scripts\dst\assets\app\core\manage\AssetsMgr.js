
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/AssetsMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8a068nxOatHYIc7NIDMmik1', 'AssetsMgr');
// app/core/manage/AssetsMgr.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CoreEventType_1 = require("../event/CoreEventType");
var ResLoader_1 = require("../utils/ResLoader");
/**
 * 资源管理
 *
 * 目录结构
 *
 * common
 *      image
 *      json
 *      prefab
 *      audio
 *      font
 *          cn
 *          en
 * tmp
 *      image
 *      prefab
 *      audio
 */
var AssetsMgr = /** @class */ (function () {
    function AssetsMgr() {
        this.prefabs = new Map(); // 公共预制体
        this.images = new Map(); // 永久存在图片
        this.audios = new Map(); // 公共声音
        this.jsons = new Map(); // 公共配置文件
        this.fonts = new Map(); // 公共字体
        this.materials = new Map(); // 公共材质
        this.temps = new Map(); // 临时缓存资源
        this.loadTempQueueMap = new Map(); //加载队列 防止一个资源同时被多个加载
        this.isLoadFont = false; //是否加载font中
        this.lastLoadFontLang = '';
        this.__onProgessCallback = null;
        this.__totalProgess = 0;
        this.__tempProgess = null;
        this.__curPercent = 0;
    }
    AssetsMgr.prototype.init = function (onProgess) {
        return __awaiter(this, void 0, void 0, function () {
            var arr, debug;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.__onProgessCallback = onProgess;
                        this.temps.clear();
                        this.__curPercent = 0;
                        this.__tempProgess = {};
                        arr = [this.loadJsons(), this.loadImages(), this.loadPrefab(), this.loadAudio(), this.loadMaterial(), this.loadFont()];
                        this.__totalProgess = 1 / arr.length;
                        debug = this.debug;
                        this.debug = false;
                        return [4 /*yield*/, Promise.all(arr)];
                    case 1:
                        _a.sent();
                        this.debug = debug;
                        this.__onProgessCallback = null;
                        this.__tempProgess = null;
                        return [2 /*return*/];
                }
            });
        });
    };
    AssetsMgr.prototype.clean = function () {
        this.prefabs.clear();
        this.images.clear();
        this.audios.clear();
        this.jsons.clear();
        this.fonts.clear();
        this.materials.clear();
        this.loadTempQueueMap.clear();
        this.temps.clear();
    };
    Object.defineProperty(AssetsMgr.prototype, "debug", {
        get: function () { return ResLoader_1.loader.debug; },
        set: function (val) { ResLoader_1.loader.debug = val; },
        enumerable: false,
        configurable: true
    });
    // 初始化的加载进度
    AssetsMgr.prototype.onInitLoadProgess = function (key, done, total) {
        if (!this.__onProgessCallback || !total) {
            return;
        }
        var last = this.__tempProgess[key];
        if (last === undefined) {
            last = this.__tempProgess[key] = 0;
        }
        var curr = done / total;
        if (last >= curr) {
            return;
        }
        this.__tempProgess[key] = curr;
        var diff = Math.max(curr - last, 0) * this.__totalProgess;
        this.__curPercent += diff;
        this.__onProgessCallback(this.__curPercent);
    };
    // 初始json文件配置
    AssetsMgr.prototype.loadJsons = function () {
        return __awaiter(this, void 0, void 0, function () {
            var assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/json', cc.JsonAsset, function (done, total) { return _this.onInitLoadProgess('json', done, total); })];
                    case 1:
                        assets = _a.sent();
                        assets.forEach(function (m) { return _this.addJsonAsset(m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    AssetsMgr.prototype.addJsonAsset = function (m) {
        var dataIdMap = {};
        if (m.json.length > 0 && m.json[0]['id'] !== undefined) {
            m.json.forEach(function (m) { return dataIdMap[m.id] = m; });
        }
        this.jsons.set(m.name, {
            datas: m.json,
            dataIdMap: dataIdMap,
            getById: function (id) {
                return this.dataIdMap[id];
            },
            get: function (key, value) {
                return this.datas.filter(function (m) { return m[key] === value; });
            }
        });
    };
    // 初始通用图片集
    AssetsMgr.prototype.loadImages = function () {
        return __awaiter(this, void 0, void 0, function () {
            var assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/image', cc.SpriteFrame, function (done, total) { return _this.onInitLoadProgess('image', done, total); })];
                    case 1:
                        assets = _a.sent();
                        assets.forEach(function (m) { return _this.images.set(m.name, m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化预制体
    AssetsMgr.prototype.loadPrefab = function () {
        return __awaiter(this, void 0, void 0, function () {
            var assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/prefab', cc.Prefab, function (done, total) { return _this.onInitLoadProgess('prefab', done, total); })];
                    case 1:
                        assets = _a.sent();
                        assets.forEach(function (m) { return _this.prefabs.set(m.name, m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化声音
    AssetsMgr.prototype.loadAudio = function () {
        return __awaiter(this, void 0, void 0, function () {
            var assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/audio', cc.AudioClip, function (done, total) { return _this.onInitLoadProgess('audio', done, total); })];
                    case 1:
                        assets = _a.sent();
                        assets.forEach(function (m) { return _this.audios.set(m.name, m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 材质
    AssetsMgr.prototype.loadMaterial = function () {
        return __awaiter(this, void 0, void 0, function () {
            var assets;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/material', cc.Material, function (done, total) { return _this.onInitLoadProgess('material', done, total); })];
                    case 1:
                        assets = _a.sent();
                        assets.forEach(function (m) { return _this.materials.set(m.name, m); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 字体
    AssetsMgr.prototype.loadFont = function (lang) {
        return __awaiter(this, void 0, void 0, function () {
            var preLang, assets, map, obj;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        lang = lang || mc.lang;
                        if (this.lastLoadFontLang === lang) {
                            return [2 /*return*/];
                        }
                        preLang = this.lastLoadFontLang;
                        this.lastLoadFontLang = lang;
                        return [4 /*yield*/, ResLoader_1.loader.loadResDir('common/font/' + lang, cc.Font, function (done, total) { return _this.onInitLoadProgess('font', done, total); })];
                    case 1:
                        assets = _a.sent();
                        map = this.getFontMap(lang);
                        assets.forEach(function (m) { return map.set(m.name, m); });
                        // 清理上一个字体
                        if (preLang && preLang !== lang) {
                            obj = this.fonts.get(preLang);
                            if (obj) {
                                obj.forEach(function (m) { return m.decRef(); });
                                obj.clear();
                                this.fonts.delete(preLang);
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AssetsMgr.prototype.getFontMap = function (lang) {
        lang = lang || mc.lang;
        var map = this.fonts.get(lang);
        if (!map) {
            map = new Map();
            this.fonts.set(lang, map);
        }
        return map;
    };
    // 切换语言资源
    AssetsMgr.prototype.changeLangJson = function (lang) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.isLoadFont) return [3 /*break*/, 1];
                        return [2 /*return*/];
                    case 1:
                        if (!!this.fonts.has(lang)) return [3 /*break*/, 3];
                        this.isLoadFont = true;
                        mc.lockTouch('change_lang_json');
                        eventCenter.emit(CoreEventType_1.default.LOADING_WAIT_BEGIN);
                        return [4 /*yield*/, this.loadFont(lang)];
                    case 2:
                        _a.sent();
                        eventCenter.emit(CoreEventType_1.default.LOADING_WAIT_END);
                        mc.unlockTouch('change_lang_json');
                        _a.label = 3;
                    case 3:
                        this.isLoadFont = false;
                        eventCenter.emit(CoreEventType_1.default.LANGUAGE_CHANGED, lang);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取图片
    AssetsMgr.prototype.getImage = function (key) {
        return this.images.get(key);
    };
    // 获取json配置
    AssetsMgr.prototype.getJson = function (key) {
        return this.jsons.get(key);
    };
    AssetsMgr.prototype.getJsonData = function (key, id) {
        var _a;
        return (_a = this.getJson(key)) === null || _a === void 0 ? void 0 : _a.getById(id);
    };
    // 获取预制体
    AssetsMgr.prototype.getPrefab = function (key) {
        return this.prefabs.get(key);
    };
    // 获取声音
    AssetsMgr.prototype.getAudio = function (key) {
        return this.audios.get(key);
    };
    // 获取材质
    AssetsMgr.prototype.getMaterial = function (key) {
        return this.materials.get(key);
    };
    // 获取字体
    AssetsMgr.prototype.getFont = function (key) {
        var _a;
        return (_a = this.getFontMap()) === null || _a === void 0 ? void 0 : _a.get(key);
    };
    // 根据key获取文本
    AssetsMgr.prototype.lang = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        if (!key) {
            return '';
        }
        var lang = mc.lang;
        var _params = [];
        params === null || params === void 0 ? void 0 : params.forEach(function (m) { return Array.isArray(m) ? _params.pushArr(m) : _params.push(m); });
        var _a = __read(key.split('.'), 2), name = _a[0], id = _a[1];
        var json = this.getJsonData(name, id) || {};
        var val = json[lang];
        if (val !== undefined) {
            return ut.stringFormat(val, this.updateLangParams(_params, lang));
        }
        return ut.stringFormat(key, this.updateLangParams(_params, lang));
    };
    // 刷新参数
    AssetsMgr.prototype.updateLangParams = function (params, lang) {
        var _this = this;
        if (!params) {
            return [];
        }
        lang = lang || mc.lang;
        return params.map(function (m) {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                var _a = __read(m.split('.'), 2), name = _a[0], id = _a[1];
                var json = _this.getJsonData(name, id) || {};
                var val = json[lang];
                return val !== undefined ? val : m;
            }
            return m;
        });
    };
    // 根据类型获取文件夹名字
    AssetsMgr.prototype.makeTypeName = function (type) {
        var name = cc.js.getClassName(type);
        if (name === 'cc.Prefab') {
            return 'prefab';
        }
        else if (name === 'cc.SpriteFrame') {
            return 'image';
        }
        else if (name === 'cc.AudioClip') {
            return 'audio';
        }
        else if (name === 'sp.SkeletonData') {
            return 'spine';
        }
        else if (name === 'cc.JsonAsset') {
            return 'json';
        }
        else if (name === 'cc.Font') {
            return 'font';
        }
        else if (name === 'cc.Material') {
            return 'material';
        }
        else if (name === 'cc.AnimationClip') {
            return 'animation';
        }
        else if (name === 'cc.Asset') {
            return 'asset';
        }
        return '';
    };
    // 加载核心资源
    AssetsMgr.prototype.loadCommonRes = function (name, type) {
        return __awaiter(this, void 0, Promise, function () {
            var at, assetMap, isFont, it;
            var _this = this;
            return __generator(this, function (_a) {
                at = this.makeTypeName(type);
                assetMap = this[at + 's'];
                if (!assetMap) {
                    return [2 /*return*/, null];
                }
                isFont = at === 'font';
                it = isFont ? this.getFont(name) : assetMap.get(name);
                if (it) {
                    return [2 /*return*/, it];
                }
                return [2 /*return*/, new Promise(function (resolve) {
                        var url = isFont ? 'common/' + at + '/' + mc.lang + '/' + name : 'common/' + at + '/' + name;
                        ResLoader_1.loader.load(url, type).then(function (asset) {
                            var _a;
                            if (!asset) {
                            }
                            else if (isFont) {
                                (_a = _this.getFontMap()) === null || _a === void 0 ? void 0 : _a.set(asset.name, asset);
                            }
                            else if (at === 'json') {
                                _this.addJsonAsset(asset);
                            }
                            else {
                                assetMap.set(asset.name, asset);
                            }
                            resolve(asset);
                        });
                    })];
            });
        });
    };
    // 加载临时资源
    AssetsMgr.prototype.loadTempRes = function (name, type, tag, progress) {
        return __awaiter(this, void 0, Promise, function () {
            var it;
            var _this = this;
            return __generator(this, function (_a) {
                it = this.temps.get(name);
                if (it) {
                    return [2 /*return*/, this.addTempResRefs(it, tag)];
                }
                return [2 /*return*/, new Promise(function (resolve) {
                        var url = 'tmp/' + _this.makeTypeName(type) + '/' + name;
                        // 放入队列
                        var queues = _this.loadTempQueueMap.get(url);
                        if (queues) {
                            return queues.push({ cb: resolve, tag: tag });
                        }
                        _this.loadTempQueueMap.set(url, [{ cb: resolve, tag: tag }]);
                        // 开始加载
                        ResLoader_1.loader.load(url, type, progress).then(function (asset) {
                            var queues2 = _this.loadTempQueueMap.get(url);
                            if (!queues2) {
                                return ResLoader_1.loader.printError('加载错误 not funcs? url=' + url);
                            }
                            else {
                                // 先添加引用
                                if (asset) {
                                    queues2.forEach(function (m) { return _this.addTempRes(name, url, asset, type, m.tag); });
                                }
                                // 再回调回去 先添加引用是防止在回调的时候被释放了
                                queues2.forEach(function (m) { return m.cb(asset); });
                            }
                            _this.loadTempQueueMap.delete(url);
                        });
                    })];
            });
        });
    };
    // 加载临时资源 文件夹
    AssetsMgr.prototype.loadTempRseDir = function (key, type, tag, progress) {
        return __awaiter(this, void 0, Promise, function () {
            var head, assets, i, l, asset, name;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        head = 'tmp/' + this.makeTypeName(type) + '/';
                        return [4 /*yield*/, ResLoader_1.loader.loadDir(head + key, type, progress)];
                    case 1:
                        assets = _a.sent();
                        for (i = 0, l = assets.length; i < l; i++) {
                            asset = assets[i];
                            name = key + '/' + asset.name;
                            this.addTempRes(name, head + name, asset, type, tag);
                        }
                        return [2 /*return*/, assets];
                }
            });
        });
    };
    // 加载远程图片
    AssetsMgr.prototype.loadRemote = function (url, ext, tag) {
        return __awaiter(this, void 0, Promise, function () {
            var it, asset, sf;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!url) {
                            return [2 /*return*/, null];
                        }
                        it = this.temps.get(url);
                        if (it) {
                            return [2 /*return*/, this.addTempResRefs(it, tag)];
                        }
                        return [4 /*yield*/, ResLoader_1.loader.loadRemote(url, ext)];
                    case 1:
                        asset = _a.sent();
                        if (!asset) {
                            return [2 /*return*/, null];
                        }
                        else if (!(asset instanceof cc.Texture2D)) {
                            ResLoader_1.loader.printError('加载错误 格式不对 url=' + url);
                            return [2 /*return*/, null];
                        }
                        sf = new cc.SpriteFrame(asset);
                        this.addTempRes(url, url, sf, cc.SpriteFrame, tag);
                        return [2 /*return*/, sf];
                }
            });
        });
    };
    // 添加临时资源
    AssetsMgr.prototype.addTempRes = function (name, url, asset, type, tag) {
        var it = this.temps.get(name);
        if (!it) {
            // 添加一次资源本身的引用计数
            asset.addRef();
            // 计入缓存
            it = { name: name, asset: asset, url: url, type: type, refs: [] };
            this.temps.set(name, it);
        }
        return this.addTempResRefs(it, tag);
    };
    // 添加临时资源引用
    AssetsMgr.prototype.addTempResRefs = function (it, tag) {
        tag = tag || '';
        var ref = it.refs.find(function (m) { return m.key === tag; });
        if (!ref) {
            ref = it.refs.add({ key: tag, count: 0 });
        }
        ref.count += 1;
        // 打印
        ResLoader_1.loader.printInfo("loadTempRes -> " + it.url + " [" + ref.count + "] <" + tag + ">");
        return it.asset;
    };
    // 删除临时资源引用
    AssetsMgr.prototype.removeTempResRef = function (it, tag) {
        var ref = it.refs.remove('key', tag);
        if (it.refs.length === 0) {
            this.temps.delete(it.name);
            ResLoader_1.loader.releaseRes(it.url, it.type);
        }
        else if (ref) {
            ResLoader_1.loader.printInfo("removeTempRes -> " + it.url + " [0] <" + tag + ">");
        }
    };
    // 释放临时资源
    AssetsMgr.prototype.releaseTempRes = function (name, tag) {
        var it = this.temps.get(name);
        if (!it) {
            return ResLoader_1.loader.printInfo('try release null res[' + name + ']');
        }
        tag = tag || '';
        var ref = it.refs.find(function (m) { return m.key === tag; });
        if (!ref) {
            return ResLoader_1.loader.printError('release error not ref[' + tag + '] at ' + it.url);
        }
        ref.count -= 1;
        if (ref.count <= 0) {
            this.removeTempResRef(it, tag);
        }
        else {
            ResLoader_1.loader.printInfo("removeTempRes -> " + it.url + " [" + ref.count + "] <" + tag + ">");
        }
    };
    // 强行释放资源
    AssetsMgr.prototype.releaseTempAsset = function (name) {
        var it = this.temps.get(name);
        if (!it) {
            return ResLoader_1.loader.printInfo('try release null res[' + name + ']');
        }
        this.temps.delete(name);
        ResLoader_1.loader.releaseAsset(it.url, it.type);
    };
    // 释放所有标记的临时资源
    AssetsMgr.prototype.releaseTempResByTag = function (tag) {
        var _this = this;
        this.temps.forEach(function (it) { return _this.removeTempResRef(it, tag); });
    };
    // 加载一次性资源 不缓存 
    // 注意：需要和releaseOnceRes成对使用
    AssetsMgr.prototype.loadOnceRes = function (name, type) {
        return __awaiter(this, void 0, void 0, function () {
            var url, asset;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        url = 'tmp/' + this.makeTypeName(type) + '/' + name;
                        return [4 /*yield*/, ResLoader_1.loader.load(url, type)];
                    case 1:
                        asset = _a.sent();
                        asset && asset.addRef();
                        return [2 /*return*/, asset];
                }
            });
        });
    };
    AssetsMgr.prototype.releaseOnceRes = function (name, type) {
        var url = 'tmp/' + this.makeTypeName(type) + '/' + name;
        ResLoader_1.loader.releaseRes(url, type);
    };
    return AssetsMgr;
}());
window['assetsMgr'] = new AssetsMgr();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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