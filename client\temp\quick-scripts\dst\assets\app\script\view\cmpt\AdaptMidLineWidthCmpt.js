
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/AdaptMidLineWidthCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '023dcrXH55EkqAxhaUbc405', 'AdaptMidLineWidthCmpt');
// app/script/view/cmpt/AdaptMidLineWidthCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 适配文本和线(两端文本/节点 中间线)
 */
var AdaptMidLineWidthCmpt = /** @class */ (function (_super) {
    __extends(AdaptMidLineWidthCmpt, _super);
    function AdaptMidLineWidthCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.line = null;
        _this.nodes = [];
        _this.interval = 0;
        _this.isLeft = true;
        _this.preNodeWidth = -1;
        return _this;
    }
    Object.defineProperty(AdaptMidLineWidthCmpt.prototype, "nodesLength", {
        get: function () {
            return this.nodes.filter(function (m) { return m.active; }).length;
        },
        enumerable: false,
        configurable: true
    });
    AdaptMidLineWidthCmpt.prototype.update = function (dt) {
        if (this.line === null || this.nodes.length < 1 || this.nodes[0].width === this.preNodeWidth) {
            return;
        }
        this.preNodeWidth = this.nodes[0].width;
        if (this.isLeft) {
            this.line.anchorX = 0;
            this.line.x = this.preNodeWidth + this.interval;
        }
        else {
            this.line.anchorX = 1;
            this.line.x = this.node.width - this.preNodeWidth - this.interval;
        }
        this.line.width = this.node.width - this.nodes.reduce(function (val, cur) { return val + (cur.active ? cur.width : 0); }, 0) - this.interval * this.nodesLength;
    };
    __decorate([
        property(cc.Node)
    ], AdaptMidLineWidthCmpt.prototype, "line", void 0);
    __decorate([
        property([cc.Node])
    ], AdaptMidLineWidthCmpt.prototype, "nodes", void 0);
    __decorate([
        property
    ], AdaptMidLineWidthCmpt.prototype, "interval", void 0);
    __decorate([
        property({ tooltip: CC_DEV && '中间线的锚点' })
    ], AdaptMidLineWidthCmpt.prototype, "isLeft", void 0);
    AdaptMidLineWidthCmpt = __decorate([
        ccclass
    ], AdaptMidLineWidthCmpt);
    return AdaptMidLineWidthCmpt;
}(cc.Component));
exports.default = AdaptMidLineWidthCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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