
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/TWLogoCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2954aApoLxCiYCgDO2vlQ9K', 'TWLogoCmpt');
// app/script/view/lobby/TWLogoCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var TWLogoCmpt = /** @class */ (function (_super) {
    __extends(TWLogoCmpt, _super);
    function TWLogoCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.startPos = cc.v2();
        _this.targetPos = cc.v2();
        _this.speed = 50;
        _this.targets = [];
        return _this;
    }
    TWLogoCmpt.prototype.start = function () {
        this.targets = [this.startPos, this.targetPos];
        this.node.setPosition(this.startPos);
        this.run(1, -1);
    };
    TWLogoCmpt.prototype.run = function (index, dir) {
        var _this = this;
        this.Component(cc.Animation).play('tw_logo_move');
        this.node.scaleX = dir * 4;
        var pos = this.targets[index];
        cc.tween(this.node)
            .to(this.speed, { x: pos.x, y: pos.y })
            .call(function () { return _this.Component(cc.Animation).play('tw_logo_stand'); })
            .delay(ut.random(0, 3))
            .call(function () {
            _this.run(ut.loopValue(index + 1, _this.targets.length), dir * -1);
        }).start();
    };
    __decorate([
        property
    ], TWLogoCmpt.prototype, "startPos", void 0);
    __decorate([
        property
    ], TWLogoCmpt.prototype, "targetPos", void 0);
    __decorate([
        property
    ], TWLogoCmpt.prototype, "speed", void 0);
    TWLogoCmpt = __decorate([
        ccclass
    ], TWLogoCmpt);
    return TWLogoCmpt;
}(cc.Component));
exports.default = TWLogoCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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