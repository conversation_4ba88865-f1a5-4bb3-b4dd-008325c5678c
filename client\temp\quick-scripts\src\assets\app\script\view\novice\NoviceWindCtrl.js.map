{"version": 3, "sources": ["assets\\app\\script\\view\\novice\\NoviceWindCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAA4D;AAC5D,2DAAiP;AAEjP,qDAAoD;AACpD,qDAAuE;AACvE,0DAAqD;AACrD,wDAAmD;AACnD,6DAA4D;AAC5D,6DAAyD;AACzD,+DAA8D;AAC9D,2DAA0D;AAC1D,2EAA0E;AAC1E,2DAA0D;AAC1D,6DAA4D;AAC5D,6DAA6D;AAM7D,qDAAgD;AAChD,yDAAoD;AACpD,qDAAgD;AAChD,2DAAsD;AACtD,+CAA0C;AAC1C,2DAAsD;AAE9C,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA4C,kCAAe;IAA3D;QAAA,qEA00BC;QAx0BA,0BAA0B;QAClB,cAAQ,GAAY,IAAI,CAAA,CAAC,oBAAoB;QAC7C,qBAAe,GAAY,IAAI,CAAA,CAAC,kCAAkC;QAClE,oBAAc,GAAY,IAAI,CAAA,CAAC,iCAAiC;QAChE,qBAAe,GAAY,IAAI,CAAA,CAAC,4BAA4B;QAC5D,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,oBAAc,GAAY,IAAI,CAAA,CAAC,iCAAiC;QAChE,oBAAc,GAAY,IAAI,CAAA,CAAC,iCAAiC;QAChE,sBAAgB,GAAY,IAAI,CAAA,CAAC,6BAA6B;QAC9D,mBAAa,GAAY,IAAI,CAAA,CAAC,0BAA0B;QACxD,oBAAc,GAAY,IAAI,CAAA,CAAC,2BAA2B;QAClE,MAAM;QAEW,cAAQ,GAAW,eAAe,CAAA;QAE3C,YAAM,GAAY,IAAI,CAAA,CAAA,SAAS;QAC/B,oBAAc,GAAY,IAAI,CAAA,CAAA,SAAS;QACvC,kBAAY,GAAY,IAAI,CAAA,CAAA,IAAI;QAChC,iBAAW,GAAY,IAAI,CAAA,CAAC,IAAI;QAChC,cAAQ,GAAY,IAAI,CAAA;QACxB,cAAQ,GAAY,IAAI,CAAA;QACxB,cAAQ,GAAY,IAAI,CAAA,CAAC,KAAK;QAC9B,cAAQ,GAAY,IAAI,CAAA,CAAC,KAAK;QAC9B,gBAAU,GAAY,IAAI,CAAA,CAAC,OAAO;QAClC,cAAQ,GAAY,IAAI,CAAA,CAAC,MAAM;QAC/B,gBAAU,GAAY,IAAI,CAAA,CAAC,QAAQ;QACnC,kBAAY,GAAiB,IAAI,CAAA;QACjC,eAAS,GAAiB,IAAI,CAAA;QAC9B,iBAAW,GAAoB,IAAI,CAAA;QAEnC,WAAK,GAAgB,IAAI,CAAA;QACzB,YAAM,GAAgB,IAAI,CAAA;QAC1B,YAAM,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA,CAAC,SAAS;QACnC,wBAAkB,GAAW,CAAC,CAAA;QAC9B,uBAAiB,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QACpC,YAAM,GAAgB,EAAE,CAAA,CAAC,QAAQ;QACjC,qBAAe,GAAkC,EAAE,CAAA,CAAC,aAAa;QACjE,uBAAiB,GAAY,KAAK,CAAA,CAAC,aAAa;QAChD,sBAAgB,GAAoB,IAAI,CAAA,CAAC,QAAQ;QACjD,yBAAmB,GAAoB,IAAI,CAAA,CAAC,QAAQ;QAEpD,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAC/B,kBAAY,GAAY,EAAE,CAAC,EAAE,EAAE,CAAA;QAE/B,gBAAU,GAA8B,EAAE,CAAA;;IAyxBnD,CAAC;IAvxBO,wCAAe,GAAtB;;QACC,OAAO;sBACJ,GAAC,kBAAQ,CAAC,aAAa,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC1D,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,SAAS,IAAG,IAAI,CAAC,UAAU,EAAE,QAAK,GAAE,IAAI;sBACnD,GAAC,mBAAS,CAAC,YAAY,IAAG,IAAI,CAAC,aAAa,EAAE,QAAK,GAAE,IAAI;sBACzD,GAAC,mBAAS,CAAC,gBAAgB,IAAG,IAAI,CAAC,gBAAgB,EAAE,QAAK,GAAE,IAAI;sBAChE,GAAC,mBAAS,CAAC,iBAAiB,IAAG,IAAI,CAAC,iBAAiB,EAAE,QAAK,GAAE,IAAI;sBAClE,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,yBAAyB,IAAG,IAAI,CAAC,wBAAwB,EAAE,QAAK,GAAE,IAAI;sBACjF,GAAC,mBAAS,CAAC,cAAc,IAAG,IAAI,CAAC,cAAc,EAAE,QAAK,GAAE,IAAI;sBAC5D,GAAC,mBAAS,CAAC,qBAAqB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACzE,GAAC,mBAAS,CAAC,WAAW,IAAG,IAAI,CAAC,WAAW,EAAE,QAAK,GAAE,IAAI;sBACtD,GAAC,mBAAS,CAAC,sBAAsB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC5E,GAAC,mBAAS,CAAC,uBAAuB,IAAG,IAAI,CAAC,sBAAsB,EAAE,QAAK,GAAE,IAAI;sBAC7E,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,oBAAoB,EAAE,QAAK,GAAE,IAAI;sBACxE,GAAC,mBAAS,CAAC,oBAAoB,IAAG,IAAI,CAAC,mBAAmB,EAAE,QAAK,GAAE,IAAI;sBACvE,GAAC,mBAAS,CAAC,2BAA2B,IAAG,IAAI,CAAC,yBAAyB,EAAE,QAAK,GAAE,IAAI;sBACpF,GAAC,mBAAS,CAAC,2BAA2B,IAAG,IAAI,CAAC,0BAA0B,EAAE,QAAK,GAAE,IAAI;sBACrF,GAAC,mBAAS,CAAC,2BAA2B,IAAG,IAAI,CAAC,yBAAyB;SACzE,CAAA;IACF,CAAC;IAEY,iCAAQ,GAArB;;;;;;wBACC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;wBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;wBAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;wBAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;wBACvD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;wBACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;wBACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;wBAC/C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;wBACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,sBAAY,CAAC,CAAA;wBACnE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,sBAAY,CAAC,CAAA;wBAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,yBAAe,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAS,CAAC,CAAC,CAAA;wBAC1G,IAAI,CAAC,mBAAmB,GAAG,IAAI,yBAAe,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAS,CAAC,CAAC,CAAA;wBACnH,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;wBACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;wBACpC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;wBACrC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,KAAK,CAAA;wBACnC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;wBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,yBAAe,CAAC,CAAA;wBAChE,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,qBAAqB,EAAE,CAAA;6BACjE,cAAc,EAAd,wBAAc;wBACjB,qBAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAA;;wBAA1D,SAA0D,CAAA;;;wBAE1D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;;4BAEzB,qBAAM,qBAAS,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACzC,OAAO;sBADkC;;wBAAzC,SAAyC,CAAA;wBAEnC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAA;6BACtC,CAAC,qBAAS,CAAC,aAAa,CAAC,OAAO,CAAC,EAAjC,wBAAiC;wBACpC,qBAAM,qBAAS,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;;wBAArC,SAAqC,CAAA;;;wBAEtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,8BAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;wBACzG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,gCAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;;;;;KACrF;IAEM,gCAAO,GAAd,UAAe,IAAS;QACvB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAA;QAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAA;QAChC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAA,CAAC,QAAQ;QAC/C,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,OAAO;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/C,oBAAO,CAAC,WAAW,EAAE,CAAA;QACrB,uBAAU,CAAC,UAAU,CAAC,0BAAe,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;QAClE,oBAAO,CAAC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAA;IACxC,CAAC;IAEM,gCAAO,GAAd;QACC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAA;QAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;QACtB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;QACtD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;QAC9B,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAA;QACxC,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,KAAK,CAAA;QACjC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAA;QACtC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,KAAK,CAAA;QAC/B,WAAW,CAAC,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvC,uBAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;QAChC,oBAAO,CAAC,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAA;IACzC,CAAC;IAEM,gCAAO,GAAd;;QACC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,MAAA,IAAI,CAAC,WAAW,0CAAE,KAAK,GAAE;QACzB,oBAAO,CAAC,cAAc,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAA;QAChD,qBAAS,CAAC,WAAW,EAAE,CAAA;QACvB,uBAAU,CAAC,cAAc,EAAE,CAAA;QAC3B,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACvC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC7C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,yCAAyC;IACzC,qCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACT,uCAAuC;YACvC,qFAAqF;YACrF,qBAAqB;YACrB,2DAA2D;YAC3D,KAAK;YACL,IAAI;YACJ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAC5B,uBAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC3B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;SAC1B;IACF,CAAC;IAED,+CAA+C;IAC/C,0CAAiB,GAAjB,UAAkB,KAA0B,EAAE,IAAY;IAE1D,CAAC;IAED,yCAAyC;IACzC,qCAAY,GAAZ,UAAa,KAA0B,EAAE,IAAY;QACpD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YAC3B,OAAM;SACN;QACD,uBAAU,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED,6CAA6C;IAC7C,yCAAgB,GAAhB,UAAiB,KAA0B,EAAE,IAAY;QACxD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE;YAC3B,OAAM;SACN;QACD,uBAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,0CAA0C;IAC1C,sCAAa,GAAb,UAAc,KAA0B,EAAE,IAAY;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,EAAE;YACV,OAAM;SACN;aAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;YAChD,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,uBAAuB,CAAC,CAAA;SAC1D;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YAC7B,OAAO,uBAAU,CAAC,SAAS,CAAC,aAAK,CAAC,oBAAoB,CAAC,CAAA;SACvD;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,wCAAwC;IACxC,oCAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QACnD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE;YACnC,OAAM;SACN;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,qDAAqD;IACrD,2CAAkB,GAAlB,UAAmB,KAA0B,EAAE,IAAY;QAC1D,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;QACxC,IAAI,IAAI,EAAE;YACT,uBAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC1E;IACF,CAAC;IACD,MAAM;IACN,iHAAiH;IAEzG,uCAAc,GAAtB;QACC,IAAI,CAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAAE,kBAAkB;YAC3D,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;YACtD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;SACzB;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAA,CAAC,QAAQ;QAC/C,IAAI,CAAC,SAAS,EAAE,CAAA,CAAC,OAAO;IACzB,CAAC;IAED,SAAS;IACD,yCAAgB,GAAxB;QACC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO;IACC,mCAAU,GAAlB,UAAmB,IAAkB;QAArC,iBAcC;QAbA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;SAC/B;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAlB,CAAkB,CAAC,CAAA;QACrD,IAAI,KAAK,EAAE;YACV,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC/C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SAC/B;aAAM;YACN,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,EAAE;gBAC7B,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,CAAA;gBAC1F,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAChC,CAAC,CAAC,CAAA;SACF;IACF,CAAC;IAED,OAAO;IACC,sCAAa,GAArB,UAAsB,IAAkB;QACvC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QACjD,IAAI,KAAK,EAAE;YACV,KAAK,CAAC,KAAK,EAAE,CAAA;YACb,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;SAC/B;IACF,CAAC;IAED,SAAS;IACD,yCAAgB,GAAxB;QACC,IAAI,CAAC,SAAS,EAAE,CAAA;IACjB,CAAC;IAED,SAAS;IACD,0CAAiB,GAAzB,UAA0B,IAAa;QACtC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,SAAS;IACD,+CAAsB,GAA9B;QACC,IAAM,KAAK,GAAiB,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAA;QACvE,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE;YAC1B,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACxB;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI,IAAK,OAAA,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAhC,CAAgC,CAAC,CAAA;IAC7E,CAAC;IAED,SAAS;IACD,iDAAwB,GAAhC;QACC,IAAI,CAAC,cAAc,EAAE,CAAA;IACtB,CAAC;IAED,SAAS;IACD,uCAAc,GAAtB,UAAuB,KAAa;;QACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3B,IAAI,OAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,0CAAE,QAAQ,MAAK,KAAK,EAAE;YACpD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;SAC9B;IACF,CAAC;IAED,0BAA0B;IAClB,6CAAoB,GAA5B;QACC,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAA;IACnC,CAAC;IAED,OAAO;IACC,oCAAW,GAAnB,UAAoB,KAAc,EAAE,YAAqB;QAAzD,iBAeC;QAdA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,OAAO,YAAY,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAA;SAC/E;aAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,oBAAoB;YACtF,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YACjG,uBAAU,CAAC,IAAI,CAAC,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,qBAAS,CAAC,QAAQ,EAAE,0BAAe,EAAE,uBAAU,CAAC,SAAS,CAAC,CAAA;YAC5G,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;YAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAA;SAC7B;QACD,KAAK;QACL,uBAAU,CAAC,MAAM,CAAC,IAAI,EAAE,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,uBAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACnG,IAAI,KAAI,CAAC,QAAQ,IAAI,YAAY,EAAE;gBAClC,KAAI,CAAC,cAAc,CAAC,KAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAA;aACxD;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,SAAS;IACD,+CAAsB,GAA9B,UAA+B,IAAgB;QAC9C,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,MAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QACpF,IAAI,EAAE,EAAE;YACP,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;SACnC;IACF,CAAC;IAED,SAAS;IACD,+CAAsB,GAA9B,UAA+B,IAAS;QACvC,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,MAAM,IAAI,OAAA,CAAC,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAC,CAAA;QACpF,IAAI,EAAE,EAAE;YACP,qBAAS,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SACnF;IACF,CAAC;IAED,WAAW;IACH,6CAAoB,GAA5B;QACC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,aAAa,EAAE,EAAjB,CAAiB,CAAC,CAAA;IAC5C,CAAC;IAED,WAAW;IACH,4CAAmB,GAA3B;QACC,IAAI,CAAC,iBAAiB,EAAE,CAAA;IACzB,CAAC;IAED,MAAM;IACE,kDAAyB,GAAjC,UAAkC,IAAS;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAC5B,yBAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACpE;IACF,CAAC;IAED,SAAS;IACD,mDAA0B,GAAlC;QAAA,iBAYC;QAXA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAClF,uBAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;YAChF,IAAI,KAAI,CAAC,QAAQ,EAAE;gBAClB,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,KAAK,EAAhB,CAAgB,CAAC,CAAA;aAClD;QACF,CAAC,CAAC,CAAC,IAAI,CAAC;YACP,IAAI,KAAI,CAAC,QAAQ,EAAE;gBAClB,KAAI,CAAC,SAAS,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;aACjC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,kDAAyB,GAAjC;QACC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;IACjC,CAAC;IAGD,sBAAY,oCAAQ;QAFpB,iHAAiH;aAEjH,cAAyB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAA,CAAC,CAAC;;;OAAA;IAEhE,OAAO;IACC,mCAAU,GAAlB,UAAmB,aAAsB;;QACxC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,qBAAS,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAA;QACnF,IAAI,OAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,EAAE,MAAK,wBAAa,IAAI,oBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,0BAAY,CAAC,iBAAiB,CAAC,EAAE;YAChG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,2BAA2B,CAAC,CAAA;SAChD;aAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC9C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YACzB,uBAAU,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,EAAE,+BAAoB,CAAC,CAAA;SACzE;aAAM;YACN,IAAI,CAAC,cAAc,EAAE,CAAA;SACrB;IACF,CAAC;IAED,OAAO;IACC,kCAAS,GAAjB,UAAkB,MAAe,EAAE,aAAuB;QAA1D,iBA8LC;;QA7LA,IAAI,CAAC,kBAAkB,GAAG,uBAAU,CAAC,SAAS,CAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC5B,OAAO;QACP,IAAM,iBAAiB,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAA;QAC3D,IAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAA;QACrK,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAA;QAChD,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,GAAiB,EAAE,EAAE,OAAO,GAAU,EAAE,CAAA;QACtH,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAA;QAChC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;QAC7B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,IAAM,MAAM,GAAG,qBAAS,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAA;QACpF,IAAM,QAAQ,GAAG,qBAAS,CAAC,QAAQ,CAAA;QACnC,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAA;QAC/B,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAA;QACnC,IAAI,qBAAqB,GAAG,EAAE,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;YACvB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;YAChD,IAAM,QAAQ,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,KAAI,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACnE,IAAI,IAAI,EAAE;gBACT,IAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACpC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;gBACvC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;oBACxB,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,IAAI,CAAC,iBAAiB,EAAE;wBACxD,MAAM,GAAG,wBAAa,GAAG,EAAE,CAAA;qBAC3B;oBACD,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,KAAK,wBAAa,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;oBAC7F,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBACxE,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE;wBAClC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,eAAe;qBAChC;oBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;iBACzC;qBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;oBAC1E,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,KAAK,IAAI,qCAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;wBACpH,IAAI,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAChF,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;wBAC5G,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;qBACnB;yBACI;wBACJ,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;qBAChK;iBACD;gBACD,SAAS;gBACT,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,wBAAa,EAAE;oBAC/D,uBAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;iBACrE;gBACD,IAAI,MAAM,EAAE,EAAE,QAAQ;oBACrB,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,QAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;oBAC9B,cAAc;oBACd,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE;wBACvC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;qBAClE;iBACD;gBACD,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY;oBAC5C,SAAS;oBACT,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,0CAAE,UAAU,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBAC/I,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA,CAAC,QAAQ;oBAC1E,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAA;iBAC5H;gBACD,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ;oBACzC,SAAS;oBACT,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,0CAAE,UAAU,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;oBAC/I,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAA,CAAC,QAAQ;oBACpG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAA;iBAChI;gBACD,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ;oBACvC,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;iBACjE;gBACD,QAAQ;gBACR,IAAI,iBAAiB,EAAE;oBACtB,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;oBACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;wBAC3B,IAAI,YAAY,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAA;wBAC7E,uBAAU,CAAC,qBAAqB,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;qBACtF;iBACD;gBACD,OAAO;gBACP,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,EAAE;oBAClD,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAA;iBACvD;gBACD,QAAQ;gBACR,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChE,IAAI,cAAc,EAAE;oBACnB,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,GAAC,GAAG,cAAc,CAAC,MAAM,EAAE,GAAC,EAAE,EAAE;wBACzC,IAAA,KAAyC,cAAc,CAAC,GAAC,CAAC,EAAxD,cAAc,oBAAA,EAAE,kBAAkB,wBAAsB,CAAA;wBAChE,IAAI,OAAO,GAAG,kBAAkB,GAAG,GAAG,GAAG,cAAc,CAAC,EAAE,CAAA;wBAC1D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE;4BACpC,qBAAqB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;4BACrC,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAA;4BAC/E,IAAI,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;4BAC3E,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAA;4BACvE,IAAI,QAAQ,GAAY,IAAI,CAAA;4BAC5B,IAAI,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,MAAM,IAAI,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,IAAI,sBAAc,CAAC,SAAS,EAAE;gCACjJ,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,CAAA;6BACnE;iCACI,IAAI,qCAAiB,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE;gCAC3D,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,WAAW,CAAC,CAAA;6BAC1E;iCACI;gCACJ,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,WAAW,CAAC,CAAA;6BAC5E;4BACD,IAAI,MAAM,GAAG,CAAC,CAAA;4BACd,QAAQ,cAAc,CAAC,IAAI,EAAE;gCAC5B,KAAK,sBAAc,CAAC,QAAQ,CAAC;gCAC7B,KAAK,sBAAc,CAAC,wBAAwB,CAAC;gCAC7C,KAAK,sBAAc,CAAC,oBAAoB;oCACvC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,CAAA;oCAC5B,MAAM;gCACP,KAAK,sBAAc,CAAC,MAAM;oCACzB,MAAM,GAAG,CAAC,CAAA;oCACV,MAAM;6BACP;4BACD,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAA;4BACxB,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,KAAK,CAAA;4BACjD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;yBAC7C;qBACD;iBACD;aACD;iBACI;gBACJ,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;gBACpD,IAAI,MAAM,EAAE;oBACX,IAAI,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;oBACpD,IAAI,QAAQ,GAAG,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAA;oBAC3E,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;oBAChH,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;iBACnB;aACD;SACD;QACD,QAAQ;QACR,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;QACnD,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QAC1C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;QAC/C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;QAC5C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QAC9C,qBAAS,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;QACjD,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAA;QACxC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAA;QACrC,UAAU;QACV,IAAI,SAAS,eAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,0CAAE,QAAQ,mCAAI,CAAC,CAAC,CAAA;QAC3D,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3C,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;SAC1C;QACD,QAAQ;QACR,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;;YACpC,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAA;YAC5B,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;YACjC,IAAI,OAAA,EAAE,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;gBAClC,IAAM,IAAI,GAAG,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACjD,qBAAS,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,KAAI,CAAC,QAAQ,CAAC,CAAA;gBACpF,KAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;aACnC;YACD,IAAI,aAAa,EAAE;gBAClB,EAAE,CAAC,OAAO,GAAG,CAAC,CAAA;gBACd,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,EAAE,CAAA;aAC9C;iBAAM;gBACN,EAAE,CAAC,OAAO,GAAG,GAAG,CAAA;aAChB;YACD,EAAE,CAAC,MAAM,GAAG,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAA;YACvC,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAA;QACtD,CAAC,CAAC,CAAA;QACF,YAAY;QACZ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,UAAC,EAAE,EAAE,IAAI;;YACvC,IAAM,IAAI,GAAc,IAAI,CAAC,MAAM,CAAA;YACnC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjC,IAAI,OAAA,EAAE,CAAC,IAAI,0CAAE,KAAK,MAAK,IAAI,CAAC,KAAK,EAAE;gBAClC,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBACzC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;gBAC/C,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,CAAA;gBAC1D,KAAK;gBACL,IAAM,MAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,CAAA;gBAC3C,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,KAAK,CAAA;gBACpE,IAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAC1B,KAAK,CAAC,IAAI,EAAE,CAAA;gBACZ,IAAI,WAAW,GAAG,IAAI,EAAE;oBACvB,MAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;oBACrC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,OAAO,IAAI,MAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAvC,CAAuC,CAAC,CAAC,KAAK,EAAE,CAAA;iBAC3F;qBAAM;oBACN,MAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;iBACvB;aACD;YACD,QAAQ,CAAC,MAAM,GAAG,SAAS,KAAK,IAAI,CAAC,KAAK,CAAA;YAC1C,EAAE,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAA;QAChC,CAAC,CAAC,CAAA;IACH,CAAC;IAED,QAAQ;IACD,6CAAoB,GAA3B,UAA4B,QAAiB,EAAE,IAAY;QAC1D,IAAI,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAA;QACvE,IAAI,IAAI,GAAG,KAAK,CAAC,eAAe,EAAE,CAAA;QAClC,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAS,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,QAAQ,CAAC,CAAC,IAAI,oBAAS,GAAG,CAAC,CAAA;SAC3B;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,oBAAS,GAAG,CAAC,IAAI,CAAC,EAAE;YACrC,QAAQ,CAAC,CAAC,IAAI,oBAAS,GAAG,CAAC,CAAA;SAC3B;IACF,CAAC;IAEO,uCAAc,GAAtB,UAAuB,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QAC5F,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YACnB,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACpD;aAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;YAC1B,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAC7B;QACD,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACrD,CAAC;IAEO,mCAAU,GAAlB,UAAmB,EAAW,EAAE,IAAY,EAAE,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QACnH,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC9D,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAI,IAAI,SAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAG,CAAC,CAAA;IACtF,CAAC;IAED,KAAK;IACG,uCAAc,GAAtB,UAAuB,QAAiB,EAAE,KAAc,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QAC/G,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA;QAC7G,IAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChE,EAAE,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,EAAE,CAAA;IACvB,CAAC;IAEO,oCAAW,GAAnB,UAAoB,IAAY;QAC/B,OAAO,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAEO,6CAAoB,GAA5B,UAA6B,EAAW,EAAE,IAAgB;;QACzD,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QAC9C,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,OAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,mCAAI,KAAK,EAAE,CAAC,CAAC,CAAA;QAC5D,OAAO,CAAC,sBAAsB,EAAE,CAAA;QAChC,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC/D,IAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;QACjD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAA,CAAC,EAAE;YACtC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;YACvD,QAAQ,CAAC,KAAK,CAAC,4BAAiB,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,KAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,YAAY,IAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,EAAE,CAAA,CAAC,CAAA;YAC3F,QAAQ,CAAC,sBAAsB,EAAE,CAAA;YACjC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAA;SACpB;aAAM;YACN,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;SAClB;QACD,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC9C,CAAC;IAED,WAAW;IACH,8CAAqB,GAA7B,UAA8B,KAAkB;QAAlB,sBAAA,EAAA,SAAiB,CAAC;QAC/C,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAA7C,CAA6C,CAAC,CAAA;QACnF,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,EAA3D,CAA2D,CAAC,CAAA;IACnG,CAAC;IAED,QAAQ;IACA,uCAAc,GAAtB;QAAA,iBAgBC;QAfA,IAAM,OAAO,GAAY,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QACxC,IAAM,OAAO,GAAY,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;QACvC,IAAM,KAAK,GAAU,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAA;QACtH,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE;YAC9B,IAAM,IAAI,GAAe,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACpD,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAA;SACvF;QACD,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE;YAClC,IAAM,IAAI,GAAe,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACpD,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAA;SACrG;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,EAAE,EAAE,IAAI;YACnC,EAAE,CAAC,WAAW,CAAC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;YACzE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,GAAG,qBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvE,CAAC,CAAC,CAAA;IACH,CAAC;IAED,SAAS;IACD,uCAAc,GAAtB,UAAuB,IAAgB;QACtC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,gBAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,IAAI,gBAAQ,CAAC,KAAK,EAAE;YAC9E,OAAM;SACN;aAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;YACxC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAClD;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA;QACxD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACjC,SAAS;QACT,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACzC,wEAAwE;IACzE,CAAC;IAED,KAAK;IACG,uCAAc,GAAtB,UAAuB,IAAoB;QAApB,qBAAA,EAAA,WAAoB;QAC1C,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;YAC9B,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAc,CAAC,CAAC,KAAK,EAAE,CAAA;YACtD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAA;SAC5B;IACF,CAAC;IAED,QAAQ;IACA,kCAAS,GAAjB;QAAA,iBAQC;QAPA,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,cAAc,EAAE,EAAlB,CAAkB,CAAC,CAAA;QACtE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;YACxC,IAAM,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,mBAAS,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAI,CAAC,cAAc,EAAE,KAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YAChG,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAA;QAChC,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,iBAAiB,IAAI,KAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAA9D,CAA8D,CAAC,CAAA;IACzF,CAAC;IAEO,mCAAU,GAAlB;QACC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAA;SACzB;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;QAChB,+EAA+E;QAC/E,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAA;IACxC,CAAC;IAED,UAAU;IACF,6CAAoB,GAA5B,UAA6B,IAAkB;QAC9C,IAAM,MAAM,GAAgB,EAAE,CAAA;QAC9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACpB,IAAM,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;YACrB,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;gBAChC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;gBAC3D,CAAC,CAAC,iBAAiB,GAAG,IAAI,CAAA;gBAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACd;QACF,CAAC,CAAC,CAAA;QACF,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAA;QACzB,MAAM,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;IACrD,CAAC;IAED,OAAO;IACO,mCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBACrC,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BAC3B,sBAAM;yBACN;wBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;wBACO,qBAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAA;;wBAA3E,KAA8B,SAA6C,EAAzE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,YAAY,kBAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;wBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BACnB,sBAAM;yBACN;6BAAM,IAAI,GAAG,KAAK,aAAK,CAAC,kBAAkB,EAAE;4BAC5C,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;4BAC1B,sBAAO,uBAAU,CAAC,cAAc,CAAC,2BAA2B,CAAC,EAAA;yBAC7D;6BAAM,IAAI,GAAG,KAAK,aAAK,CAAC,0BAA0B,EAAE,EAAE,aAAa;4BACnE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;4BAC1B,sBAAO,uBAAU,CAAC,cAAc,CAAC,mCAAmC,CAAC,EAAA;yBACrE;6BAAM,IAAI,GAAG,EAAE;4BACf,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC7B,sBAAO,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAA;yBAClD;wBACD,sDAAsD;wBACtD,qDAAqD;wBACrD,sBAAsB;wBACtB,gEAAgE;wBAChE,KAAK;wBACL,IAAI;wBACJ,uBAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,UAAC,KAAsB,EAAE,WAAoB,EAAE,YAAoB;4BAC7I,IAAI,KAAI,CAAC,QAAQ,EAAE;gCAClB,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCAC1B,IAAM,aAAa,GAAG,oBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,0BAAY,CAAC,iBAAiB,CAAC,CAAA;gCAC7E,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;6BAC3H;wBACF,CAAC,CAAC,CAAA;;;;;KACF;IAED,QAAQ;IACM,mCAAU,GAAxB,UAAyB,KAAa;;;;;;;wBACrC,IAAI,IAAI,CAAC,iBAAiB,EAAE;4BAC3B,sBAAM;yBACN;wBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;wBACO,qBAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,EAAA;;wBAAxE,KAA8B,SAA0C,EAAtE,GAAG,SAAA,EAAE,IAAI,UAAA,EAAE,YAAY,kBAAA;wBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;wBAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;4BACnB,sBAAM;yBACN;6BAAM,IAAI,GAAG,EAAE;4BACf,sBAAO,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAA;yBAChC;6BAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;4BAC7B,sBAAO,uBAAU,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAA;yBAClD;wBACD,uBAAU,CAAC,OAAO,CAAC,iBAAiB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,UAAC,KAAsB,EAAE,WAAoB;4BACrH,IAAI,KAAI,CAAC,QAAQ,EAAE;gCAClB,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gCAC1B,KAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,uBAAU,CAAC,SAAS,CAAC,GAAG,CAAC,EAAhC,CAAgC,CAAC,CAAA;6BACvG;wBACF,CAAC,CAAC,CAAA;;;;;KACF;IAED,WAAW;IACH,0CAAiB,GAAzB;QAAA,iBAYC;QAXA,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,UAAA,KAAK;YAC5C,IAAM,IAAI,GAAG,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YACxC,IAAI,IAAI,EAAE;gBACT,IAAM,MAAI,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,GAAG,0BAAe,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAA;gBACnF,IAAM,KAAG,GAAG,IAAI,CAAC,WAAW,EAAE,QAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;gBACtD,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,CAAC,IAAK,OAAA,uBAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,QAAM,EAAE,KAAI,CAAC,aAAa,EAAE,KAAG,EAAE,KAAI,CAAC,GAAG,CAAC,EAAvG,CAAuG,CAAC,CAAA;gBACjI,uBAAU,CAAC,iBAAiB,CAAC,KAAI,CAAC,eAAe,EAAE,KAAG,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;gBACjE,QAAQ;gBACR,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAA1C,CAA0C,CAAC,CAAA;aACxE;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAEO,wCAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAM,QAAQ,GAAG,qBAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAA;YACnE,uBAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YAC9F,uBAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACtE;IACF,CAAC;IAED,+BAAM,GAAN,UAAO,EAAU;;QAChB,EAAE;QACF,MAAA,IAAI,CAAC,mBAAmB,0CAAE,MAAM,CAAC,EAAE,EAAC;QACpC,aAAa;QACb,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,YAAY;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC1B,CAAC;IAEO,uCAAc,GAAtB;QACC,IAAM,KAAK,GAAG,qBAAS,CAAC,eAAe,CAAC,uBAAU,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;QAC1F,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACzF,IAAI,IAAI,IAAI,yBAAc,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,uBAAU,CAAC,SAAS,EAAE;YACnF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YACrB,IAAI,CAAC,sBAAsB,EAAE,CAAA;SAC7B;IACF,CAAC;IAED,kBAAkB;IACV,+CAAsB,GAA9B;QACC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,mBAAmB,EAAE,EAAvB,CAAuB,CAAC,CAAA;IAClD,CAAC;IAEO,2CAAkB,GAA1B;;QACC,IAAM,QAAQ,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAA;QACzC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC5C,OAAM;SACN;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACpC,QAAQ;QACR,UAAI,IAAI,CAAC,YAAY,0CAAE,qBAAqB,IAAI;YAC/C,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;SAC1B;IACF,CAAC;IAz0BmB,cAAc;QADlC,OAAO;OACa,cAAc,CA00BlC;IAAD,qBAAC;CA10BD,AA00BC,CA10B2C,EAAE,CAAC,YAAY,GA00B1D;kBA10BoB,cAAc", "file": "", "sourceRoot": "/", "sourcesContent": ["import { cameraCtrl } from \"../../common/camera/CameraCtrl\";\nimport { ACHIEVEMENT_COLOR, CAMERA_BG_COLOR, CELL_RES_FIELDS, CITY_FORT_NID, CITY_MAIN_NID, MAP_EXTRA_SIZE, MAP_MASK_ITEM_COLOR, MAP_MASK_ITEM_OPACITY, MAP_SHOW_OFFSET, SELECT_CELL_INFO_BOX, TILE_SIZE } from \"../../common/constant/Constant\";\nimport { ArmyShortInfo, PlayerInfo } from \"../../common/constant/DataType\";\nimport { ecode } from \"../../common/constant/ECode\";\nimport { DecorationType, LandType } from \"../../common/constant/Enums\";\nimport EventType from \"../../common/event/EventType\";\nimport NetEvent from \"../../common/event/NetEvent\";\nimport { animHelper } from \"../../common/helper/AnimHelper\";\nimport { gameHpr } from \"../../common/helper/GameHelper\";\nimport { guideHelper } from \"../../common/helper/GuideHelper\";\nimport { mapHelper } from \"../../common/helper/MapHelper\";\nimport { mapUionFindHelper } from \"../../common/helper/MapUionFindHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\nimport { GuideTagType } from \"../../model/guide/GuideConfig\";\nimport NoviceModel from \"../../model/guide/NoviceModel\";\nimport BaseMarchObj from \"../../model/main/BaseMarchObj\";\nimport BTCityObj from \"../../model/main/BTCityObj\";\nimport MapCellObj from \"../../model/main/MapCellObj\";\nimport PlayerModel from \"../../model/main/PlayerModel\";\nimport MapTouchCmpt from \"../cmpt/MapTouchCmpt\";\nimport SelectCellCmpt from \"../cmpt/SelectCellCmpt\";\nimport CellInfoCmpt from \"../main/CellInfoCmpt\";\nimport MapAnimNodePool from \"../main/MapAnimNodePool\";\nimport MarchCmpt from \"../main/MarchCmpt\";\nimport SceneEffectCmpt from \"../main/SceneEffectCmpt\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class NoviceWindCtrl extends mc.BaseWindCtrl {\n\n\t//@autocode property begin\n\tprivate mapNode_: cc.Node = null // path://root/map_n\n\tprivate cellEffectNode_: cc.Node = null // path://root/map_n/cell_effect_n\n\tprivate cellEmojiNode_: cc.Node = null // path://root/map_n/cell_emoji_n\n\tprivate selectCellNode_: cc.Node = null // path://root/select_cell_n\n\tprivate textNode_: cc.Node = null // path://root/text_n\n\tprivate marchLineNode_: cc.Node = null // path://root/march/march_line_n\n\tprivate marchRoleNode_: cc.Node = null // path://root/march/march_role_n\n\tprivate sceneEffectNode_: cc.Node = null // path://root/scene_effect_n\n\tprivate topLayerNode_: cc.Node = null // path://root/top_layer_n\n\tprivate weakGuideNode_: cc.Node = null // path://root/weak_guide_n\n\t//@end\n\n\tprivate readonly INIT_KEY: string = '_init_novice_'\n\n\tprivate diNode: cc.Node = null//装饰-地块下面\n\tprivate decorationNode: cc.Node = null//装饰-地块上面\n\tprivate mountainNode: cc.Node = null//山脉\n\tprivate seawaveNode: cc.Node = null //海浪\n\tprivate lineNode: cc.Node = null\n\tprivate landNode: cc.Node = null\n\tprivate cityNode: cc.Node = null //城市层\n\tprivate maskNode: cc.Node = null //遮罩层\n\tprivate btinfoNode: cc.Node = null //修建信息层\n\tprivate iconNode: cc.Node = null //小图标层\n\tprivate battleNode: cc.Node = null //战斗中图标层\n\tprivate cellInfoCmpt: CellInfoCmpt = null\n\tprivate touchCmpt: MapTouchCmpt = null\n\tprivate sceneEffect: SceneEffectCmpt = null\n\n\tprivate model: NoviceModel = null\n\tprivate player: PlayerModel = null\n\tprivate centre: cc.Vec2 = cc.v2() //当前的中心位置\n\tprivate preCameraZoomRatio: number = 0\n\tprivate preCameraPosition: cc.Vec2 = cc.v2()\n\tprivate marchs: MarchCmpt[] = [] //当前所有行军\n\tprivate tempShowCellMap: { [key: number]: MapCellObj } = {} //当前屏幕显示的地块信息\n\tprivate reqSelectArmysing: boolean = false //当前是否请求军队列表中\n\tprivate cityAnimNodePool: MapAnimNodePool = null //城市节点管理\n\tprivate seawaveAnimNodePool: MapAnimNodePool = null //海浪节点管理\n\n\tprivate _temp_vec2_1: cc.Vec2 = cc.v2()\n\tprivate _temp_vec2_2: cc.Vec2 = cc.v2()\n\tprivate _temp_vec2_3: cc.Vec2 = cc.v2()\n\tprivate _temp_vec2_4: cc.Vec2 = cc.v2()\n\tprivate _temp_vec2_5: cc.Vec2 = cc.v2()\n\n\tprivate _temp_city: { [key: number]: number } = {}\n\n\tpublic listenEventMaps() {\n\t\treturn [\n\t\t\t{ [NetEvent.NET_RECONNECT]: this.onNetReconnect, enter: true },\n\t\t\t{ [EventType.UPDATE_CELL_INFO]: this.onUpdateCellInfo, enter: true },\n\t\t\t{ [EventType.ADD_MARCH]: this.onAddMarch, enter: true },\n\t\t\t{ [EventType.REMOVE_MARCH]: this.onRemoveMarch, enter: true },\n\t\t\t{ [EventType.UPDATE_ALL_MARCH]: this.onUpdateAllMarch, enter: true },\n\t\t\t{ [EventType.CLOSE_SELECT_CELL]: this.onCloseSelectCell, enter: true },\n\t\t\t{ [EventType.UPDATE_BATTLE_DIST_INFO]: this.onUpdateBattleDistInfo, enter: true },\n\t\t\t{ [EventType.UPDATE_AVOIDWAR_DIST_INFO]: this.onUpdateAvoidWarDistInfo, enter: true },\n\t\t\t{ [EventType.UPDATE_BT_CITY]: this.onUpdateBtCity, enter: true },\n\t\t\t{ [EventType.UPDATE_ARMY_DIST_INFO]: this.onUpdateArmyDistInfo, enter: true },\n\t\t\t{ [EventType.MAP_MOVE_TO]: this.onMapMoveTo, enter: true },\n\t\t\t{ [EventType.UPDATE_PLAYER_NICKNAME]: this.onUpdatePlayerNickname, enter: true },\n\t\t\t{ [EventType.UPDATE_PLAYER_HEAD_ICON]: this.onUpdatePlayerHeadIcon, enter: true },\n\t\t\t{ [EventType.UPDATE_MARCH_OPACITY]: this.onUpdateMarchOpacity, enter: true },\n\t\t\t{ [EventType.PLAY_NEW_CELL_EFFECT]: this.onPlayNewCellEffect, enter: true },\n\t\t\t{ [EventType.WEAK_GUIDE_SHOW_NODE_CHOOSE]: this.onWeakGuideShowNodeChoose, enter: true },\n\t\t\t{ [EventType.GUIDE_PLAY_RESTORE_MAINCITY]: this.onGuidePlayRestoreMaincity, enter: true },\n\t\t\t{ [EventType.GUIDE_TAG_CLEAN_NOVICE_WIND]: this.onGuideTagCleanNoviceWind },\n\t\t]\n\t}\n\n\tpublic async onCreate() {\n\t\tthis.setParam({ isClean: false })\n\t\tthis.decorationNode = this.mapNode_.FindChild('decoration')\n\t\tthis.diNode = this.mapNode_.FindChild('di')\n\t\tthis.mountainNode = this.mapNode_.FindChild('mountain')\n\t\tthis.seawaveNode = this.mapNode_.FindChild('seawave')\n\t\tthis.lineNode = this.mapNode_.FindChild('line')\n\t\tthis.landNode = this.mapNode_.FindChild('land')\n\t\tthis.cityNode = this.mapNode_.FindChild('city')\n\t\tthis.maskNode = this.mapNode_.FindChild('mask')\n\t\tthis.btinfoNode = this.mapNode_.FindChild('btinfo')\n\t\tthis.iconNode = this.mapNode_.FindChild('icon')\n\t\tthis.battleNode = this.mapNode_.FindChild('battle')\n\t\tthis.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt)\n\t\tthis.cellInfoCmpt = this.FindChild('root/cell_info', CellInfoCmpt)\n\t\tthis.cityAnimNodePool = new MapAnimNodePool().init(this.cityNode, resHelper.getCityPrefab.bind(resHelper))\n\t\tthis.seawaveAnimNodePool = new MapAnimNodePool().init(this.seawaveNode, resHelper.getSeawavePrefab.bind(resHelper))\n\t\tthis.seawaveAnimNodePool.setAnimInfo('land_104', 1.76)\n\t\tthis.model = this.getModel('novice')\n\t\tthis.player = this.getModel('player')\n\t\tthis.selectCellNode_.active = false\n\t\tthis.cellInfoCmpt.close()\n\t\tthis.sceneEffect = this.sceneEffectNode_.getComponent(SceneEffectCmpt)\n\t\tconst sceneEffectUrl = this.model.getSeason().getCurrSceneEffectUrl()\n\t\tif (sceneEffectUrl) { //加载场景特效\n\t\t\tawait this.sceneEffect.init(sceneEffectUrl, this.INIT_KEY)\n\t\t} else {\n\t\t\tthis.sceneEffect.clean()\n\t\t}\n\t\tawait resHelper.initNovice(this.INIT_KEY)\n\t\t// 检测季节\n\t\tconst curType = this.model.getSeasonType()\n\t\tif (!resHelper.checkLandSkin(curType)) {\n\t\t\tawait resHelper.initLandSkin(curType)\n\t\t}\n\t\tthis.maskNode.children[0].color = cc.Color.WHITE.fromHEX(MAP_MASK_ITEM_COLOR[this.model.getSeasonType()])\n\t\tthis.maskNode.children[0].opacity = MAP_MASK_ITEM_OPACITY[this.model.getSeasonType()]\n\t}\n\n\tpublic onEnter(data: any) {\n\t\tthis.model.initCameraInfo()\n\t\tthis.cellEffectNode_.Data = true\n\t\tthis.topLayerNode_.Data = true\n\t\tthis.updateMap(this.model.getCentre()) //刷新地图显示\n\t\tthis.initMarch() //初始化行军\n\t\tthis.playNewCellEffect()\n\t\tthis.touchCmpt.init(this.onClickMap.bind(this))\n\t\tgameHpr.playMainBgm()\n\t\tcameraCtrl.setBgColor(CAMERA_BG_COLOR[this.model.getSeasonType()])\n\t\tgameHpr.novice.isCanAttackPlayer = true\n\t}\n\n\tpublic onLeave() {\n\t\tthis.model.saveCameraInfo()\n\t\tthis.touchCmpt.clean()\n\t\tthis.selectCellNode_.Component(SelectCellCmpt).close()\n\t\tthis.cellInfoCmpt.close()\n\t\tthis.reqSelectArmysing = false\n\t\tthis.cleanMarch()\n\t\tthis.cellEffectNode_.removeAllChildren()\n\t\tthis.cellEffectNode_.Data = false\n\t\tthis.topLayerNode_.removeAllChildren()\n\t\tthis.topLayerNode_.Data = false\n\t\tnodePoolMgr.cleanUseAndRemoveItemsByTag(this.key)\n\t\tassetsMgr.releaseTempResByTag(this.key)\n\t\tcameraCtrl.setBgColor('#D1F1F3')\n\t\tgameHpr.novice.isCanAttackPlayer = false\n\t}\n\n\tpublic onClean() {\n\t\tthis.cellInfoCmpt.clean()\n\t\tthis.sceneEffect?.clean()\n\t\tgameHpr.setNoLongerTip('first_guide_task', true)\n\t\tresHelper.cleanNovice()\n\t\tanimHelper.cleanAllFinger()\n\t\tnodePoolMgr.releaseByTag(this.INIT_KEY)\n\t\tassetsMgr.releaseTempResByTag(this.INIT_KEY)\n\t}\n\n\t// ----------------------------------------- button listener function -------------------------------------------\n\t//@autocode button listener\n\n\t// path://root/cell_info/buttons/enter_be\n\tonClickEnter(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (cell) {\n\t\t\t// if (cell.cityId === CITY_MAIN_NID) {\n\t\t\t// \tlet val = gameHpr.guide.equalGuideTag(3, GuideTagType.FREE_BATTLE_GUIDE_CELL_LV2)\n\t\t\t// \tif (val !== -1) {\n\t\t\t// \t\treturn viewHelper.showAlert('toast.finish_task_first')\n\t\t\t// \t}\n\t\t\t// }\n\t\t\tthis.model.setLookCell(cell)\n\t\t\tviewHelper.gotoWind('area')\n\t\t\tthis.hideSelectCell(false)\n\t\t}\n\t}\n\n\t// path://root/cell_info/buttons/player_info_be\n\tonClickPlayerInfo(event: cc.Event.EventTouch, data: string) {\n\n\t}\n\n\t// path://root/cell_info/buttons/build_be\n\tonClickBuild(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (!cell || !cell.isOwn()) {\n\t\t\treturn\n\t\t}\n\t\tviewHelper.showPnl('main/CityList', cell)\n\t}\n\n\t// path://root/cell_info/buttons/dismantle_be\n\tonClickDismantle(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (!cell || !cell.isOwn()) {\n\t\t\treturn\n\t\t}\n\t\tviewHelper.showPnl('main/DismantleCityTip', cell)\n\t}\n\n\t// path://root/cell_info/buttons/occupy_be\n\tonClickOccupy(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (!cell) {\n\t\t\treturn\n\t\t} else if (!this.model.checkCanOccupyCell(cell)) {\n\t\t\treturn viewHelper.showAlert(ecode.ONLY_ATTACK_ADJOIN_CELL)\n\t\t} else if (cell.isAvoidWar()) {\n\t\t\treturn viewHelper.showAlert(ecode.AVOID_WAR_NOT_ATTACK)\n\t\t}\n\t\tthis.occupyCell(cell.actIndex)\n\t}\n\n\t// path://root/cell_info/buttons/move_be\n\tonClickMove(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (!cell || !cell.isOneAlliance()) {\n\t\t\treturn\n\t\t}\n\t\tthis.moveToCell(cell.actIndex)\n\t}\n\n\t// path://root/cell_info/info/stamina/stamina_desc_be\n\tonClickStaminaDesc(event: cc.Event.EventTouch, data: string) {\n\t\tconst cell = this.cellInfoCmpt.getCell()\n\t\tif (cell) {\n\t\t\tviewHelper.showPnl('main/CellDropInfo', cell.getLandAttr(), cell.landType)\n\t\t}\n\t}\n\t//@end\n\t// ----------------------------------------- event listener function --------------------------------------------\n\n\tprivate onNetReconnect() {\n\t\tif (!gameHpr.guide.isOneGuideWorking()) { //这里如果在新手引导 就不要关闭了\n\t\t\tthis.selectCellNode_.Component(SelectCellCmpt).close()\n\t\t\tthis.cellInfoCmpt.close()\n\t\t}\n\t\tthis.updateMap(this.model.getCentre()) //刷新地图显示\n\t\tthis.initMarch() //初始化行军\n\t}\n\n\t// 更新地块信息\n\tprivate onUpdateCellInfo() {\n\t\tthis.updateMap(this.centre)\n\t}\n\n\t// 添加行军\n\tprivate onAddMarch(data: BaseMarchObj) {\n\t\tif (!data.isCanShowMarch()) {\n\t\t\treturn this.onRemoveMarch(data)\n\t\t}\n\t\tlet march = this.marchs.find(m => m.uid === data.uid)\n\t\tif (march) {\n\t\t\tmarch.init(data, this.marchRoleNode_, this.key)\n\t\t\tthis.checkMarchLineOffset(data)\n\t\t} else {\n\t\t\tthis.marchLineNode_.AddItem(it => {\n\t\t\t\tmarch = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))\n\t\t\t\tthis.checkMarchLineOffset(data)\n\t\t\t})\n\t\t}\n\t}\n\n\t// 删除行军\n\tprivate onRemoveMarch(data: BaseMarchObj) {\n\t\tconst march = this.marchs.remove('uid', data.uid)\n\t\tif (march) {\n\t\t\tmarch.clean()\n\t\t\tthis.checkMarchLineOffset(data)\n\t\t}\n\t}\n\n\t// 刷新所有行军\n\tprivate onUpdateAllMarch() {\n\t\tthis.initMarch()\n\t}\n\n\t// 关闭选择地块\n\tprivate onCloseSelectCell(play: boolean) {\n\t\tthis.hideSelectCell(!!play)\n\t}\n\n\t// 刷新战斗状态\n\tprivate onUpdateBattleDistInfo() {\n\t\tconst cells: MapCellObj[] = [], distMap = this.model.getBattleDistMap()\n\t\tfor (let index in distMap) {\n\t\t\tconst cell = this.tempShowCellMap[index]\n\t\t\tcell && cells.push(cell)\n\t\t}\n\t\tthis.battleNode.Items(cells, (it, data) => it.setPosition(data.actPosition))\n\t}\n\n\t// 刷新免战状态\n\tprivate onUpdateAvoidWarDistInfo() {\n\t\tthis.updateIconNode()\n\t}\n\n\t// 刷新修建信息\n\tprivate onUpdateBtCity(index: number) {\n\t\tthis.updateMap(this.centre)\n\t\tif (this.cellInfoCmpt.getCell()?.actIndex === index) {\n\t\t\tthis.cellInfoCmpt.updateInfo()\n\t\t}\n\t}\n\n\t// 刷新地图上面的军队分布情况  这里主动绘制一次\n\tprivate onUpdateArmyDistInfo() {\n\t\tthis.updateIconNode()\n\t\tthis.cellInfoCmpt.updateArmyInfo()\n\t}\n\n\t// 移动地图\n\tprivate onMapMoveTo(point: cc.Vec2, showCellInfo: boolean) {\n\t\tif (this.centre.equals(point)) {\n\t\t\treturn showCellInfo && this.showSelectCell(this.model.getMapCellByPoint(point))\n\t\t} else if (!this.tempShowCellMap[mapHelper.pointToIndex(point)]) { //如果没有在当前绘制区域就移动到目标点\n\t\t\tconst start = this.centre.sub(point, this._temp_vec2_4).normalizeSelf().mulSelf(2).addSelf(point)\n\t\t\tcameraCtrl.init(mapHelper.getPixelByPoint(start), mapHelper.MAP_SIZE, MAP_SHOW_OFFSET, cameraCtrl.zoomRatio)\n\t\t\tthis.updateMap(start.floor())\n\t\t\tthis.checkInCameraMarchLine()\n\t\t}\n\t\t// 移动\n\t\tcameraCtrl.moveTo(0.25, mapHelper.getPixelByPoint(point).subSelf(cameraCtrl.getWinSizeHalf())).then(() => {\n\t\t\tif (this.isActive && showCellInfo) {\n\t\t\t\tthis.showSelectCell(this.model.getMapCellByPoint(point))\n\t\t\t}\n\t\t})\n\t}\n\n\t// 刷新玩家昵称\n\tprivate onUpdatePlayerNickname(data: PlayerInfo) {\n\t\tconst it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)\n\t\tif (it) {\n\t\t\tthis.updatePlayerNickname(it, data)\n\t\t}\n\t}\n\n\t// 刷新玩家头像\n\tprivate onUpdatePlayerHeadIcon(data: any) {\n\t\tconst it = this.textNode_.children.find(m => m.active && m.Data?.owner === data.uid)\n\t\tif (it) {\n\t\t\tresHelper.loadPlayerHead(it.Child('head', cc.Sprite), data.headIcon, this.INIT_KEY)\n\t\t}\n\t}\n\n\t// 刷新行军线透明度\n\tprivate onUpdateMarchOpacity() {\n\t\tthis.marchs.forEach(m => m.updateOpacity())\n\t}\n\n\t// 播放新的地块效果\n\tprivate onPlayNewCellEffect() {\n\t\tthis.playNewCellEffect()\n\t}\n\n\t// 若引导\n\tprivate onWeakGuideShowNodeChoose(data: any) {\n\t\tif (data.scene === 'novice') {\n\t\t\tguideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key)\n\t\t}\n\t}\n\n\t// 播放修复主城\n\tprivate onGuidePlayRestoreMaincity() {\n\t\tconst index = this.player.getMainCityIndex()\n\t\tconst cell = this.tempShowCellMap[index] || gameHpr.world.getMapCellByIndex(index)\n\t\tanimHelper.playRestoreMaincity(this.cellEffectNode_, cell.actPosition, this.key, () => {\n\t\t\tif (this.isActive) {\n\t\t\t\tthis.cityNode.children.find(m => m.active = false)\n\t\t\t}\n\t\t}).then(() => {\n\t\t\tif (this.isActive) {\n\t\t\t\tthis.updateMap(this.centre, true)\n\t\t\t}\n\t\t})\n\t}\n\n\tprivate onGuideTagCleanNoviceWind() {\n\t\tthis.setParam({ isClean: true })\n\t}\n\t// ----------------------------------------- custom function ----------------------------------------------------\n\n\tprivate get isActive() { return this.isValid && this.isEnter() }\n\n\t// 点击地图\n\tprivate onClickMap(worldLocation: cc.Vec2) {\n\t\tconst cell = this.model.getMapCellByPoint(mapHelper.getPointByPixel(worldLocation))\n\t\tif (cell?.city?.id === CITY_MAIN_NID && gameHpr.guide.isCurrTag(GuideTagType.FIRST_GUIDE_BEGIN)) {\n\t\t\taudioMgr.playSFX('click')\n\t\t\tthis.emit(EventType.GUIDE_CKICK_BUILD_MAIN_CITY)\n\t\t} else if (cell && !this.selectCellNode_.Data) {\n\t\t\taudioMgr.playSFX('click')\n\t\t\tthis.showSelectCell(cell)\n\t\t\tcameraCtrl.redressPositionByRange(cell.actPosition, SELECT_CELL_INFO_BOX)\n\t\t} else {\n\t\t\tthis.hideSelectCell()\n\t\t}\n\t}\n\n\t// 绘制地图\n\tprivate updateMap(centre: cc.Vec2, isOneShowHead?: boolean) {\n\t\tthis.preCameraZoomRatio = cameraCtrl.zoomRatio\n\t\tthis.centre.set(centre)\n\t\tthis.model.setCentre(centre)\n\t\t// 绘制地面\n\t\tconst isRestoreMainCity = gameHpr.guide.isRestoreMainCity()\n\t\tconst armyDistMap = isRestoreMainCity ? this.player.getArmyDistMap() : {}, battleDist = this.model.getBattleDistMap(), avoidWarDist = this.model.getAvoidWarDistMap()\n\t\tconst btCityMap = this.model.getBTCityQueueMap()\n\t\tlet li = 0, mi = 0, ii = 0, bi = 0, linei = 0, di = 0, mti = 0, dti = 0, texts: MapCellObj[] = [], btCitys: any[] = []\n\t\tthis.seawaveAnimNodePool.reset()\n\t\tthis.cityAnimNodePool.reset()\n\t\tthis.tempShowCellMap = {}\n\t\tconst points = mapHelper.getRangePointsByPoint(centre, this.model.getMaxTileRange())\n\t\tconst areaSize = mapHelper.MAP_SIZE\n\t\tconst cityTmp = this._temp_city\n\t\tthis._temp_city = cc.js.createMap()\n\t\tlet tempDecorationLoadMap = {}\n\t\tfor (let i = 0; i < points.length; i++) {\n\t\t\tconst point = points[i]\n\t\t\tconst cell = this.model.getMapCellByPoint(point)\n\t\t\tconst position = cell?.position || mapHelper.getPixelByPoint(point)\n\t\t\tif (cell) {\n\t\t\t\tconst btInfo = btCityMap[cell.index]\n\t\t\t\tthis.tempShowCellMap[cell.index] = cell\n\t\t\t\tif (cell.cityId > 0) {\n\t\t\t\t\tlet cityId = cell.cityId\n\t\t\t\t\tif (cell.cityId === CITY_MAIN_NID && !isRestoreMainCity) {\n\t\t\t\t\t\tcityId = CITY_MAIN_NID * 10\n\t\t\t\t\t}\n\t\t\t\t\tconst animName = cell.cityId === CITY_FORT_NID ? 'city_2102_' + cell.getOwnType() : undefined\n\t\t\t\t\tthis.cityAnimNodePool.showNode(cityId, cell.actPosition, true, animName)\n\t\t\t\t\tif (cell.cityId === CITY_MAIN_NID) {\n\t\t\t\t\t\ttexts.push(cell) //这里先获取后面用来显示文本\n\t\t\t\t\t}\n\t\t\t\t\tthis._temp_city[cell.index] = cell.cityId\n\t\t\t\t} else if (cell.cityId === 0 && cell.icon && (!btInfo || btInfo.id === 0)) {\n\t\t\t\t\tif (cell.landType === LandType.SEA || cell.landType === LandType.BEACH || mapUionFindHelper.getIsUnLock(cell.index)) {\n\t\t\t\t\t\tlet itemNdoe = resHelper.getNodeByIndex(this.mountainNode, mti++, cell.position)\n\t\t\t\t\t\titemNdoe.Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType())\n\t\t\t\t\t\titemNdoe.zIndex = 0\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tresHelper.getNodeByIndex(this.landNode, li++, cell.position).Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(cell.icon, this.model.getSeasonType())\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 要塞摧毁动画\n\t\t\t\tif (cell.cityId === 0 && cityTmp[cell.index] === CITY_FORT_NID) {\n\t\t\t\t\tanimHelper.playFortDestroyEffect(this.cellEffectNode_, cell.position)\n\t\t\t\t}\n\t\t\t\tif (btInfo) { //绘制修建信息\n\t\t\t\t\tbtCitys.push({ btInfo, cell })\n\t\t\t\t\t// 只绘制修建 不绘制拆除\n\t\t\t\t\tif (cell.cityId === 0 && btInfo.id > 0) {\n\t\t\t\t\t\tthis.cityAnimNodePool.showNode(btInfo.id, cell.actPosition, false)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!!armyDistMap[cell.index]) { //绘制地图军队分布图标\n\t\t\t\t\t// 下面是否主城\n\t\t\t\t\tconst y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22\n\t\t\t\t\tconst pos = this._temp_vec2_3.set2(-22, y).addSelf(cell.position) //显示到左下角\n\t\t\t\t\tresHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('army_min_icon')\n\t\t\t\t}\n\t\t\t\tif (!!avoidWarDist[cell.index]) { //绘制免战图标\n\t\t\t\t\t// 下面是否主城\n\t\t\t\t\tconst y = (!cell.isMainCity() && this.model.getMapCellByPoint(this._temp_vec2_2.set2(cell.point.x, cell.point.y - 1))?.isMainCity()) ? -6 : -22\n\t\t\t\t\tconst pos = this._temp_vec2_3.set2(22, y).addSelf(cell.getRightPosition(this._temp_vec2_5)) //显示到右下角\n\t\t\t\t\tresHelper.getNodeByIndex(this.iconNode, ii++, pos).Component(cc.Sprite).spriteFrame = resHelper.getLandIcon('avoidwar_min_icon')\n\t\t\t\t}\n\t\t\t\tif (!!battleDist[cell.index]) { //绘制战斗图标\n\t\t\t\t\tresHelper.getNodeByIndex(this.battleNode, bi++, cell.actPosition)\n\t\t\t\t}\n\t\t\t\t// 绘制边框线\n\t\t\t\tif (isRestoreMainCity) {\n\t\t\t\t\tlet borderLines = cell.owner ? cell.borderLines : []\n\t\t\t\t\tif (borderLines.length > 0) {\n\t\t\t\t\t\tlet lineItemNode = resHelper.getNodeByIndex(this.lineNode, linei++, position)\n\t\t\t\t\t\tviewHelper.updateCellBorderLines(lineItemNode, borderLines, cell.getBorderLineColor())\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 绘制遮罩\n\t\t\t\tif (!cell.owner && cell.landType !== LandType.SEA) {\n\t\t\t\t\tresHelper.getNodeByIndex(this.maskNode, mi++, position)\n\t\t\t\t}\n\t\t\t\t//绘制地图装饰\n\t\t\t\tlet decorationList = this.model.getDecorationByIndex(cell.index)\n\t\t\t\tif (decorationList) {\n\t\t\t\t\tfor (let i = 0; i < decorationList.length; i++) {\n\t\t\t\t\t\tconst { decorationJson, decorationActIndex } = decorationList[i]\n\t\t\t\t\t\tlet tempKey = decorationActIndex + '_' + decorationJson.id\n\t\t\t\t\t\tif (!tempDecorationLoadMap[tempKey]) {\n\t\t\t\t\t\t\ttempDecorationLoadMap[tempKey] = true\n\t\t\t\t\t\t\tlet iconName = this.model.getDecorationIcon(decorationJson, decorationActIndex)\n\t\t\t\t\t\t\tlet frame = resHelper.getLandItemIcon(iconName, this.model.getSeasonType())\n\t\t\t\t\t\t\tlet actPosition = this.model.getMapCells()[decorationActIndex].position\n\t\t\t\t\t\t\tlet itemNode: cc.Node = null\n\t\t\t\t\t\t\tif (decorationJson.type == DecorationType.Shadow || decorationJson.type == DecorationType.MUD || decorationJson.type == DecorationType.MUD_OUTER) {\n\t\t\t\t\t\t\t\titemNode = resHelper.getNodeByIndex(this.diNode, di++, actPosition)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse if (mapUionFindHelper.getIsUnLock(decorationActIndex)) {\n\t\t\t\t\t\t\t\titemNode = resHelper.getNodeByIndex(this.mountainNode, mti++, actPosition)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\titemNode = resHelper.getNodeByIndex(this.decorationNode, dti++, actPosition)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet zIndex = 1\n\t\t\t\t\t\t\tswitch (decorationJson.type) {\n\t\t\t\t\t\t\t\tcase DecorationType.MOUNTAIN:\n\t\t\t\t\t\t\t\tcase DecorationType.MAIN_CITY_ROUND_MOUNTAIN:\n\t\t\t\t\t\t\t\tcase DecorationType.MAIN_CITY_ROUND_LAKE:\n\t\t\t\t\t\t\t\t\tzIndex = cc.macro.MAX_ZINDEX\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\tcase DecorationType.Shadow:\n\t\t\t\t\t\t\t\t\tzIndex = 0\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\titemNode.zIndex = zIndex\n\t\t\t\t\t\t\titemNode.Component(cc.Sprite).spriteFrame = frame\n\t\t\t\t\t\t\tthis.resetDecorationPoint(itemNode, iconName)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet landId = this.model.getRoundId(point.x, point.y)\n\t\t\t\tif (landId) {\n\t\t\t\t\tlet itemInfo = assetsMgr.getJsonData('land', landId)\n\t\t\t\t\tlet itemNode = resHelper.getNodeByIndex(this.mountainNode, mti++, position)\n\t\t\t\t\titemNode.Component(cc.Sprite).spriteFrame = resHelper.getLandItemIcon(itemInfo.icon, this.model.getSeasonType())\n\t\t\t\t\titemNode.zIndex = 0\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// 隐藏多余的\n\t\tresHelper.hideNodeByIndex(this.decorationNode, dti)\n\t\tresHelper.hideNodeByIndex(this.diNode, di)\n\t\tresHelper.hideNodeByIndex(this.landNode, li)\n\t\tresHelper.hideNodeByIndex(this.lineNode, linei)\n\t\tresHelper.hideNodeByIndex(this.maskNode, mi)\n\t\tresHelper.hideNodeByIndex(this.iconNode, ii)\n\t\tresHelper.hideNodeByIndex(this.battleNode, bi)\n\t\tresHelper.hideNodeByIndex(this.mountainNode, mti)\n\t\tthis.seawaveAnimNodePool.hideOtherNode()\n\t\tthis.cityAnimNodePool.hideOtherNode()\n\t\t// 当前正在显示的\n\t\tlet showIndex = this.cellInfoCmpt.getCell()?.actIndex ?? -1\n\t\tif (showIndex === -1 && !isRestoreMainCity) {\n\t\t\tshowIndex = this.player.getMainCityIndex()\n\t\t}\n\t\t// 绘制文本层\n\t\tthis.textNode_.Items(texts, (it, data) => {\n\t\t\tconst pos = data.actPosition\n\t\t\tit.setPosition(pos.x, pos.y + 76)\n\t\t\tif (it.Data?.owner !== data.owner) {\n\t\t\t\tconst info = this.model.getPlayerInfo(data.owner)\n\t\t\t\tresHelper.loadPlayerHead(it.Child('head', cc.Sprite), info?.headIcon, this.INIT_KEY)\n\t\t\t\tthis.updatePlayerNickname(it, info)\n\t\t\t}\n\t\t\tif (isOneShowHead) {\n\t\t\t\tit.opacity = 0\n\t\t\t\tcc.tween(it).to(0.3, { opacity: 255 }).start()\n\t\t\t} else {\n\t\t\t\tit.opacity = 255\n\t\t\t}\n\t\t\tit.active = showIndex !== data.actIndex\n\t\t\tit.Data = { index: data.actIndex, owner: data.owner }\n\t\t})\n\t\t// 绘制修建城市的信息\n\t\tthis.btinfoNode.Items(btCitys, (it, data) => {\n\t\t\tconst info: BTCityObj = data.btInfo\n\t\t\tit.setPosition(data.cell.actPosition)\n\t\t\tconst timeNode = it.Child('time')\n\t\t\tif (it.Data?.index !== info.index) {\n\t\t\t\tconst surplusTime = info.getSurplusTime()\n\t\t\t\ttimeNode.Color(info.id ? '#21DC2D' : '#FF9162')\n\t\t\t\ttimeNode.Component(cc.LabelTimer).run(surplusTime * 0.001)\n\t\t\t\t// 动画\n\t\t\t\tconst anim = it.Child('anim', cc.Animation)\n\t\t\t\tconst elapsedTime = Math.max(0, info.needTime - surplusTime) * 0.001\n\t\t\t\tconst tween = cc.tween(it)\n\t\t\t\ttween.stop()\n\t\t\t\tif (elapsedTime < 0.62) {\n\t\t\t\t\tanim.play('cting_begin', elapsedTime)\n\t\t\t\t\ttween.delay(0.62 - elapsedTime).call(() => this.isValid && anim.play('cting_loop')).start()\n\t\t\t\t} else {\n\t\t\t\t\tanim.play('cting_loop')\n\t\t\t\t}\n\t\t\t}\n\t\t\ttimeNode.active = showIndex !== info.index\n\t\t\tit.Data = { index: info.index }\n\t\t})\n\t}\n\n\t//设置装饰偏移\n\tpublic resetDecorationPoint(itemNode: cc.Node, icon: string) {\n\t\tlet frame = resHelper.getLandItemIcon(icon, this.model.getSeasonType())\n\t\tlet size = frame.getOriginalSize()\n\t\tif (size.width / TILE_SIZE % 2 == 0) {\n\t\t\titemNode.x += TILE_SIZE / 2\n\t\t}\n\t\tif (size.height / TILE_SIZE % 2 == 0) {\n\t\t\titemNode.y += TILE_SIZE / 2\n\t\t}\n\t}\n\n\tprivate getSeaLandIcon(point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n\t\tif (point.x < minx) {\n\t\t\treturn point.y < miny ? 7 : (point.y < maxy ? 3 : 4)\n\t\t} else if (point.x < maxx) {\n\t\t\treturn point.y < miny ? 2 : 0\n\t\t}\n\t\treturn point.y < miny ? 6 : (point.y < maxy ? 1 : 5)\n\t}\n\n\tprivate setSeaLand(it: cc.Node, type: string, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n\t\tconst dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy)\n\t\tit.Component(cc.Sprite).spriteFrame = this.getLandIcon(`${type}_${Math.min(dir, 4)}`)\n\t}\n\n\t// 海浪\n\tprivate setSeawaveLand(position: cc.Vec2, point: cc.Vec2, minx: number, miny: number, maxx: number, maxy: number) {\n\t\tconst dir = this.getSeaLandIcon(point, minx, miny, maxx, maxy), no = Math.floor(dir / 4) + 1, angle = dir % 4\n\t\tconst it = this.seawaveAnimNodePool.showNode(no, position, true)\n\t\tit.angle = angle * -90\n\t}\n\n\tprivate getLandIcon(icon: string) {\n\t\treturn resHelper.getLandIcon(icon)\n\t}\n\n\tprivate updatePlayerNickname(it: cc.Node, data: PlayerInfo) {\n\t\tconst nameLbl = it.Child('name/val', cc.Label)\n\t\tnameLbl.string = ut.nameFormator(data?.nickname ?? '???', 7)\n\t\tnameLbl._forceUpdateRenderData()\n\t\tlet width = Math.ceil(nameLbl.node.width * nameLbl.node.scaleX)\n\t\tconst titleLbl = it.Child('name/title', cc.Label)\n\t\tif (titleLbl.setActive(!!data?.title)) {\n\t\t\tconst json = assetsMgr.getJsonData('title', data.title)\n\t\t\ttitleLbl.Color(ACHIEVEMENT_COLOR[json?.quality || 1]).setLocaleKey('titleText.' + json?.id)\n\t\t\ttitleLbl._forceUpdateRenderData()\n\t\t\twidth = Math.max(titleLbl.node.width, width)\n\t\t\tnameLbl.node.y = -10\n\t\t} else {\n\t\t\tnameLbl.node.y = 0\n\t\t}\n\t\tit.Child('name').width = Math.max(100, width)\n\t}\n\n\t// 刷新显示文本节点\n\tprivate updateHideTextByIndex(index: number = -1) {\n\t\tthis.textNode_.children.forEach(m => m.active = !!m.Data && m.Data.index !== index)\n\t\tthis.btinfoNode.children.forEach(m => m.Data && (m.Child('time').active = m.Data.index !== index))\n\t}\n\n\t// 刷新图标层\n\tprivate updateIconNode() {\n\t\tconst offset1: cc.Vec2 = cc.v2(-22, -22)\n\t\tconst offset2: cc.Vec2 = cc.v2(22, -22)\n\t\tconst cells: any[] = [], armyDistMap = this.player.getArmyDistMap(), avoidwarDistMap = this.model.getAvoidWarDistMap()\n\t\tfor (let index in armyDistMap) {\n\t\t\tconst cell: MapCellObj = this.tempShowCellMap[index]\n\t\t\tcell && cells.push({ position: cell.position, offset: offset1, icon: 'army_min_icon' })\n\t\t}\n\t\tfor (let index in avoidwarDistMap) {\n\t\t\tconst cell: MapCellObj = this.tempShowCellMap[index]\n\t\t\tcell && cells.push({ position: cell.getRightPosition(), offset: offset2, icon: 'avoidwar_min_icon' })\n\t\t}\n\t\tthis.iconNode.Items(cells, (it, data) => {\n\t\t\tit.setPosition(this._temp_vec2_3.set(data.offset).addSelf(data.position))\n\t\t\tit.Component(cc.Sprite).spriteFrame = resHelper.getLandIcon(data.icon)\n\t\t})\n\t}\n\n\t// 显示选择地块\n\tprivate showSelectCell(cell: MapCellObj) {\n\t\tif (!cell || cell.landType == LandType.SEA || cell.landType == LandType.BEACH) {\n\t\t\treturn\n\t\t} else if (cell.actIndex !== cell.index) {\n\t\t\tcell = this.model.getMapCellByIndex(cell.actIndex)\n\t\t}\n\t\tconst pos = this.selectCellNode_.Data = cell.actPosition\n\t\tthis.selectCellNode_.Component(SelectCellCmpt).open(pos, cell.getSize())\n\t\tthis.cellInfoCmpt.open(pos, cell)\n\t\t// 隐藏文本节点\n\t\tthis.updateHideTextByIndex(cell.actIndex)\n\t\t// console.log(gameHpr.noviceServer.getArea(cell.index), cell.getArea())\n\t}\n\n\t// 隐藏\n\tprivate hideSelectCell(play: boolean = true) {\n\t\tif (this.selectCellNode_.Data) {\n\t\t\tthis.selectCellNode_.Component(SelectCellCmpt).close()\n\t\t\tthis.cellInfoCmpt.close(play)\n\t\t\tthis.updateHideTextByIndex()\n\t\t}\n\t}\n\n\t// 初始化行军\n\tprivate initMarch() {\n\t\tthis.cleanMarch()\n\t\tconst list = this.model.getAllMarchs().filter(m => m.isCanShowMarch())\n\t\tthis.marchLineNode_.Items(list, (it, data) => {\n\t\t\tconst march = this.marchs.add(it.Component(MarchCmpt).init(data, this.marchRoleNode_, this.key))\n\t\t\tmarch.isCheckLineOffset = false\n\t\t})\n\t\tthis.marchs.forEach(m => !m.isCheckLineOffset && this.checkMarchLineOffset(m.getData()))\n\t}\n\n\tprivate cleanMarch() {\n\t\twhile (this.marchs.length > 0) {\n\t\t\tthis.marchs.pop().clean()\n\t\t}\n\t\tthis.marchs = []\n\t\t// resHelper.cleanNodeChildren(this.marchLineNode_) //这个注释了 不知道什么原因会出现行军线被消耗的情况\n\t\tthis.marchRoleNode_.removeAllChildren()\n\t}\n\n\t// 检测行军线偏移\n\tprivate checkMarchLineOffset(data: BaseMarchObj) {\n\t\tconst others: MarchCmpt[] = []\n\t\tthis.marchs.forEach(m => {\n\t\t\tconst d = m.getData()\n\t\t\tif (data.checkOtherMarchLine(d)) {\n\t\t\t\tm.angleOffset = data.startIndex === d.startIndex ? 0 : -180\n\t\t\t\tm.isCheckLineOffset = true\n\t\t\t\tothers.push(m)\n\t\t\t}\n\t\t})\n\t\tconst len = others.length\n\t\tothers.forEach((m, i) => m.updateLineOffset(i, len))\n\t}\n\n\t// 攻击地块\n\tprivate async occupyCell(index: number) {\n\t\tif (this.reqSelectArmysing) {\n\t\t\treturn\n\t\t}\n\t\tthis.reqSelectArmysing = true\n\t\tconst { err, list, canGotoCount } = await this.player.getSelectArmys(index, 2, 0)\n\t\tthis.reqSelectArmysing = false\n\t\tif (!this.isActive) {\n\t\t\treturn\n\t\t} else if (err === ecode.NOT_IN_OCCUPY_TIME) {\n\t\t\tthis.hideSelectCell(false)\n\t\t\treturn viewHelper.showMessageBox('ui.not_in_occupy_time_tip')\n\t\t} else if (err === ecode.NOT_IN_OCCUPY_ANCIENT_TIME) { //提示只能在固定时间攻击\n\t\t\tthis.hideSelectCell(false)\n\t\t\treturn viewHelper.showMessageBox('ui.not_in_occupy_ancient_time_tip')\n\t\t} else if (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (list.length === 0) {\n\t\t\treturn viewHelper.showAlert('toast.not_idle_army')\n\t\t}\n\t\t// else if (gameHpr.getSelfToMapCellDis(index) >= 6) {\n\t\t// \tlet cell = gameHpr.world.getMapCellByIndex(index)\n\t\t// \tif (!cell.owner) {\n\t\t// \t\treturn viewHelper.showAlert('toast.novice_not_attack_cell')\n\t\t// \t}\n\t\t// }\n\t\tviewHelper.showPnl('main/SelectArmy', 'occupy', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean, autoBackType: number) => {\n\t\t\tif (this.isActive) {\n\t\t\t\tthis.hideSelectCell(false)\n\t\t\t\tconst isGuideBattle = gameHpr.guide.isCurrTag(GuideTagType.FIRST_BATTLE_MOVE)\n\t\t\t\tthis.model.occupyCell(armys, index, autoBackType, isSameSpeed, isGuideBattle).then(err => err && viewHelper.showAlert(err))\n\t\t\t}\n\t\t})\n\t}\n\n\t// 移动到地块\n\tprivate async moveToCell(index: number) {\n\t\tif (this.reqSelectArmysing) {\n\t\t\treturn\n\t\t}\n\t\tthis.reqSelectArmysing = true\n\t\tconst { err, list, canGotoCount } = await this.player.getSelectArmys(index, 1)\n\t\tthis.reqSelectArmysing = false\n\t\tif (!this.isActive) {\n\t\t\treturn\n\t\t} else if (err) {\n\t\t\treturn viewHelper.showAlert(err)\n\t\t} else if (list.length === 0) {\n\t\t\treturn viewHelper.showAlert('toast.not_idle_army')\n\t\t}\n\t\tviewHelper.showPnl('main/SelectArmy', 'move', index, list, canGotoCount, (armys: ArmyShortInfo[], isSameSpeed: boolean) => {\n\t\t\tif (this.isActive) {\n\t\t\t\tthis.hideSelectCell(false)\n\t\t\t\tthis.model.moveCellArmy(armys, index, false, isSameSpeed).then(err => err && viewHelper.showAlert(err))\n\t\t\t}\n\t\t})\n\t}\n\n\t// 播放新的地块效果\n\tprivate playNewCellEffect() {\n\t\tthis.model.getNotPlayNewCells().forEach(index => {\n\t\t\tconst cell = this.tempShowCellMap[index]\n\t\t\tif (cell) {\n\t\t\t\tconst json = cell.getResJson() || {}, keys = CELL_RES_FIELDS.filter(m => !!json[m])\n\t\t\t\tconst pos = cell.actPosition, isMore = keys.length > 1\n\t\t\t\tkeys.forEach((key, i) => animHelper.playFlutterCellRes(key, json[key], 0.3 + i * 0.2, isMore, this.topLayerNode_, pos, this.key))\n\t\t\t\tanimHelper.playNewCellEffect(this.cellEffectNode_, pos, this.key)\n\t\t\t\t// 隐藏行军线\n\t\t\t\tthis.marchs.forEach(march => march.isHasIndex(index) && march.hide(1.2))\n\t\t\t}\n\t\t})\n\t}\n\n\tprivate testPlayNewCell(x: number, y: number) {\n\t\tfor (let i = 0; i < 5; i++) {\n\t\t\tconst position = mapHelper.getPixelByPoint(cc.v2(x + i, y)).clone()\n\t\t\tanimHelper.playFlutterCellRes('stone', 30, 0.3, false, this.topLayerNode_, position, this.key)\n\t\t\tanimHelper.playNewCellEffect(this.cellEffectNode_, position, this.key)\n\t\t}\n\t}\n\n\tupdate(dt: number) {\n\t\t//\n\t\tthis.seawaveAnimNodePool?.update(dt)\n\t\t// 检测是否需要填充地图\n\t\tthis.checkUpdateMap()\n\t\t// 检测是否在相机范围\n\t\tthis.checkInCameraRange()\n\t}\n\n\tprivate checkUpdateMap() {\n\t\tconst point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_1)\n\t\tlet size = Math.max(Math.abs(point.x - this.centre.x), Math.abs(point.y - this.centre.y))\n\t\tif (size >= MAP_EXTRA_SIZE / 2 || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {\n\t\t\tthis.updateMap(point)\n\t\t\tthis.checkInCameraMarchLine()\n\t\t}\n\t}\n\n\t// 检测只会在在相机范围内的行军线\n\tprivate checkInCameraMarchLine() {\n\t\tthis.marchs.forEach(m => m.checkUpdateInCamera())\n\t}\n\n\tprivate checkInCameraRange() {\n\t\tconst position = cameraCtrl.getPosition()\n\t\tif (this.preCameraPosition.equals(position)) {\n\t\t\treturn\n\t\t}\n\t\tthis.preCameraPosition.set(position)\n\t\t// 选择地块框\n\t\tif (this.cellInfoCmpt?.checkNotInScreenRange()) {\n\t\t\tthis.hideSelectCell(false)\n\t\t}\n\t}\n}\n"]}