
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/BgMoveCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '616a0vupppFhLwvkdC9MIt5', 'BgMoveCmpt');
// app/script/view/login/BgMoveCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var BgMoveCmpt = /** @class */ (function (_super) {
    __extends(BgMoveCmpt, _super);
    function BgMoveCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.speed = 0;
        _this.items = [];
        return _this;
    }
    BgMoveCmpt.prototype.onLoad = function () {
        this.items = this.node.children.map(function (m) { return m; });
        var _a = __read(this.items, 2), it0 = _a[0], it1 = _a[1];
        var mul = Math.ceil(cc.winSize.width / 4 / it0.width);
        it0.width *= mul;
        it1.width *= mul;
        it0.x = 0;
        it1.x = it0.width;
    };
    BgMoveCmpt.prototype.update = function (dt) {
        var _a = __read(this.items, 2), it0 = _a[0], it1 = _a[1];
        var speed = dt * this.speed;
        it0.x -= speed;
        it1.x -= speed;
        if (it0.x <= -it0.width) {
            it0.x = it1.x + it1.width;
        }
        else if (it1.x <= -it1.width) {
            it1.x = it0.x + it0.width;
        }
    };
    __decorate([
        property
    ], BgMoveCmpt.prototype, "speed", void 0);
    BgMoveCmpt = __decorate([
        ccclass
    ], BgMoveCmpt);
    return BgMoveCmpt;
}(cc.Component));
exports.default = BgMoveCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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