
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildBazaarRecordPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e9034L5DGhNfbVILftocqvR', 'BuildBazaarRecordPnlCtrl');
// app/script/view/build/BuildBazaarRecordPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BazaarConfig_1 = require("../../model/bazaar/BazaarConfig");
var ccclass = cc._decorator.ccclass;
var BuildBazaarRecordPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildBazaarRecordPnlCtrl, _super);
    function BuildBazaarRecordPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.recordNode_ = null; // path://root/record_n
        _this.loadingNode_ = null; // path://root/loading_n
        return _this;
    }
    //@end
    BuildBazaarRecordPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BuildBazaarRecordPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildBazaarRecordPnlCtrl.prototype.onEnter = function (data) {
        this.updateRecord();
    };
    BuildBazaarRecordPnlCtrl.prototype.onRemove = function () {
    };
    BuildBazaarRecordPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildBazaarRecordPnlCtrl.prototype.updateRecord = function () {
        var _this = this;
        var sv = this.recordNode_.Component(cc.ScrollView);
        sv.content.Swih('');
        this.loadingNode_.active = true;
        GameHelper_1.gameHpr.player.getBazaarRecords().then(function (list) {
            if (!_this.isValid || !_this.isEnter()) {
                return;
            }
            _this.loadingNode_.active = false;
            var len = list.length;
            sv.Child('empty').active = len === 0;
            sv.stopAutoScroll();
            sv.content.y = 0;
            sv.Items(list, function (it, data) {
                var _a, _b;
                it.Data = data;
                it.Child('time', cc.Label).string = ut.dateFormat('MM-dd hh:mm:ss', data.time);
                var textNode = it.Child('text'), endNode = it.Child('time/end');
                var conf = BazaarConfig_1.BAZAAR_RECORD_INFO_CONF[data.type], textConfs = conf === null || conf === void 0 ? void 0 : conf.text, endConf = conf === null || conf === void 0 ? void 0 : conf.end;
                if (textNode.active = !!textConfs) {
                    textNode.setLocaleKey('ui.bazaar_record_info_' + data.type, textConfs.map(function (m) {
                        var _a;
                        if (m === 'name') {
                            return '<color=#3F332F><fontFamily=Arial>' + ut.nameFormator((_a = data.nickname) !== null && _a !== void 0 ? _a : '', 6) + '</></c>';
                        }
                        else if (m === 'res0' || m === 'res1') {
                            var res = data[m];
                            return res ? "<img src='" + Constant_1.CTYPE_ICON[res.type] + "' /><color=#3F332F>" + res.count + "</c>" : '';
                        }
                        return '';
                    }));
                }
                if (endNode.active = !!endConf && (!endConf.checkCount || ((_a = data.res0) === null || _a === void 0 ? void 0 : _a.count) !== ((_b = data.actRes) === null || _b === void 0 ? void 0 : _b.count))) {
                    endNode.Child('val').setLocaleKey(endConf.key);
                    var resNode = endNode.Child('res');
                    if (resNode.active = !!(endConf.res && (data === null || data === void 0 ? void 0 : data.actRes))) {
                        ViewHelper_1.viewHelper.updateCostViewOne(resNode, data.actRes);
                    }
                }
            });
        });
    };
    BuildBazaarRecordPnlCtrl = __decorate([
        ccclass
    ], BuildBazaarRecordPnlCtrl);
    return BuildBazaarRecordPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildBazaarRecordPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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