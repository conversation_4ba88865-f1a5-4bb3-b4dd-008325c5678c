
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStar8.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6fab2z67bpBXpeRvVcgbYi/', 'AStar8');
// app/script/common/astar/AStar8.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var AStarConfig_1 = require("./AStarConfig");
var ANode_1 = require("./ANode");
/**
 * A星寻路 8方向
 */
var AStar8 = /** @class */ (function () {
    function AStar8() {
        this.DIR_POINTS = [];
        this.DIR_COUNT = 0;
        this.opened = [];
        this.closed = {};
        this.checkHasPass = null; //检测方法
        this._temp_vec2 = cc.v2();
        this.DIR_POINTS = AStarConfig_1.DIR_POINTS_8;
        this.DIR_COUNT = this.DIR_POINTS.length;
    }
    AStar8.prototype.init = function (checkHasPass) {
        this.checkHasPass = checkHasPass;
        return this;
    };
    // 新建一个节点
    AStar8.prototype.newNode = function (x, y) {
        return new ANode_1.default().init(x, y);
    };
    // 检查拐角是否有障碍 只用于8个方向行走
    AStar8.prototype.checkCornerHasTile = function (x1, y1, x2, y2) {
        return this.checkHasPass(x1, y1) && this.checkHasPass(x2, y2);
    };
    // 寻路
    AStar8.prototype.search = function (start, end) {
        return __awaiter(this, void 0, Promise, function () {
            var node, cnt, _loop_1, this_1, i;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (start.equals(end)) {
                            return [2 /*return*/, []];
                        }
                        this.opened.length = 0;
                        this.closed = {};
                        // 把第一个点装进开起列表
                        this.opened.push(this.newNode(start.x, start.y));
                        node = null, cnt = 0;
                        _a.label = 1;
                    case 1:
                        if (!(this.opened.length > 0)) return [3 /*break*/, 4];
                        node = this.opened.shift();
                        if (node.point.equals(end)) {
                            return [3 /*break*/, 4];
                        }
                        this.closed[node.uid] = true;
                        _loop_1 = function (i) {
                            var d = this_1.DIR_POINTS[i];
                            var x = node.x + d.point.x;
                            var y = node.y + d.point.y;
                            if (!end.equals2(x, y) && (!this_1.checkHasPass(x, y) || this_1.closed[x + '_' + y])) {
                                return "continue";
                            }
                            else if (d.tag !== 1 && !this_1.checkCornerHasTile(x, node.y, node.x, y)) { // 优化拐歪
                                return "continue";
                            }
                            // 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
                            var it = this_1.opened.find(function (m) { return m.point.equals2(x, y); });
                            if (!it) {
                                var temp = this_1.newNode(x, y);
                                temp.H = end.sub(temp.point, this_1._temp_vec2).mag();
                                temp.updateParent(node, d.tag);
                                this_1.opened.push(temp);
                            }
                            else if (node.G + d.tag < it.G) {
                                it.updateParent(node, d.tag);
                            }
                        };
                        this_1 = this;
                        // 找周围的是否可以移动
                        for (i = 0; i < this.DIR_COUNT; i++) {
                            _loop_1(i);
                        }
                        // 排序
                        this.opened.sort(function (a, b) { return a.F - b.F; });
                        if (!(++cnt > 100)) return [3 /*break*/, 3];
                        cnt = 0;
                        return [4 /*yield*/, ut.waitNextFrame()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3: return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, this.genPoints(node, start)];
                }
            });
        });
    };
    AStar8.prototype.genPoints = function (node, start) {
        var _this = this;
        var points = [];
        while (node.parent !== null) {
            points.push(node.point);
            node = node.parent;
        }
        points.push(start);
        points.reverse();
        this.opened.length = 0;
        this.closed = {};
        var _loop_2 = function (i) {
            var p = points[i];
            if (!this_2.DIR_POINTS.some(function (m) { return !_this.checkHasPass(p.x + m.point.x, p.y + m.point.y); })) {
                points.splice(i, 1);
            }
        };
        var this_2 = this;
        // 删除没有障碍的点
        for (var i = points.length - 2; i >= 1; i--) {
            _loop_2(i);
        }
        return points;
    };
    return AStar8;
}());
exports.default = AStar8;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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