
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/GotoHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'fac3fC5uPNMDo/PiJanfJp3', 'GotoHelper');
// app/script/common/helper/GotoHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gotoHelper = void 0;
var CommunityConfig_1 = require("../constant/CommunityConfig");
var Constant_1 = require("../constant/Constant");
var Enums_1 = require("../constant/Enums");
var CryptoJS_1 = require("../crypto/CryptoJS");
var EventType_1 = require("../event/EventType");
var GameHelper_1 = require("./GameHelper");
var MapHelper_1 = require("./MapHelper");
var ReddotHelper_1 = require("./ReddotHelper");
var ViewHelper_1 = require("./ViewHelper");
var GotoHelper = /** @class */ (function () {
    function GotoHelper() {
    }
    // 是否可以goto
    GotoHelper.prototype.isCanGotoByTaskCond = function (type) {
        return type === Enums_1.TCType.BUILD_LV
            || type === Enums_1.TCType.CELL_COUNT
            || type === Enums_1.TCType.LAND_LV_COUNT
            || type === Enums_1.TCType.LAND_TYPE_COUNT
            || type === Enums_1.TCType.TODAY_CELL_COUNT
            || type === Enums_1.TCType.RECRUIT_PAWN_COUNT
            || type === Enums_1.TCType.RECRUIT_PAWN_APPOINT
            || type === Enums_1.TCType.UPLV_PAWN_APPOINT
            || type === Enums_1.TCType.FORGE_EQUIP_APPOINT
            || type === Enums_1.TCType.STUDY_TYPE_APPOINT
            || type === Enums_1.TCType.ARMY_PAWN_COUNT
            || type === Enums_1.TCType.ARMY_COUNT
            || type === Enums_1.TCType.UPLV_PAWN
            || type === Enums_1.TCType.BT_MAP_RES_BUILD
            || type === Enums_1.TCType.BT_MAP_BUILD
            || type === Enums_1.TCType.TODAY_TURNTABLE_COUNT
            || type === Enums_1.TCType.RECHARGE_COUNT
            || type === Enums_1.TCType.RECRUIT_PAWN_TYPE
            || type === Enums_1.TCType.RECRUIT_RANGED_PAWN
            || type === Enums_1.TCType.WORSHIP_HERO
            || type === Enums_1.TCType.FORGE_EXC_EQUIP
            || type === Enums_1.TCType.FORGE_EQUIP
            || type === Enums_1.TCType.WEAR_EQUIP
            || type === Enums_1.TCType.SMELT_EQUIP
            || type === Enums_1.TCType.WEAR_EQUIP_DEF
            || type === Enums_1.TCType.WEAR_EQUIP_ALL;
    };
    // 运行goto 用任务条件
    GotoHelper.prototype.runByTaskCond = function (cond) {
        return __awaiter(this, void 0, void 0, function () {
            var tab, uid, key, code, cfg, url;
            return __generator(this, function (_a) {
                if (!cond) {
                    return [2 /*return*/, false];
                }
                else if (cond.type === Enums_1.TCType.BUILD_LV) {
                    return [2 /*return*/, this.gotoAreaBTBuild(cond.id)];
                }
                else if (cond.type === Enums_1.TCType.CELL_COUNT || cond.type === Enums_1.TCType.LAND_LV_COUNT || cond.type === Enums_1.TCType.LAND_TYPE_COUNT || cond.type === Enums_1.TCType.TODAY_CELL_COUNT) {
                    return [2 /*return*/, this.gotoMainOtherCell(cond)];
                }
                else if (cond.type === Enums_1.TCType.RECRUIT_PAWN_COUNT
                    || cond.type === Enums_1.TCType.RECRUIT_PAWN_APPOINT
                    || cond.type === Enums_1.TCType.ARMY_PAWN_COUNT
                    || cond.type === Enums_1.TCType.ARMY_COUNT
                    || cond.type === Enums_1.TCType.RECRUIT_PAWN_TYPE
                    || cond.type === Enums_1.TCType.RECRUIT_RANGED_PAWN
                    || cond.type === Enums_1.TCType.WEAR_EQUIP_DEF) { //招募士兵
                    return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_BARRACKS_NID, 1)];
                }
                else if (cond.type === Enums_1.TCType.UPLV_PAWN_APPOINT || cond.type === Enums_1.TCType.UPLV_PAWN) { //升级指定士兵到指定等级
                    return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_DRILLGROUND_NID, 1)];
                }
                else if (cond.type === Enums_1.TCType.WORSHIP_HERO) { //供奉英雄
                    return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_HEROHALL_NID, 1)];
                }
                else if (cond.type === Enums_1.TCType.FORGE_EQUIP_APPOINT
                    || cond.type === Enums_1.TCType.FORGE_EXC_EQUIP
                    || cond.type === Enums_1.TCType.FORGE_EQUIP
                    || cond.type === Enums_1.TCType.SMELT_EQUIP) { //铁匠铺
                    tab = (cond.type === Enums_1.TCType.FORGE_EXC_EQUIP || cond.type === Enums_1.TCType.SMELT_EQUIP) ? 2 : 1;
                    return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_SMITHY_NID, tab)];
                }
                else if (cond.type === Enums_1.TCType.STUDY_TYPE_APPOINT) { //研究指定类型
                    if (cond.id === Enums_1.StudyType.POLICY) {
                        return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_MAIN_NID, 1)];
                    }
                    else if (cond.id === Enums_1.StudyType.PAWN) {
                        return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_BARRACKS_NID, 1)];
                    }
                    else if (cond.id === Enums_1.StudyType.EQUIP || cond.id === Enums_1.StudyType.EXCLUSIVE) {
                        return [2 /*return*/, this.gotoAreaOpenBuild(Constant_1.BUILD_SMITHY_NID, 1)];
                    }
                }
                else if (cond.type === Enums_1.TCType.BT_MAP_RES_BUILD || cond.type === Enums_1.TCType.BT_MAP_BUILD) { //任意修建一个资源建筑
                    return [2 /*return*/, this.gotoMainMyCell(cond)];
                }
                else if (cond.type === Enums_1.TCType.TODAY_TURNTABLE_COUNT) { //前往转盘
                }
                else if (cond.type === Enums_1.TCType.RECHARGE_COUNT) { //前往充值
                    ViewHelper_1.viewHelper.showPnl('common/Shop', 0);
                    return [2 /*return*/, true];
                }
                else if (cond.id === Constant_1.PRIZE_QUESTION_ID) { // 跳转问卷地址
                    uid = GameHelper_1.gameHpr.user.getUid(), key = 'prize_question_1';
                    code = CryptoJS_1.default.MD5(uid + key).toString(), cfg = CommunityConfig_1.COMMUNITY_PRIZE_QUESTION_GLOBAL[mc.lang] || CommunityConfig_1.COMMUNITY_PRIZE_QUESTION_GLOBAL.default;
                    url = cfg.url + "entry.1013235000=" + uid;
                    url += "&entry." + cfg.code + "=" + code;
                    cc.sys.openURL(url);
                    storageMgr.saveBool(key, true);
                    ReddotHelper_1.reddotHelper.set('prize_question', false);
                    return [2 /*return*/, true];
                }
                else if (cond.type === Enums_1.TCType.WEAR_EQUIP_ALL) {
                    return [2 /*return*/, this.gotoWearEquip(0)];
                }
                else if (cond.type === Enums_1.TCType.WEAR_EQUIP) {
                    return [2 /*return*/, this.gotoWearEquip(cond.id)];
                }
                return [2 /*return*/, false];
            });
        });
    };
    // 跳转到区域修建 建筑
    GotoHelper.prototype.gotoAreaBTBuild = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var world, index, cell, area, build;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (mc.currWindName === 'lobby') {
                            ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                            return [2 /*return*/, false];
                        }
                        world = GameHelper_1.gameHpr.world;
                        index = GameHelper_1.gameHpr.player.getMainCityIndex();
                        cell = world.getLookCell();
                        if (!((cell === null || cell === void 0 ? void 0 : cell.actIndex) !== index)) return [3 /*break*/, 4];
                        cell = world.getMapCellByIndex(index);
                        if (!cell) {
                            return [2 /*return*/, false];
                        }
                        world.setLookCell(cell);
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                        return [4 /*yield*/, eventCenter.req(EventType_1.default.REENTER_AREA_WIND)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        area = GameHelper_1.gameHpr.areaCenter.getArea(cell.index);
                        if (!area) {
                            return [2 /*return*/, false];
                        }
                        else if (GameHelper_1.gameHpr.guide.isForceWorking()) {
                            return [2 /*return*/, true]; //在引导的话 就不弹了
                        }
                        build = area.getBuildById(id);
                        if (!build) {
                            ViewHelper_1.viewHelper.showPnl('area/BuildList', area, id);
                        }
                        else if (build.lv > 0) {
                            ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build, 0);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 跳转到区域打开建造
    GotoHelper.prototype.gotoAreaOpenBuild = function (id, tab) {
        return __awaiter(this, void 0, void 0, function () {
            var world, index, cell, area, build;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (mc.currWindName === 'lobby') {
                            ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                            return [2 /*return*/, false];
                        }
                        world = GameHelper_1.gameHpr.world;
                        index = GameHelper_1.gameHpr.player.getMainCityIndex();
                        cell = world.getLookCell();
                        if (!((cell === null || cell === void 0 ? void 0 : cell.actIndex) !== index)) return [3 /*break*/, 4];
                        cell = world.getMapCellByIndex(index);
                        if (!cell) {
                            return [2 /*return*/, false];
                        }
                        world.setLookCell(cell);
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 2];
                        return [4 /*yield*/, eventCenter.req(EventType_1.default.REENTER_AREA_WIND)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        area = GameHelper_1.gameHpr.areaCenter.getArea(cell.index);
                        if (!area) {
                            return [2 /*return*/, false];
                        }
                        else if (GameHelper_1.gameHpr.guide.isForceWorking()) {
                            return [2 /*return*/, true]; //在引导的话 就不弹了
                        }
                        build = area.getBuildById(id);
                        if (!build || build.lv === 0) {
                            return [2 /*return*/, false];
                        }
                        ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build, tab);
                        // gameHpr.weakGuide.addWeakGuide({scene: 'area', })
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 跳转到地块
    GotoHelper.prototype.gotoMainOtherCell = function (cond) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var world, scene, marchTargetMap, cell, d, cells;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (mc.currWindName === 'lobby') {
                            ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                            return [2 /*return*/, false];
                        }
                        world = GameHelper_1.gameHpr.world, scene = world.getSceneKey();
                        if (!(mc.currWindName !== scene)) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind(scene)];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        marchTargetMap = {};
                        GameHelper_1.gameHpr.world.getMarchs().forEach(function (m) { return marchTargetMap[m.targetIndex] = true; });
                        cell = null;
                        for (d = 0; d <= 100; d++) {
                            cells = MapHelper_1.mapHelper.getCellsOuter((_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cells, d).map(function (m) { return world.getMapCellByIndex(m); }).filter(function (m) {
                                return !marchTargetMap[m.index] && !GameHelper_1.gameHpr.isBattleingByIndex(m.index) && !m.owner;
                            });
                            cells.sort(function (a, b) {
                                var aw = GameHelper_1.gameHpr.getSelfToMapCellDis(a.index) * 100 + a.landLv;
                                var bw = GameHelper_1.gameHpr.getSelfToMapCellDis(b.index) * 100 + b.landLv;
                                if (aw === bw) {
                                    return a.index - b.index;
                                }
                                return aw - bw;
                            });
                            if (cond.type === Enums_1.TCType.LAND_LV_COUNT) {
                                cell = cells.find(function (m) { return m.landLv === cond.id; });
                            }
                            else if (cond.type == Enums_1.TCType.LAND_TYPE_COUNT) {
                                cell = cells.find(function (m) { return m.landType === cond.id && m.landLv > 1; });
                            }
                            else {
                                cell = cells[0];
                            }
                            if (cell) {
                                break;
                            }
                        }
                        if (cell && !GameHelper_1.gameHpr.guide.isForceWorking()) {
                            eventCenter.emit(EventType_1.default.MAP_MOVE_TO, cell.point, true);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 跳转到自己的地块
    GotoHelper.prototype.gotoMainMyCell = function (cond) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var world, scene, cell, cells, maxLv_1, arr_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (mc.currWindName === 'lobby') {
                            ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                            return [2 /*return*/, false];
                        }
                        world = GameHelper_1.gameHpr.world, scene = world.getSceneKey();
                        if (!(mc.currWindName !== scene)) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind(scene)];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        cell = null;
                        cells = (_a = GameHelper_1.gameHpr.getPlayerInfo(GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.cells;
                        // 找出等级最高的地
                        if (cond.type === Enums_1.TCType.BT_MAP_RES_BUILD) {
                            maxLv_1 = 0;
                            cells === null || cells === void 0 ? void 0 : cells.forEach(function (c) {
                                var lv = c.landLv;
                                if (c.cityId === 0 && lv > maxLv_1) {
                                    cell = c;
                                    maxLv_1 = lv;
                                }
                            });
                        }
                        else if (cond.type === Enums_1.TCType.BT_MAP_BUILD && cond.id === Constant_1.CITY_FORT_NID) { //要塞
                            arr_1 = [];
                            cells === null || cells === void 0 ? void 0 : cells.forEach(function (c) {
                                if (c.cityId === 0 && GameHelper_1.gameHpr.getSelfToMapCellDis(c.index) >= 9) { //大于9格就可以修建
                                    arr_1.push(c);
                                }
                            });
                            cell = arr_1.random();
                        }
                        if (cell) {
                            eventCenter.emit(EventType_1.default.MAP_MOVE_TO, cell.point, true);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    // 跳转到自己的主城
    GotoHelper.prototype.gotoMainCity = function () {
        return __awaiter(this, void 0, void 0, function () {
            var world, scene, cell;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (mc.currWindName === 'lobby') {
                            ViewHelper_1.viewHelper.showAlert('toast.please_enter_game');
                            return [2 /*return*/, false];
                        }
                        world = GameHelper_1.gameHpr.world, scene = world.getSceneKey();
                        if (!(mc.currWindName !== scene)) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind(scene)];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2:
                        cell = world.getMapCellByIndex(GameHelper_1.gameHpr.player.getMainCityIndex());
                        if (cell) {
                            eventCenter.emit(EventType_1.default.MAP_MOVE_TO, cell.point, true);
                        }
                        return [2 /*return*/, true];
                }
            });
        });
    };
    //type=0代表任意装备 type=1表示专属装备
    GotoHelper.prototype.gotoWearEquip = function (type) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var stateSwitchMap, armys, i, army, j, pawn, i, army, j, pawn, cell;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0:
                        stateSwitchMap = {};
                        stateSwitchMap[Enums_1.ArmyState.MARCH] = 9;
                        stateSwitchMap[Enums_1.ArmyState.NONE] = 0;
                        return [4 /*yield*/, GameHelper_1.gameHpr.player.getAllArmys()];
                    case 1:
                        armys = _c.sent();
                        armys.sort(function (army1, army2) {
                            var state1 = stateSwitchMap[army1.state] || 1;
                            var state2 = stateSwitchMap[army2.state] || 1;
                            return state1 - state2;
                        });
                        if (mc.currWindName === 'area') {
                            for (i = 0; i < armys.length; i++) {
                                army = armys[i];
                                if (army.index === ((_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index)) {
                                    for (j = 0; j < army.pawns.length; j++) {
                                        pawn = army.pawns[j];
                                        if (!this.isWearEquip(pawn, type)) {
                                            return [2 /*return*/, true];
                                        }
                                    }
                                }
                            }
                        }
                        i = 0;
                        _c.label = 2;
                    case 2:
                        if (!(i < armys.length)) return [3 /*break*/, 9];
                        army = armys[i];
                        j = 0;
                        _c.label = 3;
                    case 3:
                        if (!(j < army.pawns.length)) return [3 /*break*/, 8];
                        pawn = army.pawns[j];
                        if (!!this.isWearEquip(pawn, type)) return [3 /*break*/, 7];
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(army.index);
                        if (!(cell && cell.index !== ((_b = GameHelper_1.gameHpr.world.getLookCell()) === null || _b === void 0 ? void 0 : _b.index))) return [3 /*break*/, 7];
                        if (!(mc.currWindName === 'area')) return [3 /*break*/, 5];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('novice')];
                    case 4:
                        _c.sent();
                        _c.label = 5;
                    case 5:
                        GameHelper_1.gameHpr.world.setLookCell(cell);
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind('area')];
                    case 6:
                        _c.sent();
                        return [2 /*return*/, true];
                    case 7:
                        j++;
                        return [3 /*break*/, 3];
                    case 8:
                        i++;
                        return [3 /*break*/, 2];
                    case 9: return [2 /*return*/, true];
                }
            });
        });
    };
    // 是否穿戴装备 type=0代表任意装备 type=1表示专属装备
    GotoHelper.prototype.isWearEquip = function (pawn, type) {
        if (0 === type) {
            return !!pawn.equip.uid;
        }
        var equipId = pawn.equip.uid.split('_')[0];
        var equipInfo = assetsMgr.getJsonData('equipBase', equipId);
        if (equipInfo && equipInfo.exclusive_pawn == pawn.id) {
            return true;
        }
        return false;
    };
    return GotoHelper;
}());
exports.gotoHelper = new GotoHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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