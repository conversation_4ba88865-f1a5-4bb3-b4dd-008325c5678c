
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/NotFinishOrderTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '043b3t2C2tOvI3GjSYRZcnf', 'NotFinishOrderTipPnlCtrl');
// app/script/view/main/NotFinishOrderTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventReportHelper_1 = require("../../common/helper/EventReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var NotFinishOrderTipPnlCtrl = /** @class */ (function (_super) {
    __extends(NotFinishOrderTipPnlCtrl, _super);
    function NotFinishOrderTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.ICON_SIZE = cc.size(80, 80);
        _this.user = null;
        return _this;
    }
    NotFinishOrderTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NotFinishOrderTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    NotFinishOrderTipPnlCtrl.prototype.onEnter = function (data) {
        var orders = this.user.getNotFinishOrders();
        this.updateList(orders);
    };
    NotFinishOrderTipPnlCtrl.prototype.onRemove = function () {
    };
    NotFinishOrderTipPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/claim_be
    NotFinishOrderTipPnlCtrl.prototype.onClickClaim = function (event, _) {
        var data = event.target.parent.Data;
        if (data) {
            this.do(data);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    NotFinishOrderTipPnlCtrl.prototype.updateList = function (orders) {
        var _this = this;
        if (orders.length === 0) {
            return this.hide();
        }
        var countRecordMap = {};
        var obj = GameHelper_1.gameHpr.user.getRechargeCountRecord();
        for (var k in obj) {
            countRecordMap[k] = obj[k];
        }
        this.listSv_.List(orders.length, function (it, i) {
            var data = it.Data = orders[i];
            var json = assetsMgr.getJson('recharge').get('product_id', data.productId)[0];
            if (json) {
                var count = 0;
                var rechargeCount = countRecordMap[json.product_id] || 0;
                if (!rechargeCount) {
                    count = json.ingot + json.ingot;
                }
                else {
                    count = json.ingot + json.extra;
                }
                // 多出来的几次 按正常计算
                for (var idx = 0; idx < data.quantity - 1; idx++) {
                    count += (json.ingot + json.extra);
                }
                var iconNode_1 = it.Child('icon');
                ResHelper_1.resHelper.loadIcon(json.icon, iconNode_1, _this.key).then(function () { return _this.isValid && iconNode_1.adaptScale(_this.ICON_SIZE, undefined, 1.1); });
                it.Child('name').setLocaleKey('ui.shop_ingot_name', count);
                it.Child('money', cc.Label).string = PayHelper_1.payHelper.getProductPriceText(json.product_id);
                it.Child('count').setLocaleKey('ui.quantity_text', data.quantity || 1);
                countRecordMap[json.product_id] = rechargeCount + 1;
            }
            else if (data.productId === Constant_1.RECHARGE_BATTLE_PASS) { //战令
                var iconNode_2 = it.Child('icon');
                ResHelper_1.resHelper.loadIcon('icon/battle_pass_up', iconNode_2, _this.key).then(function () { return _this.isValid && iconNode_2.adaptScale(_this.ICON_SIZE, undefined, 1.1); });
                it.Child('name').setLocaleKey('ui.battle_pass_up_name');
                it.Child('money', cc.Label).string = PayHelper_1.payHelper.getBattlePassPriceText();
                it.Child('count').setLocaleKey('');
            }
        });
    };
    NotFinishOrderTipPnlCtrl.prototype.do = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showLoadingWait(true);
                        if (!(data.state === 1 && data.cpOrderId)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.getPayReward(data)]; //这种是服务器发下来的验证了 但是还未领取
                    case 1:
                        _a.sent(); //这种是服务器发下来的验证了 但是还未领取
                        return [3 /*break*/, 5];
                    case 2:
                        if (!data.orderId) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.verifyPayOrder(data)];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        console.log('NotFinishOrder DO Error. not cpOrderId and not orderId?');
                        _a.label = 5;
                    case 5:
                        ViewHelper_1.viewHelper.showLoadingWait(false);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 验证订单
    NotFinishOrderTipPnlCtrl.prototype.verifyPayOrder = function (order) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, orders;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_VerifyPayOrder', {
                            productId: order.productId,
                            orderId: order.orderId,
                            cpOrderId: order.cpOrderId,
                            token: order.token,
                            platform: order.platform,
                            price: order.payAmount,
                            purchaseTime: order.purchaseTime,
                            currencyType: order.currencyType,
                            payAmount: order.payAmount,
                            quantity: order.quantity,
                        })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) return [3 /*break*/, 4];
                        if (!(err === ECode_1.ecode.ORDER_FINISHED || err === ECode_1.ecode.ORDER_REFUNDED || err === ECode_1.ecode.ORDER_VERIFY_REPEAT)) return [3 /*break*/, 3];
                        // 先标记消费
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token })
                            // 刷新列表
                        ];
                    case 2:
                        // 先标记消费
                        _b.sent();
                        orders = GameHelper_1.gameHpr.user.removeNotFinishOrders('orderId', order.orderId);
                        if (this.isValid) {
                            this.updateList(orders);
                        }
                        _b.label = 3;
                    case 3: return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                    case 4:
                        order.cpOrderId = data.cpOrderId;
                        // 领取奖励
                        this.getPayReward(order);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 领取奖励
    NotFinishOrderTipPnlCtrl.prototype.getPayReward = function (order) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, orders;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!order.token) return [3 /*break*/, 2];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.consumeOrder({ token: order.token })];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetPayRewards', { uid: order.cpOrderId, orderId: order.orderId })];
                    case 3:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (order.productId === Constant_1.RECHARGE_BATTLE_PASS) {
                            GameHelper_1.gameHpr.user.setBattlePassBuyPass();
                            EventReportHelper_1.eventReportHelper.checkReportShopPackage(order.productId);
                        }
                        else {
                            GameHelper_1.gameHpr.user.setIngot(data === null || data === void 0 ? void 0 : data.ingot); //设置金币
                            GameHelper_1.gameHpr.user.addRechargeCountRecord(order.productId, 1); //添加购买次数
                            if (data === null || data === void 0 ? void 0 : data.addCount) {
                                GameHelper_1.gameHpr.addGainMassage({ type: Enums_1.CType.INGOT, count: data.addCount });
                            }
                        }
                        ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
                        orders = GameHelper_1.gameHpr.user.removeNotFinishOrders('cpOrderId', order.cpOrderId);
                        if (this.isValid) {
                            this.updateList(orders);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    NotFinishOrderTipPnlCtrl = __decorate([
        ccclass
    ], NotFinishOrderTipPnlCtrl);
    return NotFinishOrderTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NotFinishOrderTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXG1haW5cXE5vdEZpbmlzaE9yZGVyVGlwUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyREFBc0U7QUFDdEUscURBQW9EO0FBQ3BELHFEQUFvRDtBQUNwRCwyRUFBMEU7QUFDMUUsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCwyREFBMEQ7QUFDMUQsMkRBQTBEO0FBQzFELDZEQUE0RDtBQUdwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUFzRCw0Q0FBYztJQUFwRTtRQUFBLHFFQXlKQztRQXZKRywwQkFBMEI7UUFDbEIsYUFBTyxHQUFrQixJQUFJLENBQUEsQ0FBQyxzQkFBc0I7UUFDNUQsTUFBTTtRQUVFLGVBQVMsR0FBWSxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQTtRQUNwQyxVQUFJLEdBQWMsSUFBSSxDQUFBOztJQWtKbEMsQ0FBQztJQWhKVSxrREFBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLDJDQUFRLEdBQXJCOzs7Z0JBQ0ksSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFBOzs7O0tBQ3BDO0lBRU0sMENBQU8sR0FBZCxVQUFlLElBQVM7UUFDcEIsSUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxDQUFBO1FBQzdDLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLENBQUE7SUFDM0IsQ0FBQztJQUVNLDJDQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sMENBQU8sR0FBZDtRQUNJLFNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7SUFDM0MsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0IsaURBQWlEO0lBQ2pELCtDQUFZLEdBQVosVUFBYSxLQUEwQixFQUFFLENBQVM7UUFDOUMsSUFBTSxJQUFJLEdBQUcsS0FBSyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQ3JDLElBQUksSUFBSSxFQUFFO1lBQ04sSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUNoQjtJQUNMLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRWpILGlIQUFpSDtJQUV6Ryw2Q0FBVSxHQUFsQixVQUFtQixNQUFhO1FBQWhDLGlCQXNDQztRQXJDRyxJQUFJLE1BQU0sQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFO1lBQ3JCLE9BQU8sSUFBSSxDQUFDLElBQUksRUFBRSxDQUFBO1NBQ3JCO1FBQ0QsSUFBTSxjQUFjLEdBQUcsRUFBRSxDQUFBO1FBQ3pCLElBQU0sR0FBRyxHQUFHLG9CQUFPLENBQUMsSUFBSSxDQUFDLHNCQUFzQixFQUFFLENBQUE7UUFDakQsS0FBSyxJQUFJLENBQUMsSUFBSSxHQUFHLEVBQUU7WUFDZixjQUFjLENBQUMsQ0FBQyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFBO1NBQzdCO1FBQ0QsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFDLEVBQUUsRUFBRSxDQUFDO1lBQ25DLElBQU0sSUFBSSxHQUFHLEVBQUUsQ0FBQyxJQUFJLEdBQUcsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ2hDLElBQU0sSUFBSSxHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDL0UsSUFBSSxJQUFJLEVBQUU7Z0JBQ04sSUFBSSxLQUFLLEdBQUcsQ0FBQyxDQUFBO2dCQUNiLElBQU0sYUFBYSxHQUFHLGNBQWMsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO2dCQUMxRCxJQUFJLENBQUMsYUFBYSxFQUFFO29CQUNoQixLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO2lCQUNsQztxQkFBTTtvQkFDSCxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO2lCQUNsQztnQkFDRCxlQUFlO2dCQUNmLEtBQUssSUFBSSxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcsR0FBRyxJQUFJLENBQUMsUUFBUSxHQUFHLENBQUMsRUFBRSxHQUFHLEVBQUUsRUFBRTtvQkFDOUMsS0FBSyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7aUJBQ3JDO2dCQUNELElBQU0sVUFBUSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ2pDLHFCQUFTLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsVUFBUSxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksVUFBUSxDQUFDLFVBQVUsQ0FBQyxLQUFJLENBQUMsU0FBUyxFQUFFLFNBQVMsRUFBRSxHQUFHLENBQUMsRUFBbkUsQ0FBbUUsQ0FBQyxDQUFBO2dCQUNqSSxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxvQkFBb0IsRUFBRSxLQUFLLENBQUMsQ0FBQTtnQkFDMUQsRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxxQkFBUyxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQTtnQkFDbkYsRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxZQUFZLENBQUMsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLFFBQVEsSUFBSSxDQUFDLENBQUMsQ0FBQTtnQkFDdEUsY0FBYyxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxhQUFhLEdBQUcsQ0FBQyxDQUFBO2FBQ3REO2lCQUFNLElBQUksSUFBSSxDQUFDLFNBQVMsS0FBSywrQkFBb0IsRUFBRSxFQUFFLElBQUk7Z0JBQ3RELElBQU0sVUFBUSxHQUFHLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ2pDLHFCQUFTLENBQUMsUUFBUSxDQUFDLHFCQUFxQixFQUFFLFVBQVEsRUFBRSxLQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLGNBQU0sT0FBQSxLQUFJLENBQUMsT0FBTyxJQUFJLFVBQVEsQ0FBQyxVQUFVLENBQUMsS0FBSSxDQUFDLFNBQVMsRUFBRSxTQUFTLEVBQUUsR0FBRyxDQUFDLEVBQW5FLENBQW1FLENBQUMsQ0FBQTtnQkFDN0ksRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxZQUFZLENBQUMsd0JBQXdCLENBQUMsQ0FBQTtnQkFDdkQsRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxxQkFBUyxDQUFDLHNCQUFzQixFQUFFLENBQUE7Z0JBQ3ZFLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxDQUFBO2FBQ3JDO1FBQ0wsQ0FBQyxDQUFDLENBQUE7SUFDTixDQUFDO0lBRWEscUNBQUUsR0FBaEIsVUFBaUIsSUFBUzs7Ozs7d0JBQ3RCLHVCQUFVLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFBOzZCQUM1QixDQUFBLElBQUksQ0FBQyxLQUFLLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxTQUFTLENBQUEsRUFBbEMsd0JBQWtDO3dCQUNsQyxxQkFBTSxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxFQUFBLENBQUMsc0JBQXNCOzt3QkFBcEQsU0FBNkIsQ0FBQSxDQUFDLHNCQUFzQjs7OzZCQUM3QyxJQUFJLENBQUMsT0FBTyxFQUFaLHdCQUFZO3dCQUNuQixxQkFBTSxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxFQUFBOzt3QkFBL0IsU0FBK0IsQ0FBQTs7O3dCQUUvQixPQUFPLENBQUMsR0FBRyxDQUFDLHlEQUF5RCxDQUFDLENBQUE7Ozt3QkFFMUUsdUJBQVUsQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLENBQUE7Ozs7O0tBQ3BDO0lBRUQsT0FBTztJQUNPLGlEQUFjLEdBQTVCLFVBQTZCLEtBQVU7Ozs7OzRCQUNiLHFCQUFNLG9CQUFPLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyx5QkFBeUIsRUFBRTs0QkFDdkUsU0FBUyxFQUFFLEtBQUssQ0FBQyxTQUFTOzRCQUMxQixPQUFPLEVBQUUsS0FBSyxDQUFDLE9BQU87NEJBQ3RCLFNBQVMsRUFBRSxLQUFLLENBQUMsU0FBUzs0QkFDMUIsS0FBSyxFQUFFLEtBQUssQ0FBQyxLQUFLOzRCQUNsQixRQUFRLEVBQUUsS0FBSyxDQUFDLFFBQVE7NEJBQ3hCLEtBQUssRUFBRSxLQUFLLENBQUMsU0FBUzs0QkFDdEIsWUFBWSxFQUFFLEtBQUssQ0FBQyxZQUFZOzRCQUNoQyxZQUFZLEVBQUUsS0FBSyxDQUFDLFlBQVk7NEJBQ2hDLFNBQVMsRUFBRSxLQUFLLENBQUMsU0FBUzs0QkFDMUIsUUFBUSxFQUFFLEtBQUssQ0FBQyxRQUFRO3lCQUMzQixDQUFDLEVBQUE7O3dCQVhJLEtBQWdCLFNBV3BCLEVBWE0sR0FBRyxTQUFBLEVBQUUsSUFBSSxVQUFBOzZCQVliLEdBQUcsRUFBSCx3QkFBRzs2QkFDQyxDQUFBLEdBQUcsS0FBSyxhQUFLLENBQUMsY0FBYyxJQUFJLEdBQUcsS0FBSyxhQUFLLENBQUMsY0FBYyxJQUFJLEdBQUcsS0FBSyxhQUFLLENBQUMsbUJBQW1CLENBQUEsRUFBakcsd0JBQWlHO3dCQUNqRyxRQUFRO3dCQUNSLHFCQUFNLHFCQUFTLENBQUMsWUFBWSxDQUFDLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxLQUFLLEVBQUUsQ0FBQzs0QkFDcEQsT0FBTzswQkFENkM7O3dCQURwRCxRQUFRO3dCQUNSLFNBQW9ELENBQUE7d0JBRTlDLE1BQU0sR0FBRyxvQkFBTyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxTQUFTLEVBQUUsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFBO3dCQUMzRSxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ2QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsQ0FBQTt5QkFDMUI7OzRCQUVMLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxFQUFBOzt3QkFFcEMsS0FBSyxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFBO3dCQUNoQyxPQUFPO3dCQUNQLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUE7Ozs7O0tBQzNCO0lBRUQsT0FBTztJQUNPLCtDQUFZLEdBQTFCLFVBQTJCLEtBQVU7Ozs7Ozs2QkFFN0IsS0FBSyxDQUFDLEtBQUssRUFBWCx3QkFBVzt3QkFDWCxxQkFBTSxxQkFBUyxDQUFDLFlBQVksQ0FBQyxFQUFFLEtBQUssRUFBRSxLQUFLLENBQUMsS0FBSyxFQUFFLENBQUMsRUFBQTs7d0JBQXBELFNBQW9ELENBQUE7OzRCQUVsQyxxQkFBTSxvQkFBTyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsd0JBQXdCLEVBQUUsRUFBRSxHQUFHLEVBQUUsS0FBSyxDQUFDLFNBQVMsRUFBRSxPQUFPLEVBQUUsS0FBSyxDQUFDLE9BQU8sRUFBRSxDQUFDLEVBQUE7O3dCQUFySCxLQUFnQixTQUFxRyxFQUFuSCxHQUFHLFNBQUEsRUFBRSxJQUFJLFVBQUE7d0JBQ2pCLElBQUksR0FBRyxFQUFFOzRCQUNMLHNCQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxFQUFBO3lCQUNuQzs2QkFBTSxJQUFJLEtBQUssQ0FBQyxTQUFTLEtBQUssK0JBQW9CLEVBQUU7NEJBQ2pELG9CQUFPLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFLENBQUE7NEJBQ25DLHFDQUFpQixDQUFDLHNCQUFzQixDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQTt5QkFDNUQ7NkJBQU07NEJBQ0gsb0JBQU8sQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLENBQUMsQ0FBQSxDQUFDLE1BQU07NEJBQ3pDLG9CQUFPLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLENBQUEsQ0FBQyxRQUFROzRCQUNoRSxJQUFJLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxRQUFRLEVBQUU7Z0NBQ2hCLG9CQUFPLENBQUMsY0FBYyxDQUFDLEVBQUUsSUFBSSxFQUFFLGFBQUssQ0FBQyxLQUFLLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFBOzZCQUN0RTt5QkFDSjt3QkFDRCx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFBO3dCQUVyQyxNQUFNLEdBQUcsb0JBQU8sQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsV0FBVyxFQUFFLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQTt3QkFDL0UsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFOzRCQUNkLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLENBQUE7eUJBQzFCOzs7OztLQUNKO0lBeEpnQix3QkFBd0I7UUFENUMsT0FBTztPQUNhLHdCQUF3QixDQXlKNUM7SUFBRCwrQkFBQztDQXpKRCxBQXlKQyxDQXpKcUQsRUFBRSxDQUFDLFdBQVcsR0F5Sm5FO2tCQXpKb0Isd0JBQXdCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUkVDSEFSR0VfQkFUVExFX1BBU1MgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCB7IENUeXBlIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgZXZlbnRSZXBvcnRIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9FdmVudFJlcG9ydEhlbHBlclwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IGpzYkhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0pzYkhlbHBlclwiO1xuaW1wb3J0IHsgcGF5SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvUGF5SGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgVXNlck1vZGVsIGZyb20gXCIuLi8uLi9tb2RlbC9jb21tb24vVXNlck1vZGVsXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE5vdEZpbmlzaE9yZGVyVGlwUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSBsaXN0U3ZfOiBjYy5TY3JvbGxWaWV3ID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9saXN0X3N2XG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIElDT05fU0laRTogY2MuU2l6ZSA9IGNjLnNpemUoODAsIDgwKVxuICAgIHByaXZhdGUgdXNlcjogVXNlck1vZGVsID0gbnVsbFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgICAgICB0aGlzLnVzZXIgPSB0aGlzLmdldE1vZGVsKCd1c2VyJylcbiAgICB9XG5cbiAgICBwdWJsaWMgb25FbnRlcihkYXRhOiBhbnkpIHtcbiAgICAgICAgY29uc3Qgb3JkZXJzID0gdGhpcy51c2VyLmdldE5vdEZpbmlzaE9yZGVycygpXG4gICAgICAgIHRoaXMudXBkYXRlTGlzdChvcmRlcnMpXG4gICAgfVxuXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xuICAgICAgICBhc3NldHNNZ3IucmVsZWFzZVRlbXBSZXNCeVRhZyh0aGlzLmtleSlcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdi92aWV3L2NvbnRlbnQvaXRlbS9jbGFpbV9iZVxuICAgIG9uQ2xpY2tDbGFpbShldmVudDogY2MuRXZlbnQuRXZlbnRUb3VjaCwgXzogc3RyaW5nKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBldmVudC50YXJnZXQucGFyZW50LkRhdGFcbiAgICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgICAgIHRoaXMuZG8oZGF0YSlcbiAgICAgICAgfVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgdXBkYXRlTGlzdChvcmRlcnM6IGFueVtdKSB7XG4gICAgICAgIGlmIChvcmRlcnMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5oaWRlKClcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjb3VudFJlY29yZE1hcCA9IHt9XG4gICAgICAgIGNvbnN0IG9iaiA9IGdhbWVIcHIudXNlci5nZXRSZWNoYXJnZUNvdW50UmVjb3JkKClcbiAgICAgICAgZm9yIChsZXQgayBpbiBvYmopIHtcbiAgICAgICAgICAgIGNvdW50UmVjb3JkTWFwW2tdID0gb2JqW2tdXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5saXN0U3ZfLkxpc3Qob3JkZXJzLmxlbmd0aCwgKGl0LCBpKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gaXQuRGF0YSA9IG9yZGVyc1tpXVxuICAgICAgICAgICAgY29uc3QganNvbiA9IGFzc2V0c01nci5nZXRKc29uKCdyZWNoYXJnZScpLmdldCgncHJvZHVjdF9pZCcsIGRhdGEucHJvZHVjdElkKVswXVxuICAgICAgICAgICAgaWYgKGpzb24pIHtcbiAgICAgICAgICAgICAgICBsZXQgY291bnQgPSAwXG4gICAgICAgICAgICAgICAgY29uc3QgcmVjaGFyZ2VDb3VudCA9IGNvdW50UmVjb3JkTWFwW2pzb24ucHJvZHVjdF9pZF0gfHwgMFxuICAgICAgICAgICAgICAgIGlmICghcmVjaGFyZ2VDb3VudCkge1xuICAgICAgICAgICAgICAgICAgICBjb3VudCA9IGpzb24uaW5nb3QgKyBqc29uLmluZ290XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY291bnQgPSBqc29uLmluZ290ICsganNvbi5leHRyYVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDlpJrlh7rmnaXnmoTlh6DmrKEg5oyJ5q2j5bi46K6h566XXG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaWR4ID0gMDsgaWR4IDwgZGF0YS5xdWFudGl0eSAtIDE7IGlkeCsrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvdW50ICs9IChqc29uLmluZ290ICsganNvbi5leHRyYSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgaWNvbk5vZGUgPSBpdC5DaGlsZCgnaWNvbicpXG4gICAgICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRJY29uKGpzb24uaWNvbiwgaWNvbk5vZGUsIHRoaXMua2V5KS50aGVuKCgpID0+IHRoaXMuaXNWYWxpZCAmJiBpY29uTm9kZS5hZGFwdFNjYWxlKHRoaXMuSUNPTl9TSVpFLCB1bmRlZmluZWQsIDEuMSkpXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ25hbWUnKS5zZXRMb2NhbGVLZXkoJ3VpLnNob3BfaW5nb3RfbmFtZScsIGNvdW50KVxuICAgICAgICAgICAgICAgIGl0LkNoaWxkKCdtb25leScsIGNjLkxhYmVsKS5zdHJpbmcgPSBwYXlIZWxwZXIuZ2V0UHJvZHVjdFByaWNlVGV4dChqc29uLnByb2R1Y3RfaWQpXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ2NvdW50Jykuc2V0TG9jYWxlS2V5KCd1aS5xdWFudGl0eV90ZXh0JywgZGF0YS5xdWFudGl0eSB8fCAxKVxuICAgICAgICAgICAgICAgIGNvdW50UmVjb3JkTWFwW2pzb24ucHJvZHVjdF9pZF0gPSByZWNoYXJnZUNvdW50ICsgMVxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnByb2R1Y3RJZCA9PT0gUkVDSEFSR0VfQkFUVExFX1BBU1MpIHsgLy/miJjku6RcbiAgICAgICAgICAgICAgICBjb25zdCBpY29uTm9kZSA9IGl0LkNoaWxkKCdpY29uJylcbiAgICAgICAgICAgICAgICByZXNIZWxwZXIubG9hZEljb24oJ2ljb24vYmF0dGxlX3Bhc3NfdXAnLCBpY29uTm9kZSwgdGhpcy5rZXkpLnRoZW4oKCkgPT4gdGhpcy5pc1ZhbGlkICYmIGljb25Ob2RlLmFkYXB0U2NhbGUodGhpcy5JQ09OX1NJWkUsIHVuZGVmaW5lZCwgMS4xKSlcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgnbmFtZScpLnNldExvY2FsZUtleSgndWkuYmF0dGxlX3Bhc3NfdXBfbmFtZScpXG4gICAgICAgICAgICAgICAgaXQuQ2hpbGQoJ21vbmV5JywgY2MuTGFiZWwpLnN0cmluZyA9IHBheUhlbHBlci5nZXRCYXR0bGVQYXNzUHJpY2VUZXh0KClcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgnY291bnQnKS5zZXRMb2NhbGVLZXkoJycpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBhc3luYyBkbyhkYXRhOiBhbnkpIHtcbiAgICAgICAgdmlld0hlbHBlci5zaG93TG9hZGluZ1dhaXQodHJ1ZSlcbiAgICAgICAgaWYgKGRhdGEuc3RhdGUgPT09IDEgJiYgZGF0YS5jcE9yZGVySWQpIHtcbiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0UGF5UmV3YXJkKGRhdGEpIC8v6L+Z56eN5piv5pyN5Yqh5Zmo5Y+R5LiL5p2l55qE6aqM6K+B5LqGIOS9huaYr+i/mOacqumihuWPllxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEub3JkZXJJZCkge1xuICAgICAgICAgICAgYXdhaXQgdGhpcy52ZXJpZnlQYXlPcmRlcihkYXRhKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ05vdEZpbmlzaE9yZGVyIERPIEVycm9yLiBub3QgY3BPcmRlcklkIGFuZCBub3Qgb3JkZXJJZD8nKVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd0xvYWRpbmdXYWl0KGZhbHNlKVxuICAgIH1cblxuICAgIC8vIOmqjOivgeiuouWNlVxuICAgIHByaXZhdGUgYXN5bmMgdmVyaWZ5UGF5T3JkZXIob3JkZXI6IGFueSkge1xuICAgICAgICBjb25zdCB7IGVyciwgZGF0YSB9ID0gYXdhaXQgZ2FtZUhwci5uZXQucmVxdWVzdCgnbG9iYnkvSERfVmVyaWZ5UGF5T3JkZXInLCB7XG4gICAgICAgICAgICBwcm9kdWN0SWQ6IG9yZGVyLnByb2R1Y3RJZCxcbiAgICAgICAgICAgIG9yZGVySWQ6IG9yZGVyLm9yZGVySWQsXG4gICAgICAgICAgICBjcE9yZGVySWQ6IG9yZGVyLmNwT3JkZXJJZCxcbiAgICAgICAgICAgIHRva2VuOiBvcmRlci50b2tlbixcbiAgICAgICAgICAgIHBsYXRmb3JtOiBvcmRlci5wbGF0Zm9ybSxcbiAgICAgICAgICAgIHByaWNlOiBvcmRlci5wYXlBbW91bnQsXG4gICAgICAgICAgICBwdXJjaGFzZVRpbWU6IG9yZGVyLnB1cmNoYXNlVGltZSxcbiAgICAgICAgICAgIGN1cnJlbmN5VHlwZTogb3JkZXIuY3VycmVuY3lUeXBlLFxuICAgICAgICAgICAgcGF5QW1vdW50OiBvcmRlci5wYXlBbW91bnQsXG4gICAgICAgICAgICBxdWFudGl0eTogb3JkZXIucXVhbnRpdHksXG4gICAgICAgIH0pXG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIGlmIChlcnIgPT09IGVjb2RlLk9SREVSX0ZJTklTSEVEIHx8IGVyciA9PT0gZWNvZGUuT1JERVJfUkVGVU5ERUQgfHwgZXJyID09PSBlY29kZS5PUkRFUl9WRVJJRllfUkVQRUFUKSB7IC8v5bey6aKG5Y+W5oiW5bey6YCA5qy+IOmHjeWkjemqjOivgeiuouWNlVxuICAgICAgICAgICAgICAgIC8vIOWFiOagh+iusOa2iOi0uVxuICAgICAgICAgICAgICAgIGF3YWl0IGpzYkhlbHBlci5jb25zdW1lT3JkZXIoeyB0b2tlbjogb3JkZXIudG9rZW4gfSlcbiAgICAgICAgICAgICAgICAvLyDliLfmlrDliJfooahcbiAgICAgICAgICAgICAgICBjb25zdCBvcmRlcnMgPSBnYW1lSHByLnVzZXIucmVtb3ZlTm90RmluaXNoT3JkZXJzKCdvcmRlcklkJywgb3JkZXIub3JkZXJJZClcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlTGlzdChvcmRlcnMpXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KGVycilcbiAgICAgICAgfVxuICAgICAgICBvcmRlci5jcE9yZGVySWQgPSBkYXRhLmNwT3JkZXJJZFxuICAgICAgICAvLyDpooblj5blpZblirFcbiAgICAgICAgdGhpcy5nZXRQYXlSZXdhcmQob3JkZXIpXG4gICAgfVxuXG4gICAgLy8g6aKG5Y+W5aWW5YqxXG4gICAgcHJpdmF0ZSBhc3luYyBnZXRQYXlSZXdhcmQob3JkZXI6IGFueSkge1xuICAgICAgICAvLyDlhYjmtojotLnmjolcbiAgICAgICAgaWYgKG9yZGVyLnRva2VuKSB7XG4gICAgICAgICAgICBhd2FpdCBqc2JIZWxwZXIuY29uc3VtZU9yZGVyKHsgdG9rZW46IG9yZGVyLnRva2VuIH0pXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyBlcnIsIGRhdGEgfSA9IGF3YWl0IGdhbWVIcHIubmV0LnJlcXVlc3QoJ2xvYmJ5L0hEX0dldFBheVJld2FyZHMnLCB7IHVpZDogb3JkZXIuY3BPcmRlcklkLCBvcmRlcklkOiBvcmRlci5vcmRlcklkIH0pXG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgIH0gZWxzZSBpZiAob3JkZXIucHJvZHVjdElkID09PSBSRUNIQVJHRV9CQVRUTEVfUEFTUykge1xuICAgICAgICAgICAgZ2FtZUhwci51c2VyLnNldEJhdHRsZVBhc3NCdXlQYXNzKClcbiAgICAgICAgICAgIGV2ZW50UmVwb3J0SGVscGVyLmNoZWNrUmVwb3J0U2hvcFBhY2thZ2Uob3JkZXIucHJvZHVjdElkKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgZ2FtZUhwci51c2VyLnNldEluZ290KGRhdGE/LmluZ290KSAvL+iuvue9rumHkeW4gVxuICAgICAgICAgICAgZ2FtZUhwci51c2VyLmFkZFJlY2hhcmdlQ291bnRSZWNvcmQob3JkZXIucHJvZHVjdElkLCAxKSAvL+a3u+WKoOi0reS5sOasoeaVsFxuICAgICAgICAgICAgaWYgKGRhdGE/LmFkZENvdW50KSB7XG4gICAgICAgICAgICAgICAgZ2FtZUhwci5hZGRHYWluTWFzc2FnZSh7IHR5cGU6IENUeXBlLklOR09ULCBjb3VudDogZGF0YS5hZGRDb3VudCB9KVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5jbGFpbV9zdWNjZWVkJylcbiAgICAgICAgLy8g5Yi35paw5YiX6KGoXG4gICAgICAgIGNvbnN0IG9yZGVycyA9IGdhbWVIcHIudXNlci5yZW1vdmVOb3RGaW5pc2hPcmRlcnMoJ2NwT3JkZXJJZCcsIG9yZGVyLmNwT3JkZXJJZClcbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCkge1xuICAgICAgICAgICAgdGhpcy51cGRhdGVMaXN0KG9yZGVycylcbiAgICAgICAgfVxuICAgIH1cbn1cbiJdfQ==