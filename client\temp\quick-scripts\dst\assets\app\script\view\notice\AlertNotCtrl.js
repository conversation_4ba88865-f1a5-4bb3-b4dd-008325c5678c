
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/AlertNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '772ed8682pCy4l/dMm95uut', 'AlertNotCtrl');
// app/script/view/notice/AlertNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var NotEvent_1 = require("../../common/event/NotEvent");
var ccclass = cc._decorator.ccclass;
var AlertNotCtrl = /** @class */ (function (_super) {
    __extends(AlertNotCtrl, _super);
    function AlertNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.roootNode_ = null; // path://rooot_n
        _this.textLbl_ = null; // path://rooot_n/text_l
        //@end
        _this.showTime = 1; //显示时间
        _this.cb = null;
        return _this;
    }
    AlertNotCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[NotEvent_1.default.OPEN_ALERT] = this.onEventOpen, _a),
        ];
    };
    AlertNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    AlertNotCtrl.prototype.onClickClose = function (event, data) {
        var _this = this;
        this.roootNode_.stopAllActions();
        cc.tween(this.roootNode_)
            .to(0.18, { opacity: 0 })
            .call(function () { return _this.leave(); })
            .start();
        this.closeNode_.stopAllActions();
        cc.tween(this.closeNode_)
            .to(0.18, { opacity: 0 })
            .start();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AlertNotCtrl.prototype.onEventOpen = function (msg, opts) {
        var _a;
        var _this = this;
        this.open();
        this.cb = opts === null || opts === void 0 ? void 0 : opts.cb;
        (_a = this.textLbl_).setLocaleKey.apply(_a, __spread([msg], ((opts === null || opts === void 0 ? void 0 : opts.params) || [])));
        // this.textLbl_._forceUpdateRenderData()
        // this.roootNode_.width = Math.max(280, this.textLbl_.node.width + 40)
        var showTime = (opts === null || opts === void 0 ? void 0 : opts.showTime) || this.showTime;
        this.roootNode_.stopAllActions();
        this.roootNode_.opacity = 255;
        this.roootNode_.scale = 0.2;
        cc.tween(this.roootNode_)
            .to(0.2, { scale: 1 }, { easing: cc.easing.backOut })
            .delay(showTime)
            .to(0.18, { opacity: 0 })
            .call(function () { return _this.leave(); })
            .start();
        this.closeNode_.stopAllActions();
        this.closeNode_.opacity = 0;
        cc.tween(this.closeNode_)
            .to(0.2, { opacity: 120 })
            .delay(showTime)
            .to(0.18, { opacity: 0 })
            .start();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AlertNotCtrl.prototype.leave = function () {
        this.hide();
        this.cb && this.cb();
        this.cb = null;
    };
    AlertNotCtrl = __decorate([
        ccclass
    ], AlertNotCtrl);
    return AlertNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = AlertNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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