
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/TaskTreasureListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b6353ho8LdPkLcpAkfMZoFs', 'TaskTreasureListPnlCtrl');
// app/script/view/common/TaskTreasureListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TaskTreasureListPnlCtrl = /** @class */ (function (_super) {
    __extends(TaskTreasureListPnlCtrl, _super);
    function TaskTreasureListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.listSv_ = null; // path://root_n/list_sv
        //@end
        _this.task = null;
        return _this;
    }
    TaskTreasureListPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    TaskTreasureListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    TaskTreasureListPnlCtrl.prototype.onEnter = function (task) {
        var _this = this;
        if (!task || task.treasureRewards.length === 0) {
            return this.hide();
        }
        this.task = task;
        var height = this.listSv_.node.height = Math.min(task.treasureRewards.length * 108 - 2, 800 - 24);
        this.rootNode_.height = height + 24;
        this.listSv_.Component(cc.Widget).updateAlignment();
        this.listSv_.Items(task.treasureRewards, function (it, data, i) {
            var _a;
            it.Data = i;
            it.Child('name/val').setLocaleKey('ui.treasure_name_' + (((_a = data.json) === null || _a === void 0 ? void 0 : _a.lv) || 1));
            _this.updateItem(it, data);
        });
    };
    TaskTreasureListPnlCtrl.prototype.onRemove = function () {
    };
    TaskTreasureListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/list_sv/view/content/item/claim_be
    TaskTreasureListPnlCtrl.prototype.onClickClaim = function (event, _) {
        var _this = this;
        var it = event.target.parent;
        var data = this.task.treasureRewards[it.Data];
        if (!data) {
            return;
        }
        else if (data.rewards.length === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TREASURE_NOT_OPEN);
        }
        var items = GameHelper_1.gameHpr.checkRewardFull(data.rewards);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimAward(_this.task.id, it.Data, data.rewards); });
        }
        this.claimAward(this.task.id, it.Data, data.rewards);
    };
    // path://root_n/list_sv/view/content/item/open_be
    TaskTreasureListPnlCtrl.prototype.onClickOpen = function (event, _) {
        var it = event.target.parent;
        var data = this.task.treasureRewards[it.Data];
        if (!data) {
            return;
        }
        else if (data.rewards.length > 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.TREASURE_YET_OPEN);
        }
        this.claimAward(this.task.id, it.Data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    TaskTreasureListPnlCtrl.prototype.updateItem = function (it, data) {
        var _a;
        var award = it.Child('award'), desc = it.Child('desc');
        var hasAward = award.active = it.Child('claim_be').active = data.rewards.length > 0;
        var state = hasAward ? 1 : 0;
        ResHelper_1.resHelper.loadIcon('icon/treasure_' + (((_a = data.json) === null || _a === void 0 ? void 0 : _a.lv) || 1) + '_' + state, it.Child('icon'), this.key);
        desc.active = it.Child('open_be').active = !hasAward;
        if (hasAward) {
            ViewHelper_1.viewHelper.updateItemByCTypes(award, data.rewards);
        }
    };
    TaskTreasureListPnlCtrl.prototype.claimAward = function (id, i, rewards) {
        var _this = this;
        var _a;
        var player = GameHelper_1.gameHpr.player, type = (_a = this.task.json) === null || _a === void 0 ? void 0 : _a.type;
        var claimTaskRewardFunc = null;
        if (type === 20) { //每日任务
            claimTaskRewardFunc = player.claimTodayTaskReward.bind(player);
        }
        else if (type === 30) { //其他任务
            claimTaskRewardFunc = player.claimOtherTaskReward.bind(player);
        }
        else {
            return;
        }
        claimTaskRewardFunc(id, i + 1, 0).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            GameHelper_1.gameHpr.addGainMassage(rewards);
            if (!_this.isValid) {
            }
            else if (type === 20) { //每日任务
                _this.onEnter(player.getTodayTasks().find(function (m) { return m.id === _this.task.id; }));
            }
            else if (type === 30) { //其他任务
                _this.onEnter(player.getOtherTasks().find(function (m) { return m.id === _this.task.id; }));
            }
        });
    };
    TaskTreasureListPnlCtrl = __decorate([
        ccclass
    ], TaskTreasureListPnlCtrl);
    return TaskTreasureListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TaskTreasureListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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