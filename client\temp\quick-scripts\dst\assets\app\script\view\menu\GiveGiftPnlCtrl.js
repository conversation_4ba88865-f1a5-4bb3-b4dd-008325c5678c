
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/GiveGiftPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ec369nOZ1tKWIcrHIY+jLCt', 'GiveGiftPnlCtrl');
// app/script/view/menu/GiveGiftPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var GiveGiftPnlCtrl = /** @class */ (function (_super) {
    __extends(GiveGiftPnlCtrl, _super);
    function GiveGiftPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.giftNode_ = null; // path://root/gift_n
        _this.giftboxNode_ = null; // path://root/giftbox_n
        //@end
        _this.friendUid = '';
        _this.friendHead = null;
        _this.selectGiftId = 0;
        _this.selectGiftBoxId = 0;
        return _this;
    }
    GiveGiftPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GiveGiftPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    GiveGiftPnlCtrl.prototype.onEnter = function (uid, friendHead) {
        this.friendUid = uid;
        this.friendHead = friendHead;
        this.selectGiftId = 0;
        this.selectGiftBoxId = 0;
        this.updateGifts();
    };
    GiveGiftPnlCtrl.prototype.onRemove = function () {
        this.friendHead = null;
    };
    GiveGiftPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/giftbox_n/list/giftbox_be
    GiveGiftPnlCtrl.prototype.onClickGiftbox = function (event, data) {
        var id = event.target.Data;
        if (this.selectGiftBoxId === id) {
            return;
        }
        this.selectGiftBoxId = id;
        var listNode = this.giftboxNode_.Child('list');
        listNode.children.forEach(function (m) { return m.Child('root/select').active = m.Data === id; });
    };
    // path://root/ok_be
    GiveGiftPnlCtrl.prototype.onClickOk = function (event, data) {
        if (!this.selectGiftId) {
            return ViewHelper_1.viewHelper.showAlert('ui.please_select_gift');
        }
        else if (!this.selectGiftBoxId) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_gift_box');
        }
        else if (GameHelper_1.gameHpr.user.getSkinItemCountById(this.selectGiftId) < 2) {
            return ViewHelper_1.viewHelper.showAlert('toast.skin_item_count_not_enough');
        }
        this.do();
    };
    // path://root/gift_n/list/view/content/gift_be
    GiveGiftPnlCtrl.prototype.onClickGift = function (event, data) {
        var _this = this;
        var _a, _b;
        var id = (_b = (_a = event.target) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.id;
        if (this.selectGiftId === id) {
            return;
        }
        this.selectGiftId = id;
        var sv = this.giftNode_.Child('list', cc.ScrollView);
        sv.content.children.forEach(function (m) { var _a; return _this.updateSelectGift(m.Child('root'), ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.id) === id); });
        this.openSelectGiftBox(true);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    GiveGiftPnlCtrl.prototype.updateGifts = function () {
        return __awaiter(this, void 0, void 0, function () {
            var user, err, sv, items, skins;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        user = GameHelper_1.gameHpr.user;
                        return [4 /*yield*/, user.reqSkinItemList(true)];
                    case 1:
                        err = _a.sent();
                        if (!this.isValid) {
                        }
                        else if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        sv = this.giftNode_.Child('list', cc.ScrollView);
                        items = user.getSkinItemList().filter(function (m) { return m.state === 0; });
                        skins = [];
                        items.forEach(function (m) {
                            var skin = skins.find(function (s) { return s.id === m.id; });
                            if (!skin) {
                                skins.push({ id: m.id, uids: [m.uid] });
                            }
                            else {
                                skin.uids.push(m.uid);
                            }
                        });
                        sv.Child('empty').active = items.length === 0;
                        skins.reverse();
                        sv.Items(skins, function (it, data) {
                            it.Data = data;
                            var root = it.Child('root');
                            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, root.Child('val'), _this.key);
                            it.Child('count', cc.Label).string = 'x' + data.uids.length;
                            _this.updateSelectGift(root, false);
                        });
                        this.openSelectGiftBox(false);
                        return [2 /*return*/];
                }
            });
        });
    };
    GiveGiftPnlCtrl.prototype.updateSelectGift = function (it, select) {
        it.Child('select2').active = it.Child('select1').active = select;
    };
    GiveGiftPnlCtrl.prototype.openSelectGiftBox = function (val) {
        var _this = this;
        var _a, _b;
        var listNode = this.giftboxNode_.Child('list');
        this.giftboxNode_.Child('none').active = !val;
        if (listNode.active = val) {
            this.selectGiftBoxId = 0;
            var giftboxType_1 = (_b = (_a = assetsMgr.getJsonData('pawnSkin', this.selectGiftId)) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : 1;
            listNode.Items(2, function (it, _, i) {
                var id = it.Data = giftboxType_1 * 100 + (i + 1);
                ResHelper_1.resHelper.loadIcon('icon/giftbox_' + id, it.Child('root/icon'), _this.key);
                it.Child('ingot/val', cc.Label).string = giftboxType_1 === 2 ? '100' : '1';
                it.Child('root/select').active = false;
            });
        }
    };
    GiveGiftPnlCtrl.prototype.do = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_SendSkinGift', { id: this.selectGiftId, boxId: this.selectGiftBoxId, friendUid: this.friendUid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.removeSkinItem(this.selectGiftId, data.uid);
                        ViewHelper_1.viewHelper.showPnl('menu/GiftBoxAnim', this.selectGiftBoxId, this.selectGiftId, this.friendHead).then(function () { return _this.isValid && _this.hide(); });
                        return [2 /*return*/];
                }
            });
        });
    };
    GiveGiftPnlCtrl = __decorate([
        ccclass
    ], GiveGiftPnlCtrl);
    return GiveGiftPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GiveGiftPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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