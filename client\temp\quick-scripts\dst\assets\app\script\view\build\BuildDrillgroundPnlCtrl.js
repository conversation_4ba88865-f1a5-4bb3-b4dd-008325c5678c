
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildDrillgroundPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f5978wbkFxIeKLRfuLPVlYZ', 'BuildDrillgroundPnlCtrl');
// app/script/view/build/BuildDrillgroundPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ECode_1 = require("../../common/constant/ECode");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ccclass = cc._decorator.ccclass;
var BuildDrillgroundPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildDrillgroundPnlCtrl, _super);
    function BuildDrillgroundPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.queueSv_ = null; // path://root/pages_n/1/drill/content/queue_sv
        //@end
        _this.PKEY_TAB = 'DRILLGROUND_TAB';
        _this.PKEY_SELECT_ARMY = 'DRILLGROUND_SELECT_ARMY';
        _this.PKEY_SELECT_PAWN = 'DRILLGROUND_SELECT_PAWN';
        _this.user = null;
        _this.player = null;
        _this.areaCenter = null;
        _this.data = null;
        _this.selectArmy = null;
        _this.selectPawn = null;
        _this.drillProgressTween = {};
        return _this;
    }
    BuildDrillgroundPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdatePawnLvingQueue, _b.enter = true, _b),
        ];
    };
    BuildDrillgroundPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.areaCenter = this.getModel('areaCenter');
                this.pagesNode_.Child('1/drill/title/bg/val').setLocaleKey('ui.drill_queue', 'ui.button_leveling');
                return [2 /*return*/];
            });
        });
    };
    BuildDrillgroundPnlCtrl.prototype.onEnter = function (data, tab) {
        this.data = data;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    BuildDrillgroundPnlCtrl.prototype.onRemove = function () {
        this.selectArmy = null;
        this.selectPawn = null;
    };
    BuildDrillgroundPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildDrillgroundPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = event.node.name;
        var node = this.pagesNode_.Swih(type)[0];
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        if (type === '0') {
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
        else if (type === '1') {
            this.updateArmyList(node);
            // 训练列表
            this.updateDrillQueue(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildDrillgroundPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/army/list/view/content/army_be
    BuildDrillgroundPnlCtrl.prototype.onClickArmy = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data) {
        }
        else if (!data.army) {
            return ViewHelper_1.viewHelper.showAlert('toast.army_not_in_maincity');
        }
        else if (data.uid !== ((_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.uid)) {
            this.user.setTempPreferenceData(this.PKEY_SELECT_ARMY, data.uid);
            this.updateArmySelect(data);
        }
    };
    // path://root/pages_n/1/pawn/list/view/content/pawn_be
    BuildDrillgroundPnlCtrl.prototype.onClickPawn = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (!data.armyUid) {
        }
        else if (data.uid === ((_a = this.selectPawn) === null || _a === void 0 ? void 0 : _a.uid)) {
            return ViewHelper_1.viewHelper.showPnl('area/PawnInfo', data, null, 'drillground');
        }
        var obj = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || {};
        obj[data.armyUid] = data.uid;
        this.user.setTempPreferenceData(this.PKEY_SELECT_PAWN, obj);
        this.updatePawnSelect(data);
    };
    // path://root/pages_n/1/pawn/cond/need/buttons/drill_be
    BuildDrillgroundPnlCtrl.prototype.onClickDrill = function (event, data) {
        var _this = this;
        if (!this.selectPawn) {
            return;
        }
        else if (!this.selectArmy) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_army');
        }
        this.areaCenter.pawnLvingToServer(this.data.aIndex, this.selectArmy.uid, this.selectPawn.uid).then(function (res) {
            if (res.err === ECode_1.ecode.ANTI_CHEAT) {
                ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
            }
            else if (res.err) {
                ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else if (_this.isValid) {
                _this.onUpdatePawnLvingQueue();
                _this.updateDrillCost(_this.pagesNode_.Child(1));
            }
        });
    };
    // path://root/pages_n/1/drill/content/0/drill_pawn_be
    BuildDrillgroundPnlCtrl.prototype.onClickDrillPawn = function (event, _) {
        var _a;
        var data = event.target.parent.Data;
        var pawn = (_a = this.areaCenter.getArea(data === null || data === void 0 ? void 0 : data.index)) === null || _a === void 0 ? void 0 : _a.getPawnByPrecise(data.auid, data.puid);
        if (pawn) {
            ViewHelper_1.viewHelper.showPnl('area/PawnInfo', this.areaCenter.createPawnByLvingInfo(pawn, data), data, 'drillground');
        }
    };
    // path://root/pages_n/1/drill/content/0/cancel_drill_be
    BuildDrillgroundPnlCtrl.prototype.onClickCancelDrill = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (!data) {
            return;
        }
        else if (data.surplusTime > 0) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.cancel_lving_no_back_cost_tip', {
                ok: function () {
                    _this.cancelLving(data);
                },
                cancel: function () { },
            });
        }
        this.cancelLving(data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildDrillgroundPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('lv').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper.updateBuildAttrInfo(node, data);
        }
    };
    // 刷新训练列表
    BuildDrillgroundPnlCtrl.prototype.onUpdatePawnLvingQueue = function () {
        this.updateDrillQueue();
        var node = this.pagesNode_.Child(1);
        this.updatePawnList(false, node);
        this.updateDrillCost(node);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildDrillgroundPnlCtrl.prototype.addArmyToList = function (data, army, pawns) {
        return {
            name: data.name,
            uid: data.uid,
            pawns: pawns,
            army: army
        };
    };
    // 刷新军队列表
    BuildDrillgroundPnlCtrl.prototype.updateArmyList = function (node) {
        var _this = this;
        var _a;
        node = node || this.pagesNode_.Child(1);
        var areaArmyMap = {};
        (_a = this.areaCenter.getArea(this.data.aIndex)) === null || _a === void 0 ? void 0 : _a.armys.forEach(function (m) {
            if (m.isCanDrillPawn()) {
                areaArmyMap[m.uid] = m;
            }
        });
        var armys = [];
        // 先装自己所有的军队 再装临时创建的军队
        this.player.getBaseArmys().forEach(function (m) { return armys.push(_this.addArmyToList(m, areaArmyMap[m.uid], m.pawns)); });
        armys.sort(function (a, b) { return Number(!!b.army) - Number(!!a.army); });
        var uid = this.user.getTempPreferenceMap(this.PKEY_SELECT_ARMY);
        var curArmy = uid ? armys.find(function (m) { return !!m.army && m.uid === uid; }) : null, index = -1;
        var sv = node.Child('army/list', cc.ScrollView);
        sv.Child('empty').active = !armys.length;
        sv.stopAutoScroll();
        sv.Items(armys, function (it, data, i) {
            var _a;
            it.Data = data;
            var army = data.army, pawnCount = (army === null || army === void 0 ? void 0 : army.pawns.length) || (((_a = data.pawns) === null || _a === void 0 ? void 0 : _a.length) || 0);
            var root = it.Child('root'), info = root.Child('info');
            root.Child('bg').Component(cc.MultiFrame).setFrame(!!army);
            info.opacity = army ? 255 : 150;
            info.Child('name', cc.Label).string = data ? ut.nameFormator(data.name, 7) : '';
            info.Child('count/val', cc.Label).string = '' + pawnCount;
            info.Child('goout').active = !army;
            // 显示选择
            if (!army) {
            }
            else if (index === -1 && (!curArmy || curArmy.uid === army.uid)) {
                curArmy = data;
                index = i;
                _this.user.setTempPreferenceData(_this.PKEY_SELECT_ARMY, army.uid);
            }
        });
        // 将选中的移动到中间
        sv.SelectItemToCentre(index);
        //
        this.updateArmySelect(curArmy, node);
    };
    BuildDrillgroundPnlCtrl.prototype.updateArmySelect = function (item, node) {
        node = node || this.pagesNode_.Child(1);
        this.selectArmy = item;
        var uid = item === null || item === void 0 ? void 0 : item.uid;
        node.Child('army/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
            m.Component(cc.Button).interactable = !select;
        });
        this.updatePawnList(true, node);
    };
    // 刷新士兵列表
    BuildDrillgroundPnlCtrl.prototype.updatePawnList = function (isLocation, node) {
        var _this = this;
        var _a, _b, _c;
        var army = (_a = this.selectArmy) === null || _a === void 0 ? void 0 : _a.army, pawns = [];
        if (army) {
            pawns.pushArr(army.pawns);
        }
        else if ((_b = this.selectArmy) === null || _b === void 0 ? void 0 : _b.pawns) {
            pawns.pushArr(this.selectArmy.pawns);
        }
        var obj = this.user.getTempPreferenceMap(this.PKEY_SELECT_PAWN) || {};
        var uid = obj[army === null || army === void 0 ? void 0 : army.uid] || '';
        var curPawn = (_c = army === null || army === void 0 ? void 0 : army.pawns) === null || _c === void 0 ? void 0 : _c.find(function (m) { return m.uid === uid; }), index = -1;
        var lvingPawnLvMap = {};
        this.player.getPawnLevelingQueues().forEach(function (m) { return lvingPawnLvMap[m.puid] = m.lv; });
        var sv = node.Child('pawn/info/list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.Items(pawns, function (it, data, i) {
            it.Data = data;
            ResHelper_1.resHelper.loadPawnHeadIcon(army ? data.getViewId() : data.id, it.Child('icon'), _this.key);
            var upLv = lvingPawnLvMap[data.uid];
            if (it.Child('lv').active = data.lv > 1 || !!upLv) {
                it.Child('lv/val', cc.Label).string = '' + data.lv;
                it.Child('lv/add', cc.Label).string = upLv > data.lv ? '+' + (upLv - data.lv) : '';
            }
            // 选择
            if (!army) {
            }
            else if (index === -1 && (!curPawn || curPawn.uid === data.uid)) {
                curPawn = data;
                index = i;
                obj[army.uid] = data.uid;
                _this.user.setTempPreferenceData(_this.PKEY_SELECT_PAWN, obj);
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(index);
        }
        this.updatePawnSelect(curPawn, node);
    };
    BuildDrillgroundPnlCtrl.prototype.updatePawnSelect = function (pawn, node) {
        node = node || this.pagesNode_.Child(1);
        node.Child('pawn/info').Swih(!pawn ? 'tip' : 'list');
        this.selectPawn = pawn;
        var uid = pawn === null || pawn === void 0 ? void 0 : pawn.uid;
        node.Child('pawn/info/list', cc.ScrollView).content.children.forEach(function (m) {
            var _a;
            m.Child('select').active = m.Child('bg/select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid;
        });
        this.updateDrillCost(node);
    };
    // 刷新训练费用
    BuildDrillgroundPnlCtrl.prototype.updateDrillCost = function (node) {
        var _this = this;
        var root = node.Child('pawn/cond'), upEffect = node.Child('pawn/up_effect');
        if (!this.selectPawn) {
            root.Swih('empty');
            return upEffect.Swih('empty');
        }
        else {
            var state = this.getDrillState();
            if (state) {
                upEffect.Child('effect').Swih('state', false, 'title')[0].Child('val').setLocaleKey(state);
                return root.Swih('empty');
            }
        }
        var pawn = this.selectPawn, effectNode = upEffect.Swih('effect')[0];
        var need = root.Swih('need')[0], effectAttr = effectNode.Swih('attrs', false, 'title')[0];
        need.Child('title/book/val', cc.Label).string = this.player.getExpBook() + '';
        // 刷新属性
        var nextAttrJson = assetsMgr.getJsonData('pawnAttr', pawn.id * 1000 + (pawn.lv + 1));
        if (effectAttr.active = !!nextAttrJson) {
            effectAttr.Child('attr').Items(this.getPawnMainAtts(pawn.attrJson, nextAttrJson), function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val', cc.Label).string = data.val + '';
                it.Child('next', cc.Label).string = '+' + (data.next - data.val);
            });
            var skillsNode = effectAttr.Child('skills'), skills = pawn.skills.filter(function (m) { return m.type > Enums_1.PawnSkillType.RESTRAIN &&
                m.type !== Enums_1.PawnSkillType.RESTRAIN_BEAST &&
                m.type !== Enums_1.PawnSkillType.PULL_STRING &&
                m.type !== Enums_1.PawnSkillType.FIRE; }); // 排除 克制、被动技能：狩猎、拉弦、火势 等
            skillsNode.Child('icons').Items(skills, function (it, skill) {
                ResHelper_1.resHelper.loadSkillIcon(skill.baseId, it.Child('val'), _this.key);
            });
            skillsNode.Child('lv').setLocaleKey('ui.short_lv', pawn.lv);
            skillsNode.Child('add', cc.Label).string = '+1';
        }
        // 刷新费用
        var season = GameHelper_1.gameHpr.world.getSeason();
        // 检测是否有训练士兵费用增加
        var cost = season.changeBaseResCost(Enums_1.CEffect.UPLVING_COST, pawn.upCost);
        // 减少时间
        var cd = (GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.UPLVING_CD) || 0);
        cd += GameHelper_1.gameHpr.getAncientEffectByPlayer(GameHelper_1.gameHpr.getUid(), Enums_1.CEffect.UPLVING_CD);
        var policyFreeCount = this.player.getFreeLevingPawnSurplusCount();
        ViewHelper_1.viewHelper.updateFreeCostView(need, cost, pawn.upTime, cd * 0.01, false, policyFreeCount);
        need.Child('buttons/drill_be/val').setLocaleKey(policyFreeCount > 0 ? 'ui.button_free_leveling' : 'ui.button_leveling');
        var info = this.player.getPawnLevelingQueues().find(function (m) { return m.puid === pawn.uid; });
        if (info) {
            need.Child('buttons').Swih('uplving')[0].Child('val').setLocaleKey(info.surplusTime > 0 ? 'ui.uplving' : 'ui.queueing');
        }
        else {
            need.Child('buttons').Swih('drill_be');
        }
    };
    BuildDrillgroundPnlCtrl.prototype.getDrillState = function () {
        var _a;
        var pawn = this.selectPawn;
        if (!pawn) {
            return assetsMgr.lang('toast.please_select_ceir_item', 'ui.ceri_type_name_2');
        }
        else if (pawn.isMachine()) {
            return 'ui.machine_nouplv';
        }
        else if (pawn.isMaxLv()) {
            return 'ui.maxlv1';
        }
        return GameHelper_1.gameHpr.checkNeedBuildLvCond((_a = pawn.attrJson) === null || _a === void 0 ? void 0 : _a.lv_cond) || '';
    };
    BuildDrillgroundPnlCtrl.prototype.getPawnMainAtts = function (currAttrJson, nextAttrJson) {
        return [
            { type: 1, val: currAttrJson.hp, next: nextAttrJson.hp },
            { type: 2, val: currAttrJson.attack, next: nextAttrJson.attack }
        ];
    };
    BuildDrillgroundPnlCtrl.prototype.getPawnSkills = function (currAttrJson, nextAttrJson) {
        var arr = [];
        var currs = ut.stringToNumbers(currAttrJson.skill), nexts = ut.stringToNumbers(nextAttrJson.skill);
        currs.forEach(function (curr, i) {
            var next = nexts[i];
            if (next && curr !== next) {
                var currSkill = assetsMgr.getJsonData('pawnSkill', curr), nextSkill = assetsMgr.getJsonData('pawnSkill', next);
                var currVal = currSkill.desc_params.split('|')[0], nextVal = nextSkill.desc_params.split('|')[0];
                if (nextVal && nextVal !== currVal) {
                    arr.push({ name: currSkill.name, val: currVal, next: nextVal });
                }
            }
        });
        return arr;
    };
    // 刷新训练列表
    BuildDrillgroundPnlCtrl.prototype.updateDrillQueue = function (node) {
        var _a, _b, _c, _d;
        node = node || this.pagesNode_.Child(1);
        var list = this.player.getPawnLevelingQueues();
        var isBattleing = GameHelper_1.gameHpr.isBattleingByIndex(this.data.aIndex);
        list.sort(function (a, b) { return b.surplusTime - a.surplusTime; });
        var area = this.areaCenter.getArea(this.data.aIndex);
        var time = 0;
        // 是否有政策的加成
        var queueCount = 6 + GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.LV_UP_QUEUE);
        node.Child('drill/title/bg/limit', cc.Label).string = '(' + list.length + '/' + queueCount + ')';
        for (var i = 0; i < queueCount; i++) {
            var it = null, data = list[i];
            if (i === 0) {
                it = node.Child('drill/content/' + i);
            }
            else {
                var childrenCount = this.queueSv_.content.childrenCount;
                if (childrenCount <= 1 || childrenCount < queueCount - 1) {
                    this.queueSv_.Items(queueCount - 1, function () { });
                }
                it = this.queueSv_.content.children[i - 1];
            }
            it.Data = data;
            var has = it.Child('icon').active = it.Child('drill_pawn_be').active = !!data;
            (_a = it.Child('cancel_drill_be')) === null || _a === void 0 ? void 0 : _a.setActive(has);
            (_b = it.Child('icon/progress')) === null || _b === void 0 ? void 0 : _b.setActive(has);
            var skinId = data ? (((_c = area === null || area === void 0 ? void 0 : area.getPawnByPrecise(data.auid, data.puid)) === null || _c === void 0 ? void 0 : _c.getViewId()) || data.id) : 0;
            if (data) {
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, it.Child('icon'), this.key);
            }
            if (i !== 0) {
                time += (data === null || data === void 0 ? void 0 : data.needTime) || 0;
            }
            else if (data) {
                var progress = it.Child('icon/progress', cc.Sprite);
                ResHelper_1.resHelper.loadPawnHeadIcon(skinId, progress, this.key);
                var stime = isBattleing ? data.surplusTime : data.getSurplusTime();
                time += stime;
                (_d = this.drillProgressTween[i]) === null || _d === void 0 ? void 0 : _d.stop();
                this.drillProgressTween[i] = null;
                progress.fillRange = stime / data.needTime;
                if (isBattleing) {
                    it.Child('time', cc.LabelTimer).string = ut.millisecondFormat(stime, 'm:ss');
                }
                else {
                    var st = stime * 0.001;
                    it.Child('time', cc.LabelTimer).run(st);
                    this.drillProgressTween[i] = cc.tween(progress).to(st, { fillRange: 0 }).start();
                }
            }
            else {
                it.Child('time', cc.LabelTimer).string = '';
            }
        }
        node.Child('drill/desc').active = time > 0;
        if (time > 0) {
            node.Child('drill/desc/title').setLocaleKey(isBattleing ? 'ui.battleing_stop_leveling' : 'ui.leveling_all_desc');
            if (node.Child('drill/desc/time').active = !isBattleing) {
                node.Child('drill/desc/time/val', cc.LabelTimer).run(time * 0.001);
            }
        }
    };
    // 取消训练
    BuildDrillgroundPnlCtrl.prototype.cancelLving = function (info) {
        var index = info.index;
        var uid = info.uid;
        var id = info.id;
        var lv = info.lv;
        NetHelper_1.netHelper.reqCancelPawnLving({ index: index, uid: uid }).then(function (res) {
            var _a;
            if (res.err) {
                return ViewHelper_1.viewHelper.showAlert(res.err);
            }
            else {
                var data = res.data;
                GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.cost);
                GameHelper_1.gameHpr.player.updatePawnLevelingQueue(data.queues);
                GameHelper_1.gameHpr.delMessageByTag(uid);
                if ((_a = data.needCost) === null || _a === void 0 ? void 0 : _a.length) {
                    ViewHelper_1.viewHelper.showPnl('common/CancelDrill', { text: 'ui.cancel_lving_tip', id: id, cost: data.needCost });
                }
            }
        });
    };
    BuildDrillgroundPnlCtrl = __decorate([
        ccclass
    ], BuildDrillgroundPnlCtrl);
    return BuildDrillgroundPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildDrillgroundPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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