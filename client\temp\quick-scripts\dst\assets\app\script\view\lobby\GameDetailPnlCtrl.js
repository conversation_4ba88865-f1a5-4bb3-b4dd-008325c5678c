
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/GameDetailPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '72c74YviSJBqJmIuCW5ca1/', 'GameDetailPnlCtrl');
// app/script/view/lobby/GameDetailPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var GameDetailPnlCtrl = /** @class */ (function (_super) {
    __extends(GameDetailPnlCtrl, _super);
    function GameDetailPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/layout/title_l
        _this.contentNode_ = null; // path://root/content_n
        return _this;
    }
    //@end
    GameDetailPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GameDetailPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    GameDetailPnlCtrl.prototype.onEnter = function (data) {
        var _a = GameHelper_1.gameHpr.getServerNameById(data.id), key = _a.key, id = _a.id;
        this.titleLbl_.setLocaleKey(key, id);
        this.updateInfo(data);
    };
    GameDetailPnlCtrl.prototype.onRemove = function () {
    };
    GameDetailPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/info/share_be
    GameDetailPnlCtrl.prototype.onClickShare = function (event, data) {
        cc.log('onClickShare', data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    GameDetailPnlCtrl.prototype.updateInfo = function (info) {
        var _this = this;
        var _a, _b, _c;
        var infoNode = this.contentNode_.Child('info'), total = this.contentNode_.Child('total/content'), members = this.contentNode_.Child('members'), sv = members.Child('list', cc.ScrollView);
        // 头像
        if (info.winType === 1) {
            ResHelper_1.resHelper.loadPlayerHead(infoNode.Child('head/val'), (_a = info.winners[0]) === null || _a === void 0 ? void 0 : _a.headicon, this.key);
        }
        else if (info.winType === 2) {
            ResHelper_1.resHelper.loadAlliIcon(((_b = info.winAlliInfo) === null || _b === void 0 ? void 0 : _b.headicon) || 0, infoNode.Child('head/val'), this.key);
        }
        infoNode.Child('name/val', cc.Label).string = info.winName;
        //
        var sumScore = 0, sumKillPawn = 0, sumDeadPawn = 0, sumOccupyCount = 0;
        info.winners.forEach(function (m) {
            sumScore += m.score;
            sumKillPawn += m.killPawn;
            sumDeadPawn += m.deadPawn;
            sumOccupyCount += m.occupyCount;
        });
        // 总积分
        infoNode.Child('score/score/val', cc.Label).string = sumScore + '';
        // 成员数量
        var memberCountLbl = members.Child('title/0/count', cc.Label), memberLbl = members.Child('title/0', cc.Label);
        memberCountLbl.string = '(' + (((_c = info.winners) === null || _c === void 0 ? void 0 : _c.length) || 0) + ')';
        memberLbl._forceUpdateRenderData();
        memberCountLbl.node.x = memberLbl.node.width * 0.5 + 8;
        //
        total.Child('0', cc.Label).string = info.landCount + '';
        total.Child('1', cc.Label).string = sumKillPawn + '';
        total.Child('2', cc.Label).string = sumDeadPawn + '';
        total.Child('3', cc.Label).string = sumOccupyCount + '';
        // 成员统计
        sv.stopAutoScroll();
        sv.content.y = 0;
        info.winners.sort(function (a, b) { return b.score - a.score; });
        var len = info.winners.length;
        sv.List(len || 0, function (it, i) {
            var data = it.Data = info.winners[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val'), data.headicon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.name, 9);
            it.Child('score', cc.Label).string = data.score + '';
            it.Child('occupy', cc.Label).string = data.occupyCount + '';
            it.Child('line').active = i < len - 1;
        });
    };
    GameDetailPnlCtrl = __decorate([
        ccclass
    ], GameDetailPnlCtrl);
    return GameDetailPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GameDetailPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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