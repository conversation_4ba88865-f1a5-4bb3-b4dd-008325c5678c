
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/SBuildCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '763feKheUhNTbVqi/8Q5yDO', 'SBuildCmpt');
// app/script/view/lobby/SBuildCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var SBuildCmpt = /** @class */ (function (_super) {
    __extends(SBuildCmpt, _super);
    function SBuildCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.id = 0;
        _this.bodyNode = null;
        _this.anim = null;
        _this.soul = null; //灵魂
        _this.preAnimation = '';
        return _this;
    }
    SBuildCmpt.prototype.onLoad = function () {
        this.load();
    };
    SBuildCmpt.prototype.load = function () {
        if (!this.bodyNode) {
            this.bodyNode = this.FindChild('body');
            this.anim = this.getComponent(cc.Animation);
        }
    };
    SBuildCmpt.prototype.init = function (soul) {
        this.load();
        this.id = soul.id;
        this.node.opacity = 255;
        this.node.Data = soul.uid;
        this.soul = soul;
        this.updateAnimation();
        return this;
    };
    // 更新动画
    SBuildCmpt.prototype.updateAnimation = function () {
        if (this.soul.anim !== this.preAnimation) {
            this.preAnimation = this.soul.anim;
            if (this.preAnimation) {
                this.anim.play(this.preAnimation);
            }
        }
    };
    SBuildCmpt.prototype.update = function (dt) {
        if (!this.soul) {
            return;
        }
        else {
            this.updateAnimation();
        }
    };
    SBuildCmpt = __decorate([
        ccclass
    ], SBuildCmpt);
    return SBuildCmpt;
}(cc.Component));
exports.default = SBuildCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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