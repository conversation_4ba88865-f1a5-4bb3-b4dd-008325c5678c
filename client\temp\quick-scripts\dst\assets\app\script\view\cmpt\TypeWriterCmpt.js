
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/TypeWriterCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3c9a0pWNiVFhocgCywAMl4Y', 'TypeWriterCmpt');
// app/script/view/cmpt/TypeWriterCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 打字机
var TypeWriterCmpt = /** @class */ (function (_super) {
    __extends(TypeWriterCmpt, _super);
    function TypeWriterCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.label = null;
        _this.interval = 0.075;
        _this.elapsed = 0;
        _this.running = false;
        _this.fullContent = '';
        _this.seq = 0;
        _this.completeCb = null;
        _this.soundPath = '';
        return _this;
    }
    TypeWriterCmpt.prototype.onLoad = function () {
        this.label = this.getComponent(cc.Label);
        this.updateFont();
    };
    TypeWriterCmpt.prototype.run = function (content, interval, params) {
        return __awaiter(this, void 0, void 0, function () {
            var soundTag, startMap;
            var _this = this;
            return __generator(this, function (_a) {
                this.clean();
                this.fullContent = assetsMgr.lang(content, params);
                soundTag = "";
                startMap = {
                    'guideText.dialog_100010': true,
                    'guideText.dialog_100060': true,
                    'guideText.dialog_110020': true,
                    'guideText.dialog_120101': true,
                };
                if (startMap[content]) {
                    soundTag = "_start";
                }
                else {
                    while (true) {
                        soundTag = '_' + ut.random(1, 3);
                        if ('novice/sound_ui_022' + soundTag !== this.soundPath) {
                            break;
                        }
                    }
                }
                this.soundPath = 'novice/sound_ui_022' + soundTag;
                audioMgr.playSFX(this.soundPath, { loop: false, tag: 'guide_talk' });
                this.elapsed = this.interval = interval !== null && interval !== void 0 ? interval : 0.075;
                if (this.interval === 0 || this.fullContent === '') {
                    return [2 /*return*/, this.stop()];
                }
                this.running = true;
                return [2 /*return*/, new Promise(function (resolve) { return _this.completeCb = resolve; })];
            });
        });
    };
    TypeWriterCmpt.prototype.clean = function () {
        audioMgr.stopSFX(this.soundPath, 'guide_talk');
        this.running = false;
        this.completeCb = null;
        this.seq = 0;
        this.label.string = '';
        // this.elapsed = 0
        // this.fullContent = ''
    };
    TypeWriterCmpt.prototype.stop = function () {
        audioMgr.fadeOutSFX(0.1, this.soundPath, 'guide_talk');
        this.running = false;
        this.label.string = this.fullContent;
        this.completeCb && this.completeCb();
        this.completeCb = null;
    };
    TypeWriterCmpt.prototype.updateFont = function () {
        this.label.font = assetsMgr.getFont('f_m');
    };
    TypeWriterCmpt.prototype.update = function (dt) {
        if (!this.running) {
            return;
        }
        this.elapsed += dt;
        if (this.elapsed < this.interval) {
            return;
        }
        this.elapsed -= this.interval;
        this.seq += 1;
        if (this.seq === this.fullContent.length) {
            this.stop();
        }
        else {
            this.label.string = this.fullContent.substring(0, this.seq);
        }
    };
    TypeWriterCmpt = __decorate([
        ccclass
    ], TypeWriterCmpt);
    return TypeWriterCmpt;
}(cc.Component));
exports.default = TypeWriterCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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