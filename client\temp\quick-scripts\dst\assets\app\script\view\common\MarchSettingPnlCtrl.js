
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/MarchSettingPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4a7a0S1Y+9NjYA1PjOK2vAl', 'MarchSettingPnlCtrl');
// app/script/view/common/MarchSettingPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var MarchSettingPnlCtrl = /** @class */ (function (_super) {
    __extends(MarchSettingPnlCtrl, _super);
    function MarchSettingPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        //@end
        _this.progressMap = {};
        return _this;
    }
    MarchSettingPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MarchSettingPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    MarchSettingPnlCtrl.prototype.onEnter = function (data) {
        var _this = this;
        this.progressMap = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.SET_MARCH_LINE_OPACITY) || {};
        this.contentNode_.children.forEach(function (m) {
            var _a;
            var progress = (_a = _this.progressMap[m.name]) !== null && _a !== void 0 ? _a : 100;
            m.Child('slider_se', cc.Slider).progress = progress * 0.01;
            _this.updateSlider(m, progress);
        });
    };
    MarchSettingPnlCtrl.prototype.onRemove = function () {
        GameHelper_1.gameHpr.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.SET_MARCH_LINE_OPACITY, this.progressMap);
        GameHelper_1.gameHpr.world.setAllMarchOpacity(this.progressMap);
    };
    MarchSettingPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/content_n/0/slider_se
    MarchSettingPnlCtrl.prototype.onClickSlider = function (event, data) {
        var node = event.node.parent, progress = Math.floor(event.progress * 100);
        if (this.progressMap[node.name] !== progress) {
            this.progressMap[node.name] = progress;
            this.updateSlider(event.node.parent, progress);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MarchSettingPnlCtrl.prototype.updateSlider = function (node, progress) {
        var slider = node.Child('slider_se');
        slider.Child('Background/val').width = progress * 0.01 * slider.width;
        node.Child('text/val', cc.Label).string = progress + '';
    };
    MarchSettingPnlCtrl = __decorate([
        ccclass
    ], MarchSettingPnlCtrl);
    return MarchSettingPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MarchSettingPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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