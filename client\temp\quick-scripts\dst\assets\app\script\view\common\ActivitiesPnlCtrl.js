
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ActivitiesPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '31851+dQxhO0ZKoxG7k60WF', 'ActivitiesPnlCtrl');
// app/script/view/common/ActivitiesPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ActivitiesPnlCtrl = /** @class */ (function (_super) {
    __extends(ActivitiesPnlCtrl, _super);
    function ActivitiesPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.activitiesSv_ = null; // path://root/activities_sv
        //@end
        // 活动配置
        _this.ACTIVITIES_CONFIG = [
            {
                id: 1,
                type: 302,
                condType: Enums_1.TCType.SIGN_DAY_COUNT,
                img: 'banner_2',
                title: 'ui.title_daily_sign_in',
                desc: 'ui.desc_daily_sign_in',
                time: '',
                view: 'menu/CLoginInfo',
                reddotFunc: function () { return _this.task.checkDailySignInTaskState(); },
                checkFunc: function () { return _this.checkDailySignIn(); },
            },
            {
                id: 2,
                type: 303,
                condType: Enums_1.TCType.RECHARGE_COUNT,
                img: 'banner_3',
                title: 'ui.title_first_pay_sale',
                desc: 'ui.desc_first_pay_sale',
                maxDescWidth: 202,
                time: '',
                view: 'common/FirstPaySale',
                reddotFunc: function () { return _this.task.checkFirstPaySaleTaskState(); },
                checkFunc: function () { return _this.checkFirstPaySale(); },
            },
            {
                id: 3,
                img: 'banner_4',
                title: 'ui.title_attentive_courteous',
                desc: 'ui.attentive_courteous_desc',
                time: '',
                view: 'lobby/FollowDC',
                reddotFunc: function () { return _this.checkFollowDC(); },
                checkFunc: function () { return _this.checkFollowDC(); },
            }
        ];
        _this.task = null;
        return _this;
    }
    ActivitiesPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ACITIVITIES] = this.onUpdateActivities, _a),
        ];
    };
    ActivitiesPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.task = this.getModel('task');
                return [2 /*return*/];
            });
        });
    };
    ActivitiesPnlCtrl.prototype.onEnter = function (data) {
        this.initActiviies();
    };
    ActivitiesPnlCtrl.prototype.onRemove = function () {
    };
    ActivitiesPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/activities_sv/view/content/item_be
    ActivitiesPnlCtrl.prototype.onClickItem = function (event, _data) {
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl(data.view);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    ActivitiesPnlCtrl.prototype.onUpdateActivities = function () {
        this.initActiviies();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ActivitiesPnlCtrl.prototype.initActiviies = function () {
        var _this = this;
        this.task.updateGeneralTaskState();
        var list = this.ACTIVITIES_CONFIG.filter(function (m) { return m.checkFunc(); });
        this.activitiesSv_.Items(list, function (it, data) {
            it.Data = data;
            it.Child('tip').Swih('dot')[0].active = data.reddotFunc();
            ResHelper_1.resHelper.loadActivtiesBanner(data.img, it.Child('bg'), _this.key);
            it.Child('title').setLocaleKey(data.title);
            var desc = assetsMgr.lang(data.desc), params = [];
            if (data.id === 1) {
                var item = _this.task.getGeneralTasks().find(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === data.condType; });
                if (item) {
                    params = [item.cond.count - 1];
                }
                desc = desc.replace('<color=#21DE29>', '<color=#16B31D><size=34>');
            }
            else if (data.id === 3) {
                it.Child('tip').Swih('new')[0].active = data.reddotFunc();
                desc = assetsMgr.lang(data.desc).replace('#3F332F', '#6180AA');
            }
            var descRt = it.Child('desc', cc.RichText);
            descRt.maxWidth = data.maxDescWidth || 240;
            descRt.setLocaleKey(desc, params);
            var time = it.Child('time');
            if (time.active = !!data.time) {
                var val = ut.timediff(Date.now(), data.time);
                time.Child('val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToString(val));
            }
        });
    };
    ActivitiesPnlCtrl.prototype.checkDailySignIn = function () {
        var task = this.task.getGeneralTasks().find(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.SIGN_DAY_COUNT; });
        return !!task;
    };
    ActivitiesPnlCtrl.prototype.checkFirstPaySale = function () {
        var task = this.task.getGeneralTasks().find(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.RECHARGE_COUNT; });
        return !!task;
    };
    ActivitiesPnlCtrl.prototype.checkFollowDC = function () {
        return !GameHelper_1.gameHpr.user.getActivityRecord()[1];
    };
    ActivitiesPnlCtrl = __decorate([
        ccclass
    ], ActivitiesPnlCtrl);
    return ActivitiesPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ActivitiesPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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