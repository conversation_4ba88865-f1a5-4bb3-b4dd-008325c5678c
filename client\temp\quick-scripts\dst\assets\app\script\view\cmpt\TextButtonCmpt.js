
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/TextButtonCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7f930zlmPtBOZ7Rw+0CPNYQ', 'TextButtonCmpt');
// app/script/view/cmpt/TextButtonCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 文本按钮
var TextButtonCmpt = /** @class */ (function (_super) {
    __extends(TextButtonCmpt, _super);
    function TextButtonCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = '';
        _this.descNode = null;
        _this.preWidth = 0;
        _this.preScaleX = 0;
        return _this;
    }
    TextButtonCmpt.prototype.onLoad = function () {
        if (!this.descNode) {
            this.descNode = this.FindChild('desc');
            if (this.key) {
                this.setKey(this.key);
            }
        }
    };
    TextButtonCmpt.prototype.setKey = function (key) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        if (!this.descNode) {
            this.onLoad();
        }
        this.key = key;
        var descLbl = this.descNode.Component(cc.Label);
        descLbl.setLocaleKey.apply(descLbl, __spread([key], params));
        descLbl._forceUpdateRenderData();
        this.updateWidth();
    };
    TextButtonCmpt.prototype.updateWidth = function () {
        this.preScaleX = this.descNode.scaleX;
        this.preWidth = this.Child('line').width = this.node.width = this.descNode.width * this.preScaleX;
    };
    TextButtonCmpt.prototype.update = function (dt) {
        if (this.preWidth !== this.descNode.width || this.preScaleX !== this.descNode.scaleX) {
            this.updateWidth();
        }
    };
    __decorate([
        property
    ], TextButtonCmpt.prototype, "key", void 0);
    TextButtonCmpt = __decorate([
        ccclass
    ], TextButtonCmpt);
    return TextButtonCmpt;
}(cc.Component));
exports.default = TextButtonCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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