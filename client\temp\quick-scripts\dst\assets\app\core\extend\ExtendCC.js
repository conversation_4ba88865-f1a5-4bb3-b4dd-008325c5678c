
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendCC.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c951fafNy1DJbnVJhNAVgV/', 'ExtendCC');
// app/core/extend/ExtendCC.ts

cc.instantiate2 = function (item, parent) {
    if (!(item === null || item === void 0 ? void 0 : item.isValid) || !(parent === null || parent === void 0 ? void 0 : parent.isValid)) {
        return null;
    }
    var it = cc.instantiate(item);
    it.parent = parent instanceof cc.Component ? parent.node : parent;
    return it;
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxjb3JlXFxleHRlbmRcXEV4dGVuZENDLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUNBLEVBQUUsQ0FBQyxZQUFZLEdBQUcsVUFBVSxJQUF5QixFQUFFLE1BQThCO0lBQ2pGLElBQUksRUFBQyxJQUFJLGFBQUosSUFBSSx1QkFBSixJQUFJLENBQUUsT0FBTyxDQUFBLElBQUksRUFBQyxNQUFNLGFBQU4sTUFBTSx1QkFBTixNQUFNLENBQUUsT0FBTyxDQUFBLEVBQUU7UUFDcEMsT0FBTyxJQUFJLENBQUE7S0FDZDtJQUNELElBQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFZLENBQUE7SUFDMUMsRUFBRSxDQUFDLE1BQU0sR0FBRyxNQUFNLFlBQVksRUFBRSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFBO0lBQ2pFLE9BQU8sRUFBRSxDQUFBO0FBQ2IsQ0FBQyxDQUFBIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbmNjLmluc3RhbnRpYXRlMiA9IGZ1bmN0aW9uIChpdGVtOiBjYy5Ob2RlIHwgY2MuUHJlZmFiLCBwYXJlbnQ6IGNjLk5vZGUgfCBjYy5Db21wb25lbnQpIHtcclxuICAgIGlmICghaXRlbT8uaXNWYWxpZCB8fCAhcGFyZW50Py5pc1ZhbGlkKSB7XHJcbiAgICAgICAgcmV0dXJuIG51bGxcclxuICAgIH1cclxuICAgIGNvbnN0IGl0ID0gY2MuaW5zdGFudGlhdGUoaXRlbSkgYXMgY2MuTm9kZVxyXG4gICAgaXQucGFyZW50ID0gcGFyZW50IGluc3RhbmNlb2YgY2MuQ29tcG9uZW50ID8gcGFyZW50Lm5vZGUgOiBwYXJlbnRcclxuICAgIHJldHVybiBpdFxyXG59Il19