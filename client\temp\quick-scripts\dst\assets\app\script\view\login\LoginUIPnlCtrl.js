
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/login/LoginUIPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cc02637NpJB3JMwiQNOxfxx', 'LoginUIPnlCtrl');
// app/script/view/login/LoginUIPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var LoginUIPnlCtrl = /** @class */ (function (_super) {
    __extends(LoginUIPnlCtrl, _super);
    function LoginUIPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.feedbackNode_ = null; // path://feedback_be_n
        _this.privacyNode_ = null; // path://privacy_n
        return _this;
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    LoginUIPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[mc.Event.PNL_ENTER] = this.onPnlEnter, _a),
            (_b = {}, _b[mc.Event.PNL_LEAVE] = this.onPnlLeave, _b),
            (_c = {}, _c[EventType_1.default.SHOW_LOGIN_UI_PRIVACY] = this.onShowLoginUIPrivacy, _c),
        ];
    };
    LoginUIPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isMask: false, isAct: false });
                return [2 /*return*/];
            });
        });
    };
    LoginUIPnlCtrl.prototype.onEnter = function (data) {
        this.privacyNode_.active = false;
    };
    LoginUIPnlCtrl.prototype.onRemove = function () {
    };
    LoginUIPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://feedback_be_n
    LoginUIPnlCtrl.prototype.onClickFeedback = function (event, data) {
        ViewHelper_1.viewHelper.showFeedback();
    };
    // path://privacy_n/privacy_policy_be
    LoginUIPnlCtrl.prototype.onClickPrivacyPolicy = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getPrivacyPolicyUrl());
    };
    // path://privacy_n/user_agreement_be
    LoginUIPnlCtrl.prototype.onClickUserAgreement = function (event, data) {
        cc.sys.openURL(GameHelper_1.gameHpr.getUserAgreementUrl());
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    LoginUIPnlCtrl.prototype.onPnlEnter = function (ui) {
        if (ui.key === 'login/MaintainTip') {
            this.setOpacity(50);
        }
    };
    LoginUIPnlCtrl.prototype.onPnlLeave = function (ui) {
        if (ui.key === 'login/MaintainTip') {
            this.setOpacity(255);
        }
    };
    LoginUIPnlCtrl.prototype.onShowLoginUIPrivacy = function () {
        this.privacyNode_.active = true;
    };
    LoginUIPnlCtrl = __decorate([
        ccclass
    ], LoginUIPnlCtrl);
    return LoginUIPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LoginUIPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGxvZ2luXFxMb2dpblVJUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwwREFBcUQ7QUFDckQsNkRBQXlEO0FBQ3pELDZEQUE0RDtBQUVwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUdsQztJQUE0QyxrQ0FBYztJQUExRDtRQUFBLHFFQWlFQztRQS9ERywwQkFBMEI7UUFDbEIsbUJBQWEsR0FBWSxJQUFJLENBQUEsQ0FBQyx1QkFBdUI7UUFDckQsa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQyxtQkFBbUI7O1FBNER4RCxpSEFBaUg7SUFDckgsQ0FBQztJQTVERyxNQUFNO0lBRUMsd0NBQWUsR0FBdEI7O1FBQ0ksT0FBTztzQkFDRCxHQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxJQUFHLElBQUksQ0FBQyxVQUFVO3NCQUNyQyxHQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxJQUFHLElBQUksQ0FBQyxVQUFVO3NCQUNyQyxHQUFDLG1CQUFTLENBQUMscUJBQXFCLElBQUcsSUFBSSxDQUFDLG9CQUFvQjtTQUNqRSxDQUFBO0lBQ0wsQ0FBQztJQUVZLGlDQUFRLEdBQXJCOzs7Z0JBQ0ksSUFBSSxDQUFDLFFBQVEsQ0FBQyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsS0FBSyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUE7Ozs7S0FDakQ7SUFFTSxnQ0FBTyxHQUFkLFVBQWUsSUFBUztRQUNwQixJQUFJLENBQUMsWUFBWSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7SUFDcEMsQ0FBQztJQUVNLGlDQUFRLEdBQWY7SUFDQSxDQUFDO0lBRU0sZ0NBQU8sR0FBZDtJQUNBLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLHVCQUF1QjtJQUN2Qix3Q0FBZSxHQUFmLFVBQWdCLEtBQTBCLEVBQUUsSUFBWTtRQUNwRCx1QkFBVSxDQUFDLFlBQVksRUFBRSxDQUFBO0lBQzdCLENBQUM7SUFFRCxxQ0FBcUM7SUFDckMsNkNBQW9CLEdBQXBCLFVBQXFCLEtBQTBCLEVBQUUsSUFBWTtRQUN6RCxFQUFFLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxvQkFBTyxDQUFDLG1CQUFtQixFQUFFLENBQUMsQ0FBQTtJQUNqRCxDQUFDO0lBRUQscUNBQXFDO0lBQ3JDLDZDQUFvQixHQUFwQixVQUFxQixLQUEwQixFQUFFLElBQVk7UUFDekQsRUFBRSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsb0JBQU8sQ0FBQyxtQkFBbUIsRUFBRSxDQUFDLENBQUE7SUFDakQsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFekcsbUNBQVUsR0FBbEIsVUFBbUIsRUFBa0I7UUFDakMsSUFBSSxFQUFFLENBQUMsR0FBRyxLQUFLLG1CQUFtQixFQUFFO1lBQ2hDLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDLENBQUE7U0FDdEI7SUFDTCxDQUFDO0lBRU8sbUNBQVUsR0FBbEIsVUFBbUIsRUFBa0I7UUFDakMsSUFBSSxFQUFFLENBQUMsR0FBRyxLQUFLLG1CQUFtQixFQUFFO1lBQ2hDLElBQUksQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLENBQUE7U0FDdkI7SUFDTCxDQUFDO0lBRU8sNkNBQW9CLEdBQTVCO1FBQ0ksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBO0lBQ25DLENBQUM7SUEvRGdCLGNBQWM7UUFEbEMsT0FBTztPQUNhLGNBQWMsQ0FpRWxDO0lBQUQscUJBQUM7Q0FqRUQsQUFpRUMsQ0FqRTJDLEVBQUUsQ0FBQyxXQUFXLEdBaUV6RDtrQkFqRW9CLGNBQWMiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRXZlbnRUeXBlIGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvRXZlbnRUeXBlXCI7XHJcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XHJcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XHJcblxyXG5jb25zdCB7IGNjY2xhc3MgfSA9IGNjLl9kZWNvcmF0b3I7XHJcblxyXG5AY2NjbGFzc1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBMb2dpblVJUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcclxuXHJcbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxyXG4gICAgcHJpdmF0ZSBmZWVkYmFja05vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vZmVlZGJhY2tfYmVfblxyXG4gICAgcHJpdmF0ZSBwcml2YWN5Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9wcml2YWN5X25cclxuICAgIC8vQGVuZFxyXG5cclxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XHJcbiAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgeyBbbWMuRXZlbnQuUE5MX0VOVEVSXTogdGhpcy5vblBubEVudGVyIH0sXHJcbiAgICAgICAgICAgIHsgW21jLkV2ZW50LlBOTF9MRUFWRV06IHRoaXMub25QbmxMZWF2ZSB9LFxyXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuU0hPV19MT0dJTl9VSV9QUklWQUNZXTogdGhpcy5vblNob3dMb2dpblVJUHJpdmFjeSB9LFxyXG4gICAgICAgIF1cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XHJcbiAgICAgICAgdGhpcy5zZXRQYXJhbSh7IGlzTWFzazogZmFsc2UsIGlzQWN0OiBmYWxzZSB9KVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkVudGVyKGRhdGE6IGFueSkge1xyXG4gICAgICAgIHRoaXMucHJpdmFjeU5vZGVfLmFjdGl2ZSA9IGZhbHNlXHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uUmVtb3ZlKCkge1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvbkNsZWFuKCkge1xyXG4gICAgfVxyXG5cclxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAgICAvL0BhdXRvY29kZSBidXR0b24gbGlzdGVuZXJcclxuXHJcbiAgICAvLyBwYXRoOi8vZmVlZGJhY2tfYmVfblxyXG4gICAgb25DbGlja0ZlZWRiYWNrKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcclxuICAgICAgICB2aWV3SGVscGVyLnNob3dGZWVkYmFjaygpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gcGF0aDovL3ByaXZhY3lfbi9wcml2YWN5X3BvbGljeV9iZVxyXG4gICAgb25DbGlja1ByaXZhY3lQb2xpY3koZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG4gICAgICAgIGNjLnN5cy5vcGVuVVJMKGdhbWVIcHIuZ2V0UHJpdmFjeVBvbGljeVVybCgpKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIHBhdGg6Ly9wcml2YWN5X24vdXNlcl9hZ3JlZW1lbnRfYmVcclxuICAgIG9uQ2xpY2tVc2VyQWdyZWVtZW50KGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcclxuICAgICAgICBjYy5zeXMub3BlblVSTChnYW1lSHByLmdldFVzZXJBZ3JlZW1lbnRVcmwoKSlcclxuICAgIH1cclxuICAgIC8vQGVuZFxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbiAgICBwcml2YXRlIG9uUG5sRW50ZXIodWk6IG1jLkJhc2VQbmxDdHJsKSB7XHJcbiAgICAgICAgaWYgKHVpLmtleSA9PT0gJ2xvZ2luL01haW50YWluVGlwJykge1xyXG4gICAgICAgICAgICB0aGlzLnNldE9wYWNpdHkoNTApXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHByaXZhdGUgb25QbmxMZWF2ZSh1aTogbWMuQmFzZVBubEN0cmwpIHtcclxuICAgICAgICBpZiAodWkua2V5ID09PSAnbG9naW4vTWFpbnRhaW5UaXAnKSB7XHJcbiAgICAgICAgICAgIHRoaXMuc2V0T3BhY2l0eSgyNTUpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHByaXZhdGUgb25TaG93TG9naW5VSVByaXZhY3koKSB7XHJcbiAgICAgICAgdGhpcy5wcml2YWN5Tm9kZV8uYWN0aXZlID0gdHJ1ZVxyXG4gICAgfVxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxufVxyXG4iXX0=