
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaWindCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9ec9aBNxbRL7r2yUkdcDY7W', 'AreaWindCtrl');
// app/script/view/area/AreaWindCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var AnimHelper_1 = require("../../common/helper/AnimHelper");
var PawnCmpt_1 = require("./PawnCmpt");
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var MapTouchCmpt_1 = require("../cmpt/MapTouchCmpt");
var ClickTouchCmpt_1 = require("../cmpt/ClickTouchCmpt");
var NetEvent_1 = require("../../common/event/NetEvent");
var HPBarCmpt_1 = require("./HPBarCmpt");
var Constant_1 = require("../../common/constant/Constant");
var BaseBuildCmpt_1 = require("./BaseBuildCmpt");
var Enums_1 = require("../../common/constant/Enums");
var ECode_1 = require("../../common/constant/ECode");
var SearchCircle_1 = require("../../common/astar/SearchCircle");
var GameHelper_1 = require("../../common/helper/GameHelper");
var SelectCellCmpt_1 = require("../cmpt/SelectCellCmpt");
var NetHelper_1 = require("../../common/helper/NetHelper");
var GuideHelper_1 = require("../../common/helper/GuideHelper");
var ccclass = cc._decorator.ccclass;
var AreaWindCtrl = /** @class */ (function (_super) {
    __extends(AreaWindCtrl, _super);
    function AreaWindCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.mapNode_ = null; // path://root/map_n
        _this.gridNode_ = null; // path://root/map_n/grid_n
        _this.skillDiNode_ = null; // path://root/map_n/skill_di_n
        _this.buildNode_ = null; // path://root/map_n/build_n
        _this.selectPawnNode_ = null; // path://root/select_pawn_n
        _this.roleNode_ = null; // path://root/role_n
        _this.topLayerNode_ = null; // path://root/top_layer_n
        _this.editJiantouNode_ = null; // path://root/edit_jiantou_n
        _this.weakGuideNode_ = null; // path://root/weak_guide_n
        //@end
        _this.PAWN_SIZE = cc.v2(1, 1);
        _this.diNode = null;
        _this.maskNode = null;
        _this.touchCmpt = null;
        _this.model = null;
        _this.areaCenter = null;
        _this.centre = cc.v2();
        _this.preCameraZoomRatio = 0;
        _this.areaSize = cc.v2(); //战场大小
        _this.buildSize = cc.v2(); //建筑区域
        _this.areaActSize = cc.v2(); //战场的实际大小
        _this.borderSize = cc.v2(); //地图边框宽度
        _this.buildOrigin = cc.v2(); //建筑起点
        _this.walls = []; //城墙列表
        _this.alliFlags = []; //联盟旗帜
        _this.areaOutDecorate = []; //装饰
        _this.builds = []; //建筑列表
        _this.buildMap = {};
        _this.pawns = []; //士兵列表
        _this.pawnMap = {};
        _this.wallLvNode = null;
        _this.hpBar = null; //血条
        _this.currEditBuild = null; //当前编辑的建筑
        _this.currEditPawn = null; //当前编辑的士兵
        _this.searchCircle = null;
        _this.isPawnMoveing = false; //当前是否士兵移动中
        _this.editPawns = {}; //编辑过的士兵列表
        _this.tempSeasonType = 0;
        _this._temp_vec2_0 = cc.v2();
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        return _this;
        // update(dt: number) {
        //     if (!this.model?.active) {
        //         return
        //     }
        //     // 检测是否需要填充地图
        //     // this.checkUpdateMap()
        // }
        // private checkUpdateMap() {
        //     const point = mapHelper.getPointByPixel(cameraCtrl.getCentrePosition(), this._temp_vec2_2)
        //     if (!this.centre.equals(point) || this.preCameraZoomRatio !== cameraCtrl.zoomRatio) {
        //         this.updateMap(point)
        //     }
        // }
    }
    AreaWindCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.REENTER_AREA_WIND] = this.onReenterAreaWind, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.SHOW_BUILD_JIANTOU] = this.onShowBuildJiantou, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.LONG_PRESS_BUILD] = this.onLongPressBuild, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.MOVE_BUILD] = this.onMoveBuild, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.CLICK_EDIT_BUILD_MENU] = this.onClickEditBuildMenu, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.EDIT_PAWN_POS] = this.onEditPawnPos, _g.enter = true, _g),
            (_h = {}, _h[EventType_1.default.CLICK_EDIT_PAWN_MENU] = this.onClickEditPawnMenu, _h.enter = true, _h),
            (_j = {}, _j[EventType_1.default.ADD_BUILD] = this.onAddBuild, _j.enter = true, _j),
            (_k = {}, _k[EventType_1.default.REMOVE_BUILD] = this.onRemoveBuild, _k.enter = true, _k),
            (_l = {}, _l[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _l.enter = true, _l),
            (_m = {}, _m[EventType_1.default.UPDATE_BUILD_POINT] = this.onUpdateBuildPoint, _m.enter = true, _m),
            (_o = {}, _o[EventType_1.default.UPDATE_BUILDS] = this.onUpdateBuilds, _o.enter = true, _o),
            (_p = {}, _p[EventType_1.default.UPDATE_AREA_HP] = this.onUpdateAreaHp, _p.enter = true, _p),
            (_q = {}, _q[EventType_1.default.UPDATE_ANCIENT_INFO] = this.onUpdateAncientInfo, _q.enter = true, _q),
            (_r = {}, _r[EventType_1.default.ADD_ARMY] = this.onAddArmy, _r.enter = true, _r),
            (_s = {}, _s[EventType_1.default.ADD_PAWN] = this.onAddPawn, _s.enter = true, _s),
            (_t = {}, _t[EventType_1.default.REMOVE_ARMY] = this.onRemoveArmy, _t.enter = true, _t),
            (_u = {}, _u[EventType_1.default.UPDATE_ARMY] = this.onUpdateArmy, _u.enter = true, _u),
            (_v = {}, _v[EventType_1.default.UPDATE_ALL_ARMY] = this.onUpdateAllArmy, _v.enter = true, _v),
            (_w = {}, _w[EventType_1.default.REMOVE_PAWN] = this.onRemovePawn, _w.enter = true, _w),
            (_x = {}, _x[EventType_1.default.AREA_BATTLE_BEGIN] = this.onAreaBattleBegin, _x.enter = true, _x),
            (_y = {}, _y[EventType_1.default.AREA_BATTLE_END] = this.onAreaBattleEnd, _y.enter = true, _y),
            (_z = {}, _z[EventType_1.default.AREA_MAIN_HIT] = this.onAreaMainHit, _z.enter = true, _z),
            (_0 = {}, _0[EventType_1.default.PLAY_FLUTTER_HP] = this.onPlayFlutterHp, _0.enter = true, _0),
            (_1 = {}, _1[EventType_1.default.PLAY_FLUTTER_ANGER] = this.onPlayFlutterAnger, _1.enter = true, _1),
            (_2 = {}, _2[EventType_1.default.PLAY_BULLET_FLY] = this.onPlayBulletFly, _2.enter = true, _2),
            (_3 = {}, _3[EventType_1.default.PLAY_BATTLE_EFFECT] = this.onPlayBattleEffect, _3.enter = true, _3),
            (_4 = {}, _4[EventType_1.default.PLAY_BATTLE_SFX] = this.onPlayBattleSfx, _4.enter = true, _4),
            (_5 = {}, _5[EventType_1.default.PLAY_BATTLE_SCENE_SHAKE] = this.onPlayBattleSceneShake, _5.enter = true, _5),
            (_6 = {}, _6[EventType_1.default.FOCUS_PAWN] = this.onFocusPawn, _6.enter = true, _6),
            (_7 = {}, _7[EventType_1.default.UPDATE_BT_QUEUE] = this.onUpdateBtQueue, _7.enter = true, _7),
            (_8 = {}, _8[EventType_1.default.UPDATE_PAWN_DRILL_QUEUE] = this.onUpdatePawnDrillQueue, _8.enter = true, _8),
            (_9 = {}, _9[EventType_1.default.UPDATE_PAWN_LVING_QUEUE] = this.onUpdatePawnDrillQueue, _9.enter = true, _9),
            (_10 = {}, _10[EventType_1.default.UPDATE_PAWN_CURING_QUEUE] = this.onUpdatePawnDrillQueue, _10.enter = true, _10),
            (_11 = {}, _11[EventType_1.default.CHANGE_SHOW_ARMY_NAME] = this.onChangeShowArmyName, _11.enter = true, _11),
            (_12 = {}, _12[EventType_1.default.CHANGE_SHOW_PAWN_EQUIP] = this.onChangeShowPawnEquip, _12.enter = true, _12),
            (_13 = {}, _13[EventType_1.default.CHANGE_PAWN_EQUIP] = this.onChangePawnEquip, _13.enter = true, _13),
            (_14 = {}, _14[EventType_1.default.CHANGE_PAWN_SKIN] = this.onChangePawnSkin, _14.enter = true, _14),
            (_15 = {}, _15[EventType_1.default.CHANGE_PAWN_PORTRAYAL] = this.onChangePawnPortrayal, _15.enter = true, _15),
            (_16 = {}, _16[EventType_1.default.FORGE_EQUIP_BEGIN] = this.onForgeEquipBegin, _16.enter = true, _16),
            (_17 = {}, _17[EventType_1.default.FORGE_EQUIP_COMPLETE] = this.onForgeEquipComplete, _17.enter = true, _17),
            (_18 = {}, _18[EventType_1.default.WEAK_GUIDE_SHOW_NODE_CHOOSE] = this.onWeakGuideShowNodeChoose, _18.enter = true, _18),
        ];
    };
    AreaWindCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.setParam({ isClean: false });
                        this.areaCenter = this.getModel('areaCenter');
                        this.diNode = this.mapNode_.FindChild('di');
                        this.maskNode = this.mapNode_.FindChild('mask');
                        this.touchCmpt = this.FindChild('touch').addComponent(MapTouchCmpt_1.default);
                        this.selectPawnNode_.active = false;
                        this.editJiantouNode_.active = false;
                        this.gridNode_.active = false;
                        // 加载UI
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadPnl('area/AreaUI')];
                    case 1:
                        // 加载UI
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onReady = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var _c, range, count;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _c = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_b = (_a = GameHelper_1.gameHpr.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.index) !== null && _b !== void 0 ? _b : -1)];
                    case 1:
                        _c.model = _d.sent();
                        if (!this.model) {
                            return [2 /*return*/];
                        }
                        this.tempSeasonType = GameHelper_1.gameHpr.world.getSeasonType();
                        this.areaCenter.setLookArea(this.model);
                        // 区域大小
                        this.areaSize.set(this.model.areaSize);
                        this.buildSize.set(this.model.buildSize);
                        // 获取地图边框的宽度 至少都有2格
                        this.model.getBorderSize(this.borderSize);
                        // 重新计算地图的真实大小
                        this.borderSize.mul(2, this.areaActSize).addSelf(this.areaSize);
                        // 计算建筑的起点
                        this.model.buildOrigin.add(this.borderSize, this.buildOrigin);
                        range = GameHelper_1.gameHpr.world.getMaxTileRange(), count = (range.x * 2 + 1) * (range.y * 2 + 1);
                        this.diNode.Items(count);
                        // 初始化城墙
                        return [4 /*yield*/, this.initWall()];
                    case 2:
                        // 初始化城墙
                        _d.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.onEnter = function (reenter) {
        if (!this.model) {
            ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
            if (GameHelper_1.gameHpr.net.isConnected()) {
                ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.UNKNOWN);
            }
            return;
        }
        this.buildNode_.Data = true;
        this.topLayerNode_.Data = true;
        this.model.setActive(true);
        // 刷新宝箱红点
        this.model.updateTreasureReddot();
        // 设置中心位置
        this.areaActSize.mul(0.5, this.centre).subSelf(cc.v2(0.5, 0.5));
        // 初始化相机位置
        var zr = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO);
        CameraCtrl_1.cameraCtrl.init(MapHelper_1.mapHelper.getPixelByPoint(this.centre), this.areaActSize, cc.Vec2.ZERO, zr);
        // 绘制士兵
        this.initPawns();
        // 绘制建筑
        this.initBuilds();
        // 刷新地图
        this.updateMap(this.centre.floor());
        // UI
        if (reenter) {
            this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('area/AreaUI', this.model);
        }
        //
        this.touchCmpt.init(this.onClickMap.bind(this));
        //
        GameHelper_1.gameHpr.playAreaBgm(this.model.isBattleing());
    };
    AreaWindCtrl.prototype.onLeave = function () {
        GameHelper_1.gameHpr.user.setLocalPreferenceData(Enums_1.PreferenceKey.AREA_ZOOM_RATIO, CameraCtrl_1.cameraCtrl.zoomRatio);
        ViewHelper_1.viewHelper.hidePnl('area/AreaUI');
        this.touchCmpt.clean();
        GameHelper_1.gameHpr.world.setLookCell(null);
        this.clean();
        this.cleanPawns();
        ResHelper_1.resHelper.cleanNodeChildren(this.diNode);
        ResHelper_1.resHelper.cleanNodeChildren(this.maskNode);
        this.buildNode_.removeAllChildren();
        this.buildNode_.Data = false;
        this.topLayerNode_.removeAllChildren();
        this.topLayerNode_.Data = false;
        nodePoolMgr.cleanUseAndRemoveItemsByTag(this.key);
        AnimHelper_1.animHelper.clean();
        assetsMgr.releaseTempResByTag(this.key);
        audioMgr.releaseByMod('build');
        audioMgr.releaseByMod('pawn');
    };
    AreaWindCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AreaWindCtrl.prototype.onNetReconnect = function () {
        this.reinit();
    };
    AreaWindCtrl.prototype.onReenterAreaWind = function () {
        return this.reenter();
    };
    // 显示编辑家具的箭头
    AreaWindCtrl.prototype.onShowBuildJiantou = function (item, index) {
        this.editJiantouNode_.active = !!item;
        if (item) {
            this.editJiantouNode_.setPosition(item.getBodyOffsetTopPosition(68));
            this.editJiantouNode_.Component(cc.MultiFrame).setFrame(index);
        }
    };
    // 长按选中一个家具
    AreaWindCtrl.prototype.onLongPressBuild = function (item) {
        // 设置可以点击选择了
        this.builds.forEach(function (m) { return item.uid !== m.uid && m.setCanClickSelect(true); });
        this.pawns.forEach(function (m) { return m.setCanClick(false); });
        // 显示编辑UI
        this.openEditBuild(item);
    };
    // 移动建筑
    AreaWindCtrl.prototype.onMoveBuild = function (item, pos) {
        var point = item.getActPointByPixel(pos);
        this.model.amendBuildPoint(point, item.data.size); //修正一下
        item.setOffsetPositionByPoint(point);
        item.updateEditState(this.model.getBuildGroundPointMap(), point);
    };
    // 点击编辑建筑菜单
    AreaWindCtrl.prototype.onClickEditBuildMenu = function (type) {
        if (!this.currEditBuild || !this.currEditBuild.data /* || this.model.isBattleing() */) {
            return;
        }
        var item = this.currEditBuild;
        if (type === 'cancel') { //取消
            item.cancel();
        }
        else if (type === 'ok') { //确定
            if (item.editState) {
                return ViewHelper_1.viewHelper.showAlert(item.editState);
            }
            item.confirm(item.getActPointByPixel(item.getTempPosition()));
            // audioMgr.playSFX('area/sound06')
        }
        ViewHelper_1.viewHelper.hidePnl('area/EditBuild'); //隐藏编辑UI
        this.builds.forEach(function (m) { return m.setCanClickSelect(false); }); //关闭点击选择
        this.pawns.forEach(function (m) { return m.setCanClick(true); });
        item.syncZindex(); //同步zindex
        this.closeEditBuild();
    };
    // 编辑士兵
    AreaWindCtrl.prototype.onEditPawnPos = function (index, uid) {
        if (this.model.index !== index || this.model.isBattleing()) {
            return;
        }
        var pawn = this.pawns.find(function (m) { return m.uid === uid; });
        if (pawn) {
            this.builds.forEach(function (m) { return m.setCanClick(false); });
            this.pawns.forEach(function (m) { return m.setCanClick(false); });
            this.maskNode.children.forEach(function (m) { return m.active = !!m.Data; });
            // 行动中
            pawn.data.actioning = true;
            // 显示pnl
            ViewHelper_1.viewHelper.showPnl('area/EditPawn', pawn);
            this.currEditPawn = pawn;
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.currEditPawn.getTempPosition(), this.PAWN_SIZE);
        }
    };
    // 点击编辑士兵的菜单
    AreaWindCtrl.prototype.onClickEditPawnMenu = function (type) {
        if (!this.currEditPawn || !this.currEditPawn.data || this.model.isBattleing()) {
            ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
            this.builds.forEach(function (m) { return m.setCanClick(true); });
            this.pawns.forEach(function (m) { return m.setCanClick(true); });
            this.maskNode.children.forEach(function (m) { return m.active = false; });
            this.closeEditPawn();
        }
        else if (type === 'gather') { //集合
            this.gatherPawn();
        }
        else if (type === 'ok') {
            this.editPawnEnd();
        }
    };
    // 添加建筑
    AreaWindCtrl.prototype.onAddBuild = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var isOwner_1 = this.model.isOwner();
            this.createBuild(data).then(function (item) {
                if (item && _this.isActive() && isOwner_1) {
                    var body = item.getBody();
                    // 摄像机移动到这个位置来
                    CameraCtrl_1.cameraCtrl.setTargetOnce(body);
                    // 扫光
                    // animHelper.playFlashLight([body])
                }
            });
        }
    };
    // 删除建筑
    AreaWindCtrl.prototype.onRemoveBuild = function (data) {
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            var build = this.builds.remove('uid', data.uid);
            if (build) {
                this.cleanBuild(build);
                assetsMgr.releaseTempRes(data.getPrefabUrl(), this.key);
            }
        }
    };
    // 刷新建筑等级
    AreaWindCtrl.prototype.onUpdateBuildLv = function (data) {
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
        }
        else if (data.uid === this.model.wall.uid) {
            this.updateWallLv(data.lv);
        }
        else {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.updateLv(data.lv);
        }
    };
    // 刷新建筑位置
    AreaWindCtrl.prototype.onUpdateBuildPoint = function (data) {
        var _a, _b;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.builds.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.syncPoint();
        }
    };
    // 刷新血量
    AreaWindCtrl.prototype.onUpdateAreaHp = function (index) {
        var _a, _b;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        }
    };
    // 刷新遗迹信息
    AreaWindCtrl.prototype.onUpdateAncientInfo = function (data) {
        var _a;
        if (data.index !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        var build = this.builds.find(function (m) { return m.data.isAncient(); });
        if (build) {
            build.updateLv(data.lv);
            build.updateUpLvAnim();
        }
    };
    // 刷新城市
    AreaWindCtrl.prototype.onUpdateBuilds = function (index) {
        var _a;
        if (index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            this.initBuilds();
        }
    };
    // 添加军队
    AreaWindCtrl.prototype.onAddArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) { return _this.createPawn(m); });
        }
    };
    // 删除军队
    AreaWindCtrl.prototype.onRemoveArmy = function (data) {
        var _this = this;
        var _a;
        if (data.aIndex === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            data.pawns.forEach(function (m) {
                var i = _this.pawns.findIndex(function (p) { var _a; return p.uid === m.uid && ((_a = p.data) === null || _a === void 0 ? void 0 : _a.armyUid) === data.uid; });
                if (i !== -1) {
                    _this.cleanPawn(_this.pawns.splice(i, 1)[0], true);
                }
            });
        }
    };
    // 更新军队信息
    AreaWindCtrl.prototype.onUpdateArmy = function (data) {
        var _this = this;
        var _a, _b;
        if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            return;
        }
        // 先删除没有的
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            var m = this.pawns[i];
            if (((_b = m.data) === null || _b === void 0 ? void 0 : _b.armyUid) !== data.uid) {
                continue;
            }
            else if (!data.pawns.has('uid', m.uid)) {
                this.pawns.splice(i, 1);
                this.cleanPawn(m, true);
            }
        }
        data.pawns.forEach(function (m) { return _this.createPawn(m); });
    };
    // 更新所有军队
    AreaWindCtrl.prototype.onUpdateAllArmy = function (index) {
        if (this.model.index === index) {
            this.initPawns();
        }
    };
    // 添加士兵
    AreaWindCtrl.prototype.onAddPawn = function (index, data) {
        if (this.model.index === index) {
            this.createPawn(data);
        }
    };
    // 删除士兵
    AreaWindCtrl.prototype.onRemovePawn = function (index, uid) {
        if (this.model.index === index) {
            this.cleanPawn(this.pawns.remove('uid', uid), true);
        }
    };
    // 战斗开始
    AreaWindCtrl.prototype.onAreaBattleBegin = function (index) {
        var _a, _b;
        if (this.model.index !== index) {
            return;
        }
        // 关闭当前正在编辑的建筑
        this.checkConfirmEditBuild();
        this.closeEditBuild();
        // 关闭当前正在编辑的士兵
        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
        this.closeEditPawn();
        // 初始化血量
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        // 初始化士兵
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(true);
    };
    // 战斗结束
    AreaWindCtrl.prototype.onAreaBattleEnd = function (index) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== index) {
            return;
        }
        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
            return this.reenter();
        }
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.init(this.model);
        this.initBuilds(true);
        this.initPawns();
        // 战斗时间
        this.emit(EventType_1.default.UPDATE_AREA_BATTLE_TIME_UI, this.model.index);
        //
        GameHelper_1.gameHpr.playAreaBgm(false);
    };
    // 受到伤害
    AreaWindCtrl.prototype.onAreaMainHit = function (data) {
        var _a, _b;
        if (data.index === ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
            if (this.model.isBattleing()) {
                (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.play();
            }
            AnimHelper_1.animHelper.playFlutterHp({ type: 'isDamage', value: data.value }, this.topLayerNode_, this.getPixelByPoint(data.point), this.key);
        }
    };
    // 播放飘血
    AreaWindCtrl.prototype.onPlayFlutterHp = function (data) {
        var _a, _b;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pos = data.point ? this.getPixelByPoint(data.point).clone() : (_b = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _b === void 0 ? void 0 : _b.getPosition();
            if (pos) {
                AnimHelper_1.animHelper.readyPlayFlutterHp(data, pos, this.topLayerNode_, this.key);
            }
        }
    };
    // 播放增加怒气
    AreaWindCtrl.prototype.onPlayFlutterAnger = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
            if (pawn) {
                AnimHelper_1.animHelper.playFlutterAnger(data.value, this.topLayerNode_, pawn.getPosition(), this.key);
            }
        }
    };
    // 播放子弹飞行
    AreaWindCtrl.prototype.onPlayBulletFly = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.startPos = this.getPixelByPoint(data.startPoint).clone();
            data.targetPos = this.getPixelByPoint(data.targetPoint).clone();
            AnimHelper_1.animHelper.playBulletFly(data, this.topLayerNode_, this.key);
        }
    };
    // 播放战斗特效
    AreaWindCtrl.prototype.onPlayBattleEffect = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === data.index) {
            data.pos = this.getPixelByPoint(data.point).clone();
            var root = this.skillDiNode_;
            if (data.root === 'top') {
                root = this.topLayerNode_;
            }
            else if (data.root === 'role') {
                root = this.roleNode_;
                data.zIndex = (Constant_1.AREA_MAX_ZINDEX - (data.pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 3;
            }
            AnimHelper_1.animHelper.playBattleEffect(data, root, this.key);
        }
    };
    // 播放音效
    AreaWindCtrl.prototype.onPlayBattleSfx = function (index, url, data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            audioMgr.playSFX(url, data);
        }
    };
    // 播放屏幕抖动
    AreaWindCtrl.prototype.onPlayBattleSceneShake = function (index, time) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) === index) {
            CameraCtrl_1.cameraCtrl.shake(time);
        }
    };
    // 聚焦士兵
    AreaWindCtrl.prototype.onFocusPawn = function (data) {
        var _a;
        if (((_a = this.model) === null || _a === void 0 ? void 0 : _a.index) !== (data === null || data === void 0 ? void 0 : data.index)) {
            return;
        } /*  else if (cameraCtrl.getNoDragTime() < 5000) {
            return //多久没拖动才可以聚焦
        } */
        var pos = this.getPixelByPoint(data.point);
        if (CameraCtrl_1.cameraCtrl.isInScreenRangeByWorld(pos)) {
            CameraCtrl_1.cameraCtrl.moveTo(0.5, pos, true);
        }
    };
    // 刷新修建队列
    AreaWindCtrl.prototype.onUpdateBtQueue = function () {
        this.builds.forEach(function (m) { return m.updateUpLvAnim(); });
    };
    // 刷新训练队列
    AreaWindCtrl.prototype.onUpdatePawnDrillQueue = function (index) {
        if (this.model.index === index) {
            this.builds.forEach(function (m) { return m.updateDrillPawn(); });
        }
    };
    // 切换显示军队的名字
    AreaWindCtrl.prototype.onChangeShowArmyName = function (val) {
        this.pawns.forEach(function (m) { return m.showArmyName(val); });
    };
    // 切换显示士兵装备
    AreaWindCtrl.prototype.onChangeShowPawnEquip = function (val) {
        this.pawns.forEach(function (m) { return m.showPawnEquip(val); });
    };
    // 切换士兵装备
    AreaWindCtrl.prototype.onChangePawnEquip = function (data) {
        var _a;
        (_a = this.pawns.find(function (m) { return m.uid === data.uid; })) === null || _a === void 0 ? void 0 : _a.updateShowPawnEquip();
    };
    // 切换士兵皮肤
    AreaWindCtrl.prototype.onChangePawnSkin = function (data) {
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn = this.pawns[i];
            if (pawn.curSkinId !== data.skinId) {
                this.pawns.splice(i, 1);
                this.cleanPawn(pawn);
                this.createPawn(data);
            }
        }
        this.builds.forEach(function (m) { return m.updateDrillPawn(); });
    };
    // 化身英雄
    AreaWindCtrl.prototype.onChangePawnPortrayal = function (data) {
        var _this = this;
        if (!data.portrayal) {
            return;
        }
        var i = this.pawns.findIndex(function (m) { return m.uid === data.uid; });
        if (i !== -1) {
            var pawn_1 = this.pawns[i];
            if (pawn_1.curPortrayalId !== data.portrayal.id) {
                pawn_1.playAvatarHeroAnim(data.portrayal.id).then(function () {
                    if (_this.isActive()) {
                        _this.pawns.splice(i, 1);
                        _this.cleanPawn(pawn_1);
                        _this.createPawn(data);
                    }
                });
            }
        }
    };
    // 打造装备开始
    AreaWindCtrl.prototype.onForgeEquipBegin = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 打造装备完成
    AreaWindCtrl.prototype.onForgeEquipComplete = function () {
        var _a;
        (_a = this.builds.find(function (m) { return m.id === Constant_1.BUILD_SMITHY_NID; })) === null || _a === void 0 ? void 0 : _a.updateForgeEquip();
    };
    // 若引导
    AreaWindCtrl.prototype.onWeakGuideShowNodeChoose = function (data) {
        if (data.scene === 'area') {
            GuideHelper_1.guideHelper.playWeakGuideFinger(data, this.weakGuideNode_, this.key);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AreaWindCtrl.prototype.isActive = function () { var _a; return this.isValid && !!((_a = this.model) === null || _a === void 0 ? void 0 : _a.active); };
    AreaWindCtrl.prototype.getPixelByPoint = function (point) {
        return point && MapHelper_1.mapHelper.getPixelByPoint(point.add(this.borderSize, this._temp_vec2_3));
    };
    AreaWindCtrl.prototype.getPointByPixel = function (pixel) {
        return pixel && MapHelper_1.mapHelper.getPointByPixel(pixel).subSelf(this.borderSize);
    };
    AreaWindCtrl.prototype.getBuildPixelByPoint = function (point) {
        return MapHelper_1.mapHelper.getPixelByPoint(point.add(this.buildOrigin, this._temp_vec2_1), this._temp_vec2_1);
    };
    AreaWindCtrl.prototype.clean = function () {
        var _a;
        this.areaCenter.setLookArea(null);
        (_a = this.model) === null || _a === void 0 ? void 0 : _a.setActive(false);
        this.model = null;
        GameHelper_1.gameHpr.cleanPawnAstarMap();
        this.searchCircle = null;
        this.cleanWalls();
        this.cleanAlliFlags();
        this.cleanAreaOutDecorate();
        this.cheanBuilds();
        // this.cleanPawns()
        this.closeEditBuild();
        this.closeEditPawn();
    };
    // 重连之后的初始化
    AreaWindCtrl.prototype.reinit = function () {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function () {
            var world, _e;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        GameHelper_1.gameHpr.cleanPawnAstarMap();
                        // 关闭当前正在编辑的建筑
                        this.checkConfirmEditBuild();
                        this.closeEditBuild();
                        // 关闭当前正在编辑的士兵
                        (_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.cancel();
                        this.closeEditPawn();
                        world = GameHelper_1.gameHpr.world;
                        _e = this;
                        return [4 /*yield*/, this.areaCenter.reqAreaByIndex((_c = (_b = world.getLookCell()) === null || _b === void 0 ? void 0 : _b.index) !== null && _c !== void 0 ? _c : -1, true)];
                    case 1:
                        _e.model = _f.sent();
                        if (!this.model) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.gotoWind(world.getSceneKey())];
                        }
                        else if (!this.areaSize.equals(this.model.areaSize) || !this.buildSize.equals(this.model.buildSize)) { //如果大小不一样需要重新绘制
                            return [2 /*return*/, this.reenter()];
                        }
                        this.model.setActive(true);
                        // 刷新地图
                        this.updateMap(this.centre.floor());
                        // 城墙等级
                        this.model.wall && this.updateWallLv(this.model.wall.lv);
                        // 血条
                        (_d = this.hpBar) === null || _d === void 0 ? void 0 : _d.init(this.model);
                        // 绘制建筑
                        this.initBuilds();
                        // 绘制士兵
                        this.initPawns();
                        // 战斗时间
                        this.emit(EventType_1.default.REINIT_AREA_UI, this.model);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 重新绘制
    AreaWindCtrl.prototype.reenter = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.emit(mc.Event.READY_BEGIN_WIND);
                        this.clean();
                        return [4 /*yield*/, this.onReady()];
                    case 1:
                        _a.sent();
                        this.emit(mc.Event.READY_END_WIND);
                        this.onEnter(true);
                        this.emit(EventType_1.default.UPDATE_REENTER_AREA);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 初始化城墙
    AreaWindCtrl.prototype.initWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pfb, node, size;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanWalls();
                        if (!!this.model.isBoss()) return [3 /*break*/, 6];
                        return [4 /*yield*/, assetsMgr.loadTempRes('wall/WALL_HP_BAR', cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (!pfb || !this.isValid) {
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, this.roleNode_);
                        this.hpBar = node.addComponent(HPBarCmpt_1.default).init(this.model);
                        size = this.model.buildSize;
                        ViewHelper_1.viewHelper.drawGrid(this.gridNode_.Component(cc.Graphics), cc.v2(size.x - 2, size.y - 2), cc.v2(this.buildOrigin.x + 1, this.buildOrigin.y + 1));
                        this.gridNode_.active = false;
                        if (!this.model.wall) return [3 /*break*/, 5];
                        if (!!this.model.isAncient()) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.createWall()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        if (!(this.model.wall.lv > 0)) return [3 /*break*/, 5];
                        return [4 /*yield*/, assetsMgr.loadTempRes('build/BUILD_LV', cc.Prefab, this.key)];
                    case 4:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = this.wallLvNode = cc.instantiate2(pfb, this.roleNode_);
                            this.updateWallLv(this.model.wall.lv);
                        }
                        _a.label = 5;
                    case 5:
                        this.updateWallHpPosition();
                        _a.label = 6;
                    case 6: 
                    // 创建区域外的装饰
                    return [4 /*yield*/, this.createAreaOutDecorate()
                        // 创建联盟旗帜
                    ];
                    case 7:
                        // 创建区域外的装饰
                        _a.sent();
                        // 创建联盟旗帜
                        return [4 /*yield*/, this.createAlliFlags(GameHelper_1.gameHpr.getPlayerAlliIcon(this.model.owner))];
                    case 8:
                        // 创建联盟旗帜
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createWall = function () {
        return __awaiter(this, void 0, void 0, function () {
            var skinId;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        while (this.walls.length > 0) {
                            this.walls.pop().destroy();
                        }
                        this.walls.length = 0;
                        skinId = 0;
                        return [4 /*yield*/, Promise.all(this.model.walls.map(function (m) { return __awaiter(_this, void 0, void 0, function () {
                                var url, pfb, node;
                                return __generator(this, function (_a) {
                                    switch (_a.label) {
                                        case 0:
                                            url = "wall/" + skinId + "/WALL_" + skinId + "_" + m.type + "_" + m.dir;
                                            if (m.index) {
                                                url += '_' + m.index;
                                            }
                                            return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                                        case 1:
                                            pfb = _a.sent();
                                            if (pfb && this.isValid) {
                                                node = cc.instantiate2(pfb, this.buildNode_);
                                                node.Data = this.model.wall;
                                                node.setPosition(MapHelper_1.mapHelper.getPixelByPoint(m.point.add(this.buildOrigin, this._temp_vec2_1)));
                                                node.addComponent(ClickTouchCmpt_1.default).on(this.onClickWall, this);
                                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                                this.walls.push(node);
                                            }
                                            return [2 /*return*/];
                                    }
                                });
                            }); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanWalls = function () {
        var _a, _b;
        (_a = this.wallLvNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.wallLvNode = null;
        (_b = this.hpBar) === null || _b === void 0 ? void 0 : _b.clean();
        this.hpBar = null;
        while (this.walls.length > 0) {
            this.walls.pop().destroy();
        }
        this.walls.length = 0;
    };
    // 创建联盟旗帜
    AreaWindCtrl.prototype.createAlliFlags = function (icon) {
        return __awaiter(this, void 0, void 0, function () {
            var points, pfb;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!icon) {
                            return [2 /*return*/, this.cleanAlliFlags()];
                        }
                        points = this.model.alliFlags;
                        if (this.alliFlags.length === points.length && this.alliFlags[0].Data === icon) {
                            return [2 /*return*/, points.forEach(function (point, i) {
                                    var node = _this.alliFlags[i], pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                    node.setPosition(pos);
                                    node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                })];
                        }
                        this.cleanAlliFlags();
                        return [4 /*yield*/, assetsMgr.loadTempRes('alli_flag/ALLI_FLAG_' + icon, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            points.forEach(function (point) {
                                var node = cc.instantiate2(pfb, _this.roleNode_), pos = MapHelper_1.mapHelper.getPixelByPoint(point.add(_this.borderSize, _this._temp_vec2_1));
                                node.Child('body').y = -28;
                                node.setPosition(pos);
                                node.zIndex = Constant_1.AREA_MAX_ZINDEX - node.y;
                                _this.alliFlags.push(node);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAlliFlags = function () {
        while (this.alliFlags.length > 0) {
            this.alliFlags.pop().destroy();
        }
        this.alliFlags.length = 0;
    };
    // 创建区域外的装饰
    AreaWindCtrl.prototype.createAreaOutDecorate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var seasonType, cell, drawType, url, pfb, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.cleanAreaOutDecorate();
                        seasonType = this.tempSeasonType;
                        cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), drawType = cell.getLandDrawType();
                        url = "area_decorate/" + seasonType + "/AREA_DECORATE_" + drawType;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.Prefab, this.key)];
                    case 1:
                        pfb = _a.sent();
                        if (pfb && this.isValid) {
                            node = cc.instantiate2(pfb, this.mapNode_.Child('bg'));
                            node.setPosition(this.borderSize.x * Constant_1.TILE_SIZE, this.borderSize.y * Constant_1.TILE_SIZE);
                            this.areaOutDecorate.push(node);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanAreaOutDecorate = function () {
        while (this.areaOutDecorate.length > 0) {
            this.areaOutDecorate.pop().destroy();
        }
        this.areaOutDecorate.length = 0;
    };
    // 初始化建筑
    AreaWindCtrl.prototype.initBuilds = function (playOccupyEffect) {
        return __awaiter(this, void 0, void 0, function () {
            var builds, i;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.buildMap = {};
                        builds = this.model.builds;
                        builds.forEach(function (m) { return _this.buildMap[m.uid] = 1; });
                        for (i = this.builds.length - 1; i >= 0; i--) {
                            if (!this.buildMap[this.builds[i].uid]) {
                                this.cleanBuild(this.builds.splice(i, 1)[0]);
                            }
                        }
                        return [4 /*yield*/, Promise.all(builds.map(function (m) { return _this.createBuild(m); }))];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl.prototype.createBuild = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var build, pfb;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        build = this.builds.find(function (m) { return m.uid === data.uid; });
                        if (build) {
                            this.buildMap[data.uid] = 2;
                            return [2 /*return*/, build.resync(data, this.model.owner)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.buildMap[data.uid] === 2) {
                            return [2 /*return*/, null]; //防止重复创建或创建没有的
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        this.buildMap[data.uid] = 2;
                        build = cc.instantiate2(pfb, this.roleNode_).getComponent(BaseBuildCmpt_1.default).init(data, this.buildOrigin, this.borderSize.y * Constant_1.TILE_SIZE, this.model.owner);
                        this.builds.push(build);
                        return [2 /*return*/, build];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cheanBuilds = function () {
        while (this.builds.length > 0) {
            this.builds.pop().clean();
        }
        this.buildMap = {};
    };
    AreaWindCtrl.prototype.cleanBuild = function (data) {
        if (data) {
            data.clean();
            delete this.buildMap[data.uid];
        }
    };
    // 刷新墙的等级
    AreaWindCtrl.prototype.updateWallLv = function (lv) {
        if (this.wallLvNode) {
            this.wallLvNode.Child('val', cc.Label).string = '' + lv;
        }
    };
    // 刷新血条位置
    AreaWindCtrl.prototype.updateWallHpPosition = function () {
        if (this.hpBar) {
            var node = this.hpBar.node, pos = cc.v2();
            if (this.model.cityId === Constant_1.CITY_MAIN_NID) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + 25);
            }
            else if (this.model.isAncient()) {
                pos = MapHelper_1.mapHelper.getPixelByPoint(cc.v2(3, 0).addSelf(this.buildOrigin));
                node.setPosition(pos.x, pos.y + (this.model.cityId === Constant_1.CITY_LUOYANG_ID ? 105 : 25));
            }
            else {
                pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
                node.setPosition(pos.x, pos.y + 47);
            }
            node.zIndex = (Constant_1.AREA_MAX_ZINDEX - (pos.y - this.borderSize.y * Constant_1.TILE_SIZE)) * 10 + 1;
        }
        if (this.wallLvNode) {
            var pos = MapHelper_1.mapHelper.getPixelByPoint(this.buildOrigin);
            this.wallLvNode.setPosition(pos.x, pos.y + 16);
            this.wallLvNode.zIndex = this.hpBar.node.zIndex + 1;
        }
    };
    // 打开编辑建筑
    AreaWindCtrl.prototype.openEditBuild = function (item) {
        // 显示pnl
        ViewHelper_1.viewHelper.showPnl('area/EditBuild');
        this.gridNode_.active = true;
        // 如果之前有就放下
        this.checkConfirmEditBuild();
        this.currEditBuild = item;
        // 刷新一下地面点
        this.model.updateBuildGroundPoints(item.data);
        // 刷新编辑状态
        item.updateEditState(this.model.getBuildGroundPointMap(), item.point);
    };
    // 如果有建筑在编辑状态 就放下
    AreaWindCtrl.prototype.checkConfirmEditBuild = function () {
        if (!this.isEditBuildState()) {
        }
        else if (this.currEditBuild.editState) {
            this.currEditBuild.cancel();
        }
        else {
            this.currEditBuild.confirm(this.currEditBuild.getActPointByPixel(this.currEditBuild.getTempPosition()));
        }
    };
    // 关闭编辑建筑
    AreaWindCtrl.prototype.closeEditBuild = function () {
        this.currEditBuild = null;
        this.editJiantouNode_.active = false;
        this.gridNode_.active = false;
    };
    // 关闭编辑士兵
    AreaWindCtrl.prototype.closeEditPawn = function () {
        var _a;
        for (var key in this.editPawns) {
            var pawn = this.editPawns[key], state = (_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState();
            if (state && state < Enums_1.PawnState.STAND) {
                pawn.data.changeState(Enums_1.PawnState.NONE);
            }
        }
        this.editPawns = {};
        this.currEditPawn = null;
        this.selectPawnNode_.Component(SelectCellCmpt_1.default).close();
        this.isPawnMoveing = false;
    };
    AreaWindCtrl.prototype.cleanPawns = function () {
        while (this.pawns.length > 0) {
            this.pawns.pop().clean();
        }
        this.pawnMap = {};
    };
    // 初始化士兵
    AreaWindCtrl.prototype.initPawns = function () {
        return __awaiter(this, void 0, void 0, function () {
            var pawns, uidMap, i, m;
            var _this = this;
            return __generator(this, function (_a) {
                pawns = this.model.getAllPawns(), uidMap = {};
                pawns.forEach(function (m) { return uidMap[m.getAbsUid()] = true; });
                for (i = this.pawns.length - 1; i >= 0; i--) {
                    m = this.pawns[i];
                    if (!uidMap[m.getAbsUid()] || !m.data || m.data.isDie()) {
                        this.cleanPawn(this.pawns.splice(i, 1)[0]);
                    }
                }
                return [2 /*return*/, Promise.all(pawns.map(function (m) { return _this.createPawn(m); }))];
            });
        });
    };
    AreaWindCtrl.prototype.createPawn = function (data) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, pfb, uid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isActive() || !data) {
                            return [2 /*return*/, null];
                        }
                        pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
                        if (!pawn) {
                        }
                        else if (pawn.curSkinId !== data.skinId || pawn.curPortrayalId !== data.getPortrayalId()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                        }
                        else if (data.isDie()) {
                            this.pawns.remove('uid', data.uid);
                            this.cleanPawn(pawn);
                            return [2 /*return*/, null];
                        }
                        else {
                            return [2 /*return*/, pawn.resync(data)];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRes(data.getPrefabUrl(), cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isActive()) {
                            return [2 /*return*/, null];
                        }
                        else if (this.pawnMap[data.uid]) {
                            return [2 /*return*/, null]; //防止多次创建
                        }
                        else if (data.aIndex !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.index)) {
                            return [2 /*return*/, null];
                        }
                        uid = data.uid;
                        data = this.model.getPawn(uid) || this.model.getBattleTempPawn(uid);
                        if (!data || data.isDie()) {
                            return [2 /*return*/, null];
                        }
                        pawn = cc.instantiate2(pfb, this.roleNode_).getComponent(PawnCmpt_1.default).init(data, this.borderSize, this.key);
                        this.pawns.push(pawn);
                        this.pawnMap[data.uid] = pawn;
                        return [2 /*return*/, pawn];
                }
            });
        });
    };
    AreaWindCtrl.prototype.cleanPawn = function (pawn, release) {
        if (pawn) {
            delete this.pawnMap[pawn.uid];
            pawn.clean(release);
        }
    };
    // 是否编辑建筑中
    AreaWindCtrl.prototype.isEditBuildState = function () {
        var _a;
        return !!((_a = this.currEditBuild) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 是否编辑小兵中
    AreaWindCtrl.prototype.isEditPawnState = function () {
        var _a;
        return !!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data);
    };
    // 绘制地图
    AreaWindCtrl.prototype.updateMap = function (centre) {
        var _this = this;
        var seasonType = this.tempSeasonType;
        var cell = GameHelper_1.gameHpr.world.getMapCellByIndex(this.model.index), seasonColorConf = Constant_1.AREA_DI_COLOR_CONF[seasonType];
        var colorConf = seasonColorConf[cell.getLandDrawType()] || seasonColorConf[0];
        var areaSize = this.model.areaSize, oYindex = (areaSize.x + 1) % 2;
        // 设置整个背景颜色
        CameraCtrl_1.cameraCtrl.setBgColor(colorConf.bg);
        var buildOrigin = this.model.buildOrigin, buildSize = this.model.buildSize;
        var points = MapHelper_1.mapHelper.getRangePointsByPoint(centre, GameHelper_1.gameHpr.world.getMaxTileRange());
        var isBoss = this.model.isBoss();
        var mi = 0;
        this.diNode.Items(points, function (it, point, i) {
            var x = point.x - _this.borderSize.x, y = point.y - _this.borderSize.y;
            var bx = x - buildOrigin.x, by = y - buildOrigin.y;
            var index = it.Data = MapHelper_1.mapHelper.pointToIndexByNumer(x, y, areaSize);
            var id = x + '_' + y;
            it.setPosition(MapHelper_1.mapHelper.getPixelByPoint(point, _this._temp_vec2_0));
            it.Color('#FFFFFF');
            if (MapHelper_1.mapHelper.isBorder(x, y, areaSize)) { //边界外
                it.Component(cc.Sprite).spriteFrame = null;
            }
            else if (MapHelper_1.mapHelper.isBorder(bx, by, buildSize)) { //战斗区域
                var idx = y % 2 === 0 ? index : index + oYindex;
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                it.Color(colorConf.battle[Number(idx % 2 !== 0)]);
                if (_this.model.banPlacePawnPointMap[id]) {
                    var mn = ResHelper_1.resHelper.getNodeByIndex(_this.maskNode, mi++, _this._temp_vec2_0);
                    mn.Data = true;
                    mn.active = _this.isEditPawnState();
                }
            }
            else if (cell.isMainCity()) { //主城区域
                if (bx === 3 && by === 3) {
                    it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_main');
                }
                else {
                    it.Component(cc.Sprite).spriteFrame = null;
                }
            }
            else if (!isBoss) { //建筑区域
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('comm_area_01');
                if (bx === 0 || bx === buildSize.x - 1 || by === 0 || by === buildSize.y - 1) {
                    it.Color(colorConf.build);
                }
                else if (cell.isAncient()) {
                    it.Color('#D6DBAA');
                }
            }
            else if (bx === 1 && by === 1) { //boss位置
                it.Component(cc.Sprite).spriteFrame = ResHelper_1.resHelper.getLandIcon('land_area_boss_' + cell.landType);
            }
            else {
                it.Component(cc.Sprite).spriteFrame = null;
            }
        });
        // 隐藏多余的
        ResHelper_1.resHelper.hideNodeByIndex(this.maskNode, mi);
    };
    AreaWindCtrl.prototype.getLandIcon = function (icon) {
        return ResHelper_1.resHelper.getLandItemIcon(icon, this.tempSeasonType);
    };
    // 点击地图
    AreaWindCtrl.prototype.onClickMap = function (worldLocation) {
        if (!this.model || !worldLocation) {
            return;
        }
        var point = this.getPointByPixel(worldLocation);
        if (!point) {
            return;
        }
        else if (MapHelper_1.mapHelper.isBorder(point.x, point.y, this.model.areaSize)) {
            return;
        }
        // 是否点击的建筑区域
        var bpoint = point.sub(this.model.buildOrigin, this._temp_vec2_1);
        if (MapHelper_1.mapHelper.isBorder(bpoint.x, bpoint.y, this.model.buildSize)) {
            this.onClickMapArea(point);
        }
        else {
            this.onClickBuildArea(bpoint);
        }
    };
    // 点击地图区域
    AreaWindCtrl.prototype.onClickMapArea = function (point) {
        if (!this.isEditPawnState() || this.isEditBuildState() || this.isPawnMoveing) {
            return;
        }
        var uid = this.currEditPawn.uid;
        if (!this.model.banPlacePawnPointMap[point.ID()] && !this.pawns.some(function (m) { return m.uid !== uid && m.getActPoint().equals(point); })) {
            this.selectPawnNode_.Component(SelectCellCmpt_1.default).open(this.getPixelByPoint(point), this.PAWN_SIZE);
            this.movePawn(point);
        }
    };
    // 点击建筑区域
    AreaWindCtrl.prototype.onClickBuildArea = function (point) {
        if (!this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        else if (!this.model.isBuildBorder(point)) {
            this.currEditBuild.setOffsetPositionByPoint(point);
            this.currEditBuild.updateEditState(this.model.getBuildGroundPointMap(), point);
        }
    };
    // 点击墙
    AreaWindCtrl.prototype.onClickWall = function () {
        if (!this.model.wall || this.isEditBuildState() || this.isEditPawnState()) {
            return;
        }
        audioMgr.playSFX('click');
        if (this.model.isAncient()) {
            var build = this.model.getBuildById(this.model.cityId);
            if (!build) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(this.model.owner)) {
                ViewHelper_1.viewHelper.showPnl(build.getUIUrl(), build);
            }
            else {
                ViewHelper_1.viewHelper.showPnl('build/BuildAncientBase', build);
            }
        }
        else if (this.model.isOwner()) {
            ViewHelper_1.viewHelper.showPnl(this.model.wall.getUIUrl(), this.model.wall);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildCity', this.model.wall);
        }
    };
    AreaWindCtrl.prototype.getSearchCircle = function () {
        if (!this.searchCircle) {
            var model_1 = this.model;
            this.searchCircle = new SearchCircle_1.default().init(function (x, y) { return model_1.checkIsBattleArea(x, y) && !model_1.banPlacePawnPointMap[x + '_' + y]; });
        }
        return this.searchCircle;
    };
    // 移动士兵
    AreaWindCtrl.prototype.movePawn = function (point) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.currEditPawn) return [3 /*break*/, 2];
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, this.movePawnOne(this.currEditPawn, point)];
                    case 1:
                        _a.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        _a.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 移动单个士兵
    AreaWindCtrl.prototype.movePawnOne = function (pawn, point) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var data, sp, area, as, points, time;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!(pawn === null || pawn === void 0 ? void 0 : pawn.data) || ((_a = pawn.data) === null || _a === void 0 ? void 0 : _a.getState()) === Enums_1.PawnState.EDIT_MOVE) {
                            return [2 /*return*/];
                        }
                        data = pawn.data;
                        sp = pawn.getActPoint();
                        area = this.model, as = GameHelper_1.gameHpr.getPawnASatr(data.uid).init(function (x, y) { return area.checkIsBattleArea(x, y) && !area.banPlacePawnPointMap[x + '_' + y]; });
                        return [4 /*yield*/, as.search(sp, point)];
                    case 1:
                        points = _b.sent();
                        if (!this.isActive() || points.length === 0) {
                            return [2 /*return*/];
                        }
                        else if (!this.editPawns[pawn.uid]) {
                            this.editPawns[pawn.uid] = pawn;
                        }
                        time = MapHelper_1.mapHelper.getMoveNeedTime(points, 400);
                        data.changeState(Enums_1.PawnState.EDIT_MOVE, { paths: points, needMoveTime: time });
                        return [4 /*yield*/, ut.wait(time * 0.001)];
                    case 2:
                        _b.sent();
                        if (data.getState() < Enums_1.PawnState.STAND) {
                            data.changeState(Enums_1.PawnState.NONE);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 集合士兵
    AreaWindCtrl.prototype.gatherPawn = function () {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var pawn, uid, armyUid, point, pawns, otherPawns, count, points;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data)) {
                            return [2 /*return*/];
                        }
                        pawn = this.currEditPawn;
                        uid = this.currEditPawn.uid;
                        armyUid = this.currEditPawn.data.armyUid;
                        point = pawn.getActPoint();
                        pawns = [], otherPawns = {};
                        this.pawns.forEach(function (m) {
                            var _a;
                            if (m.uid === uid) {
                            }
                            else if (((_a = m.data) === null || _a === void 0 ? void 0 : _a.armyUid) === armyUid) {
                                pawns.push(m);
                            }
                            else {
                                otherPawns[m.point.ID()] = true;
                            }
                        });
                        count = pawns.length;
                        if (count === 0) {
                            return [2 /*return*/];
                        }
                        points = this.getSearchCircle().search(point, count, otherPawns);
                        // 删除已经在这个位置的士兵
                        points.delete(function (m) {
                            var i = pawns.findIndex(function (p) { return p.getActPoint().equals(m); });
                            if (i !== -1) {
                                pawns.splice(i, 1);
                                return true;
                            }
                            return false;
                        });
                        if (points.length === 0) {
                            return [2 /*return*/];
                        }
                        this.isPawnMoveing = true;
                        this.emit(EventType_1.default.EDIT_PAWN_MOVEING, true);
                        return [4 /*yield*/, Promise.all(points.map(function (m, i) { return _this.movePawnOne(pawns[i], m); }))];
                    case 1:
                        _b.sent();
                        if (this.isActive()) {
                            this.isPawnMoveing = false;
                            this.emit(EventType_1.default.EDIT_PAWN_MOVEING, false);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 保存编辑士兵的信息
    AreaWindCtrl.prototype.editPawnEnd = function () {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var info, pawns, moveInfos, key, pawn, point, _c, err, data, key;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (!((_a = this.currEditPawn) === null || _a === void 0 ? void 0 : _a.data) || this.isPawnMoveing) {
                            return [2 /*return*/];
                        }
                        info = this.currEditPawn.data;
                        pawns = [];
                        moveInfos = [];
                        for (key in this.editPawns) {
                            pawn = this.editPawns[key];
                            point = pawn.getActPoint();
                            moveInfos.push({
                                uid: pawn.uid,
                                point: point.toJson()
                            });
                            if (!((_b = pawn.point) === null || _b === void 0 ? void 0 : _b.equals(point))) {
                                pawns.push({ uid: pawn.uid, point: point.toJson() });
                            }
                        }
                        if (!(pawns.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, NetHelper_1.netHelper.reqMoveAreaPawns({ index: info.aIndex, armyUid: info.armyUid, pawns: moveInfos })];
                    case 1:
                        _c = _d.sent(), err = _c.err, data = _c.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (!this.isActive()) {
                            return [2 /*return*/];
                        }
                        _d.label = 2;
                    case 2:
                        ViewHelper_1.viewHelper.hidePnl('area/EditPawn');
                        for (key in this.editPawns) {
                            this.editPawns[key].confirm();
                        }
                        this.currEditPawn.data.actioning = false;
                        this.builds.forEach(function (m) { return m.setCanClick(true); });
                        this.pawns.forEach(function (m) { return m.setCanClick(true); });
                        this.maskNode.children.forEach(function (m) { return m.active = false; });
                        this.closeEditPawn();
                        return [2 /*return*/];
                }
            });
        });
    };
    AreaWindCtrl = __decorate([
        ccclass
    ], AreaWindCtrl);
    return AreaWindCtrl;
}(mc.BaseWindCtrl));
exports.default = AreaWindCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGFyZWFcXEFyZWFXaW5kQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSwwREFBcUQ7QUFDckQsMkRBQTBEO0FBQzFELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFLNUQsNkRBQTREO0FBQzVELHVDQUFrQztBQUdsQyw2REFBNEQ7QUFDNUQscURBQWdEO0FBQ2hELHlEQUFvRDtBQUNwRCx3REFBbUQ7QUFDbkQseUNBQW9DO0FBQ3BDLDJEQUFrSjtBQUNsSixpREFBNEM7QUFDNUMscURBQXVFO0FBQ3ZFLHFEQUFvRDtBQUNwRCxnRUFBMkQ7QUFDM0QsNkRBQXlEO0FBQ3pELHlEQUFvRDtBQUNwRCwyREFBMEQ7QUFDMUQsK0RBQThEO0FBSXRELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQTBDLGdDQUFlO0lBQXpEO1FBQUEscUVBeXZDQztRQXZ2Q0csMEJBQTBCO1FBQ2xCLGNBQVEsR0FBWSxJQUFJLENBQUEsQ0FBQyxvQkFBb0I7UUFDN0MsZUFBUyxHQUFZLElBQUksQ0FBQSxDQUFDLDJCQUEyQjtRQUNyRCxrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLCtCQUErQjtRQUM1RCxnQkFBVSxHQUFZLElBQUksQ0FBQSxDQUFDLDRCQUE0QjtRQUN2RCxxQkFBZSxHQUFZLElBQUksQ0FBQSxDQUFDLDRCQUE0QjtRQUM1RCxlQUFTLEdBQVksSUFBSSxDQUFBLENBQUMscUJBQXFCO1FBQy9DLG1CQUFhLEdBQVksSUFBSSxDQUFBLENBQUMsMEJBQTBCO1FBQ3hELHNCQUFnQixHQUFZLElBQUksQ0FBQSxDQUFDLDZCQUE2QjtRQUM5RCxvQkFBYyxHQUFZLElBQUksQ0FBQSxDQUFDLDJCQUEyQjtRQUNsRSxNQUFNO1FBRVcsZUFBUyxHQUFZLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO1FBRXpDLFlBQU0sR0FBWSxJQUFJLENBQUE7UUFDdEIsY0FBUSxHQUFZLElBQUksQ0FBQTtRQUN4QixlQUFTLEdBQWlCLElBQUksQ0FBQTtRQUU5QixXQUFLLEdBQVksSUFBSSxDQUFBO1FBQ3JCLGdCQUFVLEdBQW9CLElBQUksQ0FBQTtRQUNsQyxZQUFNLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBO1FBQ3pCLHdCQUFrQixHQUFXLENBQUMsQ0FBQTtRQUM5QixjQUFRLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUNsQyxlQUFTLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUNuQyxpQkFBVyxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQSxDQUFDLFNBQVM7UUFDeEMsZ0JBQVUsR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUEsQ0FBQyxRQUFRO1FBQ3RDLGlCQUFXLEdBQVksRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUVyQyxXQUFLLEdBQWMsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUM1QixlQUFTLEdBQWMsRUFBRSxDQUFBLENBQUMsTUFBTTtRQUNoQyxxQkFBZSxHQUFjLEVBQUUsQ0FBQSxDQUFDLElBQUk7UUFDcEMsWUFBTSxHQUFvQixFQUFFLENBQUEsQ0FBQyxNQUFNO1FBQ25DLGNBQVEsR0FBOEIsRUFBRSxDQUFBO1FBQ3hDLFdBQUssR0FBZSxFQUFFLENBQUEsQ0FBQyxNQUFNO1FBQzdCLGFBQU8sR0FBUSxFQUFFLENBQUE7UUFDakIsZ0JBQVUsR0FBWSxJQUFJLENBQUE7UUFDMUIsV0FBSyxHQUFjLElBQUksQ0FBQSxDQUFDLElBQUk7UUFFNUIsbUJBQWEsR0FBYyxJQUFJLENBQUEsQ0FBQyxTQUFTO1FBQ3pDLGtCQUFZLEdBQWEsSUFBSSxDQUFBLENBQUMsU0FBUztRQUN2QyxrQkFBWSxHQUFpQixJQUFJLENBQUE7UUFDakMsbUJBQWEsR0FBWSxLQUFLLENBQUEsQ0FBQyxXQUFXO1FBQzFDLGVBQVMsR0FBZ0MsRUFBRSxDQUFBLENBQUMsVUFBVTtRQUN0RCxvQkFBYyxHQUFXLENBQUMsQ0FBQTtRQUUxQixrQkFBWSxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUMvQixrQkFBWSxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUMvQixrQkFBWSxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTtRQUMvQixrQkFBWSxHQUFZLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQTs7UUF5ckN2Qyx1QkFBdUI7UUFDdkIsaUNBQWlDO1FBQ2pDLGlCQUFpQjtRQUNqQixRQUFRO1FBQ1Isb0JBQW9CO1FBQ3BCLCtCQUErQjtRQUMvQixJQUFJO1FBRUosNkJBQTZCO1FBQzdCLGlHQUFpRztRQUNqRyw0RkFBNEY7UUFDNUYsZ0NBQWdDO1FBQ2hDLFFBQVE7UUFDUixJQUFJO0lBQ1IsQ0FBQztJQXJzQ1Usc0NBQWUsR0FBdEI7O1FBQ0ksT0FBTztzQkFDRCxHQUFDLGtCQUFRLENBQUMsYUFBYSxJQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzFELEdBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsSUFBRyxJQUFJLENBQUMsaUJBQWlCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2xFLEdBQUMsbUJBQVMsQ0FBQyxrQkFBa0IsSUFBRyxJQUFJLENBQUMsa0JBQWtCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ3BFLEdBQUMsbUJBQVMsQ0FBQyxnQkFBZ0IsSUFBRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2hFLEdBQUMsbUJBQVMsQ0FBQyxVQUFVLElBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDckQsR0FBQyxtQkFBUyxDQUFDLHFCQUFxQixJQUFHLElBQUksQ0FBQyxvQkFBb0IsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDekUsR0FBQyxtQkFBUyxDQUFDLGFBQWEsSUFBRyxJQUFJLENBQUMsYUFBYSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUMxRCxHQUFDLG1CQUFTLENBQUMsb0JBQW9CLElBQUcsSUFBSSxDQUFDLG1CQUFtQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN2RSxHQUFDLG1CQUFTLENBQUMsU0FBUyxJQUFHLElBQUksQ0FBQyxVQUFVLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ25ELEdBQUMsbUJBQVMsQ0FBQyxZQUFZLElBQUcsSUFBSSxDQUFDLGFBQWEsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDekQsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsZUFBZSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM5RCxHQUFDLG1CQUFTLENBQUMsa0JBQWtCLElBQUcsSUFBSSxDQUFDLGtCQUFrQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNwRSxHQUFDLG1CQUFTLENBQUMsYUFBYSxJQUFHLElBQUksQ0FBQyxjQUFjLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzNELEdBQUMsbUJBQVMsQ0FBQyxjQUFjLElBQUcsSUFBSSxDQUFDLGNBQWMsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDNUQsR0FBQyxtQkFBUyxDQUFDLG1CQUFtQixJQUFHLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDdEUsR0FBQyxtQkFBUyxDQUFDLFFBQVEsSUFBRyxJQUFJLENBQUMsU0FBUyxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNqRCxHQUFDLG1CQUFTLENBQUMsUUFBUSxJQUFHLElBQUksQ0FBQyxTQUFTLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQ2pELEdBQUMsbUJBQVMsQ0FBQyxXQUFXLElBQUcsSUFBSSxDQUFDLFlBQVksRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDdkQsR0FBQyxtQkFBUyxDQUFDLFdBQVcsSUFBRyxJQUFJLENBQUMsWUFBWSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUN2RCxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzlELEdBQUMsbUJBQVMsQ0FBQyxXQUFXLElBQUcsSUFBSSxDQUFDLFlBQVksRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDdkQsR0FBQyxtQkFBUyxDQUFDLGlCQUFpQixJQUFHLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDbEUsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsZUFBZSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM5RCxHQUFDLG1CQUFTLENBQUMsYUFBYSxJQUFHLElBQUksQ0FBQyxhQUFhLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzFELEdBQUMsbUJBQVMsQ0FBQyxlQUFlLElBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDOUQsR0FBQyxtQkFBUyxDQUFDLGtCQUFrQixJQUFHLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDcEUsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsZUFBZSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM5RCxHQUFDLG1CQUFTLENBQUMsa0JBQWtCLElBQUcsSUFBSSxDQUFDLGtCQUFrQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUNwRSxHQUFDLG1CQUFTLENBQUMsZUFBZSxJQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzlELEdBQUMsbUJBQVMsQ0FBQyx1QkFBdUIsSUFBRyxJQUFJLENBQUMsc0JBQXNCLEVBQUUsUUFBSyxHQUFFLElBQUk7c0JBQzdFLEdBQUMsbUJBQVMsQ0FBQyxVQUFVLElBQUcsSUFBSSxDQUFDLFdBQVcsRUFBRSxRQUFLLEdBQUUsSUFBSTtzQkFDckQsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsZUFBZSxFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM5RCxHQUFDLG1CQUFTLENBQUMsdUJBQXVCLElBQUcsSUFBSSxDQUFDLHNCQUFzQixFQUFFLFFBQUssR0FBRSxJQUFJO3NCQUM3RSxHQUFDLG1CQUFTLENBQUMsdUJBQXVCLElBQUcsSUFBSSxDQUFDLHNCQUFzQixFQUFFLFFBQUssR0FBRSxJQUFJO3VCQUM3RSxJQUFDLG1CQUFTLENBQUMsd0JBQXdCLElBQUcsSUFBSSxDQUFDLHNCQUFzQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUM5RSxJQUFDLG1CQUFTLENBQUMscUJBQXFCLElBQUcsSUFBSSxDQUFDLG9CQUFvQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUN6RSxJQUFDLG1CQUFTLENBQUMsc0JBQXNCLElBQUcsSUFBSSxDQUFDLHFCQUFxQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUMzRSxJQUFDLG1CQUFTLENBQUMsaUJBQWlCLElBQUcsSUFBSSxDQUFDLGlCQUFpQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUNsRSxJQUFDLG1CQUFTLENBQUMsZ0JBQWdCLElBQUcsSUFBSSxDQUFDLGdCQUFnQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUNoRSxJQUFDLG1CQUFTLENBQUMscUJBQXFCLElBQUcsSUFBSSxDQUFDLHFCQUFxQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUMxRSxJQUFDLG1CQUFTLENBQUMsaUJBQWlCLElBQUcsSUFBSSxDQUFDLGlCQUFpQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUNsRSxJQUFDLG1CQUFTLENBQUMsb0JBQW9CLElBQUcsSUFBSSxDQUFDLG9CQUFvQixFQUFFLFNBQUssR0FBRSxJQUFJO3VCQUN4RSxJQUFDLG1CQUFTLENBQUMsMkJBQTJCLElBQUcsSUFBSSxDQUFDLHlCQUF5QixFQUFFLFNBQUssR0FBRSxJQUFJO1NBQ3pGLENBQUE7SUFDTCxDQUFDO0lBRVksK0JBQVEsR0FBckI7Ozs7O3dCQUNJLElBQUksQ0FBQyxRQUFRLENBQUMsRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQTt3QkFDakMsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLFlBQVksQ0FBQyxDQUFBO3dCQUM3QyxJQUFJLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFBO3dCQUMzQyxJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFBO3dCQUMvQyxJQUFJLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLENBQUMsWUFBWSxDQUFDLHNCQUFZLENBQUMsQ0FBQTt3QkFDbkUsSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO3dCQUNuQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTt3QkFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO3dCQUM3QixPQUFPO3dCQUNQLHFCQUFNLHVCQUFVLENBQUMsVUFBVSxDQUFDLGFBQWEsQ0FBQyxFQUFBOzt3QkFEMUMsT0FBTzt3QkFDUCxTQUEwQyxDQUFBOzs7OztLQUM3QztJQUVZLDhCQUFPLEdBQXBCOzs7Ozs7O3dCQUNJLEtBQUEsSUFBSSxDQUFBO3dCQUFTLHFCQUFNLElBQUksQ0FBQyxVQUFVLENBQUMsY0FBYyxhQUFDLG9CQUFPLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSwwQ0FBRSxLQUFLLG1DQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUE7O3dCQUEzRixHQUFLLEtBQUssR0FBRyxTQUE4RSxDQUFBO3dCQUMzRixJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRTs0QkFDYixzQkFBTTt5QkFDVDt3QkFDRCxJQUFJLENBQUMsY0FBYyxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLGFBQWEsRUFBRSxDQUFBO3dCQUNuRCxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7d0JBQ3ZDLE9BQU87d0JBQ1AsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQTt3QkFDdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQTt3QkFDeEMsbUJBQW1CO3dCQUNuQixJQUFJLENBQUMsS0FBSyxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUE7d0JBQ3pDLGNBQWM7d0JBQ2QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO3dCQUMvRCxVQUFVO3dCQUNWLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTt3QkFFdkQsS0FBSyxHQUFHLG9CQUFPLENBQUMsS0FBSyxDQUFDLGVBQWUsRUFBRSxFQUFFLEtBQUssR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUE7d0JBQzVGLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO3dCQUN4QixRQUFRO3dCQUNSLHFCQUFNLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBQTs7d0JBRHJCLFFBQVE7d0JBQ1IsU0FBcUIsQ0FBQTs7Ozs7S0FDeEI7SUFFTSw4QkFBTyxHQUFkLFVBQWUsT0FBZ0I7UUFDM0IsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDYix1QkFBVSxDQUFDLFFBQVEsQ0FBQyxvQkFBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsQ0FBQyxDQUFBO1lBQ2hELElBQUksb0JBQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxFQUFFLEVBQUU7Z0JBQzNCLHVCQUFVLENBQUMsY0FBYyxDQUFDLGFBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQTthQUMzQztZQUNELE9BQU07U0FDVDtRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUMzQixJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDOUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDMUIsU0FBUztRQUNULElBQUksQ0FBQyxLQUFLLENBQUMsb0JBQW9CLEVBQUUsQ0FBQTtRQUNqQyxTQUFTO1FBQ1QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQTtRQUMvRCxVQUFVO1FBQ1YsSUFBTSxFQUFFLEdBQUcsb0JBQU8sQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMscUJBQWEsQ0FBQyxlQUFlLENBQUMsQ0FBQTtRQUM3RSx1QkFBVSxDQUFDLElBQUksQ0FBQyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEVBQUUsSUFBSSxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQTtRQUMzRixPQUFPO1FBQ1AsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFBO1FBQ2hCLE9BQU87UUFDUCxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7UUFDakIsT0FBTztRQUNQLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFBO1FBQ25DLEtBQUs7UUFDTCxJQUFJLE9BQU8sRUFBRTtZQUNULElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQywwQkFBMEIsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ3BFO2FBQU07WUFDSCx1QkFBVSxDQUFDLE9BQU8sQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO1NBQ2hEO1FBQ0QsRUFBRTtRQUNGLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7UUFDL0MsRUFBRTtRQUNGLG9CQUFPLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQTtJQUNqRCxDQUFDO0lBRU0sOEJBQU8sR0FBZDtRQUNJLG9CQUFPLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLHFCQUFhLENBQUMsZUFBZSxFQUFFLHVCQUFVLENBQUMsU0FBUyxDQUFDLENBQUE7UUFDeEYsdUJBQVUsQ0FBQyxPQUFPLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDakMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtRQUN0QixvQkFBTyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUE7UUFDL0IsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ1osSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO1FBQ2pCLHFCQUFTLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO1FBQ3hDLHFCQUFTLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO1FBQzFDLElBQUksQ0FBQyxVQUFVLENBQUMsaUJBQWlCLEVBQUUsQ0FBQTtRQUNuQyxJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksR0FBRyxLQUFLLENBQUE7UUFDNUIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1FBQ3RDLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxHQUFHLEtBQUssQ0FBQTtRQUMvQixXQUFXLENBQUMsMkJBQTJCLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2pELHVCQUFVLENBQUMsS0FBSyxFQUFFLENBQUE7UUFDbEIsU0FBUyxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtRQUN2QyxRQUFRLENBQUMsWUFBWSxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQzlCLFFBQVEsQ0FBQyxZQUFZLENBQUMsTUFBTSxDQUFDLENBQUE7SUFDakMsQ0FBQztJQUVNLDhCQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQixNQUFNO0lBQ04saUhBQWlIO0lBRXpHLHFDQUFjLEdBQXRCO1FBQ0ksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFBO0lBQ2pCLENBQUM7SUFFTyx3Q0FBaUIsR0FBekI7UUFDSSxPQUFPLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtJQUN6QixDQUFDO0lBRUQsWUFBWTtJQUNKLHlDQUFrQixHQUExQixVQUEyQixJQUFlLEVBQUUsS0FBYTtRQUNyRCxJQUFJLENBQUMsZ0JBQWdCLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUE7UUFDckMsSUFBSSxJQUFJLEVBQUU7WUFDTixJQUFJLENBQUMsZ0JBQWdCLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyx3QkFBd0IsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ3BFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUNqRTtJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ0gsdUNBQWdCLEdBQXhCLFVBQXlCLElBQWU7UUFDcEMsWUFBWTtRQUNaLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsSUFBSSxDQUFDLEdBQUcsS0FBSyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsQ0FBQyxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsRUFBL0MsQ0FBK0MsQ0FBQyxDQUFBO1FBQ3pFLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsRUFBcEIsQ0FBb0IsQ0FBQyxDQUFBO1FBQzdDLFNBQVM7UUFDVCxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFBO0lBQzVCLENBQUM7SUFFRCxPQUFPO0lBQ0Msa0NBQVcsR0FBbkIsVUFBb0IsSUFBZSxFQUFFLEdBQVk7UUFDN0MsSUFBTSxLQUFLLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQzFDLElBQUksQ0FBQyxLQUFLLENBQUMsZUFBZSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBLENBQUMsTUFBTTtRQUN4RCxJQUFJLENBQUMsd0JBQXdCLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDcEMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLHNCQUFzQixFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUE7SUFDcEUsQ0FBQztJQUVELFdBQVc7SUFDSCwyQ0FBb0IsR0FBNUIsVUFBNkIsSUFBWTtRQUNyQyxJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWEsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLGlDQUFpQyxFQUFFO1lBQ25GLE9BQU07U0FDVDtRQUNELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUE7UUFDL0IsSUFBSSxJQUFJLEtBQUssUUFBUSxFQUFFLEVBQUUsSUFBSTtZQUN6QixJQUFJLENBQUMsTUFBTSxFQUFFLENBQUE7U0FDaEI7YUFBTSxJQUFJLElBQUksS0FBSyxJQUFJLEVBQUUsRUFBRSxJQUFJO1lBQzVCLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTtnQkFDaEIsT0FBTyx1QkFBVSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUE7YUFDOUM7WUFDRCxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQzdELG1DQUFtQztTQUN0QztRQUNELHVCQUFVLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLENBQUEsQ0FBQyxRQUFRO1FBQzdDLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLGlCQUFpQixDQUFDLEtBQUssQ0FBQyxFQUExQixDQUEwQixDQUFDLENBQUEsQ0FBQyxRQUFRO1FBQzdELElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO1FBQzVDLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQSxDQUFDLFVBQVU7UUFDNUIsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO0lBQ3pCLENBQUM7SUFFRCxPQUFPO0lBQ0Msb0NBQWEsR0FBckIsVUFBc0IsS0FBYSxFQUFFLEdBQVc7UUFDNUMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssS0FBSyxLQUFLLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRTtZQUN4RCxPQUFNO1NBQ1Q7UUFDRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssR0FBRyxFQUFiLENBQWEsQ0FBQyxDQUFBO1FBQ2hELElBQUksSUFBSSxFQUFFO1lBQ04sSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxFQUFwQixDQUFvQixDQUFDLENBQUE7WUFDOUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxFQUFwQixDQUFvQixDQUFDLENBQUE7WUFDN0MsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO1lBQ3hELE1BQU07WUFDTixJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUE7WUFDMUIsUUFBUTtZQUNSLHVCQUFVLENBQUMsT0FBTyxDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsQ0FBQTtZQUN6QyxJQUFJLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQTtZQUN4QixJQUFJLENBQUMsZUFBZSxDQUFDLFNBQVMsQ0FBQyx3QkFBYyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsZUFBZSxFQUFFLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFBO1NBQzNHO0lBQ0wsQ0FBQztJQUVELFlBQVk7SUFDSiwwQ0FBbUIsR0FBM0IsVUFBNEIsSUFBWTtRQUNwQyxJQUFJLENBQUMsSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsV0FBVyxFQUFFLEVBQUU7WUFDM0UsdUJBQVUsQ0FBQyxPQUFPLENBQUMsZUFBZSxDQUFDLENBQUE7WUFDbkMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxFQUFuQixDQUFtQixDQUFDLENBQUE7WUFDN0MsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxFQUFuQixDQUFtQixDQUFDLENBQUE7WUFDNUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxLQUFLLEVBQWhCLENBQWdCLENBQUMsQ0FBQTtZQUNyRCxJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7U0FDdkI7YUFBTSxJQUFJLElBQUksS0FBSyxRQUFRLEVBQUUsRUFBRSxJQUFJO1lBQ2hDLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTtTQUNwQjthQUFNLElBQUksSUFBSSxLQUFLLElBQUksRUFBRTtZQUN0QixJQUFJLENBQUMsV0FBVyxFQUFFLENBQUE7U0FDckI7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLGlDQUFVLEdBQWxCLFVBQW1CLElBQWM7UUFBakMsaUJBYUM7O1FBWkcsSUFBSSxJQUFJLENBQUMsTUFBTSxZQUFLLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssQ0FBQSxFQUFFO1lBQ25DLElBQU0sU0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLENBQUE7WUFDcEMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQSxJQUFJO2dCQUM1QixJQUFJLElBQUksSUFBSSxLQUFJLENBQUMsUUFBUSxFQUFFLElBQUksU0FBTyxFQUFFO29CQUNwQyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUE7b0JBQzNCLGNBQWM7b0JBQ2QsdUJBQVUsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLENBQUE7b0JBQzlCLEtBQUs7b0JBQ0wsb0NBQW9DO2lCQUN2QztZQUNMLENBQUMsQ0FBQyxDQUFBO1NBQ0w7SUFDTCxDQUFDO0lBRUQsT0FBTztJQUNDLG9DQUFhLEdBQXJCLFVBQXNCLElBQWM7O1FBQ2hDLElBQUksSUFBSSxDQUFDLE1BQU0sWUFBSyxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUNuQyxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1lBQ2pELElBQUksS0FBSyxFQUFFO2dCQUNQLElBQUksQ0FBQyxVQUFVLENBQUMsS0FBSyxDQUFDLENBQUE7Z0JBQ3RCLFNBQVMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTthQUMxRDtTQUNKO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxzQ0FBZSxHQUF2QixVQUF3QixJQUFjOztRQUNsQyxJQUFJLElBQUksQ0FBQyxNQUFNLFlBQUssSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxDQUFBLEVBQUU7U0FDdEM7YUFBTSxJQUFJLElBQUksQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFO1lBQ3pDLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1NBQzdCO2FBQU07WUFDSCxNQUFBLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxJQUFJLENBQUMsR0FBRyxFQUFsQixDQUFrQixDQUFDLDBDQUFFLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFDO1NBQy9EO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCx5Q0FBa0IsR0FBMUIsVUFBMkIsSUFBYzs7UUFDckMsSUFBSSxJQUFJLENBQUMsTUFBTSxZQUFLLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssQ0FBQSxFQUFFO1lBQ25DLE1BQUEsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLEVBQWxCLENBQWtCLENBQUMsMENBQUUsU0FBUyxHQUFFO1NBQ3pEO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxxQ0FBYyxHQUF0QixVQUF1QixLQUFhOztRQUNoQyxJQUFJLEtBQUssWUFBSyxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUM3QixNQUFBLElBQUksQ0FBQyxLQUFLLDBDQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFDO1NBQy9CO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCwwQ0FBbUIsR0FBM0IsVUFBNEIsSUFBZ0I7O1FBQ3hDLElBQUksSUFBSSxDQUFDLEtBQUssWUFBSyxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUNsQyxPQUFNO1NBQ1Q7UUFDRCxJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFLEVBQWxCLENBQWtCLENBQUMsQ0FBQTtRQUN2RCxJQUFJLEtBQUssRUFBRTtZQUNQLEtBQUssQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1lBQ3ZCLEtBQUssQ0FBQyxjQUFjLEVBQUUsQ0FBQTtTQUN6QjtJQUNMLENBQUM7SUFFRCxPQUFPO0lBQ0MscUNBQWMsR0FBdEIsVUFBdUIsS0FBYTs7UUFDaEMsSUFBSSxLQUFLLFlBQUssSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxDQUFBLEVBQUU7WUFDN0IsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO1NBQ3BCO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxnQ0FBUyxHQUFqQixVQUFrQixJQUFhO1FBQS9CLGlCQUlDOztRQUhHLElBQUksSUFBSSxDQUFDLE1BQU0sWUFBSyxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUNuQyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQWxCLENBQWtCLENBQUMsQ0FBQTtTQUM5QztJQUNMLENBQUM7SUFFRCxPQUFPO0lBQ0MsbUNBQVksR0FBcEIsVUFBcUIsSUFBYTtRQUFsQyxpQkFTQzs7UUFSRyxJQUFJLElBQUksQ0FBQyxNQUFNLFlBQUssSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxDQUFBLEVBQUU7WUFDbkMsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUNoQixJQUFNLENBQUMsR0FBRyxLQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxVQUFBLENBQUMsWUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQyxDQUFDLEdBQUcsSUFBSSxPQUFBLENBQUMsQ0FBQyxJQUFJLDBDQUFFLE9BQU8sTUFBSyxJQUFJLENBQUMsR0FBRyxDQUFBLEVBQUEsQ0FBQyxDQUFBO2dCQUNwRixJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtvQkFDVixLQUFJLENBQUMsU0FBUyxDQUFDLEtBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQTtpQkFDbkQ7WUFDTCxDQUFDLENBQUMsQ0FBQTtTQUNMO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxtQ0FBWSxHQUFwQixVQUFxQixJQUFhO1FBQWxDLGlCQWVDOztRQWRHLElBQUksSUFBSSxDQUFDLE1BQU0sWUFBSyxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUNuQyxPQUFNO1NBQ1Q7UUFDRCxTQUFTO1FBQ1QsS0FBSyxJQUFJLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRTtZQUM3QyxJQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ3ZCLElBQUksT0FBQSxDQUFDLENBQUMsSUFBSSwwQ0FBRSxPQUFPLE1BQUssSUFBSSxDQUFDLEdBQUcsRUFBRTtnQkFDOUIsU0FBUTthQUNYO2lCQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO2dCQUN0QyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7Z0JBQ3ZCLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFBO2FBQzFCO1NBQ0o7UUFDRCxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQWxCLENBQWtCLENBQUMsQ0FBQTtJQUMvQyxDQUFDO0lBRUQsU0FBUztJQUNELHNDQUFlLEdBQXZCLFVBQXdCLEtBQWE7UUFDakMsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssS0FBSyxLQUFLLEVBQUU7WUFDNUIsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFBO1NBQ25CO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxnQ0FBUyxHQUFqQixVQUFrQixLQUFhLEVBQUUsSUFBYTtRQUMxQyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxLQUFLLEtBQUssRUFBRTtZQUM1QixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO1NBQ3hCO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxtQ0FBWSxHQUFwQixVQUFxQixLQUFhLEVBQUUsR0FBVztRQUMzQyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxLQUFLLEtBQUssRUFBRTtZQUM1QixJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxHQUFHLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQTtTQUN0RDtJQUNMLENBQUM7SUFFRCxPQUFPO0lBQ0Msd0NBQWlCLEdBQXpCLFVBQTBCLEtBQWE7O1FBQ25DLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLEtBQUssS0FBSyxFQUFFO1lBQzVCLE9BQU07U0FDVDtRQUNELGNBQWM7UUFDZCxJQUFJLENBQUMscUJBQXFCLEVBQUUsQ0FBQTtRQUM1QixJQUFJLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDckIsY0FBYztRQUNkLE1BQUEsSUFBSSxDQUFDLFlBQVksMENBQUUsTUFBTSxHQUFFO1FBQzNCLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQTtRQUNwQixRQUFRO1FBQ1IsTUFBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBQztRQUM1QixRQUFRO1FBQ1IsSUFBSSxDQUFDLFNBQVMsRUFBRSxDQUFBO1FBQ2hCLE9BQU87UUFDUCxJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsMEJBQTBCLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUNqRSxFQUFFO1FBQ0Ysb0JBQU8sQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUE7SUFDN0IsQ0FBQztJQUVELE9BQU87SUFDQyxzQ0FBZSxHQUF2QixVQUF3QixLQUFhOztRQUNqQyxJQUFJLE9BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxNQUFLLEtBQUssRUFBRTtZQUM3QixPQUFNO1NBQ1Q7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLEVBQUUsRUFBRSxlQUFlO1lBQ3BILE9BQU8sSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFBO1NBQ3hCO1FBQ0QsTUFBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBQztRQUM1QixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3JCLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQTtRQUNoQixPQUFPO1FBQ1AsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLDBCQUEwQixFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7UUFDakUsRUFBRTtRQUNGLG9CQUFPLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFBO0lBQzlCLENBQUM7SUFFRCxPQUFPO0lBQ0Msb0NBQWEsR0FBckIsVUFBc0IsSUFBUzs7UUFDM0IsSUFBSSxJQUFJLENBQUMsS0FBSyxZQUFLLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssQ0FBQSxFQUFFO1lBQ2xDLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRTtnQkFDMUIsTUFBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxJQUFJLEdBQUU7YUFDckI7WUFDRCx1QkFBVSxDQUFDLGFBQWEsQ0FBQyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsS0FBSyxFQUFFLElBQUksQ0FBQyxLQUFLLEVBQUUsRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtTQUNwSTtJQUNMLENBQUM7SUFFRCxPQUFPO0lBQ0Msc0NBQWUsR0FBdkIsVUFBd0IsSUFBUzs7UUFDN0IsSUFBSSxPQUFBLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssTUFBSyxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ2xDLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsT0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBbEIsQ0FBa0IsQ0FBQywwQ0FBRSxXQUFXLEVBQUUsQ0FBQTtZQUMzSCxJQUFJLEdBQUcsRUFBRTtnQkFDTCx1QkFBVSxDQUFDLGtCQUFrQixDQUFDLElBQUksRUFBRSxHQUFHLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDekU7U0FDSjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0QseUNBQWtCLEdBQTFCLFVBQTJCLElBQVM7O1FBQ2hDLElBQUksT0FBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLE1BQUssSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNsQyxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBbEIsQ0FBa0IsQ0FBQyxDQUFBO1lBQ3JELElBQUksSUFBSSxFQUFFO2dCQUNOLHVCQUFVLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsYUFBYSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7YUFDNUY7U0FDSjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0Qsc0NBQWUsR0FBdkIsVUFBd0IsSUFBUzs7UUFDN0IsSUFBSSxPQUFBLElBQUksQ0FBQyxLQUFLLDBDQUFFLEtBQUssTUFBSyxJQUFJLENBQUMsS0FBSyxFQUFFO1lBQ2xDLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsS0FBSyxFQUFFLENBQUE7WUFDN0QsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtZQUMvRCx1QkFBVSxDQUFDLGFBQWEsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7U0FDL0Q7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHlDQUFrQixHQUExQixVQUEyQixJQUFTOztRQUNoQyxJQUFJLE9BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxNQUFLLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDbEMsSUFBSSxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQTtZQUNuRCxJQUFJLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFBO1lBQzVCLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxLQUFLLEVBQUU7Z0JBQ3JCLElBQUksR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFBO2FBQzVCO2lCQUFNLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxNQUFNLEVBQUU7Z0JBQzdCLElBQUksR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFBO2dCQUNyQixJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsMEJBQWUsR0FBRyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxHQUFHLG9CQUFTLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxDQUFDLENBQUE7YUFDMUY7WUFDRCx1QkFBVSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1NBQ3BEO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxzQ0FBZSxHQUF2QixVQUF3QixLQUFhLEVBQUUsR0FBVyxFQUFFLElBQVM7O1FBQ3pELElBQUksT0FBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLE1BQUssS0FBSyxFQUFFO1lBQzdCLFFBQVEsQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQzlCO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCw2Q0FBc0IsR0FBOUIsVUFBK0IsS0FBYSxFQUFFLElBQVk7O1FBQ3RELElBQUksT0FBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLE1BQUssS0FBSyxFQUFFO1lBQzdCLHVCQUFVLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFBO1NBQ3pCO0lBQ0wsQ0FBQztJQUVELE9BQU87SUFDQyxrQ0FBVyxHQUFuQixVQUFvQixJQUFTOztRQUN6QixJQUFJLE9BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxPQUFLLElBQUksYUFBSixJQUFJLHVCQUFKLElBQUksQ0FBRSxLQUFLLENBQUEsRUFBRTtZQUNuQyxPQUFNO1NBQ1QsQ0FBQTs7WUFFRztRQUNKLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO1FBQzVDLElBQUksdUJBQVUsQ0FBQyxzQkFBc0IsQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN4Qyx1QkFBVSxDQUFDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ3BDO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxzQ0FBZSxHQUF2QjtRQUNJLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLGNBQWMsRUFBRSxFQUFsQixDQUFrQixDQUFDLENBQUE7SUFDaEQsQ0FBQztJQUVELFNBQVM7SUFDRCw2Q0FBc0IsR0FBOUIsVUFBK0IsS0FBYTtRQUN4QyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsS0FBSyxLQUFLLEtBQUssRUFBRTtZQUM1QixJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxlQUFlLEVBQUUsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO1NBQ2hEO0lBQ0wsQ0FBQztJQUVELFlBQVk7SUFDSiwyQ0FBb0IsR0FBNUIsVUFBNkIsR0FBWTtRQUNyQyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxZQUFZLENBQUMsR0FBRyxDQUFDLEVBQW5CLENBQW1CLENBQUMsQ0FBQTtJQUNoRCxDQUFDO0lBRUQsV0FBVztJQUNILDRDQUFxQixHQUE3QixVQUE4QixHQUFZO1FBQ3RDLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsRUFBcEIsQ0FBb0IsQ0FBQyxDQUFBO0lBQ2pELENBQUM7SUFFRCxTQUFTO0lBQ0Qsd0NBQWlCLEdBQXpCLFVBQTBCLElBQWE7O1FBQ25DLE1BQUEsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLEVBQWxCLENBQWtCLENBQUMsMENBQUUsbUJBQW1CLEdBQUU7SUFDbkUsQ0FBQztJQUVELFNBQVM7SUFDRCx1Q0FBZ0IsR0FBeEIsVUFBeUIsSUFBYTtRQUNsQyxJQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxHQUFHLEtBQUssSUFBSSxDQUFDLEdBQUcsRUFBbEIsQ0FBa0IsQ0FBQyxDQUFBO1FBQ3ZELElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO1lBQ1YsSUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQTtZQUMxQixJQUFJLElBQUksQ0FBQyxTQUFTLEtBQUssSUFBSSxDQUFDLE1BQU0sRUFBRTtnQkFDaEMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO2dCQUN2QixJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFBO2dCQUNwQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO2FBQ3hCO1NBQ0o7UUFDRCxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxlQUFlLEVBQUUsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO0lBQ2pELENBQUM7SUFFRCxPQUFPO0lBQ0MsNENBQXFCLEdBQTdCLFVBQThCLElBQWE7UUFBM0MsaUJBaUJDO1FBaEJHLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ2pCLE9BQU07U0FDVDtRQUNELElBQU0sQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxJQUFJLENBQUMsR0FBRyxFQUFsQixDQUFrQixDQUFDLENBQUE7UUFDdkQsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUU7WUFDVixJQUFNLE1BQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQzFCLElBQUksTUFBSSxDQUFDLGNBQWMsS0FBSyxJQUFJLENBQUMsU0FBUyxDQUFDLEVBQUUsRUFBRTtnQkFDM0MsTUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDO29CQUM1QyxJQUFJLEtBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRTt3QkFDakIsS0FBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO3dCQUN2QixLQUFJLENBQUMsU0FBUyxDQUFDLE1BQUksQ0FBQyxDQUFBO3dCQUNwQixLQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFBO3FCQUN4QjtnQkFDTCxDQUFDLENBQUMsQ0FBQTthQUNMO1NBQ0o7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHdDQUFpQixHQUF6Qjs7UUFDSSxNQUFBLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEVBQUUsS0FBSywyQkFBZ0IsRUFBekIsQ0FBeUIsQ0FBQywwQ0FBRSxnQkFBZ0IsR0FBRTtJQUN4RSxDQUFDO0lBRUQsU0FBUztJQUNELDJDQUFvQixHQUE1Qjs7UUFDSSxNQUFBLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEVBQUUsS0FBSywyQkFBZ0IsRUFBekIsQ0FBeUIsQ0FBQywwQ0FBRSxnQkFBZ0IsR0FBRTtJQUN4RSxDQUFDO0lBRUQsTUFBTTtJQUNFLGdEQUF5QixHQUFqQyxVQUFrQyxJQUFTO1FBQ3ZDLElBQUksSUFBSSxDQUFDLEtBQUssS0FBSyxNQUFNLEVBQUU7WUFDdkIseUJBQVcsQ0FBQyxtQkFBbUIsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7U0FDdkU7SUFDTCxDQUFDO0lBQ0QsaUhBQWlIO0lBRXpHLCtCQUFRLEdBQWhCLHNCQUFxQixPQUFPLElBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxRQUFDLElBQUksQ0FBQyxLQUFLLDBDQUFFLE1BQU0sQ0FBQSxDQUFBLENBQUMsQ0FBQztJQUUxRCxzQ0FBZSxHQUF2QixVQUF3QixLQUFjO1FBQ2xDLE9BQU8sS0FBSyxJQUFJLHFCQUFTLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQTtJQUM1RixDQUFDO0lBRU8sc0NBQWUsR0FBdkIsVUFBd0IsS0FBYztRQUNsQyxPQUFPLEtBQUssSUFBSSxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFBO0lBQzdFLENBQUM7SUFFTywyQ0FBb0IsR0FBNUIsVUFBNkIsS0FBYztRQUN2QyxPQUFPLHFCQUFTLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO0lBQ3ZHLENBQUM7SUFFTyw0QkFBSyxHQUFiOztRQUNJLElBQUksQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ2pDLE1BQUEsSUFBSSxDQUFDLEtBQUssMENBQUUsU0FBUyxDQUFDLEtBQUssRUFBQztRQUM1QixJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQTtRQUNqQixvQkFBTyxDQUFDLGlCQUFpQixFQUFFLENBQUE7UUFDM0IsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUE7UUFDeEIsSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBO1FBQ2pCLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUNyQixJQUFJLENBQUMsb0JBQW9CLEVBQUUsQ0FBQTtRQUMzQixJQUFJLENBQUMsV0FBVyxFQUFFLENBQUE7UUFDbEIsb0JBQW9CO1FBQ3BCLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTtRQUNyQixJQUFJLENBQUMsYUFBYSxFQUFFLENBQUE7SUFDeEIsQ0FBQztJQUVELFdBQVc7SUFDRyw2QkFBTSxHQUFwQjs7Ozs7Ozt3QkFDSSxvQkFBTyxDQUFDLGlCQUFpQixFQUFFLENBQUE7d0JBQzNCLGNBQWM7d0JBQ2QsSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUE7d0JBQzVCLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQTt3QkFDckIsY0FBYzt3QkFDZCxNQUFBLElBQUksQ0FBQyxZQUFZLDBDQUFFLE1BQU0sR0FBRTt3QkFDM0IsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO3dCQUVkLEtBQUssR0FBRyxvQkFBTyxDQUFDLEtBQUssQ0FBQTt3QkFDM0IsS0FBQSxJQUFJLENBQUE7d0JBQVMscUJBQU0sSUFBSSxDQUFDLFVBQVUsQ0FBQyxjQUFjLGFBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSwwQ0FBRSxLQUFLLG1DQUFJLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxFQUFBOzt3QkFBekYsR0FBSyxLQUFLLEdBQUcsU0FBNEUsQ0FBQTt3QkFDekYsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUU7NEJBQ2Isc0JBQU8sdUJBQVUsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxDQUFDLEVBQUE7eUJBQ2xEOzZCQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsRUFBRSxFQUFFLGVBQWU7NEJBQ3BILHNCQUFPLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBQTt5QkFDeEI7d0JBQ0QsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUE7d0JBQzFCLE9BQU87d0JBQ1AsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUE7d0JBQ25DLE9BQU87d0JBQ1AsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTt3QkFDeEQsS0FBSzt3QkFDTCxNQUFBLElBQUksQ0FBQyxLQUFLLDBDQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFDO3dCQUM1QixPQUFPO3dCQUNQLElBQUksQ0FBQyxVQUFVLEVBQUUsQ0FBQTt3QkFDakIsT0FBTzt3QkFDUCxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUE7d0JBQ2hCLE9BQU87d0JBQ1AsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGNBQWMsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7Ozs7O0tBQ2xEO0lBRUQsT0FBTztJQUNPLDhCQUFPLEdBQXJCOzs7Ozt3QkFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTt3QkFDcEMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFBO3dCQUNaLHFCQUFNLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBQTs7d0JBQXBCLFNBQW9CLENBQUE7d0JBQ3BCLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQTt3QkFDbEMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQTt3QkFDbEIsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLG1CQUFtQixDQUFDLENBQUE7Ozs7O0tBQzNDO0lBRUQsUUFBUTtJQUNNLCtCQUFRLEdBQXRCOzs7Ozs7d0JBQ0ksSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFBOzZCQUNiLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsRUFBcEIsd0JBQW9CO3dCQUVWLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsa0JBQWtCLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUE7O3dCQUExRSxHQUFHLEdBQUcsU0FBb0U7d0JBQzlFLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFOzRCQUN2QixzQkFBTTt5QkFDVDt3QkFDRyxJQUFJLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFBO3dCQUMvQyxJQUFJLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsbUJBQVMsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUE7d0JBRXBELElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQTt3QkFDakMsdUJBQVUsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLFFBQVEsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRSxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFBO3dCQUNoSixJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7NkJBQ3pCLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxFQUFmLHdCQUFlOzZCQUVYLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsRUFBdkIsd0JBQXVCO3dCQUN2QixxQkFBTSxJQUFJLENBQUMsVUFBVSxFQUFFLEVBQUE7O3dCQUF2QixTQUF1QixDQUFBOzs7NkJBR3ZCLENBQUEsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQSxFQUF0Qix3QkFBc0I7d0JBQ2hCLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsZ0JBQWdCLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUE7O3dCQUF4RSxHQUFHLEdBQUcsU0FBa0UsQ0FBQTt3QkFDeEUsSUFBSSxHQUFHLElBQUksSUFBSSxDQUFDLE9BQU8sRUFBRTs0QkFDckIsSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFBOzRCQUM3RCxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO3lCQUN4Qzs7O3dCQUdULElBQUksQ0FBQyxvQkFBb0IsRUFBRSxDQUFBOzs7b0JBRS9CLFdBQVc7b0JBQ1gscUJBQU0sSUFBSSxDQUFDLHFCQUFxQixFQUFFO3dCQUNsQyxTQUFTO3NCQUR5Qjs7d0JBRGxDLFdBQVc7d0JBQ1gsU0FBa0MsQ0FBQTt3QkFDbEMsU0FBUzt3QkFDVCxxQkFBTSxJQUFJLENBQUMsZUFBZSxDQUFDLG9CQUFPLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFBOzt3QkFEdkUsU0FBUzt3QkFDVCxTQUF1RSxDQUFBOzs7OztLQUMxRTtJQUVhLGlDQUFVLEdBQXhCOzs7Ozs7O3dCQUNJLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFOzRCQUMxQixJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sRUFBRSxDQUFBO3lCQUM3Qjt3QkFDRCxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7d0JBQ2YsTUFBTSxHQUFHLENBQUMsQ0FBQTt3QkFDaEIscUJBQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsVUFBTyxDQUFDOzs7Ozs0Q0FDdkMsR0FBRyxHQUFHLFVBQVEsTUFBTSxjQUFTLE1BQU0sU0FBSSxDQUFDLENBQUMsSUFBSSxTQUFJLENBQUMsQ0FBQyxHQUFLLENBQUE7NENBQzVELElBQUksQ0FBQyxDQUFDLEtBQUssRUFBRTtnREFDVCxHQUFHLElBQUksR0FBRyxHQUFHLENBQUMsQ0FBQyxLQUFLLENBQUE7NkNBQ3ZCOzRDQUNXLHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFBOzs0Q0FBM0QsR0FBRyxHQUFHLFNBQXFEOzRDQUNqRSxJQUFJLEdBQUcsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFO2dEQUNmLElBQUksR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUE7Z0RBQ2xELElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUE7Z0RBQzNCLElBQUksQ0FBQyxXQUFXLENBQUMscUJBQVMsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFBO2dEQUM3RixJQUFJLENBQUMsWUFBWSxDQUFDLHdCQUFjLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsQ0FBQTtnREFDNUQsSUFBSSxDQUFDLE1BQU0sR0FBRywwQkFBZSxHQUFHLElBQUksQ0FBQyxDQUFDLENBQUE7Z0RBQ3RDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBOzZDQUN4Qjs7OztpQ0FDSixDQUFDLENBQUMsRUFBQTs7d0JBZEgsU0FjRyxDQUFBOzs7OztLQUNOO0lBRU8saUNBQVUsR0FBbEI7O1FBQ0ksTUFBQSxJQUFJLENBQUMsVUFBVSwwQ0FBRSxPQUFPLEdBQUU7UUFDMUIsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUE7UUFDdEIsTUFBQSxJQUFJLENBQUMsS0FBSywwQ0FBRSxLQUFLLEdBQUU7UUFDbkIsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7UUFDakIsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDMUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxHQUFHLEVBQUUsQ0FBQyxPQUFPLEVBQUUsQ0FBQTtTQUM3QjtRQUNELElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTtJQUN6QixDQUFDO0lBRUQsU0FBUztJQUNLLHNDQUFlLEdBQTdCLFVBQThCLElBQVk7Ozs7Ozs7d0JBQ3RDLElBQUksQ0FBQyxJQUFJLEVBQUU7NEJBQ1Asc0JBQU8sSUFBSSxDQUFDLGNBQWMsRUFBRSxFQUFBO3lCQUMvQjt3QkFDSyxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUE7d0JBQ25DLElBQUksSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDLE1BQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxJQUFJLEVBQUU7NEJBQzVFLHNCQUFPLE1BQU0sQ0FBQyxPQUFPLENBQUMsVUFBQyxLQUFLLEVBQUUsQ0FBQztvQ0FDM0IsSUFBTSxJQUFJLEdBQUcsS0FBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRSxHQUFHLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxLQUFJLENBQUMsVUFBVSxFQUFFLEtBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFBO29DQUM5RyxJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFBO29DQUNyQixJQUFJLENBQUMsTUFBTSxHQUFHLDBCQUFlLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQTtnQ0FDMUMsQ0FBQyxDQUFDLEVBQUE7eUJBQ0w7d0JBQ0QsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFBO3dCQUNULHFCQUFNLFNBQVMsQ0FBQyxXQUFXLENBQUMsc0JBQXNCLEdBQUcsSUFBSSxFQUFFLEVBQUUsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFBOzt3QkFBckYsR0FBRyxHQUFHLFNBQStFO3dCQUMzRixJQUFJLEdBQUcsSUFBSSxJQUFJLENBQUMsT0FBTyxFQUFFOzRCQUNyQixNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsS0FBSztnQ0FDaEIsSUFBTSxJQUFJLEdBQUcsRUFBRSxDQUFDLFlBQVksQ0FBQyxHQUFHLEVBQUUsS0FBSSxDQUFDLFNBQVMsQ0FBQyxFQUFFLEdBQUcsR0FBRyxxQkFBUyxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEtBQUksQ0FBQyxVQUFVLEVBQUUsS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUE7Z0NBQ2pJLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFBO2dDQUMxQixJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFBO2dDQUNyQixJQUFJLENBQUMsTUFBTSxHQUFHLDBCQUFlLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQTtnQ0FDdEMsS0FBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUE7NEJBQzdCLENBQUMsQ0FBQyxDQUFBO3lCQUNMOzs7OztLQUNKO0lBRU8scUNBQWMsR0FBdEI7UUFDSSxPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUM5QixJQUFJLENBQUMsU0FBUyxDQUFDLEdBQUcsRUFBRSxDQUFDLE9BQU8sRUFBRSxDQUFBO1NBQ2pDO1FBQ0QsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFBO0lBQzdCLENBQUM7SUFFRCxXQUFXO0lBQ0csNENBQXFCLEdBQW5DOzs7Ozs7d0JBQ0ksSUFBSSxDQUFDLG9CQUFvQixFQUFFLENBQUE7d0JBQ3JCLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFBO3dCQUNoQyxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsRUFBRSxRQUFRLEdBQUcsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFBO3dCQUMzRixHQUFHLEdBQUcsbUJBQWlCLFVBQVUsdUJBQWtCLFFBQVUsQ0FBQTt3QkFDdkQscUJBQU0sU0FBUyxDQUFDLFdBQVcsQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUE7O3dCQUEzRCxHQUFHLEdBQUcsU0FBcUQ7d0JBQ2pFLElBQUksR0FBRyxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ2YsSUFBSSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUE7NEJBQzVELElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsb0JBQVMsRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxvQkFBUyxDQUFDLENBQUE7NEJBQzlFLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO3lCQUNsQzs7Ozs7S0FDSjtJQUVPLDJDQUFvQixHQUE1QjtRQUNJLE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQ3BDLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxFQUFFLENBQUMsT0FBTyxFQUFFLENBQUE7U0FDdkM7UUFDRCxJQUFJLENBQUMsZUFBZSxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUE7SUFDbkMsQ0FBQztJQUVELFFBQVE7SUFDTSxpQ0FBVSxHQUF4QixVQUF5QixnQkFBMEI7Ozs7Ozs7d0JBQy9DLElBQUksQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFBO3dCQUVaLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQTt3QkFDaEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLEtBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsRUFBeEIsQ0FBd0IsQ0FBQyxDQUFBO3dCQUM3QyxLQUFTLENBQUMsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRTs0QkFDOUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsRUFBRTtnQ0FDcEMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQTs2QkFDL0M7eUJBQ0o7d0JBQ0QscUJBQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsS0FBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFDLEVBQUE7O3dCQUF2RCxTQUF1RCxDQUFBOzs7OztLQUMxRDtJQUVhLGtDQUFXLEdBQXpCLFVBQTBCLElBQWM7Ozs7Ozs7d0JBQ3BDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUU7NEJBQ2xCLHNCQUFPLElBQUksRUFBQTt5QkFDZDt3QkFDRyxLQUFLLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLEVBQWxCLENBQWtCLENBQUMsQ0FBQTt3QkFDckQsSUFBSSxLQUFLLEVBQUU7NEJBQ1AsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBOzRCQUMzQixzQkFBTyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxFQUFBO3lCQUM5Qzt3QkFDVyxxQkFBTSxTQUFTLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsRUFBRSxFQUFFLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBQTs7d0JBQTNFLEdBQUcsR0FBRyxTQUFxRTt3QkFDakYsSUFBSSxDQUFDLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRTs0QkFDMUIsc0JBQU8sSUFBSSxFQUFBO3lCQUNkOzZCQUFNLElBQUksSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxFQUFFOzRCQUN0QyxzQkFBTyxJQUFJLEVBQUEsQ0FBQyxjQUFjO3lCQUM3Qjs2QkFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLFlBQUssSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxDQUFBLEVBQUU7NEJBQzFDLHNCQUFPLElBQUksRUFBQTt5QkFDZDt3QkFDRCxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUE7d0JBQzNCLEtBQUssR0FBRyxFQUFFLENBQUMsWUFBWSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsWUFBWSxDQUFDLHVCQUFhLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEdBQUcsb0JBQVMsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO3dCQUN0SixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQTt3QkFDdkIsc0JBQU8sS0FBSyxFQUFBOzs7O0tBQ2Y7SUFFTyxrQ0FBVyxHQUFuQjtRQUNJLE9BQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQzNCLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUMsS0FBSyxFQUFFLENBQUE7U0FDNUI7UUFDRCxJQUFJLENBQUMsUUFBUSxHQUFHLEVBQUUsQ0FBQTtJQUN0QixDQUFDO0lBRU8saUNBQVUsR0FBbEIsVUFBbUIsSUFBbUI7UUFDbEMsSUFBSSxJQUFJLEVBQUU7WUFDTixJQUFJLENBQUMsS0FBSyxFQUFFLENBQUE7WUFDWixPQUFPLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1NBQ2pDO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxtQ0FBWSxHQUFwQixVQUFxQixFQUFVO1FBQzNCLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixJQUFJLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxLQUFLLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDLE1BQU0sR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFBO1NBQzFEO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCwyQ0FBb0IsR0FBNUI7UUFDSSxJQUFJLElBQUksQ0FBQyxLQUFLLEVBQUU7WUFDWixJQUFJLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksRUFBRSxHQUFHLEdBQUcsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFBO1lBQ3pDLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEtBQUssd0JBQWEsRUFBRTtnQkFDckMsR0FBRyxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQTtnQkFDdEUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUE7YUFDdEM7aUJBQU0sSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFDLFNBQVMsRUFBRSxFQUFFO2dCQUMvQixHQUFHLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFBO2dCQUN0RSxJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxLQUFLLDBCQUFlLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTthQUN0RjtpQkFBTTtnQkFDSCxHQUFHLEdBQUcscUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFBO2dCQUNqRCxJQUFJLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsQ0FBQTthQUN0QztZQUNELElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQywwQkFBZSxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUMsR0FBRyxvQkFBUyxDQUFDLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFBO1NBQ3JGO1FBQ0QsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2pCLElBQU0sR0FBRyxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQTtZQUN2RCxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLENBQUE7WUFDOUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQTtTQUN0RDtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0Qsb0NBQWEsR0FBckIsVUFBc0IsSUFBZTtRQUNqQyxRQUFRO1FBQ1IsdUJBQVUsQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtRQUNwQyxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7UUFDNUIsV0FBVztRQUNYLElBQUksQ0FBQyxxQkFBcUIsRUFBRSxDQUFBO1FBQzVCLElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFBO1FBQ3pCLFVBQVU7UUFDVixJQUFJLENBQUMsS0FBSyxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUM3QyxTQUFTO1FBQ1QsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLHNCQUFzQixFQUFFLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFBO0lBQ3pFLENBQUM7SUFFRCxpQkFBaUI7SUFDVCw0Q0FBcUIsR0FBN0I7UUFDSSxJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFLEVBQUU7U0FDN0I7YUFBTSxJQUFJLElBQUksQ0FBQyxhQUFhLENBQUMsU0FBUyxFQUFFO1lBQ3JDLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFLENBQUE7U0FDOUI7YUFBTTtZQUNILElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxlQUFlLEVBQUUsQ0FBQyxDQUFDLENBQUE7U0FDMUc7SUFDTCxDQUFDO0lBRUQsU0FBUztJQUNELHFDQUFjLEdBQXRCO1FBQ0ksSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUE7UUFDekIsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7UUFDcEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFBO0lBQ2pDLENBQUM7SUFFRCxTQUFTO0lBQ0Qsb0NBQWEsR0FBckI7O1FBQ0ksS0FBSyxJQUFJLEdBQUcsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQzVCLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQUUsS0FBSyxTQUFHLElBQUksQ0FBQyxJQUFJLDBDQUFFLFFBQVEsRUFBRSxDQUFBO1lBQy9ELElBQUksS0FBSyxJQUFJLEtBQUssR0FBRyxpQkFBUyxDQUFDLEtBQUssRUFBRTtnQkFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQTthQUN4QztTQUNKO1FBQ0QsSUFBSSxDQUFDLFNBQVMsR0FBRyxFQUFFLENBQUE7UUFDbkIsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUE7UUFDeEIsSUFBSSxDQUFDLGVBQWUsQ0FBQyxTQUFTLENBQUMsd0JBQWMsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFBO1FBQ3RELElBQUksQ0FBQyxhQUFhLEdBQUcsS0FBSyxDQUFBO0lBQzlCLENBQUM7SUFFTyxpQ0FBVSxHQUFsQjtRQUNJLE9BQU8sSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQzFCLElBQUksQ0FBQyxLQUFLLENBQUMsR0FBRyxFQUFFLENBQUMsS0FBSyxFQUFFLENBQUE7U0FDM0I7UUFDRCxJQUFJLENBQUMsT0FBTyxHQUFHLEVBQUUsQ0FBQTtJQUNyQixDQUFDO0lBRUQsUUFBUTtJQUNNLGdDQUFTLEdBQXZCOzs7OztnQkFFVSxLQUFLLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRSxNQUFNLEdBQUcsRUFBRSxDQUFBO2dCQUNuRCxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsTUFBTSxDQUFDLENBQUMsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxHQUFHLElBQUksRUFBNUIsQ0FBNEIsQ0FBQyxDQUFBO2dCQUNoRCxLQUFTLENBQUMsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRTtvQkFDdkMsQ0FBQyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUE7b0JBQ3ZCLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFNBQVMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUU7d0JBQ3JELElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUE7cUJBQzdDO2lCQUNKO2dCQUNELHNCQUFPLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQWxCLENBQWtCLENBQUMsQ0FBQyxFQUFBOzs7S0FDekQ7SUFFYSxpQ0FBVSxHQUF4QixVQUF5QixJQUFhOzs7Ozs7O3dCQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUMsSUFBSSxFQUFFOzRCQUMzQixzQkFBTyxJQUFJLEVBQUE7eUJBQ2Q7d0JBQ0csSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxJQUFJLENBQUMsR0FBRyxFQUFsQixDQUFrQixDQUFDLENBQUE7d0JBQ25ELElBQUksQ0FBQyxJQUFJLEVBQUU7eUJBQ1Y7NkJBQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxLQUFLLElBQUksQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxJQUFJLENBQUMsY0FBYyxFQUFFLEVBQUU7NEJBQ3hGLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7NEJBQ2xDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUE7eUJBQ3ZCOzZCQUFNLElBQUksSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFOzRCQUNyQixJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBOzRCQUNsQyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFBOzRCQUNwQixzQkFBTyxJQUFJLEVBQUE7eUJBQ2Q7NkJBQU07NEJBQ0gsc0JBQU8sSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsRUFBQTt5QkFDM0I7d0JBQ1cscUJBQU0sU0FBUyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLEVBQUUsRUFBRSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUE7O3dCQUEzRSxHQUFHLEdBQUcsU0FBcUU7d0JBQ2pGLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUU7NEJBQzFCLHNCQUFPLElBQUksRUFBQTt5QkFDZDs2QkFBTSxJQUFJLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFOzRCQUMvQixzQkFBTyxJQUFJLEVBQUEsQ0FBQyxRQUFRO3lCQUN2Qjs2QkFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLFlBQUssSUFBSSxDQUFDLEtBQUssMENBQUUsS0FBSyxDQUFBLEVBQUU7NEJBQzFDLHNCQUFPLElBQUksRUFBQTt5QkFDZDt3QkFFSyxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQTt3QkFDcEIsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsR0FBRyxDQUFDLENBQUE7d0JBQ25FLElBQUksQ0FBQyxJQUFJLElBQUksSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFOzRCQUN2QixzQkFBTyxJQUFJLEVBQUE7eUJBQ2Q7d0JBQ0QsSUFBSSxHQUFHLEVBQUUsQ0FBQyxZQUFZLENBQUMsR0FBRyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxZQUFZLENBQUMsa0JBQVEsQ0FBQyxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLFVBQVUsRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUE7d0JBQ3hHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO3dCQUNyQixJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxJQUFJLENBQUE7d0JBQzdCLHNCQUFPLElBQUksRUFBQTs7OztLQUNkO0lBRU8sZ0NBQVMsR0FBakIsVUFBa0IsSUFBYyxFQUFFLE9BQWlCO1FBQy9DLElBQUksSUFBSSxFQUFFO1lBQ04sT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQTtZQUM3QixJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1NBQ3RCO0lBQ0wsQ0FBQztJQUVELFVBQVU7SUFDRix1Q0FBZ0IsR0FBeEI7O1FBQ0ksT0FBTyxDQUFDLFFBQUMsSUFBSSxDQUFDLGFBQWEsMENBQUUsSUFBSSxDQUFBLENBQUE7SUFDckMsQ0FBQztJQUVELFVBQVU7SUFDRixzQ0FBZSxHQUF2Qjs7UUFDSSxPQUFPLENBQUMsUUFBQyxJQUFJLENBQUMsWUFBWSwwQ0FBRSxJQUFJLENBQUEsQ0FBQTtJQUNwQyxDQUFDO0lBRUQsT0FBTztJQUNDLGdDQUFTLEdBQWpCLFVBQWtCLE1BQWU7UUFBakMsaUJBa0RDO1FBakRHLElBQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUE7UUFDdEMsSUFBTSxJQUFJLEdBQUcsb0JBQU8sQ0FBQyxLQUFLLENBQUMsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsRUFBRSxlQUFlLEdBQUcsNkJBQWtCLENBQUMsVUFBVSxDQUFDLENBQUE7UUFDaEgsSUFBTSxTQUFTLEdBQXNCLGVBQWUsQ0FBQyxJQUFJLENBQUMsZUFBZSxFQUFFLENBQUMsSUFBSSxlQUFlLENBQUMsQ0FBQyxDQUFDLENBQUE7UUFDbEcsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLEVBQUUsT0FBTyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUE7UUFDcEUsV0FBVztRQUNYLHVCQUFVLENBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUNuQyxJQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFdBQVcsRUFBRSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUE7UUFDNUUsSUFBTSxNQUFNLEdBQUcscUJBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxNQUFNLEVBQUUsb0JBQU8sQ0FBQyxLQUFLLENBQUMsZUFBZSxFQUFFLENBQUMsQ0FBQTtRQUN2RixJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxDQUFBO1FBQ2xDLElBQUksRUFBRSxHQUFHLENBQUMsQ0FBQTtRQUNWLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxVQUFDLEVBQUUsRUFBRSxLQUFLLEVBQUUsQ0FBQztZQUNuQyxJQUFNLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQyxHQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsR0FBRyxLQUFLLENBQUMsQ0FBQyxHQUFHLEtBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFBO1lBQ3RFLElBQU0sRUFBRSxHQUFHLENBQUMsR0FBRyxXQUFXLENBQUMsQ0FBQyxFQUFFLEVBQUUsR0FBRyxDQUFDLEdBQUcsV0FBVyxDQUFDLENBQUMsQ0FBQTtZQUNwRCxJQUFNLEtBQUssR0FBRyxFQUFFLENBQUMsSUFBSSxHQUFHLHFCQUFTLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxRQUFRLENBQUMsQ0FBQTtZQUNyRSxJQUFNLEVBQUUsR0FBRyxDQUFDLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQTtZQUN0QixFQUFFLENBQUMsV0FBVyxDQUFDLHFCQUFTLENBQUMsZUFBZSxDQUFDLEtBQUssRUFBRSxLQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQTtZQUNuRSxFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFBO1lBQ25CLElBQUkscUJBQVMsQ0FBQyxRQUFRLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxRQUFRLENBQUMsRUFBRSxFQUFFLEtBQUs7Z0JBQzNDLEVBQUUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUE7YUFDN0M7aUJBQU0sSUFBSSxxQkFBUyxDQUFDLFFBQVEsQ0FBQyxFQUFFLEVBQUUsRUFBRSxFQUFFLFNBQVMsQ0FBQyxFQUFFLEVBQUUsTUFBTTtnQkFDdEQsSUFBTSxHQUFHLEdBQUcsQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsS0FBSyxHQUFHLE9BQU8sQ0FBQTtnQkFDakQsRUFBRSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxDQUFBO2dCQUMzRSxFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO2dCQUNqRCxJQUFJLEtBQUksQ0FBQyxLQUFLLENBQUMsb0JBQW9CLENBQUMsRUFBRSxDQUFDLEVBQUU7b0JBQ3JDLElBQU0sRUFBRSxHQUFHLHFCQUFTLENBQUMsY0FBYyxDQUFDLEtBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRSxFQUFFLEVBQUUsS0FBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO29CQUMzRSxFQUFFLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtvQkFDZCxFQUFFLENBQUMsTUFBTSxHQUFHLEtBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQTtpQkFDckM7YUFDSjtpQkFBTSxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUUsRUFBRSxFQUFFLE1BQU07Z0JBQ2xDLElBQUksRUFBRSxLQUFLLENBQUMsSUFBSSxFQUFFLEtBQUssQ0FBQyxFQUFFO29CQUN0QixFQUFFLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxXQUFXLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtpQkFDaEY7cUJBQU07b0JBQ0gsRUFBRSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLElBQUksQ0FBQTtpQkFDN0M7YUFDSjtpQkFBTSxJQUFJLENBQUMsTUFBTSxFQUFFLEVBQUUsTUFBTTtnQkFDeEIsRUFBRSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsV0FBVyxHQUFHLHFCQUFTLENBQUMsV0FBVyxDQUFDLGNBQWMsQ0FBQyxDQUFBO2dCQUMzRSxJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUksRUFBRSxLQUFLLFNBQVMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLElBQUksRUFBRSxLQUFLLFNBQVMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxFQUFFO29CQUMxRSxFQUFFLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtpQkFDNUI7cUJBQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFLEVBQUU7b0JBQ3pCLEVBQUUsQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLENBQUE7aUJBQ3RCO2FBQ0o7aUJBQU0sSUFBSSxFQUFFLEtBQUssQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLEVBQUUsRUFBRSxRQUFRO2dCQUN2QyxFQUFFLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxXQUFXLENBQUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFBO2FBQ2pHO2lCQUFNO2dCQUNILEVBQUUsQ0FBQyxTQUFTLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUE7YUFDN0M7UUFDTCxDQUFDLENBQUMsQ0FBQTtRQUNGLFFBQVE7UUFDUixxQkFBUyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxDQUFBO0lBQ2hELENBQUM7SUFFTyxrQ0FBVyxHQUFuQixVQUFvQixJQUFZO1FBQzVCLE9BQU8scUJBQVMsQ0FBQyxlQUFlLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQTtJQUMvRCxDQUFDO0lBRUQsT0FBTztJQUNDLGlDQUFVLEdBQWxCLFVBQW1CLGFBQXNCO1FBQ3JDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQy9CLE9BQU07U0FDVDtRQUNELElBQU0sS0FBSyxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDakQsSUFBSSxDQUFDLEtBQUssRUFBRTtZQUNSLE9BQU07U0FDVDthQUFNLElBQUkscUJBQVMsQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLEVBQUU7WUFDbEUsT0FBTTtTQUNUO1FBQ0QsWUFBWTtRQUNaLElBQU0sTUFBTSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFBO1FBQ25FLElBQUkscUJBQVMsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDOUQsSUFBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUM3QjthQUFNO1lBQ0gsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxDQUFBO1NBQ2hDO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCxxQ0FBYyxHQUF0QixVQUF1QixLQUFjO1FBQ2pDLElBQUksQ0FBQyxJQUFJLENBQUMsZUFBZSxFQUFFLElBQUksSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRTtZQUMxRSxPQUFNO1NBQ1Q7UUFDRCxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQTtRQUNqQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxLQUFLLENBQUMsRUFBRSxFQUFFLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLEdBQUcsS0FBSyxHQUFHLElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBOUMsQ0FBOEMsQ0FBQyxFQUFFO1lBQ3ZILElBQUksQ0FBQyxlQUFlLENBQUMsU0FBUyxDQUFDLHdCQUFjLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUE7WUFDaEcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQTtTQUN2QjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0QsdUNBQWdCLEdBQXhCLFVBQXlCLEtBQWM7UUFDbkMsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUUsRUFBRTtZQUNwRCxPQUFNO1NBQ1Q7YUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLEVBQUU7WUFDekMsSUFBSSxDQUFDLGFBQWEsQ0FBQyx3QkFBd0IsQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUNsRCxJQUFJLENBQUMsYUFBYSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLHNCQUFzQixFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUE7U0FDakY7SUFDTCxDQUFDO0lBRUQsTUFBTTtJQUNFLGtDQUFXLEdBQW5CO1FBQ0ksSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRSxJQUFJLElBQUksQ0FBQyxlQUFlLEVBQUUsRUFBRTtZQUN2RSxPQUFNO1NBQ1Q7UUFDRCxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQUksSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLEVBQUUsRUFBRTtZQUN4QixJQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFBO1lBQ3hELElBQUksQ0FBQyxLQUFLLEVBQUU7YUFDWDtpQkFBTSxJQUFJLG9CQUFPLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsRUFBRTtnQkFDckQsdUJBQVUsQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLFFBQVEsRUFBRSxFQUFFLEtBQUssQ0FBQyxDQUFBO2FBQzlDO2lCQUFNO2dCQUNILHVCQUFVLENBQUMsT0FBTyxDQUFDLHdCQUF3QixFQUFFLEtBQUssQ0FBQyxDQUFBO2FBQ3REO1NBQ0o7YUFBTSxJQUFJLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLEVBQUU7WUFDN0IsdUJBQVUsQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUNsRTthQUFNO1lBQ0gsdUJBQVUsQ0FBQyxPQUFPLENBQUMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUN6RDtJQUNMLENBQUM7SUFFTyxzQ0FBZSxHQUF2QjtRQUNJLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ3BCLElBQU0sT0FBSyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7WUFDeEIsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLHNCQUFZLEVBQUUsQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFTLEVBQUUsQ0FBUyxJQUFLLE9BQUEsT0FBSyxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQUssQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQyxFQUF6RSxDQUF5RSxDQUFDLENBQUE7U0FDbko7UUFDRCxPQUFPLElBQUksQ0FBQyxZQUFZLENBQUE7SUFDNUIsQ0FBQztJQUVELE9BQU87SUFDTywrQkFBUSxHQUF0QixVQUF1QixLQUFjOzs7Ozs2QkFDN0IsSUFBSSxDQUFDLFlBQVksRUFBakIsd0JBQWlCO3dCQUNqQixJQUFJLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQTt3QkFDekIsSUFBSSxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGlCQUFpQixFQUFFLElBQUksQ0FBQyxDQUFBO3dCQUM1QyxxQkFBTSxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsS0FBSyxDQUFDLEVBQUE7O3dCQUFoRCxTQUFnRCxDQUFBO3dCQUNoRCxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRTs0QkFDakIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUE7NEJBQzFCLElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsRUFBRSxLQUFLLENBQUMsQ0FBQTt5QkFDaEQ7Ozs7OztLQUVSO0lBRUQsU0FBUztJQUNLLGtDQUFXLEdBQXpCLFVBQTBCLElBQWMsRUFBRSxLQUFjOzs7Ozs7O3dCQUNwRCxJQUFJLEVBQUMsSUFBSSxhQUFKLElBQUksdUJBQUosSUFBSSxDQUFFLElBQUksQ0FBQSxJQUFJLE9BQUEsSUFBSSxDQUFDLElBQUksMENBQUUsUUFBUSxRQUFPLGlCQUFTLENBQUMsU0FBUyxFQUFFOzRCQUM5RCxzQkFBTTt5QkFDVDt3QkFDSyxJQUFJLEdBQUcsSUFBSSxDQUFDLElBQUksQ0FBQTt3QkFDaEIsRUFBRSxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQTt3QkFDdkIsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLEVBQUUsRUFBRSxHQUFHLG9CQUFPLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFTLEVBQUUsQ0FBUyxJQUFLLE9BQUEsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLENBQUMsQ0FBQyxFQUF2RSxDQUF1RSxDQUFDLENBQUE7d0JBQ3JKLHFCQUFNLEVBQUUsQ0FBQyxNQUFNLENBQUMsRUFBRSxFQUFFLEtBQUssQ0FBQyxFQUFBOzt3QkFBbkMsTUFBTSxHQUFHLFNBQTBCO3dCQUN6QyxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxJQUFJLE1BQU0sQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFOzRCQUN6QyxzQkFBTTt5QkFDVDs2QkFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUU7NEJBQ2xDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLElBQUksQ0FBQTt5QkFDbEM7d0JBQ0ssSUFBSSxHQUFHLHFCQUFTLENBQUMsZUFBZSxDQUFDLE1BQU0sRUFBRSxHQUFHLENBQUMsQ0FBQTt3QkFDbkQsSUFBSSxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLFNBQVMsRUFBRSxFQUFFLEtBQUssRUFBRSxNQUFNLEVBQUUsWUFBWSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUE7d0JBQzVFLHFCQUFNLEVBQUUsQ0FBQyxJQUFJLENBQUMsSUFBSSxHQUFHLEtBQUssQ0FBQyxFQUFBOzt3QkFBM0IsU0FBMkIsQ0FBQTt3QkFDM0IsSUFBSSxJQUFJLENBQUMsUUFBUSxFQUFFLEdBQUcsaUJBQVMsQ0FBQyxLQUFLLEVBQUU7NEJBQ25DLElBQUksQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxJQUFJLENBQUMsQ0FBQTt5QkFDbkM7Ozs7O0tBQ0o7SUFFRCxPQUFPO0lBQ08saUNBQVUsR0FBeEI7Ozs7Ozs7O3dCQUNJLElBQUksUUFBQyxJQUFJLENBQUMsWUFBWSwwQ0FBRSxJQUFJLENBQUEsRUFBRTs0QkFDMUIsc0JBQU07eUJBQ1Q7d0JBQ0ssSUFBSSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUE7d0JBQ3hCLEdBQUcsR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQTt3QkFDM0IsT0FBTyxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQTt3QkFDeEMsS0FBSyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQTt3QkFFMUIsS0FBSyxHQUFlLEVBQUUsRUFBRSxVQUFVLEdBQUcsRUFBRSxDQUFBO3dCQUM3QyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7OzRCQUNoQixJQUFJLENBQUMsQ0FBQyxHQUFHLEtBQUssR0FBRyxFQUFFOzZCQUNsQjtpQ0FBTSxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksMENBQUUsT0FBTyxNQUFLLE9BQU8sRUFBRTtnQ0FDcEMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQTs2QkFDaEI7aUNBQU07Z0NBQ0gsVUFBVSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsRUFBRSxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUE7NkJBQ2xDO3dCQUNMLENBQUMsQ0FBQyxDQUFBO3dCQUNJLEtBQUssR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFBO3dCQUMxQixJQUFJLEtBQUssS0FBSyxDQUFDLEVBQUU7NEJBQ2Isc0JBQU07eUJBQ1Q7d0JBRUssTUFBTSxHQUFHLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssRUFBRSxVQUFVLENBQUMsQ0FBQTt3QkFDdEUsZUFBZTt3QkFDZixNQUFNLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQzs0QkFDWCxJQUFNLENBQUMsR0FBRyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBekIsQ0FBeUIsQ0FBQyxDQUFBOzRCQUN6RCxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRTtnQ0FDVixLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQTtnQ0FDbEIsT0FBTyxJQUFJLENBQUE7NkJBQ2Q7NEJBQ0QsT0FBTyxLQUFLLENBQUE7d0JBQ2hCLENBQUMsQ0FBQyxDQUFBO3dCQUNGLElBQUksTUFBTSxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7NEJBQ3JCLHNCQUFNO3lCQUNUO3dCQUNELElBQUksQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFBO3dCQUN6QixJQUFJLENBQUMsSUFBSSxDQUFDLG1CQUFTLENBQUMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLENBQUE7d0JBQzVDLHFCQUFNLE9BQU8sQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDLElBQUssT0FBQSxLQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBN0IsQ0FBNkIsQ0FBQyxDQUFDLEVBQUE7O3dCQUF0RSxTQUFzRSxDQUFBO3dCQUN0RSxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUUsRUFBRTs0QkFDakIsSUFBSSxDQUFDLGFBQWEsR0FBRyxLQUFLLENBQUE7NEJBQzFCLElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQVMsQ0FBQyxpQkFBaUIsRUFBRSxLQUFLLENBQUMsQ0FBQTt5QkFDaEQ7Ozs7O0tBQ0o7SUFFRCxZQUFZO0lBQ0Usa0NBQVcsR0FBekI7Ozs7Ozs7d0JBQ0ksSUFBSSxRQUFDLElBQUksQ0FBQyxZQUFZLDBDQUFFLElBQUksQ0FBQSxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUU7NEJBQ2hELHNCQUFNO3lCQUNUO3dCQUNLLElBQUksR0FBRyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQTt3QkFDN0IsS0FBSyxHQUFHLEVBQUUsQ0FBQTt3QkFDVixTQUFTLEdBQUcsRUFBRSxDQUFBO3dCQUNwQixLQUFTLEdBQUcsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFOzRCQUN0QixJQUFJLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQTs0QkFDMUIsS0FBSyxHQUFHLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQTs0QkFDaEMsU0FBUyxDQUFDLElBQUksQ0FBQztnQ0FDWCxHQUFHLEVBQUUsSUFBSSxDQUFDLEdBQUc7Z0NBQ2IsS0FBSyxFQUFFLEtBQUssQ0FBQyxNQUFNLEVBQUU7NkJBQ3hCLENBQUMsQ0FBQTs0QkFDRixJQUFJLFFBQUMsSUFBSSxDQUFDLEtBQUssMENBQUUsTUFBTSxDQUFDLEtBQUssRUFBQyxFQUFFO2dDQUM1QixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLElBQUksQ0FBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLEtBQUssQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUE7NkJBQ3ZEO3lCQUNKOzZCQUNHLENBQUEsS0FBSyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUEsRUFBaEIsd0JBQWdCO3dCQUNNLHFCQUFNLHFCQUFTLENBQUMsZ0JBQWdCLENBQUMsRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU8sRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFFLENBQUMsRUFBQTs7d0JBQWpILEtBQWdCLFNBQWlHLEVBQS9HLEdBQUcsU0FBQSxFQUFFLElBQUksVUFBQTt3QkFDakIsSUFBSSxHQUFHLEVBQUU7NEJBQ0wsc0JBQU8sdUJBQVUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLEVBQUE7eUJBQ25DOzZCQUFNLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUSxFQUFFLEVBQUU7NEJBQ3pCLHNCQUFNO3lCQUNUOzs7d0JBRUwsdUJBQVUsQ0FBQyxPQUFPLENBQUMsZUFBZSxDQUFDLENBQUE7d0JBQ25DLEtBQVMsR0FBRyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUU7NEJBQzVCLElBQUksQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsT0FBTyxFQUFFLENBQUE7eUJBQ2hDO3dCQUNELElBQUksQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLFNBQVMsR0FBRyxLQUFLLENBQUE7d0JBQ3hDLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsRUFBbkIsQ0FBbUIsQ0FBQyxDQUFBO3dCQUM3QyxJQUFJLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLEVBQW5CLENBQW1CLENBQUMsQ0FBQTt3QkFDNUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxLQUFLLEVBQWhCLENBQWdCLENBQUMsQ0FBQTt3QkFDckQsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBOzs7OztLQUN2QjtJQXp1Q2dCLFlBQVk7UUFEaEMsT0FBTztPQUNhLFlBQVksQ0F5dkNoQztJQUFELG1CQUFDO0NBenZDRCxBQXl2Q0MsQ0F6dkN5QyxFQUFFLENBQUMsWUFBWSxHQXl2Q3hEO2tCQXp2Q29CLFlBQVkiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRXZlbnRUeXBlIGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvRXZlbnRUeXBlXCI7XG5pbXBvcnQgeyBtYXBIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9NYXBIZWxwZXJcIjtcbmltcG9ydCB7IHJlc0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1Jlc0hlbHBlclwiO1xuaW1wb3J0IHsgdmlld0hlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL1ZpZXdIZWxwZXJcIjtcbmltcG9ydCBCdWlsZE9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9CdWlsZE9ialwiO1xuaW1wb3J0IEFyZWFDZW50ZXJNb2RlbCBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9BcmVhQ2VudGVyTW9kZWxcIjtcbmltcG9ydCBBcmVhT2JqIGZyb20gXCIuLi8uLi9tb2RlbC9hcmVhL0FyZWFPYmpcIjtcbmltcG9ydCBCdWlsZENtcHQgZnJvbSBcIi4vQnVpbGRDbXB0XCI7XG5pbXBvcnQgeyBhbmltSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvQW5pbUhlbHBlclwiO1xuaW1wb3J0IFBhd25DbXB0IGZyb20gXCIuL1Bhd25DbXB0XCI7XG5pbXBvcnQgUGF3bk9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9QYXduT2JqXCI7XG5pbXBvcnQgQXJteU9iaiBmcm9tIFwiLi4vLi4vbW9kZWwvYXJlYS9Bcm15T2JqXCI7XG5pbXBvcnQgeyBjYW1lcmFDdHJsIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jYW1lcmEvQ2FtZXJhQ3RybFwiO1xuaW1wb3J0IE1hcFRvdWNoQ21wdCBmcm9tIFwiLi4vY21wdC9NYXBUb3VjaENtcHRcIjtcbmltcG9ydCBDbGlja1RvdWNoQ21wdCBmcm9tIFwiLi4vY21wdC9DbGlja1RvdWNoQ21wdFwiO1xuaW1wb3J0IE5ldEV2ZW50IGZyb20gXCIuLi8uLi9jb21tb24vZXZlbnQvTmV0RXZlbnRcIjtcbmltcG9ydCBIUEJhckNtcHQgZnJvbSBcIi4vSFBCYXJDbXB0XCI7XG5pbXBvcnQgeyBBUkVBX0RJX0NPTE9SX0NPTkYsIEFSRUFfTUFYX1pJTkRFWCwgQlVJTERfU01JVEhZX05JRCwgQ0lUWV9MVU9ZQU5HX0lELCBDSVRZX01BSU5fTklELCBUSUxFX1NJWkUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0NvbnN0YW50XCI7XG5pbXBvcnQgQmFzZUJ1aWxkQ21wdCBmcm9tIFwiLi9CYXNlQnVpbGRDbXB0XCI7XG5pbXBvcnQgeyBQYXduU3RhdGUsIFByZWZlcmVuY2VLZXkgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VudW1zXCI7XG5pbXBvcnQgeyBlY29kZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRUNvZGVcIjtcbmltcG9ydCBTZWFyY2hDaXJjbGUgZnJvbSBcIi4uLy4uL2NvbW1vbi9hc3Rhci9TZWFyY2hDaXJjbGVcIjtcbmltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgU2VsZWN0Q2VsbENtcHQgZnJvbSBcIi4uL2NtcHQvU2VsZWN0Q2VsbENtcHRcIjtcbmltcG9ydCB7IG5ldEhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL05ldEhlbHBlclwiO1xuaW1wb3J0IHsgZ3VpZGVIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HdWlkZUhlbHBlclwiO1xuaW1wb3J0IEFuY2llbnRPYmogZnJvbSBcIi4uLy4uL21vZGVsL21haW4vQW5jaWVudE9ialwiO1xuaW1wb3J0IHsgQXJlYUxhbmRDb2xvckluZm8gfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0RhdGFUeXBlXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEFyZWFXaW5kQ3RybCBleHRlbmRzIG1jLkJhc2VXaW5kQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgbWFwTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L21hcF9uXG4gICAgcHJpdmF0ZSBncmlkTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L21hcF9uL2dyaWRfblxuICAgIHByaXZhdGUgc2tpbGxEaU5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9tYXBfbi9za2lsbF9kaV9uXG4gICAgcHJpdmF0ZSBidWlsZE5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9tYXBfbi9idWlsZF9uXG4gICAgcHJpdmF0ZSBzZWxlY3RQYXduTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L3NlbGVjdF9wYXduX25cbiAgICBwcml2YXRlIHJvbGVOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3Qvcm9sZV9uXG4gICAgcHJpdmF0ZSB0b3BMYXllck5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC90b3BfbGF5ZXJfblxuICAgIHByaXZhdGUgZWRpdEppYW50b3VOb2RlXzogY2MuTm9kZSA9IG51bGwgLy8gcGF0aDovL3Jvb3QvZWRpdF9qaWFudG91X25cbiAgICBwcml2YXRlIHdlYWtHdWlkZU5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC93ZWFrX2d1aWRlX25cbiAgICAvL0BlbmRcblxuICAgIHByaXZhdGUgcmVhZG9ubHkgUEFXTl9TSVpFOiBjYy5WZWMyID0gY2MudjIoMSwgMSlcblxuICAgIHByaXZhdGUgZGlOb2RlOiBjYy5Ob2RlID0gbnVsbFxuICAgIHByaXZhdGUgbWFza05vZGU6IGNjLk5vZGUgPSBudWxsXG4gICAgcHJpdmF0ZSB0b3VjaENtcHQ6IE1hcFRvdWNoQ21wdCA9IG51bGxcblxuICAgIHByaXZhdGUgbW9kZWw6IEFyZWFPYmogPSBudWxsXG4gICAgcHJpdmF0ZSBhcmVhQ2VudGVyOiBBcmVhQ2VudGVyTW9kZWwgPSBudWxsXG4gICAgcHJpdmF0ZSBjZW50cmU6IGNjLlZlYzIgPSBjYy52MigpXG4gICAgcHJpdmF0ZSBwcmVDYW1lcmFab29tUmF0aW86IG51bWJlciA9IDBcbiAgICBwcml2YXRlIGFyZWFTaXplOiBjYy5WZWMyID0gY2MudjIoKSAvL+aImOWcuuWkp+Wwj1xuICAgIHByaXZhdGUgYnVpbGRTaXplOiBjYy5WZWMyID0gY2MudjIoKSAvL+W7uuetkeWMuuWfn1xuICAgIHByaXZhdGUgYXJlYUFjdFNpemU6IGNjLlZlYzIgPSBjYy52MigpIC8v5oiY5Zy655qE5a6e6ZmF5aSn5bCPXG4gICAgcHJpdmF0ZSBib3JkZXJTaXplOiBjYy5WZWMyID0gY2MudjIoKSAvL+WcsOWbvui+ueahhuWuveW6plxuICAgIHByaXZhdGUgYnVpbGRPcmlnaW46IGNjLlZlYzIgPSBjYy52MigpIC8v5bu6562R6LW354K5XG5cbiAgICBwcml2YXRlIHdhbGxzOiBjYy5Ob2RlW10gPSBbXSAvL+WfjuWimeWIl+ihqFxuICAgIHByaXZhdGUgYWxsaUZsYWdzOiBjYy5Ob2RlW10gPSBbXSAvL+iBlOebn+aXl+W4nFxuICAgIHByaXZhdGUgYXJlYU91dERlY29yYXRlOiBjYy5Ob2RlW10gPSBbXSAvL+ijhemlsFxuICAgIHByaXZhdGUgYnVpbGRzOiBCYXNlQnVpbGRDbXB0W10gPSBbXSAvL+W7uuetkeWIl+ihqFxuICAgIHByaXZhdGUgYnVpbGRNYXA6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7fVxuICAgIHByaXZhdGUgcGF3bnM6IFBhd25DbXB0W10gPSBbXSAvL+Wjq+WFteWIl+ihqFxuICAgIHByaXZhdGUgcGF3bk1hcDogYW55ID0ge31cbiAgICBwcml2YXRlIHdhbGxMdk5vZGU6IGNjLk5vZGUgPSBudWxsXG4gICAgcHJpdmF0ZSBocEJhcjogSFBCYXJDbXB0ID0gbnVsbCAvL+ihgOadoVxuXG4gICAgcHJpdmF0ZSBjdXJyRWRpdEJ1aWxkOiBCdWlsZENtcHQgPSBudWxsIC8v5b2T5YmN57yW6L6R55qE5bu6562RXG4gICAgcHJpdmF0ZSBjdXJyRWRpdFBhd246IFBhd25DbXB0ID0gbnVsbCAvL+W9k+WJjee8lui+keeahOWjq+WFtVxuICAgIHByaXZhdGUgc2VhcmNoQ2lyY2xlOiBTZWFyY2hDaXJjbGUgPSBudWxsXG4gICAgcHJpdmF0ZSBpc1Bhd25Nb3ZlaW5nOiBib29sZWFuID0gZmFsc2UgLy/lvZPliY3mmK/lkKblo6vlhbXnp7vliqjkuK1cbiAgICBwcml2YXRlIGVkaXRQYXduczogeyBba2V5OiBzdHJpbmddOiBQYXduQ21wdCB9ID0ge30gLy/nvJbovpHov4fnmoTlo6vlhbXliJfooahcbiAgICBwcml2YXRlIHRlbXBTZWFzb25UeXBlOiBudW1iZXIgPSAwXG5cbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMDogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMTogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMjogY2MuVmVjMiA9IGNjLnYyKClcbiAgICBwcml2YXRlIF90ZW1wX3ZlYzJfMzogY2MuVmVjMiA9IGNjLnYyKClcblxuICAgIHB1YmxpYyBsaXN0ZW5FdmVudE1hcHMoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICB7IFtOZXRFdmVudC5ORVRfUkVDT05ORUNUXTogdGhpcy5vbk5ldFJlY29ubmVjdCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5SRUVOVEVSX0FSRUFfV0lORF06IHRoaXMub25SZWVudGVyQXJlYVdpbmQsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuU0hPV19CVUlMRF9KSUFOVE9VXTogdGhpcy5vblNob3dCdWlsZEppYW50b3UsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuTE9OR19QUkVTU19CVUlMRF06IHRoaXMub25Mb25nUHJlc3NCdWlsZCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5NT1ZFX0JVSUxEXTogdGhpcy5vbk1vdmVCdWlsZCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5DTElDS19FRElUX0JVSUxEX01FTlVdOiB0aGlzLm9uQ2xpY2tFZGl0QnVpbGRNZW51LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkVESVRfUEFXTl9QT1NdOiB0aGlzLm9uRWRpdFBhd25Qb3MsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQ0xJQ0tfRURJVF9QQVdOX01FTlVdOiB0aGlzLm9uQ2xpY2tFZGl0UGF3bk1lbnUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQUREX0JVSUxEXTogdGhpcy5vbkFkZEJ1aWxkLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlJFTU9WRV9CVUlMRF06IHRoaXMub25SZW1vdmVCdWlsZCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQlVJTERfTFZdOiB0aGlzLm9uVXBkYXRlQnVpbGRMdiwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQlVJTERfUE9JTlRdOiB0aGlzLm9uVXBkYXRlQnVpbGRQb2ludCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQlVJTERTXTogdGhpcy5vblVwZGF0ZUJ1aWxkcywgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQVJFQV9IUF06IHRoaXMub25VcGRhdGVBcmVhSHAsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0FOQ0lFTlRfSU5GT106IHRoaXMub25VcGRhdGVBbmNpZW50SW5mbywgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5BRERfQVJNWV06IHRoaXMub25BZGRBcm15LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkFERF9QQVdOXTogdGhpcy5vbkFkZFBhd24sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUkVNT1ZFX0FSTVldOiB0aGlzLm9uUmVtb3ZlQXJteSwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5VUERBVEVfQVJNWV06IHRoaXMub25VcGRhdGVBcm15LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9BTExfQVJNWV06IHRoaXMub25VcGRhdGVBbGxBcm15LCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlJFTU9WRV9QQVdOXTogdGhpcy5vblJlbW92ZVBhd24sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQVJFQV9CQVRUTEVfQkVHSU5dOiB0aGlzLm9uQXJlYUJhdHRsZUJlZ2luLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkFSRUFfQkFUVExFX0VORF06IHRoaXMub25BcmVhQmF0dGxlRW5kLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkFSRUFfTUFJTl9ISVRdOiB0aGlzLm9uQXJlYU1haW5IaXQsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9GTFVUVEVSX0hQXTogdGhpcy5vblBsYXlGbHV0dGVySHAsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9GTFVUVEVSX0FOR0VSXTogdGhpcy5vblBsYXlGbHV0dGVyQW5nZXIsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9CVUxMRVRfRkxZXTogdGhpcy5vblBsYXlCdWxsZXRGbHksIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9CQVRUTEVfRUZGRUNUXTogdGhpcy5vblBsYXlCYXR0bGVFZmZlY3QsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9CQVRUTEVfU0ZYXTogdGhpcy5vblBsYXlCYXR0bGVTZngsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuUExBWV9CQVRUTEVfU0NFTkVfU0hBS0VdOiB0aGlzLm9uUGxheUJhdHRsZVNjZW5lU2hha2UsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuRk9DVVNfUEFXTl06IHRoaXMub25Gb2N1c1Bhd24sIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX0JUX1FVRVVFXTogdGhpcy5vblVwZGF0ZUJ0UXVldWUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX1BBV05fRFJJTExfUVVFVUVdOiB0aGlzLm9uVXBkYXRlUGF3bkRyaWxsUXVldWUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX1BBV05fTFZJTkdfUVVFVUVdOiB0aGlzLm9uVXBkYXRlUGF3bkRyaWxsUXVldWUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuVVBEQVRFX1BBV05fQ1VSSU5HX1FVRVVFXTogdGhpcy5vblVwZGF0ZVBhd25EcmlsbFF1ZXVlLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkNIQU5HRV9TSE9XX0FSTVlfTkFNRV06IHRoaXMub25DaGFuZ2VTaG93QXJteU5hbWUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuQ0hBTkdFX1NIT1dfUEFXTl9FUVVJUF06IHRoaXMub25DaGFuZ2VTaG93UGF3bkVxdWlwLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkNIQU5HRV9QQVdOX0VRVUlQXTogdGhpcy5vbkNoYW5nZVBhd25FcXVpcCwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5DSEFOR0VfUEFXTl9TS0lOXTogdGhpcy5vbkNoYW5nZVBhd25Ta2luLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkNIQU5HRV9QQVdOX1BPUlRSQVlBTF06IHRoaXMub25DaGFuZ2VQYXduUG9ydHJheWFsLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLkZPUkdFX0VRVUlQX0JFR0lOXTogdGhpcy5vbkZvcmdlRXF1aXBCZWdpbiwgZW50ZXI6IHRydWUgfSxcbiAgICAgICAgICAgIHsgW0V2ZW50VHlwZS5GT1JHRV9FUVVJUF9DT01QTEVURV06IHRoaXMub25Gb3JnZUVxdWlwQ29tcGxldGUsIGVudGVyOiB0cnVlIH0sXG4gICAgICAgICAgICB7IFtFdmVudFR5cGUuV0VBS19HVUlERV9TSE9XX05PREVfQ0hPT1NFXTogdGhpcy5vbldlYWtHdWlkZVNob3dOb2RlQ2hvb3NlLCBlbnRlcjogdHJ1ZSB9LFxuICAgICAgICBdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgICAgICB0aGlzLnNldFBhcmFtKHsgaXNDbGVhbjogZmFsc2UgfSlcbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyID0gdGhpcy5nZXRNb2RlbCgnYXJlYUNlbnRlcicpXG4gICAgICAgIHRoaXMuZGlOb2RlID0gdGhpcy5tYXBOb2RlXy5GaW5kQ2hpbGQoJ2RpJylcbiAgICAgICAgdGhpcy5tYXNrTm9kZSA9IHRoaXMubWFwTm9kZV8uRmluZENoaWxkKCdtYXNrJylcbiAgICAgICAgdGhpcy50b3VjaENtcHQgPSB0aGlzLkZpbmRDaGlsZCgndG91Y2gnKS5hZGRDb21wb25lbnQoTWFwVG91Y2hDbXB0KVxuICAgICAgICB0aGlzLnNlbGVjdFBhd25Ob2RlXy5hY3RpdmUgPSBmYWxzZVxuICAgICAgICB0aGlzLmVkaXRKaWFudG91Tm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5ncmlkTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgLy8g5Yqg6L29VUlcbiAgICAgICAgYXdhaXQgdmlld0hlbHBlci5wcmVsb2FkUG5sKCdhcmVhL0FyZWFVSScpXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uUmVhZHkoKSB7XG4gICAgICAgIHRoaXMubW9kZWwgPSBhd2FpdCB0aGlzLmFyZWFDZW50ZXIucmVxQXJlYUJ5SW5kZXgoZ2FtZUhwci53b3JsZC5nZXRMb29rQ2VsbCgpPy5pbmRleCA/PyAtMSlcbiAgICAgICAgaWYgKCF0aGlzLm1vZGVsKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLnRlbXBTZWFzb25UeXBlID0gZ2FtZUhwci53b3JsZC5nZXRTZWFzb25UeXBlKClcbiAgICAgICAgdGhpcy5hcmVhQ2VudGVyLnNldExvb2tBcmVhKHRoaXMubW9kZWwpXG4gICAgICAgIC8vIOWMuuWfn+Wkp+Wwj1xuICAgICAgICB0aGlzLmFyZWFTaXplLnNldCh0aGlzLm1vZGVsLmFyZWFTaXplKVxuICAgICAgICB0aGlzLmJ1aWxkU2l6ZS5zZXQodGhpcy5tb2RlbC5idWlsZFNpemUpXG4gICAgICAgIC8vIOiOt+WPluWcsOWbvui+ueahhueahOWuveW6piDoh7PlsJHpg73mnIky5qC8XG4gICAgICAgIHRoaXMubW9kZWwuZ2V0Qm9yZGVyU2l6ZSh0aGlzLmJvcmRlclNpemUpXG4gICAgICAgIC8vIOmHjeaWsOiuoeeul+WcsOWbvueahOecn+WunuWkp+Wwj1xuICAgICAgICB0aGlzLmJvcmRlclNpemUubXVsKDIsIHRoaXMuYXJlYUFjdFNpemUpLmFkZFNlbGYodGhpcy5hcmVhU2l6ZSlcbiAgICAgICAgLy8g6K6h566X5bu6562R55qE6LW354K5XG4gICAgICAgIHRoaXMubW9kZWwuYnVpbGRPcmlnaW4uYWRkKHRoaXMuYm9yZGVyU2l6ZSwgdGhpcy5idWlsZE9yaWdpbilcbiAgICAgICAgLy8g5YiG5bin5Yib5bu65Zyw5Z2XXG4gICAgICAgIGNvbnN0IHJhbmdlID0gZ2FtZUhwci53b3JsZC5nZXRNYXhUaWxlUmFuZ2UoKSwgY291bnQgPSAocmFuZ2UueCAqIDIgKyAxKSAqIChyYW5nZS55ICogMiArIDEpXG4gICAgICAgIHRoaXMuZGlOb2RlLkl0ZW1zKGNvdW50KVxuICAgICAgICAvLyDliJ3lp4vljJbln47loplcbiAgICAgICAgYXdhaXQgdGhpcy5pbml0V2FsbCgpXG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIocmVlbnRlcjogYm9vbGVhbikge1xuICAgICAgICBpZiAoIXRoaXMubW9kZWwpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuZ290b1dpbmQoZ2FtZUhwci53b3JsZC5nZXRTY2VuZUtleSgpKVxuICAgICAgICAgICAgaWYgKGdhbWVIcHIubmV0LmlzQ29ubmVjdGVkKCkpIHtcbiAgICAgICAgICAgICAgICB2aWV3SGVscGVyLnNob3dNZXNzYWdlQm94KGVjb2RlLlVOS05PV04pXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmJ1aWxkTm9kZV8uRGF0YSA9IHRydWVcbiAgICAgICAgdGhpcy50b3BMYXllck5vZGVfLkRhdGEgPSB0cnVlXG4gICAgICAgIHRoaXMubW9kZWwuc2V0QWN0aXZlKHRydWUpXG4gICAgICAgIC8vIOWIt+aWsOWuneeusee6oueCuVxuICAgICAgICB0aGlzLm1vZGVsLnVwZGF0ZVRyZWFzdXJlUmVkZG90KClcbiAgICAgICAgLy8g6K6+572u5Lit5b+D5L2N572uXG4gICAgICAgIHRoaXMuYXJlYUFjdFNpemUubXVsKDAuNSwgdGhpcy5jZW50cmUpLnN1YlNlbGYoY2MudjIoMC41LCAwLjUpKVxuICAgICAgICAvLyDliJ3lp4vljJbnm7jmnLrkvY3nva5cbiAgICAgICAgY29uc3QgenIgPSBnYW1lSHByLnVzZXIuZ2V0TG9jYWxQcmVmZXJlbmNlRGF0YShQcmVmZXJlbmNlS2V5LkFSRUFfWk9PTV9SQVRJTylcbiAgICAgICAgY2FtZXJhQ3RybC5pbml0KG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQodGhpcy5jZW50cmUpLCB0aGlzLmFyZWFBY3RTaXplLCBjYy5WZWMyLlpFUk8sIHpyKVxuICAgICAgICAvLyDnu5jliLblo6vlhbVcbiAgICAgICAgdGhpcy5pbml0UGF3bnMoKVxuICAgICAgICAvLyDnu5jliLblu7rnrZFcbiAgICAgICAgdGhpcy5pbml0QnVpbGRzKClcbiAgICAgICAgLy8g5Yi35paw5Zyw5Zu+XG4gICAgICAgIHRoaXMudXBkYXRlTWFwKHRoaXMuY2VudHJlLmZsb29yKCkpXG4gICAgICAgIC8vIFVJXG4gICAgICAgIGlmIChyZWVudGVyKSB7XG4gICAgICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlVQREFURV9BUkVBX0JBVFRMRV9USU1FX1VJLCB0aGlzLm1vZGVsLmluZGV4KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdhcmVhL0FyZWFVSScsIHRoaXMubW9kZWwpXG4gICAgICAgIH1cbiAgICAgICAgLy9cbiAgICAgICAgdGhpcy50b3VjaENtcHQuaW5pdCh0aGlzLm9uQ2xpY2tNYXAuYmluZCh0aGlzKSlcbiAgICAgICAgLy9cbiAgICAgICAgZ2FtZUhwci5wbGF5QXJlYUJnbSh0aGlzLm1vZGVsLmlzQmF0dGxlaW5nKCkpXG4gICAgfVxuXG4gICAgcHVibGljIG9uTGVhdmUoKSB7XG4gICAgICAgIGdhbWVIcHIudXNlci5zZXRMb2NhbFByZWZlcmVuY2VEYXRhKFByZWZlcmVuY2VLZXkuQVJFQV9aT09NX1JBVElPLCBjYW1lcmFDdHJsLnpvb21SYXRpbylcbiAgICAgICAgdmlld0hlbHBlci5oaWRlUG5sKCdhcmVhL0FyZWFVSScpXG4gICAgICAgIHRoaXMudG91Y2hDbXB0LmNsZWFuKClcbiAgICAgICAgZ2FtZUhwci53b3JsZC5zZXRMb29rQ2VsbChudWxsKVxuICAgICAgICB0aGlzLmNsZWFuKClcbiAgICAgICAgdGhpcy5jbGVhblBhd25zKClcbiAgICAgICAgcmVzSGVscGVyLmNsZWFuTm9kZUNoaWxkcmVuKHRoaXMuZGlOb2RlKVxuICAgICAgICByZXNIZWxwZXIuY2xlYW5Ob2RlQ2hpbGRyZW4odGhpcy5tYXNrTm9kZSlcbiAgICAgICAgdGhpcy5idWlsZE5vZGVfLnJlbW92ZUFsbENoaWxkcmVuKClcbiAgICAgICAgdGhpcy5idWlsZE5vZGVfLkRhdGEgPSBmYWxzZVxuICAgICAgICB0aGlzLnRvcExheWVyTm9kZV8ucmVtb3ZlQWxsQ2hpbGRyZW4oKVxuICAgICAgICB0aGlzLnRvcExheWVyTm9kZV8uRGF0YSA9IGZhbHNlXG4gICAgICAgIG5vZGVQb29sTWdyLmNsZWFuVXNlQW5kUmVtb3ZlSXRlbXNCeVRhZyh0aGlzLmtleSlcbiAgICAgICAgYW5pbUhlbHBlci5jbGVhbigpXG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuICAgICAgICBhdWRpb01nci5yZWxlYXNlQnlNb2QoJ2J1aWxkJylcbiAgICAgICAgYXVkaW9NZ3IucmVsZWFzZUJ5TW9kKCdwYXduJylcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgb25OZXRSZWNvbm5lY3QoKSB7XG4gICAgICAgIHRoaXMucmVpbml0KClcbiAgICB9XG5cbiAgICBwcml2YXRlIG9uUmVlbnRlckFyZWFXaW5kKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5yZWVudGVyKClcbiAgICB9XG5cbiAgICAvLyDmmL7npLrnvJbovpHlrrblhbfnmoTnrq3lpLRcbiAgICBwcml2YXRlIG9uU2hvd0J1aWxkSmlhbnRvdShpdGVtOiBCdWlsZENtcHQsIGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5lZGl0SmlhbnRvdU5vZGVfLmFjdGl2ZSA9ICEhaXRlbVxuICAgICAgICBpZiAoaXRlbSkge1xuICAgICAgICAgICAgdGhpcy5lZGl0SmlhbnRvdU5vZGVfLnNldFBvc2l0aW9uKGl0ZW0uZ2V0Qm9keU9mZnNldFRvcFBvc2l0aW9uKDY4KSlcbiAgICAgICAgICAgIHRoaXMuZWRpdEppYW50b3VOb2RlXy5Db21wb25lbnQoY2MuTXVsdGlGcmFtZSkuc2V0RnJhbWUoaW5kZXgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDplb/mjInpgInkuK3kuIDkuKrlrrblhbdcbiAgICBwcml2YXRlIG9uTG9uZ1ByZXNzQnVpbGQoaXRlbTogQnVpbGRDbXB0KSB7XG4gICAgICAgIC8vIOiuvue9ruWPr+S7peeCueWHu+mAieaLqeS6hlxuICAgICAgICB0aGlzLmJ1aWxkcy5mb3JFYWNoKG0gPT4gaXRlbS51aWQgIT09IG0udWlkICYmIG0uc2V0Q2FuQ2xpY2tTZWxlY3QodHJ1ZSkpXG4gICAgICAgIHRoaXMucGF3bnMuZm9yRWFjaChtID0+IG0uc2V0Q2FuQ2xpY2soZmFsc2UpKVxuICAgICAgICAvLyDmmL7npLrnvJbovpFVSVxuICAgICAgICB0aGlzLm9wZW5FZGl0QnVpbGQoaXRlbSlcbiAgICB9XG5cbiAgICAvLyDnp7vliqjlu7rnrZFcbiAgICBwcml2YXRlIG9uTW92ZUJ1aWxkKGl0ZW06IEJ1aWxkQ21wdCwgcG9zOiBjYy5WZWMyKSB7XG4gICAgICAgIGNvbnN0IHBvaW50ID0gaXRlbS5nZXRBY3RQb2ludEJ5UGl4ZWwocG9zKVxuICAgICAgICB0aGlzLm1vZGVsLmFtZW5kQnVpbGRQb2ludChwb2ludCwgaXRlbS5kYXRhLnNpemUpIC8v5L+u5q2j5LiA5LiLXG4gICAgICAgIGl0ZW0uc2V0T2Zmc2V0UG9zaXRpb25CeVBvaW50KHBvaW50KVxuICAgICAgICBpdGVtLnVwZGF0ZUVkaXRTdGF0ZSh0aGlzLm1vZGVsLmdldEJ1aWxkR3JvdW5kUG9pbnRNYXAoKSwgcG9pbnQpXG4gICAgfVxuXG4gICAgLy8g54K55Ye757yW6L6R5bu6562R6I+c5Y2VXG4gICAgcHJpdmF0ZSBvbkNsaWNrRWRpdEJ1aWxkTWVudSh0eXBlOiBzdHJpbmcpIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJFZGl0QnVpbGQgfHwgIXRoaXMuY3VyckVkaXRCdWlsZC5kYXRhIC8qIHx8IHRoaXMubW9kZWwuaXNCYXR0bGVpbmcoKSAqLykge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaXRlbSA9IHRoaXMuY3VyckVkaXRCdWlsZFxuICAgICAgICBpZiAodHlwZSA9PT0gJ2NhbmNlbCcpIHsgLy/lj5bmtohcbiAgICAgICAgICAgIGl0ZW0uY2FuY2VsKClcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnb2snKSB7IC8v56Gu5a6aXG4gICAgICAgICAgICBpZiAoaXRlbS5lZGl0U3RhdGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93QWxlcnQoaXRlbS5lZGl0U3RhdGUpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpdGVtLmNvbmZpcm0oaXRlbS5nZXRBY3RQb2ludEJ5UGl4ZWwoaXRlbS5nZXRUZW1wUG9zaXRpb24oKSkpXG4gICAgICAgICAgICAvLyBhdWRpb01nci5wbGF5U0ZYKCdhcmVhL3NvdW5kMDYnKVxuICAgICAgICB9XG4gICAgICAgIHZpZXdIZWxwZXIuaGlkZVBubCgnYXJlYS9FZGl0QnVpbGQnKSAvL+makOiXj+e8lui+kVVJXG4gICAgICAgIHRoaXMuYnVpbGRzLmZvckVhY2gobSA9PiBtLnNldENhbkNsaWNrU2VsZWN0KGZhbHNlKSkgLy/lhbPpl63ngrnlh7vpgInmi6lcbiAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zZXRDYW5DbGljayh0cnVlKSlcbiAgICAgICAgaXRlbS5zeW5jWmluZGV4KCkgLy/lkIzmraV6aW5kZXhcbiAgICAgICAgdGhpcy5jbG9zZUVkaXRCdWlsZCgpXG4gICAgfVxuXG4gICAgLy8g57yW6L6R5aOr5YW1XG4gICAgcHJpdmF0ZSBvbkVkaXRQYXduUG9zKGluZGV4OiBudW1iZXIsIHVpZDogc3RyaW5nKSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsLmluZGV4ICE9PSBpbmRleCB8fCB0aGlzLm1vZGVsLmlzQmF0dGxlaW5nKCkpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHBhd24gPSB0aGlzLnBhd25zLmZpbmQobSA9PiBtLnVpZCA9PT0gdWlkKVxuICAgICAgICBpZiAocGF3bikge1xuICAgICAgICAgICAgdGhpcy5idWlsZHMuZm9yRWFjaChtID0+IG0uc2V0Q2FuQ2xpY2soZmFsc2UpKVxuICAgICAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zZXRDYW5DbGljayhmYWxzZSkpXG4gICAgICAgICAgICB0aGlzLm1hc2tOb2RlLmNoaWxkcmVuLmZvckVhY2gobSA9PiBtLmFjdGl2ZSA9ICEhbS5EYXRhKVxuICAgICAgICAgICAgLy8g6KGM5Yqo5LitXG4gICAgICAgICAgICBwYXduLmRhdGEuYWN0aW9uaW5nID0gdHJ1ZVxuICAgICAgICAgICAgLy8g5pi+56S6cG5sXG4gICAgICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2FyZWEvRWRpdFBhd24nLCBwYXduKVxuICAgICAgICAgICAgdGhpcy5jdXJyRWRpdFBhd24gPSBwYXduXG4gICAgICAgICAgICB0aGlzLnNlbGVjdFBhd25Ob2RlXy5Db21wb25lbnQoU2VsZWN0Q2VsbENtcHQpLm9wZW4odGhpcy5jdXJyRWRpdFBhd24uZ2V0VGVtcFBvc2l0aW9uKCksIHRoaXMuUEFXTl9TSVpFKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g54K55Ye757yW6L6R5aOr5YW155qE6I+c5Y2VXG4gICAgcHJpdmF0ZSBvbkNsaWNrRWRpdFBhd25NZW51KHR5cGU6IHN0cmluZykge1xuICAgICAgICBpZiAoIXRoaXMuY3VyckVkaXRQYXduIHx8ICF0aGlzLmN1cnJFZGl0UGF3bi5kYXRhIHx8IHRoaXMubW9kZWwuaXNCYXR0bGVpbmcoKSkge1xuICAgICAgICAgICAgdmlld0hlbHBlci5oaWRlUG5sKCdhcmVhL0VkaXRQYXduJylcbiAgICAgICAgICAgIHRoaXMuYnVpbGRzLmZvckVhY2gobSA9PiBtLnNldENhbkNsaWNrKHRydWUpKVxuICAgICAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zZXRDYW5DbGljayh0cnVlKSlcbiAgICAgICAgICAgIHRoaXMubWFza05vZGUuY2hpbGRyZW4uZm9yRWFjaChtID0+IG0uYWN0aXZlID0gZmFsc2UpXG4gICAgICAgICAgICB0aGlzLmNsb3NlRWRpdFBhd24oKVxuICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdnYXRoZXInKSB7IC8v6ZuG5ZCIXG4gICAgICAgICAgICB0aGlzLmdhdGhlclBhd24oKVxuICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICdvaycpIHtcbiAgICAgICAgICAgIHRoaXMuZWRpdFBhd25FbmQoKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5re75Yqg5bu6562RXG4gICAgcHJpdmF0ZSBvbkFkZEJ1aWxkKGRhdGE6IEJ1aWxkT2JqKSB7XG4gICAgICAgIGlmIChkYXRhLmFJbmRleCA9PT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzT3duZXIgPSB0aGlzLm1vZGVsLmlzT3duZXIoKVxuICAgICAgICAgICAgdGhpcy5jcmVhdGVCdWlsZChkYXRhKS50aGVuKGl0ZW0gPT4ge1xuICAgICAgICAgICAgICAgIGlmIChpdGVtICYmIHRoaXMuaXNBY3RpdmUoKSAmJiBpc093bmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGJvZHkgPSBpdGVtLmdldEJvZHkoKVxuICAgICAgICAgICAgICAgICAgICAvLyDmkYTlg4/mnLrnp7vliqjliLDov5nkuKrkvY3nva7mnaVcbiAgICAgICAgICAgICAgICAgICAgY2FtZXJhQ3RybC5zZXRUYXJnZXRPbmNlKGJvZHkpXG4gICAgICAgICAgICAgICAgICAgIC8vIOaJq+WFiVxuICAgICAgICAgICAgICAgICAgICAvLyBhbmltSGVscGVyLnBsYXlGbGFzaExpZ2h0KFtib2R5XSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yig6Zmk5bu6562RXG4gICAgcHJpdmF0ZSBvblJlbW92ZUJ1aWxkKGRhdGE6IEJ1aWxkT2JqKSB7XG4gICAgICAgIGlmIChkYXRhLmFJbmRleCA9PT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIGNvbnN0IGJ1aWxkID0gdGhpcy5idWlsZHMucmVtb3ZlKCd1aWQnLCBkYXRhLnVpZClcbiAgICAgICAgICAgIGlmIChidWlsZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuY2xlYW5CdWlsZChidWlsZClcbiAgICAgICAgICAgICAgICBhc3NldHNNZ3IucmVsZWFzZVRlbXBSZXMoZGF0YS5nZXRQcmVmYWJVcmwoKSwgdGhpcy5rZXkpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDlu7rnrZHnrYnnuqdcbiAgICBwcml2YXRlIG9uVXBkYXRlQnVpbGRMdihkYXRhOiBCdWlsZE9iaikge1xuICAgICAgICBpZiAoZGF0YS5hSW5kZXggIT09IHRoaXMubW9kZWw/LmluZGV4KSB7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS51aWQgPT09IHRoaXMubW9kZWwud2FsbC51aWQpIHtcbiAgICAgICAgICAgIHRoaXMudXBkYXRlV2FsbEx2KGRhdGEubHYpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLmJ1aWxkcy5maW5kKG0gPT4gbS51aWQgPT09IGRhdGEudWlkKT8udXBkYXRlTHYoZGF0YS5sdilcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOW7uuetkeS9jee9rlxuICAgIHByaXZhdGUgb25VcGRhdGVCdWlsZFBvaW50KGRhdGE6IEJ1aWxkT2JqKSB7XG4gICAgICAgIGlmIChkYXRhLmFJbmRleCA9PT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuYnVpbGRzLmZpbmQobSA9PiBtLnVpZCA9PT0gZGF0YS51aWQpPy5zeW5jUG9pbnQoKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yi35paw6KGA6YePXG4gICAgcHJpdmF0ZSBvblVwZGF0ZUFyZWFIcChpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmIChpbmRleCA9PT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuaHBCYXI/LmluaXQodGhpcy5tb2RlbClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOmBl+i/ueS/oeaBr1xuICAgIHByaXZhdGUgb25VcGRhdGVBbmNpZW50SW5mbyhkYXRhOiBBbmNpZW50T2JqKSB7XG4gICAgICAgIGlmIChkYXRhLmluZGV4ICE9PSB0aGlzLm1vZGVsPy5pbmRleCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYnVpbGQgPSB0aGlzLmJ1aWxkcy5maW5kKG0gPT4gbS5kYXRhLmlzQW5jaWVudCgpKVxuICAgICAgICBpZiAoYnVpbGQpIHtcbiAgICAgICAgICAgIGJ1aWxkLnVwZGF0ZUx2KGRhdGEubHYpXG4gICAgICAgICAgICBidWlsZC51cGRhdGVVcEx2QW5pbSgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDln47luIJcbiAgICBwcml2YXRlIG9uVXBkYXRlQnVpbGRzKGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKGluZGV4ID09PSB0aGlzLm1vZGVsPy5pbmRleCkge1xuICAgICAgICAgICAgdGhpcy5pbml0QnVpbGRzKClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOa3u+WKoOWGm+mYn1xuICAgIHByaXZhdGUgb25BZGRBcm15KGRhdGE6IEFybXlPYmopIHtcbiAgICAgICAgaWYgKGRhdGEuYUluZGV4ID09PSB0aGlzLm1vZGVsPy5pbmRleCkge1xuICAgICAgICAgICAgZGF0YS5wYXducy5mb3JFYWNoKG0gPT4gdGhpcy5jcmVhdGVQYXduKG0pKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Yig6Zmk5Yab6ZifXG4gICAgcHJpdmF0ZSBvblJlbW92ZUFybXkoZGF0YTogQXJteU9iaikge1xuICAgICAgICBpZiAoZGF0YS5hSW5kZXggPT09IHRoaXMubW9kZWw/LmluZGV4KSB7XG4gICAgICAgICAgICBkYXRhLnBhd25zLmZvckVhY2gobSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgaSA9IHRoaXMucGF3bnMuZmluZEluZGV4KHAgPT4gcC51aWQgPT09IG0udWlkICYmIHAuZGF0YT8uYXJteVVpZCA9PT0gZGF0YS51aWQpXG4gICAgICAgICAgICAgICAgaWYgKGkgIT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2xlYW5QYXduKHRoaXMucGF3bnMuc3BsaWNlKGksIDEpWzBdLCB0cnVlKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmm7TmlrDlhpvpmJ/kv6Hmga9cbiAgICBwcml2YXRlIG9uVXBkYXRlQXJteShkYXRhOiBBcm15T2JqKSB7XG4gICAgICAgIGlmIChkYXRhLmFJbmRleCAhPT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIC8vIOWFiOWIoOmZpOayoeacieeahFxuICAgICAgICBmb3IgKGxldCBpID0gdGhpcy5wYXducy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICAgICAgY29uc3QgbSA9IHRoaXMucGF3bnNbaV1cbiAgICAgICAgICAgIGlmIChtLmRhdGE/LmFybXlVaWQgIT09IGRhdGEudWlkKSB7XG4gICAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIWRhdGEucGF3bnMuaGFzKCd1aWQnLCBtLnVpZCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnBhd25zLnNwbGljZShpLCAxKVxuICAgICAgICAgICAgICAgIHRoaXMuY2xlYW5QYXduKG0sIHRydWUpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZGF0YS5wYXducy5mb3JFYWNoKG0gPT4gdGhpcy5jcmVhdGVQYXduKG0pKVxuICAgIH1cblxuICAgIC8vIOabtOaWsOaJgOacieWGm+mYn1xuICAgIHByaXZhdGUgb25VcGRhdGVBbGxBcm15KGluZGV4OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMubW9kZWwuaW5kZXggPT09IGluZGV4KSB7XG4gICAgICAgICAgICB0aGlzLmluaXRQYXducygpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmt7vliqDlo6vlhbVcbiAgICBwcml2YXRlIG9uQWRkUGF3bihpbmRleDogbnVtYmVyLCBkYXRhOiBQYXduT2JqKSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsLmluZGV4ID09PSBpbmRleCkge1xuICAgICAgICAgICAgdGhpcy5jcmVhdGVQYXduKGRhdGEpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliKDpmaTlo6vlhbVcbiAgICBwcml2YXRlIG9uUmVtb3ZlUGF3bihpbmRleDogbnVtYmVyLCB1aWQ6IHN0cmluZykge1xuICAgICAgICBpZiAodGhpcy5tb2RlbC5pbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuY2xlYW5QYXduKHRoaXMucGF3bnMucmVtb3ZlKCd1aWQnLCB1aWQpLCB0cnVlKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5oiY5paX5byA5aeLXG4gICAgcHJpdmF0ZSBvbkFyZWFCYXR0bGVCZWdpbihpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsLmluZGV4ICE9PSBpbmRleCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgLy8g5YWz6Zet5b2T5YmN5q2j5Zyo57yW6L6R55qE5bu6562RXG4gICAgICAgIHRoaXMuY2hlY2tDb25maXJtRWRpdEJ1aWxkKClcbiAgICAgICAgdGhpcy5jbG9zZUVkaXRCdWlsZCgpXG4gICAgICAgIC8vIOWFs+mXreW9k+WJjeato+WcqOe8lui+keeahOWjq+WFtVxuICAgICAgICB0aGlzLmN1cnJFZGl0UGF3bj8uY2FuY2VsKClcbiAgICAgICAgdGhpcy5jbG9zZUVkaXRQYXduKClcbiAgICAgICAgLy8g5Yid5aeL5YyW6KGA6YePXG4gICAgICAgIHRoaXMuaHBCYXI/LmluaXQodGhpcy5tb2RlbClcbiAgICAgICAgLy8g5Yid5aeL5YyW5aOr5YW1XG4gICAgICAgIHRoaXMuaW5pdFBhd25zKClcbiAgICAgICAgLy8g5oiY5paX5pe26Ze0XG4gICAgICAgIHRoaXMuZW1pdChFdmVudFR5cGUuVVBEQVRFX0FSRUFfQkFUVExFX1RJTUVfVUksIHRoaXMubW9kZWwuaW5kZXgpXG4gICAgICAgIC8vXG4gICAgICAgIGdhbWVIcHIucGxheUFyZWFCZ20odHJ1ZSlcbiAgICB9XG5cbiAgICAvLyDmiJjmlpfnu5PmnZ9cbiAgICBwcml2YXRlIG9uQXJlYUJhdHRsZUVuZChpbmRleDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsPy5pbmRleCAhPT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLmFyZWFTaXplLmVxdWFscyh0aGlzLm1vZGVsLmFyZWFTaXplKSB8fCAhdGhpcy5idWlsZFNpemUuZXF1YWxzKHRoaXMubW9kZWwuYnVpbGRTaXplKSkgeyAvL+WmguaenOWkp+Wwj+S4jeS4gOagt+mcgOimgemHjeaWsOe7mOWItlxuICAgICAgICAgICAgcmV0dXJuIHRoaXMucmVlbnRlcigpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5ocEJhcj8uaW5pdCh0aGlzLm1vZGVsKVxuICAgICAgICB0aGlzLmluaXRCdWlsZHModHJ1ZSlcbiAgICAgICAgdGhpcy5pbml0UGF3bnMoKVxuICAgICAgICAvLyDmiJjmlpfml7bpl7RcbiAgICAgICAgdGhpcy5lbWl0KEV2ZW50VHlwZS5VUERBVEVfQVJFQV9CQVRUTEVfVElNRV9VSSwgdGhpcy5tb2RlbC5pbmRleClcbiAgICAgICAgLy9cbiAgICAgICAgZ2FtZUhwci5wbGF5QXJlYUJnbShmYWxzZSlcbiAgICB9XG5cbiAgICAvLyDlj5fliLDkvKTlrrNcbiAgICBwcml2YXRlIG9uQXJlYU1haW5IaXQoZGF0YTogYW55KSB7XG4gICAgICAgIGlmIChkYXRhLmluZGV4ID09PSB0aGlzLm1vZGVsPy5pbmRleCkge1xuICAgICAgICAgICAgaWYgKHRoaXMubW9kZWwuaXNCYXR0bGVpbmcoKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuaHBCYXI/LnBsYXkoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYW5pbUhlbHBlci5wbGF5Rmx1dHRlckhwKHsgdHlwZTogJ2lzRGFtYWdlJywgdmFsdWU6IGRhdGEudmFsdWUgfSwgdGhpcy50b3BMYXllck5vZGVfLCB0aGlzLmdldFBpeGVsQnlQb2ludChkYXRhLnBvaW50KSwgdGhpcy5rZXkpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmkq3mlL7po5jooYBcbiAgICBwcml2YXRlIG9uUGxheUZsdXR0ZXJIcChkYXRhOiBhbnkpIHtcbiAgICAgICAgaWYgKHRoaXMubW9kZWw/LmluZGV4ID09PSBkYXRhLmluZGV4KSB7XG4gICAgICAgICAgICBjb25zdCBwb3MgPSBkYXRhLnBvaW50ID8gdGhpcy5nZXRQaXhlbEJ5UG9pbnQoZGF0YS5wb2ludCkuY2xvbmUoKSA6IHRoaXMucGF3bnMuZmluZChtID0+IG0udWlkID09PSBkYXRhLnVpZCk/LmdldFBvc2l0aW9uKClcbiAgICAgICAgICAgIGlmIChwb3MpIHtcbiAgICAgICAgICAgICAgICBhbmltSGVscGVyLnJlYWR5UGxheUZsdXR0ZXJIcChkYXRhLCBwb3MsIHRoaXMudG9wTGF5ZXJOb2RlXywgdGhpcy5rZXkpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmkq3mlL7lop7liqDmgJLmsJRcbiAgICBwcml2YXRlIG9uUGxheUZsdXR0ZXJBbmdlcihkYXRhOiBhbnkpIHtcbiAgICAgICAgaWYgKHRoaXMubW9kZWw/LmluZGV4ID09PSBkYXRhLmluZGV4KSB7XG4gICAgICAgICAgICBjb25zdCBwYXduID0gdGhpcy5wYXducy5maW5kKG0gPT4gbS51aWQgPT09IGRhdGEudWlkKVxuICAgICAgICAgICAgaWYgKHBhd24pIHtcbiAgICAgICAgICAgICAgICBhbmltSGVscGVyLnBsYXlGbHV0dGVyQW5nZXIoZGF0YS52YWx1ZSwgdGhpcy50b3BMYXllck5vZGVfLCBwYXduLmdldFBvc2l0aW9uKCksIHRoaXMua2V5KVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5pKt5pS+5a2Q5by56aOe6KGMXG4gICAgcHJpdmF0ZSBvblBsYXlCdWxsZXRGbHkoZGF0YTogYW55KSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsPy5pbmRleCA9PT0gZGF0YS5pbmRleCkge1xuICAgICAgICAgICAgZGF0YS5zdGFydFBvcyA9IHRoaXMuZ2V0UGl4ZWxCeVBvaW50KGRhdGEuc3RhcnRQb2ludCkuY2xvbmUoKVxuICAgICAgICAgICAgZGF0YS50YXJnZXRQb3MgPSB0aGlzLmdldFBpeGVsQnlQb2ludChkYXRhLnRhcmdldFBvaW50KS5jbG9uZSgpXG4gICAgICAgICAgICBhbmltSGVscGVyLnBsYXlCdWxsZXRGbHkoZGF0YSwgdGhpcy50b3BMYXllck5vZGVfLCB0aGlzLmtleSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaSreaUvuaImOaWl+eJueaViFxuICAgIHByaXZhdGUgb25QbGF5QmF0dGxlRWZmZWN0KGRhdGE6IGFueSkge1xuICAgICAgICBpZiAodGhpcy5tb2RlbD8uaW5kZXggPT09IGRhdGEuaW5kZXgpIHtcbiAgICAgICAgICAgIGRhdGEucG9zID0gdGhpcy5nZXRQaXhlbEJ5UG9pbnQoZGF0YS5wb2ludCkuY2xvbmUoKVxuICAgICAgICAgICAgbGV0IHJvb3QgPSB0aGlzLnNraWxsRGlOb2RlX1xuICAgICAgICAgICAgaWYgKGRhdGEucm9vdCA9PT0gJ3RvcCcpIHtcbiAgICAgICAgICAgICAgICByb290ID0gdGhpcy50b3BMYXllck5vZGVfXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEucm9vdCA9PT0gJ3JvbGUnKSB7XG4gICAgICAgICAgICAgICAgcm9vdCA9IHRoaXMucm9sZU5vZGVfXG4gICAgICAgICAgICAgICAgZGF0YS56SW5kZXggPSAoQVJFQV9NQVhfWklOREVYIC0gKGRhdGEucG9zLnkgLSB0aGlzLmJvcmRlclNpemUueSAqIFRJTEVfU0laRSkpICogMTAgKyAzXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhbmltSGVscGVyLnBsYXlCYXR0bGVFZmZlY3QoZGF0YSwgcm9vdCwgdGhpcy5rZXkpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmkq3mlL7pn7PmlYhcbiAgICBwcml2YXRlIG9uUGxheUJhdHRsZVNmeChpbmRleDogbnVtYmVyLCB1cmw6IHN0cmluZywgZGF0YTogYW55KSB7XG4gICAgICAgIGlmICh0aGlzLm1vZGVsPy5pbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIGF1ZGlvTWdyLnBsYXlTRlgodXJsLCBkYXRhKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5pKt5pS+5bGP5bmV5oqW5YqoXG4gICAgcHJpdmF0ZSBvblBsYXlCYXR0bGVTY2VuZVNoYWtlKGluZGV4OiBudW1iZXIsIHRpbWU6IG51bWJlcikge1xuICAgICAgICBpZiAodGhpcy5tb2RlbD8uaW5kZXggPT09IGluZGV4KSB7XG4gICAgICAgICAgICBjYW1lcmFDdHJsLnNoYWtlKHRpbWUpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDogZrnhKblo6vlhbVcbiAgICBwcml2YXRlIG9uRm9jdXNQYXduKGRhdGE6IGFueSkge1xuICAgICAgICBpZiAodGhpcy5tb2RlbD8uaW5kZXggIT09IGRhdGE/LmluZGV4KSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfS8qICBlbHNlIGlmIChjYW1lcmFDdHJsLmdldE5vRHJhZ1RpbWUoKSA8IDUwMDApIHtcbiAgICAgICAgICAgIHJldHVybiAvL+WkmuS5heayoeaLluWKqOaJjeWPr+S7peiBmueEplxuICAgICAgICB9ICovXG4gICAgICAgIGNvbnN0IHBvcyA9IHRoaXMuZ2V0UGl4ZWxCeVBvaW50KGRhdGEucG9pbnQpXG4gICAgICAgIGlmIChjYW1lcmFDdHJsLmlzSW5TY3JlZW5SYW5nZUJ5V29ybGQocG9zKSkge1xuICAgICAgICAgICAgY2FtZXJhQ3RybC5tb3ZlVG8oMC41LCBwb3MsIHRydWUpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDkv67lu7rpmJ/liJdcbiAgICBwcml2YXRlIG9uVXBkYXRlQnRRdWV1ZSgpIHtcbiAgICAgICAgdGhpcy5idWlsZHMuZm9yRWFjaChtID0+IG0udXBkYXRlVXBMdkFuaW0oKSlcbiAgICB9XG5cbiAgICAvLyDliLfmlrDorq3nu4PpmJ/liJdcbiAgICBwcml2YXRlIG9uVXBkYXRlUGF3bkRyaWxsUXVldWUoaW5kZXg6IG51bWJlcikge1xuICAgICAgICBpZiAodGhpcy5tb2RlbC5pbmRleCA9PT0gaW5kZXgpIHtcbiAgICAgICAgICAgIHRoaXMuYnVpbGRzLmZvckVhY2gobSA9PiBtLnVwZGF0ZURyaWxsUGF3bigpKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5YiH5o2i5pi+56S65Yab6Zif55qE5ZCN5a2XXG4gICAgcHJpdmF0ZSBvbkNoYW5nZVNob3dBcm15TmFtZSh2YWw6IGJvb2xlYW4pIHtcbiAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zaG93QXJteU5hbWUodmFsKSlcbiAgICB9XG5cbiAgICAvLyDliIfmjaLmmL7npLrlo6vlhbXoo4XlpIdcbiAgICBwcml2YXRlIG9uQ2hhbmdlU2hvd1Bhd25FcXVpcCh2YWw6IGJvb2xlYW4pIHtcbiAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zaG93UGF3bkVxdWlwKHZhbCkpXG4gICAgfVxuXG4gICAgLy8g5YiH5o2i5aOr5YW16KOF5aSHXG4gICAgcHJpdmF0ZSBvbkNoYW5nZVBhd25FcXVpcChkYXRhOiBQYXduT2JqKSB7XG4gICAgICAgIHRoaXMucGF3bnMuZmluZChtID0+IG0udWlkID09PSBkYXRhLnVpZCk/LnVwZGF0ZVNob3dQYXduRXF1aXAoKVxuICAgIH1cblxuICAgIC8vIOWIh+aNouWjq+WFteearuiCpFxuICAgIHByaXZhdGUgb25DaGFuZ2VQYXduU2tpbihkYXRhOiBQYXduT2JqKSB7XG4gICAgICAgIGNvbnN0IGkgPSB0aGlzLnBhd25zLmZpbmRJbmRleChtID0+IG0udWlkID09PSBkYXRhLnVpZClcbiAgICAgICAgaWYgKGkgIT09IC0xKSB7XG4gICAgICAgICAgICBjb25zdCBwYXduID0gdGhpcy5wYXduc1tpXVxuICAgICAgICAgICAgaWYgKHBhd24uY3VyU2tpbklkICE9PSBkYXRhLnNraW5JZCkge1xuICAgICAgICAgICAgICAgIHRoaXMucGF3bnMuc3BsaWNlKGksIDEpXG4gICAgICAgICAgICAgICAgdGhpcy5jbGVhblBhd24ocGF3bilcbiAgICAgICAgICAgICAgICB0aGlzLmNyZWF0ZVBhd24oZGF0YSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLmJ1aWxkcy5mb3JFYWNoKG0gPT4gbS51cGRhdGVEcmlsbFBhd24oKSlcbiAgICB9XG5cbiAgICAvLyDljJbouqvoi7Hpm4RcbiAgICBwcml2YXRlIG9uQ2hhbmdlUGF3blBvcnRyYXlhbChkYXRhOiBQYXduT2JqKSB7XG4gICAgICAgIGlmICghZGF0YS5wb3J0cmF5YWwpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGkgPSB0aGlzLnBhd25zLmZpbmRJbmRleChtID0+IG0udWlkID09PSBkYXRhLnVpZClcbiAgICAgICAgaWYgKGkgIT09IC0xKSB7XG4gICAgICAgICAgICBjb25zdCBwYXduID0gdGhpcy5wYXduc1tpXVxuICAgICAgICAgICAgaWYgKHBhd24uY3VyUG9ydHJheWFsSWQgIT09IGRhdGEucG9ydHJheWFsLmlkKSB7XG4gICAgICAgICAgICAgICAgcGF3bi5wbGF5QXZhdGFySGVyb0FuaW0oZGF0YS5wb3J0cmF5YWwuaWQpLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGhpcy5pc0FjdGl2ZSgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhd25zLnNwbGljZShpLCAxKVxuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jbGVhblBhd24ocGF3bilcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY3JlYXRlUGF3bihkYXRhKVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOaJk+mAoOijheWkh+W8gOWni1xuICAgIHByaXZhdGUgb25Gb3JnZUVxdWlwQmVnaW4oKSB7XG4gICAgICAgIHRoaXMuYnVpbGRzLmZpbmQobSA9PiBtLmlkID09PSBCVUlMRF9TTUlUSFlfTklEKT8udXBkYXRlRm9yZ2VFcXVpcCgpXG4gICAgfVxuXG4gICAgLy8g5omT6YCg6KOF5aSH5a6M5oiQXG4gICAgcHJpdmF0ZSBvbkZvcmdlRXF1aXBDb21wbGV0ZSgpIHtcbiAgICAgICAgdGhpcy5idWlsZHMuZmluZChtID0+IG0uaWQgPT09IEJVSUxEX1NNSVRIWV9OSUQpPy51cGRhdGVGb3JnZUVxdWlwKClcbiAgICB9XG5cbiAgICAvLyDoi6XlvJXlr7xcbiAgICBwcml2YXRlIG9uV2Vha0d1aWRlU2hvd05vZGVDaG9vc2UoZGF0YTogYW55KSB7XG4gICAgICAgIGlmIChkYXRhLnNjZW5lID09PSAnYXJlYScpIHtcbiAgICAgICAgICAgIGd1aWRlSGVscGVyLnBsYXlXZWFrR3VpZGVGaW5nZXIoZGF0YSwgdGhpcy53ZWFrR3VpZGVOb2RlXywgdGhpcy5rZXkpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgaXNBY3RpdmUoKSB7IHJldHVybiB0aGlzLmlzVmFsaWQgJiYgISF0aGlzLm1vZGVsPy5hY3RpdmUgfVxuXG4gICAgcHJpdmF0ZSBnZXRQaXhlbEJ5UG9pbnQocG9pbnQ6IGNjLlZlYzIpIHtcbiAgICAgICAgcmV0dXJuIHBvaW50ICYmIG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQocG9pbnQuYWRkKHRoaXMuYm9yZGVyU2l6ZSwgdGhpcy5fdGVtcF92ZWMyXzMpKVxuICAgIH1cblxuICAgIHByaXZhdGUgZ2V0UG9pbnRCeVBpeGVsKHBpeGVsOiBjYy5WZWMyKSB7XG4gICAgICAgIHJldHVybiBwaXhlbCAmJiBtYXBIZWxwZXIuZ2V0UG9pbnRCeVBpeGVsKHBpeGVsKS5zdWJTZWxmKHRoaXMuYm9yZGVyU2l6ZSlcbiAgICB9XG5cbiAgICBwcml2YXRlIGdldEJ1aWxkUGl4ZWxCeVBvaW50KHBvaW50OiBjYy5WZWMyKSB7XG4gICAgICAgIHJldHVybiBtYXBIZWxwZXIuZ2V0UGl4ZWxCeVBvaW50KHBvaW50LmFkZCh0aGlzLmJ1aWxkT3JpZ2luLCB0aGlzLl90ZW1wX3ZlYzJfMSksIHRoaXMuX3RlbXBfdmVjMl8xKVxuICAgIH1cblxuICAgIHByaXZhdGUgY2xlYW4oKSB7XG4gICAgICAgIHRoaXMuYXJlYUNlbnRlci5zZXRMb29rQXJlYShudWxsKVxuICAgICAgICB0aGlzLm1vZGVsPy5zZXRBY3RpdmUoZmFsc2UpXG4gICAgICAgIHRoaXMubW9kZWwgPSBudWxsXG4gICAgICAgIGdhbWVIcHIuY2xlYW5QYXduQXN0YXJNYXAoKVxuICAgICAgICB0aGlzLnNlYXJjaENpcmNsZSA9IG51bGxcbiAgICAgICAgdGhpcy5jbGVhbldhbGxzKClcbiAgICAgICAgdGhpcy5jbGVhbkFsbGlGbGFncygpXG4gICAgICAgIHRoaXMuY2xlYW5BcmVhT3V0RGVjb3JhdGUoKVxuICAgICAgICB0aGlzLmNoZWFuQnVpbGRzKClcbiAgICAgICAgLy8gdGhpcy5jbGVhblBhd25zKClcbiAgICAgICAgdGhpcy5jbG9zZUVkaXRCdWlsZCgpXG4gICAgICAgIHRoaXMuY2xvc2VFZGl0UGF3bigpXG4gICAgfVxuXG4gICAgLy8g6YeN6L+e5LmL5ZCO55qE5Yid5aeL5YyWXG4gICAgcHJpdmF0ZSBhc3luYyByZWluaXQoKSB7XG4gICAgICAgIGdhbWVIcHIuY2xlYW5QYXduQXN0YXJNYXAoKVxuICAgICAgICAvLyDlhbPpl63lvZPliY3mraPlnKjnvJbovpHnmoTlu7rnrZFcbiAgICAgICAgdGhpcy5jaGVja0NvbmZpcm1FZGl0QnVpbGQoKVxuICAgICAgICB0aGlzLmNsb3NlRWRpdEJ1aWxkKClcbiAgICAgICAgLy8g5YWz6Zet5b2T5YmN5q2j5Zyo57yW6L6R55qE5aOr5YW1XG4gICAgICAgIHRoaXMuY3VyckVkaXRQYXduPy5jYW5jZWwoKVxuICAgICAgICB0aGlzLmNsb3NlRWRpdFBhd24oKVxuICAgICAgICAvLyDph43mlrDojrflj5ZcbiAgICAgICAgY29uc3Qgd29ybGQgPSBnYW1lSHByLndvcmxkXG4gICAgICAgIHRoaXMubW9kZWwgPSBhd2FpdCB0aGlzLmFyZWFDZW50ZXIucmVxQXJlYUJ5SW5kZXgod29ybGQuZ2V0TG9va0NlbGwoKT8uaW5kZXggPz8gLTEsIHRydWUpXG4gICAgICAgIGlmICghdGhpcy5tb2RlbCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuZ290b1dpbmQod29ybGQuZ2V0U2NlbmVLZXkoKSlcbiAgICAgICAgfSBlbHNlIGlmICghdGhpcy5hcmVhU2l6ZS5lcXVhbHModGhpcy5tb2RlbC5hcmVhU2l6ZSkgfHwgIXRoaXMuYnVpbGRTaXplLmVxdWFscyh0aGlzLm1vZGVsLmJ1aWxkU2l6ZSkpIHsgLy/lpoLmnpzlpKflsI/kuI3kuIDmoLfpnIDopoHph43mlrDnu5jliLZcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnJlZW50ZXIoKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMubW9kZWwuc2V0QWN0aXZlKHRydWUpXG4gICAgICAgIC8vIOWIt+aWsOWcsOWbvlxuICAgICAgICB0aGlzLnVwZGF0ZU1hcCh0aGlzLmNlbnRyZS5mbG9vcigpKVxuICAgICAgICAvLyDln47lopnnrYnnuqdcbiAgICAgICAgdGhpcy5tb2RlbC53YWxsICYmIHRoaXMudXBkYXRlV2FsbEx2KHRoaXMubW9kZWwud2FsbC5sdilcbiAgICAgICAgLy8g6KGA5p2hXG4gICAgICAgIHRoaXMuaHBCYXI/LmluaXQodGhpcy5tb2RlbClcbiAgICAgICAgLy8g57uY5Yi25bu6562RXG4gICAgICAgIHRoaXMuaW5pdEJ1aWxkcygpXG4gICAgICAgIC8vIOe7mOWItuWjq+WFtVxuICAgICAgICB0aGlzLmluaXRQYXducygpXG4gICAgICAgIC8vIOaImOaWl+aXtumXtFxuICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlJFSU5JVF9BUkVBX1VJLCB0aGlzLm1vZGVsKVxuICAgIH1cblxuICAgIC8vIOmHjeaWsOe7mOWItlxuICAgIHByaXZhdGUgYXN5bmMgcmVlbnRlcigpIHtcbiAgICAgICAgdGhpcy5lbWl0KG1jLkV2ZW50LlJFQURZX0JFR0lOX1dJTkQpXG4gICAgICAgIHRoaXMuY2xlYW4oKVxuICAgICAgICBhd2FpdCB0aGlzLm9uUmVhZHkoKVxuICAgICAgICB0aGlzLmVtaXQobWMuRXZlbnQuUkVBRFlfRU5EX1dJTkQpXG4gICAgICAgIHRoaXMub25FbnRlcih0cnVlKVxuICAgICAgICB0aGlzLmVtaXQoRXZlbnRUeXBlLlVQREFURV9SRUVOVEVSX0FSRUEpXG4gICAgfVxuXG4gICAgLy8g5Yid5aeL5YyW5Z+O5aKZXG4gICAgcHJpdmF0ZSBhc3luYyBpbml0V2FsbCgpIHtcbiAgICAgICAgdGhpcy5jbGVhbldhbGxzKClcbiAgICAgICAgaWYgKCF0aGlzLm1vZGVsLmlzQm9zcygpKSB7XG4gICAgICAgICAgICAvLyDliqDovb3ooYDmnaFcbiAgICAgICAgICAgIGxldCBwZmIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXMoJ3dhbGwvV0FMTF9IUF9CQVInLCBjYy5QcmVmYWIsIHRoaXMua2V5KVxuICAgICAgICAgICAgaWYgKCFwZmIgfHwgIXRoaXMuaXNWYWxpZCkge1xuICAgICAgICAgICAgICAgIHJldHVyblxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgbGV0IG5vZGUgPSBjYy5pbnN0YW50aWF0ZTIocGZiLCB0aGlzLnJvbGVOb2RlXylcbiAgICAgICAgICAgIHRoaXMuaHBCYXIgPSBub2RlLmFkZENvbXBvbmVudChIUEJhckNtcHQpLmluaXQodGhpcy5tb2RlbClcbiAgICAgICAgICAgIC8vIOe7mOe9keagvFxuICAgICAgICAgICAgY29uc3Qgc2l6ZSA9IHRoaXMubW9kZWwuYnVpbGRTaXplXG4gICAgICAgICAgICB2aWV3SGVscGVyLmRyYXdHcmlkKHRoaXMuZ3JpZE5vZGVfLkNvbXBvbmVudChjYy5HcmFwaGljcyksIGNjLnYyKHNpemUueCAtIDIsIHNpemUueSAtIDIpLCBjYy52Mih0aGlzLmJ1aWxkT3JpZ2luLnggKyAxLCB0aGlzLmJ1aWxkT3JpZ2luLnkgKyAxKSlcbiAgICAgICAgICAgIHRoaXMuZ3JpZE5vZGVfLmFjdGl2ZSA9IGZhbHNlXG4gICAgICAgICAgICBpZiAodGhpcy5tb2RlbC53YWxsKSB7XG4gICAgICAgICAgICAgICAgLy8g5Yid5aeL5YyW5aKZIOmBl+i/ueS4jee7mOWItuWfjuWimVxuICAgICAgICAgICAgICAgIGlmICghdGhpcy5tb2RlbC5pc0FuY2llbnQoKSkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCB0aGlzLmNyZWF0ZVdhbGwoKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyDliqDovb3kuIDkuKrlopnnmoTnrYnnuqdcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5tb2RlbC53YWxsLmx2ID4gMCkge1xuICAgICAgICAgICAgICAgICAgICBwZmIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXMoJ2J1aWxkL0JVSUxEX0xWJywgY2MuUHJlZmFiLCB0aGlzLmtleSlcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBmYiAmJiB0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5vZGUgPSB0aGlzLndhbGxMdk5vZGUgPSBjYy5pbnN0YW50aWF0ZTIocGZiLCB0aGlzLnJvbGVOb2RlXylcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlV2FsbEx2KHRoaXMubW9kZWwud2FsbC5sdilcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMudXBkYXRlV2FsbEhwUG9zaXRpb24oKVxuICAgICAgICB9XG4gICAgICAgIC8vIOWIm+W7uuWMuuWfn+WklueahOijhemlsFxuICAgICAgICBhd2FpdCB0aGlzLmNyZWF0ZUFyZWFPdXREZWNvcmF0ZSgpXG4gICAgICAgIC8vIOWIm+W7uuiBlOebn+aXl+W4nFxuICAgICAgICBhd2FpdCB0aGlzLmNyZWF0ZUFsbGlGbGFncyhnYW1lSHByLmdldFBsYXllckFsbGlJY29uKHRoaXMubW9kZWwub3duZXIpKVxuICAgIH1cblxuICAgIHByaXZhdGUgYXN5bmMgY3JlYXRlV2FsbCgpIHtcbiAgICAgICAgd2hpbGUgKHRoaXMud2FsbHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgdGhpcy53YWxscy5wb3AoKS5kZXN0cm95KClcbiAgICAgICAgfVxuICAgICAgICB0aGlzLndhbGxzLmxlbmd0aCA9IDBcbiAgICAgICAgY29uc3Qgc2tpbklkID0gMFxuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbCh0aGlzLm1vZGVsLndhbGxzLm1hcChhc3luYyAobSkgPT4ge1xuICAgICAgICAgICAgbGV0IHVybCA9IGB3YWxsLyR7c2tpbklkfS9XQUxMXyR7c2tpbklkfV8ke20udHlwZX1fJHttLmRpcn1gXG4gICAgICAgICAgICBpZiAobS5pbmRleCkge1xuICAgICAgICAgICAgICAgIHVybCArPSAnXycgKyBtLmluZGV4XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBwZmIgPSBhd2FpdCBhc3NldHNNZ3IubG9hZFRlbXBSZXModXJsLCBjYy5QcmVmYWIsIHRoaXMua2V5KVxuICAgICAgICAgICAgaWYgKHBmYiAmJiB0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBub2RlID0gY2MuaW5zdGFudGlhdGUyKHBmYiwgdGhpcy5idWlsZE5vZGVfKVxuICAgICAgICAgICAgICAgIG5vZGUuRGF0YSA9IHRoaXMubW9kZWwud2FsbFxuICAgICAgICAgICAgICAgIG5vZGUuc2V0UG9zaXRpb24obWFwSGVscGVyLmdldFBpeGVsQnlQb2ludChtLnBvaW50LmFkZCh0aGlzLmJ1aWxkT3JpZ2luLCB0aGlzLl90ZW1wX3ZlYzJfMSkpKVxuICAgICAgICAgICAgICAgIG5vZGUuYWRkQ29tcG9uZW50KENsaWNrVG91Y2hDbXB0KS5vbih0aGlzLm9uQ2xpY2tXYWxsLCB0aGlzKVxuICAgICAgICAgICAgICAgIG5vZGUuekluZGV4ID0gQVJFQV9NQVhfWklOREVYIC0gbm9kZS55XG4gICAgICAgICAgICAgICAgdGhpcy53YWxscy5wdXNoKG5vZGUpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKVxuICAgIH1cblxuICAgIHByaXZhdGUgY2xlYW5XYWxscygpIHtcbiAgICAgICAgdGhpcy53YWxsTHZOb2RlPy5kZXN0cm95KClcbiAgICAgICAgdGhpcy53YWxsTHZOb2RlID0gbnVsbFxuICAgICAgICB0aGlzLmhwQmFyPy5jbGVhbigpXG4gICAgICAgIHRoaXMuaHBCYXIgPSBudWxsXG4gICAgICAgIHdoaWxlICh0aGlzLndhbGxzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHRoaXMud2FsbHMucG9wKCkuZGVzdHJveSgpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy53YWxscy5sZW5ndGggPSAwXG4gICAgfVxuXG4gICAgLy8g5Yib5bu66IGU55uf5peX5bicXG4gICAgcHJpdmF0ZSBhc3luYyBjcmVhdGVBbGxpRmxhZ3MoaWNvbjogbnVtYmVyKSB7XG4gICAgICAgIGlmICghaWNvbikge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2xlYW5BbGxpRmxhZ3MoKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHBvaW50cyA9IHRoaXMubW9kZWwuYWxsaUZsYWdzXG4gICAgICAgIGlmICh0aGlzLmFsbGlGbGFncy5sZW5ndGggPT09IHBvaW50cy5sZW5ndGggJiYgdGhpcy5hbGxpRmxhZ3NbMF0uRGF0YSA9PT0gaWNvbikge1xuICAgICAgICAgICAgcmV0dXJuIHBvaW50cy5mb3JFYWNoKChwb2ludCwgaSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmFsbGlGbGFnc1tpXSwgcG9zID0gbWFwSGVscGVyLmdldFBpeGVsQnlQb2ludChwb2ludC5hZGQodGhpcy5ib3JkZXJTaXplLCB0aGlzLl90ZW1wX3ZlYzJfMSkpXG4gICAgICAgICAgICAgICAgbm9kZS5zZXRQb3NpdGlvbihwb3MpXG4gICAgICAgICAgICAgICAgbm9kZS56SW5kZXggPSBBUkVBX01BWF9aSU5ERVggLSBub2RlLnlcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jbGVhbkFsbGlGbGFncygpXG4gICAgICAgIGNvbnN0IHBmYiA9IGF3YWl0IGFzc2V0c01nci5sb2FkVGVtcFJlcygnYWxsaV9mbGFnL0FMTElfRkxBR18nICsgaWNvbiwgY2MuUHJlZmFiLCB0aGlzLmtleSlcbiAgICAgICAgaWYgKHBmYiAmJiB0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgIHBvaW50cy5mb3JFYWNoKHBvaW50ID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBub2RlID0gY2MuaW5zdGFudGlhdGUyKHBmYiwgdGhpcy5yb2xlTm9kZV8pLCBwb3MgPSBtYXBIZWxwZXIuZ2V0UGl4ZWxCeVBvaW50KHBvaW50LmFkZCh0aGlzLmJvcmRlclNpemUsIHRoaXMuX3RlbXBfdmVjMl8xKSlcbiAgICAgICAgICAgICAgICBub2RlLkNoaWxkKCdib2R5JykueSA9IC0yOFxuICAgICAgICAgICAgICAgIG5vZGUuc2V0UG9zaXRpb24ocG9zKVxuICAgICAgICAgICAgICAgIG5vZGUuekluZGV4ID0gQVJFQV9NQVhfWklOREVYIC0gbm9kZS55XG4gICAgICAgICAgICAgICAgdGhpcy5hbGxpRmxhZ3MucHVzaChub2RlKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHByaXZhdGUgY2xlYW5BbGxpRmxhZ3MoKSB7XG4gICAgICAgIHdoaWxlICh0aGlzLmFsbGlGbGFncy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLmFsbGlGbGFncy5wb3AoKS5kZXN0cm95KClcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmFsbGlGbGFncy5sZW5ndGggPSAwXG4gICAgfVxuXG4gICAgLy8g5Yib5bu65Yy65Z+f5aSW55qE6KOF6aWwXG4gICAgcHJpdmF0ZSBhc3luYyBjcmVhdGVBcmVhT3V0RGVjb3JhdGUoKSB7XG4gICAgICAgIHRoaXMuY2xlYW5BcmVhT3V0RGVjb3JhdGUoKVxuICAgICAgICBjb25zdCBzZWFzb25UeXBlID0gdGhpcy50ZW1wU2Vhc29uVHlwZVxuICAgICAgICBjb25zdCBjZWxsID0gZ2FtZUhwci53b3JsZC5nZXRNYXBDZWxsQnlJbmRleCh0aGlzLm1vZGVsLmluZGV4KSwgZHJhd1R5cGUgPSBjZWxsLmdldExhbmREcmF3VHlwZSgpXG4gICAgICAgIGNvbnN0IHVybCA9IGBhcmVhX2RlY29yYXRlLyR7c2Vhc29uVHlwZX0vQVJFQV9ERUNPUkFURV8ke2RyYXdUeXBlfWBcbiAgICAgICAgY29uc3QgcGZiID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUmVzKHVybCwgY2MuUHJlZmFiLCB0aGlzLmtleSlcbiAgICAgICAgaWYgKHBmYiAmJiB0aGlzLmlzVmFsaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IG5vZGUgPSBjYy5pbnN0YW50aWF0ZTIocGZiLCB0aGlzLm1hcE5vZGVfLkNoaWxkKCdiZycpKVxuICAgICAgICAgICAgbm9kZS5zZXRQb3NpdGlvbih0aGlzLmJvcmRlclNpemUueCAqIFRJTEVfU0laRSwgdGhpcy5ib3JkZXJTaXplLnkgKiBUSUxFX1NJWkUpXG4gICAgICAgICAgICB0aGlzLmFyZWFPdXREZWNvcmF0ZS5wdXNoKG5vZGUpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcml2YXRlIGNsZWFuQXJlYU91dERlY29yYXRlKCkge1xuICAgICAgICB3aGlsZSAodGhpcy5hcmVhT3V0RGVjb3JhdGUubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgdGhpcy5hcmVhT3V0RGVjb3JhdGUucG9wKCkuZGVzdHJveSgpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5hcmVhT3V0RGVjb3JhdGUubGVuZ3RoID0gMFxuICAgIH1cblxuICAgIC8vIOWIneWni+WMluW7uuetkVxuICAgIHByaXZhdGUgYXN5bmMgaW5pdEJ1aWxkcyhwbGF5T2NjdXB5RWZmZWN0PzogYm9vbGVhbikge1xuICAgICAgICB0aGlzLmJ1aWxkTWFwID0ge31cbiAgICAgICAgLy8g5YWI5Yig6Zmk5rKh5pyJ55qEXG4gICAgICAgIGNvbnN0IGJ1aWxkcyA9IHRoaXMubW9kZWwuYnVpbGRzXG4gICAgICAgIGJ1aWxkcy5mb3JFYWNoKG0gPT4gdGhpcy5idWlsZE1hcFttLnVpZF0gPSAxKVxuICAgICAgICBmb3IgKGxldCBpID0gdGhpcy5idWlsZHMubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5idWlsZE1hcFt0aGlzLmJ1aWxkc1tpXS51aWRdKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jbGVhbkJ1aWxkKHRoaXMuYnVpbGRzLnNwbGljZShpLCAxKVswXSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChidWlsZHMubWFwKG0gPT4gdGhpcy5jcmVhdGVCdWlsZChtKSkpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBhc3luYyBjcmVhdGVCdWlsZChkYXRhOiBCdWlsZE9iaikge1xuICAgICAgICBpZiAoIXRoaXMuaXNBY3RpdmUoKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfVxuICAgICAgICBsZXQgYnVpbGQgPSB0aGlzLmJ1aWxkcy5maW5kKG0gPT4gbS51aWQgPT09IGRhdGEudWlkKVxuICAgICAgICBpZiAoYnVpbGQpIHtcbiAgICAgICAgICAgIHRoaXMuYnVpbGRNYXBbZGF0YS51aWRdID0gMlxuICAgICAgICAgICAgcmV0dXJuIGJ1aWxkLnJlc3luYyhkYXRhLCB0aGlzLm1vZGVsLm93bmVyKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHBmYiA9IGF3YWl0IGFzc2V0c01nci5sb2FkVGVtcFJlcyhkYXRhLmdldFByZWZhYlVybCgpLCBjYy5QcmVmYWIsIHRoaXMua2V5KVxuICAgICAgICBpZiAoIXBmYiB8fCAhdGhpcy5pc0FjdGl2ZSgpKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuYnVpbGRNYXBbZGF0YS51aWRdID09PSAyKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbCAvL+mYsuatoumHjeWkjeWIm+W7uuaIluWIm+W7uuayoeacieeahFxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEuYUluZGV4ICE9PSB0aGlzLm1vZGVsPy5pbmRleCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmJ1aWxkTWFwW2RhdGEudWlkXSA9IDJcbiAgICAgICAgYnVpbGQgPSBjYy5pbnN0YW50aWF0ZTIocGZiLCB0aGlzLnJvbGVOb2RlXykuZ2V0Q29tcG9uZW50KEJhc2VCdWlsZENtcHQpLmluaXQoZGF0YSwgdGhpcy5idWlsZE9yaWdpbiwgdGhpcy5ib3JkZXJTaXplLnkgKiBUSUxFX1NJWkUsIHRoaXMubW9kZWwub3duZXIpXG4gICAgICAgIHRoaXMuYnVpbGRzLnB1c2goYnVpbGQpXG4gICAgICAgIHJldHVybiBidWlsZFxuICAgIH1cblxuICAgIHByaXZhdGUgY2hlYW5CdWlsZHMoKSB7XG4gICAgICAgIHdoaWxlICh0aGlzLmJ1aWxkcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLmJ1aWxkcy5wb3AoKS5jbGVhbigpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5idWlsZE1hcCA9IHt9XG4gICAgfVxuXG4gICAgcHJpdmF0ZSBjbGVhbkJ1aWxkKGRhdGE6IEJhc2VCdWlsZENtcHQpIHtcbiAgICAgICAgaWYgKGRhdGEpIHtcbiAgICAgICAgICAgIGRhdGEuY2xlYW4oKVxuICAgICAgICAgICAgZGVsZXRlIHRoaXMuYnVpbGRNYXBbZGF0YS51aWRdXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDliLfmlrDlopnnmoTnrYnnuqdcbiAgICBwcml2YXRlIHVwZGF0ZVdhbGxMdihsdjogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLndhbGxMdk5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMud2FsbEx2Tm9kZS5DaGlsZCgndmFsJywgY2MuTGFiZWwpLnN0cmluZyA9ICcnICsgbHZcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWIt+aWsOihgOadoeS9jee9rlxuICAgIHByaXZhdGUgdXBkYXRlV2FsbEhwUG9zaXRpb24oKSB7XG4gICAgICAgIGlmICh0aGlzLmhwQmFyKSB7XG4gICAgICAgICAgICBsZXQgbm9kZSA9IHRoaXMuaHBCYXIubm9kZSwgcG9zID0gY2MudjIoKVxuICAgICAgICAgICAgaWYgKHRoaXMubW9kZWwuY2l0eUlkID09PSBDSVRZX01BSU5fTklEKSB7XG4gICAgICAgICAgICAgICAgcG9zID0gbWFwSGVscGVyLmdldFBpeGVsQnlQb2ludChjYy52MigzLCAwKS5hZGRTZWxmKHRoaXMuYnVpbGRPcmlnaW4pKVxuICAgICAgICAgICAgICAgIG5vZGUuc2V0UG9zaXRpb24ocG9zLngsIHBvcy55ICsgMjUpXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMubW9kZWwuaXNBbmNpZW50KCkpIHtcbiAgICAgICAgICAgICAgICBwb3MgPSBtYXBIZWxwZXIuZ2V0UGl4ZWxCeVBvaW50KGNjLnYyKDMsIDApLmFkZFNlbGYodGhpcy5idWlsZE9yaWdpbikpXG4gICAgICAgICAgICAgICAgbm9kZS5zZXRQb3NpdGlvbihwb3MueCwgcG9zLnkgKyAodGhpcy5tb2RlbC5jaXR5SWQgPT09IENJVFlfTFVPWUFOR19JRCA/IDEwNSA6IDI1KSlcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcG9zID0gbWFwSGVscGVyLmdldFBpeGVsQnlQb2ludCh0aGlzLmJ1aWxkT3JpZ2luKVxuICAgICAgICAgICAgICAgIG5vZGUuc2V0UG9zaXRpb24ocG9zLngsIHBvcy55ICsgNDcpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBub2RlLnpJbmRleCA9IChBUkVBX01BWF9aSU5ERVggLSAocG9zLnkgLSB0aGlzLmJvcmRlclNpemUueSAqIFRJTEVfU0laRSkpICogMTAgKyAxXG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMud2FsbEx2Tm9kZSkge1xuICAgICAgICAgICAgY29uc3QgcG9zID0gbWFwSGVscGVyLmdldFBpeGVsQnlQb2ludCh0aGlzLmJ1aWxkT3JpZ2luKVxuICAgICAgICAgICAgdGhpcy53YWxsTHZOb2RlLnNldFBvc2l0aW9uKHBvcy54LCBwb3MueSArIDE2KVxuICAgICAgICAgICAgdGhpcy53YWxsTHZOb2RlLnpJbmRleCA9IHRoaXMuaHBCYXIubm9kZS56SW5kZXggKyAxXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmiZPlvIDnvJbovpHlu7rnrZFcbiAgICBwcml2YXRlIG9wZW5FZGl0QnVpbGQoaXRlbTogQnVpbGRDbXB0KSB7XG4gICAgICAgIC8vIOaYvuekunBubFxuICAgICAgICB2aWV3SGVscGVyLnNob3dQbmwoJ2FyZWEvRWRpdEJ1aWxkJylcbiAgICAgICAgdGhpcy5ncmlkTm9kZV8uYWN0aXZlID0gdHJ1ZVxuICAgICAgICAvLyDlpoLmnpzkuYvliY3mnInlsLHmlL7kuItcbiAgICAgICAgdGhpcy5jaGVja0NvbmZpcm1FZGl0QnVpbGQoKVxuICAgICAgICB0aGlzLmN1cnJFZGl0QnVpbGQgPSBpdGVtXG4gICAgICAgIC8vIOWIt+aWsOS4gOS4i+WcsOmdoueCuVxuICAgICAgICB0aGlzLm1vZGVsLnVwZGF0ZUJ1aWxkR3JvdW5kUG9pbnRzKGl0ZW0uZGF0YSlcbiAgICAgICAgLy8g5Yi35paw57yW6L6R54q25oCBXG4gICAgICAgIGl0ZW0udXBkYXRlRWRpdFN0YXRlKHRoaXMubW9kZWwuZ2V0QnVpbGRHcm91bmRQb2ludE1hcCgpLCBpdGVtLnBvaW50KVxuICAgIH1cblxuICAgIC8vIOWmguaenOacieW7uuetkeWcqOe8lui+keeKtuaAgSDlsLHmlL7kuItcbiAgICBwcml2YXRlIGNoZWNrQ29uZmlybUVkaXRCdWlsZCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzRWRpdEJ1aWxkU3RhdGUoKSkge1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VyckVkaXRCdWlsZC5lZGl0U3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuY3VyckVkaXRCdWlsZC5jYW5jZWwoKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5jdXJyRWRpdEJ1aWxkLmNvbmZpcm0odGhpcy5jdXJyRWRpdEJ1aWxkLmdldEFjdFBvaW50QnlQaXhlbCh0aGlzLmN1cnJFZGl0QnVpbGQuZ2V0VGVtcFBvc2l0aW9uKCkpKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5YWz6Zet57yW6L6R5bu6562RXG4gICAgcHJpdmF0ZSBjbG9zZUVkaXRCdWlsZCgpIHtcbiAgICAgICAgdGhpcy5jdXJyRWRpdEJ1aWxkID0gbnVsbFxuICAgICAgICB0aGlzLmVkaXRKaWFudG91Tm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICAgICAgdGhpcy5ncmlkTm9kZV8uYWN0aXZlID0gZmFsc2VcbiAgICB9XG5cbiAgICAvLyDlhbPpl63nvJbovpHlo6vlhbVcbiAgICBwcml2YXRlIGNsb3NlRWRpdFBhd24oKSB7XG4gICAgICAgIGZvciAobGV0IGtleSBpbiB0aGlzLmVkaXRQYXducykge1xuICAgICAgICAgICAgY29uc3QgcGF3biA9IHRoaXMuZWRpdFBhd25zW2tleV0sIHN0YXRlID0gcGF3bi5kYXRhPy5nZXRTdGF0ZSgpXG4gICAgICAgICAgICBpZiAoc3RhdGUgJiYgc3RhdGUgPCBQYXduU3RhdGUuU1RBTkQpIHtcbiAgICAgICAgICAgICAgICBwYXduLmRhdGEuY2hhbmdlU3RhdGUoUGF3blN0YXRlLk5PTkUpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5lZGl0UGF3bnMgPSB7fVxuICAgICAgICB0aGlzLmN1cnJFZGl0UGF3biA9IG51bGxcbiAgICAgICAgdGhpcy5zZWxlY3RQYXduTm9kZV8uQ29tcG9uZW50KFNlbGVjdENlbGxDbXB0KS5jbG9zZSgpXG4gICAgICAgIHRoaXMuaXNQYXduTW92ZWluZyA9IGZhbHNlXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBjbGVhblBhd25zKCkge1xuICAgICAgICB3aGlsZSAodGhpcy5wYXducy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLnBhd25zLnBvcCgpLmNsZWFuKClcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnBhd25NYXAgPSB7fVxuICAgIH1cblxuICAgIC8vIOWIneWni+WMluWjq+WFtVxuICAgIHByaXZhdGUgYXN5bmMgaW5pdFBhd25zKCkge1xuICAgICAgICAvLyDlhYjliKDpmaTmsqHmnInnmoRcbiAgICAgICAgY29uc3QgcGF3bnMgPSB0aGlzLm1vZGVsLmdldEFsbFBhd25zKCksIHVpZE1hcCA9IHt9XG4gICAgICAgIHBhd25zLmZvckVhY2gobSA9PiB1aWRNYXBbbS5nZXRBYnNVaWQoKV0gPSB0cnVlKVxuICAgICAgICBmb3IgKGxldCBpID0gdGhpcy5wYXducy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICAgICAgY29uc3QgbSA9IHRoaXMucGF3bnNbaV1cbiAgICAgICAgICAgIGlmICghdWlkTWFwW20uZ2V0QWJzVWlkKCldIHx8ICFtLmRhdGEgfHwgbS5kYXRhLmlzRGllKCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNsZWFuUGF3bih0aGlzLnBhd25zLnNwbGljZShpLCAxKVswXSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gUHJvbWlzZS5hbGwocGF3bnMubWFwKG0gPT4gdGhpcy5jcmVhdGVQYXduKG0pKSlcbiAgICB9XG5cbiAgICBwcml2YXRlIGFzeW5jIGNyZWF0ZVBhd24oZGF0YTogUGF3bk9iaikge1xuICAgICAgICBpZiAoIXRoaXMuaXNBY3RpdmUoKSB8fCAhZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfVxuICAgICAgICBsZXQgcGF3biA9IHRoaXMucGF3bnMuZmluZChtID0+IG0udWlkID09PSBkYXRhLnVpZClcbiAgICAgICAgaWYgKCFwYXduKSB7XG4gICAgICAgIH0gZWxzZSBpZiAocGF3bi5jdXJTa2luSWQgIT09IGRhdGEuc2tpbklkIHx8IHBhd24uY3VyUG9ydHJheWFsSWQgIT09IGRhdGEuZ2V0UG9ydHJheWFsSWQoKSkge1xuICAgICAgICAgICAgdGhpcy5wYXducy5yZW1vdmUoJ3VpZCcsIGRhdGEudWlkKVxuICAgICAgICAgICAgdGhpcy5jbGVhblBhd24ocGF3bilcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmlzRGllKCkpIHtcbiAgICAgICAgICAgIHRoaXMucGF3bnMucmVtb3ZlKCd1aWQnLCBkYXRhLnVpZClcbiAgICAgICAgICAgIHRoaXMuY2xlYW5QYXduKHBhd24pXG4gICAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHBhd24ucmVzeW5jKGRhdGEpXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcGZiID0gYXdhaXQgYXNzZXRzTWdyLmxvYWRUZW1wUmVzKGRhdGEuZ2V0UHJlZmFiVXJsKCksIGNjLlByZWZhYiwgdGhpcy5rZXkpXG4gICAgICAgIGlmICghcGZiIHx8ICF0aGlzLmlzQWN0aXZlKCkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5wYXduTWFwW2RhdGEudWlkXSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGwgLy/pmLLmraLlpJrmrKHliJvlu7pcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmFJbmRleCAhPT0gdGhpcy5tb2RlbD8uaW5kZXgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cbiAgICAgICAgLy8g6YeN5paw6I635Y+W5Lul6Ziy5pWw5o2u5LiN57uf5LiAXG4gICAgICAgIGNvbnN0IHVpZCA9IGRhdGEudWlkXG4gICAgICAgIGRhdGEgPSB0aGlzLm1vZGVsLmdldFBhd24odWlkKSB8fCB0aGlzLm1vZGVsLmdldEJhdHRsZVRlbXBQYXduKHVpZClcbiAgICAgICAgaWYgKCFkYXRhIHx8IGRhdGEuaXNEaWUoKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgICAgfVxuICAgICAgICBwYXduID0gY2MuaW5zdGFudGlhdGUyKHBmYiwgdGhpcy5yb2xlTm9kZV8pLmdldENvbXBvbmVudChQYXduQ21wdCkuaW5pdChkYXRhLCB0aGlzLmJvcmRlclNpemUsIHRoaXMua2V5KVxuICAgICAgICB0aGlzLnBhd25zLnB1c2gocGF3bilcbiAgICAgICAgdGhpcy5wYXduTWFwW2RhdGEudWlkXSA9IHBhd25cbiAgICAgICAgcmV0dXJuIHBhd25cbiAgICB9XG5cbiAgICBwcml2YXRlIGNsZWFuUGF3bihwYXduOiBQYXduQ21wdCwgcmVsZWFzZT86IGJvb2xlYW4pIHtcbiAgICAgICAgaWYgKHBhd24pIHtcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLnBhd25NYXBbcGF3bi51aWRdXG4gICAgICAgICAgICBwYXduLmNsZWFuKHJlbGVhc2UpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmmK/lkKbnvJbovpHlu7rnrZHkuK1cbiAgICBwcml2YXRlIGlzRWRpdEJ1aWxkU3RhdGUoKSB7XG4gICAgICAgIHJldHVybiAhIXRoaXMuY3VyckVkaXRCdWlsZD8uZGF0YVxuICAgIH1cblxuICAgIC8vIOaYr+WQpue8lui+keWwj+WFteS4rVxuICAgIHByaXZhdGUgaXNFZGl0UGF3blN0YXRlKCkge1xuICAgICAgICByZXR1cm4gISF0aGlzLmN1cnJFZGl0UGF3bj8uZGF0YVxuICAgIH1cblxuICAgIC8vIOe7mOWItuWcsOWbvlxuICAgIHByaXZhdGUgdXBkYXRlTWFwKGNlbnRyZTogY2MuVmVjMikge1xuICAgICAgICBjb25zdCBzZWFzb25UeXBlID0gdGhpcy50ZW1wU2Vhc29uVHlwZVxuICAgICAgICBjb25zdCBjZWxsID0gZ2FtZUhwci53b3JsZC5nZXRNYXBDZWxsQnlJbmRleCh0aGlzLm1vZGVsLmluZGV4KSwgc2Vhc29uQ29sb3JDb25mID0gQVJFQV9ESV9DT0xPUl9DT05GW3NlYXNvblR5cGVdXG4gICAgICAgIGNvbnN0IGNvbG9yQ29uZjogQXJlYUxhbmRDb2xvckluZm8gPSBzZWFzb25Db2xvckNvbmZbY2VsbC5nZXRMYW5kRHJhd1R5cGUoKV0gfHwgc2Vhc29uQ29sb3JDb25mWzBdXG4gICAgICAgIGNvbnN0IGFyZWFTaXplID0gdGhpcy5tb2RlbC5hcmVhU2l6ZSwgb1lpbmRleCA9IChhcmVhU2l6ZS54ICsgMSkgJSAyXG4gICAgICAgIC8vIOiuvue9ruaVtOS4quiDjOaZr+minOiJslxuICAgICAgICBjYW1lcmFDdHJsLnNldEJnQ29sb3IoY29sb3JDb25mLmJnKVxuICAgICAgICBjb25zdCBidWlsZE9yaWdpbiA9IHRoaXMubW9kZWwuYnVpbGRPcmlnaW4sIGJ1aWxkU2l6ZSA9IHRoaXMubW9kZWwuYnVpbGRTaXplXG4gICAgICAgIGNvbnN0IHBvaW50cyA9IG1hcEhlbHBlci5nZXRSYW5nZVBvaW50c0J5UG9pbnQoY2VudHJlLCBnYW1lSHByLndvcmxkLmdldE1heFRpbGVSYW5nZSgpKVxuICAgICAgICBjb25zdCBpc0Jvc3MgPSB0aGlzLm1vZGVsLmlzQm9zcygpXG4gICAgICAgIGxldCBtaSA9IDBcbiAgICAgICAgdGhpcy5kaU5vZGUuSXRlbXMocG9pbnRzLCAoaXQsIHBvaW50LCBpKSA9PiB7XG4gICAgICAgICAgICBjb25zdCB4ID0gcG9pbnQueCAtIHRoaXMuYm9yZGVyU2l6ZS54LCB5ID0gcG9pbnQueSAtIHRoaXMuYm9yZGVyU2l6ZS55XG4gICAgICAgICAgICBjb25zdCBieCA9IHggLSBidWlsZE9yaWdpbi54LCBieSA9IHkgLSBidWlsZE9yaWdpbi55XG4gICAgICAgICAgICBjb25zdCBpbmRleCA9IGl0LkRhdGEgPSBtYXBIZWxwZXIucG9pbnRUb0luZGV4QnlOdW1lcih4LCB5LCBhcmVhU2l6ZSlcbiAgICAgICAgICAgIGNvbnN0IGlkID0geCArICdfJyArIHlcbiAgICAgICAgICAgIGl0LnNldFBvc2l0aW9uKG1hcEhlbHBlci5nZXRQaXhlbEJ5UG9pbnQocG9pbnQsIHRoaXMuX3RlbXBfdmVjMl8wKSlcbiAgICAgICAgICAgIGl0LkNvbG9yKCcjRkZGRkZGJylcbiAgICAgICAgICAgIGlmIChtYXBIZWxwZXIuaXNCb3JkZXIoeCwgeSwgYXJlYVNpemUpKSB7IC8v6L6555WM5aSWXG4gICAgICAgICAgICAgICAgaXQuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSBudWxsXG4gICAgICAgICAgICB9IGVsc2UgaWYgKG1hcEhlbHBlci5pc0JvcmRlcihieCwgYnksIGJ1aWxkU2l6ZSkpIHsgLy/miJjmlpfljLrln59cbiAgICAgICAgICAgICAgICBjb25zdCBpZHggPSB5ICUgMiA9PT0gMCA/IGluZGV4IDogaW5kZXggKyBvWWluZGV4XG4gICAgICAgICAgICAgICAgaXQuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSByZXNIZWxwZXIuZ2V0TGFuZEljb24oJ2NvbW1fYXJlYV8wMScpXG4gICAgICAgICAgICAgICAgaXQuQ29sb3IoY29sb3JDb25mLmJhdHRsZVtOdW1iZXIoaWR4ICUgMiAhPT0gMCldKVxuICAgICAgICAgICAgICAgIGlmICh0aGlzLm1vZGVsLmJhblBsYWNlUGF3blBvaW50TWFwW2lkXSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBtbiA9IHJlc0hlbHBlci5nZXROb2RlQnlJbmRleCh0aGlzLm1hc2tOb2RlLCBtaSsrLCB0aGlzLl90ZW1wX3ZlYzJfMClcbiAgICAgICAgICAgICAgICAgICAgbW4uRGF0YSA9IHRydWVcbiAgICAgICAgICAgICAgICAgICAgbW4uYWN0aXZlID0gdGhpcy5pc0VkaXRQYXduU3RhdGUoKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSBpZiAoY2VsbC5pc01haW5DaXR5KCkpIHsgLy/kuLvln47ljLrln59cbiAgICAgICAgICAgICAgICBpZiAoYnggPT09IDMgJiYgYnkgPT09IDMpIHtcbiAgICAgICAgICAgICAgICAgICAgaXQuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSByZXNIZWxwZXIuZ2V0TGFuZEljb24oJ2NvbW1fYXJlYV9tYWluJylcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBpdC5Db21wb25lbnQoY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IG51bGxcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgaWYgKCFpc0Jvc3MpIHsgLy/lu7rnrZHljLrln59cbiAgICAgICAgICAgICAgICBpdC5Db21wb25lbnQoY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRMYW5kSWNvbignY29tbV9hcmVhXzAxJylcbiAgICAgICAgICAgICAgICBpZiAoYnggPT09IDAgfHwgYnggPT09IGJ1aWxkU2l6ZS54IC0gMSB8fCBieSA9PT0gMCB8fCBieSA9PT0gYnVpbGRTaXplLnkgLSAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGl0LkNvbG9yKGNvbG9yQ29uZi5idWlsZClcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGNlbGwuaXNBbmNpZW50KCkpIHtcbiAgICAgICAgICAgICAgICAgICAgaXQuQ29sb3IoJyNENkRCQUEnKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSBpZiAoYnggPT09IDEgJiYgYnkgPT09IDEpIHsgLy9ib3Nz5L2N572uXG4gICAgICAgICAgICAgICAgaXQuQ29tcG9uZW50KGNjLlNwcml0ZSkuc3ByaXRlRnJhbWUgPSByZXNIZWxwZXIuZ2V0TGFuZEljb24oJ2xhbmRfYXJlYV9ib3NzXycgKyBjZWxsLmxhbmRUeXBlKVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBpdC5Db21wb25lbnQoY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IG51bGxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgLy8g6ZqQ6JeP5aSa5L2Z55qEXG4gICAgICAgIHJlc0hlbHBlci5oaWRlTm9kZUJ5SW5kZXgodGhpcy5tYXNrTm9kZSwgbWkpXG4gICAgfVxuXG4gICAgcHJpdmF0ZSBnZXRMYW5kSWNvbihpY29uOiBzdHJpbmcpIHtcbiAgICAgICAgcmV0dXJuIHJlc0hlbHBlci5nZXRMYW5kSXRlbUljb24oaWNvbiwgdGhpcy50ZW1wU2Vhc29uVHlwZSlcbiAgICB9XG5cbiAgICAvLyDngrnlh7vlnLDlm75cbiAgICBwcml2YXRlIG9uQ2xpY2tNYXAod29ybGRMb2NhdGlvbjogY2MuVmVjMikge1xuICAgICAgICBpZiAoIXRoaXMubW9kZWwgfHwgIXdvcmxkTG9jYXRpb24pIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHBvaW50ID0gdGhpcy5nZXRQb2ludEJ5UGl4ZWwod29ybGRMb2NhdGlvbilcbiAgICAgICAgaWYgKCFwb2ludCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH0gZWxzZSBpZiAobWFwSGVscGVyLmlzQm9yZGVyKHBvaW50LngsIHBvaW50LnksIHRoaXMubW9kZWwuYXJlYVNpemUpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICAvLyDmmK/lkKbngrnlh7vnmoTlu7rnrZHljLrln59cbiAgICAgICAgY29uc3QgYnBvaW50ID0gcG9pbnQuc3ViKHRoaXMubW9kZWwuYnVpbGRPcmlnaW4sIHRoaXMuX3RlbXBfdmVjMl8xKVxuICAgICAgICBpZiAobWFwSGVscGVyLmlzQm9yZGVyKGJwb2ludC54LCBicG9pbnQueSwgdGhpcy5tb2RlbC5idWlsZFNpemUpKSB7XG4gICAgICAgICAgICB0aGlzLm9uQ2xpY2tNYXBBcmVhKHBvaW50KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5vbkNsaWNrQnVpbGRBcmVhKGJwb2ludClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOeCueWHu+WcsOWbvuWMuuWfn1xuICAgIHByaXZhdGUgb25DbGlja01hcEFyZWEocG9pbnQ6IGNjLlZlYzIpIHtcbiAgICAgICAgaWYgKCF0aGlzLmlzRWRpdFBhd25TdGF0ZSgpIHx8IHRoaXMuaXNFZGl0QnVpbGRTdGF0ZSgpIHx8IHRoaXMuaXNQYXduTW92ZWluZykge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdWlkID0gdGhpcy5jdXJyRWRpdFBhd24udWlkXG4gICAgICAgIGlmICghdGhpcy5tb2RlbC5iYW5QbGFjZVBhd25Qb2ludE1hcFtwb2ludC5JRCgpXSAmJiAhdGhpcy5wYXducy5zb21lKG0gPT4gbS51aWQgIT09IHVpZCAmJiBtLmdldEFjdFBvaW50KCkuZXF1YWxzKHBvaW50KSkpIHtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0UGF3bk5vZGVfLkNvbXBvbmVudChTZWxlY3RDZWxsQ21wdCkub3Blbih0aGlzLmdldFBpeGVsQnlQb2ludChwb2ludCksIHRoaXMuUEFXTl9TSVpFKVxuICAgICAgICAgICAgdGhpcy5tb3ZlUGF3bihwb2ludClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOeCueWHu+W7uuetkeWMuuWfn1xuICAgIHByaXZhdGUgb25DbGlja0J1aWxkQXJlYShwb2ludDogY2MuVmVjMikge1xuICAgICAgICBpZiAoIXRoaXMuaXNFZGl0QnVpbGRTdGF0ZSgpIHx8IHRoaXMuaXNFZGl0UGF3blN0YXRlKCkpIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLm1vZGVsLmlzQnVpbGRCb3JkZXIocG9pbnQpKSB7XG4gICAgICAgICAgICB0aGlzLmN1cnJFZGl0QnVpbGQuc2V0T2Zmc2V0UG9zaXRpb25CeVBvaW50KHBvaW50KVxuICAgICAgICAgICAgdGhpcy5jdXJyRWRpdEJ1aWxkLnVwZGF0ZUVkaXRTdGF0ZSh0aGlzLm1vZGVsLmdldEJ1aWxkR3JvdW5kUG9pbnRNYXAoKSwgcG9pbnQpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDngrnlh7vloplcbiAgICBwcml2YXRlIG9uQ2xpY2tXYWxsKCkge1xuICAgICAgICBpZiAoIXRoaXMubW9kZWwud2FsbCB8fCB0aGlzLmlzRWRpdEJ1aWxkU3RhdGUoKSB8fCB0aGlzLmlzRWRpdFBhd25TdGF0ZSgpKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGlmICh0aGlzLm1vZGVsLmlzQW5jaWVudCgpKSB7XG4gICAgICAgICAgICBjb25zdCBidWlsZCA9IHRoaXMubW9kZWwuZ2V0QnVpbGRCeUlkKHRoaXMubW9kZWwuY2l0eUlkKVxuICAgICAgICAgICAgaWYgKCFidWlsZCkge1xuICAgICAgICAgICAgfSBlbHNlIGlmIChnYW1lSHByLmNoZWNrSXNPbmVBbGxpYW5jZSh0aGlzLm1vZGVsLm93bmVyKSkge1xuICAgICAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubChidWlsZC5nZXRVSVVybCgpLCBidWlsZClcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdidWlsZC9CdWlsZEFuY2llbnRCYXNlJywgYnVpbGQpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5tb2RlbC5pc093bmVyKCkpIHtcbiAgICAgICAgICAgIHZpZXdIZWxwZXIuc2hvd1BubCh0aGlzLm1vZGVsLndhbGwuZ2V0VUlVcmwoKSwgdGhpcy5tb2RlbC53YWxsKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdmlld0hlbHBlci5zaG93UG5sKCdidWlsZC9CdWlsZENpdHknLCB0aGlzLm1vZGVsLndhbGwpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwcml2YXRlIGdldFNlYXJjaENpcmNsZSgpIHtcbiAgICAgICAgaWYgKCF0aGlzLnNlYXJjaENpcmNsZSkge1xuICAgICAgICAgICAgY29uc3QgbW9kZWwgPSB0aGlzLm1vZGVsXG4gICAgICAgICAgICB0aGlzLnNlYXJjaENpcmNsZSA9IG5ldyBTZWFyY2hDaXJjbGUoKS5pbml0KCh4OiBudW1iZXIsIHk6IG51bWJlcikgPT4gbW9kZWwuY2hlY2tJc0JhdHRsZUFyZWEoeCwgeSkgJiYgIW1vZGVsLmJhblBsYWNlUGF3blBvaW50TWFwW3ggKyAnXycgKyB5XSlcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5zZWFyY2hDaXJjbGVcbiAgICB9XG5cbiAgICAvLyDnp7vliqjlo6vlhbVcbiAgICBwcml2YXRlIGFzeW5jIG1vdmVQYXduKHBvaW50OiBjYy5WZWMyKSB7XG4gICAgICAgIGlmICh0aGlzLmN1cnJFZGl0UGF3bikge1xuICAgICAgICAgICAgdGhpcy5pc1Bhd25Nb3ZlaW5nID0gdHJ1ZVxuICAgICAgICAgICAgdGhpcy5lbWl0KEV2ZW50VHlwZS5FRElUX1BBV05fTU9WRUlORywgdHJ1ZSlcbiAgICAgICAgICAgIGF3YWl0IHRoaXMubW92ZVBhd25PbmUodGhpcy5jdXJyRWRpdFBhd24sIHBvaW50KVxuICAgICAgICAgICAgaWYgKHRoaXMuaXNBY3RpdmUoKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuaXNQYXduTW92ZWluZyA9IGZhbHNlXG4gICAgICAgICAgICAgICAgdGhpcy5lbWl0KEV2ZW50VHlwZS5FRElUX1BBV05fTU9WRUlORywgZmFsc2UpXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDnp7vliqjljZXkuKrlo6vlhbVcbiAgICBwcml2YXRlIGFzeW5jIG1vdmVQYXduT25lKHBhd246IFBhd25DbXB0LCBwb2ludDogY2MuVmVjMikge1xuICAgICAgICBpZiAoIXBhd24/LmRhdGEgfHwgcGF3bi5kYXRhPy5nZXRTdGF0ZSgpID09PSBQYXduU3RhdGUuRURJVF9NT1ZFKSB7XG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBkYXRhID0gcGF3bi5kYXRhXG4gICAgICAgIGNvbnN0IHNwID0gcGF3bi5nZXRBY3RQb2ludCgpXG4gICAgICAgIGNvbnN0IGFyZWEgPSB0aGlzLm1vZGVsLCBhcyA9IGdhbWVIcHIuZ2V0UGF3bkFTYXRyKGRhdGEudWlkKS5pbml0KCh4OiBudW1iZXIsIHk6IG51bWJlcikgPT4gYXJlYS5jaGVja0lzQmF0dGxlQXJlYSh4LCB5KSAmJiAhYXJlYS5iYW5QbGFjZVBhd25Qb2ludE1hcFt4ICsgJ18nICsgeV0pXG4gICAgICAgIGNvbnN0IHBvaW50cyA9IGF3YWl0IGFzLnNlYXJjaChzcCwgcG9pbnQpXG4gICAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSgpIHx8IHBvaW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLmVkaXRQYXduc1twYXduLnVpZF0pIHtcbiAgICAgICAgICAgIHRoaXMuZWRpdFBhd25zW3Bhd24udWlkXSA9IHBhd25cbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0aW1lID0gbWFwSGVscGVyLmdldE1vdmVOZWVkVGltZShwb2ludHMsIDQwMClcbiAgICAgICAgZGF0YS5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuRURJVF9NT1ZFLCB7IHBhdGhzOiBwb2ludHMsIG5lZWRNb3ZlVGltZTogdGltZSB9KVxuICAgICAgICBhd2FpdCB1dC53YWl0KHRpbWUgKiAwLjAwMSlcbiAgICAgICAgaWYgKGRhdGEuZ2V0U3RhdGUoKSA8IFBhd25TdGF0ZS5TVEFORCkge1xuICAgICAgICAgICAgZGF0YS5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuTk9ORSlcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOmbhuWQiOWjq+WFtVxuICAgIHByaXZhdGUgYXN5bmMgZ2F0aGVyUGF3bigpIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJFZGl0UGF3bj8uZGF0YSkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcGF3biA9IHRoaXMuY3VyckVkaXRQYXduXG4gICAgICAgIGNvbnN0IHVpZCA9IHRoaXMuY3VyckVkaXRQYXduLnVpZFxuICAgICAgICBjb25zdCBhcm15VWlkID0gdGhpcy5jdXJyRWRpdFBhd24uZGF0YS5hcm15VWlkXG4gICAgICAgIGNvbnN0IHBvaW50ID0gcGF3bi5nZXRBY3RQb2ludCgpXG4gICAgICAgIC8vIOiOt+WPluS4gOS4quWGm+mYn+eahFxuICAgICAgICBjb25zdCBwYXduczogUGF3bkNtcHRbXSA9IFtdLCBvdGhlclBhd25zID0ge31cbiAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgaWYgKG0udWlkID09PSB1aWQpIHtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobS5kYXRhPy5hcm15VWlkID09PSBhcm15VWlkKSB7XG4gICAgICAgICAgICAgICAgcGF3bnMucHVzaChtKVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBvdGhlclBhd25zW20ucG9pbnQuSUQoKV0gPSB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICAgIGNvbnN0IGNvdW50ID0gcGF3bnMubGVuZ3RoXG4gICAgICAgIGlmIChjb3VudCA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgLy8g6I635Y+W5L2N572uXG4gICAgICAgIGNvbnN0IHBvaW50cyA9IHRoaXMuZ2V0U2VhcmNoQ2lyY2xlKCkuc2VhcmNoKHBvaW50LCBjb3VudCwgb3RoZXJQYXducylcbiAgICAgICAgLy8g5Yig6Zmk5bey57uP5Zyo6L+Z5Liq5L2N572u55qE5aOr5YW1XG4gICAgICAgIHBvaW50cy5kZWxldGUobSA9PiB7XG4gICAgICAgICAgICBjb25zdCBpID0gcGF3bnMuZmluZEluZGV4KHAgPT4gcC5nZXRBY3RQb2ludCgpLmVxdWFscyhtKSlcbiAgICAgICAgICAgIGlmIChpICE9PSAtMSkge1xuICAgICAgICAgICAgICAgIHBhd25zLnNwbGljZShpLCAxKVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgICAgfSlcbiAgICAgICAgaWYgKHBvaW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHRoaXMuaXNQYXduTW92ZWluZyA9IHRydWVcbiAgICAgICAgdGhpcy5lbWl0KEV2ZW50VHlwZS5FRElUX1BBV05fTU9WRUlORywgdHJ1ZSlcbiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwocG9pbnRzLm1hcCgobSwgaSkgPT4gdGhpcy5tb3ZlUGF3bk9uZShwYXduc1tpXSwgbSkpKVxuICAgICAgICBpZiAodGhpcy5pc0FjdGl2ZSgpKSB7XG4gICAgICAgICAgICB0aGlzLmlzUGF3bk1vdmVpbmcgPSBmYWxzZVxuICAgICAgICAgICAgdGhpcy5lbWl0KEV2ZW50VHlwZS5FRElUX1BBV05fTU9WRUlORywgZmFsc2UpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDkv53lrZjnvJbovpHlo6vlhbXnmoTkv6Hmga9cbiAgICBwcml2YXRlIGFzeW5jIGVkaXRQYXduRW5kKCkge1xuICAgICAgICBpZiAoIXRoaXMuY3VyckVkaXRQYXduPy5kYXRhIHx8IHRoaXMuaXNQYXduTW92ZWluZykge1xuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5mbyA9IHRoaXMuY3VyckVkaXRQYXduLmRhdGFcbiAgICAgICAgY29uc3QgcGF3bnMgPSBbXVxuICAgICAgICBjb25zdCBtb3ZlSW5mb3MgPSBbXVxuICAgICAgICBmb3IgKGxldCBrZXkgaW4gdGhpcy5lZGl0UGF3bnMpIHtcbiAgICAgICAgICAgIGNvbnN0IHBhd24gPSB0aGlzLmVkaXRQYXduc1trZXldXG4gICAgICAgICAgICBjb25zdCBwb2ludCA9IHBhd24uZ2V0QWN0UG9pbnQoKVxuICAgICAgICAgICAgbW92ZUluZm9zLnB1c2goe1xuICAgICAgICAgICAgICAgIHVpZDogcGF3bi51aWQsXG4gICAgICAgICAgICAgICAgcG9pbnQ6IHBvaW50LnRvSnNvbigpXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgaWYgKCFwYXduLnBvaW50Py5lcXVhbHMocG9pbnQpKSB7XG4gICAgICAgICAgICAgICAgcGF3bnMucHVzaCh7IHVpZDogcGF3bi51aWQsIHBvaW50OiBwb2ludC50b0pzb24oKSB9KVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChwYXducy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zdCB7IGVyciwgZGF0YSB9ID0gYXdhaXQgbmV0SGVscGVyLnJlcU1vdmVBcmVhUGF3bnMoeyBpbmRleDogaW5mby5hSW5kZXgsIGFybXlVaWQ6IGluZm8uYXJteVVpZCwgcGF3bnM6IG1vdmVJbmZvcyB9KVxuICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgICAgICB9IGVsc2UgaWYgKCF0aGlzLmlzQWN0aXZlKCkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB2aWV3SGVscGVyLmhpZGVQbmwoJ2FyZWEvRWRpdFBhd24nKVxuICAgICAgICBmb3IgKGxldCBrZXkgaW4gdGhpcy5lZGl0UGF3bnMpIHtcbiAgICAgICAgICAgIHRoaXMuZWRpdFBhd25zW2tleV0uY29uZmlybSgpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jdXJyRWRpdFBhd24uZGF0YS5hY3Rpb25pbmcgPSBmYWxzZVxuICAgICAgICB0aGlzLmJ1aWxkcy5mb3JFYWNoKG0gPT4gbS5zZXRDYW5DbGljayh0cnVlKSlcbiAgICAgICAgdGhpcy5wYXducy5mb3JFYWNoKG0gPT4gbS5zZXRDYW5DbGljayh0cnVlKSlcbiAgICAgICAgdGhpcy5tYXNrTm9kZS5jaGlsZHJlbi5mb3JFYWNoKG0gPT4gbS5hY3RpdmUgPSBmYWxzZSlcbiAgICAgICAgdGhpcy5jbG9zZUVkaXRQYXduKClcbiAgICB9XG5cbiAgICAvLyB1cGRhdGUoZHQ6IG51bWJlcikge1xuICAgIC8vICAgICBpZiAoIXRoaXMubW9kZWw/LmFjdGl2ZSkge1xuICAgIC8vICAgICAgICAgcmV0dXJuXG4gICAgLy8gICAgIH1cbiAgICAvLyAgICAgLy8g5qOA5rWL5piv5ZCm6ZyA6KaB5aGr5YWF5Zyw5Zu+XG4gICAgLy8gICAgIC8vIHRoaXMuY2hlY2tVcGRhdGVNYXAoKVxuICAgIC8vIH1cblxuICAgIC8vIHByaXZhdGUgY2hlY2tVcGRhdGVNYXAoKSB7XG4gICAgLy8gICAgIGNvbnN0IHBvaW50ID0gbWFwSGVscGVyLmdldFBvaW50QnlQaXhlbChjYW1lcmFDdHJsLmdldENlbnRyZVBvc2l0aW9uKCksIHRoaXMuX3RlbXBfdmVjMl8yKVxuICAgIC8vICAgICBpZiAoIXRoaXMuY2VudHJlLmVxdWFscyhwb2ludCkgfHwgdGhpcy5wcmVDYW1lcmFab29tUmF0aW8gIT09IGNhbWVyYUN0cmwuem9vbVJhdGlvKSB7XG4gICAgLy8gICAgICAgICB0aGlzLnVwZGF0ZU1hcChwb2ludClcbiAgICAvLyAgICAgfVxuICAgIC8vIH1cbn1cbiJdfQ==