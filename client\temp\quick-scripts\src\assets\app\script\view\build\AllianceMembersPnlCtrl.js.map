{"version": 3, "sources": ["assets\\app\\script\\view\\build\\AllianceMembersPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAyD;AACzD,2DAA0D;AAC1D,6DAA4D;AAEpD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAoD,0CAAc;IAAlE;QAAA,qEA4DC;QA1DG,0BAA0B;QAClB,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QAC5D,MAAM;QAEE,QAAE,GAAa,IAAI,CAAA;;QAiD3B,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IAErH,CAAC;IApDU,gDAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,yCAAQ,GAArB;;;;;;KACC;IAEM,wCAAO,GAAd,UAAe,EAAY;QAA3B,iBAaC;QAZG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAM,IAAI,GAAG,oBAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,oBAAO,CAAC,MAAM,EAAE,EAA1B,CAA0B,CAAC,CAAA;QAClF,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAC7B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAI;YAC9B,EAAE,CAAC,IAAI,GAAG,IAAI,CAAA;YACd,IAAM,IAAI,GAAG,oBAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC5C,IAAM,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE,CAAC,CAAC,CAAA;YAChF,uBAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAC9E,qBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAI,CAAC,GAAG,CAAC,CAAA;QACzE,CAAC,CAAC,CAAA;IACN,CAAC;IAEM,yCAAQ,GAAf;IACA,CAAC;IAEM,wCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,oDAAoD;IACpD,2CAAU,GAAV,UAAW,KAA0B,EAAE,IAAY;QAC/C,IAAM,KAAK,GAAW,KAAK,CAAC,MAAM,CAAC,IAAI,CAAA;QACvC,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,IAAI,EAAE,CAAA;YACX,oBAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;SAC/B;IACL,CAAC;IAED,gDAAgD;IAChD,4CAAW,GAAX,UAAY,KAA0B,EAAE,IAAY;QAChD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAA;QACvC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;QAC1B,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAtDgB,sBAAsB;QAD1C,OAAO;OACa,sBAAsB,CA4D1C;IAAD,6BAAC;CA5DD,AA4DC,CA5DmD,EAAE,CAAC,WAAW,GA4DjE;kBA5DoB,sBAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { gameHpr } from \"../../common/helper/GameHelper\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport { viewHelper } from \"../../common/helper/ViewHelper\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class AllianceMembersPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    //@end\n\n    private cb: Function = null\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(cb: Function) {\n        this.cb = cb\n        const list = gameHpr.alliance.getMembers().filter(m => m.uid !== gameHpr.getUid())\n        this.listSv_.stopAutoScroll()\n        this.listSv_.content.y = 0\n        this.listSv_.Items(list, (it, data) => {\n            it.Data = data\n            const info = gameHpr.getPlayerInfo(data.uid)\n            const head = it.Child('head')\n            head.Child('name', cc.Label).string = ut.nameFormator(info.nickname || '???', 7)\n            viewHelper.updatePositionView(head.Child('pos_be'), info.mainCityIndex, false)\n            resHelper.loadPlayerHead(head.Child('icon'), info.headIcon, this.key)\n        })\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/item/head/pos_be\n    onClickPos(event: cc.Event.EventTouch, data: string) {\n        const index: number = event.target.Data\n        if (index) {\n            this.hide()\n            gameHpr.gotoTargetPos(index)\n        }\n    }\n\n    // path://root/list_sv/view/content/item/give_be\n    onClickGive(event: cc.Event.EventTouch, data: string) {\n        const member = event.target.parent.Data\n        this.cb && this.cb(member)\n        this.hide()\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}