
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/FirstPaySalePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd5b7cJXqulM16dnQ5yIUsA2', 'FirstPaySalePnlCtrl');
// app/script/view/common/FirstPaySalePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var ccclass = cc._decorator.ccclass;
var FirstPaySalePnlCtrl = /** @class */ (function (_super) {
    __extends(FirstPaySalePnlCtrl, _super);
    function FirstPaySalePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.task = null;
        _this.list = [];
        return _this;
    }
    FirstPaySalePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    FirstPaySalePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ maskOpacity: 204 });
                this.task = this.getModel('task');
                return [2 /*return*/];
            });
        });
    };
    FirstPaySalePnlCtrl.prototype.onEnter = function (data) {
        this.init();
    };
    FirstPaySalePnlCtrl.prototype.onRemove = function () {
    };
    FirstPaySalePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/rewards/day1/items/item_be
    FirstPaySalePnlCtrl.prototype.onClickItem = function (event, data) {
        var reward = event.target.Data;
        if (reward) {
            if (reward.type === Enums_1.CType.PAWN_SKIN) {
                ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: 'pawn_skin', list: [assetsMgr.getJsonData('pawnSkin', reward.id)] });
            }
            else if (reward.type === Enums_1.CType.HEAD_ICON) {
                ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: 'headicon', list: [assetsMgr.getJsonData('headIcon', reward.id)] });
            }
            else if (reward.type === Enums_1.CType.HERO_DEBRIS) {
                var json = assetsMgr.getJsonData('portrayalBase', reward.id);
                ViewHelper_1.viewHelper.showPnl('common/PortrayalBaseInfo', json, 'shop');
            }
        }
    };
    // path://root_n/buttons/goto_be
    FirstPaySalePnlCtrl.prototype.onClickGoto = function (event, data) {
        this.hide();
        ViewHelper_1.viewHelper.hidePnl('common/ActivitiesPnl');
        ViewHelper_1.viewHelper.showPnl('common/Shop', 0, 'ingot');
    };
    // path://root_n/buttons/claim_be
    FirstPaySalePnlCtrl.prototype.onClickClaim = function (event, data) {
        var _this = this;
        var list = this.list.filter(function (m) { return m.isCanClaim(); });
        list.forEach(function (m) { return _this.claimGeneralReward(m); });
        this.emit(EventType_1.default.UPDATE_ACITIVITIES);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    FirstPaySalePnlCtrl.prototype.init = function () {
        var _this = this;
        var datas = assetsMgr.getJson('generalTask').datas.filter(function (m) { return m.cond.startsWith('1028,0,'); });
        this.list = this.task.getGeneralTasks().filter(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.RECHARGE_COUNT; }).sort(function (a, b) { return a.id - b.id; });
        var both = this.list.length === 2 && !this.list.some(function (m) { return !m.isCanClaim(); });
        this.rootNode_.Child('rewards/title').active = both;
        var _loop_1 = function (i) {
            var data = datas[i], rewards = GameHelper_1.gameHpr.stringToCTypes(data.reward), task = this_1.list.find(function (m) { return m.id === data.id; }), node = this_1.rootNode_.Child("rewards/day_" + i);
            var isClaimed = task ? task.isClaimed() : true, canClaim_1 = task ? task.isCanClaim() : false;
            node.Child('bg', cc.MultiFrame).setFrame(canClaim_1);
            node.Child('title').active = !both;
            node.Child('items').Items(rewards, function (it, data) {
                it.Data = data;
                if (data.type === Enums_1.CType.HERO_DEBRIS || data.type === Enums_1.CType.PAWN_SKIN) {
                    it.Component(cc.Button).interactable = true;
                    var node_1 = it.Swih('role')[0];
                    ResHelper_1.resHelper.loadPawnHeadIcon(data.id, node_1.Child('icon'), _this.key);
                    node_1.Child('done').active = isClaimed;
                    node_1.Child('icon').opacity = isClaimed ? 150 : 255;
                    if (data.type === Enums_1.CType.HERO_DEBRIS) {
                        _this.rootNode_.Child('role_name').setLocaleKey('portrayalText.name_' + data.id);
                        _this.rootNode_.Child('role_type').setLocaleKey('pawnText.name_' + Math.floor(data.id / 100));
                    }
                }
                else if (data.type === Enums_1.CType.HEAD_ICON) {
                    it.Component(cc.Button).interactable = true;
                    var node_2 = it.Swih('other')[0];
                    ViewHelper_1.viewHelper.updateItemByCTypeOne(node_2, data, _this.key, cc.size(76, 76), true);
                    node_2.Child('done').active = isClaimed;
                    node_2.Child('icon').opacity = isClaimed ? 150 : 255;
                }
                else {
                    it.Component(cc.Button).interactable = false;
                    var node_3 = it.Swih('other')[0];
                    ViewHelper_1.viewHelper.updateItemByCTypeOne(node_3, data, _this.key);
                    node_3.Child('done').active = isClaimed;
                    node_3.Child('icon').opacity = node_3.Child('count').opacity = isClaimed ? 150 : 255;
                }
            });
        };
        var this_1 = this;
        // 奖励
        for (var i = 0; i < datas.length; i++) {
            _loop_1(i);
        }
        // 按钮
        var showGoto = this.list.length === 2 && !this.list.some(function (m) { return m.isCanClaim(); }), canClaim = this.list.some(function (m) { return m.isCanClaim(); });
        var button = this.rootNode_.Child('buttons').Swih(showGoto ? 'goto_be' : canClaim ? 'claim_be' : 'time')[0];
        if (!showGoto && !canClaim) {
            button.Child('val', cc.LabelTimer).run(this.getNextClaimTimeMs(GameHelper_1.gameHpr.getServerNowTime()) * 0.001);
        }
    };
    FirstPaySalePnlCtrl.prototype.claimGeneralReward = function (data, heroId) {
        var _this = this;
        this.task.claimGeneralTaskReward(data.id, heroId).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            var hero = heroId ? new CTypeObj_1.default().init(Enums_1.CType.HERO_DEBRIS, heroId, 3) : data.rewards.find(function (m) { return m.type === Enums_1.CType.HERO_DEBRIS; });
            if (hero) {
                ViewHelper_1.viewHelper.showGainPortrayalDebris(hero.id, hero.count);
            }
            else {
                GameHelper_1.gameHpr.addGainMassage(data.rewards);
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
            }
            if (_this.isValid) {
                _this.init();
            }
        });
    };
    /**
     * 获取距离下次可领奖的剩余毫秒数
     * 基于当前时间判断，每天6点后可领奖一次
     * @param currentTimestamp 当前时间戳（毫秒）
     * @returns 距离下次可领奖的毫秒数（0表示可以立即领奖）
     */
    FirstPaySalePnlCtrl.prototype.getNextClaimTimeMs = function (currentTimestamp) {
        // 创建当前日期对象
        var currentDate = new Date(currentTimestamp);
        // 获取当前日期的6点时间戳
        var current6AM = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), 6, 0, 0, 0).getTime();
        // 如果当前时间已经超过今天6点，则可以领奖
        if (currentTimestamp >= current6AM) {
            // 计算明天6点的时间戳
            var tomorrow6AM = current6AM + 86400000; // 加上一天的毫秒数
            // 返回距离明天6点的毫秒数（表示下次可领奖时间）
            return tomorrow6AM - currentTimestamp;
        }
        else {
            // 如果当前时间早于今天6点，返回距离今天6点的毫秒数
            return current6AM - currentTimestamp;
        }
    };
    FirstPaySalePnlCtrl = __decorate([
        ccclass
    ], FirstPaySalePnlCtrl);
    return FirstPaySalePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = FirstPaySalePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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