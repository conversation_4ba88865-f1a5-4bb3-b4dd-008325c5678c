
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/CeriSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a0564ti+/FNwbGKntRXVavR', 'CeriSlotObj');
// app/script/model/main/CeriSlotObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 研究所槽位信息
var CeriSlotObj = /** @class */ (function () {
    function CeriSlotObj() {
        this.lv = 0; //需要研究所等级
        this.type = 0; //当前槽位类型
        this.id = 0; //当前已经研究的id
        this.selectIds = []; //选择列表
        this.surplusTime = 0; //开始研究时间
        this.resetCount = 0; //重置次数
        this.json = null;
        this.getTime = 0; //获取时间
        this.needTime = 0; //需要时间
        this.value = 0;
    }
    CeriSlotObj.prototype.fromSvr = function (data) {
        this.lv = data.lv;
        this.type = data.type;
        this.id = data.id || 0;
        this.selectIds = data.selectIds || [];
        this.surplusTime = data.surplusTime || 0;
        this.resetCount = data.resetCount || 0;
        var json = this.json = assetsMgr.getJsonData('ceri', this.id);
        this.getTime = Date.now();
        this.needTime = ((json === null || json === void 0 ? void 0 : json.time) || 0) * this.lv;
        this.value = (json === null || json === void 0 ? void 0 : json.value) || 0;
        return this;
    };
    // 是否可以研究
    CeriSlotObj.prototype.isCanStudy = function () {
        return !this.id && !this.surplusTime;
    };
    CeriSlotObj.prototype.isDone = function () {
        return this.id > 0 && !this.surplusTime;
    };
    // 获取实际的剩余时间
    CeriSlotObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 是否开始
    CeriSlotObj.prototype.isRuning = function () {
        return this.surplusTime > 0;
    };
    CeriSlotObj.prototype.getValue = function () {
        return this.isDone() ? this.value : 0;
    };
    CeriSlotObj.prototype.getValueName = function () {
        if (!this.value) {
            return '';
        }
        else if (this.type === 1) { //政策
            return 'policyText.name_' + this.value;
        }
        else if (this.type === 2) { //士兵
            return 'pawnText.name_' + this.value;
        }
        else if (this.type === 3 || this.type === 4) { //装备
            return 'equipText.name_' + this.value;
        }
        return '';
    };
    return CeriSlotObj;
}());
exports.default = CeriSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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