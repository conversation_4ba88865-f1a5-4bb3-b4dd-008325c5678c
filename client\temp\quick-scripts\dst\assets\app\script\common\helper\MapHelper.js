
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/MapHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3da56iww4FDNLVFoOVbzdmz', 'MapHelper');
// app/script/common/helper/MapHelper.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapHelper = void 0;
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var Constant_1 = require("../constant/Constant");
var Enums_1 = require("../constant/Enums");
var GameHelper_1 = require("./GameHelper");
/**
 * 地图相关方法
 */
var MapHelper = /** @class */ (function () {
    function MapHelper() {
        this.MAP_SIZE = cc.v2(600, 600);
        this.tempRangePoints = [];
        this._temp_vec2_1 = cc.v2();
        this._temp_vec2_2 = cc.v2();
        this._temp_vec2_3 = cc.v2();
        this._temp_vec2_4 = cc.v2();
        this._temp_vec2_5 = cc.v2();
        this._temp_vec2_6 = cc.v2();
        this._temp_vec2_7 = cc.v2();
        this.cityMudList = [[[0, 1, 5], [1, 1, 5], [1, 0, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 3, 101], [3, 2, 101], [3, 1, 101], [3, 0, 101], [4, 0, 101], [5, -1, 101], [5, -2, 101], [4, -3, 101], [3, -1, 101], [2, -1, 101], [2, -2, 101], [2, -3, 101], [1, -3, 101], [0, -2, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [-3, 0, 101], [-3, -1, 101], [-4, 1, 101], [-5, 0, 101], [-5, -1, 101], [-4, -2, 101], [-1, -1, 101], [3, -4, 101], [0, 2, 5], [2, 2, 5], [4, -1, 5], [4, -2, 5], [3, -3, 5], [3, -2, 5], [2, 0, 5], [2, 1, 5], [1, 2, 5], [1, -1, 5], [1, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-4, -1, 5], [-4, 0, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 2, 101], [3, 1, 101], [4, 1, 101], [5, 0, 101], [6, -1, 101], [5, -2, 101], [4, -2, 101], [3, -1, 101], [3, 0, 101], [2, -2, 101], [0, -3, 101], [1, -3, 101], [-1, -3, 101], [-1, -2, 101], [-1, 1, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [-2, -1, 101], [-2, -2, 101], [-2, -4, 101], [-3, -3, 101], [-3, -1, 101], [-3, 2, 101], [-4, 1, 101], [-4, 0, 101], [0, 2, 5], [1, 2, 5], [2, 1, 5], [4, 0, 5], [5, -1, 5], [4, -1, 5], [2, 0, 5], [2, -1, 5], [1, -2, 5], [1, -1, 5], [0, -1, 5], [0, -2, 5], [-1, -1, 5], [-1, 0, 5], [-2, -3, 5], [-3, 0, 5], [-3, 1, 5]], [[0, 1, 5], [0, 0, 5], [1, 0, 5], [1, 1, 5], [0, 3, 101], [-1, 3, 101], [1, 2, 101], [2, 2, 101], [3, 1, 101], [4, 1, 101], [5, 0, 101], [5, -1, 101], [4, -2, 101], [3, -2, 101], [3, -1, 101], [3, 0, 101], [2, -3, 101], [1, -3, 101], [0, -2, 101], [-1, -2, 101], [-1, -1, 101], [-2, 0, 101], [-2, 1, 101], [-2, 2, 101], [-3, 1, 101], [-2, -3, 101], [-3, -3, 101], [-4, -2, 101], [-4, -1, 101], [-4, 0, 101], [0, 2, 5], [-1, 2, 5], [-1, 1, 5], [-1, 0, 5], [2, 1, 5], [4, 0, 5], [4, -1, 5], [2, 0, 5], [2, -1, 5], [2, -2, 5], [1, -2, 5], [1, -1, 5], [0, -1, 5], [-2, -1, 5], [-3, 0, 5], [-3, -1, 5], [-3, -2, 5], [-2, -2, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 2, 101], [3, 1, 101], [3, 0, 101], [3, -1, 101], [4, -1, 101], [5, -2, 101], [5, -3, 101], [4, -4, 101], [3, -4, 101], [2, -2, 101], [2, -1, 101], [1, -3, 101], [2, -3, 101], [0, -3, 101], [-1, 4, 101], [-2, 4, 101], [-2, 0, 101], [-2, -1, 101], [-1, -2, 101], [-3, 0, 101], [-3, 1, 101], [-3, 2, 101], [-3, 3, 101], [-4, 0, 101], [-4, -1, 101], [-3, -2, 101], [0, 2, 5], [1, 2, 5], [2, 1, 5], [4, -2, 5], [4, -3, 5], [3, -3, 5], [3, -2, 5], [2, 0, 5], [1, -1, 5], [1, -2, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-1, 2, 5], [-1, 3, 5], [-2, 3, 5], [-2, 2, 5], [-2, 1, 5], [-1, -1, 5], [-3, -1, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 2, 101], [3, 1, 101], [4, 0, 101], [3, -1, 101], [2, -2, 101], [1, -3, 101], [0, -3, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [-2, -1, 101], [-1, -1, 101], [-1, -2, 101], [-2, -3, 101], [-3, -3, 101], [-3, 0, 101], [-4, -1, 101], [-4, -2, 101], [0, 2, 5], [1, 2, 5], [2, 1, 5], [3, 0, 5], [2, -1, 5], [2, 0, 5], [1, -1, 5], [1, -2, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-2, -2, 5], [-3, -2, 5], [-3, -1, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 2, 101], [1, 2, 101], [2, 2, 101], [3, 1, 101], [3, 0, 101], [4, -1, 101], [4, -2, 101], [3, -3, 101], [2, -3, 101], [2, -1, 101], [1, -2, 101], [-1, 1, 101], [-1, -2, 101], [-2, -1, 101], [-2, 0, 101], [0, -3, 101], [2, 1, 5], [3, -1, 5], [3, -2, 5], [2, -2, 5], [2, 0, 5], [1, -1, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, -1, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 2, 101], [1, 3, 101], [2, 2, 101], [3, 1, 101], [4, 2, 101], [5, 1, 101], [5, 0, 101], [4, -1, 101], [3, 0, 101], [2, -1, 101], [0, -2, 101], [1, -2, 101], [-1, -1, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [1, 2, 5], [2, 1, 5], [4, 1, 5], [4, 0, 5], [2, 0, 5], [1, -1, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 2, 101], [3, 1, 101], [4, 0, 101], [5, 1, 101], [6, 0, 101], [5, -1, 101], [3, -1, 101], [2, -2, 101], [1, -3, 101], [0, -3, 101], [-1, 2, 101], [-2, 3, 101], [-2, 1, 101], [-1, -2, 101], [-2, -2, 101], [-3, -1, 101], [-3, 0, 101], [-3, 2, 101], [0, 2, 5], [1, 2, 5], [2, 1, 5], [3, 0, 5], [5, 0, 5], [2, -1, 5], [2, 0, 5], [1, -1, 5], [1, -2, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-2, 2, 5], [-2, 0, 5], [-2, -1, 5], [-1, -1, 5]], [[0, 1, 5], [1, 1, 5], [1, 0, 5], [0, 0, 5], [0, 2, 101], [0, 3, 101], [1, 3, 101], [2, 3, 101], [3, 2, 101], [4, 1, 101], [4, 0, 101], [3, -1, 101], [4, -2, 101], [3, -3, 101], [2, -2, 101], [2, -1, 101], [1, -2, 101], [-1, 2, 101], [-1, 4, 101], [-2, 4, 101], [-2, 1, 101], [-1, -3, 101], [0, -3, 101], [-2, -2, 101], [-3, -1, 101], [-3, 0, 101], [-3, 2, 101], [-3, 3, 101], [2, 2, 5], [3, 1, 5], [3, 0, 5], [3, -2, 5], [2, 0, 5], [2, 1, 5], [1, 2, 5], [1, -1, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-1, 3, 5], [-2, 3, 5], [-2, 2, 5], [-2, 0, 5], [-2, -1, 5], [-1, -1, 5], [-1, -2, 5]], [[0, 1, 5], [1, 0, 5], [1, 1, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 3, 101], [3, 2, 101], [4, 3, 101], [5, 2, 101], [5, 1, 101], [4, 0, 101], [4, -1, 101], [4, -2, 101], [3, 0, 101], [3, 1, 101], [2, 0, 101], [3, -3, 101], [2, -4, 101], [1, -4, 101], [0, -3, 101], [-1, -2, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [-2, -1, 101], [-1, -4, 101], [-2, -3, 101], [0, 2, 5], [2, 2, 5], [4, 2, 5], [4, 1, 5], [3, -2, 5], [3, -1, 5], [2, 1, 5], [2, -1, 5], [2, -2, 5], [2, -3, 5], [1, -3, 5], [1, -2, 5], [1, -1, 5], [1, 2, 5], [0, -1, 5], [0, -2, 5], [-1, -3, 5], [-1, -1, 5], [-1, 0, 5], [-1, 1, 5]], [[0, 1, 5], [1, 1, 5], [1, 0, 5], [0, 0, 5], [0, 3, 101], [1, 3, 101], [2, 3, 101], [3, 2, 101], [3, 1, 101], [4, 1, 101], [5, 0, 101], [5, -1, 101], [4, -2, 101], [3, -1, 101], [3, 0, 101], [2, -2, 101], [1, -3, 101], [0, -3, 101], [-1, 2, 101], [-2, 1, 101], [-2, 0, 101], [-1, -1, 101], [-1, -2, 101], [-2, -2, 101], [-3, -1, 101], [0, 2, 5], [2, 2, 5], [4, 0, 5], [4, -1, 5], [2, 0, 5], [2, 1, 5], [1, 2, 5], [1, -1, 5], [2, -1, 5], [1, -2, 5], [0, -2, 5], [0, -1, 5], [-1, 0, 5], [-1, 1, 5], [-2, -1, 5]]];
    }
    // 获取两点的距离
    MapHelper.prototype.getPointToPointDis = function (a, b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
    };
    MapHelper.prototype.getIndexToIndexDis = function (a, b) {
        return this.getPointToPointDis(this.indexToPoint(a, this.MAP_SIZE, this._temp_vec2_5), this.indexToPoint(b, this.MAP_SIZE, this._temp_vec2_6));
    };
    // 获取两个点的距离排序值 值越大越近
    MapHelper.prototype.getPointDisSortVal = function (point, target) {
        var dis = this.getPointToPointDis(target, point);
        var mag = point.sub(target, this._temp_vec2_7).magSqr();
        var weight = 1;
        weight = weight * 100 + (99 - dis);
        weight = weight * 1000 + (999 - mag);
        weight = weight * 100 + target.x;
        return weight;
    };
    // 根据中心点算出范围点
    MapHelper.prototype.getRangePointsByPoint = function (point, maxRange) {
        var i = 0, l = this.tempRangePoints.length;
        for (var x = -maxRange.x; x <= maxRange.x; x++) {
            for (var y = -maxRange.y; y <= maxRange.y; y++) {
                var px = point.x + x, py = point.y + y;
                if (i < l) {
                    this.tempRangePoints[i].set2(px, py);
                }
                else {
                    this.tempRangePoints.push(cc.v2(px, py));
                }
                i += 1;
            }
        }
        if (i < l) {
            this.tempRangePoints.splice(i);
        }
        return this.tempRangePoints;
    };
    /**
     * 根据网格坐标获取像素坐标
     * @param point [传入的point不会被改变]
     */
    MapHelper.prototype.getPixelByPoint = function (point, out) {
        out = out || this._temp_vec2_1;
        return point.mul(Constant_1.TILE_SIZE, out).addSelf(Constant_1.TILE_SIZE_HALF);
    };
    /**
     * 根据像素位置获取网格坐标
     * @param pos
     * @param type
     */
    MapHelper.prototype.getPointByPixel = function (pos, out) {
        out = out || this._temp_vec2_2;
        return out.set2(Math.floor(pos.x / Constant_1.TILE_SIZE), Math.floor(pos.y / Constant_1.TILE_SIZE));
    };
    // 点转成下标
    MapHelper.prototype.pointToIndex = function (point, size) {
        return this.pointToIndexByNumer(point.x, point.y, size);
    };
    MapHelper.prototype.pointToIndexByNumer = function (x, y, size) {
        size = size || this.MAP_SIZE;
        if (x < 0 || x >= size.x || y < 0 || y >= size.y) {
            return -1;
        }
        return y * size.x + x;
    };
    // 下标转成点
    MapHelper.prototype.indexToPoint = function (index, size, out) {
        size = size || this.MAP_SIZE;
        out = out || this._temp_vec2_3;
        return out.set2(index % size.x, Math.floor(index / size.x));
    };
    // 是否超边了 true超出边界 false在边界内
    MapHelper.prototype.isBorder = function (x, y, size) {
        return x < 0 || y < 0 || x >= size.x || y >= size.y;
    };
    // 获取地块底
    MapHelper.prototype.getLandDi = function (index, lands) {
        var _a;
        var type = this.getLandDiType(lands[index]);
        if (type === Enums_1.LandType.CEREAL) {
            return 'land_di_3_01'; //粮食地 直接返回
        }
        var point = this.indexToPoint(index), arr = [
            this.isOneLandDi(lands[this.pointToIndexByNumer(point.x, point.y + 1)], type),
            this.isOneLandDi(lands[this.pointToIndexByNumer(point.x + 1, point.y)], type),
            this.isOneLandDi(lands[this.pointToIndexByNumer(point.x, point.y - 1)], type),
            this.isOneLandDi(lands[this.pointToIndexByNumer(point.x - 1, point.y)], type),
        ];
        var no = ((_a = Constant_1.LAND_DI_CONF.find(function (m) { return m.list.every(function (b, i) { return arr[i] === b; }); })) === null || _a === void 0 ? void 0 : _a.no) || '01';
        return "land_di_" + type + "_" + no;
    };
    MapHelper.prototype.getLandDiType = function (land) {
        if (typeof (land) === 'number') {
            return assetsMgr.getJsonData('land', land).type;
        }
        else if (!land) {
            return 0;
        }
        else if (land.cityLandType) {
            return land.cityLandType;
        }
        return land.landType;
    };
    MapHelper.prototype.isOneLandDi = function (land, type) {
        var lt = this.getLandDiType(land);
        return lt && lt === type ? 1 : 0;
    };
    // 刷新城市底
    MapHelper.prototype.updateCityLandDi = function (cell, type, cells) {
        cell.cityLandType = type;
    };
    MapHelper.prototype.isOneCityLandDi = function (cell, cityId, type, cells) {
        if (!cell) {
            return 0;
        }
        else if (Math.abs(cell.cityId) === cityId) {
            return 1;
        }
        else if (cell.landType === type) {
            return 1;
        }
        return 0;
    };
    // 刷新单个玩家的边框线信息
    MapHelper.prototype.updatePlayerCellBorderLines = function (cells) {
        var _this = this;
        cells === null || cells === void 0 ? void 0 : cells.forEach(function (cell) {
            var p = cell.point;
            cell.borderLines.length = 0;
            var b0 = !cells.get(_this.pointToIndexByNumer(p.x, p.y + 1)) && cell.borderLines.push(0); //上
            var b1 = !cells.get(_this.pointToIndexByNumer(p.x + 1, p.y)) && cell.borderLines.push(1); //右
            var b2 = !cells.get(_this.pointToIndexByNumer(p.x, p.y - 1)) && cell.borderLines.push(2); //下
            var b3 = !cells.get(_this.pointToIndexByNumer(p.x - 1, p.y)) && cell.borderLines.push(3); //左
            if (!b3 && !b0) {
                !cells.get(_this.pointToIndexByNumer(p.x - 1, p.y + 1)) && cell.borderLines.push(4); //左上
            }
            if (!b1 && !b0) {
                !cells.get(_this.pointToIndexByNumer(p.x + 1, p.y + 1)) && cell.borderLines.push(5); //右上
            }
            if (!b1 && !b2) {
                !cells.get(_this.pointToIndexByNumer(p.x + 1, p.y - 1)) && cell.borderLines.push(6); //右下
            }
            if (!b3 && !b2) {
                !cells.get(_this.pointToIndexByNumer(p.x - 1, p.y - 1)) && cell.borderLines.push(7); //左下
            }
        });
    };
    // 获取城墙列表信息
    MapHelper.prototype.getWallBuildInfo = function (size) {
        if (size.x < 3 || size.y < 3) {
            return [];
        }
        var arr = [], sx = size.x - 1, sy = size.y - 1;
        // 4个角
        arr.push({ point: cc.v2(0, sy), type: 'CORNER', dir: 0 }); //左上
        arr.push({ point: cc.v2(sx, sy), type: 'CORNER', dir: 1 }); //右上
        arr.push({ point: cc.v2(sx, 0), type: 'CORNER', dir: 2 }); //右下
        arr.push({ point: cc.v2(0, 0), type: 'CORNER', dir: 3 }); //左下
        // 城门
        arr.push({ point: cc.v2(Math.floor(size.x * 0.5), sy), type: 'DOOR', dir: 0 }); //上
        arr.push({ point: cc.v2(sx, Math.floor(size.y * 0.5)), type: 'DOOR', dir: 1 }); //右
        arr.push({ point: cc.v2(Math.floor(size.x * 0.5), 0), type: 'DOOR', dir: 2 }); //下
        arr.push({ point: cc.v2(0, Math.floor(size.y * 0.5)), type: 'DOOR', dir: 3 }); //左
        // 城墙
        var wallPosArr = [1, 2, 4, 5];
        wallPosArr.forEach(function (p, i) {
            arr.push({ point: cc.v2(p, sy), type: 'WALL', dir: 0, index: i + 1 }); //上
            arr.push({ point: cc.v2(p, 0), type: 'WALL', dir: 2, index: i + 1 }); //下
            arr.push({ point: cc.v2(sx, sy - p), type: 'WALL', dir: 1, index: i + 1 }); //右
            arr.push({ point: cc.v2(0, sy - p), type: 'WALL', dir: 3, index: i + 1 }); //左
        });
        return arr;
    };
    // 获得关口位置
    MapHelper.prototype.getPassPoints = function (size) {
        var points = [cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0)];
        var cx = Math.floor(size.x / 2), cy = Math.floor(size.y / 2);
        var mx = size.x - 1, my = size.y - 1;
        points[0].set2(cx, my); //0.上
        points[1].set2(mx, cy); //1.右
        points[2].set2(cx, 0); //2.下
        points[3].set2(0, cy); //3.左
        return points;
    };
    // 栅栏位置
    MapHelper.prototype.getAreaFencePoints = function (size, outFlames) {
        outFlames.length = 0;
        var obj = {};
        var cx = Math.floor(size.x / 2), cy = Math.floor(size.y / 2);
        this.putFencePoint(cc.v2(cx - 1, size.y), obj, outFlames);
        this.putFencePoint(cc.v2(cx + 1, size.y), obj, outFlames);
        this.putFencePoint(cc.v2(size.x, cy - 1), obj, outFlames);
        this.putFencePoint(cc.v2(size.x, cy + 1), obj, outFlames);
        this.putFencePoint(cc.v2(cx - 1, -1), obj, outFlames);
        this.putFencePoint(cc.v2(cx + 1, -1), obj, outFlames);
        this.putFencePoint(cc.v2(-1, cy - 1), obj, outFlames);
        this.putFencePoint(cc.v2(-1, cy + 1), obj, outFlames);
        for (var x = 0; x < size.x; x++) { //上
            var id = x + '_' + size.y;
            if (!obj[id] && x !== cx) {
                obj[id] = '07';
            }
        }
        for (var y = 0; y < size.y; y++) { //右
            var id = size.x + '_' + y;
            if (!obj[id] && y !== cy) {
                obj[id] = '08';
            }
        }
        for (var x = 0; x < size.x; x++) { //下
            var id = x + '_' + -1;
            if (!obj[id] && x !== cx) {
                obj[id] = '07';
            }
        }
        for (var y = 0; y < size.y; y++) { //右
            var id = -1 + '_' + y;
            if (!obj[id] && y !== cy) {
                obj[id] = '08';
            }
        }
        return obj;
    };
    MapHelper.prototype.putFencePoint = function (point, outObj, outFlames) {
        outObj[point.ID()] = '09';
        outFlames.push(point);
    };
    // 获取联盟旗帜位置
    MapHelper.prototype.getAlliFlagPoints = function (size) {
        var points = [];
        points.push(cc.v2(-1, size.y)); //左上
        points.push(cc.v2(size.x, size.y)); //右上
        points.push(cc.v2(-1, -1)); //左下
        points.push(cc.v2(size.x, -1)); //右下
        return points;
    };
    // 获取遗迹装饰位置
    MapHelper.prototype.getAncientDecoratePoints = function (size) {
        var points = [];
        points.push(cc.v2(1, size.y - 2)); //左上
        points.push(cc.v2(size.x - 2, size.y - 2)); //右上
        points.push(cc.v2(1, 1)); //左下
        points.push(cc.v2(size.x - 2, 1)); //右下
        return points;
    };
    // 禁止摆放士兵的位置
    MapHelper.prototype.getBanPlacePawnPoints = function (size) {
        var cx = Math.floor(size.x / 2), cy = Math.floor(size.y / 2);
        var points = [];
        points.pushArr(this.genPointsBySize(cc.v2(5, 2), cc.v2(cx - 2, size.y - 2))); //上
        points.pushArr(this.genPointsBySize(cc.v2(2, 5), cc.v2(size.x - 2, cy - 2))); //右
        points.pushArr(this.genPointsBySize(cc.v2(5, 2), cc.v2(cx - 2, 0))); //下
        points.pushArr(this.genPointsBySize(cc.v2(2, 5), cc.v2(0, cy - 2))); //左
        return points;
    };
    // 获取区域用于攻击的点
    MapHelper.prototype.getMainPoints = function (areaSize, buildSize, buildOrigin) {
        var cx = Math.floor(areaSize.x / 2), cy = Math.floor(areaSize.y / 2);
        if (buildSize.x * buildSize.y < 9) {
            return [cc.v2(cx, cy)];
        }
        var points = [cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0), cc.v2(0, 0)];
        points[0].set2(cx, buildOrigin.y + buildSize.y - 1); //0.上
        points[1].set2(buildOrigin.x + buildSize.x - 1, cy); //1.右
        points[2].set2(cx, buildOrigin.y); //2.下
        points[3].set2(buildOrigin.x, cy); //3.左
        return points;
    };
    MapHelper.prototype.getMainPointsByBoss = function (buildSize, buildOrigin) {
        var points = [];
        for (var x = 0; x < buildSize.x; x++) {
            points.push(cc.v2(buildOrigin.x + x, buildOrigin.y));
            points.push(cc.v2(buildOrigin.x + x, buildOrigin.y + buildSize.y - 1));
        }
        for (var y = 1; y < buildSize.y - 1; y++) {
            points.push(cc.v2(buildOrigin.x, buildOrigin.y + y));
            points.push(cc.v2(buildOrigin.x + buildSize.x - 1, buildOrigin.y + y));
        }
        return points;
    };
    // 根据大小生成站位点
    MapHelper.prototype.genPointsBySize = function (size, start) {
        start = start || cc.v2();
        var arr = [];
        for (var x = 0; x < size.x; x++) {
            for (var y = 0; y < size.y; y++) {
                arr.push(cc.v2(start.x + x, start.y + y));
            }
        }
        return arr;
    };
    // 获取一个点的外圈
    MapHelper.prototype.getOnePointOuter = function (start, size) {
        var arr = [];
        for (var x = 0; x < size.x; x++) {
            for (var y = 0; y < size.y; y++) {
                var px = x, py = y + 1;
                if (py >= size.y && (start.y + py) < this.MAP_SIZE.y) { //上
                    this.addOuterPoint(arr, px, py, start);
                }
                px = x, py = y - 1;
                if (py < 0 && (start.y + py) >= 0) { //下
                    this.addOuterPoint(arr, px, py, start);
                }
                px = x - 1, py = y;
                if (px < 0 && (start.x + px) >= 0) { //左
                    this.addOuterPoint(arr, px, py, start);
                }
                px = x + 1, py = y;
                if (px >= size.x && (start.x + px) < this.MAP_SIZE.x) { //右
                    this.addOuterPoint(arr, px, py, start);
                }
            }
        }
        return arr;
    };
    MapHelper.prototype.addOuterPoint = function (arr, px, py, start) {
        var p = cc.v2(px, py).addSelf(start);
        if (!arr.some(function (m) { return m.equals(p); })) {
            arr.push(p);
        }
    };
    // 获取地块的所有外圈
    MapHelper.prototype.getCellsOuter = function (cells, distance) {
        var size = this.MAP_SIZE, obj = {}, indexs = [];
        cells === null || cells === void 0 ? void 0 : cells.forEach(function (cell) {
            var point = cell.point;
            var x = point.x, y = point.y + distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y < size.y) { //上
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x + distance, y = point.y, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && x < size.x) { //右
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x, y = point.y - distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y >= 0) { //下
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x - distance, y = point.y, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && x >= 0) { //左
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x - distance, y = point.y + distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y < size.y && x >= 0) { //左上
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x + distance, y = point.y + distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y < size.y && x < size.x) { //右上
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x + distance, y = point.y - distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y >= 0 && x < size.x) { //右下
                obj[index] = true;
                indexs.push(index);
            }
            x = point.x - distance, y = point.y - distance, index = exports.mapHelper.pointToIndexByNumer(x, y);
            if (!cells.get(index) && !obj[index] && y >= 0 && x >= 0) { //左下
                obj[index] = true;
                indexs.push(index);
            }
        });
        return indexs;
    };
    // 修正速度
    MapHelper.prototype.amendMoveSpeed = function (speed, position, targetPosition) {
        var diffx = targetPosition.x - position.x;
        if (Math.abs(diffx) <= Math.abs(speed.x)) {
            speed.x = diffx;
        }
        var diffy = targetPosition.y - position.y;
        if (Math.abs(diffy) <= Math.abs(speed.y)) {
            speed.y = diffy;
        }
        return speed;
    };
    // 重新获取两个点最近的点
    MapHelper.prototype.getMinDisPoint = function (sps, tps) {
        var t = tps[0].clone(), s = sps[0].clone();
        var d = s.sub(t).Length();
        sps.forEach(function (s1) {
            var d1 = s1.sub(t).Length();
            if (d1 < d) {
                d = d1;
                s.set(s1);
            }
        });
        tps.forEach(function (t1) {
            var d1 = t1.sub(s).Length();
            if (d1 < d) {
                d = d1;
                t.set(t1);
            }
        });
        return [s, t];
    };
    // 获取最近的
    MapHelper.prototype.getMinDisIndex = function (point, points) {
        var _this = this;
        if (!(points === null || points === void 0 ? void 0 : points.length)) {
            return -1;
        }
        else if (points.length === 1) {
            return 0;
        }
        var idx = 0;
        var d = this.getPointToPointDis(points[idx], point);
        points.forEach(function (p, i) {
            var l = _this.getPointToPointDis(p, point);
            if (l < d) {
                d = l;
                idx = i;
            }
        });
        return idx;
    };
    MapHelper.prototype.getMinDis = function (point, points) {
        var i = this.getMinDisIndex(point, points);
        if (i != -1) {
            return points[i];
        }
        return point;
    };
    // 获取移动需要的时间 毫秒
    MapHelper.prototype.getMoveNeedTime = function (points, velocity) {
        if (points.length <= 1) {
            return 0;
        }
        // 获取总距离
        var point = this.getPixelByPoint(points[0]).clone(), len = 0;
        for (var i = 1, l = points.length; i < l; i++) {
            var next = this.getPixelByPoint(points[i]).clone();
            len += Math.floor(point.sub(next, this._temp_vec2_4).mag() * 1000);
            point = next;
        }
        // 计算需要时间
        var time = Math.floor(len / velocity);
        // cc.log(points.join2(m => m.Join(), '|') + ' = ' + Math.floor(len * 0.001) + ', time=' + time + 'ms')
        return time;
    };
    /**
     * 获取指定点位指定曼哈顿距离内的所有点
     * @param index 起始地块索引
     * @param dis 曼哈顿距离
     * @param mapSize 地图大小，如果不传则使用默认地图大小
     * @returns 返回所有在指定曼哈顿距离内的地块索引数组
     */
    MapHelper.prototype.getManhattanPoints = function (point, dis, mapSize) {
        var arr = [];
        if (dis < 0) {
            return arr; // 距离为负时返回空
        }
        var size = mapSize || this.MAP_SIZE;
        var px = point.x, py = point.y;
        for (var d = 0; d <= dis; d++) {
            // 遍历 x 方向的偏移量（从 -d 到 d）
            for (var xDiff = -d; xDiff <= d; xDiff++) {
                var yAbs = d - Math.abs(xDiff); // y 方向的绝对偏移量
                if (yAbs < 0) {
                    continue; // 无效偏移量
                }
                else if (yAbs === 0) {
                    // 添加点 (xDiff, 0)
                    var targetIndex = this.pointToIndexByNumer(px + xDiff, py, size);
                    if (targetIndex >= 0) {
                        arr.push(targetIndex);
                    }
                }
                else {
                    // 添加点 (xDiff, yAbs) 和 (xDiff, -yAbs)
                    var targetIndex1 = this.pointToIndexByNumer(px + xDiff, py + yAbs, size);
                    var targetIndex2 = this.pointToIndexByNumer(px + xDiff, py - yAbs, size);
                    if (targetIndex1 >= 0) {
                        arr.push(targetIndex1);
                    }
                    if (targetIndex2 >= 0) {
                        arr.push(targetIndex2);
                    }
                }
            }
        }
        return arr;
    };
    MapHelper.prototype.getManhattanMainCityInfo = function (point, dis) {
        if (dis < 0) {
            return null; // 距离为负时返回空
        }
        var world = GameHelper_1.gameHpr.world;
        var px = point.x, py = point.y;
        for (var d = 0; d <= dis; d++) {
            // 遍历 x 方向的偏移量（从 -d 到 d）
            for (var xDiff = -d; xDiff <= d; xDiff++) {
                var yAbs = d - Math.abs(xDiff); // y 方向的绝对偏移量
                if (yAbs < 0) {
                    continue; // 无效偏移量
                }
                var cells = [];
                if (yAbs === 0) {
                    // 添加点 (xDiff, 0)
                    var targetIndex = this.pointToIndexByNumer(px + xDiff, py);
                    if (targetIndex >= 0) {
                        cells.push(world.getMapCellByIndex(targetIndex));
                    }
                }
                else {
                    // 添加点 (xDiff, yAbs) 和 (xDiff, -yAbs)
                    var targetIndex1 = this.pointToIndexByNumer(px + xDiff, py + yAbs);
                    var targetIndex2 = this.pointToIndexByNumer(px + xDiff, py - yAbs);
                    if (targetIndex1 >= 0) {
                        cells.push(world.getMapCellByIndex(targetIndex1));
                    }
                    if (targetIndex2 >= 0) {
                        cells.push(world.getMapCellByIndex(targetIndex2));
                    }
                }
                var cell = cells.find(function (m) { return m.isMainCity() && GameHelper_1.gameHpr.checkPlayerIsProtectMode(world.getPlayerInfo(m.owner)); });
                if (cell) {
                    return cell;
                }
            }
        }
        return null;
    };
    // 检测两条线段是否相交 并返回相交点
    MapHelper.prototype.segmentsIntr = function (a, b, c, d) {
        // 三角形abc 面积的2倍
        var area_abc = (a.x - c.x) * (b.y - c.y) - (a.y - c.y) * (b.x - c.x);
        // 三角形abd 面积的2倍
        var area_abd = (a.x - d.x) * (b.y - d.y) - (a.y - d.y) * (b.x - d.x);
        // 面积符号相同则两点在线段同侧，不相交
        if (area_abc * area_abd >= 0) {
            return null;
        }
        // 三角形cda 面积的2倍
        var area_cda = (c.x - a.x) * (d.y - a.y) - (c.y - a.y) * (d.x - a.x);
        // 三角形cdb 面积的2倍 (这里有一个小优化，不需要再用公司计算面积，而是通过已知的三个面积加减得出)
        var area_cdb = area_cda + area_abc - area_abd;
        if (area_cda * area_cdb >= 0) {
            return null;
        }
        // 计算交点坐标
        var t = area_cda / (area_abd - area_abc);
        var dx = t * (b.x - a.x), dy = t * (b.y - a.y);
        return cc.v2(a.x + dx, a.y + dy);
    };
    // 获取方向 0.上 1.右 2.下 3.左
    MapHelper.prototype.getDirByPoint = function (spoint, epoint) {
        var point = epoint.sub(spoint);
        if (point.y != 0 && Math.abs(point.y) >= Math.abs(point.x)) {
            if (point.y >= 0) {
                return 2;
            }
            else {
                return 0;
            }
        }
        else if (point.x >= 0) {
            return 3;
        }
        else {
            return 1;
        }
    };
    MapHelper.prototype.getDirByIndex = function (sindex, eindex, size) {
        return this.getDirByPoint(this.indexToPoint(sindex, size), this.indexToPoint(eindex, size));
    };
    // 获取方向
    MapHelper.prototype.getAddArmyDir = function (sindex, tindex) {
        var _a = __read(exports.mapHelper.getMinDisPoint(GameHelper_1.gameHpr.world.getMapCellByIndex(sindex).getOwnPoints(), GameHelper_1.gameHpr.world.getMapCellByIndex(tindex).getOwnPoints()), 2), sp = _a[0], tp = _a[1];
        return exports.mapHelper.getDirByPoint(sp, tp);
    };
    // 解压地图信息
    MapHelper.prototype.uncompressMapLandInfo = function (serverLands) {
        var lands = [];
        var masks = ProtoHelper_1.protoHelper.getByteMask();
        var bitCount = 6; //每格地图所占字节数
        var lastBitCount = 0, lastByte = 0;
        for (var i = 0, l = serverLands.length; i < l; i++) {
            var curByte = 0;
            var landByte = serverLands[i];
            var leftBitCount = 0;
            if (lastBitCount > 0) {
                // 前几位存储在上一个字节
                curByte = lastByte & masks[lastBitCount];
                curByte = curByte << (bitCount - lastBitCount);
                // 后几位在当前字节
                leftBitCount = 8 - (bitCount - lastBitCount);
                curByte |= landByte >> leftBitCount;
                lands.push(curByte);
                // 当前字节剩余位数足够一格地图数据
                if (leftBitCount >= bitCount) {
                    var useCount = 8 - leftBitCount;
                    var extraByte = 0;
                    extraByte = landByte & masks[leftBitCount];
                    leftBitCount = 8 - useCount - leftBitCount;
                    extraByte = extraByte >> leftBitCount;
                    lands.push(extraByte);
                }
            }
            else {
                // 数据都在当前字节
                leftBitCount = 8 - bitCount;
                curByte |= landByte >> leftBitCount;
                lands.push(curByte);
            }
            lastBitCount = leftBitCount;
            lastByte = landByte;
        }
        return lands;
    };
    // 比较两个points数组是否一样
    MapHelper.prototype.pointsEquals = function (arr1, arr2) {
        if (arr1.length !== arr2.length) {
            return false;
        }
        for (var i = 0, l = arr1.length; i < l; i++) {
            if (!arr1[i].equals(arr2[i])) {
                return false;
            }
        }
        return true;
    };
    /**
     * 拷贝路径
     * @param outs
     * @param paths
     */
    MapHelper.prototype.clonePath = function (outs, paths) {
        if (!outs) {
            return false;
        }
        for (var i = 0; i < paths.length; i++) {
            if (i < outs.length) {
                outs[i].set(paths[i]);
            }
            else {
                outs.push(paths[i].clone());
            }
        }
        outs.splice(paths.length, outs.length - paths.length);
        return outs.length > 0;
    };
    // 检测桥是否可以通行
    MapHelper.prototype.checkBridgeCanPass = function (cell, nearCell) {
        var mapSizeX = this.MAP_SIZE.x, mapSizeY = this.MAP_SIZE.y;
        var dx = ut.normalizeNumber(nearCell.actPoint.x - cell.actPoint.x);
        var dy = ut.normalizeNumber(nearCell.actPoint.y - cell.actPoint.y);
        if (dx === 0 && dy === 0) {
            return false;
        }
        var p = nearCell.actPoint.clone();
        while (true) {
            p.x += dx;
            p.y += dy;
            if (p.x < 0 || p.y < 0 || p.x >= mapSizeX || p.y >= mapSizeY) {
                break;
            }
            var nextCell = GameHelper_1.gameHpr.world.getMapCellByPoint(p);
            if (!nextCell) {
                break;
            }
            else if (nextCell.landType === Enums_1.LandType.PASS) {
                continue;
            }
            else if (nextCell.isOneAlliance()) {
                return true;
            }
            break;
        }
        return false;
    };
    return MapHelper;
}());
exports.mapHelper = new MapHelper();
if (cc.sys.isBrowser) {
    window['mapHelper'] = exports.mapHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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