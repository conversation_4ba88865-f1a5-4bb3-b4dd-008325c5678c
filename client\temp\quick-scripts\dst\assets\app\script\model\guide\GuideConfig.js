
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/GuideConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b4d1b4Q2tpHd64w9LEpo9xK', 'GuideConfig');
// app/script/model/guide/GuideConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GUIDE_CONFIG = exports.GuideTextArgsType = exports.GuideTagType = exports.GuideStepType = void 0;
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
// 步骤类型
var GuideStepType;
(function (GuideStepType) {
    GuideStepType[GuideStepType["NONE"] = 0] = "NONE";
    GuideStepType[GuideStepType["DELAY"] = 1] = "DELAY";
    GuideStepType[GuideStepType["DIALOG"] = 2] = "DIALOG";
    GuideStepType[GuideStepType["ON_EVENT"] = 3] = "ON_EVENT";
    GuideStepType[GuideStepType["ON_EVENT_WAIT"] = 4] = "ON_EVENT_WAIT";
    GuideStepType[GuideStepType["CHECK"] = 5] = "CHECK";
    GuideStepType[GuideStepType["CHECK_WAIT"] = 6] = "CHECK_WAIT";
})(GuideStepType || (GuideStepType = {}));
exports.GuideStepType = GuideStepType;
// 标记类型
var GuideTagType;
(function (GuideTagType) {
    GuideTagType["FIRST_GUIDE_BEGIN"] = "FIRST_GUIDE_BEGIN";
    GuideTagType["RESTORE_MAIN_CITY"] = "RESTORE_MAIN_CITY";
    GuideTagType["FIRST_CREATE_ARMY"] = "FIRST_CREATE_ARMY";
    GuideTagType["FIRST_BATTLE_MOVE"] = "FIRST_BATTLE_MOVE";
    GuideTagType["FIRST_TRIGGER_BATTLE"] = "FIRST_TRIGGER_BATTLE";
    GuideTagType["CHECK_FIRST_BATTLE_END"] = "CHECK_FIRST_BATTLE_END";
    GuideTagType["CLAIM_TREASURE"] = "CLAIM_TREASURE";
    GuideTagType["CLAIM_TREASURE_COMPLETE"] = "CLAIM_TREASURE_COMPLETE";
    GuideTagType["READY_BT_BARRACKS"] = "READY_BT_BARRACKS";
    GuideTagType["CHECK_CAN_BT_BARRACKS"] = "CHECK_CAN_BT_BARRACKS";
    GuideTagType["CHECK_CAN_INDONE"] = "CHECK_CAN_INDONE";
    GuideTagType["CHOOSE_BTING_BUTTON"] = "CHOOSE_BTING_BUTTON";
    GuideTagType["CHECK_CAN_XL_PAWN"] = "CHECK_CAN_XL_PAWN";
    GuideTagType["FIRST_GUIDE_DONE"] = "FIRST_GUIDE_DONE";
    GuideTagType["PAWN_BACK_MAIN_GUIDE_BEGIN"] = "PAWN_BACK_MAIN_GUIDE_BEGIN";
    GuideTagType["PAWN_BACK_MAIN_GUIDE_END"] = "PAWN_BACK_MAIN_GUIDE_END";
    GuideTagType["PAWN_DRILL_COMPLETE"] = "PAWN_DRILL_COMPLETE";
    GuideTagType["ARMY_COUNT_EXTEND_BEGIN"] = "ARMY_COUNT_EXTEND_BEGIN";
    GuideTagType["ARMY_COUNT_EXTEND_END"] = "ARMY_COUNT_EXTEND_END";
    GuideTagType["BUILD_MAIN_UPGRADE_LV2"] = "BUILD_MAIN_UPGRADE_LV2";
    GuideTagType["FREE_BATTLE_GUIDE_BEGIN"] = "FREE_BATTLE_GUIDE_BEGIN";
    GuideTagType["FREE_BATTLE_GUIDE_CELL_LV2"] = "FREE_BATTLE_GUIDE_CELL_LV2";
    GuideTagType["WEAR_EQUIP_GUIDE_BEGIN"] = "WEAR_EQUIP_GUIDE_BEGIN";
    GuideTagType["WEAR_EQUIP_GUIDE_END"] = "WEAR_EQUIP_GUIDE_END";
    GuideTagType["FIND_FREE_PAWN"] = "FIND_FREE_PAWN";
    GuideTagType["BATTLE_GUIDE_BEGIN"] = "BATTLE_GUIDE_BEGIN";
    GuideTagType["BATTLE_GUIDE_END"] = "BATTLE_GUIDE_END";
    GuideTagType["OCCUPY_CASTLE_CHECK"] = "OCCUPY_CASTLE_CHECK";
    GuideTagType["PAET_4_GUIDE_BEGIN"] = "PAET_4_GUIDE_BEGIN";
    GuideTagType["PART_4_GUIDE_END"] = "PART_4_GUIDE_END";
    GuideTagType["ENTER_MAIN_CITY"] = "ENTER_MAIN_CITY";
    GuideTagType["EQUIP_STUDY_FINISH"] = "EQUIP_STUDY_FINISH";
    GuideTagType["SMITHY_GUIDE_DONE"] = "SMITHY_GUIDE_DONE";
    GuideTagType["HERO_GUIDE_BEGIN"] = "HERO_GUIDE_BEGIN";
    GuideTagType["HERO_GUIDE_END"] = "HERO_GUIDE_END";
    GuideTagType["BUILD_ACC_GUIDE_END"] = "BUILD_ACC_GUIDE_END";
    GuideTagType["HOSPITAL_GUIDE_BEGIN"] = "HOSPITAL_GUIDE_BEGIN";
    GuideTagType["HOSPITAL_GUIDE_END"] = "HOSPITAL_GUIDE_END";
    GuideTagType["HOSPITAL_GUIDE_SELECT_ARMY"] = "HOSPITAL_GUIDE_SELECT_ARMY";
    GuideTagType["LAND_STONE_GUIDE_BEGIN"] = "LAND_STONE_GUIDE_BEGIN";
    GuideTagType["LAND_STONE_GUIDE_END"] = "LAND_STONE_GUIDE_END";
    GuideTagType["LAND_STONE_GUIDE_WIND"] = "LAND_STONE_GUIDE_WIND";
    GuideTagType["LAND_STONE_GUIDE_TREASURE"] = "LAND_STONE_GUIDE_TREASURE";
    GuideTagType["BATTLE_BEGIN_GUIDE_END"] = "BATTLE_BEGIN_GUIDE_END";
    GuideTagType["ENEMY_FIRST_BATTLE_GUIDE"] = "ENEMY_FIRST_BATTLE_GUIDE";
    GuideTagType["NEWBIE_ZONE_GUIDE_COMPOSITE"] = "NEWBIE_ZONE_GUIDE_COMPOSITE";
    GuideTagType["NEWBIE_ZONE_GUIDE_END"] = "NEWBIE_ZONE_GUIDE_END";
})(GuideTagType || (GuideTagType = {}));
exports.GuideTagType = GuideTagType;
var GuideTextArgsType;
(function (GuideTextArgsType) {
    GuideTextArgsType[GuideTextArgsType["None"] = 0] = "None";
    GuideTextArgsType[GuideTextArgsType["FirstHeroName"] = 1] = "FirstHeroName";
    GuideTextArgsType[GuideTextArgsType["TreasureIronCount"] = 2] = "TreasureIronCount";
    // 士兵化身英雄
    GuideTextArgsType[GuideTextArgsType["PawnToHeroId"] = 100999] = "PawnToHeroId";
    // 英雄自选礼包
    GuideTextArgsType[GuideTextArgsType["HeroPackageId"] = 100900] = "HeroPackageId";
})(GuideTextArgsType || (GuideTextArgsType = {}));
exports.GuideTextArgsType = GuideTextArgsType;
var guideBattleEnd = {
    id: 5,
    checkFunc: {
        name: 'checkFuncPartBattle',
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.5,
            restartPoint: '0',
            tag: '0',
            taEvent: '5-1',
        },
        // 对话 恭喜你击败了强大的敌人，通过了试炼！
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_battle_010' },
                { text: 'guideText.dialog_battle_011' },
            ],
            taEvent: '5-2',
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncTaskReward'
            },
            taEvent: '5-3',
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_battle_012' },
            ],
            taEvent: '5-4',
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncSyncServerData'
            },
            taEvent: '5-5'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncOpenEndPnl'
            },
            restartPoint: GuideTagType.BATTLE_GUIDE_END,
            taEvent: '5-6'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.BATTLE_GUIDE_END,
            taEvent: '5-7',
        }
    ]
};
// 军队多选教程
// const guideMultiAttack = {
//     id: 4,
//     checkFunc: {
//         name: 'checkFuncPart4Guide',
//     },
//     isSub: false,
//     steps: [
//         {
//             type: GuideStepType.DELAY,
//             time: 0.1, //秒
//             restartPoint: '0', //存档点
//             tag: '0',
//             taEvent: '4-1', //第二段教程开始
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncCloseAllUI', //关闭所有UI
//             },
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_OPEN_MAP_CELL,
//             },
//             nodeChoose: {
//                 func: 'nodeChooseMapCellLv2',
//                 // moveCamera: true,
//                 finger: {
//                     offset: cc.v2(0, -32),
//                     angle: 0,
//                 },
//                 hide: true,
//             },
//             taEvent: '4-2', //选择领土
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_210',
//                 args: { key: 'main/SelectArmy' },
//             },
//             nodeChoose: {
//                 name: 'Wind/NoviceWind/root/cell_info/buttons/occupy_be',
//                 hide: true,
//                 finger: {
//                     offset: cc.v2(0, -26),
//                     angle: 0,
//                 },
//             },
//             taEvent: '4-3', //移动军队
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_part4_001' }
//             ],
//             mask: true
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_ARMY,
//             },
//             nodeChoose: {
//                 func: "nodeChooseArmy",      //框选军队1
//                 finger: {
//                     offset: cc.v2(325, 10),
//                     angle: 90,
//                     flip: true
//                 },
//                 desc: 'guideText.army_select_desc_002',
//                 descOffset: cc.v2(0, -350),
//             },
//             taEvent: '4-4', //框选军队1
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_ARMY,
//             },
//             nodeChoose: {
//                 func: "nodeChooseArmy",      //框选军队2
//                 args: { index: 1 },
//                 finger: {
//                     offset: cc.v2(325, 10),
//                     angle: 90,
//                     flip: true
//                 },
//                 desc: 'guideText.army_select_desc_003',
//                 descOffset: cc.v2(0, -350),
//             },
//             taEvent: '4-5', //框选军队2
//         },
//         {
//             type: GuideStepType.DELAY,
//             time: 0.26,
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_LEAVE_211',
//                 args: { key: 'main/SelectArmy' },
//             },
//             nodeChoose: {
//                 name: 'View/SelectArmyPnl/root/ok_be',
//                 hide: true,
//                 finger: {
//                     offset: cc.v2(0, -32),
//                     angle: 0,
//                 }
//             },
//             taEvent: '4-6', //派出军队
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_part4_002' }
//             ],
//             restartPoint: 'UP_MAIN_LV_READY', //存档点
//             tag: 'UP_MAIN_LV_READY',
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncHideMarchLine', //隐藏行军线
//                 args: { opacity: 20 },
//             },
//         },
//         {
//             type: GuideStepType.ON_EVENT, //款选主城
//             event: {
//                 name: EventType.GUIDE_OPEN_MAP_CELL,
//             },
//             nodeChoose: {
//                 func: 'nodeChooseMainCity',
//                 finger: {
//                     offset: cc.v2(0, 80),
//                     angle: 180,
//                 },
//                 // moveCamera: true,
//                 hide: true,
//             },
//             taEvent: '4-7', //框选主城
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'WIND_ENTER_303',
//                 args: { key: 'area' },
//             },
//             nodeChoose: {
//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
//                 hide: true,
//                 finger: {
//                     offset: cc.v2(0, 30),
//                     angle: 180,
//                 },
//                 hideChoose: true
//             },
//             taEvent: '4-8', //框选进入
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncHideMarchLine', //显示行军线
//                 args: { opacity: 100 },
//             },
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncPauseAreaBattle', //暂停区域战斗
//                 args: { index: 1277, frameCount: 1100 },
//             },
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_210',
//                 args: { key: 'build/BuildMainInfo' },
//             },
//             nodeChoose: {
//                 name: 'Wind/AreaWind/root/role_n/BUILD_2001/body',
//                 hide: true,
//                 finger: {
//                     offset: cc.v2(0, 72),
//                     angle: 180,
//                 },
//                 moveCamera: true,
//             },
//             taEvent: '4-9', //框选主城
//         },
//         {
//             type: GuideStepType.DELAY,
//             time: 0.26,
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_LEAVE_211',
//                 args: { key: 'build/BuildMainInfo' },
//             },
//             nodeChoose: {
//                 name: 'View/BuildMainInfoPnl/root/pages_n/0/bottom/buttons/up_be',
//             },
//             taEvent: '4-10', //框选升级按钮
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_part4_003' },
//                 { text: 'guideText.dialog_part4_004' },
//                 { text: 'guideText.dialog_part4_005' },
//             ],
//             restartPoint: 'UP_MAIN_LV_END', //存档点
//             tag: 'UP_MAIN_LV_END',
//         },
//         {
//             type: GuideStepType.NONE,
//             tag: GuideTagType.PART_4_GUIDE_END,
//             taEvent: '4-12', //第二段教程完成
//         },
//     ]
// }
// const guideSendRes = {
//     id: 10010,
//     checkFunc: {
//         name: 'no',
//     },
//     isSub: true,
//     steps: [
//         // 对话
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_send_res_001' },
//                 { text: 'guideText.dialog_send_res_002' }
//             ],
//             taEvent: '10010-1', //领取穷困物资
//         },
//         // 对话
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncSendResources', // 发放物资
//                 args: { ctypeStr: '1,0,200|2,0,200|3,0,200' },
//             },
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncReverse', // 返回之前的引导
//             },
//             taEvent: '10010-2', //领取穷困物资完成
//         }
//     ]
// }
// const guideSendResTip = {
//     id: 10011,
//     checkFunc: {
//         name: 'no',
//     },
//     isSub: true,
//     steps: [
//         // 对话
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_send_res_001' },
//                 { text: 'guideText.dialog_send_res_003' }
//             ],
//             taEvent: '10011-1', //穷困，但是有任务奖励可以领取
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncReverse', // 返回之前的引导
//             },
//             taEvent: '10011-2', //穷困，但是有任务奖励可以领取完成
//         }
//     ]
// }
// 装备重铸教程
// const guideGearforge = {
//     id: 6,
//     checkFunc: {
//         name: 'checkFuncGearforge',
//         keepPnl: true
//     },
//     isSub: false,
//     steps: [
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_gearforge_001' },
//                 { text: 'guideText.dialog_gearforge_002' }
//             ],
//             restartPoint: '0', //存档点
//             tag: '0',
//             taEvent: '6-1', //装备重铸引导开始
//         },
//         // 框选
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,
//             },
//             nodeChoose: {
//                 func: 'nodeChooseEquipBaseInfoBtn',      //框选装备
//                 desc: 'guideText.choose_desc_007',
//             },
//             taEvent: '6-2', //查看装备信息
//         },
//         {
//             type: GuideStepType.NONE,
//             tag: 'guideGearforgEnd',
//             taEvent: '6-3', //装备重铸引导结束
//         }
//     ]
// }
// 新手区对局引导
var guideNewbieZone = {
    id: 7,
    checkFunc: {
        name: 'checkFuncNewbieZone'
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_newbie_zone_001' },
            ],
            mask: true,
            restartPoint: '0',
            tag: '0',
            taEvent: '7-1'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncNeedToComposite',
                args: { tag: GuideTagType.NEWBIE_ZONE_GUIDE_COMPOSITE }
            },
            taEvent: '7-2'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_newbie_zone_003' },
                { text: 'guideText.dialog_newbie_zone_004' },
            ],
            taEvent: '7-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_210',
                args: { key: 'menu/Pointsets' },
            },
            nodeChoose: {
                name: 'Wind/LobbyWind/root_n/menu_right/menu_right/pointsets',
            },
            taEvent: '7-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'POINTSETS_OPEN_SHOW',
            },
            nodeChoose: {
                name: 'View/PointsetsPnl/pointsets_nbe_n/1',
                hideChoose: true
            },
            taEvent: '7-5'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'POINTSETS_CLOSE_SHOW',
            },
            nodeChoose: {
                name: 'View/PointsetsPnl',
                hide: true
            },
            taEvent: '7-6'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_newbie_zone_005' },
            ],
            taEvent: '7-7'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/PointsetsPnl/back',
            },
            taEvent: '7-8'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_210',
                args: { key: 'menu/Collection' },
            },
            nodeChoose: {
                name: 'Wind/LobbyWind/root_n/menu_right/menu_right/portrayal',
                desc: 'guideText.dialog_newbie_zone_desc_001'
            },
            tag: GuideTagType.NEWBIE_ZONE_GUIDE_COMPOSITE,
            taEvent: '7-9'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_210',
                args: { key: 'common/PortrayalInfo' }
            },
            nodeChoose: {
                name: 'View/CollectionPnl/root/pages_n/0/list/view/content/portrayal_be_clone',
            },
            taEvent: '7-10'
        },
        {
            type: GuideStepType.DELAY,
            time: 0.1
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PORTRAYAL_COMP',
            },
            nodeChoose: {
                name: 'View/PortrayalInfoPnl/comp_be_n',
            },
            taEvent: '7-11'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_newbie_zone_006', args: GuideTextArgsType.FirstHeroName },
                { text: 'guideText.dialog_newbie_zone_007', args: GuideTextArgsType.FirstHeroName },
                { text: 'guideText.dialog_newbie_zone_008' },
            ],
            restartPoint: GuideTagType.NEWBIE_ZONE_GUIDE_END,
            taEvent: '7-12'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/PortrayalInfoPnl/back',
            },
            taEvent: '7-13'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncCloseAllUI'
            },
            taEvent: '7-14'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'Wind/LobbyWind/root_n/content/bottom_n/team/layout/buttons_n/enter_game_be',
                finger: {
                    offset: cc.v2(0, -40),
                },
                hide: true,
                fullScreen: true
            },
            taEvent: '7-15'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.NEWBIE_ZONE_GUIDE_END,
            taEvent: '7-16'
        }
    ]
};
// 军队回城补血教程
var guidePawnBackMain = {
    id: 2,
    checkFunc: {
        name: 'checkFuncPawnBackMainGuide',
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.5,
            restartPoint: GuideTagType.PAWN_BACK_MAIN_GUIDE_BEGIN,
            tag: '0',
            taEvent: '2-1',
        },
        // 关闭所有界面
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncCloseAllUI',
            },
            taEvent: '2-2'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_110010' },
            ],
            tag: GuideTagType.PAWN_BACK_MAIN_GUIDE_BEGIN,
            taEvent: '2-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
            },
            nodeChoose: {
                func: 'nodeChooseMainCity',
                desc: 'guideText.choose_desc_005',
                moveCamera: true,
                finger: {
                    offset: cc.v2(0, -80),
                    angle: 0,
                },
                hide: true,
            },
            taEvent: '2-4',
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_210',
                args: { key: 'main/SelectArmy' },
            },
            nodeChoose: {
                name: 'Wind/NoviceWind/root/cell_info/buttons/move_be',
            },
            taEvent: '2-5',
        },
        {
            type: GuideStepType.DELAY,
            time: 0.26,
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_ARMY,
            },
            nodeChoose: {
                func: "nodeChooseArmy",
                args: { index: -1 },
                desc: 'guideText.army_select_desc_005',
                descOffset: cc.v2(0, -350),
            },
            taEvent: '2-6',
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_LEAVE_211',
                args: { key: 'main/SelectArmy' },
            },
            nodeChoose: {
                name: 'View/SelectArmyPnl/root/ok_be',
                hide: true,
                finger: {
                    offset: cc.v2(0, -32),
                    angle: 0,
                }
            },
            taEvent: '2-7',
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_110020' },
            ],
            tag: GuideTagType.PAWN_BACK_MAIN_GUIDE_END,
            taEvent: '2-8',
        },
    ]
};
// 铁匠铺教程
var guideSmithy = {
    id: 8,
    checkFunc: {
        name: 'checkFuncSmithy',
        keepPnl: true
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_smithy_001' }
            ],
            restartPoint: '0',
            tag: '0',
            taEvent: '8-1'
        },
        // 检查是否在铁匠铺
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkIsOpenSmithy',
                args: { tag: GuideTagType.EQUIP_STUDY_FINISH }
            },
            taEvent: '8-2'
        },
        // 检查是否进入主城
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkEnterMainCity',
                args: { tag: GuideTagType.ENTER_MAIN_CITY }
            },
            taEvent: '8-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
            },
            nodeChoose: {
                func: 'nodeChooseMainCity',
                moveCamera: true,
                finger: {
                    offset: cc.v2(0, 80),
                    angle: 180,
                },
                hide: true,
            },
            taEvent: '8-4'
        },
        // 框选进入
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'WIND_ENTER_303',
                args: { key: 'area' },
            },
            nodeChoose: {
                name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
                hideChoose: true
            },
            taEvent: '8-5'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'build/BuildSmithy' },
            },
            nodeChoose: {
                name: 'Wind/AreaWind/root/role_n/BUILD_2008/body',
            },
            tag: GuideTagType.ENTER_MAIN_CITY,
            taEvent: '8-6'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/BuildSmithyPnl/root/tabs_tc_tce/1',
            },
            taEvent: '8-7'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkEquipStudy',
                args: { slotId: 1, tag: GuideTagType.EQUIP_STUDY_FINISH }
            },
            taEvent: '8-8'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'build/StudySelect' },
            },
            nodeChoose: {
                name: 'View/BuildSmithyPnl/root/pages_n/1/equip/list/view/content/equip_be',
            },
            taEvent: '8-9'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'STUDY_SELECT_FINISH',
            },
            nodeChoose: {
                name: 'View/StudySelectPnl/root',
            },
            taEvent: '8-10'
        },
        {
            type: GuideStepType.DELAY,
            time: 0.1
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncForgeAcc'
            },
            tag: GuideTagType.EQUIP_STUDY_FINISH,
            taEvent: '8-11'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/BuildSmithyPnl/root/pages_n/1/cond/info/need/buttons/forge_be',
            },
            taEvent: '8-12'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkSmithyResResetDialogText'
            },
            restartPoint: GuideTagType.SMITHY_GUIDE_DONE,
            taEvent: '8-13'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_smithy_002' }
            ],
            taEvent: '8-14'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.SMITHY_GUIDE_DONE,
            taEvent: '8-15'
        }
    ]
};
//穿戴装备
var guideWearEquip = {
    id: 9,
    checkFunc: {
        name: 'checkFuncWearEquip'
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.5,
            restartPoint: GuideTagType.WEAR_EQUIP_GUIDE_BEGIN,
            tag: '0',
            taEvent: '9-1'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_smithy_005' },
                { text: 'guideText.dialog_smithy_006' },
            ],
            tag: GuideTagType.WEAR_EQUIP_GUIDE_BEGIN,
            taEvent: '9-2'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkWearEquipScene',
                args: { tag: GuideTagType.FIND_FREE_PAWN },
            },
            taEvent: '9-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'WIND_ENTER_303',
                args: { key: 'novice' },
            },
            nodeChoose: {
                name: 'View/UIPnl/scene_n/area/back_main_be',
            },
            taEvent: '9-4'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
            },
            nodeChoose: {
                func: 'findPawnInLand',
                moveCamera: true,
                finger: {
                    offset: cc.v2(0, 40),
                    angle: 180,
                },
                hide: true,
            },
            taEvent: '9-5'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'WIND_ENTER_303',
                args: { key: 'area' },
            },
            nodeChoose: {
                name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
                hideChoose: true
            },
            taEvent: '9-5'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'area/PawnInfo' },
            },
            nodeChoose: {
                func: 'findOnePawn'
            },
            tag: GuideTagType.FIND_FREE_PAWN,
            taEvent: '9-7'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/PawnInfoPnl/root/equip_n/info/equip_show_be',
            },
            taEvent: '9-8'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'View/PawnInfoPnl/select_equip_box_be_n/root/list/view/content/equip_item_be',
            },
            taEvent: '9-9'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_smithy_007' },
            ],
            restartPoint: GuideTagType.WEAR_EQUIP_GUIDE_END,
            taEvent: '9-10'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.WEAR_EQUIP_GUIDE_END,
            taEvent: '9-11'
        }
    ]
};
//英雄化身
// const guideWorshipHero = {
//     id: 10,
//     checkFunc: {
//         name: 'checkFuncWorshipHero',
//     },
//     isSub: false,
//     steps: [
//         {
//             type: GuideStepType.DELAY,
//             time: 0.5, //秒
//             restartPoint: GuideTagType.HERO_GUIDE_BEGIN, //存档点
//             tag: '0',
//             taEvent: '10-1'
//         },
//         // 关闭所有界面
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncCloseAllUI',
//             },
//             tag: GuideTagType.HERO_GUIDE_BEGIN, //存档点
//             taEvent: '10-2'
//         },
//         // 检查是否进入主城
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkEnterMainCity',
//                 args: { tag: GuideTagType.ENTER_MAIN_CITY }
//             },
//             taEvent: '10-3'
//         },
//         {
//             type: GuideStepType.ON_EVENT, //款选主城
//             event: {
//                 name: EventType.GUIDE_OPEN_MAP_CELL,
//             },
//             nodeChoose: {
//                 func: 'nodeChooseMainCity',
//                 moveCamera: true,
//                 finger: {
//                     offset: cc.v2(0, 80),
//                     angle: 180,
//                 },
//                 hide: true,
//             },
//             taEvent: '10-4'
//         },
//         // 框选进入
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'WIND_ENTER_303',
//                 args: { key: 'area' },
//             },
//             nodeChoose: {
//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
//                 hideChoose: true
//             },
//             taEvent: '10-5'
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_hero_001' },
//                 { text: 'guideText.dialog_hero_002' }
//             ],
//             taEvent: '10-6'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_PLAY_DONE_215',
//                 args: { key: 'build/BuildHerohall' },
//             },
//             nodeChoose: {
//                 name: 'Wind/AreaWind/root/role_n/BUILD_2015/body',
//             },
//             tag: GuideTagType.ENTER_MAIN_CITY,
//             taEvent: '10-7'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,
//             },
//             nodeChoose: {
//                 name: 'View/BuildHerohallPnl/root/tabs_tc_tce/1',
//             },
//             taEvent: '10-8'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_PLAY_DONE_215',
//                 args: { key: 'common/SelectPortrayal' },
//             },
//             nodeChoose: {
//                 name: 'View/BuildHerohallPnl/root/pages_n/1/slots_nbe/0',
//             },
//             taEvent: '10-9'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,
//             },
//             nodeChoose: {
//                 name: 'View/SelectPortrayalPnl/root_n/list/view/content/item_be',
//                 finger: {
//                     offset: cc.v2(0, -80)
//                 },
//                 hide: true,
//             },
//         },
//         {
//             type: GuideStepType.DELAY,
//             time: 0.1,
//             taEvent: '10-10'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'WORSHIP_HERO',
//             },
//             nodeChoose: {
//                 name: 'View/SelectPortrayalPnl/root_n',
//             },
//             taEvent: '10-11'
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkHeroPawn'
//             },
//             restartPoint: GuideTagType.HERO_GUIDE_END,
//             taEvent: '10-12'
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_hero_003', args: GuideTextArgsType.PawnToHeroId }
//             ],
//             restartPoint: GuideTagType.HERO_GUIDE_END,
//             taEvent: '10-13'
//         },
//         {
//             type: GuideStepType.NONE,
//             tag: GuideTagType.HERO_GUIDE_END,
//             taEvent: '10-14'
//         },
//     ]
// }
// const guidePawnToHero = {
//     id: 11,
//     checkFunc: {
//         name: 'checkFuncPawnToHero',
//     },
//     isSub: false,
//     steps: [
//         {
//             type: GuideStepType.DELAY,
//             time: 0.5, //秒
//             restartPoint: GuideTagType.HERO_GUIDE_BEGIN, //存档点
//             tag: '0',
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkFuncCloseAllUI',
//             },
//             tag: GuideTagType.HERO_GUIDE_BEGIN,
//             taEvent: '11-1'
//         },
//         {
//             type: GuideStepType.CHECK,
//             func: {
//                 name: 'checkHeroScene',
//                 args: { tag: GuideTagType.FIND_FREE_PAWN }
//             },
//             taEvent: '11-2'
//         },
//         {
//             type: GuideStepType.DELAY,
//             time: 0.1
//         },
//         {
//             type: GuideStepType.ON_EVENT, //框选返回按钮
//             event: {
//                 name: 'WIND_ENTER_303',
//                 args: { key: 'novice' },
//             },
//             nodeChoose: {
//                 name: 'View/UIPnl/scene_n/area/back_main_be',
//             },
//             taEvent: '11-3'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_OPEN_MAP_CELL,
//             },
//             nodeChoose: {
//                 func: 'findPawnInLandForHero',
//                 moveCamera: true,
//                 finger: {
//                     offset: cc.v2(0, 40),
//                     angle: 180,
//                 },
//                 hide: true,
//             },
//             taEvent: '11-4'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'WIND_ENTER_303',
//                 args: { key: 'area' },
//             },
//             nodeChoose: {
//                 name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
//                 hideChoose: true
//             },
//             taEvent: '11-5'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_PLAY_DONE_215',
//                 args: { key: 'area/PawnInfo' },
//             },
//             nodeChoose: {
//                 func: 'findOnePawnForHero'
//             },
//             tag: GuideTagType.FIND_FREE_PAWN,
//             taEvent: '11-6'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: 'PNL_ENTER_PLAY_DONE_215',
//                 args: { key: 'area/SelectAvatarHero' },
//             },
//             nodeChoose: {
//                 name: 'View/PawnInfoPnl/root/button_n/avatar_be',
//                 finger: {
//                     offset: cc.v2(0, -32),
//                     angle: 0,
//                 },
//                 hide: true,
//             },
//             taEvent: '11-7'
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,
//             },
//             nodeChoose: {
//                 name: 'View/SelectAvatarHeroPnl/root_n/content/list/item_be',
//             },
//             taEvent: '11-8'
//         },
//         {
//             type: GuideStepType.DELAY,
//             time: 0.1
//         },
//         {
//             type: GuideStepType.ON_EVENT,
//             event: {
//                 name: EventType.GUIDE_CLICK_CHOOSE_RECT,
//             },
//             nodeChoose: {
//                 name: 'View/SelectAvatarHeroPnl/root_n/buttons/ok_be',
//                 finger: {
//                     offset: cc.v2(0, -36),
//                     angle: 0,
//                 },
//                 hide: true,
//             },
//             taEvent: '11-9'
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_hero_004' }
//             ],
//             restartPoint: GuideTagType.HERO_GUIDE_END,
//             taEvent: '11-10'
//         },
//         {
//             type: GuideStepType.DIALOG,
//             content: [
//                 { text: 'guideText.dialog_hero_004' }
//             ],
//             restartPoint: GuideTagType.HERO_GUIDE_END,
//             taEvent: '11-11'
//         },
//         {
//             type: GuideStepType.NONE,
//             tag: GuideTagType.HERO_GUIDE_END,
//             taEvent: '11-12'
//         },
//     ]
// }
// 建筑加速教程
var guideBuildAcc = {
    id: 12,
    checkFunc: {
        name: 'checkFuncBuildAccTrigger',
        keepPnl: true
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_build_acc_001' },
                { text: 'guideText.dialog_build_acc_002' }
            ],
            restartPoint: '0',
            tag: '0',
            taEvent: '12-1'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkGiveGold'
            },
            taEvent: '12-2'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'common/BTQueue' },
            },
            nodeChoose: {
                name: 'View/UIPnl/right_bottom_n/bting_be_n',
                finger: {
                    offset: cc.v2(0, 36),
                    angle: 180,
                },
                hide: true,
            },
            taEvent: '12-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'NOTICE_ENTER_PLAY_DONE_403',
            },
            nodeChoose: {
                name: 'View/BTQueuePnl/root/title/in_done_n_be',
                finger: {
                    offset: cc.v2(0, 36),
                    angle: 180,
                },
                hide: true,
            },
            taEvent: '12-4'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'setSwitchGuideParent',
                args: { isView: false }
            },
            taEvent: '12-5'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
            },
            nodeChoose: {
                name: 'Notice/MessageBoxNot/root_n/buttons_nbe_n/ok',
                finger: {
                    offset: cc.v2(0, 36),
                    angle: 180,
                },
                hide: true,
            },
            taEvent: '12-6'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'setSwitchGuideParent',
                args: { isView: true }
            },
            taEvent: '12-7'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.BUILD_ACC_GUIDE_END,
            taEvent: '12-8'
        }
    ]
};
// 医馆教程
var guideHospital = {
    id: 13,
    checkFunc: {
        name: 'checkFuncHospitalTrigger',
        keepPnl: true
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.1,
            restartPoint: GuideTagType.HOSPITAL_GUIDE_BEGIN,
            tag: '0',
            taEvent: '13-1'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_hospital_001' },
                { text: 'guideText.dialog_hospital_002' },
                { text: 'guideText.dialog_hospital_003' },
            ],
            restartPoint: GuideTagType.HOSPITAL_GUIDE_END,
            taEvent: '13-2'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncShowAttackTips',
                args: [{ tips: 'guideText.dialog_notice_guide_003', icon1: 1, icon2: 1, iconName2: 'buildText.name_2016' }],
            },
            taEvent: '13-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_LEAVE_211',
                args: { key: 'help/NoticeGuide' },
            },
            nodeChoose: {
                name: 'View/NoticeGuidePnl/root',
                hide: true
            },
            taEvent: '13-4'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.HOSPITAL_GUIDE_END,
            taEvent: '13-5'
        }
    ]
};
// 石头地教程
var guideLandStone = {
    id: 14,
    checkFunc: {
        name: 'checkFuncLandStoneTrigger',
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.1,
            restartPoint: GuideTagType.LAND_STONE_GUIDE_BEGIN,
            tag: '0',
            taEvent: '14-1'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_land_stone_001' }
            ],
            tag: GuideTagType.LAND_STONE_GUIDE_BEGIN,
            taEvent: '14-2'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncLandStoneWind',
                args: { tag: GuideTagType.LAND_STONE_GUIDE_WIND }
            },
            taEvent: '14-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'area/AreaArmy' },
            },
            nodeChoose: {
                name: 'View/UIPnl/scene_n/area/bottom/area_army_be_n',
                desc: 'guideText.dialog_land_stone_desc_001',
            },
            taEvent: '14-4'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'common/TreasureList' },
            },
            nodeChoose: {
                func: 'nodeChooseArmyTreasure',
                args: { key: 'area/AreaArmy' }
            },
            taEvent: '14-5'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncLandStoneToTreasure',
                args: { tag: GuideTagType.LAND_STONE_GUIDE_TREASURE }
            },
            taEvent: '14-6'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'main/ArmyList' },
            },
            nodeChoose: {
                name: 'View/UIPnl/scene_n/main/bottom/all_army_be_n',
                desc: 'guideText.dialog_land_stone_desc_001',
            },
            tag: GuideTagType.LAND_STONE_GUIDE_WIND,
            taEvent: '14-7'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_ENTER_PLAY_DONE_215',
                args: { key: 'common/TreasureList' },
            },
            nodeChoose: {
                func: 'nodeChooseArmyTreasure',
                args: { key: 'main/ArmyList' }
            },
            taEvent: '14-8'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT
            },
            nodeChoose: {
                func: 'nodeChooseArmyTreasureOpen'
            },
            tag: GuideTagType.LAND_STONE_GUIDE_TREASURE,
            taEvent: '14-9'
        },
        {
            type: GuideStepType.DELAY,
            time: 0.1,
            restartPoint: GuideTagType.LAND_STONE_GUIDE_END,
            taEvent: '14-10'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_land_stone_002', args: GuideTextArgsType.TreasureIronCount }
            ],
            restartPoint: GuideTagType.LAND_STONE_GUIDE_END,
            taEvent: '14-11'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.LAND_STONE_GUIDE_END,
            taEvent: '14-12'
        }
    ]
};
var guideBattleBegin = {
    id: 15,
    checkFunc: {
        name: 'checkFuncBattleBeginTrigger'
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.1,
            restartPoint: '0',
            tag: '0',
            taEvent: '15-1'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_enemy_battle_001' },
                { text: 'guideText.dialog_enemy_battle_002' },
            ],
            taEvent: '15-2'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncShowAttackTips',
                args: [{ tips: 'guideText.dialog_notice_guide_001', desc: 'guideText.dialog_notice_guide_002', icon1: 0, icon2: 0 }],
            },
            taEvent: '15-3'
        },
        {
            type: GuideStepType.ON_EVENT,
            event: {
                name: 'PNL_LEAVE_211',
                args: { key: 'help/NoticeGuide' },
            },
            nodeChoose: {
                name: 'View/NoticeGuidePnl/root',
                hide: true
            },
            taEvent: '15-4'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.BATTLE_BEGIN_GUIDE_END,
            taEvent: '15-5'
        }
    ]
};
var guideFirstBattleEnd = {
    id: 16,
    checkFunc: {
        name: 'checkFuncFirstBattleEndTrigger'
    },
    isSub: false,
    steps: [
        {
            type: GuideStepType.DELAY,
            time: 0.1,
            restartPoint: '0',
            tag: '0',
            taEvent: '16-1'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_enemy_battle_003' },
                { text: 'guideText.dialog_enemy_battle_004' },
                { text: 'guideText.dialog_enemy_battle_005' },
            ],
            taEvent: '16-2'
        },
        {
            type: GuideStepType.CHECK,
            func: {
                name: 'checkFuncUpdateSmithyLv',
                args: { lv: 10, iron: 12 },
            },
            restartPoint: GuideTagType.ENEMY_FIRST_BATTLE_GUIDE,
            taEvent: '16-3'
        },
        {
            type: GuideStepType.DIALOG,
            content: [
                { text: 'guideText.dialog_enemy_battle_006' },
            ],
            taEvent: '16-4'
        },
        {
            type: GuideStepType.NONE,
            tag: GuideTagType.ENEMY_FIRST_BATTLE_GUIDE,
            taEvent: '16-5'
        }
    ]
};
// 配置
var GUIDE_CONFIG = {
    datas: [
        {
            id: 1,
            checkFunc: {
                name: 'checkFuncOneGuide',
            },
            isSub: false,
            steps: [
                {
                    type: GuideStepType.DELAY,
                    time: 0.2,
                    restartPoint: GuideTagType.FIRST_GUIDE_BEGIN,
                    tag: '0',
                    taEvent: '1-1',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100010' },
                        { text: 'guideText.dialog_100011' },
                    ],
                    tag: GuideTagType.FIRST_GUIDE_BEGIN,
                    taEvent: '1-2',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CKICK_BUILD_MAIN_CITY,
                    },
                    nodeChoose: {
                        func: 'nodeChooseMainCity',
                        finger: {
                            offset: cc.v2(0, 80),
                            angle: 180,
                        }
                    },
                    taEvent: '1-3',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncRestoreMainCity',
                    },
                    restartPoint: GuideTagType.RESTORE_MAIN_CITY,
                    taEvent: '1-4',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 4,
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100012' },
                    ],
                    tag: GuideTagType.RESTORE_MAIN_CITY,
                    restartPoint: GuideTagType.RESTORE_MAIN_CITY,
                    taEvent: '1-5',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
                    },
                    nodeChoose: {
                        func: 'nodeChooseMapCell',
                        desc: 'guideText.choose_desc_001',
                        moveCamera: true,
                        finger: {
                            offset: cc.v2(0, -40),
                            angle: 0,
                        }
                    },
                    taEvent: '1-6',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_210',
                        args: { key: 'main/SelectArmy' },
                    },
                    nodeChoose: {
                        name: 'Wind/NoviceWind/root/cell_info/buttons/occupy_be',
                    },
                    tag: GuideTagType.FIRST_BATTLE_MOVE,
                    taEvent: '1-7',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.26,
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_ARMY,
                    },
                    nodeChoose: {
                        func: "nodeChooseArmy",
                        finger: {
                            offset: cc.v2(325, 10),
                            angle: 90,
                            flip: true
                        }
                    },
                    taEvent: '1-8',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_LEAVE_211',
                        args: { key: 'main/SelectArmy' },
                    },
                    nodeChoose: {
                        name: 'View/SelectArmyPnl/root/ok_be',
                    },
                    taEvent: '1-9',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100020', desc: 'guideText.dialog_desc_001' },
                    ],
                    restartPoint: GuideTagType.FIRST_TRIGGER_BATTLE,
                    taEvent: '1-10',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncTriggerFirstBattle',
                    },
                    tag: GuideTagType.FIRST_TRIGGER_BATTLE,
                    taEvent: '1-11',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100030' },
                    ],
                    taEvent: '1-12',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
                    },
                    nodeChoose: {
                        func: 'nodeChooseMapCell',
                        finger: {
                            offset: cc.v2(0, -40),
                            angle: 0,
                        }
                    },
                    taEvent: '1-13',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'WIND_ENTER_303',
                        args: { key: 'area' },
                    },
                    nodeChoose: {
                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
                        hideChoose: true
                    },
                    taEvent: '1-14',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.5,
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100040', desc: 'guideText.dialog_desc_002' },
                    ],
                    taEvent: '1-15',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncBeginFirstBattle',
                    },
                    restartPoint: GuideTagType.CHECK_FIRST_BATTLE_END,
                    taEvent: '1-16',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_LEAVE_211',
                        args: { key: 'area/BattleEnd' },
                    },
                    tag: GuideTagType.CHECK_FIRST_BATTLE_END,
                    taEvent: '1-17',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.2,
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100050' },
                        { text: 'guideText.dialog_100051' },
                    ],
                    taEvent: '1-18',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_PLAY_DONE_215',
                        args: { key: 'area/AreaArmy' },
                    },
                    nodeChoose: {
                        name: 'View/UIPnl/scene_n/area/bottom/area_army_be_n',
                    },
                    taEvent: '1-19',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_PLAY_DONE_215',
                        args: { key: 'common/TreasureList' },
                    },
                    nodeChoose: {
                        func: 'nodeChooseTreasureText',
                        finger: {
                            offset: cc.v2(0, 24),
                            angle: 180,
                        }
                    },
                    taEvent: '1-20',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncTreasureIsOpen',
                    },
                    taEvent: '1-21',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_OPEN_TREASURE,
                    },
                    nodeChoose: {
                        name: 'View/TreasureListPnl/root_n/list_sv/view/content/item/open_be',
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -28),
                            angle: 0,
                        }
                    },
                    taEvent: '1-22',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLAIM_TREASURE,
                    },
                    nodeChoose: {
                        name: 'View/TreasureListPnl/root_n/list_sv/view/content/item/claim_be',
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -28),
                            angle: 0,
                        }
                    },
                    tag: GuideTagType.CLAIM_TREASURE,
                    taEvent: '1-23',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncCloseTreasureAndArmyPnl',
                    },
                    tag: GuideTagType.CLAIM_TREASURE_COMPLETE,
                    restartPoint: GuideTagType.READY_BT_BARRACKS,
                    taEvent: '1-24',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100060' },
                    ],
                    tag: GuideTagType.READY_BT_BARRACKS,
                    taEvent: '1-25',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncInArea',
                    },
                    taEvent: '1-26',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'WIND_ENTER_303',
                        args: { key: 'novice' },
                    },
                    nodeChoose: {
                        name: 'View/UIPnl/scene_n/area/back_main_be',
                    },
                    taEvent: '1-27',
                },
                {
                    type: GuideStepType.CHECK_WAIT,
                    func: {
                        name: 'checkFuncCanBTBarracks',
                        args: Constant_1.BUILD_BARRACKS_NID,
                    },
                    tag: GuideTagType.CHECK_CAN_BT_BARRACKS,
                    taEvent: '1-28',
                },
                // 框选主城
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
                    },
                    nodeChoose: {
                        func: 'nodeChooseMainCity',
                        finger: {
                            offset: cc.v2(0, 80),
                            angle: 180,
                        }
                    },
                    tag: GuideTagType.CHECK_CAN_BT_BARRACKS,
                    taEvent: '1-29' //款选主城
                },
                // 框选进入
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'WIND_ENTER_303',
                        args: { key: 'area' },
                    },
                    nodeChoose: {
                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
                        hideChoose: true
                    },
                    taEvent: '1-30' //框选进入
                }, {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100070' },
                    ],
                    taEvent: '1-31'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_210',
                        args: { key: 'area/BuildList' },
                    },
                    nodeChoose: {
                        name: 'View/UIPnl/scene_n/area/bottom/build_be_n',
                    },
                    taEvent: '1-32',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.26,
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_LEAVE_211',
                        args: { key: 'area/BuildList' },
                    },
                    nodeChoose: {
                        name: "View/BuildListPnl/root/list_sv/view/content/item_" + Constant_1.BUILD_BARRACKS_NID + "/button/unlock_be",
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -28),
                            angle: 0,
                        }
                    },
                    taEvent: '1-33',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncCanXlPawn',
                    },
                    tag: GuideTagType.CHECK_CAN_XL_PAWN,
                    restartPoint: GuideTagType.CHECK_CAN_XL_PAWN,
                    taEvent: '1-34'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_OPEN_MAP_CELL,
                    },
                    nodeChoose: {
                        func: 'nodeChooseMainCity',
                        finger: {
                            offset: cc.v2(0, 80),
                            angle: 180,
                        }
                    },
                    taEvent: '1-35' //款选主城
                },
                // 框选进入
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'WIND_ENTER_303',
                        args: { key: 'area' },
                    },
                    nodeChoose: {
                        name: 'Wind/NoviceWind/root/cell_info/buttons/enter_be',
                        hideChoose: true
                    },
                    taEvent: '1-36' //框选进入
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.5,
                    tag: 'build_complete',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100080' },
                    ],
                    taEvent: '1-37',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_210',
                        args: { key: 'build/BuildBarracks' },
                    },
                    nodeChoose: {
                        name: 'Wind/AreaWind/root/role_n/BUILD_2004/body',
                        desc: 'guideText.choose_desc_004',
                        moveCamera: true,
                        hide: true,
                        finger: {
                            offset: cc.v2(0, 0),
                            angle: 0,
                        }
                    },
                    taEvent: '1-38',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.26,
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
                    },
                    nodeChoose: {
                        name: 'View/BuildBarracksPnl/root/tabs_tc_tce/1',
                    },
                    taEvent: '1-39',
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100015' },
                    ],
                    taEvent: '1-40'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_210',
                        args: { key: 'common/CreateArmy' },
                    },
                    nodeChoose: {
                        func: 'nodeChooseCreateArmy',
                        desc: 'guideText.choose_desc_009',
                        finger: {
                            offset: cc.v2(0, -40),
                        },
                    },
                    taEvent: '1-41',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.26,
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_LEAVE_211',
                        args: { key: 'common/CreateArmy' },
                    },
                    nodeChoose: {
                        name: 'View/CreateArmyPnl/root',
                    },
                    taEvent: '1-42',
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkGotoPawnDrill'
                    },
                    taEvent: '1-43'
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100016' },
                    ],
                    taEvent: '1-44'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: 'PNL_ENTER_PLAY_DONE_215',
                        args: { key: 'build/StudySelect' },
                    },
                    nodeChoose: {
                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/pawn/list/view/content/pawn_be',
                        desc: 'guideText.choose_desc_008'
                    },
                    taEvent: '1-45'
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100017' },
                    ],
                    taEvent: '1-46'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
                    },
                    nodeChoose: {
                        func: 'nodeChooseTargetPawn',
                    },
                    taEvent: '1-47'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
                    },
                    nodeChoose: {
                        name: 'View/StudySelectPnl/root/buttons/study_be',
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -36),
                        },
                    },
                    taEvent: '1-48'
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.1
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100018' },
                    ],
                    taEvent: '1-49'
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncDrillAcc',
                    },
                    tag: GuideTagType.FIRST_CREATE_ARMY,
                    taEvent: '1-50'
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncChoosePawn',
                    },
                    taEvent: '1-51'
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
                    },
                    nodeChoose: {
                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/pawn/list/view/content/pawn_be',
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -50),
                        },
                    },
                    taEvent: '1-52',
                },
                {
                    type: GuideStepType.ON_EVENT,
                    event: {
                        name: EventType_1.default.GUIDE_CLICK_CHOOSE_RECT,
                    },
                    nodeChoose: {
                        name: 'View/BuildBarracksPnl/root/pages_n/1/info/cond/need/buttons/drill_be',
                        hide: true,
                        finger: {
                            offset: cc.v2(0, -36),
                        },
                    },
                    taEvent: '1-53',
                },
                {
                    type: GuideStepType.DELAY,
                    time: 0.01,
                    restartPoint: GuideTagType.FIRST_GUIDE_DONE,
                },
                {
                    type: GuideStepType.CHECK,
                    func: {
                        name: 'checkFuncCloseBarracks',
                    },
                    taEvent: '1-54'
                },
                {
                    type: GuideStepType.DIALOG,
                    content: [
                        { text: 'guideText.dialog_100090' },
                        { text: 'guideText.dialog_100091' },
                    ],
                    taEvent: '1-55'
                },
                {
                    type: GuideStepType.NONE,
                    tag: GuideTagType.FIRST_GUIDE_DONE,
                    taEvent: '1-56',
                }
            ]
        },
        guideHospital,
        // guideMultiAttack,
        guidePawnBackMain,
        // guideSendRes,
        // guideSendResTip,
        guideSmithy,
        guideWearEquip,
        // guideWorshipHero,
        // guidePawnToHero,
        guideBuildAcc,
        guideLandStone,
        guideBattleBegin,
        guideFirstBattleEnd,
        guideBattleEnd,
        guideNewbieZone,
    ]
};
exports.GUIDE_CONFIG = GUIDE_CONFIG;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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