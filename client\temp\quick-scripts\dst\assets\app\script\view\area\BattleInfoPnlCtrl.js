
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/BattleInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd2ee4LpOuVBcovBQTI+Pbq7', 'BattleInfoPnlCtrl');
// app/script/view/area/BattleInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BattleInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(BattleInfoPnlCtrl, _super);
    function BattleInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    BattleInfoPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BattleInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BattleInfoPnlCtrl.prototype.onEnter = function (data) {
        var owner = data.owner, attacker = 0, defender = 0;
        // 计算各个军队数量
        data.armys.forEach(function (m) {
            if (m.getPawnActCount() === 0) {
                return;
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defender += 1;
            }
            else {
                attacker += 1;
            }
        });
        // 从这个区域开始行军的军队数量
        var index = data.index;
        GameHelper_1.gameHpr.world.getMarchs().filter(function (m) { return m.armyIndex === index; }).forEach(function (m) {
            if (m.autoRevoke) {
            }
            else if (GameHelper_1.gameHpr.checkIsOneAlliance(m.owner, owner)) {
                defender += 1;
            }
            else {
                attacker += 1;
            }
        });
        var fsp = data.getFspModel();
        this.rootNode_.Child('0/val').setLocaleKey('ui.area_army_count_0', defender + '/' + data.maxArmyCount);
        this.rootNode_.Child('1/val').setLocaleKey('ui.area_army_count_1', attacker + '/' + data.maxArmyCount);
        this.rootNode_.Child('2/val').setLocaleKey('ui.area_add_pawn_times_0', ((fsp === null || fsp === void 0 ? void 0 : fsp.getDefenderArmyAcc()) || 0) + '/' + data.maxAddPawnTimes);
        this.rootNode_.Child('3/val').setLocaleKey('ui.area_add_pawn_times_1', ((fsp === null || fsp === void 0 ? void 0 : fsp.getAttackerArmyAcc()) || 0) + '/' + data.maxAddPawnTimes);
    };
    BattleInfoPnlCtrl.prototype.onRemove = function () {
    };
    BattleInfoPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/desc/battle_rule_1_be
    BattleInfoPnlCtrl.prototype.onClickBattleRule1 = function (event, data) {
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.battle_rule_desc_1_1' },
            { key: 'ui.battle_rule_desc_1_2' },
            { key: 'ui.battle_rule_desc_1_3' },
        ], 'ui.button_battle_rule_1');
    };
    // path://root_n/desc/battle_rule_2_be
    BattleInfoPnlCtrl.prototype.onClickBattleRule2 = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('area/BattleRule');
    };
    BattleInfoPnlCtrl = __decorate([
        ccclass
    ], BattleInfoPnlCtrl);
    return BattleInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BattleInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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