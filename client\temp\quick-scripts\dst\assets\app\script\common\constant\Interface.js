
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/Interface.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c266fVr2QdJ3Ya5s2BRlTGx', 'Interface');
// app/script/common/constant/Interface.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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