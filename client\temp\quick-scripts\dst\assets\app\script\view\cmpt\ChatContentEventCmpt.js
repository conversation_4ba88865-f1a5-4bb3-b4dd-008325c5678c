
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ChatContentEventCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '327f05BjNZAjarSKfaNzDSK', 'ChatContentEventCmpt');
// app/script/view/cmpt/ChatContentEventCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChatContentEventCmpt = /** @class */ (function (_super) {
    __extends(ChatContentEventCmpt, _super);
    function ChatContentEventCmpt() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ChatContentEventCmpt.prototype.onClickItem = function (event, _) {
        var data = event.target.parent.Data;
        if (!data || !data.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (data.type === 'equip') {
            ViewHelper_1.viewHelper.showPnl('common/EquipInfoBox', data.data);
        }
        else if (data.type === 'portrayal') {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', data.data, 'chat');
        }
        else if (data.type == 'battleInfo') {
            this.playbackBattle(data.data.uid);
        }
    };
    ChatContentEventCmpt.prototype.onClickPoint = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.hidePnl('common/Chat');
        GameHelper_1.gameHpr.gotoTargetPosByPoint(ut.stringToVec2(data), true);
    };
    // 回放战斗
    ChatContentEventCmpt.prototype.playbackBattle = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ViewHelper_1.viewHelper.showWindLoading(true);
                        return [4 /*yield*/, GameHelper_1.gameHpr.playback.setRecordById(uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            ViewHelper_1.viewHelper.showWindLoading(false);
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.preloadWind('playback')];
                    case 2:
                        _a.sent();
                        ViewHelper_1.viewHelper.showWindLoading(false);
                        ViewHelper_1.viewHelper.gotoWind('playback');
                        return [2 /*return*/];
                }
            });
        });
    };
    ChatContentEventCmpt = __decorate([
        ccclass
    ], ChatContentEventCmpt);
    return ChatContentEventCmpt;
}(cc.Component));
exports.default = ChatContentEventCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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