
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/ArmyObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '82a01xgsE1JY4cV4syXCG5d', 'ArmyObj');
// app/script/model/area/ArmyObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PawnObj_1 = require("./PawnObj");
// 战场里面的军队
var ArmyObj = /** @class */ (function () {
    function ArmyObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1; //进入方向
        this.uid = '';
        this.name = '';
        this.owner = ''; //拥有者
        this.pawns = []; //士兵列表
        this.drillPawns = []; //训练中的士兵列表
        this.marchSpeed = 0; //行军速度
        this.defaultMarchSpeed = 0; //默认行军速度
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns = []; // 治疗中的士兵
    }
    ArmyObj.prototype.init = function (index, owner, name) {
        this.aIndex = index;
        this.uid = 'temp_' + ut.UID();
        this.owner = owner;
        this.name = name;
        this.pawns.length = 0;
        this.drillPawns.length = 0;
        this.state = Enums_1.ArmyState.NONE;
        this.curingPawns.length = 0;
        return this;
    };
    ArmyObj.prototype.fromSvr = function (data) {
        var _a, _b, _c;
        this.aIndex = data.index;
        this.enterDir = (_a = data.enterDir) !== null && _a !== void 0 ? _a : -1;
        this.uid = data.uid || '';
        this.name = data.name || '';
        this.owner = data.owner || '';
        this.updatePawns(data.pawns || []);
        this.drillPawns = data.drillPawns || [];
        this.state = data.state || Enums_1.ArmyState.NONE;
        this.defaultMarchSpeed = (_c = (_b = this.getPawnByMinMarchSpeed()) === null || _b === void 0 ? void 0 : _b.marchSpeed) !== null && _c !== void 0 ? _c : 0;
        this.marchSpeed = data.marchSpeed || this.defaultMarchSpeed;
        this.curingPawns = data.curingPawns || [];
        return this;
    };
    ArmyObj.prototype.strip = function () {
        return {
            index: this.aIndex,
            uid: this.uid,
            name: this.name,
            owner: this.owner,
            pawns: this.pawns.map(function (p) { return p.strip(); }),
            drillPawns: this.drillPawns,
            state: this.state,
            enterDir: this.enterDir,
            marchSpeed: this.marchSpeed,
            curingPawns: this.curingPawns,
        };
    };
    ArmyObj.prototype.toPawnsByHP = function () {
        return {
            pawns: this.pawns.map(function (p) {
                return { uid: p.uid, curHp: Math.min(p.curHp, p.maxHp), maxHp: p.maxHp };
            })
        };
    };
    Object.defineProperty(ArmyObj.prototype, "index", {
        get: function () { return this.aIndex; },
        set: function (val) { this.aIndex = val; },
        enumerable: false,
        configurable: true
    });
    // 是否自己的军队
    ArmyObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否行军中
    ArmyObj.prototype.isMarching = function () {
        return this.state === Enums_1.ArmyState.MARCH;
    };
    // 是否战斗中
    ArmyObj.prototype.isBattleing = function () {
        return this.state === Enums_1.ArmyState.FIGHT;
    };
    // 是否在治疗中
    ArmyObj.prototype.isCuring = function () {
        return this.state === Enums_1.ArmyState.CURING;
    };
    // 是否有英雄
    ArmyObj.prototype.isHasHero = function () {
        return this.pawns.some(function (m) { return m.isHero(); });
    };
    ArmyObj.prototype.updatePawns = function (pawns) {
        var _this = this;
        // 先删除没有的
        var uidMap = {};
        pawns.forEach(function (m) { return uidMap[m.uid] = true; });
        for (var i = this.pawns.length - 1; i >= 0; i--) {
            if (!uidMap[this.pawns[i].uid]) {
                this.pawns.splice(i, 1);
            }
        }
        // 一个个添加
        pawns.forEach(function (m) { return _this.addPawn(m); });
    };
    // 删除士兵
    ArmyObj.prototype.removePawn = function (uid) {
        this.pawns.remove('uid', uid);
    };
    // 添加士兵
    ArmyObj.prototype.addPawn = function (data) {
        var _a;
        var pawn = this.pawns.find(function (m) { return m.uid === data.uid; });
        if (pawn) {
            pawn.fromSvr(data, this.uid, this.owner, this.name);
        }
        else if (data.uid === ((_a = GameHelper_1.gameHpr.uiShowPawnData) === null || _a === void 0 ? void 0 : _a.uid)) {
            pawn = this.pawns.add(GameHelper_1.gameHpr.uiShowPawnData.fromSvr(data, this.uid, this.owner, this.name));
        }
        else {
            pawn = this.pawns.add(new PawnObj_1.default().fromSvr(data, this.uid, this.owner, this.name));
        }
        pawn.enterDir = this.enterDir;
        return pawn;
    };
    // 获取实际士兵个数
    ArmyObj.prototype.getActPawnCount = function () {
        return this.pawns.length + this.drillPawns.length + this.curingPawns.length;
    };
    // 获取士兵的实际数量
    ArmyObj.prototype.getPawnActCount = function () {
        return this.pawns.filter(function (m) { return !m.isDie(); }).length;
    };
    // 是否可以训练士兵
    ArmyObj.prototype.isCanDrillPawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 是否可以治疗士兵
    ArmyObj.prototype.isCanCurePawn = function () {
        return this.isOwner() && this.state !== Enums_1.ArmyState.MARCH;
    };
    // 获取所有士兵宝箱数量
    ArmyObj.prototype.getAllPawnTreasureCount = function () {
        return this.pawns.reduce(function (val, cur) { return cur.treasures.length + val; }, 0);
    };
    // 获取所有士兵宝箱列表
    ArmyObj.prototype.getAllPawnTreasures = function () {
        var arr = [];
        this.pawns.forEach(function (m) { return arr.pushArr(m.treasures); });
        return arr;
    };
    Object.defineProperty(ArmyObj.prototype, "treasures", {
        get: function () { return this.getAllPawnTreasures(); },
        enumerable: false,
        configurable: true
    });
    // 设置士兵位置
    ArmyObj.prototype.setPawnsPoint = function (pawns) {
        if (!pawns || pawns.length === 0) {
            return;
        }
        var obj = {};
        pawns.forEach(function (m) { return obj[m.uid] = m.point; });
        this.pawns.forEach(function (m) {
            var point = obj[m.uid];
            if (point) {
                m.setPoint(point);
            }
        });
    };
    /////////////////////////////////////////////////////////// 新手村相关 ///////////////////////////////////////////////////////////////////
    // 获取行军速度最少士兵
    ArmyObj.prototype.getPawnByMinMarchSpeed = function () {
        var speed = -1, pawn = null;
        this.pawns.forEach(function (m) {
            if (speed === -1 || m.marchSpeed < speed || (m.marchSpeed == speed && m.skinId > 0)) {
                speed = m.marchSpeed;
                pawn = m;
            }
        });
        return pawn || this.pawns[0];
    };
    // 回复血量
    ArmyObj.prototype.recoverAllPawn = function () {
        this.pawns.forEach(function (m) { return m.curHp = m.maxHp; });
    };
    // 设置士兵位置
    ArmyObj.prototype.setPawnPoints = function (points) {
        var len = points === null || points === void 0 ? void 0 : points.length;
        if (!len) {
            return;
        }
        for (var i = 0, l = this.pawns.length; i < l; i++) {
            if (i < len) {
                this.pawns[i].setPoint(points[i]);
            }
            else {
                this.pawns[i].setPoint(points[0]);
            }
        }
    };
    // 是否可以战斗
    ArmyObj.prototype.isCanBattle = function () {
        return this.state === Enums_1.ArmyState.NONE && this.drillPawns.length === 0 && this.pawns.length > 0 && !this.pawns.some(function (m) { return GameHelper_1.gameHpr.noviceServer.checkPawnLving(m.uid); });
    };
    ArmyObj.prototype.updatePawnEquipAttr = function (equip) {
        this.pawns.forEach(function (m) { return m.updateEquipAttr(equip.id, equip.attrs); });
    };
    return ArmyObj;
}());
exports.default = ArmyObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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