{"version": 3, "sources": ["assets\\app\\script\\common\\constant\\Constant.ts"], "names": [], "mappings": ";;;;;AAAA,iDAAiD;;;;;;;AAEjD,iCAAuJ;AAEvJ,OAAO;AACP,IAAM,WAAW,GAAG,EAAE,CAAA;AAk8BlB,kCAAW;AAj8Bf,QAAQ;AACR,IAAM,SAAS,GAAG,EAAE,CAAA;AAk8BhB,8BAAS;AAj8Bb,WAAW;AACX,IAAM,cAAc,GAAG,CAAC,CAAA;AAk8BpB,wCAAc;AAh8BlB,SAAS;AACT,IAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAA;AA47BvD,0CAAe;AA17BnB,EAAE;AACF,IAAM,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,GAAG,CAAC,CAAA;AA27B1D,wCAAc;AAz7BlB,WAAW;AACX,IAAM,kBAAkB,GAAG,cAAc,CAAC,CAAC,GAAG,EAAE,CAAA;AA07B5C,gDAAkB;AAx7BtB,UAAU;AACV,IAAM,eAAe,GAAG,EAAE,GAAG,SAAS,CAAA;AAw7BlC,0CAAe;AAv7BnB,QAAQ;AACR,IAAM,UAAU,GAAG,KAAK,CAAA;AAu7BpB,gCAAU;AAr7Bd,OAAO;AACP,IAAM,eAAe,GAAG,GAAG,CAAA;AAq7BvB,0CAAe;AAn7BnB,YAAY;AACZ,IAAM,oBAAoB,GAAG,GAAG,CAAA;AAm7B5B,oDAAoB;AAj7BxB,OAAO;AACP,IAAM,aAAa,GAAG,IAAI,CAAA;AAi7BtB,sCAAa;AAh7BjB,OAAO;AACP,IAAM,aAAa,GAAG,IAAI,CAAA;AAi7BtB,sCAAa;AAh7BjB,OAAO;AACP,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAg7B7B,0CAAe;AA/6BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAg7B7B,0CAAe;AA/6BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAg7B7B,0CAAe;AA/6BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAg7B7B,0CAAe;AA/6BnB,IAAM,eAAe,GAAG,IAAI,CAAA,CAAC,IAAI;AAg7B7B,0CAAe;AA/6BnB,KAAK;AACL,IAAM,aAAa,GAAG,IAAI,CAAA;AA+6BtB,sCAAa;AA96BjB,MAAM;AACN,IAAM,eAAe,GAAG,IAAI,CAAA;AA86BxB,0CAAe;AA76BnB,MAAM;AACN,IAAM,eAAe,GAAG,IAAI,CAAA;AA66BxB,0CAAe;AA56BnB,SAAS;AACT,IAAM,cAAc,GAAG,IAAI,CAAA;AA46BvB,wCAAc;AA36BlB,OAAO;AACP,IAAM,cAAc,GAAG,IAAI,CAAA;AA26BvB,wCAAc;AA16BlB,SAAS;AACT,IAAM,iBAAiB,GAAG,IAAI,CAAA;AA06B1B,8CAAiB;AAz6BrB,SAAS;AACT,IAAM,mBAAmB,GAAG,IAAI,CAAA;AAy6B5B,kDAAmB;AAx6BvB,SAAS;AACT,IAAM,kBAAkB,GAAG,IAAI,CAAA;AAw6B3B,gDAAkB;AAv6BtB,UAAU;AACV,IAAM,iBAAiB,GAAG,IAAI,CAAA;AAu6B1B,8CAAiB;AAt6BrB,SAAS;AACT,IAAM,gBAAgB,GAAG,IAAI,CAAA;AAs6BzB,4CAAgB;AAr6BpB,WAAW;AACX,IAAM,gBAAgB,GAAG,IAAI,CAAA;AAq6BzB,4CAAgB;AAp6BpB,SAAS;AACT,IAAM,qBAAqB,GAAG,IAAI,CAAA;AAo6B9B,sDAAqB;AAn6BzB,SAAS;AACT,IAAM,eAAe,GAAG,IAAI,CAAA;AAm6BxB,0CAAe;AAl6BnB,WAAW;AACX,IAAM,qBAAqB,GAAG,IAAI,CAAA;AAk6B9B,sDAAqB;AAj6BzB,UAAU;AACV,IAAM,kBAAkB,GAAG,IAAI,CAAA;AAi6B3B,gDAAkB;AAh6BtB,SAAS;AACT,IAAM,kBAAkB,GAAG,IAAI,CAAA;AAg6B3B,gDAAkB;AA/5BtB,OAAO;AACP,IAAM,cAAc,GAAG,IAAI,CAAA;AAw4BvB,wCAAc;AAv4BlB,SAAS;AACT,IAAM,cAAc,GAAG,IAAI,CAAA;AA65BvB,wCAAc;AA55BlB,SAAS;AACT,IAAM,eAAe,GAAG,IAAI,CAAA;AA45BxB,0CAAe;AA15BnB,QAAQ;AACR,IAAM,gBAAgB,GAAG,IAAI,CAAA;AA05BzB,4CAAgB;AAz5BpB,QAAQ;AACR,IAAM,aAAa,GAAG,IAAI,CAAA;AAy5BtB,sCAAa;AAv5BjB,SAAS;AACT,IAAM,eAAe,GAAG,GAAG,CAAA;AAy5BvB,0CAAe;AAx5BnB,OAAO;AACP,IAAM,YAAY,GAAG,IAAI,CAAA;AAq5BrB,oCAAY;AAp5BhB,OAAO;AACP,IAAM,cAAc,GAAG,GAAG,CAAA;AAo5BtB,wCAAc;AAn5BlB,SAAS;AACT,IAAM,sBAAsB,GAAG,CAAC,CAAA;AAo5B5B,wDAAsB;AAl5B1B,eAAe;AACf,IAAM,eAAe,GAAG,EAAE,CAAA;AAk5BtB,0CAAe;AAj5BnB,eAAe;AACf,IAAM,kBAAkB,GAAG,EAAE,CAAA;AAi5BzB,gDAAkB;AAh5BtB,aAAa;AACb,IAAM,oBAAoB,GAAG,GAAG,CAAA;AAg5B5B,oDAAoB;AA94BxB,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,CAAA;AA84BzB,kDAAmB;AA74BvB,eAAe;AACf,IAAM,kBAAkB,GAAG,CAAC,CAAA;AA64BxB,gDAAkB;AA34BtB,GAAG;AACH,IAAM,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,WAAW;AA24B7C,8CAAiB;AA14BrB,IAAM,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA,CAAC,SAAS;AA24B7C,8CAAiB;AA14BrB,IAAM,kBAAkB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,WAAW;AA24B9C,gDAAkB;AA14BtB,IAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,MAAM;AA24BtC,0CAAe;AA14BnB,IAAM,sBAAsB,GAAG,CAAC,CAAA,CAAC,eAAe;AA24B5C,wDAAsB;AA14B1B,IAAM,0BAA0B,GAAG,EAAE,CAAA,CAAC,UAAU;AA24B5C,gEAA0B;AAz4B9B,SAAS;AACT,IAAM,kBAAkB,GAAG,CAAC,CAAA;AAy4BxB,gDAAkB;AAv4BtB,SAAS;AACT,IAAM,qBAAqB,GAAG;IAC1B,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;CACT,CAAA;AAg4BG,sDAAqB;AA93BzB,YAAY;AACZ,IAAM,YAAY,GAAG,GAAG,CAAA;AA83BpB,oCAAY;AA53BhB,eAAe;AACf,IAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAA;AA43B5B,0CAAe;AA13BnB,QAAQ;AACR,IAAM,YAAY,GAAG;IACjB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;IAChC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;CACnC,CAAA;AAy2BG,oCAAY;AAv2BhB,gBAAgB;AAChB,IAAM,mBAAmB,GAAG;IACxB,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;CAClB,CAAA;AAs1BG,kDAAmB;AAp1BvB,gBAAgB;AAChB,IAAM,yBAAyB,GAAG;IAC9B,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,GAAG;IACX,MAAM,EAAE,CAAC,GAAG,CAAC;CAChB,CAAA;AAo0BG,8DAAyB;AAl0B7B,QAAQ;AACR,IAAM,gBAAgB,GAAG;IACrB,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IAC3C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IAC5C,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;CAChD,CAAA;AAyzBG,4CAAgB;AAvzBpB,UAAU;AACV,IAAM,eAAe,GAAG;IACpB,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC9C,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAChD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IAClD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;IAClD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IACjD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IACnD,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;CACrD,CAAA;AA6yBG,0CAAe;AA3yBnB,YAAY;AACZ,IAAM,oBAAoB,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AA2yBpD,oDAAoB;AAzyBxB,SAAS;AACT,IAAM,mBAAmB,GAAG,GAAG,CAAA;AA0yB3B,kDAAmB;AAxyBvB,aAAa;AACb,IAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AAsyBjD,0CAAe;AApyBnB,WAAW;AACX,IAAM,gBAAgB,GAAG;IACrB,QAAQ,EAAE,aAAK,CAAC,MAAM;IACtB,QAAQ,EAAE,aAAK,CAAC,MAAM;IACtB,OAAO,EAAE,aAAK,CAAC,KAAK;CACvB,CAAA;AAiyBG,4CAAgB;AA/xBpB,cAAc;AACd,IAAM,cAAc;IAChB,GAAC,aAAK,CAAC,KAAK,IAAG,kBAAkB;IACjC,GAAC,aAAK,CAAC,SAAS,IAAG,gBAAgB;IACnC,GAAC,aAAK,CAAC,QAAQ,IAAG,eAAe;IACjC,GAAC,aAAK,CAAC,UAAU,IAAG,iBAAiB;OACxC,CAAA;AA0xBG,wCAAc;AAxxBlB,cAAc;AACd,IAAM,UAAU;IACZ,GAAC,aAAK,CAAC,MAAM,IAAG,QAAQ;IACxB,GAAC,aAAK,CAAC,MAAM,IAAG,QAAQ;IACxB,GAAC,aAAK,CAAC,KAAK,IAAG,OAAO;IACtB,GAAC,aAAK,CAAC,IAAI,IAAG,MAAM;IACpB,GAAC,aAAK,CAAC,KAAK,IAAG,OAAO;IACtB,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;IAC9B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,IAAI,IAAG,MAAM;IACpB,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;IAC9B,GAAC,aAAK,CAAC,OAAO,IAAG,SAAS;IAC1B,GAAC,aAAK,CAAC,QAAQ,IAAG,UAAU;IAC5B,GAAC,aAAK,CAAC,UAAU,IAAG,YAAY;IAChC,GAAC,aAAK,CAAC,OAAO,IAAG,SAAS;IAC1B,GAAC,aAAK,CAAC,SAAS,IAAG,WAAW;OACjC,CAAA;AAwwBG,gCAAU;AAtwBd,YAAY;AACZ,IAAM,UAAU;IACZ,GAAC,aAAK,CAAC,MAAM,IAAG,WAAW;IAC3B,GAAC,aAAK,CAAC,MAAM,IAAG,WAAW;IAC3B,GAAC,aAAK,CAAC,KAAK,IAAG,UAAU;IACzB,GAAC,aAAK,CAAC,IAAI,IAAG,SAAS;IACvB,GAAC,aAAK,CAAC,KAAK,IAAG,UAAU;IACzB,GAAC,aAAK,CAAC,SAAS,IAAG,cAAc;IACjC,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;IAC/B,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;IAC/B,GAAC,aAAK,CAAC,IAAI,IAAG,SAAS;IACvB,GAAC,aAAK,CAAC,SAAS,IAAG,cAAc;IACjC,GAAC,aAAK,CAAC,OAAO,IAAG,YAAY;IAC7B,GAAC,aAAK,CAAC,QAAQ,IAAG,aAAa;OAClC,CAAA;AAyvBG,gCAAU;AAvvBd,SAAS;AACT,IAAM,sBAAsB;IACxB,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACvC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,KAAK,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACjD,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC7C,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,OAAO,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACtC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACzC,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACzC,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,MAAM,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACrC,GAAC,eAAO,CAAC,MAAM,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAClD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACzD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,eAAe,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC9C,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC5C,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC1C,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IACvC,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,aAAa,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACzD,GAAC,eAAO,CAAC,WAAW,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACvD,GAAC,eAAO,CAAC,UAAU,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACtD,GAAC,eAAO,CAAC,qBAAqB,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACjE,GAAC,eAAO,CAAC,gBAAgB,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;IAC/C,GAAC,eAAO,CAAC,QAAQ,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IACpD,GAAC,eAAO,CAAC,cAAc,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;IAC1D,GAAC,eAAO,CAAC,OAAO,IAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE;OACtD,CAAA;AAotBG,wDAAsB;AAltB1B,WAAW;AACX,IAAM,qBAAqB;IACvB,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,UAAU,IAAG,SAAS;IACrC,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;OACvC,CAAA;AA8sBG,sDAAqB;AA5sBzB,WAAW;AACX,IAAM,qBAAqB;IACvB,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,UAAU,IAAG,SAAS;IACrC,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;OACvC,CAAA;AAwsBG,sDAAqB;AAtsBzB,SAAS;AACT,IAAM,gBAAgB;IAClB,GAAC,iBAAS,CAAC,IAAI,IAAG,SAAS;IAC3B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,KAAK,IAAG,SAAS;IAC5B,GAAC,iBAAS,CAAC,MAAM,IAAG,SAAS;IAC7B,GAAC,iBAAS,CAAC,MAAM,IAAG,SAAS;OAChC,CAAA;AA8rBG,4CAAgB;AA5rBpB,SAAS;AACT,IAAM,gBAAgB;IAClB,GAAC,qBAAa,CAAC,IAAI,IAAG,SAAS;IAC/B,GAAC,qBAAa,CAAC,SAAS,IAAG,SAAS;IACpC,GAAC,qBAAa,CAAC,IAAI,IAAG,SAAS;OAClC,CAAA;AAwrBG,4CAAgB;AAtrBpB,IAAM,YAAY,GAAG;IACjB,IAAI,EAAE,SAAS;CAClB,CAAA;AAuuBG,oCAAY;AAruBhB,YAAY;AACZ,IAAM,qBAAqB,GAAG;IAC1B,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;IACtB,CAAC,EAAE,CAAC,OAAO,CAAC;IACZ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;CACzB,CAAA;AAyqBG,sDAAqB;AAvqBzB,OAAO;AACP,IAAM,aAAa,GAAG;IAClB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE;IACzB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;IACxB,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;IACtB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;IACxB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;CAC5B,CAAA;AAiqBG,sCAAa;AA/pBjB,UAAU;AACV,IAAM,oBAAoB,GAAG;IACzB,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE,qBAAqB;CAC9B,CAAA;AAkpBG,oDAAoB;AAjpBxB,IAAM,uBAAuB,GAAG,CAAC,CAAA,CAAC,WAAW;AAkpBzC,0DAAuB;AAhpB3B,SAAS;AACT,IAAM,eAAe,GAAG;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;CACvB,CAAA;AAuoBG,0CAAe;AAroBnB,gBAAgB;AAChB,IAAM,eAAe,GAAG,EAAE,CAAA;AAqoBtB,0CAAe;AApoBnB,IAAM,gBAAgB,GAAG,EAAE,CAAA,CAAC,YAAY;AAqoBpC,4CAAgB;AApoBpB,IAAM,eAAe,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,CAAA,CAAC,cAAc;AAqoBnD,0CAAe;AAnoBnB,WAAW;AACX,IAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAmoBvC,4CAAgB;AAjoBpB,SAAS;AACT,IAAM,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAioBxD,0CAAe;AAhoBnB,IAAM,uBAAuB,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,CAAA;AAioBlD,0DAAuB;AAhoB3B,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AAgoBhC,kDAAmB;AA9nBvB,SAAS;AACT,IAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA,CAAC,2BAA2B;AA8nB7E,wCAAc;AA5nBlB,eAAe;AACf,IAAM,qBAAqB,GAAG,EAAE,CAAA;AA4nB5B,sDAAqB;AA1nBzB,aAAa;AACb,IAAM,kBAAkB,GAAG,OAAO,GAAG,EAAE,CAAA;AA0nBnC,gDAAkB;AAxnBtB,SAAS;AACT,IAAM,gBAAgB,GAAG,4BAA4B,CAAA;AAwnBjD,4CAAgB;AAvnBpB,IAAM,gBAAgB,GAAG,GAAG,CAAA;AAwnBxB,4CAAgB;AAtnBpB,kBAAkB;AAClB,IAAM,mCAAmC,GAAG,QAAQ,GAAG,EAAE,CAAA;AAsnBrD,kFAAmC;AApnBvC,iBAAiB;AACjB,IAAM,gBAAgB;IAClB,GAAC,gBAAQ,CAAC,MAAM,IAAG,CAAC;IACpB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,CAAC;IAC/B,GAAC,gBAAQ,CAAC,eAAe,IAAG,CAAC;IAC7B,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,CAAC;IAC/B,GAAC,gBAAQ,CAAC,KAAK,IAAG,CAAC;IACnB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,CAAC;IAC9B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,CAAC;IAC9B,GAAC,gBAAQ,CAAC,oBAAoB,IAAG,CAAC;IAClC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,CAAC;IACjC,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,SAAS,IAAG,CAAC;IACvB,GAAC,gBAAQ,CAAC,YAAY,IAAG,CAAC;IAC1B,GAAC,gBAAQ,CAAC,UAAU,IAAG,CAAC;IACxB,GAAC,gBAAQ,CAAC,OAAO,IAAG,CAAC;IACrB,GAAC,gBAAQ,CAAC,KAAK,IAAG,CAAC;IACnB,GAAC,gBAAQ,CAAC,aAAa,IAAG,CAAC;IAC3B,GAAC,gBAAQ,CAAC,cAAc,IAAG,CAAC;IAC5B,GAAC,gBAAQ,CAAC,cAAc,IAAG,CAAC;OAC/B,CAAA;AA+lBG,4CAAgB;AA7lBpB,cAAc;AACd,IAAM,cAAc;IAChB,GAAC,gBAAQ,CAAC,MAAM,IAAG,IAAI;IACvB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,oBAAoB,IAAG,IAAI;IACrC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,SAAS,IAAG,IAAI;IAC1B,GAAC,gBAAQ,CAAC,YAAY,IAAG,IAAI;IAC7B,GAAC,gBAAQ,CAAC,UAAU,IAAG,IAAI;IAC3B,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;OAClC,CAAA;AAqkBG,wCAAc;AAnkBlB,cAAc;AACd,IAAM,gBAAgB;IAClB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,WAAW,IAAG,IAAI;IAC5B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,KAAK,IAAG,IAAI;IACtB,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,OAAO,IAAG,IAAI;IACxB,GAAC,gBAAQ,CAAC,IAAI,IAAG,IAAI;IACrB,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,WAAW,IAAG,IAAI;IAC5B,GAAC,gBAAQ,CAAC,cAAc,IAAG,IAAI;IAC/B,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,QAAQ,IAAG,IAAI;IACzB,GAAC,gBAAQ,CAAC,IAAI,IAAG,IAAI;OACxB,CAAA;AA+iBG,4CAAgB;AA7iBpB,cAAc;AACd,IAAM,mBAAmB;IACrB,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,gBAAQ,CAAC,gBAAgB;IACtD,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,gBAAQ,CAAC,gBAAgB;IACtD,GAAC,gBAAQ,CAAC,aAAa,IAAG,gBAAQ,CAAC,eAAe;IAClD,GAAC,gBAAQ,CAAC,cAAc,IAAG,gBAAQ,CAAC,OAAO;IAC3C,GAAC,gBAAQ,CAAC,OAAO,IAAG,gBAAQ,CAAC,OAAO;IACpC,GAAC,gBAAQ,CAAC,WAAW,IAAG,gBAAQ,CAAC,OAAO;IACxC,GAAC,gBAAQ,CAAC,cAAc,IAAG,gBAAQ,CAAC,QAAQ;IAC5C,GAAC,gBAAQ,CAAC,QAAQ,IAAG,gBAAQ,CAAC,QAAQ;OACzC,CAAA;AA8hBG,kDAAmB;AA5hBvB,SAAS;AACT,IAAM,WAAW;IACb,GAAC,gBAAQ,CAAC,MAAM,IAAG,IAAI;IACvB,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,aAAa,IAAG,IAAI;IAC9B,GAAC,gBAAQ,CAAC,gBAAgB,IAAG,IAAI;IACjC,GAAC,gBAAQ,CAAC,eAAe,IAAG,IAAI;IAChC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,mBAAmB,IAAG,IAAI;IACpC,GAAC,gBAAQ,CAAC,YAAY,IAAG,IAAI;IAC7B,GAAC,gBAAQ,CAAC,WAAW,IAAG,IAAI;IAC5B,GAAC,gBAAQ,CAAC,iBAAiB,IAAG,IAAI;IAClC,GAAC,gBAAQ,CAAC,yBAAyB,IAAG,IAAI;IAC1C,GAAC,gBAAQ,CAAC,kBAAkB,IAAG,IAAI;IACnC,GAAC,gBAAQ,CAAC,uBAAuB,IAAG,IAAI;OAC3C,CAAA;AA4gBG,kCAAW;AA1gBf,SAAS;AACT,kDAAkD;AAClD,IAAM,kBAAkB,GAAG;IACvB,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClB,gBAAgB,EAAE,CAAC,QAAQ,CAAC;IAC5B,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3B,cAAc,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAC3B,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtB,MAAM,EAAE,CAAC,MAAM,CAAC;CACnB,CAAA;AAggBG,gDAAkB;AA9ftB,SAAS;AACT,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AA6fG,gDAAkB;AA3ftB,cAAc;AACd,IAAM,0BAA0B,GAAG,EAAE,CAAA;AA2fjC,gEAA0B;AA1f9B,aAAa;AACb,IAAM,yBAAyB,GAAG,GAAG,CAAA;AA0fjC,8DAAyB;AAzf7B,SAAS;AACT,IAAM,2BAA2B,GAAG;IAChC,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;CACP,CAAA;AAqfG,kEAA2B;AApf/B,cAAc;AACd,IAAM,eAAe;IACjB,GAAC,aAAK,CAAC,MAAM,IAAG,CAAC;IACjB,GAAC,aAAK,CAAC,MAAM,IAAG,CAAC;IACjB,GAAC,aAAK,CAAC,KAAK,IAAG,CAAC;IAChB,GAAC,aAAK,CAAC,QAAQ,IAAG,GAAG;IACrB,GAAC,aAAK,CAAC,IAAI,IAAG,GAAG;IACjB,GAAC,aAAK,CAAC,SAAS,IAAG,GAAG;IACtB,GAAC,aAAK,CAAC,OAAO,IAAG,GAAG;OACvB,CAAA;AA4eG,0CAAe;AA1enB,SAAS;AACT,IAAM,mBAAmB,GAAG,KAAK,CAAA;AA0e7B,kDAAmB;AAxevB,QAAQ;AACR,IAAM,kBAAkB,GAAG;IACvB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;IAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;IAC5B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;IAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;IAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;IAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,kBAAkB,EAAE;IACzC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;IAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE;CACrC,CAAA;AA8dG,gDAAkB;AA5dtB,WAAW;AACX,IAAM,cAAc,GAAG,QAAQ,GAAG,CAAC,CAAA;AA4d/B,wCAAc;AA1dlB,SAAS;AACT,IAAM,aAAa,GAAG;IAClB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACT,EAAE,EAAE,CAAC,CAAC,CAAC;CACV,CAAA;AAqdG,sCAAa;AAndjB,OAAO;AACP,IAAM,cAAc,GAAG;IACnB,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,EAAE,EAAE,EAAE;CACT,CAAA;AA8cG,wCAAc;AA5clB,YAAY;AACZ,IAAM,4BAA4B,GAAG,QAAQ,GAAG,CAAC,CAAA;AA4c7C,oEAA4B;AA3chC,IAAM,4BAA4B,GAAG,GAAG,CAAA;AA4cpC,oEAA4B;AA1chC,gBAAgB;AAChB,IAAM,qBAAqB,GAAG,CAAC,CAAA;AA0c3B,sDAAqB;AAxczB,SAAS;AACT,IAAM,eAAe,GAAG;IACpB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;CACzB,CAAA;AAkcG,0CAAe;AAhcnB,YAAY;AACZ,IAAM,iBAAiB,GAAG,OAAO,GAAG,EAAE,CAAA;AAgclC,8CAAiB;AA9brB,eAAe;AACf,IAAM,0BAA0B,GAAG,CAAC,CAAA;AA8bhC,gEAA0B;AA5b9B,aAAa;AACb,IAAM,sBAAsB,GAAG,GAAG,CAAA;AA4b9B,wDAAsB;AA1b1B,eAAe;AACf,IAAM,sBAAsB,GAAG,KAAK,GAAG,CAAC,CAAA;AA0bpC,wDAAsB;AAxb1B,eAAe;AACf,IAAM,sBAAsB,GAAG,GAAG,CAAA;AAwb9B,wDAAsB;AAtb1B,SAAS;AACT,IAAM,oBAAoB,GAAG,EAAE,CAAA;AAsb3B,oDAAoB;AApbxB,WAAW;AACX,IAAM,0BAA0B,GAAG,CAAC,CAAA;AAobhC,gEAA0B;AAlb9B,WAAW;AACX,IAAM,gCAAgC,GAAG,GAAG,CAAA;AAkbxC,4EAAgC;AAhbpC,aAAa;AACb,IAAM,mBAAmB,GAAG,EAAE,CAAA;AAgb1B,kDAAmB;AA9avB,YAAY;AACZ,IAAM,oBAAoB,GAAG,CAAC,GAAG,QAAQ,CAAA;AA8arC,oDAAoB;AA5axB,WAAW;AACX,IAAM,oBAAoB,GAAG,EAAE,CAAA;AA4a3B,oDAAoB;AA3axB,SAAS;AACT,IAAM,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAA;AA2a9B,4CAAgB;AAzapB,OAAO;AACP,IAAM,cAAc,GAAW,EAAE,CAAA;AA0a7B,wCAAc;AAzalB,IAAM,kBAAkB,GAAW,IAAI,CAAA,CAAC,gBAAgB;AA0apD,gDAAkB;AAzatB,IAAM,uBAAuB,GAAW,CAAC,CAAA,CAAC,eAAe;AA0arD,0DAAuB;AAza3B,IAAM,kBAAkB,GAAW,KAAK,CAAA,CAAC,YAAY;AA0ajD,gDAAkB;AAzatB,IAAM,yBAAyB,GAAW,KAAK,GAAG,EAAE,CAAA,CAAC,QAAQ;AA0azD,8DAAyB;AAxa7B,SAAS;AACT,IAAM,iBAAiB,GAAG,EAAE,CAAA;AAwaxB,8CAAiB;AAvarB,IAAM,qBAAqB,GAAG,EAAE,CAAA;AAwa5B,sDAAqB;AAtazB,aAAa;AACb,IAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;AAsaxC,wDAAsB;AAra1B,YAAY;AACZ,IAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAA;AAqa1B,sCAAa;AAnajB,UAAU;AACV,IAAM,kBAAkB,GAAG,EAAE,CAAA;AAmazB,gDAAkB;AAlatB,YAAY;AACZ,IAAM,uBAAuB,GAAG,GAAG,CAAA;AAka/B,0DAAuB;AAha3B,cAAc;AACd,IAAM,yBAAyB,GAAG,CAAC,CAAA;AAga/B,8DAAyB;AA/Z7B,SAAS;AACT,IAAM,gCAAgC,GAAG,EAAE,CAAA;AA+ZvC,4EAAgC;AA9ZpC,IAAM,2BAA2B,GAAG,GAAG,CAAA;AA+ZnC,kEAA2B;AA7Z/B,cAAc;AACd,IAAM,iBAAiB,GAAG,GAAG,CAAA;AA6ZzB,8CAAiB;AA3ZrB,SAAS;AACT,IAAM,aAAa,GAAG;IAClB,aAAa;IACb,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3B,8BAA8B;IAC9B,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3D,SAAS;IACT,+DAA+D;IAC/D,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;CAC9H,CAAA;AAmZG,sCAAa;AAjZjB,SAAS;AACT,IAAM,iBAAiB,GAAG,OAAO,GAAG,CAAC,CAAA;AAiZjC,8CAAiB;AA/YrB,aAAa;AACb,IAAM,iBAAiB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AA+YjC,8CAAiB;AA7YrB,cAAc;AACd,IAAM,SAAS,GAAG;IACd,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,EAAE;CACR,CAAA;AAsYG,8BAAS;AApYb,UAAU;AACV,IAAM,cAAc,GAAG,IAAI,CAAA;AAoYvB,wCAAc;AAnYlB,IAAI;AACJ,IAAM,aAAa,GAAG,IAAI,CAAA;AAmYtB,sCAAa;AAlYjB,IAAI;AACJ,IAAM,YAAY,GAAG,IAAI,CAAA;AAkYrB,oCAAY;AAhYhB,KAAK;AACL,IAAM,OAAO;IACT,GAAC,aAAK,CAAC,MAAM,IAAG,IAAI;IACpB,GAAC,aAAK,CAAC,MAAM,IAAG,IAAI;IACpB,GAAC,aAAK,CAAC,KAAK,IAAG,IAAI;IACnB,GAAC,aAAK,CAAC,QAAQ,IAAG,IAAI;IACtB,GAAC,aAAK,CAAC,QAAQ,IAAG,IAAI;IACtB,GAAC,aAAK,CAAC,IAAI,IAAG,IAAI;IAClB,GAAC,aAAK,CAAC,SAAS,IAAG,IAAI;IACvB,GAAC,aAAK,CAAC,OAAO,IAAG,IAAI;OACxB,CAAA;AAuXG,0BAAO;AArXX,WAAW;AACX,IAAM,kBAAkB,GAAG,EAAE,CAAA;AAqXzB,gDAAkB;AAnXtB,WAAW;AACX,IAAM,oBAAoB,GAAG,CAAC,CAAA;AAmX1B,oDAAoB;AAjXxB,YAAY;AACZ,IAAM,sBAAsB;IACxB,GAAC,qBAAa,CAAC,IAAI,IAAG,MAAM;IAC5B,GAAC,qBAAa,CAAC,MAAM,IAAG,MAAM;IAC9B,GAAC,qBAAa,CAAC,MAAM,IAAG,MAAM;IAC9B,GAAC,qBAAa,CAAC,UAAU,IAAG,UAAU;OACzC,CAAA;AA4WG,wDAAsB;AA1W1B,KAAK;AACL,IAAM,UAAU,GAAG,EAAE,CAAA;AA0WjB,gCAAU;AAzWd,SAAS;AACT,IAAM,SAAS,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAyW5B,8BAAS;AAxWb,eAAe;AACf,IAAM,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,EAAE,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;AAwWzE,wCAAc;AArWlB,YAAY;AACZ,IAAM,0BAA0B,GAAG;IAC/B,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;IAC1B,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE;IAC5B,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;IAC9B,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;CACnC,CAAA;AAgWG,gEAA0B;AA9V9B,UAAU;AACV,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;IACpC,CAAC,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE;CACvC,CAAA;AAuVG,gDAAkB;AArVtB,UAAU;AACV,IAAM,iBAAiB,GAAG;IACtB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AA8UG,8CAAiB;AA5UrB,SAAS;AACT,IAAM,oBAAoB,GAAG,aAAa,CAAA,CAAC,QAAQ;AA4U/C,oDAAoB;AA1UxB,WAAW;AACX,IAAM,wBAAwB,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA,CAAC,cAAc;AA0UrD,4DAAwB;AAxU5B,WAAW;AACX,IAAM,iBAAiB,GAAG,QAAQ,CAAA;AAwU9B,8CAAiB;AAtUrB,WAAW;AACX,IAAM,mBAAmB,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAA;AAsUhE,kDAAmB;AApUvB,gBAAgB;AAChB,IAAM,oBAAoB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA,CAAC,OAAO;AAoUpD,oDAAoB;AAlUxB,SAAS;AACT,IAAM,sBAAsB,GAAG,EAAE,CAAA;AAkU7B,wDAAsB;AAjU1B,YAAY;AACZ,IAAM,kBAAkB,GAAG,CAAC,CAAA;AAiUxB,gDAAkB;AA/TtB,SAAS;AACT,IAAM,mBAAmB,GAAG,GAAG,CAAA;AA+T3B,kDAAmB;AA7TvB,oBAAoB;AACpB,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;IACL,CAAC,EAAE,EAAE;CACR,CAAA;AAsTG,gDAAkB;AApTtB,UAAU;AACV,IAAM,wBAAwB,GAAG,KAAK,CAAA;AAoTlC,4DAAwB;AAlT5B,YAAY;AACZ,IAAM,iBAAiB;IACnB,GAAC,iBAAS,CAAC,MAAM,IAAG,uBAAe,CAAC,MAAM;IAC1C,GAAC,iBAAS,CAAC,IAAI,IAAG,uBAAe,CAAC,IAAI;IACtC,GAAC,iBAAS,CAAC,KAAK,IAAG,uBAAe,CAAC,KAAK;IACxC,GAAC,iBAAS,CAAC,SAAS,IAAG,uBAAe,CAAC,KAAK;OAC/C,CAAA;AA6SG,8CAAiB;AA3SrB,WAAW;AACX,IAAM,0BAA0B,GAAG,CAAC,CAAA;AA2ShC,gEAA0B;AAzS9B,aAAa;AACb,IAAM,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;AAySzC,8CAAiB;AAvSrB,SAAS;AACT,IAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAA;AAuSzB,8CAAiB;AArSrB,UAAU;AACV,IAAM,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;AAqShE,0CAAe;AAnSnB,EAAE;AACF,IAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;AAmSpE,kDAAmB;AAlSvB,IAAM,qBAAqB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAmS1C,sDAAqB;AAlSzB,IAAM,0BAA0B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAmS/C,gEAA0B;AAjS9B,WAAW;AACX,IAAM,kBAAkB,GAAG;IACvB,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;IACD,IAAI;IACJ;QACI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;QACtE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;KAC1E;CACJ,CAAA;AAgQG,gDAAkB;AA9PtB,SAAS;AACT,IAAM,mBAAmB,GAAG;IACxB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AAwPG,kDAAmB;AAtPvB,QAAQ;AACR,IAAM,UAAU,GAAG;IACf;QACI,IAAI,EAAE,uBAAe,CAAC,IAAI;QAC1B,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,EAAE;QACZ,GAAG,EAAE,EAAE;QACP,KAAK,EAAE,CAAC;QACR,mBAAmB,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;QAC3D,eAAe,EAAE,CAAC,cAAc,EAAE,gBAAgB,CAAC;QACnD,SAAS,EAAE,CAAC,eAAe,CAAC;QAC5B,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;KACpC;IACD;QACI,IAAI,EAAE,uBAAe,CAAC,KAAK;QAC3B,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,EAAE;QACZ,GAAG,EAAE,EAAE;QACP,KAAK,EAAE,CAAC;QACR,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;QACjE,eAAe,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;QAC1D,SAAS,EAAE,CAAC,qBAAqB,CAAC;QAClC,QAAQ,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC;KACzD;CACJ,CAAA;AA+NG,gCAAU;AA7Nd,UAAU;AACV,IAAM,kBAAkB;IACpB,GAAC,aAAK,CAAC,UAAU,IAAG,IAAI;IACxB,GAAC,aAAK,CAAC,YAAY,IAAG,IAAI;IAC1B,GAAC,aAAK,CAAC,WAAW,IAAG,IAAI;IACzB,GAAC,aAAK,CAAC,SAAS,IAAG,IAAI;OAC1B,CAAA;AAwNG,gDAAkB;AAtNtB,OAAO;AACP,IAAM,iBAAiB,GAAG;IACtB,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;IACZ,CAAC,EAAE,SAAS;CACf,CAAA;AA8MG,8CAAiB;AA3MrB,iCAAiC;AACjC,IAAM,kBAAkB,GAAG;IACvB,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;CACT,CAAA;AAoMG,gDAAkB;AAlMtB,iCAAiC;AACjC,IAAM,mBAAmB,GAAG;IACxB,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,CAAC;CACP,CAAA;AA2LG,kDAAmB;AAzLvB,QAAQ;AACR,IAAM,2BAA2B,GAAG,GAAG,CAAA;AAyLnC,kEAA2B", "file": "", "sourceRoot": "/", "sourcesContent": ["/////////////// 所有常量（全大写单词间用下划线隔开）///////////////\n\nimport { ArmyState, BookCommentType, BuffType, CEffect, CType, LobbyModeType, MailStateType, MarchLineType, MonthlyCardType, StudyType } from \"./Enums\"\n\n// 点击间隔\nconst CLICK_SPACE = 10\n// 一格的大小\nconst TILE_SIZE = 80\n// 地图边界额外宽度\nconst MAP_EXTRA_SIZE = 2\n\n// 地图显示偏移\nconst MAP_SHOW_OFFSET = cc.v2(TILE_SIZE * 8, TILE_SIZE * 8)\n\n//\nconst TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5)\n\n// 设施拖拽时候的高\nconst BUILD_DRAG_OFFSETY = TILE_SIZE_HALF.y - 24\n\n// 区域最高y坐标\nconst AREA_MAX_ZINDEX = 21 * TILE_SIZE\n// 层级最大值\nconst MAX_ZINDEX = 10000\n\n// 长按时间\nconst LONG_PRESS_TIME = 0.4\n\n// 延迟关闭pnl时间\nconst DELAY_CLOSE_PNL_TIME = 0.4\n\n// 主城id\nconst CITY_MAIN_NID = 1001\n// 要塞id\nconst CITY_FORT_NID = 2102\n// 4个遗迹\nconst ANCIENT_WALL_ID = 3000 //城墙\nconst CITY_CHANGAN_ID = 3001 //长安\nconst CITY_JINLING_ID = 3002 //金陵\nconst CITY_YANJING_ID = 3003 //燕京\nconst CITY_LUOYANG_ID = 3004 //洛阳\n// 农场\nconst BUILD_FARM_ID = 2201\n// 伐木场\nconst BUILD_TIMBER_ID = 2202\n// 采石场\nconst BUILD_QUARRY_ID = 2203\n// 城墙建筑id\nconst BUILD_WALL_NID = 2000\n// 主城id\nconst BUILD_MAIN_NID = 2001\n// 仓库建筑id\nconst BUILD_GRANARY_NID = 2002\n// 粮仓建筑id\nconst BUILD_WAREHOUSE_NID = 2003\n// 兵营建筑id\nconst BUILD_BARRACKS_NID = 2004\n// 大使馆建筑id\nconst BUILD_EMBASSY_NID = 2005\n// 市场建筑id\nconst BUILD_BAZAAR_NID = 2006\n// 铁匠铺所建筑id\nconst BUILD_SMITHY_NID = 2008\n// 校场建筑id\nconst BUILD_DRILLGROUND_NID = 2011\n// 工厂建筑id\nconst BUILD_PLANT_NID = 2010\n// 联盟市场建筑id\nconst BUILD_ALLI_BAZAAR_NID = 2014\n// 英雄殿建筑id\nconst BUILD_HEROHALL_NID = 2015\n// 医馆建筑id\nconst BUILD_HOSPITAL_NID = 2016\n// 旗子id\nconst BUILD_FLAG_NID = 2101\n// 要塞建筑id\nconst BUILD_FORT_NID = 2102\n// 箭塔建筑id\nconst BUILD_TOWER_NID = 2103\n\n// 强弩兵ID\nconst PAWN_CROSSBOW_ID = 3305\n// 斧骑兵ID\nconst AX_CAVALRY_ID = 3406\n\n// 初始资源产量\nconst INIT_RES_OUTPUT = 120\n// 初始容量\nconst INIT_RES_CAP = 1000\n// 初始资源\nconst INIT_RES_COUNT = 700\n// 默认修建队列\nconst DEFAULT_BT_QUEUE_COUNT = 2\n\n// 立即完成修建需要的金币数\nconst IN_DONE_BT_GOLD = 30\n// 立即完成打造需要的金币数\nconst IN_DONE_FORGE_GOLD = 30\n// 修改昵称需要的金币数\nconst MODIFY_NICKNAME_GOLD = 500\n\n// 军队最大士兵个数\nconst ARMY_PAWN_MAX_COUNT = 9\n// 大使馆多少级可以创建联盟\nconst CREATE_ALLI_MAX_LV = 3\n\n// \nconst DEFAULT_CITY_SIZE = cc.v2(1, 1) //默认的城市地块大小\nconst DEFAULT_AREA_SIZE = cc.v2(11, 11) //默认的区域大小\nconst DEFAULT_BUILD_SIZE = cc.v2(1, 1) //默认的建筑面积大小\nconst BOSS_BUILD_SIZE = cc.v2(3, 3) //boss\nconst DEFAULT_MAX_ARMY_COUNT = 5 //默认区域的最大容纳军队数量\nconst DEFAULT_MAX_ADD_PAWN_TIMES = 20 //默认最大补兵次数\n\n// 加速行军倍数\nconst UP_MARCH_SPEED_MUL = 3\n\n// 城边加速倍数\nconst MAIN_CITY_MARCH_SPEED = {\n    1: 4,\n    2: 3.5,\n    3: 3,\n    4: 2.5,\n    5: 2,\n    6: 1.5,\n}\n\n// 运送时间 格/小时\nconst TRANSIT_TIME = 300\n\n// 一场战斗最多持续时间 秒\nconst BATTLE_MAX_TIME = 3600 * 3\n\n// 地块底配置\nconst LAND_DI_CONF = [\n    { list: [0, 0, 0, 0], no: '01' },\n    { list: [0, 1, 1, 0], no: '02' },\n    { list: [0, 1, 1, 1], no: '03' },\n    { list: [0, 0, 1, 1], no: '04' },\n    { list: [1, 1, 1, 0], no: '05' },\n    { list: [1, 1, 1, 1], no: '06' },\n    { list: [1, 0, 1, 1], no: '07' },\n    { list: [1, 1, 0, 0], no: '08' },\n    { list: [1, 1, 0, 1], no: '09' },\n    { list: [1, 0, 0, 1], no: '10' },\n    { list: [0, 0, 1, 0], no: '11' },\n    { list: [1, 0, 1, 0], no: '12' },\n    { list: [1, 0, 0, 0], no: '13' },\n    { list: [0, 1, 0, 0], no: '14' },\n    { list: [0, 1, 0, 1], no: '15' },\n    { list: [0, 0, 0, 1], no: '16' },\n]\n\n// 地块底配置，方向：左上右下\nconst DECORATION_MUD_CONF = {\n    '0011': 1,\n    '1011': 2,\n    '1001': 3,\n    '0111': 4,\n    '1101': 6,\n    '0110': 7,\n    '1110': 8,\n    '1100': 9,\n    '0010': 10,\n    '1010': 11,\n    '1000': 12,\n    '0001': 13,\n    '0101': 14,\n    '0100': 15,\n    '0000': 21,\n    '1111': [5, 16],\n}\n\n// 地块底配置，方向：左上右下\nconst DECORATION_MUD_OUTER_CONF = {\n    '0011': 97,\n    '1011': 98,\n    '1001': 99,\n    '0111': 100,\n    '1101': 102,\n    '0110': 103,\n    '1110': 104,\n    '1100': 105,\n    '0010': 106,\n    '1010': 107,\n    '1000': 108,\n    '0001': 109,\n    '0101': 110,\n    '0100': 111,\n    '1111': [101],\n}\n\n// 边框线配置\nconst BORDER_LINE_CONF = [\n    { size: cc.size(80, 4), pos: cc.v2(0, 38) }, //上\n    { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右\n    { size: cc.size(80, 4), pos: cc.v2(0, -38) }, //下\n    { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左\n    { size: cc.size(4, 4), pos: cc.v2(-38, 38) }, //左上\n    { size: cc.size(4, 4), pos: cc.v2(38, 38) }, //右上\n    { size: cc.size(4, 4), pos: cc.v2(38, -38) }, //右下\n    { size: cc.size(4, 4), pos: cc.v2(-38, -38) }, //左下\n]\n\n// 河流边框线配置\nconst RIVER_LINE_CONF = {\n    0: { size: cc.size(80, 4), pos: cc.v2(0, 30) }, //上\n    111: { size: cc.size(4, 68), pos: cc.v2(38, -6) }, //右 下短上短\n    112: { size: cc.size(4, 80), pos: cc.v2(38, 0) }, //右 下短上长\n    121: { size: cc.size(4, 80), pos: cc.v2(38, -12) }, //右 下长上短\n    122: { size: cc.size(4, 92), pos: cc.v2(38, -6) }, //右 下长上长\n    311: { size: cc.size(4, 68), pos: cc.v2(-38, -6) }, //左 下短上短\n    312: { size: cc.size(4, 80), pos: cc.v2(-38, 0) }, //左 下短上长\n    321: { size: cc.size(4, 80), pos: cc.v2(-38, -12) }, //左 下长上短\n    322: { size: cc.size(4, 92), pos: cc.v2(-38, -6) }, //左 下长上长\n}\n\n// 选择地块信息框大小\nconst SELECT_CELL_INFO_BOX = cc.rect(320, 320, 272, 320)\n\n// 士兵气泡高度\nconst PAWN_BUBBLE_OFFSETY = 100\n\n// 地块资源配置列表字段\nconst CELL_RES_FIELDS = ['cereal', 'timber', 'stone']\n\n// 资源字段反向映射\nconst RES_FIELDS_CTYPE = {\n    'cereal': CType.CEREAL,\n    'timber': CType.TIMBER,\n    'stone': CType.STONE,\n}\n\n// 通用类型对应图标url\nconst CTYPE_ICON_URL = {\n    [CType.TITLE]: 'icon/title_empty',\n    [CType.WIN_POINT]: 'icon/win_point',\n    [CType.HERO_OPT]: 'icon/hero_opt',\n    [CType.UP_RECRUIT]: 'icon/up_recruit',\n}\n\n// 通用类型对应图标url\nconst CTYPE_ICON = {\n    [CType.CEREAL]: 'cereal',\n    [CType.TIMBER]: 'timber',\n    [CType.STONE]: 'stone',\n    [CType.GOLD]: 'gold',\n    [CType.INGOT]: 'ingot',\n    [CType.WAR_TOKEN]: 'war_token',\n    [CType.EXP_BOOK]: 'exp_book',\n    [CType.CEREAL_C]: 'cereal_c',\n    [CType.IRON]: 'iron',\n    [CType.UP_SCROLL]: 'up_scroll',\n    [CType.FIXATOR]: 'fixator',\n    [CType.BASE_RES]: 'base_res',\n    [CType.BASE_RES_2]: 'base_res_2',\n    [CType.STAMINA]: 'stamina',\n    [CType.RANK_COIN]: 'rank_coin',\n}\n\n// 通用类型对应的名字\nconst CTYPE_NAME = {\n    [CType.CEREAL]: 'ui.cereal',\n    [CType.TIMBER]: 'ui.timber',\n    [CType.STONE]: 'ui.stone',\n    [CType.GOLD]: 'ui.gold',\n    [CType.INGOT]: 'ui.ingot',\n    [CType.WAR_TOKEN]: 'ui.war_token',\n    [CType.EXP_BOOK]: 'ui.exp_book',\n    [CType.CEREAL_C]: 'ui.cereal_c',\n    [CType.IRON]: 'ui.iron',\n    [CType.UP_SCROLL]: 'ui.up_scroll',\n    [CType.FIXATOR]: 'ui.fixator',\n    [CType.BASE_RES]: 'ui.base_res',\n}\n\n// 建筑效果配置\nconst BUILD_EFFECT_TYPE_CONF = {\n    [CEffect.BT_QUEUE]: { vtype: 'number' },\n    [CEffect.BUILD_CD]: { vtype: 'number', suffix: '%' }, //减少建造时间 2\n    [CEffect.GRANARY_CAP]: { vtype: 'number' }, //粮仓容量 3\n    [CEffect.WAREHOUSE_CAP]: { vtype: 'number' }, //仓库容量 4\n    [CEffect.XL_CD]: { vtype: 'number', suffix: '%' }, //减少步兵训练速度 5\n    [CEffect.ALLIANCE_PERS]: { vtype: 'number' }, //联盟人数 6\n    [CEffect.MERCHANT_COUNT]: { vtype: 'number' }, //商人数量 7\n    [CEffect.DRILL_QUEUE]: { vtype: 'number' }, //招募队列 8\n    [CEffect.WALL_HP]: { vtype: 'number' }, //城墙血量 9\n    [CEffect.FORGE_CD]: { vtype: 'number', suffix: '%' }, //打造装备速度 10\n    [CEffect.ARMY_COUNT]: { vtype: 'number' }, //军队数量 11\n    [CEffect.RES_OUTPUT]: { vtype: 'number' }, //资源产量 12\n    [CEffect.MARCH_CD]: { vtype: 'number', suffix: '%' }, //减少行军时间 13\n    [CEffect.UPLVING_CD]: { vtype: 'number', suffix: '%' }, //训练士兵时间 14\n    [CEffect.GW_CAP]: { vtype: 'number' }, //增加粮仓和仓库容量 15\n    [CEffect.XL_2LV]: { vtype: 'number', suffix: '%' }, //有一定几率训练出2级士兵 16\n    [CEffect.TRANSIT_CD]: { vtype: 'number', suffix: '%' }, //每个商人的运输量增加1000 减少商人运送时间 17\n    [CEffect.MAIN_MARCH_MUL]: { vtype: 'number', suffix: '%' }, //主城要塞行军时间加速 18\n    [CEffect.CITY_BUILD_CD]: { vtype: 'number', suffix: '%' }, //地面建筑修建时间减少 19 x\n    [CEffect.TREASURE_AWARD]: { vtype: 'number', suffix: '%' }, //宝箱奖励增加 20\n    [CEffect.FREE_RECAST]: { vtype: 'number', suffix: '%' }, //有一定几率免费重铸装备 21\n    [CEffect.RARE_RES_OUTPUT]: { vtype: 'number' }, //书铁每天增加 22\n    [CEffect.MORE_RARE_RES]: { vtype: 'number' }, //书铁超过100 每天增加 23\n    [CEffect.LV_UP_QUEUE]: { vtype: 'number' }, //训练队列 24\n    [CEffect.TOWER_LV]: { vtype: 'number' }, //哨塔等级 25\n    [CEffect.FARM_OUTPUT]: { vtype: 'number', suffix: '%' }, //农场产量 26\n    [CEffect.QUARRY_OUTPUT]: { vtype: 'number', suffix: '%' }, //采石场产量 27\n    [CEffect.MILL_OUTPUT]: { vtype: 'number', suffix: '%' }, //伐木场产量 28\n    [CEffect.CURE_QUEUE]: { vtype: 'number', suffix: '%' }, //治疗队列 29\n    [CEffect.MARKET_SERVICE_CHARGE]: { vtype: 'number', suffix: '%' }, //置换手续费 30\n    [CEffect.CITY_COUNT_LIMIT]: { vtype: 'number' }, //地面建筑上限 31\n    [CEffect.TOWER_HP]: { vtype: 'number', suffix: '%' }, //哨塔，要塞，城墙的耐久提高 35\n    [CEffect.OTHER_RES_ODDS]: { vtype: 'number', suffix: '%' }, //地块的其他资源掉落概率提高 36\n    [CEffect.CURE_CD]: { vtype: 'number', suffix: '%' }, // 治疗伤兵速度 37\n}\n\n// 行军军队名字颜色\nconst MARCH_ARMY_NAME_COLOR = {\n    [MarchLineType.SELF_ARMY]: '#59A733',\n    [MarchLineType.OTHER_ARMY]: '#C34B3F',\n    [MarchLineType.ALLI_ARMY]: '#4F8FBA',\n}\n\n// 行军军队时间颜色\nconst MARCH_ARMY_TIME_COLOR = {\n    [MarchLineType.SELF_ARMY]: '#FFFFFF',\n    [MarchLineType.OTHER_ARMY]: '#FF9162',\n    [MarchLineType.ALLI_ARMY]: '#7FD6FF',\n}\n\n// 军队状态颜色\nconst ARMY_STATE_COLOR = {\n    [ArmyState.NONE]: '#936E5A',\n    [ArmyState.MARCH]: '#936E5A',\n    [ArmyState.FIGHT]: '#C34B3F',\n    [ArmyState.DRILL]: '#59A733',\n    [ArmyState.LVING]: '#59A733',\n    [ArmyState.CURING]: '#59A733',\n    [ArmyState.TONDEN]: '#59A733',\n}\n\n// 邮件状态颜色\nconst MAIL_STATE_COLOR = {\n    [MailStateType.NONE]: '#C34B3F',\n    [MailStateType.NOT_CLAIM]: '#C34B3F',\n    [MailStateType.READ]: '#A18876',\n}\n\nconst COLOR_NORMAL = {\n    DONE: '#21DC2D'\n}\n\n// 军队记录说明的配置\nconst ARMY_RECORD_DESC_CONF = {\n    0: ['index'], //在{0}发生战斗\n    1: ['index', 'target'], //从{0}移动到{1}\n    2: ['index', 'target'], //从{0}移动到{1}，被撤回\n    3: ['index', 'target'], //在{0}被遣返回{1}\n    4: ['index'], //在{0}被强行解散\n    5: ['index', 'target'], //从{0}移动到{1}，被遣返\n    6: ['index'], //在{0}被强制撤离\n    7: ['index', 'target'], //从{0}移动到{1}，被强制撤离\n}\n\n// 回放倍数\nconst PLAYBACK_MULS = [\n    { val: 4, text: '0.25x' },\n    { val: 2, text: '0.5x' },\n    { val: 1, text: '1x' },\n    { val: 0.5, text: '2x' },\n    { val: 0.25, text: '4x' },\n]\n\n// 固定到菜单配置\nconst FIXATION_MENU_CONFIG = {\n    2001: 'build/BuildMainInfo', //主城\n    2004: 'build/BuildBarracks', //兵营\n    2005: 'build/BuildEmbassy', //联盟\n    2006: 'build/BuildBazaar', //自由市场\n    2008: 'build/BuildSmithy', //铁匠铺\n    2010: 'build/BuildFactory', //工厂\n    2011: 'build/BuildDrillground', //校场\n    2012: 'build/BuildTower', //里亭属\n    2013: 'build/BuildTower', //边塞营\n    2014: 'build/BuildBazaar', //联盟市场\n    2015: 'build/BuildHerohall', //英雄殿\n    2016: 'build/BuildHospital', // 医馆\n}\nconst FIXATION_MENU_MAX_COUNT = 3 //固定到菜单最多个数\n\n// 免费头像列表\nconst FREE_HEAD_ICONS = [\n    'head_icon_free_001',\n    'head_icon_free_002',\n    'head_icon_free_003',\n    'head_icon_free_004',\n    'head_icon_free_005',\n    'head_icon_free_006',\n    'head_icon_free_007',\n    'head_icon_free_008',\n]\n\n// 商城购买添加产量需要的金币\nconst ADD_OUTPUT_GOLD = 50\nconst ADD_OUTPUT_RATIO = 20 //商城购买添加产量比例\nconst ADD_OUTPUT_TIME = 1 * 86400 * 1000 //商城购买添加产量持续时间\n\n// 内政政策槽位配置\nconst POLICY_SLOT_CONF = [3, 5, 10, 15, 20]\n\n// 装备槽位配置\nconst EQUIP_SLOT_CONF = [1, 3, 5, 7, 10, 12, 14, 16, 18, 20]\nconst EQUIP_SLOT_EXCLUSIVE_LV = { 10: true, 18: true }\n// 装备融炼解锁等级\nconst EQUIP_SMELT_NEED_LV = [14, 20]\n\n// 士兵槽位配置\nconst PAWN_SLOT_CONF = [1, 2, 4, 7, 1001, 1010, 1020] // 兵营额外的槽位的key：1000+英雄殿槽位lv\n\n// 研究每重置一次需要的金币\nconst RESET_STUDY_SLOT_GOLD = 50\n\n// 多长时间可以退出联盟\nconst CAN_EXIT_ALLI_TIME = 3600000 * 12\n\n// 创建联盟费用\nconst CREATE_ALLI_COST = '1,0,3000|2,0,2000|3,0,2000'\nconst CREATE_ALLI_COND = 100\n\n// 单个玩家给其他玩家改变人气间隔\nconst ONE_USER_POPULARITY_CHANGE_INTERVAL = 86400000 * 30\n\n// 外显buff 同时显示 层级\nconst BUFF_NODE_ZINDEX = {\n    [BuffType.SHIELD]: 1,\n    [BuffType.PROTECTION_SHIELD]: 1,\n    [BuffType.RODELERO_SHIELD]: 1,\n    [BuffType.RODELERO_SHIELD_001]: 1,\n    [BuffType.RODELERO_SHIELD_102]: 1,\n    [BuffType.ABNEGATION_SHIELD]: 1,\n    [BuffType.PARRY]: 2,\n    [BuffType.PARRY_001]: 2,\n    [BuffType.PARRY_102]: 2,\n    [BuffType.WITHSTAND]: 2,\n    [BuffType.JUMPSLASH_DAMAGE]: 2,\n    [BuffType.BEHEADED_GENERAL]: 2,\n    [BuffType.ANTICIPATION_DEFENSE]: 2,\n    [BuffType.ANTICIPATION_ATTACK]: 2,\n    [BuffType.DIZZINESS]: 3,\n    [BuffType.PARALYSIS]: 3,\n    [BuffType.PARALYSIS_UP]: 3,\n    [BuffType.WIRE_CHAIN]: 3,\n    [BuffType.SILENCE]: 4,\n    [BuffType.CHAOS]: 4,\n    [BuffType.POISONED_WINE]: 4,\n    [BuffType.LIAN_PO_ATTACK]: 5,\n    [BuffType.LIAN_PO_DEFEND]: 5,\n}\n\n// 外显buff 同时显示\nconst NEED_SHOW_BUFF = {\n    [BuffType.SHIELD]: true,\n    [BuffType.PROTECTION_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD_001]: true,\n    [BuffType.RODELERO_SHIELD_102]: true,\n    [BuffType.ABNEGATION_SHIELD]: true,\n    [BuffType.PARRY]: true,\n    [BuffType.PARRY_001]: true,\n    [BuffType.PARRY_102]: true,\n    [BuffType.WITHSTAND]: true,\n    [BuffType.JUMPSLASH_DAMAGE]: true,\n    [BuffType.BEHEADED_GENERAL]: true,\n    [BuffType.ANTICIPATION_DEFENSE]: true,\n    [BuffType.ANTICIPATION_ATTACK]: true,\n    [BuffType.DIZZINESS]: true,\n    [BuffType.PARALYSIS]: true,\n    [BuffType.PARALYSIS_UP]: true,\n    [BuffType.WIRE_CHAIN]: true,\n    [BuffType.SILENCE]: true,\n    [BuffType.CHAOS]: true,\n    [BuffType.POISONED_WINE]: true,\n    [BuffType.LIAN_PO_ATTACK]: true,\n    [BuffType.LIAN_PO_DEFEND]: true,\n}\n\n// 外显buff 互斥显示\nconst NEED_MUTUAL_BUFF = {\n    [BuffType.ARMOR_PENETRATION]: true,\n    [BuffType.INSPIRE]: true,\n    [BuffType.INSPIRE_001]: true,\n    [BuffType.WORTHY_MONARCH]: true,\n    [BuffType.DESTROY_WEAPONS]: true,\n    [BuffType.POISONING_MAX_HP]: true,\n    [BuffType.POISONING_CUR_HP]: true,\n    [BuffType.INFECTION_PLAGUE]: true,\n    [BuffType.BLEED]: true,\n    [BuffType.DAMAGE_INCREASE]: true,\n    [BuffType.DAMAGE_REDUCE]: true,\n    [BuffType.GOD_WAR]: true,\n    [BuffType.FEAR]: true,\n    [BuffType.TIMIDITY]: true,\n    [BuffType.TIGER_MANIA]: true,\n    [BuffType.IRREMOVABILITY]: true,\n    [BuffType.OVERLORD]: true,\n    [BuffType.IGNITION]: true,\n    [BuffType.RAGE]: true,\n}\n\n// 外显buff 类型转换\nconst BUFF_SHOW_TYPE_TRAN = {\n    [BuffType.POISONING_CUR_HP]: BuffType.POISONING_MAX_HP,\n    [BuffType.INFECTION_PLAGUE]: BuffType.POISONING_MAX_HP,\n    [BuffType.DAMAGE_REDUCE]: BuffType.DESTROY_WEAPONS,\n    [BuffType.WORTHY_MONARCH]: BuffType.INSPIRE,\n    [BuffType.GOD_WAR]: BuffType.INSPIRE,\n    [BuffType.TIGER_MANIA]: BuffType.INSPIRE,\n    [BuffType.IRREMOVABILITY]: BuffType.TIMIDITY,\n    [BuffType.OVERLORD]: BuffType.TIMIDITY,\n}\n\n// 护盾buff\nconst SHIELD_BUFF = {\n    [BuffType.SHIELD]: true,\n    [BuffType.PROTECTION_SHIELD]: true,\n    [BuffType.LOW_HP_SHIELD]: true,\n    [BuffType.ATTACK_SHIELD]: true,\n    [BuffType.SUCKBLOOD_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD]: true,\n    [BuffType.RODELERO_SHIELD_001]: true,\n    [BuffType.RODELERO_SHIELD_102]: true,\n    [BuffType.BATTLE_BEGIN_SHIELD]: true,\n    [BuffType.KUROU_SHIELD]: true,\n    [BuffType.SUCK_SHIELD]: true,\n    [BuffType.ABNEGATION_SHIELD]: true,\n    [BuffType.LONGITUDINAL_CLEFT_SHIELD]: true,\n    [BuffType.CRIMSONGOLD_SHIELD]: true,\n    [BuffType.BLACK_IRON_STAFF_SHIELD]: true,\n}\n\n// 战斗特效类型\n// 10000002.攻击 10000003.闪避 10000004.减伤 10000005.护盾\nconst BATTLE_EFFECT_TYPE = {\n    ATTACK: [10000002],\n    DAMAGE_REDUCTION: [10000004],\n    SHIELD: [10000005],\n    VALOR: [10000002, 10000003],\n    WISDOM_COURAGE: [10000002, 10000004],\n    KUROU: [10000002, 10000005],\n    SAND_CLOCK: [10000006],\n    TONDEN: [114001],\n}\n\n// 聊天弹幕颜色\nconst CHAT_BARRAGE_COLOR = {\n    0: '#FFFFFF', //世界\n    1: '#5BB8FF', //联盟\n    2: '#FF81F7', //私聊\n}\n\n// 和大自然交换资源手续费\nconst REPLACEMENT_SERVICE_CHARGE = 60\n// 和大自然交换最少资源\nconst REPLACEMENT_MIN_RES_COUNT = 100\n// 每日置换次数\nconst REPLACEMENT_TODAY_COUNT_MAP = {\n    0: 3,\n    1: 3,\n    2: 3,\n}\n// 各个资源占用运送的容量\nconst RES_TRANSIT_CAP = {\n    [CType.CEREAL]: 1,\n    [CType.TIMBER]: 1,\n    [CType.STONE]: 1,\n    [CType.EXP_BOOK]: 100,\n    [CType.IRON]: 100,\n    [CType.UP_SCROLL]: 500,\n    [CType.FIXATOR]: 500,\n}\n\n// 加速招募倍数\nconst UP_RECRUIT_PAWN_MUL = 0.125\n\n// 多语言名字\nconst LANGUAGE_TEXT_LIST = [\n    { lang: 'en', text: 'ENGLISH' },\n    { lang: 'cn', text: '简体中文' },\n    { lang: 'hk', text: '繁體(港澳)' },\n    { lang: 'tw', text: '繁體(臺灣)' },\n    { lang: 'jp', text: '日本語' },\n    { lang: 'kr', text: '한국어' },\n    { lang: 'idl', text: 'Bahasa Indonesia' }, //印尼\n    { lang: 'th', text: 'ภาษาไทย' }, //泰语\n    { lang: 'vi', text: 'Tiếng Việt' }, //越南\n]\n\n// 注销账号等待时间\nconst LOGOUT_MAX_DAY = 86400000 * 7\n\n// 联盟职位说明\nconst ALLI_JOB_DESC = {\n    0: [1, 2, 3, 4, 5, 7, 8], //盟主\n    1: [3, 4, 5, 7], //副盟主\n    2: [6, 8], //军师\n    10: [0], //成员\n}\n\n// 职位数量\nconst ALLI_JOB_COUNT = {\n    0: 1, //盟主\n    1: 1, //副盟主\n    2: 2, //军师\n    10: 40,\n}\n\n// 开服多久内不可攻占\nconst NOT_OCCUPY_BY_SERVER_RUNTIME = 86400000 * 3\nconst NOT_OCCUPY_BY_MAX_LAND_COUNT = 100\n\n// 不同类型的区最多可玩几个区\nconst CONCURRENT_GAME_LIMIT = 1\n\n// 领地积分配置\nconst LAND_SCORE_CONF = {\n    1: [[50, 2], [100, 1]],\n    2: [[40, 4], [80, 2]],\n    3: [[30, 6], [60, 3]],\n    4: [[20, 8], [40, 4]],\n    5: [[10, 10], [20, 5]],\n}\n\n// 多久才可以删除私聊\nconst REMOVE_PCHAT_TIME = 3600000 * 12\n\n// 攻占玩家领地要求最低距离\nconst OCCUPY_PLAYER_CELL_MIN_DIS = 5\n\n// 多少地可以无限制私聊\nconst NOLIMIT_PCHAT_MAX_LAND = 150\n\n// 聊天 显示时间的最大间隔\nconst SHOW_TIME_MAX_INTERVAL = 60000 * 1\n\n// 可申请添加好友最小地块数\nconst FRIENDS_MIN_LAND_COUNT = 100\n\n// 战斗预测费用\nconst BATTLE_FORECAST_COST = 30\n\n// 战斗预测免费次数\nconst BATTLE_FORECAST_FREE_COUNT = 5\n\n// 一键打开宝箱要求\nconst OPEN_ALL_TREASURE_MIN_LAND_COUNT = 100\n\n// 最小可修改的行军速度\nconst CAN_MIN_MARCH_SPEED = 46\n\n// 一个季节持续的时间\nconst SEASON_DURATION_TIME = 3 * 86400000\n\n// 遗迹加速资源倍数\nconst ANCIENT_SUP_COST_MUL = 40\n// 遗迹加速时间\nconst ANCIENT_SUP_TIME = 6 * 60000\n\n// 聊天相关\nconst CHAT_MAX_COUNT: number = 50\nconst CHAT_SEND_INTERVAL: number = 6000 //发送聊天的预期间隔 (毫秒)\nconst CHAT_TOLERATE_MAX_COUNT: number = 3 //最多容忍多少次在间隔内发送\nconst CHAT_REST_MAX_TIME: number = 30000 //如果太频繁就休息一下\nconst CHAT_BANNED_REST_MAX_TIME: number = 60000 * 10 //禁言休息时间\n\n// 发送喇叭费用\nconst SEND_TRUMPET_COST = 50\nconst SEND_TRUMPET_ACC_COST = 25\n\n// 多长时间可以取消报名\nconst SERVER_APPLY_CANCEL_CD = 1 * 60 * 1000\n// 下次报名的等待时间\nconst NEXT_APPLY_CD = 6 * 1000\n\n// 点将一次的费用\nconst POINTSETS_ONE_COST = 10\n// 点击5次 金币费用\nconst POINTSETS_ONE_GOLD_COST = 598\n\n// 残卷合成画像 需要数量\nconst PORTRAYAL_COMP_NEED_COUNT = 3\n// 还原画像费用\nconst RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50\nconst RESTORE_PORTRAYAL_GOLD_COST = 598\n\n// 购买自选英雄费用 元宝\nconst BUY_OPT_HERO_COST = 999\n\n// 英雄自选礼包\nconst HERO_OPT_GIFT = {\n    // 陈到, 徐盛, 张辽\n    1: [310101, 320101, 340101],\n    // 陈到, 李嗣业, 徐盛, 黄盖, 王异, 张辽, 徐晃\n    2: [310101, 310401, 320101, 320401, 330301, 340101, 340601],\n    // 3: 全自选\n    // 陈到, 张郃, 李嗣业, 文鸯, 徐盛, 曹仁, 张飞, 黄盖, 刘宠, 王异, 曹休, 张辽, 许诸, 夏侯渊, 徐晃\n    4: [310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601],\n}\n\n// 英雄复活时间\nconst HERO_REVIVES_TIME = 3600000 * 5\n\n// 英雄槽位等级开启条件\nconst HERO_SLOT_LV_COND = [1, 10, 20]\n\n// 养由基召唤时的对应等级\nconst SUMMON_LV = {\n    1: 1,\n    2: 2,\n    3: 4,\n    4: 6,\n    5: 8,\n    6: 10,\n}\n\n// 默认的宠物id\nconst DEFAULT_PET_ID = 4101\n// 矛\nconst SPEAR_PAWN_ID = 3701\n// 火\nconst FIRE_PAWN_ID = 3702\n\n// 资源\nconst RES_MAP = {\n    [CType.CEREAL]: true,\n    [CType.TIMBER]: true,\n    [CType.STONE]: true,\n    [CType.BASE_RES]: true,\n    [CType.EXP_BOOK]: true,\n    [CType.IRON]: true,\n    [CType.UP_SCROLL]: true,\n    [CType.FIXATOR]: true,\n}\n\n// 最多可标记多少个\nconst MAX_MAP_MARK_COUNT = 10\n\n// 申请联盟个数限制\nconst ALLI_APPLY_MAX_COUNT = 3\n\n// 大厅模式对应的底部\nconst LOBBY_MODE_BUTTOM_NAME = {\n    [LobbyModeType.FREE]: 'team',\n    [LobbyModeType.NEWBIE]: 'team',\n    [LobbyModeType.RANKED]: 'team',\n    [LobbyModeType.SNAIL_ISLE]: 'twomiles',\n}\n\n// 斜度\nconst SKEW_ANGLE = 45\n// 斜着的外宽高\nconst SKEW_SIZE = cc.size(16, 8)\n// 斜着的内宽高 32 16\nconst SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5)\n\n\n// 段位商城的兵符配置\nconst RANK_SHOP_WAR_TOKEN_CONFIG = [\n    { warToken: 10, coin: 10 },\n    { warToken: 100, coin: 100 },\n    { warToken: 1000, coin: 1000 },\n    { warToken: 10000, coin: 10000 },\n]\n\n// 战斗的血条颜色\nconst BATTLE_HPBAR_COLOR = {\n    m: { bar: '#8BE273', bg: '#162D20' },\n    f: { bar: '#6DB5E2', bg: '#121D3A' },\n    0: { bar: '#EE2A4A', bg: '#3B1316' },\n    1: { bar: '#FF64B8', bg: '#41142C' },\n    2: { bar: '#AD64FF', bg: '#281240' },\n    3: { bar: '#FF9648', bg: '#4A2B14' },\n}\n\n// 战斗的火焰颜色\nconst BATTLE_FIRE_COLOR = {\n    m: '#53B977',\n    f: '#40A4E9',\n    0: '#B90900',\n    1: '#FF76F7',\n    2: '#AD64FF',\n    3: '#FFA836',\n}\n\n// 战令价格配置\nconst RECHARGE_BATTLE_PASS = 'jwm_up_book' // $8.99\n\n// 战令经验购买配置\nconst RECHARGE_BATTLE_PASS_EXP = [50, 100] // 50元宝购买100经验\n\n// 有奖问卷调查id\nconst PRIZE_QUESTION_ID = 99900001\n\n// 有奖问卷调查期限\nconst PRIZE_QUESTION_TIME = ['2024-12-26-06-00', '2024-12-30-06-00']\n\n// 打开允许通知弹窗的公共CD\nconst NOTICE_PERMISSION_CD = 24 * 60 * 60 * 1000 // 24小时\n\n// 每日屯田次数\nconst TODAY_TONDEN_MAX_COUNT = 10\n// 屯田奖励点消耗倍数\nconst TONDEN_STAMINA_MUL = 3\n\n// 医馆伤兵上限\nconst HOSPITAL_PAWN_LIMIT = 200\n\n// 各等级士兵战败后回馆概率(百分比)\nconst GO_HOSPITAL_CHANCE = {\n    1: 10,\n    2: 20,\n    3: 30,\n    4: 40,\n    5: 50,\n    6: 60,\n}\n\n// 画像的天选几率\nconst PORTRAYAL_CHOSENONE_ODDS = 0.005\n\n// 研究类型转评论类型\nconst STUDY_TO_BOOKTYPE = {\n    [StudyType.POLICY]: BookCommentType.POLICY,\n    [StudyType.PAWN]: BookCommentType.PAWN,\n    [StudyType.EQUIP]: BookCommentType.EQUIP,\n    [StudyType.EXCLUSIVE]: BookCommentType.EQUIP,\n}\n\n// 盟主投票最大次数\nconst ALLI_LEADER_VOTE_MAX_COUNT = 4\n\n// 招募动态资源每级系数\nconst PAWN_COST_LV_LIST = [1, 2, 4, 6, 8, 10]\n\n// 工厂解锁配置\nconst FACTORY_SLOT_CONF = [5]\n\n// 摄像机背景颜色\nconst CAMERA_BG_COLOR = ['#ACC961', '#88CA6E', '#E4B765', '#A7E2E3']\n\n//\nconst MAP_MASK_ITEM_COLOR = ['#2B8A85', '#1755AC', '#832E4F', '#8378C2']\nconst MAP_MASK_ITEM_OPACITY = [63, 42, 38, 63]\nconst MAP_MASK_ITEM_OPACITY_GOLD = [45, 21, 20, 32]\n\n// 区域内的地面颜色\nconst AREA_DI_COLOR_CONF = [\n    // 春\n    {\n        0: { bg: '#D8C069', battle: ['#ECE5A2', '#E0DA94'], build: '#D7BD65' }, //荒地\n        3: { bg: '#AFC864', battle: ['#F4E88D', '#F0DC84'], build: '#D6BD67' }, //粮食\n        4: { bg: '#AFC864', battle: ['#DCEC95', '#D0E186'], build: '#AFC864' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#AFC864', battle: ['#DCDF7E', '#CCD775'], build: '#EBE38F' }, //主城\n    },\n    // 夏\n    {\n        0: { bg: '#D8C069', battle: ['#E8E6AC', '#DEDB9F'], build: '#D8C069' }, //荒地\n        3: { bg: '#88CB6E', battle: ['#F4E88D', '#F0DC84'], build: '#D4BD6A' }, //粮食\n        4: { bg: '#88CA6E', battle: ['#DCEC95', '#D0E186'], build: '#86C96D' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#88CA6E', battle: ['#D7DF85', '#C7D77C'], build: '#EAE195' }, //主城\n    },\n    // 秋\n    {\n        0: { bg: '#D8C069', battle: ['#EBDD8F', '#E5CF7B'], build: '#EDE399' }, //荒地\n        3: { bg: '#E1B668', battle: ['#F4E88D', '#F0DC84'], build: '#E1B567' }, //粮食\n        4: { bg: '#E1B668', battle: ['#DCEC95', '#D0E186'], build: '#DAC26A' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#C3C89B' }, //石头\n        10: { bg: '#E1B668', battle: ['#EBDD8F', '#E5CF7B'], build: '#EDE399' }, //主城\n    },\n    // 冬\n    {\n        0: { bg: '#C5ECE8', battle: ['#E8E6AC', '#DEDB9F'], build: '#D8BF67' }, //荒地\n        3: { bg: '#A6DFE0', battle: ['#F4E88D', '#F0DC84'], build: '#A6DFE0' }, //粮食\n        4: { bg: '#A6DFE0', battle: ['#DCEC95', '#D0E186'], build: '#A6DFE0' }, //木头\n        5: { bg: '#D8C069', battle: ['#E0E9B7', '#D7E0B1'], build: '#D8C069' }, //石头\n        10: { bg: '#A6DFE0', battle: ['#C7ECED', '#B4E0E4'], build: '#D9EEEC' }, //主城\n    },\n]\n\n// 难度底板颜色\nconst DIFFICULTY_BG_COLOR = {\n    1: '#81A514',\n    2: '#6683AB',\n    3: '#9C58BF',\n    4: '#CE59A0',\n    5: '#C34B3F',\n}\n\n// 月卡固定值\nconst MONTH_CARD = [\n    {\n        TYPE: MonthlyCardType.SALE, // 特惠月卡\n        FIRST: 100, // 首冲奖励元宝\n        DURATION: 31, // 时长\n        DAY: 30,    // 每日奖励金币\n        EXTRA: 0,   // 额外奖励兵符\n        PRODUCT_IDS_ANDROID: ['jwm-card-month', 'jwm-card-quarter'], // 月卡，季卡\n        PRODUCT_IDS_IOS: ['jwmCardMonth', 'jwmCardQuarter'],  // 月卡，季卡\n        RECHARGES: ['jwm_card_once'], // 一次性商品\n        RESTORES: ['jwm_card', 'jwmCard'], // 恢复订阅区分字段\n    },\n    {\n        TYPE: MonthlyCardType.SUPER, // 超级月卡\n        FIRST: 400, // 首冲奖励元宝\n        DURATION: 31, // 时长\n        DAY: 80,    // 每日奖励金币\n        EXTRA: 5,   // 额外奖励兵符\n        PRODUCT_IDS_ANDROID: ['jwm-ad-free-month', 'jwm-ad-free-quarter'], // 月卡，季卡\n        PRODUCT_IDS_IOS: ['jwmAdFreeMonth', 'jwmSuperCardQuarter'], // 月卡，季卡\n        RECHARGES: ['jwm_super_card_once'], // 一次性商品\n        RESTORES: ['jwm_ad_free', 'jwmAdFree', 'jwmSuperCard'], // 恢复订阅区分字段\n    },\n]\n\n// 需要返回的免费\nconst RETURNED_FREE_TYPE = {\n    [CType.UP_RECRUIT]: true,\n    [CType.FREE_RECRUIT]: true,\n    [CType.FREE_LEVING]: true,\n    [CType.FREE_CURE]: true,\n}\n\n// 成就颜色\nconst ACHIEVEMENT_COLOR = {\n    1: '#7A6364',\n    2: '#559743',\n    3: '#2FA895',\n    4: '#D2772E',\n    5: '#5A6CD5',\n    6: '#C252EC',\n    7: '#D93149',\n}\n\n\n// 每个等级治疗士兵消耗粮食所占百分比 k=>lv v=>百分比\nconst CURE_RES_PARAM_MAP = {\n    1: 0.2,\n    2: 0.4,\n    3: 0.5,\n    4: 0.6,\n    5: 0.7,\n    6: 0.8,\n}\n\n// 每个等级治疗士兵消耗时间所占百分比 k=>lv v=>百分比\nconst CURE_TIME_PARAM_MAP = {\n    1: 0.2,\n    2: 0.4,\n    3: 0.6,\n    4: 0.8,\n    5: 0.9,\n    6: 1,\n}\n\n// 系数基础值\nconst PAWN_COST_FACTOR_BASE_VALUE = 250\n\nexport {\n    CLICK_SPACE,\n    MAP_SHOW_OFFSET,\n    TILE_SIZE,\n    TILE_SIZE_HALF,\n    MAP_EXTRA_SIZE,\n    BUILD_DRAG_OFFSETY,\n    AREA_MAX_ZINDEX,\n    MAX_ZINDEX,\n    LONG_PRESS_TIME,\n    DELAY_CLOSE_PNL_TIME,\n    CITY_MAIN_NID,\n    BUILD_FLAG_NID,\n    CITY_FORT_NID,\n    ANCIENT_WALL_ID,\n    CITY_CHANGAN_ID,\n    CITY_JINLING_ID,\n    CITY_YANJING_ID,\n    CITY_LUOYANG_ID,\n    BUILD_FARM_ID,\n    BUILD_TIMBER_ID,\n    BUILD_QUARRY_ID,\n    BUILD_WALL_NID,\n    BUILD_MAIN_NID,\n    BUILD_GRANARY_NID,\n    BUILD_WAREHOUSE_NID,\n    BUILD_BARRACKS_NID,\n    BUILD_EMBASSY_NID,\n    BUILD_BAZAAR_NID,\n    BUILD_SMITHY_NID,\n    BUILD_DRILLGROUND_NID,\n    BUILD_PLANT_NID,\n    BUILD_ALLI_BAZAAR_NID,\n    BUILD_HEROHALL_NID,\n    BUILD_HOSPITAL_NID,\n    BUILD_FORT_NID,\n    BUILD_TOWER_NID,\n    PAWN_CROSSBOW_ID,\n    AX_CAVALRY_ID,\n    INIT_RES_CAP,\n    INIT_RES_COUNT,\n    INIT_RES_OUTPUT,\n    DEFAULT_BT_QUEUE_COUNT,\n    IN_DONE_BT_GOLD,\n    IN_DONE_FORGE_GOLD,\n    MODIFY_NICKNAME_GOLD,\n    ARMY_PAWN_MAX_COUNT,\n    CREATE_ALLI_MAX_LV,\n    DEFAULT_CITY_SIZE,\n    DEFAULT_AREA_SIZE,\n    DEFAULT_BUILD_SIZE,\n    BOSS_BUILD_SIZE,\n    DEFAULT_MAX_ARMY_COUNT,\n    DEFAULT_MAX_ADD_PAWN_TIMES,\n    UP_MARCH_SPEED_MUL,\n    MAIN_CITY_MARCH_SPEED,\n    TRANSIT_TIME,\n    BATTLE_MAX_TIME,\n    LAND_DI_CONF,\n    DECORATION_MUD_CONF,\n    DECORATION_MUD_OUTER_CONF,\n    BORDER_LINE_CONF,\n    RIVER_LINE_CONF,\n    SELECT_CELL_INFO_BOX,\n    CELL_RES_FIELDS,\n    PAWN_BUBBLE_OFFSETY,\n    RES_FIELDS_CTYPE,\n    CTYPE_ICON_URL,\n    CTYPE_ICON,\n    CTYPE_NAME,\n    BUILD_EFFECT_TYPE_CONF,\n    MARCH_ARMY_NAME_COLOR,\n    MARCH_ARMY_TIME_COLOR,\n    ARMY_STATE_COLOR,\n    MAIL_STATE_COLOR,\n    ARMY_RECORD_DESC_CONF,\n    PLAYBACK_MULS,\n    FIXATION_MENU_CONFIG,\n    FIXATION_MENU_MAX_COUNT,\n    FREE_HEAD_ICONS,\n    ADD_OUTPUT_GOLD,\n    ADD_OUTPUT_RATIO,\n    ADD_OUTPUT_TIME,\n    POLICY_SLOT_CONF,\n    EQUIP_SLOT_CONF,\n    EQUIP_SLOT_EXCLUSIVE_LV,\n    EQUIP_SMELT_NEED_LV,\n    PAWN_SLOT_CONF,\n    RESET_STUDY_SLOT_GOLD,\n    CAN_EXIT_ALLI_TIME,\n    CREATE_ALLI_COST,\n    CREATE_ALLI_COND,\n    ONE_USER_POPULARITY_CHANGE_INTERVAL,\n    BUFF_SHOW_TYPE_TRAN,\n    SHIELD_BUFF,\n    BATTLE_EFFECT_TYPE,\n    BUFF_NODE_ZINDEX,\n    NEED_SHOW_BUFF,\n    NEED_MUTUAL_BUFF,\n    CHAT_BARRAGE_COLOR,\n    REPLACEMENT_SERVICE_CHARGE,\n    REPLACEMENT_MIN_RES_COUNT,\n    REPLACEMENT_TODAY_COUNT_MAP,\n    RES_TRANSIT_CAP,\n    UP_RECRUIT_PAWN_MUL,\n    LANGUAGE_TEXT_LIST,\n    LOGOUT_MAX_DAY,\n    ALLI_JOB_DESC,\n    ALLI_JOB_COUNT,\n    NOT_OCCUPY_BY_SERVER_RUNTIME,\n    NOT_OCCUPY_BY_MAX_LAND_COUNT,\n    CONCURRENT_GAME_LIMIT,\n    LAND_SCORE_CONF,\n    REMOVE_PCHAT_TIME,\n    OCCUPY_PLAYER_CELL_MIN_DIS,\n    NOLIMIT_PCHAT_MAX_LAND,\n    SHOW_TIME_MAX_INTERVAL,\n    FRIENDS_MIN_LAND_COUNT,\n    BATTLE_FORECAST_COST,\n    BATTLE_FORECAST_FREE_COUNT,\n    OPEN_ALL_TREASURE_MIN_LAND_COUNT,\n    CAN_MIN_MARCH_SPEED,\n    SEASON_DURATION_TIME,\n    ANCIENT_SUP_COST_MUL,\n    ANCIENT_SUP_TIME,\n    COLOR_NORMAL,\n    CHAT_MAX_COUNT,\n    CHAT_SEND_INTERVAL,\n    CHAT_TOLERATE_MAX_COUNT,\n    CHAT_REST_MAX_TIME,\n    CHAT_BANNED_REST_MAX_TIME,\n    SEND_TRUMPET_COST,\n    SEND_TRUMPET_ACC_COST,\n    SERVER_APPLY_CANCEL_CD,\n    NEXT_APPLY_CD,\n    POINTSETS_ONE_COST,\n    POINTSETS_ONE_GOLD_COST,\n    PORTRAYAL_COMP_NEED_COUNT,\n    RESTORE_PORTRAYAL_WAR_TOKEN_COST,\n    RESTORE_PORTRAYAL_GOLD_COST,\n    BUY_OPT_HERO_COST,\n    HERO_OPT_GIFT,\n    HERO_REVIVES_TIME,\n    HERO_SLOT_LV_COND,\n    SUMMON_LV,\n    DEFAULT_PET_ID,\n    SPEAR_PAWN_ID,\n    FIRE_PAWN_ID,\n    RES_MAP,\n    MAX_MAP_MARK_COUNT,\n    ALLI_APPLY_MAX_COUNT,\n    LOBBY_MODE_BUTTOM_NAME,\n    SKEW_ANGLE,\n    SKEW_SIZE,\n    SKEW_SIZE_HALF,\n    RANK_SHOP_WAR_TOKEN_CONFIG,\n    BATTLE_HPBAR_COLOR,\n    BATTLE_FIRE_COLOR,\n    RECHARGE_BATTLE_PASS,\n    RECHARGE_BATTLE_PASS_EXP,\n    PRIZE_QUESTION_ID,\n    PRIZE_QUESTION_TIME,\n    NOTICE_PERMISSION_CD,\n    TODAY_TONDEN_MAX_COUNT,\n    TONDEN_STAMINA_MUL,\n    HOSPITAL_PAWN_LIMIT,\n    GO_HOSPITAL_CHANCE,\n    PORTRAYAL_CHOSENONE_ODDS,\n    STUDY_TO_BOOKTYPE,\n    ALLI_LEADER_VOTE_MAX_COUNT,\n    PAWN_COST_LV_LIST,\n    FACTORY_SLOT_CONF,\n    CAMERA_BG_COLOR,\n    MAP_MASK_ITEM_COLOR,\n    MAP_MASK_ITEM_OPACITY,\n    MAP_MASK_ITEM_OPACITY_GOLD,\n    AREA_DI_COLOR_CONF,\n    DIFFICULTY_BG_COLOR,\n    MONTH_CARD,\n    RETURNED_FREE_TYPE,\n    ACHIEVEMENT_COLOR,\n    CURE_RES_PARAM_MAP,\n    CURE_TIME_PARAM_MAP,\n    PAWN_COST_FACTOR_BASE_VALUE,\n}"]}