
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/LongPressLikeCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '42703clG/dGGIT39/OLErTb', 'LongPressLikeCmpt');
// app/script/view/lobby/LongPressLikeCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var SIConstant_1 = require("../../model/snailisle/SIConstant");
var ITouchCmpt_1 = require("../cmpt/ITouchCmpt");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 长按点赞
 */
var LongPressLikeCmpt = /** @class */ (function (_super) {
    __extends(LongPressLikeCmpt, _super);
    function LongPressLikeCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.frameCtrl = null;
        _this.touchTime = 0; //触摸时间 用于长按
        _this.frameIntervalTime = 0.04;
        _this.frameIndex = -1;
        _this.frameCount = 0;
        _this.touchId = -1;
        return _this;
    }
    LongPressLikeCmpt.prototype.init = function () {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        this.frameCtrl = this.FindChild('val', cc.MultiFrame);
        this.frameCount = this.frameCtrl.frameCount();
        this.node.SetSwallowTouches(false); //默认开启穿透
        this.frameIntervalTime = SIConstant_1.LONG_PRESS_LIKE_TIME / this.frameCount;
        this.touchTime = 0;
        return this;
    };
    // 触摸开始
    LongPressLikeCmpt.prototype.onTouchStart = function (event) {
        if (!this.interactable || this.touchId !== -1) {
            return;
        }
        this.touchId = event.getID();
        this.touchTime = SIConstant_1.LONG_PRESS_LIKE_TIME; //如果没有被选中 那么重制点击时间
        eventCenter.emit(EventType_1.default.SHOW_SNAILISLE_DESC, false);
        audioMgr.playSFX('lobby/sound_ui_024', { tag: 'long_press_like' });
    };
    LongPressLikeCmpt.prototype.onTouchMove = function (event) {
        if (this.touchId !== event.getID()) {
            return;
        }
        else if (!this.node._hitTest(event.getLocation())) {
            this.cancel();
        }
    };
    LongPressLikeCmpt.prototype.onTouchEnd = function (event) {
        if (this.touchId !== event.getID() || !this.interactable) {
            return;
        }
        this.touchId = -1;
        this.cancel();
    };
    LongPressLikeCmpt.prototype.cancel = function () {
        audioMgr.stopSFX('lobby/sound_ui_024', 'long_press_like');
        this.touchId = -1;
        this.touchTime = 0;
        this.frameIndex = -1;
        this.frameCtrl.setFrame(0);
        eventCenter.emit(EventType_1.default.SHOW_SNAILISLE_DESC, true);
    };
    LongPressLikeCmpt.prototype.update = function (dt) {
        if (!this.interactable || this.touchTime <= 0) {
            return;
        }
        this.touchTime -= dt;
        if (this.touchTime <= 0) {
            GameHelper_1.gameHpr.lobby.likeJwm();
            return this.cancel();
        }
        var index = Math.floor((SIConstant_1.LONG_PRESS_LIKE_TIME - this.touchTime) / this.frameIntervalTime);
        if (index < 0 || this.frameIndex === index) {
            return;
        }
        this.frameIndex = index;
        if (index < this.frameCount) {
            this.frameCtrl.setFrame(index);
        }
    };
    LongPressLikeCmpt = __decorate([
        ccclass
    ], LongPressLikeCmpt);
    return LongPressLikeCmpt;
}(ITouchCmpt_1.default));
exports.default = LongPressLikeCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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