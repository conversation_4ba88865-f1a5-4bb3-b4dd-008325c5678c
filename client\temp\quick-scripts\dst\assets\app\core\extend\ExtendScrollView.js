
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendScrollView.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ccd865NbQ1ADY4HhxKJM0PV', 'ExtendScrollView');
// app/core/extend/ExtendScrollView.ts

/**
 * cc.ScrollView 扩展方法
 */
// 填充列表
cc.ScrollView.prototype.Items = function (list, prefab, cb, target) {
    var i = 0, childs = this.content.children, item = childs[0];
    var count = 0;
    if (typeof (list) === 'number') {
        count = list;
        list = null;
    }
    else {
        count = list.length;
    }
    if (typeof (prefab) === 'function') {
        target = cb;
        cb = prefab;
    }
    else if (prefab instanceof cc.Node || prefab instanceof cc.Prefab) {
        item = prefab;
    }
    if (!item) {
        return logger.error('必须满足content中有一个可拷贝的节点');
    }
    var plus = this.Component(cc.ScrollViewPlus);
    plus === null || plus === void 0 ? void 0 : plus.reset();
    if (plus === null || plus === void 0 ? void 0 : plus.isFrameRender) {
        return plus.items(list, item, cb, target);
    }
    for (var l = this.content.childrenCount; i < l; i++) {
        var it = childs[i];
        if (i < count) {
            setItemData(it, list && list[i], i, cb, target);
        }
        else {
            it.Data = null;
            it.active = false;
        }
    }
    for (; i < count; i++) {
        setItemData(cc.instantiate2(item, this.content), list && list[i], i, cb, target);
    }
    // 刷新一下显示
    plus === null || plus === void 0 ? void 0 : plus.updateNodeShow();
};
// 添加一个
cc.ScrollView.prototype.AddItem = function (cb, target) {
    var i = this.content.children.findIndex(function (m) { return !m.active; });
    var it = null;
    if (i !== -1) {
        it = this.content.children[i];
    }
    else {
        i = this.content.childrenCount;
        it = cc.instantiate2(this.content.children[0], this.content);
    }
    setItemData(it, i, undefined, cb, target);
};
function setItemData(it, data, i, cb, target) {
    it.active = true;
    it.opacity = 255;
    if (!cb) {
        return;
    }
    else if (target) {
        cb.call(target, it, data, i);
    }
    else {
        cb(it, data, i);
    }
}
// 查找content的子节点
cc.ScrollView.prototype.Find = function (predicate, thisArg) {
    return this.content.children.find(predicate, thisArg);
};
//
cc.ScrollView.prototype.IsEmpty = function () {
    return !this.content.children.some(function (m) { return m.active; });
};
// list填充
cc.ScrollView.prototype.List = function (len, cb, target) {
    var ex = this.Component(cc.ScrollViewEx);
    if (ex) {
        return ex.list(len, cb, target);
    }
    else {
        logger.error('List error, not ScrollViewEx!');
    }
};
//
cc.ScrollView.prototype.GetItemNode = function () {
    var _a;
    return ((_a = this.Component(cc.ScrollViewEx)) === null || _a === void 0 ? void 0 : _a.getItemNode()) || this.content.children[0];
};
// 将选中的移动到中间
cc.ScrollView.prototype.SelectItemToCentre = function (index) {
    this.stopAutoScroll();
    if (index !== -1) {
        var lay = this.content.Component(cc.Layout);
        lay.updateLayout();
        var width = this.content.children[0].width;
        var tx = (width + lay.spacingX) * index + width * 0.5 + lay.paddingLeft; //当前位置
        var pw = this.content.parent.width;
        var cx = pw * 0.5; //中间位置
        this.content.x = cc.misc.clampf(cx - tx, Math.min(0, pw - this.content.width), 0);
    }
    else {
        this.scrollToLeft();
    }
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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