
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/BuildUnlockTipCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bf7d9NZtqlDQq/u7CN4FYN6', 'BuildUnlockTipCmpt');
// app/script/view/cmpt/BuildUnlockTipCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 适配文本和线(两端文本/节点 中间线)
 */
var BuildUnlockTipCmpt = /** @class */ (function (_super) {
    __extends(BuildUnlockTipCmpt, _super);
    function BuildUnlockTipCmpt() {
        var _a, _b;
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ICON_CONF = (_a = {},
            _a[Enums_1.BUILD_NID.MAIN] = 'unlock_tip/tb_jzsj_003',
            _a[Enums_1.BUILD_NID.BARRACKS] = 'unlock_tip/tb_jzsj_002',
            _a[Enums_1.BUILD_NID.SMITHY] = 'unlock_tip/tb_jzsj_006',
            _a[Enums_1.BUILD_NID.PLANT] = 'role/3502/role_3502',
            _a[Enums_1.BUILD_NID.HERO_HALL] = 'unlock_tip/tb_jzsj_005',
            _a[Enums_1.BUILD_NID.ALLI_BAZAAR] = 'unlock_tip/tb_jzsj_004',
            _a[Enums_1.BUILD_NID.FREE_BAZAAR] = 'unlock_tip/tb_jzsj_004',
            _a);
        _this.NAME_CONF = (_b = {},
            _b[Enums_1.BUILD_NID.MAIN] = 'ui.ceri_type_name_1',
            _b[Enums_1.BUILD_NID.BARRACKS] = 'ui.ceri_type_name_2',
            _b[Enums_1.BUILD_NID.SMITHY] = 'ui.ceri_type_name_3',
            _b[Enums_1.BUILD_NID.PLANT] = 'pawnText.name_3502',
            _b[Enums_1.BUILD_NID.HERO_HALL] = 'ui.button_portrayal',
            _b[Enums_1.BUILD_NID.ALLI_BAZAAR] = 'ui.msg_build_effect_free_bazaar',
            _b[Enums_1.BUILD_NID.FREE_BAZAAR] = 'ui.msg_build_effect_free_bazaar',
            _b);
        return _this;
    }
    // 显示当前等级后最新的解锁内容
    // 当前等级与有新的解锁内容等级相差一级时高亮，其他情况置灰
    BuildUnlockTipCmpt.prototype.updateInfo = function (data, conf, key, lv) {
        var nextLv = conf.find(function (m) { return m > (lv !== null && lv !== void 0 ? lv : data.lv); });
        if (this.node.active = !!nextLv) {
            var hightLight = nextLv - data.lv === 1;
            var bgColor = hightLight ? '#BCD931' : '#CECCC8';
            var contentNode_ = this.node.Child('content');
            contentNode_.Color(bgColor);
            this.node.Child('arrow').Color(bgColor);
            var icon = contentNode_.Child('icon', cc.Sprite);
            var isPlant = data.id === Enums_1.BUILD_NID.PLANT;
            icon.node.setScale(isPlant ? 0.7 : 1);
            icon.node.opacity = (!hightLight && isPlant) ? 255 * 0.7 : 255;
            icon.setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(hightLight));
            ResHelper_1.resHelper.loadIcon(this.ICON_CONF[data.id], icon, key);
            var desc = contentNode_.Child('val', cc.Label);
            desc.fontSize = hightLight ? 22 : 24;
            desc.Color(hightLight ? '#FFFBF2' : '#807E7A');
            var labelKey = data.id === Enums_1.BUILD_NID.HERO_HALL ? 'ui.lv_unlock_new_hero' : isPlant ? 'ui.lv_unlock' : 'ui.lv_unlock_new';
            var params = data.id === Enums_1.BUILD_NID.HERO_HALL ? [] : data.id === Enums_1.BUILD_NID.SMITHY ? (Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[nextLv] ? 'ui.ceri_type_name_4' : this.NAME_CONF[data.id]) : this.NAME_CONF[data.id];
            desc.setLocaleKey(labelKey, [assetsMgr.lang('ui.short_lv', nextLv), params]);
            desc.Component(cc.LocaleLabel).setFont(hightLight ? 'f_b_out' : 'f_m');
            desc.Component(cc.LocaleLabel).updateString();
            contentNode_.Component(cc.Layout).updateLayout();
        }
    };
    BuildUnlockTipCmpt = __decorate([
        ccclass
    ], BuildUnlockTipCmpt);
    return BuildUnlockTipCmpt;
}(cc.Component));
exports.default = BuildUnlockTipCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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