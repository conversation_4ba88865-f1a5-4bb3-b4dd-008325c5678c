{"version": 3, "sources": ["assets\\app\\script\\model\\guide\\NoviceServerModel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAAsX;AAEtX,qDAAsI;AACtI,0DAAoD;AACpD,6DAAwD;AACxD,2DAAyD;AACzD,yDAAuD;AACvD,2CAAqC;AACrC,2CAAqC;AACrC,6CAAuC;AACvC,2CAAqC;AAErC,+CAAyC;AACzC,+CAAyC;AACzC,6CAA0D;AAC1D,iDAA2C;AAC3C,qDAA+C;AAC/C,6CAAuC;AACvC,yDAAmD;AACnD,+CAA2vB;AAC3vB,2DAAqD;AACrD,mDAA6C;AAC7C,qDAA+C;AAC/C,iEAA2D;AAC3D,qDAA+C;AAE/C,2DAAqD;AACrD,yDAAmD;AACnD,yDAAmD;AACnD,mDAA6C;AAC7C,qDAA+C;AAC/C,iEAAqF;AAErF,qDAA+C;AAC/C,6DAA2D;AAE3D;;GAEG;AAEH;IAA+C,qCAAY;IAA3D;QAAA,qEAy2IC;QAv2IW,eAAS,GAAY,KAAK,CAAA;QAClC,OAAO;QACC,UAAI,GAAW,EAAE,CAAA;QACjB,oBAAc,GAAW,CAAC,CAAA;QAC1B,oBAAc,GAAW,CAAC,CAAA;QAC1B,YAAM,GAAoB,IAAI,yBAAe,EAAE,CAAA;QAC/C,YAAM,GAAoB,IAAI,yBAAe,EAAE,CAAA;QAC/C,WAAK,GAAoB,IAAI,yBAAe,EAAE,CAAA;QAC9C,aAAO,GAAW,CAAC,CAAA;QACnB,UAAI,GAAW,CAAC,CAAA;QAChB,cAAQ,GAAW,CAAC,CAAA;QACpB,aAAO,GAAW,CAAC,CAAA;QACnB,mBAAa,GAAW,CAAC,CAAA;QACzB,aAAO,GAAW,CAAC,CAAA;QACnB,UAAI,GAAW,CAAC,CAAA;QAChB,cAAQ,GAAW,CAAC,CAAA;QACpB,cAAQ,GAAqC,EAAE,CAAA;QAC/C,cAAQ,GAAkB,EAAE,CAAA;QAC5B,yBAAmB,GAAW,CAAC,CAAA;QAC/B,aAAO,GAAQ,EAAE,CAAA;QACjB,eAAS,GAAU,EAAE,CAAA;QACrB,gBAAU,GAAQ,EAAE,CAAA;QACpB,mBAAa,GAA2B,EAAE,CAAA;QAC1C,YAAM,GAAgB,EAAE,CAAA;QACxB,oBAAc,GAAQ,IAAI,CAAA;QAC1B,gBAAU,GAAuC,EAAE,CAAA,CAAA,QAAQ;QAC3D,kBAAY,GAA+B,EAAE,CAAA,CAAA,SAAS;QACtD,eAAS,GAAyC,EAAE,CAAA,CAAA,UAAU;QAC9D,iBAAW,GAAuC,EAAE,CAAA,CAAC,UAAU;QAC/D,gBAAU,GAA0C,EAAE,CAAA,CAAC,UAAU;QACjE,eAAS,GAAwB,EAAE,CAAA,CAAC,UAAU;QAC9C,gBAAU,GAAoB,EAAE,CAAA,CAAC,SAAS;QAC1C,0BAAoB,GAAW,CAAC,CAAA,CAAC,UAAU;QAC3C,kBAAY,GAA8B,EAAE,CAAA,CAAA,UAAU;QACtD,iBAAW,GAA2B,EAAE,CAAA,CAAC,SAAS;QAClD,iBAAW,GAA4B,EAAE,CAAA,CAAC,SAAS;QACnD,sBAAgB,GAA8B,EAAE,CAAA,CAAA,WAAW;QACnE,KAAK;QACG,WAAK,GAAoB,EAAE,CAAA;QAC3B,cAAQ,GAAQ,EAAE,CAAA;QAClB,YAAM,GAAqB,EAAE,CAAA,CAAC,QAAQ;QACtC,mBAAa,GAA8B,EAAE,CAAA;QAC7C,gBAAU,GAAgC,EAAE,CAAA;QAC7C,0BAAoB,GAA8B,EAAE,CAAA;QACnD,qBAAe,GAA+F,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAA;QACzI,qBAAe,GAA+D,EAAE,CAAA;QAChF,kBAAY,GAAsB,EAAE,CAAA;QACpC,mBAAa,GAAyD,EAAE,CAAA;QACzE,oBAAc,GAAmB,IAAI,wBAAc,EAAE,CAAA,CAAA,IAAI;QACxD,sBAAgB,GAAsB,EAAE,CAAA,CAAA,MAAM;QACrC,0BAAoB,GAAW,EAAE,CAAA,CAAA,UAAU;QACrD,yBAAmB,GAA+B,EAAE,CAAA,CAAA,UAAU;QAC9D,0BAAoB,GAAW,CAAC,CAAA,CAAA,SAAS;QACzC,qBAAe,GAAW,CAAC,CAAA,CAAA,SAAS;QACpC,mBAAa,GAAY,KAAK,CAAA,CAAA,gBAAgB;QAE7C,iBAAW,GAAW,CAAC,CAAA;QACvB,yBAAmB,GAAQ,IAAI,CAAA;QAE/B,cAAQ,GAAW,EAAE,CAAA;QACrB,eAAS,GAAW,EAAE,CAAA;QACtB,gBAAU,GAAqC,EAAE,CAAA;QAClD,sBAAgB,GAAW,CAAC,CAAC,CAAA;QAC7B,qBAAe,GAAW,CAAC,CAAC,CAAA;QAC5B,uBAAiB,GAAG,KAAK,CAAA;QACzB,uBAAiB,GAAQ,EAAE,CAAA,CAAC,iBAAiB;QAE5C,kBAAY,GAA2D,EAAE,CAAA;QAEzE,iBAAW,GAAY,KAAK,CAAA,CAAC,OAAO;QACpC,YAAM,GAAG,KAAK,CAAA;QACf,2BAAqB,GAAW,CAAC,CAAA,CAAA,QAAQ;QACxC,2BAAqB,GAA8B,EAAE,CAAA,CAAA,UAAU;QAgW/D,SAAG,GAAG,CAAC,CAAA;;IA+7HnB,CAAC;IA7xIU,oCAAQ,GAAf;IACA,CAAC;IAEM,gCAAI,GAAX,UAAY,GAAW,EAAE,OAAY;QAArC,iBAsLC;;QArLG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAA;QACf,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QACxC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAA;QAC9B,UAAU;QACV,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;QAC7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;QACzC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAA;QACvC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,EAAE,CAAA;QAC3C,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,mBAAS,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA1B,CAA0B,CAAC,CAAA;QACtE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;QACf,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAA;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QACzE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,wBAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAA9B,CAA8B,CAAC,CAAA;QAC1E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;QAC7C,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAA;QAC3D,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,qBAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAA;QAC3E,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,yBAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAA/B,CAA+B,CAAC,CAAA;QACvF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAA;QAC7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAA;QACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC,CAAA;QACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC,CAAA;QACpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAA;QACnD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;QAC3C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAA;QACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,EAAE,CAAA;QAC3C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAA;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrD,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,uBAAa,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAA9B,CAA8B,CAAC,CAAA;QAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAClD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,EAAE,CAAA;QAC3C,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,yBAAe,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAA/B,CAA+B,CAAC,CAAA;QAC/F,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAyB,CAAA;QAC/E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,4CAA6B,CAAA;QAC/F,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,4CAAoB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAArC,CAAqC,CAAC,CAAA;QAC3F,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,+BAAqB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAtC,CAAsC,CAAC,CAAA;QAC5F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,EAAE,CAAA;QACnD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,IAAI,EAAE,CAAA;QACzD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;QAC1D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,GAAG,CAAA;QAClD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,KAAK,CAAA;QAChD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE,CAAA;QAC7D,IAAM,KAAK,GAAG,EAAE,EAAE,IAAI,GAAG,8BAAe,CAAC,CAAC,GAAG,8BAAe,CAAC,CAAC,CAAA;QAC9D,IAAM,UAAU,GAAG,oBAAO,CAAC,UAAU,CAAA;QACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,EAAE;YACvC,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,OAAA,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;YACnE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAC3D,KAAK,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,CAAC;aACZ,CAAC,CAAA;YACF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAC/B,KAAK;YACL,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAA;gBACnB,IAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBACtD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;gBAChB,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;gBACjC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aAC3B;iBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;gBACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aAC3B;SACJ;QACD,QAAQ;QACR,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,oCAAqB,CAAC,CAAA;QAClD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YACjB,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAA;YACpB,QAAQ,CAAC,MAAM,GAAG,wBAAa,CAAA;YAC/B,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAA;YACpB,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,yBAAc,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YACjH,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,yBAAc,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YACnH,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAA;YACnB,QAAQ,CAAC,OAAO,CAAC;gBACb,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC;gBAChD,KAAK,EAAE,GAAG;gBACV,KAAK,EAAE;oBACH,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC7E,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;iBAChF;aACJ,CAAC,CAAA;YACF,QAAQ,CAAC,UAAU,EAAE,CAAA;YACrB,2CAA4B,CAAC,OAAO,CAAC,UAAA,CAAC;gBAClC,IAAM,IAAI,GAAG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAC1B,IAAI,CAAC,KAAK,GAAG,GAAG,CAAA;gBAChB,IAAI,CAAC,MAAM,GAAG,CAAC,wBAAa,CAAA;YAChC,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAA;SACtC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE;aACb;iBAAM,IAAI,CAAC,CAAC,KAAK,KAAK,KAAI,CAAC,IAAI,EAAE;gBAC9B,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aAC7B;iBAAM,IAAI,CAAC,CAAC,KAAK,KAAK,KAAI,CAAC,QAAQ,EAAE;gBAClC,KAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aAC/B;iBAAM;gBACH,CAAC,CAAC,KAAK,GAAG,EAAE,CAAA;gBACZ,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;gBACrB,CAAC,CAAC,KAAK,GAAG,EAAE,CAAA;aACf;QACL,CAAC,CAAC,CAAA;QACF,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,SAAG,IAAI,CAAC,MAAM,mCAAI,GAAG,CAAA;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,SAAG,IAAI,CAAC,MAAM,mCAAI,GAAG,CAAA;QACtC,IAAI,CAAC,KAAK,CAAC,KAAK,SAAG,IAAI,CAAC,KAAK,mCAAI,GAAG,CAAA;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,CAAA;QAChC,IAAI,CAAC,cAAc,SAAG,IAAI,CAAC,cAAc,mCAAI,GAAG,CAAA;QAChD,IAAI,CAAC,OAAO,SAAG,IAAI,CAAC,OAAO,mCAAI,GAAG,CAAA;QAClC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC,CAAA;QACpD,OAAO;YACH,MAAM,EAAE;gBACJ,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;gBAChC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;gBACpC,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,aAAa,EAAE,oCAAqB;gBACpC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC;oBACzB,OAAO;wBACH,KAAK,EAAE,CAAC,CAAC,MAAM;wBACf,GAAG,EAAE,CAAC,CAAC,GAAG;wBACV,EAAE,EAAE,CAAC,CAAC,EAAE;wBACR,EAAE,EAAE,CAAC,CAAC,EAAE;wBACR,KAAK,EAAE,CAAC,CAAC,KAAK;qBACjB,CAAA;gBACL,CAAC,CAAC;gBACF,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;gBACjD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;gBAC7C,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC;gBACtD,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;gBAC3C,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,oCAAqB,CAAC;gBAC7D,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,oCAAqB,CAAC;gBAChE,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACvC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC;gBACtC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA,CAAC,CAAC,CAAC;gBACnF,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;gBACjD,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC;aACnD;YACD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAE,EAAd,CAAc,CAAC;YAE1C,KAAK,EAAE;gBACH,EAAE,EAAE,IAAI,CAAC,QAAQ;gBACjB,IAAI,EAAE,IAAI,CAAC,SAAS;aACvB;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC9B;SACJ,CAAA;IACL,CAAC;IAED,UAAU;IACH,6CAAiB,GAAxB,UAAyB,IAAS;QAC9B,IAAI,oBAAO,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;SACtC;IACL,CAAC;IAED,MAAM;IACE,wCAAY,GAApB,UAAqB,SAAmB;QAAnB,0BAAA,EAAA,cAAmB;QACpC,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,IAAI,YAAY,GAAG,EAAE,CAAA;QACrB,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,SAAS,CAAA;QACxD,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;YACtB,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;YACxB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;gBACzC,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA;gBACjD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAClB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAA;aAC1C;SACJ;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,yBAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,EAAE,GAAG,yBAAc,CAAC,CAAC,CAAC,CAAA;YAC1B,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,2BAAiB,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,IAAA,EAAE,EAAE,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;YACjG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC,CAAA;SAC5C;QACD,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,sCAAuB,CAAA,CAAA,WAAW;QACvD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,MAAM;IACE,0CAAc,GAAtB,UAAuB,WAAqB;QAArB,4BAAA,EAAA,gBAAqB;QACxC,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,2BAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,EAAE,GAAG,2BAAgB,CAAC,CAAC,CAAC,CAAA;YAC5B,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,yBAAe,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,IAAA,EAAE,EAAE,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;YACjG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,GAAG,kCAAmB,CAAA;SAC/C;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,yCAAa,GAArB,UAAsB,KAAY,EAAE,KAAS;QAAT,sBAAA,EAAA,SAAS;QACzC,OAAO,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,MAAM;IACE,yCAAa,GAArB,UAAsB,UAAoB;QAApB,2BAAA,EAAA,eAAoB;QACtC,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,CAAA;QACvD,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;YACtB,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAA;YACxB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;oBAChB,IAAI,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;oBAC/B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;oBACpB,QAAQ,CAAC,EAAE,CAAC,GAAG,MAAM,CAAA;iBACxB;aACJ;SACJ;QACD,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,EAAE,GAAG,0BAAe,CAAC,CAAC,CAAC,CAAA;YAC3B,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,4BAAkB,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,IAAA,EAAE,EAAE,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;YACnG,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAA;SACxC;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,MAAM;IACN,wCAAY,GAAZ,UAAa,SAA8B;QACvC,IAAI,SAAS,EAAE;YACX,OAAO,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,2BAAiB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAlC,CAAkC,CAAC,CAAA;SAChE;QACD,IAAI,IAAI,GAAG,IAAI,2BAAiB,EAAE,CAAA;QAClC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAA;QACX,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,CAAA;QAClB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,uBAAuB;IACf,yCAAa,GAArB,UAAsB,IAAmB,EAAE,QAAgB;QACvD,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,QAAQ,IAAI,oBAAO,CAAC,MAAM,EAAE,EAAE;YACpE,YAAY;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBACxB,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,OAAM;iBACT;aACJ;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAA;SACjD;IACL,CAAC;IAEM,uCAAW,GAAlB,UAAmB,GAAW;QAC1B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;IACvB,CAAC;IACM,wCAAY,GAAnB,UAAoB,IAAmB;QACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;QAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpC,CAAC;IACD,qCAAS,GAAT,UAAU,IAAmB;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACpC,CAAC;IACM,oCAAQ,GAAf;QACI,OAAO,IAAI,CAAC,KAAK,CAAA;IACrB,CAAC;IAED,SAAS;IACF,6CAAiB,GAAxB,UAAyB,EAAU;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;IACjC,CAAC;IAEM,yCAAa,GAApB,UAAqB,EAAU,EAAE,KAAU;QACvC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,KAAK,CAAA;IAClC,CAAC;IAEO,uCAAW,GAAnB,UAAoB,GAAW;QAC3B,OAAO,UAAU,CAAC,QAAQ,CAAC,cAAc,GAAG,GAAG,CAAC,CAAA;IACpD,CAAC;IAEM,yCAAa,GAApB;QACI,UAAU,CAAC,MAAM,CAAC,cAAc,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAC,CAAA;IACxD,CAAC;IAED,KAAK;IACE,0CAAc,GAArB;QACI,IAAI,WAAW,GAAG,EAAE,CAAA;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,OAAO,GAAG,eAAe,GAAG,oBAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,0BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACjF,IAAI,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YACzC,IAAI,IAAI,EAAE;gBACN,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;aAC9B;SACJ;QACD,UAAU,CAAC,QAAQ,CAAC,qBAAqB,GAAG,oBAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAC3E,UAAU,CAAC,QAAQ,CAAC,sBAAsB,GAAG,oBAAO,CAAC,MAAM,EAAE,EAAE,WAAW,CAAC,CAAA;IAC/E,CAAC;IAED,OAAO;IACA,0CAAc,GAArB;QACI,IAAI,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC,qBAAqB,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAC,CAAA;QACxE,IAAI,IAAI,EAAE;YACN,IAAI,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC,sBAAsB,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAC,CAAA;YAChF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,0BAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAI,OAAO,GAAG,eAAe,GAAG,oBAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,0BAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;gBACjF,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;oBACrC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAA;iBACvD;aACJ;YACD,uBAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,GAAG,oBAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAA5D,CAA4D,CAAC,CAAA;SACxG;aACI;YACD,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,oBAAO,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,CAAA;SACvE;IACL,CAAC;IAEM,+BAAG,GAAV;QACI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IACzB,CAAC;IAGM,kCAAM,GAAb,UAAc,EAAU;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAM;SACT;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QAC3B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;QAC5B,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAA;QAChC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAA;QACnC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAA;QACjC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAA;QAC/B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;QAC7B,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;YACf,IAAI,CAAC,GAAG,IAAI,EAAE,CAAA;YACd,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;gBACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;gBACd,IAAI,CAAC,aAAa,EAAE,CAAA;gBACpB,IAAI,CAAC,SAAS,EAAE,CAAA;gBAChB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAA;aAChC;SACJ;QACD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACtB,CAAC;IAEM,qCAAS,GAAhB;QACI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACtB,CAAC;IAEM,iCAAK,GAAZ;QACI,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC/B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;SAClC;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACb,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;aAC5C;SACJ;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;gBAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;gBACjC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;aAClC;YACD,IAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACvD,KAAK,IAAI,GAAG,IAAI,SAAS,EAAE;gBACvB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;gBACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACb,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;iBAC5C;aACJ;SACJ;QACD,OAAO;YACH,KAAK,OAAA;YACL,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC;YACtC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;YAC1C,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;YAC9C,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;YAC5C,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;YAC/C,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;YAC7C,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YAC5C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;YAC3D,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC;YAChD,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,EAAE,EAAR,CAAQ,CAAC;YAChD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;SACpD,CAAA;IACL,CAAC;IAEM,mCAAO,GAAd,cAAmB,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC9B,mCAAO,GAAd,UAAe,GAAW;QACtB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAA;QACf,oBAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAEM,mCAAO,GAAd,cAAmB,OAAO,IAAI,CAAC,IAAI,CAAA,CAAC,CAAC;IAC9B,mCAAO,GAAd,UAAe,GAAW;QACtB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAA;IACnB,CAAC;IAEM,uCAAW,GAAlB,cAAuB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;IACtC,uCAAW,GAAlB,UAAmB,GAAW;QAC1B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;QACnB,oBAAO,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;IACjD,CAAC;IAEO,sCAAU,GAAlB,UAAmB,GAAQ;QACvB,IAAI,MAAM,GAAG,EAAE,CAAA;QACf,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;YACjB,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAA;SAChC;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAEO,qCAAS,GAAjB,UAAkB,EAAU;QACxB,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAM;QACxB,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;QACtB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YACtB,OAAM;SACT;QACD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAA;QAEpB,UAAU,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAC7D,oBAAoB;IACxB,CAAC;IAEM,mCAAO,GAAd,UAAe,KAAa;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAEM,yCAAa,GAApB;QACI,OAAO,IAAI,CAAC,UAAU,CAAA;IAC1B,CAAC;IAEM,2CAAe,GAAtB,UAAuB,MAA8B;;;QACjD,IAAM,EAAE,SAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,0CAAE,KAAK,CAAA;QAChD,IAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC,CAAC,CAAA;QACxD,IAAM,KAAK,SAAG,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAhC,CAAgC,CAAC,mCAAI,EAAE,CAAA;;YACpE,KAAe,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;gBAApB,IAAI,EAAE,qBAAA;gBACP,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;aAC/B;;;;;;;;;QACD,IAAI,CAAC,UAAU,YAAO,IAAI,CAAC,UAAU,EAAK,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,CAAA;QAC/F,IAAI,CAAC,wBAAwB,EAAE,CAAA;IACnC,CAAC;IAEO,oDAAwB,GAAhC;QACI,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;QACF,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAClB,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;SACjD;IACL,CAAC;IAED,SAAS;IACD,mDAAuB,GAA/B,UAAgC,GAAW,EAAE,IAAc;QACvD,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;YAChC,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YACpC,WAAW;YACX,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,EAAE;gBACzB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;gBAC9B,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;aACpE;SACJ;IACL,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAS;QACjC,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,CAAA;QACrE,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE;YACtB,IAAM,GAAG,GAAU,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,+BAAqB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAArC,CAAqC,CAAC,CAAA;SACtF;IACL,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAS;QACjC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAA;QACzB,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACrB,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;gBACf,IAAM,GAAG,GAAU,GAAG,CAAC,CAAC,CAAC,CAAA;gBACzB,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,4BAAkB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAlC,CAAkC,CAAC,CAAA;aAC5D;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;SAClC;IACL,CAAC;IAEO,+CAAmB,GAA3B;QACI,IAAI,MAAM,GAAG,0BAAe,EAAE,MAAM,GAAG,0BAAe,EAAE,KAAK,GAAG,0BAAe,CAAA;QAC/E,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;YACrD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,SAAS;gBAC5B,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAA;gBACtC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;gBACrD,IAAI,IAAI,EAAE;oBACN,MAAM,IAAI,IAAI,CAAC,MAAM,CAAA;oBACrB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAA;oBACrB,KAAK,IAAI,IAAI,CAAC,KAAK,CAAA;iBACtB;aACJ;iBAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1B,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBAClD,MAAM,IAAI,IAAI,CAAC,MAAM,CAAA;gBACrB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAA;gBACrB,KAAK,IAAI,IAAI,CAAC,KAAK,CAAA;aACtB;SACJ;QACD,OAAO,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,CAAA;IACpC,CAAC;IAED,cAAc;IACP,gDAAoB,GAA3B,UAA4B,IAAa;QAAzC,iBAIC;QAHG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,KAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;IACN,CAAC;IAED,SAAS;IACF,+CAAmB,GAA1B,UAA2B,KAAa,EAAE,GAAW,EAAE,KAAa;QAChE,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,CAAC,IAAI,EAAE;YACP,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;SACnC;QACD,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE;YACb,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;SACnB;aAAM;YACH,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;SACpB;IACL,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,GAAW;QACjC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,EAAE,CAAA;QACjD,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACvB,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;SACtB;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,aAAa;IACN,kDAAsB,GAA7B,UAA8B,GAAW;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,IAAM,GAAG,GAAG,EAAE,CAAA;gCACL,GAAG;YACR,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,IAAM,IAAI,GAAG,OAAK,OAAO,CAAC,KAAK,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,EAAE;;aAEV;YACD,IAAM,KAAK,GAAG,EAAE,CAAA;YAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;oBACjB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;iBACxB;YACL,CAAC,CAAC,CAAA;YACF,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;aAC7B;;;QAdL,KAAK,IAAI,GAAG,IAAI,IAAI;oBAAX,GAAG;SAeX;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,WAAW;IACJ,4CAAgB,GAAvB;QACI,IAAM,GAAG,GAAG,oBAAO,CAAC,MAAM,EAAE,CAAA;QAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAChC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;oBACjB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;iBACxB;YACL,CAAC,CAAC,CAAA;SACL;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,kDAAsB,GAA9B;QACI,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;oBACjB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAK,IAAI,CAAC,CAAC,UAAU,EAArB,CAAqB,CAAC,CAAA;iBAC9C;YACL,CAAC,CAAC,CAAA;SACL;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,2CAAe,GAAvB,UAAwB,IAAa;QACjC,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE;YAC9B,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;YACtC,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC1B,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aAClC;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,SAAS;IACD,yCAAa,GAArB;QACI,OAAO,uBAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC3F,CAAC;IAED,SAAS;IACD,2CAAe,GAAvB;QACI,OAAO,uBAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;IAC7F,CAAC;IAED,SAAS;IACD,uCAAW,GAAnB,UAAoB,EAAU;QAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrD,IAAI,GAAG,IAAI,CAAC,EAAE;YACV,OAAO,CAAC,CAAA;SACX;QACD,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,oCAAqB,CAAC,CAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBACb,EAAE,IAAI,CAAC,CAAC,EAAE,CAAA;aACb;QACL,CAAC,CAAC,CAAA;QACF,OAAO,EAAE,GAAG,GAAG,CAAA;IACnB,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAY;QACpC,QAAQ,IAAI,EAAE;YACV,KAAK,eAAO,CAAC,UAAU;gBACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;gBACtB,MAAM;SACb;IACL,CAAC;IAEO,uCAAW,GAAnB,UAAoB,QAAkB;QAClC,WAAW;QACP,IAAA,KAA4B,IAAI,CAAC,mBAAmB,EAAE,EAApD,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,KAAK,WAA+B,CAAA;QAC1D,aAAa;QACb,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,UAAU,CAAC,CAAA;QAClD,MAAM,IAAI,GAAG,CAAA;QACb,MAAM,IAAI,GAAG,CAAA;QACb,KAAK,IAAI,GAAG,CAAA;QACZ,OAAO;QACP,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAClD,YAAY;QACZ,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAA;QACzE,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;SAC1B;QACD,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;SAC1B;QACD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;aACpC,CAAC,CAAA;SACL;IACL,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,GAAW;QACjC,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAA;QAClC,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE;YAC1B,OAAM;SACT;QACD,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA;QACzB,OAAO;QACP,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,IAAI;YAC3D,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;SACxE;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAA;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI;YAC/B,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;SACxE;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI;YAC9B,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;SACtE;IACL,CAAC;IAEM,wCAAY,GAAnB,UAAoB,IAAgB,EAAE,IAAS;;QAC3C,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,SAAG,IAAI,MAAA,IAAE,GAAC,OAAO,GAAG,IAAI,IAAG,IAAI,MAAG,EAAE,CAAC,CAAA;IACtF,CAAC;IAEM,uCAAW,GAAlB,UAAmB,IAAgB,EAAE,IAAS;;QAC1C,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE,SAAG,IAAI,MAAA,IAAE,GAAC,OAAO,GAAG,IAAI,IAAG,IAAI,MAAG,EAAE,CAAC,CAAA;IACrF,CAAC;IAEM,sCAAU,GAAjB,UAAkB,KAAa,EAAE,IAAgB,EAAE,IAAS;;QACxD,IAAI,CAAC,IAAI,CAAC,uBAAuB,UAAI,KAAK,OAAA,EAAE,IAAI,MAAA,IAAE,GAAC,OAAO,GAAG,IAAI,IAAG,IAAI,MAAG,CAAA;IAC/E,CAAC;IAEO,oDAAwB,GAAhC,UAAiC,KAAc;QAC3C,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IAC5F,CAAC;IAED,aAAa;IACN,2CAAe,GAAtB,UAAuB,MAAc,EAAE,MAAc;QAC7C,IAAA,KAAA,OAAW,qBAAS,CAAC,cAAc,CAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,CAAC,IAAA,EAAlJ,EAAE,QAAA,EAAE,EAAE,QAA4I,CAAA;QACvJ,OAAO,qBAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,aAAa;IACN,kDAAsB,GAA7B,UAA8B,KAAsB,EAAE,MAAc,EAAE,QAAgB;QAClF,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACzC,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAC7C,IAAI,IAAI,EAAE;gBACN,IAAI,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;gBACxC,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;gBACxD,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;gBACjD,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;aACtC;SACJ;QACD,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAA;QACvD,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED,OAAO;IACA,wCAAY,GAAnB,UAAoB,KAAa,EAAE,IAAa,EAAE,MAAc,EAAE,SAAiB,EAAE,IAAa;QAC9F,IAAI,KAAK,KAAK,MAAM,EAAE;YAClB,OAAM;SACT;QACD,UAAU;QACV,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC/B,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;YAC3C,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,qBAAS,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;SACxI;QACD,YAAY;QACZ,IAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC1C,OAAO;QACP,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,SAAS,GAAG,CAAC,EAAE;YACf,IAAI,GAAG,SAAS,CAAA;SACnB;aAAM;YACH,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAC/C,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;YAC7C,IAAI,SAAS,GAAG,CAAC,EAAE,EAAE,MAAM;gBACvB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,CAAA;aACnD;SACJ;QACD,qEAAqE;QACrE,IAAM,KAAK,GAAG,IAAI,wBAAc,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACvF,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACvB,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,KAAK,CAAA;QAC5B,OAAO;QACP,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;QACrD,+BAA+B;QAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QACjE,WAAW;QACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;IACnC,CAAC;IAED,WAAW;IACH,4CAAgB,GAAxB,UAAyB,GAAW;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,SAAQ;aACX;YACD,YAAY;YACZ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YACxB,CAAC,EAAE,CAAA;YACH,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;YACzD,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;YACzE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACjB,SAAQ;aACX;YACD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;YACzC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAA;YAC3B,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAA;YAC3C,IAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC9E,eAAe;YACf,IAAI,QAAQ,IAAI,gBAAgB,EAAE;gBAC9B,IAAI,CAAC,QAAQ,EAAE;oBACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAC,kBAAkB;iBAC/C;gBACD,iCAAiC;gBACjC,IAAI,QAAQ,IAAI,CAAC,gBAAgB,EAAE;oBAC/B,OAAO;oBACP,qFAAqF;oBACrF,+EAA+E;oBAC/E,8DAA8D;oBAC9D,WAAW;oBACX,0CAA0C;oBAC1C,gDAAgD;oBAChD,cAAc;oBACd,mGAAmG;oBACnG,IAAI;iBACP;qBAAM;oBACH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;oBACrC,UAAU;oBACV,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,qBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;oBAC/E,WAAW;oBACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;oBAC/B,OAAO;oBACP,qFAAqF;iBACxF;aACJ;iBAAM,EAAE,SAAS;gBACd,gHAAgH;gBAChH,8BAA8B;gBAC9B,oCAAoC;aACvC;SACJ;IACL,CAAC;IAEO,uCAAW,GAAnB,UAAoB,IAAmB,EAAE,IAAa,EAAE,GAAW;QAC/D,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAA,CAAC,SAAS;QACnD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAArB,CAAqB,CAAC,CAAA;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAC1B,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAClC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1D,MAAM;QACN,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAChC,OAAO,IAAI,CAAA;SACd;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC3C,IAAI,QAAQ,EAAE,EAAE,aAAa;YACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;SAC/D;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,EAAE,QAAQ;YAC7C,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBACxB,IAAI,CAAC,cAAc,EAAE,CAAA,CAAC,QAAQ;aACjC;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;SACjE;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,IAAmB,EAAE,QAAgB,EAAE,UAAmB;QAApF,iBA6BC;QA5BG,YAAY;QACZ,IAAI,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,0BAAY,CAAC,oBAAoB,CAAC,EAAE;YACpE,UAAU;YACV,IAAM,MAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAI,CAAC,IAAI,EAArB,CAAqB,CAAC,CAAA;YACxD,IAAI,MAAI,IAAI,MAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAE/B,6CAA8B,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC,yBAAK,MAAI,CAAC,KAAK,CAAC,CAAC,CAAC,0CAAE,QAAQ,CAAC,CAAC,IAAC,CAAC,CAAA;gBAC5E,MAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAA;aAC3B;YACD,UAAU;YACV,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAI,CAAC,IAAI,EAArB,CAAqB,CAAC,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;gBAC9E,CAAC,CAAC,KAAK,SAAG,2CAA4B,CAAC,CAAC,CAAC,EAAE,CAAC,mCAAI,CAAC,CAAC,KAAK,CAAA;YAC3D,CAAC,CAAC,EAF4D,CAE5D,CAAC,CAAA;YACH,IAAI,CAAC,mBAAmB,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,UAAA,EAAE,CAAA;SAC7D;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM;YAC9C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,UAAA,EAAE,CAAA;YAC/D,QAAQ;YACR,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;iBACnB;qBAAM,IAAI,CAAC,CAAC,KAAK,KAAK,iBAAS,CAAC,IAAI,EAAE;oBACnC,CAAC,CAAC,KAAK,GAAG,iBAAS,CAAC,KAAK,CAAA;oBACzB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,CAAC,iBAAS,CAAC,KAAK,CAAC,EAA9B,CAA8B,CAAC,CAAA;iBACvD;YACL,CAAC,CAAC,CAAA;SACL;aAAM;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;SACnC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO;IACA,mCAAO,GAAd,UAAe,QAAgB;QAC3B,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;YACxB,OAAO,CAAC,CAAA;SACX;aACI,IAAI,QAAQ,KAAK,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE;YACrD,OAAO,CAAC,CAAA;SACX;QACD,OAAO,CAAC,CAAA;IACZ,CAAC;IAEM,uCAAW,GAAlB,UAAmB,IAAmB,EAAE,QAAgB,EAAE,GAAY;QAAtE,iBA8DC;QA7DG,IAAM,KAAK,GAAG,EAAE,CAAA;QAChB,IAAM,QAAQ,GAAG,EAAE,CAAA;QACnB,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;QAC1D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChB,OAAM;aACT;YACD,CAAC,CAAC,KAAK,GAAG,iBAAS,CAAC,KAAK,CAAA;YACzB,IAAM,UAAU,GAAG,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAA;YACvC,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;YAC5B,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;gBACb,CAAC,CAAC,WAAW,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAA;gBAC9B,QAAQ,CAAC,IAAI,CAAC;oBACV,GAAG,EAAE,CAAC,CAAC,GAAG;oBACV,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC3B,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;iBACnE,CAAC,CAAA;gBACF,IAAI,QAAQ,EAAE;oBACV,oBAAO,CAAC,cAAc,OAAC,CAAC,CAAC,QAAQ,0CAAE,UAAU,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;wBACpD,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAA;oBAC5D,CAAC,CAAC,CAAA;iBACL;gBACD,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAA;YAChD,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC9C,IAAI,CAAC,EAAE;YACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;SACvB;aAAM;YACH,CAAC,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;SACnE;QACD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;QAChB,IAAI,WAAW,GAAG,CAAC,CAAA;QACnB,QAAQ,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAA7B,CAA6B,CAAC,CAAA;QACtD,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,GAAG,EAAE,WAAW,EAA7B,CAA6B,CAAC,CAAA;QACpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAA;QAClD,OAAO;QACP,CAAC,CAAC,WAAW,CAAC;YACV,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3B,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,GAAG,IAAI,CAAC;YACb,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;SACrB,CAAC,CAAA;QACF,KAAK;QACL,mBAAQ,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,KAAK,OAAA;YACL,KAAK,EAAE;gBACH,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,SAAS,EAAE,qBAAS,CAAC,kBAAkB,CAAC,oCAAqB,EAAE,IAAI,CAAC,KAAK,CAAC;gBAC1E,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC/C,WAAW,EAAE,IAAI,CAAC,KAAK;aAC1B;YACD,GAAG,EAAE,oBAAO,CAAC,MAAM,EAAE;SACxB,CAAC,CAAA;QACF,QAAQ;QACR,IAAI,QAAQ,KAAK,oBAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAO,CAAC,MAAM,EAAE,CAAC,EAAE;YACtH,IAAI,MAAM,GAAG,IAAI,yBAAe,EAAE,CAAA;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAA;YACzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;SACxC;IACL,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,IAAmB,EAAE,WAAmB,EAAE,QAAe;QAC9E,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YAC5B,OAAO;YACP,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;SACL;aACI,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE,EAAC,IAAI;YACzC,OAAO;YACP,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;SACL;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE,EAAC,IAAI;YAC3C,OAAO;YACP,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;YACF,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;gBACtB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;YACF,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;gBACtB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;YACF,QAAQ,CAAC,IAAI,CAAC;gBACV,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC;gBACV,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,EAAE,WAAW;gBAC1B,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBACvB,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;SACL;IACL,CAAC;IAEM,wCAAY,GAAnB;QACI,IAAM,QAAQ,GAAG,oBAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAA;QAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC/B,IAAA,KAAsB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAArD,KAAK,WAAA,EAAE,QAAQ,cAAsC,CAAA;YAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YACzC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAA;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YACxC,OAAO,IAAI,CAAA;SACd;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,4CAAgB,GAAvB;QAAA,iBASC;QARG,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACpB,IAAA,KAAsB,IAAI,CAAC,mBAAmB,EAA5C,KAAK,WAAA,EAAE,QAAQ,cAA6B,CAAA;YACpD,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAA;YACpC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAChC,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,KAAK,KAAI,CAAC,IAAI,EAArB,CAAqB,GAAG;gBAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAA;aACtC;SACJ;IACL,CAAC;IAED,SAAS;IACF,4CAAgB,GAAvB,UAAwB,KAAa,EAAE,QAAgB,EAAE,aAAkB;;QACvE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QACpC,SAAS;QACT,IAAM,CAAC,SAAG,IAAI,CAAC,OAAO,mCAAI,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAC3D,IAAI,CAAC,CAAC,EAAE;YACJ,OAAM;SACT;QAED,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE,EAAE;YAC9B,IAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE,EAAC,MAAM;gBACvC,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,iBAAiB,CAAC,CAAA;gBACtD,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE;oBAC/B,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,kBAAkB,CAAC,CAAA;iBAC1D;qBACI,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAa,EAAE;oBACpC,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,mBAAmB,CAAC,CAAA;iBAC3D;aACJ;YACD,IAAI,CAAC,oBAAoB,EAAE,CAAA;SAC9B;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAM,YAAY,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAA;QACrC,oBAAO,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAA7B,CAA6B,CAAC,CAAA;QACtE,SAAS;QACT,IAAI,UAAU,GAAG,EAAE,EAAE,YAAY,GAAG,KAAK,CAAA;QACzC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAxB,CAAwB,CAAC,CAAA;QAC9C,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;YAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBAClB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;gBACpB,YAAY,GAAG,IAAI,CAAA;aACtB;SACJ;QACD,IAAI,YAAY,EAAE;YACd,IAAI,CAAC,wBAAwB,EAAE,CAAA;SAClC;QACD,YAAY;QACZ,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,QAAQ,EAAE;YAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;SACvC;QACD,IAAI,QAAQ,IAAI,KAAK,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAC,CAAA;SAChD;aAAM,EAAE,eAAe;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;SACzC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;YACnB,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,IAAI,CAAA;YAC3B,aAAa;YACb,IAAI,oBAAO,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;gBAClD,IAAI,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC7B,IAAI,CAAC,GAAG,EAAE;oBACN,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;iBACjC;gBACD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACjB;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,CAAC,CAAC,WAAW,CAAC,iBAAS,CAAC,IAAI,CAAC,CAAA;gBAC7B,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA,CAAC,QAAQ;YACtE,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QACF,IAAI,eAAe,GAAG,EAAE,CAAA,CAAA,MAAM;QAC9B,OAAO;QACH,IAAA,KAAqD,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAA9F,WAAW,iBAAA,EAAE,cAAc,oBAAA,EAAE,iBAAiB,uBAAgD,CAAA;QACpG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE;YACjF,IAAM,IAAI,GAAG,EAAE,CAAA;YACf,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvD,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;aAC1B;YACD,IAAM,mBAAmB,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,WAAW;YACvF,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE;gBACrB,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK,GAAc,OAAO,CAAC,GAAG,CAAC,CAAA;gBAC/C,IAAI,aAAa,GAAG,CAAC,EAAE,oBAAoB,GAAG,CAAC,CAAA;gBAC/C,IAAI,GAAG,KAAK,QAAQ,EAAE;oBAClB,aAAa,GAAG,mBAAmB,CAAA;oBACnC,oBAAoB,GAAG,CAAC,CAAA,CAAC,UAAU;iBACtC;gBACD,SAAS;gBACT,IAAI,iBAAiB,GAAG,CAAC,IAAI,oBAAoB,GAAG,CAAC,EAAE;oBACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;wBACrB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;4BACpD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;4BAC3B,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAA;4BAC9E,MAAM,IAAI,EAAE,CAAA;4BACZ,oBAAoB,IAAI,EAAE,CAAA;4BAC1B,IAAI,oBAAoB,IAAI,CAAC,EAAE;gCAC3B,MAAK;6BACR;yBACJ;wBACD,IAAI,oBAAoB,IAAI,CAAC,EAAE;4BAC3B,MAAK;yBACR;qBACJ;iBACJ;gBACD,IAAI,aAAa,GAAG,CAAC,EAAE;oBACnB,OAAO;oBACP,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAA;oBAC/B,IAAI,MAAM,GAAG,GAAG,CAAA;oBAChB,IAAI,GAAG,GAAG,aAAa,GAAG,GAAG,GAAG,MAAM,CAAA;oBACtC,IAAI,MAAM,GAAG,EAAE,CAAA;oBACf,IAAI,CAAC,GAAG,CAAC,CAAA;oBACT,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBACjC,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC,CAAA;wBACxC,GAAG,IAAI,GAAG,CAAA;wBACV,MAAM,IAAI,CAAC,CAAA;wBACX,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAA;qBACxB;oBACD,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAA;oBACrB,IAAI,KAAK,GAAG,CAAC,CAAA;oBAEb,KAAK,IAAI,GAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,GAAC,GAAG,CAAC,EAAE,GAAC,EAAE,EAAE;wBAC1C,IAAM,IAAI,GAAG,KAAK,CAAC,GAAC,CAAC,CAAA;wBACrB,KAAK,IAAI,MAAM,CAAC,GAAC,CAAC,CAAA;wBAClB,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;4BACvC,SAAQ;yBACX;wBACD,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;4BACpD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;4BAC3B,IAAI,EAAE,GAAG,CAAC,CAAA;4BACV,IAAI,UAAU,GAAG,CAAC,CAAA;4BAClB,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;gCACnC,UAAU,GAAG,WAAW,CAAC,EAAE,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAA;gCAC1E,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;6BACrD;iCACI;gCACD,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAA;gCAChC,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAA;6BACjD;4BACD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;gCAC/B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;6BACxE;4BACD,MAAM,IAAI,EAAE,CAAA;4BACZ,KAAK,IAAI,EAAE,CAAA;4BACX,IAAI,KAAK,IAAI,CAAC,EAAE;gCACZ,MAAK;6BACR;yBACJ;qBACJ;iBACJ;gBACD,IAAI,MAAM,GAAG,CAAC,EAAE;oBACZ,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;iBACnB;aACJ;YACD,WAAW;YACX,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;gBAClB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAA;aAClC;SACJ;QACD,KAAK;QACL,IAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE;YAChC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAA;YACrB,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;gBAChC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aACrC;iBAAM;gBACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aACnC;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACtB,SAAS;YACT,4EAA4E;YAC5E,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA,CAAC,IAAI;YACtE,SAAS;YACT,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,UAAU,EAAE,CAAA,CAAA,UAAU;gBAC3B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC7B,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;oBAC/B,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;wBACzB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAA;wBACrB,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;wBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;wBACrB,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA,CAAC,IAAI;qBACrF;iBACJ;gBACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;gBACpB,aAAa;gBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;oBACrB,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;iBAC3F;aACJ;YACD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAA;SACrC;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;QAC5B,OAAO;QACP,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,cAAc;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAC5B,OAAO;QACP,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAC5B,aAAa;QACb,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YACxB,IAAI,CAAC,gBAAgB,EAAE,CAAA;SAC1B;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,eAAe,EAAE;YACpD,QAAQ,UAAA;YACR,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;YAClB,SAAS,EAAE,eAAe;SAC7B,CAAC,CAAA;QACF,KAAK;QACL,mBAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,KAAK,EAAE,CAAC,CAAC,QAAQ;YACjB,eAAe,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC5C,KAAK,EAAE;gBACH,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,SAAS,EAAE,qBAAS,CAAC,kBAAkB,CAAC,oCAAqB,EAAE,IAAI,CAAC,KAAK,CAAC;gBAC1E,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE;gBAC1C,WAAW,EAAE,IAAI,CAAC,KAAK;aAC1B;YACD,GAAG,EAAE,oBAAO,CAAC,MAAM,EAAE;SACxB,CAAC,CAAA;QACF,IAAI,CAAC,SAAS,EAAE,CAAA;QAChB,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,aAAa,eAAA,EAAE,eAAe,iBAAA,EAAE,CAAC,CAAA;IACrE,CAAC;IAEO,kDAAsB,GAA9B,UAA+B,IAAmB,EAAE,OAAe;QAC/D,IAAI,WAAW,GAAG,EAAE,EAAE,cAAc,GAAa,EAAE,EAAE,iBAAiB,GAAG,CAAC,CAAA;QAC1E,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SAC5D;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;YAC/B,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SAC5D;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SAC5D;QACD,IAAM,KAAK,GAAG,oCAAqB,CAAA;QACnC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;QACpE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,CAAA;QACnE,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SAC5D;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;YACtD,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SAC5D;QACD,IAAI,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;QAC/E,IAAI,eAAe,EAAE;YACjB,OAAO,eAAe,CAAA;SACzB;QACD,IAAM,gBAAgB,GAAG,QAAQ,GAAG,GAAG,CAAA;QACvC,IAAM,OAAO,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;QAC1D,OAAO,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAA;aAClE;QACL,CAAC,CAAC,CAAA;QACF,cAAc,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAA;QAC9D,OAAO;QACP,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAM,IAAI,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAA;YAC7D,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBACpB,iBAAiB,GAAG,gBAAgB,GAAG,EAAE,CAAA;aAC5C;SACJ;QACD,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,gBAAA,EAAE,iBAAiB,mBAAA,EAAE,CAAA;IAC7D,CAAC;IAED,YAAY;IACJ,uDAA2B,GAAnC,UAAoC,IAAmB,EAAE,iBAAyB;;QAC9E,IAAI,SAAS,GAAG,CAAC,CAAA;QACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACnB,QAAQ,GAAG,CAAC,CAAA;YACZ,SAAS,GAAG,oBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC1D;aACI;YACD,YAAA,oBAAO,CAAC,aAAa,CAAC,oBAAO,CAAC,MAAM,EAAE,CAAC,0CAAE,KAAK,0CAAE,OAAO,CAAC,UAAA,IAAI;gBACxD,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;oBACnF,SAAS,IAAI,CAAC,CAAA;iBACjB;YACL,CAAC,EAAC;SACL;QACD,IAAI,IAAI,GAAG,8CAA+B,CAAC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;QACxE,IAAI,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE;YACjC,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,CAAA;YACjC,IAAI,OAAO,WAAW,IAAI,QAAQ,EAAE;gBAChC,WAAW,GAAG,CAAC,WAAW,CAAC,CAAA;aAC9B;YACD,OAAO,EAAE,WAAW,aAAA,EAAE,cAAc,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,iBAAiB,mBAAA,EAAE,CAAA;SACtG;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEO,2CAAe,GAAvB,UAAwB,IAAa,EAAE,GAAW,EAAE,EAAU;;QAC1D,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YACrB,OAAO,CAAC,CAAA;SACX;QACD,IAAM,MAAM,GAAG,OAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,KAAI,CAAC,CAAA;QAC1C,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAA;QAC1C,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC5B,IAAI,EAAE,KAAK,6CAA8B,EAAE,EAAC,aAAa;YACrD,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;SAC7B;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAO,CAAC,mBAAmB,CAAC;gBAC5C,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,EAAE,EAAE,EAAE;gBACN,OAAO,EAAE,EAAE;aACd,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;SAC3C;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,cAAc;IACP,kDAAsB,GAA7B,UAA8B,GAAW;QACrC,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;iBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAtB,CAAsB,CAAC,EAAlE,CAAkE,CAAC,EAAE;gBACpG,OAAO,IAAI,CAAA;aACd;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,WAAW;IACH,iDAAqB,GAA7B,UAA8B,GAAW;QACrC,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAA;QAC5C,IAAI,IAAI,CAAC,WAAW,KAAK,GAAG,EAAE;YAC1B,IAAI,CAAC,WAAW,GAAG,GAAG,CAAA;YACtB,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,YAAY,EAAE,GAAG,CAAC,CAAA;SAClD;IACL,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,IAAmB;QAA7C,iBA4BC;QA3BG,IAAI,oBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,0BAAY,CAAC,sBAAsB,CAAC,EAAE;YAC9D,OAAM;SACT;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;;YACnB,IAAM,aAAa,SAAG,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,mCAAI,CAAC,CAAC,CAAA;YAC/D,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;gBACxD,OAAM;aACT;YACD,IAAI,MAAM,GAAkB,IAAI,CAAA;YAChC,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ;gBAChC,MAAM,GAAG,KAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;aAC5E;iBAAM;gBACH,IAAM,CAAC,GAAG,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBACnC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;oBACjH,MAAM,GAAG,CAAC,CAAA;iBACb;aACJ;YACD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;gBACxC,OAAM;aACT;YACD,IAAI,KAAK,GAAG,CAAC,CAAA;YACb,iBAAiB;YACjB,KAAK,IAAI,CAAC,gCAAqB,CAAC,oBAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;YACjG,OAAO;YACP,KAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;YACxC,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,8DAAkC,GAA1C,UAA2C,GAAW,EAAE,UAAkB;QACtE,IAAI,CAAC,GAAG,EAAE;YACN,OAAO,IAAI,CAAA;SACd;QACD,IAAI,MAAM,GAAG,MAAM,EAAE,GAAG,GAAkB,IAAI,CAAA;QAC9C,IAAM,KAAK,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;QACjE,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;YACnB,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;YACvB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBAClF,IAAM,GAAG,GAAG,oBAAO,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC3D,IAAI,GAAG,GAAG,MAAM,EAAE;oBACd,MAAM,GAAG,GAAG,CAAA;oBACZ,GAAG,GAAG,IAAI,CAAA;iBACb;aACJ;SACJ;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEO,2CAAe,GAAvB,UAAwB,IAAmB,EAAE,IAAa;QAA1D,iBA8CC;QA7CG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAM;SACT;QACD,IAAM,CAAC,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,WAAW,EAAE,CAAA;QACxE,IAAI,CAAC,GAAG,EAAE;YACN,OAAM;SACT;QACD,IAAM,iBAAiB,GAAG,GAAG,CAAC,oBAAoB,EAAE,CAAA;QACpD,IAAI,CAAC,KAAK,GAAG,iBAAS,CAAC,KAAK,CAAA;QAC5B,IAAM,QAAQ,GAAG,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,CAAA;QAC3D,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnD,IAAI,CAAC,UAAU,EAAE;YACb,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAA;SACvD;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;;YAChB,CAAC,CAAC,WAAW,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAA;YAC9B,QAAQ,CAAC,IAAI,CAAC;gBACV,GAAG,EAAE,CAAC,CAAC,GAAG;gBACV,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,CAAC,CAAC,WAAW;gBAC1B,SAAS,EAAE,CAAC;aACf,CAAC,CAAA;YACF,IAAI,QAAQ,EAAE;gBACV,oBAAO,CAAC,cAAc,OAAC,CAAC,CAAC,QAAQ,0CAAE,UAAU,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC;oBACpD,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAA;gBAC5D,CAAC,CAAC,CAAA;aACL;QACL,CAAC,CAAC,CAAA;QACF,IAAI,WAAW,GAAG,CAAC,CAAA;QACnB,GAAG,CAAC,mBAAmB,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,UAAA,CAAC;YAC7C,IAAI,CAAC,CAAC,WAAW,GAAG,WAAW,EAAE;gBAC7B,WAAW,GAAG,CAAC,CAAC,WAAW,CAAA;aAC9B;QACL,CAAC,CAAC,CAAA;QACF,QAAQ,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,EAA7B,CAA6B,CAAC,CAAA;QACtD,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,GAAG,EAAE,WAAW,EAA7B,CAA6B,CAAC,CAAA;QACpD,IAAI,KAAK,GAAG;YACR,IAAI,EAAE,CAAC;YACP,iBAAiB,EAAE,iBAAiB;YACpC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;YAClB,QAAQ,EAAE,QAAQ;SACrB,CAAA;QACD,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC1B,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;IACxC,CAAC;IAED,QAAQ;IACA,2CAAe,GAAvB,UAAwB,IAAY,EAAE,IAAmB,EAAE,IAAS;QAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;YACvC,IAAI,MAAM,CAAC,OAAO,GAAG,CAAC;gBAAE,SAAQ;YAChC,IAAI,CAAC,KAAK,IAAI,EAAE;gBACZ,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;aAC9B;iBACI,IAAI,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;aAC/B;iBACI,IAAI,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;aAC9B;SACJ;QACD,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACxE,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAA;SAC3D;IACL,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAmB,EAAE,IAAa,EAAE,GAAW;QACvE,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;QACnC,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;YACvC,OAAM;SACT;aAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,0BAA0B;YACvF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC,CAAA;SACzE;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,CAAC,SAAS;SACvD;IACL,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,IAAmB;QACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;YACnB,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,EAAE;gBAChC,OAAM;aACT;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBACnB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;gBACxB,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE;oBACpF,OAAM;iBACT;gBACD,IAAM,GAAG,GAAG,qBAAS,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;gBAC5D,IAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;gBACnD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;iBAC3B;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,kDAAsB,GAA9B,UAA+B,KAAa,EAAE,IAAY,EAAE,KAAc;QACtE,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE;YACpB,OAAO,GAAG,GAAG,GAAG,IAAI,CAAA;SACvB;aAAM;YACH,OAAO,IAAI,IAAI,CAAA;SAClB;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,OAAO,CAAA;QACnC,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,cAAc,EAAE;YACxC,KAAK,OAAA;YACL,IAAI,EAAE,OAAO,GAAG,GAAG;SACtB,CAAC,CAAA;IACN,CAAC;IAEO,yCAAa,GAArB,UAAsB,IAAmB,EAAE,IAAc;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,MAAA,EAAE,CAAC,CAAA;IAClF,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,KAAa;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAC7B,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,WAAW,EAAE,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;IACvD,CAAC;IAEM,4CAAgB,GAAvB,cAA4B,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;IAC7C,8CAAkB,GAAzB,cAA8B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;IAClD,qCAAS,GAAhB,cAAqB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAA,CAAC,CAAC;IACtD,2CAAe,GAAtB,cAA2B,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;IAErD,cAAc;IACP,mDAAuB,GAA9B,UAA+B,KAAa;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,KAAK,KAAK,EAAvB,CAAuB,CAAC,CAAA;IACzD,CAAC;IAEM,2CAAe,GAAtB,UAAuB,IAAmB;;QACtC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;QACtB,OAAO,OAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,oBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAA5D,CAA4D,CAAC,0CAAE,KAAK,KAAI,EAAE,CAAA;IAC1G,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAmB,EAAE,KAAa,EAAE,QAAiB;QAC7E,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;YAC/C,OAAO,KAAK,CAAA,CAAC,sBAAsB;SACtC;aAAM,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;YACtD,OAAO,IAAI,CAAA,CAAC,mBAAmB;SAClC;QACD,uBAAuB;QACvB,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACzF,CAAC;IAED,SAAS;IACD,8CAAkB,GAA1B,UAA2B,KAAa,EAAE,GAAW;QACjD,IAAM,IAAI,GAAG,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACnD,IAAI,IAAI,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;YACrD,OAAO,KAAK,CAAA;SACf;QACD,IAAM,MAAM,GAAG,qBAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACxE,OAAO,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,YAAI,OAAA,CAAC,CAAC,oBAAO,CAAC,kBAAkB,OAAC,oBAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,0CAAE,KAAK,EAAE,GAAG,CAAC,CAAA,EAAA,CAAC,CAAA;IACzG,CAAC;IAEO,0CAAc,GAAtB,UAAuB,KAAa;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IACtC,CAAC;IAEO,0CAAc,GAAtB,UAAuB,IAAa,EAAE,KAAa;QAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;IACtD,CAAC;IAEM,iDAAqB,GAA5B,UAA6B,KAAa;QACtC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IACxD,CAAC;IAEO,gDAAoB,GAA5B,UAA6B,IAAa,EAAE,KAAa;QACrD,IAAM,QAAQ,GAAG,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9D,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;YAChB,IAAI,oBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;gBACjD,CAAC,IAAI,CAAC,CAAA;aACT;iBAAM;gBACH,CAAC,IAAI,CAAC,CAAA;aACT;QACL,CAAC,CAAC,CAAA;QACF,IAAI,QAAQ,EAAE;YACV,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAA;SAC/B;QACD,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAA;IAChC,CAAC;IAED,SAAS;IACF,0CAAc,GAArB,UAAsB,IAAS;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,OAAO;YACH,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;SAC/B,CAAA;IACL,CAAC;IAED,WAAW;IACJ,6CAAiB,GAAxB,UAAyB,IAAY,EAAE,WAAmB;;QACtD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,IAAI,IAAI,eAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,OAAO,mCAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,IAAI,IAAI,WAAW,KAAK,IAAI,CAAC,KAAK,EAAE;gBACrC,SAAQ;aACX;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBAChB,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;oBACrD,OAAM;iBACT;gBACD,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAA;YACvB,CAAC,CAAC,CAAA;SACL;QACD,OAAO;YACH,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,OAAA,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,0CAAE,YAAY,KAAI,CAAC,EAAE;SAClF,CAAA;IACL,CAAC;IAEM,4CAAgB,GAAvB,UAAwB,IAAS;QAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,IAAI,EAAE;YACN,IAAM,MAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,MAAI,EAAE;gBACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC,yBAAI,MAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAf,CAAe,CAAC,0CAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAC,CAAC,CAAA;aACpF;SACJ;QACD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAClC,CAAC;IAEM,2CAAe,GAAtB,UAAuB,IAAS;QACtB,IAAA,KAA2B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAA7E,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,QAAQ,cAAyD,CAAA;QACrF,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;YAC7B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAA;QACnE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QACzC,IAAM,GAAG,GAAQ;YACb,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC;gBAC3B,OAAO;oBACH,GAAG,EAAE,CAAC,CAAC,GAAG;oBACV,EAAE,EAAE,CAAC,CAAC,EAAE;oBACR,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,YAAY,kBAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC,CAAC;iBACtE,CAAA;YACL,CAAC,CAAC;SACL,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAA;QACjE,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,YAAY,kBAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC,CAAC,CAAA;QAC/E,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE;gBACpC,IAAI,CAAC,qBAAqB,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;aACrD;SACJ;QACD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAA;IACjC,CAAC;IAEM,4CAAgB,GAAvB,UAAwB,IAAS;QACvB,IAAA,KAA2B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAA7E,IAAI,UAAA,EAAE,IAAI,UAAA,EAAE,QAAQ,cAAyD,CAAA;QACrF,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;YAC7B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,OAAO;QACP,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;QAC9C,OAAO;QACP,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAA;QAC1C,YAAY;QACZ,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACrC,KAAK;QACL,IAAM,GAAG,GAAQ;YACb,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC;gBAC3B,OAAO;oBACH,GAAG,EAAE,CAAC,CAAC,GAAG;oBACV,EAAE,EAAE,CAAC,CAAC,EAAE;oBACR,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,YAAY,kBAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC,CAAC;iBACtE,CAAA;YACL,CAAC,CAAC;SACL,CAAA;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAA;QACjE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACrD,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,aAAa,CAAC,CAAA;QAClD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAA;IACjC,CAAC;IAED,eAAe;IACR,mDAAuB,GAA9B,UAA+B,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QACpE,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,IAAI,oBAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;YACxB,IAAI,UAAU,EAAE;gBACZ,oBAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAA;aACjC;YACD,GAAG,GAAG,4CAA6B,CAAA;SACtC;aACI;YACD,GAAG,GAAG,sCAAuB,CAAA;SAChC;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QACpC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACpB,OAAO,EAAE,IAAI,MAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IACvB,CAAC;IAEM,2CAAe,GAAtB,UAAuB,IAAS;QAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACnD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,oCAAqB,EAAE;YAC/C,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,UAAU;QACV,IAAM,KAAK,GAAG,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QAChF,OAAO;QACP,IAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC7C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,WAAW;SACzD;aAAM,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;SACpD;QACD,OAAO;QACP,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACtB,UAAU;QACV,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAA;QACnC,UAAU;QACJ,IAAA,IAAI,GAAK,oBAAO,CAAC,YAAY,CAAC,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAA/E,CAA+E;QACzF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;QACzD,KAAK;QACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;QAChE,KAAK;QACL,OAAO;YACH,GAAG,EAAE,EAAE;YACP,IAAI,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;gBACpB,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;aAC5C;SACJ,CAAA;IACL,CAAC;IAED,eAAe;IACR,+CAAmB,GAA1B,UAA2B,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAChE,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,IAAI,oBAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;YACxB,IAAI,UAAU,EAAE;gBACZ,oBAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAA;aACjC;YACD,GAAG,GAAG,qCAAsB,CAAA;SAC/B;aACI;YACD,GAAG,GAAG,qCAAsB,CAAA;SAC/B;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QACpC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACpB,OAAO,EAAE,IAAI,MAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IACvB,CAAC;IAEM,0CAAc,GAArB,UAAsB,IAAS;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;YACnD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,yBAAc,IAAI,KAAK,CAAC,EAAE,KAAK,6BAAkB,IAAI,KAAK,CAAC,EAAE,KAAK,2BAAgB,IAAI,KAAK,CAAC,EAAE,KAAK,4BAAiB;eACtI,KAAK,CAAC,EAAE,KAAK,8BAAmB,IAAI,KAAK,CAAC,EAAE,KAAK,6BAAkB,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,oCAAqB,EAAE,EAAE,WAAW;YAC3H,OAAO,EAAE,GAAG,EAAE,sBAAsB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SACrD;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK;YACnD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ;YAClC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,KAAK,CAAC,EAAE,KAAK,yBAAc,EAAE;YACpC,IAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,yBAAc,CAAC,CAAA;YAC5C,IAAI,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC1B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,mBAAmB;aACjE;SACJ;QACD,OAAO;QACP,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YAClD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAA;QAChE,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;QACpE,KAAK;QACL,OAAO;YACH,GAAG,EAAE,EAAE;YACP,IAAI,EAAE;gBACF,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;aAC5C;SACJ,CAAA;IACL,CAAC;IAEM,uCAAW,GAAlB,UAAmB,IAAS;QACxB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,OAAO;QACP,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvB,IAAM,GAAG,GAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,EAAE,CAAA;QAC9D,cAAc;QACd,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE;YACd,IAAM,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;YAC/C,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;YACvD,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,EAAE;gBACf,IAAI,CAAC,oBAAoB,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAA,CAAC,MAAM;gBACzE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;aACnC;SACJ;aAAM,EAAE,kBAAkB;YACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;SACjE;QACD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,CAAA;IACjC,CAAC;IAEM,uCAAW,GAAlB;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAA;SAC3C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,0BAAe,CAAC,CAAA;QACpD,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;YACb,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;SACpD;QACD,OAAO;QACP,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,KAAK;QACL,OAAO;YACH,GAAG,EAAE,EAAE;YAAE,IAAI,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,CAAC,IAAO,CAAC,CAAC,KAAK,EAAE,CAAA,CAAC,CAAC,CAAC;gBAC/C,IAAI,EAAE,IAAI;aACb;SACJ,CAAA;IACL,CAAC;IAED,0CAAc,GAAd;QACI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,0BAAe,CAAC,CAAA;QACpD,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;YACb,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;SACpD;QACD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QACxF,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAA;IAE5C,CAAC;IAEM,kDAAsB,GAA7B,UAA8B,EAAuB;YAArB,KAAK,WAAA,EAAE,OAAO,aAAA,EAAE,GAAG,SAAA;QAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAE9B,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,OAAO,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QAChD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC3B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,OAAO;SACrD;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,gBAAQ,CAAC,OAAO,EAAE;YAC/C,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,QAAQ;SACtD;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACvB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;SACpD;aAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACtC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,QAAQ;SACtD;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC1B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;YAChE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACzD,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM;YAClD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,OAAO;QACP,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;QACZ,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,KAAK;QACL,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAC5D,OAAO;YACH,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACjC,EAAE,EAAE,IAAI,CAAC,EAAE;aACd;SACJ,CAAA;IACL,CAAC;IAED,aAAa;IACN,0DAA8B,GAArC;QACI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAA;IAClG,CAAC;IAED,aAAa;IACN,6CAAiB,GAAxB,UAAyB,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAC9D,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,IAAI,oBAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;YACxB,IAAI,UAAU,EAAE;gBACZ,oBAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAA;aACjC;YACD,GAAG,GAAG,6CAA8B,CAAA;SACvC;aACI;YACD,GAAG,GAAG,uCAAwB,CAAA;SACjC;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QACpC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACpB,OAAO,EAAE,IAAI,MAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IACvB,CAAC;IAEM,wCAAY,GAAnB,UAAoB,EAA0C;YAAxC,KAAK,WAAA,EAAE,QAAQ,cAAA,EAAE,EAAE,QAAA,EAAE,OAAO,aAAA,EAAE,QAAQ,cAAA;QACxD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QAClD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,CAAC,EAAE,EAAE;YACzC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,QAAQ;YACnE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;YACnD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;QACrC,IAAI,WAAW,GAAG,IAAI,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAA;QAC3D,IAAI,CAAC,IAAI,EAAE,EAAE,iBAAiB;YAC1B,QAAQ,GAAG,QAAQ,IAAI,KAAK,CAAA;YAC5B,IAAI,OAAO,EAAE;gBACT,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;gBAC5D,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;aACpD;iBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC7C,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;aACpD;iBAAM,IAAI,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE;gBACvC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;gBAC3E,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM;gBAC5G,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;YACD,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC;gBACzB,KAAK,OAAA;gBACL,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;gBACb,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,IAAI,CAAC,IAAI;aACnB,CAAC,CAAA;YACF,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;SAC1C;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,8BAAmB,EAAE;YACtD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YAChE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;YAC3E,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM;YAC5G,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACxB,UAAU;QACJ,IAAA,IAAI,GAAK,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAlD,CAAkD;QAC5D,IAAI,IAAI,CAAC,8BAA8B,EAAE,GAAG,CAAC,EAAE,EAAE,SAAS;YACtD,IAAI,CAAC,oBAAoB,IAAI,CAAC,CAAA;SACjC;QACD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QACpG,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA,CAAC,OAAO;QACrC,KAAK;QACL,OAAO;YACH,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBACpC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;gBAClB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;aAClD;SACJ,CAAA;IACL,CAAC;IAEM,8CAAkB,GAAzB,UAA0B,EAAwB;YAAtB,KAAK,WAAA,EAAE,QAAQ,cAAA,EAAE,GAAG,SAAA;QAC5C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC3B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC7D,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC7D,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK;YACf,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;SAC7C;QACD,OAAO;QACP,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,oBAAoB,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;SACxE;QACD,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC/B,YAAY;QACZ,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,wBAAwB,EAAE,CAAA;SAClC;QACD,KAAK;QACL,OAAO;YACH,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE;gBAC3B,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBACpC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;aACrB;SACJ,CAAA;IACL,CAAC;IAED,SAAS;IACF,mDAAuB,GAA9B,UAA+B,IAAY,EAAE,EAAU;QACnD,IAAI,GAAG,GAAG,wCAAyB,CAAA;QACnC,IAAI,KAAK,GAAG,yCAA0B,CAAC,EAAE,CAAC,CAAA;QAC1C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QAC5C,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACpB,OAAO,EAAE,IAAI,MAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IACvB,CAAC;IAED,OAAO;IACA,6CAAiB,GAAxB,UAAyB,IAAS;QACtB,IAAA,KAAK,GAAiC,IAAI,MAArC,EAAE,OAAO,GAAwB,IAAI,QAA5B,EAAE,QAAQ,GAAc,IAAI,SAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAS;QAClD,IAAI,QAAQ,GAAyB,IAAI,CAAA;QACzC,IAAI,SAAS,GAAG,CAAC,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,OAAO,EAAE;gBACrC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;gBAC9B,SAAS,GAAG,CAAC,CAAA;gBACb,MAAK;aACR;SACJ;QACD,IAAI,QAAQ,EAAE;YACV,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAA;YAC3D,IAAI,CAAC,IAAI,EAAE;gBACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;YACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;gBACnC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;YACD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YACrC,IAAI,aAAa,GAAG,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,wCAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;aACvG;YACD,IAAI,WAAW,GAAG,KAAK,CAAA;YACvB,IAAI,CAAC,IAAI,EAAE,EAAE,iBAAiB;gBAC1B,IAAI,OAAO,EAAE;oBACT,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;iBAC7C;qBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;oBAC5D,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;iBACpD;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC7C,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA,CAAC,MAAM;iBACpD;qBAAM,IAAI,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE;oBACvC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;iBAC7C;qBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;oBAC3E,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;iBAC7C;qBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM;oBAClF,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;iBAC7C;gBACD,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC;oBACzB,KAAK,OAAA;oBACL,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;oBACb,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,IAAI,CAAC,IAAI;iBACnB,CAAC,CAAA;gBACF,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;aAC1C;iBAAM,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,8BAAmB,EAAE;gBACtD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;gBAChE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;gBAC3E,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;iBAAM,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM;gBAClF,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;aAC7C;YACD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;YACrC,IAAI,OAAO,GAAG,IAAI,+BAAqB,EAAE,CAAA;YACzC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC9B,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBAC/B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;aACxC;YAED,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;YAC7D,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,aAAa,CAAC,CAAA;YAElD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,EAAE,EAAE,CAAA;SAChI;QACD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAC9C,CAAC;IAED,SAAS;IACD,+CAAmB,GAA3B,UAA4B,OAAe;QACvC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,OAAM;SACT;QACD,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAClC,IAAI,QAAQ,GAAG,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAA;QAC1C,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACnD,IAAI,QAAQ,CAAC,WAAW,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YACxB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;aAC3D;YACD,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAC,CAAA;YACrF,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACjE,IAAI,IAAI,EAAE;gBACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC9C,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE;wBAC1C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;wBAC7B,MAAK;qBACR;iBACJ;gBACD,SAAS;gBACT,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAA;gBAC7F,WAAW;gBACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;aAClC;SACJ;IACL,CAAC;IAED,OAAO;IACP,6CAAiB,GAAjB,UAAkB,IAAS;QACf,IAAA,KAAK,GAAU,IAAI,MAAd,EAAE,GAAG,GAAK,IAAI,IAAT,CAAS;QAC3B,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAClD,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YACnC,IAAI,UAAU,GAAG,IAAI,4CAAoB,EAAE,CAAA;YAC3C,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;YAChC,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,eAAe,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAA;YACjE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBACtC,WAAW;gBACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;aAClC;YACD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,CAAA;SAChI;QACD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAC9C,CAAC;IAED,OAAO;IACP,+CAAmB,GAAnB,UAAoB,IAAS;QACjB,IAAA,GAAG,GAAK,IAAI,IAAT,CAAS;QACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACnC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,EAAE,EAAE,CAAA;IACrF,CAAC;IAEM,4CAAgB,GAAvB,UAAwB,EAAqB;YAAnB,KAAK,WAAA,EAAE,GAAG,SAAA,EAAE,KAAK,WAAA;QACvC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACnC,OAAM;SACT;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,EAAE;YACR,OAAM;SACT;aAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;YAC1C,OAAM,CAAC,OAAO;SACjB;QACD,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA,CAAC,QAAQ;QAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,UAAU,EAAE,EAAE,GAAG,KAAA,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA,CAAC,IAAI;IACtE,CAAC;IAEY,6CAAiB,GAA9B,UAA+B,EAA8E;YAA5E,KAAK,WAAA,EAAE,OAAO,aAAA,EAAE,GAAG,SAAA,EAAE,UAAU,gBAAA,EAAE,WAAW,iBAAA,EAAE,WAAW,iBAAA,EAAE,OAAO,aAAA,EAAE,MAAM,YAAA;uCAAK,OAAO;;;gBAC7G,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBAC1B,OAAO,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,OAAO,CAAC,CAAA;gBAC3C,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE;oBACxC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC3B,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA,CAAA,OAAO;iBACxC;gBACK,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;gBACnD,IAAI,CAAC,IAAI,EAAE;oBACP,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;gBACG,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;gBAC3C,IAAI,WAAW,EAAE,EAAE,IAAI;oBACnB,IAAI,CAAC,UAAU,EAAE;wBACb,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,EAAA;qBAC7C;yBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;wBAC5D,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,EAAA,CAAC,MAAM;qBACpD;yBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;wBAC7C,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,EAAA,CAAC,MAAM;qBACpD;yBAAM,IAAI,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE;wBACzC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,EAAA;qBAC7C;oBACD,OAAO,GAAG,IAAI,iBAAO,EAAE,CAAC,OAAO,CAAC;wBAC5B,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE;wBACb,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,IAAI,CAAC,IAAI;qBACnB,CAAC,CAAA;oBACF,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;iBAChD;gBACD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;oBACzC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI,8BAAmB,EAAE;oBACzD,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA,CAAC,QAAQ;iBAC1C;gBACD,aAAa;gBACb,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;gBACvB,IAAI,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,EAAE,YAAY;oBAC/C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;iBAC/B;qBAAM;oBACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;iBAClE;gBACD,OAAO;gBACP,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;gBAExB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;gBAC1C,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kBAAkB,CAAC,IAAI,CAAC,EAAE,GAAG;oBACpC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAA;iBACzC;gBACD,WAAW;gBACX,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;gBAC7B,cAAc;gBACd,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;gBAC9B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;gBAC/D,WAAW;gBACX,IAAI,CAAC,wBAAwB,EAAE,CAAA;gBAC/B,sBAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,EAAE,EAAA;;;KAC7D;IAEY,oDAAwB,GAArC,UAAsC,EAAwB;YAAtB,EAAE,QAAA,EAAE,QAAQ,cAAA,EAAE,MAAM,YAAA;;;;gBACxD,KAAK;gBACL,IAAI,QAAQ,EAAE;oBACJ,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;oBAC3C,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE;wBACzC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;qBACjC;iBACJ;gBACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,UAAA,EAAE,MAAM,QAAA,EAAE,CAAA;gBAC7C,sBAAO,EAAE,EAAA;;;KACZ;IAEY,6CAAiB,GAA9B,UAA+B,EAAiE;;YAA/D,KAAK,WAAA,EAAE,OAAO,aAAA,EAAE,GAAG,SAAA,EAAE,WAAW,iBAAA,EAAE,QAAQ,cAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAA;;;;gBACpF,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,OAAO,CAAC,CAAA;gBAC9D,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;gBACjD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;oBACnC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC3B,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA,CAAA,OAAO;iBACxC;gBACD,OAAO;gBACP,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;gBAC1B,KAAK,GAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;gBACjC,KAAK;gBACL,IAAI,OAAA,IAAI,CAAC,KAAK,0CAAE,GAAG,MAAK,QAAQ,EAAE;oBACxB,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;oBAC3C,IAAI,KAAK,IAAI,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;wBACtC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;wBACxD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;4BAChB,KAAK,GAAG,GAAG,CAAA;yBACd;qBACJ;oBACD,SAAS;oBACT,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,0BAAe,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;wBACvE,IAAI,CAAC,oBAAoB,CAAC,iBAAS,CAAC,iBAAiB,CAAC,CAAA;qBACzD;iBACJ;gBACD,KAAK;gBACL,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;gBAC1D,IAAI,CAAC,wBAAwB,EAAE,CAAA;gBAC/B,sBAAO,EAAE,EAAA;;;KACZ;IAEY,wCAAY,GAAzB,UAA0B,EAAqB;YAAnB,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,IAAI,UAAA;;;;gBACnC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,CAAA;gBACjE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;oBACnC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC3B,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA,CAAC,OAAO;iBACzC;qBAAM,IAAI,IAAI,CAAC,KAAK,KAAK,iBAAS,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;oBAChE,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;gBACK,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;gBACjD,IAAI,CAAC,IAAI,EAAE;oBACP,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,gBAAQ,CAAC,OAAO,EAAE,EAAE,QAAQ;oBACzD,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;oBACvB,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;oBACzC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;oBACzC,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;gBACK,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAA;gBAC1B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC7E,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;qBAAM,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM;oBAChE,sBAAO,EAAE,GAAG,EAAE,cAAc,EAAE,EAAA;iBACjC;gBACD,UAAU;gBACV,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,UAAU,CAAC,CAAC,CAAA;gBAC/H,KAAK;gBACL,sBAAO;wBACH,IAAI,EAAE;4BACF,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;4BACxC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;yBACvC;qBACJ,EAAA;;;KACJ;IAEM,8CAAkB,GAAzB,UAA0B,EAAc;YAAZ,KAAK,WAAA,EAAE,GAAG,SAAA;QAClC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC3B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA,CAAA,OAAO;SACxC;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,EAAnB,CAAmB,CAAC,CAAA;QACtD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACnD,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI;YACd,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,OAAO;QACP,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;SAC5C;QACD,KAAK;QACL,OAAO;YACH,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;gBACxC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;aACvC;SACJ,CAAA;IACL,CAAC;IAED,UAAU;IACH,6CAAiB,GAAxB,UAAyB,IAAY,EAAE,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAC9D,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,IAAI,oBAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;YACxB,IAAI,UAAU,EAAE;gBACZ,oBAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAA;aACjC;YACD,GAAG,GAAG,2CAA4B,CAAA;SACrC;aACI;YACD,GAAG,GAAG,qCAAsB,CAAA;SAC/B;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAA;QACpC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACpB,OAAO,EAAE,IAAI,MAAA,EAAE,EAAE,IAAA,EAAE,CAAA;IACvB,CAAC;IAEM,yCAAa,GAApB,UAAqB,EAAmB;YAAjB,GAAG,SAAA,EAAE,UAAU,gBAAA;QAClC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAI,WAAW,GAAG,CAAC,CAAA,CAAC,MAAM;QAC1B,IAAI,WAAW,GAAG,KAAK,CAAA;QACvB,IAAM,IAAI,GAAe,EAAE,CAAA;QAC3B,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA;QACtC,IAAI,IAAI,GAAQ,IAAI,CAAA;QACpB,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,KAAK,EAAE,EAAE,MAAM;YACf,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;YACjB,EAAE,GAAG,KAAK,CAAC,EAAE,CAAA;YACb,WAAW,GAAG,KAAK,CAAC,WAAW,CAAA;YAC/B,WAAW,GAAG,KAAK,CAAC,aAAa,CAAA;YACjC,IAAI,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,cAAc,GAAG,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;gBACjF,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aACtD;iBAAM;gBACH,UAAU,GAAG,CAAC,CAAA;aACjB;SACJ;aAAM;YACH,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC9B,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;SAChD;QACD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,gDAAgD;QAChD,qCAAqC;QACrC,IAAI;QACE,IAAA,IAAI,GAAK,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAlD,CAAkD;QAC5D,IAAM,GAAG,GAAQ,EAAE,CAAA;QACnB,IAAI,CAAC,WAAW,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAA;SACxD;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;SACtB;aAAM,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM;YACzD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM;YACH,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;SACzC;QACD,OAAO;QACP,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;QACtC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC5C,SAAS;QACT,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,WAAW,CAAC,CAAC,CAAA;YAC1E,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAA;SAC1C;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QAC9B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAA;IACxB,CAAC;IAEM,2CAAe,GAAtB,UAAuB,IAAS;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC3C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;QACvB,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM;YAC5G,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,KAAK;QACL,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAA;QAC5B,gBAAgB;QAChB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;QACxD,KAAK;QACL,OAAO;YACH,IAAI,EAAE;gBACF,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;gBACnB,IAAI,EAAE,IAAI,CAAC,IAAI;aAClB;SACJ,CAAA;IACL,CAAC;IAEM,yCAAa,GAApB,UAAqB,EAAa;YAAX,KAAK,WAAA,EAAE,EAAE,QAAA;QAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAChC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACxD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO;YAChD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QAC9C,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,EAAE,EAAE;YACpE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;YAChE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAA;QAC7C,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA,CAAC,QAAQ;SAC1C;aAAM,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO;YAC1F,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,sBAAsB;QACtB,IAAI,EAAE,GAAG,CAAC,CAAA;QACV,IAAI,EAAE,KAAK,wBAAa,EAAE;YACtB,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,aAAa,CAAC,CAAA;SACpD;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QAC5D,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QACpD,KAAK;QACL,OAAO,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAA;IACpD,CAAC;IAEM,4CAAgB,GAAvB,UAAwB,IAAS;QAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACxD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO;YACrD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QACvD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;QACvD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;IAClC,CAAC;IAEM,0CAAc,GAArB,UAAsB,EAAc;YAAZ,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA;QAC9B,IAAI,IAAI,GAAQ,EAAE,CAAA;QAClB,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,QAAQ,EAAE,EAAE;YACR,KAAK,iBAAS,CAAC,MAAM;gBACjB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;gBAC5B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,EAAE,CAAA;gBACnC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAA;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;gBACnD,UAAU,GAAG,oBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,yBAAc,CAAC,CAAA;gBACtD,MAAM;YACV,KAAK,iBAAS,CAAC,IAAI;gBACf,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;gBAC1B,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,EAAE,CAAA;gBACjC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;gBACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;gBAC3B,UAAU,GAAG,oBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,6BAAkB,CAAC,CAAA;gBAC1D,MAAM;YACV,KAAK,iBAAS,CAAC,KAAK,CAAC;YACrB,KAAK,iBAAS,CAAC,SAAS;gBACpB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;gBAC3B,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,GAAG,EAAE,CAAA;gBAClC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;gBAC5B,UAAU,GAAG,oBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,2BAAgB,CAAC,CAAA;gBACxD,MAAM;SACb;QACD,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,UAAU,CAAC,CAAA;QACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAChC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,MAAA,EAAE,CAAA;IAC9B,CAAC;IAED,gBAAgB;IACT,2CAAe,GAAtB,UAAuB,IAAY,EAAE,KAAa;QAC9C,IAAI,OAAO,GAAG,EAAE,CAAA;QAChB,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,QAAQ,IAAI,EAAE;YACV,KAAK,iBAAS,CAAC,MAAM;gBACjB,OAAO,GAAG,2BAAgB,CAAA;gBAC1B,KAAK,GAAG,IAAI,CAAC,WAAW,CAAA;gBACxB,MAAK;YACT,KAAK,iBAAS,CAAC,KAAK;gBAChB,OAAO,GAAG,0BAAe,CAAA;gBACzB,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;gBACvB,MAAK;YACT,KAAK,iBAAS,CAAC,IAAI;gBACf,OAAO,GAAG,yBAAc,CAAA;gBACxB,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;gBACtB,MAAK;SACZ;QACD,IAAI,YAAY,GAAG,EAAE,CAAA;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YACnB,IAAI,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAA;YACpB,IAAI,IAAI,CAAC,EAAE,EAAE;gBACT,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aAC7B;iBACI,IAAI,EAAE,IAAI,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;oBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC1C,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;wBACtD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;4BACZ,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;yBACrC;qBACJ;oBACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAS,CAAC,KAAK,KAAK,IAAI,IAAI,EAAE,KAAK,CAAC,EAAE;wBACrE,IAAI,QAAQ,GAAa,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;wBACnD,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,0CAA2B,CAAC,CAAA;wBACzD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;4BACZ,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;yBAC5B;wBACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;wBAC7C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,0CAA2B,CAAA,CAAA,YAAY;qBAC9D;yBACI;wBACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;qBACzD;iBACJ;gBACD,MAAK;aACR;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,6CAAiB,GAAxB,UAAyB,EAAU;YAAR,EAAE,QAAA,EAAE,EAAE,QAAA;QAC7B,wCAAwC;QACxC,qCAAqC;QACrC,qCAAqC;QACrC,oDAAoD;QACpD,qCAAqC;QACrC,kDAAkD;QAClD,iDAAiD;QACjD,IAAI;QACJ,iDAAiD;QACjD,eAAe;QACf,qCAAqC;QACrC,IAAI;QACJ,UAAU;QACV,sBAAsB;QACtB,0CAA0C;QAC1C,2BAA2B;QAC3B,4CAA4C;QAC5C,8DAA8D;QAC9D,iEAAiE;QACjE,8BAA8B;QAC9B,IAAI;QACJ,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAA;IAClC,CAAC;IAEM,8CAAkB,GAAzB,UAA0B,IAAS;QAC/B,IAAI,IAAI,GAAQ,IAAI,CAAA;QACpB,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,QAAQ,IAAI,CAAC,EAAE,EAAE;YACb,KAAK,iBAAS,CAAC,MAAM;gBACjB,KAAK,GAAG,IAAI,CAAC,WAAW,CAAA;gBACxB,MAAM;YACV,KAAK,iBAAS,CAAC,KAAK;gBAChB,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;gBACvB,MAAM;YACV,KAAK,iBAAS,CAAC,IAAI;gBACf,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;gBACtB,MAAM;SACb;QACD,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACrB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YAC7B,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;aAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;YAClD,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA,CAAC,WAAW;SAC7C;QACD,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACrD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,SAAS,WAAA,EAAE,EAAE,CAAA;IAC7C,CAAC;IAEM,8CAAkB,GAAzB,UAA0B,IAAS;QAC/B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;YACrC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxD,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAA;SACjC;QACD,IAAM,KAAK,GAAG,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACjD,OAAO;QACP,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACnC,mBAAmB;QACnB,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAhB,CAAgB,CAAC,CAAA;QAC5D,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,QAAQ,GAAG,CAAC,CAAA;YAChB,IAAI,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC/D,IAAM,IAAI,GAAG,IAAI,qBAAW,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YACxD,IAAI,IAAI,CAAC,IAAI,KAAK,cAAM,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBACtE,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aAC7C;YACD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAA;SACpE;aAAM;YACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;SACnC;QACD,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7D,KAAK;QACL,mBAAQ,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,oBAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;QACpF,KAAK;QACL,OAAO;YACH,IAAI,EAAE;gBACF,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBACrC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA,CAAC,CAAC,CAAC;aACjF;SACJ,CAAA;IACL,CAAC;IAED,SAAS;IACD,4CAAgB,GAAxB,UAAyB,GAAW;QAChC,mCAAmC;QACnC,uDAAuD;QACvD,iBAAiB;QACjB,0DAA0D;QAC1D,6BAA6B;QAC7B,8BAA8B;QAC9B,sEAAsE;QACtE,qEAAqE;QACrE,yDAAyD;QACzD,sEAAsE;QACtE,yEAAyE;QACzE,0EAA0E;QAC1E,YAAY;QACZ,QAAQ;QACR,KAAK;IACT,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,EAAU,EAAE,KAAU;QAC9C,IAAI,EAAE,KAAK,CAAC,EAAE;YACV,OAAO,IAAI,CAAA;SACd;QACD,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;YACnB,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE;gBAClB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;gBACrB,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE;oBACf,OAAO,KAAK,CAAA;iBACf;aACJ;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEO,0CAAc,GAAtB,UAAuB,KAAa,EAAE,EAAU,EAAE,IAAY;QAC1D,IAAM,IAAI,GAAG,IAAI,yBAAe,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5B,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA,CAAC,IAAI;IAC9D,CAAC;IAED,WAAW;IACH,kDAAsB,GAA9B,UAA+B,GAAW;QACtC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACpD,IAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YAC9B,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,SAAQ;aACX;YACD,KAAK;YACL,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAC9B,WAAW;YACX,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;YACnC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;YACzB,IAAI,CAAC,UAAU,EAAE,CAAA;YACjB,IAAI,CAAC,WAAW,EAAE,CAAA,CAAC,QAAQ;YAC3B,gBAAgB;YAChB,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC/C,IAAI,CAAC,gBAAgB,EAAE,CAAA;gBACvB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAC,CAAA;aAC7F;YACD,SAAS;YACT,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YACtB,KAAK;YACL,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,aAAa,EAAE;gBACvC,KAAK,EAAE,CAAC,CAAC,MAAM;gBACf,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC;aAC1C,CAAC,CAAA;SACL;IACL,CAAC;IAEO,kDAAsB,GAA9B,UAA+B,EAAU;QACrC,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC/B,IAAI,IAAI,CAAC,MAAM,KAAK,EAAE,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;gBAClE,KAAK,IAAI,CAAC,CAAA;aACb;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,KAAa;QACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;IAChD,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,KAAa;;QACpC,mBAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,KAAK,EAAlB,CAAkB,CAAC,0CAAE,MAAM,mCAAI,CAAC,CAAC,CAAA;IACxE,CAAC;IAEO,sCAAU,GAAlB,UAAmB,GAAW,EAAE,IAAY,EAAE,UAAkB;QAC5D,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,CAAC,WAAW;QAC1D,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QACzC,IAAI,CAAC,cAAc,GAAG;YAClB,GAAG,KAAA;YACH,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,QAAQ,EAAE,IAAI;YACd,UAAU,YAAA;SACb,CAAA;IACL,CAAC;IAEO,4CAAgB,GAAxB;QACI,IAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAA;QAC7B,IAAI,CAAC,CAAC,EAAE;YACJ,OAAO,IAAI,CAAA;SACd;QACD,OAAO;YACH,GAAG,EAAE,CAAC,CAAC,GAAG;YACV,QAAQ,EAAE,CAAC,CAAC,QAAQ;YACpB,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACjE,UAAU,EAAE,CAAC,CAAC,UAAU;SAC3B,CAAA;IACL,CAAC;IAED,SAAS;IACD,iDAAqB,GAA7B,UAA8B,GAAW;QACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,OAAM;SACT;aAAM,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;YAC3E,OAAM;SACT;QACD,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAA;QACjD,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA;QACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA,CAAC,QAAQ;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QACpD,KAAK;QACL,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,eAAe,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;IAC/D,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,GAAW,EAAE,UAAkB;QACpD,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAClC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,EAAE,EAAX,CAAW,CAAC,CAAA;QAC9C,OAAO;QACP,IAAI,CAAC,KAAK,EAAE,EAAE,YAAY;YACtB,KAAK,GAAG,IAAI,mBAAS,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;YACzC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC1B;aAAM;YACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YACvC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAA;YACtB,gBAAgB;YAChB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;SAC3D;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAEM,2CAAe,GAAtB,UAAuB,KAAgB,EAAE,UAAmB;;QACxD,YAAY;QACZ,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACnD,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;QAChB,EAAE;QACF,IAAI,QAAQ,GAAa,EAAE,CAAA;QAC3B,IAAI,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE;YAC5C,QAAQ,GAAG,OAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAjE,CAAiE,CAAC,0CAAE,IAAI,KAAI,EAAE,CAAA;SACtH;QACD,QAAQ;QACR,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI;YACrB,IAAM,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;YAClD,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;SAChE;QACD,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,EAAG,IAAI;YAC1B,IAAM,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;YACtD,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;SAChE;QACD,KAAK;QACL,IAAI,SAAS,GAAG,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QAC1D,IAAM,cAAc,GAAG,EAAE,CAAA;QACzB,SAAS,CAAC,OAAO,CAAC,UAAC,EAAE,EAAE,CAAC,IAAK,OAAA,cAAc,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAA1B,CAA0B,CAAC,CAAA;QACxD,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAA;QACjC,IAAI,QAAQ,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YAC7C,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA,CAA2B,iBAAiB;YACtE,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,IAAI;SACrC;aAAM;YACH,QAAQ,GAAG,IAAI,CAAA;SAClB;QACD,IAAM,GAAG,GAAa,EAAE,CAAA;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClD,IAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAChD,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;YAC1B,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;SAC7B;QACD,IAAM,OAAO,GAAe,EAAE,CAAA;QAC9B,GAAG,CAAC,OAAO,CAAC,UAAA,EAAE;YACV,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;YACrD,IAAI,IAAI,EAAE;gBACN,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;gBACjB,IAAI,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;gBAC7C,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBAClB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;iBACpC;qBAAM;oBACH,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA,CAAC,4BAA4B;iBACzC;gBACD,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;gBACxC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBAClB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;iBACpC;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aAClB;QACL,CAAC,CAAC,CAAA;QACF,IAAI,QAAQ,EAAE;YACV,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SACzB;QACD,OAAO,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA3C,CAA2C,CAAC,CAAA,CAAC,kBAAkB;QACtF,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAA7B,CAA6B,CAAC,CAAA;QACnD,KAAK,CAAC,UAAU,EAAE,CAAA;IACtB,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,KAAgB;QACrC,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACnD,KAAK,CAAC,SAAS,GAAG,EAAE,CAAA;YACpB,KAAK,CAAC,UAAU,EAAE,CAAA;SACrB;IACL,CAAC;IAEO,2CAAe,GAAvB,UAAwB,KAAY;QAChC,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1C,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;SAC5C;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEO,qDAAyB,GAAjC,UAAkC,GAAW,EAAE,KAAU;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE,YAAY;gBAC3C,SAAQ;aACX;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBACnB,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;oBACpB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;iBAClC;YACL,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,SAAS;IACD,gDAAoB,GAA5B,UAA6B,KAAa,EAAE,GAAW;QACnD,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC3C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAA;SACd;QACD,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YAClB,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBACf,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;gBAC9C,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;iBACzC;qBAAM,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE;oBACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;iBAClC;gBACD,OAAO,CAAC,CAAA;aACX;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAEO,gDAAoB,GAA5B,UAA6B,KAAa;QACtC,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC3C,OAAO,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,CAAA;IACvC,CAAC;IAEO,0CAAc,GAAtB,UAAuB,KAAa,EAAE,GAAW;;QAC7C,OAAO,CAAC,QAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,0CAAE,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,GAAG,EAAd,CAAc,EAAC,CAAA;IACrE,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,KAAa,EAAE,IAAY,EAAE,IAAY,EAAE,EAAU,EAAE,EAAU,EAAE,IAAY,EAAE,EAAU;QACjH,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QAC3C,IAAI,CAAC,KAAK,EAAE;YACR,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAA;SAC/C;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QAChD,IAAM,IAAI,GAAG,IAAI,+BAAqB,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QAC9E,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA;QACrD,aAAa;QACb,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SAClC;IACL,CAAC;IAED,aAAa;IACL,qDAAyB,GAAjC,UAAkC,GAAW;QACzC,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE;YACtC,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA,CAAC,SAAS;YAC5B,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,EAAE;gBAChC,SAAQ;aACX;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;YAC9C,KAAK,CAAC,KAAK,EAAE,CAAA;YACb,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAA;aAChD;iBAAM;gBACH,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;aACvC;YACD,SAAS;YACT,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;YACzD,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;SACxF;IACL,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,KAAa,EAAE,IAAY,EAAE,IAAY,EAAE,EAAU;QAC/E,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,CAAA;QACjE,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;QACD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;QACjD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;YACZ,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;SACpE;IACL,CAAC;IAED,0CAAc,GAAd,UAAe,GAAW;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;IAC/C,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAa;QACrC,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACrD,IAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QACzD,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;YACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAA;YACpB,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;SACvF;IACL,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,KAAa,EAAE,GAAW;;QACpD,IAAI,CAAC,GAAG,EAAE;YACN,OAAO,IAAI,CAAA;SACd;QACD,aAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,0CAAE,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,EAAC;IAClE,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,KAAa;;QACnC,aAAO,IAAI,CAAC,eAAe,CAAC,GAAG,0CAAG,KAAK,EAAC;IAC5C,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,KAAa;;QAClC,OAAO,OAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,0CAAE,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,MAAK,EAAE,CAAA;IACnE,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,KAAa,EAAE,IAAY,EAAE,OAAe,EAAE,EAAU,EAAE,EAAU,EAAE,IAAY,EAAE,EAAU;;QACpH,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,CAAC,EAAE;YACJ,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;SACvC;QACD,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;QACnB,IAAI,CAAC,KAAK,EAAE;YACR,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE,CAAA;SACvB;QACD,EAAE,IAAI,CAAC,aAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,0CAAG,eAAO,CAAC,KAAK,2CAAG,KAAK,KAAI,CAAC,CAAC,CAAA;QACzE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAA;QACzC,KAAK,CAAC,IAAI,CAAC,IAAI,4BAAkB,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;QAC7E,aAAa;QACb,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SAClC;IACL,CAAC;IAED,SAAS;IACD,gDAAoB,GAA5B,UAA6B,KAAa,EAAE,IAAY,EAAE,GAAW;QACjE,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACjD,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAA;SACd;QACD,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;YAClB,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBACf,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAClB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;iBACrC;qBAAM,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE;oBACxB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;iBAClC;gBACD,OAAO,CAAC,CAAA;aACX;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,aAAa;IACL,qDAAyB,GAAjC,UAAkC,GAAW;QACzC,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;YAClC,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YAC1D,IAAI,MAAM,GAAG,KAAK,CAAA;YAClB,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE;gBACf,IAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;gBACpB,IAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA,CAAC,SAAS;gBAC5B,IAAI,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,EAAE;oBACtC,SAAQ;iBACX;gBACD,MAAM,GAAG,IAAI,CAAA;gBACb,KAAK,CAAC,KAAK,EAAE,CAAA;gBACb,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClB,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAA;iBAChD;qBAAM;oBACH,OAAO,GAAG,CAAC,CAAC,CAAC,CAAA;iBAChB;gBACD,SAAS;gBACT,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAA;gBACvD,MAAM;gBACN,IAAI,CAAC,wBAAwB,EAAE,CAAA;aAClC;YACD,IAAI,CAAC,MAAM,EAAE;gBACT,SAAQ;aACX;YACD,SAAS;YACT,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAA;aAC/E;YACD,UAAU;YACV,IAAI,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;aACrC;SACJ;IACL,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,KAAa,EAAE,IAAY,EAAE,EAAU,EAAE,EAAU,EAAE,GAAY;QAC3F,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC,IAAI,EAAE;YACP,OAAM;SACT;aAAM,IAAI,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YACxD,EAAE,GAAG,CAAC,CAAA,CAAC,WAAW;SACrB;QACD,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QAC1B,OAAO;QACP,IAAM,IAAI,GAAG,IAAI,iBAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAA;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAA;QACvC,OAAO;QACD,IAAA,KAAwB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,EAArD,SAAS,eAAA,EAAE,MAAM,YAAoC,CAAA;QAC7D,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAA;SAC7C;QACD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,OAAO;QACP,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAClB,WAAW;QACX,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACtB,SAAS;QACT,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QACzC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAA;QACpC,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;SACpE;QACD,IAAI,CAAC,IAAI,CAAC,mBAAS,CAAC,kBAAkB,CAAC,CAAA;QAGvC,UAAU;QACV,IAAI,IAAI,CAAC,IAAI,IAAI,gBAAQ,CAAC,MAAM,EAAE;YAC9B,IAAI,CAAC,oBAAoB,CAAC,cAAM,CAAC,iBAAiB,CAAC,CAAA;SACtD;QAED,KAAK;QACL,mBAAQ,CAAC,WAAW,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,oBAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IACzF,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,MAAc;QACpC,IAAI,QAAQ,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,SAAS,GAAc,IAAI,CAAA;QAC1D,IAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QACvC,IAAI,IAAI,EAAE;YACN,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;YACxB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;YACpB,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAlB,CAAkB,CAAC,CAAA;SACxD;QACD,OAAO,EAAE,MAAM,QAAA,EAAE,SAAS,WAAA,EAAE,CAAA;IAChC,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,KAAa;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAA;IACtC,CAAC;IAEO,8CAAkB,GAA1B;QACI,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;QAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAA;IACnC,CAAC;IAEO,2CAAe,GAAvB;QACI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,UAAU,CAAC,CAAA;IAC7F,CAAC;IAEO,gDAAoB,GAA5B,UAA6B,KAAa,EAAE,GAAW;QACnD,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAChD,OAAO,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,CAAA;IACvC,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,KAAa,EAAE,GAAW;;QAChD,aAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,0CAAG,GAAG,EAAC;IAC7C,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,KAAa,EAAE,IAAY,EAAE,GAAW;;QAClE,aAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,0CAAE,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,EAAC;IACxE,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,KAAa;QAClC,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG,EAAE;YACN,OAAO,GAAG,CAAA;SACb;QACD,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;YACjB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,EAAE,CAAA;SACpD;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,SAAc;QACrC,IAAI,UAAU,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK,CAAA;QACtC,KAAK,IAAI,GAAG,IAAI,SAAS,EAAE;YACvB,IAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACxB,IAAI,IAAI,IAAI,eAAO,CAAC,UAAU;gBAC1B,IAAI,IAAI,eAAO,CAAC,eAAe;gBAC/B,IAAI,IAAI,eAAO,CAAC,aAAa;gBAC7B,IAAI,IAAI,eAAO,CAAC,WAAW,EAAE,EAAE,QAAQ;gBACvC,UAAU,GAAG,IAAI,CAAA;aACpB;iBAAM,IAAI,IAAI,IAAI,eAAO,CAAC,MAAM,EAAE,EAAE,SAAS;gBAC1C,MAAM,GAAG,IAAI,CAAA;aAChB;SACJ;QACD,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;SACzB;QACD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE;gBACjC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;gBAChC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;aACvC,CAAC,CAAA;SACL;IACL,CAAC;IAED,WAAW;IACH,0CAAc,GAAtB,UAAuB,EAAU;QAC7B,0DAA0D;IAC9D,CAAC;IAED,WAAW;IACH,wCAAY,GAApB,UAAqB,EAAU;;QAC3B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC/B,OAAO,IAAI,CAAA;aACd;SACJ;QACD,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5B,IAAI,OAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,0CAAE,IAAI,CAAC,WAAW,MAAK,EAAE,EAAE;gBACnD,OAAO,IAAI,CAAA;aACd;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,WAAW;IACH,yCAAa,GAArB,UAAsB,EAAU;QAC5B,0DAA0D;IAC9D,CAAC;IAEO,kDAAsB,GAA9B;QACI,IAAM,SAAS,GAAG,EAAE,CAAA;QACpB,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE;YAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;YAClC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE;gBACb,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;aAC9B;SACJ;QACD,OAAO,SAAS,CAAA;IACpB,CAAC;IAEM,0CAAc,GAArB,UAAsB,GAAW;;QAC7B,OAAO,CAAC,QAAC,IAAI,CAAC,eAAe,CAAC,UAAU,0CAAG,GAAG,EAAC,CAAA;IACnD,CAAC;IAED,UAAU;IACF,sCAAU,GAAlB,UAAmB,KAAa,EAAE,GAAW,EAAE,EAAU,EAAE,EAAU,EAAE,IAAY;QAC/E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,qBAAW,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;QACpE,aAAa;QACb,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SAC1C;QACD,QAAQ;QACR,+GAA+G;IACnH,CAAC;IAEM,oCAAQ,GAAf,UAAgB,GAAW;QACvB,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QACjC,uBAAuB;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE;YAC7D,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;SAC1C;IACL,CAAC;IAED,SAAS;IACT,uCAAW,GAAX;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAA;IAC5C,CAAC;IAEO,yCAAa,GAArB;QAAA,iBAGC;QAFG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAxB,CAAwB,CAAC,CAAA;QACpD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA,CAAC,IAAI;IAC3B,CAAC;IAEO,wCAAY,GAApB;QACI,OAAO;YACH,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;SAC5B,CAAA;IACL,CAAC;IAED,WAAW;IACH,8CAAkB,GAA1B;QACI,OAAO,IAAI,CAAC,eAAe,CAAC,eAAO,CAAC,QAAQ,CAAC,GAAG,iCAAsB,CAAA;IAC1E,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,GAAe;QAAxC,iBAoBC;QAnBG,IAAM,KAAK,GAAQ,EAAE,CAAA;QACrB,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC;YACT,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE;gBACzB,KAAK,CAAC,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE;gBAChC,KAAK,CAAC,MAAM,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;aACrC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE;gBAC/B,KAAK,CAAC,KAAK,GAAG,KAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;aACnC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;gBAClC,KAAK,CAAC,OAAO,GAAG,KAAI,CAAC,OAAO,CAAA;aAC/B;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE;gBAC9B,KAAK,CAAC,IAAI,GAAG,KAAI,CAAC,IAAI,CAAA;aACzB;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;gBACnC,KAAK,CAAC,QAAQ,GAAG,KAAI,CAAC,QAAQ,CAAA;aACjC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,OAAO,EAAE;gBACjC,KAAK,CAAC,OAAO,GAAG,KAAI,CAAC,OAAO,CAAA;aAC/B;QACL,CAAC,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,QAAQ;IACD,qCAAS,GAAhB;QACI,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QAC9E,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,YAAY,CAAA;IACnH,CAAC;IAED,OAAO;IACA,gDAAoB,GAA3B,UAA4B,GAAe,EAAE,MAAc;QAA3D,iBAEC;QADG,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,MAAM,CAAC,EAAtC,CAAsC,CAAC,CAAA;IAC5D,CAAC;IACM,kDAAsB,GAA7B,UAA8B,CAAW,EAAE,MAAc;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAA;QAC9C,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE;YACzB,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;YACrD,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;SAC1B;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,MAAM,EAAE;YAChC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;YACvD,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;SAC1B;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE;YAC/B,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAA;YACtD,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAA;SACzB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;YAClC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAC/B,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;SACrB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE;YAC9B,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YAC5B,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;SAClB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;YACnC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAChC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAA;SACtB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,OAAO,EAAE;YACjC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;YAC/B,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;SACrB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,QAAQ,EAAE;YAClC,iCAAiC;YACjC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;SAChB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAA;YACpC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;SAChB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAK,CAAC,SAAS,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAA;YAC5C,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;SAChB;QACD,OAAO,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,CAAA;IACvB,CAAC;IAED,WAAW;IACH,+CAAmB,GAA3B,UAA4B,GAAe;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;gBACrC,OAAO,KAAK,CAAA;aACf;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IACO,iDAAqB,GAA7B,UAA8B,CAAW;QACrC,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,MAAM,EAAE;YACxB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAA;SACtC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,MAAM,EAAE;YAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAA;SACtC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,KAAK,EAAE;YAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAA;SACrC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,QAAQ,EAAE,EAAE,KAAK;YACxC,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,CAAA;SACjC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,QAAQ,EAAE,EAAE,IAAI;YACvC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAA;SACvC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,IAAI,EAAE,EAAE,GAAG;YAClC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAA;SAC9B;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,SAAS,EAAE,EAAE,IAAI;YACxC,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAA;SAClC;aAAM,IAAI,CAAC,CAAC,IAAI,IAAI,aAAK,CAAC,OAAO,EAAE,EAAE,KAAK;YACvC,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,CAAA;SACjC;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IACD,cAAc;IACN,+CAAmB,GAA3B,UAA4B,GAAe,EAAE,KAAa;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;gBAC5C,OAAO,KAAK,CAAA;aACf;SACJ;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IACO,uCAAW,GAAnB,UAAoB,GAAe;QAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,oCAAqB,CAAC,CAAA;IAC/D,CAAC;IAED,OAAO;IACC,iDAAqB,GAA7B,UAA8B,EAAY,EAAE,KAAa;;QACrD,IAAI,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,MAAM;YACvB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,MAAM;YACvB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,KAAK;YACtB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,IAAI;YACrB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,QAAQ;YACzB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,IAAI;YACrB,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,SAAS;YAC1B,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,OAAO,EAAE;YAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAA;SACxC;aAAM,IAAI,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,QAAQ,EAAE;YAClC,OAAO,CAAC,QAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,aAAa,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAhB,CAAgB,EAAC,CAAA;SAC/E;aAAM,IAAI,EAAE,CAAC,IAAI,IAAI,aAAK,CAAC,UAAU,EAAE,EAAE,KAAK;YAC3C,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAA;YAC7C,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE;gBACb,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;aACjC;YACD,OAAO,EAAE,CAAC,KAAK,IAAI,KAAK,CAAA;SAC3B;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IACO,yCAAa,GAArB,UAAsB,EAAY;QAC9B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,oCAAqB,CAAC,CAAA;IAChE,CAAC;IAEM,wDAA4B,GAAnC,UAAoC,GAAe;QAC/C,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA;QACrC,IAAI,CAAC,EAAE;YACH,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;SACrC;QACD,OAAO,CAAC,CAAA;IACZ,CAAC;IAEO,yCAAa,GAArB,UAAsB,GAAW;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;QAC9B,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAA;SACtB;QACD,IAAI,CAAC,OAAO,IAAI,GAAG,CAAA;QACnB,OAAO,GAAG,CAAA;IACd,CAAC;IACO,sCAAU,GAAlB,UAAmB,GAAW;QAC1B,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAA;QAC3B,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAA;SACnB;QACD,IAAI,CAAC,IAAI,IAAI,GAAG,CAAA;QAChB,OAAO,GAAG,CAAA;IACd,CAAC;IACO,0CAAc,GAAtB,UAAuB,GAAW;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;QAC/B,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAA;SACvB;QACD,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAA;QACpB,OAAO,GAAG,CAAA;IACd,CAAC;IACO,yCAAa,GAArB,UAAsB,GAAW;QAC7B,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAA;QAC9B,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAA;SACtB;QACD,IAAI,CAAC,OAAO,IAAI,GAAG,CAAA;QACnB,OAAO,GAAG,CAAA;IACd,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,GAAW;QAChC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE;YACrB,OAAO,CAAC,CAAC,CAAA;SACZ;QACD,IAAI,CAAC,IAAI,IAAI,GAAG,CAAA;QAChB,OAAO,IAAI,CAAC,IAAI,CAAA;IACpB,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,QAAsB,EAAE,GAAW;QAAjE,iBAkCC;QAjCG,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC3D,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,QAAQ,CAAA;SAClB;QACD,IAAM,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QAC/C,IAAM,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,IAAM,KAAK,GAAa,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE;YACxB,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,GAAG,CAAC,EAArC,CAAqC,CAAC,CAAA;YACxE,OAAO,QAAQ,CAAA;SAClB;QACD,IAAM,OAAO,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;QACpD,IAAI,WAAW,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,CAAA;QACzC,OAAO,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,WAAW,IAAI,CAAC,EAAhB,CAAgB,CAAC,CAAA;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAA;YAC1C,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE;gBAC7B,IAAM,GAAG,GAAG,OAAO,CAAC,EAAE,CAAC,CAAA;gBACvB,IAAI,CAAC,GAAG,EAAE;oBACN,SAAQ;iBACX;qBAAM,IAAI,MAAM,GAAG,GAAG,EAAE;oBACrB,WAAW,IAAI,GAAG,CAAA;oBAClB,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;oBACf,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;oBACpE,MAAK;iBACR;qBAAM;oBACH,MAAM,IAAI,GAAG,CAAA;iBAChB;aACJ;SACJ;QACD,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,QAAQ,CAAC,OAAO,GAAG,CAAC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;SAChE;IACL,CAAC;IAED,gBAAgB;IACR,oDAAwB,GAAhC,UAAiC,GAAW,EAAE,GAAW;QACrD,IAAM,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACxC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAClB,OAAO,IAAI,CAAA;SACd;QACD,OAAO,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;IAC3F,CAAC;IAEO,2CAAe,GAAvB,UAAwB,IAAY,EAAE,IAAY,EAAE,GAAW;QAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9C,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;YACpC,IAAI,IAAI,EAAE;gBACN,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,IAAI,EAAd,CAAc,CAAC,CAAA;gBACjD,IAAI,IAAI,EAAE;oBACN,OAAO,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE,CAAA;iBAC3E;aACJ;SACJ;QACD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAA;IACrD,CAAC;IAEM,uCAAW,GAAlB,UAAmB,GAAW;QAC1B,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAA;QAClD,IAAI,KAAK,EAAE;YACP,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACxC,IAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YAC9C,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;gBACxC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;aACrC;SACJ;IACL,CAAC;IAEO,2CAAe,GAAvB,UAAwB,KAAqB,EAAE,IAAa;QACxD,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,KAAK,CAAC,UAAU,GAAG,KAAK,CAAA;QACxB,YAAY;QACZ,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,WAAW,CAAA;QACpC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,CAAA;QACnC,UAAU;QACV,IAAI,IAAI,EAAE;YACN,MAAM;YACN,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;YACnD,KAAK,CAAC,SAAS,GAAG,GAAG,CAAA;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;SAC1B;aAAM;YACH,SAAS;YACT,IAAM,OAAO,GAAG,GAAG,GAAG,KAAK,CAAC,SAAS,CAAA;YACrC,oBAAoB;YACpB,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC,CAAC,CAAA;SAChE;QACD,OAAO;QACP,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;IACzD,CAAC;IAED,WAAW;IACH,8CAAkB,GAA1B,UAA2B,GAAW;QAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAM;SACT;QACD,UAAU;QACV,IAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC1B,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,EAAE;YAChC,OAAM;SACT;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA,CAAC,IAAI;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAA;SACxD;QACD,QAAQ;QACR,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,KAAK,EAAE,EAAT,CAAS,CAAC,CAAC,CAAA;QACzE,OAAO;QACP,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;IAC5B,CAAC;IAED,SAAS;IACD,4CAAgB,GAAxB,UAAyB,CAAc;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;QACxD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA,CAAC,MAAM;YAC5D,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;YACtC,KAAK;YACL,mBAAQ,CAAC,WAAW,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,oBAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;YACxH,QAAQ;YACR,IAAI,SAAS,GAAG,iBAAS,CAAC,IAAI,CAAA;YAC9B,IAAI,UAAU,GAAG,kBAAU,CAAC,IAAI,CAAA;YAChC,IAAI,KAAK,CAAC,EAAE,KAAK,yBAAc,EAAE;gBAC7B,SAAS,GAAG,iBAAS,CAAC,MAAM,CAAA;gBAC5B,UAAU,GAAG,kBAAU,CAAC,kBAAkB,CAAA;aAC7C;iBAAM,IAAI,KAAK,CAAC,EAAE,KAAK,6BAAkB,EAAE;gBACxC,SAAS,GAAG,iBAAS,CAAC,IAAI,CAAA;gBAC1B,UAAU,GAAG,kBAAU,CAAC,gBAAgB,CAAA;aAC3C;iBAAM,IAAI,KAAK,CAAC,EAAE,KAAK,2BAAgB,EAAE;gBACtC,SAAS,GAAG,iBAAS,CAAC,KAAK,CAAA;gBAC3B,UAAU,GAAG,kBAAU,CAAC,iBAAiB,CAAA;aAC5C;YACD,IAAI,SAAS,KAAK,iBAAS,CAAC,IAAI,EAAE;gBAC9B,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,CAAA;gBACrD,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA,CAAC,MAAM;aAC9C;SACJ;IACL,CAAC;IAEM,+CAAmB,GAA1B,UAA2B,EAAU,EAAE,EAAU;QAC7C,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,oBAAoB;QACpB,IAAI,EAAE,KAAK,yBAAc,IAAI,2BAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACnD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAA;YAC5B,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;YACtE,sDAAsD;YACtD,yCAAyC;SAC5C;IACL,CAAC;IAED,SAAS;IACD,6CAAiB,GAAzB;QAAA,iBAyBC;QAxBG,IAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,oCAAqB,CAAC,CAAA;QAC7D,IAAI,aAAa,GAAG,CAAC,CAAA;QACrB,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QAC5E,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,CAAM,QAAQ;QAChD,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA,CAAI,QAAQ;QAChD,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,CAAG,MAAM;QAC9C,IAAI,CAAC,OAAO,CAAC,eAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,CAAC,MAAM;QAC9C,GAAG,CAAC,OAAO,CAAC,UAAA,CAAC;YACT,IAAI,CAAC,CAAC,IAAI,KAAK,eAAO,CAAC,aAAa,EAAE,EAAE,MAAM;aAC7C;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,eAAO,CAAC,cAAc,EAAE,EAAE,MAAM;gBAClD,aAAa,IAAI,CAAC,CAAC,KAAK,CAAA;aAC3B;iBAAM;gBACH,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAA;aAClC;QACL,CAAC,CAAC,CAAA;QACF,KAAK;QACL,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAA;QACpF,IAAI,UAAU,IAAI,aAAa,IAAI,YAAY,IAAI,eAAe,EAAE;YAChE,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,CAAC,CAAA;SACrG;QACD,SAAS;QACT,IAAI,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACzC,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAA;SAC1C;IACL,CAAC;IAEO,iDAAqB,GAA7B,UAA8B,KAAa;QACvC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAM,OAAO,GAAiB,EAAE,CAAA;QAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,CAAC;YACjB,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;gBACxB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;aACzB;QACL,CAAC,CAAC,CAAA;QACF,OAAO,OAAO,CAAA;IAClB,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,KAAa,EAAE,IAAY;QAClD,IAAM,OAAO,GAAkC,EAAE,CAAA;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAM,KAAK,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAA;SAC5C;QACD,OAAO,OAAO,CAAA;IAClB,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,KAAa;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,EAAE;YAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAA;SACpC;QACD,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;IACjE,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,KAAa,EAAE,GAAW;QAClD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAA;SACd;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE;YAC3B,OAAO,IAAI,CAAA;SACd;QACD,KAAK,CAAC,EAAE,IAAI,CAAC,CAAA;QACb,KAAK,CAAC,cAAc,EAAE,CAAA;QACtB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACrC,OAAO,KAAK,CAAA;IAChB,CAAC;IAEO,+CAAmB,GAA3B,UAA4B,IAAmB,EAAE,KAAe;QAC5D,IAAI,KAAK,CAAC,EAAE,IAAI,yBAAc,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAA;YAClD,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,eAAe,EAAE;gBACzC,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,UAAU,EAAE,IAAI,CAAC,UAAU;aAC9B,CAAC,CAAA,CAAC,IAAI;SACV;aAAM,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE;YAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAA;YAClD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAClD,IAAI,CAAC,WAAW,CAAC,kBAAU,CAAC,eAAe,EAAE;gBACzC,GAAG,EAAE,IAAI,CAAC,IAAI;gBACd,UAAU,EAAE,IAAI,CAAC,UAAU;aAC9B,CAAC,CAAA,CAAC,IAAI;SACV;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;IACnE,CAAC;IAED,WAAW;IACH,2CAAe,GAAvB,UAAwB,EAAU;QAC9B,KAAK,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAClC,IAAI,MAAM,KAAK,wBAAa,EAAE;gBAC1B,SAAQ;aACX;iBAAM,IAAI,MAAM,KAAK,wBAAa,EAAE;gBACjC,MAAM,GAAG,CAAC,CAAA,CAAC,eAAe;aAC7B;YACD,IAAI,MAAM,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,WAAW,EAAE,CAAA;aACrB;SACJ;IACL,CAAC;IAEO,8CAAkB,GAA1B,UAA2B,GAAW,EAAE,IAAa;QACjD,iBAAiB;QACjB,2DAA2D;QAC3D,6CAA6C;QAC7C,2BAA2B;QAC3B,yDAAyD;QACzD,kBAAkB;QAClB,0BAA0B;QAC1B,kCAAkC;QAClC,mBAAmB;QACnB,sDAAsD;QACtD,oDAAoD;QACpD,qCAAqC;QACrC,0CAA0C;QAC1C,2BAA2B;QAC3B,iFAAiF;QACjF,oBAAoB;QACpB,uBAAuB;QACvB,sCAAsC;QACtC,gBAAgB;QAChB,YAAY;QACZ,oBAAoB;QACpB,8BAA8B;QAC9B,8DAA8D;QAC9D,kDAAkD;QAClD,gDAAgD;QAChD,yEAAyE;QACzE,YAAY;QACZ,oCAAoC;QACpC,oBAAoB;QACpB,QAAQ;QACR,IAAI;QACJ,qBAAqB;QACrB,yFAAyF;QACzF,IAAI;IACR,CAAC;IAED,SAAS;IACD,2CAAe,GAAvB,UAAwB,EAAU;QAC9B,gBAAgB;QAChB,kBAAkB;QAClB,IAAI;QACJ,+CAA+C;IACnD,CAAC;IAEO,4CAAgB,GAAxB,UAAyB,IAAY,EAAE,EAAU;QAC7C,mBAAmB;QACnB,gDAAgD;QAChD,IAAI;QACJ,mBAAmB;QACnB,sBAAsB;QACtB,2CAA2C;QAC3C,6BAA6B;QAC7B,iBAAiB;QACjB,QAAQ;QACR,yBAAyB;QACzB,yBAAyB;QACzB,yDAAyD;QACzD,+BAA+B;QAC/B,sDAAsD;QACtD,QAAQ;QACR,KAAK;QACL,sBAAsB;QACtB,0DAA0D;QAC1D,mDAAmD;QACnD,6CAA6C;IACjD,CAAC;IAEO,qDAAyB,GAAjC,UAAkC,EAAU;QACxC,mBAAmB;QACnB,sBAAsB;QACtB,UAAU;QACV,wGAAwG;QACxG,yCAAyC;QACzC,gBAAgB;QAChB,2CAA2C;QAC3C,yBAAyB;QACzB,yDAAyD;QACzD,gDAAgD;QAChD,gCAAgC;QAChC,gEAAgE;QAChE,mEAAmE;QACnE,sBAAsB;QACtB,iDAAiD;QACjD,YAAY;QACZ,QAAQ;QACR,KAAK;QACL,sBAAsB;QACtB,yDAAyD;QACzD,sBAAsB;QACtB,+DAA+D;QAC/D,kDAAkD;QAClD,KAAK;QACL,6CAA6C;IACjD,CAAC;IAEO,yCAAa,GAArB,UAAsB,QAAa,EAAE,KAAY;QAC7C,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACnB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC,CAAA;SAC9B;QACD,IAAI,GAAG,GAAa,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;QAC/B,OAAO,GAAG,GAAG,EAAE,EAAE;YACb,GAAG,IAAI,CAAC,CAAA;YACR,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,EAAD,CAAC,CAAC,CAAA;YAC7B,IAAI,EAAE,GAAG,KAAK,CAAA;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,KAAK,GAAG,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;gBACnD,IAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAA;gBACxB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACZ,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBACpB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACf,EAAE,GAAG,IAAI,CAAA;iBACZ;aACJ;YACD,IAAI,EAAE,EAAE;gBACJ,MAAK;aACR;YACD,GAAG,GAAG,EAAE,CAAA;SACX;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,eAAe;IACR,8CAAkB,GAAzB,UAA0B,IAAY;QAClC,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;YACtC,IAAI,CAAC,IAAI,EAAE;gBACP,SAAQ;aACX;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAC1B,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;oBAC1C,OAAO,IAAI,CAAA;iBACd;aACJ;SACJ;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,WAAW;IACJ,2CAAe,GAAtB;QACI,IAAM,IAAI,GAAG;YACT,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;YACzC,IAAI,kBAAQ,EAAE,CAAC,IAAI,CAAC,aAAK,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC;SAC3C,CAAA;QACD,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAA;QACvC,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,MAAM,EAAE;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;SAC5B,CAAC,CAAA;IACN,CAAC;IAED,OAAO;IACC,yCAAa,GAArB,UAAsB,GAAW;QAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC,EAAE;YACxB,OAAO,CAAC,CAAC,CAAA;SACZ;QACD,IAAI,CAAC,OAAO,IAAI,GAAG,CAAA;QACnB,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACrE,OAAO,IAAI,CAAC,OAAO,CAAA;IACvB,CAAC;IAED,4BAA4B;IACrB,gDAAoB,GAA3B,UAA4B,OAAkB,EAAE,GAAe;QAAf,oBAAA,EAAA,OAAe;QAC3D,IAAM,eAAe,GAAG,oBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;QACtD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC;YAC/B,IAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAb,CAAa,CAAC,CAAA;YACrD,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,cAAM,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAA;QAC/E,CAAC,CAAC,CAAA;QACF,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;YACnB,IAAI,CAAC,wBAAwB,EAAE,CAAA;SAClC;aACI,IAAI,OAAO,KAAK,iBAAS,CAAC,aAAa,EAAE,EAAC,YAAY;YACvD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;SAC5C;IACL,CAAC;IAEM,gDAAoB,GAA3B,UAA4B,IAAY,EAAE,GAAe;QAAf,oBAAA,EAAA,OAAe;QACrD,IAAM,eAAe,GAAG,oBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;QACtD,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAA,CAAC;YAC/B,IAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAb,CAAa,CAAC,CAAA;YACrD,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAA;QAC1C,CAAC,CAAC,CAAA;QACF,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAA;YACpB,IAAI,CAAC,wBAAwB,EAAE,CAAA;SAClC;IACL,CAAC;IAEM,iDAAqB,GAA5B,UAA6B,IAAgB;QACzC,IAAM,CAAC,GAAG,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAA;QACpD,IAAM,CAAC,GAAG,qBAAS,CAAC,SAAS,CAAC,CAAC,EAAE,qCAAsB,CAAC,CAAA;QACxD,OAAO,qBAAS,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC7C,CAAC;IAEM,uCAAW,GAAlB;QACI,OAAO,IAAI,CAAC,QAAQ,CAAA;IACxB,CAAC;IAEM,0CAAc,GAArB,UAAsB,SAAiB;QACnC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAA;SACpE;IACL,CAAC;IAED,kBAAkB;IACX,qDAAyB,GAAhC,UAAiC,KAAa;QAC1C,IAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC5C,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACpC,IAAI,GAAG,GAAG,EAAE,CAAA;QACZ,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;YACpB,IAAI,GAAG,EAAE;gBACL,GAAG,IAAI,GAAG,CAAA;aACb;YACD,GAAG,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;SACnC;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEM,yCAAa,GAApB,UAAqB,SAA6C;QAA7C,0BAAA,EAAA,qCAA6C;QAC9D,IAAM,MAAM,GAAG,oBAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAChD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;QACpC,oBAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC9B,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED,SAAS;IACF,wCAAY,GAAnB,UAAoB,EAAU,EAAE,GAAc;QAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QAClD,IAAM,CAAC,GAAG,oBAAO,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACjD,IAAI,GAAG,EAAE;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,IAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;gBACjB,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE;oBACjC,OAAO,KAAK,CAAA;iBACf;aACJ;YACD,OAAO,IAAI,CAAA;SACd;aAAM;YACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAA;SACrC;IACL,CAAC;IAEM,8CAAkB,GAAzB;QACI,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,oCAAqB,CAAC,CAAA;QAC9D,OAAO,CAAC,CAAC,UAAU,CAAA;IACvB,CAAC;IAED,SAAS;IACF,sCAAU,GAAjB,UAAkB,EAAU;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,EAAE,EAAZ,CAAY,CAAC,CAAA;IAChD,CAAC;IAEM,wCAAY,GAAnB,UAAoB,QAAgB;QAChC,IAAM,GAAG,GAAG,oBAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QAClC,oBAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAA;IACvE,CAAC;IAEO,yCAAa,GAArB;QACI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACzC,CAAC;IAEa,qCAAS,GAAvB;;;gBACI,MAAM;gBACN,IAAI,oBAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;oBACnE,oBAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;iBAChE;gBACD,MAAM;gBACN,IAAI,oBAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;oBACpE,oBAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;iBACtD;gBACD,MAAM;gBACN,IAAI,oBAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;oBACpE,oBAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;iBACtD;;;;KACJ;IAED,aAAa;IACL,qCAAS,GAAjB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAChC,IAAI,QAAQ,CAAC,aAAa,EAAE;gBACxB,IAAI,KAAK,GAAG,oBAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAA;gBAC3C,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;oBACnB,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC1C,IAAI,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;wBAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC5C,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAQ,CAAA;4BACnC,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,CAAC,aAAa,EAAE;gCACzC,OAAM;6BACT;yBACJ;qBACJ;iBACJ;gBACD,OAAO;gBACP,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAA;gBAC3B,oBAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;aAC7C;SACJ;IACL,CAAC;IAED,SAAS;IACF,oCAAQ,GAAf;;QACI,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;QACrB,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;QACxC,IAAM,GAAG,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE;YAClB,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;YACzB,IAAM,IAAI,eAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAAE,OAAO,mCAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC5D,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,OAAO,CAAC,UAAA,CAAC;gBACjB,IAAI,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;oBACjB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;iBACvB;YACL,CAAC,EAAC;SACL;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,SAAS;IACT,yCAAa,GAAb,UAAc,MAAc,EAAE,KAAa;QACvC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE;gBAClC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAA;gBAClC,MAAM,GAAG,IAAI,CAAA;gBACb,MAAK;aACR;SACJ;QACD,IAAI,CAAC,MAAM,EAAE;YACT,IAAI,IAAI,GAAG,IAAI,uBAAa,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3C,IAAI,CAAC,MAAM,IAAI,KAAK,CAAA;YACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SAC7B;QACD,oBAAO,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAC3C,UAAU;QACN,IAAA,KAAgB,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAnD,GAAG,SAAA,EAAE,IAAI,UAA0C,CAAA;QACzD,IAAI,CAAC,GAAG,EAAE;YACN,IAAI,SAAS,GAAG,oBAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAA;YAC/C,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC/B,oBAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAA;YACvC,oBAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAA;YAChE,WAAW,CAAC,IAAI,CAAC,mBAAS,CAAC,qBAAqB,CAAC,CAAA;SACpD;IACL,CAAC;IAED,MAAM;IACN,4CAAgB,GAAhB,UAAiB,IAAS;QACtB,IAAI,IAAI,GAAkB,IAAI,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;gBACnC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;gBACzB,MAAK;aACR;SACJ;QACD,IAAM,UAAU,GAAG,CAAC,CAAA;QACpB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACnC,8BAA8B;YAC9B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;YACf,IAAI,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;YACnE,IAAI,MAAM,GAAG,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACxC,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACxD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;YACrC,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAChD,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACpE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YAEzC,IAAI,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,gBAAgB,EAAE,aAAa,CAAC,KAAK,CAAC,CAAA;YAC5E,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC1C,IAAI,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACjE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAA;YAE1D,IAAI,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACpD,IAAI,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC,CAAA;YAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC9C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;aAC9D;YACD,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,IAAI,CAAC,MAAM,IAAI,UAAU,CAAA;YACzB,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,MAAA,EAAE,EAAE,CAAA;SAC5B;QACD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAA;IACxB,CAAC;IAED,MAAM;IACN,0CAAc,GAAd,UAAe,IAAS;QACZ,IAAA,KAAK,GAAS,IAAI,MAAb,EAAE,EAAE,GAAK,IAAI,GAAT,CAAS;QAC1B,IAAI,aAAa,GAAG,IAAI,CAAA;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;gBAC9B,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;gBAClC,MAAK;aACR;SACJ;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,aAAa,CAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACzB,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,CAAA;IAC1F,CAAC;IAED,MAAM;IACN,kDAAsB,GAAtB,UAAuB,IAAS;QACpB,IAAA,KAAK,GAAgC,IAAI,MAApC,EAAE,OAAO,GAAuB,IAAI,QAA3B,EAAE,GAAG,GAAkB,IAAI,IAAtB,EAAE,WAAW,GAAK,IAAI,YAAT,CAAS;QACjD,IAAI,QAAQ,GAAG,IAAI,CAAA;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,EAAE;gBAC9B,QAAQ,GAAG,IAAI,CAAA;gBACf,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAA;gBACrB,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAA;aACnC;SACJ;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAA;QACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAQ,CAAA;gBAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE;oBAC9C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;oBACf,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAA;oBAC9B,IAAI,CAAC,UAAU,EAAE,CAAA;oBACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;oBACvB,MAAK;iBACR;aACJ;SACJ;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAU,CAAC,qBAAqB,EAAE,EAAE,OAAO,SAAA,EAAE,GAAG,KAAA,EAAE,SAAS,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAA;QAC/G,IAAM,SAAS,GAAG,oBAAO,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QAC5F,SAAS,CAAC,UAAU,EAAE,CAAA;QACtB,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAA;QACjC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAA;IAClD,CAAC;IAED,MAAM;IACN,mDAAuB,GAAvB;QACI,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;aACpB;SACJ;QACD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,MAAA,EAAE,EAAE,CAAA;IACxC,CAAC;IAED,QAAQ;IACR,8CAAkB,GAAlB,UAAmB,IAAS;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE;gBAC3C,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,CAAA;aAC3E;SACJ;QACD,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAA;IACrC,CAAC;IAED,MAAM;IACN,mDAAuB,GAAvB,UAAwB,IAAS;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,SAAS,EAAE;gBACjD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAA;aACvF;SACJ;QACD,OAAO,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAA;IACrC,CAAC;IAED,YAAY;IACZ,+CAAmB,GAAnB;QACI,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAA;IACxE,CAAC;IAED,UAAU;IACV,4CAAgB,GAAhB,UAAiB,OAAiB;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;QAClD,IAAI,IAAI,GAAG,qCAAsB,GAAG,KAAK,GAAG,2CAA4B,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;QACzF,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,wCAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACjE,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,qCAAsB,CAAC,GAAG,IAAI,EAAE;YAC7C,OAAM;SACT;QACD,IAAI,IAAI,GAAG,IAAI,4CAAoB,EAAE,CAAA;QACrC,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAA;QAC3B,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;QAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3B,IAAI,CAAC,YAAY,CAAC,kBAAU,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAC3D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;IAC1C,CAAC;IACO,4CAAgB,GAAxB,UAAyB,EAAU;QAC/B,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QAC1C,OAAO,KAAK,CAAA;IAChB,CAAC;IACO,4CAAgB,GAAxB,UAAyB,EAAU;QAC/B,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;IAC7D,CAAC;IACD,0CAAc,GAAd;QACI,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAx2IgB,iBAAiB;QADrC,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;OACR,iBAAiB,CAy2IrC;IAAD,wBAAC;CAz2ID,AAy2IC,CAz2I8C,EAAE,CAAC,SAAS,GAy2I1D;kBAz2IoB,iBAAiB;AA22ItC,WAAW;AACX;IAKI;QAHQ,aAAQ,GAAU,EAAE,CAAA;QACpB,UAAK,GAAW,CAAC,CAAA;QAGrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC,CAAA;QACtF,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA;IAC9E,CAAC;IAED,2BAAK,GAAL,UAAM,KAAa;QACf,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACjC,IAAI,CAAC,IAAI;YAAE,OAAM;QACT,IAAA,KAAK,GAAa,IAAI,MAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAS;QAE9B,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;YACnB,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBACrB,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBACtB,SAAQ;aACX;YACD,UAAU,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;SACzC;QACD,UAAU,CAAC,QAAQ,CAAC,cAAc,GAAG,oBAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,CAAA;QACnE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;QAClB,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QAC5D,QAAQ,CAAC,MAAM,EAAE,CAAA;IACrB,CAAC;IAED,OAAO;IACP,0BAAI,GAAJ;QACI,IAAM,KAAK,GAAG,oBAAO,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;QACnC,IAAM,MAAM,GAAG,oBAAO,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QAC3C,IAAM,IAAI,GAAG,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAA;QAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxB,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;IACrF,CAAC;IAED,UAAU;IACV,0BAAI,GAAJ;QACI,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;QACpC,IAAI,KAAK,GAAG,CAAC;YAAE,KAAK,GAAG,CAAC,CAAA;QACxB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACrB,CAAC;IAED,WAAW;IACX,6BAAO,GAAP;QACI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QAC1B,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA;QACnE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACrB,CAAC;IAED,OAAO;IACP,2BAAK,GAAL;QACI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;QAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA;QACd,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;QACjF,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAChE,CAAC;IAED,wBAAE,GAAF,UAAG,KAAa;QACZ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;QAC9D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACrB,CAAC;IACL,kBAAC;AAAD,CA/DA,AA+DC,IAAA;AAED,IAAI,UAAU,EAAE;IACZ,MAAM,CAAC,aAAa,CAAC,GAAG,IAAI,WAAW,EAAE,CAAA;CAC5C", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BUI<PERSON>_MAIN_NID, BUILD_WALL_NID, CIT<PERSON>_MAIN_NID, INIT_RES_OUTPUT, MAIN_CITY_MARCH_SPEED, DEFAULT_BT_QUEUE_COUNT, POLICY_SLOT_CONF, CITY_FORT_NID, IN_DONE_BT_GOLD, ARMY_PAWN_MAX_COUNT, BUILD_BARRACKS_NID, INIT_RES_CAP, PAWN_SLOT_CONF, EQUIP_SLOT_CONF, BUILD_SMITHY_NID, BUILD_GRANARY_NID, BUILD_WAREHOUSE_NID, BUILD_HOSPITAL_NID } from \"../../common/constant/Constant\"\nimport { ArmyShortInfo, TreasureInfo } from \"../../common/constant/DataType\"\nimport { ArmyState, CEffect, CType, NotifyType, PawnState, PawnType, StudyType, TCType, TCusCType } from \"../../common/constant/Enums\"\nimport EventType from \"../../common/event/EventType\"\nimport { gameHpr } from \"../../common/helper/GameHelper\"\nimport { mapHelper } from \"../../common/helper/MapHelper\"\nimport { taHelper } from \"../../common/helper/TaHelper\"\nimport AreaObj from \"../area/AreaObj\"\nimport ArmyObj from \"../area/ArmyObj\"\nimport BuildObj from \"../area/BuildObj\"\nimport PawnObj from \"../area/PawnObj\"\nimport CEffectObj from \"../common/CEffectObj\"\nimport CTypeObj from \"../common/CTypeObj\"\nimport EquipInfo from \"../main/EquipInfo\"\nimport { GUIDE_CONFIG, GuideTagType } from \"./GuideConfig\"\nimport NoviceAreaObj from \"./NoviceAreaObj\"\nimport NoviceBTCityObj from \"./NoviceBTCityObj\"\nimport NoviceBTObj from \"./NoviceBTObj\"\nimport NovicePawnSlotObj from \"./NovicePawnSlotObj\"\nimport { NOVICE_BUILD_CREATE_MUL, NOVICE_BUILD_SPEED_MUL, NOVICE_CURE_RES_PARAM_MAP, NOVICE_CURE_TIME_PARAM_MAP, NOVICE_DEFAULT_GOLD_COUNT, NOVICE_DEFAULT_WARTOKEN_COUNT, NOVICE_EQUIP_SLOTS_FIRST_ID, NOVICE_FIRST_BATTLE_ENEMY_HP, NOVICE_FIRST_BATTLE_PAWN_POINT, NOVICE_FIRST_BUILD_CREATE_MUL, NOVICE_FIRST_FORGE_SPEED_MUL, NOVICE_FIRST_PAWN_SLOTS, NOVICE_FIRST_RECRUIT_SPEED_MUL, NOVICE_FIRST_STONE_TREASURE_ID, NOVICE_FORGE_SPEED_MUL, NOVICE_GO_HOSPITAL_CHANCE, NOVICE_HOSPITAL_SPEED_MUL, NOVICE_INJURY_LV_REDUCE_RATE, NOVICE_INJURY_MAX_RATE, NOVICE_LAND_TREASURE_REWARD_MAP, NOVICE_MAINCITY_INDEX, NOVICE_MAINCITY_OTHER_INDEXS, NOVICE_MAINCITY_POINTS, NOVICE_MAIN_MAX_LEVEL, NOVICE_MAP_SIZE, NOVICE_POLICY_SLOTS, NOVICE_RECRUIT_SPEED_MUL } from \"./NoviceConfig\"\nimport NoviceDrillPawnObj from \"./NoviceDrillPawnObj\"\nimport NoviceMarchObj from \"./NoviceMarchObj\"\nimport NoviceOutputObj from \"./NoviceOutputObj\"\nimport NovicePawnLevelingObj from \"./NovicePawnLevelingObj\"\nimport NovicePolicyObj from \"./NovicePolicyObj\"\nimport MapCellObj from \"../main/MapCellObj\"\nimport NoviceEquipSlotObj from \"./NoviceEquipSlotObj\"\nimport PortrayalInfo from \"../common/PortrayalInfo\"\nimport NoviceHeroSlotObj from \"./NoviceHeroSlotObj\"\nimport NoviceEnemyObj from \"./NoviceEnemyObj\"\nimport NoviceRecordObj from \"./NoviceRecordObj\"\nimport NovicePawnCureInfoObj, { NoviceInjuryPawnInfo } from \"./NovicePawnCureInfoObj\"\nimport { IFighter } from \"../../common/constant/Interface\"\nimport TaskCondObj from \"../common/TaskCondObj\"\nimport { viewHelper } from \"../../common/helper/ViewHelper\"\n\n/**\n * 新手村模块\n */\*************('novice_server')\nexport default class NoviceServerModel extends mc.BaseModel {\n\n    private isRunning: boolean = false\n    // 玩家数据\n    private puid: string = ''\n    private lastOutputTime: number = 0\n    private outputInterval: number = 0\n    private cereal: NoviceOutputObj = new NoviceOutputObj()\n    private timber: NoviceOutputObj = new NoviceOutputObj()\n    private stone: NoviceOutputObj = new NoviceOutputObj()\n    private expBook: number = 0\n    private iron: number = 0\n    private upScroll: number = 0\n    private fixator: number = 0\n    private cerealConsume: number = 0\n    private stamina: number = 0\n    private gold: number = 0\n    private warToken: number = 0\n    private ownCells: { [key: number]: NoviceAreaObj } = {}\n    private btQueues: NoviceBTObj[] = []\n    private resetPolicyNeedGold: number = 0\n    private effects: any = {}\n    private merchants: any[] = []\n    private towerLvMap: any = {}\n    private configPawnMap: { [key: number]: any } = {}\n    private equips: EquipInfo[] = []\n    private currForgeEquip: any = null\n    private guideTasks: { id: number, progress: number }[] = []//当前任务列表\n    private triggerTasks: { [key: number]: boolean } = {}//已触发任务列表\n    private pawnSlots: { [key: number]: NovicePawnSlotObj } = {}//当前士兵槽位列表\n    private policySlots: { [key: number]: NovicePolicyObj } = {} //当前政策槽位列表\n    private equipSlots: { [key: number]: NoviceEquipSlotObj } = {} //当前装备槽位列表\n    private heroSlots: NoviceHeroSlotObj[] = [] //当前英雄槽位列表\n    private portrayals: PortrayalInfo[] = [] //拥有的画像列表\n    private freeRecruitPawnCount: number = 0 //免费招募士兵数量\n    private drillHistory: { [key: number]: number } = {}//历史招募士兵数量\n    private injuryPawns: NoviceInjuryPawnInfo[] = [] // 可治疗的伤兵\n    private curingPawns: NovicePawnCureInfoObj[] = [] // 治疗中的伤兵\n    private pawnDeadTimesMap: { [key: number]: number } = {}//伤兵死亡次数   \n    // 世界\n    private areas: NoviceAreaObj[] = []\n    private armysMap: any = {}\n    private marchs: NoviceMarchObj[] = [] //当前行军列表\n    private avoidWarAreas: { [key: number]: number } = {}\n    private battleDist: { [key: number]: string[] } = {}\n    public armyAutoBackIndexMap: { [key: string]: number } = {}\n    private pawnLvingQueues: { map: { [key: number]: NovicePawnLevelingObj[] }, pawnUIDMap: { [key: string]: string } } = { map: {}, pawnUIDMap: {} }\n    private drillPawnQueues: { [key: number]: { [key: string]: NoviceDrillPawnObj[] } } = {}\n    private btCityQueues: NoviceBTCityObj[] = []\n    private enemyEquipMap: { [key: number]: { id: number, attrs: number[][] } } = {}\n    public noviceEnemyObj: NoviceEnemyObj = new NoviceEnemyObj()//敌人\n    private battleRecordList: NoviceRecordObj[] = []//战斗记录\n    private readonly battleRecordMaxCount: number = 10//战斗记录最大数量\n    public attackEnemyIndexMap: { [key: number]: boolean } = {}//攻击过的敌人下标\n    public battleCountWithEnemy: number = 0//和敌人战斗次数\n    public serverStartTime: number = 0//服务器启动时间\n    public isBuildInDone: boolean = false//建造立即完成完成，新手村任务\n\n    private saveElapsed: number = 0\n    private firstBattleAreaInfo: any = null\n\n    private enemyUid: string = ''\n    private enemyName: string = ''\n    private enemyCells: { [key: number]: NoviceAreaObj } = {}\n    public defenseBattleIdx: number = -1\n    public castleBattleIdx: number = -1\n    public castleBattleStart = false\n    public lastOccupyPawnRes: any = {} //最后一次攻占要塞的士兵资源总和\n\n    private manulBattles: { [key: number]: { index: number, attacker: string } } = {}\n\n    private hasTreasure: boolean = false //是否有宝箱\n    private inited = false\n    public tempTreasureIronCount: number = 0//宝箱开出的铁\n    private tempGuideTaskProgress: { [key: number]: number } = {}//临时记录进度任务\n\n    public onCreate() {\n    }\n\n    public init(uid: string, mapJson: any) {\n        this.puid = uid\n        const now = Date.now()\n        const data = this.getSaveData(uid) || {}\n        const areas = data.areas || {}\n        // 初始化世界信息\n        this.towerLvMap = {}\n        this.configPawnMap = data.configPawnMap || {}\n        this.currForgeEquip = data.currForgeEquip\n        this.guideTasks = data.guideTasks || []\n        this.triggerTasks = data.triggerTasks || {}\n        this.equips = (data.equips || []).map(m => new EquipInfo().fromSvr(m))\n        this.areas = []\n        this.ownCells = {}\n        this.enemyCells = {}\n        this.enemyUid = data.enemyUid || this.noviceEnemyObj.getEnemyUID()\n        this.enemyName = data.enemyName || assetsMgr.lang('guideText.enemy_name')\n        this.armysMap = {}\n        this.marchs = (data.marchs || []).map(m => new NoviceMarchObj().fromDB(m))\n        this.avoidWarAreas = data.avoidWarAreas || {}\n        this.battleDist = {}\n        this.armyAutoBackIndexMap = data.armyAutoBackIndexMap || {}\n        this.btQueues = (data.btQueues || []).map(m => new NoviceBTObj().fromDB(m))\n        this.btCityQueues = (data.btCityQueues || []).map(m => new NoviceBTCityObj().fromDB(m))\n        this.enemyEquipMap = data.enemyEquipMap || {}\n        this.firstBattleAreaInfo = data.firstBattleAreaInfo\n        this.initPawnLvingQueues(data.pawnLvingQueues || {})\n        this.initDrillPawnQueues(data.drillPawnQueues || {})\n        this.defenseBattleIdx = data.defenseBattleIdx || -1\n        this.castleBattleIdx = data.castleBattleIdx\n        this.lastOccupyPawnRes = data.lastOccupyPawnRes || {}\n        this.manulBattles = data.manulBattles || {}\n        this.castleBattleStart = data.castleBattleStart || false\n        this.pawnSlots = this.getPawnSlots(data.pawnSlots)\n        this.policySlots = this.getPolicySlots(data.policySlots)\n        this.equipSlots = this.getEquipSlots(data.equipSlots)\n        this.portrayals = (data.portrayals || []).map(m => new PortrayalInfo().fromSvr(m))\n        this.heroSlots = this.getHeroSlots(data.heroSlots)\n        this.freeRecruitPawnCount = data.freeRecruitPawnCount || 0\n        this.drillHistory = data.drillHistory || {}\n        this.battleRecordList = (data.battleRecordList || []).map(m => new NoviceRecordObj().fromDB(m))\n        this.gold = data.hasOwnProperty('gold') ? data.gold : NOVICE_DEFAULT_GOLD_COUNT\n        this.warToken = data.hasOwnProperty('warToken') ? data.warToken : NOVICE_DEFAULT_WARTOKEN_COUNT\n        this.injuryPawns = (data.injuryPawns || []).map(m => new NoviceInjuryPawnInfo().fromSvr(m))\n        this.curingPawns = (data.curingPawns || []).map(m => new NovicePawnCureInfoObj().fromSvr(m))\n        this.pawnDeadTimesMap = data.pawnDeadTimesMap || {}\n        this.attackEnemyIndexMap = data.attackEnemyIndexMap || {}\n        this.battleCountWithEnemy = data.battleCountWithEnemy || 0\n        this.serverStartTime = data.serverStartTime || now\n        this.isBuildInDone = data.isBuildInDone || false\n        this.tempGuideTaskProgress = data.tempGuideTaskProgress || {}\n        const cells = [], size = NOVICE_MAP_SIZE.x * NOVICE_MAP_SIZE.y\n        const areaCenter = gameHpr.areaCenter\n        for (let index = 0; index < size; index++) {\n            const info = areas[index] || { index, landId: mapJson.json[index] }\n            const area = this.areas.add(new NoviceAreaObj().init(info))\n            cells.push({\n                landId: 1,\n                owner: area.owner,\n                cityId: 1,\n            })\n            this.updatePlayerArmyDist(area)\n            // 战斗\n            if (info.battle) {\n                info.battle.mul = 1\n                const a = areaCenter.addArea(new AreaObj().init(info))\n                area.proxyAO = a\n                a.battleBegin(info.battle, false)\n                this.addBattleDist(area)\n            } else if (this.getAreaAttacker(area)) {\n                this.addBattleDist(area)\n            }\n        }\n        // 初始化主城\n        const mainArea = this.areas[NOVICE_MAINCITY_INDEX]\n        if (!mainArea.owner) {\n            mainArea.owner = uid\n            mainArea.cityId = CITY_MAIN_NID\n            mainArea.builds = []\n            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: BUILD_MAIN_NID, point: cc.v2(2, 5), lv: 1 }, false)\n            mainArea.addBuild({ uid: ut.UID(), index: mainArea.index, id: BUILD_WALL_NID, point: cc.v2(-1, -1), lv: 1 }, false)\n            mainArea.armys = []\n            mainArea.addArmy({\n                index: mainArea.index,\n                uid: ut.UID(),\n                name: assetsMgr.lang('ui.default_army_name') + 1,\n                owner: uid,\n                pawns: [\n                    { index: mainArea.index, uid: ut.UID(), id: 3101, lv: 1, point: cc.v2(7, 4) },\n                    { index: mainArea.index, uid: ut.UID(), id: 3101, lv: 1, point: cc.v2(9, 4) },\n                ]\n            })\n            mainArea.updateSize()\n            NOVICE_MAINCITY_OTHER_INDEXS.forEach(i => {\n                const area = this.areas[i]\n                area.owner = uid\n                area.cityId = -CITY_MAIN_NID\n            })\n            this.updatePlayerArmyDist(mainArea)\n        }\n        this.areas.forEach(m => {\n            if (!m.owner) {\n            } else if (m.owner === this.puid) {\n                this.ownCells[m.index] = m\n            } else if (m.owner === this.enemyUid) {\n                this.enemyCells[m.index] = m\n            } else {\n                m.owner = ''\n                m.updateCity(0, true)\n                m.armys = []\n            }\n        })\n        // 初始化玩家信息\n        this.cereal.value = data.cereal ?? 500\n        this.timber.value = data.timber ?? 450\n        this.stone.value = data.stone ?? 450\n        this.expBook = data.expBook || 0\n        this.iron = data.iron || 0\n        this.upScroll = data.upScroll || 0\n        this.fixator = data.fixator || 0\n        this.lastOutputTime = data.lastOutputTime ?? now\n        this.stamina = data.stamina ?? 100\n        this.updateBuildEffect()\n        this.updateOpSec()\n        this.checkUpdateAreaAvoidWar(now, true)\n        this.inited = true\n        this.noviceEnemyObj.load(data.noviceEnemyData || {})\n        return {\n            player: {\n                cereal: this.cereal.strip(),\n                timber: this.timber.strip(),\n                stone: this.stone.strip(),\n                expBook: this.expBook,\n                iron: this.iron,\n                upScroll: this.upScroll,\n                fixator: this.fixator,\n                granaryCap: this.getGranaryCap(),\n                warehouseCap: this.getWarehouseCap(),\n                cerealConsume: this.cerealConsume,\n                stamina: this.stamina,\n                mainCityIndex: NOVICE_MAINCITY_INDEX,\n                builds: mainArea.builds.map(m => {\n                    return {\n                        index: m.aIndex,\n                        uid: m.uid,\n                        id: m.id,\n                        lv: m.lv,\n                        point: m.point,\n                    }\n                }),\n                armyDists: this.getPlayerArmyDistArray(this.puid),\n                resetPolicyNeedGold: this.resetPolicyNeedGold,\n                merchants: this.merchants,\n                towerLvMap: this.towerLvMap,\n                hasNewTreasure: this.checkPlayerHasTreasure(this.puid),\n                btQueues: this.btQueues.map(m => m.strip()),\n                pawnDrillQueues: this.toDrillPawnQueue(NOVICE_MAINCITY_INDEX),\n                pawnLevelingQueues: this.toPawnLvingQueue(NOVICE_MAINCITY_INDEX),\n                configPawnMap: this.configPawnMap,\n                currForgeEquip: this.toForgeEquipData(),\n                equips: this.equips.map(m => m.toDB()),\n                guideTasks: this.guideTasks.map(m => { return { id: m.id, progress: m.progress } }),\n                freeRecruitPawnCount: this.freeRecruitPawnCount,\n                pawnSlots: this.pawnSlots,\n                policySlots: this.policySlots,\n                equipSlots: this.equipSlots,\n                heroSlots: this.heroSlots,\n                injuryPawns: this.injuryPawns.map(m => m.strip()),\n                curingPawns: this.curingPawns.map(m => m.toDB()),\n            },\n            cells: this.areas.map(m => m.toBaseData()),\n\n            enemy: {\n                id: this.enemyUid,\n                name: this.enemyName,\n            },\n            user: {\n                gold: this.gold,\n                warToken: this.warToken,\n                portrayals: this.portrayals,\n            }\n        }\n    }\n\n    //记录战斗详情数据\n    public recordFighterData(data: any) {\n        if (gameHpr.isNoviceMode) {\n            this.setBattleRecord(3, null, data)\n        }\n    }\n\n    //士兵槽位\n    private getPawnSlots(pawnSlots: any = {}) {\n        let slots = {}\n        let tempPawnBase = {}\n        const pawnBase = assetsMgr.getJson('pawnBase').dataIdMap\n        for (let key in pawnBase) {\n            let info = pawnBase[key]\n            if (info.need_build_lv > 0 && info.type < 5) {\n                let list = tempPawnBase[info.need_build_lv] || []\n                list.push(info.id)\n                tempPawnBase[info.need_build_lv] = list\n            }\n        }\n        for (let i = 0; i < PAWN_SLOT_CONF.length; i++) {\n            let lv = PAWN_SLOT_CONF[i]\n            slots[lv] = new NovicePawnSlotObj().fromSvr(pawnSlots[lv] || { lv, id: 0, selectIds: [] }).init()\n            slots[lv].allSelectIds = tempPawnBase[lv]\n        }\n        slots[1].selectIds = NOVICE_FIRST_PAWN_SLOTS//新手引导固定这3个\n        return slots\n    }\n\n    //政策槽位\n    private getPolicySlots(policySlots: any = {}) {\n        let slots = {}\n        for (let i = 0; i < POLICY_SLOT_CONF.length; i++) {\n            let lv = POLICY_SLOT_CONF[i]\n            slots[lv] = new NovicePolicyObj().fromSvr(policySlots[lv] || { lv, id: 0, selectIds: [] }).init()\n            slots[lv].allSelectIds = NOVICE_POLICY_SLOTS\n        }\n        return slots\n    }\n\n    private randListCount(slots: any[], count = 3) {\n        return ut.shuffleArray(slots).slice(0, count)\n    }\n\n    //装备槽位\n    private getEquipSlots(equipSlots: any = {}) {\n        let equipMap = {}\n        let jsonData = assetsMgr.getJson('equipBase').dataIdMap\n        for (let key in jsonData) {\n            let info = jsonData[key]\n            if (info.random) {\n                let list = info.need_build_lv.split(',')\n                for (let i = 0; i < list.length; i++) {\n                    let lv = list[i]\n                    let idList = equipMap[lv] || []\n                    idList.push(info.id)\n                    equipMap[lv] = idList\n                }\n            }\n        }\n        let slots = {}\n        for (let i = 0; i < EQUIP_SLOT_CONF.length; i++) {\n            let lv = EQUIP_SLOT_CONF[i]\n            slots[lv] = new NoviceEquipSlotObj().fromSvr(equipSlots[lv] || { lv, id: 0, selectIds: [] }).init()\n            slots[lv].allSelectIds = equipMap[lv]\n        }\n        return slots\n    }\n\n    //英雄槽位\n    getHeroSlots(heroSlots: NoviceHeroSlotObj[]) {\n        if (heroSlots) {\n            return heroSlots.map(m => new NoviceHeroSlotObj().fromSvr(m))\n        }\n        let slot = new NoviceHeroSlotObj()\n        slot.lv = 1\n        let slots = [slot]\n        return slots\n    }\n\n    // 刷新野地怪物，根据攻击对象刷新不同的野怪\n    private updateMonster(area: NoviceAreaObj, attacker: string) {\n        if (!area.owner && !area.isBattleing() && attacker != gameHpr.getUid()) {\n            //已经有军队了就不刷新\n            for (let i = 0; i < area.armys.length; i++) {\n                let army = area.armys[i]\n                if (army.owner) {\n                    return\n                }\n            }\n            area.updateArmys(area.getEnemyArmys(attacker))\n        }\n    }\n\n    public setEnemyUid(uid: string) {\n        this.enemyUid = uid\n    }\n    public setEnemyArea(area: NoviceAreaObj) {\n        this.enemyCells[area.index] = area\n        delete this.ownCells[area.index]\n    }\n    resetArea(area: NoviceAreaObj) {\n        delete this.enemyCells[area.index]\n        delete this.ownCells[area.index]\n    }\n    public getAreas() {\n        return this.areas\n    }\n\n    // 获取野怪装备\n    public getEnemyEquipById(id: number) {\n        return this.enemyEquipMap[id]\n    }\n\n    public setEnemyEquip(id: number, equip: any) {\n        this.enemyEquipMap[id] = equip\n    }\n\n    private getSaveData(uid: string) {\n        return storageMgr.loadJson('novice_data_' + uid)\n    }\n\n    public cleanSaveData() {\n        storageMgr.remove('novice_data_' + gameHpr.getUid())\n    }\n\n    // 存档\n    public saveBackupData() {\n        let localConfig = {}\n        for (let i = 0; i < GUIDE_CONFIG.datas.length; i++) {\n            let dataKey = 'novice_guide_' + gameHpr.getUid() + '_' + GUIDE_CONFIG.datas[i].id\n            let data = storageMgr.loadString(dataKey)\n            if (data) {\n                localConfig[dataKey] = data\n            }\n        }\n        storageMgr.saveJson('novice_data_backup_' + gameHpr.getUid(), this.strip())\n        storageMgr.saveJson('novice_guide_backup_' + gameHpr.getUid(), localConfig)\n    }\n\n    // 读取存档\n    public loadBackupData() {\n        let data = storageMgr.loadJson('novice_data_backup_' + gameHpr.getUid())\n        if (data) {\n            let localConfig = storageMgr.loadJson('novice_guide_backup_' + gameHpr.getUid())\n            for (let i = 0; i < GUIDE_CONFIG.datas.length; i++) {\n                let dataKey = 'novice_guide_' + gameHpr.getUid() + '_' + GUIDE_CONFIG.datas[i].id\n                storageMgr.remove(dataKey)\n                if (localConfig && localConfig[dataKey]) {\n                    storageMgr.saveString(dataKey, localConfig[dataKey])\n                }\n            }\n            viewHelper.gotoWind('login').then(() => storageMgr.saveJson('novice_data_' + gameHpr.getUid(), data))\n        }\n        else {\n            console.log('novice_data_backup_' + gameHpr.getUid() + ' not exist')\n        }\n    }\n\n    public run() {\n        this.isRunning = true\n    }\n\n    private _cd = 0\n    public update(dt: number) {\n        if (!this.isRunning) {\n            return\n        }\n        const now = Date.now()\n        this.checkUpdateOutput(now)\n        this.checkUpdateMarch(now)\n        this.checkUpdateStudy(now)\n        this.checkUpdateBTQueue(now)\n        this.checkUpdateBTCityQueue(now)\n        this.checkUpdateDrillPawnQueue(now)\n        this.checkUpdatePawnLvingQueue(now)\n        this.checkUpdateAreaAvoidWar(now)\n        this.checkUpdateForgeEquip(now)\n        this.checkUpdateCurePawn(now)\n        if (this._cd >= 0) {\n            this._cd -= dt\n            if (this._cd <= 0) {\n                this._cd = 0.1\n                this.checkTreasure()\n                this.checkTask()\n                this.noviceEnemyObj.trigger()\n            }\n        }\n        this.checkSave(dt)\n    }\n\n    public forceSave() {\n        this.checkSave(10)\n    }\n\n    public strip() {\n        const areas = {}\n        for (let key in this.ownCells) {\n            const area = this.ownCells[key]\n            areas[area.index] = area.toDB()\n        }\n        const dist = this.getPlayerArmyDist(this.puid)\n        for (let key in dist) {\n            const index = Number(key)\n            if (!areas[key]) {\n                areas[index] = this.getArea(index).toDB()\n            }\n        }\n        if (this.enemyUid) {\n            for (let key in this.enemyCells) {\n                const area = this.enemyCells[key]\n                areas[area.index] = area.toDB()\n            }\n            const enemyDist = this.getPlayerArmyDist(this.enemyUid)\n            for (let key in enemyDist) {\n                const index = Number(key)\n                if (!areas[key]) {\n                    areas[index] = this.getArea(index).toDB()\n                }\n            }\n        }\n        return {\n            areas,\n            cereal: this.cereal.value,\n            timber: this.timber.value,\n            stone: this.stone.value,\n            expBook: this.expBook,\n            iron: this.iron,\n            upScroll: this.upScroll,\n            fixator: this.fixator,\n            stamina: this.stamina,\n            lastOutputTime: this.lastOutputTime,\n            btQueues: this.btQueues,\n            pawnLvingQueues: this.pawnLvingQueues,\n            drillPawnQueues: this.drillPawnQueues,\n            avoidWarAreas: this.avoidWarAreas,\n            armyAutoBackIndexMap: this.armyAutoBackIndexMap,\n            marchs: this.marchs,\n            configPawnMap: this.configPawnMap,\n            currForgeEquip: this.currForgeEquip,\n            equips: this.equips.map(m => m.toDB()),\n            btCityQueues: this.btCityQueues,\n            enemyEquipMap: this.enemyEquipMap,\n            guideTasks: this.guideTasks,\n            firstBattleAreaInfo: this.firstBattleAreaInfo,\n            freeRecruitPawnCount: this.freeRecruitPawnCount,\n            enemyUid: this.enemyUid,\n            castleBattleIdx: this.castleBattleIdx,\n            lastOccupyPawnRes: this.lastOccupyPawnRes,\n            defenseBattleIdx: this.defenseBattleIdx,\n            manulBattles: this.manulBattles,\n            castleBattleStart: this.castleBattleStart,\n            pawnSlots: this.objectToDB(this.pawnSlots),\n            policySlots: this.objectToDB(this.policySlots),\n            equipSlots: this.objectToDB(this.equipSlots),\n            portrayals: this.portrayals.map(m => m.strip()),\n            heroSlots: this.heroSlots.map(m => m.strip()),\n            noviceEnemyData: this.noviceEnemyObj.strip(),\n            drillHistory: this.drillHistory,\n            gold: this.gold,\n            warToken: this.warToken,\n            battleRecordList: this.battleRecordList.map(m => m.strip()),\n            injuryPawns: this.injuryPawns.map(m => m.toDB()),\n            curingPawns: this.curingPawns.map(m => m.toDB()),\n            pawnDeadTimesMap: this.pawnDeadTimesMap,\n            attackEnemyIndexMap: this.attackEnemyIndexMap,\n            battleCountWithEnemy: this.battleCountWithEnemy,\n            serverStartTime: this.serverStartTime,\n            triggerTasks: this.triggerTasks,\n            isBuildInDone: this.isBuildInDone,\n            tempGuideTaskProgress: this.tempGuideTaskProgress,\n        }\n    }\n\n    public getGold() { return this.gold }\n    public setGold(val: number) {\n        this.gold = val\n        gameHpr.user.setGold(this.gold, true)\n    }\n\n    public getIron() { return this.iron }\n    public setIron(val: number) {\n        this.iron = val\n    }\n\n    public getWarToken() { return this.warToken }\n    public setWarToken(val: number) {\n        this.warToken = val\n        gameHpr.user.setWarToken(this.warToken, true)\n    }\n\n    private objectToDB(obj: any) {\n        let retObj = {}\n        for (let key in obj) {\n            retObj[key] = obj[key].toDB()\n        }\n        return retObj\n    }\n\n    private checkSave(dt: number) {\n        // 避免数据还未写进内存就用初始数据覆盖存档\n        if (!this.inited) return\n        this.saveElapsed += dt\n        if (this.saveElapsed < 1) {\n            return\n        }\n        this.saveElapsed = 0\n\n        storageMgr.saveJson('novice_data_' + this.puid, this.strip())\n        // cc.log('save...')\n    }\n\n    public getArea(index: number) {\n        return this.areas[index]\n    }\n\n    public getGuideTasks() {\n        return this.guideTasks\n    }\n\n    public appendGuideTask(filter: (task: any) => boolean) {\n        const db = assetsMgr.getJson('guideTask')?.datas\n        const curTasks = new Set(this.guideTasks.map(m => m.id))\n        const tasks = db.filter(m => filter(m) && !curTasks.has(m.id)) ?? []\n        for (let id of curTasks) {\n            this.triggerTasks[id] = true\n        }\n        this.guideTasks = [...this.guideTasks, ...tasks.map(m => { return { id: m.id, progress: 0 } })]\n        this.updateGuideTasksProgress()\n    }\n\n    private updateGuideTasksProgress() {\n        const tasks = []\n        this.guideTasks.forEach(m => {\n            tasks.push({ id: m.id, progress: m.progress })\n        })\n        if (tasks.length > 0) {\n            gameHpr.player.updateGuideTasksProgress(tasks)\n        }\n    }\n\n    // 刷新免战信息\n    private checkUpdateAreaAvoidWar(now: number, init?: boolean) {\n        for (let key in this.avoidWarAreas) {\n            const time = this.avoidWarAreas[key]\n            // 新手村直接无免战\n            if (true || now - time >= 0) {\n                delete this.avoidWarAreas[key]\n                !init && this.notifyWorld(NotifyType.AREA_AVOID_WAR, Number(key))\n            }\n        }\n    }\n\n    private initPawnLvingQueues(data: any) {\n        this.pawnLvingQueues = { map: {}, pawnUIDMap: data.pawnUIDMap || {} }\n        for (let key in data.map) {\n            const arr: any[] = data.map[key]\n            this.pawnLvingQueues.map[key] = arr.map(m => new NovicePawnLevelingObj().fromDB(m))\n        }\n    }\n\n    private initDrillPawnQueues(data: any) {\n        this.drillPawnQueues = {}\n        for (let key in data) {\n            const obj = data[key]\n            for (let k in obj) {\n                const arr: any[] = obj[k]\n                obj[k] = arr.map(m => new NoviceDrillPawnObj().fromDB(m))\n            }\n            this.drillPawnQueues[key] = obj\n        }\n    }\n\n    private getPlayerCellOutput() {\n        let cereal = INIT_RES_OUTPUT, timber = INIT_RES_OUTPUT, stone = INIT_RES_OUTPUT\n        for (let key in this.ownCells) {\n            const area = this.ownCells[key], landId = area.landId\n            if (area.cityId > 0) { //从城市资源获取\n                const id = area.cityId * 1000 + landId\n                const json = assetsMgr.getJsonData('cityResAttr', id)\n                if (json) {\n                    cereal += json.cereal\n                    timber += json.timber\n                    stone += json.stone\n                }\n            } else if (area.cityId === 0) {\n                const json = assetsMgr.getJsonData('land', landId)\n                cereal += json.cereal\n                timber += json.timber\n                stone += json.stone\n            }\n        }\n        return { cereal, timber, stone }\n    }\n\n    // 更新玩家的军队分布信息\n    public updatePlayerArmyDist(area: AreaObj) {\n        area.armys.forEach(m => {\n            this.changeArmyDistIndex(m.owner, m.uid, area.index)\n        })\n    }\n\n    // 改变军队位置\n    public changeArmyDistIndex(owner: string, uid: string, index: number) {\n        let dist = this.armysMap[owner]\n        if (!dist) {\n            dist = this.armysMap[owner] = {}\n        }\n        if (index == -1) {\n            delete dist[uid]\n        } else {\n            dist[uid] = index\n        }\n    }\n\n    private getPlayerArmyDist(uid: string) {\n        const dist = this.armysMap[uid] || {}, dists = {}\n        for (let key in dist) {\n            const index = dist[key]\n            dists[index] = true\n        }\n        return dists\n    }\n\n    // 获取玩家军队分布情况\n    public getPlayerArmyDistArray(uid: string) {\n        const dist = this.getPlayerArmyDist(uid)\n        const arr = []\n        for (let key in dist) {\n            const index = Number(key)\n            const area = this.getArea(index)\n            if (!area) {\n                continue\n            }\n            const armys = []\n            area.armys.forEach(m => {\n                if (m.owner === uid) {\n                    armys.push(m.strip())\n                }\n            })\n            if (armys.length > 0) {\n                arr.push({ index, armys })\n            }\n        }\n        return arr\n    }\n\n    // 获取我的军队列表\n    public getMyPlayerArmys(): ArmyShortInfo[] {\n        const uid = gameHpr.getUid()\n        const dist = this.getPlayerArmyDist(uid)\n        const armys = []\n        for (let key in dist) {\n            const index = Number(key)\n            const area = this.getArea(index)\n            if (!area) {\n                continue\n            }\n            area.armys.forEach(m => {\n                if (m.owner === uid) {\n                    armys.push(m.strip())\n                }\n            })\n        }\n        return armys\n    }\n\n    private getPlayerCerealConsume() {\n        let count = 0\n        const uid = this.puid\n        const dist = this.getPlayerArmyDist(uid)\n        for (let key in dist) {\n            const area = this.areas[Number(key)]\n            if (!area) {\n                continue\n            }\n            area.armys.forEach(m => {\n                if (m.owner === uid) {\n                    m.pawns.forEach(p => count += p.cerealCost)\n                }\n            })\n        }\n        return count\n    }\n\n    private getPolicyEffect(type: CEffect) {\n        let count = 0\n        for (let key in this.policySlots) {\n            let policySlot = this.policySlots[key]\n            if (policySlot.type === type) {\n                count += policySlot.getValue(0)\n            }\n        }\n        return count\n    }\n\n    // 获取粮食容量\n    private getGranaryCap() {\n        return INIT_RES_CAP + (this.effects[CEffect.GRANARY_CAP] || 0) + this.getExtraCap(2002)\n    }\n\n    // 获取仓库容量\n    private getWarehouseCap() {\n        return INIT_RES_CAP + (this.effects[CEffect.WAREHOUSE_CAP] || 0) + this.getExtraCap(2003)\n    }\n\n    // 获取额外容量\n    private getExtraCap(id: number) {\n        const val = this.getPolicyEffect(CEffect.GW_CAP) || 0\n        if (val <= 0) {\n            return 0\n        }\n        let lv = 0\n        const area = this.areas[NOVICE_MAINCITY_INDEX]\n        area.builds.forEach(m => {\n            if (m.id === id) {\n                lv += m.lv\n            }\n        })\n        return lv * val\n    }\n\n    private checkPolicyWithType(type: number) {\n        switch (type) {\n            case CEffect.RES_OUTPUT:\n                this.updateOpSec(true)\n                break;\n        }\n    }\n\n    private updateOpSec(isNotify?: boolean) {\n        // 计算占领的资源田\n        let { cereal, timber, stone } = this.getPlayerCellOutput()\n        // 加上内政增加 固定值\n        let val = this.getPolicyEffect(CEffect.RES_OUTPUT)\n        cereal += val\n        timber += val\n        stone += val\n        // 获取粮耗\n        this.cerealConsume = this.getPlayerCerealConsume()\n        // 取出最大的产出时间\n        this.outputInterval = this.cereal.initOutput(cereal - this.cerealConsume)\n        let t = this.timber.initOutput(timber)\n        if (t > this.outputInterval) {\n            this.outputInterval = t\n        }\n        t = this.stone.initOutput(stone)\n        if (t > this.outputInterval) {\n            this.outputInterval = t\n        }\n        if (isNotify) {\n            this.notifyPlayer(NotifyType.OUTPUT, {\n                cereal: this.cereal.strip(),\n                timber: this.timber.strip(),\n                stone: this.stone.strip(),\n                cerealConsume: this.cerealConsume,\n            })\n        }\n    }\n\n    private checkUpdateOutput(now: number) {\n        let dt = now - this.lastOutputTime\n        if (dt < this.outputInterval) {\n            return\n        }\n        this.lastOutputTime = now\n        // 限制产出\n        let capScale = .7\n        if (this.cereal.do(dt, this.getGranaryCap() * capScale)) { //粮食\n            this.notifyPlayer(NotifyType.OUTPUT, { cereal: this.cereal.strip() })\n        }\n        const cap = this.getWarehouseCap() * capScale\n        if (this.timber.do(dt, cap)) { //木头\n            this.notifyPlayer(NotifyType.OUTPUT, { timber: this.timber.strip() })\n        }\n        if (this.stone.do(dt, cap)) { //石头\n            this.notifyPlayer(NotifyType.OUTPUT, { stone: this.stone.strip() })\n        }\n    }\n\n    public notifyPlayer(type: NotifyType, data: any) {\n        this.emit('game/OnUpdatePlayerInfo', { list: [{ type, ['data_' + type]: data }] })\n    }\n\n    public notifyWorld(type: NotifyType, data: any) {\n        this.emit('game/OnUpdateWorldInfo', { list: [{ type, ['data_' + type]: data }] })\n    }\n\n    public notifyArea(index: number, type: NotifyType, data: any) {\n        this.emit('game/OnUpdateAreaInfo', { index, type, ['data_' + type]: data })\n    }\n\n    private notifyPlayerArmyDistInfo(owner?: string) {\n        this.notifyPlayer(NotifyType.ARMY_DIST, this.getPlayerArmyDistArray(owner || this.puid))\n    }\n\n    // 获取地块于地块的距离\n    public getToMapCellDis(sindex: number, tindex: number) {\n        let [sp, tp] = mapHelper.getMinDisPoint(gameHpr.world.getMapCellByIndex(sindex).getOwnPoints(), gameHpr.world.getMapCellByIndex(tindex).getOwnPoints())\n        return mapHelper.getPointToPointDis(sp, tp)\n    }\n\n    // 获取同时到达行军时间\n    public getArmysMarchSameSpeed(armys: ArmyShortInfo[], target: number, speedAcc: number) {\n        let sameTime = 0\n        for (let i = 0; i < armys.length; i++) {\n            const area = this.getArea(armys[i].index)\n            const army = area?.getArmyByUid(armys[i].uid)\n            if (army) {\n                let pawn = army.getPawnByMinMarchSpeed()\n                const dis = this.getToMapCellDis(armys[i].index, target)\n                let time = dis * (ut.Time.Hour / pawn.marchSpeed)\n                sameTime = Math.max(sameTime, time)\n            }\n        }\n        sameTime = Math.max(sameTime / Math.abs(speedAcc), 200)\n        return sameTime\n    }\n\n    // 添加行军\n    public addMarchArmy(index: number, army: ArmyObj, target: number, timeState: number, auto: boolean) {\n        if (index === target) {\n            return\n        }\n        //玩家攻打敌人行军\n        let area = this.getArea(target)\n        if (area.owner === this.getEnemyUID() && army.owner === this.puid && !this.attackEnemyIndexMap[area.index]) {\n            this.attackEnemyIndexMap[area.index] = true\n            this.noviceEnemyObj.createEnemyArmyToArea(area, Object.keys(this.attackEnemyIndexMap).length, mapHelper.getAddArmyDir(index, target))\n        }\n        // 行军速度最慢的士兵\n        const pawn = army.getPawnByMinMarchSpeed()\n        // 获取时间\n        let time = 0\n        if (timeState > 0) {\n            time = timeState\n        } else {\n            const dis = this.getToMapCellDis(index, target)\n            time = dis * (ut.Time.Hour / pawn.marchSpeed)\n            if (timeState < 0) { //是否加速\n                time = Math.max(time / Math.abs(timeState), 200)\n            }\n        }\n        // cc.log('addMarchArmy timeState: ' + timeState + ', time: ' + time)\n        const march = new NoviceMarchObj().init(army, index, target, Math.floor(time), pawn.id)\n        march.autoRevoke = auto\n        this.marchs.push(march)\n        army.state = ArmyState.MARCH\n        // 通知行军\n        this.notifyWorld(NotifyType.ADD_MARCH, march.strip())\n        // 通知删除战场中的军队 因为进入行军中就需要从战场中删除掉\n        this.notifyArea(index, NotifyType.REMOVE_ARMY_BY_MARCH, army.uid)\n        // 通知军队分布情况\n        this.notifyPlayerArmyDistInfo()\n    }\n\n    // 检测更新行军信息\n    private checkUpdateMarch(now: number) {\n        for (let i = 0; i < this.marchs.length; i++) {\n            const m = this.marchs[i]\n            if (now - m.startTime < m.needTime) {\n                continue\n            }\n            // 到达目的地 先删除\n            this.marchs.splice(i, 1)\n            i--\n            this.notifyWorld(NotifyType.REMOVE_MARCH, { uid: m.uid })\n            let area = this.getArea(m.armyIndex), tArea = this.getArea(m.targetIndex)\n            if (!area || !tArea) {\n                continue\n            }\n            const army = area.getArmyByUid(m.armyUid)\n            if (!army) {\n                continue\n            }\n            army.state = ArmyState.NONE\n            const isRevoke = area.index === tArea.index\n            const isCanAddAreaArmy = this.checkCanAddAreaArmy(tArea, army.owner, isRevoke)\n            // 如果是撤回 或者可以进入\n            if (isRevoke || isCanAddAreaArmy) {\n                if (!isRevoke) {\n                    area.removeArmy(army.uid) // 只要不是撤回 就从当前区域删除\n                }\n                // 如果是撤回 但是不能进入当前区域 就找一个可以进入的继续行军\n                if (isRevoke && !isCanAddAreaArmy) {\n                    // 添加记录\n                    // this.AddArmyMarchRecord(m.AutoRevoke, isRevoke, army, m.StartIndex, m.TargetIndex)\n                    // if ta := this.GetPlayerNotFullArmyArea(army.Owner, tArea.index); ta != nil {\n                    //     this.AddMarchArmy(tArea.index, army, ta.index, 0, true)\n                    // } else {\n                    //     tArea.RemoveArmy(army.Uid) //直接删除士兵\n                    //     this.NotifyPlayerArmyDistInfo(army.Owner)\n                    //     // 记录解散\n                    //     this.room.GetRecord().AddArmyMarchRecord(4, army.Owner, army.Uid, army.Name, army.AIndex, 0)\n                    // }\n                } else {\n                    this.updateMonster(tArea, army.owner)\n                    // 添加到目标区域\n                    this.addAreaArmy(tArea, army, mapHelper.getAddArmyDir(area.index, tArea.index))\n                    // 通知军队分布情况\n                    this.notifyPlayerArmyDistInfo()\n                    // 添加记录\n                    // this.AddArmyMarchRecord(m.AutoRevoke, isRevoke, army, m.StartIndex, m.TargetIndex)\n                }\n            } else { //否则 强行遣返\n                //     this.room.GetRecord().AddArmyMarchRecord(5, army.Owner, army.Uid, army.Name, m.StartIndex, m.TargetIndex)\n                //     army.AutoBackIndex = -1\n                //     this.CancelMarchArmy(m, true)\n            }\n        }\n    }\n\n    private addAreaArmy(area: NoviceAreaObj, army: ArmyObj, dir: number) {\n        this.setArmyPawnPosition(area, army, dir) //设置士兵的位置\n        army.enterDir = dir\n        army.aIndex = area.index\n        army.pawns.forEach(m => m.aIndex = area.index)\n        area.addArmy(army.strip())\n        army = area.getArmyByUid(army.uid)\n        this.changeArmyDistIndex(army.owner, army.uid, area.index)\n        // 战斗中\n        if (area.isBattleing()) {\n            this.addArmyToBattle(area, army)\n            return army\n        }\n        const attacker = this.getAreaAttacker(area)\n        if (attacker) { //有攻击者 直接触发战斗\n            this.triggerAreaBattle(area, attacker, area.passPoints[dir])\n        } else if (army.getActPawnCount() > 0) { //有士兵才通知\n            if (area.isRecoverPawnHP()) {\n                army.recoverAllPawn() //恢复士兵血量\n            }\n            this.notifyArea(area.index, NotifyType.ADD_ARMY, army.strip())\n        }\n        return army\n    }\n\n    private triggerAreaBattle(area: NoviceAreaObj, attacker: string, enterPoint: cc.Vec2) {\n        // 由教程主动触发战斗\n        if (gameHpr.guide.isCurrProgressTag(GuideTagType.FIRST_TRIGGER_BATTLE)) {\n            // 调整士兵的位置\n            const army = area.armys.find(m => m.owner === this.puid)\n            if (army && army.pawns.length > 0) {\n\n                NOVICE_FIRST_BATTLE_PAWN_POINT.forEach((m, i) => army.pawns[i]?.setPoint(m))\n                army.pawns[0].curHp = 45\n            }\n            // 调整野怪血量 \n            area.armys.filter(m => m.owner !== this.puid).forEach(army => army.pawns.forEach(m => {\n                m.curHp = NOVICE_FIRST_BATTLE_ENEMY_HP[m.id] ?? m.maxHp\n            }))\n            this.firstBattleAreaInfo = { index: area.index, attacker }\n        } else if (this.manulBattles[area.index]) { //手动战斗\n            this.manulBattles[area.index] = { index: area.index, attacker }\n            // 先设置状态\n            area.armys.forEach(m => {\n                if (m.isMarching()) {\n                } else if (m.state === ArmyState.NONE) {\n                    m.state = ArmyState.FIGHT\n                    m.pawns.forEach(p => p.changeState(PawnState.STAND))\n                }\n            })\n        } else {\n            this.battleBegin(area, attacker)\n        }\n        this.addBattleDist(area)\n    }\n\n    // 获取阵营\n    public getCamp(attacker: string) {\n        if (attacker === this.puid) {\n            return 1\n        }\n        else if (attacker === this.noviceEnemyObj.getEnemyUID()) {\n            return 2\n        }\n        return 0\n    }\n\n    public battleBegin(area: NoviceAreaObj, attacker: string, mul?: number) {\n        const roles = []\n        const fighters = []\n        const pawnResObj = this.lastOccupyPawnRes[area.index] = {}\n        area.armys.forEach(m => {\n            if (m.isMarching()) {\n                return\n            }\n            m.state = ArmyState.FIGHT\n            const isAttacker = m.owner === attacker\n            const isMyArmy = m.isOwner()\n            m.pawns.forEach(p => {\n                p.changeState(PawnState.STAND)\n                fighters.push({\n                    uid: p.uid,\n                    camp: this.getCamp(m.owner),\n                    attackIndex: isAttacker ? p.attackSpeed + 100000 : p.attackSpeed,\n                })\n                if (isMyArmy) {\n                    gameHpr.stringToCTypes(p.baseJson?.drill_cost).forEach(m => {\n                        pawnResObj[m.type] = (pawnResObj[m.type] || 0) + m.count\n                    })\n                }\n                roles.push({ role_id: p.id, role_count: 1 })\n            })\n        })\n        let a = gameHpr.areaCenter.getArea(area.index)\n        if (a) {\n            a.init(area.strip())\n        } else {\n            a = gameHpr.areaCenter.addArea(new AreaObj().init(area.strip()))\n        }\n        area.proxyAO = a\n        let attackIndex = 0\n        fighters.sort((a, b) => b.attackIndex - a.attackIndex)\n        fighters.forEach(m => m.attackIndex = ++attackIndex)\n        this.addTowerToBattle(area, attackIndex, fighters)\n        // 开始战斗\n        a.battleBegin({\n            camp: this.getCamp(a.owner),\n            fighters: fighters,\n            mul: mul || 1,\n            attackerArmyAcc: 1,\n            defenderArmyAcc: 1,\n        })\n        // 上报\n        taHelper.track('ta_attackFieldStart_rookie', {\n            roles,\n            field: {\n                field_lv: area.landLv,\n                field_dis: mapHelper.getIndexToIndexDis(NOVICE_MAINCITY_INDEX, area.index),\n                field_type: area.owner ? 0 : area.getLandType(),\n                field_index: area.index,\n            },\n            uid: gameHpr.getUid()\n        })\n        //记录战斗信息\n        if (attacker === gameHpr.getUid() || (attacker === this.noviceEnemyObj.getEnemyUID() && area.owner === gameHpr.getUid())) {\n            let record = new NoviceRecordObj()\n            record.init(area, { attacker, fighters })\n            this.battleRecordList.unshift(record)\n        }\n    }\n\n    private addTowerToBattle(area: NoviceAreaObj, attackIndex: number, fighters: any[]) {\n        if (!area.cityId && area.owner) {\n            // 加入箭塔\n            fighters.push({\n                towerId: 7001,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 5, y: 5 },\n                waitRound: 0,\n            })\n        }\n        else if (area.cityId === CITY_FORT_NID) {//要塞\n            // 加入要塞\n            fighters.push({\n                towerId: 7002,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 5, y: 5 },\n                waitRound: 0,\n            })\n        } else if (area.cityId === CITY_MAIN_NID) {//主城\n            // 加入城墙\n            fighters.push({\n                towerId: 7003,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 5, y: 5 },\n                waitRound: 0,\n            })\n            fighters.push({\n                towerId: 7003,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 11, y: 5 },\n                waitRound: 0,\n            })\n            fighters.push({\n                towerId: 7003,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 5, y: 11 },\n                waitRound: 0,\n            })\n            fighters.push({\n                towerId: 7003,\n                towerLv: 1,\n                uid: ut.UID(),\n                camp: this.getCamp(area.owner),\n                attackIndex: ++attackIndex,\n                point: { x: 11, y: 11 },\n                waitRound: 0,\n            })\n        }\n    }\n\n    public resumeBattle() {\n        const lookCell = gameHpr.world.getLookCell()\n        if (this.manulBattles[lookCell.index]) {\n            let { index, attacker } = this.manulBattles[lookCell.index]\n            const area = this.getArea(lookCell.index)\n            this.battleBegin(area, attacker, 1)\n            delete this.manulBattles[lookCell.index]\n            return true\n        }\n        return false\n    }\n\n    public beginFirstBattle() {\n        if (this.firstBattleAreaInfo) {\n            const { index, attacker } = this.firstBattleAreaInfo\n            this.firstBattleAreaInfo = undefined\n            const area = this.getArea(index)\n            if (area?.armys.some(m => m.owner === this.puid)) {\n                this.battleBegin(area, attacker, 1)\n            }\n        }\n    }\n\n    // 触发战斗结束\n    public triggerBattleEnd(index: number, attacker: string, battleEndInfo: any) {\n        const area = this.areas[index]\n        if (!area) {\n            return\n        }\n        const owner = area.owner\n        const isMainCity = area.isMainCity()\n        // 还原军队状态\n        const a = area.proxyAO ?? gameHpr.areaCenter.getArea(index)\n        if (!a) {\n            return\n        }\n\n        if (owner === this.getEnemyUID()) {\n            if (attacker && attacker !== owner) {//成功占领\n                this.setGuideTaskProgress(TCusCType.OCCUPY_ENEMY_LAND)\n                if (area.cityId === CITY_MAIN_NID) {\n                    this.setGuideTaskProgress(TCusCType.CAPTURE_ENEMY_CITY)\n                }\n                else if (area.cityId === CITY_FORT_NID) {\n                    this.setGuideTaskProgress(TCusCType.DESTROY_ENEMY_TOWER)\n                }\n            }\n            this.battleCountWithEnemy++\n        }\n        area.proxyAO = null\n        const equipAttrMap = {}, armyMap = {}\n        gameHpr.player.getEquips().forEach(m => equipAttrMap[m.uid] = m.attrs)\n        // 先删除没有的\n        let armyUidMap = {}, isRemoveArmy = false\n        a.armys.forEach(m => armyUidMap[m.uid] = true)\n        for (let i = area.armys.length - 1; i >= 0; i--) {\n            const uid = area.armys[i].uid\n            if (!armyUidMap[uid]) {\n                area.removeArmy(uid)\n                isRemoveArmy = true\n            }\n        }\n        if (isRemoveArmy) {\n            this.notifyPlayerArmyDistInfo()\n        }\n        // 如果占领了 就删除\n        if (area.isOwner() || attacker) {\n            delete this.lastOccupyPawnRes[index]\n        }\n        if (attacker || owner) {\n            area.updateArmys(a.armys.map(m => m.strip()))\n        } else { //没有占领 这里重新刷新野怪\n            area.updateArmys(area.getEnemyArmys())\n        }\n        area.armys.forEach(army => {\n            army.state = ArmyState.NONE\n            // 只有是胜利者才有宝箱\n            if (gameHpr.checkIsOneAlliance(attacker, army.owner)) {\n                let arr = armyMap[army.owner]\n                if (!arr) {\n                    arr = armyMap[army.owner] = []\n                }\n                arr.push(army)\n            }\n            army.pawns.forEach(m => {\n                m.changeState(PawnState.NONE)\n                m.updateEquipAttr(m.equip.uid, equipAttrMap[m.equip.uid]) //刷新装备信息\n            })\n        })\n        let recordTreasures = []//记录宝箱\n        // 分配宝箱\n        let { treasureIds, treasureCounts, specialTreasureId } = this.getBattleTreasureCount(area, attacker)\n        if (!ut.isEmptyObject(armyMap) && (treasureIds.length > 0 || specialTreasureId > 0)) {\n            const uids = {}\n            if (treasureCounts.length < 2 || treasureIds.length === 0) {\n                treasureCounts = [0, 0]\n            }\n            const attackTreasureCount = ut.random(treasureCounts[0], treasureCounts[1]) //先随机攻占者的数量\n            for (let uid in armyMap) {\n                let rcount = 0, armys: ArmyObj[] = armyMap[uid]\n                let treasureCount = 0, specialTreasureCount = 0\n                if (uid === attacker) {\n                    treasureCount = attackTreasureCount\n                    specialTreasureCount = 1 //暂时只有攻击者有\n                }\n                // 添加特殊资源\n                if (specialTreasureId > 0 && specialTreasureCount > 0) {\n                    for (let i = 0, l = armys.length; i < l; i++) {\n                        const army = armys[i]\n                        for (let ii = 0, ll = army.pawns.length; ii < ll; ii++) {\n                            const pawn = army.pawns[ii]\n                            const sc = this.addPawnTreasure(pawn, specialTreasureCount, specialTreasureId)\n                            rcount += sc\n                            specialTreasureCount -= sc\n                            if (specialTreasureCount <= 0) {\n                                break\n                            }\n                        }\n                        if (specialTreasureCount <= 0) {\n                            break\n                        }\n                    }\n                }\n                if (treasureCount > 0) {\n                    // 随机分配\n                    let cnt = armys.length, min = 0\n                    let length = cnt\n                    let sum = treasureCount - min * length\n                    let counts = []\n                    let i = 0\n                    for (let l = length - 1; i < l; i++) {\n                        let val = ut.random(0, sum / length * 2)\n                        sum -= val\n                        length -= 1\n                        counts[i] = val + min\n                    }\n                    counts[i] = sum + min\n                    let count = 0\n\n                    for (let i = 0, l = armys.length; i < l; i++) {\n                        const army = armys[i]\n                        count += counts[i]\n                        if (count <= 0 || army.pawns.length === 0) {\n                            continue\n                        }\n                        for (let ii = 0, ll = army.pawns.length; ii < ll; ii++) {\n                            const pawn = army.pawns[ii]\n                            let sc = 0\n                            let treasureId = 0\n                            if (typeof treasureIds[0] == 'object') {\n                                treasureId = treasureIds[ut.randomIndexByWeight(treasureIds, 'weight')].id\n                                sc = this.addPawnTreasure(pawn, count, treasureId)\n                            }\n                            else {\n                                treasureId = treasureIds[rcount]\n                                sc = this.addPawnTreasure(pawn, 1, treasureId)\n                            }\n                            for (let sci = 0; sci < sc; sci++) {\n                                recordTreasures.push(pawn.treasures[pawn.treasures.length - 1 - sci])\n                            }\n                            rcount += sc\n                            count -= sc\n                            if (count <= 0) {\n                                break\n                            }\n                        }\n                    }\n                }\n                if (rcount > 0) {\n                    uids[uid] = true\n                }\n            }\n            // 发送有新宝箱通知\n            for (let uid in uids) {\n                this.sendPlayerHasTreasure(uid)\n            }\n        }\n        // 占领\n        if (attacker && attacker !== owner) {\n            area.owner = attacker\n            area.updateCity(0, true)\n            if (attacker === this.puid) {\n                this.ownCells[area.index] = area\n                delete this.enemyCells[area.index]\n            } else {\n                this.enemyCells[area.index] = area\n                delete this.ownCells[area.index]\n            }\n            this.updateOpSec(true)\n            // 新手村不免战\n            // this.addAvoidWarAreaEndTime(area.index, ut.Time.Hour * 7, false) //添加免战时间\n            this.notifyWorld(NotifyType.ADD_CELL, { index, owner: attacker }) //通知\n            // 占领敌人主城\n            if (isMainCity) {\n                area.updateSize()//更新战斗区域大小\n                for (let key in this.enemyCells) {\n                    let cell = this.enemyCells[key]\n                    if (cell.owner !== attacker) {\n                        cell.owner = attacker\n                        cell.updateCity(0, true)\n                        cell.armys.length = 0\n                        this.notifyWorld(NotifyType.ADD_CELL, { index: cell.index, owner: attacker }) //通知\n                    }\n                }\n                this.enemyCells = {}\n                //删除UI界面的所有军队\n                for (let i = 0; i < area.armys.length; i++) {\n                    let m = area.armys[i]\n                    eventCenter.emit(EventType.REMOVE_ARMY, { aIndex: m.index, uid: m.uid, pawns: m.pawns })\n                }\n            }\n            this.noviceEnemyObj.checkAddCity()\n        }\n        this.removeBattleDist(index)\n        // 更新血量\n        area.updateMaxHP()\n        // 是否有军队要自动返回的\n        this.checkArmyAutoBack(area)\n        // 调整位置\n        this.adjustmentPawnPos(area)\n        // 如果是可以恢复血量的\n        if (area.isRecoverPawnHP()) {\n            area.recoverAllPawnHP()\n        }\n        this.notifyArea(area.index, NotifyType.AREA_BATTLE_END, {\n            attacker,\n            data: area.strip(),\n            treasures: recordTreasures,\n        })\n        // 上报\n        taHelper.track('ta_attackFieldEnd_rookie', {\n            isWin: !!attacker,\n            battle_costTime: area.getBattleElapsedTime(),\n            field: {\n                field_lv: area.landLv,\n                field_dis: mapHelper.getIndexToIndexDis(NOVICE_MAINCITY_INDEX, area.index),\n                field_type: owner ? 0 : area.getLandType(),\n                field_index: area.index,\n            },\n            uid: gameHpr.getUid()\n        })\n        this.checkHero()\n        this.setBattleRecord(2, area, { battleEndInfo, recordTreasures })\n    }\n\n    private getBattleTreasureCount(area: NoviceAreaObj, fighter: string) {\n        let treasureIds = [], treasureCounts: number[] = [], specialTreasureId = 0\n        if (!fighter) {\n            return { treasureIds, treasureCounts, specialTreasureId }\n        } else if (area.owner === fighter) {\n            return { treasureIds, treasureCounts, specialTreasureId }\n        } else if (area.owner) {\n            return { treasureIds, treasureCounts, specialTreasureId }\n        }\n        const index = NOVICE_MAINCITY_INDEX\n        const dis = Math.min(16, gameHpr.getToMapCellDis(area.index, index))\n        const landLv = area.landLv, landType = area.landType\n        const json = assetsMgr.getJsonData('landAttr', landLv * 1000 + dis)\n        if (!json) {\n            return { treasureIds, treasureCounts, specialTreasureId }\n        } else if (this.changeStamina(-json.need_stamina) === -1) {\n            return { treasureIds, treasureCounts, specialTreasureId }\n        }\n        let customTreasures = this.getNoiceCustomTreasureCount(area, specialTreasureId)\n        if (customTreasures) {\n            return customTreasures\n        }\n        const treasureIdPrefix = landType * 100\n        const weights = ut.stringToNumbers(json.treasures_lv, ',')\n        weights.forEach((m, i) => {\n            if (m > 0) {\n                treasureIds.push({ id: treasureIdPrefix + (i + 1), weight: m })\n            }\n        })\n        treasureCounts = ut.stringToNumbers(json.treasures_count, ',')\n        // 特殊资源\n        if (json.other_rewards_odds) {\n            const odds = ut.stringToNumbers(json.other_rewards_odds, ',')\n            if (ut.chance(odds[0])) {\n                specialTreasureId = treasureIdPrefix + 13\n            }\n        }\n        return { treasureIds, treasureCounts, specialTreasureId }\n    }\n\n    // 获取新手村定制宝箱\n    private getNoiceCustomTreasureCount(area: NoviceAreaObj, specialTreasureId: number) {\n        let typeCount = 0\n        let landType = area.landType\n        if (area.landLv === 1) {\n            landType = 1\n            typeCount = gameHpr.getPlayerLandCountByLv(area.landLv)\n        }\n        else {\n            gameHpr.getPlayerInfo(gameHpr.getUid())?.cells?.forEach(cell => {\n                if (cell.isHasRes() && cell.landLv === area.landLv && cell.landType === area.landType) {\n                    typeCount += 1\n                }\n            })\n        }\n        let list = NOVICE_LAND_TREASURE_REWARD_MAP[landType + '_' + area.landLv]\n        if (list && typeCount < list.length) {\n            let treasureIds = list[typeCount]\n            if (typeof treasureIds == 'number') {\n                treasureIds = [treasureIds]\n            }\n            return { treasureIds, treasureCounts: [treasureIds.length, treasureIds.length], specialTreasureId }\n        }\n        return null\n    }\n\n    private addPawnTreasure(pawn: PawnObj, cnt: number, id: number) {\n        if (cnt <= 0 || id <= 0) {\n            return 0\n        }\n        const bagCap = pawn.baseJson?.bag_cap || 0\n        let count = bagCap - pawn.treasures.length\n        count = Math.min(count, cnt)\n        if (id === NOVICE_FIRST_STONE_TREASURE_ID) {//保证石头地宝箱一定添加\n            count = Math.max(count, 1)\n        }\n        for (let i = 0; i < count; i++) {\n            pawn.treasures.push(gameHpr.fromSvrTreasureInfo({\n                uid: ut.UID(),\n                id: id,\n                rewards: [],\n            }, pawn.aIndex, pawn.armyUid, pawn.uid))\n        }\n        return count\n    }\n\n    // 检测玩家是否有新的宝箱\n    public checkPlayerHasTreasure(uid: string) {\n        const dist = this.getPlayerArmyDist(uid)\n        for (let key in dist) {\n            const area = this.areas[Number(key)]\n            if (!area) {\n                continue\n            } else if (area.armys.some(army => army.owner === uid && army.pawns.some(m => m.treasures.length > 0))) {\n                return true\n            }\n        }\n        return false\n    }\n\n    // 通知玩家有新宝箱\n    private sendPlayerHasTreasure(uid: string) {\n        const has = this.checkPlayerHasTreasure(uid)\n        if (this.hasTreasure !== has) {\n            this.hasTreasure = has\n            this.notifyPlayer(NotifyType.NEW_TREASURE, has)\n        }\n    }\n\n    private checkArmyAutoBack(area: NoviceAreaObj) {\n        if (gameHpr.guide.isCurrTag(GuideTagType.CHECK_FIRST_BATTLE_END)) {\n            return\n        }\n        area.armys.forEach(army => {\n            const autoBackIndex = this.armyAutoBackIndexMap[army.uid] ?? -1\n            if (army.state === ArmyState.MARCH || autoBackIndex === -1) {\n                return\n            }\n            let target: NoviceAreaObj = null\n            if (autoBackIndex === -2) { //返回到最近的\n                target = this.getPlayerNotFullArmyMinFortAndMain(army.owner, army.aIndex)\n            } else {\n                const a = this.areas[autoBackIndex]\n                if (a && !a.isBattleing() && gameHpr.checkIsOneAlliance(army.owner, a.owner) && !this.isAreaFullArmy(a, army.owner)) {\n                    target = a\n                }\n            }\n            if (!target || target.index === area.index) {\n                return\n            }\n            let speed = 0\n            // 是否可以加速 城边3格内加速\n            speed -= (MAIN_CITY_MARCH_SPEED[gameHpr.getMainOutMarchSeepUpDis(target.index, area.index)] || 0)\n            // 添加行军\n            this.armyAutoBackIndexMap[army.uid] = -1\n            this.addMarchArmy(area.index, army, target.index, speed, false)\n        })\n    }\n\n    private getPlayerNotFullArmyMinFortAndMain(uid: string, startIndex: number) {\n        if (!uid) {\n            return null\n        }\n        let minDis = 100000, ret: NoviceAreaObj = null\n        const cells = uid === this.puid ? this.ownCells : this.enemyCells\n        for (let key in cells) {\n            const area = cells[key]\n            if (!area.isBattleing() && area.isRecoverPawnHP() && !this.isAreaFullArmy(area, uid)) {\n                const dis = gameHpr.getToMapCellDis(startIndex, area.index)\n                if (dis < minDis) {\n                    minDis = dis\n                    ret = area\n                }\n            }\n        }\n        return ret\n    }\n\n    private addArmyToBattle(area: NoviceAreaObj, army: ArmyObj) {\n        if (army.pawns.length === 0) {\n            return\n        }\n        const a = gameHpr.areaCenter.getArea(area.index), fsp = a?.getFspModel()\n        if (!fsp) {\n            return\n        }\n        const currentFrameIndex = fsp.getCurrentFrameIndex()\n        army.state = ArmyState.FIGHT\n        const fighters = [], isAttacker = army.owner !== area.owner\n        let pawnResObj = this.lastOccupyPawnRes[area.index]\n        if (!pawnResObj) {\n            pawnResObj = this.lastOccupyPawnRes[area.index] = {}\n        }\n        const isMyArmy = army.isOwner()\n        army.pawns.forEach(p => {\n            p.changeState(PawnState.STAND)\n            fighters.push({\n                uid: p.uid,\n                camp: this.getCamp(army.owner),\n                attackIndex: p.attackSpeed,\n                waitRound: 1,\n            })\n            if (isMyArmy) {\n                gameHpr.stringToCTypes(p.baseJson?.drill_cost).forEach(m => {\n                    pawnResObj[m.type] = (pawnResObj[m.type] || 0) + m.count\n                })\n            }\n        })\n        let attackIndex = 0\n        fsp.getBattleController().getFighters().forEach(m => {\n            if (m.attackIndex > attackIndex) {\n                attackIndex = m.attackIndex\n            }\n        })\n        fighters.sort((a, b) => b.attackIndex - a.attackIndex)\n        fighters.forEach(m => m.attackIndex = ++attackIndex)\n        let frame = {\n            type: 1,\n            currentFrameIndex: currentFrameIndex,\n            army: army.strip(),\n            fighters: fighters,\n        }\n        fsp.onFSPCheckFrame(frame)\n        this.setBattleRecord(1, area, frame)\n    }\n\n    //设置战斗记录\n    private setBattleRecord(type: number, area: NoviceAreaObj, data: any) {\n        for (let i = 0; i < this.battleRecordList.length; i++) {\n            const record = this.battleRecordList[i]\n            if (record.endTime > 0) continue\n            if (1 === type) {\n                record.addFrame(area, data)\n            }\n            else if (2 === type) {\n                record.battleEnd(area, data)\n            }\n            else if (3 === type) {\n                record.setFighterData(data)\n            }\n        }\n        if (2 === type && this.battleRecordList.length > this.battleRecordMaxCount) {\n            this.battleRecordList.length = this.battleRecordMaxCount\n        }\n    }\n\n    private setArmyPawnPosition(area: NoviceAreaObj, army: ArmyObj, dir: number) {\n        const pawnCount = army.pawns.length\n        if (pawnCount == 0 || dir < 0 || dir >= 4) {\n            return\n        } else if (gameHpr.checkIsOneAlliance(area.owner, army.owner)) { //在自己区域或者联盟区域的时候 直接设置在战斗区域\n            army.setPawnPoints(area.getPawnPointsByDoor(army.uid, dir, pawnCount))\n        } else {\n            army.setPawnPoints([area.passPoints[dir]]) //设置到关口位置\n        }\n    }\n\n    private adjustmentPawnPos(area: NoviceAreaObj) {\n        area.armys.forEach(army => {\n            if (army.state === ArmyState.MARCH) {\n                return\n            }\n            army.pawns.forEach(pawn => {\n                const point = pawn.point\n                if (area.checkIsBattleArea(point.x, point.y) && !area.banPlacePawnPointMap[point.ID()]) {\n                    return\n                }\n                const dir = mapHelper.getMinDisIndex(point, area.doorPoints)\n                const points = area.getPawnPointsByDoor('', dir, 1)\n                if (points.length > 0) {\n                    pawn.setPoint(points[0])\n                }\n            })\n        })\n    }\n\n    private addAvoidWarAreaEndTime(index: number, time: number, isAcc: boolean) {\n        const now = Date.now()\n        let endTime = this.avoidWarAreas[index]\n        if (!endTime || !isAcc) {\n            endTime = now + time\n        } else {\n            endTime += time\n        }\n        this.avoidWarAreas[index] = endTime\n        this.notifyWorld(NotifyType.AREA_AVOID_WAR, {\n            index,\n            time: endTime - now,\n        })\n    }\n\n    private addBattleDist(area: NoviceAreaObj, init?: boolean) {\n        const uids = this.battleDist[area.index] = area.getHasArmyPlayers()\n        !init && this.notifyWorld(NotifyType.BATTLE_DIST, { index: area.index, uids })\n    }\n\n    private removeBattleDist(index: number) {\n        delete this.battleDist[index]\n        this.notifyWorld(NotifyType.BATTLE_DIST, { index })\n    }\n\n    public getBattleDistMap() { return this.battleDist }\n    public getAvoidWarDistMap() { return this.avoidWarAreas }\n    public getMarchs() { return this.marchs.map(m => m.strip()) }\n    public getBTCityQueues() { return this.btCityQueues }\n\n    // 是否有到某个地方的行军\n    public isCheckHasMarchByTarget(index: number) {\n        return this.marchs.some(m => m.targetIndex === index)\n    }\n\n    public getAreaAttacker(area: NoviceAreaObj) {\n        const uid = area.owner\n        return area.armys.find(m => !m.isMarching() && !gameHpr.checkIsOneAlliance(m.owner, uid))?.owner || ''\n    }\n\n    private checkCanAddAreaArmy(area: NoviceAreaObj, owner: string, isRevoke: boolean) {\n        if (!isRevoke && this.isAreaFullArmy(area, owner)) {\n            return false //如果当前区域没有这个军队且满了 就不能进\n        } else if (gameHpr.checkIsOneAlliance(area.owner, owner)) {\n            return true //如果是联盟或者自己的土地就可以进入\n        }\n        // 如果在攻击范围内且不是免战的 就可以进入\n        return !this.isAvoidWarArea(area.index) && this.checkCanOccupyCell(area.index, owner)\n    }\n\n    // 是否可以攻击\n    private checkCanOccupyCell(index: number, uid: string) {\n        const cell = gameHpr.world.getMapCellByIndex(index)\n        if (cell && gameHpr.checkIsOneAlliance(cell.owner, uid)) {\n            return false\n        }\n        const indexs = mapHelper.getOnePointOuter(cell.actPoint, cell.getSize())\n        return indexs.some(m => !!gameHpr.checkIsOneAlliance(gameHpr.world.getMapCellByPoint(m)?.owner, uid))\n    }\n\n    private isAvoidWarArea(index: number) {\n        return !!this.avoidWarAreas[index]\n    }\n\n    private isAreaFullArmy(area: AreaObj, owner: string) {\n        return this.getAreaFullArmyCount(area, owner) >= 0\n    }\n\n    public isAreaFullArmyByIndex(index: number) {\n        const area = this.getArea(index)\n        return !area || this.isAreaFullArmy(area, this.puid)\n    }\n\n    private getAreaFullArmyCount(area: AreaObj, owner: string) {\n        const isFriend = gameHpr.checkIsOneAlliance(area.owner, owner)\n        let a = 0, b = 0\n        area.armys.forEach(m => {\n            if (gameHpr.checkIsOneAlliance(area.owner, m.owner)) {\n                a += 1\n            } else {\n                b += 1\n            }\n        })\n        if (isFriend) {\n            return a - area.maxArmyCount\n        }\n        return b - area.maxArmyCount\n    }\n\n    // 获取区域信息\n    public HD_GetAreaInfo(data: any) {\n        const index = data.index\n        const area = this.areas[index]\n        return {\n            err: '',\n            data: { data: area.strip() }\n        }\n    }\n\n    // 获取玩家军队列表\n    public HD_GetPlayerArmys(type: number, ignoreIndex: number) {\n        const uid = this.puid\n        const dist = this.getPlayerArmyDist(uid)\n        const arr = []\n        for (let key in dist) {\n            const index = Number(key)\n            let area = this.areas[index]?.proxyAO ?? this.areas[index]\n            if (!area || ignoreIndex === area.index) {\n                continue\n            }\n            area.armys.forEach(m => {\n                if (m.owner !== uid || (type === 1 && !m.isCanBattle())) {\n                    return\n                }\n                arr.push(m.strip())\n            })\n        }\n        return {\n            err: '',\n            data: { list: arr, canGotoCount: this.getArea(ignoreIndex)?.maxArmyCount || 0 }\n        }\n    }\n\n    public HD_MoveAreaPawns(data: any) {\n        const area = this.areas[data.index]\n        if (area) {\n            const army = area.getArmyByUid(data.armyUid)\n            if (army) {\n                data.pawns.forEach(m => army.pawns.find(p => p.uid === m.uid)?.setPoint(m.point))\n            }\n        }\n        return { err: '', data: null }\n    }\n\n    public HD_OpenTreasure(data: any) {\n        const { area, pawn, treasure } = this.getPawnTreasure(data.auid, data.puid, data.uid)\n        if (!area || !treasure || !pawn) {\n            return { err: 'ecode.500076', data: null }\n        } else if (treasure.rewards.length > 0) {\n            return { err: 'ecode.500076', data: null }\n        }\n        const mul = 1 + this.getPolicyEffect(CEffect.TREASURE_AWARD) * 0.01\n        this.randomTreasureRewards(treasure, mul)\n        const res: any = {\n            armyUid: data.auid,\n            uid: data.puid,\n            treasures: pawn.treasures.map(m => {\n                return {\n                    uid: m.uid,\n                    id: m.id,\n                    rewards: m.rewards.map(r => r instanceof CTypeObj ? r.toJson() : r)\n                }\n            }),\n        }\n        this.notifyArea(area.index, NotifyType.UPDATE_PAWN_TREASURE, res)\n        res.rewards = treasure.rewards.map(m => m instanceof CTypeObj ? m.toJson() : m)\n        this.tempTreasureIronCount = 0\n        for (let i = 0; i < res.rewards.length; i++) {\n            if (res.rewards[i].type === CType.IRON) {\n                this.tempTreasureIronCount += res.rewards[i].count\n            }\n        }\n        return { err: '', data: res }\n    }\n\n    public HD_ClaimTreasure(data: any) {\n        const { area, pawn, treasure } = this.getPawnTreasure(data.auid, data.puid, data.uid)\n        if (!area || !treasure || !pawn) {\n            return { err: 'ecode.500076', data: null }\n        }\n        // 发放奖励\n        this.changeCostByTypeObjs(treasure.rewards, 1)\n        // 删除宝箱\n        pawn.treasures.remove('uid', treasure.uid)\n        // 通知是否有新的宝箱\n        this.sendPlayerHasTreasure(this.puid)\n        // 通知\n        const res: any = {\n            armyUid: data.auid,\n            uid: data.puid,\n            treasures: pawn.treasures.map(m => {\n                return {\n                    uid: m.uid,\n                    id: m.id,\n                    rewards: m.rewards.map(r => r instanceof CTypeObj ? r.toJson() : r)\n                }\n            }),\n        }\n        this.notifyArea(area.index, NotifyType.UPDATE_PAWN_TREASURE, res)\n        res.rewards = this.toItemByTypeObjs(treasure.rewards)\n        this.setGuideTaskProgress(TCusCType.OPEN_TREASURE)\n        return { err: '', data: res }\n    }\n\n    // 获取建筑创建加速后的时间\n    public getBuildCreateSpeedTime(time: number, isResetAcc: boolean = false) {\n        let mul = 1\n        if (gameHpr.guide.buildAcc) {\n            if (isResetAcc) {\n                gameHpr.guide.buildAcc = false\n            }\n            mul = NOVICE_FIRST_BUILD_CREATE_MUL\n        }\n        else {\n            mul = NOVICE_BUILD_CREATE_MUL\n        }\n        time = Math.floor(time / mul) * 1000\n        let cd = 1 - 1 / mul\n        return { time, cd }\n    }\n\n    public HD_AddAreaBuild(data: any) {\n        if (this.btQueues.length >= this.getBTQueueMaxCount()) {\n            return { err: 'ecode.500014', data: null }\n        }\n        const area = this.areas[data.index]\n        if (!area || data.index !== NOVICE_MAINCITY_INDEX) {\n            return { err: 'ecode.500044', data: null }\n        }\n        // 创建一个0级的\n        const build = new BuildObj().init(data.index, ut.UID(), cc.v2(0, 0), data.id, 0)\n        // 获取位置\n        const point = area.getBuildCanPlacePos(build)\n        if (!point) {\n            return { err: 'ecode.500016', data: null } //没有可摆放的位置了\n        } else if (!this.checkAndDeductCostByTypeObjs(build.upCost)) {\n            return { err: 'ecode.500012', data: null } //扣除资源\n        }\n        // 设置位置\n        build.point.set(point)\n        // 添加到场景里面\n        area.addBuild(build.strip(), false)\n        // 添加到修建队列\n        let { time } = gameHpr.noviceServer.getBuildCreateSpeedTime(build.attrJson.bt_time, true)\n        this.putBTQueue(data.index, build.uid, build.id, 1, time)\n        // 通知\n        this.notifyArea(data.index, NotifyType.ADD_BUILD, build.strip())\n        // 返回\n        return {\n            err: '',\n            data: {\n                build: build.strip(),\n                output: this.toOutputInfo(),\n                queues: this.btQueues.map(m => m.strip()),\n            }\n        }\n    }\n\n    // 获取建筑创建加速后的时间\n    public getBuildUpSpeedTime(time: number, isResetAcc: boolean = false) {\n        let mul = 1\n        if (gameHpr.guide.buildAcc) {\n            if (isResetAcc) {\n                gameHpr.guide.buildAcc = false\n            }\n            mul = NOVICE_BUILD_SPEED_MUL\n        }\n        else {\n            mul = NOVICE_BUILD_SPEED_MUL\n        }\n        time = Math.floor(time / mul) * 1000\n        let cd = 1 - 1 / mul\n        return { time, cd }\n    }\n\n    public HD_UpAreaBuild(data: any) {\n        if (this.btQueues.length >= this.getBTQueueMaxCount()) {\n            return { err: 'ecode.500014', data: null }\n        }\n        const area = this.areas[data.index]\n        if (!area) {\n            return { err: 'ecode.500044', data: null }\n        }\n        const build = area.getBuildByUid(data.uid)\n        if (!build) {\n            return { err: 'ecode.500009', data: null }\n        } else if ((build.id !== BUILD_MAIN_NID && build.id !== BUILD_BARRACKS_NID && build.id !== BUILD_SMITHY_NID && build.id !== BUILD_GRANARY_NID\n            && build.id !== BUILD_WAREHOUSE_NID && build.id !== BUILD_HOSPITAL_NID) || build.lv >= NOVICE_MAIN_MAX_LEVEL) { //新引导可以升级主城\n            return { err: 'toast.novice_not_opt', data: null }\n        } else if (this.btQueues.has('uid', build.uid)) { //修建中\n            return { err: 'ecode.500013', data: null }\n        } else if (build.isMaxLv()) { //是否已经满级\n            return { err: 'ecode.500015', data: null }\n        } else if (build.id !== BUILD_MAIN_NID) {\n            const mb = area.getBuildById(BUILD_MAIN_NID)\n            if (!mb || build.lv >= mb.lv) {\n                return { err: 'ecode.500072', data: null } //不是主城的话要检测是否超过主城等级\n            }\n        }\n        // 扣除资源\n        if (!this.checkAndDeductCostByTypeObjs(build.upCost)) {\n            return { err: 'ecode.500012', data: null }\n        }\n        let time = this.getBuildUpSpeedTime(build.attrJson.bt_time).time\n        // 添加到修建队列\n        this.putBTQueue(data.index, build.uid, build.id, build.lv + 1, time)\n        // 返回\n        return {\n            err: '',\n            data: {\n                output: this.toOutputInfo(),\n                queues: this.btQueues.map(m => m.strip()),\n            }\n        }\n    }\n\n    public HD_CancelBT(data: any) {\n        const area = this.areas[data.index]\n        if (!area) {\n            return { err: 'ecode.500044', data: null }\n        }\n        const build = area.getBuildByUid(data.uid)\n        if (!build) {\n            return { err: 'ecode.500009', data: null }\n        }\n        // 取消队列\n        this.cancelBT(data.uid)\n        const res: any = { queues: this.btQueues.map(m => m.strip()) }\n        // 返回升到当前等级的资源\n        if (build.lv > 0) {\n            const attrId = build.id * 1000 + (build.lv - 1)\n            const json = assetsMgr.getJsonData('buildAttr', attrId)\n            if (json?.up_cost) {\n                this.changeCostByTypeObjs(gameHpr.stringToCTypes(json.up_cost), 1) //退还资源\n                res.output = this.toOutputInfo()\n            }\n        } else { //如果是0级就不返回资源 并删除掉\n            area.removeBuild(data.uid, false)\n            this.notifyArea(data.index, NotifyType.REMOVE_BUILD, data.uid)\n        }\n        return { err: '', data: res }\n    }\n\n    public HD_InDoneBt() {\n        if (this.btQueues.length === 0) {\n            return { err: '', data: { queues: [] } }\n        }\n        const gold = this.changePlayerGold(-IN_DONE_BT_GOLD)\n        if (gold === -1) {\n            return { err: 'ecode.500053', data: null } //金币不足\n        }\n        // 完成所有\n        this.completeAllBt()\n        this.isBuildInDone = true\n        // 返回\n        return {\n            err: '', data: {\n                queues: this.btQueues.map((m) => { m.strip() }),\n                gold: gold,\n            }\n        }\n    }\n\n    HD_InDoneForge() {\n        if (!this.currForgeEquip) {\n            return { err: 'ecode.500000', data: null }\n        }\n        const gold = this.changePlayerGold(-IN_DONE_BT_GOLD)\n        if (gold === -1) {\n            return { err: 'ecode.500053', data: null } //金币不足\n        }\n        this.checkUpdateForgeEquip(this.currForgeEquip.startTime + this.currForgeEquip.needTime)\n        return { err: '', data: { gold: gold } }\n\n    }\n\n    public HD_UseUpScrollUpPawnLv({ index, armyUid, uid }) {\n        const area = this.areas[index]\n\n        const army = area?.getArmyByUid(armyUid)\n        if (!army || army.owner !== this.puid) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const pawn = army.pawns.find(m => m.uid === uid)\n        if (!pawn) {\n            return { err: 'ecode.500017', data: null }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036', data: null } //当前战斗中\n        } else if (pawn.getPawnType() >= PawnType.MACHINE) {\n            return { err: 'ecode.500017', data: null } //器械不可升级\n        } else if (pawn.isMaxLv()) {\n            return { err: 'ecode.500015', data: null } //已经满级\n        } else if (this.checkPawnLving(pawn.uid)) {\n            return { err: 'ecode.500079', data: null } //已经在练级了\n        }\n        const json = pawn.attrJson\n        if (!json) {\n            return { err: 'ecode.500017', data: null }\n        } else if (!this.checkCTypes(gameHpr.stringToCTypes(json.lv_cond))) {\n            return { err: 'ecode.500033', data: null }\n        }\n        const cost = [new CTypeObj().init(CType.UP_SCROLL, 0, 1)]\n        if (!this.checkAndDeductCostByTypeObjs(cost)) { //扣除费用\n            return { err: 'ecode.500012', data: null }\n        }\n        // 直接升级\n        pawn.lv += 1\n        pawn.updateAttrJson()\n        // 通知\n        this.notifyArea(index, NotifyType.UPDATE_ARMY, army.strip())\n        return {\n            err: '', data: {\n                cost: this.toItemByTypeObjs(cost),\n                lv: pawn.lv,\n            }\n        }\n    }\n\n    // 获取免费招募剩余次数\n    public getFreeRecruitPawnSurplusCount() {\n        return Math.max(0, this.getPolicyEffect(CEffect.FREE_DRILL_COUNT) - this.freeRecruitPawnCount)\n    }\n\n    // 获取招募加速后的时间\n    public getDrillSpeedTime(time: number, isResetAcc: boolean = false) {\n        let mul = 1\n        if (gameHpr.guide.drillAcc) {\n            if (isResetAcc) {\n                gameHpr.guide.drillAcc = false\n            }\n            mul = NOVICE_FIRST_RECRUIT_SPEED_MUL\n        }\n        else {\n            mul = NOVICE_RECRUIT_SPEED_MUL\n        }\n        time = Math.floor(time / mul) * 1000\n        let cd = 1 - 1 / mul\n        return { time, cd }\n    }\n\n    public HD_DrillPawn({ index, buildUid, id, armyUid, armyName }) {\n        const json = assetsMgr.getJsonData('pawnBase', id)\n        if (!json) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const area = this.areas[index]\n        if (!area || area.owner !== this.puid) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const build = area.getBuildByUid(buildUid)\n        if (!build) {\n            return { err: 'ecode.500009', data: null }\n        } else if (json.spawn_build_id !== build.id) {\n            return { err: 'ecode.500009', data: null }\n        } else if (json.need_unlock === 1 && !this.isUnlockPawn(id)) { //是否需要解锁\n            return { err: 'ecode.500017', data: null }\n        } else if (this.isDrillPawnQueueFull(index, buildUid)) {\n            return { err: 'ecode.500018', data: null }\n        }\n        let army = area.getArmyByUid(armyUid)\n        let isFreeDrill = this.getFreeRecruitPawnSurplusCount() > 0\n        if (!army) { //这里如果没有 就说明要创建一个\n            armyName = armyName || '???'\n            if (armyUid) {\n                return { err: 'ecode.500011', data: null }\n            } else if (this.getPlayerArmyCount() >= this.getArmyMaxCount()) {\n                return { err: 'ecode.500054', data: null } //军队上限\n            } else if (this.isAreaFullArmy(area, this.puid)) {\n                return { err: 'ecode.500037', data: null } //军队已满\n            } else if (ut.getStringLen(armyName) > 12) {\n                return { err: 'ecode.500049', data: null }\n            } else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗\n                return { err: 'ecode.500082', data: null }\n            } else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(gameHpr.stringToCTypes(json.drill_cost))) { //扣除资源\n                return { err: 'ecode.500012', data: null }\n            }\n            army = new ArmyObj().fromSvr({\n                index,\n                uid: ut.UID(),\n                name: armyName,\n                owner: this.puid,\n            })\n            army = this.addAreaArmy(area, army, -1)\n        } else if (army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT) {\n            return { err: 'ecode.500019', data: null }\n        } else if (army.state === ArmyState.MARCH || army.aIndex !== index) {\n            return { err: 'ecode.500011', data: null }\n        } else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗\n            return { err: 'ecode.500082', data: null }\n        } else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(gameHpr.stringToCTypes(json.drill_cost))) { //扣除资源\n            return { err: 'ecode.500012', data: null }\n        }\n        // 添加到军队 表示这个军队有训练的士兵\n        army.drillPawns.push(id)\n        // 添加到训练队列\n        let { time } = this.getDrillSpeedTime(json.drill_time, true)\n        if (this.getFreeRecruitPawnSurplusCount() > 0) { //是否有免费次数\n            this.freeRecruitPawnCount += 1\n        }\n        this.putDrillPawnQueue(index, build.uid, army.uid, id, 1, time, this.getPolicyEffect(CEffect.XL_CD))\n        this.emit('DRILL_PAWN_START') //给引导用的\n        // 返回\n        return {\n            err: '', data: {\n                output: this.toOutputInfo(),\n                queues: this.toDrillPawnQueue(index),\n                army: army.strip(),\n                freeRecruitPawnCount: this.freeRecruitPawnCount,\n            }\n        }\n    }\n\n    public HD_CancelDrillPawn({ index, buildUid, uid }) {\n        const area = this.areas[index]\n        if (!area) {\n            return { err: 'ecode.500017', data: null }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036', data: null }\n        }\n        const info = this.getDrillPawnQueueInfo(index, buildUid, uid)\n        if (!info) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const json = assetsMgr.getJsonData('pawnBase', info.id)\n        if (!json) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const army = area.getArmyByUid(info.aUid)\n        if (!army || army.owner !== this.puid) {\n            return { err: 'ecode.500017', data: null }\n        }\n        const dInfo = this.cancelDrillPawnQueue(index, buildUid, uid)\n        if (!dInfo) { // 取消\n            return { err: 'ecode.500017', data: null }\n        }\n        // 返还资源\n        if (dInfo.startTime === 0) {\n            this.changeCostByTypeObjs(gameHpr.stringToCTypes(json.drill_cost), 1)\n        }\n        // 删除军队里面的\n        army.drillPawns.remove(info.id)\n        // 如果没有了 就删除\n        if (army.getActPawnCount() === 0) {\n            area.removeArmy(army.uid)\n            this.notifyPlayerArmyDistInfo()\n        }\n        // 返回\n        return {\n            err: '', data: {\n                output: this.toOutputInfo(),\n                queues: this.toDrillPawnQueue(index),\n                army: army.strip(),\n            }\n        }\n    }\n\n    // 获取治疗速度\n    public getHospitaCureSpeedTime(time: number, lv: number) {\n        let mul = NOVICE_HOSPITAL_SPEED_MUL\n        let param = NOVICE_CURE_TIME_PARAM_MAP[lv]\n        time = Math.floor(time * param / mul) * 1000\n        let cd = 1 - 1 / mul\n        return { time, cd }\n    }\n\n    // 治疗士兵\n    public HD_CureInjuryPawn(data: any) {\n        const { index, armyUid, armyName, pawnUid } = data\n        let pawnInfo: NoviceInjuryPawnInfo = null\n        let pawnIndex = 0\n        for (let i = 0; i < this.injuryPawns.length; i++) {\n            if (this.injuryPawns[i].uid === pawnUid) {\n                pawnInfo = this.injuryPawns[i]\n                pawnIndex = i\n                break\n            }\n        }\n        if (pawnInfo) {\n            const json = assetsMgr.getJsonData('pawnBase', pawnInfo.id)\n            if (!json) {\n                return { err: 'ecode.500017', data: null }\n            }\n            const area = this.areas[index]\n            if (!area || area.owner !== this.puid) {\n                return { err: 'ecode.500017', data: null }\n            }\n            let army = area.getArmyByUid(armyUid)\n            let drillCostList = gameHpr.stringToCTypes(json.drill_cost)\n            for (let i = 0; i < drillCostList.length; i++) {\n                drillCostList[i].count = Math.floor(drillCostList[i].count * NOVICE_CURE_RES_PARAM_MAP[pawnInfo.lv])\n            }\n            let isFreeDrill = false\n            if (!army) { //这里如果没有 就说明要创建一个\n                if (armyUid) {\n                    return { err: 'ecode.500011', data: null }\n                } else if (this.getPlayerArmyCount() >= this.getArmyMaxCount()) {\n                    return { err: 'ecode.500054', data: null } //军队上限\n                } else if (this.isAreaFullArmy(area, this.puid)) {\n                    return { err: 'ecode.500037', data: null } //军队已满\n                } else if (ut.getStringLen(armyName) > 12) {\n                    return { err: 'ecode.500049', data: null }\n                } else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗\n                    return { err: 'ecode.500082', data: null }\n                } else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(drillCostList)) { //扣除资源\n                    return { err: 'ecode.500012', data: null }\n                }\n                army = new ArmyObj().fromSvr({\n                    index,\n                    uid: ut.UID(),\n                    name: armyName,\n                    owner: this.puid,\n                })\n                army = this.addAreaArmy(area, army, -1)\n            } else if (army.getActPawnCount() >= ARMY_PAWN_MAX_COUNT) {\n                return { err: 'ecode.500019', data: null }\n            } else if (army.state === ArmyState.MARCH || army.aIndex !== index) {\n                return { err: 'ecode.500011', data: null }\n            } else if (!isFreeDrill && !this.checkCerealConsume(json.cereal_cost)) { //检测粮耗\n                return { err: 'ecode.500082', data: null }\n            } else if (!isFreeDrill && !this.checkAndDeductCostByTypeObjs(drillCostList)) { //扣除资源\n                return { err: 'ecode.500012', data: null }\n            }\n            this.injuryPawns.splice(pawnIndex, 1)\n            let cureObj = new NovicePawnCureInfoObj()\n            cureObj.curePawn(pawnInfo, army)\n            this.curingPawns.push(cureObj)\n            army.curingPawns.push(cureObj)\n            if (1 === this.curingPawns.length) {\n                this.curingPawns[0].checkStartCure(0)\n            }\n\n            // 通知伤兵移除\n            this.notifyPlayer(NotifyType.PAWN_INJURY_REMOVE, cureObj.uid)\n            this.setGuideTaskProgress(TCusCType.CURE_ONE_PAWN)\n\n            return { err: null, data: { army: army.strip(), output: this.toOutputInfo(), queues: this.curingPawns.map(v => v.strip()) } }\n        }\n        return { err: 'ecode.500017', data: null }\n    }\n\n    // 检查更新治疗\n    private checkUpdateCurePawn(nowTime: number) {\n        if (this.curingPawns.length === 0) {\n            return\n        }\n        let curePawn = this.curingPawns[0]\n        let costTime = nowTime - curePawn.deadTime\n        curePawn.surplusTime = curePawn.needTime - costTime\n        if (curePawn.surplusTime <= 0) {\n            this.curingPawns.shift()\n            if (this.curingPawns.length > 0) {\n                this.curingPawns[0].checkStartCure(curePawn.surplusTime)\n            }\n            this.notifyPlayer(NotifyType.PAWN_CURING_QUEUE, this.curingPawns.map(v => v.strip()))\n            let army = this.areas[curePawn.index].getArmyByUid(curePawn.auid)\n            if (army) {\n                for (let j = 0; j < army.curingPawns.length; j++) {\n                    if (army.curingPawns[j].uid === curePawn.uid) {\n                        army.curingPawns.splice(j, 1)\n                        break\n                    }\n                }\n                // 通知训练完成\n                this.areaPawnDrillComplete(army.index, curePawn.auid, curePawn.id, curePawn.lv, curePawn.uid)\n                // 通知玩家军队分布\n                this.notifyPlayerArmyDistInfo()\n            }\n        }\n    }\n\n    // 取消治疗\n    HD_CancelCurePawn(data: any) {\n        const { index, uid } = data\n        let curePawn = this.curingPawns.remove('uid', uid)\n        let army = this.areas[index].getArmyByUid(curePawn.auid)\n        if (army) {\n            army.curingPawns.remove('uid', uid)\n            let injuryPawn = new NoviceInjuryPawnInfo()\n            injuryPawn.cancelCurePawn(curePawn)\n            this.injuryPawns.add(injuryPawn)\n            this.notifyPlayer(NotifyType.PAWN_INJURY_ADD, injuryPawn.strip())\n            if (army.pawns.length === 0 && army.curingPawns.length === 0) {\n                this.areas[index].removeArmy(army.uid)\n                // 通知玩家军队分布\n                this.notifyPlayerArmyDistInfo()\n            }\n            return { err: null, data: { output: this.toOutputInfo(), queues: this.curingPawns.map(v => v.strip()), army: army.strip() } }\n        }\n        return { err: 'ecode.500017', data: null }\n    }\n\n    // 放弃治疗\n    HD_GiveupInjuryPawn(data: any) {\n        const { uid } = data\n        this.injuryPawns.remove('uid', uid)\n        return { err: null, data: { injuryPawns: this.injuryPawns.map(v => v.strip()) } }\n    }\n\n    public HD_MoveAreaBuild({ index, uid, point }) {\n        const area = this.getArea(index)\n        if (!area || area.owner !== this.puid) {\n            return\n        }\n        const build = area.getBuildByUid(uid)\n        if (!build) {\n            return\n        } else if (!area.checkCanPlace(build, point)) {\n            return //位置被占用\n        }\n        build.point.set(point) //直接改变位置\n        this.notifyArea(index, NotifyType.MOVE_BUILD, { uid, point }) //通知\n    }\n\n    public async HD_ChangePawnArmy({ index, armyUid, uid, newArmyUid, isNewCreate, attackSpeed, equipId, skinId }): Promise<any> {\n        const area = this.getArea(index)\n        const preArmy = area?.getArmyByUid(armyUid)\n        if (!preArmy || preArmy.owner != this.puid) {\n            return { err: 'ecode.500017' }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036' }//当前战斗中\n        }\n        const pawn = preArmy.pawns.find(m => m.uid === uid)\n        if (!pawn) {\n            return { err: 'ecode.500017' }\n        }\n        let newArmy = area.getArmyByUid(newArmyUid)\n        if (isNewCreate) { //新建\n            if (!newArmyUid) {\n                return { err: 'ecode.500011', data: null }\n            } else if (this.getPlayerArmyCount() >= this.getArmyMaxCount()) {\n                return { err: 'ecode.500054', data: null } //军队上限\n            } else if (this.isAreaFullArmy(area, this.puid)) {\n                return { err: 'ecode.500037', data: null } //军队已满\n            } else if (ut.getStringLen(newArmyUid) > 12) {\n                return { err: 'ecode.500049', data: null }\n            }\n            newArmy = new ArmyObj().fromSvr({\n                index: area.index,\n                uid: ut.UID(),\n                name: newArmyUid,\n                owner: this.puid,\n            })\n            newArmy = this.addAreaArmy(area, newArmy, -1)\n        }\n        if (!newArmy || newArmy.owner !== this.puid) {\n            return { err: 'ecode.500011' }\n        } else if (newArmy.getActPawnCount() >= ARMY_PAWN_MAX_COUNT) {\n            return { err: 'ecode.500019' } //军队士兵已满\n        }\n        // 先从前一个军队中删除\n        preArmy.removePawn(uid)\n        if (preArmy.getActPawnCount() === 0) { //如果一个士兵都没有了\n            area.removeArmy(preArmy.uid)\n        } else {\n            this.notifyArea(index, NotifyType.UPDATE_ARMY, preArmy.strip())\n        }\n        // 出手速度\n        pawn.attackSpeed = attackSpeed\n        // 装备\n        const equip = this.getPlayerEquip(equipId)\n        if (equip?.checkExclusivePawn(pawn.id)) {\n            pawn.changeEquip(equip.strip(), false)\n        }\n        // 添加到新的军队中\n        newArmy.addPawn(pawn.strip())\n        // 改变练级队列里面的士兵\n        this.changePawnLvingInfo(pawn)\n        this.notifyArea(index, NotifyType.UPDATE_ARMY, newArmy.strip())\n        // 通知军队分布信息\n        this.notifyPlayerArmyDistInfo()\n        return { data: { uid: newArmy.uid, name: newArmy.name, } }\n    }\n\n    public async HD_ChangeConfigPawnEquip({ id, equipUid, skinId }) {\n        // 装备\n        if (equipUid) {\n            const equip = this.getPlayerEquip(equipUid)\n            if (!equip || !equip.checkExclusivePawn(id)) {\n                return { err: 'ecode.500059' }\n            }\n        }\n        this.configPawnMap[id] = { equipUid, skinId }\n        return {}\n    }\n\n    public async HD_ChangePawnAttr({ index, armyUid, uid, attackSpeed, equipUid, skinId, syncEquip }) {\n        const area = this.getArea(index), army = area?.getArmyByUid(armyUid)\n        const pawn = army?.pawns.find(m => m.uid === uid)\n        if (!pawn || pawn.owner !== this.puid) {\n            return { err: 'ecode.500017' }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036' }//当前战斗中\n        }\n        // 出手速度\n        pawn.attackSpeed = attackSpeed\n        let datas: any[] = [pawn.strip()]\n        // 装备\n        if (pawn.equip?.uid !== equipUid) {\n            const equip = this.getPlayerEquip(equipUid)\n            if (equip && equip.checkExclusivePawn(pawn.id)) {\n                const arr = area.changePawnEquip(pawn, equip, syncEquip)\n                if (arr.length > 0) {\n                    datas = arr\n                }\n            }\n            //刀盾兵更换装备\n            if (pawn.type === 2 && equip.id == this.equipSlots[EQUIP_SLOT_CONF[1]].id) {\n                this.setGuideTaskProgress(TCusCType.CHANGE_PAWN_EQUIP)\n            }\n        }\n        // 通知\n        this.notifyArea(index, NotifyType.CHANGE_PAWN_ATTR, datas)\n        this.updateGuideTasksProgress()\n        return {}\n    }\n\n    public async HD_PawnLving({ index, auid, puid }) {\n        const area = this.getArea(index), army = area?.getArmyByUid(auid)\n        if (!army || army.owner !== this.puid) {\n            return { err: 'ecode.500017' }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036' } //当前战斗中\n        } else if (army.state === ArmyState.MARCH || army.aIndex !== index) {\n            return { err: 'ecode.500011' }\n        }\n        const pawn = army.pawns.find(m => m.uid === puid)\n        if (!pawn) {\n            return { err: 'ecode.500017' }\n        } else if (pawn.getPawnType() >= PawnType.MACHINE) { //器械不能训练\n            return { err: 'ecode.500017' }\n        } else if (pawn.isMaxLv()) {\n            return { err: 'ecode.500015' }\n        } else if (this.isPawnLvingQueueFull(index)) {\n            return { err: 'ecode.500101' }\n        } else if (this.isInLvingQueue(index, puid)) {\n            return { err: 'ecode.500079' }\n        }\n        const json = pawn.attrJson\n        if (!this.checkCTypesNeedArea(gameHpr.stringToCTypes(json.lv_cond), area.index)) {\n            return { err: 'ecode.500033' }\n        } else if (!this.checkAndDeductCostByTypeObjs(pawn.upCost)) { //扣除资源\n            return { err: 'ecode.500012' }\n        }\n        // 添加到训练队列\n        this.putPawnLvingQueue(index, army.uid, pawn.uid, pawn.id, pawn.lv + 1, json.lv_time, this.getPolicyEffect(CEffect.UPLVING_CD))\n        // 返回\n        return {\n            data: {\n                cost: this.toItemByTypeObjs(pawn.upCost),\n                queues: this.toPawnLvingQueue(index),\n            }\n        }\n    }\n\n    public HD_CancelPawnLving({ index, uid }) {\n        const area = this.getArea(index)\n        if (!area) {\n            return { err: 'ecode.500017' }\n        } else if (area.isBattleing()) {\n            return { err: 'ecode.500036' }//当前战斗中\n        }\n        const info = this.getPawnLvingQueueInfo(index, uid)\n        if (!info) {\n            return { err: 'ecode.500017' }\n        }\n        const army = area.getArmyByUid(info.auid)\n        if (!army || army.owner !== this.puid) {\n            return { err: 'ecode.500017' }\n        }\n        const pawn = army.pawns.find(m => m.uid === info.puid)\n        if (!pawn) {\n            return { err: 'ecode.500017' }\n        }\n        const lInfo = this.cancelPawnLvingQueue(index, uid)\n        if (!lInfo) { //取消\n            return { err: 'ecode.500017' }\n        }\n        // 返还资源\n        if (lInfo.startTime === 0) {\n            this.changeCostByTypeObjs(pawn.upCost, 1)\n        }\n        // 返回\n        return {\n            data: {\n                cost: this.toItemByTypeObjs(pawn.upCost),\n                queues: this.toPawnLvingQueue(index),\n            }\n        }\n    }\n\n    //锻造加速后的时间\n    public getForgeSpeedTime(time: number, isResetAcc: boolean = false) {\n        let mul = 1\n        if (gameHpr.guide.forgeAcc) {\n            if (isResetAcc) {\n                gameHpr.guide.forgeAcc = false\n            }\n            mul = NOVICE_FIRST_FORGE_SPEED_MUL\n        }\n        else {\n            mul = NOVICE_FORGE_SPEED_MUL\n        }\n        time = Math.floor(time / mul) * 1000\n        let cd = 1 - 1 / mul\n        return { time, cd }\n    }\n\n    public HD_ForgeEquip({ uid, lockEffect }) {\n        if (this.currForgeEquip) {\n            return { err: 'ecode.500058' }\n        }\n        let recastCount = 0 //重铸次数\n        let isFreeForge = false\n        const cost: CTypeObj[] = []\n        const equip = this.getPlayerEquip(uid)\n        let json: any = null\n        let id = 0\n        if (equip) { //表示重铸\n            json = equip.json\n            id = equip.id\n            recastCount = equip.recastCount\n            isFreeForge = equip.nextForgeFree\n            if (lockEffect > 0 && equip.exclusive_pawn > 0 && equip.getEffectByType(lockEffect)) {\n                cost.push(new CTypeObj().init(CType.FIXATOR, 0, 1))\n            } else {\n                lockEffect = 0\n            }\n        } else {\n            id = Number(uid.split('_')[0])\n            json = assetsMgr.getJsonData('equipBase', id)\n        }\n        if (!json) {\n            return { err: 'ecode.500059' }\n        }\n        //  else if (!this.isUnlockEquip(id)) { //是否需要解锁\n        //     return { err: 'ecode.500059' }\n        // }\n        let { time } = this.getForgeSpeedTime(json.forge_time, true)\n        const s2c: any = {}\n        if (!isFreeForge) {\n            cost.pushArr(gameHpr.stringToCTypes(json.forge_cost))\n        }\n        if (cost.length === 0) {\n        } else if (!this.checkAndDeductCostByTypeObjs(cost)) { //扣除费用\n            return { err: 'ecode.500012' }\n        } else {\n            s2c.cost = this.toItemByTypeObjs(cost)\n        }\n        // 开始打造\n        this.forgeEquip(uid, time, lockEffect)\n        s2c.currForgeEquip = this.toForgeEquipData()\n        // 下次是否免费\n        if (equip) {\n            equip.nextForgeFree = ut.chance(this.getPolicyEffect(CEffect.FREE_RECAST))\n            s2c.nextForgeFree = equip.nextForgeFree\n        }\n        this.emit('FORGE_EQUIP_START')\n        return { data: s2c }\n    }\n\n    public HD_RestoreForge(data: any) {\n        if (this.currForgeEquip) {\n            return { err: 'ecode.500058' }\n        }\n        const equip = this.getPlayerEquip(data.uid)\n        if (!equip) {\n            return { err: 'ecode.500059' }\n        } else if (equip.lastAttrs.length === 0) {\n            return { err: 'ecode.500091' }\n        }\n        const json = equip.json\n        if (!json) {\n            return { err: 'ecode.500059' }\n        } else if (!this.checkAndDeductCostByTypeObjs([new CTypeObj().init(CType.IRON, 0, json.restore_cost)])) { //扣除费用\n            return { err: 'ecode.500012' }\n        }\n        // 还原\n        this.restoreEquipAttr(equip)\n        // 更新玩家装备到临时记录列表\n        this.updatePlayerPawnEquipInfo(this.puid, equip.strip())\n        // 返回\n        return {\n            data: {\n                equip: equip.toDB(),\n                iron: this.iron,\n            }\n        }\n    }\n\n    public HD_CreateCity({ index, id }) {\n        const cell = this.getArea(index)\n        if (!cell || cell.owner !== this.puid || cell.cityId !== 0) {\n            return { err: 'ecode.500000' }\n        } else if (this.checkCellBTCitying(index)) { //正在修建中\n            return { err: 'ecode.500041' }\n        }\n        const json = assetsMgr.getJsonData('city', id)\n        if (!json) {\n            return { err: 'ecode.500009' }\n        } else if (json.need_land > 0 && json.need_land !== cell.getLandType()) {\n            return { err: 'ecode.500009' }\n        } else if (!this.checkCTypes(gameHpr.stringToCTypes(json.bt_cond))) {\n            return { err: 'ecode.500033' }\n        }\n        const count = this.getPlayerCityCountByID(id)\n        if (count >= json.bt_count) {\n            return { err: 'ecode.500043' } //超出修建数量\n        } else if (!this.checkAndDeductCostByTypeObjs(gameHpr.stringToCTypes(json.bt_cost))) { // 扣除资源\n            return { err: 'ecode.500012' }\n        }\n        // 如果是要塞再看是否有加速修建要塞的政策\n        let cd = 0\n        if (id === CITY_FORT_NID) {\n            cd += this.getPolicyEffect(CEffect.CITY_BUILD_CD)\n        }\n        let time = Math.floor(json.bt_time * 1000 * (1 - cd * 0.01))\n        // 加入队列\n        this.putBTCityQueue(index, id, Math.max(1000, time))\n        // 返回\n        return { data: { output: this.toOutputInfo() } }\n    }\n\n    public HD_DismantleCity(data: any) {\n        const cell = this.getArea(data.index)\n        if (!cell || cell.owner !== this.puid || cell.cityId === 0) {\n            return { err: 'ecode.500000' }\n        } else if (this.checkCellBTCitying(data.index)) { //正在拆除中\n            return { err: 'ecode.500042' }\n        }\n        const json = assetsMgr.getJsonData('city', cell.cityId)\n        if (!json) {\n            return { err: 'ecode.500009' }\n        }\n        // 加入队列\n        this.putBTCityQueue(data.index, 0, json.dt_time * 1000)\n        return { err: '', data: null }\n    }\n\n    public HD_StudySelect({ lv, id, tp }) {\n        let data: any = {}\n        let curBuildLv = 0\n        switch (tp) {\n            case StudyType.POLICY:\n                this.policySlots[lv].id = id\n                this.policySlots[lv].selectIds = []\n                this.policySlots[lv].init()\n                data.slots = this.policySlots\n                this.checkPolicyWithType(this.policySlots[lv].type)\n                curBuildLv = gameHpr.player.getBuildLv(BUILD_MAIN_NID)\n                break;\n            case StudyType.PAWN:\n                this.pawnSlots[lv].id = id\n                this.pawnSlots[lv].selectIds = []\n                this.pawnSlots[lv].init()\n                data.slots = this.pawnSlots\n                curBuildLv = gameHpr.player.getBuildLv(BUILD_BARRACKS_NID)\n                break;\n            case StudyType.EQUIP:\n            case StudyType.EXCLUSIVE:\n                this.equipSlots[lv].id = id\n                this.equipSlots[lv].selectIds = []\n                this.equipSlots[lv].init()\n                data.slots = this.equipSlots\n                curBuildLv = gameHpr.player.getBuildLv(BUILD_SMITHY_NID)\n                break;\n        }\n        this.resetNextSelect(tp, curBuildLv)\n        this.emit('STUDY_SELECT_FINISH')\n        return { err: null, data }\n    }\n\n    //重置下一级可以解锁的研究内容\n    public resetNextSelect(type: number, curLv: number) {\n        let cfgList = []\n        let slots = {}\n        switch (type) {\n            case StudyType.POLICY:\n                cfgList = POLICY_SLOT_CONF\n                slots = this.policySlots\n                break\n            case StudyType.EQUIP:\n                cfgList = EQUIP_SLOT_CONF\n                slots = this.equipSlots\n                break\n            case StudyType.PAWN:\n                cfgList = PAWN_SLOT_CONF\n                slots = this.pawnSlots\n                break\n        }\n        let unLockIdList = []\n        for (let i = 0; i < cfgList.length; i++) {\n            let lv = cfgList[i]\n            let slot = slots[lv]\n            if (slot.id) {\n                unLockIdList.push(slot.id)\n            }\n            else if (lv <= curLv) {\n                if (0 === slot.selectIds.length) {\n                    for (let j = 0; j < unLockIdList.length; j++) {\n                        let index = slot.allSelectIds.indexOf(unLockIdList[j])\n                        if (index > -1) {\n                            slot.allSelectIds.splice(index, 1)\n                        }\n                    }\n                    if (slot.selectIds.length === 0 && StudyType.EQUIP === type && lv === 1) {\n                        let tempList: number[] = slot.allSelectIds.concat()\n                        let index = tempList.indexOf(NOVICE_EQUIP_SLOTS_FIRST_ID)\n                        if (index > -1) {\n                            tempList.splice(index, 1)\n                        }\n                        slot.selectIds = this.randListCount(tempList)\n                        slot.selectIds[0] = NOVICE_EQUIP_SLOTS_FIRST_ID//第一个装备默认流星锤\n                    }\n                    else {\n                        slot.selectIds = this.randListCount(slot.allSelectIds)\n                    }\n                }\n                break\n            }\n        }\n        return slots\n    }\n\n    public HD_CeriStartStudy({ lv, id }) {\n        // const slot = this.getCeriSlotByLv(lv)\n        // if (!slot || !slot.isCanStudy()) {\n        //     return { err: 'ecode.500087' }\n        // } else if (!slot.selectIds.some(m => m === id)) {\n        //     return { err: 'ecode.500087' }\n        // } else if (!this.checkPerCeriSlotIsStudy(lv)) {\n        //     return { err: 'ecode.500088' } //需要研究上一个槽位\n        // }\n        // const json = assetsMgr.getJsonData('ceri', id)\n        // if (!json) {\n        //     return { err: 'ecode.500087' }\n        // }\n        // // 开始研究\n        // slot.startStudy(id)\n        // const s2c: any = { slot: slot.strip() }\n        // // 检测是否有下一个槽位 给下一个槽位随机选择\n        // const next = this.getCeriSlotByLv(lv + 1)\n        // if (next && next.id === 0 && next.selectIds.length === 0) {\n        //     next.selectIds = this.ceriRandomSelect(next.type, next.lv)\n        //     s2c.next = next.strip()\n        // }\n        return { err: null, data: {} }\n    }\n\n    public HD_CeriResetSelect(data: any) {\n        let slot: any = null\n        let slots = {}\n        switch (data.tp) {\n            case StudyType.POLICY:\n                slots = this.policySlots\n                break;\n            case StudyType.EQUIP:\n                slots = this.equipSlots\n                break;\n            case StudyType.PAWN:\n                slots = this.pawnSlots\n                break;\n        }\n        slot = slots[data.lv]\n        if (!slot || !slot.isCanStudy()) {\n            return { err: 'ecode.500087' }\n        } else if (!this.checkPerSlotIsStudy(slot.lv, slots)) {\n            return { err: 'ecode.500088' } //需要研究上一个槽位\n        }\n        let selectIds = this.randListCount(slot.allSelectIds)\n        slot.selectIds = selectIds\n        return { err: null, data: { selectIds } }\n    }\n\n    public HD_ClaimTaskReward(data: any) {\n        if (!this.guideTasks.has('id', data.id)) {\n            return { err: 'ecode.500057' }\n        }\n        const json = assetsMgr.getJsonData('guideTask', data.id)\n        if (!json) {\n            return { err: 'ecode.500057' }\n        }\n        const items = gameHpr.stringToCTypes(json.reward)\n        // 发放道具\n        this.changeCostByTypeObjs(items, 1)\n        // 放入已完成列表 并获取下一个任务\n        let index = this.guideTasks.findIndex(m => m.id === data.id)\n        if (json.next_id) {\n            let progress = 0\n            let nextJson = assetsMgr.getJsonData('guideTask', json.next_id)\n            const cond = new TaskCondObj().fromString(nextJson.cond)\n            if (cond.type === TCType.CUSTOM && cond.id in this.tempGuideTaskProgress) {\n                progress = this.tempGuideTaskProgress[cond.id]\n                delete this.tempGuideTaskProgress[cond.id]\n            }\n            this.guideTasks[index] = { id: json.next_id, progress: progress }\n        } else {\n            this.guideTasks.splice(index, 1)\n        }\n        // 通知任务奖励领取\n        this.emit(EventType.GUIDE_TASK_REWARD_CLAIM, { id: data.id })\n        // 上报\n        taHelper.trackNovice('ta_rookieTask', { rookie_id: data.id, uid: gameHpr.getUid() })\n        // 返回\n        return {\n            data: {\n                rewards: this.toItemByTypeObjs(items),\n                tasks: this.guideTasks.map(m => { return { id: m.id, progress: m.progress } }),\n            }\n        }\n    }\n\n    // 检测更新研究\n    private checkUpdateStudy(now: number) {\n        // this.ceriSlots.forEach(slot => {\n        //     if (slot.startTime == 0 || slot.needTime == 0) {\n        //         return\n        //     } else if (now - slot.startTime >= slot.needTime) {\n        //         slot.startTime = 0\n        //         slot.selectIds = []\n        //         this.notifyPlayer(NotifyType.CERI_STUDY_DONE, slot.strip())\n        //         // 检测是否有下一个槽位 给下一个槽位随机选择 但是目前其实在开始研究的时候就给下一个随机了 这里只是再次判断一下\n        //         const next = this.getCeriSlotByLv(slot.lv + 1)\n        //         if (next && next.id === 0 && next.selectIds.length === 0) {\n        //             next.selectIds = this.ceriRandomSelect(next.type, next.lv)\n        //             this.notifyPlayer(NotifyType.CERI_STUDY_DONE, next.strip())\n        //         }\n        //     }\n        // })\n    }\n\n    private checkPerSlotIsStudy(lv: number, slots: any) {\n        if (lv === 1) {\n            return true\n        }\n        for (let key in slots) {\n            if (Number(key) < lv) {\n                let slot = slots[key]\n                if (slot.id === 0) {\n                    return false\n                }\n            }\n        }\n        return true\n    }\n\n    private putBTCityQueue(index: number, id: number, time: number) {\n        const info = new NoviceBTCityObj().init(index, id, time)\n        this.btCityQueues.push(info)\n        this.notifyWorld(NotifyType.ADD_BTCITY, info.strip()) //通知\n    }\n\n    // 检测更新建造队列\n    private checkUpdateBTCityQueue(now: number) {\n        for (let i = this.btCityQueues.length - 1; i >= 0; i--) {\n            const m = this.btCityQueues[i]\n            if (now - m.startTime < m.needTime) {\n                continue\n            }\n            // 删除\n            this.btCityQueues.splice(i, 1)\n            // 设置地块城市信息\n            const area = this.getArea(m.aIndex)\n            if (!area) {\n                continue\n            }\n            area.updateCity(m.cityId)\n            area.updateSize()\n            area.updateMaxHP() //刷新最大血量\n            // 如果是要塞恢复所有士兵血量\n            if (area.isRecoverPawnHP() && !area.isBattleing()) {\n                area.recoverAllPawnHP()\n                this.notifyArea(area.index, NotifyType.UPDATE_ALL_PAWN_HP, area.armys.map(m => m.strip()))\n            }\n            // 刷新玩家产量\n            this.updateOpSec(true)\n            // 通知\n            this.notifyWorld(NotifyType.REMOVE_BTCITY, {\n                index: m.aIndex,\n                cell: area.toBaseData(),\n                builds: area.builds.map(m => m.strip()),\n            })\n        }\n    }\n\n    private getPlayerCityCountByID(id: number) {\n        let count = 0\n        for (let key in this.ownCells) {\n            const area = this.ownCells[key]\n            if (area.cityId === id || this.getBTCityIdByIndex(area.index) === id) {\n                count += 1\n            }\n        }\n        return count\n    }\n\n    private checkCellBTCitying(index: number) {\n        return this.getBTCityIdByIndex(index) !== -1\n    }\n\n    private getBTCityIdByIndex(index: number) {\n        return this.btCityQueues.find(m => m.aIndex === index)?.cityId ?? -1\n    }\n\n    private forgeEquip(uid: string, time: number, lockEffect: number) {\n        const cd = this.effects[CEffect.FORGE_CD] || 0 //这里享受 减少速度\n        time = Math.floor(time * (1 - cd * 0.01))\n        this.currForgeEquip = {\n            uid,\n            startTime: Date.now(),\n            needTime: time,\n            lockEffect,\n        }\n    }\n\n    private toForgeEquipData() {\n        const m = this.currForgeEquip\n        if (!m) {\n            return null\n        }\n        return {\n            uid: m.uid,\n            needTime: m.needTime,\n            surplusTime: Math.max(m.needTime - (Date.now() - m.startTime), 0),\n            lockEffect: m.lockEffect,\n        }\n    }\n\n    // 刷新打造装备\n    private checkUpdateForgeEquip(now: number) {\n        if (!this.currForgeEquip) {\n            return\n        } else if (now - this.currForgeEquip.startTime < this.currForgeEquip.needTime) {\n            return\n        }\n        const lockEffect = this.currForgeEquip.lockEffect\n        let uid = this.currForgeEquip.uid\n        this.currForgeEquip = null //标记为nil\n        const equip = this.forgePlayerEquip(uid, lockEffect)\n        // 通知\n        this.notifyPlayer(NotifyType.FORGE_EQUIP_RET, equip.toDB())\n    }\n\n    private forgePlayerEquip(uid: string, lockEffect: number) {\n        let id = Number(uid.split('_')[0])\n        let equip = this.equips.find(m => m.id === id)\n        // 随机属性\n        if (!equip) { //如果没有就表示是打造\n            equip = new EquipInfo().init(uid, id, [])\n            this.randomEquipAttr(equip)\n            this.equips.push(equip)\n        } else {\n            this.randomEquipAttr(equip, lockEffect)\n            equip.recastCount += 1\n            // 更新玩家装备到临时记录列表\n            this.updatePlayerPawnEquipInfo(this.puid, equip.strip())\n        }\n        return equip\n    }\n\n    public randomEquipAttr(equip: EquipInfo, lockEffect?: number) {\n        // 先记录上一次的属性\n        equip.lastAttrs = this.cloneEquipAttrs(equip.attrs)\n        equip.attrs = []\n        //\n        let lockAttr: number[] = []\n        if (lockEffect > 0 && equip.exclusive_pawn > 0) {\n            lockAttr = equip.lastAttrs.find(m => m.attr.length >= 2 && m.attr[0] === 2 && m.attr[1] === lockEffect)?.attr || []\n        }\n        // 主属性 0\n        if (equip.json.hp) { // 1\n            const arr = ut.stringToNumbers(equip.json.hp, ',')\n            equip.attrs.push({ attr: [0, 1, ut.random(arr[0], arr[1])] })\n        }\n        if (equip.json.attack) {  // 2\n            const arr = ut.stringToNumbers(equip.json.attack, ',')\n            equip.attrs.push({ attr: [0, 2, ut.random(arr[0], arr[1])] })\n        }\n        // 效果\n        let effectIds = ut.stringToNumbers(equip.json.effect, '|')\n        const effectIndexMap = {}\n        effectIds.forEach((id, i) => effectIndexMap[id] = i + 1)\n        let cnt = equip.json.effect_count\n        if (lockAttr && effectIndexMap[lockAttr[1]] > 0) {\n            cnt = Math.max(cnt - 1, 1)                           //如果有锁定效果 这里就要少一个\n            effectIds.remove(lockAttr[1]) //删除\n        } else {\n            lockAttr = null\n        }\n        const ids: number[] = []\n        for (let i = 0; i < cnt && i < effectIds.length; i++) {\n            const index = ut.random(0, effectIds.length - 1)\n            ids.push(effectIds[index])\n            effectIds.splice(index, 1)\n        }\n        const effects: number[][] = []\n        ids.forEach(id => {\n            const json = assetsMgr.getJsonData('equipEffect', id)\n            if (json) {\n                const v = [2, id]\n                let arr = ut.stringToNumbers(json.value, ',')\n                if (arr.length === 2) {\n                    v.push(ut.random(arr[0], arr[1]))\n                } else {\n                    v.push(0) //这里就算没有 也要push一个0因为 后面可能有概率\n                }\n                arr = ut.stringToNumbers(json.odds, ',')\n                if (arr.length === 2) {\n                    v.push(ut.random(arr[0], arr[1]))\n                }\n                effects.push(v)\n            }\n        })\n        if (lockAttr) {\n            effects.push(lockAttr)\n        }\n        effects.sort((a, b) => effectIndexMap[a[1]] - effectIndexMap[b[1]]) //这里要排个序 必须根据配置顺序来\n        effects.forEach(m => equip.attrs.push({ attr: m }))\n        equip.updateAttr()\n    }\n\n    private restoreEquipAttr(equip: EquipInfo) {\n        if (equip.lastAttrs.length > 0) {\n            equip.attrs = this.cloneEquipAttrs(equip.lastAttrs)\n            equip.lastAttrs = []\n            equip.updateAttr()\n        }\n    }\n\n    private cloneEquipAttrs(attrs: any[]) {\n        const ret = []\n        for (let i = 0, l = attrs.length; i < l; i++) {\n            ret.push({ attr: attrs[i].attr.slice() })\n        }\n        return ret\n    }\n\n    private updatePlayerPawnEquipInfo(uid: string, equip: any) {\n        const dist = this.getPlayerArmyDist(uid)\n        for (let key in dist) {\n            const area = this.getArea(Number(key))\n            if (!area || area.isBattleing()) { //这里在战斗不更新装备\n                continue\n            }\n            area.armys.forEach(army => {\n                if (army.owner === uid) {\n                    army.updatePawnEquipAttr(equip)\n                }\n            })\n        }\n    }\n\n    // 从队列中删除\n    private cancelPawnLvingQueue(index: number, uid: string) {\n        const queue = this.getPawnLvingQueue(index)\n        if (!queue) {\n            return null\n        }\n        for (let i = queue.length - 1; i >= 0; i--) {\n            const d = queue[i]\n            if (d.uid === uid) {\n                delete this.pawnLvingQueues.pawnUIDMap[d.puid]\n                queue.splice(i, 1)\n                if (queue.length === 0) {\n                    delete this.pawnLvingQueues.map[index]\n                } else if (d.startTime > 0) {\n                    queue[0].startTime = Date.now()\n                }\n                return d\n            }\n        }\n        return null\n    }\n\n    private isPawnLvingQueueFull(index: number) {\n        const queue = this.getPawnLvingQueue(index)\n        return !!queue && queue.length >= 6\n    }\n\n    private isInLvingQueue(index: number, uid: string) {\n        return !!this.getPawnLvingQueue(index)?.some(m => m.puid === uid)\n    }\n\n    private putPawnLvingQueue(index: number, auid: string, puid: string, id: number, lv: number, time: number, cd: number) {\n        let queue = this.pawnLvingQueues.map[index]\n        if (!queue) {\n            this.pawnLvingQueues.map[index] = queue = []\n        }\n        time = Math.floor(time * 1000 * (1 - cd * 0.01))\n        const info = new NovicePawnLevelingObj().init(index, auid, puid, id, lv, time)\n        queue.push(info)\n        this.pawnLvingQueues.pawnUIDMap[info.puid] = info.uid\n        // 设置第一个的开始时间\n        if (queue.length === 1) {\n            queue[0].startTime = Date.now()\n        }\n    }\n\n    // 检测更新士兵训练队列\n    private checkUpdatePawnLvingQueue(now: number) {\n        for (let key in this.pawnLvingQueues.map) {\n            const queue = this.pawnLvingQueues.map[key]\n            const m = queue[0] //取出第一个开始\n            if (now - m.startTime < m.needTime) {\n                continue\n            }\n            delete this.pawnLvingQueues.pawnUIDMap[m.puid]\n            queue.shift()\n            if (queue.length > 0) {\n                queue[0].startTime = m.startTime + m.needTime\n            } else {\n                delete this.pawnLvingQueues.map[key]\n            }\n            // 通知练级完成\n            this.areaPawnLvingComplete(m.index, m.auid, m.puid, m.lv)\n            // 通知队列信息\n            this.notifyPlayer(NotifyType.PAWN_LEVELING_QUEUE, this.toPawnLvingQueue(Number(key)))\n        }\n    }\n\n    private areaPawnLvingComplete(index: number, auid: string, puid: string, lv: number) {\n        const area = this.getArea(index), army = area?.getArmyByUid(auid)\n        if (!army) {\n            return\n        }\n        const pawn = army.pawns.find(m => m.uid === puid)\n        if (pawn) {\n            pawn.lv = lv\n            pawn.updateAttrJson()\n            this.notifyArea(area.index, NotifyType.UPDATE_ARMY, army.strip())\n        }\n    }\n\n    getPlayerEquip(uid: string) {\n        return this.equips.find(m => m.uid === uid)\n    }\n\n    private changePawnLvingInfo(pawn: PawnObj) {\n        const uid = this.pawnLvingQueues.pawnUIDMap[pawn.uid]\n        const info = this.getPawnLvingQueueInfo(pawn.aIndex, uid)\n        if (info) {\n            info.auid = pawn.armyUid\n            info.puid = pawn.uid\n            this.notifyPlayer(NotifyType.PAWN_LEVELING_QUEUE, this.toPawnLvingQueue(info.index))\n        }\n    }\n\n    private getPawnLvingQueueInfo(index: number, uid: string) {\n        if (!uid) {\n            return null\n        }\n        return this.getPawnLvingQueue(index)?.find(m => m.uid === uid)\n    }\n\n    private getPawnLvingQueue(index: number) {\n        return this.pawnLvingQueues.map?.[index]\n    }\n\n    private toPawnLvingQueue(index: number) {\n        return this.getPawnLvingQueue(index)?.map(m => m.strip()) || []\n    }\n\n    private putDrillPawnQueue(index: number, buid: string, armyUid: string, id: number, lv: number, time: number, cd: number) {\n        let m = this.drillPawnQueues[index]\n        if (!m) {\n            this.drillPawnQueues[index] = m = {}\n        }\n        let queue = m[buid]\n        if (!queue) {\n            m[buid] = queue = []\n        }\n        cd += (this.getAreaBuildEffect(index, buid)?.[CEffect.XL_CD]?.value || 0)\n        time = Math.floor(time * (1 - cd * 0.01))\n        queue.push(new NoviceDrillPawnObj().init(index, buid, armyUid, id, lv, time))\n        // 设置第一个的开始时间\n        if (queue.length === 1) {\n            queue[0].startTime = Date.now()\n        }\n    }\n\n    // 从队列中删除\n    private cancelDrillPawnQueue(index: number, buid: string, uid: string) {\n        const queue = this.getDrillPawnQueue(index, buid)\n        if (!queue) {\n            return null\n        }\n        for (let i = queue.length - 1; i >= 0; i--) {\n            const m = queue[i]\n            if (m.uid === uid) {\n                queue.splice(i, 1)\n                if (queue.length === 0) {\n                    delete this.drillPawnQueues[index]\n                } else if (m.startTime > 0) {\n                    queue[0].startTime = Date.now()\n                }\n                return m\n            }\n        }\n        return null\n    }\n\n    // 检测更新士兵训练队列\n    private checkUpdateDrillPawnQueue(now: number) {\n        for (let key in this.drillPawnQueues) {\n            const index = Number(key), obj = this.drillPawnQueues[key]\n            let update = false\n            for (let k in obj) {\n                const queue = obj[k]\n                const m = queue[0] //取出第一个开始\n                if (!m || now - m.startTime < m.needTime) {\n                    continue\n                }\n                update = true\n                queue.shift()\n                if (queue.length > 0) {\n                    queue[0].startTime = m.startTime + m.needTime\n                } else {\n                    delete obj[k]\n                }\n                // 通知训练完成\n                this.areaPawnDrillComplete(m.index, m.aUid, m.id, m.lv)\n                //招募完成\n                this.notifyPlayerArmyDistInfo()\n            }\n            if (!update) {\n                continue\n            }\n            // 通知队列信息\n            const area = this.areas[index]\n            if (area) {\n                this.notifyPlayer(NotifyType.PAWN_DRILL_QUEUE, this.toDrillPawnQueue(index))\n            }\n            // 如果没有就删除\n            if (ut.isEmptyObject(this.drillPawnQueues[index])) {\n                delete this.drillPawnQueues[index]\n            }\n        }\n    }\n\n    private areaPawnDrillComplete(index: number, auid: string, id: number, lv: number, uid?: string) {\n        const area = this.areas[index], army = area?.getArmyByUid(auid)\n        if (!army) {\n            return\n        } else if (ut.chance(this.getPolicyEffect(CEffect.XL_2LV))) {\n            lv = 2 //有一定几率直接2级\n        }\n        // 删除训练中的\n        army.drillPawns.remove(id)\n        // 创建士兵\n        const pawn = new PawnObj().init(id)\n        pawn.uid = uid || ut.UID()\n        pawn.owner = army.owner\n        pawn.armyUid = army.uid\n        pawn.enterDir = army.enterDir\n        pawn.aIndex = army.index\n        pawn.setPoint(area.getDrillPawnPoint())\n        // 添加装备\n        const { equipInfo, skinId } = this.getConfigPawnInfo(pawn.id)\n        if (equipInfo) {\n            pawn.changeEquip(equipInfo.strip(), false)\n        }\n        pawn.skinId = skinId\n        // 添加士兵\n        army.addPawn(pawn)\n        // 主动刷新玩家粮耗\n        this.updateOpSec(true)\n        // 招募历史数量\n        let hisCount = this.drillHistory[id] || 0\n        this.drillHistory[id] = hisCount + 1\n        // 战斗中的话 就不通知 用帧同步通知\n        if (!area.isBattleing()) {\n            this.notifyArea(area.index, NotifyType.UPDATE_ARMY, army.strip())\n        }\n        this.emit(EventType.GUIDE_PAWN_DRILLED)\n\n\n        //招募远程士兵任务\n        if (pawn.type == PawnType.ARCHER) {\n            this.addGuideTaskProgress(TCType.RECRUIT_PAWN_TYPE)\n        }\n\n        // 上报\n        taHelper.trackNovice('ta_recruitPawn_rookie', { role_id: id, uid: gameHpr.getUid() })\n    }\n\n    private getConfigPawnInfo(pawnId: number) {\n        let equipUid = '', skinId = 0, equipInfo: EquipInfo = null\n        const info = this.configPawnMap[pawnId]\n        if (info) {\n            equipUid = info.equipUid\n            skinId = info.skinId\n            equipInfo = this.equips.find(m => m.uid === equipUid)\n        }\n        return { skinId, equipInfo }\n    }\n\n    private checkCerealConsume(count: number) {\n        return this.cereal.opHour >= count\n    }\n\n    private getPlayerArmyCount() {\n        const dist = this.armysMap[this.puid] || {}\n        return Object.keys(dist).length\n    }\n\n    private getArmyMaxCount() {\n        return (this.effects[CEffect.ARMY_COUNT] || 0) + this.getPolicyEffect(CEffect.ARMY_COUNT)\n    }\n\n    private isDrillPawnQueueFull(index: number, uid: string) {\n        const queue = this.getDrillPawnQueue(index, uid)\n        return !!queue && queue.length >= 6\n    }\n\n    private getDrillPawnQueue(index: number, uid: string) {\n        return this.drillPawnQueues[index]?.[uid]\n    }\n\n    private getDrillPawnQueueInfo(index: number, buid: string, uid: string) {\n        return this.getDrillPawnQueue(index, buid)?.find(m => m.uid === uid)\n    }\n\n    private toDrillPawnQueue(index: number) {\n        const ret = {}\n        const obj = this.drillPawnQueues[index]\n        if (!obj) {\n            return ret\n        }\n        for (let key in obj) {\n            ret[key] = { list: obj[key].map(m => m.strip()) }\n        }\n        return ret\n    }\n\n    private updatePolicyEffect(effectMap: any) {\n        let RES_OUTPUT = false, GW_CAP = false\n        for (let key in effectMap) {\n            const type = Number(key)\n            if (type == CEffect.RES_OUTPUT ||\n                type == CEffect.RARE_RES_OUTPUT ||\n                type == CEffect.MORE_RARE_RES ||\n                type == CEffect.LV_UP_QUEUE) { //增加资源产量\n                RES_OUTPUT = true\n            } else if (type == CEffect.GW_CAP) { //粮食和仓库容量\n                GW_CAP = true\n            }\n        }\n        if (RES_OUTPUT) {\n            this.updateOpSec(true)\n        }\n        if (GW_CAP) {\n            this.notifyPlayer(NotifyType.OUTPUT, {\n                granaryCap: this.getGranaryCap(),\n                warehouseCap: this.getWarehouseCap(),\n            })\n        }\n    }\n\n    // 是否解锁某个政策\n    private isUnlockPolicy(id: number) {\n        // return this.ceriSlots.some(m => m.getActValue() === id)\n    }\n\n    // 是否解锁某个士兵\n    private isUnlockPawn(id: number) {\n        for (let key in this.pawnSlots) {\n            if (this.pawnSlots[key].id === id) {\n                return true\n            }\n        }\n        for (let key in this.heroSlots) {\n            if (this.heroSlots[key].hero?.json.avatar_pawn === id) {\n                return true\n            }\n        }\n        return false\n    }\n\n    // 是否解锁某个装备\n    private isUnlockEquip(id: number) {\n        // return this.ceriSlots.some(m => m.getActValue() === id)\n    }\n\n    private getPolicyEffectMapBool() {\n        const effectMap = {}\n        for (let key in this.policySlots) {\n            const slot = this.policySlots[key]\n            if (slot.id > 0) {\n                effectMap[slot.type] = true\n            }\n        }\n        return effectMap\n    }\n\n    public checkPawnLving(uid: string) {\n        return !!this.pawnLvingQueues.pawnUIDMap?.[uid]\n    }\n\n    // 添加到建造队列\n    private putBTQueue(index: number, uid: string, id: number, lv: number, time: number) {\n        this.btQueues.push(new NoviceBTObj().init(index, uid, id, lv, time))\n        // 设置第一个的开始时间\n        if (this.btQueues.length === 1) {\n            this.btQueues[0].startTime = Date.now()\n        }\n        // // 上报\n        // taHelper.trackNovice('ta_building_rookie', { build: { build_id: id, build_lv: lv }, uid: gameHpr.getUid() })\n    }\n\n    public cancelBT(uid: string) {\n        // 从队列中删除\n        this.btQueues.remove('bUid', uid)\n        // 如果取消的第一个 还有的话就设置开始时间\n        if (this.btQueues.length > 0 && this.btQueues[0].startTime == 0) {\n            this.btQueues[0].startTime = Date.now()\n        }\n    }\n\n    // 获取建造队列\n    getBTQueues() {\n        return this.btQueues.map(m => m.strip())\n    }\n\n    private completeAllBt() {\n        this.btQueues.forEach(m => this.updateBTComplete(m))\n        this.btQueues = [] //置空\n    }\n\n    private toOutputInfo() {\n        return {\n            granaryCap: this.getGranaryCap(),\n            warehouseCap: this.getWarehouseCap(),\n            cereal: this.cereal.strip(),\n            timber: this.timber.strip(),\n            stone: this.stone.strip(),\n        }\n    }\n\n    // 获取修建队列数量\n    private getBTQueueMaxCount() {\n        return this.getPolicyEffect(CEffect.BT_QUEUE) + DEFAULT_BT_QUEUE_COUNT\n    }\n\n    private toItemByTypeObjs(tos: CTypeObj[]) {\n        const items: any = {}\n        tos.forEach(m => {\n            if (m.type === CType.CEREAL) {\n                items.cereal = this.cereal.strip()\n            } else if (m.type === CType.TIMBER) {\n                items.timber = this.timber.strip()\n            } else if (m.type === CType.STONE) {\n                items.stone = this.stone.strip()\n            } else if (m.type === CType.EXP_BOOK) {\n                items.expBook = this.expBook\n            } else if (m.type === CType.IRON) {\n                items.iron = this.iron\n            } else if (m.type === CType.UP_SCROLL) {\n                items.upScroll = this.upScroll\n            } else if (m.type === CType.FIXATOR) {\n                items.fixator = this.fixator\n            }\n        })\n        return items\n    }\n\n    // 是否满资源\n    public isFullRes() {\n        const granaryCap = this.getGranaryCap(), warehouseCap = this.getWarehouseCap()\n        return this.cereal.value >= granaryCap && this.timber.value >= warehouseCap && this.stone.value >= warehouseCap\n    }\n\n    // 扣除资源\n    public changeCostByTypeObjs(tos: CTypeObj[], change: number) {\n        tos.forEach(m => this.changeCostByTypeObjOne(m, change))\n    }\n    public changeCostByTypeObjOne(m: CTypeObj, change: number) {\n        let count = m.count * change, add = 0, val = 0\n        if (m.type === CType.CEREAL) {\n            add = this.cereal.change(count, this.getGranaryCap())\n            val = this.cereal.value\n        } else if (m.type === CType.TIMBER) {\n            add = this.timber.change(count, this.getWarehouseCap())\n            val = this.timber.value\n        } else if (m.type === CType.STONE) {\n            add = this.stone.change(count, this.getWarehouseCap())\n            val = this.stone.value\n        } else if (m.type === CType.EXP_BOOK) {\n            add = this.changeExpBook(count)\n            val = this.expBook\n        } else if (m.type === CType.IRON) {\n            add = this.changeIron(count)\n            val = this.iron\n        } else if (m.type === CType.UP_SCROLL) {\n            add = this.changeUpScroll(count)\n            val = this.upScroll\n        } else if (m.type === CType.FIXATOR) {\n            add = this.changeFixator(count)\n            val = this.fixator\n        } else if (m.type === CType.BUILD_LV) {\n            // this.SetBuildLv(m.id, m.count)\n            val = m.count\n        } else if (m.type === CType.GOLD) {\n            this.setGold(this.getGold() + count)\n            val = m.count\n        } else if (m.type === CType.WAR_TOKEN) {\n            this.setWarToken(this.getWarToken() + count)\n            val = m.count\n        }\n        return { add, val }\n    }\n\n    // 检测资源是否满足\n    private checkCostByTypeObjs(tos: CTypeObj[]) {\n        for (let i = 0, l = tos.length; i < l; i++) {\n            if (!this.checkCostByTypeObjOne(tos[i])) {\n                return false\n            }\n        }\n        return true\n    }\n    private checkCostByTypeObjOne(m: CTypeObj) {\n        if (m.type == CType.CEREAL) {\n            return this.cereal.value >= m.count\n        } else if (m.type == CType.TIMBER) {\n            return this.timber.value >= m.count\n        } else if (m.type == CType.STONE) {\n            return this.stone.value >= m.count\n        } else if (m.type == CType.EXP_BOOK) { //经验书\n            return this.expBook >= m.count\n        } else if (m.type == CType.CEREAL_C) { //粮耗\n            return this.cereal.opHour >= m.count\n        } else if (m.type == CType.IRON) { //铁\n            return this.iron >= m.count\n        } else if (m.type == CType.UP_SCROLL) { //卷轴\n            return this.upScroll >= m.count\n        } else if (m.type == CType.FIXATOR) { //固定器\n            return this.fixator >= m.count\n        }\n        return false\n    }\n    // 检测通用条件 需要区域\n    private checkCTypesNeedArea(cts: CTypeObj[], index: number) {\n        for (let i = 0, l = cts.length; i < l; i++) {\n            if (!this.checkCTypeOneNeedArea(cts[i], index)) {\n                return false\n            }\n        }\n        return true\n    }\n    private checkCTypes(cts: CTypeObj[]) {\n        return this.checkCTypesNeedArea(cts, NOVICE_MAINCITY_INDEX)\n    }\n\n    // 检测单个\n    private checkCTypeOneNeedArea(ct: CTypeObj, index: number) {\n        if (ct.type == CType.CEREAL ||\n            ct.type == CType.TIMBER ||\n            ct.type == CType.STONE ||\n            ct.type == CType.GOLD ||\n            ct.type == CType.EXP_BOOK ||\n            ct.type == CType.IRON ||\n            ct.type == CType.UP_SCROLL ||\n            ct.type == CType.FIXATOR) {\n            return this.checkCostByTypeObjOne(ct)\n        } else if (ct.type == CType.BUILD_LV) {\n            return !!this.areas[index]?.getBuildsById(ct.id).some(m => m.lv >= ct.count)\n        } else if (ct.type == CType.CELL_COUNT) { //领地数\n            let count = Object.keys(this.ownCells).length\n            if (ct.id === 1) {\n                count = Math.max(0, count - 4)\n            }\n            return ct.count <= count\n        }\n        return false\n    }\n    private checkCTypeOne(ct: CTypeObj) {\n        return this.checkCTypeOneNeedArea(ct, NOVICE_MAINCITY_INDEX)\n    }\n\n    public checkAndDeductCostByTypeObjs(tos: CTypeObj[]) {\n        let b = this.checkCostByTypeObjs(tos)\n        if (b) {\n            this.changeCostByTypeObjs(tos, -1)\n        }\n        return b\n    }\n\n    private changeExpBook(val: number) {\n        let count = this.expBook + val\n        if (count < 0) {\n            val = -this.expBook\n        }\n        this.expBook += val\n        return val\n    }\n    private changeIron(val: number) {\n        let count = this.iron + val\n        if (count < 0) {\n            val = -this.iron\n        }\n        this.iron += val\n        return val\n    }\n    private changeUpScroll(val: number) {\n        let count = this.upScroll + val\n        if (count < 0) {\n            val = -this.upScroll\n        }\n        this.upScroll += val\n        return val\n    }\n    private changeFixator(val: number) {\n        let count = this.fixator + val\n        if (count < 0) {\n            val = -this.fixator\n        }\n        this.fixator += val\n        return val\n    }\n\n    private changePlayerGold(val: number) {\n        if (this.gold + val < 0) {\n            return -1\n        }\n        this.gold += val\n        return this.gold\n    }\n\n    private randomTreasureRewards(treasure: TreasureInfo, mul: number) {\n        const json = assetsMgr.getJsonData('treasure', treasure.id)\n        if (!json) {\n            return treasure\n        }\n        const arr = ut.stringToNumbers(json.count, ',')\n        const count = ut.random(arr[0], arr[1])\n        const items: string[] = json.rewards.split('|')\n        if (items.length === count) {\n            treasure.rewards = items.map(m => this.stringToTypeObjRandCount(m, mul))\n            return treasure\n        }\n        const weights = ut.stringToNumbers(json.weight, ',')\n        let totalWeight = 0, cnt = weights.length\n        weights.forEach(m => totalWeight += m)\n        for (let i = 0; i < count; i++) {\n            let offset = ut.random(0, totalWeight - 1)\n            for (let ii = 0; ii < cnt; ii++) {\n                const val = weights[ii]\n                if (!val) {\n                    continue\n                } else if (offset < val) {\n                    totalWeight -= val\n                    weights[ii] = 0\n                    treasure.rewards.push(this.stringToTypeObjRandCount(items[ii], mul))\n                    break\n                } else {\n                    offset -= val\n                }\n            }\n        }\n        if (treasure.rewards.length === 0) {\n            treasure.rewards = [new CTypeObj().init(CType.CEREAL, 0, 50)]\n        }\n    }\n\n    // 字符串转类型对象 随机个数\n    private stringToTypeObjRandCount(str: string, mul: number) {\n        const arr = ut.stringToNumbers(str, ',')\n        if (arr.length !== 4) {\n            return null\n        }\n        return new CTypeObj().init(arr[0], arr[1], Math.floor(ut.random(arr[2], arr[3]) * mul))\n    }\n\n    private getPawnTreasure(auid: string, puid: string, uid: string) {\n        const dist = this.getPlayerArmyDist(this.puid)\n        for (let key in dist) {\n            const area = this.areas[Number(key)]\n            if (!area) {\n                continue\n            }\n            const army = area.getArmyByUid(auid)\n            if (army) {\n                const pawn = army.pawns.find(m => m.uid === puid)\n                if (pawn) {\n                    return { area, pawn, treasure: pawn.treasures.find(m => m.uid === uid) }\n                }\n            }\n        }\n        return { area: null, pawn: null, treasure: null }\n    }\n\n    public cancelMarch(uid: string) {\n        const march = this.marchs.find(m => m.uid === uid)\n        if (march) {\n            const area = this.areas[march.armyIndex]\n            const army = area?.getArmyByUid(march.armyUid)\n            if (army) {\n                this.armyAutoBackIndexMap[army.uid] = -1\n                this.cancelMarchArmy(march, false)\n            }\n        }\n    }\n\n    private cancelMarchArmy(march: NoviceMarchObj, auto: boolean) {\n        const now = Date.now()\n        march.autoRevoke = false\n        // 将目标变成起始位置\n        march.startIndex = march.targetIndex\n        march.targetIndex = march.armyIndex\n        // 如果是自动遣返\n        if (auto) {\n            // 加下速\n            march.needTime = Math.max(march.needTime / 2, 1000)\n            march.startTime = now\n            this.marchs.push(march)\n        } else {\n            // 已经走的时间\n            const elapsed = now - march.startTime\n            // 开始时间等于 当前时间减去剩余时间\n            march.startTime = now - Math.max(march.needTime - elapsed, 0)\n        }\n        // 通知行军\n        this.notifyWorld(NotifyType.ADD_MARCH, march.strip())\n    }\n\n    // 检测更新建造队列\n    private checkUpdateBTQueue(now: number) {\n        if (this.btQueues.length === 0) {\n            return\n        }\n        // 取出第一个开始\n        const m = this.btQueues[0]\n        if (now - m.startTime < m.needTime) {\n            return\n        }\n        this.btQueues.shift() //删除\n        if (this.btQueues.length > 0) {\n            this.btQueues[0].startTime = m.startTime + m.needTime\n        }\n        // 先通知队列\n        this.notifyPlayer(NotifyType.BT_QUEUE, this.btQueues.map(m => m.strip()))\n        // 刷新完成\n        this.updateBTComplete(m)\n    }\n\n    // 通知建造升级\n    private updateBTComplete(m: NoviceBTObj) {\n        const build = this.areaBuildBTComplete(m.aIndex, m.bUid)\n        if (build) {\n            this.notifyPlayer(NotifyType.BUILD_UP, build.strip()) //通知玩家\n            this.updateBuildUpLvInfo(m.bid, m.bLv)\n            // 上报\n            taHelper.trackNovice('ta_building_rookie', { build: { build_id: build.id, build_lv: build.lv }, uid: gameHpr.getUid() })\n            //通知研究更新\n            let studyType = StudyType.NONE\n            let notifyType = NotifyType.NONE\n            if (build.id === BUILD_MAIN_NID) {\n                studyType = StudyType.POLICY\n                notifyType = NotifyType.UPDATE_POLICY_SLOT\n            } else if (build.id === BUILD_BARRACKS_NID) {\n                studyType = StudyType.PAWN\n                notifyType = NotifyType.UPDATE_PAWN_SLOT\n            } else if (build.id === BUILD_SMITHY_NID) {\n                studyType = StudyType.EQUIP\n                notifyType = NotifyType.UPDATE_EQUIP_SLOT\n            }\n            if (studyType !== StudyType.NONE) {\n                let slots = this.resetNextSelect(studyType, build.lv)\n                this.notifyPlayer(notifyType, slots) //通知玩家\n            }\n        }\n    }\n\n    public updateBuildUpLvInfo(id: number, lv: number) {\n        // 刷新一下效果\n        this.updateBuildEffect()\n        // 如果是主城建筑 刷新需要重置的金币\n        if (id === BUILD_MAIN_NID && POLICY_SLOT_CONF.has(lv)) {\n            this.resetPolicyNeedGold = 0\n            this.notifyPlayer(NotifyType.UPDATE_RP_GOLD, this.resetPolicyNeedGold)\n            // } else if (id === BUILD_CERI_NID) { //如果是研究所 刷新槽位信息\n            //     this.updateCeriSlotInfo(lv, false)\n        }\n    }\n\n    // 刷新建筑效果\n    private updateBuildEffect() {\n        const eos = this.getAreaAllBuildEffect(NOVICE_MAINCITY_INDEX)\n        let merchantCount = 0\n        let granaryCap = this.getGranaryCap(), warehouseCap = this.getWarehouseCap()\n        this.effects[CEffect.FORGE_CD] = 0      //减少打造时间\n        this.effects[CEffect.ARMY_COUNT] = 0    //军队最大数量\n        this.effects[CEffect.GRANARY_CAP] = 0   //粮食容量\n        this.effects[CEffect.WAREHOUSE_CAP] = 0 //仓库容量\n        eos.forEach(m => {\n            if (m.type === CEffect.ALLIANCE_PERS) { //联盟人数\n            } else if (m.type === CEffect.MERCHANT_COUNT) { //商人数量\n                merchantCount += m.value\n            } else {\n                this.effects[m.type] += m.value\n            }\n        })\n        // 容量\n        const newGranaryCap = this.getGranaryCap(), newWarehouseCap = this.getWarehouseCap()\n        if (granaryCap != newGranaryCap || warehouseCap != newWarehouseCap) {\n            this.notifyPlayer(NotifyType.OUTPUT, { granaryCap: newGranaryCap, warehouseCap: newWarehouseCap })\n        }\n        // 刷新商人数量\n        if (merchantCount !== this.merchants.length) {\n            this.updateMerchantCount(merchantCount)\n        }\n    }\n\n    private getAreaAllBuildEffect(index: number) {\n        const area = this.areas[index]\n        const effects: CEffectObj[] = []\n        area.builds.forEach(m => {\n            if (m.lv !== 0 && m.effect) {\n                effects.push(m.effect)\n            }\n        })\n        return effects\n    }\n\n    private getAreaBuildEffect(index: number, buid: string) {\n        const effects: { [key: number]: CEffectObj } = {}\n        const area = this.areas[index]\n        const build = area?.getBuildByUid(buid)\n        if (build) {\n            effects[build.effect.type] = build.effect\n        }\n        return effects\n    }\n\n    private updateMerchantCount(count: number) {\n        while (this.merchants.length < count) {\n            this.merchants.push({ state: 0 })\n        }\n        this.notifyPlayer(NotifyType.UPDATE_MERCHANT, this.merchants)\n    }\n\n    private areaBuildBTComplete(index: number, uid: string) {\n        const area = this.areas[index]\n        if (!area) {\n            return null\n        }\n        const build = area.getBuildByUid(uid)\n        if (!build || build.isMaxLv()) {\n            return null\n        }\n        build.lv += 1\n        build.updateAttrJson()\n        this.areaBuildUpComplete(area, build)\n        return build\n    }\n\n    private areaBuildUpComplete(area: NoviceAreaObj, build: BuildObj) {\n        if (build.id == BUILD_WALL_NID) {\n            this.towerLvMap[build.getBuildPawnId()] = build.lv\n            area.updateMaxHP()\n            this.notifyWorld(NotifyType.PLAYER_TOWER_LV, {\n                uid: this.puid,\n                towerLvMap: this.towerLvMap,\n            }) //通知\n        } else if (build.id == 2012 || build.id == 2013) {\n            this.towerLvMap[build.getBuildPawnId()] = build.lv\n            this.updateAreaMaxHP(build.id === 2013 ? 2102 : 0)\n            this.notifyWorld(NotifyType.PLAYER_TOWER_LV, {\n                uid: this.puid,\n                towerLvMap: this.towerLvMap,\n            }) //通知\n        }\n        this.notifyArea(area.index, NotifyType.BUILD_UP, build.strip())\n    }\n\n    // 刷新战斗中的血量\n    private updateAreaMaxHP(id: number) {\n        for (let key in this.ownCells) {\n            const area = this.ownCells[key]\n            let cityId = Math.abs(area.cityId)\n            if (cityId === CITY_MAIN_NID) {\n                continue\n            } else if (cityId !== CITY_FORT_NID) {\n                cityId = 0 //只要不是要塞其他全都是哨站\n            }\n            if (cityId === id) {\n                area.updateMaxHP()\n            }\n        }\n    }\n\n    private updateCeriSlotInfo(blv: number, init: boolean) {\n        // let ok = false\n        // for (let i = 0, l = CERI_SLOT_CONF.length; i < l; i++) {\n        //     const { lv, type } = CERI_SLOT_CONF[i]\n        //     const preLv = lv - 1\n        //     const slot = this.ceriSlots.find(m => m.lv === lv)\n        //     if (slot) {\n        //         if (lv > blv) {\n        //             slot.selectIds = []\n        //         } else {\n        //             const pre = this.getCeriSlotByLv(preLv)\n        //             if ((pre && pre.id > 0) || lv == 1) {\n        //                 if (slot.id > 0) {\n        //                     slot.selectIds = []\n        //                 } else {\n        //                     slot.selectIds = this.ceriRandomSelect(slot.type, slot.lv)\n        //                 }\n        //             } else {\n        //                 slot.selectIds = []\n        //             }\n        //         }\n        //         ok = true\n        //     } else if (lv <= blv) {\n        //         const slot = new NoviceCeriSlotObj().init(lv, type)\n        //         const pre = this.getCeriSlotByLv(preLv)\n        //         if ((pre && pre.id > 0) || lv == 1) {\n        //             slot.selectIds = this.ceriRandomSelect(slot.type, slot.lv)\n        //         }\n        //         this.ceriSlots.push(slot)\n        //         ok = true\n        //     }\n        // }\n        // if (ok && !init) {\n        //     this.notifyPlayer(NotifyType.UPDATE_CERI_SLOT, this.ceriSlots.map(m => m.strip()))\n        // }\n    }\n\n    // 获取槽位信息\n    private getCeriSlotByLv(lv: number) {\n        // if (lv < 1) {\n        //     return null\n        // }\n        // return this.ceriSlots.find(m => m.lv === lv)\n    }\n\n    private ceriRandomSelect(type: number, lv: number) {\n        // if (type == 4) {\n        //     return this.ceriRandomExclusiveSelect(lv)\n        // }\n        // const idMap = {}\n        // const preIdMap = {}\n        // this.ceriSlots.forEach(m => { //当前研究槽位信息\n        //     if (m.type !== type) {\n        //         return\n        //     }\n        //     idMap[m.id] = true\n        //     if (m.lv === lv) {\n        //         m.selectIds.forEach(id => preIdMap[id] = true)\n        //     } else if (m.id === 0) {\n        //         m.selectIds.forEach(id => idMap[id] = true)\n        //     }\n        // })\n        // // 删除已经解锁 或者已经随机出来的\n        // let datas = assetsMgr.getJson('ceri').get('type', type)\n        // datas.delete(m => idMap[m.id] || m.need_lv > lv)\n        // return this.randomCeriIds(preIdMap, datas)\n    }\n\n    private ceriRandomExclusiveSelect(lv: number) {\n        // const idMap = {}\n        // const preIdMap = {}\n        // // 初始士兵\n        // const pawns = assetsMgr.getJson('pawnBase').datas.filter(m => m.spawn_build_id > 0 && !m.need_unlock)\n        // pawns.forEach(m => idMap[m.id] = true)\n        // // 获取已经有的士兵列表\n        // this.ceriSlots.forEach(m => { //当前研究槽位信息\n        //     if (m.lv === lv) {\n        //         m.selectIds.forEach(id => preIdMap[id] = true)\n        //     } else if (m.type === 2 && m.value > 0) {\n        //         idMap[m.value] = true\n        //     } else if (m.type === 4 && m.value > 0) { //已经获取的专属装备 要忽略\n        //         const json = assetsMgr.getJsonData('equipBase', m.value)\n        //         if (json) {\n        //             idMap[json.exclusive_pawn] = false\n        //         }\n        //     }\n        // })\n        // // 删除已经解锁 或者已经随机出来的\n        // const datas = assetsMgr.getJson('ceri').get('type', 4)\n        // datas.delete(m => {\n        //     const json = assetsMgr.getJsonData('equipBase', m.value)\n        //     return !json || !idMap[json.exclusive_pawn]\n        // })\n        // return this.randomCeriIds(preIdMap, datas)\n    }\n\n    private randomCeriIds(preIdMap: any, datas: any[]) {\n        if (datas.length <= 3) {\n            return datas.map(m => m.id)\n        }\n        let ids: number[] = [], cnt = 0\n        while (cnt < 50) {\n            cnt += 1\n            const arr = datas.map(m => m)\n            let ok = false\n            for (let i = 0; i < 3 && arr.length > 0; i++) {\n                const index = ut.randomIndexByWeight(arr, 'weight')\n                const id = arr[index].id\n                ids.push(id)\n                arr.splice(index, 1)\n                if (!preIdMap[id]) {\n                    ok = true\n                }\n            }\n            if (ok) {\n                break\n            }\n            ids = []\n        }\n        return ids\n    }\n\n    // 检测是否有相同的军队名字\n    public checkArmyNameEqual(name: string) {\n        const uid = this.puid\n        const dist = this.getPlayerArmyDist(uid)\n        for (let key in dist) {\n            const area = this.getArea(Number(key))\n            if (!area) {\n                continue\n            }\n            for (let i = 0, l = area.armys.length; i < l; i++) {\n                const army = area.armys[i]\n                if (army.owner === uid && army.name === name) {\n                    return true\n                }\n            }\n        }\n        return false\n    }\n\n    // 修复主城扣除资源\n    public restoreMainCity() {\n        const cost = [\n            new CTypeObj().init(CType.CEREAL, 0, 50),\n            new CTypeObj().init(CType.TIMBER, 0, 250),\n            new CTypeObj().init(CType.STONE, 0, 250),\n        ]\n        this.checkAndDeductCostByTypeObjs(cost)\n        this.notifyPlayer(NotifyType.OUTPUT, {\n            cereal: this.cereal.strip(),\n            timber: this.timber.strip(),\n            stone: this.stone.strip(),\n        })\n    }\n\n    // 改变体力\n    private changeStamina(val: number) {\n        if (this.stamina + val < 0) {\n            return -1\n        }\n        this.stamina += val\n        this.notifyPlayer(NotifyType.UPDATE_ITEMS, { stamina: this.stamina })\n        return this.stamina\n    }\n\n    // 设置引导任务进度 只针对动态发生的非统计形式的任务\n    public setGuideTaskProgress(cusType: TCusCType, val: number = 1) {\n        const playerGuideTask = gameHpr.player.getGuideTasks()\n        const task = this.guideTasks.find(m => {\n            const task = playerGuideTask.find(n => n.id === m.id)\n            return task && task.cond.type === TCType.CUSTOM && task.cond.id === cusType\n        })\n        if (task) {\n            task.progress = val\n            this.updateGuideTasksProgress()\n        }\n        else if (cusType !== TCusCType.OPEN_TREASURE) {//开宝箱不需要提前记录\n            this.tempGuideTaskProgress[cusType] = val\n        }\n    }\n\n    public addGuideTaskProgress(type: TCType, val: number = 1) {\n        const playerGuideTask = gameHpr.player.getGuideTasks()\n        const task = this.guideTasks.find(m => {\n            const task = playerGuideTask.find(n => n.id === m.id)\n            return task && task.cond.type === type\n        })\n        if (task) {\n            task.progress += val\n            this.updateGuideTasksProgress()\n        }\n    }\n\n    public getDistanceToMainCity(cell: MapCellObj) {\n        const t = mapHelper.indexToPoint(cell.index).clone()\n        const s = mapHelper.getMinDis(t, NOVICE_MAINCITY_POINTS)\n        return mapHelper.getPointToPointDis(s, t)\n    }\n\n    public getEnemyUID() {\n        return this.enemyUid\n    }\n\n    public addManulBattle(areaIndex: number) {\n        if (!this.manulBattles[areaIndex]) {\n            this.manulBattles[areaIndex] = { index: areaIndex, attacker: '' }\n        }\n    }\n\n    // 获取最后攻占时的士兵资源字符串\n    public getLastOccupyPawnResToStr(index: number) {\n        const resObj = this.lastOccupyPawnRes[index]\n        delete this.lastOccupyPawnRes[index]\n        let str = ''\n        for (let key in resObj) {\n            if (str) {\n                str += '|'\n            }\n            str += key + ',0,' + resObj[key]\n        }\n        return str\n    }\n\n    public sendResources(rewardStr: string = '1,0,600|2,0,600|3,0,600') {\n        const ctypes = gameHpr.stringToCTypes(rewardStr)\n        this.changeCostByTypeObjs(ctypes, 1)\n        gameHpr.addGainMassage(ctypes)\n        gameHpr.player.updateRewardItemsByFlags(this.toItemByTypeObjs(ctypes))\n    }\n\n    // 士兵能否训练\n    public canDrillPawn(id: string, res?: number[]) {\n        const json = assetsMgr.getJsonData('pawnBase', id)\n        const m = gameHpr.stringToCTypes(json.drill_cost)\n        if (res) {\n            for (let i = 0; i < m.length; i++) {\n                const need = m[i]\n                if (need.count > res[need.type - 1]) {\n                    return false\n                }\n            }\n            return true\n        } else {\n            return this.checkCostByTypeObjs(m)\n        }\n    }\n\n    public isUserPawnDrilling() {\n        const drillPawns = this.drillPawnQueues[NOVICE_MAINCITY_INDEX]\n        return !!drillPawns\n    }\n\n    // 是否正在修建\n    public isBuilding(id: number) {\n        return this.btQueues.some(m => m.bid === id)\n    }\n\n    public debugCostRes(ctypeStr: string) {\n        const cts = gameHpr.stringToCTypes(ctypeStr)\n        this.changeCostByTypeObjs(cts, -1)\n        gameHpr.player.updateRewardItemsByFlags(this.toItemByTypeObjs(cts))\n    }\n\n    private checkTreasure() {\n        this.sendPlayerHasTreasure(this.puid)\n    }\n\n    private async checkTask() {\n        //主线任务\n        if (gameHpr.guide.getIsFinishGuideID(1) && !this.triggerTasks[100001]) {\n            gameHpr.guide.checkFuncGenGuideTask({ id: [100001, 100101] })\n        }\n        //医馆任务\n        if (gameHpr.guide.getIsFinishGuideID(13) && !this.triggerTasks[100301]) {\n            gameHpr.guide.checkFuncGenGuideTask({ id: 100301 })\n        }\n        //攻打任务\n        if (gameHpr.guide.getIsFinishGuideID(15) && !this.triggerTasks[100201]) {\n            gameHpr.guide.checkFuncGenGuideTask({ id: 100201 })\n        }\n    }\n\n    //检查化身的英雄是否阵亡\n    private checkHero() {\n        for (let i = 0; i < this.heroSlots.length; i++) {\n            let heroSlot = this.heroSlots[i]\n            if (heroSlot.avatarArmyUID) {\n                let armys = gameHpr.player.getArmyDistMap()\n                for (let key in armys) {\n                    let armyBaseList = armys[key]\n                    for (let j = 0; j < armyBaseList.length; j++) {\n                        let armyBase = armyBaseList[j]\n                        for (let k = 0; k < armyBase.pawns.length; k++) {\n                            let pawn = armyBase.pawns[k] as any\n                            if (pawn.armyUid === heroSlot.avatarArmyUID) {\n                                return\n                            }\n                        }\n                    }\n                }\n                // 英雄死亡\n                heroSlot.avatarArmyUID = ''\n                gameHpr.player.updateHeroSlotOne(heroSlot)\n            }\n        }\n    }\n\n    // 获取玩家士兵\n    public getPawns() {\n        const uid = this.puid\n        const dist = this.getPlayerArmyDist(uid)\n        const arr = []\n        for (let key in dist) {\n            const index = Number(key)\n            const area = this.areas[index]?.proxyAO ?? this.areas[index]\n            area?.armys.forEach(m => {\n                if (m.owner === uid) {\n                    arr.pushArr(m.pawns)\n                }\n            })\n        }\n        return arr\n    }\n\n    // 英雄自选礼包\n    genHeroReward(heroId: number, count: number) {\n        let isFind = false\n        for (let i = 0; i < this.portrayals.length; i++) {\n            if (this.portrayals[i].id === heroId) {\n                this.portrayals[i].debris += count\n                isFind = true\n                break\n            }\n        }\n        if (!isFind) {\n            let info = new PortrayalInfo().init(heroId)\n            info.debris += count\n            this.portrayals.push(info)\n        }\n        gameHpr.user.setPortrayals(this.portrayals)\n        //自动合成完整英雄\n        let { err, data } = this.HD_PortrayalComp({ id: heroId })\n        if (!err) {\n            let portrayal = gameHpr.user.getPortrayals()[0]\n            portrayal.updateInfo(data.info)\n            gameHpr.user.checkHasCanCompPortrayal()\n            gameHpr.player.updatePawnHeroAttr(portrayal.id, portrayal.attrs)\n            eventCenter.emit(EventType.UPDATE_PORTRAYAL_INFO)\n        }\n    }\n\n    //合成英雄\n    HD_PortrayalComp(data: any) {\n        let info: PortrayalInfo = null\n        for (let i = 0; i < this.portrayals.length; i++) {\n            if (this.portrayals[i].id === data.id) {\n                info = this.portrayals[i]\n                break\n            }\n        }\n        const needDebris = 3\n        if (info && info.debris >= needDebris) {\n            // info.lastAttrs = info.attrs\n            info.attrs = []\n            let portrayalBase = assetsMgr.getJsonData('portrayalBase', data.id)\n            let hpList = portrayalBase.hp.split(',')\n            let hp = ut.random(Number(hpList[0]), Number(hpList[1]))\n            info.attrs.push({ attr: [0, 1, hp] })\n            let attackList = portrayalBase.attack.split(',')\n            let attack = ut.random(Number(attackList[0]), Number(attackList[1]))\n            info.attrs.push({ attr: [0, 2, attack] })\n\n            let skillBase = assetsMgr.getJsonData('portrayalSkill', portrayalBase.skill)\n            let skillList = skillBase.value.split(',')\n            let skill = ut.random(Number(skillList[0]), Number(skillList[1]))\n            info.attrs.push({ attr: [1, portrayalBase.skill, skill] })\n\n            let strategyList = portrayalBase.strategy.split('|')\n            let strategyRandList = this.randListCount(strategyList, 2)\n            for (let i = 0; i < strategyRandList.length; i++) {\n                info.attrs.push({ attr: [2, Number(strategyRandList[i])] })\n            }\n            info.recompCount++\n            info.debris -= needDebris\n            return { data: { info } }\n        }\n        return { err: null }\n    }\n\n    //供奉英雄\n    HD_WorshipHero(data: any) {\n        const { index, id } = data\n        let curPortrayals = null\n        for (let i = 0; i < this.portrayals.length; i++) {\n            if (this.portrayals[i].id === id) {\n                curPortrayals = this.portrayals[i]\n                break\n            }\n        }\n        this.heroSlots[index].hero = curPortrayals\n        this.emit('WORSHIP_HERO')\n        return { err: null, data: { slot: this.heroSlots[index], pawnSlots: this.pawnSlots } }\n    }\n\n    //英雄化身\n    HD_ChangePawnPortrayal(data: any) {\n        const { index, armyUid, uid, portrayalId } = data\n        let heroSlot = null\n        for (let i = 0; i < this.heroSlots.length; i++) {\n            let slot = this.heroSlots[i]\n            if (slot.hero.id === portrayalId) {\n                heroSlot = slot\n                heroSlot.deadTime = 0\n                heroSlot.avatarArmyUID = armyUid\n            }\n        }\n        let armys = this.areas[index].armys\n        for (let i = 0; i < armys.length; i++) {\n            let pawns = armys[i].pawns\n            for (let j = 0; j < pawns.length; j++) {\n                let pawn = pawns[j] as any\n                if (pawn.armyUid === armyUid && pawn.uid === uid) {\n                    pawn.skinId = 0\n                    pawn.portrayal = heroSlot.hero\n                    pawn.updateAttr()\n                    pawn.curHp = pawn.maxHp\n                    break\n                }\n            }\n        }\n        this.notifyArea(index, NotifyType.CHANGE_PAWN_PORTRAYAL, { armyUid, uid, portrayal: heroSlot.hero, skinId: 0 })\n        const pawnLocal = gameHpr.areaCenter.getArea(index).getPawnByPrecise(data.armyUid, data.uid)\n        pawnLocal.updateAttr()\n        pawnLocal.curHp = pawnLocal.maxHp\n        return { err: null, data: { slot: heroSlot } }\n    }\n\n    //战斗记录\n    HD_GetBattleRecordsList() {\n        let list = []\n        for (let i = 0; i < this.battleRecordList.length; i++) {\n            let record = this.battleRecordList[i]\n            if (record.endTime > 0) {\n                list.push(record)\n            }\n        }\n        return { err: null, data: { list } }\n    }\n\n    //获取战斗记录\n    HD_GetBattleRecord(data: any) {\n        for (let i = 0; i < this.battleRecordList.length; i++) {\n            if (this.battleRecordList[i].uid === data.uid) {\n                return { err: null, data: { record: this.battleRecordList[i].strip() } }\n            }\n        }\n        return { err: 'login.net_error' }\n    }\n\n    //战斗详情\n    HD_GetArmyRecordsByUids(data: any) {\n        for (let i = 0; i < this.battleRecordList.length; i++) {\n            if (this.battleRecordList[i].uid === data.battleUid) {\n                return { err: null, data: { list: this.battleRecordList[i].getBattleStatistics() } }\n            }\n        }\n        return { err: 'login.net_error' }\n    }\n\n    //战败士兵回馆死亡次数\n    HD_GetPawnDeadLvMap() {\n        return { err: null, data: { pawnDeadLvMap: this.pawnDeadTimesMap } }\n    }\n\n    //战败士兵回医疗馆\n    addPawnToHealing(fighter: IFighter) {\n        let times = this.getPawnDeadTimes(fighter.getLv())\n        let rate = NOVICE_INJURY_MAX_RATE - times * NOVICE_INJURY_LV_REDUCE_RATE[fighter.getLv()]\n        rate = Math.max(rate, NOVICE_GO_HOSPITAL_CHANCE[fighter.getLv()])\n        if (ut.random(0, NOVICE_INJURY_MAX_RATE) > rate) {\n            return\n        }\n        let info = new NoviceInjuryPawnInfo()\n        info.id = fighter.getId()\n        info.uid = fighter.getUid()\n        info.lv = fighter.getLv()\n        info.deadTime = Date.now()\n        info.cureCount = times\n        info.curing = 0\n        info.auid = fighter.getArmyUid()\n        this.injuryPawns.push(info)\n        this.notifyPlayer(NotifyType.PAWN_INJURY_ADD, info.strip())\n        this.addPawnDeadTimes(fighter.getLv())\n    }\n    private getPawnDeadTimes(lv: number) {\n        let times = this.pawnDeadTimesMap[lv] || 0\n        return times\n    }\n    private addPawnDeadTimes(lv: number) {\n        this.pawnDeadTimesMap[lv] = this.getPawnDeadTimes(lv) + 1\n    }\n    getInjuryPawns() {\n        return this.injuryPawns\n    }\n}\n\n// 新手村回档调试用\nclass NoviceDebug {\n\n    private _history: any[] = []\n    private index: number = 0\n\n    constructor() {\n        this._history = JSON.parse(cc.sys.localStorage.getItem('noviceDebug_history') || '[]')\n        this.index = Number(cc.sys.localStorage.getItem('noviceDebug_index') || 0)\n    }\n\n    apply(index: number) {\n        const data = this._history[index]\n        if (!data) return\n        const { guide, server } = data\n\n        for (let key in guide) {\n            if (guide[key] === null) {\n                storageMgr.remove(key)\n                continue\n            }\n            storageMgr.saveString(key, guide[key])\n        }\n        storageMgr.saveJson('novice_data_' + gameHpr.user.getUid(), server)\n        this.index = index\n        cc.sys.localStorage.setItem('noviceDebug_index', this.index)\n        location.reload()\n    }\n\n    // 存一次档\n    save() {\n        const guide = gameHpr.guide.strip()\n        const server = gameHpr.noviceServer.strip()\n        const data = { guide, server }\n        this._history.push(data)\n        cc.sys.localStorage.setItem('noviceDebug_history', JSON.stringify(this._history))\n    }\n\n    // 回到上一个存档\n    back() {\n        let index = this._history.length - 1\n        if (index < 0) index = 0\n        this.apply(index)\n    }\n\n    // 前进到下一个存档\n    forward() {\n        let index = this.index + 1\n        if (index >= this._history.length) index = this._history.length - 1\n        this.apply(index)\n    }\n\n    // 清空存档\n    clear() {\n        this._history = []\n        this.index = 0\n        cc.sys.localStorage.setItem('noviceDebug_history', JSON.stringify(this._history))\n        cc.sys.localStorage.setItem('noviceDebug_index', this.index)\n    }\n\n    go(index: number) {\n        index = Math.max(0, Math.min(index, this._history.length - 1))\n        this.apply(index)\n    }\n}\n\nif (CC_PREVIEW) {\n    window['noviceDebug'] = new NoviceDebug()\n}"]}