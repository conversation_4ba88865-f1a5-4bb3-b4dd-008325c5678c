
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/TaskModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1750aYlGRREJKdjVhHonUq8', 'TaskModel');
// app/script/model/common/TaskModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var TaskObj_1 = require("./TaskObj");
/**
 * 任务模块
 */
var TaskModel = /** @class */ (function (_super) {
    __extends(TaskModel, _super);
    function TaskModel() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.user = null;
        _this.generalTasks = []; //常规任务列表
        _this.achieveTasks = []; //成就任务列表
        return _this;
    }
    TaskModel.prototype.onCreate = function () {
        this.user = this.getModel('user');
    };
    TaskModel.prototype.init = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetTasks', { uid: this.user.getUid() })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.updateGeneralTasks((data === null || data === void 0 ? void 0 : data.generalTasks) || [], false);
                        this.updateAchieveTasks((data === null || data === void 0 ? void 0 : data.achieveTasks) || [], false);
                        ReddotHelper_1.reddotHelper.unregister('general_task');
                        ReddotHelper_1.reddotHelper.register('general_task', this.checkGeneralTaskState, this, 2);
                        ReddotHelper_1.reddotHelper.unregister('achieve_task');
                        ReddotHelper_1.reddotHelper.register('achieve_task', this.checkAchieveTaskState, this, 2);
                        ReddotHelper_1.reddotHelper.unregister('first_pay');
                        ReddotHelper_1.reddotHelper.register('first_pay', this.checkFirstPaySaleTaskState, this, 2);
                        if (!storageMgr.loadBool('prize_question_1') && GameHelper_1.gameHpr.checkActivityAutoDate(Constant_1.PRIZE_QUESTION_TIME[0], Constant_1.PRIZE_QUESTION_TIME[1])) { // 有奖问卷红点
                            ReddotHelper_1.reddotHelper.set('prize_question', true);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 是否显示任务提示
    TaskModel.prototype.isShowTaskTip = function () {
        var _a;
        return (_a = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_GUIDE_TASK_TIP)) !== null && _a !== void 0 ? _a : true;
    };
    // 获取常规任务列表 --------------------------------------------------------------------------------------------------------------------------------
    TaskModel.prototype.getGeneralTasks = function () {
        return this.generalTasks;
    };
    TaskModel.prototype.updateGeneralTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.generalTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'generalTask');
            task && _this.generalTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_GENERAL_TASK_LIST);
    };
    // 更新任务进度
    TaskModel.prototype.updateGeneralTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (taskInfo) { var _a, _b; return (_b = (_a = _this.generalTasks.find(function (m) { return m.id === taskInfo.id; })) === null || _a === void 0 ? void 0 : _a.cond) === null || _b === void 0 ? void 0 : _b.updateProgress(taskInfo.progress); });
            this.updateGeneralTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_GENERAL_TASK_LIST);
        }
    };
    // 刷新任务状态
    TaskModel.prototype.updateGeneralTaskState = function () {
        var canget = false, firstPay = false;
        this.generalTasks.forEach(function (m) {
            var _a;
            if (m.checkUpdateComplete() === Enums_1.TaskState.CANGET) {
                canget = true;
                if (((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.RECHARGE_COUNT) {
                    firstPay = true;
                }
            }
        });
        this.generalTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        ReddotHelper_1.reddotHelper.set('general_task', canget);
        ReddotHelper_1.reddotHelper.set('first_pay', firstPay);
    };
    // 红点检测
    TaskModel.prototype.checkGeneralTaskState = function (val) {
        return val || this.generalTasks.some(function (m) { return m.checkUpdateComplete() === Enums_1.TaskState.CANGET; });
    };
    // 领取任务奖励
    TaskModel.prototype.claimGeneralTaskReward = function (id, heroId) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_ClaimGeneralTaskReward', { sid: this.user.getSid(), id: id, heroId: heroId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            GameHelper_1.gameHpr.player.updateRewardItemsByFlags(data.rewards);
                            this.updateGeneralTasks(data.tasks || []);
                            if (data.isInviteFriend) {
                                this.user.updateInviteFriends(data.inviteFriends);
                            }
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    // 检测每日签到活动红点
    TaskModel.prototype.checkDailySignInTaskState = function () {
        return this.getGeneralTasks().filter(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.SIGN_DAY_COUNT; }).some(function (m) { return m.isComplete(); });
    };
    // 检测首充特惠活动红点
    TaskModel.prototype.checkFirstPaySaleTaskState = function () {
        return this.getGeneralTasks().filter(function (m) { var _a; return ((_a = m.cond) === null || _a === void 0 ? void 0 : _a.type) === Enums_1.TCType.RECHARGE_COUNT; }).some(function (m) { return m.isComplete(); });
    };
    // 获取成就任务列表 --------------------------------------------------------------------------------------------------------------------------------
    TaskModel.prototype.getAchieveTasks = function () {
        return this.achieveTasks;
    };
    TaskModel.prototype.updateAchieveTasks = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        this.achieveTasks = [];
        tasks.forEach(function (taskInfo) {
            var task = new TaskObj_1.default().init(taskInfo, 'achieveTask');
            task && _this.achieveTasks.push(task);
        });
        isEmit && this.emit(EventType_1.default.UPDATE_ACHIEVE_TASK_LIST);
    };
    // 更新任务进度
    TaskModel.prototype.updateAchieveTasksProgress = function (tasks, isEmit) {
        var _this = this;
        if (isEmit === void 0) { isEmit = true; }
        if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
            tasks.forEach(function (taskInfo) { var _a, _b; return (_b = (_a = _this.achieveTasks.find(function (m) { return m.id === taskInfo.id; })) === null || _a === void 0 ? void 0 : _a.cond) === null || _b === void 0 ? void 0 : _b.updateProgress(taskInfo.progress); });
            this.updateAchieveTaskState();
            isEmit && this.emit(EventType_1.default.UPDATE_ACHIEVE_TASK_LIST);
        }
    };
    // 刷新任务状态
    TaskModel.prototype.updateAchieveTaskState = function () {
        var canget = false;
        this.achieveTasks.forEach(function (m) {
            if (m.checkUpdateComplete() === Enums_1.TaskState.CANGET) {
                canget = true;
            }
        });
        this.achieveTasks.sort(function (a, b) { return a.getSortVal() - b.getSortVal(); });
        ReddotHelper_1.reddotHelper.set('achieve_task', canget);
    };
    // 红点检测
    TaskModel.prototype.checkAchieveTaskState = function (val) {
        return val || this.achieveTasks.some(function (m) { return m.checkUpdateComplete() === Enums_1.TaskState.CANGET; });
    };
    // 领取任务奖励
    TaskModel.prototype.claimAchieveTaskReward = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_ClaimAchieveTaskReward', { sid: this.user.getSid(), id: id }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (!err) {
                            this.user.setTitles(data.titles || []);
                            this.updateAchieveTasks(data.tasks || []);
                        }
                        return [2 /*return*/, err];
                }
            });
        });
    };
    TaskModel = __decorate([
        mc.addmodel('task')
    ], TaskModel);
    return TaskModel;
}(mc.BaseModel));
exports.default = TaskModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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