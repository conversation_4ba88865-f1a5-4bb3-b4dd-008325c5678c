
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/novice/NoviceRiskTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f2a0ef9pCNKRoyMK133+ck1', 'NoviceRiskTipPnlCtrl');
// app/script/view/novice/NoviceRiskTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var LocaleLabel_1 = require("../../../core/component/LocaleLabel");
var ccclass = cc._decorator.ccclass;
var NoviceRiskTipPnlCtrl = /** @class */ (function (_super) {
    __extends(NoviceRiskTipPnlCtrl, _super);
    function NoviceRiskTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.rootNode_ = null; // path://root_n
        _this.noLongerTge_ = null; // path://root_n/no_longer_t
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    NoviceRiskTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoviceRiskTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                cc.log('NoviceRiskTipPnlCtrl onCreate');
                return [2 /*return*/];
            });
        });
    };
    NoviceRiskTipPnlCtrl.prototype.onEnter = function (armys) {
        cc.log('NoviceRiskTipPnlCtrl onEnter');
        var key = 'guideText.risk_tip_003';
        // 满血时
        if (armys.every(function (army) { return army.pawns.every(function (pawn) { return pawn.hp[0] === pawn.hp[1]; }); })) {
            if (armys.length > 1) {
                key = 'guideText.risk_tip_002';
            }
            else {
                key = 'guideText.risk_tip_001';
            }
        }
        this.rootNode_.Child('content', LocaleLabel_1.default).setKey(key);
    };
    NoviceRiskTipPnlCtrl.prototype.onRemove = function () {
        cc.log('NoviceRiskTipPnlCtrl onRemove');
    };
    NoviceRiskTipPnlCtrl.prototype.onClean = function () {
        cc.log('NoviceRiskTipPnlCtrl onClean');
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    NoviceRiskTipPnlCtrl.prototype.onClickClose = function (event, data) {
        cc.log('onClickClose', data);
        this.close();
    };
    // path://root_n/buttons/ok_be
    NoviceRiskTipPnlCtrl.prototype.onClickOk = function (event, data) {
        this.close();
    };
    NoviceRiskTipPnlCtrl = __decorate([
        ccclass
    ], NoviceRiskTipPnlCtrl);
    return NoviceRiskTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoviceRiskTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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