
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BaseAction.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a9f88fWSspNVq3bWOyZtmGd', 'BaseAction');
// app/script/model/behavior/BaseAction.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseNode_1 = require("./BaseNode");
var BTConstant_1 = require("./BTConstant");
// 动作节点
var BaseAction = /** @class */ (function (_super) {
    __extends(BaseAction, _super);
    function BaseAction() {
        var _this = _super.call(this) || this;
        _this.type = BTConstant_1.BTType.ACTION;
        return _this;
    }
    return BaseAction;
}(BaseNode_1.default));
exports.default = BaseAction;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQmFzZUFjdGlvbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1Q0FBa0M7QUFDbEMsMkNBQXNDO0FBRXRDLE9BQU87QUFDUDtJQUF3Qyw4QkFBUTtJQUU1QztRQUFBLFlBQ0ksaUJBQU8sU0FFVjtRQURHLEtBQUksQ0FBQyxJQUFJLEdBQUcsbUJBQU0sQ0FBQyxNQUFNLENBQUE7O0lBQzdCLENBQUM7SUFDTCxpQkFBQztBQUFELENBTkEsQUFNQyxDQU51QyxrQkFBUSxHQU0vQyIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBCYXNlTm9kZSBmcm9tIFwiLi9CYXNlTm9kZVwiO1xyXG5pbXBvcnQgeyBCVFR5cGUgfSBmcm9tIFwiLi9CVENvbnN0YW50XCI7XHJcblxyXG4vLyDliqjkvZzoioLngrlcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgQmFzZUFjdGlvbiBleHRlbmRzIEJhc2VOb2RlIHtcclxuXHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlcigpXHJcbiAgICAgICAgdGhpcy50eXBlID0gQlRUeXBlLkFDVElPTlxyXG4gICAgfVxyXG59Il19