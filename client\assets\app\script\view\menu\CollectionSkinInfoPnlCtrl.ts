import { ecode } from "../../common/constant/ECode";
import { SHOP_PAWN_SKIN_ANIM_CONF } from "../../common/constant/FrameAnimConf";
import { gameHpr } from "../../common/helper/GameHelper";
import { gotoHelper } from "../../common/helper/GotoHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PawnFrameAnimationCmpt from "../cmpt/PawnFrameAnimationCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class CollectionSkinInfoPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private titleLbl_: cc.Label = null // path://root/title/title_l
    private bgNode_: cc.Node = null // path://root/bg_n
    private nameNode_: cc.Node = null // path://root/name_n
    private iconNode_: cc.Node = null // path://root/icon_n
    private timeNode_: cc.Node = null // path://root/time_n
    private skinExchangeNode_: cc.Node = null // path://root/skin_exchange_be_n
    private nextNode_: cc.Node = null // path://root/next_n
    private buttonsNode_: cc.Node = null // path://root/buttons_n
    //@end

    private json: any = null
    private animCmpt: PawnFrameAnimationCmpt = null

    private type: string = ''
    private list: any[] = []
    private itemSkin: any = null
    private curPage: number = 0
    private maxPage: number = 0
    private cb: Function = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: { list: any[], type: string, index?: number, skins?: any[] }, cb: Function) {
        this.cb = cb
        this.updateViewInfo(data)
    }

    public onRemove() {
    }

    public onClean() {
        assetsMgr.releaseTempResByTag(this.key)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/buttons_n/lay/give_be
    onClickGive(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('menu/Personal', 0)
    }

    // path://root/buttons_n/lay/use_be
    onClickUse(event: cc.Event.EventTouch, _data: string) {
        if (gameHpr.isInLobby()) {
            return viewHelper.showAlert('toast.please_game_use')
        } else if (this.type === 'pawn_skin') {
            if (!this.checkPawnUnlock()) {
                return viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [resHelper.getPawnName(this.json.pawn_id)] })
            }
            this.syncInfoToServer()
        } else {
            this.changeCitySkin()
        }
    }

    // path://root/buttons_n/buy_be
    onClickBuy(event: cc.Event.EventTouch, _data: string) {
        this.cb && this.cb(this.list[this.curPage]?.json || this.list[this.curPage])
        this.hide()
    }

    // path://root/skin_exchange_be_n
    onClickSkinExchange(event: cc.Event.EventTouch, _data: string) {
        const id = assetsMgr.getJson('pawnSkin').datas.find(m => m.value.toString().includes(this.json.id + ','))?.id
        viewHelper.showPnl('common/SkinExchange', id)
    }

    // path://root/next_n/next_page_be@0
    onClickNextPage(event: cc.Event.EventTouch, data: string) {
        const add = data === '0' ? -1 : 1
        const curPage = this.curPage || 0, maxPage = this.maxPage
        const index = ut.loopValue(curPage + add, maxPage)
        if (index !== curPage) {
            this.curPage = index
            const skins = this.list[index]?.itemSkins
            this.updateViewInfo({ list: this.list, type: this.type, index: this.curPage, skins: skins })
        }
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private checkPawnUnlock() {
        return gameHpr.player.getAllCanRecruitPawnIds().has(this.json.pawn_id)
    }

    private updateViewInfo(data: { type: string, list: any[], index?: number, skins?: any[] }) {
        this.type = data.type
        this.list = data.list
        this.itemSkin = data.skins?.length > 0 ? data.skins[0] : null
        this.curPage = data.index || 0
        this.maxPage = this.list.length
        this.json = this.list[this.curPage]?.json || this.list[this.curPage]
        this.titleLbl_.setLocaleKey(this.json.desc)
        const isPawnSkin = this.type === 'pawn_skin'
        this.nextNode_.active = !!(this.maxPage - 1)
        this.nameNode_.active = isPawnSkin
        this.timeNode_.active = isPawnSkin && !!this.itemSkin
        this.bgNode_.Swih(isPawnSkin ? 'pawn' : 'city')
        this.skinExchangeNode_.active = isPawnSkin && this.json.cond > 102 && this.json.type !== 5 // 101牛仔 102机甲没有炫彩且隐藏皮肤不显示
        if (isPawnSkin) {
            const skin = this.itemSkin, state = skin?.state
            const unlockList = gameHpr.user.getUnlockPawnSkinIds(), have = unlockList.has(this.json.id)
            if (this.json.cond > 100 || (this.json.cond === 5 && !have)) { // 盲盒皮肤 || 5 炫彩皮肤(一旦拥有会和盲盒皮肤分开存放)
                if (skin) { // 已经有了
                    if (state < 0) { // 封禁中
                        const cond = this.buttonsNode_.Swih('cond')[0]
                        cond.Child('val').setLocaleKey('toast.ban_item_skin')
                    } else {    // 正常 || 锁定中
                        const lay = this.buttonsNode_.Swih('lay')[0]
                        lay.Swih('', true)
                        const giveBtn = lay.Child('give_be', cc.Button)
                        const canGive = state === 0
                        giveBtn.interactable = canGive
                        giveBtn.Child('root', cc.MultiFrame).setFrame(canGive)
                        giveBtn.Child('count/val', cc.Label).string = this.itemSkin ? data.skins.length + '' : ''
                        const useBtn = lay.Child('use_be')
                        useBtn.opacity = this.checkPawnUnlock() && !gameHpr.isInLobby() ? 255 : 150
                        if (this.timeNode_.active = state > 0) {
                            this.timeNode_.Child('val').setLocaleKey('ui.item_skin_surplus_time', gameHpr.millisecondToCountDown(Math.max(0, state)))
                        }
                    }
                } else {
                    const cond = this.buttonsNode_.Swih('cond')[0]
                    const type = (this.json.cond > 100 || this.json.cond === 5) ? 2 : this.json.cond
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type)
                }
            } else {
                const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
                const buyList = gameHpr.user.getCanBuyPawnSkins(serverArea)
                const canBuy = buyList.has('id', this.json.id)
                if (have) {
                    const useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0]
                    useBtn.opacity = this.checkPawnUnlock() && !gameHpr.isInLobby() ? 255 : 150
                } else if (canBuy || this.json.cond === 3) {
                    const buy = this.buttonsNode_.Swih('buy_be')[0]
                    buy.Data = this.json
                    viewHelper.updateCostText(buy, this.json)
                } else {
                    const cond = this.buttonsNode_.Swih('cond')[0]
                    const type = this.json.cond === 1 ? 2 : this.json.cond
                    cond.Child('val').setLocaleKey('ui.get_cond_' + type)
                }
            }
        } else {
            const serverArea = gameHpr.isRelease ? gameHpr.getServerArea() : 'test'
            const buyList = gameHpr.user.getCanBuyCitySkins(serverArea)
            const unlockList = gameHpr.user.getUnlockCitySkinIds()
            const canBuy = buyList.has('id', this.json.id)
            const have = unlockList.has(this.json.id)
            if (have) {
                const useBtn = this.buttonsNode_.Swih('lay')[0].Swih('use_be')[0]
                useBtn.opacity = !gameHpr.isInLobby() ? 255 : 150
            } else if (canBuy || this.json.cond === 3) {
                const buy = this.buttonsNode_.Swih('buy_be')[0]
                buy.Data = this.json
                viewHelper.updateCostText(buy, this.json)
            } else {
                const cond = this.buttonsNode_.Swih('cond')[0]
                const type = this.json.cond === 1 ? 2 : this.json.cond
                cond.Child('val').setLocaleKey('ui.get_cond_' + type)
            }
        }
        this.loadSkin(isPawnSkin)
    }

    private async loadSkin(isPawnSkin: boolean) {
        const json = this.json
        const root = this.iconNode_.Child('mask').Swih(isPawnSkin ? 'root' : 'val')[0]
        if (isPawnSkin) {
            root.removeAllChildren()
            this.animCmpt = null
            this.nameNode_.Child('val').setLocaleKey(resHelper.getPawnName(json.pawn_id))
            viewHelper.showLoadingWait(true)
            const pfb = await assetsMgr.loadTempRes('march/ROLE_' + json.id, cc.Prefab, this.key)
            viewHelper.showLoadingWait(false)
            const node = cc.instantiate2(pfb, root)
            const conf = SHOP_PAWN_SKIN_ANIM_CONF[json.id]
            if (!!conf) {
                this.animCmpt = node.addComponent(PawnFrameAnimationCmpt).init(node.FindChild('body/anim', cc.Sprite), json.id, this.key)
                this.animCmpt.play()
            } else {
                node.Child('body/anim', cc.Animation)?.play('role_' + json.id + '_walk')
            }
        } else {
            resHelper.loadCityIcon(json.id, root, this.key)
        }
    }

    private async syncInfoToServer() {
        const pawnId = this.json.pawn_id, skinId = this.json.id
        // const conf = gameHpr.player.getConfigPawnInfo(pawnId)
        // if (conf.skinId === skinId) {
        //     viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })
        //     return this.hide()
        // }
        const { err, data } = await gameHpr.net.request('game/HD_UsePawnSkin', { pawnId, skinId })
        if (err === ecode.PAWN_NOT_EXIST) {
            return viewHelper.showAlert('toast.replace_all_pawn_skin_fail', { params: [resHelper.getPawnName(pawnId)] })
        } else if (err) {
            return viewHelper.showAlert(err)
        } else if (data.hasPawn) {
            viewHelper.showAlert('toast.replace_all_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })
        } else {
            viewHelper.showAlert('toast.replace_def_pawn_skin_succ', { params: [resHelper.getPawnName(pawnId)] })
        }
        gameHpr.player.changeConfigPawnSkinId(pawnId, skinId)
        this.hide()
    }

    private async changeCitySkin() {
        const playerInfo = gameHpr.getPlayerInfo(gameHpr.getUid()),
            index = playerInfo?.mainCityIndex || -1, id = this.json.id,
            skinId = id === playerInfo.cells.get(index).cityId ? 0 : id
        const { err, data } = await gameHpr.net.request('game/HD_ChangeCitySkin', { index, skinId }, true)
        if (err) {
            return viewHelper.showAlert(err)
        }
        gameHpr.world.updateCitySkin(index, skinId)
        viewHelper.hidePnl('menu/Collection')
        this.hide()
        gotoHelper.gotoMainCity()
    }

    update(dt: number) {
        this.animCmpt?.updateFrame(dt * 1000)
    }
}
