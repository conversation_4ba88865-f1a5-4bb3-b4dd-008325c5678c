
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/VersionDescPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '40835fk1RRNdp1gsmz2EBli', 'VersionDescPnlCtrl');
// app/script/view/common/VersionDescPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var VersionDesc_1 = require("../../common/constant/VersionDesc");
var ccclass = cc._decorator.ccclass;
var VersionDescPnlCtrl = /** @class */ (function (_super) {
    __extends(VersionDescPnlCtrl, _super);
    function VersionDescPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pagesNode_ = null; // path://root/pages_n
        //@end
        _this.list = [];
        _this.curVersion = '';
        return _this;
    }
    VersionDescPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    VersionDescPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    VersionDescPnlCtrl.prototype.onEnter = function () {
        var lang = mc.lang;
        var list = this.list = VersionDesc_1.versionDesc.filter(function (m) {
            var _a;
            if (m.content.length === 0) {
                return false;
            }
            var _b = __read(m.content[0].split('.'), 2), name = _b[0], id = _b[1];
            return !!((_a = assetsMgr.getJsonData(name, id)) === null || _a === void 0 ? void 0 : _a[lang]);
        });
        if (list.length === 0) {
            return this.pagesNode_.Swih('');
        }
        this.updateInfo(list[0]);
    };
    VersionDescPnlCtrl.prototype.onRemove = function () {
    };
    VersionDescPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/pages_n/info/view/content/look_other_version_be
    VersionDescPnlCtrl.prototype.onClickLookOtherVersion = function (event, _) {
        var _this = this;
        var sv = this.pagesNode_.Swih('list')[0].Component(cc.ScrollView);
        sv.List(this.list.length, function (it, i) {
            var data = _this.list[i];
            it.Data = data.version;
            var color = data.version === _this.curVersion ? '#4AB32E' : '#3F332F';
            var lbl = it.Child('name/version', cc.Label);
            lbl.Color(color).setLocaleKey('ui.version_desc2', data.version);
            lbl._forceUpdateRenderData();
            it.Child('name/line').Color(color).width = lbl.node.width;
            it.Child('time', cc.Label).string = data.time;
        });
    };
    // path://root/pages_n/list/view/content/item_be
    VersionDescPnlCtrl.prototype.onClickItem = function (event, _) {
        var version = event.target.Data;
        var data = this.list.find(function (m) { return m.version === version; });
        if (data) {
            this.updateInfo(data);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    VersionDescPnlCtrl.prototype.updateInfo = function (data) {
        var index = 0;
        this.curVersion = data.version;
        var sv = this.pagesNode_.Swih('info')[0].Component(cc.ScrollView);
        var it = sv.content;
        it.Child('top/version').setLocaleKey('ui.version_desc2', data.version);
        it.Child('top/time', cc.Label).string = data.time;
        it.Child('info').Items(data.content, function (node, text, i) {
            var rt = node.Child('val', cc.RichText);
            if (text.includes('ui.version_')) { // 小标题
                rt.fontSize = 24;
                rt.lineHeight = 28;
                rt.Color('#3F332F');
                rt.setLocaleKey(text);
                node.x = -2;
                node.height = 36;
                index = i;
            }
            else if (!text) { // 占位空行
                rt.string = '';
                node.x = 12;
                node.height = 10;
            }
            else { // 更新内容
                rt.fontSize = 22;
                rt.lineHeight = 26;
                rt.Color('#756963');
                rt.setLocaleKey('ui.no_desc', i - index, text);
                node.x = 12;
                node.height = rt.node.height;
            }
        });
    };
    VersionDescPnlCtrl = __decorate([
        ccclass
    ], VersionDescPnlCtrl);
    return VersionDescPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = VersionDescPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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