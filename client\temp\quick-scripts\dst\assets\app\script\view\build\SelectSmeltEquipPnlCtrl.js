
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/SelectSmeltEquipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '13469s0nulJs772+lk7Rs15', 'SelectSmeltEquipPnlCtrl');
// app/script/view/build/SelectSmeltEquipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectSmeltEquipPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectSmeltEquipPnlCtrl, _super);
    function SelectSmeltEquipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        _this.selectSmeltEquipBoxNode_ = null; // path://select_smelt_equip_box_be_n
        //@end
        _this.preRootHeight = 0;
        _this.equip = null;
        _this.cb = null;
        _this.isSameSmelEffect = false;
        _this.mainAttrs = []; //主属性 用于显示
        _this.effects = []; //效果
        return _this;
    }
    SelectSmeltEquipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectSmeltEquipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectSmeltEquipPnlCtrl.prototype.onEnter = function (equip, cb) {
        var _this = this;
        this.equip = equip;
        this.cb = cb;
        this.rootNode_.Child('info/desc').setLocaleKey('ui.smelt_desc', equip.baseName);
        this.rootNode_.Child('info/sv/name').setLocaleKey('ui.add_smelt_attr_desc', equip.baseName);
        ResHelper_1.resHelper.loadEquipIcon(equip.id, this.rootNode_.Child('main/val'), this.key);
        // 显示融炼的装备
        var buildLv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
        this.rootNode_.Child('smelt_select_equip_nbe').children.forEach(function (m, i) {
            var _a;
            var index = Number(m.name), root = m.Child('root'), state = m.Child('state');
            var needLv = Constant_1.EQUIP_SMELT_NEED_LV[index], isUnlock = buildLv >= needLv;
            var lock = root.Child('lock'), add = root.Child('add'), icon = root.Child('val', cc.Sprite);
            lock.active = !isUnlock;
            m.Component(cc.MultiFrame).setFrame(isUnlock);
            var viceId = (_a = equip === null || equip === void 0 ? void 0 : equip.smeltEffects[i]) === null || _a === void 0 ? void 0 : _a.id, smeltedEquip = GameHelper_1.gameHpr.player.getEquipById(viceId), isSmelted = !!viceId && !!smeltedEquip;
            add.active = isUnlock && !isSmelted;
            add.Color(isUnlock ? '#C3A587' : '#B1AAA2');
            if (isSmelted) {
                ResHelper_1.resHelper.loadEquipIcon(viceId, icon, _this.key);
            }
            else {
                icon.spriteFrame = null;
            }
            root.Child('remove_be').active = isSmelted;
            var color = (isUnlock && isSmelted) ? '#756963' : isUnlock ? '#4AB32E' : '#D7634D';
            var key = (isUnlock && isSmelted) ? 'equipText.name_' + viceId : isUnlock ? 'ui.select_equip_desc' : 'ui.need_lv_unlock';
            var params = isUnlock ? [] : [needLv];
            state.Color(color).setLocaleKey(key, params);
            m.Data = smeltedEquip;
            m.Component(cc.Button).interactable = isUnlock;
        });
        this.rootNode_.Child('info').Swih('desc');
        this.rootNode_.Child('ding/val', cc.Label).string = GameHelper_1.gameHpr.player.getFixator() + '';
        // 刷新装备追加信息
        var count = this.updateEquipAttr();
        // 刷新按钮
        this.updateButton(count);
    };
    SelectSmeltEquipPnlCtrl.prototype.onRemove = function () {
        this.rootNode_.Child('smelt_select_equip_nbe').children.forEach(function (m) { return m.Data = null; });
        this.selectSmeltEquipBoxNode_.Child('smelt_show').children.forEach(function (m) { return m.Data = null; });
        this.selectSmeltEquipBoxNode_.active = false;
        this.mainAttrs = [];
        this.effects = [];
        this.cb && this.cb();
        this.cb = null;
    };
    SelectSmeltEquipPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/smelt_select_equip_nbe
    SelectSmeltEquipPnlCtrl.prototype.onClickSmeltSelectEquip = function (event, data) {
        audioMgr.playSFX('click');
        this.showEquipList(Number(event.target.name));
    };
    // path://select_smelt_equip_box_be_n
    SelectSmeltEquipPnlCtrl.prototype.onClickSelectSmeltEquipBox = function (event, data) {
        this.closeEquipList();
    };
    // path://select_smelt_equip_box_be_n/root/list/view/content/smelt_equip_item_be
    SelectSmeltEquipPnlCtrl.prototype.onClickSmeltEquipItem = function (event, _) {
        audioMgr.playSFX('click');
        var type = event.target.Child('val').Data;
        if (type) {
            if (type > 1) {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_equip_effect_tip_2', { params: ['equipText.name_' + type] });
            }
            else {
                return ViewHelper_1.viewHelper.showAlert('toast.yet_equip_effect_tip_' + type);
            }
        }
        this.updateEquipListSelect(event.target.Data);
    };
    // path://root_n/buttons/smelting_be
    SelectSmeltEquipPnlCtrl.prototype.onClickSmelting = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID) < Constant_1.EQUIP_SMELT_NEED_LV[0]) {
            return ViewHelper_1.viewHelper.showAlert('toast.unlock_smelt_tip');
        }
        var ids = [];
        this.rootNode_.Child('smelt_select_equip_nbe').children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.id) {
                ids.push(m.Data.id);
            }
        });
        var viceCount = ids.length;
        if (viceCount === 0) {
            if (this.equip.smeltEffects.length > 0) {
                return ViewHelper_1.viewHelper.showMessageBox('ui.restore_smelt_tip', {
                    params: [this.equip.baseName],
                    ok: function () { return _this.do(ids); },
                    cancel: function () { }
                });
            }
            else {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_SELECT_EQUIP);
            }
        }
        else if (GameHelper_1.gameHpr.player.getFixator() < viceCount) {
            return ViewHelper_1.viewHelper.showAlert('toast.fixator_not_enough');
        }
        else if (this.isSameSmelEffect) {
            ViewHelper_1.viewHelper.showMessageBox('ui.smelt_same_effect_tip', {
                ok: function () { return _this.do(ids); },
                cancel: function () { }
            });
        }
        else if (!ViewHelper_1.viewHelper.showNoLongerTip('begin_smelt_tip', {
            content: 'ui.begin_smelt_tip',
            ok: function () { return _this.do(ids); },
            cancel: function () { },
        })) {
            this.do(ids);
        }
    };
    // path://root_n/smelt_select_equip_nbe/0/root/remove_be
    SelectSmeltEquipPnlCtrl.prototype.onClickRemove = function (event, data) {
        var index = event.target.parent.parent.name;
        this.updateSmeltSelectEquip(Number(index), null);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectSmeltEquipPnlCtrl.prototype.updateListPosition = function () {
        var root = this.rootNode_;
        if (this.preRootHeight !== root.height) {
            this.preRootHeight = root.height;
            var node_1 = this.selectSmeltEquipBoxNode_.Child('root');
            node_1.setPosition(ut.convertToNodeAR(root.Child('equip_list'), this.selectSmeltEquipBoxNode_));
            node_1.scale = root.scale;
            var show_1 = this.selectSmeltEquipBoxNode_.Child('smelt_show');
            root.Child('smelt_select_equip_nbe').children.forEach(function (m) {
                node_1 = show_1.Child(m.name);
                node_1.setPosition(ut.convertToNodeAR(m, show_1));
                node_1.scale = root.scale;
            });
        }
    };
    // 显示装备列表
    SelectSmeltEquipPnlCtrl.prototype.showEquipList = function (index) {
        var _this = this;
        var _a, _b, _c, _d;
        this.updateListPosition();
        this.selectSmeltEquipBoxNode_.Data = index;
        this.selectSmeltEquipBoxNode_.active = true;
        this.selectSmeltEquipBoxNode_.Child('smelt_show').Swih(index);
        var node = this.rootNode_;
        var root = this.selectSmeltEquipBoxNode_.Child('root');
        var otherShowEquipUid = (_b = (_a = node.Child('smelt_select_equip_nbe/' + Number(!index))) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.uid;
        var mainEquipEffectMap = this.equip.getOriginalAttrTypeMap(), yetSmeltEquipIdMap = GameHelper_1.gameHpr.player.getYetSmeltEquipIdMap(this.equip.uid);
        var equips = this.getCanSmeltEquips(otherShowEquipUid, mainEquipEffectMap, yetSmeltEquipIdMap), len = equips.length;
        var sv = root.Child('list', cc.ScrollView);
        sv.Child('empty').active = len === 0;
        sv.Items([null].concat(equips), function (it, data) {
            it.Data = data;
            var icon = it.Child('val');
            if (icon.active = !!data) {
                ResHelper_1.resHelper.loadEquipIcon(data.id, icon, _this.key, data.getSmeltCount());
                if (data.effects.some(function (e) { return mainEquipEffectMap[e.type]; })) {
                    icon.Data = 1;
                }
                else if (yetSmeltEquipIdMap[data.id]) {
                    icon.Data = yetSmeltEquipIdMap[data.id];
                }
                else {
                    icon.Data = 0;
                }
                icon.opacity = icon.Data ? 120 : 255;
            }
            it.Child('none').active = !data;
            it.Component(cc.MultiFrame).setFrame(!icon.Data);
        });
        var equipUid = (_d = (_c = node.Child('smelt_select_equip_nbe/' + index)) === null || _c === void 0 ? void 0 : _c.Data) === null || _d === void 0 ? void 0 : _d.uid;
        var svIndex = equips.findIndex(function (m) { return m.uid === equipUid; });
        if (svIndex >= 0) {
            var lay = sv.content.Component(cc.Layout), item = sv.GetItemNode();
            var w = len * (item.width + lay.spacingX) + lay.paddingLeft + lay.paddingRight - lay.spacingX, pw = sv.content.parent.width;
            var minx = Math.max(w - pw, 0);
            sv.stopAutoScroll();
            sv.content.x = -Math.min(Math.max(0, svIndex * (item.width + lay.spacingX) - item.width * 0.5), minx);
        }
        else {
            sv.scrollToLeft();
        }
        this.updateEquipListSelect(equips[svIndex]);
    };
    // 刷新选择信息
    SelectSmeltEquipPnlCtrl.prototype.updateEquipListSelect = function (equip) {
        var index = this.selectSmeltEquipBoxNode_.Data;
        var root = this.selectSmeltEquipBoxNode_.Child('root');
        // 刷新选择
        root.Child('list', cc.ScrollView).content.children.forEach(function (it) {
            var data = it.Data;
            var select = it.Child('select').active = (equip === null || equip === void 0 ? void 0 : equip.uid) === (data === null || data === void 0 ? void 0 : data.uid);
            it.Component(cc.Button).interactable = !select;
        });
        // 显示选择的
        var node = this.selectSmeltEquipBoxNode_.Child('smelt_show/' + index);
        node.Data = equip;
        if (equip) {
            ResHelper_1.resHelper.loadEquipIcon(equip.id, node.Swih('val')[0], this.key, equip.getSmeltCount());
        }
        else {
            node.Swih('add');
        }
        // 显示信息
        root.Child('empty').active = !equip;
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        sv.stopAutoScroll();
        info.y = 0;
        if (sv.setActive(!!equip)) {
            ViewHelper_1.viewHelper.updateEquipView(info, equip, this.key);
            info.Child('tip').active = !!(equip === null || equip === void 0 ? void 0 : equip.effects.length) && GameHelper_1.gameHpr.world.getExclusiveEquipEffects(this.equip.id).has(equip.effects[0].type);
        }
    };
    SelectSmeltEquipPnlCtrl.prototype.closeEquipList = function () {
        var _a;
        var index = this.selectSmeltEquipBoxNode_.Data, showNode = this.selectSmeltEquipBoxNode_.Child('smelt_show');
        var equip = (_a = showNode.Child(index)) === null || _a === void 0 ? void 0 : _a.Data;
        showNode.children.forEach(function (m) { return m.Data = null; });
        this.selectSmeltEquipBoxNode_.active = false;
        this.updateSmeltSelectEquip(index, equip);
    };
    SelectSmeltEquipPnlCtrl.prototype.updateSmeltSelectEquip = function (index, equip) {
        var node = this.rootNode_;
        var it = node.Child('smelt_select_equip_nbe/' + index), root = it.Child('root');
        it.Data = equip;
        if (equip) {
            root.Child('remove_be').active = true;
            root.Child('add').active = root.Child('lock').active = false;
            ResHelper_1.resHelper.loadEquipIcon(equip.id, root.Child('val'), this.key, equip.getSmeltCount());
            it.Child('state').Color('#756963').setLocaleKey('equipText.name_' + equip.id);
        }
        else {
            root.Child('add').active = true;
            root.Child('val', cc.Sprite).spriteFrame = null;
            root.Child('remove_be').active = root.Child('lock').active = false;
            it.Child('state').Color('#4AB32E').setLocaleKey('ui.select_equip_desc');
        }
        // 刷新装备追加信息
        var count = this.updateEquipAttr();
        // 刷新按钮
        this.updateButton(count);
    };
    SelectSmeltEquipPnlCtrl.prototype.updateEquipAttr = function () {
        var _this = this;
        var count = 0;
        this.mainAttrs = [];
        this.effects = [];
        this.rootNode_.Child('smelt_select_equip_nbe').children.forEach(function (m) {
            var data = m.Data;
            if (data) {
                count += 1;
                data.mainAttrs.forEach(function (v) {
                    var attr = _this.mainAttrs.find(function (x) { return x.type === v.type; }), value = Math.round(v.value * 0.5);
                    if (attr) {
                        attr.value += value;
                    }
                    else {
                        _this.mainAttrs.push({ value: value, type: v.type, base: [], initValue: 0 });
                    }
                });
                _this.effects.pushArr(data.effects);
            }
        });
        //
        if (count) {
            var sv = this.rootNode_.Child('info').Swih('sv')[0].Component(cc.ScrollView);
            sv.stopAutoScroll();
            sv.content.y = 0;
            var node = sv.content.Child('attrs');
            var attrNode = node.Child('attr'), effectNode = node.Child('effects');
            // 属性
            attrNode.Items(this.mainAttrs, function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val', cc.Label).string = '+' + data.value;
            });
            // 效果
            if (effectNode.active = this.effects.length > 0) {
                effectNode.Items(this.effects, function (it, data) { return it.setLocaleKey('ui.equip_secondary_text', assetsMgr.lang.apply(assetsMgr, __spread([data.name], data.getDescParams()))); });
            }
        }
        else {
            this.rootNode_.Child('info').Swih('desc');
        }
        return count;
    };
    SelectSmeltEquipPnlCtrl.prototype.updateButton = function (count) {
        var _a;
        var button = this.rootNode_.Child('buttons/smelting_be');
        button.Child('lay/fixator/val', cc.Label).string = '' + count;
        button.Child('lay/val').setLocaleKey(!count && this.equip.isSmelt() ? 'ui.button_restore' : 'ui.button_smelting');
        // 判断现有的装备属性是否变化
        var effects = ((_a = this.equip) === null || _a === void 0 ? void 0 : _a.smeltEffects) || [];
        if (count !== effects.length) {
            this.isSameSmelEffect = false;
        }
        else {
            for (var i = 0; i < effects.length; i++) {
                var viceId = effects[i].id, attrs = GameHelper_1.gameHpr.player.getEquipById(viceId).attrs.map(function (m) { return m.attr; });
                this.isSameSmelEffect = this.hasSmeltEquipSameEffect(viceId, attrs);
                if (!this.isSameSmelEffect) {
                    break;
                }
            }
        }
        var buildLv = GameHelper_1.gameHpr.player.getBuildLv(Constant_1.BUILD_SMITHY_NID);
        this.rootNode_.Child('buttons/smelting_be').opacity = (this.isSameSmelEffect || buildLv < Constant_1.EQUIP_SMELT_NEED_LV[0]) ? 120 : 255;
    };
    // 获取可参与熔炼的装备
    SelectSmeltEquipPnlCtrl.prototype.getCanSmeltEquips = function (ignoreUid, mainEquipEffectMap, yetSmeltEquipIdMap) {
        return GameHelper_1.gameHpr.player.getEquips().filter(function (m) { return m.uid !== ignoreUid && !!m.smelt_type && m.effects.length > 0; }).sort(function (a, b) {
            var aw = a.effects.some(function (e) { return mainEquipEffectMap[e.type]; }) ? 1 : 0;
            var bw = b.effects.some(function (e) { return mainEquipEffectMap[e.type]; }) ? 1 : 0;
            aw = aw * 10 + (yetSmeltEquipIdMap[a.id] ? 1 : 0);
            bw = bw * 10 + (yetSmeltEquipIdMap[b.id] ? 1 : 0);
            return aw - bw;
        });
    };
    SelectSmeltEquipPnlCtrl.prototype.do = function (ids) {
        this.cb && this.cb(this.equip.uid, ids);
        this.hide();
    };
    /**
     * 检查是否熔炼过相同的属性
     * @param viceId 副装备ID
     * @param attrs 要检查的属性数组
     * @returns 是否熔炼过相同的属性
     */
    SelectSmeltEquipPnlCtrl.prototype.hasSmeltEquipSameEffect = function (viceId, attrs) {
        // 主装备中找到被熔炼的属性
        var smeltAttrs = this.equip.attrs.map(function (m) { return m.attr; }).filter(function (m) { return m.length >= 5 && m[4] === viceId; });
        if (smeltAttrs.length === 0 || smeltAttrs.length < attrs.length) {
            return false;
        }
        // 比较属性 每一条都相同则返回true
        for (var i = 0; i < attrs.length; i++) {
            var attr = attrs[i];
            var smeltAttr = smeltAttrs[i];
            if (smeltAttr.length < attr.length) {
                return false;
            }
            for (var j = 0; j < attr.length; j++) {
                var value = attr[j];
                // 如果是基础属性且是第三个值(索引2)，则熔炼的值减半
                if (j === 2 && attr[0] === 0) {
                    value = Math.round(value / 2);
                }
                if (smeltAttr[j] !== value) {
                    return false;
                }
            }
        }
        return true;
    };
    SelectSmeltEquipPnlCtrl = __decorate([
        ccclass
    ], SelectSmeltEquipPnlCtrl);
    return SelectSmeltEquipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectSmeltEquipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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