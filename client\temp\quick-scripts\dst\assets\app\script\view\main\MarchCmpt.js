
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/MarchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4b92cVAtRxMKqPG7IAN+c/F', 'MarchCmpt');
// app/script/view/main/MarchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("../../common/camera/CameraCtrl");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
// 行军
var MarchCmpt = /** @class */ (function (_super) {
    __extends(MarchCmpt, _super);
    function MarchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.LINE_HEIGHT = 24;
        _this.rootNode = null;
        _this.lineNode = null;
        _this.data = null;
        _this.marchRoleNode = null;
        _this.roleSpeed = cc.v2(); //移动速度
        _this.rolePosition = cc.v2(); //角色的正常位置
        _this.offset = cc.v2(); //当前的偏移
        _this.moveTime = 0;
        _this.angleOffset = 0;
        _this.isCheckLineOffset = false;
        _this.loadingUid = '';
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        _this._temp_vec2_3 = cc.v2();
        _this._temp_vec2_4 = cc.v2();
        return _this;
    }
    MarchCmpt.prototype.onLoad = function () {
    };
    MarchCmpt.prototype.init = function (data, roleNode, key) {
        this.rootNode = this.Child('root');
        this.lineNode = this.Child('root/val');
        this.rootNode.active = true;
        this.data = data;
        this.offset.set2(0, 0);
        this.node.angle = data.angle - 90;
        this.node.setPosition(data.startPos);
        this.rootNode.Child('val').opacity = data.opacity;
        this.lineNode.y = -this.LINE_HEIGHT;
        this.lineNode.Component(cc.MultiFrame).setFrame(data.getMarchLineType());
        this.createMarchRole(roleNode, key);
        this.hide((this.data.hideTime - Date.now()) * 0.001);
        this.checkUpdateInCamera();
        return this;
    };
    MarchCmpt.prototype.clean = function () {
        var _a;
        (_a = this.node) === null || _a === void 0 ? void 0 : _a.setActive(false);
        this.cleanRole();
        this.data = null;
        this.loadingUid = '';
    };
    MarchCmpt.prototype.cleanRole = function () {
        var _a;
        (_a = this.marchRoleNode) === null || _a === void 0 ? void 0 : _a.destroy();
        this.marchRoleNode = null;
        this.moveTime = 0;
    };
    MarchCmpt.prototype.getData = function () { return this.data; };
    Object.defineProperty(MarchCmpt.prototype, "uid", {
        get: function () { var _a; return (_a = this.data) === null || _a === void 0 ? void 0 : _a.uid; },
        enumerable: false,
        configurable: true
    });
    MarchCmpt.prototype.isHasIndex = function (index) {
        var _a, _b;
        if (((_a = this.data) === null || _a === void 0 ? void 0 : _a.startIndex) === index) {
            return this.data.getElapsedDistance() < 100;
        }
        else if (((_b = this.data) === null || _b === void 0 ? void 0 : _b.targetIndex) === index) {
            return this.data.getSurplusDistance() < 100;
        }
        return false;
    };
    // 创建移动目标
    MarchCmpt.prototype.createMarchRole = function (root, key) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function () {
            var node, url, infoUrl, _c, pfb, infoPfb, info, label, info, res, goods, has;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        node = this.marchRoleNode;
                        if (!(!(node === null || node === void 0 ? void 0 : node.isValid) || ((_a = node === null || node === void 0 ? void 0 : node.Data) === null || _a === void 0 ? void 0 : _a.uid) !== this.data.uid)) return [3 /*break*/, 2];
                        if (this.loadingUid === this.data.uid) {
                            return [2 /*return*/];
                        }
                        this.loadingUid = this.data.uid;
                        this.cleanRole();
                        url = this.data.getMarchRoleUrl(), infoUrl = this.data.getMarchInfoUrl();
                        return [4 /*yield*/, Promise.all([assetsMgr.loadTempRes(url, cc.Prefab, key), assetsMgr.loadTempRes(infoUrl, cc.Prefab, key)])];
                    case 1:
                        _c = __read.apply(void 0, [_d.sent(), 2]), pfb = _c[0], infoPfb = _c[1];
                        if (!this.data || !this.isValid || !root.isValid) {
                            return [2 /*return*/];
                        }
                        else if (!pfb || !infoPfb) {
                            return [2 /*return*/];
                        }
                        node = this.marchRoleNode = cc.instantiate2(pfb, root);
                        info = cc.instantiate2(infoPfb, node);
                        info.setPosition(0, this.data.isHero() ? 110 : 86);
                        if (this.data.isArmy()) {
                            label = info.Child('name', cc.Label);
                            label.string = this.data.getArmyName();
                            label._forceUpdateRenderData();
                            info.width = Math.max(100, label.node.width * label.node.scaleX + 20);
                        }
                        _d.label = 2;
                    case 2:
                        if (!this.data.isArmy()) {
                            info = node.Child('MARCH_TRANSIT_INFO');
                            if (info) {
                                res = info.Child('res'), goods = this.data['goods'];
                                has = res.active = goods.count > 0;
                                has && ViewHelper_1.viewHelper.updateCostViewOne(res, goods);
                                info.Child('empty').active = !has;
                                info.width = Math.max(100, res.width * res.scaleX + 20);
                            }
                        }
                        node.Data = this.data;
                        node.opacity = this.data.hideTime <= 0 ? this.data.opacity : 0;
                        this.rolePosition.set(this.data.getMoveStartPos(this.data.getSurplusTime()));
                        node.setPosition(this.offset.add(this.rolePosition, this._temp_vec2_1));
                        (_b = node.Child('body/anim', cc.Animation)) === null || _b === void 0 ? void 0 : _b.play(this.data.getMarchRoleAnim());
                        node.Child('body').scaleX = this.data.roleMoveSpeed.x < 0 ? -1 : 1;
                        this.moveTime = Date.now();
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新线的偏移
    MarchCmpt.prototype.updateLineOffset = function (idx, len) {
        var _a;
        var one = len % 2 !== 0;
        var dir = idx % 2 === 0 ? -1 : 1;
        var cnt = Math.floor((idx + (one ? 1 : 0)) / 2);
        var ang = this.node.angle + 180 * dir + this.angleOffset;
        var sx = Math.min(80 / len, 20);
        var w = (sx * cnt + (one ? 0 : sx * 0.5)) * dir;
        // cc.log('updateLineOffset len=' + len + ' w=' + w)
        this.offset.set2(ut.cos(ang) * w, ut.sin(ang) * w);
        // cc.log(this.uid, idx, len, dir, cnt, ang, w, this.offset.Join())
        this.node.setPosition(this.offset.add(this.data.startPos, this._temp_vec2_1));
        (_a = this.marchRoleNode) === null || _a === void 0 ? void 0 : _a.setPosition(this.offset.add(this.rolePosition, this._temp_vec2_1));
    };
    // 检测刷新和相机区域内相机的点
    MarchCmpt.prototype.checkUpdateInCamera = function () {
        if (!this.data) {
            return;
        }
        var pointA = this.data.startPos, pointB = this.data.targetPos;
        var points = CameraCtrl_1.cameraCtrl.checkLineToRectPoint(pointA, pointB);
        if (this.rootNode.active = points.length > 0) {
            var _a = __read(points, 2), s = _a[0], e = _a[1], isA = s.equals(pointA);
            if (isA) {
                this.rootNode.y = 0;
            }
            else {
                this.rootNode.y = s.sub(pointA, this._temp_vec2_4).mag();
            }
            e = e || pointB;
            var dis = e.equals(pointB) && isA ? this.data.distance : s.sub(e, this._temp_vec2_3).mag();
            this.rootNode.height = dis;
            this.lineNode.height = dis + this.LINE_HEIGHT;
        }
    };
    // 隐藏
    MarchCmpt.prototype.hide = function (time) {
        this.data.hideTime = time <= 0 ? 0 : (Date.now() + time * 1000);
        this.rootNode.opacity = time <= 0 ? 255 : 0;
        if (this.marchRoleNode) {
            this.marchRoleNode.opacity = time <= 0 ? this.data.opacity : 0;
        }
    };
    // 刷新透明度
    MarchCmpt.prototype.updateOpacity = function () {
        this.rootNode.Child('val').opacity = this.data.opacity;
        if (this.marchRoleNode && this.data.hideTime <= 0) {
            this.marchRoleNode.opacity = this.data.opacity;
        }
    };
    MarchCmpt.prototype.update = function (dt) {
        var _a;
        if (!this.data || !((_a = this.marchRoleNode) === null || _a === void 0 ? void 0 : _a.isValid)) {
            this.marchRoleNode = null;
            return;
        }
        else if (this.data.getSurplusTime() <= 0) {
            return GameHelper_1.gameHpr.world.removeMarch(this.data.uid);
        }
        var now = Date.now();
        var speed = this.moveTime > 0 ? now - this.moveTime : 0;
        this.moveTime = now;
        // 移动角色
        this.data.roleMoveSpeed.mul(speed * 0.001, this.roleSpeed);
        MapHelper_1.mapHelper.amendMoveSpeed(this.roleSpeed, this.rolePosition, this.data.targetPos);
        this.rolePosition.addSelf(this.roleSpeed);
        var pos = this.offset.add(this.rolePosition, this._temp_vec2_2);
        this.marchRoleNode.setPosition(pos);
        this.marchRoleNode.zIndex = cc.macro.MAX_ZINDEX - pos.y;
        // 移动线
        this.lineNode.y += dt * 10;
        if (this.lineNode.y >= 0) {
            this.lineNode.y -= this.LINE_HEIGHT;
        }
        // 刷新隐藏时间
        if (this.data.hideTime > 0 && Date.now() >= this.data.hideTime) {
            this.hide(0);
        }
    };
    MarchCmpt = __decorate([
        ccclass
    ], MarchCmpt);
    return MarchCmpt;
}(cc.Component));
exports.default = MarchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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