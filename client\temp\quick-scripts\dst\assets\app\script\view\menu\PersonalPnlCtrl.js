
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/PersonalPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '54465B3zhVOQLbHmRM4qPkr', 'PersonalPnlCtrl');
// app/script/view/menu/PersonalPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var TextButtonCmpt_1 = require("../cmpt/TextButtonCmpt");
var ccclass = cc._decorator.ccclass;
var PersonalPnlCtrl = /** @class */ (function (_super) {
    __extends(PersonalPnlCtrl, _super);
    function PersonalPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.personalDescEb_ = null; // path://root/pages_n/0/content/personal_desc_eb_ebee
        _this.inputFriendEb_ = null; // path://root/pages_n/1/input/input_friend_eb
        //@end
        _this.user = null;
        _this.friend = null;
        _this.rank = null;
        _this.preHeadIcon = '';
        _this.preTitle = 0;
        _this.prePersonalDesc = '';
        _this.gameRecordList = [];
        _this.PKEY_TAB = 'PERSONAL_TAB';
        return _this;
    }
    PersonalPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e;
        return [
            (_a = {}, _a[EventType_1.default.MODIFY_NICKNAME_SUC] = this.onModifyNicknameSuc, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_FRIEND_LIST] = this.onUpdateFriendList, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_FRIEND_APPLYS] = this.onUpdateFriendApplys, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_BLACKLISTS] = this.onUpdateBlacklists, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_FRIEND_INFO] = this.onUpdateFriendInfo, _e.enter = true, _e),
        ];
    };
    PersonalPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.friend = this.getModel('friend');
                this.rank = this.getModel('rank');
                return [2 /*return*/];
            });
        });
    };
    PersonalPnlCtrl.prototype.onEnter = function (tab) {
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
    };
    PersonalPnlCtrl.prototype.onRemove = function () {
        // reddotHelper.pause('new_friend_apply', false)
        // 头像改变同步到服务器
        var headIcon = this.user.getHeadIcon();
        if (this.preHeadIcon !== headIcon) {
            GameHelper_1.gameHpr.net.send('lobby/HD_ChangeUserHeadIcon', { headIcon: headIcon });
        }
        // 称号改变同步到服务器
        var title = this.user.getTitle();
        if (this.preTitle !== title) {
            this.emit(EventType_1.default.CLOSE_SELECT_CELL);
            GameHelper_1.gameHpr.net.send('lobby/HD_ChangeUserTitle', { id: title });
        }
        // 个人简介同步到服务器
        var personalDesc = this.user.getPersonalDesc();
        if (this.prePersonalDesc !== personalDesc) {
            GameHelper_1.gameHpr.net.send('lobby/HD_ChangeUserPersonalDesc', { personalDesc: personalDesc });
        }
    };
    PersonalPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    PersonalPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) { // 个人信息
            this.updatePersonalInfo(node);
        }
        else if (type === 1) { // 添加好友
            ReddotHelper_1.reddotHelper.set('new_friend_apply', false, true);
            this.inputFriendEb_.string = '';
            this.showApplys(node);
        }
        else if (type === 2) { // 个人战绩
            this.updateBattleRecordInfo(node);
        }
    };
    // path://root/pages_n/0/head_be/name/nickname/edit/modify_name_be
    PersonalPnlCtrl.prototype.onClickModifyName = function (event, data) {
        audioMgr.playSFX('click');
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return ViewHelper_1.viewHelper.showAlert('toast.novice_not_opt_tip');
        }
        ViewHelper_1.viewHelper.showPnl('menu/ModifyNickname');
    };
    // path://root/pages_n/0/head_be
    PersonalPnlCtrl.prototype.onClickHead = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (GameHelper_1.gameHpr.isNoviceMode) {
            return ViewHelper_1.viewHelper.showAlert('toast.novice_not_opt_tip');
        }
        var user = this.user;
        ViewHelper_1.viewHelper.showPnl('menu/SelectHeadIcon', function (val) {
            if (user.setHeadIcon(val) && _this.isValid) {
                ResHelper_1.resHelper.loadPlayerHead(_this.pagesNode_.Child('0/head_be/val'), val, _this.key);
            }
        });
    };
    // path://root/pages_n/0/head_be/name/title/wear_title_be
    PersonalPnlCtrl.prototype.onClickWearTitle = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var user = this.user;
        ViewHelper_1.viewHelper.showPnl('menu/SelectTitle', function (id) {
            if (user.setTitle(id) && _this.isValid) {
                var node = _this.pagesNode_.Child(0);
                var nameNode = node.Child('head_be/name');
                ViewHelper_1.viewHelper.updatePlayerTitleText(nameNode.Child('title/title'), user.getUid(), _this.key);
            }
        });
    };
    // path://root/pages_n/0/head_be/name/copy_uid_be
    PersonalPnlCtrl.prototype.onClickCopyUid = function (event, data) {
        GameHelper_1.gameHpr.copyToClipboard(this.user.getUid(), 'toast.yet_copy_clipboard');
    };
    // path://root/pages_n/0/content/p/button/popularity_record_be
    PersonalPnlCtrl.prototype.onClickPopularityRecord = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/PopularityRecord');
    };
    // path://root/pages_n/0/content/personal_desc_eb_ebee
    PersonalPnlCtrl.prototype.onClickPersonalDescEnded = function (event, data) {
        var desc = event.string.trim();
        if (ut.getStringLen(desc) > 40) {
            ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
            desc = event.string = ut.nameFormator(desc, 20, '');
        }
        else if (GameHelper_1.gameHpr.getTextNewlineCount(desc) >= 1) {
            ViewHelper_1.viewHelper.showAlert('toast.text_len_limit_content');
            var arr = desc.split('\n');
            if (arr.length >= 2) {
                desc = arr[0];
            }
        }
        this.user.setPersonalDesc(desc);
    };
    // path://root/pages_n/0/friends/list/view/content/friend_be
    PersonalPnlCtrl.prototype.onClickFriend = function (event, _data) {
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('menu/FriendInfo', data);
        }
    };
    // path://root/pages_n/0/friends/list/view/content/friend_be/gift_be
    PersonalPnlCtrl.prototype.onClickGift = function (event, _data) {
        var data = event.target.parent.Data;
        if (data && data.giftList.length > 0) {
            this.claimFriendGift(data.uid, data.giftList[0]);
        }
    };
    // path://root/pages_n/0/friends/list/view/content/friend_be/chat_be
    PersonalPnlCtrl.prototype.onClickChat = function (event, _data) {
        audioMgr.playSFX('click');
        var data = event.target.parent.Data;
        if (data) {
            var pnl = mc.currWindName === 'lobby' ? 'lobby/LobbyChat' : 'common/Chat';
            ViewHelper_1.viewHelper.showPnl(pnl, { target: data });
            this.hide();
        }
    };
    // path://root/pages_n/1/add_friend_be
    PersonalPnlCtrl.prototype.onClickAddFriend = function (event, data) {
        var _this = this;
        var val = this.inputFriendEb_.string.trim();
        if (!val) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_input_name_or_uid');
        }
        this.friend.applyFriend(val).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.inputFriendEb_.string = '';
            }
            ViewHelper_1.viewHelper.showAlert('toast.apply_friend_succeed');
            GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.SUBS);
        });
    };
    // path://root/pages_n/1/list/view/content/item/apply_agree_be@0
    PersonalPnlCtrl.prototype.onClickApplyAgree = function (event, data) {
        var info = event.target.parent.Data;
        if (info) {
            ViewHelper_1.viewHelper.showPnl('menu/AgreeFriendApply', info, data === '1');
        }
    };
    // path://root/pages_n/1/black_list_be
    PersonalPnlCtrl.prototype.onClickBlackList = function (event, _data) {
        ViewHelper_1.viewHelper.showPnl('menu/Blacklist');
    };
    // path://root/pages_n/2/game_info/info/giveup_be
    PersonalPnlCtrl.prototype.onClickGiveup = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.world.getServerRunTime() <= ut.Time.Day * 3) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.no_giveup_game_tip');
        }
        else if (GameHelper_1.gameHpr.alliance.isMeCreater()) {
            return ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.ALLI_CREATOR_CANT_GIVE_UP);
        }
        var key = GameHelper_1.gameHpr.isRankServer() ? 'ui.giveup_rank_game_tip' : 'ui.giveup_game_tip';
        ViewHelper_1.viewHelper.showMessageBox(key, {
            ok: function () { return _this.giveupGame(); },
            cancel: function () { }
        });
    };
    // path://root/pages_n/2/records/history/content/item/detail_be
    PersonalPnlCtrl.prototype.onClickDetail = function (event, _data) {
        var data = event.target.parent.Data;
        data && ViewHelper_1.viewHelper.showPnl('menu/PersonalGameDetail', data);
    };
    // path://root/pages_n/2/more_history_be
    PersonalPnlCtrl.prototype.onClickMoreHistory = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/PersonalGameHistory');
    };
    // path://root/pages_n/2/game_info/info/settle_be
    PersonalPnlCtrl.prototype.onClickSettle = function (event, data) {
        var _this = this;
        GameHelper_1.gameHpr.player.nowSettleGame().then(function (ok) { return (_this.isValid && ok) && _this.hide(); });
    };
    // path://root/pages_n/2/game_info/info/settle_info_be
    PersonalPnlCtrl.prototype.onClickSettleInfo = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('main/GameOver');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 修改昵称成功
    PersonalPnlCtrl.prototype.onModifyNicknameSuc = function () {
        this.pagesNode_.Child('0/head_be/name/nickname/val', cc.Label).string = ut.nameFormator(this.user.getNickname(), 11);
    };
    PersonalPnlCtrl.prototype.onUpdateFriendList = function () {
        this.showFriends();
    };
    PersonalPnlCtrl.prototype.onUpdateFriendApplys = function () {
        this.showApplys();
    };
    PersonalPnlCtrl.prototype.onUpdateBlacklists = function () {
        this.updateBlackListInfo();
    };
    // 刷新好友信息
    PersonalPnlCtrl.prototype.onUpdateFriendInfo = function (data) {
        var sv = this.pagesNode_.Child('0/friends/list', cc.ScrollView);
        var it = sv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateFriendItem(it, data);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 个人信息 -----------------------------------------------------------------------------------------------------------------------------
    PersonalPnlCtrl.prototype.updatePersonalInfo = function (node) {
        node = node || this.pagesNode_.Child(0);
        var isNoviceMode = GameHelper_1.gameHpr.isNoviceMode;
        this.preHeadIcon = this.user.getHeadIcon();
        this.preTitle = this.user.getTitle();
        var uid = this.user.getUid();
        ResHelper_1.resHelper.loadPlayerHead(node.Child('head_be/val'), this.preHeadIcon, this.key);
        var nameNode = node.Child('head_be/name'), contentNode = node.Child('content');
        nameNode.Child('copy_uid_be/uid', cc.Label).string = 'UID: ' + uid;
        nameNode.Child('nickname/val', cc.Label).string = ut.nameFormator(this.user.getNickname(), 11);
        if (this.personalDescEb_.setActive(!isNoviceMode)) {
            this.prePersonalDesc = this.personalDescEb_.string = this.user.getPersonalDesc();
        }
        // 称号
        ViewHelper_1.viewHelper.updatePlayerTitleText(nameNode.Child('title/title'), this.user.getUid(), this.key);
        // 段位
        ViewHelper_1.viewHelper.updatePlayerRankInfo(contentNode.Child('rank'), uid, this.key);
        // 人气
        ViewHelper_1.viewHelper.updatePlayerPopularity(contentNode.Child('popularity'), contentNode.Child('p/button'), uid, this.key);
        // 好友列表
        this.showFriends(node);
    };
    PersonalPnlCtrl.prototype.showFriends = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(0);
        var sv = node.Child('friends/list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var list = this.friend.getFriends(), len = list.length;
        list.sort(function (a, b) {
            if (a.notReadCount !== b.notReadCount) {
                return b.notReadCount - a.notReadCount;
            }
            var aGiftCount = a.giftList.length, bGiftCount = b.giftList.length;
            if (aGiftCount !== bGiftCount) {
                return bGiftCount - aGiftCount;
            }
            return a.offlineTime - b.offlineTime;
        });
        sv.List(len, function (it, i) { return _this.updateFriendItem(it, list[i]); });
        sv.node.Child('empty').active = !len;
        var count = node.Child('friends/title/bg');
        count.Child('cur', cc.Label).string = list.length + '/';
        count.Child('max', cc.Label).string = 100 + '';
    };
    PersonalPnlCtrl.prototype.updateFriendItem = function (it, data) {
        it.Data = data;
        ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, this.key);
        it.Child('name/val', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
        it.Child('name/note', cc.Label).string = data.noteName ? '(' + data.noteName + ')' : '';
        var serverNode = it.Child('online/server');
        serverNode.active = false;
        if (data.offlineTime) {
            it.Child('online/val').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.getOfflineTime()));
        }
        else if (data.playSid) {
            it.Child('online/val').Color('#49983C').setLocaleKey('ui.in_game');
            serverNode.active = true;
            var _a = GameHelper_1.gameHpr.getServerNameById(data.playSid), key = _a.key, id = _a.id;
            serverNode.setLocaleKey('ui.bracket', assetsMgr.lang(key, id));
        }
        else {
            it.Child('online/val').Color('#49983C').setLocaleKey('ui.in_ready_war');
        }
        if (it.Child('chat_be/dot').active = data.notReadCount > 0) {
            it.Child('chat_be/dot/val', cc.Label).string = Math.min(99, data.notReadCount) + '';
        }
        var giftCount = data.giftList.length;
        if (it.Child('gift_be').active = !!giftCount) {
            it.Child('gift_be/count', cc.Label).string = giftCount > 1 ? 'x' + giftCount : '';
        }
    };
    // 添加好友 -----------------------------------------------------------------------------------------------------------------------------
    // 申请列表
    PersonalPnlCtrl.prototype.showApplys = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var list = this.friend.getApplys(), len = list.length;
        list.sort(function (a, b) { return b.time - a.time; });
        sv.node.Child('empty').active = !len;
        sv.List(len, function (it, i) {
            var data = it.Data = list[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            it.Child('time').setLocaleKey('ui.apply_friend_time', ut.dateFormat('MM-dd hh:mm:ss', data.time));
        });
        this.updateBlackListInfo(node);
    };
    PersonalPnlCtrl.prototype.updateBlackListInfo = function (node) {
        node = node || this.pagesNode_.Child(1);
        var len = this.friend.getBlacklists().length;
        node.Child('black_list_be', TextButtonCmpt_1.default).setKey('ui.button_blacklist', [len]);
    };
    // 黑名单
    PersonalPnlCtrl.prototype.showBlacklist = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(1);
        var sv = node.Component(cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var list = this.friend.getBlacklists(), len = list.length;
        list.sort(function (a, b) { return b.time - a.time; });
        node.Child('empty').active = !len;
        sv.List(len, function (it, i) {
            var data = it.Data = list[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            it.Child('time').setLocaleKey('ui.add_blacklist_time', ut.dateFormat('MM-dd hh:mm:ss', data.time));
        });
    };
    // 领取礼物
    PersonalPnlCtrl.prototype.claimFriendGift = function (friendUid, data) {
        return __awaiter(this, void 0, void 0, function () {
            var err;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.friend.claimFriendGift(friendUid, data.uid)];
                    case 1:
                        err = _a.sent();
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        ViewHelper_1.viewHelper.showPnl('menu/GiftBoxAnim', data.boxId, data.id);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 个人战绩 -----------------------------------------------------------------------------------------------------------------------------
    // 放弃本次对局
    PersonalPnlCtrl.prototype.giveupGame = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GiveupGame', {}, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.user.setSid(0);
                        this.user.setPlaySid(0);
                        this.user.addGiveupCount(1);
                        this.user.setRankScore(data.rankScore);
                        this.user.setPassNewbieIndex(data.passNewbieIndex || 0);
                        ViewHelper_1.viewHelper.gotoWind('lobby');
                        return [2 /*return*/];
                }
            });
        });
    };
    PersonalPnlCtrl.prototype.updateBattleRecordInfo = function (node) {
        var _a;
        node = node || this.pagesNode_.Child(2);
        var sid = this.user.getPlaySid(), overInfo = GameHelper_1.gameHpr.world.getGameOverInfo();
        var gameInfo = node.Child('game_info').Swih(!!sid || GameHelper_1.gameHpr.isNoviceMode ? 'info' : 'empty')[0];
        var sidNode = gameInfo.Child('play_sid'), layout = gameInfo.Child('layout'), onlineTime = gameInfo.Child('online_time'), serverTime = gameInfo.Child('server_time'), runTime = gameInfo.Child('run_time'), closeTime = gameInfo.Child('close_time');
        if (!!sid || GameHelper_1.gameHpr.isNoviceMode) {
            closeTime.active = !!overInfo;
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            sidNode.Child('val').setLocaleKey('ui.server_name_novice');
            gameInfo.Child('settle_be').active = false;
            gameInfo.Child('giveup_be').active = false;
            onlineTime.Child('lay/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToStringForHour(this.user.getSumOnlineTime()));
            serverTime.Child('lay/val', cc.LabelTimer).setEndTime(ut.MAX_VALUE).setFormat(function (time) { return ut.dateFormat('MM/dd hh:mm:ss', time * 1000); }).run(GameHelper_1.gameHpr.getServerNowTimeByZoneOffset() * 0.001);
            layout.Child('score/val').setLocaleKey('ui.cur_score', 0);
            layout.Child('ranking/val').setLocaleKey('ui.cur_ranking', 1);
            runTime.Child('lay/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToString(Date.now() - GameHelper_1.gameHpr.noviceServer.serverStartTime));
            var history = node.Child('records/history');
            history.Child('empty').active = true;
            history.Child('content').active = false;
            node.Child('records/title/count/val', cc.Label).string = '-';
        }
        else {
            if (!!sid) {
                var _b = GameHelper_1.gameHpr.getServerNameById(sid), key = _b.key, id = _b.id;
                sidNode.Child('val').setLocaleKey(key, id);
                var isYetSettle = !!((_a = GameHelper_1.gameHpr.getPlayerInfo(this.user.getUid())) === null || _a === void 0 ? void 0 : _a.isSettled);
                var showSettle = GameHelper_1.gameHpr.world.isKarmicMahjong() && GameHelper_1.gameHpr.player.isCapture() && this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.WAIT_SETTLE);
                if (closeTime.active) {
                    closeTime.setLocaleKey('ui.server_close_desc', GameHelper_1.gameHpr.millisecondToStringForDay(overInfo.closeTime));
                }
                gameInfo.Child('settle_info_be').active = !!overInfo && !GameHelper_1.gameHpr.isNoviceMode;
                gameInfo.Child('settle_be').active = !isYetSettle && showSettle && !overInfo && !GameHelper_1.gameHpr.isNoviceMode;
                gameInfo.Child('giveup_be').active = !isYetSettle && !showSettle && !overInfo && !GameHelper_1.gameHpr.isNoviceMode;
                onlineTime.Child('lay/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToStringForHour(this.user.getSumOnlineTime()));
                serverTime.Child('lay/val', cc.LabelTimer).setEndTime(ut.MAX_VALUE).setFormat(function (time) { return ut.dateFormat('MM/dd hh:mm:ss', time * 1000); }).run(GameHelper_1.gameHpr.getServerNowTimeByZoneOffset() * 0.001);
                this.showMeRankNo(layout, runTime);
            }
            this.showHistoryRecords(node);
        }
    };
    PersonalPnlCtrl.prototype.showMeRankNo = function (node, runTime) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, data, err, meInfo;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.rank.getPlayerScoreList(1)];
                    case 1:
                        _b.sent();
                        if (!node.isValid) return [3 /*break*/, 4];
                        if (!(mc.currWindName === 'lobby')) return [3 /*break*/, 3];
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_GetCurGameInfo', {})];
                    case 2:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('err')];
                        }
                        else {
                            node.Child('score/val').setLocaleKey('ui.cur_score', data.score);
                            node.Child('ranking/val').setLocaleKey('ui.cur_ranking', (data.rank < 0 ? '-' : (data.rank + 1) + ''));
                            runTime.Child('lay/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToString(data.runTime));
                        }
                        return [3 /*break*/, 4];
                    case 3:
                        meInfo = this.rank.getMeScoreInfo();
                        node.Child('score/val').setLocaleKey('ui.cur_score', (meInfo.landScore + meInfo.alliScore + meInfo.extraScore));
                        node.Child('ranking/val').setLocaleKey('ui.cur_ranking', (meInfo.no < 0 ? '-' : (meInfo.no + 1) + ''));
                        runTime.Child('lay/val').setLocaleKey('ui.text', GameHelper_1.gameHpr.millisecondToString(GameHelper_1.gameHpr.world.getServerRunTime()));
                        _b.label = 4;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 个人历史对局
    PersonalPnlCtrl.prototype.showHistoryRecords = function (node) {
        var _this = this;
        node = node || this.pagesNode_.Child(2);
        node.Child('records/title/count/val', cc.Label).string = this.user.getAccTotalGameCount() + '';
        var history = node.Child('records/history'), empty = history.Child('empty');
        empty.active = false;
        GameHelper_1.gameHpr.net.request('lobby/HD_GetGameRecordList', { page: 1, serverType: -1 }, true).then(function (_a) {
            var err = _a.err, data = _a.data;
            _this.gameRecordList = (data === null || data === void 0 ? void 0 : data.datas) || [];
            var len = _this.gameRecordList.length;
            empty.active = !len;
            var content = history.Child('content');
            content.active = !!len;
            content.Items(_this.gameRecordList.slice(0, 3), function (it, data) {
                it.Data = data;
                var _a = GameHelper_1.gameHpr.getServerNameById(data.sid), key = _a.key, id = _a.id;
                it.Child('name/val').setLocaleKey(key, id);
                it.Child('alliance/val', cc.Label).string = data.alliName || '-';
                ResHelper_1.resHelper.loadAlliIcon(data.alliHeadicon || 0, it.Child('alliance/icon'), _this.key);
                it.Child('time/val', cc.Label).string = ut.dateFormat('yyyy/MM/dd', data.startTime) + ' - ' + ut.dateFormat('yyyy/MM/dd', data.endTime);
                var isGiveupGame = data.rank === -2;
                // it.Child('detail_be').active = !isGiveupGame
                var layout = it.Child('layout').Swih(isGiveupGame ? 'giveup' : 'over')[0];
                if (!isGiveupGame) {
                    layout.Child('score/val', cc.Label).string = data.score;
                    layout.Child('ranking/val', cc.Label).string = data.rank >= 0 ? (data.rank + 1) : '-';
                }
                it.Child('heros').Items(data.useHero, function (it, data) {
                    ResHelper_1.resHelper.loadPawnHeadMiniIcon(data, it.Child('val'), _this.key);
                });
            });
        });
    };
    PersonalPnlCtrl = __decorate([
        ccclass
    ], PersonalPnlCtrl);
    return PersonalPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PersonalPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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