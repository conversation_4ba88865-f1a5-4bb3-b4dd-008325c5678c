
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/EditPawnPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '018c58oEwFBlrQ3aB2v5jZ4', 'EditPawnPnlCtrl');
// app/script/view/area/EditPawnPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var ccclass = cc._decorator.ccclass;
var EditPawnPnlCtrl = /** @class */ (function (_super) {
    __extends(EditPawnPnlCtrl, _super);
    function EditPawnPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.buttonsNode_ = null; // path://buttons_n
        _this.gatherBtn_ = null; // path://gather_be_b
        //@end
        _this.oPosition = cc.v2();
        return _this;
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    EditPawnPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.EDIT_PAWN_MOVEING] = this.onEditPawnMoveing, _a.enter = true, _a)
        ];
    };
    EditPawnPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                return [2 /*return*/];
            });
        });
    };
    EditPawnPnlCtrl.prototype.onEnter = function (data) {
        this.oPosition.set(data.getTempPosition());
        this.emit(EventType_1.default.CHANGE_SCREEN_UI, false);
    };
    EditPawnPnlCtrl.prototype.onRemove = function () {
        this.emit(EventType_1.default.CHANGE_SCREEN_UI, true);
    };
    EditPawnPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://buttons_n/ok_be
    EditPawnPnlCtrl.prototype.onClickOk = function (event, data) {
        this.emit(EventType_1.default.CLICK_EDIT_PAWN_MENU, 'ok');
    };
    // path://gather_be_b
    EditPawnPnlCtrl.prototype.onClickGather = function (event, data) {
        this.emit(EventType_1.default.CLICK_EDIT_PAWN_MENU, 'gather');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 士兵移动中
    EditPawnPnlCtrl.prototype.onEditPawnMoveing = function (val) {
        this.buttonsNode_.Swih(val ? 'moveing' : 'ok_be');
        this.gatherBtn_.interactable = !val;
    };
    EditPawnPnlCtrl = __decorate([
        ccclass
    ], EditPawnPnlCtrl);
    return EditPawnPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = EditPawnPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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