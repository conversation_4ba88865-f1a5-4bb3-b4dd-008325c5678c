
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaWatchChatCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2a4542cMXZIHrtSqe5JWbfP', 'AreaWatchChatCmpt');
// app/script/view/area/AreaWatchChatCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var AreaWatchChatCmpt = /** @class */ (function (_super) {
    __extends(AreaWatchChatCmpt, _super);
    function AreaWatchChatCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.MSG_MAX_COUNT = 3; //消息最多个数
        _this.OUT_OFFSET_X = 16; //在外面初始x的偏移
        _this.SPACING_Y = 68; //间隙
        _this.index = 0;
        _this.key = '';
        _this.chats = [];
        _this.item = null;
        _this.itemPool = []; //消息节点池子
        _this.items = []; //当前显示的消息节点
        return _this;
    }
    AreaWatchChatCmpt.prototype.onLoad = function () {
        this.item = this.FindChild('item');
        this.item.parent = null;
        this.itemPool.push(this.item);
    };
    AreaWatchChatCmpt.prototype.onDestroy = function () {
        while (this.itemPool.length > 0) {
            this.itemPool.pop().destroy();
        }
        ut.destroyNode(this.item);
        this.item = null;
    };
    AreaWatchChatCmpt.prototype.init = function (index) {
        this.index = index;
        this.key = 'AreaWatchChatCmpt_' + index;
        this.clean();
        eventCenter.off(EventType_1.default.ADD_AREA_CHAT, this.onAddChat, this);
        eventCenter.on(EventType_1.default.ADD_AREA_CHAT, this.onAddChat, this);
    };
    AreaWatchChatCmpt.prototype.clean = function () {
        while (this.items.length > 0) {
            this.putItemOne(this.items.pop());
        }
        this.node.removeAllChildren();
        assetsMgr.releaseTempResByTag(this.key);
    };
    // 添加一个消息
    AreaWatchChatCmpt.prototype.onAddChat = function (index, msg) {
        if (this.index !== index) {
            return;
        }
        var info = GameHelper_1.gameHpr.getPlayerInfo(msg.uid);
        if (info) {
            this.chats.push({
                headIcon: info.headIcon,
                duration: 5,
                emoji: msg.emoji,
            });
            if (this.items.length >= this.MSG_MAX_COUNT) {
                var data = this.items[0].Data;
                if (data && data.duration > 0.5) {
                    data.duration = 0.5;
                }
            }
        }
    };
    AreaWatchChatCmpt.prototype.getItem = function () {
        var node = this.itemPool.pop() || cc.instantiate(this.item);
        node.parent = this.node;
        node.active = true;
        node.stopAllActions();
        node.opacity = 255;
        node.scaleY = 1;
        return node;
    };
    AreaWatchChatCmpt.prototype.putItemOne = function (node) {
        node.parent = null;
        node.Data = null;
        this.itemPool.push(node);
    };
    // 显示一条信息
    AreaWatchChatCmpt.prototype.show = function (data) {
        var _a;
        // audioMgr.playSFX('common/sound_ui_010')
        var it = this.getItem();
        it.Data = data;
        ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val'), data.headIcon, this.key);
        var scale = ((_a = assetsMgr.getJsonData('chatEmoji', data.emoji)) === null || _a === void 0 ? void 0 : _a.scale) || 0;
        ResHelper_1.resHelper.loadEmojiNode(data.emoji, it.Child('icon/root'), scale * -1, this.key, true);
        var cnt = this.items.length;
        it.setPosition(-(it.width + this.OUT_OFFSET_X), -(cnt >= this.MSG_MAX_COUNT ? cnt - 1 : cnt) * this.SPACING_Y);
        // 出来
        this.moveIn(it);
        // 刷新其他的
        if (this.items.length >= this.MSG_MAX_COUNT) {
            // 第一个移除去
            this.moveOut(this.items.shift());
        }
        this.items.push(it);
    };
    // 进来
    AreaWatchChatCmpt.prototype.moveIn = function (it) {
        it.stopAllActions();
        cc.tween(it).to(0.2, { x: 0 }, { easing: cc.easing.sineOut }).start();
    };
    // 出去
    AreaWatchChatCmpt.prototype.moveOut = function (it) {
        var _this = this;
        it.stopAllActions();
        var outX = -(it.width + this.OUT_OFFSET_X);
        cc.tween(it).to(0.2, { x: outX, opacity: 0 }, { easing: cc.easing.sineIn }).call(function () { return _this.putItemOne(it); }).start();
        // 往上移
        this.items.forEach(function (m, i) { return _this.moveY(m, i); });
    };
    // 向上移动
    AreaWatchChatCmpt.prototype.moveY = function (it, i) {
        var endY = -(i * this.SPACING_Y);
        if (it.y !== endY) {
            it.stopAllActions();
            cc.tween(it).to(0.2, { y: endY }).start();
        }
    };
    AreaWatchChatCmpt.prototype.update = function (dt) {
        for (var i = this.items.length - 1; i >= 0; i--) {
            var it = this.items[i], data = it.Data;
            if (data) {
                data.duration -= dt;
                if (data.duration > 0) {
                    continue;
                }
            }
            this.items.splice(i, 1);
            this.moveOut(it);
        }
        if (this.items.length < this.MSG_MAX_COUNT && this.chats.length > 0) {
            this.show(this.chats.shift());
        }
    };
    AreaWatchChatCmpt = __decorate([
        ccclass
    ], AreaWatchChatCmpt);
    return AreaWatchChatCmpt;
}(cc.Component));
exports.default = AreaWatchChatCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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