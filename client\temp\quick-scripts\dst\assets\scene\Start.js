
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scene/Start.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '003a8vdWWRCFpeVpn51vGdR', 'Start');
// scene/Start.ts

"use strict";
//这个场景是启动页，不要引用任何模块代码，只能使用基础的引擎代码
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var version_1 = require("./version");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 游戏入口
var Start = /** @class */ (function (_super) {
    __extends(Start, _super);
    function Start() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    // 获取是否正式版
    Start.prototype.isRelease = function () {
        var _a, _b;
        if (typeof wx === 'undefined') {
            return !cc.sys.isBrowser;
        }
        else if (!wx['getAccountInfoSync']) {
            return false;
        }
        else if (cc.sys.isBrowser) {
            return false;
        }
        var version = (_b = (_a = wx.getAccountInfoSync()) === null || _a === void 0 ? void 0 : _a.miniProgram) === null || _b === void 0 ? void 0 : _b.envVersion;
        // console.log('envVersion=' + version)
        return version === 'release';
    };
    Start.prototype.hasToken = function () {
        var token = '';
        try {
            token = localStorage.getItem('slg_account_token');
        }
        catch (error) {
            console.log(error);
        }
        return !!token;
    };
    Start.prototype.onLoad = function () {
        var _a;
        cc.macro.ENABLE_MULTI_TOUCH = true;
        cc.debug.setDisplayStats(false);
        cc.dynamicAtlasManager.enabled = false;
        cc.macro.SUPPORT_TEXTURE_FORMATS = ['.png', '.jpg', '.jpeg', '.pkm', '.pvr', '.webp', '.bmp']; //优化纹理读取顺序
        if (this.isRelease() && !this.hasToken()) {
            var ta = window['ta_twomiles'] = version_1.default.newThinkingAnalyticsAPI();
            ta.track('ta_tutorial_v2', {
                'tutorial_step': '0-0',
                'os': ((_a = ta.getPresetProperties()) === null || _a === void 0 ? void 0 : _a.os) || 'none',
            }, new Date(), function (res) {
                // console.log(res)
            });
        }
        if (cc.sys.isBrowser && !cc.sys.isMobile) {
            cc.assetManager.downloader.maxConcurrency = 30;
        }
    };
    Start.prototype.start = function () {
        this.load();
    };
    Start.prototype.load = function () {
        return __awaiter(this, void 0, void 0, function () {
            var logoNode, isInland, now, volData, vol, logoSound;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        logoNode = this.node.getChildByName('Logo');
                        isInland = version_1.default.getAppType() === 'inland';
                        logoNode.getChildByName('game_tip').active = isInland;
                        logoNode.getChildByName('age_tip').active = isInland;
                        logoNode.getChildByName('cocos').active = !isInland;
                        now = Date.now();
                        return [4 /*yield*/, Promise.all([this.loadBundleByName('app'), this.loadBundleByName('manifest')])];
                    case 1:
                        _a.sent();
                        volData = localStorage.getItem('slg_sfx_volume');
                        vol = volData !== null ? volData : "1";
                        if (vol === "1") {
                            logoSound = this.getComponent(cc.AudioSource);
                            logoSound.play();
                        }
                        return [4 /*yield*/, this.showLogo(logoNode, now)];
                    case 2:
                        _a.sent();
                        cc.director.loadScene('app');
                        return [2 /*return*/];
                }
            });
        });
    };
    Start.prototype.showLogo = function (logoNode, now) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        var delay = Math.max(1.4 - (Date.now() - now) * 0.001, 0);
                        cc.tween(logoNode)
                            .delay(delay)
                            .to(0.4, { opacity: 0 })
                            .call(resolve)
                            .start();
                    })];
            });
        });
    };
    // 加载Bundle
    Start.prototype.loadBundleByName = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            var success;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!true) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.loadBundle(name)];
                    case 1:
                        success = _a.sent();
                        if (success) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 0];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    Start.prototype.loadBundle = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        cc.assetManager.loadBundle(name, function (err, bundle) {
                            if (err) {
                                console.error('loadBundleError: ', name, err);
                            }
                            resolve(!err);
                        });
                    })];
            });
        });
    };
    Start = __decorate([
        ccclass
    ], Start);
    return Start;
}(cc.Component));
exports.default = Start;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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