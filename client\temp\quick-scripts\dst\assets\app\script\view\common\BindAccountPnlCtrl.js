
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/BindAccountPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c4ab0xmuahGqJRgYkjVePlD', 'BindAccountPnlCtrl');
// app/script/view/common/BindAccountPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var TaHelper_1 = require("../../common/helper/TaHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BindAccountPnlCtrl = /** @class */ (function (_super) {
    __extends(BindAccountPnlCtrl, _super);
    function BindAccountPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.model = null;
        return _this;
    }
    BindAccountPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BindAccountPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('login');
                return [2 /*return*/];
            });
        });
    };
    BindAccountPnlCtrl.prototype.onEnter = function (data) {
        var isGLobal = GameHelper_1.gameHpr.isGLobal(), isMobile = ut.isMobile(), isAndroid = ut.isAndroid(), isIos = ut.isIos();
        this.buttonsNode_.Child('wx_login_be').active = false; //微信
        this.buttonsNode_.Child('facebook_login_be').active = isGLobal && isMobile; //facebook 只要是海外就显示
        this.buttonsNode_.Child('apple_login_be').active = isGLobal && isIos; //apple 只有苹果手机显示
        this.buttonsNode_.Child('google_login_be').active = isGLobal && (isAndroid || isIos); //google 只有安卓手机显示
    };
    BindAccountPnlCtrl.prototype.onRemove = function () {
    };
    BindAccountPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/wx_login_be
    BindAccountPnlCtrl.prototype.onClickWxLogin = function (event, data) {
    };
    // path://root/buttons_n/facebook_login_be
    BindAccountPnlCtrl.prototype.onClickFacebookLogin = function (event, data) {
        var _this = this;
        this.model.facebookBind().then(function (err) { return _this.bindRet(err); });
    };
    // path://root/buttons_n/apple_login_be
    BindAccountPnlCtrl.prototype.onClickAppleLogin = function (event, data) {
        var _this = this;
        this.model.appleBind().then(function (err) { return _this.bindRet(err); });
    };
    // path://root/buttons_n/google_login_be
    BindAccountPnlCtrl.prototype.onClickGoogleLogin = function (event, data) {
        var _this = this;
        this.model.googleBind().then(function (err) { return _this.bindRet(err); });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 绑定结束处理
    BindAccountPnlCtrl.prototype.bindRet = function (err) {
        if (err === ECode_1.ecode.ACCOUNT_ALREADY_BIND) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.account_yet_exist', {
                okText: 'ui.button_goto_login',
                ok: function () { return GameHelper_1.gameHpr.changeAccount(); },
                cancel: function () { },
            });
        }
        else if (err) {
            return ViewHelper_1.viewHelper.showAlert(err);
        }
        else if (this.isValid) {
            this.hide();
        }
        eventCenter.emit(EventType_1.default.BIND_ACCOUNT_SUCCEED);
        ViewHelper_1.viewHelper.showAlert('toast.bind_account_succeed');
        TaHelper_1.taHelper.track('ta_bindAccount', { type: GameHelper_1.gameHpr.user.getLoginType() });
    };
    BindAccountPnlCtrl = __decorate([
        ccclass
    ], BindAccountPnlCtrl);
    return BindAccountPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BindAccountPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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