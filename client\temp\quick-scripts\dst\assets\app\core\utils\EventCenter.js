
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/utils/EventCenter.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5f069Xpi9JJU7ik7L7yBfqC', 'EventCenter');
// app/core/utils/EventCenter.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * 全局事件驱动
 */
var EventCenter = /** @class */ (function () {
    function EventCenter() {
        this.__events = new Map(); // 事件列表
        this.__emit_types = [];
        this.__off_types = [];
    }
    /**
     * 派发事件
     * @param  type
     * @param  data
     */
    EventCenter.prototype.emit = function (type) {
        var _a;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        type = String(type);
        var list = this.__events.get(type);
        if (!list || list.length === 0) {
            return;
        }
        this.__emit_types.push(type);
        for (var i = 0, l = list.length; i < l; i++) {
            var it = list[i];
            it && (_a = it.callback).call.apply(_a, __spread([it.target], params));
        }
        // 清理为null的
        if (this.__off_types.length > 0 && this.__off_types.remove(type)) {
            for (var i = list.length - 1; i >= 0; i--) {
                if (!list[i]) {
                    list.splice(i, 1);
                }
            }
            if (list.length === 0) {
                this.__events.delete(type);
            }
        }
        this.__emit_types.remove(type);
    };
    /**
     * 请求事件
     * @param type
     * @param params
     */
    EventCenter.prototype.get = function (type) {
        var _a;
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        var list = this.__events.get(String(type));
        if (!list || list.length === 0) {
            return null;
        }
        if (list.length > 1) {
            logger.error('req event length > 1, the default index is 0, type=' + type);
        }
        var it = list[0];
        if (!it) {
            return null;
        }
        return (_a = it.callback).call.apply(_a, __spread([it.target], params));
    };
    /**
     * 异步 请求事件
     * @param type
     */
    EventCenter.prototype.req = function (type) {
        var params = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            params[_i - 1] = arguments[_i];
        }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.get.apply(this, __spread([type], params))];
            });
        });
    };
    /**
     * 监听事件
     * @param  type
     * @param  callback
     * @param  target
     */
    EventCenter.prototype.on = function (type, callback, target) {
        type = String(type);
        var list = this.__events.get(type);
        if (!list) {
            list = [];
            this.__events.set(type, list);
        }
        if (list.some(function (m) { return m && m.callback.toString() == callback.toString() && m.target == target; })) {
            return logger.error('repeat listen', type, (target ? target.name : 'null') + '.' + callback.name);
        }
        list.push({ callback: callback, target: target });
    };
    /**
     * 监听一次
     * @param type
     * @param callback
     * @param target
     */
    EventCenter.prototype.once = function (type, callback, target) {
        type = String(type);
        var fired = false, self = this;
        function g() {
            var params = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                params[_i] = arguments[_i];
            }
            self.off(type, g);
            if (!fired) {
                fired = true;
                callback.call.apply(callback, __spread([target], params));
            }
        }
        this.on(type, g);
    };
    /**
     * 异步等待
     * @param type
     */
    EventCenter.prototype.wait = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                type = String(type);
                this.off(type);
                return [2 /*return*/, new Promise(function (resolve) {
                        var fired = false, self = _this;
                        function g(params) {
                            self.off(type, g);
                            if (!fired) {
                                fired = true;
                                resolve(params);
                            }
                        }
                        _this.on(type, g);
                    })];
            });
        });
    };
    /**
     * 删除一个事件
     * @param  type
     * @param  callback
     * @param  target
     */
    EventCenter.prototype.off = function (type, callback, target) {
        type = String(type);
        var list = this.__events.get(type);
        if (!list) {
            return;
        }
        if (!callback) {
            this.__events.delete(type);
            return;
        }
        for (var i = list.length - 1; i >= 0; i--) {
            var it = list[i];
            if (it && it.target == target && it.callback.toString() == callback.toString()) {
                if (this.__emit_types.has(type)) {
                    list[i] = null;
                    this.__off_types.push(type);
                    // logger.error('try at emit in off type=' + type)
                }
                else {
                    list.splice(i, 1);
                }
                break;
            }
        }
        if (list.length === 0) {
            this.__events.delete(type);
        }
    };
    // 打印监听列表
    EventCenter.prototype.print = function () {
        logger.info('events:', this.__events);
    };
    EventCenter.prototype.clean = function () {
        this.__events.clear();
    };
    return EventCenter;
}());
exports.default = EventCenter;
window['eventCenter'] = new EventCenter();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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