
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendAnimation.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '021836F5Q5NLI7eb5cdYCT5', 'ExtendAnimation');
// app/core/extend/ExtendAnimation.ts

/**
 * Animation扩展方法
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
cc.Animation.prototype.playAsync = function (name, startTime) {
    return __awaiter(this, void 0, void 0, function () {
        var _this = this;
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve) {
                    _this.once('finished', resolve);
                    _this.play(name, startTime);
                })];
        });
    });
};
cc.Animation.prototype.playToFinished = function (callback, name) {
    this.off('finished');
    this.once('finished', callback);
    return this.play(name);
};
cc.Animation.prototype.setCompleteListener = function (callback) {
    this.off('finished');
    callback && this.on('finished', callback);
};
cc.Animation.prototype.reset = function (name) {
    name && this.play(name);
    this.setCurrentTime(0);
    this.stop();
};
sp.Skeleton.prototype.playAsync = function (name) {
    var _this = this;
    return new Promise(function (resolve) {
        _this.setCompleteListener(function () {
            _this.isValid && _this.setCompleteListener(null);
            resolve();
        });
        _this.animation = name;
    });
};
sp.Skeleton.prototype.play = function (name) {
    this.animation = name;
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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