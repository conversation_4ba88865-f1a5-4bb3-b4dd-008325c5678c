
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/PortrayalInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c97c97Fj9RAsL8e3mWC7K9y', 'PortrayalInfo');
// app/script/model/common/PortrayalInfo.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var PortrayalSkillObj_1 = require("./PortrayalSkillObj");
var StrategyObj_1 = require("./StrategyObj");
// 画像信息
var PortrayalInfo = /** @class */ (function () {
    function PortrayalInfo() {
        this.id = 0;
        this.attrs = [];
        // public lastAttrs: AttrArrayInfo[] = []
        this.recompCount = 0; //重新合成次数
        this.debris = 0; //拥有的碎片数量
        this.storeSlots = []; //保存槽位
        this.historyAttrs = []; //历史属性
        this.index = 0; //获取顺序
        this.json = null;
        this.attack = 0;
        this.hp = 0;
        this.skill = null; //技能
        this.strategys = []; //韬略列表
        this.mainAttrs = []; //主属性 用于显示
        this.showBoxOffset = cc.v2(); //显示偏移
        this.showSSROffset = cc.v2(); //显示偏移
        this.moveVelocity = 0; //移动速度
        this.tempDebris = 0; //临时的碎片用于合成的时候显示
    }
    PortrayalInfo.prototype.init = function (id, json) {
        if (json) {
            this.id = id;
            this.initJson(json);
        }
        else {
            this.updateBaseJson(id);
        }
        return this;
    };
    PortrayalInfo.prototype.fromSvr = function (data) {
        this.updateBaseJson(data.id);
        this.updateInfo(data);
        return this;
    };
    PortrayalInfo.prototype.updateInfo = function (data) {
        this.setAttr(data.attrs);
        this.recompCount = data.recompCount || 0;
        this.debris = data.debris || 0;
        this.storeSlots = data.slots || [];
        this.historyAttrs = data.history || [];
    };
    PortrayalInfo.prototype.fromChat = function (str) {
        str = str.replace('@portrayal{', '');
        str = str.replace('}@end', '');
        var _a = __read(str.split(':'), 2), id = _a[0], s = _a[1];
        var attrs = s.split('|').map(function (m) { return { attr: ut.stringToNumbers(m, ',') }; });
        this.updateBaseJson(Number(id));
        if (!this.json) {
            return null;
        }
        this.setAttr(attrs);
        return this;
    };
    // 包装聊天信息
    PortrayalInfo.prototype.toChatString = function () {
        return "@portrayal{" + this.id + ":" + this.attrs.join2(function (m) { var _a; return (_a = m.attr) === null || _a === void 0 ? void 0 : _a.join(','); }, '|') + "}@end";
    };
    PortrayalInfo.prototype.strip = function () {
        return { id: this.id, attrs: ut.deepClone(this.attrs) };
    };
    PortrayalInfo.prototype.clone = function () {
        return new PortrayalInfo().fromSvr(this.strip());
    };
    Object.defineProperty(PortrayalInfo.prototype, "name", {
        get: function () { return 'portrayalText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalInfo.prototype, "avatarPawn", {
        get: function () { var _a; return (_a = this.json) === null || _a === void 0 ? void 0 : _a.avatar_pawn; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalInfo.prototype, "avatarPawnName", {
        get: function () { return 'pawnText.name_' + this.avatarPawn; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PortrayalInfo.prototype, "hasAnim", {
        get: function () { var _a; return !!((_a = this.json) === null || _a === void 0 ? void 0 : _a.has_anim); },
        enumerable: false,
        configurable: true
    });
    // 获取用于聊天的名字
    PortrayalInfo.prototype.getChatName = function () {
        if (this.isChosenOne()) {
            return assetsMgr.lang('ui.smelting_equip_name_1', this.name);
        }
        return assetsMgr.lang(this.name);
    };
    PortrayalInfo.prototype.getChatNameColor = function () {
        return this.isChosenOne() ? '#D13D31' : '#FF8000';
    };
    // 副名
    PortrayalInfo.prototype.getViceName = function () {
        if (assetsMgr.getJsonData('portrayalText', 'vice_' + this.id)) {
            return 'portrayalText.vice_' + this.id;
        }
        return '';
    };
    PortrayalInfo.prototype.isUnlock = function () {
        var _a;
        return !!((_a = this.attrs) === null || _a === void 0 ? void 0 : _a.length);
    };
    // 获取ui地板类型
    PortrayalInfo.prototype.getUIDiType = function () {
        if (!this.isUnlock()) {
            return 0;
        }
        return 1;
    };
    PortrayalInfo.prototype.isCanComp = function () {
        return this.debris >= Constant_1.PORTRAYAL_COMP_NEED_COUNT;
    };
    PortrayalInfo.prototype.updateBaseJson = function (id) {
        this.id = id;
        this.initJson(assetsMgr.getJsonData('portrayalBase', this.id));
    };
    PortrayalInfo.prototype.initJson = function (json) {
        var _a;
        this.json = json;
        if (json) {
            this.showBoxOffset = ut.stringToVec2(this.json.ui_offset);
            this.showSSROffset = ut.stringToVec2(this.json.ui_ssr_offset);
            this.moveVelocity = this.json.velocity || 100;
            this.pawnType = ((_a = assetsMgr.getJsonData('pawnBase', this.avatarPawn)) === null || _a === void 0 ? void 0 : _a.type) || 0;
        }
    };
    PortrayalInfo.prototype.setAttr = function (attrs) {
        this.attrs = (attrs || []).map(function (m) { return Array.isArray(m) ? { attr: m } : m; });
        this.updateAttr();
    };
    PortrayalInfo.prototype.updateAttr = function () {
        var _this = this;
        this.attack = 0;
        this.hp = 0;
        this.mainAttrs = [];
        this.skill = null;
        this.strategys = [];
        this.attrs.forEach(function (m) {
            var _a = __read(m.attr, 3), fieldType = _a[0], type = _a[1], value = _a[2];
            if (fieldType === 0) { //属性
                if (type === 1) {
                    _this.hp += value;
                }
                else if (type === 2) {
                    _this.attack += value;
                }
                _this.mainAttrs.push({ type: type, value: value });
            }
            else if (fieldType === 1) { //技能
                _this.skill = new PortrayalSkillObj_1.default().init(type, value);
            }
            else if (fieldType === 2) { //韬略
                _this.strategys.push(new StrategyObj_1.default().init(type));
            }
        });
    };
    // 是否天选
    PortrayalInfo.prototype.isChosenOne = function () {
        var _a;
        if (!this.json) {
            return false;
        }
        else if (!this.isUnlock()) {
            return false;
        }
        else if (this.hp !== ut.stringToNumbers(this.json.hp, ',')[1]) {
            return false;
        }
        else if (this.attack !== ut.stringToNumbers(this.json.attack, ',')[1]) {
            return false;
        }
        // 技能
        return !!((_a = this.skill) === null || _a === void 0 ? void 0 : _a.isChosenOne());
    };
    // 天选几率
    PortrayalInfo.prototype.getChosenOneOdds = function () {
        var cnt = this.isUnlock() ? this.recompCount + 1 : 0;
        return parseFloat((Constant_1.PORTRAYAL_CHOSENONE_ODDS * cnt).toFixed(3));
    };
    return PortrayalInfo;
}());
exports.default = PortrayalInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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