
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/ECode.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9ad48J4la5DEriPnFFxZjwN', 'ECode');
// app/script/common/constant/ECode.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ecode = void 0;
// 服务器返回错误码
exports.ecode = {
    UNKNOWN: 'ecode.500000',
    NOT_ACCOUNT_TOKEN: 'ecode.500001',
    TOKEN_INVALID: 'ecode.500002',
    NOT_BIND_UID: 'ecode.500003',
    ROOM_CLOSE: 'ecode.500004',
    ROOM_NOT_EXIST: 'ecode.500006',
    PLAYER_NOT_EXIST: 'ecode.500007',
    POINT_OCCUPIED: 'ecode.500010',
    ARMY_NOT_EXIST: 'ecode.500011',
    RES_NOT_ENOUGH: 'ecode.500012',
    YET_IN_BTQUEUE: 'ecode.500013',
    YET_MAXLV: 'ecode.500015',
    PAWN_NOT_EXIST: 'ecode.500017',
    ARMY_PAWN_FULL: 'ecode.500019',
    APPLY_ALLIANCE_TOOMANY: 'ecode.500024',
    NOT_OPERATING_AUTH: 'ecode.500025',
    YET_CANCEL_APPLY: 'ecode.500026',
    YET_JOIN_ALLIANCE: 'ecode.500027',
    MERCHANT_NOT_ENOUGH: 'ecode.500029',
    PLEASE_INPUT_RES_COUNT: 'ecode.500030',
    HAS_ONE_BUILD_NOT_MAXLV: 'ecode.500034',
    AVOID_WAR_NOT_ATTACK: 'ecode.500035',
    BATTLEING: 'ecode.500036',
    ONLY_ATTACK_ADJOIN_CELL: 'ecode.500038',
    PLEASE_INPUT_TITLE: 'ecode.500045',
    PLEASE_INPUT_RECEIVER: 'ecode.500046',
    PLEASE_INPUT_CONTENT: 'ecode.500047',
    TEXT_LEN_LIMIT: 'ecode.500049',
    TEXT_HAS_SENSITIVE: 'ecode.500050',
    NOT_CITY_INDEX: 'ecode.500052',
    GOLD_NOT_ENOUGH: 'ecode.500053',
    PLAYER_FULL_ARMY: 'ecode.500054',
    FORGE_EQUIPING: 'ecode.500058',
    EQUIP_NOT_EXIST: 'ecode.500059',
    VERSION_TOOLOW: 'ecode.500060',
    NAME_EXIST: 'ecode.500061',
    PLEASE_INPUT_EXCHANGE_CODE: 'ecode.500068',
    TEXT_HAS_SPECIAL_SYMBOL: 'ecode.500070',
    ROOM_FULL: 'ecode.500073',
    ROOM_OVER: 'ecode.500074',
    PLEASE_FITON_POLICY: 'ecode.500075',
    TREASURE_YET_OPEN: 'ecode.500077',
    TREASURE_NOT_OPEN: 'ecode.500078',
    JOIN_ALLI_TIME_TOO_SHORT: 'ecode.500083',
    NOT_IN_OCCUPY_TIME: 'ecode.500085',
    NEED_STUDY_PER_SLOT: 'ecode.500088',
    NOT_OPEN_OTHER_TREASURE: 'ecode.500090',
    NOT_CAN_RESTORE_ATTR: 'ecode.500091',
    ORDER_VERIFY_API_ERROR: 'ecode.500119',
    PAY_FAIL: 'ecode.500120',
    ACCOUNT_ALREADY_BIND: 'ecode.500121',
    VERSION_TOOTALL: 'ecode.500122',
    NAME_IS_NULL: 'ecode.500123',
    CHAT_LAND_COUNT_NOT_ENOUGH: 'ecode.500127',
    GUEST_NOT_LOGOUT: 'ecode.500128',
    LOGOUT_WAIT_TIME_UNMET: 'ecode.500129',
    CANT_ENTER_ROOM: 'ecode.500131',
    ALLI_JOB_FULL: 'ecode.500132',
    SERVER_APPLY_REPEAT: 'ecode.500133',
    CELL_PROTECT: 'ecode.500134',
    SERVER_NOT_APPLY: 'ecode.500135',
    SERVER_APPLY_FINISHED: 'ecode.500136',
    CONCURRENT_GAME_LIMIT: 'ecode.500137',
    YET_GIVE_GAME: 'ecode.500139',
    SUBSCRIPTION_TIMEOUT: 'ecode.500142',
    BAN_OPT: 'ecode.500143',
    ORDER_FINISHED: 'ecode.500144',
    ORDER_REFUNDED: 'ecode.500145',
    ORDER_VERIFY_REPEAT: 'ecode.500146',
    OCCUPY_CELL_DIS_COND: 'ecode.500150',
    ALREADY_FRIENDS: 'ecode.500151',
    NOT_APPLY_FRIEND: 'ecode.500153',
    FRIEND_APPLY_LAND_LIMIT: 'ecode.500158',
    FRIEND_NOT_GUEST: 'ecode.500159',
    ONLY_MY_CELL_SEND_EMOJI: 'ecode.500163',
    NEED_PAWN_SET_MARCHSPEED: 'ecode.500166',
    IN_SMELTING: 'ecode.500168',
    NOT_SELECT_EQUIP: 'ecode.500169',
    CANNOT_SMELTING: 'ecode.500170',
    ANCIETN_PAUSE: 'ecode.500174',
    ANCIETN_CONTRIBUTE_RES_LIMIT: 'ecode.500176',
    CANT_LEAVE_ALLI_WITH_ANCIENT: 'ecode.500177',
    ANCIENT_CONTRIBUTE_COUNT_LIMIT: 'ecode.500178',
    TEAM_NOT_EXIST: 'ecode.500179',
    USER_ALREADY_IN_GAME: 'ecode.500181',
    USER_ALREADY_IN_TEAM: 'ecode.500182',
    IN_GAME: 'ecode.500184',
    IN_APPLY: 'ecode.500185',
    NOVICE_PLAYER: 'ecode.500185',
    INGOT_NOT_ENOUGH: 'ecode.500189',
    WAR_TOKEN_NOT_ENOUGH: 'ecode.500192',
    DEBRIS_NOT_ENOUGH: 'ecode.500193',
    ONLY_ALLI_GIVE: 'ecode.500195',
    LOBBY_QUEUE_UP: 'ecode.500197',
    ARMY_ONLY_AVATAR_ONE: 'ecode.500202',
    HERO_YET_DIE: 'ecode.500203',
    HERO_YET_AVATAR: 'ecode.500204',
    CUR_LOBBY_FULL: 'ecode.500206',
    MAP_MARK_COUNT_LIMIT: 'ecode.500207',
    NOT_IN_OCCUPY_ANCIENT_TIME: 'ecode.500212',
    RANK_COIN_NOT_ENOUGH: 'ecode.500213',
    TODAY_YET_WATERING: 'ecode.500216',
    ALLI_CREATOR_CANT_GIVE_UP: 'ecode.500218',
    NOT_CAN_RESTORE_PORTRAYAL: 'ecode.500219',
    ANTI_CHEAT: 'ecode.500221',
    BAN_ACCOUNT: 'ecode.500222',
    BATTLE_PASS_TIMEOUT: 'ecode.500223',
    BATTLE_PASS_BUY_SCORE_LIMIT: 'ecode.500226',
    ALLI_CHAT_CHANNEL_LIMIT: 'ecode.500229',
    ANCIENT_CONTRIBUTE_CD: 'ecode.500230',
    CELL_TONDEN_LIMIT: 'ecode.500231',
    CELL_TONDENING: 'ecode.500232',
    STAMINA_NOT_ENOUGH: 'ecode.500233',
    CURING_QUEUE_FULL: 'ecode.500234',
    PORTRAYAL_SLOT_NOT_EXIST: 'ecode.500235',
    PORTRAYAL_SLOT_LIMIT: 'ecode.500236',
    SMELTING_NO_FORGE: 'ecode.500237',
    ALLI_VOTE_LIMIT: 'ecode.500242',
    ALREADY_IN_CUSTOM_ROOM: 'ecode.500243',
    CUSTOM_ROOM_FULL: 'ecode.500244',
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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