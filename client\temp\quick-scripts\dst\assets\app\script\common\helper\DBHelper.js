
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/DBHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1492mRDt9OmLGfd6Q/DUn2', 'DBHelper');
// app/script/common/helper/DBHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("./GameHelper");
var DBHelper = /** @class */ (function () {
    function DBHelper() {
    }
    // 获取建筑属性
    DBHelper.prototype.buildAttr = function (bid, lv) {
        return assetsMgr.getJsonData('buildAttr', bid * 1000 + lv);
    };
    DBHelper.prototype.buildEffectDelta = function (bid, lv0, lv1, def) {
        var _a, _b, _c, _d, _e, _f;
        if (def === void 0) { def = 0; }
        var buildAttr0 = this.buildAttr(bid, lv0);
        var effects0 = (_c = (buildAttr0 ? (_b = (_a = GameHelper_1.gameHpr.stringToCEffects(buildAttr0.effects)) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.value : def)) !== null && _c !== void 0 ? _c : def;
        var buildAttr1 = this.buildAttr(bid, lv1);
        var effects1 = (_f = (buildAttr1 ? (_e = (_d = GameHelper_1.gameHpr.stringToCEffects(buildAttr1.effects)) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.value : def)) !== null && _f !== void 0 ? _f : def;
        return effects1 - effects0;
    };
    return DBHelper;
}());
exports.default = new DBHelper;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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