
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceHeroSlotObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '538aaZj8dZIzKWBE9Zmgl3+', 'NoviceHeroSlotObj');
// app/script/model/guide/NoviceHeroSlotObj.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var HeroSlotObj_1 = require("../main/HeroSlotObj");
// 装备槽位
var NoviceHeroSlotObj = /** @class */ (function (_super) {
    __extends(NoviceHeroSlotObj, _super);
    function NoviceHeroSlotObj() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NoviceHeroSlotObj.prototype.strip = function () {
        var _a;
        return {
            lv: this.lv,
            avatarArmyUID: this.avatarArmyUID,
            reviveSurplusTime: this.reviveSurplusTime,
            getTime: this.getTime,
            hero: (_a = this.hero) === null || _a === void 0 ? void 0 : _a.strip(),
        };
    };
    NoviceHeroSlotObj.prototype.toDB = function () {
        var _a;
        return {
            lv: this.lv,
            avatarArmyUID: this.avatarArmyUID,
            reviveSurplusTime: this.reviveSurplusTime,
            getTime: this.getTime,
            hero: (_a = this.hero) === null || _a === void 0 ? void 0 : _a.strip(),
        };
    };
    return NoviceHeroSlotObj;
}(HeroSlotObj_1.default));
exports.default = NoviceHeroSlotObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxndWlkZVxcTm92aWNlSGVyb1Nsb3RPYmoudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsbURBQTZDO0FBRTdDLE9BQU87QUFDUDtJQUErQyxxQ0FBVztJQUExRDs7SUFxQkEsQ0FBQztJQW5CVSxpQ0FBSyxHQUFaOztRQUNJLE9BQU87WUFDSCxFQUFFLEVBQUUsSUFBSSxDQUFDLEVBQUU7WUFDWCxhQUFhLEVBQUUsSUFBSSxDQUFDLGFBQWE7WUFDakMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLGlCQUFpQjtZQUN6QyxPQUFPLEVBQUUsSUFBSSxDQUFDLE9BQU87WUFDckIsSUFBSSxRQUFFLElBQUksQ0FBQyxJQUFJLDBDQUFFLEtBQUssRUFBRTtTQUMzQixDQUFBO0lBQ0wsQ0FBQztJQUVNLGdDQUFJLEdBQVg7O1FBQ0ksT0FBTztZQUNILEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRTtZQUNYLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO1lBQ3pDLE9BQU8sRUFBRSxJQUFJLENBQUMsT0FBTztZQUNyQixJQUFJLFFBQUUsSUFBSSxDQUFDLElBQUksMENBQUUsS0FBSyxFQUFFO1NBQzNCLENBQUE7SUFDTCxDQUFDO0lBQ0wsd0JBQUM7QUFBRCxDQXJCQSxBQXFCQyxDQXJCOEMscUJBQVcsR0FxQnpEIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlcm9TbG90T2JqIGZyb20gXCIuLi9tYWluL0hlcm9TbG90T2JqXCJcblxuLy8g6KOF5aSH5qe95L2NXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBOb3ZpY2VIZXJvU2xvdE9iaiBleHRlbmRzIEhlcm9TbG90T2JqIHtcblxuICAgIHB1YmxpYyBzdHJpcCgpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGx2OiB0aGlzLmx2LFxuICAgICAgICAgICAgYXZhdGFyQXJteVVJRDogdGhpcy5hdmF0YXJBcm15VUlELFxuICAgICAgICAgICAgcmV2aXZlU3VycGx1c1RpbWU6IHRoaXMucmV2aXZlU3VycGx1c1RpbWUsXG4gICAgICAgICAgICBnZXRUaW1lOiB0aGlzLmdldFRpbWUsXG4gICAgICAgICAgICBoZXJvOiB0aGlzLmhlcm8/LnN0cmlwKCksXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgdG9EQigpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGx2OiB0aGlzLmx2LFxuICAgICAgICAgICAgYXZhdGFyQXJteVVJRDogdGhpcy5hdmF0YXJBcm15VUlELFxuICAgICAgICAgICAgcmV2aXZlU3VycGx1c1RpbWU6IHRoaXMucmV2aXZlU3VycGx1c1RpbWUsXG4gICAgICAgICAgICBnZXRUaW1lOiB0aGlzLmdldFRpbWUsXG4gICAgICAgICAgICBoZXJvOiB0aGlzLmhlcm8/LnN0cmlwKCksXG4gICAgICAgIH1cbiAgICB9XG59Il19