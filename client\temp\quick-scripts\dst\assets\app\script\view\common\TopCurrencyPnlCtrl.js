
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/TopCurrencyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e2e84AXv9RGYbLkO9C5SfHK', 'TopCurrencyPnlCtrl');
// app/script/view/common/TopCurrencyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var TopCurrencyPnlCtrl = /** @class */ (function (_super) {
    __extends(TopCurrencyPnlCtrl, _super);
    function TopCurrencyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.warTokenNode_ = null; // path://top/war_token_n
        _this.goldNode_ = null; // path://top/gold_n
        //@end
        _this.warTokenValLbl = null;
        _this.goldValLbl = null;
        _this.user = null;
        return _this;
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    TopCurrencyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_WAR_TOKEN] = this.onUpdateWarToken, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _b.enter = true, _b),
        ];
    };
    TopCurrencyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                this.warTokenValLbl = this.warTokenNode_.FindChild('val', cc.LabelRollNumber);
                this.goldValLbl = this.goldNode_.FindChild('val', cc.LabelRollNumber);
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    TopCurrencyPnlCtrl.prototype.onEnter = function (data) {
        this.warTokenValLbl.set(this.user.getWarToken());
        this.goldValLbl.set(this.user.getGold());
    };
    TopCurrencyPnlCtrl.prototype.onRemove = function () {
    };
    TopCurrencyPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://top/gold_n/add_gold_be
    TopCurrencyPnlCtrl.prototype.onClickAddGold = function (event, data) {
        ViewHelper_1.viewHelper.showBuyGoldTipPnl();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    TopCurrencyPnlCtrl.prototype.onUpdateWarToken = function () {
        this.warTokenValLbl.to(this.user.getWarToken());
    };
    // 刷新金币
    TopCurrencyPnlCtrl.prototype.onUpdateGold = function () {
        this.goldValLbl.to(this.user.getGold());
    };
    TopCurrencyPnlCtrl = __decorate([
        ccclass
    ], TopCurrencyPnlCtrl);
    return TopCurrencyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = TopCurrencyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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