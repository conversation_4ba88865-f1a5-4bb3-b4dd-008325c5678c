
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/ModifyMarchSpeedPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ab0a7ohAFxCo6LzjG6W57nb', 'ModifyMarchSpeedPnlCtrl');
// app/script/view/main/ModifyMarchSpeedPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ModifyMarchSpeedPnlCtrl = /** @class */ (function (_super) {
    __extends(ModifyMarchSpeedPnlCtrl, _super);
    function ModifyMarchSpeedPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inputEb_ = null; // path://root/input_eb_ebee
        _this.unitLbl_ = null; // path://root/unit_l
        _this.tipLbl_ = null; // path://root/tip_l
        //@end
        _this.data = null;
        _this.cb = null;
        return _this;
    }
    ModifyMarchSpeedPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ModifyMarchSpeedPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.inputEb_.setPlaceholder('ui.min_march_speed_desc', 'f_m', Constant_1.CAN_MIN_MARCH_SPEED);
                return [2 /*return*/];
            });
        });
    };
    ModifyMarchSpeedPnlCtrl.prototype.onEnter = function (data, cb) {
        if (data.pawns.length === 0) {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NEED_PAWN_SET_MARCHSPEED);
            return this.hide();
        }
        this.data = data;
        this.cb = cb;
        this.inputEb_.string = data.marchSpeed + '';
        this.unitLbl_.setLocaleKey('ui.march_speed_desc', '');
        this.tipLbl_.setLocaleKey('ui.max_can_march_speed', data.defaultMarchSpeed);
    };
    ModifyMarchSpeedPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    ModifyMarchSpeedPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/button/ok_be
    ModifyMarchSpeedPnlCtrl.prototype.onClickOk = function (event, data) {
        this.do(Number(this.inputEb_.string.trim()) || 0);
    };
    // path://root/input_eb_ebee
    ModifyMarchSpeedPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var speed = Number(event.string.trim()) || 0;
        if (!speed) {
            this.inputEb_.string = '';
        }
        else {
            this.inputEb_.string = cc.misc.clampf(speed, Constant_1.CAN_MIN_MARCH_SPEED, this.data.defaultMarchSpeed) + '';
        }
    };
    // path://root/button/reset_be
    ModifyMarchSpeedPnlCtrl.prototype.onClickReset = function (event, data) {
        this.do(this.data.defaultMarchSpeed);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    ModifyMarchSpeedPnlCtrl.prototype.do = function (speed) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!speed) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.please_input_march_speed')];
                        }
                        else if (this.data.marchSpeed === speed) {
                            return [2 /*return*/, this.hide()];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_SetArmySpeed', { uid: this.data.uid, index: this.data.index, marchSpeed: speed }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        else if (this.isValid) {
                            this.cb && this.cb(data === null || data === void 0 ? void 0 : data.marchSpeed);
                            this.cb = null;
                            this.hide();
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    ModifyMarchSpeedPnlCtrl = __decorate([
        ccclass
    ], ModifyMarchSpeedPnlCtrl);
    return ModifyMarchSpeedPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ModifyMarchSpeedPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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