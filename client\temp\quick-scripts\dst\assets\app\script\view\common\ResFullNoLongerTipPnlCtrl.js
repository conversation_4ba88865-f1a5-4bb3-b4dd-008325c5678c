
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ResFullNoLongerTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f4a1fG7pWdEr79JmxvvGK54', 'ResFullNoLongerTipPnlCtrl');
// app/script/view/common/ResFullNoLongerTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var ResFullNoLongerTipPnlCtrl = /** @class */ (function (_super) {
    __extends(ResFullNoLongerTipPnlCtrl, _super);
    function ResFullNoLongerTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.descLbl_ = null; // path://root/content/desc_l
        _this.itemsSv_ = null; // path://root/content/items_sv
        _this.noLongerTge_ = null; // path://root/no_longer_t
        _this.buttonsNode_ = null; // path://root/buttons_nbe_n
        //@end
        _this.noKey = '';
        _this.okCb = null;
        _this.cancelCb = null;
        return _this;
    }
    ResFullNoLongerTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ResFullNoLongerTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    ResFullNoLongerTipPnlCtrl.prototype.onEnter = function (items, data) {
        var _a;
        var _this = this;
        this.noKey = data.noKey;
        this.okCb = data.ok;
        this.cancelCb = data.cancel;
        (_a = this.descLbl_).setLocaleKey.apply(_a, __spread([data.content], (data.params || [])));
        this.itemsSv_.Items(items, function (it, data) { return ViewHelper_1.viewHelper.updateItemByCTypeOne(it, data, _this.key); });
        this.noLongerTge_.isChecked = !!data.select;
        // 是否显示取消按钮
        this.buttonsNode_.Child('cancel').active = !!this.cancelCb;
        // 设置按钮名字
        this.buttonsNode_.Child('ok/val', cc.Label).setLocaleKey(data.okText || 'login.button_ok');
        this.buttonsNode_.Child('cancel/val', cc.Label).setLocaleKey(data.cancelText || 'login.button_cancel');
        if (data.hideClose) { //隐藏背景关闭
            this.closeNode_.active = false;
        }
        else if (data.select) { //如果默认选中得 就让他延迟关闭
            this.delayClose();
        }
        else {
            this.closeNode_.active = true;
        }
    };
    ResFullNoLongerTipPnlCtrl.prototype.onRemove = function () {
    };
    ResFullNoLongerTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe_n
    ResFullNoLongerTipPnlCtrl.prototype.onClickButtons = function (event, data) {
        GameHelper_1.gameHpr.setNoLongerTipBySid(this.noKey, this.noLongerTge_.isChecked);
        this.hide();
        var name = event.target.name;
        if (name === 'ok') {
            this.okCb && this.okCb();
        }
        else if (name === 'cancel') {
            this.cancelCb && this.cancelCb();
        }
        this.okCb = null;
        this.cancelCb = null;
    };
    // path://close_be_n
    ResFullNoLongerTipPnlCtrl.prototype.onClickClose = function (event, data) {
        GameHelper_1.gameHpr.setNoLongerTipBySid(this.noKey, this.noLongerTge_.isChecked);
        this.hide();
        this.okCb = null;
        this.cancelCb = null;
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 被动打开时延时关闭
    ResFullNoLongerTipPnlCtrl.prototype.delayClose = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.closeNode_.active = false;
                        return [4 /*yield*/, ut.wait(Constant_1.DELAY_CLOSE_PNL_TIME, this)];
                    case 1:
                        _a.sent();
                        if (this.isValid) {
                            this.closeNode_.active = true;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    ResFullNoLongerTipPnlCtrl = __decorate([
        ccclass
    ], ResFullNoLongerTipPnlCtrl);
    return ResFullNoLongerTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ResFullNoLongerTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcUmVzRnVsbE5vTG9uZ2VyVGlwUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJEQUFzRTtBQUN0RSw2REFBeUQ7QUFDekQsNkRBQTREO0FBR3BELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQXVELDZDQUFjO0lBQXJFO1FBQUEscUVBcUZDO1FBbkZHLDBCQUEwQjtRQUNsQixnQkFBVSxHQUFZLElBQUksQ0FBQSxDQUFDLG9CQUFvQjtRQUMvQyxjQUFRLEdBQWEsSUFBSSxDQUFBLENBQUMsNkJBQTZCO1FBQ3ZELGNBQVEsR0FBa0IsSUFBSSxDQUFBLENBQUMsK0JBQStCO1FBQzlELGtCQUFZLEdBQWMsSUFBSSxDQUFBLENBQUMsMEJBQTBCO1FBQ3pELGtCQUFZLEdBQVksSUFBSSxDQUFBLENBQUMsNEJBQTRCO1FBQ2pFLE1BQU07UUFFRSxXQUFLLEdBQVcsRUFBRSxDQUFBO1FBQ2xCLFVBQUksR0FBYSxJQUFJLENBQUE7UUFDckIsY0FBUSxHQUFhLElBQUksQ0FBQTs7SUF5RXJDLENBQUM7SUF2RVUsbURBQWUsR0FBdEI7UUFDSSxPQUFPLEVBQUUsQ0FBQTtJQUNiLENBQUM7SUFFWSw0Q0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSwyQ0FBTyxHQUFkLFVBQWUsS0FBaUIsRUFBRSxJQUFTOztRQUEzQyxpQkFtQkM7UUFsQkcsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFBO1FBQ3ZCLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQTtRQUNuQixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDM0IsQ0FBQSxLQUFBLElBQUksQ0FBQyxRQUFRLENBQUEsQ0FBQyxZQUFZLHFCQUFDLElBQUksQ0FBQyxPQUFPLEdBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxJQUFJLEVBQUUsQ0FBQyxHQUFDO1FBQ2hFLElBQUksQ0FBQyxRQUFRLENBQUMsS0FBSyxDQUFDLEtBQUssRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJLElBQUssT0FBQSx1QkFBVSxDQUFDLG9CQUFvQixDQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxFQUFuRCxDQUFtRCxDQUFDLENBQUE7UUFDN0YsSUFBSSxDQUFDLFlBQVksQ0FBQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDM0MsV0FBVztRQUNYLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQTtRQUMxRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFlBQVksQ0FBQyxLQUFLLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLE1BQU0sSUFBSSxpQkFBaUIsQ0FBQyxDQUFBO1FBQzFGLElBQUksQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLFlBQVksRUFBRSxFQUFFLENBQUMsS0FBSyxDQUFDLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxVQUFVLElBQUkscUJBQXFCLENBQUMsQ0FBQTtRQUN0RyxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUUsRUFBRSxRQUFRO1lBQzFCLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtTQUNqQzthQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxFQUFFLGlCQUFpQjtZQUN2QyxJQUFJLENBQUMsVUFBVSxFQUFFLENBQUE7U0FDcEI7YUFBTTtZQUNILElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQTtTQUNoQztJQUNMLENBQUM7SUFFTSw0Q0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLDJDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiw0QkFBNEI7SUFDNUIsa0RBQWMsR0FBZCxVQUFlLEtBQTBCLEVBQUUsSUFBWTtRQUNuRCxvQkFBTyxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsQ0FBQTtRQUNwRSxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDWCxJQUFNLElBQUksR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQTtRQUM5QixJQUFJLElBQUksS0FBSyxJQUFJLEVBQUU7WUFDZixJQUFJLENBQUMsSUFBSSxJQUFJLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQTtTQUMzQjthQUFNLElBQUksSUFBSSxLQUFLLFFBQVEsRUFBRTtZQUMxQixJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUUsQ0FBQTtTQUNuQztRQUNELElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFBO1FBQ2hCLElBQUksQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFBO0lBQ3hCLENBQUM7SUFFRCxvQkFBb0I7SUFDcEIsZ0RBQVksR0FBWixVQUFhLEtBQTBCLEVBQUUsSUFBWTtRQUNqRCxvQkFBTyxDQUFDLG1CQUFtQixDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsQ0FBQTtRQUNwRSxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDWCxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQTtJQUN4QixDQUFDO0lBQ0QsTUFBTTtJQUNOLGlIQUFpSDtJQUVqSCxpSEFBaUg7SUFFakgsWUFBWTtJQUNFLDhDQUFVLEdBQXhCOzs7Ozt3QkFDSSxJQUFJLENBQUMsVUFBVSxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7d0JBQzlCLHFCQUFNLEVBQUUsQ0FBQyxJQUFJLENBQUMsK0JBQW9CLEVBQUUsSUFBSSxDQUFDLEVBQUE7O3dCQUF6QyxTQUF5QyxDQUFBO3dCQUN6QyxJQUFJLElBQUksQ0FBQyxPQUFPLEVBQUU7NEJBQ2QsSUFBSSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFBO3lCQUNoQzs7Ozs7S0FDSjtJQXBGZ0IseUJBQXlCO1FBRDdDLE9BQU87T0FDYSx5QkFBeUIsQ0FxRjdDO0lBQUQsZ0NBQUM7Q0FyRkQsQUFxRkMsQ0FyRnNELEVBQUUsQ0FBQyxXQUFXLEdBcUZwRTtrQkFyRm9CLHlCQUF5QiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERFTEFZX0NMT1NFX1BOTF9USU1FIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xyXG5pbXBvcnQgeyBnYW1lSHByIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvR2FtZUhlbHBlclwiO1xyXG5pbXBvcnQgeyB2aWV3SGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvVmlld0hlbHBlclwiO1xyXG5pbXBvcnQgQ1R5cGVPYmogZnJvbSBcIi4uLy4uL21vZGVsL2NvbW1vbi9DVHlwZU9ialwiO1xyXG5cclxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xyXG5cclxuQGNjY2xhc3NcclxuZXhwb3J0IGRlZmF1bHQgY2xhc3MgUmVzRnVsbE5vTG9uZ2VyVGlwUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcclxuXHJcbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxyXG4gICAgcHJpdmF0ZSBjbG9zZU5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vY2xvc2VfYmVfblxyXG4gICAgcHJpdmF0ZSBkZXNjTGJsXzogY2MuTGFiZWwgPSBudWxsIC8vIHBhdGg6Ly9yb290L2NvbnRlbnQvZGVzY19sXHJcbiAgICBwcml2YXRlIGl0ZW1zU3ZfOiBjYy5TY3JvbGxWaWV3ID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9jb250ZW50L2l0ZW1zX3N2XHJcbiAgICBwcml2YXRlIG5vTG9uZ2VyVGdlXzogY2MuVG9nZ2xlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9ub19sb25nZXJfdFxyXG4gICAgcHJpdmF0ZSBidXR0b25zTm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290L2J1dHRvbnNfbmJlX25cclxuICAgIC8vQGVuZFxyXG5cclxuICAgIHByaXZhdGUgbm9LZXk6IHN0cmluZyA9ICcnXHJcbiAgICBwcml2YXRlIG9rQ2I6IEZ1bmN0aW9uID0gbnVsbFxyXG4gICAgcHJpdmF0ZSBjYW5jZWxDYjogRnVuY3Rpb24gPSBudWxsXHJcblxyXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcclxuICAgICAgICByZXR1cm4gW11cclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIG9uRW50ZXIoaXRlbXM6IENUeXBlT2JqW10sIGRhdGE6IGFueSkge1xyXG4gICAgICAgIHRoaXMubm9LZXkgPSBkYXRhLm5vS2V5XHJcbiAgICAgICAgdGhpcy5va0NiID0gZGF0YS5va1xyXG4gICAgICAgIHRoaXMuY2FuY2VsQ2IgPSBkYXRhLmNhbmNlbFxyXG4gICAgICAgIHRoaXMuZGVzY0xibF8uc2V0TG9jYWxlS2V5KGRhdGEuY29udGVudCwgLi4uKGRhdGEucGFyYW1zIHx8IFtdKSlcclxuICAgICAgICB0aGlzLml0ZW1zU3ZfLkl0ZW1zKGl0ZW1zLCAoaXQsIGRhdGEpID0+IHZpZXdIZWxwZXIudXBkYXRlSXRlbUJ5Q1R5cGVPbmUoaXQsIGRhdGEsIHRoaXMua2V5KSlcclxuICAgICAgICB0aGlzLm5vTG9uZ2VyVGdlXy5pc0NoZWNrZWQgPSAhIWRhdGEuc2VsZWN0XHJcbiAgICAgICAgLy8g5piv5ZCm5pi+56S65Y+W5raI5oyJ6ZKuXHJcbiAgICAgICAgdGhpcy5idXR0b25zTm9kZV8uQ2hpbGQoJ2NhbmNlbCcpLmFjdGl2ZSA9ICEhdGhpcy5jYW5jZWxDYlxyXG4gICAgICAgIC8vIOiuvue9ruaMiemSruWQjeWtl1xyXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCdvay92YWwnLCBjYy5MYWJlbCkuc2V0TG9jYWxlS2V5KGRhdGEub2tUZXh0IHx8ICdsb2dpbi5idXR0b25fb2snKVxyXG4gICAgICAgIHRoaXMuYnV0dG9uc05vZGVfLkNoaWxkKCdjYW5jZWwvdmFsJywgY2MuTGFiZWwpLnNldExvY2FsZUtleShkYXRhLmNhbmNlbFRleHQgfHwgJ2xvZ2luLmJ1dHRvbl9jYW5jZWwnKVxyXG4gICAgICAgIGlmIChkYXRhLmhpZGVDbG9zZSkgeyAvL+makOiXj+iDjOaZr+WFs+mXrVxyXG4gICAgICAgICAgICB0aGlzLmNsb3NlTm9kZV8uYWN0aXZlID0gZmFsc2VcclxuICAgICAgICB9IGVsc2UgaWYgKGRhdGEuc2VsZWN0KSB7IC8v5aaC5p6c6buY6K6k6YCJ5Lit5b6XIOWwseiuqeS7luW7tui/n+WFs+mXrVxyXG4gICAgICAgICAgICB0aGlzLmRlbGF5Q2xvc2UoKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHRoaXMuY2xvc2VOb2RlXy5hY3RpdmUgPSB0cnVlXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25DbGVhbigpIHtcclxuICAgIH1cclxuXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXHJcblxyXG4gICAgLy8gcGF0aDovL3Jvb3QvYnV0dG9uc19uYmVfblxyXG4gICAgb25DbGlja0J1dHRvbnMoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG4gICAgICAgIGdhbWVIcHIuc2V0Tm9Mb25nZXJUaXBCeVNpZCh0aGlzLm5vS2V5LCB0aGlzLm5vTG9uZ2VyVGdlXy5pc0NoZWNrZWQpXHJcbiAgICAgICAgdGhpcy5oaWRlKClcclxuICAgICAgICBjb25zdCBuYW1lID0gZXZlbnQudGFyZ2V0Lm5hbWVcclxuICAgICAgICBpZiAobmFtZSA9PT0gJ29rJykge1xyXG4gICAgICAgICAgICB0aGlzLm9rQ2IgJiYgdGhpcy5va0NiKClcclxuICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdjYW5jZWwnKSB7XHJcbiAgICAgICAgICAgIHRoaXMuY2FuY2VsQ2IgJiYgdGhpcy5jYW5jZWxDYigpXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMub2tDYiA9IG51bGxcclxuICAgICAgICB0aGlzLmNhbmNlbENiID0gbnVsbFxyXG4gICAgfVxyXG5cclxuICAgIC8vIHBhdGg6Ly9jbG9zZV9iZV9uXHJcbiAgICBvbkNsaWNrQ2xvc2UoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xyXG4gICAgICAgIGdhbWVIcHIuc2V0Tm9Mb25nZXJUaXBCeVNpZCh0aGlzLm5vS2V5LCB0aGlzLm5vTG9uZ2VyVGdlXy5pc0NoZWNrZWQpXHJcbiAgICAgICAgdGhpcy5oaWRlKClcclxuICAgICAgICB0aGlzLm9rQ2IgPSBudWxsXHJcbiAgICAgICAgdGhpcy5jYW5jZWxDYiA9IG51bGxcclxuICAgIH1cclxuICAgIC8vQGVuZFxyXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuXHJcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBjdXN0b20gZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxyXG5cclxuICAgIC8vIOiiq+WKqOaJk+W8gOaXtuW7tuaXtuWFs+mXrVxyXG4gICAgcHJpdmF0ZSBhc3luYyBkZWxheUNsb3NlKCkge1xyXG4gICAgICAgIHRoaXMuY2xvc2VOb2RlXy5hY3RpdmUgPSBmYWxzZVxyXG4gICAgICAgIGF3YWl0IHV0LndhaXQoREVMQVlfQ0xPU0VfUE5MX1RJTUUsIHRoaXMpXHJcbiAgICAgICAgaWYgKHRoaXMuaXNWYWxpZCkge1xyXG4gICAgICAgICAgICB0aGlzLmNsb3NlTm9kZV8uYWN0aXZlID0gdHJ1ZVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG4iXX0=