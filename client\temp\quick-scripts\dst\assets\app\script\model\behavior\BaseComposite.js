
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/BaseComposite.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '88f48SeUCxBXYaNVRmdNUq0', 'BaseComposite');
// app/script/model/behavior/BaseComposite.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseNode_1 = require("./BaseNode");
var BTConstant_1 = require("./BTConstant");
// 组合节点
var BaseComposite = /** @class */ (function (_super) {
    __extends(BaseComposite, _super);
    function BaseComposite() {
        var _this = _super.call(this) || this;
        _this.children = [];
        _this.type = BTConstant_1.BTType.COMPOSITE;
        return _this;
    }
    BaseComposite.prototype.getChildrenCount = function () {
        return this.children.length;
    };
    // 添加子节点
    BaseComposite.prototype.addChild = function (node) {
        if (node) {
            this.children.push(node);
        }
    };
    return BaseComposite;
}(BaseNode_1.default));
exports.default = BaseComposite;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQmFzZUNvbXBvc2l0ZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1Q0FBa0M7QUFDbEMsMkNBQXNDO0FBRXRDLE9BQU87QUFDUDtJQUEyQyxpQ0FBUTtJQUkvQztRQUFBLFlBQ0ksaUJBQU8sU0FFVjtRQUxNLGNBQVEsR0FBZSxFQUFFLENBQUE7UUFJNUIsS0FBSSxDQUFDLElBQUksR0FBRyxtQkFBTSxDQUFDLFNBQVMsQ0FBQTs7SUFDaEMsQ0FBQztJQUVNLHdDQUFnQixHQUF2QjtRQUNJLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUE7SUFDL0IsQ0FBQztJQUVELFFBQVE7SUFDRCxnQ0FBUSxHQUFmLFVBQWdCLElBQWM7UUFDMUIsSUFBSSxJQUFJLEVBQUU7WUFDTixJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtTQUMzQjtJQUNMLENBQUM7SUFDTCxvQkFBQztBQUFELENBbkJBLEFBbUJDLENBbkIwQyxrQkFBUSxHQW1CbEQiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQmFzZU5vZGUgZnJvbSBcIi4vQmFzZU5vZGVcIjtcclxuaW1wb3J0IHsgQlRUeXBlIH0gZnJvbSBcIi4vQlRDb25zdGFudFwiO1xyXG5cclxuLy8g57uE5ZCI6IqC54K5XHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEJhc2VDb21wb3NpdGUgZXh0ZW5kcyBCYXNlTm9kZSB7XHJcblxyXG4gICAgcHVibGljIGNoaWxkcmVuOiBCYXNlTm9kZVtdID0gW11cclxuXHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlcigpXHJcbiAgICAgICAgdGhpcy50eXBlID0gQlRUeXBlLkNPTVBPU0lURVxyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBnZXRDaGlsZHJlbkNvdW50KCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLmNoaWxkcmVuLmxlbmd0aFxyXG4gICAgfVxyXG5cclxuICAgIC8vIOa3u+WKoOWtkOiKgueCuVxyXG4gICAgcHVibGljIGFkZENoaWxkKG5vZGU6IEJhc2VOb2RlKSB7XHJcbiAgICAgICAgaWYgKG5vZGUpIHtcclxuICAgICAgICAgICAgdGhpcy5jaGlsZHJlbi5wdXNoKG5vZGUpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59Il19