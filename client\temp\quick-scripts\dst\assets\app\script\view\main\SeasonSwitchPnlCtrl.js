
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/SeasonSwitchPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '16df6MWITZK7qL/PzGOmpt9', 'SeasonSwitchPnlCtrl');
// app/script/view/main/SeasonSwitchPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SeasonSwitchPnlCtrl = /** @class */ (function (_super) {
    __extends(SeasonSwitchPnlCtrl, _super);
    function SeasonSwitchPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.bgNode_ = null; // path://bg_n
        _this.closeNode_ = null; // path://close_be_n
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.descLbl_ = null; // path://root/desc_l
        _this.pollcyNode_ = null; // path://root/pollcy_n
        _this.winCondNode_ = null; // path://root/win_cond_n
        _this.zsNode_ = null; // path://zs_n
        //@end
        _this.delay = 0;
        _this.time = 0;
        return _this;
    }
    SeasonSwitchPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.CHANGE_SEASON_COMPLETE] = this.onChangeSeasonComplete, _a.enter = true, _a)
        ];
    };
    SeasonSwitchPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false, isClean: false });
                return [2 /*return*/];
            });
        });
    };
    SeasonSwitchPnlCtrl.prototype.onEnter = function (isInit) {
        this.node.opacity = 255;
        var seasonType = GameHelper_1.gameHpr.world.getSeasonType();
        this.titleLbl_.setLocaleKey('ui.season_' + seasonType);
        this.bgNode_.Child('di', cc.MultiFrame).setFrame(seasonType);
        this.Child('root', cc.MultiFrame).setFrame(seasonType);
        this.zsNode_.Swih(seasonType);
        this.descLbl_.setLocaleKey(this.getSeasonDesc(seasonType));
        this.updatePollcys(seasonType);
        if (this.winCondNode_.active = !!seasonType) {
            this.winCondNode_.Child('desc').setLocaleKey('ui.wind_cond_type_desc_' + GameHelper_1.gameHpr.world.getWinCondType());
        }
        this.closeNode_.Component(cc.Button).interactable = false;
        this.closeNode_.Child('desc').active = false;
        this.delay = 0;
        this.time = Date.now();
        this.emit(EventType_1.default.CHANGE_SEASON_BEGIN);
    };
    SeasonSwitchPnlCtrl.prototype.onRemove = function () {
    };
    SeasonSwitchPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://close_be_n
    SeasonSwitchPnlCtrl.prototype.onClickClose = function (event, data) {
        var _this = this;
        this.node.opacity = 255;
        this.setParam({ isClean: true });
        cc.tween(this.node).to(0.3, { opacity: 0 }).call(function () { return _this.isValid && _this.close(); }).start();
    };
    // path://root/pollcy_n/list/pollcy_be
    SeasonSwitchPnlCtrl.prototype.onClickPollcy = function (event, _) {
        var data = event.target.Data;
        if (data) {
            ViewHelper_1.viewHelper.showPnl('common/PolicyInfoBox', data.id);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    SeasonSwitchPnlCtrl.prototype.onChangeSeasonComplete = function () {
        this.delay = Math.max(1, (Date.now() - this.time) * 0.001);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    SeasonSwitchPnlCtrl.prototype.getSeasonDesc = function (type) {
        var winType = GameHelper_1.gameHpr.world.getWinCondType();
        if (type === 0) {
            return 'ui.season_desc_x';
        }
        else if (type === 1) {
            if (winType === Enums_1.WinCondType.KARMIC_MAHJONG) {
                return 'ui.season_desc_9';
            }
            return GameHelper_1.gameHpr.world.getSeason().getRunTime() < ut.Time.Hour * 10 ? 'ui.season_desc_5_0' : 'ui.season_desc_5';
        }
        else if (type === 2) {
            return 'ui.season_desc_3_0';
        }
        else if (type === 3) {
            if (winType === Enums_1.WinCondType.KARMIC_MAHJONG) {
                return 'ui.season_desc_10';
            }
            return 'ui.season_desc_4';
        }
    };
    // 刷新政策列表
    SeasonSwitchPnlCtrl.prototype.updatePollcys = function (type) {
        var _this = this;
        var list = Object.values(GameHelper_1.gameHpr.world.getSeasonPolicys()).sort(function (a, b) { return a.lv - b.lv; });
        this.pollcyNode_.Child('list').Items(list, function (it, data, i) {
            it.Data = data;
            ResHelper_1.resHelper.loadPolicyIcon(data.id, it.Child('val'), _this.key);
            ResHelper_1.resHelper.loadIcon('icon/season_' + data.lv, it.Child('season'), _this.key);
            it.Child('new').active = data.lv === type;
        });
    };
    SeasonSwitchPnlCtrl.prototype.update = function (dt) {
        if (this.delay <= 0) {
            return;
        }
        this.delay -= dt;
        if (this.delay <= 0) {
            this.closeNode_.Component(cc.Button).interactable = true;
            this.closeNode_.Child('desc').active = true;
        }
    };
    SeasonSwitchPnlCtrl = __decorate([
        ccclass
    ], SeasonSwitchPnlCtrl);
    return SeasonSwitchPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SeasonSwitchPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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