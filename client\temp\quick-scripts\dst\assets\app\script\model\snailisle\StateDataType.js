
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/StateDataType.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '19b80QIXgRN+IYSVp4YWwqg', 'StateDataType');
// app/script/model/snailisle/StateDataType.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeStateData = exports.IStateData = void 0;
var IStateData = /** @class */ (function () {
    function IStateData() {
    }
    IStateData.prototype.init = function () {
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        return this;
    };
    IStateData.prototype.clean = function () { };
    return IStateData;
}());
exports.IStateData = IStateData;
// 时间相关状态
var TimeStateData = /** @class */ (function (_super) {
    __extends(TimeStateData, _super);
    function TimeStateData() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    TimeStateData.prototype.init = function (time) {
        this.time = time;
        this.elapsed = 0;
        return this;
    };
    TimeStateData.prototype.ratio = function () {
        return this.time > 0 ? this.elapsed / this.time : 0;
    };
    TimeStateData.prototype.isEnd = function () {
        return this.elapsed >= this.time;
    };
    TimeStateData.prototype.getSurplusTime = function () {
        return Math.max(this.time - this.elapsed, 0);
    };
    // 直接完成
    TimeStateData.prototype.complete = function () {
        this.elapsed = this.time;
    };
    TimeStateData.prototype.update = function (dt) {
        this.elapsed += dt;
        return this.isEnd();
    };
    return TimeStateData;
}(IStateData));
exports.TimeStateData = TimeStateData;
exports.default = {
    TimeStateData: TimeStateData,
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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