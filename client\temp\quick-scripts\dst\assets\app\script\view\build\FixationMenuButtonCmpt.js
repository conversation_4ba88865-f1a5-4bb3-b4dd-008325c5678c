
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/FixationMenuButtonCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c9a2b8FbopIyZvyL8R0Ce7N', 'FixationMenuButtonCmpt');
// app/script/view/build/FixationMenuButtonCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 固定到菜单按钮
 */
var FixationMenuButtonCmpt = /** @class */ (function (_super) {
    __extends(FixationMenuButtonCmpt, _super);
    function FixationMenuButtonCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.key = 0;
        _this.conf = null;
        _this.iconMulti = null;
        _this.isFixation = false;
        return _this;
    }
    FixationMenuButtonCmpt.prototype.start = function () {
        this.updateKey();
    };
    FixationMenuButtonCmpt.prototype.setIconMulti = function (val) {
        if (!this.iconMulti) {
            this.iconMulti = this.FindChild('val', cc.MultiFrame);
        }
        this.iconMulti.setFrame(val);
    };
    FixationMenuButtonCmpt.prototype.updateKey = function () {
        this.conf = Constant_1.FIXATION_MENU_CONFIG[this.key];
        if (this.node.active = !!this.conf) {
            this.node.off('click', this.onClick, this);
            this.node.on('click', this.onClick, this);
            var arr = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA) || [];
            this.isFixation = arr.has(this.key);
            this.setIconMulti(this.isFixation);
        }
    };
    FixationMenuButtonCmpt.prototype.onClick = function () {
        var _this = this;
        if (!this.conf) {
            return;
        }
        var isFixation = !this.isFixation;
        if (!GameHelper_1.gameHpr.isNoLongerTip('show_fixation_menu_tip')) { //显示提示
            return ViewHelper_1.viewHelper.showPnl('common/NoLongerTip', {
                content: 'ui.show_fixation_menu_tip_' + (isFixation ? 0 : 1),
                noKey: 'show_fixation_menu_tip',
                select: true,
                ok: function () { return _this.isValid && _this.do(isFixation); },
            });
        }
        else {
            this.do(isFixation);
        }
    };
    FixationMenuButtonCmpt.prototype.do = function (isFixation) {
        var user = GameHelper_1.gameHpr.user;
        var arr = user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA) || [];
        // 检测是否超出上限
        if (!isFixation) {
            arr.remove(this.key);
        }
        else if (arr.length >= Constant_1.FIXATION_MENU_MAX_COUNT) {
            return ViewHelper_1.viewHelper.showAlert('toast.fixation_menu_count_maximum', { params: [Constant_1.FIXATION_MENU_MAX_COUNT] });
        }
        else {
            arr.push(this.key);
        }
        this.isFixation = isFixation;
        this.setIconMulti(this.isFixation);
        user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.FIXATION_MENU_DATA, arr);
    };
    FixationMenuButtonCmpt.prototype.setKey = function (key) {
        this.key = key;
        this.updateKey();
    };
    __decorate([
        property
    ], FixationMenuButtonCmpt.prototype, "key", void 0);
    FixationMenuButtonCmpt = __decorate([
        ccclass
    ], FixationMenuButtonCmpt);
    return FixationMenuButtonCmpt;
}(cc.Component));
exports.default = FixationMenuButtonCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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