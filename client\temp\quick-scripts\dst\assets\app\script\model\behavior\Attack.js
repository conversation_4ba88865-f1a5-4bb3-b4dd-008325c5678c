
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/Attack.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '7a9eeYnqZdJYIyYrsZgr8QX', 'Attack');
// app/script/model/behavior/Attack.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var MapHelper_1 = require("../../common/helper/MapHelper");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 攻击
var Attack = /** @class */ (function (_super) {
    __extends(Attack, _super);
    function Attack() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.attackAnimTimes = [];
        _this.needAttackTime = 0; //需要攻击的时间
        _this.needHitTime = 0; //需要击中的时间
        _this.bulletId = 0; //子弹id
        _this.isChangeFighterState = false; //是否改变士兵的状态了
        _this.isChangeTargetState = false; //是否改变目标士兵的状态了
        _this.isPlayBullet = false; //是否播放子弹了
        _this.dashArray = []; //冲撞时间记录
        _this.isChangeTargetDashState = false;
        _this.isDashing = false; //是否击飞结束
        _this._temp_vec2_1 = cc.v2();
        _this._temp_vec2_2 = cc.v2();
        return _this;
    }
    Attack.prototype.onInit = function (conf) {
        this.attackAnimTimes = this.target.entity.getAttackAnimTimes();
        var _a = __read(this.attackAnimTimes[0], 3), a = _a[0], b = _a[1], c = _a[2];
        this.needAttackTime = Math.floor(a * 1000);
        this.needHitTime = Math.floor(b * 1000);
        this.bulletId = c;
    };
    Attack.prototype.onOpen = function () {
        var _a, _b, _c, _d;
        this.setTreeBlackboardData('isAttack', true); //记录已经攻击过了
        this.isChangeFighterState = false;
        this.isChangeTargetState = false;
        this.isChangeTargetDashState = false;
        this.isPlayBullet = false;
        this.dashArray = [];
        this.isDashing = false;
        var heroSkillId = (_a = this.target.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id;
        // 如果是陌刀兵 提前随机好攻击段
        var skill = this.target.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (skill) {
            var index = (_b = this.getBlackboardData('instabilityAttackIndex')) !== null && _b !== void 0 ? _b : 0;
            if (index === 0) {
                if (skill.intensifyType === 1 && !!this.target.checkTriggerBuff(Enums_1.BuffType.STEADY_ATTACK)) {
                    index = 4; //专属触发最高值
                }
                else if (((_c = this.target.attackTarget) === null || _c === void 0 ? void 0 : _c.getPawnType()) === Enums_1.PawnType.SOWAR) {
                    index = 3; //目标是骑兵时都会造成第3段
                }
                else if ((_d = this.target.attackTarget) === null || _d === void 0 ? void 0 : _d.isBuild()) {
                    index = 1;
                }
                else if (heroSkillId === Enums_1.HeroType.LI_SIYE) {
                    index = 3; //李嗣业总是按人马俱碎的随即范围
                }
                else {
                    index = this.ctrl.getRandom().get(1, 3);
                }
                this.setBlackboardData('instabilityAttackIndex', index);
            }
            var _e = __read(this.attackAnimTimes[Math.min(index - 1, 2)], 3), a = _e[0], b = _e[1], c = _e[2];
            this.needAttackTime = Math.floor(a * 1000);
            this.needHitTime = Math.floor(b * 1000);
        }
        // 如果是斧骑兵 并且有专属 准备好攻击方式
        skill = this.target.getSkillByType(Enums_1.PawnSkillType.LONGITUDINAL_CLEFT);
        if (skill && this.attackAnimTimes.length >= 2) {
            var index = skill.intensifyType === 1 ? 1 : 0;
            // 程咬金 0 2 4 | 1 3 5
            if (heroSkillId === Enums_1.HeroType.CHENG_YAOJIN) {
                var axes = this.target.getBuffValue(Enums_1.BuffType.THREE_AXES);
                if (axes === 1) {
                    index += 2; //劈脑袋
                }
                else if (axes === 3) {
                    index += 4; //掏耳朵
                }
            }
            this.setBlackboardData('instabilityAttackIndex', index);
            var _f = __read(this.attackAnimTimes[index], 3), a = _f[0], b = _f[1], c = _f[2];
            this.needAttackTime = Math.floor(a * 1000);
            this.needHitTime = Math.floor(b * 1000);
        }
        // 如果是秦琼 并且有强化攻击 准备好攻击方式
        if (heroSkillId === Enums_1.HeroType.QIN_QIONG) {
            var index = this.target.isHasBuff(Enums_1.BuffType.BEHEADED_GENERAL) ? 1 : 0;
            this.setBlackboardData('instabilityAttackIndex', index);
            var _g = __read(this.attackAnimTimes[index], 3), a = _g[0], b = _g[1], c = _g[2];
            this.needAttackTime = Math.floor(a * 1000);
            this.needHitTime = Math.floor(b * 1000);
        }
        // 猩猩 狂怒
        if (this.target.getId() === 4114) {
            var index = this.target.isHasBuff(Enums_1.BuffType.RAGE) ? 1 : 0;
            this.setBlackboardData('instabilityAttackIndex', index);
            var _h = __read(this.attackAnimTimes[index], 3), a = _h[0], b = _h[1], c = _h[2];
            this.needAttackTime = Math.floor(a * 1000);
            this.needHitTime = Math.floor(b * 1000);
        }
    };
    Attack.prototype.onTick = function (dt) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g;
        var attackTarget = this.target.attackTarget;
        if (!attackTarget || attackTarget.isDie()) {
            return BTConstant_1.BTState.FAILURE;
        }
        var needAttackTime = this.getNeedAttackTime();
        var currAttackTime = (_a = this.getBlackboardData('currAttackTime')) !== null && _a !== void 0 ? _a : 0;
        if (currAttackTime >= needAttackTime && !this.isDashing) {
            return BTConstant_1.BTState.SUCCESS;
        }
        var heroSkillId = (_b = this.target.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id;
        // 增加时间
        this.setBlackboardData('currAttackTime', currAttackTime + dt);
        // 是否到击中的时间点
        var needHitTime = this.bulletId ? needAttackTime - dt : this.needHitTime;
        if (currAttackTime >= needHitTime) {
            var time = currAttackTime - needHitTime;
            // 裴行俨 需要击飞
            if (heroSkillId === Enums_1.HeroType.PEI_XINGYAN) {
                if (this.dashArray.length === 0) {
                    var point = this.target.getPoint();
                    var p = attackTarget.getCanByReqelPoint(point, 1);
                    if (p) {
                        attackTarget.setPoint(p);
                        this.ctrl.updateFighterPointMap();
                        this.dashArray = [{ point: point, time: 0 }];
                    }
                }
                this.isDashing = true;
                if (this.dashArray.length > 0 && !this.diaupOne(attackTarget, dt, 300, 0)) {
                    return BTConstant_1.BTState.RUNNING;
                }
                this.isDashing = false;
            }
            // 击中
            if (!this.getBlackboardData('isHit')) {
                var instabilityAttackIndex = (_c = this.getBlackboardData('instabilityAttackIndex')) !== null && _c !== void 0 ? _c : 0;
                var info = this.ctrl.getAttackDamage(this.target, attackTarget, {
                    instabilityAttackIndex: instabilityAttackIndex,
                    attackType: 'attack',
                    getExtraDamge: function () {
                        if (heroSkillId === Enums_1.HeroType.PEI_XINGYAN) {
                            var dis = MapHelper_1.mapHelper.getPointToPointDis(attackTarget.getPoint(), attackTarget.getLastPoint());
                            var v = _this.ctrl.getBaseAttackDamage(_this.target, attackTarget, { attackAmend: _this.target.getPortrayalSkill().target * 0.01 });
                            return v * dis;
                        }
                        return 0;
                    },
                    getTrueDamage: function () {
                        if (heroSkillId === Enums_1.HeroType.HAN_DANG) {
                            var atk = _this.ctrl.getBaseAttackDamage(_this.target, attackTarget);
                            return Math.round(atk * _this.target.getCurAnger() * _this.target.getPortrayalSkill().value * 0.01);
                        }
                        return 0;
                    }
                });
                this.setBlackboardData('isHit', true);
                this.setBlackboardData('isCrit', info.isCrit);
                var _h = attackTarget.hitPrepDamageHandle(info.damage, info.trueDamage), damage = _h.damage, trueDamage = _h.trueDamage;
                this.setBlackboardData('damage', damage);
                this.setBlackboardData('trueDamage', trueDamage);
                if (!attackTarget.isDie()) {
                    var sumDamage = Math.max(0, damage) + trueDamage;
                    var v = attackTarget.onHit(sumDamage, [this.target.getOwner()]);
                    // 处理攻击方装备效果-后置
                    var heal = this.ctrl.doAttackAfter(this.target, attackTarget, {
                        actDamage: v.damage,
                        sumDamage: sumDamage,
                        trueDamage: trueDamage,
                        hitShield: v.hitShield,
                        time: time,
                        instabilityAttackIndex: instabilityAttackIndex,
                        attackType: 'attack'
                    }).heal;
                    this.setBlackboardData('heal', heal + v.heal);
                    // 增加攻击方怒气
                    var anger = 1;
                    if (this.target.getEquipEffectByType(Enums_1.EquipEffectType.RECOVER_ANGER)) {
                        anger += 1;
                    }
                    if (this.target.isHasStrategys(40103, 40202, 40304, 40402)) {
                        anger += 1;
                    }
                    this.target.addAnger(anger);
                    // 攻击次数
                    this.target.addAttackCount(1);
                    // 增加防守方怒气
                    if (v.damage > 0 || damage === -2 || (damage === -1 && attackTarget.isHasStrategys(50002))) {
                        attackTarget.addAnger(1);
                    }
                }
                // 太史慈 有概率直接释放骑射
                if (heroSkillId === Enums_1.HeroType.TAI_SHICI && !this.getTreeBlackboardData('isBatter') && this.ctrl.getRandom().chance(this.target.getPortrayalSkill().value)) {
                    var v = this.target.setFullAnger(1, false);
                    this.target.changeState(Enums_1.PawnState.ADD_ANGER, { val: v });
                    this.target.cleanBlackboard('hitTargetAddAngerUids');
                    this.setTreeBlackboardData('isBatter', true); //标记连击
                }
                // 猩猩 狂怒 震屏
                if (this.target.getId() === 4114 && instabilityAttackIndex === 1) {
                    this.ctrl.playSceneShake(0.3);
                }
            }
            // 设置目标士兵状态
            if (!this.isChangeTargetState) {
                this.isChangeTargetState = true;
                var damage = (_d = this.getBlackboardData('damage')) !== null && _d !== void 0 ? _d : this.target.getActAttack();
                // 如果是典韦格挡了 就不播放了
                if (damage === -3 && heroSkillId === Enums_1.HeroType.DIAN_WEI) {
                    eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: attackTarget.getAreaIndex(), uid: attackTarget.getUid(), value: 0, isTurntheblade: true });
                }
                else {
                    var trueDamage = (_e = this.getBlackboardData('trueDamage')) !== null && _e !== void 0 ? _e : 0;
                    var isCrit = !!this.getBlackboardData('isCrit');
                    var heal = (_f = this.getBlackboardData('heal')) !== null && _f !== void 0 ? _f : 0;
                    attackTarget.changeState(Enums_1.PawnState.HIT, { damage: damage, trueDamage: trueDamage, isCrit: isCrit, heal: heal, attackPoint: this.target.getPoint(), isDie: attackTarget.isDie(), time: time });
                }
            }
        }
        // 如果是强弩兵 这里归0
        if (this.target.entity.id === Constant_1.PAWN_CROSSBOW_ID && this.target.getCurAnger() > 0) {
            this.target.setAnger(0);
        }
        // 设置士兵状态 更新视图信息
        if (!this.isChangeFighterState) {
            this.isChangeFighterState = true;
            var instabilityAttackIndex = (_g = this.getBlackboardData('instabilityAttackIndex')) !== null && _g !== void 0 ? _g : 0;
            this.target.changeState(Enums_1.PawnState.ATTACK, { currAttackTime: currAttackTime, targetPoint: attackTarget.getPoint(), instabilityAttackIndex: instabilityAttackIndex });
        }
        // 播放子弹飞行
        if (!this.bulletId || this.isPlayBullet) {
        }
        else if (currAttackTime >= this.needHitTime) {
            this.isPlayBullet = true;
            eventCenter.emit(EventType_1.default.PLAY_BULLET_FLY, {
                bulletId: this.bulletId,
                currTime: currAttackTime - this.needHitTime,
                needTime: needAttackTime - this.needHitTime,
                index: this.target.getAreaIndex(),
                startPoint: this.target.getPoint(),
                targetPoint: attackTarget.getPoint(),
            });
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 获取攻击需要的时间
    Attack.prototype.getNeedAttackTime = function () {
        var _a;
        if (!this.bulletId) {
            return this.needAttackTime;
        }
        var time = (_a = this.getBlackboardData('needAttackTime')) !== null && _a !== void 0 ? _a : 0;
        if (time === 0) {
            var point = this.target.getPoint(), target = this.target.attackTarget.getPoint();
            // 计算距离
            var mag = Math.floor(MapHelper_1.mapHelper.getPixelByPoint(point, this._temp_vec2_1).subSelf(MapHelper_1.mapHelper.getPixelByPoint(target, this._temp_vec2_2)).mag() * 1000);
            // 计算飞行时间 + 等待发射的时间
            time = Math.floor(mag / this.needAttackTime * 1000) + this.needHitTime;
            this.setBlackboardData('needAttackTime', time);
        }
        return time;
    };
    // 击飞
    Attack.prototype.diaupOne = function (attackTarget, dt, needDiaupTime, parabolaHeight) {
        var _a, _b;
        var currDiaupTime = (_a = this.getBlackboardData('currDiaupTime')) !== null && _a !== void 0 ? _a : 0;
        if (currDiaupTime >= needDiaupTime) {
            return true;
        }
        this.setBlackboardData('currDiaupTime', currDiaupTime + dt);
        // 设置目标士兵状态
        if (!this.isChangeTargetDashState) {
            this.isChangeTargetDashState = true;
            attackTarget.changeState(Enums_1.PawnState.DIAUP, { time: needDiaupTime - currDiaupTime - 50, attackPoint: (_b = this.dashArray[0]) === null || _b === void 0 ? void 0 : _b.point, parabolaHeight: parabolaHeight });
        }
        return false;
    };
    return Attack;
}(BaseAction_1.default));
exports.default = Attack;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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