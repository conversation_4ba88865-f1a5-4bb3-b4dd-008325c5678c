
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/guide/NoviceMarchObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '443f90TGqFCDak1ObLdliqt', 'NoviceMarchObj');
// app/script/model/guide/NoviceMarchObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一个行军
var NoviceMarchObj = /** @class */ (function () {
    function NoviceMarchObj() {
        this.uid = '';
        this.owner = '';
        this.armyUid = '';
        this.roleId = 0;
        this.armyIndex = 0;
        this.startIndex = 0;
        this.targetIndex = 0;
        this.startTime = 0;
        this.needTime = 0;
        this.autoRevoke = false;
        this.armyName = '';
    }
    NoviceMarchObj.prototype.init = function (army, index, target, time, roleId) {
        this.uid = ut.UID();
        this.owner = army.owner;
        this.armyUid = army.uid;
        this.armyIndex = army.aIndex;
        this.armyName = army.name;
        this.roleId = roleId;
        this.startIndex = index;
        this.targetIndex = target;
        this.needTime = time;
        this.startTime = Date.now();
        return this;
    };
    NoviceMarchObj.prototype.fromDB = function (data) {
        this.uid = data.uid;
        this.owner = data.owner;
        this.armyUid = data.armyUid;
        this.roleId = data.roleId;
        this.armyIndex = data.armyIndex;
        this.startIndex = data.startIndex;
        this.targetIndex = data.targetIndex;
        this.startTime = data.startTime;
        this.needTime = data.needTime;
        this.autoRevoke = data.autoRevoke;
        this.armyName = data.armyName;
        return this;
    };
    NoviceMarchObj.prototype.strip = function () {
        return {
            uid: this.uid,
            owner: this.owner,
            armyUid: this.armyUid,
            armyName: this.armyName,
            roleId: this.roleId,
            armyIndex: this.armyIndex,
            startIndex: this.startIndex,
            targetIndex: this.targetIndex,
            needTime: this.needTime,
            autoRevoke: this.autoRevoke,
            surplusTime: Math.max(this.needTime - (Date.now() - this.startTime), 0),
        };
    };
    return NoviceMarchObj;
}());
exports.default = NoviceMarchObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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