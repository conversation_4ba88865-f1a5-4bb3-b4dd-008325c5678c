
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/ISceneMapObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9057bsEe9pBkqJujrOEyG/I', 'ISceneMapObj');
// app/script/model/snailisle/ISceneMapObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var ISceneMapObj = /** @class */ (function () {
    function ISceneMapObj() {
        this.uid = '';
        this.point = cc.v2();
        this.points = []; //站位点
        this.zIndex = 0;
        this.checkZindex = 0;
        this.noUpdateZindex = false;
        this.baseType = 0;
    }
    ISceneMapObj.prototype.getActPoints = function (point) { return this.points; };
    ISceneMapObj.prototype.clean = function () {
        this.point = null;
        this.points = null;
    };
    return ISceneMapObj;
}());
exports.default = ISceneMapObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxzbmFpbGlzbGVcXElTY2VuZU1hcE9iai50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUNBO0lBQUE7UUFFVyxRQUFHLEdBQVcsRUFBRSxDQUFBO1FBQ2hCLFVBQUssR0FBWSxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUE7UUFDeEIsV0FBTSxHQUFjLEVBQUUsQ0FBQSxDQUFDLEtBQUs7UUFDNUIsV0FBTSxHQUFXLENBQUMsQ0FBQTtRQUNsQixnQkFBVyxHQUFXLENBQUMsQ0FBQTtRQUN2QixtQkFBYyxHQUFZLEtBQUssQ0FBQTtRQUMvQixhQUFRLEdBQVcsQ0FBQyxDQUFBO0lBUS9CLENBQUM7SUFOVSxtQ0FBWSxHQUFuQixVQUFvQixLQUFlLElBQUksT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFBLENBQUMsQ0FBQztJQUVwRCw0QkFBSyxHQUFaO1FBQ0ksSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUE7UUFDakIsSUFBSSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUE7SUFDdEIsQ0FBQztJQUNMLG1CQUFDO0FBQUQsQ0FoQkEsQUFnQkMsSUFBQSIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBJU2NlbmVNYXBPYmoge1xyXG5cclxuICAgIHB1YmxpYyB1aWQ6IHN0cmluZyA9ICcnXHJcbiAgICBwdWJsaWMgcG9pbnQ6IGNjLlZlYzIgPSBjYy52MigpXHJcbiAgICBwdWJsaWMgcG9pbnRzOiBjYy5WZWMyW10gPSBbXSAvL+ermeS9jeeCuVxyXG4gICAgcHVibGljIHpJbmRleDogbnVtYmVyID0gMFxyXG4gICAgcHVibGljIGNoZWNrWmluZGV4OiBudW1iZXIgPSAwXHJcbiAgICBwdWJsaWMgbm9VcGRhdGVaaW5kZXg6IGJvb2xlYW4gPSBmYWxzZVxyXG4gICAgcHVibGljIGJhc2VUeXBlOiBudW1iZXIgPSAwXHJcblxyXG4gICAgcHVibGljIGdldEFjdFBvaW50cyhwb2ludD86IGNjLlZlYzIpIHsgcmV0dXJuIHRoaXMucG9pbnRzIH1cclxuXHJcbiAgICBwdWJsaWMgY2xlYW4oKSB7XHJcbiAgICAgICAgdGhpcy5wb2ludCA9IG51bGxcclxuICAgICAgICB0aGlzLnBvaW50cyA9IG51bGxcclxuICAgIH1cclxufSJdfQ==