
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ShopPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '34f74QMYpNK+obClMWK9Jn2', 'ShopPnlCtrl');
// app/script/view/common/ShopPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PayHelper_1 = require("../../common/helper/PayHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var LocalConfig_1 = require("../../common/LocalConfig");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var ccclass = cc._decorator.ccclass;
var ShopPnlCtrl = /** @class */ (function (_super) {
    __extends(ShopPnlCtrl, _super);
    function ShopPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://top_n
        _this.rootNode_ = null; // path://root_n
        _this.shopSv_ = null; // path://root_n/shop_sv
        _this.mysteryBoxNode_ = null; // path://root_n/shop_sv/view/content/mystery_box_n
        _this.freeGoldNode_ = null; // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
        _this.optionalHeroNode_ = null; // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
        _this.ingotNode_ = null; // path://root_n/shop_sv/view/content/ingot_n
        _this.recommendSv_ = null; // path://root_n/shop_sv/view/content/recommend/recommend_sv
        _this.cardNode_ = null; // path://root_n/shop_sv/view/content/card_n
        _this.restoreBuyNode_ = null; // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
        _this.limitedSkinNode_ = null; // path://root_n/shop_sv/view/content/limited_skin_n
        _this.citySkinNode_ = null; // path://root_n/shop_sv/view/content/city_skin_n
        _this.pawnSkinNode_ = null; // path://root_n/shop_sv/view/content/pawn_skin_n
        _this.newHeadNode_ = null; // path://root_n/shop_sv/view/content/new_head_n
        _this.classicalHeadNode_ = null; // path://root_n/shop_sv/view/content/classical_head_n
        _this.newEmojiNode_ = null; // path://root_n/shop_sv/view/content/new_emoji_n
        _this.classicalEmojiNode_ = null; // path://root_n/shop_sv/view/content/classical_emoji_n
        _this.skinTabsNode_ = null; // path://root_n/skin_tabs_n
        _this.socialTabsNode_ = null; // path://root_n/social_tabs_n
        _this.tabsTc_ = null; // path://root_n/tabs_tc_tce
        //@end
        // 推荐列表 type:对应配置表，id:对应配置表相应id
        _this.RECOMMENDED_LIST = [
            { type: 'head', id: 206 },
            { type: 'city', id: 1001111 },
            { type: 'pawn', id: 3103105 },
            { type: 'emoji', id: 2040 },
            { type: 'head', id: 190 },
            { type: 'city', id: 1001107 },
            { type: 'pawn', id: 3202101 },
            { type: 'emoji', id: 2050 },
            { type: 'head', id: 187 },
        ];
        _this.PKEY_TAB = 'SHOP_TAB';
        _this.goldValLbl = null;
        _this.ingotValLbl = null;
        _this.user = null;
        _this.curTab = 0;
        _this.showType = '';
        _this.mysteryBoxPrefab = null;
        _this.preViewHeight = 904;
        _this.drawCount = 0;
        _this.drawRate = 10;
        _this.frameCount = 0;
        _this.funcList = [
            _this.updateRecommend,
            _this.updateMonthlyCard,
            _this.updateHeadIcon,
            _this.updateChatEmoji,
        ];
        return _this;
    }
    ShopPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_GOLD] = this.onUpdateGold, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_INGOT] = this.onUpdateIngot, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.INIT_PAY_FINISH] = this.onInitPayFinish, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.UPDATE_SUBSCRIPTION] = this.onUpdateSubscripion, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_RECHARGE_COUNT] = this.onUpdateRechargeCount, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_MYSTERYBOX] = this.onUpdateMysteryBox, _f.enter = true, _f),
        ];
    };
    ShopPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var throttleUpdate;
            var _this = this;
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                this.user = this.getModel('user');
                this.loadMysteryBoxPrefab();
                // this.restoreBuyNode_.active = !gameHpr.isNoviceMode && (cc.sys.isBrowser || !gameHpr.isRelease)
                this.optionalHeroNode_.Child('button/money/val', cc.Label).string = Constant_1.BUY_OPT_HERO_COST + '';
                throttleUpdate = this.throttle(function (event) { return _this.onScrolling(event); }, 200);
                this.shopSv_.node.on('scrolling', throttleUpdate, this);
                return [2 /*return*/];
            });
        });
    };
    ShopPnlCtrl.prototype.onEnter = function (type, showType) {
        var _a;
        if (typeof (type) === 'string') {
            type = Number(type);
        }
        this.curTab = (_a = type !== null && type !== void 0 ? type : this.user.getTempPreferenceMap(this.PKEY_TAB)) !== null && _a !== void 0 ? _a : 0;
        this.showType = showType;
        this.restoreBuyNode_.active = ut.isIos() && LocalConfig_1.localConfig.RELEASE && !GameHelper_1.gameHpr.isRelease; // 苹果审核才显示
        this.drawCount = this.frameCount = 0;
        this.initTop();
        this.checkShowNotFinishOrder();
        this.updateShopIngot();
        // this.updateRecommend()
        // this.updateMonthlyCard()
        this.updateSkin();
        // this.updateHeadIcon()
        // this.updateChatEmoji()
        this.tabsTc_.Tabs(this.curTab);
        this.emit(EventType_1.default.HIDE_TOP_NODE, false);
        this.playRootAnimation();
    };
    ShopPnlCtrl.prototype.onRemove = function () {
        // this.topNode_.active = false
        this.emit(EventType_1.default.HIDE_TOP_NODE, true);
    };
    ShopPnlCtrl.prototype.onClean = function () {
        this.drawCount = this.frameCount = 0;
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/tabs_tc_tce
    ShopPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.curTab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var y = 0;
        if (type === 0) { // 元宝位置 0
            if (this.showType === 'ingot') {
                y = this.ingotNode_.y;
            }
            this.skinTabsNode_.active = this.socialTabsNode_.active = false;
        }
        else if (type === 1) { // 月卡位置 1
            y = this.cardNode_.y;
            this.skinTabsNode_.active = this.socialTabsNode_.active = false;
        }
        else if (type === 2) { // 皮肤位置 2
            this.skinTabsNode_.active = true;
            this.socialTabsNode_.active = false;
            y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y;
        }
        else if (type === 3) { // 新款头像位置 3
            this.skinTabsNode_.active = false;
            this.socialTabsNode_.active = true;
            y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/skin_tabs_n/skin_tabs_nbe
    ShopPnlCtrl.prototype.onClickSkinTabs = function (event, data) {
        var name = event.target.name;
        var y = 0;
        if (name === '0') { // 跳转限定
            y = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y;
        }
        else if (name === '1') { // 跳转主城
            y = this.citySkinNode_.y;
        }
        else if (name === '2') { // 跳转士兵
            y = this.pawnSkinNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/social_tabs_n/social_tabs_nbe
    ShopPnlCtrl.prototype.onClickSocialTabs = function (event, data) {
        var name = event.target.name;
        var y = 0;
        if (name === '0') { // 跳转头像
            y = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y;
        }
        else if (name === '1') { // 跳转表情
            y = this.newEmojiNode_.active ? this.newEmojiNode_.y : this.classicalEmojiNode_.y;
        }
        this.shopSv_.stopAutoScroll();
        this.shopSv_.content.y = Math.abs(y);
    };
    // path://root_n/shop_sv/view/content/exchange/list/free_gold_be_n
    ShopPnlCtrl.prototype.onClickFreeGold = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (!this.user.isBuyLimitFreeGold()) {
            return ViewHelper_1.viewHelper.showAlert('ui.yet_buy_day');
        }
        this.user.buyFreeGold().then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateFreeGold();
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    // path://root_n/shop_sv/view/content/exchange/list/buy_gold_be
    ShopPnlCtrl.prototype.onClickBuyGold = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/ShopBuyGoldTip');
    };
    // path://root_n/shop_sv/view/content/exchange/list/optional_hero_be_n
    ShopPnlCtrl.prototype.onClickOptionalHero = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (!this.user.isBuyLimitOptionalHero()) {
            return ViewHelper_1.viewHelper.showAlert('ui.yet_buy_week');
        }
        var list = assetsMgr.getJson('portrayalBase').datas.map(function (m) { return new PortrayalInfo_1.default().init(m.id, m); });
        ViewHelper_1.viewHelper.showPnl('common/SelectPortrayal', Enums_1.SelectPortrayalType.BUY, list, function (arr) {
            if (_this.isValid && arr.length > 0) {
                _this.buyHero(arr[0].id);
            }
        }, 3);
    };
    // path://root_n/shop_sv/view/content/ingot_n/title/pay_not_arrived_be
    ShopPnlCtrl.prototype.onClickPayNotArrived = function (event, data) {
        ViewHelper_1.viewHelper.showLoadingWait(true);
        this.checkShowNotFinishOrder(true).then(function () { return ViewHelper_1.viewHelper.showLoadingWait(false); });
    };
    // path://root_n/shop_sv/view/content/card_n/title/restore_buy_be_n
    ShopPnlCtrl.prototype.onClickRestoreBuy = function (event, data) {
        PayHelper_1.payHelper.restoreBuySub();
    };
    // path://root_n/shop_sv/view/content/ingot_n/list/ingot_be
    ShopPnlCtrl.prototype.onClickIngot = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        if (this.user.isGuest()) {
            return ViewHelper_1.viewHelper.showMessageBox('ui.guest_buy_ingot_tip', {
                okText: 'ui.button_bind_account',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('common/BindAccount'); },
                cancel: function () { }
            });
        }
        else if (!PayHelper_1.payHelper.isInitFinish()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
        }
        var json = event.target.Data;
        ViewHelper_1.viewHelper.showPnl('common/ShopBuyIngotTip', json, function (ok) {
            if (ok) {
                PayHelper_1.payHelper.buyProduct(json.product_id).then(function (suc) {
                    logger.print('6.buyProduct end. suc=' + suc + ', isValid=' + _this.isValid);
                    if (suc && _this.isValid) {
                        _this.updateShopIngot();
                    }
                });
            }
        });
    };
    // path://root_n/shop_sv/view/content/recommend/recommend_sv/view/content/buy_be@recommend
    ShopPnlCtrl.prototype.onClickBuy = function (event, param) {
        var _this = this;
        var data = event.target.Data;
        if (!data) {
            return;
        }
        var buyType = '';
        if (param === 'recommend') { // 推荐
            buyType = data.type;
            data = data.json;
        }
        if (param === 'city' || buyType === 'city' || param === 'pawn' || buyType === 'pawn') { // 士兵、城市皮肤
            var type_1 = param === 'city' || buyType === 'city' ? 'city_skin' : 'pawn_skin';
            ViewHelper_1.viewHelper.showPnl('menu/CollectionSkinInfo', { type: type_1, list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                    return;
                }
                if (type_1 === 'pawn_skin') {
                    _this.buyPawnSkin(ret.id);
                }
                else if (type_1 === 'city_skin') {
                    _this.buyCitySkin(ret.id);
                }
            });
        }
        else if (param === 'head' || buyType === 'head' || param === 'emoji' || buyType === 'emoji') { // 头像
            var type_2 = param === 'head' || buyType === 'head' ? 'headicon' : 'chat_emoji';
            ViewHelper_1.viewHelper.showPnl('menu/CollectionEmojiInfo', { type: type_2, list: [data] }, function (ret) {
                if (!_this.isValid || !ret) {
                    return;
                }
                else if (GameHelper_1.gameHpr.costDeductTip(ret)) {
                    return;
                }
                if (type_2 === 'headicon') {
                    _this.buyHeadIcon(ret.id);
                }
                else if (type_2 === 'chat_emoji') {
                    _this.buyChatEmoji(ret.id);
                }
            });
        }
    };
    // path://root_n/shop_sv/view/content/card_n/list/card_0/sale_card_nbe
    ShopPnlCtrl.prototype.onClickSaleCard = function (event, _data) {
        var name = event.target.name, data = event.target.Data;
        if (name === '0') { // 订阅
            if (data && data.leftDays === 0 && data.surplusTime > 0) {
                ViewHelper_1.viewHelper.showAlert('toast.subscription_not_end');
            }
            else {
                ViewHelper_1.viewHelper.showPnl('common/SubscriptionDesc', Enums_1.MonthlyCardType.SALE);
            }
        }
        else if (name === '1') { // 领取
            this.getMonthlyCardAward(Enums_1.MonthlyCardType.SALE);
        }
        else if (name === '2') { // 已领取
        }
    };
    // path://root_n/shop_sv/view/content/card_n/list/card_1/super_card_nbe
    ShopPnlCtrl.prototype.onClickSuperCard = function (event, _data) {
        var name = event.target.name, data = event.target.Data;
        if (name === '0') { // 订阅
            if (data && data.leftDays === 0 && data.surplusTime > 0) {
                ViewHelper_1.viewHelper.showAlert('toast.subscription_not_end');
            }
            else {
                ViewHelper_1.viewHelper.showPnl('common/SubscriptionDesc', Enums_1.MonthlyCardType.SUPER);
            }
        }
        else if (name === '1') { // 领取
            this.getMonthlyCardAward(Enums_1.MonthlyCardType.SUPER);
        }
        else if (name === '2') { // 已领取
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // 刷新元宝
    ShopPnlCtrl.prototype.onUpdateIngot = function () {
        var ingot = this.user.getIngot();
        this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.to(ingot);
    };
    // 刷新元宝
    ShopPnlCtrl.prototype.onUpdateGold = function () {
        var gold = this.user.getGold();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.to(gold);
    };
    // 初始化支付完成 刷新下金币
    ShopPnlCtrl.prototype.onInitPayFinish = function () {
        this.updateShopIngot();
        this.checkShowNotFinishOrder();
    };
    // 刷新订阅信息
    ShopPnlCtrl.prototype.onUpdateSubscripion = function () {
        console.log('onUpdateSubscripion has=' + this.user.isHasSubscription());
        this.updateMonthlyCard();
    };
    // 刷新充值次数
    ShopPnlCtrl.prototype.onUpdateRechargeCount = function () {
        this.updateShopIngot();
    };
    // 刷新盲盒
    ShopPnlCtrl.prototype.onUpdateMysteryBox = function () {
        this.updateMysteryBox(this.mysteryBoxNode_.children[0]);
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    ShopPnlCtrl.prototype.initTop = function () {
        var node = this.topNode_.Swih(mc.currWindName === 'lobby' ? 'lobby' : 'main')[0];
        this.goldValLbl = node.Child('gold/val', cc.LabelRollNumber);
        this.ingotValLbl = node.Child('ingot/val', cc.LabelRollNumber);
        var gold = this.user.getGold(), ingot = this.user.getIngot();
        this.goldValLbl.Color(gold < 0 ? '#F26B50' : '#564C49');
        this.goldValLbl.set(gold);
        this.ingotValLbl.Color(ingot < 0 ? '#F26B50' : '#564C49');
        this.ingotValLbl.set(ingot);
    };
    // 
    ShopPnlCtrl.prototype.playRootAnimation = function () {
        this.rootNode_.stopAllActions();
        this.rootNode_.scale = 0.4;
        cc.tween(this.rootNode_).to(0.25, { scale: 1 }, { easing: cc.easing.backOut }).start();
    };
    // 检测未完成的订单
    ShopPnlCtrl.prototype.checkShowNotFinishOrder = function (showToast) {
        return __awaiter(this, void 0, void 0, function () {
            var ok;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!PayHelper_1.payHelper.isInitFinish()) return [3 /*break*/, 2];
                        return [4 /*yield*/, PayHelper_1.payHelper.checkPayInit()];
                    case 1:
                        _a.sent();
                        _a.label = 2;
                    case 2: return [4 /*yield*/, this.user.checkHasNotFinishOrder(PayHelper_1.payHelper.getPlatform())];
                    case 3:
                        ok = _a.sent();
                        if (ok) {
                            ViewHelper_1.viewHelper.showPnl('main/NotFinishOrderTip');
                        }
                        else if (!showToast) {
                        }
                        else if (!PayHelper_1.payHelper.isInitFinish()) {
                            ViewHelper_1.viewHelper.showAlert('toast.please_wait_init_pay');
                        }
                        else {
                            ViewHelper_1.viewHelper.showAlert('toast.no_check_not_finish_order');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 刷新元宝
    ShopPnlCtrl.prototype.updateShopIngot = function () {
        var _this = this;
        var isFinish = PayHelper_1.payHelper.isInitFinish();
        var list = assetsMgr.getJson('recharge').datas.filter(function (m) { return !!m.ignore; });
        var rechargeCountRecord = this.user.getRechargeCountRecord();
        this.ingotNode_.Child('list').Items(list, function (it, json) {
            it.Data = json;
            ResHelper_1.resHelper.loadIcon(json.icon, it.Child('icon/val'), _this.key);
            it.Child('name').setLocaleKey('ui.shop_ingot_name', json.ingot);
            var button = it.Child('button');
            if (button.Child('money').active = isFinish) {
                button.Child('money/val', cc.Label).string = PayHelper_1.payHelper.getProductPriceText(json.product_id);
            }
            var extra = it.Child('mask/extra');
            if (!rechargeCountRecord[json.product_id]) { //首次
                extra.active = true;
                extra.Child('val').setLocaleKey('ui.first_give_ingot', json.ingot);
            }
            else if (extra.active = !!json.extra) {
                extra.Child('val').setLocaleKey('ui.give_ingot', json.extra);
            }
            button.Child('loading').active = !isFinish;
        });
        // 免费
        this.updateFreeGold();
        // 自选
        this.updateOptionalHero();
        //
        this.onUpdateIngot();
        this.onUpdateGold();
    };
    // 刷新推荐
    ShopPnlCtrl.prototype.updateRecommend = function () {
        var _this = this;
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = [];
        var _loop_1 = function (i) {
            var arr = [];
            var data = this_1.RECOMMENDED_LIST[i];
            if (data.type === 'city') {
                arr = this_1.user.getCanBuyCitySkins(serverArea);
            }
            else if (data.type === 'pawn') {
                arr = this_1.user.getCanBuyPawnSkins(serverArea);
            }
            else if (data.type === 'head') {
                arr = this_1.user.getCanBuyHeadIcons(serverArea);
            }
            else if (data.type === 'emoji') {
                arr = this_1.user.getCanBuyChatEmojis(serverArea);
            }
            if (arr.some(function (m) { return m.id === data.id; })) {
                list.push(data);
            }
        };
        var this_1 = this;
        // 先做一次筛选，拥有的就不显示了
        for (var i = 0; i < this.RECOMMENDED_LIST.length; i++) {
            _loop_1(i);
        }
        this.recommendSv_.stopAutoScroll();
        this.recommendSv_.content.y = 0;
        this.recommendSv_.Items(list, function (it, data) {
            var isPawnOrCity = data.type === 'city' || data.type === 'pawn';
            it.Child('icon', cc.Sprite).enabled = isPawnOrCity;
            var icon = it.Child('icon').Swih(isPawnOrCity ? '1' : '0')[0];
            icon.scale = 1;
            var json = null;
            if (data.type === 'pawn') {
                json = assetsMgr.getJsonData('pawnSkin', data.id);
                ResHelper_1.resHelper.loadPawnHeadIcon(json.id, icon, _this.key);
            }
            else if (data.type === 'city') {
                icon.scale = 0.65;
                json = assetsMgr.getJsonData('citySkin', data.id);
                ResHelper_1.resHelper.loadCityIcon(json.id, icon, _this.key);
            }
            else if (data.type === 'head') {
                json = assetsMgr.getJsonData('headIcon', data.id);
                ResHelper_1.resHelper.loadPlayerHead(icon, json.icon, _this.key, true);
            }
            else if (data.type === 'emoji') {
                json = assetsMgr.getJsonData('chatEmoji', data.id);
                ResHelper_1.resHelper.loadEmojiIcon(json.id, icon, _this.key);
            }
            it.Data = { type: data.type, json: json };
            _this.updateCostText(it, json);
            var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
            it.Child('new').active = isNew;
            it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
            it.Child('mask/extra', cc.Sprite).enabled = isNew;
        });
    };
    // 刷新月卡
    ShopPnlCtrl.prototype.updateMonthlyCard = function () {
        var node = this.cardNode_.Child('list');
        var subDatas = this.user.getSubDatas();
        var _loop_2 = function (i) {
            var data = Constant_1.MONTH_CARD[i], item = node.Child('card_' + i);
            var subData = subDatas.find(function (m) { return data.PRODUCT_IDS_ANDROID.includes(m.productId); }) || subDatas.find(function (m) { return data.PRODUCT_IDS_IOS.includes(m.productId); }) || subDatas.find(function (m) { return data.RECHARGES.includes(m.productId); });
            var isBuy = !!subData; // 是否已购买
            var canClaim = isBuy ? (subData.leftDays > 0 && subData.lastAwardTime <= 0) : false; //是否可领取
            var imm = item.Child('imm'), isFirstPay = !this_2.user.getRechargeCountRecord()[data.TYPE];
            imm.Child('mask').active = isFirstPay;
            imm.Child('count', cc.Label).string = 'x' + data.FIRST;
            imm.Child('mask/extra/val').setLocaleKey('ui.first_give_ingot', data.FIRST);
            var isDone = isBuy && !canClaim;
            imm.Child('done').active = isBuy;
            imm.Child('icon').opacity = isBuy ? 150 : 255;
            var day = item.Child('day');
            day.Child('done').active = isDone;
            var tipNode = item.Child('tip').Swih(isBuy ? 'surplus' : 'total')[0];
            if (isBuy) {
                tipNode.Child('val', cc.Label).string = subData.leftDays + '';
            }
            else {
                var total = data.FIRST * 2 + data.DAY * data.DURATION;
                tipNode.Child('g_count', cc.Label).string = total + '';
                var wartoken = tipNode.Child('wartoken'), wtCount = tipNode.Child('wt_count', cc.Label);
                if (wartoken.active = wtCount.node.active = !!data.EXTRA) {
                    wtCount.string = data.EXTRA * data.DURATION + '';
                }
            }
            var notEnd = (subData === null || subData === void 0 ? void 0 : subData.leftDays) === 0 && (subData === null || subData === void 0 ? void 0 : subData.surplusTime) > 0, showBuy = !subData || notEnd;
            if (i === 0) {
                day.Child('icon').opacity = isDone ? 150 : 255;
                day.Child('count', cc.Label).string = 'x' + data.DAY;
                var button = item.Child('sale_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0]; // 0:购买；1：领取；2：已领取
                button.Data = subData;
                button.opacity = notEnd ? 120 : 255; // 天数已尽，但没有到期
            }
            else {
                day.Child('icon').children.forEach(function (m) { return m.opacity = isDone ? 150 : 255; });
                day.Child('count').children.forEach(function (m, i) {
                    if (i === 0) {
                        m.Component(cc.Label).string = 'x' + data.DAY;
                    }
                    else {
                        m.Component(cc.Label).string = 'x' + data.EXTRA;
                    }
                });
                var button = item.Child('super_card_nbe').Swih(canClaim ? '1' : showBuy ? '0' : '2')[0]; // 0:购买；1：领取；2：已领取
                button.Data = subData;
                button.opacity = notEnd ? 120 : 255; // 天数已尽，但没有到期
            }
        };
        var this_2 = this;
        for (var i = 0; i < node.childrenCount; i++) {
            _loop_2(i);
        }
    };
    // 刷新皮肤
    ShopPnlCtrl.prototype.updateSkin = function () {
        return __awaiter(this, void 0, void 0, function () {
            var serverArea, citySkinList, list, arr1, arr2, arr3, mysteryBoxId, timeNode, json, _a, startTime, endTime;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                        citySkinList = this.updateCitySkin(serverArea);
                        list = this.user.getCanBuyPawnSkins(serverArea);
                        arr1 = [], arr2 = [], arr3 = [];
                        mysteryBoxId = 0;
                        list.forEach(function (m) {
                            if (m.cond > 100) {
                                arr3.push(m);
                                mysteryBoxId = m.cond;
                            }
                            else if (m['limit_time_' + serverArea] && m.cond === 4) {
                                arr2.push(m);
                            }
                            else {
                                arr1.push(m);
                            }
                        });
                        arr1.sort(function (a, b) {
                            var aw = 0, bw = 0;
                            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
                            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
                            return bw - aw;
                        });
                        arr2.sort(function (a, b) {
                            var aw = 0, bw = 0;
                            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
                            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
                            return bw - aw;
                        });
                        // 限定皮肤(目前只计划士兵皮肤)
                        if (this.limitedSkinNode_.active = this.skinTabsNode_.Child('skin_tabs_nbe/0').active = arr2.length > 0) {
                            this.updatePawnSkinList(this.limitedSkinNode_, arr2, true);
                            timeNode = this.limitedSkinNode_.Child('title/lay/time'), json = arr2[0];
                            _a = __read((json['limit_time_' + serverArea] || json.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                            timeNode.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime));
                        }
                        // 士兵皮肤
                        this.updatePawnSkinList(this.pawnSkinNode_, arr1, false);
                        if (!(this.mysteryBoxNode_.active = arr3.length > 0)) return [3 /*break*/, 2];
                        return [4 /*yield*/, this.loadMysteryBoxPrefab(mysteryBoxId)];
                    case 1:
                        _b.sent();
                        this.initMysterBox(serverArea, arr3);
                        _b.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 加载盲盒预制
    ShopPnlCtrl.prototype.loadMysteryBoxPrefab = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var serverArea_1, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // 当前开放的盲盒id
                        if (!id) {
                            serverArea_1 = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
                            assetsMgr.getJson('pawnSkin').datas.forEach(function (m) {
                                if (m.cond < 100) {
                                    return;
                                }
                                var _a = __read((m['limit_time_' + serverArea_1] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                                if (startTime && endTime && !GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) {
                                    return; //是否有时间限制
                                }
                                id = m.cond;
                            });
                        }
                        if (!!this.mysteryBoxPrefab) return [3 /*break*/, 2];
                        _a = this;
                        return [4 /*yield*/, assetsMgr.loadTempRes('mysterybox/MYSTERYBOX_' + id, cc.Prefab, this.key)];
                    case 1:
                        _a.mysteryBoxPrefab = _b.sent();
                        _b.label = 2;
                    case 2: return [2 /*return*/, this.mysteryBoxPrefab];
                }
            });
        });
    };
    ShopPnlCtrl.prototype.initMysterBox = function (serverArea, arr) {
        var mysteryboxNode = null;
        if (this.mysteryBoxNode_.childrenCount > 1) {
            mysteryboxNode = this.mysteryBoxNode_.children[0];
        }
        else {
            mysteryboxNode = cc.instantiate2(this.mysteryBoxPrefab, this.mysteryBoxNode_);
        }
        mysteryboxNode.zIndex = -1;
        this.mysteryBoxNode_.Swih(mysteryboxNode.name)[0];
        var json = mysteryboxNode.Data = arr[0];
        var _a = __read((json['limit_time_' + serverArea] || json.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
        var timeNode = mysteryboxNode.Child('time'), timeVal = timeNode.Child('val');
        timeVal.setLocaleKey('ui.mysterybox_limit_time', endTime.split('-').slice(0, 3).join('/'));
        this.updateMysteryBox(mysteryboxNode, serverArea);
        this.addClickEvent(mysteryboxNode.Child('mysterybox_be', cc.Button), 'onClickMysterybox');
        this.addClickEvent(mysteryboxNode.Child('mysterybox_rule_be', cc.Button), 'onClickMysteryboxRule');
        this.addClickEvent(mysteryboxNode.Child('skin_exchange_be', cc.Button), 'onClickSkinExchange');
        if (!storageMgr.loadBool('click_MysteryBox_tab' + json.cond)) {
            ReddotHelper_1.reddotHelper.set('mystery_box', false);
            storageMgr.saveBool('click_MysteryBox_tab' + json.cond, true);
        }
    };
    ShopPnlCtrl.prototype.updateMysteryBox = function (node, serverArea) {
        var _this = this;
        serverArea = serverArea || (GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test');
        var exchangeSv = node.Child('exchange_sv', cc.ScrollView), colorSkins = this.getColorSkin(serverArea);
        exchangeSv.Items(colorSkins, function (it, data) {
            var _a;
            it.Data = data.id;
            var icon = it.Child('icon/val'), mask = it.Child('icon/mask');
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, icon, _this.key);
            ResHelper_1.resHelper.loadPawnHeadIcon(data.id, mask, _this.key);
            var _b = __read(ut.stringToNumbers(data.value, ','), 2), needId = _b[0], needCount = _b[1];
            var hasCount = ((_a = _this.user.getSkinItemList().filter(function (m) { return m.id === needId; })) === null || _a === void 0 ? void 0 : _a.length) || 0;
            mask.Component(cc.Sprite).fillRange = Math.min(1, 1 - hasCount / needCount);
            _this.addClickEvent(it.Component(cc.Button), 'onClickSkinExchange');
        });
    };
    ShopPnlCtrl.prototype.getColorSkin = function (serverArea) {
        var unlockMap = {};
        this.user.getUnlockPawnSkinIds().forEach(function (m) { return unlockMap[m] = true; });
        return assetsMgr.getJson('pawnSkin').datas.filter(function (m) {
            if (!unlockMap[m.id] && m.cond === 5) {
                var _a = __read((m['limit_time_' + serverArea] || m.limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
                if (startTime && endTime && GameHelper_1.gameHpr.checkActivityAutoDate(startTime, endTime)) {
                    return true;
                }
            }
            return false;
        });
    };
    ShopPnlCtrl.prototype.updateCitySkin = function (serverArea) {
        var list = this.user.getCanBuyCitySkins(serverArea);
        var arr1 = [], arr2 = [];
        list.forEach(function (m) {
            // if (m['limit_time_' + serverArea]) {
            // 	arr2.push(m)
            // } else {
            arr1.push(m);
            // }
        });
        arr1.sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.sort + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0);
            bw = b.sort + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0);
            return bw - aw;
        });
        // arr2.sort((a, b) => {
        // 	let aw = 0, bw = 0
        // 	aw = a.sort + (gameHpr.checkShopNewProduct(a) ? a.sort * 10 : 0)
        // 	bw = b.sort + (gameHpr.checkShopNewProduct(b) ? b.sort * 10 : 0)
        // 	return bw - aw
        // })
        this.updateCitySkinList(this.citySkinNode_, arr1);
        // // 季节限定
        // const limitedNode = this.citySkinNode_.Child('limited')
        // if (limitedNode.active = arr2.length > 0) {
        // 	this.updateCitySkinList(limitedNode.Child('list'), arr2)
        // 	const timeNode = limitedNode.Child('time'), timeLbl = timeNode.Child('val', cc.Label), json = arr2[0]
        // 	const [startTime, endTime] = (json['limit_time_' + serverArea] || json.limit_time_hk).split('|')
        // 	timeLbl.setLocaleKey('ui.limited_time_desc', this.getEndDate(endTime, startTime))
        // 	timeLbl._forceUpdateRenderData()
        // 	const w = (timeNode.width - timeLbl.node.width - 16) / 2
        // 	timeNode.Child('0').width = timeNode.Child('1').width = w
        // }
        return list;
    };
    ShopPnlCtrl.prototype.updateCitySkinList = function (node, list) {
        var _this = this;
        var node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        if (node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadCityIcon(json.id, it.Child('icon/val'), _this.key);
                _this.updateCostText(it, json);
                var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
                it.Child('new').active = isNew;
                it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
                it.Child('mask/extra', cc.Sprite).enabled = isNew;
            });
        }
    };
    ShopPnlCtrl.prototype.updatePawnSkinList = function (node, list, isLimited) {
        var _this = this;
        var node_ = null;
        if (isLimited) {
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.name === 'list') {
            node_.Items(list, function (it, json) {
                var _a;
                it.Data = json;
                it.Child('name').setLocaleKey(ut.nameFormator(assetsMgr.lang('pawnText.name_' + json.pawn_id), 5));
                _this.updateCostText(it, json);
                var isNew = GameHelper_1.gameHpr.checkShopNewProduct(json);
                (_a = it.Child('new')) === null || _a === void 0 ? void 0 : _a.setActive(isNew);
                it.Color(isNew ? '#FAEDCD' : '#F1E8D3');
                it.Child('mask/extra', cc.Sprite).enabled = isNew;
                ResHelper_1.resHelper.loadPawnHeadIcon(json.id, it.Child('icon/val'), _this.key);
            });
        }
    };
    ShopPnlCtrl.prototype.updateCostText = function (it, json) {
        var node = it.Child('mask/extra');
        if (json.gold > 0) {
            node.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.GOLD);
            node.Child('val', cc.Label).string = json.gold + '';
        }
        else if (json.ingot > 0) {
            node.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(Enums_1.CType.INGOT);
            node.Child('val', cc.Label).string = json.ingot + '';
        }
        else {
            node.Child('icon').active = false;
        }
    };
    ShopPnlCtrl.prototype.getEndDate = function (endTime, startTime) {
        var year = new Date().getFullYear();
        if (((startTime === null || startTime === void 0 ? void 0 : startTime.split('-')[0]) || '').length !== 4) {
            startTime = year + '-' + startTime;
        }
        if (((endTime === null || endTime === void 0 ? void 0 : endTime.split('-')[0]) || '').length !== 4) {
            var _a = __read(startTime.split('-'), 2), _1 = _a[0], sm = _a[1];
            var em = endTime.split('-')[0];
            if (Number(em) < Number(sm)) {
                year += 1;
            }
            endTime = year + '-' + endTime;
        }
        var _b = __read(endTime === null || endTime === void 0 ? void 0 : endTime.split('-'), 3), endYear = _b[0], endMonth = _b[1], endDay = _b[2];
        return assetsMgr.lang('ui.date', endYear, endMonth, endDay);
    };
    ShopPnlCtrl.prototype.getAddNewTime = function (endTime) {
        var _a = __read(endTime === null || endTime === void 0 ? void 0 : endTime.split('-'), 3), endYear = _a[0], endMonth = _a[1], endDay = _a[2];
        return endMonth + '/' + endDay;
    };
    ShopPnlCtrl.prototype.buyCitySkin = function (id) {
        var _this = this;
        this.user.buyCitySkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateSkin();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'city'; })) {
                    _this.updateRecommend();
                }
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    ShopPnlCtrl.prototype.buyPawnSkin = function (id) {
        var _this = this;
        this.user.buyPawnSkin(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateSkin();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'pawn'; })) {
                    _this.updateRecommend();
                }
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
            }
        });
    };
    // 购买盲盒
    ShopPnlCtrl.prototype.buyMysterybox = function (json) {
        ViewHelper_1.viewHelper.showMessageBox('ui.buy_mysterybox_tip', {
            params: [json.ingot, json.desc],
            ok: function () { return ViewHelper_1.viewHelper.showPnl("activity/MysteryboxShow" + json.cond, json.cond, json.ingot); },
            cancel: function () { },
        });
    };
    // 刷新头像
    ShopPnlCtrl.prototype.updateHeadIcon = function () {
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = this.user.getCanBuyHeadIcons(serverArea).sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.id + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0);
            bw = b.id + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0);
            return bw - aw;
        });
        // 新款头像
        var newHeadIcons = list.filter(function (m) { return !!m['limit_time_' + serverArea]; });
        var date = '';
        if (newHeadIcons.length > 0) {
            var _a = __read((newHeadIcons[0]['limit_time_' + serverArea] || newHeadIcons[0].limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            date = this.getAddNewTime(startTime);
        }
        this.updateHeadIconList(this.newHeadNode_, newHeadIcons, true, date);
        // 经典头像
        var classicHeadIcons = list.filter(function (m) { return !m['limit_time_' + serverArea]; });
        this.updateHeadIconList(this.classicalHeadNode_, classicHeadIcons, false, '');
    };
    ShopPnlCtrl.prototype.updateHeadIconList = function (node, list, isNew, date) {
        var _this = this;
        var node_ = null;
        if (isNew) {
            node.active = list.length > 0;
            node.Child('title/lay/lay/time', cc.Label).string = date;
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.active && node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadPlayerHead(it.Child('icon/val'), json.icon, _this.key, true);
                _this.updateCostText(it, json);
            });
        }
    };
    // 购买头像
    ShopPnlCtrl.prototype.buyHeadIcon = function (id) {
        var _this = this;
        this.user.buyHeadIcon(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateHeadIcon();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'head'; })) {
                    _this.updateRecommend();
                }
            }
        });
    };
    // 刷新表情
    ShopPnlCtrl.prototype.updateChatEmoji = function () {
        var serverArea = GameHelper_1.gameHpr.isRelease ? GameHelper_1.gameHpr.getServerArea() : 'test';
        var list = this.user.getCanBuyChatEmojis(serverArea).sort(function (a, b) {
            var aw = 0, bw = 0;
            aw = a.id + (GameHelper_1.gameHpr.checkShopNewProduct(a) ? a.id * 10 : 0);
            bw = b.id + (GameHelper_1.gameHpr.checkShopNewProduct(b) ? b.id * 10 : 0);
            return bw - aw;
        });
        // 新款表情
        var newEmojis = list.filter(function (m) { return !!m['limit_time_' + serverArea]; });
        var date = '';
        if (newEmojis.length > 0) {
            var _a = __read((newEmojis[0]['limit_time_' + serverArea] || newEmojis[0].limit_time_hk).split('|'), 2), startTime = _a[0], endTime = _a[1];
            date = this.getAddNewTime(startTime);
        }
        this.updateChatEmojiList(this.newEmojiNode_, newEmojis, true, date);
        //经典表情
        var classicEmojis = list.filter(function (m) { return !m['limit_time_' + serverArea]; });
        this.updateChatEmojiList(this.classicalEmojiNode_, classicEmojis, false, '');
    };
    ShopPnlCtrl.prototype.updateChatEmojiList = function (node, list, isNew, date) {
        var _this = this;
        var node_ = null;
        if (isNew) {
            node.active = list.length > 0;
            node.Child('title/lay/lay/time', cc.Label).string = date;
            node_ = node.Child('list');
        }
        else {
            node_ = node.Child('content').Swih(list.length > 0 ? 'list' : 'empty')[0];
        }
        if (node_.active && node_.name === 'list') {
            node_.Items(list, function (it, json) {
                it.Data = json;
                ResHelper_1.resHelper.loadEmojiIcon(json.id, it.Child('icon/val'), _this.key);
                _this.updateCostText(it, json);
            });
        }
    };
    ShopPnlCtrl.prototype.buyChatEmoji = function (id) {
        var _this = this;
        this.user.buyChatEmoji(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.buy_succeed');
                _this.updateChatEmoji();
                if (_this.RECOMMENDED_LIST.some(function (m) { return m.type === 'emoji'; })) {
                    _this.updateRecommend();
                }
            }
        });
    };
    // 购买英雄
    ShopPnlCtrl.prototype.buyHero = function (id) {
        var _this = this;
        this.user.buyHero(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateOptionalHero();
                ViewHelper_1.viewHelper.showGainPortrayalDebris(id, 3);
            }
        });
    };
    // 刷新免费金币
    ShopPnlCtrl.prototype.updateFreeGold = function () {
        var isCanBuy = this.user.isBuyLimitFreeGold();
        this.freeGoldNode_.Child('button').opacity = isCanBuy ? 255 : 120;
        this.freeGoldNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_day' : 'ui.yet_buy_day');
    };
    // 刷新自选英雄
    ShopPnlCtrl.prototype.updateOptionalHero = function () {
        var isCanBuy = this.user.isBuyLimitOptionalHero();
        this.optionalHeroNode_.Child('button').opacity = isCanBuy ? 255 : 120;
        this.optionalHeroNode_.Child('mask/limit/val').setLocaleKey(isCanBuy ? 'ui.limit_buy_count_week' : 'ui.yet_buy_week');
    };
    // 领取订阅的月卡奖励
    ShopPnlCtrl.prototype.getMonthlyCardAward = function (type) {
        var _this = this;
        this.user.reqGetMonthlyCardAward(type).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                _this.updateMonthlyCard();
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
            }
        });
    };
    ShopPnlCtrl.prototype.onClickMysterybox = function (event, data) {
        var _this = this;
        var node = event.target.parent;
        var json = node.Data;
        if (json && json.cond > 100) {
            if (this.user.getIngot() < json.ingot) {
                return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
            }
            else if (!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
                ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', json.cond, function () { return _this.isValid && _this.buyMysterybox(json); });
            }
            else {
                this.buyMysterybox(json);
            }
        }
    };
    ShopPnlCtrl.prototype.onClickMysteryboxRule = function (event, data) {
        audioMgr.playSFX('click');
        var id = this.mysteryBoxNode_.children[0].Data.cond;
        id && ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', id);
    };
    ShopPnlCtrl.prototype.onClickSkinExchange = function (event, _data) {
        var data = event.target.Data;
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('common/SkinExchange', data);
    };
    ShopPnlCtrl.prototype.onScrolling = function (event) {
        var height = event.node.height / 2, content = event.content, skinY = this.limitedSkinNode_.active ? this.limitedSkinNode_.y : this.citySkinNode_.y, socialY = this.newHeadNode_.active ? this.newHeadNode_.y : this.classicalHeadNode_.y, showCard = content.y >= Math.abs(this.cardNode_.y + height) && content.y < Math.abs(skinY + height), showSkin = content.y >= Math.abs(skinY + height) && content.y < Math.abs(socialY + height), showSocial = content.y >= Math.abs(socialY + height);
        if (this.skinTabsNode_.active !== showSkin) {
            this.skinTabsNode_.active = showSkin;
        }
        if (this.socialTabsNode_.active !== showSocial) {
            this.socialTabsNode_.active = showSocial;
        }
        event.Child('view').height = (showSkin || showSocial) ? this.preViewHeight - this.skinTabsNode_.height : this.preViewHeight;
        var tab = showCard ? 1 : showSkin ? 2 : showSocial ? 3 : 0;
        if (this.curTab !== tab) {
            this.curTab = tab;
            this.tabsTc_.Swih(tab);
            this.user.setTempPreferenceData(this.PKEY_TAB, tab);
        }
    };
    ShopPnlCtrl.prototype.throttle = function (func, delay) {
        var timer = null;
        return function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            if (!timer) {
                func.apply(this, args);
                timer = setTimeout(function () {
                    timer = null;
                }, delay);
            }
        };
    };
    ShopPnlCtrl.prototype.update = function (dt) {
        if (this.drawCount < this.funcList.length) {
            this.frameCount++;
            if (this.frameCount > this.drawRate) {
                this.frameCount = 0;
                this.funcList[this.drawCount].bind(this)();
                this.drawCount++;
            }
        }
    };
    ShopPnlCtrl = __decorate([
        ccclass
    ], ShopPnlCtrl);
    return ShopPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ShopPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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