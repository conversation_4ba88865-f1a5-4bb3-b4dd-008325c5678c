"use strict";
cc._RF.push(module, 'ff88aonsGdNPo3LtJ/+WF7j', 'DonateAncientLvPnlCtrl');
// app/script/view/build/DonateAncientLvPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var DonateAncientLvPnlCtrl = /** @class */ (function (_super) {
    __extends(DonateAncientLvPnlCtrl, _super);
    function DonateAncientLvPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.iconSpr_ = null; // path://root/content/bg/icon_s
        _this.inputEb_ = null; // path://root/content/bg/input_eb_ebee
        //@end
        _this.type = null;
        _this.maxCount = 0;
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    DonateAncientLvPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    DonateAncientLvPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    DonateAncientLvPnlCtrl.prototype.onEnter = function (type, maxCount, cb) {
        this.type = type;
        this.maxCount = maxCount;
        this.cb = cb;
        this.inputEb_.string = '';
        this.inputEb_.setPlaceholder('ui.max_donate_count', 'f_m', maxCount);
        this.iconSpr_.spriteFrame = ResHelper_1.resHelper.getResIcon(type);
    };
    DonateAncientLvPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    DonateAncientLvPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    DonateAncientLvPnlCtrl.prototype.onClickOk = function (event, data) {
        var count = Number(this.inputEb_.string.trim());
        if (!count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLEASE_INPUT_RES_COUNT);
        }
        else if (count > this.maxCount) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.ANCIETN_CONTRIBUTE_RES_LIMIT);
        }
        else if (GameHelper_1.gameHpr.getCountByCType(this.type) < count) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        this.cb && this.cb(count);
        this.hide();
    };
    // path://root/content/bg/input_eb_ebee
    DonateAncientLvPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        var count = Number(event.string.trim());
        if (!count) {
            event.string = '';
            return;
        }
        var cnt = count;
        if (count > this.maxCount) {
            cnt = this.maxCount;
        }
        var ac = GameHelper_1.gameHpr.getCountByCType(this.type);
        if (ac < cnt) {
            cnt = ac;
        }
        if (cnt !== count) {
            event.string = '' + cnt;
        }
    };
    // path://root/content/max_be
    DonateAncientLvPnlCtrl.prototype.onClickMax = function (event, data) {
        this.inputEb_.string = Math.min(this.maxCount, GameHelper_1.gameHpr.getCountByCType(this.type)) + '';
    };
    DonateAncientLvPnlCtrl = __decorate([
        ccclass
    ], DonateAncientLvPnlCtrl);
    return DonateAncientLvPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = DonateAncientLvPnlCtrl;

cc._RF.pop();