
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/CCMvc.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4368dJAEQJPFY47vGhK2Ons', 'CCMvc');
// app/core/CCMvc.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var BasePnlCtrl_1 = require("./base/BasePnlCtrl");
var BaseWindCtrl_1 = require("./base/BaseWindCtrl");
var BaseNoticeCtrl_1 = require("./base/BaseNoticeCtrl");
var BaseWdtCtrl_1 = require("./base/BaseWdtCtrl");
var BaseLogCtrl_1 = require("./base/BaseLogCtrl");
var BaseModel_1 = require("./base/BaseModel");
var WindCtrlMgr_1 = require("./manage/WindCtrlMgr");
var ViewCtrlMgr_1 = require("./manage/ViewCtrlMgr");
var ModelMgr_1 = require("./manage/ModelMgr");
var CoreEventType_1 = require("./event/CoreEventType");
var ViewLayerCtrl_1 = require("./layer/ViewLayerCtrl");
var WindLayerCtrl_1 = require("./layer/WindLayerCtrl");
var NoticeLayerCtrl_1 = require("./layer/NoticeLayerCtrl");
var NoticeCtrlMgr_1 = require("./manage/NoticeCtrlMgr");
var CCMvc = /** @class */ (function () {
    function CCMvc() {
        this.GameNameSpace = 'game';
        this.BasePnlCtrl = BasePnlCtrl_1.default;
        this.BaseWindCtrl = BaseWindCtrl_1.default;
        this.BaseNoticeCtrl = BaseNoticeCtrl_1.default;
        this.BaseWdtCtrl = BaseWdtCtrl_1.default;
        this.BaseLogCtrl = BaseLogCtrl_1.default;
        this.BaseModel = BaseModel_1.default;
        this.Event = null;
        this.__modelMgr = new ModelMgr_1.default();
        this.__canChangeLang = false; //是否实时切换
        this.__lang = ''; //当前语言
        this.__windLayerCtrl = null;
        this.__viewLayerCtrl = null;
        this.__noticeLayerCtrl = null;
        this.__lockNode = null;
        this.__locks = []; //锁列表
        this.__temp_models = {};
    }
    CCMvc.prototype.init = function (name, root, opts) {
        this.GameNameSpace = name || 'game';
        this.__lang = (opts === null || opts === void 0 ? void 0 : opts.lang) || this.__lang;
        this.__canChangeLang = !!(opts === null || opts === void 0 ? void 0 : opts.changeLang);
        // 清理所有事件
        eventCenter.clean();
        // 创建各个视图层
        this.__windLayerCtrl = this.createNode('Wind', 'default', root, 1).addComponent(WindLayerCtrl_1.default).__init(new WindCtrlMgr_1.default());
        this.__viewLayerCtrl = this.createNode('View', 'ui', root, 2).addComponent(ViewLayerCtrl_1.default).__init(new ViewCtrlMgr_1.default());
        this.__noticeLayerCtrl = this.createNode('Notice', 'ui', root, 3).addComponent(NoticeLayerCtrl_1.default).__init(new NoticeCtrlMgr_1.default());
        // 设置pnl层级配置
        this.__viewLayerCtrl.setPnlIndexConf((opts === null || opts === void 0 ? void 0 : opts.pnlIndexConf) || {});
        // 创建锁定触摸节点
        this.__lockNode = this.createNode('LockNode', 'ui', root, 4);
        this.__lockNode.addComponent(cc.BlockInputEvents);
        this.__lockNode.active = false;
        // 事件
        this.Event = CoreEventType_1.default;
        // 初始化模型
        if (this.__temp_models) {
            this.__modelMgr.init(this.__temp_models);
            this.__modelMgr.create();
            this.__temp_models = undefined;
        }
        else if (this.__modelMgr) {
            this.__modelMgr.create();
        }
        // logger.info('mvc core init!')
        var win = cc.winSize, view = cc.view.getFrameSize(), safe = cc.sys.getSafeAreaRect();
        logger.info('WinSize=' + win.width + ',' + win.height, 'ViewSize=' + view.width + ',' + view.height, 'SafeSzie=' + safe.width + ',' + safe.height);
    };
    CCMvc.prototype.createNode = function (name, group, root, zIndex) {
        var node = root.FindChild(name);
        if (node) {
            node.destroy();
        }
        node = new cc.Node(name);
        node.parent = root;
        node.zIndex = zIndex;
        node.width = cc.winSize.width;
        node.height = cc.winSize.height;
        node.group = group;
        return node;
    };
    CCMvc.prototype.getWindNode = function () { return this.__windLayerCtrl.node; };
    CCMvc.prototype.getViewNode = function () { return this.__viewLayerCtrl.node; };
    CCMvc.prototype.getNoticeNode = function () { return this.__noticeLayerCtrl.node; };
    Object.defineProperty(CCMvc.prototype, "currWind", {
        // 设置获取当前wind
        get: function () {
            return this.__windLayerCtrl.getCurrWind();
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CCMvc.prototype, "currWindName", {
        // 获取当前wind名字
        get: function () {
            var _a;
            return ((_a = this.__windLayerCtrl.getCurrWind()) === null || _a === void 0 ? void 0 : _a.key) || '';
        },
        enumerable: false,
        configurable: true
    });
    // 获取当前打开的列表
    CCMvc.prototype.getOpenPnls = function () {
        return this.__viewLayerCtrl.getOpenPnls();
    };
    // 获取当前正在打开的列表
    CCMvc.prototype.getLoadQueues = function () {
        return this.__viewLayerCtrl.getLoadQueues();
    };
    // 锁定触摸
    CCMvc.prototype.lockTouch = function (key) {
        this.__lockNode.active = true;
        if (this.__locks.has(key)) {
            logger.error('repeat lock! ' + key);
        }
        else {
            this.__locks.push(key);
        }
        // cc.error('lockTouch', key)
    };
    // 解锁触摸
    CCMvc.prototype.unlockTouch = function (key) {
        this.__locks.remove(key);
        this.__lockNode.active = this.__locks.length > 0;
        // cc.error('unlockTouch', key)
    };
    // 当前是否触摸状态
    CCMvc.prototype.isLockTouch = function () {
        return this.__lockNode.active;
    };
    Object.defineProperty(CCMvc.prototype, "lang", {
        // ----------------------------------多语言------------------------------------------
        // 获取当前语言
        get: function () {
            if (!this.__lang) {
                this.__lang = 'cn';
            }
            return this.__lang;
        },
        // 切换语言
        set: function (val) {
            if (this.__lang !== val) {
                this.__lang = val;
                localStorage.setItem('__' + this.GameNameSpace + '_lang__', val);
                assetsMgr.changeLangJson(val);
            }
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(CCMvc.prototype, "canChangeLang", {
        get: function () { return this.__canChangeLang; },
        set: function (val) {
            this.__canChangeLang = val;
        },
        enumerable: false,
        configurable: true
    });
    // ----------------------------------模块管理------------------------------------------
    // 添加模型装饰器
    CCMvc.prototype.addmodel = function (type) {
        var self = this;
        return function (target) {
            self.__temp_models[type] = target;
        };
    };
    CCMvc.prototype.getModel = function (key) {
        return this.__modelMgr.get(key);
    };
    CCMvc.prototype.pushModel = function () {
        var _a;
        var params = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            params[_i] = arguments[_i];
        }
        (_a = this.__modelMgr).add.apply(_a, __spread(params));
    };
    CCMvc.prototype.resetModel = function (model) {
        this.__modelMgr.reset(model);
    };
    return CCMvc;
}());
// @ts-ignore
window['mc'] = new CCMvc();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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