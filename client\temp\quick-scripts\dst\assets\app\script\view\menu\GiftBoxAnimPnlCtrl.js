
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/GiftBoxAnimPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8cdf0+vwA5MIJuPHEzqnlNn', 'GiftBoxAnimPnlCtrl');
// app/script/view/menu/GiftBoxAnimPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var GiftBoxAnimPnlCtrl = /** @class */ (function (_super) {
    __extends(GiftBoxAnimPnlCtrl, _super);
    function GiftBoxAnimPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_n
        _this.boxNode_ = null; // path://box_n
        _this.skinNode_ = null; // path://skin_n
        _this.loadingNode_ = null; // path://loading_n
        return _this;
    }
    //@end
    GiftBoxAnimPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    GiftBoxAnimPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    GiftBoxAnimPnlCtrl.prototype.onEnter = function (boxId, skinId, target) {
        this.load(boxId, skinId, target);
    };
    GiftBoxAnimPnlCtrl.prototype.onRemove = function () {
        this.boxNode_.removeAllChildren();
    };
    GiftBoxAnimPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    GiftBoxAnimPnlCtrl.prototype.load = function (boxId, skinId, target) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var light, pfb, node, anim, pos;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.closeNode_.active = false;
                        this.loadingNode_.active = true;
                        this.skinNode_.opacity = 0;
                        light = this.skinNode_.Child('light');
                        light.opacity = 0;
                        this.boxNode_.removeAllChildren();
                        ResHelper_1.resHelper.loadPawnHeadIcon(skinId, this.skinNode_.Child('val'), this.key);
                        return [4 /*yield*/, assetsMgr.loadTempRes('other/GIFTBOX_' + boxId, cc.Prefab, this.key)];
                    case 1:
                        pfb = _b.sent();
                        if (!pfb || !this.isValid) {
                            this.hide();
                            (_a = this.loadingNode_) === null || _a === void 0 ? void 0 : _a.setActive(false);
                            return [2 /*return*/];
                        }
                        node = cc.instantiate2(pfb, this.boxNode_);
                        node.setPosition(0, 0);
                        node.scale = 1;
                        anim = node.Child('val', cc.Animation);
                        if (!target) return [3 /*break*/, 3];
                        this.skinNode_.opacity = 255;
                        this.skinNode_.scale = 1.3;
                        this.skinNode_.y = 140;
                        cc.tween(this.skinNode_).to(0.2, { y: 0, scale: 0 }).start();
                        return [4 /*yield*/, anim.playAsync('giftbox_' + boxId + '_pack')];
                    case 2:
                        _b.sent();
                        pos = ut.convertToNodeAR(target, node.parent);
                        cc.tween(node)
                            .to(0.5, { x: pos.x, y: pos.y, scale: 0 }, { easing: cc.easing.sineIn })
                            .call(function () { return _this.isValid && _this.hide(); })
                            .start();
                        return [3 /*break*/, 5];
                    case 3:
                        cc.tween(this.skinNode_)
                            .delay(0.55)
                            .call(function () {
                            _this.skinNode_.opacity = 255;
                            _this.skinNode_.scale = 0.1;
                            _this.skinNode_.y = -20;
                        })
                            .to(0.15, { y: 120, scale: 1.3 })
                            .call(function () {
                            cc.tween(light).to(0.1, { opacity: 255 }).start();
                        })
                            .start();
                        return [4 /*yield*/, anim.playAsync('giftbox_' + boxId + '_open')];
                    case 4:
                        _b.sent();
                        this.loadingNode_.active = false;
                        this.closeNode_.active = true;
                        _b.label = 5;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    GiftBoxAnimPnlCtrl = __decorate([
        ccclass
    ], GiftBoxAnimPnlCtrl);
    return GiftBoxAnimPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = GiftBoxAnimPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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