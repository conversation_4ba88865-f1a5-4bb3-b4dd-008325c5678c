"use strict";
cc._RF.push(module, 'ffccbRJuVhBd56QaYT9ilwG', 'SelectArrowsCmpt');
// app/script/view/cmpt/common/SelectArrowsCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 箭头选择
var SelectArrowsCmpt = /** @class */ (function (_super) {
    __extends(SelectArrowsCmpt, _super);
    function SelectArrowsCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.speed = 13;
        _this.items = [];
        return _this;
    }
    SelectArrowsCmpt.prototype.onLoad = function () {
        this.items = this.node.children.map(function (m) { return m.FindChild('val'); });
    };
    SelectArrowsCmpt.prototype.update = function (dt) {
        var sx = dt * this.speed;
        this.items.forEach(function (m) { return m.x += sx; });
        var it = this.items[0];
        if (it.x >= 4) {
            it.x = 4;
            this.speed *= -1;
        }
        else if (it.x < 0) {
            it.x = 0;
            this.speed *= -1;
        }
    };
    SelectArrowsCmpt = __decorate([
        ccclass
    ], SelectArrowsCmpt);
    return SelectArrowsCmpt;
}(cc.Component));
exports.default = SelectArrowsCmpt;

cc._RF.pop();