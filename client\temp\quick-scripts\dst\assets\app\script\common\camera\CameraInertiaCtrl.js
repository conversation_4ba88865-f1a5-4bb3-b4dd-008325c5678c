
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/camera/CameraInertiaCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f0276a9NGRMRLJzycklcpOk', 'CameraInertiaCtrl');
// app/script/common/camera/CameraInertiaCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var CameraCtrl_1 = require("./CameraCtrl");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var VEC2_ZERO = cc.v2(0, 0);
var EPSILON = 1e-4;
var MOVEMENT_FACTOR = 0.7;
var NUMBER_OF_GATHERED_TOUCHES_FOR_MOVE_SPEED = 5;
var quintEaseOut = function (time) {
    time -= 1;
    return (time * time * time * time * time + 1);
};
var getTimeInMilliseconds = function () {
    var currentTime = new Date();
    return currentTime.getMilliseconds();
};
/**
 * 摄像机惯性
 */
var CameraInertiaCtrl = /** @class */ (function (_super) {
    __extends(CameraInertiaCtrl, _super);
    function CameraInertiaCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.brake = 0.7;
        _this._touchMoveDisplacements = [];
        _this._touchMoveTimeDeltas = [];
        _this._autoScrolling = false;
        _this._tempPoint = cc.v2();
        _this._tempPrevPoint = cc.v2();
        return _this;
    }
    CameraInertiaCtrl.prototype.dragStart = function () {
        this._autoScrolling = false;
        this._touchMovePreviousTimestamp = getTimeInMilliseconds();
        this._touchMoveDisplacements.length = 0;
        this._touchMoveTimeDeltas.length = 0;
    };
    CameraInertiaCtrl.prototype.drag = function (touch) {
        var deltaMove = this._getLocalAxisAlignDelta(touch);
        this._gatherTouchMove(deltaMove);
    };
    CameraInertiaCtrl.prototype.dragEnd = function (touch) {
        this._handleReleaseLogic(touch);
    };
    CameraInertiaCtrl.prototype.stopAutoScroll = function () {
        this._autoScrolling = false;
        this._autoScrollAccumulatedTime = this._autoScrollTotalTime;
    };
    CameraInertiaCtrl.prototype._handleReleaseLogic = function (touch) {
        var delta = this._getLocalAxisAlignDelta(touch);
        this._gatherTouchMove(delta);
        this._processInertiaScroll();
    };
    CameraInertiaCtrl.prototype._processInertiaScroll = function () {
        var touchMoveVelocity = this._calculateTouchMoveVelocity();
        if (touchMoveVelocity.mag() > 7 && this.brake < 1) {
            this._startInertiaScroll(touchMoveVelocity);
        }
    };
    CameraInertiaCtrl.prototype._startInertiaScroll = function (touchMoveVelocity) {
        var inertiaTotalMovement = touchMoveVelocity.mul(MOVEMENT_FACTOR);
        this._startAttenuatingAutoScroll(inertiaTotalMovement, touchMoveVelocity);
    };
    CameraInertiaCtrl.prototype._calculateAttenuatedFactor = function (distance) {
        if (this.brake <= 0) {
            return (1 - this.brake);
        }
        //attenuate formula from: http://learnopengl.com/#!Lighting/Light-casters
        return (1 - this.brake) * (1 / (1 + distance * 0.000014 + distance * distance * 0.000000008));
    };
    CameraInertiaCtrl.prototype._calculateAutoScrollTimeByInitalSpeed = function (initalSpeed) {
        return Math.sqrt(Math.sqrt(initalSpeed / 5));
    };
    CameraInertiaCtrl.prototype._startAttenuatingAutoScroll = function (deltaMove, initialVelocity) {
        var time = this._calculateAutoScrollTimeByInitalSpeed(initialVelocity.mag());
        var targetDelta = deltaMove.normalize();
        var maxRange = CameraCtrl_1.cameraCtrl.getMaxRange();
        var totalMoveWidth = maxRange.x;
        var totalMoveHeight = maxRange.y;
        var attenuatedFactorX = this._calculateAttenuatedFactor(totalMoveWidth);
        var attenuatedFactorY = this._calculateAttenuatedFactor(totalMoveHeight);
        targetDelta = cc.v2(targetDelta.x * totalMoveWidth * (1 - this.brake) * attenuatedFactorX, targetDelta.y * totalMoveHeight * attenuatedFactorY * (1 - this.brake));
        var originalMoveLength = deltaMove.mag();
        var factor = targetDelta.mag() / originalMoveLength;
        targetDelta = targetDelta.add(deltaMove);
        if (this.brake > 0 && factor > 7) {
            factor = Math.sqrt(factor);
            targetDelta = deltaMove.mul(factor).add(deltaMove);
        }
        if (this.brake > 0 && factor > 3) {
            factor = 3;
            time = time * factor;
        }
        if (this.brake === 0 && factor > 1) {
            time = time * factor;
        }
        this._startAutoScroll(targetDelta, time, true);
    };
    CameraInertiaCtrl.prototype._startAutoScroll = function (deltaMove, timeInSecond, attenuated) {
        var adjustedDeltaMove = deltaMove;
        this._autoScrolling = true;
        this._autoScrollTargetDelta = adjustedDeltaMove;
        this._autoScrollAttenuate = attenuated;
        this._autoScrollStartPosition = cc.v2(CameraCtrl_1.cameraCtrl.getPosition());
        this._autoScrollTotalTime = timeInSecond;
        this._autoScrollAccumulatedTime = 0;
    };
    CameraInertiaCtrl.prototype.update = function (dt) {
        if (this._autoScrolling) {
            this._processAutoScrolling(dt);
        }
    };
    CameraInertiaCtrl.prototype._processAutoScrolling = function (dt) {
        this._autoScrollAccumulatedTime += dt;
        var percentage = Math.min(1, this._autoScrollAccumulatedTime / this._autoScrollTotalTime);
        if (this._autoScrollAttenuate) {
            percentage = quintEaseOut(percentage);
        }
        var newPosition = this._autoScrollStartPosition.add(this._autoScrollTargetDelta.mul(percentage));
        var reachedEnd = Math.abs(percentage - 1) <= EPSILON;
        if (CameraCtrl_1.cameraCtrl.isOutOfBoudary(newPosition)) {
            reachedEnd = true;
        }
        else {
            CameraCtrl_1.cameraCtrl.setPosition(newPosition);
        }
        if (reachedEnd) {
            CameraCtrl_1.cameraCtrl.recordPosition();
            this._autoScrolling = false;
        }
    };
    CameraInertiaCtrl.prototype._getLocalAxisAlignDelta = function (touch) {
        CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(touch.getLocation(), this._tempPoint);
        CameraCtrl_1.cameraCtrl.getScreenToWorldPoint(touch.getPreviousLocation(), this._tempPrevPoint);
        return this._tempPrevPoint.sub(this._tempPoint);
    };
    CameraInertiaCtrl.prototype._gatherTouchMove = function (delta) {
        while (this._touchMoveDisplacements.length >= NUMBER_OF_GATHERED_TOUCHES_FOR_MOVE_SPEED) {
            this._touchMoveDisplacements.shift();
            this._touchMoveTimeDeltas.shift();
        }
        this._touchMoveDisplacements.push(delta);
        var timeStamp = getTimeInMilliseconds();
        this._touchMoveTimeDeltas.push((timeStamp - this._touchMovePreviousTimestamp) / 1000);
        this._touchMovePreviousTimestamp = timeStamp;
    };
    CameraInertiaCtrl.prototype._calculateTouchMoveVelocity = function () {
        var totalTime = 0;
        totalTime = this._touchMoveTimeDeltas.reduce(function (a, b) {
            return a + b;
        }, totalTime);
        if (totalTime <= 0 || totalTime >= 0.5) {
            return cc.v2(0, 0);
        }
        var totalMovement = cc.v2(0, 0);
        totalMovement = this._touchMoveDisplacements.reduce(function (a, b) {
            return a.add(b);
        }, totalMovement);
        var result = cc.v2(totalMovement.x * (1 - this.brake) / totalTime, totalMovement.y * (1 - this.brake) / totalTime);
        var factor = 3;
        var absX = Math.abs(result.x);
        var absY = Math.abs(result.y);
        if (absX > absY * factor) {
            result.y = 0;
        }
        else if (absY > absX * factor) {
            result.x = 0;
        }
        return result;
    };
    __decorate([
        property(cc.Float)
    ], CameraInertiaCtrl.prototype, "brake", void 0);
    CameraInertiaCtrl = __decorate([
        ccclass
    ], CameraInertiaCtrl);
    return CameraInertiaCtrl;
}(cc.Component));
exports.default = CameraInertiaCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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