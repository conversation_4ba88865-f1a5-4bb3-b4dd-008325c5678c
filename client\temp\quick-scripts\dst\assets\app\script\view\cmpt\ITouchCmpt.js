
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/cmpt/ITouchCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dc74cGZyJVJFKV/bVNnEpLp', 'ITouchCmpt');
// app/script/view/cmpt/ITouchCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
/**
 * 用于点击地图上面的触摸
 */
var ITouchCmpt = /** @class */ (function (_super) {
    __extends(ITouchCmpt, _super);
    function ITouchCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.interactable = true;
        return _this;
    }
    ITouchCmpt.prototype.clean = function () {
    };
    ITouchCmpt = __decorate([
        ccclass
    ], ITouchCmpt);
    return ITouchCmpt;
}(cc.Component));
exports.default = ITouchCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNtcHRcXElUb3VjaENtcHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU0sSUFBQSxLQUF3QixFQUFFLENBQUMsVUFBVSxFQUFuQyxPQUFPLGFBQUEsRUFBRSxRQUFRLGNBQWtCLENBQUM7QUFFNUM7O0dBRUc7QUFFSDtJQUF3Qyw4QkFBWTtJQUFwRDtRQUFBLHFFQU9DO1FBTFUsa0JBQVksR0FBWSxJQUFJLENBQUE7O0lBS3ZDLENBQUM7SUFIVSwwQkFBSyxHQUFaO0lBRUEsQ0FBQztJQU5nQixVQUFVO1FBRDlCLE9BQU87T0FDYSxVQUFVLENBTzlCO0lBQUQsaUJBQUM7Q0FQRCxBQU9DLENBUHVDLEVBQUUsQ0FBQyxTQUFTLEdBT25EO2tCQVBvQixVQUFVIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcclxuXHJcbi8qKlxyXG4gKiDnlKjkuo7ngrnlh7vlnLDlm77kuIrpnaLnmoTop6bmkbhcclxuICovXHJcbkBjY2NsYXNzXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIElUb3VjaENtcHQgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xyXG5cclxuICAgIHB1YmxpYyBpbnRlcmFjdGFibGU6IGJvb2xlYW4gPSB0cnVlXHJcblxyXG4gICAgcHVibGljIGNsZWFuKCkge1xyXG5cclxuICAgIH1cclxufSJdfQ==