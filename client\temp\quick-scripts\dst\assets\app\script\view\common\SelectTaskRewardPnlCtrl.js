
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/SelectTaskRewardPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9acdf+8I3ZNC6cMA+Mc+6wC', 'SelectTaskRewardPnlCtrl');
// app/script/view/common/SelectTaskRewardPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectTaskRewardPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectTaskRewardPnlCtrl, _super);
    function SelectTaskRewardPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentTc_ = null; // path://root/content_tc
        //@end
        _this.task = null;
        return _this;
    }
    SelectTaskRewardPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectTaskRewardPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectTaskRewardPnlCtrl.prototype.onEnter = function (task) {
        this.task = task;
        this.contentTc_.node.Items(task.rewards, function (it, data, i) {
            it.Data = i;
            it.Child('icon', cc.Sprite).spriteFrame = ResHelper_1.resHelper.getResIcon(data.type);
            it.Child('count', cc.Label).string = 'x' + data.count;
        });
        this.contentTc_.Swih('');
    };
    SelectTaskRewardPnlCtrl.prototype.onRemove = function () {
    };
    SelectTaskRewardPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/ok_be
    SelectTaskRewardPnlCtrl.prototype.onClickOk = function (event, data) {
        var _this = this;
        var i = this.contentTc_.toggleItems.findIndex(function (m) { return m.isChecked; });
        var reward = this.task.rewards[i];
        if (!reward) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_select_one_reward');
        }
        var items = GameHelper_1.gameHpr.checkRewardFull([reward]);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.claimAward(i, reward); });
        }
        this.claimAward(i, reward);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    SelectTaskRewardPnlCtrl.prototype.claimAward = function (i, reward) {
        var _this = this;
        var _a;
        var player = GameHelper_1.gameHpr.player, type = (_a = this.task.json) === null || _a === void 0 ? void 0 : _a.type;
        var claimTaskRewardFunc = null;
        if (type === 20) { //每日任务
            claimTaskRewardFunc = player.claimTodayTaskReward.bind(player);
        }
        else if (type === 30) { //其他任务
            claimTaskRewardFunc = player.claimOtherTaskReward.bind(player);
        }
        else {
            return;
        }
        claimTaskRewardFunc(this.task.id, 0, i + 1).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            GameHelper_1.gameHpr.addGainMassage(reward);
            // viewHelper.hidePnl('common/GuideTask')
            if (_this.isValid) {
                _this.hide();
            }
        });
    };
    SelectTaskRewardPnlCtrl = __decorate([
        ccclass
    ], SelectTaskRewardPnlCtrl);
    return SelectTaskRewardPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectTaskRewardPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcU2VsZWN0VGFza1Jld2FyZFBubEN0cmwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsNkRBQXlEO0FBQ3pELDJEQUEwRDtBQUMxRCw2REFBNEQ7QUFJcEQsSUFBQSxPQUFPLEdBQUssRUFBRSxDQUFDLFVBQVUsUUFBbEIsQ0FBbUI7QUFHbEM7SUFBcUQsMkNBQWM7SUFBbkU7UUFBQSxxRUF5RUM7UUF2RUcsMEJBQTBCO1FBQ2xCLGdCQUFVLEdBQXVCLElBQUksQ0FBQSxDQUFDLHlCQUF5QjtRQUN2RSxNQUFNO1FBRUUsVUFBSSxHQUFZLElBQUksQ0FBQTs7SUFtRWhDLENBQUM7SUFqRVUsaURBQWUsR0FBdEI7UUFDSSxPQUFPLEVBQUUsQ0FBQTtJQUNiLENBQUM7SUFFWSwwQ0FBUSxHQUFyQjs7Ozs7O0tBQ0M7SUFFTSx5Q0FBTyxHQUFkLFVBQWUsSUFBYTtRQUN4QixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQztZQUNqRCxFQUFFLENBQUMsSUFBSSxHQUFHLENBQUMsQ0FBQTtZQUNYLEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxXQUFXLEdBQUcscUJBQVMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFBO1lBQ3pFLEVBQUUsQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsR0FBRyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUE7UUFDekQsQ0FBQyxDQUFDLENBQUE7UUFDRixJQUFJLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtJQUM1QixDQUFDO0lBRU0sMENBQVEsR0FBZjtJQUNBLENBQUM7SUFFTSx5Q0FBTyxHQUFkO0lBQ0EsQ0FBQztJQUVELGlIQUFpSDtJQUNqSCwyQkFBMkI7SUFFM0Isb0JBQW9CO0lBQ3BCLDJDQUFTLEdBQVQsVUFBVSxLQUEwQixFQUFFLElBQVk7UUFBbEQsaUJBV0M7UUFWRyxJQUFNLENBQUMsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDLFdBQVcsQ0FBQyxTQUFTLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsU0FBUyxFQUFYLENBQVcsQ0FBQyxDQUFBO1FBQ2pFLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFBO1FBQ25DLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDVCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLGdDQUFnQyxDQUFDLENBQUE7U0FDaEU7UUFDRCxJQUFNLEtBQUssR0FBRyxvQkFBTyxDQUFDLGVBQWUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUE7UUFDL0MsSUFBSSxLQUFLLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRTtZQUNsQixPQUFPLHVCQUFVLENBQUMsT0FBTyxDQUFDLG1CQUFtQixFQUFFLEtBQUssRUFBRSxVQUFDLEVBQVcsSUFBTyxFQUFFLElBQUksS0FBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUEsQ0FBQyxDQUFDLENBQUMsQ0FBQTtTQUMvRztRQUNELElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFBO0lBQzlCLENBQUM7SUFDRCxNQUFNO0lBQ04saUhBQWlIO0lBRWpILGlIQUFpSDtJQUV6Ryw0Q0FBVSxHQUFsQixVQUFtQixDQUFTLEVBQUUsTUFBZ0I7UUFBOUMsaUJBb0JDOztRQW5CRyxJQUFNLE1BQU0sR0FBRyxvQkFBTyxDQUFDLE1BQU0sRUFBRSxJQUFJLFNBQUcsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLDBDQUFFLElBQUksQ0FBQTtRQUMxRCxJQUFJLG1CQUFtQixHQUFhLElBQUksQ0FBQTtRQUN4QyxJQUFJLElBQUksS0FBSyxFQUFFLEVBQUUsRUFBRSxNQUFNO1lBQ3JCLG1CQUFtQixHQUFHLE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7U0FDakU7YUFBTSxJQUFJLElBQUksS0FBSyxFQUFFLEVBQUUsRUFBRSxNQUFNO1lBQzVCLG1CQUFtQixHQUFHLE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7U0FDakU7YUFBTTtZQUNILE9BQU07U0FDVDtRQUNELG1CQUFtQixDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFVBQUEsR0FBRztZQUNoRCxJQUFJLEdBQUcsRUFBRTtnQkFDTCxPQUFPLHVCQUFVLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFBO2FBQ25DO1lBQ0Qsb0JBQU8sQ0FBQyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUE7WUFDOUIseUNBQXlDO1lBQ3pDLElBQUksS0FBSSxDQUFDLE9BQU8sRUFBRTtnQkFDZCxLQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7YUFDZDtRQUNMLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQXhFZ0IsdUJBQXVCO1FBRDNDLE9BQU87T0FDYSx1QkFBdUIsQ0F5RTNDO0lBQUQsOEJBQUM7Q0F6RUQsQUF5RUMsQ0F6RW9ELEVBQUUsQ0FBQyxXQUFXLEdBeUVsRTtrQkF6RW9CLHVCQUF1QiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdhbWVIcHIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9HYW1lSGVscGVyXCI7XG5pbXBvcnQgeyByZXNIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9SZXNIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgQ1R5cGVPYmogZnJvbSBcIi4uLy4uL21vZGVsL2NvbW1vbi9DVHlwZU9ialwiO1xuaW1wb3J0IFRhc2tPYmogZnJvbSBcIi4uLy4uL21vZGVsL2NvbW1vbi9UYXNrT2JqXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFNlbGVjdFRhc2tSZXdhcmRQbmxDdHJsIGV4dGVuZHMgbWMuQmFzZVBubEN0cmwge1xuXG4gICAgLy9AYXV0b2NvZGUgcHJvcGVydHkgYmVnaW5cbiAgICBwcml2YXRlIGNvbnRlbnRUY186IGNjLlRvZ2dsZUNvbnRhaW5lciA9IG51bGwgLy8gcGF0aDovL3Jvb3QvY29udGVudF90Y1xuICAgIC8vQGVuZFxuXG4gICAgcHJpdmF0ZSB0YXNrOiBUYXNrT2JqID0gbnVsbFxuXG4gICAgcHVibGljIGxpc3RlbkV2ZW50TWFwcygpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgcHVibGljIGFzeW5jIG9uQ3JlYXRlKCkge1xuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKHRhc2s6IFRhc2tPYmopIHtcbiAgICAgICAgdGhpcy50YXNrID0gdGFza1xuICAgICAgICB0aGlzLmNvbnRlbnRUY18ubm9kZS5JdGVtcyh0YXNrLnJld2FyZHMsIChpdCwgZGF0YSwgaSkgPT4ge1xuICAgICAgICAgICAgaXQuRGF0YSA9IGlcbiAgICAgICAgICAgIGl0LkNoaWxkKCdpY29uJywgY2MuU3ByaXRlKS5zcHJpdGVGcmFtZSA9IHJlc0hlbHBlci5nZXRSZXNJY29uKGRhdGEudHlwZSlcbiAgICAgICAgICAgIGl0LkNoaWxkKCdjb3VudCcsIGNjLkxhYmVsKS5zdHJpbmcgPSAneCcgKyBkYXRhLmNvdW50XG4gICAgICAgIH0pXG4gICAgICAgIHRoaXMuY29udGVudFRjXy5Td2loKCcnKVxuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3Qvb2tfYmVcbiAgICBvbkNsaWNrT2soZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBjb25zdCBpID0gdGhpcy5jb250ZW50VGNfLnRvZ2dsZUl0ZW1zLmZpbmRJbmRleChtID0+IG0uaXNDaGVja2VkKVxuICAgICAgICBjb25zdCByZXdhcmQgPSB0aGlzLnRhc2sucmV3YXJkc1tpXVxuICAgICAgICBpZiAoIXJld2FyZCkge1xuICAgICAgICAgICAgcmV0dXJuIHZpZXdIZWxwZXIuc2hvd0FsZXJ0KCd0b2FzdC5wbGVhc2Vfc2VsZWN0X29uZV9yZXdhcmQnKVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGl0ZW1zID0gZ2FtZUhwci5jaGVja1Jld2FyZEZ1bGwoW3Jld2FyZF0pXG4gICAgICAgIGlmIChpdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICByZXR1cm4gdmlld0hlbHBlci5zaG93UG5sKCdjb21tb24vUmVzRnVsbFRpcCcsIGl0ZW1zLCAob2s6IGJvb2xlYW4pID0+IHsgb2sgJiYgdGhpcy5jbGFpbUF3YXJkKGksIHJld2FyZCkgfSlcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmNsYWltQXdhcmQoaSwgcmV3YXJkKVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgY2xhaW1Bd2FyZChpOiBudW1iZXIsIHJld2FyZDogQ1R5cGVPYmopIHtcbiAgICAgICAgY29uc3QgcGxheWVyID0gZ2FtZUhwci5wbGF5ZXIsIHR5cGUgPSB0aGlzLnRhc2suanNvbj8udHlwZVxuICAgICAgICBsZXQgY2xhaW1UYXNrUmV3YXJkRnVuYzogRnVuY3Rpb24gPSBudWxsXG4gICAgICAgIGlmICh0eXBlID09PSAyMCkgeyAvL+avj+aXpeS7u+WKoVxuICAgICAgICAgICAgY2xhaW1UYXNrUmV3YXJkRnVuYyA9IHBsYXllci5jbGFpbVRvZGF5VGFza1Jld2FyZC5iaW5kKHBsYXllcilcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAzMCkgeyAvL+WFtuS7luS7u+WKoVxuICAgICAgICAgICAgY2xhaW1UYXNrUmV3YXJkRnVuYyA9IHBsYXllci5jbGFpbU90aGVyVGFza1Jld2FyZC5iaW5kKHBsYXllcilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNsYWltVGFza1Jld2FyZEZ1bmModGhpcy50YXNrLmlkLCAwLCBpICsgMSkudGhlbihlcnIgPT4ge1xuICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgIHJldHVybiB2aWV3SGVscGVyLnNob3dBbGVydChlcnIpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBnYW1lSHByLmFkZEdhaW5NYXNzYWdlKHJld2FyZClcbiAgICAgICAgICAgIC8vIHZpZXdIZWxwZXIuaGlkZVBubCgnY29tbW9uL0d1aWRlVGFzaycpXG4gICAgICAgICAgICBpZiAodGhpcy5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5oaWRlKClcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9XG59XG4iXX0=