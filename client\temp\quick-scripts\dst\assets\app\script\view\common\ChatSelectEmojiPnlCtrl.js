
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ChatSelectEmojiPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f95ea2fwXxLeqPJXr+1jQCl', 'ChatSelectEmojiPnlCtrl');
// app/script/view/common/ChatSelectEmojiPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var ChatSelectEmojiPnlCtrl = /** @class */ (function (_super) {
    __extends(ChatSelectEmojiPnlCtrl, _super);
    function ChatSelectEmojiPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.lastUseNode_ = null; // path://root/last_use_n
        _this.listSv_ = null; // path://root/list_sv
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        //@end
        _this.PKEY_TAB = 'CHAT_SELECT_EMOJI_TAB';
        _this.EMOJI_SIZE = cc.size(64, 64);
        _this.user = null;
        _this.cb = null;
        return _this;
    }
    ChatSelectEmojiPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    ChatSelectEmojiPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.onEnter = function (cb) {
        this.cb = cb;
        // 刷新历史使用
        this.updateLastUse();
        this.tabsTc_.Tabs(this.user.getTempPreferenceMap(this.PKEY_TAB) || 0);
    };
    ChatSelectEmojiPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    ChatSelectEmojiPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    ChatSelectEmojiPnlCtrl.prototype.onClickItem = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        this.cb && this.cb(id);
        this.hide();
        this.addUseCount(id);
    };
    // path://root/last_use_n/last_use_be
    ChatSelectEmojiPnlCtrl.prototype.onClickLastUse = function (event, data) {
        audioMgr.playSFX('click');
        var id = event.target.Data;
        this.cb && this.cb(id);
        this.hide();
    };
    // path://root/tabs_tc_tce
    ChatSelectEmojiPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        this.updateEmojis(type);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    ChatSelectEmojiPnlCtrl.prototype.updateLastUse = function () {
        var _this = this;
        var list = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT) || [];
        var len = list.length;
        // 这里兼容一下
        var emojis = this.user.getUnlockChatEmojiIds();
        for (var i = list.length - 1; i >= 0; i--) {
            var id = list[i].id, type = Math.floor(id / 1000);
            if (type === 1) {
                continue;
            }
            else if (!emojis.has(id)) {
                list.splice(i, 1);
            }
        }
        if (list.length !== len) {
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT, list);
        }
        list.sort(function (a, b) { return b.count - a.count; });
        this.lastUseNode_.Items(list.slice(0, 5), function (it, data) {
            it.Data = data.id;
            var val = it.Child('val');
            val.scale = 1;
            ResHelper_1.resHelper.loadEmojiIcon(data.id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE); });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.addUseCount = function (id) {
        var list = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT) || [];
        var it = list.find(function (m) { return m.id === id; });
        if (it) {
            it.count += 1;
        }
        else {
            list.push({ id: id, count: 1 });
        }
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.USE_EMOJIS_COUNT, list);
    };
    ChatSelectEmojiPnlCtrl.prototype.updateEmojis = function (type) {
        var _this = this;
        var ids = this.getCanUseEmojis(type);
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        this.listSv_.Child('empty').active = type !== 0 && ids.length === 0;
        this.listSv_.List(ids.length, function (it, i) {
            var id = it.Data = ids[i];
            var val = it.Child('val');
            ResHelper_1.resHelper.loadEmojiIcon(id, val, _this.key).then(function () { return _this.isValid && val.adaptScale(_this.EMOJI_SIZE); });
        });
    };
    ChatSelectEmojiPnlCtrl.prototype.getCanUseEmojis = function (type) {
        type = type + 1;
        var datas = [];
        assetsMgr.getJson('chatEmoji').datas.forEach(function (m) { return (m.type === type && m.cond === 0) && datas.push(m.id); });
        if (type === 2) { //这里加上购买的动态表情
            datas.pushArr(this.user.getUnlockChatEmojiIds());
        }
        return datas;
    };
    ChatSelectEmojiPnlCtrl = __decorate([
        ccclass
    ], ChatSelectEmojiPnlCtrl);
    return ChatSelectEmojiPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = ChatSelectEmojiPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGNvbW1vblxcQ2hhdFNlbGVjdEVtb2ppUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxxREFBNEQ7QUFDNUQsMkRBQTBEO0FBR2xELElBQUEsT0FBTyxHQUFLLEVBQUUsQ0FBQyxVQUFVLFFBQWxCLENBQW1CO0FBR2xDO0lBQW9ELDBDQUFjO0lBQWxFO1FBQUEscUVBNkhDO1FBM0hHLDBCQUEwQjtRQUNsQixrQkFBWSxHQUFZLElBQUksQ0FBQSxDQUFDLHlCQUF5QjtRQUN0RCxhQUFPLEdBQWtCLElBQUksQ0FBQSxDQUFDLHNCQUFzQjtRQUNwRCxhQUFPLEdBQXVCLElBQUksQ0FBQSxDQUFDLDBCQUEwQjtRQUNyRSxNQUFNO1FBRVcsY0FBUSxHQUFXLHVCQUF1QixDQUFBO1FBQ25ELGdCQUFVLEdBQVksRUFBRSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUE7UUFFckMsVUFBSSxHQUFjLElBQUksQ0FBQTtRQUN0QixRQUFFLEdBQWEsSUFBSSxDQUFBOztJQWlIL0IsQ0FBQztJQS9HVSxnREFBZSxHQUF0QjtRQUNJLE9BQU8sRUFBRSxDQUFBO0lBQ2IsQ0FBQztJQUVZLHlDQUFRLEdBQXJCOzs7Z0JBQ0ksSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFBOzs7O0tBQ3BDO0lBRU0sd0NBQU8sR0FBZCxVQUFlLEVBQVk7UUFDdkIsSUFBSSxDQUFDLEVBQUUsR0FBRyxFQUFFLENBQUE7UUFDWixTQUFTO1FBQ1QsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFBO1FBQ3BCLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFBO0lBQ3pFLENBQUM7SUFFTSx5Q0FBUSxHQUFmO1FBQ0ksSUFBSSxDQUFDLEVBQUUsR0FBRyxJQUFJLENBQUE7SUFDbEIsQ0FBQztJQUVNLHdDQUFPLEdBQWQ7SUFDQSxDQUFDO0lBRUQsaUhBQWlIO0lBQ2pILDJCQUEyQjtJQUUzQiwyQ0FBMkM7SUFDM0MsNENBQVcsR0FBWCxVQUFZLEtBQTBCLEVBQUUsSUFBWTtRQUNoRCxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQzVCLElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUN0QixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7UUFDWCxJQUFJLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQyxDQUFBO0lBQ3hCLENBQUM7SUFFRCxxQ0FBcUM7SUFDckMsK0NBQWMsR0FBZCxVQUFlLEtBQTBCLEVBQUUsSUFBWTtRQUNuRCxRQUFRLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFBO1FBQ3pCLElBQU0sRUFBRSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFBO1FBQzVCLElBQUksQ0FBQyxFQUFFLElBQUksSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsQ0FBQTtRQUN0QixJQUFJLENBQUMsSUFBSSxFQUFFLENBQUE7SUFDZixDQUFDO0lBRUQsMEJBQTBCO0lBQzFCLDRDQUFXLEdBQVgsVUFBWSxLQUFnQixFQUFFLElBQVk7UUFDdEMsQ0FBQyxJQUFJLElBQUksUUFBUSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQTtRQUNsQyxJQUFNLElBQUksR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUNwQyxJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUUsSUFBSSxDQUFDLENBQUE7UUFDcEQsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQTtJQUMzQixDQUFDO0lBQ0QsTUFBTTtJQUNOLGlIQUFpSDtJQUVqSCxpSEFBaUg7SUFFekcsOENBQWEsR0FBckI7UUFBQSxpQkF1QkM7UUF0QkcsSUFBTSxJQUFJLEdBQVUsSUFBSSxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxxQkFBYSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxDQUFBO1FBQzFGLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUE7UUFDdkIsU0FBUztRQUNULElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMscUJBQXFCLEVBQUUsQ0FBQTtRQUNoRCxLQUFLLElBQUksQ0FBQyxHQUFHLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUU7WUFDdkMsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRSxJQUFJLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLEdBQUcsSUFBSSxDQUFDLENBQUE7WUFDbkQsSUFBSSxJQUFJLEtBQUssQ0FBQyxFQUFFO2dCQUNaLFNBQVE7YUFDWDtpQkFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsRUFBRTtnQkFDeEIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUE7YUFDcEI7U0FDSjtRQUNELElBQUksSUFBSSxDQUFDLE1BQU0sS0FBSyxHQUFHLEVBQUU7WUFDckIsSUFBSSxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxxQkFBYSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFBO1NBQ3pFO1FBQ0QsSUFBSSxDQUFDLElBQUksQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDLElBQUssT0FBQSxDQUFDLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQyxLQUFLLEVBQWpCLENBQWlCLENBQUMsQ0FBQTtRQUN0QyxJQUFJLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJO1lBQy9DLEVBQUUsQ0FBQyxJQUFJLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQTtZQUNqQixJQUFNLEdBQUcsR0FBRyxFQUFFLENBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFBO1lBQzNCLEdBQUcsQ0FBQyxLQUFLLEdBQUcsQ0FBQyxDQUFBO1lBQ2IscUJBQVMsQ0FBQyxhQUFhLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxHQUFHLEVBQUUsS0FBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxjQUFNLE9BQUEsS0FBSSxDQUFDLE9BQU8sSUFBSSxHQUFHLENBQUMsVUFBVSxDQUFDLEtBQUksQ0FBQyxVQUFVLENBQUMsRUFBL0MsQ0FBK0MsQ0FBQyxDQUFBO1FBQy9HLENBQUMsQ0FBQyxDQUFBO0lBQ04sQ0FBQztJQUVPLDRDQUFXLEdBQW5CLFVBQW9CLEVBQVU7UUFDMUIsSUFBTSxJQUFJLEdBQVUsSUFBSSxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxxQkFBYSxDQUFDLGdCQUFnQixDQUFDLElBQUksRUFBRSxDQUFBO1FBQzFGLElBQU0sRUFBRSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsVUFBQSxDQUFDLElBQUksT0FBQSxDQUFDLENBQUMsRUFBRSxLQUFLLEVBQUUsRUFBWCxDQUFXLENBQUMsQ0FBQTtRQUN0QyxJQUFJLEVBQUUsRUFBRTtZQUNKLEVBQUUsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFBO1NBQ2hCO2FBQU07WUFDSCxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxJQUFBLEVBQUUsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUE7U0FDOUI7UUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLHFCQUFhLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDMUUsQ0FBQztJQUVPLDZDQUFZLEdBQXBCLFVBQXFCLElBQVk7UUFBakMsaUJBVUM7UUFURyxJQUFNLEdBQUcsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3RDLElBQUksQ0FBQyxPQUFPLENBQUMsY0FBYyxFQUFFLENBQUE7UUFDN0IsSUFBSSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQTtRQUMxQixJQUFJLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxNQUFNLEdBQUcsSUFBSSxLQUFLLENBQUMsSUFBSSxHQUFHLENBQUMsTUFBTSxLQUFLLENBQUMsQ0FBQTtRQUNuRSxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLFVBQUMsRUFBRSxFQUFFLENBQUM7WUFDaEMsSUFBTSxFQUFFLEdBQUcsRUFBRSxDQUFDLElBQUksR0FBRyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUE7WUFDM0IsSUFBTSxHQUFHLEdBQUcsRUFBRSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtZQUMzQixxQkFBUyxDQUFDLGFBQWEsQ0FBQyxFQUFFLEVBQUUsR0FBRyxFQUFFLEtBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsY0FBTSxPQUFBLEtBQUksQ0FBQyxPQUFPLElBQUksR0FBRyxDQUFDLFVBQVUsQ0FBQyxLQUFJLENBQUMsVUFBVSxDQUFDLEVBQS9DLENBQStDLENBQUMsQ0FBQTtRQUMxRyxDQUFDLENBQUMsQ0FBQTtJQUNOLENBQUM7SUFFTyxnREFBZSxHQUF2QixVQUF3QixJQUFZO1FBQ2hDLElBQUksR0FBRyxJQUFJLEdBQUcsQ0FBQyxDQUFBO1FBQ2YsSUFBTSxLQUFLLEdBQWEsRUFBRSxDQUFBO1FBQzFCLFNBQVMsQ0FBQyxPQUFPLENBQUMsV0FBVyxDQUFDLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxJQUFJLElBQUksQ0FBQyxDQUFDLElBQUksS0FBSyxDQUFDLENBQUMsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBckQsQ0FBcUQsQ0FBQyxDQUFBO1FBQ3hHLElBQUksSUFBSSxLQUFLLENBQUMsRUFBRSxFQUFFLGFBQWE7WUFDM0IsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUMsQ0FBQTtTQUNuRDtRQUNELE9BQU8sS0FBSyxDQUFBO0lBQ2hCLENBQUM7SUE1SGdCLHNCQUFzQjtRQUQxQyxPQUFPO09BQ2Esc0JBQXNCLENBNkgxQztJQUFELDZCQUFDO0NBN0hELEFBNkhDLENBN0htRCxFQUFFLENBQUMsV0FBVyxHQTZIakU7a0JBN0hvQixzQkFBc0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmVmZXJlbmNlS2V5IH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IHsgcmVzSGVscGVyIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9oZWxwZXIvUmVzSGVscGVyXCI7XG5pbXBvcnQgVXNlck1vZGVsIGZyb20gXCIuLi8uLi9tb2RlbC9jb21tb24vVXNlck1vZGVsXCI7XG5cbmNvbnN0IHsgY2NjbGFzcyB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIENoYXRTZWxlY3RFbW9qaVBubEN0cmwgZXh0ZW5kcyBtYy5CYXNlUG5sQ3RybCB7XG5cbiAgICAvL0BhdXRvY29kZSBwcm9wZXJ0eSBiZWdpblxuICAgIHByaXZhdGUgbGFzdFVzZU5vZGVfOiBjYy5Ob2RlID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9sYXN0X3VzZV9uXG4gICAgcHJpdmF0ZSBsaXN0U3ZfOiBjYy5TY3JvbGxWaWV3ID0gbnVsbCAvLyBwYXRoOi8vcm9vdC9saXN0X3N2XG4gICAgcHJpdmF0ZSB0YWJzVGNfOiBjYy5Ub2dnbGVDb250YWluZXIgPSBudWxsIC8vIHBhdGg6Ly9yb290L3RhYnNfdGNfdGNlXG4gICAgLy9AZW5kXG5cbiAgICBwcml2YXRlIHJlYWRvbmx5IFBLRVlfVEFCOiBzdHJpbmcgPSAnQ0hBVF9TRUxFQ1RfRU1PSklfVEFCJ1xuICAgIHByaXZhdGUgRU1PSklfU0laRTogY2MuU2l6ZSA9IGNjLnNpemUoNjQsIDY0KVxuXG4gICAgcHJpdmF0ZSB1c2VyOiBVc2VyTW9kZWwgPSBudWxsXG4gICAgcHJpdmF0ZSBjYjogRnVuY3Rpb24gPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgICAgIHRoaXMudXNlciA9IHRoaXMuZ2V0TW9kZWwoJ3VzZXInKVxuICAgIH1cblxuICAgIHB1YmxpYyBvbkVudGVyKGNiOiBGdW5jdGlvbikge1xuICAgICAgICB0aGlzLmNiID0gY2JcbiAgICAgICAgLy8g5Yi35paw5Y6G5Y+y5L2/55SoXG4gICAgICAgIHRoaXMudXBkYXRlTGFzdFVzZSgpXG4gICAgICAgIHRoaXMudGFic1RjXy5UYWJzKHRoaXMudXNlci5nZXRUZW1wUHJlZmVyZW5jZU1hcCh0aGlzLlBLRVlfVEFCKSB8fCAwKVxuICAgIH1cblxuICAgIHB1YmxpYyBvblJlbW92ZSgpIHtcbiAgICAgICAgdGhpcy5jYiA9IG51bGxcbiAgICB9XG5cbiAgICBwdWJsaWMgb25DbGVhbigpIHtcbiAgICB9XG5cbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBidXR0b24gbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIC8vQGF1dG9jb2RlIGJ1dHRvbiBsaXN0ZW5lclxuXG4gICAgLy8gcGF0aDovL3Jvb3QvbGlzdF9zdi92aWV3L2NvbnRlbnQvaXRlbV9iZVxuICAgIG9uQ2xpY2tJdGVtKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgYXVkaW9NZ3IucGxheVNGWCgnY2xpY2snKVxuICAgICAgICBjb25zdCBpZCA9IGV2ZW50LnRhcmdldC5EYXRhXG4gICAgICAgIHRoaXMuY2IgJiYgdGhpcy5jYihpZClcbiAgICAgICAgdGhpcy5oaWRlKClcbiAgICAgICAgdGhpcy5hZGRVc2VDb3VudChpZClcbiAgICB9XG5cbiAgICAvLyBwYXRoOi8vcm9vdC9sYXN0X3VzZV9uL2xhc3RfdXNlX2JlXG4gICAgb25DbGlja0xhc3RVc2UoZXZlbnQ6IGNjLkV2ZW50LkV2ZW50VG91Y2gsIGRhdGE6IHN0cmluZykge1xuICAgICAgICBhdWRpb01nci5wbGF5U0ZYKCdjbGljaycpXG4gICAgICAgIGNvbnN0IGlkID0gZXZlbnQudGFyZ2V0LkRhdGFcbiAgICAgICAgdGhpcy5jYiAmJiB0aGlzLmNiKGlkKVxuICAgICAgICB0aGlzLmhpZGUoKVxuICAgIH1cblxuICAgIC8vIHBhdGg6Ly9yb290L3RhYnNfdGNfdGNlXG4gICAgb25DbGlja1RhYnMoZXZlbnQ6IGNjLlRvZ2dsZSwgZGF0YTogc3RyaW5nKSB7XG4gICAgICAgICFkYXRhICYmIGF1ZGlvTWdyLnBsYXlTRlgoJ2NsaWNrJylcbiAgICAgICAgY29uc3QgdHlwZSA9IE51bWJlcihldmVudC5ub2RlLm5hbWUpXG4gICAgICAgIHRoaXMudXNlci5zZXRUZW1wUHJlZmVyZW5jZURhdGEodGhpcy5QS0VZX1RBQiwgdHlwZSlcbiAgICAgICAgdGhpcy51cGRhdGVFbW9qaXModHlwZSlcbiAgICB9XG4gICAgLy9AZW5kXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gZXZlbnQgbGlzdGVuZXIgZnVuY3Rpb24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGN1c3RvbSBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiAgICBwcml2YXRlIHVwZGF0ZUxhc3RVc2UoKSB7XG4gICAgICAgIGNvbnN0IGxpc3Q6IGFueVtdID0gdGhpcy51c2VyLmdldExvY2FsUHJlZmVyZW5jZURhdGEoUHJlZmVyZW5jZUtleS5VU0VfRU1PSklTX0NPVU5UKSB8fCBbXVxuICAgICAgICBjb25zdCBsZW4gPSBsaXN0Lmxlbmd0aFxuICAgICAgICAvLyDov5nph4zlhbzlrrnkuIDkuItcbiAgICAgICAgY29uc3QgZW1vamlzID0gdGhpcy51c2VyLmdldFVubG9ja0NoYXRFbW9qaUlkcygpXG4gICAgICAgIGZvciAobGV0IGkgPSBsaXN0Lmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgICAgICBjb25zdCBpZCA9IGxpc3RbaV0uaWQsIHR5cGUgPSBNYXRoLmZsb29yKGlkIC8gMTAwMClcbiAgICAgICAgICAgIGlmICh0eXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIWVtb2ppcy5oYXMoaWQpKSB7XG4gICAgICAgICAgICAgICAgbGlzdC5zcGxpY2UoaSwgMSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAobGlzdC5sZW5ndGggIT09IGxlbikge1xuICAgICAgICAgICAgdGhpcy51c2VyLnNldExvY2FsUHJlZmVyZW5jZURhdGEoUHJlZmVyZW5jZUtleS5VU0VfRU1PSklTX0NPVU5ULCBsaXN0KVxuICAgICAgICB9XG4gICAgICAgIGxpc3Quc29ydCgoYSwgYikgPT4gYi5jb3VudCAtIGEuY291bnQpXG4gICAgICAgIHRoaXMubGFzdFVzZU5vZGVfLkl0ZW1zKGxpc3Quc2xpY2UoMCwgNSksIChpdCwgZGF0YSkgPT4ge1xuICAgICAgICAgICAgaXQuRGF0YSA9IGRhdGEuaWRcbiAgICAgICAgICAgIGNvbnN0IHZhbCA9IGl0LkNoaWxkKCd2YWwnKVxuICAgICAgICAgICAgdmFsLnNjYWxlID0gMVxuICAgICAgICAgICAgcmVzSGVscGVyLmxvYWRFbW9qaUljb24oZGF0YS5pZCwgdmFsLCB0aGlzLmtleSkudGhlbigoKSA9PiB0aGlzLmlzVmFsaWQgJiYgdmFsLmFkYXB0U2NhbGUodGhpcy5FTU9KSV9TSVpFKSlcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICBwcml2YXRlIGFkZFVzZUNvdW50KGlkOiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgbGlzdDogYW55W10gPSB0aGlzLnVzZXIuZ2V0TG9jYWxQcmVmZXJlbmNlRGF0YShQcmVmZXJlbmNlS2V5LlVTRV9FTU9KSVNfQ09VTlQpIHx8IFtdXG4gICAgICAgIGNvbnN0IGl0ID0gbGlzdC5maW5kKG0gPT4gbS5pZCA9PT0gaWQpXG4gICAgICAgIGlmIChpdCkge1xuICAgICAgICAgICAgaXQuY291bnQgKz0gMVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbGlzdC5wdXNoKHsgaWQsIGNvdW50OiAxIH0pXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy51c2VyLnNldExvY2FsUHJlZmVyZW5jZURhdGEoUHJlZmVyZW5jZUtleS5VU0VfRU1PSklTX0NPVU5ULCBsaXN0KVxuICAgIH1cblxuICAgIHByaXZhdGUgdXBkYXRlRW1vamlzKHR5cGU6IG51bWJlcikge1xuICAgICAgICBjb25zdCBpZHMgPSB0aGlzLmdldENhblVzZUVtb2ppcyh0eXBlKVxuICAgICAgICB0aGlzLmxpc3RTdl8uc3RvcEF1dG9TY3JvbGwoKVxuICAgICAgICB0aGlzLmxpc3RTdl8uY29udGVudC55ID0gMFxuICAgICAgICB0aGlzLmxpc3RTdl8uQ2hpbGQoJ2VtcHR5JykuYWN0aXZlID0gdHlwZSAhPT0gMCAmJiBpZHMubGVuZ3RoID09PSAwXG4gICAgICAgIHRoaXMubGlzdFN2Xy5MaXN0KGlkcy5sZW5ndGgsIChpdCwgaSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgaWQgPSBpdC5EYXRhID0gaWRzW2ldXG4gICAgICAgICAgICBjb25zdCB2YWwgPSBpdC5DaGlsZCgndmFsJylcbiAgICAgICAgICAgIHJlc0hlbHBlci5sb2FkRW1vamlJY29uKGlkLCB2YWwsIHRoaXMua2V5KS50aGVuKCgpID0+IHRoaXMuaXNWYWxpZCAmJiB2YWwuYWRhcHRTY2FsZSh0aGlzLkVNT0pJX1NJWkUpKVxuICAgICAgICB9KVxuICAgIH1cblxuICAgIHByaXZhdGUgZ2V0Q2FuVXNlRW1vamlzKHR5cGU6IG51bWJlcikge1xuICAgICAgICB0eXBlID0gdHlwZSArIDFcbiAgICAgICAgY29uc3QgZGF0YXM6IG51bWJlcltdID0gW11cbiAgICAgICAgYXNzZXRzTWdyLmdldEpzb24oJ2NoYXRFbW9qaScpLmRhdGFzLmZvckVhY2gobSA9PiAobS50eXBlID09PSB0eXBlICYmIG0uY29uZCA9PT0gMCkgJiYgZGF0YXMucHVzaChtLmlkKSlcbiAgICAgICAgaWYgKHR5cGUgPT09IDIpIHsgLy/ov5nph4zliqDkuIrotK3kubDnmoTliqjmgIHooajmg4VcbiAgICAgICAgICAgIGRhdGFzLnB1c2hBcnIodGhpcy51c2VyLmdldFVubG9ja0NoYXRFbW9qaUlkcygpKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBkYXRhc1xuICAgIH1cbn1cbiJdfQ==