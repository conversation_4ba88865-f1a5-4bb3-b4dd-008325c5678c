
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/astar/AStep.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '2401f63uEJJoq8SD1Xdi15G', 'AStep');
// app/script/common/astar/AStep.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 一次的移动
var AStep = /** @class */ (function () {
    function AStep() {
        this.parent = null;
        this.points = [];
        this.last = cc.v2();
        this.pathLens = []; //每次的路径长度
        this.M = 0; //路径总长度
        this.G = 0; //总次数
        this.F = 0; //
    }
    AStep.prototype.ID = function () {
        return this.last.ID();
    };
    AStep.prototype.toString = function () {
        cc.log(this.points.map(function (m) { return m.ID(); }), 'F=' + this.F, 'G=' + this.G, 'M=' + this.M);
    };
    AStep.prototype.init = function (node, parent) {
        this.parent = parent;
        this.last.set(node.point);
        this.G = parent ? parent.G + 1 : 0;
        this.F = node.F;
        this.points.length = 0;
        while (!!node.parent) {
            this.points.push(node.point);
            node = node.parent;
        }
        if (node) {
            this.points.push(node.point);
        }
        this.points.reverse();
        this.M = this.points.length - 1;
        if (parent) {
            this.M += parent.M;
        }
        this.initPathLens();
        return this;
    };
    AStep.prototype.initPathLens = function () {
        this.pathLens.length = 0;
        var step = this;
        while (!!step.parent) {
            this.pathLens.push(step.points.length - 1);
            step = step.parent;
        }
        this.pathLens.reverse();
    };
    // 比较两个
    AStep.prototype.equals = function (step) {
        if (this.M !== step.M) {
            return this.M - step.M;
        }
        // 这里需要从第一个开始比较 越往前长度越大 越好
        return this.getPointsW() - step.getPointsW();
    };
    AStep.prototype.getPointsW = function () {
        var w = 1, i = 0;
        // 最后一个权重倒着算 因为如果最后一个路径最短最好 前面的路径最长越好
        for (var l = this.pathLens.length - 1; i < l; i++) {
            w = w * 10 + this.pathLens[i];
        }
        w = w * 10 + (10 - this.pathLens[i]);
        return w;
    };
    // 判断前面的是否都走满了的 最后一个可以没有满
    AStep.prototype.isPerfectPath = function (moveRange) {
        for (var i = 0, l = this.pathLens.length - 1; i < l; i++) {
            if (this.pathLens[i] !== moveRange) {
                return false;
            }
        }
        return true;
    };
    return AStep;
}());
exports.default = AStep;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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