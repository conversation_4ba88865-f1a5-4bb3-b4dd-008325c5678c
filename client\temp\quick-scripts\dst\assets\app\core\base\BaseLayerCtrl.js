
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/base/BaseLayerCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0e8f5zru/xL+KOoEKaKkME7', 'BaseLayerCtrl');
// app/core/base/BaseLayerCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseMvcCtrl_1 = require("./BaseMvcCtrl");
var BaseLayerCtrl = /** @class */ (function (_super) {
    __extends(BaseLayerCtrl, _super);
    function BaseLayerCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.__listens = []; // 监听列表
        return _this;
    }
    BaseLayerCtrl.prototype.__preload = function () { };
    BaseLayerCtrl.prototype.__init = function (mgr) {
        this.setCtrlMgr(mgr);
        this.__wrapListenMaps(this.listenEventMaps(), this.__listens, this).forEach(function (_a) {
            var type = _a.type, cb = _a.cb, target = _a.target;
            return eventCenter.on(type, cb, target);
        });
        this.onCreate();
        return this;
    };
    BaseLayerCtrl.prototype.__clean = function () {
        this.__listens.forEach(function (_a) {
            var type = _a.type, cb = _a.cb, target = _a.target;
            return eventCenter.off(type, cb, target);
        });
        this.onClean();
    };
    BaseLayerCtrl.prototype.onCreate = function () {
    };
    BaseLayerCtrl.prototype.onClean = function () {
    };
    BaseLayerCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BaseLayerCtrl.prototype.setCtrlMgr = function (mgr) {
    };
    return BaseLayerCtrl;
}(BaseMvcCtrl_1.default));
exports.default = BaseLayerCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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