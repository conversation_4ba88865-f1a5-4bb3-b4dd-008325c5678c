
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/AchievementListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b9b32cV6HBGpb0OBkCpSGdz', 'AchievementListPnlCtrl');
// app/script/view/menu/AchievementListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AchievementListPnlCtrl = /** @class */ (function (_super) {
    __extends(AchievementListPnlCtrl, _super);
    function AchievementListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.topNode_ = null; // path://root/top_n
        _this.achievementSv_ = null; // path://root/achievement_sv
        //@end
        _this.IDS = [4, 10, 12, 13]; // 这些类型的成就需要拥有才显示 比如：绝版的和DC发放的
        _this.user = null;
        _this.model = null;
        return _this;
    }
    AchievementListPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AchievementListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.model = this.getModel('task');
                return [2 /*return*/];
            });
        });
    };
    AchievementListPnlCtrl.prototype.onEnter = function (data) {
        ReddotHelper_1.reddotHelper.pause('achieve_task', true);
        this.updateTopInfo();
    };
    AchievementListPnlCtrl.prototype.onRemove = function () {
        ReddotHelper_1.reddotHelper.pause('achieve_task', false);
    };
    AchievementListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/achievement_sv/view/content/item/claim_be
    AchievementListPnlCtrl.prototype.onClickClaim = function (event, _data) {
        var data = event.target.parent.parent.Data;
        if (data) {
            this.claimAchieveReward(data.id);
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    AchievementListPnlCtrl.prototype.updateTopInfo = function () {
        this.model.updateAchieveTaskState();
        // 已完成的个数，特殊的完成个数，特殊的总个数
        var finishedCount = 0, specialCount = 0, extraCount = 0;
        // 当前所有成就，包含绝版、隐藏等，同类型只包含一个（已完成的除外）
        var datas = assetsMgr.getJson('title').datas, achievements = this.model.getAchieveTasks(), titles = this.user.getUnlockTitles(), list = [];
        // 先把进行中或可领取的加进去
        for (var i = 0; i < achievements.length; i++) {
            var data = assetsMgr.getJsonData('title', achievements[i].json.title_id);
            data && list.push(data);
        }
        var _loop_1 = function (i) {
            var data = datas[i], isSpecial = this_1.IDS.indexOf(data.type) > -1;
            // 特殊的的 || 列表中该类型的一个都没有的 || 列表中该类型的已经有了，但是id比成就任务中的id更小的（已完成的前置任务）
            if (isSpecial || !list.some(function (m) { return m.type === data.type; }) || (!!list.find(function (m) { return m.type === data.type && m.id > data.id; }) && !list.some(function (m) { return m.id == data.id; }))) {
                list.push(data);
                if (!isSpecial) { // 记录已完成的数量
                    finishedCount++;
                }
                else {
                    extraCount++;
                    if (titles.some(function (m) { return m.id === data.id; })) {
                        specialCount++;
                    }
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < datas.length; i++) {
            _loop_1(i);
        }
        // 如果拥有就要加上绝版的数量，没有就要减去
        var cur = finishedCount + specialCount, max = datas.length - extraCount + specialCount;
        var progress = this.topNode_.Child('progress');
        progress.Child('val', cc.Sprite).fillRange = cc.misc.clampf(cur / max, 0, 1);
        this.topNode_.Child('info', cc.Label).string = cur + '/' + max;
        this.showAchieveTasks(list, titles, achievements);
    };
    AchievementListPnlCtrl.prototype.showAchieveTasks = function (list, titles, achievements) {
        var _this = this;
        var datas = list.sort(function (a, b) {
            var aData = achievements.find(function (m) { return m.json.title_id === a.id; }), bData = achievements.find(function (m) { return m.json.title_id === b.id; });
            var aw = aData ? aData.getSortVal() : 100000, bw = bData ? bData.getSortVal() : 100000;
            if (a.hide_task === 1) {
                aw += 10000;
            }
            if (b.hide_task === 1) {
                bw += 10000;
            }
            return aw - bw;
        });
        this.achievementSv_.stopAutoScroll();
        this.achievementSv_.content.y = 0;
        this.achievementSv_.Items(datas, function (it, data) {
            var achieve = achievements.find(function (m) { return m.json.title_id === data.id; }), isHideTask = (data === null || data === void 0 ? void 0 : data.hide_task) === 1;
            // 绝版成就，不显示, 如果玩家已有绝版成就则需要显示为已领取
            var lay = it.Child('lay'), tipNode = lay.Child('tip'), isCanClaim = achieve === null || achieve === void 0 ? void 0 : achieve.isCanClaim();
            // 提示
            tipNode.active = !!GameHelper_1.gameHpr.isInGame();
            if (!tipNode.active) {
                // 没在游戏中不显示
            }
            else if (data.id === 102001) { //肝帝 显示时间
                tipNode.Color('#BFA891').setLocaleKey('ui.server_sum_online_time', GameHelper_1.gameHpr.millisecondToStringForHour(GameHelper_1.gameHpr.player.getSumOnlineTime()));
            }
            else if (data.id === 103003 && !isCanClaim && GameHelper_1.gameHpr.getWorldEventValue(1)) { //万亩 显示是否有人完成
                tipNode.Color('#D7634D').setLocaleKey('ui.curr_game_p_done');
            }
            else {
                tipNode.active = false;
            }
            // 进行中的 或 已完成未领取的
            if (achieve) {
                it.Data = achieve;
                if (isHideTask && !achieve.isCanClaim()) { // 是否隐藏成就且不能领取
                    lay.Child('name').Swih('hide');
                    lay.Child('desc', cc.Label).string = '??????';
                    ResHelper_1.resHelper.loadTitleIcon(it.Child('icon'), 1, 'title_hide', _this.key);
                    lay.Child('progress').active = false;
                    it.Child('buttons').Swih('doing');
                }
                else {
                    var name = lay.Child('name').Swih('title')[0];
                    name.Child('val').setLocaleKey('titleText.' + (data === null || data === void 0 ? void 0 : data.id));
                    lay.Child('desc').setLocaleKey(achieve.desc, achieve.descParams);
                    ResHelper_1.resHelper.loadTitleIcon(it.Child('icon'), (data === null || data === void 0 ? void 0 : data.quality) || 1, (data === null || data === void 0 ? void 0 : data.icon) || 'title_none', _this.key);
                    var progress = lay.Child('progress'), _a = __read(achieve.getProgress(), 2), a = _a[0], b = _a[1];
                    if (progress.active = b > 0 && !tipNode.active) {
                        progress.Child('val', cc.Sprite).fillRange = cc.misc.clampf(a / b, 0, 1);
                        progress.Child('info', cc.Label).string = a + '/' + b;
                    }
                    it.Child('buttons').Swih(isCanClaim ? 'claim_be' : achieve.isClaimed() ? 'done' : 'doing');
                }
            }
            else { // 已完成的 
                if (_this.IDS.indexOf(data.type) > -1) { // 隐藏成就需要已拥有才有显示
                    it.active = titles.some(function (m) { return m.id === data.id; });
                }
                if (it.active) {
                    var name = lay.Child('name').Swih('title')[0];
                    name.Child('val').setLocaleKey('titleText.' + (data === null || data === void 0 ? void 0 : data.id));
                    lay.Child('desc').setLocaleKey(data.desc, data.params);
                    ResHelper_1.resHelper.loadTitleIcon(it.Child('icon'), (data === null || data === void 0 ? void 0 : data.quality) || 1, (data === null || data === void 0 ? void 0 : data.icon) || 'title_none', _this.key);
                    lay.Child('progress').active = false;
                    it.Child('buttons').Swih('done');
                }
            }
            lay.Component(cc.Layout).updateLayout();
            ut.waitNextFrame().then(function () {
                if (_this.isValid && it.isValid) {
                    it.height = Math.max(132, lay.height);
                }
            });
        });
    };
    AchievementListPnlCtrl.prototype.claimAchieveReward = function (id) {
        var _this = this;
        this.model.claimAchieveTaskReward(id).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                ViewHelper_1.viewHelper.showAlert('toast.claim_succeed');
                _this.updateTopInfo();
            }
        });
    };
    AchievementListPnlCtrl = __decorate([
        ccclass
    ], AchievementListPnlCtrl);
    return AchievementListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AchievementListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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