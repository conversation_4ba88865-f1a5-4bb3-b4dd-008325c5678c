
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/manage/AudioMgr.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd4e27fQr8FOvbIoEipdjK2J', 'AudioMgr');
// app/core/manage/AudioMgr.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var ResLoader_1 = require("../utils/ResLoader");
/**
 * 音频管理中心
 */
var AudioMgr = /** @class */ (function () {
    function AudioMgr() {
        this.__bgmVolume = 1; //背景音乐 音量
        this.__sfxVolume = 1; //特效音乐 音量
        this.__isPause = false;
        this.__bgmAudioID = -1; //背景音乐 ID
        this.__bgmVolumeRatio = 1; //背景比例
        this.__caches = [];
        this.__sfxs_infos = []; //当前播放的特效列表
        this.__stateTypeMap = {};
        this.__bgmUrl = "";
        this.__bgmTimeMap = {};
    }
    AudioMgr.prototype.init = function () {
        var bgmV = storageMgr.loadNumber('bgm_volume');
        this.__bgmVolume = bgmV !== null ? bgmV : 1;
        var sfxV = storageMgr.loadNumber('sfx_volume');
        this.__sfxVolume = sfxV !== null ? sfxV : 1;
        logger.info('bgmVolume:', this.__bgmVolume, ' sfxVolume:', this.__sfxVolume);
        // 微信小游戏
        if (ut.isWechatGame()) {
            wx.onAudioInterruptionBegin(this.onAudioInterruptionBegin.bind(this));
            wx.onAudioInterruptionEnd(this.onAudioInterruptionEnd.bind(this));
        }
    };
    AudioMgr.prototype.clean = function () {
        if (ut.isWechatGame()) {
            wx.offAudioInterruptionBegin(this.onAudioInterruptionBegin.bind(this));
            wx.offAudioInterruptionEnd(this.onAudioInterruptionEnd.bind(this));
        }
    };
    AudioMgr.prototype.onAudioInterruptionBegin = function () {
        this.pauseAll();
    };
    AudioMgr.prototype.onAudioInterruptionEnd = function () {
        this.resumeAll();
    };
    AudioMgr.prototype.pauseAll = function () {
        this.__isPause = true;
        this.__sfxs_infos.forEach(function (m) { return (m.id !== undefined) && cc.audioEngine.pause(m.id); });
        if (this.__bgmAudioID >= 0)
            cc.audioEngine.pause(this.__bgmAudioID);
    };
    AudioMgr.prototype.resumeAll = function () {
        this.__isPause = false;
        if (this.__bgmVolume > 0 && this.__bgmAudioID >= 0) {
            cc.audioEngine.resume(this.__bgmAudioID);
        }
        if (this.__sfxVolume > 0) {
            this.__sfxs_infos.forEach(function (m) { return (m.id !== undefined) && cc.audioEngine.resume(m.id); });
        }
    };
    AudioMgr.prototype.stopAll = function () {
        cc.audioEngine.stopAll();
    };
    Object.defineProperty(AudioMgr.prototype, "bgmVolume", {
        get: function () {
            return this.__bgmVolume;
        },
        set: function (val) {
            this.__bgmVolume = val;
            storageMgr.saveNumber('bgm_volume', val);
            if (this.__bgmAudioID >= 0) {
                if (val <= 0) {
                    cc.audioEngine.pause(this.__bgmAudioID);
                }
                else {
                    cc.audioEngine.resume(this.__bgmAudioID);
                }
                cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio);
            }
        },
        enumerable: false,
        configurable: true
    });
    AudioMgr.prototype.setTempBgmVolume = function (val) {
        if (this.__bgmAudioID >= 0) {
            cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio);
        }
    };
    Object.defineProperty(AudioMgr.prototype, "sfxVolume", {
        get: function () {
            return this.__sfxVolume;
        },
        set: function (val) {
            this.__sfxVolume = val;
            storageMgr.saveNumber('sfx_volume', val);
            this.__sfxs_infos.forEach(function (m) { return cc.audioEngine.setVolume(m.id, val); });
        },
        enumerable: false,
        configurable: true
    });
    // 加载单个声音
    AudioMgr.prototype.load = function (urls) {
        return __awaiter(this, void 0, void 0, function () {
            var i, l, asset;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        urls = Array.isArray(urls) ? urls : [urls];
                        i = 0, l = urls.length;
                        _a.label = 1;
                    case 1:
                        if (!(i < l)) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.__load(urls[i])];
                    case 2:
                        asset = _a.sent();
                        asset && this.__addAudio(asset.mod, asset.url, asset.audio);
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    // 预加载    
    AudioMgr.prototype.preload = function (url) {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.__load(url)];
                    case 1:
                        data = _a.sent();
                        if (data.audio) {
                            this.__addAudio(data.mod, data.url, data.audio);
                        }
                        else {
                            cc.error('load error url=', url);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载对于模块的音效
    AudioMgr.prototype.loadByMod = function (mod) {
        return __awaiter(this, void 0, void 0, function () {
            var url, audios;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        mod = mod || mc.currWindName;
                        url = 'sound/' + mod;
                        return [4 /*yield*/, ResLoader_1.loader.loadResDir(url, cc.AudioClip)];
                    case 1:
                        audios = _a.sent();
                        audios.forEach(function (audio) { return _this.__addAudio(mod, url + '/' + audio.name, audio); });
                        return [2 /*return*/];
                }
            });
        });
    };
    // 释放音效
    AudioMgr.prototype.release = function (val) {
        var asset = this.__getForCache(val);
        if (asset) {
            this.__caches.remove('url', asset.url);
            if (!this.__sfxs_infos.remove('url', asset.url)) {
                this.__sfxs_infos.remove('url', asset.audio.nativeUrl);
            }
            ResLoader_1.loader.releaseRes(asset.url, cc.AudioClip);
        }
    };
    // 释放对应mod的音效
    AudioMgr.prototype.releaseByMod = function (mod) {
        mod = mod || mc.currWindName;
        for (var i = this.__caches.length - 1; i >= 0; i--) {
            var asset = this.__caches[i];
            if (asset.mod === mod) {
                this.__caches.splice(i, 1);
                if (!this.__sfxs_infos.remove('url', asset.url)) {
                    this.__sfxs_infos.remove('url', asset.audio.nativeUrl);
                }
                ResLoader_1.loader.releaseAsset(asset.url, cc.AudioClip);
            }
        }
    };
    // 释放所有声音
    AudioMgr.prototype.releaseAll = function () {
        while (this.__caches.length > 0) {
            ResLoader_1.loader.releaseAsset(this.__caches.pop().url, cc.AudioClip);
        }
        this.__sfxs_infos.length = 0;
    };
    // 添加音效
    AudioMgr.prototype.__addAudio = function (mod, url, audio) {
        if (!this.__caches.some(function (m) { return m.url === url; })) {
            this.__caches.push({ mod: mod, url: url, audio: audio });
        }
    };
    // 加载
    AudioMgr.prototype.__load = function (val) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, wind, name, audio, mod, url;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = __read(val.split('/'), 2), wind = _a[0], name = _a[1];
                        audio = null, mod = '', url = '';
                        if (!!name) return [3 /*break*/, 4];
                        mod = mc.currWindName;
                        url = "sound/" + mod + "/" + wind;
                        ResLoader_1.loader.error = false;
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.AudioClip)];
                    case 1:
                        audio = _b.sent();
                        ResLoader_1.loader.error = true;
                        if (!!audio) return [3 /*break*/, 3];
                        mod = 'common';
                        url = "sound/" + mod + "/" + wind;
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.AudioClip)];
                    case 2:
                        audio = _b.sent();
                        _b.label = 3;
                    case 3: return [3 /*break*/, 6];
                    case 4:
                        mod = wind;
                        url = "sound/" + mod + "/" + name;
                        return [4 /*yield*/, ResLoader_1.loader.loadRes(url, cc.AudioClip)];
                    case 5:
                        audio = _b.sent();
                        _b.label = 6;
                    case 6: return [2 /*return*/, { mod: mod, url: url, audio: audio }];
                }
            });
        });
    };
    // 获取缓存音效
    AudioMgr.prototype.__getForCache = function (val) {
        var _a = __read(val.split('/'), 2), wind = _a[0], name = _a[1];
        var asset = null, url = '';
        if (!name) {
            url = "sound/" + mc.currWindName + "/" + wind;
            asset = this.__caches.find(function (m) { return m.url === url; });
            if (!asset) {
                url = "sound/common/" + wind;
                asset = this.__caches.find(function (m) { return m.url === url; });
            }
        }
        else {
            url = "sound/" + wind + "/" + name;
            asset = this.__caches.find(function (m) { return m.url === url; });
        }
        return asset;
    };
    // 获取音效
    AudioMgr.prototype.__getAudio = function (val) {
        var audio = assetsMgr.getAudio(val);
        if (audio) {
            return audio;
        }
        var asset = this.__getForCache(val);
        return asset ? asset.audio : null;
    };
    // 播放音乐
    AudioMgr.prototype.playBGM = function (url, volume) {
        var _this = this;
        if (volume === void 0) { volume = 1; }
        if (!url) {
            return;
        }
        this.stopBGM();
        if (url instanceof cc.AudioClip) {
            return this.__playBGM(url, volume);
        }
        var audio = this.__getAudio(url);
        if (audio) { // 播放
            this.__playBGM(audio, volume);
        }
        else {
            this.__load(url).then(function (m) {
                if (m.audio) {
                    _this.__addAudio(m.mod, m.url, m.audio);
                    _this.__playBGM(m.audio, volume);
                }
                else {
                    cc.error('playBGM error url=', url);
                }
            });
        }
    };
    // 播放音乐
    AudioMgr.prototype.playFadeInBGM = function (url, volume) {
        var _this = this;
        if (volume === void 0) { volume = 1; }
        if (!url) {
            return;
        }
        this.__fadeOutBGM(this.__bgmAudioID, this.__bgmUrl, 0.25, true);
        this.__bgmUrl = url;
        var audio = this.__getAudio(url);
        if (audio) { // 播放
            this.__playBGM(audio, volume);
            this.__fadeInBGM(this.__bgmAudioID, 0.25);
        }
        else {
            this.__load(url).then(function (m) {
                if (m.audio) {
                    _this.__addAudio(m.mod, m.url, m.audio);
                    _this.__playBGM(m.audio, volume);
                }
                else {
                    cc.error('playBGM error url=', url);
                }
            });
        }
    };
    AudioMgr.prototype.__playBGM = function (audio, volume) {
        this.__bgmVolumeRatio = volume;
        this.__bgmAudioID = cc.audioEngine.play(audio, true, this.bgmVolume * this.__bgmVolumeRatio);
        if (this.bgmVolume === 0 || this.__isPause) {
            cc.audioEngine.pause(this.__bgmAudioID);
        }
        if (this.__bgmUrl !== "") {
            var info = this.__bgmTimeMap[this.__bgmUrl];
            if (info) {
                var passTime = Date.now() - info.stopTime;
                if (passTime < 1000 * 150) { //2分半内才会继续进度播放
                    cc.audioEngine.setCurrentTime(this.__bgmAudioID, this.__bgmTimeMap[this.__bgmUrl].curTime);
                }
                else {
                    delete this.__bgmTimeMap[this.__bgmUrl];
                }
            }
        }
    };
    AudioMgr.prototype.stopBGM = function () {
        if (this.__bgmAudioID >= 0) {
            cc.audioEngine.stop(this.__bgmAudioID);
            this.__bgmAudioID = -1;
        }
    };
    AudioMgr.prototype.pauseBGM = function () {
        this.__bgmAudioID >= 0 && cc.audioEngine.pause(this.__bgmAudioID);
    };
    AudioMgr.prototype.resumeBGM = function () {
        this.__bgmAudioID >= 0 && cc.audioEngine.resume(this.__bgmAudioID);
    };
    AudioMgr.prototype.resetBGMRecord = function () {
        this.__bgmTimeMap = {};
    };
    AudioMgr.prototype.fadeOutBGM = function (duration, stop) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.__fadeOutBGM(this.__bgmAudioID, this.__bgmUrl, duration, stop)];
            });
        });
    };
    AudioMgr.prototype.__fadeOutBGM = function (audioID, bgmUrl, duration, stop) {
        return __awaiter(this, void 0, void 0, function () {
            var volume, target, diff;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (audioID < 0) {
                            return [2 /*return*/];
                        }
                        if (stop) {
                            if (bgmUrl !== '') {
                                this.__bgmTimeMap[bgmUrl] = {
                                    curTime: cc.audioEngine.getCurrentTime(audioID),
                                    stopTime: Date.now()
                                };
                            }
                        }
                        this.__stateTypeMap[audioID] = 'fadeOut';
                        volume = cc.audioEngine.getVolume(audioID);
                        target = 0;
                        diff = (target - volume) / 25 / duration;
                        _a.label = 1;
                    case 1:
                        if (!(volume + diff >= target)) return [3 /*break*/, 3];
                        volume += diff;
                        cc.audioEngine.setVolume(audioID, volume);
                        return [4 /*yield*/, ut.wait(0.04)];
                    case 2:
                        _a.sent();
                        if (this.__stateTypeMap[audioID] !== 'fadeOut') {
                            return [2 /*return*/];
                        }
                        return [3 /*break*/, 1];
                    case 3:
                        if (stop) {
                            cc.audioEngine.stop(audioID);
                            if (this.__bgmAudioID === audioID) {
                                this.__bgmAudioID = -1;
                            }
                        }
                        else {
                            cc.audioEngine.setVolume(audioID, target);
                        }
                        delete this.__stateTypeMap[audioID];
                        return [2 /*return*/];
                }
            });
        });
    };
    AudioMgr.prototype.fadeInBGM = function (duration, ratio) {
        if (ratio === void 0) { ratio = 1; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.__fadeInBGM(this.__bgmAudioID, duration, ratio);
                return [2 /*return*/];
            });
        });
    };
    AudioMgr.prototype.__fadeInBGM = function (audioID, duration, ratio) {
        if (ratio === void 0) { ratio = 1; }
        return __awaiter(this, void 0, void 0, function () {
            var type, volume, target, diff;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (audioID < 0) {
                            return [2 /*return*/];
                        }
                        type = "fadeIn" + ratio;
                        this.__stateTypeMap[audioID] = type;
                        volume = cc.audioEngine.getVolume(audioID);
                        target = this.bgmVolume * this.__bgmVolumeRatio * ratio;
                        diff = (target - volume) / 25 / duration;
                        _a.label = 1;
                    case 1:
                        if (!(volume < target)) return [3 /*break*/, 3];
                        volume += diff;
                        cc.audioEngine.setVolume(audioID, volume);
                        return [4 /*yield*/, ut.wait(0.04)];
                    case 2:
                        _a.sent();
                        if (this.__stateTypeMap[audioID] !== type) {
                            return [2 /*return*/];
                        }
                        return [3 /*break*/, 1];
                    case 3:
                        cc.audioEngine.setVolume(audioID, target);
                        delete this.__stateTypeMap[audioID];
                        return [2 /*return*/];
                }
            });
        });
    };
    // 播发音效
    AudioMgr.prototype.playSFX = function (url, opts) {
        return __awaiter(this, void 0, void 0, function () {
            var tag, audio, asset;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!url) {
                            return [2 /*return*/, -1];
                        }
                        else if (this.sfxVolume <= 0 && !(opts === null || opts === void 0 ? void 0 : opts.loop)) {
                            return [2 /*return*/, -1];
                        }
                        tag = (opts === null || opts === void 0 ? void 0 : opts.tag) || '';
                        if (url instanceof cc.AudioClip) {
                            this.__sfxs_infos.push({ url: url.nativeUrl, tag: tag, id: -1 });
                            return [2 /*return*/, this.__playSFX(url, url.nativeUrl, opts)];
                        }
                        audio = this.__getAudio(url);
                        if (audio) {
                            this.__sfxs_infos.push({ url: url, tag: tag, id: -1 });
                            return [2 /*return*/, this.__playSFX(audio, url, opts)];
                        }
                        this.__sfxs_infos.push({ url: url, tag: tag, id: -1 });
                        return [4 /*yield*/, this.__load(url)];
                    case 1:
                        asset = _a.sent();
                        if (asset === null || asset === void 0 ? void 0 : asset.audio) {
                            this.__addAudio(asset.mod, asset.url, asset.audio);
                            return [2 /*return*/, this.__playSFX(asset.audio, url, opts)];
                        }
                        cc.error('playSFX error url=', url);
                        return [2 /*return*/, -1];
                }
            });
        });
    };
    AudioMgr.prototype.__playSFX = function (audio, url, opts) {
        var _this = this;
        var _a, _b;
        var tag = (opts === null || opts === void 0 ? void 0 : opts.tag) || '';
        var info = this.__sfxs_infos.find(function (m) { return m.url === url && m.tag === tag; });
        if (!info) {
            return;
        }
        var loop = !!(opts === null || opts === void 0 ? void 0 : opts.loop), volume = (_a = opts === null || opts === void 0 ? void 0 : opts.volume) !== null && _a !== void 0 ? _a : 1, startTime = (_b = opts === null || opts === void 0 ? void 0 : opts.startTime) !== null && _b !== void 0 ? _b : 0, onComplete = opts === null || opts === void 0 ? void 0 : opts.onComplete;
        var audioId = info.id = cc.audioEngine.play(audio, loop, this.sfxVolume * volume);
        if (!loop) { //只有不是循环的才会有回调
            cc.audioEngine.setFinishCallback(audioId, function () {
                _this.__sfxs_infos.remove('id', audioId);
                onComplete && onComplete();
            });
        }
        if (startTime) {
            cc.audioEngine.setCurrentTime(audioId, startTime);
        }
        if (this.__isPause) {
            cc.audioEngine.pause(audioId);
        }
        return audioId;
    };
    AudioMgr.prototype.stopSFX = function (val, tag) {
        if (tag === void 0) { tag = ''; }
        if (!val) {
        }
        else if (typeof (val) === 'number') {
            cc.audioEngine.stop(val);
            this.__sfxs_infos.remove('id', val);
        }
        else if (typeof (val) === 'string') {
            this.__sfxs_infos.delete(function (m) { return m.url === val && m.tag === tag; }).forEach(function (m) { return cc.audioEngine.stop(m.id); });
        }
        else if (val instanceof cc.AudioClip) {
            this.__sfxs_infos.delete(function (m) { return m.url === val.url && m.tag === tag; }).forEach(function (m) { return cc.audioEngine.stop(m.id); });
        }
    };
    AudioMgr.prototype.fadeOutSFX = function (duration, val, tag) {
        var _this = this;
        if (tag === void 0) { tag = ''; }
        if (!val) {
        }
        else if (typeof (val) === 'number') {
            this.__fadeOutSFX(val, duration);
            this.__sfxs_infos.remove('id', val);
        }
        else if (typeof (val) === 'string') {
            this.__sfxs_infos.delete(function (m) { return m.url === val && m.tag === tag; }).forEach(function (m) {
                _this.__fadeOutSFX(m.id, duration);
            });
        }
        else if (val instanceof cc.AudioClip) {
            this.__sfxs_infos.delete(function (m) { return m.url === val.url && m.tag === tag; }).forEach(function (m) {
                _this.__fadeOutSFX(m.id, duration);
            });
        }
    };
    AudioMgr.prototype.__fadeOutSFX = function (audioID, duration) {
        return __awaiter(this, void 0, void 0, function () {
            var volume, target, diff;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (audioID < 0) {
                            return [2 /*return*/];
                        }
                        volume = cc.audioEngine.getVolume(audioID);
                        target = 0;
                        diff = (target - volume) / 25 / duration;
                        _a.label = 1;
                    case 1:
                        if (!(volume + diff >= target)) return [3 /*break*/, 3];
                        volume += diff;
                        cc.audioEngine.setVolume(audioID, volume);
                        return [4 /*yield*/, ut.wait(0.04)];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 1];
                    case 3:
                        cc.audioEngine.stop(audioID);
                        return [2 /*return*/];
                }
            });
        });
    };
    return AudioMgr;
}());
window['audioMgr'] = new AudioMgr();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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