
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/activity/MysteryboxShow103PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '078f3kY+2RNQ5TJtPf9XXP6', 'MysteryboxShow103PnlCtrl');
// app/script/view/activity/MysteryboxShow103PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var MysteryboxShow103PnlCtrl = /** @class */ (function (_super) {
    __extends(MysteryboxShow103PnlCtrl, _super);
    function MysteryboxShow103PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_be_n
        _this.posNode_ = null; // path://root/pos_n
        _this.rootNode_ = null; // path://root/root_nbe_n
        _this.buttonNode_ = null; // path://root/button_n
        _this.descLbl_ = null; // path://root/desc_l
        //@end
        _this.MYSTERY_BOX_ID = 103;
        _this.boxId = 0;
        _this.ignot = 0;
        _this.ANIMATIONS = ['mysterybox_anim_103_normal', 'mysterybox_anim_103_special'];
        _this.EFFECT_ANIMS = ['mysterybox_anim_103_effect1', 'mysterybox_anim_103_effect2'];
        return _this;
    }
    MysteryboxShow103PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    MysteryboxShow103PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false });
                return [2 /*return*/];
            });
        });
    };
    MysteryboxShow103PnlCtrl.prototype.onEnter = function (id, ignot) {
        this.boxId = id;
        this.ignot = ignot;
        this.init();
    };
    MysteryboxShow103PnlCtrl.prototype.onRemove = function () {
    };
    MysteryboxShow103PnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/root_nbe_n
    MysteryboxShow103PnlCtrl.prototype.onClickRoot = function (event, data) {
        audioMgr.playSFX('click');
        this.do(event.target.name);
    };
    // path://close_be_n
    MysteryboxShow103PnlCtrl.prototype.onClickClose = function (event, data) {
        this.hide();
    };
    // path://root/button_n/once_again_be
    MysteryboxShow103PnlCtrl.prototype.onClickOnceAgain = function (event, data) {
        var _this = this;
        if (GameHelper_1.gameHpr.user.getIngot() < this.ignot) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
        }
        else if (!GameHelper_1.gameHpr.isNoLongerTip(Enums_1.NoLongerTipKey.SHOW_BUY_MYSTERYBOX_RULE)) {
            ViewHelper_1.viewHelper.showPnl('common/MysteryboxRule', this.MYSTERY_BOX_ID, function () { return _this.isValid && _this.init(); });
        }
        else {
            this.init();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    MysteryboxShow103PnlCtrl.prototype.init = function () {
        var _this = this;
        this.rootNode_.children.forEach(function (m) {
            var _a;
            m.active = true;
            m.opacity = 255;
            var effect = m.Child('item/effect', cc.Animation);
            effect.reset();
            m.Child('count').active = false;
            m.Component(cc.Button).interactable = false;
            var anim = m.Child('item', cc.Animation), name = (_a = m.Data) !== null && _a !== void 0 ? _a : _this.ANIMATIONS[0];
            anim.reset(name);
            m.Child('item/card/val', cc.Sprite).spriteFrame = null;
            m.setPosition(0, 0);
            var target = _this.posNode_.Child(m.name).getPosition();
            m.stopAllActions();
            cc.tween(m)
                .to(0.3, { x: target.x, y: target.y }, { easing: cc.easing.sineOut })
                .call(function () { return m.Component(cc.Button).interactable = true; })
                .start();
        });
        this.buttonNode_.active = false;
        this.descLbl_.setLocaleKey('');
        ut.wait(0.4, this).then(function () {
            if (_this.isValid) {
                _this.descLbl_.setLocaleKey('ui.please_select_mb_box_102');
            }
        });
    };
    // 购买盲盒
    MysteryboxShow103PnlCtrl.prototype.do = function (index) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data, json, _loop_1, this_1, i, l, isHideCard, it, anim;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        mc.lockTouch('item_skin');
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('lobby/HD_BuySkinBlindBox', { id: this.boxId }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        this.descLbl_.setLocaleKey('');
                        GameHelper_1.gameHpr.user.setIngot(data.ingot);
                        GameHelper_1.gameHpr.user.setSkinItemList(data.skinItemList);
                        json = assetsMgr.getJsonData('pawnSkin', data.skinId);
                        if (!json) {
                            mc.unlockTouch('item_skin');
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.UNKNOWN)];
                        }
                        _loop_1 = function (i, l) {
                            var it_1 = this_1.rootNode_.children[i];
                            it_1.Component(cc.Button).interactable = false;
                            if (it_1.name !== index) {
                                cc.tween(it_1)
                                    .to(0.3, { opacity: 0 })
                                    .call(function () { return it_1.active = false; })
                                    .start();
                            }
                        };
                        this_1 = this;
                        for (i = 0, l = this.rootNode_.children.length; i < l; i++) {
                            _loop_1(i, l);
                        }
                        isHideCard = json.value === 2;
                        it = this.rootNode_.Child(index);
                        anim = it.Data = isHideCard ? this.ANIMATIONS[1] : this.ANIMATIONS[0];
                        cc.tween(it)
                            .to(0.3, { x: 0, y: 0 }, { easing: cc.easing.sineIn })
                            .call(function () {
                            var item = it.Child('item');
                            var spr = item.Child('card/val', cc.Sprite);
                            ResHelper_1.resHelper.loadPawnHeadIcon(data.skinId, spr, _this.key);
                            var time = Date.now();
                            item.Component(cc.Animation).playAsync(anim).then(function () {
                                var _a;
                                // console.log('cost time: ', Date.now() - time)
                                var count = ((_a = GameHelper_1.gameHpr.user.getSkinItemList().filter(function (m) { return m.id === data.skinId; })) === null || _a === void 0 ? void 0 : _a.length) || 1;
                                it.Child('count').active = true;
                                it.Child('count/val', cc.Label).string = Math.max(0, count - 1) + '';
                                _this.closeNode_.active = true;
                                _this.buttonNode_.active = true;
                                _this.descLbl_.setLocaleKey('ui.click_close_desc');
                                mc.unlockTouch('item_skin');
                            });
                            ut.wait(0.72).then(function () {
                                var effectAni = item.Child('effect', cc.Animation);
                                effectAni.play(isHideCard ? _this.EFFECT_ANIMS[1] : _this.EFFECT_ANIMS[0]);
                            });
                        })
                            .start();
                        return [2 /*return*/];
                }
            });
        });
    };
    MysteryboxShow103PnlCtrl = __decorate([
        ccclass
    ], MysteryboxShow103PnlCtrl);
    return MysteryboxShow103PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = MysteryboxShow103PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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