
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/NoticePermission3PnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '0b7c15bw3lBxrIdcBa72ZBl', 'NoticePermission3PnlCtrl');
// app/script/view/common/NoticePermission3PnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var JsbHelper_1 = require("../../common/helper/JsbHelper");
var ccclass = cc._decorator.ccclass;
var NoticePermission3PnlCtrl = /** @class */ (function (_super) {
    __extends(NoticePermission3PnlCtrl, _super);
    function NoticePermission3PnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descNode_ = null; // path://root/message/desc_n
        _this.listNode_ = null; // path://root/message/list_n
        //@end
        _this.MSGS = ['ui.fcm_message_12', 'ui.team_invite'];
        return _this;
    }
    NoticePermission3PnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    NoticePermission3PnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    NoticePermission3PnlCtrl.prototype.onEnter = function (data) {
        this.showDesc();
    };
    NoticePermission3PnlCtrl.prototype.onRemove = function () {
    };
    NoticePermission3PnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    NoticePermission3PnlCtrl.prototype.onClickButtons = function (event, data) {
        var name = event.target.name;
        if (name === 'ok') {
            JsbHelper_1.jsbHelper.openSelfSetting();
        }
        else if (name === 'cancel') {
            this.hide();
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    NoticePermission3PnlCtrl.prototype.showDesc = function () {
        return __awaiter(this, void 0, void 0, function () {
            var width_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ut.waitNextFrame()];
                    case 1:
                        _a.sent();
                        if (this.isValid) {
                            width_1 = this.descNode_.width / 2;
                            this.listNode_.Items(this.MSGS, function (it, data) {
                                it.x = -width_1;
                                it.Child('val').setLocaleKey(data);
                            });
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    NoticePermission3PnlCtrl = __decorate([
        ccclass
    ], NoticePermission3PnlCtrl);
    return NoticePermission3PnlCtrl;
}(mc.BasePnlCtrl));
exports.default = NoticePermission3PnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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