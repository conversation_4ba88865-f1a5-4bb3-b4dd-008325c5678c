
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/AlliMemberBattlePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a2599BlphdBNrTDrq+3KP7b', 'AlliMemberBattlePnlCtrl');
// app/script/view/build/AlliMemberBattlePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var AlliMemberBattlePnlCtrl = /** @class */ (function (_super) {
    __extends(AlliMemberBattlePnlCtrl, _super);
    function AlliMemberBattlePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        return _this;
        // ----------------------------------------- button listener function -------------------------------------------
        //@autocode button listener
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    AlliMemberBattlePnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AlliMemberBattlePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    AlliMemberBattlePnlCtrl.prototype.onEnter = function (data, alliScores) {
        data = data || {};
        this.rootNode_.Child('0').setLocaleKey('ui.alli_battle_record_x', data.battleCount || 0);
        this.rootNode_.Child('1').setLocaleKey('ui.alli_battle_record_0_1', data.killCount || 0);
        this.rootNode_.Child('2').setLocaleKey('ui.alli_battle_record_1_1', data.deadCount || 0);
        this.rootNode_.Child('3').setLocaleKey('ui.alli_battle_record_0_0', data.pawnDamage || 0);
        this.rootNode_.Child('4').setLocaleKey('ui.alli_battle_record_1_0', data.PawnHitDamage || 0);
        this.rootNode_.Child('5').setLocaleKey('ui.alli_battle_record_2_0', data.buildDamage || 0);
        this.rootNode_.Child('6').setLocaleKey('ui.alli_battle_record_3_0', data.damageToBuild || 0);
        var cur = alliScores[0] || 0, top = alliScores[1] || 0;
        this.rootNode_.Child('7/val').setLocaleKey('ui.score_rand_desc_2', cur);
        var topNode = this.rootNode_.Child('7/top');
        if (topNode.active = top > cur) {
            topNode.setLocaleKey('ui.score_historic_high_desc', top);
        }
    };
    AlliMemberBattlePnlCtrl.prototype.onRemove = function () {
    };
    AlliMemberBattlePnlCtrl.prototype.onClean = function () {
    };
    AlliMemberBattlePnlCtrl = __decorate([
        ccclass
    ], AlliMemberBattlePnlCtrl);
    return AlliMemberBattlePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliMemberBattlePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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