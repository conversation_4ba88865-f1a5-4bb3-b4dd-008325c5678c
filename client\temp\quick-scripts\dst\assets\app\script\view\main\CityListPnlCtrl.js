
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/main/CityListPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3e2f1JBZV9EH4gT6uyrZx6w', 'CityListPnlCtrl');
// app/script/view/main/CityListPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var CityListPnlCtrl = /** @class */ (function (_super) {
    __extends(CityListPnlCtrl, _super);
    function CityListPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.cell = null;
        _this.datas = [];
        return _this;
    }
    CityListPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    CityListPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                this.datas.length = 0;
                assetsMgr.getJson('city').datas.forEach(function (m) {
                    if (!m.bt_count) {
                        return;
                    }
                    _this.datas.push({
                        id: m.id,
                        json: m,
                        name: 'cityText.name_' + m.id,
                        desc: 'cityText.desc_' + m.id,
                        cost: GameHelper_1.gameHpr.stringToCTypes(m.bt_cost),
                        btTime: m.bt_time || 0,
                        yetBtCount: 0,
                    });
                });
                return [2 /*return*/];
            });
        });
    };
    CityListPnlCtrl.prototype.onEnter = function (cell) {
        this.cell = cell;
        this.updateList();
    };
    CityListPnlCtrl.prototype.onRemove = function () {
    };
    CityListPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item/unlock_be
    CityListPnlCtrl.prototype.onClickUnlock = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (!data) {
            return;
        }
        else if (!GameHelper_1.gameHpr.checkCTypes(data.cost)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        GameHelper_1.gameHpr.world.createCity(this.cell.index, data.id).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                _this.hide();
                _this.emit(EventType_1.default.CLOSE_SELECT_CELL);
            }
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    CityListPnlCtrl.prototype.updateList = function () {
        var _this = this;
        var landType = this.cell.landType, landLv = this.cell.landLv, landId = landType * 100 + landLv;
        var datas = [];
        this.datas.forEach(function (m) {
            if (!m.json.need_land || (m.json.need_land === landType && landLv > 1)) {
                m.yetBtCount = GameHelper_1.gameHpr.getCitysById(m.id).length;
                var id = m.id * 1000 + landId;
                var json_1 = assetsMgr.getJsonData('cityResAttr', id);
                m.res = json_1 && Constant_1.CELL_RES_FIELDS.filter(function (m) { return !!json_1[m]; }).map(function (key) {
                    return { key: key, val: json_1[key] || 0 };
                });
                datas.push(m);
            }
        });
        // datas.sort((a, b) => (b.json.bt_count - b.yetBtCount) - (a.json.bt_count - a.yetBtCount))
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        var extraCount = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CITY_COUNT_LIMIT);
        var fortCd = ut.numberFixed(1 - GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.CITY_BUILD_CD) * 0.01);
        this.listSv_.Items(datas, function (it, data, i) {
            it.Data = data;
            var base = it.Child('base');
            ResHelper_1.resHelper.loadCityIcon(data.id, base.Child('icon/val'), _this.key);
            base.Child('name').setLocaleKey(data.name);
            base.Child('desc/val').setLocaleKey(data.desc);
            var res = base.Child('desc/res');
            if (res.active = !!data.res) {
                res.Items(data.res, function (it, d) {
                    it.Child('icon', cc.Sprite).spriteFrame = assetsMgr.getImage(d.key);
                    it.Child('val').setLocaleKey('ui.res_desc', d.val);
                });
            }
            it.Child('need/cost').Items(data.cost, function (m, cost) { return ViewHelper_1.viewHelper.updateCostViewOne(m, cost, true); });
            var bottom = it.Child('need/bottom');
            var cd = data.id === Constant_1.CITY_FORT_NID ? fortCd : 0;
            bottom.Child('time/val', cc.Label).Color(cd > 0 && cd < 1 ? '#4AB32E' : '#756963').string = ut.secondFormat(cd ? Math.floor(ut.numberFixed(data.btTime * cd)) : data.btTime, 'h:mm:ss');
            var button = it.Child('button');
            var cur = data.yetBtCount, max = data.json.bt_count + extraCount;
            button.Child('count/val', cc.Label).Color(cur === max ? '#756963' : (cur < max ? '#4AB32E' : '#D7634D')).string = '' + cur;
            button.Child('count/max', cc.Label).string = '/' + max;
            var cond = it.Child('cond'), condText = GameHelper_1.gameHpr.checkUnlcokBuildCond(data.json.bt_cond);
            var isHasCond = cond.active = !!condText;
            button.Child('unlock_be').active = !isHasCond;
            if (isHasCond) {
                cond.Child('val').setLocaleKey(condText);
            }
            else {
                button.Child('unlock_be', cc.Button).interactable = cur < max;
            }
        });
    };
    CityListPnlCtrl = __decorate([
        ccclass
    ], CityListPnlCtrl);
    return CityListPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = CityListPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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