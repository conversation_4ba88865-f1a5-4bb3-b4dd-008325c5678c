
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/constant/CommunityConfig.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1148cxupzhDdLcOfEfPnter', 'CommunityConfig');
// app/script/common/constant/CommunityConfig.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.COMMUNITY_PRIZE_QUESTION_GLOBAL = exports.COMMUNITY_CONFIG_GLOBAL = exports.COMMUNITY_CONFIG_INLAND = void 0;
// 社区配置 国内
var COMMUNITY_CONFIG_INLAND = {
    default: [
        // { key: 'qq', url: '947733478' },
        { key: 'weibo', url: 'https://www.baidu.com/' },
    ],
};
exports.COMMUNITY_CONFIG_INLAND = COMMUNITY_CONFIG_INLAND;
// 社区配置 海外
var COMMUNITY_CONFIG_GLOBAL = {
    default: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/NinetyThousandAcres' },
    ],
    cn: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/profile.php?id=100091571177907' },
        { key: 'line', url: 'https://line.me/ti/g2/CVDA9Vb11Vi8FbCXAU15Q2tb0Dkt5TIrSmD7FQ?utm_source=invitation&utm_medium=link_copy&utm_campaign=default' },
    ],
    hk: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/profile.php?id=100091571177907' },
        { key: 'line', url: 'https://line.me/ti/g2/CVDA9Vb11Vi8FbCXAU15Q2tb0Dkt5TIrSmD7FQ?utm_source=invitation&utm_medium=link_copy&utm_campaign=default' },
    ],
    tw: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/profile.php?id=100091571177907' },
        { key: 'line', url: 'https://line.me/ti/g2/CVDA9Vb11Vi8FbCXAU15Q2tb0Dkt5TIrSmD7FQ?utm_source=invitation&utm_medium=link_copy&utm_campaign=default' },
    ],
    kr: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'cafe', url: 'https://cafe.naver.com/9nta' },
        { key: 'kakao', url: 'http://pf.kakao.com/_xcRazn' },
    ],
    jp: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'twitter', url: 'https://x.com/kyuumannho' },
        { key: 'gamerch', url: 'https://gamerch.com/kyuumannho/' },
    ],
    idl: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/NinetyThousandAcres' },
    ],
    th: [
        { key: 'discord', url: 'https://discord.gg/sxsv5nBBbT' },
        { key: 'facebook', url: 'https://www.facebook.com/NinetyThousandAcres' },
    ],
};
exports.COMMUNITY_CONFIG_GLOBAL = COMMUNITY_CONFIG_GLOBAL;
var COMMUNITY_PRIZE_QUESTION_GLOBAL = {
    default: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSd--58f66HInDC7fB0oqkYxT4HzZScj-mpsidjTiXWsjjrIig/viewform?usp=pp_url&',
        code: 347808658
    },
    cn: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSfAUrM94EKlKGyC2QllFHNG9JAdUvOFlzCOIJZDhenhV_x7Gw/viewform?usp=pp_url&',
        code: 1997217508
    },
    hk: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSfAUrM94EKlKGyC2QllFHNG9JAdUvOFlzCOIJZDhenhV_x7Gw/viewform?usp=pp_url&',
        code: 1997217508
    },
    tw: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSfAUrM94EKlKGyC2QllFHNG9JAdUvOFlzCOIJZDhenhV_x7Gw/viewform?usp=pp_url&',
        code: 1997217508
    },
    kr: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSfNJrsljxZEuqk9dBv-UP5g-F_8TXh-FCQCeRSSqEI8YcFqEg/viewform?usp=pp_url&',
        code: 621597326
    },
    jp: {
        url: 'https://docs.google.com/forms/d/e/1FAIpQLSdxnX8AEIQygkKxyevp-pIKGP4SA_ma4jqCy2uxZJYyTChq8w/viewform?usp=pp_url&',
        code: 129795041
    },
};
exports.COMMUNITY_PRIZE_QUESTION_GLOBAL = COMMUNITY_PRIZE_QUESTION_GLOBAL;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXGNvbW1vblxcY29uc3RhbnRcXENvbW11bml0eUNvbmZpZy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSxVQUFVO0FBQ1YsSUFBTSx1QkFBdUIsR0FBRztJQUM1QixPQUFPLEVBQUU7UUFDTCxtQ0FBbUM7UUFDbkMsRUFBRSxHQUFHLEVBQUUsT0FBTyxFQUFFLEdBQUcsRUFBRSx3QkFBd0IsRUFBRTtLQUNsRDtDQUNKLENBQUE7QUFvRkcsMERBQXVCO0FBbEYzQixVQUFVO0FBQ1YsSUFBTSx1QkFBdUIsR0FBRztJQUM1QixPQUFPLEVBQUU7UUFDTCxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLCtCQUErQixFQUFFO1FBQ3hELEVBQUUsR0FBRyxFQUFFLFVBQVUsRUFBRSxHQUFHLEVBQUUsOENBQThDLEVBQUU7S0FFM0U7SUFFRCxFQUFFLEVBQUU7UUFDQSxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLCtCQUErQixFQUFFO1FBQ3hELEVBQUUsR0FBRyxFQUFFLFVBQVUsRUFBRSxHQUFHLEVBQUUseURBQXlELEVBQUU7UUFDbkYsRUFBRSxHQUFHLEVBQUUsTUFBTSxFQUFFLEdBQUcsRUFBRSw4SEFBOEgsRUFBRTtLQUN2SjtJQUNELEVBQUUsRUFBRTtRQUNBLEVBQUUsR0FBRyxFQUFFLFNBQVMsRUFBRSxHQUFHLEVBQUUsK0JBQStCLEVBQUU7UUFDeEQsRUFBRSxHQUFHLEVBQUUsVUFBVSxFQUFFLEdBQUcsRUFBRSx5REFBeUQsRUFBRTtRQUNuRixFQUFFLEdBQUcsRUFBRSxNQUFNLEVBQUUsR0FBRyxFQUFFLDhIQUE4SCxFQUFFO0tBQ3ZKO0lBQ0QsRUFBRSxFQUFFO1FBQ0EsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLEdBQUcsRUFBRSwrQkFBK0IsRUFBRTtRQUN4RCxFQUFFLEdBQUcsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLHlEQUF5RCxFQUFFO1FBQ25GLEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsOEhBQThILEVBQUU7S0FDdko7SUFFRCxFQUFFLEVBQUU7UUFDQSxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLCtCQUErQixFQUFFO1FBQ3hELEVBQUUsR0FBRyxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsNkJBQTZCLEVBQUU7UUFDbkQsRUFBRSxHQUFHLEVBQUUsT0FBTyxFQUFFLEdBQUcsRUFBRSw2QkFBNkIsRUFBRTtLQUN2RDtJQUVELEVBQUUsRUFBRTtRQUNBLEVBQUUsR0FBRyxFQUFFLFNBQVMsRUFBRSxHQUFHLEVBQUUsK0JBQStCLEVBQUU7UUFDeEQsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLEdBQUcsRUFBRSwwQkFBMEIsRUFBRTtRQUNuRCxFQUFFLEdBQUcsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLGlDQUFpQyxFQUFFO0tBQzdEO0lBRUQsR0FBRyxFQUFFO1FBQ0QsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLEdBQUcsRUFBRSwrQkFBK0IsRUFBRTtRQUN4RCxFQUFFLEdBQUcsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLDhDQUE4QyxFQUFFO0tBQzNFO0lBRUQsRUFBRSxFQUFFO1FBQ0EsRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLEdBQUcsRUFBRSwrQkFBK0IsRUFBRTtRQUN4RCxFQUFFLEdBQUcsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLDhDQUE4QyxFQUFFO0tBQzNFO0NBQ0osQ0FBQTtBQXNDRywwREFBdUI7QUFwQzNCLElBQU0sK0JBQStCLEdBQUc7SUFDcEMsT0FBTyxFQUFFO1FBQ0wsR0FBRyxFQUFFLGlIQUFpSDtRQUN0SCxJQUFJLEVBQUUsU0FBUztLQUNsQjtJQUVELEVBQUUsRUFBRTtRQUNBLEdBQUcsRUFBRSxpSEFBaUg7UUFDdEgsSUFBSSxFQUFFLFVBQVU7S0FDbkI7SUFDRCxFQUFFLEVBQUU7UUFDQSxHQUFHLEVBQUUsaUhBQWlIO1FBQ3RILElBQUksRUFBRSxVQUFVO0tBQ25CO0lBQ0QsRUFBRSxFQUFFO1FBQ0EsR0FBRyxFQUFFLGlIQUFpSDtRQUN0SCxJQUFJLEVBQUUsVUFBVTtLQUNuQjtJQUVELEVBQUUsRUFBRTtRQUNBLEdBQUcsRUFBRSxpSEFBaUg7UUFDdEgsSUFBSSxFQUFFLFNBQVM7S0FDbEI7SUFFRCxFQUFFLEVBQUU7UUFDQSxHQUFHLEVBQUUsaUhBQWlIO1FBQ3RILElBQUksRUFBRSxTQUFTO0tBQ2xCO0NBS0osQ0FBQTtBQUtHLDBFQUErQiIsImZpbGUiOiIiLCJzb3VyY2VSb290IjoiLyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG4vLyDnpL7ljLrphY3nva4g5Zu95YaFXHJcbmNvbnN0IENPTU1VTklUWV9DT05GSUdfSU5MQU5EID0ge1xyXG4gICAgZGVmYXVsdDogW1xyXG4gICAgICAgIC8vIHsga2V5OiAncXEnLCB1cmw6ICc5NDc3MzM0NzgnIH0sXHJcbiAgICAgICAgeyBrZXk6ICd3ZWlibycsIHVybDogJ2h0dHBzOi8vd3d3LmJhaWR1LmNvbS8nIH0sXHJcbiAgICBdLFxyXG59XHJcblxyXG4vLyDnpL7ljLrphY3nva4g5rW35aSWXHJcbmNvbnN0IENPTU1VTklUWV9DT05GSUdfR0xPQkFMID0ge1xyXG4gICAgZGVmYXVsdDogWyAvL+m7mOiupCDmrKfnvo4gXHJcbiAgICAgICAgeyBrZXk6ICdkaXNjb3JkJywgdXJsOiAnaHR0cHM6Ly9kaXNjb3JkLmdnL3N4c3Y1bkJCYlQnIH0sXHJcbiAgICAgICAgeyBrZXk6ICdmYWNlYm9vaycsIHVybDogJ2h0dHBzOi8vd3d3LmZhY2Vib29rLmNvbS9OaW5ldHlUaG91c2FuZEFjcmVzJyB9LFxyXG4gICAgICAgIC8vIHsga2V5OiAnaW5zdGFncmFtJywgdXJsOiAnaHR0cHM6Ly93d3cuaW5zdGFncmFtLmNvbS9uaW5ldHl0aG91c2FuZGFjcmVzLycgfSxcclxuICAgIF0sXHJcblxyXG4gICAgY246IFsgLy/nroDkvZNcclxuICAgICAgICB7IGtleTogJ2Rpc2NvcmQnLCB1cmw6ICdodHRwczovL2Rpc2NvcmQuZ2cvc3hzdjVuQkJiVCcgfSxcclxuICAgICAgICB7IGtleTogJ2ZhY2Vib29rJywgdXJsOiAnaHR0cHM6Ly93d3cuZmFjZWJvb2suY29tL3Byb2ZpbGUucGhwP2lkPTEwMDA5MTU3MTE3NzkwNycgfSxcclxuICAgICAgICB7IGtleTogJ2xpbmUnLCB1cmw6ICdodHRwczovL2xpbmUubWUvdGkvZzIvQ1ZEQTlWYjExVmk4RmJDWEFVMTVRMnRiMERrdDVUSXJTbUQ3RlE/dXRtX3NvdXJjZT1pbnZpdGF0aW9uJnV0bV9tZWRpdW09bGlua19jb3B5JnV0bV9jYW1wYWlnbj1kZWZhdWx0JyB9LFxyXG4gICAgXSxcclxuICAgIGhrOiBbIC8v6aaZ5rivXHJcbiAgICAgICAgeyBrZXk6ICdkaXNjb3JkJywgdXJsOiAnaHR0cHM6Ly9kaXNjb3JkLmdnL3N4c3Y1bkJCYlQnIH0sXHJcbiAgICAgICAgeyBrZXk6ICdmYWNlYm9vaycsIHVybDogJ2h0dHBzOi8vd3d3LmZhY2Vib29rLmNvbS9wcm9maWxlLnBocD9pZD0xMDAwOTE1NzExNzc5MDcnIH0sXHJcbiAgICAgICAgeyBrZXk6ICdsaW5lJywgdXJsOiAnaHR0cHM6Ly9saW5lLm1lL3RpL2cyL0NWREE5VmIxMVZpOEZiQ1hBVTE1UTJ0YjBEa3Q1VElyU21EN0ZRP3V0bV9zb3VyY2U9aW52aXRhdGlvbiZ1dG1fbWVkaXVtPWxpbmtfY29weSZ1dG1fY2FtcGFpZ249ZGVmYXVsdCcgfSxcclxuICAgIF0sXHJcbiAgICB0dzogWyAvL+WPsOa5vlxyXG4gICAgICAgIHsga2V5OiAnZGlzY29yZCcsIHVybDogJ2h0dHBzOi8vZGlzY29yZC5nZy9zeHN2NW5CQmJUJyB9LFxyXG4gICAgICAgIHsga2V5OiAnZmFjZWJvb2snLCB1cmw6ICdodHRwczovL3d3dy5mYWNlYm9vay5jb20vcHJvZmlsZS5waHA/aWQ9MTAwMDkxNTcxMTc3OTA3JyB9LFxyXG4gICAgICAgIHsga2V5OiAnbGluZScsIHVybDogJ2h0dHBzOi8vbGluZS5tZS90aS9nMi9DVkRBOVZiMTFWaThGYkNYQVUxNVEydGIwRGt0NVRJclNtRDdGUT91dG1fc291cmNlPWludml0YXRpb24mdXRtX21lZGl1bT1saW5rX2NvcHkmdXRtX2NhbXBhaWduPWRlZmF1bHQnIH0sXHJcbiAgICBdLFxyXG5cclxuICAgIGtyOiBbIC8v6Z+p5Zu9XHJcbiAgICAgICAgeyBrZXk6ICdkaXNjb3JkJywgdXJsOiAnaHR0cHM6Ly9kaXNjb3JkLmdnL3N4c3Y1bkJCYlQnIH0sXHJcbiAgICAgICAgeyBrZXk6ICdjYWZlJywgdXJsOiAnaHR0cHM6Ly9jYWZlLm5hdmVyLmNvbS85bnRhJyB9LFxyXG4gICAgICAgIHsga2V5OiAna2FrYW8nLCB1cmw6ICdodHRwOi8vcGYua2FrYW8uY29tL194Y1Jhem4nIH0sXHJcbiAgICBdLFxyXG5cclxuICAgIGpwOiBbIC8v5pel5pysXHJcbiAgICAgICAgeyBrZXk6ICdkaXNjb3JkJywgdXJsOiAnaHR0cHM6Ly9kaXNjb3JkLmdnL3N4c3Y1bkJCYlQnIH0sXHJcbiAgICAgICAgeyBrZXk6ICd0d2l0dGVyJywgdXJsOiAnaHR0cHM6Ly94LmNvbS9reXV1bWFubmhvJyB9LFxyXG4gICAgICAgIHsga2V5OiAnZ2FtZXJjaCcsIHVybDogJ2h0dHBzOi8vZ2FtZXJjaC5jb20va3l1dW1hbm5oby8nIH0sXHJcbiAgICBdLFxyXG5cclxuICAgIGlkbDogWyAvL+WNsOWwvFxyXG4gICAgICAgIHsga2V5OiAnZGlzY29yZCcsIHVybDogJ2h0dHBzOi8vZGlzY29yZC5nZy9zeHN2NW5CQmJUJyB9LFxyXG4gICAgICAgIHsga2V5OiAnZmFjZWJvb2snLCB1cmw6ICdodHRwczovL3d3dy5mYWNlYm9vay5jb20vTmluZXR5VGhvdXNhbmRBY3JlcycgfSxcclxuICAgIF0sXHJcblxyXG4gICAgdGg6IFsgLy/ms7Dlm71cclxuICAgICAgICB7IGtleTogJ2Rpc2NvcmQnLCB1cmw6ICdodHRwczovL2Rpc2NvcmQuZ2cvc3hzdjVuQkJiVCcgfSxcclxuICAgICAgICB7IGtleTogJ2ZhY2Vib29rJywgdXJsOiAnaHR0cHM6Ly93d3cuZmFjZWJvb2suY29tL05pbmV0eVRob3VzYW5kQWNyZXMnIH0sXHJcbiAgICBdLFxyXG59XHJcblxyXG5jb25zdCBDT01NVU5JVFlfUFJJWkVfUVVFU1RJT05fR0xPQkFMID0ge1xyXG4gICAgZGVmYXVsdDoge1xyXG4gICAgICAgIHVybDogJ2h0dHBzOi8vZG9jcy5nb29nbGUuY29tL2Zvcm1zL2QvZS8xRkFJcFFMU2QtLTU4ZjY2SEluREM3ZkIwb3FrWXhUNEh6WlNjai1tcHNpZGpUaVhXc2pqcklpZy92aWV3Zm9ybT91c3A9cHBfdXJsJicsXHJcbiAgICAgICAgY29kZTogMzQ3ODA4NjU4XHJcbiAgICB9LCAgLy8g6buY6K6kIFQzIOasp+e+jiBcclxuXHJcbiAgICBjbjoge1xyXG4gICAgICAgIHVybDogJ2h0dHBzOi8vZG9jcy5nb29nbGUuY29tL2Zvcm1zL2QvZS8xRkFJcFFMU2ZBVXJNOTRFS2xLR3lDMlFsbEZITkc5SkFkVXZPRmx6Q09JSlpEaGVuaFZfeDdHdy92aWV3Zm9ybT91c3A9cHBfdXJsJicsXHJcbiAgICAgICAgY29kZTogMTk5NzIxNzUwOFxyXG4gICAgfSwgLy8g566A5L2TXHJcbiAgICBoazoge1xyXG4gICAgICAgIHVybDogJ2h0dHBzOi8vZG9jcy5nb29nbGUuY29tL2Zvcm1zL2QvZS8xRkFJcFFMU2ZBVXJNOTRFS2xLR3lDMlFsbEZITkc5SkFkVXZPRmx6Q09JSlpEaGVuaFZfeDdHdy92aWV3Zm9ybT91c3A9cHBfdXJsJicsXHJcbiAgICAgICAgY29kZTogMTk5NzIxNzUwOFxyXG4gICAgfSwgLy8g6aaZ5rivXHJcbiAgICB0dzoge1xyXG4gICAgICAgIHVybDogJ2h0dHBzOi8vZG9jcy5nb29nbGUuY29tL2Zvcm1zL2QvZS8xRkFJcFFMU2ZBVXJNOTRFS2xLR3lDMlFsbEZITkc5SkFkVXZPRmx6Q09JSlpEaGVuaFZfeDdHdy92aWV3Zm9ybT91c3A9cHBfdXJsJicsXHJcbiAgICAgICAgY29kZTogMTk5NzIxNzUwOFxyXG4gICAgfSwgLy8g5Y+w5rm+XHJcblxyXG4gICAga3I6IHtcclxuICAgICAgICB1cmw6ICdodHRwczovL2RvY3MuZ29vZ2xlLmNvbS9mb3Jtcy9kL2UvMUZBSXBRTFNmTkpyc2xqeFpFdXFrOWRCdi1VUDVnLUZfOFRYaC1GQ1FDZVJTU3FFSThZY0ZxRWcvdmlld2Zvcm0/dXNwPXBwX3VybCYnLFxyXG4gICAgICAgIGNvZGU6IDYyMTU5NzMyNlxyXG4gICAgfSwgLy8g6Z+p5Zu9XHJcblxyXG4gICAganA6IHtcclxuICAgICAgICB1cmw6ICdodHRwczovL2RvY3MuZ29vZ2xlLmNvbS9mb3Jtcy9kL2UvMUZBSXBRTFNkeG5YOEFFSVF5Z2tLeHlldnAtcElLR1A0U0FfbWE0anFDeTJ1eFpKWXlUQ2hxOHcvdmlld2Zvcm0/dXNwPXBwX3VybCYnLFxyXG4gICAgICAgIGNvZGU6IDEyOTc5NTA0MVxyXG4gICAgfSwgLy8g5pel5pysXHJcblxyXG4gICAgLy8gaWRsOiB7fSwgLy8g5Y2w5bC8XHJcblxyXG4gICAgLy8gdGg6IHt9LCAvLyDms7Dlm71cclxufVxyXG5cclxuZXhwb3J0IHtcclxuICAgIENPTU1VTklUWV9DT05GSUdfSU5MQU5ELFxyXG4gICAgQ09NTVVOSVRZX0NPTkZJR19HTE9CQUwsXHJcbiAgICBDT01NVU5JVFlfUFJJWkVfUVVFU1RJT05fR0xPQkFMLFxyXG59Il19