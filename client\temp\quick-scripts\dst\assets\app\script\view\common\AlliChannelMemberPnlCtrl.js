
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/AlliChannelMemberPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '1dc20qEDJxGdpsQbf8RyodY', 'AlliChannelMemberPnlCtrl');
// app/script/view/common/AlliChannelMemberPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var AlliChannelMemberPnlCtrl = /** @class */ (function (_super) {
    __extends(AlliChannelMemberPnlCtrl, _super);
    function AlliChannelMemberPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.titleLbl_ = null; // path://root/title/title_l
        _this.listSv_ = null; // path://root/list_sv
        _this.buttonsNode_ = null; // path://root/buttons_n
        //@end
        _this.alliance = null;
        return _this;
    }
    AlliChannelMemberPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_ALLIANCE] = this.onUpdateAlliance, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_ALLI_CHANEEL_MEMEBERS] = this.onUpdateAlliMembers, _b.enter = true, _b)
        ];
    };
    AlliChannelMemberPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.alliance = this.getModel('alliance');
                return [2 /*return*/];
            });
        });
    };
    AlliChannelMemberPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        var uid = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        var channel = this.alliance.getChatChannels().find(function (m) { return m.uid === uid; });
        if (channel) {
            this.titleLbl_.string = assetsMgr.lang('ui.title_alli_channel_members', channel.name);
        }
        else {
            this.titleLbl_.string = assetsMgr.lang('ui.title_alli_main_channel_members');
        }
        this.updateAlliMembers();
        this.buttonsNode_.active = uid !== '0' && this.alliance.canEditChannel();
    };
    AlliChannelMemberPnlCtrl.prototype.onRemove = function () {
    };
    AlliChannelMemberPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_n/add_member_be
    AlliChannelMemberPnlCtrl.prototype.onClickAddMember = function (event, data) {
        var _this = this;
        var uid = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
        ViewHelper_1.viewHelper.showPnl('common/AddAlliMember', this.alliance.getChannelMemberInfos(uid).map(function (m) { return m.uid; }), function (uids) {
            var uid = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
            _this.reqUpdateAlliChatChannel(uid, uids, uids.length !== _this.alliance.getMembers().length);
        }, false);
    };
    // path://root/list_sv/view/content/member/kick_be
    AlliChannelMemberPnlCtrl.prototype.onClickKick = function (event, _data) {
        var _this = this;
        var data = event.target.parent.Data;
        if (data) {
            ViewHelper_1.viewHelper.showMessageBox('ui.remove_alli_channel_member_tip', {
                params: [data.nickname],
                ok: function () {
                    var uid = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
                    var memberUids = _this.alliance.getChannelMemberInfos(uid).filter(function (m) { return m.uid !== data.uid; }).map(function (m) { return m.uid; });
                    _this.reqUpdateAlliChatChannel(uid, memberUids, memberUids.length !== _this.alliance.getMembers().length);
                },
                cancel: function () { }
            });
        }
    };
    // path://root/buttons_n/dissolve_be
    AlliChannelMemberPnlCtrl.prototype.onClickDissolve = function (event, data) {
        var _this = this;
        var uid = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
        if (!uid || uid === '0') {
            return;
        }
        var name = this.alliance.getChatChannels().find(function (m) { return m.uid === uid; }).name;
        ViewHelper_1.viewHelper.showMessageBox('ui.remove_alli_channel_tip', {
            params: [name],
            ok: function () { return GameHelper_1.gameHpr.alliance.removeChatChannel(uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.hide();
                }
            }); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    AlliChannelMemberPnlCtrl.prototype.onUpdateAlliance = function () {
        this.hide();
    };
    AlliChannelMemberPnlCtrl.prototype.onUpdateAlliMembers = function (data, isKick) {
        if (isKick) {
            this.hide();
        }
        else {
            this.updateAlliMembers(data.uid);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    AlliChannelMemberPnlCtrl.prototype.updateAlliMembers = function (uid) {
        var _this = this;
        var channelUid = uid || GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL) || '';
        var members = this.alliance.getChannelMemberInfos(channelUid).sort(function (a, b) {
            if (a.offlineTime !== b.offlineTime) {
                return a.offlineTime - b.offlineTime;
            }
            else if (a.job !== b.job) {
                return a.job - b.job;
            }
            return a.joinTime - b.joinTime;
        });
        var selfUid = GameHelper_1.gameHpr.getUid();
        var memberCount = members.length;
        var now = Date.now();
        this.listSv_.stopAutoScroll();
        this.listSv_.List(memberCount, function (it, i) {
            var data = it.Data = members[i];
            it.Child('kick_be').active = data.uid !== selfUid && channelUid !== '0' && _this.alliance.canEditChannel();
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            var nameLbl = it.Child('name', cc.Label), jobNode = it.Child('job');
            nameLbl.string = ut.nameFormator(data.nickname || '???', 7);
            if (jobNode.active = data.job < Enums_1.AllianceJobType.MEMBER) {
                nameLbl._forceUpdateRenderData();
                jobNode.x = nameLbl.node.x + nameLbl.node.width * 0.5 + 4;
                jobNode.setLocaleKey('(' + assetsMgr.lang('ui.alliance_job_' + data.job) + ')');
            }
            if (data.offlineTime) {
                it.Child('online').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.offlineTime + (now - data.getTime)));
            }
            else {
                it.Child('online').Color('#4AB32E').setLocaleKey('ui.online');
            }
        });
    };
    AlliChannelMemberPnlCtrl.prototype.reqUpdateAlliChatChannel = function (uid, memberUids, memberFilter) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_UpdateAlliChatChannel', { uid: uid, memberUids: memberUids, memberFilter: memberFilter }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    AlliChannelMemberPnlCtrl = __decorate([
        ccclass
    ], AlliChannelMemberPnlCtrl);
    return AlliChannelMemberPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AlliChannelMemberPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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