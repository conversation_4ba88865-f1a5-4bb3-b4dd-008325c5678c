
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/lobby/LobbyChatPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6281dznIbZK64miDQZT/t6I', 'LobbyChatPnlCtrl');
// app/script/view/lobby/LobbyChatPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var NetEvent_1 = require("../../common/event/NetEvent");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var BaseUserInfo_1 = require("../../model/common/BaseUserInfo");
var FriendInfo_1 = require("../../model/friend/FriendInfo");
var LabelAutoAnyCmpt_1 = require("../cmpt/LabelAutoAnyCmpt");
var ccclass = cc._decorator.ccclass;
var LobbyChatPnlCtrl = /** @class */ (function (_super) {
    __extends(LobbyChatPnlCtrl, _super);
    function LobbyChatPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.chatNode_ = null; // path://root/chat_n
        _this.inputEb_ = null; // path://root/chat_n/send/input_eb
        _this.sendNode_ = null; // path://root/chat_n/send/send_be_n
        _this.emojiNode_ = null; // path://root/chat_n/send/emoji_be_n
        _this.worldChannelNode_ = null; // path://root/chat_n/world_channel_be_n
        _this.worldChannelItemsNode_ = null; // path://root/chat_n/world_channel_be_n/mask/root/world_channel_items_nbe_n
        _this.loadingNode_ = null; // path://loading_n
        //@end
        _this.PKEY_TAB = 'LOBBY_CHAT_TAB';
        _this.user = null;
        _this.model = null;
        _this.team = null;
        _this.friend = null;
        _this.type = 0;
        _this.chatSv = null;
        _this.chatItem = null;
        _this.persChatItem = null;
        _this.tempChatNodeMap = {};
        _this.curFirstChatUid = '';
        _this.friendInfo = null;
        return _this;
    }
    LobbyChatPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f;
        return [
            (_a = {}, _a[NetEvent_1.default.NET_RECONNECT] = this.onNetReconnect, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.ADD_LOBBY_CHAT] = this.onAddLobbyChat, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_SEND_CHAT_BUTTON] = this.onUpdateSendChatButton, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.TRANSLATE_TEXT_COMPLETE] = this.onTranslateTextComplete, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.UPDATE_TEAM_LIST] = this.onUpdateTeamList, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.ADD_FRIEND_CHAT] = this.onAddFriendCaht, _f.enter = true, _f),
        ];
    };
    LobbyChatPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.model = this.getModel('lobby');
                this.team = this.getModel('team');
                this.friend = this.getModel('friend');
                this.chatItem = this.chatNode_.FindChild('item');
                this.chatItem.active = true;
                this.chatItem.parent = null;
                this.persChatItem = this.chatNode_.FindChild('pers_item');
                this.persChatItem.active = true;
                this.persChatItem.parent = null;
                return [2 /*return*/];
            });
        });
    };
    LobbyChatPnlCtrl.prototype.onEnter = function (data) {
        var _a;
        this.friendInfo = data === null || data === void 0 ? void 0 : data.target;
        var type = (data === null || data === void 0 ? void 0 : data.target) ? 2 : (_a = data === null || data === void 0 ? void 0 : data.tab) !== null && _a !== void 0 ? _a : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0);
        if (type === 1 && !this.team.hasTeam()) {
            type = 0;
        }
        this.tabsTc_.Tabs(type);
        this.worldChannelNode_.active = type === 0;
        ViewHelper_1.viewHelper.changePopupBoxList(this.worldChannelNode_, false);
    };
    LobbyChatPnlCtrl.prototype.onRemove = function () {
        this.tempChatNodeMap = {};
        this.chatNode_.Child('sv').children.forEach(function (m) { return m.Child('list', cc.ScrollView).content.children.forEach(function (n) { return n.children.forEach(function (it) { return it.active = false; }); }); });
        this.worldChannelNode_.Data = null;
        this.friend.setCurrLookChatFriendUID('');
    };
    LobbyChatPnlCtrl.prototype.onClean = function () {
        ut.destroyNode(this.chatItem);
        this.chatItem = null;
        ut.destroyNode(this.persChatItem);
        this.persChatItem = null;
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    LobbyChatPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = Number(event.node.name);
        this.type = type;
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.chatNode_.Child('sv').Swih(type)[0];
        this.chatSv = node.Child('list', cc.ScrollView);
        if (!this.tempChatNodeMap[type]) {
            this.setChatItemActive();
        }
        this.setLoading(false);
        var forbiden = true;
        var isWorldChannel = type === 0, isAlliChannel = type === 1, isPersChannel = type === 2;
        this.chatNode_.Child('send').active = !isPersChannel;
        if (this.worldChannelNode_.active = isWorldChannel) {
            this.updateWorldChannels(true);
        }
        else if (isAlliChannel) {
            forbiden = this.team.hasTeam();
            var onlineNode = node.Child('top/alli_online_be');
            var key = this.team.hasTeam() ? '' : this.team.isInGame() ? 'ui.not_have_team' : 'toast.please_join_team';
            if (onlineNode.active = key !== 'ui.not_have_team') {
                var online = this.team.hasTeam() ? this.team.getActTeammates().length : 0;
                onlineNode.Child('online_count/cur', cc.Label).string = online + '';
                onlineNode.Child('online_count/max', cc.Label).string = '/40';
            }
            this.updateChatList(!!data, key);
        }
        else if (isPersChannel) {
            node.Child('friends').active = true;
            this.chatSv.node.active = false;
            if (this.friendInfo) { // 直接进入好友对话
                this.enterFriendChat(this.friendInfo);
            }
            else {
                this.updatePersChats();
            }
        }
        this.updateSendButton(forbiden);
    };
    // path://root/chat_n/item/root/head_be
    LobbyChatPnlCtrl.prototype.onClickHead = function (event, _) {
        audioMgr.playSFX('click');
        var data = event.target.parent.parent.Data;
        if (data) {
            if (this.type === 2) { // 好友
                var friend = null;
                var isOwner = data.sender === GameHelper_1.gameHpr.getUid(), user = this.user;
                if (isOwner) {
                    friend = new FriendInfo_1.default().init({
                        uid: data.sender,
                        nickname: user.getNickname(),
                        headIcon: user.getHeadIcon(),
                        playSid: user.getPlaySid(),
                    });
                }
                else {
                    friend = GameHelper_1.gameHpr.friend.getFriends().find(function (m) { return m.uid === data.sender; });
                }
                friend && ViewHelper_1.viewHelper.showPnl('menu/FriendInfo', friend, 'chat');
            }
            else {
                ViewHelper_1.viewHelper.showPnl('lobby/UserInfo', data.user || new BaseUserInfo_1.default().init({
                    uid: data.sender,
                    nickname: data.senderNickname,
                    headIcon: data.senderHeadicon
                }), 'chat');
            }
        }
        else {
            ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.PLAYER_NOT_EXIST);
        }
    };
    // path://root/chat_n/item/root/content/content_be
    LobbyChatPnlCtrl.prototype.onClickContent = function (event, _) {
        var data = event.target.parent.Data;
        if (!data || !data.data) {
            return;
        }
        audioMgr.playSFX('click');
        if (data.type === 'portrayal') {
            ViewHelper_1.viewHelper.showPnl('common/PortrayalInfoBox', data.data, 'chat');
        }
    };
    // path://root/chat_n/item/root/content/content_be/translate_be
    LobbyChatPnlCtrl.prototype.onClickTranslate = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (!data) {
            return;
        }
        var isFriend = this.isFriendChat();
        var chatInfo = GameHelper_1.gameHpr.translateText(data.data, isFriend ? 'friend' : 'lobby');
        this.updateChatItem(data.node, chatInfo);
    };
    // path://root/chat_n/send/send_be_n
    LobbyChatPnlCtrl.prototype.onClickSend = function (event, _) {
        var isFriend = this.isFriendChat();
        var content = this.inputEb_.string.trim();
        if (!content) {
            return;
        }
        else if (ut.getStringLen(content) > 120) {
            return ViewHelper_1.viewHelper.showAlert('toast.send_chat_content_toolong');
        }
        else if (!isFriend && GameHelper_1.gameHpr.getTextNewlineCount(content) > 4) {
            return ViewHelper_1.viewHelper.showAlert('toast.send_chat_content_toolong');
        }
        if (isFriend) {
            var uid = this.friend.getCurrLookChatFriendUID();
            var ok = this.friend.sendChat(uid, content);
            if (ok === 1) {
                return this.updateSendButton();
            }
            else if (ok === 0) {
                this.inputEb_.string = '';
                this.updatePersChatList();
            }
        }
        else {
            var ok = this.model.sendChat(this.type, content);
            if (ok === 1) {
                return this.updateSendButton();
            }
            else if (ok !== 0) {
                return;
            }
            this.inputEb_.string = '';
            if (this.chatSv) {
                this.updateChatList();
            }
        }
    };
    // path://root/chat_n/send/emoji_be_n
    LobbyChatPnlCtrl.prototype.onClickEmoji = function (event, data) {
        var _this = this;
        audioMgr.playSFX('click');
        var isFriend = this.isFriendChat();
        ViewHelper_1.viewHelper.showPnl('common/ChatSelectEmoji', function (id) {
            if (!_this.isValid) {
                return;
            }
            var ok = isFriend ? _this.friend.sendChat(_this.friend.getCurrLookChatFriendUID(), '', { emoji: id }) : _this.model.sendChat(_this.type, '', { emoji: id });
            if (ok === 1) {
                return _this.updateSendButton();
            }
            else if (ok !== 0) {
            }
            else if (_this.chatSv) {
                _this.updateChatList();
            }
        });
    };
    // path://root/chat_n/send/trumpet_be
    LobbyChatPnlCtrl.prototype.onClickTrumpet = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/SendTrumpet');
    };
    // path://root/chat_n/world_channel_be_n
    LobbyChatPnlCtrl.prototype.onClickWorldChannel = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.changePopupBoxList(event.target, true);
    };
    // path://root/chat_n/world_channel_be_n/select_mask_be
    LobbyChatPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        ViewHelper_1.viewHelper.changePopupBoxList(event.target.parent, false);
    };
    // path://root/chat_n/world_channel_be_n/mask/root/world_channel_items_nbe_n
    LobbyChatPnlCtrl.prototype.onClickWorldChannelItems = function (event, _data) {
        var _a;
        var data = event.target.Data;
        ViewHelper_1.viewHelper.changePopupBoxList(this.worldChannelNode_, false);
        var oldChannel = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        if (data.uid !== oldChannel) {
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, data.uid);
            this.changeWorldChannel(data);
        }
    };
    // path://root/chat_n/sv/1/top/alli_online_be
    LobbyChatPnlCtrl.prototype.onClickAlliOnline = function (event, data) {
        audioMgr.playSFX('click');
        ViewHelper_1.viewHelper.showPnl('lobby/TeamList', !this.team.isInGame());
    };
    // path://root/chat_n/sv/2/friends/view/content/friend_chat_be
    LobbyChatPnlCtrl.prototype.onClickFriendChat = function (event, _data) {
        var data = event.target.Data;
        this.enterFriendChat(data);
    };
    // path://root/chat_n/sv/2/list/top/back/back_list_be
    LobbyChatPnlCtrl.prototype.onClickBackList = function (event, data) {
        this.backToFriendList();
    };
    // path://root/chat_n/sv/2/top/pers_add/pers_add_be
    LobbyChatPnlCtrl.prototype.onClickPersAdd = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('menu/Personal', 1);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    LobbyChatPnlCtrl.prototype.onNetReconnect = function () {
        this.updateChatList();
    };
    LobbyChatPnlCtrl.prototype.onAddLobbyChat = function (data) {
        var _a;
        if (!this.isValid) {
            return;
        }
        else if (data.channel !== ((_a = this.model) === null || _a === void 0 ? void 0 : _a.getCurLookChannel())) {
            return;
        }
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateChatItem(it, data);
        }
        else {
            this.updateChatList();
        }
    };
    LobbyChatPnlCtrl.prototype.onUpdateSendChatButton = function () {
        this.updateSendButton();
    };
    // 翻译完成
    LobbyChatPnlCtrl.prototype.onTranslateTextComplete = function (type, data) {
        if (type !== 'lobby') {
            return;
        }
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === data.uid; });
        if (it) {
            this.updateChatItem(it, data);
        }
    };
    LobbyChatPnlCtrl.prototype.onUpdateTeamList = function () {
        if (this.type === 1 && !this.team.hasTeam()) {
            this.tabsTc_.Tabs(0);
        }
    };
    // 添加好友聊天信息
    LobbyChatPnlCtrl.prototype.onAddFriendCaht = function (data) {
        if (!this.isFriendChat() || data.uid !== this.friend.getCurrLookChatFriendUID()) {
            return;
        }
        var chat = data.chat;
        var it = this.chatSv.content.children.find(function (m) { var _a; return ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === chat.uid; });
        if (it) {
            this.updatePersItem(it, chat);
        }
        else {
            this.updatePersChatList();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新聊天列表
    LobbyChatPnlCtrl.prototype.updateChatList = function (init, emptyTip) {
        var _this = this;
        if (emptyTip === void 0) { emptyTip = ''; }
        var sv = this.chatSv;
        var channel = this.model.getChannelByType(this.type);
        this.model.setCurLookChannel(channel);
        if (init) {
            this.showReqChatLoading(true);
        }
        this.model.getChatsByChannel(channel).then(function (list) {
            var _a, _b;
            if (!_this.isValid) {
                return;
            }
            else if (init) {
                _this.showReqChatLoading(false);
            }
            var count = list.length - 1;
            GameHelper_1.gameHpr.updateChatAllTime(list);
            var firstUid = ((_a = list[0]) === null || _a === void 0 ? void 0 : _a.uid) || '', tempMap = {};
            if (!_this.curFirstChatUid) {
                _this.curFirstChatUid = firstUid;
            }
            if (_this.curFirstChatUid === firstUid) {
                (_b = _this.tempChatNodeMap[_this.type]) === null || _b === void 0 ? void 0 : _b.forEach(function (m) {
                    var _a;
                    if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                        tempMap[m.Data.uid] = true;
                    }
                });
            }
            else {
                _this.tempChatNodeMap[_this.type] = null;
            }
            sv.stopAutoScroll();
            var height = sv.content.height, y = sv.content.y;
            sv.Items(list, _this.chatItem, function (it, data, i) {
                if (!tempMap[data.uid]) {
                    _this.updateChatItem(it, data);
                }
                if (i === count) {
                    sv.Component(cc.ScrollViewPlus).isFrameRender = false;
                    _this.updateScrollViewContent(sv, height, y);
                }
            });
            var emptyNode_ = sv.node.Child('empty');
            if (emptyNode_) {
                emptyNode_.active = !!emptyTip || !list.length;
                emptyNode_.Child('val').setLocaleKey(emptyTip);
            }
        });
    };
    LobbyChatPnlCtrl.prototype.updateItem = function (it, data) {
        var _a, _b, _c, _d;
        it.Data = data;
        var timeLbl = it.Child('time', cc.Label);
        if (timeLbl.setActive(!!data.showTime)) {
            timeLbl.setLocaleKey('ui.text', data.showTime);
        }
        var height = data.showTime ? 40 : 0;
        var root = it.Child('root'), system = it.Child('system');
        if (system.active = data.sender === '-1' && !!((_a = data.params) === null || _a === void 0 ? void 0 : _a.length)) {
            root.active = false;
            // system.setLocaleKey(data.params[0], ...data.params.slice(1))
            system.Component(cc.RichText).string = assetsMgr.lang.apply(assetsMgr, __spread([data.params[0]], data.params.slice(1)));
            system.y = data.showTime ? -56 : -16;
            it.height = height + 32 + 12;
            return;
        }
        var isMe = GameHelper_1.gameHpr.getUid() === data.sender;
        root.active = true;
        root.y = data.showTime ? -40 : 0;
        root.Child('head_be').x = isMe ? 278 : -278;
        var name = root.Child('name'), content = root.Child('content'), emoji = root.Child('emoji'), loading = root.Child('loading');
        name.anchorX = content.anchorX = isMe ? 1 : 0;
        name.x = content.x = isMe ? 240 : -240;
        loading.x = emoji.x = isMe ? 190 : -190;
        var user = data.user || new BaseUserInfo_1.default().init({ uid: data.sender, nickname: data.senderNickname, headIcon: data.senderHeadicon });
        ResHelper_1.resHelper.loadPlayerHead(root.Child('head_be/val', cc.Sprite), user === null || user === void 0 ? void 0 : user.headIcon, this.key);
        var nameLbl = root.Child('name', cc.Label);
        nameLbl.string = ut.nameFormator((user === null || user === void 0 ? void 0 : user.nickname) || '???', 12);
        nameLbl._forceUpdateRenderData();
        this.updateTtile(it, 0, name.x, nameLbl.node.width * 0.5, name.anchorX);
        var hasEmoji = !!data.emoji && !data.bannedSurplusTime;
        content.active = !hasEmoji;
        content.Data = null;
        if (hasEmoji) { //是表情
            loading.active = !!data.wait;
            if (emoji.active = !data.wait) {
                var scale = ((_b = assetsMgr.getJsonData('chatEmoji', data.emoji)) === null || _b === void 0 ? void 0 : _b.scale) || 0;
                if (scale && !isMe) {
                    scale *= -1;
                }
                ResHelper_1.resHelper.loadEmojiNode(data.emoji, emoji, scale, this.key, true);
            }
            height += (36 + 100);
        }
        else {
            emoji.removeAllChildren();
            loading.active = emoji.active = false;
            var contentLbl = content.Child('content_be', cc.Label), translateLbl = content.Child('translate', cc.Label);
            var translateButton = content.Child('content_be/translate_be');
            translateLbl.node.anchorX = contentLbl.node.anchorX = content.anchorX;
            translateLbl.node.x = contentLbl.node.x = 0;
            var isBanned = data.bannedSurplusTime > 0;
            if (isBanned) {
                translateButton.active = false;
                contentLbl.Color('#B6A591').string = assetsMgr.lang('ui.banned_chat_desc', GameHelper_1.gameHpr.millisecondToStringForDay(data.bannedSurplusTime));
            }
            else if (data.portrayal) { //是否画像
                translateButton.active = false;
                content.Data = { type: 'portrayal', data: data.portrayal };
                contentLbl.Color(data.wait ? '#7F7F7F' : data.portrayal.getChatNameColor()).string = '[' + data.portrayal.getChatName() + ']';
            }
            else {
                translateButton.active = !isMe && !data.translate && !!data.content && GameHelper_1.gameHpr.isGLobal();
                content.Data = { node: it, data: data };
                contentLbl.Color(data.wait ? '#7F7F7F' : '#333333').string = data.content;
            }
            var lineNode = content.Child('line'), translateLoading = content.Child('loading');
            lineNode.active = !!data.translate;
            translateLoading.active = !!((_c = data.translate) === null || _c === void 0 ? void 0 : _c.req);
            contentLbl.Component(cc.Button).interactable = !isBanned && !!data.portrayal;
            contentLbl.Component(LabelAutoAnyCmpt_1.default).check();
            if (translateButton.active) {
                translateButton.x = contentLbl.node.width + 50;
            }
            if (translateLbl.setActive(!!((_d = data.translate) === null || _d === void 0 ? void 0 : _d.text))) {
                translateLbl.string = data.translate.text;
                translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
                lineNode.Child('val').width = translateLbl.node.width * 0.5;
            }
            else {
                lineNode.Child('val').width = contentLbl.node.width * 0.5;
            }
            content.Component(cc.Layout).updateLayout();
            height += Math.max(60, Math.ceil(content.height + 32));
        }
        it.height = height;
    };
    // 显示称号
    LobbyChatPnlCtrl.prototype.updateTtile = function (it, id, x, width, anchorX) {
        var json = assetsMgr.getJsonData('title', id);
        var titleNode = it.Child('root/title');
        if (titleNode.active = !!json) {
            titleNode.Color(Constant_1.ACHIEVEMENT_COLOR[(json === null || json === void 0 ? void 0 : json.quality) || 1]).setLocaleKey('ui.bracket', 'titleText.' + json.id);
            titleNode.anchorX = anchorX;
            titleNode.x = x + (width + 8) * (anchorX ? -1 : 1);
        }
    };
    LobbyChatPnlCtrl.prototype.updateScrollViewContent = function (sv, preHeight, preY) {
        if (sv.isScrolling()) {
            return;
        }
        var height = sv.node.height;
        sv.content.Component(cc.Layout).updateLayout();
        if (sv.content.height <= height) {
            sv.scrollToTop();
        }
        else if (preY >= preHeight - height - 50) { //在最下面的位置
            sv.scrollToBottom();
        }
    };
    // 刷新发送按钮
    LobbyChatPnlCtrl.prototype.updateSendButton = function (forbiden) {
        var _this = this;
        var time = this.isFriendChat() ? this.friend.checkRestSurplusTime() : this.model.checkRestSurplusTime(this.type === 1);
        var b = this.sendNode_.Component(cc.Button).interactable = forbiden !== null && forbiden !== void 0 ? forbiden : !time;
        this.sendNode_.Component(cc.MultiFrame).setFrame(b);
        this.emojiNode_.Component(cc.Button).interactable = b;
        this.emojiNode_.Child('val').Color(b ? '#756963' : '#B6A591');
        if (time > 0) {
            this.sendNode_.Swih('time')[0].Component(cc.LabelTimer).run(time * 0.001, function () { return _this.isValid && _this.updateSendButton(); });
        }
        else {
            this.sendNode_.Swih('val');
        }
    };
    LobbyChatPnlCtrl.prototype.showReqChatLoading = function (val) {
        this.chatNode_.Child('loading').active = val;
        this.chatNode_.Child('send').active = !val;
    };
    LobbyChatPnlCtrl.prototype.setChatItemActive = function () {
        var _a, _b;
        var chatSv = this.chatSv;
        if (!chatSv) {
            return;
        }
        var tempList = this.tempChatNodeMap[this.type] = [];
        var svp = chatSv.getComponent(cc.ScrollViewPlus);
        if (svp.isFrameRender) {
            return;
        }
        var children = chatSv.content.children;
        this.curFirstChatUid = ((_b = (_a = children[0]) === null || _a === void 0 ? void 0 : _a.Data) === null || _b === void 0 ? void 0 : _b.uid) || '';
        // 优先显示视野内的
        var surfaceLine = -chatSv.content.y + chatSv.content.parent.height * 0.5;
        var minDis = chatSv.content.height;
        var idx = -1;
        for (var i = 0, l = children.length; i < l; i++) {
            var child = children[i];
            var dis = Math.abs(child.y - surfaceLine);
            if (dis < minDis) {
                minDis = dis;
                idx = i;
            }
        }
        if (idx !== -1) {
            for (var l = idx, r = idx + 1; l >= 0 || r < children.length;) {
                l >= 0 && tempList.push(children[l--]);
                r < children.length && tempList.push(children[r++]);
            }
            tempList.reverse();
        }
    };
    // 刷新世界频道
    LobbyChatPnlCtrl.prototype.updateWorldChannels = function (init) {
        var _a;
        this.worldChannelNode_.Data = true;
        var sames = ['cn', 'hk', 'tw'], channels = [{ uid: '0', name: 'ui.lobby_chat_channel' }];
        if (sames.has(mc.lang)) {
            channels.push({ uid: 'zh', name: '简繁中文' });
        }
        else {
            var data_1 = Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === mc.lang; });
            channels.push({ uid: mc.lang, name: data_1.text });
        }
        var len = channels.length - 1;
        var curChannelUid = (_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
        this.worldChannelItemsNode_.Items(channels, function (it, data, i) {
            it.Data = data;
            var val = it.Child('val', cc.Label);
            if (i) {
                val.string = data.name;
            }
            else {
                val.setLocaleKey(data.name);
            }
            val.Color(curChannelUid === data.uid ? '#B6A591' : '#756963');
            it.Child('line').active = i < len;
        });
        // 刷新频道信息
        var data = channels.find(function (m) { return (m === null || m === void 0 ? void 0 : m.uid) === curChannelUid; });
        if (!data) {
            data = channels[0];
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL, data.uid);
            this.changeWorldChannel(data, init);
        }
        else if (init) {
            this.changeWorldChannel(data, init);
        }
    };
    // 切换世界频道
    LobbyChatPnlCtrl.prototype.changeWorldChannel = function (data, init) {
        if (init === void 0) { init = false; }
        var isWorld = data.uid === '0', worldVal = this.worldChannelNode_.Child('world'), xVal = this.worldChannelNode_.Child('x', cc.Label);
        if (isWorld) {
            xVal.node.active = false;
            worldVal.active = true;
            worldVal.setLocaleKey(data.name);
        }
        else {
            xVal.node.active = true;
            worldVal.active = false;
            xVal.string = data.name;
        }
        this.worldChannelItemsNode_.children.forEach(function (m) {
            var _a;
            if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                var select = m.Data.uid === data.uid;
                m.Child('val').Color(select ? '#B6A591' : '#756963');
            }
        });
        this.updateChatList(init);
    };
    // 好友---------------------------------------------------------------------------------------------
    LobbyChatPnlCtrl.prototype.updateChatItem = function (it, data) {
        if (this.isFriendChat()) {
            this.updatePersItem(it, data);
        }
        else {
            this.updateItem(it, data);
        }
    };
    LobbyChatPnlCtrl.prototype.isFriendChat = function () { return this.type === 2; };
    // 刷新私聊列表
    LobbyChatPnlCtrl.prototype.updatePersChats = function (node) {
        var _this = this;
        node = node || this.chatNode_.Child('sv/2');
        var sv = node.Child('friends', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var len = 0;
        var list = GameHelper_1.gameHpr.friend.getFriends();
        len = list.length;
        list.sort(function (a, b) {
            if (a.notReadCount !== b.notReadCount) {
                return b.notReadCount - a.notReadCount;
            }
            var aGiftCount = a.giftList.length, bGiftCount = b.giftList.length;
            if (aGiftCount !== bGiftCount) {
                return bGiftCount - aGiftCount;
            }
            return a.offlineTime - b.offlineTime;
        });
        sv.List(len, function (it, i) {
            var data = it.Data = list[i], layout = it.Child('layout');
            it.Child('dot').active = data.notReadCount > 0;
            var content = '';
            it.Child('content', cc.Label).string = data.lastChatInfo ? (data.lastChatInfo.content || (data.lastChatInfo.emoji ? "[" + assetsMgr.lang('ui.title_emoji') + "]" : '')) : '';
            it.Child('time', cc.Label).string = data.lastChatInfo ? ut.dateFormat('h:mm', data.lastChatInfo.time) : '';
            layout.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
            var serverNode = layout.Child('online/server');
            serverNode.active = false;
            if (data.offlineTime) {
                layout.Child('online/val').Color('#B6A591').setLocaleKey('ui.offline_time', GameHelper_1.gameHpr.millisecondToStringForDay(data.getOfflineTime()));
            }
            else if (data.playSid) {
                layout.Child('online/val').Color('#59A733').setLocaleKey('ui.in_game');
                serverNode.active = true;
                var _a = GameHelper_1.gameHpr.getServerNameById(data.playSid), key = _a.key, id = _a.id;
                serverNode.setLocaleKey('ui.bracket', assetsMgr.lang(key, id));
            }
            else {
                layout.Child('online/val').Color('#59A733').setLocaleKey('ui.in_ready_war');
            }
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
        });
        sv.node.Child('empty').active = !len;
    };
    LobbyChatPnlCtrl.prototype.backToFriendList = function () {
        this.chatNode_.Child('sv/2/friends').active = true;
        this.chatSv.node.active = false;
        this.friend.setCurrLookChatFriendUID('');
        this.updatePersChats();
    };
    LobbyChatPnlCtrl.prototype.enterFriendChat = function (data) {
        this.chatNode_.Child('sv/2/friends').active = false;
        this.chatNode_.Child('send').active = true;
        this.chatSv.node.active = true;
        var playerNode_ = this.chatSv.node.Child('top/player');
        playerNode_.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 7);
        ResHelper_1.resHelper.loadPlayerHead(playerNode_.Child('head/val', cc.Sprite), data.headIcon, this.key);
        this.friend.setCurrLookChatFriendUID(data.uid);
        this.loadPersChatList(data);
    };
    LobbyChatPnlCtrl.prototype.loadPersChatList = function (data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.chatSv.Items(0);
                        this.sendNode_.Swih('val');
                        this.setLoading(true);
                        return [4 /*yield*/, this.friend.checkInitFriendChats(data.uid)];
                    case 1:
                        _a.sent();
                        this.setLoading(false);
                        this.friend.tagChatRead(data);
                        this.updatePersChatList(true);
                        this.updateSendButton();
                        return [2 /*return*/];
                }
            });
        });
    };
    LobbyChatPnlCtrl.prototype.setLoading = function (val) {
        this.loadingNode_.active = val;
        this.sendNode_.Component(cc.Button).interactable = !val;
        this.sendNode_.Component(cc.MultiFrame).setFrame(!val);
    };
    // 刷新聊天列表
    LobbyChatPnlCtrl.prototype.updatePersChatList = function (init) {
        var _this = this;
        var _a, _b;
        var uid = this.friend.getCurrLookChatFriendUID();
        var list = this.friend.getChatsByFriend(uid), count = list.length - 1;
        GameHelper_1.gameHpr.updateChatAllTime(list);
        var firstUid = ((_a = list[0]) === null || _a === void 0 ? void 0 : _a.uid) || '', tempMap = {};
        if (!this.curFirstChatUid) {
            this.curFirstChatUid = firstUid;
        }
        if (this.curFirstChatUid === firstUid) {
            (_b = this.tempChatNodeMap[this.type]) === null || _b === void 0 ? void 0 : _b.forEach(function (m) {
                var _a;
                if ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) {
                    tempMap[m.Data.uid] = true;
                }
            });
        }
        else {
            this.tempChatNodeMap[this.type] = null;
        }
        var height = this.chatSv.content.height, y = this.chatSv.content.y;
        this.chatSv.stopAutoScroll();
        this.chatSv.Items(list, this.persChatItem, function (it, data, i) {
            if (!tempMap[data.uid]) {
                _this.updatePersItem(it, data);
            }
            if (i === count) {
                _this.chatSv.Component(cc.ScrollViewPlus).isFrameRender = false;
                _this.updatePersScrollViewContent(_this.chatSv, height, y, init);
            }
        });
    };
    LobbyChatPnlCtrl.prototype.updatePersItem = function (it, data) {
        var _a, _b, _c;
        it.Data = data;
        var timeLbl = it.Child('time', cc.Label);
        if (timeLbl.setActive(!!data.showTime)) {
            timeLbl.setLocaleKey('ui.text', data.showTime);
        }
        var height = data.showTime ? 40 : 0;
        var isMe = GameHelper_1.gameHpr.getUid() === data.sender;
        var root = it.Child('root');
        root.active = true;
        root.y = data.showTime ? -40 : 0;
        root.Child('head_be').x = isMe ? 278 : -278;
        // root.Child('head_be', cc.Button).interactable = !isMe
        var name = root.Child('name'), content = root.Child('content'), emoji = root.Child('emoji'), loading = root.Child('loading');
        name.anchorX = content.anchorX = isMe ? 1 : 0;
        name.x = content.x = isMe ? 240 : -240;
        loading.x = emoji.x = isMe ? 190 : -190;
        var player = isMe ? { headIcon: GameHelper_1.gameHpr.user.getHeadIcon(), nickname: GameHelper_1.gameHpr.user.getNickname(), noteName: '' } : this.friend.getFriendByUID(data.sender);
        ResHelper_1.resHelper.loadPlayerHead(root.Child('head_be/val', cc.Sprite), player === null || player === void 0 ? void 0 : player.headIcon, this.key);
        var nameLbl = root.Child('name', cc.Label);
        nameLbl.string = ut.nameFormator((player === null || player === void 0 ? void 0 : player.nickname) || '???', 12);
        nameLbl._forceUpdateRenderData();
        this.updateNoteName(it, player === null || player === void 0 ? void 0 : player.noteName, name.x, nameLbl.node.width * 0.5, name.anchorX);
        var hasEmoji = !!data.emoji;
        content.active = !hasEmoji;
        content.Data = null;
        if (hasEmoji) { //是表情
            loading.active = !!data.wait;
            if (emoji.active = !data.wait) {
                var scale = ((_a = assetsMgr.getJsonData('chatEmoji', data.emoji)) === null || _a === void 0 ? void 0 : _a.scale) || 0;
                if (scale && !isMe) {
                    scale *= -1;
                }
                ResHelper_1.resHelper.loadEmojiNode(data.emoji, emoji, scale, this.key, true);
            }
            height += (36 + 100);
        }
        else {
            // emoji.removeAllChildren()
            // loading.active = emoji.active = false
            // const contentLbl = content.Component(cc.Label)
            emoji.removeAllChildren();
            loading.active = emoji.active = false;
            var contentLbl = content.Child('content_be', cc.Label), translateLbl = content.Child('translate', cc.Label);
            var translateButton = content.Child('content_be/translate_be');
            translateLbl.node.anchorX = contentLbl.node.anchorX = content.anchorX;
            translateLbl.node.x = contentLbl.node.x = 0;
            translateButton.active = !isMe && !data.translate && !!data.content;
            content.Data = { node: it, data: data };
            contentLbl.Color(data.wait ? '#7F7F7F' : '#333333').string = data.content;
            var lineNode = content.Child('line'), translateLoading = content.Child('loading');
            lineNode.active = !!data.translate;
            translateLoading.active = !!((_b = data.translate) === null || _b === void 0 ? void 0 : _b.req);
            contentLbl.Component(cc.Button).interactable = false;
            contentLbl.Component(LabelAutoAnyCmpt_1.default).check();
            if (translateButton.active) {
                translateButton.x = contentLbl.node.width + 50;
            }
            if (translateLbl.setActive(!!((_c = data.translate) === null || _c === void 0 ? void 0 : _c.text))) {
                translateLbl.string = data.translate.text;
                translateLbl.Component(LabelAutoAnyCmpt_1.default).check();
                lineNode.Child('val').width = translateLbl.node.width * 0.5;
            }
            else {
                lineNode.Child('val').width = contentLbl.node.width * 0.5;
            }
            content.Component(cc.Layout).updateLayout();
            height += Math.max(60, Math.ceil(content.height + 32));
        }
        it.height = height;
    };
    // 显示备注
    LobbyChatPnlCtrl.prototype.updateNoteName = function (it, noteName, x, width, anchorX) {
        var titleNode = it.Child('root/title');
        if (titleNode.active = !!noteName) {
            titleNode.Component(cc.Label).string = '(' + noteName + ')';
            titleNode.anchorX = anchorX;
            titleNode.x = x + (width + 8) * (anchorX ? -1 : 1);
        }
    };
    LobbyChatPnlCtrl.prototype.updatePersScrollViewContent = function (sv, preHeight, preY, init) {
        if (sv.isScrolling()) {
            return;
        }
        var content = sv.content, height = sv.node.height;
        content.Component(cc.Layout).updateLayout();
        if (content.height <= height) {
            sv.scrollToTop();
        }
        else if ( /* init ||  */preY >= preHeight - height - 50) { //在最下面的位置
            sv.scrollToBottom();
        }
    };
    LobbyChatPnlCtrl.prototype.update = function (dt) {
        var tempList = this.tempChatNodeMap[this.type];
        if ((tempList === null || tempList === void 0 ? void 0 : tempList.length) > 0) {
            var node = tempList.pop();
            if (node === null || node === void 0 ? void 0 : node.Data) {
                this.updateChatItem(node, node.Data);
            }
        }
    };
    LobbyChatPnlCtrl = __decorate([
        ccclass
    ], LobbyChatPnlCtrl);
    return LobbyChatPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = LobbyChatPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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