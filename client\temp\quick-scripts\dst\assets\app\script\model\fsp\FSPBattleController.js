
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/FSPBattleController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dbd57kBBPJPh4OrlkOr/Sc9', 'FSPBattleController');
// app/script/model/fsp/FSPBattleController.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
Object.defineProperty(exports, "__esModule", { value: true });
var SearchPoint_1 = require("../../common/astar/SearchPoint");
var SearchRange_1 = require("../../common/astar/SearchRange");
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var RandomObj_1 = require("../common/RandomObj");
var Fighter_1 = require("./Fighter");
var MainDoor_1 = require("./MainDoor");
var Tower_1 = require("./Tower");
var BuffObj_1 = require("../area/BuffObj");
// 战斗逻辑 演绎战斗逻辑
var FSPBattleController = /** @class */ (function () {
    function FSPBattleController() {
        this.area = null;
        this.random = null;
        this.searchPoint = null;
        this.environmentBuffs = []; //环境效果
        this.accAttackIndex = 0; //当前的累计攻击下标
        this.camp = 0; //当前场景所属阵营
        this.camps = {}; //当前阵营列表
        this.mainDoors = []; //城门列表
        this.fighters = []; //战斗者列表
        this.currentFighter = null; //当前回合出战者
        this.lockedMovePointMap = new Map(); //士兵移动锁定点位Map
        this.campStrategyMap = {}; //阵营韬略列表
        this.fighterPointMap = {}; //当前所有士兵的位置分布
    }
    FSPBattleController.prototype.init = function (area, data) {
        var _this = this;
        var _a, _b, _c, _d, _e, _f;
        // cc.log('FSPBattleController init', data)
        this.area = area;
        this.environmentBuffs = ((_a = data.environmentBuffs) === null || _a === void 0 ? void 0 : _a.map(function (m) { return new BuffObj_1.default().fromSvr(m); })) || [];
        this.camp = data.camp;
        this.searchPoint = new SearchPoint_1.default().init(this.checkHasPass.bind(this));
        // 初始化士兵
        var tempAttackTargetMap = {};
        var _g = this.getAreaBuildPawnInfo(area), buildPawnId = _g.buildPawnId, buildPawnLv = _g.buildPawnLv;
        this.fighters = [];
        this.campStrategyMap = {};
        area.battleTempPawns = [];
        (_b = data.fighters) === null || _b === void 0 ? void 0 : _b.forEach(function (m) {
            if (m.towerLv) {
                tempAttackTargetMap[m.uid] = m.attackTarget;
                _this.fighters.push(new Tower_1.default().initTower(m, m.towerId || buildPawnId, m.towerLv, _this.camp, area, _this));
            }
            else if (m.isFalg) {
                tempAttackTargetMap[m.uid] = m.attackTarget;
                _this.fighters.push(new Fighter_1.default().init(area.addBuildFlag(m), m, _this));
            }
            else if (m.isPet) {
                tempAttackTargetMap[m.uid] = m.attackTarget;
                var pawn = area.addPetPawn(m);
                pawn.curHp = m.hp[0] || 0;
                pawn.addBuffs(m.buffs);
                _this.fighters.push(new Fighter_1.default().init(pawn, m, _this));
            }
            else if (m.isNoncombat) {
                var pawn = area.addNoncombat(m);
                pawn.curHp = m.hp[0] || 0;
                pawn.maxHp = m.hp[1] || 0;
                _this.fighters.push(new Fighter_1.default().init(pawn, m, _this));
            }
            else {
                var pawn = area.getPawn(m.uid);
                if (pawn) {
                    tempAttackTargetMap[pawn.uid] = m.attackTarget;
                    pawn.addBuffs(m.buffs);
                    var fighter = new Fighter_1.default().init(pawn, m, _this);
                    _this.addCampStrategys(fighter);
                    _this.fighters.push(fighter);
                }
            }
        });
        // 初始化城门
        // const boss = area.getBoss()
        // this.mainDoors = area.mainPoints.map(m => boss ? new BossDoor().initBoss(boss, this.camp, m, this) : new MainDoor().init(area, this.camp, m))
        if (!area.isBoss()) {
            this.mainDoors = area.mainPoints.map(function (m) { return new MainDoor_1.default().init(area, _this.camp, m); });
        }
        // 根据出手顺序排序
        this.fighters.sort(function (a, b) { return a.attackIndex - b.attackIndex; });
        // 初始化随机种子
        this.random = new RandomObj_1.default(data.randSeed);
        this.accAttackIndex = data.accAttackIndex || (((_c = this.fighters.last()) === null || _c === void 0 ? void 0 : _c.attackIndex) || 1);
        // 初始化每个士兵的攻击目标
        this.fighters.forEach(function (m) {
            m.setAttackTarget(_this.getFighter(tempAttackTargetMap[m.getUid()]));
            m.checkBattleBeginTrigger();
            m.updateStrategyEffect();
        });
        this.updateFighterPointMap();
        // 获取当前出战者
        if (data.currentFighter) {
            this.currentFighter = this.getFighter(data.currentFighter.uid);
            if (this.currentFighter) {
                if (data.currentFighter.blackboardMap) { //新手村
                    this.currentFighter.blackboard = data.currentFighter.blackboardMap;
                }
                else {
                    var blackboard = data.currentFighter.blackboard || {};
                    for (var id in blackboard) {
                        var info = this.currentFighter.blackboard[id] = {};
                        var _h = blackboard[id], boolMap = _h.boolMap, intMap = _h.intMap, vecArray = _h.vecArray, stringMap = _h.stringMap, vec2Map = _h.vec2Map;
                        Object.assign(info, boolMap);
                        Object.assign(info, intMap);
                        Object.assign(info, stringMap);
                        Object.assign(info, vec2Map);
                        for (var k in vecArray) {
                            info[k] = (_d = vecArray[k]) === null || _d === void 0 ? void 0 : _d.list;
                        }
                    }
                }
            }
        }
        if (!this.currentFighter) {
            this.currentFighter = this.fighters[0];
        }
        cc.log('battleStart randSeed: ' + data.randSeed + ', index: ' + area.index);
        cc.log((data.currentFrameIndex || 0) + ' >>>>>>>>> ' + this.currentFighter.getId() + '(' + this.currentFighter.getPoint().Join() + ') [' + this.currentFighter.getUid() + '] ' + this.currentFighter.attackIndex + ' ' + this.currentFighter.camp);
        // console.log('init lockedMovePointMap');
        // 初始化士兵移动目标Map
        this.lockedMovePointMap.clear();
        if (data.lockedMovePointMap) {
            for (var k in data.lockedMovePointMap) {
                var pointLockData = data.lockedMovePointMap[k];
                var findIFighter = this.getFighter(pointLockData.fighterUid);
                if (findIFighter) {
                    var movePoint = cc.v2(((_e = pointLockData.movePoint) === null || _e === void 0 ? void 0 : _e.x) || 0, ((_f = pointLockData.movePoint) === null || _f === void 0 ? void 0 : _f.y) || 0);
                    this.lockedMovePointMap.set(k, { fighter: findIFighter, weight: (pointLockData === null || pointLockData === void 0 ? void 0 : pointLockData.weight) || 0, movePoint: movePoint });
                    // console.log('add lockedMovePoint k:' + k + ' uid: ' + findFighter.getUid());
                }
            }
        }
        return this;
    };
    FSPBattleController.prototype.strip = function () {
        return {
            camp: this.camp,
            currentFighter: this.toCurrentFighterData(),
            randSeed: this.random.seed,
            fighters: this.fighters.map(function (m) { return m.strip(); }),
            lockedMovePointMap: this.getLockedMovePointMapStrip()
        };
    };
    FSPBattleController.prototype.toCurrentFighterData = function () {
        if (!this.currentFighter) {
            return null;
        }
        var blackboard = ut.deepClone(this.currentFighter.blackboard);
        return {
            uid: this.currentFighter.getUid(),
            blackboardMap: blackboard,
        };
    };
    FSPBattleController.prototype.getCurrentFrameIndex = function () {
        var _a, _b;
        return (_b = (_a = this.area.getFspModel()) === null || _a === void 0 ? void 0 : _a.getCurrentFrameIndex()) !== null && _b !== void 0 ? _b : -1;
    };
    FSPBattleController.prototype.getLockedMovePointMapStrip = function () {
        var map = {};
        this.lockedMovePointMap.forEach(function (v, k) {
            map[k] = { fighterUid: v.fighter.getUid(), movePoint: v.movePoint, weight: v.weight };
        });
        return map;
    };
    // 获取区域建筑士兵信息
    FSPBattleController.prototype.getAreaBuildPawnInfo = function (area) {
        var _a, _b;
        var cityId = area.cityId, id = 0, lv = 0;
        if (area.isAncient()) { //遗迹
            return { buildPawnId: 8001, buildPawnLv: (_b = (_a = GameHelper_1.gameHpr.world.getAncientInfo(Math.abs(area.index))) === null || _a === void 0 ? void 0 : _a.lv) !== null && _b !== void 0 ? _b : 1 };
        }
        else if (cityId === Constant_1.CITY_MAIN_NID) { //城墙
            id = 7003;
        }
        else if (cityId === Constant_1.CITY_FORT_NID) { //要塞
            id = 7002;
        }
        else if (area.owner) { //除了要塞其他都是箭塔
            id = 7001;
        }
        return id > 0 ? { buildPawnId: id, buildPawnLv: GameHelper_1.gameHpr.getPlayerTowerLvByPawn(area.owner, id) } : { buildPawnId: 0 };
    };
    FSPBattleController.prototype.stop = function () {
        this.currentFighter = null;
        this.area = null;
        this.random = null;
        this.fighters = null;
        this.mainDoors = null;
    };
    FSPBattleController.prototype.isWin = function () {
        var uid = GameHelper_1.gameHpr.getUid();
        if (this.area.owner === uid || !this.isMeAttacker(uid)) {
            return false;
        }
        return this.fighters.some(function (m) { var _a; return ((_a = m.entity) === null || _a === void 0 ? void 0 : _a.owner) === uid && !m.isDie() && !m.isBuild(); });
    };
    FSPBattleController.prototype.getCamps = function () { return this.camps; };
    FSPBattleController.prototype.isMeAttacker = function (uid) {
        for (var key in this.camps) {
            if (this.camps[key] === uid) {
                return true;
            }
        }
        return false;
    };
    // 添加士兵
    FSPBattleController.prototype.addFighters = function (fighters) {
        var _this = this;
        var uids = {};
        // 获取当前出手士兵下标
        var curFighterIndex = this.getCurFighterIndex();
        this.accAttackIndex = fighters[fighters.length - 1].attackIndex;
        // this.fighters.forEach(m => uids[m.getUid()] = true)
        for (var i = 0; i < this.fighters.length; i++) {
            var f = this.fighters[i];
            uids[f.getUid()] = true;
            if (i >= curFighterIndex) {
                // 设置添加士兵后的出手顺序
                this.accAttackIndex += 1;
                f.attackIndex = this.accAttackIndex;
            }
        }
        var addFighters = [], checkUpdateStrategyCampMap = {};
        fighters.forEach(function (m) {
            if (uids[m.uid]) {
                return cc.log('出现重复的士兵', m);
            }
            uids[m.uid] = true;
            var pawn = (m.uid.startsWith('pet_') || m.uid.startsWith('build_')) ? _this.area.getBattleTempPawn(m.uid) : _this.area.getPawn(m.uid);
            if (pawn) {
                pawn.addBuffs(m.buffs);
                var fighter = new Fighter_1.default().init(pawn, m, _this);
                addFighters.push(fighter);
                if (_this.addCampStrategys(fighter)) {
                    checkUpdateStrategyCampMap[m.camp] = true;
                }
            }
        });
        // 刷新韬略
        if (!ut.isEmptyObject(checkUpdateStrategyCampMap)) {
            this.fighters.forEach(function (m) {
                if (checkUpdateStrategyCampMap[m.camp]) {
                    m.updateStrategyEffect();
                }
            });
        }
        // 根据出手顺序排序
        this.fighters.pushArr(addFighters);
        this.fighters.sort(function (a, b) { return a.attackIndex - b.attackIndex; });
        addFighters.forEach(function (m) {
            m.checkBattleBeginTrigger();
            m.updateStrategyEffect();
        });
        this.updateFighterPointMap();
        // cc.log(this.fighters.map(m => m.getPoint().ID() + '_' + m.getAttackIndex()))
        return addFighters;
    };
    // 删除士兵
    FSPBattleController.prototype.removeFightersByArmyUid = function (uid) {
        var checkUpdateStrategyCampMap = {};
        var delPetsMap = {};
        for (var i = this.fighters.length - 1; i >= 0; i--) {
            var f = this.fighters[i];
            if (f.entity.armyUid === uid) {
                if (this.currentFighter.getUid() === f.getUid()) {
                    this.currentFighter.setRoundEnd();
                }
                this.fighters.splice(i, 1);
                // 删除韬略
                if (this.removeCampStrategy(f)) {
                    checkUpdateStrategyCampMap[f.camp] = true;
                }
            }
            var heroSkill = f.getPortrayalSkill();
            if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.YANG_YOUJI) {
                var petUid = 'pet_' + f.getUid();
                delPetsMap[petUid] = true;
            }
        }
        this.updateFighterPointMap();
        // 重新刷新下韬略
        if (!ut.isEmptyObject(checkUpdateStrategyCampMap)) {
            this.fighters.forEach(function (m) {
                if (checkUpdateStrategyCampMap[m.camp]) {
                    m.updateStrategyEffect();
                }
            });
        }
        // 检测删除召唤物
        for (var i = this.fighters.length - 1; i >= 0; i--) {
            var f = this.fighters[i];
            if (delPetsMap[f.getUid()]) {
                this.fighters.splice(i, 1);
            }
        }
    };
    // 获取当前出手士兵下标
    FSPBattleController.prototype.getCurFighterIndex = function () {
        for (var i = 0; i < this.fighters.length; i++) {
            var f = this.fighters[i];
            if (f.getUid() === this.currentFighter.getUid()) {
                return i;
            }
        }
        return -1;
    };
    // 刷新建筑信息
    FSPBattleController.prototype.UpdateBuildInfo = function (pawnId, lv) {
        this.fighters.forEach(function (m) {
            if (m.isTower()) {
                m.updateTowerInfo(pawnId, lv);
            }
        });
    };
    // 每一个逻辑帧
    FSPBattleController.prototype.updateFrame = function (dt) {
        if (!this.currentFighter || this.fighters.length === 0 || this.currentFighter.isBattleOver()) {
            return this.area.battleEndByLocal();
        }
        else if (this.currentFighter.updateBattleOver(dt)) {
            return;
        }
        else if (this.currentFighter.isRoundEnd()) {
            // 删除死亡的士兵
            var uid_1 = this.removeDieFighters();
            // 检测战斗是否结束
            var endDelayTime = this.checkBattleOver();
            if (endDelayTime > 0) {
                this.currentFighter.setBattleOverDelay(endDelayTime);
                return;
            }
            // 刷新位置分布
            this.updateFighterPointMap();
            // 上一个结束
            this.currentFighter.updateBuff();
            this.currentFighter.endAction();
            // 下一个出手
            this.currentFighter = this.getNextFighter();
            this.currentFighter.beginAction();
            if (uid_1) {
                this.fighters.delete(function (m) { return m.getUid() === uid_1; });
            }
        }
        this.currentFighter.behaviorTick(dt); //执行士兵的逻辑
    };
    // 刷新士兵死亡情况
    FSPBattleController.prototype.removeDieFighters = function () {
        var _this = this;
        var _a, _b;
        var removeCurrUid = '', checkUpdateStrategyCampMap = {}, noncombats = [], tempFighters = [];
        for (var i = this.fighters.length - 1; i >= 0; i--) {
            var f = this.fighters[i];
            if (f.isDie()) {
                if (f.getUid() === ((_a = this.currentFighter) === null || _a === void 0 ? void 0 : _a.getUid())) {
                    removeCurrUid = f.getUid(); //当前出手的士兵死亡 等到获取下一个出手士兵后再删除
                }
                else {
                    this.fighters.splice(i, 1);
                }
                // 删除实际军队里面的 这里不发送事件到视图层 因为要做死亡动画
                if (f.isPawn()) {
                    this.area.removeArmyPawn(f.entity.armyUid, f.entity.uid, false);
                    eventCenter.emit(EventType_1.default.UPDATE_BATTLE_ARMY_BY_UI);
                }
                // 删除韬略
                if (this.removeCampStrategy(f)) {
                    checkUpdateStrategyCampMap[f.camp] = true;
                }
                // 如果是秦良玉还要删除矛
                if (((_b = f.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.QIN_LIANGYU) {
                    noncombats.push('build_' + f.getUid());
                }
                // 如果是宠物和军旗 还需要删除temp里面的信息
                if (f.isPet() || f.isFlag()) {
                    tempFighters.push(f.getUid());
                }
            }
        }
        // 重新刷新下韬略
        if (!ut.isEmptyObject(checkUpdateStrategyCampMap)) {
            this.fighters.forEach(function (m) {
                if (checkUpdateStrategyCampMap[m.camp]) {
                    m.updateStrategyEffect();
                }
            });
        }
        // 删除非战斗单位
        noncombats.forEach(function (m) {
            _this.removeNoncombat(m);
            eventCenter.emit(EventType_1.default.REMOVE_PAWN, _this.area.index, m);
        });
        // 删除临时单位
        tempFighters.forEach(function (m) { return _this.area.removeBattleTempPawn(m); });
        return removeCurrUid;
    };
    // 战斗是否结束
    FSPBattleController.prototype.checkBattleOver = function () {
        if (this.fighters.length === 0) {
            return 1; //没有士兵肯定就结束了啊
        }
        var isBoss = this.area.isBoss();
        if (!isBoss && (this.mainDoors.length === 0 || this.mainDoors.some(function (m) { return m.isDie(); }))) {
            return 1; //血量小于0 直接结束
        }
        var campMap = {};
        if (!isBoss) {
            campMap[this.camp] = true;
        }
        this.fighters.forEach(function (m) {
            if (!m.isDie() && !m.isNoncombat()) {
                campMap[m.camp] = true;
            }
        });
        // cc.log("checkBattleOver campMap", campMap)
        if (Object.keys(campMap).length <= 1) {
            return 1000; //只要还只剩下一个阵营 就算结束
        }
        return 0;
    };
    // 刷新所有士兵的位置分布
    FSPBattleController.prototype.updateFighterPointMap = function () {
        var _a;
        var _this = this;
        this.fighterPointMap = {};
        this.camps = (_a = {}, _a[this.camp] = this.area.owner, _a);
        var flagPointMap = {};
        var sr = new SearchRange_1.default().init(this.checkIsBattleArea.bind(this));
        var lianPoList = [];
        this.fighters.forEach(function (m) {
            var _a;
            var id = m.getPoint().ID();
            var arr = _this.fighterPointMap[id];
            if (!arr) {
                arr = _this.fighterPointMap[id] = [];
            }
            arr.push({ uid: m.getUid(), id: m.getId(), camp: m.camp, pawnType: m.getPawnType() });
            if (!_this.camps[m.camp]) {
                _this.camps[m.camp] = m.entity.owner;
            }
            if (m.isFlag()) {
                sr.search(m.getPoint(), 2, true).forEach(function (p) {
                    var pid = p.ID();
                    var camps = flagPointMap[pid];
                    if (!camps) {
                        flagPointMap[pid] = [m.camp];
                    }
                    else if (!camps.has(m.camp)) {
                        camps.push(m.camp);
                    }
                });
            }
            else if (((_a = m.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.LIAN_PO && m.getHpRatio() !== 0.5) { //是否廉颇
                lianPoList.push({ lp: m, targets: [], overlaps: [], pointMap: {} });
            }
        });
        this.fighters.forEach(function (m) {
            if (!m.isPawn()) {
                return;
            }
            var point = m.getPoint(), pointId = point.ID();
            // 检测军旗
            var camps = flagPointMap[pointId];
            if (!camps || camps.length === 0) { //删除
                m.removeMultiBuff(Enums_1.BuffType.DAMAGE_INCREASE, Enums_1.BuffType.DAMAGE_REDUCE);
            }
            else {
                var has = camps.has(m.camp);
                if (has) { //添加增伤
                    m.addBuff(Enums_1.BuffType.DAMAGE_INCREASE, m.getOwner(), 1);
                }
                if (!has || camps.length >= 2) { //添加减伤
                    m.addBuff(Enums_1.BuffType.DAMAGE_REDUCE, m.getOwner(), 1);
                }
            }
            // 检测廉颇
            lianPoList.forEach(function (_a) {
                var lp = _a.lp, targets = _a.targets, overlaps = _a.overlaps, pointMap = _a.pointMap;
                if (lp.getUid() !== m.getUid() && lp.getCamp() === m.getCamp()) {
                    var dis = MapHelper_1.mapHelper.getPointToPointDis(lp.getPoint(), point);
                    if (dis > 3) {
                    }
                    else if (!pointMap[pointId]) {
                        pointMap[pointId] = true;
                        targets.push(m); //优先选择没有重叠的
                    }
                    else {
                        overlaps.push(m); //这里就是重叠一起的
                    }
                }
            });
            m.removeMultiBuffNoEmit(Enums_1.BuffType.LIAN_PO_ATTACK, Enums_1.BuffType.LIAN_PO_DEFEND);
        });
        // 廉颇添加buff
        lianPoList.forEach(function (_a) {
            var lp = _a.lp, targets = _a.targets, overlaps = _a.overlaps;
            var heroSkill = lp.getPortrayalSkill();
            var lpUid = lp.getUid(), isAttack = lp.getHpRatio() > 0.5, valueRatio = 1 + heroSkill.value * 0.01;
            var lpBuffMaxCnt = heroSkill.target, value = heroSkill.params;
            if (targets.length < lpBuffMaxCnt && overlaps.length > 0) {
                if (isAttack) {
                    overlaps.sort(function (a, b) {
                        var aw = a.getActAttack(), bw = b.getActAttack();
                        if (aw === bw) {
                            aw -= a.getAttackIndex();
                            bw -= b.getAttackIndex();
                        }
                        return bw - aw;
                    });
                }
                else {
                    overlaps.sort(function (a, b) {
                        var aw = 100 - a.getHpRatio() * 100, bw = 100 - b.getHpRatio() * 100;
                        if (aw === bw) {
                            aw -= a.getAttackIndex();
                            bw -= b.getAttackIndex();
                        }
                        return bw - aw;
                    });
                }
                targets.pushArr(overlaps.slice(0, lpBuffMaxCnt - targets.length));
            }
            else {
                targets = targets.slice(0, lpBuffMaxCnt);
            }
            var buffId = isAttack ? Enums_1.BuffType.LIAN_PO_ATTACK : Enums_1.BuffType.LIAN_PO_DEFEND;
            targets.forEach(function (m) { return m.addBuffValue(buffId, lpUid, value); });
            lp.addBuffValue(buffId, lpUid, Math.round(value * valueRatio));
        });
    };
    // 获取下一个战斗者
    FSPBattleController.prototype.getNextFighter = function () {
        if (this.currentFighter.isHasBuff(Enums_1.BuffType.CONTINUE_ACTION)) {
            return this.currentFighter; //继续行动
        }
        var uid = this.currentFighter.getUid();
        var i = 0, cnt = this.fighters.length;
        for (i = 0; i < cnt; i++) {
            if (this.fighters[i].getUid() === uid) {
                break;
            }
        }
        i = ut.loopValue(i + 1, cnt);
        return this.fighters[i];
    };
    // 获取当前状态快照md5
    FSPBattleController.prototype.getSnapshootMD5 = function () {
        var md5 = Math.abs(this.area.index) + '_' + this.area.curHp + '_' + this.area.maxHp;
        this.fighters.forEach(function (m) { return md5 += '_' + m.md5(); });
        return md5;
    };
    FSPBattleController.prototype.addCampStrategys = function (f) {
        var _this = this;
        if (!f.isHero()) {
            return false;
        }
        var camp = f.getCamp();
        var strategyMap = this.campStrategyMap[camp];
        if (!strategyMap) {
            strategyMap = this.campStrategyMap[camp] = {};
        }
        f.getPortrayal().strategys.forEach(function (m) {
            if (m.type === 50029 && _this.camp === camp) {
                return; //这个韬略只能处于进攻方增加
            }
            var _a = __read(m.targetId, 2), targetType = _a[0], targetValue = _a[1];
            if (targetType === 10) {
                targetType = 1;
                targetValue = f.getId();
            }
            var key = m.type + '_' + targetType + '_' + targetValue;
            var v = strategyMap[key];
            if (v) {
                v.addFighter(f);
            }
            else {
                strategyMap[key] = m.clone(targetType, targetValue, f);
            }
        });
        return true;
    };
    // 删除韬略
    FSPBattleController.prototype.removeCampStrategy = function (f) {
        if (!f.isHero()) {
            return false;
        }
        var camp = f.getCamp();
        var strategyMap = this.campStrategyMap[camp];
        if (ut.isEmptyObject(strategyMap)) {
            return false;
        }
        var uid = f.getUid(), ok = false;
        for (var k in strategyMap) {
            var m = strategyMap[k];
            if (m.removeFighter(uid)) {
                ok = true;
                if (m.fighters.length === 0) {
                    delete strategyMap[k];
                }
            }
        }
        return ok;
    };
    FSPBattleController.prototype.getCampStrategyMap = function (camp) {
        return this.campStrategyMap[camp];
    };
    FSPBattleController.prototype.getHeroFighters = function (type, camp, ignoreUid) {
        var obj = this.campStrategyMap[camp], arr = [];
        for (var k in obj) {
            obj[k].fighters.forEach(function (m) {
                var _a;
                if (((_a = m.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === type && m.getUid() !== ignoreUid) {
                    arr.push(m);
                }
            });
        }
        return arr;
    };
    FSPBattleController.prototype.getMainDoors = function () { return this.mainDoors; };
    FSPBattleController.prototype.getMainCamp = function () { return this.camp; };
    FSPBattleController.prototype.getFighters = function () { return this.fighters; };
    FSPBattleController.prototype.getRandom = function () { return this.random; };
    FSPBattleController.prototype.getAreaSize = function () { return this.area.areaSize; };
    FSPBattleController.prototype.getAreaIndex = function () { return this.area.index; };
    FSPBattleController.prototype.getAreaMainPoints = function () { return this.area.mainPoints; };
    // 获取士兵
    FSPBattleController.prototype.getFighter = function (uid) {
        if (!uid) {
            return null;
        }
        return this.fighters.find(function (m) { return m.getUid() === uid && !m.isDie(); }) || this.mainDoors.find(function (m) { return m.getUid() === uid && !m.isDie(); });
    };
    // 获取某个阵营的士兵数量
    FSPBattleController.prototype.getFighterCountByCamp = function (camp) {
        return this.fighters.filter(function (m) { return m.isPawn() && m.getCamp() === camp; }).length;
    };
    // 获取某个点的所有士兵
    FSPBattleController.prototype.getFightersByPoint = function (x, y) {
        return this.fighters.filter(function (m) { return m.getPoint().equals2(x, y); });
    };
    FSPBattleController.prototype.getFighterPointInfos = function (key) {
        var _a;
        return (_a = this.fighterPointMap[key]) === null || _a === void 0 ? void 0 : _a.filter(function (m) { return m.pawnType !== Enums_1.PawnType.NONCOMBAT; });
    };
    // 获取某个点位的士兵数量
    FSPBattleController.prototype.getFighterCountByPoint = function (point) {
        var _a;
        return ((_a = this.getFighterPointInfos(point.ID())) === null || _a === void 0 ? void 0 : _a.length) || 0;
    };
    // 获取某个点位的士兵数量
    FSPBattleController.prototype.getFighterCountForCampByPoint = function (point, camp, reverse) {
        var _a;
        return ((_a = this.fighterPointMap[point.ID()]) === null || _a === void 0 ? void 0 : _a.filter(function (m) { return (reverse ? m.camp !== camp : m.camp === camp) && m.pawnType !== Enums_1.PawnType.NONCOMBAT; }).length) || 0;
    };
    // 检测是否在战斗区域内
    FSPBattleController.prototype.checkIsBattleArea = function (x, y) {
        return this.area.checkIsBattleArea(x, y);
    };
    // 检测是否有士兵
    FSPBattleController.prototype.checkHasFighter = function (x, y) {
        var arr = this.getFighterPointInfos(x + '_' + y);
        return !!arr && arr.length > 0;
    };
    // 检测是否有某个士兵 根据id
    FSPBattleController.prototype.checkHasFighterById = function (x, y, id) {
        var _a;
        return !!((_a = this.fighterPointMap[x + '_' + y]) === null || _a === void 0 ? void 0 : _a.some(function (m) { return m.id === id; }));
    };
    // 检测是否可经过
    FSPBattleController.prototype.checkHasPass = function (x, y, camp) {
        if (!this.checkIsBattleArea(x, y)) {
            return false;
        }
        var arr = this.getFighterPointInfos(x + '_' + y);
        if (!arr || arr.length === 0) {
            return true;
        }
        else if (camp) {
            return !arr.some(function (m) { return m.camp !== camp; });
        }
        return false;
    };
    FSPBattleController.prototype.checkHasPassToState = function (x, y, camp) {
        if (!this.checkIsBattleArea(x, y)) {
            return 0;
        }
        var arr = this.getFighterPointInfos(x + '_' + y);
        if (!arr || arr.length === 0) {
            return 1;
        }
        else if (camp) {
            return !arr.some(function (m) { return m.camp !== camp; }) ? 2 : 0;
        }
        return 0;
    };
    // 是否孤立
    FSPBattleController.prototype.isIsolate = function (f) {
        var _this = this;
        var _a;
        var points = (_a = f.getSearchRange()) === null || _a === void 0 ? void 0 : _a.search(f.getPoint(), 1, true), camp = f.getCamp() || [], uid = f.getUid();
        return !points.some(function (point) {
            var arr = _this.getFighterPointInfos(point.ID());
            if (!arr || arr.length === 0) {
                return false;
            }
            return arr.some(function (m) { return m.uid !== uid && m.camp === camp; });
        });
    };
    // 搜索击飞点
    FSPBattleController.prototype.searchDiaupPoint = function (point, points) {
        var _a;
        return (_a = this.searchPoint) === null || _a === void 0 ? void 0 : _a.search(point, points, 0);
    };
    // 选择没有人的点
    FSPBattleController.prototype.searchIdlePoint = function (point, range) {
        var _a;
        return (_a = this.searchPoint) === null || _a === void 0 ? void 0 : _a.search(point, [], range);
    };
    // 检测冲撞点
    FSPBattleController.prototype.checkDashPoint = function (point, camp) {
        if (this.area.checkIsBuildArea(point.x, point.y)) {
            return false; //建筑区域不可停留
        }
        var arr = this.getFighterPointInfos(point.ID());
        if (!arr || arr.length === 0) {
            return true; //表示没有人 可以停留
        }
        var data = arr[0];
        return data.camp != camp && data.pawnType < Enums_1.PawnType.BUILD;
    };
    FSPBattleController.prototype.getFighterCampIndex = function (uid) {
        var _a, _b;
        var camp = uid ? (_b = (_a = this.fighters.find(function (m) { return m.getUid() === uid; })) === null || _a === void 0 ? void 0 : _a.camp) !== null && _b !== void 0 ? _b : -1 : this.camp;
        if (camp > 0) {
            var arr = [];
            for (var key in this.camps) {
                if (!GameHelper_1.gameHpr.checkIsOneAlliance(this.camps[key])) {
                    arr.push(Number(key));
                }
            }
            return arr.sort(function (a, b) { return a - b; }).indexOf(camp);
        }
        return -1;
    };
    // 删除有位置的点 从后面开始删
    FSPBattleController.prototype.removeHasFighterPointByLast = function (paths) {
        for (var i = paths.length - 1; i >= 0; i--) {
            var p = paths[i];
            if (this.checkHasFighter(p.x, p.y)) {
                paths.splice(i, 1); //从后往后看是否有人 如果有删除
            }
            else {
                break;
            }
        }
        return paths;
    };
    // 通过buff修正攻击力
    FSPBattleController.prototype.amendAttackByBuff = function (fighter, type, damage, reverse, addType, ignoreReduction) {
        if (damage < 0) {
            return damage;
        }
        var buff = fighter.checkTriggerBuff(type);
        if (!buff) {
            return damage;
        }
        else if (addType === 1) {
            return damage + buff.value;
        }
        var bv = buff.value;
        if (ignoreReduction) {
            bv *= ignoreReduction;
        }
        var val = reverse ? 1 - bv : bv;
        return Math.round(damage * val);
    };
    // 检测buff闪避
    FSPBattleController.prototype.checkBuffDodge = function (dodgeOdds, attacker, defender, ignoreDodge) {
        var _a;
        var addOdds = defender.getStrategyValue(50002);
        if (((_a = defender.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.ZHAO_YUN) {
            addOdds += 10; //赵云初始10
        }
        var buffs = defender.getBuffs(), oddsArr = [dodgeOdds];
        for (var i = 0, l = buffs.length; i < l; i++) {
            var m = buffs[i];
            if (m.type === Enums_1.BuffType.DODGE) { //闪避
                oddsArr.push(m.value);
            }
            else if (m.type === Enums_1.BuffType.VALOR) { //李嗣业 勇猛
                var skill_1 = defender.getPortrayalSkill();
                if ((skill_1 === null || skill_1 === void 0 ? void 0 : skill_1.id) === Enums_1.HeroType.LI_SIYE) {
                    oddsArr.push(m.value * skill_1.params);
                }
            }
            else if (m.type === Enums_1.BuffType.ROYAL_BLUE_DODGE) { //赵云 龙胆 闪避
                addOdds += m.value;
            }
        }
        // 被动技能 躲闪
        var evade = defender.getSkillByType(Enums_1.PawnSkillType.EVADE);
        if (evade) {
            oddsArr.push(evade.value);
        }
        if (attacker.isTower()) {
            // 先登 闪避
            if (defender.isHasBuff(Enums_1.BuffType.PRESTAGE)) {
                oddsArr.push(80);
            }
            // 韬略
            var v = defender.getStrategyValue(50023);
            if (v > 0) {
                oddsArr.push(v);
            }
        }
        // 防守方为连弩兵且有专属装备 则有闪避效果
        var skill = defender.getSkillByType(207);
        if ((skill === null || skill === void 0 ? void 0 : skill.intensifyType) === 1) {
            // 受到攻击伤害时，有30%几率闪避该次伤害，与攻击者的距离每远一格，几率就减少10%
            var dis = MapHelper_1.mapHelper.getPointToPointDis(attacker.getPoint(), defender.getPoint());
            var v = Math.max(0, 30 - Math.max(0, dis - 1) * 10);
            if (v > 0) {
                oddsArr.push(v);
            }
        }
        for (var i = 0, l = oddsArr.length; i < l; i++) {
            var odds = Math.round((oddsArr[i] + addOdds) * ignoreDodge);
            if (this.random.chance(odds)) {
                return true;
            }
        }
        return false;
    };
    // 受击基础攻击真实伤害
    FSPBattleController.prototype.onHitBaseTrueDamage = function (attacker, defender, data) {
        var trueDamage = this.getBaseAttackDamage(attacker, defender, data);
        var damage = defender.hitPrepDamageHandle(0, trueDamage).trueDamage;
        var v = defender.onHit(damage, [attacker.getOwner()]);
        if (data === null || data === void 0 ? void 0 : data.doAfter) {
            this.doAttackAfter(attacker, defender, {
                attackSegments: 1,
                sumDamage: damage,
                actDamage: v.damage,
                trueDamage: trueDamage,
                hitShield: v.hitShield,
            });
        }
        defender.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: defender.isDie() });
    };
    // 获取基础的攻击伤害
    FSPBattleController.prototype.getBaseAttackDamage = function (attacker, defender, data) {
        var _a, _b;
        var damage = attacker.getAttack();
        var defenderPawnType = defender.getPawnType(), isBuild = defenderPawnType === Enums_1.PawnType.BUILD;
        data = data || {};
        // 如果是建筑 伤害默认为1
        if (isBuild) {
            damage = 1;
        }
        else if (data.instabilityAttackIndex && attacker.getId() === 3104) {
            damage = attacker.getInstabilityRandomAttack(data.instabilityAttackIndex); //不稳定攻击
        }
        else if (data.initDamage) {
            damage = data.initDamage; //强制初始伤害
        }
        else {
            damage = attacker.amendAttack(damage);
        }
        // 攻击方是否有克制技能
        var attackRestrain = attacker.getSkillByType(Enums_1.PawnSkillType.ATTACK_RESTRAIN);
        if ((attackRestrain === null || attackRestrain === void 0 ? void 0 : attackRestrain.target) === defenderPawnType) {
            damage = Math.round(damage * attacker.amendRestrainValue(attackRestrain.value, Enums_1.PawnSkillType.ATTACK_RESTRAIN));
        }
        // 狩猎
        attackRestrain = attacker.getSkillByType(Enums_1.PawnSkillType.RESTRAIN_BEAST);
        if ((attackRestrain === null || attackRestrain === void 0 ? void 0 : attackRestrain.target) === defenderPawnType) {
            damage = Math.round(damage * attackRestrain.value);
        }
        // 防守方是否有克制技能
        var defenseRestrain = defender.getSkillByType(Enums_1.PawnSkillType.DEFENSE_RESTRAIN);
        if ((defenseRestrain === null || defenseRestrain === void 0 ? void 0 : defenseRestrain.target) === attacker.getPawnType()) {
            damage = Math.round(damage * defender.amendRestrainValue(defenseRestrain.value, Enums_1.PawnSkillType.DEFENSE_RESTRAIN));
        }
        if (!isBuild) {
            // 攻击力修正
            if (data === null || data === void 0 ? void 0 : data.attackAmend) {
                damage = Math.round(damage * data.attackAmend);
            }
            // 长剑兵攻击流血目标 攻击力提高30%
            if (attacker.getId() === 3105 && ((_a = attacker.getSkillByType(Enums_1.PawnSkillType.SKILL_216)) === null || _a === void 0 ? void 0 : _a.intensifyType) === 1 && defender.isHasBuff(Enums_1.BuffType.BLEED)) {
                damage = Math.round(damage * 1.3);
            }
            // 弓骑兵攻击近战时 攻击力提高30%
            if (attacker.getId() === 3405 && defender.getAttackRange() <= 1 && ((_b = attacker.getSkillByType(Enums_1.PawnSkillType.SKILL_217)) === null || _b === void 0 ? void 0 : _b.intensifyType) === 1) {
                damage = Math.round(damage * 1.3);
            }
            // 韬略 猎人攻击山贼
            var sv = attacker.getStrategyValue(31301);
            if (sv > 0 && defenderPawnType === Enums_1.PawnType.CATERAN) {
                damage += Math.round(damage * sv * 0.01);
            }
        }
        return damage;
    };
    // 获取攻击伤害
    FSPBattleController.prototype.getAttackDamage = function (attacker, defender, data) {
        var _this = this;
        var _a, _b, _c, _d;
        var owners = [attacker.getOwner()];
        var damage = attacker.getAttack(), trueDamage = 0, isCrit = false;
        var defenderPawnType = defender.getPawnType(), isBuild = defenderPawnType === Enums_1.PawnType.BUILD;
        var attackerHeroSkill = attacker.getPortrayalSkill(), defenderHeroSkill = defender.getPortrayalSkill();
        var isBaseAttack = (data === null || data === void 0 ? void 0 : data.attackType) === 'attack';
        // 最终修正伤害
        var attackLastDamageAmend = (_a = data === null || data === void 0 ? void 0 : data.lastDamageAmend) !== null && _a !== void 0 ? _a : 1, attackLastDamageLow = 0, ignoreReduction = 0;
        // ---------------------------------处理一级攻击力---------------------------------------- 技能
        // 如果是建筑 伤害默认为1
        if (isBuild) {
            damage = 1;
        }
        else if ((data === null || data === void 0 ? void 0 : data.instabilityAttackIndex) && attacker.getId() === 3104) {
            damage = attacker.getInstabilityRandomAttack(data.instabilityAttackIndex); //不稳定攻击
        }
        else if (data === null || data === void 0 ? void 0 : data.initDamage) {
            damage = data.initDamage; //强制初始伤害
        }
        else {
            damage = attacker.amendAttack(damage);
        }
        // 攻击方是否有克制技能
        var attackRestrain = attacker.getSkillByType(Enums_1.PawnSkillType.ATTACK_RESTRAIN);
        if ((attackRestrain === null || attackRestrain === void 0 ? void 0 : attackRestrain.target) === defenderPawnType) {
            damage = Math.round(damage * attacker.amendRestrainValue(attackRestrain.value, Enums_1.PawnSkillType.ATTACK_RESTRAIN));
            ignoreReduction += (attackRestrain.params || 0);
        }
        // 狩猎
        attackRestrain = attacker.getSkillByType(Enums_1.PawnSkillType.RESTRAIN_BEAST);
        if ((attackRestrain === null || attackRestrain === void 0 ? void 0 : attackRestrain.target) === defenderPawnType) {
            damage = Math.round(damage * attackRestrain.value);
        }
        // 防守方是否有克制技能
        var defenseRestrain = defender.getSkillByType(Enums_1.PawnSkillType.DEFENSE_RESTRAIN);
        if ((defenseRestrain === null || defenseRestrain === void 0 ? void 0 : defenseRestrain.target) === attacker.getPawnType()) {
            damage = Math.round(damage * defender.amendRestrainValue(defenseRestrain.value, Enums_1.PawnSkillType.DEFENSE_RESTRAIN));
        }
        if (!isBuild) {
            // 攻击力修正 ----------------------------------------
            if (data === null || data === void 0 ? void 0 : data.attackAmend) {
                damage = Math.round(damage * data.attackAmend);
            }
            // 长剑兵攻击流血目标 攻击力提高30%
            if (attacker.getId() === 3105 && ((_b = attacker.getSkillByType(Enums_1.PawnSkillType.SKILL_216)) === null || _b === void 0 ? void 0 : _b.intensifyType) === 1 && defender.isHasBuff(Enums_1.BuffType.BLEED)) {
                damage = Math.round(damage * 1.3);
            }
            // 弓骑兵攻击近战时 攻击力提高30%
            if (attacker.getId() === 3405 && defender.getAttackRange() <= 1 && ((_c = attacker.getSkillByType(Enums_1.PawnSkillType.SKILL_217)) === null || _c === void 0 ? void 0 : _c.intensifyType) === 1) {
                damage = Math.round(damage * 1.3);
            }
            // 韬略 猎人攻击山贼
            var sv = attacker.getStrategyValue(31301);
            if (sv > 0 && defenderPawnType === Enums_1.PawnType.CATERAN) {
                damage += Math.round(damage * sv * 0.01);
            }
            // 李牧 蓄势 攻击附带真实伤害
            var ANTICIPATION_ATTACK = attacker.getBuff(Enums_1.BuffType.ANTICIPATION_ATTACK);
            if (ANTICIPATION_ATTACK) {
                trueDamage += Math.round(damage * ANTICIPATION_ATTACK.value * 0.01);
            }
            // 额外伤害 ------------------------------------------
            if (data === null || data === void 0 ? void 0 : data.getExtraDamge) {
                damage += data.getExtraDamge();
            }
            // 猩猩 狂怒
            if (attacker.getId() === 4114 && attacker.isHasBuff(Enums_1.BuffType.RAGE)) {
                damage = Math.round(damage * 3);
            }
            // 韬略 当前生命值的伤害
            sv = attacker.getStrategyValue(40406);
            if (sv > 0) {
                damage += Math.round(defender.getCurHp() * sv * 0.01);
            }
            // 陈到 盾兵总攻击
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.CHEN_DAO) { //陈到额外伤害
                var extraDamge_1 = 0, armyUid_1 = attacker.getArmyUid();
                this.fighters.forEach(function (m) {
                    if (m.getArmyUid() !== armyUid_1 || m.getPawnType() !== Enums_1.PawnType.PELTAST || m.isDie()) {
                        return;
                    }
                    extraDamge_1 += m.getActAttack();
                });
                damage += Math.round(extraDamge_1 * attackerHeroSkill.params);
            }
            // 秦琼 斩将 造成额外伤害
            if (isBaseAttack) {
                var BEHEADED_GENERAL = attacker.checkTriggerBuff(Enums_1.BuffType.BEHEADED_GENERAL);
                if (BEHEADED_GENERAL) {
                    damage += Math.round(defender.getMaxHp() * BEHEADED_GENERAL.value * 0.01);
                }
            }
            // 乐进 先登
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.YUE_JIN) {
                damage += Math.round(attacker.getBuffValue(Enums_1.BuffType.PRESTAGE) * attackerHeroSkill.value * 0.01);
            }
            // 大黄蜂 额外造成当前生命
            var TELSON = attacker.getSkillByType(Enums_1.PawnSkillType.TELSON);
            if (TELSON) {
                damage += Math.round(defender.getCurHp() * TELSON.value);
            }
            // 连弩兵每第5次射击额外造成目标最大生命值2%的伤害
            var strategyBuff = attacker.getStrategyBuff(31102);
            if (strategyBuff) {
                var buff_1 = attacker.getBuffOrAdd(Enums_1.BuffType.ARBATOR_ATTACK_COUNT, attacker.getUid());
                if (buff_1.value + 1 >= strategyBuff.params) {
                    buff_1.value = 0;
                    damage += Math.round(defender.getMaxHp() * strategyBuff.value * 0.01);
                }
                else {
                    buff_1.value += 1;
                }
            }
            // 韬略 最大生命值的真实伤害
            sv = attacker.getStrategyValue(50030);
            if (sv > 0) {
                trueDamage += Math.round(defender.getMaxHp() * sv * 0.01);
            }
            // 政策 野怪克星
            attackLastDamageAmend += (attacker.getBuffValue(Enums_1.BuffType.ADD_DMG_TO_MONSTER) * 0.01);
            // 韬略 增伤
            attackLastDamageAmend += attacker.getStrategyValue(10001) * 0.01;
            attackLastDamageAmend += attacker.getStrategyValue(20005) * 0.01;
            // 韬略 对方是否有缴械
            sv = attacker.getStrategyValue(30301);
            if (sv > 0 && defender.isHasBuff(Enums_1.BuffType.DESTROY_WEAPONS)) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 生命值高于多少加伤害
            sv = attacker.getStrategyValue(50011);
            if (sv > 0 && defender.getHpRatio() > 0.5) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 对方是否有中毒
            sv = attacker.getStrategyValue(31201);
            if (sv > 0 && defender.isHasBuffs(Enums_1.BuffType.POISONING_MAX_HP, Enums_1.BuffType.POISONING_CUR_HP)) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 对方是否有眩晕
            sv = attacker.getStrategyValue(31801);
            if (sv > 0 && defender.isHasBuff(Enums_1.BuffType.DIZZINESS)) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 攻击力比自身低
            sv = attacker.getStrategyValue(50025);
            if (sv > 0 && defender.getActAttack() < attacker.getActAttack()) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 血越少伤害越高
            sv = attacker.getStrategyValue(40105);
            if (sv > 0) {
                attackLastDamageAmend += Math.floor((1 - attacker.getHpRatio()) / 0.1) * sv * 0.01;
            }
            // 韬略 对方是否孤立
            sv = attacker.getStrategyValue(50016);
            if (sv > 0 && this.isIsolate(defender)) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 交战30分钟 伤害提高
            sv = attacker.getStrategyValue(50019);
            if (sv > 0 && this.area.getBattleElapsedTime() >= ut.Time.Minute * 30) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 对英雄造成的伤害提高
            sv = attacker.getStrategyValue(50021);
            if (sv > 0 && defender.isHero()) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 敌方士兵数量大于我方时，我方全体士兵造成的伤害提高
            sv = attacker.getStrategyValue(50024);
            if (sv > 0) {
                var cnt = this.getFighterCountByCamp(attacker.getCamp());
                if (cnt < this.fighters.length - cnt) {
                    attackLastDamageAmend += sv * 0.01;
                }
            }
            // 韬略 四周没有友方增伤
            sv = attacker.getStrategyValue(50026);
            if (sv > 0 && this.isIsolate(attacker)) {
                attackLastDamageAmend += sv * 0.01;
            }
            // 韬略 全体前20回合造成的伤害提高10%
            var RECORD_ROUND_ADD_DAMAGE = attacker.getBuff(Enums_1.BuffType.RECORD_ROUND_ADD_DAMAGE);
            if (RECORD_ROUND_ADD_DAMAGE && RECORD_ROUND_ADD_DAMAGE.value > 0) {
                RECORD_ROUND_ADD_DAMAGE.value -= 1;
                attackLastDamageAmend += attacker.getStrategyValue(50032) * 0.01;
            }
            // 剑盾兵专属被动 加伤害
            var RODELERO_ADD_ATTACK = attacker.checkTriggerBuff(Enums_1.BuffType.RODELERO_ADD_ATTACK);
            if (RODELERO_ADD_ATTACK) {
                attackLastDamageAmend += RODELERO_ADD_ATTACK.value * 0.01;
            }
            // 斧骑兵 盾甲粉碎
            var SHIELD_CRUSHING = attacker.getSkillByType(Enums_1.PawnSkillType.SHIELD_CRUSHING);
            if (SHIELD_CRUSHING) {
                // 有护盾伤害提高
                if (defender.getShieldVal() > 0) {
                    attackLastDamageAmend += SHIELD_CRUSHING.value;
                }
                // 专属的重击 伤害提高30%
                var instabilityAttackIndex = (data === null || data === void 0 ? void 0 : data.instabilityAttackIndex) || 0;
                if (instabilityAttackIndex % 2 === 1) {
                    attackLastDamageAmend += 0.3;
                }
                // 程咬金
                if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.CHENG_YAOJIN) {
                    var iai = Math.floor(instabilityAttackIndex / 2);
                    if (iai === 1) { // 第1斧 加伤害
                        attackLastDamageAmend += 0.5;
                    }
                    else if (iai === 2) { //第3斧 加真实伤害
                        trueDamage += Math.round((defender.getMaxHp() - defender.getCurHp()) * attackerHeroSkill.value * 0.01);
                    }
                }
            }
            // 夏侯渊 奔袭
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.XIA_HOUYUAN) {
                var v = attacker.getBuffValue(Enums_1.BuffType.LONG_RANGE_RAID) * attackerHeroSkill.params * 0.01;
                attackLastDamageAmend += v;
            }
            // 赵云 龙胆 伤害
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.ZHAO_YUN) {
                attackLastDamageAmend += attacker.getBuffValue(Enums_1.BuffType.ROYAL_BLUE_DAMAGE) * 0.01;
            }
            // 许褚 裸衣
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.XU_CHU) {
                attackLastDamageAmend += attacker.getBuffValue(Enums_1.BuffType.NAKED_CLOTHES);
            }
            // 吕蒙 三日后伤害提高
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.LV_MENG && attacker.getBuffValue(Enums_1.BuffType.CHECK_ABNEGATION) >= attackerHeroSkill.target) {
                attackLastDamageAmend += attackerHeroSkill.value * 0.01;
            }
            // 于禁 毅重
            attackLastDamageAmend += attacker.getBuffValue(Enums_1.BuffType.RESOLUTE) * 0.01;
            // 邓艾 屯垦令结束加伤害
            if (attacker.isHasBuff(Enums_1.BuffType.TONDEN_RECOVER)) {
                attackLastDamageAmend += 0.1;
            }
            // 廉颇 攻
            attackLastDamageAmend += attacker.getBuffValue(Enums_1.BuffType.LIAN_PO_ATTACK) * 0.01;
            // 霍去病 跳斩伤害
            if (!isBaseAttack) {
                var JUMPSLASH_DAMAGE = attacker.checkTriggerBuff(Enums_1.BuffType.JUMPSLASH_DAMAGE);
                if (JUMPSLASH_DAMAGE) {
                    attackLastDamageAmend += JUMPSLASH_DAMAGE.value * 0.01;
                }
            }
            // 高顺 陷阵 每损失1%生命，造成的伤害就提高2%
            if (attacker.isHasBuff(Enums_1.BuffType.BREAK_ENEMY_RANKS)) {
                var maxHp = attacker.getMaxHp();
                var v = (maxHp - attacker.getCurHp()) / maxHp;
                attackLastDamageAmend += v;
            }
            // 孟获 再起 提高伤害
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.MENG_HUO) {
                var v = attacker.getBuffValue(Enums_1.BuffType.RECURRENCE) * attackerHeroSkill.value * 0.01;
                attackLastDamageAmend += v;
            }
            // 攻击方被动暴击 目前暂时是野怪技能
            var critSkill = attacker.getSkillByType(Enums_1.PawnSkillType.CRIT);
            if (critSkill && this.random.chance(critSkill.target)) {
                isCrit = true;
                damage = Math.round(damage * critSkill.value);
            }
        }
        else {
            // 政策 摧坚巧工
            var v = attacker.getBuffValue(Enums_1.BuffType.ADD_DMG_TO_BUILD);
            if (v > 0 && this.random.chance(v)) {
                damage += 1;
            }
            // 环境 建筑受到的伤害提高
            var ENVIRONMENT_BUILD_DMG = this.environmentBuffs.find(function (m) { return m.type === Enums_1.BuffType.ENVIRONMENT_BUILD_DMG; });
            if (ENVIRONMENT_BUILD_DMG) {
                damage += ENVIRONMENT_BUILD_DMG.value;
            }
        }
        // ---------------------------------处理二级攻击力---------------------------------------- 装备效果
        var attackerAttackRange = attacker.getAttackRange();
        var effects1 = attacker.getEquipEffects(), effects2 = defender.getEquipEffects();
        var executeRatio = 0, infallibleRatio = 0, ignoreDodge = 0;
        // 最后结算伤害
        if (!isBuild) {
            // 攻击方装备效果
            effects1.forEach(function (m) {
                if (m.type === Enums_1.EquipEffectType.BLACK_IRON_STAFF) { //玄铁杖
                    // 目标身上是否有无法再触发的buff
                    if (!defender.isHasBuff(Enums_1.BuffType.BLACK_IRON_STAFF_MARK)) {
                        defender.addBuff(Enums_1.BuffType.BLACK_IRON_STAFF_MARK, attacker.getUid(), 1); //标记
                        var val = defender.getMaxHp() * m.value * 0.01;
                        trueDamage += Math.round(val);
                        // 添加护盾
                        attacker.addBuffValue(Enums_1.BuffType.BLACK_IRON_STAFF_SHIELD, attacker.getUid(), Math.round(val * 0.3));
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.CUR_HP_DAMAGE) { //当前生命值的伤害
                    damage += Math.round(defender.getCurHp() * m.value * 0.01);
                }
                else if (m.type === Enums_1.EquipEffectType.CRIT) { //暴击
                    if (_this.random.chance(m.odds)) {
                        isCrit = true;
                        damage = Math.round(damage * m.value * 0.01);
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.MAX_HP_TRUE_DMG) { //百分比真实伤害
                    if (_this.random.chance(m.odds)) {
                        var base = attackerAttackRange > 1 ? 1 : 2; //近战+2 远程+1
                        var val = base + attacker.getLv();
                        trueDamage += Math.round(defender.getMaxHp() * val * 0.01);
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.BILLHOOK) { //钩镰
                    // 对生命和攻击力都比自身高的目标 额外造成真实伤害
                    var attack = defender.getActAttack();
                    if (defender.getCurHp() > attacker.getCurHp() && attack > attacker.getActAttack()) {
                        trueDamage += Math.round(attack * (m.value * attacker.getLv()) * 0.01);
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.SILVER_SNAKE_WHIP) { //银蛇鞭
                    // 切换目标加伤害
                    if (isBaseAttack) {
                        var buff_2 = attacker.getBuffOrAdd(Enums_1.BuffType.CHECK_SILVER_SNAKE_WHIP, '');
                        if (buff_2.provider !== defender.getUid()) {
                            buff_2.provider = defender.getUid();
                            attackLastDamageAmend += (m.value * 0.01);
                        }
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.DOUBLE_EDGED_SWORD) { //月牙刀
                    attackLastDamageAmend += (m.value * 0.01);
                    ignoreReduction += 0.2; //无视20%减伤
                }
                else if (m.type === Enums_1.EquipEffectType.INFALLIBLE) { //转伤
                    infallibleRatio = m.value * 0.01;
                }
                else if (m.type === Enums_1.EquipEffectType.NOT_DODGE) { //无法被闪避
                    ignoreDodge += m.value * 0.01;
                }
                else if (m.type === Enums_1.EquipEffectType.EXECUTE) { //处决
                    executeRatio = m.value * 0.01;
                }
                else if (m.type === Enums_1.EquipEffectType.HIT_GET_SUCK_BLOOD) { //受击获得吸血
                    buff = attacker.getBuff(Enums_1.BuffType.HIT_SUCK_BLOOD);
                    if (buff && buff.value >= 30) {
                        attackLastDamageAmend += 0.3; //达到30%吸血时下次攻击提高30%伤害
                    }
                }
            });
            // 军旗伤害提升和降低
            var DAMAGE_INCREASE = attacker.getBuff(Enums_1.BuffType.DAMAGE_INCREASE), DAMAGE_REDUCE = attacker.getBuff(Enums_1.BuffType.DAMAGE_REDUCE);
            if (DAMAGE_INCREASE && DAMAGE_REDUCE) {
                // 如果有2个 就抵消了
            }
            else if (DAMAGE_INCREASE) {
                attackLastDamageAmend += DAMAGE_INCREASE.value;
            }
            else if (DAMAGE_REDUCE) {
                attackLastDamageLow += DAMAGE_REDUCE.value;
            }
            // 不动如山
            var STILL_MOUNTAIN = defender.getSkillByType(Enums_1.PawnSkillType.STILL_MOUNTAIN);
            if (STILL_MOUNTAIN) {
                attackLastDamageLow += STILL_MOUNTAIN.value; //0.7
            }
            STILL_MOUNTAIN = attacker.getSkillByType(Enums_1.PawnSkillType.STILL_MOUNTAIN);
            if (STILL_MOUNTAIN) {
                ignoreReduction += STILL_MOUNTAIN.value; //无视减伤
            }
            // 屯垦令开始时降低伤害
            if (attacker.isHasBuff(Enums_1.BuffType.TONDEN_BEGIN)) {
                attackLastDamageLow += 0.2;
            }
            attackLastDamageLow = Math.max(0, 1 - attackLastDamageLow);
            // 最终伤害修正
            damage = Math.round(damage * attackLastDamageAmend * attackLastDamageLow);
            // 获取真实伤害
            if (data === null || data === void 0 ? void 0 : data.getTrueDamage) {
                trueDamage += data.getTrueDamage();
            }
            // 真实伤害修正
            trueDamage = Math.round(trueDamage * attackLastDamageAmend * attackLastDamageLow);
            // 杨妙真 识破 这个需要放到最后特殊处理 因为算的额外真实伤害
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.YANG_MIAOZHEN) {
                var buff_3 = defender.getBuff(Enums_1.BuffType.PENETRATE);
                if (buff_3) {
                    var td = Math.round(defender.getMaxHp() * attackerHeroSkill.params * attackLastDamageLow);
                    trueDamage += td;
                    buff_3.value = Math.round(td * attackerHeroSkill.value * 0.01);
                }
            }
            // 转伤
            if (infallibleRatio > 0) {
                var infallibleTrueDamage = Math.round(damage * infallibleRatio);
                trueDamage += infallibleTrueDamage;
                damage -= infallibleTrueDamage;
            }
            // 韬略 无视闪避
            ignoreDodge += attacker.getStrategyValue(40306) * 0.01;
        }
        // 格挡
        var buff = defender.checkTriggerBuffOr(Enums_1.BuffType.PARRY, Enums_1.BuffType.PARRY_102, Enums_1.BuffType.PARRY_001);
        if (buff) {
            return { damage: -2, trueDamage: 0, isCrit: false };
        }
        else if (isBaseAttack) {
            buff = defender.getBuff(Enums_1.BuffType.TURNTHEBLADE);
            if (buff && this.random.chance(buff.value)) {
                // // 典韦招架则反击 可以放到攻击后 并且需要判断多段攻击的时候处理
                // const skill = defender.getPortrayalSkill()
                // if (skill?.id === HeroType.DIAN_WEI) {
                //     // 获取目标范围2格内的敌方目标
                //     const arr = defender.getCanAttackPawnByRange(defender.getCanAttackFighters(), 2, 8, '')
                //     if (arr.length > 0) {
                //         let val = Math.round(defender.getActAttack() * (skill.value * 0.01))
                //         arr.forEach(f => {
                //             val = f.hitPrepDamageHandle(0, val).trueDamage
                //             const v = f.onHit(val, owners)
                //             f.changeState(PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: f.isDie(), time: data.time })
                //         })
                //     }
                //     defender.changeState(PawnState.SKILL, { sound: '', skillName: 'skill_back', currAttackTime: 0, skill, targetPoint: attacker.getPoint() })
                // }
                return { damage: -3, trueDamage: 0, isCrit: false };
            }
        }
        else { //技能攻击
            if (defender.checkTriggerBuff(Enums_1.BuffType.WITHSTAND)) {
                return { damage: -4, trueDamage: 0, isCrit: false }; //抵挡技能攻击
            }
        }
        // ---------------------------------处理防守方减伤----------------------------------------
        ignoreReduction += (((_d = attacker.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK)) === null || _d === void 0 ? void 0 : _d.params) || 0); //无视减伤
        ignoreReduction = Math.max(0, 1 - ignoreReduction);
        // 韬略 减伤 先算
        if (damage > 0 && !isBuild) {
            var v = Math.round(defender.getStrategyValue(10001) * ignoreReduction) * 0.01;
            damage = Math.round(damage * (1 - v));
            v = Math.round(defender.getStrategyValue(20006) * ignoreReduction) * 0.01;
            damage = Math.round(damage * (1 - v));
            v = Math.round(defender.getStrategyValue(30401) * ignoreReduction) * 0.01;
            if (v > 0 && attacker.getActAttack() > defender.getActAttack()) {
                damage = Math.round(damage * (1 - v));
            }
            v = Math.round(defender.getStrategyValue(40206) * ignoreReduction);
            damage = Math.max(0, damage - v);
            // 韬略 四周没有友方减伤
            v = Math.round(defender.getStrategyValue(50020) * ignoreReduction) * 0.01;
            if (v > 0 && this.isIsolate(defender)) {
                damage = Math.round(damage * (1 - v));
            }
            v = Math.round(defender.getStrategyValue(50033) * ignoreReduction) * 0.01;
            damage = Math.round(damage * (1 - v));
        }
        var FIXED_DAMAGE = null, SAME_CORPS_REDUCE_DMG = null, dodgeOdds = 0;
        effects2.forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.REDUCTION_INJURY) { //锁子甲
                var value = 5 + defender.getBuffValue(Enums_1.BuffType.HAUBERK_DEFENSE);
                var v = Math.round(value * ignoreReduction);
                damage = Math.max(0, damage - v);
            }
            else if (m.type === Enums_1.EquipEffectType.REDUCTION_RANGED) { //减少远程伤害
                if (attackerAttackRange > 1 && damage > 0) {
                    var v = Math.round(m.value * ignoreReduction) * 0.01;
                    if (defender.getHpRatio() < 0.5) { //血量低于50 效果减半
                        damage = Math.round(damage * (1 - v * 0.5));
                    }
                    else {
                        damage = Math.round(damage * (1 - v));
                    }
                }
            }
            else if (m.type === Enums_1.EquipEffectType.REDUCTION_MELEE) { //减少近战伤害
                if (attackerAttackRange <= 1 && damage > 0) {
                    var v = Math.round(m.value * ignoreReduction) * 0.01;
                    if (defender.getHpRatio() < 0.5) { //血量低于50 效果翻倍
                        damage = Math.round(damage * (1 - v * 2));
                    }
                    else {
                        damage = Math.round(damage * (1 - v));
                    }
                }
            }
            else if (m.type === Enums_1.EquipEffectType.THOUSAND_UMBRELLA) { //减伤伤害 千机伞
                var val = defender.getBuffValue(Enums_1.BuffType.THOUSAND_UMBRELLA) === 1 ? m.odds * 2 : m.odds;
                var v = Math.round(val * ignoreReduction) * 0.01;
                damage = Math.round(damage * (1 - v));
            }
            else if (m.type === Enums_1.EquipEffectType.OBSIDIAN_ARMOR) { //黑曜铠
                if (defender.isHasBuff(Enums_1.BuffType.OBSIDIAN_ARMOR_DEFENSE)) {
                    var v = Math.round(m.value * ignoreReduction) * 0.01;
                    damage = Math.round(damage * (1 - v));
                }
            }
            else if (m.type === Enums_1.EquipEffectType.SAME_CORPS_REDUCE_DMG) { //受到不同目标的伤害递减
                SAME_CORPS_REDUCE_DMG = m;
            }
            else if (m.type === Enums_1.EquipEffectType.FIXED_DAMAGE) { //固定伤害
                FIXED_DAMAGE = m;
            }
            else if (m.type === Enums_1.EquipEffectType.DODGE) { //闪避
                dodgeOdds = m.odds;
            }
        });
        if (!isBuild) {
            // 是否闪避
            if (this.checkBuffDodge(dodgeOdds, attacker, defender, 1 - ignoreDodge)) {
                // 赵云 龙胆 伤害
                if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.ZHAO_YUN) {
                    var buff_4 = defender.getBuffOrAdd(Enums_1.BuffType.ROYAL_BLUE_DAMAGE, defender.getUid());
                    buff_4.value += defenderHeroSkill.value;
                }
                if (attacker.isTower()) {
                    // 乐进 先登 闪避后加伤害
                    if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.YUE_JIN) {
                        var buff_5 = defender.getBuff(Enums_1.BuffType.PRESTAGE);
                        if (buff_5) {
                            buff_5.value += damage;
                        }
                    }
                }
                return { damage: -1, trueDamage: 0, isCrit: false };
            }
            if (!defender.isDie()) {
                // 处决
                if (executeRatio > 0) {
                    // 先计算本次伤害是否能破护盾
                    var curActHp = defender.getCurHp() + defender.getShieldVal();
                    if (curActHp / defender.getMaxHp() < executeRatio) {
                        damage = 0;
                        trueDamage = curActHp;
                        // 被处决加攻击力
                        buff = attacker.getBuffOrAdd(Enums_1.BuffType.ADD_EXECUTE_ATTACK, attacker.getUid());
                        buff.value += 1;
                        return { damage: damage, trueDamage: trueDamage, isCrit: false };
                    }
                }
                // 黄忠 处决
                if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.HUANG_ZHONG) {
                    // 先计算本次伤害是否能破护盾
                    var curActHp = defender.getCurHp() + defender.getShieldVal(), ratio = curActHp / defender.getMaxHp();
                    var odds = 0;
                    if (ratio < attackerHeroSkill.params * 0.5) {
                        odds = attackerHeroSkill.value * 2;
                    }
                    else if (ratio < attackerHeroSkill.params) {
                        odds = attackerHeroSkill.value;
                    }
                    if (this.random.chance(odds)) {
                        damage = 0;
                        trueDamage = curActHp;
                        return { damage: damage, trueDamage: trueDamage, isCrit: false };
                    }
                }
            }
        }
        // 普通伤害修正
        if (damage > 0 && !isBuild) {
            // 护心镜
            if (SAME_CORPS_REDUCE_DMG) {
                buff = defender.getBuff(Enums_1.BuffType.DAMAGE_DECREASE);
                if (buff) {
                    var v = Math.round(buff.value * ignoreReduction) * 0.01;
                    damage = Math.round(damage * (1 - v));
                }
                else {
                    buff = defender.addBuff(Enums_1.BuffType.DAMAGE_DECREASE, '');
                }
                if (!buff.provider.includes(attacker.getUid())) {
                    if (buff.provider) {
                        buff.provider += '|';
                    }
                    buff.provider += attacker.getUid();
                    buff.value = Math.min(SAME_CORPS_REDUCE_DMG.value * 3, buff.value + 3);
                }
            }
            // 防守方增益buff和技能 需要放到最后计算
            if (attackerAttackRange > 1) {
                var REDUCTION_RANGED = defender.getSkillByType(Enums_1.PawnSkillType.REDUCTION_RANGED);
                if (REDUCTION_RANGED) {
                    damage = Math.round(damage * (1 - REDUCTION_RANGED.value * ignoreReduction)); //被动技能 减少远程伤害
                }
            }
            // 鳞甲
            var REDUCTION_DMG = defender.getSkillByType(Enums_1.PawnSkillType.REDUCTION_DMG);
            if (REDUCTION_DMG) {
                damage = Math.round(damage * (1 - REDUCTION_DMG.value * ignoreReduction)); //被动技能 减少伤害
            }
            // 立盾
            damage = this.amendAttackByBuff(defender, Enums_1.BuffType.STAND_SHIELD, damage, true, 0, ignoreReduction);
            // 姜维 智勇
            if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.JIANG_WEI) {
                buff = defender.getBuff(Enums_1.BuffType.WISDOM_COURAGE);
                if (buff) {
                    var v = buff.value * defenderHeroSkill.params * 0.01;
                    damage = Math.round(damage * (1 - v * ignoreReduction));
                }
            }
            // 张辽 突袭
            if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.ZHANG_LIAO) {
                buff = defender.getBuff(Enums_1.BuffType.ASSAULT);
                if (buff) {
                    var v = buff.value * 0.01;
                    damage = Math.round(damage * (1 - v * ignoreReduction));
                }
            }
            // 廉颇 守
            var buffValue = defender.getBuffValue(Enums_1.BuffType.LIAN_PO_DEFEND);
            if (buffValue > 0) {
                var v = buffValue * 0.01;
                damage = Math.round(damage * (1 - v * ignoreReduction));
            }
            // 梁红玉 擂鼓防护
            buffValue = defender.getBuffValue(Enums_1.BuffType.THUNDERS_DEFENSE);
            if (buffValue > 0) {
                var v = buffValue * 0.01;
                damage = Math.round(damage * (1 - v * ignoreReduction));
            }
            // 辛弃疾 金戈
            if (defender.isHasBuff(Enums_1.BuffType.KERIAN)) {
                damage = Math.round(damage * (1 - 0.3 * ignoreReduction));
            }
            // 李牧 蓄势防护
            if (defender.isHasBuff(Enums_1.BuffType.ANTICIPATION_DEFENSE)) {
                damage = Math.round(damage * (1 - 0.5 * ignoreReduction));
            }
            // 周泰 为3格范围内承担伤害
            this.getHeroFighters(Enums_1.HeroType.ZHOU_TAI, defender.getCamp(), defender.getUid()).forEach(function (m) {
                var skill = m.getPortrayalSkill();
                if (MapHelper_1.mapHelper.getPointToPointDis(m.getPoint(), defender.getPoint()) <= skill.target) {
                    var r = skill.value;
                    var transferDamage = Math.round(damage * r * 0.01);
                    var transferTrueDamage = Math.round(trueDamage * r * 0.01);
                    damage = Math.max(damage - transferDamage, 0);
                    trueDamage = Math.max(trueDamage - transferTrueDamage, 0);
                    // 扣除伤害 不用触发破甲
                    var v = m.onHit(transferDamage + transferTrueDamage, owners);
                    m.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: m.isDie(), time: 0 });
                }
            });
            // 陈到 伤害转移
            if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.CHEN_DAO) {
                var peltast_1 = null, maxRatio_1 = 0;
                var armyUid_2 = defender.getArmyUid();
                this.getFighters().forEach(function (m) {
                    if (m.getArmyUid() !== armyUid_2 || m.getPawnType() !== Enums_1.PawnType.PELTAST || m.isDie()) {
                        return;
                    }
                    var ratio = m.getHpRatio();
                    if (ratio > maxRatio_1) {
                        peltast_1 = m;
                        maxRatio_1 = ratio;
                    }
                });
                if (peltast_1) {
                    var transferDamage = Math.round(damage * defenderHeroSkill.value * 0.01);
                    var transferTrueDamage = Math.round(trueDamage * defenderHeroSkill.value * 0.01);
                    damage = Math.max(damage - transferDamage, 0);
                    trueDamage = Math.max(trueDamage - transferTrueDamage, 0);
                    // 扣除伤害 不用触发破甲
                    var v = peltast_1.onHit(transferDamage + transferTrueDamage, owners);
                    peltast_1.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: peltast_1.isDie(), time: 0 });
                }
            }
            // 金甲 这里单独将 固定伤害放到最后
            if (FIXED_DAMAGE) {
                var fd = FIXED_DAMAGE.value;
                if (damage > fd) {
                    var s = damage - fd; //多余伤害
                    damage = fd;
                    buff = defender.getBuff(Enums_1.BuffType.DELAY_DEDUCT_HP) || defender.addBuff(Enums_1.BuffType.DELAY_DEDUCT_HP, defender.getUid(), 1);
                    buff.value += s;
                }
            }
        }
        return { damage: damage, trueDamage: trueDamage, isCrit: isCrit };
    };
    // 造成伤害后
    FSPBattleController.prototype.doAttackAfter = function (attacker, defender, data) {
        var _this = this;
        var _a, _b;
        var owners = [attacker.getOwner()];
        var attackRange = attacker.getAttackRange(), camp = attacker.getCamp();
        var attackerHeroSkill = attacker.getPortrayalSkill(), defenderHeroSkill = defender.getPortrayalSkill();
        var isDie = defender.isDie();
        var heal = 0, attackerHeal = 0, lastDamageAmend = data.lastDamageAmend || 0, trueDamage = data.trueDamage || 0;
        var attackHeal = data.healVal || 0, damageHealRatio = 0;
        var isBaseAttack = data.attackType === 'attack';
        var attackSegments = data.attackSegments || 0;
        var suckbloodShield = false;
        if (lastDamageAmend <= 0) {
            lastDamageAmend = 1;
        }
        if (defender.isPawn()) {
            // 攻击方装备效果
            attacker.getEquipEffects().forEach(function (m) {
                if (m.type === Enums_1.EquipEffectType.LONGYUAN_SWORD) { //龙渊剑
                    if (_this.random.chance(25)) {
                        var buff = attacker.getBuffOrAdd(Enums_1.BuffType.LONGYUAN_SWORD_ATTACK, attacker.getUid());
                        buff.value += 1;
                        _this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, attacker.getPoint());
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.BLOOD_RETURN) { //回血
                    if (data.actDamage > 0 && !attackSegments) {
                        // 攻击范围大于2恢复减半
                        var value = attackRange > 2 ? Math.round(m.value * 0.5) : m.value;
                        attackHeal += value;
                        // 有几率给血量百分比最低的回血
                        if (_this.random.chance(40)) {
                            var uid_2 = attacker.getUid(), point_1 = attacker.getPoint();
                            var f_1 = null, r_1 = 0;
                            _this.fighters.forEach(function (m) {
                                if (m.camp === camp && m.getUid() !== uid_2 && !m.isDie() && m.getPawnType() < Enums_1.PawnType.MACHINE && MapHelper_1.mapHelper.getPointToPointDis(m.getPoint(), point_1) <= 5) {
                                    var hr = m.getHpRatio();
                                    if (r_1 === 0 || hr < r_1) {
                                        r_1 = hr;
                                        f_1 = m;
                                    }
                                }
                            });
                            if (f_1) {
                                var val = f_1.onHeal(value);
                                f_1.changeState(Enums_1.PawnState.HEAL, { val: val, time: 0 });
                            }
                        }
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.SUCK_BLOOD) { //吸血
                    damageHealRatio += m.value;
                    suckbloodShield = true;
                }
                else if (m.type === Enums_1.EquipEffectType.ATTACK_GET_SHIELD) { //攻击获得护盾
                    if (data.actDamage > 0) {
                        attacker.addBuffValue(Enums_1.BuffType.ATTACK_SHIELD, attacker.getUid(), Math.max(1, Math.round(defender.getMaxHp() * m.value * 0.01)));
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.RANGE_ATTACK) { //范围攻击 乾坤刀
                    if (data.actDamage > 0 && _this.random.chance(m.odds)) {
                        var arr = defender.getCanAttackPawnByRange(attacker.getCanAttackFighters(), 1, 4, defender.getUid());
                        if (arr.length > 0) {
                            arr.forEach(function (f) {
                                _this.onHitBaseTrueDamage(attacker, f, { attackAmend: m.value * 0.01 * lastDamageAmend, instabilityAttackIndex: data.instabilityAttackIndex });
                            });
                            var point = defender.getPoint();
                            // eventCenter.emit(EventType.PLAY_BATTLE_EFFECT, { type: m.type * 10000 + 1, delay: 0, index: this.area.index, point, root: 'di' })
                            // eventCenter.emit(EventType.PLAY_BATTLE_EFFECT, { type: m.type * 10000 + 2, delay: 0, index: this.area.index, point, root: 'top' })
                            _this.playBattleEffect([310001], point, 'di');
                            _this.playBattleEffect([310002], point, 'top');
                        }
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.BURNING_HEART_RING && !isBaseAttack && !isDie) { //焚心戒
                    if (data.actDamage > 0 && _this.random.chance(m.odds)) {
                        var val = 5 * attacker.getLv(), buff = defender.getBuff(Enums_1.BuffType.IGNITION);
                        if (buff) {
                            val = Math.max(val, buff.value);
                        }
                        defender.addBuffValue(Enums_1.BuffType.IGNITION, attacker.getUid(), val);
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.SPIKY_BALL && isDie) { //尖刺球
                    var arr = defender.getCanAttackPawnByRange(attacker.getCanAttackFighters(), 2, 8, defender.getUid());
                    if (arr.length > 0) {
                        var extraDamge_2 = Math.max(0, data.sumDamage - data.actDamage);
                        arr.forEach(function (f) {
                            var trueDamage = _this.getBaseAttackDamage(attacker, f, { attackAmend: m.value * 0.01 });
                            var damage = f.hitPrepDamageHandle(0, trueDamage + extraDamge_2).trueDamage;
                            var v = f.onHit(damage, [attacker.getOwner()]);
                            f.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: f.isDie() });
                        });
                        _this.playBattleEffect([480001], defender.getPoint(), 'top');
                    }
                }
            });
            // 是否有 周瑜的 链索
            var WIRE_CHAIN = defender.getBuff(Enums_1.BuffType.WIRE_CHAIN);
            if (WIRE_CHAIN) {
                var uid_3 = defender.getUid();
                var arr = attacker.getCanAttackFighters().filter(function (m) { return m.getUid() !== uid_3 && m.isHasBuff(Enums_1.BuffType.WIRE_CHAIN); });
                var damageVal_1 = Math.round(data.sumDamage * WIRE_CHAIN.value * 0.01);
                arr.forEach(function (m) {
                    var val = m.hitPrepDamageHandle(0, damageVal_1).trueDamage;
                    var v = m.onHit(val, owners);
                    m.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: m.isDie(), time: data.time });
                });
            }
            // 英雄攻击后
            if (attackerHeroSkill) {
                // 关银屏 范围攻击
                if (attackerHeroSkill.id === Enums_1.HeroType.GUAN_YIN_PING && data.actDamage > 0 && !isBaseAttack) {
                    if (attacker.isHasBuff(Enums_1.BuffType.AROA)) {
                        var buff = attacker.getBuff(Enums_1.BuffType.DAMAGE_SUPERPOSITION);
                        if (buff && buff.value > 0) {
                            attacker.removeBuff(Enums_1.BuffType.AROA);
                            // 主要目标也要眩晕
                            if (!isDie) {
                                defender.addBuff(Enums_1.BuffType.DIZZINESS, attacker.getUid(), 1);
                            }
                            var damageVal_2 = Math.round(buff.value * (attackerHeroSkill.value * 0.01) * lastDamageAmend);
                            var arr = defender.getCanAttackPawnByRange(attacker.getCanAttackFighters(), 2, 4, defender.getUid());
                            arr.forEach(function (f) {
                                var val = f.hitPrepDamageHandle(0, damageVal_2).trueDamage;
                                var v = f.onHit(val, owners);
                                // 添加buff
                                f.addBuff(Enums_1.BuffType.DIZZINESS, attacker.getUid(), 1);
                                f.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: f.isDie(), time: data.time });
                            });
                            this.playBattleEffect([211001], defender.getPoint(), 'top');
                        }
                    }
                    else {
                        attacker.addBuff(Enums_1.BuffType.AROA, attacker.getUid(), 1);
                    }
                }
                // 杨妙真 识破恢复
                if (attackerHeroSkill.id === Enums_1.HeroType.YANG_MIAOZHEN) {
                    var buff = defender.getBuff(Enums_1.BuffType.PENETRATE);
                    if (buff) {
                        attackHeal += buff.value;
                        buff.value = 0;
                    }
                }
                // 辛弃疾 愁梦记录攻击次数
                if (attackerHeroSkill.id === Enums_1.HeroType.XIN_QIJI) {
                    var buff = attacker.getBuff(Enums_1.BuffType.SORROWFUL_DREAM);
                    if (!buff) {
                    }
                    else if (buff.value >= attackerHeroSkill.target - 1) {
                        buff.value = 0;
                        attacker.addBuffValue(Enums_1.BuffType.KERIAN, attacker.getUid(), attackerHeroSkill.value);
                        this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.WISDOM_COURAGE, attacker.getPoint());
                    }
                    else {
                        buff.value += 1;
                    }
                }
            }
            // 猩猩 狂怒
            if (attacker.checkTriggerBuff(Enums_1.BuffType.RAGE) && data.sumDamage > 0) {
                var arr = defender.getCanAttackPawnByRange(attacker.getCanAttackFighters(), 2, 8, defender.getUid());
                if (arr.length > 0) {
                    var extraDamge_3 = Math.round(data.sumDamage * 0.5);
                    arr.forEach(function (f) {
                        var damage = f.hitPrepDamageHandle(0, extraDamge_3).trueDamage;
                        var v = f.onHit(damage, [attacker.getOwner()]);
                        f.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: f.isDie() });
                    });
                }
                this.playBattleEffect([308001], defender.getPoint(), 'top');
            }
            // 韬略 攻击回血
            attackHeal += attacker.getStrategyValue(30102);
            // buff吸血
            var suckBloodBuff = attacker.getBuff(Enums_1.BuffType.HIT_SUCK_BLOOD);
            if (suckBloodBuff) {
                damageHealRatio += suckBloodBuff.value;
            }
            // 战斧
            var LOW_HP_ADD_SUCKBLOOD = attacker.getBuff(Enums_1.BuffType.LOW_HP_ADD_SUCKBLOOD);
            if (LOW_HP_ADD_SUCKBLOOD) {
                damageHealRatio += LOW_HP_ADD_SUCKBLOOD.value;
            }
            // 徐盛 庇护
            var PROTECTION_NIE = attacker.getBuff(Enums_1.BuffType.PROTECTION_NIE);
            if (PROTECTION_NIE) {
                damageHealRatio += PROTECTION_NIE.value;
            }
            // 斧骑韬略
            if (data.hitShield) {
                // 吸盾
                var v = Math.round(data.hitShield * attacker.getStrategyValue(32001) * 0.01);
                if (v > 0) {
                    var buff = attacker.getBuffOrAdd(Enums_1.BuffType.SUCK_SHIELD, attacker.getUid());
                    buff.value = Math.round(v * (1 + attacker.getStrategyValue(30601) * 0.01));
                }
                // 破盾 对周围目标造成伤害
                if (defender.getShieldVal() === 0) {
                    v = attacker.getStrategyValue(32002);
                    if (v > 0) {
                        var arr = defender.getCanAttackPawnByRange(attacker.getCanAttackFighters(), 1, 4, defender.getUid());
                        if (arr.length > 0) {
                            var damageVal_3 = Math.round(data.hitShield * v * 0.01);
                            arr.forEach(function (f) {
                                var val = f.hitPrepDamageHandle(0, damageVal_3).trueDamage;
                                var v = f.onHit(val, owners);
                                f.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: f.isDie(), time: data.time });
                            });
                        }
                    }
                }
            }
        }
        // 攻击方回血
        if (attacker.isPawn()) {
            if (damageHealRatio > 0 && !data.noHeal) {
                attackHeal += Math.round(data.sumDamage * damageHealRatio * 0.01);
            }
            if (attackHeal > 0) {
                var v = attacker.onHeal(attackHeal, suckbloodShield);
                attackerHeal += v;
                if (v > 0 && !data.noShowHeal) {
                    attacker.changeState(Enums_1.PawnState.HEAL, { val: v, time: data.time });
                }
            }
        }
        // 剑盾加攻击力 击杀山贼头目
        if (defender.isDie() && defender.isPawn() && defender.getId() === 4205 && attacker.getSkillByType(Enums_1.PawnSkillType.CADET)) {
            // 用服务器通知
        }
        // 曹操 给队友加士气
        if (defender.isPawn() && (attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.CAO_CAO) {
            var uid_4 = attacker.getUid();
            attacker.getCanAttackRangeFighter(this.fighters, 3, 20, '', function (m) { return m.getCamp() !== camp || m.isFlag(); }).forEach(function (m) {
                var buff = m.getBuffOrAdd(Enums_1.BuffType.MORALE, uid_4);
                if (buff.value < attackerHeroSkill.value) {
                    buff.value += 1;
                    m.updateMaxHpRecord(m.getMaxHp());
                    var v = m.onHeal(attackerHeroSkill.params); //回复血量
                    if (m.getUid() === uid_4) {
                        attackerHeal += v;
                    }
                    if (v > 0) {
                        m.changeState(Enums_1.PawnState.HEAL, { val: v, time: data.time });
                    }
                    _this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, m.getPoint());
                }
            });
        }
        // 防守方装备效果
        if (!defender.isPawn()) {
        }
        else if (defender.isDie()) {
            // 陌刀专属：击杀下次最高值
            var INSTABILITY_ATTACK = attacker.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
            if ((INSTABILITY_ATTACK === null || INSTABILITY_ATTACK === void 0 ? void 0 : INSTABILITY_ATTACK.intensifyType) === 1 && !attacker.isHasBuff(Enums_1.BuffType.STEADY_ATTACK)) {
                attacker.addBuff(Enums_1.BuffType.STEADY_ATTACK, attacker.getUid()).times = 1;
            }
            // 韬略 击杀加攻击力
            if (attacker.isHasStrategys(40104, 40305, 40403)) {
                var buff = attacker.getBuffOrAdd(Enums_1.BuffType.KILL_ADD_ATTACK, attacker.getUid());
                buff.value += 1;
                this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, attacker.getPoint());
            }
            // 韬略 击杀后回怒
            if (!isBaseAttack) {
                var v = attacker.getStrategyValue(50022);
                var val = attacker.addAnger(v);
                attacker.changeState(Enums_1.PawnState.ADD_ANGER, { val: val });
            }
        }
        else if (data.actDamage > 0) { //如果还活着
            // 徐晃 给目标添加破甲
            if ((attackerHeroSkill === null || attackerHeroSkill === void 0 ? void 0 : attackerHeroSkill.id) === Enums_1.HeroType.XU_HUANG) {
                var lv = ((_a = defender.getBuff(Enums_1.BuffType.ARMOR_PENETRATION)) === null || _a === void 0 ? void 0 : _a.lv) || 0;
                if (lv < attackerHeroSkill.params) {
                    var buff = defender.addBuff(Enums_1.BuffType.ARMOR_PENETRATION, attacker.getUid(), lv + 1);
                    buff.value = attackerHeroSkill.value;
                }
            }
            // 检测技能受击效果
            // 剑盾 专属效果 每受到一次伤害增加3%伤害
            var GRANAGE = defender.getSkillByType(Enums_1.PawnSkillType.SKILL_218);
            if (GRANAGE && data.actDamage > 0 && GRANAGE.intensifyType === 1) {
                var buff = defender.getBuff(Enums_1.BuffType.RODELERO_ADD_ATTACK) || defender.addBuff(Enums_1.BuffType.RODELERO_ADD_ATTACK, defender.getUid(), 1);
                buff.value += 3;
            }
            // 检测英雄技能受击效果
            if (!defenderHeroSkill) {
            }
            else if (defenderHeroSkill.id === Enums_1.HeroType.CAO_REN) { //曹仁 添加buff
                var buff = defender.getBuffOrAdd(Enums_1.BuffType.TOUGH, defender.getUid());
                if (buff.value < defenderHeroSkill.value) {
                    buff.value += 1;
                    heal += defender.onHeal(defenderHeroSkill.target);
                }
            }
            else if (defenderHeroSkill.id === Enums_1.HeroType.DIAN_WEI) { //典韦 添加buff
                var buff = defender.getBuffOrAdd(Enums_1.BuffType.COURAGEOUSLY, defender.getUid());
                if (buff.value < defenderHeroSkill.value) {
                    buff.value += 1;
                    this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, defender.getPoint());
                }
            }
            else if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.ZHAO_YUN) { //赵云 龙胆 加闪避
                var buff = defender.getBuffOrAdd(Enums_1.BuffType.ROYAL_BLUE_DODGE, defender.getUid());
                buff.value += defenderHeroSkill.params;
            }
            else if ((defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.MENG_HUO) { //孟获 再起 加伤害
                var buff = defender.getBuffOrAdd(Enums_1.BuffType.RECURRENCE, defender.getUid());
                if (buff.value < defenderHeroSkill.target) {
                    buff.value += 1;
                }
                else {
                    // 重新叠加
                    buff.value = 1;
                    // 回复生命
                    var v = Math.round((defender.getMaxHp() - defender.getCurHp()) * 0.1);
                    heal += defender.onHeal(v);
                }
            }
            // 韬略 受到真实伤害回血
            if (trueDamage) {
                var v = Math.round(trueDamage * defender.getStrategyValue(31601) * 0.01);
                heal += defender.onHeal(v); //回复血量
            }
            // 检测装备受击效果
            defender.getEquipEffects().forEach(function (m) {
                if (m.type === Enums_1.EquipEffectType.REDUCTION_INJURY) { //锁子甲
                    if (_this.random.chance(m.odds)) {
                        var buff = defender.getBuffOrAdd(Enums_1.BuffType.HAUBERK_DEFENSE, defender.getUid());
                        if (buff.value < Math.floor(defender.getMaxHp() * 0.02)) {
                            buff.value += 1;
                            _this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.DAMAGE_REDUCTION, defender.getPoint());
                        }
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.THE_INJURY) { //反伤
                    if (attacker.isPawn() && _this.random.chance(m.odds)) {
                        var value = 5 + Math.floor(defender.getMaxHp() * 0.02);
                        var val = attacker.hitPrepDamageHandle(0, value).trueDamage;
                        var v = attacker.onHit(val, [defender.getOwner()]);
                        attacker.changeState(Enums_1.PawnState.DEDUCT_HP, { trueDamage: v.damage, heal: v.heal, time: data.time });
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.SAME_CORPS_REDUCE_DMG) { //受到不同目标的攻击时 伤害递减 达到30%回复10%血量
                    var buff = defender.getBuff(Enums_1.BuffType.DAMAGE_DECREASE);
                    if (buff && buff.value >= 30 && !buff.provider.includes('x')) {
                        buff.provider = 'x|' + buff.provider;
                        heal += defender.onHeal(Math.round(defender.getMaxHp() * 0.1)); //回复血量
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.HIT_GET_SUCK_BLOOD) { //受击获得吸血
                    var buff = defender.getBuffOrAdd(Enums_1.BuffType.HIT_SUCK_BLOOD, defender.getUid());
                    if (buff.value < 60) {
                        buff.value = Math.min(buff.value + m.value, 60);
                    }
                }
                else if (m.type === Enums_1.EquipEffectType.BELT_BLOODRAGE) { //受击回怒
                    if (defender.isHasAnger() && _this.random.chance(m.odds)) {
                        var val = defender.addAnger(1);
                        defender.changeState(Enums_1.PawnState.ADD_ANGER, { val: val });
                    }
                }
            });
        }
        // 防守方反制效果
        if (attacker.isPawn()) {
            // 反伤
            var THE_INJURY = defender.getSkillByType(Enums_1.PawnSkillType.THE_INJURY);
            if (THE_INJURY) {
                var val = Math.round(data.actDamage * THE_INJURY.value);
                if (val > 0) {
                    val = attacker.hitPrepDamageHandle(0, val).trueDamage;
                    var v = attacker.onHit(val, [defender.getOwner()]);
                    attacker.changeState(Enums_1.PawnState.DEDUCT_HP, { trueDamage: v.damage, heal: v.heal, time: data.time });
                }
            }
            // 受到有只能是伤害 有几率使攻击者眩晕1回合
            if (trueDamage) {
                var DIZZY_HEART_DROP = defender.getEquipEffectByType(Enums_1.EquipEffectType.DIZZY_HEART_DROP);
                if (!!DIZZY_HEART_DROP && this.random.chance(DIZZY_HEART_DROP.odds)) {
                    attacker.addBuff(Enums_1.BuffType.DIZZINESS, defender.getUid(), 1);
                }
            }
            // 瘟疫感染
            var INFECTION_PLAGUE = defender.getSkillByType(Enums_1.PawnSkillType.INFECTION_PLAGUE);
            if (INFECTION_PLAGUE) {
                if (defender.getMinDis(attacker) <= INFECTION_PLAGUE.target) {
                    var buff = attacker.getBuffOrAdd(Enums_1.BuffType.INFECTION_PLAGUE, defender.getUid());
                    buff.value += INFECTION_PLAGUE.value;
                }
            }
        }
        // 先登 归0
        if (!defender.isBuild()) {
            (_b = attacker.getBuff(Enums_1.BuffType.PRESTAGE)) === null || _b === void 0 ? void 0 : _b.setValue(0);
        }
        if (GameHelper_1.gameHpr.isNoviceMode) {
            GameHelper_1.gameHpr.noviceServer.recordFighterData({ index: attacker.getAreaIndex(), attacker: attacker, defender: defender, heal: heal, attackerHeal: attackerHeal, data: data });
        }
        return { heal: heal, attackerHeal: attackerHeal };
    };
    // 阵亡时处理
    FSPBattleController.prototype.doDieAfter = function (defender) {
        var _this = this;
        var _a;
        var defenderUid = defender.getUid(), camp = defender.getCamp(), point = defender.getPoint();
        var GEN_FLAG_BY_DIE = defender.getEquipEffectByType(Enums_1.EquipEffectType.GEN_FLAG_BY_DIE);
        if (GEN_FLAG_BY_DIE) { //阵亡时掉落军旗
            var point_2 = defender.getPoint();
            if (!this.getFightersByPoint(point_2.x, point_2.y).some(function (m) { return m.isFlag(); })) { //该位置是否已经有军旗了
                var pawn = this.area.addBuildFlag({
                    uid: 'flag_' + defenderUid,
                    hp: [GEN_FLAG_BY_DIE.value, GEN_FLAG_BY_DIE.value],
                    point: point_2,
                    owner: defender.getOwner()
                });
                var fighter = new Fighter_1.default().init(pawn, {
                    camp: defender.getCamp(),
                    attackIndex: defender.getAttackIndex(),
                }, this);
                this.fighters.push(fighter);
                eventCenter.emit(EventType_1.default.ADD_PAWN, this.area.index, pawn);
                this.fighters.sort(function (a, b) { return a.attackIndex - b.attackIndex; }); //根据出手顺序排序
                fighter.checkBattleBeginTrigger();
                this.updateFighterPointMap();
            }
        }
        var defenderHeroSkill = defender.getPortrayalSkill();
        // 曹操
        var isCaoCao = (defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.CAO_CAO;
        // 高顺
        var isGaoShun = (defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.GAO_SHUN;
        // 于禁
        var isYuJin = (defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.YU_JIN;
        // 董卓
        var isDongZhuo = (defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.DONG_ZHUO;
        // 霍去病
        var isHuoQuBing = (defenderHeroSkill === null || defenderHeroSkill === void 0 ? void 0 : defenderHeroSkill.id) === Enums_1.HeroType.HUO_QUBING;
        // 宠物
        var petMaster = defender.getPetMaster();
        // 敌方阵亡加攻击 数量
        var addDdieAttackFighters = [];
        //
        this.fighters.forEach(function (m) {
            var uid = m.getUid();
            if (uid === defenderUid) {
                return;
            }
            else if (m.getCamp() === camp) {
                // 死的是否曹操 删除士气
                if (isCaoCao && m.removeBuffByProvider(Enums_1.BuffType.MORALE, defenderUid)) {
                    var maxHp = m.entity.getMaxHp();
                    m.entity.curHp = Math.min(m.entity.curHp, maxHp);
                    m.updateMaxHpRecord(maxHp);
                }
                // 死的是否高顺 删除陷阵效果
                if (isGaoShun && m.removeBuffByProvider(Enums_1.BuffType.BREAK_ENEMY_RANKS, defenderUid)) {
                    var maxHp = m.entity.getMaxHp();
                    m.entity.curHp = Math.min(m.entity.curHp, maxHp);
                    m.updateMaxHpRecord(maxHp);
                }
                // 死的是于禁 删除毅重效果
                if (isYuJin) {
                    m.removeBuffByProvider(Enums_1.BuffType.RESOLUTE, defenderUid);
                }
                // 死的是霍去病 删除回怒效果
                if (isHuoQuBing) {
                    m.removeBuffByProvider(Enums_1.BuffType.CHECK_JUMPSLASH_ADD_ANGER, defenderUid);
                }
                // 死的是宠物 记录喂养次数
                if (uid === petMaster) {
                    var val = defender.getBuffValue(Enums_1.BuffType.FEED_INTENSIFY);
                    if (val > 0) {
                        var buff = m.getBuffOrAdd(Enums_1.BuffType.FEED_INTENSIFY_RECORD, uid);
                        if (val > buff.value) {
                            buff.value = val;
                        }
                    }
                }
            }
            else {
                var dis = MapHelper_1.mapHelper.getPointToPointDis(point, m.getPoint());
                // 看周围是否有 李嗣业 ^^
                var heroSkill = m.getPortrayalSkill();
                if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.LI_SIYE && dis <= m.getAttackRange()) {
                    var buff = m.getBuffOrAdd(Enums_1.BuffType.VALOR, m.getUid());
                    if (buff.value < heroSkill.value) {
                        buff.value += 1;
                        _this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.VALOR, m.getPoint());
                    }
                }
                // 韬略 是否有加攻击
                var sb = m.getStrategyBuff(50003);
                if (sb && dis <= 1 && m.getBuffValue(Enums_1.BuffType.DDIE_ADD_ATTACK) < sb.params) {
                    addDdieAttackFighters.push(m);
                }
                // 死的是董卓 删除暴虐效果
                if (isDongZhuo) {
                    m.removeBuffByProvider(Enums_1.BuffType.TYRANNICAL, defenderUid);
                }
            }
        });
        // 鸩毒
        var POISONED_WINE = defender.getBuff(Enums_1.BuffType.POISONED_WINE);
        if (POISONED_WINE) {
            var camp_1 = defender.getCamp();
            var arr = defender.getCanAttackRangeFighter(this.getFighters(), 2, 8, defenderUid, function (m) { return m.getCamp() !== camp_1 || m.isFlag(); });
            if (arr.length > 0) {
                var damage_1 = Math.round(POISONED_WINE.value * (POISONED_WINE.lv + 9) * 0.01);
                arr.forEach(function (m) {
                    var val = m.hitPrepDamageHandle(0, damage_1).trueDamage;
                    var v = m.onHit(val, []);
                    m.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: m.isDie() });
                });
                (_a = this.currentFighter) === null || _a === void 0 ? void 0 : _a.addRoundEndDelayTime(500);
                this.playBattleEffect([210002], defender.getPoint(), 'top');
            }
        }
        // 敌方阵亡 我方攻击力+1
        var addDdieAttackFighterLen = addDdieAttackFighters.length;
        while (addDdieAttackFighterLen > 4) {
            addDdieAttackFighterLen -= 1;
            addDdieAttackFighters.splice(this.random.get(0, addDdieAttackFighterLen), 1);
        }
        addDdieAttackFighters.forEach(function (m) {
            m.getBuffOrAdd(Enums_1.BuffType.DDIE_ADD_ATTACK, m.getUid()).value += 1;
            _this.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.ATTACK, m.getPoint());
        });
    };
    // 获取以指定点位为移动目标的fighter
    FSPBattleController.prototype.getLockMovePointFighter = function (point) {
        var lockInfo = this.lockedMovePointMap.get(point.ID());
        var delLock = false;
        // 锁定该点的单位不存在、死亡或者被强制位移后 移除锁定
        if (lockInfo) {
            if (lockInfo.fighter) {
                if (lockInfo.fighter.isDie() || !cc.Vec2.equals(lockInfo.movePoint, lockInfo.fighter.getPoint())) {
                    delLock = true;
                }
                else if (lockInfo.fighter.getAttackTarget() && !cc.Vec2.equals(lockInfo.fighter.getPoint(), point) && lockInfo.fighter.checkInMyAttackRange(lockInfo.fighter.getAttackTarget())) {
                    // 锁定该点的单位未移动到锁定点位且已有攻击目标 移除锁定
                    delLock = true;
                }
            }
            else {
                delLock = true;
            }
        }
        if (delLock) {
            this.delLockMovePointFighter(point);
            return null;
        }
        return lockInfo;
    };
    // 设置以指定点位为移动目标的fighter
    FSPBattleController.prototype.setLockMovePointFighter = function (point, fighter, weight, movePoint) {
        // console.log('setLockMovePointFighter fighterUid: ' + fighter.getUid() + ' point: ' + point + ' weight: ' + weight + ' movePoint: ' + movePoint)
        this.lockedMovePointMap.set(point.ID(), { fighter: fighter, weight: weight, movePoint: movePoint });
        return true;
    };
    // 移除以指定点位为移动目标的fighter
    FSPBattleController.prototype.delLockMovePointFighter = function (point) {
        return !!point && this.lockedMovePointMap.delete(point.ID());
    };
    // 获取弓骑兵骑射移动路径
    FSPBattleController.prototype.getBowmanMovePaths = function (attacker) {
        var fighters = attacker.getCanAttackFighters().filter(function (m) { return (m.isPawn() || m.isFlag()) && !m.isDie(); });
        if (fighters.length === 0) {
            return {};
        }
        var fighter = attacker;
        var attackRange = attacker.getAttackRange();
        // 获取待移动的点
        var points = fighter.searchRange.search(fighter.getPoint(), 1);
        var target = null, weight = 0, point = null;
        var _loop_1 = function (i) {
            var np = points[i];
            if (this_1.checkHasFighter(np.x, np.y)) {
                return "continue";
            }
            var minDis = 0, tempW = 0, tempTarget = null;
            fighters.forEach(function (m) {
                var dis = MapHelper_1.mapHelper.getPointToPointDis(m.getPoint(), np);
                var w = dis * 10 + (9 - m.getAttackSpeed());
                w = w * 1000 + m.getAttackIndex();
                if (w < tempW || !tempTarget) {
                    tempW = w;
                    minDis = dis;
                    tempTarget = m;
                }
            });
            if (minDis > attackRange) {
                return "continue";
            }
            // let w = minDis <= attackRange ? 1 : 0
            // w = w * 1000 + minDis
            var w = minDis;
            if (w > weight && tempTarget) {
                weight = w;
                target = tempTarget.getUid();
                point = np.toJson();
            }
        };
        var this_1 = this;
        for (var i = 0; i < points.length; i++) {
            _loop_1(i);
        }
        return { target: target, point: point };
    };
    FSPBattleController.prototype.isCurrentRoundEnd = function () {
        var e_1, _a, e_2, _b;
        if (!this.currentFighter.isRoundEnd())
            return false;
        var sets = new Set();
        try {
            for (var _c = __values(this.fighters), _d = _c.next(); !_d.done; _d = _c.next()) {
                var fighter = _d.value;
                if (fighter.isDie())
                    return false;
                sets.add(fighter);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(this.lockedMovePointMap), _f = _e.next(); !_f.done; _f = _e.next()) {
                var _g = __read(_f.value, 2), key = _g[0], value = _g[1];
                if (!sets.has(value.fighter)) {
                    return false;
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return true;
    };
    // 播放战斗特效
    FSPBattleController.prototype.playBattleEffect = function (types, point, root, params) {
        var _a;
        (_a = this.area) === null || _a === void 0 ? void 0 : _a.playBattleEffect(types, point, root, params);
    };
    // 屏幕抖动
    FSPBattleController.prototype.playSceneShake = function (time) {
        eventCenter.emit(EventType_1.default.PLAY_BATTLE_SCENE_SHAKE, this.area.index, time);
    };
    // 播放音效
    FSPBattleController.prototype.playSFX = function (url, data) {
        if (this.area) {
            eventCenter.emit(EventType_1.default.PLAY_BATTLE_SFX, this.area.index, url, data);
        }
    };
    // 添加宠物士兵
    FSPBattleController.prototype.addPetPawn = function (uid, id, lv, target, camp, owner) {
        var point = target.clone();
        var pawn = this.area.addPetPawn({ uid: uid, id: id, lv: lv, point: point, owner: owner });
        var attackIndex = this.accAttackIndex + 1;
        var fighter = this.addFighters([{
                uid: uid, camp: camp,
                attackIndex: attackIndex,
                isPet: true,
            }])[0];
        eventCenter.emit(EventType_1.default.ADD_PAWN, this.area.index, pawn);
        this.playBattleEffect([214001], point, 'top');
        return fighter;
    };
    // 添加地面非战斗单位
    FSPBattleController.prototype.addNoncombat = function (uid, id, lv, target, camp, owner, params) {
        var _a, _b, _c;
        var point = target.clone();
        var pawn = this.area.addNoncombat({ uid: uid, id: id, lv: lv, point: point, owner: owner, enterDir: (_a = params === null || params === void 0 ? void 0 : params.dir) !== null && _a !== void 0 ? _a : 1 });
        pawn.curHp = ((_b = params === null || params === void 0 ? void 0 : params.hp) === null || _b === void 0 ? void 0 : _b[0]) || 0;
        pawn.maxHp = ((_c = params === null || params === void 0 ? void 0 : params.hp) === null || _c === void 0 ? void 0 : _c[1]) || 0;
        var attackIndex = this.accAttackIndex + 1;
        this.addFighters([{
                uid: uid, camp: camp,
                attackIndex: attackIndex,
            }]);
        eventCenter.emit(EventType_1.default.ADD_PAWN, this.area.index, pawn);
    };
    // 删除地面非战斗单位
    FSPBattleController.prototype.removeNoncombat = function (uid) {
        for (var i = this.fighters.length - 1; i >= 0; i--) {
            var f = this.fighters[i];
            if (f.getUid() === uid) {
                this.fighters.splice(i, 1);
                // 删除实际军队里面的 这里不发送事件到视图层 因为要做死亡动画
                this.area.removeBattleTempPawn(uid);
                return;
            }
        }
    };
    // 归1
    FSPBattleController.prototype.normalizeVec2 = function (point) {
        if (point.x === 0 && point.y === 0) {
            if (this.random.chance(50)) {
                point.x = this.random.chance(50) ? 1 : -1;
            }
            else {
                point.y = this.random.chance(50) ? 1 : -1;
            }
        }
        else if (point.x !== 0 && point.y !== 0) {
            if (this.random.chance(50)) {
                point.x = 0;
            }
            else {
                point.y = 0;
            }
        }
        point.x = ut.normalizeNumber(point.x);
        point.y = ut.normalizeNumber(point.y);
        return point;
    };
    return FSPBattleController;
}());
exports.default = FSPBattleController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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