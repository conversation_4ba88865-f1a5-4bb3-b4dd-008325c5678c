
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/ResHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a19724Y6I5IRa5AlxX2Mhvc', 'ResHelper');
// app/script/common/helper/ResHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resHelper = void 0;
var Constant_1 = require("../constant/Constant");
var OutlineShaderCtrl_1 = require("../shader/OutlineShaderCtrl");
var GameHelper_1 = require("./GameHelper");
/**
 * 游戏中的资源相关帮助方法
 */
var ResHelper = /** @class */ (function () {
    function ResHelper() {
        this.lands = {};
        this.landMap = {};
        this.seawavePrefabs = {};
        this.cityPrefabs = {};
        this.mapFlagNumbers = {};
        this.spriteDefaultMaterial = null;
        this.sprite2dGrayMaterial = null;
    }
    ResHelper.prototype.init = function (progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var sfs, pfbs;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        this.lands = {};
                        assetsMgr.debug = false;
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.lands[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('seawave', cc.Prefab, '_seawave_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 2:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.seawavePrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('city', cc.Prefab, '_city_prefab_', function (done, total) { return progessCallback(done / total); })];
                    case 3:
                        pfbs = _a.sent();
                        pfbs.forEach(function (m) { return _this.cityPrefabs[m.name] = m; });
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('map_flag_num', cc.SpriteFrame, '_land_res_', function (done, total) { return progessCallback(done / total); })];
                    case 4:
                        // 地图标记数字
                        sfs = _a.sent();
                        sfs.forEach(function (m) { return _this.mapFlagNumbers[m.name] = m; });
                        assetsMgr.debug = true;
                        // 材质
                        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite');
                        this.sprite2dGrayMaterial = cc.Material.getBuiltinMaterial('2d-gray-sprite');
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.initNovice = function (key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, assetsMgr.loadTempRes('other/CITY_10010', cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        this.cityPrefabs[pfb.name] = pfb;
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanNovice = function () {
        delete this.cityPrefabs['CITY_10010'];
    };
    ResHelper.prototype.getPawnName = function (id) { return 'pawnText.name_' + id; };
    ResHelper.prototype.checkLandSkin = function (type) {
        return !!this.landMap[type];
    };
    // 初始化地块皮肤
    ResHelper.prototype.initLandSkin = function (type, progessCallback) {
        return __awaiter(this, void 0, void 0, function () {
            var obj, sfs;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        obj = this.landMap[type];
                        if (obj) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, assetsMgr.loadTempRseDir('land_' + type, cc.SpriteFrame, '_land_res_' + type, function (done, total) { return progessCallback && progessCallback(done / total); })];
                    case 1:
                        sfs = _a.sent();
                        obj = this.landMap[type] = {};
                        sfs.forEach(function (m) { return obj[m.name] = m; });
                        return [2 /*return*/];
                }
            });
        });
    };
    ResHelper.prototype.cleanLandSkin = function () {
        var type = GameHelper_1.gameHpr.world.getSeason().type;
        for (var k in this.landMap) {
            if (Number(k) !== type) {
                delete this.landMap[k];
                assetsMgr.releaseTempResByTag('_land_res_' + k);
            }
        }
    };
    // 根据下标获取节点
    ResHelper.prototype.getNodeByIndex = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position);
        return it;
    };
    ResHelper.prototype.getNodeByIndex2 = function (node, i, position) {
        var it = node.children[i] || cc.instantiate2(node.children[0], node);
        it.active = true;
        it.Data = null;
        it.setPosition(position.x, position.y - Constant_1.TILE_SIZE_HALF.y);
        return it;
    };
    // 隐藏多于的
    ResHelper.prototype.hideNodeByIndex = function (node, idx) {
        for (var i = idx, l = node.childrenCount; i < l; i++) {
            var it = node.children[i];
            it.active = false;
            it.Data = null;
        }
    };
    // 清理子节点只剩1个
    ResHelper.prototype.cleanNodeChildren = function (node) {
        var _a;
        for (var i = node.childrenCount - 1; i >= 1; i--) {
            node.children[i].destroy();
        }
        (_a = node.children[0]) === null || _a === void 0 ? void 0 : _a.setActive(false);
    };
    // 获取sprite材质
    ResHelper.prototype.get2dSpriteMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : this.sprite2dGrayMaterial;
    };
    // 纯色灰材质
    ResHelper.prototype.getSpriteColorGrayMaterial = function (unlock) {
        return unlock ? this.spriteDefaultMaterial : assetsMgr.getMaterial('SpriteColorGrey');
    };
    // 地面icon
    ResHelper.prototype.getLandIcon = function (icon) {
        if (icon.startsWith('land_')) {
            return this.getLandItemIcon(icon, GameHelper_1.gameHpr.world.getSeasonType());
        }
        return this.lands[icon];
    };
    // 地面资源icon
    ResHelper.prototype.getLandItemIcon = function (icon, type) {
        var _a;
        return ((_a = this.landMap[type]) === null || _a === void 0 ? void 0 : _a[icon]) || this.lands[icon] || null;
    };
    // 获取海浪预制体
    ResHelper.prototype.getSeawavePrefab = function (id) {
        return this.seawavePrefabs['SEAWAVE_' + id];
    };
    // 获取城市预制体
    ResHelper.prototype.getCityPrefab = function (id) {
        return this.cityPrefabs['CITY_' + id];
    };
    // 获取资源icon
    ResHelper.prototype.getResIcon = function (type) {
        return assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
    };
    // 获取地图标记数字
    ResHelper.prototype.getMapFlagNumber = function (flag) {
        var key = flag <= 0 ? 'x' : (flag - 1);
        return this.mapFlagNumbers['map_flag_' + key];
    };
    // 加载icon
    ResHelper.prototype.loadIcon = function (url, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var spr, sf;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!url || !icon || !icon.isValid) {
                            return [2 /*return*/, null];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/, null];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                        }
                        spr['__load_icon_url'] = url;
                        return [4 /*yield*/, assetsMgr.loadTempRes(url, cc.SpriteFrame, key)];
                    case 1:
                        sf = _a.sent();
                        if (spr.isValid && spr['__load_icon_url'] === url) {
                            spr.spriteFrame = sf || null;
                        }
                        return [2 /*return*/, spr];
                }
            });
        });
    };
    // 加载设施icon
    ResHelper.prototype.loadBuildIcon = function (url, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('build/' + url, icon, key)];
            });
        });
    };
    // 加载城市icon
    ResHelper.prototype.loadCityIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('city/city_' + id, icon, key)];
            });
        });
    };
    // 加载遗迹buffIcon
    ResHelper.prototype.loadAncientBuffIcon = function (type, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('ancient_buff/tb_yiji_buff_' + type, icon, key)];
            });
        });
    };
    // 加载士兵头像icon
    ResHelper.prototype.loadPawnHeadIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (id === 3405104) { //牛仔隐藏款 特殊处理
                    return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_00", icon, key, setEmpty)];
                }
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id + "_01", icon, key, setEmpty)];
            });
        });
    };
    // 加载士兵小头像icon
    ResHelper.prototype.loadPawnHeadMiniIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon("role/" + id + "/role_" + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载技能图标
    ResHelper.prototype.loadSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('skill/skill_' + id, icon, key)];
            });
        });
    };
    // 加载装备icon
    ResHelper.prototype.loadEquipIcon = function (id, icon, key, smeltLv) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var spr, outline;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.loadIcon('equip/equip_' + id, icon, key)];
                    case 1:
                        spr = _b.sent();
                        if (!spr || !spr.isValid) {
                        }
                        else if (smeltLv) {
                            outline = spr.getComponent(OutlineShaderCtrl_1.default) || spr.addComponent(OutlineShaderCtrl_1.default);
                            outline.setTarget(spr);
                            outline.setOutlineSize(2);
                            outline.setColor(ut.colorFromHEX(smeltLv === 1 ? '#58F1FF' : '#E488FF'));
                            outline.setVisible(true);
                            // spr.node.adaptScale(size, cc.size(size.width + 8, size.height + 8))
                        }
                        else {
                            (_a = spr.getComponent(OutlineShaderCtrl_1.default)) === null || _a === void 0 ? void 0 : _a.setVisible(false);
                            // spr.node.scale = 1
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载政策icon
    ResHelper.prototype.loadPolicyIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('policy/policy_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载buff icon
    ResHelper.prototype.loadBuffIcon = function (id, icon, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('buff_icon/buff_' + id, icon, key, setEmpty)];
            });
        });
    };
    // 加载联盟图标
    ResHelper.prototype.loadAlliIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('alli_icon/alli_icon_' + id, icon, key)];
            });
        });
    };
    // 加载评分图标
    ResHelper.prototype.loadRankScoreIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('rank_icon/rank_icon_' + (id >= 0 ? id : 'none'), icon, key)];
            });
        });
    };
    // 加载礼物图标
    ResHelper.prototype.loadGiftIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('gift/gift_' + id, icon, key)];
            });
        });
    };
    // 加载表情
    ResHelper.prototype.loadEmojiIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('emoji/emoji_' + id, icon, key)];
            });
        });
    };
    // 加载画像
    ResHelper.prototype.loadPortrayalImage = function (id, icon, key) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_b) {
                if ((_a = assetsMgr.getJsonData('portrayalBase', id)) === null || _a === void 0 ? void 0 : _a.has_anim) {
                    return [2 /*return*/, this.loadIcon("portrayal_anim/" + id + "/portrayal_" + id + "_01", icon, key)];
                }
                return [2 /*return*/, this.loadIcon('portrayal/portrayal_' + id, icon, key)];
            });
        });
    };
    // 加载残卷遮挡
    ResHelper.prototype.loadPortrayalDebrisMask = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('portrayal/pd_mask_' + id, icon, key)];
            });
        });
    };
    // 加载英雄技能图标
    ResHelper.prototype.loadHeroSkillIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('hero_skill_icon/hero_skill_' + id, icon, key)];
            });
        });
    };
    // 加载英雄预制体
    ResHelper.prototype.loadHeroMarchPrefab = function (id, key) {
        return __awaiter(this, void 0, void 0, function () {
            var pfbName, pfb;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        pfbName = 'ROLE_' + id;
                        return [4 /*yield*/, assetsMgr.loadTempRes('march/' + pfbName, cc.Prefab, key)];
                    case 1:
                        pfb = _a.sent();
                        if ((pfb === null || pfb === void 0 ? void 0 : pfb.name) !== pfbName) {
                            return [2 /*return*/, null];
                        }
                        return [2 /*return*/, pfb];
                }
            });
        });
    };
    // 加载植物种子图标
    ResHelper.prototype.loadBotanySeedIcon = function (id, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('botany_seed/botany_seed_' + id, icon, key)];
            });
        });
    };
    // 加载称号图标
    ResHelper.prototype.loadTitleIcon = function (icon, quality, url, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                // 加载底图品质
                this.loadIcon('title_icon/title_quality_' + quality, icon, key);
                // 加载图标
                this.loadIcon('title_icon/' + url, icon.Child('val'), key);
                return [2 /*return*/];
            });
        });
    };
    // 加载称号小图标
    ResHelper.prototype.loadTitleMiniIcon = function (url, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('title_icon/' + url, icon, key)];
            });
        });
    };
    // 加载活动banner
    ResHelper.prototype.loadActivtiesBanner = function (url, icon, key) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.loadIcon('activities/' + url, icon, key)];
            });
        });
    };
    // 加载表情节点
    ResHelper.prototype.loadEmojiNode = function (id, root, scale, key, setEmpty) {
        if (setEmpty === void 0) { setEmpty = true; }
        return __awaiter(this, void 0, void 0, function () {
            var node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!root || !id) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            root.removeAllChildren();
                        }
                        return [4 /*yield*/, nodePoolMgr.get('emoji/EMOJI_' + id, key)];
                    case 1:
                        node = _a.sent();
                        if (node && root.isValid) {
                            node.parent = root;
                            node.active = true;
                            node.setPosition(0, 0);
                            node.scaleX = scale || 1;
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    // 加载玩家头像
    ResHelper.prototype.loadPlayerHead = function (icon, url, key, setEmpty) {
        return __awaiter(this, void 0, void 0, function () {
            var spr, val, node;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!(icon === null || icon === void 0 ? void 0 : icon.isValid)) {
                            return [2 /*return*/];
                        }
                        spr = icon instanceof cc.Sprite ? icon : icon.Component(cc.Sprite);
                        if (!spr) {
                            return [2 /*return*/];
                        }
                        else if (setEmpty) {
                            spr.spriteFrame = null;
                            spr.node.removeAllChildren();
                        }
                        url = spr['_player_head_icon_'] = url || 'head_icon_free_001';
                        val = null;
                        if (!url.startsWith('head_icon_anim_')) return [3 /*break*/, 2];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.Prefab, key)];
                    case 1:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 2:
                        if (!url.startsWith('head_icon_')) return [3 /*break*/, 4];
                        return [4 /*yield*/, assetsMgr.loadTempRes('headicon/' + url, cc.SpriteFrame, key)];
                    case 3:
                        val = _a.sent();
                        return [3 /*break*/, 6];
                    case 4: return [4 /*yield*/, assetsMgr.loadRemote(url, '.jpg', key || '_player_head_')];
                    case 5:
                        val = _a.sent();
                        _a.label = 6;
                    case 6:
                        if (spr.isValid && spr['_player_head_icon_'] === url) {
                            spr.node.removeAllChildren();
                            if (!val) {
                                spr.spriteFrame = null;
                            }
                            else if (val instanceof cc.Prefab) {
                                spr.spriteFrame = null;
                                node = cc.instantiate2(val, spr.node);
                                node.setContentSize(spr.node.width * spr.node.scaleX, spr.node.height * spr.node.scaleY);
                            }
                            else if (val instanceof cc.SpriteFrame) {
                                spr.spriteFrame = val;
                            }
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    return ResHelper;
}());
exports.resHelper = new ResHelper();
if (cc.sys.isBrowser) {
    window['resHelper'] = exports.resHelper;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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