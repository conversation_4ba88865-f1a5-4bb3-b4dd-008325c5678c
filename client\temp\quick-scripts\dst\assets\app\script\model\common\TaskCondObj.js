
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/TaskCondObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'bc827KCrEFGMb5gV1uLTGIr', 'TaskCondObj');
// app/script/model/common/TaskCondObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
// 任务条件类型
var TaskCondObj = /** @class */ (function () {
    function TaskCondObj() {
        this.id = 0;
        this.count = 0;
        this.progress = 0;
    }
    TaskCondObj.prototype.init = function (type, id, count) {
        this.type = type;
        this.id = id;
        this.count = count;
        return this;
    };
    TaskCondObj.prototype.fromString = function (val) {
        var _a = __read(val.split(',').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }), 3), type = _a[0], id = _a[1], count = _a[2];
        this.type = Number(type) || Enums_1.TCType.NONE;
        this.id = Number(id) || 0;
        this.count = Number(count) || 0;
        return this;
    };
    TaskCondObj.prototype.isComplete = function () {
        return this.progress >= this.count;
    };
    TaskCondObj.prototype.updateProgress = function (val) {
        this.progress = val;
    };
    return TaskCondObj;
}());
exports.default = TaskCondObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxjb21tb25cXFRhc2tDb25kT2JqLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHFEQUFvRDtBQUVwRCxTQUFTO0FBQ1Q7SUFBQTtRQUdXLE9BQUUsR0FBVyxDQUFDLENBQUE7UUFDZCxVQUFLLEdBQVcsQ0FBQyxDQUFBO1FBQ2pCLGFBQVEsR0FBVyxDQUFDLENBQUE7SUF3Qi9CLENBQUM7SUF0QlUsMEJBQUksR0FBWCxVQUFZLElBQVksRUFBRSxFQUFVLEVBQUUsS0FBYTtRQUMvQyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQTtRQUNoQixJQUFJLENBQUMsRUFBRSxHQUFHLEVBQUUsQ0FBQTtRQUNaLElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFBO1FBQ2xCLE9BQU8sSUFBSSxDQUFBO0lBQ2YsQ0FBQztJQUVNLGdDQUFVLEdBQWpCLFVBQWtCLEdBQVc7UUFDbkIsSUFBQSxLQUFBLE9BQW9CLEdBQUcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFSLENBQVEsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxDQUFDLEVBQUgsQ0FBRyxDQUFDLElBQUEsRUFBckUsSUFBSSxRQUFBLEVBQUUsRUFBRSxRQUFBLEVBQUUsS0FBSyxRQUFzRCxDQUFBO1FBQzVFLElBQUksQ0FBQyxJQUFJLEdBQUcsTUFBTSxDQUFDLElBQUksQ0FBQyxJQUFJLGNBQU0sQ0FBQyxJQUFJLENBQUE7UUFDdkMsSUFBSSxDQUFDLEVBQUUsR0FBRyxNQUFNLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFBO1FBQ3pCLElBQUksQ0FBQyxLQUFLLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQTtRQUMvQixPQUFPLElBQUksQ0FBQTtJQUNmLENBQUM7SUFFTSxnQ0FBVSxHQUFqQjtRQUNJLE9BQU8sSUFBSSxDQUFDLFFBQVEsSUFBSSxJQUFJLENBQUMsS0FBSyxDQUFBO0lBQ3RDLENBQUM7SUFFTSxvQ0FBYyxHQUFyQixVQUFzQixHQUFXO1FBQzdCLElBQUksQ0FBQyxRQUFRLEdBQUcsR0FBRyxDQUFBO0lBQ3ZCLENBQUM7SUFDTCxrQkFBQztBQUFELENBN0JBLEFBNkJDLElBQUEiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUQ1R5cGUgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2NvbnN0YW50L0VudW1zXCJcclxuXHJcbi8vIOS7u+WKoeadoeS7tuexu+Wei1xyXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBUYXNrQ29uZE9iaiB7XHJcblxyXG4gICAgcHVibGljIHR5cGU6IFRDVHlwZVxyXG4gICAgcHVibGljIGlkOiBudW1iZXIgPSAwXHJcbiAgICBwdWJsaWMgY291bnQ6IG51bWJlciA9IDBcclxuICAgIHB1YmxpYyBwcm9ncmVzczogbnVtYmVyID0gMFxyXG5cclxuICAgIHB1YmxpYyBpbml0KHR5cGU6IFRDVHlwZSwgaWQ6IG51bWJlciwgY291bnQ6IG51bWJlcikge1xyXG4gICAgICAgIHRoaXMudHlwZSA9IHR5cGVcclxuICAgICAgICB0aGlzLmlkID0gaWRcclxuICAgICAgICB0aGlzLmNvdW50ID0gY291bnRcclxuICAgICAgICByZXR1cm4gdGhpc1xyXG4gICAgfVxyXG5cclxuICAgIHB1YmxpYyBmcm9tU3RyaW5nKHZhbDogc3RyaW5nKSB7XHJcbiAgICAgICAgY29uc3QgW3R5cGUsIGlkLCBjb3VudF0gPSB2YWwuc3BsaXQoJywnKS5tYXAobSA9PiBtLnRyaW0oKSkuZmlsdGVyKG0gPT4gISFtKVxyXG4gICAgICAgIHRoaXMudHlwZSA9IE51bWJlcih0eXBlKSB8fCBUQ1R5cGUuTk9ORVxyXG4gICAgICAgIHRoaXMuaWQgPSBOdW1iZXIoaWQpIHx8IDBcclxuICAgICAgICB0aGlzLmNvdW50ID0gTnVtYmVyKGNvdW50KSB8fCAwXHJcbiAgICAgICAgcmV0dXJuIHRoaXNcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgaXNDb21wbGV0ZSgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5wcm9ncmVzcyA+PSB0aGlzLmNvdW50XHJcbiAgICB9XHJcblxyXG4gICAgcHVibGljIHVwZGF0ZVByb2dyZXNzKHZhbDogbnVtYmVyKSB7XHJcbiAgICAgICAgdGhpcy5wcm9ncmVzcyA9IHZhbFxyXG4gICAgfVxyXG59Il19