
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/area/PawnObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'e3304Wt8+VDip92tR5grFfv', 'PawnObj');
// app/script/model/area/PawnObj.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ErrorReportHelper_1 = require("../../common/helper/ErrorReportHelper");
var GameHelper_1 = require("../../common/helper/GameHelper");
var PortrayalInfo_1 = require("../common/PortrayalInfo");
var EquipInfo_1 = require("../main/EquipInfo");
var BuffObj_1 = require("./BuffObj");
var PawnSkillObj_1 = require("./PawnSkillObj");
var PawnStateObj_1 = require("./PawnStateObj");
// 一个士兵
var PawnObj = /** @class */ (function () {
    function PawnObj() {
        this.aIndex = 0; //所属哪个区域
        this.enterDir = -1;
        this.uid = '';
        this.cuid = ''; //客户端uid
        this.armyUid = ''; //所属队伍uid
        this.armyName = ''; //军队名字
        this.owner = '';
        this.recordDataMap = {}; //记录
        this.treasures = []; //当前宝箱个数
        this.id = 0;
        this.point = cc.v2();
        this.lv = 0; //等级
        this.skinId = 0; //皮肤id
        this.curHp = 0; //当前血量
        this.maxHp = 0;
        this.curAnger = 0; //怒气
        this.maxAnger = 0;
        this.attack = 0;
        this.attackRange = 0; //攻击范围
        this.moveRange = 0; //移动范围
        this.attackSpeed = 0; //当前出手速度
        this.equip = null; //当前携带的装备
        this.portrayal = null; //携带的画像
        this.rodeleroCadetLv = 0; //当前 见习勇者 层数
        this.petId = 0; //携带的宠物
        this.state = null; //当前状态
        this.skills = []; //技能列表
        this.actioning = false; //是否行动中
        this.buffs = []; //当前的buff列表
        this.strategyBuffMap = {}; //韬略map
        this.attrId = 0;
        this.baseJson = null;
        this.attrJson = null;
        this.upCost = []; //升级费用
        this.upTime = 0; //升级需要的时间
        this.tempCurrHp = -1; //用于记录
        this.tempAttackAnimTimes = null; //攻击动画时间
        this.cuid = ut.UID();
    }
    PawnObj.prototype.toString = function () {
        return "uid:" + this.uid + ", point:" + this.point.Join(",") + ", hp:" + this.curHp + "/" + this.getMaxHp() + ", attack:" + this.attack + ", attackRange:" + this.attackRange + ", moveRange:" + this.moveRange;
    };
    PawnObj.prototype.init = function (id, equip, lv, skinId, rodeleroCadetLv) {
        var _a;
        this.id = id;
        this.lv = lv || 1;
        this.skinId = skinId || 0;
        this.equip = new EquipInfo_1.default().fromSvr(equip || { id: 0, attrs: [] });
        this.rodeleroCadetLv = rodeleroCadetLv !== null && rodeleroCadetLv !== void 0 ? rodeleroCadetLv : (this.id === 3205 ? (((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0) : 0);
        this.tempCurrHp = -1;
        this.initJson();
        this.curHp = this.maxHp;
        return this;
    };
    PawnObj.prototype.fromSvr = function (data, auid, owner, aname) {
        var _a, _b;
        // cc.log(data)
        this.aIndex = data.index;
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv || 1;
        this.skinId = data.skinId || 0;
        this.point.set(data.point);
        this.armyUid = auid;
        this.attackSpeed = data.attackSpeed || 0;
        this.equip = new EquipInfo_1.default().fromSvr(data.equip || { id: 0, attrs: [] });
        this.portrayal = data.portrayal ? new PortrayalInfo_1.default().fromSvr(data.portrayal) : null;
        this.rodeleroCadetLv = data.rodeleroCadetLv || 0;
        this.petId = data.petId || 0;
        this.armyName = aname || '';
        this.owner = owner;
        this.recordDataMap = data.recordDataMap || {};
        this.tempCurrHp = -1;
        this.updateTreasures(data.treasures);
        this.setBuffs(data.buffs || []);
        this.strategyBuffMap = {};
        this.initJson(data.isFight);
        this.curHp = (_b = (_a = data.hp) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : this.maxHp;
        this.curAnger = data.curAnger;
        if (!this.curAnger && !this.isBattleing()) {
            this.initAnger();
        }
        return this;
    };
    PawnObj.prototype.strip = function () {
        var _a;
        return {
            index: this.aIndex,
            armyUid: this.armyUid,
            uid: this.uid,
            skinId: this.skinId,
            point: this.point.toJson(),
            id: this.id,
            lv: this.lv,
            curAnger: this.curAnger,
            attackSpeed: this.attackSpeed,
            equip: this.equip.strip(),
            portrayal: (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.strip(),
            rodeleroCadetLv: this.rodeleroCadetLv,
            petId: this.petId,
            treasures: ut.deepClone(this.treasures),
            hp: [this.curHp, this.maxHp],
            buffs: this.buffs.map(function (m) { return m.strip(); }),
            recordDataMap: ut.deepClone(this.recordDataMap)
        };
    };
    Object.defineProperty(PawnObj.prototype, "index", {
        get: function () { return this.aIndex; },
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.initJson = function (isFight) {
        var _a;
        this.baseJson = assetsMgr.getJsonData('pawnBase', this.id);
        this.attackSpeed = this.attackSpeed || (((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.attack_speed) || 9);
        this.changeState(isFight ? Enums_1.PawnState.STAND : Enums_1.PawnState.NONE, null, true);
        this.updateAttrJson();
    };
    // 刷新属性json
    PawnObj.prototype.updateAttrJson = function () {
        var _a, _b;
        if (this.isMachine()) {
            this.lv = 1;
        }
        this.attrId = this.id * 1000 + this.lv;
        this.attrJson = assetsMgr.getJsonData('pawnAttr', this.attrId);
        if (!this.attrJson) {
            return ErrorReportHelper_1.errorReportHelper.reportError('PawnObj.updateAttrJson', { id: this.id, lv: this.lv });
        }
        this.maxHp = this.attrJson.hp || 0;
        this.maxAnger = this.attrJson.anger || 0;
        this.attack = this.attrJson.attack || 0;
        this.attackRange = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_range) || 0;
        this.moveRange = ((_b = this.attrJson) === null || _b === void 0 ? void 0 : _b.move_range) || 0;
        this.skills = ut.stringToNumbers(this.attrJson.skill).map(function (m) { return new PawnSkillObj_1.default().init(m); });
        this.upCost = GameHelper_1.gameHpr.getPawnCost(this.id, this.lv, this.attrJson);
        this.upTime = GameHelper_1.gameHpr.getPawnCostTime(this.id, this.lv, this.attrJson);
        this.updateAttr(true);
    };
    // 获取攻击动画时间
    PawnObj.prototype.getAttackAnimTimes = function () {
        var _a, _b, _c;
        if (!this.tempAttackAnimTimes) {
            var attackAnimTimeStr = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.attack_anim_time) || '';
            if (this.isHero()) {
                attackAnimTimeStr = ((_b = assetsMgr.getJsonData('portrayalBase', this.portrayal.id)) === null || _b === void 0 ? void 0 : _b.attack_anim_time) || attackAnimTimeStr;
            }
            else if (this.skinId > 0) {
                attackAnimTimeStr = ((_c = assetsMgr.getJsonData('pawnSkin', this.skinId)) === null || _c === void 0 ? void 0 : _c.attack_anim_time) || attackAnimTimeStr;
            }
            this.tempAttackAnimTimes = attackAnimTimeStr.split('|').map(function (m) { return ut.stringToNumbers(m, ','); });
        }
        return this.tempAttackAnimTimes;
    };
    // 记录当前的血量 用于打开士兵面板时 切换装备的时候可以记录一开始的血量
    PawnObj.prototype.recordCurrHp = function (val) {
        this.tempCurrHp = val ? this.curHp : -1;
    };
    PawnObj.prototype.getViewId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || this.skinId || this.id; };
    PawnObj.prototype.getPrefabUrl = function () { return 'pawn/PAWN_' + this.getViewId(); };
    PawnObj.prototype.getUid = function () { return this.uid; };
    PawnObj.prototype.getAbsUid = function () { return this.uid + this.getViewId(); };
    PawnObj.prototype.getPoint = function () { return this.point; };
    PawnObj.prototype.setPoint = function (point) { this.point.set(point); };
    PawnObj.prototype.getPawnType = function () { return this.type; };
    PawnObj.prototype.getPortrayalSkill = function () { var _a; return (_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.skill; };
    PawnObj.prototype.getPortrayalId = function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) || 0; };
    Object.defineProperty(PawnObj.prototype, "name", {
        get: function () { var _a; return ((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.getChatName()) || 'pawnText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "type", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.type) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "typeName", {
        get: function () { return this.type ? 'ui.pawn_type_' + this.type : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "marchSpeed", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.march_speed) || 0; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "cerealCost", {
        get: function () { var _a; return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.cereal_cost) || 1; } //粮耗
        ,
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(PawnObj.prototype, "behaviorId", {
        get: function () { var _a; return (_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.behavior_id; } //行为树配置id
        ,
        enumerable: false,
        configurable: true
    });
    PawnObj.prototype.getAttackRange = function () {
        return this.attackRange + this.getStrategyValue(40101) + this.getStrategyValue(40302) + this.getStrategyValue(50013);
    };
    PawnObj.prototype.getMoveRange = function () {
        return this.moveRange + this.getStrategyValue(31901);
    };
    // 获取移动速度 每秒移动距离
    PawnObj.prototype.getMoveVelocity = function () {
        var _a;
        if (this.portrayal) {
            return this.portrayal.moveVelocity;
        }
        return ((_a = this.baseJson) === null || _a === void 0 ? void 0 : _a.velocity) || 100;
    };
    PawnObj.prototype.getAttackText = function () {
        var attack = this.amendAttack(this.attack) + '';
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (skill) {
            var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
            return attack + '-' + this.amendAttack(maxAttack);
        }
        return attack;
    };
    PawnObj.prototype.getAttackTextByIndex = function (index) {
        var minAttack = this.amendAttack(this.attack);
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return minAttack + '';
        }
        var maxAttack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        maxAttack = this.amendAttack(maxAttack);
        minAttack += (Math.round((maxAttack - minAttack) / 3) * index + 1);
        return minAttack + '-' + maxAttack;
    };
    // 获取最大攻击力 目前只用于陌刀
    PawnObj.prototype.getInstabilityMaxAttack = function () {
        var skill = this.getSkillByType(Enums_1.PawnSkillType.INSTABILITY_ATTACK);
        if (!skill) {
            return this.attack;
        }
        var attack = skill.value + Math.max(0, this.attack - this.attrJson.attack);
        return this.amendAttack(attack);
    };
    PawnObj.prototype.amendAttack = function (attack, ignoreBuffType) {
        var _this = this;
        var addAttackRatio = 0, lowAttackRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c, _d, _e;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.ATTACK_HALO || m.type === Enums_1.BuffType.LV_1_POWER || m.type === Enums_1.BuffType.LONGYUAN_SWORD_ATTACK) {
                addAttackRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK || m.type === Enums_1.BuffType.KUROU_ADD_ATTACK) {
                addAttackRatio += m.value; //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_ATTACK || m.type === Enums_1.BuffType.INSPIRE || m.type === Enums_1.BuffType.INSPIRE_001 || m.type === Enums_1.BuffType.WORTHY_MONARCH || m.type === Enums_1.BuffType.ADD_EXECUTE_ATTACK || m.type === Enums_1.BuffType.KILL_ADD_ATTACK || m.type === Enums_1.BuffType.GOD_WAR || m.type === Enums_1.BuffType.DDIE_ADD_ATTACK) {
                attack += m.value; //增加攻击力
            }
            else if (m.type === Enums_1.BuffType.WISDOM_COURAGE) { //姜维 智勇
                if (((_b = (_a = _this.portrayal) === null || _a === void 0 ? void 0 : _a.skill) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.JIANG_WEI) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.VALOR) { //李嗣业 勇猛
                if (((_d = (_c = _this.portrayal) === null || _c === void 0 ? void 0 : _c.skill) === null || _d === void 0 ? void 0 : _d.id) === Enums_1.HeroType.LI_SIYE) {
                    attack += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_e = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _e === void 0 ? void 0 : _e.target) || 0;
                attack += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TIGER_MANIA) { //许褚 虎痴
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.DESTROY_WEAPONS) { //摧毁武器 降低攻击力
                lowAttackRatio += m.value;
            }
            else if (m.type === Enums_1.BuffType.LOW_HP_ADD_ATTACK_S) { //韬略 加攻击力
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.THOUSAND_UMBRELLA) { //千机伞
                var effect = _this.getEquipEffectByType(Enums_1.EquipEffectType.THOUSAND_UMBRELLA);
                if (effect) {
                    var val = m.value === 0 ? effect.value * 2 : effect.value;
                    addAttackRatio += (val * 0.01);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addAttackRatio += (m.value * 0.06);
            }
            else if (m.type === Enums_1.BuffType.COURAGEOUSLY) { //典韦 奋勇
                attack += m.value;
            }
            else if (m.type === Enums_1.BuffType.KERIAN) { //辛弃疾 金戈
                addAttackRatio += (m.value * 0.01);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20001 || s.type === 40301 || s.type === 50010) {
                attack += s.value;
            }
            else if (s.type === 20003 || s.type === 50029) {
                addAttackRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addAttackRatio > 0) {
            attack += Math.round(attack * addAttackRatio);
        }
        // 降低比列
        if (lowAttackRatio > 0) {
            attack = Math.max(0, attack - Math.round(attack * lowAttackRatio));
        }
        return attack;
    };
    PawnObj.prototype.getInitMaxHp = function () {
        return this.maxHp;
    };
    PawnObj.prototype.getMaxHp = function () {
        return this.amendMaxHp();
    };
    PawnObj.prototype.amendMaxHp = function (ignoreBuffType) {
        var _this = this;
        if (ignoreBuffType === void 0) { ignoreBuffType = Enums_1.BuffType.NONE; }
        var hp = this.maxHp;
        var addHpRatio = 0, lowHpRatio = 0;
        this.buffs.forEach(function (m) {
            var _a, _b, _c;
            if (m.type === ignoreBuffType) {
                return;
            }
            else if (m.type === Enums_1.BuffType.DEFEND_HALO || m.type === Enums_1.BuffType.LV_1_POWER) {
                addHpRatio += (m.value * 0.01); //提升攻击力百分比
            }
            else if (m.type === Enums_1.BuffType.ADD_MAX_HP) {
                hp += m.value;
            }
            else if (m.type === Enums_1.BuffType.MORALE) { //曹操 士气
                var v = ((_a = assetsMgr.getJsonData('portrayalSkill', Enums_1.HeroType.CAO_CAO)) === null || _a === void 0 ? void 0 : _a.params) || 0;
                hp += (m.value * v);
            }
            else if (m.type === Enums_1.BuffType.TOUGH) { //曹仁 坚韧
                if (((_c = (_b = _this.portrayal) === null || _b === void 0 ? void 0 : _b.skill) === null || _c === void 0 ? void 0 : _c.id) === Enums_1.HeroType.CAO_REN) {
                    hp += (m.value * _this.portrayal.skill.target);
                }
            }
            else if (m.type === Enums_1.BuffType.BREAK_ENEMY_RANKS) { //高顺 陷阵
                addHpRatio += 0.1;
            }
            else if (m.type === Enums_1.BuffType.FEED_INTENSIFY) { //养由基 投喂强化
                addHpRatio += (m.value * 0.04);
            }
            else if (m.type === Enums_1.BuffType.TYRANNICAL) { //董卓 暴虐
                lowHpRatio += (m.value * 0.04);
            }
        });
        // 韬略
        for (var key in this.strategyBuffMap) {
            var s = this.strategyBuffMap[key];
            if (s.type === 20002) {
                hp += s.value;
            }
            else if (s.type === 50010) {
                hp += s.params;
            }
            else if (s.type === 20004) {
                addHpRatio += (s.value * 0.01);
            }
        }
        // 提升比列
        if (addHpRatio > 0) {
            hp += Math.round(hp * addHpRatio);
        }
        // 降低比列
        if (lowHpRatio > 0) {
            hp = Math.max(1, hp - Math.round(hp * lowHpRatio));
        }
        return hp;
    };
    PawnObj.prototype.getCadetLvText = function () {
        var _a;
        if (this.rodeleroCadetLv < 0) {
            return '0';
        }
        var actLv = ((_a = GameHelper_1.gameHpr.getPlayerInfo(this.owner || GameHelper_1.gameHpr.getUid())) === null || _a === void 0 ? void 0 : _a.rodeleroCadetLv) || 0;
        if (!this.uid) {
            return actLv;
        }
        var curLv = this.rodeleroCadetLv;
        if (actLv > curLv) {
            return curLv + "(+" + (actLv - curLv) + ")";
        }
        return actLv + '';
    };
    PawnObj.prototype.getPetId = function () {
        return this.petId;
    };
    PawnObj.prototype.setPetId = function (id) {
        this.petId = id;
    };
    // 改变军队
    PawnObj.prototype.changeArmy = function (data) {
        this.armyUid = data.uid;
        this.armyName = data.name;
    };
    // 是否英雄
    PawnObj.prototype.isHero = function () { return !!this.portrayal; };
    // 是否boss
    PawnObj.prototype.isBoss = function () { return (this.type === Enums_1.PawnType.BEAST || this.type === Enums_1.PawnType.CATERAN) && this.attrJson.move_range === 0; };
    // 是否器械
    PawnObj.prototype.isMachine = function () { return this.type === Enums_1.PawnType.MACHINE; };
    // 是否建筑
    PawnObj.prototype.isBuilding = function () { return this.type === Enums_1.PawnType.BUILD; };
    // 是否非战斗单位
    PawnObj.prototype.isNoncombat = function () { return this.type === Enums_1.PawnType.NONCOMBAT; };
    // 获取血条预制体url
    PawnObj.prototype.getHPBarPrefabUrl = function () {
        if (this.isHero()) {
            return 'pawn/HERO_HP_BAR';
        }
        else if (this.isBoss()) {
            return 'pawn/BOSS_HP_BAR';
        }
        return 'pawn/HP_BAR';
    };
    // 是否可以穿装备
    PawnObj.prototype.isCanWearEquip = function () {
        var _a;
        return (this.type < Enums_1.PawnType.MACHINE || this.isBoss()) && (!!this.owner || !!((_a = this.equip) === null || _a === void 0 ? void 0 : _a.id));
    };
    // 是否满级
    PawnObj.prototype.isMaxLv = function () {
        var _a;
        return !((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.lv_cost);
    };
    // 是否自己的士兵
    PawnObj.prototype.isOwner = function () {
        return this.owner === GameHelper_1.gameHpr.getUid();
    };
    // 是否战斗中
    PawnObj.prototype.isBattleing = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) >= Enums_1.PawnState.STAND || this.aIndex < 0;
    };
    // 获取战斗阵营
    PawnObj.prototype.getBattleCamp = function () {
        var _a, _b, _c;
        var ctrl = (_b = (_a = GameHelper_1.gameHpr.areaCenter.getArea(this.aIndex)) === null || _a === void 0 ? void 0 : _a.getFspModel()) === null || _b === void 0 ? void 0 : _b.getBattleController();
        return (_c = ctrl === null || ctrl === void 0 ? void 0 : ctrl.getFighterCampIndex(this.uid)) !== null && _c !== void 0 ? _c : -1;
    };
    PawnObj.prototype.getHpText = function () {
        var maxHp = this.getMaxHp();
        var curHp = this.uid ? this.curHp : maxHp;
        return curHp + '/' + maxHp;
    };
    PawnObj.prototype.getHpRatio = function () {
        var maxHp = this.getMaxHp();
        return maxHp > 0 ? this.curHp / maxHp : 0;
    };
    PawnObj.prototype.getMaxAnger = function () {
        var _a;
        if (this.maxAnger === 0) {
            return 0;
        }
        var maxAnger = this.maxAnger;
        // 吕蒙 -50%怒气
        if (((_a = this.getPortrayalSkill()) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.LV_MENG) {
            maxAnger = Math.round(this.maxAnger * 0.5);
        }
        // 韬略 怒气-1
        if (this.isHasStrategys(40102, 40201, 40303, 40401)) {
            maxAnger = Math.max(1, maxAnger - 1);
        }
        return maxAnger;
    };
    PawnObj.prototype.getCurAnger = function () {
        return this.curAnger;
    };
    PawnObj.prototype.getAngerText = function () {
        return this.maxAnger > 0 ? this.curAnger + '/' + this.getMaxAnger() : '0/0';
    };
    PawnObj.prototype.getAngerRatio = function () {
        return this.maxAnger > 0 ? this.curAnger / this.getMaxAnger() : 0;
    };
    PawnObj.prototype.isDie = function () {
        return this.curHp <= 0 && this.maxHp > 0;
    };
    PawnObj.prototype.getState = function () {
        var _a;
        return ((_a = this.state) === null || _a === void 0 ? void 0 : _a.type) || Enums_1.PawnState.NONE;
    };
    // 改变状态
    PawnObj.prototype.changeState = function (state, data, init) {
        if (!this.state) {
            this.state = new PawnStateObj_1.default();
        }
        this.state.init(state, data);
        // cc.log('changeState', this.uid, this.point.ID(), PawnState[state], data)
        // 非战斗状态
        if (!init && !this.isBattleing()) {
            this.initAnger();
            this.cleanAllBuffs();
            this.tempAttackAnimTimes = null;
        }
    };
    // 设置画像
    PawnObj.prototype.setPortrayal = function (data) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== data.id) {
            this.skinId = 0; //有画像就一定没有皮肤
            this.portrayal = new PortrayalInfo_1.default().fromSvr(data);
            this.updateAttr();
            this.initAnger();
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_PORTRAYAL, this);
        }
    };
    // 改变攻击速度
    PawnObj.prototype.changeAttackSpeed = function (val) {
        var num = this.attackSpeed + val;
        if (num < 1) {
            num = 9;
        }
        else if (num > 9) {
            num = 1;
        }
        this.attackSpeed = num;
    };
    PawnObj.prototype.setAttackSpeed = function (val) {
        this.attackSpeed = val;
    };
    PawnObj.prototype.changeSkin = function (skinId) {
        if (this.skinId !== skinId) {
            this.skinId = skinId;
            eventCenter.emit(EventType_1.default.CHANGE_PAWN_SKIN, this);
        }
    };
    // 切换装备
    PawnObj.prototype.changeEquip = function (data, isEmit) {
        if (isEmit === void 0) { isEmit = true; }
        if (this.equip.uid !== data.uid) {
            this.equip.setId(data.uid, data.id);
            this.updateEquipAttr(this.equip.uid, data.attrs);
            isEmit && eventCenter.emit(EventType_1.default.CHANGE_PAWN_EQUIP, this);
        }
    };
    // 刷新装备信息
    PawnObj.prototype.updateEquipAttr = function (uid, attrs) {
        if (!this.equip.uid || !attrs) {
            this.equip.setAttr([]);
        }
        else if (this.equip.uid === uid) {
            // 如果是专属 检测自己是否可以携带
            if (!this.equip.isExclusive() || this.equip.checkExclusivePawn(this.id)) {
                this.equip.setAttr(attrs);
            }
            else {
                this.equip.clean();
            }
        }
        this.updateAttr();
    };
    // 刷新英雄信息
    PawnObj.prototype.updateHeroAttr = function (id, attrs) {
        var _a;
        if (((_a = this.portrayal) === null || _a === void 0 ? void 0 : _a.id) !== id || !attrs) {
            return;
        }
        this.portrayal.setAttr(attrs);
        this.updateAttr();
    };
    // 刷新属性 这个只是前端模拟 还是需要以服务器数据为准
    // 一般如果在场景没有战斗并改变属性的时候可以前端自行计算
    PawnObj.prototype.updateAttr = function (init) {
        var _this = this;
        var _a, _b, _c, _d;
        if (!init && this.isBattleing()) {
            return;
        }
        var maxHp = this.attrJson.hp || 0, attack = this.attrJson.attack || 0, attackRange = this.attrJson.attack_range || 0, moveRange = this.attrJson.move_range || 0;
        this.maxHp = maxHp;
        this.attack = attack;
        this.attackRange = attackRange;
        this.moveRange = moveRange;
        var runDay = GameHelper_1.gameHpr.getServerRunDay();
        var addHpRatio = 0, addAttackRatio = 0;
        // 加上装备的
        this.getEquipEffects().forEach(function (m) {
            if (m.type === Enums_1.EquipEffectType.MINGGUANG_ARMOR) { //提高生命
                _this.maxHp += m.value * 10;
            }
            else if (m.type === Enums_1.EquipEffectType.BAIBI_SWORD) { //提高攻击
                _this.attack += m.value;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_HP) { //根据服务器运行时间加生命
                _this.maxHp += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.TODAY_ADD_ATTACK) { //根据服务器运行时间加攻击
                _this.attack += m.value * runDay;
            }
            else if (m.type === Enums_1.EquipEffectType.CENTERING_HELMET) { //提高生命比例
                addHpRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.LONGYUAN_SWORD) { //提高攻击比例
                addAttackRatio += (m.value * 0.01);
            }
            else if (m.type === Enums_1.EquipEffectType.NOT_DODGE) { //攻击范围 +1
                _this.attackRange += 1;
            }
            else if (m.type === Enums_1.EquipEffectType.ADD_MOVE_RANGE) { //移动范围 +1
                _this.moveRange += 1;
            }
        });
        this.maxHp += this.equip.hp;
        this.attack += this.equip.attack;
        // 画像属性
        if (this.portrayal) {
            this.maxHp += this.portrayal.hp;
            this.attack += this.portrayal.attack;
            // 裴行俨 自带攻击力
            if (((_a = this.portrayal.skill) === null || _a === void 0 ? void 0 : _a.id) === Enums_1.HeroType.PEI_XINGYAN) {
                addAttackRatio += this.portrayal.skill.value * 0.01;
            }
        }
        // 见习勇者
        if (this.rodeleroCadetLv > 0) {
            var max = ((_b = this.getSkillByType(Enums_1.PawnSkillType.CADET)) === null || _b === void 0 ? void 0 : _b.value) || 0;
            var cadetLv = Math.min(this.rodeleroCadetLv, max);
            this.maxHp += cadetLv * 5;
            this.attack += Math.round(cadetLv * 0.5);
        }
        // 生命 提升比列
        if (addHpRatio > 0) {
            this.maxHp += Math.round(this.maxHp * addHpRatio);
        }
        // 攻击 提升比例
        if (addAttackRatio > 0) {
            this.attack += Math.round(this.attack * addAttackRatio);
        }
        // 是否有技能强化
        if (this.equip.skillIntensify) {
            var _e = __read(this.equip.skillIntensify, 2), id_1 = _e[0], type = _e[1];
            (_c = this.skills.find(function (m) { return m.baseId === id_1; })) === null || _c === void 0 ? void 0 : _c.setIntensifyType(type);
        }
        else {
            this.skills.forEach(function (m) { return m.setIntensifyType(0); });
        }
        // 如果是主城或配置士兵直接满血
        if ((_d = GameHelper_1.gameHpr.world.getMapCellByIndex(this.aIndex)) === null || _d === void 0 ? void 0 : _d.isRecoverPawnHP()) {
            this.curHp = this.getMaxHp();
        }
        else if (this.tempCurrHp >= 0) {
            this.curHp = Math.min(this.tempCurrHp, this.getMaxHp());
        }
        else {
            this.curHp = Math.min(this.curHp, this.getMaxHp());
        }
    };
    // 获取技能信息
    PawnObj.prototype.getSkillByType = function (type) {
        return this.skills.find(function (m) { return m.type === type; });
    };
    // 获取主动技能
    PawnObj.prototype.getActiveSkill = function () {
        return this.skills.find(function (m) { return m.use_type === 1 || m.type === Enums_1.PawnSkillType.FULL_STRING || m.type === Enums_1.PawnSkillType.LONGITUDINAL_CLEFT; }); //目前满弦、顺劈也算主动
    };
    PawnObj.prototype.setBuffs = function (buffs) {
        this.buffs = buffs.map(function (m) { return new BuffObj_1.default().fromSvr(m); });
    };
    PawnObj.prototype.addBuffs = function (buffs) {
        var _this = this;
        if (buffs === null || buffs === void 0 ? void 0 : buffs.length) {
            var buffMap_1 = {};
            this.buffs.forEach(function (m) { return buffMap_1[m.type] = m; });
            buffs.forEach(function (m) {
                var buff = buffMap_1[m.type];
                if (buff) {
                    buff.fromSvr(m);
                }
                else {
                    _this.buffs.push(new BuffObj_1.default().fromSvr(m));
                }
            });
        }
    };
    PawnObj.prototype.isHasBuff = function (type) {
        return this.buffs.some(function (m) { return m.type === type; });
    };
    PawnObj.prototype.getBuffValue = function (type) {
        var _a;
        return ((_a = this.buffs.find(function (m) { return m.type === type; })) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    PawnObj.prototype.cleanAllBuffs = function () {
        this.cleanBuffs();
        this.cleanStrategyBuffs();
    };
    PawnObj.prototype.cleanBuffs = function () {
        this.buffs.length = 0;
    };
    // 获取护盾值
    PawnObj.prototype.getShieldValue = function () {
        var val = 0;
        this.buffs.forEach(function (m) {
            if (m.isHasShield()) {
                val += m.value;
            }
        });
        return val;
    };
    PawnObj.prototype.cleanStrategyBuffs = function () {
        this.strategyBuffMap = {};
    };
    PawnObj.prototype.addStrategyBuff = function (strategy) {
        this.strategyBuffMap[strategy.type] = strategy;
    };
    PawnObj.prototype.getStrategyBuff = function (tp) {
        return this.strategyBuffMap[tp];
    };
    // 获取韬略数值
    PawnObj.prototype.getStrategyValue = function (tp) {
        var _a;
        return ((_a = this.getStrategyBuff(tp)) === null || _a === void 0 ? void 0 : _a.value) || 0;
    };
    // 是否有某个韬略
    PawnObj.prototype.isHasStrategys = function () {
        var _this = this;
        var tps = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            tps[_i] = arguments[_i];
        }
        return tps.some(function (m) { return !!_this.strategyBuffMap[m]; });
    };
    PawnObj.prototype.isHasStrategy = function () {
        return !ut.isEmptyObject(this.strategyBuffMap);
    };
    PawnObj.prototype.initAnger = function () {
        var _a, _b;
        this.curAnger = ((_a = this.attrJson) === null || _a === void 0 ? void 0 : _a.init_anger) || 0;
        if (this.curAnger > 0) {
            // 吕蒙
            if (((_b = this.getPortrayalSkill()) === null || _b === void 0 ? void 0 : _b.id) === Enums_1.HeroType.LV_MENG) {
                this.curAnger = Math.round(this.curAnger * 0.5);
            }
        }
        return this;
    };
    PawnObj.prototype.isHasAnger = function () {
        return this.maxAnger > 0;
    };
    // 获取装备效果列表
    PawnObj.prototype.getEquipEffects = function () {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects;
    };
    // 获取某个装备效果
    PawnObj.prototype.getEquipEffectByType = function (type) {
        var _a;
        return (_a = this.equip) === null || _a === void 0 ? void 0 : _a.effects.find(function (m) { return m.type === type; });
    };
    // 刷新宝箱
    PawnObj.prototype.updateTreasures = function (treasures) {
        var _this = this;
        this.treasures = (treasures || []).map(function (m) { return GameHelper_1.gameHpr.fromSvrTreasureInfo(m, _this.aIndex, _this.armyUid, _this.uid); });
    };
    return PawnObj;
}());
exports.default = PawnObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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