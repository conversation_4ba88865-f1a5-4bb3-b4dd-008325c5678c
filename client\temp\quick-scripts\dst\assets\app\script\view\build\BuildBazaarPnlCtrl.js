
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildBazaarPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a8f86o02vJPRaMgcmni3/yo', 'BuildBazaarPnlCtrl');
// app/script/view/build/BuildBazaarPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var ccclass = cc._decorator.ccclass;
var BuildBazaarPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildBazaarPnlCtrl, _super);
    function BuildBazaarPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.marketNode_ = null; // path://root/pages_n/1/market_n
        _this.selectMaskNode_ = null; // path://root/pages_n/1/market_n/select_mask_be_n
        _this.buttonsNode_ = null; // path://root/pages_n/1/buttons_nbe_n
        _this.replacementBtn_ = null; // path://root/pages_n/2/replacement_b_be
        _this.loadingNode_ = null; // path://root/loading_n
        //@end
        _this.model = null;
        _this.data = null;
        _this.currTab = 0;
        _this.tradingList = [];
        _this.currSelectFilterTypeMap = {}; //当前的筛选类型
        _this.currSelectSort = 0; //当前选择的排序
        _this.giveName = '';
        _this.isReqRradingRess = false;
        return _this;
    }
    BuildBazaarPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_MERCHANTS] = this.onUpdateMerchants, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_TRADING] = this.onUpdateTrading, _c.enter = true, _c),
        ];
    };
    BuildBazaarPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.model = this.getModel('bazaar');
                this.selectMaskNode_.zIndex = 1;
                return [2 /*return*/];
            });
        });
    };
    BuildBazaarPnlCtrl.prototype.onEnter = function (data, giveName) {
        this.data = data;
        this.selectMaskNode_.active = false;
        // 联盟市场 必须要加入联盟才可以交易
        if (this.data.id === Constant_1.BUILD_ALLI_BAZAAR_NID && !GameHelper_1.gameHpr.player.isHasAlliance()) {
            giveName = '';
        }
        if (giveName) {
            this.giveName = giveName;
            this.tabsTc_.Tabs(1);
            ViewHelper_1.viewHelper.showPnl('build/BuildBazaarChild', 2, this.data, this.giveName);
        }
        else {
            this.tabsTc_.Tabs(this.currTab);
        }
    };
    BuildBazaarPnlCtrl.prototype.onRemove = function () {
    };
    BuildBazaarPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildBazaarPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.currTab = Number(event.node.name);
        var node = this.pagesNode_.Swih(type)[0];
        if (type === 0) {
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            // viewHelper.updateBuildBaseUI(node, this.data, this.key)
            // this.showBaseInfo(node)
        }
        else if (type === 1) { // 市场
            this.updateTradingInfo();
        }
        else if (type === 2) { // 置换
            node.Child('sell/input_ebee', cc.EditBox).setPlaceholder('ui.min_res_replacement_desc', 'f_m', Constant_1.REPLACEMENT_MIN_RES_COUNT);
            this.initBoxList(this.model.CAN_REPLACEMENT_RES, node.Child('sell/box_select_be/list'));
            this.initBoxList(this.model.CAN_REPLACEMENT_RES, node.Child('price/box_select_be/list'));
            this.updateReplacement(node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildBazaarPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/market_n/list/view/content/item/buy/buy_be
    BuildBazaarPnlCtrl.prototype.onClickBuy = function (event, _) {
        var _this = this;
        audioMgr.playSFX('click');
        var data = event.target.parent.parent.Data;
        if (data.owner === GameHelper_1.gameHpr.getUid()) {
            return;
        }
        else if (!GameHelper_1.gameHpr.checkCType(data.buy)) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        ViewHelper_1.viewHelper.showPnl('common/BuyTResTip', data.buy, data.sell, function (ok) {
            if (!_this.isValid || !ok) {
                return;
            }
            _this.model.buyRes(data.uid).then(function (err) {
                if (!_this.isValid) {
                }
                else if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else {
                    _this.reqTradingList();
                    ViewHelper_1.viewHelper.showAlert('toast.buy_res_succeed');
                }
            });
        });
    };
    // path://root/pages_n/1/market_n/list/view/content/item/cancel/soldout_be
    BuildBazaarPnlCtrl.prototype.onClickSoldout = function (event, _) {
        var _this = this;
        var data = event.target.parent.parent.Data;
        if (!data) {
            return;
        }
        ViewHelper_1.viewHelper.showMessageBox('ui.soldout_tip', {
            ok: function () { return _this.model.cancelSell(data.uid).then(function (err) {
                if (err) {
                    return ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.reqTradingList();
                }
            }); },
            cancel: function () { }
        });
    };
    // path://root/pages_n/1/market_n/filter_sell_be
    BuildBazaarPnlCtrl.prototype.onClickFilterSell = function (event, data) {
        this.changeSelectList(event.target, !this.selectMaskNode_.active);
    };
    // path://root/pages_n/1/market_n/filter_buy_be
    BuildBazaarPnlCtrl.prototype.onClickFilterBuy = function (event, data) {
        this.changeSelectList(event.target, !this.selectMaskNode_.active);
    };
    // path://root/pages_n/1/market_n/filter_sell_be/mask/root/fiter_items_nbe
    BuildBazaarPnlCtrl.prototype.onClickFiterItems = function (event, data) {
        var node = event.target.parent.parent.parent.parent;
        this.changeSelectList(node, false);
        var type = Number(event.target.name);
        var key = node.name.split('_')[1];
        if (type !== this.currSelectFilterTypeMap[key]) {
            GameHelper_1.gameHpr.user.setTempPreferenceData('BAZAAR_FILTER_' + key, type);
            this.selectFilterItem(node, type, key);
        }
    };
    // path://root/pages_n/1/market_n/sort_select_be
    BuildBazaarPnlCtrl.prototype.onClickSortSelect = function (event, data) {
        this.changeSelectList(event.target, !this.selectMaskNode_.active);
    };
    // path://root/pages_n/1/market_n/sort_select_be/mask/root/sort_items_nbe
    BuildBazaarPnlCtrl.prototype.onClickSortItems = function (event, data) {
        var node = event.target.parent.parent.parent.parent;
        this.changeSelectList(node, false);
        var type = Number(event.target.name);
        if (type !== this.currSelectSort) {
            GameHelper_1.gameHpr.user.setTempPreferenceData(Enums_1.PreferenceKey.BAZAAR_SORT, type);
            this.selectSortItem(node, type);
        }
    };
    // path://root/pages_n/1/market_n/select_mask_be_n
    BuildBazaarPnlCtrl.prototype.onClickSelectMask = function (event, data) {
        if (this.selectMaskNode_.Data) {
            this.changeSelectList(this.selectMaskNode_.Data, false);
        }
    };
    // path://root/pages_n/1/buttons_nbe_n
    BuildBazaarPnlCtrl.prototype.onClickButtons = function (event, data) {
        var type = Number(event.target.name);
        if (type) {
            // 联盟市场 必须要加入联盟才可以交易
            if (this.data.id === Constant_1.BUILD_ALLI_BAZAAR_NID && !GameHelper_1.gameHpr.player.isHasAlliance()) {
                return ViewHelper_1.viewHelper.showAlert('toast.please_join_alli');
            }
            ViewHelper_1.viewHelper.showPnl('build/BuildBazaarChild', type, this.data, this.giveName);
        }
        else {
            ViewHelper_1.viewHelper.showPnl('build/BuildBazaarRecord');
        }
    };
    // path://root/pages_n/2/price/box_select_be
    BuildBazaarPnlCtrl.prototype.onClickBoxSelect = function (event, data) {
        this.changeBoxList(event.target, true);
    };
    // path://root/pages_n/2/price/box_select_be/box_mask_be
    BuildBazaarPnlCtrl.prototype.onClickBoxMask = function (event, data) {
        this.changeBoxList(event.target.parent, false);
    };
    // path://root/pages_n/2/price/box_select_be/list/box_item_be
    BuildBazaarPnlCtrl.prototype.onClickBoxItem = function (event, data) {
        var _a, _b;
        var node = event.target, it = node.parent.parent;
        var type = (_b = (_a = it.parent) === null || _a === void 0 ? void 0 : _a.parent) === null || _b === void 0 ? void 0 : _b.name;
        this.changeBoxList(it, false);
        this.selectBoxItem(it, node.Data);
    };
    // path://root/pages_n/2/sell/input_ebee
    BuildBazaarPnlCtrl.prototype.onClickInputEnded = function (event, data) {
        this.updateReplacementResCount();
    };
    // path://root/pages_n/2/replacement_b_be
    BuildBazaarPnlCtrl.prototype.onClickReplacement = function (event, data) {
        this.replacementOk();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildBazaarPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
        }
    };
    // 刷新商人数量
    BuildBazaarPnlCtrl.prototype.onUpdateMerchants = function () {
    };
    // 刷新交易中心
    BuildBazaarPnlCtrl.prototype.onUpdateTrading = function () {
        this.reqTradingList();
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 交易中心 -----------------------------------------------------------------------------------------------------------------------------
    BuildBazaarPnlCtrl.prototype.updateTradingInfo = function (node) {
        var _a, _b, _c;
        node = node || this.marketNode_;
        this.selectFilterItem(node.Child('filter_sell_be'), (_a = GameHelper_1.gameHpr.user.getTempPreferenceMap(Enums_1.PreferenceKey.BAZAAR_FILTER_SELL)) !== null && _a !== void 0 ? _a : 0, 'sell');
        this.selectFilterItem(node.Child('filter_buy_be'), (_b = GameHelper_1.gameHpr.user.getTempPreferenceMap(Enums_1.PreferenceKey.BAZAAR_FILTER_BUY)) !== null && _b !== void 0 ? _b : 0, 'buy');
        this.selectSortItem(node.Child('sort_select_be'), (_c = GameHelper_1.gameHpr.user.getTempPreferenceMap(Enums_1.PreferenceKey.BAZAAR_SORT)) !== null && _c !== void 0 ? _c : 0);
        this.updateButtons();
        this.reqTradingList(node);
    };
    BuildBazaarPnlCtrl.prototype.updateButtons = function () {
        var opacity = this.data.id === Constant_1.BUILD_ALLI_BAZAAR_NID && !GameHelper_1.gameHpr.player.isHasAlliance() ? 120 : 255;
        this.buttonsNode_.Child('1').opacity = opacity;
        this.buttonsNode_.Child('2').opacity = opacity;
    };
    BuildBazaarPnlCtrl.prototype.reqTradingList = function (node) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isReqRradingRess) {
                            return [2 /*return*/];
                        }
                        this.isReqRradingRess = true;
                        _a = this;
                        return [4 /*yield*/, this.model.getTradingRess()];
                    case 1:
                        _a.tradingList = _b.sent();
                        this.isReqRradingRess = false;
                        if (this.isValid) {
                            this.updateTradingList(node);
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    BuildBazaarPnlCtrl.prototype.updateTradingList = function (node) {
        var _this = this;
        node = node || this.marketNode_;
        var uid = GameHelper_1.gameHpr.getUid(), list = this.tradingList.filter(function (m) {
            var sellFilterType = _this.currSelectFilterTypeMap['sell'], buyFilterType = _this.currSelectFilterTypeMap['buy'];
            return (!sellFilterType || sellFilterType === m.sell.type) && (!buyFilterType || buyFilterType === m.buy.type);
        });
        list.sort(function (a, b) {
            if (_this.currSelectSort === 0) { //最近卖出
                var ant = a.getNoticeWaitTime(), bnt = b.getNoticeWaitTime();
                var as = ant ? 0 : a.getSurplusTime(), bs = bnt ? 0 : b.getSurplusTime();
                return as === bs ? ant - bnt : bs - as;
            }
            else if (_this.currSelectSort === 1) { //运送时间
                if (a.transitTime !== b.transitTime) {
                    return a.transitTime - b.transitTime;
                }
            }
            else if (_this.currSelectSort === 2) { //最低费用
                if (a.buy.count !== b.buy.count) {
                    return a.buy.count - b.buy.count;
                }
            }
            else if (_this.currSelectSort === 3) { //最高提供
                if (a.sell.count !== b.sell.count) {
                    return b.sell.count - a.sell.count;
                }
            }
            return b.getSurplusTime() - a.getSurplusTime();
        });
        node.Child('list/empty').active = list.length === 0;
        var sv = node.Child('list', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        sv.List(list.length, function (it, i) {
            var data = it.Data = list[i];
            var isOwner = data.owner === uid;
            ViewHelper_1.viewHelper.updateCostViewOne(it.Child('goods'), data.sell);
            ViewHelper_1.viewHelper.updateCostViewOne(it.Child('cost'), data.buy, !isOwner);
            it.Child('name', cc.Label).string = ut.nameFormator(GameHelper_1.gameHpr.getPlayerName(data.owner), 7);
            var buyNode = it.Child('buy'), soldoutNode = it.Child('soldout'), noticeNode = it.Child('notice'), cancelNode = it.Child('cancel');
            var cancelTime = data.getCancelTime();
            var isCanCancel = cancelNode.active = cancelTime > 0 && isOwner;
            if (isCanCancel) {
                it.Color(isOwner ? '#FADFA5' : '#E9DDC7');
                cancelNode.Child('time', cc.LabelTimer).run(cancelTime * 0.001, function () {
                    if (_this.isValid) {
                        _this.updateTradingList();
                    }
                });
            }
            if (buyNode.active = !isOwner && !isCanCancel) {
                var dis = GameHelper_1.gameHpr.getSelfToOtherPlayerDis(data.owner);
                var time = dis * (ut.Time.Hour / Constant_1.TRANSIT_TIME);
                buyNode.Child('time', cc.Label).string = ut.millisecondFormat(time, 'h:mm:ss');
            }
            if (soldoutNode.active = isOwner && !isCanCancel) {
                it.Color('#E9DDC7');
                it.Child('name').opacity = soldoutNode.opacity = 120;
                soldoutNode.Child('time', cc.LabelTimer).run(data.getSurplusTime() * 0.001, function () {
                    if (_this.isValid) {
                        _this.tradingList.remove('uid', data.uid);
                        _this.updateTradingList();
                    }
                });
            }
        });
    };
    // 打开关闭弹出框
    BuildBazaarPnlCtrl.prototype.changeSelectList = function (node, val) {
        this.selectMaskNode_.Data = val ? node : null;
        this.selectMaskNode_.active = val;
        var mask = node.Child('mask'), root = mask.Child('root');
        if (val) {
            node.zIndex = 2;
            mask.active = true;
            root.y = -mask.height;
            cc.tween(root).to(0.15, { y: 4 }, { easing: cc.easing.sineOut }).start();
        }
        else {
            node.zIndex = 0;
            root.y = 4;
            cc.tween(root).to(0.1, { y: -mask.height }).call(function () { return mask.active = false; }).start();
        }
        cc.tween(node.Child('icon')).to(0.15, { angle: val ? -180 : 0 }).start();
    };
    // 选择赛选
    BuildBazaarPnlCtrl.prototype.selectFilterItem = function (node, type, key) {
        node.Data = this.currSelectFilterTypeMap[key] = type;
        node.Child('val', cc.Label).setLocaleKey(type ? '' : 'ui.bazaar_filter_all');
        var iconNode = node.Child('val/icon', cc.Sprite);
        if (iconNode.setActive(!!type)) {
            iconNode.spriteFrame = ResHelper_1.resHelper.getResIcon(type);
        }
        node.Child('mask/root/fiter_items_nbe').children.forEach(function (m) {
            m.Child('select').active = Number(m.name) === type;
        });
        // 筛选
        this.updateTradingList();
    };
    // 排序
    BuildBazaarPnlCtrl.prototype.selectSortItem = function (node, type) {
        node.Data = this.currSelectSort = type;
        node.Child('val', cc.Label).setLocaleKey('ui.bazaar_sort_desc_' + type);
        node.Child('mask/root/sort_items_nbe').children.forEach(function (m) {
            var select = Number(m.name) === type;
            m.Child('val').Color(select ? '#564C49' : '#A18876');
            m.Child('select').active = select;
        });
        // 排序
        this.updateTradingList();
    };
    // 置换 ---------------------------------------------------------------------------------------------------------------------------------
    BuildBazaarPnlCtrl.prototype.initBoxList = function (types, node) {
        node.Items(types, function (it, type, i) {
            it.Data = type;
            it.Child('icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
            it.Child('val').setLocaleKey(Constant_1.CTYPE_NAME[type]);
            it.Child('line').active = i < types.length - 1;
        });
    };
    BuildBazaarPnlCtrl.prototype.replacementOk = function () {
        var _this = this;
        if (this.model.getSurplusReplacementCount() <= 0) {
            return;
        }
        var node = this.pagesNode_.Child(2);
        var sellType = node.Child('sell/box_select_be').Data, sellEb = node.Child('sell/input_ebee', cc.EditBox);
        var buyType = node.Child('price/box_select_be').Data;
        var sellCount = Number(sellEb.string.trim()) || 0;
        var buyCount = Math.floor(sellCount * (100 - this.getServiceCharge()) * 0.01);
        var items = GameHelper_1.gameHpr.checkRewardFull([new CTypeObj_1.default().init(buyType, 0, buyCount)]);
        if (items.length > 0) {
            return ViewHelper_1.viewHelper.showPnl('common/ResFullTip', items, function (ok) { ok && _this.replacementRes(sellType, sellCount, buyType); });
        }
        this.replacementRes(sellType, sellCount, buyType);
    };
    BuildBazaarPnlCtrl.prototype.updateReplacement = function (node) {
        node = node || this.pagesNode_.Child(2);
        var pe = GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MARKET_SERVICE_CHARGE), serviceCharge = Math.max(Constant_1.REPLACEMENT_SERVICE_CHARGE - pe, 0);
        node.Child('service_charge/val', cc.Label).Color(pe ? '#49983C' : '#A18876').string = serviceCharge + '%';
        var upNode = node.Child('service_charge/up');
        if (upNode.active = !!pe) {
            upNode.Child('val', cc.Label).string = '-' + pe + '%';
        }
        var sellBox = node.Child('sell/box_select_be'), priceBox = node.Child('price/box_select_be');
        this.changeBoxList(sellBox, false);
        this.changeBoxList(priceBox, false);
        sellBox.Data = sellBox.Data || this.model.CAN_REPLACEMENT_RES[0];
        priceBox.Data = priceBox.Data || this.model.CAN_REPLACEMENT_RES[1];
        sellBox['_mutual'] = priceBox;
        priceBox['_mutual'] = sellBox;
        this.selectBoxItem(sellBox, sellBox.Data);
        this.selectBoxItem(priceBox, priceBox.Data);
        this.updateTodayReplacementCount(node);
        this.updateReplacementResCount(node);
    };
    // 打开关闭box弹出框
    BuildBazaarPnlCtrl.prototype.changeBoxList = function (node, val) {
        node.Child('box_mask_be').active = val;
        node.Child('list').active = val;
        // 刷新列表选中的颜色
        if (val) {
            var type_1 = node.Data;
            node.Child('list').children.forEach(function (m) {
                var select = m.Data === type_1;
                var color = select ? '#564C49' : '#A18876';
                m.Child('select').active = select;
                m.Child('val').Color(color);
            });
        }
    };
    // 选择一个
    BuildBazaarPnlCtrl.prototype.selectBoxItem = function (node, type) {
        var _a;
        var curType = node.Data;
        var banType = (_a = node['_mutual']) === null || _a === void 0 ? void 0 : _a.Data;
        node.Data = type;
        node.Child('content/icon', cc.Sprite).spriteFrame = assetsMgr.getImage(Constant_1.CTYPE_ICON[type]);
        node.Child('content/val').setLocaleKey(Constant_1.CTYPE_NAME[type]);
        if (curType && type === banType) {
            this.selectBoxItem(node['_mutual'], curType);
        }
    };
    BuildBazaarPnlCtrl.prototype.updateTodayReplacementCount = function (node) {
        var count = this.model.getSurplusReplacementCount();
        var isCanReplacement = count > 0;
        node.Child('replacement_count/val', cc.Label).Color(isCanReplacement ? '#F38C1D' : '#A18876').string = '' + count;
        this.replacementBtn_.interactable = isCanReplacement;
        this.replacementBtn_.Component(cc.MultiFrame).setFrame(isCanReplacement);
    };
    // 刷新交换数量
    BuildBazaarPnlCtrl.prototype.updateReplacementResCount = function (node) {
        node = node || this.pagesNode_.Child(2);
        var sellInput = node.Child('sell/input_ebee', cc.EditBox);
        var sellType = node.Child('sell/box_select_be').Data;
        var sellNum = Math.min(Number(sellInput.string.trim()) || 0, GameHelper_1.gameHpr.getCountByCType(sellType));
        if (sellNum > 0) {
            sellNum = Math.max(sellNum, Constant_1.REPLACEMENT_MIN_RES_COUNT);
        }
        var priceNum = Math.floor(sellNum * (100 - this.getServiceCharge()) * 0.01);
        sellInput.string = sellNum ? sellNum + '' : '';
        node.Child('price/reward/val', cc.Label).string = priceNum + '';
    };
    BuildBazaarPnlCtrl.prototype.replacementRes = function (sellType, sellCount, buyType) {
        var _this = this;
        this.model.replacementRes(sellType, sellCount, buyType).then(function (err) {
            if (!_this.isValid) {
            }
            else if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                var node = _this.pagesNode_.Child(2);
                node.Child('sell/input_ebee', cc.EditBox).string = '';
                _this.updateTodayReplacementCount(node);
                _this.updateReplacementResCount(node);
                ViewHelper_1.viewHelper.showAlert('toast.replacement_res_succeed');
            }
        });
    };
    // 获取手续费
    BuildBazaarPnlCtrl.prototype.getServiceCharge = function () {
        return Math.max(Constant_1.REPLACEMENT_SERVICE_CHARGE - GameHelper_1.gameHpr.getPlayerPolicyEffect(Enums_1.CEffect.MARKET_SERVICE_CHARGE), 0);
    };
    BuildBazaarPnlCtrl = __decorate([
        ccclass
    ], BuildBazaarPnlCtrl);
    return BuildBazaarPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildBazaarPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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