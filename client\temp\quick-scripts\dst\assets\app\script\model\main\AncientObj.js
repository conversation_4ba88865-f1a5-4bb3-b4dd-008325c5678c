
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/AncientObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'faa5b0wbetD0bUtyC+cJOkZ', 'AncientObj');
// app/script/model/main/AncientObj.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ProtoHelper_1 = require("../../../proto/ProtoHelper");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var CTypeObj_1 = require("../common/CTypeObj");
// 一个遗迹
var AncientObj = /** @class */ (function () {
    function AncientObj() {
        this.cell = null;
        this.lvUpRes = {}; //升级资源
        this.speedUpRes = 0; //加速资源
        this.lv = 1;
        this.surplusTime = 0;
        this.state = 0; //修建状态 0未修建 1修建中
        this.pauseState = 0; //暂停状态 0未暂停 1交战暂停
        this.curContribute = 0; //捐献状态 位运算 0加速 1粮食 2木头 3石头
        this.ctbSurplusCount = 0; //剩余捐献次数
        this.firstSurplusTime = 0; //首次攻占成功剩余时间
        this.getTime = 0;
        this.ctbCdSurplusTime = 0; //捐献CD剩余时间
        this.getCtbCdTime = 0;
        this.lastReqDonateStatisticsTime = 0; //最后请求统计时间
        this.tempDonateStatistics = []; //统计
        this.lastReqLogsTime = 0; //最后请求记录时间
        this.tempLogsList = []; //记录
    }
    AncientObj.prototype.init = function (cell) {
        this.cell = cell;
        return this;
    };
    Object.defineProperty(AncientObj.prototype, "index", {
        get: function () { var _a, _b; return (_b = (_a = this.cell) === null || _a === void 0 ? void 0 : _a.actIndex) !== null && _b !== void 0 ? _b : -1; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AncientObj.prototype, "owner", {
        get: function () { var _a, _b; return (_b = (_a = this.cell) === null || _a === void 0 ? void 0 : _a.owner) !== null && _b !== void 0 ? _b : ''; },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AncientObj.prototype, "id", {
        get: function () { var _a, _b; return Math.abs((_b = (_a = this.cell) === null || _a === void 0 ? void 0 : _a.cityId) !== null && _b !== void 0 ? _b : 0); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(AncientObj.prototype, "name", {
        get: function () { return 'cityText.name_' + this.cell.cityId; },
        enumerable: false,
        configurable: true
    });
    // 刷新遗迹信息
    AncientObj.prototype.updateInfo = function (data, updateAreaBuild) {
        var _this = this;
        var _a, _b;
        this.lvUpRes = {};
        (_a = data.lvUpRes) === null || _a === void 0 ? void 0 : _a.forEach(function (m) { return _this.lvUpRes[m.type] = new CTypeObj_1.default().fromSvr(m); });
        this.speedUpRes = data.speedUpRes || 0;
        this.lv = data.lv || 1;
        this.surplusTime = data.surplusTime || 0;
        this.state = data.state || 0;
        this.pauseState = data.pauseState || 0;
        if (data.curContribute !== -1) {
            this.curContribute = (_b = data.curContribute) !== null && _b !== void 0 ? _b : 0;
        }
        this.ctbSurplusCount = data.ctbSurplusCount || 0;
        this.firstSurplusTime = data.firstSurplusTime || 0;
        this.ctbCdSurplusTime = data.ctbCdSurplusTime || 0;
        this.getTime = this.getCtbCdTime = Date.now();
        // 刷新区域的建筑
        if (updateAreaBuild) {
            var area = GameHelper_1.gameHpr.areaCenter.getArea(data.index);
            if (area) {
                var build = area.getBuildById(this.cell.cityId);
                if (build && build.lv !== this.lv) {
                    build.updateLv(this.lv);
                    eventCenter.emit(EventType_1.default.UPDATE_BUILD_LV, build);
                }
            }
        }
    };
    // 获取实际的剩余时间
    AncientObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 获取实际首次攻占剩余时间
    AncientObj.prototype.getFirstSurplusTime = function () {
        return this.firstSurplusTime > 0 ? Math.max(0, this.firstSurplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 获取捐献冷却时间
    AncientObj.prototype.getCtbCdSurplusTime = function () {
        return this.ctbCdSurplusTime > 0 ? Math.max(0, this.ctbCdSurplusTime - (Date.now() - this.getCtbCdTime)) : 0;
    };
    // 获取捐献的升级资源类型
    AncientObj.prototype.getContributeLUpType = function () {
        if (ProtoHelper_1.protoHelper.checkFlag(this.curContribute, Enums_1.CType.CEREAL)) {
            return Enums_1.CType.CEREAL;
        }
        else if (ProtoHelper_1.protoHelper.checkFlag(this.curContribute, Enums_1.CType.TIMBER)) {
            return Enums_1.CType.TIMBER;
        }
        else if (ProtoHelper_1.protoHelper.checkFlag(this.curContribute, Enums_1.CType.STONE)) {
            return Enums_1.CType.STONE;
        }
        return -1;
    };
    // 是否捐献过加速资源
    AncientObj.prototype.isContributeSUp = function () {
        return ProtoHelper_1.protoHelper.checkFlag(this.curContribute, 0);
    };
    // 获取记录列表
    AncientObj.prototype.getLogs = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqLogsTime > 0 && Date.now() - this.lastReqLogsTime <= 10000) {
                            return [2 /*return*/, this.tempLogsList];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_GetAncientLogs', { index: this.index })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqLogsTime = Date.now();
                        this.tempLogsList = [];
                        data === null || data === void 0 ? void 0 : data.list.forEach(function (m) {
                            _this.tempLogsList.push({
                                uid: m.uid,
                                type: m.type,
                                nickname: GameHelper_1.gameHpr.getPlayerName(m.uid),
                                time: m.time,
                                res: { type: m.resType, id: 0, count: m.count },
                            });
                        });
                        return [2 /*return*/, this.tempLogsList];
                }
            });
        });
    };
    // 获取捐献统计
    AncientObj.prototype.getDonateStatistics = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, err, data;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.lastReqDonateStatisticsTime > 0 && Date.now() - this.lastReqDonateStatisticsTime <= 10000) {
                            return [2 /*return*/, this.tempDonateStatistics];
                        }
                        return [4 /*yield*/, GameHelper_1.gameHpr.net.request('game/HD_GetAncientDonateAcc', { index: this.index })];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        this.lastReqDonateStatisticsTime = Date.now();
                        this.tempDonateStatistics = [];
                        data === null || data === void 0 ? void 0 : data.list.forEach(function (m) {
                            var info = GameHelper_1.gameHpr.getPlayerInfo(m.uid);
                            if (info) {
                                _this.tempDonateStatistics.push({
                                    uid: m.uid,
                                    nickname: info.nickname,
                                    headIcon: info.headIcon,
                                    items: m.items.sort(function (a, b) { return a.type - b.type; }),
                                    sum: m.items.reduce(function (val, cur) { return val + cur.count; }, 0),
                                });
                            }
                        });
                        this.tempDonateStatistics.sort(function (a, b) { return b.sum - a.sum; });
                        return [2 /*return*/, this.tempDonateStatistics];
                }
            });
        });
    };
    return AncientObj;
}());
exports.default = AncientObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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