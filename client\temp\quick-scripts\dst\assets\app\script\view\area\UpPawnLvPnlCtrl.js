
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/UpPawnLvPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '37aedtNPddDpoqGISim838X', 'UpPawnLvPnlCtrl');
// app/script/view/area/UpPawnLvPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ccclass = cc._decorator.ccclass;
var UpPawnLvPnlCtrl = /** @class */ (function (_super) {
    __extends(UpPawnLvPnlCtrl, _super);
    function UpPawnLvPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.contentNode_ = null; // path://root/content_n
        //@end
        _this.cb = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    UpPawnLvPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    UpPawnLvPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    UpPawnLvPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        var count = 1;
        var color = GameHelper_1.gameHpr.player.getUpScroll() < count ? '#C34A32' : '#333333';
        this.contentNode_.setLocaleKey('ui.up_pawn_lv_tip', "<color=" + color + ">" + count + "</c>", data.lv, data.lv + 1);
    };
    UpPawnLvPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    UpPawnLvPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    UpPawnLvPnlCtrl.prototype.onClickButtons = function (event, data) {
        var _this = this;
        if (!this.cb) {
            return this.hide();
        }
        this.cb(event.target.name === 'ok').then(function (ok) { return (ok && _this.isValid) && _this.hide(); });
    };
    UpPawnLvPnlCtrl = __decorate([
        ccclass
    ], UpPawnLvPnlCtrl);
    return UpPawnLvPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = UpPawnLvPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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