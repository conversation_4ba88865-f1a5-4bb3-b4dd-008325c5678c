
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/AddAlliMemberPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '046c0cErz5GJ7k5DT9APzxz', 'AddAlliMemberPnlCtrl');
// app/script/view/common/AddAlliMemberPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var AddAlliMemberPnlCtrl = /** @class */ (function (_super) {
    __extends(AddAlliMemberPnlCtrl, _super);
    function AddAlliMemberPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        _this.selectedNode_ = null; // path://root/selected_n
        _this.selectAllTge_ = null; // path://root/select_all_t_te
        //@end
        _this.alliance = null;
        _this.selectAll = false;
        _this.selectRecords = {};
        _this.memberUids = [];
        _this.cb = null;
        _this.isFromCreate = false;
        return _this;
    }
    AddAlliMemberPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AddAlliMemberPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.alliance = this.getModel('alliance');
                return [2 /*return*/];
            });
        });
    };
    AddAlliMemberPnlCtrl.prototype.onEnter = function (memberUids, cb, isFromCreate) {
        var _this = this;
        this.memberUids = memberUids.slice();
        this.cb = cb;
        this.isFromCreate = isFromCreate;
        var myUid = GameHelper_1.gameHpr.user.getUid();
        isFromCreate && this.memberUids.forEach(function (m) { return m !== myUid && (_this.selectRecords[m] = true); });
        this.updateAlliMembers();
        this.updateSelectedMemberCount();
        this.updateSelectAllState();
    };
    AddAlliMemberPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
        this.selectAll = false;
        this.selectRecords = {};
    };
    AddAlliMemberPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/member_te
    AddAlliMemberPnlCtrl.prototype.onClickMember = function (event, _data) {
        var isChecked = event['isChecked'], data = event.target.parent.Data;
        if (data) {
            this.selectRecords[data.uid] = isChecked;
        }
        this.updateSelectedMemberCount();
        this.updateSelectAllState();
    };
    // path://root/ok_be
    AddAlliMemberPnlCtrl.prototype.onClickOk = function (event, data) {
        var selectedUids = this.isFromCreate ? this.selectedUids.concat([GameHelper_1.gameHpr.user.getUid()]) : this.selectedUids.concat(this.memberUids);
        this.cb && this.cb(selectedUids);
        this.hide();
    };
    // path://root/select_all_t_te
    AddAlliMemberPnlCtrl.prototype.onClickSelectAll = function (event, data) {
        var _this = this;
        this.selectAll = !this.selectAll;
        this.selectAllTge_.isChecked = this.selectAll;
        var myUid = GameHelper_1.gameHpr.user.getUid();
        this.alliance.getMembers().forEach(function (m) {
            if (m.uid !== myUid && (_this.isFromCreate || !_this.memberUids.has(m.uid))) {
                _this.selectRecords[m.uid] = _this.selectAll;
            }
        });
        this.listSv_.content.children.forEach(function (m) {
            var data = m.Data;
            if (data && data.uid !== myUid && typeof _this.selectRecords[data.uid] === 'boolean') {
                m.Component(cc.Toggle).isChecked = _this.selectRecords[data.uid];
            }
        });
        this.updateSelectedMemberCount();
    };
    Object.defineProperty(AddAlliMemberPnlCtrl.prototype, "selectedUids", {
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
        get: function () {
            var _this = this;
            // return Object.entries(this.selectRecords)
            //     .filter(([key, value]) => value === true)
            //     .map(([key]) => key)
            var selects = [];
            Object.keys(this.selectRecords).forEach(function (key) { return _this.selectRecords[key] && selects.push(key); });
            return selects;
        },
        enumerable: false,
        configurable: true
    });
    AddAlliMemberPnlCtrl.prototype.updateAlliMembers = function () {
        var _this = this;
        var members = this.alliance.getMembers().slice().sort(function (a, b) {
            if (_this.isFromCreate) {
                if (a.offlineTime !== b.offlineTime) {
                    return a.offlineTime - b.offlineTime;
                }
                else if (a.job !== b.job) {
                    return a.job - b.job;
                }
                return a.joinTime - b.joinTime;
            }
            else {
                var aIn = _this.memberUids.has(a.uid), bIn = _this.memberUids.has(b.uid);
                if (!aIn && !bIn) {
                    if (a.offlineTime !== b.offlineTime) {
                        return a.offlineTime - b.offlineTime;
                    }
                    else if (a.job !== b.job) {
                        return a.job - b.job;
                    }
                    else if (a.joinTime !== b.joinTime) {
                        return a.joinTime - b.joinTime;
                    }
                }
                return aIn ? 1 : -1;
            }
        });
        this.listSv_.stopAutoScroll();
        this.listSv_.content.y = 0;
        var selfUid = GameHelper_1.gameHpr.user.getUid();
        var memberCount = members.length;
        this.listSv_.List(memberCount, function (it, i) {
            var data = it.Data = members[i], isMySelf = data.uid === selfUid;
            var existed = _this.memberUids.some(function (m) { return m === data.uid; }), special = isMySelf || (existed && !_this.isFromCreate);
            it.opacity = special ? 150 : 255;
            var toggle = it.Component(cc.Toggle);
            toggle.isChecked = existed;
            toggle.interactable = special ? false : true;
            ResHelper_1.resHelper.loadPlayerHead(it.Child('head/val', cc.Sprite), data.headIcon, _this.key);
            it.Child('name', cc.Label).string = ut.nameFormator(data.nickname || '???', 14);
        });
    };
    AddAlliMemberPnlCtrl.prototype.updateSelectedMemberCount = function () {
        this.selectedNode_.Child('val').setLocaleKey('ui.member_count', this.selectedUids.length);
    };
    AddAlliMemberPnlCtrl.prototype.updateSelectAllState = function () {
        var checked = false;
        if (this.isFromCreate) {
            checked = this.selectedUids.length === this.alliance.getMembers().length - 1;
        }
        else {
            checked = this.selectedUids.length === this.alliance.getMembers().length - this.memberUids.length;
        }
        this.selectAll = checked;
        this.selectAllTge_.isChecked = checked;
    };
    AddAlliMemberPnlCtrl = __decorate([
        ccclass
    ], AddAlliMemberPnlCtrl);
    return AddAlliMemberPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AddAlliMemberPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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