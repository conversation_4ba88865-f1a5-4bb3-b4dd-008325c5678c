
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/SelectAvatarHeroPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '24f1aIDVkpDwK2ANwHomOyu', 'SelectAvatarHeroPnlCtrl');
// app/script/view/area/SelectAvatarHeroPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ECode_1 = require("../../common/constant/ECode");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var SelectAvatarHeroPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectAvatarHeroPnlCtrl, _super);
    function SelectAvatarHeroPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.cb = null;
        _this.currSelectPortrayal = null;
        return _this;
    }
    SelectAvatarHeroPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectAvatarHeroPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    SelectAvatarHeroPnlCtrl.prototype.onEnter = function (id, cb) {
        var _this = this;
        this.cb = cb;
        var list = GameHelper_1.gameHpr.player.getHeroSlots().filter(function (m) { var _a; return ((_a = m.hero) === null || _a === void 0 ? void 0 : _a.avatarPawn) === id; });
        var empty = this.rootNode_.Child('content/empty');
        if (empty.active = !list.length) {
            empty.setLocaleKey('ui.select_avatar_hero_empty', 'pawnText.name_' + id);
        }
        this.rootNode_.Child('content/list').Items(list, function (it, data) {
            it.Data = { id: data.hero.id, hero: data.hero };
            var hero = data.hero, stateNode = it.Child('state');
            var state = it.Data.state = data.getState();
            var isDie = state === 3;
            it.Child('name/val').setLocaleKey(hero.getChatName());
            var iconNode = it.Child('mask/icon');
            ResHelper_1.resHelper.loadPortrayalImage(hero.id, iconNode, _this.key);
            iconNode.setPosition(hero.showBoxOffset);
            iconNode.Component(cc.Sprite).setMaterial(0, ResHelper_1.resHelper.get2dSpriteMaterial(!isDie));
            // 状态
            stateNode.Child('val').Color(ViewHelper_1.viewHelper.getHeroStateBgColor(state)).setLocaleKey('ui.avatar_state_' + state);
            // 是否阵亡
            var diType = isDie ? 0 : hero.getUIDiType();
            it.Child('di', cc.MultiFrame).setFrame(diType);
            it.Child('di/val').active = !!diType;
            if (it.Child('time').active = isDie) {
                it.Child('time/val', cc.LabelTimer).run(data.getReviveSurplusTime() * 0.001);
            }
        });
        this.updateSelectHeroInfo(null);
    };
    SelectAvatarHeroPnlCtrl.prototype.onRemove = function () {
        this.cb && this.cb(0);
        this.cb = null;
    };
    SelectAvatarHeroPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/content/list/item_be
    SelectAvatarHeroPnlCtrl.prototype.onClickItem = function (event, _) {
        var data = event.target.Data;
        if (!data) {
            return;
        }
        else if (data.state === 2) { //2.化身中
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.HERO_YET_AVATAR);
        }
        else if (data.state === 3) { //3.阵亡中
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.HERO_YET_DIE);
        }
        this.updateSelectHeroInfo(data.hero);
    };
    // path://root_n/buttons/ok_be
    SelectAvatarHeroPnlCtrl.prototype.onClickOk = function (event, _) {
        var data = this.currSelectPortrayal;
        if (!data) {
            return ViewHelper_1.viewHelper.showAlert('ui.select_hero_tip');
        }
        this.cb && this.cb(data.id);
        this.cb = null;
        this.hide();
    };
    // path://root_n/buttons/desc_be
    SelectAvatarHeroPnlCtrl.prototype.onClickDesc = function (event, data) {
        ViewHelper_1.viewHelper.showDescInfo([
            { key: 'ui.avatar_rule_desc_1' },
            { key: 'ui.avatar_rule_desc_2' },
            { key: 'ui.avatar_rule_desc_3' },
        ], 'ui.title_avatar_desc');
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 刷新选择
    SelectAvatarHeroPnlCtrl.prototype.updateSelectHeroInfo = function (data) {
        var _this = this;
        this.currSelectPortrayal = data;
        var root = this.rootNode_;
        // 刷新选择
        root.Child('content/list').children.forEach(function (it) {
            var _a;
            var select = (data === null || data === void 0 ? void 0 : data.id) === ((_a = it.Data) === null || _a === void 0 ? void 0 : _a.id);
            it.Child('select').active = select;
            it.Component(cc.Button).interactable = !select;
        });
        // 显示信息
        root.Child('empty').active = !data;
        var sv = root.Child('info', cc.ScrollView), info = sv.content;
        if (sv.setActive(!!data)) {
            sv.stopAutoScroll();
            info.y = 0;
            ViewHelper_1.viewHelper.updatePortrayalAttr(info, data, true);
            ut.waitNextFrame(2).then(function () {
                if (_this.isValid) {
                    sv.node.height = cc.misc.clampf(info.height + 4, 160, 320);
                    sv.node.Child('view', cc.Widget).updateAlignment();
                }
            });
        }
    };
    SelectAvatarHeroPnlCtrl = __decorate([
        ccclass
    ], SelectAvatarHeroPnlCtrl);
    return SelectAvatarHeroPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectAvatarHeroPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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