
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/BTInfoObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'aa323vUFghAqb7GXECNNW0m', 'BTInfoObj');
// app/script/model/main/BTInfoObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 建造信息
var BTInfoObj = /** @class */ (function () {
    function BTInfoObj() {
        this.index = 0;
        this.uid = ''; //建筑uid
        this.id = 0; //建筑id
        this.lv = 0; //要建造的等级
        this.needTime = 0; //需要建造的时间
        this.surplusTime = 0; //剩余建造的时间
        this.getTime = 0; //获取时间
    }
    BTInfoObj.prototype.fromSvr = function (data) {
        this.index = data.index;
        this.uid = data.uid;
        this.id = data.id;
        this.lv = data.lv;
        this.needTime = data.needTime;
        this.surplusTime = data.surplusTime;
        this.getTime = Date.now();
        return this;
    };
    Object.defineProperty(BTInfoObj.prototype, "name", {
        get: function () { return 'buildText.name_' + this.id; },
        enumerable: false,
        configurable: true
    });
    // 获取实际的剩余时间
    BTInfoObj.prototype.getSurplusTime = function () {
        return this.surplusTime > 0 ? Math.max(0, this.surplusTime - (Date.now() - this.getTime)) : 0;
    };
    // 是否开始
    BTInfoObj.prototype.isRuning = function () {
        return this.surplusTime > 0;
    };
    return BTInfoObj;
}());
exports.default = BTInfoObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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