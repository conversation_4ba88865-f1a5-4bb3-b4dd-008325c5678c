
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/StartStudyTipPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '6b5ed4SW7xLRoh5+st2+gym', 'StartStudyTipPnlCtrl');
// app/script/view/build/StartStudyTipPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var StartStudyTipPnlCtrl = /** @class */ (function (_super) {
    __extends(StartStudyTipPnlCtrl, _super);
    function StartStudyTipPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.descLbl_ = null; // path://root/desc_l
        _this.iconNode_ = null; // path://root/icon_n
        //@end
        _this.cb = null;
        return _this;
    }
    StartStudyTipPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    StartStudyTipPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    StartStudyTipPnlCtrl.prototype.onEnter = function (data, cb) {
        this.cb = cb;
        if (data.type === 1) {
            this.descLbl_.setLocaleKey('ui.start_study_policy_tip');
        }
        else {
            this.descLbl_.setLocaleKey('ui.start_study_tip', 'ui.ceri_type_name_' + data.type);
        }
        this.iconNode_.Child('name').setLocaleKey(this.getItemName(data));
        this.loadIcon(this.iconNode_.Child('val', cc.Sprite), data);
    };
    StartStudyTipPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    StartStudyTipPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/buttons_nbe
    StartStudyTipPnlCtrl.prototype.onClickButtons = function (event, data) {
        this.cb && this.cb(event.target.name === 'ok');
        this.hide();
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    StartStudyTipPnlCtrl.prototype.getItemName = function (data) {
        if (!data) {
            return '';
        }
        else if (data.type === Enums_1.StudyType.POLICY) { //政策
            return 'policyText.name_' + data.id;
        }
        else if (data.type === Enums_1.StudyType.PAWN) { //士兵
            return 'pawnText.name_' + data.id;
        }
        else if (data.type === Enums_1.StudyType.EQUIP) { //装备
            return 'equipText.name_' + data.id;
        }
        return '';
    };
    StartStudyTipPnlCtrl.prototype.loadIcon = function (spr, data) {
        if (data.type === Enums_1.StudyType.POLICY) { //政策
            ResHelper_1.resHelper.loadPolicyIcon(data.id, spr, this.key);
        }
        else if (data.type === Enums_1.StudyType.PAWN) { //士兵
            ResHelper_1.resHelper.loadPawnHeadMiniIcon(data.id, spr, this.key);
        }
        else if (data.type === Enums_1.StudyType.EQUIP) { //装备
            ResHelper_1.resHelper.loadEquipIcon(data.id, spr, this.key);
        }
        else {
            spr.spriteFrame = null;
        }
    };
    StartStudyTipPnlCtrl = __decorate([
        ccclass
    ], StartStudyTipPnlCtrl);
    return StartStudyTipPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = StartStudyTipPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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