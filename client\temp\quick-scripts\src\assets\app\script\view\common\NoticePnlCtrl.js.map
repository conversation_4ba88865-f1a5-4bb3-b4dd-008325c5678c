{"version": 3, "sources": ["assets\\app\\script\\view\\common\\NoticePnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAyD;AAEjD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAA2C,iCAAc;IAAzD;QAAA,qEAyEC;QAvEG,0BAA0B;QAClB,aAAO,GAAkB,IAAI,CAAA,CAAC,sBAAsB;QACpD,kBAAY,GAAY,IAAI,CAAA,CAAC,wBAAwB;;IAqEjE,CAAC;IApEG,MAAM;IAEC,uCAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,gCAAQ,GAArB;;;;;;KACC;IAEM,+BAAO,GAAd,UAAe,IAAS;QACpB,IAAI,CAAC,IAAI,EAAE,CAAA;IACf,CAAC;IAEM,gCAAQ,GAAf;IACA,CAAC;IAEM,+BAAO,GAAd;IACA,CAAC;IAED,iHAAiH;IACjH,2BAA2B;IAE3B,6DAA6D;IAC7D,mCAAW,GAAX,UAAY,KAA0B,EAAE,KAAa;QACjD,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAA;QAC/B,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,GAAG,IAAI,EAAf,CAAe,CAAC,CAAA;QACjD,GAAG,CAAC,MAAM,GAAG,KAAK,CAAA;IACtB,CAAC;IACD,MAAM;IACN,iHAAiH;IAEjH,iHAAiH;IAEnG,4BAAI,GAAlB;;;;;;wBACU,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;wBACzC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;wBACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAA;wBAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAA;wBAClB,qBAAM,oBAAO,CAAC,iBAAiB,EAAE,EAAA;;wBAAxC,IAAI,GAAG,SAAiC;wBAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAA;wBAChC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,CAAA;wBAChC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,EAAE,EAAE,IAAS;4BACnC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,CAAA;4BAC5C,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAA;4BACtD,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAS,IAAI,CAAC,QAAQ,EAAE,UAAC,IAAI,EAAE,IAAI;gCACxD,IAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAA;gCACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM;oCACjC,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAA;oCAChB,EAAE,CAAC,UAAU,GAAG,EAAE,CAAA;oCAClB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oCACnB,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;oCAChB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;oCACX,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;iCACnB;qCAAM,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO;oCACvB,EAAE,CAAC,MAAM,GAAG,EAAE,CAAA;oCACd,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;oCACX,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;iCACnB;qCAAM,EAAE,OAAO;oCACZ,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAA;oCAChB,EAAE,CAAC,UAAU,GAAG,EAAE,CAAA;oCAClB,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oCACnB,EAAE,CAAC,MAAM,GAAG,IAAI,CAAA;oCAChB,IAAI,CAAC,CAAC,GAAG,EAAE,CAAA;oCACX,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAA;iCAChD;4BACL,CAAC,CAAC,CAAA;wBACN,CAAC,CAAC,CAAA;;;;;KACL;IAxEgB,aAAa;QADjC,OAAO;OACa,aAAa,CAyEjC;IAAD,oBAAC;CAzED,AAyEC,CAzE0C,EAAE,CAAC,WAAW,GAyExD;kBAzEoB,aAAa", "file": "", "sourceRoot": "/", "sourcesContent": ["import { gameHpr } from \"../../common/helper/GameHelper\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class NoticePnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private listSv_: cc.ScrollView = null // path://root/list_sv\n    private loadingNode_: cc.Node = null // path://root/loading_n\n    //@end\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: any) {\n        this.load()\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n\n    // path://root/list_sv/view/content/page/content/item/open_be\n    onClickOpen(event: cc.Event.EventTouch, _data: string) {\n        const msg = event.target.parent\n        msg.parent.children.forEach(m => m.active = true)\n        msg.active = false\n    }\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n    private async load() {\n        const empty = this.listSv_.Child('empty')\n        empty.active = false\n        this.listSv_.content.children[0].active = false\n        this.loadingNode_.active = true\n        const list = await gameHpr.getGameNoticeText()\n        this.loadingNode_.active = false\n        empty.active = list.length === 0\n        this.listSv_.Items(list, (it, data: any) => {\n            it.Child('top/new').active = data.Type === 0\n            it.Child('top/title', cc.RichText).string = data.Title\n            it.Child('content').Items<string>(data.Contents, (node, text) => {\n                const rt = node.Child('val', cc.RichText)\n                if (text.includes('<size=')) { // 小标题\n                    rt.fontSize = 48\n                    rt.lineHeight = 56\n                    rt.Color('#3F332F')\n                    rt.string = text\n                    node.x = -2\n                    node.height = 36\n                } else if (!text) { // 占位空行\n                    rt.string = ''\n                    node.x = 12\n                    node.height = 10\n                } else { // 更新内容\n                    rt.fontSize = 44\n                    rt.lineHeight = 52\n                    rt.Color('#756963')\n                    rt.string = text\n                    node.x = 12\n                    node.height = rt.node.height * rt.node.scaleY\n                }\n            })\n        })\n    }\n}\n"]}