import { ecode } from "../../common/constant/ECode";
import { gameHpr } from "../../common/helper/GameHelper";
import { payHelper } from "../../common/helper/PayHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { jsbHelper } from "../../common/helper/JsbHelper";
import UserModel from "../../model/common/UserModel";
import { MONTH_CARD } from "../../common/constant/Constant";
import { MonthlyCardType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";

const { ccclass } = cc._decorator;

@ccclass
export default class SubscriptionDescPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    private titleLbl_: cc.Label = null // path://root/bg/content/title/title_l
    private infoNode_: cc.Node = null // path://root/bg/content/info_n
    private totalDescNode_: cc.Node = null // path://root/bg/content/total_desc_n
    private buttonsNode_: cc.Node = null // path://root/bg/content/buttons_n
    private subscriptionButtonNode_: cc.Node = null // path://root/bg/content/buttons_n/subscription_button_n
    //@end

    private user: UserModel = null
    private restoreSubscriptions: any[] = []
    private cardType: MonthlyCardType = MonthlyCardType.SALE

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.user = this.getModel('user')
        await payHelper.checkPayInit()
        const list = await payHelper.getSubscriptions()
        this.restoreSubscriptions = list.slice()
    }

    public onEnter(type: MonthlyCardType) {
        this.cardType = type
        const index = MONTH_CARD.findIndex(m => m.TYPE === type)
        this.titleLbl_.setLocaleKey('ui.monthly_card_' + (index + 1))
        const isFirsPay = !this.user.getRechargeCountRecord()[type]
        const firstPayLbl = this.infoNode_.Child('1/content/layout/val')
        firstPayLbl.active = isFirsPay
        isFirsPay && firstPayLbl.setLocaleKey('ui.bracket', assetsMgr.lang('ui.first_pay_double'))
        const data = MONTH_CARD[index]
        this.infoNode_.Child('1/content/count', cc.Label).string = isFirsPay ? 'x' + data.FIRST * 2 : 'x' + data.FIRST
        this.infoNode_.Child('1/tip_desc').setLocaleKey('ui.subscription_imm_tip', isFirsPay ? data.FIRST * 4 : data.FIRST * 3)
        this.infoNode_.Child('2/count', cc.Label).string = 'x' + data.DAY
        this.infoNode_.Child('3/count', cc.Label).string = 'x' + data.EXTRA
        this.infoNode_.Child('2/line').active = this.infoNode_.Child('3').active = type === MonthlyCardType.SUPER

        const params = type === MonthlyCardType.SALE ? [data.DURATION, data.DAY * data.DURATION] : [data.DURATION, data.DAY * data.DURATION, data.EXTRA * data.DURATION]
        this.totalDescNode_.setLocaleKey('ui.subscription_total_value_' + (index + 1), params)

        const monthCard = MONTH_CARD.find(m => m.TYPE === type)
        const has = this.restoreSubscriptions.some(m => monthCard.RESTORES.includes(m.productId))
        this.buttonsNode_.Swih(has ? 'restore_buy_be' : 'subscription_button_n')[0]
        if (!has) {
            const node1 = this.subscriptionButtonNode_.Child('subscription_1_be/root')
            const node7 = this.subscriptionButtonNode_.Child('subscription_7_be/root')
            const node30 = this.subscriptionButtonNode_.Child('subscription_30_be/root')
            const key = '0'
            const it1 = node1.Swih(key)[0], it7 = node7.Swih(key)[0], it30 = node30.Swih(key)[0]
            it1.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'month', false)
            it7.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'month', true)
            it30.Child('val', cc.Label).string = payHelper.getSubPriceText(type, 'quarter', true)
        }
    }

    public onRemove() {
    }

    public onClean() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/bg/content/buttons_n/subscription_button_n/subscription_1_be
    onClickSubscription1(event: cc.Event.EventTouch, data: string) {
        const id = MONTH_CARD.find(m => m.TYPE === this.cardType).RECHARGES[0]
        id && this.buyProduct(id)
    }

    // path://root/bg/content/buttons_n/subscription_button_n/subscription_7_be
    onClickSubscription7(event: cc.Event.EventTouch, data: string) {
        this.buySubScription(this.cardType, 'month')
    }

    // path://root/bg/content/buttons_n/subscription_button_n/subscription_30_be
    onClickSubscription30(event: cc.Event.EventTouch, data: string) {
        this.buySubScription(this.cardType, 'quarter')
    }

    // path://root/bg/content/desc/desc1_be
    onClickDesc1(event: cc.Event.EventTouch, data: string) {
        cc.sys.openURL(gameHpr.getPrivacyPolicyUrl())
    }

    // path://root/bg/content/desc/desc2_be
    onClickDesc2(event: cc.Event.EventTouch, data: string) {
        cc.sys.openURL(gameHpr.getUserAgreementUrl())
    }

    // path://root/bg/content/buttons_n/restore_buy_be
    onClickRestoreBuy(event: cc.Event.EventTouch, data: string) {
        this.restore()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private buyProduct(productId: string) {
        if (this.user.isGuest()) {
            return viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: () => viewHelper.showPnl('common/BindAccount'),
                cancel: () => { }
            })
        } else if (!payHelper.isInitFinish()) {
            return viewHelper.showAlert('toast.please_wait_init_pay')
        }
        payHelper.buyProduct(productId).then(ok => {
            logger.print('6.buyProduct end. suc=' + ok + ', isValid=' + this.isValid)
            if (this.isValid && ok) {
                this.hide()
            }
        })
    }

    private buySubScription(cardType: MonthlyCardType, type: string) {
        if (this.user.isGuest()) {
            return viewHelper.showMessageBox('ui.guest_buy_subscription_tip', {
                okText: 'ui.button_bind_account',
                ok: () => viewHelper.showPnl('common/BindAccount'),
                cancel: () => { }
            })
        } else if (!payHelper.isInitFinish()) {
            return viewHelper.showAlert('toast.please_wait_init_pay')
        }
        payHelper.buySubscription(cardType, type).then(ok => {
            if (this.isValid && ok) {
                this.hide()
            }
        })
    }

    // 恢复购买
    private async restore() {
        if (this.restoreSubscriptions.length === 0) {
            return this.onEnter(this.cardType)
        }
        const order = this.restoreSubscriptions[0]
        const { err, data } = await gameHpr.net.request('lobby/HD_VerifySubOrder', {
            productId: order.productId,
            orderId: order.orderId,
            cpOrderId: order.cpOrderId,
            token: order.token,
            platform: order.platform,
            price: order.price,
            purchaseTime: order.purchaseTime,
            currencyType: order.currencyType,
            payAmount: order.payAmount,
        }, true)
        if (!this.isValid) {
            return
        } else if (err === ecode.SUBSCRIPTION_TIMEOUT || err === ecode.ORDER_VERIFY_API_ERROR) { //过期了
            this.restoreSubscriptions.shift()
            viewHelper.showLoadingWait(true)
            await jsbHelper.consumeOrder({ token: order.token, type: 'subs' })
            viewHelper.showLoadingWait(false)
            if (this.restoreSubscriptions.length > 0) {
                this.restore()
            } else {
                payHelper.cleanRestoredSubs()
                viewHelper.showAlert(err)
                this.onEnter(this.cardType)
            }
            return
        } else if (err) {
            return viewHelper.showAlert(err)
        } else {
            viewHelper.showAlert('toast.restore_buy_sub_succeed')
            this.hide()
        }
    }
}
