
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/area/AreaUIChildPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '33831IwdGdNprTaYKpPhHBx', 'AreaUIChildPnlCtrl');
// app/script/view/area/AreaUIChildPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var ccclass = cc._decorator.ccclass;
var AreaUIChildPnlCtrl = /** @class */ (function (_super) {
    __extends(AreaUIChildPnlCtrl, _super);
    function AreaUIChildPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.closeNode_ = null; // path://close_n
        _this.menuNode_ = null; // path://menu_n
        _this.hideNameNode_ = null; // path://menu_n/bg/options/hide_name_be_n
        _this.hideEquipNode_ = null; // path://menu_n/bg/options/hide_equip/hide_equip_be_n
        //@end
        _this.user = null;
        return _this;
    }
    AreaUIChildPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    AreaUIChildPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _this = this;
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.setParam({ isClean: false, isAct: false, isMask: false });
                this.closeNode_.on(cc.Node.EventType.TOUCH_END, function () { return _this.openAnimation(false); }, this);
                this.closeNode_.SetSwallowTouches(false);
                return [2 /*return*/];
            });
        });
    };
    AreaUIChildPnlCtrl.prototype.onEnter = function (pos) {
        this.showPawnInfo();
        this.menuNode_.setPosition(pos);
        this.openAnimation(true);
    };
    AreaUIChildPnlCtrl.prototype.onRemove = function () {
    };
    AreaUIChildPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://menu_n/bg/options/hide_name_be_n
    AreaUIChildPnlCtrl.prototype.onClickHideName = function (event, data) {
        audioMgr.playSFX('click');
        var hide = !this.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_NAME);
        this.hideNameNode_.Child('Background/checkmark').active = !hide;
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_NAME, hide);
        this.emit(EventType_1.default.CHANGE_SHOW_ARMY_NAME, hide);
    };
    // path://menu_n/bg/options/hide_equip/hide_equip_be_n
    AreaUIChildPnlCtrl.prototype.onClickHideEquip = function (event, data) {
        audioMgr.playSFX('click');
        var hide = !this.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP);
        this.hideEquipNode_.Child('Background/checkmark').active = !hide;
        this.user.setLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP, hide);
        this.emit(EventType_1.default.CHANGE_SHOW_PAWN_EQUIP, hide);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    // 士兵显示信息
    AreaUIChildPnlCtrl.prototype.showPawnInfo = function () {
        this.hideNameNode_.Child('Background/checkmark').active = !this.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_ARMY_NAME);
        this.hideEquipNode_.Child('Background/checkmark').active = !this.user.getLocalPreferenceData(Enums_1.PreferenceKey.SHOW_PAWN_EQUIP);
    };
    // 动画
    AreaUIChildPnlCtrl.prototype.openAnimation = function (open) {
        var _this = this;
        if (open) {
            this.menuNode_.scaleY = 0;
            cc.tween(this.menuNode_).to(0.2, { scaleY: 1 }, { easing: cc.easing.sineOut }).start();
        }
        else {
            this.menuNode_.scaleY = 1;
            cc.tween(this.menuNode_).to(0.1, { scaleY: 0 }).call(function () { return _this.hide(); }).start();
        }
    };
    AreaUIChildPnlCtrl = __decorate([
        ccclass
    ], AreaUIChildPnlCtrl);
    return AreaUIChildPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = AreaUIChildPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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