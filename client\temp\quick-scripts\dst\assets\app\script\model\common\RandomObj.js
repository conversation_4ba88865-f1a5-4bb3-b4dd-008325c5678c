
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/common/RandomObj.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '72d83C29MFPTZPc8S9GGUBa', 'RandomObj');
// app/script/model/common/RandomObj.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 自定义伪随机
var RandomObj = /** @class */ (function () {
    function RandomObj(seed) {
        this.seed = 0;
        this.seed = seed || Date.now();
    }
    RandomObj.prototype.setSeed = function (seed) {
        this.seed = seed || Date.now();
    };
    RandomObj.prototype.numn = function () {
        this.seed = (this.seed * 9301 + 49297) % 233280;
        var rand = Math.floor((this.seed / 233280.0) * 10000) * 0.0001;
        return rand;
    };
    RandomObj.prototype.get = function (min, max) {
        if (min >= max) {
            return min;
        }
        return Math.floor(this.numn() * (Math.max(max - min, 0) + 1)) + min;
    };
    // 概率
    RandomObj.prototype.chance = function (odds) {
        if (odds <= 0) {
            return false;
        }
        var mul = 100;
        return this.get(0, 100 * mul - 1) < odds * mul;
    };
    return RandomObj;
}());
exports.default = RandomObj;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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