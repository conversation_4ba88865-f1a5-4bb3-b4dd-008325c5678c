
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/ChatBarrageCmpt.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '776ecRv5FdLr7SSTGfVPM6g', 'ChatBarrageCmpt');
// app/script/view/common/ChatBarrageCmpt.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var ChatBarrageCmpt = /** @class */ (function (_super) {
    __extends(ChatBarrageCmpt, _super);
    function ChatBarrageCmpt() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.BARRAGE_RATIO_LIST = [0.25, 0.5, 1]; //一个弹幕的高度
        _this.BARRAGE_ITEM_HEIGHT = 48; //一个弹幕的高度
        _this.BARRAGE_MOVE_SPEED = 55; //弹幕移动速度
        _this.size = cc.size(0, 0);
        _this.barrageChannel = [];
        _this.caches = []; //
        _this.cloneItem = null;
        _this.itemPool = [];
        _this.barrageRatioSetting = {};
        _this.isOpen = false;
        return _this;
    }
    ChatBarrageCmpt.prototype.onLoad = function () {
        this.cloneItem = this.node.children[0];
        this.cloneItem.active = false;
        this.itemPool.push(this.cloneItem);
    };
    // 初始化弹幕数量
    ChatBarrageCmpt.prototype.init = function () {
        if (!this.cloneItem) {
            this.onLoad();
        }
        for (var i = 0; i <= 2; i++) {
            this.barrageRatioSetting[i] = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.BARRAGE_AREA_RATIO + i) || (i === 1 ? 1 : 0); // 联盟弹幕1 默认开启
        }
        this.cleanBarrageChannelItems();
        this.barrageChannel = [];
        var area = cc.winSize.height - 600;
        var height = this.node.height = area;
        this.node.y = area * 0.5 - height;
        var itemHeight = this.BARRAGE_ITEM_HEIGHT;
        var size = this.size = cc.size(cc.winSize.width, height);
        var count = Math.max(1, Math.floor(height / itemHeight));
        var startY = (size.height - count * itemHeight) / 2 + itemHeight / 2;
        var barrageRatios = this.BARRAGE_RATIO_LIST.map(function (m) {
            return { type: m / 0.25, index: Math.ceil(count * m) };
        });
        var _loop_1 = function (i) {
            var index = count - i;
            var areaRatioMap = { 4: true };
            barrageRatios.filter(function (m) { return index <= m.index; }).forEach(function (m) { return areaRatioMap[m.type] = true; });
            this_1.barrageChannel.push({ open: true, areaRatioMap: areaRatioMap, y: startY + i * itemHeight, items: [] });
        };
        var this_1 = this;
        for (var i = 0; i < count; i++) {
            _loop_1(i);
        }
        this.isOpen = true;
    };
    // 改变配置
    ChatBarrageCmpt.prototype.changeSetting = function (type, areaRatio) {
        this.barrageRatioSetting[type] = areaRatio;
    };
    ChatBarrageCmpt.prototype.clean = function () {
        // this.cleanBarrageChannelItems()
        // this.caches.length = 0
        // this.barrageChannel.length = 0
    };
    ChatBarrageCmpt.prototype.cleanBarrageChannelItems = function () {
        var _this = this;
        this.barrageChannel.forEach(function (m) {
            while (m.items.length > 0) {
                var node = m.items.pop();
                node.active = false;
                _this.itemPool.push(node);
            }
        });
    };
    // 添加弹幕
    ChatBarrageCmpt.prototype.addBarrage = function (data) {
        var _a, _b, _c, _d, _e;
        if (!this.isOpen || data.bannedSurplusTime > 0 || data.type === 3) {
            return;
        }
        else if (data.type === 2 && data.sender === GameHelper_1.gameHpr.getUid()) {
            return;
        }
        // // 检测是否联盟对应的频道
        // const curChannelUid = gameHpr.user.getTempPreferenceMap(PreferenceKey.ALLI_CHAT_CHANNEL) || '0'
        // if (data.type === 1) {
        //     if (curChannelUid !== '0') {
        //         return
        //     }
        // } else if (data.type === 4) {
        //     if (data.channel.split('_')[2] !== curChannelUid) {
        //         return
        //     }
        // }
        // 如果是世界子频道，子频道不一致则不显示
        if (data.type === 5) {
            var _f = __read(data.channel.split('_'), 2), _ = _f[0], channelType = _f[1], isZh = ['cn', 'tw', 'hk'].has(mc.lang);
            var cur = GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL);
            if (!cur || cur === '0') { // 如果没有选择过频道，要判断当前选择的语言   如果当前是世界频道，但不是当前语言
                cur = isZh ? 'zh' : mc.lang;
            }
            if (channelType !== cur) {
                return;
            }
        }
        // 频道开关判断
        var type = data.type === 4 ? 1 : data.type === 5 ? 0 : data.type;
        var isWorld = type === 0, isAlli = type === 1, channel_ = data.channel, key = Enums_1.PreferenceKey.BARRAGE_AREA_RATIO + type, openType = (_a = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(key)) !== null && _a !== void 0 ? _a : (isAlli ? 1 : 0);
        if (isWorld) {
            var cur = (_b = channel_.split('_')[1]) !== null && _b !== void 0 ? _b : channel_;
            if (cur && cur !== '0') {
                key += '_' + cur;
            }
            openType = (_c = GameHelper_1.gameHpr.user.getLocalPreferenceData(key)) !== null && _c !== void 0 ? _c : 0;
        }
        else if (isAlli) {
            var cur = (_d = channel_.split('_')[2]) !== null && _d !== void 0 ? _d : '0';
            if (cur && cur !== '0') {
                key += '_' + cur;
                openType = (_e = GameHelper_1.gameHpr.user.getLocalPreferenceDataBySid(key)) !== null && _e !== void 0 ? _e : 1;
            }
        }
        if (!openType) {
            return;
        }
        var channel = this.barrageChannel.filter(function (m) { return m.open && !!m.areaRatioMap[openType]; }).random();
        if (!channel) {
            return this.caches.push(data);
        }
        this.addItem(channel, data);
    };
    ChatBarrageCmpt.prototype.addItem = function (channel, data) {
        var _a;
        var node = this.itemPool.pop();
        if (!node) {
            node = cc.instantiate2(this.cloneItem, this.node);
        }
        node.active = true;
        node.x = this.size.width;
        node.y = channel.y;
        var type = data.type === 4 ? 1 : data.type === 5 ? 0 : data.type;
        var color = Constant_1.CHAT_BARRAGE_COLOR[type] || '#FFFFFF';
        var _b = __read(data.channel.split('_'), 3), channelType = _b[0], _ = _b[1], channelUid = _b[2];
        if (channelType === '1' || channelType === '4') { //联盟频道
            var nameText = '', job = GameHelper_1.gameHpr.alliance.getMemberJob(data.sender);
            if (job !== Enums_1.AllianceJobType.MEMBER) {
                nameText += '<' + assetsMgr.lang('ui.alliance_job_' + job) + '>';
            }
            if (channelType === '4') { //副频道
                var channel_1 = GameHelper_1.gameHpr.alliance.getChatChannels().find(function (m) { return m.uid === channelUid; }), name = (channel_1 === null || channel_1 === void 0 ? void 0 : channel_1.name) || '???';
                nameText += ' [' + name + ']';
                (channel_1 === null || channel_1 === void 0 ? void 0 : channel_1.color) && (color = '#' + channel_1.color);
            }
            node.Child('name', cc.Label).string = ut.nameFormator(data.senderNickname || GameHelper_1.gameHpr.getPlayerName(data.sender), 12) + nameText;
        }
        else if (channelType === '5') { //世界副频道
            var name = '???';
            var channel_2 = (_a = _ !== null && _ !== void 0 ? _ : GameHelper_1.gameHpr.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _a !== void 0 ? _a : '';
            if (channel_2 && channel_2 !== '0') {
                name = channel_2 === 'zh' ? '简繁中文' : Constant_1.LANGUAGE_TEXT_LIST.find(function (m) { return m.lang === channel_2; }).text;
            }
            node.Child('name', cc.Label).string = ut.nameFormator(data.senderNickname || GameHelper_1.gameHpr.getPlayerName(data.sender), 12) + ' [' + name + ']';
        }
        else {
            node.Child('name', cc.Label).string = ut.nameFormator(data.senderNickname || GameHelper_1.gameHpr.getPlayerName(data.sender), 12);
        }
        var emoji = node.Child('emoji');
        if (emoji.active = !!data.emoji) {
            node.Child('content', cc.Label).Color(color).string = ': ';
            ResHelper_1.resHelper.loadEmojiNode(data.emoji, emoji.Child('root'), 1, 'common');
        }
        else if (data.equip) {
            node.Child('content', cc.Label).Color(color).string = ': [' + data.equip.getChatName() + ']';
        }
        else if (data.portrayal) {
            node.Child('content', cc.Label).Color(color).string = ': [' + data.portrayal.getChatName() + ']';
        }
        else if (data.battleInfo) {
            node.Child('content', cc.Label).Color(color).string = ": [" + assetsMgr.lang('ui.title_army_battle_record') + "(" + MapHelper_1.mapHelper.indexToPoint(data.battleInfo.index).Join() + ")]";
        }
        else {
            node.Child('content', cc.Label).Color(color).string = ': ' + data.content;
        }
        channel.open = false;
        channel.items.push(node);
    };
    ChatBarrageCmpt.prototype.update = function (dt) {
        var _this = this;
        if (!this.isOpen) {
            return;
        }
        var speed = dt * this.BARRAGE_MOVE_SPEED;
        var open = false;
        this.barrageChannel.forEach(function (m) {
            for (var i = m.items.length - 1; i >= 0; i--) {
                var node = m.items[i];
                if (node.x < -node.width) {
                    m.items.splice(i, 1);
                    node.active = false;
                    _this.itemPool.push(node);
                }
                else {
                    node.x -= speed;
                }
            }
            // 判断最后一个是否已经走出来了
            if (m.items.length > 0) {
                var node = m.items.last();
                if (node.x + node.width < _this.size.width - 20) {
                    m.open = true;
                }
            }
            else {
                m.open = true;
            }
            if (m.open) {
                open = true;
            }
        });
        if (open && this.caches.length > 0) {
            this.addBarrage(this.caches.shift());
        }
    };
    ChatBarrageCmpt = __decorate([
        ccclass
    ], ChatBarrageCmpt);
    return ChatBarrageCmpt;
}(cc.Component));
exports.default = ChatBarrageCmpt;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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