
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/main/SeasonInfo.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4e9d0nH49NEHKytU6aP9yXd', 'SeasonInfo');
// app/script/model/main/SeasonInfo.ts

"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var GameHelper_1 = require("../../common/helper/GameHelper");
var SEASON_SCENE_EFFECT = ['SE_HUABAN', 'SE_RAIN', 'SE_SHUYE', 'SE_SNOWFLAKE'];
// 季节文本说明
var SEASON_EFFECT_DESC_TEXT = (_a = {},
    _a[Enums_1.WinCondType.TERRITORY_DISPUTE] = [
        [],
        [],
        [],
        [],
    ],
    _a[Enums_1.WinCondType.REBUILD_RUINS] = [
        [],
        [],
        [
            { key: 'ui.season_desc_7', params: [] },
            { key: 'ui.season_desc_3', params: [] },
        ],
        [
            { key: 'ui.season_desc_7', params: [] },
            { key: 'ui.season_desc_3', params: [] },
            { key: 'ui.season_desc_4', params: [] },
        ],
    ],
    _a[Enums_1.WinCondType.KARMIC_MAHJONG] = [
        [],
        [
            { key: 'ui.season_desc_9', params: [] }
        ],
        [
            { key: 'ui.season_desc_7', params: [] },
            { key: 'ui.season_desc_3', params: [] },
        ],
        [
            { key: 'ui.season_desc_7', params: [] },
            { key: 'ui.season_desc_3', params: [] },
            { key: 'ui.season_desc_10', params: [] },
        ],
    ],
    _a);
// 季节信息
var SeasonInfo = /** @class */ (function () {
    function SeasonInfo() {
        this.type = 0;
        this.surplusTime = 0;
        this.getTime = 0;
        this.effectMap = {};
        this.policys = {}; //当前政策槽位列表
    }
    SeasonInfo.prototype.init = function (data) {
        this.type = data.type;
        this.surplusTime = data.nextSeasonSurplusTime || 0;
        this.policys = GameHelper_1.gameHpr.fromSvrByPolicys(data.policys);
        this.getTime = Date.now();
        this.updateEffects();
        return this;
    };
    SeasonInfo.prototype.updateEffects = function () {
        // this.effectMap = {}
        if (this.type === 0) { //春
        }
        else if (this.type === 1) { //夏
        }
        else if (this.type === 2) { //秋
            // 清理免战
            GameHelper_1.gameHpr.world.cleanGeneralAvoidWar();
        }
        else if (this.type === 3) { //冬
            // 清理免战
            GameHelper_1.gameHpr.world.cleanGeneralAvoidWar();
        }
    };
    SeasonInfo.prototype.getSurplusTime = function () {
        return Math.max(0, this.surplusTime - (Date.now() - this.getTime));
    };
    SeasonInfo.prototype.getRunTime = function () {
        return Math.max(0, ut.Time.Day * 3 - this.getSurplusTime());
    };
    SeasonInfo.prototype.getPolicys = function () { return this.policys; };
    SeasonInfo.prototype.getEffect = function (type) {
        return this.effectMap[type] || 0;
    };
    SeasonInfo.prototype.getEffectDescTextByType = function (type) {
        var _a;
        var winType = GameHelper_1.gameHpr.world.getWinCondType();
        var texts = (((_a = SEASON_EFFECT_DESC_TEXT[winType]) === null || _a === void 0 ? void 0 : _a[type]) || []).slice();
        if (this.type === 1) { //夏季特殊处理 遗迹10点出现
            if (this.getRunTime() < ut.Time.Hour * 10) {
                texts.push({ key: 'ui.season_desc_5_0', params: [] });
            }
            else {
                texts.push({ key: 'ui.season_desc_5', params: [] });
                texts.push({ key: 'ui.season_desc_7', params: [] });
                texts.push({ key: 'ui.season_desc_8', params: [] });
            }
        }
        return texts;
    };
    // 改变基础资源费用
    SeasonInfo.prototype.changeBaseResCost = function (type, list) {
        var up = this.getEffect(type);
        if (up > 0) {
            list = list.map(function (m) {
                var v = m.clone();
                if (v.type <= Enums_1.CType.STONE) {
                    v.count = Math.round(v.count * up);
                }
                return v;
            });
        }
        return list;
    };
    // 获取当前季节的场景效果
    SeasonInfo.prototype.getCurrSceneEffectUrl = function () {
        return SEASON_SCENE_EFFECT[this.type] || '';
    };
    return SeasonInfo;
}());
exports.default = SeasonInfo;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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