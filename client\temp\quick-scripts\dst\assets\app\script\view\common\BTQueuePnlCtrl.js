
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/BTQueuePnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd73ccWxNZhN/IWc1P14GAeA', 'BTQueuePnlCtrl');
// app/script/view/common/BTQueuePnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BTQueuePnlCtrl = /** @class */ (function (_super) {
    __extends(BTQueuePnlCtrl, _super);
    function BTQueuePnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.inDoneNode_ = null; // path://root/title/in_done_n_be
        _this.listNode_ = null; // path://root/list_n
        //@end
        _this.player = null;
        return _this;
    }
    BTQueuePnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BT_QUEUE] = this.onUpdateBtQueue, _a.enter = true, _a),
        ];
    };
    BTQueuePnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.player = this.getModel('player');
                return [2 /*return*/];
            });
        });
    };
    BTQueuePnlCtrl.prototype.onEnter = function (data) {
        this.inDoneNode_.Child('lay/val', cc.Label).string = Constant_1.IN_DONE_BT_GOLD + '';
        this.emit(EventType_1.default.CLOSE_SELECT_CELL); //关闭地块信息面板
        this.onUpdateBtQueue();
    };
    BTQueuePnlCtrl.prototype.onRemove = function () {
    };
    BTQueuePnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_n/item/root/cancel_bt_be
    BTQueuePnlCtrl.prototype.onClickCancelBt = function (event, _) {
        var data = event.target.parent.parent.Data;
        if (data) {
            var player_1 = this.player;
            ViewHelper_1.viewHelper.showPnl('common/CancelBT', data, function (ok) {
                ok && player_1.cancelBtToServer(data.index, data.uid);
            });
        }
    };
    // path://root/title/in_done_n_be
    BTQueuePnlCtrl.prototype.onClickInDone = function (event, data) {
        if (GameHelper_1.gameHpr.user.getGold() < Constant_1.IN_DONE_BT_GOLD) {
            return ViewHelper_1.viewHelper.showGoldNotEnough();
        }
        var player = this.player;
        ViewHelper_1.viewHelper.showMessageBox('ui.in_done_bt_tip', {
            params: [Constant_1.IN_DONE_BT_GOLD],
            ok: function () { return player.inDoneBt().then(function (err) { return err && ViewHelper_1.viewHelper.showAlert(err); }); },
            cancel: function () { },
        });
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    // ----------------------------------------- custom function ----------------------------------------------------
    BTQueuePnlCtrl.prototype.onUpdateBtQueue = function () {
        var _this = this;
        var queues = this.player.getBtQueues();
        if (queues.length === 0) {
            return this.hide();
        }
        this.listNode_.Items(Math.max(queues.length, this.player.getBtQueueCount()), function (it, _, i) {
            var data = it.Data = queues[i];
            if (data) {
                var root = it.Swih('root')[0];
                root.Child('name/val', cc.Label).setLocaleKey(data.name);
                root.Child('name/lv', cc.Label).setLocaleKey('ui.lv', data.lv);
                var surplusTime = data.getSurplusTime();
                if (surplusTime > 0) {
                    root.Child('time').Color('#59A733');
                    root.Child('time', cc.LabelTimer).run(surplusTime * 0.001, function () {
                        if (_this.isValid) {
                            _this.player.removeLocalBTQueues(data.uid);
                            _this.onUpdateBtQueue();
                        }
                    });
                }
                else {
                    root.Child('time').Color('#936E5A');
                    root.Child('time', cc.LabelTimer).string = ut.millisecondFormat(data.needTime, 'h:m:ss');
                }
            }
            else {
                it.Swih('empty');
            }
        });
    };
    BTQueuePnlCtrl = __decorate([
        ccclass
    ], BTQueuePnlCtrl);
    return BTQueuePnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BTQueuePnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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