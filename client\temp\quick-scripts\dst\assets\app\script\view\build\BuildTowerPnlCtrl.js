
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildTowerPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b200cFzskJMJb3KdDlTjJas', 'BuildTowerPnlCtrl');
// app/script/view/build/BuildTowerPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BUILD_NAME = {
    2012: 'buildText.name_2103',
    2013: 'buildText.name_2102',
};
var BuildTowerPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildTowerPnlCtrl, _super);
    function BuildTowerPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.rootNode_ = null; // path://root_n
        //@end
        _this.data = null;
        return _this;
    }
    BuildTowerPnlCtrl.prototype.listenEventMaps = function () {
        var _a;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a)
        ];
    };
    BuildTowerPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/];
            });
        });
    };
    BuildTowerPnlCtrl.prototype.onEnter = function (data) {
        this.data = data;
        ViewHelper_1.viewHelper._updateBuildBaseInfo(this.rootNode_.Child('info/top'), data, this.key);
        this.updateBuildAttrInfo();
    };
    BuildTowerPnlCtrl.prototype.onRemove = function () {
    };
    BuildTowerPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root_n/bottom/buttons/up_be
    BuildTowerPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildTowerPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            this.rootNode_.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            this.updateBuildAttrInfo();
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    BuildTowerPnlCtrl.prototype.updateBuildAttrInfo = function () {
        var _this = this;
        var data = this.data, isMaxLv = data.isMaxLv();
        var attr = this.rootNode_.Child('info/attrs');
        var bottom = this.rootNode_.Child('bottom');
        // 显示下级信息和升级费用
        var top = attr.Child('top');
        top.Child('curr').setLocaleKey('ui.lv', data.lv);
        if (data.nextLvInfo) {
            top.Child('next').Color('#625450').setLocaleKey('ui.lv', data.nextLvInfo.lv);
        }
        else {
            top.Child('next').Color('#B6A591').setLocaleKey('ui.maxlv1');
        }
        // 显示属性
        var id = data.getBuildPawnId() || 0;
        var currJson = assetsMgr.getJsonData('pawnAttr', id * 1000 + data.lv);
        var nextJson = data.nextLvInfo ? assetsMgr.getJsonData('pawnAttr', id * 1000 + data.nextLvInfo.lv) : null;
        attr.Child('items').Items(2, function (it, data, i) {
            var key = 'ui.build_eff_desc_35_' + (i + 1);
            var currVal = i === 0 ? currJson.hp : currJson.attack;
            it.Child('curr/icon').active = false;
            it.Child('curr/val').setLocaleKey(key, BUILD_NAME[_this.data.id], currVal);
            if (it.Child('next').active = !!nextJson) {
                var nextVal = i === 0 ? nextJson.hp : nextJson.attack;
                it.Child('next/icon').active = false;
                it.Child('next/val', cc.Label).string = nextVal + '';
            }
            it.Child('line').active = i < 2 - 1;
        });
        // 刷新费用和按钮
        if (bottom.active = !isMaxLv) {
            ViewHelper_1.viewHelper.updateBuildBottomInfo(data, bottom);
        }
    };
    BuildTowerPnlCtrl = __decorate([
        ccclass
    ], BuildTowerPnlCtrl);
    return BuildTowerPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildTowerPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXHZpZXdcXGJ1aWxkXFxCdWlsZFRvd2VyUG5sQ3RybC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDQSwwREFBcUQ7QUFDckQsNkRBQXlEO0FBQ3pELDZEQUE0RDtBQUdwRCxJQUFBLE9BQU8sR0FBSyxFQUFFLENBQUMsVUFBVSxRQUFsQixDQUFtQjtBQUVsQyxJQUFNLFVBQVUsR0FBRztJQUNmLElBQUksRUFBRSxxQkFBcUI7SUFDM0IsSUFBSSxFQUFFLHFCQUFxQjtDQUM5QixDQUFBO0FBR0Q7SUFBK0MscUNBQWM7SUFBN0Q7UUFBQSxxRUFpRkM7UUEvRUcsMEJBQTBCO1FBQ2xCLGVBQVMsR0FBWSxJQUFJLENBQUEsQ0FBQyxnQkFBZ0I7UUFDbEQsTUFBTTtRQUVFLFVBQUksR0FBYSxJQUFJLENBQUE7O0lBMkVqQyxDQUFDO0lBekVVLDJDQUFlLEdBQXRCOztRQUNJLE9BQU87c0JBQ0QsR0FBQyxtQkFBUyxDQUFDLGVBQWUsSUFBRyxJQUFJLENBQUMsZUFBZSxFQUFFLFFBQUssR0FBRSxJQUFJO1NBQ25FLENBQUE7SUFDTCxDQUFDO0lBRVksb0NBQVEsR0FBckI7Ozs7OztLQUNDO0lBRU0sbUNBQU8sR0FBZCxVQUFlLElBQWM7UUFDekIsSUFBSSxDQUFDLElBQUksR0FBRyxJQUFJLENBQUE7UUFDaEIsdUJBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxVQUFVLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ2pGLElBQUksQ0FBQyxtQkFBbUIsRUFBRSxDQUFBO0lBQzlCLENBQUM7SUFFTSxvQ0FBUSxHQUFmO0lBQ0EsQ0FBQztJQUVNLG1DQUFPLEdBQWQ7UUFDSSxTQUFTLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFBO0lBQzNDLENBQUM7SUFFRCxpSEFBaUg7SUFDakgsMkJBQTJCO0lBRTNCLHFDQUFxQztJQUNyQyxxQ0FBUyxHQUFULFVBQVUsS0FBMEIsRUFBRSxJQUFZO1FBQzlDLG9CQUFPLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUE7SUFDekMsQ0FBQztJQUNELE1BQU07SUFDTixpSEFBaUg7SUFFekcsMkNBQWUsR0FBdkIsVUFBd0IsSUFBYztRQUNsQyxJQUFJLElBQUksQ0FBQyxJQUFJLENBQUMsR0FBRyxLQUFLLElBQUksQ0FBQyxHQUFHLEVBQUU7WUFDNUIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLENBQUMsc0JBQXNCLENBQUMsQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQTtZQUMzRSxJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQTtTQUM3QjtJQUNMLENBQUM7SUFDRCxpSEFBaUg7SUFFekcsK0NBQW1CLEdBQTNCO1FBQUEsaUJBZ0NDO1FBL0JHLElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxJQUFJLEVBQUUsT0FBTyxHQUFHLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQTtRQUNoRCxJQUFNLElBQUksR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxZQUFZLENBQUMsQ0FBQTtRQUMvQyxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsQ0FBQTtRQUM3QyxjQUFjO1FBQ2QsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQTtRQUM3QixHQUFHLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLFlBQVksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1FBQ2hELElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNqQixHQUFHLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxZQUFZLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDLENBQUE7U0FDL0U7YUFBTTtZQUNILEdBQUcsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxDQUFDLFlBQVksQ0FBQyxXQUFXLENBQUMsQ0FBQTtTQUMvRDtRQUNELE9BQU87UUFDUCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxDQUFBO1FBQ3JDLElBQU0sUUFBUSxHQUFHLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLEVBQUUsR0FBRyxJQUFJLEdBQUcsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFBO1FBQ3ZFLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLEVBQUUsR0FBRyxJQUFJLEdBQUcsSUFBSSxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFBO1FBQzNHLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxVQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsQ0FBQztZQUNyQyxJQUFNLEdBQUcsR0FBRyx1QkFBdUIsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQTtZQUM3QyxJQUFNLE9BQU8sR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFBO1lBQ3ZELEVBQUUsQ0FBQyxLQUFLLENBQUMsV0FBVyxDQUFDLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQTtZQUNwQyxFQUFFLENBQUMsS0FBSyxDQUFDLFVBQVUsQ0FBQyxDQUFDLFlBQVksQ0FBQyxHQUFHLEVBQUUsVUFBVSxDQUFDLEtBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEVBQUUsT0FBTyxDQUFDLENBQUE7WUFDekUsSUFBSSxFQUFFLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUMsUUFBUSxFQUFFO2dCQUN0QyxJQUFNLE9BQU8sR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFBO2dCQUN2RCxFQUFFLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxDQUFDLE1BQU0sR0FBRyxLQUFLLENBQUE7Z0JBQ3BDLEVBQUUsQ0FBQyxLQUFLLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxNQUFNLEdBQUcsT0FBTyxHQUFHLEVBQUUsQ0FBQTthQUN2RDtZQUNELEVBQUUsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBTSxHQUFHLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFBO1FBQ3ZDLENBQUMsQ0FBQyxDQUFBO1FBQ0YsVUFBVTtRQUNWLElBQUksTUFBTSxDQUFDLE1BQU0sR0FBRyxDQUFDLE9BQU8sRUFBRTtZQUMxQix1QkFBVSxDQUFDLHFCQUFxQixDQUFDLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQTtTQUNqRDtJQUNMLENBQUM7SUFoRmdCLGlCQUFpQjtRQURyQyxPQUFPO09BQ2EsaUJBQWlCLENBaUZyQztJQUFELHdCQUFDO0NBakZELEFBaUZDLENBakY4QyxFQUFFLENBQUMsV0FBVyxHQWlGNUQ7a0JBakZvQixpQkFBaUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDRWZmZWN0IH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9FbnVtc1wiO1xuaW1wb3J0IEV2ZW50VHlwZSBmcm9tIFwiLi4vLi4vY29tbW9uL2V2ZW50L0V2ZW50VHlwZVwiO1xuaW1wb3J0IHsgZ2FtZUhwciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL0dhbWVIZWxwZXJcIjtcbmltcG9ydCB7IHZpZXdIZWxwZXIgfSBmcm9tIFwiLi4vLi4vY29tbW9uL2hlbHBlci9WaWV3SGVscGVyXCI7XG5pbXBvcnQgQnVpbGRPYmogZnJvbSBcIi4uLy4uL21vZGVsL2FyZWEvQnVpbGRPYmpcIjtcblxuY29uc3QgeyBjY2NsYXNzIH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5jb25zdCBCVUlMRF9OQU1FID0ge1xuICAgIDIwMTI6ICdidWlsZFRleHQubmFtZV8yMTAzJyxcbiAgICAyMDEzOiAnYnVpbGRUZXh0Lm5hbWVfMjEwMicsXG59XG5cbkBjY2NsYXNzXG5leHBvcnQgZGVmYXVsdCBjbGFzcyBCdWlsZFRvd2VyUG5sQ3RybCBleHRlbmRzIG1jLkJhc2VQbmxDdHJsIHtcblxuICAgIC8vQGF1dG9jb2RlIHByb3BlcnR5IGJlZ2luXG4gICAgcHJpdmF0ZSByb290Tm9kZV86IGNjLk5vZGUgPSBudWxsIC8vIHBhdGg6Ly9yb290X25cbiAgICAvL0BlbmRcblxuICAgIHByaXZhdGUgZGF0YTogQnVpbGRPYmogPSBudWxsXG5cbiAgICBwdWJsaWMgbGlzdGVuRXZlbnRNYXBzKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgeyBbRXZlbnRUeXBlLlVQREFURV9CVUlMRF9MVl06IHRoaXMub25VcGRhdGVCdWlsZEx2LCBlbnRlcjogdHJ1ZSB9XG4gICAgICAgIF1cbiAgICB9XG5cbiAgICBwdWJsaWMgYXN5bmMgb25DcmVhdGUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uRW50ZXIoZGF0YTogQnVpbGRPYmopIHtcbiAgICAgICAgdGhpcy5kYXRhID0gZGF0YVxuICAgICAgICB2aWV3SGVscGVyLl91cGRhdGVCdWlsZEJhc2VJbmZvKHRoaXMucm9vdE5vZGVfLkNoaWxkKCdpbmZvL3RvcCcpLCBkYXRhLCB0aGlzLmtleSlcbiAgICAgICAgdGhpcy51cGRhdGVCdWlsZEF0dHJJbmZvKClcbiAgICB9XG5cbiAgICBwdWJsaWMgb25SZW1vdmUoKSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uQ2xlYW4oKSB7XG4gICAgICAgIGFzc2V0c01nci5yZWxlYXNlVGVtcFJlc0J5VGFnKHRoaXMua2V5KVxuICAgIH1cblxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIGJ1dHRvbiBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gICAgLy9AYXV0b2NvZGUgYnV0dG9uIGxpc3RlbmVyXG5cbiAgICAvLyBwYXRoOi8vcm9vdF9uL2JvdHRvbS9idXR0b25zL3VwX2JlXG4gICAgb25DbGlja1VwKGV2ZW50OiBjYy5FdmVudC5FdmVudFRvdWNoLCBkYXRhOiBzdHJpbmcpIHtcbiAgICAgICAgZ2FtZUhwci5jbGlja0J1aWxkVXAodGhpcy5kYXRhLCB0aGlzKVxuICAgIH1cbiAgICAvL0BlbmRcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBldmVudCBsaXN0ZW5lciBmdW5jdGlvbiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gICAgcHJpdmF0ZSBvblVwZGF0ZUJ1aWxkTHYoZGF0YTogQnVpbGRPYmopIHtcbiAgICAgICAgaWYgKHRoaXMuZGF0YS51aWQgPT09IGRhdGEudWlkKSB7XG4gICAgICAgICAgICB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnaW5mby90b3AvaWNvbi9sdi92YWwnKS5zZXRMb2NhbGVLZXkoJ3VpLmx2JywgZGF0YS5sdilcbiAgICAgICAgICAgIHRoaXMudXBkYXRlQnVpbGRBdHRySW5mbygpXG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gY3VzdG9tIGZ1bmN0aW9uIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuICAgIHByaXZhdGUgdXBkYXRlQnVpbGRBdHRySW5mbygpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMuZGF0YSwgaXNNYXhMdiA9IGRhdGEuaXNNYXhMdigpXG4gICAgICAgIGNvbnN0IGF0dHIgPSB0aGlzLnJvb3ROb2RlXy5DaGlsZCgnaW5mby9hdHRycycpXG4gICAgICAgIGNvbnN0IGJvdHRvbSA9IHRoaXMucm9vdE5vZGVfLkNoaWxkKCdib3R0b20nKVxuICAgICAgICAvLyDmmL7npLrkuIvnuqfkv6Hmga/lkozljYfnuqfotLnnlKhcbiAgICAgICAgY29uc3QgdG9wID0gYXR0ci5DaGlsZCgndG9wJylcbiAgICAgICAgdG9wLkNoaWxkKCdjdXJyJykuc2V0TG9jYWxlS2V5KCd1aS5sdicsIGRhdGEubHYpXG4gICAgICAgIGlmIChkYXRhLm5leHRMdkluZm8pIHtcbiAgICAgICAgICAgIHRvcC5DaGlsZCgnbmV4dCcpLkNvbG9yKCcjNjI1NDUwJykuc2V0TG9jYWxlS2V5KCd1aS5sdicsIGRhdGEubmV4dEx2SW5mby5sdilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRvcC5DaGlsZCgnbmV4dCcpLkNvbG9yKCcjQjZBNTkxJykuc2V0TG9jYWxlS2V5KCd1aS5tYXhsdjEnKVxuICAgICAgICB9XG4gICAgICAgIC8vIOaYvuekuuWxnuaAp1xuICAgICAgICBjb25zdCBpZCA9IGRhdGEuZ2V0QnVpbGRQYXduSWQoKSB8fCAwXG4gICAgICAgIGNvbnN0IGN1cnJKc29uID0gYXNzZXRzTWdyLmdldEpzb25EYXRhKCdwYXduQXR0cicsIGlkICogMTAwMCArIGRhdGEubHYpXG4gICAgICAgIGNvbnN0IG5leHRKc29uID0gZGF0YS5uZXh0THZJbmZvID8gYXNzZXRzTWdyLmdldEpzb25EYXRhKCdwYXduQXR0cicsIGlkICogMTAwMCArIGRhdGEubmV4dEx2SW5mby5sdikgOiBudWxsXG4gICAgICAgIGF0dHIuQ2hpbGQoJ2l0ZW1zJykuSXRlbXMoMiwgKGl0LCBkYXRhLCBpKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBrZXkgPSAndWkuYnVpbGRfZWZmX2Rlc2NfMzVfJyArIChpICsgMSlcbiAgICAgICAgICAgIGNvbnN0IGN1cnJWYWwgPSBpID09PSAwID8gY3Vyckpzb24uaHAgOiBjdXJySnNvbi5hdHRhY2tcbiAgICAgICAgICAgIGl0LkNoaWxkKCdjdXJyL2ljb24nKS5hY3RpdmUgPSBmYWxzZVxuICAgICAgICAgICAgaXQuQ2hpbGQoJ2N1cnIvdmFsJykuc2V0TG9jYWxlS2V5KGtleSwgQlVJTERfTkFNRVt0aGlzLmRhdGEuaWRdLCBjdXJyVmFsKVxuICAgICAgICAgICAgaWYgKGl0LkNoaWxkKCduZXh0JykuYWN0aXZlID0gISFuZXh0SnNvbikge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5leHRWYWwgPSBpID09PSAwID8gbmV4dEpzb24uaHAgOiBuZXh0SnNvbi5hdHRhY2tcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgnbmV4dC9pY29uJykuYWN0aXZlID0gZmFsc2VcbiAgICAgICAgICAgICAgICBpdC5DaGlsZCgnbmV4dC92YWwnLCBjYy5MYWJlbCkuc3RyaW5nID0gbmV4dFZhbCArICcnXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpdC5DaGlsZCgnbGluZScpLmFjdGl2ZSA9IGkgPCAyIC0gMVxuICAgICAgICB9KVxuICAgICAgICAvLyDliLfmlrDotLnnlKjlkozmjInpkq5cbiAgICAgICAgaWYgKGJvdHRvbS5hY3RpdmUgPSAhaXNNYXhMdikge1xuICAgICAgICAgICAgdmlld0hlbHBlci51cGRhdGVCdWlsZEJvdHRvbUluZm8oZGF0YSwgYm90dG9tKVxuICAgICAgICB9XG4gICAgfVxufVxuIl19