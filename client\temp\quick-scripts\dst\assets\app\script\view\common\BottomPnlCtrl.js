
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/BottomPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'cf420fEb35LKq/YUaFlWtDh', 'BottomPnlCtrl');
// app/script/view/common/BottomPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameHelper_1 = require("../../common/helper/GameHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var ccclass = cc._decorator.ccclass;
var BottomPnlCtrl = /** @class */ (function (_super) {
    __extends(BottomPnlCtrl, _super);
    function BottomPnlCtrl() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    //@autocode property begin
    //@end
    BottomPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    BottomPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false, isClean: false });
                return [2 /*return*/];
            });
        });
    };
    BottomPnlCtrl.prototype.onEnter = function (data) {
    };
    BottomPnlCtrl.prototype.onRemove = function () {
    };
    BottomPnlCtrl.prototype.onClean = function () {
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://android_back_be
    BottomPnlCtrl.prototype.onClickAndroidBack = function (event, data) {
        var currWindName = mc.currWindName;
        if (currWindName === 'area' || currWindName === 'playback') {
            ViewHelper_1.viewHelper.gotoWind(GameHelper_1.gameHpr.world.getSceneKey());
        }
        else {
            ViewHelper_1.viewHelper.showMessageBox('login.exit_game_tip', { ok: function () { return GameHelper_1.gameHpr.exitGame(); }, cancel: function () { } });
        }
    };
    BottomPnlCtrl = __decorate([
        ccclass
    ], BottomPnlCtrl);
    return BottomPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BottomPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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