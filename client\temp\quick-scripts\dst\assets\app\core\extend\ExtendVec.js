
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/core/extend/ExtendVec.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '847a6lbbhlF9rHKISg5L822', 'ExtendVec');
// app/core/extend/ExtendVec.ts

/**
 * Vec2扩展方法
 */
cc.Vec2.prototype.ID = function () {
    return this.x + '_' + this.y;
};
cc.Vec2.prototype.set2 = function (x, y) {
    this.x = x;
    this.y = y;
    return this;
};
// 比较
cc.Vec2.prototype.equals2 = function (x, y) {
    return this.x === x && this.y === y;
};
// 拼接字符串
cc.Vec2.prototype.Join = function (separator) {
    if (separator === void 0) { separator = ','; }
    return this.x + separator + this.y;
};
cc.Vec2.prototype.toVec3 = function () {
    this['z'] = 0;
    return this;
};
cc.Vec2.prototype.newVec3 = function () {
    return cc.v3(this.x, this.y, 0);
};
cc.Vec2.prototype.toJson = function () {
    return { x: this.x, y: this.y };
};
// 翻转
cc.Vec2.prototype.FlipX = function () {
    var x = this.x;
    this.x = this.y;
    this.y = x;
    return this;
};
cc.Vec2.prototype.floor = function () {
    this.x = Math.floor(this.x);
    this.y = Math.floor(this.y);
    return this;
};
cc.Vec2.prototype.ceil = function () {
    this.x = Math.ceil(this.x);
    this.y = Math.ceil(this.y);
    return this;
};
// 相加的长度
cc.Vec2.prototype.Length = function () {
    return Math.abs(this.x) + Math.abs(this.y);
};
// 自己想乘
cc.Vec2.prototype.SelfMul = function () {
    return this.x * this.y;
};
// 转成index
cc.Vec2.prototype.toIndex = function (size) {
    if (this.x < 0 || this.x >= size.x || this.y < 0 || this.y >= size.y) {
        return -1;
    }
    return this.y * size.x + this.x;
};
/**
 * Vec3扩展方法
 */
cc.Vec3.prototype.set2 = function (x, y, z) {
    this.x = x;
    this.y = y;
    this.z = z;
    return this;
};
// 比较
cc.Vec3.prototype.equals2 = function (x, y, z) {
    return this.x === x && this.y === y && (z === undefined || this.z === z);
};
// 拼接字符串
cc.Vec3.prototype.Join = function (separator) {
    if (separator === void 0) { separator = ','; }
    return this.x + separator + this.y + separator + this.z;
};
cc.Vec3.prototype.toVec2 = function () {
    return this;
};
cc.Vec3.prototype.newVec2 = function () {
    return cc.v2(this.x, this.y);
};

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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