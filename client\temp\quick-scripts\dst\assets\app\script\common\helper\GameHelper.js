
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/GameHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd0dcfzM0kRKy61yn68vsuPt', 'GameHelper');
// app/script/common/helper/GameHelper.ts

"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spread = (this && this.__spread) || function () {
    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gameHpr = void 0;
var version_1 = require("../../../../scene/version");
var CEffectObj_1 = require("../../model/common/CEffectObj");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var EquipInfo_1 = require("../../model/main/EquipInfo");
var PolicyObj_1 = require("../../model/main/PolicyObj");
var TaskCondObj_1 = require("../../model/common/TaskCondObj");
var AStar8_1 = require("../astar/AStar8");
var CameraCtrl_1 = require("../camera/CameraCtrl");
var Constant_1 = require("../constant/Constant");
var Enums_1 = require("../constant/Enums");
var EventType_1 = require("../event/EventType");
var JsbEvent_1 = require("../event/JsbEvent");
var LocalConfig_1 = require("../LocalConfig");
var JsbHelper_1 = require("./JsbHelper");
var MapHelper_1 = require("./MapHelper");
var PayHelper_1 = require("./PayHelper");
var PopupPnlHelper_1 = require("./PopupPnlHelper");
var ReddotHelper_1 = require("./ReddotHelper");
var ViewHelper_1 = require("./ViewHelper");
var WxHelper_1 = require("./WxHelper");
var PortrayalInfo_1 = require("../../model/common/PortrayalInfo");
var ECode_1 = require("../constant/ECode");
/**
 * 游戏逻辑帮助类
 */
var GameHelper = /** @class */ (function () {
    function GameHelper() {
        this.isRelease = LocalConfig_1.localConfig.RELEASE; //是否正式版
        this.isGameRuning = false; //是否运行游戏
        this.isNoviceMode = false; //是否新手村模式
        this.isEnterNovice = false; //是否新手村模式
        this.isEnterLobby = false; //是否首次进入大厅
        this.isEnterWorld = false; //是否进入过主场景
        this.clickTouchId = -1; //公共的触摸id
        this.buildTouchId = -1; //设施公共触摸id
        this.uiShowPawnData = null;
        this.gameDownloadUrl = ''; //游戏下载路径
        this.openCdk = false; //是否强行打开兑换码
        this.gameNoticeVersion = 0; //游戏公告版本号
        this.curBgmUrl = '';
        this.localInitTime = 0; //本地初始化时间
        this.serverInitTime = 0; //服务器初始化时间
        this.serverZoneOffset = -1000000; //服务器时区
        this.canBattleTime = [17, 0, 23, 0]; //可以交战的时间
        this.bazaarPublicityTime = [4, 0, 24, 0]; //市场公示时间 这个时间段为3个小时 其他为6个小时
        this.isGuestCanCreateAlli = false; //游客是否可以创建联盟
        this.fullServerPers = 600; //满服务器人数
        this.notEnterServerTime = 259200000; //开服多久不能进入游戏
        this.alliPolicySlotConf = {}; //联盟槽位配置
        this.pawnAstarMap = {};
        this.tempUnlockBuildCondText = {}; //临时记录需要建筑等级解锁的条件
        this.tempInviteMePlayerUid = ''; //邀请我的玩家
        this.gameNoticeTextMap = []; //游戏公告内容
        this._net = null;
        this._user = null;
        this._areaCenter = null;
        this._alliance = null;
        this._guide = null;
        this._weak_guide = null;
        this._message = null;
        this._chat = null;
        this._task = null;
        this._noviceServer = null;
        this._rank = null;
        this._friend = null;
        this._ground = null;
        this._book = null;
        this._playback = null;
        this._lobby = null;
        this._team = null;
        this._player = null;
        this._novice = null; //新手村
        this._world = null;
    }
    Object.defineProperty(GameHelper.prototype, "net", {
        get: function () { return this._net || (this._net = mc.getModel('net')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "user", {
        get: function () { return this._user || (this._user = mc.getModel('user')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "areaCenter", {
        get: function () { return this._areaCenter || (this._areaCenter = mc.getModel('areaCenter')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "alliance", {
        get: function () { return this._alliance || (this._alliance = mc.getModel('alliance')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "guide", {
        get: function () { return this._guide || (this._guide = mc.getModel('guide')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "weakGuide", {
        get: function () { return this._weak_guide || (this._weak_guide = mc.getModel('weak_guide')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "message", {
        get: function () { return this._message || (this._message = mc.getModel('message')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "chat", {
        get: function () { return this._chat || (this._chat = mc.getModel('chat')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "task", {
        get: function () { return this._task || (this._task = mc.getModel('task')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "noviceServer", {
        get: function () { return this._noviceServer || (this._noviceServer = mc.getModel('novice_server')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "rank", {
        get: function () { return this._rank || (this._rank = mc.getModel('rank')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "friend", {
        get: function () { return this._friend || (this._friend = mc.getModel('friend')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "ground", {
        get: function () { return this._ground || (this._ground = mc.getModel('ground')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "book", {
        get: function () { return this._book || (this._book = mc.getModel('book')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "playback", {
        get: function () { return this._playback || (this._playback = mc.getModel('playback')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "lobby", {
        get: function () { return this._lobby || (this._lobby = mc.getModel('lobby')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "team", {
        get: function () { return this._team || (this._team = mc.getModel('team')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "player", {
        get: function () { return this._player || (this._player = mc.getModel('player')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "novice", {
        get: function () { return this._novice || (this._novice = mc.getModel('novice')); },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(GameHelper.prototype, "world", {
        get: function () {
            if (this.isNoviceMode) {
                return this.novice;
            }
            return this._world || (this._world = mc.getModel('world'));
        },
        enumerable: false,
        configurable: true
    });
    GameHelper.prototype.getVersion = function () { return version_1.default.VERSION; };
    GameHelper.prototype.getAppType = function () { return version_1.default.getAppType(); };
    GameHelper.prototype.isInland = function () { return version_1.default.getAppType() === 'inland'; };
    GameHelper.prototype.isGLobal = function () { return version_1.default.getAppType() === 'global'; };
    GameHelper.prototype.getServerAreaList = function () { return version_1.default.getServerAreaList(); };
    GameHelper.prototype.getServerArea = function () { return version_1.default.getServerArea(); };
    GameHelper.prototype.setServerArea = function (val) { version_1.default.setServerArea(val); };
    GameHelper.prototype.getUid = function () { return this.user.getUid(); };
    GameHelper.prototype.getSid = function () { return this.user.getSid(); };
    GameHelper.prototype.isGuest = function () { return this.user.isGuest(); }; //是否游客
    // 初始化服务器时间
    GameHelper.prototype.initServerTime = function (now, serverInitTime, serverZoneOffset, delta) {
        this.localInitTime = now;
        serverInitTime = serverInitTime || 0;
        delta = delta || 0;
        this.serverInitTime = Math.max(serverInitTime - delta, 0) || now;
        this.serverZoneOffset = serverZoneOffset !== null && serverZoneOffset !== void 0 ? serverZoneOffset : (0 - new Date().getTimezoneOffset() / 60);
    };
    // 初始化服务器配置 来之http
    GameHelper.prototype.initServerConfigByHttp = function (data) {
        var _a, _b, _c;
        if (data) {
            this.setFullServerPers((_a = data.fullServerPers) !== null && _a !== void 0 ? _a : 1500);
            this.setNotEnterServerTime((_b = data.notEnterServerTime) !== null && _b !== void 0 ? _b : (ut.Time.Day * 3));
            this.setOpenGuide(!!data.openGuide);
            this.openCdk = cc.sys.isBrowser || !!data.openCdk;
            this.gameNoticeVersion = (_c = data.noticeVersion) !== null && _c !== void 0 ? _c : 0;
        }
    };
    // 初始化服务器配置
    GameHelper.prototype.initServerConfig = function (data) {
        if (data) {
            MapHelper_1.mapHelper.MAP_SIZE.set(data.mapSize || cc.v2(500, 500));
            this.setCanBattleTime(data.canBattleTime);
            this.setGuestCanCreateAlli(data.guestCanCreateAlli);
            this.alliPolicySlotConf = data.alliPolicySlotConf;
        }
    };
    // 获取服务器时区
    GameHelper.prototype.getServerZoneOffset = function () {
        if (this.serverZoneOffset === -1000000) {
            return null;
        }
        return this.serverZoneOffset;
    };
    // 获取服务器时间
    GameHelper.prototype.getServerNowTime = function () {
        return this.serverInitTime + (Date.now() - this.localInitTime);
    };
    // 根据时区获取服务器时间
    GameHelper.prototype.getServerNowTimeByZoneOffset = function () {
        var currentDate = new Date(this.getServerNowTime());
        // 获取当前的 UTC 时间的毫秒数
        var utcTime = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
        // 根据时区偏移量计算新的时间
        var newDate = new Date(utcTime + (this.serverZoneOffset * 3600000));
        return newDate.getTime();
    };
    GameHelper.prototype.setOpenGuide = function (val) {
        LocalConfig_1.localConfig.openGuide = !!val;
    };
    // 获取游戏公告
    GameHelper.prototype.getGameNoticeText = function () {
        return __awaiter(this, void 0, void 0, function () {
            var lang, list, url, res, data;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, ut.lock('getGameNoticeText')];
                    case 1:
                        _a.sent();
                        lang = mc.lang;
                        list = this.gameNoticeTextMap[lang];
                        if (!(list === undefined)) return [3 /*break*/, 3];
                        url = LocalConfig_1.localConfig.httpServerUrl.test;
                        if (this.isRelease) {
                            url = LocalConfig_1.localConfig.httpServerUrl[this.getServerArea()] || LocalConfig_1.localConfig.httpServerUrl.hk;
                        }
                        return [4 /*yield*/, this.net.post({ url: url + '/getNotice', showConnectFail: true, data: { lang: lang } })];
                    case 2:
                        res = _a.sent();
                        data = res === null || res === void 0 ? void 0 : res.data;
                        list = this.gameNoticeTextMap[lang] = (data === null || data === void 0 ? void 0 : data.list) || [];
                        _a.label = 3;
                    case 3:
                        ut.unlock('getGameNoticeText');
                        return [2 /*return*/, list];
                }
            });
        });
    };
    // 设置游客是否创建联盟
    GameHelper.prototype.setGuestCanCreateAlli = function (val) { this.isGuestCanCreateAlli = !!val; };
    GameHelper.prototype.getGuestCanCreateAlli = function () { return this.isGuestCanCreateAlli; };
    // 满服务器人数
    GameHelper.prototype.setFullServerPers = function (val) { this.fullServerPers = val; };
    GameHelper.prototype.getFullServerPers = function () { return this.fullServerPers; };
    // 开服多久后不能进入游戏
    GameHelper.prototype.setNotEnterServerTime = function (val) { this.notEnterServerTime = val; };
    GameHelper.prototype.getNotEnterServerTime = function () { return this.notEnterServerTime; };
    // 联盟配置槽位
    GameHelper.prototype.getAlliPolicySlotConfByIndex = function (i) { return this.alliPolicySlotConf[i] || 0; };
    GameHelper.prototype.getAlliPolicySlotConf = function () { return this.alliPolicySlotConf; };
    GameHelper.prototype.exitGame = function () {
        if (ut.isWechatGame()) {
            wx.exitMiniProgram();
        }
        else {
            cc.game.end();
        }
    };
    GameHelper.prototype.reinit = function () {
        this.clickTouchId = -1;
        this.buildTouchId = -1;
        this.tempUnlockBuildCondText = {};
        this.gameNoticeTextMap = [];
    };
    GameHelper.prototype.resetByLogin = function () {
        this.isNoviceMode = false;
        this.isEnterNovice = false;
        this.isEnterLobby = false;
        this.isEnterWorld = false;
        PayHelper_1.payHelper.clean();
        PopupPnlHelper_1.popupPnlHelper.clean();
        ReddotHelper_1.reddotHelper.clean();
        this.net.clean();
        this.rank.clean();
        this.user.cleanByLogin();
        this.guide.clean();
        // 游戏相关
        this.resetGame();
    };
    GameHelper.prototype.resetByLobby = function () {
        this.user.clean();
        // 游戏相关
        this.resetGame();
    };
    GameHelper.prototype.resetGame = function () {
        this.tempUnlockBuildCondText = {};
        this.curBgmUrl = '';
        this.uiShowPawnData = null;
        this.player.clean();
        this.world.clean();
        this.message.clean();
        this.areaCenter.clean();
        // audioMgr.stopBGM()
        CameraCtrl_1.cameraCtrl.reset();
        eventCenter.emit(EventType_1.default.CLEAN_SYS_MSG_NOTICE);
        eventCenter.emit(mc.Event.CLEAN_CACHE_WIND);
    };
    GameHelper.prototype.gameRestart = function () {
        var model = mc.getModel('login');
        model.isInitAsset1 = false;
        model.isInitAsset2 = false;
        assetsMgr.clean();
        audioMgr.stopAll();
        audioMgr.releaseAll();
        audioMgr.clean();
        WxHelper_1.wxHelper.destroyAllButton();
        if (cc.sys.isBrowser) {
            location.reload();
        }
        else if (ut.isMiniGame() && ut.checkVersion(wx.getSystemInfoSync().SDKVersion, '2.22.1')) {
            wx.restartMiniProgram();
        }
        else {
            cc.game.restart();
        }
    };
    // 获取当前服务器类型
    GameHelper.prototype.getServerType = function (sid) {
        sid = sid !== null && sid !== void 0 ? sid : this.getSid();
        return Math.floor(sid / 1000000);
    };
    // 获取服务器小类型
    GameHelper.prototype.getServerSubType = function (sid) {
        sid = sid !== null && sid !== void 0 ? sid : this.getSid();
        return Math.floor(sid / 100000 % 10);
    };
    // 获取服务器名字文本
    GameHelper.prototype.getServerNameById = function (serverId) {
        if (serverId <= 0) {
            return { key: this.isNoviceMode ? 'ui.server_name_novice' : 'ui.lobby', id: Math.abs(serverId) };
        }
        var maxType = Math.floor(serverId / 1000000);
        var minType = Math.floor(serverId / 100000 % 10);
        var id = serverId % 1000000;
        if (minType > 0) {
            id = serverId % 100000;
        }
        return { key: 'ui.server_name_' + maxType, id: id };
    };
    // 获取服务器运行天数
    GameHelper.prototype.getServerRunDay = function () {
        return Math.ceil(this.world.getServerRunTime() / ut.Time.Day);
    };
    // 是否排位区
    GameHelper.prototype.isRankServer = function () {
        return this.getServerType() === 2;
    };
    // 是否自由区
    GameHelper.prototype.isFreeServer = function () {
        var serverId = this.getSid();
        var maxType = Math.floor(serverId / 1000000);
        var minType = Math.floor(serverId / 100000 % 10);
        return maxType === 0 && minType === 1;
    };
    // 是否在大厅中
    GameHelper.prototype.isInLobby = function () {
        return !this.getSid();
    };
    // 是否在游戏中
    GameHelper.prototype.isInGame = function () {
        return !!this.getSid();
    };
    GameHelper.prototype.playLobbyBgm = function () {
        audioMgr.resetBGMRecord();
        this.playBgBgm('lobby/sound_bgm_004', 0.65);
    };
    GameHelper.prototype.playMainBgm = function () {
        if (!this.curBgmUrl || this.curBgmUrl.startsWith('area/sound_bgm_003') || this.curBgmUrl === 'lobby/sound_bgm_004') {
            var url = 'common/sound_bgm_001';
            if (this.user.getLoginDayCount() >= 3 && this.isCanOccupyTime()) {
                url = 'common/sound_bgm_002';
            }
            this.playBgBgm(url, 0.65);
        }
    };
    GameHelper.prototype.playAreaBgm = function (ok) {
        if (ok) {
            var val = 0.65;
            var cell = this.world.getLookCell();
            if (cell) {
                if (cell.owner) { //pvp
                    this.playBgBgm('area/sound_bgm_003', val);
                }
                else {
                    var _a = __read(cell.getLandDifficultyLv(), 2), type = _a[0], lv = _a[1];
                    // console.log(type, lv)
                    if (type <= 1) { //简单
                        this.playBgBgm('area/sound_bgm_003_B', val);
                    }
                    else if (type == 4 && lv <= 3) { //地狱 1 2 3
                        this.playBgBgm('area/sound_bgm_003_A', val);
                    }
                    else { //其他
                        this.playBgBgm('area/sound_bgm_003', val);
                    }
                }
            }
            else {
                this.playBgBgm('area/sound_bgm_003', val);
            }
        }
        else {
            this.playMainBgm();
        }
    };
    GameHelper.prototype.playBgBgm = function (url, val) {
        if (url !== this.curBgmUrl) {
            this.curBgmUrl = url;
            audioMgr.playFadeInBGM(this.curBgmUrl, val);
        }
    };
    GameHelper.prototype.stopBgBgm = function () {
        this.curBgmUrl = '';
        audioMgr.stopBGM();
    };
    // 获取配置
    GameHelper.prototype.getConfigByArea = function () {
        return LocalConfig_1.localConfig.config[this.getServerArea()] || LocalConfig_1.localConfig.config.hk;
    };
    // 获取服务器信息
    GameHelper.prototype.getServerInfoUrl = function () {
        if (!this.isRelease) {
            return LocalConfig_1.localConfig.getServerInfoUrl.test;
        }
        return LocalConfig_1.localConfig.getServerInfoUrl[this.getServerArea()] || LocalConfig_1.localConfig.getServerInfoUrl.hk;
    };
    // 获取http服务器url
    GameHelper.prototype.getHttpServerUrl = function () {
        if (!this.isRelease) {
            return LocalConfig_1.localConfig.httpServerUrl.test;
        }
        return LocalConfig_1.localConfig.httpServerUrl[this.getServerArea()] || LocalConfig_1.localConfig.httpServerUrl.hk;
    };
    // 获取游戏下载地址
    GameHelper.prototype.getGameDownloadUrl = function () {
        if (this.gameDownloadUrl) {
            return this.gameDownloadUrl;
        }
        var url = this.getConfigByArea().downloadUrl;
        if (ut.isIos()) {
            return url.ios;
        }
        else if (ut.isAndroid()) {
            return url.google;
        }
        return '';
    };
    // 获取服务器地址
    GameHelper.prototype.getServerUrl = function () {
        if (!this.isRelease) {
            return LocalConfig_1.localConfig.server_test;
        }
        else if (this.isGLobal()) {
            return LocalConfig_1.localConfig.servers[this.getServerArea()] || LocalConfig_1.localConfig.servers.hk;
        }
        else if (!ut.isWechatGame() || WxHelper_1.wxHelper.isRelease()) {
            return LocalConfig_1.localConfig.servers.china;
        }
        return LocalConfig_1.localConfig.server_test;
    };
    // 打开dc
    GameHelper.prototype.openDiscord = function () {
        cc.sys.openURL('https://discord.gg/8FWEpcES6q');
    };
    // 重置服务器选择
    GameHelper.prototype.resetSelectServer = function (wait) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var _b, data, err;
            return __generator(this, function (_c) {
                switch (_c.label) {
                    case 0: return [4 /*yield*/, this.net.request('lobby/HD_ResetSelectServer', {}, wait)];
                    case 1:
                        _b = _c.sent(), data = _b.data, err = _b.err;
                        this.user.setSid(0);
                        this.user.setPlaySid((_a = data === null || data === void 0 ? void 0 : data.playSid) !== null && _a !== void 0 ? _a : this.user.getPlaySid());
                        this.user.setTotalGameCount((data === null || data === void 0 ? void 0 : data.totalGameCount) || [0, 0]);
                        this.user.setTotalNewbieCount((data === null || data === void 0 ? void 0 : data.totalNewbieCount) || [0, 0]);
                        this.user.setPassNewbieIndex((data === null || data === void 0 ? void 0 : data.passNewbieIndex) || 0);
                        this.user.setGiveupCount((data === null || data === void 0 ? void 0 : data.giveupCount) || 0);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 包名
    GameHelper.prototype.getBundleId = function () {
        if (CC_JSB) {
            return this.isInland() ? 'twgame.inland.acers' : 'twgame.global.acers';
        }
        return '';
    };
    // 获取运行平台
    GameHelper.prototype.getRunPlatform = function () {
        if (ut.isMobile()) {
            return ut.isAndroid() ? 'android' : 'ios';
        }
        else if (ut.isMiniGame()) {
            return typeof qq != 'undefined' ? 'qq' : 'wx';
        }
        return 'none';
    };
    // 获取商店平台
    GameHelper.prototype.getShopPlatform = function () {
        var isGLobal = this.isGLobal();
        if (ut.isIos()) {
            return isGLobal ? 'ios_global' : 'ios_inland';
        }
        else if (ut.isAndroid()) {
            return isGLobal ? 'google' : 'taptap';
        }
        else if (cc.sys.isBrowser) {
            return 'web';
        }
        return cc.sys.os + '_' + cc.sys.platform;
    };
    // 获取进入参数
    GameHelper.prototype.getEnterQuery = function () {
        if (ut.isMiniGame()) {
            return WxHelper_1.wxHelper.getEnterInfo();
        }
        return JsbHelper_1.jsbHelper.getAwakeParams();
    };
    // // 获取邀请我的玩家UID
    // public getInviteMePlayerUid() {
    //     const launchInfo = this.getEnterQuery()
    //     logger.print('EnterQuery =', launchInfo)
    //     const type = Number(launchInfo?.type) || ShareType.NONE
    //     if (type === ShareType.INVITE || type === ShareType.ON_SHARE_APP_MESSAGE) { //邀请信息
    //         return launchInfo.uid || ''
    //     }
    //     return ''
    // }
    GameHelper.prototype.initInviteMePlayerUid = function () {
        var launchInfo = this.getEnterQuery();
        logger.print('EnterQuery =', launchInfo);
        var type = Number(launchInfo === null || launchInfo === void 0 ? void 0 : launchInfo.type) || Enums_1.ShareType.NONE;
        if (type === Enums_1.ShareType.INVITE || type === Enums_1.ShareType.ON_SHARE_APP_MESSAGE) { //邀请信息
            this.tempInviteMePlayerUid = launchInfo.uid || '';
        }
    };
    GameHelper.prototype.getInviteMePlayerUid = function () {
        return this.tempInviteMePlayerUid;
    };
    // 获取隐私政策
    GameHelper.prototype.getPrivacyPolicyUrl = function () {
        if (this.isGLobal()) {
            var lang = mc.lang;
            if (lang !== 'cn' && lang !== 'jp') {
                lang = 'en';
            }
            return "https://www.dhgames.cn/other/privacy-" + lang + ".html";
        }
        return 'http://www.twomiles.cn/privacycn.html';
    };
    // 获取用户协议
    GameHelper.prototype.getUserAgreementUrl = function () {
        if (this.isGLobal()) {
            var lang = mc.lang;
            if (lang !== 'cn' && lang !== 'jp') {
                lang = 'en';
            }
            return "https://www.dhgames.cn/other/register-" + lang + ".html";
        }
        return 'https://littlesnail.twomiles.cn/register-cn.html';
    };
    // 获取注销协议
    GameHelper.prototype.getLogoutAgreementUrl = function () {
        if (this.isGLobal()) {
            var lang = mc.lang;
            if (lang !== 'cn') {
                lang = 'en';
            }
            return "https://www.dhgames.cn/other/game-logout-" + lang + ".html";
        }
        return 'http://www.twomiles.cn/logout_cn.html';
    };
    // 获取玩家人数
    GameHelper.prototype.getPlayerCount = function () {
        var count = 0;
        var obj = this.world.getAllPlayerMap();
        for (var k in obj) {
            if (!obj[k].spectateIndex) {
                count += 1;
            }
        }
        return count;
    };
    GameHelper.prototype.getEmptyPlayerInfo = function (uid, index) {
        return {
            uid: uid,
            mainCityIndex: index,
            nickname: '???',
            headIcon: '',
            personalDesc: '',
            title: 0,
            allianceUid: '',
            allianceName: '',
            allianceIcon: 0,
            cells: new Map(),
            towerLvMap: {},
            policys: {},
            rodeleroCadetLv: 0,
            maxLandCount: 0,
            isGiveupGame: false,
            cityOutputMap: {},
            spectateIndex: 0,
            isSettled: false,
            farmType: 1,
        };
    };
    GameHelper.prototype.initPlayerInfo = function (player, data) {
        if (!player || !data) {
            return null;
        }
        player.mainCityIndex = data.mainCityIndex;
        player.nickname = data.nickname || '???';
        player.headIcon = data.headIcon || '';
        player.personalDesc = data.personalDesc || '';
        player.title = data.title || 0;
        player.allianceUid = data.allianceUid;
        player.allianceName = data.allianceName;
        player.allianceIcon = data.allianceIcon;
        player.towerLvMap = data.towerLvMap || {};
        player.policys = this.fromSvrByPolicys(data.policys || {});
        player.rodeleroCadetLv = data.rodeleroCadetLv || 0;
        player.maxLandCount = data.maxLandCount || 0;
        player.isGiveupGame = !!data.isGiveupGame;
        player.cityOutputMap = this.wrapCityOutputMap(data.cityOutputMap || {}, player.cityOutputMap);
        player.spectateIndex = data.spectateIndex || 0;
        player.injuryPawns = data.injuryPawns || [];
        player.curingQueues = data.curingQueues || [];
        player.isSettled = !!data.isSettled;
        player.farmType = data.farmType || 1;
        return player;
    };
    GameHelper.prototype.wrapCityOutputMap = function (data, old) {
        old = old || {};
        var obj = {};
        for (var index in data) {
            delete old[index];
            var arr = obj[index] = data[index].list || [], len = arr.length;
            if (len >= 2) {
                this.world.setCityOutput(Number(index), new CTypeObj_1.default().init(Enums_1.CType.BASE_RES_2, 0, arr.reduce(function (val, cur) { return cur.count + val; }, 0)));
            }
            else if (len >= 1) {
                this.world.setCityOutput(Number(index), arr[0]);
            }
        }
        // 删除多余的
        for (var index in old) {
            this.world.setCityOutput(Number(index), null);
        }
        return obj;
    };
    // 获取玩家信息
    GameHelper.prototype.getPlayerInfo = function (uid) {
        if (!uid) {
            return null;
        }
        return this.world.getPlayerInfo(uid);
    };
    // 获取玩家名字
    GameHelper.prototype.getPlayerName = function (uid) {
        var _a;
        return ((_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.nickname) || '???';
    };
    // 获取玩家头像
    GameHelper.prototype.getPlayerHead = function (uid) {
        var _a;
        return ((_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.headIcon) || '';
    };
    // 获取玩家联盟名字
    GameHelper.prototype.getPlayerAlliName = function (uid) {
        var _a;
        return ((_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.allianceName) || '';
    };
    // 获取联盟icon
    GameHelper.prototype.getPlayerAlliIcon = function (uid) {
        var _a;
        return (_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.allianceIcon;
    };
    // 查询本地玩家
    GameHelper.prototype.queryLocalPlayer = function (name, idx) {
        var index = -1, tuid = '';
        var tplr = this.world.getPlayerByNameOrUID(name);
        if (tplr) {
            tuid = tplr.uid;
            index = tplr.mainCityIndex;
        }
        else if (idx != -1) {
            index = idx;
        }
        else {
            return null;
        }
        var cell = this.world.getMapCellByIndex(index);
        if (!cell || cell.owner == '' || cell.owner == this.user.getUid() || (tuid != '' && cell.owner != tuid)) {
            return null;
        }
        return tplr || this.getPlayerInfo(cell.owner);
    };
    // 获取用户称号列表
    GameHelper.prototype.getUserTitles = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var now, info, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!uid || uid === this.getUid()) {
                            return [2 /*return*/, { title: this.user.getTitle(), titles: this.user.getUnlockTitles() }];
                        }
                        plr = plr || this.getPlayerInfo(uid) || this.lobby.getUser(uid);
                        if (!plr) {
                            return [2 /*return*/, null];
                        }
                        now = Date.now();
                        info = plr.titleInfo;
                        if (!info) {
                            info = plr.titleInfo = { title: 0, time: 0, titles: [], reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserTitles', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        info.reqing = false;
                        info.time = now;
                        info.titles = (data === null || data === void 0 ? void 0 : data.titles) || [];
                        info.title = (data === null || data === void 0 ? void 0 : data.title) || 0;
                        return [2 /*return*/, info];
                }
            });
        });
    };
    GameHelper.prototype.getUserTitle = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var now, info, _a, err, data;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!uid || uid === this.getUid()) {
                            return [2 /*return*/, this.user.getTitle()];
                        }
                        else if (plr === null || plr === void 0 ? void 0 : plr.title) {
                            return [2 /*return*/, plr.title];
                        }
                        plr = this.getPlayerInfo(uid);
                        if (plr) {
                            return [2 /*return*/, plr.title];
                        }
                        plr = this.lobby.getUser(uid);
                        if (!plr) {
                            return [2 /*return*/, 0];
                        }
                        now = Date.now();
                        info = plr.titleInfo;
                        if (!info) {
                            info = plr.titleInfo = { title: 0, time: 0, titles: [], reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info.title];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info.title];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserTitles', { uid: uid }, true)];
                    case 1:
                        _a = _b.sent(), err = _a.err, data = _a.data;
                        info.reqing = false;
                        info.time = now;
                        info.titles = (data === null || data === void 0 ? void 0 : data.titles) || [];
                        info.title = (data === null || data === void 0 ? void 0 : data.title) || 0;
                        return [2 /*return*/, info.title];
                }
            });
        });
    };
    // 获取用户人气
    GameHelper.prototype.getUserPopularity = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var now, info, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        plr = plr || this.getPlayerInfo(uid) || this.lobby.getUser(uid);
                        if (!plr) {
                            return [2 /*return*/, null];
                        }
                        now = Date.now();
                        info = plr.popularityInfo;
                        if (!info) {
                            info = plr.popularityInfo = { records: [], time: 0, list: [], reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info];
                        }
                        info.reqing = true;
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserPopularity', { uid: uid })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        info.reqing = false;
                        info.time = now;
                        info.records = (data === null || data === void 0 ? void 0 : data.records) || [];
                        info.list = ((data === null || data === void 0 ? void 0 : data.list) || [{ value: [101, 0] }]).map(function (m) { return m.value; });
                        info.list.sort(function (a, b) {
                            var aw = (a[0] === 101 || a[0] === 102 ? 1 : 0) * 1000000 + a[1];
                            var bw = (b[0] === 101 || b[0] === 102 ? 1 : 0) * 1000000 + b[1];
                            return bw - aw;
                        });
                        return [2 /*return*/, info];
                }
            });
        });
    };
    // 获取用户评分
    GameHelper.prototype.getUserRankScore = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var now, info, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (uid === this.getUid()) {
                            return [2 /*return*/, { score: this.user.getRankScore(), count: this.user.getAccTotalRankCount() }];
                        }
                        plr = plr || this.getPlayerInfo(uid);
                        if (!plr) {
                            return [2 /*return*/, { score: 0, count: 0 }];
                        }
                        now = Date.now();
                        info = plr.rankScoreInfo;
                        if (!info) {
                            info = plr.rankScoreInfo = { time: 0, data: { score: 0, count: 0 }, reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info.data];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info.data];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserRankScore', { uid: uid })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        info.reqing = false;
                        info.time = now;
                        info.data = { score: (data === null || data === void 0 ? void 0 : data.rankScore) || 0, count: (data === null || data === void 0 ? void 0 : data.accTotalRankCount) || 0 };
                        return [2 /*return*/, info.data];
                }
            });
        });
    };
    // 获取用户总局数
    GameHelper.prototype.getUserTotalGameCount = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var now, info, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (uid === this.getUid()) {
                            return [2 /*return*/, this.user.getTotalGameCount()];
                        }
                        plr = plr || this.getPlayerInfo(uid);
                        if (!plr) {
                            return [2 /*return*/, [0, 0]];
                        }
                        now = Date.now();
                        info = plr.totalGameCount;
                        if (!info) {
                            info = plr.totalGameCount = { time: 0, count: [0, 0], reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info.count];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info.count];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserTotalGameCount', { uid: uid })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        info.reqing = false;
                        info.time = now;
                        info.count = (data === null || data === void 0 ? void 0 : data.totalGameCount) || [0, 0];
                        return [2 /*return*/, info.count];
                }
            });
        });
    };
    // 获取用户简介
    GameHelper.prototype.getUserPersonalDesc = function (uid, plr) {
        return __awaiter(this, void 0, void 0, function () {
            var playerInfo, now, info, _a, data, err;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (uid === this.getUid()) {
                            return [2 /*return*/, this.user.getPersonalDesc()];
                        }
                        playerInfo = this.getPlayerInfo(uid);
                        if (playerInfo) {
                            return [2 /*return*/, playerInfo.personalDesc];
                        }
                        now = Date.now();
                        info = plr === null || plr === void 0 ? void 0 : plr.personalDesc;
                        if (!plr) {
                            info = { time: 0, text: '', reqing: false };
                        }
                        else if (!info) {
                            info = plr.personalDesc = { time: 0, text: '', reqing: false };
                        }
                        else if (info.reqing) {
                            return [2 /*return*/, info.text];
                        }
                        else if (info.time > 0 && now - info.time < ut.Time.Minute) {
                            return [2 /*return*/, info.text];
                        }
                        return [4 /*yield*/, this.net.request('lobby/HD_GetUserPersonalDesc', { uid: uid })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        info.reqing = false;
                        info.time = now;
                        info.text = (data === null || data === void 0 ? void 0 : data.personalDesc) || '';
                        return [2 /*return*/, info.text];
                }
            });
        });
    };
    // 包装政策
    GameHelper.prototype.fromSvrByPolicys = function (policys) {
        var obj = {};
        for (var index in policys) {
            obj[index] = new PolicyObj_1.default().fromSvr({ lv: Number(index), id: policys[index] }).init();
        }
        return obj;
    };
    // 研究信息
    GameHelper.prototype.fromSvrByStudyData = function (dataMap, studyClass) {
        var obj = {};
        for (var lv in dataMap) {
            obj[lv] = new studyClass().fromSvr(dataMap[lv]).init();
        }
        return obj;
    };
    // 检测研究槽位红点
    GameHelper.prototype.checkStudySlotsReddot = function (slots) {
        var ok = false;
        for (var k in slots) {
            if (slots[k].isCanStudy()) {
                ok = true;
                break;
            }
        }
        return ok;
    };
    // 获取玩家的政策基础信息
    GameHelper.prototype.getPlayerPolicysBaseInfo = function (uid) {
        var _this = this;
        var _a;
        var allianceUid = '', sumEmbassyLv = 0, myPolicys = {};
        if (!uid || uid === this.getUid()) {
            myPolicys = this.player.getPolicySlots();
            allianceUid = this.player.getAllianceUid();
            sumEmbassyLv = this.alliance.getSumEmbassyLv();
        }
        else {
            var plr = this.getPlayerInfo(uid);
            if (!plr) {
                return [];
            }
            myPolicys = plr.policys;
            allianceUid = plr.allianceUid;
            sumEmbassyLv = ((_a = this.world.getAlliBaseInfo(allianceUid)) === null || _a === void 0 ? void 0 : _a.sumEmbassyLv) || 0;
        }
        var policys = [];
        var policyIndexMap = {};
        // 自己的政策
        for (var k in myPolicys) {
            this.addPolicys(policyIndexMap, policys, myPolicys[k]);
        }
        // 联盟的政策
        var alliPolicys = this.world.getAlliPolicysByUid(allianceUid);
        var conf = this.alliPolicySlotConf;
        for (var k in alliPolicys) {
            if (sumEmbassyLv < (conf[k] || conf[0])) {
                continue;
            }
            this.addPolicys(policyIndexMap, policys, alliPolicys[k], 2);
        }
        // 季节的政策
        Object.values(this.world.getSeasonPolicys()).sort(function (a, b) { return a.lv - b.lv; }).forEach(function (m, i) {
            _this.addPolicys(policyIndexMap, policys, m, 10 + Number(m.lv));
        });
        return policys;
    };
    GameHelper.prototype.addPolicys = function (policyIndexMap, policys, data, style) {
        var _a;
        var id = data.id;
        if (id) {
            var i = (_a = policyIndexMap[id]) !== null && _a !== void 0 ? _a : -1;
            if (i >= 0) {
                policys[i].up += 1;
                policys[i].styles.push(style);
            }
            else {
                policyIndexMap[id] = policys.push({ id: id, up: 1, type: data.type, values: data.values, styles: [style] }) - 1;
            }
        }
    };
    // 获取玩家季节政策
    GameHelper.prototype.getSeasonPolicies = function () {
        var _this = this;
        var policyIndexMap = {};
        var policys = [];
        Object.values(this.world.getSeasonPolicys()).sort(function (a, b) { return a.lv - b.lv; }).forEach(function (m, i) {
            _this.addPolicys(policyIndexMap, policys, m, 10 + Number(m.lv));
        });
        return policys;
    };
    // 获取玩家政策效果包含联盟
    GameHelper.prototype.getPlayerPolicyEffect = function (type, uid) {
        var policy = this.getPlayerPolicysBaseInfo(uid).find(function (m) { return m.type === type; });
        if (policy) {
            return policy.values[Math.min(policy.up - 1, policy.values.length - 1)];
        }
        return 0;
    };
    // 获取遗迹效果 根据玩家
    GameHelper.prototype.getAncientEffectByPlayer = function (uid, type) {
        if (!uid) {
            return 0;
        }
        var ancientMap = this.world.getAncientMap();
        for (var k in ancientMap) {
            var info = ancientMap[k];
            if (this.checkIsOneAlliance(info.owner, uid)) {
                var lv = info.getFirstSurplusTime() ? 20 : info.lv;
                var json = assetsMgr.getJsonData('buildAttr', info.id * 1000 + lv);
                if (json) {
                    var _a = __read(ut.stringToNumbers(json.effects, ','), 2), t = _a[0], v = _a[1];
                    if (t === type) {
                        return v || 0;
                    }
                }
            }
        }
        return 0;
    };
    // 获取政策战斗buff
    GameHelper.prototype.getPolicyBattleBuffs = function (defender) {
        var policyMap = {};
        exports.gameHpr.getPlayerPolicysBaseInfo().forEach(function (m) { return policyMap[m.type] = { id: m.id, val: m.values[Math.min(m.up - 1, m.values.length - 1)] }; });
        var buffs = [], uid = exports.gameHpr.getUid();
        var addBuffVal = function (type) {
            var m = policyMap[type];
            if (m) {
                buffs.push({ type: m.id, value: m.val, provider: uid });
            }
        };
        var isOneAlliance = exports.gameHpr.checkIsOneAlliance(defender);
        if (isOneAlliance) {
            // 守卫光环 1026
            addBuffVal(Enums_1.CEffect.DEFEND_HALO);
        }
        else {
            // 进攻号角 1027
            addBuffVal(Enums_1.CEffect.ATTACK_HALO);
            // 摧坚巧工 1043
            addBuffVal(Enums_1.CEffect.ADD_DMG_TO_BUILD);
            // 野怪克星 1042
            if (!defender) {
                addBuffVal(Enums_1.CEffect.ADD_DMG_TO_MONSTER);
            }
        }
        // 护甲专精 1028
        addBuffVal(Enums_1.CEffect.ADD_MAX_HP);
        // 武器专精 1029
        addBuffVal(Enums_1.CEffect.ADD_ATTACK);
        // 一级之力 1030
        addBuffVal(Enums_1.CEffect.LV_1_POWER);
        // 生生不息 1044
        addBuffVal(Enums_1.CEffect.CIRCLE_OF_LIFE);
        return buffs;
    };
    // 获取玩家的箭塔等级
    GameHelper.prototype.getPlayerTowerLvByPawn = function (uid, id) {
        var _a;
        var lv = ((_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.towerLvMap[id]) || 1;
        if (id === 7001) {
            lv += this.getPlayerPolicyEffect(Enums_1.CEffect.TOWER_LV, uid);
        }
        return lv;
    };
    // 是否沦陷了
    GameHelper.prototype.isCapture = function (uid) {
        var _a, _b;
        return !((_b = (_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.size);
    };
    // 是否在观战
    GameHelper.prototype.isSpectate = function (uid) {
        var _a;
        return !!((_a = this.getPlayerInfo(uid !== null && uid !== void 0 ? uid : this.getUid())) === null || _a === void 0 ? void 0 : _a.spectateIndex);
    };
    // 是否和自己一个联盟
    GameHelper.prototype.isOneAlliance = function (uid) {
        return this.alliance.isMember(uid);
    };
    // 检测两个玩家是否同一个联盟
    GameHelper.prototype.checkIsOneAlliance = function (a, b) {
        b = b !== null && b !== void 0 ? b : this.getUid();
        if (a === b) {
            return true;
        }
        var ap = this.getPlayerInfo(a), bp = this.getPlayerInfo(b);
        if (!ap || !bp) {
            return false;
        }
        else if (!ap.allianceUid && !bp.allianceUid) {
            return false;
        }
        return ap.allianceUid === bp.allianceUid;
    };
    // 地块是否发生战斗
    GameHelper.prototype.isBattleingByIndex = function (index) {
        return !!this.world.getBattleDistMap()[index] || this.playback.isWatching;
    };
    // 是否有同一个联盟的军队
    GameHelper.prototype.checkAreaOneAlliArmy = function (val) {
        var _this = this;
        val = typeof (val) === 'number' ? this.world.getMapCellByIndex(val) : val;
        if (!val) {
            return false;
        }
        else if (this.isOneAlliance(val.owner)) {
            return true;
        }
        var battleUids = this.world.getBattleDistMap()[val.index].list;
        return !!(battleUids === null || battleUids === void 0 ? void 0 : battleUids.some(function (m) { return _this.isOneAlliance(m); }));
    };
    // 获取玩家保护模式下的时间
    GameHelper.prototype.getPlayerProtectAvoidTime = function (uid) {
        if (this.isNoviceMode || uid === '') {
            return 0;
        }
        var season = this.world.getSeason();
        if (season === null || season === void 0 ? void 0 : season.type) {
            return 0; //只有春季才有保护模式
        }
        else if (this.checkPlayerIsProtectMode(this.getPlayerInfo(uid !== null && uid !== void 0 ? uid : this.getUid()))) {
            return season.getSurplusTime();
        }
        return 0;
    };
    // 检测玩家的保护模式状态 0.没有 1.有保护 2.保护过期
    GameHelper.prototype.checkPlayerProtectModeState = function (uid) {
        if (this.isNoviceMode) {
            return 0;
        }
        var seasonType = this.world.getSeasonType();
        if (seasonType >= 2) {
            return 0; //秋季过后 都没有免战了 就不显示框了
        }
        else if (!this.checkPlayerIsProtectMode(this.getPlayerInfo(uid))) {
            return 0; //没有保护模式
        }
        return seasonType + 1;
    };
    GameHelper.prototype.checkPlayerIsProtectMode = function (player) {
        return !this.isNoviceMode && !!player && !player.isGiveupGame && !player.isSettled && player.farmType === 2;
    };
    // 获取玩家拥有的地块数量
    GameHelper.prototype.getPlayerOweCellCount = function (uid, notMainCityCount) {
        var _a, _b;
        var count = ((_b = (_a = this.getPlayerInfo(uid || this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.size) || 0;
        return notMainCityCount ? Math.max(0, count - 4) : count;
    };
    // 获取玩家拥有的地块
    GameHelper.prototype.getPlayerOweCells = function (uid, notMainCityCount) {
        var cells = new Map();
        var playerInfo = this.getPlayerInfo(uid || this.getUid());
        if (playerInfo) {
            playerInfo.cells.forEach(function (cell) {
                if (notMainCityCount) {
                    if (cell.index !== playerInfo.mainCityIndex && cell.index !== playerInfo.mainCityIndex + 1
                        && cell.index !== playerInfo.mainCityIndex + MapHelper_1.mapHelper.MAP_SIZE.x && cell.index !== playerInfo.mainCityIndex + MapHelper_1.mapHelper.MAP_SIZE.x + 1) {
                        cells.set(cell.index, cell);
                    }
                }
                else {
                    cells.set(cell.index, cell);
                }
            });
        }
        return cells;
    };
    // 获取最大领地数 私聊专用
    GameHelper.prototype.getMaxLandCountByPChat = function () {
        return Math.max(this.getPlayerOweCellCount(), this.user.getMaxLandCount());
    };
    // 获取玩家x级地块y级难度土地数量 忽略主城占领的地块
    GameHelper.prototype.getPlayerLandByDiffcult = function (uid) {
        var _a, _b;
        var diffMap = {}; // 类型: {等级: 数量}
        (_b = (_a = this.getPlayerInfo(uid || this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
            if (cell.isHasRes()) {
                var _a = __read(cell.getLandDifficultyLv(), 2), type = _a[0], lv = _a[1];
                var lvMap = diffMap[type];
                if (!lvMap) {
                    lvMap = diffMap[type] = {};
                }
                lvMap[lv] = (lvMap[lv] || 0) + 1;
            }
        });
        return diffMap;
    };
    // 获取玩家x级土地数量 忽略掉主城占领的地块
    GameHelper.prototype.getPlayerLandCountByLv = function (lv, uid) {
        var _a, _b;
        var count = 0;
        (_b = (_a = this.getPlayerInfo(uid || this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
            if (cell.isHasRes() && cell.landLv === lv) {
                count += 1;
            }
        });
        return count;
    };
    // 获取玩家x级土地数量 忽略掉主城占领的地块 资源地大于1级才算
    GameHelper.prototype.getPlayerLandCountByType = function (type, uid) {
        var _a, _b;
        var count = 0;
        (_b = (_a = this.getPlayerInfo(uid || this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
            if (cell.isHasRes() && cell.landType === type && cell.landLv > 1) {
                count += 1;
            }
        });
        return count;
    };
    // 获取玩家所有要塞
    GameHelper.prototype.getPlayerForts = function (uid) {
        var _a, _b;
        var arr = [];
        (_b = (_a = this.getPlayerInfo(uid || this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
            if (cell.cityId === Constant_1.CITY_FORT_NID) {
                arr.push(cell);
            }
        });
        return arr;
    };
    // 获取地块到地块的距离
    GameHelper.prototype.getToMapCellDis = function (sindex, tindex) {
        var sCell = this.world.getMapCellByIndex(sindex);
        var tCell = this.world.getMapCellByIndex(tindex);
        if (!sCell || !tCell) {
            return 0;
        }
        var _a = __read(MapHelper_1.mapHelper.getMinDisPoint(sCell.getOwnPoints(), tCell.getOwnPoints()), 2), s = _a[0], t = _a[1];
        return MapHelper_1.mapHelper.getPointToPointDis(s, t);
    };
    GameHelper.prototype.getSelfToMapCellDis = function (index) {
        return this.getToMapCellDis(this.player.getMainCityIndex(), index);
    };
    // 获取自己和其他玩家的距离
    GameHelper.prototype.getSelfToOtherPlayerDis = function (uid) {
        return this.getToMapCellDis(this.player.getMainCityIndex(), this.getPlayerMainCityIndex(uid));
    };
    // 获取玩家主城位置
    GameHelper.prototype.getPlayerMainCityIndex = function (uid) {
        var _a;
        return ((_a = this.getPlayerInfo(uid)) === null || _a === void 0 ? void 0 : _a.mainCityIndex) || -1;
    };
    // 修正index 有可能当前index是城市的其他位置 需要修正到城市位置
    GameHelper.prototype.amendAreaIndex = function (index) {
        var _a, _b, _c;
        return (_c = (_b = (_a = this.world.getMapCellByIndex(index)) === null || _a === void 0 ? void 0 : _a.city) === null || _b === void 0 ? void 0 : _b.index) !== null && _c !== void 0 ? _c : index;
    };
    // 获取地块的名字
    GameHelper.prototype.getCellBaseNameByIndex = function (index) {
        var _a;
        return ((_a = this.world.getMapCellByIndex(index)) === null || _a === void 0 ? void 0 : _a.getBaseName()) || '';
    };
    // 字符串转通用类型列表
    GameHelper.prototype.stringToCTypes = function (str) {
        return String(str !== null && str !== void 0 ? str : '').split('|').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }).map(function (m) { return new CTypeObj_1.default().fromString(m); });
    };
    // 字符串转通用效果列表
    GameHelper.prototype.stringToCEffects = function (str) {
        return String(str !== null && str !== void 0 ? str : '').split('|').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }).map(function (m) { return new CEffectObj_1.default().fromString(m); });
    };
    // 字符串转任务条件类型
    GameHelper.prototype.stringToTaskConds = function (str) {
        return String(str !== null && str !== void 0 ? str : '').split('|').map(function (m) { return m.trim(); }).filter(function (m) { return !!m; }).map(function (m) { return new TaskCondObj_1.default().fromString(m); });
    };
    // 根据通用类型获取数量
    GameHelper.prototype.getCountByCType = function (type) {
        if (type === Enums_1.CType.GOLD) {
            return this.user.getGold();
        }
        else if (type === Enums_1.CType.CEREAL) {
            return this.player.getCereal();
        }
        else if (type === Enums_1.CType.TIMBER) {
            return this.player.getTimber();
        }
        else if (type === Enums_1.CType.STONE) {
            return this.player.getStone();
        }
        else if (type === Enums_1.CType.EXP_BOOK) {
            return this.player.getExpBook();
        }
        else if (type === Enums_1.CType.IRON) {
            return this.player.getIron();
        }
        else if (type === Enums_1.CType.UP_SCROLL) {
            return this.player.getUpScroll();
        }
        else if (type === Enums_1.CType.FIXATOR) {
            return this.player.getFixator();
        }
        return 0;
    };
    // 检测通用类型
    GameHelper.prototype.checkCType = function (ct, index) {
        if (!ct) {
            return false;
        }
        var type = ct === null || ct === void 0 ? void 0 : ct.type;
        if (type === Enums_1.CType.CEREAL
            || type === Enums_1.CType.TIMBER
            || type === Enums_1.CType.STONE
            || type === Enums_1.CType.GOLD
            || type === Enums_1.CType.EXP_BOOK
            || type === Enums_1.CType.IRON
            || type === Enums_1.CType.UP_SCROLL
            || type === Enums_1.CType.FIXATOR) {
            return this.getCountByCType(ct.type) >= ct.count;
        }
        else if (type === Enums_1.CType.CEREAL_C) { //粮耗
            return this.player.getCerealOp() >= ct.count;
        }
        else if (type === Enums_1.CType.BUILD_LV) { //建筑
            return this.player.getMainBuilds().some(function (m) { return m.id === ct.id && m.lv >= ct.count; });
        }
        return false;
    };
    GameHelper.prototype.checkCTypes = function (cts, index) {
        var _this = this;
        return !cts || cts.every(function (m) { return _this.checkCType(m, index); });
    };
    // 添加消息
    GameHelper.prototype.addMessage = function (opts) {
        if (this.isInLobby() && !this.isNoviceMode) {
            return;
        }
        this.message.add(opts);
    };
    GameHelper.prototype.delMessageByTag = function (tag) {
        this.message.removeByTag(tag);
    };
    // 添加获取消息
    GameHelper.prototype.addGainMassage = function (data, playSfx) {
        if (playSfx === void 0) { playSfx = true; }
        if (!data) {
            return;
        }
        else if (!Array.isArray(data)) {
            eventCenter.emit(EventType_1.default.ADD_GAIN_MESSAGE, data);
        }
        else if (data.length === 0) {
            return;
        }
        else {
            data.forEach(function (m) { return eventCenter.emit(EventType_1.default.ADD_GAIN_MESSAGE, m); });
        }
        if (playSfx) {
            audioMgr.playSFX('common/sound_ui_009');
        }
    };
    // 点击建筑升级
    GameHelper.prototype.clickBuildUp = function (data, ui) {
        this.areaCenter.upBuildToServer(data).then(function (err) {
            if (!ui.isValid) {
            }
            else if (err === ECode_1.ecode.ANTI_CHEAT) {
                ViewHelper_1.viewHelper.showPnl('main/AntiCheat');
            }
            else if (err) {
                ViewHelper_1.viewHelper.showAlert(err);
            }
            else {
                ui.hide();
                // viewHelper.showAlert('toast.yet_add_build_queue')
                exports.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.PUSH);
            }
        });
    };
    // 检测条件 返回第一个不满足的
    GameHelper.prototype.checkCondsByString = function (str) {
        var conds = this.stringToCTypes(str);
        if (conds.length === 0) {
            return null;
        }
        var _loop_1 = function (i, l) {
            var cond = conds[i];
            if (cond.type === Enums_1.CType.BUILD_LV) {
                if (!this_1.player.getMainBuilds().some(function (m) { return m.id === cond.id && m.lv >= cond.count; })) {
                    return { value: cond };
                }
            }
        };
        var this_1 = this;
        for (var i = 0, l = conds.length; i < l; i++) {
            var state_1 = _loop_1(i, l);
            if (typeof state_1 === "object")
                return state_1.value;
        }
        return null;
    };
    // 检测建筑条件
    GameHelper.prototype.checkUnlcokBuildCond = function (str, force) {
        if (!str) {
            return '';
        }
        var k = force ? 1 : 0;
        var obj = this.tempUnlockBuildCondText[k];
        if (!obj) {
            obj = this.tempUnlockBuildCondText[k] = {};
        }
        var s = obj[str];
        if (s !== undefined) {
            return s;
        }
        var conds = this.stringToCTypes(str).filter(function (m) { return m.type === Enums_1.CType.BUILD_LV; });
        if (conds.length === 0) {
            obj[str] = '';
            return '';
        }
        var buildLvMap = {}, color = '#D7634D';
        if (!force) {
            this.player.getMainBuilds().forEach(function (m) {
                var lv = buildLvMap[m.id] || 0;
                if (m.lv > lv) {
                    buildLvMap[m.id] = m.lv;
                }
            });
        }
        else {
            color = '#625450';
        }
        s = '';
        conds.forEach(function (cond) {
            var ids = cond.getIds();
            if (ids.some(function (id) { return (buildLvMap[id] || 0) >= cond.count; })) {
                return;
            }
            else if (s !== '') {
                s += ', ';
            }
            s += ids.join2(function (id) { return assetsMgr.lang('ui.build_cond_lv', 'buildText.name_' + id, cond.count, color); }, "  <color=#B6A591>" + assetsMgr.lang('ui.or') + "</c> ");
        });
        obj[str] = s;
        return s;
    };
    GameHelper.prototype.cleanUnlockBuildCondText = function () {
        this.tempUnlockBuildCondText = {};
    };
    GameHelper.prototype.checkNeedBuildLvCond = function (str) {
        if (!str) {
            return '';
        }
        var cond = this.stringToCTypes(str).find(function (m) { return m.type === Enums_1.CType.BUILD_LV; });
        if (!cond || cond.count <= this.player.getBuildLv(cond.id)) {
            return '';
        }
        return assetsMgr.lang('ui.need_build_lv_desc', 'buildText.name_' + cond.id, "<color=#C34A32>" + cond.count + "</c>");
    };
    // 检测领地数条件
    GameHelper.prototype.checkCellCountCond = function (str) {
        if (!str) {
            return '';
        }
        var cond = this.stringToCTypes(str).find(function (m) { return m.type === Enums_1.CType.CELL_COUNT; });
        if (!cond || cond.count <= this.getPlayerOweCellCount(this.getUid(), cond.id === 1)) {
            return '';
        }
        return assetsMgr.lang('ui.need_cell_count_cond', cond.count);
    };
    // 检测奖励资源是否满了
    GameHelper.prototype.checkRewardFull = function (rewards) {
        var _this = this;
        var arr = [];
        rewards.forEach(function (m) { return _this.checkRewardFullAddArr(m.type, m.count, arr); });
        return arr;
    };
    GameHelper.prototype.checkRewardFullAddArr = function (type, count, arr) {
        if (type === Enums_1.CType.CEREAL) {
            count = Math.max(count + this.player.getCereal() - this.player.getGranaryCap(), 0);
        }
        else if (type === Enums_1.CType.TIMBER) {
            count = Math.max(count + this.player.getTimber() - this.player.getWarehouseCap(), 0);
        }
        else if (type === Enums_1.CType.STONE) {
            count = Math.max(count + this.player.getStone() - this.player.getWarehouseCap(), 0);
        }
        else if (type === Enums_1.CType.BASE_RES) {
            this.checkRewardFullAddArr(Enums_1.CType.CEREAL, count, arr);
            this.checkRewardFullAddArr(Enums_1.CType.TIMBER, count, arr);
            this.checkRewardFullAddArr(Enums_1.CType.STONE, count, arr);
            count = 0;
        }
        else {
            count = 0;
        }
        if (count > 0) {
            arr.push(new CTypeObj_1.default().init(type, 0, count));
        }
    };
    // 合并数量
    GameHelper.prototype.mergeTypeObjsCount = function (slice) {
        var elems = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            elems[_i - 1] = arguments[_i];
        }
        elems.forEach(function (m) {
            var it = slice.find(function (t) { return t.type === m.type && t.id === m.id; });
            if (it) {
                it.count += m.count;
            }
            else {
                slice.push(m.clone());
            }
        });
    };
    // 获取城市列表
    GameHelper.prototype.getCitysById = function (id) {
        var _a, _b;
        var arr = [];
        (_b = (_a = this.getPlayerInfo(this.getUid())) === null || _a === void 0 ? void 0 : _a.cells) === null || _b === void 0 ? void 0 : _b.forEach(function (cell) {
            var _a, _b;
            if (((_a = cell.city) === null || _a === void 0 ? void 0 : _a.id) === id || ((_b = cell.getBTCityInfo()) === null || _b === void 0 ? void 0 : _b.id) === id) {
                arr.push(cell);
            }
        });
        return arr;
    };
    // 移动到目标位置
    GameHelper.prototype.gotoTargetPos = function (index, showCellInfo) {
        var _a;
        if (showCellInfo === void 0) { showCellInfo = true; }
        return __awaiter(this, void 0, void 0, function () {
            var key;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        key = this.world.getSceneKey();
                        if (!(mc.currWindName !== key)) return [3 /*break*/, 2];
                        if (mc.currWindName === 'area' && ((_a = this.world.getLookCell()) === null || _a === void 0 ? void 0 : _a.actIndex) === this.amendAreaIndex(index)) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, ViewHelper_1.viewHelper.gotoWind(key)];
                    case 1:
                        _b.sent();
                        _b.label = 2;
                    case 2:
                        eventCenter.emit(EventType_1.default.MAP_MOVE_TO, MapHelper_1.mapHelper.indexToPoint(index).clone(), showCellInfo);
                        return [2 /*return*/];
                }
            });
        });
    };
    GameHelper.prototype.gotoTargetPosByPoint = function (point, showCellInfo) {
        if (showCellInfo === void 0) { showCellInfo = true; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.gotoTargetPos(this.amendAreaIndex(MapHelper_1.mapHelper.pointToIndex(point)), showCellInfo)];
            });
        });
    };
    // 获取标记说明
    GameHelper.prototype.getOneFlagDescByIndex = function (index) {
        var mark0 = this.player.getMapMark(index);
        var mark1 = this.alliance.getMapFlagInfo(index);
        if (!mark0 && !mark1) {
            return null;
        }
        else if (mark1) {
            return { desc: mark1.desc, flag: mark1.flag, type: 0 };
        }
        else if (mark0) {
            return { desc: mark0.desc, flag: mark0.flag, type: 0 };
        }
        return null;
    };
    // 刷新置顶标记信息
    GameHelper.prototype.updateOntopFlagData = function (flag, index, type) {
        var key = type + '_' + flag;
        var flags = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS) || {};
        if (index >= 0) {
            flags[key] = index;
        }
        else {
            delete flags[key];
        }
        this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS, flags);
        eventCenter.emit(EventType_1.default.UPDATE_ONTOP_FLAG, flags);
    };
    // 获取置顶信息
    GameHelper.prototype.getOntopFlagCount = function () {
        return Object.keys(this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ONTOP_FLAGS) || {}).length;
    };
    // 倒计时
    GameHelper.prototype.millisecondToCountDown = function (msd) {
        if (msd >= ut.Time.Day) {
            var str = msd % ut.Time.Day >= ut.Time.Hour ? ut.millisecondFormat(msd, 'd{0}h{1}') : ut.millisecondFormat(msd, 'd{0}');
            return ut.stringFormat(str, [assetsMgr.lang('login.time_day'), assetsMgr.lang('login.time_hour')]);
        }
        else if (msd >= ut.Time.Hour) {
            var str = msd % ut.Time.Hour >= ut.Time.Minute ? ut.millisecondFormat(msd, 'h{0}m{1}') : ut.millisecondFormat(msd, 'h{0}');
            return ut.stringFormat(str, [assetsMgr.lang('login.time_hour'), assetsMgr.lang('login.time_minute')]);
        }
        return ut.millisecondFormat(msd, 'mm:ss');
    };
    // 毫秒转字符串
    GameHelper.prototype.millisecondToString = function (msd) {
        if (msd >= ut.Time.Day) {
            var str = msd % ut.Time.Day >= ut.Time.Hour ? ut.millisecondFormat(msd, 'd{0}h{1}') : ut.millisecondFormat(msd, 'd{0}');
            return ut.stringFormat(str, [assetsMgr.lang('login.time_day'), assetsMgr.lang('login.time_hour')]);
        }
        else if (msd >= ut.Time.Hour) {
            var str = msd % ut.Time.Hour >= ut.Time.Minute ? ut.millisecondFormat(msd, 'h{0}m{1}') : ut.millisecondFormat(msd, 'h{0}');
            return ut.stringFormat(str, [assetsMgr.lang('login.time_hour'), assetsMgr.lang('login.time_minute')]);
        }
        else if (msd >= ut.Time.Minute) {
            var str = msd % ut.Time.Minute >= ut.Time.Second ? ut.millisecondFormat(msd, 'm{0}s{1}') : ut.millisecondFormat(msd, 'm{0}');
            return ut.stringFormat(str, [assetsMgr.lang('login.time_minute'), assetsMgr.lang('login.time_second')]);
        }
        return Math.floor(msd / ut.Time.Second) + assetsMgr.lang('login.time_second');
    };
    GameHelper.prototype.millisecondToStringForDay = function (msd) {
        var day = Math.floor(msd / ut.Time.Day);
        var hour = Math.floor(msd / ut.Time.Hour);
        var minute = Math.floor(msd / ut.Time.Minute);
        if (day > 0) {
            return day + assetsMgr.lang('login.time_day');
        }
        else if (hour > 0) {
            return hour + assetsMgr.lang('login.time_hour');
        }
        return Math.max(1, minute) + assetsMgr.lang('login.time_minute');
    };
    GameHelper.prototype.millisecondToStringForHour = function (msd) {
        var hour = Math.floor(msd / ut.Time.Hour);
        var minute = Math.floor(msd / ut.Time.Minute);
        if (hour > 0) {
            return hour + assetsMgr.lang('login.time_hour');
        }
        return Math.max(1, minute) + assetsMgr.lang('login.time_minute');
    };
    // 如果只有2个的时候 会自动加上年份
    GameHelper.prototype.checkActivityAutoDate = function (startTime, endTime) {
        var year = new Date().getFullYear();
        if (((startTime === null || startTime === void 0 ? void 0 : startTime.split('-')[0]) || '').length !== 4) {
            startTime = year + '-' + startTime;
        }
        if (((endTime === null || endTime === void 0 ? void 0 : endTime.split('-')[0]) || '').length !== 4) {
            var _a = __read(startTime.split('-'), 2), _1 = _a[0], sm = _a[1];
            var em = endTime.split('-')[0];
            if (Number(em) < Number(sm)) {
                year += 1;
            }
            endTime = year + '-' + endTime;
        }
        return this.checkActivityDate(startTime, endTime);
    };
    // 检查是否在活动时间内，不保证安全
    GameHelper.prototype.checkActivityDate = function (startTime, endTime) {
        if (!startTime || !endTime) {
            return false;
        }
        return this.checkCommunityActivityDate(startTime) && !this.checkCommunityActivityDate(endTime);
    };
    GameHelper.prototype.checkCommunityActivityDate = function (date, offset) {
        if (offset === void 0) { offset = 0; }
        var arr = date.split('-');
        if (arr.length < 2) {
            return false;
        }
        var now = Date.now();
        var _a = __read(arr, 6), startYear = _a[0], startMonth = _a[1], startDay = _a[2], h = _a[3], m = _a[4], s = _a[5];
        if (arr.length === 2) {
            startDay = startMonth;
            startMonth = startYear;
            startYear = new Date(now).getFullYear() + '';
        }
        else if (!startYear || startYear === 'y') {
            startYear = new Date(now).getFullYear() + '';
        }
        h = h || '00';
        m = m || '00';
        s = s || '00';
        date = startYear + '/' + startMonth + '/' + startDay + ' ' + h + ':' + m + ':' + s;
        var dateTime = new Date(date).getTime() + offset * ut.Time.Day;
        return now >= dateTime;
    };
    // 检测是否为新品
    GameHelper.prototype.checkShopNewProduct = function (data) {
        var _a;
        var serverArea = this.isRelease ? this.getServerArea() : 'test';
        var _b = __read((_a = (data['limit_time_' + serverArea] || data.limit_time_hk)) === null || _a === void 0 ? void 0 : _a.split('|'), 2), startTime = _b[0], endTime = _b[1];
        if (startTime && endTime && this.checkActivityAutoDate(startTime, endTime)) {
            return true;
        }
        return false;
    };
    // 获取野地等级 根据距离
    GameHelper.prototype.getLandAttrLvByDis = function (dis, landLv) {
        var maxDis = 12;
        if (landLv === 1) {
            maxDis = 10;
        }
        else if (landLv === 2) {
            maxDis = 8;
        }
        else if (landLv === 3) {
            maxDis = 14;
        }
        else if (landLv === 4) {
            maxDis = 15;
        }
        else if (landLv === 5) {
            maxDis = 16;
        }
        return cc.misc.clampf(dis, 1, maxDis);
    };
    // 获取野地的士兵配置
    GameHelper.prototype.getAreaPawnConfInfo = function (index, landId, dis) {
        var _this = this;
        // 每距离1格算一个阶段 最大10个阶段
        var landInfo = assetsMgr.getJsonData('land', landId);
        var landLv = landInfo.lv;
        var level = this.getLandAttrLvByDis(dis, landLv);
        var json = assetsMgr.getJsonData('landAttr', landLv * 1000 + level);
        if (!json) {
            return { index: index, hp: [0, 0], armys: [] };
        }
        var landType = landInfo.type;
        // 血量
        var hp = json.hp;
        // 军队
        var pawns = [];
        var armysString = json['armys_' + landType] || json.armys_3;
        var arr = armysString.split('|');
        arr.forEach(function (str) {
            if (!str) {
                return;
            }
            var p = str.split(','), point = p[1], id = Number(p[0]), lv = Number(p[2]);
            var equip = exports.gameHpr.noviceServer.getEnemyEquipById(id);
            // 给boss随机一个装备
            if (!equip) {
                var json_1 = assetsMgr.getJsonData('pawnBase', id);
                if (json_1 && (json_1.type === Enums_1.PawnType.BEAST || json_1.type === Enums_1.PawnType.CATERAN) && !json_1.velocity) {
                    var base = assetsMgr.getJson('equipBase').datas.filter(function (m) { return !m.exclusive_pawn && !m.not_monster.includes(id + ''); }).random();
                    if (base) {
                        var mainId_1 = base.id;
                        var attrs = _this.getEquipAttrs(base);
                        // 山贼还需要加一件装备
                        if (json_1.type === Enums_1.PawnType.CATERAN) {
                            base = assetsMgr.getJson('equipBase').datas.filter(function (m) { return m.id !== mainId_1 && !m.exclusive_pawn && !m.not_monster.includes(id + ''); }).random();
                            base && attrs.pushArr(_this.getEquipAttrs(base, true));
                        }
                        // 赋值
                        equip = { id: mainId_1, attrs: attrs };
                        // 记录一下
                        exports.gameHpr.noviceServer.setEnemyEquip(id, equip);
                    }
                }
            }
            pawns.push({
                uid: 'pawn_uid_' + index + '_' + id + '_' + point,
                index: index,
                point: ut.stringToVec2(point, '_'),
                id: id,
                lv: lv,
                equip: equip,
            });
        });
        // 放入临时里面
        return {
            index: index,
            hp: [hp, hp],
            armys: [{
                    index: index, pawns: pawns,
                    uid: 'army_uid_' + index,
                    state: 0,
                    name: '',
                    owner: '',
                }]
        };
    };
    GameHelper.prototype.getEquipAttrs = function (json, isVice) {
        var attrs = [];
        if (json.hp) {
            var val = ut.stringToNumbers(json.hp, ',')[1];
            attrs.push([0, 1, isVice ? Math.round(val * 0.5) : val]);
        }
        if (json.attack) {
            var val = ut.stringToNumbers(json.attack, ',')[1];
            attrs.push([0, 2, isVice ? Math.round(val * 0.5) : val]);
        }
        var effect = assetsMgr.getJsonData('equipEffect', ut.stringToNumbers(json.effect).random());
        if (effect) {
            var arr = [2, effect.id];
            if (effect.value) {
                arr.push(ut.stringToNumbers(effect.value, ',')[effect.value_max || 0]);
            }
            else {
                arr.push(0); //这里要补位 后面还有几率
            }
            if (effect.odds) {
                arr.push(ut.stringToNumbers(effect.odds, ',')[1]);
            }
            attrs.push(arr);
        }
        // 第5位 是副装备id
        if (isVice) {
            attrs.forEach(function (arr) {
                for (var i = arr.length; i < 4; i++) {
                    arr.push(0);
                }
                arr.push(json.id);
            });
        }
        return attrs;
    };
    // 获取所有士兵的战斗力之和
    GameHelper.prototype.getPawnsAttack = function (pawns) {
        var atkSum = 0, hpSum = 0;
        pawns.forEach(function (m) {
            var json = assetsMgr.getJsonData('pawnAttr', m.id * 1000 + m.lv);
            if (!json) {
                return;
            }
            var equip = new EquipInfo_1.default().fromSvr(m.equip || { id: 0, attrs: [] });
            hpSum += m.hp ? m.hp[0] : json.hp;
            atkSum += json.attack + equip.attack;
        });
        return atkSum * hpSum;
    };
    // 获取多个军队的战斗力之和
    GameHelper.prototype.getArmysAttack = function (armys) {
        var pawns = [];
        armys.forEach(function (m) { return pawns.push.apply(pawns, __spread(m.pawns)); });
        return this.getPawnsAttack(pawns);
    };
    // 获取军队最低的行军速度
    GameHelper.prototype.getArmyMarchSpeed = function (army) {
        var val = 0;
        army.pawns.forEach(function (m) {
            var json = assetsMgr.getJsonData('pawnBase', m.id);
            if (!json) {
                return;
            }
            else if (json.march_speed < val || val === 0) {
                val = json.march_speed;
            }
        });
        return val;
    };
    // 是否不再提示
    GameHelper.prototype.isNoLongerTip = function (key) {
        var _a;
        return !!((_a = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.NO_LONGER_TIP)) === null || _a === void 0 ? void 0 : _a[key]);
    };
    // 设置不再提示
    GameHelper.prototype.setNoLongerTip = function (key, val) {
        var data = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.NO_LONGER_TIP) || {};
        if (!!data[key] !== val) {
            data[key] = val;
            this.user.setLocalPreferenceData(Enums_1.PreferenceKey.NO_LONGER_TIP, data);
        }
    };
    // 是否不再提示
    GameHelper.prototype.isNoLongerTipBySid = function (key) {
        var _a;
        return !!((_a = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.NO_LONGER_TIP)) === null || _a === void 0 ? void 0 : _a[key]);
    };
    // 设置不再提示
    GameHelper.prototype.setNoLongerTipBySid = function (key, val) {
        var data = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.NO_LONGER_TIP) || {};
        if (!!data[key] !== val) {
            data[key] = val;
            this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.NO_LONGER_TIP, data);
        }
    };
    // 拷贝到剪切板
    GameHelper.prototype.copyToClipboard = function (text, toast) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!!text) return [3 /*break*/, 1];
                        return [3 /*break*/, 10];
                    case 1:
                        if (!cc.sys.isBrowser) return [3 /*break*/, 5];
                        if (!(navigator === null || navigator === void 0 ? void 0 : navigator.clipboard)) return [3 /*break*/, 3];
                        return [4 /*yield*/, navigator.clipboard.writeText(text)];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3: return [2 /*return*/];
                    case 4: return [3 /*break*/, 10];
                    case 5:
                        if (!ut.isMiniGame()) return [3 /*break*/, 7];
                        return [4 /*yield*/, WxHelper_1.wxHelper.setClipboardData({ data: text })];
                    case 6:
                        _a.sent();
                        return [2 /*return*/]; //微信自带提示
                    case 7:
                        if (!ut.isMobile()) return [3 /*break*/, 9];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.call(JsbEvent_1.default.COPY_TO_CLIPBOARD, { data: text })];
                    case 8:
                        _a.sent();
                        return [3 /*break*/, 10];
                    case 9: return [2 /*return*/];
                    case 10:
                        toast && ViewHelper_1.viewHelper.showAlert(toast);
                        return [2 /*return*/];
                }
            });
        });
    };
    // 获取到明天06:00还剩余多长时间, 如果starTime和nowTime在同一侧(当天6点的左侧或右侧), 返回> 0， 否则 < 0
    GameHelper.prototype.getToDaySurpluTime = function (startTime, day) {
        if (day === void 0) { day = 1; }
        if (startTime <= 0) {
            return 0;
        }
        var offset = ut.Time.Hour * 2;
        var now = Date.now() + offset;
        var date = new Date(startTime + offset);
        var time = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0).getTime();
        time -= date.getTimezoneOffset() * ut.Time.Minute; //当天6点
        time += ut.Time.Day * day;
        return Math.floor((time - now) * 0.001);
    };
    GameHelper.prototype.setCanBattleTime = function (arr) {
        if (!arr || arr.length != 4) {
            return;
        }
        this.canBattleTime = arr;
    };
    // 是否在可攻击玩家时间段
    GameHelper.prototype.isCanOccupyTime = function () {
        var now = new Date();
        var hour = now.getHours();
        var min = now.getMinutes();
        var nowTotalMin = hour * 60 + min;
        var startTotalMin = this.canBattleTime[0] * 60 + this.canBattleTime[1];
        var endTotalMin = this.canBattleTime[2] * 60 + this.canBattleTime[3];
        return nowTotalMin >= startTotalMin && nowTotalMin <= endTotalMin;
    };
    // 获取市场卖出公示时间
    GameHelper.prototype.getBazaarPublicityTime = function () {
        var now = new Date();
        var hour = now.getHours();
        var min = now.getMinutes();
        var nowTotalMin = hour * 60 + min;
        var startTotalMin = this.bazaarPublicityTime[0] * 60 + this.bazaarPublicityTime[1];
        var endTotalMin = this.bazaarPublicityTime[2] * 60 + this.bazaarPublicityTime[3];
        return nowTotalMin >= startTotalMin && nowTotalMin <= endTotalMin ? 1 * ut.Time.Hour : 6 * ut.Time.Hour;
    };
    // 包装宝箱信息
    GameHelper.prototype.fromSvrTreasureInfo = function (data, index, auid, puid) {
        data.json = assetsMgr.getJsonData('treasure', data.id);
        data.index = index;
        data.auid = auid;
        data.puid = puid;
        data.rewards = data.rewards.map(function (m) { return new CTypeObj_1.default().init(m.type, m.id, m.count); });
        return data;
    };
    // 检测添加私聊条件
    GameHelper.prototype.checkAddPChatCond = function (uid) {
        if (uid === this.user.getUid()) {
            return false;
        }
        else if (uid && this.chat.getPChatChannels().some(function (m) { return m.channel.includes(uid); })) {
            return true;
        }
        else if (this.user.isGuest()) {
            ViewHelper_1.viewHelper.showAlert('toast.guest_no_send_pchat');
            return false;
        }
        var maxLandCount = this.getMaxLandCountByPChat();
        if (maxLandCount >= Constant_1.NOLIMIT_PCHAT_MAX_LAND) {
            return true;
        }
        // 每50地1个私聊
        var count = Math.floor(maxLandCount / 50);
        var pchatCount = this.chat.getInitiativePChatCount();
        if (pchatCount >= count) {
            var nextPChatCount = pchatCount + 1;
            ViewHelper_1.viewHelper.showAlert('toast.add_pchat_land_cond', { params: [nextPChatCount * 50, nextPChatCount] });
            return false;
        }
        return true;
    };
    // 添加私聊
    GameHelper.prototype.addPChat = function (uid) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, channel, puid, pchat, lookChannel;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.checkAddPChatCond(uid)) {
                            return [2 /*return*/, false];
                        }
                        else if (this.friend.isInBlacklist(uid)) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert('toast.int_blacklist_not_pchat')];
                        }
                        _a = this.chat.addPChatByMe(uid), channel = _a.channel, puid = _a.puid, pchat = _a.pchat;
                        lookChannel = this.chat.getCurLookChannel();
                        if (!!lookChannel) return [3 /*break*/, 2];
                        return [4 /*yield*/, ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: 2, target: { channel: channel, puid: puid, pchat: pchat } })];
                    case 1:
                        _b.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        if (lookChannel === channel) {
                        }
                        else {
                            eventCenter.emit(EventType_1.default.CHANGE_CHAT_CHANNEL, { channel: channel, puid: puid, pchat: pchat });
                        }
                        _b.label = 3;
                    case 3: return [2 /*return*/, true];
                }
            });
        });
    };
    // 切换账号
    GameHelper.prototype.changeAccount = function () {
        var loginType = this.user.getLoginType();
        if (loginType === Enums_1.LoginType.FACEBOOK) {
            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.FACEBOOK_LOGOUT);
        }
        else if (loginType === Enums_1.LoginType.GOOGLE) {
            JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.GOOGLE_LOGOUT);
            if (ut.isIos()) {
                JsbHelper_1.jsbHelper.cast(JsbEvent_1.default.FIREBASE_SIGNOUT);
            }
        }
        storageMgr.saveString('account_token', '0');
        if (mc.currWindName === 'login') {
            eventCenter.emit(EventType_1.default.RELOGIN);
        }
        else {
            ViewHelper_1.viewHelper.gotoWind('login');
        }
    };
    // 显示战斗结束界面
    GameHelper.prototype.showBattleEndView = function (res, treasures, noTreasureByNotStamina, fullLostCount) {
        ViewHelper_1.viewHelper.showPnl('area/BattleEnd', res, treasures, noTreasureByNotStamina, fullLostCount);
    };
    // 获取基础资源
    GameHelper.prototype.getCellBaseRes = function (index, cityId) {
        var cell = this.world.getMapCellByIndex(index);
        var arr = (cell === null || cell === void 0 ? void 0 : cell.getBaseRes()) || [];
        if (cityId === Constant_1.CITY_MAIN_NID) {
            var point = MapHelper_1.mapHelper.indexToPoint(index);
            cell = this.world.getMapCellByPoint(cc.v2(point.x + 1, point.y)); //右
            arr.pushArr((cell === null || cell === void 0 ? void 0 : cell.getBaseRes()) || []);
            cell = this.world.getMapCellByPoint(cc.v2(point.x, point.y + 1)); //上
            arr.pushArr((cell === null || cell === void 0 ? void 0 : cell.getBaseRes()) || []);
            cell = this.world.getMapCellByPoint(cc.v2(point.x + 1, point.y + 1)); //右上
            arr.pushArr((cell === null || cell === void 0 ? void 0 : cell.getBaseRes()) || []);
        }
        // 同类型合并
        var map = {};
        for (var i = 0; i < arr.length; i++) {
            var item = arr[i];
            var count = map[item.type] || 0;
            map[item.type] = count + item.val;
        }
        arr.length = 0;
        for (var key in map) {
            arr.push({ type: Number(key), val: map[key] });
        }
        return arr;
    };
    // 拆分评分
    GameHelper.prototype.resolutionRankScore = function (score, rankCount) {
        if (score <= 0) {
            return { id: rankCount > 0 ? 0 : -1, winPoint: 0 };
        }
        var list = assetsMgr.getJson('seasonReward').datas;
        for (var i = 0, l = list.length - 1; i < l; i++) {
            var m = list[i];
            if (score < m.rank_score) {
                return { id: 0, winPoint: score, maxPoint: m.rank_score };
            }
            else if (score < list[i + 1].rank_score) {
                return { id: m.id, winPoint: score - m.rank_score, maxPoint: list[i + 1].rank_score - m.rank_score };
            }
        }
        var last = list.last();
        return { id: last.id, winPoint: Math.max(0, score - last.rank_score), maxPoint: -1 };
    };
    // 刷新聊天时间
    GameHelper.prototype.updateChatAllTime = function (list) {
        if (!(list === null || list === void 0 ? void 0 : list.length)) {
            return;
        }
        var zero = ut.dateZeroTime(this.getServerNowTime());
        var yesterdayZero = zero - ut.Time.Day;
        var count = 0;
        for (var i = list.length - 1; i >= 1; i--) {
            var pre = list[i], last = list[i - 1];
            if (last.showTime) {
                count = 0;
                continue;
            }
            else if (last.time - pre.time < Constant_1.SHOW_TIME_MAX_INTERVAL && count < 9) {
                count += 1;
                last.showTime = '';
                continue;
            }
            // cc.log(count)
            count = 0;
            if (zero <= last.time) {
                last.showTime = ut.dateFormat('hh:mm', last.time);
            }
            else {
                last.showTime = yesterdayZero <= last.time ? assetsMgr.lang('ui.yesterday_time', ut.dateFormat('hh:mm', last.time)) : ut.dateFormat('yyyy-MM-dd hh:mm', last.time);
            }
        }
    };
    // 获取士兵的训练 只用于非战斗下使用
    GameHelper.prototype.getPawnASatr = function (uid) {
        var astar = this.pawnAstarMap[uid];
        if (!astar) {
            astar = this.pawnAstarMap[uid] = new AStar8_1.default();
        }
        return astar;
    };
    GameHelper.prototype.cleanPawnAstarMap = function () {
        this.pawnAstarMap = {};
    };
    // 是否遗迹拥有者
    GameHelper.prototype.isAncientOwner = function (uid) {
        uid = uid || this.getUid();
        var obj = this.world.getAncientMap();
        for (var k in obj) {
            if (obj[k].owner === uid) {
                return true;
            }
        }
        return false;
    };
    // 获取文本换行数量
    GameHelper.prototype.getTextNewlineCount = function (text) {
        var _a;
        return ((_a = text === null || text === void 0 ? void 0 : text.match(/\n/g)) === null || _a === void 0 ? void 0 : _a.length) || 0;
    };
    // 翻译文本
    GameHelper.prototype.translateText = function (data, type) {
        data.translate = { req: true, text: '' };
        // 请求翻译
        this.net.send('lobby/HD_TranslateText', { uid: type + '_' + data.uid, text: data.content, lang: mc.lang });
        return data;
    };
    GameHelper.prototype.checkChatEquip = function (data) {
        if (!data || !ut.isObject(data) || !data.id) {
            return null;
        }
        try {
            var equip = new EquipInfo_1.default().fromSvr(data);
            if (equip === null || equip === void 0 ? void 0 : equip.json) {
                return equip;
            }
        }
        catch (error) {
        }
        return null;
    };
    GameHelper.prototype.checkChatPortrayal = function (data) {
        if (!data || !ut.isObject(data) || !data.id) {
            return null;
        }
        try {
            var portrayal = new PortrayalInfo_1.default().fromSvr(data);
            if (portrayal === null || portrayal === void 0 ? void 0 : portrayal.json) {
                return portrayal;
            }
        }
        catch (error) {
        }
        return null;
    };
    // 检测是否发送的坐标
    GameHelper.prototype.checkChatTextIsHasPoint = function (text) {
        if (!text) {
            return '';
        }
        // 匹配所有可能坐标的模式（宽松匹配）
        var coordPattern = /\[([^\]]+)\]/g, has = false;
        var newText = text.replace(coordPattern, function (origin, content) {
            // 严格验证坐标有效性
            var numPattern = /^\s*([\d.]+)\s*,\s*([\d.]+)\s*$/;
            var isValid = numPattern.test(content);
            if (isValid) {
                var arr = origin.slice(1, origin.length - 1).split(',');
                if (arr.length === 2) {
                    var x = Number(arr[0]), y = Number(arr[1]);
                    if (isNaN(x) || isNaN(y)) {
                        return origin;
                    }
                    x = Math.floor(x), y = Math.floor(y);
                    var size = MapHelper_1.mapHelper.MAP_SIZE;
                    if (x >= 0 && x < size.x && y >= 0 && y < size.y) {
                        has = true;
                        return "<color=#49983C><on click=\"onClickPoint\" param=\"" + x + "," + y + "\">" + origin + "</on></color>";
                    }
                }
            }
            return origin;
        });
        return has ? newText : '';
    };
    // 是否恢复信息
    GameHelper.prototype.checkChatHasReplyInfo = function (data) {
        if (!data) {
            return null;
        }
        var name = data.senderNickname || exports.gameHpr.getPlayerName(data.sender);
        var content = name + ': ' + data.content;
        return { uid: data.uid, text: content };
    };
    GameHelper.prototype.encodeURIObj = function (obj, question) {
        if (obj === void 0) { obj = {}; }
        if (question === void 0) { question = false; }
        var kvs = [];
        var url = '';
        for (var k in obj) {
            kvs.push(encodeURIComponent(k) + '=' + encodeURIComponent(obj[k]));
        }
        if (kvs.length > 0) {
            if (question) {
                url += '?';
            }
            url += kvs.join('&');
        }
        return url;
    };
    // 获取战斗颜色
    GameHelper.prototype.getBattleHpBarColor = function (data) {
        if (data.isOwner()) {
            return Constant_1.BATTLE_HPBAR_COLOR.m;
        }
        else if (this.isOneAlliance(data.owner)) {
            return Constant_1.BATTLE_HPBAR_COLOR.f;
        }
        return Constant_1.BATTLE_HPBAR_COLOR[data.getBattleCamp()] || Constant_1.BATTLE_HPBAR_COLOR[0];
    };
    // 获取战斗时火焰颜色
    GameHelper.prototype.getBattleFireBarColor = function (data) {
        if (data.isOwner()) {
            return Constant_1.BATTLE_FIRE_COLOR.m;
        }
        else if (this.isOneAlliance(data.owner)) {
            return Constant_1.BATTLE_FIRE_COLOR.f;
        }
        return Constant_1.BATTLE_FIRE_COLOR[data.getBattleCamp()] || Constant_1.BATTLE_FIRE_COLOR[0];
    };
    // 通过设备网络状态获取相应的提示文本
    GameHelper.prototype.getTextByNetworkStatus = function (str) {
        // 先获取是否打开网络
        var text = '';
        var open = cc.sys.getNetworkType() !== cc.sys.NetworkType.NONE;
        // 如果已经打开，再获取是否允许访问网络
        if (open) {
            open = JsbHelper_1.jsbHelper.getNetworkAccessPermission();
            if (!open) {
                text = 'login.net_forbidden_desc';
            }
        }
        else {
            text = cc.sys.os === cc.sys.OS_IOS ? 'login.net_closed_desc_ios' : 'login.net_closed_desc_android';
        }
        return text || str;
    };
    // 检测允许通知弹窗
    GameHelper.prototype.checkNoticePermission = function (type) {
        return __awaiter(this, void 0, void 0, function () {
            var time, cnt, data, auth;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (this.isNoviceMode || this.guide.isWorking() || (type === Enums_1.NoticePermissionType.PUSH && mc.getOpenPnls().has('key', 'main/SeasonSwitch')))
                            return [2 /*return*/];
                        time = 0, cnt = 0;
                        data = storageMgr.loadString('notice_permission');
                        if (data) {
                            _a = __read(ut.stringToNumbers(data, '_'), 2), time = _a[0], cnt = _a[1];
                        }
                        if (!(!data || Date.now() - time >= Constant_1.NOTICE_PERMISSION_CD * cnt)) return [3 /*break*/, 2];
                        return [4 /*yield*/, JsbHelper_1.jsbHelper.checkNoticePer()];
                    case 1:
                        auth = _b.sent();
                        if (!auth) {
                            ViewHelper_1.viewHelper.showPnl('common/NoticePermission' + type).then(function (pnl) {
                                if (pnl === null || pnl === void 0 ? void 0 : pnl.isValid) {
                                    time = Date.now(), cnt = Math.min(++cnt, 7);
                                    storageMgr.saveString('notice_permission', time + '_' + cnt);
                                }
                            });
                        }
                        else if (!this.user.getFcmConfs().has(0)) { // 检测游戏内的通知开关是否打开
                            time = Date.now(), cnt = Math.min(++cnt, 7);
                            storageMgr.saveString('notice_permission', time + '_' + cnt);
                            ViewHelper_1.viewHelper.showMessageBox('ui.open_game_notice_permission', {
                                ok: function () { return ViewHelper_1.viewHelper.showPnl('menu/FcmSet'); },
                                okText: 'ui.button_goto_open'
                            });
                        }
                        _b.label = 2;
                    case 2: return [2 /*return*/];
                }
            });
        });
    };
    // 获取画像技能说明参数
    GameHelper.prototype.getPortrayalSkillDescParams = function (id) {
        var json = assetsMgr.getJsonData('portrayalSkill', id);
        var arr = [];
        if (json.value) {
            arr.push('[' + json.value.replace(',', '-') + ']' + json.suffix);
        }
        if (json.target) {
            arr.push(json.target);
        }
        return arr;
    };
    // 获取频道弹幕设置信息
    // 世界、联盟频道弹幕开关分开单独设置
    GameHelper.prototype.getChatChannelBarrageSettingInfo = function (tab) {
        var _a, _b, _c, _d, _e;
        var isWorld = tab === 0, isAlli = tab === 1, key = Enums_1.PreferenceKey.BARRAGE_AREA_RATIO + tab, getType = (_a = this.user.getLocalPreferenceDataBySid(key)) !== null && _a !== void 0 ? _a : (isAlli ? 1 : 0);
        if (isWorld) { // 世界
            var cur = (_b = this.user.getLocalPreferenceData(Enums_1.PreferenceKey.WORLD_CHAT_CHANNEL)) !== null && _b !== void 0 ? _b : '0';
            if (cur && cur !== '0') { // 主频道区分开
                key += '_' + cur;
            }
            getType = (_c = this.user.getLocalPreferenceData(key)) !== null && _c !== void 0 ? _c : 0;
        }
        else if (isAlli) { // 联盟频道
            var cur = (_d = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.ALLI_CHAT_CHANNEL)) !== null && _d !== void 0 ? _d : '0';
            if (cur && cur !== '0') { // 主频道区分开
                key += '_' + cur;
                getType = (_e = this.user.getLocalPreferenceDataBySid(key)) !== null && _e !== void 0 ? _e : 1;
            }
        }
        return { key: key, getType: getType };
    };
    // 获取研究槽位的idMap
    GameHelper.prototype.getStudySlotIdMap = function (slotMap) {
        var idMap = {};
        for (var lv in slotMap) {
            var slot = slotMap[lv];
            if (slot === null || slot === void 0 ? void 0 : slot.isYetStudy()) {
                idMap[slot.id] = true;
            }
        }
        return idMap;
    };
    // 获取士兵费用
    GameHelper.prototype.getPawnCost = function (id, lv, json) {
        var arr = [];
        if (lv < 0 || lv >= Constant_1.PAWN_COST_LV_LIST.length) {
            return arr;
        }
        else if (lv === 0) {
            json = json || assetsMgr.getJsonData('pawnBase', id);
            arr.push.apply(arr, __spread(exports.gameHpr.stringToCTypes(json.drill_cost)));
        }
        else {
            var attrId = id * 1000 + lv;
            json = json || assetsMgr.getJsonData('pawnAttr', attrId);
            arr.push.apply(arr, __spread(exports.gameHpr.stringToCTypes(json.lv_cost)));
        }
        var baseCost = this.isInLobby() ? this.lobby.getPawnBaseCost(id) : this.world.getPawnBaseCost(id);
        if (baseCost) {
            var costVal_1 = Constant_1.PAWN_COST_LV_LIST[lv] * baseCost;
            // 替换配置表中获取到的粮食消耗
            arr.forEach(function (m) {
                if (m.type === Enums_1.CType.CEREAL) {
                    m.count = costVal_1;
                }
            });
        }
        return arr;
    };
    // 获取士兵招募时间或训练时间
    GameHelper.prototype.getPawnCostTime = function (id, lv, json) {
        var time = 0;
        if (lv < 0 || lv >= Constant_1.PAWN_COST_LV_LIST.length) {
            return time;
        }
        else if (lv === 0) {
            json = json || assetsMgr.getJsonData('pawnBase', id);
            time = json.drill_time;
        }
        else {
            var attrId = id * 1000 + lv;
            json = json || assetsMgr.getJsonData('pawnAttr', attrId);
            time = json.lv_time;
        }
        var baseCost = this.isInLobby() ? this.lobby.getPawnBaseCost(id) : this.world.getPawnBaseCost(id);
        if (baseCost) {
            time = Constant_1.PAWN_COST_LV_LIST[lv] * baseCost;
        }
        return time;
    };
    // 获取世界事件
    GameHelper.prototype.getWorldEventValue = function (type) {
        return this.world.getEventValue(type);
    };
    // 获取额外积分
    GameHelper.prototype.getExtraScore = function () {
        if (this.world.isKarmicMahjong()) {
            return 0; //血战到底模式 没有额外积分
        }
        var over = this.world.getGameOverInfo();
        if (!over) {
            return 0;
        }
        else if (over.winType === 1) { //个人
            return over.winUid === this.getUid() ? 600 : 0;
        }
        else if (over.winType === 2) { //联盟
            return over.winUid === this.alliance.getUid() ? 600 : 0;
        }
        return 0;
    };
    // 获取城边行军加速倍速
    GameHelper.prototype.getMainOutMarchSeepUpDis = function (target, armyIndex) {
        var mainCityIndex = this.player.getMainCityIndex(), maxDis = Object.keys(Constant_1.MAIN_CITY_MARCH_SPEED).length;
        // 先获取和主城的距离
        var tdis = this.getToMapCellDis(target, mainCityIndex);
        var adis = this.getToMapCellDis(armyIndex, mainCityIndex);
        // 都在范围内才加速
        if (tdis <= maxDis && adis <= maxDis) {
            return Math.max(tdis, adis);
        }
        return -1;
    };
    // 金币、元宝消耗提示
    GameHelper.prototype.costDeductTip = function (json) {
        var cost = 0, costType = '';
        if (json.gold > 0) {
            cost = json.gold;
            costType = 'gold';
        }
        else if (json.ingot > 0) {
            cost = json.ingot;
            costType = 'ingot';
        }
        if (costType === 'gold') {
            if (exports.gameHpr.user.getGold() < json.gold) {
                ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.GOLD_NOT_ENOUGH);
                return true;
            }
        }
        else if (costType === 'ingot') {
            if (exports.gameHpr.user.getIngot() < json.ingot) {
                ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.INGOT_NOT_ENOUGH);
                return true;
            }
        }
        return false;
    };
    return GameHelper;
}());
exports.gameHpr = new GameHelper();
if (cc.sys.isBrowser) {
    window['gameHpr'] = exports.gameHpr;
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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