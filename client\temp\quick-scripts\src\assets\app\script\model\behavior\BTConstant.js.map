{"version": 3, "sources": ["assets\\app\\script\\model\\behavior\\BTConstant.ts"], "names": [], "mappings": ";;;;;;;AACA,IAAK,MAKJ;AALD,WAAK,MAAM;IACP,iCAAuB,CAAA;IACvB,iCAAuB,CAAA;IACvB,2BAAiB,CAAA;IACjB,iCAAuB,CAAA;AAC3B,CAAC,EALI,MAAM,KAAN,MAAM,QAKV;AAoCG,wBAAM;AAlCV,IAAK,OAKJ;AALD,WAAK,OAAO;IACR,2CAAW,CAAA;IACX,2CAAW,CAAA;IACX,2CAAW,CAAA;IACX,uCAAS,CAAA;AACb,CAAC,EALI,OAAO,KAAP,OAAO,QAKX;AA8BG,0BAAO;AA5BX,IAAM,cAAc,GAAG;IACnB,MAAM,EAAE;QACJ,EAAE,EAAE,KAAK;KACZ;IACD,OAAO,EAAE;QACL,EAAE,EAAE,KAAK;KACZ;IACD,MAAM,EAAE;QACJ,EAAE,EAAE,KAAK;KACZ;IACD,OAAO,EAAE;QACL,EAAE,EAAE,KAAK;KACZ;IACD,OAAO,EAAE;QACL,EAAE,EAAE,KAAK;KACZ;CACJ,CAAA;AAED,SAAS,eAAe,CAAC,EAAU,EAAE,MAAc;IAC/C,IAAM,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA;IACvC,IAAI,CAAC,QAAQ,EAAE;QACX,OAAO,EAAE,CAAA;KACZ;IACD,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;AAC7B,CAAC;AAKG,0CAAe", "file": "", "sourceRoot": "/", "sourcesContent": ["\nenum BTType {\n    COMPOSITE = 'composite', //组合节点\n    DECORATOR = 'decorator', //装饰节点\n    ACTION = 'action', //动作节点\n    CONDITION = 'condition', //条件节点\n}\n\nenum BTState {\n    SUCCESS = 1, //成功\n    FAILURE = 2, //失败\n    RUNNING = 3, //运行中\n    ERROR = 4, //错误\n}\n\nconst SKIN_BUFF_CONF = {\n    320501: { //剑盾 曹操\n        26: 26001,\n    },\n    3205102: { //剑盾buff\n        26: 26102,\n    },\n    320301: { //枪盾 张飞\n        11: 11001,\n    },\n    3203102: { //枪盾buff\n        11: 11102,\n    },\n    3304109: { //猎人buff\n        12: 12001,\n    },\n}\n\nfunction getBuffIDBySkin(id: number, skinId: number) {\n    const skinBuff = SKIN_BUFF_CONF[skinId]\n    if (!skinBuff) {\n        return id\n    }\n    return skinBuff[id] || id\n}\n\nexport {\n    BTType,\n    BTState,\n    getBuffIDBySkin,\n}"]}