{"version": 3, "sources": ["assets\\app\\script\\view\\common\\BuffInfoBoxPnlCtrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAuD;AACvD,2DAA0D;AAGlD,IAAA,OAAO,GAAK,EAAE,CAAC,UAAU,QAAlB,CAAmB;AAGlC;IAAgD,sCAAc;IAA9D;QAAA,qEAyDC;QAvDG,0BAA0B;QAClB,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,eAAS,GAAY,IAAI,CAAA,CAAC,qBAAqB;QAC/C,cAAQ,GAAY,IAAI,CAAA,CAAC,oBAAoB;;QA6CrD,iHAAiH;QACjH,2BAA2B;QAC3B,MAAM;QACN,iHAAiH;QAEjH,iHAAiH;IAErH,CAAC;IAnDG,MAAM;IAEC,4CAAe,GAAtB;QACI,OAAO,EAAE,CAAA;IACb,CAAC;IAEY,qCAAQ,GAArB;;;;;;KACC;IAEM,oCAAO,GAAd,UAAe,IAAa;;QACxB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC/D,qBAAS,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;QACxE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAA;QACnF,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;SAC7G;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAA;SACvE;QACD,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QACnD,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE;YAC3D,IAAI,GAAG,oBAAoB,CAAA,CAAC,QAAQ;SACvC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;YACrE,IAAI,GAAG,oBAAoB,CAAA,CAAC,aAAa;SAC5C;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,gBAAgB,IAAI,CAAC,MAAM,EAAE;YAC3D,IAAI,GAAG,oBAAoB,CAAA,CAAC,aAAa;YACzC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAA;SAC1B;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5E,IAAI,GAAG,kBAAkB,CAAA,CAAC,aAAa;SAC1C;QACD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACzC,SAAS;QACT,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,CAAA,KAAA,IAAI,CAAC,QAAQ,CAAA,CAAC,YAAY,qBAAC,IAAI,CAAC,GAAG,GAAK,IAAI,CAAC,SAAS,GAAC;SAC1D;IACL,CAAC;IAEM,qCAAQ,GAAf;IACA,CAAC;IAEM,oCAAO,GAAd;QACI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAhDgB,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAyDtC;IAAD,yBAAC;CAzDD,AAyDC,CAzD+C,EAAE,CAAC,WAAW,GAyD7D;kBAzDoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { BuffType } from \"../../common/constant/Enums\";\nimport { resHelper } from \"../../common/helper/ResHelper\";\nimport BuffObj from \"../../model/area/BuffObj\";\n\nconst { ccclass } = cc._decorator;\n\n@ccclass\nexport default class BuffInfoBoxPnlCtrl extends mc.BasePnlCtrl {\n\n    //@autocode property begin\n    private iconNode_: cc.Node = null // path://root/icon_n\n    private descNode_: cc.Node = null // path://root/desc_n\n    private tipNode_: cc.Node = null // path://root/tip_n\n    //@end\n\n    public listenEventMaps() {\n        return []\n    }\n\n    public async onCreate() {\n    }\n\n    public onEnter(data: BuffObj) {\n        this.iconNode_.Component(cc.MultiFrame).setFrame(data.iconType)\n        resHelper.loadBuffIcon(data.icon, this.iconNode_.Child('val'), this.key)\n        this.iconNode_.Child('name').setLocale<PERSON><PERSON>(data.name)\n        this.iconNode_.Child('type').setLocaleKey('ui.buff_effect_type_' + data.effectType)\n        if (data.round >= 0) {\n            this.iconNode_.Child('round').setLocaleKey('ui.surplus_round_count', Math.min(data.round, data.needRound))\n        } else {\n            this.iconNode_.Child('round').setLocaleKey('ui.forever_round_count')\n        }\n        let desc = data.desc, params = data.getDescParams()\n        if (data.type === BuffType.HIT_SUCK_BLOOD && data.value >= 30) {\n            desc = 'buffText.desc_16_1' //环刀特殊处理\n        } else if (data.type === BuffType.TOUGH && data.value >= data.tempParam) {\n            desc = 'buffText.desc_43_1' //曹仁 满层坚韧特殊处理\n        } else if (data.type === BuffType.CHECK_ABNEGATION && !params) {\n            desc = 'buffText.desc_64_1' //吕蒙 检测克己满的处理\n            params = data.tempParam\n        } else if (data.type === BuffType.COURAGEOUSLY && data.value >= data.tempParam) {\n            desc = 'buffText.desc_90' //典韦 满层奋勇特殊处理\n        }\n        this.descNode_.setLocaleKey(desc, params)\n        // 韬略buff\n        if (this.tipNode_.active = !!data.tip) {\n            this.tipNode_.setLocaleKey(data.tip, ...data.tipParams)\n        }\n    }\n\n    public onRemove() {\n    }\n\n    public onClean() {\n        assetsMgr.releaseTempResByTag(this.key)\n    }\n\n    // ----------------------------------------- button listener function -------------------------------------------\n    //@autocode button listener\n    //@end\n    // ----------------------------------------- event listener function --------------------------------------------\n\n    // ----------------------------------------- custom function ----------------------------------------------------\n\n}\n"]}