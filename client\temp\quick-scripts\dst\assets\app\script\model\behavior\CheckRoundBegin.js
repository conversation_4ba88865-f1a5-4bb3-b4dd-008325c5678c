
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/CheckRoundBegin.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd322fzN+TZGZrFCzVjywjwD', 'CheckRoundBegin');
// app/script/model/behavior/CheckRoundBegin.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var MapHelper_1 = require("../../common/helper/MapHelper");
var BaseAction_1 = require("./BaseAction");
var BTConstant_1 = require("./BTConstant");
// 检测回合开始时
var CheckRoundBegin = /** @class */ (function (_super) {
    __extends(CheckRoundBegin, _super);
    function CheckRoundBegin() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.isChangeFighterState = false; //是否改变士兵的状态了
        _this.flamingArmorEffect = null; //烈焰铠甲效果
        return _this;
    }
    CheckRoundBegin.prototype.onInit = function (conf) {
    };
    CheckRoundBegin.prototype.onOpen = function () {
        this.isChangeFighterState = false;
        // 烈焰铠甲
        this.flamingArmorEffect = this.target.getEquipEffectByType(Enums_1.EquipEffectType.FLAMING_ARMOR);
        this.setBlackboardData('isHasArmorEffect', !!this.flamingArmorEffect);
        // 定心盔 每5回合恢复
        var CHECK_CENTERING_HELMET = this.target.getBuff(Enums_1.BuffType.CHECK_CENTERING_HELMET);
        if (CHECK_CENTERING_HELMET) {
            CHECK_CENTERING_HELMET.value -= 1;
            if (CHECK_CENTERING_HELMET.value <= 0) {
                CHECK_CENTERING_HELMET.value = 5;
                this.setBlackboardData('isHasCenteringHelmet', true);
            }
        }
        // 赤金盾 每3回合获得一个护盾
        var CHECK_CRIMSONGOLD_SHIELD = this.target.getBuff(Enums_1.BuffType.CHECK_CRIMSONGOLD_SHIELD);
        if (CHECK_CRIMSONGOLD_SHIELD) {
            CHECK_CRIMSONGOLD_SHIELD.value -= 1;
            if (CHECK_CRIMSONGOLD_SHIELD.value <= 0) {
                CHECK_CRIMSONGOLD_SHIELD.value = 3;
                this.setBlackboardData('isHasCrimsongoldShield', true);
            }
        }
    };
    CheckRoundBegin.prototype.onLeave = function (state) {
        // 如果失败时 要处理连击问题
        if (state === BTConstant_1.BTState.FAILURE) {
            this.setTreeBlackboardData('batterCount', -1);
        }
        var attackTarget = this.target.getAttackTarget();
        if (attackTarget && attackTarget.isDie()) {
            this.target.changeAttackTarget(null);
        }
    };
    CheckRoundBegin.prototype.onTick = function (dt) {
        var heroSkill = this.target.getPortrayalSkill();
        if (this.target.waitRound > 0) {
            this.target.waitRound -= 1;
            return BTConstant_1.BTState.FAILURE; //是否需要等待一回合
        }
        else if (this.getBlackboardData('isHasArmorEffect')) {
            return this.checkFlamingArmor(dt); //焱阳铠
        }
        else if (this.getBlackboardData('isHasCenteringHelmet')) {
            return this.checkCenteringHelmet(dt); //定心盔
        }
        else if (this.getBlackboardData('isHasCrimsongoldShield')) {
            return this.checkCrimsongoldShield(dt); //赤金盾
        }
        else if (this.target.isHasBuffs(Enums_1.BuffType.DIZZINESS, Enums_1.BuffType.STAND_SHIELD, Enums_1.BuffType.PARALYSIS, Enums_1.BuffType.PARALYSIS_UP, Enums_1.BuffType.FEAR, Enums_1.BuffType.ANTICIPATION_DEFENSE)) {
            return BTConstant_1.BTState.FAILURE; //是否有眩晕buff 或者 立盾状态
        }
        else if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.HUANG_GAI && !this.target.isHasBuff(Enums_1.BuffType.CHECK_KUROU)) {
            return this.checkKuRou(dt, heroSkill); //黄盖 检测是否释放苦肉
        }
        else if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.DENG_AI && !this.target.isHasBuff(Enums_1.BuffType.CHECK_TONDEN)) {
            return this.checkTonden(dt, heroSkill); //邓艾 检测是否释放屯垦令
        }
        else if ((heroSkill === null || heroSkill === void 0 ? void 0 : heroSkill.id) === Enums_1.HeroType.SUN_SHANGXIANG && !this.getBlackboardData('isCheckLittleGirl')) {
            return this.checkLittleGirl(dt, heroSkill); //孙尚香 每5回合释放技能
        }
        else if (this.target.checkTriggerBuff(Enums_1.BuffType.CHECK_JADE_PENDANT)) {
            return this.checkJadePendant(); //检测玉佩
        }
        else if (this.target.getId() === Constant_1.PAWN_CROSSBOW_ID) {
            return this.checkPullString(dt); //检测拉弦
        }
        else if (this.target.getId() === Constant_1.AX_CAVALRY_ID) {
            this.target.setAnger(1); //给斧骑兵加1怒
        }
        return BTConstant_1.BTState.SUCCESS;
    };
    // 检测是否有焱阳铠 对1格内造成伤害
    CheckRoundBegin.prototype.checkFlamingArmor = function (dt) {
        var _a;
        if (!this.flamingArmorEffect) {
            this.flamingArmorEffect = this.target.getEquipEffectByType(Enums_1.EquipEffectType.FLAMING_ARMOR);
        }
        var currTime = (_a = this.getBlackboardData('currTimeFlamingArmor')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= 600) {
            this.setBlackboardData('isHasArmorEffect', false);
            return BTConstant_1.BTState.RUNNING;
        }
        // 增加时间
        this.setBlackboardData('currTimeFlamingArmor', currTime + dt);
        if (currTime === 0) {
            var trueDamage_1 = Math.round(this.target.getMaxHp() * 0.04);
            if (trueDamage_1 <= 0 || !this.ctrl.getRandom().chance(this.flamingArmorEffect.odds)) {
                this.setBlackboardData('isHasArmorEffect', false);
                return BTConstant_1.BTState.RUNNING;
            }
            // 对1格内造成伤害
            var arr = this.target.getCanAttackPawnByRange(this.target.getCanAttackFighters(), 1, 4, '');
            if (arr.length === 0) {
                this.setBlackboardData('isHasArmorEffect', false);
                return BTConstant_1.BTState.RUNNING;
            }
            var hasDie_1 = false;
            var owners_1 = [this.target.getOwner()];
            arr.forEach(function (m) {
                var damage = m.hitPrepDamageHandle(0, trueDamage_1).trueDamage;
                var v = m.onHit(damage, owners_1);
                var isDie = m.isDie();
                if (isDie) {
                    hasDie_1 = true;
                }
                m.changeState(Enums_1.PawnState.HIT, { trueDamage: v.damage, heal: v.heal, isDie: isDie, time: currTime });
            });
            // 如果有目标死了 需要重新获得攻击目标
            if (hasDie_1) {
                this.target.cleanCanAttackTargets();
            }
            this.ctrl.playBattleEffect([this.flamingArmorEffect.type * 10000 + 1], this.target.getPoint(), 'top');
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测是否有定心盔 对1格内友方回血
    CheckRoundBegin.prototype.checkCenteringHelmet = function (dt) {
        var _a;
        var currTime = (_a = this.getBlackboardData('currTimeCenteringHelmet')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= 600) {
            this.setBlackboardData('isHasCenteringHelmet', false);
            return BTConstant_1.BTState.RUNNING;
        }
        // 增加时间
        this.setBlackboardData('currTimeCenteringHelmet', currTime + dt);
        if (currTime === 0) {
            // 对1格内至多4个友方恢复生命
            var uid_1 = this.target.getUid(), point_1 = this.target.getPoint(), camp_1 = this.target.getCamp(), rang_1 = 1, count = 4;
            var fighters = this.ctrl.getFighters().filter(function (m) {
                return m.getCamp() === camp_1 && m.getUid() != uid_1 && m.isPawn() && MapHelper_1.mapHelper.getPointToPointDis(m.getPoint(), point_1) == rang_1 && !m.isFullHp();
            }).sort(function (a, b) {
                var aw = Math.round((1 - a.getHpRatio()) * 100) * 1000 + (999 - a.getAttackIndex());
                var bw = Math.round((1 - b.getHpRatio()) * 100) * 1000 + (999 - b.getAttackIndex());
                return bw - aw;
            });
            var friends = fighters.slice(0, count);
            if (!this.target.isFullHp()) {
                friends.push(this.target);
            }
            if (friends.length === 0) {
                this.setBlackboardData('isHasCenteringHelmet', false);
                return BTConstant_1.BTState.RUNNING;
            }
            friends.forEach(function (m) {
                var val = Math.round((m.getMaxHp() - m.getCurHp()) * 0.25);
                val = m.onHeal(val);
                m.changeState(Enums_1.PawnState.HEAL, { val: val });
            });
            this.ctrl.playBattleEffect([390001], this.target.getPoint(), 'di');
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测是否有赤金盾 获得护盾
    CheckRoundBegin.prototype.checkCrimsongoldShield = function (dt) {
        var _a;
        var currTime = (_a = this.getBlackboardData('currTimeCrimsongoldShield')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= 600) {
            this.setBlackboardData('isHasCrimsongoldShield', false);
            return BTConstant_1.BTState.RUNNING;
        }
        // 增加时间
        this.setBlackboardData('currTimeCrimsongoldShield', currTime + dt);
        if (currTime === 0) {
            var shieldVal = Math.round(this.target.getMaxHp() * 0.04);
            this.target.addBuffValue(Enums_1.BuffType.CRIMSONGOLD_SHIELD, this.target.getUid(), shieldVal);
            this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.SHIELD, this.target.getPoint());
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测苦肉
    CheckRoundBegin.prototype.checkKuRou = function (dt, skill) {
        var _this = this;
        var _a, _b;
        if (this.target.getHpRatio() <= skill.params) {
            this.target.addBuff(Enums_1.BuffType.CHECK_KUROU, this.target.getUid(), 1);
            return BTConstant_1.BTState.RUNNING; //血量低于20% 不苦肉
        }
        var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= 850) {
            this.target.addBuff(Enums_1.BuffType.CHECK_KUROU, this.target.getUid(), 1);
            return BTConstant_1.BTState.RUNNING;
        }
        // 增加时间
        this.setBlackboardData('currTime', currTime + dt);
        if (currTime >= 600) {
            if (!this.getBlackboardData('isHit')) {
                this.setBlackboardData('isHit', true);
                var damage = Math.round(this.target.getMaxHp() * skill.params);
                var camp_2 = this.target.camp, point_2 = this.target.getPoint(), index = this.target.getAreaIndex(), uid_2 = this.target.getUid();
                // 扣除血量
                var v = this.target.onHit(damage, []);
                eventCenter.emit(EventType_1.default.PLAY_FLUTTER_HP, { index: index, uid: uid_2, trueDamage: -v.damage, hasShield: !!((_b = this.target.entity) === null || _b === void 0 ? void 0 : _b.getShieldValue()) });
                // 给队友加护盾
                var shield_1 = Math.round(damage * skill.value * 0.01);
                var arr = this.ctrl.getFighters().filter(function (m) { return m.camp === camp_2 && m.isPawn() && m.getUid() !== uid_2 && !m.isHasBuff(Enums_1.BuffType.KUROU_SHIELD); });
                arr.sort(function (a, b) {
                    var aw = 99 - MapHelper_1.mapHelper.getPointToPointDis(a.getPoint(), point_2);
                    var bw = 99 - MapHelper_1.mapHelper.getPointToPointDis(b.getPoint(), point_2);
                    aw = aw * 1000 + Math.floor((1 - a.getHpRatio()) * 100);
                    bw = bw * 1000 + Math.floor((1 - b.getHpRatio()) * 100);
                    aw = aw * 1000 + (999 - a.getAttackIndex());
                    bw = bw * 1000 + (999 - b.getAttackIndex());
                    return bw - aw;
                });
                arr = arr.slice(0, skill.target);
                arr.push(this.target);
                arr.forEach(function (m) {
                    m.addBuffValue(Enums_1.BuffType.KUROU_SHIELD, uid_2, shield_1);
                    m.addBuff(Enums_1.BuffType.KUROU_ADD_ATTACK, uid_2, 1);
                    _this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.KUROU, m.getPoint());
                });
            }
        }
        if (!this.isChangeFighterState) {
            this.isChangeFighterState = true;
            this.target.changeState(Enums_1.PawnState.SKILL, { currAttackTime: 0, sound: '', skillName: 'kurou' });
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测屯垦
    CheckRoundBegin.prototype.checkTonden = function (dt, skill) {
        var _this = this;
        var _a;
        var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0;
        if (currTime >= 1500) {
            this.target.addBuff(Enums_1.BuffType.CHECK_TONDEN, this.target.getUid(), 1);
            return BTConstant_1.BTState.RUNNING;
        }
        // 增加时间
        this.setBlackboardData('currTime', currTime + dt);
        if (currTime >= 1000) {
            if (!this.getBlackboardData('isHit')) {
                this.setBlackboardData('isHit', true);
                var camp_3 = this.target.camp, point_3 = this.target.getPoint(), uid_3 = this.target.getUid();
                var arr = this.ctrl.getFighters().filter(function (m) { return m.camp === camp_3 && m.isPawn() && m.getUid() !== uid_3 && !m.isHasBuffs(Enums_1.BuffType.TONDEN_BEGIN, Enums_1.BuffType.TONDEN_RECOVER); });
                arr.sort(function (a, b) {
                    var aw = 99 - MapHelper_1.mapHelper.getPointToPointDis(a.getPoint(), point_3);
                    var bw = 99 - MapHelper_1.mapHelper.getPointToPointDis(b.getPoint(), point_3);
                    aw = aw * 1000 + (999 - a.getAttackIndex());
                    bw = bw * 1000 + (999 - b.getAttackIndex());
                    return bw - aw;
                });
                arr = arr.slice(0, skill.target);
                arr.push(this.target);
                arr.forEach(function (m) {
                    m.addBuffValue(Enums_1.BuffType.TONDEN_BEGIN, uid_3, skill.value);
                    _this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.TONDEN, m.getPoint());
                });
            }
        }
        if (!this.isChangeFighterState) {
            this.isChangeFighterState = true;
            this.target.changeState(Enums_1.PawnState.SKILL, { currAttackTime: 0, sound: '', skillName: 'tonden' });
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 孙尚香 每5回合释放技能
    CheckRoundBegin.prototype.checkLittleGirl = function (dt, skill) {
        var _this = this;
        var _a;
        var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0;
        if (currTime === 0) {
            var round = skill.params;
            var buff = this.target.getBuff(Enums_1.BuffType.CHECK_LITTLE_GIRL);
            if (!buff) {
                buff = this.target.addBuffValue(Enums_1.BuffType.CHECK_LITTLE_GIRL, this.target.getUid(), round);
            }
            if (buff.value > 1) {
                buff.value -= 1;
                this.setBlackboardData('isCheckLittleGirl', true);
                return BTConstant_1.BTState.RUNNING;
            }
            buff.value = round;
            this.ctrl.playBattleEffect([111001], this.target.getPoint());
        }
        // 增加时间
        this.setBlackboardData('currTime', currTime + dt);
        if (currTime >= 300) {
            var camp_4 = this.target.getCamp(), uid_4 = this.target.getUid(), point_4 = this.target.getPoint();
            var fTargets_1 = [], eTargets_1 = [];
            this.ctrl.getFighters().forEach(function (m) {
                if (!m.isPawn()) {
                    return;
                }
                else if (m.getCamp() === camp_4) {
                    fTargets_1.push(m);
                }
                else if (!m.isHasBuff(Enums_1.BuffType.DIZZINESS)) {
                    eTargets_1.push(m);
                }
            });
            var cnt = this.target.entity.isMaxLv() ? 2 : 1;
            // 恢复生命比最低的友方
            if (fTargets_1.length > 0) {
                fTargets_1.sort(function (a, b) {
                    var aw = Math.floor((1 - a.getHpRatio()) * 100);
                    var bw = Math.floor((1 - b.getHpRatio()) * 100);
                    aw = aw * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point_4, a.getPoint()));
                    bw = bw * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point_4, b.getPoint()));
                    aw = aw * 1000 + (999 - a.getAttackIndex());
                    bw = bw * 1000 + (999 - b.getAttackIndex());
                    return bw - aw;
                }).slice(0, cnt).forEach(function (m) {
                    var val = Math.round(m.getMaxHp() * skill.value * 0.01);
                    var v = m.onHeal(val);
                    m.changeState(Enums_1.PawnState.HEAL, { val: v, time: 0 });
                    _this.ctrl.playBattleEffect([111002], m.getPoint());
                });
            }
            // 眩晕敌方攻击力最高的
            if (eTargets_1.length > 0) {
                eTargets_1.sort(function (a, b) {
                    var aw = a.getActAttack() * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point_4, a.getPoint()));
                    var bw = b.getActAttack() * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(point_4, b.getPoint()));
                    aw = aw * 1000 + (999 - a.getAttackIndex());
                    bw = bw * 1000 + (999 - b.getAttackIndex());
                    return bw - aw;
                }).slice(0, cnt).forEach(function (m) {
                    m.addBuff(Enums_1.BuffType.DIZZINESS, uid_4, 3);
                    _this.ctrl.playBattleEffect([111003], m.getPoint());
                });
            }
            this.setBlackboardData('isCheckLittleGirl', true);
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测玉佩
    CheckRoundBegin.prototype.checkJadePendant = function () {
        var _this = this;
        this.target.removeBuff(Enums_1.BuffType.CHECK_JADE_PENDANT);
        var effect = this.target.getEquipEffectByType(Enums_1.EquipEffectType.BATTLE_BEGIN_SHIELD);
        if (effect) {
            var camp_5 = this.target.camp, point_5 = this.target.getPoint(), uid_5 = this.target.getUid();
            var shield_2 = Math.round(this.target.getMaxHp() * effect.value * 0.01);
            var arr = this.ctrl.getFighters().filter(function (m) { return m.camp === camp_5 && m.isPawn() && !m.isHasBuff(Enums_1.BuffType.BATTLE_BEGIN_SHIELD); });
            arr.sort(function (a, b) {
                var aw = 99 - MapHelper_1.mapHelper.getPointToPointDis(a.getPoint(), point_5);
                var bw = 99 - MapHelper_1.mapHelper.getPointToPointDis(b.getPoint(), point_5);
                aw = aw * 1000 + Math.floor((1 - a.getHpRatio()) * 100);
                bw = bw * 1000 + Math.floor((1 - b.getHpRatio()) * 100);
                aw = aw * 1000 + (999 - a.getAttackIndex());
                bw = bw * 1000 + (999 - b.getAttackIndex());
                return bw - aw;
            });
            arr = arr.slice(0, 3);
            arr.forEach(function (m) {
                m.addBuffValue(Enums_1.BuffType.BATTLE_BEGIN_SHIELD, uid_5, shield_2); //添加护盾buff
                m.addBuff(Enums_1.BuffType.WITHSTAND, uid_5, 1); //抵挡一次技能攻击
                _this.ctrl.playBattleEffect(Constant_1.BATTLE_EFFECT_TYPE.SHIELD, m.getPoint());
            });
        }
        return BTConstant_1.BTState.RUNNING;
    };
    // 检测是否需要拉弦 如果还没拉弦 那么就播放拉弦的动画
    CheckRoundBegin.prototype.checkPullString = function (dt) {
        var _a;
        var curAnger = this.target.getCurAnger();
        if (curAnger > 0) {
            return BTConstant_1.BTState.SUCCESS;
        }
        else if (curAnger === 0) {
            this.target.setAnger(-1); //让状态变成拉弦状态
        }
        var animTimes = this.target.entity.getAttackAnimTimes()[0];
        if ((animTimes === null || animTimes === void 0 ? void 0 : animTimes.length) > 0) {
            var needAttackTime = Math.floor(animTimes[0] * 1000);
            var currTime = (_a = this.getBlackboardData('currTime')) !== null && _a !== void 0 ? _a : 0;
            if (currTime >= needAttackTime) {
                var val = 1;
                if (this.target.getStrategyValue(31402) > 0) {
                    val = 2; //韬略
                }
                this.target.setAnger(val);
                this.target.changeState(Enums_1.PawnState.STAND);
                return BTConstant_1.BTState.FAILURE;
            }
            // 增加时间
            this.setBlackboardData('currTime', currTime + dt);
            // 设置士兵状态 更新视图信息
            if (!this.isChangeFighterState) {
                this.isChangeFighterState = true;
                this.target.changeState(Enums_1.PawnState.SKILL, { currAttackTime: currTime });
            }
            return BTConstant_1.BTState.RUNNING;
        }
        return BTConstant_1.BTState.SUCCESS;
    };
    return CheckRoundBegin;
}(BaseAction_1.default));
exports.default = CheckRoundBegin;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcQ2hlY2tSb3VuZEJlZ2luLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDJEQUFxRztBQUNyRyxxREFBNkY7QUFDN0YsMERBQXFEO0FBQ3JELDJEQUEwRDtBQUcxRCwyQ0FBc0M7QUFDdEMsMkNBQXVDO0FBRXZDLFVBQVU7QUFDVjtJQUE2QyxtQ0FBVTtJQUF2RDtRQUFBLHFFQXFZQztRQW5ZVywwQkFBb0IsR0FBWSxLQUFLLENBQUEsQ0FBQyxZQUFZO1FBQ2xELHdCQUFrQixHQUFtQixJQUFJLENBQUEsQ0FBQyxRQUFROztJQWtZOUQsQ0FBQztJQWhZVSxnQ0FBTSxHQUFiLFVBQWMsSUFBUztJQUN2QixDQUFDO0lBRU0sZ0NBQU0sR0FBYjtRQUNJLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxLQUFLLENBQUE7UUFDakMsT0FBTztRQUNQLElBQUksQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLHVCQUFlLENBQUMsYUFBYSxDQUFDLENBQUE7UUFDekYsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtRQUNyRSxhQUFhO1FBQ2IsSUFBTSxzQkFBc0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLHNCQUFzQixDQUFDLENBQUE7UUFDbkYsSUFBSSxzQkFBc0IsRUFBRTtZQUN4QixzQkFBc0IsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFBO1lBQ2pDLElBQUksc0JBQXNCLENBQUMsS0FBSyxJQUFJLENBQUMsRUFBRTtnQkFDbkMsc0JBQXNCLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTtnQkFDaEMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxDQUFBO2FBQ3ZEO1NBQ0o7UUFDRCxpQkFBaUI7UUFDakIsSUFBTSx3QkFBd0IsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLHdCQUF3QixDQUFDLENBQUE7UUFDdkYsSUFBSSx3QkFBd0IsRUFBRTtZQUMxQix3QkFBd0IsQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFBO1lBQ25DLElBQUksd0JBQXdCLENBQUMsS0FBSyxJQUFJLENBQUMsRUFBRTtnQkFDckMsd0JBQXdCLENBQUMsS0FBSyxHQUFHLENBQUMsQ0FBQTtnQkFDbEMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHdCQUF3QixFQUFFLElBQUksQ0FBQyxDQUFBO2FBQ3pEO1NBQ0o7SUFDTCxDQUFDO0lBRU0saUNBQU8sR0FBZCxVQUFlLEtBQWM7UUFDekIsZ0JBQWdCO1FBQ2hCLElBQUksS0FBSyxLQUFLLG9CQUFPLENBQUMsT0FBTyxFQUFFO1lBQzNCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTtTQUNoRDtRQUNELElBQU0sWUFBWSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsZUFBZSxFQUFFLENBQUE7UUFDbEQsSUFBSSxZQUFZLElBQUksWUFBWSxDQUFDLEtBQUssRUFBRSxFQUFFO1lBQ3RDLElBQUksQ0FBQyxNQUFNLENBQUMsa0JBQWtCLENBQUMsSUFBSSxDQUFDLENBQUE7U0FDdkM7SUFDTCxDQUFDO0lBRU0sZ0NBQU0sR0FBYixVQUFjLEVBQVU7UUFDcEIsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRSxDQUFBO1FBQ2pELElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLEdBQUcsQ0FBQyxFQUFFO1lBQzNCLElBQUksQ0FBQyxNQUFNLENBQUMsU0FBUyxJQUFJLENBQUMsQ0FBQTtZQUMxQixPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBLENBQUMsV0FBVztTQUNyQzthQUFNLElBQUksSUFBSSxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixDQUFDLEVBQUU7WUFDbkQsT0FBTyxJQUFJLENBQUMsaUJBQWlCLENBQUMsRUFBRSxDQUFDLENBQUEsQ0FBQyxLQUFLO1NBQzFDO2FBQU0sSUFBSSxJQUFJLENBQUMsaUJBQWlCLENBQUMsc0JBQXNCLENBQUMsRUFBRTtZQUN2RCxPQUFPLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxFQUFFLENBQUMsQ0FBQSxDQUFDLEtBQUs7U0FDN0M7YUFBTSxJQUFJLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyx3QkFBd0IsQ0FBQyxFQUFFO1lBQ3pELE9BQU8sSUFBSSxDQUFDLHNCQUFzQixDQUFDLEVBQUUsQ0FBQyxDQUFBLENBQUMsS0FBSztTQUMvQzthQUFNLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxVQUFVLENBQzdCLGdCQUFRLENBQUMsU0FBUyxFQUNsQixnQkFBUSxDQUFDLFlBQVksRUFDckIsZ0JBQVEsQ0FBQyxTQUFTLEVBQ2xCLGdCQUFRLENBQUMsWUFBWSxFQUNyQixnQkFBUSxDQUFDLElBQUksRUFDYixnQkFBUSxDQUFDLG9CQUFvQixDQUNoQyxFQUFFO1lBQ0MsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQSxDQUFDLG1CQUFtQjtTQUM3QzthQUFNLElBQUksQ0FBQSxTQUFTLGFBQVQsU0FBUyx1QkFBVCxTQUFTLENBQUUsRUFBRSxNQUFLLGdCQUFRLENBQUMsU0FBUyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsZ0JBQVEsQ0FBQyxXQUFXLENBQUMsRUFBRTtZQUM3RixPQUFPLElBQUksQ0FBQyxVQUFVLENBQUMsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFBLENBQUMsYUFBYTtTQUN0RDthQUFNLElBQUksQ0FBQSxTQUFTLGFBQVQsU0FBUyx1QkFBVCxTQUFTLENBQUUsRUFBRSxNQUFLLGdCQUFRLENBQUMsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsZ0JBQVEsQ0FBQyxZQUFZLENBQUMsRUFBRTtZQUM1RixPQUFPLElBQUksQ0FBQyxXQUFXLENBQUMsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFBLENBQUMsY0FBYztTQUN4RDthQUFNLElBQUksQ0FBQSxTQUFTLGFBQVQsU0FBUyx1QkFBVCxTQUFTLENBQUUsRUFBRSxNQUFLLGdCQUFRLENBQUMsY0FBYyxJQUFJLENBQUMsSUFBSSxDQUFDLGlCQUFpQixDQUFDLG1CQUFtQixDQUFDLEVBQUU7WUFDbEcsT0FBTyxJQUFJLENBQUMsZUFBZSxDQUFDLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQSxDQUFDLGNBQWM7U0FDNUQ7YUFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsZ0JBQWdCLENBQUMsZ0JBQVEsQ0FBQyxrQkFBa0IsQ0FBQyxFQUFFO1lBQ2xFLE9BQU8sSUFBSSxDQUFDLGdCQUFnQixFQUFFLENBQUEsQ0FBQyxNQUFNO1NBQ3hDO2FBQU0sSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssRUFBRSxLQUFLLDJCQUFnQixFQUFFO1lBQ2pELE9BQU8sSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUMsQ0FBQSxDQUFDLE1BQU07U0FDekM7YUFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssd0JBQWEsRUFBRTtZQUM5QyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQSxDQUFDLFNBQVM7U0FDcEM7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxvQkFBb0I7SUFDWiwyQ0FBaUIsR0FBekIsVUFBMEIsRUFBVTs7UUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsRUFBRTtZQUMxQixJQUFJLENBQUMsa0JBQWtCLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsQ0FBQyx1QkFBZSxDQUFDLGFBQWEsQ0FBQyxDQUFBO1NBQzVGO1FBQ0QsSUFBSSxRQUFRLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHNCQUFzQixDQUFDLG1DQUFJLENBQUMsQ0FBQTtRQUMxRSxJQUFJLFFBQVEsSUFBSSxHQUFHLEVBQUU7WUFDakIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQ2pELE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHNCQUFzQixFQUFFLFFBQVEsR0FBRyxFQUFFLENBQUMsQ0FBQTtRQUM3RCxJQUFJLFFBQVEsS0FBSyxDQUFDLEVBQUU7WUFDaEIsSUFBTSxZQUFVLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFBO1lBQzVELElBQUksWUFBVSxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsU0FBUyxFQUFFLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDaEYsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixFQUFFLEtBQUssQ0FBQyxDQUFBO2dCQUNqRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO2FBQ3pCO1lBQ0QsV0FBVztZQUNYLElBQU0sR0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRSxFQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUE7WUFDN0YsSUFBSSxHQUFHLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDbEIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLGtCQUFrQixFQUFFLEtBQUssQ0FBQyxDQUFBO2dCQUNqRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO2FBQ3pCO1lBQ0QsSUFBSSxRQUFNLEdBQUcsS0FBSyxDQUFBO1lBQ2xCLElBQU0sUUFBTSxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFBO1lBQ3ZDLEdBQUcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUNULElBQU0sTUFBTSxHQUFHLENBQUMsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLEVBQUUsWUFBVSxDQUFDLENBQUMsVUFBVSxDQUFBO2dCQUM5RCxJQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxRQUFNLENBQUMsQ0FBQTtnQkFDakMsSUFBTSxLQUFLLEdBQUcsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFBO2dCQUN2QixJQUFJLEtBQUssRUFBRTtvQkFDUCxRQUFNLEdBQUcsSUFBSSxDQUFBO2lCQUNoQjtnQkFDRCxDQUFDLENBQUMsV0FBVyxDQUFDLGlCQUFTLENBQUMsR0FBRyxFQUFFLEVBQUUsVUFBVSxFQUFFLENBQUMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsS0FBSyxPQUFBLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUE7WUFDL0YsQ0FBQyxDQUFDLENBQUE7WUFDRixxQkFBcUI7WUFDckIsSUFBSSxRQUFNLEVBQUU7Z0JBQ1IsSUFBSSxDQUFDLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRSxDQUFBO2FBQ3RDO1lBQ0QsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLEdBQUcsS0FBSyxHQUFHLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUE7U0FDeEc7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxvQkFBb0I7SUFDWiw4Q0FBb0IsR0FBNUIsVUFBNkIsRUFBVTs7UUFDbkMsSUFBSSxRQUFRLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHlCQUF5QixDQUFDLG1DQUFJLENBQUMsQ0FBQTtRQUM3RSxJQUFJLFFBQVEsSUFBSSxHQUFHLEVBQUU7WUFDakIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHNCQUFzQixFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQ3JELE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHlCQUF5QixFQUFFLFFBQVEsR0FBRyxFQUFFLENBQUMsQ0FBQTtRQUNoRSxJQUFJLFFBQVEsS0FBSyxDQUFDLEVBQUU7WUFDaEIsaUJBQWlCO1lBQ2pCLElBQU0sS0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLEVBQUUsT0FBSyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUUsTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLEVBQUUsTUFBSSxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUcsQ0FBQyxDQUFBO1lBQ25ILElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQztnQkFDN0MsT0FBTyxDQUFDLENBQUMsT0FBTyxFQUFFLEtBQUssTUFBSSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxLQUFHLElBQUksQ0FBQyxDQUFDLE1BQU0sRUFBRSxJQUFJLHFCQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxFQUFFLE9BQUssQ0FBQyxJQUFJLE1BQUksSUFBSSxDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQTtZQUNoSixDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQztnQkFDVCxJQUFNLEVBQUUsR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQTtnQkFDckYsSUFBTSxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsR0FBRyxJQUFJLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUE7Z0JBQ3JGLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQTtZQUNsQixDQUFDLENBQUMsQ0FBQTtZQUNGLElBQU0sT0FBTyxHQUFHLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQ3hDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxFQUFFO2dCQUN6QixPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQTthQUM1QjtZQUNELElBQUksT0FBTyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7Z0JBQ3RCLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxzQkFBc0IsRUFBRSxLQUFLLENBQUMsQ0FBQTtnQkFDckQsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTthQUN6QjtZQUNELE9BQU8sQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUNiLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLEdBQUcsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUE7Z0JBQzFELEdBQUcsR0FBRyxDQUFDLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFBO2dCQUNuQixDQUFDLENBQUMsV0FBVyxDQUFDLGlCQUFTLENBQUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxLQUFBLEVBQUUsQ0FBQyxDQUFBO1lBQzFDLENBQUMsQ0FBQyxDQUFBO1lBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxFQUFFLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDckU7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxnQkFBZ0I7SUFDUixnREFBc0IsR0FBOUIsVUFBK0IsRUFBVTs7UUFDckMsSUFBSSxRQUFRLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLDJCQUEyQixDQUFDLG1DQUFJLENBQUMsQ0FBQTtRQUMvRSxJQUFJLFFBQVEsSUFBSSxHQUFHLEVBQUU7WUFDakIsSUFBSSxDQUFDLGlCQUFpQixDQUFDLHdCQUF3QixFQUFFLEtBQUssQ0FBQyxDQUFBO1lBQ3ZELE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLDJCQUEyQixFQUFFLFFBQVEsR0FBRyxFQUFFLENBQUMsQ0FBQTtRQUNsRSxJQUFJLFFBQVEsS0FBSyxDQUFDLEVBQUU7WUFDaEIsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxHQUFHLElBQUksQ0FBQyxDQUFBO1lBQzNELElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLGdCQUFRLENBQUMsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsRUFBRSxTQUFTLENBQUMsQ0FBQTtZQUN0RixJQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLDZCQUFrQixDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUE7U0FDaEY7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxPQUFPO0lBQ0Msb0NBQVUsR0FBbEIsVUFBbUIsRUFBVSxFQUFFLEtBQXdCO1FBQXZELGlCQThDQzs7UUE3Q0csSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLFVBQVUsRUFBRSxJQUFJLEtBQUssQ0FBQyxNQUFNLEVBQUU7WUFDMUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsZ0JBQVEsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQTtZQUNsRSxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBLENBQUMsYUFBYTtTQUN2QztRQUNELElBQUksUUFBUSxTQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsbUNBQUksQ0FBQyxDQUFBO1FBQzlELElBQUksUUFBUSxJQUFJLEdBQUcsRUFBRTtZQUNqQixJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ2xFLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUE7UUFDakQsSUFBSSxRQUFRLElBQUksR0FBRyxFQUFFO1lBQ2pCLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxDQUFDLEVBQUU7Z0JBQ2xDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUE7Z0JBQ3JDLElBQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ2hFLElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE9BQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxFQUFFLEtBQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFlBQVksRUFBRSxFQUFFLEtBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFBO2dCQUM3SCxPQUFPO2dCQUNQLElBQU0sQ0FBQyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQTtnQkFDdkMsV0FBVyxDQUFDLElBQUksQ0FBQyxtQkFBUyxDQUFDLGVBQWUsRUFBRSxFQUFFLEtBQUssT0FBQSxFQUFFLEdBQUcsT0FBQSxFQUFFLFVBQVUsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsU0FBUyxFQUFFLENBQUMsUUFBQyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sMENBQUUsY0FBYyxHQUFFLEVBQUUsQ0FBQyxDQUFBO2dCQUNySSxTQUFTO2dCQUNULElBQU0sUUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQyxLQUFLLEdBQUcsSUFBSSxDQUFDLENBQUE7Z0JBQ3RELElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksS0FBSyxNQUFJLElBQUksQ0FBQyxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUsS0FBSyxLQUFHLElBQUksQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLGdCQUFRLENBQUMsWUFBWSxDQUFDLEVBQTFGLENBQTBGLENBQUMsQ0FBQTtnQkFDekksR0FBRyxDQUFDLElBQUksQ0FBQyxVQUFDLENBQUMsRUFBRSxDQUFDO29CQUNWLElBQUksRUFBRSxHQUFHLEVBQUUsR0FBRyxxQkFBUyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsRUFBRSxPQUFLLENBQUMsQ0FBQTtvQkFDL0QsSUFBSSxFQUFFLEdBQUcsRUFBRSxHQUFHLHFCQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxFQUFFLE9BQUssQ0FBQyxDQUFBO29CQUMvRCxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFBO29CQUN2RCxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFBO29CQUN2RCxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQTtvQkFDM0MsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUE7b0JBQzNDLE9BQU8sRUFBRSxHQUFHLEVBQUUsQ0FBQTtnQkFDbEIsQ0FBQyxDQUFDLENBQUE7Z0JBQ0YsR0FBRyxHQUFHLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQTtnQkFDaEMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ3JCLEdBQUcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO29CQUNULENBQUMsQ0FBQyxZQUFZLENBQUMsZ0JBQVEsQ0FBQyxZQUFZLEVBQUUsS0FBRyxFQUFFLFFBQU0sQ0FBQyxDQUFBO29CQUNsRCxDQUFDLENBQUMsT0FBTyxDQUFDLGdCQUFRLENBQUMsZ0JBQWdCLEVBQUUsS0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFBO29CQUM1QyxLQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLDZCQUFrQixDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTtnQkFDdEUsQ0FBQyxDQUFDLENBQUE7YUFDTDtTQUNKO1FBQ0QsSUFBSSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtZQUM1QixJQUFJLENBQUMsb0JBQW9CLEdBQUcsSUFBSSxDQUFBO1lBQ2hDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLGlCQUFTLENBQUMsS0FBSyxFQUFFLEVBQUUsY0FBYyxFQUFFLENBQUMsRUFBRSxLQUFLLEVBQUUsRUFBRSxFQUFFLFNBQVMsRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFBO1NBQ2pHO1FBQ0QsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBRUQsT0FBTztJQUNDLHFDQUFXLEdBQW5CLFVBQW9CLEVBQVUsRUFBRSxLQUF3QjtRQUF4RCxpQkFpQ0M7O1FBaENHLElBQUksUUFBUSxTQUFXLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxVQUFVLENBQUMsbUNBQUksQ0FBQyxDQUFBO1FBQzlELElBQUksUUFBUSxJQUFJLElBQUksRUFBRTtZQUNsQixJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxnQkFBUSxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ25FLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7U0FDekI7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUE7UUFDakQsSUFBSSxRQUFRLElBQUksSUFBSSxFQUFFO1lBQ2xCLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsT0FBTyxDQUFDLEVBQUU7Z0JBQ2xDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUE7Z0JBQ3JDLElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE9BQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxFQUFFLEtBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFBO2dCQUN6RixJQUFJLEdBQUcsR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxVQUFBLENBQUMsSUFBSSxPQUFBLENBQUMsQ0FBQyxJQUFJLEtBQUssTUFBSSxJQUFJLENBQUMsQ0FBQyxNQUFNLEVBQUUsSUFBSSxDQUFDLENBQUMsTUFBTSxFQUFFLEtBQUssS0FBRyxJQUFJLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxnQkFBUSxDQUFDLFlBQVksRUFBRSxnQkFBUSxDQUFDLGNBQWMsQ0FBQyxFQUFwSCxDQUFvSCxDQUFDLENBQUE7Z0JBQ25LLEdBQUcsQ0FBQyxJQUFJLENBQUMsVUFBQyxDQUFDLEVBQUUsQ0FBQztvQkFDVixJQUFJLEVBQUUsR0FBRyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLEVBQUUsT0FBSyxDQUFDLENBQUE7b0JBQy9ELElBQUksRUFBRSxHQUFHLEVBQUUsR0FBRyxxQkFBUyxDQUFDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxRQUFRLEVBQUUsRUFBRSxPQUFLLENBQUMsQ0FBQTtvQkFDL0QsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUE7b0JBQzNDLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFBO29CQUMzQyxPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUE7Z0JBQ2xCLENBQUMsQ0FBQyxDQUFBO2dCQUNGLEdBQUcsR0FBRyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUE7Z0JBQ2hDLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFBO2dCQUNyQixHQUFHLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztvQkFDVCxDQUFDLENBQUMsWUFBWSxDQUFDLGdCQUFRLENBQUMsWUFBWSxFQUFFLEtBQUcsRUFBRSxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUE7b0JBQ3ZELEtBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsNkJBQWtCLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFBO2dCQUN2RSxDQUFDLENBQUMsQ0FBQTthQUNMO1NBQ0o7UUFDRCxJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzVCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUE7WUFDaEMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsaUJBQVMsQ0FBQyxLQUFLLEVBQUUsRUFBRSxjQUFjLEVBQUUsQ0FBQyxFQUFFLEtBQUssRUFBRSxFQUFFLEVBQUUsU0FBUyxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUE7U0FDbEc7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxlQUFlO0lBQ1AseUNBQWUsR0FBdkIsVUFBd0IsRUFBVSxFQUFFLEtBQXdCO1FBQTVELGlCQWdFQzs7UUEvREcsSUFBSSxRQUFRLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsQ0FBQyxtQ0FBSSxDQUFDLENBQUE7UUFDOUQsSUFBSSxRQUFRLEtBQUssQ0FBQyxFQUFFO1lBQ2hCLElBQU0sS0FBSyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUE7WUFDMUIsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsZ0JBQVEsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFBO1lBQzFELElBQUksQ0FBQyxJQUFJLEVBQUU7Z0JBQ1AsSUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsWUFBWSxDQUFDLGdCQUFRLENBQUMsaUJBQWlCLEVBQUUsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsRUFBRSxLQUFLLENBQUMsQ0FBQTthQUMzRjtZQUNELElBQUksSUFBSSxDQUFDLEtBQUssR0FBRyxDQUFDLEVBQUU7Z0JBQ2hCLElBQUksQ0FBQyxLQUFLLElBQUksQ0FBQyxDQUFBO2dCQUNmLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxtQkFBbUIsRUFBRSxJQUFJLENBQUMsQ0FBQTtnQkFDakQsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTthQUN6QjtZQUNELElBQUksQ0FBQyxLQUFLLEdBQUcsS0FBSyxDQUFBO1lBQ2xCLElBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxNQUFNLENBQUMsRUFBRSxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUE7U0FDL0Q7UUFDRCxPQUFPO1FBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUE7UUFDakQsSUFBSSxRQUFRLElBQUksR0FBRyxFQUFFO1lBQ2pCLElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLEVBQUUsS0FBRyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLEVBQUUsT0FBSyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUE7WUFDOUYsSUFBTSxVQUFRLEdBQUcsRUFBRSxFQUFFLFVBQVEsR0FBRyxFQUFFLENBQUE7WUFDbEMsSUFBSSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUM3QixJQUFJLENBQUMsQ0FBQyxDQUFDLE1BQU0sRUFBRSxFQUFFO29CQUNiLE9BQU07aUJBQ1Q7cUJBQU0sSUFBSSxDQUFDLENBQUMsT0FBTyxFQUFFLEtBQUssTUFBSSxFQUFFO29CQUM3QixVQUFRLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFBO2lCQUNuQjtxQkFBTSxJQUFJLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxnQkFBUSxDQUFDLFNBQVMsQ0FBQyxFQUFFO29CQUN6QyxVQUFRLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFBO2lCQUNuQjtZQUNMLENBQUMsQ0FBQyxDQUFBO1lBQ0YsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFBO1lBQ2hELGFBQWE7WUFDYixJQUFJLFVBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO2dCQUNyQixVQUFRLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUM7b0JBQ2YsSUFBSSxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQTtvQkFDL0MsSUFBSSxFQUFFLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQTtvQkFDL0MsRUFBRSxHQUFHLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxPQUFLLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQTtvQkFDeEUsRUFBRSxHQUFHLEVBQUUsR0FBRyxHQUFHLEdBQUcsQ0FBQyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxPQUFLLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQTtvQkFDeEUsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLEdBQUcsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUE7b0JBQzNDLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFBO29CQUMzQyxPQUFPLEVBQUUsR0FBRyxFQUFFLENBQUE7Z0JBQ2xCLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUMsT0FBTyxDQUFDLFVBQUEsQ0FBQztvQkFDdEIsSUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLEdBQUcsS0FBSyxDQUFDLEtBQUssR0FBRyxJQUFJLENBQUMsQ0FBQTtvQkFDekQsSUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQTtvQkFDdkIsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxpQkFBUyxDQUFDLElBQUksRUFBRSxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUE7b0JBQ2xELEtBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQTtnQkFDdEQsQ0FBQyxDQUFDLENBQUE7YUFDTDtZQUNELGFBQWE7WUFDYixJQUFJLFVBQVEsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFO2dCQUNyQixVQUFRLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUM7b0JBQ2YsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDLFlBQVksRUFBRSxHQUFHLEdBQUcsR0FBRyxDQUFDLEVBQUUsR0FBRyxxQkFBUyxDQUFDLGtCQUFrQixDQUFDLE9BQUssRUFBRSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFBO29CQUMxRixJQUFJLEVBQUUsR0FBRyxDQUFDLENBQUMsWUFBWSxFQUFFLEdBQUcsR0FBRyxHQUFHLENBQUMsRUFBRSxHQUFHLHFCQUFTLENBQUMsa0JBQWtCLENBQUMsT0FBSyxFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDLENBQUE7b0JBQzFGLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFBO29CQUMzQyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQTtvQkFDM0MsT0FBTyxFQUFFLEdBQUcsRUFBRSxDQUFBO2dCQUNsQixDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxVQUFBLENBQUM7b0JBQ3RCLENBQUMsQ0FBQyxPQUFPLENBQUMsZ0JBQVEsQ0FBQyxTQUFTLEVBQUUsS0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFBO29CQUNyQyxLQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUMsTUFBTSxDQUFDLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUE7Z0JBQ3RELENBQUMsQ0FBQyxDQUFBO2FBQ0w7WUFDRCxJQUFJLENBQUMsaUJBQWlCLENBQUMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLENBQUE7U0FDcEQ7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCxPQUFPO0lBQ0MsMENBQWdCLEdBQXhCO1FBQUEsaUJBd0JDO1FBdkJHLElBQUksQ0FBQyxNQUFNLENBQUMsVUFBVSxDQUFDLGdCQUFRLENBQUMsa0JBQWtCLENBQUMsQ0FBQTtRQUNuRCxJQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLG9CQUFvQixDQUFDLHVCQUFlLENBQUMsbUJBQW1CLENBQUMsQ0FBQTtRQUNwRixJQUFJLE1BQU0sRUFBRTtZQUNSLElBQU0sTUFBSSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE9BQUssR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLFFBQVEsRUFBRSxFQUFFLEtBQUcsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFBO1lBQ3pGLElBQU0sUUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLEVBQUUsR0FBRyxNQUFNLENBQUMsS0FBSyxHQUFHLElBQUksQ0FBQyxDQUFBO1lBQ3ZFLElBQUksR0FBRyxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUMsTUFBTSxDQUFDLFVBQUEsQ0FBQyxJQUFJLE9BQUEsQ0FBQyxDQUFDLElBQUksS0FBSyxNQUFJLElBQUksQ0FBQyxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQyxnQkFBUSxDQUFDLG1CQUFtQixDQUFDLEVBQTNFLENBQTJFLENBQUMsQ0FBQTtZQUMxSCxHQUFHLENBQUMsSUFBSSxDQUFDLFVBQUMsQ0FBQyxFQUFFLENBQUM7Z0JBQ1YsSUFBSSxFQUFFLEdBQUcsRUFBRSxHQUFHLHFCQUFTLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxDQUFDLFFBQVEsRUFBRSxFQUFFLE9BQUssQ0FBQyxDQUFBO2dCQUMvRCxJQUFJLEVBQUUsR0FBRyxFQUFFLEdBQUcscUJBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLEVBQUUsT0FBSyxDQUFDLENBQUE7Z0JBQy9ELEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLFVBQVUsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUE7Z0JBQ3ZELEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLFVBQVUsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUE7Z0JBQ3ZELEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxHQUFHLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFBO2dCQUMzQyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQTtnQkFDM0MsT0FBTyxFQUFFLEdBQUcsRUFBRSxDQUFBO1lBQ2xCLENBQUMsQ0FBQyxDQUFBO1lBQ0YsR0FBRyxHQUFHLEdBQUcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFBO1lBQ3JCLEdBQUcsQ0FBQyxPQUFPLENBQUMsVUFBQSxDQUFDO2dCQUNULENBQUMsQ0FBQyxZQUFZLENBQUMsZ0JBQVEsQ0FBQyxtQkFBbUIsRUFBRSxLQUFHLEVBQUUsUUFBTSxDQUFDLENBQUEsQ0FBQyxVQUFVO2dCQUNwRSxDQUFDLENBQUMsT0FBTyxDQUFDLGdCQUFRLENBQUMsU0FBUyxFQUFFLEtBQUcsRUFBRSxDQUFDLENBQUMsQ0FBQSxDQUFDLFVBQVU7Z0JBQ2hELEtBQUksQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsNkJBQWtCLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFBO1lBQ3ZFLENBQUMsQ0FBQyxDQUFBO1NBQ0w7UUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO0lBQzFCLENBQUM7SUFFRCw2QkFBNkI7SUFDckIseUNBQWUsR0FBdkIsVUFBd0IsRUFBVTs7UUFDOUIsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxXQUFXLEVBQUUsQ0FBQTtRQUMxQyxJQUFJLFFBQVEsR0FBRyxDQUFDLEVBQUU7WUFDZCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO1NBQ3pCO2FBQU0sSUFBSSxRQUFRLEtBQUssQ0FBQyxFQUFFO1lBQ3ZCLElBQUksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUEsQ0FBQyxXQUFXO1NBQ3ZDO1FBQ0QsSUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsa0JBQWtCLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQTtRQUM1RCxJQUFJLENBQUEsU0FBUyxhQUFULFNBQVMsdUJBQVQsU0FBUyxDQUFFLE1BQU0sSUFBRyxDQUFDLEVBQUU7WUFDdkIsSUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLEtBQUssQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUcsSUFBSSxDQUFDLENBQUE7WUFDdEQsSUFBSSxRQUFRLFNBQVcsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsQ0FBQyxtQ0FBSSxDQUFDLENBQUE7WUFDOUQsSUFBSSxRQUFRLElBQUksY0FBYyxFQUFFO2dCQUM1QixJQUFJLEdBQUcsR0FBRyxDQUFDLENBQUE7Z0JBQ1gsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLEtBQUssQ0FBQyxHQUFHLENBQUMsRUFBRTtvQkFDekMsR0FBRyxHQUFHLENBQUMsQ0FBQSxDQUFDLElBQUk7aUJBQ2Y7Z0JBQ0QsSUFBSSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsR0FBRyxDQUFDLENBQUE7Z0JBQ3pCLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLGlCQUFTLENBQUMsS0FBSyxDQUFDLENBQUE7Z0JBQ3hDLE9BQU8sb0JBQU8sQ0FBQyxPQUFPLENBQUE7YUFDekI7WUFDRCxPQUFPO1lBQ1AsSUFBSSxDQUFDLGlCQUFpQixDQUFDLFVBQVUsRUFBRSxRQUFRLEdBQUcsRUFBRSxDQUFDLENBQUE7WUFDakQsZ0JBQWdCO1lBQ2hCLElBQUksQ0FBQyxJQUFJLENBQUMsb0JBQW9CLEVBQUU7Z0JBQzVCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUE7Z0JBQ2hDLElBQUksQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLGlCQUFTLENBQUMsS0FBSyxFQUFFLEVBQUUsY0FBYyxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUE7YUFDekU7WUFDRCxPQUFPLG9CQUFPLENBQUMsT0FBTyxDQUFBO1NBQ3pCO1FBQ0QsT0FBTyxvQkFBTyxDQUFDLE9BQU8sQ0FBQTtJQUMxQixDQUFDO0lBQ0wsc0JBQUM7QUFBRCxDQXJZQSxBQXFZQyxDQXJZNEMsb0JBQVUsR0FxWXREIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQVhfQ0FWQUxSWV9JRCwgQkFUVExFX0VGRkVDVF9UWVBFLCBQQVdOX0NST1NTQk9XX0lEIH0gZnJvbSBcIi4uLy4uL2NvbW1vbi9jb25zdGFudC9Db25zdGFudFwiO1xuaW1wb3J0IHsgQnVmZlR5cGUsIEVxdWlwRWZmZWN0VHlwZSwgSGVyb1R5cGUsIFBhd25TdGF0ZSB9IGZyb20gXCIuLi8uLi9jb21tb24vY29uc3RhbnQvRW51bXNcIjtcbmltcG9ydCBFdmVudFR5cGUgZnJvbSBcIi4uLy4uL2NvbW1vbi9ldmVudC9FdmVudFR5cGVcIjtcbmltcG9ydCB7IG1hcEhlbHBlciB9IGZyb20gXCIuLi8uLi9jb21tb24vaGVscGVyL01hcEhlbHBlclwiO1xuaW1wb3J0IFBvcnRyYXlhbFNraWxsT2JqIGZyb20gXCIuLi9jb21tb24vUG9ydHJheWFsU2tpbGxPYmpcIjtcbmltcG9ydCBFcXVpcEVmZmVjdE9iaiBmcm9tIFwiLi4vbWFpbi9FcXVpcEVmZmVjdE9ialwiO1xuaW1wb3J0IEJhc2VBY3Rpb24gZnJvbSBcIi4vQmFzZUFjdGlvblwiO1xuaW1wb3J0IHsgQlRTdGF0ZSB9IGZyb20gXCIuL0JUQ29uc3RhbnRcIjtcblxuLy8g5qOA5rWL5Zue5ZCI5byA5aeL5pe2XG5leHBvcnQgZGVmYXVsdCBjbGFzcyBDaGVja1JvdW5kQmVnaW4gZXh0ZW5kcyBCYXNlQWN0aW9uIHtcblxuICAgIHByaXZhdGUgaXNDaGFuZ2VGaWdodGVyU3RhdGU6IGJvb2xlYW4gPSBmYWxzZSAvL+aYr+WQpuaUueWPmOWjq+WFteeahOeKtuaAgeS6hlxuICAgIHByaXZhdGUgZmxhbWluZ0FybW9yRWZmZWN0OiBFcXVpcEVmZmVjdE9iaiA9IG51bGwgLy/ng4jnhLDpk6DnlLLmlYjmnpxcblxuICAgIHB1YmxpYyBvbkluaXQoY29uZjogYW55KSB7XG4gICAgfVxuXG4gICAgcHVibGljIG9uT3BlbigpIHtcbiAgICAgICAgdGhpcy5pc0NoYW5nZUZpZ2h0ZXJTdGF0ZSA9IGZhbHNlXG4gICAgICAgIC8vIOeDiOeEsOmToOeUslxuICAgICAgICB0aGlzLmZsYW1pbmdBcm1vckVmZmVjdCA9IHRoaXMudGFyZ2V0LmdldEVxdWlwRWZmZWN0QnlUeXBlKEVxdWlwRWZmZWN0VHlwZS5GTEFNSU5HX0FSTU9SKVxuICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdpc0hhc0FybW9yRWZmZWN0JywgISF0aGlzLmZsYW1pbmdBcm1vckVmZmVjdClcbiAgICAgICAgLy8g5a6a5b+D55uUIOavjzXlm57lkIjmgaLlpI1cbiAgICAgICAgY29uc3QgQ0hFQ0tfQ0VOVEVSSU5HX0hFTE1FVCA9IHRoaXMudGFyZ2V0LmdldEJ1ZmYoQnVmZlR5cGUuQ0hFQ0tfQ0VOVEVSSU5HX0hFTE1FVClcbiAgICAgICAgaWYgKENIRUNLX0NFTlRFUklOR19IRUxNRVQpIHtcbiAgICAgICAgICAgIENIRUNLX0NFTlRFUklOR19IRUxNRVQudmFsdWUgLT0gMVxuICAgICAgICAgICAgaWYgKENIRUNLX0NFTlRFUklOR19IRUxNRVQudmFsdWUgPD0gMCkge1xuICAgICAgICAgICAgICAgIENIRUNLX0NFTlRFUklOR19IRUxNRVQudmFsdWUgPSA1XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnaXNIYXNDZW50ZXJpbmdIZWxtZXQnLCB0cnVlKVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIOi1pOmHkeebviDmr48z5Zue5ZCI6I635b6X5LiA5Liq5oqk55u+XG4gICAgICAgIGNvbnN0IENIRUNLX0NSSU1TT05HT0xEX1NISUVMRCA9IHRoaXMudGFyZ2V0LmdldEJ1ZmYoQnVmZlR5cGUuQ0hFQ0tfQ1JJTVNPTkdPTERfU0hJRUxEKVxuICAgICAgICBpZiAoQ0hFQ0tfQ1JJTVNPTkdPTERfU0hJRUxEKSB7XG4gICAgICAgICAgICBDSEVDS19DUklNU09OR09MRF9TSElFTEQudmFsdWUgLT0gMVxuICAgICAgICAgICAgaWYgKENIRUNLX0NSSU1TT05HT0xEX1NISUVMRC52YWx1ZSA8PSAwKSB7XG4gICAgICAgICAgICAgICAgQ0hFQ0tfQ1JJTVNPTkdPTERfU0hJRUxELnZhbHVlID0gM1xuICAgICAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQ3JpbXNvbmdvbGRTaGllbGQnLCB0cnVlKVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHVibGljIG9uTGVhdmUoc3RhdGU6IEJUU3RhdGUpIHtcbiAgICAgICAgLy8g5aaC5p6c5aSx6LSl5pe2IOimgeWkhOeQhui/nuWHu+mXrumimFxuICAgICAgICBpZiAoc3RhdGUgPT09IEJUU3RhdGUuRkFJTFVSRSkge1xuICAgICAgICAgICAgdGhpcy5zZXRUcmVlQmxhY2tib2FyZERhdGEoJ2JhdHRlckNvdW50JywgLTEpXG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYXR0YWNrVGFyZ2V0ID0gdGhpcy50YXJnZXQuZ2V0QXR0YWNrVGFyZ2V0KClcbiAgICAgICAgaWYgKGF0dGFja1RhcmdldCAmJiBhdHRhY2tUYXJnZXQuaXNEaWUoKSkge1xuICAgICAgICAgICAgdGhpcy50YXJnZXQuY2hhbmdlQXR0YWNrVGFyZ2V0KG51bGwpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBwdWJsaWMgb25UaWNrKGR0OiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgaGVyb1NraWxsID0gdGhpcy50YXJnZXQuZ2V0UG9ydHJheWFsU2tpbGwoKVxuICAgICAgICBpZiAodGhpcy50YXJnZXQud2FpdFJvdW5kID4gMCkge1xuICAgICAgICAgICAgdGhpcy50YXJnZXQud2FpdFJvdW5kIC09IDFcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLkZBSUxVUkUgLy/mmK/lkKbpnIDopoHnrYnlvoXkuIDlm57lkIhcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc0hhc0FybW9yRWZmZWN0JykpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNoZWNrRmxhbWluZ0FybW9yKGR0KSAvL+eEsemYs+mToFxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQ2VudGVyaW5nSGVsbWV0JykpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNoZWNrQ2VudGVyaW5nSGVsbWV0KGR0KSAvL+WumuW/g+eblFxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQ3JpbXNvbmdvbGRTaGllbGQnKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2hlY2tDcmltc29uZ29sZFNoaWVsZChkdCkgLy/otaTph5Hnm75cbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhcmdldC5pc0hhc0J1ZmZzKFxuICAgICAgICAgICAgQnVmZlR5cGUuRElaWklORVNTLFxuICAgICAgICAgICAgQnVmZlR5cGUuU1RBTkRfU0hJRUxELFxuICAgICAgICAgICAgQnVmZlR5cGUuUEFSQUxZU0lTLFxuICAgICAgICAgICAgQnVmZlR5cGUuUEFSQUxZU0lTX1VQLFxuICAgICAgICAgICAgQnVmZlR5cGUuRkVBUixcbiAgICAgICAgICAgIEJ1ZmZUeXBlLkFOVElDSVBBVElPTl9ERUZFTlNFXG4gICAgICAgICkpIHtcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLkZBSUxVUkUgLy/mmK/lkKbmnInnnKnmmZVidWZmIOaIluiAhSDnq4vnm77nirbmgIFcbiAgICAgICAgfSBlbHNlIGlmIChoZXJvU2tpbGw/LmlkID09PSBIZXJvVHlwZS5IVUFOR19HQUkgJiYgIXRoaXMudGFyZ2V0LmlzSGFzQnVmZihCdWZmVHlwZS5DSEVDS19LVVJPVSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNoZWNrS3VSb3UoZHQsIGhlcm9Ta2lsbCkgLy/pu4Tnm5Yg5qOA5rWL5piv5ZCm6YeK5pS+6Ium6IKJXG4gICAgICAgIH0gZWxzZSBpZiAoaGVyb1NraWxsPy5pZCA9PT0gSGVyb1R5cGUuREVOR19BSSAmJiAhdGhpcy50YXJnZXQuaXNIYXNCdWZmKEJ1ZmZUeXBlLkNIRUNLX1RPTkRFTikpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmNoZWNrVG9uZGVuKGR0LCBoZXJvU2tpbGwpIC8v6YKT6Im+IOajgOa1i+aYr+WQpumHiuaUvuWxr+WepuS7pFxuICAgICAgICB9IGVsc2UgaWYgKGhlcm9Ta2lsbD8uaWQgPT09IEhlcm9UeXBlLlNVTl9TSEFOR1hJQU5HICYmICF0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc0NoZWNrTGl0dGxlR2lybCcpKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5jaGVja0xpdHRsZUdpcmwoZHQsIGhlcm9Ta2lsbCkgLy/lrZnlsJrpppkg5q+PNeWbnuWQiOmHiuaUvuaKgOiDvVxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFyZ2V0LmNoZWNrVHJpZ2dlckJ1ZmYoQnVmZlR5cGUuQ0hFQ0tfSkFERV9QRU5EQU5UKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2hlY2tKYWRlUGVuZGFudCgpIC8v5qOA5rWL546J5L2pXG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXJnZXQuZ2V0SWQoKSA9PT0gUEFXTl9DUk9TU0JPV19JRCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2hlY2tQdWxsU3RyaW5nKGR0KSAvL+ajgOa1i+aLieW8plxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFyZ2V0LmdldElkKCkgPT09IEFYX0NBVkFMUllfSUQpIHtcbiAgICAgICAgICAgIHRoaXMudGFyZ2V0LnNldEFuZ2VyKDEpIC8v57uZ5pan6aqR5YW15YqgMeaAklxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBCVFN0YXRlLlNVQ0NFU1NcbiAgICB9XG5cbiAgICAvLyDmo4DmtYvmmK/lkKbmnInnhLHpmLPpk6Ag5a+5MeagvOWGhemAoOaIkOS8pOWus1xuICAgIHByaXZhdGUgY2hlY2tGbGFtaW5nQXJtb3IoZHQ6IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuZmxhbWluZ0FybW9yRWZmZWN0KSB7XG4gICAgICAgICAgICB0aGlzLmZsYW1pbmdBcm1vckVmZmVjdCA9IHRoaXMudGFyZ2V0LmdldEVxdWlwRWZmZWN0QnlUeXBlKEVxdWlwRWZmZWN0VHlwZS5GTEFNSU5HX0FSTU9SKVxuICAgICAgICB9XG4gICAgICAgIGxldCBjdXJyVGltZTogbnVtYmVyID0gdGhpcy5nZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWVGbGFtaW5nQXJtb3InKSA/PyAwXG4gICAgICAgIGlmIChjdXJyVGltZSA+PSA2MDApIHtcbiAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQXJtb3JFZmZlY3QnLCBmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgfVxuICAgICAgICAvLyDlop7liqDml7bpl7RcbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWVGbGFtaW5nQXJtb3InLCBjdXJyVGltZSArIGR0KVxuICAgICAgICBpZiAoY3VyclRpbWUgPT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHRydWVEYW1hZ2UgPSBNYXRoLnJvdW5kKHRoaXMudGFyZ2V0LmdldE1heEhwKCkgKiAwLjA0KVxuICAgICAgICAgICAgaWYgKHRydWVEYW1hZ2UgPD0gMCB8fCAhdGhpcy5jdHJsLmdldFJhbmRvbSgpLmNoYW5jZSh0aGlzLmZsYW1pbmdBcm1vckVmZmVjdC5vZGRzKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQXJtb3JFZmZlY3QnLCBmYWxzZSlcbiAgICAgICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyDlr7kx5qC85YaF6YCg5oiQ5Lyk5a6zXG4gICAgICAgICAgICBjb25zdCBhcnIgPSB0aGlzLnRhcmdldC5nZXRDYW5BdHRhY2tQYXduQnlSYW5nZSh0aGlzLnRhcmdldC5nZXRDYW5BdHRhY2tGaWdodGVycygpLCAxLCA0LCAnJylcbiAgICAgICAgICAgIGlmIChhcnIubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnaXNIYXNBcm1vckVmZmVjdCcsIGZhbHNlKVxuICAgICAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGxldCBoYXNEaWUgPSBmYWxzZVxuICAgICAgICAgICAgY29uc3Qgb3duZXJzID0gW3RoaXMudGFyZ2V0LmdldE93bmVyKCldXG4gICAgICAgICAgICBhcnIuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBkYW1hZ2UgPSBtLmhpdFByZXBEYW1hZ2VIYW5kbGUoMCwgdHJ1ZURhbWFnZSkudHJ1ZURhbWFnZVxuICAgICAgICAgICAgICAgIGNvbnN0IHYgPSBtLm9uSGl0KGRhbWFnZSwgb3duZXJzKVxuICAgICAgICAgICAgICAgIGNvbnN0IGlzRGllID0gbS5pc0RpZSgpXG4gICAgICAgICAgICAgICAgaWYgKGlzRGllKSB7XG4gICAgICAgICAgICAgICAgICAgIGhhc0RpZSA9IHRydWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbS5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuSElULCB7IHRydWVEYW1hZ2U6IHYuZGFtYWdlLCBoZWFsOiB2LmhlYWwsIGlzRGllLCB0aW1lOiBjdXJyVGltZSB9KVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC8vIOWmguaenOacieebruagh+atu+S6hiDpnIDopoHph43mlrDojrflvpfmlLvlh7vnm67moIdcbiAgICAgICAgICAgIGlmIChoYXNEaWUpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnRhcmdldC5jbGVhbkNhbkF0dGFja1RhcmdldHMoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoW3RoaXMuZmxhbWluZ0FybW9yRWZmZWN0LnR5cGUgKiAxMDAwMCArIDFdLCB0aGlzLnRhcmdldC5nZXRQb2ludCgpLCAndG9wJylcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgfVxuXG4gICAgLy8g5qOA5rWL5piv5ZCm5pyJ5a6a5b+D55uUIOWvuTHmoLzlhoXlj4vmlrnlm57ooYBcbiAgICBwcml2YXRlIGNoZWNrQ2VudGVyaW5nSGVsbWV0KGR0OiBudW1iZXIpIHtcbiAgICAgICAgbGV0IGN1cnJUaW1lOiBudW1iZXIgPSB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdjdXJyVGltZUNlbnRlcmluZ0hlbG1ldCcpID8/IDBcbiAgICAgICAgaWYgKGN1cnJUaW1lID49IDYwMCkge1xuICAgICAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnaXNIYXNDZW50ZXJpbmdIZWxtZXQnLCBmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgfVxuICAgICAgICAvLyDlop7liqDml7bpl7RcbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWVDZW50ZXJpbmdIZWxtZXQnLCBjdXJyVGltZSArIGR0KVxuICAgICAgICBpZiAoY3VyclRpbWUgPT09IDApIHtcbiAgICAgICAgICAgIC8vIOWvuTHmoLzlhoXoh7PlpJo05Liq5Y+L5pa55oGi5aSN55Sf5ZG9XG4gICAgICAgICAgICBjb25zdCB1aWQgPSB0aGlzLnRhcmdldC5nZXRVaWQoKSwgcG9pbnQgPSB0aGlzLnRhcmdldC5nZXRQb2ludCgpLCBjYW1wID0gdGhpcy50YXJnZXQuZ2V0Q2FtcCgpLCByYW5nID0gMSwgY291bnQgPSA0XG4gICAgICAgICAgICBjb25zdCBmaWdodGVycyA9IHRoaXMuY3RybC5nZXRGaWdodGVycygpLmZpbHRlcihtID0+IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbS5nZXRDYW1wKCkgPT09IGNhbXAgJiYgbS5nZXRVaWQoKSAhPSB1aWQgJiYgbS5pc1Bhd24oKSAmJiBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKG0uZ2V0UG9pbnQoKSwgcG9pbnQpID09IHJhbmcgJiYgIW0uaXNGdWxsSHAoKVxuICAgICAgICAgICAgfSkuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGF3ID0gTWF0aC5yb3VuZCgoMSAtIGEuZ2V0SHBSYXRpbygpKSAqIDEwMCkgKiAxMDAwICsgKDk5OSAtIGEuZ2V0QXR0YWNrSW5kZXgoKSlcbiAgICAgICAgICAgICAgICBjb25zdCBidyA9IE1hdGgucm91bmQoKDEgLSBiLmdldEhwUmF0aW8oKSkgKiAxMDApICogMTAwMCArICg5OTkgLSBiLmdldEF0dGFja0luZGV4KCkpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGJ3IC0gYXdcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICBjb25zdCBmcmllbmRzID0gZmlnaHRlcnMuc2xpY2UoMCwgY291bnQpXG4gICAgICAgICAgICBpZiAoIXRoaXMudGFyZ2V0LmlzRnVsbEhwKCkpIHtcbiAgICAgICAgICAgICAgICBmcmllbmRzLnB1c2godGhpcy50YXJnZXQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZnJpZW5kcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdpc0hhc0NlbnRlcmluZ0hlbG1ldCcsIGZhbHNlKVxuICAgICAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZyaWVuZHMuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICBsZXQgdmFsID0gTWF0aC5yb3VuZCgobS5nZXRNYXhIcCgpIC0gbS5nZXRDdXJIcCgpKSAqIDAuMjUpXG4gICAgICAgICAgICAgICAgdmFsID0gbS5vbkhlYWwodmFsKVxuICAgICAgICAgICAgICAgIG0uY2hhbmdlU3RhdGUoUGF3blN0YXRlLkhFQUwsIHsgdmFsIH0pXG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoWzM5MDAwMV0sIHRoaXMudGFyZ2V0LmdldFBvaW50KCksICdkaScpXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgIH1cblxuICAgIC8vIOajgOa1i+aYr+WQpuaciei1pOmHkeebviDojrflvpfmiqTnm75cbiAgICBwcml2YXRlIGNoZWNrQ3JpbXNvbmdvbGRTaGllbGQoZHQ6IG51bWJlcikge1xuICAgICAgICBsZXQgY3VyclRpbWU6IG51bWJlciA9IHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lQ3JpbXNvbmdvbGRTaGllbGQnKSA/PyAwXG4gICAgICAgIGlmIChjdXJyVGltZSA+PSA2MDApIHtcbiAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzSGFzQ3JpbXNvbmdvbGRTaGllbGQnLCBmYWxzZSlcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICAgICAgfVxuICAgICAgICAvLyDlop7liqDml7bpl7RcbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWVDcmltc29uZ29sZFNoaWVsZCcsIGN1cnJUaW1lICsgZHQpXG4gICAgICAgIGlmIChjdXJyVGltZSA9PT0gMCkge1xuICAgICAgICAgICAgY29uc3Qgc2hpZWxkVmFsID0gTWF0aC5yb3VuZCh0aGlzLnRhcmdldC5nZXRNYXhIcCgpICogMC4wNClcbiAgICAgICAgICAgIHRoaXMudGFyZ2V0LmFkZEJ1ZmZWYWx1ZShCdWZmVHlwZS5DUklNU09OR09MRF9TSElFTEQsIHRoaXMudGFyZ2V0LmdldFVpZCgpLCBzaGllbGRWYWwpXG4gICAgICAgICAgICB0aGlzLmN0cmwucGxheUJhdHRsZUVmZmVjdChCQVRUTEVfRUZGRUNUX1RZUEUuU0hJRUxELCB0aGlzLnRhcmdldC5nZXRQb2ludCgpKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICB9XG5cbiAgICAvLyDmo4DmtYvoi6bogolcbiAgICBwcml2YXRlIGNoZWNrS3VSb3UoZHQ6IG51bWJlciwgc2tpbGw6IFBvcnRyYXlhbFNraWxsT2JqKSB7XG4gICAgICAgIGlmICh0aGlzLnRhcmdldC5nZXRIcFJhdGlvKCkgPD0gc2tpbGwucGFyYW1zKSB7XG4gICAgICAgICAgICB0aGlzLnRhcmdldC5hZGRCdWZmKEJ1ZmZUeXBlLkNIRUNLX0tVUk9VLCB0aGlzLnRhcmdldC5nZXRVaWQoKSwgMSlcbiAgICAgICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkcgLy/ooYDph4/kvY7kuo4yMCUg5LiN6Ium6IKJXG4gICAgICAgIH1cbiAgICAgICAgbGV0IGN1cnJUaW1lOiBudW1iZXIgPSB0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdjdXJyVGltZScpID8/IDBcbiAgICAgICAgaWYgKGN1cnJUaW1lID49IDg1MCkge1xuICAgICAgICAgICAgdGhpcy50YXJnZXQuYWRkQnVmZihCdWZmVHlwZS5DSEVDS19LVVJPVSwgdGhpcy50YXJnZXQuZ2V0VWlkKCksIDEpXG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgICAgIH1cbiAgICAgICAgLy8g5aKe5Yqg5pe26Ze0XG4gICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lJywgY3VyclRpbWUgKyBkdClcbiAgICAgICAgaWYgKGN1cnJUaW1lID49IDYwMCkge1xuICAgICAgICAgICAgaWYgKCF0aGlzLmdldEJsYWNrYm9hcmREYXRhKCdpc0hpdCcpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnaXNIaXQnLCB0cnVlKVxuICAgICAgICAgICAgICAgIGNvbnN0IGRhbWFnZSA9IE1hdGgucm91bmQodGhpcy50YXJnZXQuZ2V0TWF4SHAoKSAqIHNraWxsLnBhcmFtcylcbiAgICAgICAgICAgICAgICBjb25zdCBjYW1wID0gdGhpcy50YXJnZXQuY2FtcCwgcG9pbnQgPSB0aGlzLnRhcmdldC5nZXRQb2ludCgpLCBpbmRleCA9IHRoaXMudGFyZ2V0LmdldEFyZWFJbmRleCgpLCB1aWQgPSB0aGlzLnRhcmdldC5nZXRVaWQoKVxuICAgICAgICAgICAgICAgIC8vIOaJo+mZpOihgOmHj1xuICAgICAgICAgICAgICAgIGNvbnN0IHYgPSB0aGlzLnRhcmdldC5vbkhpdChkYW1hZ2UsIFtdKVxuICAgICAgICAgICAgICAgIGV2ZW50Q2VudGVyLmVtaXQoRXZlbnRUeXBlLlBMQVlfRkxVVFRFUl9IUCwgeyBpbmRleCwgdWlkLCB0cnVlRGFtYWdlOiAtdi5kYW1hZ2UsIGhhc1NoaWVsZDogISF0aGlzLnRhcmdldC5lbnRpdHk/LmdldFNoaWVsZFZhbHVlKCkgfSlcbiAgICAgICAgICAgICAgICAvLyDnu5npmJ/lj4vliqDmiqTnm75cbiAgICAgICAgICAgICAgICBjb25zdCBzaGllbGQgPSBNYXRoLnJvdW5kKGRhbWFnZSAqIHNraWxsLnZhbHVlICogMC4wMSlcbiAgICAgICAgICAgICAgICBsZXQgYXJyID0gdGhpcy5jdHJsLmdldEZpZ2h0ZXJzKCkuZmlsdGVyKG0gPT4gbS5jYW1wID09PSBjYW1wICYmIG0uaXNQYXduKCkgJiYgbS5nZXRVaWQoKSAhPT0gdWlkICYmICFtLmlzSGFzQnVmZihCdWZmVHlwZS5LVVJPVV9TSElFTEQpKVxuICAgICAgICAgICAgICAgIGFyci5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBhdyA9IDk5IC0gbWFwSGVscGVyLmdldFBvaW50VG9Qb2ludERpcyhhLmdldFBvaW50KCksIHBvaW50KVxuICAgICAgICAgICAgICAgICAgICBsZXQgYncgPSA5OSAtIG1hcEhlbHBlci5nZXRQb2ludFRvUG9pbnREaXMoYi5nZXRQb2ludCgpLCBwb2ludClcbiAgICAgICAgICAgICAgICAgICAgYXcgPSBhdyAqIDEwMDAgKyBNYXRoLmZsb29yKCgxIC0gYS5nZXRIcFJhdGlvKCkpICogMTAwKVxuICAgICAgICAgICAgICAgICAgICBidyA9IGJ3ICogMTAwMCArIE1hdGguZmxvb3IoKDEgLSBiLmdldEhwUmF0aW8oKSkgKiAxMDApXG4gICAgICAgICAgICAgICAgICAgIGF3ID0gYXcgKiAxMDAwICsgKDk5OSAtIGEuZ2V0QXR0YWNrSW5kZXgoKSlcbiAgICAgICAgICAgICAgICAgICAgYncgPSBidyAqIDEwMDAgKyAoOTk5IC0gYi5nZXRBdHRhY2tJbmRleCgpKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYncgLSBhd1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgYXJyID0gYXJyLnNsaWNlKDAsIHNraWxsLnRhcmdldClcbiAgICAgICAgICAgICAgICBhcnIucHVzaCh0aGlzLnRhcmdldClcbiAgICAgICAgICAgICAgICBhcnIuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICAgICAgbS5hZGRCdWZmVmFsdWUoQnVmZlR5cGUuS1VST1VfU0hJRUxELCB1aWQsIHNoaWVsZClcbiAgICAgICAgICAgICAgICAgICAgbS5hZGRCdWZmKEJ1ZmZUeXBlLktVUk9VX0FERF9BVFRBQ0ssIHVpZCwgMSlcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5jdHJsLnBsYXlCYXR0bGVFZmZlY3QoQkFUVExFX0VGRkVDVF9UWVBFLktVUk9VLCBtLmdldFBvaW50KCkpXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGUgPSB0cnVlXG4gICAgICAgICAgICB0aGlzLnRhcmdldC5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuU0tJTEwsIHsgY3VyckF0dGFja1RpbWU6IDAsIHNvdW5kOiAnJywgc2tpbGxOYW1lOiAna3Vyb3UnIH0pXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgIH1cblxuICAgIC8vIOajgOa1i+Wxr+WeplxuICAgIHByaXZhdGUgY2hlY2tUb25kZW4oZHQ6IG51bWJlciwgc2tpbGw6IFBvcnRyYXlhbFNraWxsT2JqKSB7XG4gICAgICAgIGxldCBjdXJyVGltZTogbnVtYmVyID0gdGhpcy5nZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWUnKSA/PyAwXG4gICAgICAgIGlmIChjdXJyVGltZSA+PSAxNTAwKSB7XG4gICAgICAgICAgICB0aGlzLnRhcmdldC5hZGRCdWZmKEJ1ZmZUeXBlLkNIRUNLX1RPTkRFTiwgdGhpcy50YXJnZXQuZ2V0VWlkKCksIDEpXG4gICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgICAgIH1cbiAgICAgICAgLy8g5aKe5Yqg5pe26Ze0XG4gICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lJywgY3VyclRpbWUgKyBkdClcbiAgICAgICAgaWYgKGN1cnJUaW1lID49IDEwMDApIHtcbiAgICAgICAgICAgIGlmICghdGhpcy5nZXRCbGFja2JvYXJkRGF0YSgnaXNIaXQnKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzSGl0JywgdHJ1ZSlcbiAgICAgICAgICAgICAgICBjb25zdCBjYW1wID0gdGhpcy50YXJnZXQuY2FtcCwgcG9pbnQgPSB0aGlzLnRhcmdldC5nZXRQb2ludCgpLCB1aWQgPSB0aGlzLnRhcmdldC5nZXRVaWQoKVxuICAgICAgICAgICAgICAgIGxldCBhcnIgPSB0aGlzLmN0cmwuZ2V0RmlnaHRlcnMoKS5maWx0ZXIobSA9PiBtLmNhbXAgPT09IGNhbXAgJiYgbS5pc1Bhd24oKSAmJiBtLmdldFVpZCgpICE9PSB1aWQgJiYgIW0uaXNIYXNCdWZmcyhCdWZmVHlwZS5UT05ERU5fQkVHSU4sIEJ1ZmZUeXBlLlRPTkRFTl9SRUNPVkVSKSlcbiAgICAgICAgICAgICAgICBhcnIuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgICAgICAgICBsZXQgYXcgPSA5OSAtIG1hcEhlbHBlci5nZXRQb2ludFRvUG9pbnREaXMoYS5nZXRQb2ludCgpLCBwb2ludClcbiAgICAgICAgICAgICAgICAgICAgbGV0IGJ3ID0gOTkgLSBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKGIuZ2V0UG9pbnQoKSwgcG9pbnQpXG4gICAgICAgICAgICAgICAgICAgIGF3ID0gYXcgKiAxMDAwICsgKDk5OSAtIGEuZ2V0QXR0YWNrSW5kZXgoKSlcbiAgICAgICAgICAgICAgICAgICAgYncgPSBidyAqIDEwMDAgKyAoOTk5IC0gYi5nZXRBdHRhY2tJbmRleCgpKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYncgLSBhd1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgYXJyID0gYXJyLnNsaWNlKDAsIHNraWxsLnRhcmdldClcbiAgICAgICAgICAgICAgICBhcnIucHVzaCh0aGlzLnRhcmdldClcbiAgICAgICAgICAgICAgICBhcnIuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICAgICAgbS5hZGRCdWZmVmFsdWUoQnVmZlR5cGUuVE9OREVOX0JFR0lOLCB1aWQsIHNraWxsLnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmN0cmwucGxheUJhdHRsZUVmZmVjdChCQVRUTEVfRUZGRUNUX1RZUEUuVE9OREVOLCBtLmdldFBvaW50KCkpXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGUpIHtcbiAgICAgICAgICAgIHRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGUgPSB0cnVlXG4gICAgICAgICAgICB0aGlzLnRhcmdldC5jaGFuZ2VTdGF0ZShQYXduU3RhdGUuU0tJTEwsIHsgY3VyckF0dGFja1RpbWU6IDAsIHNvdW5kOiAnJywgc2tpbGxOYW1lOiAndG9uZGVuJyB9KVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBCVFN0YXRlLlJVTk5JTkdcbiAgICB9XG5cbiAgICAvLyDlrZnlsJrpppkg5q+PNeWbnuWQiOmHiuaUvuaKgOiDvVxuICAgIHByaXZhdGUgY2hlY2tMaXR0bGVHaXJsKGR0OiBudW1iZXIsIHNraWxsOiBQb3J0cmF5YWxTa2lsbE9iaikge1xuICAgICAgICBsZXQgY3VyclRpbWU6IG51bWJlciA9IHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lJykgPz8gMFxuICAgICAgICBpZiAoY3VyclRpbWUgPT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHJvdW5kID0gc2tpbGwucGFyYW1zXG4gICAgICAgICAgICBsZXQgYnVmZiA9IHRoaXMudGFyZ2V0LmdldEJ1ZmYoQnVmZlR5cGUuQ0hFQ0tfTElUVExFX0dJUkwpXG4gICAgICAgICAgICBpZiAoIWJ1ZmYpIHtcbiAgICAgICAgICAgICAgICBidWZmID0gdGhpcy50YXJnZXQuYWRkQnVmZlZhbHVlKEJ1ZmZUeXBlLkNIRUNLX0xJVFRMRV9HSVJMLCB0aGlzLnRhcmdldC5nZXRVaWQoKSwgcm91bmQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoYnVmZi52YWx1ZSA+IDEpIHtcbiAgICAgICAgICAgICAgICBidWZmLnZhbHVlIC09IDFcbiAgICAgICAgICAgICAgICB0aGlzLnNldEJsYWNrYm9hcmREYXRhKCdpc0NoZWNrTGl0dGxlR2lybCcsIHRydWUpXG4gICAgICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnVmZi52YWx1ZSA9IHJvdW5kXG4gICAgICAgICAgICB0aGlzLmN0cmwucGxheUJhdHRsZUVmZmVjdChbMTExMDAxXSwgdGhpcy50YXJnZXQuZ2V0UG9pbnQoKSlcbiAgICAgICAgfVxuICAgICAgICAvLyDlop7liqDml7bpl7RcbiAgICAgICAgdGhpcy5zZXRCbGFja2JvYXJkRGF0YSgnY3VyclRpbWUnLCBjdXJyVGltZSArIGR0KVxuICAgICAgICBpZiAoY3VyclRpbWUgPj0gMzAwKSB7XG4gICAgICAgICAgICBjb25zdCBjYW1wID0gdGhpcy50YXJnZXQuZ2V0Q2FtcCgpLCB1aWQgPSB0aGlzLnRhcmdldC5nZXRVaWQoKSwgcG9pbnQgPSB0aGlzLnRhcmdldC5nZXRQb2ludCgpXG4gICAgICAgICAgICBjb25zdCBmVGFyZ2V0cyA9IFtdLCBlVGFyZ2V0cyA9IFtdXG4gICAgICAgICAgICB0aGlzLmN0cmwuZ2V0RmlnaHRlcnMoKS5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgICAgIGlmICghbS5pc1Bhd24oKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG0uZ2V0Q2FtcCgpID09PSBjYW1wKSB7XG4gICAgICAgICAgICAgICAgICAgIGZUYXJnZXRzLnB1c2gobSlcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCFtLmlzSGFzQnVmZihCdWZmVHlwZS5ESVpaSU5FU1MpKSB7XG4gICAgICAgICAgICAgICAgICAgIGVUYXJnZXRzLnB1c2gobSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgY29uc3QgY250ID0gdGhpcy50YXJnZXQuZW50aXR5LmlzTWF4THYoKSA/IDIgOiAxXG4gICAgICAgICAgICAvLyDmgaLlpI3nlJ/lkb3mr5TmnIDkvY7nmoTlj4vmlrlcbiAgICAgICAgICAgIGlmIChmVGFyZ2V0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgZlRhcmdldHMuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgICAgICAgICBsZXQgYXcgPSBNYXRoLmZsb29yKCgxIC0gYS5nZXRIcFJhdGlvKCkpICogMTAwKVxuICAgICAgICAgICAgICAgICAgICBsZXQgYncgPSBNYXRoLmZsb29yKCgxIC0gYi5nZXRIcFJhdGlvKCkpICogMTAwKVxuICAgICAgICAgICAgICAgICAgICBhdyA9IGF3ICogMTAwICsgKDk5IC0gbWFwSGVscGVyLmdldFBvaW50VG9Qb2ludERpcyhwb2ludCwgYS5nZXRQb2ludCgpKSlcbiAgICAgICAgICAgICAgICAgICAgYncgPSBidyAqIDEwMCArICg5OSAtIG1hcEhlbHBlci5nZXRQb2ludFRvUG9pbnREaXMocG9pbnQsIGIuZ2V0UG9pbnQoKSkpXG4gICAgICAgICAgICAgICAgICAgIGF3ID0gYXcgKiAxMDAwICsgKDk5OSAtIGEuZ2V0QXR0YWNrSW5kZXgoKSlcbiAgICAgICAgICAgICAgICAgICAgYncgPSBidyAqIDEwMDAgKyAoOTk5IC0gYi5nZXRBdHRhY2tJbmRleCgpKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYncgLSBhd1xuICAgICAgICAgICAgICAgIH0pLnNsaWNlKDAsIGNudCkuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsID0gTWF0aC5yb3VuZChtLmdldE1heEhwKCkgKiBza2lsbC52YWx1ZSAqIDAuMDEpXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHYgPSBtLm9uSGVhbCh2YWwpXG4gICAgICAgICAgICAgICAgICAgIG0uY2hhbmdlU3RhdGUoUGF3blN0YXRlLkhFQUwsIHsgdmFsOiB2LCB0aW1lOiAwIH0pXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3RybC5wbGF5QmF0dGxlRWZmZWN0KFsxMTEwMDJdLCBtLmdldFBvaW50KCkpXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIOecqeaZleaVjOaWueaUu+WHu+WKm+acgOmrmOeahFxuICAgICAgICAgICAgaWYgKGVUYXJnZXRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICBlVGFyZ2V0cy5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGxldCBhdyA9IGEuZ2V0QWN0QXR0YWNrKCkgKiAxMDAgKyAoOTkgLSBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKHBvaW50LCBhLmdldFBvaW50KCkpKVxuICAgICAgICAgICAgICAgICAgICBsZXQgYncgPSBiLmdldEFjdEF0dGFjaygpICogMTAwICsgKDk5IC0gbWFwSGVscGVyLmdldFBvaW50VG9Qb2ludERpcyhwb2ludCwgYi5nZXRQb2ludCgpKSlcbiAgICAgICAgICAgICAgICAgICAgYXcgPSBhdyAqIDEwMDAgKyAoOTk5IC0gYS5nZXRBdHRhY2tJbmRleCgpKVxuICAgICAgICAgICAgICAgICAgICBidyA9IGJ3ICogMTAwMCArICg5OTkgLSBiLmdldEF0dGFja0luZGV4KCkpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBidyAtIGF3XG4gICAgICAgICAgICAgICAgfSkuc2xpY2UoMCwgY250KS5mb3JFYWNoKG0gPT4ge1xuICAgICAgICAgICAgICAgICAgICBtLmFkZEJ1ZmYoQnVmZlR5cGUuRElaWklORVNTLCB1aWQsIDMpXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY3RybC5wbGF5QmF0dGxlRWZmZWN0KFsxMTEwMDNdLCBtLmdldFBvaW50KCkpXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2lzQ2hlY2tMaXR0bGVHaXJsJywgdHJ1ZSlcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gQlRTdGF0ZS5SVU5OSU5HXG4gICAgfVxuXG4gICAgLy8g5qOA5rWL546J5L2pXG4gICAgcHJpdmF0ZSBjaGVja0phZGVQZW5kYW50KCkge1xuICAgICAgICB0aGlzLnRhcmdldC5yZW1vdmVCdWZmKEJ1ZmZUeXBlLkNIRUNLX0pBREVfUEVOREFOVClcbiAgICAgICAgY29uc3QgZWZmZWN0ID0gdGhpcy50YXJnZXQuZ2V0RXF1aXBFZmZlY3RCeVR5cGUoRXF1aXBFZmZlY3RUeXBlLkJBVFRMRV9CRUdJTl9TSElFTEQpXG4gICAgICAgIGlmIChlZmZlY3QpIHtcbiAgICAgICAgICAgIGNvbnN0IGNhbXAgPSB0aGlzLnRhcmdldC5jYW1wLCBwb2ludCA9IHRoaXMudGFyZ2V0LmdldFBvaW50KCksIHVpZCA9IHRoaXMudGFyZ2V0LmdldFVpZCgpXG4gICAgICAgICAgICBjb25zdCBzaGllbGQgPSBNYXRoLnJvdW5kKHRoaXMudGFyZ2V0LmdldE1heEhwKCkgKiBlZmZlY3QudmFsdWUgKiAwLjAxKVxuICAgICAgICAgICAgbGV0IGFyciA9IHRoaXMuY3RybC5nZXRGaWdodGVycygpLmZpbHRlcihtID0+IG0uY2FtcCA9PT0gY2FtcCAmJiBtLmlzUGF3bigpICYmICFtLmlzSGFzQnVmZihCdWZmVHlwZS5CQVRUTEVfQkVHSU5fU0hJRUxEKSlcbiAgICAgICAgICAgIGFyci5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgICAgICAgICAgbGV0IGF3ID0gOTkgLSBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKGEuZ2V0UG9pbnQoKSwgcG9pbnQpXG4gICAgICAgICAgICAgICAgbGV0IGJ3ID0gOTkgLSBtYXBIZWxwZXIuZ2V0UG9pbnRUb1BvaW50RGlzKGIuZ2V0UG9pbnQoKSwgcG9pbnQpXG4gICAgICAgICAgICAgICAgYXcgPSBhdyAqIDEwMDAgKyBNYXRoLmZsb29yKCgxIC0gYS5nZXRIcFJhdGlvKCkpICogMTAwKVxuICAgICAgICAgICAgICAgIGJ3ID0gYncgKiAxMDAwICsgTWF0aC5mbG9vcigoMSAtIGIuZ2V0SHBSYXRpbygpKSAqIDEwMClcbiAgICAgICAgICAgICAgICBhdyA9IGF3ICogMTAwMCArICg5OTkgLSBhLmdldEF0dGFja0luZGV4KCkpXG4gICAgICAgICAgICAgICAgYncgPSBidyAqIDEwMDAgKyAoOTk5IC0gYi5nZXRBdHRhY2tJbmRleCgpKVxuICAgICAgICAgICAgICAgIHJldHVybiBidyAtIGF3XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgYXJyID0gYXJyLnNsaWNlKDAsIDMpXG4gICAgICAgICAgICBhcnIuZm9yRWFjaChtID0+IHtcbiAgICAgICAgICAgICAgICBtLmFkZEJ1ZmZWYWx1ZShCdWZmVHlwZS5CQVRUTEVfQkVHSU5fU0hJRUxELCB1aWQsIHNoaWVsZCkgLy/mt7vliqDmiqTnm75idWZmXG4gICAgICAgICAgICAgICAgbS5hZGRCdWZmKEJ1ZmZUeXBlLldJVEhTVEFORCwgdWlkLCAxKSAvL+aKteaMoeS4gOasoeaKgOiDveaUu+WHu1xuICAgICAgICAgICAgICAgIHRoaXMuY3RybC5wbGF5QmF0dGxlRWZmZWN0KEJBVFRMRV9FRkZFQ1RfVFlQRS5TSElFTEQsIG0uZ2V0UG9pbnQoKSlcbiAgICAgICAgICAgIH0pXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgIH1cblxuICAgIC8vIOajgOa1i+aYr+WQpumcgOimgeaLieW8piDlpoLmnpzov5jmsqHmi4nlvKYg6YKj5LmI5bCx5pKt5pS+5ouJ5bym55qE5Yqo55S7XG4gICAgcHJpdmF0ZSBjaGVja1B1bGxTdHJpbmcoZHQ6IG51bWJlcikge1xuICAgICAgICBjb25zdCBjdXJBbmdlciA9IHRoaXMudGFyZ2V0LmdldEN1ckFuZ2VyKClcbiAgICAgICAgaWYgKGN1ckFuZ2VyID4gMCkge1xuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuU1VDQ0VTU1xuICAgICAgICB9IGVsc2UgaWYgKGN1ckFuZ2VyID09PSAwKSB7XG4gICAgICAgICAgICB0aGlzLnRhcmdldC5zZXRBbmdlcigtMSkgLy/orqnnirbmgIHlj5jmiJDmi4nlvKbnirbmgIFcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhbmltVGltZXMgPSB0aGlzLnRhcmdldC5lbnRpdHkuZ2V0QXR0YWNrQW5pbVRpbWVzKClbMF1cbiAgICAgICAgaWYgKGFuaW1UaW1lcz8ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgY29uc3QgbmVlZEF0dGFja1RpbWUgPSBNYXRoLmZsb29yKGFuaW1UaW1lc1swXSAqIDEwMDApXG4gICAgICAgICAgICBsZXQgY3VyclRpbWU6IG51bWJlciA9IHRoaXMuZ2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lJykgPz8gMFxuICAgICAgICAgICAgaWYgKGN1cnJUaW1lID49IG5lZWRBdHRhY2tUaW1lKSB7XG4gICAgICAgICAgICAgICAgbGV0IHZhbCA9IDFcbiAgICAgICAgICAgICAgICBpZiAodGhpcy50YXJnZXQuZ2V0U3RyYXRlZ3lWYWx1ZSgzMTQwMikgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbCA9IDIgLy/pn6znlaVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy50YXJnZXQuc2V0QW5nZXIodmFsKVxuICAgICAgICAgICAgICAgIHRoaXMudGFyZ2V0LmNoYW5nZVN0YXRlKFBhd25TdGF0ZS5TVEFORClcbiAgICAgICAgICAgICAgICByZXR1cm4gQlRTdGF0ZS5GQUlMVVJFXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyDlop7liqDml7bpl7RcbiAgICAgICAgICAgIHRoaXMuc2V0QmxhY2tib2FyZERhdGEoJ2N1cnJUaW1lJywgY3VyclRpbWUgKyBkdClcbiAgICAgICAgICAgIC8vIOiuvue9ruWjq+WFteeKtuaAgSDmm7TmlrDop4blm77kv6Hmga9cbiAgICAgICAgICAgIGlmICghdGhpcy5pc0NoYW5nZUZpZ2h0ZXJTdGF0ZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuaXNDaGFuZ2VGaWdodGVyU3RhdGUgPSB0cnVlXG4gICAgICAgICAgICAgICAgdGhpcy50YXJnZXQuY2hhbmdlU3RhdGUoUGF3blN0YXRlLlNLSUxMLCB7IGN1cnJBdHRhY2tUaW1lOiBjdXJyVGltZSB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIEJUU3RhdGUuUlVOTklOR1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBCVFN0YXRlLlNVQ0NFU1NcbiAgICB9XG59Il19