
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/Probability.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '81a54L6me1Dy5siPd0p/rMx', 'Probability');
// app/script/model/behavior/Probability.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseCondition_1 = require("./BaseCondition");
var BTConstant_1 = require("./BTConstant");
// 概率
var Probability = /** @class */ (function (_super) {
    __extends(Probability, _super);
    function Probability() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.odds = 0;
        return _this;
    }
    Probability.prototype.onInit = function (conf) {
        this.odds = conf.parameters;
    };
    Probability.prototype.onTick = function (dt) {
        return this.ctrl.getRandom().chance(this.odds) ? BTConstant_1.BTState.SUCCESS : BTConstant_1.BTState.FAILURE;
    };
    return Probability;
}(BaseCondition_1.default));
exports.default = Probability;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0c1xcYXBwXFxzY3JpcHRcXG1vZGVsXFxiZWhhdmlvclxcUHJvYmFiaWxpdHkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsaURBQTRDO0FBQzVDLDJDQUF1QztBQUV2QyxLQUFLO0FBQ0w7SUFBeUMsK0JBQWE7SUFBdEQ7UUFBQSxxRUFXQztRQVRXLFVBQUksR0FBVyxDQUFDLENBQUE7O0lBUzVCLENBQUM7SUFQVSw0QkFBTSxHQUFiLFVBQWMsSUFBUztRQUNuQixJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQyxVQUFVLENBQUE7SUFDL0IsQ0FBQztJQUVNLDRCQUFNLEdBQWIsVUFBYyxFQUFVO1FBQ3BCLE9BQU8sSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxvQkFBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsb0JBQU8sQ0FBQyxPQUFPLENBQUE7SUFDdEYsQ0FBQztJQUNMLGtCQUFDO0FBQUQsQ0FYQSxBQVdDLENBWHdDLHVCQUFhLEdBV3JEIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEJhc2VDb25kaXRpb24gZnJvbSBcIi4vQmFzZUNvbmRpdGlvblwiO1xyXG5pbXBvcnQgeyBCVFN0YXRlIH0gZnJvbSBcIi4vQlRDb25zdGFudFwiO1xyXG5cclxuLy8g5qaC546HXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFByb2JhYmlsaXR5IGV4dGVuZHMgQmFzZUNvbmRpdGlvbiB7XHJcblxyXG4gICAgcHJpdmF0ZSBvZGRzOiBudW1iZXIgPSAwXHJcblxyXG4gICAgcHVibGljIG9uSW5pdChjb25mOiBhbnkpIHtcclxuICAgICAgICB0aGlzLm9kZHMgPSBjb25mLnBhcmFtZXRlcnNcclxuICAgIH1cclxuXHJcbiAgICBwdWJsaWMgb25UaWNrKGR0OiBudW1iZXIpIHtcclxuICAgICAgICByZXR1cm4gdGhpcy5jdHJsLmdldFJhbmRvbSgpLmNoYW5jZSh0aGlzLm9kZHMpID8gQlRTdGF0ZS5TVUNDRVNTIDogQlRTdGF0ZS5GQUlMVVJFXHJcbiAgICB9XHJcbn0iXX0=