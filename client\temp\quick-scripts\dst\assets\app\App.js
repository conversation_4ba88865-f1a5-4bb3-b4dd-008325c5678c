
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/App.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '09b6eEbiRtBBIvq54SBitim', 'App');
// app/App.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.App = void 0;
var version_1 = require("../scene/version");
var Enums_1 = require("./script/common/constant/Enums");
var DhHelper_1 = require("./script/common/helper/DhHelper");
var ErrorReportHelper_1 = require("./script/common/helper/ErrorReportHelper");
var ShareHelper_1 = require("./script/common/helper/ShareHelper");
var TaHelper_1 = require("./script/common/helper/TaHelper");
var LocalConfig_1 = require("./script/common/LocalConfig");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var App = /** @class */ (function (_super) {
    __extends(App, _super);
    function App() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.windName = '';
        return _this;
    }
    App.prototype.onLoad = function () {
        // cc.game.setFrameRate(60)
        // cc.debug.setDisplayStats(!localConfig.RELEASE)
        cc.debug.setDisplayStats(false);
        ut.setKeepScreenOn(true);
        logger.open = LocalConfig_1.localConfig.openLog;
        logger.openPrint = LocalConfig_1.localConfig.openPrint;
        if (LocalConfig_1.localConfig.RELEASE) {
            cc.log = function (msg) {
                var subst = [];
                for (var _i = 1; _i < arguments.length; _i++) {
                    subst[_i - 1] = arguments[_i];
                }
            };
        }
    };
    App.prototype.start = function () {
        ErrorReportHelper_1.errorReportHelper.setCurLevel(Enums_1.ReportErrorLevel.START);
        mc.init(version_1.default.GAME_NAME_SPACE, this.node, {
            lang: version_1.default.getLocalLang(),
            changeLang: true,
            pnlIndexConf: {
                'common/Top': { gt: ['common/UI'] },
                'main/SeasonSwitch': { gt: ['common/UI', 'common/Top', 'main/CaptureTip', 'main/GameOver'] },
                'common/Guide': {
                    gt: [
                        'common/UI',
                        'common/BTQueue',
                        'common/CreateArmy',
                        'common/TreasureList',
                        'common/SelectPortrayal',
                        'common/PortrayalInfo',
                        'main/SelectArmy',
                        'main/ArmyList',
                        'area/BuildList',
                        'area/PawnInfo',
                        'area/AreaArmy',
                        'area/SelectAvatarHero',
                        'build/BuildBarracks',
                        'build/BuildMainInfo',
                        'build/StudySelect',
                        'build/StartStudyTip',
                        'build/BuildSmithy',
                        'build/BuildHerohall',
                        'build/BuildHospital',
                        'menu/Pointsets',
                        'menu/Collection',
                        'novice/NoviceGameOver',
                    ]
                },
                'login/LoginUI': {
                    gt: [
                        'login/AppUpdateTip',
                        'login/CreatePlayer',
                        'login/LoginButton',
                        'login/SelectServer',
                        'login/VersionLowTip',
                        'login/WxUpdateTip',
                        'login/LogoutTimeTip',
                        'login/BanAccountTimeTip',
                    ]
                },
                'login/Feedback': {
                    gt: [
                        'login/AppUpdateTip',
                        'login/CreatePlayer',
                        'login/LoginButton',
                        'login/SelectServer',
                        'login/VersionLowTip',
                        'login/WxUpdateTip',
                        'login/LogoutTimeTip',
                        'login/BanAccountTimeTip',
                    ]
                },
                'login/Standings': {
                    gt: [
                        'login/AppUpdateTip',
                        'login/CreatePlayer',
                        'login/LoginButton',
                        'login/SelectServer',
                        'login/VersionLowTip',
                        'login/WxUpdateTip',
                        'login/LogoutTimeTip',
                        'login/BanAccountTimeTip',
                    ]
                },
            }
        });
        TaHelper_1.taHelper.init();
        DhHelper_1.dhHelper.init();
        ShareHelper_1.shareHelper.init();
        this.load();
    };
    App.prototype.load = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // 加载首个窗口
                    return [4 /*yield*/, this.loadBundleByName(cc.AssetManager.BuiltinBundleName.RESOURCES)];
                    case 1:
                        // 加载首个窗口
                        _a.sent();
                        return [4 /*yield*/, this.loadWind(this.windName)];
                    case 2:
                        _a.sent();
                        this.FindChild('LoginBg').destroy();
                        // 销毁loginBg 进入第一个窗口
                        eventCenter.emit(mc.Event.GOTO_WIND, this.windName);
                        this.enabled = false;
                        return [2 /*return*/];
                }
            });
        });
    };
    App.prototype.loadWind = function (wind) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) { return eventCenter.emit(mc.Event.PRELOAD_WIND, wind, resolve); })];
            });
        });
    };
    // private async hideLogo(it: cc.Node) {
    //     return new Promise(reslove => {
    //         cc.tween(it)
    //             .to(0.5, { opacity: 0 }, { easing: cc.easing.sineIn })
    //             .call(reslove)
    //             .start()
    //     })
    // }
    App.prototype.loadBundle = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve) {
                        cc.assetManager.loadBundle(name, function (err, bundle) {
                            if (err) {
                                console.error('loadBundleError: ', name, err);
                            }
                            resolve(!err);
                        });
                    })];
            });
        });
    };
    App.prototype.loadBundleByName = function (name) {
        return __awaiter(this, void 0, void 0, function () {
            var success;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!true) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.loadBundle(name)];
                    case 1:
                        success = _a.sent();
                        if (success) {
                            return [2 /*return*/];
                        }
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 100); })];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 0];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    __decorate([
        property()
    ], App.prototype, "windName", void 0);
    App = __decorate([
        ccclass
    ], App);
    return App;
}(cc.Component));
exports.App = App;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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