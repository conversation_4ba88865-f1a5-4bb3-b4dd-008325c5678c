
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/notice/WindWaitNotCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '9e1b8Mu9m5Mi7WJcWhZDC9F', 'WindWaitNotCtrl');
// app/script/view/notice/WindWaitNotCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var ccclass = cc._decorator.ccclass;
var WindWaitNotCtrl = /** @class */ (function (_super) {
    __extends(WindWaitNotCtrl, _super);
    function WindWaitNotCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.roootNode_ = null; // path://rooot_n
        return _this;
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    //@end
    WindWaitNotCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d;
        return [
            (_a = {}, _a[mc.Event.LOAD_BEGIN_WIND] = this.onEventOpen, _a),
            (_b = {}, _b[mc.Event.LOAD_END_WIND] = this.onEventHide, _b),
            (_c = {}, _c[mc.Event.READY_BEGIN_WIND] = this.onEventOpen, _c),
            (_d = {}, _d[mc.Event.READY_END_WIND] = this.onEventHide, _d),
        ];
    };
    WindWaitNotCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.roootNode_.active = false;
                return [2 /*return*/];
            });
        });
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    WindWaitNotCtrl.prototype.onEventOpen = function () {
        this.open();
        this.roootNode_.active = true;
    };
    WindWaitNotCtrl.prototype.onEventHide = function () {
        this.hide();
        this.roootNode_.active = false;
    };
    WindWaitNotCtrl = __decorate([
        ccclass
    ], WindWaitNotCtrl);
    return WindWaitNotCtrl;
}(mc.BaseNoticeCtrl));
exports.default = WindWaitNotCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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