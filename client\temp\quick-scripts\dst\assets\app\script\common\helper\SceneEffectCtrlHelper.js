
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/common/helper/SceneEffectCtrlHelper.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '3805agwKRhJvKIFnaLzLqpp', 'SceneEffectCtrlHelper');
// app/script/common/helper/SceneEffectCtrlHelper.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sceneEffectCtrlHelper = void 0;
// 场景特效控制信息
var SceneEffectCtrlHelper = /** @class */ (function () {
    function SceneEffectCtrlHelper() {
        this.DURATION_RANGE = [60, 120]; //持续时间
        this.INTERVAL_RANGE = [60, 120]; //间隔时间
        this.intervalTime = 0;
        this.intervalElapsed = 0;
        this.durationTime = 0;
        this.durationElapsed = 0;
        this.running = true;
    }
    SceneEffectCtrlHelper.prototype.init = function () {
        this.running = ut.random(100) > 50;
        this.durationTime = ut.random(this.DURATION_RANGE[0], this.DURATION_RANGE[1]) + 3;
        this.intervalTime = ut.random(this.INTERVAL_RANGE[0], this.INTERVAL_RANGE[1]);
        // cc.log('run: ' + this.running)
    };
    SceneEffectCtrlHelper.prototype.update = function (dt) {
        if (!this.intervalTime || !this.durationTime) {
            return;
        }
        else if (this.running) {
            this.durationElapsed += dt;
            if (this.durationElapsed >= this.durationTime) {
                this.durationElapsed = 0;
                this.durationTime = ut.random(this.DURATION_RANGE[0], this.DURATION_RANGE[1]) + 3;
                this.running = false;
                // cc.log('run: ' + this.running)
            }
        }
        else {
            this.intervalElapsed += dt;
            if (this.intervalElapsed >= this.intervalTime) {
                this.intervalElapsed = 0;
                this.intervalTime = ut.random(this.INTERVAL_RANGE[0], this.INTERVAL_RANGE[1]);
                this.running = true;
                // cc.log('run: ' + this.running)
            }
        }
    };
    return SceneEffectCtrlHelper;
}());
exports.sceneEffectCtrlHelper = new SceneEffectCtrlHelper();

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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