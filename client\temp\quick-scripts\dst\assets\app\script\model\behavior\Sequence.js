
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/behavior/Sequence.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '57413bkv/pBprDaAbQPY0xm', 'Sequence');
// app/script/model/behavior/Sequence.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var BaseComposite_1 = require("./BaseComposite");
var BTConstant_1 = require("./BTConstant");
// 序列执行，如果一个节点执行失败就返回
var Sequence = /** @class */ (function (_super) {
    __extends(Sequence, _super);
    function Sequence() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Sequence.prototype.onOpen = function () {
        this.setBlackboardData('startIndex', 0);
    };
    Sequence.prototype.onTick = function (dt) {
        var _a;
        var startIndex = (_a = this.getBlackboardData('startIndex')) !== null && _a !== void 0 ? _a : 0;
        for (var i = startIndex, l = this.getChildrenCount(); i < l; i++) {
            var state = this.children[i].execute(dt);
            if (state === BTConstant_1.BTState.SUCCESS) {
                continue;
            }
            else if (state === BTConstant_1.BTState.RUNNING) {
                this.setBlackboardData('startIndex', i); //这里记录运行中的 下次继续从这里执行
            }
            return state;
        }
        return BTConstant_1.BTState.SUCCESS;
    };
    return Sequence;
}(BaseComposite_1.default));
exports.default = Sequence;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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