
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/fsp/test_battle.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a62115SschLw72K4toRSuMa', 'test_battle');
// app/script/model/fsp/test_battle.ts

"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
Object.defineProperty(exports, "__esModule", { value: true });
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var MapHelper_1 = require("../../common/helper/MapHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
window['battleTest'] = function (landId, dis, dir, mul) {
    if (dir === void 0) { dir = 2; }
    if (mul === void 0) { mul = 1; }
    if (!cc.sys.isBrowser) {
        return;
    }
    var index = 1, owner = GameHelper_1.gameHpr.getUid();
    var attackIndex = 0;
    var armys = [], fighters = [], towers = [];
    var point = { x: 5, y: 0 };
    if (dir === 0) {
        point = { x: 5, y: 10 };
    }
    else if (dir === 1) {
        point = { x: 10, y: 5 };
    }
    else if (dir === 3) {
        point = { x: 0, y: 5 };
    }
    var pawnCountMap = {};
    // 加入我方军队
    getMyArmys().map(function (m, i) {
        m.uid = ut.UID();
        m.name = '编队' + i;
        m.index = index;
        m.owner = owner;
        m.state = 3;
        m.enterDir = dir;
        m.pawns.forEach(function (x) {
            x.index = index;
            x.uid = ut.UID();
            x.point = { x: point.x, y: point.y };
            if (pawnCountMap[x.id + '_' + x.lv]) {
                pawnCountMap[x.id + '_' + x.lv] += 1;
            }
            else {
                pawnCountMap[x.id + '_' + x.lv] = 1;
            }
        });
        return m;
    }).forEach(function (army) {
        army.pawns.forEach(function (pawn) {
            fighters.push({
                uid: pawn.uid,
                camp: 2,
                waitRound: army.waitRound || 0,
                attackIndex: ++attackIndex,
            });
        });
        armys.push(army);
    });
    // 加入敌方军队
    var conf = GameHelper_1.gameHpr.getAreaPawnConfInfo(index, landId, dis);
    conf.armys.forEach(function (army) {
        army.pawns.sort(function (a, b) {
            var ap = assetsMgr.getJsonData('pawnBase', a.id).attack_speed;
            var bp = assetsMgr.getJsonData('pawnBase', b.id).attack_speed;
            var aw = ap * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(a.point, point));
            var bw = bp * 100 + (99 - MapHelper_1.mapHelper.getPointToPointDis(b.point, point));
            return bw - aw;
        });
        army.pawns.forEach(function (pawn) {
            fighters.push({
                uid: pawn.uid,
                camp: 1,
                waitRound: 0,
                attackIndex: ++attackIndex,
            });
        });
    });
    // 加入箭塔
    towers.push({
        id: 7002,
        lv: 1,
        uid: ut.UID(),
        camp: 1,
        attackIndex: ++attackIndex,
    });
    armys.pushArr(conf.armys);
    // 设置回放数据
    GameHelper_1.gameHpr.playback.setRecordData({
        index: index,
        frames: [{
                type: 0,
                camp: 1,
                owner: '',
                cityId: 0,
                randSeed: 114200,
                fps: 20,
                hp: conf.hp,
                builds: [{ id: 2102, point: { x: 5, y: 5 } }],
                armys: armys,
                fighters: fighters,
                towers: towers,
                mul: mul,
            }],
    }).then(function () {
        ViewHelper_1.viewHelper.gotoWind('playback');
        eventCenter.off(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
        eventCenter.on(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
        function onAreaBattleEnd(index) {
            eventCenter.off(EventType_1.default.AREA_BATTLE_END, onAreaBattleEnd);
            var area = GameHelper_1.gameHpr.areaCenter.getLookArea();
            var has = false, cnt = 0;
            cc.log('----------------------存活士兵----------------------');
            area === null || area === void 0 ? void 0 : area.armys.forEach(function (m) {
                if (m.owner === owner) {
                    m.pawns.forEach(function (pawn) {
                        var key = pawn.id + '_' + pawn.lv;
                        if (pawnCountMap[key]) {
                            pawnCountMap[key] -= 1;
                        }
                        cnt += 1;
                        cc.log(cnt + ' id=' + pawn.id + ', lv=' + pawn.lv + ', hp=' + pawn.getHpText() + ' (' + (pawn.getHpRatio() * 100).toFixed(1) + '%)');
                        has = true;
                    });
                }
            });
            if (!has) {
                cc.log('无一生还!');
            }
            cc.log('----------------------损失士兵----------------------');
            has = false;
            for (var key in pawnCountMap) {
                var _a = __read(key.split('_'), 2), id = _a[0], lv = _a[1];
                var count = pawnCountMap[key];
                if (count) {
                    has = true;
                    cc.log('id=' + id + ', lv=' + lv + ', count=' + count);
                }
            }
            if (!has) {
                cc.log('没有损失!');
            }
            cc.log('---------------------------------------------------');
        }
    });
};
window['battleTest2'] = function () {
    if (!cc.sys.isBrowser) {
        return;
    }
    var index = 1, owner = GameHelper_1.gameHpr.getUid();
    var attackIndex = 0;
    var armys = [], fighters = [], towers = [];
    // 加入我方军队
    getMyArmys().map(function (m, i) {
        m.uid = ut.UID();
        m.name = '我方' + i;
        m.index = index;
        m.owner = owner;
        m.state = 3;
        m.enterDir = 2;
        m.pawns.forEach(function (x) {
            x.index = index;
            x.uid = ut.UID();
            x.point = { x: 5, y: 0 };
        });
        return m;
    }).forEach(function (army) {
        army.pawns.forEach(function (pawn) {
            fighters.push({
                uid: pawn.uid,
                camp: 2,
                waitRound: army.waitRound || 0,
                attackIndex: ++attackIndex,
            });
        });
        armys.push(army);
    });
    // 加入敌方军队
    getEnemyArmys().map(function (m, i) {
        m.uid = ut.UID();
        m.name = '敌方' + i;
        m.index = index;
        m.owner = '1111111';
        m.state = 3;
        m.enterDir = 0;
        m.pawns.forEach(function (x) {
            x.index = index;
            x.uid = ut.UID();
            x.point = { x: 5, y: 4 };
        });
        return m;
    }).forEach(function (army) {
        army.pawns.forEach(function (pawn) {
            fighters.push({
                uid: pawn.uid,
                camp: 1,
                waitRound: 0,
                attackIndex: ++attackIndex,
            });
        });
        armys.push(army);
    });
    // 加入箭塔
    fighters.push({
        towerId: 7001,
        towerLv: 1,
        uid: ut.UID(),
        camp: 1,
        attackIndex: ++attackIndex,
        point: { x: 5, y: 5 },
        waitRound: 0,
    });
    // 设置回放数据
    GameHelper_1.gameHpr.playback.setRecordData({
        index: index,
        frames: [{
                type: 0,
                camp: 1,
                owner: '1111111',
                cityId: 0,
                randSeed: 114200,
                fps: 20,
                hp: [50, 50],
                // builds: [{ id: 2101, point: { x: 5, y: 5 }, lv: 1 }],
                armys: armys,
                fighters: fighters,
                towers: towers,
            }],
    }).then(function () {
        ViewHelper_1.viewHelper.gotoWind('playback');
    });
};
// 获取我的军队
function getMyArmys() {
    return [
        //  2级地 5距离 3级地 4距离
        // { pawns: [
        //     { id: 3201, lv: 1 }, 
        //     { id: 3201, lv: 1 }, 
        //     { id: 3101, lv: 1 }, 
        //     { id: 3101, lv: 1 }, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        // ] },
        //  1级地 6距离 不带装备
        // { pawns: [
        //     { id: 3201, lv: 1 }, 
        //     { id: 3201, lv: 1 }, 
        //     { id: 3201, lv: 1 }, 
        //     { id: 3101, lv: 1 }, 
        //     { id: 3101, lv: 1 }, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        // ] },
        // //  1级地 6距离
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  }}, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        // ] },
        //  1级地 7距离   2级6距离  7距离   3级 6距离  4级 5距离
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        // ] },
        //  1级地 8距离 9距离 2级地8距离
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        // ] },
        //  1级地 9距离  10距离[损失6] 没有猎人 最挫的远程 4级6[损失6]
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // ] },
        //  1级地 11距离 没有猎人 最挫的远程
        //     { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // ] },
        // //  1级地 11距离  带了猎人  2级10距离
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // ] },
        //  1级地 12~14距离  2级9       没有猎人 最挫的远程
        //     { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } }, 
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        //     { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // ] },
        //  2级地 11距离 没有猎人 最挫的远程
        // { pawns: [
        // { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        // { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        // { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        // { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        // { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] }  }, 
        // { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]]  } },
        // ] },
        //这里开始一队就很难了，需要额外队伍
        //1级16距离  2级12距离  2级13距离 14距离  15距离
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        //2级16距离 3级地11 12[损失比较大]
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // 3满队1级 3级地13[损失7个] 3级地14[损失19个] 
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        //3级地最难测试：满级重盾队伍，带铁盾
        // {
        //     pawns: [
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //     ]
        // },
        // {
        //     pawns: [
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //         { id: 3404, lv: 6, equip: { id: 6022, attrs: [[0, 1, ut.random(20, 70)], [2, 26, 35]] } },
        //     ]
        // },
        // 4满队1级 附加了毒弓 3级地15[损失9个兵] 3级地16[损失11个兵] 4级10~12[损失12个兵]
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // //4级 15距离 死17个兵  如果带头盔 会死4个4级兵  如果带铁盾，则只会死低兵
        // //equip: { id: 6022, attrs: [[0, 1, 45], [2, 26, 35]] }
        // {
        //     pawns: [
        //         { id: 3201, lv: 6, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 6, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 6, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 6, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 6, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3201, lv: 6, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3103, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6002, attrs: [[0, 1, 85], [1, 2, 0]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3303, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        //2级16 无伤
        // {
        //     pawns: [
        //         { id: 3201, lv: 5, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3201, lv: 5, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3201, lv: 5, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 2, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3201, lv: 1, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6016, attrs: [[0, 1, 40], [1, 2, 0], [2, 16, 15, 50]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]] } },
        //     ]
        // },
        //     //1级地 14 15 16   2级10
        //     { pawns: [{ id: 3201, lv: 4, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 3, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 3, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        // ] },
        //     //1级地 12级 ~ 13级 2级9
        //     { pawns: [{ id: 3201, lv: 2, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3201, lv: 2, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  }  }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        // ] },
        //1级地<=8~11  2级8
        //     { pawns: [{ id: 3201, lv: 2, equip: { id: 6002, attrs: [[0, 1, 95], [1, 2, 0]] }  }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  }  }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 10]]  } },
        // ] },
        // { pawns: [{ id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }, { id: 3201, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }, { id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3101, lv: 6 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }] },
        // { pawns: [{ id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 4 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }] },
        // { pawns: [{ id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }, { id: 3101, lv: 3 }] },
        // { pawns: [{ id: 3101, lv: 3 }, { id: 3102, lv: 3 }, { id: 3103, lv: 3 }, { id: 3201, lv: 3 }, { id: 3202, lv: 3 }, { id: 3301, lv: 3 }, { id: 3302, lv: 3 }, { id: 3401, lv: 3 }, { id: 3402, lv: 3 }] },
        // { pawns: [{ id: 3501, lv: 1 }, { id: 3502, lv: 1 }, { id: 3203, lv: 3 }, { id: 3303, lv: 3 }, { id: 3304, lv: 3 }] },
        // {
        //     pawns: [
        //         // { id: 3104, lv: 5, equip: { id: 6013, attrs: [[0, 2, 10], [1, 1, 20], [2, 11, 50]] } },
        //         { id: 3202, lv: 6 },
        //         // { id: 3404, lv: 6, equip: { id: 6020, attrs: [[0, 1, 1], [1, 2, 2], [2, 20, 8, 40]] } },
        //         // { id: 3203, lv: 6, equip: { id: 6016, attrs: [[0, 1, 1], [1, 2, 2], [2, 16, 20, 50]] } },
        //         // { id: 3304, lv: 6 },
        //         // { id: 3303, lv: 6, equip: { id: 6015, attrs: [[0, 1, 1], [1, 2, 2], [2, 15, 50, 50]] } }
        //     ]
        // },
        // { pawns: [{ id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }, { id: 3402, lv: 6 }] },
        // { pawns: [{ id: 3401, lv: 3 }, { id: 3401, lv: 2 }, { id: 3401, lv: 1 }, { id: 3401, lv: 3 }] },
        // {
        //     pawns: [
        //         { id: 3305, lv: 6, equip: { id: 6117, attrs: [[0, 1, 1], [1, 2, 2], [2, 5, 20, 40]] } },
        //         { id: 3305, lv: 6, equip: { id: 6117, attrs: [[0, 1, 1], [1, 2, 2], [2, 5, 20, 40]] } },
        //         { id: 3305, lv: 6, equip: { id: 6117, attrs: [[0, 1, 1], [1, 2, 2], [2, 5, 20, 40]] } },
        //         { id: 3305, lv: 6, equip: { id: 6117, attrs: [[0, 1, 1], [1, 2, 2], [2, 5, 20, 40]] } },
        //     ]
        // },
        // {
        //     pawns: [
        //         { id: 3202, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120]] } },
        //         // { id: 3201, lv: 1, equip: { id: 6022, attrs: [[0, 1, 60], [2, 26, 35]] } },
        //         // { id: 3201, lv: 1, equip: { id: 6022, attrs: [[0, 1, 60], [2, 26, 35]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //     ],
        //     waitRound: 1
        // },
        // {
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //     ]
        // },
        // {
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [2, 1, 8, 2]] } },
        //     ]
        // },
        // {
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //     ]
        // },
        // {
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 4], [2, 1, 8, 2]] } },
        //     ]
        // },
        // { pawns: [{ id: 3101, lv: 6 }] },
        // { pawns: [{ id: 3102, lv: 6 }] },
        // { pawns: [{ id: 3103, lv: 6 }] },
        // { pawns: [{ id: 3104, lv: 6 }] },
        // { pawns: [{ id: 3105, lv: 6 }] },
        // { pawns: [{ id: 3201, lv: 6 }] },
        // { pawns: [{ id: 3202, lv: 6 }] },
        // { pawns: [{ id: 3203, lv: 6 }] },
        // { pawns: [{ id: 3104, lv: 6 }] },
        // { pawns: [{ id: 3205, lv: 6 }] },
        // { pawns: [{ id: 3301, lv: 6 }] },
        // { pawns: [{ id: 3302, lv: 6 }] },
        // { pawns: [{ id: 3303, lv: 6 }] },
        // { pawns: [{ id: 3304, lv: 6 }] },
        // { pawns: [{ id: 3305, lv: 6 }] },
        // { pawns: [{ id: 3401, lv: 6 }] },
        // { pawns: [{ id: 3402, lv: 6 }] },
        // { pawns: [{ id: 3403, lv: 6 }] },
        // { pawns: [{ id: 3404, lv: 6 }] },
        // { pawns: [{ id: 3405, lv: 6 }] },
        // { pawns: [{ id: 3501, lv: 1 }] },
        // { pawns: [{ id: 3502, lv: 1 }] },
        // {
        //     pawns: [
        //         { id: 3405, lv: 6, equip: { id: 6119, attrs: [[0, 1, 1], [1, 2, 8], [2, 3, 200, 40], [2, 17, 0, 40], [2, 28]] } },
        //         { id: 3405, lv: 6, equip: { id: 6119, attrs: [[0, 1, 1], [1, 2, 8], [2, 3, 200, 40], [2, 17, 0, 40], [2, 28]] } },
        //         { id: 3405, lv: 6, equip: { id: 6119, attrs: [[0, 1, 1], [1, 2, 8], [2, 3, 200, 40], [2, 17, 0, 40], [2, 28]] } },
        //         { id: 3405, lv: 6, equip: { id: 6119, attrs: [[0, 1, 1], [1, 2, 8], [2, 3, 200, 40], [2, 17, 0, 40], [2, 28]] } },
        //     ]
        // },
        // { //长枪兵
        //     pawns: [
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 33], [0, 2, 8], [2, 15, 0, 43], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //         { id: 3101, lv: 6, equip: { id: 6101, attrs: [[0, 1, 1], [1, 2, 8], [2, 15, 0, 40], [2, 1, 7], [2, 19, 5]] } },
        //     ]
        // },
        // { //长剑兵
        //     pawns: [
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //         { id: 3105, lv: 6, equip: { id: 6118, attrs: [[0, 1, 1], [1, 2, 8], [2, 17, 0, 40], [2, 20, 7], [2, 19, 5]] } },
        //     ]
        // },
        // { //长矛兵
        //     pawns: [
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3102, lv: 6, equip: { id: 6102, attrs: [[0, 1, 1], [1, 2, 8], [2, 14, 5], [2, 5, 40], [2, 19, 5]] } },
        //     ]
        // },
        // { //大刀骑
        //     pawns: [
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //         { id: 3403, lv: 6, equip: { id: 6113, attrs: [[0, 1, 22], [0, 2, 14], [2, 2101, 28], [2, 5, 40], [2, 19, 5]] } },
        //     ]
        // },
        // { //连弩
        //     pawns: [
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //         { id: 3302, lv: 6, equip: { id: 6107, attrs: [[0, 1, 20], [0, 2, 11], [2, 15, 0, 100], [2, 14, 5], [2, 17, 0, 40]] } },
        //     ]
        // },
        // { //枪骑
        //     pawns: [
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //         { id: 3402, lv: 6, equip: { id: 6109, attrs: [[0, 1, 20], [0, 2, 8], [2, 3, 170, 43], [2, 5, 38], [2, 19, 5]] } },
        //     ]
        // },
        // //回血刀测试
        // {
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3304, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3202, lv: 1, equip: { id: 6022, attrs: [[0, 1, 60], [2, 26, 35]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //         { id: 3301, lv: 1, equip: { id: 6003, attrs: [[0, 2, 5], [1, 1, 15], [2, 1, 8, 20]] } },
        //     ]
        // },
        //最新
        //  1级地<=1~2 简单1
        //     { pawns: [{ id: 3101, lv: 1  }, 
        //     { id: 3101, lv: 1}, 
        // ] },
        //  1级地 3距离
        // { pawns: [{ id: 3101, lv: 1  }, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        // ] },
        //  1级地 4距离
        // { pawns: [
        //     { id: 3201, lv: 1 }, 
        //     { id: 3101, lv: 1}, 
        //     { id: 3101, lv: 1}, 
        // ] },
        //  //  1级地 5距离
        //  { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] } }, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        // ] },
        //  1级地 6距离 简单6
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 6]] }}, 
        // ] },
        //  2级地 4距离 简单7
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        // ] },
        //  2级地 5距离 简单8
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3101, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] }}, 
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        // ] },
        // //  2级地 5距离 简单9
        // { pawns: [
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 70], [1, 2, 0]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        //     { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 7]] } },
        // ] },
        //1级地
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 80], [1, 2, 0]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 3,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        // {
        //     waitRound: 4,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 8]] } },
        //     ]
        // },
        //地狱2
        // {
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //         { id: 3201, lv: 1, equip: { id: 6002, attrs: [[0, 1, 120], [1, 2, 1]] } },
        //     ]
        // },
        // {
        //     waitRound: 1,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //     ]
        // },
        // {
        //     waitRound: 2,
        //     pawns: [
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //         { id: 3304, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
        //     ]
        // },
        {
            // waitRound: 2,
            pawns: [
                { id: 3302, lv: 6, portrayal: { id: 330201, attrs: [[0, 1, 100], [0, 2, 10], [1, 25, 10], [2, 10001, 0]] } },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
                { id: 3405, lv: 6 },
            ]
        },
    ];
}
// 获取敌方的军队
function getEnemyArmys() {
    return [
        // {
        //     pawns: [
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //         { id: 3101, lv: 1 },
        //     ]
        // },
        {
            pawns: [
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
                { id: 3202, lv: 1, equip: { id: 6001, attrs: [[0, 1, 0], [1, 2, 13]] } },
            ]
        },
    ];
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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