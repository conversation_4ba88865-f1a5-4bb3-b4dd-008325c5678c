
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/build/BuildSmithyPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '73937giKMhL77Muov2GnF8G', 'BuildSmithyPnlCtrl');
// app/script/view/build/BuildSmithyPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var CTypeObj_1 = require("../../model/common/CTypeObj");
var BuildUnlockTipCmpt_1 = require("../cmpt/BuildUnlockTipCmpt");
var ReddotHelper_1 = require("../../common/helper/ReddotHelper");
var ccclass = cc._decorator.ccclass;
var BuildSmithyPnlCtrl = /** @class */ (function (_super) {
    __extends(BuildSmithyPnlCtrl, _super);
    function BuildSmithyPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.tabsTc_ = null; // path://root/tabs_tc_tce
        _this.pagesNode_ = null; // path://root/pages_n
        _this.unlockTipNode_ = null; // path://root/pages_n/0/bottom/title/unlock_tip_n
        _this.showRangeTge_ = null; // path://root/pages_n/1/info/buttons/show_range_t_te
        //@end
        _this.PKEY_TAB = 'SMITHY_TAB';
        _this.PKEY_SELECT_EQUIP_SLOT = 'PKEY_SELECT_EQUIP_SLOT';
        _this.PKEY_SELECT_EXCLUSIVE_SLOT = 'PKEY_SELECT_EXCLUSIVE_SLOT';
        _this.user = null;
        _this.player = null;
        _this.data = null;
        _this.tab = 0;
        _this.selectSlot = null;
        _this.lockEffectConf = {};
        _this.currPlayForgeSFX = false;
        _this.isForgedEquip = false; // 是否打造或重铸装备
        _this.unlockTipCmpt = null;
        return _this;
    }
    BuildSmithyPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b, _c, _d, _e, _f, _g;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_EQUIP_ATTR] = this.onUpdateEquipAttr, _b.enter = true, _b),
            (_c = {}, _c[EventType_1.default.UPDATE_BUILD_LV] = this.onUpdateBuildLv, _c.enter = true, _c),
            (_d = {}, _d[EventType_1.default.FORGE_EQUIP_COMPLETE] = this.onUpdateEquipSlots, _d.enter = true, _d),
            (_e = {}, _e[EventType_1.default.SMELT_EQUIP_COMPLETE] = this.onUpdateEquipSlots, _e.enter = true, _e),
            (_f = {}, _f[EventType_1.default.UPDATE_EQUIP_SLOTS] = this.onUpdateEquipSlots, _f.enter = true, _f),
            (_g = {}, _g[EventType_1.default.SMITHY_SELECT_EQUIP] = this.onSmithySelectEquip, _g.enter = true, _g),
        ];
    };
    BuildSmithyPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                this.player = this.getModel('player');
                this.unlockTipCmpt = this.unlockTipNode_.Component(BuildUnlockTipCmpt_1.default);
                return [2 /*return*/];
            });
        });
    };
    BuildSmithyPnlCtrl.prototype.onEnter = function (data, tab) {
        var _a;
        this.data = data;
        this.lockEffectConf = this.user.getLocalPreferenceDataBySid(Enums_1.PreferenceKey.LOCK_EQUIP_EFFECT_CONF) || {};
        this.showRangeTge_.isChecked = (_a = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.SHOW_EQUIP_ATTR_RANGE)) !== null && _a !== void 0 ? _a : true;
        this.isForgedEquip = false;
        this.tabsTc_.Tabs(tab !== null && tab !== void 0 ? tab : (this.user.getTempPreferenceMap(this.PKEY_TAB) || 0));
        ReddotHelper_1.reddotHelper.set('can_forge_equip', this.checkCanForgeEquip());
        ReddotHelper_1.reddotHelper.set('can_study_exclusive', this.checkCanStudyExclusive());
        ReddotHelper_1.reddotHelper.set('can_forge_exclusive', this.checkCanForgeExclusive());
        ReddotHelper_1.reddotHelper.set('can_smelt_exclusive', this.checkCanSmeltExclusive());
    };
    BuildSmithyPnlCtrl.prototype.onRemove = function () {
        this.stopPlayForgeSound();
        this.stopPlaySmelitAnim();
        this.selectSlot = null;
        this.isForgedEquip && GameHelper_1.gameHpr.checkNoticePermission(Enums_1.NoticePermissionType.PUSH);
    };
    BuildSmithyPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/tabs_tc_tce
    BuildSmithyPnlCtrl.prototype.onClickTabs = function (event, data) {
        !data && audioMgr.playSFX('click');
        var type = this.tab = Number(event.node.name);
        this.user.setTempPreferenceData(this.PKEY_TAB, type);
        var node = this.pagesNode_.Swih(type !== 0 ? 1 : 0)[0];
        if (type === 0) {
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.EQUIP_SLOT_CONF, this.key);
            ViewHelper_1.viewHelper._updateBuildBaseInfo(node.Child('info/top'), this.data, this.key);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.stopPlayForgeSound();
            this.stopPlaySmelitAnim();
        }
        else {
            this.showForgeInfo(true, node);
        }
    };
    // path://root/pages_n/0/bottom/buttons/up_be
    BuildSmithyPnlCtrl.prototype.onClickUp = function (event, data) {
        GameHelper_1.gameHpr.clickBuildUp(this.data, this);
    };
    // path://root/pages_n/1/equip/list/view/content/equip_be
    BuildSmithyPnlCtrl.prototype.onClickEquip = function (event, _) {
        var _a;
        audioMgr.playSFX('click');
        var data = event.target.Data;
        if (data) {
            if (data.isYetStudy() && data.uid === ((_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.uid)) {
                return;
            }
            if (data.isYetStudy() || this.data.lv < data.lv) {
                this.user.setTempPreferenceData(this.PKEY_SELECT_EQUIP_SLOT, data.uid);
                this.updateEquipSelect(data, false);
            }
            else if (data.selectIds.length === 0) {
                return ViewHelper_1.viewHelper.showAlert('toast.please_unlock_prev_slot', { params: ['ui.ceri_type_name_3'] });
            }
            else {
                ViewHelper_1.viewHelper.showPnl('build/StudySelect', data);
            }
        }
    };
    // path://root/pages_n/1/cond/info/need/buttons/forge_be
    BuildSmithyPnlCtrl.prototype.onClickForge = function (event, _) {
        var _this = this;
        var _a, _b;
        var data = this.selectSlot, id = data === null || data === void 0 ? void 0 : data.id;
        if (!id) {
            return;
        }
        var uid = ((_a = data.equip) === null || _a === void 0 ? void 0 : _a.uid) || data.uid;
        if (this.player.getCurrForgeEquip()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.FORGE_EQUIPING);
        }
        else if (this.player.getCurrSmeltEquip()) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.SMELTING_NO_FORGE); //融炼中无法打造
        }
        else if (!((_b = data.equip) === null || _b === void 0 ? void 0 : _b.nextForgeFree) && !GameHelper_1.gameHpr.checkCTypes(data.forgeCost) && this.player.getfreeForgeSurplusCount() <= 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.RES_NOT_ENOUGH);
        }
        else if (!GameHelper_1.gameHpr.isNoLongerTip('forge_equip') && !GameHelper_1.gameHpr.isNoviceMode) {
            return ViewHelper_1.viewHelper.showPnl('common/NoLongerTip', {
                noKey: 'forge_equip',
                content: 'ui.forge_equip_tip',
                okText: 'ui.button_gotit',
                select: true,
                ok: function () { return _this.forge(uid); },
            });
        }
        this.forge(uid);
    };
    // path://root/pages_n/1/cond/info/forge/in_done_be
    BuildSmithyPnlCtrl.prototype.onClickInDone = function (event, _) {
        var _this = this;
        var data = this.player.getCurrForgeEquip();
        if (!data) {
            return;
        }
        else if (this.user.getGold() < Constant_1.IN_DONE_FORGE_GOLD) {
            return ViewHelper_1.viewHelper.showGoldNotEnough();
        }
        var key = data.isYetForge ? 'ui.in_done_forge_tip_2_2' : 'ui.in_done_forge_tip_2_1';
        ViewHelper_1.viewHelper.showMessageBox(key, {
            params: [Constant_1.IN_DONE_FORGE_GOLD],
            ok: function () { return _this.isValid && _this.player.inDoneForge().then(function (err) {
                if (err) {
                    ViewHelper_1.viewHelper.showAlert(err);
                }
                else if (_this.isValid) {
                    _this.isForgedEquip = false;
                    if (data.id > 6100) { // 专属
                        _this.tab = 2;
                        _this.user.setTempPreferenceData(_this.PKEY_SELECT_EXCLUSIVE_SLOT, data.uid);
                    }
                    else {
                        _this.tab = 1;
                        _this.user.setTempPreferenceData(_this.PKEY_SELECT_EQUIP_SLOT, data.uid);
                    }
                    _this.tabsTc_.Swih(_this.tab);
                }
            }); },
            cancel: function () { },
        });
    };
    // path://root/pages_n/1/info/buttons/send_to_chat_be
    BuildSmithyPnlCtrl.prototype.onClickSendToChat = function (event, data) {
        var _this = this;
        var _a;
        audioMgr.playSFX('click');
        var equip = (_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.equip;
        if (equip) {
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_equip_to_chat_tip', params: [equip.getChatName()] }, function (type, childType, select) {
                if (GameHelper_1.gameHpr.chat.sendChat(type, childType, '', { equip: equip, select: select }) === 0) {
                    var target = GameHelper_1.gameHpr.chat.getTargetChat(type, childType, select);
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, target: target }).then(function () { return _this.isValid && _this.hide(); });
                }
            });
        }
    };
    // path://root/pages_n/1/cond/info/need/buttons/restore_be
    BuildSmithyPnlCtrl.prototype.onClickRestore = function (event, _) {
        var data = event.target.Data;
        if (!data) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_forge_equip');
        }
        else if (!data.restore_cost) {
            return ViewHelper_1.viewHelper.showAlert('toast.equip_no_restore');
        }
        else if (data.lastAttrs.length === 0) {
            return ViewHelper_1.viewHelper.showAlert(ECode_1.ecode.NOT_CAN_RESTORE_ATTR);
        }
        ViewHelper_1.viewHelper.showPnl('build/RestoreForge', data);
    };
    // path://root/pages_n/1/info/buttons/lock_effect_be
    BuildSmithyPnlCtrl.prototype.onClickLockEffect = function (event, _) {
        var _this = this;
        var _a, _b;
        audioMgr.playSFX('click');
        var equip = (_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.equip;
        if (!equip) {
            return;
        }
        else if (!equip.exclusive_pawn) {
            return ViewHelper_1.viewHelper.showAlert('toast.only_exclusive_equip_opt');
        }
        else if (((_b = this.player.getCurrForgeEquip()) === null || _b === void 0 ? void 0 : _b.uid) === equip.uid) { //当前重铸中 不可锁定
            return ViewHelper_1.viewHelper.showAlert('toast.recasting_no_lock_effect');
        }
        ViewHelper_1.viewHelper.showPnl('build/LockEquipEffect', equip, this.getLockEffectType(equip.uid), function (type) {
            if (_this.isValid) {
                _this.lockEffectConf[equip.uid] = type;
                _this.user.setLocalPreferenceDataBySid(Enums_1.PreferenceKey.LOCK_EQUIP_EFFECT_CONF, _this.lockEffectConf);
                _this.updateShowEquipInfo();
            }
        });
    };
    // path://root/pages_n/1/info/buttons/look_base_info_be
    BuildSmithyPnlCtrl.prototype.onClickLookBaseInfo = function (event, data) {
        var _a;
        audioMgr.playSFX('click');
        var equip = (_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.equip;
        if (equip) {
            ViewHelper_1.viewHelper.showPnl('common/EquipBaseInfoBox', equip.json, 'smithy');
        }
    };
    // path://root/pages_n/1/info/buttons/show_range_t_te
    BuildSmithyPnlCtrl.prototype.onClickShowRange = function (event, data) {
        audioMgr.playSFX('click');
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.SHOW_EQUIP_ATTR_RANGE, event.isChecked);
        this.updateShowEquipInfo();
    };
    // path://root/pages_n/1/equip/exclusive/0/smelt_nbe
    BuildSmithyPnlCtrl.prototype.onClickSmelt = function (event, data) {
        var slot = event.target.parent.parent.Data, needLv = event.target.Data;
        this.openSmeltView(slot, needLv);
    };
    // path://root/pages_n/1/equip/exclusive/0/exclusive_be
    BuildSmithyPnlCtrl.prototype.onClickExclusive = function (event, _data) {
        var _a;
        audioMgr.playSFX('click');
        var data = event.target.parent.Data;
        if (data) {
            if (data.isYetStudy() && data.uid === ((_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.uid)) {
                return;
            }
            if (data.isYetStudy() || this.data.lv < data.lv) {
                this.user.setTempPreferenceData(this.PKEY_SELECT_EXCLUSIVE_SLOT, data.uid);
                this.updateExclusiveSelect(data);
            }
            else {
                ViewHelper_1.viewHelper.showPnl('build/StudySelect', data);
            }
        }
    };
    // path://root/pages_n/1/equip/exclusive/0/smelt_nbe/0/remove_be
    BuildSmithyPnlCtrl.prototype.onClickRemove = function (event, data) {
        var slot = event.target.parent.parent.parent.Data, needLv = event.target.parent.Data;
        this.openSmeltView(slot, needLv);
    };
    // path://root/pages_n/1/info/root/view/content/attrs/view_attr_be
    BuildSmithyPnlCtrl.prototype.onClickViewAttr = function (event, data) {
        var slot = this.selectSlot;
        if (slot) {
            ViewHelper_1.viewHelper.showPnl('common/EquipBaseInfoBox', slot.json, 'book');
        }
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    BuildSmithyPnlCtrl.prototype.onUpdateBuildLv = function (data) {
        if (this.data.uid === data.uid) {
            var node = this.pagesNode_.Child(0);
            node.Child('info/top/icon/lv/val').setLocaleKey('ui.lv', data.lv);
            ViewHelper_1.viewHelper._updateBuildAttrInfo(this.data, node.Child('info/attrs'), node.Child('bottom'), this.data.getEffectsForView(), this.key);
            this.unlockTipCmpt.updateInfo(this.data, Constant_1.EQUIP_SLOT_CONF, this.key, data.lv);
        }
    };
    // 刷新装备属性
    BuildSmithyPnlCtrl.prototype.onUpdateEquipAttr = function (uid) {
        var _a;
        if (((_a = this.selectSlot) === null || _a === void 0 ? void 0 : _a.getEquipUid()) === uid) {
            this.updateShowEquipInfo();
        }
    };
    // 更新槽位信息
    BuildSmithyPnlCtrl.prototype.onUpdateEquipSlots = function () {
        this.showForgeInfo(true);
        ReddotHelper_1.reddotHelper.set('can_forge_equip', this.checkCanForgeEquip());
        ReddotHelper_1.reddotHelper.set('can_study_exclusive', this.checkCanStudyExclusive());
        ReddotHelper_1.reddotHelper.set('can_forge_exclusive', this.checkCanForgeExclusive());
        ReddotHelper_1.reddotHelper.set('can_smelt_exclusive', this.checkCanSmeltExclusive());
    };
    // 选择装备
    BuildSmithyPnlCtrl.prototype.onSmithySelectEquip = function (slot) {
        var _a;
        if (slot) {
            var isExclusive = !!((_a = slot.json) === null || _a === void 0 ? void 0 : _a.exclusive_pawn);
            var key = isExclusive ? this.PKEY_SELECT_EXCLUSIVE_SLOT : this.PKEY_SELECT_EQUIP_SLOT;
            this.user.setTempPreferenceData(key, slot.uid);
            isExclusive ? this.updateExclusiveSelect(slot) : this.updateEquipSelect(slot, true);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    // 显示普通装备信息
    BuildSmithyPnlCtrl.prototype.showForgeInfo = function (isLocation, node) {
        var _this = this;
        var _a, _b;
        node = node || this.pagesNode_.Child(1);
        var root = node.Child('equip'), isEquipTab = this.tab === 1;
        root.Child('title/bg/val').setLocaleKey(isEquipTab ? 'ui.select_equip' : 'ui.select_exclusive_equip');
        // console.log(this.selectSlot)
        var forgeUid = (_a = this.player.getCurrForgeEquip()) === null || _a === void 0 ? void 0 : _a.uid;
        var smeltUid = (_b = this.player.getCurrSmeltEquip()) === null || _b === void 0 ? void 0 : _b.uid;
        var selectSlot = null;
        var selectUid = isLocation && forgeUid ? forgeUid : this.user.getTempPreferenceMap(this.PKEY_SELECT_EQUIP_SLOT);
        if (!isEquipTab) {
            selectUid = smeltUid ? smeltUid : this.user.getTempPreferenceMap(this.PKEY_SELECT_EXCLUSIVE_SLOT);
        }
        // 刷新装备列表
        var equipSlots = isEquipTab ? this.player.getCanForgeEquips().filter(function (m) { return !Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; }) : this.player.getCanForgeEquips().filter(function (m) { return !!Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; }), buildLv = this.data.lv;
        if (isEquipTab) {
            node.Child('equip/exclusive').active = false;
            var sv = node.Child('equip/list', cc.ScrollView);
            sv.node.active = true;
            sv.stopAutoScroll();
            sv.Items(equipSlots, function (it, slot) {
                it.Data = slot;
                var lv = slot.lv, uid = slot.uid, equip = slot.equip;
                var isUnlock = buildLv >= lv, isSelect = !!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy());
                var icon = it.Child('icon');
                if (icon.active = isSelect) {
                    ResHelper_1.resHelper.loadEquipIcon(slot.id, icon, _this.key, equip === null || equip === void 0 ? void 0 : equip.getSmeltCount());
                    icon.opacity = equip ? 255 : 120;
                }
                var add = isUnlock && !isSelect, canSelect = !!(slot === null || slot === void 0 ? void 0 : slot.selectIds.length);
                it.Child('add').active = add;
                it.Child('new').active = canSelect;
                it.Child('lock').active = !isUnlock && !isSelect;
                var forging = isSelect && uid === forgeUid;
                it.Child('forge_anim').active = forging;
                it.Child('dot').active = !equip && isUnlock && isSelect && !forging;
                it.Component(cc.MultiFrame).setFrame(forging);
                // 找到选择 
                // 1.首先加号不能框选
                // 2.一个装备都没有研究时要提示点击加号选择（也就是一个都不框选）
                // 3.如果预先框选的是锁，后面锁变成了加号，要调整框选为第一个已研究的装备，没有变成加号就保持原样
                // 4.其他情况默认框选第一个已研究的装备
                if (!selectSlot && (!selectUid || selectUid === uid)) { // 默认不能框选锁，除非主动选择
                    _this.user.setTempPreferenceData(_this.PKEY_SELECT_EQUIP_SLOT, uid);
                    selectSlot = slot;
                }
                if (selectSlot && uid === selectUid && add) { // 上述第3种情况
                    selectSlot = equipSlots[0];
                }
            });
            // 刷新选择
            this.updateEquipSelect(selectSlot, isLocation, node);
        }
        else {
            this.updateExclusiveInfo(node, equipSlots, selectSlot, forgeUid, smeltUid, selectUid);
        }
    };
    // 专属装备
    BuildSmithyPnlCtrl.prototype.updateExclusiveInfo = function (node, slots, selectSlot, forgeUid, smeltUid, selectUid) {
        var _this = this;
        node.Child('equip/list').active = false;
        var exclusive = node.Child('equip/exclusive');
        exclusive.active = true;
        exclusive.children.forEach(function (it, i) {
            var slot = it.Data = slots[i];
            var lv = slot.lv, uid = slot.uid, equip = slot.equip;
            var isUnlock = _this.data.lv >= lv, isSelect = !!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy());
            var add = isUnlock && !isSelect;
            var item = it.Child('exclusive_be');
            item.Child('add').active = item.Child('new').active = add;
            item.Child('lock').active = !isUnlock && !isSelect;
            var forging = isSelect && uid === forgeUid, smelting = isSelect && uid === smeltUid;
            item.Child('forge_anim').active = forging;
            item.Child('smelt_anim').active = smelting;
            item.Child('dot').active = !equip && isUnlock && isSelect && !forging;
            item.Component(cc.MultiFrame).setFrame(forging ? 2 : smelting ? 1 : 0);
            var icon = item.Child('icon');
            if (icon.active = isSelect) {
                ResHelper_1.resHelper.loadEquipIcon(slot.id, icon, _this.key, equip === null || equip === void 0 ? void 0 : equip.getSmeltCount());
                icon.opacity = equip ? 255 : 120;
            }
            // 副装备
            var smeltNode = it.Child('smelt_nbe');
            smeltNode.children.forEach(function (it, i) {
                var _a;
                it.Data = Constant_1.EQUIP_SMELT_NEED_LV[i];
                var equipId = slot.id, viceId = (_a = equip === null || equip === void 0 ? void 0 : equip.smeltEffects[i]) === null || _a === void 0 ? void 0 : _a.id;
                it.Child('remove_be').active = !!viceId;
                var unlock = _this.data.lv >= Constant_1.EQUIP_SMELT_NEED_LV[i] && !!equipId;
                it.Child('dot').active = !viceId && unlock;
                it.Component(cc.MultiFrame).setFrame(unlock);
                var add = it.Child('add');
                if (add.active = !viceId) {
                    add.Color(unlock ? '#C3A587' : '#B1AAA2');
                }
                var icon = it.Child('icon', cc.Sprite);
                if (viceId) {
                    ResHelper_1.resHelper.loadEquipIcon(viceId, icon, _this.key);
                }
                else {
                    icon.spriteFrame = null;
                }
            });
            // 找到选择
            if (!selectSlot && (!selectUid || uid === selectUid)) {
                _this.user.setTempPreferenceData(_this.PKEY_SELECT_EXCLUSIVE_SLOT, uid);
                selectSlot = slot;
            }
            if (selectSlot && uid === selectUid && add) { // 上述第3种情况
                selectSlot = slots[0];
            }
        });
        // 刷新选择
        this.updateExclusiveSelect(selectSlot, node);
    };
    // 刷新装备选择
    BuildSmithyPnlCtrl.prototype.updateEquipSelect = function (slot, isLocation, node) {
        node = node || this.pagesNode_.Child(1);
        this.selectSlot = slot;
        var isUnlock = this.data.lv >= ((slot === null || slot === void 0 ? void 0 : slot.lv) || 1), isSelect = !!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy());
        var uid = slot === null || slot === void 0 ? void 0 : slot.uid, selectIndex = -1;
        var sv = node.Child('equip/list', cc.ScrollView);
        sv.content.children.forEach(function (m, i) {
            var _a;
            var select = m.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid && !(isUnlock && !isSelect);
            if (select) {
                selectIndex = i;
            }
        });
        if (isLocation) {
            sv.SelectItemToCentre(selectIndex);
        }
        // 显示装备信息
        this.updateShowEquipInfo(node);
    };
    // 刷新显示的装备信息
    BuildSmithyPnlCtrl.prototype.updateShowEquipInfo = function (node) {
        node = node || this.pagesNode_.Child(1);
        var slot = this.selectSlot;
        var sv = node.Child('info/root', cc.ScrollView);
        sv.stopAutoScroll();
        sv.content.y = 0;
        var lv = (slot === null || slot === void 0 ? void 0 : slot.lv) || (this.tab === 1 ? 1 : 10);
        var info = sv.content, isUnlock = this.data.lv >= lv, isSelect = !!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy()), equip = slot === null || slot === void 0 ? void 0 : slot.equip;
        // 分享锁定按钮
        var isExclusive = node.Child('info/buttons/lock_effect_be').active = node.Child('info/buttons/look_base_info_be').active = !!(equip === null || equip === void 0 ? void 0 : equip.exclusive_pawn);
        this.showRangeTge_.node.active = !!equip && !isExclusive;
        node.Child('info/buttons/send_to_chat_be').active = !!equip && !GameHelper_1.gameHpr.isNoviceMode;
        this.showRangeTge_.node.active = !!equip && !isExclusive;
        // 是否解锁
        var state = node.Child('info/state'), forgeEquip = this.player.getCurrForgeEquip(), smeltEquip = this.player.getCurrSmeltEquip();
        if (state.active = (!isSelect && !forgeEquip && !smeltEquip)) { // 打造以及融炼过程中不显示
            var tip = state.Swih(isUnlock ? 'tip' : 'lock')[0], isExclusiveSlot = Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[lv];
            if (!isUnlock) {
                if (isExclusiveSlot) {
                    tip.Child('val').setLocaleKey('ui.need_lv_unlock_exclusive', lv);
                }
                else {
                    tip.Child('val').setLocaleKey('ui.lv_unlock_new', [assetsMgr.lang('ui.short_lv', lv), 'ui.ceri_type_name_3']);
                }
            }
        }
        // 属性
        info.Child('exclusive').active = info.Child('icon').active = info.Child('attrs').active = isSelect;
        if (!isSelect) {
        }
        else if (equip) {
            ViewHelper_1.viewHelper.updateEquipView(info, equip, this.key, this.getLockEffectType(equip.uid), equip.smeltEffects, !isExclusive && this.showRangeTge_.isChecked);
        }
        else if (slot.json) {
            ViewHelper_1.viewHelper.updateEquipBaseView(info, slot.json, 'build');
        }
        // 刷新打造信息
        this.updateForgeCond(node);
    };
    // 刷新打造条件
    BuildSmithyPnlCtrl.prototype.updateForgeCond = function (node) {
        node = node || this.pagesNode_.Child(1);
        var slot = this.selectSlot;
        var root = node.Child('cond');
        root.Child('title/iron/val', cc.Label).string = this.player.getIron() + '';
        var forgeEquip = this.player.getCurrForgeEquip(), smeltEquip = this.player.getCurrSmeltEquip();
        // 打造中
        if (forgeEquip) {
            root.Child('title').Swih('none', true);
            root.Child('title/bg/val').setLocaleKey(forgeEquip.isYetForge ? 'ui.forge_desc_2' : 'ui.forge_desc_1');
            var forge = root.Child('info').Swih('forge')[0];
            forge.Child('time/val', cc.LabelTimer).run(forgeEquip.getSurplusTime() * 0.001);
            ResHelper_1.resHelper.loadEquipIcon(forgeEquip.id, forge.Child('lu/icon'), this.key, forgeEquip.getSmeltCount());
            ResHelper_1.resHelper.loadEquipIcon(forgeEquip.id, forge.Child('lu/icon/val'), this.key);
            if (!this.currPlayForgeSFX) {
                this.currPlayForgeSFX = true;
                forge.Child('lu', cc.Animation).play('forge', 0);
            }
            var buttonNode = forge.Child('in_done_be');
            var isFreeForge = forgeEquip.needTime <= 3000;
            buttonNode.opacity = isFreeForge ? 120 : 255;
            buttonNode.Component(cc.Button).interactable = !isFreeForge;
        }
        else {
            this.stopPlayForgeSound();
        }
        // 融炼中
        this.stopPlaySmelitAnim();
        if (!forgeEquip && !!smeltEquip) {
            root.Child('title').Swih('none', true);
            root.Child('title/bg/val').setLocaleKey('ui.in_smelting');
            var smelt = root.Child('info').Swih('smelt')[0];
            smelt.Child('time/val', cc.LabelTimer).run(smeltEquip.getSurplusTime() * 0.001);
            this.beginPlaySmelt(smelt, smeltEquip);
        }
        // 打造费用
        if (!forgeEquip && !smeltEquip) {
            if (!!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy())) {
                var equip = slot === null || slot === void 0 ? void 0 : slot.equip, type = equip ? 2 : 1;
                root.Child('title').Swih('none', true);
                root.Child('title/bg/val').setLocaleKey('ui.forge_cost_' + type);
                var need = root.Child('info').Swih('need')[0];
                // 费用
                var fixator = (this.getLockEffectType(equip === null || equip === void 0 ? void 0 : equip.uid) ? 1 : 0) + this.getSmeltNeedFixatorCount(equip);
                var cost = fixator ? slot.forgeCost.concat(new CTypeObj_1.default().init(Enums_1.CType.FIXATOR, 0, fixator)) : slot.forgeCost;
                var policyFreeCount = this.player.getfreeForgeSurplusCount();
                var isFree = !!(equip === null || equip === void 0 ? void 0 : equip.nextForgeFree), recastCount = (equip === null || equip === void 0 ? void 0 : equip.recastCount) || 0;
                var time = slot.forgeTime;
                var cd = this.data.getEffectValue(Enums_1.CEffect.FORGE_CD) * 0.01;
                if (GameHelper_1.gameHpr.isNoviceMode) {
                    cd = GameHelper_1.gameHpr.noviceServer.getForgeSpeedTime(time).cd;
                }
                ViewHelper_1.viewHelper.updateFreeCostView(need, cost, time, cd, isFree, policyFreeCount);
                need.Child('buttons/forge_be/root/val').setLocaleKey((isFree || policyFreeCount > 0) ? 'ui.button_forge_free' : 'ui.button_forge_' + type);
                // 重铸次数
                var recastNode = need.Child('buttons/forge_be/recast');
                if (recastNode.active = !!recastCount) {
                    recastNode.setLocaleKey('ui.recast_count', recastCount);
                }
                // 还原
                var restore = need.Child('buttons/restore_be');
                restore.Data = equip;
                restore.opacity = !!(equip === null || equip === void 0 ? void 0 : equip.lastAttrs.length) && !!(equip === null || equip === void 0 ? void 0 : equip.restore_cost) ? 255 : 120;
            }
            else {
                root.Child('title').Swih('none');
                root.Child('info').Swih('');
            }
        }
    };
    // 刷新专属装备选择
    BuildSmithyPnlCtrl.prototype.updateExclusiveSelect = function (slot, node) {
        node = node || this.pagesNode_.Child(1);
        this.selectSlot = slot;
        var isUnlock = this.data.lv >= ((slot === null || slot === void 0 ? void 0 : slot.lv) || 10), isSelect = !!(slot === null || slot === void 0 ? void 0 : slot.isYetStudy());
        var uid = slot === null || slot === void 0 ? void 0 : slot.uid;
        var exclusive = node.Child('equip/exclusive');
        exclusive.children.forEach(function (m, i) {
            var _a;
            var target = m.Child('exclusive_be');
            target.Child('select').active = ((_a = m.Data) === null || _a === void 0 ? void 0 : _a.uid) === uid && !(isUnlock && !isSelect);
        });
        // 显示装备信息
        this.updateShowEquipInfo(node);
    };
    // 打开融炼界面
    BuildSmithyPnlCtrl.prototype.openSmeltView = function (slot, needLv) {
        var _this = this;
        var equip = slot === null || slot === void 0 ? void 0 : slot.equip;
        if (this.data.lv < needLv) {
            return ViewHelper_1.viewHelper.showAlert('toast.unlock_smelt_tip', { params: [needLv] });
        }
        if (!equip || !(slot === null || slot === void 0 ? void 0 : slot.isYetStudy()) || !slot.isYetStudy()) {
            return ViewHelper_1.viewHelper.showAlert('toast.please_forge_exclusive');
        }
        if (!(equip === null || equip === void 0 ? void 0 : equip.isExclusive()) || this.player.getCurrForgeEquip() || this.player.getCurrSmeltEquip()) {
            return;
        }
        ViewHelper_1.viewHelper.showPnl('build/SelectSmeltEquip', equip, function (mainUid, ids) {
            if (!_this.isValid || !mainUid) {
            }
            else if (ids.length > 0) {
                _this.smelt(mainUid, ids);
            }
            else {
                _this.restoreSmelt(mainUid);
            }
        });
    };
    // 获取需要消耗钉子的数量 根据熔炼是否融了已经有的效果
    BuildSmithyPnlCtrl.prototype.getSmeltNeedFixatorCount = function (equip) {
        if (!(equip === null || equip === void 0 ? void 0 : equip.isExclusive())) {
            return 0;
        }
        var effectIdMap = {};
        GameHelper_1.gameHpr.world.getExclusiveEquipEffects(equip.id).forEach(function (m) { return effectIdMap[m] = true; });
        return equip.smeltEffects.filter(function (m) { return !!effectIdMap[m.type]; }).length;
    };
    // 是否有可打造的普通装备
    BuildSmithyPnlCtrl.prototype.checkCanForgeEquip = function () {
        var _this = this;
        var slots = this.player.getCanForgeEquips().filter(function (m) { return !Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; });
        return slots.some(function (m) { var _a; return _this.data.lv >= (m === null || m === void 0 ? void 0 : m.lv) && !(m === null || m === void 0 ? void 0 : m.equip) && !!(m === null || m === void 0 ? void 0 : m.isYetStudy()) && ((_a = _this.player.getCurrForgeEquip()) === null || _a === void 0 ? void 0 : _a.id) !== m.id; });
    };
    // 是否有可研究的专属
    BuildSmithyPnlCtrl.prototype.checkCanStudyExclusive = function () {
        var _this = this;
        var slots = this.player.getCanForgeEquips().filter(function (m) { return !!Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; });
        return slots.some(function (m) { return _this.data.lv >= (m === null || m === void 0 ? void 0 : m.lv) && !(m === null || m === void 0 ? void 0 : m.isYetStudy()); });
    };
    // 是否有可打造的专属
    BuildSmithyPnlCtrl.prototype.checkCanForgeExclusive = function () {
        var _this = this;
        var slots = this.player.getCanForgeEquips().filter(function (m) { return !!Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; });
        return slots.some(function (m) { var _a; return _this.data.lv >= (m === null || m === void 0 ? void 0 : m.lv) && !(m === null || m === void 0 ? void 0 : m.equip) && !!(m === null || m === void 0 ? void 0 : m.isYetStudy()) && ((_a = _this.player.getCurrForgeEquip()) === null || _a === void 0 ? void 0 : _a.id) !== m.id; });
    };
    // 是否有可融炼的专属
    BuildSmithyPnlCtrl.prototype.checkCanSmeltExclusive = function () {
        var _this = this;
        var slots = this.player.getCanForgeEquips().filter(function (m) { return !!Constant_1.EQUIP_SLOT_EXCLUSIVE_LV[m.lv]; });
        return slots.some(function (m, i) { return _this.data.lv >= Constant_1.EQUIP_SMELT_NEED_LV[i] && !!(m === null || m === void 0 ? void 0 : m.isYetStudy()) && !!(m === null || m === void 0 ? void 0 : m.equip) && !(m === null || m === void 0 ? void 0 : m.equip.smeltEffects.some(function (n) { return !!n.id; })); });
    };
    BuildSmithyPnlCtrl.prototype.stopPlayForgeSound = function () {
        this.currPlayForgeSFX = false;
        audioMgr.stopSFX('build/sound_ui_008', 'forge');
    };
    // 开始播放融炼
    BuildSmithyPnlCtrl.prototype.beginPlaySmelt = function (root, data) {
        var _this = this;
        var equipAnim = root.Child('lu/equip_anim', cc.Animation), effect = root.Child('lu/effect'), luAnim = root.Child('lu/anim', cc.Animation);
        // 赋值
        ResHelper_1.resHelper.loadEquipIcon(data.id, effect.Child('equip'), this.key);
        equipAnim.node.children.forEach(function (m, i) {
            var id = data.viceIds[i];
            if (m.active = !!id) {
                ResHelper_1.resHelper.loadEquipIcon(id, m, _this.key);
            }
        });
        // 播放动画
        this.playSmelitAnim(data.getSurplusTime() * 0.001, luAnim, effect, equipAnim);
    };
    // 动起来
    BuildSmithyPnlCtrl.prototype.playSmelitAnim = function (time, luAnim, effect, equipAnim) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 为了和服务器一致多加1秒
                        time += 1;
                        if (!(time - 1.8 > 0)) return [3 /*break*/, 2];
                        audioMgr.playSFX('build/sound_ui_011', { loop: true, tag: 'smelit_begin' });
                        luAnim.play('smelt_lu_begin');
                        equipAnim.play('smelt_equip_begin');
                        effect.Child('equip', cc.Animation).play();
                        return [4 /*yield*/, ut.wait(time - 1.8, this)];
                    case 1:
                        _a.sent();
                        time = 1.8;
                        _a.label = 2;
                    case 2:
                        time = 1.8 - time;
                        audioMgr.stopSFX('build/sound_ui_011', 'smelit_begin');
                        if (!(time < 0.72)) return [3 /*break*/, 4];
                        audioMgr.playSFX('build/sound_ui_012', { startTime: time, tag: 'smelit_end' });
                        return [4 /*yield*/, ut.wait(0.45)]; // luAnim.playAsync('smelt_lu_end', time)
                    case 3:
                        _a.sent(); // luAnim.playAsync('smelt_lu_end', time)
                        time = 0.72;
                        _a.label = 4;
                    case 4:
                        equipAnim.stop();
                        equipAnim.node.children.forEach(function (m) { return m.Component(cc.Sprite).spriteFrame = null; });
                        luAnim.stop();
                        luAnim.Component(cc.MultiFrame).setFrame(0);
                        time = time - 0.72;
                        if (!(time < 0.5)) return [3 /*break*/, 6];
                        effect.Child('equip', cc.Animation).stop();
                        effect.Child('equip').y = 0;
                        effect.Child('anim').active = true;
                        return [4 /*yield*/, effect.Child('anim', cc.Animation).playAsync('smelt_lu_effect', time)];
                    case 5:
                        _a.sent();
                        time = 0.5;
                        _a.label = 6;
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    BuildSmithyPnlCtrl.prototype.stopPlaySmelitAnim = function () {
        audioMgr.stopSFX('build/sound_ui_011', 'smelit_begin');
        audioMgr.stopSFX('build/sound_ui_012', 'smelit_end');
        var root = this.pagesNode_.Child('1/cond/info/smelt');
        var equipAnim = root.Child('lu/equip_anim', cc.Animation), effect = root.Child('lu/effect'), luAnim = root.Child('lu/anim', cc.Animation);
        luAnim.stop();
        luAnim.Component(cc.MultiFrame).setFrame(0);
        effect.Child('equip', cc.Animation).stop();
        effect.Child('anim', cc.Animation).stop();
        effect.Child('anim').active = false;
        equipAnim.stop();
        equipAnim.node.children.forEach(function (m) { return m.Component(cc.Sprite).spriteFrame = null; });
    };
    // 刷新打造动画
    BuildSmithyPnlCtrl.prototype.updateForgeAnim = function (node) {
        var _a, _b;
        node = node || this.pagesNode_.Child(1);
        var forgeId = ((_a = this.player.getCurrForgeEquip()) === null || _a === void 0 ? void 0 : _a.uid) || -1;
        if (this.tab === 1) { // 普通装备
            node.Child('equip/list/view/content').children.forEach(function (m) {
                var data = m.Data;
                var forging = (data === null || data === void 0 ? void 0 : data.uid) === forgeId;
                m.Child('forge_anim').active = forging;
                m.Component(cc.MultiFrame).setFrame(forging);
                m.Child('icon').opacity = (data === null || data === void 0 ? void 0 : data.equip) ? 255 : 120;
                if (m.Child('dot').active) {
                    m.Child('dot').active = !forging;
                }
            });
        }
        else if (this.tab == 2) { // 专属
            var smeltId_1 = (_b = this.player.getCurrSmeltEquip()) === null || _b === void 0 ? void 0 : _b.uid;
            node.Child('equip/exclusive').children.forEach(function (m) {
                var data = m.Data, item = m.Child('exclusive_be');
                var forging = (data === null || data === void 0 ? void 0 : data.uid) === forgeId, smelting = (data === null || data === void 0 ? void 0 : data.uid) === smeltId_1;
                item.Component(cc.MultiFrame).setFrame(forging ? 2 : smelting ? 1 : 0);
                item.Child('forge_anim').active = forging;
                item.Child('smelt_anim').active = smelting;
                item.Child('icon').opacity = (data === null || data === void 0 ? void 0 : data.equip) ? 255 : 120;
                if (item.Child('dot').active) {
                    item.Child('dot').active = !forging;
                }
            });
        }
    };
    BuildSmithyPnlCtrl.prototype.getLockEffectType = function (uid) {
        var _a, _b;
        return (_b = (_a = this.lockEffectConf) === null || _a === void 0 ? void 0 : _a[uid]) !== null && _b !== void 0 ? _b : 0;
    };
    // 打造
    BuildSmithyPnlCtrl.prototype.forge = function (uid) {
        var _this = this;
        this.player.forgeEquip(uid, this.getLockEffectType(uid)).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                var node = _this.pagesNode_.Child(1);
                _this.updateForgeCond(node);
                _this.updateForgeAnim(node);
                _this.isForgedEquip = true;
                ReddotHelper_1.reddotHelper.set('can_forge_equip', _this.checkCanForgeEquip());
                ReddotHelper_1.reddotHelper.set('can_forge_exclusive', _this.checkCanForgeExclusive());
            }
        });
    };
    // 融炼
    BuildSmithyPnlCtrl.prototype.smelt = function (mainUid, ids) {
        var _this = this;
        this.player.smeltEquip(mainUid, ids).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                var node = _this.pagesNode_.Child(1);
                _this.updateForgeCond(node);
                _this.updateForgeAnim(node);
                // this.isForgedEquip = true
                ReddotHelper_1.reddotHelper.set('can_smelt_exclusive', _this.checkCanSmeltExclusive());
            }
        });
    };
    // 还原融炼
    BuildSmithyPnlCtrl.prototype.restoreSmelt = function (mainUid) {
        var _this = this;
        this.player.restoreSmeltEquip(mainUid).then(function (err) {
            if (err) {
                return ViewHelper_1.viewHelper.showAlert(err);
            }
            else if (_this.isValid) {
                var node = _this.pagesNode_.Child(1);
                _this.showForgeInfo(false, node);
                _this.updateForgeAnim(node);
                ReddotHelper_1.reddotHelper.set('can_smelt_exclusive', _this.checkCanSmeltExclusive());
            }
        });
    };
    BuildSmithyPnlCtrl = __decorate([
        ccclass
    ], BuildSmithyPnlCtrl);
    return BuildSmithyPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = BuildSmithyPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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