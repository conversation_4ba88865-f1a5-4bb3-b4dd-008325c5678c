
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/menu/SelectHeadIconPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '02d65tnpXBJwbu4JRsKTWjg', 'SelectHeadIconPnlCtrl');
// app/script/view/menu/SelectHeadIconPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ccclass = cc._decorator.ccclass;
var SelectHeadIconPnlCtrl = /** @class */ (function (_super) {
    __extends(SelectHeadIconPnlCtrl, _super);
    function SelectHeadIconPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.listSv_ = null; // path://root/list_sv
        //@end
        _this.user = null;
        _this.cb = null;
        _this.selectHeadIcon = '';
        _this.selectNode = null;
        return _this;
        //@end
        // ----------------------------------------- event listener function --------------------------------------------
        // ----------------------------------------- custom function ----------------------------------------------------
    }
    SelectHeadIconPnlCtrl.prototype.listenEventMaps = function () {
        return [];
    };
    SelectHeadIconPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    SelectHeadIconPnlCtrl.prototype.onEnter = function (cb) {
        var _this = this;
        this.cb = cb;
        this.selectHeadIcon = this.user.getHeadIcon();
        var list = Constant_1.FREE_HEAD_ICONS.concat(this.user.getUnlockHeadIcons()).reverse();
        this.listSv_.List(list.length, function (it, i) {
            var headIcon = it.Data = list[i];
            ResHelper_1.resHelper.loadPlayerHead(it.Child('val'), headIcon, _this.key);
            if (it.Child('select').active = headIcon === _this.selectHeadIcon) {
                _this.selectNode = it;
            }
        });
    };
    SelectHeadIconPnlCtrl.prototype.onRemove = function () {
        this.cb = null;
    };
    SelectHeadIconPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://root/list_sv/view/content/item_be
    SelectHeadIconPnlCtrl.prototype.onClickItem = function (event, _) {
        var _a, _b;
        audioMgr.playSFX('click');
        var it = event.target;
        if (it.uuid !== ((_a = this.selectNode) === null || _a === void 0 ? void 0 : _a.uuid)) {
            it.Child('select').active = true;
            (_b = this.selectNode) === null || _b === void 0 ? void 0 : _b.Child('select').setActive(false);
            this.selectNode = it;
            this.selectHeadIcon = it.Data;
        }
    };
    // path://root/ok_be
    SelectHeadIconPnlCtrl.prototype.onClickOk = function (event, data) {
        this.cb && this.cb(this.selectHeadIcon);
        this.hide();
    };
    SelectHeadIconPnlCtrl = __decorate([
        ccclass
    ], SelectHeadIconPnlCtrl);
    return SelectHeadIconPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = SelectHeadIconPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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