
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/model/snailisle/SnailIsleModel.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8abf6plUulEd7AGu/1vAw3Z', 'SnailIsleModel');
// app/script/model/snailisle/SnailIsleModel.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var Enums_1 = require("../../common/constant/Enums");
var BaseMapModel_1 = require("./BaseMapModel");
var BuildEnums_1 = require("./BuildEnums");
var SIConstant_1 = require("./SIConstant");
var MapSceneHelper_1 = require("./MapSceneHelper");
var RoleObj_1 = require("./RoleObj");
var SBuildObj_1 = require("./SBuildObj");
/**
 * 蜗牛岛
 */
var SnailIsleModel = /** @class */ (function (_super) {
    __extends(SnailIsleModel, _super);
    function SnailIsleModel() {
        var _this = _super.call(this, 'snailIsle') || this;
        _this.strollPoints = [cc.v2(0, 1), cc.v2(0, 2), cc.v2(0, 3), cc.v2(0, 4), cc.v2(0, 5), cc.v2(0, 6), cc.v2(1, 0), cc.v2(2, 0), cc.v2(3, 0), cc.v2(4, 0), cc.v2(5, 0), cc.v2(7, 4)];
        _this.builds = [];
        _this.collisionBuilds = []; //需要碰撞的设施
        _this.tempCollisionPoints = {}; //参与碰撞的点 除开墙以外的 用于astar检测
        _this.tempRecordSeatIndex = {}; //临时记录人的位置下标
        _this.tempRecordRoleUseBuildData = {}; //记录人当前使用的设施数据
        _this.restRandomTime = [10, 20]; //休息随机时间
        _this.restTime = 0; //休息时间
        _this.workTime = 0; //工作时间
        _super.prototype.init.call(_this, Enums_1.MapSceneType.SNAIL_ISLE);
        _this.builds = assetsMgr.getJson('_snailIsle_build').datas.map(function (m) { return new SBuildObj_1.default().init(m); });
        // 更新层级
        _this.updateCollisionBuilds();
        return _this;
    }
    SnailIsleModel.prototype.create = function () {
        var points = this.groundPoints.slice();
        this.enter(Enums_1.MapRoleType.DXF, points.randomRemove());
        this.enter(Enums_1.MapRoleType.YMZ, points.randomRemove());
        this.enter(Enums_1.MapRoleType.HZL, points.randomRemove());
        this.enter(Enums_1.MapRoleType.DJY, points.randomRemove());
        this.resetRestTime();
        return this;
    };
    // 重置工作时间
    SnailIsleModel.prototype.resetRestTime = function () {
        this.restTime = ut.random(this.restRandomTime[0], this.restRandomTime[1]);
    };
    SnailIsleModel.prototype.update = function (dt) {
        _super.prototype.update.call(this, dt);
        // 刷新休息时间
        if (this.restTime > 0) {
            this.restTime -= dt;
            if (this.restTime <= 0) {
                this.workTime = SIConstant_1.ROLE_WORK_TIME + 10;
                this.roles.forEach(function (m) { return m.readyWork(); });
            }
        }
        // 刷新工作时间
        if (this.workTime > 0) {
            this.workTime -= dt;
            if (this.workTime <= 0) {
                this.resetRestTime();
            }
        }
    };
    SnailIsleModel.prototype.roleBow = function () {
        if ((this.workTime > 0 && this.workTime < 10) || (this.restTime > 0 && this.restTime < 5)) {
            this.restTime = 3;
        }
        else {
            this.resetRestTime();
        }
        this.workTime = 0;
        this.roles.forEach(function (m) { return m.readyBow(); });
    };
    SnailIsleModel.prototype.getBuilds = function () { return this.builds; };
    // 获取一个设施
    SnailIsleModel.prototype.getBuildById = function (id) {
        return this.builds.find(function (m) { return m.id === id; });
    };
    // 刷新地面空着的点
    SnailIsleModel.prototype.updateGroundPoints = function () {
        var _this = this;
        this.groundPoints.length = 0;
        this.mapPoints.forEach(function (m) {
            if (_this.checkCanPass(m.x, m.y)) {
                _this.groundPoints.push(m);
            }
        });
    };
    SnailIsleModel.prototype.getCanMovePoints = function () {
        return this.groundPoints;
    };
    SnailIsleModel.prototype.getStrollPoint = function (role) {
        var point = this.strollPoints.randomRemove();
        if (role.lastStrollPoint) {
            this.strollPoints.push(role.lastStrollPoint);
        }
        role.lastStrollPoint = point;
        return point;
    };
    // 获取工作的位置
    SnailIsleModel.prototype.getWorkPoint = function (role) {
        if (role.id === Enums_1.MapRoleType.DXF) {
            return cc.v2(-24, 45.5);
        }
        else if (role.id === Enums_1.MapRoleType.YMZ) { //明正 电脑办公
            return cc.v2(-5, 60.5);
        }
        else if (role.id === Enums_1.MapRoleType.HZL) { //宗林 沙发办公
            return cc.v2(5, 35.5);
        }
        else if (role.id === Enums_1.MapRoleType.DJY) { //景元 电脑办公
            return cc.v2(11, 52.5);
        }
        return null;
    };
    // ----------------------------------------- 公共方法 --------------------------------------------
    // 获取记录的位置
    SnailIsleModel.prototype.getRecordSeatPoint = function (uid) {
        var it = this.tempRecordSeatIndex[uid];
        return it ? it.i : undefined;
    };
    // 记录位置
    SnailIsleModel.prototype.recordSeatPoint = function (role, indexs) {
        var i = -1;
        if (indexs.length > 0) {
            i = indexs.remove(indexs.random());
            this.tempRecordSeatIndex[role.uid] = { indexs: indexs, i: i };
        }
        return i;
    };
    // 离开位置
    SnailIsleModel.prototype.leaveSeatPoint = function (role) {
        var it = this.tempRecordSeatIndex[role.uid];
        if (it) {
            it.indexs.push(it.i);
            delete this.tempRecordSeatIndex[role.uid];
        }
    };
    // 使用家具开始
    SnailIsleModel.prototype.useBuildBegin = function (role) {
        if (role.id === Enums_1.MapRoleType.HZL) { //宗林 沙发
        }
        else if (role.id === Enums_1.MapRoleType.YMZ) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER1);
            build.anim = build.use_begin_anim;
            role.dir = 1;
        }
        else if (role.id === Enums_1.MapRoleType.DJY) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER2);
            build.anim = build.use_begin_anim;
            role.dir = 1;
        }
        else if (role.id === Enums_1.MapRoleType.DXF) {
            role.dir = 1;
        }
    };
    // 使用中
    SnailIsleModel.prototype.useBuildRun = function (role) {
        if (role.id === Enums_1.MapRoleType.HZL) { //宗林 沙发
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.SOFA);
            build.anim = build.use_run_anim;
            role.zIndex = build.zIndex + 1;
            role.noUpdateZindex = true;
            role.dir = 1;
        }
        else if (role.id === Enums_1.MapRoleType.YMZ) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER1);
            build.anim = build.use_run_anim;
            role.dir = 1;
        }
        else if (role.id === Enums_1.MapRoleType.DJY) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER2);
            build.anim = build.use_run_anim;
            role.dir = 1;
        }
        else if (role.id === Enums_1.MapRoleType.DXF) {
        }
    };
    // 使用家具结束
    SnailIsleModel.prototype.useBuildEnd = function (role) {
        if (role.id === Enums_1.MapRoleType.HZL) { //宗林 沙发
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.SOFA);
            build.anim = build.use_end_anim;
            role.noUpdateZindex = false;
            this.setRoleZindex(role);
        }
        else if (role.id === Enums_1.MapRoleType.YMZ) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER1);
            build.anim = build.use_end_anim;
        }
        else if (role.id === Enums_1.MapRoleType.DJY) {
            var build = this.getBuildById(BuildEnums_1.SnailisleBuildType.COMPUTER2);
            build.anim = build.use_end_anim;
        }
        else if (role.id === Enums_1.MapRoleType.DXF) {
        }
    };
    // 获取客人使用的设施信息
    SnailIsleModel.prototype.getRoleUseBuildData = function (uid) {
        return this.tempRecordRoleUseBuildData[uid];
    };
    // 这个设施是否在使用中
    SnailIsleModel.prototype.isRoleUseBuildByType = function (type) {
        for (var uid in this.tempRecordRoleUseBuildData) {
            if (this.tempRecordRoleUseBuildData[uid].type === type) {
                return true;
            }
        }
        return false;
    };
    // ----------------------------------------- 重写方法 --------------------------------------------
    // 刷新碰撞的设施
    SnailIsleModel.prototype.updateCollisionBuilds = function () {
        var _this = this;
        this.tempCollisionPoints = {};
        this.collisionBuilds.length = 0;
        this.builds.forEach(function (m) {
            _this.collisionBuilds.push(m);
            m.points.forEach(function (p) { return _this.tempCollisionPoints[p.Join('_')] = true; });
        });
        MapSceneHelper_1.mapSceneHelper.sortMapObjZindex(this.collisionBuilds.slice());
        // 重新设置一次
        this.roles.forEach(function (m) { return _this.setRoleZindex(m, true); });
        // 刷新地面点
        this.updateGroundPoints();
    };
    // 第二位
    SnailIsleModel.prototype.updateRoleZindex = function (role, point) {
        var zIndex = -30000;
        for (var i = this.collisionBuilds.length - 1; i >= 0; i--) {
            var m = this.collisionBuilds[i];
            if (m.points.some(function (p) { return p.x >= point.x && p.y >= point.y; }) && m.zIndex > zIndex) {
                zIndex = m.zIndex;
            }
        }
        role.checkZindex = zIndex + Math.max(100 - (point.x + point.y), 0) * 10;
    };
    // 获取设施root位置
    SnailIsleModel.prototype.getBuildRootPositionByType = function (id) {
        var build = this.builds.find(function (m) { return m.id === id; });
        if (build.rootPosition) {
            return build.rootPosition;
        }
        return this.getActPixelByPoint(build.point).clone();
    };
    // 是否可通过
    SnailIsleModel.prototype.checkCanPass = function (x, y) {
        var ok1 = _super.prototype.checkCanPass.call(this, x, y);
        var ok2 = !this.tempCollisionPoints[x + '_' + y];
        return ok1 && ok2;
    };
    // 进入这个场景
    SnailIsleModel.prototype.enter = function (type, point) {
        var role = new RoleObj_1.default(type).init();
        this.createRole(role, point);
        role.run();
    };
    return SnailIsleModel;
}(BaseMapModel_1.default));
exports.default = SnailIsleModel;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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