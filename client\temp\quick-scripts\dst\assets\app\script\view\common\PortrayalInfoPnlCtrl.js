
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/app/script/view/common/PortrayalInfoPnlCtrl.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'dbf9dvENJdCdbC53Df6/7nf', 'PortrayalInfoPnlCtrl');
// app/script/view/common/PortrayalInfoPnlCtrl.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var Constant_1 = require("../../common/constant/Constant");
var ECode_1 = require("../../common/constant/ECode");
var Enums_1 = require("../../common/constant/Enums");
var EventType_1 = require("../../common/event/EventType");
var GameHelper_1 = require("../../common/helper/GameHelper");
var NetHelper_1 = require("../../common/helper/NetHelper");
var ResHelper_1 = require("../../common/helper/ResHelper");
var ViewHelper_1 = require("../../common/helper/ViewHelper");
var FrameAnimationCmpt_1 = require("../cmpt/FrameAnimationCmpt");
var ccclass = cc._decorator.ccclass;
var PortrayalInfoPnlCtrl = /** @class */ (function (_super) {
    __extends(PortrayalInfoPnlCtrl, _super);
    function PortrayalInfoPnlCtrl() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        //@autocode property begin
        _this.pictureNode_ = null; // path://picture_n
        _this.nameNode_ = null; // path://name/name_n
        _this.roootNode_ = null; // path://rooot_n
        _this.showRangeTge_ = null; // path://rooot_n/attrs/0/show_range_t_te
        _this.lockStrategyNode_ = null; // path://rooot_n/attrs/strategy/lock_strategy_be_n
        _this.saveSchemeNode_ = null; // path://save_scheme_be_n
        _this.recompCountNode_ = null; // path://recomp_count_n
        _this.compNode_ = null; // path://comp_be_n
        //@end
        _this.user = null;
        _this.data = null;
        _this.list = [];
        return _this;
    }
    PortrayalInfoPnlCtrl.prototype.listenEventMaps = function () {
        var _a, _b;
        return [
            (_a = {}, _a[EventType_1.default.UPDATE_PORTRAYAL_ATTR] = this.onUpdatePortrayalAttr, _a.enter = true, _a),
            (_b = {}, _b[EventType_1.default.UPDATE_PORTRAYAL_INFO] = this.onUpdatePortrayalInfo, _b.enter = true, _b),
        ];
    };
    PortrayalInfoPnlCtrl.prototype.onCreate = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                this.setParam({ isAct: false, isMask: false });
                this.pictureNode_.Component(cc.Animation).play('comp_stop');
                this.user = this.getModel('user');
                return [2 /*return*/];
            });
        });
    };
    PortrayalInfoPnlCtrl.prototype.onEnter = function (data, list) {
        var _a;
        this.list = list;
        this.showRangeTge_.isChecked = (_a = this.user.getTempPreferenceMap(Enums_1.PreferenceKey.SHOW_PORTRAYAL_ATTR_RANGE)) !== null && _a !== void 0 ? _a : true;
        this.updateUI(data);
    };
    PortrayalInfoPnlCtrl.prototype.onRemove = function () {
        this.pictureNode_.Component(cc.Animation).play('comp_stop');
    };
    PortrayalInfoPnlCtrl.prototype.onClean = function () {
        assetsMgr.releaseTempResByTag(this.key);
    };
    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    // path://rooot_n/attrs/0/show_range_t_te
    PortrayalInfoPnlCtrl.prototype.onClickShowRange = function (event, data) {
        audioMgr.playSFX('click');
        this.user.setTempPreferenceData(Enums_1.PreferenceKey.SHOW_PORTRAYAL_ATTR_RANGE, event.isChecked);
        this.updateAttr();
    };
    // path://rooot_n/attrs/strategy/lock_strategy_be_n
    PortrayalInfoPnlCtrl.prototype.onClickLockStrategy = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/StrategyListBox', ut.stringToNumbers(this.data.json.strategy), this.data.avatarPawnName);
    };
    // path://comp_be_n
    PortrayalInfoPnlCtrl.prototype.onClickComp = function (event, data) {
        var _this = this;
        if (this.data.debris < Constant_1.PORTRAYAL_COMP_NEED_COUNT) {
            return ViewHelper_1.viewHelper.showMessageBox(ECode_1.ecode.DEBRIS_NOT_ENOUGH, {
                okText: 'ui.button_goto_comp',
                cancelText: 'ui.button_goto_pointsets',
                ok: function () { return ViewHelper_1.viewHelper.showPnl('menu/CompDebris', _this.data); },
                cancel: function () { return ViewHelper_1.viewHelper.showPnl('menu/Pointsets'); },
            });
        }
        var type = this.data.isChosenOne() ? 1 : 0;
        if (!this.data.isUnlock() || !ViewHelper_1.viewHelper.showNoLongerTip('comp_desc_' + type, {
            content: 'ui.portrayal_comp_desc_' + type,
            okText: 'login.button_ok',
            ok: function () { return _this.comp(); },
            cancel: function () { },
        })) {
            this.comp();
        }
    };
    // path://rooot_n/attrs/icon/send_to_chat_be
    PortrayalInfoPnlCtrl.prototype.onClickSendToChat = function (event, _) {
        var _this = this;
        audioMgr.playSFX('click');
        if (!this.data) {
            return;
        }
        var isLobby = mc.currWindName === 'lobby';
        var data = this.data;
        if (isLobby) {
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_portrayal_to_chat_tip', params: [data.getChatName()], isLobby: isLobby }, function (type, select) {
                if (GameHelper_1.gameHpr.lobby.sendChat(type, '', { portrayal: data, select: select }) === 0) {
                    ViewHelper_1.viewHelper.showPnl('lobby/LobbyChat', { tab: type }).then(function () {
                        _this.isValid && _this.hide();
                        ViewHelper_1.viewHelper.hidePnl('menu/Collection');
                    });
                }
            });
        }
        else {
            ViewHelper_1.viewHelper.showPnl('common/SendInfoToChat', { key: 'ui.send_portrayal_to_chat_tip', params: [data.getChatName()], isLobby: isLobby }, function (type, childType, select) {
                if (GameHelper_1.gameHpr.chat.sendChat(type, childType, '', { portrayal: data, select: select }) === 0) {
                    var target = GameHelper_1.gameHpr.chat.getTargetChat(type, childType, select);
                    ViewHelper_1.viewHelper.showPnl('common/Chat', { tab: type, target: target }).then(function () {
                        _this.isValid && _this.hide();
                        ViewHelper_1.viewHelper.hidePnl('menu/Collection');
                    });
                }
            });
        }
    };
    // path://next_portrayal_nbe
    PortrayalInfoPnlCtrl.prototype.onClickNextPortrayal = function (event, _) {
        var _this = this;
        var index = this.list.findIndex(function (m) { return m.id === _this.data.id; });
        var add = event.target.name === '0' ? -1 : 1;
        var idx = ut.loopValue(index + add, this.list.length);
        var data = this.list[idx];
        if (data && this.data.id !== data.id) {
            this.updateUI(data);
        }
    };
    // path://rooot_n/attrs/icon/desc_be
    PortrayalInfoPnlCtrl.prototype.onClickDesc = function (event, data) {
        audioMgr.playSFX('click');
        var list = [
            { key: 'ui.portrayal_desc_2', isNo: true },
            { key: 'ui.portrayal_desc_3', isNo: true },
            { key: 'ui.portrayal_desc_4', isNo: true },
            { key: 'ui.portrayal_desc_5', isNo: true },
        ];
        if (!this.data.isChosenOne()) {
            list.unshift({ key: 'ui.portrayal_desc_1', params: [Constant_1.PORTRAYAL_CHOSENONE_ODDS + '%', this.data.getChosenOneOdds() + '%'], isNo: true });
        }
        ViewHelper_1.viewHelper.showDescInfo(list);
    };
    // path://save_scheme_be_n
    PortrayalInfoPnlCtrl.prototype.onClickSaveScheme = function (event, data) {
        ViewHelper_1.viewHelper.showPnl('common/SaveScheme', this.data);
    };
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    PortrayalInfoPnlCtrl.prototype.onUpdatePortrayalAttr = function (data) {
        if (this.data.id === data.id) {
            this.updateAttr();
            this.updateButton();
        }
    };
    PortrayalInfoPnlCtrl.prototype.onUpdatePortrayalInfo = function () {
        var id = this.data.id;
        var data = this.user.getPortrayals().find(function (m) { return m.id === id; });
        if (data) {
            var index = this.list.findIndex(function (m) { return m.id === id; });
            if (index !== -1) {
                this.list[index] = data;
            }
            console.log('PortrayalInfoPnlCtrl.ts:165', { data: data });
            this.updateUI(data);
        }
    };
    // ----------------------------------------- custom function ----------------------------------------------------
    PortrayalInfoPnlCtrl.prototype.updateUI = function (data) {
        this.data = data;
        // 名字
        ViewHelper_1.viewHelper.showPortrayalName(this.nameNode_, data.getChatName(), data.getViceName());
        // 刷新立绘
        ViewHelper_1.viewHelper.updatePicture(data.id, data.isUnlock(), this.pictureNode_.Child('icon'), data.showSSROffset, data.hasAnim, this.key);
        // 属性
        this.updateAttr();
        // 按钮
        this.updateButton();
    };
    PortrayalInfoPnlCtrl.prototype.updateButton = function () {
        var data = this.data, isUnlock = data.isUnlock();
        this.compNode_.Child('lay/debris/val', cc.Label).Color(data.debris < Constant_1.PORTRAYAL_COMP_NEED_COUNT ? '#C34B3F' : '#3F332F').string = data.debris + '';
        this.compNode_.Child('lay/val').setLocaleKey(isUnlock ? 'ui.button_recomp' : 'ui.button_repair');
        // if (this.restoreNode_.active = isUnlock) {
        // 	this.restoreNode_.opacity = data.lastAttrs.length > 0 ? 255 : 120
        // }
    };
    // 刷新属性
    PortrayalInfoPnlCtrl.prototype.updateAttr = function () {
        var _this = this;
        var info = this.data, root = this.roootNode_.Child('attrs'), isUnlock = info.isUnlock();
        // 名字
        ResHelper_1.resHelper.loadHeroSkillIcon(info.json.skill, root.Child('icon/val'), this.key);
        root.Child('icon/name/val').setLocaleKey('portrayalSkillText.name_' + info.json.skill);
        root.Child('icon/name/pawn').setLocaleKey('ui.avatar_pawn_desc', info.avatarPawnName);
        var skillNode = root.Child('skill');
        var strategysNode = root.Child('strategys'), strategyName = root.Child('strategy/name'), strategyEmpty = root.Child('strategy/empty');
        var isChosenOne = this.data.isChosenOne();
        root.Child('icon/send_to_chat_be').active = strategysNode.active = strategyName.active = isUnlock;
        strategyEmpty.active = !isUnlock;
        if (isUnlock) {
            var showRange_1 = this.showRangeTge_.isChecked && !isChosenOne;
            root.Child('0').Swih(isChosenOne ? 'no_rand' : 'show_range_t_te');
            // 属性
            root.Child('attr').Items(info.mainAttrs, function (it, data) {
                it.Child('icon', cc.MultiFrame).setFrame(data.type - 1);
                it.Child('val').active = true;
                it.Child('val', cc.Label).string = '+' + data.value;
                if (it.Child('base').active = showRange_1) {
                    _this.getMainAttrRangeValue(data.type).forEach(function (v, i) { return it.Child('base/' + i, cc.Label).string = v + ''; });
                }
                it.Child('base2').active = false;
            });
            // 技能
            if (skillNode.active = !!info.skill) {
                skillNode.setLocaleKey(info.skill.desc, info.skill.getDescParams(showRange_1));
            }
            // 韬略
            strategyName.Child('count', cc.Label).string = "(" + info.strategys.length + ")";
            strategysNode.Items(info.strategys, function (it, data) { return ViewHelper_1.viewHelper.showStrategyText(it, data, info.avatarPawnName); });
        }
        else {
            root.Child('0').Swih('');
            // 属性
            root.Child('attr').Items([1, 2], function (it, type) {
                it.Child('icon', cc.MultiFrame).setFrame(type - 1);
                it.Child('val').active = it.Child('base').active = false;
                it.Child('base2').active = true;
                _this.getMainAttrRangeValue(type).forEach(function (v, i) { return it.Child('base2/' + i, cc.Label).string = v + ''; });
            });
            // 技能
            skillNode.active = true;
            skillNode.setLocaleKey('portrayalSkillText.desc_' + info.json.skill, GameHelper_1.gameHpr.getPortrayalSkillDescParams(info.json.skill));
        }
        // 重绘次数
        if (this.recompCountNode_.active = info.recompCount > 0) {
            this.recompCountNode_.Child('val').setLocaleKey('ui.recomp_count', info.recompCount);
            var chosenoneOddsNode = this.recompCountNode_.Child('chosenone').Swih(isChosenOne ? 'one' : 'odds')[0];
            if (!isChosenOne) {
                chosenoneOddsNode.setLocaleKey('ui.chosenone_odds', info.getChosenOneOdds() + '%');
            }
            root.Child('icon/desc_be/val', cc.MultiFrame).setFrame(isChosenOne);
        }
        this.compNode_.Component(cc.Button).interactable = true;
    };
    PortrayalInfoPnlCtrl.prototype.getMainAttrRangeValue = function (type) {
        if (type === 1) {
            return ut.stringToNumbers(this.data.json.hp, ',');
        }
        else if (type === 2) {
            return ut.stringToNumbers(this.data.json.attack, ',');
        }
        return [0, 0];
    };
    // 合成
    PortrayalInfoPnlCtrl.prototype.comp = function () {
        return __awaiter(this, void 0, void 0, function () {
            var portrayal, _a, data, err, isUnlock, portrayalAnim, chonghuiMask, nowIsUnlock;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        this.compNode_.Component(cc.Button).interactable = false;
                        portrayal = this.data;
                        return [4 /*yield*/, NetHelper_1.netHelper.reqPortrayalComp({ id: portrayal.id })];
                    case 1:
                        _a = _b.sent(), data = _a.data, err = _a.err;
                        if (err) {
                            return [2 /*return*/, ViewHelper_1.viewHelper.showAlert(err)];
                        }
                        isUnlock = portrayal.isUnlock();
                        portrayal.updateInfo(data.info);
                        this.user.checkHasCanCompPortrayal();
                        GameHelper_1.gameHpr.player.updatePawnHeroAttr(portrayal.id, portrayal.attrs);
                        eventCenter.emit(EventType_1.default.UPDATE_PORTRAYAL_INFO);
                        if (!this.isValid) return [3 /*break*/, 3];
                        portrayalAnim = this.pictureNode_.Child('icon/val', FrameAnimationCmpt_1.default);
                        portrayalAnim === null || portrayalAnim === void 0 ? void 0 : portrayalAnim.stop();
                        // 刷新碎片数量
                        this.compNode_.Child('lay/debris/val', cc.Label).Color(portrayal.debris < Constant_1.PORTRAYAL_COMP_NEED_COUNT ? '#C34A32' : '#333333').string = portrayal.debris + '';
                        if (isUnlock) {
                            chonghuiMask = this.pictureNode_.Child('chonghui_mask');
                            ResHelper_1.resHelper.loadPortrayalImage(portrayal.id, chonghuiMask, this.key);
                            chonghuiMask.setPosition(portrayal.showSSROffset);
                        }
                        audioMgr.playSFX('common/sound_ui_020');
                        mc.lockTouch('comp_anim');
                        return [4 /*yield*/, this.pictureNode_.Component(cc.Animation).playAsync(isUnlock ? 'chonghui' : 'huizhi')];
                    case 2:
                        _b.sent();
                        mc.unlockTouch('comp_anim');
                        if (this.isValid) {
                            nowIsUnlock = portrayal.isUnlock();
                            if (isUnlock !== nowIsUnlock) {
                                ViewHelper_1.viewHelper.updatePicture(portrayal.id, nowIsUnlock, this.pictureNode_.Child('icon'), portrayal.showSSROffset, portrayal.hasAnim, this.key);
                                // this.compNode_.Child('lay/val').setLocaleKey(nowIsUnlock ? 'ui.button_recomp' : 'ui.button_repair')
                            }
                            else {
                                portrayalAnim === null || portrayalAnim === void 0 ? void 0 : portrayalAnim.play('standby');
                            }
                            ViewHelper_1.viewHelper.showPortrayalName(this.nameNode_, portrayal.getChatName(), portrayal.getViceName());
                            this.updateAttr();
                            this.updateButton();
                        }
                        _b.label = 3;
                    case 3:
                        if (GameHelper_1.gameHpr.guide.isWorking()) {
                            this.emit('PORTRAYAL_COMP');
                        }
                        return [2 /*return*/];
                }
            });
        });
    };
    PortrayalInfoPnlCtrl = __decorate([
        ccclass
    ], PortrayalInfoPnlCtrl);
    return PortrayalInfoPnlCtrl;
}(mc.BasePnlCtrl));
exports.default = PortrayalInfoPnlCtrl;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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